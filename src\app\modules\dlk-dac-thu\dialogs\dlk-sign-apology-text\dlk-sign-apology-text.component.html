<button mat-icon-button
        class="close-button"
        (click)="onDismiss()">
  <mat-icon>close</mat-icon>
</button>
<h3 class="dialog_title"
    mat-dialog-title
    i18n>Văn bản xin lỗi</h3>
<form [formGroup]="addForm"
      class="addForm edit"
      (submit)="onConfirm()"
      id="ngAddForm">
  <div fxLayout="row"
       fxLayoutAlign="center"
       fxLayout.xs="column"
       fxLayout.sm="row"
       class="header-sector">
    <div fxFlex.gt-sm="39"
         fxFlex.lt-md="39"
         class="header-sector1">
      <span i18n>Văn bản</span>
    </div>
    <div fxFlex.gt-sm="60"
         fxFlex.lt-md="60"
         class="header-sector2">
      <a title
         title="Thêm mới dòng"
         class="aHover"
         (click)="addItem()">
        <div fxLayout="row"
             class="bn-add">
          <mat-icon>add</mat-icon>
          <span i18n>Thêm mới tên văn bản</span>
        </div>
      </a>
    </div>
  </div>
  <div fxLayout="column"
       fxLayoutAlign="center"
       fxLayout.xs="column"
       fxLayout.sm="column"
       class="mat-elevation-z0 padding-sectorname">
    <div fxLayout="row"
         fxLayoutAlign="center"
         fxLayout.lt-md="column"
         fxLayout.sm="row"
         class="mat-elevation-z0 margin-sectorname"
         *ngFor="let field of fieldArray; let i = index">
      <div fxLayout="row"
           fxFlex.gt-sm="94"
           fxFlex.lt-md="92"
           fxLayout.xs="column"
           class="mat-elevation-z2 padding-itemsectorname">
        <div fxFlex.gt-sm="50.5"
             fxFlex.lt-md="51.7"
             class="header-sector1"
             *ngIf="!changeTag">
          <mat-form-field appearance="outline"
                          fxFlex='grow'>
            <mat-label i18n>Tên văn bản</mat-label>
            <input matInput
                   (change)="changeName(i , $event)"
                   required
                   maxlength="256"
                   (focusout)="changeName(i , $event)"
                   value="{{field.name}}"
                   oninput="if(this.value.trim().length === 0) {this.value = null}">
          </mat-form-field>
        </div>
        <div fxFlex.gt-sm="50.5"
             fxFlex.lt-md="51.7"
             class="header-sector1"
             *ngIf="changeTag">
          <mat-form-field appearance="outline"
                          fxFlex='grow'>
            <mat-label>Số văn bản</mat-label>
            <input matInput
                   (change)="changeName(i , $event)"
                   required
                   maxlength="256"
                   (focusout)="changeName(i , $event)"
                   value="{{field.name}}"
                   oninput="if(this.value.trim().length === 0) {this.value = null}">
          </mat-form-field>
        </div>
        <div fxFlex.gt-sm="2.4"
             fxFlex.lt-md="2.3"></div>
        <div fxFlex.gt-sm="47.1"
             fxFlex.lt-md="45.0"
             class="bn-left">
          <mat-form-field appearance="outline"
                          fxFlex='grow'>
            <mat-label>Ngôn ngữ</mat-label>
            <mat-select (selectionChange)="changeLanguage(i , $event)"
                        value={{field.languageId}}>
              <mat-option *ngFor='let language of field.listLanguageItem'
                          value="{{language.languageId}}">
                {{ language.name }}</mat-option>
            </mat-select>
          </mat-form-field>
        </div>
      </div>
      <div fxFlex.gt-sm="3"
           fxFlex.lt-md="5"
           class="div-icon">
        <a title
           title="Xóa dòng"
           class="aHover"
           (click)="deleteItem(i)">
          <mat-icon class="material-icons-outlined icon-color">cancel</mat-icon>
        </a>
      </div>
    </div>
    <div class="error_MsgCustom"
         *ngIf="isFieldArrayInvalid">
      <span i18n>Vui lòng nhập tên văn bản</span>
      <div class="err">
        <mat-icon>priority_high</mat-icon>
      </div>
    </div>
  </div>
  <div fxLayout="row"
       fxLayout.xs="column"
       fxLayout.sm="column"
       class="marginRow">
    <mat-form-field appearance="outline"
                    fxFlex='grow'>
      <mat-label>Loại lý do văn bản</mat-label>
      <mat-select formControlName="vbxl_type">
        <mat-option *ngFor="let item of listLoaiVBXL"
                    [value]="item.value.toString()"
                    [disabled]="item.isDisabled">
          {{item.text}}
        </mat-option>
      </mat-select>
    </mat-form-field>
  </div>
  <div class="error_MsgCustom"
       *ngIf="isFieldArrayInvalid">
    <span i18n>Vui lòng nhập tên văn bản</span>
    <div class="err">
      <mat-icon>priority_high</mat-icon>
    </div>
  </div>
  <div fxLayout="row"
       fxLayout.xs="column"
       fxLayout.sm="column"
       class="marginRow">
    <mat-form-field appearance="outline"
                    fxFlex='grow'>
      <mat-label i18n>Mô tả</mat-label>
      <textarea matInput
                formControlName="description"
                cdkTextareaAutosize
                cdkAutosizeMinRows="4"
                cdkAutosizeMaxRows="4"
                maxlength="1000"
                oninput="if(this.value.trim().length === 0) {this.value = null}"></textarea>
    </mat-form-field>
  </div>
  <div fxLayout="row"
       fxLayout.xs="column"
       fxLayout.sm="column"
       fxLayoutAlign="space-between"
       class="uploadFiles">
    <div class="card"
         fxFlex='row'>
      <div class="cardTitle">
        <span i18n>File đính kèm</span>
        <div class="cardAction fileAttachDone">
          <mat-spinner diameter="25"></mat-spinner>
          <div class="done ">
            <mat-icon>check_circle_outline</mat-icon>
            <span i18n>Đã lưu</span>
          </div>
        </div>
      </div>
      <div class="cardContent">
        <div class="drag_upload_btn">
          <button mat-button>
            <mat-icon>cloud_upload</mat-icon>
            <span i18n>Kéo thả tệp tin hoặc</span><a href=""><span i18n> Tải lên</span></a>
            <div>
              <span>Kích thước tối đa của một tệp tin:</span> {{ maxFileSize }}MB
            </div>
          </button>
          <input id="img_upload"
                 name="img_upload[]"
                 type='file'
                 (change)="onSelectFileAttach($event, 'fileAttachDone')"
                 [accept]="acceptFileExtension"
                 value="{{blankVal}}">
          <div class="fileUploadPreview">
            <div class="listUploaded"
                 *ngFor="let attach of apologyTextFilePreview; let i = index">
              <div class="fileInfo">
                <div class="fileIcon"
                     [ngStyle]="{'background-image': 'url('+ attach.icon +')'}">
                </div>
                <div class="dGrid">
                  <p class="fileName"
                     matTooltip=""
                     [matTooltipPosition]="'right'">
                    {{attach.name}}</p>
                  <p class="fileSize">{{bytesToSize(attach.size)}}</p>
                </div>
                <a mat-icon-button
                   class="deleteFile">
                  <mat-icon (click)="removeAttachItem(i, attach.id, attach.name, 'fileAttachDone')">
                    close
                  </mat-icon>
                </a>
              </div>
              <a mat-icon-button
                 class="moreBtn"
                 [matMenuTriggerFor]="digitallySignedFileMenu">
                <mat-icon>more_vert</mat-icon>
              </a>
              <mat-menu #digitallySignedFileMenu="matMenu">
                <button mat-menu-item
                        class="menuAction"
                        (click)="downloadFile(attach.id, attach.name)">
                  <mat-icon>cloud_download</mat-icon>
                  <span i18n>Tải xuống tệp tin</span>
                </button>
                <!-- <button mat-menu-item class="menuAction">
                                <mat-icon>verified</mat-icon>
                                <span i18n>Ký số Ban Cơ yếu VGCA</span>
                            </button> -->
                <button mat-menu-item
                        class="menuAction"
                        (click)="verifiedSimHSM(attach)">
                  <mat-icon>verified</mat-icon>
                  <span i18n>Ký số sim</span>
                </button>
              </mat-menu>
            </div>
          </div>
        </div>

      </div>
    </div>
  </div>

  <div fxLayout="row"
       fxLayoutAlign="center"
       fxLayout.xs="row"
       fxLayout.sm="row"
       class="divAddBtn">
    <button mat-flat-button
            fxFlex='grow'
            class="saveBtn"
            type="submit"
            form="ngAddForm">
      <span i18n>Lưu lại</span>
    </button>
  </div>
</form>