import { rejects } from 'assert';
import { resolve } from 'dns';
import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { ApiProviderService } from 'src/app/core/service/api-provider.service';
import { Observable } from 'rxjs';
import { IFormMap } from '../../schema/form-map';

@Injectable({
  providedIn: 'root'
})
export class FormService {

  constructor(
    private http: HttpClient,
    private apiProviderService: ApiProviderService
  ) { }

  private basepad = this.apiProviderService.getUrl('digo', 'basepad');
  private formPath = this.apiProviderService.getUrl('digo', 'basepad') + '/form/';
  private formGroupPath = this.apiProviderService.getUrl('digo', 'basepad') + '/formGroup/';
  private formProcedurePath = this.apiProviderService.getUrl('digo', 'basepad') + '/procedure-form/';
  private adapter = this.apiProviderService.getUrl('digo', 'adapter');

  getLgspSyncForm(configId, agencyId, subsystemId, agencyCode, syncCode, syncTotalElements): Observable<any> {
    let URL = this.adapter + `/lgsp-thanhPhanHoSo/--dongBoDanhMuc`;
    URL += `?config-id=${configId}`;
    URL += `&agency-id=${agencyId}`;
    URL += subsystemId?`&subsystem-id=${subsystemId}`:``;
    URL += `&agency-code=${agencyCode}`;
    URL += `&syncCode=${syncCode}`;
    URL += `&syncTotalElements=${syncTotalElements}`;
    return this.http.get(URL);
  }

  getSyncStatus(code): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    const url = this.basepad + '/sync-form-status/--get-by-code?code=' + code;
    return this.http.get<any>(url, { headers });
  }

  getSyncTotalElements(configId, agencyId, subsystemId, agencyCode?:string): Observable<any> {
    let URL = this.adapter + `/lgsp-thanhPhanHoSo/--syncTotalElements`;
    URL += `?config-id=${configId}`;
    URL += `&agency-id=${agencyId}`;
    URL += subsystemId?`&subsystem-id=${subsystemId}`:``;
    URL += `&agency-code=${agencyCode}`;
    return this.http.get(URL);
  }

  getListForm(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.formPath + searchString, { headers });
  }

  getListFormGroup(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.formGroupPath + searchString, { headers });
  }

  getAllFormGroup(): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.formGroupPath + '--all-formGroup', { headers });
  }

  getPageFormsNotMap(page?:number, size?:number, keyword?:string):Observable<any>{
    let URL = `${this.formPath}?page=${page}&size=${size}&map-type=0`;
    if(keyword)
      URL += `&keyword=${keyword}`;
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(URL, { headers });
  }

  getPageFormstMapped(page?:number, size?:number, sort?:string, keyword?:string):Observable<any>{
    if(page > 0){
      page -= 1;
    }
    let URL = `${this.formPath}?page=${page}&size=${size}&map-type=1&spec=page`;
    if(keyword){
      URL += `&keyword=${keyword}`;
    }
    if(sort){
      URL += `&sort=${sort}`;
    }
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get<any>(URL, { headers });
  }

  mapForm(formId?:string, formOrginId?:string):Observable<any>{
    let URL = `${this.formPath}${formId}/--map?form-orgin-id=${formOrginId}`;
    let headers = new HttpHeaders();
    //headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.put<any>(URL,{ headers });
  }

  removeMapped(formId?:string):Observable<any>{
    let URL = `${this.formPath}${formId}/--remove-map`;
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.put<any>(URL,{ headers });
  }

  postNewForm(requestBody) {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    return this.http.post<any>(this.formPath, requestBody, { headers });
  }

  postNewFormGroup(requestBody) {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    return this.http.post<any>(this.formGroupPath, requestBody, { headers });
  }

  getFormInfo(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.formPath + id, { headers });
  }

  getFormGroupInfo(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.formGroupPath + id, { headers });
  }

  getFormInfoPromise(id): Promise<any> {
    return new Promise((resolve, rejects)=>{
      let headers = new HttpHeaders();
      headers = headers.set('Accept-Language', localStorage.getItem('language'));
      this.http.get(this.formPath + id, { headers }).subscribe(rs=>{
        resolve(rs);
      });
    });
  }

  putUpdateForm(id, requestBody) {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.put<any>(this.formPath + id, requestBody, { headers });
  }

  putUpdateFormGroup(id, requestBody) {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.put<any>(this.formGroupPath + id, requestBody, { headers });
  }

  deleteForm(id: string) {
    let headers = new HttpHeaders();
    headers = headers.append('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.delete(this.formPath + id, { headers });
  }

  deleteFormGroup(id: string) {
    let headers = new HttpHeaders();
    headers = headers.append('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.delete(this.formGroupPath + id, { headers });
  }

  importData(file: File): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    let formData: FormData = new FormData();
    formData.append("file",file);
    return this.http.post(this.formPath + "--import-excell", formData, { headers });
  }

  importDataProcedureForm(id: any, file: File): Observable<any> {
    let formData: FormData = new FormData();
    formData.append("file",file);
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'multipart/form-data');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.post(this.formProcedurePath + `${id}/--from-file?update-clone-procedure=true`, formData);
  }

}
