import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { DlkBaoCaoChiThi08CapHuyenRoutingModule } from './dlk-baocao-chithi08-caphuyen-routing.module';
import { DlkBaoCaoChiThi08CapHuyenComponent } from './dlk-baocao-chithi08-caphuyen.component';
import { SharedModule } from 'src/app/shared/shared.module';
import { NgxPrinterModule } from 'ngx-printer';


@NgModule({
  declarations: [DlkBaoCaoChiThi08CapHuyenComponent],
  imports: [
    CommonModule,
    DlkBaoCaoChiThi08CapHuyenRoutingModule,
    SharedModule,
    NgxPrinterModule.forRoot({ printOpenWindow: true })
  ]
})
export class DlkBaoCaoChiThi08CapHuyenModule { }
