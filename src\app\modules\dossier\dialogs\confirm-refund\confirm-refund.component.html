
<button mat-icon-button class="close-button" (click)="onDismiss()">
    <mat-icon>close</mat-icon>
</button>
<h3 class="dialog_title" mat-dialog-title>X<PERSON><PERSON> nh<PERSON>n hoàn tiền</h3>
<div class="information">
    <div fxLayout="row" fxLayout.xs="row" fxLayout.sm="row" class="formFieldOutline">
        <div  appearance="outline" fxFlex.gt-md="33" fxFlex.gt-sm="32" fxFlex.gt-xs="49.5" fxFlex='grow'>
            <h3>
                <span>M<PERSON> hồ sơ: </span> <span style = "font-weight: bold;">{{dossierCode}}</span>
            </h3>
        </div>
    </div>
    <div fxLayout="row" fxLayout.xs="row" fxLayout.sm="row" class="formFieldOutline">
        <div  appearance="outline" fxFlex.gt-md="33" fxFlex.gt-sm="32" fxFlex.gt-xs="49.5" fxFlex='grow'>
            <h3>
                <span style = "font-weight: bold;">Thông tin hoàn tiền: </span>
            </h3>
        </div>
    </div>
    
    <div fxLayout="row" fxLayout.xs="row" fxLayout.sm="row" class="formFieldOutline">
        <div  appearance="outline" fxFlex.gt-md="33" fxFlex.gt-sm="32" fxFlex.gt-xs="49.5" fxFlex='grow'>
            <span style = "font-weight: bold;">Số tài khoản: </span> <span>{{feeRefundData.stk}}</span>
        </div>
        <div  appearance="outline" fxFlex.gt-md="33" fxFlex.gt-sm="32" fxFlex.gt-xs="49.5" fxFlex='grow'>
            <span style = "font-weight: bold;">Tên chủ tài khoản: </span> <span>{{feeRefundData.owner}}</span>
        </div>
    </div>
    <div  appearance="outline" fxFlex.gt-md="33" fxFlex.gt-sm="32" fxFlex.gt-xs="49.5" fxFlex='grow' style = "margin-top: 8px;">
        <span style = "font-weight: bold;">Tên ngân hàng: </span> <span>{{feeRefundData.bank}}</span>
    </div>
</div>
<div fxLayout="row" fxLayout.xs="row" fxLayout.sm="row" class="formFieldOutline" style ="margin-top: 24px;">
    <div  appearance="outline" fxFlex.gt-md="33" fxFlex.gt-sm="32" fxFlex.gt-xs="49.5" fxFlex='grow'>
        <h3>
            <span style = "font-weight: bold;">Thông phí, lệ phí: </span>
        </h3>
    </div>
</div>
<div class="feeData" fxLayout="row" fxLayout.xs="column" fxLayout.sm="column">
    <mat-table [dataSource]="feeDataSource" fxFlex='grow' class="tblFee">
        <ng-container matColumnDef="procostType">
            <mat-header-cell *matHeaderCellDef i18n>Loại lệ phí</mat-header-cell>
            <mat-cell *matCellDef="let row" i18n-data-label data-label="Loại lệ phí">
                {{row.typeName}}
                <span *ngIf="row.typeName == undefined || row.typeName == null || row.typeName.trim() == ''" i18n>(Không tìm thấy bản dịch)</span>
            </mat-cell>
            <mat-footer-cell *matFooterCellDef i18n>Tổng</mat-footer-cell>
        </ng-container>
        <ng-container matColumnDef="amount">
            <mat-header-cell *matHeaderCellDef i18n>Thành tiền</mat-header-cell>
            <mat-cell *matCellDef="let row" i18n-data-label data-label="Thành tiền">
                {{row.quantity*row.cost | number }} {{row.monetaryUnit}} </mat-cell>
            <mat-footer-cell *matFooterCellDef class="totalCell">{{totalCost}}
            </mat-footer-cell>
        </ng-container>
        <ng-container matColumnDef="pay">
            <mat-header-cell *matHeaderCellDef i18n>Thanh toán</mat-header-cell>
            <mat-cell *matCellDef="let row" data-label="Thanh toán">
                <span *ngIf="row.paid != row.quantity*row.cost" i18n>Chưa thanh toán</span>
                <span *ngIf="row.paid == row.quantity*row.cost" i18n>Đã thanh toán</span>
            </mat-cell>
            <mat-footer-cell *matFooterCellDef class="totalCell">
                {{totalCost}}
                <span class="rest">(<span i18n>Còn lại</span> {{totalRemaining}})</span>
            </mat-footer-cell>
        </ng-container>
        <ng-container matColumnDef="typePay">
            <mat-header-cell *matHeaderCellDef>Hình thức thanh toán</mat-header-cell>
            <mat-cell *matCellDef="let row" i18n-data-label data-label="Mô tả" req>
                {{row.typePay}}
            </mat-cell>
            <mat-footer-cell *matFooterCellDef></mat-footer-cell>
        </ng-container>
        <mat-header-row *matHeaderRowDef="feeDisplayedColumns"></mat-header-row>
        <mat-row *matRowDef="let row; columns: feeDisplayedColumns;"></mat-row>
        <mat-footer-row *matFooterRowDef="feeDisplayedColumns"></mat-footer-row>
    </mat-table>
</div>
<form [formGroup]="feeRefundForm"  style="margin-top: 16px; width:100%">
    <div fxLayout="row" fxLayout.xs="row" fxLayout.sm="row" class="formFieldOutline">
        <mat-form-field  appearance="outline" fxFlex.gt-md="33" fxFlex.gt-sm="32" fxFlex.gt-xs="49.5" fxFlex='grow'>
            <mat-label >Số tiền đã trả:</mat-label>
            <input type="number" matInput formControlName="feeRefund" maxlength="500">
        </mat-form-field>
    </div>
    <div fxLayout="row" fxLayout.xs="row" fxLayout.sm="row" class="formFieldOutline">
        <mat-form-field  appearance="outline" fxFlex.gt-md="100" fxFlex.gt-sm="100" fxFlex.gt-xs="100" fxFlex='grow'>
            <mat-label >Lý do hoàn tiền:</mat-label>
        <input type="text" matInput formControlName="description" maxlength="500">
        </mat-form-field>
    </div>
</form>

<div fxLayout="row" fxLayout.xs="column" fxLayout.sm="column" fxLayoutAlign="center">
    <button mat-flat-button fxFlex='20' class="applyBtn" (click)="onConfirm()">
        <span i18n>Lưu lại</span>
    </button>
    <button mat-flat-button fxFlex='20' class="cancelBtn" (click)="onDismiss()">
        <span i18n>Đóng</span>
    </button>
</div>
