import { Component, EventEmitter, Inject, Input, OnInit, Output } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { EnvService } from 'src/app/core/service/env.service';
import { DeploymentService } from 'src/app/data/service/deployment.service';
import { SnackbarService } from 'src/app/data/service/snackbar/snackbar.service';
import { NgxPrinterService } from 'ngx-printer';
import html2canvas from 'html2canvas';
import jsPDF from 'jspdf';
import { DatePipe } from '@angular/common';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { GetCitizenInfoService } from 'src/app/shared/components/get-citizen-info/get-citizen-info.service';
import { KeycloakService } from 'keycloak-angular';
import { UserService } from 'src/app/data/service/user.service';


@Component({
  selector: 'get-citizen-info-qbh',
  templateUrl: './get-citizen-info-qbh.component.html',
  styleUrls: ['./get-citizen-info-qbh.component.scss']
})
export class GetCitizenInfoQbhComponent implements OnInit {

  selectedLang = localStorage.getItem('language') || 'vi';
  config = this.envService.getConfig();
  enableValidateResidentialInfo = this.deploymentService.env.enableValidateResidentialInfo;
  env = this.deploymentService.getAppDeployment()?.env;
  formIsValid?:boolean;
  infoErrorMsg?:string;
  userName: string;
  accountId: string;
  isSuccess?: boolean;
  data: any;
  //QBH IGATESUPP-61957
  qbhSaveInfoCitizens = this.deploymentService.env?.OS_QBH?.qbhSaveInfoCitizens;
  verifyForm= new FormGroup({
    fullname: new FormControl('', Validators.required),
    identityNumber: new FormControl('', Validators.required),
    birthday: new FormControl('', Validators.required)
  });
  constructor(
    @Inject(MAT_DIALOG_DATA) public dataReceive: GetCitizenInfoQbhComponentModel, public dialogRef: MatDialogRef<GetCitizenInfoQbhComponent>,
    private snackbarService: SnackbarService,
    private getCitizenInfoService:GetCitizenInfoService,
    private envService: EnvService,
    private datePipe: DatePipe,
    private deploymentService: DeploymentService,
    private printerService: NgxPrinterService,
    private userService: UserService,
    private keycloakService: KeycloakService
    ) {}

  ngOnInit() {
    this.getUserAccount();
    // const msgObj = {
    //   vi: 'Thông tin công dân hợp lệ',
    //   en: 'Citizen infomation is valid'
    // };
    
    //this.snackbarService.openSnackBar(1, '', msgObj[this.selectedLang], 'success_notification', this.config.expiredTime);
    // this.data.TinhTrangHonNhan = tinhTrangHonNhanMap[this.data?.TinhTrangHonNhan];
    // this.data.GioiTinh = gioiTinhMap[this.data?.GioiTinh];
    // this.data.NhomMau = nhomMauMap[this.data?.NhomMau];
    // this.data.ChuHo.QuanHe = quanHeMap[this.data?.ChuHo?.QuanHe];
    // if(this.data?.NgayThangNamSinh != null){
    //   this.data.NgayThangNamSinh.NgayThangNam = convertDateString(this.data?.NgayThangNamSinh?.NgayThangNam);
    // }
    // this.showPrint = this.data?.showPrint;
  }


  onDismiss() {
    this.dialogRef.close();

  }

  // onConfirm() {
  //   let result = {
  //     "status": false
  //   }
  //   this.dialogRef.close(result);
  // }
  getUserAccount() {
    if (this.env?.OS_DBN?.checkCitizenWithUserName) {
      this.keycloakService.loadUserProfile().then(user => {
        // tslint:disable-next-line: no-string-literal
        this.accountId = user['attributes'].user_id[0];
        this.userService.getUserInfo(this.accountId).subscribe(data => {
          this.userName = data?.fullname;
          if (!!this.userName && !!this.env?.OS_DBN?.checkCitizenWithUserNameValue ){
            this.userName = this.userName + this.env?.OS_DBN?.checkCitizenWithUserNameValue;
          }
        });
      });
    }
  }
  toBirthday(isoDate){
    let date = new Date(isoDate);
    let yyyy = date.getFullYear();
    let mm:any = date.getMonth()+1;
    let dd:any = date.getDate();

    if (dd < 10) {
      dd = `0${dd}`;
    }
    if (mm < 10) {
      mm = `0${mm}`;
    }

    return `${yyyy}${mm}${dd}`;
  }

  onConfirm(){
    ////debugger
    const data = this.verifyForm.getRawValue();
    if(!data?.fullname ||!data?.identityNumber||!data?.birthday){
      let msgObj = {
        vi: 'Vui lòng nhập đầy đủ thông tin',
        en: 'Please press full information'
      };
      this.snackbarService.openSnackBar(0, '', msgObj[this.selectedLang], 'error_notification', this.config.expiredTime);
      return;
    }else if(data?.identityNumber.length != 9 && data?.identityNumber.length != 12){
      let msgObj = {
        vi: 'Vui lòng kiểm tra lại Số CMND/CCCD',
        en: 'Please press full information'
      };
      this.snackbarService.openSnackBar(0, '', msgObj[this.selectedLang], 'error_notification', this.config.expiredTime);
      return;

    }
    this.formIsValid = true;
    let birthday = this.toBirthday(data?.birthday);
    const userAgency = JSON.parse(localStorage.getItem('userAgency'));
    
    const log = {
      identityNumber: data?.identityNumber,
      name: data?.fullname,
      birtDay: data?.birthday,
      status: true,
      function: 1,
      agency: {
        id: userAgency.id ? userAgency.id : this.config.rootAgency.id,
        name: userAgency.name ? userAgency.name : this.config.rootAgency.trans.vi.name,
        code: userAgency.code ? userAgency.code : this.config.rootAgency.code
      }
    };
    
    this.getCitizenInfoService.getCitizenIfo(data?.identityNumber,data?.fullname,birthday,this.dataReceive.eFormId, true, this.userName).subscribe(rs=>{
      if(rs?.logId) sessionStorage.setItem('logCSDLDCId', rs.logId);
      if (rs.originInfo){
        if(rs?.logId) {
          sessionStorage.setItem('logCSDLDCId', rs.logId);
          sessionStorage.setItem('identityNumber', data?.identityNumber);
          log['logCSDLDCId'] = rs.logId;
          log['menuUrl'] = window.location.href;
          log['menu'] = this.getCitizenInfoService.checkMenuByUrl(window.location.href)?.name;
          log['menuCode'] = this.getCitizenInfoService.checkMenuByUrl(window.location.href)?.code;
          log['actions'] = 'Kiểm tra danh tính số';
          log['ipAddress'] = sessionStorage.getItem('clientIP');
        }
        this.getCitizenInfoService.writeCheckCitizenLog(log).subscribe(rp => {

        });
        console.log('dataaaa'+rs);
        this.data = rs.originInfo;
        this.data.TinhTrangHonNhan = tinhTrangHonNhanMap[this.data?.TinhTrangHonNhan];
        this.data.GioiTinh = gioiTinhMap[this.data?.GioiTinh];
        this.data.NhomMau = nhomMauMap[this.data?.NhomMau];
        this.data.ChuHo.QuanHe = quanHeMap[this.data?.ChuHo?.QuanHe];
        if(this.data?.NgayThangNamSinh != null){
          this.data.NgayThangNamSinh.NgayThangNam = convertDateString(this.data?.NgayThangNamSinh?.NgayThangNam);
        }
        this.isSuccess = true;
      } else {
        const msgObj = {
          vi: `Thông tin công dân không hợp lệ, vui lòng kiểm tra lại: Họ và tên, CMND/CCCD, Ngày sinh`,
          en: `Citizen infomation isn't valid, please again check: fullname, identity number, birthday`
        };
        this.infoErrorMsg = msgObj[this.selectedLang];
      } 
    }, err => {
      const msgObj = err.error.message;
      this.snackbarService.openSnackBar(0, msgObj, '', 'error_notification', this.config.expiredTime);
    });
  }

  onPrint(){
    this.printerService.printDiv('printDiv');
  }

  onDismissAndConfirm() {
    this.getCitizenInfoService.writeCheckCitizenLogAction('Lưu giữ thông tin CD (QBH)')
    $("#contentXuat").html("Xuất bởi: " + this.userName + ". Thông tin khai thác từ CSDLQG về dân cư vào thời gian: " + this.datePipe.transform(new Date(), 'dd-MM-yyyy hh:mm:ss'));
    const elementPdf = document.getElementById('printDiv');
    console.log(elementPdf);
    if(elementPdf) {
      html2canvas(elementPdf).then((canvas) => {
        const pdf = new jsPDF('l', 'px', 'a4');
        pdf.addImage(canvas.toDataURL('image/png'), 'PNG', 0, 0, 630, 300);
        const pdfBase64 = pdf.output('dataurlstring'); // Convert PDF to base64
        let result = {
          "status": true,
          "data": pdfBase64
        }
        this.dialogRef.close(result);
        //pdf.save('modal.pdf');
      })
    }
    $("#contentXuat").html("");
  }
}


export class GetCitizenInfoQbhComponentModel {
  constructor(public eFormId: string, public dossierId: string) {}

  //ngOnInit() {}
}



function convertDateString(dateString: string): string {
  const year = dateString.slice(0, 4);
  const month = dateString.slice(4, 6);
  const day = dateString.slice(6, 8);
  return `${day}/${month}/${year}`;
}

interface GioiTinhMap {
  [key: string]: string;
}
const gioiTinhMap: GioiTinhMap = {
  0: "Chưa có thông tin",
  1: "Giới tính nam",
  2: "Giới tính nữ",
};

interface TinhTrangHonNhanMap {
  [key: string]: string;
}
const tinhTrangHonNhanMap: TinhTrangHonNhanMap = {
  0: "Chưa có thông tin",
  1: "Chưa kết hôn",
  2: "Đang có vợ/chồng",
  3: "Đã ly hôn hoặc góa vợ/chồng"
};

interface NhomMauMap {
  [key: string]: string;
}
const nhomMauMap: NhomMauMap = {
  "00": "Chưa có thông tin",
  "01": "Nhóm máu A",
  "02": "Nhóm máu B",
  "03": "Nhóm máu AB",
  "04": "Nhóm máu O"
};

interface QuanHeMap {
  [key: string]: string;
}
const quanHeMap: QuanHeMap = {
  "00" : "Chưa có thông tin",
  "02" : "Cha",
  "0210" : "Cha đẻ",
  "0211" : "Ba",
  "0212" : "Bố",
  "0213" : "Tía",
  "0214" : "Cha chồng",
  "0215" : "Cha nuôi",
  "0216" : "Cha vợ",
  "03" : "Mẹ",
  "0310" : "Mẹ đẻ",
  "0311" : "Mẹ chồng",
  "0312" : "Mẹ nuôi",
  "0313" : "Mẹ vợ",
  "04" : "Vợ",
  "05" : "Chồng",
  "06" : "Ông",
  "0610" : "Ông nội",
  "0611" : "Ông ngoại",
  "07" : "Bà",
  "0710" : "Bà nội",
  "0711" : "Bà ngoại",
  "08" : "Con",
  "0810" : "Con đẻ",
  "0811" : "Con chồng",
  "0812" : "Con dâu",
  "0813" : "Con nuôi",
  "0814" : "Con rể",
  "0815" : "Con vợ",
  "09" : "Anh",
  "0910" : "Anh ruột",
  "0911" : "Anh chồng",
  "0912" : "Anh họ",
  "0913" : "Anh rể",
  "0914" : "Anh vợ",
  "10" : "Chị",
  "1010" : "Chị ruột",
  "1011" : "Chị chồng",
  "1012" : "Chị dâu",
  "1013" : "Chị họ",
  "1014" : "Chị vợ",
  "11" : "Em",
  "1110" : "Em ruột",
  "1111" : "Em chồng",
  "1112" : "Em dâu",
  "1113" : "Em họ",
  "1114" : "Em rể",
  "1115" : "Em vợ",
  "12" : "Cháu",
  "1210" : "Cháu nội",
  "1211" : "Cháu ngoại",
  "1212" : "Cháu rể",
  "1213" : "Cháu dâu",
  "1214" : "Cháu họ",
  "13" : "Bác",
  "14" : "Thím",
  "15" : "Cô",
  "16" : "Cậu",
  "17" : "Dì",
  "18" : "Chú",
  "19" : "Bạn",
  "20" : "Chắt",
  "21" : "Cụ",
  "22" : "Người giám hộ",
  "23" : "Người được giám hộ",
  "24" : "Người được chăm sóc",
  "25" : "Người được trợ giúp",
  "26" : "Người được nuôi dưỡng",
  "27" : "Người thuê nhà",
  "28" : "Người mượn nhà",
  "29" : "Người ở nhờ",
  "30" : "Nhân khẩu tập thể",
  "31" : "Đồng nghiệp - CA",
  "32" : "Đồng nghiệp - QĐ",
  "33" : "Cùng ở thuê",
  "99" : "Khác",
  "CH01" : "Chủ hộ",
};
