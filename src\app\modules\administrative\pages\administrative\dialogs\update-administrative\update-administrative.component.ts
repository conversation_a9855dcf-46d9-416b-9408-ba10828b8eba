import { Component, OnInit, AfterV<PERSON>w<PERSON>nit ,On<PERSON>estroy, ViewChild} from '@angular/core';
import { FormGroup, FormControl, Validators } from '@angular/forms';
import { EnvService } from 'src/app/core/service/env.service';
import { SnackbarService } from 'src/app/data/service/snackbar/snackbar.service';
import { MatDialog, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { Inject } from '@angular/core';
import { BasepadService } from 'src/app/data/service/basepad/basepad.service';
import { BasedataService } from 'src/app/data/service/basedata/basedata.service';
import { ReplaySubject, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { DeploymentService } from 'src/app/data/service/deployment.service';
import { ComboboxLazyLoadComponent } from 'src/app/shared/components/combobox-lazy-load/combobox-lazy-load.component';
import { AgencyService } from 'src/app/data/service/basedata/agency.service';
export interface Element {
  id: String;
  name: String;

}

const Agency: Element[] = [];

@Component({
  selector: 'app-update-administrative',
  templateUrl: './update-administrative.component.html',
  styleUrls: ['./update-administrative.component.scss']
})
export class UpdateAdministrativeComponent implements OnInit, AfterViewInit, OnDestroy {
  @ViewChild('searchAgency') searchAgency: ComboboxLazyLoadComponent;
  Id: String;
  countResult1 = 0;
  formTemp;

  countResult = 0;
  // configDepartmentTagId = this.deploymentService.env.OS_HCM.configDepartmentTagId;
  config = this.envService.getConfig();
  selectedLang: string;
  accountId: any;
  guideTitle = [];
  listGuideType = [];
  listGuideTypePage = 0;
  isFullListGuideType = false;
  isSubmit = false;
  listAcceptExt = [];
  listAcceptFileType = [];
  blankVal = '';
  result = [];
  isFieldArrayInvalid = false;
  languageIdUsed = [228];
  listLanguage = [
    {
      languageId: 228,
      name: 'Tiếng Việt'
    },
    {
      languageId: 46,
      name: 'English'
    }
  ];
  newAttribute: any = {
    languageId: 228,
    name: '',
    listLanguageItem: JSON.parse(JSON.stringify(this.listLanguage))
  };
  newItemfield: any = {
    code: '',
    name: '',
    status:'',
  }
  fieldArray: Array<any> = [];
  status: Array<any> = [
    {
      status: 0,
      name: 'Đóng'
    },
    {
      status: 1,
      name: 'Mở'
    }
  ];
  statusVi: Array<any> = [
    {
      status: 0,
      name: 'Đóng'
    },
    {
      status: 1,
      name: 'Mở'
    }
  ];
  statusEn: Array<any> = [
    {
      status: 0,
      name: 'Close'
    },
    {
      status: 1,
      name: 'Open'
    }
  ];
  nameDefault = '';
  protected onDestroy = new Subject<void>();
  updateForm = new FormGroup({
    status: new FormControl(''),
    numberIncreaseAccordingBook: new FormControl(false),
    resetAccordingYear: new FormControl(false)
  });
  oldCodePublic: any;
  
  isAdmin?: boolean = sessionStorage.getItem('oneGateAdminMaster') === '1';
  showPublicAdministrativeAgency = this.deploymentService.env.OS_HCM.showPublicAdministrativeAgency;
  agencyId;
  constructor(
    private dialog: MatDialog,
    public dialogRef: MatDialogRef<UpdateAdministrativeComponent>,
    @Inject(MAT_DIALOG_DATA) public data: ConfirmUpdateDialogModel,
    private envService: EnvService,
    private snackbarService: SnackbarService,
    private basedataService: BasedataService,
    private basepadService: BasepadService,
    private deploymentService: DeploymentService,
    private agencyService: AgencyService
  ) {
    this.Id = data.id;
 
    this.getDetailPublicAdmin(this.Id);
    
  }

  getDetailPublicAdmin(Id) {
    const info = {
      code: '',
      name:'',
      status: ''
    }
    
    let search = '?id=' + Id;
    console.log('update', search);
    this.basepadService.searchPublicAdministration(search).subscribe(async res => {
      let information:any = info;
      information.name = res.content[0].name;
      information.code = res.content[0].code;
      information.status = res.content[0].status;
      this.updateForm.patchValue({
        numberIncreaseAccordingBook: res.content[0]?.numberIncreaseAccordingBook,
        resetAccordingYear: res.content[0]?.resetAccordingYear
      })
      if(this.showPublicAdministrativeAgency && this.showPublicAdministrativeAgency.enable){
        information.agencyId = res.content[0].agencyId;
        this.agencyId = res.content[0].agencyId;
      }
      this.oldCodePublic= res.content[0].code;
      console.log('information', information);
      this.fieldArray.push(JSON.parse(JSON.stringify(information)));
      
      console.log('update', res);
    }, err => {
      console.log(err);
    });

  }

  ngOnInit(): void {
    this.selectedLang = localStorage.getItem('language');
    this.nameDefault = '(Không tìm thấy bản dịch)';
    if (Number(localStorage.getItem('languageId')) === 46) {
      this.status = this.statusEn;
      this.nameDefault = '(No translation found)';
    }
   
  }

  async loadAgency(obj){
    const page = obj?.page || 0;
    const size = obj?.size || 10;
    const parentId = this.getRootAgencyId();
    const tagId = this.showPublicAdministrativeAgency.agencyTagId;
    if(this.showPublicAdministrativeAgency && this.showPublicAdministrativeAgency.enable && (!this.isAdmin || !this.showPublicAdministrativeAgency.admin)){
      let agencyId = this.getAgencyId();
      let rootAgency = await this.agencyService.getRootAgency(agencyId);
      if(rootAgency && rootAgency.tag.find(o => o.id == this.showPublicAdministrativeAgency.agencyTagId)){
        this.searchAgency.update([rootAgency], false);
      }
    }
    else {
      this.agencyService.getPageAgency(page, size, parentId, tagId).subscribe((rs) => {
        const data = [].concat(rs.content);
        const hasNext = !rs.last;
        this.searchAgency.update(data, hasNext);
      });
    }
  }

  getAgencyId(): string | null {
    const userAgency = JSON.parse(localStorage.getItem('userAgency'));
    if (userAgency){
        return userAgency.id;
    }
    return null;
  }

  getRootAgencyId(){
    const userAgency = JSON.parse(localStorage.getItem('userAgency'));
    let rootAgencyId: any = '';
    if (this.config.rootAgency !== null) {
      rootAgencyId = this.config.rootAgency.id;
    } else {
      rootAgencyId = userAgency.id;
    }
    return rootAgencyId;
  }

  ngAfterViewInit() {
    setTimeout(() => {
      //console.clear();
    }, 2000);
  }

  onDismiss() {
    this.dialogRef.close();
  }

  onChangeStatus(event:any){
    let value= event.value;
    this.updateForm.controls.trangThai.setValue(value);
  }

ngOnDestroy() {
  this.onDestroy.next();
  this.onDestroy.complete();
}

async checkExistCode(code){
  let result=true;
  let search= '?page=0&size=1000&spec=page';
  const data= await this.basepadService.searchPublicAdministration(search).toPromise();
    for (let i = 0; i < data.numberOfElements; i++) {
      //debugger;
      if(data.content[i].code==code){
        result= false;
      }
    }
  return result;
 }

  async onConfirm() {
    const formObj = this.updateForm.getRawValue();
    this.isSubmit = true;
    if (this.fieldArray[0].name==='' || this.fieldArray[0].code==='' || this.fieldArray[0].status==='') {
      this.isSubmit = false;
      const msgObj = {
        vi: 'Vui lòng điền đầy đủ thông tin!',
        en: 'Please fill full information!'
      };

      this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
    }else if(this.oldCodePublic!=this.fieldArray[0].code && await this.checkExistCode(this.fieldArray[0].code)==false){
      this.isSubmit = false;
      const msgObj = {
        vi: 'Mã sổ này đã tồn tại!',
        en: 'Please This code already exists!'
      };
  
      this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
    } else {
          
          const formObj = this.updateForm.getRawValue();
          let content:any = {
            code:'',
            name:'',
            status: '',
            numberIncreaseAccordingBook: false,
            resetAccordingYear: false,
          };
          
              content.code = this.fieldArray[0].code;
              content.name= this.fieldArray[0].name;
              content.status = this.fieldArray[0].status;
              content.resetAccordingYear = formObj.resetAccordingYear;
              content.numberIncreaseAccordingBook = formObj.numberIncreaseAccordingBook;
              if(this.showPublicAdministrativeAgency && this.showPublicAdministrativeAgency.enable){
                content.agencyId = this.searchAgency.value[0];
              }
              console.log('form', content);
              const requestBody = JSON.stringify(content, null, 2);
              console.log('form', content, requestBody);
              this.basepadService.putPublicAdministration(content, this.Id).subscribe(data => {
                const result = {
                  status: true
                };
                this.dialogRef.close(result);
                // window.location.reload();
              }, err => {
                const result = {
                  status: false,
                  code: err
                };
                this.dialogRef.close(result);
              });
      
    }

  }


  changeName(i, $event) {
    const value = $event.target.value;
    this.fieldArray[i].name = value.trim();
    this.checkFormTypeInvalid(i);
    setTimeout(() => {
      this.checkFormTypeInvalid3();
    }, 200);
  }
  changeStatus(i, $event) {
    const value = $event.value;
    this.fieldArray[i].status = value;
    this.checkFormTypeInvalid(i);
    setTimeout(() => {
      this.checkFormTypeInvalid3();
    }, 200);
  }
  
  changeCode(i, $event) {
    const value = $event.target.value;
    this.fieldArray[i].code = value.trim();
    this.checkFormTypeInvalid(i);
    setTimeout(() => {
      this.checkFormTypeInvalid3();
    }, 200);
  }


  isNullName = false;

  checkFormTypeInvalid(i) {
    if (this.fieldArray[i].name.trim().length === 0) {
      this.isFieldArrayInvalid = true;
      this.isNullName = true;
    } else {
      this.isFieldArrayInvalid = false;
      this.isNullName = false;
    }
  }

  checkFormTypeInvalid3() {
    let count = 0;
    this.fieldArray.forEach(frmType => {
      if (frmType.name.length === 0) {
        count++;
      }
    });
    if (count === 0) {
      this.isFieldArrayInvalid = false;
      this.isNullName = false;
    } else {
      this.isFieldArrayInvalid = true;
      this.isNullName = true;
    }
  }


  // ========================================================== Manual function

}

export class ConfirmUpdateDialogModel {
  constructor(public id: string) {
  }
}
