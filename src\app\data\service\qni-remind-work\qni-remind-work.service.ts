import { Injectable } from '@angular/core';
import { rejects } from 'assert';
import { resolve } from 'dns';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { ApiProviderService } from 'src/app/core/service/api-provider.service';
import { Observable } from 'rxjs';
import { IFormMap } from '../../schema/form-map';
@Injectable({
  providedIn: 'root'
})
export class QniRemindWorkService {
  private basepad = this.apiProviderService.getUrl('digo', 'basepad');
  private formPath = this.apiProviderService.getUrl('digo', 'basepad') + '/form/';
  private remindWork = this.apiProviderService.getUrl('digo', 'padman') + '/qni-remind-notify';
  private adapter = this.apiProviderService.getUrl('digo', 'adapter');
  private human = this.apiProviderService.getUrl('digo', 'human');
  constructor(
    private http: HttpClient,
    private apiProviderService: ApiProviderService
  ) { }
  getListOverdue(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    console.log('searchString:',searchString);
    return this.http.get(this.remindWork+"/over-due" + searchString, { headers });
  }
  getListDueSoon(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    console.log('searchString:',searchString);
    return this.http.get(this.remindWork+"/due-soon" + searchString, { headers });
  }
  getListAddingRequest(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    console.log('searchString:',searchString);
    return this.http.get(this.remindWork+"/adding-request" + searchString, { headers });
  }
  getListPending(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    console.log('searchString:',searchString);
    return this.http.get(this.remindWork+"/pending" + searchString, { headers });
  }
  getUserRemind(userId: string): Observable<any>{
    const URL = `${this.human}/user-remind/${userId}`;
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get<any>(URL, { headers });
  }
  getListNewRegister(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    console.log('searchString:',searchString);
    return this.http.get(this.remindWork+"/new-register" + searchString, { headers });
  }
  getListNewRegisterHPG(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    console.log('searchString:',searchString);
    return this.http.get(this.remindWork+"/new-register-hpg" + searchString, { headers });
  }
}
