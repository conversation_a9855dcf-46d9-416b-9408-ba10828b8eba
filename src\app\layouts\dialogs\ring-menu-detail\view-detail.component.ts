import { Component, Inject, OnInit } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { Subscription } from 'rxjs';
import { MatTableDataSource } from '@angular/material/table';
import { EnvService } from 'core/service/env.service';
import { SnackbarService } from 'src/app/data/service/snackbar/snackbar.service';
import { DeploymentService } from 'data/service/deployment.service';
import { StatisticsService } from 'src/app/data/service/statistics/statistics.service';
import { ActivatedRoute, Router } from '@angular/router';


export interface RingMenuDossierDetail {
  id: string;
  no: number;
  dossierCode: string;            // Mã số hồ sơ
  procedureName: string;          // Tên thủ tục
  completedDate: string;          // Ng<PERSON>y kết thúc xử lý
  applicantOwnerFullName: string; // Ch<PERSON> hồ sơ
}

@Component({
  selector: 'app-ring-menu',
  templateUrl: './view-detail.component.html',
  styleUrls: ['./view-detail.component.scss']
})
export class RingMenuDetail implements OnInit {
  config = this.envService.getConfig();
  subscription: Subscription;
  paginationType =  this.deploymentService.env.statistics.paginationType;
  displayedColumns: string[] = ['no', 'dossierCode', 'procedureName', 'applicantOwnerFullName', 'completedDate'];
  dataSource: MatTableDataSource<RingMenuDossierDetail>;
  ELEMENTDATA: RingMenuDossierDetail[] = [];
  isWaitingData = false;
  selectedLang: string;

  constructor(
    private router: Router,
    public dialogRef: MatDialogRef<RingMenuDetail>,
    @Inject(MAT_DIALOG_DATA) public listDossier: RingMenuDetailModel,
    private snackbarService: SnackbarService,
    private envService: EnvService,
    private deploymentService: DeploymentService,
    private statisticsService: StatisticsService,
  ) {
    this.dataSource = new MatTableDataSource(this.ELEMENTDATA);
  }

  ngOnInit(): void {
    console.log(this.listDossier);
    this.selectedLang = localStorage.getItem('language');
    this.getDossierStatisticDetail();
  }


  getDossierStatisticDetail() {
    this.ELEMENTDATA = [];
    this.dataSource.data = [];
    let dosssierList = this.listDossier.data;
    if(dosssierList.length > 0) {
      let stt = 0;
      for (let i = 0; i < dosssierList.length; i++) {
        let fullName = dosssierList[i]?.applicant?.data?.fullname ? dosssierList[i].applicant.data.fullname : "";
        let obj = {
          no: stt + 1,
          id : dosssierList[i]?.id ? dosssierList[i].id : "",
          dossierCode: dosssierList[i]?.code ? dosssierList[i].code : "",
          procedureName: dosssierList[i]?.procedure?.code ? dosssierList[i].procedure.code : "",
          completedDate : dosssierList[i]?.appointmentDate ? dosssierList[i].appointmentDate : "",
          applicantOwnerFullName : fullName
        };
          this.ELEMENTDATA.push(obj);
          stt++;
      }
    }
    this.dataSource.data = this.ELEMENTDATA;
  }
    
  

  onClose() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
    this.dialogRef.close();
  }

  openDetail(row = {} as RingMenuDossierDetail){
    let url;
    let dossierDataIndex = this.listDossier.data.findIndex(object => {
      return object.id === row.id;
    }); 
    if (dossierDataIndex != -1) {
      let dossierData = this.listDossier.data[dossierDataIndex];
      if(dossierData.dossierStatus.id != 0){
        let dossierIdTaskCurrent = dossierData?.task ? dossierData.task.filter(t => t.isCurrent === 1)[0].id : '631c42e85a90b05d45c98acb';
        if (dossierIdTaskCurrent != null) {
          url = this.router.serializeUrl(
            this.router.createUrlTree(['dossier/processing/' + dossierData.id], {
              queryParams: {
                procedure: dossierData.procedure.id,
                task: dossierIdTaskCurrent
              }
            }));
        } else {
          const msgObj = {
            vi: 'Không tìm công việc nào cho hồ sơ này!',
            en: 'No task process found for this dossier!'
          };
          this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
        }
      } else {
        url = this.router.serializeUrl(
          this.router.createUrlTree(['dossier/online-reception/receiving/' + dossierData.id], {
            queryParams: {
              procedure : dossierData.procedure.id,
              procedureProcessDef: dossierData.procedureProcessDefinition.id
            }
          })
        );
      }
      window.open(url, '_blank');
      return;
    } else {
      const msgObj = {
        vi: 'Không tìm thấy bước xử lý nào cho hồ sơ này!',
        en: 'No step process found for this dossier!'
      };
      this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
    }
  }
}

export class RingMenuDetailModel {
  constructor(public data: any[]) { }
}
