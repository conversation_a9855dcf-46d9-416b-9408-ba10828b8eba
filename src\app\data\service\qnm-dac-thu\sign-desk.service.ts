import { Injectable } from '@angular/core';
import { rejects } from 'assert';
import { resolve } from 'dns';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { ApiProviderService } from 'src/app/core/service/api-provider.service';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class SignDeskService {

  private basepad = this.apiProviderService.getUrl('digo', 'basepad');
  private padmanPath = this.apiProviderService.getUrl('digo', 'padman') + '/dossier-signdesk';
  
  constructor(
    private http: HttpClient,
    private apiProviderService: ApiProviderService
  ) { }
  postSignDesk(data): Observable<any>{
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.post<any>(this.padmanPath + "/add-sign",data, { headers });
  }
  getSignDesk(id): Observable<any>{
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get<any>(this.padmanPath + "/"+id, { headers });
  }
  updateSignDesk(id,data): Observable<any>{
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.post<any>(this.padmanPath + "/update-sign/"+id,data, { headers });
  }
}