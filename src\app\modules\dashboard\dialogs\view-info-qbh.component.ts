import {Component, Inject, OnInit, NgModule} from '@angular/core';
import {Subscription} from 'rxjs';
import {MAT_DIALOG_DATA, MatDialog, MatDialogRef} from '@angular/material/dialog';
import {EnvService} from 'core/service/env.service';
import { MatTableDataSource } from '@angular/material/table';
import { DossierDetailLogQBHComponent } from '../../qbh-log-8h/dialogs/view-detail-log-qbh.component';

export class ViewDialogInfoQBHModel {
    constructor(public todayDateString ,public userNameTemp,public info, public listDosseirFail, public countDossierFail, public fromDate, public toDate) {}
}

export interface InfoNotificationQbh {
    id: string;
    no: number;
    dossierCode: string;            // Mã số hồ sơ
    procedureName: string;          // Tên thủ tục
    sectorName: string;             // Tên lĩnh vực
    noiDungYeuCauGiaiQuyet: string; // Nội dung
    acceptedDate: string;           // Ngày tiếp nhận
    appointmentDate: string;        // Ngày hẹn trả
    completedDate: string;          // <PERSON><PERSON>y kết thúc xử lý
    applicantOwnerFullName: string; // Chủ hồ sơ
    applicantPhoneNumber: string;   // Số điện thoại
    assigneeFullname: string;       // Cán bộ xử lý hiện tại
    dossierStatusName: string;      // Trạng thái
}

export interface DataDialog {
    fromDate: string;
    toDate: string;
    agencyId: string;
    type: number;
}

@Component({
    selector: 'app-view-info-qbh',
    templateUrl: './view-info-qbh.component.html',
    styleUrls: ['./view-info-qbh.component.scss']
})
export class ViewInfoQBHComponent implements OnInit {
    config = this.envService.getConfig();
    ELEMENTDATA: any[] = [];
    dataSource: MatTableDataSource<any>;
    subscription: Subscription;
    size = 10;
    page = 1;
    pageIndex = 1;
    countResult = 0;
    pgSizeOptions = this.config.pageSizeOptions;
    isWaitingData = false;
    isExportExcel = false;
    info: string;
    listDosseirFail: any;
    countDossierFail: number;
    snackbarService: any;
    fromDate;
    toDate;
    todayDateString;
    userNameTemp;

    constructor(
        public dialogRef: MatDialogRef<ViewInfoQBHComponent>,
        @Inject(MAT_DIALOG_DATA) public data: ViewDialogInfoQBHModel,
        private envService: EnvService,
        private dialog: MatDialog,
    ) {
        this.todayDateString = data.todayDateString;
        this.userNameTemp = data.userNameTemp;
        this.info = data.info;
        this.listDosseirFail = data.listDosseirFail;
        this.countDossierFail = data.countDossierFail;
        this.fromDate = data.fromDate;
        this.toDate = data.toDate;
    }

    ngOnInit(): void {
        
        // this.getData(this.page - 1, this.size);
        
    }
    // getData(page, size) {
    //     this.isWaitingData = true;
    //     const searchDetail = '?from=' + formatDate(this.data.fromDate, 'yyyy-MM-dd', 'en-US') + 'T00:00:00.000Z'
    //         + '&to=' + formatDate(this.data.toDate, 'yyyy-MM-dd', 'en-US') + 'T23:59:59.999Z&agency-id=' + this.data.agencyId
    //         + '&type=' + this.data.type + '&page=' + page + '&size=' + size;
    //     this.qnmStatisticsService.getDigitizationQnmDossierDetail(searchDetail).subscribe(res => {
    //         this.ELEMENT_DATA = res?.content;
    //         this.dataSource.data = this.ELEMENT_DATA;
    //         this.countResult = res?.totalElements;
    //         this.isWaitingData = false;
    //     }, error => {
    //         this.isWaitingData = false;
    //     });
    // }

    // async exportToExcelV1() {
    //     const searchDetail = '?from=' + formatDate(this.data.fromDate, 'yyyy-MM-dd', 'en-US') + 'T00:00:00.000Z'
    //         + '&to=' + formatDate(this.data.toDate, 'yyyy-MM-dd', 'en-US') + 'T23:59:59.999Z&agency-id=' + this.data.agencyId
    //         + '&type=' + this.data.type + '&pagination=' + false;

    //     this.isExportExcel = true;
       
    //     await this.qnmStatisticsService.exportToExcelDigitizationDetail(searchDetail);
    //     this.isExportExcel = false;
    // }

    // paginate(event: any, type) {
    //     switch (type) {
    //         case 0:
    //             this.pageIndex = event;
    //             this.page = event;
    //             this.getData(this.pageIndex - 1, this.size);
    //             break;
    //         case 1:
    //             this.pageIndex = 1;
    //             this.page = 1;
    //             this.getData(this.pageIndex - 1, this.size);
    //             break;
    //     }
    // }
    listDosier() {

    }

    async openDetail() {
        const params = {
            fromDate: this.fromDate,
            toDate: this.toDate,
            totalDossierStr: this.data.listDosseirFail
        };
        const dialogRef = this.dialog.open(DossierDetailLogQBHComponent, {
            width: '80%',
            height: '80%',
            data: params,
            disableClose: true,
            autoFocus: false,
            panelClass: 'full-width-dialog'
        });
        dialogRef.afterClosed().subscribe(dialogResult => { });
    }
    onClose() {
        if (this.subscription) {
            this.subscription.unsubscribe();
        }
        this.dialogRef.close();
    }
    onDismiss() {
        this.dialogRef.close();
      }
}
export interface DossierStatisticDetail {
    no: number;
    dossierCode: string;            // Mã số hồ sơ
    procedureName: string;          // Tên thủ tục
    sectorName: string;             // Tên lĩnh vực
    noiDungYeuCauGiaiQuyet: string; // Nội dung
}
