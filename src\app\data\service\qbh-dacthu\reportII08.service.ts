import { Injectable } from '@angular/core';
import {HttpClient, HttpHeaders, HttpParams} from '@angular/common/http';
import { ApiProviderService } from 'src/app/core/service/api-provider.service';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class II08QBHReportsService {

  constructor(
    private http: HttpClient,
    private apiProviderService: ApiProviderService
  ) { }

  private basepad = this.apiProviderService.getUrl('digo', 'basepad');
  private basedata = this.apiProviderService.getUrl('digo', 'basedata');
  private padman = this.apiProviderService.getUrl('digo', 'padman');

  getSectorProcedureByLevelId(search: String): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.basepad + '/qbh-procedure/--get-sector-by-agency-level' + search, { headers });
  }

  getDossierSectorProcedureByLevelId(searchStr: String): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.padman + '/qbh-dossier-statistic/--by-agency-report-II08' + searchStr, { headers });
  }
}
