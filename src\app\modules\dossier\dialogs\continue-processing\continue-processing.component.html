<button mat-icon-button class="close-button" (click)="onDismiss()">
    <mat-icon>close</mat-icon>
</button>
<h3 class="dialog_title" mat-dialog-title><PERSON><PERSON><PERSON> c<PERSON>u tiếp tục</h3>
<div mat-dialog-content class="dialog_content">
    <span>Bạn có chắc chắn muốn tiếp tục x<PERSON> lý hồ sơ</span><span
        class="highlight">{{dossierCode}}</span><span>?</span>
    <!-- <div class="commentEditor" fxLayout="column" fxLayout.xs="column" fxLayout.sm="column">
        <p class="lbl"><span i18n>Lý do</span><span style="color:#ce7a58;">&nbsp;*</span></p>
        <ckeditor debounce="10" [editor]="Editor" class="customCKEditor" (change)="onCommentEditorChange($event)"
            fxFlex='grow' [config]='commentConfig'>
        </ckeditor>
        <div class="errorMsg" *ngIf="isCKMaxlenght">
            <span i18n>Nội dung không quá 500 ký tự</span>
            <div class="err">
                <mat-icon>priority_high</mat-icon>
            </div>
        </div>
    </div> -->
    
</div>

<!-- <digo-check-send-notify functionType="paymentRequestDossier" functionTypeOfficer="" [receiveType]="env.enableApprovalOfLeadership"></digo-check-send-notify> -->

<div fxLayout="row" fxLayout.xs="column" fxLayout.sm="column" fxLayoutAlign="center">
    <button mat-flat-button fxFlex='30' class="applyBtn" (click)="onConfirm()">
        <span i18n>Đồng ý</span>
    </button>
</div>