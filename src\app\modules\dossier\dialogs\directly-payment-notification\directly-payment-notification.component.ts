import { Component, Inject, OnInit } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { EnvService } from 'src/app/core/service/env.service';
import { DeploymentService } from 'src/app/data/service/deployment.service';
import { NotificationService } from 'src/app/data/service/notification.service';
import { SnackbarService } from 'src/app/data/service/snackbar/snackbar.service';
import { PaymentRequestComponent } from '../payment-request/payment-request.component';
import * as ClassicEditor from '@ckeditor/ckeditor5-build-classic/build/ckeditor';
import { DossierService } from 'src/app/data/service/dossier/dossier.service';
import { PadmanService } from 'src/app/data/service/svc-padman/padman.service';
import { BasepadService } from 'src/app/data/service/basepad/basepad.service';
import { KeycloakService } from 'keycloak-angular';
import { UserService } from 'src/app/data/service/user.service';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import * as tUtils from 'src/app/data/service/thoai.service';
import { DatePipe } from '@angular/common';
import { AgencyService } from 'src/app/data/service/basedata/agency.service';

@Component({
  selector: 'app-directly-payment-notification',
  templateUrl: './directly-payment-notification.component.html',
  styleUrls: ['./directly-payment-notification.component.scss']
})
export class DirectlyPaymentNotificationComponent implements OnInit {
  config = this.envService.getConfig();
  env: any = this.deploymentService.getAppDeployment()?.env;
  selectedLang = localStorage.getItem('language') || 'vi';
  selectedLangId = Number(localStorage.getItem('languageId')) || 228;
  dossierCode: string;
  dossierId: string;
  dossierDetail: any;
  currentTask: any;
  commentContent = '';
  commentContentPlainText = '';
  hideDaysWaitingForPayment = false;
  isCKMaxlength = false;
  commentConfig = {
    placeholder: 'Nhập lý do...',
    language: 'vi',
    removePlugins: ['MediaEmbed', 'ImageUpload'],
    toolbarLocation: 'bottom'
  };
  public Editor = ClassicEditor;
  receiveType = 2;
  totalCost = '';
  userName: string;
  accountId: string;
  checkConfirmPayment = false;
  showConfirmPayment = false;
  disableConfirmPayment = false;
  showCheckConfirmPayment = this.deploymentService.env.OS_HCM.showCheckConfirmPayment;
  updateForm = new FormGroup({
    daysWaitingForPayment: new FormControl(1)
  })
  dossierStatus = { id: '', name: []};
  dossierTaskStatus = { id: '', name: [] };
  dossierMenuTaskRemind = { id: '', name: [] };
  directlyPaymentDue = "";
  directlyPayment = this.deploymentService.env?.OS_HCM?.directlyPayment ? 
    this.deploymentService.env?.OS_HCM?.directlyPayment : 
    {
      sentDirectlyPaymentNotificationTaskStatus: "6412aa8ef4a42852018da4fa",
      sentDirectlyPaymentNotificationTagId: "63fb7a261aee0a3ee93c8dac",
    } 
  isSmsQNM = this.env?.OS_QNM?.isSmsQNM ? true : false;
  updateRequireFieldForPayReDossier = this.deploymentService.getAppDeployment()?.updateRequireFieldForPayReDossier == 1 ? true : false;

  constructor(
    public dialogRef: MatDialogRef<PaymentRequestComponent>,
    @Inject(MAT_DIALOG_DATA) public data: ConfirmDirectlyPaymentNotificationDialogModel,
    private snackbarService: SnackbarService,
    private envService: EnvService,
    private deploymentService: DeploymentService,
    private notiService: NotificationService,
    private dossierService: DossierService,
    private padmanService: PadmanService,
    private keycloakService: KeycloakService,
    private userService: UserService,
    private datePipe: DatePipe,
    private basepadService: BasepadService,
    private agencyService: AgencyService
  ) {
    this.dossierCode = data.dossierCode;
    this.dossierId = data.dossierId;
   }

  ngOnInit(): void {
    this.getUserAccount();
    this.getDetailDossier();
    this.getDossierTaskStatus();
    this.getDossierMenuTaskRemind();
  }

  getPlainText( strSrc ) {
    let resultStr = "";

    // Ignore the <p> tag if it is in very start of the text
    if(strSrc.indexOf('<p>') == 0)
        resultStr = strSrc.substring(3);
    else
        resultStr = strSrc;

    // Replace <p> with two newlines
    resultStr = resultStr.replace(/<p>/gi, "\r\n\r\n");
    // Replace <br /> with one newline
    resultStr = resultStr.replace(/<br \/>/gi, "\r\n");
    resultStr = resultStr.replace(/<br>/gi, "\r\n");

    //-+-+-+-+-+-+-+-+-+-+-+
    // Strip off other HTML tags.
    //-+-+-+-+-+-+-+-+-+-+-+

    return  resultStr.replace( /<[^<|>]+?>/gi,'' );
  }

  onCommentEditorChange(event) {
    this.commentContent = event.editor.getData();
    if (event.editor.getData().trim().length > 500) {
      this.isCKMaxlength = true;
    } else {
      this.isCKMaxlength = false;
    }

    this.commentContentPlainText = "";
    this.commentContentPlainText = this.getPlainText(this.commentContent);
  }

  async onConfirm() {
    this.disableConfirmPayment = true;
    const formObj = this.updateForm.getRawValue();
    if (this.commentContent.trim() === '') {
      this.disableConfirmPayment = false;
      const msgObj = {
        vi: 'Vui lòng nhập ý kiến xử lý!',
        en: 'Please enter a handling comments!'
      };
      this.snackbarService.openSnackBar(2, '', msgObj[this.selectedLang], 'warning_notification', this.config.expiredTime);
      return;
    }
    //Hiện thông báo khi bật tham số và chưa check đồng ý
    if(this.showCheckConfirmPayment && !this.checkConfirmPayment && this.showConfirmPayment)
    {
      this.disableConfirmPayment = false;
      const msgObj = {
        vi: 'Vui lòng đồng ý để xác nhận số tiền yêu cầu thanh toán!',
        en: 'Please agree to confirm the amount requested for payment!'
      };
      this.snackbarService.openSnackBar(2, '', msgObj[this.selectedLang], 'warning_notification', this.config.expiredTime);
      return;
    }

    // update bắt buộc thanh toán cho phí > 0
    if (this.updateRequireFieldForPayReDossier) {
      this.dossierService.updateRequiredFee(this.dossierId).subscribe();
      await this.dossierService.updateRequiredFee(this.dossierId).toPromise();
    }

    // Hiện thông báo khi nhập số ngày không hợp lệ
    if (!formObj.daysWaitingForPayment) {
      this.disableConfirmPayment = false;
      const msgObj = {
        vi: 'Vui lòng nhập số ngày chờ thanh toán',
        en: 'Please enter the number of days waiting for payment',
      };
      this.snackbarService.openSnackBar(2, '', msgObj[this.selectedLang], 'warning_notification', this.config.expiredTime);
      return;
    }
    if (this.commentContent.trim() !== '') {
      await this.postTimesheetDirectlyPaymentNotification();
      this.sendDirectlyPaymentNotification();
      const agencies = this.agencyService.getAgencies();
      const extend = {
        dossier:{
          id: this.dossierId,
          code: this.dossierCode,
        },
        agencies: agencies
      };
      
      await this.notiService.changeSendSubject.next(
        {
          id: this.dossierDetail?.procedureProcessDefinition?.id,
          phone: this.dossierDetail?.applicant?.data?.phoneNumber,
          email: this.dossierDetail?.applicant?.data?.email,
          currentTask: this.currentTask,
          contentParams: {
            parameters: {
              sector: !!this.dossierDetail?.procedure?.sector?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name ? this.dossierDetail?.procedure?.sector?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name : '',
              procedure: !!this.dossierDetail?.procedure?.translate?.find(agency => agency.languageId === Number(this.selectedLangId))?.name ? this.dossierDetail?.procedure?.translate?.find(agency => agency.languageId === Number(this.selectedLangId))?.name : '',
              agency: !!this.dossierDetail?.agency?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name ? this.dossierDetail?.agency?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name : '',
              agencyId: !!this.dossierDetail?.agency?.id ? this.dossierDetail?.agency?.id : this.dossierDetail?.agency?.parent?.id,
              extend: this.isSmsQNM ? extend : {},
              applicantFullname: this.dossierDetail?.applicant?.data?.fullname,
              officerFullname: '',
              subsystem: !!this.env?.subsystem?.name ? this.env?.subsystem?.name : "Hệ thống Một cửa điện tử",
              dossierCode: this.dossierDetail?.nationCode ? this.dossierDetail?.nationCode : this.dossierDetail?.code,
              nextTask: !!this.env?.notify?.additionalRequirement?.nextTask ? this.env?.notify?.additionalRequirement?.nextTask : 'Yêu cầu bổ sung hồ sơ',
              // IGATESUPP-63184
              nextStatus: !!this.env?.notify?.directlyPaymentNotification?.nextStatus ? this.env?.notify?.directlyPaymentNotification?.nextStatus : (this.dossierDetail?.dossierTaskStatus?.name ?? ''), // cho bo sung, trang thai hien tai cua ho so
              dossierStatus: !!this.env?.notify?.directlyPaymentNotification?.dossierStatus ? this.env?.notify?.directlyPaymentNotification?.dossierStatus : (this.dossierDetail?.dossierStatus?.name?.find(t => t.languageId === Number(this.selectedLangId))?.name ?? ''), // cho bo sung, trang thai hien tai cua ho so
              dossierSearchPadsvcUrl: (!!this.env?.domain?.padsvc ? this.env?.domain?.padsvc : "http://padsvctest.vncitizens.vn/vi/") + "dossier/public",
              dossierDetailPadsvcUrl: (!!this.env?.domain?.padsvc ? this.env?.domain?.padsvc : "http://padsvctest.vncitizens.vn/vi/") + "dossier/detail/" + this.dossierId,
              dossierSearchUrl: (!!this.env?.domain?.onegate ? this.env?.domain?.onegate : "http://padsvctest.vncitizens.vn/vi/") + "dossier/search/",
              dossierDetailUrl: (!!this.env?.domain?.onegate ? this.env?.domain?.onegate : "http://padsvctest.vncitizens.vn/vi/") + "dossier/search/" + this.dossierId,
              dossierPayDetailPadsvcUrl: (!!this.env?.domain?.onegate ? this.env?.domain?.onegate : "http://padsvctest.vncitizens.vn/vi/") + "dossier/payment/" + this.dossierId,
              returnMethod: !!this.dossierDetail?.dossierReceivingKind?.name?.find(recieve => recieve.languageId === Number(this.selectedLangId))?.name ? this.dossierDetail?.dossierReceivingKind?.name?.find(recieve => recieve.languageId === Number(this.selectedLangId))?.name : '',
              receivingDate: '',
              appointmentDate: '',
              dossierFee: this.totalCost,
              reason: this.commentContentPlainText,
              directlyPaymentDue: this.datePipe.transform(new Date(this.directlyPaymentDue), 'dd/MM/yyyy HH:mm:ss'),
            }
          }
        }
      );

      this.postComment(this.commentContent.trim());
      this.notiService.confirmSendSubject.next({
        confirm: true,
        renewContent: true
      });
      this.disableConfirmPayment = false;
      this.dialogRef.close(true);
    }
  }

  getDossierTaskStatus() {
    let tagId = this.directlyPayment.sentDirectlyPaymentNotificationTaskStatus;
    this.dossierService.getAgencyTag(tagId).subscribe(rs => {
      this.dossierTaskStatus.id = rs.id;
      this.dossierTaskStatus.name = rs.trans;
    }, err => {
      console.log(err);
    });
  }
  getDossierMenuTaskRemind() {
    let tagId = this.directlyPayment.sentDirectlyPaymentNotificationTagId;
    this.dossierService.getAgencyTag(tagId).subscribe(rs => {
      this.dossierMenuTaskRemind.id = rs.id;
      this.dossierMenuTaskRemind.name = rs.trans;
    }, err => {
      console.log(err);
    });
  }
  
  async getDetailDossier() {
    await this.getDossierFeeNotify();
    this.dossierService.getDossierDetail(this.dossierId).subscribe(data => {
      this.dossierDetail = data;
      this.currentTask = null;
      if (!!data.currentTask && data.currentTask.length > 0){
        this.currentTask = data.currentTask[0];
      }
      this.dossierDetail = JSON.parse(JSON.stringify(data));
      this.dossierStatus = data?.dossierStatus;
      this.notiService.checkSendSubject.next(
        {
          id: data?.procedureProcessDefinition?.id,
          phone: data?.applicant?.data?.phoneNumber,
          email: data?.applicant?.data?.email,
          currentTask: this.currentTask,
          contentParams: {
            parameters: {
              sector: !!data?.procedure?.sector?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name ? data?.procedure?.sector?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name : '',
              procedure: !!data?.procedure?.translate?.find(agency => agency.languageId === Number(this.selectedLangId))?.name ? data?.procedure?.translate?.find(agency => agency.languageId === Number(this.selectedLangId))?.name : '',
              agency: !!data?.agency?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name ? data?.agency?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name : '',
              agencyId: !!data?.agency?.id ? data?.agency?.id : data?.agency?.parent?.id,
              extend: {},
              applicantFullname: data?.applicant?.data?.fullname,
              officerFullname: '',
              subsystem: !!this.env?.subsystem?.name ? this.env?.subsystem?.name : "Hệ thống Một cửa điện tử",
              dossierCode: data?.nationCode ? data?.nationCode : data?.code,
              nextTask: !!this.env?.notify?.additionalRequirement?.nextTask ? this.env?.notify?.additionalRequirement?.nextTask : 'Yêu cầu bổ sung hồ sơ',
              // IGATESUPP-63184
              nextStatus: !!this.env?.notify?.directlyPaymentNotification?.nextStatus ? this.env?.notify?.directlyPaymentNotification?.nextStatus : (this.dossierDetail?.dossierTaskStatus?.name ?? ''), // cho bo sung, trang thai hien tai cua ho so
              dossierStatus: !!this.env?.notify?.directlyPaymentNotification?.dossierStatus ? this.env?.notify?.directlyPaymentNotification?.dossierStatus : (this.dossierDetail?.dossierStatus?.name?.find(t => t.languageId === Number(this.selectedLangId))?.name ?? ''), // cho bo sung, trang thai hien tai cua ho so
              dossierSearchPadsvcUrl: (!!this.env?.domain?.padsvc ? this.env?.domain?.padsvc : "http://padsvctest.vncitizens.vn/vi/") + "dossier/public",
              dossierDetailPadsvcUrl: (!!this.env?.domain?.padsvc ? this.env?.domain?.padsvc : "http://padsvctest.vncitizens.vn/vi/") + "dossier/detail/" + this.dossierId,
              dossierSearchUrl: (!!this.env?.domain?.onegate ? this.env?.domain?.onegate : "http://padsvctest.vncitizens.vn/vi/") + "dossier/search/",
              dossierDetailUrl: (!!this.env?.domain?.onegate ? this.env?.domain?.onegate : "http://padsvctest.vncitizens.vn/vi/") + "dossier/search/" + this.dossierId,
              dossierPayDetailPadsvcUrl: (!!this.env?.domain?.onegate ? this.env?.domain?.onegate : "http://padsvctest.vncitizens.vn/vi/") + "dossier/payment/" + this.dossierId,
              returnMethod: !!data?.dossierReceivingKind?.name?.find(recieve => recieve.languageId === Number(this.selectedLangId))?.name ? data?.dossierReceivingKind?.name?.find(recieve => recieve.languageId === Number(this.selectedLangId))?.name : '',
              receivingDate: '',
              appointmentDate: '',
              dossierFee: this.totalCost,
              reason: this.commentContentPlainText,
              directlyPaymentDue: this.directlyPaymentDue,
            }
          }
        }
      );
      this.getProcedure(data?.procedure?.id);
    });
  }

  getDossierFeeNotify() {
    return new Promise<void>(resolve => {
      this.padmanService.getDossierFee(this.dossierId).toPromise().then(res => {
        if(res && res.length > 0)
        {
          let totalCostValue = 0;
          let totalPaidValue = 0;
          res.forEach(element => {
            totalCostValue += element.quantity * element.amount;
            totalPaidValue += element.paid;
          });
          const monetaryUnit = res[0]?.monetaryUnit ? res[0].monetaryUnit : 'VNĐ';
          let total = totalCostValue - totalPaidValue;
          this.totalCost = total >= 0 ? Number((total).toFixed(1)).toLocaleString() + ' ' + monetaryUnit : '';
        }
        resolve();
      }).catch(err => {
        resolve();
      });
    });
  }

  getProcedure(procedureId) {
    this.basepadService.getProcedureDetail(procedureId).subscribe(async data => {
      if(data && !!data.isConfirmPayment) {
        this.checkConfirmPayment = true;
      }
      if(data && !!data.isCheckConfirmPayment) {
        this.showConfirmPayment = true;
      }
      const userAgency = JSON.parse(localStorage.getItem('userAgency'));
      if (this.env?.OS_HCM?.hideDaysWaitingForPayment.includes(userAgency?.id)){
        console.log('hideDaysWaitingForPayment');
        const formObj = this.updateForm.getRawValue();
        formObj.daysWaitingForPayment = 0;
        this.hideDaysWaitingForPayment = true;
      }
    }, err => {
      console.log(err);
    });
  }

  postComment(commentContent, description?:string) {
    let msgObj = {};
    if(!!description){
      msgObj = {
        vi: `Yêu cầu thanh toán trực tiếp: ${commentContent}<br>Nội dung: ${description}`,
        en: `Directly payment request: ${commentContent}<br>Description: ${description}`
      };
    }else{
      msgObj = {
        vi: `Yêu cầu thanh toán trực tiếp: ${commentContent}`,
        en: `Directly payment request: ${commentContent}`
      };
    }
    const content = {
      groupId: 2,
      itemId: this.dossierId,
      user: {
        id: this.accountId,
        fullname: this.userName
      },
      content: msgObj[this.selectedLang],
      // file: this.uploadedImage
    };
    const requestBody = JSON.stringify(content, null, 2);
    this.dossierService.postComment(requestBody).subscribe(data => { });
  }

  sendDirectlyPaymentNotification() {
    const formObj = this.updateForm.getRawValue();
    let requestBodyObj = {
      dossierStatus: 18,
      comment: this.commentContent,
      dossierTaskStatus: this.dossierTaskStatus,
      dossierMenuTaskRemind: this.dossierMenuTaskRemind,
      directlyPaymentNotification: {
        numberWaitingDirectlyPaymentDay: formObj.daysWaitingForPayment,
        directlyPaymentDue: this.directlyPaymentDue,
        sentNotificationBefore: this.dossierDetail?.extendHCM?.direclyPaymentData?.sentNotificationBefore ? true : false, 
        updateOldData: this.dossierDetail?.dossierStatus?.id === 18 ? false : true
      },
    };
    this.dossierService.putDossierStatusWithComment(this.dossierId, requestBodyObj).subscribe(data => { });
  }

  async postTimesheetDirectlyPaymentNotification() {
    const currentDate = this.datePipe.transform(tUtils.newDate(), 'yyyy-MM-ddTHH:mm:ss.SSSZ');
    const formObj = this.updateForm.getRawValue();
    const listTimesheet = [];
    const processingTimeUnit = "d";
    listTimesheet.push({
      timesheet: {
        id:
          this.dossierDetail.procedureProcessDefinition?.processDefinition
            ?.timesheet !== undefined
            ? this.dossierDetail.procedureProcessDefinition
              .processDefinition.timesheet.id
            : this.config.defaultTimesheetId,
      },
      dossier: {
        id: this.dossierId,
      },
      duration: formObj.daysWaitingForPayment,
      startDate: currentDate,
      endDate: null,
      checkOffDay: true,
      offTime: this.env?.limitedAppointmentTime ? this.env?.limitedAppointmentTime : null,
      processingTimeUnit: processingTimeUnit,
    });

    const requestBody = JSON.stringify(listTimesheet, null, 2);
    const data = await this.genTime(requestBody);

    if (!!data) {
      this.directlyPaymentDue = data[0].due;
    } else {
      return null;
    }
  }

  genTime(requestBody) {
    return new Promise(resolve => {
      this.dossierService.postTimesheet(requestBody).subscribe(data => {
        if (!!data && data.length !== 0) {
          resolve(data);
        } else {
          resolve(null);
        }
      }, (err) => {
        resolve(null);
      });
    });
  }

  getUserAccount() {
    this.keycloakService.loadUserProfile().then(user => {
      // tslint:disable-next-line: no-string-literal
      this.accountId = user['attributes'].user_id[0];
      this.userService.getUserInfo(this.accountId).subscribe(data => {
        this.userName = data.fullname;
      });
    });
  }

  onDismiss() {
    this.dialogRef.close();
  }
}

export class ConfirmDirectlyPaymentNotificationDialogModel {
  constructor(public dossierId: string, public dossierCode: string, public typeProcess?) {
  }
}
