<button mat-icon-button class="close-button" (click)="onDismiss()">
    <mat-icon>close</mat-icon>
</button>
<span class="dialog_title">DANH SÁCH HỒ SƠ {{lblTitle}}</span>
<div fxLayout="row" fxLayoutAlign="center">
    <div class="frm_main sector table-scroll-remind" fxFlex="grow">
        <div class="frm_tbl_sector sector" style="height: 700px; overflow: auto;">
            <table mat-table
                   [dataSource]="dataSource">
                   <!-- stt -->
              <ng-container matColumnDef="stt">
                <mat-header-cell *matHeaderCellDef i18n>STT</mat-header-cell>
                <mat-cell *matCellDef="let row" i18n-data-label data-label="STT"> {{row.stt}} </mat-cell>
              </ng-container>

              <ng-container matColumnDef="codee">
                <mat-header-cell *matHeaderCellDef><PERSON><PERSON> <PERSON>ồ sơ</mat-header-cell>
                <mat-cell *matCellDef="let row" data-label="<PERSON><PERSON> hồ sơ" >
                    <a   class = "btn" (click)="openDetail(row.id, row.procedure.id, row.task, row?.currentTask)">{{row.code}}</a>
                </mat-cell>
              </ng-container>

              <ng-container matColumnDef="procedure">
                <mat-header-cell  *matHeaderCellDef>Thủ tục hành chính</mat-header-cell>
                <mat-cell *matCellDef="let row" data-label="Thủ tục hành chính">
                           <span *ngIf = "row.procedure?.translate?.name != null && row.procedure?.translate?.name != 'underfined'">
                              {{row.procedure?.translate?.name}}
                          </span>
                          <span *ngIf = "row.procedure?.translate.length > 0">
                            {{row.procedure?.translate[0]?.name}}
                        </span>
                </mat-cell>
              </ng-container>

              <ng-container matColumnDef="sector">
                <mat-header-cell *matHeaderCellDef>Lĩnh vực</mat-header-cell>
                <mat-cell *matCellDef="let row" data-label="Lĩnh vực">
                          <span>
                              {{row.procedure?.sector?.name[0]?.name}}
                          </span>
                </mat-cell>
              </ng-container>

              <ng-container matColumnDef="required">
                <mat-header-cell *matHeaderCellDef>Yêu cầu giải quyết</mat-header-cell>
                <mat-cell *matCellDef="let row" data-label="Yêu cầu giải quyết">
                          <span>
                              {{row?.applicant?.data?.noidungyeucaugiaiquyet}}
                          </span>
                </mat-cell>
              </ng-container>

              <ng-container matColumnDef="assignedDate">
                <mat-header-cell *matHeaderCellDef>Ngày tiếp nhận</mat-header-cell>
                <mat-cell *matCellDef="let row" data-label="Ngày tiếp nhận">
                  <span>{{row?.acceptedDate|date: 'dd/MM/yyyy HH:mm:ss'}}</span>
                </mat-cell>
              </ng-container>

              <ng-container matColumnDef="appointmentDate">
                <mat-header-cell *matHeaderCellDef>Ngày hẹn trả</mat-header-cell>
                <mat-cell *matCellDef="let row" data-label="Ngày hẹn trả">
                  <span>{{row?.appointmentDate|date: 'dd/MM/yyyy HH:mm:ss'}}</span>
                </mat-cell>
              </ng-container>

              <ng-container matColumnDef="completedDate">
                <mat-header-cell *matHeaderCellDef>Ngày kết thúc xử lý</mat-header-cell>
                <mat-cell *matCellDef="let row" data-label="Ngày kết thúc xử lý">
                  <span>{{row.completedDate|date: 'dd/MM/yyyy HH:mm:ss'}}</span>
                </mat-cell>
              </ng-container>

              <ng-container matColumnDef="owner">
                <mat-header-cell *matHeaderCellDef>Chủ hồ sơ</mat-header-cell>
                <mat-cell *matCellDef="let row" data-label="Chủ hồ sơ">
                          <span>
                              {{row?.applicant?.data?.ownerFullname}}
                          </span>
                </mat-cell>
              </ng-container>

              <ng-container matColumnDef="user">
                <mat-header-cell *matHeaderCellDef>Cán bộ tiếp nhận</mat-header-cell>
                <mat-cell *matCellDef="let row" data-label="Cán bộ tiếp nhận">
                          <span *ngIf = "row?.canBoXuLy != null " >
                            {{ row?.canBoXuLy ? row?.canBoXuLy[0]?.fullname : "" }}
                          </span>
                          <span *ngIf = "row?.canBoXuLy == null " >
                            {{ row?.task && row?.task.length > 0 && row?.task[0]?.assignee ? row?.task[0]?.assignee?.fullname : ""}}
                          </span>
                </mat-cell>
              </ng-container>



              <ng-container matColumnDef="status">
                <mat-header-cell *matHeaderCellDef>Trạng thái</mat-header-cell>
                <mat-cell *matCellDef="let row" data-label="Trạng thái">
                          <span>
                              {{row?.dossierStatus?.name[0]?.name ? row?.dossierStatus?.name[0].name  :row?.dossierStatus?.name}}
                          </span>
                </mat-cell>
              </ng-container>

              <mat-header-row *matHeaderRowDef="displayedColumns"></mat-header-row>
              <mat-row *matRowDef="let row; columns: displayedColumns;"></mat-row>
            </table>
            <div class="frm_Pagination">
      <ul class="temp_Arr">
        <li *ngFor="let item of ELEMENTDATA  | paginate: {itemsPerPage: size, currentPage: page, totalItems: countResult, id: 'pgnx'}"></li>
      </ul>
      <div class="pageSize">
        <span i18n>Hiển thị </span>
        <mat-form-field appearance="outline">
          <mat-select [(value)]="size" (valueChange)="paginate(pageIndex, 1)">
            <mat-option *ngFor='let opt of pgSizeOptions;' [value]="opt">{{opt}}</mat-option>
          </mat-select>
        </mat-form-field>
        <span><span i18n>trên</span> {{countResult}} <span i18n>bản ghi</span></span>
      </div>
      <div class="control">
        <pagination-controls id="pgnx" (pageChange)="page = $event; paginate(page, 0)" responsive="true"
                             previousLabel="" nextLabel="">
        </pagination-controls>
      </div>
    </div>
          <div style="display: flex; width: 100%;  justify-content: center;">
            <button style = "margin-left: 10px;" mat-flat-button  class="primary-btn-excel" (click)="exportToExcel()">
              <mat-icon style = "margin-right: 10px;">cloud_download</mat-icon>
              <span>Xuất excel</span>
            </button>
            <button style = "margin-left: 10px; width: 131px" mat-flat-button class="primary-btn"  (click)="onDismiss()" >
              <span> Đóng </span>
            </button>
          </div>
       </div>
    </div>
</div>
