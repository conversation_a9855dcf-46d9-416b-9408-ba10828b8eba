import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { DlkHoSoToiHanRoutingModule } from './dlk-hosotoihan-routing.module';
import { DlkHoSoToiHanComponent } from './dlk-hosotoihan.component';
import { SharedModule } from 'src/app/shared/shared.module';
import { NgxPrinterModule } from 'ngx-printer';


@NgModule({
  declarations: [DlkHoSoToiHanComponent],
  imports: [
    CommonModule,
    DlkHoSoToiHanRoutingModule,
    SharedModule,
    NgxPrinterModule.forRoot({ printOpenWindow: true })
  ]
})
export class DlkHoSoToiHanModule { }
