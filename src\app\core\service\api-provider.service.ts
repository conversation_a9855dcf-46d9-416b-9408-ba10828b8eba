import { Injectable } from '@angular/core';
import { EnvService } from './env.service';
import { DeploymentService } from 'src/app/data/service/deployment/deployment.service';

export interface Service {
  name: string;
  path: string;
  rootUrl?: string;
}

export interface Provider {
  name: string;
  rootUrl: string;
  services?: Service[];
}

@Injectable({
  providedIn: 'root'
})
export class ApiProviderService {
  private configs;
  private apiProviders;

  constructor(private envService: EnvService,
              private deploymentService: DeploymentService) {
    this.configs = this.deploymentService.getConfig();
    this.apiProviders = envService.getConfig().apiProviders;
  }

  getUrl(providerName: string, serviceName?: string): string {
    let url: string = null;
    try{
      let provider: Provider = this.apiProviders[providerName];
      let services = provider?.services;
      let service = services[serviceName];
      let serviceRootUrl = service?.rootUrl;
      if(serviceRootUrl){
        url = serviceRootUrl;
      }else {
        const gateway = this.configs?.gateway;
        const gwServices = gateway?.services
        const gwRootUrls = gateway?.rootUrls;
        if(gwServices && gwServices[serviceName]){
          const gwService = gwServices[serviceName];
          const gwPath = gwService?.path;
          let gwRootUrl = gwRootUrls?.rootUrl0;
          if(gwService?.rootUrl){
            gwRootUrl = gwRootUrls[gwService?.rootUrl];
          }
          url = `${gwRootUrl}/${gwPath}`;
        }else {
          const path = service?.path;
          const rootUrl = provider?.rootUrl;
          url = `${rootUrl}/${path}`;
        }
      }
    }catch (ex){}
    return url;
  }
}

