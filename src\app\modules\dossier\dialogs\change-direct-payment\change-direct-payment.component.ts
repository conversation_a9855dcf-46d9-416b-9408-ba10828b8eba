import { Component, OnInit, Inject } from '@angular/core';
import * as ClassicEditor from '@ckeditor/ckeditor5-build-classic/build/ckeditor';
import { EnvService } from 'src/app/core/service/env.service';
import { DossierService } from 'src/app/data/service/dossier/dossier.service';
import { DeploymentService } from 'src/app/data/service/deployment.service';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { SnackbarService } from 'src/app/data/service/snackbar/snackbar.service';
import * as tUtils from 'src/app/data/service/thoai.service';
import { UserService } from 'src/app/data/service/user.service';
import { KeycloakService } from 'keycloak-angular';

@Component({
  selector: 'app-change-direct-payment',
  templateUrl: './change-direct-payment.component.html',
  styleUrls: ['./change-direct-payment.component.scss']
})
export class ChangeDirectPaymentComponent implements OnInit {

  config = this.envService.getConfig();
  selectedLang = localStorage.getItem('language') || 'vi';
  selectedLangId = Number(localStorage.getItem('languageId')) || 228;
  dossierId: string;
  commentContent = '';
  userName: string;
  accountId: string;
  dossierCode: string;
  isCKMaxlenght = false;
  dossierDetail: any;
  oldPaymentMethod: any;
  newPaymentMethod: any;
  dueWaitPaidDay='';
  oldDossierStatus: any;
  currentDossierStatus: any;
  oldData:any;
  newData:any;
  appointmentDate = '';
  dueDate = '';
  durationDate = 0;

  public Editor = ClassicEditor;
  commentConfig = {
    placeholder: 'Nhập lý do...',
    language: 'vi',
    removePlugins: ['MediaEmbed', 'ImageUpload'],
    toolbarLocation: 'bottom'
  };
  descriptionConfig = {
    placeholder: 'Nhập nội dung...',
    language: 'vi',
    removePlugins: ['MediaEmbed', 'ImageUpload'],
    toolbarLocation: 'bottom'
  };

  showChangeDirectPayment = this.deploymentService.env?.OS_HCM?.showChangeDirectPayment;
  allowChange=true;
  constructor(
    private envService: EnvService,
    private userService: UserService,
    public dialogRef: MatDialogRef<ChangeDirectPaymentComponent>,
    @Inject(MAT_DIALOG_DATA) public data: ChangeDirectPaymentDialogModel,
    private snackbarService: SnackbarService,
    private deploymentService: DeploymentService,
    private dossierService: DossierService,
    private keycloakService: KeycloakService
  ) {
    this.dossierId = data.dossierId;
    this.dossierCode = data.dossierCode;
  }

  ngOnInit(): void {
    if(this.showChangeDirectPayment?.enable){
      this.getDetailDossier();
      this.getUserAccount();
    }
  }

  getUserAccount() {
    this.keycloakService.loadUserProfile().then(user => {
      // tslint:disable-next-line: no-string-literal
      this.accountId = user['attributes'].user_id[0];
      this.userService.getUserInfo(this.accountId).subscribe(data => {
        this.userName = data.fullname;
      });
    });
  }

  async getDetailDossier() {
    this.dossierService.getDossierDetail(this.dossierId).subscribe(data => {
      this.dossierDetail = data;
      this.oldPaymentMethod = data.paymentMethod;
      this.newPaymentMethod = {
        id : "5f7fca83b80e603d5300dcf4",
        name : "Trực tiếp"
      };
      var statusId = this.showChangeDirectPayment?.statusId;
      if(statusId != null){
        if(statusId.split(",").includes(String(data.dossierStatus.id))){
          this.allowChange = true;
        }else{
          this.allowChange = false;
        }
      }
    });
  }

  postHistory() {
    const content = {
      groupId: 1,
      itemId: this.dossierId,
      user: {
        id: this.accountId,
        name: this.userName
      },
      type: 3,
      deploymentId: this.config.deploymentId,
      action: [
        {
          fieldNameRbk: 'lang.word.status',
          originalValue: this.oldPaymentMethod,
          newValue: this.newPaymentMethod
        }
      ]
    };
    this.dossierService.postHistory(JSON.stringify(content, null, 2)).subscribe(data => {
    });
  }

  async getDurationTwoDate(requestTime){
    return new Promise<void>(resolve => {
      this.dossierService.genDurationTimesheetV2(requestTime).toPromise().then(dt => {
        this.durationDate = dt[0].duration;
        resolve();
      }).catch(err => {
        resolve();
      });
    });
  }

  async getPostTimeSheet(requestTime, dueDate){
    return new Promise<void>(resolve => {
      this.dossierService.postTimesheet(requestTime).toPromise().then(dt => {
        var due = dt[0].due;
        var date = new Date(due);
        if(dueDate){
          this.dueDate = date.toISOString();
        }else{
          this.appointmentDate = date.toISOString();
        }
        resolve();
      }).catch(err => {
        resolve();
      });
    });
  }

  convertDateTimeToDate(dateTimeString: string): Date {
    // Parse the date string, assuming the format is yyyy-mm-ddThh:mi:ss+0700
    const parts = dateTimeString.split(/[:+.T-]/);
    const year = parseInt(parts[0], 10);
    const month = parseInt(parts[1], 10) - 1; // Month is 0-indexed
    const day = parseInt(parts[2], 10);
    const hours = parseInt(parts[3], 10)+7;
    const minutes = parseInt(parts[4], 10);
    const seconds = parseInt(parts[5], 10);

    // Create a new Date object considering the timezone offset (+0700)
    const date = new Date(year, month, day, hours, minutes, seconds);

    // Convert the date to ISO format
    return date;
  }

  async onConfirm() {
    if (this.commentContent.trim() === '') {
      const msgObj = {
        vi: 'Vui lòng nhập ý kiến xử lý!',
        en: 'Please enter a handling comments!'
      };
      this.snackbarService.openSnackBar(2, '', msgObj[this.selectedLang], 'warning_notification', this.config.expiredTime);
    }
    if (this.commentContent.trim() !== '') {
      if (this.dossierDetail?.oldData?.dossierStatus){
        if(this.dossierDetail.oldData.dossierStatus.id > 12 || this.dossierDetail.oldData.dossierStatus.id == 3 || this.dossierDetail.oldData.dossierStatus.id == 6 || this.dossierDetail.oldData.dossierStatus.id == 7){
          if(this.dossierDetail?.oldDataBefore?.dossierStatus){
            this.oldDossierStatus = this.dossierDetail.oldDataBefore.dossierStatus.id;
          }else{
            this.oldDossierStatus = this.dossierDetail.oldData.dossierStatus.id;
          }
        }else{
          this.oldDossierStatus = this.dossierDetail.oldData.dossierStatus.id;
        }
      }else{
        this.oldDossierStatus = 2
      }

      if (this.dossierDetail?.dossierStatus) {
        this.currentDossierStatus = this.dossierDetail.dossierStatus;
      }

      //cập nhật lại hạn xử lý + ngày hẹn trả quy trình và hồ sơ
      if(this.dossierDetail?.paymentRequestData){
        var requestDate = this.convertDateTimeToDate(this.dossierDetail.paymentRequestData.requestDate);
        const today = tUtils.newDate();
        today.setTime(today.getTime() + 7*60*60*1000); //đổi mã vùng
        const currentDate: string = today.toISOString().slice(0, 23) + '+0700';
        let requestTime = [
          {
            timesheet: {
              id: this.deploymentService.env?.OS_HCM?.timesheetIdPaymentDayLimit ? this.deploymentService.env.OS_HCM.timesheetIdPaymentDayLimit : "628866972afeb001f1e3c575"
            },
            dossier: {
              id: this.dossierId
            },
            startDate: requestDate.toISOString().slice(0, 23) + '+0700',
            endDate: currentDate,
            checkOffDay: true,
            overDue: false
          }
        ];
        await this.getDurationTwoDate(requestTime);
        let bodyRequest=[];
        if(this.dossierDetail?.appointmentDate){
          bodyRequest = [
            {
              timesheet: {
                id: this.deploymentService.env?.OS_HCM?.timesheetIdPaymentDayLimit ? this.deploymentService.env.OS_HCM.timesheetIdPaymentDayLimit : "628866972afeb001f1e3c575"
              },
              dossier: {
                id: this.dossierId
              },
              duration: this.durationDate,
              startDate: this.dossierDetail.appointmentDate.slice(0, 23) + '+0700',
              endDate: "",
              checkOffDay: true,
              processingTimeUnit: "d"
            }
          ];
          await this.getPostTimeSheet(bodyRequest, false);
        }
        var oldCurrentTask;
        var oldTaskIsCurrent;
        if(this.dossierDetail?.task){          
          oldTaskIsCurrent = this.dossierDetail.task;
          oldCurrentTask = this.dossierDetail.currentTask;
          //cập nhật hạn xử lý bước tại currentTask và task.isCurrent = 1
          for(var i = oldTaskIsCurrent.length-1; i > -1; i--){
            if(this.dossierDetail.task[i].isCurrent == 1){
              bodyRequest = [
                {
                  timesheet: {
                    id: this.deploymentService.env?.OS_HCM?.timesheetIdPaymentDayLimit ? this.deploymentService.env.OS_HCM.timesheetIdPaymentDayLimit : "628866972afeb001f1e3c575"
                  },
                  dossier: {
                    id: this.dossierId
                  },
                  duration: this.durationDate,
                  startDate: this.dossierDetail.currentTask[0].dueDate.slice(0, 23) + '+0700',
                  endDate: "",
                  checkOffDay: true,
                  processingTimeUnit: "d"
                }
              ];
              await this.getPostTimeSheet(bodyRequest, true);

              this.dossierDetail.task[i].dueDate = this.dueDate;
              this.dossierDetail.task[i].updatedDate = currentDate;
              this.dossierDetail.currentTask[0].dueDate = this.dueDate;
              this.dossierDetail.currentTask[0].updatedDate = currentDate;
              break;
            }
          }

          var old_dossierTaskStatus = this.dossierDetail.oldData.dossierTaskStatus;
          var old_dossierMenuTaskRemind = this.dossierDetail.oldData.dossierMenuTaskRemind;
          if(this.dossierDetail.oldData.dossierStatus.id > 12 || this.dossierDetail.oldData.dossierStatus.id == 3 || this.dossierDetail.oldData.dossierStatus.id == 6 || this.dossierDetail.oldData.dossierStatus.id == 7){
            if(this.dossierDetail?.oldDataBefore?.dossierTaskStatus){
              old_dossierTaskStatus = this.dossierDetail.oldDataBefore.dossierTaskStatus;
              old_dossierMenuTaskRemind = this.dossierDetail.oldDataBefore.dossierMenuTaskRemind;
            }
          }
        
          this.oldData = {
            dossierStatus: this.dossierDetail.dossierStatus,
            dossierTaskStatus: this.dossierDetail.dossierTaskStatus,
            dossierMenuTaskRemind: this.dossierDetail.dossierMenuTaskRemind,
            paymentMethod: this.oldPaymentMethod,
            dueDate: this.dossierDetail.dueDate,
            appointmentDate: this.dossierDetail.appointmentDate,
            currentTask: oldCurrentTask,
            task: oldTaskIsCurrent
          };
          this.newData = {
            dossierStatus: this.oldDossierStatus,
            dossierTaskStatus: old_dossierTaskStatus,
            dossierMenuTaskRemind: old_dossierMenuTaskRemind,
            paymentMethod: this.newPaymentMethod,
            dueDate: this.dueDate,
            appointmentDate: this.appointmentDate,
            currentTask: this.dossierDetail.currentTask,
            task: this.dossierDetail.task
          };
        }else{
          if(this.dossierDetail?.oldData){
            var old_dossierTaskStatus = this.dossierDetail.oldData.dossierTaskStatus;
            var old_dossierMenuTaskRemind = this.dossierDetail.oldData.dossierMenuTaskRemind;
            if(this.dossierDetail.oldData.dossierStatus.id > 12 || this.dossierDetail.oldData.dossierStatus.id == 3 || this.dossierDetail.oldData.dossierStatus.id == 6 || this.dossierDetail.oldData.dossierStatus.id == 7){
              if(this.dossierDetail?.oldDataBefore?.dossierTaskStatus){
                old_dossierTaskStatus = this.dossierDetail.oldDataBefore.dossierTaskStatus;
                old_dossierMenuTaskRemind = this.dossierDetail.oldDataBefore.dossierMenuTaskRemind;
              }
            }
          }
        }
      }else{
        if(this.dossierDetail?.oldData){
          var old_dossierTaskStatus = this.dossierDetail.oldData.dossierTaskStatus;
          var old_dossierMenuTaskRemind = this.dossierDetail.oldData.dossierMenuTaskRemind;
          if(this.dossierDetail.oldData.dossierStatus.id > 12 || this.dossierDetail.oldData.dossierStatus.id == 3 || this.dossierDetail.oldData.dossierStatus.id == 6 || this.dossierDetail.oldData.dossierStatus.id == 7){
            if(this.dossierDetail?.oldDataBefore?.dossierTaskStatus){
              old_dossierTaskStatus = this.dossierDetail.oldDataBefore.dossierTaskStatus;
              old_dossierMenuTaskRemind = this.dossierDetail.oldDataBefore.dossierMenuTaskRemind;
            }
          }
        }
      }
      const description = 'Yêu cầu hồ sơ '+this.dossierCode+' chuyển <b>Hình thức thanh toán trực tuyến</b> sang <b>hình thức thanh toán trực tiếp</b>';
      const requestBodyObj: any = {
        dossierStatus: this.oldDossierStatus,
        comment: this.commentContent.trim(),
        description: description,
        dossierTaskStatus: old_dossierTaskStatus,
        dossierMenuTaskRemind: old_dossierMenuTaskRemind,
        currentTask: this.dossierDetail.currentTask,
        task: this.dossierDetail.task,
        paymentMethod: this.newPaymentMethod,
        appointmentDate: this.appointmentDate,
        dueDate: this.dueDate,
        oldData: {
          dossierStatus: this.dossierDetail.dossierStatus,
          dossierTaskStatus: this.dossierDetail.dossierTaskStatus,
          dossierMenuTaskRemind: this.dossierDetail.dossierMenuTaskRemind,
          paymentMethod: this.oldPaymentMethod,
          dueDate: this.dossierDetail.dueDate,
          appointmentDate: this.dossierDetail.appointmentDate,
          task: oldTaskIsCurrent,
          currentTask: oldCurrentTask
        }
      };

      if (this.commentContent.trim() !== '') {
        this.postComment(this.commentContent.trim(), description);
        requestBodyObj.comment = this.commentContent.trim();
        requestBodyObj.description = description;
      } else {
        const msgObj = {
          vi: description,
          en: 'Request for cast payment of dossier <b>' + this.dossierCode + '</b>!'
        };
        this.postComment(msgObj[this.selectedLang]);
        requestBodyObj.comment = msgObj[this.selectedLang];
      }

      const requestBody = JSON.stringify(requestBodyObj, null, 2);
      this.dossierService.putDossierStatusWithComment(this.dossierId, requestBody).subscribe(data => {
        if (data.affectedRows === 1) {
          this.postHistory();
          this.dialogRef.close(true);
        } else {
          this.dialogRef.close(false);
        }
      }, err => {
        this.dialogRef.close(false);
      });
    }
  }

  onDismiss() {
    this.dialogRef.close();
  }

  postComment(commentContent, description?:string) {
    let msgObj = {};
    if(!!description){
      msgObj = {
        vi: `Yêu cầu thanh toán: ${commentContent}<br>Nội dung: ${description}`,
        en: `Payment request: ${commentContent}<br>Description: ${description}`
      };
    }else{
      msgObj = {
        vi: `Yêu cầu yêu cầu thanh toán: ${commentContent}`,
        en: `Payment request: ${commentContent}`
      };
    }
    const content = {
      groupId: 2,
      itemId: this.dossierId,
      user: {
        id: this.accountId,
        fullname: this.userName
      },
      content: msgObj[this.selectedLang]
    };
    const requestBody = JSON.stringify(content, null, 2);
    this.dossierService.postComment(requestBody).subscribe(data => { });
  }

  onCommentEditorChange(event) {
    this.commentContent = event.editor.getData();
    if (event.editor.getData().trim().length > 500) {
      this.isCKMaxlenght = true;
    } else {
      this.isCKMaxlenght = false;
    }
  }
}

export class ChangeDirectPaymentDialogModel {
  constructor(public dossierId: string, public dossierCode: string) {
  }
}
