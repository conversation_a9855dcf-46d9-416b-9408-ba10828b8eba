import { Component, OnInit, Inject } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';

@Component({
  selector: 'app-confirm-cancel-schedule-cto',
  templateUrl: './confirm-cancel-schedule-cto.component.html',
  styleUrls: ['./confirm-cancel-schedule-cto.component.scss']
})
export class ConfirmCancelScheduleComponent implements OnInit {

  constructor(
    public dialogRef: MatDialogRef<ConfirmCancelScheduleComponent>,
    @Inject(MAT_DIALOG_DATA) public data: ConfirmCancelDialogModel,
  ) { }

  ngOnInit(): void {
  }

  onConfirm() {
    this.dialogRef.close(true);
  }

  onDismiss() {
    this.dialogRef.close(false);
  }
}

export class ConfirmCancelDialogModel {
  constructor() {
  }
}
