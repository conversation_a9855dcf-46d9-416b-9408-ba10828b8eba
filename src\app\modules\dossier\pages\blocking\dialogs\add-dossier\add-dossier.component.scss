.dialog_title {
    font-style: normal;
    font-weight: 500;
    font-size: 20px;
    color: #CE7A58;
}

::ng-deep .addForm .addBtn {
    margin-top: 1em;
    background-color: #CE7A58;
    color: #fff;
    height: 3em;
}

.close-button {
    float: right;
    top: -24px;
    right: -24px;
}
.prc_Editor {
    .ck {
        &.ck-toolbar {
            border-radius: 4px 4px 0 0;
            border: 1px solid #dedede;
        }

        &.ck-content {
            background-color: #eaebeb;
            border: none;
            border-radius: 0 0 4px 4px;
        }
    }

    .ck-editor__editable {
        min-height: 5em !important;
    }
}

.errorMsg {
    font-size: 12px !important;
    float: right;
    display: flex;
    color: #ce7a58;
    margin: 0.5em 0;
    width: 100%;

    span {
        margin-left: auto !important;
    }

    .err {
        background-color: #f2a63494;
        border-radius: 50%;
        width: 1.2em;
        height: 1.2em;
        justify-content: center;
        display: flex;
        margin-left: 0.5em;
        margin-top: 0.2em;

        .mat-icon {
            color: #ce7a58;
            vertical-align: middle;
            align-self: center;
            transform: scale(0.4);
            margin-left: 0.05em;
        }
    }
}
.file {
    background-color: #8c8c8c2d;
    padding-left: 0.5em;
    border-radius: 4px;
    display: flex;
    margin: 0.25em 0;
    .icon {
        align-self: center;
        width: 2em;
        height: 2em;
        background-position: center;
        background-repeat: no-repeat;
        background-size: 100%;
        margin-right: 0.5em;
    }
    .name {
        align-self: center;
        color: #1e2f41;
        width: 80%;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
    .btn-remove{
        width: 15%;
        text-align: right;
        padding: 0px 5px;
    }
    .viewFile {
        align-self: center;
        .mat-icon {
            margin: 0;
        }
    }
    .deleteFile {
        align-self: center;
    }
}
.uploadBtn {
    overflow: hidden;
    display: inline-block;
    position: relative;
    .btn_upload{
        // background-color: #f9f9f9 !important;
    }

    .mat-button {
        background-color: #8c8c8c2d;
        padding: 0.2em 1em;
        border-radius: 4px;
        margin: 0.25em 0;
        display: flex;
        color: #1e2f41;
        width: 100%;
        justify-content: center;

        .mat-icon {
            color: #ce7a58;
            transform: rotate(45deg);
        }
    }

    input[type="file"] {
        position: absolute;
        left: 0;
        top: 0;
        opacity: 0;
        width: 100%;
        height: 100%;
        cursor: pointer;
    }
}
@media screen and (max-width: 600px) {
    .bn-add{
        float: left;
        margin-top: auto;
        margin-bottom: auto;
        align-items: center;
        color: #CE7A58;
    }
}
@media screen and (max-width: 960px) {
    .padding-itemsectorname{
        padding: 5px;
        border-radius: 5px;
    }
}