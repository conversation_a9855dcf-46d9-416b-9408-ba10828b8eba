import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { DlkBaocaoChithi25CapDonViComponent } from './dlk-baocao-chithi25-capdonvi.component';
import { SharedModule } from 'src/app/shared/shared.module';
import { NgxPrinterModule } from 'ngx-printer';
import {DossierDetailComponent} from '../dialogs/view-detail.component';
import { DlkBaocaoChithi25CapDonViRoutingModule } from './dlk-baocao-chithi25-capdonvi-routing.module';


@NgModule({
  declarations: [DlkBaocaoChithi25CapDonViComponent],
  imports: [
    CommonModule,
    DlkBaocaoChithi25CapDonViRoutingModule,    
    SharedModule,
    
    
    NgxPrinterModule.forRoot({ printOpenWindow: true })
  ]
})
export class DlkBaocaoChithi25CapDonViModule { }
