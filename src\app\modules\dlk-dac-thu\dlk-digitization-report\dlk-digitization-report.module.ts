import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { DLKDigitizationReportRoutingModule } from './dlk-digitization-report-routing.module';
import { DLKDigitizationReportComponent } from './dlk-digitization-report.component';
import { SharedModule } from 'src/app/shared/shared.module';
import { DlkDigitizationReportDetailComponent } from './dlk-digitization-report-detail/dlk-digitization-report-detail.component';


@NgModule({
  declarations: [DLKDigitizationReportComponent, DlkDigitizationReportDetailComponent],
  imports: [
    CommonModule,
    DLKDigitizationReportRoutingModule,
    SharedModule
  ]
})
export class DLKDigitizationReportModule { }
