import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { ApiProviderService } from 'src/app/core/service/api-provider.service';

@Injectable({
    providedIn: 'root'
})
export class ReceiptBookService {

    constructor(
        private http: HttpClient,
        private apiProviderService: ApiProviderService
    ) { }
    private receiptBookUrl = this.apiProviderService.getUrl('digo', 'basepad') + '/receipt-book';
    search(query): Observable<any> {
        const endpoint = this.receiptBookUrl + '/--search' + query;
        return this.http.get(endpoint);
    }

    post(body: any): Observable<any>{
        let headers = new HttpHeaders();
        headers = headers.set('Content-Type', 'application/json');
        return this.http.post<any>(this.receiptBookUrl, body, { headers });
    }

    update(id: string, body: any): Observable<any>{
        let headers = new HttpHeaders();
        headers = headers.set('Content-Type', 'application/json');
        return this.http.put<any>(this.receiptBookUrl + `/${id}`, body, { headers });
    }

    updateStatus(id: string): Observable<any>{
        let headers = new HttpHeaders();
        headers = headers.set('Content-Type', 'application/json');
        return this.http.put<any>(this.receiptBookUrl + `/${id}/--status`, '', { headers });
    }

    details(id: string): Observable<any>{
        return this.http.get(this.receiptBookUrl + `/${id}`);
    }

    cloneReceiptBook(id: string, body: any): Observable<any> {
        let headers = new HttpHeaders();
        headers = headers.set('Content-Type', 'application/json');
        return this.http.put<any>(this.receiptBookUrl + `/${id}/--clone-receipt-book`, body, { headers });
    }

    delete(id: string) {
        let headers = new HttpHeaders();
        headers = headers.append('Content-Type', 'application/json');
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        return this.http.delete(this.receiptBookUrl + `/${id}`, { headers });
    }

    updateReceiptBookAgency(body: any): Observable<any>{
        let headers = new HttpHeaders();
        headers = headers.set('Content-Type', 'application/json');
        return this.http.put<any>(this.receiptBookUrl + '/--agency-receipt-book', body, { headers });
    }

    updateReceiptBookSector(body: any): Observable<any>{
        let headers = new HttpHeaders();
        headers = headers.set('Content-Type', 'application/json');
        return this.http.put<any>(this.receiptBookUrl + '/--sector-receipt-book', body, { headers });
    }    
}