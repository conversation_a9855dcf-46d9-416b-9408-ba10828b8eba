import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { PerformChargeFeesRoutingModule } from './perform-charge-fees-routing.module';
import { SharedModule } from 'src/app/shared/shared.module';
import { FormioModule } from 'angular-formio';

import { PerformChargeFeesComponent } from './perform-charge-fees.component';
import { ApplyChangeFeesComponent } from './dialogs/apply-change-fees/apply-change-fees.component';

@NgModule({
  declarations: [
    PerformChargeFeesComponent,
    ApplyChangeFeesComponent
  ],
  imports: [
    CommonModule,
    PerformChargeFeesRoutingModule,
    SharedModule,
    FormioModule
  ]
})
export class PerformChargeFeesModule { }
