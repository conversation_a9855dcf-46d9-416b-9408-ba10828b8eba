import { Component, Inject, OnInit } from '@angular/core';
import { MatDialog, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { MatTableDataSource } from '@angular/material/table';
import { Router } from '@angular/router';
import { KeycloakService } from 'keycloak-angular';
import { Subscription } from 'rxjs';
import { EnvService } from 'src/app/core/service/env.service';
import { FormElement } from 'src/app/data/schema/form-element';
import { DeploymentService } from 'src/app/data/service/deployment.service';
import { DossierService } from 'src/app/data/service/dossier/dossier.service';
import { SnackbarService } from 'src/app/data/service/snackbar/snackbar.service';
import { UserService } from 'src/app/data/service/user.service';
import { ConfirmSelectTaskDialogModel, SelectTaskComponent } from 'src/app/modules/dossier/pages/processing/dialogs/select-task/select-task.component';
import { RemindType } from '../../enums/RemindType';
import { QbhRemindWorkService } from 'src/app/data/service/qbh-remind-work/qbh-remind-work.service';
import * as fs from 'file-saver';
import moment from 'moment';
import { Workbook } from 'exceljs';
import {ProcessHandleComponent, ProcessHandleDialogModel} from 'shared/components/process-handle/process-handle.component';
import { QBHStatisticService } from 'src/app/data/service/qbh-statistics/qbh-statistic.service';
import { KhaRemindWorkService } from 'src/app/data/service/kha-remind-work/kha-remind-work.service';

@Component({
  selector: 'app-list-dossier-remind',
  templateUrl: './list-dossier-remind.component.html',
  styleUrls: ['./list-dossier-remind.component.scss']
})
export class ListDossierRemindComponent implements OnInit {

  constructor(
    public dialogRef: MatDialogRef<ListDossierRemindComponent>,
    @Inject(MAT_DIALOG_DATA) public data: ConfirmDossierRemindDialogModel,
    private deploymentService: DeploymentService,
    private keycloakService: KeycloakService,
    private userService: UserService,
    private envService: EnvService,
    private khaRemindWorkService: KhaRemindWorkService,
    private router: Router,
    private dialog: MatDialog,
    private qbhStatisticsService: QBHStatisticService,
    private dossierService: DossierService,
    private snackbarService: SnackbarService,

  ) {
    this.type =data.type;
    this.dataSource = new MatTableDataSource(this.ELEMENTDATA);
   }
  lblTitle: string = "quá hạn xử lý";
  type :number =0;
  userId:any;
  userInfo: any;
  agencyId: any;
  config = this.envService.getConfig();
  size = 10;
  pageIndex = 1;
  page = 1;
  countResult = 0;
  pgSizeOptions = this.config.pageSizeOptions;
  selectedLang: string;
  dataFirst: any = [];
  listForm = [];
  timeDueSoon:number =1;
  useDueDateRemindWork : boolean =false;
  isDue: number = 0;
  ELEMENTDATA: any[] = [];
  dataSource: MatTableDataSource<any>;
  env = this.deploymentService.getAppDeployment()?.env;
  displayedColumns: string[] = ['stt', 'codee', 'procedure', 'sector', 'required', 'assignedDate', 'appointmentDate', 'completedDate', 'owner', 'user', 'status' ];
  searchDomain:String ="";
  listUserAgency: any;
  userExperience: any;
  accepterInfo = {
    id: '',
    username: '',
    fullname: '',
    accountId: ''
  };
  subscription: Subscription;
  approvalAgencyList = [];
  agencyInfo = [];
  userAgency = JSON.parse(localStorage.getItem('userAgency'));
  agencyTagName:string=  "Cấp Tỉnh,Cấp Sở,Cấp Huyện,Cấp Xã,Cấp Xã/Phường/Thị trấn";
  rootAgency = null;
  async ngOnInit(): Promise<void> {

    this.selectedLang = localStorage.getItem('language');
    await this.getUserAccount();
    this.agencyId = await this.getUserAgencyId();
    await this.getRemindTitle();
    console.log('page', this.page);
  }
  getUserAccount() {
    this.keycloakService.loadUserProfile().then(user => {
      // tslint:disable-next-line: no-string-literal
      this.userId = user['attributes'].user_id[0];
      console.log('userId',this.userId )
      this.userService.getUserInfo(this.userId).subscribe(async data => {
        this.userInfo = data;
        console.log('userInfo',this.userInfo )
      }, error => {
        console.log(error);
      });
    });
  }
  getUserAgencyId(): string {
    const userAgency = JSON.parse(localStorage.getItem('userAgency'));
    if (userAgency !== null) {
      return userAgency?.id;
    } else {
      return this.config?.rootAgency?.id;
    }
  }
  onDismiss(): void {
    this.dialogRef.close();
  }
  paginate(event: any, type) {
    switch (type) {
      case 0:
        this.pageIndex = event;
        this.page = event;
        this.getListDossier(this.type,'?page=' + (this.pageIndex - 1) + '&size=' + this.size + '&spec=page');
        break;
      case 1:
        this.pageIndex = 1;
        this.page = 1;
        this.getListDossier(this.type,'?page=' + (this.pageIndex - 1) + '&size=' + this.size + '&spec=page');
        break;
    }
  }
  async getListDossier(type: number, searchString: string){
   
    this.rootAgency = await this.qbhStatisticsService.findRootAgency(this.agencyId,this.agencyTagName);
    // if (this.rootAgency?.id != null) {
    //   if(this.rootAgency?.id === '655dc9dececceb05ee44499f'){
    //     this.rootAgency.id = '60b87fb59adb921904a0213e'; //UBND tỉnh Quảng Tri có 2 agency.id
    //   }
    //   searchString += `&agcencyId=${this.rootAgency.id}`;
    // }

    if (this.userId != null && this.userId != '') {
      searchString += `&userId=${this.userId}`
    }

    searchString += '&sort=appointmentDate,asc';
    switch (this.type){
      case   RemindType.DATIEPNHANTRUCTUYEN:
        this.getListTiepNhanTrucTuyen(searchString);
        break;
      case   RemindType.DATIEPNHANTRUCTIEP:
        this.getListTiepNhanTrucTiep(searchString);
        break;
      case   RemindType.DATIEPNHANLIENTHONG:
        this.getListTiepNhanLienThong(searchString);
        break;
      case   RemindType.DAGIAIQUYETDUNGHAN:
        this.getListGiaiQuyetDungHan(searchString);
        break;
      case   RemindType.DAGIAIQUYETQUAHAN:
        this.getListGiaiQuyetQuaHan(searchString);
        break;
      case   RemindType.DANGGIAIQUYETCONHAN:
        this.getListDangGiaiQuyetConHan(searchString)
        break;
      case RemindType.DANGGIAIQUYETDENHAN:
        this.getListDangGiaiQuyetGanHan(searchString);
        break;
      case RemindType.DANGGIAIQUYETTREHAN:
        this.getListDangGiaiQuyetQuaHan(searchString);
        break;
     }
  }

  getListTiepNhanTrucTuyen(searchString: string){
    this.khaRemindWorkService.getListAcceptTrucTuyen(searchString).subscribe(data => {
      this.ELEMENTDATA = [];
      this.countResult = data.totalElements;
      for (let i = 0; i < data.numberOfElements; i++) {
        data.content[i].stt = this.size * (this.pageIndex - 1) + (i + 1);
        this.ELEMENTDATA.push(data.content[i]);
      }
      this.dataSource.data = this.ELEMENTDATA;
    });
  }

  getListTiepNhanTrucTiep(searchString: string){
    this.khaRemindWorkService.getListAcceptTrucTiep(searchString).subscribe(data => {
      this.ELEMENTDATA = [];
      this.countResult = data.totalElements;
      for (let i = 0; i < data.numberOfElements; i++) {
        data.content[i].stt = this.size * (this.pageIndex - 1) + (i + 1);
        this.ELEMENTDATA.push(data.content[i]);
      }
      this.dataSource.data = this.ELEMENTDATA;
    });
  }

  getListGiaiQuyetDungHan(searchString: string){
    this.khaRemindWorkService.getListResoleDue(searchString).subscribe(data => {
      this.ELEMENTDATA = [];
      this.countResult = data.totalElements;
      for (let i = 0; i < data.numberOfElements; i++) {
        data.content[i].stt = this.size * (this.pageIndex - 1) + (i + 1);
        this.ELEMENTDATA.push(data.content[i]);
      }
      this.dataSource.data = this.ELEMENTDATA;
    });
  }

  getListGiaiQuyetQuaHan(searchString: string){
    this.khaRemindWorkService.getListResoleOverDue(searchString).subscribe(data => {
      this.ELEMENTDATA = [];
      this.countResult = data.totalElements;
      for (let i = 0; i < data.numberOfElements; i++) {
        data.content[i].stt = this.size * (this.pageIndex - 1) + (i + 1);
        this.ELEMENTDATA.push(data.content[i]);
      }
      this.dataSource.data = this.ELEMENTDATA;
    });
  }

  getListDangGiaiQuyetConHan(searchString: string){
    this.khaRemindWorkService.getListUnresolveOnTime(searchString).subscribe(data => {
      this.ELEMENTDATA = [];
      this.countResult = data.totalElements;
      for (let i = 0; i < data.numberOfElements; i++) {
        data.content[i].stt = this.size * (this.pageIndex - 1) + (i + 1);
        this.ELEMENTDATA.push(data.content[i]);
      }
      this.dataSource.data = this.ELEMENTDATA;
    });
  }

  getListDangGiaiQuyetQuaHan(searchString: string){
    this.khaRemindWorkService.getListUnresolveOverDue(searchString).subscribe(data => {
      this.ELEMENTDATA = [];
      this.countResult = data.totalElements;
      for (let i = 0; i < data.numberOfElements; i++) {
        data.content[i].stt = this.size * (this.pageIndex - 1) + (i + 1);
        this.ELEMENTDATA.push(data.content[i]);
      }
      this.dataSource.data = this.ELEMENTDATA;
    });
  }

  getListDangGiaiQuyetGanHan(searchString: string){
    this.khaRemindWorkService.getListUnresolveIsDue(searchString).subscribe(data => {
      this.ELEMENTDATA = [];
      this.countResult = data.totalElements;
      for (let i = 0; i < data.numberOfElements; i++) {
        data.content[i].stt = this.size * (this.pageIndex - 1) + (i + 1);
        this.ELEMENTDATA.push(data.content[i]);
      }
      this.dataSource.data = this.ELEMENTDATA;
    });
  }

  getListTiepNhanLienThong(searchString: string){
    this.khaRemindWorkService.getListLienThong(searchString).subscribe(data => {
      this.ELEMENTDATA = [];
      this.countResult = data.totalElements;
      for (let i = 0; i < data.numberOfElements; i++) {
        data.content[i].stt = this.size * (this.pageIndex - 1) + (i + 1);
        this.ELEMENTDATA.push(data.content[i]);
      }
      this.dataSource.data = this.ELEMENTDATA;
    });
  }
  getRemindTitle(){
    let searchString ='?page=' + (this.pageIndex - 1) + '&size=' + this.size + '&spec=page';
        if(this.type == 0)
         this.lblTitle = "đã tiếp nhận trực tuyến";
        else if(this.type == 1)
         this.lblTitle = "đã tiếp nhận trực tiếp";
        else if(this.type == 2)
         this.lblTitle = "đã tiếp nhận liên thông";
        else if(this.type == 3)
         this.lblTitle = "đã giải quyết đúng hạn";
        else if(this.type == 4){
         this.lblTitle = "đã giải quyết quá hạn";
        }
        else if(this.type == 5){
          this.lblTitle = "đang giải quyết còn hạn";
        }
        else if(this.type == 6) {
          this.lblTitle = "đang giải quyết đến hạn"
        }
        else if(this.type == 7) {
          this.lblTitle = "đang giải quyết trễ hạn"
        }
        this.getListDossier(this.type,searchString);
  }

  getConfig(){
    const config = this.deploymentService.getAppDeployment();
    if (!!config) {
        if (config.domain && config.domain.length > 0){
          // tslint:disable-next-line:max-line-length
          const domain = config.domain.filter(listdomain => ((listdomain.domain.toLowerCase().indexOf(window.location.host) > -1) || (window.location.host.toLowerCase().indexOf(listdomain.domain) > -1)));
          if (domain && domain.length > 0 && domain[0].rootAgency){
            this.searchDomain = '&domain-agency-id=' + domain[0].rootAgency.id;
          }
        }
    }
  }


  exportToExcel() {
      const userId = JSON.parse(localStorage.getItem('userAgency'));
      // create workbook and worksheet
      const workbook = new Workbook();
      workbook.creator = 'Snippet Coder';
      workbook.lastModifiedBy = 'SnippetCoder';
      workbook.created = new Date();
      workbook.modified = new Date();
      const worksheet = workbook.addWorksheet();
      const fontHeader = {name: 'Times New Roman'};
      worksheet.getColumn('A').font = fontHeader;
      worksheet.getColumn('B').font = fontHeader;
      worksheet.getColumn('C').font = fontHeader;
      worksheet.getColumn('D').font = fontHeader;
      worksheet.getColumn('E').font = fontHeader;
      worksheet.getColumn('F').font = fontHeader;
      worksheet.getColumn('G').font = fontHeader;
      worksheet.getColumn('H').font = fontHeader;
      worksheet.getColumn('I').font = fontHeader;
      worksheet.getColumn('J').font = fontHeader;
      worksheet.getColumn('K').font = fontHeader;
      worksheet.getColumn('L').font = fontHeader;
      worksheet.getColumn('M').font = fontHeader;
      worksheet.getColumn('N').font = fontHeader;
      worksheet.getColumn('O').font = fontHeader;
      worksheet.getColumn('P').font = fontHeader;
      worksheet.getColumn('Q').font = fontHeader;
      worksheet.getColumn('R').font = fontHeader;
      worksheet.getColumn('S').font = fontHeader;
      worksheet.getColumn('T').font = fontHeader;
      worksheet.getColumn('U').font = fontHeader;

      // Add header row
      const today = new Date();
      worksheet.addRow([]);
      worksheet.mergeCells('A1:C1');
      worksheet.mergeCells('A2:C2');
      worksheet.getCell('A1').value = 'Tỉnh Khánh Hòa';
      worksheet.getCell('A2').value = `Đơn vị: ${userId?.name}`;

      worksheet.getCell('A1').alignment = {horizontal: 'center', vertical: 'middle'};
      worksheet.getCell('A1').font = { ...worksheet.getCell('A1').font,...{bold: true, size: 14}}
      worksheet.getCell('A2').alignment = {horizontal: 'center', vertical: 'middle'};
      worksheet.getCell('A2').font = { ...worksheet.getCell('A1').font,...{bold: true, size: 14}}

      // add report name

      worksheet.mergeCells('A4:E4');
      worksheet.mergeCells('A5:E5');

      worksheet.getCell('A5').value = `Ngày: ${moment().format("DD/MM/YYYY")} `;
      worksheet.getCell('A5').alignment = {horizontal: 'center', vertical: 'middle'};

      worksheet.getCell('A4').value = `THỐNG KÊ DANH SÁCH HỒ SƠ ${this.lblTitle.toUpperCase()}`;
      worksheet.getCell('A4').alignment = {horizontal: 'center', vertical: 'middle'};

      worksheet.getCell('A5').font = { ...worksheet.getCell('D3').font,...{ size: 14}}
      worksheet.getCell('A4').font = { ...worksheet.getCell('D4').font,...{ size: 14}}

      worksheet.getCell('E1').value = `CỘNG HÒA XÃ HỘI CHỦ NGHĨA VIỆT NAM`;
      worksheet.getCell('E1').alignment = {horizontal: 'center', vertical: 'middle'};

      worksheet.getCell('E2').value = `Độc lập - Tự do - Hạnh phúc`;
      worksheet.getCell('E2').alignment = {horizontal: 'center', vertical: 'middle'};

      worksheet.getCell('A7').value = "STT";
      worksheet.getCell('B7').value = "Mã hồ sơ";
      worksheet.getCell('C7').value = "Thủ tục hành chính";
      worksheet.getCell('D7').value = "Tên lĩnh vực";
      worksheet.getCell('E7').value = "Yêu cầu giải quyết";
      worksheet.getCell('F7').value = "Ngày tiếp nhận";
      worksheet.getCell('G7').value = "Ngày hẹn trả";
      worksheet.getCell('H7').value = "Ngày kết thúc";
      worksheet.getCell('I7').value = "Chủ hồ sơ";
      worksheet.getCell('J7').value = "Cán bộ tiếp nhận";
      worksheet.getCell('K7').value = "Trạng thái";


      worksheet.getColumn('A').width = 10;
      worksheet.getColumn('B').width = 30;
      worksheet.getColumn('C').width = 40;
      worksheet.getColumn('D').width = 30;
      worksheet.getColumn('E').width = 40;
      worksheet.getColumn('F').width = 20;
      worksheet.getColumn('G').width = 20;
      worksheet.getColumn('H').width = 20;
      worksheet.getColumn('I').width = 25;
      worksheet.getColumn('J').width = 25;
      worksheet.getColumn('K').width = 40;


      const mileRow = 8;
      const cellNumber = 11;

      worksheet.getRow(7).alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
      worksheet.getRow(7).font = {name: 'Times New Roman', size: 12, bold: true};

      for (let i = 1 ; i <= cellNumber ; i++) {
        const cell = worksheet.findCell(7, i );
        if (cell) {
          cell.border  =  { top: { style: 'thin' }, left: { style: 'thin' }, bottom : { style: 'thin' }, right: { style: 'thin' } };
          cell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: {argb: 'FFFFFFFF'},
          };
        }
        else {
          console.log('không tìm thấy ', cell);
        }
      }

      // đổ data
      let indexArr = 0;

      let sttCount = 1;
      for(let i = mileRow ; i <=  mileRow + this.ELEMENTDATA.length-1 ; i++) {
        worksheet.getRow(i).height = 30;
        if (this.ELEMENTDATA[indexArr]?.initial != null) {
          const cell = worksheet.getCell(i, 1);
          cell.alignment = {horizontal: 'left', vertical: 'middle'};
          cell.font = { bold: true };
          cell.value = this.ELEMENTDATA[indexArr]?.initial ;
        }
        else
        {
          for (let j = 1; j <= cellNumber; j++) {
            const cell = worksheet.getCell(i, j);
            cell.font = {name: 'Arial', size: 10,};
            cell.alignment = {horizontal: 'left', vertical: 'middle'};
            cell.border = {top: {style: 'thin'}, left: {style: 'thin'}, bottom: {style: 'thin'}, right: {style: 'thin'}};
            let value = null;
            switch (j) {
              case 1:
                cell.value = sttCount; // index
                break;
              case 2:
                cell.alignment = {horizontal: 'center', vertical: 'middle'};
                cell.value = this.ELEMENTDATA[indexArr]?.code ;
                break;
              case 3:
                cell.alignment = {horizontal: 'center', vertical: 'middle'};
                let procedureName = '';
                if (this.ELEMENTDATA[indexArr].procedure?.translate?.name != null && this.ELEMENTDATA[indexArr].procedure) {
                  procedureName = this.ELEMENTDATA[indexArr].procedure?.translate?.name;
                }
                if (this.ELEMENTDATA[indexArr].procedure?.translate.length > 0) {
                  procedureName = this.ELEMENTDATA[indexArr].procedure?.translate[0]?.name;
                }
                cell.value = procedureName;
                break;
              case 4:
                cell.alignment = {horizontal: 'center', vertical: 'middle'};
                cell.value = this.ELEMENTDATA[indexArr]?.procedure?.sector?.name[0]?.name ?? "";
                break;
              case 5:
                cell.alignment = {horizontal: 'center', vertical: 'middle'};
                cell.value = this.ELEMENTDATA[indexArr]?.applicant?.data?.noidungyeucaugiaiquyet  ?? "";
                break;
              case 6:
                cell.alignment = {horizontal: 'center', vertical: 'middle'};
                cell.value = this.ELEMENTDATA[indexArr]?.acceptedDate ? moment(this.ELEMENTDATA[indexArr]?.acceptedDate).format("DD/MM/YYYY") : "";
                break;
              case 7:
                cell.alignment = {horizontal: 'center', vertical: 'middle'};
                cell.value = this.ELEMENTDATA[indexArr]?.appointmentDate ? moment(this.ELEMENTDATA[indexArr]?.appointmentDate).format("DD/MM/YYYY") : "";
                break;
              case 8:
                cell.alignment = {horizontal: 'center', vertical: 'middle'};
                cell.value = this.ELEMENTDATA[indexArr]?.completedDate ? moment(this.ELEMENTDATA[indexArr]?.completedDate).format("DD/MM/YYYY") : "";
                break;
              case 9:
                cell.alignment = {horizontal: 'center', vertical: 'middle'};
                cell.value = this.ELEMENTDATA[indexArr]?.applicant?.data?.ownerFullname  ?? "";
                break;
              case 10:
                cell.alignment = {horizontal: 'center', vertical: 'middle'};
                let canBoXuLy = '';
                if (this.ELEMENTDATA[indexArr]?.canBoXuLy) {
                  canBoXuLy = this.ELEMENTDATA[indexArr].canBoXuLy[0]?.fullname;
                } else if (this.ELEMENTDATA[indexArr]?.task && this.ELEMENTDATA[indexArr].task.length > 0 && this.ELEMENTDATA[indexArr].task[0]?.assignee) {
                  canBoXuLy = this.ELEMENTDATA[indexArr]?.task[0]?.assignee?.fullname;
                }
                cell.value = canBoXuLy;
                break;
              case 11:
                cell.alignment = {horizontal: 'center', vertical: 'middle'};
                const dossierStatus = this.ELEMENTDATA[indexArr]?.dossierStatus?.name[0]?.name ? this.ELEMENTDATA[indexArr]?.dossierStatus?.name[0].name  : this.ELEMENTDATA[indexArr]?.dossierStatus?.name
                cell.value = dossierStatus  ?? "";
                break;
            }
          }
          sttCount += 1;
        }
        indexArr++;
      }

      const EXCEL_TYPE = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8';
      // Save Excel File
      workbook.xlsx.writeBuffer().then((dataBuffer: ArrayBuffer) => {
        const blob = new Blob([dataBuffer], {type: EXCEL_TYPE});
        fs.saveAs(blob, `KHA_thong_ke_ho_so.xlsx`);
      });
    }

    viewProcess(dossierId, dossierCode) {
      const dialogData = new ProcessHandleDialogModel(dossierId, dossierCode);
      const dialogRef = this.dialog.open(ProcessHandleComponent, {
        minWidth: '70vw',
        data: dialogData,
        disableClose: true,
        autoFocus: false
      });
      dialogRef.afterClosed().subscribe(dialogResult => {
      });
    }

    openDetail(dossierId, procedureId, task, assigneeTask) {
      if (this.type === 5 || this.type === 6 || this.type === 7) {
        this.processingDossier(dossierId, procedureId, assigneeTask)
      } else {
        this.dossierDetail(dossierId, procedureId, task)
      }
    }

    dossierDetail(dossierId, procedureId, task) {
      const queryParamsObject = {
        procedure: procedureId,
      };

      if (task !== undefined) {
        Object.assign(queryParamsObject, { task: task[task.length - 1].id });
      }

        const url = this.router.serializeUrl(
          this.router.createUrlTree(['dossier/search/' + dossierId], {
            queryParams: queryParamsObject
          })
        );

        window.open(url, '_blank');
        return;
    }

    processingDossier(dossierId, procedureId, assigneeTask) {
      this.dossierService.getDossierDetail(dossierId).subscribe(data => {
        if (data.task !== undefined) {  
          if (data.task.filter(t => t.isCurrent === 1)[0] !== undefined && data.task.filter(t => t.isCurrent === 1)[0] !== null) {
          let url = '';
          url = this.router.serializeUrl(
            this.router.createUrlTree(['dossier/processing/' + dossierId], {
              queryParams: {
                procedure: procedureId,
                task: !!assigneeTask[0]?.id?assigneeTask[0].id:data.task.filter(t => t.isCurrent === 1)[0].id
              }
            }));
  
            window.open(url, '_blank');
                return;
  
        } else {
          const msgObj = {
            vi: 'Không tìm thấy công việc nào cho hồ sơ này!',
            en: 'No task found for this dossier!'
          };
          this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
        }
      }
    });
  }
}

export class ConfirmDossierRemindDialogModel {
  constructor(public type) {

  }
}
