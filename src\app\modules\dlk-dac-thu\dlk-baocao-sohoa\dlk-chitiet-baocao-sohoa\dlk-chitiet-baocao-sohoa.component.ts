import { ChangeDetectorRef, Component, Inject, OnInit } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { MatTableDataSource } from '@angular/material/table';
import { EnvService } from 'src/app/core/service/env.service';
import { FormElement } from 'src/app/data/schema/form-element';
import { SnackbarService } from 'src/app/data/service/snackbar/snackbar.service';
import { PadmanService } from 'src/app/data/service/svc-padman/padman.service';
import { DLKStatisticService } from 'src/app/data/service/dlk-dac-thu/dlk-statistic.service';
import { DeploymentService } from 'src/app/data/service/deployment.service';
import * as tUtils from 'src/app/data/service/thoai.service';
import { ExportExcelService } from 'src/app/data/service/export-excel/export-excel.service';
import { DatePipe } from '@angular/common';
import { DLKReportDigitizaTionService } from 'src/app/data/service/dlk-dac-thu/dlk-report-digitization.service';
@Component({
  selector: 'app-dlk-chitiet-baocao-sohoa',
  templateUrl: './dlk-chitiet-baocao-sohoa.component.html',
  styleUrls: ['./dlk-chitiet-baocao-sohoa.component.scss']
})
export class DlkChiTietBaoCaoSoHoaComponent implements OnInit {
  config = this.envService.getConfig();
  size = 10;
  pageIndex = 1;
  page = 1;
  countResult = 0;
  numberOfElements = 0;
  fromDate
  toDate
  agencyId
  type
  pgSizeOptions = this.config.pageSizeOptions;
  paginationType = this.deploymentService.env.statistics.paginationType;
  excelData: any[] = []
  displayedColumns: string[] = ['stt', 'code', 'procedure', 'sector', 'acceptedDate', 'appointmentDate', 'completedDate', 'applicant', 'phoneNumber', 'dossierStatus', 'isDossierAttachedFileTag', 'dossierReceiving', 'isDossierResultFileTag', 'digitizingStatus'];
  columns: any[];
  ELEMENTDATA: FormElement[] = [];
  dataSource: MatTableDataSource<FormElement>;
  isLoading = false;
  constructor(
    public dialogRef: MatDialogRef<DlkChiTietBaoCaoSoHoaComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any,
    private snackbarService: SnackbarService,
    private envService: EnvService,
    private deploymentService: DeploymentService,
    private exportExcel: DLKReportDigitizaTionService,
    private dlkReportDigitizaTionService: DLKReportDigitizaTionService,
    private datePipe: DatePipe,
  ) {
    this.dataSource = new MatTableDataSource(this.ELEMENTDATA);
  }

  changePageOrSize(obj) {
    var limit = this.size;
    if (tUtils.hasOwnProperty(obj, 'currentPage')) {
      this.page = obj.currentPage;
      this.pageIndex = obj.currentPage;
    }
    if (tUtils.hasOwnProperty(obj, 'itemsPerPage')) {
      this.size = obj.itemsPerPage;
    }
    if ((this.countResult - ((this.page - 1) * this.size)) < this.size) {
      limit = (this.countResult - ((this.page - 1) * this.size));
    } else {
      limit = this.size;
    }
    var searchString = "?fromDate=" + this.fromDate + "Z&id=" + this.agencyId + "&toDate=" + this.toDate + "Z&skip=" + (this.page - 1) * this.size + "&limit=" + limit + "&type=" + this.type + "&listIdProcedureStr=" + this.data.strIdProcedureDigitizationReport + "&strCodeTinhSoHoaTPHS=" + this.data.strCodeTinhSoHoaTPHSEnv;
    this.getListDossier(searchString);
  }

  ngOnInit(): void {
    this.fromDate = this.data.fromDate;
    this.toDate = this.data.toDate;
    this.agencyId = this.data.agencyId;
    this.type = this.data.type;
    this.countResult = this.data.totalItem;
    var searchString = "?fromDate=" + this.fromDate + "Z&id=" + this.agencyId + "&toDate=" + this.toDate + "Z&skip=" + (this.page - 1) * this.size + "&limit=" + this.size + "&type=" + this.type + "&listIdProcedureStr=" + this.data.strIdProcedureDigitizationReport + "&strCodeTinhSoHoaTPHS=" + this.data.strCodeTinhSoHoaTPHSEnv;
    this.getListDossier(searchString);
  }

  onDismiss() {
    this.dialogRef.close();
  }

  exportToExcelBtn() {
    const from = new Date(this.fromDate)
    from.setHours(23);
    from.setMinutes(59);
    from.setSeconds(59);
    from.setMilliseconds(165);
    const to = new Date(this.toDate);
    to.setHours(23);
    to.setMinutes(59);
    to.setSeconds(59);
    to.setMilliseconds(165);
    if (this.toDate === null || this.fromDate === null) {
      const message = {
        vi: 'Ngày thống kê không được để trống!',
        en: 'Statistical date cannot be blank!'
      };
      this.snackbarService.openSnackBar(0, '', message[localStorage.getItem('language')], 'error_notification', 3000);
      return;
    } else if (from > to) {
      const message = {
        vi: 'Ngày bắt đầu phải nhỏ hơn hoặc bằng ngày kết thúc!',
        en: 'Start date must be less than or equal to end date!'
      };
      this.snackbarService.openSnackBar(0, '', message[localStorage.getItem('language')], 'error_notification', 3000);
      return;
    }
    var searchString = "?fromDate=" + this.fromDate + "Z&id=" + this.agencyId + "&toDate=" + this.toDate + "Z&skip=" + (this.page - 1) * this.size + "&limit=" + 0 + "&type=" + this.type + "&listIdProcedureStr=" + this.data.strIdProcedureDigitizationReport + "&strCodeTinhSoHoaTPHS=" + this.data.strCodeTinhSoHoaTPHSEnv;
    this.isLoading = true;
    this.dlkReportDigitizaTionService.getListDossierPaginationData(searchString)
      .subscribe(data => {
        this.excelData = [];
        for (let i = 0; i < data?.length; i++) {
          let item: ItemExcel = new ItemExcel()
          item.stt = Number(i + 1)
          item.code = data[i]?.code ?? "";
          item.procedure = "";
          if (!!data[i]?.procedure && !!data[i]?.procedure?.translate) {
            for (let j = 0; j < data[i]?.procedure?.translate?.length; j++) {
              if (Number(localStorage.getItem('languageId')) == data[i]?.procedure?.translate[j]?.languageId) {
                item.procedure = data[i]?.procedure?.translate[j]?.name ?? "";
                break;
              }
            }
          }
          item.sector = '';
          if (!!data[i]?.procedure?.sector && !!data[i]?.procedure?.sector?.name) {
            for (let j = 0; j < data[i]?.procedure?.sector?.name?.length; j++) {
              if (Number(localStorage.getItem('languageId')) === data[i]?.procedure?.sector?.name[j]?.languageId) {
                item.sector = data[i]?.procedure?.sector?.name[j]?.name ?? "";
                break;
              }
            }
          }
          if (!!data[i]?.agency && !!data[i]?.agency?.name) {
            for (let j = 0; j < data[i].agency?.name?.length; j++) {
              if (Number(localStorage.getItem('languageId')) === data[i].agency?.name[j]?.languageId) {
                item.agencyName = data[i].agency?.name[j]?.name;
                break;
              }
            }
          }

          item.acceptedDate = this.datePipe.transform(data[i].acceptedDate, 'dd/MM/yyyy HH:mm:ss') ?? "";
          item.appointmentDate = this.datePipe.transform(data[i].appointmentDate, 'dd/MM/yyyy HH:mm:ss') ?? ""
          item.completedDate = this.datePipe.transform(data[i].completedDate, 'dd/MM/yyyy HH:mm:ss') ?? "";

          item.dossierStatus = "";
          for (let j = 0; j < data[i]?.dossierTaskStatus?.name?.length; j++) {
            if (Number(localStorage.getItem('languageId')) === data[i]?.dossierTaskStatus?.name[j]?.languageId) {
              item.dossierStatus = data[i].dossierTaskStatus?.name[j]?.name ?? "";
              break;
            }
          }

          if(!item.dossierStatus && data[i].dossierTaskStatus && data[i].dossierTaskStatus?.name && data[i].dossierTaskStatus?.name?.length > 0){
            item.dossierStatus = data[i].dossierTaskStatus?.name[0]?.name;
          }

          for (let j = 0; j < data[i]?.dossierReceivingKind?.name?.length; j++) {
            if (Number(localStorage.getItem('languageId')) === data[i].dossierReceivingKind?.name[j]?.languageId) {
              item.dossierReceiving = data[i]?.dossierReceivingKind?.name[j]?.name ?? "";
              break;
            }
          }

          this.excelData.push(item);
        }
        this.exportToExcel();
      })
    this.isLoading = false;
  }
  exportToExcel() {
    let title = '';
    let textBanner = '';
    let subtextBanner = '';
    let total = '';
    let subColumns: any[] = [];
    let agencyRootName = '';
    let sign = '';
    const userAgency = JSON.parse(localStorage.getItem('userAgency'));
    if (userAgency !== null) {
      agencyRootName = userAgency.name;
    } else {
      if (Number(localStorage.getItem('languageId')) === 228) {
        agencyRootName = this.config.rootAgency.trans.vi.name;
      }
      else {
        agencyRootName = this.config.rootAgency.trans.vi.name;
      }
    }
    let fromDateExcel = '';
    let toDateExcel = '';
    if (this.fromDate != null || this.fromDate !== '') {
      fromDateExcel = this.fromDate;
    }
    if (this.toDate != null || this.toDate !== '') {
      toDateExcel = this.toDate;
    }
    let name = '';
    switch (this.type) {
      case 0:
        name = 'DANH SÁCH HỒ SƠ CÓ SỐ HOÁ THÀNH PHẦN HỒ SƠ';
        break;
      case 1:
        name = 'DANH SÁCH HỒ SƠ CHƯA SỐ HOÁ THÀNH PHẦN HỒ SƠ';
        break;
      case 2:
        name = 'DANH SÁCH HỒ SƠ CÓ SỐ HOÁ KẾT QUẢ';
        break;
      case 3:
        name = 'DANH SÁCH HỒ SƠ CHƯA SỐ HOÁ KẾT QUẢ';
        break;
    }
    if (localStorage.getItem('language') === 'vi') {
      title = name;
      name = name + ' ' + this.datePipe.transform(fromDateExcel, 'dd/MM/yyyy')
        + ' - ' + this.datePipe.transform(toDateExcel, 'dd/MM/yyyy');
      textBanner = 'CỘNG HÒA XÃ HỘI CHỦ NGHĨA VIỆT NAM';
      subtextBanner = 'Độc lập - Tự do - Hạnh phúc';
      total = 'Tổng cộng';
      this.columns = ['STT', 'Số hồ sơ', 'Tên thủ tục hành chính', 'Tên lĩnh vực', 'Đơn vị', 'Ngày tiếp nhận', 'Ngày hẹn trả', 'Ngày kết thúc xử lý', 'Trạng thái hồ sơ', 'Hình thức nhận kết quả (Trực tiếp/Trực tuyến, Bưu điện)']
      subColumns = [];
      sign = 'Người lập báo cáo'
    } else if (localStorage.getItem('language') === 'en') {
      name = '(' + name + this.datePipe.transform(fromDateExcel, 'dd/MM/yyyy')
        + ' - ' + this.datePipe.transform(toDateExcel, 'dd/MM/yyyy') + ')';
      title = 'LIST DOSSIERS'
      textBanner = 'CỘNG HÒA XÃ HỘI CHỦ NGHĨA VIỆT NAM';
      subtextBanner = 'Độc lập - Tự do - Hạnh phúc';
      total = 'Total';
      this.columns = ['No', 'Dossier code', 'Procedure', 'Sector', 'Accepted date', 'Agency', 'Appointment date', 'Completed date', 'Dossier Status', 'Receiving kind']
      subColumns = []
      sign = 'reporter'
    }
    const subTitle = '(Từ ngày ' + this.datePipe.transform(fromDateExcel, 'dd/MM/yyyy')
      + ' Đến ngày ' + this.datePipe.transform(toDateExcel, 'dd/MM/yyyy') + ')';

    this.exportExcel.exportAsExcelFileStatisticDigitized(title, subTitle, this.columns, subColumns, this.excelData, null, name, 'Sheet1', '', this.data.agencyName, textBanner, subtextBanner, agencyRootName, sign);
  }

  getListDossier(searchString) {
    this.dlkReportDigitizaTionService.getListDossierPaginationData(searchString)
      .subscribe(data => {
        this.ELEMENTDATA = [];
        this.numberOfElements = data.length;
        for (let i = 0; i < this.numberOfElements; i++) {
          data[i].stt = this.size * (this.pageIndex - 1) + (i + 1);
          data[i].procedureName = '';
          if (!!data[i].procedure && !!data[i].procedure?.translate) {
            for (let j = 0; j < data[i].procedure?.translate?.length; j++) {
              if (Number(localStorage.getItem('languageId')) == data[i].procedure?.translate[j]?.languageId) {
                data[i].procedureName = data[i].procedure?.translate[j]?.name;
                break;
              }
            }
          }
          data[i].sectorName = '';
          if (!!data[i].procedure?.sector && !!data[i].procedure?.sector?.name) {
            for (let j = 0; j < data[i].procedure?.sector?.name?.length; j++) {
              if (Number(localStorage.getItem('languageId')) === data[i].procedure?.sector?.name[j]?.languageId) {
                data[i].sectorName = data[i].procedure?.sector?.name[j]?.name;
                break;
              }
            }
          }
          for (let j = 0; j < data[i].dossierReceivingKind?.name?.length; j++) {
            if (Number(localStorage.getItem('languageId')) === data[i].dossierReceivingKind?.name[j]?.languageId) {
              data[i].receivingKind = data[i].dossierReceivingKind?.name[j]?.name;
              break;
            }
          }

          if (!!data[i].attachment) {
            for (let j = 0; j < data[i].attachment.length; j++) {
              if (data[i].attachment[j]?.group == this.config.dossierAttachedFileTagId) {
                break;
              }
            }
          }
          
          data[i].dossierStatusName = "";

          for (let j = 0; j < data[i].dossierTaskStatus?.name?.length; j++) {
            if (Number(localStorage.getItem('languageId')) === data[i].dossierTaskStatus?.name[j]?.languageId) {
              data[i].dossierStatusName = data[i].dossierTaskStatus?.name[j]?.name;
              break;
            }
          }
          
          if(!data[i].dossierStatusName && data[i].dossierTaskStatus && data[i].dossierTaskStatus?.name && data[i].dossierTaskStatus?.name?.length > 0){
            data[i].dossierStatusName = data[i].dossierTaskStatus?.name[0]?.name;
          }

          for (let j = 0; j < data[i].agency?.name?.length; j++) {
            if (Number(localStorage.getItem('languageId')) === data[i].agency?.name[j]?.languageId) {
              data[i].agencyName = data[i].agency?.name[j]?.name;
              break;
            }
          }

          this.ELEMENTDATA.push(data[i]);
        }
        this.dataSource.data = this.ELEMENTDATA;
      })
  }
}

class ItemExcel {
  stt: number;
  code: string;
  procedure: string;
  sector: string;
  acceptedDate: string;
  appointmentDate: string;
  completedDate: string;
  applicant: string;
  phoneNumber: string;
  dossierStatus: string;
  isDossierAttachedFileTag: string;
  dossierReceiving: string;
  isDossierResultFileTag: string;
  digitizingStatus: string;
  agencyName: string;
}