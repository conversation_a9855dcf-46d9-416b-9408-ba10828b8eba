import { Injectable } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { ApiProviderService } from 'src/app/core/service/api-provider.service';
import { Observable } from 'rxjs';
import {SnackbarService} from 'data/service/snackbar/snackbar.service';
import {EnvService} from 'core/service/env.service';
import {DeploymentService} from 'data/service/deployment.service';
import { saveAs } from 'file-saver';
@Injectable({
  providedIn: 'root'
})
export class ReportService {
  config = this.envService.getConfig();
  padmanReport  = this.deploymentService.getAppDeployment()?.padmanReport || false;
  constructor(
    private envService: EnvService,
    private dialog: MatDialog,
    private http: HttpClient,
    private snackbar: SnackbarService,
    private apiProviderService: ApiProviderService,
    private deploymentService: DeploymentService
  ) { }

  private basepad = this.apiProviderService.getUrl('digo', 'basepad');
  private getAgencyURL = this.apiProviderService.getUrl('digo', 'basedata');
  private getSector = `${this.basepad}/sector`;
  private procedurePath = `${this.basepad}/procedure`;
  private getTag = this.apiProviderService.getUrl('digo', 'basecat');
  private getDossierPayment = this.apiProviderService.getUrl('digo', 'padman');
  private padmanURLReport = this.apiProviderService.getUrl('digo', 'padmanReport');
  private padman = this.apiProviderService.getUrl('digo', 'padman');
  private reporter = this.apiProviderService.getUrl('digo', 'reporter');
  private adapter = this.apiProviderService.getUrl('digo', 'adapter');
  private statistics = this.apiProviderService.getUrl('digo', 'statistics');

  getDigitizationQniDossierReport(search): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.reporter + '/digitizing-report-qni' + search, { headers }).pipe();
  }

  getDetailDossierQniDigitization(search): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.reporter + '/digitizing-report-qni/--detail' + search, { headers }).pipe();
  }

  getListSector(): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.getSector, { headers }).pipe();
  }

  getPageSectors(page?: number, size?: number, keyword?: string): Observable<any> {
    let URL = `${this.getSector}?spec=page&page=${page}&size=${size}`;
    if (keyword) {
      URL += `&keyword=` + keyword;
    }
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(URL, { headers }).pipe();
  }
  getPageSectorsGLI(page?: number, size?: number, keyword?: string,parentId?: string,agency?: string,): Observable<any> {
    let URL = `${this.basepad}/sector/--by-agency-gli?spec=page&page=${page}&size=${size}&parent-agency-id=${parentId}&agency-id=${agency}`;
    if (keyword) {
      URL += `&keyword=` + keyword;
    }
    let headers = new HttpHeaders();
    headers = headers.append('Authorization', 'Bearer ' + localStorage.getItem('OAuth2TOKEN'));
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(URL, { headers }).pipe();
  }

  getListAgency(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    let kq = '';
    if (id !== null && id !== '') {
      kq = '&parent-id=' + id;
    }
    return this.http.get(this.getAgencyURL + '/agency/' + '?page=0&size=50' + kq, { headers }).pipe();
  }

  getListAgencyForReceipt(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    let kq = '';
    if (id !== null && id !== '') {
      kq = '&parent-id=' + id;
    }
    return this.http.get(this.getAgencyURL + '/agency/' + id);
  }

  getPageAgency(page?: number, size?: number, parentId?: string, keyword?: string): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    let URL = this.getAgencyURL + `/agency?page=${page}&size=${size}&sort=name.name,asc&status=1`;
    if (!!parentId) {
      URL += `&parent-id=${parentId}`;
    }
    if (!!keyword) {
      URL += `&keyword=${keyword}`;
    }
    return this.http.get(URL, { headers }).pipe();
  }

  getAgency( parentId?: string): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    let URL = this.getAgencyURL + `/agency?size=1000&sort=name.name,asc&status=1`;
    if (parentId) {
      URL += `&parent-id=${parentId}`;
    }
    return this.http.get(URL, { headers }).pipe();
  }


  getPageAgencyTag(page?: number, size?: number, id?: string, keyword?: string): Observable<any> {
    let URL = `${this.getTag}/tag/--by-category-id?category-id=${id}&page=${page}&size=${size}`;
    if (!!keyword) {
      URL += `&keyword=${keyword}`;
    }
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(URL, { headers }).pipe();
  }

  getListAgencyLevel(keyword?: string): Observable<any> {
    let url = this.getAgencyURL + '/agency-level';
    url += '?size=1000';
    if (keyword) {
      url += '&keyword=' + keyword;
    }
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(url, { headers }).pipe();
  }
  
  getReportByDossierDetailHBH(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    //return this.http.get('http://localhost:8081/hbh-report/statistic/--by-process-detail'+ searchString, { headers });
    return this.http.get( this.getDossierPayment + '/hbh-report/statistic/--by-process-detail' + searchString, {headers});
  }


  getReportByDossierDetailDBN(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    if(this.deploymentService.env.OS_HGI.enableStatisticalSector==1 || this.deploymentService.env.OS_HGI.enableStatisticalAll==1 || this.deploymentService.env.OS_HGI.enableStatisticalProcedure==1){
      return this.http.get(this.getDossierPayment + '/hgi-report/statistic/--by-process-detail' + searchString, {headers});
    }
    else{
      return this.http.get( this.getDossierPayment + '/dbn-report/statistic/--by-process-detail' + searchString, {headers});
    }
  }

  getReportByDossierDetailHGI(searchString,body): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.post( this.getDossierPayment + '/hgi-report/statistic/--by-process-detail' + searchString, body, { headers }).pipe();
  }

  getReportByProcedureHGI(searchString, body): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.post( this.getDossierPayment + '/hgi-report/statistic/--by-procedure' + searchString, body, { headers }).pipe();
  }

  getReportByAgencySectorHGI(searchString, body): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.post( this.getDossierPayment + '/hgi-report/statistic/--by-process' + searchString, body, { headers }).pipe();
  }

  getReportBySectorHGI(searchString, body): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.post( this.getDossierPayment + '/hgi-report/statistic/--by-sector' + searchString, body, { headers }).pipe();
  }

  getReportByDossierDetailAGG(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get( this.getDossierPayment + '/agg-report/statistic/--by-process-detail' + searchString, {headers});
  }

  getReportBCCIDBN(body): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    if (window.location.hostname === 'localhost'){
      return this.http.post( 'http://localhost:8084/dbn-vnpost/--report', body, { headers }).pipe();
    } else
    {
      return this.http.post( this.adapter + '/dbn-vnpost/--report' , body, { headers }).pipe();
    }
  }

  getReportBCCIHGI(body): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language') || 'vi');
    headers = headers.append('Content-Type', 'application/json');
    // headers = headers.append('Access-Control-Allow-Origin', '*');
    // return this.http.post('http://localhost:8081/hgi-report/statistic/dossier-bcci', body,{ headers }).pipe();
    return this.http.post(this.getDossierPayment + '/hgi-report/statistic/dossier-bcci', body,{ headers }).pipe();
  }

  getDossierDetailBCCI(body, page: number, size: number, exportExcel: boolean = false): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language') || 'vi');
    headers = headers.append('Content-Type', 'application/json');
    // headers = headers.append('Access-Control-Allow-Origin', '*');
    // return this.http.post('http://localhost:8081/hgi-report/statistic/dossier-detail-bcci', body, { headers }).pipe();
    return this.http.post(this.getDossierPayment + '/hgi-report/statistic/dossier-detail-bcci?page='+page+'&size='+size+'&exportExcel='+exportExcel, body,{ headers }).pipe();
  }

  exportToExcelStatistic(params: string): any {
    const url = this.getDossierPayment + '/dbn-report/statistic/--by-process-detail/--export' + params;
    this.getFileExport(url).then();
  }

  exportToExcelStatisticHGI(params: string, body): any {
    const url = this.getDossierPayment + '/hgi-report/statistic/--by-process-detail/--export' + params;
    this.getFileExportPost(url,body).then();
  }

  exportToExcelStatisticHbh(params: string): any {
    //const url = 'http://localhost:8081/hbh-report/statistic/--by-process-detail/--export' + params;
    const url = this.getDossierPayment + '/hbh-report/statistic/--by-process-detail/--export' + params;
    this.getFileExport(url).then();
}


  exportToExcelStatisticAGG(params: string): any {
    const url = this.getDossierPayment + '/agg-report/statistic/--by-process-detail/--export' + params;
    this.getFileExport(url).then();
 }

  exportDossierStatisticDetail(params: string): any {
    const url = this.getDossierPayment + '/dbn-digitize/digitization-detail/--excel' + params;
    this.getFileExport(url).then();
  }

  exportDossierStatisticDetailKGG(params: string): any {
    const url = this.getDossierPayment + '/kgg-digitize/digitization-detail/--excel' + params;
    this.getFileExport(url).then();
  }

  exportDossierStatisticDetailQNI(params: string): any {
    const url = this.getDossierPayment + '/qni-digitize/digitization-detail/--excel' + params;
    this.getFileExport(url).then();
  }

  exportDossierStatisticDetailQNIV2(params: string): any {
    const url = this.reporter + '/digitizing-report-qni/--detail/--export' + params;
    this.getFileExport(url).then();
  }

  exportDossierStatisticDetailAGG(params: string): any {
    const url = this.padman + '/agg-digitize/digitization-detail/--excel' + params;
    //this.getFileExport(url).then();
    return new Promise((resolve) => {
      this.http.get(this.padman + '/agg-digitize/digitization-detail/--excel' + params, {
        observe: 'response',
        responseType: 'blob'
      }).toPromise().then(res => {
        const blob = new Blob([res.body], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'});
        let filename = 'bao_cao_chi_tiet_ho_so.xlsx';
        if (res.headers.get('content-disposition') != null) {
          filename = res.headers.get('content-disposition').split(';')[1].split('=')[1].replace(/"/g, '');
        }
        const blobUrl = URL.createObjectURL(blob);
        const xhr = new XMLHttpRequest();
        xhr.responseType = 'blob';

        xhr.onload = () => {
          const recoveredBlob = xhr.response;
          const reader = new FileReader();
          reader.onload = () => {
            const base64data = reader.result.toString();
            const anchor = document.createElement('a');
            anchor.download = filename;
            anchor.href = base64data;
            anchor.click();
          };
          reader.readAsDataURL(recoveredBlob);
        };

        xhr.open('GET', blobUrl);
        xhr.send();
        resolve(true);
      }).catch(err => {
        if (err.status === 500) {
          const message = {
            vi: 'Hệ thống tạm thời không thể xuất file excel!',
            en: 'The system is temporarily unable to export the excel file!'
          };
          this.snackbar.openSnackBar(0, '', message[localStorage.getItem('language')], 'error_notification', this.config.expiredTime);
        }
        resolve(false);
      });
    });
  }

  getFileExport(url) {
      return new Promise((resolve) => {
          this.http.get(url, {
              observe: 'response',
              responseType: 'blob'
          }).toPromise().then(res => {
              const blob = new Blob([res.body], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'});
              let filename = 'bao_cao_chi_tiet_ho_so.xlsx';
              if (res.headers.get('Content-Disposition') != null) {
                  filename = res.headers.get('Content-Disposition').split(';')[1].split('=')[1].replace(/\"/g, '');
              }
              const blobUrl = URL.createObjectURL(blob);

              const xhr = new XMLHttpRequest();
              xhr.responseType = 'blob';

              xhr.onload = () => {
                  const recoveredBlob = xhr.response;
                  const reader = new FileReader();
                  reader.onload = () => {
                      const base64data = reader.result.toString();
                      const object = JSON.stringify({
                          mimeType: filename.substring(filename.lastIndexOf('.') + 1),
                          name: filename.substring(0, filename.lastIndexOf('.')),
                          data: base64data.split(',')[1]
                      });
                      const anchor = document.createElement('a');
                      anchor.download = filename;
                      anchor.href = base64data;
                      anchor.click();
                  };
                  reader.readAsDataURL(recoveredBlob);
              };

              xhr.open('GET', blobUrl);
              xhr.send();
              resolve(true);
          }).catch(err => {
              if (err.status === 500) {
                  const message = {
                      vi: 'Hệ thống tạm thời không thể xuất file excel!',
                      en: 'The system is temporarily unable to export the excel file!'
                  };
                  this.snackbar.openSnackBar(0, '', message[localStorage.getItem('language')], 'error_notification',
                      this.config.expiredTime);
              }
              resolve(false);
          });
      });
  }
  getReportByAgencySectorHBH(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    //return this.http.get('http://localhost:8081/hbh-report/statistic/--by-process'+ searchString);
    return this.http.get( this.getDossierPayment + '/hbh-report/statistic/--by-process' + searchString);
  }

  getReportByAgencySectorDBN(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    if(this.deploymentService.env.OS_HGI.enableStatisticalAll==1){
      return this.http.get( this.getDossierPayment + '/hgi-report/statistic/--by-process' + searchString);
    }
    else{
      return this.http.get( this.getDossierPayment + '/dbn-report/statistic/--by-process' + searchString);
    }
  }

  getReportByAgencySectorAGG(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get( this.getDossierPayment + '/agg-report/statistic/--by-process' + searchString);
  }

  digitizeMultiDossier(data): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    if (window.location.hostname === 'localhost'){
      return this.http.put<any>('http://localhost:8080/dbn-digitize/--dossier', data, { headers });
    } else {
      return this.http.put<any>(this.getDossierPayment + '/dbn-digitize/--dossier', data, { headers });
    }
  }

  getListDigitizeDossier(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    if (window.location.hostname === 'localhost'){
      return this.http.get('http://localhost:8080/dbn-digitize/' + searchString);
    } else {
      return this.http.get(this.getDossierPayment + /dbn-digitize/ + searchString);
    }
  }

  getReportByProcedureDBN(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    if(this.deploymentService.env.OS_HGI.enableStatisticalProcedure==1){
       return this.http.get( this.getDossierPayment + '/hgi-report/statistic/--by-procedure' + searchString, {headers});
    }
    else{
      return this.http.get( this.getDossierPayment + '/dbn-report/statistic/--by-procedure' + searchString, {headers});
    }
  }

  getReportByProcedureHbh(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    //return this.http.get('http://localhost:8081/hbh-report/statistic/--by-procedure'+ searchString);
    return this.http.get( this.getDossierPayment + '/hbh-report/statistic/--by-procedure' + searchString, {headers});
  }

  getReportByProcedureAGG(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get( this.getDossierPayment + '/agg-report/statistic/--by-procedure' + searchString, {headers});
  }

  getReportBySectorDBN(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    if(this.deploymentService.env.OS_HGI.enableStatisticalSector==1){
      return this.http.get( this.getDossierPayment + '/hgi-report/statistic/--by-sector' + searchString);
    }
    else{
      return this.http.get( this.getDossierPayment + '/dbn-report/statistic/--by-sector' + searchString);
    }
  }

  getReportBySectorHBH(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    //return this.http.get('http://localhost:8081/hbh-report/statistic/--by-sector-hbh' + searchString);
    return this.http.get( this.getDossierPayment + '/hbh-report/statistic/--by-sector-hbh' + searchString);
  }
  getListProcedure(sectorId = '', agencyLevel = '', proceLevelId = '', agencyId = ''): Observable<any> {

    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    if (sectorId == null) {
      sectorId = '';
    }
    if (agencyId == null) {
      agencyId = '';
    }
    if (proceLevelId == null) {
      proceLevelId = '';
    }
    if (agencyLevel == null) {
      agencyLevel = '';
    }
    const URL = `${this.procedurePath}?sector-id=${sectorId}&agency-id=${agencyId}&agency-level-id=${agencyLevel}procedure-level-id=${proceLevelId}&&page=0&size=50`;
    return this.http.get(URL).pipe();
  }

  getListAgencyTag(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.getTag + '/tag/--by-category-id?category-id=' + id + '&page=0&size=50').pipe();
  }

  getReportForDossier(searchParams): Observable<any> {
	// return this.http.get(this.getDossierPayment + '/dossier-payment/' + searchString).pipe();
    return this.http.get((!this.padmanReport ? this.getDossierPayment : this.padmanURLReport) + '/dossier-fee/--statictis', {params: searchParams}).pipe();
  }

  getReportForDossierElastis(searchParams): Observable<any> {
    // return this.http.get(this.getDossierPayment + '/dossier-payment/' + searchString).pipe();
      return this.http.get(this.statistics + '/dossier-fee-statistic/--statistics', {params: searchParams}).pipe();
    }

  getReportForDossierElastisByProcedure(searchParams): Observable<any> {
      // return this.http.get(this.getDossierPayment + '/dossier-payment/' + searchString).pipe();
        return this.http.get(this.statistics + '/dossier-fee-statistic/--statistics-procedure', {params: searchParams}).pipe();
      }

  getReportForDossierElastisBySector(searchParams): Observable<any> {
        // return this.http.get(this.getDossierPayment + '/dossier-payment/' + searchString).pipe();
          return this.http.get(this.statistics + '/dossier-fee-statistic/--statistics-sector', {params: searchParams}).pipe();
        }

  getReportForDossierElastisByAgency(searchParams): Observable<any> {
          // return this.http.get(this.getDossierPayment + '/dossier-payment/' + searchString).pipe();
            return this.http.get(this.statistics + '/dossier-fee-statistic/--statistics-agency', {params: searchParams}).pipe();
          }

  getReportForDossierKGG(searchParams): Observable<any> {
	// return this.http.get(this.getDossierPayment + '/dossier-payment/' + searchString).pipe();
    return this.http.get((!this.padmanReport ? this.getDossierPayment : this.padmanURLReport) + '/dossier-fee/--statictis-kgg', {params: searchParams}).pipe();
  }

  getFeeReceiptReportForDossier(searchParams): Observable<any> {
    //return this.http.get('http://localhost:8081' + '/dossier-fee/--receipt-fee-statictis', {params: searchParams}).pipe();
    return this.http.get((!this.padmanReport ? this.padman : this.padmanURLReport) + '/dossier-fee/--receipt-fee-statictis', {params: searchParams}).pipe();
  }

  getFeeReportForDossier(searchParams): Observable<any> {
    // return this.http.get(this.getDossierPayment + '/dossier-payment/' + searchString).pipe();
    return this.http.get((!this.padmanReport ? this.getDossierPayment : this.padmanURLReport) + '/dossier-fee/--fee-report', {params: searchParams}).pipe();
  }

  getListApologyTextByProcedure(searchString): Observable<any> {
    return this.http.get(this.basepad + '/procedure-config/template/--list' + searchString);
  }

  getListApologyTextByAgency(searchString): Observable<any> {
    return this.http.get(this.basepad + '/agency-config/template/--list' + searchString);
  }

  getListApologyTextDefault(): Observable<any> {
    return this.http.get(this.basepad + '/default-config/--all');
  }

  getReportByAgencySector(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.reporter + '/dossiercounting/--from-to-by-agency-sector' + searchString);
  }

  getReportByAgencySectorV2(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.reporter + '/dossiercounting/--from-to-by-agency-sectors' + searchString);
  }

  getListReportById(search): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.reporter + '/template' + search);
  }

  getBarReportData(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.reporter + '/dossiercounting/--from-to-bundle' + searchString, { headers }).pipe();
  }

  getBarReportDataV2(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
 //   let url = "http://localhost:8088"
    return this.http.get( this.reporter + '/dossiercounting/--from-to-bundles' + searchString, { headers }).pipe();
  }
  getBarReportAgencySector(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
 //   let url = "http://localhost:8088"
    return this.http.get( this.reporter + '/dossiercounting/--from-to-by-agency-sectors' + searchString, { headers }).pipe();
  }

  getBarReportDetails(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
 //   let url = "http://localhost:8088"
    return this.http.get( this.reporter + '/dossiercounting/--get-id-dossier' + searchString, { headers }).pipe();
  }

  getDigitizationReportDetails(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
 //   let url = "http://localhost:8088"
    return this.http.get( this.reporter + '/dossiercounting/--digitization-v2-details' + searchString, { headers }).pipe();
  }

  getReportDataByAgency(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    switch (localStorage.getItem('isLoggedIn')) {
      case 'false':
        const token = localStorage.getItem('OAuth2TOKEN');
        headers = headers.append('Authorization', 'Bearer ' + token);
        return this.http.get(this.reporter + '/dossiercounting/--from-to-by-agency' + searchString, { headers }).pipe();
      case 'true':
        return this.http.get(this.reporter + '/dossiercounting/--from-to-by-agency' + searchString, { headers }).pipe();
    }
  }

  getListDossierCoutingDetailReport(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.reporter + '/dossiercounting/--counting-detail' + searchString, { headers }).pipe();
  }

  getListDossierCoutingDetailReceivedReport(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.reporter + '/dossiercounting/--received-applymethod-detail' + searchString, { headers }).pipe();
  }

  getListDossierCoutingDetailPreviousReport(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.reporter + '/dossiercounting/--counting-detail-previous' + searchString, { headers }).pipe();
  }

  getDocumentDigitizationReport(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    // headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.reporter + '/dossiercounting/--digitization' + searchString, { headers }).pipe();
  }

  getReportForStatisticRegistrationReader(searchParams): Observable<any> {
    return this.http.get((!this.padmanReport ? this.getDossierPayment : this.padmanURLReport) + '/dossier/--search-registration-reader', {params: searchParams}).pipe();
  }

  getReportForStatisticHCMAdbook(searchParams): Observable<any> {
    return this.http.get((!this.padmanReport ? this.getDossierPayment : this.padmanURLReport) + '/dossier/--statictis-hcm-adbook', {params: searchParams}).pipe();
  }

  getReportForDossierDBN(searchParams): Observable<any> {
        return this.http.get(this.getDossierPayment + '/dossier-fee/--statictisdbn', {params: searchParams}).pipe();
  }


  getListAgencyByParentId(searchParams): Observable<any> {
        return this.http.get(this.getAgencyURL + '/agency/--by-parent-id' , { params: searchParams }).pipe();
  }

  getListDossierStatisticFee(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.getDossierPayment +'/dossier-receipt/--statistic-dossier-receipt-fee' + searchString, { headers });
  }

  generalStatisticReceiptList(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.getDossierPayment +'/dossier-receipt/--general-statistic-receipt-fee' + searchString, { headers });
  }
  generalAgencyStatisticDetailList(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.getDossierPayment +'/dossier-receipt/--general-agency-statistic-detail-list' + searchString, { headers });
  }
  getDocumentDigitizationReportv2(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.reporter + '/dossiercounting/--digitization-v2' + searchString, { headers }).pipe();
  }

  getDocumentDigitizationReportv2_1(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.reporter + '/dossiercounting/--digitization-v2-1' + searchString, { headers }).pipe();
  }

  // new version export StatictisLLTPDossier.
  exportToExcelLLTPDossierStatisticV2(searchParams){
    console.log('download Thong-ke-ho-so-Ly-lich-Tu-Phap-tiep-nhan-truc-tuyen excel');
    this.getExportStatictisLLTPDossierV2(searchParams).subscribe((response: Blob) => {
      saveAs(response, "Thong-ke-ho-so-Ly-lich-Tu-Phap-tiep-nhan-truc-tuyen.xlsx");
    });
  }
  getExportStatictisLLTPDossierV2(searchParams): Observable<any>{
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get( this.statistics + '/hcm-dossier-statistic/--dossier-lltp-statistic-export', {params: searchParams, headers, responseType: 'blob' })
  }



  getStaticticalFee(searchParams): Observable<any> {
    return this.http.get((!this.padmanReport ? this.getDossierPayment : this.padmanURLReport) + '/dossier-fee/--statictical', {params: searchParams}).pipe();
  }

  getStaticticalFeeBDH(searchParams): Observable<any> {
    const url = `${this.getDossierPayment}/statistic-bdh/--dossier-fee`;
    return this.http.get(url, { params: searchParams });
  }

  getStaticticalFeeReceiptBDH(searchParams): Observable<any> {
    const url = `${this.getDossierPayment}/statistic-bdh/--dossier-fee-invoice-receipt`;
    return this.http.get(url, {params: searchParams});
  }

  getStaticticalFeeHbh(searchParams): Observable<any> {
    //return this.http.get('http://localhost:8081' + '/dossier-fee/--statictical-hbh', {params: searchParams}).pipe();
    return this.http.get(this.getDossierPayment + '/dossier-fee/--statictical-hbh', {params: searchParams}).pipe();
  }

  getStaticticalFeeAGG(searchParams): Observable<any> {
    return this.http.get(this.getDossierPayment + '/dossier-fee/--statictical-agg', {params: searchParams}).pipe();
  }

  getReportForDossierHGI(searchParams): Observable<any> {
    return this.http.get(this.getDossierPayment + '/dossier-fee-hgi/--statictis', {params: searchParams}).pipe();
  }

  getFeeReceiptReportForDossierV2(searchParams): Observable<any> {
    return this.http.get(this.statistics + '/dossier-fee/--receipt-fee-statictis', {params: searchParams}).pipe();
  }

  exportFeeReceiptReportForDossierV2(searchParams){
    this.getExportFeeReceiptReportForDossierV2(searchParams).subscribe((response: Blob) => {
      saveAs(response, "Thong ke phi co so bien lai.xlsx");
    });
  }

  getExportFeeReceiptReportForDossierV2(searchParams): Observable<any>{
   // const URL = "http://localhost:8081/user" + '/--report-user-export'
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get( this.statistics + '/dossier-fee/--receipt-fee-statictis-export', {params: searchParams, headers, responseType: 'blob' })
  }

  getPageAgencyByLevel(page?: number, size?: number, parentId?: string, keyword?: string, levelId?: string): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    let URL = this.getAgencyURL + `/agency?page=${page}&size=${size}&sort=name.name,asc&status=1`;
    if (!!parentId) {
      URL += `&parent-id=${parentId}`;
    }
    if (!!keyword) {
      URL += `&keyword=${keyword}`;
    }
    if (!!levelId) {
      URL += `&level=${levelId}`;
    }
    return this.http.get(URL, { headers }).pipe();
  }

  getReportTTHCCForm5AGG(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get( this.getDossierPayment + '/agg-report/statistic/--by-process-form5' + searchString);
  }

  getReportByDossierDetailForm5AGG(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get( this.getDossierPayment + '/agg-report/statistic/--by-process-detail-form5' + searchString, {headers});
  }

  getReportM4V3AGG(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get( this.getDossierPayment + '/agg-m4v3-report/statistic/--by-process' + searchString);
  }

  getReportM4V3DetailAGG(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get( this.getDossierPayment + '/agg-m4v3-report/statistic/--by-process-detail' + searchString, {headers});
  }
  getReportStatictisProcdureListHGI(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get( this.basepad + '/procedure-list-hgi/--by-agency' + searchString, {headers});
    
  }

  getProcdureDetailHGI(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get( this.basepad + '/procedure-list-hgi/list-procedure-detail' + searchString, {headers});
    
  }

   //Start IGATESUPP-97889
  getTransferResultsLIstHGI(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    // return this.http.get( 'http://localhost:8081/hgi-report/statistic/--by-transferresult-hgi' + searchString);
    return this.http.get( this.getDossierPayment + '/hgi-report/statistic/--by-transferresult-hgi' + searchString);
  }
  getTransferResultsLIstToExcelHGI(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    // return this.http.get( 'http://localhost:8081/hgi-report/statistic/get-transferresult-to-excel-hgi' + searchString);
    return this.http.get( this.getDossierPayment + '/hgi-report/statistic/get-transferresult-to-excel-hgi' + searchString);
  }
   //End IGATESUPP-97889
   
   getReportStatictisPaidDossierListHGI(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    //  return this.http.get( 'http://localhost:8081/paid-dossier-hgi/hgi-statistics' + searchString);
    return this.http.get( this.getDossierPayment + '/paid-dossier-hgi/hgi-statistics' + searchString);
  }

  getPaidDossierDetailHGI(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    // return this.http.get( 'http://localhost:8081/paid-dossier-hgi/get-list-dossier' + searchString);
    return this.http.get( this.getDossierPayment + '/paid-dossier-hgi/get-list-dossier' + searchString);
  }

getAgecncyTagForgetPaidDossierDetailHGI(): Observable<any>{
  let headers = new HttpHeaders();
  let URL = this.getTag+"/tag/--search?code=LIST_TAG&name=";
  headers = headers.set('Accept-Language', localStorage.getItem('language'));
  return this.http.get( URL, { headers }).pipe();
}


getListStatisticOverdue8hAgencyVpc(searchString): Observable<any> {
  let headers = new HttpHeaders();
  headers = headers.set('Accept-Language', localStorage.getItem('language'));
  return this.http.get(this.padman + '/vpc-statistic-overdue-8h/agency/list?' + searchString, { headers });
}

getListDossierOverdue8hDetailVpc(searchString): Observable<any> {
  let headers = new HttpHeaders();
  headers = headers.set('Accept-Language', localStorage.getItem('language'));
  return this.http.get(this.padman + '/vpc-statistic-overdue-8h/agency/detail?' + searchString, { headers });
}

getReportKiosDossierListHGI(searchString): Observable<any> {
  let headers = new HttpHeaders();
  headers = headers.set('Accept-Language', localStorage.getItem('language'));
  //return this.http.get( 'http://localhost:8081/kios-dossier-hgi/get-kios-dossier' + searchString);
  return this.http.get( this.getDossierPayment + '/kios-dossier-hgi/get-kios-dossier' + searchString);
}
getReportKiosDossierDetailListHGI(searchString): Observable<any> {
  let headers = new HttpHeaders();
  headers = headers.set('Accept-Language', localStorage.getItem('language'));
  //return this.http.get( 'http://localhost:8081/kios-dossier-hgi/get-kios-dossier-detail' + searchString); 
  return this.http.get( this.getDossierPayment + '/kios-dossier-hgi/get-kios-dossier-detail' + searchString);
}
getFileExportPost(url, body) {
  return new Promise((resolve) => {
      this.http.post(url, body, {
          observe: 'response',
          responseType: 'blob'
      }).toPromise().then(res => {
          const blob = new Blob([res.body], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'});
          let filename = 'bao_cao_chi_tiet_ho_so.xlsx';
          if (res.headers.get('Content-Disposition') != null) {
              filename = res.headers.get('Content-Disposition').split(';')[1].split('=')[1].replace(/"/g, '');
          }
          const blobUrl = URL.createObjectURL(blob);

          const xhr = new XMLHttpRequest();
          xhr.responseType = 'blob';

          xhr.onload = () => {
              const recoveredBlob = xhr.response;
              const reader = new FileReader();
              reader.onload = () => {
                  const base64data = reader.result.toString();
                  const object = JSON.stringify({
                      mimeType: filename.substring(filename.lastIndexOf('.') + 1),
                      name: filename.substring(0, filename.lastIndexOf('.')),
                      data: base64data.split(',')[1]
                  });
                  const anchor = document.createElement('a');
                  anchor.download = filename;
                  anchor.href = base64data;
                  anchor.click();
              };
              reader.readAsDataURL(recoveredBlob);
          };

          xhr.open('GET', blobUrl);
          xhr.send();
          resolve(true);
      }).catch(err => {
          if (err.status === 500) {
              const message = {
                  vi: 'Hệ thống tạm thời không thể xuất file excel!',
                  en: 'The system is temporarily unable to export the excel file!'
              };
              this.snackbar.openSnackBar(0, '', message[localStorage.getItem('language')], 'error_notification',
                  this.config.expiredTime);
          }
          resolve(false);
      });
  });
}
getReportRatingAgencyHGI(searchString): Observable<any> {
  let headers = new HttpHeaders();
  headers = headers.set('Accept-Language', localStorage.getItem('language'));
  return this.http.get( this.getDossierPayment + '/hgi-report/statistic/--agency-rating' + searchString, {headers});
}
getReportRatingOfficerHGI(searchString): Observable<any> {
  let headers = new HttpHeaders();
  headers = headers.set('Accept-Language', localStorage.getItem('language'));
  return this.http.get( this.getDossierPayment + '/hgi-report/statistic/--officer-rating' + searchString, {headers});
}
getReportRatingDetail(searchString): Observable<any> {
  let headers = new HttpHeaders();
  headers = headers.set('Accept-Language', localStorage.getItem('language'));
  return this.http.get(this.getDossierPayment + '/hgi-report/statistic/--by-rating-detail' + searchString, {headers});
}
exportToExcelStatisticRatingHGI(params: string): any {
  const url = this.getDossierPayment + '/hgi-report/statistic/--by-rating-detail/--export' + params;
  this.getFileExport(url).then();
}
getReportByProcedureLdg(searchString, body): Observable<any> {
  let headers = new HttpHeaders();
  headers = headers.set('Accept-Language', localStorage.getItem('language'));
  console.log(this.getDossierPayment )
  return this.http.post( this.getDossierPayment + '/statistics-ldg/--by-procedure' + searchString, body, { headers }).pipe();
}
getReportByDossierDetailLdg(searchString,body): Observable<any> {
  let headers = new HttpHeaders();
  headers = headers.set('Accept-Language', localStorage.getItem('language'));
  return this.http.post( this.getDossierPayment + '/statistics-ldg/--by-process-detail' + searchString, body, { headers }).pipe();
}
}
