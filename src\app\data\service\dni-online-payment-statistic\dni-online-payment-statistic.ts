import { Injectable } from '@angular/core';
import { Workbook } from 'exceljs';
import ExcelJS from 'exceljs';
import { Observable } from 'rxjs';
import { MatDialog } from '@angular/material/dialog';
import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { EnvService } from 'core/service/env.service';
import { DeploymentService } from '../deployment.service';
import { SnackbarService } from 'data/service/snackbar/snackbar.service';
import { ApiProviderService } from 'src/app/core/service/api-provider.service';
import * as fs from 'file-saver';

const EXCEL_TYPE = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8';
const EXCEL_EXTENSION = '.xlsx';

@Injectable({
  providedIn: 'root'
})
export class DNIOnlinePaymentService {

  language = localStorage.getItem('language') === 'vi' ? 228 : 46;
  config = this.envService.getConfig();

  constructor(
    private dialog: MatDialog,
    private http: HttpClient,
    private envService: EnvService,
    private snackbar: SnackbarService,
    private deploymentService: DeploymentService,
    private apiProviderService: ApiProviderService,
  ) { }

  private padmanURL = this.apiProviderService.getUrl('digo', 'padman');
  private basedataURL = this.apiProviderService.getUrl('digo', 'basedata');
  private basepadURL = this.apiProviderService.getUrl('digo', 'basepad');
  private statisticsURL = this.apiProviderService.getUrl('digo','statistics');
  getDossierStatisticReceived(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');
    //return this.http.get('http://localhost:8081/dossier-statistic-received/--received-statistic' + searchString, { headers }).pipe();

    return this.http.get(this.padmanURL + '/dossier-statistic-received/--received-statistic' + searchString, { headers }).pipe();
  }

  getDossierStatisticDigitizationDetail(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');
    //return this.http.get('http://localhost:8081/dni-online-payment-statistic/--dossier-static-detail' + searchString, { headers }).pipe();

    return this.http.get(this.padmanURL + '/dni-online-payment-statistic/--dossier-static-detail' + searchString, { headers }).pipe();
  }

  getProcedureDataDetail(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');
    //return this.http.get('http://localhost:8091/procedure/report-onl-paymnet-dni/--detail' + searchString, { headers }).pipe();

    return this.http.get(this.basepadURL + '/procedure/report-onl-paymnet-dni/--detail' + searchString, { headers }).pipe();
  }

  getProcedureDetails(procedureIds: string[], page: number, size: number): Observable<any> {
    let params = new HttpParams();

    // Append all procedure-id-list
    procedureIds.forEach(id => {
      params = params.append('procedure-id-list', id);
    });


    // Pagination if needed
    params = params.set('page', page.toString());
    params = params.set('size', size.toString());

    //return this.http.get('http://localhost:8091/procedure/report-onl-payment/--detail-by-list', { params });
    return this.http.get(this.basepadURL +'/procedure/report-onl-payment/--detail-by-list', { params });

  }

  exportProcedureForSpecify(procedureIds: string[]): Promise<boolean> {
    return new Promise((resolve) => {
        let params = new HttpParams();

        // Append all procedure-id-list
        procedureIds.forEach(id => {
            params = params.append('procedure-id-list', id);
        });

        //const url = 'http://localhost:8091';
        const url = this.basepadURL
        console.log("url " + url);
        
        this.http.get(url + '/procedure/report-onl-payment/--detail-by-list/--export', {
            params,
            observe: 'response',
            responseType: 'blob'
        }).toPromise().then(res => {
            const blob = new Blob([res.body], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'});
            let filename = 'danh-sach-thu-tuc.xlsx';
            
            // Extract filename from response headers if available
            if (res.headers.get('content-disposition') != null) {
                filename = res.headers.get('content-disposition').split(';')[1].split('=')[1].replace(/"/g, '');
            }
            
            const blobUrl = URL.createObjectURL(blob);
            const xhr = new XMLHttpRequest();
            xhr.responseType = 'blob';

            xhr.onload = () => {
                const recoveredBlob = xhr.response;
                const reader = new FileReader();
                reader.onload = () => {
                    const base64data = reader.result.toString();
                    const anchor = document.createElement('a');
                    anchor.download = filename;
                    anchor.href = base64data;
                    anchor.click();
                };
                reader.readAsDataURL(recoveredBlob);
            };

            xhr.open('GET', blobUrl);
            xhr.send();
            resolve(true);
        }).catch(err => {
            if (err.status === 500) {
                const message = {
                    vi: 'Hệ thống tạm thời không thể xuất file excel!',
                    en: 'The system is temporarily unable to export the excel file!'
                };
                // Assuming you have snackbar service available
                // this.snackbar.openSnackBar(0, '', message[localStorage.getItem('language')], 'error_notification', this.config.expiredTime);
                console.log("error: " + err);
            }
            resolve(false);
        });
    });
}

  exportProcedureDataDetail(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');
    //return this.http.get('http://localhost:8091/procedure/report-dvc-onl-dni/--export' + searchString, { headers }).pipe();

    return this.http.get(this.basepadURL + '/procedure/report-dvc-onl-dni/--export' + searchString, { headers }).pipe();
  }

  exportProcedureStatisticOnlDNI(searchString): Promise<boolean> {
    return new Promise((resolve) => {
        //const url = 'http://localhost:8091';
        const url = this.basepadURL
      
        
        console.log("url " + url);
        
        this.http.get(url + '/procedure/report-dvc-onl-payment-dni/--export' + searchString, {
            observe: 'response',
            responseType: 'blob'
        }).toPromise().then(res => {
            const blob = new Blob([res.body], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'});
            let filename = 'bao-cao-chi-tiet-dni.xlsx';
            
            // Extract filename from response headers if available
            if (res.headers.get('content-disposition') != null) {
                filename = res.headers.get('content-disposition').split(';')[1].split('=')[1].replace(/"/g, '');
            }
            
            const blobUrl = URL.createObjectURL(blob);
            const xhr = new XMLHttpRequest();
            xhr.responseType = 'blob';

            xhr.onload = () => {
                const recoveredBlob = xhr.response;
                const reader = new FileReader();
                reader.onload = () => {
                    const base64data = reader.result.toString();
                    const anchor = document.createElement('a');
                    anchor.download = filename;
                    anchor.href = base64data;
                    anchor.click();
                };
                reader.readAsDataURL(recoveredBlob);
            };

            xhr.open('GET', blobUrl);
            xhr.send();
            resolve(true);
        }).catch(err => {
            if (err.status === 500) {
                const message = {
                    vi: 'Hệ thống tạm thời không thể xuất file excel!',
                    en: 'The system is temporarily unable to export the excel file!'
                };
                // Assuming you have snackbar service available
                // this.snackbar.openSnackBar(0, '', message[localStorage.getItem('language')], 'error_notification', this.config.expiredTime);
                console.log("error: " + err);
            }
            resolve(false);
        });
    });
}


  getDossierStatisticOnlineByAngency(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');
    //return this.http.get('http://localhost:8081/dni-online-payment-statistic/--by-agency' + searchString, { headers }).pipe();
    return this.http.get(this.padmanURL + '/dni-online-payment-statistic/--by-agency' + searchString, { headers }).pipe();
  }

  exportDossierDetail(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');
    //return this.http.get('http://localhost:8081/received-statistic/--dossier-static-detail/--export' + searchString, { headers }).pipe();
    return this.http.get(this.padmanURL + '/received-statistic/--dossier-static-detail/--export' + searchString, { headers }).pipe();
  }



  

  exportToExcelStatisticGeneralDetail(params: string): any {
    return new Promise((resolve) => {
      const url = this.padmanURL;
      //const url = "http://localhost:8081"
      console.log("url " + url);
      this.http.get(url + '/dni-online-payment-statistic/--dossier-static-detail/--export' + params, {
        observe: 'response',
        responseType: 'blob'
      }).toPromise().then(res => {
        const blob = new Blob([res.body], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'});
        let filename = 'bao_cao_chi_tiet.xlsx';
        if (res.headers.get('content-disposition') != null) {
          filename = res.headers.get('content-disposition').split(';')[1].split('=')[1].replace(/"/g, '');
        }
        const blobUrl = URL.createObjectURL(blob);
        const xhr = new XMLHttpRequest();
        xhr.responseType = 'blob';

        xhr.onload = () => {
          const recoveredBlob = xhr.response;
          const reader = new FileReader();
          reader.onload = () => {
            const base64data = reader.result.toString();
            const anchor = document.createElement('a');
            anchor.download = filename;
            anchor.href = base64data;
            anchor.click();
          };
          reader.readAsDataURL(recoveredBlob);
        };

        xhr.open('GET', blobUrl);
        xhr.send();
        resolve(true);
      }).catch(err => {
        if (err.status === 500) {
          const message = {
            vi: 'Hệ thống tạm thời không thể xuất file excel!',
            en: 'The system is temporarily unable to export the excel file!'
          };
          this.snackbar.openSnackBar(0, '', message[localStorage.getItem('language')], 'error_notification', this.config.expiredTime);
          console.log("error: " + err)
        }
        resolve(false);
      });
    });
  }

  exportToExcelStatisticGeneralDetailES(params: string): any {
    return new Promise((resolve) => {
      this.http.get(this.statisticsURL + '/qni-dossier-statistic/--detail/--export' + params, {
        observe: 'response',
        responseType: 'blob'
      }).toPromise().then(res => {
        const blob = new Blob([res.body], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'});
        let filename = 'bao_cao_chi_tiet.xlsx';
        if (res.headers.get('content-disposition') != null) {
          filename = res.headers.get('content-disposition').split(';')[1].split('=')[1].replace(/"/g, '');
        }
        const blobUrl = URL.createObjectURL(blob);
        const xhr = new XMLHttpRequest();
        xhr.responseType = 'blob';

        xhr.onload = () => {
          const recoveredBlob = xhr.response;
          const reader = new FileReader();
          reader.onload = () => {
            const base64data = reader.result.toString();
            const anchor = document.createElement('a');
            anchor.download = filename;
            anchor.href = base64data;
            anchor.click();
          };
          reader.readAsDataURL(recoveredBlob);
        };

        xhr.open('GET', blobUrl);
        xhr.send();
        resolve(true);
      }).catch(err => {
        if (err.status === 500) {
          const message = {
            vi: 'Hệ thống tạm thời không thể xuất file excel!',
            en: 'The system is temporarily unable to export the excel file!'
          };
          this.snackbar.openSnackBar(0, '', message[localStorage.getItem('language')], 'error_notification', this.config.expiredTime);
        }
        resolve(false);
      });
    });
  }

  getRootAgency(agencyId: string): any {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');

    return this.http.get(this.basedataURL + `/qni-agency/${agencyId}/--find-root`, {headers}).pipe();
  }
  
  getRootAgencies(searchString: string): any {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');

    return this.http.get(this.basedataURL + '/agency/--find-root' + searchString, {headers}).pipe();
  }

  

  exportToExcelStatisticOnline(
    reportHeading: string,
    reportSubHeading: string,
    nameReport: string,
    subNameReport: string,
    json: any[],
    footerData: any[],
    excelFileName: string,
    sheetName: string
  ) {
    const workbook = new Workbook();
    workbook.creator = 'User';
    workbook.lastModifiedBy = 'User';
    workbook.created = new Date();
    workbook.modified = new Date();
  
    const worksheet = workbook.addWorksheet(sheetName);
  
    // Set standard font and column widths
    const columnKeys = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I'];
    columnKeys.forEach((col) => {
      worksheet.getColumn(col).font = { name: 'Times New Roman', size: 12 };
    });
  
    worksheet.getColumn('A').width = 5;   // STT
    worksheet.getColumn('B').width = 30;  // Đơn vị
    worksheet.getColumn('C').width = 15;  // Số TTHC có phí, lệ phí, NVTC
    worksheet.getColumn('D').width = 20;  // Số TTHC có phát sinh giao dịch trực tuyến
    worksheet.getColumn('E').width = 20;  // Tỷ lệ TTHC có giao dịch trực tuyến
    worksheet.getColumn('F').width = 15;  // Số hồ sơ có phí, lệ phí, NVTC
    worksheet.getColumn('G').width = 15;  // Số hồ sơ có phát sinh giao dịch trực tuyến
    worksheet.getColumn('H').width = 20;  // Tỷ lệ hồ sơ có giao dịch trực tuyến
    worksheet.getColumn('I').width = 20;  // Ghi chú
  
    // Title and date
    worksheet.mergeCells('A1:I1');
    worksheet.getCell('A1').value = reportHeading;
    worksheet.getCell('A1').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getCell('A1').font = { size: 13, bold: true, name: 'Times New Roman' };
  
    worksheet.mergeCells('A3:I3');
    worksheet.getCell('A3').value = reportSubHeading;
    worksheet.getCell('A3').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getCell('A3').font = { size: 12, italic: true, name: 'Times New Roman' };
  
    worksheet.addRow([]);
  
    // Header rows with hardcoded values
    const headerRow1 = worksheet.addRow([
      'STT', 'Đơn vị', 
      'Thống kê theo số lượng TTHC', '', '', 
      'Thống kê theo số lượng hồ sơ', '', '', 
      'Ghi chú'
    ]);
  
    const headerRow2 = worksheet.addRow([
      '', '', 
      'Số TTHC có phí, lệ phí, NVTC', 
      'Số TTHC có phát sinh giao dịch trực tuyến', 
      'Tỷ lệ TTHC có giao dịch trực tuyến', 
      'Số hồ sơ có phí, lệ phí, NVTC', 
      'Số hồ sơ có phát sinh giao dịch trực tuyến', 
      'Tỷ lệ hồ sơ có giao dịch trực tuyến', 
      ''
    ]);
  
    const headerRow3 = worksheet.addRow([
      '(1)', '(2)', '(3)', '(4)', '(5) = (4/3)*100', '(6)', '(7)', '(8) = (7/6)*100', '(9)'
    ]);
  
    // Merge cells for the main headers
    worksheet.mergeCells('A5:A6'); // STT
    worksheet.mergeCells('B5:B6'); // Đơn vị
    worksheet.mergeCells('C5:E5'); // Thống kê theo số lượng TTHC
    worksheet.getCell('C5').value = 'Thống kê theo số lượng TTHC';
    worksheet.mergeCells('F5:H5'); // Thống kê theo số lượng hồ sơ
    worksheet.getCell('F5').value = 'Thống kê theo số lượng hồ sơ';
    worksheet.mergeCells('I5:I6'); // Ghi chú
    worksheet.getCell('I5').value = 'Ghi chú';
  
    // Style the header rows
    [headerRow1, headerRow2, headerRow3].forEach(row => {
      row.eachCell(cell => {
        cell.font = { size: 12, bold: true, name: 'Times New Roman' };
        cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
        cell.border = { top: { style: 'thin' }, left: { style: 'thin' }, bottom: { style: 'thin' }, right: { style: 'thin' } };
        cell.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: 'FFFFE0' },
          bgColor: { argb: 'FFFFE0' }
        };
      });
    });
  
    // Data rows
    json.forEach(item => {
      const dataRow = worksheet.addRow([
        item.stt, // STT
        item.agency, // Đơn vị
        item.numberOfPayProcedure, // Số TTHC có phí, lệ phí, NVTC
        item.onlinePaymentProcedure, // Số TTHC có phát sinh giao dịch trực tuyến
        (item.numberOfPayProcedure ? (item.onlinePaymentProcedure / item.numberOfPayProcedure) * 100 : 0) / 100, // Tỷ lệ TTHC có giao dịch trực tuyến
        item.hasPayment, // Số hồ sơ có phí, lệ phí, NVTC
        item.hasOnlinePayment, // Số hồ sơ có phát sinh giao dịch trực tuyến
        (item.hasPayment ? (item.hasOnlinePayment / item.hasPayment) * 100 : 0) / 100, // Tỷ lệ hồ sơ có giao dịch trực tuyến
        '' // Ghi chú (no data provided)
      ]);
  
      const rowNum = dataRow.number;
  
      dataRow.eachCell((cell, colNumber) => {
        cell.font = { size: 12, name: 'Times New Roman' };
        cell.border = { top: { style: 'thin' }, left: { style: 'thin' }, bottom: { style: 'thin' }, right: { style: 'thin' } };
        // Apply percentage formatting for columns E (5) and H (8)
        if ([5, 8].includes(colNumber)) {
          cell.numFmt = '0.00%';
        }
      });
  
      worksheet.getCell(`B${rowNum}`).alignment = { horizontal: 'left', vertical: 'middle', wrapText: true };
      worksheet.getCell(`I${rowNum}`).alignment = { horizontal: 'left', vertical: 'middle', wrapText: true };
      ['A', 'C', 'D', 'E', 'F', 'G', 'H'].forEach(col => {
        worksheet.getCell(`${col}${rowNum}`).alignment = { horizontal: 'center', vertical: 'middle' };
      });
    });
  
    // Footer
    let footerRowIndex = 0;
  
    if (footerData && footerData.length > 0) {
      const lastDataRow = worksheet.lastRow?.number || 8;
      footerRowIndex = lastDataRow + 1;
      const footerRow = worksheet.addRow([
        footerData[0][0], // "Tổng cộng"
        '', // Đơn vị (merged with STT)
        footerData[0][2], // totalNumberOfPayProcedure
        footerData[0][3], // totalOnlinePaymentProcedure  
        footerData[0][4] / 100, // totalPercentageProcedure (convert to decimal)
        footerData[0][5], // totalHasPayment
        footerData[0][6], // totalHasOnlinePayment
        footerData[0][7] / 100, // totalPercentageDossier (convert to decimal)
        footerData[0][8] // Ghi chú (empty)
      ]);
  
      worksheet.mergeCells(`A${footerRowIndex}:B${footerRowIndex}`);
  
      footerRow.eachCell((cell, colNumber) => {
        cell.font = { size: 13, bold: true, name: 'Times New Roman' };
        cell.border = { top: { style: 'thin' }, left: { style: 'thin' }, bottom: { style: 'thin' }, right: { style: 'thin' } };
        cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
        if ([5, 8].includes(colNumber)) {
          cell.numFmt = '0.00%';
        }
      });
    }
  
    // Notes row
    const noteRowIndex = footerRowIndex > 0 ? footerRowIndex + 2 : (worksheet.lastRow?.number || 8) + 2;
    worksheet.mergeCells(`A${noteRowIndex}:I${noteRowIndex}`);
    worksheet.getCell(`A${noteRowIndex}`).font = { size: 10, italic: true, name: 'Times New Roman' };
    worksheet.getCell(`A${noteRowIndex}`).alignment = { horizontal: 'left', vertical: 'middle', wrapText: true };
  
    // Export the file
    workbook.xlsx.writeBuffer().then((dataBuffer: ArrayBuffer) => {
      const blob = new Blob([dataBuffer], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      });
      fs.saveAs(blob, `${excelFileName}.xlsx`);
    });
  }
  
  extractDataForWorksheet(jsonData) {
    // Extract only these specific fields from each item and add BCCI
    return jsonData.map(item => {
      // Create array with the 3 values from JSON plus the BCCI field
      return [
        item.stt || 0,
        item.sectorName || '',    // agency name
        item.resolved || 0,       // First column: resolved
        item.resolvedOnline || 0, // Second column: resolvedOnline
        item.resolvedDirect || 0, // Third column: resolvedDirect
        0,                         // Fourth column: BCCI (always 0)
        ''                          // note
      ];
    });
  }

  public exportToExcelStatisticGeneral(
    reportHeading: string,
    reportSubHeading: string,
    nameReport: string,
    subNameReport: string,
    json: any[],
    footerData: any[],
    excelFileName: string,
    sheetName: string
  ) {
    const data = json;
    // create workbook and worksheet
    const workbook = new Workbook();
    workbook.creator = 'Snippet Coder';
    workbook.lastModifiedBy = 'SnippetCoder';
    workbook.created = new Date();
    workbook.modified = new Date();
    const worksheet = workbook.addWorksheet(sheetName);

    worksheet.getColumn('A').font = {name: 'Times New Roman', size: 12};
    worksheet.getColumn('B').font = {name: 'Times New Roman', size: 12};
    worksheet.getColumn('C').font = {name: 'Times New Roman', size: 12};
    worksheet.getColumn('D').font = {name: 'Times New Roman', size: 12};
    worksheet.getColumn('E').font = {name: 'Times New Roman', size: 12};
    worksheet.getColumn('F').font = {name: 'Times New Roman', size: 12};
    worksheet.getColumn('G').font = {name: 'Times New Roman', size: 12};
    worksheet.getColumn('H').font = {name: 'Times New Roman', size: 12};
    worksheet.getColumn('I').font = {name: 'Times New Roman', size: 12};
    worksheet.getColumn('J').font = {name: 'Times New Roman', size: 12};
    worksheet.getColumn('K').font = {name: 'Times New Roman', size: 12};
    worksheet.getColumn('L').font = {name: 'Times New Roman', size: 12};
    worksheet.getColumn('M').font = {name: 'Times New Roman', size: 12};

    // Add header row
    worksheet.addRow([]);
    worksheet.mergeCells('A1:B1');
    worksheet.getCell('A1').value = nameReport;
    worksheet.getCell('A1').alignment = {horizontal: 'center', vertical: 'middle'};

    worksheet.mergeCells('A2:B2');
    worksheet.getCell('A2').value = subNameReport;
    worksheet.getCell('A2').alignment = {horizontal: 'center', vertical: 'middle'};

    worksheet.mergeCells('H1:M1');
    if (this.language === 228) {
      worksheet.getCell('H1').value = 'Đơn vị báo cáo: ';
    } else {
      worksheet.getCell('H1').value = 'Reporting unit: ';
    }
    worksheet.getCell('H1').alignment = {horizontal: 'left', vertical: 'middle'};
    worksheet.getCell('H1').font = {size: 12, bold: true, name: 'Times New Roman'};

    worksheet.mergeCells('H2:M4');
    if (this.language === 228) {
      worksheet.getCell('H2').value =
        '+UBND cấp xã, cơ quan chuyên môn thuộc UBND cấp huyện.\n+Cơ quan chuyên môn thuộc UBND cấp tỉnh.\n+Cơ quan, đơn vị trực thuộc bộ, cơ quan ngang bộ.\n+Cơ quan trung ương tổ chức theo ngành dọc cấp xã.';
    } else {
      worksheet.getCell('H2').value = '+Units under ministries, ministerial-level agencies;\n+Specialized Division of People\'s Committee of district / town / city ...;\n+Commune People\'s Committee';
    }
    worksheet.getRow(3).height = 33;
    worksheet.getRow(4).height = 33;

    worksheet.mergeCells('H5:M5');
    if (this.language === 228) {
      worksheet.getCell('H5').value = 'Đơn vị nhận báo cáo: ';
    } else {
      worksheet.getCell('H5').value = 'Unit receiving report: ';
    }
    worksheet.getCell('H5').alignment = {horizontal: 'left', vertical: 'middle'};
    worksheet.getCell('H5').font = {size: 12, bold: true, name: 'Times New Roman'};

    worksheet.mergeCells('H6:M7');
    if (this.language === 228) {
      worksheet.getCell('H6').value = '+UBND cấp huyện.\n+UBND cấp tỉnh.\n+Bộ, cơ quan ngang bộ.\n+Cơ quan trung ương tổ chức theo ngành dọc cấp huyện.';
    } else {
      worksheet.getCell('H6').value = '+Ministries, Ministerial-level unit \n(Office of ministries, ministerial-level agencies);\n+District People\'s Committee, Provincial People\'s Committee Office\n(Division of Administrative Procedure)';
    }
    worksheet.getCell('H6').alignment = {horizontal: 'left', vertical: 'middle'};
    worksheet.getRow(7).height = 70;

    worksheet.mergeCells('K9:M9');
    if (this.language === 228) {
      worksheet.getCell('K9').value = 'Đơn vị tính: Số hồ sơ TTHC.';
    } else {
      worksheet.getCell('K9').value = 'Unit: Number of dossiers.';
    }
    worksheet.mergeCells('C1:G4');
    worksheet.getCell('C1').value = reportHeading;
    worksheet.getCell('C1').alignment = {horizontal: 'center', vertical: 'middle'};
    worksheet.getCell('C1').font = {size: 15, bold: true, name: 'Times New Roman'};

    worksheet.mergeCells('C5:G5');
    if (this.language === 228) {
      worksheet.getCell('C5').value = 'Kỳ báo cáo: Quý.../Năm...';
    } else {
      worksheet.getCell('C5').value = 'Reporting period: Quarter.../year...';
    }
    worksheet.getCell('C5').alignment = {horizontal: 'center', vertical: 'middle'};
    worksheet.getCell('C5').font = {size: 12, name: 'Times New Roman'};

    worksheet.mergeCells('C6:G6');
    worksheet.getCell('C6').value = reportSubHeading;
    worksheet.getCell('C6').alignment = {horizontal: 'center', vertical: 'middle'};
    worksheet.getCell('C6').font = {size: 12, italic: true, name: 'Times New Roman'};

    // NỘI DUNG TABLE-HEADER
    worksheet.mergeCells('A10:A12');
    worksheet.mergeCells('B10:B12');
    worksheet.mergeCells('C10:F10');
    worksheet.mergeCells('G10:J10');
    worksheet.mergeCells('K10:M10');
    worksheet.mergeCells('N10:Q10');
    worksheet.mergeCells('C11:C12');
    worksheet.mergeCells('D11:E11');
    worksheet.mergeCells('F11:F12');
    worksheet.mergeCells('G11:G12');
    worksheet.mergeCells('H11:H12');
    worksheet.mergeCells('I11:I12');
    worksheet.mergeCells('J11:J12');
    worksheet.mergeCells('K11:K12');
    worksheet.mergeCells('L11:L12');
    worksheet.mergeCells('M11:M12');
    worksheet.mergeCells('N11:N12');
    worksheet.mergeCells('O11:O12');
    worksheet.mergeCells('P11:P12');
    worksheet.mergeCells('Q11:Q12');
    worksheet.mergeCells('R10:R12');

    worksheet.getCell('A13').value = '(1)';
    worksheet.getCell('B13').value = '(2)';
    worksheet.getCell('C13').value = '(3)=(4)+(5)+(6)';
    worksheet.getCell('D13').value = '(4)';
    worksheet.getCell('E13').value = '(5)';
    worksheet.getCell('F13').value = '(6)';
    worksheet.getCell('G13').value = '(7)=(8)+(9)+(10)';
    worksheet.getCell('H13').value = '(8)';
    worksheet.getCell('I13').value = '(9)';
    worksheet.getCell('J13').value = '(10)';
    worksheet.getCell('K13').value = '(11)=(12)+(13)';
    worksheet.getCell('L13').value = '(12)';
    worksheet.getCell('M13').value = '(13)';
    worksheet.getCell('N13').value = '(14)';
    worksheet.getCell('O13').value = '(15)';
    worksheet.getCell('P13').value = '(16)';
    worksheet.getCell('Q13').value = '(17)';
    worksheet.getCell('R13').value = '(18)';
    if (this.language === 228) {
      worksheet.getCell('A10').value = 'STT';
      worksheet.getCell('B10').value = 'Lĩnh vực giải quyết';
      worksheet.getCell('C10').value = 'Số lượng hồ sơ tiếp nhận';
      worksheet.getCell('G10').value = 'Số lượng hồ sơ đã giải quyết';
      worksheet.getCell('K10').value = 'Số lượng hồ sơ đang giải quyết';
      worksheet.getCell('N10').value = 'Số lượng hồ tiếp nhận chi tiết';
      worksheet.getCell('C11').value = 'Tổng số';
      worksheet.getCell('D11').value = 'Trong kỳ';
      worksheet.getCell('F11').value = 'Từ kỳ trước';
      worksheet.getCell('G11').value = 'Tổng số';
      worksheet.getCell('H11').value = 'Trước hạn';
      worksheet.getCell('I11').value = 'Đúng hạn';
      worksheet.getCell('J11').value = 'Quá hạn';
      worksheet.getCell('K11').value = 'Tổng số';
      worksheet.getCell('L11').value = 'Trong hạn';
      worksheet.getCell('M11').value = 'Quá hạn';
      worksheet.getCell('N11').value = 'Trực tiếp';
      worksheet.getCell('O11').value = 'Bưu chính';
      worksheet.getCell('P11').value = 'Bưu chính công ích';
      worksheet.getCell('Q11').value = 'Smartphone';
      worksheet.getCell('D12').value = 'Trực tuyến';
      worksheet.getCell('E12').value = 'Trực tiếp, dịch vụ bưu chính';
      worksheet.getCell('R10').value = 'Hồ sơ rút';
      
    } else {
      worksheet.getCell('A10').value = 'No.';
      worksheet.getCell('B10').value = 'Name of sector';
      worksheet.getCell('C10').value = 'Number of dossiers received';
      worksheet.getCell('G10').value = 'Number of dossiers resolved';
      worksheet.getCell('K10').value = 'Number of dossiers being resolved';
      worksheet.getCell('N10').value = 'Number of dossiers received detail';
      worksheet.getCell('C11').value = 'Total';
      worksheet.getCell('D11').value = 'Current period';
      worksheet.getCell('F11').value = 'Previous period';
      worksheet.getCell('G11').value = 'Total';
      worksheet.getCell('H11').value = 'Early';
      worksheet.getCell('I11').value = 'On time';
      worksheet.getCell('J11').value = 'Out of date';
      worksheet.getCell('K11').value = 'Total';
      worksheet.getCell('L11').value = 'On time';
      worksheet.getCell('M11').value = 'Out of date';
      worksheet.getCell('N11').value = 'Direct';
      worksheet.getCell('O11').value = 'Postal';
      worksheet.getCell('P11').value = 'Public postal';
      worksheet.getCell('Q11').value = 'Smartphone';
      worksheet.getCell('D12').value = 'Online';
      worksheet.getCell('E12').value = 'Direct, postal service';
      worksheet.getCell('R10').value = 'Dossier for Withdraw';
    }

    worksheet.getColumn('B').width = 45;
    worksheet.getColumn('C').width = 17;
    worksheet.getColumn('E').width = 10;
    worksheet.getColumn('G').width = 17;
    worksheet.getColumn('K').width = 17;
    worksheet.getColumn('P').width = 15;
    worksheet.getColumn('Q').width = 15;
    worksheet.getRow(2).height = 27;
    worksheet.getColumn('C').alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
    worksheet.getColumn('D').alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
    worksheet.getColumn('E').alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
    worksheet.getColumn('F').alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
    worksheet.getColumn('G').alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
    worksheet.getColumn('H').alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
    worksheet.getColumn('I').alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
    worksheet.getColumn('J').alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
    worksheet.getColumn('K').alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
    worksheet.getColumn('L').alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
    worksheet.getColumn('M').alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
    worksheet.getColumn('N').alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
    worksheet.getColumn('O').alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
    worksheet.getColumn('P').alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
    worksheet.getColumn('Q').alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
    worksheet.getColumn('R').alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};

    worksheet.getCell('H1').style.alignment = {horizontal: 'left', vertical: 'middle', wrapText: true};
    worksheet.getCell('H2').style.alignment = {horizontal: 'left', vertical: 'middle', wrapText: true};
    worksheet.getCell('H5').style.alignment = {horizontal: 'left', vertical: 'middle', wrapText: true};
    worksheet.getCell('H6').style.alignment = {horizontal: 'left', vertical: 'middle', wrapText: true};

    worksheet.properties.outlineLevelCol = 2;
    worksheet.properties.defaultRowHeight = 15;

    let i = 10;
    const j = 13;
    for (i; i <= j; i++) {
      let k = 1;
      const l = 18;
      for (k; k <= l; k++) {
        worksheet.findCell(i, k).alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
        worksheet.findCell(i, k).fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: {argb: '00FFAB'},
          bgColor: {argb: '00FFAB'}
        };
        worksheet.findCell(i, k).border = {top: {style: 'thin'}, left: {style: 'thin'}, bottom: {style: 'thin'}, right: {style: 'thin'}};
        worksheet.findCell(i, k).font = {size: 12, bold: true, name: 'Times New Roman'};
      }
    }

// This function extracts the three fields and adds the BCCI field


// Main code to use in your Excel generation
const extractedRows = this.extractDataForWorksheet(data);
console.log("extractedRows: " + extractedRows);

// Add each row to the worksheet
extractedRows.forEach(rowData => {
  const borderrow = worksheet.addRow(rowData);
  borderrow.eachCell((cell) => {
    cell.border = {top: {style: 'thin'}, left: {style: 'thin'}, bottom: {style: 'thin'}, right: {style: 'thin'}};
    cell.alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
  });
});

const dataLength = extractedRows.length;
if (dataLength > 0) {
  for (let i = 0; i < dataLength; i++) {
    // Note: If the left-aligned field is still needed
    worksheet.getCell('B' + (14 + i)).alignment = {horizontal: 'left', vertical: 'middle', wrapText: true};
  }
}

    // footer data row
    if (footerData != null) {
      footerData.forEach((element: any) => {
        const eachRow = [];
        element.forEach((val: any) => {
          eachRow.push(val);
        });
        const footerRow = worksheet.addRow(eachRow);
        const cellMerge = 'A' + footerRow.number + ':B' + footerRow.number;
        worksheet.mergeCells(cellMerge);
        footerRow.eachCell((cell) => {
          cell.font = {size: 13, bold: true, name: 'Times New Roman'};
          cell.border = {top: {style: 'thin'}, left: {style: 'thin'}, bottom: {style: 'thin'}, right: {style: 'thin'}};
          cell.alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
        });
      });
    }

    // Save Excel File
    workbook.xlsx.writeBuffer().then((dataBuffer: ArrayBuffer) => {
      const blob = new Blob([dataBuffer], {type: EXCEL_TYPE});
      fs.saveAs(blob, excelFileName + EXCEL_EXTENSION);
    });
  }

  public exportToExcelStatisticOnlinePayment(
    reportHeading: string,
    reportSubHeading: string,
    nameReport: string,
    subNameReport: string,
    data: any[],
    excelFileName: string,
    sheetName: string
  ) {
    // create workbook and worksheet
    const workbook = new Workbook();
    workbook.creator = 'Snippet Coder';
    workbook.lastModifiedBy = 'SnippetCoder';
    workbook.created = new Date();
    workbook.modified = new Date();
    const worksheet = workbook.addWorksheet(sheetName);

    worksheet.getColumn('A').font = {name: 'Times New Roman', size: 12};
    worksheet.getColumn('B').font = {name: 'Times New Roman', size: 12};
    worksheet.getColumn('C').font = {name: 'Times New Roman', size: 12};
    worksheet.getColumn('D').font = {name: 'Times New Roman', size: 12};
    worksheet.getColumn('E').font = {name: 'Times New Roman', size: 12};
    worksheet.getColumn('F').font = {name: 'Times New Roman', size: 12};
    worksheet.getColumn('G').font = {name: 'Times New Roman', size: 12};
    worksheet.getColumn('H').font = {name: 'Times New Roman', size: 12};
    worksheet.getColumn('I').font = {name: 'Times New Roman', size: 12};
    worksheet.getColumn('J').font = {name: 'Times New Roman', size: 12};
    worksheet.getColumn('K').font = {name: 'Times New Roman', size: 12};

    // Add header row
    worksheet.addRow([]);
    worksheet.mergeCells('A1:B1');
    worksheet.getCell('A1').value = nameReport;
    worksheet.getCell('A1').alignment = {horizontal: 'center', vertical: 'middle'};

    worksheet.mergeCells('A2:B2');
    worksheet.getCell('A2').value = subNameReport;
    worksheet.getCell('A2').alignment = {horizontal: 'center', vertical: 'middle'};

    worksheet.mergeCells('H1:M1');
    if (this.language === 228) {
      worksheet.getCell('H1').value = 'Đơn vị báo cáo: ';
    } else {
      worksheet.getCell('H1').value = 'Reporting unit: ';
    }
    worksheet.getCell('H1').alignment = {horizontal: 'left', vertical: 'middle'};
    worksheet.getCell('H1').font = {size: 12, bold: true, name: 'Times New Roman'};

    worksheet.mergeCells('H2:M4');
    if (this.language === 228) {
      worksheet.getCell('H2').value =
        '+UBND cấp xã, cơ quan chuyên môn thuộc UBND cấp huyện.\n+Cơ quan chuyên môn thuộc UBND cấp tỉnh.\n+Cơ quan, đơn vị trực thuộc bộ, cơ quan ngang bộ.\n+Cơ quan trung ương tổ chức theo ngành dọc cấp xã.';
    } else {
      worksheet.getCell('H2').value = '+Units under ministries, ministerial-level agencies;\n+Specialized Division of People\'s Committee of district / town / city ...;\n+Commune People\'s Committee';
    }
    worksheet.getRow(3).height = 33;
    worksheet.getRow(4).height = 33;

    worksheet.mergeCells('H5:M5');
    if (this.language === 228) {
      worksheet.getCell('H5').value = 'Đơn vị nhận báo cáo: ';
    } else {
      worksheet.getCell('H5').value = 'Unit receiving report: ';
    }
    worksheet.getCell('H5').alignment = {horizontal: 'left', vertical: 'middle'};
    worksheet.getCell('H5').font = {size: 12, bold: true, name: 'Times New Roman'};

    worksheet.mergeCells('H6:M7');
    if (this.language === 228) {
      worksheet.getCell('H6').value = '+UBND cấp huyện.\n+UBND cấp tỉnh.\n+Bộ, cơ quan ngang bộ.\n+Cơ quan trung ương tổ chức theo ngành dọc cấp huyện.';
    } else {
      worksheet.getCell('H6').value = '+Ministries, Ministerial-level unit \n(Office of ministries, ministerial-level agencies);\n+District People\'s Committee, Provincial People\'s Committee Office\n(Division of Administrative Procedure)';
    }
    worksheet.getCell('H6').alignment = {horizontal: 'left', vertical: 'middle'};
    worksheet.getRow(7).height = 70;

    worksheet.mergeCells('J9:K9');
    if (this.language === 228) {
      worksheet.getCell('J9').value = 'Đơn vị tính: Số hồ sơ TTHC.';
    } else {
      worksheet.getCell('J9').value = 'Unit: Number of dossiers.';
    }
    worksheet.mergeCells('C1:G4');
    worksheet.getCell('C1').value = reportHeading;
    worksheet.getCell('C1').alignment = {horizontal: 'center', vertical: 'middle'};
    worksheet.getCell('C1').font = {size: 15, bold: true, name: 'Times New Roman'};

    worksheet.mergeCells('C5:G5');
    if (this.language === 228) {
      worksheet.getCell('C5').value = 'Kỳ báo cáo: Quý.../Năm...';
    } else {
      worksheet.getCell('C5').value = 'Reporting period: Quarter.../year...';
    }
    worksheet.getCell('C5').alignment = {horizontal: 'center', vertical: 'middle'};
    worksheet.getCell('C5').font = {size: 12, name: 'Times New Roman'};

    worksheet.mergeCells('C6:G6');
    worksheet.getCell('C6').value = reportSubHeading;
    worksheet.getCell('C6').alignment = {horizontal: 'center', vertical: 'middle'};
    worksheet.getCell('C6').font = {size: 12, italic: true, name: 'Times New Roman'};

    worksheet.getCell('A11').value = '(1)';
    worksheet.getCell('B11').value = '(2)';
    worksheet.getCell('C11').value = '(3)';
    worksheet.getCell('D11').value = '(4)=(5)+(6)';
    worksheet.getCell('E11').value = '(5)';
    worksheet.getCell('F11').value = '(6)';
    worksheet.getCell('G11').value = '(7)';
    worksheet.getCell('H11').value = '(8)';
    worksheet.getCell('I11').value = '(9)';
    worksheet.getCell('J11').value = '(10)=(7)/(4)';
    worksheet.getCell('K11').value = '(11)';
    if (this.language === 228) {
      worksheet.getCell('A10').value = 'STT';
      worksheet.getCell('B10').value = 'Cơ quan, địa phương';
      worksheet.getCell('C10').value = 'Tên thủ tục hành chính có yêu cầu nghĩa vụ tài chính';
      worksheet.getCell('D10').value = 'Tổng số hồ sơ có yêu cầu nghĩa vụ tài chính';
      worksheet.getCell('E10').value = 'Trực tiếp';
      worksheet.getCell('F10').value = 'Trực tuyến';
      worksheet.getCell('G10').value = 'Số hồ sơ có phát sinh thanh toán trực tuyến';
      worksheet.getCell('H10').value = 'Số TTHC có phát sinh thanh toán trực tuyến';
      worksheet.getCell('I10').value = 'Tỷ lệ TTHC có giao dịch thanh toán trực tuyến';
      worksheet.getCell('J10').value = 'Tỷ lệ hồ sơ thanh toán trực tuyến';
      worksheet.getCell('K10').value = 'Ghi chú';
    } else {
      worksheet.getCell('A10').value = 'STT';
      worksheet.getCell('B10').value = 'Cơ quan, địa phương';
      worksheet.getCell('C10').value = 'Tên thủ tục hành chính có yêu cầu nghĩa vụ tài chính';
      worksheet.getCell('D10').value = 'Tổng số hồ sơ có yêu cầu nghĩa vụ tài chính';
      worksheet.getCell('E10').value = 'Trực tiếp';
      worksheet.getCell('F10').value = 'Trực tuyến';
      worksheet.getCell('G10').value = 'Số hồ sơ có phát sinh thanh toán trực tuyến';
      worksheet.getCell('H10').value = 'Số TTHC có phát sinh thanh toán trực tuyến';
      worksheet.getCell('I10').value = 'Tỷ lệ TTHC có giao dịch thanh toán trực tuyến';
      worksheet.getCell('J10').value = 'Tỷ lệ hồ sơ thanh toán trực tuyến';
      worksheet.getCell('K10').value = 'Ghi chú';
    }

    worksheet.getColumn('B').width = 25;
    worksheet.getColumn('C').width = 40;
    worksheet.getColumn('D').width = 25;
    worksheet.getColumn('E').width = 15;
    worksheet.getColumn('F').width = 15;
    worksheet.getColumn('G').width = 25;
    worksheet.getColumn('H').width = 25;
    worksheet.getColumn('I').width = 25;
    worksheet.getColumn('J').width = 25;
    worksheet.getColumn('K').width = 20;


    worksheet.getRow(2).height = 27;
    worksheet.getColumn('C').alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
    worksheet.getColumn('D').alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
    worksheet.getColumn('E').alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
    worksheet.getColumn('F').alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
    worksheet.getColumn('G').alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
    worksheet.getColumn('H').alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
    worksheet.getColumn('I').alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
    worksheet.getColumn('J').alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
    worksheet.getColumn('K').alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};

    worksheet.getCell('H1').style.alignment = {horizontal: 'left', vertical: 'middle', wrapText: true};
    worksheet.getCell('H2').style.alignment = {horizontal: 'left', vertical: 'middle', wrapText: true};
    worksheet.getCell('H5').style.alignment = {horizontal: 'left', vertical: 'middle', wrapText: true};
    worksheet.getCell('H6').style.alignment = {horizontal: 'left', vertical: 'middle', wrapText: true};

    worksheet.properties.outlineLevelCol = 2;
    worksheet.properties.defaultRowHeight = 15;

    let i = 10;
    const j = 11;
    for (i; i <= j; i++) {
      let k = 1;
      const l = 11;
      for (k; k <= l; k++) {
        worksheet.findCell(i, k).alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
        worksheet.findCell(i, k).fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: {argb: 'E8E8E8'},
          bgColor: {argb: 'E8E8E8'}
        };
        worksheet.findCell(i, k).border = {top: {style: 'thin'}, left: {style: 'thin'}, bottom: {style: 'thin'}, right: {style: 'thin'}};
        worksheet.findCell(i, k).font = {size: 12, bold: true, name: 'Times New Roman'};
      }
    }

    data.forEach((element: any) => {
      const firstRowPerData = [element.agencyName, null, "Tổng số thủ tục: " + element.procedureUsingPayment, element.sumFinancialObligation, element.sumFinancialObligationDirect, element.sumFinancialObligationOnline,
         element.sumPaymentOnline, element.procedureUsingPaymentOnline, element.percentProcedureUsingPayment, element.percentPaymentOnline, ""
        ];
      const firstRow = worksheet.addRow(firstRowPerData);
      firstRow.height = 25;
      firstRow.eachCell((cell) => {
        cell.border = {top: {style: 'thin'}, left: {style: 'thin'}, bottom: {style: 'thin'}, right: {style: 'thin'}};
        cell.alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
        cell.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: 'E8E8E8' },
          bgColor: { argb: 'E8E8E8' }
        };
        cell.font = { bold: true };
      });
      worksheet.mergeCells(`A${firstRow.number}:B${firstRow.number}`);

      element.procedurePayment.forEach((proce : any) => {
        const eachRow = [proce.stt, proce.agencyName, proce.procedureName, proce.financialObligation, proce.financialObligationDirect, proce.financialObligationOnline,
           proce.paymentOnline, proce.procedureUsingPaymentOnline, proce.percentProcedureUsingPayment,
           proce.percentPaymentOnline, proce.note
        ];
        const childRow = worksheet.addRow(eachRow);
        childRow.eachCell((cell) => {
          cell.border = {top: {style: 'thin'}, left: {style: 'thin'}, bottom: {style: 'thin'}, right: {style: 'thin'}};
          cell.alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
        });
      })
    });

    // Save Excel File
    workbook.xlsx.writeBuffer().then((dataBuffer: ArrayBuffer) => {
      const blob = new Blob([dataBuffer], {type: EXCEL_TYPE});
      fs.saveAs(blob, excelFileName + EXCEL_EXTENSION);
    });
  }
  //Báo cáo chậm tiếp nhận QNI
  getSlowReceptionReport(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.get(this.reportURL + '/reception-report-qni' + searchString, { headers }).pipe();
  }
  getDetailSlowReceptionReportQNI(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.get(this.reportURL + '/reception-report-qni/--detail' + searchString, { headers }).pipe();
  }
  exportReportSlowReceptionDetail(searchString: string): any {
    return new Promise((resolve) => {
      this.http.get(this.reportURL + '/reception-report-qni/--detail/--export' + searchString, {
        observe: 'response',
        responseType: 'blob'
      }).toPromise().then(res => {
        const blob = new Blob([res.body], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'});
        let filename = 'bao_cao_chi_tiet.xlsx';
        if (res.headers.get('content-disposition') != null) {
          filename = res.headers.get('content-disposition').split(';')[1].split('=')[1].replace(/"/g, '');
        }
        const blobUrl = URL.createObjectURL(blob);
        const xhr = new XMLHttpRequest();
        xhr.responseType = 'blob';

        xhr.onload = () => {
          const recoveredBlob = xhr.response;
          const reader = new FileReader();
          reader.onload = () => {
            const base64data = reader.result.toString();
            const anchor = document.createElement('a');
            anchor.download = filename;
            anchor.href = base64data;
            anchor.click();
          };
          reader.readAsDataURL(recoveredBlob);
        };

        xhr.open('GET', blobUrl);
        xhr.send();
        resolve(true);
      }).catch(err => {
        if (err.status === 500) {
          const message = {
            vi: 'Hệ thống tạm thời không thể xuất file excel!',
            en: 'The system is temporarily unable to export the excel file!'
          };
          this.snackbar.openSnackBar(0, '', message[localStorage.getItem('language')], 'error_notification', this.config.expiredTime);
        }
        resolve(false);
      });
    });
  }
  

  public exportToExcelSlowReception(
    reportSubHeading: string,
    data: any[],
    footerData: any,
    excelFileName: string,
    sheetName: string
  ) {
    // create workbook and worksheet
    const workbook = new Workbook();
    workbook.creator = 'Snippet Coder';
    workbook.lastModifiedBy = 'SnippetCoder';
    workbook.created = new Date();
    workbook.modified = new Date();
    const worksheet = workbook.addWorksheet(sheetName);

    worksheet.getColumn('A').font = {name: 'Times New Roman', size: 12};
    worksheet.getColumn('B').font = {name: 'Times New Roman', size: 12};
    worksheet.getColumn('C').font = {name: 'Times New Roman', size: 12};
    worksheet.getColumn('D').font = {name: 'Times New Roman', size: 12};
    worksheet.getColumn('E').font = {name: 'Times New Roman', size: 12};
    worksheet.getColumn('F').font = {name: 'Times New Roman', size: 12};
    worksheet.getColumn('G').font = {name: 'Times New Roman', size: 12};
  

    // Add header row
    worksheet.addRow([]);

    worksheet.mergeCells('A1:G1');
    if (this.language === 228) {
      worksheet.getCell('A1').value = "Cộng hòa xã hội chủ nghĩa việt nam".toUpperCase();
    } else {
      worksheet.getCell('A1').value = "Socialist Republic of Vietnam".toUpperCase();
    }
    worksheet.getCell('A1').alignment = {horizontal: 'center', vertical: 'middle'};

    worksheet.mergeCells('A2:G2');
    if (this.language === 228) {
      worksheet.getCell('A2').value = "Độc lập - Tự do - Hạnh phúc";
    } else {
      worksheet.getCell('A2').value = "Independence - Freedom - Happiness";
    }
    worksheet.getCell('A2').alignment = {horizontal: 'center', vertical: 'middle'};

    worksheet.mergeCells('A4:G4');
    if (this.language === 228) {
      worksheet.getCell('A4').value = 'Danh mục hồ sơ TTHC trực tuyến chậm được tiếp nhận';
    } else {
      worksheet.getCell('A4').value = 'List of online administrative procedure files that are slow to be received';
    }
    worksheet.getCell('A4').alignment = {horizontal: 'center', vertical: 'middle'};
    worksheet.getCell('A4').font = {size: 12, bold: true, name: 'Times New Roman'};

    worksheet.mergeCells('A5:G5');
    worksheet.getCell('A5').value = reportSubHeading;
    worksheet.getCell('A5').alignment = {horizontal: 'center', vertical: 'middle'};
    worksheet.getCell('A5').font = {size: 12, italic: true, name: 'Times New Roman'};


    worksheet.getCell('A9').value = '(1)';
    worksheet.getCell('B9').value = '(2)';
    worksheet.getCell('C9').value = '(3)';
    worksheet.getCell('D9').value = '(4)=(5)+(6)';
    worksheet.getCell('E9').value = '(5)';
    worksheet.getCell('F9').value = '(6)';
    worksheet.getCell('G9').value = '(7)';

    worksheet.mergeCells('A7:A8');
    worksheet.mergeCells('B7:B8');
    worksheet.mergeCells('C7:C8');
    worksheet.mergeCells('D7:F7');
    worksheet.mergeCells('G7:G8');
   
    if (this.language === 228) {
      worksheet.getCell('A7').value = 'STT';
      worksheet.getCell('B7').value = 'Đơn vị';
      worksheet.getCell('C7').value = 'Thủ tục';
      worksheet.getCell('D7').value = 'Số lượng hồ sơ tiếp nhận';
      worksheet.getCell('D8').value = 'Tổng số';
      worksheet.getCell('E8').value = 'Số hồ sơ tiếp nhận đúng hạn';
      worksheet.getCell('F8').value = 'Số hồ sơ chậm được tiếp nhận';
      worksheet.getCell('G7').value = 'Ghi chú';
    } else {
      worksheet.getCell('A7').value = 'STT';
      worksheet.getCell('B7').value = 'Agency';
      worksheet.getCell('C7').value = 'Procedure';
      worksheet.getCell('D7').value = 'Number of dossier reception';
      worksheet.getCell('D8').value = 'Total';
      worksheet.getCell('E8').value = 'Number of dossier reception ontime';
      worksheet.getCell('F8').value = 'Number of dossier receprion late';
      worksheet.getCell('G7').value = 'Note';
    }

    worksheet.getColumn('A').width = 15;
    worksheet.getColumn('B').width = 25;
    worksheet.getColumn('C').width = 45;
    worksheet.getColumn('D').width = 20;
    worksheet.getColumn('E').width = 30;
    worksheet.getColumn('F').width = 30;
    worksheet.getColumn('G').width = 30;
   

    worksheet.getColumn('A').alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
    worksheet.getColumn('B').alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
    worksheet.getColumn('C').alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
    worksheet.getColumn('D').alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
    worksheet.getColumn('E').alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
    worksheet.getColumn('F').alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
    worksheet.getColumn('G').alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};


    worksheet.properties.outlineLevelCol = 2;
    worksheet.properties.defaultRowHeight = 15;

    let i = 7;
    const j = 9;
    for (i; i <= j; i++) {
      let k = 1;
      const l = 7;
      for (k; k <= l; k++) {
        worksheet.findCell(i, k).alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
        worksheet.findCell(i, k).fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: {argb: 'E8E8E8'},
          bgColor: {argb: 'E8E8E8'}
        };
        worksheet.findCell(i, k).border = {top: {style: 'thin'}, left: {style: 'thin'}, bottom: {style: 'thin'}, right: {style: 'thin'}};
        worksheet.findCell(i, k).font = {size: 12, bold: true, name: 'Times New Roman'};
      }
    };
    let currentRowIndex = 9;
    data.forEach((element: any) => {
      let startInsertRow = currentRowIndex + 1;
      let endInsertRow = currentRowIndex + element.procedureReception.length;
      if(element.procedureReception.length > 0){
        element.procedureReception.forEach((proce : any) => { 
          const eachRow = [proce.stt, "", proce.procedureName, proce.toTal, 
              proce.onTimeReception, proce.slowReception, proce.note
            ];
          const childRow = worksheet.addRow(eachRow);
          childRow.eachCell((cell) => {
            cell.border = {top: {style: 'thin'}, left: {style: 'thin'}, bottom: {style: 'thin'}, right: {style: 'thin'}};
            cell.alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
          });
          currentRowIndex++;
        })
        worksheet.mergeCells(`B${startInsertRow}:B${endInsertRow}`);
        worksheet.getCell(`B${startInsertRow}`).value = element.agencyName;
        worksheet.getCell(`B${startInsertRow}`).alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
      }
    });

    const footerIndex = currentRowIndex + 1;
    const footerRowData = [this.language === 228 ? "Tổng công" : "Sum", "", "", footerData.totalReception, footerData.totalReceptionOntime, footerData.totalReceptionLate, ""]
    const footerRow = worksheet.addRow(footerRowData);
    footerRow.eachCell((cell) => {
      cell.border = {top: {style: 'thin'}, left: {style: 'thin'}, bottom: {style: 'thin'}, right: {style: 'thin'}};
      cell.alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
      cell.font = { bold: true };
    });
    worksheet.mergeCells(`A${footerIndex}:B${footerIndex}`);
    worksheet.getCell(`A${footerIndex}`).value = this.language === 228 ? "Tổng công" : "Sum";

    // Save Excel File
    workbook.xlsx.writeBuffer().then((dataBuffer: ArrayBuffer) => {
      const blob = new Blob([dataBuffer], {type: EXCEL_TYPE});
      fs.saveAs(blob, excelFileName + EXCEL_EXTENSION);
    });
  }
  getListSectorsByAgency(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.basepadURL + '/sector/--by-agency'+ searchString, { headers }).pipe();
  }
  getListProcedureByAgency(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.basepadURL + '/procedure/--find-all-public'+ searchString, { headers }).pipe();
  }
  getListSectorsByAgencyQNI(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.basepadURL + '/sector/--by-agency-qni'+ searchString, { headers }).pipe();
  }
  getListProcedureByAgencyQNI(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.basepadURL + '/procedure/--find-all-procedure-qni'+ searchString, { headers }).pipe();
  }
  getTotalByAgencyQNI(search: String): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.basepadURL + '/qni-procedure/report-dvc-onl/--group-by-agency-qni' + search, { headers });
  }
  getReportTTPVKSTTHCByAgencyQNI(search: String): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.basepadURL + '/qni-procedure/report-TTPVKSTTHC/--group-by-agency-qni' + search, { headers });
  }
  getListProcedureFeeByAgencyQNI(search: String): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.basepadURL + '/qni-procedure/report-TTPVKSTTHC/--procedure-fee-and-form-qni' + search, { headers });
  }
  getSectorsByAgencyInProcedure(searchString): Observable<any> {
    return this.http.get(this.basepadURL + '/sector/--by-agency-procedure-qni' + searchString).pipe();
  }
  getProcedureStatisticOnlDetail(search): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.basepadURL + '/qni-procedure/report-dvc-onl-qni/--detail' + search, { headers }).pipe();
  }
  getProcedureReportTTPVKSTTHClDetail(search): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.basepadURL + '/qni-procedure/report-TTPVKSTTHC/--detail' + search, { headers }).pipe();
  }
  getProcedureOnlineReportTTPVKSTTHClDetail(search): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.padmanURL + '/qni-dossier-statistic-onl/report-TTPVKSTTHC/--detail--online' + search, { headers }).pipe();
  }
  getDossierDetail_ReportTTPVKSTTHC(search): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.padmanURL + '/qni-dossier-statistic-onl/report-TTPVKSTTHC/--dossier--detail' + search, { headers }).pipe();
  }
  getDossierStatisticOnlDetail(search): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.padmanURL + '/qni-dossier-statistic-onl/statistic-onl/--detail' + search, { headers }).pipe();
  }
  
  
  exportDossierStatisticOnlDetail(params: string): any {
    return new Promise((resolve) => {
        this.http.get(this.padmanURL + '/qni-dossier-statistic-onl/statistic-onl/--detail/--export' + params, {
            observe: 'response',
            responseType: 'blob'
        }).toPromise().then(res => {
            const blob = new Blob([res.body], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'});
            let filename = 'danh_sach_ho_so.xlsx';
            if (res.headers.get('content-disposition') != null) {
                filename = res.headers.get('content-disposition').split(';')[1].split('=')[1].replace(/"/g, '');
            }
            const blobUrl = URL.createObjectURL(blob);
            const xhr = new XMLHttpRequest();
            xhr.responseType = 'blob';

            xhr.onload = () => {
                const recoveredBlob = xhr.response;
                const reader = new FileReader();
                reader.onload = () => {
                    const base64data = reader.result.toString();
                    const anchor = document.createElement('a');
                    anchor.download = filename;
                    anchor.href = base64data;
                    anchor.click();
                };
                reader.readAsDataURL(recoveredBlob);
            };

            xhr.open('GET', blobUrl);
            xhr.send();
            resolve(true);
        }).catch(err => {
            if (err.status === 500) {
                const message = {
                    vi: 'Hệ thống tạm thời không thể xuất file excel!',
                    en: 'The system is temporarily unable to export the excel file!'
                };
                this.snackbar.openSnackBar(0, '', message[localStorage.getItem('language')], 'error_notification', this.config.expiredTime);
            }
            resolve(false);
        });
    });
  }
  exportDossier_ReportTTPVKSTTHC(params: string): any {
    return new Promise((resolve) => {
        this.http.get(this.padmanURL + '/qni-dossier-statistic-onl/report-TTPVKSTTHC/--dossier/--excel' + params, {
            observe: 'response',
            responseType: 'blob'
        }).toPromise().then(res => {
            const blob = new Blob([res.body], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'});
            let filename = 'danh_sach_ho_so.xlsx';
            if (res.headers.get('content-disposition') != null) {
                filename = res.headers.get('content-disposition').split(';')[1].split('=')[1].replace(/"/g, '');
            }
            const blobUrl = URL.createObjectURL(blob);
            const xhr = new XMLHttpRequest();
            xhr.responseType = 'blob';

            xhr.onload = () => {
                const recoveredBlob = xhr.response;
                const reader = new FileReader();
                reader.onload = () => {
                    const base64data = reader.result.toString();
                    const anchor = document.createElement('a');
                    anchor.download = filename;
                    anchor.href = base64data;
                    anchor.click();
                };
                reader.readAsDataURL(recoveredBlob);
            };

            xhr.open('GET', blobUrl);
            xhr.send();
            resolve(true);
        }).catch(err => {
            if (err.status === 500) {
                const message = {
                    vi: 'Hệ thống tạm thời không thể xuất file excel!',
                    en: 'The system is temporarily unable to export the excel file!'
                };
                this.snackbar.openSnackBar(0, '', message[localStorage.getItem('language')], 'error_notification', this.config.expiredTime);
            }
            resolve(false);
        });
    });
  }
  exportProcedureStatisticOnlDetail(params: string): any {
    return new Promise((resolve) => {
        this.http.get(this.basepadURL + '/procedure/report-dvc-onl-qni/--detail/--export' + params, {
            observe: 'response',
            responseType: 'blob'
        }).toPromise().then(res => {
            const blob = new Blob([res.body], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'});
            let filename = 'ds_tthcc_da_cung_cap.xlsx';
            if (res.headers.get('content-disposition') != null) {
                filename = res.headers.get('content-disposition').split(';')[1].split('=')[1].replace(/"/g, '');
            }
            const blobUrl = URL.createObjectURL(blob);
            const xhr = new XMLHttpRequest();
            xhr.responseType = 'blob';

            xhr.onload = () => {
                const recoveredBlob = xhr.response;
                const reader = new FileReader();
                reader.onload = () => {
                    const base64data = reader.result.toString();
                    const anchor = document.createElement('a');
                    anchor.download = filename;
                    anchor.href = base64data;
                    anchor.click();
                };
                reader.readAsDataURL(recoveredBlob);
            };

            xhr.open('GET', blobUrl);
            xhr.send();
            resolve(true);
        }).catch(err => {
            if (err.status === 500) {
                const message = {
                    vi: 'Hệ thống tạm thời không thể xuất file excel!',
                    en: 'The system is temporarily unable to export the excel file!'
                };
                this.snackbar.openSnackBar(0, '', message[localStorage.getItem('language')], 'error_notification', this.config.expiredTime);
            }
            resolve(false);
        });
    });
  }
  exportProcedureOnlineTTPVKSTTHCDetail(params: string): any {
    //debugger;
    return new Promise((resolve) => {
        this.http.get(this.padmanURL +  '/qni-dossier-statistic-onl/report-TTPVKSTTHC/--procedure/--excel' + params, {
            observe: 'response',
            responseType: 'blob'
        }).toPromise().then(res => {
          
            const blob = new Blob([res.body], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'});
            let filename = 'ds_tthcc_da_cung_cap.xlsx';
            if (res.headers.get('content-disposition') != null) {
                filename = res.headers.get('content-disposition').split(';')[1].split('=')[1].replace(/"/g, '');
            }
            const blobUrl = URL.createObjectURL(blob);
            const xhr = new XMLHttpRequest();
            xhr.responseType = 'blob';

            xhr.onload = () => {
                const recoveredBlob = xhr.response;
                const reader = new FileReader();
                reader.onload = () => {
                    const base64data = reader.result.toString();
                    const anchor = document.createElement('a');
                    anchor.download = filename;
                    anchor.href = base64data;
                    anchor.click();
                };
                reader.readAsDataURL(recoveredBlob);
            };

            xhr.open('GET', blobUrl);
            xhr.send();
            resolve(true);
        }).catch(err => {
            if (err.status === 500) {
                const message = {
                    vi: 'Hệ thống tạm thời không thể xuất file excel!',
                    en: 'The system is temporarily unable to export the excel file!'
                };
                this.snackbar.openSnackBar(0, '', message[localStorage.getItem('language')], 'error_notification', this.config.expiredTime);
            }
            resolve(false);
        });
    });
  }
  exportProcedureTTPVKSTTHCDetail(params: string): any {
    return new Promise((resolve) => {
        this.http.get(this.basepadURL + '/procedure/report-TTPVKSTTHC-qni/--procedure/--export' + params, {
            observe: 'response',
            responseType: 'blob'
        }).toPromise().then(res => {
            const blob = new Blob([res.body], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'});
            let filename = 'ds_tthcc_da_cung_cap.xlsx';
            if (res.headers.get('content-disposition') != null) {
                filename = res.headers.get('content-disposition').split(';')[1].split('=')[1].replace(/"/g, '');
            }
            const blobUrl = URL.createObjectURL(blob);
            const xhr = new XMLHttpRequest();
            xhr.responseType = 'blob';

            xhr.onload = () => {
                const recoveredBlob = xhr.response;
                const reader = new FileReader();
                reader.onload = () => {
                    const base64data = reader.result.toString();
                    const anchor = document.createElement('a');
                    anchor.download = filename;
                    anchor.href = base64data;
                    anchor.click();
                };
                reader.readAsDataURL(recoveredBlob);
            };

            xhr.open('GET', blobUrl);
            xhr.send();
            resolve(true);
        }).catch(err => {
            if (err.status === 500) {
                const message = {
                    vi: 'Hệ thống tạm thời không thể xuất file excel!',
                    en: 'The system is temporarily unable to export the excel file!'
                };
                this.snackbar.openSnackBar(0, '', message[localStorage.getItem('language')], 'error_notification', this.config.expiredTime);
            }
            resolve(false);
        });
    });
  }
  getSectorsByAgencyInProcedureV2(searchString): Observable<any> {
    return this.http.get(this.basepadURL + '/qni-procedure/--all-by-agency-qni' + searchString).pipe();
  }
  getListAgencyByTagId(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');

    return this.http.get(this.basedataURL + '/qni-agency/--by-ancestor-id' + searchString, { headers }).pipe();
  }
  exportDossier_ReportDVCLT(params: string): any {
    return new Promise((resolve) => {
        this.http.get(this.padmanURL + '/dvclt-dossier/export-excel-qni' + params, {
            observe: 'response',
            responseType: 'blob'
        }).toPromise().then(res => {
            const blob = new Blob([res.body], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'});
            let filename = 'danh_sach_ho_so.xlsx';
            if (res.headers.get('content-disposition') != null) {
                filename = res.headers.get('content-disposition').split(';')[1].split('=')[1].replace(/"/g, '');
            }
            const blobUrl = URL.createObjectURL(blob);
            const xhr = new XMLHttpRequest();
            xhr.responseType = 'blob';

            xhr.onload = () => {
                const recoveredBlob = xhr.response;
                const reader = new FileReader();
                reader.onload = () => {
                    const base64data = reader.result.toString();
                    const anchor = document.createElement('a');
                    anchor.download = filename;
                    anchor.href = base64data;
                    anchor.click();
                };
                reader.readAsDataURL(recoveredBlob);
            };

            xhr.open('GET', blobUrl);
            xhr.send();
            resolve(true);
        }).catch(err => {
            if (err.status === 500) {
                const message = {
                    vi: 'Hệ thống tạm thời không thể xuất file excel!',
                    en: 'The system is temporarily unable to export the excel file!'
                };
                this.snackbar.openSnackBar(0, '', message[localStorage.getItem('language')], 'error_notification', this.config.expiredTime);
            }
            resolve(false);
        });
    });
  }

  //Báo cáo thanh toán trực tuyến Quảng Ngãi
  getPaymentReportQNI(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');

    return this.http.get(this.reportURL + '/payment-report-qni' + searchString, { headers }).pipe();
  }
  getDetailPaymentReportQNI(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');

    return this.http.get(this.reportURL + '/payment-report-qni/--detail' + searchString, { headers }).pipe();
  }
  exportToExcelReportPaymentOnlineQNI(searchString: string): any {
    return new Promise((resolve) => {
      this.http.get(this.reportURL + '/payment-report-qni/--detail/--export' + searchString, {
        observe: 'response',
        responseType: 'blob'
      }).toPromise().then(res => {
        const blob = new Blob([res.body], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'});
        let filename = 'bao_cao_chi_tiet.xlsx';
        if (res.headers.get('content-disposition') != null) {
          filename = res.headers.get('content-disposition').split(';')[1].split('=')[1].replace(/"/g, '');
        }
        const blobUrl = URL.createObjectURL(blob);
        const xhr = new XMLHttpRequest();
        xhr.responseType = 'blob';

        xhr.onload = () => {
          const recoveredBlob = xhr.response;
          const reader = new FileReader();
          reader.onload = () => {
            const base64data = reader.result.toString();
            const anchor = document.createElement('a');
            anchor.download = filename;
            anchor.href = base64data;
            anchor.click();
          };
          reader.readAsDataURL(recoveredBlob);
        };

        xhr.open('GET', blobUrl);
        xhr.send();
        resolve(true);
      }).catch(err => {
        if (err.status === 500) {
          const message = {
            vi: 'Hệ thống tạm thời không thể xuất file excel!',
            en: 'The system is temporarily unable to export the excel file!'
          };
          this.snackbar.openSnackBar(0, '', message[localStorage.getItem('language')], 'error_notification', this.config.expiredTime);
        }
        resolve(false);
      });
    });
  }

  // Báo cáo phục vụ cho tỉnh Quảng Ngãi
  private reportURL = this.apiProviderService.getUrl('digo', 'reporter');
  getReportQNI(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');

    return this.http.get(this.reportURL + '/general-report-qni' + searchString, { headers }).pipe();
  }

  getReportQNIDetail(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.get(this.reportURL + '/general-report-qni/--detail' + searchString, { headers }).pipe();
  }

  exportToExcelReportQNI(params: string): any {
    return new Promise((resolve) => {
      this.http.get(this.reportURL + '/general-report-qni/--detail/--export' + params, {
        observe: 'response',
        responseType: 'blob'
      }).toPromise().then(res => {
        const blob = new Blob([res.body], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'});
        let filename = 'bao_cao_chi_tiet.xlsx';
        if (res.headers.get('content-disposition') != null) {
          filename = res.headers.get('content-disposition').split(';')[1].split('=')[1].replace(/"/g, '');
        }
        const blobUrl = URL.createObjectURL(blob);
        const xhr = new XMLHttpRequest();
        xhr.responseType = 'blob';

        xhr.onload = () => {
          const recoveredBlob = xhr.response;
          const reader = new FileReader();
          reader.onload = () => {
            const base64data = reader.result.toString();
            const anchor = document.createElement('a');
            anchor.download = filename;
            anchor.href = base64data;
            anchor.click();
          };
          reader.readAsDataURL(recoveredBlob);
        };

        xhr.open('GET', blobUrl);
        xhr.send();
        resolve(true);
      }).catch(err => {
        if (err.status === 500) {
          const message = {
            vi: 'Hệ thống tạm thời không thể xuất file excel!',
            en: 'The system is temporarily unable to export the excel file!'
          };
          this.snackbar.openSnackBar(0, '', message[localStorage.getItem('language')], 'error_notification', this.config.expiredTime);
        }
        resolve(false);
      });
    });
  }
}