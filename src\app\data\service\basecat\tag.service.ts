import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { ApiProviderService } from 'src/app/core/service/api-provider.service';
import { EnvService } from 'src/app/core/service/env.service';

@Injectable({
    providedIn: 'root'
})
export class TagService {
    private basecat = this.apiProviderService.getUrl('digo', 'basecat');

    constructor(
        private http: HttpClient,
        private envService: EnvService,
        private apiProviderService: ApiProviderService,
    ) { }

    private getFullyDetailsUrl(id: string): string{
        return this.apiProviderService.getUrl('digo', 'basecat') + `/tag/${id}/--fully`;
    }

    public getFullyDetails(id: string) : Observable<any> {
        return this.http.get(this.getFullyDetailsUrl(id));
    }

    public getTagByTagCategory(id: string): Observable<any>{
        let headers = new HttpHeaders();
        headers = headers.set('Content-Type', 'application/json');
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        return this.http.get<any>(this.basecat + '/tag/--by-category-id?category-id=' + id, {headers});
    }
}
