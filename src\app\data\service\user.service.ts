import { KeycloakService } from 'keycloak-angular';
import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { ApiProviderService } from 'src/app/core/service/api-provider.service';
import { Observable, BehaviorSubject } from 'rxjs';
import jwt_decode from 'jwt-decode';
import { EnvService } from 'src/app/core/service/env.service';
import * as tUtils from 'src/app/data/service/thoai.service';
import {DeploymentService} from 'data/service/deployment.service';

@Injectable({
  providedIn: 'root'
})
export class UserService {

  config = this.envService.getConfig();
  integrateDigitalSignature = new BehaviorSubject<string>('');
  getCheckIntegrateDigitalSignature = this.integrateDigitalSignature.asObservable();

  // userInfo
  UID: any;
  userAgency = [];
  selectedOption: any = '';
  siteName: any;
  agencyName = '';
  accountId: any = '';
  userPermissions = [];

  constructor(
    private http: HttpClient,
    private keycloakService: KeycloakService,
    private apiProviderService: ApiProviderService,
    private envService: EnvService,
    private deploymentService: DeploymentService,
  ) { }
  private getUserInfoPath = this.apiProviderService.getUrl('digo', 'human') + '/user/';
  private getTempPath = this.apiProviderService.getUrl('digo', 'fileman') + '/file/';

  private getUserfilePath = this.apiProviderService.getUrl('digo', 'fileman') + '/file/';
  private basedata = this.apiProviderService.getUrl('digo', 'basedata');

  //covert file html
  downloadExport(params: string) {
    let headers = new HttpHeaders();
    headers = headers.set("Content-Type", "application/json");
    return this.http.get(this.getTempPath + "convert", { observe: 'response', responseType: 'blob' }).toPromise();
  }

  postHtml(requestBody: any): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'text/html');
    headers = headers.set('Accept', '*/*');
    headers = headers.set('Accept-Language', localStorage.getItem('language'))
    return this.http.post<any>(this.getTempPath + "convert", requestBody, { headers, observe: 'response', responseType: 'blob' as 'json' });
  }

  // module chữ ký
  getUserSigns(profileId) {
    return this.http.get<any>(this.getUserInfoPath + profileId + "/signturn");
  }

  getUserInfo(id: string): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.getUserInfoPath + id, { headers });
  }

  getUserInfoAsync(id: string): Promise<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.getUserInfoPath + id, { headers }).toPromise();
  }

  getUserSign(id): Observable<any> {
    const URL = `${this.getUserInfoPath}${id}/sign`;
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(URL, { headers });
  }

  getUserAvatar(id: string): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.append('Content-Type', 'application/json');
    return this.http.get(this.getUserfilePath + id, { headers, responseType: 'blob' as 'json' });
  }

  getFullUserInfo(id: string): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.getUserInfoPath + id + '/--fully', { headers });
  }

  getListUserFullnameExperience(searchString: string): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.getUserInfoPath + 'fullname+experience/' + searchString, { headers });
  }

  // delete
  getListUserFullnameExperienceOnlyAgency(searchString: string): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.getUserInfoPath + 'fullname+experience+onlyagency/' + searchString, { headers });
  }

  getListUserExperienceTreeViewV2(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.getUserInfoPath + '--experience-tree-view-v2?ancestor-id=' + id, { headers });
  }

  getUserExperience(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.getUserInfoPath + id + '/experience', { headers });
  }

  getAllUserByExperience(agencyId): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.getUserInfoPath + '--by-experience', { headers,params: {"agency-id": agencyId} });
  }

  getUserByIdentify(identify): Promise<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.getUserInfoPath + '/--get-id-by-identity/' + identify, { headers, responseType: 'text'}).toPromise();
  }

  getUserByIdentify_QBH(identify): Promise<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.getUserInfoPath + '/--get-id-by-identity-first/' + identify, { headers, responseType: 'text'}).toPromise();
  }
  
  getUserFully(id: string): Promise<any> {
    const headers = new HttpHeaders();
    return this.http
      .get(this.getUserInfoPath + id + '/--fully/', { headers })
      .toPromise();
  }

  getAgencyName(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.basedata + '/agency/' + id + '//name+code/--fully', { headers });
  }

  getCheckUserAdmin(searchStr): Observable<any>{
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get<any>(this.getUserInfoPath +'/--check-user-exists-by-agency'+ searchStr , { headers });
  }

  setUserToken() {
    this.keycloakService.getToken().then(token => {
      localStorage.setItem('userToken', token);
    });
  }

  getUserPermissions() {
    const decodedToken: any = jwt_decode(localStorage.getItem('userToken'));
    return decodedToken.permissions;
  }

  checkPermissionExists(permission) {
    const decodedToken: any = jwt_decode(localStorage.getItem('userToken'));
    const permissions = decodedToken.permissions;
    for (const p of permissions) {
      if (permission === p.permission.code) {
        return true;
      }
    }
    return false;
  }


  //check quyền kí số
  chanegPerIntegrateDigitalSignature(per) {
    this.integrateDigitalSignature.next(per);
  }

  getAgencyInfo(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.basedata + '/agency/' + id, { headers }).pipe();
  }

  async setUserAgency(): Promise<any> {
    let userId = '';
    await this.keycloakService.loadUserProfile().then(async user => {
      // tslint:disable-next-line: no-string-literal
      userId = user['attributes'].user_id;
    });

    const data = await this.getUserExperience(userId).toPromise();

    localStorage.setItem('listAgencyUser', JSON.stringify(data));
    let i = data.length;
    while (i--) {
        if (!!data[i]?.agency && !!data[i]?.agency?.id) {
            data[i].agency.nation = this.config.nation;
        }else{
            window.alert('Không lấy được thông tin đơn vị. Kiểm tra lại quá trình công tác của cán bộ!');
            window.location.href = '/';
        }
    }

    this.userAgency = data;
        this.siteName = data[0]?.agency?.name;

    if (data.length > 1) {
      this.selectedOption = data[0].agency.id;

      const userAgency = JSON.parse(localStorage.getItem('userAgency'));
      const userExperienceAgency = JSON.parse(localStorage.getItem('userExperienceAgency'));
      if (userAgency !== null) {
        this.siteName = userAgency.name;
        this.selectedOption = userAgency.id;
        let dataFilter = data.filter(f => f.agency.id === userAgency.id);
        if(userExperienceAgency !== null){
           dataFilter = data.filter(f => (f.agency.id === userAgency.id && f.position.id === userExperienceAgency?.position?.id));
        }
        if (dataFilter.length > 0) {
          await this.getAgencyInfo(userAgency.id).toPromise().then(res => {
              dataFilter[0].agency.tag = res.tag;
              localStorage.setItem('userAgency', JSON.stringify(dataFilter[0].agency));
              localStorage.setItem('levelAgency', res.level ? JSON.stringify(res.level) : null);
              localStorage.setItem('userExperienceAgency', JSON.stringify(dataFilter[0]));
          });
        }
      } else {
          const enableLoadprimaryAgency =  this.deploymentService.getAppDeployment()?.enableLoadprimaryAgency ? this.deploymentService.getAppDeployment()?.enableLoadprimaryAgency : false;
          if(enableLoadprimaryAgency){
              const primaryAgency = data.find(item => item.primary === true);
              this.selectedOption =  primaryAgency?.agency?.id  ? primaryAgency.agency.id : data[0].agency.id;
              await this.getAgencyInfo(this.selectedOption).toPromise().then(res => {
                  if (primaryAgency?.agency?.id){
                      primaryAgency.agency.tag = res.tag;
                      this.siteName = primaryAgency?.agency?.name;
                  }else {
                      data[0].agency.tag = res.tag;
                  }
                  localStorage.setItem('userAgency', JSON.stringify(primaryAgency?.agency?.id ? primaryAgency.agency : data[0].agency));
                  localStorage.setItem('userExperienceAgency', JSON.stringify(primaryAgency?.agency?.id ? primaryAgency : data[0]));
                  localStorage.setItem('levelAgency', res.level ? JSON.stringify(res.level) : null);
              });
          }else {
              this.selectedOption = data[0].agency.id;
              await this.getAgencyInfo(data[0].agency.id).toPromise().then(res => {
                  data[0].agency.tag = res.tag;
                  localStorage.setItem('userAgency', JSON.stringify(data[0].agency));
                  localStorage.setItem('userExperienceAgency', JSON.stringify(data[0]));
                  localStorage.setItem('levelAgency', res.level ? JSON.stringify(res.level) : null);
              });
          }
      }
    } else {
      if (data.length > 0) {
        localStorage.removeItem('userAgency');
        localStorage.removeItem('userExperienceAgency');
        // tslint:disable-next-line: max-line-length
        this.agencyName = !!data[0].agency?.parent && !!data[0].agency?.parent.name ? data[0].agency?.parent.name : data[0].agency.name;
        const userAgency = JSON.parse(localStorage.getItem('userAgency'));
        if (userAgency !== null) {
          this.siteName = userAgency.name;
          this.selectedOption = userAgency.id;
        } else {
          this.selectedOption = data[0].agency.id;
          await this.getAgencyInfo(data[0].agency.id).toPromise().then(res => {
            data[0].agency.tag = res.tag;
            localStorage.setItem('userAgency', JSON.stringify(data[0].agency));
            localStorage.setItem('userExperienceAgency', JSON.stringify(data[0]));
          });
        }
      }
    }
  }

  setUserPermission() {
    this.keycloakService.loadUserProfile().then(async user => {
      // tslint:disable-next-line: no-string-literal
      this.UID = user['attributes'].user_id;
      localStorage.setItem('UID', this.UID);

      // tslint:disable-next-line: no-string-literal
      this.accountId = user['attributes'].account_id[0];

      // tslint:disable-next-line: no-string-literal
      if (user['attributes'].permissions !== undefined && user['attributes'].permissions !== null) {
        // tslint:disable-next-line: no-string-literal
        const permissions = JSON.parse(user['attributes'].permissions);
        let i = permissions.length;
        while (i--) {
          if (!!permissions[i].permission) {
            this.userPermissions.push({
              code: permissions[i].permission.code
            });
          }
        }
        sessionStorage.setItem('userPermissions', this.userPermissions.toString());
      }
    });
  }

  //check user is admin
  async checkIsAdmin() {
    return new Promise<void>(async (resolve) => {
      try {
        const permissions = this.getUserPermissions();
        let isAdmin = false;
        localStorage.setItem('checkIsAdmin', 'false');
        for (const p of permissions) {
          if (p.permission.code === 'admin' || p.permission.code === 'oneGateAdminMaster') {
            isAdmin = true;
            localStorage.setItem('checkIsAdmin', 'true');
            break;
          }
        }
        if (isAdmin === true) {
          const searchStr = '?user-id=' + this.UID + '&agency-id=' + this.config?.rootAgency?.id;
          const data = await this.getCheckUserAdmin(searchStr).toPromise().then(data => {
            localStorage.setItem('superAdmin', 'true');
          }).catch(
            err => {
              localStorage.setItem('superAdmin', 'false');
            });
        }
      }
      catch {
        localStorage.setItem('superAdmin', 'false');
        localStorage.setItem('checkIsAdmin', 'false');
      }
      resolve();
    });
  }

  async getUserInfoData(): Promise<any> {
    this.setUserPermission();
    await this.setUserAgency();

    const listUserAgency = JSON.parse(localStorage.getItem('listAgencyUser'));
    const userAgency = JSON.parse(localStorage.getItem('userAgency'));
    const userExperience = listUserAgency.find(item => item.agency.id === userAgency.id);

    if (!userExperience || !userExperience.agency) {
      localStorage.removeItem('userAgency');
      if (!!listUserAgency && listUserAgency.length !== 0) {
        await this.getUserInfoData();
      }
    }

    await this.checkIsAdmin();
    const userInfo = {
      UID: this.UID,
      userAgency: this.userAgency,
      selectedOption: this.selectedOption,
      siteName: this.siteName,
      agencyName: this.agencyName,
      accountId: this.accountId,
      userPermissions: this.userPermissions
    };
    return userInfo;
  }

  hasPermisson(perm:string) {
    let check = false;
    const arrPerm = this.getUserPermissions() ?? [];
    arrPerm.forEach(p => {
      
      if (perm === (p?.permission?.code ?? "")) {
        check = true;
      } 
    });
    return check;
  }

}
