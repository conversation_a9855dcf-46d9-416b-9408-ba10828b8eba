import { SectorService } from './../../../../data/service/sector/sector.service';
import { AfterViewInit, ChangeDetectorRef, Component, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { FormGroup, Validators, FormControl } from '@angular/forms';
import { DossierSearchElement } from 'src/app/data/schema/dossier-search-element';
import { MatTableDataSource } from '@angular/material/table';
import { DossierService } from 'src/app/data/service/dossier/dossier.service';
import { ActivatedRoute, Router } from '@angular/router';
import { EnvService } from 'src/app/core/service/env.service';
import { DatePipe } from '@angular/common';
import { SelectionModel } from '@angular/cdk/collections';
import { SnackbarService } from 'src/app/data/service/snackbar/snackbar.service';
import { MatDialog } from '@angular/material/dialog';
import {
  CancelDossierComponent,
  CancelDossierDialogModel
} from 'src/app/modules/dossier/dialogs/cancel-dossier/cancel-dossier.component';
import {
  ProcessSelectionComponent,
  ConfirmProcessSelectionDialogModel
} from 'src/app/modules/dossier/pages/online-reception/dialogs/process-selection/process-selection.component';
import {
  DeleteDossierComponent,
  ConfirmDeleteDialogModel
} from 'src/app/modules/dossier/pages/processing/dialogs/delete-dossier/delete-dossier.component';
import { KeycloakService } from 'keycloak-angular';
import { UserService } from 'src/app/data/service/user.service';
import {
  UpdateHistoryComponent,
  UpdateHistoryDialogModel
} from 'src/app/modules/dossier/pages/processing/dialogs/update-history/update-history.component';
import {
  RefuseComponent,
  RefuseDialogModel
} from 'src/app/modules/dossier/dialogs/refuse/refuse.component';
import { MainService } from 'src/app/data/service/main/main.service';
import {
  AdditionalRequirementComponent,
  ConfirmAdditionalRequirementDialogModel
} from 'src/app/modules/dossier/dialogs/additional-requirement/additional-requirement.component';
import {
  AdditionalRequirementPaperComponent,
  ConfirmAdditionalRequirementPaperDialogModel
} from 'src/app/modules/dossier/dialogs/additional-requirement-paper/additional-requirement-paper.component';
import {
  ConfirmAdditionalRequiremenPaperAndPaymentDialogModel,
  AdditionalRequiremenPaperAndPaymentComponent } from 'src/app/modules/dossier/dialogs/additional-requirement-paper-and-payment/additional-requirement-paper-and-payment.component';

import { ProcedureService } from 'src/app/data/service/procedure/procedure.service';
import { ViewProcessDiagramComponent, ViewProcessDiagramDialogModel } from 'src/app/shared/components/view-process-diagram/view-process-diagram.component';
import { ReplaySubject, Subject } from 'rxjs';
import { MatSelect } from '@angular/material/select';
import { takeUntil } from 'rxjs/operators';
import { AdminLayoutNavComponent } from 'src/app/layouts/admin/admin-layout-nav/admin-layout-nav.component';
import { WithdrawComponent, WithdrawModel } from '../../dialogs/withdraw/withdraw.component';
import { DeploymentService } from 'src/app/data/service/deployment.service';
import { NotificationService } from 'src/app/data/service/notification.service';
import { ComboboxLazyLoadComponent } from 'src/app/shared/components/combobox-lazy-load/combobox-lazy-load.component';
import { PaymentRequestComponent, ConfirmPaymentRequestDialogModel } from '../../dialogs/payment-request/payment-request.component';
import { ChangeDirectPaymentComponent, ChangeDirectPaymentDialogModel } from '../../dialogs/change-direct-payment/change-direct-payment.component';
import * as tUtils from 'src/app/data/service/thoai.service';
import { ConfirmDirectlyPaymentNotificationDialogModel, DirectlyPaymentNotificationComponent } from '../../dialogs/directly-payment-notification/directly-payment-notification.component';
import { ppid } from 'process';
import { Console } from 'console';
import { BasecatService } from 'src/app/data/service/basecat/basecat.service';
import { CommonService } from 'src/app/data/service/common.service';
import { utils } from 'protractor';
import { authenticationStatus } from 'src/app/data/service/config.service';
import { ConfirmCancelSDialogModel, CancelSDossierComponent } from './dialogs/cancel-dossier/cancel-dossier.component';
import {NoticeDossierComponent} from "shared/components/notice-dossier/notice-dossier.component";
import {NotificationV2Service} from 'data/service/etl-data/notificationV2.service';
import { ConfirmRefundComponent, ConfirmRefundDialogModel } from '../../dialogs/confirm-refund/confirm-refund.component';
import { CloneDossierComponent, CloneDossierDialogModel } from './dialogs/clone-dossier/clone-dossier.component';
import { WithdrawQBHComponent, WithdrawQBHModel } from '../../dialogs/withdraw-qbh/withdraw-qbh.component';
import { ReturnAdditionalDossierComponent, ReturnAdditionalRequestDialogModel } from './dialogs/return-addional-dossier/return-additional-dossier.component';
import { UpdatePaymentMethodComponent, UpdatePaymentMethodDialogModel } from '../../dialogs/update-payment-method/update-payment-method.component';
import { Clipboard } from '@angular/cdk/clipboard';
@Component({
  selector: 'app-online-reception',
  templateUrl: './online-reception.component.html',
  styleUrls: ['./online-reception.component.scss']
})
export class OnlineReceptionComponent implements OnInit, AfterViewInit, OnDestroy {
  depConfig = this.deploymentService.getAppDeployment();
  config = this.envService.getConfig();
  env = this.deploymentService.getAppDeployment()?.env;
  fullTextKHA = this.deploymentService.getAppDeployment()?.fullTextKHA;
  userAgency = JSON.parse(localStorage.getItem('userAgency'));
  userExperienceAgency = JSON.parse(localStorage.getItem('userExperienceAgency'));
  userId = localStorage.getItem('UID');
  // tslint:disable-next-line:triple-equals
  isAdmin?: boolean = sessionStorage.getItem('oneGateAdminMaster') == '1';
  selectedLang: string;
  listNation = [];
  listProvince = [];
  listDistrict = [];
  listVillage = [];
  listProvince1 = [];
  listDistrict1 = [];
  listVillage1 = [];
  xpandStatus = false;
  isCheckedAll = false;
  timeOutGetListSector: any = null;
  timeOutGetListProcedure: any = null;
  pageTitle = {
    vi: `Tiếp nhận hồ sơ trực tuyến`,
    en: `Online dossier reception`
  };
  enableTooltip=  this.deploymentService.env.OS_HCM.showToolTipsHCM ;
  paymentLater = false;
  showStopProcessing = true;
  isShowResPerson = false;
  isShowSearchTaxCode =this.deploymentService.env?.OS_HCM?.isShowSearchTaxCode
  enablefindResPerson =this.deploymentService.env?.OS_HCM?.findResPerson?.enable;
  listAgencyFindResperon = this.deploymentService.env?.OS_HCM?.findResPerson?.agencyList;
  listAgencyStopProcessing=this.deploymentService.env?.OS_HCM?.listAgencyStopProcessing
  enableFilterSectorIs = this.deploymentService.env?.OS_HCM_SCT_LV?.isEnableFilter ? this.deploymentService.env?.OS_HCM_SCT_LV?.isEnableFilter : false;
  agencyUnitEnableFilterSector = this.deploymentService.env?.OS_HCM_SCT_LV?.listAgencyIdUnit ? this.deploymentService.env?.OS_HCM_SCT_LV?.listAgencyIdUnit : [];
  allowFilterAppliedDateOptimization = this.deploymentService.env?.OS_HCM.allowFilterAppliedDateOptimization
  allowFilterAppliedDateOptimizationCustom = this.deploymentService.env?.OS_HCM.allowFilterAppliedDateOptimizationCustom
  rangeLimitDossierOnlineReception = this.deploymentService.env.OS_HCM.allowFilterAppliedDateOptimization.rangeLimitDossierOnlineReception
  // IGATESUPP-89251: [iGate2.0][QNI] - Luồng xử lý hồ sơ dừng xử lý khi đang yêu cầu bổ sung - 26.06.2024
  enablePauseWhenAdditional = this.deploymentService.getAppDeployment()?.enablePauseWhenAdditional ? this.deploymentService.getAppDeployment()?.enablePauseWhenAdditional : false
  // IGATESUPP-122003: [iGate2.0][QNI] - Điều chỉnh lại hồ sơ Dừng xử lý - 08.04.2025
  processingFlowStopProcessingNAST = this.deploymentService.getAppDeployment()?.processingFlowStopProcessingNAST ? this.deploymentService.getAppDeployment()?.processingFlowStopProcessingNAST : 0;
  overDueAdditionalMenuRemindTaskId = this.deploymentService.getAppDeployment()?.overDueAdditionalMenuRemindTaskId ? this.deploymentService.getAppDeployment()?.overDueAdditionalMenuRemindTaskId : "669f1083e374be63429b632a";
  returnOverDueAdditionalMenuRemindTaskId = this.deploymentService.getAppDeployment()?.returnOverDueAdditionalMenuRemindTaskId ? this.deploymentService.getAppDeployment()?.returnOverDueAdditionalMenuRemindTaskId : "66aadee3e374be63429b645c";
  
   //IGATESUPP-118965
   enableShowAIClassifyDossier = this.deploymentService.getAppDeployment()?.showAIClassifyDossier ? (this.deploymentService.getAppDeployment().showAIClassifyDossier > 0 ? true : false) : false;
   listAgencyShowAIClassifyDossier = this.deploymentService.getAppDeployment()?.showAIClassifyDossierAgencyIds ? this.deploymentService.getAppDeployment().showAIClassifyDossierAgencyIds : [];
   listAgencyAdvanceSearch = this.deploymentService.getAppDeployment()?.listAgencyAdvanceSearch ? this.deploymentService.getAppDeployment().listAgencyAdvanceSearch : [];
   percentageValidShowAIClassifyDossier = this.deploymentService.getAppDeployment()?.showAIClassifyDossierPercentageValid ? this.deploymentService.getAppDeployment().showAIClassifyDossierPercentageValid : 0;
   isShowAIClassifyDossier = false;
   //IGATESUPP-118965

  //IGATESUPP-115968
  enableShowFormOfPayment = this.deploymentService.getAppDeployment()?.showFormOfPayment?.enable ? (this.deploymentService.getAppDeployment().showFormOfPayment.enable > 0 ? true : false) : false;
  methodPaymentListShowFormOfPayment= this.deploymentService.getAppDeployment()?.showFormOfPayment?.methodPaymentList ? this.deploymentService.getAppDeployment().showFormOfPayment.methodPaymentList : [];
  agencyIdsShowFormOfPayment= this.deploymentService.getAppDeployment()?.showFormOfPayment?.agencyIds ? this.deploymentService.getAppDeployment().showFormOfPayment.agencyIds : [];
  configWorkInterfaceKHA: boolean = this.deploymentService?.getAppDeployment()?.configWorkInterfaceKHA || false;
  enableChangePaymentMethodDossier= this.deploymentService.getAppDeployment()?.enableChangePaymentMethodDossier ? this.deploymentService.getAppDeployment().enableChangePaymentMethodDossier : 0;

  openEformAction = this.deploymentService.env.openEformAction;

  isShowFilterApplyDate = false;
  searchForm= new FormGroup({
    code: new FormControl(''),
    nationCode: new FormControl(''),
    identityNumber: new FormControl(''),
    applicantName: new FormControl(''),
    ownerFullname: new FormControl(''),
    sectorCtrl: new FormControl(''),
    searchSectorCtrl: new FormControl(),
    procedureCtrl: new FormControl(''),
    searchProcedureCtrl: new FormControl(),
    nation: new FormControl(''),
    province: new FormControl(''),
    district: new FormControl(''),
    village: new FormControl(''),
    acceptFrom: new FormControl(''),
    acceptTo: new FormControl(''),
    applyMethod: new FormControl(''),
    hasDutypaidCert: new FormControl(''),
    dossierStatus: new FormControl('0,1,3,12,15,14,17,18,19,20,21,22'),
    vnpostStatus: new FormControl(''),
    appliedFrom: new FormControl(''),
    appliedTo: new FormControl(''),
    applicantOrganization: new FormControl(''),
    sortCtrl: new FormControl(''),
    additionalRequestDeadline: new FormControl(''),
    payStatus: new FormControl(''),
    paymentMethod: new FormControl(''),
    taxCode :new FormControl(''),
    resPerson :new FormControl(''),
    dossierSeen: new FormControl(false),
    procedureNameCtrl: new FormControl(''),
    province1: new FormControl(''),
    district1: new FormControl(''),
    village1: new FormControl(''),
    remindCtrl: new FormControl(''),
  });
  // receivingDossierSeen = new FormGroup({
  //   dossierSeen: new FormControl(false)
  //   });
  // Sector infinity scroll with search
  private listSector: any[] = [];
  listSectorPage = 0;
  isFullListSector = false;
  searchSectorKeyword = '';
  sectorCtrl: FormControl = new FormControl();
  searchSectorCtrl: FormControl = new FormControl();
  protected sectors: any[] = this.listSector;
  public sectorFiltered: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  @ViewChild('sectorMatSelectInfiniteScroll', { static: true }) sectorMatSelectInfiniteScroll: MatSelect;

  // Procedure infinity scroll with search
  private listProcedure: any[] = [];
  listProcedurePage = 0;
  isFullListProcedure = false;
  searchProcedureKeyword = '';
  procedureCtrl: FormControl = new FormControl();
  searchProcedureCtrl: FormControl = new FormControl();
  protected procedures: any[] = this.listProcedure;
  public procedureFiltered: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  //tlqkhanh.hcm-IGATESUPP-68633
  allowShowOrganizationName = (tUtils.isAllowedAgency(localStorage,this.deploymentService.env?.OS_HCM?.isShowOrganizationName.agencyIds))? true : false;
  isShowOrganizationName = (this.deploymentService.env?.OS_HCM?.isShowOrganizationName.enable && this.allowShowOrganizationName) ? this.deploymentService.env?.OS_HCM?.isShowOrganizationName.enable : false;
  //endof tlqkhanh.hcm-IGATESUPP-68633
  @ViewChild('procedureMatSelectInfiniteScroll', { static: true }) procedureMatSelectInfiniteScroll: MatSelect;

  showSectorOnlineReception = this.deploymentService?.newConfigV2?.showSectorOnlineReception;
  enableFunctionAppraisalDossier = this.deploymentService?.functionAppraisalDossier ? this.deploymentService?.functionAppraisalDossier: false;
  protected onDestroy = new Subject<void>();
  // ================================================= Table
  showCreateSearchOwnerFullname = this.deploymentService.getAppDeployment()?.showCreateSearchOwnerFullname === 1;
  isOwnerFullname = this.env?.isOwnerFullname === 1;
  displayedColumnsQNI: string[] = ['stt', 'code', 'procedure', 'profileOwner', 'appliedDate', 'status', 'action'];
  displayedColumnsDefault: string[] = ['stt', 'code', 'procedure', 'applicant', 'appliedDate', 'status', 'action'];
  displayedColumnsQBH: string[] = ['stt', 'code', 'procedure', 'applicant', 'appliedDate', 'status', 'action'];
  displayedColumns: string[] =  this.env?.isOwnerFullname === 1 ? this.displayedColumnsQNI : this.displayedColumnsDefault;
  configColumTable: string[] = !!this.deploymentService?.getAppDeployment()?.displayedColumns?.hsChoTiepNhan ?
      Object.values(this.deploymentService?.getAppDeployment()?.displayedColumns?.hsChoTiepNhan) : this.displayedColumnsDefault;
  configDisplayColumns: boolean = this.deploymentService?.getAppDeployment()?.configDisplayColumns || false;
  ELEMENTDATA: DossierSearchElement[] = [];
  dataSource: MatTableDataSource<DossierSearchElement>;
  selection = new SelectionModel<any>(true, []);
  checkedDownloadFile = [];
  enableShowDirectlyPaymentStatus = false;
  //phucnh.it2-IGATESUPP-39497  
  //tlqkhanh.hcm-IGATESUPP-68633
  displayedColumnsHCM: string[] = this.isShowOrganizationName ? ['stt', 'code', 'procedure', 'applicant', 'organizationName', 'appliedDate', 'payStatus', 'status', 'action'] : ['stt', 'code', 'procedure', 'applicant', 'appliedDate', 'payStatus', 'status', 'action'];
  //endof tlqkhanh.hcm-IGATESUPP-68633  
  enableAddPayStatusColumn = this.deploymentService?.env?.OS_HCM?.enableAddPayStatusColumn;
  //IGATESUPP-51370: cho phép hiển thị ngày update hs của công dan ở menu chờ tiếp nhân site 1 cửa
  showUpdateDateOneGate = this.deploymentService?.env?.OS_HCM?.showUpdateDateOneGate;
  idDossierTaskStatus = this.deploymentService?.env?.OS_HCM?.idDossierTaskStatus;
  turnOnElementSearchOnlineReception = this.deploymentService?.env?.OS_HCM?.turnOnElementSearchOnlineReception;
  //end phucnh.it2-IGATESUPP-39497
  countResult = 0;
  countDirectlyPaymentDone =0;
  enableSearchBySector = this.deploymentService.env.OS_BDG.enableSearchBySector;
  showButtonDetailDBN = this.env?.showButtonDetailDBN ? this.env?.showButtonDetailDBN : false;
  identityNumber_Reception_HGI = this.deploymentService?.env?.OS_HGI?.identityNumber_Reception_HGI ? this.deploymentService?.env?.OS_HGI?.identityNumber_Reception_HGI : false;
  searchHighPerformenceHgi = this.deploymentService?.env?.OS_HGI?.searchHighPerformenceHgi ? this.deploymentService?.env?.OS_HGI?.searchHighPerformenceHgi : false;
  size = this.env?.OS_KTM?.defaultPageSize ? this.env?.OS_KTM?.defaultPageSize : 10;
  page = 1;
  pageIndex = 1;
  pgSizeOptions = this.config.pageSizeOptions;
  paramsQuery = {
    code: '',
    identity: '',
    applicant: '',
    page: '1',
    size: this.env?.OS_KTM?.defaultPageSize ? this.env?.OS_KTM?.defaultPageSize : 10,
    procedure: '',
    nation: '',
    sector: '',
    province: '',
    district: '',
    village: '',
    acceptFrom: '',
    acceptTo: '',
    remindId: '',
    appliedFrom: '',
    appliedTo: '',
    organization:'',
    sortId: '',
    additionalRequestDeadline: '',
    payStatus: '',
    paymentMethod: '',
    taxCode: '',
    resPerson: '',
    province1: '',
    district1: '',
    village1: '',
  };
  searchDomain = '';
  justRegistered: any;
  expandReminderMenu = true;
  remindId = '';
  listMenuRemind: any = [];
  listMenuRemindAdditionalRequirementDossier : any = [];
  listMenuRemindAddedDossier : any = [];
  lengthRemind = 0;
  enableShowAdditionalRequirements= this.deploymentService.env.OS_HCM.enableShowAdditionalRequirements;
  cancelRemindId = this.deploymentService.env.dossierMenuTaskRemind?.cancelDossier.id;
  dossierAddedId = this.deploymentService.env.dossierTaskStatus?.dossierAdded.id;
  listAgencyAllowShowAddressWhenHover = this.deploymentService.env.OS_HCM?.listAgencyAllowShowAddressWhenHover ? this.deploymentService.env.OS_HCM?.listAgencyAllowShowAddressWhenHover : [];
  flexGtSm4Column = 20;
  flexGtSm5Column = 16;
  flexGtSm = 20;
  isShowAddressWhenHover = false;
  isFFOTask = false;
  listVnpostStatus:any = [];

  listSectorId: any;
  listsectorSearch = [];
  listSectorUser = "";
  listAgencyAppliShowTooltipOrganization = this.deploymentService.env.OS_HCM?.listAgencyAppliShowTooltipOrganization ? this.deploymentService.env.OS_HCM?.listAgencyAppliShowTooltipOrganization : [];
  isShowTooltipOrganizationForAgency = false;
  hasDossierDeletePermission = false;
  checkProvineAdmin? = JSON.parse(localStorage.getItem('superAdmin'));
  oneGateDeleteDossier = sessionStorage.getItem('oneGateDeleteDossier');

  // OS KGG
  isSortBySubmissionDateOnlineReception = this.deploymentService.env.OS_KGG.isSortBySubmissionDateOnlineReception;
  isUserRQ = this.env?.OS_KGG?.isUserRQ ? this.env?.OS_KGG?.isUserRQ : false;
  isShowAgencyEnterprise = this.deploymentService.env.OS_HCM.showAgencyEnterprise;
  paginationType = this.deploymentService.env.paginationType;
  numberOfElements = 0;
  showContentAdditionalRequest = this.deploymentService.getAppDeployment()?.showContentAdditionalRequest || false;

  // IGATESUPP-33975-trankequang
  enableExportPageDossiersToExcelButton = this.deploymentService.env?.OS_HCM?.enableExportPageDossiersToExcel ? this.deploymentService.env?.OS_HCM?.enableExportPageDossiersToExcel : false;
  listDisplayingDossier = [];
  qbhlistlayout = this.deploymentService?.env?.OS_QBH?.qbhlistlayout ? this.deploymentService?.env?.OS_QBH?.qbhlistlayout : false;
  qbhlayoutformalities = this.deploymentService?.env?.OS_QBH?.qbhlayoutformalities ? this.deploymentService?.env?.OS_QBH?.qbhlayoutformalities : false;

  //Doi thong tin FullName và ContactPerson cho thu tuc Thong bao khuyen mai
  swapFullName2ContactPerson= this.deploymentService.env.swapFullName2ContactPersonForTBKM;

  renamerejectbtn = this.deploymentService?.env?.OS_QBH?.renamerejectbtn ? this.deploymentService?.env?.OS_QBH?.renamerejectbtn : false;
  showAppliedDateFilter = this.deploymentService?.env?.OS_HCM?.showAppliedDateFilter;
  showOrganizationInformation = this.env?.OS_HCM?.showOrganizationInformation ? this.env?.OS_HCM?.showOrganizationInformation : false;
  showDirectlyPaymentNotification = this.deploymentService?.env?.OS_HCM.showDirectlyPaymentNotification;
  checkNullData = 0;
  filterByPositionAgencyType = !!this.env?.filterByPositionAgencyType ? true : false;
  showAdditionalRequirementPaper = this.deploymentService.env.OS_HCM.showAdditionalRequirementPaper;
  //IGATESUPP-41626
  onlyDisplayNationCode = this.deploymentService.env.OS_HCM.hideNationCodeIfExist2TypeOfCode ? this.deploymentService.env.OS_HCM.hideNationCodeIfExist2TypeOfCode : false;
  isChangeDate =false
  // trankequang - IGATESUPP-37926 : add combobox to sort dossier
  arrSortType = [
    {id: 0, value : "Chưa chọn"},
    {id : 1, value : "Ngày nộp tăng dần"},
    {id : 2, value : "Ngày nộp giảm dần"}
  ];

  sortId = sessionStorage.getItem('dossierArrSortType') ? sessionStorage.getItem('dossierArrSortType') : '0';
  showSortTypeHCM = false;
  
  remindAll = this.deploymentService.getAppDeployment()?.listRemindPageOnlineReception;

  //IGATESUPP-53825
  showConfirmOnlinePayment = false;
  //IGATESUPP-59515
  showSearchListOfficerDossierSeen = false;
  showDossierQualifiedRecept = this.deploymentService.env.OS_HCM.showDossierQualifiedRecept ? this.deploymentService.env.OS_HCM.showDossierQualifiedRecept : false;

  chungThucDienTu = this.deploymentService.getAppDeployment()?.chungThucDienTu;
  showStatusCTDT = this.chungThucDienTu?.showStatus ? this.chungThucDienTu?.showStatus : 0;
  listAuthenticationStatus = this.chungThucDienTu?.authenticationStatus ? this.chungThucDienTu?.authenticationStatus : authenticationStatus;

  //IGATESUPP-40055
  showAdditionalRequirementDate = this.deploymentService.env?.OS_HCM?.showadditionalRequirementDate ? this.deploymentService.env?.OS_HCM?.showadditionalRequirementDate : false;
  //IGATESUPP-43844
  showListAdditionalRequest = this.deploymentService.env?.OS_HCM?.showListAdditionalRequest ? this.deploymentService.env?.OS_HCM?.showListAdditionalRequest : false;
  isDirectlyAdditionalRequirementDossier = false;
  isDirectlyisDirectlyAddedDossier = false;
  showSortComboBox = this.deploymentService.env?.OS_HCM?.showSortComboBox ? this.deploymentService.env.OS_HCM.showSortComboBox : 0;
  directlyPayment = this.deploymentService.env?.OS_HCM?.directlyPayment ?
    this.deploymentService.env?.OS_HCM?.directlyPayment :
    {
      sentDirectlyPaymentNotificationTagId: "63fb7a261aee0a3ee93c8dac",
      allowedAdministrativeAgency: [],
    }
  isDirectlyPaymentOverdueRemind = false;
  isDirectlyPaymentDone = false;
  isDirectPaymentNotificationAllowed = false;
  showDirectlyPaymentOverdueRemind = this.deploymentService.env.OS_HCM.directlyPayment.showDirectlyPaymentOverdueRemind;
  showQuarantineAddress = this.deploymentService.env.OS_HCM.showQuarantineAddress;
  listStatusViewReceptionPage = this.config.listStatusViewReceptionPage ?  this.config.listStatusViewReceptionPage : [];
  paymentConfirmed = this.deploymentService.env?.OS_HCM?.paymentConfirmed ? this.deploymentService.env?.OS_HCM?.paymentConfirmed : {
    dossierTaskStatus: "6411109d5706bf6444283fd8",
    dossierMenuTaskRemind: "6409826d1aee0a3ee93c8dbc",
    showdirectlyPaymentConfirmBtn: false,
  };
  numberDateAdditionalRequirement = this.deploymentService.env.OS_HCM.numberDateAdditionalRequirement;
  numberDateName = {
    vi: 'Quá hạn chờ bổ sung',
    en: 'Overdue additional requirement'
  };
  requestToSubmitPaperApplication = this.deploymentService.env?.OS_HCM?.requestToSubmitPaperApplication ? this.deploymentService.env?.OS_HCM?.requestToSubmitPaperApplication : {
    dossierTaskStatus: "64111af15706bf6444283feb",
    dossierMenuTaskRemind: "640e796667b52157543bccd0",
  };

  deadlineForAdditionalRequests = this.deploymentService.env.deadlineForAdditionalRequests;
  additionalRequestDeadline = "";
  showCancelProcessingOnlineReception = this.deploymentService.env?.OS_QNI?.showCancelProcessingOnlineReception;
  listProcedureCodeShowTooltip = this.deploymentService.env.OS_HCM.listProcedureCodeShowTooltip;

  //os_hcm
  computerEnable = false;
  investorEnable = this.deploymentService.env.OS_HCM.investorEnable == true ?  true : false;
  onlineReceptionScreen = this.enableAddPayStatusColumn?.onlineReceptionScreen == true ? true : false;

  RemoveStopProcessingButton = this.deploymentService.env?.OS_QNI?.RemoveStopProcessingButton;
  filterSectorOfQNI = this.deploymentService.env?.OS_QNI?.filterDossierBySector;
  enableSectorUserSearch = this.deploymentService.env.OS_HCM.enableSectorUserSearch;
  isShowFilterSector = false;
  dossierFeeByDossier = new Map<String, any[]>();

  searchPaymentMethod = this.deploymentService.env.OS_QNM.searchPaymentMethod;
  listPaymentMethodQNM = this.deploymentService.env.OS_QNM.listPaymentMethodQNM;
  checkAll = false;
  checkAllF = false;
  selectedDossiers = []
  selectedDossiersF = []
  checkEnableReceiveMultiple = false;
  enableReceiveMultiple = this.deploymentService.env.OS_HCM.receiveMultiple.enable;
  statusListReceiveMultiple = this.deploymentService.env.OS_HCM.receiveMultiple.statusList;
  agencyListReceiveMultiple = this.deploymentService.env.OS_HCM.receiveMultiple.agencyList;
  enableDownloadManyDossierRecept = this.deploymentService.env.OS_HCM.enableDownloadManyDossierRecept?.enable && tUtils.isAllowedAgency(localStorage, this.deploymentService.env.OS_HCM.enableDownloadManyDossierRecept?.listAgency);
  
  filterSubmissionDate=(this.env?.OS_HCM?.filterSubmissionDate?.enable &&
    this.deploymentService.env.OS_HCM.filterSubmissionDate.AgencyIds.includes( this.userAgency.parent?.id ?? this.userAgency.id  )
  ) ? true : false;
    // Accepter Info
    accepterInfo = {
      id: '',
      fullname: '',
      username: '',
      accountId: '',
    };
      // Agency
  agencyInfo = [];
  selectedLangId = Number(localStorage.getItem('languageId')) || 228;
  enableSaveFullAddress = this.deploymentService.env.OS_HCM.enableSaveFullAddress;
  eForm = {
    id: '',
    component: null,
    data: null,
    renderOptions: {
    },
  };
  applicantEForm = {
    id: '',
    component: null,
    data: null,
    renderOptions: {
    },
  };
  enableMergeDeepDossier = this.deploymentService.env.enableMergeDeepDossier.onlineReception;

  //IGATESUPP-50774 [HCM iGATE V2] Quận Tân Phú - Lọc hồ sơ quá hạn tiếp nhận [SD1570]
  turnOnCountDown8h = this.deploymentService.env.OS_HCM.turnOnCountDown8h ? this.deploymentService.env.OS_HCM.turnOnCountDown8h : false;
  listAgencyCountDown8hSetEndWorkTime = this.deploymentService.env.OS_HCM.listAgencyCountDown8hSetEndWorkTime ? this.deploymentService.env.OS_HCM.listAgencyCountDown8hSetEndWorkTime :
  [
    {
      AgencyID: "62f31441e13bef0ed2e582ca",
      endWorkTime: 15 // theo mốc 24h
    }
  ];

  hideRequestToWithdraw = this.deploymentService.env.OS_HCM.hideRequestToWithdraw.enable &&
                          tUtils.isAllowedAgency(localStorage, this.deploymentService.env.OS_HCM.hideRequestToWithdraw.agencyUsed);

  showDossierName = this.deploymentService.env.OS_HGG.showDossierName;
  hideDeleteButtonDossier = false;
  giaodientiepnhanxuly = this.deploymentService.env.OS_HCM.giaodientiepnhanxuly;
  groupTaskID = this.deploymentService.env.OS_HCM.groupTaskID;
  //QBH
  qbhlist_collapse = this.deploymentService?.env?.OS_QBH?.qbhlist_collapse ? this.deploymentService?.env?.OS_QBH?.qbhlist_collapse : false;
  allowShowCancelDossierReception = this.deploymentService.env.OS_HCM.allowShowCancelDossierReception;
  nowDate = tUtils.newDate();
  // tslint:disable-next-line: max-line-length
  toDate = this.nowDate.getFullYear() + '-' + ((this.nowDate.getMonth() + 1) <= 9 ? '0' + (this.nowDate.getMonth() + 1) : (this.nowDate.getMonth() + 1)) + '-' + ((this.nowDate.getDate() <= 9) ? ('0' + this.nowDate.getDate()) : this.nowDate.getDate());
  // tslint:disable-next-line: max-line-length
  fromDate 
  filterAppliedDateMenuRemind = this.deploymentService.env.OS_HCM?.filterAppliedDateMenuRemind
  listTimeSheetConfig = [];
  dossierReceiptCreationStatusConfig = this.deploymentService.env.OS_HCM.dossierReceiptCreationStatusConfig;
  showRequirePaperAndPayment = this.deploymentService.env.OS_HCM?.showRequirePaperAndPayment;
  isShowRequirePaperAndPayment = false;
  enableOnlinePayment = this.deploymentService.env.OS_HBH.enableOnlinePayment || false;//IGATESUPP-70693
  enablePaymentHPG = this.deploymentService.getAppDeployment()?.enablePaymentHPG ? this.deploymentService.getAppDeployment()?.enablePaymentHPG : false;

  isNotification = this.deploymentService.env.notification.isNotification;
  isNotificationStage1 = this.deploymentService.env.notification.isNotificationStage1;
  changeTagQti = this.env?.OS_QTI?.changeTag === true ? this.env?.OS_QTI?.changeTag : false;
  procedurePage = 0;
  isProcedureListFull = false;
  applyAgencyShowAutoDeadlineAdditionDossier: boolean = false;
  procedureList = [];
  newProcedureSelect = this.deploymentService.env.OS_HCM?.newProcedureSelect ? this.deploymentService.env.OS_HCM.newProcedureSelect : false;
  //phucnh.it2-IGATESUPP-77778
  showDossierUpdateTime = this.deploymentService.env?.OS_HCM?.showDossierUpdateTime;
  turnOnDossierUpdateTimeLabel = false;
  showChangeDirectPayment = this.deploymentService.env.OS_HCM?.showChangeDirectPayment;
  feeRefund = this.deploymentService.env.feeRefund
  cloneDossierConfig = this.deploymentService.cloneDossier?.applyOnlineConfig;
  notReceiveStopProcessing = this.deploymentService.getAppDeployment().notReceiveStopProcessing === true ? true : false;  // IGATESUPP-86673: hiện HS dừng xử lý ở menu không cần xử lý
   //IGATESUPP-86102
   isDVCLTEnable = this.deploymentService.getAppDeployment()?.isDVCLTEnable;
  showsFilterAddressEnterprise = this.deploymentService.showsFilterAddressEnterpriseEnable && tUtils.isAllowedAgency(localStorage,this.deploymentService.showsFilterAddressEnterpriseAgencyIds);
  isShowNationCodeCbx = this.deploymentService.optimize.dossierSearch.isShowNationCodeCbx;
  interfaceWorkHorizontal = this.deploymentService.getAppDeployment().interfaceWorkHorizontal == 1 ? 1 : 0;
  qbhwithdrawprocess = this.deploymentService?.env?.OS_QBH?.qbhwithdrawprocess ? this.deploymentService?.env?.OS_QBH?.qbhwithdrawprocess : false;

  //IGATESUPP-98928  Thêm tên trích yếu hiển thị ở thông tin hồ sơ
  showAbstractConstructionUnitEnable = this.deploymentService.getAppDeployment()?.showAbstractConstructionUnitEnable;
  showAbstractConstructionUnitAgencyIds = this.deploymentService.getAppDeployment()?.showAbstractConstructionUnitAgencyIds;
  onOffAbstractConstructionUnit = false;

  enableDVCLT  =  this.deploymentService.getAppDeployment()?.enableDVCLT ? this.deploymentService.getAppDeployment()?.enableDVCLT : false;


  showSourceLLTP = this.deploymentService.getAppDeployment()?.showSourceLLTP ? this.deploymentService.getAppDeployment()?.showSourceLLTP : false;

  //OS BDG: Begin
  isEnableLableOrganizationInformation = this.deploymentService?.env?.OS_BDG?.isEnableLableOrganizationInformation;
	
   //IGATESUPP-102806 Sở Tư Pháp - Bổ sung trạng thái và filter Hồ sơ đăng ký trên ứng dụng VNeID
  showFilterRegisterApplicationVNeID = this.deploymentService.getAppDeployment()?.showFilterRegisterApplicationVNeID ?  this.deploymentService.getAppDeployment().showFilterRegisterApplicationVNeID : 0;
  showFilterRegisterApplicationVNeIDAgencyId = this.deploymentService.getAppDeployment()?.showFilterRegisterApplicationVNeIDAgencyId ?  this.deploymentService.getAppDeployment().showFilterRegisterApplicationVNeIDAgencyId : [];
  checkShowFilterRegisterApplicationVNeID = false;
  enableShowAutoDeadlineAdditionDossier = this.deploymentService.getAppDeployment()?.showAutoDeadlineAdditionDossier?.enable || 0;
  listShowAutoDeadlineAdditionDossierAgencyId = this.deploymentService.getAppDeployment()?.showAutoDeadlineAdditionDossier?.agencyId || [];
  twoLevelPublicAdministration  =  this.deploymentService.getAppDeployment()?.twoLevelPublicAdministration ? this.deploymentService.getAppDeployment()?.twoLevelPublicAdministration : false;

  isAGG = this.deploymentService.getAppDeployment()?.traCuuHoSoChoTiepNhanAGG == 1 ? true : false;
  constructor(
    private router: Router,
    private dossierService: DossierService,
    private activeRoute: ActivatedRoute,
    private envService: EnvService,
    private deploymentService: DeploymentService,
    private datePipe: DatePipe,
    private cdRef: ChangeDetectorRef,
    private dialog: MatDialog,
    private snackbarService: SnackbarService,
    private keycloakService: KeycloakService,
    private userService: UserService,
    private sectorService : SectorService,
    private procedureService: ProcedureService,
    private notiService: NotificationService,
    private mainService: MainService,
    private adminLayoutNavComponent: AdminLayoutNavComponent,
    private basecatService: BasecatService,
    private commonService: CommonService,
    private notificationV2Service: NotificationV2Service,
    private clipboard: Clipboard
  ) {
    if(this.dossierReceiptCreationStatusConfig.enable){
      this.listStatusViewReceptionPage.push(this.dossierReceiptCreationStatusConfig.dossierMenuTaskRemind);
    }
    this.dataSource = new MatTableDataSource(this.ELEMENTDATA);
    this.getConfig();
    let agencyAllowId = this.deploymentService.env.OS_HCM.allowedAgencyEnableDirectlyPaymentStatus;
    if(agencyAllowId.filter(item => item == this.userAgency.id).length > 0 ){
      this.enableShowDirectlyPaymentStatus = true;
    } else if(this.userAgency.parent != null ){
      if(agencyAllowId.filter(item => item == this.userAgency.parent.id).length > 0 ){
        this.enableShowDirectlyPaymentStatus = true;
      }
    } else if( this.userAgency.ancestors != null){
      this.userAgency.ancestors.forEach(element => {
        if(agencyAllowId.filter(item => item == element.id).length > 0 ){
          this.enableShowDirectlyPaymentStatus = true;
        }
      });
    }
    if (this.activeRoute.snapshot.queryParamMap.get('code') != null) {
      this.paramsQuery.code = this.activeRoute.snapshot.queryParamMap.get('code');
      this.searchForm.patchValue({
        code: this.paramsQuery.code
      });
    }
    if (this.activeRoute.snapshot.queryParamMap.get('taxCode') != null) {
      this.paramsQuery.taxCode = this.activeRoute.snapshot.queryParamMap.get('taxCode');
      this.searchForm.patchValue({
        taxCode: this.paramsQuery.taxCode
      });
    }
    // if (this.activeRoute.snapshot.queryParamMap.get('resPerson') != null) {
    //   this.paramsQuery.resPerson = this.activeRoute.snapshot.queryParamMap.get('resPerson');
    //   this.searchForm.patchValue({
    //     resPerson: this.paramsQuery.resPerson
    //   });
    // }
    if (this.activeRoute.snapshot.queryParamMap.get('identity') != null) {
      this.paramsQuery.identity = this.activeRoute.snapshot.queryParamMap.get('identity');
      this.searchForm.patchValue({
        identityNumber: this.paramsQuery.identity
      });
    }
    if (this.activeRoute.snapshot.queryParamMap.get('applicant') != null) {
      this.paramsQuery.applicant = this.activeRoute.snapshot.queryParamMap.get('applicant');
      this.searchForm.patchValue({
        applicantName: this.paramsQuery.applicant
      });
    }
    if (this.activeRoute.snapshot.queryParamMap.get('sector') != null) {
      this.paramsQuery.sector = this.activeRoute.snapshot.queryParamMap.get('sector');
      this.searchForm.patchValue({
        sectorCtrl: this.paramsQuery.sector
      });
    }
    if (this.activeRoute.snapshot.queryParamMap.get('procedure') != null) {
      this.paramsQuery.procedure = this.activeRoute.snapshot.queryParamMap.get('procedure');
      this.searchForm.patchValue({
        procedureCtrl: this.paramsQuery.procedure
      });
    }
    if (this.activeRoute.snapshot.queryParamMap.get('nation') != null) {
      this.paramsQuery.nation = this.activeRoute.snapshot.queryParamMap.get('nation');
      this.searchForm.patchValue({
        nation: this.paramsQuery.nation
      });
    }
    if (this.activeRoute.snapshot.queryParamMap.get('province') != null) {
      this.paramsQuery.province = this.activeRoute.snapshot.queryParamMap.get('province');
    }
    if (this.activeRoute.snapshot.queryParamMap.get('district') != null) {
      this.paramsQuery.district = this.activeRoute.snapshot.queryParamMap.get('district');
    }
    if (this.activeRoute.snapshot.queryParamMap.get('village') != null) {
      this.paramsQuery.village = this.activeRoute.snapshot.queryParamMap.get('village');
    }
    if (this.activeRoute.snapshot.queryParamMap.get('acceptFrom') != null) {
      this.paramsQuery.acceptFrom = this.activeRoute.snapshot.queryParamMap.get('acceptFrom');
      if (this.paramsQuery.acceptFrom !== '') {
        this.searchForm.patchValue({
          acceptFrom: new Date(this.datePipe.transform(this.paramsQuery.acceptFrom, 'dd/MM/yyyy'))
        });
      }
    }
    if (this.activeRoute.snapshot.queryParamMap.get('acceptTo') != null) {
      this.paramsQuery.acceptTo = this.activeRoute.snapshot.queryParamMap.get('acceptTo');
      if (this.paramsQuery.acceptTo !== '') {
        this.searchForm.patchValue({
          acceptTo: new Date(this.datePipe.transform(this.paramsQuery.acceptTo, 'dd/MM/yyyy'))
        });
      }
    }
    if (this.activeRoute.snapshot.queryParamMap.get('page') != null) {
      this.paramsQuery.page = this.activeRoute.snapshot.queryParamMap.get('page');
    }
    if (this.activeRoute.snapshot.queryParamMap.get('size') != null) {
      this.paramsQuery.size = this.activeRoute.snapshot.queryParamMap.get('size');
    }
    if (this.activeRoute.snapshot.queryParamMap.get('remindId') != null) {
      this.remindId = this.paramsQuery.remindId = this.activeRoute.snapshot.queryParamMap.get('remindId');
    }
     // tslint:disable-next-line:only-arrow-functions
    //  this.router.routeReuseStrategy.shouldReuseRoute = function() {
    //   return false;
    // };
    if (this.activeRoute.snapshot.queryParamMap.get('appliedFrom') != null && this.showAppliedDateFilter) {
      this.paramsQuery.appliedFrom = this.activeRoute.snapshot.queryParamMap.get('appliedFrom');
      if (this.paramsQuery.appliedFrom !== '') {
        this.searchForm.patchValue({
          appliedFrom: new Date(this.datePipe.transform(this.paramsQuery.appliedFrom, 'dd/MM/yyyy'))
        });
      }
    }
    if (this.activeRoute.snapshot.queryParamMap.get('appliedTo') != null && this.showAppliedDateFilter) {
      this.paramsQuery.appliedTo = this.activeRoute.snapshot.queryParamMap.get('appliedTo');
      if (this.paramsQuery.appliedTo !== '') {
        this.searchForm.patchValue({
          appliedTo: new Date(this.datePipe.transform(this.paramsQuery.appliedTo, 'dd/MM/yyyy'))
        });
      }
    }
    if (this.activeRoute.snapshot.queryParamMap.get('sortId') != null ) {
      this.sortId = this.paramsQuery.sortId = this.activeRoute.snapshot.queryParamMap.get('sortId');
      this.searchForm.patchValue({
        sortCtrl: this.paramsQuery.sortId
      });
    }
    if (this.activeRoute.snapshot.queryParamMap.get('additionalRequestDeadline') != null) {
      this.additionalRequestDeadline = this.paramsQuery.additionalRequestDeadline = this.activeRoute.snapshot.queryParamMap.get('additionalRequestDeadline');
      this.searchForm.patchValue({
        additionalRequestDeadline: this.paramsQuery.additionalRequestDeadline
      });
    }
    if (this.activeRoute.snapshot.queryParamMap.get('payStatus') != null ) {
      this.paramsQuery.payStatus = this.activeRoute.snapshot.queryParamMap.get('payStatus');
      this.searchForm.patchValue({
        sortCtrl: this.paramsQuery.payStatus
      });
    }
    if (this.activeRoute.snapshot.queryParamMap.get('paymentMethod') != null ) {
      this.paramsQuery.paymentMethod = this.activeRoute.snapshot.queryParamMap.get('paymentMethod');
      this.searchForm.patchValue({
        sortCtrl: this.paramsQuery.paymentMethod
      });
    }
    // QBH
    // QBH layout
    if(this.qbhlistlayout == true)
    {
      this.displayedColumns = this.displayedColumnsQBH;
    }
    //QBH end
    //AGG
    if(this.isAGG){
      this.displayedColumns = ['stt', 'code', 'procedure', 'applicant', 'appliedDate', 'pay', 'status', 'action'];
    }
    //AGG

    this.checkAgencySortType();
    if (this.activeRoute.snapshot.queryParamMap.get('province1') != null) {
      this.paramsQuery.province1 = this.activeRoute.snapshot.queryParamMap.get('province1');
    }
    if (this.activeRoute.snapshot.queryParamMap.get('district1') != null) {
      this.paramsQuery.district1 = this.activeRoute.snapshot.queryParamMap.get('district1');
    }
    if (this.activeRoute.snapshot.queryParamMap.get('village1') != null) {
      this.paramsQuery.village1 = this.activeRoute.snapshot.queryParamMap.get('village1');
    }
    this.checkShowFilterRegisterApplicationVNeIDValue();
  }

  async ngOnInit(): Promise<void> {
    if(this.showRequirePaperAndPayment.enable && tUtils.isAllowedAgency(localStorage, this.showRequirePaperAndPayment.agencyId)){
      this.isShowRequirePaperAndPayment = true;
    }
    const userAgency = JSON.parse(localStorage.getItem("userAgency"));
    if ( tUtils.nonNull(this.showDossierUpdateTime, 'enable') &&  this.showDossierUpdateTime.enable == true
          && tUtils.nonNull(this.showDossierUpdateTime, 'agency') 
          && this.showDossierUpdateTime.agency.filter( item => item == this.userAgency.id || item == this.userAgency?.parent?.id).length > 0){
            this.turnOnDossierUpdateTimeLabel = true;
            
    }
    if(this.enableShowAutoDeadlineAdditionDossier == 1)
    {
          let agencyId = JSON.parse(localStorage.getItem("userAgency")).parent?.id || "";
          this.applyAgencyShowAutoDeadlineAdditionDossier =  this.listShowAutoDeadlineAdditionDossierAgencyId.filter(item => item == agencyId).length > 0; 
    }
    // if(this.allowFilterAppliedDateOptimizationCustom.enable && this.allowFilterAppliedDateOptimizationCustom.allowedMenu.includes('dossier/online-reception')){
    //   if(!!userAgency?.code){
    //     for(let i in this.allowFilterAppliedDateOptimizationCustom.agencyUsed){
    //       userAgency.code.split('.').reduce((acc, part, index) => {
    //         const partialValue = index === 0 ? part : `${acc[index - 1]}.${part}`;
    //         acc.push(partialValue);
    //         if(partialValue == this.allowFilterAppliedDateOptimizationCustom.agencyUsed[i]){
    //           this.isShowFilterApplyDate =true;
    //           this.rangeLimitDossierOnlineReception = this.allowFilterAppliedDateOptimizationCustom.rangeLimitDossierOnlineReception
    //         }
    //         return acc;
    //       }, []);
    //     }
    //   }
    // }
    if(this.allowFilterAppliedDateOptimizationCustom.enable 
      && this.allowFilterAppliedDateOptimizationCustom.allowedMenu.includes('dossier/online-reception')
      && tUtils.isAllowedAgency(localStorage, this.allowFilterAppliedDateOptimizationCustom.agencyUsed)
      ){
          this.isShowFilterApplyDate =true;
          this.rangeLimitDossierOnlineReception = this.allowFilterAppliedDateOptimizationCustom.rangeLimitDossierOnlineReception
    }
    
    if(this.allowFilterAppliedDateOptimization.enable && this.allowFilterAppliedDateOptimization.allowedMenu.includes('dossier/online-reception')){
      this.isShowFilterApplyDate = true
    }
    const permissions = this.userService.getUserPermissions();
    for (const p of permissions) {
      if (p.permission.code == 'confirmOnlinePayment') {
        this.showConfirmOnlinePayment=true;
        break;
      }
    }
    for (const p of permissions) {
      if (p.permission.code === 'dossierQualifiedReceptSearch') {
        if(this.showDossierQualifiedRecept){
          this.showSearchListOfficerDossierSeen = true;
        }
        break;
      }
    }
    if(this.notReceiveStopProcessing || this.listAgencyStopProcessing.filter(item => item == userAgency.id || item == userAgency?.parent?.id || item == userAgency?.ancestors?.id).length > 0){
      this.showStopProcessing = false;
    }
    if(this.listAgencyAppliShowTooltipOrganization.filter(item => item == userAgency.id || item == userAgency?.parent?.id || item == userAgency?.ancestors?.id).length > 0){
      this.isShowTooltipOrganizationForAgency = true;
    }
    if(this.listAgencyAllowShowAddressWhenHover.filter(item => item == userAgency.id || item == userAgency?.parent?.id || item == userAgency?.ancestors?.id).length > 0){
      this.isShowAddressWhenHover = true;
    }
    if(!this.showStopProcessing){
      this.searchForm.patchValue({dossierStatus:('0,1,3,15,14,17,18,19,20,21')})
    }

    if(this.enablefindResPerson && this.listAgencyFindResperon.filter(item => item == userAgency.id || item == userAgency?.parent?.id || item == userAgency?.ancestors?.id).length > 0){
        this.isShowResPerson = true ; 
    }
    if(this.giaodientiepnhanxuly == true){
      await this.getChildGroupCategory();
    }
    this.env = this.deploymentService.getAppDeployment()?.env;
    this.selectedLang = localStorage.getItem('language');
    this.mainService.setPageTitle(this.pageTitle[this.selectedLang]);
    for (const p of permissions) {
      if (p.permission.code === 'admin' || p.permission.code === 'oneGateAdminMaster' || p.permission.code === 'oneGateDossierDelete') {
        this.hasDossierDeletePermission = true;
        break;
      }
    }

    if(this.enableFilterSectorIs){
       if(this.agencyUnitEnableFilterSector.filter(item => item == userAgency.id || item == userAgency?.parent?.id || item == userAgency?.ancestors?.id).length > 0) {
        this.isShowFilterSector = true;
       }
    }
    
    if(this.isShowFilterApplyDate){
      if(this.rangeLimitDossierOnlineReception > 0){
        this.fromDate = tUtils.getPastDate(this.rangeLimitDossierOnlineReception);
        this.searchForm.patchValue({
          appliedFrom: this.fromDate,
          appliedTo: this.toDate,
        })
      } else {
        this.searchForm.patchValue({
          appliedFrom: this.toDate,
          appliedTo: this.toDate,
        })
      }
    }

    this.checkReceiveMultiple();
    this.addAdditionalRemindId();
    this.getListNation();
    if (this.showsFilterAddressEnterprise) {
      this.getListProvince1();
    }
    await this.getListSector();
    if(this.newProcedureSelect){
      this.getProcedureList(true)
    }else{
      this.getListProcedure();
    }
    this.getRemindMenuTask();
    this.getDossierTaskStatus();
    if(this.env?.vnpost?.config === '1' && this.env?.vnpost)
    {
      this.getListVNPostStatus();
    }
    if (!!this.env?.paymentLater && this.env?.paymentLater === true)
    {
      this.paymentLater = true;
    }
    if(this.isShowAgencyEnterprise == 1){
      this.flexGtSm4Column = 18;
      this.flexGtSm5Column = 14;
    }
    //phucnh.it2-IGATESUPP-39497
    this.displayedColumns = this.onlineReceptionScreen ? this.displayedColumnsHCM : this.displayedColumns;
    //end phucnh.it2-IGATESUPP-39497

    //BEGIN dangquang-IGATESUPP-59015
    if (this.enableDownloadManyDossierRecept) {
      this.displayedColumns.splice(0, 0, 'selectDownloadFile');
    }
    //END dangquang-IGATESUPP-59015

    //AGG
    if(this.isAGG){
      this.displayedColumns = ['stt', 'code', 'procedure', 'applicant', 'appliedDate', 'pay', 'status', 'action'];
    }
    //AGG
    if (this.configDisplayColumns) {
      this.displayedColumns = this.configColumTable;
    }
    await this.checkDirectlyPaymentNotificationUserAgency();
    this.getShowApplicantOrganizationEnable(this.remindId);
    this.autoSearch();

    await this.checkShowAbstractConstructionUnit();


    if(this.qbhlist_collapse){
      this.xpandStatus = true;
      if(this.depConfig?.addressDefault?.nationId){
        const nationId = this.searchForm.controls['nation'].value;
        if(!nationId){
          const defaultNationId = this.depConfig?.addressDefault?.nationId;
          this.searchForm.patchValue({nation: defaultNationId});
          this.nationChange({value: defaultNationId});
        }
        setTimeout(() => {
          const provinceId = this.searchForm.controls[ 'province' ].value;
          if(!provinceId){
            const defaultProvinceId = this.depConfig?.addressDefault?.provinceId;
            this.searchForm.patchValue({province: defaultProvinceId});
            this.provinceChange({value: defaultProvinceId});
          }
        }, 2000);
      }
    }

    if (this.deploymentService.env.OS_HGG.hideDeleteButtonDossier) {
      this.hideDeleteButtonDossier = true;
    }
    if(this.enableShowAIClassifyDossier && this.listAgencyShowAIClassifyDossier.filter( item => item == userAgency.id || item == userAgency?.parent?.id  || item == userAgency?.ancestors?.id ).length > 0 ){
      this.isShowAIClassifyDossier = true;
      this.displayedColumns.splice(this.displayedColumns.indexOf('action'), 0, 'AIClassifyDossier');
    }
  }
  changeAppliedFromDate($event) {
    this.isChangeDate = true;
    const formObj = this.searchForm.getRawValue();

    if (this.rangeLimitDossierOnlineReception > 0) {
      const fromDate = new Date(formObj.appliedFrom);
      const toDate = new Date(formObj.appliedTo);
      let sumDate = Number(toDate) - Number(fromDate);

      if (sumDate > 0) {
        let constDate = Math.floor(sumDate / 1000 / 60 / 60 / 24);
        if (constDate > this.rangeLimitDossierOnlineReception) {
          let toDate = tUtils.getFutureDate(this.rangeLimitDossierOnlineReception, formObj.appliedFrom);
          this.searchForm.patchValue({
            appliedTo: toDate
          })
        }
      }
    }
  }

  checkEnableRefund(dossier:any){
    try{
      if(dossier?.feeRefundData?.status == 0 
        && !!dossier.feeRefundData?.stk 
        && !!dossier.feeRefundData?.owner 
        && !!dossier.feeRefundData?.bank ){
          return true
        }
    }catch(e){
      return false
    }
      return false
  }

  confirmRefund(dossierId, dossierCode){
      const dialogData = new ConfirmRefundDialogModel(dossierId, dossierCode)
      const dialogRef = this.dialog.open(ConfirmRefundComponent, {
        minWidth: '50vw',
      maxHeight: "85vh",
      data: dialogData,
      disableClose: true,
      autoFocus: false
      })
      dialogRef.afterClosed().subscribe(dialogResult => {
        if (dialogResult == 1) {
          const msgObj = {
            vi: 'Đã xác nhận hoàn tiền!',
            en: 'Refund confirmed!'
          };
          this.snackbarService.openSnackBar(1, msgObj[this.selectedLang], '', 'success_notification', this.config.expiredTime);
          const searchString = this.generateSearchString(this.paginationType, (this.pageIndex - 1), this.size, 'id,desc');
          this.getListDossier(searchString);
        }else if (dialogResult == 0){
          const msgObj = {
            vi: 'Xác nhận hoàn tiền thất bại!',
            en: 'Refund confirm failed!'
          };
          this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
        }
      })
  }

  changeAppliedToDate($event) {
    this.isChangeDate = true;
    const formObj = this.searchForm.getRawValue();

    if (this.rangeLimitDossierOnlineReception > 0) {
      const fromDate = new Date(formObj.appliedFrom);
      const toDate = new Date(formObj.appliedTo);
      let sumDate = Number(toDate) - Number(fromDate);

      if (sumDate > 0) {
        let constDate = Math.floor(sumDate / 1000 / 60 / 60 / 24);
        if (constDate > this.rangeLimitDossierOnlineReception) {
          let fromDate = tUtils.getPastDate(this.rangeLimitDossierOnlineReception, formObj.appliedTo);
          this.searchForm.patchValue({
            appliedFrom: fromDate
          })
        }
      }
    }
  }


  getShowApplicantOrganizationEnable(remindId?){
    //yêu cầu os_hcm task IGATESUPP-43273
    if (this.deploymentService.env.OS_HCM.showApplicantOrganization.enable == true) {
      if (localStorage.getItem("userAgency") !== null && localStorage.getItem("userAgency") !== undefined) {
        const agency =  JSON.parse(localStorage.getItem("userAgency"));
        const allowedAgencyList = this.deploymentService.env.OS_HCM.showApplicantOrganization.allowedAgency;
        if (allowedAgencyList) {
          let checkEnable = false;
          for (let i = 0; i < allowedAgencyList.length; i++) {
            const element = allowedAgencyList[i];
            if (element == agency?.id  && remindId == "60f52e6a09cbf91d41f88836" || element == agency?.parent?.id && remindId == "60f52e6a09cbf91d41f88836") {
              checkEnable = true;
              break;
            }
          }
          this.computerEnable = checkEnable == true ? true : false;
        }
      }
    }
  }

  getConfig(){
    const config = this.deploymentService.getAppDeployment();
    if (!!config) {
        if (config.domain && config.domain.length > 0){
          // tslint:disable-next-line:max-line-length
          const domain = config.domain.filter(listdomain => ((listdomain.domain.toLowerCase().indexOf(window.location.host) > -1) || (window.location.host.toLowerCase().indexOf(listdomain.domain) > -1)));
          if (domain && domain.length > 0 && domain[0].rootAgency){
            this.searchDomain = '&domain-agency-id=' + domain[0].rootAgency.id;
          }
        }
    }
  }

  ngAfterViewInit() {
    // setTimeout(() => {
    //   // console.clear();
    //   this.autoSearch();
    // }, 1000);
  }

  ngOnDestroy() {
    this.onDestroy.next();
    this.onDestroy.complete();
  }
  // ========================================================== GET
  async getListDossier(searchString) {
    const userAgency = JSON.parse(localStorage.getItem('userAgency'));
    searchString = this.remakeRequestUrl(searchString);
    this.dossierService.getListDossier(searchString + this.searchDomain).subscribe(async data => {
      if (true) {
        this.dossierFeeByDossier.clear();
        const ids = data.content.map(item => item.id);
        const dossierFee = await this.commonService.getProcostDossiers(ids);
        dossierFee.forEach(item => {
          if (!!!this.dossierFeeByDossier.get(item.dossier.id)) {
            this.dossierFeeByDossier.set(item.dossier.id, [])
          }
          this.dossierFeeByDossier.set(item.dossier.id, [item, ...this.dossierFeeByDossier.get(item.dossier.id)])
        })
      }
      this.ELEMENTDATA = [];
      this.listDisplayingDossier = [];
      // this.countResult = data.totalElements;
      if (data.content.length > 0){
        this.checkNullData = 0;
      } else {
        this.checkNullData = 1;
      }
      this.numberOfElements = data.numberOfElements;
      this.pageIndex = data?.number + 1;
      for (let i = 0; i < data.numberOfElements; i++) {
        //IGATESUPP-41626
        if(this.onlyDisplayNationCode && !!data.content[i].nationCode && data.content[i].nationCode != ''){
          data.content[i].code = data.content[i].nationCode;
          data.content[i].nationCode = '';
        }
        let requireAdditional = true;
        data.content[i].stt = this.size * (this.pageIndex - 1) + (i + 1);
        data.content[i].isChangePaymentMethod = false;
        if(this.enableShowFormOfPayment && this.methodPaymentListShowFormOfPayment.length > 0 && this.agencyIdsShowFormOfPayment.filter( item => item == userAgency.id || item == userAgency?.parent?.id  || item == userAgency?.ancestors?.id ).length == 0){
          if(this.methodPaymentListShowFormOfPayment.filter(item => item == data.content[i]?.paymentMethod?.id).length > 0){
            data.content[i].isChangePaymentMethod = true;
          } 
        }
        if(this.enableFunctionAppraisalDossier){
          if(!!data.content[i]?.extendHCM?.officerDossierQualifiedRecept && data.content[i]?.extendHCM?.officerDossierQualifiedRecept.length > 0){
            data.content[i].checkDossierQualifiedPass = true;
          }
        }
        // dossier task status
        if (data.content[i].dossierTaskStatus !== undefined && data.content[i].dossierTaskStatus !== null ){
          data.content[i].dossierStatus.name = data.content[i].dossierTaskStatus.name;
          // tslint:disable-next-line:max-line-length
          if (data.content[i].dossierTaskStatus.id === this.deploymentService.env.dossierTaskStatus?.requestForAdditionalDocuments.id || data.content[i].dossierTaskStatus.id === this.deploymentService.env.dossierTaskStatus?.dossierAdded.id  && this.enableShowAdditionalRequirements=== 0){
            requireAdditional = false;
          }
        }else{
          if (data.content[i].dossierStatus.id === 0){
            // tslint:disable-next-line:max-line-length
            data.content[i].dossierStatus.name = this.justRegistered['trans'].filter(res => Number(res.languageId) === Number(localStorage.getItem('languageId')))[0].name;
          }else{
            data.content[i].dossierStatus.name = data.content[i].currentTask[0].bpmProcessDefinitionTask.name['name'];
          }
        }
        data.content[i].requireAdditional = requireAdditional;

        if (!!data.content[i].currentTask && data.content[i].currentTask.length !== 0) {
          if (data.content[i].currentTask[0].bpmProcessDefinitionTask.remind.id === '60f6364e09cbf91d41f88859') {
            data.content[i].isFFOTask = true;
            data.content[i].dossierStatus.name = 'Thực hiện nghĩa vụ tài chính';
          }
        }

        if (data.content[i].dossierStatus?.id === 1 && this.env?.refuseProcessing ) {
          data.content[i].isRefuse = true ;
        }else{
          data.content[i].isRefuse = false ;
        }

        if (!!data.content[i].paymentMethod && !!data.content[i].paymentMethod.code
            && (data.content[i].paymentMethod.code === 'PAYMENT_PLATFORM_HCM_LGSP'  || data.content[i].paymentMethod.code === 'E_PAYMENT_HCM_LGSP' ||
            ((data.content[i].paymentMethod.code === 'VNPT_PAYMENT_PLATFORM' || data.content[i].paymentMethod.code === 'VNPT_PAY') )) &&
             this.paymentLater === true &&
            (!data.content[i].paymentRequestData || !data.content[i].paymentRequestData.paidDate)){
              data.content[i].checkPaymentLater = true;
        }else{
          data.content[i].checkPaymentLater = false;
        }

        if(this.swapFullName2ContactPerson?.isSwap
          && !!this.swapFullName2ContactPerson?.procedureId
          && this.swapFullName2ContactPerson?.procedureId.length !== 0
          && this.swapFullName2ContactPerson?.procedureId.includes(data.content[i]?.procedure?.id)
          && !!data.content[i]?.applicant?.data?.contactPerson){
          if(!!data.content[i]?.applicant?.data?.fullname){
            const fullName = data.content[i]?.applicant?.data?.fullname;
            data.content[i].applicant.data.ownerFullname = fullName;
          }else {
            console.log("Swapped but fullname empty");
          }
        }else if(this.swapFullName2ContactPerson?.isSwap
          && !!this.swapFullName2ContactPerson?.procedureId
          && this.swapFullName2ContactPerson?.procedureId.length !== 0
          && this.swapFullName2ContactPerson?.procedureId.includes(data.content[i]?.procedure?.id)
          && !!data.content[i]?.applicant?.data?.contact){
          if(!!data.content[i]?.applicant?.data?.fullname){
            const fullName = data.content[i]?.applicant?.data?.fullname;
            data.content[i].applicant.data.ownerFullname = fullName;
          }else {
            console.log("Swapped but fullname empty");
          }
        }
        if(this.enableShowAutoDeadlineAdditionDossier == 1 && this.applyAgencyShowAutoDeadlineAdditionDossier){
            //kiểm tra hs quá hạn bổ sung của SCT thuộc HCM
            // id: 24 --- Trạng thái hồ sơ: Quá hạn bổ sung
            if(data.content[i].dossierStatus?.id == 24)
            {
                delete data.content[i];  
                continue;
            }
        }else if(this.numberDateAdditionalRequirement && this.numberDateAdditionalRequirement.enable){
          //Kiểm tra trạng thái hồ sơ có hạn yêu cầu
          if(data.content[i].dossierStatus?.numberDateRequire && data.content[i].dossierStatus?.dateRequire){
            let current = new Date();
            current.setHours(0, 0, 0, 0);
            let dateRequire =  new Date(data.content[i].dossierStatus?.dateRequire);
            if(dateRequire < current){
              data.content[i].dossierStatus.name = this.numberDateName[this.selectedLang];
            }
          }
        }
        //phucnh.it2-IGATESUPP-39497
        let dossierFee:any  = null;
        // dossierFee = await this.getProcostDossier(data.content[i].id);
        dossierFee = this.dossierFeeByDossier.get(data.content[i].id)? this.dossierFeeByDossier.get(data.content[i].id) : [];
        data.content[i].status = this.getTotalCost(dossierFee).value;
        data.content[i].paidId = this.getTotalCost(dossierFee).id;
        //end phucnh.it2-IGATESUPP-39497

        //linhnvv.agg-IGATESUPP-103676
        dossierFee = this.dossierFeeByDossier.get(data.content[i].id)? this.dossierFeeByDossier.get(data.content[i].id) : [];
        data.content[i].payAGG = this.getTotalCostAGG(dossierFee);
        //data.content[i].paidId = this.getTotalCost(dossierFee).id;
        //linhnvv.agg-IGATESUPP-103676

        //IGATESUPP-40128
        //Kiểm tra và lấy địa chỉ kiểm dịch
        if(this.showQuarantineAddress && this.showQuarantineAddress.enable && this.showQuarantineAddress.listProcedureCode){
          let procedure =  this.showQuarantineAddress.listProcedureCode.find(o => o == data.content[i]?.procedure?.code);
          if(procedure){
            data.content[i].quarantineAddress = this.getDCKiemDich(data.content[i]);
            data.content[i].quarantineName = this.getTenKiemDich(data.content[i]);
          }
        }

        if (this.turnOnCountDown8h) {
          const userAgency = JSON.parse(localStorage.getItem('userAgency'));
          let agencyIdIndex = this.listAgencyCountDown8hSetEndWorkTime.findIndex(object => {
            return object.agencyID === userAgency.id;
          });
          if (agencyIdIndex != -1) {
            let agencyEndWorkingTime = this.listAgencyCountDown8hSetEndWorkTime[agencyIdIndex].endWorkTime;
            let totalWorkingTime = 0;
            if (agencyEndWorkingTime <= 12) {
              totalWorkingTime = agencyEndWorkingTime - 8;
            } else if (agencyEndWorkingTime > 12) {
               totalWorkingTime = agencyEndWorkingTime - 9;
            }
            const appliedDate = new Date(Date.parse(data.content[i].appliedDate));
            const appliedTime = appliedDate.getTime();
            let maxDueDate = Math.floor(8/totalWorkingTime);
            let maxDueTime = Math.floor(8%totalWorkingTime);
            let maxDueAcceptDossier = appliedTime + Math.floor(maxDueDate*24*60*60*1000) + Math.floor(maxDueTime*60*60*1000);

            const currentDate = new Date();
            let breakTime = 24 - totalWorkingTime;
            let remainTime = maxDueAcceptDossier - Math.floor(breakTime*60*60*1000*maxDueDate) - currentDate.getTime();

            if(appliedDate.getHours() >= agencyEndWorkingTime) {
              remainTime += Math.floor(Math.floor(breakTime*60*60*1000))
            }

            if(remainTime > 0) {
              if (remainTime <= Math.floor(8*60*60*1000)) {
                const hour = Math.floor(remainTime / (1000 * 60 * 60));
                const minutes = Math.floor((remainTime % (1000 * 60 * 60)) / (1000 * 60));
                data.content[i].timeRemain = "Còn lại: " + hour + " giờ " + minutes + " phút";
              } else if (remainTime > Math.floor(8*60*60*1000)) {
                data.content[i].timeRemain = "Còn lại: 8 giờ 00 phút";
              }
            }
          }
        } else if (data.content[i].dossierStatus?.id == 0 && data.content[i].dossierTaskStatus?.id == this.deploymentService?.env?.dossierTaskStatus?.justRegistered?.id) {
          //IGATESUPP-45293
          //Thêm thời gian đếm ngược trước khi tiếp nhận
          //let onOffHCM = this.env?.OS_HCM?.timeCountAfterReceiveHCM;
          let onOffHCM = false;
          let listAgencyCountDown8h = !!this.env?.OS_HCM.listAgencyCountDown8h ? this.env?.OS_HCM.listAgencyCountDown8h : [];
          // let listAgencyCountDown8h = [
          //   "61075a146e8bb4462db32064",
          //   "60c868a6289bad69c7cc0169"
          // ];
          if(listAgencyCountDown8h.length > 0) {
            if(listAgencyCountDown8h.includes(this.userAgency.id)) {
              onOffHCM = true;
            }
          }
          console.log("==========onOffHCM: " + onOffHCM);
          let timeConfig = this.env?.OS_HCM?.timeFinishWorkDay;
          //const timeNextDay = 7 * 60 * 60 * 1000 + 30 * 60 * 1000 + ((24 - timeConfig) * 60 * 60 * 1000);
          const var8h = 8 * 1000 * 60 * 60;
          const currentDate = new Date();
          const ngayNop = new Date(Date.parse(data.content[i].appliedDate));
          let gioNghi = new Date(Date.parse(data.content[i].appliedDate));
          let hanTiepNhan = new Date(Date.parse(data.content[i].appliedDate));
          gioNghi.setHours(timeConfig);
          gioNghi.setMinutes(0);
          gioNghi.setSeconds(0);
          const gioNop = ngayNop.getHours();
          //console.log("=========gioNop: " + gioNop);
          let remainTime = 0;
          let remainDate = currentDate.getTime() - Date.parse(data.content[i].appliedDate);
          //console.log("==================:" + remainDate);
          const diffInHours = Math.floor(remainDate / (1000 * 60 * 60));
          if(ngayNop < gioNghi && this.getDayOfWeek(ngayNop) != 'Saturday' && this.getDayOfWeek(ngayNop) != 'Sunday' ) {
            if(diffInHours < 8) {
              remainTime = var8h - remainDate;
            }
          }
          else {
            // if(diffInHours < 24) {
            //   ngayNop.setDate(ngayNop.getDate() + 1);
            //   ngayNop.setHours(7);
            //   ngayNop.setMinutes(30);
            //   remainDate = currentDate.getTime() - ngayNop.getTime();
            //   if(remainDate > 0) {
            //     remainTime = var8h - remainDate;
            //   }
            // }
            const timeSheetId = data.content[i]?.procedureProcessDefinition?.processDefinition?.timesheet?.id;
            let tmpTimeSheet = this.listTimeSheetConfig.find(ts => ts.id == timeSheetId);
            if (!tmpTimeSheet) {
              const resTimesheet = await this.basecatService.getTimeSheetConfig(timeSheetId);
              this.listTimeSheetConfig.push(resTimesheet);
              tmpTimeSheet = resTimesheet;
            }
            const t = tmpTimeSheet?.config
            if (tmpTimeSheet?.config && tmpTimeSheet?.config.length > 0) {
              let startHours = new Date(Date.parse(tmpTimeSheet?.config[0]?.period[0]?.startHours));
              startHours.setHours(startHours.getHours() + startHours.getTimezoneOffset()/60);
              if (this.getDayOfWeek(ngayNop) == 'Friday' ||this.getDayOfWeek(ngayNop) == 'Saturday' || this.getDayOfWeek(ngayNop) == 'Sunday') {
                hanTiepNhan = this.getStartOfNextWeek(ngayNop);
              } else {
                hanTiepNhan.setDate(ngayNop.getDate() + 1);
              }
              
              hanTiepNhan.setHours(startHours.getHours() + 8);
              hanTiepNhan.setMinutes(startHours.getMinutes());
              hanTiepNhan.setSeconds(startHours.getSeconds());
              if (currentDate < hanTiepNhan) {
                remainTime = hanTiepNhan.getTime() - currentDate.getTime();
              } else {
                remainTime = 0;
              }
            } else {
              onOffHCM = false;
            }
          }
          
          // if(diffInHours < 8) {
          //   if(gioNop < timeConfig) {
          //     remainTime = var8h - remainDate;
          //   }
          //   else {
          //     remainTime = var8h + timeNextDay - remainDate;
          //   }
          // }
          const hour = Math.floor(remainTime / (1000 * 60 * 60));
          const minutes = Math.floor((remainTime % (1000 * 60 * 60)) / (1000 * 60));
          //console.log(hour + " giờ " + minutes + " phút");
          if(onOffHCM) {
              if(remainTime < (8*60*60*1000))
                data.content[i].timeRemain = "Còn lại: " + hour + " giờ " + minutes + " phút";
              else 
              data.content[i].timeRemain = "Còn lại: 8 giờ 00 phút";
          }
        }
        
        data.content[i].isOnlinePayment =false;
        if(data.content[i].paymentMethod){
          if(this.showConfirmOnlinePayment &&(data.content[i].paymentMethod?.code=='PAYMENT_PLATFORM_HCM_LGSP'||data.content[i].paymentMethod?.code=='E_PAYMENT_HCM_LGSP')){
            data.content[i].isOnlinePayment=true;
          }
        }
        let procedureDet: any;
        if(tUtils.nonNull(data.content[i]?.extendHCM, "officerDossierQualifiedRecept")){
          if(data.content[i]?.extendHCM?.officerDossierQualifiedRecept.length > 0){
            procedureDet= await this.procedureService.getProcedureDetail(data.content[i]?.procedure?.id).toPromise();
            data.content[i].isShowDossierQualifiedRecept = procedureDet?.extendHCM?.enableShowDossierQualifiedRecept ? procedureDet?.extendHCM?.enableShowDossierQualifiedRecept : false;
            data.content[i]?.extendHCM?.officerDossierQualifiedRecept.forEach(element => {
              if(element.result == 0){
                element.result = 'Hồ sơ đủ điều kiện';
              } else if(element.result == 1) {
                element.result = 'Hồ sơ yêu cầu bổ sung';
              } else {
                element.result = 'Hồ sơ từ chối';
              }
            });
          }
        }
        if(this.isShowAIClassifyDossier){
          const procedureDetail = !!procedureDet ? procedureDet : await this.procedureService.getProcedureDetail(data.content[i]?.procedure?.id).toPromise();
          if(procedureDetail?.extendHCM?.showAIClassifyDossier){
            let AIClassifyDossier = false;
            let totalDocuments = 0;
            let validDocuments = 0;
            let invalidDocuments = 0;
            if(data.content[i]?.extendHCM?.listFileHash){
              let total = data.content[i].extendHCM.listFileHash.length;
              let valid = data.content[i].extendHCM.listFileHash.filter(item => item.isVerifed === 1).length;
              let inValid = data.content[i].extendHCM.listFileHash.filter(item => item.isVerifed === 0).length;
              if(total == valid){
                AIClassifyDossier = true;
              } 
              totalDocuments = total;
              validDocuments = valid;
              invalidDocuments = inValid;
            }
            data.content[i].AIClassifyDossier = AIClassifyDossier ;
            data.content[i].totalDocuments = totalDocuments;
            data.content[i].validDocuments = validDocuments;
            data.content[i].invalidDocuments = invalidDocuments;
          }
        }

        if(this.isShowAddressWhenHover){
          data.content[i].toolTipAddress = this.getToolTipAddress(data.content[i]);
        }
        if(this.showChangeDirectPayment && this.showChangeDirectPayment?.enable){
          //1: Không tính phí, 2: Chưa thanh toán; 3: Đã thanh toán 1 phần; 4: Đã thanh toán
          if(!!data.content[i].paymentMethod && !!this.showChangeDirectPayment?.statusId && !!data.content[i].paymentMethod?.code && (data.content[i].paidId == 2 || data.content[i].paidId == 3)){
            var currentStatus = data.content[i].dossierStatus?.id;
            var arrStatus = this.showChangeDirectPayment.statusId.split(",");
            if(arrStatus.includes(String(currentStatus))){
            	data.content[i].showChangeDirectPayment = true;
            }else{
              data.content[i].showChangeDirectPayment = false;
            } 
          } else{
            data.content[i].showChangeDirectPayment = false;
          }        
        }else{
          data.content[i].showChangeDirectPayment = false;
        }
        this.ELEMENTDATA.push(data.content[i]);
        this.listDisplayingDossier.push(data.content[i]);
      }
      this.dataSource.data = this.ELEMENTDATA;
      this.setTotalElements(data, this.paginationType);
    });
  }

  getDayOfWeek(date: Date): string {
    const daysOfWeek = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
    const dayIndex = date.getDay();
    return daysOfWeek[dayIndex];
  }

  getStartOfNextWeek(currentDate: Date): Date {
    const nextWeek = new Date(currentDate);
    nextWeek.setDate(currentDate.getDate() + 7);
    const dayOfWeek = nextWeek.getDay();
    const daysUntilMonday = dayOfWeek === 0 ? 7 : dayOfWeek; 
    nextWeek.setDate(nextWeek.getDate() - daysUntilMonday + 1);
    return nextWeek;
  }


  cancelProcessing(dossierId, dossierCode) {
    if (dossierCode === undefined || dossierCode === null) {
      dossierCode = '';
    }
    const dialogData = new CancelDossierDialogModel(dossierId, dossierCode);
    const dialogRef = this.dialog.open(CancelDossierComponent, {
      minWidth: '50vw',
      maxHeight: '90vh',
      data: dialogData,
      disableClose: true,
      autoFocus: false,
      panelClass: 'cancel-dossier-dialog-container'
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
      if(dialogResult ==  true){
        const msgObj = {
          vi: 'Đã dừng hồ sơ!',
          en: 'Dossier cancle!'
        };
        this.snackbarService.openSnackBar(1, msgObj[this.selectedLang], '', 'success_notification', this.config.expiredTime);
        const searchString = this.generateSearchString(this.paginationType, (this.pageIndex - 1), this.size, 'id,desc');
        this.getListDossier(searchString);
        this.callRemindTask();
      }else{
        const msgObj = {
          vi: 'Dừng hồ sơ không thành công!',
          en: 'Cancle failed!'
        };
        this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
      }
    });
  }

  getDCKiemDich(item)
  {
    let str = '';
    if(item.eForm?.data?.SoNhaDuong6){
      str += item.eForm?.data?.SoNhaDuong6 + ', ';
    }
    if(item.eForm?.data?.PhuongXa6?.label){
      str += item.eForm?.data?.PhuongXa6?.label + ', ';
    }
    if(item.eForm?.data?.QuanHuyen6?.label){
      str += item.eForm?.data?.QuanHuyen6?.label + ', ';
    }
    if(item.eForm?.data?.TinhThanhPho6?.label){
      str += item.eForm?.data?.TinhThanhPho6?.label + ', ';
    }
    return str;
    
  }
  getToolTipAddress(item)
  {
    let str = '';
    if(item?.applicant?.data?.TenNguoiChiuTrachNhiem){
      str += item.applicant?.data?.TenNguoiChiuTrachNhiem + ' - ';
    }
    if(item?.applicant?.data?.organization){
      str += item.applicant?.data?.organization + ' - ';
    }
    if(item?.applicant?.data?.address1){
      str += item.applicant?.data?.address1 + ', ';
    }
    if(item?.applicant?.data?.village1?.label){
      str += item?.applicant?.data?.village1?.label + ', ';
    }
    if(item?.applicant?.data?.district1?.label){
      str += item?.applicant?.data?.district1?.label + ', ';
    }
    if(item?.applicant?.data?.province1?.label){
      str += item?.applicant?.data?.province1?.label + ', ';
    }
    if(item?.applicant?.data?.nation1?.label){
      str += item?.applicant?.data?.nation1?.label;
    }
   
    return str;
  }

  getTenKiemDich(item)
  {
    let str = '';
    if(item.eForm?.data?.TenDiaDiemKiemDich){
      str = item.eForm?.data?.TenDiaDiemKiemDich;
    }
    return str;
  }

  //phucnh.it2-IGATESUPP-39497
  async getProcostDossier(dossierId) {

    return new Promise<Array<any>>(resolve => {
      this.dossierService.getDossierFee(dossierId).subscribe(data => {
        resolve(data);
      });
    });
  }

  getTotalCost(data) {
    let cost = 0;
    let paid = 0;
    let result ={
      id: 0,
      value: ''
    };
    if(data.length == 0 ){
      result ={
        id: 1,
        value: 'Không tính phí'
      };
    }else{
      data.forEach(element => {
        cost += element.quantity * element.amount;
        paid += element.paid;
      });
      if(paid == 0 && cost != 0){
        result ={
          id: 2,
          value: 'Chưa thanh toán'
        };
      }else{

        if(cost == paid && cost != 0){
          result ={
            id: 4,
            value: 'Đã thanh toán'
          };
        }else{
          if(cost > paid && paid != 0){
            result ={
              id: 3,
              value: 'Đã thanh toán 1 phần'
            };
          }else{
            result ={
              id: 1,
              value: 'Không tính phí'
            };
          }
        }
      }
    }
    return result;
  }
  //end phucnh.it2-IGATESUPP-39497

  //AGG
  getTotalCostAGG(data) {
    let result = '';
    if(data?.length > 0){
      const tongTien = data?.reduce((sum, current) => sum + (current.amount * current.quantity), 0);
      const daThanhToan = data?.reduce((sum, current) => sum + current.paid, 0);
      if(daThanhToan >= tongTien) {
        result = "Đã thanh toán";
      }else if(daThanhToan > 0 && daThanhToan < tongTien){
        result = "Đã thanh toán một phần";
      }else{
        result = "Chưa thanh toán";
      }
    }else{
      result = "Không có lệ phí";
    }
    return result;
  }
  //AGG

  getDossierTaskStatus() {
    this.dossierService.getAgencyTag(this.deploymentService.env.dossierTaskStatus.justRegistered.id).subscribe(rs => {
      this.justRegistered = rs;
    }, err => {
      console.log(err);
    });
  }

  protected filterSectorS() {
    if (!this.sectors) {
      return;
    }
    let search = this.searchSectorCtrl.value.trim();
    this.searchSectorKeyword = search;
    if (!search) {
      this.sectors = JSON.parse(JSON.stringify(this.listSector).replace(/null/g, '""'));
      this.sectorFiltered.next(this.sectors);
      return;
    } else {
      search = search.toLowerCase();
    }

    if (this.sectors.filter(sec => sec.name.toLowerCase().indexOf(search) > -1).length > 0) {
      this.sectorFiltered.next(
        this.sectors.filter(sec => sec.name.toLowerCase().indexOf(search) > -1)
      );
    } else {
    if (this.isFullListSector == true) {
      this.sectors = JSON.parse(JSON.stringify(this.listSector).replace(/null/g, '""'));
      this.sectorFiltered.next(
        this.sectors.filter(sec => sec.name.toLowerCase().indexOf(search) > -1)
      );
    } else {

      this.sectorService.getListSector('?page=' + this.listSectorPage + '&size=50&sort=name.name,asc&status=1&all-agency=0').subscribe(data => {
        console.log('api sector ', data);

        this.listSectorPage++;
        this.isFullListSector = data.last;
        for (let i = 0; i < data.numberOfElements ; i++) {

          this.listSector.push(data.content[i]);
        }
        // this.listSector.sort((a, b) => a.name.localeCompare(b.name));
        this.sectors = JSON.parse(JSON.stringify(this.listSector).replace(/null/g, '""'));
        this.sectorFiltered.next(this.sectors);
      } , err => {
        console.log(err);
      }) ;
    }
  }
  }

  getListSector() {
    const search = !this.searchSectorKeyword.trim() ? "" : this.searchSectorKeyword.toLowerCase();
    return new Promise<void>(async resolve => {
      if (this.isFullListSector) {
        resolve();
      } else {
        if(this.isShowFilterSector || this.showSectorOnlineReception){
          console.log('this.userAgency')
          console.log(this.userAgency)
          let searchString =  '?agency-id=' + this.userAgency.id;
          if (this.showSectorOnlineReception) {
            searchString =  '?agency-id=' + this.userAgency.parent.id;
          }
           const data1 = await this.sectorService.getListSectorAll(searchString + '&only-agency-id=1').toPromise();
           if(data1.length > 0){
            const data = data1;
            console.log('api sector ', data);
            this.isFullListSector = true;
            for (let i = 0; i < data.length ; i++) {
              data[i].name = data[i].name[0].name;
              this.listSector.push(data[i]);
            }
            this.listSector.sort((a, b) => a.name.localeCompare(b.name));
            this.sectors = JSON.parse(JSON.stringify(this.listSector).replace(/null/g, '""'));
            this.sectorFiltered.next(this.sectors);
            this.searchSectorCtrl.valueChanges.pipe(takeUntil(this.onDestroy)).subscribe(() => {
              this.filterSectorS();
            });
          }
          else {
            let accountId = localStorage.getItem('tempUID');
            this.sectorService.getUserSectorOnlyOne(accountId, this.userAgency.id).subscribe(data => {
              this.listSectorId = data.sectorIds;
              if (!!this.listSectorId && this.listSectorId.length !== 0) {
                for (let i = 0; i < data.sectors.length; i++) {
                  this.listSectorUser += data.sectors[i].id + ',';
                  console.log("this.listSectorUser", this.listSectorUser)
                  if (!this.listSector.filter(sector => sector.id === data.sectors[i].id)[0]) {
                    this.listSector.push(data.sectors[i]);
                  }
                  if (this.searchSectorKeyword !== '' && !this.listsectorSearch.filter(sector => sector.id === data.sectors[i].id)[0]) {
                    this.listsectorSearch.push(data.sectors[i]);
                  }
                }
                // this.listSector.sort((a, b) => a.name.localeCompare(b.name));
                this.sectors = JSON.parse(JSON.stringify(this.listSector).replace(/null/g, '""'));
                // this.sectorFiltered.next(this.sectors);
                this.listsectorSearch = JSON.parse(JSON.stringify(this.listsectorSearch).replace(/null/g, '""'));
                if (this.searchSectorKeyword !== '') {
                  this.sectorFiltered.next(this.listsectorSearch.filter(sec => sec.name.toLowerCase().indexOf(search) > -1));
                }
                else {
                  this.sectorFiltered.next(this.sectors);
                }
                // this.searchSectorCtrl.valueChanges.pipe(takeUntil(this.onDestroy)).subscribe(() => {
                //   this.filterSector(this.searchSectorKeyword);
                // });
              } else {
                const searchString = '?keyword=' + this.searchSectorKeyword + '&agency-id=' + this.userAgency.id + '&page=' + this.listSectorPage + '&size=' + 1000 + '&spec=' + this.paginationType + '&sort=name.name,asc&status=1';
                this.procedureService.getListSector(searchString).subscribe(data => {
                  this.listSectorId = data.content;
                  if (!!this.listSectorId && this.listSectorId.length !== 0) {
                    this.isFullListSector = data.last;
                    this.listSectorPage++;
                    for (let i = 0; i < data.numberOfElements; i++) {
                      if (!this.listSector.filter(sector => sector.id === data.content[i].id)[0]) {
                        this.listSector.push(data.content[i]);
                      }
                    }
                    this.listSector.sort((a, b) => a.name.localeCompare(b.name));
                    this.sectors = JSON.parse(JSON.stringify(this.listSector).replace(/null/g, '""'));
                    this.sectorFiltered.next(this.sectors.filter(sec => sec.name.toLowerCase().indexOf(search) > -1));
                    // this.searchSectorCtrl.valueChanges.pipe(takeUntil(this.onDestroy)).subscribe(() => {
                    //   this.filterSector();
                    // });
                  } else {
                    const searchString = '?keyword=' + this.searchSectorKeyword + '&agency-id=' + this.userAgency?.parent?.id + '&page=' + this.listSectorPage + '&size=' + 1000 + '&spec=' + this.paginationType + '&sort=name.name,asc&status=1';
                    this.procedureService.getListSector(searchString).subscribe(data => {
                      this.listSectorId = data.content;
                      if (!!this.listSectorId && this.listSectorId.length !== 0) {
                        this.isFullListSector = data.last;
                        this.listSectorPage++;
                        for (let i = 0; i < data.numberOfElements; i++) {
                          if (!this.listSector.filter(sector => sector.id === data.content[i].id)[0]) {
                            this.listSector.push(data.content[i]);
                          }
                        }
                        this.listSector.sort((a, b) => a.name.localeCompare(b.name));
                        this.sectors = JSON.parse(JSON.stringify(this.listSector).replace(/null/g, '""'));
                        this.sectorFiltered.next(this.sectors.filter(sec => sec.name.toLowerCase().indexOf(search) > -1));
                      }
                      else {
                        const searchString = '?keyword=' + this.searchSectorKeyword + '&agency-id=' + '&page=' + this.listSectorPage + '&size=' + 1000 + '&spec=' + this.paginationType + '&sort=name.name,asc&status=1';
                        this.procedureService.getListSector(searchString).subscribe(data => {
                          this.listSectorId = data.content;
                          this.isFullListSector = data.last;
                          this.listSectorPage++;
                          for (let i = 0; i < data.numberOfElements; i++) {
                            if (!this.listSector.filter(sector => sector.id === data.content[i].id)[0]) {
                              this.listSector.push(data.content[i]);
                            }
                          }
                          this.listSector.sort((a, b) => a.name.localeCompare(b.name));
                          this.sectors = JSON.parse(JSON.stringify(this.listSector).replace(/null/g, '""'));
                          this.sectorFiltered.next(this.sectors.filter(sec => sec.name.toLowerCase().indexOf(search) > -1));
                        });
                      }
                    });
                  }
                });
              }
              resolve();
            }, err => {
              console.log(err);
              const searchString = '?keyword=' + this.searchSectorKeyword + '&agency-id=' + this.userAgency.id + '&page=' + this.listSectorPage + '&size=' + 1000 + '&spec=' + this.paginationType + '&sort=name.name,asc&status=1';
              this.procedureService.getListSector(searchString).subscribe(data => {
                this.listSectorId = data.content;
                if (!!this.listSectorId && this.listSectorId.length !== 0) {
                  this.isFullListSector = data.last;
                  this.listSectorPage++;
                  for (let i = 0; i < data.numberOfElements; i++) {
                    if (!this.listSector.filter(sector => sector.id === data.content[i].id)[0]) {
                      this.listSector.push(data.content[i]);
                    }
                  }
                  this.listSector.sort((a, b) => a.name.localeCompare(b.name));
                  this.sectors = JSON.parse(JSON.stringify(this.listSector).replace(/null/g, '""'));
                  this.sectorFiltered.next(this.sectors.filter(sec => sec.name.toLowerCase().indexOf(search) > -1));
                  this.searchSectorCtrl.valueChanges.pipe(takeUntil(this.onDestroy)).subscribe(() => {
                  //  this.filterSector(this.searchSectorKeyword);
                  });
                } else {
                  if (!!this.userAgency?.parent?.id) {
                    const searchString = '?keyword=' + this.searchSectorKeyword + '&agency-id=' + this.userAgency.parent.id + '&page=' + this.listSectorPage + '&size=' + 1000 + '&spec=' + this.paginationType + '&sort=name.name,asc&status=1';
                    this.procedureService.getListSector(searchString).subscribe(data => {
                      this.listSectorId = data.content;
                      if (!!this.listSectorId && this.listSectorId.length !== 0) {
                        this.isFullListSector = data.last;
                        this.listSectorPage++;
                        for (let i = 0; i < data.numberOfElements; i++) {
                          if (!this.listSector.filter(sector => sector.id === data.content[i].id)[0]) {
                            this.listSector.push(data.content[i]);
                          }
                        }
                        this.listSector.sort((a, b) => a.name.localeCompare(b.name));
                        this.sectors = JSON.parse(JSON.stringify(this.listSector).replace(/null/g, '""'));
                        this.sectorFiltered.next(this.sectors.filter(sec => sec.name.toLowerCase().indexOf(search) > -1));
                      } else {
                        const searchString = '?keyword=' + this.searchSectorKeyword + '&agency-id=' + '&page=' + this.listSectorPage + '&size=' + 1000 + '&spec=' + this.paginationType + '&sort=name.name,asc&status=1';
                        this.procedureService.getListSector(searchString).subscribe(data => {
                          this.listSectorId = data.content;
                          this.isFullListSector = data.last;
                          this.listSectorPage++;
                          for (let i = 0; i < data.numberOfElements; i++) {
                            if (!this.listSector.filter(sector => sector.id === data.content[i].id)[0]) {
                              this.listSector.push(data.content[i]);
                            }
                          }
                          this.listSector.sort((a, b) => a.name.localeCompare(b.name));
                          this.sectors = JSON.parse(JSON.stringify(this.listSector).replace(/null/g, '""'));
                          this.sectorFiltered.next(this.sectors.filter(sec => sec.name.toLowerCase().indexOf(search) > -1));
                        });
                      }
                    });
                  } else {
                    const searchString = '?keyword=' + this.searchSectorKeyword + '&agency-id=' + '&page=' + this.listSectorPage + '&size=' + 1000 + '&spec=' + this.paginationType + '&sort=name.name,asc&status=1';
                    this.procedureService.getListSector(searchString).subscribe(data => {
                      this.listSectorId = data.content;
                      this.isFullListSector = data.last;
                      this.listSectorPage++;
                      for (let i = 0; i < data.numberOfElements; i++) {
                        if (!this.listSector.filter(sector => sector.id === data.content[i].id)[0]) {
                          this.listSector.push(data.content[i]);
                        }
                      }
                      this.listSector.sort((a, b) => a.name.localeCompare(b.name));
                      this.sectors = JSON.parse(JSON.stringify(this.listSector).replace(/null/g, '""'));
                      this.sectorFiltered.next(this.sectors.filter(sec => sec.name.toLowerCase().indexOf(search) > -1));
                    });
                  }
                }
              });
              resolve();
            });
         }
        } else {
          let i = 0;
          setTimeout(() => {
            let accountId = localStorage.getItem('tempUID');
            while ( i < 5) {
              if(accountId != null || accountId != ''){
                break;
              }else{
                accountId = localStorage.getItem('tempUID');
              }
            }
            this.sectorService.getUserSectorOnlyOne(accountId, this.userAgency.id).subscribe(data => {
              console.log("data", data);
              this.listSectorId = data.sectorIds;
              if (!!this.listSectorId && this.listSectorId.length !== 0) {
                for (let i = 0; i < data.sectors.length; i++) {
                  this.listSectorUser += data.sectors[i].id + ',';
                  console.log("this.listSectorUser", this.listSectorUser)
                  if (!this.listSector.filter(sector => sector.id === data.sectors[i].id)[0]) {
                    this.listSector.push(data.sectors[i]);
                  }
                  if (this.searchSectorKeyword !== '' && !this.listsectorSearch.filter(sector => sector.id === data.sectors[i].id)[0]) {
                    this.listsectorSearch.push(data.sectors[i]);
                  }
                }
                // this.listSector.sort((a, b) => a.name.localeCompare(b.name));
                this.sectors = JSON.parse(JSON.stringify(this.listSector).replace(/null/g, '""'));
                // this.sectorFiltered.next(this.sectors);
                this.listsectorSearch = JSON.parse(JSON.stringify(this.listsectorSearch).replace(/null/g, '""'));
                if (this.searchSectorKeyword !== '') {
                  this.sectorFiltered.next(this.listsectorSearch.filter(sec => sec.name.toLowerCase().indexOf(search) > -1));
                }
                else {
                  this.sectorFiltered.next(this.sectors);
                }
                // this.searchSectorCtrl.valueChanges.pipe(takeUntil(this.onDestroy)).subscribe(() => {
                //   this.filterSector(this.searchSectorKeyword);
                // });
              } else {
                const searchString = '?keyword=' + this.searchSectorKeyword + '&agency-id=' + this.userAgency.id + '&page=' + this.listSectorPage + '&size=' + 1000 + '&spec=' + this.paginationType + '&sort=name.name,asc&status=1';
                this.procedureService.getListSector(searchString).subscribe(data => {
                  this.listSectorId = data.content;
                  if (!!this.listSectorId && this.listSectorId.length !== 0) {
                    this.isFullListSector = data.last;
                    this.listSectorPage++;
                    for (let i = 0; i < data.numberOfElements; i++) {
                      if (!this.listSector.filter(sector => sector.id === data.content[i].id)[0]) {
                        this.listSector.push(data.content[i]);
                      }
                    }
                    this.listSector.sort((a, b) => a.name.localeCompare(b.name));
                    this.sectors = JSON.parse(JSON.stringify(this.listSector).replace(/null/g, '""'));
                    this.sectorFiltered.next(this.sectors.filter(sec => sec.name.toLowerCase().indexOf(search) > -1));
                    // this.searchSectorCtrl.valueChanges.pipe(takeUntil(this.onDestroy)).subscribe(() => {
                    //   this.filterSector();
                    // });
                  } else {
                    const searchString = '?keyword=' + this.searchSectorKeyword + '&agency-id=' + this.userAgency?.parent?.id + '&page=' + this.listSectorPage + '&size=' + 1000 + '&spec=' + this.paginationType + '&sort=name.name,asc&status=1';
                    this.procedureService.getListSector(searchString).subscribe(data => {
                      this.listSectorId = data.content;
                      if (!!this.listSectorId && this.listSectorId.length !== 0) {
                        this.isFullListSector = data.last;
                        this.listSectorPage++;
                        for (let i = 0; i < data.numberOfElements; i++) {
                          if (!this.listSector.filter(sector => sector.id === data.content[i].id)[0]) {
                            this.listSector.push(data.content[i]);
                          }
                        }
                        this.listSector.sort((a, b) => a.name.localeCompare(b.name));
                        this.sectors = JSON.parse(JSON.stringify(this.listSector).replace(/null/g, '""'));
                        this.sectorFiltered.next(this.sectors.filter(sec => sec.name.toLowerCase().indexOf(search) > -1));
                      }
                      else {
                        const searchString = '?keyword=' + this.searchSectorKeyword + '&agency-id=' + '&page=' + this.listSectorPage + '&size=' + 1000 + '&spec=' + this.paginationType + '&sort=name.name,asc&status=1';
                        this.procedureService.getListSector(searchString).subscribe(data => {
                          this.listSectorId = data.content;
                          this.isFullListSector = data.last;
                          this.listSectorPage++;
                          for (let i = 0; i < data.numberOfElements; i++) {
                            if (!this.listSector.filter(sector => sector.id === data.content[i].id)[0]) {
                              this.listSector.push(data.content[i]);
                            }
                          }
                          this.listSector.sort((a, b) => a.name.localeCompare(b.name));
                          this.sectors = JSON.parse(JSON.stringify(this.listSector).replace(/null/g, '""'));
                          this.sectorFiltered.next(this.sectors.filter(sec => sec.name.toLowerCase().indexOf(search) > -1));
                        });
                      }
                    });
                  }
                });
              }
              resolve();
            }, err => {
              console.log(err);
              const searchString = '?keyword=' + this.searchSectorKeyword + '&agency-id=' + this.userAgency.id + '&page=' + this.listSectorPage + '&size=' + 1000 + '&spec=' + this.paginationType + '&sort=name.name,asc&status=1';
              this.procedureService.getListSector(searchString).subscribe(data => {
                this.listSectorId = data.content;
                if (!!this.listSectorId && this.listSectorId.length !== 0) {
                  this.isFullListSector = data.last;
                  this.listSectorPage++;
                  for (let i = 0; i < data.numberOfElements; i++) {
                    if (!this.listSector.filter(sector => sector.id === data.content[i].id)[0]) {
                      this.listSector.push(data.content[i]);
                    }
                  }
                  this.listSector.sort((a, b) => a.name.localeCompare(b.name));
                  this.sectors = JSON.parse(JSON.stringify(this.listSector).replace(/null/g, '""'));
                  this.sectorFiltered.next(this.sectors.filter(sec => sec.name.toLowerCase().indexOf(search) > -1));
                  this.searchSectorCtrl.valueChanges.pipe(takeUntil(this.onDestroy)).subscribe(() => {
                  //  this.filterSector(this.searchSectorKeyword);
                  });
                } else {
                  if (!!this.userAgency?.parent?.id) {
                    const searchString = '?keyword=' + this.searchSectorKeyword + '&agency-id=' + this.userAgency.parent.id + '&page=' + this.listSectorPage + '&size=' + 1000 + '&spec=' + this.paginationType + '&sort=name.name,asc&status=1';
                    this.procedureService.getListSector(searchString).subscribe(data => {
                      this.listSectorId = data.content;
                      if (!!this.listSectorId && this.listSectorId.length !== 0) {
                        this.isFullListSector = data.last;
                        this.listSectorPage++;
                        for (let i = 0; i < data.numberOfElements; i++) {
                          if (!this.listSector.filter(sector => sector.id === data.content[i].id)[0]) {
                            this.listSector.push(data.content[i]);
                          }
                        }
                        this.listSector.sort((a, b) => a.name.localeCompare(b.name));
                        this.sectors = JSON.parse(JSON.stringify(this.listSector).replace(/null/g, '""'));
                        this.sectorFiltered.next(this.sectors.filter(sec => sec.name.toLowerCase().indexOf(search) > -1));
                      } else {
                        const searchString = '?keyword=' + this.searchSectorKeyword + '&agency-id=' + '&page=' + this.listSectorPage + '&size=' + 1000 + '&spec=' + this.paginationType + '&sort=name.name,asc&status=1';
                        this.procedureService.getListSector(searchString).subscribe(data => {
                          this.listSectorId = data.content;
                          this.isFullListSector = data.last;
                          this.listSectorPage++;
                          for (let i = 0; i < data.numberOfElements; i++) {
                            if (!this.listSector.filter(sector => sector.id === data.content[i].id)[0]) {
                              this.listSector.push(data.content[i]);
                            }
                          }
                          this.listSector.sort((a, b) => a.name.localeCompare(b.name));
                          this.sectors = JSON.parse(JSON.stringify(this.listSector).replace(/null/g, '""'));
                          this.sectorFiltered.next(this.sectors.filter(sec => sec.name.toLowerCase().indexOf(search) > -1));
                        });
                      }
                    });
                  } else {
                    const searchString = '?keyword=' + this.searchSectorKeyword + '&agency-id=' + '&page=' + this.listSectorPage + '&size=' + 1000 + '&spec=' + this.paginationType + '&sort=name.name,asc&status=1';
                    this.procedureService.getListSector(searchString).subscribe(data => {
                      this.listSectorId = data.content;
                      this.isFullListSector = data.last;
                      this.listSectorPage++;
                      for (let i = 0; i < data.numberOfElements; i++) {
                        if (!this.listSector.filter(sector => sector.id === data.content[i].id)[0]) {
                          this.listSector.push(data.content[i]);
                        }
                      }
                      this.listSector.sort((a, b) => a.name.localeCompare(b.name));
                      this.sectors = JSON.parse(JSON.stringify(this.listSector).replace(/null/g, '""'));
                      this.sectorFiltered.next(this.sectors.filter(sec => sec.name.toLowerCase().indexOf(search) > -1));
                    });
                  }
                }
              });
              resolve();
            });
          }, 2000);
       }
        if (this.showSectorOnlineReception) {
          if (this.newProcedureSelect) {
            this.getProcedureList(true)
          } else {
            this.getListProcedure();
          }
          this.getRemindMenuTask();
          this.getDossierTaskStatus();
          if (this.env?.vnpost?.config === '1' && this.env?.vnpost) {
            this.getListVNPostStatus();
          }
          if (!!this.env?.paymentLater && this.env?.paymentLater === true) {
            this.paymentLater = true;
          }
          if (this.isShowAgencyEnterprise == 1) {
            this.flexGtSm4Column = 18;
            this.flexGtSm5Column = 14;
          }
          //phucnh.it2-IGATESUPP-39497
          this.displayedColumns = this.onlineReceptionScreen ? this.displayedColumnsHCM : this.displayedColumns;
          //end phucnh.it2-IGATESUPP-39497

          //BEGIN dangquang-IGATESUPP-59015
          if (this.enableDownloadManyDossierRecept) {
            this.displayedColumns.splice(0, 0, 'selectDownloadFile');
          }
          //END dangquang-IGATESUPP-59015

          await this.checkDirectlyPaymentNotificationUserAgency();
          this.getShowApplicantOrganizationEnable(this.remindId);
          this.autoSearch();

          if (this.qbhlist_collapse) {
            this.xpandStatus = true;
            if (this.depConfig?.addressDefault?.nationId) {
              const nationId = this.searchForm.controls['nation'].value;
              if (!nationId) {
                const defaultNationId = this.depConfig?.addressDefault?.nationId;
                this.searchForm.patchValue({ nation: defaultNationId });
                this.nationChange({ value: defaultNationId });
              }
              setTimeout(() => {
                const provinceId = this.searchForm.controls['province'].value;
                if (!provinceId) {
                  const defaultProvinceId = this.depConfig?.addressDefault?.provinceId;
                  this.searchForm.patchValue({ province: defaultProvinceId });
                  this.provinceChange({ value: defaultProvinceId });
                }
              }, 2000);
            }
          }

          if (this.deploymentService.env.OS_HGG.hideDeleteButtonDossier) {
            this.hideDeleteButtonDossier = true;
          }
        }
      }
    });
  }

  getListProcedure() {
    if (this.isFullListProcedure) {
      return;
    } else {
      let agencyIdSearch = '';
      // if ( this.userAgency !== null && this.userAgency !== undefined && !this.isAdmin) {
      if ( this.userAgency !== null && this.userAgency !== undefined && !this.isAdmin) {
        if (!!this.userAgency.parent && !!this.userAgency.parent.id) {
          agencyIdSearch = `&agency-id=${this.userAgency.parent.id}`;
        } else if (this.userAgency.id !== this.config.rootAgency.id) {
          agencyIdSearch = `&agency-id=${this.userAgency.id}`;
        }
      }
      agencyIdSearch = this.env?.filterProcedureByAgencyId ? agencyIdSearch : '';
      const searchString =
        '?status=1&sort=translate.name,asc&keyword=' + this.searchProcedureKeyword +
        '&spec=' + this.paginationType + '&page=' + this.listProcedurePage + '&size=50' +
        '&sector-id=' + this.paramsQuery.sector + agencyIdSearch;
      this.procedureService.getListProcedure(searchString).subscribe(data => {
        this.isFullListProcedure = data.last;
        this.listProcedurePage++;
        for (let i = 0; i < data.numberOfElements; i++) {
          this.listProcedure.push(data.content[i]);
        }
        this.procedures = JSON.parse(JSON.stringify(this.listProcedure).replace(/null/g, '""'));
        this.procedureFiltered.next(this.procedures);
        // this.searchProcedureCtrl.valueChanges.pipe(takeUntil(this.onDestroy)).subscribe(() => {
        //   this.filterProcedure();
        // });
      }, err => {
        console.log(err);
      });
    }
  }

  getListNation() {
    this.dossierService.getListNation().subscribe(data => {
      this.listNation = data;
    }, err => {
      console.log(err);
    });
  }

  getListProvince() {
    this.dossierService.getListPlace(
      this.searchForm.get('nation').value,
      null,
      this.config.placeProvinceTypeId
    ).subscribe(data => {
      this.listProvince = data;
      this.searchForm.patchValue({
        province: this.paramsQuery.province
      });
      if (this.searchForm.get('province').value !== '') {
        this.getListDistrict();
      }
    }, err => {
      console.log(err);
    });
  }

  getListDistrict() {
    this.dossierService.getListPlace(
      this.searchForm.get('nation').value,
      this.searchForm.get('province').value,
      this.config.placeDistrictTypeId
    ).subscribe(data => {
      this.listDistrict = data;
      this.searchForm.patchValue({
        district: this.paramsQuery.district
      });
      if (this.searchForm.get('district').value !== '') {
        this.getListVillage();
      }
    }, err => {
      console.log(err);
    });
  }

  getListVillage() {
    this.dossierService.getListPlace(
      this.searchForm.get('nation').value,
      this.searchForm.get('district').value,
      this.config.placeWardTypeId
    ).subscribe(data => {
      this.listVillage = data;
      this.searchForm.patchValue({
        village: this.paramsQuery.village
      });
    }, err => {
      console.log(err);
    });
  }

  getListProvince1() {
    this.dossierService.getListPlace(
      '5f39f4a95224cf235e134c5c',
      null,
      this.config.placeProvinceTypeId
    ).subscribe(data => {
      this.listProvince1 = data;
      this.searchForm.patchValue({
        province1: this.paramsQuery.province1
      });
      if (this.searchForm.get('province1').value !== '') {
        this.getListDistrict1();
      }
    }, err => {
      console.log(err);
    });
  }

  getListDistrict1() {
    this.dossierService.getListPlace(
      '5f39f4a95224cf235e134c5c',
      this.searchForm.get('province1').value,
      this.config.placeDistrictTypeId
    ).subscribe(data => {
      this.listDistrict1 = data;
      this.searchForm.patchValue({
        district1: this.paramsQuery.district1
      });
      if (this.searchForm.get('district1').value !== '') {
        this.getListVillage1();
      }
    }, err => {
      console.log(err);
    });
  }

  getListVillage1() {
    this.dossierService.getListPlace(
      '5f39f4a95224cf235e134c5c',
      this.searchForm.get('district1').value,
      this.config.placeWardTypeId
    ).subscribe(data => {
      this.listVillage1 = data;
      this.searchForm.patchValue({
        village1: this.paramsQuery.village1
      });
    }, err => {
      console.log(err);
    });
  }

  getStatusColor(type) {
    switch (type) {
      case 0: return '#000000';
      case 1: return '#FF9800';
      case 2: return '#c47a04';
      case 3: return '#FF9800';
      case 4: return '#03A9F4';
      case 5: return '#03A9F4';
      case 6: return '#DE1212';
      case 12: return '#DE1212';
      default: return '#000000de';
    }
  }

  setAuthStatusName(statusId){
    const status = this.listAuthenticationStatus.find(status => status.id == statusId);
    if (status) {
      return status.name;
    } else {
      return "Không xác định";
    }
  }

  // ========================================================== ON CHANGE
  nationChange(event) {
    this.getListProvince();
    this.listDistrict = [];
    this.listVillage = [];
  }

  provinceChange(event) {
    this.getListDistrict();
    this.listVillage = [];
  }

  districtChange(event) {
    this.getListVillage();
  }

  province1Change(event) {
    this.getListDistrict1();
    this.listVillage1 = [];
  }

  district1Change(event) {
    this.getListVillage1();
  }

  printReportChange(event) {
    event.value = '';
  }

  sectorChange(event) {
    this.paramsQuery.sector = event.value;
    this.router.navigate([], {
      queryParams: {
        sector: event.value
      },
      queryParamsHandling: 'merge'
    });
    this.onConfirmSearch();
    this.listProcedure = [];
    this.isFullListProcedure = false;
    this.listProcedurePage = 0;
    this.getListProcedure();
  }

  async onConfirmSearch() {
    //const applyMethodValue = this.applyMethod.value[0];
    //return;
    ////debugger
    if(this.isChangeDate){
      this.getRemindMenuTask()
      this.isChangeDate = false;
    }
    const formObj = this.searchForm.getRawValue();
    const checkDate = await this.checkDateValid(formObj.acceptFrom, formObj.acceptTo);
    if(this.showAppliedDateFilter) {
      await this.checkDateValid(formObj.appliedFrom, formObj.appliedTo);
    }
    if (checkDate) {
      const searchString = this.generateSearchString(this.paginationType, 0, this.size, 'id,desc');
      this.paramsQuery = {
        code: formObj.code.trim(),
        identity: formObj.identityNumber.trim(),
        applicant: formObj.applicantName.trim(),
        page: '1',
        size: this.size.toString(),
        procedure: formObj.procedureCtrl,
        nation: formObj.nation,
        sector: formObj.sectorCtrl,
        province: formObj.province,
        district: formObj.district,
        village: formObj.village,
        acceptFrom: (formObj.acceptFrom ? this.datePipe.transform(formObj.acceptFrom, 'dd/MM/yyyy') : ''),
        acceptTo: (formObj.acceptTo ? this.datePipe.transform(formObj.acceptTo, 'dd/MM/yyyy') : ''),
        remindId: this.remindId,
        appliedFrom: (this.showAppliedDateFilter && formObj.appliedFrom ? this.datePipe.transform(formObj.appliedFrom, 'dd/MM/yyyy') : ''),
        appliedTo: (this.showAppliedDateFilter && formObj.appliedTo ? this.datePipe.transform(formObj.appliedTo, 'dd/MM/yyyy') : ''),
        organization: formObj.applicantOrganization.trim(),
        sortId: this.sortId,
        additionalRequestDeadline: formObj.additionalRequestDeadline,
        payStatus: formObj.payStatus,
        paymentMethod: formObj.paymentMethod,
        taxCode: formObj.taxCode,
        resPerson: formObj.resPerson,
        province1: formObj.province1,
        district1: formObj.district1,
        village1: formObj.village1
      };
      this.pageIndex = 1;
      this.page = 1;

      this.router.navigate([], {
        queryParams: {
          identity: formObj.identityNumber.trim(),
          applicant: formObj.applicantName.trim(),
          page: 1,
          size: this.size.toString(),
          procedure: formObj.procedureCtrl,
          sector: formObj.sectorCtrl,
          province: formObj.province,
          district: formObj.district,
          village: formObj.village,
          acceptFrom: (formObj.acceptFrom ? this.datePipe.transform(formObj.acceptFrom, 'dd/MM/yyyy') : ''),
          acceptTo: (formObj.acceptTo ? this.datePipe.transform(formObj.acceptTo, 'dd/MM/yyyy') : ''),
          remindId: this.remindId,
          appliedFrom: (this.showAppliedDateFilter && formObj.appliedFrom ? this.datePipe.transform(formObj.appliedFrom, 'dd/MM/yyyy') : ''),
          appliedTo: (this.showAppliedDateFilter && formObj.appliedTo ? this.datePipe.transform(formObj.appliedTo, 'dd/MM/yyyy') : ''),
          organization: formObj.applicantOrganization.trim(),
          sortId: this.sortId,
          payStatus: formObj.payStatus,
          paymentMethod: formObj.paymentMethod,
          taxCode: formObj.taxCode,
          resPerson: formObj.resPerson,
          filterOfficerDossierQualifiRecept: formObj.dossierSeen,
          province1: formObj.province1,
          district1: formObj.district1,
          village1: formObj.village1
        }
      });
      this.getListDossier(searchString);

      if (this.isDVCLTEnable && this.deploymentService.getAppDeployment()?.syncDossierDVCLT) {
        this.dossierService.syncDossierDVCLT(formObj.code.trim()).subscribe(data => {})
      }
    }
  }

  onClickOpenAdvancedSearchBox() {
    this.xpandStatus = this.xpandStatus ? false : true;
    if(this.xpandStatus){
      if(this.depConfig?.addressDefault?.nationId){
        const nationId = this.searchForm.controls['nation'].value;
        if(!nationId){
          const defaultNationId = this.depConfig?.addressDefault?.nationId;
          this.searchForm.patchValue({nation: defaultNationId});
          this.nationChange({value: defaultNationId});
        }
        setTimeout(() => {
          const provinceId = this.searchForm.controls[ 'province' ].value;
          if(!provinceId){
            const defaultProvinceId = this.depConfig?.addressDefault?.provinceId;
            this.searchForm.patchValue({province: defaultProvinceId});
            this.provinceChange({value: defaultProvinceId});
          }
        }, 2000);
      }
    }
  }

  // ========================================================== FUNCTION

  generateAddressIs2Cap(data) {
    if (!data || typeof data !== 'object') {
        return '';
    }

    const addressParts = [];

    // Village - hỗ trợ nhiều trường village
    const villageData = data?.village2 || data?.village1 || data?.village || data?.PhuongXa || data?.PhuongXa1;
    if (villageData) {
        const villageName = villageData.label || villageData.name || (typeof villageData === 'string' ? villageData : '');
        if (villageName && typeof villageName === 'string') {
            addressParts.push(villageName.trim());
        }
    }

    // Province - hỗ trợ nhiều trường province
    const provinceData = data?.province2 || data?.province1 || data?.province ||  data?.TinhTP || data?.TinhTP1;
    if (provinceData) {
        const provinceName = provinceData.label || provinceData.name || (typeof provinceData === 'string' ? provinceData : '');
        if (provinceName && typeof provinceName === 'string') {
            addressParts.push(provinceName.trim());
        }
    }

    // Kết thúc bằng dấu chấm nếu có tỉnh/thành phố
    if (addressParts.length > 0) {
        if (addressParts.length === 2) {
            return addressParts[0] + ', ' + addressParts[1] + '.';
        }
        return addressParts.join(', ');
    }
    return '';
  }

  generateAddressFix(data) {
    // return this.mainService.generateAddress(placeObj);
    if (!data || typeof data !== 'object') {
        return '';
    }
    
    const addressParts = [];
    
    // Village
    if (data?.village?.label) {
        addressParts.push(data.village.label);
    }
    
    // District
    if (data?.district?.label) {
        addressParts.push(data.district.label);
    }
    
    // Province
    if (data?.province?.label) {
        addressParts.push(data.province.label);
    }
    
    // Nation
    if (data?.nation?.label) {
        addressParts.push(data.nation.label);
    }
    
    return addressParts.join(', ');
  }

  generateAddress(data) {
    // return this.mainService.generateAddress(placeObj);
    let address = '';
    if (data?.village !== undefined && data?.village !== null) {
      address += data?.village?.label + ', ';
    }
    if (data?.district !== undefined && data?.district !== null) {
      address += data?.district?.label + ', ';
    }
    if (data?.province !== undefined && data?.province !== null) {
      address += data?.province?.label + ', ';
    }
    if (data?.nation !== undefined && data?.nation !== null) {
      address += data?.nation?.label;
    }
    return address;
  }


  checkDateValid(startDate, endDate) {
    return new Promise((resolve) => {
      try {
        if (startDate.getTime() > endDate.getTime()) {
          const msgObj = {
            vi: 'Ngày bắt đầu phải nhỏ hơn ngày kết thúc!',
            en: 'Start date must be lesser than end date!'
          };
          this.snackbarService.openSnackBar(0, '', msgObj[this.selectedLang], 'error_notification', this.config.expiredTime);
          resolve(false);
        } else {
          resolve(true);
        }
      } catch (error) {
        resolve(true);
      }
    });
  }

  generateSearchString(spec, page, size, sort) {
    if(this.isSortBySubmissionDateOnlineReception){
        sort = 'appliedDate,asc';
    }
    else if(this.showSortTypeHCM){
      switch(this.sortId){
        case '1':{
          sort = 'dueDate,asc';
          break;
        }
        case '2':{
          sort = 'dueDate,desc';
          break;
        }
        case '3':{
          sort = 'appliedDate,asc';
          break;
        }
        case '4':{
          sort = 'appliedDate,desc';
          break;
        }
        default: {
          sort = 'dueDate,asc';
        }
      }
    }
   else{
    // IGATESUPP-37926 : sort by sort combobox value
    if (this.showSortComboBox) {
      switch(this.sortId){
        case '1':{
          sort = 'appliedDate,asc';
          break;
        }
        case '2':{
          sort = 'appliedDate,desc';
          break;
        }
      }
    }
  }
    const userAgency = JSON.parse(localStorage.getItem('userAgency'));
    let rootAgencyId: any = '';
    let parentAgencyId = '';
    let agencyTypeIds = [];
    if (!!userAgency) {
      rootAgencyId = userAgency.id;
      if (!!userAgency.parent) {
        parentAgencyId = userAgency.parent.id;
      }
      if (!!userAgency.tag) {
        agencyTypeIds = userAgency.tag;
      }
    }
    let sectorId = '';
    if(this.enableSearchBySector && !!this.listSectorId && this.listSectorId.length !== 0 && !this.listSectorId[0].id){
      sectorId  = this.listSectorId;
    }
    const formObj = this.searchForm.getRawValue();
    if(this.showListAdditionalRequest) {
      this.listStatusViewReceptionPage.push('60f52e6a09cbf91d41f88836', '60f52eaf09cbf91d41f88837');
    }
    if(this.enablePauseWhenAdditional) {
      this.listStatusViewReceptionPage.push(this.overDueAdditionalMenuRemindTaskId);
    }
    if(this.processingFlowStopProcessingNAST == "1"){
      this.listStatusViewReceptionPage = this.listStatusViewReceptionPage.filter(item => item !== '61ee30eada2d36b037e00005');
    }
    let remindId = this.remindId ? this.remindId : this.listStatusViewReceptionPage;
    if (this.isDirectlyPaymentOverdueRemind == true) {
      remindId = "63fb7a261aee0a3ee93c8dac";
    }
    if(this.isDirectlyPaymentDone == true){
      remindId = '6437b0e8fa6bf4afb723c0f7';
    }
    let searchString = 'search?code=' + encodeURIComponent(formObj.code.trim()).substring(0, 1000) +
      '&spec=' + spec +
      '&page=' + page +
      '&size=' + size +
      '&sort=' + sort +
      '&identity-number=' + encodeURIComponent(formObj.identityNumber.trim()).substring(0, 1000) +
      '&applicant-name=' + encodeURIComponent(formObj.applicantName.trim()).substring(0, 1000) +
      '&applicant-owner-name=' + encodeURIComponent(formObj.ownerFullname.trim()).substring(0, 1000) +
      '&nation-id=' + formObj.nation +
      '&province-id=' + formObj.province +
      '&district-id=' + formObj.district +
      '&ward-id=' + formObj.village +
      '&accepted-from=' + (formObj.acceptFrom ? this.datePipe.transform(formObj.acceptFrom, 'dd/MM/yyyy') : '') +
      '&accepted-to=' + (formObj.acceptTo ? this.datePipe.transform(formObj.acceptTo, 'dd/MM/yyyy') : '') +
      '&dossier-status=' + formObj.dossierStatus +
      '&agency-id=' + rootAgencyId +
      '&parent-agency-id=' + parentAgencyId +
      '&agency-type-id=' + agencyTypeIds.toString() +
      '&filter-by-position-agency-type=' + this.filterByPositionAgencyType +
      '&remind-id=' +  remindId +
      '&applicant-organization='+ encodeURIComponent(formObj.applicantOrganization.trim()).substring(0, 1000) +
      '&taxCode='+ formObj.taxCode +
      '&exclude-dossier-by-kiosk=true' +
      '&resPerson=' +formObj.resPerson +
      '&is-get-updatedDate='+ this.filterSubmissionDate +
      '&province1=' + formObj.province1 +
      '&district1=' + formObj.district1 +
      '&village1=' + formObj.village1;
    if (this.filterByPositionAgencyType){
      let positionId = '';
      if (this.userExperienceAgency !== null) {
        positionId = this.userExperienceAgency.position.id;
      }
      searchString = searchString + '&position-id=' + positionId;
    }

    const applyMethod = this.applyMethod?.value[0];
    if(applyMethod){
      searchString += applyMethod !== '3'?'&apply-method-id=' + applyMethod:'&fpt-dossier=1';
    }
    if (!!this.listSectorUser || formObj.sectorCtrl !== null && formObj.sectorCtrl.replace(/\s+/g, '') != '' ) {
      searchString += '&sector-id=' + formObj.sectorCtrl;
      if(formObj.sectorCtrl.trim()==""){
        if(this.enableSectorUserSearch && !!this.listSectorUser){
          searchString += this.listSectorUser;
        }else{
          searchString += sectorId;
        }

      }
    }
    if (formObj.procedureCtrl !== null) {
      searchString += '&procedure-id=' + formObj.procedureCtrl;
    }
    if (this.isFFOTask) {
      searchString += '&has-dutypaid-cert=' + formObj.hasDutypaidCert;
    }

    if(this.env?.vnpost?.config && this.env?.vnpost?.config === '1')
    {
      searchString += '&vnpost-status-code=' + formObj.vnpostStatus;
    }

    if(this.showAppliedDateFilter || this.isShowFilterApplyDate){
      searchString += '&applied-from=' + (formObj.appliedFrom ? this.datePipe.transform(formObj.appliedFrom, 'dd/MM/yyyy') : '') +
      '&applied-to=' + (formObj.appliedTo ? this.datePipe.transform(formObj.appliedTo, 'dd/MM/yyyy') : '');
    }

    if (this.isDirectlyPaymentOverdueRemind) {
      searchString += "&directly-payment-overdue=true";
    }


    if (this.deadlineForAdditionalRequests.enable && formObj.additionalRequestDeadline !== "") {
      searchString += '&additional-request-deadline=' + formObj.additionalRequestDeadline;
    }

    if (remindId == '60f52e6a09cbf91d41f88836') {
      searchString += '&task-status-id=60ebf03109cbf91d41f87f8b'
    }
    if (remindId == '60f52eaf09cbf91d41f88837') {
      searchString += '&task-status-id=60ebf0db09cbf91d41f87f8c'
    }

    if (this.filterSectorOfQNI) {
      searchString += "&filter-by-sector=" + this.filterSectorOfQNI;
    }
    
    if(this.searchPaymentMethod){
      searchString += "&paystatus=" + formObj.payStatus + "&paymentMethod="+ formObj.paymentMethod;
    }
    
    if(formObj.dossierSeen){
      searchString += "&filter-officer-dossier-qualifi-recept=true";
    }
    if(this.checkShowFilterRegisterApplicationVNeID) {
      searchString += "&includeVNeID=true" 
    }

    return searchString;
  }

  remakeRequestUrl(searchString) {
    const formObj = this.searchForm.getRawValue();

    searchString = searchString.replace('search?', '');
    const searchStringObj = tUtils.parseParams(searchString);
    if (this.deploymentService.optimize.dossierSearch.codeMatch) {
      searchStringObj['code-match'] = searchStringObj['code'];
      delete searchStringObj['code'];

      searchStringObj['nation-code-match'] = formObj.nationCode;
    }

    if (this.deploymentService.optimize.dossierSearch.onlineReceptionSort.length !== 0) {
      searchStringObj['sort'] = this.deploymentService.optimize.dossierSearch.onlineReceptionSort;
    }

    const params = new URLSearchParams(searchStringObj);
    searchString = 'search?' + params.toString();

    return searchString;
  }

  autoSearch() {
    this.pageIndex = Number(this.paramsQuery.page);
    this.page = Number(this.paramsQuery.page);
    this.size = Number(this.paramsQuery.size);
    let array: string[] = this.deploymentService.env.OS_HCM.listAgencyUseSortType;
    var parentAgencyId = (this.userAgency.parent != null && this.userAgency.parent != undefined) ? this.userAgency.parent.id : null;
    if(array.filter((value) => (value.includes(this.userAgency.id) || (parentAgencyId != null &&  value.includes(parentAgencyId)))).length > 0 && this.env?.OS_HCM?.dossierArrSortType !== undefined){
      if (!sessionStorage.getItem('dossierArrSortType')) {
        sessionStorage.setItem('dossierArrSortType', this.env?.OS_HCM?.dossierArrSortType);
      }
      this.sortId = sessionStorage.getItem('dossierArrSortType');
    }else{
      sessionStorage.removeItem('dossierArrSortType');
    }
    const searchString = this.generateSearchString(this.paginationType, (this.page - 1), this.size, 'id,desc');
    this.getListDossier(searchString);
  }

  // paginate(event: any, type) {
  paginate(){
    const searchString = this.generateSearchString(this.paginationType, (this.pageIndex - 1), this.size, 'id,desc');
    this.getListDossier(searchString);
    this.router.navigate([], {
      queryParams: {
        code: this.paramsQuery.code,
        identity: this.paramsQuery.identity.trim(),
        applicant: this.paramsQuery.applicant.trim(),
        page: this.pageIndex,
        size: this.size,
        procedure: this.paramsQuery.procedure,
        sector: this.paramsQuery.sector,
        province: this.paramsQuery.province,
        district: this.paramsQuery.district,
        village: this.paramsQuery.village,
        acceptFrom: (this.paramsQuery.acceptFrom ? this.datePipe.transform(this.paramsQuery.acceptFrom, 'dd/MM/yyyy') : ''),
        acceptTo: (this.paramsQuery.acceptTo ? this.datePipe.transform(this.paramsQuery.acceptTo, 'dd/MM/yyyy') : ''),
        remindId: this.paramsQuery.remindId,
        appliedFrom: (this.showAppliedDateFilter && this.paramsQuery.appliedFrom ? this.datePipe.transform(this.paramsQuery.appliedFrom, 'dd/MM/yyyy') : ''),
        appliedTo: (this.showAppliedDateFilter && this.paramsQuery.appliedTo ? this.datePipe.transform(this.paramsQuery.appliedTo, 'dd/MM/yyyy') : ''),
        sortId: this.sortId,
        additionalRequestDeadline: this.additionalRequestDeadline,
        payStatus: this.paramsQuery.payStatus,
        paymentMethod: this.paramsQuery.paymentMethod,

      }
    });
    // switch (type) {
    //   case 0:
    //     this.pageIndex = event;
    //     this.page = event;
    //     const searchString = this.generateSearchString(this.paginationType, (this.pageIndex - 1), this.size, 'id,desc');
    //     this.getListDossier(searchString);
    //     this.router.navigate([], {
    //       queryParams: {
    //         code: this.paramsQuery.code,
    //         identity: this.paramsQuery.identity.trim(),
    //         applicant: this.paramsQuery.applicant.trim(),
    //         page: this.pageIndex,
    //         size: this.size,
    //         procedure: this.paramsQuery.procedure,
    //         sector: this.paramsQuery.sector,
    //         province: this.paramsQuery.province,
    //         district: this.paramsQuery.district,
    //         village: this.paramsQuery.village,
    //         acceptFrom: (this.paramsQuery.acceptFrom ? this.datePipe.transform(this.paramsQuery.acceptFrom, 'dd/MM/yyyy') : ''),
    //         acceptTo: (this.paramsQuery.acceptTo ? this.datePipe.transform(this.paramsQuery.acceptTo, 'dd/MM/yyyy') : ''),
    //         remindId: this.paramsQuery.remindId
    //       }
    //     });
    //     break;
    //   case 1:
    //     this.pageIndex = 1;
    //     this.page = 1;
    //     const searchString2 = this.generateSearchString(this.paginationType, (this.pageIndex - 1), this.size, 'id,desc');
    //     this.getListDossier(searchString2);
    //     this.router.navigate([], {
    //       queryParams: {
    //         code: this.paramsQuery.code,
    //         identity: this.paramsQuery.identity.trim(),
    //         applicant: this.paramsQuery.applicant.trim(),
    //         page: 1,
    //         size: this.size,
    //         procedure: this.paramsQuery.procedure,
    //         sector: this.paramsQuery.sector,
    //         province: this.paramsQuery.province,
    //         district: this.paramsQuery.district,
    //         village: this.paramsQuery.village,
    //         acceptFrom: (this.paramsQuery.acceptFrom ? this.datePipe.transform(this.paramsQuery.acceptFrom, 'dd/MM/yyyy') : ''),
    //         acceptTo: (this.paramsQuery.acceptTo ? this.datePipe.transform(this.paramsQuery.acceptTo, 'dd/MM/yyyy') : ''),
    //         remindId: this.paramsQuery.remindId
    //       }
    //     });
    //     break;
    // }
  }

  isAllSelected() {
    const numSelected = this.selection.selected.length;
    const numRows = this.dataSource.data.length;
    return numSelected === numRows;
  }

  masterToggle() {
    this.isAllSelected() ?
      this.selection.clear() :
      this.dataSource.data.forEach(row => this.selection.select(row));
  }

  checkboxLabel(row?: any): string {
    if (!row) {
      return `${this.isAllSelected() ? 'select' : 'deselect'} all`;
    }
    return `${this.selection.isSelected(row) ? 'deselect' : 'select'} row ${row.position + 1}`;
  }

  protected filterSector(search) {
    if (!this.sectors) {
      return;
    }
    // let search = encodeURIComponent(this.searchSectorCtrl.value.trim()).substring(0, 1000);
    this.searchSectorKeyword = search;
    if (!search) {
      this.sectorFiltered.next(this.sectors.slice());
      return;
    } else {
      search = search.toLowerCase();
    }
    if (!!this.listSectorUser || this.sectors.filter(sec => sec.name.toLowerCase().indexOf(search) > -1).length > 0) {
      this.sectorFiltered.next(
        this.sectors.filter(sec => sec.name.toLowerCase().indexOf(search) > -1)
      );
    } else {
      this.listSectorPage = 0;
      const searchString = '?keyword=' + search + '&page=0&size=1000&spec=' + this.paginationType + '&sort=name.name,asc&status=1';
      this.procedureService.getListSector(searchString).subscribe(data => {
        for (let i = 0; i < data.numberOfElements; i++) {
          this.listSector.push(data.content[i]);
        }
        this.listSector.sort((a, b) => a.name.localeCompare(b.name));
        this.sectors = JSON.parse(JSON.stringify(this.listSector).replace(/null/g, '""'));
        this.sectorFiltered.next(
          this.sectors.filter(sec => sec.name.toLowerCase().indexOf(search) > -1)
        );
      }, err => {
        console.log(err);
      });
    }
  }

  protected filterProcedure(search) {
    if (!this.procedures) {
      return;
    }
    // let search = encodeURIComponent(this.searchProcedureCtrl.value.trim()).substring(0, 1000);
    this.searchProcedureKeyword = search;
    if (!search) {
      this.procedureFiltered.next(this.procedures.slice());
      return;
    } else {
      search = search.toLowerCase();
    }
    if (this.procedures.filter(proc => proc.name.toLowerCase().indexOf(search) > -1).length > 0) {
      this.procedureFiltered.next(
        this.procedures.filter(proc => proc.name.toLowerCase().indexOf(search) > -1)
      );
    } else {
      // tslint:disable-next-line: max-line-length
      let agencyIdSearch = '';
      // if ( this.userAgency !== null && this.userAgency !== undefined && !this.isAdmin) {
      if ( this.userAgency !== null && this.userAgency !== undefined && !this.isAdmin) {
        if (!!this.userAgency.parent && !!this.userAgency.parent.id) {
          agencyIdSearch = `&agency-id=${this.userAgency.parent.id}`;
        } else if (this.userAgency.id !== this.config.rootAgency.id) {
          agencyIdSearch = `&agency-id=${this.userAgency.id}`;
        }
      }
      agencyIdSearch = this.env?.filterProcedureByAgencyId ? agencyIdSearch : '';
      const searchString =
        '?status=1&sort=translate.name,asc&keyword=' + search +
        '&spec=' + this.paginationType + '&page=0&size=50' +
        '&sector-id=' + this.paramsQuery.sector + agencyIdSearch;
      this.listProcedurePage = 0;
      this.procedureService.getListProcedure(searchString).subscribe(data => {
        for (let i = 0; i < data.numberOfElements; i++) {
          this.listProcedure.push(data.content[i]);
        }
        this.procedures = JSON.parse(JSON.stringify(this.listProcedure).replace(/null/g, '""'));
        this.procedureFiltered.next(
          this.procedures
        );
      }, err => {
        console.log(err);
      });
    }
  }

  // ========================================================== DIALOGS

  processSelection(procedureId, userName, dossierId, appliedDate) {
    const dialogData = new ConfirmProcessSelectionDialogModel(procedureId, userName, dossierId, appliedDate);
    const dialogRef = this.dialog.open(ProcessSelectionComponent, {
      minWidth: '50vw',
      data: dialogData,
      disableClose: true,
      autoFocus: false
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
      const res = dialogResult;
      if (res === true) {
      }
      if (res === false) {
      }
    });
  }

  receivingDossier(event:MouseEvent,id, procedure, procedureProcessDef, dossierStatusId) {
    const isOpenInNewTabHCM = this.deploymentService.env?.OS_HCM?.isOpenInNewTabHCM
    const isOpenInNewTab = this.deploymentService.getAppDeployment()?.env?.OS_KTM?.openInNewTab == true ? true  : false;
    if (this.deploymentService.env?.disableCancelDossier && dossierStatusId === 12) {
      return;
    }
    if (isOpenInNewTab) {
      const url = this.router.serializeUrl(
        this.router.createUrlTree(['dossier/online-reception/receiving/' + id], {
          queryParams: {
            procedure,
            procedureProcessDef
          }
        })
      );

      window.open(url, '_blank');
      return;
    }
    if(isOpenInNewTabHCM&& event.ctrlKey){
      const url = this.router.serializeUrl(
        this.router.createUrlTree(['dossier/online-reception/receiving/' + id], {
          queryParams: {
            procedure,
            procedureProcessDef
          }
        }),
      );
      window.open(url, '_blank');
      return;
    }
    this.router.navigate(['dossier/online-reception/receiving/' + id], {
      queryParams: {
        procedure,
        procedureProcessDef
      }
    });
  }
  onRightClick(id, procedure, procedureProcessDef){
    const queryParamsObject = {
      procedure: procedure,
      procedureProcessDef
    };
    const isOpenInNewTabHCM = this.deploymentService.env?.OS_HCM?.isOpenInNewTabHCM
    if(isOpenInNewTabHCM){
    const url = this.router.serializeUrl(
      this.router.createUrlTree(['dossier/online-reception/receiving/' + id], {
          queryParams: queryParamsObject,
        }),
    );
    window.open(url, '_blank');
    return;
    }
  }
  
  payment(id){
    if(this.enablePaymentHPG) {
      // Gọi api cập nhật hình thức thanh toán từ bất kể hình thức gì sang thanh toán trực tuyến
      const rs = {
          id: "5fd1c7991b53d8779bc9ea7a",
          name: "Thanh toán DVC QG",
          code: "VNPT_PAYMENT_PLATFORM",
          note: "paymentLater"
      };
      const putBody = {
          paymentMethod: rs
      };
      const requestBody = JSON.stringify(putBody, null, 2);
      this.dossierService.putDossierOnline(id, requestBody).subscribe(data => {
              if (data.affectedRows === 1) {
                this.router.navigate(['/dossier/payment/' + id], {});
              } else {
                  const msgObj = {
                      vi: 'Cập nhật hình thức thanh toán thất bại!',
                      en: 'Update payment method failed!'
                  };
                  this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
              }
          });
    } else {
    this.router.navigate(['/dossier/payment/' + id], {});
    }
  }

  deleteDialog(dossierId, dossierCode) {
    const dialogData = new ConfirmDeleteDialogModel(dossierId, dossierCode);
    const dialogRef = this.dialog.open(DeleteDossierComponent, {
      width: '500px',
      data: dialogData,
      disableClose: true,
      autoFocus: false
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
      if (dialogResult === 0) {
        const msgObj = {
          vi: 'Đã xoá hồ sơ!',
          en: 'Dossier deleted!'
        };
        this.snackbarService.openSnackBar(1, msgObj[this.selectedLang], '', 'success_notification', this.config.expiredTime);
        const searchString = this.generateSearchString(this.paginationType, (this.pageIndex - 1), this.size, 'id,desc');
        this.getListDossier(searchString);
      }

      if (dialogResult === 1) {
        const msgObj = {
          vi: 'Đã hủy hồ sơ!',
          en: 'Dossier cancelled!'
        };
        this.snackbarService.openSnackBar(1, msgObj[this.selectedLang], '', 'success_notification', this.config.expiredTime);
        const searchString = this.generateSearchString(this.paginationType, (this.pageIndex - 1), this.size, 'id,desc');
        this.getListDossier(searchString);
      }

      if (dialogResult === null) {
        const msgObj = {
          vi: 'Xoá không thành công!',
          en: 'Deletion failed!'
        };
        this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
      }
      this.callRemindTask();
    });
  }

  viewUpdateHistory(dossierId, dossierCode) {
    const dialogData = new UpdateHistoryDialogModel(dossierId, dossierCode);
    const dialogRef = this.dialog.open(UpdateHistoryComponent, {
      minWidth: '70vw',
      data: dialogData,
      disableClose: true,
      autoFocus: false
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
    });
  }

  returnDossierAdditionalRequest(dossierId, dossierCode){
    const dialogData = new ReturnAdditionalRequestDialogModel(dossierId, dossierCode);
    const dialogRef = this.dialog.open(ReturnAdditionalDossierComponent, {
      minWidth: '50vw',
      maxHeight: '75vh',
      data: dialogData,
      disableClose: true,
      autoFocus: false
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
      if(dialogResult){
        const msgObj = {
          vi: 'Trả hồ sơ do quá hạn bổ sung thành công!',
          en: 'Returned addional request dossier overdue successful!',
        };
        this.snackbarService.openSnackBar(
          1,
          '',
          msgObj[this.selectedLang],
          'warning_notification',
          this.config.expiredTime
        );
        this.onConfirmSearch();
      }
    });
  }

  refuseDialog(dossierId, code, dossierStatus?) {
    const dialogData = new RefuseDialogModel(dossierId, code, dossierStatus);
    const dialogRef = this.dialog.open(RefuseComponent, {
      minWidth: '50vw',
      maxHeight: '75vh',
      data: dialogData,
      disableClose: true,
      autoFocus: false
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
      const res = dialogResult;
      if (res === true) {
        const msgObj = {
          vi: 'Đã từ chối hồ sơ!',
          en: 'Dossier refused!'
        };
        this.snackbarService.openSnackBar(1, msgObj[this.selectedLang], '', 'success_notification', this.config.expiredTime);
        const searchString = this.generateSearchString(this.paginationType, (this.pageIndex - 1), this.size, 'id,desc');
        this.getListDossier(searchString);
        this.callRemindTask();
      }
      if (res === false) {
        const msgObj = {
          vi: 'Từ chối hồ sơ không thành công!',
          en: 'Refuse failed!'
        };
        this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
      }
    });
  }

  additionalRequirement(dossierId, dossierCode) {
    const dialogData = new ConfirmAdditionalRequirementDialogModel(dossierId, dossierCode, 2);
    const dialogRef = this.dialog.open(AdditionalRequirementComponent, {
      minWidth: '50vw',
      maxHeight: "85vh",
      data: dialogData,
      disableClose: true,
      autoFocus: false
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
      if (dialogResult === true) {
        const msgObj = {
          vi: 'Đã yêu cầu bổ sung!',
          en: 'Additional request sent!'
        };
        this.snackbarService.openSnackBar(1, msgObj[this.selectedLang], '', 'success_notification', this.config.expiredTime);
        const searchString = this.generateSearchString(this.paginationType, (this.pageIndex - 1), this.size, 'id,desc');
        this.getListDossier(searchString);
        this.callRemindTask();
        this.notiService.confirmSendSubject.next({
          confirm: true,
          renewContent: false
        });
      }
      if (dialogResult === false) {
        const msgObj = {
          vi: 'Yêu cầu bổ sung không thành công!',
          en: 'Additional request failed!'
        };
        this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
      }
    });
  }

  additionalRequirementPaper(dossierId, dossierCode) {
    const dialogData = new ConfirmAdditionalRequirementPaperDialogModel(dossierId, dossierCode, 2);
    const dialogRef = this.dialog.open(AdditionalRequirementPaperComponent, {
      minWidth: '50vw',
      maxHeight: "65vh",
      data: dialogData,
      disableClose: true,
      autoFocus: false
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
      if (dialogResult === true) {
        const msgObj = {
          vi: 'Đã yêu cầu bổ sung bản giấy!',
          en: 'Additional request sent!'
        };
        this.snackbarService.openSnackBar(1, msgObj[this.selectedLang], '', 'success_notification', this.config.expiredTime);
        const searchString = this.generateSearchString(this.paginationType, (this.pageIndex - 1), this.size, 'id,desc');
        this.getListDossier(searchString);
        this.callRemindTask();
        this.notiService.confirmSendSubject.next({
          confirm: true,
          renewContent: false
        });
      }
      if (dialogResult === false) {
        const msgObj = {
          vi: 'Yêu cầu bổ sung không thành công!',
          en: 'Additional request failed!'
        };
        this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
      }
    });
  }

  paymentRequest(dossierId, dossierCode) {
    const dialogData = new ConfirmPaymentRequestDialogModel(dossierId, dossierCode, 2);
    const dialogRef = this.dialog.open(PaymentRequestComponent, {
      minWidth: '50vw',
      maxHeight: "85vh",
      data: dialogData,
      disableClose: true,
      autoFocus: false
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
      if (dialogResult === true) {
        const msgObj = {
          vi: 'Đã yêu cầu thanh toán!',
          en: 'Payment request sent!'
        };
        this.snackbarService.openSnackBar(1, msgObj[this.selectedLang], '', 'success_notification', this.config.expiredTime);
        const searchString = this.generateSearchString(this.paginationType, (this.pageIndex - 1), this.size, 'id,desc');
        this.getListDossier(searchString);
        this.callRemindTask();
        this.notiService.confirmSendSubject.next({
          confirm: true,
          renewContent: false
        });
      }
      if (dialogResult === false) {
        const msgObj = {
          vi: 'Yêu cầu Thanh toán không thành công!',
          en: 'Payment request failed!'
        };
        this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
      }
    });
  }

  allowDirectlyPaymentRequest(dossierId, dossierCode) {
    //nhipttt-IGATESUPP-79392
    const dialogData = new ChangeDirectPaymentDialogModel(dossierId, dossierCode);
    const dialogRef = this.dialog.open(ChangeDirectPaymentComponent, {
      minWidth: '50vw',
      maxHeight: "85vh",
      data: dialogData,
      disableClose: true,
      autoFocus: false
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
      if (dialogResult === true) {
        const msgObj = {
          vi: 'Đã chuyển đổi hình thức yêu cầu thanh toán!',
          en: 'Payment request sent!'
        };
        this.snackbarService.openSnackBar(1, msgObj[this.selectedLang], '', 'success_notification', this.config.expiredTime);
        const searchString = this.generateSearchString(this.paginationType, (this.pageIndex - 1), this.size, 'id,desc');
        this.getListDossier(searchString);
        this.callRemindTask();
        this.notiService.confirmSendSubject.next({
          confirm: true,
          renewContent: false
        });
      }
      if (dialogResult === false) {
        const msgObj = {
          vi: 'Yêu cầu chuyển đổi hình thức thanh toán không thành công!',
          en: 'Payment request failed!'
        };
        this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
      }
    });
  }

  additionalRequirementPaperAndPayment(dossierId, dossierCode) {
    const dialogData = new ConfirmAdditionalRequiremenPaperAndPaymentDialogModel(dossierId, dossierCode, 2 );
    const dialogRef = this.dialog.open(AdditionalRequiremenPaperAndPaymentComponent, {
      minWidth: '50vw',
      maxHeight: "85vh",
      data: dialogData,
      disableClose: true,
      autoFocus: false
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
      if (dialogResult === true) {
        const msgObj = {
          vi: 'Đã yêu cầu nộp hồ sơ giấy và thanh toán!',
          en: 'Payment request sent!'
        };
        this.snackbarService.openSnackBar(1, msgObj[this.selectedLang], '', 'success_notification', this.config.expiredTime);
        const searchString = this.generateSearchString(this.paginationType, (this.pageIndex - 1), this.size, 'id,desc');
        this.getListDossier(searchString);
        this.callRemindTask();
        this.notiService.confirmSendSubject.next({
          confirm: true,
          renewContent: false
        });
      }
      if (dialogResult === false) {
        const msgObj = {
          vi: 'Yêu cầu nộp hồ sơ giấy và thanh toán không thành công!',
          en: 'Payment request failed!'
        };
        this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
      }
    });
  }

  directlyPaymentRequest(dossierId, dossierCode) {
    const dialogData = new ConfirmDirectlyPaymentNotificationDialogModel(dossierId, dossierCode, 2);
    const dialogRef = this.dialog.open(DirectlyPaymentNotificationComponent, {
      minWidth: '50vw',
      maxHeight: "85vh",
      data: dialogData,
      disableClose: true,
      autoFocus: false
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
      if (dialogResult === true) {
        const msgObj = {
          vi: 'Gửi thông báo thành công!',
          en: 'Send notification successfully!'
        };
        this.snackbarService.openSnackBar(1, msgObj[this.selectedLang], '', 'success_notification', this.config.expiredTime);
        // this.notiService.confirmSendSubject.next({
        //   confirm: true,
        //   renewContent: false
        // });
        const searchString = this.generateSearchString(this.paginationType, (this.pageIndex - 1), this.size, 'id,desc');
        this.getListDossier(searchString);
        this.callRemindTask();
      }
      if (dialogResult === false) {
        const msgObj = {
          vi: 'Gửi thông báo không thành công!',
          en: 'Send notification failed!'
        };
        this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
      }
    });
  }

  withdrawDialogs(dossierId, dossierCode){
    if (dossierCode === undefined || dossierCode === null) {
      dossierCode = '';
    }
    const dialogData = new WithdrawModel(dossierId, dossierCode);
    const dialogRef = this.dialog.open(WithdrawComponent, {
      minWidth: '45vw',
      data: dialogData,
      disableClose: true,
      autoFocus: false
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
      if (dialogResult === true) {
        const msgObj = {
          vi: 'Đã rút hồ sơ!',
          en: 'Dossier has been withdraw!'
        };
        this.snackbarService.openSnackBar(1, msgObj[localStorage.getItem('language')], '', 'success_notification', this.config.expiredTime);
        const searchString = this.generateSearchString(this.paginationType, (this.pageIndex - 1), this.size, 'id,desc');
        this.getListDossier(searchString);
        this.callRemindTask();
      }
      if (dialogResult === false) {
        const msgObj = {
          vi: 'Rút hồ sơ không thành công!',
          en: 'Withdraw dossier failed!'
        };
        this.snackbarService.openSnackBar(0, msgObj[localStorage.getItem('language')], '', 'error_notification', this.config.expiredTime);
      }
    });
  }


  viewProcess(prDefId) {
    this.procedureService.getProcedureProcessDetail(prDefId).subscribe(data => {
      if (data !== undefined && data !== null) {
        // tslint:disable-next-line: max-line-length
        const dialogData = new ViewProcessDiagramDialogModel(data.processDefinition.activiti.model.id, data.processDefinition.name, data.processDefinition.id);
        const dialogRef = this.dialog.open(ViewProcessDiagramComponent, {
          width: '80vw',
          minHeight: '80vh',
          data: dialogData,
          disableClose: true,
          autoFocus: false
        });
        dialogRef.afterClosed().subscribe(dialogResult => {
        });
      } else {
        const msgObj = {
          vi: 'Không tìm thấy quy trình cho hồ sơ này!',
          en: 'No process found for this dossier!'
        };
        this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
      }
    }, err => {
      const msgObj = {
        vi: 'Không tìm thấy quy trình cho hồ sơ này!',
        en: 'No process found for this dossier!'
      };
      this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
    });
  }
  // ======== Menu remind
  async getRemindMenuTask(){
    // //debugger;
    let formObj = this.searchForm.getRawValue();
    const userAgency = JSON.parse(localStorage.getItem('userAgency'));
    let rootAgencyId: any = '';
    let parentAgencyId = '';
    let agencyTypeIds = [];
    if (!!userAgency) {
      rootAgencyId = userAgency.id;
      if (!!userAgency.parent) {
        parentAgencyId = userAgency.parent.id;
      }
      if (!!userAgency.tag) {
        agencyTypeIds = userAgency.tag;
      }
    }
    let sectorId = '';
    if(this.enableSearchBySector && !!this.listSectorId && this.listSectorId.length !== 0 && !this.listSectorId[0].id){
      sectorId  = this.listSectorId;
    }
    if(this.enableSectorUserSearch && !!this.listSectorUser){
      sectorId = this.listSectorUser;
    }

    this.listMenuRemind = [];
    // tslint:disable-next-line:max-line-length
    let searchString = '&in-list=1'
                        + '&agency-id=' + rootAgencyId
                        + '&parent-agency-id=' + parentAgencyId
                        + '&agency-type-id=' + agencyTypeIds.toString()
                        + '&filter-by-position-agency-type=' + this.filterByPositionAgencyType
                        + '&sector-id=' + sectorId
                        + '&exclude-dossier-by-kiosk=true'
    let additionalRequirementMenuTaskRemindsearchString = searchString;
    if(this.showStopProcessing){
      if(this.processingFlowStopProcessingNAST == "1"){
        searchString+='&dossier-status=0,1,3,14,15,17,18,19,20,21,22';
      }else{
        searchString+='&dossier-status=0,1,3,12,14,15,17,18,19,20,21,22';
      }
    }else{
      searchString+='&dossier-status=0,1,3,14,15,17,18,19,20,21,22';
    }
    if(this.filterAppliedDateMenuRemind){
       searchString += '&applied-from=' + (formObj.appliedFrom ? this.datePipe.transform(formObj.appliedFrom, 'dd/MM/yyyy') : '') +
      '&applied-to=' + (formObj.appliedTo ? this.datePipe.transform(formObj.appliedTo, 'dd/MM/yyyy') : '');
      additionalRequirementMenuTaskRemindsearchString += '&applied-from=' + (formObj.appliedFrom ? this.datePipe.transform(formObj.appliedFrom, 'dd/MM/yyyy') : '') +
      '&applied-to=' + (formObj.appliedTo ? this.datePipe.transform(formObj.appliedTo, 'dd/MM/yyyy') : '');
    }

    if (this.filterSectorOfQNI) {
      searchString += "&filter-by-sector=" + this.filterSectorOfQNI;
    }


      //Lấy MenuTaskRemind hồ sơ yêu cầu bổ sung tách riêng
      if(this.showListAdditionalRequest){
        this.getNumberAdditionalRequirementDossierMenuTaskRemind(searchString);
        this.getNumberAddedDossierMenuTaskRemind(searchString);
      }

    if (this.showListAdditionalRequest) {
      let indexOfAdditionalRequestJob = this.listStatusViewReceptionPage.indexOf('60f52e6a09cbf91d41f88836');
      if (indexOfAdditionalRequestJob != null && indexOfAdditionalRequestJob != undefined) {
        this.listStatusViewReceptionPage.splice(indexOfAdditionalRequestJob, 1);
      }
      let indexOfAddedDossierJob = this.listStatusViewReceptionPage.indexOf('60f52eaf09cbf91d41f88837');
      if (indexOfAdditionalRequestJob != null && indexOfAdditionalRequestJob != undefined) {
        this.listStatusViewReceptionPage.splice(indexOfAddedDossierJob, 1);
      }
    }

    if(this.enablePauseWhenAdditional){
      this.listStatusViewReceptionPage.push(this.overDueAdditionalMenuRemindTaskId);
    }


    searchString += '&remind-id=' + this.listStatusViewReceptionPage;

    if (this.filterByPositionAgencyType){
      let positionId = '';
      if (this.userExperienceAgency !== null) {
        positionId = this.userExperienceAgency.position.id;
      }
      searchString = searchString + '&position-id=' + positionId;
      additionalRequirementMenuTaskRemindsearchString += '&position-id=' + positionId;
    }

    // STAGE: 1 => Ho so cho tiep nhan
    if (this.isNotification && this.isNotificationStage1) {
      const searchRemind = 'stage=1' +
        '&assignee-id=' + this.userId +
        '&position-id=' + this.userExperienceAgency.position.id +
        '&agency-id=' + this.userAgency.id;

      this.notificationV2Service.getNotificationCurrent(searchRemind).subscribe(data => {
        for (const item of data) {
          if (item.id !== this.config.dossierReminderRecallId) {
            this.listMenuRemind.push(item);
          }
        }
        this.lengthRemind = this.listMenuRemind.length;
        if (this.enableShowDirectlyPaymentStatus === true) {
          this.lengthRemind = this.lengthRemind + 1;
        }
        this.listMenuRemind = this.listMenuRemind.sort((a, b) => {
          return compare(a.count, b.count, false);
        });
        if (data.content?.length === 0) {
          this.expandReminderMenu = false;
        }
        // HCM
        if (this.numberDateAdditionalRequirement && this.numberDateAdditionalRequirement.enable) {
          this.getNumberDateAdditionalRequirementMenuTaskRemind(additionalRequirementMenuTaskRemindsearchString);
        }
      });
    } else {
    if(!this.deploymentService.getAppDeployment()?.disableMenuRemind)
    {
    this.dossierService.getDossierMenuTaskRemindAllAgency(searchString).subscribe(data => {
      //this.listMenuRemind = data.content;
      // tslint:disable-next-line:prefer-for-of
      for (let i = 0; i < data.length; i++){
        if (data[i].id !== this.config.dossierReminderRecallId){
          this.listMenuRemind.push(data[i]);
        }
      }
      this.lengthRemind = this.listMenuRemind.length;
      if(this.enableShowDirectlyPaymentStatus== true){
        this.lengthRemind = this.lengthRemind + 1;
      }
      this.listMenuRemind = this.listMenuRemind.sort((a, b) => {
        return compare(a.count, b.count, false);
      });
      if (data.content?.length === 0){
        this.expandReminderMenu = false;
      }
      //Lấy MenuTaskRemind hồ sơ có hạn yêu cầu bổ sung
      if(this.numberDateAdditionalRequirement && this.numberDateAdditionalRequirement.enable){
        this.getNumberDateAdditionalRequirementMenuTaskRemind(additionalRequirementMenuTaskRemindsearchString);
      }
    });
    }
    }
    if(this.enableShowDirectlyPaymentStatus == true){
      let idReminDirectlyPaymentDone = '6437b0e8fa6bf4afb723c0f7';
      let search2 = 'remind-id=' + idReminDirectlyPaymentDone
      + '&in-list=1'
      + '&agency-id=' + rootAgencyId
      + '&parent-agency-id=' + parentAgencyId
      + '&agency-type-id=' + agencyTypeIds.toString()
      + '&filter-by-position-agency-type=' + this.filterByPositionAgencyType
      + '&sector-id=' + sectorId
      if(this.showStopProcessing){
        searchString +='&dossier-status=0,1,3,12,14,15,17,18,19,20,21,22';
      }else{
        searchString +='&dossier-status=0,1,3,14,15,17,18,19,20,21,22';
      }
      if (this.filterByPositionAgencyType){
        let positionId = '';
        if (this.userExperienceAgency !== null) {
          positionId = this.userExperienceAgency.position.id;
        }
        search2 = search2 + '&position-id=' + positionId;
      }
      if(!this.deploymentService.getAppDeployment()?.disableMenuRemind)
      {
      const data2 = await this.dossierService.getDossierMenuTaskRemindAllAgency(search2).toPromise();
      //console.log('data2', data2);
      this.countDirectlyPaymentDone = data2[0].count;
      }
  }
  }
  getNumberDateAdditionalRequirementMenuTaskRemind(searchString){
    searchString += '&is-date-require=true'
                + '&date-require-status-id=' + this.numberDateAdditionalRequirement.statusId;
    searchString +='&dossier-status=1';
    searchString += '&remind-id=60f52e6a09cbf91d41f88836';
    if(!this.deploymentService.getAppDeployment()?.disableMenuRemind)
    {
    this.dossierService.getDossierMenuTaskRemindAllAgency(searchString).subscribe(data => {
      //this.listMenuRemind = data.content;
      // tslint:disable-next-line:prefer-for-of
      for (let i = 0; i < data.length; i++){
        if (data[i].id !== this.config.dossierReminderRecallId){
          data[i].idRes = data[i].id;
          data[i].id = this.numberDateAdditionalRequirement.statusId;
          data[i].name = this.numberDateName[this.selectedLang];
          this.listMenuRemind.push(data[i]);
        }
      }
      this.lengthRemind = this.listMenuRemind.length;
      if(this.enableShowDirectlyPaymentStatus== true){
        this.lengthRemind = this.lengthRemind + 1;
      }
      this.listMenuRemind = this.listMenuRemind.sort((a, b) => {
        return compare(a.count, b.count, false);
      });
      if (data.content?.length === 0){
        this.expandReminderMenu = false;
      }
    });
    }
  }
  onClickOpenReminderMenu(){
    this.expandReminderMenu = !this.expandReminderMenu;
    this.changeSearchRemind('');
  }
  changeSearchRemind(remindId){
    this.isDirectlyPaymentOverdueRemind = false;
    this.isDirectlyPaymentDone = false;
    this.isDirectlyAdditionalRequirementDossier = false;
    this.isDirectlyisDirectlyAddedDossier = false;
    const applicant = this.env?.isOwnerFullname === 1 ? "profileOwner" : "applicant";
    if (remindId === '60f6364e09cbf91d41f88859') {
      this.isFFOTask = true;
      this.displayedColumns = ['stt', 'code', 'procedure', applicant, 'appliedDate', 'status', 'statusDutypaidCert', 'action'];
    } else {
      this.isFFOTask = false;
      if (this.configDisplayColumns) {
        this.displayedColumns = this.configColumTable;
      } else {
        this.displayedColumns = ['stt', 'code', 'procedure', applicant, 'appliedDate', 'status', 'action'];
      }
    }
    if(this.onlineReceptionScreen){
      this.displayedColumns = this.displayedColumnsHCM;
    }
    if(this.isAGG){
      this.displayedColumns = ['stt', 'code', 'procedure', 'applicant', 'appliedDate', 'pay', 'status', 'action'];
    }
    this.remindId = remindId;
    this.getShowApplicantOrganizationEnable(remindId);
    //Lấy MenuTaskRemind hồ sơ có hạn yêu cầu bổ sung
    if(this.numberDateAdditionalRequirement && this.numberDateAdditionalRequirement.enable){
      if(remindId == this.numberDateAdditionalRequirement.statusId){
        let obj = this.listMenuRemind.find(o => o.id == remindId);
        if(obj) this.remindId = obj.idRes;
      }
    }
    this.pageIndex = 1;
    this.page = 1;
    this.size = 10;
    let searchString = this.generateSearchString(this.paginationType, 0, this.size, 'id,desc');
    //Lấy MenuTaskRemind hồ sơ có hạn yêu cầu bổ sung
    if(this.numberDateAdditionalRequirement && this.numberDateAdditionalRequirement.enable){
      if(remindId == this.numberDateAdditionalRequirement.statusId){
        this.remindId = this.numberDateAdditionalRequirement.statusId;
        searchString += '&is-date-require=true'
        + '&date-require-status-id=' + this.numberDateAdditionalRequirement.statusId
      }
    }

    this.getListDossier(searchString);
  }

  getDirectlyPaymentOverDueDossier(){
    this.isDirectlyPaymentOverdueRemind = true;
    this.isDirectlyPaymentDone = false;
    this.isDirectlyAdditionalRequirementDossier = false;
    this.isDirectlyisDirectlyAddedDossier = false;
    this.remindId = '';
    this.pageIndex = 1;
    this.page = 1;
    this.size = 10;
    const searchString = this.generateSearchString(this.paginationType, 0, this.size, 'id,desc');
    this.getListDossier(searchString);
  }

  getDirectlyPaymentDone(){
    this.isDirectlyPaymentDone = true;
    this.isDirectlyPaymentOverdueRemind = false;
    this.isDirectlyAdditionalRequirementDossier = false;
    this.isDirectlyisDirectlyAddedDossier = false;
    this.remindId = '';
    this.pageIndex = 1;
    this.page = 1;
    this.size = 10;
    const searchString = this.generateSearchString(this.paginationType, 0, this.size, 'id,desc');
    this.getListDossier(searchString);
  }


  callRemindTask(){
    this.getRemindMenuTask();
    // this.adminLayoutNavComponent.getDossierRemind();
  }

  @ViewChild('applyMethod') applyMethod:ComboboxLazyLoadComponent;
  loadApplyMethod(obj){
    const keyword = obj?.keyword||null;
    const page = obj?.page||0;
    const size = obj?.size||10;
    this.dossierService.getApplyMethods(page,size,keyword).then(rs=>{
      let data = rs.data;
      if(!this.checkShowFilterRegisterApplicationVNeID) {
        let vneidIndex = data.findIndex(item => item.id == 4);
        if (vneidIndex > -1) {
          data.splice(vneidIndex,1);
        }
        
      }
      const hasNext = !rs.last;
      this.applyMethod.update(data,hasNext);
    });
  }

  dossierDetail(dossierId, procedureId, task) {
    const queryParamsObject = {
      procedure: procedureId,
    };

    if (task !== undefined) {
      Object.assign(queryParamsObject, { task: task[task.length - 1].id });
    }

    const isOpenInNewTab = this.deploymentService.getAppDeployment()?.env?.OS_KTM?.openInNewTab === true;
    if (isOpenInNewTab) {
      const url = this.router.serializeUrl(
        this.router.createUrlTree(['dossier/search/' + dossierId], {
          queryParams: queryParamsObject
        })
      );

      window.open(url, '_blank');
      return;
    }
    this.router.navigate(['dossier/search/' + dossierId], {
      queryParams: queryParamsObject
    });
  }

  getListVNPostStatus()
  {
    if (this.env?.vnpost?.listStatus) {
      this.listVnpostStatus = this.env?.vnpost?.listStatus;
    } else {
      this.listVnpostStatus = [
        {
          id: -1,
          name: "Tất cả"
        },
        {
          id: 100,
          name: "Thêm mới đơn hàng thành công"
        },
        {
          id: 101,
          name: "Thêm mới đơn hàng thất bại"
        },
        {
          id: 4,
          name: "Nhận tin"
        },
        {
          id: 8,
          name: "Báo hủy"
        },
        {
          id: 13,
          name: "Đến lấy nhưng chưa có hàng lần 1"
        },
        {
          id: 14,
          name: "Đến lấy nhưng chưa có hàng lần 2"
        },
        {
          id: 15,
          name: "Đến lấy nhưng chưa có hàng lần 3"
        },
        {
          id: 16,
          name: "Đến lấy nhưng chưa có hàng trên 3 lần"
        },
        {
          id: 21,
          name: "Hủy"
        },
        {
          id: 22,
          name: "Đã nhận báo hủy"
        },
        {
          id: 102,
          name: "Nhận hàng thành công"
        }
      ];
    }
  }

  onEnter(type, event) {
    switch (type) {
      case 'sector': {
        clearTimeout(this.timeOutGetListSector);
        this.timeOutGetListSector = setTimeout(async () => {
          this.searchSectorKeyword = event.target.value;
          this.filterSector(this.searchSectorKeyword);
        }, 300);
        break;
      }
      case 'procedure': {
        clearTimeout(this.timeOutGetListProcedure);
        this.timeOutGetListProcedure = setTimeout(async () => {
          this.searchProcedureKeyword = event.target.value;
          this.filterProcedure(this.searchProcedureKeyword);
        }, 300);
        break;
      }
    }
  }

  getNextBatch(type) {
    switch (type) {
      case 'sector': {
        this.getListSector();
        break;
      }
      case 'procedure': {
        this.getListProcedure();
        break;
      }
    }
  }
  getProcedureList(scroll: boolean) {
    if (!scroll) {
      this.procedurePage = 0;
      this.isProcedureListFull = false;
      this.procedureList = [];
    }
    let agencyIdSearch = ''
    const formObj = this.searchForm.getRawValue();
    if ( this.userAgency !== null && this.userAgency !== undefined && !this.isAdmin) {
      if (!!this.userAgency.parent && !!this.userAgency.parent.id) {
        agencyIdSearch = `&agency-id=${this.userAgency.parent.id}`;
      } else if (this.userAgency.id !== this.config.rootAgency.id) {
        agencyIdSearch = `&agency-id=${this.userAgency.id}`;
      }
    }
    agencyIdSearch = this.env?.filterProcedureByAgencyId ? agencyIdSearch : '';
    let searchString = '?page=' + this.procedurePage + '&size=50&status=1&spec=page&status=1&sort=translate.name,asc&represent=true'
      + '&keyword=' + encodeURIComponent(formObj.procedureNameCtrl.trim()) + agencyIdSearch;
      this.procedureService.getListProcedure(searchString).subscribe(data => {
        this.procedureList = [...this.procedureList, ...data.content];
        this.procedureList = Object.values(this.procedureList.reduce((acc, cur) => Object.assign(acc, {[cur.id] : cur}), {}));
        this.procedurePage++;
        this.isProcedureListFull = data.last;
      });
  }
  keyupSearchProcedure() {
    clearTimeout(this.timeOutGetListProcedure);
    this.timeOutGetListProcedure = setTimeout(() => {
      this.getProcedureList(false);
    }, 300);

  }
  clearSearchProcedure() {
      this.searchForm.patchValue({
        procedureNameCtrl: ''
      });
      this.getProcedureList(false);
  }

  async resetSearchForm(type) {
    switch (type) {
      case 'sector': {
        this.searchSectorKeyword = '';
        this.filterSector(this.searchSectorKeyword);
        break;
      }
      case 'procedure': {
        this.searchProcedureKeyword = '';
        this.filterProcedure(this.searchProcedureKeyword);
        break;
      }
    }
  }

  setTotalElements(data, paginationType) {
    if (paginationType === 'page') {
      this.countResult = data.totalElements;
    } else {
      if (data.last) {
        if (!!data.number) {
          this.page = data.number + 1;
          this.countResult = this.size * this.page;
        } else {
          this.countResult = this.size * this.page;
        }
      } else {
        if (this.numberOfElements < this.size) {
          this.countResult = this.size * this.page;
        } else {
          this.countResult = this.countResult + this.ELEMENTDATA.length + 1;
        }
      }
    }
  }

  changePageOrSize(obj) {
    if (tUtils.hasOwnProperty(obj, 'currentPage')) {
      this.page = obj.currentPage;
      this.pageIndex = obj.currentPage;
    }
    if (tUtils.hasOwnProperty(obj, 'itemsPerPage')) {
      this.size = obj.itemsPerPage;
    }
    if (tUtils.hasOwnProperty(obj, 'totalItems')) {
      this.countResult = obj.totalItems;
    } else {
      this.paginate();
    }
  }

  exportPageDossierToExcel() {
    let name = '';
    const newDate = tUtils.newDate();
    if (localStorage.getItem('language') === 'vi') {
      name = 'Danh sach ho so ' + this.datePipe.transform(newDate, 'dd/MM/yyyy');
    }
    else if (localStorage.getItem('language') === 'en') {
      name = 'List dossiers ' + this.datePipe.transform(newDate, 'dd/MM/yyyy');
    }
    return this.dossierService.exportPageDossierToExcel(this.listDisplayingDossier, name, "sheet1");
  }
  downloadAllFile(id) {
    this.dossierService.getAllFileDossier(id).subscribe(data => {
      data.forEach(element => {
        this.procedureService.downloadFile(element.id, id).subscribe(data => {
          const dataType = data.type;
          const binaryData = [];
          binaryData.push(data);
          const downloadLink = document.createElement('a');
          downloadLink.href = window.URL.createObjectURL(new Blob(binaryData, { type: dataType }));
          downloadLink.setAttribute('download', element.id + '_' + element.filename);
          document.body.appendChild(downloadLink);
          downloadLink.click();
        }, err => {
          const msgObj = {
            vi: 'Không tìm thấy file!',
            en: 'File not found!'
          };
          this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
          console.log(err);
        });
      });

    }, err => {
      const msgObj = {
        vi: 'Không tìm thấy văn bản của hồ sơ!',
        en: 'File dossier not found!'
      };
      this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
      console.log(err);
    });
  }

  downloadAllFileDossierSelected(dossier) {
    this.dossierService.getAllFileDossier(dossier.id).subscribe(data => {
      data.forEach(element => {
        this.procedureService.downloadFile(element.id, dossier.id).subscribe(data => {
          const dataType = data.type;
          const binaryData = [];
          binaryData.push(data);
          const downloadLink = document.createElement('a');
          downloadLink.href = window.URL.createObjectURL(new Blob(binaryData, { type: dataType }));
          downloadLink.setAttribute('download', (dossier.code != null ? dossier.code + '_' : '') + element.filename);
          document.body.appendChild(downloadLink);
          downloadLink.click();
        }, err => {
          const msgObj = {
            vi: 'Không tìm thấy văn bản của hồ sơ!',
            en: 'File dossier not found!'
          };
          this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
          console.log(err);
        });
      });

    }, err => {
      const msgObj = {
        vi: 'Không tìm thấy văn bản của hồ sơ!',
        en: 'File dossier not found!'
      };
      this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
      console.log(err);
    });
  }
  async checkDirectlyPaymentNotificationUserAgency() {
    const userAgency = JSON.parse(localStorage.getItem('userAgency'));
    if (this.directlyPayment.allowedAdministrativeAgency.includes(userAgency?.id)) {
      this.isDirectPaymentNotificationAllowed = true;
      return;
    }
    let parentId = userAgency?.parent?.id ? userAgency?.parent?.id : "";
    let parentAgency;
    while (parentId !== "") {
      parentAgency = await this.dossierService.getAgencyFully(parentId);
      if (!!parentAgency) {
        if (this.directlyPayment.allowedAdministrativeAgency.includes(parentAgency.id)) {
          this.isDirectPaymentNotificationAllowed = true;
          break;
        }
        parentId = parentAgency?.parent?.id ? parentAgency?.parent?.id : "";
      }
      else {
        break;
      }
    }
  }

  addAdditionalRemindId() {
    // Add sent directly payment notification remind id
    this.listStatusViewReceptionPage = [this.directlyPayment.sentDirectlyPaymentNotificationTagId, ...this.config.listStatusViewReceptionPage];
    // Add Request to submit paper application remind id
    this.listStatusViewReceptionPage = [this.requestToSubmitPaperApplication.dossierMenuTaskRemind, ...this.listStatusViewReceptionPage];
    // Add paper confirmed remind id
    this.listStatusViewReceptionPage = [this.paymentConfirmed.dossierMenuTaskRemind, ...this.listStatusViewReceptionPage];
  }
  checkProcedureCode(code, eForm){
    if(this.listProcedureCodeShowTooltip != null && this.listProcedureCodeShowTooltip.length > 0 && this.listProcedureCodeShowTooltip.includes(code)){
      let tooltip = 'Tỉnh/Thành phố: ' + eForm?.data?.TinhThanhPho1?.label + '\n' + 'Quận/Huyện: ' + eForm?.data?.QuanHuyen1?.label + '\n' + 'Phường/Xã: ' + eForm?.data?.PhuongXa1?.label + '\n' + 'Số nhà/Đường: ' + eForm?.data?.DiaChi1;
      return tooltip;
    }
  }
  getDirectlyAdditionalRequirementDossier(inputremindId){

    this.remindId = inputremindId;
    this.pageIndex = 1;
    this.page = 1;
    this.size = 10;
    this.isDirectlyPaymentOverdueRemind = false;
    this.isDirectlyPaymentDone = false;
    let searchString = this.generateSearchString(this.paginationType, 0, this.size, 'id,desc');
    if(inputremindId == '60f52e6a09cbf91d41f88836') {
      this.isDirectlyAdditionalRequirementDossier = true;
      this.isDirectlyisDirectlyAddedDossier = false;
      searchString += '&task-status-id=60ebf03109cbf91d41f87f8b';
    } else if(inputremindId == '60f52eaf09cbf91d41f88837') {
      this.isDirectlyisDirectlyAddedDossier = true;
      this.isDirectlyAdditionalRequirementDossier = false;
      searchString += '&task-status-id=60ebf0db09cbf91d41f87f8c';
    } else {
      return
    }
    this.getListDossier(searchString);
  }

  getNumberAdditionalRequirementDossierMenuTaskRemind(searchString) {
    searchString += '&remind-id=60f52e6a09cbf91d41f88836&task-status-id=60ebf03109cbf91d41f87f8b';
    if(!this.deploymentService.getAppDeployment()?.disableMenuRemind)
    {
    this.dossierService.getDossierMenuTaskRemindAllAgency(searchString).subscribe(data => {
      if (data.length > 0) {
        this.listMenuRemindAdditionalRequirementDossier.push(data[0]);
      }
    });
    }
  }

  getNumberAddedDossierMenuTaskRemind(searchString) {
    searchString += '&remind-id=60f52eaf09cbf91d41f88837&task-status-id=60ebf0db09cbf91d41f87f8c';
    if(!this.deploymentService.getAppDeployment()?.disableMenuRemind)
    {
    this.dossierService.getDossierMenuTaskRemindAllAgency(searchString).subscribe(data => {
      if (data.length > 0) {
        this.listMenuRemindAddedDossier.push(data[0]);
      }
    });
    }
  }
  public onSortChange() {
    if(sessionStorage.getItem('dossierArrSortType')){
      sessionStorage.setItem('dossierArrSortType', this.sortId);
    }

  }
  checkAgencySortType(){
    let array: string[] = this.deploymentService.env.OS_HCM.listAgencyUseSortType;
    var parentAgencyId = (this.userAgency.parent != null && this.userAgency.parent != undefined) ? this.userAgency.parent.id : null;
    if(array.filter((value) => (value.includes(this.userAgency.id) || (parentAgencyId != null &&  value.includes(parentAgencyId)))).length > 0){
      this.showSortTypeHCM = true;
    }
    if(this.showSortTypeHCM){
      this.arrSortType = [
        {id : 1, value : "Hồ sơ trễ hạn trước"},
        {id : 2, value : "Hồ sơ còn hạn trước"},
        {id : 3, value : "Ngày nộp tăng dần"},
        {id : 4, value : "Ngày nộp giảm dần"}
      ];
    }
    // else{
    //   sessionStorage.removeItem('dossierArrSortType');
    // }
  }

  checkAllItem(event) {
    if (event.checked) {
      this.checkAll = true;
      for (let i = 0; i < this.numberOfElements; i++) {
        if (this.selectedDossiers.indexOf(this.dataSource.data[i].id) === -1 && this.statusListReceiveMultiple.includes(this.dataSource.data[i].dossierTaskStatus.id)) {
          this.selectedDossiers.push(this.dataSource.data[i].id);
          this.dataSource.data[i].checked = true;
        }
      }
    } else {
      this.checkAll = false;
      for (let i = 0; i < this.numberOfElements; i++) {
        const index = this.selectedDossiers.indexOf(this.dataSource.data[i].id);
        if (index >= 0) {
          this.selectedDossiers.splice(index, 1);
          this.dataSource.data[i].checked = false;
        }
      }
    }
    // this.checkWorkPermitProcedureTypesSelected();
  }

  checkItem(event, id) {
    if (event.checked) {
      this.selectedDossiers.push(id);
      let countCheck = true;
      for (let i = 0; i < this.numberOfElements; i++) {
        if (this.dataSource.data[i].checked != true) {
          countCheck = false;
          break;
        }
      }
      this.checkAll = countCheck;
    }
    else {
      this.checkAll = false;
      const i = this.selectedDossiers.indexOf(id);
      this.selectedDossiers.splice(i, 1);
    }
    // this.checkWorkPermitProcedureTypesSelected();
  }

  checkAllItemF(event) {
    if (event.checked) {
      this.checkAllF = true;
      for (let i = 0; i < this.numberOfElements; i++) {
        if (this.selectedDossiersF.indexOf(this.dataSource.data[i]) == -1) {
          this.selectedDossiersF.push(this.dataSource.data[i]);
          this.checkedDownloadFile[i] = true;
        }
      }
    } else {
      this.checkAllF = false;
      for (let i = 0; i < this.numberOfElements; i++) {
        const index = this.selectedDossiersF.indexOf(this.dataSource.data[i]);
        if (index >= 0) {
          this.selectedDossiersF.splice(index, 1);
          this.checkedDownloadFile[i] = false;
        }
      }
    }
    // this.checkWorkPermitProcedureTypesSelected();
  }

  checkItemF(event, row) {
    if (event.checked) {
      this.selectedDossiersF.push(row);
      let countCheck = true;
      for (let i = 0; i < this.numberOfElements; i++) {
        if (this.checkedDownloadFile[i] != true) {
          countCheck = false;
          break;
        }
      }
      this.checkAll = countCheck;
    }
    else {
      this.checkAll = false;
      const i = this.selectedDossiersF.indexOf(row);
      this.selectedDossiersF.splice(i, 1);
    }
    // this.checkWorkPermitProcedureTypesSelected();
  }

  async transferMultiDossier() {
    // const selectedDossiersTransfer: any = [];
    // const listDossierFailStatus = [];
    // const listDossierFailAllowClaim = [];
    // const listDossierCanHandler = [];
    // const listDossierNeedApproval = [];

    if (this.selectedDossiers.length <= 0) {
      this.snackbarService.openSnackBar(0, 'Phải chọn hồ sơ trước khi chuyển!', '', 'error_notification', this.config.expiredTime);
      return;
    }

    if (this.selectedDossiers.length > 50) {
      this.snackbarService.openSnackBar(0, 'Không được chọn quá 50 hồ sơ!', '', 'error_notification', this.config.expiredTime);
      return;
    }
    let listSuccessful = ""
    let listError = ""
    let countError  = 0;
    let countSuccessful = 0
    for await (const id of this.selectedDossiers) {
      const dossier: any = this.dataSource.data.find(item => item.id === id);
      if (!!dossier) {
        this.dossierService.getDossierDetail(dossier.id).subscribe(async (dossierDetail) => {
          console.log("aaaaaaaaaaaaaaaaaaaa", dossierDetail)
          this.procedureService.getProcedureProcessDetail(dossierDetail.procedureProcessDefinition.id).subscribe((data) => {

            ////
            if (dossierDetail.applicant.eformId) {
              this.applicantEForm.id = dossierDetail.applicant.eformId;
            } else {
              this.applicantEForm.id = this.config.defaultFormIO.applicantEForm;
            }

            if (!!dossierDetail.eForm) {
              this.eForm.id = dossierDetail.eForm.id;
              this.eForm.data = {
                data: dossierDetail.eForm.data,
              };
            } else {
              this.eForm.id = this.config.defaultFormIO.eForm;
            }

            const newDate = tUtils.newDate();
            const contentObj: any = {
              id: dossierDetail.id,
              code: dossierDetail.code,
              nationCode: dossierDetail.nationCode,
              codePattern: {},
              procedure: dossierDetail.procedure,
              applyMethod: dossierDetail.applyMethod,
              dossierReceivingKind: {},
              receivingPlace: {},
              applicant: {},
              agency: {},
              accepter: {
                id: this.accepterInfo.id,
                fullname: this.accepterInfo.fullname
              },
              assignee: {
                id: this.accepterInfo.id,
                fullname: this.accepterInfo.fullname,
                account: {
                  id: this.accepterInfo.accountId,
                  username: [
                    {
                      value: this.accepterInfo.username,
                    },
                  ],
                },
              },
              procedureProcessDefinition: {
                id: '',
                processDefinition: {
                  id: '',
                  processingTime: '',
                  processingTimeUnit: '',
                  eForm: {},
                  applicantEForm: {},
                  timesheet: {},
                },
              },
              eForm: {},
              deploymentId: this.config.deploymentId,
              dossierStatus: {
                id: 2,
                name: [
                  {
                    languageId: 228,
                    name: 'Đang xử lý',
                  },
                  {
                    languageId: 46,
                    name: 'Inprogress',
                  },
                ],
                comment: '',
              },
              createdDate: dossierDetail.createdDate,
              updatedDate: newDate,
              acceptedDate: dossierDetail?.acceptedDate,
              dossierFormFile: [],
              extendHCM: dossierDetail.extendHCM
            };

            console.log('contentObj.receivingPlace');
            console.log(contentObj.receivingPlace);


            // Agency
            if (this.agencyInfo.length !== 0) {
              contentObj.agency = this.agencyInfo[0];
            } else {
              delete contentObj.agency;
            }

            // Applicant Info
            const applicantEFormData = dossierDetail.applicant.data;

            // tslint:disable-next-line: max-line-length
            if (
              applicantEFormData.identityNumber !== undefined &&
              applicantEFormData.identityNumber !== null &&
              applicantEFormData.identityNumber !== ''
            ) {
              applicantEFormData.identityNumber = String(
                applicantEFormData.identityNumber
              );
            }

            if (
              dossierDetail?.applicant?.userId !== undefined &&
              dossierDetail.applicant.userId !== null
            ) {
              Object.assign(contentObj.applicant, {
                userId: dossierDetail.applicant.userId,
              });
            }

            if (
              this.applicantEForm.id !== undefined &&
              this.applicantEForm.id !== null &&
              this.applicantEForm.id !== ''
            ) {
              Object.assign(contentObj.applicant, {
                eformId: this.applicantEForm.id,
              });
              Object.assign(contentObj.applicant, { data: applicantEFormData });
            } else {
              delete contentObj.applicant;
            }

            if (dossierDetail?.dossierReceivingKind !== undefined) {
              contentObj.dossierReceivingKind = {
                id: dossierDetail.dossierReceivingKind.id,
                name: [
                  {
                    languageId: this.selectedLangId,
                    name: dossierDetail.dossierReceivingKind.name[0].name,
                  },
                ],
              };


              // if (this.enableSaveFullAddress == 1) {

              //   contentObj.receivingPlace = {
              //     id: rcObj.rcVillage,
              //     fullAddress: rcObj.customAddress,
              //     nationName: this.listNation.filter(
              //       (nation) => nation.id === this.defaultNation
              //     )[0].name,
              //     rcSend: rcObj.rcSend,
              //     rcReceive: rcObj.rcReceive,
              //     name: rcObj.rcName,
              //     phoneNumber: rcObj.rcPhoneNumber,
              //     email: rcObj.rcEmail,
              //     fullAddressR: rcObj.customAddressR,
              //     resultReceivingPlaceId: ((rcObj.rcVillageR != '' && rcObj.rcVillageR != null) ? rcObj.rcVillageR : null),
              //     rcDistrict: rcObj.rcDistrict,
              //     rcDistrictR: rcObj.rcDistrictR,
              //     rcProvince: rcObj.rcProvince,
              //     rcProvinceR: rcObj.rcProvinceR,
              //     rcVillage: rcObj.rcVillage,
              //     rcVillageR: rcObj.rcVillageR,
              //   };

              // } else {
              //   if (dossierDetail.dossierReceivingKind.id === this.env?.vnpost?.receiveResultsByAddress) {
              //     contentObj.receivingPlace = {
              //       id: rcObj.rcVillage,
              //       fullAddress: rcObj.customAddress,
              //       nationName: this.listNation.filter(
              //         (nation) => nation.id === this.defaultNation
              //       )[0].name,
              //       rcSend: rcObj.rcSend,
              //       rcReceive: rcObj.rcReceive,
              //       name: rcObj.rcName,
              //       phoneNumber: rcObj.rcPhoneNumber,
              //       email: rcObj.rcEmail,
              //       fullAddressR: rcObj.customAddressR,
              //       resultReceivingPlaceId: ((rcObj.rcVillageR != '' && rcObj.rcVillageR != null) ? rcObj.rcVillageR : null)
              //     };
              //   }
              // }

            } else {
              delete contentObj.dossierReceivingKind;
              delete contentObj.receivingPlace;
            }


            console.log('contentObj');
            console.log(contentObj);

            // Applicant Info
            const eFormData = this.eForm.data.data;
            if (
              this.eForm.id !== undefined &&
              this.eForm.id !== null &&
              this.eForm.id !== ''
            ) {
              Object.assign(contentObj.eForm, { id: this.eForm.id });
              Object.assign(contentObj.eForm, { data: eFormData });
            }

            if (
              dossierDetail.procedureProcessDefinition !== undefined &&
              dossierDetail.procedureProcessDefinition !== null
            ) {
              contentObj.procedureProcessDefinition =
                dossierDetail.procedureProcessDefinition;
              Object.assign(
                contentObj.procedureProcessDefinition.processDefinition,
                {
                  eForm: {
                    id: this.eForm.id
                    // design: this.eForm.component,
                  },
                }
              );
              Object.assign(
                contentObj.procedureProcessDefinition.processDefinition,
                {
                  applicantEForm: {
                    id: this.applicantEForm.id
                    // design: this.applicantEForm.component,
                  },
                }
              );
            } else {
              delete contentObj.procedureProcessDefinition;
            }

            if (
              dossierDetail.appliedDate !== undefined &&
              dossierDetail.appliedDate !== null
            ) {
              Object.assign(contentObj, {
                appliedDate: dossierDetail.appliedDate,
              });
            }
            Object.assign(contentObj, { acceptedDate: dossierDetail?.acceptedDate });

            // let appointmentDate = await this.postTimesheet(1, this.dossierDetail?.appointmentDate);
            // if (!!appointmentDate) {
            //   appointmentDate = new Date(appointmentDate.toString());
            // } else {
            //   appointmentDate = this.dossierDetail?.appointmentDate;
            // }
            let appointmentDate = dossierDetail?.appointmentDate;
            if (appointmentDate !== undefined && appointmentDate !== null) {
              Object.assign(contentObj, { appointmentDate });
            }
            // if(this.originalAppointmentDate.getTime() != appointmentDate.getTime()){
            //   Object.assign(contentObj, { useEditedAppointmentDate: true });
            // }

            // // dossierFormFile
            // contentObj.dossierFormFile = this.dossierFormFileData;

            const taskVariable = {
              assignee: {
                id: this.accepterInfo.id,
                fullname: this.accepterInfo.fullname,
                account: {
                  id: this.accepterInfo.accountId,
                  username: [
                    {
                      value: this.accepterInfo.username,
                    },
                  ],
                },
              },
              candidateGroup: [this.agencyInfo[0]],
              agency: this.agencyInfo[0],
            };
            Object.assign(contentObj, { taskVariable });

            // // Thông tin người dân đã được xác thực từ CSDLQG Dân cư
            // if (tUtils.nonNull(this.validateResidentialInfo, 'fullname') && tUtils.nonNull(this.applicantEForm.data.data, 'fullname')) {
            //   if (!(this.validateResidentialInfo?.fullname == this.applicantEForm.data?.data?.fullname
            //     && this.validateResidentialInfo?.identityNumber == this.applicantEForm.data?.data?.identityNumber
            //     && this.validateResidentialInfo?.birthday == this.applicantEForm.data?.data?.birthday)) {
            //     this.isValidResidentialInfo = false;
            //     if (tUtils.nonNull(this.dossier, 'validateResidentialInfo')) {
            //       this.validateResidentialInfo = this.dossier.validateResidentialInfo;
            //       this.validateResidentialInfo.confirm = false;
            //       Object.assign(contentObj, { validateResidentialInfo: this.validateResidentialInfo });
            //     }
            //   }
            // }

            // if (tUtils.nonNull(this.validateResidentialInfo, 'fullname') && this.isValidResidentialInfo) {
            //   Object.assign(contentObj, { validateResidentialInfo: this.validateResidentialInfo });
            // }

            let mergedDossier = JSON.parse(JSON.stringify(contentObj));

            if (this.enableMergeDeepDossier) {
              mergedDossier = tUtils.mergeDeep(dossierDetail, JSON.parse(JSON.stringify(contentObj)));
              delete mergedDossier.dossierTaskStatus;
              delete mergedDossier.dossierMenuTaskRemind;
            }

            ///
            const resultObj = {
              processDefinitionId:
                data.processDefinition.activiti.id,
              payloadType: 'StartProcessPayload',
              variables: {
                dossier: dossierDetail,
              },
              commandType: 'StartProcessInstanceCmd',
            };
            const resultJson = JSON.stringify(resultObj, null, 2);
            this.dossierService.startReceivingDossier(resultJson).subscribe(async (dataStartReceiving) => {
              const createProcessBody = {
                processInstanceId: dataStartReceiving.entry.id
              };
              this.dossierService.postDossierCreateProcess(createProcessBody).subscribe((data) => {
                if(data?.affectedRows === 1){
                  listSuccessful += dossierDetail.code + "; "
                  countSuccessful++;
                }
                else {
                  listError += dossierDetail.code + "; "
                  countError++;
                }
              });
            });
          });
        });
      }
    }
    setTimeout(() => {
      console.log("listSuccessful ", listSuccessful, countSuccessful);
      console.log("ListError ", listError, countError);
      const msgObj = {
        vi: 'Tiếp nhận thành công',
        en: 'Reception successful'
      };
      this.snackbarService.openSnackBar(1, msgObj[this.selectedLang], '', 'success_notification', this.config.expiredTime);
      this.selectedDossiers = [];
      this.checkAll = false;
      switch (this.deploymentService.env.OS_HCM.enableReloadDossier) {
        case 1:
          setTimeout(() => {
            console.log("reload page 1");
            this.reloadPage();
            this.loadReminderMenu();
            this.onConfirmSearch();
          }, 1000);
          break;
        case 2:
          console.log("reload page 2");
          setTimeout(() => {
            window.location.reload();
          }, 1000);
          break;
        default:
          console.log("reload page 0");
          setTimeout(() => {
            tUtils.reload(this.router);
          }, 1000);
          break;
      }
    });
  }

  reloadPage() {
    this.router.navigateByUrl('/dossier/online-reception', { skipLocationChange: true }).then(() => {
      this.router.navigate(['/dossier/online-reception']);
    });
  }

  async loadReminderMenu(){
    for (let i=0; i < this.listStatusViewReceptionPage.length; i++) {
      let object = await this.mainService.getReminderMenu(this.listStatusViewReceptionPage[i]);
      object.searchType = i+1;
      this.listMenuRemind.push(object);
    }
  }

  async checkReceiveMultiple(){
    const userAgency = JSON.parse(localStorage.getItem('userAgency'));
    var parentAgencyId = (userAgency.parent != null && userAgency.parent != undefined) ? userAgency.parent.id : null;
    if(this.agencyListReceiveMultiple.filter((value) => (value.includes(userAgency.id) || (parentAgencyId != null &&  value.includes(parentAgencyId)))).length > 0 && this.enableReceiveMultiple){
      this.checkEnableReceiveMultiple = true;
      //tlqkhanh.hcm-IGATESUPP-68633
      this.displayedColumnsHCM = this.isShowOrganizationName ? ['select', 'stt', 'code', 'procedure', 'applicant', 'organizationName', 'appliedDate', 'payStatus', 'status', 'action'] : ['select', 'stt', 'code', 'procedure', 'applicant', 'appliedDate', 'payStatus', 'status', 'action'];
      //endof tlqkhanh.hcm-IGATESUPP-68633
      this.keycloakService.loadUserProfile().then(async (user) => {
        this.accepterInfo.username = user.username;
        // tslint:disable-next-line: no-string-literal
        this.accepterInfo.id = user['attributes'].user_id[0];
        // tslint:disable-next-line: no-string-literal
        this.accepterInfo.accountId = user['attributes'].account_id[0];
        // tslint:disable-next-line: no-string-literal
        this.userService
          .getUserInfo(user['attributes'].user_id)
          .subscribe((data) => {
            this.accepterInfo.fullname = data.fullname;
          });
        });
        if (!!userAgency) {
          this.userAgency = userAgency;
          await this.getAgencyInfo(userAgency.id);
        }
    }
  }

  async getAgencyInfo(agencyId) {
    return new Promise((resolve) => {
      this.dossierService.getAgencyInfo(agencyId).subscribe(async data => {
          const agencyTag = [];
          data.tag.forEach((tagId) => {
            this.dossierService.getAgencyTag(tagId).subscribe((rs) => {
              agencyTag.push({
                id: rs.id,
                name: rs.trans,
              });
            });
          });
          const agencyInfoData = {
            id: agencyId,
            code: data.code,
            name: data.name,
            tag: agencyTag,
            parent: null,
            ancestors: null,
          };

          // Parent
          if (data.parent !== null || data.parent !== '') {
            const parentTag = [];
            data.tag.forEach((tagId) => {
              this.dossierService.getAgencyTag(tagId).subscribe((tg) => {
                parentTag.push({
                  id: tg.id,
                  name: tg.trans,
                });
              });
            });

            this.dossierService.getAgencyInfo(data.parent).subscribe((res) => {
              agencyInfoData.parent = {
                id: data.parent,
                name: res.name,
                tag: parentTag,
              };
            });
          }
          // Ancestors
          if (!!data.ancestors && data.ancestors.length !== 0) {
            agencyInfoData.ancestors = [];
            data.ancestors.forEach(anc => {
              agencyInfoData.ancestors.push({
                id: anc.id,
                name: [{
                  languageId: this.selectedLangId,
                  name: anc.name,
                }]
              });
            });
          }

          if (this.agencyInfo.length !== 0) {
            this.agencyInfo[0] = agencyInfoData;
          } else {
            this.agencyInfo.push(agencyInfoData);
          }

          delete agencyInfoData.code;   // xóa agency.code để lấy code từ parent agency

          if (!agencyInfoData.code) {
            this.agencyInfo[0].code = await this.getAgencyCodeRecursive(agencyInfoData, data.parent);
          }
          resolve(agencyInfoData);
        },
        (err) => {
          resolve(null);
        }
      );
    });
  }

  getAgencyCodeRecursive(agencyInfo, parentId) {
    let info = agencyInfo;
    let id = parentId;
    let check = true;

    return new Promise<any>(async resolve => {
      while (check) {
        if (!!info.code) {
          resolve(info.code);
          check = false;
        } else {
          await this.dossierService.getAgencyInfo(id).toPromise().then(data => {
            info = data;
            id = data.parent;
            check = true;
          }).catch(err => {
            resolve(info.code);
            check = false;
          });
        }
      }
    });
  }

  confirmPayment(dossier:any){
    console.log(dossier);

    const dossierStatus ={
      id:'15',
      name:[
        {
          languageId:228,
          name:'Đã thanh toán'
        },
        {
          languageId:46,
          name:'Paid'
        }
      ]
    }
    
    const dossierTaskStatus ={
      id:"62e35811a106a86e839f765f",
      name:[
        {
          languageId:228,
          name:'Đã thanh toán'
        },
        {
          languageId:46,
          name:'Paid'
        }
      ]
    }

    const dossierMenuTaskRemind ={
      id:"62e35a51a106a86e839f7668",
      name:[
        {
          languageId:228,
          name:'Đã thanh toán hồ sơ'
        },
        {
          languageId:46,
          name:'Paid dossier'
        }
      ]
    }



    const requestBody = {
      id:dossier.id,
      code:dossier.code,
      dossierStatus,
      dossierTaskStatus,
      dossierMenuTaskRemind
    }
    console.log(requestBody)
    this.dossierService.putDossierPaymentOnline(requestBody).subscribe(data=>{
      this.router.navigate(['dossier/online-reception/receiving/' + dossier.id], {
        queryParams: {
          procedure:dossier.procedure.id,
          procedureProcessDef:dossier.procedureProcessDefinition.id
        }
      });
    })

  }

  downloadMultiDossier() {
    if (this.selectedDossiersF.length <= 0) {
      this.snackbarService.openSnackBar(0, 'Phải chọn hồ sơ trước khi tải xuống !', '', 'error_notification', this.config.expiredTime);
      return;
    }

    if (this.selectedDossiersF.length > 50) {
      this.snackbarService.openSnackBar(0, 'Không được chọn quá 50 hồ sơ!', '', 'error_notification', this.config.expiredTime);
      return;
    }
    for (let dossier of this.selectedDossiersF) {
      this.downloadAllFileDossierSelected(dossier);
    }
  }
  countDossier = ''
  getTitleHcm(){
    let listEl = document.querySelectorAll(".active");
    if(listEl?.length < 1){
      return false;
    }
    for(var index = 0; index < listEl.length; index++){
      if(listEl[index].className.includes('itemCv')){
        try {
          this.countDossier = listEl[index].children[1].innerHTML
          return "Hồ sơ " + listEl[index].children[0].innerHTML.toLowerCase() + " (" + this.countDossier + ")";
        } catch (error) {
          this.countDossier = '';
          return "Hồ sơ " + listEl[index].children[0].innerHTML.toLowerCase();
        }       
      }
    }
    return false;
  }

  checkShowButtonCancelDossier(row){
    if(this.allowShowCancelDossierReception && this.allowShowCancelDossierReception.enable){
      if(this.allowShowCancelDossierReception?.statusId?.find(o => o === row?.dossierStatus?.id))
        return true;
    }
    return false;
  }
  changeCancelDossier(row){
    const dialogData = new ConfirmCancelSDialogModel(row.id, row.code);
    const dialogRef = this.dialog.open(CancelSDossierComponent, {
      width: '500px',
      data: dialogData,
      disableClose: true,
      autoFocus: false
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
      if (dialogResult) {
        if (dialogResult === true) {
          const msgObj = {
            vi: 'Đã chuyển vào hồ sơ không cần xử lý thành công!',
            en: 'Dossier to cancle!'
          };
          this.snackbarService.openSnackBar(1, msgObj[localStorage.getItem('language')], '', 'success_notification', this.config.expiredTime);
          const searchString = this.generateSearchString(this.paginationType, (this.pageIndex - 1), this.size, 'id,desc');
          this.getListDossier(searchString);
          this.callRemindTask();
        }
        else {
          const msgObj = {
            vi: 'Chuyển vào hồ sơ không cần xử lý thất bại!',
            en: 'Cancel failed!'
          };
          this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
        }
      }
    });
  }
  listAllIdchild = [];
  showListCV = true;
  showListSearch = true;
  async getChildGroupCategory(){
    let groupTask = [];
    for (let index = 0; index < this.groupTaskID.length; index++) {
      let objectGroup = {
        id: this.groupTaskID[index].id,
        name: this.groupTaskID[index].name,
        listIdChild: [],
        child: [],
        count: 0
      };
      let group = await this.getCategory(this.groupTaskID[index].id, objectGroup);
      groupTask.push(group);
    }
    this.groupTaskID = groupTask;
  }
  async getCategory(id, group){
    const groupCat = await this.basecatService.getListGuideType(id, 0).toPromise();
    if (groupCat.content.length != 0) {
      let list = groupCat.content?.map(el => el.id);
      group.listIdChild.push(...list);
      this.listAllIdchild.push(...list);
      group.child = groupCat.content;
    }
    return group;
  }
  getBackground(id, process){
    let category = process.child.find(el => el.id == id);
    return category.primaryColor == null ? null : '#' + category.primaryColor;
  }
  isChildGroup(id, listChild, index){
    if(listChild){
      let check = listChild.includes(id);
      if(check == true){
        this.groupTaskID[index].count++;
      }
      return check;
    }
    return false;
  }
  
  
  checkPermissionNotify() {
    return this.userService.checkPermissionExists('oneGateNotify');
  }
  openNotify(dossierId, dossierCode) {
    if (dossierCode === undefined || dossierCode === null) {
      dossierCode = '';
    }
    const dialogRef = this.dialog.open(NoticeDossierComponent, {
      minWidth: '550px',
      maxHeight: '90vh',
      data: {
        dossierId: dossierId,
        dossierCode: dossierCode,
      },
      disableClose: true,
      autoFocus: false,
      panelClass: 'cancel-dossier-dialog-container'
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
      if (dialogResult ===  true){
      }
    });
  }
  withdrawQBHDialogs(dossierId, dossierCode){
    if (dossierCode === undefined || dossierCode === null) {
      dossierCode = '';
    }
    const dialogData = new WithdrawQBHModel(dossierId, dossierCode);
    const dialogRef = this.dialog.open(WithdrawQBHComponent, {
      minWidth: '45vw',
      data: dialogData,
      disableClose: true,
      autoFocus: false
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
      if (dialogResult === true) {
        const msgObj = {
          vi: 'Đã rút hồ sơ!',
          en: 'Dossier has been withdraw!'
        };
        this.snackbarService.openSnackBar(1, msgObj[localStorage.getItem('language')], '', 'success_notification', this.config.expiredTime);
        const searchString = this.generateSearchString(this.paginationType, (this.pageIndex - 1), this.size, 'id,desc');
        this.getListDossier(searchString);
        this.callRemindTask();
      }
      if (dialogResult === false) {
        const msgObj = {
          vi: 'Rút hồ sơ không thành công!',
          en: 'Withdraw dossier failed!'
        };
        this.snackbarService.openSnackBar(0, msgObj[localStorage.getItem('language')], '', 'error_notification', this.config.expiredTime);
      }
    });
  }
  cloneDossier(dossierId, dossierCode, procedureId, procedureProcessDef) {
    const dialogData = new CloneDossierDialogModel(dossierId, dossierCode, procedureId, procedureProcessDef);
    const dialogRef = this.dialog.open(CloneDossierComponent, {
      minWidth: '40vw',
      maxHeight: "85vh",
      data: dialogData,
      autoFocus: false
    })
    dialogRef.afterClosed().subscribe(r => {
      if(r?.success) {
        this.getRemindMenuTask();
        this.loadReminderMenu();
        this.onConfirmSearch();
      }
    })
  }

  checkShowAbstractConstructionUnit() {
    const userAgency = JSON.parse(localStorage.getItem('userAgency'));
    if (this.showAbstractConstructionUnitEnable == 1 && this.showAbstractConstructionUnitAgencyIds.length > 0) {
      if(this.showAbstractConstructionUnitAgencyIds.filter(item => item == userAgency.id).length > 0){
        this.onOffAbstractConstructionUnit = true;
      } else {
        this.onOffAbstractConstructionUnit = false;
      }
    }
  }
  checkDVCLL(isDVCLT : any = false, dossierInput: any) {
    let showBtn = true;
    if(this.enableDVCLT) {
      if(isDVCLT == true && dossierInput?.nationCode && dossierInput?.dossierStatus?.id == 1) {
        showBtn = false;
      } else {
        showBtn = true;
      }
    }
      return showBtn; 
  }

  checkHttpStatusHPG(dosier : any) {
    let showBtn = true;
    if(this.enableDVCLT) {
      if(dosier?.httpStatusHPG == true && dosier?.dossierStatus?.id == 1 && dosier?.nationCode) { // trạng thái yêu cầu bổ sung và đã gửi hs sang liên thông hộ tịch
        showBtn = false;
      } else {
        showBtn = true;
      }
    }
      return showBtn;
  }
  checkShowFilterRegisterApplicationVNeIDValue(){
    if (this.showFilterRegisterApplicationVNeID == 1) {
        const userAgency = JSON.parse(localStorage.getItem('userAgency'));
        if (this.showFilterRegisterApplicationVNeIDAgencyId.filter(item => item == userAgency.id).length >0) {
          this.checkShowFilterRegisterApplicationVNeID = true;
        } else {
          this.checkShowFilterRegisterApplicationVNeID = false;
        }
    }
  }

  onClickEdit(dossier) {
      const dialogData = new UpdatePaymentMethodDialogModel(dossier.id, this.accepterInfo.id, this.accepterInfo.fullname, dossier?.paymentMethod?.name, dossier?.paymentMethod?.id);
      const dialogRef = this.dialog.open(UpdatePaymentMethodComponent, {
        minWidth: '25vw',
        maxHeight: "60vh",
        data: dialogData,
        disableClose: true,
        autoFocus: false
      });
      dialogRef.afterClosed().subscribe(dialogResult => {
        if(dialogResult.status == true){
          // this.disableRequestPayment = true;
        }
      });
  }
  copyIdToClipboard(data) {
    this.clipboard.copy(data.procedure.id);
    const msgObj = {
      vi: 'Sao chép ID thành công!',
      en: 'Copy ID successfully!'
    };

    this.snackbarService.openSnackBar(1,
      '',
      msgObj[this.selectedLang],
      'success_notification',
      this.config.expiredTime
    );
  }

  viewEformProperties(data) {
    if (this.config.formioDomain) {
      window.open(this.config.formioDomain + '/' + this.selectedLang + '/manager/' + data.applicant.eformId + '/' + this.openEformAction, '_blank');
    } else {
      this.clipboard.copy(data.applicant.eformId);
      const msgObj = {
        vi: 'Sao chép ID thành công!',
        en: 'Copy ID successfully!'
      };
      this.snackbarService.openSnackBar(1,
        '',
        msgObj[this.selectedLang],
        'success_notification',
        this.config.expiredTime
      );
    }
  }

}
function compare(a: number | string, b: number | string, isAsc: boolean) {
  return (a < b ? -1 : 1) * (isAsc ? 1 : -1);
}



