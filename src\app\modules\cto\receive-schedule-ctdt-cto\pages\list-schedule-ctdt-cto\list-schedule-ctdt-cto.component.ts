import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { FormGroup, FormControl } from '@angular/forms';
import { DossierSearchElement } from 'src/app/data/schema/dossier-search-element';
import { MatTableDataSource } from '@angular/material/table';
import { ActivatedRoute, Router } from '@angular/router';
import { EnvService } from 'src/app/core/service/env.service';
import { MatAccordion } from '@angular/material/expansion';
import { DatePipe } from '@angular/common';
import { MainService } from 'src/app/data/service/main/main.service';
import { ScheduleCtdtService } from 'src/app/data/service/cto-statistics/schedule-ctdt-cto.service';
import { DeploymentService } from 'src/app/data/service/deployment.service';
import * as tUtils from 'src/app/data/service/thoai.service';
import { SnackbarService } from 'src/app/data/service/snackbar/snackbar.service';
import { MatDialog } from '@angular/material/dialog';
import { ConfirmCancelDialogModel, ConfirmCancelScheduleComponent } from '../confirm-cancel-schedule-cto/confirm-cancel-schedule-cto.component';
export type PageOrientation = 'portrait' | 'landscape';
@Component({
  selector: 'app-list-schedule-ctdt-cto',
  templateUrl: './list-schedule-ctdt-cto.component.html',
  styleUrls: ['./list-schedule-ctdt-cto.component.scss', '/src/app/app.component.scss']
})
export class ListScheduleCtdtComponent implements OnInit {
  @ViewChild('pdfContent') pdfContent: ElementRef;
  depConfig = this.deploymentService.getAppDeployment();
  selectedLangId = Number(localStorage.getItem('languageId')) || 228;
  userAgency = JSON.parse(localStorage.getItem('userAgency'));
  @ViewChild(MatAccordion) accordion: MatAccordion;
  searchForm = new FormGroup({
    code: new FormControl(''),
    identityNumber: new FormControl(''),
    applicantName: new FormControl(''),
    appointmentFromDate: new FormControl(''),
    appointmentToDate: new FormControl(''),
  });
  pageTitle = {
    vi: `Tiếp nhận lịch hẹn Chứng thực điện tử`,
    en: `Tiếp nhận lịch hẹn Chứng thực điện tử`
  };
  config = this.envService.getConfig();
  env = this.deploymentService.getAppDeployment()?.env;
  userId = localStorage.getItem('tempUID');
  expandReminderMenu = true;
  selectedLang = localStorage.getItem('language') || 'vi';
  pgSizeOptions = this.config.pageSizeOptions;
  isFullListProcedure = false;
  isOwnerFullname = this.env?.isOwnerFullname === 1;
  exportExcelQNM = this.deploymentService.env.OS_QNM.exportExcelQNM;
  displayedColumns: string[] = ['stt', 'applicantName', 'applicantPhone', 'procedureName', 'applicantDate', 'status', 'action'];
  ELEMENTDATA: DossierSearchElement[] = [];
  dataSource: MatTableDataSource<DossierSearchElement>;
  countResult = 0;
  size =  10;
  page = 1;
  pageIndex = 1;
  waitingDownloadExcel = false;
  excelData = [];
  remindId = '';
  agencyId: string;
  checkProvineAdmin?  = JSON.parse(localStorage.getItem('superAdmin'));
  paginationType = this.deploymentService.env.paginationType;
  identityNumber:"";
  fullName:"";
  dateSchedule:"";
  appointmentFromDate:"";
  appointmentToDate:"";
  isShowInternalForm = false;
  constructor(
    private router: Router,
    private activeRoute: ActivatedRoute,
    private envService: EnvService,
    private deploymentService: DeploymentService,
    private datePipe: DatePipe,
    private mainService: MainService,
    private scheduleCtdtService: ScheduleCtdtService,
    private dialog: MatDialog,
    private snackbarService: SnackbarService,
  ) {

    this.dataSource = new MatTableDataSource(this.ELEMENTDATA);
    this.env = this.deploymentService.getAppDeployment()?.env;
    this.getConfig();
  }
//đã code ở đây IGATESUPP-115852
  async ngOnInit(): Promise<void> {
    const userAgency = JSON.parse(localStorage.getItem('userAgency'));
    let searchStr = "?agency-id="+this.userAgency.id+"&status=0&spec=page&page=0&size="+ this.size ;
    // let searchStr = "?status=0&spec=page&page=0&size="+ this.size ;
    this.getListScheduleCtdt(searchStr);
  }

  getConfig() {
    const config = this.deploymentService.getAppDeployment();
    if (!!config) {
      if (config.domain && config.domain.length > 0) {
        // tslint:disable-next-line:max-line-length
        const domain = config.domain.filter(listdomain => ((listdomain.domain.toLowerCase().indexOf(window.location.host) > -1) || (window.location.host.toLowerCase().indexOf(listdomain.domain) > -1)));
        if (domain && domain.length > 0 && domain[0].rootAgency) {
          //  this.searchDomain = '&domain-agency-id=' + domain[0].rootAgency.id;
        }
      }
    }
  }
  getListScheduleCtdt(searchString) {
    this.scheduleCtdtService.getListScheduleCtdt(searchString).subscribe(data=>{

      this.ELEMENTDATA = [];
      this.countResult = data.totalElements;
      for (let i = 0; i < data.numberOfElements; i++) {
        data.content[i].stt = this.size * (this.pageIndex - 1) + (i + 1);
        this.ELEMENTDATA.push(data.content[i]);
      }
      this.dataSource.data = this.ELEMENTDATA;
      console.log('getListScheduleCtdt',this.dataSource.data)
    });
  }
  countScheduleCtdt() {
    let searchStr = "";
    this.scheduleCtdtService.countScheduleCtdt(searchStr).subscribe(data=>{
      console.log('countScheduleCtdt',data)
    });
  }
  getDate(dateTime) {
    return dateTime ? this.datePipe.transform(dateTime, 'dd/MM/yyyy') : '';
  }
// coi code ở đây IGATESUPP-115852
  onConfirmSearch() {
    this.ELEMENTDATA = [];
    const formObj = this.searchForm.getRawValue();
    console.log('formObj', formObj);

    this.identityNumber = formObj.identityNumber;
    this.appointmentFromDate = formObj.appointmentFromDate;
    this.appointmentToDate = formObj.appointmentToDate;

    let searchString = `?agency-id=${this.userAgency.id}&status=0&page=${this.pageIndex - 1}&size=${this.size}&spec=page`;

    if (this.identityNumber)
      searchString += '&identity=' +  this.identityNumber;

    if (this.appointmentFromDate)
      searchString += `&appointmentFromDate=${this.datePipe.transform(this.appointmentFromDate, 'yyyy-MM-dd')}`;

    if (this.appointmentToDate)
      searchString += `&appointmentToDate=${this.datePipe.transform(this.appointmentToDate, 'yyyy-MM-dd')}`;

    this.getListScheduleCtdt(searchString);
  }

  exportExcelDossier(){
    let name = '';
    const newDate = tUtils.newDate();
    name = 'Danh sách lịch hẹn chứng thực điện tử ' + this.datePipe.transform(newDate, 'dd/MM/yyyy');
    let data  = this.ELEMENTDATA;
    return this.scheduleCtdtService.exportScheduleCtdtToExcel(data, name, "CTDT");
  }
  changePageOrSize(obj) {
    if (tUtils.hasOwnProperty(obj, 'currentPage')) {
      this.page = obj.currentPage;
      this.pageIndex = obj.currentPage;
    }
    if (tUtils.hasOwnProperty(obj, 'itemsPerPage')) {
      this.size = obj.itemsPerPage;
    }
    if (tUtils.hasOwnProperty(obj, 'totalItems')) {
      this.countResult = obj.totalItems;
    } else {
      this.paginate();
    }
  }
  paginate() {
    this. onConfirmSearch() ;
  }
  onClickOpenReminderMenu() {
    this.expandReminderMenu = !this.expandReminderMenu;
    this.remindId = '';
    //  this.changeSearchRemind('','');
  }
  convertFormatDateTime(date){
    try{
      if(date == null || date == "")
        return "";
      var date1  = date.split("T")[0];
      var date2 = date.split("T")[1];
      let date11 = date1.split("-");
      return date11[2] + "/" + date11[1] + "/" + date11[0] + " " + date2.split(".")[0];
    }catch{return "";}

  }

  convertTo24HourFormat(time: string): string {
    if (!time) return '';

    const [hourMinute, period] = time.split(' '); // Tách giờ phút và AM/PM
    let [hour, minute] = hourMinute.split(':').map(Number);

    if (period === 'PM' && hour !== 12) {
      hour += 12; // Chuyển đổi PM thành 24 giờ
    } else if (period === 'AM' && hour === 12) {
      hour = 0; // Chuyển đổi 12 AM thành 00 giờ
    }

    return `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}:00`;
  }

  checkAppointmentStatus(appointmentDateTime: string, appointmentTime: string): string {
    if (!appointmentDateTime || !appointmentTime) {
      return 'Không xác định';
    }

    // Chuyển đổi row?.appointmentDateTime thành đối tượng Date
    const appointmentDate = new Date(appointmentDateTime);

    // Chuyển đổi appointmentTime (11:00 AM) thành định dạng 24h (11:00:00 hoặc 14:00:00)
    const timeParts = appointmentTime.match(/(\d+):(\d+) (\w+)/);
    if (!timeParts) {
      return 'Không xác định';
    }
    let hours = parseInt(timeParts[1], 10);
    const minutes = parseInt(timeParts[2], 10);
    const period = timeParts[3];

    if (period === 'PM' && hours !== 12) {
      hours += 12;
    } else if (period === 'AM' && hours === 12) {
      hours = 0;
    }

    // Gán giờ & phút vào ngày hẹn
    appointmentDate.setHours(hours, minutes, 0, 0);

    // Lấy thời gian hiện tại
    const now = new Date();

    // So sánh thời gian
    return appointmentDate >= now ? 'Còn hạn' : 'Quá hạn';
  }

  getAppointmentStatusClass(appointmentDateTime: string, appointmentTime: string): string {
    const status = this.checkAppointmentStatus(appointmentDateTime, appointmentTime);
    return status === 'Còn hạn' ? 'status-blue' : 'status-red';
  }


  // IGATESUPP-115852 bước 2 từ chối lịch hẹn
  cancelDialog(id) {
    const dialogData = new ConfirmCancelDialogModel();
    const dialogRef = this.dialog.open(ConfirmCancelScheduleComponent, {
      width: '500px',
      data: dialogData,
      disableClose: true,
      autoFocus: false
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
      const res = dialogResult;
      if (res === true) {
        let paramsQuery = "?status=2";
        this.scheduleCtdtService.putUpdateStatusScheduleCtdt(id,paramsQuery).subscribe(data=>{  this. onConfirmSearch() ; });
        const msgObj = {
          vi: 'Từ chối lịch hẹn thành công!',
          en: 'Successful delete!'
        };
        this.snackbarService.openSnackBar(1, msgObj[this.selectedLang], '', 'success_notification',  800);

      }
    });
  }
  // IGATESUPP-115852 bước 2 tải eform
  receivingDossier(id,procedureId, processDefinition, eForm2, applicantEForm2) {
    const isOpenInNewTab = this.deploymentService.getAppDeployment()?.env?.OS_KTM?.openInNewTab == true ? true  : false;
    const hideDefaultFormIO = this.deploymentService.getAppDeployment()?.env?.OS_KGG?.hideDefaultFormIO;

    // const defaultFormIO = this.deploymentService.newConfigV2?.defaultFormIO.enable ? this.deploymentService.newConfigV2?.defaultFormIO : this.config.defaultFormIO;
    const defaultFormIO =  this.deploymentService.getAppDeployment()?.defaultFormIO;
    this.router.navigate(['/dossier/receive-schedule-ctdt-cto/receiving-cto/' + procedureId], {
      queryParams: {
        processDefinition: processDefinition,
        schedulectdtId:id,
        eForm: eForm2.length !== 0 ? eForm2 : hideDefaultFormIO ? "" : defaultFormIO.eForm,
        applicantEForm: applicantEForm2.length !== 0 ? applicantEForm2 : hideDefaultFormIO ? "" : defaultFormIO.applicantEForm
      }
    });
  }


}

function compare(a: number | string, b: number | string, isAsc: boolean) {
  return (a < b ? -1 : 1) * (isAsc ? 1 : -1);
}
