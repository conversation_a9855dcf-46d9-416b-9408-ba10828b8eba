import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { ApiProviderService } from 'src/app/core/service/api-provider.service';
import { Observable } from 'rxjs';

@Injectable({
    providedIn: 'root'
})
export class ReporterService {

    constructor(
        private http: HttpClient,
        private apiProviderService: ApiProviderService
    ) { }

    private reporter = this.apiProviderService.getUrl('digo', 'reporter');
    private localhost = 'http://localhost:8080';
    getListProcedureFrequent(searchString): Observable<any> {
        let headers = new HttpHeaders();
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        switch (localStorage.getItem('isLoggedIn')) {
            case 'false':
                const token = localStorage.getItem('OAuth2TOKEN');
                headers = headers.append('Authorization', 'Bearer ' + token);
                return this.http.get(this.reporter + '/procedure/--frequent' + searchString, { headers }).pipe();
            case 'true':
                return this.http.get(this.reporter + '/procedure/--frequent' + searchString, { headers }).pipe();
        }
    }

    getListProcedureQuantitySector(searchString): Observable<any> {
        let headers = new HttpHeaders();
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        switch (localStorage.getItem('isLoggedIn')) {
            case 'false':
                const token = localStorage.getItem('OAuth2TOKEN');
                headers = headers.append('Authorization', 'Bearer ' + token);
                return this.http.get(this.reporter + '/procedure-sector-agency/--procedure-quantity-by-tag' + searchString, { headers }).pipe();
            // tslint:disable-next-line:max-line-length
            // return this.http.get('http://localhost:8080' + '/procedure-sector-agency/--procedure-quantity-by-tag' + searchString, { headers }).pipe();
            case 'true':
                return this.http.get(this.reporter + '/procedure-sector-agency/--procedure-quantity-by-tag' + searchString, { headers }).pipe();
            // tslint:disable-next-line:max-line-length
            // return this.http.get('http://localhost:8080' + '/procedure-sector-agency/--procedure-quantity-by-tag' + searchString, { headers }).pipe();
        }
    }

    getListAgencyStatistic(searchString): Observable<any> {
        let headers = new HttpHeaders();
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        switch (localStorage.getItem('isLoggedIn')) {
            case 'false':
                const token = localStorage.getItem('OAuth2TOKEN');
                headers = headers.append('Authorization', 'Bearer ' + token);
                return this.http.get(this.reporter + '/agency/--dossier?' + searchString, { headers }).pipe();
            case 'true':
                return this.http.get(this.reporter + '/agency/--dossier?' + searchString, { headers }).pipe();
        }
    }

    downloadFromData(id, requestBody): Observable<any> {
        let headers = new HttpHeaders();
        return this.http.post<any>(this.reporter + '/office-template/--download-from-data?save-format=docx&id=' + id, requestBody, { responseType: 'blob' as 'json' }).pipe();
    }

    postReport(body): Observable<any>{
        let headers = new HttpHeaders()
        headers = headers.append('Accept', 'application/json');
        headers = headers.set('Accept-Language', localStorage.getItem('language'))
        return this.http.post(this.reporter + '/report/--post-report', body, { headers });
      }

    putReportName(id,body): Observable<any>{
        let headers = new HttpHeaders()
        headers = headers.append('Accept', 'application/json');
        headers = headers.set('Accept-Language', localStorage.getItem('language'))
        return this.http.put(this.reporter + `/report/${id}/--update-name-report`, body, { headers });
      }

    deleteReport(id) {    
        return this.http.delete<any>(this.reporter + `/report/${id}`).pipe();
      }

    getReportByKeyword(search): Observable<any>{
        let headers = new HttpHeaders();
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        return this.http.get(this.reporter + '/report/--report' + search, { headers }).pipe();
      }

    syncSectorByAgency(body: any) {
        let headers = new HttpHeaders()
        headers = headers.append('Accept', 'application/json');
        headers = headers.set('Accept-Language', localStorage.getItem('language'))
        return this.http.put(this.reporter + `/procedure-sector-agency/--update-sector-by-agency?is-replace=false`, body, { headers });
    }
    printBillNew(requestBody): Observable<any> {
        let headers = new HttpHeaders();
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        return this.http.post<any>(this.reporter + '/print-report/--output', requestBody, { headers }).pipe();
    }
}
