import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { DlkBaoCaoChiThi08CapSoRoutingModule } from './dlk-baocao-chithi08-capso-routing.module';
import { DlkBaoCaoChiThi08CapSoComponent } from './dlk-baocao-chithi08-capso.component';
import { SharedModule } from 'src/app/shared/shared.module';
import { NgxPrinterModule } from 'ngx-printer';


@NgModule({
  declarations: [DlkBaoCaoChiThi08CapSoComponent],
  imports: [
    CommonModule,
    DlkBaoCaoChiThi08CapSoRoutingModule,
    SharedModule,
    NgxPrinterModule.forRoot({ printOpenWindow: true })
  ]
})
export class DlkBaoCaoChiThi08CapSoModule { }
