import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Subject } from 'rxjs';
import { EnvService } from 'src/app/core/service/env.service';

@Injectable({
  providedIn: 'root'
})
export class DeploymentService {
  constructor(
    private http: HttpClient,
    private envService: EnvService
  ) { }

  getConfigUrls = this.envService.getConfig().deploymentUrl +
    `/app-deployment?deployment-id=${this.envService.getConfig().deploymentId}&app-code=web-onegate`;

  setConfig() {
    let headers = new HttpHeaders();
    headers = headers.append('Content-Type', 'application/json');
    this.http.get(this.getConfigUrls, { headers, responseType: 'json' }).subscribe((res: any) => {
      localStorage.setItem('deploymentVariables', JSON.stringify(res));
      return res.configuration;
    });
  }

  getConfig(): any {
    const deploymentVariables = JSON.parse(localStorage.getItem('deploymentVariables'));
    if (deploymentVariables) {
      return deploymentVariables.configuration;
    } else {
      return this.setConfig();
    }
  }

  getMapsConfig(): any{
    const config = this.getConfig();
    return config.maps;
  }
}
