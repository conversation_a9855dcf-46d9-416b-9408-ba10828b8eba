<button mat-icon-button class="close-button" (click)="onDismiss()">
    <mat-icon>close</mat-icon>
</button>
<h3 class="dialog_title" mat-dialog-title>Chi tiết hồ sơ {{budgetDossierDetail.hsid}}</h3>
<div mat-dialog-content class="dialog_content">
    <ng-container *ngIf="!!budgetDossierDetail">
        <div class="tbl mat-elevation-z2">
            <div fxLayout="row" fxLayoutAlign="center">
                <div fxFlex="42">
                    <ul>
                        <li>
                            <span class="detail-title"><PERSON><PERSON> <PERSON><PERSON> sơ: </span>
                            <span>{{budgetDossierDetail.hsid}}</span>
                        </li>
                        <li>
                            <span class="detail-title">Tên đơn vị/dự án: </span>
                            <span>{{budgetDossierDetail.ten_hs}}</span>
                        </li>
                        <li>
                            <span class="detail-title">Người đăng ký nộ<PERSON> <PERSON>ồ sơ: </span>
                            <span>{{budgetDossierDetail.nguoi_dk}}</span>
                        </li>
                        <li>
                            <span class="detail-title">Email: </span>
                            <span>{{budgetDossierDetail.email}}</span>
                        </li>
                        <li>
                            <span class="detail-title">Số điện thoại: </span>
                            <span>{{budgetDossierDetail.sdt_didong}}</span>
                        </li>
                        <li>
                            <span class="detail-title">Ngày đăng ký nộp hồ sơ: </span>
                            <span>{{budgetDossierDetail.ngay_dk}}</span>
                        </li>
                    </ul>
                </div>
                <div fxFlex="28">
                    <ul>
                        <li>
                            <span class="detail-title">Mã số cơ quan tài chính: </span>
                            <span>{{budgetDossierDetail.cqtc_ma}}</span>
                        </li>
                    </ul>
                </div>
                <div fxFlex="28">
                    <ul>
                        <li>
                            <span class="detail-title">Mã ĐVQHNS được cấp: </span>
                            <span>{{budgetDossierDetail.ma}}</span>
                        </li>
                    </ul>
                </div>
            </div>
            <mat-divider></mat-divider>
            <div fxLayout="row" fxLayoutAlign="center">
                <div fxFlex="42">
                    <ul>
                        <li>
                            <span class="detail-title">Họ và tên người nhập tờ khai: </span>
                            <span>{{budgetDossierDetail.nguoi_tao}}</span>
                        </li>
                        <li>
                            <span class="detail-title">Ngày NSD nhập tờ khai: </span>
                            <span>{{budgetDossierDetail.ngay_tao}}</span>
                        </li>
                        <li>
                            <span class="detail-title">Ngày trả thực tế: </span>
                            <span>{{budgetDossierDetail.ngay_pd}}</span>
                        </li>
                        <li>
                            <span class="detail-title">Người thực hiện cấp mã: </span>
                            <span>{{budgetDossierDetail.nguoi_pd}}</span>
                        </li>
                        <li>
                            <span class="detail-title">Kiểu hồ sơ: </span>
                            <span *ngIf="budgetDossierDetail.kieu_hs === '1'">Hồ sơ dùng cho đơn vị dự toán, đơn vị có quan hệ với ngân sách nhà nược, đơn vị khác có quan hệ với ngân sách</span>
                            <span *ngIf="budgetDossierDetail.kieu_hs === '2'">Hồ sơ dùng cho dự án đầu tư ở giai đoạn chuẩn bị đầu tư</span>
                            <span *ngIf="budgetDossierDetail.kieu_hs === '3'">Hồ sơ dùng cho dự án đầu tư ở giai đoạn thực hiện đầu tư</span>
                            <span *ngIf="budgetDossierDetail.kieu_hs === '4'">Hồ sơ dùng cho dự án thay đổi giai đoạn từ chuẩn bị đầu tư sang thực hiện đầu tư</span>
                            <span *ngIf="budgetDossierDetail.kieu_hs === '5'">Hồ sơ dung cho dự án/đơn vị đăng ký thay đổi thông tin</span>
                        </li>
                        <li>
                            <span class="detail-title">Kiểu tiếp nhận: </span>
                            <span *ngIf="budgetDossierDetail.kieu_tiep_nhan === 1">Trực tuyến</span>
                            <span *ngIf="budgetDossierDetail.kieu_tiep_nhan === 2">Trực tiếp</span>
                        </li>
                        <li>
                            <span class="detail-title">Trạng thái hồ sơ: </span>
                            <span *ngIf="budgetDossierDetail.trang_thai === 1">Chờ tiếp nhận</span>
                            <span *ngIf="budgetDossierDetail.trang_thai === 2">Từ chối tiếp nhận</span>
                            <span *ngIf="budgetDossierDetail.trang_thai === 3">Đã tiếp nhận</span>
                            <span *ngIf="budgetDossierDetail.trang_thai === 4">Đóng</span>
                            <span *ngIf="budgetDossierDetail.trang_thai === 5">Hủy</span>
                            <span *ngIf="budgetDossierDetail.trang_thai === 6">Đã phê duyệt</span>
                        </li>
                        <li>
                            <span class="detail-title">Ngày trả kết quả hồ sơ theo quy định: </span>
                            <span>{{budgetDossierDetail.ngay_tra}}</span>
                        </li>
                    </ul>
                </div>
                <div fxFlex="28"></div>
                <div fxFlex="28"></div>
            </div>
        </div>
    </ng-container>
    <br>
    <div>
        <b>Lịch sử xử lý hồ sơ</b>
    </div>
    <ng-container>
        <div class="tbl mat-elevation-z2">
            <table mat-table [dataSource]="dataSourceDL2">
                <ng-container matColumnDef="NGAY_XL">
                    <mat-header-cell *matHeaderCellDef>Ngày xử lý</mat-header-cell>
                    <mat-cell *matCellDef="let row" data-label="Ngày xử lý">{{row.NGAY_XL}}</mat-cell>
                </ng-container>

                <ng-container matColumnDef="TRANG_THAI_TEN">
                    <mat-header-cell *matHeaderCellDef>Trạng thái hồ sơ</mat-header-cell>
                    <mat-cell *matCellDef="let row" data-label="Trạng thái hồ sơ" class="ofw-anywhere">{{row.TRANG_THAI_TEN}}</mat-cell>
                </ng-container>

                <ng-container matColumnDef="CAN_BO_XL">
                    <mat-header-cell *matHeaderCellDef>Cán bộ xử lý</mat-header-cell>
                    <mat-cell *matCellDef="let row" data-label="Cán bộ xử lý" class="ofw-anywhere">{{row.CAN_BO_XL}}</mat-cell>
                </ng-container>

                <ng-container matColumnDef="DON_VI_XL">
                    <mat-header-cell *matHeaderCellDef>Đơn vị xử lý</mat-header-cell>
                    <mat-cell *matCellDef="let row" data-label="Đơn vị xử lý" class="ofw-anywhere">{{row.DON_VI_XL}}</mat-cell>
                </ng-container>

                <ng-container matColumnDef="NOI_DUNG_XL">
                    <mat-header-cell *matHeaderCellDef>Nội dung xử lý</mat-header-cell>
                    <mat-cell *matCellDef="let row" data-label="Nội dung xử lý" class="ofw-anywhere">{{row.NOI_DUNG_XL}}</mat-cell>
                </ng-container>

                <mat-header-row *matHeaderRowDef="displayedColumnsDL2"></mat-header-row>
                <mat-row *matRowDef="let row; columns: displayedColumnsDL2;"></mat-row>
            </table>
            <div class="pagination">
                <ul class="temp-arr">
                    <li *ngFor="let item of ELEMENTDATADL2  | paginate: {itemsPerPage: size, currentPage: page, totalItems: countResult, id: 'pgnx2'}">
                    </li>
                </ul>
                <div class="page-size">
                    <span i18n>Hiển thị </span>
                    <mat-form-field appearance="outline">
                        <mat-select [(value)]="size" (valueChange)="paginate(pageIndex, 1)">
                            <mat-option *ngFor='let opt of pgSizeOptions;' [value]="opt">{{opt}}</mat-option>
                        </mat-select>
                    </mat-form-field>
                    <span><span i18n>trên</span> {{countResult}} <span i18n>bản ghi</span></span>
                </div>
                <div class="control">
                    <pagination-controls id="pgnx2" (pageChange)="page = $event; paginate(page, 0)" responsive="true" previousLabel="" nextLabel="">
                    </pagination-controls>
                </div>
            </div>
        </div>
    </ng-container>
</div>
<div fxLayout="row" fxLayout.xs="column" fxLayout.sm="column" fxLayoutAlign="center">
    <button mat-flat-button fxFlex='30' class="applyBtn" (click)="onConfirm()">
        <span>Đóng</span>
    </button>
</div>