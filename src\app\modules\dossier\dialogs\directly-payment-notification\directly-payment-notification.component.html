<button mat-icon-button class="close-button" (click)="onDismiss()">
    <mat-icon>close</mat-icon>
</button>
<h3 class="dialog_title" mat-dialog-title>Thông báo thanh toán trực tiếp</h3>
<div mat-dialog-content class="dialog_content">
    <span>Bạn có chắc chắn muốn gửi thông báo thanh toán trực tiếp cho hồ sơ </span><span
        class="highlight">{{dossierCode}}</span><span>?</span>
    <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="column" *ngIf="showCheckConfirmPayment && showConfirmPayment" style="padding: 10px 0;font-weight: 500;">
        <mat-checkbox [(ngModel)]="checkConfirmPayment"><span i18n="@@ConfirmPaymentLabel">X<PERSON><PERSON> nh<PERSON><PERSON> s<PERSON> tiền yêu c<PERSON>u thanh toán</span>{{totalCost ? ':' : ''}} <span class="highlight">{{totalCost}}</span>
        </mat-checkbox>
    </div>
    <form *ngIf="!hideDaysWaitingForPayment" [formGroup]="updateForm" class="updateForm" id="ngupdateForm">
        <div fxLayout="column" fxLayout.xs="column" fxLayout.sm="column" class="formFieldOutline" fxLayoutAlign="space-between">
            <mat-form-field appearance="outline" fxFlex.gt-sm="column" fxFlex.gt-xs="column" fxFlex='grow'>
                <mat-label i18n="@@daysWaitingForPayment">Số ngày chờ thanh toán</mat-label>
                <input type="number" matInput formControlName="daysWaitingForPayment" required oninput="this.value = !!this.value && Math.abs(this.value) > 0 ? Math.abs(this.value) : null">
            </mat-form-field>
        </div>
    </form>
    <div class="commentEditor" fxLayout="column" fxLayout.xs="column" fxLayout.sm="column">
        <p class="lbl"><span i18n>Lý do</span><span style="color:#ce7a58;">&nbsp;*</span></p>
        <ckeditor debounce="10" [editor]="Editor" class="customCKEditor" (change)="onCommentEditorChange($event)"
            fxFlex='grow' [config]='commentConfig'>
        </ckeditor>
        <div class="errorMsg" *ngIf="isCKMaxlength">
            <span i18n>Nội dung không quá 500 ký tự</span>
            <div class="err">
                <mat-icon>priority_high</mat-icon>
            </div>
        </div>
    </div>
</div>

<digo-check-send-notify functionType="directlyPaymentNotification" functionTypeOfficer="" [receiveType]="receiveType"></digo-check-send-notify>

<div fxLayout="row" fxLayout.xs="column" fxLayout.sm="column" fxLayoutAlign="center">
    <button [disabled]="disableConfirmPayment" mat-flat-button fxFlex='30' class="applyBtn" (click)="onConfirm()">
        <span i18n>Đồng ý</span>
    </button>
</div>