import { Component, OnInit, Output, Input, EventEmitter, OnChanges, SimpleChanges} from '@angular/core';
import { FormGroup, FormBuilder, FormArray, FormControl, Validators, AbstractControl } from '@angular/forms';
import { EnvService } from 'src/app/core/service/env.service';
import { AdapterService } from 'src/app/data/service/adapter/adapter.service';
import { DeploymentService } from 'src/app/data/service/deployment.service';
import { SnackbarService } from 'src/app/data/service/snackbar/snackbar.service';
import {GetCitizenInfoService} from 'shared/components/get-citizen-info/get-citizen-info.service';
import {HttpClient} from "@angular/common/http";
import { MatDatepickerInputEvent } from '@angular/material/datepicker';
import {ConfirmReportComponent, ConfirmReportDialogModel} from 'shared/components/dialogs/confirm-report/confirm-report.component';
import * as tUtils from 'src/app/data/service/thoai.service';
import {MatDialog} from '@angular/material/dialog';
import {KeycloakService} from "keycloak-angular";
import { UserService } from 'data/service/user.service';
import { ProcedureService } from 'src/app/data/service/procedure/procedure.service';

@Component({
  selector: 'app-get-citizen-info-bdh',
  templateUrl: './get-citizen-info-bdh.component.html',
  styleUrls: ['./get-citizen-info-bdh.component.scss']
})
export class GetCitizenInfoBdhComponent implements OnInit, OnChanges {

 
   configV2 = this.deploymentService.getAppDeployment();
   isKTMCitizenStatistic = this.deploymentService.env?.OS_KTM?.isKTMCitizenStatistic ? this.deploymentService.env?.OS_KTM?.isKTMCitizenStatistic : false;
   limitSearchCitizenPros = this.configV2?.limitSearchCitizenPros ? this.configV2?.limitSearchCitizenPros > 0 : false;
   requiredCitizenInfoConfig = this.configV2?.requiredCitizenInfoConfig ? this.configV2?.requiredCitizenInfoConfig > 0 : false;
   searchProcedureId = null;
 
     memberIdentityNumbers = "";
     data = {
       anyPermissions: ['oneGateResidentialInfo' , 'oneGateHouseholdInfo'] // Danh sách quyền của user
     };
     @Output() formValueChangedCitizenInfoBDH = new EventEmitter<any>(); // Event để phát dữ liệu
     @Input() dataMemberCitizenInfoBDH: any;
     @Input() checkId: any;
     @Input() dossierId: string;
     @Input() dossierCode: string;
     // @Input() eformId:string;
     @Input() procedureId:string;
     memberForms: FormGroup;
     productForm: FormGroup;
     receiveData = new FormGroup({
       fullNameOwner: new FormControl(''),
       identityOwner: new FormControl(''),
       birthDayOwner: new FormControl(''),
   
     });
     pickers: any[] = [];
     birthdateErrors: string[] = [];
     fullNameOwner = "";
     identityOwner = "";
     birthDayOwner = "";
     identityMember = "";
     ELEMENTDATA = null;
     config = this.envService.getConfig();
     correct: any;
     message: string;
     items: any;
     key: '';
     value: '';
     eformId = '';
     agencyId = '';
     subsystemId = this.deploymentService.env.subsystemId;
     checkIdentity: any;
     checkCitizen = this.deploymentService.getAppDeployment()?.checkCitizen;
     checkCH ='';
     checkTV='';
     checkHT='';
     duplicateError = false; // Biến lưu trạng thái trùng lặp
     checkIdFlex: string = "20";
     getOriginInfo= this.checkCitizen?.getOriginInfo ? this.checkCitizen.getOriginInfo:false;
     env = this.deploymentService.getAppDeployment()?.env;
     userName: string;
     accountId: string;
     isShowHousehold=false;
   
     selectedLang: string;
 
     constructor(private fb: FormBuilder,
       private envService: EnvService,
       private snackbarService: SnackbarService,
       private adapterService: AdapterService,
       private deploymentService: DeploymentService,
       private getCitizenInfoService: GetCitizenInfoService,
       private http: HttpClient,
       private dialog: MatDialog,
       private keycloakService: KeycloakService,
       private userService: UserService,
       private procedureService: ProcedureService,
     ) {
       const userAgency = JSON.parse(localStorage.getItem('userAgency'));
       this.agencyId = userAgency.id;
       this.productForm = this.fb.group({
         quantities: this.fb.array([])
       });
       this.pickers = [true]; // Khởi tạo tham chiếu cho từng dòng
     }
   
     ngOnInit(): void {
       console.log('Dữ liệu nhận từ cha khi khởi tạo:', this.dataMemberCitizenInfoBDH);
       const permissions = this.userService.getUserPermissions();
       for(const p of permissions){
           if(p.permission.code ==="oneGateResidentialInfo"){
               this.isShowHousehold = true
               break;
           }
       }
       this.selectedLang = localStorage.getItem('language');
       this.checkIdFlex = this.checkId === '1' ? "33" : "20";
       if(typeof this.dataMemberCitizenInfoBDH !== 'undefined'){
         if(this.dataMemberCitizenInfoBDH.length == 0 || this.dataMemberCitizenInfoBDH === undefined || this.dataMemberCitizenInfoBDH == null){
           this.addQuantity();
         }else{
           // Thêm một phần tử vào FormArray 'quantities'
           if(typeof this.dataMemberCitizenInfoBDH !== 'undefined'){
             for(let i =0; i< this.dataMemberCitizenInfoBDH.length; i++){
             this.addQuantityData({
               fullNameMember: this.dataMemberCitizenInfoBDH[i].fullNameMember,
               birthDayMember: new Date(this.dataMemberCitizenInfoBDH[i].birthDayMember),
               identityMember: this.dataMemberCitizenInfoBDH[i].identityMember,
               cmndMember: this.dataMemberCitizenInfoBDH[i].cmndMember,
               permanentAddressMember: this.dataMemberCitizenInfoBDH[i].permanentAddressMember,
               currentAddressMember: this.dataMemberCitizenInfoBDH[i].currentAddressMember,
               checkInfo: this.dataMemberCitizenInfoBDH[i].checkInfo
             });
             }
           }
         }
       }else{
         this.addQuantity();
       }
       this.getUserAccount();
       // Lắng nghe thay đổi trong form
       this.productForm.valueChanges.subscribe((value) => {
         this.formValueChangedCitizenInfoBDH.emit(value); // Phát dữ liệu lên parent
       });
       
     }
     ngOnChanges(changes: SimpleChanges) {
       if (changes['dataMemberCitizenInfoBDH'] && changes['dataMemberCitizenInfoBDH'].currentValue) {
         console.log('Dữ liệu mới từ cha:', changes['dataMemberCitizenInfoBDH'].currentValue);
       }
       this.checkIdFlex = this.checkId === '1' ? "33" : "20";
       if(typeof this.dataMemberCitizenInfoBDH !== 'undefined'){
         if(this.dataMemberCitizenInfoBDH.length == 0 || this.dataMemberCitizenInfoBDH === undefined || this.dataMemberCitizenInfoBDH == null){
           
         }else{
           // Thêm một phần tử vào FormArray 'quantities'
           if(typeof this.dataMemberCitizenInfoBDH !== 'undefined'){
             for(let i =0; i< this.dataMemberCitizenInfoBDH.length; i++){
               if(i==0){
                 if(this.quantities().length > 0){
                   this.quantities().at(i).patchValue({
                     fullNameMember: this.dataMemberCitizenInfoBDH[i].fullNameMember,
                     birthDayMember: new Date(this.dataMemberCitizenInfoBDH[i].birthDayMember),
                     identityMember: this.dataMemberCitizenInfoBDH[i].identityMember,
                     cmndMember: this.dataMemberCitizenInfoBDH[i].cmndMember,
                     permanentAddressMember: this.dataMemberCitizenInfoBDH[i].permanentAddressMember,
                     currentAddressMember: this.dataMemberCitizenInfoBDH[i].currentAddressMember,
                     checkInfo: this.dataMemberCitizenInfoBDH[i].checkInfo
                   }); 
                 }else{
                   this.addQuantityData({
                     fullNameMember: this.dataMemberCitizenInfoBDH[i].fullNameMember,
                     birthDayMember: new Date(this.dataMemberCitizenInfoBDH[i].birthDayMember),
                     identityMember: this.dataMemberCitizenInfoBDH[i].identityMember,
                     cmndMember: this.dataMemberCitizenInfoBDH[i].cmndMember,
                     permanentAddressMember: this.dataMemberCitizenInfoBDH[i].permanentAddressMember,
                     currentAddressMember: this.dataMemberCitizenInfoBDH[i].currentAddressMember,
                     checkInfo: this.dataMemberCitizenInfoBDH[i].checkInfo
                   });
                 }
               }else{
                 this.addQuantityData({
                   fullNameMember: this.dataMemberCitizenInfoBDH[i].fullNameMember,
                   birthDayMember: new Date(this.dataMemberCitizenInfoBDH[i].birthDayMember),
                   identityMember: this.dataMemberCitizenInfoBDH[i].identityMember,
                   cmndMember: this.dataMemberCitizenInfoBDH[i].cmndMember,
                   permanentAddressMember: this.dataMemberCitizenInfoBDH[i].permanentAddressMember,
                   currentAddressMember: this.dataMemberCitizenInfoBDH[i].currentAddressMember,
                   checkInfo: this.dataMemberCitizenInfoBDH[i].checkInfo
                 });
               }
             }
           }
         }
       }
     }
     getUserAccount() {
       if (this.env?.OS_DBN?.checkCitizenWithUserName) {
         this.keycloakService.loadUserProfile().then(user => {
           // tslint:disable-next-line: no-string-literal
           this.accountId = user['attributes'].user_id[0];
           this.userService.getUserInfo(this.accountId).subscribe(data => {
             this.userName = (!!data?.account?.username && data?.account?.username.length > 0) ? data?.account?.username[0]?.value : null;
             if (!!this.userName && !!this.env?.OS_DBN?.checkCitizenWithUserNameValue ){
               this.userName = this.userName + this.env?.OS_DBN?.checkCitizenWithUserNameValue;
             }
           });
         });
       }
     }
     checkValidProcedure() {
       return new Promise((resolve) => {
         this.procedureService.getProcedureDetail(this.procedureId).subscribe(procedureDetail => {
           if (procedureDetail.limitCheckCitizenFields == null) {
             let code = procedureDetail.code;
             let agencyId = JSON.parse(localStorage.getItem('userAgency')).id;
             let searchStr = `?sort=createdDate,desc&page=0&size=50&spec=page&agency-id=${agencyId}&status=1&common-use=true&fix-role=false&keyword=${code}`;
   
             this.procedureService.getListSearchProcedure(searchStr).subscribe(procedureCommonUse => {
               if (procedureCommonUse.numberOfElements == 0 ) {
                 if(this.requiredCitizenInfoConfig){
                   this.snackbarService.openSnackBar(0, '', "Thủ tục này chưa cấu hình Dữ liệu được cho phép tra cứu!", 'error_notification', this.config.expiredTime);
                   resolve(false);
                 }
                 else
                   resolve(true);
               } 
               else {
                 this.procedureService.getProcedureDetail(procedureCommonUse.content[0].id).subscribe(detail => {
                   if (detail.limitCheckCitizenFields == null) {
                     if(this.requiredCitizenInfoConfig){
                       this.snackbarService.openSnackBar(0, '', "Thủ tục này chưa cấu hình Dữ liệu được cho phép tra cứu!", 'error_notification', this.config.expiredTime);
                       resolve(false);
                     }
                     else
                       resolve(true);
                   } 
                   else{
                     this.searchProcedureId = this.procedureId;
                     resolve(true);
                   }
                 });
               }
             });
           } 
           else{
             this.searchProcedureId = this.procedureId;
             resolve(true);
           }
         });
       });
     }
     getClientIp() {
       this.http.get<{ ip: string }>(!!this.deploymentService.env?.iPCheckUrl ? this.deploymentService.env?.iPCheckUrl : 'https://ip.vnptioffice.vn/?format=json')
         .subscribe(data => {
           localStorage.setItem('clientIP', data.ip);
         });
     }
     quantities(): FormArray {
       return this.productForm.get("quantities") as FormArray
     }
     newQuantity(): FormGroup {
       return this.fb.group({
         fullNameMember: '',
         birthDayMember: '',
         cmndMember: '',
         identityMember: '',
         permanentAddressMember: '',
         currentAddressMember: '',
         checkInfo: '',
       })
     }
     // add input
     addQuantity() {
       this.quantities().push(this.newQuantity());
       this.pickers.push(true); // Thêm tham chiếu cho dòng mới
     }
     // Phương thức thêm phần tử vào FormArray
   addQuantityData(data?: any): void {
     const quantityGroup = this.newQuantity();
 
     // Nếu có dữ liệu truyền vào, gán giá trị cho FormGroup
     if (data) {
       quantityGroup.patchValue(data);
     }
 
     // Thêm FormGroup vào FormArray
     this.quantities().push(quantityGroup);
   }
   
     // Thêm một form mới
     // addForm(): void {
     //   this.memberForms.push(this.createMemberForm());
     // }
     // remove input
     removeQuantity(i: number) {
       this.quantities().removeAt(i);
     }
     validateDate(index: number): void {
       let formObj = this.productForm.getRawValue();
       const birthdateValue =formObj.quantities[index].birthdate;
       if (!birthdateValue) {
         this.birthdateErrors[index] = 'Trường này không được để trống!';
         return;
       }
   
       // Regex kiểm tra định dạng ngày/tháng/năm hoặc năm
       const dateRegex = /^\d{2}\/\d{2}\/\d{4}$/; // dd/mm/yyyy
       const yearRegex = /^\d{4}$/; // yyyy
   
       if (dateRegex.test(birthdateValue)) {
         this.birthdateErrors[index] = ''; // Định dạng ngày/tháng/năm hợp lệ
       } else if (yearRegex.test(birthdateValue)) {
         this.birthdateErrors[index] = ''; // Định dạng năm hợp lệ
       } else {
         this.birthdateErrors[index] = 'Ngày sinh không đúng định dạng! Nhập ngày/tháng/năm hoặc năm (yyyy).';
       }
     }
      // Khi người dùng nhập liệu
   onInputChange(event: any, index: number): void {
     const inputValue = event.target.value;
     const yearRegex = /^\d{4}$/; // yyyy
     const dateRegex = /^\d{2}\/\d{2}\/\d{4}$/; // dd/mm/yyyy
     let formObj = this.productForm.getRawValue();
 
     if (yearRegex.test(inputValue)) {
       // Người dùng nhập chỉ năm
       
       const birthdateValue =formObj.quantities[index].birthdate;
       formObj.quantities[index].birthdate?.setValue(inputValue);
       this.birthdateErrors[index] = '';
     } else if (dateRegex.test(inputValue)) {
       // Người dùng nhập ngày/tháng/năm
       formObj.quantities[index].birthdate?.setValue(inputValue);
       this.birthdateErrors[index] = '';
     } else {
       // Sai định dạng
       this.birthdateErrors[index] = 'Vui lòng nhập ngày/tháng/năm hoặc năm (yyyy).';
     }
   }
 
   // Khi người dùng chọn từ lịch
   onDateChange(event: MatDatepickerInputEvent<Date>, index: number): void {
     const selectedDate = event.value;
     let formObj = this.productForm.getRawValue();
     if (selectedDate) {
       formObj.quantities[index].birthdate?.setValue(selectedDate.toISOString().split('T')[0]); // Lưu định dạng ISO (yyyy-MM-dd)
       this.birthdateErrors[index] = '';
     }
   }
 
   // Khi người dùng chọn năm từ "multi-year" view
   onYearSelected(event: Date, index: number,  picker: any): void {
     const year = event.getFullYear();
     let formObj = this.productForm.getRawValue();
     formObj.quantities[index].birthdate?.setValue(year.toString());
     picker.close(); // Đóng picker sau khi chọn năm
     this.birthdateErrors[index] = '';
   }
   // Kiểm tra trùng lặp
   isDuplicate(): void {
    this.checkHT = '';
    const quantities = this.productForm.get('quantities') as FormArray;
  
    const allValues: string[] = quantities.controls.reduce((acc: string[], group: AbstractControl) => {
      const identity = group.get('identityMember')?.value?.trim();
      const cmnd = group.get('cmndMember')?.value?.trim();
      return [...acc, ...[identity, cmnd].filter(v => v)]; // ⚠️ lọc bỏ rỗng, undefined, null
    }, []);
  
    const uniqueValues = new Set(allValues);
    this.duplicateError = uniqueValues.size !== allValues.length;
  
    if (this.duplicateError) {
      console.warn('❗ Có dữ liệu CMND/CCCD bị trùng (đã bỏ qua giá trị rỗng)');
    }
  }
     checkValidIndentity(indentity, cmnd) {
       return new Promise((resolve) => {
         try {
           if ((indentity != '' &&indentity.length !== 12) || (cmnd != '' && cmnd.length !== 9)) {
             const msgObj = {
               vi: 'Chứng minh nhân dân hoặc căn cước công dân chưa đúng!',
               en: 'Identity proof is not correct!'
             };
             // this.ELEMENTDATA = null;
             this.snackbarService.openSnackBar(0, '', msgObj[this.selectedLang], 'error_notification', this.config.expiredTime);
             resolve(false);
           } else {
             resolve(true);
           }
         } catch (error) {
           resolve(true);
         }
       });
     }
     async checkInfoMember() {
       let formObj = this.productForm.getRawValue();
       if(this.limitSearchCitizenPros){
         let checkValidProcedure = await this.checkValidProcedure();
         if(!checkValidProcedure)
           return;
       }
       let demXT = 0;
       for (let i = 0; i < formObj.quantities.length; i++) {
         let checkEmptyInput = await this.checkValidAllInput(formObj.quantities[i].fullNameMember, formObj.quantities[i].identityMember, formObj.quantities[i].birthDayMember, formObj.quantities[i].cmndMember)
         if (checkEmptyInput) {
           let checkIndentity = await this.checkValidIndentity(formObj.quantities[i].identityMember + '', formObj.quantities[i].cmndMember + '');
           if (checkIndentity) {
            if(formObj.quantities[i].checkInfo != 1){
             let y = formObj.quantities[i].birthDayMember.getFullYear();
             let m = formObj.quantities[i].birthDayMember.getMonth() + 1;
             if (m < 10) {
               m = '0' + m;
             }
             let d = formObj.quantities[i].birthDayMember.getDate();
             if (d < 10) {
               d = '0' + d;
             }
             let searchString = '';
             let birthday = y + m + d;
             if(m =='01' && d == '01'){
               birthday = y;
             }
             let soDinhDanh = formObj.quantities[i].identityMember == '' ? formObj.quantities[i].cmndMember : formObj.quantities[i].identityMember;
             searchString += '&indentity-number=' + soDinhDanh;
             searchString += '&fullname=' + formObj.quantities[i].fullNameMember;
             searchString += '&birthday=' + birthday;
             let searchStringData = "/citizen/--info?"
             searchStringData += "agency-id=" + this.agencyId;
             searchStringData += "&subsystem-id=" + this.subsystemId;
             this.productForm.value
             searchStringData += "&get-origin-info=true";
             // const formObj = this.productForm.getRawValue();
             searchStringData += searchString;
             
             const userAgency = JSON.parse(localStorage.getItem('userAgency'));
             const log = {
               identityNumber: soDinhDanh,
               name: formObj.quantities[i].fullNameMember,
               birtDay: formObj.quantities[i].birthDayMember,
               status: true,
               function: 1,
               url: location.href,
               agency: {
                 id: userAgency?.id ? userAgency.id : this.config?.rootAgency?.id,
                 name: userAgency?.name ? userAgency.name : this.config?.rootAgency?.trans?.vi?.name,
                 code: userAgency?.code ? userAgency.code : this.config?.rootAgency?.code
               },
               ipAddress: this.isKTMCitizenStatistic ? localStorage.getItem('clientIP') : '',
               menuCode: '037',
               dossierId: this.dossierId,
               dossierCode: this.dossierCode
             };
             this.getCitizenInfoService.getCitizenIfo(soDinhDanh,formObj.quantities[i].fullNameMember,birthday,this.eformId, this.getOriginInfo, this.userName, this.searchProcedureId, this.dossierId, this.dossierCode).subscribe(
               async data => {
                   if (await this.checkInfoNull(data.originInfo)) {
                     const quantities = this.productForm.get('quantities') as FormArray;
 
                     for (let i = 0; i < quantities.length; i++) {
                       const quantityGroup = quantities.controls[i] as FormGroup; // Truy cập từng FormGroup trong FormArray
 
                       if(quantityGroup.get('cmndMember')?.value != ''){
                        if(quantityGroup.get('cmndMember')?.value === data.originInfo?.SoCMND){
                          quantityGroup.patchValue({
                            checkInfo: 1,
                            fullNameMember: data.originInfo?.HoVaTen?.Ten,
                            birthDayMember: data.originInfo?.NgayThangNamSinh?.NgayThangNam
                              ? this.formatBirthDayRespon(data.originInfo.NgayThangNamSinh.NgayThangNam)
                              : '',
                            identityMember: data.originInfo?.SoDinhDanh,
                            permanentAddressMember:
                              data.originInfo?.ThuongTru?.ChiTiet +
                              ', ' +
                              data.originInfo?.ThuongTru?.PhuongXa?.label +
                              ', ' +
                              data.originInfo?.ThuongTru?.QuanHuyen?.label +
                              ', ' +
                              data.originInfo?.ThuongTru?.TinhThanh?.label,
                            currentAddressMember:
                              data.originInfo?.NoiOHienTai?.ChiTiet +
                              ', ' +
                              data.originInfo?.NoiOHienTai?.PhuongXa?.label +
                              ', ' +
                              data.originInfo?.NoiOHienTai?.QuanHuyen?.label +
                              ', ' +
                              data.originInfo?.NoiOHienTai?.TinhThanh?.label,
                          });
                        }
                      }else{
                       if (quantityGroup.get('identityMember')?.value === data.originInfo?.SoDinhDanh) {
                         quantityGroup.patchValue({
                           checkInfo: 1,
                           fullNameMember: data.originInfo?.HoVaTen?.Ten,
                           birthDayMember: data.originInfo?.NgayThangNamSinh?.NgayThangNam
                             ? this.formatBirthDayRespon(data.originInfo.NgayThangNamSinh.NgayThangNam)
                             : '',
                           cmndMember: data.originInfo?.SoCMND,
                           permanentAddressMember:
                             data.originInfo?.ThuongTru?.ChiTiet +
                             ', ' +
                             data.originInfo?.ThuongTru?.PhuongXa?.label +
                             ', ' +
                             data.originInfo?.ThuongTru?.QuanHuyen?.label +
                             ', ' +
                             data.originInfo?.ThuongTru?.TinhThanh?.label,
                           currentAddressMember:
                             data.originInfo?.NoiOHienTai?.ChiTiet +
                             ', ' +
                             data.originInfo?.NoiOHienTai?.PhuongXa?.label +
                             ', ' +
                             data.originInfo?.NoiOHienTai?.QuanHuyen?.label +
                             ', ' +
                             data.originInfo?.NoiOHienTai?.TinhThanh?.label,
                         });
                       }
                      }
                    }
                     this.getCitizenInfoService.writeCheckCitizenLog(log).subscribe(rp => {});
                   }else{
                    const quantities = this.productForm.get('quantities') as FormArray;
                    const quantityGroup = quantities.controls[i] as FormGroup;
                    quantityGroup.patchValue({
                      checkInfo: 2
                    });
                    demXT++;
                    log.status = false;
                     if(!this.checkCitizen.offConfirmErr ){
                       this.confirmReportCheckCitizenDialog(log);
                     }else{
                       this.getCitizenInfoService.writeCheckCitizenLog(log).subscribe(rp => {});
                     }
                   }
               }, err => {
                 const msgObj = err.error.message;
                 this.snackbarService.openSnackBar(0, msgObj, '', 'error_notification', this.config.expiredTime);
                 log.status = false;
                 if(!this.checkCitizen.offConfirmErr ){
                   this.confirmReportCheckCitizenDialog(log);
                 }else{
                   this.getCitizenInfoService.writeCheckCitizenLog(log).subscribe(rp => {});
                 }
               });
            }
          }
         }
       }
      //  if(demXT == 0){
      //    const quantities = this.productForm.get('quantities') as FormArray;
      //    const quantityGroup = quantities.controls[0] as FormGroup;
      //    quantityGroup.patchValue({
      //      checkInfo: 0
      //    });
      //  }
     }
     formatBirthDayRespon(birthDay) { //yyyymmdd
       // let y = birthDay.slice(0, 4);
       // let m = birthDay.slice(4, 6);
       // let d = birthDay.slice(6, 8);
       // return d + '/' + m + '/' + y
       const year = parseInt(birthDay.substring(0, 4), 10);
       const month = parseInt(birthDay.substring(4, 6), 10) - 1; // Tháng bắt đầu từ 0
       const day = parseInt(birthDay.substring(6, 8), 10);
 
       return new Date(year, month, day);
     }
     checkValidIdentity(identityOwner) {
       return new Promise((resolve) => {
         try {
           if (identityOwner.length !== 12) {
             this.snackbarService.openSnackBar(0, '', 'Chưa đúng căn cước công dân!', 'error_notification', this.config.expiredTime);
             resolve(false);
           } else {
             resolve(true);
           }
         } catch (error) {
           resolve(true);
         }
       });
     }
   
     checkValidAllInput(fullNameMember, identityMember, birthDayMember,cmndMember) {
       return new Promise((resolve) => {
         try {
           if (fullNameMember == '' || birthDayMember == '' || (identityMember == '' && cmndMember == '')) {
             this.snackbarService.openSnackBar(0, '', 'Bạn cần nhập đầy đủ thông tin!', 'error_notification', this.config.expiredTime);
             resolve(false);
           } else {
             resolve(true);
           }
         } catch (error) {
           resolve(true);
         }
       })
     }
 
     checkInfoNull(originInfo){
       return new Promise((resolve) => {
         try {
           if (originInfo === null) {
             const msgObj = {
               vi: 'Thông tin nhập vào chưa đúng, vui lòng kiểm tra lại!',
               en: 'You need to enter all the information!'
             };
             // this.ELEMENTDATA = null;
             this.snackbarService.openSnackBar(0, '', msgObj[this.selectedLang], 'error_notification', this.config.expiredTime);
             resolve(false);
           } else {
             resolve(true);
           }
         } catch (error) {
           resolve(true);
         }
       })
     }
   confirmReportCheckCitizenDialog(log) {
       const dialogData = new ConfirmReportDialogModel('Phản hồi ý kiến', 'Thông tin hiện tại không tìm thấy, nếu bạn chắc chắn thông tin tìm kiếm là đúng vui lòng phản hồi cho chúng tôi! xin cám ơn');
       const dialogRef = this.dialog.open(ConfirmReportComponent, {
         minWidth: '600px',
         minHeight: '40vh',
         data: dialogData,
         disableClose: true,
         autoFocus: false
       });
       dialogRef.afterClosed().subscribe(async dialogResult => {
         const res = dialogResult;
         log.report = '';
         if (res.status === true) {
           log.report = res.data;
         }
         this.getCitizenInfoService.writeCheckCitizenLog(log).subscribe(data => {});
       });
     }
     userPermissions = ['oneGateResidentialInfo', 'viewDashboard']; // Giả định quyền user
 
   // Kiểm tra nếu user có quyền
   hasPermission(permission: string): boolean {
     return this.data.anyPermissions.includes(permission);
   }
 
   // Kiểm tra nếu user có bất kỳ quyền nào trong danh sách
   hasAnyPermission(): boolean {
     return this.data.anyPermissions.some(permission => this.userPermissions.includes(permission));
   }

}
