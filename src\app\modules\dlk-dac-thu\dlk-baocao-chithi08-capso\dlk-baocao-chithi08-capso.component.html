<h2>BÁO CÁO CHỈ THỊ 08 CẤP SỞ</h2>
<div class="prc_searchbar">
  <div fxLayout="row" fxLayout.xs="row" fxLayout.sm="row" class="formFieldOutline" fxLayoutAlign="space-between">
    <div appearance="outline"  fxFlex='grow'>
      <!-- <div fxLayout="row" fxLayout.xs="row" fxLayout.sm="row" class="formFieldOutline" fxLayoutAlign="space-between"> -->
        <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="row">
        <mat-form-field appearance="outline" fxFlex.gt-sm="20" fxFlex="20" fxFlex='grow'>
          <mat-label>Từ ngày</mat-label>
          <input matInput [matDatepicker]="pickerAcceptFrom"  name="startDate" [(ngModel)]="startDate">
          <mat-datepicker-toggle matSuffix [for]="pickerAcceptFrom" ></mat-datepicker-toggle>
          <mat-datepicker #pickerAcceptFrom></mat-datepicker>
        </mat-form-field>
        <div fxFlex='1'></div>
        <mat-form-field appearance="outline" fxFlex.gt-sm="20" fxFlex="20" fxFlex='grow'>
          <mat-label>Đến ngày</mat-label>
          <input matInput [matDatepicker]="pickerAcceptTo" name="endDate"  [(ngModel)]="endDate">
          <mat-datepicker-toggle matSuffix [for]="pickerAcceptTo"></mat-datepicker-toggle>
          <mat-datepicker #pickerAcceptTo></mat-datepicker>
        </mat-form-field>
        <div fxFlex='1'></div>
        <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
            <mat-label i18n>Cơ quan</mat-label>
            <mat-select name="lstCoQuan" msInfiniteScroll (selectionChange)="changeAgency($event)" [(ngModel)]="agencyId">
              <mat-option *ngFor="let item of listAgencyAccept" [value]="item.id" >{{item.name}}</mat-option>
            </mat-select>
        </mat-form-field>
      </div>
    </div>
  </div>

  <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="row" fxLayoutAlign="end" style="padding-bottom: 1em;">
    <button mat-flat-button fxFlex.lt-md="22" fxFlex.md="20" fxFlex.gt-md="12" fxFlex.gt-xs="49.5" fxFlex='grow' style="background-color: #ce7a58;color: white;"
      class="btn-search" type="submit" (click)="thongKe()">
      <mat-icon class="iconStatistical">bar_chart</mat-icon><span>Thống kê</span>
      <mat-progress-bar mode="indeterminate" *ngIf="waitingDownloadExcel"></mat-progress-bar>
    </button>
    <div fxFlex='1'></div>
    <button mat-flat-button fxFlex.lt-md="22" fxFlex.md="20" fxFlex.gt-md="12" fxFlex.gt-xs="49.5" fxFlex='grow'
      class="btn-download-excel"  (click)="exportToExcel()" [disabled]="waitingDownloadExcel" style="background-color: #38A938;color: white;">
      <mat-icon class="iconStatistical">cloud_download</mat-icon>
      <span style="padding-right: 2px;"> Xuất excel</span>
      <mat-progress-bar mode="indeterminate" *ngIf="waitingDownloadExcel"></mat-progress-bar>
    </button>
  </div>
</div>

<div fxLayout="row" fxLayoutAlign="center">
  <div class="frm_main" fxFlex="grow">
    <div class="logbookTbl">
      <div class="logbookOnlyTbl">
        <table class="table">
          <thead>
            <tr>
              <th rowspan="5">STT</th>
              <th rowspan="5">Tên lĩnh vực/thủ tục</th>
              <th rowspan="4">Mức độ thủ tục</th>
              <th colspan="11">Tiếp nhận hồ sơ TTHC</th>
              <th colspan="9">Trả kết quả giải quyết TTHC </th>
              <th colspan="9">Dịch vụ công trực tuyến (DVCTT)</th>
              <th colspan="3">TTHC cung cấp dịch vụ BCCI</th>
              <th colspan="6">Thanh toán trực tuyến</th>
            </tr>
            <tr>
              <th rowspan="3">Tổng số hồ sơ tiếp nhận trong tháng</th>
              <th rowspan="3">Trực tiếp</th>
              <th rowspan="3">BCCI</th>
              <th colspan="6">Trực tuyến</th>
              <th colspan="2" rowspan="2">Cập nhật lên iGate</th>
              <th rowspan="3">Tổng số</th>
              <th colspan="2" rowspan="2">Trả trực tiếp</th>
              <th colspan="2" rowspan="2">Trả qua dịch vụ BCCI</th>
              <th colspan="2" rowspan="2">Trả trực tuyến</th>
              <th rowspan="3">Cập nhật lên iGate</th>
              <th rowspan="3">Số hồ sơ TTHC sử dụng ký số trong giải quyết</th>
              <th rowspan="3">Tổng số TTHC của cơ quan, đơn vị</th>
              <th rowspan="3">Số DVCTT có phát sinh hồ sơ</th>
              <th rowspan="3">Tỷ lệ DVCTT có phát sinh hồ sơ</th>
              <th colspan="3" rowspan="2">Một phần</th>
              <th colspan="3" rowspan="2">Toàn trình</th>
              <th rowspan="3">Số TTHC cung cấp dịch vụ BCCI</th>
              <th colspan="2" rowspan="2">TTHC cung cấp dịch vụ BCCI có phát sinh hồ sơ</th>
              <th rowspan="3">Số TTHC cung cấp dịch vụ thanh toán trực tuyến</th>
              <th rowspan="3">Số hồ sơ TTHC phát sinh của các TTHC cung cấp dịch vụ thanh toán trực tuyến (bao gồm trực tiếp, trực tuyến, BCCI)</th>
              <th colspan="2" rowspan="2">Số TTHC cung cấp dịch vụ thanh toán trực tuyến có phát sinh hồ sơ</th>
              <th colspan="2" rowspan="2">Thực hiện thanh toán trực tuyến</th>
            </tr>
            <tr>
              <th colspan="2">Một phần</th>
              <th colspan="2">Toàn trình</th>
              <th colspan="2">Trực tuyến (một phần và toàn trình)</th>
            </tr>
            <tr>
              <th>Số hồ sơ</th>
              <th>Tỷ lệ %</th>
              <th>Số hồ sơ</th>
              <th>Tỷ lệ %</th>
              <th>Tổng số hồ sơ</th>
              <th>Tỷ lệ %</th>
              <th>Số hồ sơ tiếp nhận đã cập nhật lên iGate</th>
              <th>Tỷ lệ %</th>
              <th>Số hồ sơ</th>
              <th>Tỷ lệ %</th>
              <th>Số hồ sơ</th>
              <th>Tỷ lệ %</th>
              <th>Số hồ sơ</th>
              <th>Tỷ lệ %</th>
              <th>Số DVCTT một phần </th>
              <th>Số DVCTT một phần có phát sinh hồ sơ</th>
              <th>Tỷ lệ %</th>
              <th>Số DVCTT toàn trình</th>
              <th>Số DVCTT toàn trình có phát sinh hồ sơ</th>
              <th>Tỷ lệ %</th>
              <th>Số TTHC cung cấp dịch vụ BCCI có phát sinh hồ sơ</th>
              <th>Tỷ lệ %</th>
              <th>Số TTHC</th>
              <th>Tỷ lệ %</th>
              <th>Số hồ sơ TTHC đã giải quyết sử dụng dịch vụ thanh toán trực tuyến</th>
              <th>Tỷ lệ %</th>
            </tr>
            <tr class="cen">
              <th>(1)</th>
              <th>(2)</th>
              <th>(3)</th>
              <th>(4)</th>
              <th>(5)</th>
              <th>(6)</th>
              <th>(7)</th>
              <th>(8)</th>
              <th>(9)</th>
              <th>(10)</th>
              <th>(11)</th>
              <th>(12)</th>
              <th>(13)</th>
              <th>(14)</th>
              <th>(15)</th>
              <th>(16)</th>
              <th>(17)</th>
              <th>(18)</th>
              <th>(19)</th>
              <th>(20)</th>
              <th>(21)</th>
              <th>(22)</th>
              <th>(23)</th>
              <th>(24)</th>
              <th>(25)</th>
              <th>(26)</th>
              <th>(27)</th>
              <th>(28)</th>
              <th>(29)</th>
              <th>(30)</th>
              <th>(31)</th>
              <th>(32)</th>
              <th>(33)</th>
              <th>(34)</th>
              <th>(35)</th>
              <th>(36)</th>
              <th>(37)</th>
              <th>(38)</th>
              <th>(39)</th>
            </tr>
          </thead>
          <tbody>
            <ng-container *ngFor="let item of ListMain;let i = index ">
              <!-- Danh sách lĩnh vực -->
              <tr *ngIf="ListMain.length>0">
                <td>{{i+1}}</td>
                <td colspan="2"><a (click)="ViewDetail(i,item.show)">{{item.sector}}</a></td>
                <td *ngFor="let prop of listProp">
                  <ng-container *ngIf="prop.type">
                    {{item[prop.name + '_tky']}}
                  </ng-container>
                  <ng-container *ngIf="prop.fixValue">
                    {{ prop.fixValue }}
                  </ng-container>
                </td>
              </tr>

              <!-- Chi tiết thủ tục -->
              <ng-container *ngFor="let item1 of item.data;let j = index ">
                <tr *ngIf="item.show">
                  <td>{{colToLetter(j)}}</td>
                  <td>{{item1.procedureName}}</td>
                  <td>{{item1.MucDo}}</td>
                  <td *ngFor="let prop of listProp">
                    <ng-container *ngIf="prop.type">
                      {{item1[prop.name + '_tky']}}
                    </ng-container>
                    <ng-container *ngIf="prop.fixValue">
                      {{ prop.fixValue }}
                    </ng-container>
                  </td>
                </tr>
              </ng-container>
            </ng-container>
            <tr *ngIf="ListMain.length>0" class="sum">
              <td colspan="3"> TỔNG CỘNG</td>
              <td *ngFor="let prop of listProp">
                <ng-container *ngIf="prop.type">
                  <ng-container *ngIf="prop.type == 'hs'">
                    <a (click)="onClickTd(prop.name + '_tky', 0)">
                      {{ TongCong[prop.name + '_tky'] }}
                    </a>
                  </ng-container>
                  <ng-container *ngIf="prop.type == 'tt'">
                    <a (click)="onClickTd(prop.name + '_tky', 1)">
                      {{ TongCong[prop.name + '_tky'] }}
                    </a>
                  </ng-container>
                  <ng-container *ngIf="prop.type == 'tl'">
                    {{ TongCong[prop.name + '_tky'] }}
                  </ng-container>
                </ng-container>
                <ng-container *ngIf="prop.fixValue">
                  {{ prop.fixValue }}
                </ng-container>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</div>