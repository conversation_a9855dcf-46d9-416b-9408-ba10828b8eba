import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class EventSharedService {
  isReloadRingNotify: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);

  constructor() { }

  setReloadRingNotify(data: boolean) {
    this.isReloadRingNotify.next(data);
  }

  getReloadRingNotify() {
    return this.isReloadRingNotify.asObservable();
  }
}