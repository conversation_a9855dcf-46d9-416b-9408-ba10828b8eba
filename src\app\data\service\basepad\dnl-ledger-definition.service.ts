import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { ApiProviderService } from 'src/app/core/service/api-provider.service';
import { EnvService } from 'src/app/core/service/env.service';

@Injectable({
    providedIn: 'root'
})
export class DNLLedgerDefinitionService {

    constructor(
        private http: HttpClient,
        private envService: EnvService,
        private apiProviderService: ApiProviderService,
    ) { }

    private getCountLedgersByDefinitionIdUrl(id: string) {
        return this.apiProviderService.getUrl('digo', 'basepad') + `/dnl/ledger-definition/${id}/--count-ledger`;
    }
    private searchUrls = this.apiProviderService.getUrl('digo', 'basepad') + '/dnl/ledger-definition/--search';
    private postUrls = this.apiProviderService.getUrl('digo', 'basepad') + '/dnl/ledger-definition';
    private getDetailsUrls(id: string){
        return this.apiProviderService.getUrl('digo', 'basepad') + `/dnl/ledger-definition/${id}`;
    }
    private getUpdateUrls(id: string){
        return this.apiProviderService.getUrl('digo', 'basepad') + `/dnl/ledger-definition/${id}`;
    }

    private getArchiveUrls(id: string){
        return this.apiProviderService.getUrl('digo', 'basepad') + `/dnl/ledger-definition/${id}/--archive`;
    }

    search(query): Observable<any> {
        console.log(`LedgerDefinitionService: search: ${query}`);
        const endpoint = this.searchUrls + (!!query ? '?' + query : '');
        return this.http.get(endpoint);
    }
 
    post(body: any): Observable<any>{
        let headers = new HttpHeaders();
        headers = headers.set('Content-Type', 'application/json');
        return this.http.post<any>(this.postUrls, body, { headers });
    }

    update(id: string, body: any): Observable<any>{
        let headers = new HttpHeaders();
        headers = headers.set('Content-Type', 'application/json');
        return this.http.put<any>(this.getUpdateUrls(id), body, { headers });
    }

    details(id: string): Observable<any>{
        return this.http.get(this.getDetailsUrls(id));
    }

    archive(id: string): Observable<any>{
        let headers = new HttpHeaders();
        headers = headers.set('Content-Type', 'application/json');
        return this.http.post<any>(this.getArchiveUrls(id), null, { headers });
    }

    countLedgersByDefinitionId(id: string): Observable<any>{
        console.log(`LedgerDefinitionService: countLedgersByDefinitionId: ${id}`);
        return this.http.get(this.getCountLedgersByDefinitionIdUrl(id));
    }
}
