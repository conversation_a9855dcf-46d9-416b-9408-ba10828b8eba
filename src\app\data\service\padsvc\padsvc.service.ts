import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { ApiProviderService } from 'src/app/core/service/api-provider.service';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class PadsvcService {
  constructor(
    private http: HttpClient,
    private apiProviderService: ApiProviderService
  ) {}

  private getProcedureURL = this.apiProviderService.getUrl('digo', 'basepad');
  private getFileURL = this.apiProviderService.getUrl('digo', 'fileman');
  private tagPath = this.apiProviderService.getUrl('digo', 'basecat') + '/tag/';
  private basedata = this.apiProviderService.getUrl('digo', 'basedata');
 // private adapterPath = this.apiProviderService.getUrl('digo', 'integration');
  private adapterPath = this.apiProviderService.getUrl('digo', 'adapter');
 // private adapter = this.apiProviderService.getUrl('digo', 'integration');
  private adapter = this.apiProviderService.getUrl('digo', 'adapter');
  private padmanPath = this.apiProviderService.getUrl('digo', 'padman');

  deleteDossier(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http
      .delete(this.padmanPath + '/dossier/' + id + '/?is-canceled=0', {
        headers,
      })
      .pipe();
  }

  getListForm(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http
      .get(this.getProcedureURL + '/form/' + searchString, { headers })
      .pipe();
  }

  postForm(requestBody) {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.post<any>(this.getProcedureURL + '/form', requestBody, {
      headers,
    });
  }

  downloadFile(id, uuid?): Observable<any> {
    return this.http
      .get(this.getFileURL + '/file/' + id + '?uuid=' + uuid, {
        responseType: 'blob' as 'json',
      })
      .pipe();
  }

  getListTagByCategoryId(
    id: string,
    page: number,
    size: number,
    sort: string
  ): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    // tslint:disable-next-line: max-line-length
    // return this.http.get(this.tagPath + '--by-category-id?category-id=' + id + '&page=' + page + '&size=' + size + '&sort=' + sort, { headers }).pipe();
    switch (localStorage.getItem('isLoggedIn')) {
      case 'false':
        const token = localStorage.getItem('OAuth2TOKEN');
        headers = headers.append('Authorization', 'Bearer ' + token);
        // tslint:disable-next-line:max-line-length
        return this.http
          .get(
            this.tagPath +
              '--by-category-id?category-id=' +
              id +
              '&page=' +
              page +
              '&size=' +
              size +
              '&sort=' +
              sort,
            { headers }
          )
          .pipe();
      case 'true':
        // tslint:disable-next-line:max-line-length
        return this.http
          .get(
            this.tagPath +
              '--by-category-id?category-id=' +
              id +
              '&page=' +
              page +
              '&size=' +
              size +
              '&sort=' +
              sort,
            { headers }
          )
          .pipe();
    }
  }

  getListTagByCategoryIdKeyword(
    id: string,
    page: number,
    size: number,
    sort: string,
    keyword: string
  ): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    // tslint:disable-next-line: max-line-length
    return this.http
      .get(
        this.tagPath +
          '--by-category-id?category-id=' +
          id +
          '&page=' +
          page +
          '&keyword=' +
          keyword +
          '&size=' +
          size +
          '&sort=' +
          sort,
        { headers }
      )
      .pipe();
  }

  getTagByCategoryIdCode(id: string, code: string): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    // tslint:disable-next-line: max-line-length
    return this.http
      .get(
        this.tagPath +
          '--fully-by-code-and-category?category-id=' +
          id +
          '&code=' +
          code,
        { headers }
      )
      .pipe();
  }

  postDossierPayment(dataPost): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    return this.http.post<any>(this.padmanPath + '/dossier-payment', dataPost, {
      headers,
    });
  }

  getDossierPayment(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    return this.http.get<any>(
      this.padmanPath + '/dossier-payment' + searchString,
      { headers }
    );
  }

  getDossierPaymentDetail(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    return this.http.get<any>(this.padmanPath + '/dossier-payment/' + id, {
      headers,
    });
  }

  postVNPTPayInit(): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    return this.http.post<any>(this.adapter + '/vnpt-pay/--init', { headers });
  }

  postCreateVNPTPayInit(data, agencyId, subsystemId): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    // tslint:disable-next-line:max-line-length
    return this.http.post<any>(
      this.adapter +
        '/vnpt-pay/get-init' +
        '?agency-id=' +
        agencyId +
        '&subsystem-id=' +
        subsystemId,
      data,
      { headers }
    );
  }

  postCheckVNPTPay(data, token, url): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    return this.http.post<any>(
      this.adapter +
        '/vnpt-pay/get-init' +
        '?url=' +
        url +
        '/query_transaction',
      data,
      { headers }
    );
  }

  postPaymentPlatformInit(requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    return this.http.post<any>(
      this.adapter + '/payment-platform/--init',
      requestBody,
      { headers }
    );
  }
  getPlaceAddress(id: string): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.basedata + '/place/' + id + '/--address', {
      headers,
    });
  }

  getListPlace(nationId, parentId, parentTypeId): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    let param = '?nation-id=' + nationId;
    param += parentId ? '&parent-id=' + parentId : '';
    param += '&parent-type-id=' + parentTypeId;
    return this.http.get(this.basedata + '/place/--search' + param, {
      headers,
    });
  }

  getPrice(search, requestBody) {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.post<any>(
      this.adapterPath + '/vnpost/--get-price' + search,
      requestBody,
      { headers }
    );
  }

  sendVNPostOverLgspHcm(search, requestBody) {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.post<any>(
      this.adapterPath + '/lgspHcmVnpost/getOrder' + search,
      requestBody,
      { headers }
    );
    // return this.http.post<any>('http://localhost:8080' + '/vnpost/--post-order' + search, requestBody, { headers });
  }

  getPriceOverLgspHcm(search, requestBody) {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.post<any>(
      this.adapterPath + '/lgspHcmVnpost/getPrice' + search,
      requestBody,
      { headers }
    );
  }

  sendVNPost(search, requestBody) {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.post<any>(
      this.adapterPath + '/vnpost/--post-order' + search,
      requestBody,
      { headers }
    );
    // return this.http.post<any>('http://localhost:8080' + '/vnpost/--post-order' + search, requestBody, { headers });
  }

  sendKonTumVNPost(search, requestBody) {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.post<any>(
      this.adapterPath + '/KonTumVnpost/--post-order' + search,
      requestBody,
      { headers }
    );
    //return this.http.post<any>('http://localhost:8080' + '/KonTumVnpost/--post-order' + search, requestBody, { headers });
  }

  getMappingId(search): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http
      .get(this.adapterPath + '/mapping-data' + search, { headers })
      .pipe();
  }

  getDetailEnterprise(search, requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.post<any>(
      this.adapterPath + '/dkdn/--get-detail-enterprise' + search,
      requestBody,
      { headers }
    );
  }

  postOcr(requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    return this.http.post<any>(
      this.adapter + '/ekyc/--translate/',
      requestBody,
      { headers }
    );
  }

  sendVNPostStatus(search, requestBody) {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.put<any>(this.padmanPath + search, requestBody, {
      headers,
    });
  }

  getAcountPayment(agencyId, subsystemId, procedureProcessId): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    // tslint:disable-next-line: max-line-length
    return this.http
      .get(
        this.adapterPath +
          '/payment-platform/--get-beneficiary-account?agency-id=' +
          agencyId +
          '&subsystem-id=' +
          subsystemId +
          '&procedure-process-id=' +
          procedureProcessId,
        { headers }
      )
      .pipe();
  }

  postPaymentPlatformLGSPHCMInit(requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    return this.http.post<any>(
      this.adapter + '/payment-platform-hcm/--payGate',
      requestBody,
      { headers }
    );
  }

  postPaymentPlatformLGSPHCMReceive(requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    return this.http.post<any>(
      this.adapter + '/payment-platform-hcm/--payReceive',
      requestBody,
      { headers }
    );
  }

  postPaymentPlatformLGSPHCMOrderInfo(requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    return this.http.post<any>(
      this.adapter + '/payment-platform-hcm/--getOrderInfo',
      requestBody,
      { headers }
    );
  }

  getAgencyPaymentPlatformBdg(
    agencyId,
    subsystemId,
    configId,
    procedureProcessId?
  ): Observable<any> {
    let headers = new HttpHeaders();
    const dd = procedureProcessId
      ? '&procedure-process-id=' + procedureProcessId
      : '';
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    // tslint:disable-next-line: max-line-length
    return this.http
      .get(
        this.adapterPath +
          '/payment-platform/--get-agency-beneficiary-account-bdg?agency-id=' +
          agencyId +
          '&subsystem-id=' +
          subsystemId +
          '&config-id=' +
          configId +
          dd,
        { headers }
      )
      .pipe();
  }

  getAgencyPaymentVNPTPayBdg(agencyId, subsystemId, configId): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    // tslint:disable-next-line: max-line-length
    return this.http
      .get(
        this.adapterPath +
          '/vnpt-pay/--get-agency-beneficiary-account-bdg?agency-id=' +
          agencyId +
          '&subsystem-id=' +
          subsystemId +
          '&config-id=' +
          configId,
        { headers }
      )
      .pipe();
  }

  postPaymentPlatformInitBdg(requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    return this.http.post<any>(
      this.adapter + '/payment-platform/--init-bdg',
      requestBody,
      { headers }
    );
  }

  postCreateVNPTPayInitBdg(
    data,
    agencyId,
    subsystemId,
    configId
  ): Observable<any> {
    let headers = new HttpHeaders();
    const configid = configId ? '&config-id=' + configId : '';
    headers = headers.set('Content-Type', 'application/json');
    // tslint:disable-next-line:max-line-length
    return this.http.post<any>(
      this.adapter +
        '/vnpt-pay/get-init-bdg' +
        '?agency-id=' +
        agencyId +
        '&subsystem-id=' +
        subsystemId +
        configid,
      data,
      { headers }
    );
  }

  getPriceOverBdg(search, requestBody) {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.post<any>(
      this.adapterPath + '/vnpostBdg/--get-price' + search,
      requestBody,
      { headers }
    );
    //return this.http.post<any>('http://localhost:8088' + '/vnpostBdg/--get-price' + search, requestBody, { headers });
  }

  sendVNPostOverBdg(search, requestBody) {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.post<any>(
      this.adapterPath + '/vnpostBdg/--post-order' + search,
      requestBody,
      { headers }
    );
    //return this.http.post<any>('http://localhost:8088' + '/vnpostBdg/--post-order' + search, requestBody, { headers });
  }

  sendFpt1GateExpertise(requestBody) {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.post<any>(
      this.adapterPath + '/bdg-connect-log/--post-fpt1gate-expertise',
      requestBody,
      { headers }
    );
  }
}
