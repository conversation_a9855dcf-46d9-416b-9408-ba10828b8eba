import { Component, OnInit, Inject } from '@angular/core';
import { MatDialog, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { KeycloakService } from 'keycloak-angular';
import { DossierService } from 'src/app/data/service/dossier/dossier.service';
import { UserService } from 'src/app/data/service/user.service';
import * as ClassicEditor from '@ckeditor/ckeditor5-build-classic/build/ckeditor';
import '@ckeditor/ckeditor5-build-classic/build/translations/vi';
import { EnvService } from 'src/app/core/service/env.service';
import { SnackbarService } from 'src/app/data/service/snackbar/snackbar.service';
import { AddApologyTextComponent, ConfirmAddApologyTextComponent } from '../../pages/search/dialogs/add-apology-text/add-apology-text.component';
import { ConfirmSignApologyTextComponent, SignApologyTextComponent } from '../../pages/search/dialogs/sign-apology-text/sign-apology-text.component';
import { FormControl, FormGroup } from '@angular/forms';
import { ProcedureService } from 'src/app/data/service/procedure/procedure.service';
import { DeploymentService } from 'src/app/data/service/deployment.service';
import * as tUtils from 'src/app/data/service/thoai.service';
import { NotificationService } from 'src/app/data/service/notification.service';
import { DatePipe } from '@angular/common';
import { HomeService } from 'src/app/data/service/home/<USER>';
import { ProcessService } from 'src/app/data/service/process/process.service';
import { take } from 'rxjs/operators';
import {NotifyQNIService} from "shared/components/check-send-notify-qni/check-send-notify-qni.service";
import { PadmanService } from 'src/app/data/service/svc-padman/padman.service';
import {LogmanService} from 'data/service/logman/logman.service';
import { AgencyService } from 'src/app/data/service/basedata/agency.service';
import {AdapterService} from 'data/service/adapter/adapter.service';
import {TrinamService} from 'modules/hbh/trinam.service';

@Component({
  selector: 'app-suspend',
  templateUrl: './suspend.component.html',
  styleUrls: ['./suspend.component.scss']
})
export class SuspendComponent implements OnInit {

  config = this.envService.getConfig();
  env = this.deploymentService.getAppDeployment()?.env;
  selectedLang = localStorage.getItem('language') || 'vi';
  selectedLangId = Number(localStorage.getItem('languageId')) || 228;
  dossierId: string;
  commentContent = '';
  userName: string;
  fullname: string;
  accountId: string;
  userId: string;
  dossierCode: string;
  officers = [];
  agencyId: any;
  getApprovalAgency = '';
  isCKMaxlenght = false;
  extend = false;
  public Editor = ClassicEditor;
  commentConfig = {
    placeholder: 'Nhập nội dung...',
    language: 'vi',
    removePlugins: ['MediaEmbed', 'ImageUpload'],
    toolbarLocation: 'bottom'
  };
  dossierTaskStatus = { id: '', name: []};
  dossierMenuTaskRemind = { id: '', name: [] };
  dossierDetail: any;
  updateForm = new FormGroup({
    numberPauseDay: new FormControl(1),
    approvalAgencyId: new FormControl(''),
  });
  textPhone = '';
  textEmail = '';
  smsEmailContent = '';
  smsEmailMaxLength = null;
  willSend = false;
  willSendEmail = false;
  willSendSMS = false;
  listEmail = [];
  listPhone = [];
  processDetail: any;
  checkNumberPauses = false;
  // ================================================= Upload file
  acceptFileExtension = this.config.acceptFileExtension;
  acceptFileType = this.config.acceptFileType;
  maxFileSize = this.env?.maxFileSize || this.config.maxFileSize;
  uploaded: boolean;
  blankVal: any;
  uploadedImage = [];
  files = [];
  urls = [];
  fileNames = [];
  fileNamesFull = [];
  listProcedureProcess = [];
  requireAttachmentWhenPause = false;

  //Thu tuc co cau hinh dong bo BXD-LGSP Tan Tan
  isSyncDBNLGSPTanDanBXD = 0;
  statusName = "";

  currentTask: any;
  notifyQNI = this.deploymentService.getAppDeployment()?.notifyqni?.enable;

  totalCost = '';

  ckeditorMaxLengthNotification = "";
  ckeditorMaxLength = this.deploymentService.env.OS_HCM.ckeditorMaxLength;
  approvalAgency = [];
  visibleApprovalAgency = this.deploymentService.env.visibleApprovalAgency;
  isBoTNMTQNM = this.env?.OS_QNM?.boTNMT === true ? this.env?.OS_QNM?.boTNMT : false;
  isBoTNMTHCM = this.deploymentService.env.OS_HCM.maTTBoTNMT;
  isBoTNMTGLI = this.deploymentService.env.OS_GLI.boTNMT;
  hideNumberPauseDaySuspend = this.deploymentService.env.OS_HCM.hideNumberPauseDaySuspend ? this.deploymentService.env.OS_HCM.hideNumberPauseDaySuspend : false;
  hideNumberPauseDaySuspendWithAgency = this.deploymentService.env.OS_HCM.hideNumberPauseDaySuspendWithAgency ? this.deploymentService.env.OS_HCM.hideNumberPauseDaySuspendWithAgency : [];
  checkHideNumberPauseDaySuspend = false;
  defaultNumberPauseDay = this.deploymentService.env.OS_HCM.defaultNumberPauseDay ? this.deploymentService.env.OS_HCM.defaultNumberPauseDay : 100;
  
  // IGATESUPP-44607
  allowNextStepWaitingForApproval = this.deploymentService.env.OS_HCM.allowNextStepWaitingForApproval ? this.deploymentService.env.OS_HCM.allowNextStepWaitingForApproval : false;
  enableApprovePauseCheckBox = false;
  dossierTaskStatusWaitingForApproval = { id: '', name: [] };
  dossierMenuTaskRemindWaitingForApproval = { id: '', name: [] };
  setOnlyApprovalAgency = this.deploymentService.env?.OS_QNI?.setOnlyApprovalAgency;
  isSmsQNM = this.env?.OS_QNM?.isSmsQNM ? true : false;
  isHasCodeLDXH = false;
  disableButton = false;
  constructor(
    private dialog: MatDialog,
    private userService: UserService,
    private envService: EnvService,
    private notiService: NotificationService,
    private deploymentService: DeploymentService,
    private keycloakService: KeycloakService,
    public dialogRef: MatDialogRef<SuspendComponent>,
    @Inject(MAT_DIALOG_DATA) public data: SuspendModel,
    private snackbarService: SnackbarService,
    private dossierService: DossierService,
    private procedureService: ProcedureService,
    private datePipe: DatePipe,
    private processService: ProcessService,
    private homeService : HomeService,
    private notifyQNIService: NotifyQNIService,
    private padmanService: PadmanService,
    private logmanService: LogmanService,
    private agencyService: AgencyService,
    private adapterService: AdapterService,
    private trinameService: TrinamService,
  ) {
    this.dossierId = data.dossierId;
    this.dossierCode = data.dossierCode;
  }

  oldstatus = '';

  ngOnInit(): void {
    this.getDetailDossier();
    this.getUserAccount();
    this.checkAgencyHideNumberPauseDaySuspend();
    this.getDossierTaskStatus();
    this.getDossierMenuTaskRemind();
    this.getConfigCloud();
    if (this.selectedLang === 'vi') {
      this.ckeditorMaxLengthNotification = "Nội dung không quá 500 ký tự!";
      if (this.ckeditorMaxLength > 0)
        this.ckeditorMaxLengthNotification = "Nội dung không quá "+ this.ckeditorMaxLength + " ký tự!";
    } else if (this.selectedLang === 'en') {
      this.ckeditorMaxLengthNotification = "Content must not exceed 500 characters!";
      if (this.ckeditorMaxLength > 0)
        this.ckeditorMaxLengthNotification = "Content must not exceed "+ this.ckeditorMaxLength + " characters!";
    }
  }

  getUserAccount() {
    this.keycloakService.loadUserProfile().then(user => {
      // tslint:disable-next-line: no-string-literal
      this.userId = user['attributes'].user_id[0];
      this.accountId = user['attributes'].account_id[0];
      this.userName =  user.username;
      this.userService.getUserInfo(this.userId).subscribe(data => {
        this.fullname = data.fullname;
      });
    });
  }

  // IGATESUPP-51500 - Đánh giá lại chức năng phê duyệt hồ sơ - 19.06.2023
  postOnlyApprovalAgencyOfDossier(code) {

    const formObj = this.updateForm.getRawValue();
    if (!!formObj.approvalAgencyId && formObj.approvalAgencyId != '') {
      this.getApprovalAgency = formObj.approvalAgencyId;
    }else{
      return;
    }

    const body = {
      id: this.getApprovalAgency
    };
    const requestBody = JSON.stringify(body, null, 2);
    this.dossierService.postOnlyApprovalAgencyOfDossier(requestBody, code).toPromise();
  }
  
  getDossierTaskStatus() {
    const tagId = this.env?.enableApprovalOfLeadership == 1 ? this.deploymentService.env.dossierTaskStatus.waitingForApprovalToPause.id : this.deploymentService.env.dossierTaskStatus.pending.id;
    this.dossierService.getAgencyTag(tagId).subscribe(rs => {
      this.dossierTaskStatus.id = rs.id;
      this.dossierTaskStatus.name = rs.trans;
    }, err => {
      console.log(err);
    });
  }
  getDossierMenuTaskRemind() {
    const tagId = this.env?.enableApprovalOfLeadership == 1 ? this.deploymentService.env.dossierMenuTaskRemind.waitingForApprovalToPause.id : this.deploymentService.env.dossierMenuTaskRemind.pending.id;
    this.dossierService.getAgencyTag(tagId).subscribe(rs => {
      this.dossierMenuTaskRemind.id = rs.id;
      this.dossierMenuTaskRemind.name = rs.trans;
      this.statusName = rs?.trans.filter(t => t.languageId === Number(this.selectedLangId))?.name;
    }, err => {
      console.log(err);
    });
  }

  async getDetailDossier(){
    await this.getDossierFeeNotify();
    this.dossierService.getDossierDetail(this.dossierId).subscribe(data => {
      if (data.acceptedDate !== undefined && data.appointmentDate !== undefined) {
        if (data.countExtendTime && data.countExtendTime > 0){
          this.extend = true;
        }
      }
      this.dossierDetail = data;
      this.getProcedureDetail(data?.procedure?.id);
      this.agencyId = data?.agency?.id;
      if (!!data.currentTask && data.currentTask.length > 0){
        this.currentTask = data.currentTask[0];
        this.getApprovaledAgency(this.currentTask.candidateGroup[0]);
      }
      if (this.dossierDetail.dossierTaskStatus === undefined && this.dossierDetail.dossierTaskStatus === null) {
        // this.putDossierStatus();
      }
      else{
        this.oldstatus = this.dossierDetail.dossierTaskStatus.name;
      }
      // tslint:disable-next-line:max-line-length
      if (data.applicant.data.phoneNumber !== null && data.applicant.data.phoneNumber !== '' && data.applicant.data.phoneNumber !== undefined) {
        this.textPhone = data.applicant.data.phoneNumber;
        this.listPhone.push({
          number: data.applicant.data.phoneNumber,
          nextFlow: ''
        });
      }
      if ( data.applicant.data.email !== null && data.applicant.data.email !== '' && data.applicant.data.email !== undefined) {
        this.textEmail = data.applicant.data.email;
        this.listEmail.push({
          email: data.applicant.data.email,
          nextFlow: ''
        });
      }
      setTimeout(() => {
        if(!!this.notifyQNI){
          this.notifyQNIService.checkSendSubject.next(
            {
              id: data?.procedureProcessDefinition?.id,
              phone: data?.applicant?.data?.phoneNumber,
              email: data?.applicant?.data?.email,
              currentTask: this.currentTask,
              contentParams: {
                parameters: {
                  sector: !!data?.procedure?.sector?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name ? data?.procedure?.sector?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name : '',
                  procedure: !!data?.procedure?.translate?.find(agency => agency.languageId === Number(this.selectedLangId))?.name ? data?.procedure?.translate?.find(agency => agency.languageId === Number(this.selectedLangId))?.name : '',
                  agency: !!data?.agency?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name ? data?.agency?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name : '',
                  agencyId: data?.agency?.id,
                  applicantFullname: data?.applicant?.data?.fullname,
                  officerFullname: '',
                  subsystem: !!this.env?.subsystem?.name ? this.env?.subsystem?.name : "Hệ thống Một cửa điện tử",
                  dossierCode: data?.code,
                  nextTask: !!this.env?.notify?.suspendDossier?.nextTask ? this.env?.notify?.suspendDossier?.nextTask : 'Tạm dừng xử lý',
                  dossierSearchPadsvcUrl: (!!this.env?.domain?.padsvc ? this.env?.domain?.padsvc : "http://padsvctest.vncitizens.vn/vi/") + "dossier/public",
                  dossierDetailPadsvcUrl: (!!this.env?.domain?.padsvc ? this.env?.domain?.padsvc : "http://padsvctest.vncitizens.vn/vi/") + "dossier/detail/" + this.dossierId,
                  dossierSearchUrl: (!!this.env?.domain?.onegate ? this.env?.domain?.onegate : "http://padsvctest.vncitizens.vn/vi/") + "dossier/search/",
                  dossierDetailUrl: (!!this.env?.domain?.onegate ? this.env?.domain?.onegate : "http://padsvctest.vncitizens.vn/vi/") + "dossier/search/" + this.dossierId,
                  returnMethod: !!data?.dossierReceivingKind?.name?.find(recieve => recieve.languageId === Number(this.selectedLangId))?.name ? data?.dossierReceivingKind?.name?.find(recieve => recieve.languageId === Number(this.selectedLangId))?.name : '',
                  receivingDate: '',
                  appointmentDate: '',
                  dossierFee: this.totalCost,
                  reason: ''
                }
              }
            }
          );
        } else if(this.isSmsQNM) {
          const agencies = this.agencyService.getAgencies();
          const extend = {
            dossier:{
              id: this.dossierId,
              code: this.dossierCode,
            },
            agencies: agencies
          };
          this.notiService.checkSendSubject.next(
            {
              id: data?.procedureProcessDefinition?.id,
              phone: data?.applicant?.data?.phoneNumber,
              email: data?.applicant?.data?.email,
              currentTask: this.currentTask,
              contentParams: {
                parameters: {
                  sector: !!data?.procedure?.sector?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name ? data?.procedure?.sector?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name : '',
                  procedure: !!data?.procedure?.translate?.find(agency => agency.languageId === Number(this.selectedLangId))?.name ? data?.procedure?.translate?.find(agency => agency.languageId === Number(this.selectedLangId))?.name : '',
                  agency: !!data?.agency?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name ? data?.agency?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name : '',
                  agencyId: data?.agency?.id,
                  applicantFullname: data?.applicant?.data?.fullname,
                  officerFullname: '',
                  subsystem: !!this.env?.subsystem?.name ? this.env?.subsystem?.name : "Hệ thống Một cửa điện tử",
                  dossierCode: data?.nationCode ? data?.nationCode : data?.code,
                  nextTask: !!this.env?.notify?.suspendDossier?.nextTask ? this.env?.notify?.suspendDossier?.nextTask : 'Tạm dừng xử lý',
                  dossierSearchPadsvcUrl: (!!this.env?.domain?.padsvc ? this.env?.domain?.padsvc : "http://padsvctest.vncitizens.vn/vi/") + "dossier/public",
                  dossierDetailPadsvcUrl: (!!this.env?.domain?.padsvc ? this.env?.domain?.padsvc : "http://padsvctest.vncitizens.vn/vi/") + "dossier/detail/" + this.dossierId,
                  dossierSearchUrl: (!!this.env?.domain?.onegate ? this.env?.domain?.onegate : "http://padsvctest.vncitizens.vn/vi/") + "dossier/search/",
                  dossierDetailUrl: (!!this.env?.domain?.onegate ? this.env?.domain?.onegate : "http://padsvctest.vncitizens.vn/vi/") + "dossier/search/" + this.dossierId,
                  returnMethod: !!data?.dossierReceivingKind?.name?.find(recieve => recieve.languageId === Number(this.selectedLangId))?.name ? data?.dossierReceivingKind?.name?.find(recieve => recieve.languageId === Number(this.selectedLangId))?.name : '',
                  receivingDate: '',
                  appointmentDate: '',
                  dossierFee: this.totalCost,
                  reason: '',
                  extend: extend
                }
              }
            }
          );
        } else {
          this.notiService.checkSendSubject.next(
            {
              id: data?.procedureProcessDefinition?.id,
              phone: data?.applicant?.data?.phoneNumber,
              email: data?.applicant?.data?.email,
              currentTask: this.currentTask,
              contentParams: {
                parameters: {
                  sector: !!data?.procedure?.sector?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name ? data?.procedure?.sector?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name : '',
                  procedure: !!data?.procedure?.translate?.find(agency => agency.languageId === Number(this.selectedLangId))?.name ? data?.procedure?.translate?.find(agency => agency.languageId === Number(this.selectedLangId))?.name : '',
                  agency: !!data?.agency?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name ? data?.agency?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name : '',
                  agencyId: data?.agency?.id,
                  applicantFullname: data?.applicant?.data?.fullname,
                  officerFullname: '',
                  subsystem: !!this.env?.subsystem?.name ? this.env?.subsystem?.name : "Hệ thống Một cửa điện tử",
                  dossierCode: data?.nationCode ? data?.nationCode : data?.code,
                  nextTask: !!this.env?.notify?.suspendDossier?.nextTask ? this.env?.notify?.suspendDossier?.nextTask : 'Tạm dừng xử lý',
                  // IGATESUPP-63184
                  nextStatus: !!this.env?.notify?.suspendDossier?.nextStatus ? this.env?.notify?.suspendDossier?.nextStatus : (data?.dossierTaskStatus?.name ?? ''), // cho bo sung, trang thai hien tai cua ho so
                  dossierStatus: !!this.env?.notify?.suspendDossier?.dossierStatus ? this.env?.notify?.suspendDossier?.dossierStatus : (data?.dossierStatus?.name?.find(t => t.languageId === Number(this.selectedLangId))?.name ?? ''), // cho bo sung, trang thai hien tai cua ho so
                  dossierSearchPadsvcUrl: (!!this.env?.domain?.padsvc ? this.env?.domain?.padsvc : "http://padsvctest.vncitizens.vn/vi/") + "dossier/public",
                  dossierDetailPadsvcUrl: (!!this.env?.domain?.padsvc ? this.env?.domain?.padsvc : "http://padsvctest.vncitizens.vn/vi/") + "dossier/detail/" + this.dossierId,
                  dossierSearchUrl: (!!this.env?.domain?.onegate ? this.env?.domain?.onegate : "http://padsvctest.vncitizens.vn/vi/") + "dossier/search/",
                  dossierDetailUrl: (!!this.env?.domain?.onegate ? this.env?.domain?.onegate : "http://padsvctest.vncitizens.vn/vi/") + "dossier/search/" + this.dossierId,
                  returnMethod: !!data?.dossierReceivingKind?.name?.find(recieve => recieve.languageId === Number(this.selectedLangId))?.name ? data?.dossierReceivingKind?.name?.find(recieve => recieve.languageId === Number(this.selectedLangId))?.name : '',
                  receivingDate: '',
                  appointmentDate: '',
                  dossierFee: this.totalCost,
                  reason: ''
                }
              }
            }
          );
        }
      }, this.config?.reloadTimeout);
      this.getProcedureProcessDetail(data.procedureProcessDefinition.id);
    });
  }

  getDossierFeeNotify() {
    return new Promise<void>(resolve => {
      this.padmanService.getDossierFee(this.dossierId).toPromise().then(res => {
        if(res && res.length > 0)
        {
          let totalCostValue = 0;
          let totalPaidValue = 0;
          res.forEach(element => {
            totalCostValue += element.quantity * element.amount;
            totalPaidValue += element.paid;
          });
          const monetaryUnit = res[0]?.monetaryUnit ? res[0].monetaryUnit : 'VNĐ';
          let total = totalCostValue - totalPaidValue;
          this.totalCost = total > 0 ? Number((total).toFixed(1)).toLocaleString() + ' ' + monetaryUnit : '';
        }
        resolve();
      }).catch(err => {
        resolve();
      });
    });
  }

  getProcedureDetail(procedureId) {
    return new Promise<void>(resolve => {
      this.procedureService.getProcedureDetail(procedureId).subscribe(async data => {
        this.isSyncDBNLGSPTanDanBXD = data?.isSyncDBNLGSPTanDanBXD ? Number(data?.isSyncDBNLGSPTanDanBXD) : 0;
        this.enableApprovePauseCheckBox = data?.extendHCM?.enableApprovePauseCheckBox ? data?.extendHCM?.enableApprovePauseCheckBox : false;
        this.isHasCodeLDXH = !!data.btxhcode;
        return resolve;
      }, err => {
        console.log(err);
        return resolve;
      });
    });
  }
  getProcedureProcessDetail(processId) {
    this.procedureService.getProcedureProcessDetail(processId).subscribe(data => {
      this.processDetail = data;
      this.checkedNumberPauses();
    });
  }
  checkedNumberPauses(){
    if(this.processDetail.numberPauses === -1 || this.dossierDetail.countPauses < this.processDetail.numberPauses){
      this.checkNumberPauses = true;
    }
  }

  putDossierStatus() {
    const requestBodyObj = [
      {
        id: this.dossierId,
        dossierStatus: 2,
        dossierTaskStatus: null
      }
    ];
    const requestBody = JSON.stringify(requestBodyObj, null, 2);
    this.dossierService.putDossierStatus(requestBody).subscribe(data => {
      if (data.affectedRows === 1) {
        this.getDetailDossier();
      }
    });
  }

  postHistory(){
    let newStatus = '';
    if (this.dossierTaskStatus.name.length > 0){
      newStatus = this.dossierTaskStatus.name[0].name;
      this.dossierTaskStatus.name.forEach(element => {
        if (element.languageId === Number(localStorage.getItem('languageId'))){
          newStatus = element.name;
        }
      });
    }
    const content = {
      groupId: 1,
      itemId: this.dossierId,
      user: {
        id: this.userId,
        name: this.fullname
      },
      type: 3,
      deploymentId: this.config.deploymentId,
      action: [
        {
          fieldNameRbk: 'lang.word.status',
          originalValue: this.oldstatus,
          newValue: newStatus
        }
      ]
    };
    this.dossierService.postHistory(JSON.stringify(content, null, 2)).subscribe(data => {

    });
  }

  GetListUserByPermission(agency) {
    return new Promise<void>((resolve) => {
      this.processService.getApprovalAgency(agency.id).pipe(take(1)).subscribe(async data => {
        console.log('data', data);
        if (!!data && data.content.length !== 0) {
          this.getApprovalAgency = data.content[0].approvalAgency.id;
        }
        else{
          if (!!agency?.parent?.id){
            await this.GetListUserByPermissionParent(agency.parent.id);
          }
        }
        if (this.getApprovalAgency == '') {
          this.getApprovalAgency = agency.id;
        }
        const formObj = this.updateForm.getRawValue();
        if (!!formObj.approvalAgencyId && formObj.approvalAgencyId != '') {
          this.getApprovalAgency = formObj.approvalAgencyId;
        }
        let permission = "pauseDossierApproval";
        this.processService.getListUserByPermissionCode(this.getApprovalAgency, permission).pipe(take(1)).subscribe(data2 => {
          this.officers = data2;
          resolve();
        }, (err) => {
          resolve();
        });
      });
    });
  }

  GetListUserByPermissionParent(agencyId) {
    return new Promise<void>((resolve) => {
      this.processService.getApprovalAgency(agencyId).pipe(take(1)).subscribe(data => {
        console.log('data', data);
        if (!!data && data.content.length !== 0) {
          this.getApprovalAgency = data.content[0].approvalAgency.id;
        }
        // if (this.getApprovalAgency == '') {
        //   this.getApprovalAgency = agencyId;
        // }
        resolve();
      }, (err) => {
        resolve();
      });
    });
  }

  // GetListUserByPermission(agencyId) {
  //   return new Promise<void>((resolve) => {
  //     this.processService.getApprovalAgency(agencyId).pipe(take(1)).subscribe(data => {
  //       console.log('data', data);
  //       if (!!data && data.content.length !== 0) {
  //         this.getApprovalAgency = data.content[0].approvalAgency.id;
  //       }
  //       if (this.getApprovalAgency == '') {
  //         this.getApprovalAgency = agencyId;
  //       }
  //       let permission = "pauseDossierApproval";
  //       this.processService.getListUserByPermissionCode(this.getApprovalAgency, permission).pipe(take(1)).subscribe(data => {
  //         this.officers = data;
  //         resolve();
  //       }, (err) => {
  //         resolve();
  //       });
  //     });
  //   });
  // }

  async onConfirm() {
    this.disableButton = true;
    // await this.GetListUserByPermission(this.agencyId);
    if (!!this.currentTask && !!this.currentTask.candidateGroup[0] && !!this.currentTask.candidateGroup[0].id){
      await this.GetListUserByPermission(this.currentTask.candidateGroup[0]);
    } else {
      await this.GetListUserByPermission({id: this.agencyId});
    }
    if (this.env?.enableApprovalOfLeadership != 2 && this.officers.length == 0) {
      this.homeService.error('Cán bộ phê duyệt hồ sơ của đơn vị chưa được cấu hình. \r\n Vui lòng cấu hình cán bộ phê duyệt để tiếp tục!',
        'Approval officer has not yet been configured \r\n Please configure the approval officer to continue!');
      const msgObj = {
        vi: 'Cán bộ phê duyệt hồ sơ của đơn vị chưa được cấu hình',
        en: 'Approval officer has not yet been configured'
      };
      this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
      this.disableButton = false;
      return
    }

    const formObj = this.updateForm.getRawValue();
    if (formObj.numberPauseDay === '' && !this.checkHideNumberPauseDaySuspend){
      this.disableButton = false;
      const msgObj = {
        vi: 'Vui lòng nhập số ngày tạm dừng!',
        en: 'Please enter the number of pause days!'
      };
      this.snackbarService.openSnackBar(2, '', msgObj[this.selectedLang], 'warning_notification', this.config.expiredTime);
    }
    if (this.commentContent.trim() === '' && !this.checkHideNumberPauseDaySuspend){
      this.disableButton = false;
      const msgObj = {
        vi: 'Vui lòng nhập lý do tạm dừng hồ sơ!',
        en: 'Please enter a reason for pause dossier!'
      };
      this.snackbarService.openSnackBar(2, '', msgObj[this.selectedLang], 'warning_notification', this.config.expiredTime);
    }
    if (this.isCKMaxlenght){
      let msgObj;
      if (this.ckeditorMaxLength > 0) {
        this.disableButton = false;
        msgObj = {
          vi: 'Nội dung không quá ' + this.ckeditorMaxLength + ' ký tự!',
          en: 'Content must not exceed ' + this.ckeditorMaxLength + ' characters!'
        };
      } else {
        msgObj = {
          vi: 'Nội dung không quá 500 ký tự!',
          en: 'Content must not exceed 500 characters!'
        };
      }
      this.snackbarService.openSnackBar(2, '', msgObj[this.selectedLang], 'warning_notification', this.config.expiredTime);
    }

    if (this.visibleApprovalAgency && (!this.getApprovalAgency || this.getApprovalAgency === '')) {
      this.disableButton = false;
      const msgObj = {
        vi: 'Đơn vị phê duyệt hồ sơ chưa được chọn!',
        en: 'The unit that approves the application has not been selected!'
      };
      this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
      return
    }
    let checkRequireAttachment = true;
    if (this.requireAttachmentWhenPause && this.files.length === 0){
      this.disableButton = false;
      checkRequireAttachment = false;
      const msgObj = {
        vi: 'Vui lòng đính kèm tệp tin!',
        en: 'Please attach file!'
      };
      this.snackbarService.openSnackBar(2, '', msgObj[this.selectedLang], 'warning_notification', this.config.expiredTime);
    }
    if (!this.isCKMaxlenght && (this.commentContent.trim() !== '' || this.checkHideNumberPauseDaySuspend) && !this.updateForm.invalid && checkRequireAttachment) {
      if (this.files.length > 0) {
        this.keycloakService.loadUserProfile().then(user => {
          // tslint:disable-next-line: no-string-literal
          const userId = user['attributes'].user_id[0];
          this.uploadMultiFile(this.files, userId);
        });
      } else {
        this.putDossierPause();
      }
    }
  }
  async putDossierPause(){
    const formObj = this.updateForm.getRawValue();
    const requestBodyObj = {
      dossierStatus: this.env?.enableApprovalOfLeadership == 1 ? 9 : 3,
      comment: '',
      enableApprovalOfLeadership: this.env?.enableApprovalOfLeadership,
      dossierTaskStatus: this.dossierTaskStatus,
      dossierMenuTaskRemind: this.dossierMenuTaskRemind,
      numberPauseDay: formObj.numberPauseDay,
      attachment: this.uploadedImage,
      user: {
        id: this.userId,
        fullname: this.fullname,
        account: {
          id: this.accountId,
          username: [
            {
              value: this.userName
            }
          ]
        }
      },
      notSumPauseDayToProcessingTime: this.deploymentService.env.OS_HCM.notSumPauseDayToProcessingTime,
      //IGATESUPP-103472 -	QTI
      sumPauseProcessingTimeDueDate: this.deploymentService.getAppDeployment()?.addTimeForProcessing == 1 ? true: false,
      
    };
    if (this.commentContent.trim() !== '') {
      const msgObj = {
        vi: 'Tạm dừng hồ sơ <b>' + this.dossierCode + '</b> <br /> Lý do: ' + this.commentContent.trim(),
        en: 'Dossier <b>' + this.dossierCode + '</b> has been suspend! <br /> Reason: ' + this.commentContent.trim()
      };
      this.postComment(msgObj[this.selectedLang]);
      requestBodyObj.comment = this.commentContent.trim();
    } else {
      const msgObj = {
        vi: 'Tạm dừng hồ sơ <b>' + this.dossierCode + '</b>',
        en: 'Dossier <b>' + this.dossierCode + '</b> has been suspend!'
      };
      this.postComment(msgObj[this.selectedLang]);
      requestBodyObj.comment = msgObj[this.selectedLang];
    }
    if (this.allowNextStepWaitingForApproval && this.enableApprovePauseCheckBox) {
      requestBodyObj.dossierStatus = 9;

      await this.setdossierTaskStatus();
      await this.setdossierTaskStatusRemind();

      requestBodyObj.dossierTaskStatus.id = this.dossierTaskStatusWaitingForApproval.id;
      requestBodyObj.dossierTaskStatus.name = this.dossierTaskStatusWaitingForApproval.name;
      requestBodyObj.dossierMenuTaskRemind.id = this.dossierMenuTaskRemindWaitingForApproval.id;
      requestBodyObj.dossierMenuTaskRemind.name =this.dossierMenuTaskRemindWaitingForApproval.name;

    }

    const requestBody = JSON.stringify(requestBodyObj, null, 2);
    this.dossierService.putDossierPauseWithComment(this.dossierId, requestBody).subscribe(async data => {
      if (data.affectedRows === 1) {
        if (this.env.enableApprovalOfLeadership == 1 || (this.allowNextStepWaitingForApproval && this.enableApprovePauseCheckBox)){
          const data = {
            type: 9,
            date: tUtils.newDate(),
            attachment: this.uploadedImage,
            pauseDays: formObj.numberPauseDay
          }
          await this.dossierService.updateApprovalData(this.dossierId, data).toPromise();
        }
        this.postHistory();
        // if (this.willSendEmail) {
        //   this.postEmail(this.listEmail, 1);
        // }
        // if (this.willSendSMS) {
        //   this.postSMS(this.listPhone);
        // }
        if(!!this.notifyQNI){
          this.notifyQNIService.confirmSendSubject.next({
            confirm: true,
            renewContent: false,
          });
        } else {
          this.notiService.confirmSendSubject.next({
            confirm: true,
            renewContent: false,
          });
        }
        if(this.env?.enableApprovalOfLeadership === 0 && this.isSyncDBNLGSPTanDanBXD === 1){
          this.syncPostReceiveInforFileFromLocal();
        }
        if(this.isBoTNMTQNM || this.isBoTNMTHCM || this.isBoTNMTGLI){
          this.dossierService.updateDossierBoTNMT(this.dossierId).subscribe(test => {});
        }
        if (this.deploymentService.env?.OS_HBH?.isTnmtTriNam) {
          this.adapterService.sendTNMTTriNam(this.dossierId);
        }

        if (this.isHasCodeLDXH && this.deploymentService?.env?.OS_HBH?.enableLDXHTriNam) {
          Object.assign(this.dossierDetail, { contentTask: 'Tạm dừng hồ sơ'});
          this.trinameService.syncTaskLDXH(this.dossierDetail);
        }
        // IGATESUPP-51500 - Đánh giá lại chức năng phê duyệt hồ sơ - 19.06.2023
        if(this.setOnlyApprovalAgency && this.approvalAgency.length > 1){
          this.postOnlyApprovalAgencyOfDossier(this.dossierCode);
        }
        this.dialogRef.close(true);
        const dataLog = {
          dossierId: this.dossierId,
          code: this.dossierCode,
          body: requestBody
        };
        this.logmanService.postUserEventsLog('pauseDossier', dataLog).subscribe();
        if (this.deploymentService?.env?.OS_BTTTT?.isEnabledNotify) {
          // IGATESUPP-62366 thong bao cho nguoi dan
          this.dossierService.noticeDossier(this.dossierId, {comment: 'Lý do:&nbsp;' + this.commentContent.trim()}).subscribe(res => {});
        }
      } else {
        this.dialogRef.close(false);
      }
    }, err => {
      this.dialogRef.close(false);
    });
  }

  onDismiss() {
    this.dialogRef.close();
  }

  postComment(commentContent) {
    const content = {
      groupId: 2,
      itemId: this.dossierId,
      user: {
        id: this.userId,
        fullname: this.fullname
      },
      file: this.uploadedImage,
      content: commentContent.trim()
    };
    const requestBody = JSON.stringify(content, null, 2);
    this.dossierService.postComment(requestBody).subscribe(data => { });
  }

  onCommentEditorChange(event) {
    this.commentContent = event.editor.getData();
    if (this.ckeditorMaxLength > 0) {
      if (event.editor.getData().trim().length > this.ckeditorMaxLength) {
        this.isCKMaxlenght = true;
      } else {
        this.isCKMaxlenght = false;
      }
    } else if (event.editor.getData().trim().length > 500) {
      this.isCKMaxlenght = true;
    } else {
      this.isCKMaxlenght = false;
    }
  }

  addApologyText(dossierId) {
    const dialogData = new ConfirmAddApologyTextComponent(dossierId);
    const dialogRef = this.dialog.open(AddApologyTextComponent, {
      minWidth: '40vw',
      maxWidth: '60vw',
      maxHeight: '60vh',
      data: dialogData,
      disableClose: true,
      autoFocus: false
    });
  }

  signApologyText(dossierId) {
    const dialogData = new ConfirmSignApologyTextComponent(dossierId);
    const dialogRef = this.dialog.open(SignApologyTextComponent, {
      minWidth: '50vw',
      maxWidth: '60vw',
      minHeight: '40vh',
      maxHeight: '70vh',
      data: dialogData,
      disableClose: true,
      autoFocus: false
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
      if (dialogResult === true) {
        const msgObj = {
          vi: 'Đã lưu!',
          en: 'Saved!'
        };
        this.snackbarService.openSnackBar(1, msgObj[localStorage.getItem('language')], '', 'success_notification', this.config.expiredTime);
      }else if (dialogResult === false) {
        const msgObj = {
          vi: 'Lưu thất bại!',
          en: 'Save failed!'
        };
        this.snackbarService.openSnackBar(0, msgObj[localStorage.getItem('language')], '', 'error_notification', this.config.expiredTime);
      }
    });
  }
  onCheckboxSendChange(type) {
    switch (type) {
      case 1:
        this.willSendSMS = !this.willSendSMS;
        if (this.willSendSMS === true) {
          this.smsEmailMaxLength = this.config.dossierEmailSMSZaloConfig.characterLimit;
        } else {
          this.smsEmailMaxLength = null;
        }
        break;
      case 2:
        this.willSendEmail = !this.willSendEmail;
        break;
    }
    if (this.willSendSMS || this.willSendEmail) {
      this.willSend = true;
    } else {
      this.willSend = false;
    }
  }
  postEmail(listEmail, type) {
    if (listEmail.length > 0) {
      listEmail.forEach(mail => {
        const emailConfig = this.config.dossierEmailSMSZaloConfig;
        let subject = emailConfig.increaseDue[this.selectedLang];
        subject = subject.replace('{{code}}', this.dossierDetail.code);
        const contentTemp = emailConfig.reasonExtend[this.selectedLang] + '' + this.smsEmailContent;
        const userAgency = JSON.parse(localStorage.getItem('userAgency'));
        let rootAgencyId: any = '';
        if (userAgency !== null) {
          rootAgencyId = userAgency.id;
        } else {
          rootAgencyId = this.config.rootAgency.id;
        }
        this.dossierService.postEmailByAgency(
          rootAgencyId,
          this.config.subsystemId,
          contentTemp,
          [mail.email],
          subject
        ).subscribe(emailRS => {
          console.log(emailRS);
        }, err => {
          console.log(err);
        });
      });
    }
  }

  postSMS(listPhoneNumber) {
    const emailConfig = this.config.dossierEmailSMSZaloConfig;
    let subject = emailConfig.increaseDue[this.selectedLang];
    subject = subject.replace('{{code}}', this.dossierDetail.code);
    subject += '. ' + emailConfig.reasonExtend[this.selectedLang] + '' + this.smsEmailContent;
    console.log(subject)
    if (listPhoneNumber.length > 0) {
      listPhoneNumber.forEach(phone => {
        const userAgency = JSON.parse(localStorage.getItem('userAgency'));
        let rootAgencyId: any = '';
        if (userAgency !== null) {
          rootAgencyId = userAgency.id;
        } else {
          rootAgencyId = this.config.rootAgency.id;
        }
        this.dossierService.postSMSByAgency(
          rootAgencyId,
          this.config.subsystemId,
          subject,
          [phone.number]
        ).subscribe(smsRS => {
          console.log(smsRS);
        }, err => {
          console.log(err);
        });
      });
    }
  }
    // ========= file
  uploadMultiFile(file, userId) {
    this.procedureService.uploadMultiFile(file, userId).subscribe(data => {
        this.uploadedImage = data;
        this.putDossierPause();
      }, err => {
        console.log(err);
    });
  }

  onSelectFile(event) {
      if (event.target.files && event.target.files[0]) {
        for (const i of event.target.files) {
          if (i.size >= this.maxFileSize * 1024 * 1024) {
            const msgObj = {
              vi: 'Kích thước tệp tin quá lớn, tên tệp: ' + i.name,
              en: 'The file is too large, file name: ' + i.name
            };
            this.snackbarService.openSnackBar(0, '', msgObj[this.selectedLang], 'error_notification', this.config.expiredTime);
            return;
          }
          if (this.acceptFileType.filter(t => t === i.type.toLowerCase()).length > 0) {
            this.files.push(i);
            const extension = i.name.substring(i.name.lastIndexOf('.')).split('.').pop();
            this.urls.push(this.getFileIcon(extension));

            const reader = new FileReader();
            reader.onload = () => {
              this.uploaded = true;
            };
            if (i.name.length > 20) {
              const startText = i.name.substr(0, 5);
              const shortText = i
                .name
                .substring(i.name.length - 7, i.name.length);
              this.fileNames.push(startText + '...' + shortText);
              this.fileNamesFull.push(i.name);
            } else {
              this.fileNames.push(i.name);
              this.fileNamesFull.push(i.name);
            }
            reader.readAsDataURL(i);
          }
          else {
            const msgObj = {
              vi: 'Không hỗ trợ loại tệp tin ',
              en: 'File type is not supported '
            };
            // tslint:disable-next-line: max-line-length
            this.snackbarService.openSnackBar(0, '', msgObj[this.selectedLang] + i.type.substring(i.type.lastIndexOf('/') + 1).toUpperCase(), 'error_notification', this.config.expiredTime);
          }
        }
      }
  }

  removeItem(index: number) {
      this.urls.splice(index, 1);
      this.fileNames.splice(index, 1);
      this.fileNamesFull.splice(index, 1);
      this.files.splice(index, 1);
      this.blankVal = '';
  }

  getFileIcon(ext) {
      return this.config.cloudStaticURL + 'icon/files/300x300/' + ext + '.png';
  }
  getConfigCloud() {
    const config = this.deploymentService.getAppDeployment();
    if (this.env?.OS_BDG?.isRequiredUploadFileBDG?.suspend != undefined && this.env?.OS_BDG?.isRequiredUploadFileBDG?.suspend != null) {
      this.requireAttachmentWhenPause = this.env?.OS_BDG?.isRequiredUploadFileBDG?.suspend;
    } else {
      if (!!config?.env?.MCDT_DINH_KEM_FILE_KHI_TAM_DUNG) {
        this.requireAttachmentWhenPause = config?.env?.MCDT_DINH_KEM_FILE_KHI_TAM_DUNG;
      }
    }
  }

  // DBN - BXD - dong bo thong hs qua LGSP Tan Dan
  syncPostReceiveInforFileFromLocal (){
    this.dossierService.getDossierDetail(this.dossierId).subscribe(data => {
      let dataApplicant =  data?.applicant?.data;
      let nhanThongTinHSTuDP = [];
      let thongTinNguoiNop = {
        HoTenNguoiNop : dataApplicant?.fullname, //*
        SoCMND : dataApplicant?.identityNumber, //*
        EmailNguoiNop: dataApplicant?.email,
        SoDienThoaiNguoiNop: dataApplicant?.phoneNumber, //*
        DiaChiThuongTruNguoiNop: dataApplicant?.address + ","
                               + dataApplicant?.village?.label + ","
                               + dataApplicant?.district?.label + ","
                               + dataApplicant?.province?.label + ","
                               + dataApplicant?.nation?.label, //*
        MaDoiTuong: ""
      };
      let giayToTrongQuaTrinhXuLy = {
        MaGiayTo: "",
        TenGiayTo: "",
        NoiDungBase64: "",
        DinhDangTepTin: "", // dinh dang .pdf, .doc, .png,...
        MoTa: "",
        LoaiGiayTo: ""
      }
      let hinhThucTraKetQua = 0;
      switch(data?.dossierReceivingKind?.id) {
        case '5f8968888fffa53e4c073ded':
        hinhThucTraKetQua = 0;
        break;
        case '604ecde877bed4110c1dd0d1':
          hinhThucTraKetQua = 1;
          break;
        case '6163f1e20f363b04cf60b224':
          hinhThucTraKetQua = 1;
          break;
        case '5f8969018fffa53e4c073dee':
          hinhThucTraKetQua = 1;
          break;
      }
      let arrThongTinTienTrinh = [];
      let dossierStatus = this.statusName;
      data?.task.forEach(task => {
          if(task?.isCurrent === 1){
            let thongTinTienTrinh = {
              NguoiXuLy: task?.assignee?.fullname, //*
              ChucDanh: "",
              ThoiDiemXuLy: this.datePipe.transform(task?.createdDate, 'yyyyMMddHHmmss'), //* yyyyMMddHHmmss
              PhongBanXuLy: task?.agency?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name,
              NoiDungXuLy:  task?.bpmProcessDefinitionTask?.remind ?  task?.bpmProcessDefinitionTask?.remind?.name : "",
              NgayBatDau:   this.datePipe.transform(task?.createdDate, 'yyyyMMddHHmmss'), //yyyyMMddHHmmss
              NgayKetThucTheoQuyDinh: task?.isCurrent === 0? this.datePipe.transform(task?.updatedDate, 'yyyyMMddHHmmss') : ""
            }
            arrThongTinTienTrinh.push(thongTinTienTrinh);
          }

      });
      let dossierSync = {
        MaHoSo: data.code, //*
        TrangThaiHoSo: dossierStatus, // phu luc 2 *
        NgayTiepNhan: this.datePipe.transform(data?.acceptedDate, 'yyyyMMddHHmmss') ,// yyyyMMddHHmmss*
        NgayHenTraKetQua: this.datePipe.transform(data?.appointmentDate, 'yyyyMMddHHmmss'), //yyyyMMddHHmmss
        ThongTinNguoiNop : thongTinNguoiNop,
        GiayToTrongQuaTrinhXuLy : [],
        HinhThucTraKetQua: hinhThucTraKetQua, // 0: tra truc tiep 1: tra qua duong buu dien 2: tra kq truc tuyen
        ThongTinTienTrinh : arrThongTinTienTrinh,
        MaTTHC: data?.procedure?.code,
        TenTTHC: data?.procedure?.translate?.find(procedure => procedure.languageId === Number(this.selectedLangId))?.name ? data?.procedure?.translate?.find(procedure => procedure.languageId === Number(this.selectedLangId))?.name : '',
      }
      //nhanThongTinHSTuDP.push(dossierSync);
      let params = "agencyId=" + data?.agency.id + "&subsystemId=" + this.config?.subsystemId ; // &configId
      this.dossierService.postReceiveInforFileFromLocal(params, dossierSync).subscribe(async data => {
        console.log("===postReceiveInforFileFromLocal===");
        console.log(data);
      }, er => {
        console.log(er);
      })
    }, err => {
      console.log(err);
    });
  }

  getApprovaledAgency(agency) {
    this.approvalAgency = [];
    let enableApprovaledAgencyId = this.deploymentService.env.OS_QNI.enableApprovaledAgencyId;
    if (enableApprovaledAgencyId){
      if (agency && agency.id) {
        this.processService.getApprovalAgency(agency.id).subscribe(data2 => {    
          if (data2 && data2.content && data2.content.length > 0) {
            const uniqueAgencies = new Set();
            data2.content.forEach(item => {
              if (item.approvalAgency && item.approvalAgency.name) {
                item.approvalAgency.name.forEach(name => {
                  if (name.languageId === this.selectedLangId) {
                    item.approvalAgency.DisplayName = name.name;
                  }
                });
                if (!uniqueAgencies.has(item.approvalAgency.id)) {
                  uniqueAgencies.add(item.approvalAgency.id);
                  this.approvalAgency.push(item.approvalAgency);
                }
              }
            });
  
            if (this.approvalAgency.length === 1) {
              this.updateForm.patchValue({
                approvalAgencyId: this.approvalAgency[0].id,
              });
            }
          }
        });
      }
    }else{
      if (!!agency.parent) {
        this.processService.getApprovalAgency(agency.parent.id).subscribe(data => {
          if (data.content.length > 0){
            this.approvalAgency = [];
            // tslint:disable-next-line:prefer-for-of
            for (let i = 0; i < data.content.length; i++){
              data.content[i].approvalAgency.name.forEach(name => {
                if (name.languageId === this.selectedLangId){
                data.content[i].approvalAgency.DisplayName = name.name;
                }
              });
              if (this.approvalAgency.filter(item => item.id === data.content[i].approvalAgency.id).length === 0){
                this.approvalAgency.push(data.content[i].approvalAgency);
              }
            }
          }
          if (!!this.approvalAgency) {
            this.processService.getApprovalAgency(agency.id).subscribe(data2 => {
              if (data2.content.length > 0){
                // tslint:disable-next-line:prefer-for-of
                for (let i = 0; i < data2.content.length; i++){
                  data2.content[i].approvalAgency.name.forEach(name => {
                    if (name.languageId === this.selectedLangId){
                    data2.content[i].approvalAgency.DisplayName = name.name;
                    }
                  });
                  if (this.approvalAgency.filter(item => item.id === data2.content[i].approvalAgency.id).length === 0){
                    this.approvalAgency.push(data2.content[i].approvalAgency);
                  }
                }
              }
              if (!!this.approvalAgency &&  this.approvalAgency.length === 1){
                this.updateForm.patchValue({
                  approvalAgencyId: this.approvalAgency[0].id,
                });
              }
            });
          }
        });
      }
      console.log('approvalAgency', this.approvalAgency);
    }
  }

  checkAgencyHideNumberPauseDaySuspend() {
    const userAgency = JSON.parse(localStorage.getItem('userAgency'));

    if (this.hideNumberPauseDaySuspendWithAgency.length !== 0 && this.hideNumberPauseDaySuspend) {
      if (this.hideNumberPauseDaySuspendWithAgency.includes(userAgency.id))
      {
        this.checkHideNumberPauseDaySuspend = true;
        this.updateForm.get('numberPauseDay').setValue(this.defaultNumberPauseDay);
      }
      else if (!!userAgency.ancestors) {
        for (const agency of userAgency.ancestors) {
          if(this.hideNumberPauseDaySuspendWithAgency.includes(agency.id))
          {
            this.checkHideNumberPauseDaySuspend = true;
            this.updateForm.get('numberPauseDay').setValue(this.defaultNumberPauseDay);
          }
        }
      }
      
    }
    else {
      this.checkHideNumberPauseDaySuspend = false;
    }
    console.log("checkHideNumberPauseDaySuspend", this.checkHideNumberPauseDaySuspend)
  }

  setdossierTaskStatus() {
    return new Promise<void>(resolve => { 
      const tagWaitingForApprovalStatusId = this.allowNextStepWaitingForApproval ? this.deploymentService.env.dossierTaskStatus.waitingForApprovalToPause.id : this.deploymentService.env.dossierTaskStatus.pending.id;
      this.dossierService.getAgencyTag(tagWaitingForApprovalStatusId).subscribe(data => {
        this.dossierTaskStatusWaitingForApproval.id = data.id;
        this.dossierTaskStatusWaitingForApproval.name = data.trans;
        resolve();
      }, err => {
        resolve();
      });
    });

  }

  setdossierTaskStatusRemind () {
    return new Promise<void>(resolve => {
      const tagWaitingForApprovalRemindId = this.allowNextStepWaitingForApproval ? this.deploymentService.env.dossierMenuTaskRemind.waitingForApprovalToPause.id : this.deploymentService.env.dossierMenuTaskRemind.pending.id;
      this.dossierService.getAgencyTag(tagWaitingForApprovalRemindId).subscribe(data => {
        this.dossierMenuTaskRemindWaitingForApproval.id = data.id;
        this.dossierMenuTaskRemindWaitingForApproval.name = data.trans;
        this.statusName = data?.trans.filter(t => t.languageId === Number(this.selectedLangId))?.name;
        resolve();
      }, err => {
        resolve();
      });
    });
  }


}

export class SuspendModel {
  constructor(public dossierId: string, public dossierCode: string) {
  }
}
