import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { ApiProviderService } from 'src/app/core/service/api-provider.service';

@Injectable({
  providedIn: 'root',
})
export class QnmQuestionService {
  private questionPath = this.apiProviderService.getUrl('digo', 'basepad') + '/cauhoi-thuonggap';
  constructor(
    private http: HttpClient,
    private apiProviderService: ApiProviderService
  ) {}
  // lấy danh sách câu hỏi thường gặp
  getListFrequentlyQuestion(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.questionPath + '/danh-sach' + searchString, { headers }).pipe();
  }

  getListFrequentlyQuestionAll(): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.questionPath + '/danh-sach-all', { headers }).pipe();
  }

  // minhvnt.dng add 07/01/2023 => thêm mới câu hỏi thường gặp
  addFrequentlyQuestion(requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.post<any>(this.questionPath + "/--add", requestBody, { headers });
  }

  // cập nhật câu hỏi thường gặp
  updateFrequentlyQuestion(id, requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.put<any>(this.questionPath + `/--update/${id}`, requestBody, { headers });
  }

  // xóa câu hỏi thường gặp
  deleteFrequentlyQuestion(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.delete<any>(this.questionPath +`/--delete/${id}`, { headers });
  }

  // chi tiết câu hỏi thường gặp
  detailFrequentlyQuestion(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.questionPath + `/cauhoi-thuonggap/--search${searchString}`, { headers });
  }

  // upload file template
  postQuestionFromFile(payload: any, file: any): Observable<any> {
    const formData: FormData = new FormData();
    // formData.append('postQnmQuestionByFile', payload);
    formData.append('postQnmQuestionByFile', new Blob([JSON.stringify(payload)], {
      type: 'application/json'
    }));
    formData.append('file', file);
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'multipart/form-data');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.post<any>(this.questionPath + `/--from-file`, formData, );
  }
}
