.cell_info {
  color: blue;
  text-decoration: none;
  cursor: pointer;
  max-width: 100%;
}

.example-form-field {
  margin-right: 20px;
}

td.mat-footer-cell {
  text-align: center;
}

.search-nested {
  width: 80% !important;
  height: 50px !important;
  margin-left: 15px !important;
}

.clear-search-nested {
  right: 0 !important;
  position: absolute !important;
  top: 0 !important;
}
// ================================= searchForm
.hidden{
  display: none !important;
}
::ng-deep .frm_searchbar .searchForm .mat-form-field-appearance-outline .mat-form-field-outline {
  color: transparent;
}

::ng-deep .frm_searchbar .searchForm .mat-form-field-appearance-outline .mat-form-field-outline-thick {
  color: #dddddd;
}

::ng-deep .frm_searchbar .searchForm .mat-form-field-appearance-outline .mat-form-field-outline {
  background-color: #eaebeb;
  border-radius: 5px;
}


::ng-deep .frm_searchbar .mat-form-field-appearance-outline .mat-form-field-infix {
  padding: 0.8em 0;
}

::ng-deep .frm_searchbar .mat-form-field-label-wrapper {
  top: -1em;
}

::ng-deep .frm_searchbar .mat-form-field.mat-focused .mat-form-field-label {
  color: #ce7a58;
  font-size: 18px;
  margin-bottom: 1em;
}

::ng-deep .frm_searchbar .mat-form-field-appearance-outline.mat-form-field-can-float.mat-form-field-should-float .mat-form-field-label {
  color: #ce7a58;
  font-size: 18px;
  margin-bottom: 1em;
}
// ================================= table + frm_tbl + tab 1
::ng-deep .frm_tbl0 table {
  width: 100%;
  margin-top: 1.5vh;
}

.data-label {
  word-wrap: break-word;
}

::ng-deep .frm_tbl0 th.mat-header-cell, td.mat-cell, td.mat-footer-cell {
  text-align: center;
  border: 1px solid #CCC;
  padding: 0 !important;
  word-wrap: break-word;
  color: #495057;
}

::ng-deep .frm_tbl0 .mat-header-row {
  background-color: #e8e8e8;
  word-wrap: break-word;
  // white-space: none;
  // white-space: normal;
}

::ng-deep .frm_tbl0 .mat-header-row .mat-header-cell p {
  margin-bottom: 0;
  font-weight: 400;
  font-style: italic;
  word-wrap: break-word;
}

::ng-deep .frm_tbl0 .mat-row:nth-child(even) {
  background-color: #FAFAFA;
}

::ng-deep .frm_tbl0 .mat-row:nth-child(odd) {
  background-color: #fff;
}

tr.mat-footer-row {
  font-weight: bold;
}

a:hover {
  text-decoration: underline;
  cursor: pointer;
}

::ng-deep .frm_searchbar .searchForm{
  @import "~src/styles/buttons.scss";

  .btn-download-excel {
      @extend .t-btn-download-excel;
  }

  .btn-search {
      @extend .t-btn-search;
  }
}

.mat-radio-button {
  padding: 8px 20px;
}

.mat-column-def {
  width: auto !important;
}


.mat-cell, .mat-header-cell {
  white-space: normal !important;
  word-wrap: break-word !important;
}

.mat-table {
  table-layout: auto !important;
  overflow: auto !important;
  word-break: break-word !important;
  word-wrap: break-word !important;
}

::ng-deep .frm_tbl0 .mat-column-stt2 {
  flex: 0 0 3%;
  text-align: center !important;
}

::ng-deep .frm_tbl0 .mat-column-dossierCode {
  flex: 0 0 13%;
}
