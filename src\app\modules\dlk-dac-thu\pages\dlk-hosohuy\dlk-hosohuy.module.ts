import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { DlkHoSoHuyRoutingModule } from './dlk-hosohuy-routing.module';
import { DlkHoSoHuyComponent } from './dlk-hosohuy.component';
import { SharedModule } from 'src/app/shared/shared.module';
import { NgxPrinterModule } from 'ngx-printer';


@NgModule({
  declarations: [DlkHoSoHuyComponent],
  imports: [
    CommonModule,
    DlkHoSoHuyRoutingModule,
    SharedModule,
    NgxPrinterModule.forRoot({ printOpenWindow: true })
  ]
})
export class DlkHoSoHuyModule { }
