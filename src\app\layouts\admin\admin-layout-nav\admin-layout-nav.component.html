<div class="main-container" [class.main-is-mobile]="mobileQuery.matches">
    <mat-sidenav-container class="main-sidenav-container" fxLayout="row" fxLayout.xs="column" fxLayout.sm="column">
        <mat-sidenav #snav [mode]="mobileQuery.matches ? 'over' : 'side'" [fixedInViewport]="mobileQuery.matches" [opened]="isSidebarActive()" fxFlex.gt-sm="20" fxFlex='70' [ngStyle]="{'background-image': 'url(' + sidenav.backgroundImg + ')'}">
            <div class="site_logo" [ngStyle]="{'background-color': siteContent.backgroundColor}">
                <a href="/">
                    <div class="logo_img" fxShow="true" fxHide.lt-md [ngStyle]="{'background-image': 'url(' + siteContent.logoUrl + ')'}"></div>
                    <div class="site_name">
                        <span class="line-height-logo" *ngIf="siteContent.name !== undefined">{{siteContent.name[selectedLang]}}</span>
                        <span class="line-height-logo" *ngIf="QniHeThongText === false">{{siteName}}</span>
                        <span class="line-height-logo" *ngIf="QniHeThongText === true">HỆ THỐNG THÔNG TIN GIẢI QUYẾT THỦ TỤC HÀNH CHÍNH TỈNH QUẢNG NGÃI</span>
                    </div>
                </a>
                <button class="close-button" mat-icon-button (click)="snav.toggle()" fxShow="true" fxHide.gt-sm>
                    <mat-icon>close</mat-icon>
                </button>
            </div>
            <!-- menu -->
            <mat-accordion [multi]="true">
              <ng-container *ngIf="!!configs?.storage468">
                <span *ngIf="match(['storage4Emp','storage4Adm']) && this.configs?.storage468?.webDocAdmURL && !!this.configs?.storage468?.enableStorageMenu">
                    <span *ngFor='let m of storageMenu; let i = index;'>
                        <mat-divider *ngIf="countAvailableMenu[i] > 0"></mat-divider>
                            <mat-expansion-panel id="sidebar_menu1"
                                                 [expanded]="matchPosition(position, m.listSubMenu, i)"
                                                 *ngIf="countAvailableMenu[i] > 0 && storageMenu[i].active;  else isNotActiveSubMenu">
                            </mat-expansion-panel>
                        <ng-template #isNotActiveSubMenu>
                            <button mat-button routerLinkActive="active" routerLink="/{{ m.route }}"
                                    [queryParams]="{'system':'igate2', 'agency-code': agency?.code}"
                                    style=" margin:0.1em 0;box-shadow: unset;padding: 0.2em 1em 0.2em 0.6em; color:black; font-weight: 500; width: 100%;text-align: left;">
                                <mat-icon class="material-icons-outlined" style="margin-right: 0.3em "
                                          [ngClass]="{'menuIcon_active': m.active === true}">store</mat-icon>
                                <span *ngFor="let mainmenu of m.mainMenu"
                                      [ngClass]="{'hidden': mainmenu.languageId != selectedLangId}">{{mainmenu.name}}</span>
                            </button>
                            <mat-divider></mat-divider>
                        </ng-template>
                    </span>
                </span>
              </ng-container>
              <ng-container *ngIf="!!configs?.chungThucDienTu">
                  <span *ngFor='let m of chungThucMenu; let i = index;'>
                      <button mat-button (click)="redirectChungThuc()" *ngIf="m.isActive && this.configs?.chungThucDienTu?.domain_chungthuc"
                              style=" margin:0.1em 0;box-shadow: unset;padding: 0.2em 1em 0.2em 0.6em; color:black; font-weight: 500; width: 100%;text-align: left;">
                          <mat-icon class="material-icons-outlined" style="margin-right: 0.3em "
                                    [ngClass]="{'menuIcon_active': m.active === true}"> {{m.icon}}</mat-icon>
                          <span *ngFor="let mainmenu of m.mainMenu"
                                [ngClass]="{'hidden': mainmenu.languageId != selectedLangId}">{{mainmenu.name}}</span>
                      </button>
                      <mat-divider></mat-divider>
                  </span>
              </ng-container>
            <span *ngFor='let m of sidebarMenu; let i = index;'>
                    <mat-divider *ngIf="i > 0"></mat-divider>
                    <mat-expansion-panel id="sidebar_menu" [expanded]="matchPosition(position, m.listSubMenu, i)"
                                         *ngIf="countAvailableMenu[i] > 0">
                        <mat-expansion-panel-header [collapsedHeight]="'20px'" [expandedHeight]="'20px'">
                            <mat-panel-title>
                                <mat-icon class="material-icons-outlined"
                                          [ngClass]="{'menuIcon_active': m.active === true}">{{m.icon}} </mat-icon>
                                <span *ngFor="let mainmenu of m.mainMenu" [ngClass]="{'hidden': mainmenu.languageId != selectedLangId}">{{mainmenu.name}}</span>
                </mat-panel-title>
                </mat-expansion-panel-header>

                <span *ngFor='let submenu of m.listSubMenu; let j = index'>
                            <a id="submenu" mat-button *ngIf="submenu.isActive"
                               routerLinkActive="active" routerLink="/{{ submenu.route }}">
                                <span class="submenuTitle" *ngFor="let sub of submenu.title" [ngClass]="{'hidden': sub.languageId != selectedLangId}">{{sub.name}}</span>
                </a>
                </span>
                </mat-expansion-panel>
                </span>
          </mat-accordion>
        </mat-sidenav>
        <mat-sidenav-content fxFlex.gt-sm="80" fxFlex='100'>
            <mat-toolbar *ngIf="isEnableToolbar()" class="main-toolbar">
                <!-- Responsive -->
                <mat-toolbar-row fxShow="true" fxHide.gt-sm>
                    <button class="btn_toggleMenu" mat-icon-button (click)="snav.toggle()">
                        <mat-icon>menu_open</mat-icon>
                    </button>

                    <span class="toolbar-spacer"></span>
                    <a href="/" class="site_logo">
                        <div class="logo_img" [ngStyle]="{'background-image': 'url(' + config.cloudStaticURL + 'logo/quoc-huy.png)'}"></div>
                    </a>

                    <span class="toolbar-spacer"></span>

                    <button mat-icon-button [matMenuTriggerFor]="resMenu" class="btn_resMenu">
                        <mat-icon>category</mat-icon>
                    </button>
                    <mat-menu #resMenu="matMenu">
                        <button mat-menu-item [matMenuTriggerFor]="gridMenu" *ngIf="listAppEnable == 1 && listApps.length > 0">
                            <mat-icon>apps</mat-icon>
                            <span i18n>Ứng dụng</span>
                        </button>
                        <mat-divider></mat-divider>
                        <button mat-menu-item [matMenuTriggerFor]="onegateMenu" *ngIf="listAppEnable == 1 && listOnegateApps.length > 0">
                            <mat-icon class="material-icons-outlined">widgets</mat-icon>
                            <span i18n>Ứng dụng một cửa</span>
                        </button>
                        <mat-divider></mat-divider>
                        <button mat-menu-item [matMenuTriggerFor]="siteMenu">
                            <mat-icon>account_balance</mat-icon>
                            {{siteName}}
                        </button>
                        <button mat-menu-item [matMenuTriggerFor]="typeComboBoxLanguage != 2 ? langMenu : null" *ngIf="selectedLang == 'vi' && typeComboBoxLanguage != 3 && !hideLanguageHeader">
                            <mat-icon>language</mat-icon>
                            Tiếng Việt (VI)
                        </button>
                        <button mat-menu-item [matMenuTriggerFor]="typeComboBoxLanguage != 2 ? langMenu : null" *ngIf="selectedLang == 'en' && typeComboBoxLanguage != 3 && !hideLanguageHeader">
                            <mat-icon>language</mat-icon>
                            English (EN)
                        </button>
                        <button mat-menu-item [matMenuTriggerFor]="notiMenu" *ngIf="notificationEnable == 1">
                            <mat-icon matBadgeSize="small" matBadge="{{totalDossierRemind}}" matBadgeColor="warn">notifications</mat-icon>
                            <span i18n>Thông báo</span>
                        </button>
                        <mat-divider></mat-divider>
                        <button *ngIf="!showTooltipAgencyPositionName" mat-menu-item [matMenuTriggerFor]="accountMenu">
                            <mat-icon>account_circle</mat-icon>
                            <span class="res_accountName">{{userName}}</span>
                        </button>
                        <button *ngIf="showTooltipAgencyPositionName" mat-menu-item [matMenuTriggerFor]="accountMenu" matTooltip="{{userPositionName}}">
                          <mat-icon>account_circle</mat-icon>
                          <span class="res_accountName">{{userName}}</span>
                      </button>
                    </mat-menu>
                </mat-toolbar-row>

                <mat-toolbar-row fxShow="true" fxHide.lt-md>
                    <!-- Show/hide sidebar -->
                    <button class="btn_toggleMenu" mat-icon-button (click)="snav.toggle()">
                        <mat-icon>menu_open</mat-icon>
                    </button>
                    <ng-container *ngIf="userAgency.length < 2">
                        <a mat-button class="main-app-name" style="max-width: 45%;">
                            <span class="main-agency-name" *ngIf="agencyName == ''">{{siteName}}</span>
                            <span class="main-agency-name" matTooltip="{{userAgencyPositionName}}" *ngIf="agencyName != ''">{{agencyName}}</span>
                        </a>
                    </ng-container>
                    <ng-container *ngIf="userAgency.length > 1 && !showTooltipAgencyPositionName && !filterByPositionAgencyType">
                        <mat-select [(value)]="selectedOption" style="max-width: 20em;" title="Chọn đơn vị">
                            <mat-option *ngFor="let ag of userAgency" value="{{ag.agency.id}}" (click)='onAgencyChange(ag)' style="font-size: 16px;line-height: 3em; height: unset;">
                                <span class="main-agency-name">{{ ag.agency.name }}</span>
                            </mat-option>
                        </mat-select>
                    </ng-container>
                    <ng-container *ngIf="userAgency.length > 1 && showTooltipAgencyPositionName && !filterByPositionAgencyType">
                      <mat-select [(value)]="selectedOption" matTooltip="{{userAgencyPositionName}}" style="max-width: 20em;" title="Chọn đơn vị">
                          <mat-option *ngFor="let ag of userAgency" value="{{ag.agency.id}}" matTooltip="{{ ag.agency.name }} ({{ag?.position?.name}})" (click)='onAgencyChange(ag)' style="font-size: 16px;line-height: 3em; height: unset;">
                              <span class="main-agency-name">{{ ag.agency.name }}</span>
                          </mat-option>
                      </mat-select>
                  </ng-container>
                  <ng-container *ngIf="userAgency.length > 1 && filterByPositionAgencyType">
                    <mat-select [(value)]="selectedOption" matTooltip="{{userAgencyPositionName}}" style="max-width: 20em;" title="Chọn đơn vị">
                            <mat-option *ngFor="let ag of userAgency" value="{{ag.id}}" (click)='onAgencyChange(ag)' style="font-size: 16px;line-height: 3em; height: unset;">
                           <span class="main-agency-name">{{ag?.position?.name}} / {{ ag.agency.name }}</span>
                        </mat-option>
                    </mat-select>
                </ng-container>
                    <span class="toolbar-spacer"></span>

                    <a mat-button class="btn_gridMenu" *ngIf="homeUrl != null && homeUrl != ''" href="{{homeUrl}}">
                        <mat-icon matTooltip= "Trang chủ">home</mat-icon>
                    </a>
                    <mat-divider vertical class="v_divider"></mat-divider>

                    <!-- Noti -->

                    <a mat-button [matMenuTriggerFor]="subNotiMenu" class="btn_notiMenu" *ngIf="notificationEnable == 1 && checkShowSubMenuNotiValue">
                        <ng-container *ngIf="subMenuRemindNoti.length > 0">
                            <mat-icon matTooltip="Thông báo" class="material-icons" matBadge="{{totalSubNotiMenu}}" matBadgeColor="warn">notifications</mat-icon>
                        </ng-container>
                        <ng-container *ngIf="subMenuRemindNoti.length === 0">
                            <mat-icon matTooltip="Thông báo" class="material-icons">notifications</mat-icon>
                        </ng-container>
                    </a>

                    <!-- Hồ sơ đến hạn -->
                    <a mat-button
                        *ngIf="remindDossierDueDlk"
                        [routerLink]="['/dlk-dac-thu/dlk-hosotoihan']" 
                        [queryParams]="{isRemind: true}"
                        class="btn_remindDD">
                        <ng-container *ngIf="remindDDCount == 0">
                            <mat-icon [matBadge]="remindDDCount"
                                      matBadgeColor="warn"
                                      matTooltip="Không có hồ sơ nào sắp hết hạn trong 1 ngày">
                                pending_actions
                            </mat-icon>
                        </ng-container>
                        <ng-container *ngIf="remindDDCount > 0">
                            <mat-icon [matBadge]="remindDDCount"
                                      matBadgeColor="warn"
                                      [matTooltip]="'Có ' + remindDDCount + ' hồ sơ sắp hết hạn trong 1 ngày'">
                                pending_actions
                            </mat-icon>
                        </ng-container>
                    </a>

                    <mat-menu #subNotiMenu="matMenu">
                        <ng-container *ngIf="subMenuRemindNoti.length > 0">
                            <ng-container *ngFor="let item of subMenuRemindNoti">
                                <a (click)='onClickSubMenuRemind(item.type)' class="remindMenu" mat-menu-item>
                                    <span class="remindNumber">{{item.value}}</span>
                                    <span class="remindName" *ngIf="item.name != null">{{item.name}}</span>
                                    <span class="remindName" *ngIf="item.name == null" i18n>(Công việc chưa cấu hình tên)</span>
                                </a>
                            </ng-container>
                        </ng-container>
                        <!-- <ng-container *ngIf="subMenuRemindNoti.length === 0">
                            <div mat-menu-item i18n>Không có thông báo nào!</div>
                        </ng-container> -->
                    </mat-menu>

                    <a mat-button [matMenuTriggerFor]="notiMenu" class="btn_notiMenu" *ngIf="notificationEnable == 1 && !checkShowSubMenuNotiValue">
                        <ng-container *ngIf="listDossierRemind.length > 0">
                            <mat-icon matTooltip="Thông báo" class="material-icons" matBadge="{{totalDossierRemind}}" matBadgeColor="warn">notifications</mat-icon>
                        </ng-container>
                        <ng-container *ngIf="listDossierRemind.length === 0">
                            <mat-icon matTooltip="Thông báo" class="material-icons">notifications</mat-icon>
                        </ng-container>
                    </a>
                    <mat-menu #notiMenu="matMenu">
                        <ng-container *ngIf="listDossierRemind.length > 0">
                            <ng-container *ngFor="let item of listDossierRemind">
                                <a (click)='onClickRemind(item.id, item.type)' class="remindMenu" mat-menu-item>
                                    <span class="remindNumber">{{item.count}}</span>
                                    <span class="remindName" *ngIf="item.name != null">{{item.name}}</span>
                                    <span class="remindName" *ngIf="item.name == null" i18n>(Công việc chưa cấu hình tên)</span>
                                </a>
                            </ng-container>
                        </ng-container>
                        <ng-container *ngIf="listDossierRemind.length === 0">
                            <div mat-menu-item i18n>Không có thông báo nào!</div>
                        </ng-container>
                    </mat-menu>

                    <mat-divider vertical class="v_divider"></mat-divider>

                    <!-- onegateMenu -->
                    <a mat-button [matMenuTriggerFor]="onegateMenu" class="btn_gridMenu" *ngIf="listAppEnable == 1 && listOnegateApps.length > 0">
                        <mat-icon class="material-icons-outlined">widgets</mat-icon>
                    </a>
                    <mat-menu #onegateMenu="matMenu" class="menu_apps">
                        <div class="gridApps">
                            <a class="items" *ngFor="let app of listOnegateApps" matTooltip="{{app.app?.name}}" href="{{app.appDeployment?.configuration?.url}}" target="blank">
                                <div class="appLogo" [style.background-image]="app.app?.logoURL"></div>
                                <span class="appName">
                                    {{app.app?.name}}
                                    <span *ngIf="app.app?.name == undefined || app.app?.name == null || app.app?.name.trim() == ''" i18n>(Không tìm thấy bản dịch)</span>
                                </span>
                            </a>
                        </div>
                    </mat-menu>
                    <mat-divider vertical class="v_divider" *ngIf="listAppEnable == 1 && listOnegateApps.length > 0"></mat-divider>
                    <!-- gridMenu -->
                    <a mat-button [matMenuTriggerFor]="gridMenu" class="btn_gridMenu" *ngIf="listAppEnable == 1 && listApps.length > 0">
                        <mat-icon>apps</mat-icon>
                    </a>
                    <mat-menu #gridMenu="matMenu" class="menu_apps">
                        <div class="gridApps">
                            <a class="items" *ngFor="let app of listApps" matTooltip="{{app.app?.name}}" href="{{app.appDeployment?.configuration?.url}}" target="blank">
                                <div class="appLogo" [style.background-image]="app.app?.logoURL"></div>
                                <span class="appName" *ngIf="app.app.brandname != '' && app.app.brandname != null">
                                    {{app.app?.brandname}}
                                    <!-- <span *ngIf="app.app?.name == undefined || app.app?.name == null || app.app?.name.trim() == ''" i18n>(Không tìm thấy bản dịch)</span> -->
                                </span>
                                <span class="appName" *ngIf="app.app.brandname == '' || app.app.brandname == null">
                                    {{app.app?.name}}
                                    <span *ngIf="app.app?.name == undefined || app.app?.name == null || app.app?.name.trim() == ''" i18n>(Không tìm thấy bản dịch)</span>
                                </span>
                            </a>
                        </div>
                    </mat-menu>

                    <mat-divider vertical class="v_divider" *ngIf="listAppEnable == 1 && listApps.length > 0"></mat-divider>

                    <!-- siteMenu -->
                    <a mat-button [matMenuTriggerFor]="siteMenu" class="btn_gridMenu" *ngIf="listAppEnable == 1 && listApps.length > 0" style="display: none;">
                        <mat-icon>apps</mat-icon>
                    </a>
                    <mat-menu #siteMenu="matMenu" class="menu_sites">
                        <div class="gridApps">
                            <ng-container *ngIf="userAgency.length > 1 && !showTooltipAgencyPositionName && !filterByPositionAgencyType">
                                <a class="items" *ngFor="let ag of userAgency"  (click)='onAgencyChange(ag)' style="font-size: 16px;line-height: 3em; height: unset;">
                                    <span class="main-agency-name">{{ ag.agency.name }}</span>
                                </a>
                            </ng-container>
                            <ng-container *ngIf="userAgency.length > 1 && showTooltipAgencyPositionName && !filterByPositionAgencyType">
                                <a class="items" *ngFor="let ag of userAgency"   matTooltip="{{ ag.agency.name }} ({{ag?.position?.name}})" (click)='onAgencyChange(ag)' style="font-size: 16px;line-height: 3em; height: unset;">
                                    <span class="main-agency-name">{{ ag.agency.name }}</span>
                                </a>
                            </ng-container>
                            <ng-container *ngIf="userAgency.length > 1 && filterByPositionAgencyType">

                                <a class="items" *ngFor="let ag of userAgency" (click)='onAgencyChange(ag)' style="font-size: 16px;line-height: 3em; height: unset;">
                                    <span class="main-agency-name">{{ ag.agency.name }}</span>
                                </a>
                            </ng-container>
                        </div> 
                    </mat-menu>
                    
                    <!-- Account -->
                    <a mat-button class="btn_accountMenu" [matMenuTriggerFor]="accountMenu">
                        <span id="account_name" *ngIf="showTooltipAgencyPositionName" matTooltip="{{userPositionName}}">
                          <div class="avatar" [style.background-image]="avatar">
                            <div class="verified_avatar_icon_toolbar" *ngIf="isClean == true">✓</div>
                          </div>
                          {{displayName}}
                        </span>
                        <span id="account_name" *ngIf="!showTooltipAgencyPositionName">
                          <div class="avatar" [style.background-image]="avatar">
                            <div class="verified_avatar_icon_toolbar" *ngIf="isClean == true">✓</div>
                          </div>
                          {{displayName}}
                        </span>
                    </a>
                    <mat-menu #accountMenu="matMenu">
                        <a *ngIf="isAccountIntegration == false" mat-menu-item target="blank" href="{{config.webAccountURL}}my-account/info">
                            <mat-icon>account_circle</mat-icon><span>Tài khoản</span>
                        </a>
                        <a *ngIf="isAccountIntegration == true" mat-menu-item target="blank" routerLink="/my-account">
                            <mat-icon>account_circle</mat-icon><span>Tài khoản</span>
                        </a>
                        <button mat-menu-item (click)="logout()">
                            <mat-icon>exit_to_app</mat-icon><span>Đăng xuất</span>
                        </button>
                    </mat-menu>
                    <mat-divider vertical class="v_divider"></mat-divider>
                    <a *ngIf="this.onlinkHDSD == 1" mat-menu-item target="blank" href="{{this.editlinkHDSD}}">
                        <mat-icon>library_books</mat-icon><span>Tài liệu HDSD</span>
                    </a>
                    <mat-divider vertical class="v_divider"></mat-divider>
                    <!-- Search -->
                    <form *ngIf="showQuickLookupDossier" class="quick-search-form" (submit)="onSubmitQuickSearch()">
                        <mat-form-field appearance="outline" floatLabel="never">
                            <mat-icon matPrefix>search</mat-icon>
                            <input type="text" matInput placeholder="Nhập số hồ sơ" [formControl]="dossierCode">
                            <div matSuffix class="more-suffix">
                                <mat-divider vertical class="v_divider"></mat-divider>
                                <mat-icon (click)="onSubmitQuickSearch()">more_horiz</mat-icon>
                            </div>
                        </mat-form-field>
                    </form>

                    <!-- Lang -->
                    <button mat-button [matMenuTriggerFor]="typeComboBoxLanguage != 2 ? langMenu : null" align="end" class="btn_langMenu" *ngIf="selectedLang == 'vi' && typeComboBoxLanguage != 3 && !hideLanguageHeader">
                        <img class="iconStatistical" src="{{config.cloudStaticURL}}logo/flag/vi.jpg" id="lang_flag" alt=""
                            height="18em">
                        <span id="lang_acronym">Tiếng Việt</span>
                        <mat-icon>expand_more</mat-icon>
                    </button>
                    <button mat-button [matMenuTriggerFor]="typeComboBoxLanguage != 2 ? langMenu : null" align="end" class="btn_langMenu" *ngIf="selectedLang == 'en' && typeComboBoxLanguage != 3 && !hideLanguageHeader">
                        <img class="iconStatistical" src="{{config.cloudStaticURL}}logo/flag/eng.jpg" id="lang_flag" alt=""
                            height="18em">
                        <span id="lang_acronym">English</span>
                        <mat-icon>expand_more</mat-icon>
                    </button>
                    <mat-menu #langMenu="matMenu">
                        <button mat-menu-item (click)="changeLanguage('vi', 228)" *ngIf="!hideLanguageHeader">
                            <button mat-button disabled>
                                <img class="iconStatistical" src="{{config.cloudStaticURL}}logo/flag/vi.jpg" id="lang_flag" alt=""
                                    height="20em">
                                <span id="lang_acronym">Tiếng Việt (VI)</span>
                            </button>
                        </button>
                        <button mat-menu-item (click)="changeLanguage('en', 46)" *ngIf="!hideLanguageHeader">
                            <button mat-button disabled>
                                <img class="iconStatistical" src="{{config.cloudStaticURL}}logo/flag/eng.jpg" id="lang_flag" alt=""
                                    height="18em">
                                <span id="lang_acronym">English (EN)</span>
                            </button>
                        </button>
                    </mat-menu>
                </mat-toolbar-row>
            </mat-toolbar>
            <div class="content">
                <router-outlet></router-outlet>
            </div>
            <mat-toolbar class="footer" fxShow="true" fxHide.lt-md>
                <div class="copyright">
                    <!-- <mat-icon>copyright</mat-icon> -->
                    <div *ngIf="footerContent.name != undefined" class="itemRight">{{footerContent.name[selectedLang]}}</div>
                    <div *ngIf="footerContent.address != undefined" class="itemRight">{{footerContent.address[selectedLang]}}</div>
                </div>
                <span class="toolbar-spacer"></span>
                <div *ngIf="footerContent?.right?.enable == 1">
                    <div class="developedBy">
                        <span>{{footerContent.right.name[selectedLang]}}</span>
                        <div class="img" [ngStyle]="{'background-image': 'url(' + footerContent.right.logoUrl + ')'}"></div>
                    </div>
                    <div class="developedBy">
                        <span>{{footerContent.right.version[selectedLang]}}</span>
                    </div>
                </div>
                <div *ngIf="viewDowloadApp == true"
                fxShow="true"
                class="developedBy"
                fxLayout.lt-lg='column'
                fxLayout.gt-md='column'
                fxLayoutAlign.gt-md='space-between'
                ngClass.lt-md='m-auto'
                >
                    <!-- <span class="as-center" [ngStyle]="{'text-align':'center'}" >
                        <div class="top-right-container">
                            <span class="image-container">
                              <img [src]="staticCloudUrl + 'img/AppStore.png'" alt="ios" [ngStyle]="{'height': 'inherit'}" class="mr-05 cursor-pointer" (click)="openLink(0)">
                              <img [src]="'assets/img/new.png'" alt="" class="badge" *ngIf="footer?.isAppNew == true">
                            </span>
                          </div>
                    </span>
                    &emsp;
                    <span class="as-center" [ngStyle]="{'text-align':'center'}" >
                        <div class="top-right-container">
                            <span class="image-container">
                                <img [src]="staticCloudUrl + 'img/PlayStore.png'" alt="android" [ngStyle]="{'height': 'inherit'}" class="cursor-pointer" (click)="openLink(1)">
                                <img  [ngStyle]="{ 'width': '2em', 'height': '1em'}" src="assets\img\new.png" *ngIf="footer?.isAppNew == true " id="chatbot_image" alt="" class="badge chatbot-image">
                            </span>
                          </div>
                    </span> -->
                    <span class="as-center" [ngStyle]="{'text-align':'center'}" >
                        <div class="top-right-container">
                          <span class="image-container">
                            <img [src]="staticCloudUrl + 'img/AppStore.png'" alt="iOS" [ngStyle]="{'height': 'inherit'}" class="mr-05 cursor-pointer" (click)="openLink(0)">
                            <img [src]="'assets/img/new.png'" alt="" class="badge" *ngIf="isAppNew == true">
                          </span>
                          <span class="image-container">
                            <img [src]="staticCloudUrl + 'img/PlayStore.png'" alt="Android" [ngStyle]="{'height': 'inherit'}" class="cursor-pointer" (click)="openLink(1)">
                            <img [src]="'assets/img/new.png'" alt="" class="badge chatbot-image" *ngIf="isAppNew == true">
                          </span>
                        </div>
                    </span>
                </div>

                <div *ngIf="isShowNetworkTrust" style="text-align: right">
                    <a href="{{linkIpv6}}" target="_blank">
                        <img src="{{LogoIpv6}}" alt="ipv6 ready" title="ipv6 ready"
                        style="border-width: 0px; border-style: solid;margin-right: 5px;">
                    </a>
                    <a href="{{linkATTT}}" title="Chung nhan Tin Nhiem Mang" target="_blank">
                        <img src="{{LogoATTT}}" width="100px" height="auto" alt="Chung nhan Tin Nhiem Mang"></a>
                </div>
            </mat-toolbar>
        </mat-sidenav-content>
    </mat-sidenav-container>
</div>
