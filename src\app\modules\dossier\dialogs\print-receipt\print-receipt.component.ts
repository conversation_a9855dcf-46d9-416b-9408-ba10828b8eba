import { Component, OnInit, ChangeDetectorRef, AfterViewInit, On<PERSON>estroy } from '@angular/core';
import { FormGroup, FormControl, Validators } from '@angular/forms';
import { EnvService } from 'src/app/core/service/env.service';
import { SnackbarService } from 'src/app/data/service/snackbar/snackbar.service';
import { KeycloakService } from 'keycloak-angular';
import { ApiProviderService } from 'src/app/core/service/api-provider.service';
import { BasedataService } from 'src/app/data/service/basedata/basedata.service';
import { BasepadService } from 'src/app/data/service/basepad/basepad.service';
import { ReplaySubject, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { ActivatedRoute, Router } from '@angular/router';
import { DeploymentService } from 'src/app/data/service/deployment.service';
import { FormElement } from 'src/app/data/schema/form-element';
import { MatTableDataSource } from '@angular/material/table';
import { BasecatService } from 'src/app/data/service/basecat/basecat.service';
import { MatDialog, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { Inject } from '@angular/core'; 
import { DossierService } from 'src/app/data/service/dossier/dossier.service';
import { ProcedureService } from 'src/app/data/service/procedure/procedure.service';
import { SelectionModel } from '@angular/cdk/collections';
import * as tUtils from 'src/app/data/service/thoai.service';
import { ConfigService } from 'src/app/data/service/dossier/config.service';
import { AgencyService } from 'src/app/data/service/basedata/agency.service';
import { PrintTemplateComponent, ConfirmPrintTemplateDialogModel } from 'src/app/shared/components/print-template/print-template.component';
import { DigitalSignatureService } from 'src/app/data/service/digital-signature/digital-signature.service';
import { ProcedureReportService } from 'src/app/data/service/procedure-report/procedure-report.service';
import jwt_decode from 'jwt-decode';
import { ReportService } from 'src/app/data/service/report/report.service';
export interface Element {
  code: String;
  typeFee: String;
  total: Number;
}

const Agency: Element[] = [];

@Component({
  selector: 'app-print-receipt',
  templateUrl: './print-receipt.component.html',
  styleUrls: ['./print-receipt.component.scss']
})
export class PrintReceiptComponent implements OnInit, AfterViewInit, OnDestroy  {
  countResult1 = 0;
  formTemp;
  countResult = 0;
  displaySyntaxId:any
  digitalSignatureEnable: boolean = false;
  digitalSignature = {
    SmartCA:false,
    VGCA:false,
    VNPTCA:false,
    VNPTSim:false,
    NEAC:false
  }
  // configDepartmentTagId = this.deploymentService.env.OS_HCM.configDepartmentTagId;
  config = this.envService.getConfig();
  selectedLang: string;
  accountId: any;
  dossier: any;
  isFieldArrayInvalid = false;
  languageIdUsed = [228];
  listLanguage = [
    {
      languageId: 228,
      name: 'Tiếng Việt'
    },
    {
      languageId: 46,
      name: 'English'
    }
  ];
  newAttribute: any = {
    languageId: 228,
    name: '',
    listLanguageItem: JSON.parse(JSON.stringify(this.listLanguage))
  };
  newItemfield: any = {
   code: ''
  }
  fieldArray: Array<any> = [this.newItemfield];
  status: Array<any> = [
    {
      status: 0,
      name: 'Đóng'
    },
    {
      status: 1,
      name: 'Mở'
    }
  ];
  statusVi: Array<any> = [
    {
      status: 0,
      name: 'Đóng'
    },
    {
      status: 1,
      name: 'Mở'
    }
  ];
  statusEn: Array<any> = [
    {
      status: 0,
      name: 'Close'
    },
    {
      status: 1,
      name: 'Open'
    }
  ];
  nameDefault = '';
  addForm = new FormGroup({
    loaiQuyetDinh: new FormControl(''),
    displaySyntax: new FormControl(''),
   
  });
  saveForm= new FormGroup({

  });
  
  generatedNumber:any;
  Id:any;
  isSubmit = false;
  searchSectorKeyword = '';
  listSectorPage = 0;
  // listGuide = [];
  protected onDestroy = new Subject<void>();
  ELEMENTDATA: FormElement[] = [];
  ELEMENTDATA3: any[] = [];
  dataSource: MatTableDataSource<FormElement>;
  ELEMENTDATA1: FormElement[] = [];
  dataSource1: MatTableDataSource<FormElement>;

  // ------
  receiptDisplayedColumns: string[] = ['codeReceipt', 'procostType',  'amount','action'];
  ELEMENTDATA2: any[] = [];
  receiptDataSource: MatTableDataSource<any>;
  dossierFee: any;
  procedureId = '';
  otherUnit = false;
  totalCost = '';
  totalRemaining = '';
  remaining = 0;
  hideTotal = false;
  total = 0;
  selection = new SelectionModel<any>(true, []);
  mainUnit = '';
  listProduct = [];
  disableTagSelectBox = false;
  onlyPaidFee = this.deploymentService.env?.OS_HCM?.onlyPaidFee ? this.deploymentService.env?.OS_HCM?.onlyPaidFee : false; 
  listConfigTemplate = [];
  dossierDetail = [];
  rootAgency = this.agencyService.getAgency(false);
  currentAgencyId : string;
  isEditSignTokenName = false;
  env = this.deploymentService.getAppDeployment()?.env;
  enablePrintBillNew = this.deploymentService.getAppDeployment()?.enablePrintBillNew ? this.deploymentService.getAppDeployment().enablePrintBillNew : true;
  taskId: string;
  attachFilePreview = [];
  resultFilePreview = [];
  listSamplePrint=[];
  constructor(
    private dialog: MatDialog,
    public dialogRef: MatDialogRef<PrintReceiptComponent>,
    @Inject(MAT_DIALOG_DATA) public data: ConfirmPrintReceiptDialogModel,
    private envService: EnvService,
    private snackbarService: SnackbarService,
    private keycloakService: KeycloakService,
    private basedataService: BasedataService,
    private basepadService: BasepadService,
    private basecatService: BasecatService,
    private dossierService: DossierService,
    private procedureService: ProcedureService,
    private deploymentService: DeploymentService,
    private configService: ConfigService,
    private agencyService: AgencyService,
    private  apiProviderService: ApiProviderService,
    private procedureReportService: ProcedureReportService,
    private activeRoute: ActivatedRoute,
    private reportSerivce: ReportService
  ) { 
    this.dataSource = new MatTableDataSource(this.ELEMENTDATA);
    this.dataSource1 = new MatTableDataSource(this.ELEMENTDATA1);
    this.Id=data.id;
    this.dossier=data.dossier;
    this.receiptDataSource = new MatTableDataSource(this.ELEMENTDATA3);
    this.procedureId = data.procedureId;
    this.listSamplePrint = data.listSamplePrint;
  }

  async ngOnInit(): Promise<void> {
    this.selectedLang = localStorage.getItem('language');
    this.nameDefault = '(Không tìm thấy bản dịch)';
    if (Number(localStorage.getItem('languageId')) === 46) {
      this.status = this.statusEn;
      this.nameDefault = '(No translation found)';
    }
    
    // this.getTagName();
    // this.getDisplaySyntax();
    // this.getDossierFee();
    console.log('init print receipt');
    this.getListReceiptPaper();
    
    this.getDossierDetail();
    
      // const msgObj = {
      //   vi: 'Vui lòng cấp mã số biên lai cho hồ sơ này !',
      //   en: 'Please provide a receipt number for this dossier !'
      // };

      // this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'success_notification', this.config.expiredTime);
  }
  

  getListSampleReceipt(){
    let search= '?type-id='+ this.deploymentService.env.OS_HCM.enablePrintReceiptPaper.idTagSampleReceiptPaper + '&sort=createdDate,desc';
    this.reportSerivce.getListReportById(search).subscribe(data =>{
      this.listConfigTemplate = data.content;
      console.log('this.listSampleReceiptPaper',this.listConfigTemplate);
    })
  }

  checkDigitalSignatureSupported(dsType:string): boolean{
   
    // Check config
    if(dsType.toLowerCase() == 'smartca' && this.env?.digitalSignature?.SmartCA != 1){
      return false;
    }else if(dsType.toLowerCase() == 'vgca' && this.env?.digitalSignature?.VGCA != 1){
      return false
    }else if(dsType.toLowerCase() == 'vnptca' && this.env?.digitalSignature?.VNPTCA != 1){
      return false
    }else if(dsType.toLowerCase() == 'vnptsim' && this.env?.digitalSignature?.VNPTSim != 1){
      return false
    }else if(dsType.toLowerCase() == 'neac' && this.env?.digitalSignature?.NEAC != 1){
      return false
    }

    if(this.dossierDetail[0].currentTask?.length > 0){
      for (const task of this.dossierDetail[0].currentTask) {
        if(this.taskId == task.id){
          if(dsType.toLowerCase() == 'smartca' && task.bpmProcessDefinitionTask?.dynamicVariable?.canUseDigitalSignSmartCA == 1){
            return true;
          }else if(dsType.toLowerCase() == 'vgca' && task.bpmProcessDefinitionTask?.dynamicVariable?.canUseDigitalSignVGCA == 1){
            return true;
          }else if(dsType.toLowerCase() == 'vnptca' && task.bpmProcessDefinitionTask?.dynamicVariable?.canUseDigitalSignVNPTCA == 1){
            return true;
          }else if(dsType.toLowerCase() == 'vnptsim' && task.bpmProcessDefinitionTask?.variable?.canUseDigitalSign == 1){
            return true;
          }else if(dsType.toLowerCase() == 'neac' && task.bpmProcessDefinitionTask?.dynamicVariable?.canUseDigitalSignNEAC == 1){
            return true;
          }
          return false;
        }
      }
    }
    return false;
  }

  hasDigitalSignaturePermission(): boolean{
    // Check permission
    const token = localStorage.getItem('userToken');
    const decodedToken: any = jwt_decode(token);
    for (const iterator of decodedToken.permissions) {
      if(iterator.permission.code == 'integrateDigitalSignature'){
        return true;
      }
    }
    return false;
  }

   getDossierDetail() {
    return new Promise<void>((resolve, reject) => {
      this.dossierService.getDossierDetail(this.Id).subscribe(async data => {
        if (this.activeRoute.snapshot.queryParamMap.get('task') != null) {
          this.taskId = this.activeRoute.snapshot.queryParamMap.get('task');
        }
        this.dossierDetail = [];
        this.currentAgencyId = data.agency.id;
        this.dossierDetail.push(data);
        this.getListConfigTemplate(this.rootAgency.rootAgencyId ?? data.agency.id);
        if(this.hasDigitalSignaturePermission()){
          this.digitalSignatureEnable = true;
          this.digitalSignature = {
            SmartCA:false,
            VGCA:false,
            VNPTCA:false,
            VNPTSim:false,
            NEAC:false
          };
          this.digitalSignature.SmartCA = this.checkDigitalSignatureSupported('smartca');
          this.digitalSignature.VGCA = this.checkDigitalSignatureSupported('vgca');
          this.digitalSignature.VNPTCA = this.checkDigitalSignatureSupported('vnptca');
          this.digitalSignature.VNPTSim = this.checkDigitalSignatureSupported('vnptsim');
          this.digitalSignature.NEAC = this.checkDigitalSignatureSupported('neac');
          this.isEditSignTokenName = this.env?.OS_KTM?.isEditSignTokenName == true ? true : false ;
        }
      })})}

      async getListConfigTemplate(agencyId) {
        // const arrayList = [];
        // const searchString = '?procedure-id=' + this.procedureId + '&agency-id=' + agencyId;
        // this.procedureService.getListConfigTemplate(searchString).subscribe(async data => {
        //   for await (const iterator of data) {
        //     if (iterator.location === 0 || iterator.location === 1 || iterator.location === null || typeof iterator.location === 'undefined') {
        //       const detail = await this.getDetailTemplate(iterator.id);
        //       if (!!detail) {
        //         const itemList = [];
        //         if (detail.fileSign) {
        //           for await (const value of detail.fileSign) {
        //             itemList.push({
        //               fileSignId: value.fileSignId,
        //               fileSignName: value.fileSignName,
        //               check: false
        //             });
        //           }
        //         }
        //         const item = { fileSign: itemList };
        //         const obj = Object.assign({}, iterator, item);
        //         arrayList.push(obj);
        //       }
        //     }
        //   }
        //   this.listConfigTemplate = arrayList;
        // });
        if(this.listSamplePrint.length > 0){
          const hasSignedFile = this.dossierDetail[0]?.signedTemplateFile?.length > 0;
          const arrayList = [];
          for await (const iterator of this.listSamplePrint) {
            if (iterator.location === 0 || iterator.location === 1 || iterator.location === null || typeof iterator.location === 'undefined') {
              const detail = await this.getDetailTemplate(iterator.id);
              if (!!detail) {
                const itemList = [];
                if(hasSignedFile){
                  for (const template of this.dossierDetail[0].signedTemplateFile) {
                    if(detail.id == template.id){
                      for await (const value of template.fileSign) {
                        itemList.push({
                          fileSignId: value.id,
                          fileSignName: value.fileSignName,
                          check: false
                        });
                      }
                    }
                  }
                }
                const item = { fileSign: itemList };
                const obj = Object.assign({}, iterator, item);
                arrayList.push(obj);
              }
            }
          }
          this.listConfigTemplate = this.listSamplePrint;
        } else {
          let search= '?type-id='+ this.deploymentService.env.OS_HCM.enablePrintReceiptPaper.idTagSampleReceiptPaper + '&sort=createdDate,desc';
          this.reportSerivce.getListReportById(search).subscribe(data =>{
            this.listConfigTemplate = data.content;
            console.log('this.listSampleReceiptPaper',this.listSamplePrint);
          })
        this.listConfigTemplate = this.listSamplePrint;
      }
      }

  getDetailTemplate(id) {
    return new Promise<any>(resolve => {
      this.configService.getDetailTemplate(id).subscribe(detail => {
        resolve(detail);
      }, err => {
        resolve(null);
      });
    });
  }

  async getListReceiptPaper(){
    console.log(this.Id);
    console.log(this.procedureId);
    console.log(this.listSamplePrint);
    console.log('tesst ');
    if(!!this.dossier.extendHCM.procedureAdministrationWithReceiptNumberCode){
      this.dossier.extendHCM.procedureAdministrationWithReceiptNumberCode.forEach(element => {
        let info={
          code: '',
          total: 0,
          typeFee:''
        }
        info.code= element.code;
       info.total= element?.receipt?.total ?  element?.receipt?.total : 0;
        let listType=[];

        //  let fee=0;
        if(tUtils.nonNull(element,'receipt')){
          for(let i=0 ; i< element.receipt.product.length ; i ++){
            listType.push(element.receipt.product[i].prodName);
          }
        }
        // element?.receipt?.proceduct.forEach(ele=> {
        //   listType.push(ele.prodName);
        //  });
         info.typeFee= listType.toString();
        //  element.fee = fee;
         this.ELEMENTDATA3.push(info);
      });
     this.receiptDataSource.data =  this.ELEMENTDATA3;
     console.log('this.dataSource.data check', this.ELEMENTDATA3, this.receiptDataSource.data);
    }
  }

  ngOnDestroy() {
    this.onDestroy.next();
    this.onDestroy.complete();
  }
  
  ngAfterViewInit() {
    setTimeout(() => {
      // console.clear();
    }, 2000);
  }

  onDismiss() {
    this.dialogRef.close();
  }

  createPrintBill(code, reportType, item?) {
    let url = '';
    this.keycloakService.getToken().then(token => {
      let data = {};
      if(!!this.enablePrintBillNew){
        data = {
          "report": reportType,
          "apiGateway": this.apiProviderService.getUrl('digo', 'padman'),
          "dossierId": this.Id,
          "receiptCode": code,
        };
      }else{
      url = this.config.birtviewerURL
        + 'output?__report=' + reportType
        + '&&displayNone=true&__dpi=96&__format=html&__pageoverflow=0&__overwrite=false'
        + '&token=' + token
        + '&apiGateway=' + this.apiProviderService.getUrl('digo', 'padman')
        + '&dossierId=' + this.Id
        + '&receiptCode=' + code;
      }
      //this.procedureReportService.getReportTemplate(url).subscribe(data => {
      //}, err => {
        const dialogData = new ConfirmPrintTemplateDialogModel(url, item, this.Id, this.config.dossierAttachedFileTagId, this.digitalSignature, null, false, this.enablePrintBillNew, data);
        const dialogRef = this.dialog.open(PrintTemplateComponent, {
          minWidth: '55vw',
          maxHeight: '90vh',
          // maxHeight: '90vh',
          data: dialogData,
          autoFocus: false
        });
        dialogRef.afterClosed().subscribe(dialogResult => {
          if(dialogResult == true){
            this.getListConfigTemplate(this.rootAgency?.rootAgencyId ?? this.currentAgencyId);
          }
          setTimeout(() => {
            if (localStorage.getItem('checkVerifiled')) {
              if (localStorage.getItem('checkVerifiled') === 'true') {
                this.getDossierAttachment(true);
              } else {
                this.getDossierAttachment(false);
              }
            }
          }, 1000);
        });
      //});
    });
  }

  getDossierAttachment(dialogResult) {
    this.dossierService.getDossierDetail(this.Id).subscribe(async data => {
      let count = 0;
      if (data.attachment) {
        this.attachFilePreview = [];
        this.resultFilePreview = [];
        const asyncFunction = new Promise<void>((resolve, reject) => {
          data.attachment.forEach(async att => {
            if (att.group === this.config.dossierAttachedFileTagId) {
              this.attachFilePreview.push({
                id: att.id,
                name: att.filename,
                size: att.size,
                icon: this.getFileIcon(att.filename.split('.').pop())
              });
            }
            if (att.group === this.config.dossierResultFileTagId) {
              this.resultFilePreview.push({
                id: att.id,
                name: att.filename,
                size: att.size,
                icon: this.getFileIcon(att.filename.split('.').pop())
              });
            }

            count++;
            if (count === data.attachment.length) { resolve(); }
          });
        });
        asyncFunction.then(() => {
          if (dialogResult === true) {
            const msgObj = {
              vi: 'Ký số thành công!',
              en: 'Successfully signed!'
            };
            this.snackbarService.openSnackBar(1, msgObj[this.selectedLang], '', 'success_notification', this.config.expiredTime);
            this.getDossierDetail();
          } else if (dialogResult === false) {
            const msgObj = {
              vi: 'Ký số thất bại!',
              en: 'Unsuccessful signed!'
            };
            this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
          }
        });
      } else {
        this.attachFilePreview = [];
      }
    });
    localStorage.removeItem('checkVerifiled');
  }

  getFileIcon(ext) {
    return this.config.cloudStaticURL + 'icon/files/512x512/' + ext + '.png';
  }
 
 

 
 
}

export class ConfirmPrintReceiptDialogModel {
  constructor(
    public id: string,
    public procedureId: string,
    public listSamplePrint: any[],
    public dossier: any
  ) { }
}
