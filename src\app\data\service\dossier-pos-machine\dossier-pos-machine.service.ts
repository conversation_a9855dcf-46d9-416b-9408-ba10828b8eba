import { Injectable } from '@angular/core';
import {HttpClient, HttpHeaders} from '@angular/common/http';
import {ApiProviderService} from 'core/service/api-provider.service';
import {Observable} from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class DossierPosMachineService {
  constructor(
    private http: HttpClient,
    private apiProviderService: ApiProviderService
  ) { }
  private padman = this.apiProviderService.getUrl('digo', 'padman');
  //private padman = 'http://localhost:8081';
  private postMachinePath = this.padman + '/dossier-pos-machine';
  search(url: string): Observable<any>{
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.postMachinePath + '?' + url, { headers });
  }

  getInfo(id: string): Observable<any>{
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.postMachinePath + '/' + id, { headers });
  }

  update(id, body: any): Observable<any>{
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.put(this.postMachinePath + '/' + id , body, { headers });
  }

  addNew(body: any): Observable<any>{
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.set('Content-Type', 'application/json');
    return this.http.post(this.postMachinePath , body, { headers });
  }

  delete(id: any): Observable<any>{
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.delete(this.postMachinePath + '/' + id , { headers });
  }

  putPaidPosMachineDossierFee(body): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.set('Content-Type', 'application/json');
    return this.http.put(this.padman + '/dossier-fee/--pay-pos-machine-by-id', body, { headers });
  }
}
