.frm_main .searchForm .btn_add {
    background-color: #e8e8e8;
    color: #666;
    float: right;
    height: 3.2em !important;
  }

.frm_main .space {
float: right;
}

.search-nested {
    width: 80% !important;
    height: 50px !important;
    margin-left: 15px !important;
}

.clear-search-nested {
    right: 0 !important;
    position: absolute !important;
    top: 0 !important;
}

::ng-deep .syncStringCode .mat-form-field-appearance-outline .mat-form-field-wrapper {
    margin-top: 0 !important;
    margin-bottom: 0 !important;
}

.frm_main .searchForm .btn_add .mat-icon {
color: #ce7a58;
}

.syncListDossier .mat-header-row .mat-header-cell {
    margin: 5px;
}

.syncListDossier  .mat-cell {
    margin: 5px;
}
::ng-deep {
    .mat-tooltip {
        font-size: 13px;
    }
}

::ng-deep .pull-right {
    margin-left: auto;
    margin-top: 1em;
    margin-right: 10px;
}

::ng-deep .prc_searchbar .searchForm .mat-form-field-appearance-outline .mat-form-field-outline {
    color: transparent;
}

::ng-deep .prc_searchbar .searchForm .mat-form-field-appearance-outline .mat-form-field-outline-thick {
    color: #dddddd;
}

::ng-deep .prc_searchbar .searchForm .mat-form-field-appearance-outline .mat-form-field-outline {
    background-color: #eaebeb;
    border-radius: 5px;
}

::ng-deep .prc_searchbar .searchForm .searchBtn {
    margin-top: 0.3em;
    background-color: #ce7a58;
    color: #fff;
    height: 3.2em !important;
}

::ng-deep .prc_searchbar .searchForm .formFieldItems {
    flex-wrap: wrap;
}

::ng-deep .searchForm .advanced-box .panel {
    background-color: #f9f9f9;
    box-shadow: none;
    display: flex;
    flex-wrap: wrap;
}

::ng-deep .advanced-button .advanced-expand-icon {
    font-size: 16px;
}

::ng-deep .searchForm .advanced-button {
    margin: auto;
    color: #ce7a58;
}

::ng-deep .mat-checkbox-frame {
    border-radius: 4px;
}

::ng-deep .mat-expansion-panel-body {
    padding: 5px 0 0 0 !important;
}

::ng-deep .prc_searchbar .mat-form-field-appearance-outline .mat-form-field-infix {
    padding: 0.8em 0;
}

::ng-deep .prc_searchbar .mat-form-field-label-wrapper {
    top: -1em;
}

::ng-deep .prc_searchbar .mat-form-field.mat-focused .mat-form-field-label {
    color: #ce7a58;
    font-size: 14px;
}

::ng-deep .prc_searchbar .mat-form-field-appearance-outline.mat-form-field-can-float.mat-form-field-should-float .mat-form-field-label {
    color: #ce7a58;
    transform: translateY(-1.55em) scale(1);
    margin-bottom: 1em;
}

// ================================= searchtbl
.frm_main {
    margin-top: 1em;
    background-color: #fff;
    box-shadow: 4px 0px 8px rgba(0, 0, 0, 0.1);
    padding: 1em 1em 1em 1em;
    .btn-claim {
        background-color: #e8e8e8;
        color: #666666;
        height: 3.2em !important;
        margin-top: 4px;
    }
    .mat-stroked-button {
        border: none !important;
    }
    .primary-btn {
        background-color: #ce7a58;
        color: #fff;
        height: 2.8em;
    }
}

::ng-deep .frm_main .btn-claim {
    background-color: #e8e8e8;
    color: #666666;
    height: 3.2em !important;
    margin-top: 4px;
}

::ng-deep .cbx-print-report {
    margin-left: auto;
    line-height: 1em;
}

::ng-deep .cbx-print-report .icon {
    color: #666;
}

::ng-deep .cbx-print-report .mat-form-field-wrapper {
    padding-bottom: 0px;
    margin-top: 0px;
}

::ng-deep .frm_main .mat-stroked-button {
    border: none !important;
}

.top-control {
    padding-bottom: 0.8em;
    .btn_ctrl {
        background-color: #e8e8e8;
        color: #666;
        float: right;
        .mat-icon:nth-child(1) {
            color: #ce7a58;
            margin-right: 0.2em;
        }
        .mat-icon:nth-child(2) {
            margin-left: 0.2em;
        }
    }
}

::ng-deep .frm_main .mat-form-field-appearance-outline .mat-form-field-infix {
    padding: 0.8em 0;
}

::ng-deep .frm_main .mat-form-field-label-wrapper {
    top: -1em;
}

::ng-deep .frm_main .mat-form-field.mat-focused .mat-form-field-label {
    color: #ce7a58;
    font-size: 14px;
}

::ng-deep .frm_main .mat-form-field-appearance-outline.mat-form-field-can-float.mat-form-field-should-float .mat-form-field-label {
    color: #ce7a58;
    transform: translateY(-1.55em) scale(1);
    margin-bottom: 1em;
}

::ng-deep .frm_main .mat-form-field-appearance-outline .mat-form-field-outline {
    color: transparent;
}

::ng-deep .frm_main .mat-form-field-appearance-outline .mat-form-field-outline-thick {
    color: #dddddd;
}

::ng-deep .frm_main .mat-form-field-appearance-outline .mat-form-field-outline {
    background-color: #eaebeb;
    border-radius: 5px;
}

::ng-deep .mat-checkbox-checked.mat-accent .mat-checkbox-background {
    background-color: #ce7a58 !important;
}

.searchtbl table {
    border-radius: 4px;
    border: 1px solid #ececec;
    width: 100%;
}

.searchtbl .checkbox {
    border-radius: 4px;
}

::ng-deep .mat-menu-item .mat-icon {
    margin-right: 16px;
    vertical-align: middle;
    color: #ce7a58;
}

::ng-deep .searchtbl .mat-header-row {
    background-color: #e8e8e8;
    min-height: 3.5em !important;
}

::ng-deep .searchtbl .mat-header-row .mat-header-cell {
    color: #495057;
    font-size: 14px;
    // display: grid;
}

::ng-deep .searchtbl .mat-header-row .mat-header-cell p {
    margin-bottom: 0;
    font-weight: 400;
    font-style: italic;
}

::ng-deep .searchtbl .mat-column-stt {
    flex: 0 0 5%;
}

::ng-deep .searchtbl .mat-column-code {
    flex: 0 0 10%;
    padding-right: 0.5em;
}

::ng-deep .searchtbl .mat-column-select {
    flex: 0 0 5%;
}

::ng-deep .searchtbl .mat-column-procedureName {
    flex: 1 0 5%;
    padding: 0 1em 0 1em;
    .procedureName {
        display: -webkit-box;
        -webkit-line-clamp: 4;
        -webkit-box-orient: vertical;
        width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
    }
}

::ng-deep .searchtbl .mat-column-applicantName {
    flex: 0 0 15%;
    padding-right: 0.5em;
}

::ng-deep .searchtbl .mat-column-agency {
    flex: 0 0 10%;
    padding-right: 0.5em;
}

::ng-deep .searchtbl .mat-column-agencyPerform {
  flex: 0 0 10%;
  padding-right: 0.5em;
}

::ng-deep .searchtbl .mat-column-status {
    flex: 0 0 10%;
    padding-right: 0.5em;
}

::ng-deep .searchtbl .mat-column-action {
    flex: 0 0 5%;
    float: right;
}

::ng-deep .searchtbl .mat-column-processingTime {
    flex: 0 0 17%;
    padding-right: 0.5em;
    padding-left: 0.5em;
    ul {
        padding: 1em;
        .due {
            letter-spacing: 0;
            font-weight: 500;
            color: #3b9cff;
            margin-left: -1em;
            .overdue {
                color: #f44336;
            }
        }
    }
}

::ng-deep .searchtbl .btn_downloadForm {
    padding: 0;
    color: #ce7a58;
}

::ng-deep .searchtbl .cell_code {
    color: #ce7a58;
    font-weight: 500;
    text-decoration: none;
    a {
        cursor: pointer;
    }
}

::ng-deep .searchtbl .btn_downloadForm .mat-button-wrapper {
    display: flex;
}

::ng-deep .searchtbl .btn_downloadForm {
    padding: 0;
    color: #ce7a58;
}

::ng-deep .searchtbl .btn_downloadForm .mat-button-wrapper {
    display: flex;
}

::ng-deep .searchtbl .btn_downloadForm .mat-button-wrapper .download_icon .mat-icon {
    vertical-align: middle;
    margin-right: 0.2em;
    background-color: #ce7a58;
    color: #fff;
    border-radius: 50%;
    padding: 0.2em;
    transform: scale(0.8);
}

::ng-deep .searchtbl .btn_downloadForm .mat-button-wrapper span {
    align-self: center;
}

::ng-deep .searchtbl .mat-row {
    border: none;
}

::ng-deep .searchtbl .mat-row:nth-child(even) {
    background-color: #fafafa;
}

::ng-deep .searchtbl .mat-row:nth-child(odd) {
    background-color: #fff;
}

::ng-deep .menuAction {
    font-weight: 500;
}

@media screen and (max-width: 600px) {
    .searchtbl .mat-header-row {
        display: none;
    }
    .searchtbl .mat-table {
        border: 0;
        vertical-align: middle;
    }
    .searchtbl .mat-table caption {
        font-size: 1em;
    }
    .searchtbl .mat-table .mat-row {
        border-bottom: 5px solid #ddd;
        display: block;
        min-height: unset;
    }
    .searchtbl .mat-table .mat-cell {
        border-bottom: 1px solid #ddd;
        display: block;
        font-size: 14px;
        text-align: right;
        margin-bottom: 4%;
        padding: 0 0.5em;
    }
    .searchtbl .mat-table .mat-cell:before {
        content: attr(data-label);
        float: left;
        font-weight: 500;
        font-size: 14px;
    }
    .searchtbl .mat-table .mat-cell:last-child {
        border-bottom: 0;
    }
    .searchtbl .mat-table .mat-cell:first-child {
        margin-top: 4%;
    }
     ::ng-deep .searchtbl .mat-column-status {
        float: unset;
    }
     ::ng-deep .searchtbl .mat-column-action {
        float: unset;
    }
     ::ng-deep .searchtbl .mat-row:nth-child(even) {
        background-color: unset;
    }
     ::ng-deep .searchtbl .mat-row:nth-child(odd) {
        background-color: unset;
    }
}

.cell_code_online {
    color: #ce7a58;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    max-width: 100%;
    word-break: break-all;
}

.cell_code_direct {
    color: blue;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    max-width: 100%;
    word-break: break-all;
}

.cell_code_other {
    color: green;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    max-width: 100%;
    word-break: break-all;
}

.search {
    h2 {
        margin: 0 0 0 0;
    }
}

.title-reminder {
    cursor: pointer;
    // padding-left: 0 !important;
    .title {
        font: 500 18px/25px Roboto, "Helvetica Neue", sans-serif;
    }
    .content {
        // padding-left: 0.5rem;
        width: 100%;
        vertical-align: middle;
        align-items: center !important;
    }
    .content:hover {
        border-radius: 10px;
        background-color: #f1f1f1;
    }
    .count-task {
        color: red;
    }
}

.menu_reminder {
    padding-right: 1rem;
    //overflow-y: auto;
    .panel {
        overflow-y: auto;
        span {
            height: 100%;
        }
        .active {
            color: #ce7a58;
            background-color: #f4eadf;
        }
        #submenu {
            margin: 0 0 0 0;
            width: 100%;
            text-align: left;
            padding: 0.2em 1em;
            .submenuTitle {
                //   display: block;
                //   width: 100%;
                line-height: 20px;
                white-space: pre-wrap;
                padding: 0.5em 0 0.5em 0.5em;
            }
            .count {
                min-width: 20%;
                background-color: #db3700 !important;
                padding: 2px 10px !important;
                border-radius: 15px !important;
                color: white !important;
                margin-right: 0.3em;
            }
        }
        #submenu:focus {
            background-color: 'blue';
        }
    }
}

::ng-deep .menu_reminder .mat-expansion-panel-body {
    padding: 5px 0 5px 0 !important;
}

::-webkit-scrollbar-track {
    box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.1);
    background-color: #F5F5F5;
    border-radius: 10px;
}

::-webkit-scrollbar {
    width: 10px;
    background-color: #F5F5F5;
}

::-webkit-scrollbar-thumb {
    border-radius: 10px;
    background-color: #FFF;
    background-image: -webkit-gradient(linear, 40% 0%, 75% 84%, from(#ce7a58), to(#ce7a58), color-stop(.6, #ce7a58))
}
