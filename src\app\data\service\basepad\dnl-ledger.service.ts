import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { ApiProviderService } from 'src/app/core/service/api-provider.service';
import { EnvService } from 'src/app/core/service/env.service';

@Injectable({
    providedIn: 'root'
})
export class DNLLedgerService {

    constructor(
        private http: HttpClient,
        private envService: EnvService,
        private apiProviderService: ApiProviderService,
    ) { }

    private procedureSearchUrl = this.apiProviderService.getUrl('digo', 'basepad') + '/procedure/--search';
    private searchUrls = this.apiProviderService.getUrl('digo', 'basepad') + '/dnl/ledger/--search';
    private postUrls = this.apiProviderService.getUrl('digo', 'basepad') + '/dnl/ledger';

    // linhtm.dni added
    private availableProceduresForLedgerDefinitionUrl = this.apiProviderService.getUrl('digo', 'basepad') + '/procedure/--dnl-available-procedures';
    private assignLedgerNumberToDocumentUrl = this.apiProviderService.getUrl('digo', 'basepad') + '/dnl/ledger/--document-number';
    
    
    private getDetailsUrls(id: string){
        return this.apiProviderService.getUrl('digo', 'basepad') + `/dnl/ledger/${id}`;
    }
    private getUpdateUrls(id: string){
        return this.apiProviderService.getUrl('digo', 'basepad') + `/dnl/ledger/${id}`;
    }

    private getArchiveUrls(id: string){
        return this.apiProviderService.getUrl('digo', 'basepad') + `/dnl/ledger/${id}/--archive`;
    }

    search(query): Observable<any> {
        const endpoint = this.searchUrls + (!!query ? '?' + query : '');
        return this.http.get(endpoint);
    }

    post(body: any): Observable<any>{
        let headers = new HttpHeaders();
        headers = headers.set('Content-Type', 'application/json');
        return this.http.post<any>(this.postUrls, body, { headers });
    }

    update(id: string, body: any): Observable<any>{
        let headers = new HttpHeaders();
        headers = headers.set('Content-Type', 'application/json');
        return this.http.put<any>(this.getUpdateUrls(id), body, { headers });
    }

    details(id: string): Observable<any>{
        return this.http.get(this.getDetailsUrls(id));
    }

    getProcedureByAgency(agencyId: string, page: number) : Observable<any>{ 
        const endpoint = this.procedureSearchUrl + (`?agency-id=${agencyId}&page=${page}&size=10&spec=page`);
        return this.http.get(endpoint);
    }

    archive(id: string): Observable<any>{
        let headers = new HttpHeaders();
        headers = headers.set('Content-Type', 'application/json');
        return this.http.post<any>(this.getArchiveUrls(id), null, { headers });
    }

    // linhtm.dni added: get all available procedures for a ledger definition
    getAllAvailableProceduresForLedgerDefinition(agencyId: string, ledgerDefinitionId: string) : Observable<any>{ 
        const endpoint = this.availableProceduresForLedgerDefinitionUrl + (`?agency-id=${agencyId}&definition-id=${ledgerDefinitionId}`);
        return this.http.get(endpoint);
    }

    assignLedgerNumberToDocument(procedureId: string, documentId: string) : Observable<any>{ 
        const endpoint = this.assignLedgerNumberToDocumentUrl + (`?procedure-id=${procedureId}&document-id=${documentId}`);
        return this.http.put(endpoint, null);
    }

    getDocumentNumberStatus(procedureId: string, documentId: string) : Observable<any>{ 
        const endpoint = this.assignLedgerNumberToDocumentUrl + (`?procedure-id=${procedureId}&document-id=${documentId}`);
        return this.http.get(endpoint);
    }
}
