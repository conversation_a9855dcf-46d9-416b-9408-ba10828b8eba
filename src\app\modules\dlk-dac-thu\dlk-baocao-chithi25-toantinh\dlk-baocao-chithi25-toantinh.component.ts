import {
  Component,
  OnInit,
  ChangeDetectorRef,
  AfterViewInit,
  ViewChild,
  OnDestroy,
} from '@angular/core';
import { FormGroup, FormControl } from '@angular/forms';
import { Router } from '@angular/router';
import { EnvService } from 'src/app/core/service/env.service';
import { MatTableDataSource } from '@angular/material/table';
import { DatePipe } from '@angular/common';
import { ExportExcelService } from 'src/app/data/service/export-excel/export-excel.service';
import { MainService } from 'src/app/data/service/main/main.service';
import { ProcedureService } from 'src/app/data/service/procedure/procedure.service';
import { takeUntil } from 'rxjs/operators';
import { ReplaySubject, Subject } from 'rxjs';              
import { ActivatedRoute } from '@angular/router';
import { DossierService } from 'src/app/data/service/dossier/dossier.service';
import { NgxPrinterService } from 'ngx-printer';
import { SnackbarService } from 'src/app/data/service/snackbar/snackbar.service';
import { MatSelect } from '@angular/material/select';
import { ReportService } from 'data/service/report/report.service';
import { DeploymentService } from 'data/service/deployment.service';
import * as tUtils from 'src/app/data/service/thoai.service';
import { CookieService } from 'ngx-cookie-service';
import { DLKStatisticsService } from 'src/app/data/service/dlk-statistics/dlk-statistics.service';
import { StatisticsService } from 'src/app/data/service/statistics/statistics.service';
import { Workbook } from 'exceljs';
import * as fs from 'file-saver';
export interface Agency {
  id: string;
  name: string;
}
import {
  DossierDetail25DialogModel,
  DossierDetailComponent,
  procedureDetailDialogModel,
} from '../dialogs/view-detail.component';
import { MatDialog } from '@angular/material/dialog';
@Component({
  selector: 'app-dlk-baocao-chithi25-toantinh',
  templateUrl: './dlk-baocao-chithi25-toantinh.component.html',
  styleUrls: [
    './dlk-baocao-chithi25-toantinh.component.scss',
    '/src/app/app.component.scss',
    '/src/app/shared/scss/form-field-outline.scss',
  ],
})
export class DlkBaocaoChithi25ToanTinhComponent
  implements OnInit, AfterViewInit, OnDestroy
{
  nowDate = tUtils.newDate();


  // tslint:disable-next-line: max-line-length
  toDate =
    this.nowDate.getFullYear() +
    '-' +
    (this.nowDate.getMonth() + 1 <= 9
      ? '0' + (this.nowDate.getMonth() + 1)
      : this.nowDate.getMonth() + 1) +
    '-' +
    (this.nowDate.getDate() <= 9
      ? '0' + this.nowDate.getDate()
      : this.nowDate.getDate()) + 'T00:00:00';
  // tslint:disable-next-line: max-line-length
  fromDate =
    this.nowDate.getFullYear() +
    '-' +
    (this.nowDate.getMonth() + 1 <= 9
      ? '0' + (this.nowDate.getMonth() + 1)
      : this.nowDate.getMonth() + 1) +
    '-01T00:00:00';

  keyword = '';
  config = this.envService.getConfig();

  selectedLang = localStorage.getItem('language') || 'vi';
  selectedLangId = Number(localStorage.getItem('languageId')) || 228;
  pageTitle = {
    vi: 'Báo cáo chị thị 25 toàn tỉnh',
    en: 'Report according to the template of Directive 25 for the entire province',
  };

  protected onDestroy = new Subject<void>();
  // =================== old
  env: any = this.deploymentService.getAppDeployment()?.env;
  ELEMENTDATA: any[] = [];
  dataSource: MatTableDataSource<any>;
  keySearchSectorAgency = '';
  // Search Sector
  keywordSector = '';
  totalPagesSector = 0;
  currentPageSector = 0;
  pageSizeSector = 100;
  searchString = '';

  parentAgency = '';
  agencyId = '';
  userAgency?: any = JSON.parse(localStorage.getItem('userAgency'));
  userAgencyCount?: any =
    JSON.parse(localStorage.getItem('listAgencyUser'))?.length || 0;
  ELEMENTDATAALL: any[] = [];
  dataSourceAll: MatTableDataSource<any>;

  listHoSoCapTinh = [];
  listHoSoCapHuyen = [];
  TongCapTinh: any;
  TongCapHuyen: any;
  TongCapTinhLK: any;
  TongCapHuyenLK: any;
  listHoSo = [];
  listHoSoLK = [];
  procedureAgencyLevel =
    this.deploymentService.env.statistics.procedureAgencyLevel;
  Agency =  this.env?.OS_DLK ;
  size = 10;
  pageIndex = 1;
  page = 1;
  countResult = 0;
  pgSizeOptions = this.config.pageSizeOptions;
  isAdmin?: boolean = sessionStorage.getItem('oneGateAdminMaster') === '1';
  paramsQuery = {
    fromDate: '',
    toDate: '',
    fromLKDate:'',
    toLKDate: '',
    agencyId: null,
    page:0,
    listAgencyId:[]
  };
  paramsDossier = {
    page: 0,
    size: 10,
    fromDate: null,
    toDate: null,
    agencyId: null,
    applyMethodId: null,
    receivingKind: null,
    hinhThucNop: null,
    keyword: '',
    dossierStatusId: null,
    procedureLevelId: null,
    code: '',
  };

  listAgencyAccept = [];
  listAgency = [];
  listXaPhuongAgency = [];
  listPhongBanAgency = [];
  keywordAgencyAccept = '';
  totalPagesAgencyAccept = 0;
  currentPageAgencyAccept = 0;
  pageSizeAgencyAccept = 50;
  startDateCumulative = new Date();
  endDateCumulative = new Date();
  startDate = new Date();
  endDate = new Date();
  flagTTPVHCC = true;
  timeOutAgencyAccept: any = null;
  listSectorfillter: any[] = [];
  //listSector: any[] = [];
  listProcedure: any[] = [];
  listProcedurefillter: any[] = [];
  totalPagesProcedure = 0;
  currentPageProcedure = 0;
  searchProcedureKeyword = '';
  listHinhThucNhan: any[] = [];
  EXCEL_EXTENSION = '.xlsx';
  EXCEL_TYPE =
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8';
  @ViewChild('procedureMatSelectInfiniteScroll', { static: true })
  procedureMatSelectInfiniteScroll: MatSelect;

  constructor(
    private envService: EnvService,
    public datepipe: DatePipe,
    private mainService: MainService,
    private cdRef: ChangeDetectorRef,
    private procedureService: ProcedureService,
    private datePipe: DatePipe,
    private deploymentService: DeploymentService,
    private dlkStatisticService: DLKStatisticsService,
    private statisticsService: StatisticsService,
    private reportService: ReportService,
    private dialog: MatDialog,
    private snackbar: SnackbarService,

  ) {
    this.dataSource = new MatTableDataSource(this.ELEMENTDATA);
    this.dataSourceAll = new MatTableDataSource(this.ELEMENTDATAALL);
  }
  ngOnInit(): void {
    this.mainService.setPageTitle(
      this.pageTitle[localStorage.getItem('language')]
    );
    this.startDate = new Date(this.fromDate);
    this.startDateCumulative = new Date(this.fromDate);
    this.endDate = new Date(this.toDate);
    this.parentAgency = this.Agency?.rootAgencyId ? this.Agency?.rootAgencyId  : "60b87fb59adb921904a0213e" ; // dlk
  }

  ngAfterViewInit() {
    let phongbanLevel = this.env?.OS_DLK?.configDepartmentTagId ? this.env?.OS_DLK?.configDepartmentTagId : "5f39f42d5224cf235e134c5a"
    let xaLevel = this.env?.OS_DLK?.configCommueTagId ? this.env?.OS_DLK?.configCommueTagId : "62cd908b011f773c23acd947"
    this.cdRef.detectChanges();
    this.getListAgencyAccept(this.Agency.rootAgencyId, '', 0, 10000);
    this.getListChildAgencyAccept(phongbanLevel, '', 0, 10000,1);
    this.getListChildAgencyAccept(xaLevel, '', 0, 10000,2);
  }

  ngOnDestroy() {
    this.onDestroy.next();
    this.onDestroy.complete();
  }
  colToLetter(number) {
    let result = '';
    do {
      const letter = String.fromCharCode(65 + (number % 26));
      result = letter + result;
      number = Math.floor(number / 26) - 1;
    } while (number >= 0);
    return result;
  }
  thongKe() {
    if(this.validateForm() == 1){
      this.waitingDownloadExcel = true;
      this.paramsQuery.page = 0;
      this.page = 1;
      this.getListHoSo();
    }
  }
  paginate(event) {
    this.paramsQuery.page = event;
  }

  getListHoSo() {
    let listAgencyId = this.listAgency.map(item => item.id);

    let promise = new Promise((resolve, reject) => {
      (this.paramsQuery.fromDate = this.startDate
        ? this.datePipe.transform(this.startDate, 'yyyy-MM-dd')
        : ''),
        (this.paramsQuery.toDate = this.endDate
          ? this.datePipe.transform(this.endDate, 'yyyy-MM-dd')
          : ''),
        (this.paramsQuery.fromLKDate = this.startDateCumulative
          ? this.datePipe.transform(this.startDateCumulative, 'yyyy-MM-dd')
          : ''),
        (this.paramsQuery.toLKDate = this.endDateCumulative
          ? this.datePipe.transform(this.endDateCumulative, 'yyyy-MM-dd')
          : ''),
        (this.paramsQuery.agencyId = this.parentAgency);
        (this.paramsQuery.listAgencyId = listAgencyId )
      this.dlkStatisticService.getlistDossierCT25(this.paramsQuery).subscribe(
        (res) => {
          let listDVCap3 = [];
          listDVCap3.push(...this.listXaPhuongAgency)
          let listIdDVCap3 = listDVCap3.map(item => item.id)        
            for (let ix = 0; ix < res.length; ix++) {        
              if (listIdDVCap3.includes(res[ix].agencyParentId) && res[ix].agencyParentId != this.parentAgency && !listAgencyId.includes(res[ix].agencyParentId)) {
                  let findItem = listDVCap3.find((item) => item.id == res[ix].agencyParentId)
                    res[ix].agencyParentId = findItem.parent
              }
            }
            this.listHoSo = res;                      
            this.BuilData();
        },
        (err) => {
          console.log(err);
        }
      );
    });
    return promise;
  }
  SelectedAgency = '';
  GetDetailDossier(
    AgencyId,
    AgencyName,
    TrongKy,
    tongSoHoSo,
    soHoSoTonKyTruoc,
    soHoSoTN,
    soHoSoDXL,
    soHoSoDXLTrongHan,
    soHoSoDXLQuaHan,
    soHoSoCXL,
    soHoSoTONCONHAN,
    soHoSoTONQUAHAN,
    total
  ) {
    if (TrongKy == 1) {
      this.paramsDossier.fromDate = this.startDate
        ?  this.datePipe.transform(this.startDate, 'yyyy-MM-dd')
        : '';
      this.paramsDossier.toDate = this.endDate
      ?  this.datePipe.transform(this.endDate, 'yyyy-MM-dd')
      : '';
    } else {
      this.paramsDossier.fromDate = this.startDateCumulative
        ?  this.datePipe.transform(this.startDateCumulative, 'yyyy-MM-dd')
        : '';
      this.paramsDossier.toDate = this.endDateCumulative
      ? this.datePipe.transform(this.endDateCumulative, 'yyyy-MM-dd')
      : '';
    }
    // tslint:disable-next-line:max-line-length
    const dialogData = new DossierDetail25DialogModel(
      AgencyId,
      AgencyName,
      TrongKy,
      tongSoHoSo,
      soHoSoTonKyTruoc,
      soHoSoTN,
      soHoSoDXL,
      soHoSoDXLTrongHan,
      soHoSoDXLQuaHan,
      soHoSoCXL,
      soHoSoTONCONHAN,
      soHoSoTONQUAHAN,
      this.paramsDossier.fromDate,
      this.paramsDossier.toDate,
      this.listAgency,
      total,
      2,
      [],
      0
    );
    const dialogRef = this.dialog.open(DossierDetailComponent, {
      width: '95%',
      height: '90%',
      data: dialogData,
      disableClose: true,
      autoFocus: false,
    });
    dialogRef.afterClosed().subscribe(() => {});
  }

  PROCEDUREDATA: any[] = [];

  getListAgencyAccept(prid, keyword, page, size) {
    const searchString =
      '?parent-id=' +
      prid +
      '&keyword=' +
      keyword +
      '&page=' +
      page +
      '&size=' +
      size +
      '&sort=name.name,asc&status=1';
    this.listHoSoCapTinh = [];
    this.listHoSoCapHuyen = [];
    this.procedureService.getListAgencyWithParent(searchString).subscribe(
      (res) => {
        this.listAgency = res.content;
      },
      (err) => {
        console.log(err);
      }
    );
  }

  getListChildAgencyAccept(levelId, keyword, page, size, type) {
    this.listHoSoCapTinh = [];
    this.listHoSoCapHuyen = [];
    this.dlkStatisticService.getListAgencyWithLevel25(this.parentAgency).subscribe(
      (res) => {
        this.listXaPhuongAgency = res.content;        
      },
      (err) => {
        console.log(err);
      }
    );
  }

  BuilData() {
    let a = {
      TrongKy: 1,
      tongSoHoSo: 0,
      soHoSoTonKyTruoc: 0,
      soHoSoTN: 0,
      soHoSoDXLTrongHan: 0,
      soHoSoDXLTrongHan_LK: 0,
      soHoSoDXL: 0,
      soHoSoDXL_LK: 0,
      soHoSoDXLQuaHan: 0,
      soHoSoDXLQuaHan_LK: 0,
      soHoSoTON: 0,
      soHoSoTONCONHAN: 0,
      soHoSoTONQUAHAN: 0,
      vanban_QUAHAN: 0,
      vanban_QUAHAN2: 0,
      vanban_MAT_HS2: 0,
      vanban_SAISOT2: 0,
      vanban_THIEU_TPHS2: 0,
      vanban_MAT_HS: 0,
      vanban_SAISOT: 0,
      vanban_THIEU_TPHS: 0,
      vanban_QUAHAN_LK: 0,
      vanban_QUAHAN2_LK: 0,
      vanban_MAT_HS2_LK: 0,
      vanban_SAISOT2_LK: 0,
      vanban_THIEU_TPHS2_LK: 0,
      vanban_MAT_HS_LK: 0,
      vanban_SAISOT_LK: 0,
      vanban_THIEU_TPHS_LK: 0,
      tongSoVBXL: 0,
      tongSoVBXL_LK: 0
    };
    this.listHoSoCapTinh = [];
    this.listHoSoCapHuyen = [];
    this.TongCapTinh = {
      TrongKy: 1,
      tongSoHoSo: 0,
      soHoSoTonKyTruoc: 0,
      soHoSoTN: 0,
      soHoSoDXLTrongHan: 0,
      soHoSoDXLTrongHan_LK: 0,
      soHoSoDXL: 0,
      soHoSoDXL_LK: 0,
      soHoSoDXLQuaHan: 0,
      soHoSoDXLQuaHan_LK: 0,
      soHoSoTON: 0,
      soHoSoTONCONHAN: 0,
      soHoSoTONQUAHAN: 0,
      vanban_QUAHAN: 0,
      vanban_QUAHAN2: 0,
      vanban_MAT_HS2: 0,
      vanban_SAISOT2: 0,
      vanban_THIEU_TPHS2: 0,
      vanban_MAT_HS: 0,
      vanban_SAISOT: 0,
      vanban_THIEU_TPHS: 0,
      vanban_QUAHAN_LK: 0,
      vanban_QUAHAN2_LK: 0,
      vanban_MAT_HS2_LK: 0,
      vanban_SAISOT2_LK: 0,
      vanban_THIEU_TPHS2_LK: 0,
      vanban_MAT_HS_LK: 0,
      vanban_SAISOT_LK: 0,
      vanban_THIEU_TPHS_LK: 0,
      tongSoVBXL: 0,
      tongSoVBXL_LK: 0
    };

    this.TongCapHuyen = {
      TrongKy: 1,
      tongSoHoSo: 0,
      soHoSoTonKyTruoc: 0,
      soHoSoTN: 0,
      soHoSoDXLTrongHan: 0,
      soHoSoDXLTrongHan_LK: 0,
      soHoSoDXL: 0,
      soHoSoDXL_LK: 0,
      soHoSoDXLQuaHan: 0,
      soHoSoDXLQuaHan_LK: 0,
      soHoSoTON: 0,
      soHoSoTONCONHAN: 0,
      soHoSoTONQUAHAN: 0,
      vanban_QUAHAN: 0,
      vanban_QUAHAN2: 0,
      vanban_MAT_HS2: 0,
      vanban_SAISOT2: 0,
      vanban_THIEU_TPHS2: 0,
      vanban_MAT_HS: 0,
      vanban_SAISOT: 0,
      vanban_THIEU_TPHS: 0,
      vanban_QUAHAN_LK: 0,
      vanban_QUAHAN2_LK: 0,
      vanban_MAT_HS2_LK: 0,
      vanban_SAISOT2_LK: 0,
      vanban_THIEU_TPHS2_LK: 0,
      vanban_MAT_HS_LK: 0,
      vanban_SAISOT_LK: 0,
      vanban_THIEU_TPHS_LK: 0,
      tongSoVBXL: 0,
      tongSoVBXL_LK: 0

    };

    for (let i = 0; i < this.listAgency.length; i++) {
      let SlhoSoCoQuan = this.listHoSo.filter(
        (f) =>
          (f.agencyId !== null && f.agencyId === this.listAgency[i].id) ||
          (f.agencyParentId !== null &&
            f.agencyParentId === this.listAgency[i].id)
      );

      var main = {
        id: this.listAgency[i].id,
        coQuan: this.listAgency[i].name,
        data: [],
      };

      var arr = {
        id: this.listAgency[i].id,
        coQuan: this.listAgency[i].name,
        TrongKy: 1,
        tongSoHoSo: 0,
        soHoSoTonKyTruoc: 0,
        soHoSoTN: 0,
        soHoSoDXLTrongHan: 0,
        soHoSoDXL: 0,
        soHoSoDXLQuaHan: 0,
        soHoSoTON: 0,
        soHoSoTONCONHAN: 0,
        soHoSoTONQUAHAN: 0,
        vanban_QUAHAN: 0,
        vanban_QUAHAN2: 0,
        vanban_MAT_HS2: 0,
        vanban_SAISOT2: 0,
        vanban_THIEU_TPHS2: 0,
        vanban_MAT_HS: 0,
        vanban_SAISOT: 0,
        vanban_THIEU_TPHS: 0,
        tongSoVBXL: 0,
        soHoSoDXL_LK: 0,
        soHoSoDXLTrongHan_LK: 0,
        soHoSoDXLQuaHan_LK: 0,
        vanban_QUAHAN_LK: 0,
        vanban_QUAHAN2_LK: 0,
        vanban_MAT_HS2_LK: 0,
        vanban_SAISOT2_LK: 0,
        vanban_THIEU_TPHS2_LK: 0,
        vanban_MAT_HS_LK: 0,
        vanban_SAISOT_LK: 0,
        vanban_THIEU_TPHS_LK: 0,
        tongSoVBXL_LK: 0
      };

      if (SlhoSoCoQuan.length > 0) {
        SlhoSoCoQuan.forEach((element) => {
          (arr.tongSoHoSo += (element.soHoSoTonKyTruoc + element.soHoSoTN)),
            (arr.soHoSoTonKyTruoc += element.soHoSoTonKyTruoc),
            (arr.soHoSoTN += element.soHoSoTN),
            (arr.soHoSoDXL += (element.soHoSoDXLTrongHan + element.soHoSoDXLQuaHan)),
            (arr.soHoSoDXLQuaHan += element.soHoSoDXLQuaHan),
            (arr.soHoSoDXLTrongHan += element.soHoSoDXLTrongHan),
            (arr.soHoSoTON += (element.soHoSoTONCONHAN + element.soHoSoTONQUAHAN)),
            (arr.soHoSoTONCONHAN += element.soHoSoTONCONHAN),
            (arr.soHoSoTONQUAHAN += element.soHoSoTONQUAHAN);
            (arr.tongSoVBXL += (element.vanban_QUAHAN + element.vanban_QUAHAN2 + element.vanban_MAT_HS + element.vanban_MAT_HS2 + element.vanban_SAISOT + element.vanban_SAISOT2 + element.vanban_THIEU_TPHS + element.vanban_THIEU_TPHS2));
            
            (arr.vanban_QUAHAN += (element.vanban_QUAHAN + element.vanban_QUAHAN2));
            (arr.vanban_MAT_HS += element.vanban_MAT_HS + element.vanban_MAT_HS2);
            (arr.vanban_SAISOT += element.vanban_SAISOT + element.vanban_SAISOT2);
            (arr.vanban_THIEU_TPHS += element.vanban_THIEU_TPHS + element.vanban_THIEU_TPHS2);
            
            (arr.tongSoVBXL_LK += (element.vanban_QUAHAN_LK + element.vanban_QUAHAN2_LK + element.vanban_MAT_HS_LK + element.vanban_MAT_HS2_LK + element.vanban_SAISOT_LK + element.vanban_SAISOT2_LK + element.vanban_THIEU_TPHS_LK + element.vanban_THIEU_TPHS2_LK));            
            (arr.soHoSoDXL_LK += (element.soHoSoDXLTrongHan_LK + element.soHoSoDXLQuaHan_LK)),
            (arr.soHoSoDXLQuaHan_LK += element.soHoSoDXLQuaHan_LK),
            (arr.soHoSoDXLTrongHan_LK += element.soHoSoDXLTrongHan_LK)
          });
      }
      main.data.push(arr);

      let agencyLevel0 = this.env?.OS_DLK?.configDepartmentTagId ? this.env?.OS_DLK?.configDepartmentTagId : "5f39f42d5224cf235e134c5a";
      let agencyLevel1 = this.env?.OS_DLK?.configDistrictTagId ? this.env?.OS_DLK?.configDistrictTagId : "5f39f4155224cf235e134c59";


      /////////////////////////////////
      if (
        this.listAgency[i].level !== null &&
        this.listAgency[i].level.id == agencyLevel0
      ) {
        this.listHoSoCapTinh.push(main);
        this.TongCapTinh.tongSoHoSo += arr.tongSoHoSo;
        this.TongCapTinh.soHoSoTonKyTruoc += arr.soHoSoTonKyTruoc;
        this.TongCapTinh.soHoSoTN += arr.soHoSoTN;
        this.TongCapTinh.soHoSoDXL += arr.soHoSoDXL;
        this.TongCapTinh.soHoSoDXLTrongHan += arr.soHoSoDXLTrongHan;
        this.TongCapTinh.soHoSoDXLQuaHan += arr.soHoSoDXLQuaHan;
        this.TongCapTinh.soHoSoTON += arr.soHoSoTON;
        this.TongCapTinh.soHoSoTONCONHAN += arr.soHoSoTONCONHAN;
        this.TongCapTinh.soHoSoTONQUAHAN += arr.soHoSoTONQUAHAN;
        this.TongCapTinh.tongSoVBXL += arr.tongSoVBXL;
        this.TongCapTinh.vanban_QUAHAN += arr.vanban_QUAHAN;
        this.TongCapTinh.vanban_MAT_HS += arr.vanban_MAT_HS;
        this.TongCapTinh.vanban_SAISOT += arr.vanban_SAISOT;
        this.TongCapTinh.vanban_THIEU_TPHS += arr.vanban_THIEU_TPHS;
        
        this.TongCapTinh.soHoSoDXL_LK += arr.soHoSoDXL_LK;
        this.TongCapTinh.tongSoVBXL_LK += arr.tongSoVBXL_LK;
        this.TongCapTinh.soHoSoDXLQuaHan_LK += arr.soHoSoDXLQuaHan_LK;
        this.TongCapTinh.soHoSoDXLTrongHan_LK += arr.soHoSoDXLTrongHan_LK;


      }
      if (
        this.listAgency[i].level !== null &&
        (this.listAgency[i].level.id == agencyLevel1)
      ) {
        this.listHoSoCapHuyen.push(main);
        this.TongCapHuyen.tongSoHoSo += arr.tongSoHoSo;
        this.TongCapHuyen.soHoSoTonKyTruoc += arr.soHoSoTonKyTruoc;
        this.TongCapHuyen.soHoSoTN += arr.soHoSoTN;
        this.TongCapHuyen.soHoSoDXL += arr.soHoSoDXL;
        this.TongCapHuyen.soHoSoDXLTrongHan += arr.soHoSoDXLTrongHan;
        this.TongCapHuyen.soHoSoDXLQuaHan += arr.soHoSoDXLQuaHan;
        this.TongCapHuyen.soHoSoTON += arr.soHoSoTON;
        this.TongCapHuyen.soHoSoTONCONHAN += arr.soHoSoTONCONHAN;
        this.TongCapHuyen.soHoSoTONQUAHAN += arr.soHoSoTONQUAHAN;
        this.TongCapHuyen.tongSoVBXL += arr.tongSoVBXL;
        this.TongCapHuyen.vanban_QUAHAN += arr.vanban_QUAHAN;
        this.TongCapHuyen.vanban_MAT_HS += arr.vanban_MAT_HS;
        this.TongCapHuyen.vanban_SAISOT += arr.vanban_SAISOT;
        this.TongCapHuyen.vanban_THIEU_TPHS += arr.vanban_THIEU_TPHS;
        
        this.TongCapHuyen.soHoSoDXL_LK += arr.soHoSoDXL_LK;
        this.TongCapHuyen.tongSoVBXL_LK += arr.tongSoVBXL_LK;
        this.TongCapHuyen.soHoSoDXLQuaHan_LK += arr.soHoSoDXLQuaHan_LK;
        this.TongCapHuyen.soHoSoDXLTrongHan_LK += arr.soHoSoDXLTrongHan_LK;

      }
    }
    this.waitingDownloadExcel = false;
  }
  waitingDownloadExcel: boolean = false;

  validateForm() {
    let language = localStorage.getItem('language');

    let data = {
      errMessage: "",
      status: false
    }
    if (this.startDate == null || this.endDate == null) {
      data.errMessage = language == 'vi' ? 'Vui lòng nhập đầy đủ thông tin ngày tháng bắt đầu và kết thúc' : 'Please enter complete information for the start and end dates.';
      this.snackbar.openSnackBar(0, data.errMessage, '', 'error_notification', this.config.expiredTime);

      return 0;
    } else if(this.startDate.getTime() > this.endDate.getTime()) {
      data.errMessage = language == 'vi' ? 'Ngày bắt đầu không được lớn hơn ngày kết thúc' : 'The start date cannot be later than the end date.'
      this.snackbar.openSnackBar(0, data.errMessage, '', 'error_notification', this.config.expiredTime);

      return 0;
    }else if (this.startDateCumulative == null || this.endDateCumulative == null) {
      data.errMessage = language == 'vi' ? 'Vui lòng nhập đầy đủ thông tin ngày tháng bắt đầu lũy kế  và kết thúc lũy kế' : 'Please enter complete information for the start and end dates.';
      this.snackbar.openSnackBar(0, data.errMessage, '', 'error_notification', this.config.expiredTime);

      return 0;
    } else if(this.startDateCumulative.getTime() > this.endDateCumulative.getTime()) {
      data.errMessage = language == 'vi' ? 'Ngày bắt đầu lũy kế không được lớn hơn ngày kết thúc lũy kế' : 'The start date cannot be later than the end date.'
      this.snackbar.openSnackBar(0, data.errMessage, '', 'error_notification', this.config.expiredTime);

      return 0;
    }else{
      return 1;
    }
  }
  
  async exportToExcel() {
    //this.dataExport = await this.getDossierStatisticDetailExport();
    if(this.validateForm() == 1){
      this.waitingDownloadExcel = true;
      this.paramsQuery.page = 0;
      this.page = 1;
      this.getListHoSo();    
      const from = this.datePipe.transform(this.startDate, 'dd-MM-yyyy');
      const to = this.datePipe.transform(this.endDate, 'dd-MM-yyyy');
      const newDateshort = this.datePipe.transform(new Date(), 'dd-MM-yyyy');
      const newDate = this.datePipe.transform(new Date(), 'dd-MM-yyyy HH:ss:mm');
      const excelFileName = `Bao_cao_chi_thi_25__toantinh_${newDate}`;
      let headerXLS = {
        row1: 'CỘNG HÒA XÃ HỘI CHỦ NGHĨA VIỆT NAM',
        row2: 'Độc lập - Tự do - Hạnh phúc',
        row3: 'Đắk lắk, ' + newDateshort,
        row4: `BÁO CÁO CHỈ THỊ 25 TOÀN TỈNH`,
        row5: `(Từ ${from} đến ngày ${to})`,
      };

      const workbook = new Workbook();
      workbook.creator = 'Snippet Coder';
      workbook.lastModifiedBy = 'SnippetCoder';
      workbook.created = new Date();
      workbook.modified = new Date();
      const worksheet = workbook.addWorksheet('sheet1');

      // Add header row
      worksheet.addRow([]);
      worksheet.mergeCells('A1:X1');
      worksheet.getCell('A1').value = headerXLS.row1;
      worksheet.getCell('A1').alignment = {
        horizontal: 'center',
        vertical: 'middle',
      };
      worksheet.getCell('A1').font = {
        size: 13,
        bold: true,
        name: 'Times New Roman',
      };

      worksheet.mergeCells('A2:X2');
      worksheet.getCell('A2').value = headerXLS.row2;
      worksheet.getCell('A2').alignment = {
        horizontal: 'center',
        vertical: 'middle',
      };
      worksheet.getCell('A2').font = {
        size: 13,
        bold: true,
        name: 'Times New Roman',
      };

      worksheet.mergeCells('A3:X3');
      worksheet.getCell('A3').value = headerXLS.row3;
      worksheet.getCell('A3').alignment = {
        horizontal: 'center',
        vertical: 'middle',
      };
      worksheet.getCell('A3').font = {
        size: 13,
        underline: true,
        name: 'Times New Roman',
      };

      worksheet.mergeCells('A4:X4');
      worksheet.getCell('A4').value = headerXLS.row4;
      worksheet.getCell('A4').alignment = {
        horizontal: 'center',
        vertical: 'middle',
      };
      worksheet.getCell('A4').font = {
        size: 14,
        bold: true,
        name: 'Times New Roman',
      };

      worksheet.mergeCells('A5:X5');
      worksheet.getCell('A5').value = '';
      worksheet.getCell('A5').alignment = {
        horizontal: 'center',
        vertical: 'middle',
      };
      worksheet.getCell('A5').font = {
        size: 11,
        italic: true,
        name: 'Times New Roman',
      };

      worksheet.mergeCells('A6:X6');
      worksheet.getCell('A6').value = headerXLS.row5;
      worksheet.getCell('A6').alignment = {
        horizontal: 'center',
        vertical: 'middle',
      };
      worksheet.getCell('A6').font = {
        size: 11,
        italic: true,
        name: 'Times New Roman',
      };

      worksheet.mergeCells('A7:A10');
      worksheet.getCell('A7').value = 'STT';

      worksheet.mergeCells('B7:B10');
      worksheet.getCell('B7').value = 'Tên cơ quan, đơn vị';


      worksheet.mergeCells('C7:E7');
      worksheet.getCell('C7').value = 'Tổng số hồ sơ đã tiếp nhận trong tháng';
      worksheet.getCell('C7').alignment = {
        horizontal: 'center',
        vertical: 'middle',
        wrapText: true,
      };
      worksheet.getCell('C7').font = {
        size: 11,
        bold: true,
        name: 'Times New Roman',
      };
      worksheet.getCell('C7').border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };

      worksheet.mergeCells('F7:O7');
      worksheet.getCell('F7').value = 'Số hồ sơ đã giải quyết trong tháng';
      worksheet.getCell('F7').alignment = {
        horizontal: 'center',
        vertical: 'middle',
        wrapText: true,
      };
      worksheet.getCell('F7').font = {
        size: 11,
        bold: true,
        name: 'Times New Roman',
      };
      worksheet.getCell('F7').border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };

      worksheet.mergeCells('P7:R7');
      worksheet.getCell('P7').value =
        'Số hồ sơ còn tồn chưa giải quyết';
      worksheet.getCell('P7').alignment = {
        horizontal: 'center',
        vertical: 'middle',
        wrapText: true,
      };
      worksheet.getCell('P7').font = {
        size: 11,
        bold: true,
        name: 'Times New Roman',
      };
      worksheet.getCell('P7').border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };

      worksheet.mergeCells('S7:X7');
      worksheet.getCell('S7').value =
        'Lũy kế hồ sơ đã giải quyết từ đầu năm';
      worksheet.getCell('S7').alignment = {
        horizontal: 'center',
        vertical: 'middle',
        wrapText: true,
      };
      worksheet.getCell('S7').font = {
        size: 11,
        bold: true,
        name: 'Times New Roman',
      };
      worksheet.getCell('S7').border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };

      worksheet.mergeCells('C8:C10');
      worksheet.getCell('C8').value = 'Tổng số';


      worksheet.mergeCells('D8:E8');
      worksheet.getCell('D8').value = 'Trong đó';
      worksheet.getCell('D8').alignment = {
        horizontal: 'center',
        vertical: 'middle',
        wrapText: true,
      };
      worksheet.getCell('D8').font = {
        size: 11,
        bold: true,
        name: 'Times New Roman',
      };

      worksheet.mergeCells('F8:F10');
      worksheet.getCell('F8').value = 'Tổng số';

      worksheet.mergeCells('G8:O8');
      worksheet.getCell('G8').value = 'Trong đó';
      worksheet.getCell('G8').alignment = {
        horizontal: 'center',
        vertical: 'middle',
        wrapText: true,
      };
      worksheet.getCell('G8').font = {
        size: 11,
        bold: true,
        name: 'Times New Roman',
      };
      worksheet.mergeCells('P8:P10');
      worksheet.getCell('P8').value = 'Tổng số';

      worksheet.mergeCells('Q8:R8');
      worksheet.getCell('Q8').value = 'Trong đó';
      worksheet.getCell('Q8').alignment = {
        horizontal: 'center',
        vertical: 'middle',
        wrapText: true,
      };
      worksheet.getCell('Q8').font = {
        size: 11,
        bold: true,
        name: 'Times New Roman',
      };

      worksheet.mergeCells('S8:S10');
      worksheet.getCell('S8').value = 'Tổng số';

      worksheet.mergeCells('T8:X8');
      worksheet.getCell('T8').value = 'Trong đó';
      worksheet.getCell('T8').alignment = {
        horizontal: 'center',
        vertical: 'middle',
        wrapText: true,
      };
      worksheet.getCell('T8').font = {
        size: 11,
        bold: true,
        name: 'Times New Roman',
      };


      worksheet.mergeCells('D9:D10');
      worksheet.getCell('D9').value = 'Số hồ sơ chưa giải quyết của tháng trước chuyển qua';

      worksheet.mergeCells('E9:E10');
      worksheet.getCell('E9').value = 'Tổng số hồ sơ tiếp nhận mới trong tháng';

      worksheet.mergeCells('G9:G10');
      worksheet.getCell('G9').value = 'Giải quyết trước, đúng hạn';

      worksheet.mergeCells('H9:H10');
      worksheet.getCell('H9').value = 'Giải quyết quá hạn';

      worksheet.mergeCells('I9:M9');
      worksheet.getCell('I9').value = 'Số văn bản xin lỗi';

      worksheet.mergeCells('N9:N10');
      worksheet.getCell('N9').value = 'Công khai số văn bản xin lỗi lên hệ thông thông tin giải quyết TTHC (iGate)';

      worksheet.mergeCells('O9:O10');
      worksheet.getCell('O9').value = 'Số văn bản xin lỗi đã niêm yết tại bộ phận 1 cửa';

      worksheet.mergeCells('Q9:Q10');
      worksheet.getCell('Q9').value = 'Số hồ sơ trước, đúng thời hạn';

      worksheet.mergeCells('R9:R10');
      worksheet.getCell('R9').value = 'Số hồ sơ quá thời hạn';

      worksheet.mergeCells('T9:T10');
      worksheet.getCell('T9').value = 'Giải quyết trước, đúng hạn';

      worksheet.mergeCells('U9:U10');
      worksheet.getCell('U9').value = 'Giải quyết quá hạn';

      worksheet.mergeCells('V9:V10');
      worksheet.getCell('V9').value = 'Số văn bản xin lỗi';

      worksheet.mergeCells('W9:W10');
      worksheet.getCell('W9').value = 'Công khai Số văn bản xin lỗi lên hệ thông thông tin giải quyết TTHC';

      worksheet.mergeCells('X9:X10');
      worksheet.getCell('X9').value = 'Số văn bản xin lỗi đã niêm yết tại bộ phận 1 cửa';

      worksheet.getCell('I10').value = 'Tổng số';

      worksheet.getCell('J10').value = 'Do giải quyết quá hạn';

      worksheet.getCell('K10').value = 'Do tiếp nhận thành phần hồ sơ không đủ';

      worksheet.getCell('L10').value = 'Do hồ sơ bị mất, thất lạc hoặc hư hỏng';

      worksheet.getCell('M10').value = 'Do sai sót trong kết quả giải quyết';

      const rowStartHeaderContent = 11;
      const NumberCol = 24;
      for (let index = 0; index < NumberCol; index++) {
        worksheet.getCell(10, index + 1).alignment = {
          horizontal: 'center',
          vertical: 'middle',
          wrapText: true,
        };
        worksheet.getCell(10, index + 1).font = {
          size: 11,
          bold: true,
          name: 'Times New Roman',
        };
        worksheet.getCell(10, index + 1).border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' },
        };

        worksheet.getCell(9, index + 1).alignment = {
          horizontal: 'center',
          vertical: 'middle',
          wrapText: true,
        };
        worksheet.getCell(9, index + 1).font = {
          size: 11,
          bold: true,
          name: 'Times New Roman',
        };
        worksheet.getCell(9, index + 1).border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' },
        };

        worksheet.getCell('T8').border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' },
        };

        worksheet.getCell(rowStartHeaderContent, index + 1).value =
          '(' + (index + 1).toString() + ')';
        worksheet.getCell(rowStartHeaderContent, index + 1).alignment = {
          horizontal: 'center',
          vertical: 'middle',
          wrapText: true,
        };
        worksheet.getCell(rowStartHeaderContent, index + 1).font = {
          size: 11,
          bold: true,
          name: 'Times New Roman',
        };
        worksheet.getCell(rowStartHeaderContent, index + 1).border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' },
        };
      }

      let rowStartContent = rowStartHeaderContent + 1;

      worksheet.getCell(rowStartContent, 1).value = 'I';
      worksheet.getCell(rowStartContent, 1).alignment = {
        horizontal: 'center',
        vertical: 'middle',
      };
      worksheet.getCell(rowStartContent, 1).font = {
        size: 13,
        bold: true,
        name: 'Times New Roman',
      };
      worksheet.mergeCells(rowStartContent, 2, rowStartContent, NumberCol);
      worksheet.getCell(rowStartContent, 2).value = 'Sở ban ngành';
      worksheet.getCell(rowStartContent, 2).alignment = {
        horizontal: 'left',
        vertical: 'middle',
      };
      worksheet.getCell(rowStartContent, 2).font = {
        size: 13,
        bold: true,
        name: 'Times New Roman',
      };
      worksheet.getCell(rowStartContent, 2).border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };

      rowStartContent = rowStartContent + 1;

      // cấp sở ban ngành
      const data = this.listHoSoCapTinh;
      const data2 = this.listHoSoCapTinh;
      for (let i = 0; i < data.length; i++) {
        var item = data[i];
        var r = 0;

        worksheet.getCell(rowStartContent, 1).value = i + 1;

        worksheet.getCell(rowStartContent, 2).value = item.coQuan;

        //trong kỳ
        worksheet.getCell(rowStartContent + r, 3).value =
          item.data[r].tongSoHoSo;
        worksheet.getCell(rowStartContent + r, 4).value =
          item.data[r].soHoSoTonKyTruoc;
        worksheet.getCell(rowStartContent + r, 5).value =
          item.data[r].soHoSoTN;
        worksheet.getCell(rowStartContent + r, 6).value =
          item.data[r].soHoSoDXL;
        worksheet.getCell(rowStartContent + r, 7).value =
          item.data[r].soHoSoDXLTrongHan;
        worksheet.getCell(rowStartContent + r, 8).value =
          item.data[r].soHoSoDXLQuaHan;
        worksheet.getCell(rowStartContent + r, 9).value =
          item.data[r].tongSoVBXL;
        worksheet.getCell(rowStartContent + r, 10).value =
          item.data[r].vanban_QUAHAN;
        worksheet.getCell(rowStartContent + r, 11).value =
          item.data[r].vanban_THIEU_TPHS;
        worksheet.getCell(rowStartContent + r, 12).value =
          item.data[r].vanban_MAT_HS;
        worksheet.getCell(rowStartContent + r, 13).value =
          item.data[r].vanban_SAISOT;
        worksheet.getCell(rowStartContent + r, 14).value = 
          item.data[r].tongSoVBXL;
        worksheet.getCell(rowStartContent + r, 15).value =
          item.data[r].tongSoVBXL;
        worksheet.getCell(rowStartContent + r, 16).value =
          item.data[r].soHoSoTON;
        worksheet.getCell(rowStartContent + r, 17).value =
          item.data[r].soHoSoTONCONHAN;
        worksheet.getCell(rowStartContent + r, 18).value =
          item.data[r].soHoSoTONQUAHAN;
        worksheet.getCell(rowStartContent + r, 19).value =
          item.data[r].soHoSoDXL_LK;
        worksheet.getCell(rowStartContent + r, 20).value =
          item.data[r].soHoSoDXLTrongHan_LK;
        worksheet.getCell(rowStartContent + r, 21).value =
          item.data[r].soHoSoDXLQuaHan_LK;
        worksheet.getCell(rowStartContent + r, 22).value =
          item.data[r].tongSoVBXL_LK;      
        worksheet.getCell(rowStartContent + r, 23).value =
          item.data[r].tongSoVBXL_LK;      
        worksheet.getCell(rowStartContent + r, 24).value =
          item.data[r].tongSoVBXL_LK;


        for (let c = 0; c < NumberCol; c++) {
          worksheet.getCell(rowStartContent + r, c + 1).alignment = {
            horizontal: 'center',
            vertical: 'middle',
            wrapText: true,
          };
          worksheet.getCell(rowStartContent + r, c + 1).font = {
            size: 11,
            name: 'Times New Roman',
          };
          worksheet.getCell(rowStartContent + r, c + 1).border = {
            top: { style: 'thin' },
            left: { style: 'thin' },
            bottom: { style: 'thin' },
            right: { style: 'thin' },
          };
        }


        for (let c = 0; c < NumberCol; c++) {
          worksheet.getCell(rowStartContent + r, c + 1).alignment = {
            horizontal: 'center',
            vertical: 'middle',
            wrapText: true,
          };
          worksheet.getCell(rowStartContent + r, c + 1).font = {
            size: 11,
            name: 'Times New Roman',
          };
          worksheet.getCell(rowStartContent + r, c + 1).border = {
            top: { style: 'thin' },
            left: { style: 'thin' },
            bottom: { style: 'thin' },
            right: { style: 'thin' },
          };
        }
        rowStartContent = rowStartContent + 1;
      }

      //Tổng cấp tỉnh
      worksheet.mergeCells(rowStartContent, 1, rowStartContent, 2);
      worksheet.getCell(rowStartContent, 1).value = 'TỔNG SỞ BAN NGÀNH';
      worksheet.getCell(rowStartContent, 1).alignment = {
        horizontal: 'center',
        vertical: 'middle',
      };
      worksheet.getCell(rowStartContent, 1).font = {
        size: 11,
        bold: true,
        name: 'Times New Roman',
      };
      worksheet.getCell(rowStartContent, 1).border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
    worksheet.getCell(rowStartContent + r, 3).value =
      this.TongCapTinh.tongSoHoSo;
    worksheet.getCell(rowStartContent + r, 4).value =
      this.TongCapTinh.soHoSoTonKyTruoc;
    worksheet.getCell(rowStartContent + r, 5).value =
      this.TongCapTinh.soHoSoTN;
    worksheet.getCell(rowStartContent + r, 6).value =
      this.TongCapTinh.soHoSoDXL;
    worksheet.getCell(rowStartContent + r, 7).value =
      this.TongCapTinh.soHoSoDXLTrongHan;
    worksheet.getCell(rowStartContent + r, 8).value =
      this.TongCapTinh.soHoSoDXLQuaHan;
    worksheet.getCell(rowStartContent + r, 9).value =
      this.TongCapTinh.tongSoVBXL;
    worksheet.getCell(rowStartContent + r, 10).value =
      this.TongCapTinh.vanban_QUAHAN;
    worksheet.getCell(rowStartContent + r, 11).value =
      this.TongCapTinh.vanban_THIEU_TPHS;
    worksheet.getCell(rowStartContent + r, 12).value =
      this.TongCapTinh.vanban_MAT_HS;
    worksheet.getCell(rowStartContent + r, 13).value =
      this.TongCapTinh.vanban_SAISOT;
    worksheet.getCell(rowStartContent + r, 14).value = 
      this.TongCapTinh.tongSoVBXL;
    worksheet.getCell(rowStartContent + r, 15).value =
      this.TongCapTinh.tongSoVBXL;
    worksheet.getCell(rowStartContent + r, 16).value =
      this.TongCapTinh.soHoSoTON;
    worksheet.getCell(rowStartContent + r, 17).value =
      this.TongCapTinh.soHoSoTONCONHAN;
    worksheet.getCell(rowStartContent + r, 18).value =
      this.TongCapTinh.soHoSoTONQUAHAN;
    worksheet.getCell(rowStartContent + r, 19).value =
      this.TongCapTinh.soHoSoDXL_LK;
    worksheet.getCell(rowStartContent + r, 20).value =
      this.TongCapTinh.soHoSoDXLTrongHan_LK;
    worksheet.getCell(rowStartContent + r, 21).value =
      this.TongCapTinh.soHoSoDXLQuaHan_LK;
    worksheet.getCell(rowStartContent + r, 22).value =
      this.TongCapTinh.tongSoVBXL_LK;      
    worksheet.getCell(rowStartContent + r, 23).value =
      this.TongCapTinh.tongSoVBXL_LK;      
    worksheet.getCell(rowStartContent + r, 24).value =
      this.TongCapTinh.tongSoVBXL_LK;
        
      rowStartContent = rowStartContent + 1;


      for (let index = 2; index < NumberCol; index++) {
        worksheet.getCell(rowStartContent, index + 1).alignment = {
          horizontal: 'center',
          vertical: 'middle',
        };
        worksheet.getCell(rowStartContent, index + 1).font = {
          size: 11,
          bold: true,
          name: 'Times New Roman',
        };
        worksheet.getCell(rowStartContent, index + 1).border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' },
        };

        worksheet.getCell(rowStartContent - 1, index + 1).alignment = {
          horizontal: 'center',
          vertical: 'middle',
        };
        worksheet.getCell(rowStartContent - 1, index + 1).font = {
          size: 11,
          bold: true,
          name: 'Times New Roman',
        };
        worksheet.getCell(rowStartContent - 1, index + 1).border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' },
        };
      }

      rowStartContent = rowStartContent;
      worksheet.getCell(rowStartContent, 1).value = 'II';
      worksheet.getCell(rowStartContent, 1).alignment = {
        horizontal: 'center',
        vertical: 'middle',
      };
      worksheet.getCell(rowStartContent, 1).font = {
        size: 13,
        bold: true,
        name: 'Times New Roman',
      };
      worksheet.mergeCells(rowStartContent, 2, rowStartContent, NumberCol);
      worksheet.getCell(rowStartContent, 2).value = 'Thành phố, huyện, thị xã';
      worksheet.getCell(rowStartContent, 2).alignment = {
        horizontal: 'left',
        vertical: 'middle',
      };
      worksheet.getCell(rowStartContent, 2).font = {
        size: 13,
        bold: true,
        name: 'Times New Roman',
      };
      worksheet.getCell(rowStartContent, 2).border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };

      rowStartContent = rowStartContent + 1;
      // List cấp huyện
      const data1 = this.listHoSoCapHuyen;
      for (let i = 0; i < data1.length; i++) {
        var item = data1[i];
        var r = 0;

        worksheet.getCell(rowStartContent + r, 1).value = i + 1;
        worksheet.getCell(rowStartContent + r, 2).value = item.coQuan;

        //trong kỳ
      worksheet.getCell(rowStartContent + r, 3).value =
        item.data[r].tongSoHoSo;
      worksheet.getCell(rowStartContent + r, 4).value =
        item.data[r].soHoSoTonKyTruoc;
      worksheet.getCell(rowStartContent + r, 5).value =
        item.data[r].soHoSoTN;
      worksheet.getCell(rowStartContent + r, 6).value =
        item.data[r].soHoSoDXL;
      worksheet.getCell(rowStartContent + r, 7).value =
        item.data[r].soHoSoDXLTrongHan;
      worksheet.getCell(rowStartContent + r, 8).value =
        item.data[r].soHoSoDXLQuaHan;
      worksheet.getCell(rowStartContent + r, 9).value =
        item.data[r].tongSoVBXL;
      worksheet.getCell(rowStartContent + r, 10).value =
        item.data[r].vanban_QUAHAN;
      worksheet.getCell(rowStartContent + r, 11).value =
        item.data[r].vanban_THIEU_TPHS;
      worksheet.getCell(rowStartContent + r, 12).value =
        item.data[r].vanban_MAT_HS;
      worksheet.getCell(rowStartContent + r, 13).value =
        item.data[r].vanban_SAISOT;
      worksheet.getCell(rowStartContent + r, 14).value = 
        item.data[r].tongSoVBXL;
      worksheet.getCell(rowStartContent + r, 15).value =
        item.data[r].tongSoVBXL;
      worksheet.getCell(rowStartContent + r, 16).value =
        item.data[r].soHoSoTON;
      worksheet.getCell(rowStartContent + r, 17).value =
        item.data[r].soHoSoTONCONHAN;
      worksheet.getCell(rowStartContent + r, 18).value =
        item.data[r].soHoSoTONQUAHAN;
      worksheet.getCell(rowStartContent + r, 19).value =
        item.data[r].soHoSoDXL_LK;
      worksheet.getCell(rowStartContent + r, 20).value =
        item.data[r].soHoSoDXLTrongHan_LK;
      worksheet.getCell(rowStartContent + r, 21).value =
        item.data[r].soHoSoDXLQuaHan_LK;
      worksheet.getCell(rowStartContent + r, 22).value =
        item.data[r].tongSoVBXL_LK;      
      worksheet.getCell(rowStartContent + r, 23).value =
        item.data[r].tongSoVBXL_LK;      
      worksheet.getCell(rowStartContent + r, 24).value =
        item.data[r].tongSoVBXL_LK;

        for (let c = 0; c < NumberCol; c++) {
          worksheet.getCell(rowStartContent + r, c + 1).alignment = {
            horizontal: 'center',
            vertical: 'middle',
            wrapText: true,
          };
          worksheet.getCell(rowStartContent + r, c + 1).font = {
            size: 11,
            name: 'Times New Roman',
          };
          worksheet.getCell(rowStartContent + r, c + 1).border = {
            top: { style: 'thin' },
            left: { style: 'thin' },
            bottom: { style: 'thin' },
            right: { style: 'thin' },
          };
        }
        // Lũy kế


        for (let c = 0; c < NumberCol; c++) {
          worksheet.getCell(rowStartContent + r, c + 1).alignment = {
            horizontal: 'center',
            vertical: 'middle',
            wrapText: true,
          };
          worksheet.getCell(rowStartContent + r, c + 1).font = {
            size: 11,
            name: 'Times New Roman',
          };
          worksheet.getCell(rowStartContent + r, c + 1).border = {
            top: { style: 'thin' },
            left: { style: 'thin' },
            bottom: { style: 'thin' },
            right: { style: 'thin' },
          };
        }
        rowStartContent = rowStartContent + 1;
      }

      //Tổng cấp Huyện
      worksheet.mergeCells(rowStartContent, 1, rowStartContent, 2);
      worksheet.getCell(rowStartContent, 1).value =
        'TỔNG THÀNH PHỐ, HUYỆN, THỊ XÃ';
      worksheet.getCell(rowStartContent, 1).alignment = {
        horizontal: 'center',
        vertical: 'middle',
      };
      worksheet.getCell(rowStartContent, 1).font = {
        size: 11,
        bold: true,
        name: 'Times New Roman',
      };
      worksheet.getCell(rowStartContent, 1).border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };

      worksheet.getCell(rowStartContent + r, 3).value =
      this.TongCapHuyen.tongSoHoSo;
    worksheet.getCell(rowStartContent + r, 4).value =
      this.TongCapHuyen.soHoSoTonKyTruoc;
    worksheet.getCell(rowStartContent + r, 5).value =
      this.TongCapHuyen.soHoSoTN;
    worksheet.getCell(rowStartContent + r, 6).value =
      this.TongCapHuyen.soHoSoDXL;
    worksheet.getCell(rowStartContent + r, 7).value =
      this.TongCapHuyen.soHoSoDXLTrongHan;
    worksheet.getCell(rowStartContent + r, 8).value =
      this.TongCapHuyen.soHoSoDXLQuaHan;
    worksheet.getCell(rowStartContent + r, 9).value =
      this.TongCapHuyen.tongSoVBXL;
    worksheet.getCell(rowStartContent + r, 10).value =
      this.TongCapHuyen.vanban_QUAHAN;
    worksheet.getCell(rowStartContent + r, 11).value =
      this.TongCapHuyen.vanban_THIEU_TPHS;
    worksheet.getCell(rowStartContent + r, 12).value =
      this.TongCapHuyen.vanban_MAT_HS;
    worksheet.getCell(rowStartContent + r, 13).value =
      this.TongCapHuyen.vanban_SAISOT;
    worksheet.getCell(rowStartContent + r, 14).value = 
      this.TongCapHuyen.tongSoVBXL;
    worksheet.getCell(rowStartContent + r, 15).value =
      this.TongCapHuyen.tongSoVBXL;
    worksheet.getCell(rowStartContent + r, 16).value =
      this.TongCapHuyen.soHoSoTON;
    worksheet.getCell(rowStartContent + r, 17).value =
      this.TongCapHuyen.soHoSoTONCONHAN;
    worksheet.getCell(rowStartContent + r, 18).value =
      this.TongCapHuyen.soHoSoTONQUAHAN;
    worksheet.getCell(rowStartContent + r, 19).value =
      this.TongCapHuyen.soHoSoDXL_LK;
    worksheet.getCell(rowStartContent + r, 20).value =
      this.TongCapHuyen.soHoSoDXLTrongHan_LK;
    worksheet.getCell(rowStartContent + r, 21).value =
      this.TongCapHuyen.soHoSoDXLQuaHan_LK;
    worksheet.getCell(rowStartContent + r, 22).value =
      this.TongCapHuyen.tongSoVBXL_LK;      
    worksheet.getCell(rowStartContent + r, 23).value =
      this.TongCapHuyen.tongSoVBXL_LK;      
    worksheet.getCell(rowStartContent + r, 24).value =
      this.TongCapHuyen.tongSoVBXL_LK;


      for (let index = 2; index < NumberCol; index++) {
        worksheet.getCell(rowStartContent, index + 1).alignment = {
          horizontal: 'center',
          vertical: 'middle',
        };
        worksheet.getCell(rowStartContent, index + 1).font = {
          size: 11,
          bold: true,
          name: 'Times New Roman',
        };
        worksheet.getCell(rowStartContent, index + 1).border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' },
        };

        worksheet.getCell(rowStartContent - 1, index + 1).alignment = {
          horizontal: 'center',
          vertical: 'middle',
        };
        worksheet.getCell(rowStartContent - 1, index + 1).font = {
          size: 11,
          bold: true,
          name: 'Times New Roman',
        };
        worksheet.getCell(rowStartContent - 1, index + 1).border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' },
        };
      }

      worksheet.getColumn(1).width = 7;
      worksheet.getColumn(1).alignment = {
        horizontal: 'center',
        vertical: 'middle',
        wrapText: true,
      };
      worksheet.getColumn(2).width = 25;
      worksheet.getColumn(2).alignment = {
        horizontal: 'center',
        vertical: 'middle',
        wrapText: true,
      };
      worksheet.getColumn(3).width = 22;

      worksheet.getColumn(2).width = 60;
      worksheet.getColumn(2).alignment = {horizontal: 'left', vertical: 'middle', wrapText: true};
      worksheet.getRow(1).alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
      worksheet.getRow(2).alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
      worksheet.getRow(3).alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
      worksheet.getRow(4).alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
      worksheet.getRow(5).alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
      worksheet.getRow(6).alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
      worksheet.getRow(7).alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
      worksheet.getRow(8).alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
      worksheet.getRow(9).alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
      worksheet.getRow(10).alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
      worksheet.getRow(11).alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};

      // Save Excel File
      workbook.xlsx.writeBuffer().then((dataBuffer: ArrayBuffer) => {
        const blob = new Blob([dataBuffer], { type: this.EXCEL_TYPE });
        fs.saveAs(blob, excelFileName + this.EXCEL_EXTENSION);
      });
    }
  }
}
