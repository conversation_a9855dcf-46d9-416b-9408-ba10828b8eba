import { Injectable } from '@angular/core';
import {HttpClient, HttpHeaders} from '@angular/common/http';
import { ApiProviderService } from 'src/app/core/service/api-provider.service';
import {Observable} from 'rxjs';
import { DeploymentService } from '../deployment.service';
import { SnackbarService } from '../snackbar/snackbar.service';
import { EnvService } from 'src/app/core/service/env.service';

@Injectable({
    providedIn: 'root'
})
export class PadmanService {
    config = this.envService.getConfig();

    constructor(
        private http: HttpClient,
        private apiProviderService: ApiProviderService,
        private deploymentService: DeploymentService,
        private snackbar: SnackbarService,
        private envService: EnvService
    ) { }

    sendHTPV2(code): Observable<any> {
        let headers = new HttpHeaders();
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        headers.append('Access-Control-Allow-Origin', '*');
        let urlDomainVneidLLTP = "/judicial-civil-status/--resend?code=" + code ; 
        console.log("urlDomainVneidLLTP", urlDomainVneidLLTP);
        return this.http.post<any>(this.padman + urlDomainVneidLLTP, { headers }).pipe();
    }

    sendLLTPVNeID(code, requestBody, applyDirect?, enableTransferVneidLltpAgesb?): Observable<any> {
        let headers = new HttpHeaders();
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        headers.append('Access-Control-Allow-Origin', '*');
        let urlDomainVneidLLTP = !!this.deploymentService.getAppDeployment()?.urlDomainVneIDLLTP ? this.deploymentService.getAppDeployment()?.urlDomainVneIDLLTP : "/v2/lyLichTuPhap/re-send-lltp-vneid";
        urlDomainVneidLLTP =  urlDomainVneidLLTP +"?code=" + code + "&applyDirect=" + applyDirect + "&enableTransferVneidLltpAgesb=" + enableTransferVneidLltpAgesb;
        console.log("urlDomainVneidLLTP", urlDomainVneidLLTP);
        return this.http.post<any>(this.padman + urlDomainVneidLLTP, requestBody, { headers }).pipe();
    }

    getVnpostFee(id): Observable<any> {
        let headers = new HttpHeaders();
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        // switch (localStorage.getItem('isLoggedIn')) {
        // case 'false':
        //     const token = localStorage.getItem('OAuth2TOKEN');
        //     headers = headers.append('Authorization', 'Bearer ' + token);
        //     return this.http.get(this.padman + '/dossier-transport-fee?dossier-id=' + id, { headers }).pipe();
        // case 'true':
        //     return this.http.get(this.padman + '/dossier-transport-fee?dossier-id=' + id, { headers }).pipe();
        // }
        return this.http.get(this.padman + '/dossier-transport-fee?dossier-id=' + id, { headers }).pipe();
    }
    getDossierFeeRequired(id, value): Observable<any> {
        let headers = new HttpHeaders();
         headers = headers.set('Accept-Language', localStorage.getItem('language'));
        // switch (localStorage.getItem('isLoggedIn')) {
        // case 'false':
        //     const token = localStorage.getItem('OAuth2TOKEN');
        //     headers = headers.append('Authorization', 'Bearer ' + token);
        //     return this.http.get(this.padman + '/dossier-fee?dossier-id=' + id + value, { headers }).pipe();
        // case 'true':
        //     return this.http.get(this.padman + '/dossier-fee?dossier-id=' + id + value, { headers }).pipe();
        // }
        return this.http.get(this.padman + '/dossier-fee?dossier-id=' + id + value, { headers }).pipe();
    }

    getDossierDetail(id): Observable<any> {
        let headers = new HttpHeaders();
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        return this.http.get(this.padman + '/dossier/' + id + '/--online', { headers }).pipe();
    }

    private padman = this.apiProviderService.getUrl('digo', 'padman');
    private padmanURLReport = this.apiProviderService.getUrl('digo', 'padmanReport');
    private basepad = this.apiProviderService.getUrl('digo', 'basepad');
    private padmanReport  = this.deploymentService.getAppDeployment()?.padmanReport || false;
    // private padman = 'http://localhost:8081';
    updateSync(id: string, requestBody: any): Observable<any> {
        let headers = new HttpHeaders();
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        return this.http.put<any>(this.padman + `/dossier/${id}/--update-sync`, requestBody, { headers }).pipe();
    }
    updateProcedure(id: string, sameDayPayProfile): Observable<any> {
        let headers = new HttpHeaders({
            'Accept-Language': localStorage.getItem('language') || 'en',
            'Content-Type': 'application/json'
        });

        return this.http.put<any>(`${this.padman}/dossier/${id}/--update-procedure-id`+"?sameDayPayProfile="+sameDayPayProfile, { headers }).pipe();
    }


    updateExrateCurrencyDossierFee(dossierid, exratedCurrency): Observable<any> {
        let headers = new HttpHeaders();
        headers = headers.set('Content-Type', 'application/json');
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        return this.http.put(this.padman + '/dossier-fee/' + dossierid + '/--update-currency-rate-dossier-fee?currencyRate=' + exratedCurrency, { headers }).pipe();
    }
    updateDossierProcessing(id,requestBody): Observable<any> {
        let headers = new HttpHeaders();
        headers = headers.set('Content-Type', 'application/json');
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        return this.http.put<any>(this.padman + `/v2/dossiers/${id}/process`, requestBody, { headers });
    }

    getListDossierOfReport(searchString: string): Observable<any> {
        let headers = new HttpHeaders();
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        return this.http.get<any>(this.padman + `/dossier/--counting-detail` +  searchString, { headers }).pipe();
    }

    putChangeStatus(id, requestBody) {
        let headers = new HttpHeaders();
        headers = headers.set('Content-Type', 'application/json');
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        return this.http.put(this.padman + '/dossier/' + id + '/status', requestBody, { headers }).pipe();
      }

    approveExtend(id, requestBody): Observable<any> {
        let headers = new HttpHeaders();
        headers = headers.set('Content-Type', 'application/json');
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        return this.http.put<any>(this.padman + `/dossier/${id}/--approve-extend`, requestBody, { headers });
    }

    getDossierFee(id): Observable<any> {
        let headers = new HttpHeaders();
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        return this.http.get(this.padman + '/dossier-fee?dossier-id=' + id, { headers });
    }

    getDossierStatisticOnl(search): Observable<any> {
        let headers = new HttpHeaders();
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        return this.http.get(this.padman + '/dossier-statistic/statistic-onl' + search, { headers }).pipe();
    }

    getDossierStatisticOnlExtraKHA(search): Observable<any> {
        let headers = new HttpHeaders();
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        headers.append('Content-Type', 'application/json');
        headers.append('Access-Control-Allow-Origin', '*');
        //return this.http.get('http://localhost:8081/dossier-statistic/kha-statistic-onl/--detail' + search, { headers }).pipe();
        return this.http.get(this.padman + '/dossier-statistic/kha-statistic-onl/--detail' + search, { headers }).pipe();
    }

    getDossierStatisticOnlExtraHGG(search): Observable<any> {
        let headers = new HttpHeaders();
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        headers.append('Content-Type', 'application/json');
        headers.append('Access-Control-Allow-Origin', '*');
        //return this.http.get('http://localhost:8081/dossier-statistic/kha-statistic-onl/--detail' + search, { headers }).pipe();
        return this.http.get(this.padman + '/dossier-statistic/statistic-onl-hgg' + search, { headers }).pipe();
    }

    getprocedureLevelByListAgencyHGG(requestBody): Observable<any> {
        let headers = new HttpHeaders();
        headers = headers.set('Content-Type', 'application/json');
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        return this.http.post<any>(this.basepad + `/hgg-procedure/--find-procedure-level-by-list-agency-id`, requestBody, { headers });
    }

    getDossierStatisticOnlineKHA(search): Observable<any> {
        let headers = new HttpHeaders();
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        headers.append('Content-Type', 'application/json');
        headers.append('Access-Control-Allow-Origin', '*');
        //return this.http.get('http://localhost:8081/dossier-statistic/--statistic-online-dossiers' + search, { headers }).pipe();
        return this.http.get(this.padman + '/dossier-statistic/--statistic-online-dossiers' + search, { headers }).pipe();
    }

    getDossierStatisticOnlineExportKHA(search): Observable<any> {
        let headers = new HttpHeaders();
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        headers = headers.append('Content-Type', 'application/json');
        headers.append('Access-Control-Allow-Origin', '*');
        //return this.http.get('http://localhost:8081/dossier-statistic/--statistic-online-dossiers-get-all' + search, { headers }).pipe();
        return this.http.get(this.padman + '/dossier-statistic/--statistic-online-dossiers-get-all' + search, { headers }).pipe();
    }

    exportDossierStatisticOnlExtraKHA(search): Observable<any> {
        let headers = new HttpHeaders();
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        headers.append('Content-Type', 'application/json');
        headers.append('Access-Control-Allow-Origin', '*');
        // return this.http.get('http://localhost:8081/dossier-statistic/kha-statistic-onl/--detail/--export' + search, { headers }).pipe();
        return this.http.get(this.padman + '/dossier-statistic/kha-statistic-onl/--detail/--export' + search, { headers }).pipe();
    }

    getDossierStatisticOnlExtraQNi(search): Observable<any> {
        let headers = new HttpHeaders();
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        return this.http.get(this.padman + '/qni-dossier-statistic-onl/statistic-onl' + search, { headers }).pipe();
    }
    getDossierReportTTPVKSTTHCQNI(search): Observable<any> {
        let headers = new HttpHeaders();
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        return this.http.get(this.padman + '/qni-dossier-statistic-onl/report-TTPVKSTTHC/--dossier/' + search, { headers }).pipe();
    }
    getProcedureReportTTPVKSTTHCQNI(search): Observable<any> {
        let headers = new HttpHeaders();
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        return this.http.get(this.padman + '/qni-dossier-statistic-onl/report-TTPVKSTTHC/--procedure/' + search, { headers }).pipe();
    }

    // minhvnt.dng 05/01/2023 => lấy báo cáo bàn ký điện tử của Quảng Nam
    getDossierSignDeskReport(search): Observable<any> {
        let headers = new HttpHeaders();
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        return this.http.get(this.padman + '/dossier-signdesk/baocao-banky' + search, { headers }).pipe();
    }

    getAGGDossierReturnedReport(search): Observable<any> {
      let headers = new HttpHeaders();
      headers = headers.set('Accept-Language', localStorage.getItem('language'));
      return this.http.get(this.padman + '/agg-dossier-returned/--report' + search, { headers }).pipe();
    }

    exportDossierSignDeskReport(search): Observable<any> {
        let headers = new HttpHeaders();
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        return this.http.get(this.padman + '/dossier-signdesk/--export' + search, { headers }).pipe();
    }

    exportAGGDossierReturnedReport(search): Observable<any> {
      let headers = new HttpHeaders();
      headers = headers.set('Accept-Language', localStorage.getItem('language'));
      return this.http.get(this.padman + '/agg-dossier-returned/--export' + search, { headers }).pipe();
  }

    getDigitizationDossierReport(search): Observable<any> {
        let headers = new HttpHeaders();
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        return this.http.get(this.padman + '/dbn-digitize/digitization-by-agency' + search, { headers }).pipe();
    }

    getDigitizationReportLDG(search): Observable<any> {
        let headers = new HttpHeaders();
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        return this.http.get(this.padman + '/statistics-ldg/digitization-by-agency' + search, { headers }).pipe();
    }

    getDetailDigitizationLDG(search): Observable<any> {
        let headers = new HttpHeaders();
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        return this.http.get(this.padman + '/statistics-ldg/digitization-detail' + search, { headers }).pipe();
    }

    getDigitizationDossierReportAGG(search): Observable<any> {
        let headers = new HttpHeaders();
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        return this.http.get(this.padman + '/agg-digitize/digitization-by-agency' + search, { headers }).pipe();
    }

    getDigitizationReportV2AGG(search): Observable<any> {
        let headers = new HttpHeaders();
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        return this.http.get(this.padman + '/agg-digitize-report-v2' + search, { headers }).pipe();
    }

    //KGG OS
    getDigitizationDossierReportKGG(search): Observable<any> {
      let headers = new HttpHeaders();
      headers = headers.set('Accept-Language', localStorage.getItem('language'));
      return this.http.get(this.padman + '/kgg-digitize/digitization-by-agency' + search, { headers }).pipe();
    }
    //END KGG OS

    // minhvnt.dng 20/11/2022 => l?y danh s�ch d?t l?ch h?n giao d?ch c?a Qu?ng Nam
    getScheduleQnm(search): Observable<any> {
        let headers = new HttpHeaders();
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        return this.http.get(this.padman + '/dac-thu-qnm/ds-dat-lich' + search, { headers }).pipe();
    }

    getDetailDossier(search): Observable<any> {
        let headers = new HttpHeaders();
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        return this.http.get(this.padman + '/dbn-digitize/digitization-detail' + search, { headers }).pipe();
    }

    getDetailDossierAGG(search): Observable<any> {
        let headers = new HttpHeaders();
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        return this.http.get(this.padman + '/agg-digitize/digitization-detail' + search, { headers }).pipe();
    }

    getDetailDigitizationReportV2AGG(search): Observable<any> {
        let headers = new HttpHeaders();
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        return this.http.get(this.padman + '/agg-digitize-report-v2/dossier-detail' + search, { headers }).pipe();
    }

    getDetailDigitizationReportV2AGGToExportExcel(search): Observable<any> {
        let headers = new HttpHeaders();
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        return this.http.get(this.padman + '/agg-digitize-report-v2/dossier-detail/--excel' + search, { headers }).pipe();
    }


    //KGG OS
    getDetailDossierKGG(search): Observable<any> {
      let headers = new HttpHeaders();
      headers = headers.set('Accept-Language', localStorage.getItem('language'));
      return this.http.get(this.padman + '/kgg-digitize/digitization-detail' + search, { headers }).pipe();
    }
    //END KGG OS

    getDigitizationDossierDetailExport(search): Observable<any> {
        let headers = new HttpHeaders();
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        return this.http.get(this.padman + '/dbn-digitize/digitization-detail/--excel' + search, { headers }).pipe();
    }

    getDigitizationQniDossierReport(search): Observable<any> {
        let headers = new HttpHeaders();
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        return this.http.get(this.padman + '/qni-digitize/digitization-by-agency' + search, { headers }).pipe();
    }

    getDetailDossierQniDigitization(search): Observable<any> {
        let headers = new HttpHeaders();
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        return this.http.get(this.padman + '/qni-digitize/digitization-detail' + search, { headers }).pipe();
    }

    getDossierStatisticOnlQBH(search): Observable<any> {
        let headers = new HttpHeaders();
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        return this.http.get(this.padman + '/dossier-statistic-qbh/statistic-onl-qbh' + search, { headers }).pipe();
    }

    postKetQuaDanhGiaDonVi(requestBody): Observable<any> {
        let headers = new HttpHeaders();
        headers = headers.set('Content-Type', 'application/json');
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        return this.http.post<any>(this.padman + `/qbh-danhgia-tthc/ketqua-danhgia-donvi`, requestBody, { headers });
    }

    postThamDinhKetQuaDanhGiaDonVi(requestBody): Observable<any> {
        let headers = new HttpHeaders();
        headers = headers.set('Content-Type', 'application/json');
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        return this.http.post<any>(this.padman + `/qbh-danhgia-tthc/thamdinh-danhgia-donvi`, requestBody, { headers });
    }

    getListKetQuaDanhGiaDonVi(searchString): Observable<any> {
        let headers = new HttpHeaders();
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        return this.http.get(this.padman + '/qbh-danhgia-tthc/search' + searchString, {headers});
    }

    listKetQuaThamDinhDanhGiaDonVi(searchString): Observable<any> {
        let headers = new HttpHeaders();
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        return this.http.get(this.padman + '/qbh-danhgia-tthc/danhsach-thamdinh' + searchString, {headers});
    }

    getKetQuaDanhGiaDonViId(id): Observable<any> {
        let headers = new HttpHeaders();
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        return this.http.get(this.padman + '/qbh-danhgia-tthc/get-ketQua-donVi/' + id, {headers});
    }

    checkExitKetQuaDanhGiaDonVi(searchString): Promise<any> {
        let headers = new HttpHeaders();
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        return this.http.get(this.padman + '/qbh-danhgia-tthc/check-exit-ketQua-danhGia-donVi' + searchString, {headers}).toPromise();
    }

    updateDossierExtend(dossierId: string, requestBody): Observable<any> {
        let headers = new HttpHeaders();
        headers = headers.set('Content-Type', 'application/json');
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        return this.http.put<any>(this.padman + '/lgsp-hbh/' + dossierId + '/updateDossierExtend', requestBody, { headers }).pipe();
    }

    getCmuDigitizationDossierReport(params:any): Observable<any> {
        let headers = new HttpHeaders();
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        //return this.http.get("http://localhost:8081" + '/cmu-dossier-statistic/--dossier-fee-statistic-cmu', { headers,params });
        return this.http.get(this.padman + '/cmu-dossier-statistic/--dossier-fee-statistic-cmu', { headers,params });
    }
    getDossierStatisticOnlExtraCMU(search): Observable<any> {
        let headers = new HttpHeaders();
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        return this.http.get(this.padman + '/cmu-dossier-statistic/statistic-onl' + search, { headers }).pipe();
    }

    sendVNPostResult(search, requestBody) {
        let headers = new HttpHeaders();
        headers = headers.set('Content-Type', 'application/json');
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        headers.append('Access-Control-Allow-Origin', '*');
        return this.http.put<any>(this.padman + search, requestBody, { headers });
    }

    getDossierTracking(id): Observable<any> {
        let headers = new HttpHeaders();
        headers = headers.set('Content-Type', 'application/json');
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        headers.append('Access-Control-Allow-Origin', '*');
        return this.http.get(this.padman + '/dossier-tracking/--find-by-dossier-id?dossier-id=' + id, { headers }).pipe();
    }

    getBcQd06(search): Observable<any> {
      let headers = new HttpHeaders();
      headers = headers.set('Accept-Language', localStorage.getItem('language'));
      return this.http.get(this.padman + '/dvclt-dossier/bc-qd-06' + search, { headers }).pipe();
    }

    getBcQd06Huyen(requestBody) {
      let headers = new HttpHeaders();
      headers = headers.set('Content-Type', 'application/json');
      headers = headers.set('Accept-Language', localStorage.getItem('language'));
      headers.append('Access-Control-Allow-Origin', '*');
      return this.http.post<any>(this.padman + '/dvclt-dossier/bc-qd-06-huyen', requestBody, { headers });
    }

    getDetailTTHC(searchString): Observable<any> {
        let headers = new HttpHeaders();
        headers = headers.set('Content-Type', 'application/json');
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        headers.append('Access-Control-Allow-Origin', '*');
        return this.http.get((!this.padmanReport ? this.padman : this.padmanURLReport) + '/dossier/search-detail-tthc' + searchString, { headers }).pipe();
    }

    getDLKChiThi18(requestBody): Observable<any> {
      let headers = new HttpHeaders();
      headers = headers.set('Content-Type', 'application/json');
      headers = headers.set('Accept-Language', localStorage.getItem('language'));
      return this.http.post<any>((!this.padmanReport ? this.padman : this.padmanURLReport) + `/dlk-chithi18/dlk-ct18`, requestBody, { headers });
    }

    getDLKChiThi18ChiTiet(search): Observable<any> {
      let headers = new HttpHeaders();
      headers = headers.set('Content-Type', 'application/json');
      headers = headers.set('Accept-Language', localStorage.getItem('language'));
      return this.http.get(this.padman + `/dlk-chithi18/--ct18chitiet` + search, { headers }).pipe();
    }
    getStatisticsDVCLTByAgency(searchParams): Observable<any> {
        let headers = new HttpHeaders();
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        return this.http.get(this.padman + '/dvclt-dossier/statistic/--by-agency', {params: searchParams}).pipe();
    }
    getStatisticsDVCLTByAgencyKTM(searchParams): Observable<any> {
        let headers = new HttpHeaders();
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        return this.http.get(this.padman + '/dvclt-dossier/statistic/--by-agency-ktm', {params: searchParams}).pipe();
        }
    getDossierTax(searchString): Observable<any> {
        let headers = new HttpHeaders();
        headers = headers.set('Content-Type', 'application/json');
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        headers.append('Access-Control-Allow-Origin', '*');
         return this.http.get<any>(this.padman  + '/ktm-tax/--detail' + searchString, {headers}).pipe();
    }
    getDossierTaxByAgency(searchString): Observable<any> {
        let headers = new HttpHeaders();
        headers = headers.set('Content-Type', 'application/json');
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        headers.append('Access-Control-Allow-Origin', '*');
         return this.http.get<any>(this.padman  + '/ktm-tax/--count' + searchString, {headers}).pipe();
    }

    getProcedureReportTTPVKSTTHCQNM(search, body): Observable<any> {
        let headers = new HttpHeaders();
        headers = headers.set('Content-Type', 'application/json');
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        headers.append('Access-Control-Allow-Origin', '*');
        // return this.http.post('http://localhost:8081/qnm-dossier-statistic/report-ttpvkstthc-procedure' + search, body, { headers }).pipe();
        return this.http.post(this.padman + "/qnm-dossier-statistic/report-ttpvkstthc-procedure" + search, body, { headers }).pipe();
    }

    getDossierReportTTPVKSTTHCQNM(search, body): Observable<any> {
        let headers = new HttpHeaders();
        headers = headers.set('Content-Type', 'application/json');
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        headers.append('Access-Control-Allow-Origin', '*');
        // return this.http.post('http://localhost:8081/qnm-dossier-statistic/report-ttpvkstthc-dossier' + search, body, { headers }).pipe();
        return this.http.post(this.padman + "/qnm-dossier-statistic/report-ttpvkstthc-dossier" + search, body, { headers }).pipe();
    }

    getListLogAutoReceiveDossierQBH(searchString): Observable<any> {
        let headers = new HttpHeaders();
        headers = headers.set('Content-Type', 'application/json');
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        return this.http.get<any>(this.padman  + '/qbh-log-auto-receive-dossier/search' + searchString, {headers});
    }
    autoReceiveDossierQBH(searchString): Observable<any> {
        let headers = new HttpHeaders();
        headers = headers.set('Content-Type', 'application/json');
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        return this.http.get<any>(this.padman  + '/dossier/list-dossier-new-register-QBH' + searchString, {headers});
    }
    getAggDossierBudget(search): Observable<any> {
        let headers = new HttpHeaders();
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        return this.http.get(this.padman + '/agg-dossier-budget/search' + search, { headers }).pipe();
    }
    getBusinessRegisterInfo(dossierId): Observable<any> {
        let headers = new HttpHeaders();
        headers = headers.set('Content-Type', 'application/json');
        headers = headers.append('Authorization', 'Bearer ' + localStorage.getItem('OAuth2TOKEN'));
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        //return this.http.get<any>("http://localhost:8081/dossier-nbrs/" + dossierId, { headers }).pipe();
        return this.http.get(this.padman + '/dossier-nbrs/' + dossierId, { headers }).pipe();
    }

    getStatisticData(searchString): Observable<any> {
        let headers = new HttpHeaders();
        headers = headers.set('Content-Type', 'application/json');
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        return this.http.get((!this.padmanReport ? this.padman : this.padmanURLReport) + '/dossier-statistic/--public' + searchString, { headers }).pipe();
      }

    getDossierStatisticOnlGeneralQBH(search): Observable<any> {
        let headers = new HttpHeaders();
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        return this.http.get(this.padman + '/qbh-statistic-dvc/--dvconline-general-qbh' + search, { headers }).pipe();
    }

    getDossierListGTVTDB(searchString): Observable<any>{
        let headers = new HttpHeaders();
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        return this.http.get(this.padman + '/agg-hoso-gtvt/search' + searchString, { headers }).pipe();
    }

    putStatusLogVNPost5343(body): Observable<any> {
        let headers = new HttpHeaders();
        headers = headers.set('Content-Type', 'application/json');
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        return this.http.put(this.padman + '/vnpost-5343/--status-log', body, { headers }).pipe();
    }


    getAggHoSoDkkd(search): Observable<any> {
        let headers = new HttpHeaders();
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        return this.http.get(this.padman + '/agg-hoso-dkkd/search' + search, { headers }).pipe();
    }
    GetStatusLogVNPost5343(id): Observable<any> {
        let headers = new HttpHeaders();
        headers = headers.set('Content-Type', 'application/json');
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        return this.http.get(this.padman + '/vnpost-5343/'+ id +'/--check-log', { headers }).pipe();
    }

    getScheduleQbh(search): Observable<any> {
        let headers = new HttpHeaders();
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        return this.http.get(this.padman + '/v2/qbh-dossier-statistic/ds-dat-lich' + search, { headers }).pipe();
    }

    initQRPaymentDvc(body, isDNI, isProcNameShortened): Observable<any> {
        let headers = new HttpHeaders();
        headers = headers.set('Content-Type', 'application/json');
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        if(localStorage.getItem('isLoggedIn') === 'false')
          headers = headers.append('Authorization', 'Bearer ' + localStorage.getItem('OAuth2TOKEN'));
        return this.http.post<any>(this.padman + '/dossier-payment/--create-payment-dvc-ktm?is-dni=' + isDNI + '&is-shortened=' + isProcNameShortened, body, { headers }).pipe();
    }

    checkDossierPayment(id: any): Observable<any> {
        let headers = new HttpHeaders();
        headers = headers.set('Content-Type', 'application/json');
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        if(localStorage.getItem('isLoggedIn') === 'false')
          headers = headers.append('Authorization', 'Bearer ' + localStorage.getItem('OAuth2TOKEN'));
        return this.http.post<any>(this.padman + '/dossier-payment/--check-payment-status?id=' + id, { headers }).pipe();
    }

    sendSoHoaHBH(code): Observable<any> {
        let headers = new HttpHeaders();
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        headers.append('Access-Control-Allow-Origin', '*');
        let url = "/lgsp-hbh/--sendSoHoa";
        url =  url +"?code=" + code ;
        console.log("urlDomainVneidLLTP", url);
        //return this.http.post<any>("http://localhost:8081" + url, {}, { headers }).pipe();
        return this.http.post<any>(this.padman + url, {}, { headers }).pipe();
    }

    dashboardRating(code): Observable<any> {
        let headers = new HttpHeaders();
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        if(localStorage.getItem('isLoggedIn') === 'false')
            headers = headers.append('Authorization', 'Bearer ' + localStorage.getItem('OAuth2TOKEN'));
        return this.http.get(this.padman + '/dossier-btttt/--statistic-rating' + code, {headers}).pipe();
      }
      getReportNearDueLDG(search): Observable<any> {
        let headers = new HttpHeaders();
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        return this.http.get(this.padman + '/statistics-ldg/dossier-near-due' + search, { headers }).pipe();
    }
    getDetailReportNearDueLDG(search): Observable<any> {
        let headers = new HttpHeaders();
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        return this.http.get(this.padman + '/statistics-ldg/dossier-near-due-detail' + search, { headers }).pipe();
    }

    exportExcelDossier(type, year, agency, procedure): Promise<any> {
        let url = this.padman + `/dossier-merge/`;
        let filename = '';
        switch(type){
            case 1: {
                url += `--export-dossier`;
                filename = 'du_lieu_ho_so.xlsx';
                break;
            }
            case 2: {
                url += `--export-dossier-fee`;
                filename = 'du_lieu_phi_ho_so.xlsx';
                break;
            }
            case 3: {
                url += `--export-dossier-form-file`;
                filename = 'du_lieu_thanh_phan_ho_so.xlsx';
                break;
            }
        }
        return new Promise((resolve) => {
            this.http.get(url + `?year=${year}&agency=${agency}&procedure=${procedure}`, {
                observe: 'response',
                responseType: 'blob'
            }).toPromise().then(res => {
                const blob = new Blob([res.body], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'});
                let filename = 'du_lieu_ho_so.xlsx';
                if (res.headers.get('content-disposition') != null) {
                filename = res.headers.get('content-disposition').split(';')[1].split('=')[1].replace(/"/g, '');
                }
                const blobUrl = URL.createObjectURL(blob);
                const xhr = new XMLHttpRequest();
                xhr.responseType = 'blob';

                xhr.onload = () => {
                const recoveredBlob = xhr.response;
                const reader = new FileReader();
                reader.onload = () => {
                    const base64data = reader.result.toString();
                    const anchor = document.createElement('a');
                    anchor.download = filename;
                    anchor.href = base64data;
                    anchor.click();
                };
                reader.readAsDataURL(recoveredBlob);
                };

                xhr.open('GET', blobUrl);
                xhr.send();
                resolve(true);
            }).catch(err => {
                if (err.status === 500) {
                const message = {
                    vi: 'Hệ thống tạm thời không thể xuất file excel!',
                    en: 'The system is temporarily unable to export the excel file!'
                };
                this.snackbar.openSnackBar(0, '', message[localStorage.getItem('language')], 'error_notification', this.config.expiredTime);
                }
                resolve(false);
            });
        });
    }

    importExcelDossier(formData): Observable<any> {
        let headers = new HttpHeaders();
        // headers = headers.set('Content-Type', 'multipart/form-data');
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        return this.http.post<any>(this.padman + '/dossier-merge/--import-dossier', formData, { headers });
    }
}
