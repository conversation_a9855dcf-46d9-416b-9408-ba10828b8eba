
export interface ElementProcessingStatistic {
    stt: string;
    agencyId: string;
    agencyName: string,
    implementationDate?: string;
    totalDossier: number;   // tongSoHoSo
    totalProcedure: number;   // tongSoThuTuc
    dossierLevel2: number;   // hoSoTiepNhanMucDo2
    dossierBCCILevel2: number;   // hoSoTiepNhanBCCIMucDo2
    procedureLevel2: number; // thuTucMucDo2
    dossierLevel3: number;   // hoSoTiepNhanTrucTiepMucDo3
    dossierOnlineLevel3: number;   // hoSoTiepNhanTrucTuyenMucDo3
    dossierBCCILevel3: number;   // hoSoTiepNhanBCCIMucDo3
    procedureLevel3: number; // thuTucMucDo3
    dossierLevel4: number;   // hoSoTiepNhanTrucTiepMucDo4
    dossierOnlineLevel4: number;   // hoSoTiepNhanTrucTuyenMucDo4
    dossierBCCILevel4: number;   // hoSoTiepNhanBCCIMucDo4
    procedureLevel4: number; // thuTucMucDo4
    dossierExist: number;          // hoSoTon
    dossierReceived: number;    // hoSoDaTiepNhan
    dossierOnlineReceived: number;   // hoSoTrucTuyenDaTiepNhan
    resolvedEarlyLevel2: number;       // hoSoDaGiaiQuyetTruocHanMucDo2
    resolvedOnTimeLevel2: number;          // hoSoDaGiaiQuyetDungHanMucDo2
    resolvedOverdueLevel2: number;   // hoSoDaGiaiQuyetTreHanMucDo2
    resolvedEarlyLevel3: number;       // hoSoDaGiaiQuyetTruocHanMucDo3
    resolvedOnTimeLevel3: number;          // hoSoDaGiaiQuyetDungHanMucDo3
    resolvedOverdueLevel3: number;   // hoSoDaGiaiQuyetTreHanMucDo3
    resolvedEarlyLevel4: number;      // hoSoDaGiaiQuyetTruocHanMucDo4
    resolvedOnTimeLevel4: number;          // hoSoDaGiaiQuyetDungHanMucDo4
    resolvedOverdueLevel4: number;   // hoSoDaGiaiQuyetTreHanMucDo4
    unresolved: number;        // dangGiaiQuyet
    resolveRate: number;  // tyLeGiaiQuyet
    note? : string; // ghiChu
    resolved? : number; // tongHoSoDaXuLy
}

export interface ElementDigitizationStatistic {
    stt: number;
    agencyId: string;
    agencyName: string;
    totalReceiverDossier: number;
    totalDossierNotDigitized: number;
    totalDossierDigitized: number;
    digitizedRate: any;
    fullDigitizedDossier: number;
    fullDigitizedRate: any;
    totalResolvedDossier: number;
    totalDossierNotDigitizedResolutionResult: number;
    totalDossierDigitizedResolutionResult: number;
    digitizedResolutionResultRate: any;
    totalDossierDigitizedAndResolutionResult: number;
    digitizedRateAndResolutionResultRate: any;
  }

export interface Element6b {
    sectorId: string;
    stt: string;
    sectorName: string;
    agencyLevelId: string;
    child: any;
    isGroup: boolean;
}

export interface Element6bExport {
    stt: string;
    sectorName: string;
    received: number;          // tiepNhanTrongKy
    receivedOnline: number;    // tiepNhanTrucTuyen
    receivedOld: number;       // kyTruocChuyenSang
    receivedDirect: number;    // tiepNhanTrucTiep
    resolved: number;          // daXuLy
    resolvedOnTime: number;    // daXuLyDungHan
    resolvedOverdue: number;   // daXuLyQuaHan
    unresolved: number;        // dangXuLy
    unresolvedOnTime: number;  // dangXuLyTrongHan
    unresolvedOverdue: number; // dangXuLyQuaHan
    resolvedOneGate: number;   // daXuLyMotCua
    resolvedOnTimeOneGate: number;   // daXuLyDungHanMotCua
    resolvedOverTimeOneGate: number;   // daXuLyQuaHanMotCua
    unresolvedOneGate: number;   // dangXuLyMotCua
}

export interface Element6d {
    sectorId: string;
    stt: string;
    sectorName: string;
    agencyLevelId: string;
    agencyLevelName: string;
    child: any;
    isGroup: boolean;
}

export interface Element6dExport {
    stt: string;
    sectorName: string;
    received: number;          // tiepNhanTrongKy
    receivedOnline: number;    // tiepNhanTrucTuyen
    receivedOld: number;       // kyTruocChuyenSang
    receivedDirect: number;    // tiepNhanTrucTiep
    resolved: number;          // daXuLy
    resolvedOnTime: number;    // daXuLyDungHan
    resolvedOverdue: number;   // daXuLyQuaHan
    unresolved: number;        // dangXuLy
    unresolvedOnTime: number;  // dangXuLyTrongHan
    unresolvedOverdue: number; // dangXuLyQuaHan
    resolvedOneGate: number;   // daXuLyMotCua
    resolvedOnTimeOneGate: number;   // daXuLyDungHanMotCua
    resolvedOverTimeOneGate: number;   // daXuLyQuaHanMotCua
    unresolvedOneGate: number;   // dangXuLyMotCua
}

export interface Element6dExpand {
    stt: string;
    sectorName: string;
    received: number;          // tiepNhanTrongKy
    receivedOnline: number;    // tiepNhanTrucTuyen
    receivedDirect: number;    // tiepNhanTrucTiep
    receivedOld: number;       // kyTruocChuyenSang
    resolved: number;          // daXuLy
    resolvedEarly: number;     // daXuLyTruocHan
    resolvedOnTime: number;    // daXuLyDungHan
    resolvedOverdue: number;   // daXuLyQuaHan
    unresolved: number;        // dangXuLy
    unresolvedOnTime: number;  // dangXuLyTrongHan
    unresolvedOverdue: number; // dangXuLyQuaHan
    resolvedOneGate: number;   // daXuLyMotCua
    unresolvedOneGate: number;   // dangXuLyMotCua
    resolvedOnTimeOneGate: number;   // daXuLyDungHanMotCua
    resolvedOverTimeOneGate: number;   // daXuLyQuaHanMotCua
}

export interface Element6gOver {
    stt: string;
    sectorName: string;
    unresolvedOverdue: number; // dangXuLyQuaHan
    reason: string; // dangXuLyQuaHan
    note: string; // dangXuLyQuaHan
}

export interface Element6gProcedure {
    stt: string;
    procedureName: string;
    content: string;
    document: string;
    child: any;
    isGroup: boolean;
}

export interface Element6gProcedureExpand {
    stt: string;
    procedureName: string;
    content: string;
    document: string;
}