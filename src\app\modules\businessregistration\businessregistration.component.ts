import { Component, OnInit, ViewChild, AfterViewInit, ChangeDetectorRef } from '@angular/core';
import { FormGroup, FormControl } from '@angular/forms';
import { MatPaginator } from '@angular/material/paginator';
import { MatTableDataSource } from '@angular/material/table';
import { MatSort } from '@angular/material/sort';
import { MainService } from 'src/app/data/service/main/main.service';
import { BusinessregistrationService } from 'src/app/data/service/businessregistration/businessregistration.service';
import { Router } from '@angular/router';
import { KeycloakService } from 'keycloak-angular';
import { EnvService } from 'src/app/core/service/env.service';
import { DatePipe } from '@angular/common';
import { SnackbarService } from 'src/app/data/service/snackbar/snackbar.service';
import { DetailsComponent, BusinessRegistrationDetailsDialogModel } from 'src/app/modules/businessregistration/dialogs/details/details.component';
import { MatDialog } from '@angular/material/dialog';
import { DeploymentService } from 'src/app/data/service/deployment.service';
import {MatDatepickerInputEvent} from '@angular/material/datepicker';
export interface FormElement {
  files: any;
  id: number;
}

@Component({
  selector: 'app-businessregistration',
  templateUrl: './businessregistration.component.html',
  styleUrls: ['./businessregistration.component.scss', '/src/app/app.component.scss', '/src/app/shared/scss/form-field-outline.scss']
})
export class BusinessregistrationComponent implements OnInit, AfterViewInit {

  isLoggedIn = (localStorage.getItem('isLoggedIn') === 'true');
  // private exportTime = { hour: 7, minute: 15, meriden: 'PM', format: 24 };
  config = this.envService.getConfig();
  // tslint:disable-next-line: max-line-length
  displayedColumns: string[] = ['stt', 'inJournalNo', 'documentType', 'enterpriseCode', 'enterpriseGDTCode', 'name', 'siteID', 'receiptDate', 'planDate', 'processStatus','options'];
  // tslint:disable-next-line: max-line-length
  displayedColumns1: string[] = ['stt', 'inJournalNo', 'documentType', 'enterpriseGDTCode', 'nameTime', 'siteID', 'processStatus', 'processedDate'];
  ELEMENTDATA: FormElement[] = [];
  dataSource: MatTableDataSource<FormElement>;
  ELEMENTDATA1: FormElement[] = [];
  dataSource1: MatTableDataSource<FormElement>;
  showDelay = new FormControl(1000);
  hideDelay = new FormControl(2000);
  request0 = '';
  request1 = '';
  request2 = '';
  result: boolean;
  pageSizes = this.config.pageSizeOptions;
  totalElements: number;
  totalPages: number;
  selectedPageSize: number;
  currentPage: number;
  offset: number;
  listFiles = [];
  timeout: any = null;
  isExpanded = false;
  type = 0;
  selected = 3;
  selectedLang: string;

  mainInformation: any[];
  representatives: any[];
  hoadress: any[];
  businessActivities: any[];
  member: any[];
  membercheck: any = true;
  mainInformationcheck: any = true;
  representativescheck: any = true;
  hoadresscheck: any = true;
  businessActivitiescheck: any = true;

  size = 10;
  pageIndex = 1;
  page = 1;
  countResult = 0;
  countResult1 = 0;
  pgSizeOptions = this.config.pageSizeOptions;

  maxToDate = new Date();
  maxFromDate = new Date();
  searchForm = new FormGroup({
    id: new FormControl(''),
    fromDate: new FormControl(new Date(new Date().getTime() - 4 * 86400000)),
    toDate: new FormControl(new Date()),
    fromTime: new FormControl(''),
    toTime: new FormControl('')
  });
  env = this.deploymentService.getAppDeployment()?.env;

  formObj: any;
  @ViewChild(MatPaginator, { static: true }) paginator: MatPaginator;
  @ViewChild(MatSort, { static: true }) sort: MatSort;

  constructor(
    private service: BusinessregistrationService,
    private main: MainService,
    private cdRef: ChangeDetectorRef,
    private keycloak: KeycloakService,
    private mainService: MainService,
    private router: Router,
    private envService: EnvService,
    private datepipe: DatePipe,
    private snackbarService: SnackbarService,
    private dialog: MatDialog,
    private deploymentService: DeploymentService
  ) {
    this.dataSource = new MatTableDataSource(this.ELEMENTDATA);
    this.dataSource1 = new MatTableDataSource(this.ELEMENTDATA1);
  }

  ngOnInit(): void {
    if (Number(localStorage.getItem('languageId')) === 46) {
      this.request0 = 'Please enter the business code to perform a detailed business information lookup.';
      this.request1 = 'Please select a search period not to exceed 5 days.';
      this.request2 = 'Please select a start and end time not to exceed 24 hours.';
    }
    else {
      this.request0 = 'Vui lòng nhập mã số doanh nghiệp để thực hiện tra cứu thông tin chi tiết của doanh nghiệp.';
      this.request1 = 'Vui lòng lựa chọn khoảng thời gian tìm kiếm không quá 5 ngày.';
      this.request2 = 'Vui lòng chọn thời gian bắt đầu và kết thúc không quá 24 giờ.';
    }
    this.selectedLang = localStorage.getItem('language');
    this.selected = 3;
  }

  onKeySearch(type) {
    const formObj = this.searchForm.getRawValue();
    const fromDateParse = formObj.fromDate;
    const toDateParse = formObj.toDate;
    this.type = type;
    if (type === 1) {
      const msgObj = {
        vi: 'Vui lòng lựa chọn thời gian từ ngày đến ngày.',
        en: 'Please choose "from date" and "to date" to find the result.'
      };
      const msgObj1 = {
        vi: 'Vui lòng lựa chọn từ ngày nhỏ hơn đến ngày.',
        en: 'Please choose "from date" less than "to date".'
      };
      const msgObj2 = {
        vi: 'Vui lòng lựa chọn khoảng thời gian từ ngày và đến ngày không quá 5 ngày.',
        en: 'Please select a period between "from date" and "to date" does not exceed 5 days.'
      };
      if (formObj.fromDate == null || formObj.toDate == null || formObj.fromDate === '' || formObj.toDate === '') {
        this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
      } else {
        if (fromDateParse > toDateParse) {
          this.snackbarService.openSnackBar(0, msgObj1[this.selectedLang], '', 'error_notification', this.config.expiredTime);
        } else {
          if ((toDateParse - fromDateParse) / (1000 * 60 * 60 * 24) > 5) {
            this.snackbarService.openSnackBar(0, msgObj2[this.selectedLang], '', 'error_notification', this.config.expiredTime);
          } else {
            this.executeListing(type);
          }
        }
      }
    } else if (type === 0) {
      if (formObj.id === '' || formObj.id === null) { } else {
        if (formObj.id.length <= 15) {
          this.executeListing(type);
        } else {
          const msgObj = {
            vi: 'Vui lòng nhập ít hơn 15 ký tự.',
            en: 'Please enter less than 15 character.'
          };
          this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
        }
      }
    } else {
      const msgObj = {
        vi: 'Vui lòng lựa chọn thời gian bắt đầu và kết thúc.',
        en: 'Please select a start and end time.'
      };
      const msgObj1 = {
        vi: 'Vui lòng lựa chọn thời gian bắt đầu nhỏ hơn thời gian kết thúc.',
        en: 'Please select the start time to be less than the end time.'
      };
      if (formObj.fromTime === '' || formObj.fromTime === null || formObj.toTime === '' || formObj.toTime === null) {
        this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
      } else {
        const arrStart = formObj.fromTime.split(':');
        const arrEnd = formObj.toTime.split(':');
        if (arrStart[0] > arrEnd[0]) {
          this.snackbarService.openSnackBar(0, msgObj1[this.selectedLang], '', 'error_notification', this.config.expiredTime);
        } else if (arrStart[0] === arrEnd[0] && arrStart[1] > arrEnd[1]){
          this.snackbarService.openSnackBar(0, msgObj1[this.selectedLang], '', 'error_notification', this.config.expiredTime);
        }else {
          this.executeListing(type);
        }
      }
    }
  }

  executeListing(type) {
    const formObj = this.searchForm.getRawValue();
    let configId = '';
    // if (this.env?.businessRegistration && this.env?.businessRegistration?.configId != null && this.env?.businessRegistration?.configId != '') {
    //   configId = '?config-id=' + this.env?.businessRegistration?.configId;
    // } else {
    //   configId = '?agency-id=' + this.config.rootAgency.id + '&subsystem-id=' + this.config.subsystemWebPadsvcId;
    // }
    configId = '?agency-id=' + this.config.rootAgency.id + '&subsystem-id=' + this.config.subsystemId;

    if (type === 1) {
      if (formObj.fromDate !== '' && formObj.toDate !== '') {
        (document.querySelector('.hidden') as HTMLElement).style.display = 'none';
        
        this.search(configId, 0, this.config.pageSizeOptions[1], type);
        setTimeout(() => {
          if (this.totalElements !== 0) {
            (document.querySelector('.hidden2') as HTMLElement).style.display = 'block';
            (document.querySelector('#main') as HTMLElement).style.display = 'block';
          }
          else {
            (document.querySelector('.hidden2') as HTMLElement).style.display = 'none';
          }
          (document.querySelector('.hidden') as HTMLElement).style.display = 'block';
          (document.querySelector('#main') as HTMLElement).style.display = 'block';
        }, 500);
      }
      else {
        (document.querySelector('.hidden') as HTMLElement).style.display = 'none';
        (document.querySelector('#main') as HTMLElement).style.display = 'none';
      }
    } else if (type === 0) {
      if (formObj.id === '' || formObj.id === null) {
        (document.querySelector('.hidden') as HTMLElement).style.display = 'none';
        (document.querySelector('#main') as HTMLElement).style.display = 'none';
      }
      else {
        (document.querySelector('.hidden') as HTMLElement).style.display = 'none';
        (document.querySelector('#main') as HTMLElement).style.display = 'none';
        this.search(configId, 0, this.config.pageSizeOptions[1], type);
        setTimeout(() => {
          if (this.totalElements !== 0) {
            (document.querySelector('.hidden2') as HTMLElement).style.display = 'block';
          }
          else {
            (document.querySelector('.hidden2') as HTMLElement).style.display = 'none';
          }
          (document.querySelector('.hidden') as HTMLElement).style.display = 'block';
          (document.querySelector('#main') as HTMLElement).style.display = 'block';
        }, 500);
      }
    } else {
      if (formObj.fromTime === '' || formObj.fromTime === null || formObj.toTime === '' || formObj.toTime === null) {
        (document.querySelector('.hidden') as HTMLElement).style.display = 'none';
        (document.querySelector('#main') as HTMLElement).style.display = 'none';
      } else {
        (document.querySelector('.hidden') as HTMLElement).style.display = 'none';
        (document.querySelector('#main') as HTMLElement).style.display = 'none';
        this.search(configId, 0, this.config.pageSizeOptions[1], type);
        setTimeout(() => {
          if (this.totalElements !== 0) {
            (document.querySelector('.hidden2') as HTMLElement).style.display = 'block';
          }
          else {
            (document.querySelector('.hidden2') as HTMLElement).style.display = 'none';
          }
          (document.querySelector('.hidden') as HTMLElement).style.display = 'block';
          (document.querySelector('#main') as HTMLElement).style.display = 'block';
        }, 500);
      }
    }
  }

  search(configId, page, size, type) {
    if(configId !== null && configId !== '')
    {
      configId += '&spec=page&size=' + size + '&page=' + page;
    }
    let a = 0;
    if (this.isLoggedIn) {
      a = 1;
    }
    if (type === 1) {
      const formObj = this.searchForm.getRawValue();
      const dataPost: any = {};
      dataPost.from_date = this.datepipe.transform(formObj.fromDate, 'dd/MM/yyyy').toString();
      dataPost.to_date = this.datepipe.transform(formObj.toDate, 'dd/MM/yyyy').toString();
      dataPost.offset = page;
      dataPost.limit = size;
      const fromPage = page * size;
      const toPage = (page + 1) * size;
      this.service.postDetails(configId, dataPost).subscribe(item => {
        this.ELEMENTDATA = [];
        this.countResult = item.totalCount;
        const dataSize = item.data.length;
        for (let i = 0; i < item.data.length; i++) {
          item.data[i].stt = page * size + i + 1;
          this.ELEMENTDATA.push(item.data[i]);
        }
        this.dataSource.data = this.ELEMENTDATA;
      }, err => {
        console.log(err);
        let msgObj = err.error.message || 'Lỗi không xác định';
          if (msgObj.includes('java.lang.NullPointerException')) {
            msgObj = {
              vi: 'Không tìm thấy dữ liệu',
              en: 'Data not found'
            };
            this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
            return;
          }
          this.snackbarService.openSnackBar(0, msgObj, '', 'error_notification', this.config.expiredTime);
      });
    } else if (type === 0) {
      const dataPost: any = {};
      const formObj = this.searchForm.getRawValue();
      let id = '';
      id = formObj.id;
      dataPost.msdn = id.toString().trim();
      this.service.postDetailsEnterprise(configId, dataPost).subscribe(item => {
        console.log(item);
        this.businessActivities = [];
        this.hoadress = [];
        this.representatives = [];
        this.mainInformation = [];
        this.member = [];
        if (item.data.businessActivities !== null) {
          this.businessActivities.push(item.data.businessActivities);
        } else {
          this.businessActivitiescheck = false;
        }

        if (item.data.hoadress !== null) {
          this.hoadress.push(item.data.hoadress);
        } else {
          this.hoadresscheck = false;
        }

        if (item.data.representatives !== null) {
          this.representatives.push(item.data.representatives);
        } else {
          this.representativescheck = false;
        }

        if (item.data.mainInformation !== null) {
          this.mainInformation.push(item.data.mainInformation);
        } else {
          this.mainInformationcheck = false;
        }

        if (item.data.member !== null) {
          this.member.push(item.data.member);
        }
        else {
          this.membercheck = false;
        }
      }, err => {
        console.log(err);
      });
    } else {
      const formObj = this.searchForm.getRawValue();
      const dataPost: any = {};
      dataPost.fromTS = formObj.fromTime;
      dataPost.toTS = formObj.toTime;
      dataPost.offset = page;
      dataPost.limit = size;
      const fromPage = page * size;
      const toPage = (page + 1) * size;
      this.service.postHandlingFileInDay(configId, dataPost).subscribe(item => {
        this.ELEMENTDATA1 = [];
        this.countResult1 = item.totalCount;
        const dataSize = item.data.length;
        for (let i = 0; i < item.data.length; i++) {
          item.data[i].stt = page * size + i + 1;          
          this.ELEMENTDATA1.push(item.data[i]);
        }
        this.dataSource1.data = this.ELEMENTDATA1;
      }, err => {
        console.log(err);
        let msgObj = err.error.message || 'Lỗi không xác định';
          if (msgObj.includes('java.lang.NullPointerException')) {
            msgObj = {
              vi: 'Không tìm thấy dữ liệu',
              en: 'Data not found'
            };
            this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
            return;
          }
          this.snackbarService.openSnackBar(0, msgObj, '', 'error_notification', this.config.expiredTime);
      });
    }

  }

  typeChange(id) {
    this.selected = id.value;
  }

  showListFile(code) {
    const dialogData = new BusinessRegistrationDetailsDialogModel(code);
    const dialogRef = this.dialog.open(DetailsComponent, {
      width: '800px',
      data: dialogData,
      disableClose: false,
      position: {
        top: '10em'
      },
      autoFocus: false
    });
    dialogRef.afterClosed().subscribe(dialogResult => { });
  }

  ngAfterViewInit() {
  }

  paginate(event: any, type, check) {
    let configId = '';
    // if (this.env?.businessRegistration && this.env?.businessRegistration?.configId != null && this.env?.businessRegistration?.configId != '') {
    //   configId = '?config-id=' + this.env?.businessRegistration?.configId;
    // } else {
    //   configId = '?agency-id=' + this.config.rootAgency.id + '&subsystem-id=' + this.config.subsystemWebPadsvcId;
    // }
    configId = '?agency-id=' + this.config.rootAgency.id + '&subsystem-id=' + this.config.subsystemId;

    switch (type) {
      case 0:
        this.pageIndex = event;
        this.page = event;
        this.search(configId, this.pageIndex - 1, this.size, check);
        break;
      case 1:
        this.pageIndex = 1;
        this.page = 1;
        this.search(configId, this.pageIndex - 1, this.size, check);
        break;
    }
  }
  
  triggerToDate(event: MatDatepickerInputEvent<Date>){
    /**
     * Mỗi lần thay đổi ngày bắt đầu . 
     *  - Ngày kết thúc sẽ + 4 days
     *  - Max ngày: +4
     * Nếu: MaxToDate - Ngày hiện tại > 0: Max ngày, Ngày kết thúc = Ngày hiện tại
     * Nếu: Ngày kết thúc - Ngày chọn bắt đầu <=5 && >=0 -> Giữ nguyên giá trị ngày kết thúc cũ
     *  
     */
    this.maxToDate = new Date(event.value.getTime() + 4 * 86400000);
    let toDateOld = this.searchForm.controls["toDate"].value;
    let diffDate = Math.round((toDateOld.getTime() - event.value.getTime())/86400000);
    let diffToDate = Math.round((this.maxToDate.getTime() - new Date().getTime())/86400000);

    if (diffToDate >= 0) {
      this.maxToDate = new Date();
    }
    
    if ( diffDate < 5 && diffDate >= 0 ) {
      this.searchForm.controls["toDate"].setValue(toDateOld);
    }
    else if(diffToDate > 0) {
      this.searchForm.controls["toDate"].setValue(new Date());
    }else{
      this.searchForm.controls["toDate"].setValue(new Date(event.value.getTime() + 4 * 86400000));
    }
  }

}
