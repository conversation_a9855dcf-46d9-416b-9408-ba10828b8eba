import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import * as moment from 'moment';
import { Observable } from 'rxjs';
import { ApiProviderService } from 'src/app/core/service/api-provider.service';
import { EnvService } from 'src/app/core/service/env.service';
import { DeploymentService } from 'src/app/data/service/deployment.service';

export interface Config {
  id: string;
  rotation: number;
  value: {
    startDate: string | null;
    endDate: string | null;
    dayOfWeek: number[] | null;
    dayOfMonth: number[] | null;
  };
  status: boolean;
  period: {
    startHours: string;
    endHours: string;
  }[];
  seconds: number;
  order: number | null;
}

export interface WorkSchedule {
  id: string;
  name: string;
  status: number;
  config: Config[];
  workHours: number;
  isDefault: boolean;
  updatedDate: string;
  createdDate: string;
  deploymentId: string;
}

@Injectable({
  providedIn: 'root',
})
export class TimeSheetService {
  config = this.enservice.getConfig();
  env = this.deploymentService.getAppDeployment()?.env;

  timeSheetId =
    this.deploymentService.env?.OS_CMU?.autoUpdateAcceptedDate?.timeSheetId;
  private actModelingAPI =
    this.apiProviderService.getUrl('digo', 'modeling') + '/v1';
  private actModelingContentAPI =
    this.apiProviderService.getUrl('digo', 'bpm') +
    '/process-definition/models/';
  // private actModelingContentAPI = 'http://localhost:8080/process-definition/models/';
  private bpmAPI = this.apiProviderService.getUrl('digo', 'bpm');
  private basecat = this.apiProviderService.getUrl('digo', 'basecat');
  private rbOnegate = this.apiProviderService.getUrl('digo', 'rbo');
  private reporter = this.apiProviderService.getUrl('digo', 'reporter');
  private human = this.apiProviderService.getUrl('digo', 'human');
  private basedata = this.apiProviderService.getUrl('digo', 'basedata');
  private basepad = this.apiProviderService.getUrl('digo', 'basepad');
  private padman = this.apiProviderService.getUrl('digo', 'padman');
  private sysman = this.apiProviderService.getUrl('digo', 'sysman');
  private formio = this.config.formioURL;
  private size = this.env.formIOMaxSize ? this.env.formIOMaxSize : 100000;

  constructor(
    private http: HttpClient,
    private apiProviderService: ApiProviderService,
    private enservice: EnvService,
    private deploymentService: DeploymentService
  ) {}

  getTimeSheet(): Observable<any> {
    this.http.options;
    return this.http.get(this.basecat + `/timesheet/${this.timeSheetId}`);
  }

  getNextWorkingDate(
    date: moment.Moment,
    holidays: string[],
    workingTimeConfig: Config
  ): moment.Moment {
    while (true) {
      if (workingTimeConfig) {
        date = this.getNextWorkingTime(date, workingTimeConfig.period);
        if (!workingTimeConfig.value!.dayOfWeek!.includes(date.day())) {
          date
            .add(1, 'day')
            .startOf('day')
            .hour(workingTimeConfig.period[0][0] ?? 7);
          continue;
        }
      }

      const nextDay = date.format('YYYY-MM-DD');
      if (holidays.includes(nextDay)) {
        date
          .add(1, 'day')
          .startOf('day')
          .hour(workingTimeConfig!.period[0][0] ?? 7);
        continue;
      }

      return date;
    }
  }
  updateNumberWithValidRanges(
    validRanges: [number, number][],
    number: number
  ): number {
    for (const range of validRanges) {
      const [lower, upper] = range;

      if (number < lower) {
        return lower;
      } else if (number >= lower && number <= upper) {
        return number;
      }
    }

    if (validRanges.length > 0) {
      return validRanges[0][0];
    } else {
      return number;
    }
  }

  convertTimeRanges(
    config: { startHours: string; endHours: string }[]
  ): [number, number][] {
    const convertedRanges: [number, number][] = [];

    for (const range of config) {
      const startHours =
        new Date(range.startHours).getUTCHours() +
        new Date(range.startHours).getUTCMinutes() / 60;
      const endHours =
        new Date(range.endHours).getUTCHours() +
        new Date(range.endHours).getUTCMinutes() / 60;
      convertedRanges.push([startHours, endHours]);
    }

    return convertedRanges;
  }
  getNextWorkingTime(
    date: moment.Moment,
    period: { startHours: string; endHours: string }[]
  ): moment.Moment {
    const timeRange = this.convertTimeRanges(period);

    const currentHour = date.hour() + date.minute() / 60;
    const updateHour = this.updateNumberWithValidRanges(timeRange, currentHour);

    const hours = Math.floor(updateHour);
    const minutes = (updateHour - hours) * 60;

    if (currentHour > timeRange[0][0] && updateHour === timeRange[0][0]) {
      return date.add(1, 'day').startOf('day').hour(hours).minute(minutes);
    } else {
      return date.startOf('day').hour(hours).minute(minutes);
    }
  }

  async updateDateBasedOnConfig(
    inputDate: moment.Moment
  ): Promise<moment.Moment> {
    const holidays: string[] = [];

    const workSchedule = await this.getTimeSheet()
      .toPromise()
      .catch((rs) => null);
    if (!workSchedule) {
      return inputDate;
    }
    let workingTimeRangeConfig: Config = null;
    // Extract holidays from the config
    for (const config of workSchedule.config) {
      if (config.status && config.rotation === 3 && config.period.length > 0) {
        workingTimeRangeConfig = config;
      }

      if (config.status || config.rotation !== 1) {
        continue; // Skip non-holiday or non-rotation-1 entries
      }
     

      const startDate = config.value.startDate.split('T')[0];
      const endDate = (config.value.endDate ?? startDate).split('T')[0];

      // Iterate through holiday range and add holidays to the list
      let currentDate = moment(startDate);
      const endDateMoment = moment(endDate);

      while (currentDate.isSameOrBefore(endDateMoment)) {
        holidays.push(currentDate.format('YYYY-MM-DD'));
        currentDate.add(1, 'day');
      }
    }

    return this.getNextWorkingDate(inputDate, holidays, workingTimeRangeConfig);
  }
}
