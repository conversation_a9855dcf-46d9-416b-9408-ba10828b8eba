<h2><PERSON><PERSON> sơ đến hạn theo c<PERSON> quan</h2>
<div class="prc_searchbar">
  <!-- <div fxLayout="row" fxLayout.xs="row" fxLayout.sm="row" class="formFieldOutline" fxLayoutAlign="space-between">
    <mat-form-field appearance="outline" fxFlex.gt-sm="66.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
      <mat-label>Nhập từ khóa</mat-label>
      <input type="text" [(ngModel)]="paramsQuery.keyword" matInput>
    </mat-form-field>
    <mat-checkbox appearance="outline" fxFlex.gt-sm="33" fxFlex.gt-xs="49.5" fxFlex='grow' [(ngModel)]="flagTTPVHCC"
      (change)="clickTTPVHCC()">Thuộc TTPVHCC</mat-checkbox>
  </div> -->
  <div fxLayout="row"
       fxLayout.xs="row"
       fxLayout.sm="row"
       class="formFieldOutline"
       fxLayoutAlign="space-between">
    <mat-form-field appearance="outline"
                    fxFlex.gt-sm="33"
                    fxFlex.gt-xs="49.5"
                    fxFlex='grow'>
      <mat-label>Đơn vị tiếp nhận</mat-label>
      <mat-select msInfiniteScroll
                  (infiniteScroll)="getAgencyScroll()"
                  [complete]="totalPagesAgencyAccept <= currentPageAgencyAccept+1"
                  [(ngModel)]="paramsQuery.agency"
                  (selectionChange)="changeAgencyAccept()">
        <mat-option>
          <ngx-mat-select-search ngModel
                                 (ngModelChange)="searchAngency($event)"
                                 placeholderLabel="">
          </ngx-mat-select-search>
        </mat-option>
        <mat-option value="">Tất cả</mat-option>
        <mat-option *ngFor="let item of listAgencyAccept"
                    [value]="item.id">{{item.name}}</mat-option>
      </mat-select>
    </mat-form-field>

    <mat-form-field appearance="outline"
                    fxFlex.gt-sm="33"
                    fxFlex.gt-xs="49.5"
                    fxFlex='grow'>
      <mat-label>Lĩnh vực</mat-label>
      <mat-select msInfiniteScroll
                  (infiniteScroll)="getListSectorScroll()"
                  [complete]="totalPagesSector <= currentPageSector+1"
                  [(ngModel)]="paramsQuery.sector"
                  (selectionChange)="sectorChange()">
        <mat-option>
          <ngx-mat-select-search ngModel
                                 (ngModelChange)="searchSector($event)"
                                 placeholderLabel="">
          </ngx-mat-select-search>
        </mat-option>
        <mat-option value="">Tất cả</mat-option>
        <mat-option *ngFor="let item of listSectorfillter"
                    [value]="item.id">{{item.name}}</mat-option>
      </mat-select>
    </mat-form-field>

    <mat-form-field appearance="outline"
                    fxFlex.gt-sm="33"
                    fxFlex.gt-xs="49.5"
                    fxFlex='grow'>
      <mat-label>Thủ tục</mat-label>
      <mat-select msInfiniteScroll
                  (infiniteScroll)="getListProcedureScroll()"
                  [complete]="totalPagesProcedure <= currentPageProcedure+1"
                  [(ngModel)]="paramsQuery.procedure">
        <mat-option>
          <ngx-mat-select-search ngModel
                                 (ngModelChange)="searchProvedure($event)"
                                 placeholderLabel="">
          </ngx-mat-select-search>
        </mat-option>
        <mat-option value="">Tất cả</mat-option>
        <mat-option *ngFor="let item of listProcedurefillter"
                    [value]="item.id">{{item.name}}</mat-option>
      </mat-select>
    </mat-form-field>
  </div>

  <div fxLayout="row"
       fxLayout.xs="row"
       fxLayout.sm="row"
       class="formFieldOutline"
       fxLayoutAlign="space-between">
    <div fxFlex.gt-sm="33"
         fxFlex.gt-xs="49.5"
         fxFlex='grow'>
      <fieldset class="fs_wrapper"
                style="margin-bottom: 10px">
        <legend>Thời gian giải quyết hồ sơ đến hạn</legend>
        <mat-form-field appearance="outline"
                        [fxFlex.gt-sm]="'49.5'"
                        fxFlex.gt-xs="49.5"
                        fxFlex='grow'
                        style="margin-right: 10px">
          <mat-label>Từ ngày</mat-label>
          <input matInput
                 [(ngModel)]="paramsQuery.fromDateNumber"
                 type="number">
        </mat-form-field>
        <mat-form-field appearance="outline"
                        [fxFlex.gt-sm]="'49.5'"
                        fxFlex.gt-xs="49.5"
                        fxFlex='grow'>
          <mat-label>Đến ngày</mat-label>
          <input matInput
                 [(ngModel)]="paramsQuery.toDateNumber"
                 type="number">
        </mat-form-field>
      </fieldset>
      <div class="error_MsgCustom"
           *ngIf="isDateFieldInvalid">
        <span>{{messageDateFieldInvalid}}</span>
        <div class="err">
          <mat-icon>priority_high</mat-icon>
        </div>
      </div>
    </div>
  </div>

  <div fxLayout="row"
       fxLayout.xs="column"
       fxLayout.sm="row"
       fxLayoutAlign="end"
       style="padding-bottom: 1em;">
    <button mat-flat-button
            fxFlex.lt-md="22"
            fxFlex.md="20"
            fxFlex.gt-md="12"
            fxFlex.gt-xs="49.5"
            fxFlex='grow'
            class="btn-search"
            type="submit"
            (click)="thongKe()"
            style="background-color: #ce7a58;color: white;">
      <mat-icon class="iconStatistical">bar_chart</mat-icon><span>Thống kê</span>
    </button>
    <div fxFlex='1'></div>
    <button mat-flat-button
            fxFlex.lt-md="22"
            fxFlex.md="20"
            fxFlex.gt-md="12"
            fxFlex.gt-xs="49.5"
            fxFlex='grow'
            class="btn-download-excel"
            style="background-color: #127905;color: white;"
            (click)="xuatExcel()">
      <mat-icon class="iconStatistical">cloud_download</mat-icon>
      <span style="padding-left: 2px;"> Xuất excel</span>
      <mat-progress-bar mode="indeterminate"
                        *ngIf="waitingDownloadExcel"></mat-progress-bar>
    </button>
    <div fxFlex='1'></div>


    <!-- <div fxFlex='1'></div>
      <button mat-flat-button fxFlex.lt-md="22" fxFlex.md="20" fxFlex.gt-md="12" fxFlex.gt-xs="49.5" fxFlex='grow'
        class="btn-download-excel" [disabled]="waitingDownloadExcel">
        <mat-icon class="iconStatistical">cloud_download</mat-icon>
        <span>Xuất excel</span>
        <mat-progress-bar mode="indeterminate" *ngIf="waitingDownloadExcel"></mat-progress-bar>
      </button>
      <div fxFlex='1'></div>
      <button mat-flat-button fxFlex.lt-md="22" fxFlex.md="20" fxFlex.gt-md="12" fxFlex.gt-xs="49.5" fxFlex='grow'
        class="btn-print" [disabled]="waitingDownloadPrint">
        <mat-icon class="iconStatistical">print</mat-icon>
        <span>In danh sách</span>
        <mat-progress-bar mode="indeterminate" *ngIf="waitingDownloadPrint"></mat-progress-bar>
      </button> -->
  </div>
</div>

<div fxLayout="row"
     fxLayoutAlign="center">
  <div class="frm_main"
       fxFlex="grow">
    <div class="logbookTbl">
      <div class="logbookOnlyTbl">
        <table class="table">
          <tr>
            <th class="text-center">STT</th>
            <th style="width: 200px;">Số hồ sơ</th>
            <th>Thủ tục</th>
            <th style="width: 300px;">TGQĐ hồ sơ</th>
            <th>Người đăng ký</th>
            <th>Lĩnh vực</th>
            <th>Bộ phận/CB đang xử lý</th>
          </tr>
          <tr *ngFor="let item of listHoSo;;let i = index ">
            <td class="text-center">{{ paramsQuery.page*paramsQuery.size + i + 1}}</td>
            <td style="cursor: pointer;color: #ce7a58;">{{item.code}}</td>
            <td>{{item.procedureCode}}
              <br>
              <span>{{item?.procedureName}}</span>
            </td>
            <td style="text-align: left ; font-style: italic;">
              <div>
                <!-- <span>{{item?.procedureCode}}</span><br> -->
                <span style="color: #3300FF"> {{item?.stringDateWork}}</span> <span
                      style="font-style: italic;color: #FF0000">
                  ({{item?.remainingDateWord ? item?.remainingDateWord : 'Hạn trong ngày'}})</span><br>
                - <span>Ngày tiếp nhận</span>: {{item?.acceptedDate | date:'dd/MM/yyyy HH:mm:ss'}} <br>
                - <span>Hạn xử lý</span>: <span style="color: #CC00CC">{{item?.appointmentDate | date:'dd/MM/yyyy HH:mm:ss'}}
                </span><br>
                - <span>Ngày hẹn trả</span>: {{item?.appointmentDate | date:'dd/MM/yyyy HH:mm:ss'}} <br>
                <span *ngIf="item?.completedDate">- <span>Ngày có KQ</span>: {{item?.completedDate |
                  date:'dd/MM/yyyy HH:mm:ss'}}
                  <br></span>
                <span *ngIf="item?.returnedDate">- <span>Ngày trả KQ</span>: {{item?.returnedDate | date:'dd/MM/yyyy HH:mm:ss'}}
                  <br></span>
              </div>
            </td>
            <td>
              <span style="font-weight: bold;">{{getFullName(item.applicant?.data)}}</span>
              <br />
              <span>{{generateAddress(item.applicant?.data)}}</span>
            </td>
            <td>{{item?.sectorName}}</td>
            <td>
              <span *ngIf="item?.assigneeFullNameCurrentTask"
                    style="color:#F09">{{item?.assigneeFullNameCurrentTask}}</span><br>
              <span *ngIf="item?.assigneeFullNameCurrentTask && item?.agencyNameCurrentTask"></span>
              <span *ngIf="item?.agencyNameCurrentTask"> {{item?.agencyNameCurrentTask}}</span>
            </td>
          </tr>
        </table>
        <div class="frm_Pagination">
          <ul class="temp_Arr">
            <li
                *ngFor="let item of listHoSo  | paginate: {itemsPerPage: paramsQuery.size, currentPage: page, totalItems: countResult, id: 'pgnx'}">
            </li>
          </ul>
          <div class="pageSize">
            <span>Hiển thị </span>
            <mat-form-field appearance="outline">
              <mat-select [(ngModel)]="paramsQuery.size"
                          (valueChange)="paginate(0)">
                <mat-option *ngFor='let opt of pgSizeOptions;'
                            [value]="opt">{{opt}}</mat-option>
              </mat-select>
            </mat-form-field>
            <span><span>trên</span> {{countResult}} <span>bản ghi</span></span>
          </div>
          <div class="control">
            <pagination-controls id="pgnx"
                                 (pageChange)="page = $event; paginate(page - 1)"
                                 responsive="true"
                                 previousLabel=""
                                 nextLabel="">
            </pagination-controls>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>