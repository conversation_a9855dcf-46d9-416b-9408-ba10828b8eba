import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { ApiProviderService } from 'src/app/core/service/api-provider.service';
import { Observable } from 'rxjs';
import { EnvService } from 'src/app/core/service/env.service';
@Injectable({
  providedIn: 'root'
})
export class AgencyYearService {

  constructor(
    private http: HttpClient,
    private apiProviderService: ApiProviderService,
    private envservice: EnvService
  ) { }
  config = this.envservice.getConfig();
  private basepad = this.apiProviderService.getUrl('digo', 'basepad');
  private procedurePath = this.apiProviderService.getUrl('digo', 'basepad') + '/procedure/';
  private agencyPath = this.apiProviderService.getUrl('digo', 'basedata') + '/agency/';
  private sectorPath = this.apiProviderService.getUrl('digo', 'basepad') + '/sector/';
  private tagPath = this.apiProviderService.getUrl('digo', 'basecat') + '/tag/';
  private procostPath = this.apiProviderService.getUrl('digo', 'basepad') + '/procost/';
  private procostTypePath = this.apiProviderService.getUrl('digo', 'basepad') + '/procost-type/';
  private procedureFormPath = this.apiProviderService.getUrl('digo', 'basepad') + '/procedure-form/';
  private procedureFormEformPath = this.apiProviderService.getUrl('digo', 'basepad') + '/procedure-form-eform/';
  private formPath = this.apiProviderService.getUrl('digo', 'basepad') + '/form/';
  private processPath = this.apiProviderService.getUrl('digo', 'basepad') + '/procedure-process-definition/';
  private modelingPath = this.apiProviderService.getUrl('digo', 'modeling') + '/v1/models/';
  private bpmProcessPath = this.apiProviderService.getUrl('digo', 'bpm') + '/process-definition/';
  private bpm = this.apiProviderService.getUrl('digo', 'bpm');
  private adapter = this.apiProviderService.getUrl('digo', 'adapter');
  private human = this.apiProviderService.getUrl('digo', 'human');
  private reporter = this.apiProviderService.getUrl('digo', 'reporter');
  private formio = this.config.formioURL;
  private padmanURL = this.apiProviderService.getUrl('digo', 'padman');

  getListAgencyWithParent(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.agencyPath + searchString, { headers });
  }

  getListSector(search: String): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.basepad + '/procedure/sector-by-agency' + search, { headers });
  }

  getListProcedure(search: String): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.basepad + '/procedure' + search, { headers });
  }

  getListDossierStatisticalAgencyByYear(search: String): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.reporter + '/dossier/statistic/--by-agency' + search, { headers });
    // return this.http.get('http://localhost:8080' + '/dossier/statistic/--by-sector' + search, { headers });
  }

  getDossierStatisticByAgencyInYear(search): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.get(this.padmanURL + '/dossier-statistic/--dossier-in-year' + search, { headers }).pipe();
  }
}
