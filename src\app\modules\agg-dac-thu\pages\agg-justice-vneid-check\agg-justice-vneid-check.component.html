<h2>AGG KIỂM TRA LIÊN THÔNG BỘ TƯ PHÁP - VNEID </h2>
<div class="prc_searchbar">
   <form  class="searchForm" [formGroup]="searchForm">
      <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="row" class="formFieldOutline" fxLayoutAlign="space-left">
         <mat-form-field appearance="outline" fxFlex="50" fxFlex.gt-md="22" fxFlex.gt-sm="50" fxFlex.gt-xs="100" class="form-field-margin">
            <mat-label i18n><PERSON><PERSON> s<PERSON> hồ sơ</mat-label>
            <input type="text" matInput formControlName="code" maxlength="500">
         </mat-form-field>
         <div fxFlex='1'></div>
         <mat-form-field appearance="outline" fxFlex="50" fxFlex.gt-md="22" fxFlex.gt-sm="50" fxFlex.gt-xs="100" class="form-field-margin">
            <mat-label>Trạng thái đồng bộ</mat-label>
            <mat-select placeholder="Chọn trạng thái" formControlName="syncStatus">
              <mat-option value="">Tất cả</mat-option>
              <mat-option value="cdb">Chờ đồng bộ</mat-option>
              <mat-option value="ddb">Đã đồng bộ</mat-option>
              <mat-option value="dckq">Đã có kết quả</mat-option>
            </mat-select>
          </mat-form-field>
          <div fxFlex='1'></div>
         <mat-form-field appearance="outline" fxFlex="50" fxFlex.gt-sm="25" fxFlex.gt-xs="49.5" fxFlex='grow'>
            <mat-label  >Từ ngày (Ngày tạo hồ sơ)</mat-label>
            <input matInput [matDatepicker]="pickerFromDate" formControlName="fromDate">
            <mat-datepicker-toggle matSuffix [for]="pickerFromDate"></mat-datepicker-toggle>
            <mat-datepicker #pickerFromDate></mat-datepicker>
         </mat-form-field>
         <div fxFlex='1'></div>
         <mat-form-field appearance="outline" fxFlex="50" fxFlex.gt-sm="25" fxFlex.gt-xs="49.5" fxFlex='grow'>
            <mat-label  >Đến ngày (Ngày tạo hồ sơ)</mat-label>
            <input matInput [matDatepicker]="pickerToDate" formControlName="toDate">
            <mat-datepicker-toggle matSuffix [for]="pickerToDate"></mat-datepicker-toggle>
            <mat-datepicker #pickerToDate></mat-datepicker>
         </mat-form-field>
         <div fxFlex='1'></div>
         <button type="submit" mat-flat-button fxFlex="100" fxFlex.gt-sm="12" fxFlex.gt-xs="12" class="btn-search" (click)="onConfirmSearch()">
            <mat-icon>search</mat-icon>
            <span i18n>Tìm kiếm</span>
         </button>
      </div>
      <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="row" class="formFieldOutline" fxLayoutAlign="space-left">
         <fieldset fxFlex.gt-sm="27" fxFlex.gt-xs="49.5" fxFlex='grow' style="margin-bottom: 10px">
            <legend style="color: #ce7a58">Kiểm tra kết nối</legend>
              <button mat-raised-button color="primary" formControlName="toDate" style="margin-right: 10px" (click)="isCheckConnect('btp')" >
                Hệ thống Bộ Tư Pháp
              </button>
              <button mat-raised-button color="accent" formControlName="toDate" (click)="isCheckConnect('vneid')">
                Hệ thống VNEID
              </button>
          </fieldset> 
      </div>
   </form>
</div>
<div fxLayout="row" fxLayoutAlign="center">
   <div class="prc_main" fxFlex="grow">
      <div class="prc_tbl">
         <div class="table-container">
         <table mat-table [dataSource]="dataSource" >
            <ng-container matColumnDef="stt">
               <mat-header-cell *matHeaderCellDef class="stt">STT</mat-header-cell>
               <mat-cell *matCellDef="let row; let i = index" data-label="STT" class="stt" > 
                  {{ pageIndex * pageSize + i + 1 }}
               </mat-cell>
            </ng-container>
            <ng-container matColumnDef="code">
               <mat-header-cell *matHeaderCellDef>Mã số hồ sơ</mat-header-cell>
               <mat-cell *matCellDef="let row" data-label="Mã số hồ sơ" class="cell_code" ><a>{{row.code}}</a>
               </mat-cell>
            </ng-container>
            <ng-container matColumnDef="applicantUser">
              <mat-header-cell *matHeaderCellDef>Thông tin người nộp/mục đích</mat-header-cell>
              <mat-cell *matCellDef="let row" data-label="Tên người nộp hồ sơ" class="cell_code" >
                <a>{{row.applicantUser}}
                <br>
                CCCD Số: {{row.identityNumber}}
                <br>
                Mục đích: {{row.target}}
              </a>
              </mat-cell>
           </ng-container>
            <ng-container matColumnDef="statusAsync">
               <mat-header-cell *matHeaderCellDef>Trạng thái đồng bộ BTP</mat-header-cell>
               <mat-cell *matCellDef="let row" [ngClass]="getStatusClass(row.statusAsync)" data-label="Trạng thái đồng bộ BTP">
               <a>{{row.statusAsync}}</a>
               </mat-cell>
            </ng-container>
            <ng-container matColumnDef="statusMessageMCDT">
               <mat-header-cell *matHeaderCellDef >Trạng thái hồ sơ MCDT</mat-header-cell>
               <mat-cell *matCellDef="let row" data-label="Trạng thái hồ sơ MCDT">{{row.statusMessageMCDT}}
               </mat-cell>
            </ng-container>
            <ng-container matColumnDef="createdDate">
               <mat-header-cell *matHeaderCellDef >Ngày tạo hồ sơ</mat-header-cell>
               <mat-cell *matCellDef="let row" data-label="Ngày tạo hồ sơ" >{{row.createdDate | date:'dd-MM-yyyy HH:mm:ss'}}
               </mat-cell>
            </ng-container>
            <ng-container matColumnDef="appointmentDate">
               <mat-header-cell *matHeaderCellDef >Ngày hẹn trả hồ sơ</mat-header-cell>
               <mat-cell *matCellDef="let row" data-label="Ngày hẹn trả hồ sơ">{{row.appointmentDate | date:'dd-MM-yyyy HH:mm:ss'}}
               </mat-cell>
            </ng-container>
            <ng-container matColumnDef="completedDate">
               <mat-header-cell *matHeaderCellDef >Ngày có kết quả</mat-header-cell>
               <mat-cell *matCellDef="let row" data-label="Ngày có kết quả"  >{{row.completedDate | date:'dd-MM-yyyy HH:mm:ss'}}
               </mat-cell>
            </ng-container>
            <ng-container matColumnDef="target">
               <mat-header-cell *matHeaderCellDef >Mục đích đăng ký</mat-header-cell>
               <mat-cell *matCellDef="let row" data-label="Mục đích đăng ký"  >{{row.target}}
               </mat-cell>
            </ng-container>
            <ng-container matColumnDef="statusMessageLLTP">
               <mat-header-cell *matHeaderCellDef>Nội dung phản hồi LLTP</mat-header-cell>
               <mat-cell *matCellDef="let row" data-label="Mã số hồ sơ">
                  <div class="status-message">
                     {{row.statusMessageLLTP}}
                   </div>                   
               </mat-cell>
             </ng-container>
             
            <ng-container matColumnDef="action">
               <mat-header-cell class ="action" *matHeaderCellDef >Hành động</mat-header-cell>
               <mat-cell *matCellDef="let row" data-label="Hành động">
                  <div style="display: flex; flex-direction: column;">
                    <button mat-icon-button color="primary" style="color: #4CAF50;" (click)="sendLLTPVNeID(row.code)">
                      <mat-icon>sync</mat-icon>
                      Đồng bộ sang LLTP
                    </button>
                    <button mat-icon-button color="accent" style="color: #2196F3;" (click)="syncLLTPVNEID(row.code)">
                      <mat-icon>sync</mat-icon>
                      Đồng bộ sang VNEID
                    </button>
                    <button mat-icon-button color="warn" style="color: #F44336;" (click)="eVictFileLLTP(row.code)">
                      <mat-icon>restore_page</mat-icon>
                      Đồng bộ QĐ thu hồi
                    </button>
                  </div>
                </mat-cell>
                
            </ng-container>
            <mat-header-row *matHeaderRowDef="displayedColumns"></mat-header-row>
            <mat-row *matRowDef="let row; columns: displayedColumns;"></mat-row>
         </table>
         </div>
         <mat-paginator [pageSizeOptions]="[5, 20, 50, 100, 500]" showFirstLastButtons (page)="onPageChange($event)"></mat-paginator>
      </div>
   </div>
</div>