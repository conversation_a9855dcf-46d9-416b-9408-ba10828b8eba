import { Injectable } from '@angular/core';
import { Workbook } from 'exceljs';
import { Observable } from 'rxjs';
import { MatDialog } from '@angular/material/dialog';
import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { EnvService } from 'core/service/env.service';
import { DeploymentService } from '../deployment.service';
import { SnackbarService } from 'data/service/snackbar/snackbar.service';
import { ApiProviderService } from 'src/app/core/service/api-provider.service';
import * as fs from 'file-saver';
import { DatePipe } from '@angular/common';
const EXCEL_TYPE = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8';
const EXCEL_EXTENSION = '.xlsx';

@Injectable({
  providedIn: 'root'
})
export class ScheduleCtdtService {

  language = localStorage.getItem('language') === 'vi' ? 228 : 46;
  config = this.envService.getConfig();

  constructor(
    private dialog: MatDialog,
    private http: HttpClient,
    private envService: EnvService,
    private snackbar: SnackbarService,
    private deploymentService: DeploymentService,
    private apiProviderService: ApiProviderService,
    private datePipe: DatePipe,
  ) { }

  private padmanURL = this.apiProviderService.getUrl('digo', 'padman');
  private basedataURL = this.apiProviderService.getUrl('digo', 'basedata');
  private basepadURL = this.apiProviderService.getUrl('digo', 'basepad');
  
  getListScheduleCtdt(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');

    return this.http.get(this.padmanURL + '/schedule-ctdt-qbh/--search' + searchString, { headers }).pipe();
  }

  getScheduleCtdtById(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');

    return this.http.get(this.padmanURL + '/schedule-ctdt-qbh/detail/' + id, { headers }).pipe();
  }

  countScheduleCtdt(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');

    return this.http.get(this.padmanURL + '/schedule-ctdt-qbh/--count-new-schedule' + searchString, { headers }).pipe();
  }

  putUpdateStatusScheduleCtdt(id, paramsQuery) {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.put<any>(this.padmanURL + '/schedule-ctdt-qbh/--update-status/'+ id + paramsQuery, { headers });
  }
 
 exportScheduleCtdtToExcel(
     data: any[],
     excelFileName: string,
     sheetName: string
   ) {
     // create workbook and worksheet
     const workbook = new Workbook();
     workbook.creator = 'Snippet Coder';
     workbook.lastModifiedBy = 'SnippetCoder';
     workbook.created = new Date();
     workbook.modified = new Date();
     const worksheet = workbook.addWorksheet(sheetName);
 
     let columns = ['A', 'B', 'C', 'D', 'E', 'F', 'G'];
     worksheet.views = [{}]
 
     columns.forEach(columnName => {
       worksheet.getColumn(columnName).font = { name: 'Times New Roman', size: 11 };
       worksheet.getColumn(columnName).alignment = { horizontal: 'left', vertical: 'middle', wrapText: true };
       worksheet.getColumn(columnName).width = 15;
     })
 
     worksheet.mergeCells('A1:G1');
     worksheet.getCell('A1').value = 'DANH SÁCH LỊCH HẸN CHỨNG THỰC ĐIỆN TỬ';
     worksheet.getCell('A1').font = { bold : true, size: 12 };
     worksheet.getCell('A1').alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
     worksheet.getRow(1).height = 30;
 
     worksheet.getRow(2).height = 30;
     worksheet.getRow(2).alignment = { horizontal: 'center', vertical: 'middle' };
     worksheet.getRow(2).font = { bold: true };
     worksheet.getRow(3).height = 30;
     worksheet.getRow(3).alignment = { horizontal: 'center', vertical: 'middle' };
     worksheet.getRow(3).font = { bold: true };
 
     // header
     worksheet.mergeCells('A2:A3');
     worksheet.getCell('A2').value = 'STT';
     worksheet.getColumn('A').alignment = { horizontal: 'center', vertical: 'middle' };
     worksheet.getColumn('A').width = 5;
 
     worksheet.mergeCells('B2:B3');
     worksheet.getCell('B2').value = 'Mã thủ tục';
 
     worksheet.mergeCells('C2:C3');
     worksheet.getCell('C2').value = 'Tên thủ tục';
 
     worksheet.mergeCells('D2:E2');
     worksheet.getCell('D2').value = 'Thông tin người nộp';
     worksheet.getCell('D3').value = 'Họ tên';
     worksheet.getCell('E3').value = 'Địa chỉ';
 
     worksheet.mergeCells('F2:F3');
     worksheet.getCell('F2').value = 'Ngày nộp';

     worksheet.mergeCells('G2:G3');
     worksheet.getCell('G2').value = 'Trạng thái';
 
     // header border
     ['A2','B2','C2','D2','E2','D3','E3','F3','G3','F2'].forEach(columnName => {
       worksheet.getCell(columnName).border = {top: {style: 'thin'}, left: {style: 'thin'}, bottom: {style: 'thin'}, right: {style: 'thin'}};
     });
 
     // add data
     let i = 1;
     data.forEach( dossier => {
       const createdDate = this.datePipe.transform(dossier.createdDate, 'dd/MM/yyyy HH:mm:ss');
       const status = "Chờ tiếp nhận lịch hẹn";
       const eachRow = [
         i++,                                // STT
         dossier.procedureCode,                       // Mã thủ tục
         dossier.procedureName,             // Tên thủ tục
         dossier.applicant.fullname,        // Người nộp
         dossier.applicant.address,        // Địa chỉ
         createdDate,                        // Ngày nộp hồ sơ
         status                     // Trạng thái
       ];
       worksheet.addRow(eachRow);
     })
 
     // Add Border & Alignment for data cell
     for (var j = 4; j < i+3; j++) {
       columns.forEach(columnName => {
         worksheet.getCell(columnName + j).border = {left: {style: 'thin'}, bottom: {style: 'thin'}, right: {style: 'thin'}};
       })
     }
 
     // Save Excel File
     workbook.xlsx.writeBuffer().then((dataBuffer: ArrayBuffer) => {
       const blob = new Blob([dataBuffer], {type: EXCEL_TYPE});
       fs.saveAs(blob, excelFileName + EXCEL_EXTENSION);
     });
   }
}
