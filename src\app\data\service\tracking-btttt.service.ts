import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { DeploymentService } from './deployment.service';
import {KeycloakService} from 'keycloak-angular';
import {UserService} from 'data/service/user.service';
import {EnvService} from 'core/service/env.service';
import { ApiProviderService } from 'src/app/core/service/api-provider.service';
@Injectable({
    providedIn: 'root'
})
export class TrackingBTTTTService {

    private trackingBTTTT: any = this.deploymentService.getAppDeployment()?.trackingBTTTT;
    private adapter = this.apiProviderService.getUrl('digo', 'adapter');
    private config = this.envService.getConfig();
    private userInfo: any;
    constructor(
        private http: HttpClient,
        private deploymentService: DeploymentService,
        private keycloakService: KeycloakService,
        private userService: UserService,
        private envService: EnvService,
        private apiProviderService: ApiProviderService,
    ) {
      this.getUserAccount();
    }

    getUserAccount() {
      this.keycloakService.loadUserProfile().then(user => {
        // tslint:disable-next-line: no-string-literal
        this.userService.getUserInfo(user['attributes'].user_id[0]).subscribe(data => {
          this.userInfo = data;
        }, error => {
          console.log(error);
        });
      });
    }

    getAgencyCode(){
      const userAgency = JSON.parse(localStorage.getItem('userAgency'));
      let code: any = '';
      if (userAgency !== null) {
        code = userAgency.code;
      } else {
        code = this.config.rootAgency.code;
      }
      return code;
    }

    getIdQG(userEMC: any): string {
      if ( userEMC?.provider == null ) { return null; }
      if ( userEMC?.provider?.provider === this.trackingBTTTT.provider){
        return userEMC?.provider?.userId;
      }
      return null;
    }

    mapTypeUserWithEMC(type: number){
      // igate : 1 - cong dan, 2 - DN, 3 - can bo
      // emc : 2 - ca nhan, 1 - DN, 3 - can bo
      switch (type){
        case 1: return 2;
        case 2: return 1;
        case 3: return 3;
        default: return type;
      }
    }

    async getUser(params: TrackingBTTTTModel){
        let userEMC = this.userInfo; // thong tin can bo
        if (params.userId != null){
          // lay thong tin cong dan nop ho so, truong hop truc tiep thi params.userId la user can bo nop
          userEMC = await this.userService.getUserInfoAsync(params.userId);
        }
        const user =
        {
          ID_local: userEMC.id,
          ID_QG : this.getIdQG(userEMC),
          TYPE: this.mapTypeUserWithEMC(userEMC.type),
          LOCATION: this.getAgencyCode()
        };
        return JSON.stringify(user);
    }

    getIsFormDVCQG(params: TrackingBTTTTModel): string {
        let isFormDVCQG = params?.fromDVCQG.toString();
        if (!!params?.nationCode) {
          isFormDVCQG = '1';
        } else {
          const listCode = this.trackingBTTTT?.listCodeFromDVCQG;
          if (listCode != null) {
            listCode.forEach(item => {
              if (params?.dossierCode.toLowerCase().indexOf(item.toLowerCase()) !== -1) {
                isFormDVCQG = '1';
              }
            });
          }
        }
        return isFormDVCQG;
    }

    public async pushTracking(params: TrackingBTTTTModel) {
        if (!!this.trackingBTTTT?.endpoint){
            const headers = {
                'Content-type': 'application/x-www-form-urlencoded'
            };
            if (this.trackingBTTTT?.enableLogRetry) {
              // gọi luồng qua cauhinhv2 để ghi log retry
              const formData = new FormData();
              formData.append('codeProfile', params?.dossierCode);
              formData.append('siteId', this.trackingBTTTT?.siteId);
              formData.append('codeTTHC', params?.procedureCode);
              formData.append('nameTTHC', params?.procedureName);
              formData.append('status', params?.status.toString());
              formData.append('formsReception', params?.receptionMethod.toString());
              formData.append('formsPayments', params?.paymentMethod ? params?.paymentMethod.toString() : '');
              formData.append('level', params?.level.toString());
              formData.append('isFromDVCQG', this.getIsFormDVCQG(params));
              formData.append('isDVCBC', params?.isDVCBC.toString());
              formData.append('data', params?.data);
              formData.append('user', await this.getUser(params));
          
              this.http.post(this.adapter + '/service/emc/trackingmcdt', formData).subscribe(data => {
                console.log('tracking success: ', data);
              });
            }else{
            const body = new HttpParams()
                .set('codeProfile', params?.dossierCode)
                .set('siteId', this.trackingBTTTT?.siteId)
                .set('codeTTHC', params?.procedureCode)
                .set('nameTTHC', params?.procedureName)
                .set('status', params?.status.toString())
                .set('formsReception', params?.receptionMethod.toString())
                .set('formsPayments', params?.paymentMethod ? params?.paymentMethod.toString() : null)
                .set('level', params?.level.toString())
                .set('isFromDVCQG', this.getIsFormDVCQG(params))
                .set('isDVCBC', params?.isDVCBC.toString())
                .set('data', params?.data)
                .set('user', await this.getUser(params));
            console.log('tracking body', body);
            this.http.post(this.trackingBTTTT?.endpoint, body, { headers }).subscribe(data => {
                console.log('tracking success: ', data);
            });
            }
        } else {
            console.log('tracking not configured');
        }
    }
}

export class TrackingBTTTTModel{
    constructor(
        public dossierCode: string,
        public procedureCode: string,
        public procedureName: string,
        public status: number,
        public level: number = 0,
        public fromDVCQG: number = 0,
        public isDVCBC: number = 0,
        public receptionMethod: number = 2,
        public paymentMethod: any = 2,
        public userId: string,
        public data: string = '',
        public nationCode: string
    ){}
}
