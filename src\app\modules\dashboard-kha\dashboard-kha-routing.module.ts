import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Routes } from '@angular/router';
import { AuthGuard } from 'src/app/core/guard/auth.guard';
import { DashboardKhaComponent } from './pages/dashboard-kha/dashboard-kha.component';


const routes: Routes = [
  {
    path: '',
    component: DashboardKhaComponent,
    canActivate: [AuthGuard]
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class DashboardKhaRoutingModule { }
