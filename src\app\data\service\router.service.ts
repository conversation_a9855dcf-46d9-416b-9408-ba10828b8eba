import { Injectable } from '@angular/core';
import { Router, Event, NavigationStart } from '@angular/router';

@Injectable({
    providedIn: 'root'
})
export class RouterService {

    private navigationStartName = 'navigationStarts';

    constructor(
        private router: Router,
    ) { }

    setNavigation() {
        this.router.events.subscribe((event: Event) => {
            if (event instanceof NavigationStart) {
                if (event.url === '/') {
                    localStorage.setItem(this.navigationStartName, '[]');
                }

                const navigationStarts = JSON.parse(localStorage.getItem(this.navigationStartName)) || [];
                if (navigationStarts.length > 10) {
                    navigationStarts.shift();
                    navigationStarts.push(event);
                } else {
                    navigationStarts.push(event);
                }
                localStorage.setItem(this.navigationStartName, JSON.stringify(navigationStarts));
            }
            // do not delete
            // if (event instanceof NavigationEnd) { }
            // if (event instanceof NavigationError) { }
            // if (event instanceof RoutesRecognized) { }
        });
    }

    getNavigation(type): any {
        switch (type) {
            case 'start': {
                const navigationStarts = JSON.parse(localStorage.getItem('navigationStarts'));
                return navigationStarts;
            }
            case 'end': {
                break;
            }
            case 'error': {
                break;
            }
            case 'routesRecognized': {
                break;
            }
        }
    }

    // Auto redirect when user has been permission
    autoRedirect() {
        // Cần tối ưu lại, tạm thời không dùng
        // const insufficientPermissionRouterLink = '/error/insufficient-permission';
        // const navigationStarts = this.getNavigation('start');
        // let i = navigationStarts.length;

        // if (i === 1) {
        //     this.router.navigate([navigationStarts[i - 1].url]);
        // }

        // while (i--) {
        //     if (navigationStarts[i].url === insufficientPermissionRouterLink) {
        //         if (navigationStarts[i - 1].url === insufficientPermissionRouterLink) {
        //             continue;
        //         } else {
        //             this.router.navigate([navigationStarts[i - 1].url]);
        //             break;
        //         }
        //     }
        // }
    }

}
