@import "~src/styles/custom-material.scss";

::ng-deep .procedure-list-page {
    @import "~src/styles/framework.scss";
    @import "~src/styles/custom.scss";
    @import "~src/styles/pagination.scss";
    @import "~src/styles/buttons.scss";

    .form-search {
        @extend .cform-search;

        .btn-search {
            @extend .t-btn-search;
        }
    }

    .btn-add {
        @extend .t-btn-add;
    }

    .btn-more {
        @extend .t-btn-more;
    }

    .tbl {
        @extend .cmat-table;
        @extend .bgc-white;

        table {
            width: 100%;
        }

        .mat-column-code {
            @extend .mat-column-name;
            @extend .f-0-0-10;

            a {
                text-decoration: none;
                font-weight: 500;
                color: #ce7a58;
                @extend .more-text;
            }
        }

        .mat-column-name {
            @extend .pdr-10px;
            @extend .pdl-10px;
            @extend .ofw-anywhere;
        }

        .mat-column-agency {
            @extend .mat-column-name;
        }

        .mat-column-sector {
            @extend .mat-column-name;
            @extend .f-0-0-8;
        }

        .mat-column-agencyLevel {
            @extend .mat-column-name;
            @extend .f-0-0-8;
        }

        .mat-column-procedureLevel {
            @extend .mat-column-name;
            @extend .f-0-0-8;
        }

        .mat-column-status {
            @extend .mat-column-name;
            @extend .f-0-0-6;
        }
    }

    @media screen and (max-width: 600px) {
        .tbl {
            .mat-header-row {
                display: none;
            }

            .mat-table {
                border: 0;
                vertical-align: middle;

                .mat-row {
                    border-bottom: 5px solid #ddd;
                    display: block;
                    min-height: unset;
                }

                .mat-cell {
                    border-bottom: 1px solid #ddd;
                    display: block;
                    font-size: 14px;
                    text-align: right;
                    margin-bottom: 4%;
                    padding: 0 0.5em;

                    &:before {
                        content: attr(data-label);
                        float: left;
                        font-weight: 500;
                        font-size: 14px;
                    }

                    &:last-child {
                        border-bottom: 0;
                    }

                    &:first-child {
                        margin-top: 4%;
                    }
                }
            }

            .mat-row {
                &:nth-child(even) {
                    background-color: unset;
                }

                &:nth-child(odd) {
                    background-color: unset;
                }
            }
        }
    }
}
