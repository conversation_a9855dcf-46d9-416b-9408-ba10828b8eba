import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { ApiProviderService } from 'src/app/core/service/api-provider.service';
import { Observable } from 'rxjs';
import { EnvService } from 'src/app/core/service/env.service';

@Injectable({
  providedIn: 'root'
})
export class BasedataService {

  config = this.envService.getConfig();
  constructor(
    private envService: EnvService,
    private http: HttpClient,
    private apiProviderService: ApiProviderService
  ) { }

  getPlaceAddress(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    switch (localStorage.getItem('isLoggedIn')) {
      case 'false':
        const token = localStorage.getItem('OAuth2TOKEN');
        headers = headers.append('Authorization', 'Bearer ' + token);
        return this.http.get(this.basedata + '/place/' + id + '/--address', { headers }).pipe();
      case 'true':
        return this.http.get(this.basedata + '/place/' + id + '/--address', { headers }).pipe();
    }
  }

  getAgencyInfo(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    switch (localStorage.getItem('isLoggedIn')) {
      case 'false':
        const token = localStorage.getItem('OAuth2TOKEN');
        headers = headers.append('Authorization', 'Bearer ' + token);
        return this.http.get(this.basedata + '/agency/' + id, { headers }).pipe();
      case 'true':
        return this.http.get(this.basedata + '/agency/' + id, { headers }).pipe();
    }
  }
  private basedata = this.apiProviderService.getUrl('digo', 'basedata');
  // private basedata = "http://localhost:8888"

  listAgencyNameLogo(keyword, tagId, parentId, ancestorIds, page, size): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    let searchStr = `page=${page}&size=${size}`;
    if(keyword){
      searchStr += `&keyword=${keyword}`;
    }
    if (tagId) {
      searchStr += `&tag-id=${tagId}`;
    }
    if(parentId){
      searchStr += `&parent-id=${parentId}`;
    }
    if(ancestorIds?.length > 0){
      let arrayStr = "[";
      for (const id of ancestorIds) {
        arrayStr += `${id},`;
      }
      arrayStr = arrayStr.substring(0,arrayStr.length-1) + "]";
      searchStr += `&ancestor-id=${arrayStr}`;
    }
    return this.http.get(this.basedata + `/agency/name+logo-id?${searchStr}`, { headers });
  }

  getAgencyNameCode(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.basedata + '/agency/name+code' + searchString, { headers });
  }

  getSubAgencyByParentId(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.basedata + '/agency/--by-parent-id' + searchString, { headers }); 
  }

  getSubAgencybyAncestorId(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.basedata + '/agency/--by-ancestor-id-and-tag-id' + searchString, { headers }); 
  }

  getAgencyNameTag(id, searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.basedata + `/agency/${id}/name+tag` + searchString, { headers });
  }

  getAgencyTreeViewByAncestorId(options): Observable<any> {
    return this.http.get<any>(this.basedata + '/agency' +
      `/tree-view/--by-ancestor?keyword=` + options.keyword +
      `&ancestor-id=${options['ancestor-id']}` +
      `&code=${options.code}` +
      `&status=${options.status}` +
      `&phone=${options.phone}`);
  }
  getAgencyTreeViewByAncestorIdTagId(options): Observable<any> {
    return this.http.get<any>(this.basedata + '/agency' +
      `/tree-view/--by-ancestor?keyword=` + options.keyword +
      `&ancestor-id=${options['ancestor-id']}` +
      `&code=${options.code}` +
      `&status=${options.status}` +
      `&phone=${options.phone}`+
      `&tag-id=${options.tagId}`);
  }
  
  getAgencyExceptChildWardTreeViewByAncestorId(options): Observable<any> {
    console.log("option",options);
    
    return this.http.get<any>(this.basedata + '/agency' +
      `/tree-view/--by-ancestor-except-child-ward?keyword=` + options.keyword +
      `&ancestor-id=${options['ancestor-id']}` +
      `&code=${options.code}` +
      `&status=${options.status}` +
      `&phone=${options.phone}`);
  }

  // lấy danh sách đơn vị cùng LevelId
  listAgencyNameLogoBaseOnLevelId(keyword, levelId, page, size): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    let searchStr = `page=${page}&size=${size}`;
    //let searchStr ="";
    if(keyword){
      searchStr += `&keyword=${keyword}`;
    }
    if (levelId) {
      searchStr += `&agency-level-id=${levelId}`;
    }
    return this.http.get(this.basedata + `/agency/name+logo-id?${searchStr}`, { headers });
  }

  getListPlace(searchStr: string):Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.basedata + "/place/--search?" + searchStr, { headers });
  }

  listTreeViewAgency(searchString): Observable<any> {
      let headers = new HttpHeaders();
      headers = headers.set('Accept-Language', localStorage.getItem('language'));
      return this.http.get(this.basedata + '/agency/tree-view' + searchString, { headers });
  }

  getCodeAgencyId(parentId): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
   // return this.http.get('http://localhost:8520/agency/' + parentId, { headers });
    return this.http.get(this.basedata + '/agency/' + parentId, { headers });
}

  getAgencyByAncestorIdAndListIdIgnore(options): Observable<any> {
    return this.http.get<any>(this.basedata + '/agency' +
      `/tree-view/--by-ancestor-and-list-id-ignore?keyword=` + options.keyword +
      `&ancestor-id=${options['ancestor-id']}` +
      `&code=${options.code}` +
      `&status=${options.status}` +
      `&phone=${options.phone}` +
      `&list-agency-id-ignore=${options.listAgencyIgnore}`);
  }

  getListAgencyByAncestorIdAndTagId(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.get(this.basedata + '/agency/--by-ancestor-id-and-tag-id' + searchString, { headers }).pipe();
  }

  findEthnicNameLimit(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.get(this.basedata + '/ethnic/name/--limit' + searchString, { headers }).pipe();
  } 

  getEthnicById(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.get(this.basedata + `/ethnic/${id}`, { headers }).pipe();
  } 


  getNation(searchString): Observable<any> {
    let  headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.get(this.basedata + '/nation/name' + searchString, { headers }).pipe();
  }

  getNationById(id): Observable<any> {
    let  headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.get(this.basedata + `/nation/${id}`, { headers }).pipe();
  }

  getAgencyById(id): Observable<any> {
    let  headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.get(this.basedata + `/agency/${id}`, { headers }).pipe();
  }
  getPlace(searchString): Observable<any> {
    let  headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.get(this.basedata + '/place/getPlaces' + searchString, { headers }).pipe();
  }

  getPlaceIdName(id): Observable<any> {
    let  headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.get(this.basedata + `/place/${id}/id+name` , { headers }).pipe();
  }
  
  getBusinessSectorQBH(searchString) : Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.set('Content-Type', 'application/json');
    return this.http.get(this.basedata + '/qbh-business-sector/get-sector' +searchString ,{headers});
  }

  getAllEthnic(searchString: String = ""): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.get(this.basedata + '/ethnic/get-all' + searchString, { headers }).pipe();
  }

  getNationList(searchString: string = ""): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.get(this.basedata + '/nation' + searchString, { headers }).pipe();
  }

  findAgencyByName(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.get(this.basedata + '/agency/name' + searchString, { headers }).pipe();
  }

  createBusinessRegistrationHTX(body): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.post(this.basedata + '/business-registration/htx', body, { headers }).pipe();
  }
  
  getBusinessRegistrationById(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.set('Content-Type', 'application/json');
    return this.http.get(this.basedata + `/business-registration/${id}` ,{headers});
  }

 
  getBusinessSectorTree(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.set('Content-Type', 'application/json');
    return this.http.get(this.basedata + '/qbh-business-sector/get-tree' +searchString ,{headers});
  }

  getCodeNameBusinessSector(): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.set('Content-Type', 'application/json');
    return this.http.get(this.basedata + '/qbh-business-sector/get-sector/code+name',{headers});
  }

  getBusinessCooperativeById(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.set('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.get(this.basedata + `/business-registration/htx/${id}` ,{headers}).pipe();
  }

  updateSuspendHTX(id, body): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.put(this.basedata + '/business-registration/suspendHTX/' + id, body, { headers }).pipe();
  }

  updateHTX(id, body): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.put(this.basedata + '/business-registration/htx/' + id, body, { headers }).pipe();
  }

  searchBusinessSectorOfTree(searchString) : Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.set('Content-Type', 'application/json');
    return this.http.get(this.basedata + '/qbh-business-sector/get-list-search-sector-tree' +searchString ,{headers});
  }

  deleteCooperative(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.put(this.basedata + '/business-registration/htx/delete/' + id, { headers }).pipe();
  }
  
  getAgencyTreeViewByAncestorIdQBH(options): Observable<any> {
    return this.http.get<any>(this.basedata + '/qbh-agency' +
      `/tree-view/--by-ancestor-qbh?keyword=` + options.keyword +
      `&ancestor-id=${options['ancestor-id']}` +
      `&code=${options.code}` +
      `&status=${options.status}` +
      `&phone=${options.phone}` +
      `&tag=${options['tag']}`);
  }

  getAgencyList(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.get(this.basedata + '/agency/--search' + searchString, { headers }).pipe();
  }
  getAgencylevelAll(): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.get(this.basedata + '/agency-level/--get-list-no-page' , { headers }).pipe();
  }
}
