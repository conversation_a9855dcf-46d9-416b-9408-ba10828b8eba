import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { ApiProviderService } from 'src/app/core/service/api-provider.service';

@Injectable({
    providedIn: 'root'
})
export class DocumentManagementService {
    constructor(
        private http: HttpClient,
        private apiProviderService: ApiProviderService,
    ) { }

    private documentManagementPath = this.apiProviderService.getUrl('digo', 'basepad') + '/document-management/';

    getListDocument(searchString): Observable<any> {
        let headers = new HttpHeaders();
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        return this.http.get(this.documentManagementPath + searchString, { headers });
    }

    deleteDocument(searchkey): Observable<any> {
        let headers = new HttpHeaders();
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
         return this.http.delete(this.documentManagementPath+"deleteFileById" + searchkey, { headers });
    }

    addOrUpdateDocument(id, requestBody): Observable<any> {
        let headers = new HttpHeaders();
        headers = headers.set('Content-Type', 'application/json');
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        if(id == '0'){
            return this.http.post<any>(this.documentManagementPath, requestBody, { headers });
        }
        return this.http.put<any>(this.documentManagementPath + `${id}`, requestBody, { headers });
    }

    getDocumentById(id): Observable<any> {
        let headers = new HttpHeaders();
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        return this.http.get(this.documentManagementPath + `${id}`, { headers });
    }

    getFileByDocumentId(searchkey): Observable<any> {
        let headers = new HttpHeaders();
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        return this.http.get(this.documentManagementPath + 'getFileById/' + searchkey, { headers });
    }
}
