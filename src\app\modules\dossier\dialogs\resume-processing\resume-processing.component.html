<button mat-icon-button class="close-button" (click)="onDismiss()">
    <mat-icon>close</mat-icon>
</button>
<h3 class="dialog_title" mat-dialog-title i18n>Ti<PERSON><PERSON> tục xử lý</h3>
<div mat-dialog-content class="dialog_content">
    <span i18n>Bạn có chắc chắn muốn tiếp tục x<PERSON> lý hồ sơ </span><span class="highlight">{{dossierCode}}</span><span>?</span>
    <div class="commentEditor" fxLayout="column" fxLayout.xs="column" fxLayout.sm="column">
        <p class="lbl" i18n>Ý kiến xử lý</p>
        <ckeditor [editor]="Editor" class="customCKEditor" (change)="onCommentEditorChange($event)" fxFlex='grow'
            [config]='commentConfig'>
        </ckeditor>
        <div class="errorMsg" *ngIf="isCKMaxlenght">
            <!-- <span i18n>Nội dung không quá 500 ký tự</span> -->
            <span>{{ckeditorMaxLengthNotification}}</span>
            <div class="err">
                <mat-icon>priority_high</mat-icon>
            </div>
        </div>
    </div>
    <div *ngIf="!enableSmsResumeDossier">
        <digo-check-send-notify functionType="resumeDossier"></digo-check-send-notify>
    </div>
    <div *ngIf="enableSmsResumeDossier">
        <digo-check-send-notify [receiveType]="2" functionType="resumeDossier"></digo-check-send-notify>
    </div>  
</div>
<div fxLayout="row" fxLayout.xs="column" fxLayout.sm="column" fxLayoutAlign="center">
    <button mat-flat-button fxFlex='30' class="applyBtn" (click)="onConfirm()" [disabled]="disableButton">
        <span i18n>Đồng ý</span>
    </button>
</div>