<h2>{{ pageTitle.vi }}</h2>
<div class="procedure-list-page">
    <form [formGroup]="searchForm" (submit)="onConfirmSearch()" class="form-search">
        <div fxLayout="row" fxLayout.xs="row" fxLayout.sm="row" fxLayoutAlign="space-between" class="flex-wrap-wrap">
          <mat-form-field appearance="outline" fxFlex.gt-md="24.5" fxFlex.gt-sm="49.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
            <mat-label>Từ ngày</mat-label>
            <input matInput [matDatepicker]="pickerFromDate" formControlName="fromdate" maxlength="20" required>
            <mat-datepicker-toggle matSuffix [for]="pickerFromDate"></mat-datepicker-toggle>
            <mat-datepicker #pickerFromDate></mat-datepicker>
          </mat-form-field>

          <mat-form-field appearance="outline" fxFlex.gt-md="24.5" fxFlex.gt-sm="49.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
              <mat-label>Đến ngày</mat-label>
              <input matInput [matDatepicker]="pickerToDate" formControlName="todate" maxlength="20" required>
              <mat-datepicker-toggle matSuffix [for]="pickerToDate"></mat-datepicker-toggle>
              <mat-datepicker #pickerToDate></mat-datepicker>
          </mat-form-field>

          <mat-form-field appearance="outline" fxFlex.gt-md="24.5" fxFlex.gt-sm="49.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
              <mat-label>Từ ngày lũy kế</mat-label>
              <input matInput [matDatepicker]="pickerFromLuyKeDate" formControlName="fromLKdate" maxlength="20" required>
              <mat-datepicker-toggle matSuffix [for]="pickerFromLuyKeDate"></mat-datepicker-toggle>
              <mat-datepicker #pickerFromLuyKeDate></mat-datepicker>
          </mat-form-field>

          <mat-form-field appearance="outline" fxFlex.gt-md="24.5" fxFlex.gt-sm="49.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
              <mat-label>Đến ngày lũy kế</mat-label>
              <input matInput [matDatepicker]="pickerToLuyKeDate" formControlName="toLKdate" maxlength="20" required>
              <mat-datepicker-toggle matSuffix [for]="pickerToLuyKeDate"></mat-datepicker-toggle>
              <mat-datepicker #pickerToLuyKeDate></mat-datepicker>
          </mat-form-field>
      </div>
      <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="row" fxLayoutAlign="end">
        <div fxFlex='1'></div>
        <button mat-flat-button fxFlex.gt-sm="12" fxFlex.gt-md="12" fxFlex.gt-xs="49.5" fxFlex='grow' class="btn-download-excel">
            <mat-icon class="iconStatistical">cloud_download</mat-icon>
            <span i18n="@@exportExcel"> Xuất excel</span>
        </button>
        <div fxFlex='1'></div>
        <button type="submit" mat-flat-button fxFlex.gt-sm="12" fxFlex='12' class="btn-search">
            <mat-icon class="iconStatistical">bar_chart</mat-icon>
            <span i18n="@@statistical">Thống kê</span>
        </button>
    </div>
    </form>

    <div fxLayout="row" fxLayoutAlign="center">
        <div fxFlex="grow">
            <div class="tbl mat-elevation-z8">
                <table mat-table [dataSource]="dataSource">
                    <ng-container matColumnDef="stt">
                        <th *matHeaderCellDef>(1)</th>
                        <td *matCellDef="let row; let i = index;" class="text-center"> {{i + 1}} </td>
                    </ng-container>

                    <ng-container matColumnDef="thutuc">
                        <th *matHeaderCellDef>(2)</th>
                        <td *matCellDef="let row" class="text-justify">{{row.agencyName}}
                        </td>
                    </ng-container>

                    <ng-container matColumnDef="tongHS_thang">
                      <th *matHeaderCellDef >(3)=(4)+(5)<br/>(3)=(6)+(13)</th>
                      <td *matCellDef="let row" class="text-bold" (click)="xemChiTiet('TON_TIEPNHAN', row.agencyId)"> {{ row.hsTon + row.hsTiepNhan}}
                      </td>
                    </ng-container>

                    <ng-container matColumnDef="HS_ton">
                      <th *matHeaderCellDef>(4)</th>
                      <td *matCellDef="let row" (click)="xemChiTiet('TON', row.agencyId)"> {{row.hsTon}}
                      </td>
                    </ng-container>

                    <ng-container matColumnDef="HS_tiepnhan">
                      <th *matHeaderCellDef>(5)</th>
                      <td *matCellDef="let row" (click)="xemChiTiet('TIEPNHAN', row.agencyId)"> {{row.hsTiepNhan}}
                      </td>
                    </ng-container>

                    <ng-container matColumnDef="tongHS_giaiquyet">
                      <th *matHeaderCellDef>(6)=(7)+(8)+(9)</th>
                      <td *matCellDef="let row" class="text-bold"> {{ row.hsTruocHan + row.hsDungHan + row.hsQuaHan}}
                      </td>
                    </ng-container>

                    <ng-container matColumnDef="HS_truochan">
                      <th *matHeaderCellDef>(7)</th>
                      <td *matCellDef="let row" (click)="xemChiTiet('HS_TRUOCHAN', row.agencyId)"> {{row.hsTruocHan}}
                      </td>
                    </ng-container>

                    <ng-container matColumnDef="HS_dunghan">
                      <th *matHeaderCellDef>(8)</th>
                      <td *matCellDef="let row" (click)="xemChiTiet('HS_DUNGHAN', row.agencyId)"> {{row.hsDungHan}}
                      </td>
                    </ng-container>

                    <ng-container matColumnDef="HS_quahan">
                      <th *matHeaderCellDef>(9)</th>
                      <td *matCellDef="let row" (click)="xemChiTiet('HS_QUAHAN', row.agencyId)"> {{row.hsQuaHan}}
                      </td>
                    </ng-container>

                    <ng-container matColumnDef="VBGQ">
                      <th *matHeaderCellDef>(10)</th>
                      <td *matCellDef="let row"> 0
                      </td>
                    </ng-container>

                    <ng-container matColumnDef="VBGQ_web">
                      <th *matHeaderCellDef>(11)</th>
                      <td *matCellDef="let row"> 0
                      </td>
                    </ng-container>

                    <ng-container matColumnDef="VBGQ_1cua">
                      <th *matHeaderCellDef>(12)</th>
                      <td *matCellDef="let row"> 0
                      </td>
                    </ng-container>

                    <ng-container matColumnDef="tongHS_ton">
                      <th *matHeaderCellDef>(13)=(14)+(15)</th>
                      <td *matCellDef="let row" class="text-bold"> {{ row.hsTonConHan + row.hsTonQuaHan}}
                      </td>
                    </ng-container>

                    <ng-container matColumnDef="HS_tonconhan">
                      <th *matHeaderCellDef>(14)</th>
                      <td *matCellDef="let row" (click)="xemChiTiet('HS_TONCONHAN', row.agencyId)"> {{ row.hsTonConHan }}
                      </td>
                    </ng-container>

                    <ng-container matColumnDef="HS_tonquahan">
                      <th *matHeaderCellDef>(15)</th>
                      <td *matCellDef="let row" (click)="xemChiTiet('HS_TONQUAHAN', row.agencyId)"> {{ row.hsTonQuaHan}}
                      </td>
                    </ng-container>

                    <ng-container matColumnDef="VBton">
                      <th *matHeaderCellDef>(16)</th>
                      <td *matCellDef="let row"> 0
                      </td>
                    </ng-container>

                    <ng-container matColumnDef="VBton_web">
                      <th *matHeaderCellDef>(17)</th>
                      <td *matCellDef="let row"> 0
                      </td>
                    </ng-container>

                    <ng-container matColumnDef="VBton_1cua">
                      <th *matHeaderCellDef>(18)</th>
                      <td *matCellDef="let row"> 0
                      </td>
                    </ng-container>

                    <ng-container matColumnDef="tongHS_luyke">
                      <th *matHeaderCellDef>(19)=(20)+(21)+(22)</th>
                      <td *matCellDef="let row" class="text-bold"> {{row.lkHSTruocHan + row.lkHSDungHan + row.lkHSQuaHan}}
                      </td>
                    </ng-container>

                    <ng-container matColumnDef="LK_HS_truochan">
                      <th *matHeaderCellDef>(20)</th>
                      <td *matCellDef="let row" (click)="xemChiTiet('LKHS_TRUOCHAN', row.agencyId)"> {{row.lkHSTruocHan}}
                      </td>
                    </ng-container>

                    <ng-container matColumnDef="LK_HS_dunghan">
                      <th *matHeaderCellDef>(21)</th>
                      <td *matCellDef="let row" (click)="xemChiTiet('LKHS_DUNGHAN', row.agencyId)"> {{row.lkHSDungHan}}
                      </td>
                    </ng-container>

                    <ng-container matColumnDef="LK_HS_quahan">
                      <th *matHeaderCellDef>(22)</th>
                      <td *matCellDef="let row" (click)="xemChiTiet('LKHS_QUAHAN', row.agencyId)"> {{row.lkHSQuaHan}}
                      </td>
                    </ng-container>

                    <ng-container matColumnDef="LK_VB">
                      <th *matHeaderCellDef>(23)</th>
                      <td *matCellDef="let row"> 0
                      </td>
                    </ng-container>

                    <ng-container matColumnDef="LK_VB_web">
                      <th *matHeaderCellDef>(24)</th>
                      <td *matCellDef="let row"> 0
                      </td>
                    </ng-container>

                    <ng-container matColumnDef="LK_VB_1cua">
                      <th *matHeaderCellDef>(25)</th>
                      <td *matCellDef="let row"> 0
                      </td>
                    </ng-container>

                    <ng-container matColumnDef="gr_stt">
                      <th *matHeaderCellDef [attr.rowspan]="3">
                        STT
                      </th>
                    </ng-container>

                    <ng-container matColumnDef="gr_thutuc">
                      <th *matHeaderCellDef [attr.rowspan]="3">
                        Lĩnh vực, thủ tục hành chính
                      </th>
                    </ng-container>

                    <ng-container matColumnDef="grHS_tiepnhan">
                      <th *matHeaderCellDef [attr.colspan]="3">
                        Tổng số hồ sơ đã tiếp nhận trong tháng
                      </th>
                    </ng-container>

                    <ng-container matColumnDef="grHS_giaiquyet">
                      <th *matHeaderCellDef [attr.colspan]="7">
                        Số hồ sơ đã giải quyết trong tháng
                      </th>
                    </ng-container>

                    <ng-container matColumnDef="grHS_tonchuagiaiquyet">
                      <th *matHeaderCellDef [attr.colspan]="6">
                        Số hồ sơ còn tồn chưa giải quyết
                      </th>
                    </ng-container>

                    <ng-container matColumnDef="grHS_luykegiaiquyet">
                      <th *matHeaderCellDef [attr.colspan]="7">
                        Lũy kế hồ sơ đã giải quyết từ đầu năm
                      </th>
                    </ng-container>

                    <ng-container matColumnDef="gr_tongHS_thang">
                      <th *matHeaderCellDef [attr.rowspan]="2">
                        Tổng số
                      </th>
                    </ng-container>

                    <ng-container matColumnDef="grHS_tiepnhan_trongdo">
                      <th *matHeaderCellDef [attr.colspan]="2">
                        Trong đó
                      </th>
                    </ng-container>

                    <ng-container matColumnDef="gr_tongHS_giaiquyet">
                      <th *matHeaderCellDef [attr.rowspan]="2">
                        Tổng số
                      </th>
                    </ng-container>

                    <ng-container matColumnDef="grHS_giaiquyet_trongdo">
                      <th *matHeaderCellDef [attr.colspan]="6">
                        Trong đó
                      </th>
                    </ng-container>

                    <ng-container matColumnDef="gr_tongHS_ton">
                      <th *matHeaderCellDef [attr.rowspan]="2">
                        Tổng số
                      </th>
                    </ng-container>

                    <ng-container matColumnDef="grHS_tonchuagiaiquyet_trongdo">
                      <th *matHeaderCellDef [attr.colspan]="5">
                        Trong đó
                      </th>
                    </ng-container>

                    <ng-container matColumnDef="gr_tongHS_luyke">
                      <th *matHeaderCellDef [attr.rowspan]="2">
                        Tổng số
                      </th>
                    </ng-container>

                    <ng-container matColumnDef="grHS_luykegiaiquyet_trongdo">
                      <th *matHeaderCellDef [attr.colspan]="6">
                        Trong đó
                      </th>
                    </ng-container>

                    <ng-container matColumnDef="lb_HS_ton">
                      <th *matHeaderCellDef>
                        Số hồ sơ chưa giải quyết của tháng trước chuyển qua
                      </th>
                    </ng-container>

                    <ng-container matColumnDef="lb_HS_tiepnhan">
                      <th *matHeaderCellDef>
                        Tổng số hồ sơ tiếp nhận mới trong tháng
                      </th>
                    </ng-container>

                    <ng-container matColumnDef="lb_HS_truochan">
                      <th *matHeaderCellDef>
                        Giải quyết trước hạn
                      </th>
                    </ng-container>

                    <ng-container matColumnDef="lb_HS_dunghan">
                      <th *matHeaderCellDef>
                        Giải quyết đúng hạn
                      </th>
                    </ng-container>

                    <ng-container matColumnDef="lb_HS_quahan">
                      <th *matHeaderCellDef>
                        Giải quyết quá hạn
                      </th>
                    </ng-container>

                    <ng-container matColumnDef="lb_VBGQ">
                      <th *matHeaderCellDef>
                        Số văn bản xin lỗi
                      </th>
                    </ng-container>

                    <ng-container matColumnDef="lb_VBGQ_web">
                      <th *matHeaderCellDef>
                        Số văn bản xin lỗi đã đăng lên trang web
                      </th>
                    </ng-container>

                    <ng-container matColumnDef="lb_VBGQ_1cua">
                      <th *matHeaderCellDef>
                        Số văn bản xin lỗi đã niêm yết tại bộ phận 1 cửa
                      </th>
                    </ng-container>

                    <ng-container matColumnDef="lb_HS_tonconhan">
                      <th *matHeaderCellDef>
                        Số hồ sơ trong thời hạn giải quyết
                      </th>
                    </ng-container>

                    <ng-container matColumnDef="lb_HS_tonquahan">
                      <th *matHeaderCellDef>
                        Số hồ sơ quá hạn giải quyết
                      </th>
                    </ng-container>

                    <ng-container matColumnDef="lb_VBton">
                      <th *matHeaderCellDef>
                        Số văn bản xin lỗi
                      </th>
                    </ng-container>

                    <ng-container matColumnDef="lb_VBton_web">
                      <th *matHeaderCellDef>
                        Số văn bản xin lỗi đã đăng lên trang web
                      </th>
                    </ng-container>

                    <ng-container matColumnDef="lb_VBton_1cua">
                      <th *matHeaderCellDef>
                        Số văn bản xin lỗi đã niêm yết tại bộ phận 1 cửa
                      </th>
                    </ng-container>

                    <ng-container matColumnDef="lb_LK_HS_truochan">
                      <th *matHeaderCellDef>
                        Giải quyết trước hạn
                      </th>
                    </ng-container>

                    <ng-container matColumnDef="lb_LK_HS_dunghan">
                      <th *matHeaderCellDef>
                        Giải quyết đúng hạn
                      </th>
                    </ng-container>

                    <ng-container matColumnDef="lb_LK_HS_quahan">
                      <th *matHeaderCellDef>
                        Giải quyết quá hạn
                      </th>
                    </ng-container>

                    <ng-container matColumnDef="lb_LK_VB">
                      <th *matHeaderCellDef>
                        Số văn bản xin lỗi
                      </th>
                    </ng-container>

                    <ng-container matColumnDef="lb_LK_VB_web">
                      <th *matHeaderCellDef>
                        Số văn bản xin lỗi đã đăng lên trang web
                      </th>
                    </ng-container>

                    <ng-container matColumnDef="lb_LK_VB_1cua">
                      <th *matHeaderCellDef>
                        Số văn bản xin lỗi đã niêm yết tại bộ phận 1 cửa
                      </th>
                    </ng-container>

                    <tr mat-header-row *matHeaderRowDef="['gr_stt','gr_thutuc','grHS_tiepnhan','grHS_giaiquyet','grHS_tonchuagiaiquyet','grHS_luykegiaiquyet']"></tr>
                    <tr mat-header-row *matHeaderRowDef="['gr_tongHS_thang','grHS_tiepnhan_trongdo','gr_tongHS_giaiquyet','grHS_giaiquyet_trongdo','gr_tongHS_ton','grHS_tonchuagiaiquyet_trongdo','gr_tongHS_luyke','grHS_luykegiaiquyet_trongdo']"></tr>
                    <tr mat-header-row *matHeaderRowDef="['lb_HS_ton','lb_HS_tiepnhan','lb_HS_truochan','lb_HS_dunghan','lb_HS_quahan','lb_VBGQ','lb_VBGQ_web','lb_VBGQ_1cua',
                                                          'lb_HS_tonconhan','lb_HS_tonquahan','lb_VBton','lb_VBton_web','lb_VBton_1cua',
                                                          'lb_LK_HS_truochan','lb_LK_HS_dunghan','lb_LK_HS_quahan','lb_LK_VB','lb_LK_VB_web','lb_LK_VB_1cua']"></tr>
                    <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
                    <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
                </table>
            </div>
        </div>
    </div>
</div>
