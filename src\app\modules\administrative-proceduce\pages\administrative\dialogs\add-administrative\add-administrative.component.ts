import { Component, OnInit, ChangeDetectorRef, AfterViewInit, OnDestroy } from '@angular/core';
import { FormGroup, FormControl, Validators } from '@angular/forms';
import { EnvService } from 'src/app/core/service/env.service';
import { SnackbarService } from 'src/app/data/service/snackbar/snackbar.service';
import { KeycloakService } from 'keycloak-angular';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { BasedataService } from 'src/app/data/service/basedata/basedata.service';
import { BasepadService } from 'src/app/data/service/basepad/basepad.service';
import { ReplaySubject, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { DeploymentService } from 'src/app/data/service/deployment.service';
import { FormElement } from 'src/app/data/schema/form-element';
import { MatTableDataSource } from '@angular/material/table';
import { AgencyService } from 'src/app/data/service/basedata/agency.service';
export interface Element {
  id: String;
  name: String;
}

const Agency: Element[] = [];
@Component({
  selector: 'app-add-administrative',
  templateUrl: './add-administrative.component.html',
  styleUrls: ['./add-administrative.component.scss']
})
export class AddAdministrativeComponent implements OnInit, AfterViewInit, OnDestroy {

  countResult1 = 0;
  formTemp;
  countResult = 0;

  // configDepartmentTagId = this.deploymentService.env.OS_HCM.configDepartmentTagId;
  config = this.envService.getConfig();
  selectedLang: string;
  accountId: any;
  pageIndex = 0;
  result = [];
  isFieldArrayInvalid = false;
  languageIdUsed = [228];
  listLanguage = [
    {
      languageId: 228,
      name: 'Tiếng Việt'
    },
    {
      languageId: 46,
      name: 'English'
    }
  ];
  newAttribute: any = {
    languageId: 228,
    name: '',
    listLanguageItem: JSON.parse(JSON.stringify(this.listLanguage))
  };
  newItemfield: any = {
    publicAdmin: '',
    name:'',
    startValid: 0,
    endValid: 0,
    nowValid:0,
    displaySyntax: ''
  }

  fieldArray: Array<any> = [this.newItemfield];
  status: Array<any> = [
    {
      status: 0,
      name: 'Đóng'
    },
    {
      status: 1,
      name: 'Mở'
    }
  ];
  statusVi: Array<any> = [
    {
      status: 0,
      name: 'Đóng'
    },
    {
      status: 1,
      name: 'Mở'
    }
  ];
  statusEn: Array<any> = [
    {
      status: 0,
      name: 'Close'
    },
    {
      status: 1,
      name: 'Open'
    }
  ];
  nameDefault = '';
  addForm = new FormGroup({
    publicAdmin: new FormControl(''),
    status: new FormControl(''),
  });
  isSubmit = false;
  searchSectorKeyword = '';
  listSectorPage = 0;
  listAcceptFileType = [];
  listAcceptExt = [];
  ELEMENTDATA: FormElement[] = [];
  dataSource: MatTableDataSource<FormElement>;
  // listGuide = [];
  protected onDestroy = new Subject<void>();
  isAdmin?: boolean = sessionStorage.getItem('oneGateAdminMaster') === '1';
  showPublicAdministrativeAgency = this.deploymentService.env.OS_HCM.showPublicAdministrativeAgency;
  constructor(
    private dialog: MatDialog,
    public dialogRef: MatDialogRef<AddAdministrativeComponent>,
    private envService: EnvService,
    private snackbarService: SnackbarService,
    private keycloakService: KeycloakService,
    private basedataService: BasedataService,
    private basepadService: BasepadService,
    private deploymentService: DeploymentService,
    private agencyService: AgencyService,
  ) {
    this.listAcceptFileType = this.config.acceptFileType;
    this.listAcceptExt = this.config.acceptFileExtension;
    this.dataSource = new MatTableDataSource(this.ELEMENTDATA);
  }

  async ngOnInit() {
    this.selectedLang = localStorage.getItem('language');
    this.nameDefault = '(Không tìm thấy bản dịch)';
    if (Number(localStorage.getItem('languageId')) === 46) {
      this.status = this.statusEn;
      this.nameDefault = '(No translation found)';
    }
    let searchString = '?page='+ this.pageIndex +'&size=1000&spec=page&status=1';
    if(this.showPublicAdministrativeAgency && this.showPublicAdministrativeAgency.enable && (!this.isAdmin || !this.showPublicAdministrativeAgency.admin)){
      let agencyId = this.getAgencyId();
      let rootAgency = await this.agencyService.getRootAgency(agencyId);
      if(rootAgency && rootAgency.tag.find(o => o.id == this.showPublicAdministrativeAgency.agencyTagId)){
        agencyId = rootAgency.id;
      }
      searchString += '&agency-id=' + agencyId;
    }
    await this.getAllPublicAdministration(searchString); 
  }

  getAgencyId(): string | null {
    const userAgency = JSON.parse(localStorage.getItem('userAgency'));
    if (userAgency){
        return userAgency.id;
    }
    return null;
  }

  ngOnDestroy() {
    this.onDestroy.next();
    this.onDestroy.complete();
  }

  ngAfterViewInit() {
    setTimeout(() => {
      console.clear();
    }, 2000);
  }

  onDismiss() {
    this.dialogRef.close();
  }

  async getAllPublicAdministration(search){
      this.ELEMENTDATA = [];
      let isLast = false;
      do {
        const data = await this.basepadService.searchPublicAdministration(search).toPromise();
        // this.countResult = data.totalElements;
        for (let i = 0; i < data.numberOfElements; i++) {
          this.ELEMENTDATA.push(data.content[i]);
        }
        if(data.last == true){
          isLast = true;
          break;
        }
        this.pageIndex++;
        search = search.replace(/page=\d+/, "page=" + this.pageIndex);
      } while(isLast === false);
      this.dataSource.data = this.ELEMENTDATA;
      console.log('this.dataSource.data',this.dataSource.data);
  }

  checkDisplaySyntax() {
    let result = true;
    if (this.fieldArray.length >= 1) {
      this.fieldArray.forEach(element => {
        // const expression: RegExp = /^[(\w\d\W)+]+\{S+\T+\T+\}[\w+]+$/i;
        // if(element.displaySyntax=='{STT}'){
        //   console.log('so sánh','{STT}',element.displaySyntax);
        // }else{
        //   console.log('so sánh','{STT}');
        // }
        // const kq: boolean = expression.test(element.displaySyntax);

        // if (kq == false) {
        //   result = false;
        // }
        let text = element.displaySyntax;
        const myArray = text.split("{STT}");
        if(myArray.length<2){
          result = false;
        }
      });
    } else {
      result = false;
    }
    return result;
  }


  async onConfirm() {
    this.isSubmit = true;
    const formObj = this.addForm.getRawValue();
     console.log('check', this.fieldArray[0]);
     if (this.fieldArray[0].startValid===null || this.fieldArray[0].endValid===null || formObj.publicAdmin.length===0 || this.fieldArray[0].displaySyntax==='' || this.fieldArray[0].name ==='' || formObj.status.length===0
     ) {
      this.isSubmit = false;
      const msgObj = {
        vi: 'Vui lòng điền đầy đủ thông tin!',
        en: 'Please fill full information!'
      };

      this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
    }else if (this.checkDisplaySyntax()==false) {
      this.isSubmit = false;
      const msgObj = {
        vi: 'Không đúng cú pháp hiển thị!',
        en: 'Please incorrect syntax displayed!'
      };
 
      this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
    } else if (this.fieldArray[0].startValid>=this.fieldArray[0].endValid) {
     this.isSubmit = false;
     const msgObj = {
       vi: 'Giá trị kết thúc không được nhỏ hơn hoặc bằng giá trị bắt đầu!',
       en: 'Please End value cannot be less than or equal to start value!'
     };

     this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
   } else if (this.fieldArray[0].nowValid > this.fieldArray[0].endValid || (this.fieldArray[0].nowValid != 0 && this.fieldArray[0].nowValid < this.fieldArray[0].startValid)) {
    this.isSubmit = false;
    const msgObj = {
      vi: 'Giá trị hiện tại vượt phạm vi bộ số!',
      en: 'Please now value is out of range!'
    };

    this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
  }
    else {
           
        let content = {
          publicAdmin:[],
          name:'',
          startValid:0,
          endValid: 0, 
          nowValid:0,
          displaySyntax:'',
          status:''      
        };
        let search= '?page=0&size=1000&spec=page&id='+ formObj.publicAdmin;
        const res= await this.basepadService.searchPublicAdministration(search).toPromise();
        formObj.publicAdmin=[{
          id: res.content[0].id,
          code: res.content[0].code,
          name: res.content[0].name,
          status: res.content[0].status,
          agencyId: res.content[0].agencyId,
          numberIncreaseAccordingBook: res.content[0]?.numberIncreaseAccordingBook,
          resetAccordingYear: res.content[0]?.resetAccordingYear
        }]

            content.publicAdmin= formObj.publicAdmin;
            content.name = this.fieldArray[0].name;
            content.startValid = this.fieldArray[0].startValid;
            content.endValid = this.fieldArray[0].endValid;
            content.nowValid = this.fieldArray[0].nowValid;
            content.displaySyntax = this.fieldArray[0].displaySyntax;
            content.status= formObj.status;
            console.log('Addform', this.fieldArray);
            // const requestBody = JSON.stringify(content, null, 2);
            console.log('form', content);
  
          
           this.basepadService.postProceAdministration(content).subscribe(data => {
            const result = {
              status: true
            };
            this.dialogRef.close(result);
          }, err => {
            const result = {
              status: false,
              code: err
            };
            this.dialogRef.close(result);
          });
        
  
      }
    

  }

 
  changeName(i, $event) {
    const value = $event.target.value;
    this.fieldArray[i].name = value.trim();

    setTimeout(() => {

    }, 200);
  }

  changeStartValid(i, $event) {
    const value = $event.target.value;
    this.fieldArray[i].startValid = value.trim();

    setTimeout(() => {

    }, 200);
  }
  changeEndValid(i, $event) {
    const value = $event.target.value;
    this.fieldArray[i].endValid = value.trim();

    setTimeout(() => {

    }, 200);
  }

  changeNowValid(i, $event) {
    const value = $event.target.value;
    this.fieldArray[i].nowValid = value.trim();

    setTimeout(() => {

    }, 200);
  }
  changeCode(i, $event) {
    const value = $event.target.value;
    this.fieldArray[i].code = value.trim();

    setTimeout(() => {

    }, 200);
  }
  changeDisplaySyntax(i, $event) {
    const value = $event.target.value;
    this.fieldArray[i].displaySyntax = value.trim();

    setTimeout(() => {

    }, 200);
  }

}

export class ConfirmAddDialogModel {
  constructor() { }
}