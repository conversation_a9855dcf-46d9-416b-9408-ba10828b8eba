import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { AuthGuard } from 'src/app/core/guard/auth.guard';


const routes: Routes = [
  {
    path: 'apply-fee-dossiers',
    loadChildren: () => import('./pages/perform-charge-fees/perform-charge-fees.module').then(m => m.PerformChargeFeesModule),
    canActivate: [AuthGuard],
    data: {
      anyPermissions: ['oneGateAdminMaster', 'onegateApplyFeeDossiers'],
    }
  },
  {
    path: 'statistic-charge-fee',
    loadChildren: () => import('./pages/statistic-charge-fee/statistic-charge-fee.module').then(m => m.StatisticChargeFeeModule),
    canActivate: [AuthGuard],
    data: {
      anyPermissions: ['onegateReportApplyFeeDossiers','oneGateReport']
    }
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class ChargeFeesRoutingModule { }
