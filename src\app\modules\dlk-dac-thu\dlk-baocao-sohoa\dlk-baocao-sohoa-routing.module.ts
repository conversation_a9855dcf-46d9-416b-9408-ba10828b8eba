import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { DlkBaoCaoSoHoaComponent } from './dlk-baocao-sohoa.component';
import { DlkChiTietBaoCaoSoHoaComponent } from './dlk-chitiet-baocao-sohoa/dlk-chitiet-baocao-sohoa.component';
import { AuthGuard } from 'src/app/core/guard/auth.guard';


const routes: Routes = [
  {
    path: '',
    component: DlkBaoCaoSoHoaComponent
  },
  {
    path: 'chi-tiet-ho-so',
    loadChildren: () => import('./dlk-chitiet-baocao-sohoa/dlk-chitiet-baocao-sohoa.module').then(m => m.DlkChiTietBaoCaoSoHoaModule),
    canActivate: [AuthGuard],
    data: {
      anyPermissions: ['dlkChiTietHoSo']
    }
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class DlkBaoCaoSoHoaRoutingModule { }
