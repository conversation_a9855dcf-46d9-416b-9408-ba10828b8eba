.frm_main {
    border-radius: 4px;
    padding: 1em;
    box-shadow: 0px 1px 15px rgba(69, 65, 78, 0.19);
    margin-bottom: 1em;

    .ctrl {
        .btnCtrl {
            background-color: #e8e8e8;
            color: #666;
            float: right;
            margin-left: 1em;

            .mat-icon {
                color: #ce7a58;
                margin-right: .2em;
            }
        }
    }

    .logbookTbl {

        //margin-top: 1.5em;
        .logbookOnlyTbl {
            overflow-x: scroll;
        }

        table {
            border-collapse: collapse;
            width: 100%;
        }

        table tr th {
            border: 1px solid;
            text-align: center;
            font-weight: bold;
            background: #cccccc50;
            //color: #fff;
            border: 1px solid rgb(182, 182, 182);
            padding: 5px;
            font-size: 14px;
        }

        table tr td {
            border: 1px solid rgb(182, 182, 182);
            text-align: center;
            padding: 5px;
            font-size: 14px;
        }

        table tr td a {
            cursor: pointer;
            color: #3c8dbc;
        }

        .sum {
            font-weight: 500;
            background-color: #dff0d8;
        }

        ;
    }
}

::ng-deep .prc_searchbar .searchForm {
    @import "~src/styles/buttons.scss";

    .btn-search {
        @extend .t-btn-search;
    }

    .btn-download-excel {
        @extend .t-btn-download-excel;
    }

    .btn-select-display-col {
        @extend .t-btn-select-display-col;
    }


    .btn-print {
        @extend .t-btn-print;
    }
}

.logbookOnlyTbl_mobile {
    display: none;
}

@media screen and (max-width: 600px) {
    .logbookOnlyTbl {
        display: none;
    }

    .logbookOnlyTbl_mobile {
        display: block !important;

        .mat-header-row {
            display: none;
        }

        .mat-table {
            border: 0;
            vertical-align: middle;

            .mat-row {
                border-bottom: 5px solid #ddd;
                display: block;
                min-height: unset;
            }

            .mat-cell {
                border-bottom: 1px solid #ddd;
                display: block;
                font-size: 14px;
                text-align: right;
                margin-bottom: 4%;
                padding: 0 0.5em;

                &:before {
                    content: attr(data-label);
                    float: left;
                    font-weight: 500;
                    font-size: 14px;
                    width: 45%;
                    text-align: left;
                }

                &:last-child {
                    border-bottom: 0;
                }

                &:first-child {
                    margin-top: 4%;
                }
            }
        }

        .mat-row {
            &:nth-child(even) {
                background-color: unset;
            }

            &:nth-child(odd) {
                background-color: unset;
            }
        }
    }
}

.sum-tr td {
    font-weight: 500;
    background-color: #dff0d8;
}

.sum-tr-2 td {
    font-weight: 500;
    background-color: #337ab7;
    color: #fff;
}

.congthuc_cover {
    padding: 1em;
    color: #8a6d3b;
    background-color: #fcf8e3;
    border: 1px solid transparent;
    border-radius: 4px;
    border-color: #faebcc;
}