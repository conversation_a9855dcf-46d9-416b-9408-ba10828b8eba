import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { DlkBaocaoChithi25ToanXaRoutingModule } from './dlk-baocao-chithi25-toanxa-routing.module';
import { DlkBaocaoChithi25ToanXaComponent } from './dlk-baocao-chithi25-toanxa.component';
import { SharedModule } from 'src/app/shared/shared.module';
import { NgxPrinterModule } from 'ngx-printer';
import {DossierDetailComponent} from '../dialogs/view-detail.component';


@NgModule({
  declarations: [DlkBaocaoChithi25ToanXaComponent],
  imports: [
    CommonModule,
    DlkBaocaoChithi25ToanXaRoutingModule,    
    SharedModule,    
    NgxPrinterModule.forRoot({ printOpenWindow: true })
  ]
})
export class DlkBaocaoChithi25ToanXaModule { }
