import { Component, OnInit, ViewChild } from '@angular/core';
import { MatTableDataSource } from '@angular/material/table';
import { AggJusticeVneidCheck } from './interface/agg-justice-vneid-check-interface';
import { SnackbarService } from 'src/app/data/service/snackbar/snackbar.service';
import { StatisticsService } from 'src/app/data/service/statistics/statistics.service';
import { MatPaginator, MatPaginatorIntl, PageEvent } from '@angular/material/paginator';
import { PadmanService } from 'src/app/data/service/svc-padman/padman.service';
import { EnvService } from 'src/app/core/service/env.service';
import { DossierService } from 'src/app/data/service/dossier/dossier.service';
import { ConfirmEvictFileLLTPDialogModel, EvictFileLLTPComponent } from 'src/app/modules/dossier/pages/search/dialogs/evict-file-lltp/evict-file-lltp.component';
import { MatDialog } from '@angular/material/dialog';
import * as tUtils from 'src/app/data/service/thoai.service';
import { FormControl, FormGroup } from '@angular/forms';
import { DatePipe } from '@angular/common';
import { AdapterService } from 'src/app/data/service/adapter/adapter.service';
@Component({
  selector: 'app-agg-justice-vneid-check',
  templateUrl: './agg-justice-vneid-check.component.html',
  styleUrls: ['./agg-justice-vneid-check.component.scss']
})
export class AggJusticeVneidCheckComponent implements OnInit {
  @ViewChild(MatPaginator) paginator: MatPaginator;
  public isLoading = false;
  public loadingMessage = '';
  public dataSource: MatTableDataSource<AggJusticeVneidCheck>;
  public ELEMENTDATA: AggJusticeVneidCheck[]=[];
  public pageIndex: number = 0;
  public pageSize: number = 5;
  public language = localStorage.getItem('language');
  public config = this.envService.getConfig();
  public searchForm=new FormGroup({
      fromDate:new FormControl(''),
      toDate:new FormControl(''),
      code:new FormControl(''),
      syncStatus:new FormControl('')
    })
  public displayedColumns: string[] = [
    "stt", 
    "code",
    "applicantUser",
    "statusAsync", 
    "statusMessageMCDT", 
    "createdDate", 
    "appointmentDate",  
    "statusMessageLLTP",
    "action", 
  ];
 
  constructor(
    public _MatPaginatorIntl: MatPaginatorIntl,
    private snackbarService: SnackbarService,
    private statisticsService: StatisticsService,
    private padmanService: PadmanService,
    private adapterService : AdapterService,
    private envService: EnvService,
    private dossierService: DossierService,
    private datePipe: DatePipe,
    private dialog: MatDialog,
  ) {this.dataSource = new MatTableDataSource(this.ELEMENTDATA); }

  ngOnInit(): void {
    this._MatPaginatorIntl.itemsPerPageLabel = 'Số dòng';
    this._MatPaginatorIntl.firstPageLabel = 'Trang đầu';
    this._MatPaginatorIntl.lastPageLabel = 'Trang cuối';
    this._MatPaginatorIntl.nextPageLabel = 'Trang tiếp theo';
    this._MatPaginatorIntl.previousPageLabel = 'Trang trước';
    this._MatPaginatorIntl.getRangeLabel = (
      page: number,
      pageSize: number,
      length: number
    ): string => {
      if (length === 0 || pageSize === 0) {
        return `0 của ${length}`;
      }
      length = Math.max(length, 0);
      const startIndex = page * pageSize;
      const endIndex =
        startIndex < length
          ? Math.min(startIndex + pageSize, length)
          : startIndex + pageSize;
      return `${startIndex + 1} - ${endIndex} của ${length}`;
    };
    this.getReportData("");
  }
  onConfirmSearch() {
    const formSearch = this.searchForm.getRawValue();
    let { fromDate, toDate } = formSearch;
    const {code,syncStatus } = formSearch;
    const messages = {
      blankStartDates: {
        vi: 'Ngày bắt đầu không được để trống!',
        en: 'Statistical date cannot be blank!'
      },
      blankEndDates: {
        vi: 'Ngày kết thúc không được để trống!',
        en: 'Statistical date cannot be blank!'
      },
      invalidDateRange: {
        vi: 'Ngày bắt đầu phải nhỏ hơn hoặc bằng ngày kết thúc!',
        en: 'Start date must be less than or equal to end date!'
      },
    };
    if (fromDate && toDate === '') {
      return this.snackbarService.openSnackBar(0, '', messages.blankEndDates[this.language], 'error_notification', 3000);
    } 
    else if (fromDate === '' && toDate) {
      return this.snackbarService.openSnackBar(0, '', messages.blankStartDates[this.language], 'error_notification', 3000);
    } 
    else if (new Date(toDate) < new Date(fromDate)) {
      return this.snackbarService.openSnackBar(0, '', messages.invalidDateRange[this.language], 'error_notification', 3000);
    }
    let params: string[] = [];

    if (code.trim()) {
      params.push(`code=${code.trim()}`);
    } 
    
    if (syncStatus) {
        params.push(`syncStatus=${syncStatus}`);
    }
    
    if (fromDate) {
        fromDate = this.formatDateWithoutTimezone(fromDate);
        params.push(`fromDateString=${this.datePipe.transform(fromDate, 'yyyy-MM-dd')}T00:00:00.000Z`);
    }
    
    if (toDate) {
        toDate = this.formatDateWithoutTimezone(toDate);
        params.push(`toDateString=${this.datePipe.transform(toDate, 'yyyy-MM-dd')}T23:59:59.999Z`);
    }
    const searchParams = params.length ? `?${params.join('&')}` : '';
    this.getReportData(searchParams);
    
  }
  getStatusClass(status: string): string {
    switch (status) {
      case 'Đã đồng bộ':
        return 'status-synced';
      case 'Đã có kết quả':
        return 'status-result';
      case 'Chờ đồng bộ':
        return 'status-pending';
      case 'Không xác định':
        return 'status-unknown';
      default:
        return '';
    }
  }
  
  getReportData(searchParams): void {
    const messages = {
      searchParamNullVariable: {
        vi: 'Đã có lỗi xảy ra khi tải dữ liệu!',
        en: 'An error has occurred!',
      },
    };

    this.isLoading = true;
    this.loadingMessage = this.language === 'vi' ? 'Đang tải dữ liệu...' : 'Loading data...';

    this.statisticsService.aggGetListDossierIntegrationVneidLltp(searchParams).subscribe(
      (data) => {
        this.ELEMENTDATA = data.data;
        this.dataSource.data = this.ELEMENTDATA;
        this.dataSource.paginator = this.paginator;
        this.isLoading = false;
        this.loadingMessage = '';
      },
      (error) => {
        this.isLoading = false;
        this.loadingMessage = '';
        const message = messages.searchParamNullVariable[this.language];
        this.snackbarService.openSnackBar(0, '', message, 'error_notification', 3000);
      }
    );
  }
  onPageChange(event: PageEvent) {
    this.pageIndex = event.pageIndex;
    this.pageSize = event.pageSize;
  }
  async sendLLTPVNeID(code: string) {
    if (!code) {
      const msgObj2 = {
        vi: 'Không lấy được mã hồ sơ LLTP VNeID',
        en: 'Cannot retrieve LLTP VNeID record code',
      };
      this.snackbarService.openSnackBar(1, msgObj2[this.language], '', 'error_notification', this.config.expiredTime);
      this.reloadPageAfterDelay();
      return;
    }
  
    try {
      const result: any = await this.padmanService.sendLLTPVNeID(code, null, null, true).toPromise();
  
      if (result?.status === 1) {
        const successMsg = {
          vi: result?.statusDescription || 'Giao dịch thành công LLTP VNeID',
          en: 'Send LLTP VNeID success!',
        };
        this.snackbarService.openSnackBar(1, successMsg[this.language], '', 'success_notification', this.config.expiredTime);
      } else {
        const errorDescription = result?.error || 'Lỗi trục LGSP timeout';
        const errorMsg = {
          vi: errorDescription,
          en: 'Send LLTP VNeID failed!',
        };
        this.snackbarService.openSnackBar(1, errorMsg[this.language], '', 'error_notification', this.config.expiredTime);
      }
    } catch (error) {
      console.error(error);
      const errorMsg = {
        vi: 'Lỗi trục LGSP timeout',
        en: 'LGSP timeout error',
      };
      this.snackbarService.openSnackBar(1, errorMsg[this.language], '', 'error_notification', this.config.expiredTime);
    } finally {
      this.reloadPageAfterDelay();
    }
  }
  
  private reloadPageAfterDelay(delay: number = 3000) {
    setTimeout(() => {
      window.location.reload();
    }, delay); 
  }
  
  async syncLLTPVNEID(code) {
    const result:any = await this.dossierService.syncLLTPVNEID(code).toPromise();
    const msgObj = {vi: 'Đang thực hiện đồng bộ, vui lòng chờ đợi!', en: 'Send success!'};
    this.snackbarService.openSnackBar(1, msgObj[this.language], '', 'success_notification', this.config.expiredTime);
    window.location.reload();
  }
  eVictFileLLTP(code) {
      const dialogData = new ConfirmEvictFileLLTPDialogModel(code);
      const dialogRef = this.dialog.open(EvictFileLLTPComponent, {
        minWidth: '75vw',
        maxWidth: '75vw',
        maxHeight: '90vh',
        data: dialogData,
        disableClose: true,
        autoFocus: false
      });
      dialogRef.afterClosed().subscribe(dialogResult => {
        if(dialogResult.status == true){
          const msgObj = {
            vi: 'Thu hồi phiếu LLTP thành công!',
            en: 'Successfully revoked LLTP votes!'
          };
          this.snackbarService.openSnackBar(1, msgObj[this.language], '', 'success_notification', this.config.expiredTime);
        } else {
          const msgObj = {
            vi: tUtils.nonNull(dialogResult, "errorMessage") ?  ('Thu hồi phiếu LLTP thất bại!' + dialogResult?.errorMessage) : 'Thu hồi phiếu LLTP thất bại!',
            en: 'Fail revoked LLTP votes!'
          };
          this.snackbarService.openSnackBar(0, msgObj[this.language], '', 'error_notification', this.config.expiredTime);
        }
      });
    }
    formatDateWithoutTimezone(date: string | Date): string {
      const d = new Date(date);
      const year = d.getFullYear();
      const month = ('0' + (d.getMonth() + 1)).slice(-2); 
      const day = ('0' + d.getDate()).slice(-2);
      return `${year}-${month}-${day}`;
    }
    isCheckConnect(type: string): void {
      const connectionMessages = {
        btp: {
          success: "Kết nối đến Bộ Tư Pháp Thành Công!",
          error: "Kết nối đến Bộ Tư Pháp Thất Bại!"
        },
        vneid: {
          success: "Kết nối đến VNEID Thành Công!",
          error: "Kết nối đến VNEID Thất Bại!"
        }
      };
      this.adapterService.aggIsCheckConnectVNeIDLttp(type).subscribe(
        (data) => {
          const messages = connectionMessages[type];
          if (messages) {
            const notificationType = data.status === "true" ? 'success_notification' : 'error_notification';
            const message = data.status === "true" ? messages.success : messages.error;
            this.snackbarService.openSnackBar(
              data.status === "true" ? 1 : 0,
              '',
              message,
              notificationType,
              5000
            );
          }
        },
        (error) => {
          this.isLoading = false;
          this.loadingMessage = '';
          this.snackbarService.openSnackBar(0, '', "Đã có lỗi xảy ra khi kiểm tra kết nối!", 'error_notification', 3000);
        }
      );
    }
    
}