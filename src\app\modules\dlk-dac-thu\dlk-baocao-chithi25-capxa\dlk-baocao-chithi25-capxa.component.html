<h2>BÁO CÁO CHỈ THỊ 25 CẤP XÃ</h2>
<div class="prc_searchbar">

    <div fxLayout="row" fxLayout.xs="row" fxLayout.sm="row" class="formFieldOutline" fxLayoutAlign="space-between">
        <div appearance="outline" fxFlex='grow'>
            <div fxLayout="row" fxLayout.xs="row" fxLayout.sm="row" class="formFieldOutline" fxLayoutAlign="space-left">

                <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                    <mat-label i18n>Cơ quan</mat-label>
                    <mat-select name="lstCoQuan" msInfiniteScroll (selectionChange)="changeAgency()" [(ngModel)]="agencyId">

                        <mat-option *ngFor="let item of listAgencyAccept" [value]="item.id">{{item.name}}</mat-option>
                    </mat-select>
                </mat-form-field>

            </div>
            <div fxLayout="row" fxLayout.xs="row" fxLayout.sm="row" class="formFieldOutline" fxLayoutAlign="space-left">

                <mat-form-field appearance="outline" fxFlex.gt-sm="24" fxFlex.gt-xs="49.5" fxFlex='grow'>
                    <mat-label>Từ ngày</mat-label>
                    <input matInput [matDatepicker]="pickerAcceptFrom" [(ngModel)]="startDate">
                    <mat-datepicker-toggle matSuffix [for]="pickerAcceptFrom"></mat-datepicker-toggle>
                    <mat-datepicker #pickerAcceptFrom></mat-datepicker>
                </mat-form-field>
                <mat-form-field appearance="outline" class="ml-20" fxFlex.gt-sm="24" fxFlex.gt-xs="49.5" fxFlex='grow'>
                    <mat-label>Đến ngày</mat-label>
                    <input matInput [matDatepicker]="pickerAcceptTo" [(ngModel)]="endDate">
                    <mat-datepicker-toggle matSuffix [for]="pickerAcceptTo"></mat-datepicker-toggle>
                    <mat-datepicker #pickerAcceptTo></mat-datepicker>
                </mat-form-field>
            </div>
        </div>


    </div>


    <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="row" fxLayoutAlign="end" style="padding-bottom: 1em;">
        <button mat-flat-button fxFlex.lt-md="22" fxFlex.md="20" fxFlex.gt-md="12" fxFlex.gt-xs="49.5" fxFlex='grow' style="background-color: #ce7a58;" class="btn-search" type="submit" (click)="thongKe()" [disabled]="waitingDownloadExcel"> 
      <mat-icon class="iconStatistical">bar_chart</mat-icon><span>Thống kê</span>
      <mat-progress-bar mode="indeterminate" *ngIf="waitingDownloadExcel"></mat-progress-bar>
    </button>
        <div fxFlex='1'></div>
        <button mat-flat-button fxFlex.lt-md="22" fxFlex.md="20" fxFlex.gt-md="12" fxFlex.gt-xs="49.5" fxFlex='grow' class="btn-download-excel" (click)="exportToExcel()" [disabled]="waitingDownloadExcel" style="background-color: #38A938;">
      <mat-icon class="iconStatistical">cloud_download</mat-icon>
      <span>Xuất excel</span>
      <mat-progress-bar mode="indeterminate" *ngIf="waitingDownloadExcel"></mat-progress-bar>
    </button>

    </div>
</div>

<div fxLayout="row" fxLayoutAlign="center">
    <div class="frm_main" fxFlex="grow">
        <div class="logbookTbl">
            <div class="logbookOnlyTbl">
                <table class="table">
                    <thead>
                        <tr>
                            <th rowspan="4">STT</th>
                            <th rowspan="4">Lĩnh vực/Thủ tục</th>
                            <th colspan="3">Tổng số hồ sơ đã tiếp nhận trong tháng</th>
                            <th colspan="10">Số hồ sơ đã giải quyết trong tháng</th>
                            <th colspan="3">Số hồ sơ còn tồn chưa giải quyết</th>
                        </tr>
                        <tr>
                            <th rowspan="3">Tổng số</th>
                            <th colspan="2">Trong đó</th>
                            <th rowspan="3">Tổng số</th>
                            <th colspan="9">Trong đó</th>
                            <th rowspan="3">Tổng số</th>
                            <th colspan="2">Trong đó</th>

                        </tr>
                        <tr>
                            <th rowspan="2">Số hồ sơ chưa giải quyết của tháng trước chuyển qua</th>
                            <th rowspan="2">Tổng số hồ sơ tiếp nhận mới trong tháng</th>

                            <th rowspan="2">Giải quyết trước, đúng hạn</th>
                            <th rowspan="2">Giải quyết quá hạn</th>
                            <th colspan="5">Số văn bản xin lỗi</th>
                            <th rowspan="2">Công khai số văn bản xin lỗi lên hệ thông thông tin giải quyết TTHC (iGate)</th>
                            <th rowspan="2">Số văn bản xin lỗi đã niêm yết tại bộ phận 1 cửa</th>

                            <th rowspan="2">Số hồ sơ trước, đúng thời hạn</th>
                            <th rowspan="2">Số hồ sơ quá thời hạn</th>
                        </tr>
                        <tr>
                            <th>Tổng số</th>
                            <th>Do giải quyết quá hạn</th>
                            <th>Do tiếp nhận thành phần hồ sơ không đủ</th>
                            <th>Do hồ sơ bị mất, thất lạc hoặc hư hỏng</th>
                            <th>Do sai sót trong kết quả giải quyết</th>
                        </tr>
                        <tr>
                            <th>(1)</th>
                            <th>(2)</th>
                            <th>(3)</th>
                            <th>(4)</th>
                            <th>(5)</th>
                            <th>(6)</th>
                            <th>(7)</th>
                            <th>(8)</th>
                            <th>(9)</th>
                            <th>(10)</th>
                            <th>(11)</th>
                            <th>(12)</th>
                            <th>(13)</th>
                            <th>(14)</th>
                            <th>(15)</th>
                            <th>(16)</th>
                            <th>(17)</th>
                            <th>(18)</th>

                        </tr>
                    </thead>
                    <tbody>

                        <ng-container *ngFor="let item of ListMain;let i = index ">
                            <tr *ngIf="ListMain">
                                <td>{{i+1}}</td>
                                <td style="text-align: left;"><a (click)="ViewDetail(i,item.show)">{{item.sector}}</a></td>
                                <td>{{item.tongSoHoSo}}</td>
                                <td>{{item.soHoSoTonKyTruoc}}</td>
                                <td>{{item.soHoSoTN}}</td>
                                <td>{{item.soHoSoDXL}}</td>
                                <td>{{item.soHoSoDXLTrongHan}}</td>
                                <td>{{item.soHoSoDXLQuaHan}}</td>
                                <td>{{item.tongSoVBXL}}</td>
                                <td>{{item.vanban_QUAHAN}}</td>
                                <td>{{item.vanban_THIEU_TPHS}}</td>
                                <td>{{item.vanban_MAT_HS}}</td>
                                <td>{{item.vanban_SAISOT}}</td>
                                <td>{{item.tongSoVBXL}}</td>
                                <td>{{item.tongSoVBXL}}</td>
                                <td>{{item.soHoSoTON}}</td>
                                <td>{{item.soHoSoTONCONHAN}}</td>
                                <td>{{item.soHoSoTONQUAHAN}}</td>
                            </tr>
                            <ng-container *ngFor="let item1 of item.data;let j = index ">
                                <tr *ngIf="item.show">
                                    <td>{{colToLetter(j)}}</td>
                                    <td style="text-align: left;">{{item1.procedureName}}</td>
                                    <td>{{item1.tongSoHoSo}}</td>
                                    <td>{{item1.soHoSoTonKyTruoc}}</td>
                                    <td>{{item1.soHoSoTN}}</td>
                                    <td>{{item1.soHoSoDXL}}</td>
                                    <td>{{item1.soHoSoDXLTrongHan}}</td>
                                    <td>{{item1.soHoSoDXLQuaHan}}</td>
                                    <td>{{item1.tongSoVBXL}}</td>
                                    <td>{{item1.vanban_QUAHAN}}</td>
                                    <td>{{item1.vanban_THIEU_TPHS}}</td>
                                    <td>{{item1.vanban_MAT_HS}}</td>
                                    <td>{{item1.vanban_SAISOT}}</td>
                                    <td>{{item1.tongSoVBXL}}</td>
                                    <td>{{item1.tongSoVBXL}}</td>
                                    <td>{{item1.soHoSoTON}}</td>
                                    <td>{{item1.soHoSoTONCONHAN}}</td>
                                    <td>{{item1.soHoSoTONQUAHAN}}</td>
                                </tr>
                            </ng-container>
                        </ng-container>
                        <tr *ngIf="TongCapSo" class="sum">
                            <td colspan="2"> TỔNG </td>
                            <td><a (click)="GetDetailDossier(agencyId,agencyName,1,1,0,0,0,0,0,0,0,0,0)">{{TongCapSo.tongSoHoSo}}</a></td>
                            <td><a (click)="GetDetailDossier(agencyId,agencyName,1,0,1,0,0,0,0,0,0,0,0)">{{TongCapSo.soHoSoTonKyTruoc}}</a></td>
                            <td><a (click)="GetDetailDossier(agencyId,agencyName,1,0,0,1,0,0,0,0,0,0,0)">{{TongCapSo.soHoSoTN}}</a></td>
                            <td><a (click)="GetDetailDossier(agencyId,agencyName,1,0,0,0,1,0,0,0,0,0,0)">{{TongCapSo.soHoSoDXL}}</a></td>
                            <td><a (click)="GetDetailDossier(agencyId,agencyName,1,0,0,0,0,1,0,0,0,0,0)">{{TongCapSo.soHoSoDXLTrongHan}}</a></td>
                            <td><a (click)="GetDetailDossier(agencyId,agencyName,1,0,0,0,0,0,1,0,0,0,0)">{{TongCapSo.soHoSoDXLQuaHan}}</a></td>
                            <td>{{TongCapSo.tongSoVBXL}}</td>
                            <td>{{TongCapSo.vanban_QUAHAN}}</td>
                            <td>{{TongCapSo.vanban_THIEU_TPHS}}</td>
                            <td>{{TongCapSo.vanban_MAT_HS}}</td>
                            <td>{{TongCapSo.vanban_SAISOT}}</td>
                            <td>{{TongCapSo.tongSoVBXL}}</td>
                            <td>{{TongCapSo.tongSoVBXL}}</td>
                            <td><a (click)="GetDetailDossier(agencyId,agencyName,1,0,0,0,0,0,0,1,0,0,0)">{{TongCapSo.soHoSoTON}}</a></td>
                            <td><a (click)="GetDetailDossier(agencyId,agencyName,1,0,0,0,0,0,0,0,1,0,0)">{{TongCapSo.soHoSoTONCONHAN}}</a></td>
                            <td><a (click)="GetDetailDossier(agencyId,agencyName,1,0,0,0,0,0,0,0,0,1,0)">{{TongCapSo.soHoSoTONQUAHAN}}</a></td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>