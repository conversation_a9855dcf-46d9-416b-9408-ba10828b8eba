@media screen and (max-width: 959px)  {
    table{
        width: 100% !important;
    }
    .frm_tbl0{
        display: none;
    }
    .frm_tbl0_mobile {
        display: block !important;
        width: 100%;
        .mat-header-row {
            display: none;
        }

        .mat-table {
            border: 0;
            vertical-align: middle;

            .mat-row {
                border-bottom: 5px solid #ddd;
                display: block;
                min-height: unset;
            }

            .mat-cell {
                border-bottom: 1px solid #ddd;
                display: block;
                font-size: 14px;
                text-align: right;
                margin-bottom: 4%;
                padding: 0 0.5em;

                &:before {
                    content: attr(data-label);
                    float: left;
                    font-weight: 500;
                    font-size: 14px;
                }

                &:last-child {
                    border-bottom: 0;
                }

                &:first-child {
                    margin-top: 4%;
                }
            }
        }

        .mat-row {
            &:nth-child(even) {
                background-color: unset;
            }

            &:nth-child(odd) {
                background-color: unset;
            }
        }
        .ngx-pagination {
            margin-left: -2rem !important;
        }
    }
}
.mat-card-content {
    border-bottom: 1px solid #ddd !important;
    display: flex;
    justify-content: space-between;
    width: 100%;
}
.cell_info {
    color: blue;
    text-decoration: none;
    cursor: pointer;
    max-width: 100%;
}

.example-form-field {
    margin-right: 20px;
}

td.mat-footer-cell {
    text-align: center;
}

.search-nested {
    width: 80% !important;
    height: 50px !important;
    margin-left: 15px !important;
}

.clear-search-nested {
    right: 0 !important;
    position: absolute !important;
    top: 0 !important;
}

// ================================= searchForm
.hidden {
    display: none !important;
}

::ng-deep .frm_searchbar .searchForm .mat-form-field-appearance-outline .mat-form-field-outline {
    color: transparent;
}

::ng-deep .frm_searchbar .searchForm .mat-form-field-appearance-outline .mat-form-field-outline-thick {
    color: #dddddd;
}

::ng-deep .frm_searchbar .searchForm .mat-form-field-appearance-outline .mat-form-field-outline {
    background-color: #eaebeb;
    border-radius: 5px;
}


::ng-deep .frm_searchbar .mat-form-field-appearance-outline .mat-form-field-infix {
    padding: 0.8em 0;
}

::ng-deep .frm_searchbar .mat-form-field-label-wrapper {
    top: -1em;
}

::ng-deep .frm_searchbar .mat-form-field.mat-focused .mat-form-field-label {
    color: #ce7a58;
    font-size: 18px;
    margin-bottom: 1em;
}

::ng-deep .frm_searchbar .mat-form-field-appearance-outline.mat-form-field-can-float.mat-form-field-should-float .mat-form-field-label {
    color: #ce7a58;
    font-size: 18px;
    margin-bottom: 1em;
}

// ================================= table + frm_tbl + tab 1
::ng-deep .frm_tbl0 table {
    width: 100%;
    margin-top: 1.5vh;
}

.data-label {
    word-wrap: break-word;
}

::ng-deep .frm_tbl0 th.mat-header-cell,
td.mat-cell,
td.mat-footer-cell {
    text-align: center;
    border: 1px solid #CCC;
    padding: 0 !important;
    word-wrap: break-word;
    color: #495057;
}

::ng-deep .frm_tbl0 .mat-header-row {
    background-color: #e8e8e8;
    word-wrap: break-word;
    // white-space: none;
    // white-space: normal;
}

::ng-deep .frm_tbl0 .mat-header-row .mat-header-cell p {
    margin-bottom: 0;
    font-weight: 400;
    font-style: italic;
    word-wrap: break-word;
}

::ng-deep .frm_tbl0 .mat-row:nth-child(even) {
    background-color: #FAFAFA;
}

::ng-deep .frm_tbl0 .mat-row:nth-child(odd) {
    background-color: #fff;
}

tr.mat-footer-row {
    font-weight: bold;
}

::ng-deep {
    .btnDisabled {
        cursor: not-allowed;
    }

    .mat-button-wrapper {
        display: flex;
        justify-content: center;

        .mat-spinner {
            margin-right: 0.3em;
            align-self: center;

            circle {
                // stroke: #ce7a58;
                stroke: white;
            }
        }
    }

    .iconStatistical {
        padding-top: 5px;
    }
}

a:hover {
    text-decoration: underline;
    cursor: pointer;
}

::ng-deep .frm_searchbar .searchForm {
    @import "~src/styles/buttons.scss";

    .btn-download-excel {
        @extend .t-btn-download-excel;
    }

    .btn-search {
        @extend .t-btn-search;
    }
}

// ================================= frm_main
.frm_main {
    border-radius: 4px;
    box-shadow: 0px 1px 15px rgba(69, 65, 78, 0.19);
    margin-top: 15px;
    padding: 1em;
}

::ng-deep {
    .btnDisabled {
        cursor: not-allowed;
    }

    .mat-button-wrapper {
        display: flex;
        justify-content: center;

        .mat-spinner {
            margin-right: 0.3em;
            align-self: center;

            circle {
                // stroke: #ce7a58;
                stroke: white;
            }
        }
    }

    .iconStatistical {
        padding-top: 5px;
    }
}

::ng-deep .frm_searchbar .searchForm {
    @import "~src/styles/buttons.scss";

    .btn-search {
        @extend .t-btn-search;
    }

    .btn-download-excel {
        @extend .t-btn-download-excel;
    }
}

// enable sticky with tab
::ng-deep #my-tab-group .mat-tab-body-wrapper .mat-tab-body.mat-tab-body-active {
    overflow: visible !important;
}

::ng-deep #my-tab-group .mat-tab-body-wrapper {
    overflow: visible !important;
}

::ng-deep #my-tab-group .mat-tab-body-wrapper .mat-tab-body-content {
    overflow: visible !important;
}

::ng-deep .frm_tbl0 .element-details {
    overflow: hidden;
    display: flex;
}

tr.detail-row {
    height: 0;
}

tr.detail6d-row {
    height: 0;
}

tr.element-row:not(.expanded-row):hover {
    background: whitesmoke;
}

tr.element-row:not(.expanded-row):active {
    background: #efefef;
}

.element-row td {
    border-bottom-width: 0;
}

tr.element6d-row:not(.expanded6d-row):hover {
    background: whitesmoke;
}

tr.element6d-row:not(.expanded6d-row):active {
    background: #efefef;
}

.element6d-row td {
    border-bottom-width: 0;
}

.example-element-detail {
    overflow: hidden;
    display: flex;
}

tr.detail-row {
    height: 0;
}

::ng-deep .frm_tbl1 th.mat-header-cell-child,
td.mat-cell-child,
td.mat-footer-cell-child {
    text-align: center;
    border-right-width: 1px;
    border-top-width: 1px;
    border-left-width: 1px;
    border-right-style: solid;
    border-left-style: solid;
    border-top-style: solid;
    border-right-color: rgb(204, 204, 204);
    border-top-color: rgb(204, 204, 204);
    border-left-color: rgb(204, 204, 204);
    padding: 0 !important;
    word-wrap: break-word;
    color: #495057;
}