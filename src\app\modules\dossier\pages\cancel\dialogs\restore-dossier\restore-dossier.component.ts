import { HumanService } from '../../../../../../data/service/human/human.service';
import { Component, OnInit, Inject, AfterViewInit } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { KeycloakService } from 'keycloak-angular';
import { EnvService } from 'src/app/core/service/env.service';
import { DossierService } from 'src/app/data/service/dossier/dossier.service';
import { UserService } from 'src/app/data/service/user.service';
import { DeploymentService } from 'src/app/data/service/deployment.service';

@Component({
  selector: 'app-restore-dossier',
  templateUrl: './restore-dossier.component.html',
  styleUrls: ['./restore-dossier.component.scss']
})
export class RestoreDossierComponent implements OnInit, AfterViewInit {

  config = this.envService.getConfig();
  userId = localStorage.getItem('tempUID');
  selectedLangId = Number(localStorage.getItem('languageId')) || 228;
  env = this.deploymentService.getAppDeployment().env;

  dossierId: string;
  dossierCode: string;
  checkOfficer = false;
  isAdmin = false;

  dossierDetail = [];
  listFile = [];

  userInfo = {
    id: '',
    fullname: '',
    username: '',
    accountId: '',
  };
  dossierTaskStatus = { id: '', name: []};
  dossierMenuTaskRemind = {id: '', name: []};
  constructor(
    private dossierService: DossierService,
    private keycloakService: KeycloakService,
    private userService: UserService,
    private envService: EnvService,
    private humanService: HumanService,
    private deploymentService: DeploymentService,
    public dialogRef: MatDialogRef<RestoreDossierComponent>,
    @Inject(MAT_DIALOG_DATA) public data: ConfirmRestoreDialogModel
  ) {
    this.dossierId = data.id;
    this.dossierCode = data.dossierCode;
  }

  ngOnInit(): void { }

  ngAfterViewInit() {
    const permissions = this.userService.getUserPermissions();
    for (const p of permissions) {
      if (p.permission.code === 'admin' || p.permission.code === 'oneGateAdminMaster') {
        this.isAdmin = true;
        break;
      }
    }
    this.getDossierDetail();
    this.getUserInfo();
    this.checkIsAdmin();
  }
  getDossierDetail() {
    this.dossierService.getDossierDetail(this.dossierId).subscribe(data => {
      this.dossierDetail = [];
      this.dossierDetail.push(data);

      // getlistfile
      if (data.dossierFormFile !== undefined || data.dossierFormFile !== null) {
        data.dossierFormFile.forEach(element => {
          if (element.file.length > 0) {
            element.file.forEach(f => {
              this.listFile.push(f);
            });
          }
        });
      }
    });
  }

  getUserInfo() {
    this.keycloakService.loadUserProfile().then(user => {
      console.log(user);
      this.userInfo.username = user.username;
      // tslint:disable-next-line: no-string-literal
      this.userInfo.id = user['attributes'].user_id[0];
      // tslint:disable-next-line: no-string-literal
      this.userInfo.accountId = user['attributes'].account_id[0];
      // tslint:disable-next-line: no-string-literal
      this.userService.getUserInfo(user['attributes'].user_id).subscribe(data => {
        this.userInfo.fullname = data.fullname;
      });
    });
  }

  async onConfirm() {    
    this.dossierService.restoreDossierStatus(this.dossierId).subscribe(data => {
      this.dialogRef.close(1);
    }, err => {
      this.dialogRef.close(null);
    });
  }

  onDismiss() {
    this.dialogRef.close();
  }

  //check user is admin
  checkIsAdmin() {
    if (this.isAdmin === true) {
      const searchStr = '?user-id=' + this.userId + '&agency-id=' + this.config?.rootAgency?.id;
      this.humanService.getCheckUserAdmin(searchStr).subscribe(data => {
        this.checkOfficer = true
      }, err => {
        this.checkOfficer = false;
      });
    }
  }
}

export class ConfirmRestoreDialogModel {
  constructor(public id: string, public dossierCode: string) {
  }
}
