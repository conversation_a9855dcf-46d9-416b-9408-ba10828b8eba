import { DatePipe } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { FormGroup, FormControl } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import { MatTableDataSource } from '@angular/material/table';
import { EnvService } from 'src/app/core/service/env.service';
import { BasedataService } from 'src/app/data/service/basedata/basedata.service';
import { DeploymentService } from 'src/app/data/service/deployment.service';
import { DLKStatisticService } from 'src/app/data/service/dlk-dac-thu/dlk-statistic.service';
import { ExportExcelService } from 'src/app/data/service/export-excel/export-excel.service';
import { ProcedureService } from 'src/app/data/service/procedure/procedure.service';
import { ReportService } from 'src/app/data/service/report/report.service';
import { SnackbarService } from 'src/app/data/service/snackbar/snackbar.service';
import { StatisticalService } from 'src/app/data/service/statistical/statistical.service';
import * as tUtils from 'src/app/data/service/thoai.service';
import { DlkDigitizationReportDetailComponent } from './dlk-digitization-report-detail/dlk-digitization-report-detail.component';
import { DLKReportDigitizaTionService } from 'src/app/data/service/dlk-dac-thu/dlk-report-digitization.service';

@Component({
  selector: 'app-dlk-digitization-report',
  templateUrl: './dlk-digitization-report.component.html',
  styleUrls: ['./dlk-digitization-report.component.scss']
})
export class DLKDigitizationReportComponent implements OnInit {
  userAgency?: any = JSON.parse(localStorage.getItem('userAgency'));
  rootAgencyId: string;
  data: Array<any> = [];
  dataSource: MatTableDataSource<Row>;
  dataSourceTable: MatTableDataSource<any>;
  ELEMENTDATA: Array<Row> = [];
  LISTELEMENTDATA: Array<Row[]> = [];
  displayedColumns: string[] = ['stt', 'tencoquan', 'sohoahstiepnhan', 'sohoahscosohoatphs', 'tylesohoatphs', 'sohschuasohoatphs', 'sohsdagiaiquyet', 'sohscosohoakq', 'tylesohoakq', 'sohschuasohoakq'];
  searchForm = new FormGroup({
    fromDate: new FormControl(new Date(Date.now() - 10 * 24 * 60 * 60 * 1000)),
    toDate: new FormControl(new Date()),
    agencyLevelCtrl: new FormControl(''),
    agencyCtrl: new FormControl(''),
  });
  public searchAgencyCtrl: FormControl = new FormControl();
  env = this.deploymentService.getAppDeployment()?.env;
  config = this.envService.getConfig();
  nowDate = tUtils.newDate();
  toDate;
  fromDate;
  configProvinceTagId;
  configDepartmentTagId;
  configDistrictTagId
  configCommueTagId;
  headAgencyId;
  isLoading = false;
  footerData: any[][] = [];
  agencyNameExcel = "";
  listAgency: Array<any> = [];
  listAgencyCapXa: Array<any> = [];
  ListAgencySearch: Array<any> = [];
  ListAgencySearchCurrent: Array<any> = [];
  Agency = this.deploymentService.env.OS_DLK;
  dsLoc: any[] = [];
  dsCapTinh: any[] = [];
  dsCapHuyen: any[] = [];
  dsCapXa: any[] = [];
  timeOutSearch = null;
  msTimeOutSearch = this.deploymentService.env.statistics.msTimeOutSearch;
  listIdProcedureDigitizationReport = this.deploymentService.getAppDeployment().listIdProcedureDigitizationReport ?? [];
  strIdProcedureDigitizationReport = "";

  constructor(
    private snackbarService: SnackbarService,
    private deploymentService: DeploymentService,
    private envService: EnvService,
    private dlkReportDigitizaTionService: DLKReportDigitizaTionService,
    private datePipe: DatePipe,
    private dialog: MatDialog,
  ) {
    var today = new Date();
    this.fromDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    this.toDate = today;
  }

  ngOnInit(): void {
    this.dataSource = new MatTableDataSource(this.ELEMENTDATA);
    this.configProvinceTagId = this.env?.OS_DLK?.configProvinceTagId ? this.env?.OS_DLK?.configProvinceTagId : "";
    this.configDepartmentTagId = this.env?.OS_DLK?.configDepartmentTagId ? this.env?.OS_DLK?.configDepartmentTagId : "";
    this.configDistrictTagId = this.env?.OS_DLK?.configDistrictTagId ? this.env?.OS_DLK?.configDistrictTagId : "";
    this.configCommueTagId = this.env?.OS_DLK?.configCommueTagId ? this.env?.OS_DLK?.configCommueTagId : "";
  }

  addFormDialog(agencyId, type, totalItem, agencyName) {
    var dialogTitle = "";
    var from = new Date(this.searchForm.get('fromDate').value);
    var to = new Date(this.searchForm.get('toDate').value);
    const fromDate = this.datePipe.transform(from, 'yyyy-MM-dd\'T\'00:00:00.000');
    const toDate = this.datePipe.transform(to, 'yyyy-MM-dd\'T\'23:59:59.999');
    const dialogRef = this.dialog.open(DlkDigitizationReportDetailComponent, {
      width: '95vw',
      height: '95vh',
      data: {
        agencyId: agencyId,
        fromDate: fromDate,
        toDate: toDate,
        type: type,
        totalItem: totalItem,
        agencyName: agencyName,
        title: dialogTitle,
        strIdProcedureDigitizationReport: this.strIdProcedureDigitizationReport,
      },
      disableClose: true,
      autoFocus: false
    });
  }

  ngAfterViewInit(): void {
    this.rootAgencyId = this.env?.OS_DLK?.rootAgencyId;
    this.getAllAgency(this.rootAgencyId, '', 0, 10000, false);
    this.getAgencyComune();
    this.listIdProcedureDigitizationReport.forEach(element => {
      this.strIdProcedureDigitizationReport += element + ',';
    });
  }

  getAgencyComune() {
    this.dlkReportDigitizaTionService.getListAgencyWithLevel(this.rootAgencyId).subscribe(res => {
      this.listAgencyCapXa = res.content;
    }, err => {
      console.log(err);
    });
  }

  searchAgencyList(keyword) {
    clearTimeout(this.timeOutSearch);
    this.timeOutSearch = setTimeout(() => {
      this.getAllAgency(this.rootAgencyId, keyword, 0, 10000, true);
    }, this.msTimeOutSearch);
  }

  getAllAgency(prid, keyword, page, size, isSearching) {
    const searchString = '?parent-id=' + prid + '&keyword=' + keyword + '&page=' + page + '&size=' + size + '&sort=name.name,asc&status=1';
    this.dlkReportDigitizaTionService.getListAgencyWithParent(searchString).subscribe(res => {
      if (isSearching) {
        this.ListAgencySearchCurrent = res.content;
        this.ListAgencySearchCurrent.unshift({
          id: "",
          name: "Tất cả"
        });
      } else {
        this.listAgency = res.content;
        this.listAgency.unshift({
          id: "",
          name: "Tất cả"
        });
        this.initAgencySelect();
        this.search(false);
      }
    }, err => {
      console.log(err);
    });
  }

  initAgencySelect() {
    this.searchAgencyCtrl.valueChanges.subscribe(() => {
      this.searchAgencyList(this.searchAgencyCtrl.value);
    });
    this.changeAgency(null);
  }

  changeAgency($event) {
    this.searchForm.patchValue({
      agencyCtrl: ""
    });
    let value;
    if ($event?.value) {
      value = $event.value;
    }

    if ($event?.id) {
      value = $event.id;
    }
    if (value) {
      this.searchForm.patchValue({
        agencyCtrl: value
      });
    } else {
      this.ListAgencySearch = [];
      this.ListAgencySearch = this.listAgency;
      this.searchForm.patchValue({
        agencyCtrl: this.ListAgencySearch[0].id
      });
    }
    this.ListAgencySearchCurrent = this.ListAgencySearch;
  }

  exportToExcel() {
    const newDate = tUtils.newDate();
    let title = '';
    let nameReport = '';
    let subNameReport = '';

    const formObj = this.searchForm.getRawValue();
    let fromDateExcel = '';
    let from = [];
    let toDateExcel = '';
    let to = [];
    if (formObj.fromDate != null || formObj.fromDate !== '') {
      fromDateExcel = this.datePipe.transform(formObj.fromDate, 'dd/MM/yyyy');
      from = fromDateExcel.split('/');
      fromDateExcel = 'ngày ' + from[0] + ' tháng ' + from[1] + ' năm ' + from[2];
    }
    if (formObj.toDate != null || formObj.toDate !== '') {
      toDateExcel = this.datePipe.transform(formObj.toDate, 'dd/MM/yyyy');
      to = toDateExcel.split('/');
      toDateExcel = 'ngày ' + to[0] + ' tháng ' + to[1] + ' năm ' + to[2];
    }

    let name = '';
    if (localStorage.getItem('language') === 'vi') {
      title = 'KẾT QUẢ THỰC HIỆN SỐ HÓA HỒ SƠ THỦ TỤC HÀNH CHÍNH';
      name = 'thong ke so hoa ' + this.datePipe.transform(newDate, 'dd/MM/yyyy');
    } else if (localStorage.getItem('language') === 'en') {
      name = 'digitization report ' + this.datePipe.transform(newDate, 'dd/MM/yyyy');
      title = 'PROCEDURE DOSSIER DIGITIZATION REPORT';
    }
    let subTitle = '';
    if (fromDateExcel !== '' && toDateExcel !== '') {
      subTitle = '(Từ ' + fromDateExcel + ' đến ' + toDateExcel + ')';
    }

    const totalSoHoaHoSoTiepNhan = this.sum("soHoaHoSoTiepNhan", this.dataSource.data);
    const totalHsCoSoHoaTPHS = this.sum("hsCoSoHoaTPHS", this.dataSource.data);
    const totalTyLeSoHoaTPHS = totalSoHoaHoSoTiepNhan > 0 ? this.formatRationNoSufix(totalHsCoSoHoaTPHS / totalSoHoaHoSoTiepNhan) : 0;
    const totalHsChuaSoHoaTPHS = this.sum("hsChuaSoHoaTPHS", this.dataSource.data);
    const totalSoHsDaGiaiQuyet = this.sum("soHsDaGiaiQuyet", this.dataSource.data);
    const totalSoHsCoSoHoaKQ = this.sum("soHsCoSoHoaKQ", this.dataSource.data);
    const totalTyLeSoHoaKQ = totalSoHsDaGiaiQuyet > 0 ? this.formatRationNoSufix(totalSoHsCoSoHoaKQ / totalSoHsDaGiaiQuyet) : 0;
    const totalSoHsChuaSoHoaKQ = this.sum("soHsChuaSoHoaKQ", this.dataSource.data);
    let excelData = [];
    this.footerData = [[
      "TỔNG SỐ", "", totalSoHoaHoSoTiepNhan, totalHsCoSoHoaTPHS, totalTyLeSoHoaTPHS, totalHsChuaSoHoaTPHS,
      totalSoHsDaGiaiQuyet,
      totalSoHsCoSoHoaKQ, totalTyLeSoHoaKQ, totalSoHsChuaSoHoaKQ,
    ]];
    let selectedAgency = this.searchForm.get('agencyCtrl').value;

    if (!selectedAgency) {
      excelData.push({
        no: "I",
        agency: "CẤP TỈNH",
      });
      for (let i = 0; i < this.dsCapTinh.length; i++) {
        let item = this.dsCapTinh[i];
        let excelItem: Row = {
          no: i + 1,
          agency: item.agency,
          soHoaHoSoTiepNhan: item?.soHoaHoSoTiepNhan ?? 0,
          hsCoSoHoaTPHS: item?.hsCoSoHoaTPHS ?? 0,
          tyLeSoHoaTPHS: (item?.soHoaHoSoTiepNhan ?? 0) > 0 ? this.formatRationNoSufix((item?.hsCoSoHoaTPHS ?? 0) / (item?.soHoaHoSoTiepNhan ?? 0)) : 0,
          hsChuaSoHoaTPHS: item?.hsChuaSoHoaTPHS ?? 0,
          soHsDaGiaiQuyet: item?.soHsDaGiaiQuyet ?? 0,
          soHsCoSoHoaKQ: item?.soHsCoSoHoaKQ ?? 0,
          tyLeSoHoaKQ: (item?.soHsDaGiaiQuyet ?? 0) > 0 ? this.formatRationNoSufix((item?.soHsCoSoHoaKQ ?? 0) / (item?.soHsDaGiaiQuyet ?? 0)) : 0,
          soHsChuaSoHoaKQ: item?.soHsChuaSoHoaKQ ?? 0,
        }
        excelData.push(excelItem);
      }

      excelData.push({
        no: "",
        agency: "TỔNG CẤP TỈNH",
        soHoaHoSoTiepNhan: this.sum("soHoaHoSoTiepNhan", this.dsCapTinh),
        hsCoSoHoaTPHS: this.sum("hsCoSoHoaTPHS", this.dsCapTinh),
        tyLeSoHoaTPHS: this.calTyLeSoHoaTPHS(this.dsCapTinh),
        hsChuaSoHoaTPHS: this.sum("hsChuaSoHoaTPHS", this.dsCapTinh),
        soHsDaGiaiQuyet: this.sum("soHsDaGiaiQuyet", this.dsCapTinh),
        soHsCoSoHoaKQ: this.sum("soHsCoSoHoaKQ", this.dsCapTinh),
        tyLeSoHoaKQ: this.calTyLeSoHoaKQ(this.dsCapTinh),
        soHsChuaSoHoaKQ: this.sum("soHsChuaSoHoaKQ", this.dsCapTinh),
      });
      excelData.push({
        no: "II",
        agency: "CẤP HUYỆN",
      });

      for (let i = 0; i < this.dsCapHuyen.length; i++) {
        let item = this.dsCapHuyen[i];
        let excelItem: Row = {
          no: i + 1,
          agency: item.agency,
          soHoaHoSoTiepNhan: item?.soHoaHoSoTiepNhan ?? 0,
          hsCoSoHoaTPHS: item?.hsCoSoHoaTPHS ?? 0,
          tyLeSoHoaTPHS: (item?.soHoaHoSoTiepNhan ?? 0) > 0 ? this.formatRationNoSufix((item?.hsCoSoHoaTPHS ?? 0) / (item?.soHoaHoSoTiepNhan ?? 0)) : 0,
          hsChuaSoHoaTPHS: item?.hsChuaSoHoaTPHS ?? 0,
          soHsDaGiaiQuyet: item?.soHsDaGiaiQuyet ?? 0,
          soHsCoSoHoaKQ: item?.soHsCoSoHoaKQ ?? 0,
          tyLeSoHoaKQ: (item?.soHsDaGiaiQuyet ?? 0) > 0 ? this.formatRationNoSufix((item?.soHsCoSoHoaKQ ?? 0) / (item?.soHsDaGiaiQuyet ?? 0)) : 0,
          soHsChuaSoHoaKQ: item?.soHsChuaSoHoaKQ ?? 0,
        }
        excelData.push(excelItem);
      }
      excelData.push({
        no: "",
        agency: "TỔNG CẤP HUYỆN",
        soHoaHoSoTiepNhan: this.sum("soHoaHoSoTiepNhan", this.dsCapHuyen),
        hsCoSoHoaTPHS: this.sum("hsCoSoHoaTPHS", this.dsCapHuyen),
        tyLeSoHoaTPHS: this.calTyLeSoHoaTPHS(this.dsCapHuyen),
        hsChuaSoHoaTPHS: this.sum("hsChuaSoHoaTPHS", this.dsCapHuyen),
        soHsDaGiaiQuyet: this.sum("soHsDaGiaiQuyet", this.dsCapHuyen),
        soHsCoSoHoaKQ: this.sum("soHsCoSoHoaKQ", this.dsCapHuyen),
        tyLeSoHoaKQ: this.calTyLeSoHoaKQ(this.dsCapHuyen),
        soHsChuaSoHoaKQ: this.sum("soHsChuaSoHoaKQ", this.dsCapHuyen),
      });
      this.dlkReportDigitizaTionService.exportAsExcelDigitizationReportDlkV2(title, subTitle, nameReport, subNameReport, excelData, this.footerData, name, 'Sheet1', this.agencyNameExcel, this.dsCapTinh.length);
    } else {
      for (let i = 0; i < this.dataSource.data.length; i++) {
        let item = this.dataSource.data[i];
        let excelItem: Row = {
          no: i + 1,
          agency: item.agency,
          soHoaHoSoTiepNhan: item?.soHoaHoSoTiepNhan ?? 0,
          hsCoSoHoaTPHS: item?.hsCoSoHoaTPHS ?? 0,
          tyLeSoHoaTPHS: (item?.soHoaHoSoTiepNhan ?? 0) > 0 ? this.formatRationNoSufix((item?.hsCoSoHoaTPHS ?? 0) / (item?.soHoaHoSoTiepNhan ?? 0)) : 0,
          hsChuaSoHoaTPHS: item?.hsChuaSoHoaTPHS ?? 0,
          soHsDaGiaiQuyet: item?.soHsDaGiaiQuyet ?? 0,
          soHsCoSoHoaKQ: item?.soHsCoSoHoaKQ ?? 0,
          tyLeSoHoaKQ: (item?.soHsDaGiaiQuyet ?? 0) > 0 ? this.formatRationNoSufix((item?.soHsCoSoHoaKQ ?? 0) / (item?.soHsDaGiaiQuyet ?? 0)) : 0,
          soHsChuaSoHoaKQ: item?.soHsChuaSoHoaKQ ?? 0,
        }
        excelData.push(excelItem);
      }
      this.dlkReportDigitizaTionService.exportAsExcelDigitizationReportDlkV2(title, subTitle, nameReport, subNameReport, excelData, this.footerData, name, 'Sheet1', this.agencyNameExcel, 0);
    }
  }

  async search(exportFile) {
    let selectedAgency = this.searchForm.get('agencyCtrl').value;
    let from = new Date(this.searchForm.get('fromDate').value);
    let to = new Date(this.searchForm.get('toDate').value);
    const fromDate = this.datePipe.transform(from, 'yyyy-MM-dd\'T\'00:00:00.000');
    const toDate = this.datePipe.transform(to, 'yyyy-MM-dd\'T\'23:59:59.999');

    if (from > to) {
      const message = 'Ngày bắt đầu phải nhỏ hơn hoặc bằng ngày kết thúc';
      this.snackbarService.openSnackBar(0, '', message, 'error_notification', this.config.expiredTime);
      return;
    }

    this.isLoading = true;
    this.ELEMENTDATA = [];
    var listAgencyTemp: Array<any> = [];
    var agencyStr = "";
    for (var i = 0; i < this.ListAgencySearchCurrent.length; i++) {
      agencyStr += this.ListAgencySearchCurrent[i].id + ',';
    }
    let searchString = '?parent-id=' + this.rootAgencyId + '&keyword=' + "" + '&page=' + 0 + '&size=' + 10000 + '&sort=name.name,asc&status=1';
    if (selectedAgency) {
      searchString = '?parent-id=' + selectedAgency + '&keyword=' + "" + '&page=' + 0 + '&size=' + 10000 + '&sort=name.name,asc&status=1';
    }
    this.dlkReportDigitizaTionService.getListAgencyWithParent(searchString).subscribe(async res => {
      listAgencyTemp = res.content;

      let subSearchString = '?fromDate=' + fromDate + 'Z&id=' + this.rootAgencyId + '&toDate=' + toDate + 'Z' + "&agencyStr=" + agencyStr + "&listIdProcedureStr=" + this.strIdProcedureDigitizationReport;
      if (selectedAgency) {
        agencyStr = '';
        for (var i = 0; i < listAgencyTemp.length; i++) {
          agencyStr += listAgencyTemp[i].id + ',';
        }
        subSearchString = '?fromDate=' + fromDate + 'Z&id=' + selectedAgency + '&toDate=' + toDate + 'Z' + "&agencyStr=" + agencyStr + "&listIdProcedureStr=" + this.strIdProcedureDigitizationReport;
      }
      await this.dlkReportDigitizaTionService.getDigitiedReportData(subSearchString).toPromise().then(res => {
        if (res) {
          let data = res;
          var dataFinal = [];
          var listDossierDistrict = data.filter(it => listAgencyTemp.map(item => item.id).includes(it.agencyId));
          var listDossierComune = data.filter(it => listAgencyTemp.map(item => item.id).includes(it.parentId));
          var listDossierComuneChild = data.filter(it => !listDossierComune.includes(it) && !listDossierDistrict.includes(it));
          for (var i = 0; i < listDossierComuneChild.length; i++) {
            var comuneParent = this.listAgencyCapXa.find(it => it.id == listDossierComuneChild[i]?.parentId);
            if (comuneParent) {
              listDossierComuneChild[i].parentId = comuneParent?.parent;
              listDossierComune.push(listDossierComuneChild[i]);
            }
          }
          for (var i = 0; i < listAgencyTemp.length; i++) {
            var currentAgency = [];
            var finalDistrictItem = {
              agencyName: listAgencyTemp[i].name,
              agencyId: listAgencyTemp[i].id,
              parentId: listAgencyTemp[i].parent?.id,
              soHoaHoSoTiepNhan: 0,
              hsCoSoHoaTPHS: 0,
              hsChuaSoHoaTPHS: 0,
              soHsDaGiaiQuyet: 0,
              soHsCoSoHoaKQ: 0,
              soHsChuaSoHoaKQ: 0,
            }
            var itemDefault = {
              agencyName: listAgencyTemp[i].name,
              agencyId: listAgencyTemp[i].id,
              parentId: listAgencyTemp[i].parent?.id,
              soHoaHoSoTiepNhan: 0,
              hsCoSoHoaTPHS: 0,
              hsChuaSoHoaTPHS: 0,
              soHsDaGiaiQuyet: 0,
              soHsCoSoHoaKQ: 0,
              soHsChuaSoHoaKQ: 0,
            }
            let itemDossierDistrict = listDossierDistrict.find(item => item.agencyId == listAgencyTemp[i].id);
            if (itemDossierDistrict) {
              currentAgency.push(itemDossierDistrict);
            } else {
              currentAgency.push(itemDefault);
            }
            var dataComune = listDossierComune.filter(item => item.parentId == listAgencyTemp[i].id);
            currentAgency.push(...dataComune);
            finalDistrictItem.soHoaHoSoTiepNhan = this.sum("soHoaHoSoTiepNhan", currentAgency);
            finalDistrictItem.hsCoSoHoaTPHS = this.sum("hsCoSoHoaTPHS", currentAgency);
            finalDistrictItem.hsChuaSoHoaTPHS = this.sum("hsChuaSoHoaTPHS", currentAgency);
            finalDistrictItem.soHsDaGiaiQuyet = this.sum("soHsDaGiaiQuyet", currentAgency);
            finalDistrictItem.soHsCoSoHoaKQ = this.sum("soHsCoSoHoaKQ", currentAgency);
            finalDistrictItem.soHsChuaSoHoaKQ = this.sum("soHsChuaSoHoaKQ", currentAgency);
            dataFinal.push(finalDistrictItem);
          }
          for (let i = 0; i < listAgencyTemp.length; i++) {
            const item = dataFinal.find(it => it.agencyId == listAgencyTemp[i].id);
            let row: Row = {
              no: i,
              agency: listAgencyTemp[i].name,
              soHoaHoSoTiepNhan: item?.soHoaHoSoTiepNhan ?? 0,
              hsCoSoHoaTPHS: item?.hsCoSoHoaTPHS ?? 0,
              tyLeSoHoaTPHS: (item?.soHoaHoSoTiepNhan ?? 0) > 0 ? (item?.hsCoSoHoaTPHS ?? 0) / (item?.soHoaHoSoTiepNhan ?? 0) : 0,
              hsChuaSoHoaTPHS: item?.hsChuaSoHoaTPHS ?? 0,
              soHsDaGiaiQuyet: item?.soHsDaGiaiQuyet ?? 0,
              soHsCoSoHoaKQ: item?.soHsCoSoHoaKQ ?? 0,
              tyLeSoHoaKQ: (item?.soHsDaGiaiQuyet ?? 0) > 0 ? (item?.soHsCoSoHoaKQ ?? 0) / (item?.soHsDaGiaiQuyet ?? 0) : 0,
              soHsChuaSoHoaKQ: item?.soHsChuaSoHoaKQ ?? 0,
              agencyId: listAgencyTemp[i].id,
              agencyLevelId: listAgencyTemp[i].level?.id,
            }
            this.ELEMENTDATA.push(row);
          }
        }
      });
      if (selectedAgency) {
        this.dsCapTinh = [];
        this.dsCapHuyen = [];
        this.dsCapXa = [];
        this.dsLoc = this.ELEMENTDATA;
        this.dataSource.data = this.ELEMENTDATA;
      } else {
        this.dsLoc = [];
        this.dsCapTinh = this.ELEMENTDATA.filter(item => item.agencyLevelId == this.configDepartmentTagId || item.agencyLevelId == this.configProvinceTagId);
        this.dsCapHuyen = this.ELEMENTDATA.filter(item => item.agencyLevelId == this.configDistrictTagId);
        this.dataSource.data = this.dsCapTinh.concat(this.dsCapHuyen);
      }
      this.isLoading = false;
      if (exportFile) {
        this.exportToExcel();
      }
    }, err => {
      console.log(err);
    });
  }
  formatRationNoSufix(input) {
    if (Number.isNaN(input)) return '0';
    return (input * 100).toFixed(2).replace('.00', '').replace('.', ',');
  }

  formatRation(input) {
    if (Number.isNaN(input)) return '0%';
    return (input * 100).toFixed(2).replace('.00', '').replace('.', ',') + '%';
  }

  public sum(key: keyof any, data) {
    return data?.reduce((a, b) => a + (Number(b[key]) || 0), 0);
  }

  public sumWithFormat(key: keyof any, data) {
    return this.formatNumber(data?.reduce((a, b) => a + (Number(b[key]) || 0), 0));
  }

  formatNumber(number: number) {
    return (number == null || number == undefined) ? "" : number.toString().replace(/(.)(?=(\d{3})+$)/g, '$1.');
  }

  calTyLeSoHoaTPHS(data) {
    return this.formatRation(this.sum("hsCoSoHoaTPHS", data) / this.sum("soHoaHoSoTiepNhan", data));
  }

  calTyLeSoHoaKQ(data) {
    return this.formatRation(this.sum("soHsCoSoHoaKQ", data) / this.sum("soHsDaGiaiQuyet", data));
  }
}

export interface Row {
  no: number,
  agency: string,
  soHoaHoSoTiepNhan: number,
  hsCoSoHoaTPHS: number,
  tyLeSoHoaTPHS: any,
  hsChuaSoHoaTPHS: any,
  soHsDaGiaiQuyet: number,
  soHsCoSoHoaKQ: number,
  tyLeSoHoaKQ: any,
  soHsChuaSoHoaKQ: number,
  agencyId?: number,
  agencyLevelId?: string,
}