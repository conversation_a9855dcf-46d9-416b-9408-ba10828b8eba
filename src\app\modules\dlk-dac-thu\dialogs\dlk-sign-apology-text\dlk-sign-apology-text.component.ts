import { Component, Inject, OnInit } from '@angular/core';
import { FormControl, FormGroup } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialog, MatDialogRef } from '@angular/material/dialog';
import { KeycloakService } from 'keycloak-angular';
import { EnvService } from 'src/app/core/service/env.service';
import { DeploymentService } from 'src/app/data/service/deployment.service';
import { DossierService } from 'src/app/data/service/dossier/dossier.service';
import { ProcedureService } from 'src/app/data/service/procedure/procedure.service';
import { SnackbarService } from 'src/app/data/service/snackbar/snackbar.service';
import { ConfirmVerifiedSimDialogModel, VerifiedSimComponent } from 'src/app/shared/components/verified-sim/verified-sim.component';

@Component({
  selector: 'app-dlk-sign-apology-text',
  templateUrl: './dlk-sign-apology-text.component.html',
  styleUrls: ['./dlk-sign-apology-text.component.scss']
})
export class DlkSignApologyTextComponent implements OnInit {
  listLoaiVBXL: any[] = [
    { value: "0", text: "Lựa chọn lý do", isDisabled: true },
    { value: "1", text: "Do giải quyết quá hạn", isDisabled: false },
    { value: "2", text: "Do tiếp nhận thành phần hồ sơ không đủ", isDisabled: false },
    { value: "3", text: "Do hồ sơ bị mất, thất lạc hoặc hư hỏng", isDisabled: false },
    { value: "4", text: "Do sai sót trong kết quả giải quyết", isDisabled: false },
  ]

  config = this.envService.getConfig();
  env = this.deploymentService.getAppDeployment()?.env;
  selectedLang = localStorage.getItem('language');
  dossierId = '';
  dossierDetail = [];
  acceptFileExtension = this.config.acceptFileExtension;
  acceptFileType = this.config.acceptFileType;
  maxFileSize = this.env?.maxFileSize || this.config.maxFileSize;
  attachFiles = [];
  attachFilePreview = [];
  resultFiles = [];
  resultFilePreview = [];
  apologyTextFiles = [];
  apologyTextFilePreview = [];
  apologyTextFilePost = [];
  accountId: string;
  userName: string;
  blankVal: any;

  countFile = 0;

  apologyName = [];
  isExists = false;

  isFieldArrayInvalid = false;
  languageIdUsed = [228];
  listLanguage = [
    {
      languageId: 228,
      name: 'Tiếng Việt'
    },
    {
      languageId: 46,
      name: 'English'
    }
  ];
  newAttribute: any = {
    languageId: 228,
    name: '',
    listLanguageItem: JSON.parse(JSON.stringify(this.listLanguage))
  };
  fieldArray: Array<any> = [this.newAttribute];

  addForm = new FormGroup({
    description: new FormControl(''),
    vbxl_type: new FormControl('1'),
  });
  changeTag = true;

  constructor(
    private envService: EnvService,
    private deploymentService: DeploymentService,
    private snackbarService: SnackbarService,
    private procedureService: ProcedureService,
    private keycloakService: KeycloakService,
    private dossierService: DossierService,
    @Inject(MAT_DIALOG_DATA) public data: ConfirmSignApologyTextComponent,
    public dialogRef: MatDialogRef<DlkSignApologyTextComponent>,
    private dialog: MatDialog,
  ) {
    this.dossierId = data.id;
  }

  ngOnInit(): void {
    this.getUserAccount();
    this.getDossierDetail();
    this.getDossierApologyByDossierId();
  }

  onDismiss(): void {
    if (this.countFile === 0) {
      this.dialogRef.close();
    } else {
      const msgObj = {
        vi: 'Đã có thay đổi, vui lòng nhấn lưu lại trước khi thoát!',
        en: 'Have changed, please click save before exit!'
      };
      this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
    }
  }

  async onConfirm() {
    const formObj = this.addForm.getRawValue();
    this.checkFormTypeInvalid1();

    if (this.isFieldArrayInvalid === true) {
      const msgObj = {
        vi: 'Vui lòng điền đầy đủ thông tin!',
        en: 'Please fill full information!'
      };
      this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
    } else {
      const userAgency = JSON.parse(localStorage.getItem('userAgency'));
      let rootAgencyId: any = '';
      if (userAgency !== null && !!userAgency.parent && !!userAgency.parent.id) {
        rootAgencyId = userAgency.parent.id;
      } else {
        rootAgencyId = this.config.rootAgency.id;
      }
      const content = {
        agency: {
          id: this.dossierDetail[0].agency.id
        },
        parent: {
          id: rootAgencyId
        },
        vbxlType: formObj.vbxl_type ?? "1",
        description: formObj.description,
        dossier: {
          id: this.dossierId,
          code: this.dossierDetail[0].code
        },
        file: this.apologyTextFilePost,
        name: []
      };

      let nameReturn = '';

      if (this.fieldArray.length !== 0) {
        const fieldArray = this.fieldArray;
        for (let i = 0, count = this.fieldArray.length; i < count; i++) {
          const tempObj = {
            languageId: fieldArray[i].languageId,
            name: fieldArray[i].name
          };
          this.apologyName.push(tempObj);
          if (fieldArray[i].languageId === Number(localStorage.getItem('languageId'))) {
            nameReturn = fieldArray[i].name;
          }
        }
        content.name = this.apologyName;
      }

      const requestBody = JSON.stringify(content, null, 2);
      if (this.isExists === true) {
        this.dossierService.putDossierApology(this.dossierId, requestBody).subscribe(data => {
          if (data.affectedRows === 1) {
            this.dialogRef.close(true);
          } else {
            this.dialogRef.close(false);
          }
        }, err => {
          this.dialogRef.close(false);
        });
      } else {
        this.dossierService.postDossierApology(requestBody).subscribe(data => {
          this.dialogRef.close(true);
        }, err => {
          this.dialogRef.close(false);
        });
      }
    }
  }

  getUserAccount() {
    this.keycloakService.loadUserProfile().then(user => {
      // tslint:disable-next-line: no-string-literal
      this.userName = user.username;
      this.accountId = user['attributes'].user_id[0];
    });
  }

  getDossierApologyByDossierId() {
    this.dossierService.getDossierApologyByDossierId(this.dossierId).subscribe(data => {
      if (data != null) {

        this.isExists = true;
        this.addForm.patchValue({
          description: data.description,
          vbxl_type: data.vbxlType + ''
        });
        this.fieldArray = [];
        // tslint:disable-next-line: no-shadowed-variable
        data.name.forEach(element => {
          this.languageIdUsed.push(element.languageId);
        });
        // tslint:disable-next-line: prefer-for-of
        for (let i = 0; i < data.name.length; i++) {
          const languageItem = [];
          // tslint:disable-next-line: no-shadowed-variable
          this.listLanguage.forEach(element => {
            if (element.languageId === data.name[i].languageId || this.languageIdUsed.indexOf(element.languageId) === -1) {
              languageItem.push(JSON.parse(JSON.stringify(element)));
            }
          });
          const item = {
            languageId: data.name[i].languageId,
            name: data.name[i].name,
            listLanguageItem: JSON.parse(JSON.stringify(languageItem))
          };
          this.fieldArray.push(JSON.parse(JSON.stringify(item)));
        }
      }

      // tslint:disable-next-line:triple-equals
      if (this.fieldArray.length == 0) {
        this.fieldArray.push(this.newAttribute);
      }
    }, err => {
      this.isExists = false;
    });
  }

  addItem() {
    const temp = [];
    this.listLanguage.forEach(val => temp.push(Object.assign({}, val)));
    if (this.fieldArray.length < this.listLanguage.length) {
      const listLanguageItemOne = [];
      // tslint:disable-next-line: prefer-for-of
      for (let i = 0; i < temp.length; i++) {
        const abc = JSON.parse(JSON.stringify(temp[i]));
        if (this.languageIdUsed.indexOf(abc.languageId) === -1) {
          listLanguageItemOne.push(abc);
        }
      }
      const newItem = {
        languageId: listLanguageItemOne[0].languageId,
        name: this.fieldArray[0]?.name,
        listLanguageItem: JSON.parse(JSON.stringify(listLanguageItemOne))
      };
      // tslint:disable-next-line: prefer-for-of
      for (let i = 0; i < this.fieldArray.length; i++) {
        let a = 0;
        // tslint:disable-next-line: prefer-for-of
        for (let j = 0; j < this.fieldArray[i].listLanguageItem.length; j++) {
          if (this.fieldArray[i].listLanguageItem[j].languageId === listLanguageItemOne[0].languageId) {
            a = j;
            break;
          }
        }
        this.fieldArray[i].listLanguageItem.splice(a, 1);
      }
      this.languageIdUsed.push(newItem.languageId);
      this.fieldArray.push(newItem);
    }
  }

  deleteItem(index) {
    if (this.fieldArray.length > 1) {
      let language = {};
      let id = 0;
      // tslint:disable-next-line: no-shadowed-variable
      this.fieldArray[index].listLanguageItem.forEach(element => {
        if (this.fieldArray[index].languageId === element.languageId) {
          language = JSON.parse(JSON.stringify(element));
          id = element.languageId;
        }
      });
      this.fieldArray.splice(index, 1);

      // tslint:disable-next-line: no-shadowed-variable
      this.fieldArray.forEach(element => {
        element.listLanguageItem.push(Object.assign({}, language));
      });
      this.languageIdUsed.splice(this.languageIdUsed.indexOf(id), 1);
    }
  }

  changeName(i, $event) {
    const value = $event.target.value;
    if (value.trim().length === 0) {
      this.isFieldArrayInvalid = true;
    }
    this.fieldArray[i].name = value.trim();
    this.checkFormTypeInvalid(i);
    setTimeout(() => {
      this.checkFormTypeInvalid1();
    }, 200);
  }

  checkFormTypeInvalid(i) {
    if (this.fieldArray[i].name.length === 0) {
      this.isFieldArrayInvalid = true;
    } else {
      this.isFieldArrayInvalid = false;
    }
  }

  checkFormTypeInvalid1() {
    let count = 0;
    this.fieldArray.forEach(frmType => {
      if (frmType.name.length === 0) {
        count++;
      }
    });
    if (count === 0) {
      this.isFieldArrayInvalid = false;
    } else {
      this.isFieldArrayInvalid = true;
    }
  }

  changeLanguage(i, event) {
    const value = Number(event.value);
    let language = {};
    let languageUsed = {};
    const id = this.fieldArray[i].languageId;
    // tslint:disable-next-line: no-shadowed-variable
    this.listLanguage.forEach(element => {
      if (value === element.languageId) {
        language = JSON.parse(JSON.stringify(element));
      }
      if (this.fieldArray[i].languageId === element.languageId) {
        languageUsed = JSON.parse(JSON.stringify(element));
      }
    });
    this.fieldArray[i].languageId = value;
    // tslint:disable-next-line: prefer-for-of
    for (let j = 0; j < this.fieldArray.length; j++) {
      if (i !== j) {
        // tslint:disable-next-line: prefer-for-of
        for (let k = 0; k < this.fieldArray[j].listLanguageItem.length; k++) {
          if (this.fieldArray[j].listLanguageItem[k].languageId === value) {
            const a = k;
            this.fieldArray[j].listLanguageItem.splice(a, 1);
            break;
          }
        }
        this.fieldArray[j].listLanguageItem.push(languageUsed);
      }
    }
    this.languageIdUsed.splice(this.languageIdUsed.indexOf(id), 1);
    this.languageIdUsed.push(value);
  }

  onSelectFileAttach(event, doneClass) {
    if (event.target.files.length > 0) {
      if (event.target.files[0].size >= this.maxFileSize * 1024 * 1024) {
        const msgObj = {
          vi: 'Kích thước tệp tin quá lớn, tên tệp: ' + event.target.files[0].name,
          en: 'The file is too large, file name: ' + event.target.files[0].name
        };
        this.snackbarService.openSnackBar(0, '', msgObj[this.selectedLang], 'error_notification', this.config.expiredTime);
        return;
      }
      if ((this.acceptFileType.filter(type => type.toLowerCase() === event.target.files[0].type.toLowerCase())).length < 1) {
        if (this.selectedLang === 'vi') {
          this.snackbarService.openSnackBar(0, 'Lỗi', 'Không hỗ trợ dạng tệp tin này, vui lòng chọn loại tệp tin khác!', 'error_notification', this.config.expiredTime);
        }
        if (this.selectedLang === 'en') {
          this.snackbarService.openSnackBar(0, 'Error', 'This file type is not supported, please choose another file type!', 'error_notification', this.config.expiredTime);
        }
        event.target.value = null;
      } else {
        this.showSpinner(doneClass);
        this.apologyTextFiles.push(event.target.files[0]);
        const temp = [];
        temp.push(event.target.files[0]);
        const fileIcon = this.getFileIcon(event.target.files[0].name.split('.').pop());
        this.procedureService.uploadMultiFile(temp, this.accountId).subscribe(data => {
          this.apologyTextFilePreview.push({
            id: data[0].id,
            name: data[0].filename,
            size: data[0].size,
            icon: fileIcon
          });
          this.apologyTextFilePost.push({
            id: data[0].id,
            filename: data[0].filename,
            size: data[0].size
          });
          event.target.value = null;
          this.countFile += 1;
          this.putDossierAttachment(doneClass, 3, 'lang.word.file', '', data[0].filename);
        });
      }
    }
  }

  bytesToSize(size) {
    if (isNaN(parseFloat(size)) || !isFinite(size)) { return '?'; }
    let unit = 0;
    while (size >= 1024) {
      size /= 1024;
      unit++;
    }
    return size.toFixed(+ 0) + ' ' + this.config.fileUnits[unit];
  }

  removeAttachItem(index: number, id, name, doneClass) {
    this.apologyTextFiles.splice(index, 1);
    this.apologyTextFilePreview.splice(index, 1);
    this.apologyTextFilePost.splice(index, 1);
    this.blankVal = '';
    this.deleteFile(id, name, doneClass);
  }

  deleteFile(id, name, doneClass) {
    this.showSpinner(doneClass);
    this.procedureService.deleteFile(id).subscribe(data => {
      this.countFile += 1;
      this.putDossierAttachment(doneClass, 5, 'lang.word.file', name, '');
    }, err => {
      const msgObj = {
        vi: 'Không thể xoá file!',
        en: 'Cannot delete file!'
      };
      this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
    });
  }

  downloadFile(id, filename) {
    this.procedureService.downloadFile(id, this.dossierId).subscribe(data => {
      const dataType = data.type;
      const binaryData = [];
      binaryData.push(data);
      const downloadLink = document.createElement('a');
      downloadLink.href = window.URL.createObjectURL(new Blob(binaryData, { type: dataType }));
      downloadLink.setAttribute('download', filename);
      document.body.appendChild(downloadLink);
      downloadLink.click();
    }, err => {
      const msgObj = {
        vi: 'Không tìm thấy file!',
        en: 'File not found!'
      };
      this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
      console.log(err);
    });
  }

  verifiedSimHSM(detail) {
    const dialogData = new ConfirmVerifiedSimDialogModel(detail.id, this.accountId, this.dossierId, this.config.dossierApologyTextFileTagId, this.userName, detail);
    const dialogRef = this.dialog.open(VerifiedSimComponent, {
      minWidth: '55vw',
      minHeight: '90vh',
      data: dialogData,
      autoFocus: false
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
      if (dialogResult === true) {
        this.countFile += 1;
        this.getDossierAttachment(true);
      } else if (dialogResult === false) {
        this.getDossierAttachment(false);
      }
    });
  }

  getDossierAttachment(dialogResult) {
    this.dossierService.getDossierDetail(this.dossierId).subscribe(async data => {
      let count = 0;
      if (data.attachment) {
        this.apologyTextFilePreview = [];
        this.apologyTextFilePost = [];
        const asyncFunction = new Promise<void>((resolve, reject) => {
          data.attachment.forEach(async att => {
            if (att.group === this.config.dossierApologyTextFileTagId) {
              this.apologyTextFilePreview.push({
                id: att.id,
                name: att.filename,
                size: att.size,
                icon: this.getFileIcon(att.filename.split('.').pop())
              });
              this.apologyTextFilePost.push({
                id: att.id,
                filename: att.filename,
                size: att.size
              });
            }

            count++;
            if (count === data.attachment.length) { resolve(); }
          });
        });
        asyncFunction.then(() => {
          if (dialogResult === true) {
            const msgObj = {
              vi: 'Ký số thành công!',
              en: 'Successfully signed!'
            };
            this.snackbarService.openSnackBar(1, msgObj[this.selectedLang], '', 'success_notification', this.config.expiredTime);
          } else if (dialogResult === false) {
            const msgObj = {
              vi: 'Ký số thất bại!',
              en: 'Unsuccessful signed!'
            };
            this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
          }
        });
      } else {
        this.apologyTextFilePreview = [];
        this.apologyTextFilePost = [];
      }
    });
  }

  getFileIcon(ext) {
    return this.config.cloudStaticURL + 'icon/files/512x512/' + ext + '.png';
  }

  showSpinner(doneClass) {
    this.hideSpinner(doneClass);
    const checkIcon = document.querySelector('.' + doneClass + ' .mat-spinner');
    (checkIcon as HTMLElement).style.display = 'block';
  }

  showResult(doneClass) {
    const txtDone = document.querySelector('.' + doneClass + ' .done');
    const checkIcon = document.querySelector('.' + doneClass + ' .mat-spinner');
    (checkIcon as HTMLElement).style.display = 'none';
    (txtDone as HTMLElement).style.display = 'block';
  }

  hideSpinner(doneClass) {
    const txtDone = document.querySelector('.' + doneClass + ' .done');
    const checkIcon = document.querySelector('.' + doneClass + ' .mat-spinner');
    (checkIcon as HTMLElement).style.display = 'none';
    (txtDone as HTMLElement).style.display = 'none';
  }

  putDossierAttachment(doneClass, type, rbk, oldName, newName) {
    const rs = [];
    this.attachFilePreview.forEach(file => {
      rs.push({
        id: file.id,
        filename: file.name,
        size: file.size,
        group: this.config.dossierAttachedFileTagId
      });
    });
    this.resultFilePreview.forEach(file => {
      rs.push({
        id: file.id,
        filename: file.name,
        size: file.size,
        group: this.config.dossierResultFileTagId
      });
    });
    this.apologyTextFilePreview.forEach(file => {
      rs.push({
        id: file.id,
        filename: file.name,
        size: file.size,
        group: this.config.dossierApologyTextFileTagId
      });
    });
    this.apologyTextFilePost.forEach(file => {
      rs.push({
        id: file.id,
        filename: file.name,
        size: file.size
      });
    });
    const putBody = {
      attachment: rs
    };
    const requestBody = JSON.stringify(putBody, null, 2);
    this.dossierService.putDossierOnline(this.dossierId, requestBody).subscribe(data => {
      if (data.affectedRows === 1) {
        setTimeout(() => {
          this.showResult(doneClass);
          setTimeout(() => {
            this.hideSpinner(doneClass);
          }, 2000);
        }, 1000);
        // this.postHistory(type, rbk, oldName, newName);
      } else {
        const msgObj = {
          vi: 'Cập nhật thất bại!',
          en: 'Update failed!'
        };
        this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
        setTimeout(() => {
          this.hideSpinner(doneClass);
        }, 1000);
      }
    });
  }

  getDossierDetail() {
    this.dossierService.getDossierDetail(this.dossierId).subscribe(data => {
      this.dossierDetail = [];
      this.dossierDetail.push(data);
      if (data.attachment !== undefined) {
        data.attachment.forEach(att => {
          if (att.group === this.config.dossierAttachedFileTagId) {
            this.attachFilePreview.push({
              id: att.id,
              name: att.filename,
              size: att.size,
              icon: this.getFileIcon(att.filename.split('.').pop())
            });
          }
          if (att.group === this.config.dossierResultFileTagId) {
            this.resultFilePreview.push({
              id: att.id,
              name: att.filename,
              size: att.size,
              icon: this.getFileIcon(att.filename.split('.').pop())
            });
          }
          if (att.group === this.config.dossierApologyTextFileTagId) {
            this.apologyTextFilePreview.push({
              id: att.id,
              name: att.filename,
              size: att.size,
              icon: this.getFileIcon(att.filename.split('.').pop())
            });
            this.apologyTextFilePost.push({
              id: att.id,
              filename: att.filename,
              size: att.size
            });
          }
        });
      }
    });
  }

}

export class ConfirmSignApologyTextComponent {
  constructor(public id: string) {
  }
}
