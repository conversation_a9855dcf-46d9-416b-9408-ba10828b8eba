import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { EnvService } from 'core/service/env.service';
import { ApiProviderService } from 'src/app/core/service/api-provider.service';
import { Workbook } from 'exceljs';
import * as fs from 'file-saver';

const EXCEL_TYPE = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8';
const EXCEL_EXTENSION = '.xlsx';


@Injectable({
  providedIn: 'root'
})
export class DLKReportDigitizaTionService {

  language = localStorage.getItem('language') === 'vi' ? 228 : 46;
  config = this.envService.getConfig();

  constructor(
    private http: HttpClient,
    private envService: EnvService,
    private apiProviderService: ApiProviderService,
  ) { }

  private padmanURL = this.apiProviderService.getUrl('digo', 'padman');
  private basedataURL = this.apiProviderService.getUrl('digo', 'basedata');
  private basepadURL = this.apiProviderService.getUrl('digo', 'basepad');
  private basecatURL = this.apiProviderService.getUrl('digo', 'basecat');
  private agencyPath = this.apiProviderService.getUrl('digo', 'basedata') + '/agency/';

  public excelExportPaymentURL = this.apiProviderService.getUrl('digo', 'padman') + '/dossier-payment/--qnm-export-excel-list-dossier-payment'

  getListAgencyWithParent(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.agencyPath + searchString, { headers });
  }

  getListAgencyWithLevel(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.agencyPath + 'tree-view?keyword=&agencyName=&ancestor-id=' + id + '&code=&status=&levelId=&phone=&parent-id=&tag-id=&sort=', { headers });
  }

  getDigitiedReportData(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    return this.http.get(this.padmanURL + '/dlk-dossier/report/--digitization' + searchString, { headers }).pipe();
  }

  getListDossierPaginationData(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    return this.http.get(this.padmanURL + '/dlk-dossier/report/digitization/--pagination' + searchString, { headers }).pipe();
  }

  public exportAsExcelDigitizationReportDlkV2(
    reportHeading: string,
    reportSubHeading: string,
    nameReport: string,
    subNameReport: string,
    json: any[],
    footerData: any[],
    excelFileName: string,
    sheetName: string,
    agencyName: string,
    lenghtCT: any,
  ) {
    const data = json;
    // create workbook and worksheet
    const workbook = new Workbook();
    workbook.creator = 'Snippet Coder';
    workbook.lastModifiedBy = 'SnippetCoder';
    workbook.created = new Date();
    workbook.modified = new Date();
    const worksheet = workbook.addWorksheet(sheetName);

    worksheet.getColumn('A').font = { name: 'Times New Roman', size: 12 };
    worksheet.getColumn('B').font = { name: 'Times New Roman', size: 12 };
    worksheet.getColumn('C').font = { name: 'Times New Roman', size: 12 };
    worksheet.getColumn('D').font = { name: 'Times New Roman', size: 12 };
    worksheet.getColumn('E').font = { name: 'Times New Roman', size: 12 };
    worksheet.getColumn('F').font = { name: 'Times New Roman', size: 12 };
    worksheet.getColumn('G').font = { name: 'Times New Roman', size: 12 };
    worksheet.getColumn('H').font = { name: 'Times New Roman', size: 12 };
    worksheet.getColumn('I').font = { name: 'Times New Roman', size: 12 };
    worksheet.getColumn('J').font = { name: 'Times New Roman', size: 12 };

    worksheet.getColumn('A').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getColumn('B').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getColumn('C').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getColumn('D').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getColumn('E').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getColumn('F').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getColumn('G').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getColumn('H').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getColumn('I').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getColumn('J').alignment = { horizontal: 'center', vertical: 'middle' };


    // worksheet.getColumn('A').width = 30;
    worksheet.getColumn('B').width = 40;
    worksheet.getColumn('C').width = 20;
    worksheet.getColumn('D').width = 20;
    worksheet.getColumn('E').width = 20;
    worksheet.getColumn('F').width = 20;
    worksheet.getColumn('G').width = 20;
    worksheet.getColumn('H').width = 20;
    worksheet.getColumn('I').width = 20;
    worksheet.getColumn('J').width = 20;


    // Add header row
    worksheet.addRow([]);
    worksheet.mergeCells('A1:J1');
    worksheet.getCell('A1').value = reportHeading;
    worksheet.getCell('A1').alignment = { horizontal: 'center', vertical: 'middle' };

    worksheet.addRow([]);
    worksheet.mergeCells('A2:J2');
    worksheet.getCell('A2').value = reportSubHeading;
    worksheet.getCell('A2').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getCell('A2').font = { size: 12, italic: true, name: 'Times New Roman' };

    worksheet.mergeCells('A4:A6');
    worksheet.getCell('A4').value = 'STT';
    worksheet.mergeCells('B4:B6');
    worksheet.getCell('B4').value = 'Đơn vị (Cơ quan)';
    worksheet.mergeCells('C4:G4');
    worksheet.getCell('C4').value = 'Số hóa hồ sơ TTHC khi Tiếp nhận';
    worksheet.mergeCells('H4:J4');
    worksheet.getCell('H4').value = 'Số hóa kết quả giải quyết TTHC';

    worksheet.getCell('C5').value = 'Số hồ sơ Tiếp nhận';
    worksheet.getCell('D5').value = 'Số hồ sơ có số hóa thành phần HS';
    worksheet.getCell('E5').value = 'Tỷ lệ số hồ sơ có số hóa đầy đủ thành phần HS khi tiếp nhận';
    worksheet.getCell('F5').value = 'Số hồ sơ chưa số hóa TPHS';
    worksheet.getCell('G5').value = 'Số hồ sơ đã giải quyết';
    worksheet.getCell('H5').value = 'Số hồ sơ có số hóa kết quả';
    worksheet.getCell('I5').value = 'Tỷ lệ số hóa kết quả hồ sơ';
    worksheet.getCell('J5').value = 'Số hồ sơ chưa số hóa kết quả';

    worksheet.getCell('C6').value = '(1)';
    worksheet.getCell('D6').value = '(2)';
    worksheet.getCell('E6').value = '(3)=(2)/(1)';
    worksheet.getCell('F6').value = '(4)';
    worksheet.getCell('G6').value = '(5)';
    worksheet.getCell('H6').value = '(6)';
    worksheet.getCell('I6').value = '(7)=(6)/(5)';
    worksheet.getCell('J6').value = '(8)';

    worksheet.properties.outlineLevelCol = 2; worksheet.properties.defaultRowHeight = 15;
    let i = 4;
    const j = 6;
    for (i; i <= j; i++) {
      let k = 1;
      const l = 10;
      for (k; k <= l; k++) {
        worksheet.findCell(i, k).alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
        worksheet.findCell(i, k).fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: 'C0C0C0C0' },
          bgColor: { argb: 'FF0000FF' }
        };
        worksheet.findCell(i, k).border = { top: { style: 'thin' }, left: { style: 'thin' }, bottom: { style: 'thin' }, right: { style: 'thin' } };
        worksheet.findCell(i, k).font = { size: 12, bold: true, name: 'Times New Roman' };
      }
    }

    // get all columns from JSON
    let columnsArray: any[];
    for (const key in json) {
      if (json.hasOwnProperty(key)) {
        columnsArray = Object.keys(json[key]);
      }
    }
    // Add Data and Conditional Formatting
    data.forEach((element: any) => {
      const eachRow = [];
      columnsArray.forEach((column) => {
        eachRow.push(element[column]);
      });
      const borderrow = worksheet.addRow(eachRow);
      borderrow.eachCell((cell) => {
        cell.border = { top: { style: 'thin' }, left: { style: 'thin' }, bottom: { style: 'thin' }, right: { style: 'thin' } };
        cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
      });
    });
    if (lenghtCT > 0) {
      worksheet.mergeCells('B7:J7');
      var num = lenghtCT + 9;
      worksheet.mergeCells('B' + num + ':J' + num);
      worksheet.getCell('B7:J7').alignment = { horizontal: 'left', vertical: 'middle', wrapText: true };
      worksheet.getCell('B' + num + ':J' + num).alignment = { horizontal: 'left', vertical: 'middle', wrapText: true };
      worksheet.getCell('B7:J7').font = { size: 13, bold: true, name: 'Times New Roman' };
      worksheet.getCell('B' + num + ':J' + num).font = { size: 13, bold: true, name: 'Times New Roman' };
    }
    // footer data row
    if (footerData != null) {
      footerData.forEach((element: any) => {
        const eachRow = [];
        element.forEach((val: any) => {
          eachRow.push(val);
        });
        const footerRow = worksheet.addRow(eachRow);
        const cellMerge = 'A' + footerRow.number + ':B' + footerRow.number;
        worksheet.mergeCells(cellMerge);
        footerRow.eachCell((cell) => {
          cell.font = { size: 13, bold: true, name: 'Times New Roman' };
          cell.border = { top: { style: 'thin' }, left: { style: 'thin' }, bottom: { style: 'thin' }, right: { style: 'thin' } };
          cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
        });
      });
    }

    // Save Excel File
    // tslint:disable-next-line:no-shadowed-variable
    workbook.xlsx.writeBuffer().then((data: ArrayBuffer) => {
      const blob = new Blob([data], { type: EXCEL_TYPE });
      fs.saveAs(blob, excelFileName + EXCEL_EXTENSION);
    });
  }

  public exportAsExcelFileStatisticDigitized(
    reportHeading: string,
    reportSubHeading: string,
    headersArray: any[],
    subheaderArray: any[],
    json: any[],
    footerData: any,
    excelFileName: string,
    sheetName: string,
    agencyName: string,
    subAgencyName: string,
    bannerName: string,
    subBannerName: string,
    agencyRootName: string,
    sign?: string
  ) {
    const header = headersArray;
    const subheader = subheaderArray;
    const data = json;
    // create workbook and worksheet
    const workbook = new Workbook();
    workbook.creator = 'Snippet Coder';
    workbook.lastModifiedBy = 'SnippetCoder';
    workbook.created = new Date();
    workbook.modified = new Date();
    const worksheet = workbook.addWorksheet(sheetName);

    // Add header row
    worksheet.addRow([]);
    worksheet.mergeCells('A1:F1');
    worksheet.getCell('A1').value = subAgencyName;
    worksheet.getCell('A1').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getCell('A1').font = { size: 13, bold: true };

    worksheet.mergeCells('A2:F2');
    worksheet.getCell('A2').value = '';
    worksheet.getCell('A2').alignment = { horizontal: 'center', vertical: 'middle' };


    worksheet.mergeCells('G1:I1');
    worksheet.getCell('G1').value = bannerName;
    worksheet.getCell('G1').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getCell('G1').font = { bold: true };

    worksheet.mergeCells('G2:I2');
    worksheet.getCell('G2').value = subBannerName;
    worksheet.getCell('G2').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getCell('G2').font = { bold: true };

    worksheet.addRow([]);

    worksheet.addRow([]);
    worksheet.mergeCells('A4:' + this.numToAlpha(header.length - 1) + '4');
    worksheet.getCell('A4').value = reportHeading;
    worksheet.getCell('A4').alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
    worksheet.getCell('A4').font = { size: 15, bold: true };

    if (reportSubHeading !== '') {
      worksheet.addRow([]);
      worksheet.mergeCells('A5:' + this.numToAlpha(header.length - 1) + '5');
      worksheet.getCell('A5').value = reportSubHeading;
      worksheet.getCell('A5').alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
    }
    const headerRow = worksheet.addRow(header);
    // const subheaderRow = worksheet.addRow(subheader);

    worksheet.getColumn('D').alignment = { horizontal: 'left', vertical: 'middle', wrapText: true };
    worksheet.getColumn('E').alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
    worksheet.getColumn('F').alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
    worksheet.getColumn('G').alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
    worksheet.getColumn('H').alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
    worksheet.getColumn('I').alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
    worksheet.getColumn('J').alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
    worksheet.getColumn('K').alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
    worksheet.getColumn('M').alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
    worksheet.getColumn('N').alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };

    worksheet.properties.outlineLevelCol = 2; worksheet.properties.defaultRowHeight = 15;

    // Cell style: fill and border
    headerRow.eachCell((cell, index) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'C0C0C0C0' },
        bgColor: { argb: 'FF0000FF' }
      };
      cell.border = { top: { style: 'thin' }, left: { style: 'thin' }, bottom: { style: 'thin' }, right: { style: 'thin' } };
      cell.font = { size: 12, bold: true };
      cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };

      worksheet.getColumn(index).width = header[index - 1].length < 20 ? 20 : header[index - 1].length;
    });

    // get all columns from JSON
    let columnsArray: any[];
    for (const key in json) {
      if (json.hasOwnProperty(key)) {
        columnsArray = Object.keys(json[key]);
      }
    }
    // //debugger
    // Add Data and Conditional Formatting
    data.forEach((element: any) => {
      const eachRow = [];
      columnsArray.forEach((column) => {
        eachRow.push(element[column]);
      });
      if (element.isDeleted === 'Y') {
        const deletedRow = worksheet.addRow(eachRow);
        deletedRow.eachCell((cell) => {
          cell.font = { name: 'Calibri', family: 4, size: 11, bold: false, strike: true };
        });
      } else {
        const borderrow = worksheet.addRow(eachRow);
        borderrow.eachCell((cell) => {
          cell.border = { top: { style: 'thin' }, left: { style: 'thin' }, bottom: { style: 'thin' }, right: { style: 'thin' } };
          if (cell.address.indexOf('C') !== -1) {
            cell.alignment = { wrapText: true, horizontal: 'left', vertical: 'middle' };
          } else {
            cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
          }
        });
      }
    });

    // footer data row
    if (footerData != null) {
      footerData.forEach((element: any) => {
        const eachRow = [];
        element.forEach((val: any) => {
          eachRow.push(val);
        });
        const footerRow = worksheet.addRow(eachRow);
        const cellMerge = 'A' + footerRow.number + ':C' + footerRow.number;
        worksheet.mergeCells(cellMerge);
        footerRow.eachCell((cell) => {
          cell.font = { size: 15, bold: true };
          cell.border = { top: { style: 'thin' }, left: { style: 'thin' }, bottom: { style: 'thin' }, right: { style: 'thin' } };
          cell.alignment = { horizontal: 'center', vertical: 'middle' };
        });
      });
    }

    // Save Excel File
    workbook.xlsx.writeBuffer().then((data: ArrayBuffer) => {
      const blob = new Blob([data], { type: EXCEL_TYPE });
      fs.saveAs(blob, excelFileName + EXCEL_EXTENSION);
    });
  }


  private numToAlpha(num: number) {
    let alpha = '';
    for (; num >= 0; num = parseInt((num / 26).toString(), 10) - 1) {
      alpha = String.fromCharCode(num % 26 + 0x41) + alpha;
    }

    return alpha;
  }
}
