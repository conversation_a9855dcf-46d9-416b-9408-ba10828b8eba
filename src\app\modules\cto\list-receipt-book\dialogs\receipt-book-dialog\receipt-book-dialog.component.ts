import { Component, Inject, LOCALE_ID, OnInit } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { EnvService } from 'src/app/core/service/env.service';
import { TagService } from 'src/app/data/service/basecat/tag.service';
import { ReceiptBookService } from 'src/app/data/service/cto-statistics/receipt-book.service';
import { SnackbarService } from 'src/app/data/service/snackbar/snackbar.service';
import { DeploymentService } from 'src/app/data/service/deployment.service';

@Component({
  selector: 'app-receipt-book-dialog',
  templateUrl: './receipt-book-dialog.component.html',
  styleUrls: ['./receipt-book-dialog.component.scss']
})
export class ReceiptBookDialogComponent implements OnInit {

  config = this.envService.getConfig();
  formOption = {
    title: {
      0: {
        vi: "Thêm mới sổ tiếp nhận",
        en: "Add new receipt book"
      },
      1: {
        vi: "Cập nhật sổ tiếp nhận",
        en: "Update receipt book"
      },
      2: {
        vi: "Chi tiết sổ tiếp nhận",
        en: "Details receipt book"
      }
    },
    ledgerTypeId: this.deploymentService.getAppDeployment()?.configLedgerTypeId ?? "61baa383975790432875b2e6",
    submitting: false,
    disable: false,
    searchString: ''
  }
  
  receiptBook = new FormGroup({
    name: new FormControl('', Validators.required),
    code: new FormControl('', Validators.required),
    type: new FormControl('', Validators.required),
    status: new FormControl(true, Validators.required),
    year: new FormControl(new Date().getFullYear(), [Validators.required, Validators.pattern('^[0-9]{4}$')]),
    orderNumber: new FormControl(1),
  });

  listReceiptBookType = [];

  constructor(
    private dialogRef: MatDialogRef<ReceiptBookDialogComponent>,
    private envService: EnvService,
    private receiptBookService: ReceiptBookService,
    private tagService: TagService,
    private snackbarService: SnackbarService,
    private deploymentService: DeploymentService,
    @Inject(LOCALE_ID) public localeId: string,
    @Inject(MAT_DIALOG_DATA) public data: ConfirmReceiptBookDialogModel,
  ) { }

  ngOnInit(): void {
    if(this.data.type == 1) {
      this.setEdit();
    } else if(this.data.type == 2) {    
      this.setView();
      this.formOption.disable = true;
    }
  }

  async setEdit(){
    const details = await this.receiptBookService.details(this.data.id).toPromise();
    this.receiptBook = new FormGroup({
      name: new FormControl(details?.name, Validators.required),
      code: new FormControl(details?.code, Validators.required),
      type: new FormControl(details?.type, Validators.required),
      status: new FormControl(details?.status, Validators.required),
      year: new FormControl(details?.year, Validators.required),
      orderNumber: new FormControl(details?.orderNumber),
    });
  }

  async setView(){
    const details = await this.receiptBookService.details(this.data.id).toPromise();
    this.receiptBook = new FormGroup({
      name: new FormControl({ value: details?.name, disabled: true }, Validators.required),
      code: new FormControl({ value: details?.code, disabled: true }, Validators.required),
      type: new FormControl({ value: details?.type, disabled: true }, Validators.required),
      status: new FormControl({ value: details?.status, disabled: true }, Validators.required),
      year: new FormControl({ value: details?.year, disabled: true }, Validators.required),
      orderNumber: new FormControl({ value: details?.orderNumber, disabled: true })      
    });
  }

  onDismiss(){
    this.dialogRef.close(false);
  }

  async onConfirm(){
    const formObj = this.receiptBook.getRawValue();
    const typeDetails = await this.tagService.getFullyDetails(formObj?.type?.id).toPromise();   
    let requestBody = {
      name: formObj?.name,
      code: formObj?.code,
      type: {
        id: formObj?.type?.id,
        code: typeDetails?.code,
        name: typeDetails?.trans
      },
      status: !!formObj?.status?.name ? formObj?.status?.id : formObj?.status,
      year: formObj?.year,
      orderNumber: formObj?.orderNumber
    }
    if (this.receiptBook.invalid === false) {
      if (this.data.type == 0) {
        this.onPost(requestBody);
      } else if (this.data.type == 1) {
        this.onPut(requestBody);
      }
    }
  }

  onPut(requestBody: any){
    this.receiptBookService.update(this.data.id, requestBody).subscribe(data => {
      this.dialogRef.close({ status: true, data: requestBody });
      const msgObj = {
        vi: 'Cập nhật sổ thành công',
        en: 'Update ledger definition successful'
      };
      this.snackbarService.openSnackBar(1, msgObj[this.localeId],'', 'success_notification', this.config.expiredTime);
    }, err => {
      const msgObj = {
        vi: 'Không thể cập nhật sổ',
        en: 'Can not update ledger definition'
      };
      this.snackbarService.openSnackBar(0, msgObj[this.localeId],  err.error.message, 'error_notification', this.config.expiredTime);
      this.dialogRef.close({ status: false, data: requestBody });
    });
  }

  onPost(requestBody: any) {
    this.receiptBookService.post(requestBody).subscribe({
      next: data => {
        const successMsg = {
          vi: 'Thêm mới sổ thành công',
          en: 'Successfully added receipt book'
        };
        this.snackbarService.openSnackBar(
          1,
          successMsg[this.localeId],
          '',
          'success_notification',
          this.config.expiredTime
        );
  
        // Return the new item data
        this.dialogRef.close({
          status: true,
          name: requestBody?.name
        });
      },
      error: err => {
        const errorMsg = {
          vi: 'Thêm mới sổ không thành công',
          en: 'Failed to add receipt book'
        };
        this.snackbarService.openSnackBar(
          0,
          errorMsg[this.localeId],
          err?.error?.message || '',
          'error_notification',
          this.config.expiredTime
        );
  
        // Optionally notify failure
        this.dialogRef.close({
          status: false,
          name: requestBody?.name
        });
      }
    });
  }
  
  preventInvalidKeys(event: KeyboardEvent) {
    const allowedKeys = [
      'Backspace', 'Tab', 'ArrowLeft', 'ArrowRight', 'Delete', 'Home', 'End'
    ];
    const isNumber = event.key >= '0' && event.key <= '9';
    if ((!isNumber && !allowedKeys.includes(event.key))) {
      event.preventDefault();
    }
  }
}

export class ConfirmReceiptBookDialogModel {
  constructor(public type: number, public id: string, public data: any){}
}