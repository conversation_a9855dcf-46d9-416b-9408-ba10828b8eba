import {Injectable, OnInit, OnDestroy} from '@angular/core';
import {WebsocketService} from './websocket.service';
import {NotificationData, NotificationV2Service} from 'data/service/etl-data/notificationV2.service';
import {NotificationConstant} from 'data/service/etl-data/notification.constant';
import {IMessage, messageCallbackType} from '@stomp/stompjs';
import {ApiProviderService} from 'core/service/api-provider.service';
import {EnvService} from 'core/service/env.service';

@Injectable({providedIn: 'root'})
export class NotificationWebsocketService implements OnInit, OnDestroy {
  websocketService!: WebsocketService;

  constructor(
    private notificationV2Service: NotificationV2Service,
    private apiProviderService: ApiProviderService,
    private envService: EnvService,
  ) {
    const isWebSocket = this.envService.getConfig().isWebSocket;
    console.log('isWebSocket', isWebSocket);
    if (isWebSocket && isWebSocket == 'true') {
      const userCode = localStorage.getItem('UID');
      const topicSpecificUser = NotificationConstant.WS_SPECIFIC_TOPIC + '/' + userCode;
      const topicBroadcast = NotificationConstant.WS_BROADCAST_TOPIC;
      const topicAlarm = NotificationConstant.WS_ALARM_TOPIC + '/' + userCode;

      const ns = this.notificationV2Service;

      const handleBroadcastMessage = (message: IMessage) => {
        // console.log("handleBroadcastMessage", message);
        if (message.body) {
          const wsMessage = JSON.parse(message.body);
          //  console.log(wsMessage);
          if (wsMessage.message) {
            const notificationData: NotificationData[] = JSON.parse(wsMessage.message);
            // console.log("dossierReminds" , dossierReminds)
            ns.notificationDataChangeSubject.next(notificationData);
          }

        }
      };

      const handleSpecificMessage = (message: IMessage) => {
        if (message.body) {
          const wsMessage = JSON.parse(message.body);
          //  console.log(wsMessage);
          if (wsMessage.message) {
            const notificationData: NotificationData[] = JSON.parse(wsMessage.message);
            // console.log("dossierReminds" , dossierReminds)
            ns.notificationDataChangeSubject.next(notificationData);
          }

        }

      };

      const handleAlarmMessage = (message: IMessage) => {

        console.log('handleAlarmMessage', message.body);

      };

      this.initNotificationWebsocket([
        {topics: [topicBroadcast], cb: handleBroadcastMessage},
        {topics: [topicSpecificUser], cb: handleSpecificMessage},
        {topics: [topicAlarm], cb: handleAlarmMessage}
      ]);
    }
  }

  // tslint:disable-next-line:contextual-lifecycle
  ngOnInit(): void {

  }

  ngOnDestroy(): void {
    WebsocketService.getInstance(this.apiProviderService).disconnect();
    // tslint:disable-next-line:no-unused-expression
    !!this.notificationV2Service.notificationDataChangeSubject && this.notificationV2Service.notificationDataChangeSubject.unsubscribe();
  }

  private initNotificationWebsocket(topicCBs: Array<{ topics: string[], cb: messageCallbackType }>) {

    setTimeout(() => {
      this.websocketService = WebsocketService.getInstance(this.apiProviderService);
      this.websocketService.connect(
        () => {
          for (const topicCB of topicCBs) {
            for (const topic of topicCB.topics) {
              this.websocketService.subscribe(topic, topicCB.cb);
            }
          }
        },
        () => {
        },
        () => {
        });
    }, 1000);
  }

}
