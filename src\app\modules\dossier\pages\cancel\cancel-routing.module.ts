import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { CancelComponent } from './cancel.component';
import {CancleDetailComponent} from 'modules/dossier/pages/cancel/cancle-detail/cancle-detail.component';


const routes: Routes = [
  {
    path: '',
    children: [
      { path: '', component: CancelComponent },
      { path: ':id', component: CancleDetailComponent },
    ]
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class CancelRoutingModule { }
