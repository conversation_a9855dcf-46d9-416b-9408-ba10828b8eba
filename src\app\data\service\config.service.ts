// D<PERSON>ch các lable của phân trang
export const translatePaginator = ['Số dòng', 'Trang đầu', 'Trang trước', 'Trang tiếp theo', 'Trang cuối', 'của'];

// Date picker format
export const PICK_FORMATS = {
  parse: {
    dateInput: {
      month: 'short',
      year: 'numeric',
      day: 'numeric'
    }
  },
  display: {
    dateInput: 'input',
    monthYearLabel: {
      year: 'numeric',
      month: 'short'
    },
    dateA11yLabel: {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    },
    monthYearA11yLabel: {
      year: 'numeric',
      month: 'long'
    }
  }
};
//Danh mục loại lịch sử
export const typeList = [
  {"id" :1 , "name" : "Bình luận"}, {"id" : 2, "name" : "Chuyển bước"}, {"id" : 3, "name" : "<PERSON>ậ<PERSON> nhật thông tin"}, {"id" : 4 , "name" : "Thêm mới"}, {"id" : 5 , "name" : "<PERSON>o<PERSON>"}, {"id" : 6 , "name" : "Tìm kiếm"},
];

// Danh mục loại chứng thực
export const authenticationType = [
  {
    id: 0,
    name: 'Không chứng thực'
  },
  {
    id: 1,
    name: 'Chứng thực điện tử'
  },
  {
    id: 2,
    name: 'Chứng thực giấy'
  },
  {
    id: 3,
    name: 'Cả 2 loại'
  }
];

// Danh mục trạng thái chứng thực
export const authenticationStatus = [
  {
    id: 0,
    name: 'Chưa đồng bộ chứng thực'
  },
  {
    id: 1,
    name: 'Đồng bộ chứng thực thành công'
  },
  {
    id: 2,
    name: 'Đồng bộ chứng thực thất bại'
  },
  {
    id: 3,
    name: 'Cán bộ chứng thực yêu cầu bổ sung'
  },
  {
    id: 4,
    name: 'Cán bộ chứng thực từ chối hồ sơ'
  },
  {
    id: 5,
    name: 'Đã đồng bộ lại chứng thực hồ sơ yêu cầu bổ sung'
  },
  {
    id: 6,
    name: 'Cán bộ chứng thực chuyển bước trả kết quả'
  }
]

// Danh mục trạng thái đã hoàn thành chứng thực
export const authStatusComplete = [
  3,
  4,
  6
]

// Danh mục trạng thái có thể đồng bộ lại
export const authStatusCanReSync = [
  2,
  3,
  4
]

export const customHistoryData = (originalValues, newValues , idItem, groupId, userId, userFullname, deploymentId, type) => {
  let arrayDetailValue = [];
  arrayDetailValue.push({
    fieldNameRbk : "lang.word.status",
    originalValue : JSON.stringify(originalValues) ,
    newValue:  JSON.stringify(newValues)
  })
  let dataHistory: any = {
    groupId: groupId,
    itemId: idItem,
    user: {
      id : userId,
      name: userFullname
    },
    deploymentId: deploymentId,
    type: type,
    action : arrayDetailValue
  }
  return dataHistory;
}

export const reloadTimeout = 2500; // milisecond
export const pageSizeOptions = [5, 10, 20, 50];
export const notificationHistoryGroupId = 3;
export const notificationCategoryId = '6';
export const petitionHistoryGroupId = 2;
export const petitionCommentGroupId = 1;
export const petitionCategoryId = '3';
export const petitionAcceptFileExtension = ['.jpg', 'jpeg', '.png', '.mp3', '.mp4', '.txt', '.pdf'];
export const petitionAcceptFileType = ['audio/mpeg', 'image/jpeg', 'image/png', 'image/jpg', 'text/plain', 'application/pdf', 'video/mp4' ];

export const PHONE_NUMBER_REGEX = /^[0|+][0-9]{9,13}$/;
export const IDENTITY_CARD_NUMBER_REGEX = /^[0-9]{9,12}$/;
export const TEXT_INPUT_REGEX = /^[a-zA-ZÁÀẢÃẠĂẮẰẲẴẶÂẤẦẨẪẬĐÉÈẺẼẸÊẾỀỂỄỆÍÌỈĨỊÓÒỎÕỌÔỐỒỔỖỘƠỚỜỞỠỢÚÙỦŨỤƯỨỪỬỮỰÝỲỶỸỴáàảãạăắằẳẵặâấầẩẫậđéèẻẽẹêếềểễệíìỉĩịóòỏõọôốồổỗộơớờởỡợúùủũụưứừửữựýỳỷỹỵ\w]+.*[^\s]$/;
export const TEXTAREA_REGEX = /^([a-zA-ZÁÀẢÃẠĂẮẰẲẴẶÂẤẦẨẪẬĐÉÈẺẼẸÊẾỀỂỄỆÍÌỈĨỊÓÒỎÕỌÔỐỒỔỖỘƠỚỜỞỠỢÚÙỦŨỤƯỨỪỬỮỰÝỲỶỸỴáàảãạăắằẳẵặâấầẩẫậđéèẻẽẹêếềểễệíìỉĩịóòỏõọôốồổỗộơớờởỡợúùủũụưứừửữựýỳỷỹỵ\w]+.*[\n]?)+$/;

export const getShortName = name => {
  if (name.length > 12) {
    return name.substr(0, 3) + '...' + name.substring(name.length - 6, name.length);
  }
  return name;
};

//trong một mảng có nhiều phần tử trùng thì lọc lại chỉ giữ 1 phần tử
export const deleteDuplicateElement = (array) => {
  let resultArray = [];
  const uniqueArray = array.reduce((accumulator, currentValue) => {
    const id = currentValue.id;
    if (!accumulator[id]) {
      accumulator[id] = currentValue;
    }
    return accumulator;
  }, {});
  resultArray = Object.values(uniqueArray);
  return resultArray;
}

export const objectExistsInList = (object, objectList) => {
  for (const obj of objectList) {
    if (obj.id === object.id) {
      return true;
    }
  }
  return false;
};

export const stringTextExistsInList = (text, stringList) => {
  if (!!stringList.find(str => str === text)) {
    return true;
  }
  return false;
};

export const getFullAddress = (
  place,
  parentTypeIdProvinces,
  parentTypeIdTows,
  parentTypeIdVillages
) => {
  let province;
  let town;
  let village;
  for (const item of place) {
    if (stringTextExistsInList(item.typeId, parentTypeIdProvinces)) {
      province = item.name;
      continue;
    }
    if (stringTextExistsInList(item.typeId, parentTypeIdTows)) {
      town = item.name;
      continue;
    }
    if (stringTextExistsInList(item.typeId, parentTypeIdVillages)) {
      village = item.name;
      continue;
    }
  }
  let ret = '';
  ret = !!village ? village : '';
  ret = !!town ? (ret === '' ? town : ret + ', ' + town) : ret;
  ret = !!province ? (ret === '' ? province : ret + ', ' + province) : ret;
  return ret;
};

// Dossier Applicant Guide Data
export const APPLICANT_GUIDE_DATA = [
  {
    key:"organization",
    description: "Tên tổ chức"
  },
  {
    key:"fullname",
    description: "Họ và tên"
  },
  {
     key:"ownerFullname",
     description: "Họ và tên chủ hồ sơ"
  },
  {
    key:"gender",
    description: "Giới tính => 1: Nam, 2: Nữ"
  },
  {
    key:"birthday",
    description: "Ngày sinh"
  },
  {
    key:"nation",
    description: "Quốc gia"
  },
  {
    key:"province",
    description: "Tỉnh/TP"
  },
  {
    key:"district",
    description: "Quận/Huyện"
  },
  {
    key:"village",
    description: "Phường xã"
  },
  {
    key:"address",
    description: "Địa chỉ chi tiết"
  },
  {
    key:"phoneNumber",
    description: "Số điện thoại"
  },
  {
    key:"fax",
    description: "Số fax"
  },
  {
    key:"email",
    description: "Email"
  },
  {
    key:"taxCode",
    description: "Mã số thuế"
  },
  {
    key:"position",
    description: "Chức vụ"
  },
  {
    key:"accountNumber",
    description: "Số tài khoản"
  },
  {
    key:"identityNumber",
    description: "Số CMND/CCCD"
  },
  {
    key:"identityDate",
    description: "Ngày cấp CMND/CCCD"
  },
  {
    key:"identityAgency",
    description: "Nơi cấp CMND/CCCD"
  },
  {
    key:"noidungyeucaugiaiquyet",
    description: "Nội dung yêu cầu giải quyết"
  }
];

export const DOSSIER_STATUS_GUIDE_DATA = [
  {
    key:"dossierCode",
    description:"Mã hồ sơ"
  },
  {
    key:"assignee",
    description:"Người xử lý"
  },
  {
    key:"position",
    description:"Chức danh"
  },
  {
    key:"content",
    description:"Nội dung xử lý"
  },
  {
    key:"agency",
    description:"Phòng Ban xử lý"
  },
  {
    key:"status",
    description:"Trạng thái"
  },
  {
    key:"startDate",
    description:"Ngày bắt đầu"
  }
]

export const FEE_GUIDE_DATA = [
  {
    key:"HINH_THUC_NHAN_KQ",
    description: "Hình thức nhận kết quả"
  },
  {
    key:"HINH_THUC_THANH_TOAN",
    description: "Hình thức thanh toán"
  },
];

export const COMPOSITION_GUIDE_DATA = [
  {
    key:"file",
    description: "File thành phần hồ sơ"
  },
  {
    key:"detail",
    description: "Trường hợp giấy tờ (bản chính, bản phụ)"
  },
];

export const STATUS_NEEDS_CALCULATOR_TIMING = [
  {
    id: 0,
    description: 'Hồ sơ chờ tiếp nhận'
    // Hồ sơ mới nộp sẽ không tính hạn xử lý, cái này dành cho hồ sơ đang xử lý mà trả về chờ tiếp nhận
  },
  {
    id: 2,
    description: 'Hồ sơ đang xử lý'
  },
  {
    id: 11,
    description: 'Chờ phê duyệt dừng xử lý'
  },
  {
    id: 9,
    description: 'Chờ phê duyệt tạm dừng'
  },
  {
    id: 10,
    description: 'Chờ phê duyệt gia hạn'
  }
];

//IGATESUPP-47744 -Hàm kiểm tra thêm cột ghi chú trong Menu Xử lý hồ sơ/ Menu Tra cứu hồ sơ toàn cơ quan 
export const checkShowNoteEnable = (enable, objectAgecnyList) => {
  const userAgency = getUserAgencyEx();
  try {
    if (enable) {
      for (const obj of objectAgecnyList) {
        if (obj === userAgency.id) {
          return true;
        }
      }
    }
  } catch (error) {
    return false;
  }
  return false;
};

export const getUserAgencyEx =() =>{
  const userAgency = JSON.parse(localStorage.getItem('userAgency'));
  return userAgency;
}
