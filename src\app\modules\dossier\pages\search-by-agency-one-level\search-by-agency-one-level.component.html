<div fxLayout="{{interfaceWorkHorizontal == 1? 'column': 'row'}}" fxLayoutAlign="space-between" fxLayout.sm="column"
    fxLayout.xs="column">
    <div fxFlex="{{interfaceWorkHorizontal == 1? 100: 25}}" fxFlex.sm="100" fxFlex.xs="100">
        <div (click)="onClickOpenReminderMenu()" mat-button class="title-reminder" fxLayout="row"
            fxLayoutAlign="space-between">
            <div class="content">
                <span fxFlex="100" class="title"><span i18n>Danh sách công việc</span> (<a
                        class="count-task">{{lengthRemind}}</a>)</span>
                <mat-icon fxFlex="10">expand_more</mat-icon>
            </div>
        </div>
        <div class="menu_reminder">
            <mat-accordion class="advanced-box" multi>
                <mat-expansion-panel class="panel" *ngIf="expandReminderMenu" [(expanded)]="expandReminderMenu"
                    [ngStyle]="{'height': interfaceWorkHorizontal? 'unset': xpandStatus ? '28rem' : '8rem' }">
                    <span *ngFor="let remind of listMenuRemind;">
                        <a id="submenu" mat-button active-link="active"
                            [ngClass]="{ 'active': remindId === remind.id , 'interfaceWorkHorizontalClass': interfaceWorkHorizontal == 1}"
                            (click)="changeSearchRemind(remind.id,remind.name)">
                            <mat-icon>receipt</mat-icon><span class="submenuTitle">{{remind.name}}</span>&nbsp;<span
                                class="count">{{remind.count}}</span>
                        </a>
                    </span>
                </mat-expansion-panel>
            </mat-accordion>
        </div>
    </div>
    <div fxFlex="{{interfaceWorkHorizontal == 1? 100: 75}}" fxFlex.sm="100" fxFlex.xs="100" class="search">
        <h2>Tra cứu hồ sơ HCC một cấp</h2>
        <div class="prc_searchbar">
            <form [formGroup]="searchForm" (submit)="onConfirmSearch()" class="searchForm">
                <div class="allsearch">
                    <div fxLayout="row" fxLayout.xs="row" fxLayout.sm="row" class="formFieldItems"
                        fxLayoutAlign="space-between">
                        <mat-form-field appearance="outline" fxFlex.gt-md="24" fxFlex.gt-sm="32" fxFlex.gt-xs="49.5"
                            fxFlex='grow'>
                            <mat-label i18n>Cơ quan</mat-label>
                            <mat-select #agencyMatSelectInfiniteScroll msInfiniteScroll
                                (selectionChange)="AgencyChange($event)"
                                (infiniteScroll)="getNextBatch('agency')"  formControlName="advAgency"
                                [complete]="isFullListAgency == true">
                                <div  fxLayout="row" fxLayout.xs="row" fxLayout.sm="row" >
                                    <input matInput #searchAgency (keyup)="onEnter('agency', $event)"
                                        (keydown)="$event.stopPropagation()" placeholder="Nhập từ khóa"  class="search-nested" />
                                    <button mat-icon-button  *ngIf="searchAgency.value !== ''"
                                        (click)="searchAgency.value = ''; resetSearchForm('agency')" class="clear-search-nested">
                                        <mat-icon> close </mat-icon>
                                    </button>
                                </div>
                                <!-- <mat-option>
                                    <ngx-mat-select-search [formControl]="searchAgencyCtrl" placeholderLabel=""
                                        [disableScrollToActiveOnOptionsChanged]="true" i18n-noEntriesFoundLabel
                                        noEntriesFoundLabel="Không tìm thấy kết quả nào!">
                                    </ngx-mat-select-search>
                                </mat-option> -->
                                <mat-option value="" i18n>Tất cả</mat-option>
                                <mat-option *ngFor="let agency of agencyFiltered | async" value="{{agency.id}}">
                                    {{agency.name}}
                                    <span
                                        *ngIf="agency.name == undefined || agency.name == null || agency.name.trim() == ''"
                                        i18n>(Không tìm thấy bản dịch)</span>
                                </mat-option>
                            </mat-select>
                            <!-- <mat-select msInfiniteScroll (infiniteScroll)="getAgencyList(true)" formControlName="advAgency"
                                        [complete]="isAgencyListFull">
                                <mat-option>
                                    <ngx-mat-select-search formControlName="searchAgencyCtrl" placeholderLabel=""
                                    (selectionChange)="getAgencyList(true)"
                                                        [disableScrollToActiveOnOptionsChanged]="true"
                                                        i18n-noEntriesFoundLabel
                                                        noEntriesFoundLabel="Không tìm thấy kết quả nào!">
                                        <mat-icon ngxMatSelectSearchClear (click)="clearAgencyList()">close</mat-icon>
                                    </ngx-mat-select-search>
                                </mat-option>
                                <mat-option value="" i18n>Tất cả</mat-option>
                                <mat-option *ngFor="let agency of agencyList" value="{{agency.id}}">
                                    {{agency.name}}
                                    <span *ngIf="agency.name == undefined || agency.name == null" i18n>(Không tìm thấy bản dịch)</span>
                                </mat-option>
                            </mat-select> -->
                            <!-- <mat-select formControlName="advAgency" (selectionChange)="agencyChange($event)"
                                msInfiniteScroll (infiniteScroll)="getListAgency()"
                                [complete]="isFullListAgency == true">
                                <mat-option value=""><span i18n>Tất cả</span></mat-option>
                                <mat-option *ngFor='let agency of listAgency' value="{{agency.id}}">
                                    {{agency.name}}
                                    <span
                                    (keyup)="searchAgencyList()"
                                        *ngIf="agency.name == undefined || agency.name == null || agency.name.trim() == ''"
                                        i18n>(Không tìm thấy bản dịch)</span>
                                </mat-option>
                            </mat-select> -->
                        </mat-form-field>
                        <mat-form-field appearance="outline" fxFlex.gt-md="24" fxFlex.gt-sm="32" fxFlex.gt-xs="49.5"
                            fxFlex='grow'>
                            <mat-label i18n>Mã số hồ sơ</mat-label>
                            <input type="text" matInput formControlName="code">
                        </mat-form-field>
                        <mat-form-field appearance="outline" fxFlex.gt-md="24" fxFlex.gt-sm="32" fxFlex.gt-xs="49.5"
                            fxFlex='grow'>
                            <mat-label i18n>Tiếp nhận từ ngày</mat-label>
                            <input matInput [matDatepicker]="pickerAcceptFrom" formControlName="advAcceptFrom"
                                (dateChange)="endDateErrShowed('advAcceptFrom', 'advAcceptTo')">
                            <mat-datepicker-toggle matSuffix [for]="pickerAcceptFrom"></mat-datepicker-toggle>
                            <mat-datepicker #pickerAcceptFrom></mat-datepicker>
                        </mat-form-field>
                        <mat-form-field appearance="outline" fxFlex.gt-md="24" fxFlex.gt-sm="32" fxFlex.gt-xs="49.5"
                            fxFlex='grow' class="hasError">
                            <mat-label i18n>Tiếp nhận đến ngày</mat-label>
                            <input matInput [matDatepicker]="pickerAcceptTo" formControlName="advAcceptTo"
                                (dateChange)="endDateErrShowed('advAcceptFrom', 'advAcceptTo')">
                            <mat-datepicker-toggle matSuffix [for]="pickerAcceptTo"></mat-datepicker-toggle>
                            <mat-datepicker #pickerAcceptTo></mat-datepicker>
                            <mat-error *ngIf="searchForm.get('advAcceptTo').hasError('limit')">Ngày bắt đầu không được
                                vượt quá ngày kết thúc</mat-error>
                        </mat-form-field>
                    </div>
                    <div fxLayout="row" fxLayout.xs="row" fxLayout.sm="row" class="formFieldItems"
                        fxLayoutAlign="space-between">
                        <mat-form-field appearance="outline" fxFlex.gt-sm="24" fxFlex.gt-xs="49.5" fxFlex='grow'>
                            <mat-label i18n>Lĩnh vực</mat-label>
                            <mat-select #sectorMatSelectInfiniteScroll msInfiniteScroll
                                (infiniteScroll)="getNextBatch('sector')" formControlName="advSector"
                                [complete]="isFullListSector == true">
                                <!-- <mat-option> -->
                                    <div  fxLayout="row" fxLayout.xs="row" fxLayout.sm="row" >
                                        <input matInput #searchSector (keyup)="onEnter('sector', $event)"
                                            (keydown)="$event.stopPropagation()" placeholder="Nhập từ khóa"  class="search-nested" />
                                        <button mat-icon-button  *ngIf="searchSector.value !== ''"
                                            (click)="searchSector.value = ''; resetSearchForm('sector')" class="clear-search-nested">
                                            <mat-icon> close </mat-icon>
                                        </button>
                                    </div>
                                    <!-- <ngx-mat-select-search [formControl]="searchSectorCtrl" placeholderLabel=""
                                        [disableScrollToActiveOnOptionsChanged]="true" i18n-noEntriesFoundLabel
                                        noEntriesFoundLabel="Không tìm thấy kết quả nào!">
                                    </ngx-mat-select-search> -->
                                <!-- </mat-option> -->
                                <mat-option value="" i18n>Tất cả</mat-option>
                                <mat-option *ngFor="let sector of sectorFiltered | async" value="{{sector.id}}">
                                    {{sector.name}}
                                    <span
                                        *ngIf="sector.name == undefined || sector.name == null || sector.name.trim() == ''"
                                        i18n>(Không tìm thấy bản dịch)</span>
                                </mat-option>
                            </mat-select>
                        </mat-form-field>
                        <mat-form-field appearance="outline" fxFlex.gt-sm="24" fxFlex.gt-xs="49.5" fxFlex='grow'>
                            <mat-label i18n>Thủ tục</mat-label>
                            <mat-select #procedureMatSelectInfiniteScroll msInfiniteScroll
                                (infiniteScroll)="getNextBatch('procedure')"
                                formControlName="advProcedure" (selectionChange)="advProcedureChange($event)"
                                [complete]="isFullListProcedure == true">
                                <div  fxLayout="row" fxLayout.xs="row" fxLayout.sm="row" >
                                    <input matInput #searchProcedure (keyup)="onEnter('procedure', $event)"
                                        (keydown)="$event.stopPropagation()" placeholder="Nhập từ khóa"  class="search-nested" />
                                    <button mat-icon-button  *ngIf="searchProcedure.value !== ''"
                                        (click)="searchProcedure.value = ''; resetSearchForm('procedure')" class="clear-search-nested">
                                        <mat-icon> close </mat-icon>
                                    </button>
                                </div>
                                <!-- <mat-option>
                                    <ngx-mat-select-search [formControl]="searchProcedureCtrl" placeholderLabel=""
                                        [disableScrollToActiveOnOptionsChanged]="true" i18n-noEntriesFoundLabel
                                        noEntriesFoundLabel="Không tìm thấy kết quả nào!">
                                    </ngx-mat-select-search>
                                </mat-option> -->
                                <mat-option value="" i18n>Tất cả</mat-option>
                                <mat-option
                                    matTooltip="{{procedure?.translate?.name}}{{procedure?.agency?.name != '' ?  ' - ' +  procedure?.agency?.name : ''}}"
                                    matTooltipDisabled="{{enableTooltip}}"
                                    *ngFor="let procedure of procedureFiltered | async" value="{{procedure.id}}">
                                    {{procedure?.translate?.name}}{{procedure?.agency?.name != '' ? ' - ' + procedure?.agency?.name : ''}}
                                    <span
                                        *ngIf="procedure?.translate?.name == undefined || procedure?.translate?.name == null || procedure?.translate?.name.trim() == ''"
                                        i18n>(Không tìm thấy bản dịch)</span>
                                </mat-option>
                            </mat-select>
                        </mat-form-field>
                        <mat-form-field appearance="outline" fxFlex.gt-md="24" fxFlex.gt-sm="32" fxFlex.gt-xs="49.5"
                            fxFlex='grow'>
                            <mat-label i18n>Trạng thái hồ sơ</mat-label>
                            <mat-select formControlName="advTaskStatusId" msInfiniteScroll
                                (infiniteScroll)="getListDossierTaskName()"
                                [complete]="isFullListDossierTaskName == true">
                                <mat-option value=""><span i18n>Tất cả</span></mat-option>
                                <mat-option *ngFor='let taskNameOpt of listDossierTaskName' value="{{taskNameOpt.id}}">
                                    {{taskNameOpt.name}}
                                    <span
                                        *ngIf="taskNameOpt.name == undefined || taskNameOpt.name == null || taskNameOpt.name.trim() == ''"
                                        i18n>(Không tìm thấy bản dịch)</span>
                                </mat-option>
                            </mat-select>
                        </mat-form-field>
                        <mat-form-field appearance="outline" fxFlex.gt-md="24" fxFlex.gt-sm="32" fxFlex.gt-xs="49.5"
                            fxFlex='grow'>
                            <mat-label>Trạng thái VNPOST</mat-label>
                            <mat-select formControlName="advVNpostStatusId" msInfiniteScroll
                                (infiniteScroll)="getListDossierTaskName()"
                                [complete]="isFullListDossierTaskName == true">
                                <mat-option value=""><span i18n>Tất cả</span></mat-option>
                                <mat-option *ngFor='let vnpostStatus of listVNpostStatus' value="{{vnpostStatus.id}}">
                                    {{vnpostStatus.value}}
                                </mat-option>
                            </mat-select>
                        </mat-form-field>
                    </div>
                    <div fxLayout="row" fxLayout.xs="row" fxLayout.sm="row" class="formFieldItems"
                        fxLayoutAlign="start center" fxLayoutGap="8px">
                        <button mat-flat-button fxFlex.gt-sm="16" fxFlex.gt-xs="49.5" fxFlex='grow' class="searchBtn"
                            type="submit">
                            <mat-icon style="color: white">search</mat-icon><span i18n>Tìm kiếm</span>
                        </button>
                      
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- <div fxLayout="row" fxLayoutAlign="center">
    <div fxFlex="grow">
        <div class="top-controlcancel" fxLayout="row" fxLayoutAlign="start">
            <button style = "margin-left: 10px;" mat-flat-button *ngIf="exportExcelQNM"  class="primary-btn" (click)="exportExcelDossierQNM()" >
                <mat-icon style = "margin-right: 10px;">cloud_download</mat-icon>
                <span>Xuất excel</span>
            </button>
            <button style = "margin-left: 10px;" mat-flat-button *ngIf="exportExcelQNM"  class="primary-btn" (click)="downloadAsPDF()" >
                <mat-icon style = "margin-right: 10px;">cloud_download</mat-icon>
                <span>Xuất PDF</span>
            </button>
        </div>
    </div>
</div> -->

<div fxLayout="row" fxLayoutAlign="center">
    <div class="frm_main" fxFlex="grow">
        <div class="top-control">
            <!-- <button mat-stroked-button class="btn-claim" fxFlex.gt-md="12" fxFlex.gt-sm="32" fxFlex.gt-xs="40"
                fxFlex='grow'>
                <mat-icon>book_online</mat-icon><span i18n>Nhận xử lý</span>
            </button> -->

            <!-- <mat-form-field class="cbx-print-report" appearance="outline" fxFlex.gt-md="12" fxFlex.gt-sm="32"
                fxFlex.gt-xs="40" fxFlex='grow'>
                <span matPrefix style="margin-right: 8px;">
                    <mat-icon class="icon">print</mat-icon>
                </span>
                <mat-label><span i18n>In phiếu</span></mat-label>
                <mat-select (selectionChange)="printReportChange($event)">
                    <mat-option value="0"><span>Phiếu tiếp nhận</span></mat-option>
                    <mat-option value="1"><span>Phiếu lệ phí</span></mat-option>
                </mat-select>
            </mat-form-field> -->
            <button mat-flat-button fxFlex.gt-sm="12" fxFlex.gt-xs="49.5" fxFlex='grow'
                [disabled]="selectedDossiers.length == 0"
                [ngClass]="{ 'primary-btn': selectedDossiers.length !== 0, '':   selectedDossiers.length == 0 }"
                (click)="deleteMultiDossier()"
                *ngIf="oneGateDeleteDossier === '1' && ((env?.hideDeleteButton !== true && env?.isDienBien === true) || hasDossierDeletePermission)">
                <mat-icon>delete_outline</mat-icon><span i18n>Xóa hồ sơ</span>
            </button>
        </div>
        <div *ngIf="checkNullData === 1 then nullData; else hasData"></div>
        <ng-template #nullData>
            <br />
            <div fxLayout="row" fxLayoutAlign="center">
                <div fxFlex="grow" style="text-align: center; font-size: 18px">Không có kết quả tìm kiếm</div>
            </div>
        </ng-template>
        <ng-template #hasData>
        <div class="searchtbl">
            <table mat-table [dataSource]="dataSource">
                <!-- <ng-container matColumnDef="stt">
                    <mat-header-cell *matHeaderCellDef>
                        <mat-checkbox class="checkbox" [(ngModel)]="isCheckedAll" (change)="setAll($event.checked)">
                        </mat-checkbox>
                    </mat-header-cell>
                    <mat-cell *matCellDef="let row" data-label="">
                        <mat-checkbox class="checkbox" [checked]=row.checked></mat-checkbox>
                    </mat-cell>
                </ng-container> -->
                <ng-container matColumnDef="select">
                    <mat-header-cell *matHeaderCellDef class="checkAllProcedureAdd">
                        <mat-checkbox [checked]="checkAll" (change)="checkAllItem($event)">
                        </mat-checkbox>
                    </mat-header-cell>
                    <mat-cell *matCellDef="let row; index as i" data-label="Chọn" class="checkAllProcedureAdd">
                        <mat-checkbox (change)="checkItem($event, row)" [(ngModel)]="row.checked">
                        </mat-checkbox>
                    </mat-cell>
                </ng-container>

                <ng-container matColumnDef="code">
                    <mat-header-cell *matHeaderCellDef i18n>Mã số hồ sơ</mat-header-cell>
                    <mat-cell *matCellDef="let row" i18n-data-label data-label="Mã số hồ sơ" style="display: block;"
                        [ngClass]="{ 'cell_code_online': row.applyMethod.id === 0||4, 'cell_code_direct':   row.applyMethod.id === 1, 'cell_code_other':   row.applyMethod.id === 2 }"
                        #tooltip="matTooltip"
                        [matTooltip]="showDossierName?row.codeText+' - '+row.applicant?.data?.tenHoSo:row.codeText"
                        style="flex-direction: column">
                        <a (click)="dossierDetail(row.id, row.procedure.id, row.task)">{{row.code}}<span
                                *ngIf="!!row.nationCode && row.nationCode != ''"><br> ({{row.nationCode}})</span></a>
                        <li *ngIf="row.takeNumber!=null "><span [ngStyle]="{'color': row.due[0]?.timesheet?.color }"
                                matTooltip="{{row.takeNumber}}"> Mã số được cấp: {{row.takeNumber}}</span></li>
                        <li *ngIf="!!row?.listCodeTakeNumber && row?.listCodeTakeNumber.length != 0"><span
                                [ngStyle]="{'color': row.due[0]?.timesheet?.color }"
                                matTooltip="{{row.listCodeTakeNumber}}"> Mã số được cấp:
                                {{row.listCodeTakeNumber}}</span></li>
                        <br>
                        <span class="more-text dossier-name" *ngIf="showDossierName"
                            style="color: rgba(0,0,0,.87); font-weight: normal"> {{row.applicant?.data?.tenHoSo}}</span>
                    </mat-cell>
                </ng-container>
                <ng-container matColumnDef="noidung">
                    <mat-header-cell *matHeaderCellDef>Nội dung yêu cầu giải quyết</mat-header-cell>
                    <mat-cell *matCellDef="let row" i18n-data-label data-label="Nội dung yêu cầu giải quyết"
                        style="display: block;color: black" #tooltip="matTooltip"
                        [ngClass]="{ 'cell_code_online': row.applyMethod.id === 0||4, 'cell_code_direct':   row.applyMethod.id === 1, 'cell_code_other':   row.applyMethod.id === 2 }"
                        matTooltip="{{row.applicant?.data?.noidungyeucaugiaiquyet}}">
                        <a (click)="dossierDetail(row.id, row.procedure.id, row.task)">
                            <span [ngClass]="{'colum_noidung_qbh': this.qbhlayoutformalities == true}">
                                {{row.applicant.data.noidungyeucaugiaiquyet}}
                            </span></a>
                    </mat-cell>
                </ng-container>

                <ng-container matColumnDef="procedureName">
                    <mat-header-cell *matHeaderCellDef i18n>Thủ tục</mat-header-cell>
                    <mat-cell *matCellDef="let row" i18n-data-label data-label="Thủ tục">
                        <span>
                            <a class="cell_code"
                                (click)="dossierDetail(row.id, row.procedure.id, row.task)">{{row.procedure.code}}</a>
                            <span class="procedureName" *ngIf="row.procedure.translate == undefined"> - <span
                                    i18n>(Không tìm thấy bản dịch)</span></span>
                            <span class="procedureName" *ngIf="row.procedure.translate" #tooltip="matTooltip"
                                matTooltip="{{row.procedure.translate.name}}"> - {{row.procedure.translate.name}}
                                <span
                                    *ngIf="row.procedure.translate != undefined && ( row.procedure.translate.name == undefined || row.procedure.translate.name == null || row.procedure.translate.name.trim() == '')"
                                    i18n>(Không tìm thấy bản dịch)</span></span>
                        </span>
                    </mat-cell>
                </ng-container>

                <ng-container matColumnDef="processingTime">
                    <mat-header-cell *matHeaderCellDef i18n>Thời gian quy định</mat-header-cell>
                    <mat-cell *matCellDef="let row" i18n-data-label data-label="Thời gian quy định">
                        <ul>
                            <span
                                *ngIf="!!row.acceptedDate && statusNeedsCalculatorTiming.includes(row.dossierStatus.id) && row.undefindedCompleteTime === 0"
                                class="due">
                                <span *ngIf="row.due.length > 0">
                                    <span
                                        *ngIf="row.due[0].timesheet.isOverDue == true && row.checkHindenOverDueCalculation"
                                        class="overdue">Đã quá hạn </span>
                                    <span *ngIf="row.due[0].timesheet.isOverDue == false">Còn lại </span>
                                    <ng-container
                                        *ngIf="row.due[0].timesheet.isOverDue == true; then overDueTime else earrlyDueTime;"></ng-container>
                                    <ng-template #overDueTime>
                                        <span *ngIf="row.checkHindenOverDueCalculation"
                                            [ngClass]="{'overdue': row.due[0].timesheet.isOverDue == true}">
                                            <span class="timeNumber">{{row.due[0].timesheet.timer.day}}</span><span>
                                                ngày </span>
                                            <span class="timeNumber">{{row.due[0].timesheet.timer.hour}}</span><span>
                                                giờ </span>
                                            <span class="timeNumber">{{row.due[0].timesheet.timer.minute}}</span><span>
                                                phút </span>
                                            <span class="timeNumber">{{row.due[0].timesheet.timer.second}}</span><span>
                                                giây </span>
                                        </span>
                                    </ng-template>
                                    <ng-template #earrlyDueTime>
                                        <span [ngClass]="{'overdue': row.due[0].timesheet.isOverDue == true}">
                                            <span class="timeNumber">{{row.due[0].timesheet.timer.day}}</span><span>
                                                ngày </span>
                                            <span class="timeNumber">{{row.due[0].timesheet.timer.hour}}</span><span>
                                                giờ </span>
                                            <span class="timeNumber">{{row.due[0].timesheet.timer.minute}}</span><span>
                                                phút </span>
                                            <span class="timeNumber">{{row.due[0].timesheet.timer.second}}</span><span>
                                                giây </span>
                                        </span>
                                    </ng-template>
                                    <!-- <span [ngClass]="{'overdue': row.due[0].timesheet.isOverDue == true}">
                                        <span class="timeNumber">{{row.due[0].timesheet.timer.day}}</span><span> ngày </span>
                            <span class="timeNumber">{{row.due[0].timesheet.timer.hour}}</span><span> giờ </span>
                            <span class="timeNumber">{{row.due[0].timesheet.timer.minute}}</span><span> phút </span>
                            <span class="timeNumber">{{row.due[0].timesheet.timer.second}}</span><span> giây </span>
                            </span> -->
                                </span>
                            </span>
                            <li><span>Ngày nộp: </span>{{row.appliedDate | date : 'dd/MM/yyyy HH:mm:ss'}}</li>
                            <li *ngIf="row.acceptedDate != undefined"><span>Ngày tiếp nhận:
                                </span>{{(!!row.oldAcceptedDate ? row.oldAcceptedDate : row.acceptedDate) | date :
                                'dd/MM/yyyy HH:mm:ss'}}</li>
                            <li
                                *ngIf="additionalRequirementDate === true && !!row.additionalRequirementDetail?.acceptedDate">
                                <span>Ngày tiếp nhận bổ sung: </span>{{row?.additionalRequirementDetail?.acceptedDate |
                                date : 'dd/MM/yyyy HH:mm:ss'}}
                            </li>
                            <ng-container
                                *ngIf="!getCheckHideDossierTaskStatus(row) && row.undefindedCompleteTime != 1; then existAppointment else notExistAppointment;"></ng-container>
                            <ng-template #existAppointment>
                                <li
                                    *ngIf="!getCheckHideDossierTaskStatus(row) && row.acceptedDate != undefined && row.appointmentDate != null && row.appointmentDate != undefined">
                                    <span>Hạn xử lý toàn quy trình: </span>{{row.appointmentDate | date : 'dd/MM/yyyy
                                    HH:mm:ss'}}
                                </li>
                                <li
                                    *ngIf="!getCheckHideDossierTaskStatus(row) && row.acceptedDate != undefined && (row.appointmentDate == null || row.appointmentDate == undefined)">
                                    <span>Hạn xử lý toàn quy trình: </span>{{row.endDate[0].due | date : 'dd/MM/yyyy
                                    HH:mm:ss'}}
                                </li>
                            </ng-template>
                            <ng-template #notExistAppointment>
                                <li *ngIf="!getCheckHideDossierTaskStatus(row)"><span>Hạn xử lý toàn quy trình:
                                    </span>(<i i18n>Không xác định thời hạn</i>)</li>
                            </ng-template>

                            <!-- <li *ngIf="row.acceptedDate != undefined && row.undefindedCompleteTime === 0"><span>Ngày hẹn trả: </span>{{row.appointmentDate | date : 'dd/MM/yyyy HH:mm:ss'}}</li> -->
                            <li
                                *ngIf="!getCheckHideDossierTaskStatus(row) && row.acceptedDate != undefined && row.undefindedCompleteTime === 0">
                                <span i18n>Ngày hẹn trả: </span>
                                <ng-container
                                    *ngIf="row?.approvalData?.extendTime != null && row?.approvalData?.extendTime != '' && row?.approvalData?.extendTime != undefined; then extendTime else appointment;"></ng-container>
                                <ng-template #extendTime>
                                    <span>{{row?.approvalData?.extendTime | date : 'dd/MM/yyyy HH:mm:ss'}}</span>
                                </ng-template>
                                <ng-template #appointment>
                                    <span>{{row.appointmentDate | date : 'dd/MM/yyyy HH:mm:ss'}}</span>
                                </ng-template>
                            </li>

                            <li *ngIf="row.applicant?.data?.noiDung != '' && row.applicant?.data?.noiDung">Nội dung
                                trích yếu: {{row.applicant?.data?.noiDung}} </li>
                        </ul>
                    </mat-cell>
                </ng-container>

                <ng-container matColumnDef="applicantName">
                    <mat-header-cell *matHeaderCellDef i18n>Người nộp</mat-header-cell>
                    <mat-cell *matCellDef="let row" i18n-data-label data-label="Người nộp">
                        <ul>
                            <li *ngIf="row.applicant?.data?.fullname != '' && row.applicant?.data?.fullname">Người nộp:
                                {{row.applicant?.data?.fullname}} </li>
                            <li
                                *ngIf="row.applicant?.data?.organization != '' && row.applicant?.data?.organization && showOrganizationInformation">
                                Cơ quan/doanh nghiệp: {{row.applicant?.data?.organization}} </li>
                            <li
                                *ngIf="row.applicant?.data?.address1 != '' && row.applicant?.data?.address1 && showOrganizationInformation">
                                Địa chỉ chi tiết: {{row.applicant?.data?.address1}} </li>
                            <li
                                *ngIf="row.applicant?.data?.village1?.label != '' && row.applicant?.data?.village1?.label && showOrganizationInformation">
                                Phường/xã : {{row.applicant?.data?.village1?.label}} </li>
                            <li
                                *ngIf="row.applicant?.data?.district1?.label != '' && row.applicant?.data?.district1?.label && showOrganizationInformation">
                                Quận/huyện: {{row.applicant?.data?.district1?.label}} </li>
                            <li
                                *ngIf="row.applicant?.data?.province1?.label != '' && row.applicant?.data?.province1?.label && showOrganizationInformation">
                                Tỉnh/thành phố: {{row.applicant?.data?.province1?.label}} </li>
                        </ul>
                    </mat-cell>
                </ng-container>

                <ng-container matColumnDef="profileOwner">
                    <mat-header-cell *matHeaderCellDef>Chủ hồ sơ</mat-header-cell>
                    <mat-cell *matCellDef="let row" i18n-data-label data-label="Chủ hồ sơ">
                        {{row.applicant?.data?.ownerFullname}}
                    </mat-cell>
                </ng-container>

                <ng-container matColumnDef="pay">
                    <mat-header-cell *matHeaderCellDef i18n>Trạng thái thanh toán</mat-header-cell>
                    <mat-cell *matCellDef="let row" i18n-data-label data-label="Trạng thái thanh toán">
                        <span>{{row.status}}</span>
                    </mat-cell>
                </ng-container>

                <ng-container matColumnDef="agency">
                    <mat-header-cell *matHeaderCellDef i18n>Cơ quan thực hiện</mat-header-cell>
                    <div *ngIf="!showAssignee">
                        <mat-cell *matCellDef="let row" i18n-data-label data-label="Cơ quan thực hiện"> {{check(row)}}
                        </mat-cell>
                    </div>
                    <div *ngIf="showAssignee">
                        <mat-cell [ngStyle]="{'color': row.due[0]?.timesheet?.color || 'black'}" *matCellDef="let row"
                            i18n-data-label data-label="Cơ quan thực hiện">
                            <ul>
                                <li><span i18n>Cơ quan</span>:&nbsp;<span>{{check(row)}}</span></li>
                                <li *ngIf="row.userProcessing !== null && row.userProcessing !== ''"><span>Cán
                                        bộ</span>:&nbsp;<span
                                        matTooltip="{{row.userProcessing}}">{{row.userProcessing}}</span></li>
                            </ul>
                        </mat-cell>
                    </div>
                </ng-container>

                <ng-container matColumnDef="status">
                    <mat-header-cell *matHeaderCellDef i18n>Trạng thái</mat-header-cell>
                    <mat-cell *matCellDef="let row" i18n-data-label data-label="Trạng thái"
                        style="display: inline-block;">
                        <!-- <span *ngIf="row.dossierStatus != undefined && (row.dossierStatus.id == 5 || row.dossierStatus.id == 0 || row.dossierStatus.id == 6)" [style.color]="getStatusColor(row.dossierStatus.id)">
                            {{row.dossierStatus.name}}
                        </span>
                        <ng-container *ngIf="row.currentTask != undefined">
                            <span style="color: rgb(243, 156, 18);" *ngIf="row.currentTask[0]?.bpmProcessDefinitionTask?.name != undefined && row.dossierStatus.id !== 5 && row.dossierStatus.id !== 0">{{row.currentTask[0].bpmProcessDefinitionTask.name['name']}}</span>
                        </ng-container> -->
                        <span [style.color]="getStatusColor(row.dossierStatus.id)">{{row.dossierStatus.name}}
                            <span *ngIf="env?.vnpost?.config === 1 || env?.vnpost?.config === '1'"
                                style="color: black;">
                                <br> {{row.vnpostStatusReturn}}
                            </span>
                        </span>
                        <span *ngIf="this.showStatusCTDT && row.authenticationStatusCurrent"
                            style="color: black;"><i>{{setAuthStatusName(row.authenticationStatusCurrent?.id)}}</i><br></span>
                        <span *ngIf="showStatusVnpost == 1 && row.vnpostStatus" style="color: black;">Trạng thái
                            Vnpost:<br>{{row.vnpostStatus}}</span>
                        <span *ngIf="row?.vnpostStatus5343" style="color: black; font-weight: 500;">
                            <br> VNPost: {{row.vnpostStatus5343.statusMessage}} <br>
                        </span>
                        <span *ngIf="row.extendQNI?.isVbdlis == true"> Đồng bộ VBDLIS </span><br>
                        <span style="color: red"
                            *ngIf="row.extendQNI?.isVbdlis !== undefined && row.extendQNI?.isVbdlis == false"> Đồng bộ
                            VBDLIS thất bại </span>
                        <span style="color: red"
                            *ngIf="row.extendQNI?.isTBNOHTTTL !== undefined && row.extendQNI?.isTBNOHTTTL == false">
                            Đồng bộ tích hợp TBNOHTTTL thất bại </span>
                        <span *ngIf="row.extendQNI?.isTBNOHTTTL == true"> Đồng bộ tích hợp TBNOHTTTL </span><br>
                        <span *ngIf="row.extendQNI?.isIlis == true"> Đồng bộ ILIS </span><br>
                        <span style="color: red"
                            *ngIf="row.extendQNI?.isIlis !== undefined && row.extendQNI?.isIlis == false"> Đồng bộ ILIS
                            thất bại </span>
                        <span *ngIf="row.extendCMU?.isBhtn == true"> Đồng bộ BHTN </span><br>
                        <span style="color: red"
                            *ngIf="row.extendCMU?.isBhtn !== undefined && row.extendCMU?.isBhtn == false"> Đồng bộ BHTN
                            thất bại </span>
                    </mat-cell>
                </ng-container>

                <ng-container matColumnDef="action">
                    <mat-header-cell *matHeaderCellDef i18n>Thao tác</mat-header-cell>
                    <mat-cell *matCellDef="let row" i18n-data-label data-label="Thao tác">
                        <button (click)="checkPermissionShowNote(row?.currentTask)" mat-icon-button [matMenuTriggerFor]="actionMenu">
                            <mat-icon>more_horiz</mat-icon>
                        </button>
                        <mat-menu #actionMenu="matMenu" xPosition="before">
                            <ng-container *ngIf="enableShowPaymentOnlineQni">
                                <button mat-menu-item class="menuAction" (click)="paymentOnlineOfficer(row)">
                                    <mat-icon>monetization_on</mat-icon>
                                    <span >Thanh toán trực tuyến</span>
                                </button>
                            </ng-container>                            
                            <ng-container *ngIf = "row.extendQNI?.isVbdlis == undefined && row.extendQNI?.isIlis == undefined && row.extendCMU?.isBhtn == undefined">
                                <ng-container *ngIf="hiddenButtonIssueReceiptsDossierOnline==false">
                                    <ng-container *ngIf="allowCreateReceiptWithAnyStatus == true; else elseNotDone">
                                        <button mat-menu-item class="menuAction" (click)="receiptCreation(row.id, row.code, row.procedure.id, row.task, row.applyMethod?.id)" *ngIf="allowShowButtonReleaseViewReceipt == true || isShowDossierReceiptCreationStatus()">
                                            <mat-icon>receipt</mat-icon>
                                            <span i18>Phát hành biên lai</span> 
                                        </button>
                                    </ng-container>
                                    <ng-template #elseNotDone>
                                        <ng-container *ngIf="row | checkValidConfigButton">
                                            <button mat-menu-item class="menuAction" (click)="receiptCreation(row.id, row.code, row.procedure.id, row.task, row.applyMethod?.id)" *ngIf="allowShowButtonReleaseViewReceipt == true || isShowDossierReceiptCreationStatus()">
                                                <mat-icon>receipt</mat-icon>
                                                <span i18>Phát hành biên lai</span>
                                            </button>
                                        </ng-container>
                                    </ng-template>
                                </ng-container>
                                <ng-container *ngIf="hiddenButtonIssueReceiptsDossierOnline==true">                                   
                                        <ng-container *ngIf="(row | checkValidConfigButton) && row?.status!='Chưa thanh toán' && row?.status!='Không tính phí' ">
                                            <button mat-menu-item class="menuAction" (click)="receiptCreation(row.id, row.code, row.procedure.id, row.task, row.applyMethod?.id)" *ngIf="allowShowButtonReleaseViewReceipt == true || isShowDossierReceiptCreationStatus()">
                                                <mat-icon>receipt</mat-icon>
                                                <span i18>Phát hành biên lai</span>
                                            </button>
                                        </ng-container>
                                </ng-container>
                                <button mat-menu-item class="menuAction" (click)="receiptList(row.id, row.procedure.id)" *ngIf="allowShowButtonReleaseViewReceipt == true">
                                    <mat-icon>preview</mat-icon>
                                    <span i18n>Xem biên lai</span>
                                </button>
                                <button mat-menu-item class="menuAction" (click)="checkPaymentByCode(row.code)" *ngIf="showReCheckPayment == true">
                                    <mat-icon>preview</mat-icon>
                                    <span>Kiểm tra lại thanh toán payment</span>
                                </button>
                                <button mat-flat-button class="btnSecondary" (click)=" $event.stopPropagation(); $event.preventDefault(); getListConfigTemplateDetail(row.agency.id, row.procedure.id)" [matMenuTriggerFor]="printMenu" *ngIf="allowShowButtonPrint == true">
                                    <mat-icon>print</mat-icon>
                                    <span i18n>In phiếu</span>
                                    <mat-icon>keyboard_arrow_down</mat-icon>
                                </button>
                                <mat-menu #printMenu="matMenu">
                                    <button mat-menu-item *ngFor="let bill of listConfigTemplateDetail" (click)=" createPrintBill(bill.file.path, row.id, bill.id)"  >{{bill.name}}</button>
                                </mat-menu>
                                <button mat-menu-item class="menuAction" *ngIf="row.task != undefined && row.task.length > 0" (click)="viewProcess(row.id, row.code)">
                                    <mat-icon>insights</mat-icon><span i18n>Xem quy trình</span>
                                </button>
                                <button mat-menu-item class="menuAction" (click)="viewSendAnApologyLetter(row.id, row.code)" *ngIf="enableFunctionToSendApologyLetter">
                                    <mat-icon>email</mat-icon><span>Gửi thư xin lỗi</span>
                                </button>
                                <button *ngIf ="!!row.task && row.task.length > 0 && row.task[0].bpmProcessDefinitionTask?.dynamicVariable?.tbnohtttlQni == true && row.extendQNI?.isTBNOHTTTL == undefined" mat-menu-item (click)="sendInfoDossierTBNOHTTTLQNI(row.id)">
                                    <mat-icon>memory</mat-icon>
                                    <span>Tiếp nhận TBNOHTTTL QNI</span>
                                </button>
                                <button *ngIf ="row.extendQNI?.isTBNOHTTTL == true" mat-menu-item (click)="sendInfoProcessDossierTBNOHTTTLQNI(row.code)">
                                    <mat-icon>memory</mat-icon>
                                    <span>Đồng bộ trạng thái TBNOHTTTL QNI</span>
                                </button>
                                <button mat-menu-item class="menuAction" (click)="dossierDetail1($event, row.id, row.procedure.id, row.task)">
                                    <mat-icon>info</mat-icon><span i18n>Chi tiết hồ sơ</span>
                                </button>
                                <button mat-menu-item class="menuAction" (click)="reassign(row.id, row.code, row?.currentTask, row.procedure?.id)" *ngIf="enableReassignDossier && !isAdmin && row.canReassign && [2, 4].includes(row.dossierStatus?.id) && !!row.previousTask && row.previousTask.length !== 0">
                                    <mat-icon>edit</mat-icon><span>Điều chỉnh lại người xử lý</span>
                                </button>
                                <button mat-menu-item class="menuAction" *ngIf="(isDienBien === true && (checkProvineAdmin === true || env?.hideDeleteButton !== true || (hasDossierDeletePermission && row.dossierStatus?.id === 2 && row.currentTask[0]?.isFirst === 1 && row.applyMethod?.id !== 0))) && oneGateDeleteDossier === '1' && !hideDeleteButtonDossier" (click)="deleteDialog(row.id, row.code)">
                                    <mat-icon>delete_outline</mat-icon><span i18n>Xóa</span>
                                </button>
                                <button mat-menu-item class="menuAction" *ngIf="row?.enableDisplayOfSpecializedProcessing" (click)="viewSpecializedProcess(row.code)">
                                    <mat-icon>insights</mat-icon><span i18n="@@specializedProcessing">Quy trình xử lý chuyên ngành</span>
                                </button>
                                <button mat-menu-item class="menuAction" *ngIf="isShowBtnSyncDossier"  (click)="syncStatusDossier(row)">
                                    <mat-icon>sync</mat-icon><span >Đồng bộ lại trạng thái hồ sơ</span>
                                </button>
                                <ng-container *ngIf="isAdmin">
                                    <button mat-menu-item class="menuAction" *ngIf="row.dossierStatus.id != 0 || (row.dossierStatus.id == 0 && !!row.task && row.task.length != 0)" (click)="returnAccept(row.id, row.code)">
                                        <mat-icon>settings_backup_restore</mat-icon><span>Trả về chờ tiếp nhận</span>
                                    </button>
                                    <button mat-menu-item class="menuAction" *ngIf="(row.dossierStatus.id != 0 || (row.dossierStatus.id == 0 && !!row.task && row.task.length != 0)) && enableForceEndProcess" (click)="forceEndProcess(row.id, row.code)">
                                        <mat-icon>check</mat-icon><span>Kết thúc hồ sơ</span>
                                    </button>
                                    <button mat-menu-item class="menuAction" (click)="reassign(row.id, row.code, row?.currentTask, row.procedure?.id)" *ngIf="enableReassignDossier && [2, 4].includes(row.dossierStatus?.id) && !!row.previousTask && row.previousTask.length !== 0">
                                        <mat-icon>edit</mat-icon><span>Điều chỉnh lại người xử lý</span>
                                    </button>
                                    <button mat-menu-item class="menuAction" (click)="checkLogSync(row.id, row.code)"  *ngIf="enableCheckLogSyncDossier">
                                        <mat-icon>check</mat-icon><span>Kiểm tra đồng bộ</span>
                                    </button>
                                    <button mat-menu-item class="menuAction" *ngIf="isDienBien !== true && !hideDeleteButtonDossier" (click)="deleteDialog(row.id, row.code)">
                                        <mat-icon>delete_outline</mat-icon><span i18n>Xóa</span>
                                    </button>
                                    <button mat-menu-item class="menuAction" *ngIf="row.dossierStatus.id != 1 && row.requireAdditional" (click)="additionalRequirement(row.id, row.code)">
                                        <mat-icon>reply</mat-icon><span i18n>Yêu cầu bổ sung</span>
                                    </button>
                                    <button mat-menu-item class="menuAction" *ngIf="row.dossierStatus.id == 2 || row.dossierStatus.id == 4" (click)="suspenDialogs(row.id, row.code)">
                                        <mat-icon>pause_circle_outline</mat-icon><span i18n>Tạm dừng</span>
                                    </button>
                                    <button mat-menu-item class="menuAction" *ngIf="row.dossierStatus.id == 3" (click)="resumeDialog(row.id, row.code)">
                                        <mat-icon>autorenew</mat-icon><span i18n>Tiếp tục xử lý</span>
                                    </button>
                                    <button mat-menu-item class="menuAction" *ngIf="row.dossierStatus.id == 0" (click)="refuseDialog(row.id, row.code)">
                                        <mat-icon>block</mat-icon><span i18n>Từ chối</span>
                                    </button>
                                    <button mat-menu-item class="menuAction" *ngIf="row.due.length > 0 && row.due[0].timesheet.isOverDue == true" (click)="addApologyText(row.id)">
                                        <mat-icon>assignment_returned</mat-icon><span i18n>Tải xuống mẫu văn bản xin lỗi</span>
                                    </button>
                                    <button mat-menu-item class="menuAction" *ngIf="row.due.length > 0 && row.due[0].timesheet.isOverDue == true" (click)="signApologyText(row.id)">
                                        <mat-icon>note_add</mat-icon><span i18n>Thêm văn bản xin lỗi</span>
                                    </button>
                                    <button mat-menu-item class="menuAction" (click)="downloadAllFile(row.id)">
                                        <mat-icon>cloud_download</mat-icon>
                                        <span>Tải văn bản của hồ sơ</span>
                                    </button>
                                    <button mat-menu-item class="menuAction" (click)="UpdatePaymentMethod(row.id, row.paymentMethod)" *ngIf="oneGateUpdatePaymentMethod">
                                        <mat-icon>edit</mat-icon>
                                        <span>Cập nhật hình thức thanh toán</span>
                                    </button>
                                    <ng-container *ngIf="(row?.dossierStatus?.id !== 4 || row?.dossierStatus?.id !== 5) && !isDienBien">
                                    <ng-container *ngIf="this.qbhwithdrawprocess == true && this.checkDrawQBH == true; else normal">
                                      <button mat-menu-item class="menuAction" *ngIf="!hideRequestToWithdraw" (click)="withdrawQBHDialogs(row.id, row.code)" >
                                        <mat-icon>reply</mat-icon><span *ngIf="!isUserRQ;else isUserKGGTemplate">Yêu cầu rút hồ sơ</span>
                                        <ng-template #isUserKGGTemplate>
                                            <span>Rút hồ sơ theo yêu cầu</span>
                                        </ng-template>
                                      </button>
                                    </ng-container>
                                    <ng-template #normal>
                                        <button mat-menu-item class="menuAction" *ngIf="!hideRequestToWithdraw" (click)="withdrawDialogs(row.id, row.code)" >
                                            <mat-icon>reply</mat-icon><span *ngIf="!isUserRQ;else isUserKGGTemplate">Yêu cầu rút hồ sơ</span>
                                            <ng-template #isUserKGGTemplate>
                                                <span>Rút hồ sơ theo yêu cầu</span>
                                            </ng-template>
                                          </button>
                                    </ng-template>
                                </ng-container>
                                </ng-container>
                                <ng-container *ngIf="![4,5,6].includes(row?.dossierStatus?.id) && isDienBien && isOneGateOfficer">
                                  <ng-container *ngIf="[0,1].includes(row?.dossierStatus?.id ); then thenBlock else elseBlock"></ng-container>
                                  <ng-template #thenBlock>
                                    <button mat-menu-item class="menuAction" *ngIf="!hideRequestToWithdraw" (click)="withdrawDialogs(row.id, row.code,true,1)" >
                                      <mat-icon>reply</mat-icon><span *ngIf="!isUserRQ;else isUserKGGTemplate">Yêu cầu rút hồ sơ</span>
                                      <ng-template #isUserKGGTemplate>
                                        <span>Rút hồ sơ theo yêu cầu</span>
                                    </ng-template>
                                    </button>
                                  </ng-template>
                                  <ng-template #elseBlock>
                                    <ng-container *ngIf="this.qbhwithdrawprocess == true && this.checkDrawQBH == true; else normal2">
                                    <button mat-menu-item class="menuAction" *ngIf="!hideRequestToWithdraw" (click)="withdrawQBHDialogs(row.id, row.code,true)" >
                                      <mat-icon>reply</mat-icon><span *ngIf="!isUserRQ;else isUserKGGTemplate">Yêu cầu rút hồ sơ</span>
                                      <ng-template #isUserKGGTemplate>
                                        <span>Rút hồ sơ theo yêu cầu</span>
                                      </ng-template>
                                    </button>
                                    </ng-container>
                                    <ng-template #normal2>
                                        <button mat-menu-item class="menuAction" *ngIf="!hideRequestToWithdraw  && this.checkDrawQBH == false" (click)="withdrawDialogs(row.id, row.code,true)" >
                                            <mat-icon>reply</mat-icon><span *ngIf="!isUserRQ;else isUserKGGTemplate">Yêu cầu rút hồ sơ</span>
                                            <ng-template #isUserKGGTemplate>
                                              <span>Rút hồ sơ theo yêu cầu</span>
                                            </ng-template>
                                          </button>
                                    </ng-template>
                                  </ng-template>
                                </ng-container>
                            </ng-container>

                            <ng-container *ngIf="row.extendQNI?.isVbdlis == true || row.extendQNI?.isVbdlis == false">
                                <button mat-menu-item class="menuAction" (click)="dossierDetail1($event,row.id, row.procedure.id, row.task)">
                                    <mat-icon>info</mat-icon><span i18n>Chi tiết hồ sơ</span>
                                </button>
                                <button mat-menu-item class="menuAction" *ngIf="isAdmin" (click)="deleteDialog(row.id, row.code)">
                                    <mat-icon>delete_outline</mat-icon><span i18n>Xóa</span>
                                </button>
                                <button mat-flat-button class="btnPrimary" (click)="syncPostUpdateFinishDossierVBDLIS(row.code)" *ngIf="row?.extendQNI?.isVbdlis == true &&  ( row.dossierStatus?.id === 5 || row.dossierStatus?.id === 6 || row.dossierStatus?.id === 12)">
                                    <mat-icon>done_all</mat-icon>
                                    <span>Cập nhật trạng thái trả kết quả qua VBDLIS</span>
                                </button>
                                <button mat-menu-item class="menuAction" *ngIf="enableButtonUpdateVBDLIS" (click)="syncAdditinalRequestIntoVBDLIS(row.code)">
                                    <mat-icon>info</mat-icon><span>Cập nhật gia hạn/tạm dừng/ bổ sung qua VBDLIS</span>
                                </button>
                                <button mat-menu-item class="menuAction" (click)="viewProcessingVBDLIS(row.id, row.code)" *ngIf="enableViewProcessingVbdlisBtn">
                                    <mat-icon>assignment</mat-icon><span>Xem quá trình xử lý VBDLIS</span>
                                </button>
                                <button mat-menu-item class="menuAction" (click)="checkPaymentByCode(row.code)" *ngIf="showReCheckPayment == true">
                                    <mat-icon>preview</mat-icon>
                                    <span>Kiểm tra lại thanh toán payment</span>
                                </button>
                            </ng-container>
                            <ng-container *ngIf="row.extendQNI?.isIlis == true || row.extendQNI?.isIlis == false">
                                <button mat-menu-item class="menuAction" *ngIf="row.task != undefined && row.task.length > 0" (click)="viewProcess(row.id, row.code)">
                                    <mat-icon>insights</mat-icon><span i18n>Xem quy trình</span>
                                </button>
                                <button mat-menu-item class="menuAction" (click)="dossierDetail1($event,row.id, row.procedure.id, row.task)">
                                    <mat-icon>info</mat-icon><span i18n>Chi tiết hồ sơ</span>
                                </button>
                                <button mat-menu-item class="menuAction" *ngIf="isAdmin" (click)="deleteDialog(row.id, row.code)">
                                    <mat-icon>delete_outline</mat-icon><span i18n>Xóa</span>
                                </button>
                                <button mat-menu-item class="menuAction" (click)="reassign(row.id, row.code, row?.currentTask, row.procedure?.id)" *ngIf="isAdmin && enableReassignDossier && [2, 4].includes(row.dossierStatus?.id) && !!row.previousTask && row.previousTask.length !== 0">
                                    <mat-icon>edit</mat-icon><span>Điều chỉnh lại người xử lý</span>
                                </button>
                                <button mat-menu-item class="menuAction" (click)="reassign(row.id, row.code, row?.currentTask, row.procedure?.id)" *ngIf="enableReassignDossier && !isAdmin && row.canReassign && [2, 4].includes(row.dossierStatus?.id) && !!row.previousTask && row.previousTask.length !== 0">
                                    <mat-icon>edit</mat-icon><span>Điều chỉnh lại người xử lý</span>
                                </button>
                                <button mat-menu-item class="menuAction" (click)="downloadAllFile(row.id)" *ngIf="isAdmin">
                                    <mat-icon>cloud_download</mat-icon>
                                    <span>Tải văn bản của hồ sơ</span>
                                </button>
                              
                            </ng-container>
                            <ng-container *ngIf="checkNotePermistionEnable">
                                <button mat-menu-item (click)="onShowNote(row)">
                                    <mat-icon>note_alt</mat-icon>
                                    <span i18n="@@note">Ghi chú</span>
                                </button>
                            </ng-container>
                            <ng-container *ngIf="row.extendCMU?.isBhtn == true || row.extendCMU?.isBhtn == false">
                                <button mat-menu-item class="menuAction" (click)="dossierDetail1($event,row.id, row.procedure.id, row.task)">
                                    <mat-icon>info</mat-icon><span i18n>Chi tiết hồ sơ</span>
                                </button>
                                <button mat-menu-item class="menuAction" *ngIf="isAdmin" (click)="deleteDialog(row.id, row.code)">
                                    <mat-icon>delete_outline</mat-icon><span i18n>Xóa</span>
                                </button>
                            </ng-container>
                            <ng-container *ngIf="row?.enableButtonReturnResult">
                                <button mat-menu-item (click)="syncDossierLGSPHCM(row)">
                                    <mat-icon>check</mat-icon>
                                    <span>Trả kết quả chuyên ngành</span>
                                </button>
                            </ng-container>
                            <button class="menuAction" mat-menu-item (click)="downloadZipFileDossier(row)" *ngIf="downloadFileDossierRar">
                                <mat-icon>cloud_download</mat-icon>
                                <span>Tải văn bản nén của hồ sơ</span>
                            </button>
                        </mat-menu>
                    </mat-cell>
                </ng-container>

                <mat-header-row *matHeaderRowDef="displayedColumns"></mat-header-row>

                <mat-row *matRowDef="let row; columns: displayedColumns;"></mat-row>
            </table>
            <pagination-slice id="pgnx" [itemsPerPage]="size" [currentPage]="page" [totalItems]="countResult"
                [pageSizeOptions]="[].concat(pgSizeOptions)" [dataSource]="ELEMENTDATA"
                (change)="changePageOrSize($event)" [type]="paginationType">
            </pagination-slice>
            <div id="print-section" #pdfContent style="display: none;">
                <table style="width: 100%; border-collapse: collapse;">
                    <tr>
                        <th [ngStyle]="columnStyles[0]"
                            style="vertical-align: middle;text-align: center;font-size: 13px;">STT</th>
                        <th [ngStyle]="columnStyles[1]"
                            style="vertical-align: middle;text-align: center;font-size: 13px;">Số hồ sơ</th>
                        <th [ngStyle]="columnStyles[2]"
                            style="vertical-align: middle;text-align: center;font-size: 13px;">Nội dung yêu cầu giải
                            quyết</th>
                        <th [ngStyle]="columnStyles[3]"
                            style="vertical-align: middle;text-align: center;font-size: 13px;">Ngày tiếp nhận /
                            <br>Ngày hẹn trả /
                            <br>Ngày kết thúc xử lý
                        </th>
                        <th [ngStyle]="columnStyles[4]"
                            style="vertical-align: middle;text-align: center;font-size: 13px;">Chủ hồ sơ</th>
                        <th [ngStyle]="columnStyles[5]"
                            style="vertical-align: middle;text-align: center;font-size: 13px;">Người nộp hồ sơ</th>
                        <th [ngStyle]="columnStyles[6]"
                            style="vertical-align: middle;text-align: center;font-size: 13px;">{{ isAddColumDataExport ?
                            'Số điện thoại người nộp' : 'Số điện thoại '}}</th>
                        <th [ngStyle]="columnStyles[7]"
                            style="vertical-align: middle;text-align: center;font-size: 13px;">Trạng thái hồ sơ</th>
                        <th *ngIf="isAddColumDataExport" [ngStyle]="columnStyles[8]"
                            style="vertical-align: middle;text-align: center;font-size: 13px;">Số điện thoại chủ hồ sơ
                        </th>
                        <th *ngIf="isAddColumDataExport" [ngStyle]="columnStyles[9]"
                            style="vertical-align: middle;text-align: center;font-size: 13px;">Địa chỉ chủ hồ sơ</th>
                        <th *ngIf="isAddColumDataExport" [ngStyle]="columnStyles[10]"
                            style="vertical-align: middle;text-align: center;font-size: 13px;">Địa chỉ người nộp</th>
                    </tr>
                    <tbody id='tbody'>

                    </tbody>


                </table>
            </div>
        </div>
    </ng-template>
    </div>
</div>