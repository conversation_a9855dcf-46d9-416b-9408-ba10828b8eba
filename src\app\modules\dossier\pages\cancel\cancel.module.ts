import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { CancelRoutingModule } from './cancel-routing.module';
import { SharedModule } from 'src/app/shared/shared.module';

import { CancelComponent } from './cancel.component';
import {CancleDetailComponent} from 'modules/dossier/pages/cancel/cancle-detail/cancle-detail.component';
import { RestoreDossierComponent } from './dialogs/restore-dossier/restore-dossier.component';
import { ReturnCancelProcessingDossierComponent } from './dialogs/return-cancel-processing-dossier/return-cancel-processing-dossier';


@NgModule({
  declarations: [CancelComponent, CancleDetailComponent, RestoreDossierComponent, ReturnCancelProcessingDossierComponent],
  imports: [
    CommonModule,
    CancelRoutingModule,
    SharedModule
  ]
})
export class CancelModule { }
