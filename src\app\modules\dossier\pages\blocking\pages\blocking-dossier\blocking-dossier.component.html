<h2><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON> hồ sơ ngăn chặn</h2>
<div fxLayout="row" fxLayoutAlign="center">
    <div class="frm_searchbar" fxFlex="grow">
        <form [formGroup]="searchForm" class="searchForm" (submit)="filter()">
            <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="row">
                <mat-form-field appearance="outline" fxFlex.gt-sm="82" fxFlex='79'>
                    <mat-label i18n>Nhập từ khoá</mat-label>
                    <input matInput formControlName="keyword">
                </mat-form-field>
                <div fxFlex='1'></div>
                <button mat-flat-button fxFlex.gt-sm="17" fxFlex='20' class="searchBtn" type="submit">
                    <mat-icon>search</mat-icon> <span i18n>Tìm kiếm</span>
                </button>
                <div fxFlex='1'></div>
                <button mat-flat-button fxFlex.gt-sm="17" fxFlex='20' class="searchBtn" type="button" (click)="exportToExcelAll()">
                    <mat-icon>cloud_download</mat-icon> <span> Xuất dữ liệu</span>
                </button>
            </div>
        </form>
    </div>
</div>
<div fxLayout="row" fxLayoutAlign="center">
    <div class="frm_main sector" fxFlex="grow">
    <mat-tab-group>
        <mat-tab id="forms" label="Danh sách hồ sơ ngăn chặn">
          <button mat-flat-button class="btn_add" (click)="addFormDialog()">
            <mat-icon>add</mat-icon>
            <span i18n>Thêm mới</span>
          </button>

 <div [hidden]="!adapterError"><span class="error" i18n>Lỗi máy chủ cổng Dịch vụ công, vui lòng quay lại sau.</span></div>

 <div class="frm_tbl_sector sector">
    <mat-table
           [dataSource]="dataSource">
      <ng-container matColumnDef="stt">
        <mat-header-cell *matHeaderCellDef i18n>STT</mat-header-cell>
        <mat-cell *matCellDef="let row" i18n-data-label data-label="STT"> {{row.stt}} </mat-cell>
      </ng-container>
      <ng-container matColumnDef="certificateId">
        <mat-header-cell *matHeaderCellDef>Số bìa đỏ</mat-header-cell>
        <mat-cell *matCellDef="let row" data-label="Số bìa đỏ">
          <span>
            {{row?.certificateId}}
            <span *ngIf="!row?.certificateId">(Không có dữ liệu)</span>
          </span>
        </mat-cell>
      </ng-container>
      <ng-container matColumnDef="ownerFullname">
        <mat-header-cell *matHeaderCellDef>Tên người sử dụng</mat-header-cell>
        <mat-cell *matCellDef="let row" data-label="Tên người sử dụng">
          <span>
            {{row?.ownerFullname}}
            <span *ngIf="!row?.ownerFullname">(Không có dữ liệu)</span>
          </span>
        </mat-cell>
      </ng-container>
      <ng-container matColumnDef="slotId">
        <mat-header-cell *matHeaderCellDef>Thửa đất</mat-header-cell>
        <mat-cell *matCellDef="let row" data-label="Thửa đất">
            <span>
                {{row?.slotId}}
                <span *ngIf="!row?.slotId">(Không có dữ liệu)</span>
            </span>
        </mat-cell>
      </ng-container>
      <ng-container matColumnDef="blockDate">
        <mat-header-cell *matHeaderCellDef>Ngày ký</mat-header-cell>
        <mat-cell *matCellDef="let row" data-label="Ngày ký">
          <span>{{row.blockDate|date:'dd/MM/yyyy'}}</span>
        </mat-cell>
      </ng-container>
      <ng-container matColumnDef="content">
        <mat-header-cell *matHeaderCellDef>Nội dung</mat-header-cell>
        <mat-cell *matCellDef="let row" data-label="Nội dung">
          <span class="ellipsis">
            <span>{{row?.content}}</span>
            <span *ngIf="!row?.content">(Không có dữ liệu)</span>
          </span>
        </mat-cell>
      </ng-container>
      <ng-container matColumnDef="action">
        <mat-header-cell *matHeaderCellDef i18n>Thao tác</mat-header-cell>
        <mat-cell *matCellDef="let row" i18n-data-label data-label="Thao tác">
          <button mat-icon-button [matMenuTriggerFor]="actionMenu">
            <mat-icon>more_horiz</mat-icon>
          </button>
          <mat-menu #actionMenu="matMenu" xPosition="before">
            <button mat-menu-item class="menuAction" (click)="updateFormDialog(row.id)">
              <mat-icon>edit</mat-icon>
              <span i18n>Cập nhật</span>
            </button>
            <button mat-menu-item class="menuAction" (click)="deleteFormDialog(row.id, row.title)">
              <mat-icon>delete_outline</mat-icon>
              <span i18n>Xoá</span>
            </button>
          </mat-menu>
        </mat-cell>
      </ng-container>

      <mat-header-row *matHeaderRowDef="displayedColumns"></mat-header-row>
      <mat-row *matRowDef="let row; columns: displayedColumns;"></mat-row>
    </mat-table>
    <div class="frm_Pagination">
      <ul class="temp_Arr">
        <li *ngFor="let item of ELEMENTDATA  | paginate: {itemsPerPage: size, currentPage: pageIndex, totalItems: countResult, id: 'pgnx'}"></li>
      </ul>
      <div class="pageSize">
        <span i18n>Hiển thị </span>
        <mat-form-field appearance="outline">
          <mat-select [(value)]="size" (valueChange)="paginate(pageIndex, 1)">
            <mat-option *ngFor='let opt of pgSizeOptions;' [value]="opt">{{opt}}</mat-option>
          </mat-select>
        </mat-form-field>
        <span><span i18n>trên</span> {{countResult}} <span i18n>bản ghi</span></span>
      </div>
      <div class="control">
        <pagination-controls id="pgnx" (pageChange)="pageIndex = $event; paginate(pageIndex, 0)" responsive="true"
                             previousLabel="" nextLabel="">
        </pagination-controls>
      </div>
    </div>
  </div>

</mat-tab>
</mat-tab-group>
</div>
</div>
