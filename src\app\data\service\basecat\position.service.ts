import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { ApiProviderService } from 'src/app/core/service/api-provider.service';
import { EnvService } from 'src/app/core/service/env.service';

@Injectable({
  providedIn: 'root'
})
export class PositionService {

  private basecat = this.apiProviderService.getUrl('digo', 'basecat');

  constructor(
    private http: HttpClient,
    private envService: EnvService,
    private apiProviderService: ApiProviderService,
  ) { }


  public getPositionList(searchStr): Observable<any> {
    return this.http.get(this.basecat + '/position' + searchStr);
  }
}
