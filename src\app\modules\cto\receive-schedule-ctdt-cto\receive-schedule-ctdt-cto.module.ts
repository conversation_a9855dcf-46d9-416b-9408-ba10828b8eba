import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReceiveScheduleCtdtRoutingModule } from './receive-schedule-ctdt-routing-cto.module';
import { SharedModule } from 'src/app/shared/shared.module';
import { FormioModule } from 'angular-formio';
import { ListScheduleCtdtComponent } from './pages/list-schedule-ctdt-cto/list-schedule-ctdt-cto.component';
import { ConfirmCancelScheduleComponent } from './pages/confirm-cancel-schedule-cto/confirm-cancel-schedule-cto.component';
import { ReceivingCtdtComponent } from './pages/receiving-cto/receiving-cto.component';

@NgModule({
  declarations: [
    ListScheduleCtdtComponent,
    ConfirmCancelScheduleComponent,
    ReceivingCtdtComponent,

  ],
  imports: [
    CommonModule,
    ReceiveScheduleCtdtRoutingModule,
    SharedModule,
    FormioModule
  ]
})
export class ReceiveScheduleCtdtModule { }
