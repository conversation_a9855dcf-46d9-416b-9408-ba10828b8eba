import { Component, OnInit, Inject, AfterViewInit, LOCALE_ID, ViewChild, SystemJsNgModuleLoader } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { FormGroup, Validators, FormControl } from '@angular/forms';
import { Observable } from 'rxjs';
import { EnvService } from 'src/app/core/service/env.service';
import { FormioComponent } from 'angular-formio';
import { SnackbarService } from 'src/app/data/service/snackbar/snackbar.service';
import { HomeService } from 'src/app/data/service/home/<USER>';
import { DossierService } from 'src/app/data/service/dossier/dossier.service';
import { ProcedureService } from 'src/app/data/service/procedure/procedure.service';
import { PdfViewerComponent, PdfViewerDialog } from 'src/app/shared/components/pdf-viewer/pdf-viewer.component';
import { MatDialog } from '@angular/material/dialog';
import { KeycloakService } from 'keycloak-angular';
import jwt_decode from 'jwt-decode';
import { DeploymentService } from 'src/app/data/service/deployment.service';
import { AdapterService } from 'src/app/data/service/adapter/adapter.service';
import { ConfigService } from 'src/app/data/service/dossier/config.service';
import * as VGCA from 'src/assets/js/vgcaplugin.js';
import { UploadService } from 'src/app/data/service/upload/upload.service';
import { LogmanService } from 'src/app/data/service/logman/logman.service';
import { DigitalSignatureService } from 'src/app/data/service/digital-signature/digital-signature.service';
import * as tUtils from 'src/app/data/service/thoai.service';

declare var vgca_sign_approved: any;
declare var vgca_sign_copy: any;

export interface Form {
  id: string;
  code: string;
  name: string;
  status: number;
}

@Component({
  selector: 'app-export-eform',
  templateUrl: './export-eform.component.html',
  styleUrls: ['./export-eform.component.scss']
})
export class ExportEFormComponent implements OnInit {
  config = this.envService.getConfig();

  addForm = new FormGroup({
    form: new FormControl(''),
    type: new FormControl(''),
    quantity: new FormControl(1)
  });
  formFile: Blob;
  formName = '';
  isLoading = false;
  listForm: Form[] = [];
  listType = [];
  procedureId: string;
  filteredOptions: Observable<Form[]>;
  selectedTypeName = '';
  timeout: any = null;
  eform: any;
  applicantEForm = {
    id: '',
    component: null,
    data: null,
    renderOptions: {
      // language: this.localeId,
      // i18n: {
      //   en: {
      //     'Họ và tên': 'Full name',
      //     'Cơ quan/ tổ chức': 'Agency/ organization',
      //     'Số CMND': 'ID number',
      //     'Ngày cấp CMND': 'Date of issue of ID card',
      //     'Nơi cấp CMND': 'Place of issue of ID card',
      //     'Số điện thoại': 'Phone number',
      //     'Ngày sinh': 'Birthday',
      //     'Quốc gia': 'Nation',
      //     'Giới tính': 'Gender',
      //     'Tỉnh/TP': 'Province / City',
      //     'Quận/huyện': 'District',
      //     'Phường/xã': 'Wards',
      //     'Địa chỉ chi tiết': 'Detailed address',
      //     'Nhập họ tên': 'Enter your full name',
      //     'Nhập cơ quan/ tổ chức': 'Enter agency / organization',
      //     'Nhập số CMND': 'Enter ID number',
      //     'Nhập ngày cấp CMND': 'Enter date of issue of ID card',
      //     'Chọn nơi cấp CMND': 'Choose where to issue ID card',
      //     'Nhập số điện thoại': 'Enter phone number',
      //     'Chọn giới tính': 'Choose gender',
      //     'Chọn quốc gia': 'Choose country',
      //     'Chọn Tỉnh/TP': 'Select Province / City',
      //     'Chọn Quận/huyện': 'Select District / District',
      //     'Chọn Phường/xã': 'Choose ward / commune',
      //     'Nhập địa chỉ chi tiết': 'Enter detailed address'
      //   },
      //   vi: {
      //     'Full name is required': 'Tên đầy đủ là bắt buộc',
      //     required: 'Bắt buộc nhập!',
      //     match: 'Vui lòng điền chính xác định dạng!'
      //   }
      // }
    }
  };
  selectedLang = '';
  dem = 0;
  dossierId = null;
  objectData = null;
  changeCheckbox = false;
  @ViewChild('formEFormComp') formEFormComp: FormioComponent;
  env = this.deploymentService.getAppDeployment()?.env;
  isEditSignTokenName = false;
  dataFormAuto: any;

  isVgcaSignCopy = this.deploymentService?.env?.OS_HPG?.vgca_sign_copy;
  vgcaSignLabel = this.deploymentService?.env?.OS_HPG?.vgca_sign_copy_label;

  enableExportXMLFormEform = this.deploymentService?.env?.formEform?.enableExportXML ? this.deploymentService?.env?.formEform?.enableExportXML   : false;
  enableExportJSONFormEform = this.deploymentService?.env?.formEform?.enableExportJSON ? this.deploymentService?.env?.formEform?.enableExportJSON   : false;
  
  // IGATESUPP-110576
  showDocumentCodeToEform = false;
  enableShowDocumentCodeToEform = this.deploymentService.getAppDeployment()?.showDocumentCodeToEform?.enable ? (this.deploymentService.getAppDeployment().showDocumentCodeToEform.enable == 1 ? true : false) : false;
  listAgencyShowDocumentCodeToEform = this.deploymentService.getAppDeployment()?.showDocumentCodeToEform?.agencyIds ? this.deploymentService.getAppDeployment().showDocumentCodeToEform.agencyIds : [];
  constructor(
    public dialogRef: MatDialogRef<ExportEFormComponent>,
    @Inject(MAT_DIALOG_DATA) public data: ExportEFormDialogModel,
    private envService: EnvService,
    private dossierService: DossierService,
    private procedureService: ProcedureService,
    private snackbarService: SnackbarService,
    private homeService: HomeService,
    @Inject(LOCALE_ID) protected localeId: string,
    private dialog: MatDialog,
    private keycloakService: KeycloakService,
    private deploymentService: DeploymentService,
    private adapterService: AdapterService,
    private configService: ConfigService,
    private uploadService: UploadService,
    private logmanService: LogmanService,
    private digitalSignatureService: DigitalSignatureService,
  ) {
    this.procedureId = data.procedureId;
    //debugger;
    this.applicantEForm.id = data.valueEform.eForm.id;
    this.eform = data.valueEform;
    this.formName = data.formName;
    this.dossierId = data.dossierId;
    this.digitalSignature = data.digitalSignaturePerm;
    this.dataFormAuto = data.eformData;
    this.changeCheckbox = data.valueEform?.changeCheckbox;

  }

  // eForm
  digitalSignatureEnable: boolean;
  digitalSignature = {
    SmartCA:false,
    VGCA:false,
    VNPTCA:false,
    VNPTSim:false,
    NEAC:false
  }

  tempObj : any = {};

  ngOnInit(): void {
    let userAgency = JSON.parse(localStorage.getItem('userAgency'));
    this.selectedLang = localStorage.getItem('language');
    const searchString = '?page=0&size=10&spec=page&sort=name,asc&status=1';
    if (!!this.dataFormAuto){
      this.applicantEForm.data = {
        data: this.dataFormAuto
      };
    }
    else{
      this.applicantEForm.data = {
        data: {}
      };
    }
    if(this.enableShowDocumentCodeToEform  && this.listAgencyShowDocumentCodeToEform.filter( item => item == userAgency.id || item == userAgency?.parent?.id  || item == userAgency?.ancestors?.id ).length > 0){
      this.showDocumentCodeToEform = true;
    }
    console.log(this.applicantEForm)
    this.getFormIo();
    this.getFormFile();
    this.getData();
    // this.getListForm(searchString);
    // this.getListTagDocumentType(this.config.documentTypeCategoryId, 0, 50, 'order,asc');

    // Check if digital signature supported
    if(this.hasDigitalSignaturePermission()){
      console.log("hasDigitalSignaturePermission");
      this.digitalSignatureEnable = true;
      if (this.digitalSignature != null && this.digitalSignature != undefined){
        // ignore
      }else{
        this.digitalSignature = {
          SmartCA:false,
          VGCA:false,
          VNPTCA:false,
          VNPTSim:false,
          NEAC:false
        }
        this.digitalSignature.SmartCA = this.env?.digitalSignature?.SmartCA == 1;
        this.digitalSignature.VGCA = this.env?.digitalSignature?.VGCA == 1;
        this.digitalSignature.VNPTCA = this.env?.digitalSignature?.VNPTCA == 1;
        this.digitalSignature.VNPTSim = this.env?.digitalSignature?.VNPTSim == 1;
        this.digitalSignature.NEAC = this.env?.digitalSignature?.NEAC == 1;
        this.isEditSignTokenName = this.env?.OS_KTM?.isEditSignTokenName == true ? true : false ;
      }
      console.log("this.digitalSignature",this.digitalSignature);
    }
  }

  getData(){
    if (this.dossierId != null){
      this.dossierService.getDossierFormEformData('?dossier-id=' + this.dossierId + '&form-id=' + this.eform.id).subscribe(data => {
        if (data.length > 0){
          this.applicantEForm.data = {
            data: data[0].data
          };
        }
      });
    }
  }

  getFormIo(){
    this.dossierService.getFormIoData(this.applicantEForm.id).subscribe(applicantEForm => {
      this.applicantEForm.component = applicantEForm;
      // if (!!this.dataFormAuto){
      //   this.applicantEForm.data = {
      //     data: this.dataFormAuto
      //   };
      // }
      // else{
      //   this.applicantEForm.data = {
      //     data: {}
      //   };
      // }
    }, err => {
      if (this.dem < 3){
        this.getFormIo();
        this.dem++;
      }
    });
  }
  
  async changeFile(type){
    const applicantEFormStatus = this.formEFormComp.formio.checkValidity(null, true);
    // const eFormStatus = this.eFormComp.formio.checkValidity(null, true);

    if (applicantEFormStatus === false) {
      const viMsg = 'Vui lòng điền đầy đủ thông tin!';
      const enMsg = 'Please fill full information!';
      this.homeService.error(viMsg, enMsg);
      return;
    }else {
      let value = this.applicantEForm.data.data;
      const component = JSON.parse(JSON.stringify(this.applicantEForm.component.components));
      const newComponent: Array<any> = [];
      // tslint:disable-next-line:prefer-for-of
      for (let i = 0; i < component.length; i++){
        if (component[i].components) {
          // tslint:disable-next-line:prefer-for-of
          for (let k = 0; k < component[i].components.length; k++){
            if (component[i].components[k].data){
              delete component[i].components[k].data;
            }
          }
        }
        const item = {
          label: component[i].label,
          key:  component[i].key,
          components: component[i].components
        };
        newComponent.push(item);
      }
      this.isLoading = true;
      this.objectData = {
        formId: this.eform.id,
        data: this.applicantEForm.data.data
      };
      // tslint:disable-next-line:max-line-length
      if(this.showDocumentCodeToEform){
        let dossierDetail = await this.dossierService.getDossierDetail(this.dossierId).toPromise();
        if(tUtils.nonNull(dossierDetail, 'extendHCM') && dossierDetail?.extendHCM?.procedureAdministrationWithAssignedCode){
          let procedureAdministrationWithAssignedCode = dossierDetail.extendHCM.procedureAdministrationWithAssignedCode;
          if(procedureAdministrationWithAssignedCode.length > 0){

            let number = procedureAdministrationWithAssignedCode[procedureAdministrationWithAssignedCode.length - 1]?.code;
            value["extendHCMProcedureAdministrationWithAssignedCodeAssignedNumber"] = number;
            const item = {"label":"Số văn bản phát hành:","key": "extendHCMProcedureAdministrationWithAssignedCodeAssignedNumber"};
            newComponent.push(item);
          }
          
        }
      }

      if(type === 'json' || type === 'xml'){
        let fileName = this.eform.file.filename;
        const index = fileName.lastIndexOf('.');
        fileName = fileName.substring(0, index);
        fileName = fileName + '.' + type;
        this.procedureService.getExportFormEform(fileName, JSON.stringify(value), type).subscribe(data => {
          const dataType = data.type;
          const binaryData = [];
          binaryData.push(data);
          // const blob = new Blob(binaryData, { type: dataType });
          const downloadLink = document.createElement('a');
          downloadLink.href = window.URL.createObjectURL(new Blob(binaryData, { type: dataType }));
          downloadLink.setAttribute('download', fileName);
          document.body.appendChild(downloadLink);
          downloadLink.click();
          this.isLoading = false;
          this.dialogRef.close(this.objectData);
        }, err => {
          this.isLoading = false;
        });
      } else {
        this.procedureService.getChangeFileFormEform(this.formFile, JSON.stringify(value), JSON.stringify(newComponent), type,this.changeCheckbox).subscribe(data => {
          const dataType = data.type;
          const binaryData = [];
          binaryData.push(data);
          const blob = new Blob(binaryData, { type: dataType });
          // this.formFile = blob;
          const downloadLink = document.createElement('a');
          downloadLink.href = window.URL.createObjectURL(new Blob(binaryData, { type: dataType }));
          let fileName = this.eform.file.filename;
          if (type === 'pdf'){
            const index = fileName.lastIndexOf('.');
            fileName = fileName.substring(0, index);
            fileName = fileName + '.pdf';
          }
          downloadLink.setAttribute('download', fileName);
          document.body.appendChild(downloadLink);
          downloadLink.click();
          this.isLoading = false;
          this.dialogRef.close(this.objectData);
        }, err => {
          this.isLoading = false;
        });
      }
    }
  }

  getFormFile(){
    this.procedureService.downloadFile(this.eform.file.id, this.procedureId).subscribe(data => {
      const dataType = data.type;
      const binaryData = [];
      binaryData.push(data);
      const blob = new Blob(binaryData, { type: dataType });
      this.formFile = blob;
      // const downloadLink = document.createElement('a');
      // downloadLink.href = window.URL.createObjectURL(new Blob(binaryData, { type: dataType }));
      // downloadLink.setAttribute('download', filename);
      // document.body.appendChild(downloadLink);
      // downloadLink.click();
    }, err => {
      const msgObj = {
        vi: 'Không tìm thấy file!',
        en: 'File not found!'
      };
      this.snackbarService.openSnackBar( 0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
      console.log(err);
      this.dialogRef.close(false);
    });
  }
  onDismiss(): void {
    this.dialogRef.close(false);
  }

  onChangeForm(event) {
    // if (event.data !== undefined && event?.data?.isOwnerDossier !== undefined && event?.changed?.component?.key === 'isOwnerDossier') {
    //   if (event.data.isOwnerDossier === true && this.isCalledOwnerDossier === false) {
    //     this.onChangeOwnerDossier(true);
    //   } else if (event.data.isOwnerDossier === false) {
    //     this.onChangeOwnerDossier(false);
    //   }
    // }
  }

  displayFn(form: Form): string {
    return form && form.name ? form.name : '';
  }

  private _filter(name: string): Form[] {
    const filterValue = name.toLowerCase();
    return this.listForm.filter(option => option.name.toLowerCase().includes(filterValue));
  }

  setSelectedType(name) {
    this.selectedTypeName = name;
  }

  openPdfDigitalSignature(dsType?:number){
    // dsType => 1: ký số sim, default: smart-ca
    const applicantEFormStatus = this.formEFormComp.formio.checkValidity(null, true);
    if (applicantEFormStatus === false) {
      const viMsg = 'Vui lòng điền đầy đủ thông tin!';
      const enMsg = 'Please fill full information!';
      this.homeService.error(viMsg, enMsg);
      return;
    }else {
      const value = this.applicantEForm.data.data;
      const component = JSON.parse(JSON.stringify(this.applicantEForm.component.components));
      const newComponent: Array<any> = [];
      for (let i = 0; i < component.length; i++){
        if (component[i].components) {
          for (let k = 0; k < component[i].components.length; k++){
            if (component[i].components[k].data){
              delete component[i].components[k].data;
            }
          }
        }
        const item = {
          label: component[i].label,
          key:  component[i].key,
          components: component[i].components
        };
        newComponent.push(item);
      }
      this.isLoading = true;
      this.objectData = {
        formId: this.eform.id,
        data: this.applicantEForm.data.data
      };
      this.procedureService.getChangeFileFormEform(this.formFile, JSON.stringify(value), JSON.stringify(newComponent), "pdf",this.changeCheckbox).subscribe(async data => {
        this.isLoading = false;
        data.name = this.formName + ".pdf";
        const accountId = await this.getAccountId();
        const files = [];
        files.push(data);
        this.procedureService.uploadMultiFile(files,accountId).subscribe(idFile => {
          const dialogData = new PdfViewerDialog(idFile[0].id, data.name, this.dossierId, dsType);
          const dialogRef = this.dialog.open(PdfViewerComponent, {
            maxWidth: '100%',
            width: '100%',
            height: '100%',
            maxHeight: '100%',
            data: dialogData,
            disableClose: false,
            panelClass: 'custom-dialog-container'
          });
          dialogRef.afterClosed().subscribe( async rs => {
            if(rs){
              this.logmanService.postSignHistory(rs.id,rs.name).subscribe();
              this.dialogRef.close({
                type: 1,
                file:{
                  id: rs.id,
                  filename: rs.name,
                  size: rs.size
                }
              });
            }
          });
        });
      }, err => {
        this.isLoading = false;
      });
    }
  }

  async getAccountId(): Promise<String>{
    const token = await this.keycloakService.getToken().finally();
    const decodedToken: any = jwt_decode(token);
    return decodedToken.account_id;
  }

  checkSmartCAAllowed(): boolean{
    let check = true;
    // Check config
    if (this.env?.digitalSignature?.SmartCA != 1){
      return false;
    }

    // Check process config
    return check;
  }

  hasDigitalSignaturePermission(): boolean{
    // Check permission
    const token = localStorage.getItem('userToken');
    const decodedToken: any = jwt_decode(token);
    for (const iterator of decodedToken.permissions) {
      if(iterator.permission.code == 'integrateDigitalSignature'){
        return true;
      }
    }
    return false;
  }

  openVGCAPlugin(){
    const applicantEFormStatus = this.formEFormComp.formio.checkValidity(null, true);
    if (applicantEFormStatus === false) {
      const viMsg = 'Vui lòng điền đầy đủ thông tin!';
      const enMsg = 'Please fill full information!';
      this.homeService.error(viMsg, enMsg);
      return;
    }else {
      const value = this.applicantEForm.data.data;
      const component = JSON.parse(JSON.stringify(this.applicantEForm.component.components));
      const newComponent: Array<any> = [];
      for (let i = 0; i < component.length; i++){
        if (component[i].components) {
          for (let k = 0; k < component[i].components.length; k++){
            if (component[i].components[k].data){
              delete component[i].components[k].data;
            }
          }
        }
        const item = {
          label: component[i].label,
          key:  component[i].key,
          components: component[i].components
        };
        newComponent.push(item);
      }
      this.isLoading = true;
      this.objectData = {
        formId: this.eform.id,
        data: this.applicantEForm.data.data
      };
      this.procedureService.getChangeFileFormEform(this.formFile, JSON.stringify(value), JSON.stringify(newComponent), "pdf",this.changeCheckbox).subscribe(async data => {
        this.isLoading = false;
        data.name = this.formName + ".pdf";
        const accountId = await this.getAccountId();
        const files = [];
        files.push(data);
        this.procedureService.uploadMultiFile(files,accountId).subscribe( async idFile => {

          this.tempObj.fileId = idFile[0].id;
          this.tempObj.filename = idFile[0].filename;
          this.tempObj.size = idFile[0].size;

          let file = await this.configService.downloadFile(idFile[0].id, this.procedureId).toPromise();
          var prms = {};
          prms["FileUploadHandler"] = this.adapterService.getVGCAAdapterCallBackUrl(idFile[0].id,this.tempObj.filename,accountId);
          prms["SessionId"] = "";
          // Create a public link
          const formData: FormData = new FormData();
          file.name = "example.pdf";
          formData.append('file', file, file.name);
          const fileResponse = await  this.uploadService.uploadFile(formData).toPromise();
          this.tempObj.tempFileId = fileResponse.id;
          const publicFileUrl = `${this.uploadService.publicFileUrl}/${fileResponse.id}.pdf`;
          prms["FileName"] = publicFileUrl;
          var json_prms = JSON.stringify(prms);
          vgca_sign_approved(json_prms, this.SignFileCallBack);
        });
      }, err => {
        this.isLoading = false;
      });
    }
  }

  openVGCApluginCopy(){
    const applicantEFormStatus = this.formEFormComp.formio.checkValidity(null, true);
    if (applicantEFormStatus === false) {
      const viMsg = 'Vui lòng điền đầy đủ thông tin!';
      const enMsg = 'Please fill full information!';
      this.homeService.error(viMsg, enMsg);
      return;
    }else {
      const value = this.applicantEForm.data.data;
      const component = JSON.parse(JSON.stringify(this.applicantEForm.component.components));
      const newComponent: Array<any> = [];
      for (let i = 0; i < component.length; i++){
        if (component[i].components) {
          for (let k = 0; k < component[i].components.length; k++){
            if (component[i].components[k].data){
              delete component[i].components[k].data;
            }
          }
        }
        const item = {
          label: component[i].label,
          key:  component[i].key,
          components: component[i].components
        };
        newComponent.push(item);
      }
      this.isLoading = true;
      this.objectData = {
        formId: this.eform.id,
        data: this.applicantEForm.data.data
      };
      this.procedureService.getChangeFileFormEform(this.formFile, JSON.stringify(value), JSON.stringify(newComponent), "pdf",this.changeCheckbox).subscribe(async data => {
        this.isLoading = false;
        data.name = this.formName + ".pdf";
        const accountId = await this.getAccountId();
        const files = [];
        files.push(data);
        this.procedureService.uploadMultiFile(files,accountId).subscribe( async idFile => {

          this.tempObj.fileId = idFile[0].id;
          this.tempObj.filename = idFile[0].filename;
          this.tempObj.size = idFile[0].size;

          let file = await this.configService.downloadFile(idFile[0].id, this.procedureId).toPromise();
          var prms = {};
          prms["FileUploadHandler"] = this.adapterService.getVGCAAdapterCallBackUrl(idFile[0].id,this.tempObj.filename,accountId);
          prms["SessionId"] = "";
          // Create a public link
          const formData: FormData = new FormData();
          file.name = "example.pdf";
          formData.append('file', file, file.name);
          const fileResponse = await  this.uploadService.uploadFile(formData).toPromise();
          this.tempObj.tempFileId = fileResponse.id;
          const publicFileUrl = `${this.uploadService.publicFileUrl}/${fileResponse.id}.pdf`;
          prms["FileName"] = publicFileUrl;
          var json_prms = JSON.stringify(prms);
          vgca_sign_copy(json_prms, this.SignFileCallBack);
        });
      }, err => {
        this.isLoading = false;
      });
    }
  }

  SignFileCallBack = async rv => {
    var received_msg = JSON.parse(rv);
    if(this.tempObj.tempFileId){
      this.uploadService.deleteFile(this.tempObj.tempFileId).subscribe();
    }
    switch(received_msg.Status){
      case (14):{
        // Cancel :: do nothing
        const msgObj = {
          vi: 'Đã hủy ký số!',
          en: 'Cancel sign document!'
        };
        this.snackbarService.openSnackBar(2, msgObj[this.selectedLang], '', 'warning_notification', this.config.expiredTime);
        break;
      }
      case true: {
        // Sign file successfully
        break;
      }
      case false: {
        // Failed to sign file
        const msgObj = {
          vi: 'Ký số thất bại!',
          en: 'Signing failed!'
        };
        this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
        break;
      }
      case 0: {
        if (received_msg.FileServer === ''){
          const msgObj = {
            vi: 'Đã hủy ký số!',
            en: 'Cancel sign document!'
          };
          this.snackbarService.openSnackBar(2, msgObj[this.selectedLang], '', 'warning_notification', this.config.expiredTime);
          break;
        }
        // Sign file successfully
        const newFilename = this.changeFilenameOfSignedFile(this.tempObj.filename);
        this.logmanService.postSignHistory(received_msg.FileServer,newFilename).subscribe();
        this.dialogRef.close({
          type: 1,
          file:{
            id: received_msg.FileServer,
            filename: newFilename,
            size: this.tempObj.size
          }
        });
        break;
      }
      default: {
        // Error
        console.log("error: ",received_msg.Message);
        const msgObj = {
          vi: 'Ký số thất bại!',
          en: 'Signing failed!'
        };
        this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
      }
    }
    return received_msg;
  }

  changeFilenameOfSignedFile(fullFilename:string):string{
    const signalStr = "_signed";
    const index = fullFilename.lastIndexOf(".");
    const extention = fullFilename.substring(index+1);
    const filename = fullFilename.substring(0,index);
    if(filename.length < 8){
      return `${filename}${signalStr}.${extention}`;
    }
    if(filename.slice(-7) != signalStr){
      return `${filename}${signalStr}.${extention}`;
    }
    return fullFilename;
  }

  openVnptCaPlugin(){
    const applicantEFormStatus = this.formEFormComp.formio.checkValidity(null, true);
    if (applicantEFormStatus === false) {
      const viMsg = 'Vui lòng điền đầy đủ thông tin!';
      const enMsg = 'Please fill full information!';
      this.homeService.error(viMsg, enMsg);
      return;
    }else {
      const value = this.applicantEForm.data.data;
      const component = JSON.parse(JSON.stringify(this.applicantEForm.component.components));
      const newComponent: Array<any> = [];
      for (let i = 0; i < component.length; i++){
        if (component[i].components) {
          for (let k = 0; k < component[i].components.length; k++){
            if (component[i].components[k].data){
              delete component[i].components[k].data;
            }
          }
        }
        const item = {
          label: component[i].label,
          key:  component[i].key,
          components: component[i].components
        };
        newComponent.push(item);
      }
      this.isLoading = true;
      this.objectData = {
        formId: this.eform.id,
        data: this.applicantEForm.data.data
      };
      this.procedureService.getChangeFileFormEform(this.formFile, JSON.stringify(value), JSON.stringify(newComponent), "pdf",this.changeCheckbox).subscribe(async data => {
        this.isLoading = false;
        data.name = this.formName + ".pdf";
        const accountId = await this.getAccountId();
        const files = [];
        files.push(data);
        this.procedureService.uploadMultiFile(files,accountId).subscribe(async idFile => {
          const result = await this.digitalSignatureService.signWithVNPTCA(idFile[0].id, data.name, this.dossierId);
          if(result.status == 1){
            this.dialogRef.close({
              type: 1,
              file:{
                id: result.data.fileId,
                filename: result.data.filename,
                size: result.data.size
              }
            });
          }
        });
      }, err => {
        this.isLoading = false;
      });
    }
  }
  
  openNEAC(){
    let dsType = 5;
    const applicantEFormStatus = this.formEFormComp.formio.checkValidity(null, true);
    if (applicantEFormStatus === false) {
      const viMsg = 'Vui lòng điền đầy đủ thông tin!';
      const enMsg = 'Please fill full information!';
      this.homeService.error(viMsg, enMsg);
      return;
    }else {
      const value = this.applicantEForm.data.data;
      const component = JSON.parse(JSON.stringify(this.applicantEForm.component.components));
      const newComponent: Array<any> = [];
      for (let i = 0; i < component.length; i++){
        if (component[i].components) {
          for (let k = 0; k < component[i].components.length; k++){
            if (component[i].components[k].data){
              delete component[i].components[k].data;
            }
          }
        }
        const item = {
          label: component[i].label,
          key:  component[i].key,
          components: component[i].components
        };
        newComponent.push(item);
      }
      this.isLoading = true;
      this.objectData = {
        formId: this.eform.id,
        data: this.applicantEForm.data.data
      };
      this.procedureService.getChangeFileFormEform(this.formFile, JSON.stringify(value), JSON.stringify(newComponent), "pdf",this.changeCheckbox).subscribe(async data => {
        this.isLoading = false;
        data.name = this.formName + ".pdf";
        const accountId = await this.getAccountId();
        const files = [];
        files.push(data);
        this.procedureService.uploadMultiFile(files,accountId).subscribe(idFile => {
          const dialogData = new PdfViewerDialog(idFile[0].id, data.name, this.dossierId, dsType);
          const dialogRef = this.dialog.open(PdfViewerComponent, {
            maxWidth: '100%',
            width: '100%',
            height: '100%',
            maxHeight: '100%',
            data: dialogData,
            disableClose: false,
            panelClass: 'custom-dialog-container'
          });
          dialogRef.afterClosed().subscribe( async rs => {
            if(rs){
              this.logmanService.postSignHistory(rs.id,rs.name).subscribe();
              this.dialogRef.close({
                type: 1,
                file:{
                  id: rs.id,
                  filename: rs.name,
                  size: rs.size
                }
              })
            }
          });
        });
      }, err => {
        this.isLoading = false;
      });
    }
  }
}

export class ExportEFormDialogModel {
  // tslint:disable-next-line:max-line-length
  constructor(public valueEform: any, public formName: string, public dossierId: string, public procedureId: string, public digitalSignaturePerm?: any, public eformData?: any) {
  }
}

