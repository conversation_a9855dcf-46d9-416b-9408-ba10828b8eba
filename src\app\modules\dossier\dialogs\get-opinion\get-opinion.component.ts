import { Component, OnInit, Inject, ViewChild } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA, MatDialog } from '@angular/material/dialog';
import { KeycloakService } from 'keycloak-angular';
import { DossierService } from 'src/app/data/service/dossier/dossier.service';
import { UserService } from 'src/app/data/service/user.service';
import * as ClassicEditor from '@ckeditor/ckeditor5-build-classic/build/ckeditor';
import '@ckeditor/ckeditor5-build-classic/build/translations/vi';
import { EnvService } from 'src/app/core/service/env.service';
import { SnackbarService } from 'src/app/data/service/snackbar/snackbar.service';
import { DeploymentService } from 'src/app/data/service/deployment.service';
import { ProcedureService } from 'src/app/data/service/procedure/procedure.service';
import { CheckSendNotifyComponent } from 'src/app/shared/components/check-send-notify/check-send-notify.component';
import { Message, MessageCode } from 'src/app/shared/ts/message';
import { FormControl } from '@angular/forms';
import { MatCheckboxChange } from '@angular/material/checkbox';

@Component({
  selector: 'app-get-opinion',
  templateUrl: './get-opinion.component.html',
  styleUrls: ['./get-opinion.component.scss']
})
export class GetOpinionComponent implements OnInit {
  @ViewChild(CheckSendNotifyComponent) checkSendNotifyComponent;
  config = this.envService.getConfig();
  env: any = this.deploymentService.getAppDeployment()?.env;
  selectedLang = localStorage.getItem('language') || 'vi';
  dossierId: string;
  descriptionContent = '';
  userName: string;
  accountId: string;
  consultationId: string;
  dossierCode: string;
  summary: string;
  isCKMaxlenght = false;
  isCKDescriptionMaxlenght = false;
  dossierDetail: any;
  oldstatus = '';
  isDisabled = false;
  isConfirm = true;
  isEditSignTokenName = false;
  
  // IGATESUPP-89251: [iGate2.0][QNI] - Luồng xử lý hồ sơ dừng xử lý khi đang yêu cầu bổ sung - 26.06.2024
  enablePauseWhenAdditional = this.deploymentService.getAppDeployment()?.enablePauseWhenAdditional ? this.deploymentService.getAppDeployment()?.enablePauseWhenAdditional : false

  public Editor = ClassicEditor;
  descriptionConfig = {
    placeholder: 'Nhập nội dung...',
    language: 'vi',
    removePlugins: ['MediaEmbed', 'ImageUpload'],
    toolbarLocation: 'bottom'
  };
  dossierTaskStatus = { id: '', name: [] };
  dossierMenuTaskRemind = { id: '', name: [] };
  // ================================================= Upload file
  uploaded: boolean;
  blankVal: any;
  uploadedImage = null;
  files = [];
  urls = [];
  fileNames = [];
  uploadFileNames = [];
  fileNamesFull = [];

  acceptFileExtension = this.config.acceptFileExtension;
  acceptFileType = this.config.acceptFileType;
  maxFileSize = this.env?.maxFileSize || this.config.maxFileSize;
  typeProcess = 1;

  notifyQNI = this.deploymentService.getAppDeployment()?.notifyqni?.enable;

  ckeditorMaxLengthNotification = "";
  ckeditorMaxLength = this.deploymentService.env.OS_HCM.ckeditorMaxLength;
  tittle = new FormControl('');

  //IGATESUPP-54548
  enableRequestAdditionalDossier = this.deploymentService?.env?.OS_QBH?.enableRequestAdditionalDossier ?? false;

  additionRequestMinDate: Date;
  isDueDateChecked: boolean = false;

  constructor(
    private userService: UserService,
    private envService: EnvService,
    private keycloakService: KeycloakService,
    public dialogRef: MatDialogRef<GetOpinionComponent>,
    @Inject(MAT_DIALOG_DATA) public data: GetOpinionDialogModel,
    private snackbarService: SnackbarService,
    private deploymentService: DeploymentService,
    private procedureService: ProcedureService,
    private dossierService: DossierService,
  ) {
    this.dossierId = data.dossierId;
    this.consultationId = data.consultationId;
    this.dossierCode = data.dossierCode;
    this.summary = data.summary;
    if (this.typeProcess === 2){
      this.env.enableApprovalOfLeadership = 2;
    }
    this.additionRequestMinDate = new Date();
  }

  ngOnInit(): void {
    this.getUserAccount();
    this.getDossierTaskStatus();
    this.getDetailDossier();
    if (this.selectedLang === 'vi') {
      this.ckeditorMaxLengthNotification = "Nội dung không quá 500 ký tự!";
      if (this.ckeditorMaxLength > 0)
        this.ckeditorMaxLengthNotification = "Nội dung không quá "+ this.ckeditorMaxLength + " ký tự!";
    } else if (this.selectedLang === 'en') {
      this.ckeditorMaxLengthNotification = "Content must not exceed 500 characters!";
      if (this.ckeditorMaxLength > 0)
        this.ckeditorMaxLengthNotification = "Content must not exceed "+ this.ckeditorMaxLength + " characters!";
    }
    if (this.enableRequestAdditionalDossier){
      this.checkOverDue();
    }
  }

  async checkOverDue(){
    const currentDossierDetail = await this.dossierService.getDossierDetail(this.dossierId).toPromise();
    if (this.dossierService.checkOverDue(currentDossierDetail)) {
      const message = Message.getMessage(MessageCode.EXTEND_TIME);
      this.dossierService.showWarningDialog(message,this.dialogRef,);
    }
  }

  getUserAccount() {
    this.keycloakService.loadUserProfile().then(user => {
      // tslint:disable-next-line: no-string-literal
      this.accountId = user['attributes'].user_id[0];
      this.userService.getUserInfo(this.accountId).subscribe(data => {
        this.userName = data.fullname;
      });
    });
  }
  getDossierTaskStatus() {
    const tagId = this.env?.enableApprovalOfLeadership == 1 ? this.deploymentService.env.dossierTaskStatus.waitingForApprovalToAddition.id : this.deploymentService.env.dossierTaskStatus.requestForAdditionalDocuments.id;
    this.dossierService.getAgencyTag(tagId).subscribe(rs => {
      this.dossierTaskStatus.id = rs.id;
      this.dossierTaskStatus.name = rs.trans;
    }, err => {
      console.log(err);
    });
  }
  async getDetailDossier() {
    this.dossierService.getDossierDetail(this.dossierId).subscribe(async data => {
      this.dossierDetail = data;
      if (this.dossierDetail.dossierTaskStatus === undefined && this.dossierDetail.dossierTaskStatus === null) {
        // this.putDossierStatus();
      }
      else {
        this.oldstatus = this.dossierDetail.dossierTaskStatus.name;
      }
    });
    // this.flagDossierDetailCompletedSubject.next(); 
  }

  postHistory(newVal) {
    const content = {
      groupId: 1,
      itemId: this.dossierId,
      user: {
        id: this.accountId,
        name: this.userName
      },
      type: 3,
      deploymentId: this.config.deploymentId,
      action: [
        {
          fieldNameRbk: 'lang.word.file',
          originalValue: '',
          newValue: newVal,
        }
      ]
    };
    this.dossierService.postHistory(JSON.stringify(content, null, 2)).subscribe(data => {
    });
  }

  onConfirmChange(event: MatCheckboxChange): void {
    this.isConfirm = event.checked;
  }

  putAnsweredDossier() {
    this.dossierService.putAnsweredDossier(this.dossierId, this.consultationId).subscribe(data => { });
  }

  async onConfirm() {
    this.isDisabled = true;
    console.log("this.uploadFileNames",this.uploadFileNames);

    let checkFile = true;
    if (this.descriptionContent.trim() === '') {
      this.isDisabled = false; 
      const msgObj = {
        vi: 'Vui lòng nhập nội dung trả lời!',
        en: 'Please enter reply content!'
      };
      this.snackbarService.openSnackBar(2, '', msgObj[this.selectedLang], 'warning_notification', this.config.expiredTime);
    }
    if (this.isCKMaxlenght || this.isCKDescriptionMaxlenght) {
      let msgObj;
      if (this.ckeditorMaxLength > 0) {
        msgObj = {
          vi: 'Nội dung không quá ' + this.ckeditorMaxLength + ' ký tự!',
          en: 'Content must not exceed ' + this.ckeditorMaxLength + ' characters!'
        };
      } else {
        msgObj = {
          vi: 'Nội dung không quá 500 ký tự!',
          en: 'Content must not exceed 500 characters!'
        };
      }
      this.isDisabled = false; 
      this.snackbarService.openSnackBar(2, '', msgObj[this.selectedLang], 'warning_notification', this.config.expiredTime);
    }
   
    if (!this.isCKMaxlenght && !this.isCKDescriptionMaxlenght && this.descriptionContent.trim() !== '' && checkFile) {
      if (this.descriptionContent.trim() !== '') {
        this.postComment(this.descriptionContent.trim());
        for (let j = 0; j < this.uploadFileNames.length; j++) {
          this.postHistory(JSON.stringify({
            id: this.uploadFileNames[j].id,
            type: 'file',
            name: this.uploadFileNames[j].filename,
            uuid: this.uploadFileNames[j].uuid,
          }));
          this.updateDossierAttachment(this.uploadFileNames[j]);
        }
        if(this.isConfirm) {
          this.putAnsweredDossier();
        }
        this.dialogRef.close(true);
      }
    }
  }

  onDismiss() {
    // xóa dữ liệu file tạm
    this.uploadFileNames.forEach(element => {
      this.procedureService.deleteFile(element.id).subscribe(res => {
      }, err => {
        console.log(err);
      });
    });
    this.uploadFileNames = [];
    //
    this.dialogRef.close();
  }

  postComment(description?:string) {
    const content = {
      groupId: 2,
      itemId: this.dossierId,
      user: {
        id: this.accountId,
        fullname: this.userName
      },
      content: description,
      file: this.uploadFileNames
    };
    const requestBody = JSON.stringify(content, null, 2);
    this.dossierService.postComment(requestBody).subscribe(data => { });
  }

  getPlainText( strSrc ) {
    let resultStr = "";

    // Ignore the <p> tag if it is in very start of the text
    if(strSrc.indexOf('<p>') == 0)
        resultStr = strSrc.substring(3);
    else
        resultStr = strSrc;

    // Replace <p> with two newlines
    resultStr = resultStr.replace(/<p>/gi, "\r\n\r\n");
    // Replace <br /> with one newline
    resultStr = resultStr.replace(/<br \/>/gi, "\r\n");
    resultStr = resultStr.replace(/<br>/gi, "\r\n");

    // Replace &nbsp; with space
    resultStr = resultStr.replace(/&nbsp;/gi, " ");
    //-+-+-+-+-+-+-+-+-+-+-+
    // Strip off other HTML tags.
    //-+-+-+-+-+-+-+-+-+-+-+

    return  resultStr.replace( /<[^<|>]+?>/gi,'' );
  }

  onContentEditorChange(event) {
    this.descriptionContent = event.editor.getData();
    if (this.ckeditorMaxLength > 0) {
      if (event.editor.getData().trim().length > this.ckeditorMaxLength) {
        this.isCKDescriptionMaxlenght = true;
      } else {
        this.isCKDescriptionMaxlenght = false;
      }
    } else if (event.editor.getData().trim().length > 500) {
      this.isCKDescriptionMaxlenght = true;
    } else {
      this.isCKDescriptionMaxlenght = false;
    }
  }

  uploadMultiFile(file, accountId) {
    return new Promise((resolve) => {
      this.procedureService.uploadMultiFile(file, accountId).subscribe(data => {
        if(!!data && data.length > 0){
          const updatedData = data.map(d => ({
            ...d,
            group: this.config.dossierAttachedFileTagId
          }));
          this.uploadedImage = updatedData;
          this.uploadFileNames.push(...updatedData);
          // this.formToJSON();
          resolve(true);
        } else {
          resolve(false);
        }
      }, err => {
        console.log(err);
        resolve(false);
      });
    });
  }

  async updateDossierAttachment(newFile) {
    let data = await this.dossierService.getDossierDetail(this.dossierId).toPromise();
    let dossierAttachment = [];
    if (data.attachment) {
      dossierAttachment = data.attachment;
    }
    if(newFile.idold)
      dossierAttachment = dossierAttachment.filter(e => e.id !== newFile.idold || e.group != newFile.group);
    else
      dossierAttachment = dossierAttachment.filter(e => e.id !== newFile.id || e.group != newFile.group);
      dossierAttachment.push(newFile);
    const putBody = {
      attachment: dossierAttachment
    };
    const requestBody = JSON.stringify(putBody, null, 2);
    this.dossierService.putDossierOnline(this.dossierId, requestBody).subscribe(
      () => {
        console.log('Cập nhật thành công');
      },
      err => {
        console.log('Cập nhật thất bại!', err);
      }
    );
  }

  async onSelectFile(event) {
    if (event.target.files && event.target.files[0]) {
      for (const i of event.target.files) {
        if (i.size >= this.maxFileSize * 1024 * 1024) {
          const msgObj = {
            vi: 'Kích thước tệp tin quá lớn, tên tệp: ' + i.name,
            en: 'The file is too large, file name: ' + i.name
          };
          this.snackbarService.openSnackBar(0, '', msgObj[this.selectedLang], 'error_notification', this.config.expiredTime);
          return;
        }
        if (this.acceptFileType.filter(t => t === i.type.toLowerCase()).length > 0) {
          let newFile = [];
          newFile.push(i);
          this.files.push(i);
          const extension = i.name.substring(i.name.lastIndexOf('.')).split('.').pop();
          this.urls.push(this.getFileIcon(extension));

          const reader = new FileReader();
          reader.onload = () => {
            this.uploaded = true;
          };
          if (i.name.length > 20) {
            const startText = i.name.substr(0, 5);
            const shortText = i
              .name
              .substring(i.name.length - 7, i.name.length);
            this.fileNames.push(startText + '...' + shortText);
            this.fileNamesFull.push(i.name);
          } else {
            this.fileNames.push(i.name);
            this.fileNamesFull.push(i.name);
          }
          reader.readAsDataURL(i);
          if (this.files.length > 0) {
            let checkFile = await this.uploadMultiFile(newFile, this.accountId);
            if (!checkFile) {
              const msgObj = {
                vi: 'Upload file thất bại!',
                en: 'File upload failed!'
              };
              this.snackbarService.openSnackBar(2, '', msgObj[this.selectedLang], 'warning_notification', this.config.expiredTime);
            }
          }
        } else {
          const msgObj = {
            vi: 'Không hỗ trợ loại tệp tin ',
            en: 'File type is not supported '
          };
          // tslint:disable-next-line: max-line-length
          this.snackbarService.openSnackBar(0, '', msgObj[this.selectedLang] + i.type.substring(i.type.lastIndexOf('/') + 1).toUpperCase(), 'error_notification', this.config.expiredTime);
        }
      }
    }
  }

  removeItem(index: number) {
    this.procedureService.deleteFile(this.uploadFileNames[index].id).subscribe(res => {
    }, err => {
      console.log(err);
    });
    this.uploadFileNames.splice(index, 1);
    this.urls.splice(index, 1);
    this.fileNames.splice(index, 1);
    this.fileNamesFull.splice(index, 1);
    this.files.splice(index, 1);
    this.blankVal = '';
  }
  

  getFileIcon(ext) {
    return this.config.cloudStaticURL + 'icon/files/300x300/' + ext + '.png';
  }

  downloadFileExport(index: number){
    let file = this.uploadFileNames[index];
    this.downloadFile(file.id, file.filename);
  }

  downloadFile(id, filename) {
    this.procedureService.downloadFile(id, this.dossierId).subscribe(data => {
      const dataType = data.type;
      const binaryData = [];
      binaryData.push(data);
      const downloadLink = document.createElement('a');
      downloadLink.href = window.URL.createObjectURL(new Blob(binaryData, { type: dataType }));
      downloadLink.setAttribute('download', filename);
      document.body.appendChild(downloadLink);
      downloadLink.click();
    }, err => {
      const msgObj = {
        vi: 'Không tìm thấy file!',
        en: 'File not found!'
      };
      this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
      console.log(err);
    });
  }

  routerLink(file)
  {
    if(!file) return;
    let name = file.filename ? file.filename: file.name;
    name = name.toUpperCase();
    if(name.indexOf('.JPG') >= 0 || name.indexOf('.JPEG') >= 0 || name.indexOf('.PNG') >= 0)
      return ['/viewer-image/' + file.id, {dossierId: this.dossierId }];

    return ['/viewer/' + file.id, {dossierId: this.dossierId }];
  }

 async downloadFileFromResult(file){
    this.procedureService.downloadFile(file?.id, this.dossierId).subscribe(async data => {
      const dataType = data.type;
      const binaryData = [];
      binaryData.push(data);
      var blob = new Blob(binaryData, { type: dataType });
      var myFile = new File([blob], file.filename)
      this.files.push(myFile);
      const extension = file.filename.substring(file.filename.lastIndexOf('.')).split('.').pop();
       this.urls.push(this.getFileIcon(extension));

      if (file.filename.length > 20) {
        const startText = file.filename.substr(0, 5);
        const shortText = file.filename.substring(file.filename.length - 7, file.filename.length);
        this.fileNames.push(startText + '...' + shortText);
        this.fileNamesFull.push(file.filename);
      } else {
        this.fileNames.push(file.filename);
        this.fileNamesFull.push(file.filename);
      }
      //reader.readAsDataURL(blob);
      if (myFile) {
        let checkFile = await this.uploadMultiFile([myFile], this.accountId);
        if (!checkFile) {
          const msgObj = {
            vi: 'Upload file thất bại!',
            en: 'File upload failed!'
          };
          this.snackbarService.openSnackBar(2, '', msgObj[this.selectedLang], 'warning_notification', this.config.expiredTime);
        }
      }
    });
  }
}

export class GetOpinionDialogModel {
  constructor(public dossierId: string, public dossierCode: string, public consultationId: string, public summary: string) {
  }
}


