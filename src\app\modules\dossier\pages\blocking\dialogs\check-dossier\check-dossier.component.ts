import { Component, Inject, OnInit } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialog, MatDialogRef } from '@angular/material/dialog';
import { FormService } from 'src/app/data/service/form/form.service';
import { BlockingDossierService } from 'data/service/qnm-blocking-dossier/blocking-dossier.service';
import { FormControl, FormGroup } from '@angular/forms';
import { SnackbarService } from 'data/service/snackbar/snackbar.service';
import { EnvService } from 'core/service/env.service';
import { ErrorComponent, ErrorDialogModel } from 'modules/dossier/pages/blocking/dialogs/error/error.component';

@Component({
  selector: 'app-check-blocking-dossier',
  templateUrl: './check-dossier.component.html',
  styleUrls: ['./check-dossier.component.scss']
})
export class CheckDossierComponent implements OnInit {
  constructor(public dialogRef: MatDialogRef<CheckDossierComponent>,
              @Inject(MAT_DIALOG_DATA) public data: ConfirmCheckBlockingDossierDialogModel,
              private dialog: MatDialog,
              private envService: EnvService,
              private formService: FormService,
              private snackbarService: SnackbarService,
              private blockingDossierService: BlockingDossierService) {
  }

  config = this.envService.getConfig();

  checkForm = new FormGroup({
    ownerFullname: new FormControl(''),
    identityNumber: new FormControl(''),
    certificateId: new FormControl(''),
    slotId: new FormControl(''),
  });

  ngOnInit(): void {
    if (this.data.identityNumber) {
      this.checkForm.patchValue({
        identityNumber: this.data.identityNumber
      });
    }
    if (this.data.ownerFullname) {
      this.checkForm.patchValue({
        ownerFullname: this.data.ownerFullname
      });
    }
  }

  onConfirm() {
    const formObject = this.checkForm.getRawValue();
    const searchQuery = `?page=0&size=10&ownerFullname=${formObject?.ownerFullname}` +
                        `&identityNumber=${formObject?.identityNumber}&certificateId=${formObject?.certificateId}`;
    this.blockingDossierService.getListBlockingDossier(searchQuery).subscribe(data => {
      console.log('data', data);
      if (data.totalElements) {
        // this.dialogRef.close(true);
        const message = data?.content[0]?.content || '(Không có dữ liệu nội dung ngăn chặn)';
        this.error(message, message);
      } else {
        this.dialogRef.close(true);
        this.snackbarService.openSnackBar(1, 'Hợp lệ', 'Hồ sơ hợp lệ', 'success_notification', this.config.expiredTime);
      }
    });
  }

  onDismiss() {
    this.dialogRef.close();
  }

  error(vi, en) {
    const dialogData = new ErrorDialogModel(vi, en);
    const dialogRef = this.dialog.open(ErrorComponent, {
      width: '500px',
      data: dialogData,
      disableClose: false,
      autoFocus: false
    });
  }

}
export class ConfirmCheckBlockingDossierDialogModel {
  constructor(
    public ownerFullname: string,
    public identityNumber: string,
    public certificateId?: string,
    public slotId?: string,
  ) {}
}
