import { Component, OnInit, ChangeDetectorRef, AfterViewInit, On<PERSON>estroy, ViewChild } from '@angular/core';
import { FormGroup, FormControl, Validators } from '@angular/forms';
import { EnvService } from 'src/app/core/service/env.service';
import { SnackbarService } from 'src/app/data/service/snackbar/snackbar.service';
import { KeycloakService } from 'keycloak-angular';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { BasedataService } from 'src/app/data/service/basedata/basedata.service';
import { BasepadService } from 'src/app/data/service/basepad/basepad.service';
import { ReplaySubject, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { DeploymentService } from 'src/app/data/service/deployment.service';
import { ComboboxLazyLoadComponent } from 'src/app/shared/components/combobox-lazy-load/combobox-lazy-load.component';
import { AgencyService } from 'src/app/data/service/basedata/agency.service';
export interface Element {
  id: String;
  name: String;
}

const Agency: Element[] = [];
@Component({
  selector: 'app-add-administrative',
  templateUrl: './add-administrative.component.html',
  styleUrls: ['./add-administrative.component.scss']
})
export class AddAdministrativeComponent implements OnInit, AfterViewInit, OnDestroy {
  @ViewChild('searchAgency') searchAgency: ComboboxLazyLoadComponent;
  countResult1 = 0;
  formTemp;
  countResult = 0;
  selectAgency = Agency;
  selectProce = Agency;
  listAgency = [];
  listProcedure = [];
  // configDepartmentTagId = this.deploymentService.env.OS_HCM.configDepartmentTagId;
  config = this.envService.getConfig();
  selectedLang: string;
  accountId: any;
  guideTitle = [];
  listGuideType = [];
  listGuideTypePage = 0;
  isFullListGuideType = false;
  listAcceptExt = [];
  listAcceptFileType = [];
  blankVal = '';
  result = [];
  isFieldArrayInvalid = false;
  languageIdUsed = [228];
  listLanguage = [
    {
      languageId: 228,
      name: 'Tiếng Việt'
    },
    {
      languageId: 46,
      name: 'English'
    }
  ];
  newAttribute: any = {
    languageId: 228,
    name: '',
    listLanguageItem: JSON.parse(JSON.stringify(this.listLanguage))
  };
  newItemfield: any = {
    code: '',
    name: ''
  }
  fieldArray: Array<any> = [this.newItemfield];
  status: Array<any> = [
    {
      status: 0,
      name: 'Đóng'
    },
    {
      status: 1,
      name: 'Mở'
    }
  ];
  statusVi: Array<any> = [
    {
      status: 0,
      name: 'Đóng'
    },
    {
      status: 1,
      name: 'Mở'
    }
  ];
  statusEn: Array<any> = [
    {
      status: 0,
      name: 'Close'
    },
    {
      status: 1,
      name: 'Open'
    }
  ];
  nameDefault = '';
  addForm = new FormGroup({
    status: new FormControl(''),
    numberIncreaseAccordingBook: new FormControl(false),
    resetAccordingYear: new FormControl(false)
  });
  title = new FormControl('', [Validators.required, Validators.pattern(this.config.regValidators)]);
  guideType = new FormControl('', [Validators.required, Validators.pattern(this.config.regValidators)]);
  isSubmit = false;

  // listGuide = [];
  protected onDestroy = new Subject<void>();
  private listSector: any[] = [];
  protected sectors: any[] = this.listSector;
  public sectorFiltered: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  searchSectorCtrl: FormControl = new FormControl();
  isAdmin?: boolean = sessionStorage.getItem('oneGateAdminMaster') === '1';
  showPublicAdministrativeAgency = this.deploymentService.env.OS_HCM.showPublicAdministrativeAgency;
  agencyId;
  constructor(
    private dialog: MatDialog,
    public dialogRef: MatDialogRef<AddAdministrativeComponent>,
    private envService: EnvService,
    private snackbarService: SnackbarService,
    private keycloakService: KeycloakService,
    private basedataService: BasedataService,
    private basepadService: BasepadService,
    private deploymentService: DeploymentService,
    private agencyService: AgencyService
  ) {
    this.listAcceptFileType = this.config.acceptFileType;
    this.listAcceptExt = this.config.acceptFileExtension;
  }

  ngOnInit(): void {
    this.selectedLang = localStorage.getItem('language');
    this.nameDefault = '(Không tìm thấy bản dịch)';
    if (Number(localStorage.getItem('languageId')) === 46) {
      this.status = this.statusEn;
      this.nameDefault = '(No translation found)';
    }
    
  }
  
ngOnDestroy() {
  this.onDestroy.next();
  this.onDestroy.complete();
}


  ngAfterViewInit() {
    setTimeout(() => {
      //console.clear();
    }, 2000);
  }

  onDismiss() {
    this.dialogRef.close();
  }


   async checkExistCode(code){
    let result=true;
  let search= '?page=0&size=1000&spec=page';
  const data= await this.basepadService.searchPublicAdministration(search).toPromise();
    for (let i = 0; i < data.numberOfElements; i++) {
      ////debugger;
      if(data.content[i].code==code){
        result= false;
      }
    }
  return result;
   }

   async loadAgency(obj){
    const page = obj?.page || 0;
    const size = obj?.size || 10;
    const parentId = this.getRootAgencyId();
    const tagId = this.showPublicAdministrativeAgency.agencyTagId;
    if(this.showPublicAdministrativeAgency && this.showPublicAdministrativeAgency.enable && (!this.isAdmin || !this.showPublicAdministrativeAgency.admin)){
      let agencyId = this.getAgencyId();
      let rootAgency = await this.agencyService.getRootAgency(agencyId);
      if(rootAgency && rootAgency.tag.find(o => o.id == this.showPublicAdministrativeAgency.agencyTagId)){
        this.searchAgency.update([rootAgency], false);
        this.agencyId = rootAgency.id;
      }
    }
    else {
      this.agencyService.getPageAgency(page, size, parentId, tagId).subscribe((rs) => {
        const data = [].concat(rs.content);
        const hasNext = !rs.last;
        this.searchAgency.update(data, hasNext);
      });
    }
  }

  getAgencyId(): string | null {
    const userAgency = JSON.parse(localStorage.getItem('userAgency'));
    if (userAgency){
        return userAgency.id;
    }
    return null;
  }

  getRootAgencyId(){
    const userAgency = JSON.parse(localStorage.getItem('userAgency'));
    let rootAgencyId: any = '';
    if (this.config.rootAgency !== null) {
      rootAgencyId = this.config.rootAgency.id;
    } else {
      rootAgencyId = userAgency.id;
    }
    return rootAgencyId;
  }

  async onConfirm() {
    this.isSubmit = true;
    const formObj = this.addForm.getRawValue();
     console.log('check', this.fieldArray[0]);
     if (this.fieldArray[0].name==='' || this.fieldArray[0].code===''  || formObj.status.length===0) {
      this.isSubmit = false;
      const msgObj = {
        vi: 'Vui lòng điền đầy đủ thông tin!',
        en: 'Please fill full information!'
      };

      this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
    } else if (await this.checkExistCode(this.fieldArray[0].code)==false) {
      this.isSubmit = false;
      const msgObj = {
        vi: 'Mã sổ này đã tồn tại!',
        en: 'Please This code already exists!'
      };

      this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
    } 
    else {
        let content:any = {
          code:'',
          name:'',
          status: '',
          numberIncreaseAccordingBook: false,
          resetAccordingYear: false,
        };

            content.code = this.fieldArray[0].code;
            content.name = this.fieldArray[0].name;
            content.status = formObj.status;
            content.numberIncreaseAccordingBook = formObj.numberIncreaseAccordingBook;
            content.resetAccordingYear = formObj.resetAccordingYear;
            if(this.showPublicAdministrativeAgency && this.showPublicAdministrativeAgency.enable){
              content.agencyId = this.searchAgency.value[0];
            }
            console.log('Addform', this.fieldArray);
            // const requestBody = JSON.stringify(content, null, 2);
            console.log('form', content);
  
          
           this.basepadService.postPublicAdministration(content).subscribe(data => {
            const result = {
              status: true
            };
            this.dialogRef.close(result);
          }, err => {
            const result = {
              status: false,
              code: err
            };
            this.dialogRef.close(result);
          });
        
  
      }
    
      
    }



  changeName(i, $event) {
    const value = $event.target.value;
    this.fieldArray[i].name = value.trim();

    setTimeout(() => {

    }, 200);
  }
  changeCode(i, $event) {
    const value = $event.target.value;
    this.fieldArray[i].code = value.trim();

    setTimeout(() => {

    }, 200);
  }
 

}

export class ConfirmAddDialogModel {
  constructor() { }
}