// tslint:disable-next-line: no-namespace
declare namespace paymentPlatformInit {

    export interface PhiLePhi {
        LoaiPhiLePhi: string;
        MaPhiLePhi: string;
        TenPhiLePhi: string;
        SoTien: string;
    }

    export interface DSKhoanNop {
        NoiDung: string;
        SoTien: string;
    }

    export interface ThongTinBienLai {
        MaDichVu: number;
        TKThuHuong: string;
        MaNHThuHuong: string;
        TenTKThuHuong: string;
        PhiLePhi: PhiLePhi[];
        MaDonVi: string;
        TenDonVi: string;
        MaHoSo: string;
        MaDVC: string;
        TenDVC: string;
        MaTTHC: string;
        TenTTHC: string;
        NoiDungThanhToan: string;
        MaLoaiHinhThuPhat: string;
        TenLoaiHinhThuPhat: string;
        HoTenNguoiNop: string;
        SoCMNDNguoiNop: string;
        DiaChiNguoiNop: string;
        HuyenNguoiNop: string;
        TinhNguoiNop: string;
        MaCoQuanQD: string;
        TenCoQuanQD: string;
        KhoBac: string;
        NgayQD: string;
        SoQD: string;
        ThoiGianViPham: string;
        DiaDiemViPham: string;
        TenNguoiViPham: string;
        TaiKhoanThuNSNN: string;
        DSKhoanNop: DSKhoanNop[];
    }

    export interface PaymentPlatformInit {
        SubsystemId: string;
        AgencyId: string;
        LoaiBanTin: string;
        PhienBan: string;
        MaDoiTac: string;
        MaThamChieu: string;
        SoTien: string;
        LoaiHinhThanhToan: string;
        MaKenhThanhToan: string;
        MaThietBi: string;
        NgonNgu: string;
        MaTienTe: string;
        MaNganHang: string;
        ThongTinGiaoDich: string;
        ThoiGianGD: string;
        Ip: string;
        ThongTinBienLai: ThongTinBienLai;
        MaXacThuc: string;
    }

}
