<button mat-icon-button class="close-button" (click)="onDismiss()">
  <mat-icon>close</mat-icon>
</button>
<h3 class="dialog_title" mat-dialog-title>T<PERSON><PERSON> ki<PERSON><PERSON> hồ sơ ngăn chặn</h3>
<form [formGroup]="checkForm" class="checkForm">
  <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="column">
    <mat-form-field appearance="outline" fxFlex='grow'>
      <mat-label>Họ tên</mat-label>
      <input type="text" matInput formControlName="ownerFullname" required readonly>
    </mat-form-field>
  </div>
  <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="column">
    <mat-form-field appearance="outline" fxFlex='grow'>
      <mat-label>Số CMND/CCCD</mat-label>
      <input type="text" matInput formControlName="identityNumber" required readonly>
    </mat-form-field>
  </div>
  <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="column">
    <mat-form-field appearance="outline" fxFlex='grow'>
      <mat-label>Số bìa đỏ</mat-label>
      <input type="text" matInput formControlName="certificateId" required>
    </mat-form-field>
  </div>
  <br>
  <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="column" fxLayoutAlign="center">
    <button mat-flat-button fxFlex='17' class="checkBtn" (click)="onConfirm()" [disabled]="checkForm.invalid">
      <span i18n>Tìm kiếm</span>
    </button>
  </div>
</form>
