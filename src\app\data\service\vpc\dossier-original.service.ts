import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { ApiProviderService } from 'src/app/core/service/api-provider.service';

@Injectable({
  providedIn: 'root'
})
export class DossierOriginalService {

  private apiURL = this.apiProviderService.getUrl('digo', 'padman') + '/api-vpc-dossier-original';
  constructor(
    private http: HttpClient,
    private apiProviderService: ApiProviderService,
  ) { }
  
  putConfirmOriginalDossier(requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.put<any>(this.apiURL + '/--confirm-multiple-original-dossier', requestBody, { headers });
  }

}
