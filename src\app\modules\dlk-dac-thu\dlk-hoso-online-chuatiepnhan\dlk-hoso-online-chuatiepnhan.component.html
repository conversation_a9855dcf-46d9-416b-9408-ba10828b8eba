<h2>T<PERSON><PERSON><PERSON> <PERSON><PERSON> hồ sơ online chưa tiế<PERSON> nhận</h2>
<div fxLayout="row" fxLayoutAlign="center" fxLayout.sm="column" fxLayout.xs="column">
    <div class="frm_searchbar" fxFlex="grow">
        <form class="searchForm" [formGroup]="searchForm">
          <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="row" class="formFieldItems" fxLayoutAlign="space-between">
                <mat-form-field appearance="outline" fxFlex.gt-sm="42.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                    <mat-label i18n>Đơn vị</mat-label>
                    <mat-select msInfiniteScroll (infiniteScroll)="getAgencyList(true)" formControlName="agency"
                                [complete]="isAgencyListFull">
                        <mat-option>
                            <ngx-mat-select-search formControlName="agencyCtrl" placeholderLabel=""
                                                   (keyup)="searchAgencyList()"
                                                   [disableScrollToActiveOnOptionsChanged]="true"
                                                   [clearSearchInput]="false"
                                                   i18n-noEntriesFoundLabel
                                                   noEntriesFoundLabel="Không tìm thấy kết quả nào!">
                                <mat-icon ngxMatSelectSearchClear (click)="clearAgencyList()">close</mat-icon>
                            </ngx-mat-select-search>
                        </mat-option>
                        <mat-option value="" i18n>Tất cả</mat-option>
                        <mat-option *ngFor="let agency of agencyList" value="{{agency.id}}">
                            {{agency.name}}
                            <span *ngIf="agency.name == undefined || agency.name == null" >(Không tìm thấy bản dịch)</span>
                        </mat-option>
                    </mat-select>
                </mat-form-field>
                <div fxLayoutAlign="end">
                  <button mat-flat-button class="btn-download-excel" (click)="getListDossierAllExcel()" >
                    <mat-icon class="iconStatistical">cloud_download</mat-icon>
                    <span i18n >Xuất excel</span>
                  </button>
                  <div fxFlex='1'></div>
                  <button mat-flat-button class="btn-search" (click)="searchBtn()"
                          [disabled]="isLoading">
                          <mat-icon class="iconStatistical" *ngIf="!isLoading">bar_chart</mat-icon>
                          <mat-spinner diameter="25" class="iconStatistical" *ngIf="isLoading"></mat-spinner>
                          <span i18n>Thống kê</span>
                  </button>
                </div>
              
                <!-- <div fxFlex='1'></div>
                <mat-form-field appearance="outline" fxFlex.gt-sm="27.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                  <mat-label i18n>Từ ngày</mat-label>
                  <input matInput [matDatepicker]="pickerFromDate" formControlName="fromDate">
                  <mat-datepicker-toggle matSuffix [for]="pickerFromDate"></mat-datepicker-toggle>
                  <mat-datepicker #pickerFromDate></mat-datepicker>
                </mat-form-field>
                <div fxFlex='1'></div>
                <mat-form-field appearance="outline" fxFlex.gt-sm="27.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                  <mat-label i18n>Đến ngày</mat-label>
                  <input matInput [matDatepicker]="pickerToDate" formControlName="toDate">
                  <mat-datepicker-toggle matSuffix [for]="pickerToDate"></mat-datepicker-toggle>
                  <mat-datepicker #pickerToDate></mat-datepicker>
                </mat-form-field> -->
            </div>
        </form>
    </div>
</div>
<div fxLayout="row" fxLayoutAlign="center">
  <div class="prc_main" fxFlex="grow">
    <div class="top-control" fxLayout="row" fxLayoutAlign="end">
      <button mat-flat-button class="btn_ctrl" [matMenuTriggerFor]="printMenu" style="display: none;">
        <mat-icon>print</mat-icon>
        <span i18n>In phiếu</span>
        <mat-icon>keyboard_arrow_down</mat-icon>
      </button>
    </div>
    <mat-menu #printMenu="matMenu">
      <button mat-menu-item i18n>Phiếu hướng dẫn</button>
    </mat-menu>
    <div class="frm_tbl">
      <table mat-table [dataSource]="dataSource">

        <!-- Header row third group -->
        <ng-container matColumnDef="no">
          <th mat-header-cell *matHeaderCellDef style="width: 5%;" class="cell_info">STT</th>
          <td mat-cell *matCellDef="let row;index as i" style="width: 5%; text-align: center;"  class="cell_info">{{row.no}}</td>
        </ng-container>
        <ng-container matColumnDef="agencyName">
          <th mat-header-cell *matHeaderCellDef style="width:20%" class="cell_info">Cơ quan</th>
          <td mat-cell *matCellDef="let row" style="width:20%" class="cell_info">{{row.agencyName}}</td>
        </ng-container>
        <ng-container matColumnDef="code">
          <th mat-header-cell *matHeaderCellDef style="width:15%" class="cell_info">Số hồ sơ</th>
          <td mat-cell *matCellDef="let row" style="width:15%" class="cell_info">{{row.code}}</td>
        </ng-container>
        <ng-container matColumnDef="procedureCode">
          <th mat-header-cell *matHeaderCellDef style="width:15%" class="cell_info">Mã thủ tục</th>
          <td mat-cell *matCellDef="let row" style="width:15%" class="cell_info">{{row.procedureCode}}</td>
        </ng-container>
        <ng-container matColumnDef="procedureName">
          <th mat-header-cell *matHeaderCellDef style="width:25%" class="cell_info">Tên thủ tục</th>
          <td mat-cell *matCellDef="let row" style="width:25%" class="cell_info">{{row.procedureName}}</td>
        </ng-container>
        <ng-container matColumnDef="appliedDate">
          <th mat-header-cell *matHeaderCellDef style="width:10%" class="cell_info">Ngày nộp</th>
          <td mat-cell *matCellDef="let row" style="width:10%" class="cell_info">{{row.appliedDate}}</td>
        </ng-container>
        <ng-container matColumnDef="appliedTime">
          <th mat-header-cell *matHeaderCellDef style="width:10%" class="cell_info">Thời gian đã nộp</th>
          <td mat-cell *matCellDef="let row" style="width:10%" class="cell_info">{{row.appliedTime}}</td>
        </ng-container>

        <mat-header-row *matHeaderRowDef="displayedColumns"></mat-header-row>
        <mat-row *matRowDef="let row; columns: displayedColumns;"></mat-row>
      </table>
      <div class="frm_Pagination">
        <ul class="temp_Arr">
          <li *ngFor="let item of ELEMENTDATA  | paginate: {itemsPerPage: size, currentPage: page, totalItems: countResult, id: 'pgnx'}">
          </li>
        </ul>
        <div class="pageSize">
          <span i18n>Hiển thị </span>
          <mat-form-field appearance="outline">
            <mat-select [(value)]="size" (valueChange)="paginate(pageIndex, 1)">
              <mat-option *ngFor='let opt of pgSizeOptions;' [value]="opt">{{opt}}</mat-option>
            </mat-select>
          </mat-form-field>
          <span><span i18n>trên</span> {{countResult}} <span i18n>bản ghi</span></span>
        </div>
        <div class="control">
          <pagination-controls id="pgnx" (pageChange)="page = $event; paginate(page, 0)" responsive="true" previousLabel="" nextLabel="">
          </pagination-controls>
        </div>
      </div>
    </div>
  </div>
</div>
