import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { LGSPHCMLLTPVNEIDLogRoutingModule } from './lgsp-hcm-lltp-vneid-log-routing.module';
import { SharedModule } from 'src/app/shared/shared.module';

import { LGSPHCMLLTPVNEIDLogComponent } from './pages/lgsp-hcm-lltp-vneid-log.component';
import { LGSPHCMLLTPVNEIDLogDetailComponent } from './dialogs/view-detail/view-detail.component';

@NgModule({
  declarations: [
    LGSPHCMLLTPVNEIDLogComponent,
    LGSPHCMLLTPVNEIDLogDetailComponent
  ],
  imports: [
    CommonModule,
    LGSPHCMLLTPVNEIDLogRoutingModule,
    SharedModule
  ]
})
export class LGSPHCMLLTPVNEIDLogModule { }

