export const getTranslateName = (translate: any[], languageId?) => {
    languageId = languageId || 228;
    let i = translate.length;
    while (i--) {
        if (!!translate[i] && !!translate[i].languageId && translate[i].languageId === languageId) {
            return translate[i].name;
        }
    }
    return '';
};

export const getNullTitle = (byId: boolean, lang) => {
    let condition;
    if (byId) {
        condition = lang + '' === '228';
    } else {
        condition = lang === 'vi';
    }
    let title = '(Translation not found)';
    if (condition) {
        title = '(<PERSON>hông tìm thấy bản dịch)';
    }
    return title;
};

export const valueExistsInList = (value, key, objectList) => {
    let i = objectList.length;
    while (i--) {
        if (objectList[i][key] === value) {
            return true;
        }
    }
    return false;
};

export const calculateDifference = (date1, date2) => {
    const d1 : any = new Date(date1);
    const d2 : any = new Date(date2);

    // T<PERSON>h khoảng cách theo milliseconds
    let diffTime = Math.abs(d2 - d1);

    // Chuyển đổi sang ngày, giờ, phút, giây
    const days = Math.floor(diffTime / (1000 * 60 * 60 * 24));
    diffTime %= 1000 * 60 * 60 * 24;

    const hours = Math.floor(diffTime / (1000 * 60 * 60));
    diffTime %= 1000 * 60 * 60;

    const minutes = Math.floor(diffTime / (1000 * 60));
    diffTime %= 1000 * 60;

    const seconds = Math.floor(diffTime / 1000);

    return { days, hours, minutes, seconds };
}

export const addTimeToDate = (date: string, days: number = 0, hours: number = 0, minutes: number = 0, seconds: number = 0) => {
    const resultDate = new Date(date);

    // Tính tổng số milliseconds cần cộng
    const additionalTime =
        days * 24 * 60 * 60 * 1000 +
        hours * 60 * 60 * 1000 +
        minutes * 60 * 1000 +
        seconds * 1000;

    // Cộng thời gian vào ngày
    resultDate.setTime(resultDate.getTime() + additionalTime);

    // Trả về ngày giờ đầy đủ (ISO format)
    return resultDate.toISOString();
}


export const removeObjectInList = (value, key, objectList) => {
    const index = objectList.findIndex(item => item[key] === value);
    if (index > -1) { objectList.splice(index, 1); }
    return objectList;
};

export const copyObject = (object) => {
    return JSON.parse(JSON.stringify(object));
};

export const tree = (data, root) => {
    const r = [];
    const o = {};
    data.forEach(a => {
        if (o[a.id] && o[a.id].children) {
            a.children = o[a.id] && o[a.id].children;
        }
        o[a.id] = a;
        if (a.parent === root) {
            r.push(a);
        } else {
            o[a.parent] = o[a.parent] || {};
            o[a.parent].children = o[a.parent].children || [];
            o[a.parent].children.push(a);
        }
    });
    return r;
};

export const treeNotUser = (data, root) => {
    const r = [];
    const o = {};
    data.forEach(a => {
        if(a.isUser != true){
            if (o[a.id] && o[a.id].children) {
                a.children = o[a.id] && o[a.id].children;
            }
            o[a.id] = a;
            if (a.parent === root) {
                r.push(a);
            } else {
                o[a.parent] = o[a.parent] || {};
                o[a.parent].children = o[a.parent].children || [];
                o[a.parent].children.push(a);
            }
        }
    });
    return r;
};

export const sleep = (ms) => {
    return new Promise((resolve) => {
        setTimeout(resolve, ms);
    });
};

export const arraysContainSame = (a, b) => {
    // a = Array.isArray(a) ? a : [];
    // b = Array.isArray(b) ? b : [];
    if (a.length === 0 || b.length === 0) {
        return false;
    }
    return a.every(el => b.includes(el)) || b.every(el => a.includes(el));
};

declare global { interface Array<T> { unique(key?: string): any; } }
export const addUniqueToArray = () => {
    // Example addUniqueToArray
    // import * as tUtils from 'src/app/data/service/thoai.service';
    // tUtils.addUniqueToArray();
    // const array1 = ['Vijendra', 'Singh'];
    // const array2 = ['Singh', 'Shakya'];
    // // Merges both arrays and gets unique items
    // const array3 = array1.concat(array2).unique();
    // console.log(array3);

    Array.prototype.unique = function(key?: string) {
        const a = this.concat();
        if (!!key) {
            return [...new Map(a.map(item => [item[key], item])).values()];
        } else {
            return [...new Map(a.map(item => [item, item])).values()];
        }
    };
};

export const merge2ArraysWithoutDuplicate = (initialData, newData, key) => {
    const items = new Set(initialData.map(d => d[key]));
    const merged = initialData.concat(newData.filter(d => !items.has(d[key])));

    return merged;
};

export const newDate = () => {
    // domain get date must be enable Access-Control-Expose-Headers: Date
    // Example for k8s
    // add config below to ingress
    // nginx.ingress.kubernetes.io/configuration-snippet: |
    //  more_set_headers "Access-Control-Expose-Headers: Date";

    let domain = 'https://apiv2.vnptigate.vn/sy';
    let env = null;
    const deploymentVariables = JSON.parse(localStorage.getItem('deploymentVariables'));
    if (deploymentVariables) {
        env = deploymentVariables.configuration?.env;
    }
    domain = env?.domainGetDate || domain;
    const req = new XMLHttpRequest();
    req.open('OPTIONS', domain, false);
    req.send(null);
    const dateStr = req.getResponseHeader('Date');

    let date = new Date();
    if (!!dateStr) {
        date = new Date(Date.parse(dateStr));
    }

    return date;
};

export const generateObjectId = () => {
    // tslint:disable-next-line: no-bitwise
    const timestamp = (new Date().getTime() / 1000 | 0).toString(16);
    return timestamp + 'xxxxxxxxxxxxxxxx'.replace(/[x]/g, () => {
        // tslint:disable-next-line: no-bitwise
        return (Math.random() * 16 | 0).toString(16);
    }).toLowerCase();
};

export const hasOwnProperty = (object, property) => {
    if (!!object) {
        return object.hasOwnProperty(property);
    }
    return false;
};

export const nonNull = (object, property) => {
    return !!object && object.hasOwnProperty(property) && object[property] !== null && object[property] !== undefined && (!Array.isArray(object[property]) || object[property].length > 0);
};

export const isObject = (item) => {
    return (item && typeof item === 'object' && !Array.isArray(item) && item !== null);
};

export const mergeDeep = (target, source) => {
    if (isObject(target) && isObject(source)) {
        for (const key in source) {
            if (isObject(source[key])) {
                if (!target[key]) Object.assign(target, { [key]: {} });
                mergeDeep(target[key], source[key]);
            } else {
                Object.assign(target, { [key]: source[key] });
            }
        }
    }
    return target;
};

export const redirect = (router, url, params) => {
    // private router: Router,                              // import Router vào file .ts
    // url: 'procedure/assign-agency/' + this.procedureId   // url kiểu string
    // params:                                              // params kiểu object, có giá trị thì truyền như bên dưới, ngược lại truyền {}
    // const params = {
    //     queryParams: {
    //         procedure: this.procedureId
    //     }
    // }

    router.navigateByUrl('/', { skipLocationChange: true }).then(() =>
        router.navigate([url], params)
    );
};

export const reload = (router) => {
    redirect(router, getUrlWithoutParams(router), {
        queryParams: router.currentUrlTree.queryParams
    });
}

export const getUrlWithoutParams = (router) => {
    const urlTree = router.parseUrl(router.url);
    urlTree.queryParams = {};
    urlTree.fragment = null; // optional
    return urlTree.toString();
}

export const parseParams = (paramStr) => {
    const params: any = new URLSearchParams(paramStr);
    const paramsObj = {};

    for (const [key, value] of params) {
        paramsObj[key] = value;
    }

    return paramsObj;
}

// getFileMimeType
// @param {Object} the file object created by the input[type=file] DOM element.
// @return {Object} a Promise that resolves with the MIME type as argument or undefined
// if no MIME type matches were found.
export const getFileMimeType = (file) => {
    // Making the function async.
    return new Promise<any>(resolve => {
        const fileReader = new FileReader();
        fileReader.onloadend = (event: any) => {
            const byteArray = new Uint8Array(event.target.result);
            const mimeType = getMimeType(getFileHeader(byteArray));
            if (!!mimeType && mimeType !== 'Unsupported') {
                resolve(mimeType);
                return;
            }

            // This is only the case when the bytes have a readable character.
            const td = new TextDecoder('utf-8');
            const headerString = td.decode(byteArray);

            // Array to be iterated [<string signature>, <MIME type>]
            const mimeTypes = [
                // Images
                ['PNG', 'image/png'],
                // Audio
                ['ID3', 'audio/mpeg'], // MP3
                // Video
                ['ftypmp4', 'video/mp4'], // MP4
                ['ftypisom', 'video/mp4'], // MP4
                // HTML
                ['<!DOCTYPE html>', 'text/html'],
                // XML
                ['<?xml', 'text/xml'],
                // PDF
                ['%PDF', 'application/pdf']
                // Add the needed files for your case.
            ];

            // Iterate over the required types.
            // tslint:disable-next-line: prefer-for-of
            for (let i = 0; i < mimeTypes.length; i++) {
                // If a type matches we return the MIME type
                if (headerString.indexOf(mimeTypes[i][0]) > -1) {
                    resolve(mimeTypes[i][1]);
                    return;
                }
            }

            // If not is found we resolve with a blank argument
            resolve(null);

        };
        // Slice enough bytes to get readable strings.
        // I chose 32 arbitrarily. Note that some headers are offset by
        // a number of bytes.
        fileReader.readAsArrayBuffer(file.slice(0, 32));
    });

    function getFileHeader(byteArr) {
        const arr = byteArr.subarray(0, 4);
        let header = '';
        // tslint:disable-next-line: prefer-for-of
        for (let i = 0; i < arr.length; i++) {
            header += pad(arr[i].toString(16), 2);
        }
        return header;
    }

    function pad(num, size) {
        const s = '0000' + num;
        return s.substring(s.length - size);
    }

    function getMimeType(headerString) {
        let type = '';
        switch (headerString.toLowerCase()) {
            case '89504e47':
                type = 'image/png';
                break;
            case '47494638':
                type = 'image/gif';
                break;
            case 'ffd8ffe0':
                type = 'jpg';
                break;
            case 'ffd8ffe1':
                type = 'jpg';
                break;
            case 'ffd8ffdb':
                type = 'jpg';
                break;
            case 'ffd8ffe2':
                type = 'image/jpeg';
                break;
            case '25504446':
                type = 'pdf';
                break;
            case '7b5c7274': // 6631
                type = 'rtf';
                break;
            case '504b0304':
                type = 'zip archive (Office)'; // .docx, .xlsx
                break;
            case '504b0506':
                type = 'zip archive empty';
                break;
            case '504b0708':
                type = 'zip archive spanned';
                break;
            case '49492a00':
                type = 'TIF (little endian format)';
                break;
            case '4d4d002a':
                type = 'TIF (big endian format)';
                break;
            case 'd0cf11e0': // a1b11ae1
                type = 'Old Office Format'; // .doc, .xls
                break;
            default:
                type = 'Unsupported';
                break;
        }
        return type;
    }
};

export const sortObjectsByName = (objects: any[]) => {
    return objects.sort((a, b) => {
        const nameA = a.name;
        const nameB = b.name;

        // Định nghĩa thứ tự sắp xếp riêng dựa trên bảng mã Unicode của các ký tự trong tiếng Việt
        const collator = new Intl.Collator("vi", { sensitivity: "base" });
        return collator.compare(nameA, nameB);
    });
}

export const isAllowedAgency = (localStorage, agencyIds) => {
    const userAgency = JSON.parse(localStorage.getItem('userAgency'));

    if (!userAgency) {
        return false;
    }

    if (agencyIds.includes(userAgency.id)) {
        return true;
    }

    if (!!userAgency.parent && !!userAgency.parent.id && agencyIds.includes(userAgency.parent.id)) {
        return true;
    }

    return false;
}

export const getFutureDate = (numberDate, fromDate?) => {
    var nowDate = fromDate ? new Date(fromDate) : newDate();
    var futureDate = new Date(nowDate.getTime() + numberDate * 24 * 60 * 60 * 1000);

    var year = futureDate.getFullYear();
    var month = ('0' + (futureDate.getMonth() + 1)).slice(-2); // Tháng bắt đầu từ 0
    var day = ('0' + futureDate.getDate()).slice(-2);

    return year + '-' + month + '-' + day;
}

export const getPastDate = (numberDate, fromDate?) => {
    var nowDate = fromDate ? new Date(fromDate) : newDate();
    var pastDate = new Date(nowDate.getTime() - numberDate * 24 * 60 * 60 * 1000); // Lấy thời gian ở quá khứ

    var year = pastDate.getFullYear();
    var month = ('0' + (pastDate.getMonth() + 1)).slice(-2);
    var day = ('0' + pastDate.getDate()).slice(-2);

    return year + '-' + month + '-' + day;
}

export const removePTag = (s) =>{ // <p>x</p>
    return s.replace(/<\/?p>/g, '');
}
