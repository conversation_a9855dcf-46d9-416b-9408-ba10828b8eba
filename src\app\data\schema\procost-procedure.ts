export interface ProcostProcedureElement {
    id: string;
    type: {
        id: string;
        type: number;
        name: string;
    };
    procedure: {
        id: string;
    };
    monetaryUnit: {
        id: string;
        name: string;
    };
    decription: string;
    cost: number;
    status: number;
    isOutput: number;
    default: number;
    required: number;
    advance: [
        {
            applyMethod: {
                id: string;
            };
            cost: number;
            default: number;
            required: number;
        }
    ];
}
