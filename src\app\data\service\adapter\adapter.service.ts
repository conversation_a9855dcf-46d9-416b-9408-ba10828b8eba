import { Injectable } from '@angular/core';
import { HttpBackend, HttpClient, HttpHeaders } from '@angular/common/http';
import { ApiProviderService } from 'src/app/core/service/api-provider.service';
import { Observable } from 'rxjs';
import { EnvService } from 'src/app/core/service/env.service';
import { IRequestTanDanDoc } from '../../schema/tan-dan';
import { AgencyService } from '../basedata/agency.service';
import { DeploymentService } from '../deployment.service';

@Injectable({
  providedIn: 'root'
})
export class AdapterService {

  config = this.envService.getConfig();
  env = this.deploymentService.getAppDeployment()?.env;
  agencyAndAncestors = this.agencyService.getAgencyAndAncestors();
  configIdTNMTTriNam = this.deploymentService?.env?.OS_HBH?.configIdTNMTTriNam || null;
  confidIdLGSPCaMau = this.deploymentService?.env?.OS_CMU?.configIdLGSPCaMau || null;
  ConfigIDLGSPEoffice = this.deploymentService.getAppDeployment()?.ConfigIDLGSPEoffice ? this.deploymentService.getAppDeployment()?.ConfigIDLGSPEoffice : null; //IGATESUPP-110591
  private httpWithoutAuth: HttpClient;
  constructor(
    private envService: EnvService,
    private http: HttpClient,
    private apiProviderService: ApiProviderService,
    private agencyService: AgencyService,
    private deploymentService: DeploymentService,
    private httpBackend: HttpBackend,
  ) { 
    this.httpWithoutAuth = new HttpClient(this.httpBackend);
  }

  private adapterUrl = this.apiProviderService.getUrl('digo', 'adapter');
  
  getLGSPV2StatisticalDossiers(year: any,month: any,agency: any): Observable<any> {
    let headers = new HttpHeaders();
    agency = !!agency ? agency : "SGTVT61";
    const formData = new FormData();
    formData.append('yearDay', year);
    formData.append('mouthDay', month);
    formData.append('agency', agency);
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.post<any>(this.adapterUrl + '/service/transportation/statistical-dossiers', formData, {headers});
  }

  getLGSPV2Dossiers(start,end,mauBien?,fromDate?,toDate?,maThuTuc?,receiveFromDate?,receiveToDate?,releaseFromDate?,releaseToDate?,finishFromDate?,status?): Observable<any> {
    let headers = new HttpHeaders();
    const formData = new FormData();
    formData.append('Start', start);
    formData.append('End', end);
    formData.append('MauBien', mauBien);
    formData.append('FromDate', fromDate);
    formData.append('ToDate', toDate);
    formData.append('MaThuTuc', maThuTuc);
    formData.append('ReceiveFromDate', receiveFromDate);
    formData.append('ReceiveToDate', receiveToDate);
    formData.append('ReleaseFromDate', releaseFromDate);
    formData.append('ReleaseToDate', releaseToDate);
    formData.append('FinishFromDate', finishFromDate);
    formData.append('Status', status);
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.post<any>(this.adapterUrl + '/service/transportation/get-dossiers', formData, {headers});
  }

  getTransportationSeachDossier(code): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    const formData = new FormData();
    formData.append('mahoso', code);
    return this.http.post<any>(this.adapterUrl + '/service/transportation/--seach-dossier', formData, {headers}).pipe();
  }

  signDocumentBySmartCA(fileId: string, position: string, page: number, username: string, password: string, visibleType?: string, signature?: any, token?: string): Observable<any> {
    const URL = `${this.adapterUrl}/smart-ca/--sign`;
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    if(!visibleType){
      visibleType = "TEXT_ONLY";
    }
    const requestBody = {
      signatureFileId: fileId,
      position: position,
      page: page,
      username: username,
      password: password,
      visibleType: visibleType,
      signature: signature,
      token: token
    }
    return this.http.post(URL, requestBody,{ headers });
  }

  getSmartCAToken(username: string, password: string): Observable<any> {
    password = encodeURIComponent(password);
    const URL = `${this.adapterUrl}/smart-ca/--get-token?username=${username}&password=${password}`;
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    return this.http.get(URL, { headers, responseType: 'text' });
  }

  getVGCAAdapterCallBackUrl(fileId,filename, accountId?):string{
    if(fileId == undefined || fileId == null) fileId = "";
    if(filename == undefined || filename == null) filename = "";
    const userId = accountId || "";
    return this.adapterUrl + `/vgca/--upload-signed-file?file-id=${fileId}&name=${filename}&user-id=${userId}`;
  }

  getConfigTanDan(agencyId:string):Observable<any>{
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    const URL = `${this.adapterUrl}/lgsp-tandan/--find-config-id?subsystem-id=5f7c16069abb62f511880003&agency-id=${agencyId}`;
    return this.http.get(URL,{ headers });
  }

  sendTanDanDoc(params: String, body:IRequestTanDanDoc):Observable<any>{
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    const URL = `${this.adapterUrl}/lgsp-tandan/send-document${params}`;
    return this.http.post(URL, body, { headers });
  }

  getCitizenIfo(indentityNumber?:string, fullname?:string, birthday?:string, eformId?:string):Observable<any>{
    this.getClientIp();
    let headers = new HttpHeaders();
    const subsystemId = this.config?.subsystemId;
    const agency = this.agencyService.getAgency(true);
    const agencyId = agency.rootAgencyId;
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    let URL = `${this.adapterUrl}/citizen/--info?`;
    URL += `agency-id=${agencyId}`;
    URL += `&subsystem-id=${subsystemId}`;
    URL += `&indentity-number=${indentityNumber}`;
    URL += `&fullname=${fullname}`;
    URL += `&birthday=${birthday}`;
    URL += eformId?`&eform-id=${eformId}`:``;
    return this.http.get(URL,{ headers });
  }

  sendLLTPTanDan(search, requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.post<any>(this.adapterUrl + '/lgsp-tandan-jr/--send' + search, requestBody, { headers }).pipe();
  }

  sendHTTPTanDan(requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.post<any>(this.adapterUrl + '/lgsp-tandan/--register-civil', requestBody, { headers }).pipe();
  }

  sendLLTPMinhTue(search, requestBody, code): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.post<any>(this.adapterUrl + '/lgsp-minhtue-cr/--send' + search + '&code=' + code, requestBody, { headers }).pipe();
  }

  sendHTTPMinhTue(requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.post<any>(this.adapterUrl + '/lgsp-minhtue/--register-civil', requestBody, { headers }).pipe();
  }
  //get LLTP Minh Tue Category - KGG OS
  getLLTPMinhTueCategory(type: number): Observable<any>{
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers.append('Access-Control-Allow-Origin', '*');
    const agencyId = JSON.parse(localStorage.getItem('userAgency'))?.id;
    const subsystemId = this.env?.subsystem?.id;
    return this.http.get(this.adapterUrl + `/lgsp-minhtue-cr/--tra-danh-muc?infoType=${type}&agency-id=${agencyId}&subsystem-id=${subsystemId}`, { headers });
  }
  //KGG OS

  getLLTPCategory(type: number): Observable<any>{
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers.append('Access-Control-Allow-Origin', '*');
    const agencyId = JSON.parse(localStorage.getItem('userAgency'))?.id;
    const subsystemId = this.env?.subsystem?.id;
    return this.http.get(this.adapterUrl + `/lgsp-tandan-jr/--category?infoType=${type}&agency-id=${agencyId}&subsystem-id=${subsystemId}`, { headers });
  }

  getLosSms(page,size,agencyId, keyword,fromDate,toDate,subsystemId):Observable<any>{
    let URL = `${this.adapterUrl}/sms-brandname/--log-sms`;
    URL += `?page=${page}`;
    URL += `&size=${size}`;
    URL += `&agency-id=${agencyId}`;
    URL += keyword?`&keyword=${keyword}`:``;
    URL += fromDate?`&form-date=${fromDate}`:``;
    URL += toDate?`&to-date=${toDate}`:``;
    URL += subsystemId?`&subsystem-id=${subsystemId}`:``;
    let headers = new HttpHeaders();
    return this.http.get(URL, { headers });
  }

  getLosSmsQBH(page,size,agencyId,fromDate,toDate,status,keyword):Observable<any>{
    let URL = `${this.adapterUrl}/sms-brandname/--statistics-sms--qbh`;
    //let URL = "http://localhost:8096/sms-brandname/--statistics-sms--qbh";
    URL += `?page=${page}`;
    URL += `&size=${size}`;
    URL += `&agency-id=${agencyId}`;
    URL += fromDate?`&form-date=${fromDate}`:``;
    URL += toDate?`&to-date=${toDate}`:``;
    URL += status?`&status=${status}`:``;
    URL += keyword?`&keyword=${keyword}`:``;
    let headers = new HttpHeaders();
    return this.http.get(URL, { headers });
  }
  getLosSmsQNI(page, size, agencyId, keyword, fromDate, toDate, subsystemId): Observable<any> {
    let URL = `${this.adapterUrl}/sms-brandname/--log-sms-qni`;
    URL += `?page=${page}`;
    URL += `&size=${size}`;
    URL += `&agency-id=${agencyId}`;
    URL += keyword ? `&keyword=${keyword}` : ``;
    URL += fromDate ? `&form-date=${fromDate}` : ``;
    URL += toDate ? `&to-date=${toDate}` : ``;
    URL += subsystemId ? `&subsystem-id=${subsystemId}` : ``;
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accep-Language', localStorage.getItem('language'));
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.get(URL);
  }

  getLosSmsQNIV2(page, size, agencyId, keyword, fromDate, toDate, subsystemId): Observable<any> {
    let URL = `${this.adapterUrl}/sms-brandname/--log-sms-qni-v2`;
    URL += `?page=${page}`;
    URL += `&size=${size}`;
    URL += `&agency-id=${agencyId}`;
    URL += keyword ? `&keyword=${keyword}` : ``;
    URL += fromDate ? `&form-date=${fromDate}` : ``;
    URL += toDate ? `&to-date=${toDate}` : ``;
    URL += subsystemId ? `&subsystem-id=${subsystemId}` : ``;
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accep-Language', localStorage.getItem('language'));
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.get(URL);
  }

  resendErrorSmsQni(dossierCodes : String[]): Observable<any> {
    let URL = `${this.adapterUrl}/v2/sms-brandname/--resend-error-sms-qni`;
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accep-Language', localStorage.getItem('language'));
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.post(URL, {dossierCodes});
  }

  closeErrorSmsQni(dossierCodes : String[]): Observable<any> {
    let URL = `${this.adapterUrl}/v2/sms-brandname/--updated-error-sms-qni`;
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accep-Language', localStorage.getItem('language'));
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.put(URL, {dossierCodes});
  }


  exportLosSms(agencyId, keyword,fromDate,toDate,subsystemId):Observable<any>{
    let URL = `${this.adapterUrl}/sms-brandname/--export-log-sms`;
    URL += `?agency-id=${agencyId}`;
    URL += keyword?`&keyword=${keyword}`:``;
    URL += fromDate?`&form-date=${fromDate}`:``;
    URL += toDate?`&to-date=${toDate}`:``;
    URL += subsystemId?`&subsystem-id=${subsystemId}`:``;
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    // headers.append('Access-Control-Allow-Origin', '*');
    //headers = headers.set('Content-Type', 'application/octet-stream');
    return this.http.get(URL);
  }
  exportLosSmsQBH(agencyId,fromDate,toDate,status,keyword):Observable<any>{
    let URL = `${this.adapterUrl}/sms-brandname/--export-log-sms-qbh`;
    //let URL = "http://localhost:8096/sms-brandname/--export-log-sms-qbh";
    URL += `?agency-id=${agencyId}`;
    //URL += '?agency-id=6463339cc401f717760af031,642d299699897648ca5b0f34,64633424c401f717760af035,642d299699897648ca5b0f3e'
    URL += fromDate?`&form-date=${fromDate}`:``;
    URL += toDate?`&to-date=${toDate}`:``;
    URL += status?`&status=${status}`:``;
    URL += keyword?`&keyword=${keyword}`:``;
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    // headers.append('Access-Control-Allow-Origin', '*');
    //headers = headers.set('Content-Type', 'application/octet-stream');
    return this.http.get(URL);
  }
  sendSmsDirect(phones, content, agencies):Observable<any>{
    const  URL = `${this.adapterUrl}/sms-brandname/--send-batch-and-write-log`
    const data = {
      agencyId: agencies?.agency?.id,
      subsystemId: `5f7c16069abb62f511880003`,
      content: content,
      phoneNumber: phones,
      extend: {
        agencies: this.agencyAndAncestors
      }
    }

    let headers = new HttpHeaders();
    return this.http.post(URL,data, { headers });
  }

  getUserOrAgancyTanDan(page,size,configId, keyword,code):Observable<any>{
    let URL = `${this.adapterUrl}/lgsp-tandan/users-or-agancies`
    URL += `?page=${page}`;
    URL += `&size=${size}`;
    URL += `&config-id=${configId}`;
    URL += keyword?`&keyword=${keyword}`:``;
    URL += code?`&code=${code}`:``;
    let headers = new HttpHeaders();
    return this.http.get(URL, { headers });
  }

  sendLLTPLgspHcm(search, requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers.append('Access-Control-Allow-Origin', '*');
    let URL = '/lgsp-hcm-lltp/sync-nhan-hoso-dangky';
    if(this.deploymentService?.env?.OS_HGI?.isRequestHGI==1){
      URL='/lgsp-hgi-lltp/sync-nhan-hoso-dangky';
    }
    if(this.deploymentService?.env?.OS_HGI?.isRequestHGI==2){
      URL='/lgsp-hgi-lltp/sync-send-dossier';
    }
    //IGATESUPP-94895
    if(this.deploymentService?.env?.OS_CMU?.isRequestCMU){
      URL='/lgsp-cmu-lltp/sync-send-dossier';
    }
    //IGATESUPP-94965
    if(this.deploymentService.getAppDeployment()?.sendLgspLltpHcmSync){
      URL = '/lgsp-hcm-lltp/sync-nhan-hoso-dangky/--sync';
    }
    return this.http.post<any>(this.adapterUrl + URL + search, requestBody, { headers }).pipe();
  }

  checkTraTrangThaiHS(requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.post<any>(this.adapterUrl + '/lgsp-hcm-lltp/traTrangThaiHoSo', requestBody, { headers }).pipe();
  }

  sendHTTPLgspHcm(search, requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.post<any>(this.adapterUrl + '/lgsp-hcm-civil-status/sync-nhan-hoso-dangky' + search, requestBody, { headers }).pipe();
  }

  danhDauNhanHSThanhCongLgspHcm(search, requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.post<any>(this.adapterUrl + '/lgsp-hcm-lltp/--mark-successful-received-dossier' + search, requestBody, { headers }).pipe();
  }

  getCertNEAC(body){
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.post<any>(this.adapterUrl + '/neac-sign/--get-certificate', body , { headers });
  }

  signNEAC(body){
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.post<any>(this.adapterUrl + '/neac-sign/--sign', body , { headers });
  }

  reGetFileDVCQG(configId, deloymentId, requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.post<any>(this.adapterUrl + '/npadsvc/' + configId + '/' + deloymentId + "/--get-file-dvcqg",
     requestBody, { headers }).pipe();
  }

  getStatusDossierSyncDVCQG(dossierId:string):Observable<any>{
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    const URL = `${this.adapterUrl}/integrated-event/--get-status-dossier-sync-dvcqg?dossier-id=${dossierId}`;
    return this.http.get(URL,{ headers });
  }

  getCsdlqgdcKtm(searchString):Observable<any>{
    this.getClientIp();
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    let URL = `${this.adapterUrl}`; //"https://apikontum.digigov.vn/ad/"
    URL += searchString;
    return this.http.get(URL,{ headers });
  }


  getFamilyByBHXHCode(code: string, agency: string) {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    const subsystemId = this.env?.subsystem?.id;
    return this.http.post<any>(
      this.adapterUrl +
        '/bhxh/--get-tthgd-by-masobhxh?subsystem-id=' +
        subsystemId +
        `&agency-id=${agency}`,
      { maSoBhxh: code },
      { headers }
    );
  }

  getInfoFamily(body: any, agency: string) {
    const subsystemId = this.env?.subsystem?.id;
    let configid = '';
    if (this.env.OS_BDG?.isEnableOs) {
      configid = '&config-id=' + this.env.OS_BDG.configLGSPMinhTueId;
    }
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.post<any>(
      this.adapterUrl +
        '/bhxh/--get-info-tthgd-bdg?subsystem-id=' +
        subsystemId +
        `&agency-id=${agency}` +
        configid,
      body,
      { headers }
    );
  }

  getBHXHCode(body: any, agency: string) {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    const subsystemId = this.env?.subsystem?.id;

    return this.http.post<any>(
      this.adapterUrl +
        '/bhxh/--get-masobhxh-by-tieuchi?subsystem-id=' +
        subsystemId +
        `&agency-id=${agency}`,
      body,
      { headers }
    );
    // tslint:disable-next-line:max-line-length
    // return this.http.post<any>(this.adapterUrl + '/bhxh/--get-masobhxh-by-tieuchi?subsystem-id=' + this.padsvcSubsystem + `&agency-id=${agency}`, body, { headers });
  }


  syncDossierStatusKTMToBLDTBXH(dossier:any):Observable<any>{
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.set('Content-Type', 'application/json');
    const URL = `${this.adapterUrl}/btxh-ktm/--push-sync-status-dossier`;
    return this.http.post(URL, dossier, { headers });
  }

  syncDossierResultKTMToBLDTBXH(dossier:any):Observable<any>{
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.set('Content-Type', 'application/json');
    const URL = `${this.adapterUrl}/btxh-ktm/--push-sync-dossier`;
    return this.http.post(URL, dossier, { headers });
  }

  sendDossierConnectTo(dossierId) :Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.append('Content-Type', 'application/json');
    return this.http.get(this.adapterUrl + '/bdg-connect/sendDossierTo?dossier-id='+ dossierId, { headers }).pipe();
  }

  updateProcessDossierConnectTo(dossierId) :Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.append('Content-Type', 'application/json');
    return this.http.get(this.adapterUrl + '/bdg-connect/updateProcessDossierTo?dossier-id='+ dossierId, { headers }).pipe();
  }

  postDossierConnectBxdDBN(dossierId): Observable<any>{
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    if (this.deploymentService.env?.OS_HBH?.isConnectBxdHbh == true) {
      return this.http.post(this.adapterUrl + '/hbh-connect-bxd/--send-dossier?dossier-id='+ dossierId, { headers }).pipe();
    }
    return this.http.post(this.adapterUrl + '/dbn-connect-bxd/--send-dossier?dossier-id='+ dossierId, { headers }).pipe();
  }

  postProcessingDossierConnectBxdDBN(dossierId): Observable<any>{
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    if (this.deploymentService.env?.OS_HBH?.isConnectBxdHbh == true) {
      return this.http.post(this.adapterUrl + '/hbh-connect-bxd/--update-process-dossier?dossier-id='+ dossierId, { headers }).pipe();
    }
    return this.http.post(this.adapterUrl + '/dbn-connect-bxd/--update-process-dossier?dossier-id='+ dossierId, { headers }).pipe();
  }

  getMappingDataKTM(dataType, sourceID): Observable<any> {
    const URL = `${this.adapterUrl}/mapping-data?data-type=${dataType}&source-id=${sourceID}`;
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(URL, { headers });
  }

  syncDossierStatusToBLDTBXH(dossier:any):Observable<any>{
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.set('Content-Type', 'application/json');
    const URL = `${this.adapterUrl}/btxh/--push-sync-status-dossier`;
    return this.http.post(URL, dossier, { headers });
  }

  syncDossierResultToBLDTBXH(dossier:any):Observable<any>{
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.set('Content-Type', 'application/json');
    const URL = `${this.adapterUrl}/btxh/--push-sync-dossier`;
    return this.http.post(URL, dossier, { headers });
  }

  getMappingData(dataType, sourceID): Observable<any> {
    const URL = `${this.adapterUrl}/mapping-data?data-type=${dataType}&source-id=${sourceID}`;
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(URL, { headers });
  }

  getCsdldc034(param):Observable<any>{
    this.getClientIp();
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    let URL = `${this.adapterUrl}`; //"https://xacthuc.kontum.gov.vn/auth"
    URL += `/family/--info?`;
    URL += param;
    return this.http.get(URL,{ headers });
  }
  getCsdldc033(param):Observable<any>{
    this.getClientIp();
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    let URL = `${this.adapterUrl}`; //"https://xacthuc.kontum.gov.vn/auth"
    URL += `/auth/--info?`;
    URL += param;
    return this.http.get(URL,{ headers });
  }


  getAPIDanhMucMaDinhDanhCap1(userToken, level): Observable<any>{
    let headers = new HttpHeaders();
    let tokenBearer:any = 'Bearer ' + userToken;
    headers = headers.append('Authorization', 'Bearer ' + tokenBearer);
    headers = headers.append('Content-Type', 'application/json');
    const URL = `${this.adapterUrl}/lgsp-dmdungchung-btttt/getMaDinhDanhCap1?level=${level}`;
    return this.http.get(URL, {headers});
  }

  postRequestListDossierTransportation(subfix, requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.post(this.adapterUrl + '/lgsp-minhtue/--request-list-dossier-transportation' + subfix, requestBody, { headers }).pipe();
  }

  getDetailDossierTransportation(subfix, idDossier): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.get(this.adapterUrl + '/lgsp-minhtue/--search-dossier-transportation' + subfix + '&id-dossier=' + idDossier, { headers }).pipe();
  }

  getStatisticalTransportation(subfix, month, year): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.get(this.adapterUrl + '/lgsp-minhtue/--statistical-transportation' + subfix + '&month=' + month + '&year=' + year, { headers }).pipe();
  }

  addTextToPDF(body){
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.post<any>(this.adapterUrl + '/pdf/--add-text', body , { headers });
  }

  changeFileSign(body){
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.post<any>(this.adapterUrl + '/pdf/--change-file-sign', body , { headers });
  }

  fileInfo(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get<any>(this.apiProviderService.getUrl('digo', 'fileman') + '/wopi/files/' + id, {headers}).pipe();
  }

  getPaperNation(bodyCall): Observable<any>{
    let headers = new HttpHeaders();
    headers = headers.set('Cache-Control', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    return this.http.post(this.adapterUrl + '/npadsvc/--personal-origin', bodyCall, {headers});
  }

  previewVietInfoReceipt(body): Observable<any>{
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    return this.http.post(this.adapterUrl + '/hcm-vietinfo-receipt/preview', body, { headers , responseType: 'blob'}).pipe();
  }

  issueVietInfoReceipt(body): Observable<any>{
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    return this.http.post(this.adapterUrl + '/hcm-vietinfo-receipt/issue', body, { headers , responseType: 'blob'}).pipe();
  }

  viewVietInfoReceipt(body): Observable<any>{
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    return this.http.post(this.adapterUrl + '/hcm-vietinfo-receipt/view', body, { headers , responseType: 'blob'}).pipe();
  }

  cancelVietInfoReceipt(body): Observable<any>{
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    return this.http.post(this.adapterUrl + '/hcm-vietinfo-receipt/cancel', body, { headers }).pipe();
  }

  getDetailIntegratedConfig(agencyId, integrationServiceId, subsystemId): Observable<any> {
    const param = '?agency-id=' + agencyId + '&integration-service-id=' + integrationServiceId + '&subsystem-id=' + subsystemId;
    return this.http.get(this.adapterUrl + '/integrated-configuration/' + param);
  }
  // API Dong bo ho so BXD
  syncDossierConstructKTM(config, requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.set('Content-Type', 'application/json');
    return this.http.post<any>(this.adapterUrl + '/construct/--sync-info-dossier?' + "config-id="+ config, requestBody, { headers });
    }
  //all api BXD from LGSP
  syncLicenseMOC(param,datas): Observable<any>{
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.set('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');
   return this.http.post<any>(this.adapterUrl+ '/construct/--sync-license?' + param, datas, { headers });

  }

  getDossierListGTVT(searchString,GTVTQNI,GTVTAGG): Observable<any>{
    let headers = new HttpHeaders();
    headers = headers.set('Cache-Control', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    let url = '/gtvt/dossier-list';
    if(GTVTQNI){
      url = '/gtvt/dossier-list-qni';
    }
    if(GTVTAGG){
      url = '/gtvt/dossier-list-agg';
    }
    // return this.http.get<any>('http://localhost:8084' + '/gtvt/dossier-list' + searchString, {headers}).pipe();
    return this.http.get<any>(this.adapterUrl + url + searchString, {headers}).pipe();

  }

  getDossierDetailGTVT(searchString,GTVTQNI,GTVTAGG): Observable<any>{
    let headers = new HttpHeaders();
    headers = headers.set('Cache-Control', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    let url = '/gtvt/dossier-detail';
    if(GTVTQNI){
      url = '/gtvt/dossier-detail-qni';
    }
    if(GTVTAGG){
      url = '/gtvt/dossier-detail-agg';
    }
    // return this.http.get<any>('http://localhost:8084' + '/gtvt/dossier-detail' + searchString, {headers}).pipe();
    return this.http.get<any>(this.adapterUrl + url + searchString, {headers}).pipe();

  }

  getDossierStatisticGTVT(searchString,GTVTQNI,GTVTAGG): Observable<any>{
    let headers = new HttpHeaders();
    headers = headers.set('Cache-Control', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    let url = '/gtvt/dossier-statistic';
    if(GTVTQNI){
      url = '/gtvt/dossier-statistic-qni';
    }
    if(GTVTAGG){
      url = '/gtvt/dossier-statistic-agg';
    }
    // return this.http.get<any>('http://localhost:8084' + '/gtvt/dossier-statistic' + searchString, {headers}).pipe();
    return this.http.get<any>(this.adapterUrl + url + searchString, {headers}).pipe();
  }

  synchronizeDossierByCodeGTVT(listcode,GTVTQNI,GTVTAGG): Observable<any>{
    let headers = new HttpHeaders();
    headers = headers.set('Cache-Control', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    let url = '/gtvt/dossier-sync-by-code';
    if(GTVTQNI){
      url = '/gtvt/dossier-sync-by-code-qni';
    }
    if(GTVTAGG){
      url = '/gtvt/dossier-sync-by-code-agg';
    }
    // return this.http.post('http://localhost:8084' + '/gtvt/dossier-sync-by-code',listcode, {headers});
    return this.http.post(this.adapterUrl + url ,listcode, {headers});
  }

  syncDossierGTVT(searchString,GTVTQNI,GTVTAGG): Observable<any>{
    let headers = new HttpHeaders();
    headers = headers.set('Cache-Control', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    let url = '/gtvt/dossier-sync';
    if(GTVTQNI){
      url = '/gtvt/dossier-sync-qni';
    }
    if(GTVTAGG){
      url = '/gtvt/dossier-sync-agg';
    }
    // return this.http.get<any>('http://localhost:8084' + '/gtvt/dossier-sync' + searchString, {headers}).pipe();
    return this.http.get<any>(this.adapterUrl + url + searchString, {headers}).pipe();
  }

  insertDossierListGTVT(searchString): Observable<any>{
    let headers = new HttpHeaders();
    headers = headers.set('Cache-Control', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    let url = '/gtvt/dossier-insert-agg';
    return this.http.get<any>(this.adapterUrl + url + searchString, {headers}).pipe();
  }

  insertDossierDetailGTVT(searchString): Observable<any>{
    let headers = new HttpHeaders();
    headers = headers.set('Cache-Control', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    let url = '/gtvt/dossier-insert-detail-agg';
    return this.http.get<any>(this.adapterUrl + url + searchString, {headers}).pipe();

  }

  traCuuHoSoGPLXHGG(body): Observable<any>{
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    const URL = `${this.adapterUrl}/gplx-gtvt-hgg/--tra-cuu-ho-so?config-id=${this.env?.OS_HGG?.configIdGPLXBGTVT}`;
    return this.http.post(URL, body, { headers });
  }

  danhsachHoSoGPLXHGG(body): Observable<any>{
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    const URL = `${this.adapterUrl}/gplx-gtvt-hgg/--danh-sach-ho-so?config-id=${this.env?.OS_HGG?.configIdGPLXBGTVT}`;
    return this.http.post(URL, body, { headers });
  }

  getCredentialsList(body): Observable<any>{
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    const agencyId = JSON.parse(localStorage.getItem('userAgency'))?.id;
    const subsystemId = this.env?.subsystem?.id;
    return this.http.post<any>(this.adapterUrl + `/digital-signature/credentialsList?agency-id=${agencyId}&subsystem-id=${subsystemId}`, body , { headers });
  }

  preSignFile(body, username, credentialID): Observable<any>{
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers.set('Content-Type', null);
    headers.set('Accept', "multipart/form-data");
    const agencyId = JSON.parse(localStorage.getItem('userAgency'))?.id;
    const subsystemId = this.env?.subsystem?.id;
    return this.http.post<any>(this.adapterUrl + `/digital-signature/preSignFile?agency-id=${agencyId}&subsystem-id=${subsystemId}&username=${username}&credentialID=${credentialID}`, body , { headers });
  }

  exeSignFile(body): Observable<any>{
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    const agencyId = JSON.parse(localStorage.getItem('userAgency'))?.id;
    const subsystemId = this.env?.subsystem?.id;
    return this.http.post<any>(this.adapterUrl + `/digital-signature/exeSignFile?agency-id=${agencyId}&subsystem-id=${subsystemId}`, body , { headers });
  }

  downloadFile(body, params): Observable<any>{
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    const agencyId = JSON.parse(localStorage.getItem('userAgency'))?.id;
    const subsystemId = this.env?.subsystem?.id;
    return this.http.post<any>(this.adapterUrl + `/digital-signature/downloadFile?agency-id=${agencyId}&subsystem-id=${subsystemId}&idFile=${params.idFile}&responseType=${params.responseType}`, body, { headers, responseType: 'blob' as 'json' });
  }

  sendLGSPTriNam(requestBody, path: string): Observable<any> {// LGSP trục trí nam
        let headers = new HttpHeaders();
        headers = headers.set('Content-Type', 'application/json');
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        headers.append('Access-Control-Allow-Origin', '*');
        return this.http.post<any>(this.adapterUrl + '/lgsp-hbh/trinam/' + path, requestBody, { headers }).pipe();
  }
  postLGSPTriNam(requestBody, path: string): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.post<any>(this.adapterUrl + '/' + path , requestBody, { headers }).pipe();
  }

  //Đồng bộ hồ sơ sang LGSP HCM
  syncDossierLGSPHCM(body): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    return this.http.post<any>(this.adapterUrl + '/lgsp-hcm-dossier/--sync-dossier', body, { headers });
  }


  sendTNMTTriNam(dossierId: string) {
    return this.sendLGSPTriNam(
        {},
        'TNMT/CapNhatThongTinHoSo?log-service=TNMT&dossierId=' + dossierId + '&config-id=' + this.configIdTNMTTriNam
    ).toPromise();
  }

  UpdateStatusDossierLGSPQNM(body: any): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.post<any>(this.adapterUrl + '/lgsp-minhtue-tnmt/cap-nhat-ho-so', body, { headers }).pipe();
  }

  downloadEform(eformId: any): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.append('Content-Type', 'application/json');
    let URL = `${this.adapterUrl}/eform-connect/${eformId}/--download`;
    return this.http.get(URL, { headers, responseType: 'blob'});
  }

  getListVanBanIofficeV4(requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers.append('Access-Control-Allow-Origin', '*');
    const userAgency = JSON.parse(localStorage.getItem('userAgency'));
    let rootAgencyId: any = '';
    let parentAgencyId = '';
    if (!!userAgency) {
      rootAgencyId = userAgency.id;
      if (!!userAgency.parent) {
        parentAgencyId = userAgency.parent.id;
      }
    }

    let params = '?agency-id=' + parentAgencyId + '&config-id=' + this.confidIdLGSPCaMau;
    // return this.http.post<any>('http://localhost:8080/lgsp-cmu/--danh-sach-vbdi-dph-lt-igate' + params, requestBody, { headers }).pipe();
    return this.http.post<any>(this.adapterUrl + '/lgsp-cmu/--danh-sach-vbdi-dph-lt-igate' + params, requestBody, { headers }).pipe();
  }

  //IGATESUPP-110591
  getListVanBanIofficeHgi(requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers.append('Access-Control-Allow-Origin', '*');
    const userAgency = JSON.parse(localStorage.getItem('userAgency'));
    let rootAgencyId: any = '';
    let parentAgencyId = '';
    if (!!userAgency) {
      rootAgencyId = userAgency.id;
      if (!!userAgency.parent) {
        parentAgencyId = userAgency.parent.id;
      }
    }
    let params = '?agency-id=' + parentAgencyId + '&config-id=' + this.ConfigIDLGSPEoffice;
    // return this.http.post<any>('http://localhost:8080/lgsp-hgi/--list-vanban'+ params, requestBody, { headers }).pipe();
    return this.http.post<any>(this.adapterUrl + '/lgsp-hgi/--list-vanban' + params, requestBody, { headers }).pipe();
  }

  getTotalVanBanIofficeHgi(requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers.append('Access-Control-Allow-Origin', '*');
    const userAgency = JSON.parse(localStorage.getItem('userAgency'));
    let rootAgencyId: any = '';
    let parentAgencyId = '';
    if (!!userAgency) {
      rootAgencyId = userAgency.id;
      if (!!userAgency.parent) {
        parentAgencyId = userAgency.parent.id;
      }
    }
    let params = '?agency-id=' + parentAgencyId + '&config-id=' + this.ConfigIDLGSPEoffice;
    // return this.http.post<any>('http://localhost:8080/lgsp-hgi/--total-vanban'+ params, requestBody, { headers }).pipe();
    return this.http.post<any>(this.adapterUrl + '/lgsp-hgi/--total-vanban' + params, requestBody, { headers }).pipe();
  }

  getFileVanBanIofficeV4(maVanBanDi): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.append('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers.append('Access-Control-Allow-Origin', '*');
    const userAgency = JSON.parse(localStorage.getItem('userAgency'));
    let rootAgencyId: any = '';
    let parentAgencyId = '';
    if (!!userAgency) {
      rootAgencyId = userAgency.id;
      if (!!userAgency.parent) {
        parentAgencyId = userAgency.parent.id;
      }
    }

    let params = '?agency-id=' + parentAgencyId + '&config-id=' + this.confidIdLGSPCaMau + '&maVanBanDi=' + maVanBanDi;

    // return this.http.post<any>('http://localhost:8080/lgsp-cmu/--danh-sach-file-lien-thong-igate' + params, { headers }).pipe();
    return this.http.post<any>(this.adapterUrl + '/lgsp-cmu/--danh-sach-file-lien-thong-igate' + params, { headers }).pipe();
  }

  // IGATESUPP-110591
  getFileVanBanIofficeHgi(requestBody): Observable<any> {
    console.log("requestBody",requestBody)
    let headers = new HttpHeaders();
    headers = headers.append('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers.append('Access-Control-Allow-Origin', '*');
    const userAgency = JSON.parse(localStorage.getItem('userAgency'));
    let rootAgencyId: any = '';
    let parentAgencyId = '';
    if (!!userAgency) {
      rootAgencyId = userAgency.id;
      if (!!userAgency.parent) {
        parentAgencyId = userAgency.parent.id;
      }
    }

    let params = '?agency-id=' + parentAgencyId + '&config-id=' + this.ConfigIDLGSPEoffice;

    // return this.http.post<any>('http://localhost:8080/lgsp-hgi/--danh-sach-file-hgi' + params, requestBody, { headers }).pipe();
    return this.http.post<any>(this.adapterUrl + '/lgsp-hgi/--danh-sach-file-hgi' + params, requestBody, { headers }).pipe();
  }

  sendCSDLNganhCongThuongCmu(requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.post<any>(this.adapterUrl + '/csdl-cong-thuong/--tiep-nhan-ho-so', requestBody, { headers }).pipe();
  }

  getEntDossiersKGG(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');
    // return this.http.get('http://localhost:8080' + '/lgsp-kgg/--get-ent-dossiers-kgg' + searchString, { headers }).pipe();
    return this.http.get(this.adapterUrl + '/lgsp-kgg/--get-ent-dossiers-kgg' + searchString, { headers }).pipe();
  }

  getCoopDossiersKGG(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');
    // return this.http.get('http://localhost:8080' + '/lgsp-kgg/--get-coop-dossiers-kgg' + searchString, { headers }).pipe();
    return this.http.get(this.adapterUrl + '/lgsp-kgg/--get-coop-dossiers-kgg' + searchString, { headers }).pipe();
  }

  getHouseDossiersKGG(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');
    // return this.http.get('http://localhost:8080' + '/lgsp-kgg/--get-house-dossiers-kgg' + searchString, { headers }).pipe();
    return this.http.get(this.adapterUrl + '/lgsp-kgg/--get-house-dossiers-kgg' + searchString, { headers }).pipe();
  }

  getDanhSachHoSoGPLX(params): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.get(this.adapterUrl + '/lgsp-hcm-gplx/--danhSachHoSo' + params, { headers }).pipe();
  }

  getTraCuuHoSoGPLX(params): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.get(this.adapterUrl + '/lgsp-hcm-gplx/--traCuuHoSo' + params, { headers }).pipe();
  }

  sendDrivingLicenseAll(requestBody: any, paramsUrl : any): Observable<any> {
    if (paramsUrl == null && paramsUrl == undefined) {
      paramsUrl = "/service/driving-license/dossier-list";
    }
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.post<any>(this.adapterUrl + paramsUrl, requestBody, { headers }).pipe();
  }

  getDrivingLicenseQni(params :any): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.get(this.adapterUrl + '/gplxqni/--search-dossier-gplx' + params, { headers }).pipe();
  }

  getDrivingLicenseDetailQni(params :any): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.get(this.adapterUrl + '/gplxqni/--search-detail-dossier-gplx' + params, { headers }).pipe();
  }

  getDrivingLicenseFileQni(params :any): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.get(this.adapterUrl + '/gplxqni/--search-file-dossier-gplx' + params, { headers }).pipe();
  }

  getLayTaiLieuHoSo(params): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.get(this.adapterUrl + '/lgsp-hcm-gplx/--layTaiLieuHoSo' + params, { headers, responseType: 'blob' });
  }

  //IGATESUPP-83486 - Tích hợp đăng ký doanh nghiệp qua trục LGSP Cà Mau
  getEntDossiersCMU(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');
    // return this.http.get('http://localhost:8080' + '/lgsp-cmu/--get-ent-dossiers-cmu' + searchString, { headers }).pipe();
    return this.http.get(this.adapterUrl + '/lgsp-cmu/--get-ent-dossiers-cmu' + searchString, { headers }).pipe();
  }

  getCoopDossiersCMU(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.get(this.adapterUrl + '/lgsp-cmu/--get-coop-dossiers-cmu' + searchString, { headers }).pipe();
  }

  getHouseDossiersCMU(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.get(this.adapterUrl + '/lgsp-cmu/--get-house-dossiers-cmu' + searchString, { headers }).pipe();
  }
  syncDossierTNMTQni(configId: String, dossierId: String): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');

    const params = `?config-id=${configId}&dossier-id=${dossierId}`;

    return this.http.post<any>(this.adapterUrl + '/intergrated-tnmt-qni/--sync-dossier' + params, { headers });
  }

  getInfoBusinessRegistrationEnterpriseLGSPHCM(msdn):Observable<any>{
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));

    const params = `?msdn=${msdn}`;

    const URL = this.adapterUrl + '/lgsp-dkdn/--layThongTinDangKyKinhDoanhDoanhNghiep' + params;
    return this.http.get(URL,{ headers });
  }

  getInfoBusinessEnterpriseLGSPHCM(msdn): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));

    const params = `?msdn=${msdn}`;

    const URL = this.adapterUrl + '/lgsp-dkdn/--layThongTinDoanhNghiep' + params;
    return this.http.get(URL,{ headers });
  }

  getInfoBusinessHouseholdLGSPHCM(msdn): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));

    const params = `?msdn=${msdn}`;

    const URL = this.adapterUrl + '/lgsp-dkdn/--layThongTinHoKinhDoanh' + params;
    return this.http.get(URL,{ headers });
  }

  getInfoBusinessCooperativeLGSPHCM(msdn): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));

    const params = `?msdn=${msdn}`;

    const URL = this.adapterUrl + '/lgsp-dkdn/--layThongTinHopTacXa' + params;
    return this.http.get(URL,{ headers });
  }

  capNhatTrangThaiDVCLT(requestBody):Observable<any>{
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.post<any>(this.adapterUrl + '/api/lienthongDVCLT/capNhatTrangThaiHoSoDVCLTHoTich', requestBody, { headers });
  }
  uploadDossierToIStorage(id: string, codeAgencyPush: string, identityNumber: string) {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.post<any>(this.adapterUrl + `/istorage/uploadDossier/${id}?creatorDepartmentCode=` + codeAgencyPush + '&identityNumber=' + identityNumber,null);
  }

  getInfoDigitalSignature(username):Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    const agencyId = JSON.parse(localStorage.getItem('userAgency'))?.id;
    const subsystemId = this.env?.subsystem?.id;
    return this.http.get(this.adapterUrl + `/digital-signature/getInfo?email=${username}&agency-id=${agencyId}&subsystem-id=${subsystemId}`, { headers });
  }

  renewOTPDigitalSignature(idFile) {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    const agencyId = JSON.parse(localStorage.getItem('userAgency'))?.id;
    const subsystemId = this.env?.subsystem?.id;
    return this.http.post<any>(this.adapterUrl + `/digital-signature/renewOTP/${idFile}?agency-id=${agencyId}&subsystem-id=${subsystemId}`, null, { headers, responseType: 'text' as 'json' });
  }
  sendHTTPAGESB(requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.post<any>(this.adapterUrl + '/ag-esb-http/--register-civil', requestBody, { headers }).pipe();
  }
  sendLLTPAGESB(requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.post<any>(this.adapterUrl + '/ag-esb-lltp/--register-criminal-records', requestBody, { headers }).pipe();
  }
  sendTNMTAGESB(requestBody: any): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.post<any>(this.adapterUrl + '/so-tnmt-agg/--register-so-tnmt', requestBody, { headers }).pipe();
  }
  sendPowacoAGG(requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.post<any>(this.adapterUrl + '/powaco/--register-powaco', requestBody, { headers }).pipe();
  }
  sendDKKDAGESB(requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.post<any>(this.adapterUrl + '/ag-esb-dkkd/--register-business', requestBody, { headers }).pipe();
  }
  checkCardId(requestBody):Observable<any>
  {
    let headers=new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.post<any>(this.adapterUrl + '/ag-esb-dkkd/--check-cardid', requestBody, { headers }).pipe();
  }

  senGetRequestAdapter(params, path: string): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.get<any>(this.adapterUrl + '/' + path, { headers, params}).pipe();
  }

  exeSignOrigin(body, username, credentialID): Observable<any>{
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers.set('Content-Type', null);
    headers.set('Accept', "multipart/form-data");
    const agencyId = JSON.parse(localStorage.getItem('userAgency'))?.id;
    const subsystemId = this.env?.subsystem?.id;
    return this.http.post<any>(this.adapterUrl + `/digital-signature/exeSignOrigin?agency-id=${agencyId}&subsystem-id=${subsystemId}&username=${username}&credentialID=${credentialID}`, body , { headers });
  }

  // đồng bộ hồ sơ BXD HPG
  syncDossierConstructHPG(requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.set('Content-Type', 'application/json');
    return this.http.post<any>(this.adapterUrl + '/construct-hpg/--sync-info-dossier', requestBody, { headers });
  }
  syncLicenseMOCHPG(datas): Observable<any>{
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.set('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');
   return this.http.post<any>(this.adapterUrl+ '/construct-hpg/--sync-license', datas, { headers });
  }
  searchDossierBXD(params): Observable<any>{
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.set('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');
   return this.http.get<any>(this.adapterUrl+ '/construct-hpg/--search-dossier-bxd', { headers, params:params });
  }

  checkDetailEnterPrise(msdn?:string,serviceConfigId?:string):Observable<any>{
    let headers = new HttpHeaders();
    const subsystemId = this.config?.subsystemId;
    const agency = this.agencyService.getAgency(true);
    const agencyId = agency.rootAgencyId;
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    let URL = `${this.adapterUrl}/dkdn/--get-detail-enterprise-btttt?`;
    URL += `agency-id=${agencyId}`;
    URL += `&subsystem-id=${subsystemId}`;
    URL += `&msdn=${msdn}`;
    URL += `&config-id=${serviceConfigId}`;
    return this.http.get(URL,{ headers });
  }

  getClientIp() {
    if(!sessionStorage.getItem('clientIP')) {
      this.httpWithoutAuth.get<{ ip: string }>(!!this.deploymentService.env?.iPCheckUrl ? this.deploymentService.env?.iPCheckUrl : 'https://ip.vnptioffice.vn/?format=json')
      .subscribe(data => {
        sessionStorage.setItem('clientIP', data.ip);
      });
    }
  }
  
    ReCallDossierSyncDVCQG(body): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    //return this.http.post<any>('http://localhost:9096/integrated-event/call-run-event-qbh', body, { headers });
    return this.http.post<any>(this.adapterUrl+'/integrated-event/call-run-event-qbh', body, { headers });

  }
  aggIsCheckConnectVNeIDLttp(type): Observable<any> {
    let headers = new HttpHeaders()
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.get(this.adapterUrl + '/--ag-esb-vneid-lltp/--check-connect-api?type='+ type, { headers }).pipe();
  }

  //-----start namds.dlc-IGATESUPP-113362-----//
  sendHTTPMinhTueDLK(requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.post<any>(this.adapterUrl + '/lgsp-minhtue/--register-civildlk', requestBody, { headers }).pipe();
  }
  //-----end namds.dlc-IGATESUPP-113362-----//

  getPriceViettelPost(requestBody) {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.post<any>(this.adapterUrl + '/service/viettelpost-receive/getprice-dvc-external', requestBody, { headers });
  }

  sendViettelPost(requestBody) {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.post<any>(this.adapterUrl + '/service/viettelpost-receive/createorder-dvc-external', requestBody, { headers });
  }

  postReceiveDossierStatusData(requestBody?): Observable<any> {
        let headers = new HttpHeaders();
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        headers.append('Access-Control-Allow-Origin', '*');
        return this.http.post<any>(this.adapterUrl + '/api-integration/--send-kafka', requestBody, { headers }).pipe();
    }

}
