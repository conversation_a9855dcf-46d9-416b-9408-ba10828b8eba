import { Permission } from './../../schema/permission';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { ApiProviderService } from 'src/app/core/service/api-provider.service';
import { Observable } from 'rxjs';
import { EnvService } from 'src/app/core/service/env.service';
import { DeploymentService } from 'src/app/data/service/deployment.service';
import { Clipboard } from '@angular/cdk/clipboard';

@Injectable({
  providedIn: 'root'
})
export class ProcessService {

  config = this.enservice.getConfig();
  env = this.deploymentService.getAppDeployment()?.env;
  private actModelingAPI = this.apiProviderService.getUrl('digo', 'modeling') + '/v1';
  private actModelingContentAPI = this.apiProviderService.getUrl('digo', 'bpm') + '/process-definition/models/';
  // private actModelingContentAPI = 'http://localhost:8080/process-definition/models/';
  private bpmAPI = this.apiProviderService.getUrl('digo', 'bpm');
  private basecat = this.apiProviderService.getUrl('digo', 'basecat');
  private rbOnegate = this.apiProviderService.getUrl('digo', 'rbo');
  private reporter = this.apiProviderService.getUrl('digo', 'reporter');
  private human = this.apiProviderService.getUrl('digo', 'human');
  private basedata = this.apiProviderService.getUrl('digo', 'basedata');
  private basepad = this.apiProviderService.getUrl('digo', 'basepad');
  private padman = this.apiProviderService.getUrl('digo', 'padman');
  private sysman = this.apiProviderService.getUrl('digo', 'sysman');
  private formio = this.config.formioURL;
  private size = this.env.formIOMaxSize ? this.env.formIOMaxSize : 100000;

  constructor(
    private http: HttpClient,
    private apiProviderService: ApiProviderService,
    private enservice: EnvService,
    private deploymentService: DeploymentService,
    private clipboard: Clipboard
  ) {
  }

  getListPosition(page, agencyId?: Array<string>, levelTransfer?): Observable<any> {
    const headers = new HttpHeaders();
    let param = `?page=${page}&agency-id=${agencyId}&size=50`;
    if (levelTransfer) { param += `&level-transfer=${levelTransfer}`; }
    return this.http.get(this.basecat + '/position' + param, { headers });
  }

  getListPositionWithParams(params): Observable<any> {
    const headers = new HttpHeaders();
    return this.http.get(this.basecat + '/position' + params, { headers });
  }

  
  getListAgencyByPosition(id): Observable<any> {
    const headers = new HttpHeaders();
    return this.http.get(this.basecat + '/position/--get-list-agency?position-id=' + id, { headers });
  }

  getDetailProcessByActivitiId(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.bpmAPI + '/process-definition/--get-by-process-definition-id?id=' + id, { headers });
  }

  getListTagByCategoryId(categoryId, page): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    const param = '?category-id=' + categoryId + '&page=' + page + '&sort=order';
    return this.http.get(this.basecat + '/tag/--by-category-id' + param, { headers });
  }

  getListTagByCategoryIdWithSearch(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.basecat + '/tag/--by-category-id' + searchString, { headers });
  }

  getListTagByCategoryIdWithSearchPromise(searchString): Promise<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.basecat + '/tag/--by-category-id' + searchString, { headers }).toPromise();
  }

  getListAgency(parentId, page): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    const param = '?&page=' + page + '&parent-id=' + parentId + '&size=10';
    return this.http.get(this.basedata + '/agency' + param, { headers });
  }

  getListAgencyWithSearch(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.basedata + '/agency' + searchString, { headers });
  }

  getListAgencyLevel(page, size, stt?): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    const status = !!stt ? stt : '';
    const param = '?page=' + page + '&size=' + size + '&status=' + status + '&sort=name,asc';
    return this.http.get(this.basedata + '/agency-level' + param, { headers });
  }

  // getListForm(tag, skip) {
  //   console.log('=========================================');
  //   console.log(this.formio);
  //   return this.http.get(this.formio + '/form?tags=' + tag + '&skip=' + skip + '&limit=' + this.size);
  // }
  getListForm(tag, skip): Observable<any> {
    console.log('=========================================');
    console.log(this.size);
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    // tslint:disable-next-line: max-line-length
    return this.http.get<any>(this.formio + '/form' + '?name__regex=' + this.config.regexEform + '&skip=' + skip + '&limit=' + this.size, { headers }).pipe();
  }

  getListReport(typeId, page): Observable<any> {
    // tslint:disable-next-line: max-line-length
    return this.http.get(this.reporter + '/template?type-id=' + typeId + '&subsystem-id=' + this.enservice.getConfig().systemId + '&page=' + page + '&size=' + this.size);
  }

  getListProject(page: number): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.actModelingAPI + '/projects?page=' + page + '&sort=creationDate,desc', { headers });
  }

  getListProcessDef(processId, page): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    // tslint:disable-next-line: max-line-length
    return this.http.get(this.actModelingAPI + '/projects/' + processId + '/models?type=PROCESS&maxItems=100&skipCount=' + page * 100, { headers });
  }

  getListProcess(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.bpmAPI + '/process-definition' + searchString, { headers });
  }

  getListProcessPromise(searchString): Promise<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.bpmAPI + '/process-definition' + searchString, { headers }).toPromise();
  }

  postProcess(requestBody) {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    return this.http.post<any>(this.bpmAPI + '/process-definition', requestBody, { headers });
  }

  postProcessV2(requestBody) {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    return this.http.post<any>(this.bpmAPI + '/v2/process', requestBody, { headers });
  }

  postTaskDefinition(requestBody) {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    return this.http.post<any>(this.bpmAPI + '/process-definition-task', requestBody, { headers });
  }

  getTaskByProcessAndTaskDef(processId, taskDef) {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    const params = '?process-definition-id=' + processId + '&task-definition-key=' + taskDef;
    return this.http.get<any>(this.bpmAPI + '/process-definition-task/--find-by-activiti-task' + params, { headers });
  }


  deployProcess(processModelId) {

    return this.http.put<any>(this.rbOnegate + '/digo/model/' + processModelId + '/--deploy', null);
  }


  updateProcess(id, requestBody) {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    return this.http.put<any>(this.bpmAPI + '/process-definition/' + id, requestBody, { headers });
  }

  updateProcessV2(id, requestBody) {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    return this.http.put<any>(this.bpmAPI + '/v2/process/' + id, requestBody, { headers });
  }

  updateProcessProperties(id, requestBody) {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    return this.http.put<any>(this.bpmAPI + '/process-definition/' + id + '/--properties', requestBody, { headers });
  }

  deleteProcess(processId) {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    return this.http.delete<any>(this.bpmAPI + '/process-definition/' + processId, { headers });
  }

  deleteProcessDefTask(taskId) {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    return this.http.delete<any>(this.bpmAPI + '/process-definition-task/' + taskId, { headers });
  }

  getUrlModel(modelId) {
    return this.actModelingContentAPI + modelId + '/content';
  }

  getUser(username): Observable<any> {
    return this.http.get(this.human + '/user?username=' + username);
  }

  getFormIOList(): Observable<any> {
    return this.http.get(this.formio + '/form?type=form&tags=iGate');
  }

  getListTimeSheet(page): Observable<any> {
    return this.http.get(this.basecat + '/timesheet/name?page=' + page);
  }

  getProcessDetail(id: string): Observable<any> {
    return this.http.get(this.bpmAPI + '/process-definition/' + id);
  }

  getDetailTimeSheet(id): Observable<any> {
    return this.http.get(this.basecat + '/timesheet/' + id);
  }

  putProcess(id, requestBody) {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    return this.http.put<any>(this.bpmAPI + '/process-definition/' + id, requestBody, { headers });
  }

  getAllTimeSheetByProcessDef(id: string, hourTimesheet?): Observable<any> {
    if(!!hourTimesheet ){
      return this.http.get(this.bpmAPI + '/process-definition-task/--all-timesheet-by-process-def?process-definition-id=' + id + "&hour-timesheet=" + hourTimesheet);
    }
    else{
    return this.http.get(this.bpmAPI + '/process-definition-task/--all-timesheet-by-process-def?process-definition-id=' + id);
    }
  }

  changeTimesheet(processDefinitionId, requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.append('Content-Type', 'application/json');
    const param = '?process-definition-id=' + processDefinitionId;
    return this.http.put(this.bpmAPI + '/process-definition-task/--change-timesheet/' + param, requestBody, { headers });
  }

  changeDefinedTask(id, requestBody) {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    return this.http.put<any>(this.bpmAPI + '/process-definition/' + id + '/--change-defined-task', requestBody, { headers });
  }

  getTaskByProcessDefAndTaskKey(processDef, taskKey) {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    const params = '?process-definition-id=' + processDef + '&task-definition-key=' + taskKey;
    return this.http.get<any>(this.bpmAPI + '/process-definition-task/--find-by-process-def-and-task-key' + params, { headers });
  }

  getTaskConfig(dossierId, currentTaskDefinitionKey, nextTaskDefinitionKey) {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    const params = `?dossier-id=${dossierId}&current-task-definition-key=${currentTaskDefinitionKey}&next-task-definition-key=${nextTaskDefinitionKey}`;
    return this.http.get<any>(this.bpmAPI + '/process-definition-task/--config' + params, { headers });
  }

  getListTaskByProcessDefId(processDefId) {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    const params = '?process-definition-id=' + processDefId + '&size=50&spec=page';
    return this.http.get<any>(this.bpmAPI + '/process-definition-task' + params, { headers });
  }

  getListUserByAgencyId(agencyId, page): Observable<any> {
    return this.http.get(this.human + '/user/fullname+experience?agency-id=' + agencyId + '&page=' + page + '&size=50');
  }

  getAgencyById(id): Observable<any> {
    return this.http.get(this.basedata + '/agency/' + id);
  }

  getUserInfoFully(userId): Observable<any> {
    return this.http.get(this.human + '/user/' + userId + '/--fully');
  }

  getCheckProcessIsUsed(processId): Observable<any> {
    return this.http.get(this.basepad + '/procedure-process-definition/' + processId + '/--is-used');
  }

  getAgencyChildTree(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.basedata + '/agency/--child-tree' + searchString, { headers });
  }

  getNextFlow(taskId) {
    return this.http.get<any>(this.rbOnegate + '/digo/task/' + taskId + '/--check-gateway?level=2');
  }

  getTreeProcess(activitiId, deep, flowElementId) {
    return this.http.get<any>(this.rbOnegate + '/digo/process-definitions/' + activitiId + '/next-flow-element?deep=' + deep + '&flow-element-id=' + flowElementId);
  }

  getAgencyFamily(id) {
    return this.http.get(
      this.apiProviderService.getUrl('digo', 'basedata') +
      `/agency/${id}/name+code+parent+ancestor/--fully`
    );
  }

  getAgencyList(parentId, page): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    const params = '?&page=' + page + '&parent-id=' + parentId + '&size=50&sort=order,asc';
    return this.http.get(this.basedata + '/agency' + params,{ headers });
  }

  getAgencyNameNLogo(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.basedata + '/agency/' + id + '/name+logo-id',{ headers });
  }

  getListUserByAgencyIdPositionId(agencyId, positionId, page): Observable<any> {
    return this.http.get(this.human + '/user/--by-position?agency-id=' + agencyId + '&position-id=' + positionId + '&page=' + page + '&size=50');
  }

  getListUserWithParams(params): Observable<any> {
    return this.http.get(this.human + '/user/--by-position' + params);
  }

  getListUserAll(params): Observable<any> {
    return this.http.get(this.human + '/user/' + params);
  }

  getListUserAllAuth(params): Observable<any> {
    return this.http.get(this.human + '/user/--auth' + params);
  }

  getAgencyTreeViewByAncestorId(options): Observable<any> {
    return this.http.get<any>(this.basedata + '/agency' +
      `/tree-view/--by-ancestor?keyword=` + options.keyword +
      `&ancestor-id=${options['ancestor-id']}` +
      `&code=${options.code}` +
      `&status=${options.status}` +
      `&phone=${options.phone}`);
  }

  getListProcedureProcessDefGroupByProcessDefId(options): Observable<any> {
    return this.http.get<any>(this.basepad + '/procedure-process-definition' +
      `/--group-by-process-def-id?keyword=` + options.keyword +
      `&page=${options.page}` +
      `&size=${options.size}`);
  }

  getDetailProcedureProcessDefGroupByProcessDefId(id): Observable<any> {
    return this.http.get<any>(this.basepad + '/procedure-process-definition/' + id + '/--group-by-process-def-id');
  }

  postApplyProcessMulti(requestBody): Observable<any> {
    return this.http.post<any>(this.basepad + '/procedure-process-definition/--apply-process-multi', requestBody);
  }

  putApplyProcessMulti(id, requestBody): Observable<any> {
    return this.http.put<any>(this.basepad + '/procedure-process-definition/' + id + '/--apply-process-multi', requestBody);
  }

  deleteProcessMulti(id) {
    return this.http.delete<any>(this.basepad + '/procedure-process-definition/' + id + '/--delete-process-multi');
  }

  cloneProcess(requestBody): Observable<any> {
    return this.http.post<any>(this.bpmAPI + '/process-definition/--clone-process', requestBody);
  }

  checkExistsProcessDefinitionId(id): Observable<any> {
    const params = 'process-definition-id=' + id;
    return this.http.get<any>(this.basepad + '/procedure-process-definition/--check-exists-process-definition-id?' + params);
  }

  checkExistsProcedureProcessDefId(id): Observable<any> {
    const params = 'procedure-process-def-id=' + id;
    return this.http.get<any>(this.padman + '/dossier/--check-exists-procedure-process-def-id?' + params);
  }

  getProcedureProcessDefinitionDetail(id):Observable<any>{
    const URL = `${this.basepad}/procedure-process-definition/${id}`;
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(URL, { headers });
  }

  getListUserByPermissionCode(agencyId, code): Observable<any> {
    return this.http.get(this.human + '/user/--get-user-list-by-permission-code?agency-id=' + agencyId + '&code=' + code);
  }

  getApprovalAgency(agencyId): Observable<any> {
    return this.http.get(this.basepad + '/approval-agency-config/?page=0&size=10&spec=page&approvaledAgency-id=' + agencyId );
  }

  findProcessDefinitionTaskById(taskId): Observable<any>{
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    return this.http.get<any>(this.bpmAPI + '/process-definition-task/' + taskId, { headers });
  }

  copyToClipboard(id: string, definitionId: string): void {
    this.clipboard.copy(id + ',' + definitionId);
  }

  postCreateProcess(requestBody) {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    return this.http.post<any>(this.bpmAPI + '/v2/process/--create', requestBody, { headers });
  }

  putApplyProcess(idProcess, idmodel) {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    return this.http.put<any>(this.bpmAPI + '/v2/process/'+ idProcess +'/'+ idmodel +'/--apply', { headers });
  }

  getListForm2(skip, status, agencyId): Observable<any> {
    console.log('=========================================');
    console.log(this.size);
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get<any>(this.bpmAPI + '/eform-catalog/--search' + '?page=0&spec=page&size=' + this.size + '&status=' + status + '&agency-id=' + agencyId, { headers }).pipe();
  }

  getEFormDetail(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get<any>(this.formio + '/form/' + id, { headers }).pipe();
  }

  getListFormLazyLoad(keyword, skip, size): Observable<any> {
    console.log('=========================================');
    console.log(this.size);
    console.log(size);
    let url = this.formio + '/form' + '?name__regex=';
    if(!!keyword){
      url += `${keyword}`;
    } else {
      url += this.config.regexEform;
    }
    console.log('skip---- ', skip);
    url = `${url}&skip=${skip}&limit=${size}`;
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    // tslint:disable-next-line: max-line-length
    return this.http.get<any>(url, { headers }).pipe();
  }

  getListEFormWithKeyWord(skip, limit, keyword: string): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    let _keyword = '';
    if (!!keyword){
      _keyword = `&title__regex=/${keyword}/i`;
    }
    else {
      _keyword = `&title__regex=/${this.config.regexEform}/i`;
    }
    return this.http.get<any>(this.formio + '/form' + '?' + 'skip=' + skip + '&limit=' + limit + _keyword, { headers }).pipe();
  }

  getListTimeSheetKeyWord(page, keyword): Observable<any> {
   // console.log("call getListTimeSheetKeyWord");
    let url = this.basecat + '/timesheet/name' + '?page=' + page;
    if(!!keyword){
      // console.log('keyword ' + keyword);
      url += '&keyword=' + keyword;
    }
    console.log('url ' + url);
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get<any>(url, { headers }).pipe();
  }
  getTimeSheetById(id): Observable<any> {
   // console.log('call get timesheet id');
    let url = this.basecat + '/timesheet/' + id;
    console.log('url ' + url);
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get<any>(url, { headers });
  }

  getListTagByCategoryIdSearchKeyWord(categoryId, page, keyword): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    const param = '?category-id=' + categoryId + '&page=' + page + '&sort=order';
    let url = this.basecat + '/tag/--by-category-id' + param;
    if (!!keyword){
      url += '&keyword=' + keyword;
    }
    console.log('url api');
    console.log(url);
    return this.http.get(url, { headers });
  }

  getListPositionKeyWord(keyWord, page , agencyId?: Array<string>, levelTransfer?): Observable<any> {
    const headers = new HttpHeaders();
    let param = `?page=${page}&agency-id=${agencyId}&size=50`;
    if (levelTransfer) { param += `&level-transfer=${levelTransfer}`; }
    let url = this.basecat + '/position' + param;
    if(!!keyWord){
      url += '&keyword=' + keyWord;
    }
    return this.http.get(url, { headers });
  }

  getListUserByAgencyIdPositionIdKeyword(agencyId, positionId, page, keyword): Observable<any> {
    let url = this.human + '/user/--by-position?agency-id=' + agencyId + '&position-id=' + positionId + '&page=' + page + '&size=50';
    if (!!keyword){
      url += '&keyword=' + keyword;
    }
    return this.http.get(url);
  }

  getListUserByAgencyIdKeyword(agencyId, page, keyword): Observable<any> {
    let url = this.human + '/user/fullname+experience?agency-id=' + agencyId + '&page=' + page + '&size=15';
    if (!!keyword){
      url += '&keyword=' + keyword;
    }
    return this.http.get(url);
  }

}
