import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { ApiProviderService } from 'src/app/core/service/api-provider.service';
import { EnvService } from 'src/app/core/service/env.service';

@Injectable({
  providedIn: 'root'
})
export class DossierAuthService {

  constructor(
    private http: HttpClient,
    private envService: EnvService,
    private apiProviderService: ApiProviderService,
  ) { }

  private searchUrls = this.apiProviderService.getUrl('digo', 'basepad') + '/dossier-auth/--search';
  private exportUrls = this.apiProviderService.getUrl('digo', 'basepad') + '/dossier-auth/--export';
  private postUrls = this.apiProviderService.getUrl('digo', 'basepad') + '/dossier-auth';
  private getUpdateStatusUrls(id: string){
    return this.apiProviderService.getUrl('digo', 'basepad') + `/dossier-auth/${id}/--status`;
  }
  private getDetailsUrls(id: string){
    return this.apiProviderService.getUrl('digo', 'basepad') + `/dossier-auth/${id}`;
  }
  private getUpdateUrls(id: string){
    return this.apiProviderService.getUrl('digo', 'basepad') + `/dossier-auth/${id}`;
  }
  private getUpdateCompleteUrls(id: string){
    return this.apiProviderService.getUrl('digo', 'basepad') + `/dossier-auth/${id}/--complete`;
  }
  private getMaxNumAuthUrls(id: string){
    return this.apiProviderService.getUrl('digo', 'basepad') + `/dossier-auth/${id}/--max-num`;
  }

  search(query): Observable<any> {
    const endpoint = this.searchUrls + (!!query ? '' + query : '');
    return this.http.get(endpoint);
  }

  export(query): Observable<any> {
    const endpoint = this.exportUrls + (!!query ? '' + query : '');
    return this.http.get(endpoint);
  }

  post(body: any): Promise<any>{
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    return this.http.post<any>(this.postUrls, body, { headers }).toPromise();
  }

  update(id: string, body: any): Observable<any>{
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    return this.http.put<any>(this.getUpdateUrls(id), body, { headers });
  }

  updateStatus(id: string): Observable<any>{
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    return this.http.put<any>(this.getUpdateStatusUrls(id), '', { headers });
  }

  updateComplete(id: string): Observable<any>{
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    return this.http.put<any>(this.getUpdateCompleteUrls(id), '', { headers });
  }

  details(id: string): Observable<any>{
    return this.http.get(this.getDetailsUrls(id));
  }

  getMaxNumAuth(id: string): Observable<any>{
    return this.http.get(this.getMaxNumAuthUrls(id));
  }
}
