@import "~src/styles/pagination.scss";
.dialog_title {
    font-style: normal;
    font-weight: 500;
    font-size: 20px;
    color: #CE7A58;
}

::ng-deep .applyBtn {
    margin-top: 1em;
    background-color: #CE7A58;
    color: #fff;
    height: 3em;
}

.close-button {
    float: right;
    top: -24px;
    right: -24px;
}

.dialog_content {
    font-size: 15px;
    .name {
        word-wrap: break-word;
        max-width: 100%;
    }
}

.dialog_content .highlight{
    color: #CE7A58;
}
.tbl {
    table {
        width: 100%;
    }

    ul {
        list-style-type: none;
        margin-left: -2em;
    }

    li {
        margin: 1em 0;
    }

    .detail-title {
        font-weight: bold;
        margin-bottom: 5px;
    }

    .mat-divider {
        margin: 0 3em;
    }

    .mat-header-row {
        background-color: #e8e8e8;

        .mat-header-cell {
            color: #495057;
            font-size: 14px;

            p {
                margin-bottom: 0;
                font-weight: 400;
                font-style: italic;
            }
        }
    }

    .mat-row {
        border: none;
    }

    .mat-row:nth-child(even) {
        background-color: #fafafa;
    }

    .mat-row:nth-child(odd) {
        background-color: #fff;
    }

    .mat-column-hsid {
        flex: 0 0 10%;
    }

    .mat-column-ten_hs {
        flex: 1 0 5%;
        padding-right: 15px;
    }

    .mat-column-ngay_dk {
        flex: 0 0 10%;
    }

    .mat-column-email {
        flex: 1 0 0;
        padding-right: 15px;
    }

    .mat-column-nguoi_dk {
        flex: 1 0 0;
        padding-right: 15px;
    }

    .mat-column-sdt_didong {
        flex: 0 0 10%;
    }

    .mat-column-kieu_tiep_nhan {
        flex: 0 0 10%;
    }

    .mat-column-trang_thai {
        flex: 0 0 10%;
        padding-right: unset;
    }

    .mat-column-cqtc_ten {
        flex: 1 0 0;
        padding-right: 15px;
    }

    .mat-column-hso_bo_sung {
        flex: 1 0 0;
        padding-right: 15px;
    }

    .mat-column-hso_cho_dung_han {
        flex: 1 0 0;
        padding-right: 15px;
    }

    .mat-column-hso_cho_qua_han {
        flex: 1 0 0;
        padding-right: 15px;
    }

    .mat-column-hso_dung_han {
        flex: 1 0 0;
        padding-right: 15px;
    }

    .mat-column-hso_ky_truoc {
        flex: 1 0 0;
        padding-right: 15px;
    }

    .mat-column-hso_qua_han {
        flex: 1 0 0;
        padding-right: 15px;
    }

    .mat-column-hso_trong_ky {
        flex: 1 0 0;
        padding-right: 15px;
    }

    .mat-column-hso_truoc_han {
        flex: 1 0 0;
        padding-right: 15px;
    }
}