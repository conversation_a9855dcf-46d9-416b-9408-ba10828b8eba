import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { KeycloakService } from 'keycloak-angular';
import { Subscription } from 'rxjs';
import { EnvService } from 'src/app/core/service/env.service';
import { DeploymentService } from 'src/app/data/service/deployment.service';
import { DossierService } from 'src/app/data/service/dossier/dossier.service';
import { MainService } from 'src/app/data/service/main/main.service';
import { SnackbarService } from 'src/app/data/service/snackbar/snackbar.service';
import { UserService } from 'src/app/data/service/user.service';
import { ConfirmDossierRemindDialogModel, ListDossierRemindComponent } from '../list-dossier-remind/list-dossier-remind.component';
import {
  ListDossierRemindDauKyComponent
} from 'modules/dashboard-kha/pages/list-dossier-remind-dau-ky/list-dossier-remind-dau-ky.component';
import {MatTableDataSource} from '@angular/material/table';
import {ProcessHandleComponent, ProcessHandleDialogModel} from 'shared/components/process-handle/process-handle.component';
import { QBHStatisticService } from 'src/app/data/service/qbh-statistics/qbh-statistic.service';
import { Router } from '@angular/router';
import { KhaRemindWorkService } from 'src/app/data/service/kha-remind-work/kha-remind-work.service';

@Component({
  selector: 'app-dashboard-kha',
  templateUrl: './dashboard-kha.component.html',
  styleUrls: ['./dashboard-kha.component.scss']
})
export class DashboardKhaComponent implements OnInit {
  constructor(
    private deploymentService: DeploymentService,
    private keycloakService: KeycloakService,
    private userService: UserService,
    private envService: EnvService,
    private khaRemindWorkService: KhaRemindWorkService,    
    private dialog: MatDialog,
    private snackbarService: SnackbarService,
    private dossierService: DossierService,
    private mainService: MainService,
    private cdRef: ChangeDetectorRef,
    private qbhStatisticsService: QBHStatisticService,
    private router: Router

    ) {
      if (this.userService.hasPermisson(this.notShowDashBoardPerm)) {
        this.router.navigateByUrl('/dossier/processing');
        return;
      }
    }

  env = this.deploymentService.getAppDeployment()?.env;
  countTiLeDungHan: number = 0;
  countDauKy: number = 0;
  countDaTiepNhan: number = 0;
  countDaTiepNhanTrucTuyen: number = 0;
  countDaTiepNhanTrucTiep: number = 0;
  countDaTiepNhanLienThong: number = 0;
  countDaGiaiQuyet: number = 0;
  countDaGiaiQuyetDungHan: number = 0;
  countDaGiaiQuyetQuaHan: number = 0;
  countDangGiaiQuyet: number = 0;
  countDangGiaiQuyetDungHan: number = 0;
  countDangGiaiQuyetDenHan: number = 0;
  countDangGiaiQuyetQuaHan: number = 0;
  currentYear: number = new Date().getFullYear();
  userId:any;
  userInfo: any;
  agencyId: any;
  config = this.envService.getConfig();
  selectedLang: string;
  accountId : string;
  isDue : number = 0;
  userAgencyLevel: any = JSON.parse(localStorage.getItem('levelAgency'));
  agencyCapSo: any = this.deploymentService.env.OS_QTI.qtiCapSo ? this.deploymentService.env.OS_QTI.qtiCapSo : '5ff6b1a706d0e31c6bf13e09';

  userAgency: any;
  listUserAgency: any;
  userExperience: any;
  accepterInfo = {
    id: '',
    username: '',
    fullname: '',
    accountId: ''
  };
  subscription: Subscription;
  approvalAgencyList = [];
  agencyInfo = [];
  searchDomain:String ="";
  ELEMENTDATA: any[] = [];
  dataSource: MatTableDataSource<any>;
  displayedColumns: string[] = ['stt', 'code', 'procedure', 'required' ,'user','date'];
  size = 10;
  pageIndex = 1;
  page = 1;
  countResult = 0;
  pgSizeOptions = this.config.pageSizeOptions;

  agencyTagName:string= "Cấp Tỉnh,Cấp Sở,Cấp Huyện,Cấp Xã,Cấp Xã/Phường/Thị trấn";
  rootId = null;
  notShowDashBoardPerm = "notShowDashBoardPerm";

 async ngOnInit() {
    this.selectedLang = localStorage.getItem('language');
    this.agencyId = this.getUserAgencyId();
    //this.rootId = await this.findRootAgency(this.agencyId);
    this.rootId = null;
    if(this.rootId === '655dc9dececceb05ee44499f'){
      this.rootId = '60b87fb59adb921904a0213e'; //UBND tỉnh Quảng Tri có 2 agency.id
    }
    await this.getUserAccount();
    this.getListDossier('?page=' + 0 + '&size=' + this.size + '&spec=page');
    let searchString = `?page=0&size=10&spec=page&sort=appointmentDate,asc`;
    if (this.rootId != null) {
      searchString += `&agcencyId=${this.rootId}`;
    }
    if (this.userId != null && this.userId != '') {
      searchString += `&userId=${this.userId}`;
    }
    await this.countDossierDauKy(searchString);
    await this.countDossierDaTiepNhan(searchString);
    await this.countDossierDaGiaiQuyet(searchString);
    await this.countDangGiaiQuyetHoso(searchString);
    if (this.countDaGiaiQuyet == 0) {
      this.countTiLeDungHan = 0;
    } else {
      // this.countTiLeDungHan = parseFloat(((this.countDaGiaiQuyetDungHan / this.countDaGiaiQuyet) * 100).toFixed(1));
      const tiLeDungHan = (this.countDaGiaiQuyetDungHan / this.countDaGiaiQuyet) * 100;
      const tiLeDungHanKhongLamTron = Math.floor(tiLeDungHan * 10) / 10;
      this.countTiLeDungHan = parseFloat(tiLeDungHanKhongLamTron.toFixed(1)) ;
    }
    console.log("Ti lẹ dung han: ", this.countTiLeDungHan);
  }
  listDossier(type:number){
      const dialogData = new ConfirmDossierRemindDialogModel(type);
      const dialogRef = this.dialog.open(ListDossierRemindComponent, {
        width: '1200px',
        data: dialogData,
        disableClose: true,
        autoFocus: false
      });
      dialogRef.afterClosed().subscribe(dialogResult => {
        const res = dialogResult;
        console.log('res',res)
        if (res === true) {
          const msgObj = {
            vi: 'Đã thêm mới!',
            en: 'Created!'
          };
          this.snackbarService.openSnackBar(1, '',
          msgObj[this.selectedLang],
          'success_notification',
          this.config.expiredTime
        );
       // this.paginate(this.pageIndex, 0);
        }
        if (res === false) {
          this.snackbarService.openSnackBar(0,
             this.envService.getTranslateNotificationLabel(this.selectedLang, 'error', 'post'),
           res.name, 'error_notification', this.config.expiredTime);
        }
      });
  }

  listDossierDauKy(type:number){
    const dialogData = new ConfirmDossierRemindDialogModel(type);
    const dialogRef = this.dialog.open(ListDossierRemindDauKyComponent, {
      width: '1200px',
      data: dialogData,
      disableClose: true,
      autoFocus: false
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
      const res = dialogResult;
      console.log('res',res)
      if (res === true) {
        const msgObj = {
          vi: 'Đã thêm mới!',
          en: 'Created!'
        };
        this.snackbarService.openSnackBar(1, '',
          msgObj[this.selectedLang],
          'success_notification',
          this.config.expiredTime
        );
        // this.paginate(this.pageIndex, 0);
      }
      if (res === false) {
        this.snackbarService.openSnackBar(0,
          this.envService.getTranslateNotificationLabel(this.selectedLang, 'error', 'post'),
          res.name, 'error_notification', this.config.expiredTime);
      } 
    });
  }

  getUserAccount() {
    this.keycloakService.loadUserProfile().then(user => {
      // tslint:disable-next-line: no-string-literal
      this.userId = user['attributes'].user_id[0];
      console.log('userId',this.userId )
      this.userService.getUserInfo(this.userId).subscribe(data => {
        this.accountId = data.account.id
      }, error => {
        console.log(error);
      });
    });
  }
  getUserAgencyId(): string {
    const userAgency = JSON.parse(localStorage.getItem('userAgency'));
    if (userAgency !== null) {
      return userAgency?.id;
    } else {
      return this.config?.rootAgency?.id;
    }
  }

  async countDossierDauKy(searchString: string) {
    const data = await this.khaRemindWorkService.getListFormTerm(searchString).toPromise();
    this.countDauKy = data.totalElements;
  }

  async countDossierDaTiepNhan(searchString: string) {
    const dataTrucTiep = await this.khaRemindWorkService.getListAcceptTrucTiep(searchString).toPromise();
    const dataTrucTuyen = await this.khaRemindWorkService.getListAcceptTrucTuyen(searchString).toPromise();
    this.countDaTiepNhanTrucTiep = dataTrucTiep.totalElements;
    this.countDaTiepNhanTrucTuyen = dataTrucTuyen.totalElements;
    this.countDaTiepNhanLienThong = 0;
    this.countDaTiepNhan = this.countDaTiepNhanTrucTuyen + this.countDaTiepNhanTrucTiep;
  }

  async countDossierDaGiaiQuyet(searchString: string) {
    //
    const dataDue = await this.khaRemindWorkService.getListResoleDue(searchString).toPromise();
    const dataOverDue = await this.khaRemindWorkService.getListResoleOverDue(searchString).toPromise();

    this.countDaGiaiQuyetDungHan = dataDue.totalElements;
    this.countDaGiaiQuyetQuaHan = dataOverDue.totalElements;

    this.countDaGiaiQuyet = this.countDaGiaiQuyetDungHan + this.countDaGiaiQuyetQuaHan;
  }

  async countDangGiaiQuyetHoso(searchString: string){
    const dataDungHan = await this.khaRemindWorkService.getListUnresolveOnTime(searchString).toPromise(); // con han
    const dataDenHan = await this.khaRemindWorkService.getListUnresolveIsDue(searchString).toPromise(); // dung han
    const dataQuaHan = await this.khaRemindWorkService.getListUnresolveOverDue(searchString).toPromise(); // qua han
    this.countDangGiaiQuyetDenHan = dataDenHan.totalElements;
    this.countDangGiaiQuyetDungHan = dataDungHan.totalElements;
    this.countDangGiaiQuyetQuaHan = dataQuaHan.totalElements;
    this.countDangGiaiQuyet = this.countDangGiaiQuyetDungHan + this.countDangGiaiQuyetQuaHan;
  }
  
  // den han (trong ngay hom nay) / qua han (truoc ngay hom nay)
  getListDossier(searchString){
    console.log("get? ");
    const userAgency = JSON.parse(localStorage.getItem('userAgency'));
    if (this.rootId != null) {
      searchString += `&agcencyId=${this.rootId}`;
    }
    if (this.userId != null && this.userId != '') {
      searchString += `&userId=${this.userId}`;
    }
    // if (!!userAgency.parent) {
    //   searchString += `&parentId=${userAgency?.parent?.id}`;
    // }
    // if (this.userAgencyLevel.id !== this.agencyCapSo) {
    //   searchString += `&agcencyId=${userAgency.id}`;
    // }
    searchString += `&sort=appointmentDate,asc`;
    this.khaRemindWorkService.getListDueCBCC(searchString).subscribe(data => {
      this.ELEMENTDATA = [];
      this.countResult = data.totalElements;
      for (let i = 0; i < data.numberOfElements; i++) {
        data.content[i].stt = this.size * (this.pageIndex - 1) + (i + 1);
        this.ELEMENTDATA.push(data.content[i]);
      }
      this.dataSource = new MatTableDataSource(this.ELEMENTDATA);
      console.log('data source', JSON.stringify(this.dataSource.data));
    });
  }

  paginate(event: any, type) {
    switch (type) {
      case 0:
        this.pageIndex = event;
        this.page = event;
        this.getListDossier('?page=' + (this.pageIndex - 1) + '&size=' + this.size + '&spec=page');
        break;
      case 1:
        this.pageIndex = 1;
        this.page = 1;
        this.getListDossier('?page=' + (this.pageIndex - 1) + '&size=' + this.size + '&spec=page');
        break;
    }
  }

  viewProcess(dossierId, dossierCode) {
    const dialogData = new ProcessHandleDialogModel(dossierId, dossierCode);
    const dialogRef = this.dialog.open(ProcessHandleComponent, {
      minWidth: '70vw',
      data: dialogData,
      disableClose: true,
      autoFocus: false
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
    });
  }

  async findRootAgency(agencyId) {
    const data = await this.qbhStatisticsService.findRootAgency(agencyId,this.agencyTagName);
    return data?.id ?? null;
  }

  dossierDetail(dossierId, procedureId, task) {
    const queryParamsObject = {
      procedure: procedureId,
    };

    if (task !== undefined) {
      Object.assign(queryParamsObject, { task: task[task.length - 1].id });
    }

      const url = this.router.serializeUrl(
        this.router.createUrlTree(['dossier/search/' + dossierId], {
          queryParams: queryParamsObject
        })
      );

      window.open(url, '_blank');
      return;
  }

  processingDossier(dossierId, procedureId, assigneeTask) {
      this.dossierService.getDossierDetail(dossierId).subscribe(data => {
        if (data.task !== undefined && assigneeTask) {  
          if (data.task.filter(t => t.isCurrent === 1)[0] !== undefined && data.task.filter(t => t.isCurrent === 1)[0] !== null) {
          let url = '';
          url = this.router.serializeUrl(
            this.router.createUrlTree(['dossier/processing/' + dossierId], {
              queryParams: {
                procedure: procedureId,
                task: !!assigneeTask[0]?.id?assigneeTask[0].id:data.task.filter(t => t.isCurrent === 1)[0].id
              }
            }));
  
            window.open(url, '_blank');
                return;
  
        } else {
          const msgObj = {
            vi: 'Không tìm thấy công việc nào cho hồ sơ này!',
            en: 'No task found for this dossier!'
          };
          this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
        }
      }
    });
  }

}
