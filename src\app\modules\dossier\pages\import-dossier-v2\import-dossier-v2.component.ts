import { Component, OnInit } from '@angular/core';
import { BasedataService } from 'src/app/data/service/basedata/basedata.service';
import { SectorService } from 'src/app/data/service/sector/sector.service';
import { ProcedureService } from 'src/app/data/service/procedure/procedure.service';
import { DossierService } from 'src/app/data/service/dossier/dossier.service';
import { EnvService } from 'src/app/core/service/env.service';
import { SnackbarService } from 'src/app/data/service/snackbar/snackbar.service';
import { APPLICANT_GUIDE_DATA, FEE_GUIDE_DATA, COMPOSITION_GUIDE_DATA } from 'src/app/data/service/config.service';
import {NotificationDialogComponent, NotificationDialogModel} from "src/app/shared/components/dialogs/notification-dialog/notification-dialog.component";
import { MatDialog } from '@angular/material/dialog';
import { DeploymentService } from 'src/app/data/service/deployment.service';
import { AngularFireDatabase } from '@angular/fire/database';
import { FirebaseService } from 'data/service/firebase/firebase.service';
@Component({
  selector: 'app-import-dossier-v2',
  templateUrl: './import-dossier-v2.component.html',
  styleUrls: ['./import-dossier-v2.component.scss']
})
export class ImportDossierV2Component implements OnInit {

  constructor(
    public basedataService: BasedataService,
    public sectorService: SectorService,
    public procedureService: ProcedureService,
    private envService: EnvService,
    public dossierService: DossierService,
    private snackbarService: SnackbarService,
    private dialog: MatDialog,
    private deploymentService: DeploymentService,
    private db: AngularFireDatabase,
    private firebaseService: FirebaseService
  ) { }

  config = this.envService.getConfig();
  errMessage = "";
  selectedLang = localStorage.getItem('language') || 'vi';
  enableImportDossierFulltime = this.deploymentService?.env?.enableImportDossierFulltime ? this.deploymentService?.env?.enableImportDossierFulltime : false;
  agencyList = [];
  agencyKeyword = "";
  agencyTotalPages = 0;
  agencyPageIndex = 0;
  timeOutAgency = null;
  agency: any;

  sectorList = [];
  sectorKeyword = "";
  sectorTotalPages = 0;
  sectorPageIndex = 0;
  timeOutSector = null;
  sector: any;

  procedureList = [];
  procedureKeyword = "";
  procedureTotalPages = 0;
  procedurePageIndex = 0;
  timeOutProcedure = null;
  procedure: any;

  size = 10;

  userAgency : any = null;
  blankVal = '';

  dossierFile: File;
  feeFile: File;
  formFile: File;
  taskFile: File;
  acceptedDossierFiles = ['.XLS', '.XLSX', '.CVS'];
  isFileUploaded: boolean = false;
  isFeeFileUploaded: boolean = false;
  isFormFileUploaded: boolean = false;
  isTaskFileUploaded: boolean = false;
  componentFiles = [];
  componentFileNames = [];
  acceptFileExtension = this.config.acceptFileExtension;
  isLoading = false;

  importDossier: any = undefined;
  importDossierFee: any = undefined;
  importFormFile: any = undefined;
  importResult: any;
  importStatusBar: boolean = false;
  
  APPLICANT_GUIDE_DATA = {
    title: this.selectedLang == "en" ? "Applicant dossier info" : "Thông tin hồ sơ",
    data: APPLICANT_GUIDE_DATA,
    displayedColumns: ["STT", "key", "description"]
  };

  FEE_GUIDE_DATA = {
    title: this.selectedLang == "en" ? "Fee, Service fee info" : "Thông tin phí, lệ phí",
    data: FEE_GUIDE_DATA,
    displayedColumns: ["STT", "key", "description"]
  };

  COMPOSITION_GUIDE_DATA = {
    title: this.selectedLang == "en" ? "Dossier composition" : "Thành phần hồ sơ",
    data: COMPOSITION_GUIDE_DATA,
    displayedColumns: ["STT", "key", "description"]
  };

  LIST_TABLE_GUIDE = [
    this.APPLICANT_GUIDE_DATA, // Thông tin hồ sơ
    // this.FEE_GUIDE_DATA,  // Thông tin phí, lệ phí
    // this.COMPOSITION_GUIDE_DATA // Thành phần hồ sơ
  ]

  ngOnInit(): void {
   this.firebaseService.remove("importDossier","dossier");
   this.firebaseService.remove("importDossier","fee");
   this.firebaseService.remove("importDossier","formFile");
   this.firebaseService.remove("importDossier","task");
   this.userAgency = JSON.parse(localStorage.getItem("userAgency"));
  }

  ngAfterViewInit(){
    this.listenImportDossier();
    this.listenImportDossierFee();
    this.listenImportFormFile();
  }


  onSelectImportFile(event){
    if(event.target.files?.length > 0){
      this.dossierFile = event.target.files[0];
      this.isFileUploaded = true;
    }else{
      this.isFileUploaded = false;
    }
  }

  onSelectFeeFile(event){
        if(event.target.files?.length > 0){
            this.feeFile = event.target.files[0];
            this.isFeeFileUploaded = true;
        }else{
            this.isFeeFileUploaded = false;
        }
  }
  onSelectFormFile(event){
        if(event.target.files?.length > 0){
            this.formFile = event.target.files[0];
            this.isFormFileUploaded = true;
        }else{
            this.isFormFileUploaded = false;
        }
  }
  onSelectTaskFile(event){
    if(event.target.files?.length > 0){
      this.taskFile = event.target.files[0];
      this.isTaskFileUploaded = true;
    }else{
      this.isTaskFileUploaded = false;
    }
  }

  getIconFilename(fileName) {
    const fileIcon = fileName.split('.').pop();
    return (
      this.config.cloudStaticURL +
      'icon/files/512x512/' +
      fileIcon.toLowerCase() +
      '.png'
    );
  }

  deleteDossierFile(){
    this.dossierFile = null;
    this.isFileUploaded = false;
  }
  deleteFeeFile(){
        this.feeFile = null;
        this.isFeeFileUploaded = false;
  }
  deleteFormFile(){
        this.formFile = null;
        this.isFormFileUploaded = false;
  }
  deleteTaskFile(){
    this.taskFile = null;
    this.isTaskFileUploaded = false;
}

  removeComponentFile(i){
    this.componentFiles.splice(i,1);
    this.componentFileNames.splice(i,1);
  }

  onSelectAttachment(event){
    if(event.target.files?.length > 0){
      for (const file of event.target.files) {
        this.componentFiles.push(file);
        this.componentFileNames.push({
          name:file.name
        });
      }
    }
  }


  
  

  onSumit(){
      //debugger;
      this.importStatusBar = false
      this.firebaseService.remove("importDossier","dossier");
      this.firebaseService.remove("importDossier","fee");
      this.firebaseService.remove("importDossier","formFile");
      // this.firebaseService.remove("importDossier","task");
      this.listenImportDossier();
      this.listenImportDossierFee();
      this.listenImportFormFile();
      //debugger
      const hour = new Date().getHours();
      if (!this.enableImportDossierFulltime && hour <= 17 && hour >= 7) {
          const msgObj = {
              vi: "Vui lòng sử dụng chức năng import sau 17h!",
              en: "Please use import function after 17h!"
          };
          this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', 5000);
          return;
      }
      
      
    if(!this.dossierFile && !this.formFile && !this.feeFile && !this.taskFile){
      const msgObj = {
        vi: "Vui lòng chọn file import!",
        en: "Please choose a dossier excel file!"
      }
      this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', 5000);
      return;
    }
    let formData : FormData = new FormData();
    if(this.dossierFile) {
          formData.append('dossierFile', this.dossierFile);
    }
    if(this.feeFile) {
          formData.append('feeFile', this.feeFile);
    }
    if(this.formFile){
        formData.append("formFile",this.formFile);
    }
    this.componentFiles.forEach(file => {
          formData.append("files",file);
    });
    if(this.taskFile) {
      formData.append('dossierTaskFile', this.taskFile);
}
    this.isLoading = true;
    this.importStatusBar = true;
    this.dossierService.importDossierV2FromExcell(formData).subscribe(res => {
     // this.isLoading = false;
    //  this.resetForm();
    this.importResult = res;
    }, err => {
      console.log(err);
      let errMsg = "";
      if(err.error.code){
        errMsg = ": "+err.error.message;
          this.snackbarService.openSnackBar(0, errMsg, '', 'error_notification', 5000);
      }
        //this.isLoading = false;
    })
  }

  resetForm(){
    this.componentFiles = [];
    this.componentFileNames = [];
    this.dossierFile = null;
    this.feeFile = null;
    this.formFile = null;
    this.isFileUploaded = false;
    this.isFeeFileUploaded = false;
    this.isFormFileUploaded = false;
    this.taskFile = null;
    this.isTaskFileUploaded = false;
  }

  downloadTemplate(){
    window.open(this.config.dossierV2TemplateUrl, '_self');
  }
  dossierFeeV2TemplateUrl(){
        window.open(this.config.dossierFeeV2TemplateUrl, '_self');
  }
  dossierFormFileV2TemplateUrl(){
     window.open(this.config.dossierFormFileV2TemplateUrl, '_self');
  }
  dossierTaskV2TemplateUrl(){
     window.open(this.config.dossierTaskV2TemplateUrl, '_self');
  }

  listenImportDossier() {
    const listenBy = `${this.deploymentService.getAppDeployment()?.fireBase?.ref}/importDossier`;
    const data = this.db.list(listenBy);
    data.snapshotChanges(['child_changed', 'child_added']).subscribe(actions => {
      const action = actions[0];
      if (action) {
        this.importDossier = action.payload.val();
        // if (this.importDossier?.inProgress == false && this.importDossier?.total == 0) {
        //   this.snackbarService.openSnackBar(0, this.importDossier?.message, '', 'error_notification', 3000);
        // }
          if(this.importDossier?.inProgress == false && this.importFormFile != undefined && this.importFormFile?.inProgress == false && this.importDossierFee != undefined && this.importDossierFee?.inProgress == false) {
              this.isLoading = false;
              this.resetForm();
          }else{
              this.isLoading = true;
          }
      }
    });
  }

    listenImportDossierFee() {
        const listenBy = `${this.deploymentService.getAppDeployment()?.fireBase?.ref}/importDossier`;
        const data = this.db.list(listenBy);
        data.snapshotChanges(['child_changed', 'child_added']).subscribe(actions => {
            const action = actions[1];
            if (action) {
                this.importDossierFee = action.payload.val();
                // if (this.importDossier?.inProgress == false && this.importDossier?.total == 0) {
                //   this.snackbarService.openSnackBar(0, this.importDossier?.message, '', 'error_notification', 3000);
                // }
                if(this.importDossierFee?.inProgress == false && this.importDossier != undefined && this.importDossier?.inProgress == false && this.importFormFile != undefined && this.importFormFile?.inProgress == false) {
                    this.isLoading = false;
                    this.resetForm();
                }else{
                    this.isLoading = true;
                }
            }
        });
    }

  listenImportFormFile() {
    const listenBy = `${this.deploymentService.getAppDeployment()?.fireBase?.ref}/importDossier`;
    const data = this.db.list(listenBy);
    data.snapshotChanges(['child_changed', 'child_added']).subscribe(actions => {
      const action = actions[2];
      if (action) {
        this.importFormFile = action.payload.val();
        // if (this.importFormFile?.inProgress == false && this.importFormFile?.total == 0) {
        //   this.snackbarService.openSnackBar(0, this.importFormFile?.message, '', 'error_notification', 3000);
        // }
        if(this.importDossierFee != undefined && this.importDossierFee?.inProgress == false && this.importDossier != undefined && this.importDossier?.inProgress == false && this.importFormFile?.inProgress == false) {
          this.isLoading = false;
            this.resetForm();
        }else{
            this.isLoading = true;
        }
      }
    });
  }

}
