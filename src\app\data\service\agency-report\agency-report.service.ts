import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { ApiProviderService } from 'src/app/core/service/api-provider.service';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class AgencyReportService {

  constructor(
    private http: HttpClient,
    private apiProviderService: ApiProviderService

  ) { }

  private sectorPath = this.apiProviderService.getUrl('digo', 'basepad') + '/sector';
  private levelPath = this.apiProviderService.getUrl('digo', 'basecat') + '/tag/';
  private agencyPath = this.apiProviderService.getUrl('digo', 'basedata') + '/agency';
  private procedurePath = this.apiProviderService.getUrl('digo', 'basepad') + '/procedure';
  private reporterPath = this.apiProviderService.getUrl('digo', 'reporter') + '/dossier';
  private reporterPath1 = this.apiProviderService.getUrl('digo', 'reporter') + '/agency';
  private humanPath = this.apiProviderService.getUrl('digo', 'human') + '/user';
  private padmanPath = this.apiProviderService.getUrl('digo', 'padman') + '/dossier';
  getListAgency(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.agencyPath + searchString, { headers });
  }

  getListSector(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.sectorPath + searchString, { headers });
  }

  getListDeclareAgency(): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.agencyPath + '/name+tag', { headers });
  }

  getListChildAgency(search: string): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.agencyPath + '/--child-tree' + search, { headers });
  }

  getListTableAgency(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.agencyPath + '/name+logo-id' + searchString, { headers });
  }

  getOfficers(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.humanPath + '/fullname+experience' + searchString, { headers });
  }

  getNumberOfDossierStatus(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.padmanPath + '/report' + searchString, { headers });
  }

  getListSectorByAgencyId(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.reporterPath1 + '/--sector' + searchString, { headers });
  }

}
