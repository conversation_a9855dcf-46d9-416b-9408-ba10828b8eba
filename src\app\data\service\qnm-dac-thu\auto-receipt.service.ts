import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { ApiProviderService } from 'src/app/core/service/api-provider.service';
import { Observable } from 'rxjs';
import { Workbook } from 'exceljs';
import { DatePipe } from '@angular/common';
import * as fs from 'file-saver';
import { DossierBlocking } from 'modules/dossier/pages/blocking/pages/blocking-dossier/blocking-dossier.component';

const EXCEL_TYPE = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8';
const EXCEL_EXTENSION = '.xlsx';

@Injectable({
  providedIn: 'root'
})
export class AutoReceiptService {

  private padmanURL = this.apiProviderService.getUrl('digo', 'padman');
  private autoReceiptPath = this.padmanURL + '/dac-thu-qnm';

  constructor(
    private http: HttpClient,
    private datePipe: DatePipe,
    private apiProviderService: ApiProviderService
  ) { }

  getListConfigAutoReceipt(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    console.log('autoReceiptPath:', this.autoReceiptPath);
    console.log('searchString:', searchString);
    return this.http.get(this.autoReceiptPath + '/get-config-auto-receipt/search' + searchString, { headers });
  }


  createConfigAutoReceipt(requestBody) {
    try {
      console.log('WS');
      let headers = new HttpHeaders();
      headers = headers.set('Content-Type', 'application/json');
      return this.http.post<any>(this.autoReceiptPath + '/create-config-auto-receipt', requestBody, { headers });
    } catch (e) {
      console.log(e);
    }
  }

  getConfigAutoReceipt(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.autoReceiptPath + '/get-config-auto-receipt-by-id/' + id, { headers });
  }

  updateConfigAutoReceipt(id, requestBody) {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.put<any>(this.autoReceiptPath + '/update-config-auto-receipt/' + id, requestBody, { headers });
  }

  deleteConfigAutoReceipt(id: string) {
    let headers = new HttpHeaders();
    headers = headers.append('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.delete(this.autoReceiptPath + '/delete-config-auto-receipt/' + id, { headers });
  }

  public exportToExcel(
      reportTitle: string,
      json: any[],
      excelFileName: string,
      sheetName: string
  ) {
    const data = json;

    // create workbook and worksheet
    const workbook = new Workbook();
    workbook.created = new Date();
    workbook.modified = new Date();
    const worksheet = workbook.addWorksheet(sheetName);

    worksheet.getColumn('A').width = 15;
    worksheet.getColumn('A').font = {name: 'Times New Roman', size: 12};
    worksheet.getColumn('A').alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};

    worksheet.getColumn('B').width = 35;
    worksheet.getColumn('B').font = {name: 'Times New Roman', size: 12};
    worksheet.getColumn('B').alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};

    worksheet.getColumn('C').width = 50;
    worksheet.getColumn('C').font = {name: 'Times New Roman', size: 12};
    worksheet.getColumn('C').alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};

    worksheet.getColumn('D').width = 35;
    worksheet.getColumn('D').font = {name: 'Times New Roman', size: 12};
    worksheet.getColumn('D').alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};

    worksheet.getColumn('E').width = 30;
    worksheet.getColumn('E').font = {name: 'Times New Roman', size: 12};
    worksheet.getColumn('E').alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};

    worksheet.getColumn('F').width = 90;
    worksheet.getColumn('F').font = {name: 'Times New Roman', size: 12};
    worksheet.getColumn('F').alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};

    // Nội dung TABLE-TITLE
    worksheet.mergeCells('A2:F4');
    worksheet.getCell('A2').value = reportTitle;
    worksheet.getCell('A2').font = {name: 'Times New Roman', size: 15, bold: true};
    worksheet.getCell('A2').alignment = { horizontal: 'center', vertical: 'middle' };

    // Nội dung TABLE-HEADER
    worksheet.getCell('A6').value = 'STT';
    worksheet.getCell('B6').value = 'Sổ bìa đỏ';
    worksheet.getCell('C6').value = 'Tên người sử dụng';
    worksheet.getCell('D6').value = 'Thửa đất số';
    worksheet.getCell('E6').value = 'Ngày ký';
    worksheet.getCell('F6').value = 'Nội dung';

    worksheet.properties.outlineLevelCol = 2;
    worksheet.properties.defaultRowHeight = 15;

    const y = 6;
    const rowLength = 6;
    for (let x = 1; x <= rowLength; x++) {
      const workCell = worksheet.getCell(y, x);
      workCell.alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
      workCell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: {argb: 'C0C0C0C0'},
        bgColor: {argb: 'FF0000FF'}
      };
      workCell.border = {top: {style: 'thin'}, left: {style: 'thin'}, bottom: {style: 'thin'}, right: {style: 'thin'}};
      workCell.font = {size: 12, bold: true, name: 'Times New Roman'};
    }

    // add data and cell formatting
    data.forEach((element: any, index) => {
      const rowArr = [
        index + 1,
        element?.certificate?.certificateId,
        element?.certificate?.ownerFullname,
        element?.certificate?.slotId,
        element?.blockDate ? this.datePipe.transform(element?.blockDate, 'dd/MM/yyyy').toString() : '',
        element?.content
      ];
      const borderRow = worksheet.addRow(rowArr);
      borderRow.eachCell((cell) => {
        cell.border = {
          top: {style: 'thin'},
          left: {style: 'thin'},
          bottom: {style: 'thin'},
          right: {style: 'thin'}
        };
        cell.alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
      });
    });

    // Save Excel File
    workbook.xlsx.writeBuffer().then((dataBuffer: ArrayBuffer) => {
      const blob = new Blob([dataBuffer], {type: EXCEL_TYPE});
      fs.saveAs(blob, excelFileName + EXCEL_EXTENSION);
    });
  }

}
