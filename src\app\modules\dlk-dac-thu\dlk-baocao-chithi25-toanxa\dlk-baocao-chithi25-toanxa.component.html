<h2><PERSON><PERSON>O CÁO CHỈ THỊ 25 TOÀN TỈNH - CẤP XÃ</h2>
<div class="prc_searchbar">

    <div fxLayout="row" fxLayout.xs="row" fxLayout.sm="row" class="formFieldOutline" fxLayoutAlign="start">
        <div appearance="outline" fxFlex='grow'>
            <div fxLayout="row" fxLayout.xs="row" fxLayout.sm="row" class="formFieldOutline" fxLayoutAlign="start">

                <mat-form-field appearance="outline" fxFlex.gt-sm="24" fxFlex.gt-xs="49.5" fxFlex='grow'>
                    <mat-label>Từ ngày</mat-label>
                    <input matInput [matDatepicker]="pickerAcceptFrom" [(ngModel)]="startDate">
                    <mat-datepicker-toggle matSuffix [for]="pickerAcceptFrom"></mat-datepicker-toggle>
                    <mat-datepicker #pickerAcceptFrom></mat-datepicker>
                </mat-form-field>
                <mat-form-field class="ml-20" appearance="outline" fxFlex.gt-sm="24" fxFlex.gt-xs="49.5" fxFlex='grow'>
                    <mat-label>Đến ngày</mat-label>
                    <input matInput [matDatepicker]="pickerAcceptTo" [(ngModel)]="endDate">
                    <mat-datepicker-toggle matSuffix [for]="pickerAcceptTo"></mat-datepicker-toggle>
                    <mat-datepicker #pickerAcceptTo></mat-datepicker>
                </mat-form-field>
            </div>
        </div>


    </div>


    <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="row" fxLayoutAlign="end" style="padding-bottom: 1em;">
        <button mat-flat-button fxFlex.lt-md="22" fxFlex.md="20" fxFlex.gt-md="12" fxFlex.gt-xs="49.5" fxFlex='grow' style="background-color: #ce7a58;" class="btn-search" type="submit" (click)="thongKe()" [disabled]="waitingDownloadExcel"> 
      <mat-icon class="iconStatistical">bar_chart</mat-icon><span>Thống kê</span>
      <mat-progress-bar mode="indeterminate" *ngIf="waitingDownloadExcel"></mat-progress-bar>
    </button>
        <div fxFlex='1'></div>
        <button mat-flat-button fxFlex.lt-md="22" fxFlex.md="20" fxFlex.gt-md="12" fxFlex.gt-xs="49.5" fxFlex='grow' class="btn-download-excel" (click)="exportToExcel()" [disabled]="waitingDownloadExcel" style="background-color: #38A938;">
      <mat-icon class="iconStatistical">cloud_download</mat-icon>
      <span>Xuất excel</span>
      <mat-progress-bar mode="indeterminate" *ngIf="waitingDownloadExcel"></mat-progress-bar>
    </button>

    </div>
</div>

<div fxLayout="row" fxLayoutAlign="center">
    <div class="frm_main" fxFlex="grow">
        <div class="logbookTbl">
            <div class="logbookOnlyTbl">
                <table class="table">
                    <thead>
                        <tr>
                            <th rowspan="4">STT</th>
                            <th rowspan="4">Cơ quan/Đơn vị</th>
                            <th colspan="3">Tổng số hồ sơ đã tiếp nhận trong tháng</th>
                            <th colspan="10">Số hồ sơ đã giải quyết trong tháng</th>
                            <th colspan="3">Số hồ sơ còn tồn chưa giải quyết</th>
                        </tr>
                        <tr>
                            <th rowspan="3">Tổng số</th>
                            <th colspan="2">Trong đó</th>
                            <th rowspan="3">Tổng số</th>
                            <th colspan="9">Trong đó</th>
                            <th rowspan="3">Tổng số</th>
                            <th colspan="2">Trong đó</th>
                        </tr>
                        <tr>
                            <th rowspan="2">Số hồ sơ chưa giải quyết của tháng trước chuyển qua</th>
                            <th rowspan="2">Tổng số hồ sơ tiếp nhận mới trong tháng</th>

                            <th rowspan="2">Giải quyết trước, đúng hạn</th>
                            <th rowspan="2">Giải quyết quá hạn</th>
                            <th colspan="5">Số văn bản xin lỗi</th>
                            <th rowspan="2">Công khai số văn bản xin lỗi lên hệ thông thông tin giải quyết TTHC (iGate)</th>
                            <th rowspan="2">Số văn bản xin lỗi đã niêm yết tại bộ phận 1 cửa</th>

                            <th rowspan="2">Số hồ sơ trước, đúng thời hạn</th>
                            <th rowspan="2">Số hồ sơ quá thời hạn</th>
                        </tr>
                        <tr>
                            <th>Tổng số</th>
                            <th>Do giải quyết quá hạn</th>
                            <th>Do tiếp nhận thành phần hồ sơ không đủ</th>
                            <th>Do hồ sơ bị mất, thất lạc hoặc hư hỏng</th>
                            <th>Do sai sót trong kết quả giải quyết</th>
                        </tr>
                        <tr>
                            <th>(1)</th>
                            <th>(2)</th>
                            <th>(3)</th>
                            <th>(4)</th>
                            <th>(5)</th>
                            <th>(6)</th>
                            <th>(7)</th>
                            <th>(8)</th>
                            <th>(9)</th>
                            <th>(10)</th>
                            <th>(11)</th>
                            <th>(12)</th>
                            <th>(13)</th>
                            <th>(14)</th>
                            <th>(15)</th>
                            <th>(16)</th>
                            <th>(17)</th>
                            <th>(18)</th>
                        </tr>
                    </thead>
                    <tbody>

                        <tr>
                            <td colspan="23" style="text-align: left; font-weight: bold">Đơn vị cấp xã</td>
                        </tr>

                        <ng-container *ngFor="let item of listHoSoCapTinh;let i = index ">
                            <tr *ngIf="listHoSoCapTinh.length>0">
                                <td rowspan="1">{{i+1}}</td>
                                <td rowspan="1" style="text-align: left;">{{item.coQuan}}</td>
                                <td><a (click)="GetDetailDossier(item.id,item.coQuan,item.data[0].TrongKy,1,0,0,0,0,0,0,0,0,0)">{{item.data[0].tongSoHoSo}}</a></td>
                                <td><a (click)="GetDetailDossier(item.id,item.coQuan,item.data[0].TrongKy,0,1,0,0,0,0,0,0,0,0)">{{item.data[0].soHoSoTonKyTruoc}}</a></td>
                                <td><a (click)="GetDetailDossier(item.id,item.coQuan,item.data[0].TrongKy,0,0,1,0,0,0,0,0,0,0)">{{item.data[0].soHoSoTN}}</a></td>
                                <td><a (click)="GetDetailDossier(item.id,item.coQuan,item.data[0].TrongKy,0,0,0,1,0,0,0,0,0,0)">{{item.data[0].soHoSoDXL}}</a></td>
                                <td><a (click)="GetDetailDossier(item.id,item.coQuan,item.data[0].TrongKy,0,0,0,0,1,0,0,0,0,0)">{{item.data[0].soHoSoDXLTrongHan}}</a></td>
                                <td><a (click)="GetDetailDossier(item.id,item.coQuan,item.data[0].TrongKy,0,0,0,0,0,1,0,0,0,0)">{{item.data[0].soHoSoDXLQuaHan}}</a></td>
                                <td>{{item.data[0].tongSoVBXL}}</td>
                                <td>{{item.data[0].vanban_QUAHAN}}</td>
                                <td>{{item.data[0].vanban_THIEU_TPHS}}</td>
                                <td>{{item.data[0].vanban_MAT_HS}}</td>
                                <td>{{item.data[0].vanban_SAISOT}}</td>
                                <td>{{item.data[0].tongSoVBXL}}</td>
                                <td>{{item.data[0].tongSoVBXL}}</td>
                                <td><a (click)="GetDetailDossier(item.id,item.coQuan,item.data[0].TrongKy,0,0,0,0,0,0,1,0,0,0)">{{item.data[0].soHoSoTON}}</a></td>
                                <td><a (click)="GetDetailDossier(item.id,item.coQuan,item.data[0].TrongKy,0,0,0,0,0,0,0,1,0,0)">{{item.data[0].soHoSoTONCONHAN}}</a></td>
                                <td><a (click)="GetDetailDossier(item.id,item.coQuan,item.data[0].TrongKy,0,0,0,0,0,0,0,0,1,0)">{{item.data[0].soHoSoTONQUAHAN}}</a></td>

                            </tr>
                        </ng-container>
                        <tr *ngIf="listHoSoCapTinh.length>0" class="sum">
                            <td colspan="2"> TỔNG CẤP XÃ</td>
                            <td> {{TongCapTinh.tongSoHoSo}}</td>
                            <td> {{TongCapTinh.soHoSoTonKyTruoc}}</td>
                            <td> {{TongCapTinh.soHoSoTN}}</td>
                            <td> {{TongCapTinh.soHoSoDXL}}</td>
                            <td> {{TongCapTinh.soHoSoDXLTrongHan}}</td>
                            <td> {{TongCapTinh.soHoSoDXLQuaHan}}</td>
                            <td> {{TongCapTinh.tongSoVBXL}}</td>
                            <td> {{TongCapTinh.vanban_QUAHAN}}</td>
                            <td> {{TongCapTinh.vanban_THIEU_TPHS}}</td>
                            <td> {{TongCapTinh.vanban_MAT_HS}}</td>
                            <td> {{TongCapTinh.vanban_SAISOT}}</td>
                            <td> {{TongCapTinh.tongSoVBXL}}</td>
                            <td> {{TongCapTinh.tongSoVBXL}}</td>
                            <td> {{TongCapTinh.soHoSoTON}}</td>
                            <td> {{TongCapTinh.soHoSoTONCONHAN}}</td>
                            <td> {{TongCapTinh.soHoSoTONQUAHAN}}</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>