import { Injectable } from '@angular/core';
import { AdapterService } from '../adapter/adapter.service';
import { DeploymentService } from '../deployment.service';

@Injectable({
  providedIn: 'root'
})
export class SocialProtectionKTMService {
  private verifyFinanceObligationId = this.deploymentService.env.OS_KTM.verifyFinanceObligationId;
  private payFinanceObligationId = this.deploymentService.env.OS_KTM.payFinanceObligationId;
  private mappingDossierStatusId = this.deploymentService.env.OS_KTM.mappingDossierStatusId;
  constructor(
    private deploymentService: DeploymentService,
    private adapterService: AdapterService,
  ) { }

  async mappingStatusId(source: number, dossierMenuTaskRemind: any){
    if(dossierMenuTaskRemind == this.verifyFinanceObligationId || dossierMenuTaskRemind == this.payFinanceObligationId){ // nghia vu tai chinh
      return "6";
    }
    let result = "";
    const res = await this.adapterService.getMappingDataKTM(this.mappingDossierStatusId, source).toPromise();
    if(res.dest){
      result = res.dest.id;
    }
    return result;
  }

  checkIfDossierNeedSyncBTXH(procedure) {
    return procedure.btxhcode ? true : false;
  }
}