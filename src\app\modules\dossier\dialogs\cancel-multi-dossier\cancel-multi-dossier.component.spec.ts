import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { CancelMultiDossierComponent } from './cancel-multi-dossier.component';

describe('CancelMultiDossierComponent', () => {
  let component: CancelMultiDossierComponent;
  let fixture: ComponentFixture<CancelMultiDossierComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ CancelMultiDossierComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(CancelMultiDossierComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
