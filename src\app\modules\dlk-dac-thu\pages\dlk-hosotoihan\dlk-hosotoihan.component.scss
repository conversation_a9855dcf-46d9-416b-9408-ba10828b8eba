.search-nested {
    width: 80% !important;
    height: 50px !important;
    margin-left: 15px !important;
}

.clear-search-nested {
    right: 0 !important;
    position: absolute !important;
    top: 0 !important;
}

.data-label {
    word-wrap: break-word;
}


.mat-tooltip {
    font-size: 13px;
}

.frm_main {
    border-radius: 4px;
    padding: 1em;
    box-shadow: 0px 1px 15px rgba(69, 65, 78, 0.19);

    .ctrl {
        .btnCtrl {
            background-color: #e8e8e8;
            color: #666;
            float: right;
            margin-left: 1em;

            .mat-icon {
                color: #ce7a58;
                margin-right: .2em;
            }
        }
    }

    .logbookTbl {
        .logbookOnlyTbl {
            overflow-x: scroll;
        }

        table {
            border-collapse: collapse;
            width: 100%;
        }

        table tr th {
            border: 1px solid;
            text-align: center !important;
            font-weight: bold;
            background: #cccccc50;
            //color: #fff;
            border: 1px solid rgb(182, 182, 182);
            padding: 5px;
            font-size: 14px;
        }

        table tr td {
            border: 1px solid rgb(182, 182, 182);
            text-align: left !important;
            padding: 5px;
            font-size: 13px;
        }
    }
}

::ng-deep .prc_searchbar .searchForm {
    @import "~src/styles/buttons.scss";

    .btn-search {
        @extend .t-btn-search;
    }

    .btn-download-excel {
        @extend .t-btn-download-excel;
    }

    .btn-select-display-col {
        @extend .t-btn-select-display-col;
    }


    .btn-print {
        @extend .t-btn-print;
    }
}

.logbookOnlyTbl_mobile {
    display: none;
}

::ng-deep .fs_wrapper {
    border: 1px solid rgba(69, 65, 78, 0.19);
    border-radius: 8px;

    .mat-form-field-wrapper {
        padding-bottom: 0;
    }
}

.error_MsgCustom {
    font-size: 12px !important;
    display: flex;
    color: #ff0000;
    margin-top: 0.5em;

    span {
        // margin-left: auto;
        align-self: center;
    }

    .err {
        background-color: #f2a63494;
        border-radius: 50%;
        width: 1.2em;
        height: 1.2em;
        justify-content: center;
        display: flex;
        margin-left: 0.5em;
        align-self: center;

        .mat-icon {
            color: #ff0000;
            vertical-align: middle;
            align-self: center;
            transform: scale(0.5);
            justify-content: center;
            margin-left: 0.05em;
        }
    }
}

@media screen and (max-width: 600px) {
    .logbookOnlyTbl {
        display: none;
    }

    .logbookOnlyTbl_mobile {
        display: block !important;

        .mat-header-row {
            display: none;
        }

        .mat-table {
            border: 0;
            vertical-align: middle;

            .mat-row {
                border-bottom: 5px solid #ddd;
                display: block;
                min-height: unset;
            }

            .mat-cell {
                border-bottom: 1px solid #ddd;
                display: block;
                font-size: 14px;
                text-align: right;
                margin-bottom: 4%;
                padding: 0 0.5em;

                &:before {
                    content: attr(data-label);
                    float: left;
                    font-weight: 500;
                    font-size: 14px;
                    width: 45%;
                    text-align: left;
                }

                &:last-child {
                    border-bottom: 0;
                }

                &:first-child {
                    margin-top: 4%;
                }
            }
        }

        .mat-row {
            &:nth-child(even) {
                background-color: unset;
            }

            &:nth-child(odd) {
                background-color: unset;
            }
        }

        .fs_wrapper {
            border-radius: 6px;
        }
    }
}