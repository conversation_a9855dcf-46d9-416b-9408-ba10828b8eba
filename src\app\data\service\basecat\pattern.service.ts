import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { ApiProviderService } from 'src/app/core/service/api-provider.service';
import { EnvService } from 'src/app/core/service/env.service';

@Injectable({
    providedIn: 'root'
})
export class PatternService {

    constructor(
        private http: HttpClient,
        private envService: EnvService,
        private apiProviderService: ApiProviderService,
    ) { }

    private getNextValueUrls(id: string): string{
        return this.apiProviderService.getUrl('digo', 'basecat') + `/pattern/${id}/--get-next-value`;
    }

    public getNextValue(id: string, agency: string) : Observable<any> {
        let headers = new HttpHeaders();
        headers = headers.set('Content-Type', 'application/json');
        let endpoint = this.getNextValueUrls(id);
        if(!!agency){
            endpoint += `?code=${agency}`
        }
        return this.http.put<any>(endpoint, '', { headers });
    }
}
