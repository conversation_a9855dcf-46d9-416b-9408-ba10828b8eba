import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { ApiProviderService } from 'src/app/core/service/api-provider.service';
import { Observable } from 'rxjs';
import { EnvService } from 'src/app/core/service/env.service';

@Injectable({
  providedIn: 'root'
})
export class EInvoiceService {

  config = this.envService.getConfig();
  constructor(
    private envService: EnvService,
    private http: HttpClient,
    private apiProviderService: ApiProviderService
  ) { }

  private human = this.apiProviderService.getUrl('digo', 'human');
  private adapter = this.apiProviderService.getUrl('digo', 'adapter');
  private basecat = this.apiProviderService.getUrl('digo', 'basecat');
  private basedata = this.apiProviderService.getUrl('digo', 'basedata');
  private padman = this.apiProviderService.getUrl('digo', 'padman');
  private basepad = this.apiProviderService.getUrl('digo', 'basepad');

  getUserExperience(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.human + '/user/' + id + '/experience', { headers });
  }

  issueEInvoice(data): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    return this.http.post(this.adapter + '/e-invoice/--issue', data, { headers });
  }

  postDossierInvoice(data): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    return this.http.post(this.padman + '/dossier-invoice', data, { headers });
  }

  getListInvoiceTagCategory(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.basecat + '/tag/--by-category-id?category-id=' + id + '&size=50', { headers });
  }

  getListTaxValueTagCategory(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.basecat + '/tag/--by-category-id?category-id=' + id + '&size=50', { headers });
  }

  getDetailTypeInvoice(id): Promise<any> {
    return this.http.get(this.basecat + '/tag/' + id).toPromise();
  }

  getAgency(id): Promise<any> {
    return this.http.get(this.basedata + '/agency/' + id).toPromise();
  }

  getPlace(id): Promise<any> {
    return this.http.get(this.basedata + '/place/' + id).toPromise();
  }

  getCountDossierInvoice(statusId, dossierId): Promise<any> {
    const param = '?status-id=' + statusId + '&dossier-id=' + dossierId + '&spec=page';
    return this.http.get(this.padman + '/dossier-invoice' + param).toPromise();
  }

  getDossierInvoiceByProdId(statusId, prodId, dossierId): Promise<any> {
    // tslint:disable-next-line: max-line-length
    return this.http.get(this.padman + '/dossier-invoice/search-prod-id?status-id=' + statusId + '&prod-id=' + prodId + '&dossier-id=' + dossierId).toPromise();
  }

  getProcostForOnline(id): Promise<any> {
    return this.http.get(this.basepad + '/procost/--for-online?procedure-id=' + id).toPromise();
  }

  getListTagByCategoryId(categoryId, page): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    const param = '?category-id=' + categoryId + '&page=' + page;
    return this.http.get(this.basecat + '/tag/--by-category-id' + param, { headers });
  }

  getListInvoiceSupplier(id, page): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    const param = '&page=' + page;
    return this.http.get(this.basecat + '/tag/--by-category-id?category-id=' + id + param, { headers });
  }

  getListDossierInvoice(searchString): Observable<any> {
    return this.http.get(this.padman + '/dossier-invoice' + searchString);
  }

  printEInvoice(params): Promise<any> {
    return this.http.get(this.adapter + '/e-invoice/--print' + params).toPromise();
  }

  unPaymentEInvoice(params): Observable<any> {
    return this.http.get(this.adapter + '/e-invoice/--unpayment' + params);
  }

  paymentEInvoice(params): Observable<any> {
    return this.http.get(this.adapter + '/e-invoice/--payment' + params);
  }

  cancelEInvoice(params): Observable<any> {
    return this.http.delete(this.adapter + '/e-invoice/--cancel' + params);
  }

  printNoPayEInvoice(params): Promise<any> {
    return this.http.get(this.adapter + '/e-invoice/--print-no-pay' + params).toPromise();
  }

  printConvertEInvoice(params): Promise<any> {
    return this.http.get(this.adapter + '/e-invoice/--print-convert' + params).toPromise();
  }

  getDossierInvoice(id): Observable<any> {
    return this.http.get(this.padman + '/dossier-invoice/' + id);
  }

  putDossierInvoice(id, data): Observable<any> {
    return this.http.put(this.padman + '/dossier-invoice/' + id, data);
  }

}
