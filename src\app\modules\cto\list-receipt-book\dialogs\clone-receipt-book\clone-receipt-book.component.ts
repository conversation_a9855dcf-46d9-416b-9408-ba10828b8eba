import { Component, Inject, OnInit } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { ReceiptBookService } from 'src/app/data/service/cto-statistics/receipt-book.service';
import { SnackbarService } from 'src/app/data/service/snackbar/snackbar.service';

@Component({
    selector: 'app-clone-receipt-book',
    templateUrl: './clone-receipt-book.component.html',
    styleUrls: ['./clone-receipt-book.component.scss']
})

export class CloneReceiptBookComponent implements OnInit {
  selectedLang: string;
  receiptBook = new FormGroup({
    code: new FormControl('', Validators.required),
    name: new FormControl('', Validators.required),
    year: new FormControl('', Validators.required)
  });
    constructor(
        public dialogRef: MatDialogRef<CloneReceiptBookComponent>,
        @Inject(MAT_DIALOG_DATA) public data: ConfirmCloneReceiptBookModel,
        private receiptBookService: ReceiptBookService,
        private snackbarService: SnackbarService) {}

    ngOnInit(): void {
      this.selectedLang = localStorage.getItem('language');
      this.setEdit();
    }

    onDismiss(): void {
        this.dialogRef.close();
    }
    
    async onSubmit() {
      let formObj = this.receiptBook.getRawValue();
      let requestBody = {
        name: formObj?.name,
        code: formObj?.code,
        year: formObj?.year,
      }
      if (this.receiptBook.invalid === false) {
        this.receiptBookService.cloneReceiptBook(this.data.id, requestBody).subscribe(res => {
          const msgObj = {
            vi: "Clone sổ tiếp nhận thành công",
            en: "Clone receipt book successfully"
          }
          this.snackbarService.openSnackBar(1, msgObj[this.selectedLang], '', 'success_notification', 3000);
          this.dialogRef.close({
            status: true,
            name: requestBody
          });
        }, err => {
          console.log(err);
          const msgObj = {
            vi: "Không thể sao chép sổ tiếp nhận. Vui lòng thử lại sau!",
            en: "Failed to clone receipt book. Please try again later!"
          }
          this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', 3000);
        });
      }
    }
    
    async setEdit(){
      const details = await this.receiptBookService.details(this.data.id).toPromise();
      this.receiptBook = new FormGroup({
        name: new FormControl(details?.name, Validators.required),
        code: new FormControl(details?.code, Validators.required),
        year: new FormControl(details?.year, Validators.required)
      });
    } 
    
    preventInvalidKeys(event: KeyboardEvent) {
      const allowedKeys = [
        'Backspace', 'Tab', 'ArrowLeft', 'ArrowRight', 'Delete', 'Home', 'End'
      ];
      const isNumber = event.key >= '0' && event.key <= '9';
      if ((!isNumber && !allowedKeys.includes(event.key))) {
        event.preventDefault();
      }
    }    
}

export class ConfirmCloneReceiptBookModel {
    constructor(public id, public year) {}
}