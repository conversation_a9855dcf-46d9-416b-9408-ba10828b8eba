<button mat-icon-button class="close-button" (click)="onDismiss()">
    <mat-icon>close</mat-icon>
</button>
<h3 class="dialog_title" mat-dialog-title><PERSON><PERSON><PERSON><PERSON>ử lý</h3>
<form [formGroup]="addForm" (submit)="onConfirm()" class="addForm" id="ngAddForm">
    <div fxLayout="column" fxLayout.xs="column" fxLayout.sm="column">
        <mat-form-field appearance="outline" fxFlex="grow">
            <mat-label>Đơn vị</mat-label>
            <mat-select [disabled]="true" [value]="rootAgency">
                <mat-option [value]="rootAgency">
                    {{ rootAgency?.name || '(Không có tên)' }}
                </mat-option>
            </mat-select>
        </mat-form-field>
    </div>
    <div fxLayout="column" fxLayout.xs="column" fxLayout.sm="column">
        <mat-form-field appearance="outline" fxFlex='grow'>
            <mat-label>Phòng ban</mat-label>
            <mat-select msInfiniteScroll (infiniteScroll)="getAgencyList(true)" formControlName="agency" [required]="false"
                        [complete]="isAgencyListFull" (selectionChange)="onAgencyChange($event.value)">
            <mat-option>
                <ngx-mat-select-search formControlName="agencyCtrl" placeholderLabel=""
                                    [disableScrollToActiveOnOptionsChanged]="true"
                                    (keyup)="keyupSearch('agency')" noEntriesFoundLabel="Không tìm thấy kết quả nào!">
                <mat-icon ngxMatSelectSearchClear (click)="clearList('agency')">close</mat-icon>
                </ngx-mat-select-search>
            </mat-option>
            <mat-option value="">Tất cả</mat-option>
            <mat-option *ngFor="let agency of agencyList" [value]="agency">
                {{agency.name}}
                <span *ngIf="agency.name == undefined || agency.name == null">(Không tìm thấy bản
                                    dịch)</span>
            </mat-option>
            </mat-select>
        </mat-form-field>
    </div>
    <div fxLayout="column" fxLayout.xs="column" fxLayout.sm="column">
        <mat-form-field appearance="outline" fxFlex='grow'>
        <mat-label>Chức vụ</mat-label>
        <mat-select formControlName="position" [required]="false" (selectionChange)="onPositionChange($event.value)">
            <mat-option>
            <ngx-mat-select-search [formControl]="searchPositionCtrl" placeholderLabel=""
                                    [disableScrollToActiveOnOptionsChanged]="true"
                                    noEntriesFoundLabel="Không tìm thấy kết quả nào!">
            </ngx-mat-select-search>
            </mat-option>
            <mat-option *ngFor="let position of positionFiltered | async" value="{{position.id}}">
            {{ position.name }}
            </mat-option>
        </mat-select>
        </mat-form-field>
    </div>
    <div fxLayout="column" fxLayout.xs="column" fxLayout.sm="column">
        <mat-form-field appearance="outline" fxFlex='grow'>
        <mat-label>Người thực hiện</mat-label>
        <mat-select formControlName="assignee" [required]="true">
            <mat-option>
            <ngx-mat-select-search [formControl]="searchAssigneeCtrl" placeholderLabel=""
                                    [disableScrollToActiveOnOptionsChanged]="true"
                                    noEntriesFoundLabel="Không tìm thấy kết quả nào!">
            </ngx-mat-select-search>
            </mat-option>
            <mat-option *ngFor="let assignee of assigneeFiltered | async" [value]="{ id: assignee.id, name: assignee.fullname }">
                {{ assignee.fullname }}
            </mat-option>
        </mat-select>
        </mat-form-field>
    </div>
    <div fxLayout="column" fxLayout.xs="column" fxLayout.sm="column">
        <mat-form-field appearance="outline" fxFlex='grow'>
        <mat-label >Nội dung</mat-label>
        <textarea matInput formControlName="summary" required rows="5" maxlength="1000"
                  oninput="if(this.value.trim().length == 0) {this.value = ''}"></textarea>
        <mat-error class="error_Msg">
          <span >Vui lòng nhập Nội dung xử lý</span>
          <div class="err">
            <mat-icon>priority_high</mat-icon>
          </div>
        </mat-error>
      </mat-form-field>
    </div>
</form>
<div fxLayout="row" fxLayout.xs="column" fxLayout.sm="column" fxLayoutAlign="center">
    <button mat-flat-button class="applyBtn" type="submit" form="ngAddForm">
        <span i18n>Đồng ý</span>
    </button>
</div>
