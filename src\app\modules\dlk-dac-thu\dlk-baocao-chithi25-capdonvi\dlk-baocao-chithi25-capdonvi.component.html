<h2>BÁO CÁO CHỈ THỊ 25 CẤP ĐƠN VỊ</h2>
<div class="prc_searchbar">

    <div fxLayout="row" fxLayout.xs="row" fxLayout.sm="row" class="formFieldOutline" fxLayoutAlign="space-between">
        <div appearance="outline" fxFlex='grow'>
            <div fxLayout="row" fxLayout.xs="row" fxLayout.sm="row" class="formFieldOutline" fxLayoutAlign="space-between">

                <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                    <mat-label i18n>Cơ quan</mat-label>
                    <mat-select name="lstCoQuan" msInfiniteScroll (selectionChange)="changeAgency()" [(ngModel)]="agencyId">

                        <mat-option *ngFor="let item of listAgencyAccept" [value]="item.id">{{item.name}}</mat-option>
                    </mat-select>
                </mat-form-field>

            </div>
            <div fxLayout="row" fxLayout.xs="row" fxLayout.sm="row" class="formFieldOutline" fxLayoutAlign="space-between">

                <mat-form-field appearance="outline" fxFlex.gt-sm="24" fxFlex.gt-xs="49.5" fxFlex='grow'>
                    <mat-label>Từ ngày</mat-label>
                    <input matInput [matDatepicker]="pickerAcceptFrom" [(ngModel)]="startDate">
                    <mat-datepicker-toggle matSuffix [for]="pickerAcceptFrom"></mat-datepicker-toggle>
                    <mat-datepicker #pickerAcceptFrom></mat-datepicker>
                </mat-form-field>
                <mat-form-field appearance="outline" fxFlex.gt-sm="24" fxFlex.gt-xs="49.5" fxFlex='grow'>
                    <mat-label>Đến ngày</mat-label>
                    <input matInput [matDatepicker]="pickerAcceptTo" [(ngModel)]="endDate">
                    <mat-datepicker-toggle matSuffix [for]="pickerAcceptTo"></mat-datepicker-toggle>
                    <mat-datepicker #pickerAcceptTo></mat-datepicker>
                </mat-form-field>
                <mat-form-field appearance="outline" fxFlex.gt-sm="24" fxFlex.gt-xs="49.5" fxFlex='grow'>
                    <mat-label>Từ ngày lũy kế</mat-label>
                    <input matInput [matDatepicker]="pickerAcceptFrom1" [(ngModel)]="startDateCumulative">
                    <mat-datepicker-toggle matSuffix [for]="pickerAcceptFrom1"></mat-datepicker-toggle>
                    <mat-datepicker #pickerAcceptFrom1></mat-datepicker>
                </mat-form-field>
                <mat-form-field appearance="outline" fxFlex.gt-sm="24" fxFlex.gt-xs="49.5" fxFlex='grow'>
                    <mat-label>Đến ngày lũy kế</mat-label>
                    <input matInput [matDatepicker]="pickerAcceptTo1" [(ngModel)]="endDateCumulative">
                    <mat-datepicker-toggle matSuffix [for]="pickerAcceptTo1"></mat-datepicker-toggle>
                    <mat-datepicker #pickerAcceptTo1></mat-datepicker>
                </mat-form-field>

            </div>
        </div>


    </div>


    <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="row" fxLayoutAlign="end" style="padding-bottom: 1em;">
        <button mat-flat-button fxFlex.lt-md="22" fxFlex.md="20" fxFlex.gt-md="12" fxFlex.gt-xs="49.5" fxFlex='grow' style="background-color: #ce7a58;" class="btn-search" type="submit" (click)="thongKe()" [disabled]="waitingDownloadExcel"> 
      <mat-icon class="iconStatistical">bar_chart</mat-icon><span>Thống kê</span>
      <mat-progress-bar mode="indeterminate" *ngIf="waitingDownloadExcel"></mat-progress-bar>
    </button>
        <div fxFlex='1'></div>
        <button mat-flat-button fxFlex.lt-md="22" fxFlex.md="20" fxFlex.gt-md="12" fxFlex.gt-xs="49.5" fxFlex='grow' class="btn-download-excel" (click)="exportToExcel()" [disabled]="waitingDownloadExcel" style="background-color: #38A938;">
      <mat-icon class="iconStatistical">cloud_download</mat-icon>
      <span>Xuất excel</span>
      <mat-progress-bar mode="indeterminate" *ngIf="waitingDownloadExcel"></mat-progress-bar>
    </button>

    </div>
</div>

<div fxLayout="row" fxLayoutAlign="center">
    <div class="frm_main" fxFlex="grow">
        <div class="logbookTbl">
            <div class="logbookOnlyTbl">
                <table class="table">
                    <thead>
                        <tr>
                            <th rowspan="4">STT</th>
                            <th rowspan="4">Cơ quan/Đơn vị</th>
                            <th colspan="3">Tổng số hồ sơ đã tiếp nhận trong tháng</th>
                            <th colspan="10">Số hồ sơ đã giải quyết trong tháng</th>
                            <th colspan="3">Số hồ sơ còn tồn chưa giải quyết</th>
                            <th colspan="6">Lũy kế hồ sơ đã giải quyết từ đầu năm</th>
                        </tr>
                        <tr>
                            <th rowspan="3">Tổng số</th>
                            <th colspan="2">Trong đó</th>
                            <th rowspan="3">Tổng số</th>
                            <th colspan="9">Trong đó</th>
                            <th rowspan="3">Tổng số</th>
                            <th colspan="2">Trong đó</th>
                            <th rowspan="3">Tổng số</th>
                            <th colspan="5">Trong đó</th>
                        </tr>
                        <tr>
                            <th rowspan="2">Số hồ sơ chưa giải quyết của tháng trước chuyển qua</th>
                            <th rowspan="2">Tổng số hồ sơ tiếp nhận mới trong tháng</th>

                            <th rowspan="2">Giải quyết trước, đúng hạn</th>
                            <th rowspan="2">Giải quyết quá hạn</th>
                            <th colspan="5">Số văn bản xin lỗi</th>
                            <th rowspan="2">Công khai số văn bản xin lỗi lên hệ thông thông tin giải quyết TTHC (iGate)</th>
                            <th rowspan="2">Số văn bản xin lỗi đã niêm yết tại bộ phận 1 cửa</th>

                            <th rowspan="2">Số hồ sơ trước, đúng thời hạn</th>
                            <th rowspan="2">Số hồ sơ quá thời hạn</th>

                            <th rowspan="2">Giải quyết trước, đúng hạn</th>
                            <th rowspan="2">Giải quyết quá hạn</th>
                            <th rowspan="2">Số văn bản xin lỗi</th>
                            <th rowspan="2">Công khai Số văn bản xin lỗi lên hệ thông thông tin giải quyết TTHC</th>
                            <th rowspan="2">Số văn bản xin lỗi đã niêm yết tại bộ phận 1 cửa</th>
                        </tr>
                        <tr>
                            <th>Tổng số</th>
                            <th>Do giải quyết quá hạn</th>
                            <th>Do tiếp nhận thành phần hồ sơ không đủ</th>
                            <th>Do hồ sơ bị mất, thất lạc hoặc hư hỏng</th>
                            <th>Do sai sót trong kết quả giải quyết</th>
                        </tr>
                        <tr>
                            <th>(1)</th>
                            <th>(2)</th>
                            <th>(3)</th>
                            <th>(4)</th>
                            <th>(5)</th>
                            <th>(6)</th>
                            <th>(7)</th>
                            <th>(8)</th>
                            <th>(9)</th>
                            <th>(10)</th>
                            <th>(11)</th>
                            <th>(12)</th>
                            <th>(13)</th>
                            <th>(14)</th>
                            <th>(15)</th>
                            <th>(16)</th>
                            <th>(17)</th>
                            <th>(18)</th>
                            <th>(19)</th>
                            <th>(20)</th>
                            <th>(21)</th>
                            <th>(22)</th>
                            <th>(23)</th>
                            <th>(24)</th>
                        </tr>
                    </thead>
                    <tbody>

                        <ng-container *ngFor="let item of listHoSoCapSo;let i = index ">
                            <tr *ngIf="listHoSoCapSo">
                                <td rowspan="1">{{i+1}}</td>
                                <td style="text-align: left;" rowspan="1"><a (click)="ViewDetail(i,item.show)">{{item.sector}}{{item.coQuan}}</a></td>
                                <td><a (click)="GetDetailDossier(item.id,item.coQuan,item.data[0].TrongKy,1,0,0,0,0,0,0,0,0,0)">{{item.data[0].tongSoHoSo}}</a></td>
                                <td><a (click)="GetDetailDossier(item.id,item.coQuan,item.data[0].TrongKy,0,1,0,0,0,0,0,0,0,0)">{{item.data[0].soHoSoTonKyTruoc}}</a></td>
                                <td><a (click)="GetDetailDossier(item.id,item.coQuan,item.data[0].TrongKy,0,0,1,0,0,0,0,0,0,0)">{{item.data[0].soHoSoTN}}</a></td>
                                <td><a (click)="GetDetailDossier(item.id,item.coQuan,item.data[0].TrongKy,0,0,0,1,0,0,0,0,0,0)">{{item.data[0].soHoSoDXL}}</a></td>
                                <td><a (click)="GetDetailDossier(item.id,item.coQuan,item.data[0].TrongKy,0,0,0,0,1,0,0,0,0,0)">{{item.data[0].soHoSoDXLTrongHan}}</a></td>
                                <td><a (click)="GetDetailDossier(item.id,item.coQuan,item.data[0].TrongKy,0,0,0,0,0,1,0,0,0,0)">{{item.data[0].soHoSoDXLQuaHan}}</a></td>
                                <td>{{item.data[0].tongSoVBXL}}</td>
                                <td>{{item.data[0].vanban_QUAHAN}}</td>
                                <td>{{item.data[0].vanban_THIEU_TPHS}}</td>
                                <td>{{item.data[0].vanban_MAT_HS}}</td>
                                <td>{{item.data[0].vanban_SAISOT}}</td>
                                <td>{{item.data[0].tongSoVBXL}}</td>
                                <td>{{item.data[0].tongSoVBXL}}</td>
                                <td><a (click)="GetDetailDossier(item.id,item.coQuan,item.data[0].TrongKy,0,0,0,0,0,0,1,0,0,0)">{{item.data[0].soHoSoTON}}</a></td>
                                <td><a (click)="GetDetailDossier(item.id,item.coQuan,item.data[0].TrongKy,0,0,0,0,0,0,0,1,0,0)">{{item.data[0].soHoSoTONCONHAN}}</a></td>
                                <td><a (click)="GetDetailDossier(item.id,item.coQuan,item.data[0].TrongKy,0,0,0,0,0,0,0,0,1,0)">{{item.data[0].soHoSoTONQUAHAN}}</a></td>
                                <td><a (click)="GetDetailDossier(item.id,item.coQuan,2,0,0,0,1,0,0,0,0,0,0)">{{item.data[0].soHoSoDXL_LK}}</a></td>
                                <td><a (click)="GetDetailDossier(item.id,item.coQuan,2,0,0,0,0,1,0,0,0,0,0)">{{item.data[0].soHoSoDXLTrongHan_LK}}</a></td>
                                <td><a (click)="GetDetailDossier(item.id,item.coQuan,2,0,0,0,0,0,1,0,0,0,0)">{{item.data[0].soHoSoDXLQuaHan_LK}}</a></td>
                                <td>{{item.data[0].tongSoVBXL_LK}}</td>
                                <td>{{item.data[0].tongSoVBXL_LK}}</td>
                                <td>{{item.data[0].tongSoVBXL_LK}}</td>
                            </tr>
                            <ng-container *ngFor="let itemSec of item.data[0].data;let ix = index ">
                                <tr *ngIf="item.show">
                                    <td>{{colToLetter(ix)}}</td>
                                    <td style="text-align: left;"><a (click)="ViewDetailPro(i,ix,itemSec.showPro)">{{itemSec.sector}}</a></td>
                                    <td>{{itemSec.tongSoHoSo}}</td>
                                    <td>{{itemSec.soHoSoTonKyTruoc}}</td>
                                    <td>{{itemSec.soHoSoTN}}</td>
                                    <td>{{itemSec.soHoSoDXL}}</td>
                                    <td>{{itemSec.soHoSoDXLTrongHan}}</td>
                                    <td>{{itemSec.soHoSoDXLQuaHan}}</td>
                                    <td>{{itemSec.tongSoVBXL}}</td>
                                    <td>{{itemSec.vanban_QUAHAN}}</td>
                                    <td>{{itemSec.vanban_THIEU_TPHS}}</td>
                                    <td>{{itemSec.vanban_MAT_HS}}</td>
                                    <td>{{itemSec.vanban_SAISOT}}</td>
                                    <td>{{itemSec.tongSoVBXL}}</td>
                                    <td>{{itemSec.tongSoVBXL}}</td>
                                    <td>{{itemSec.soHoSoTON}}</td>
                                    <td>{{itemSec.soHoSoTONCONHAN}}</td>
                                    <td>{{itemSec.soHoSoTONQUAHAN}}</td>
                                    <td>{{itemSec.soHoSoDXL_LK}}</td>
                                    <td>{{itemSec.soHoSoDXLTrongHan_LK}}</td>
                                    <td>{{itemSec.soHoSoDXLQuaHan_LK}}</td>
                                    <td>{{itemSec.tongSoVBXL_LK}}</td>
                                    <td>{{itemSec.tongSoVBXL_LK}}</td>
                                    <td>{{itemSec.tongSoVBXL_LK}}</td>
                                </tr>
                                <ng-container *ngFor="let itemPro of itemSec.dataPro;let j = index ">
                                    <tr *ngIf="itemSec.showPro">
                                        <td>{{colToLetter(ix)}}.{{colToLetter(j)}}</td>
                                        <td style="text-align: left;">{{itemPro.procedureName}}</td>
                                        <td>{{itemPro.tongSoHoSo}}</td>
                                        <td>{{itemPro.soHoSoTonKyTruoc}}</td>
                                        <td>{{itemPro.soHoSoTN}}</td>
                                        <td>{{itemPro.soHoSoDXL}}</td>
                                        <td>{{itemPro.soHoSoDXLTrongHan}}</td>
                                        <td>{{itemPro.soHoSoDXLQuaHan}}</td>
                                        <td>{{itemPro.tongSoVBXL}}</td>
                                        <td>{{itemPro.vanban_QUAHAN}}</td>
                                        <td>{{itemPro.vanban_THIEU_TPHS}}</td>
                                        <td>{{itemPro.vanban_MAT_HS}}</td>
                                        <td>{{itemPro.vanban_SAISOT}}</td>
                                        <td>{{itemPro.tongSoVBXL}}</td>
                                        <td>{{itemPro.tongSoVBXL}}</td>
                                        <td>{{itemPro.soHoSoTON}}</td>
                                        <td>{{itemPro.soHoSoTONCONHAN}}</td>
                                        <td>{{itemPro.soHoSoTONQUAHAN}}</td>
                                        <td>{{itemPro.soHoSoDXL_LK}}</td>
                                        <td>{{itemPro.soHoSoDXLTrongHan_LK}}</td>
                                        <td>{{itemPro.soHoSoDXLQuaHan_LK}}</td>
                                        <td>{{itemPro.tongSoVBXL_LK}}</td>
                                        <td>{{itemPro.tongSoVBXL_LK}}</td>
                                        <td>{{itemPro.tongSoVBXL_LK}}</td>
                                    </tr>
                                </ng-container>
                            </ng-container>
                        </ng-container>
                        <tr *ngIf="TongCapSo" class="sum">
                            <td colspan="2"> TỔNG </td>
                            <td> {{TongCapSo.tongSoHoSo}}</td>
                            <td> {{TongCapSo.soHoSoTonKyTruoc}}</td>
                            <td> {{TongCapSo.soHoSoTN}}</td>
                            <td> {{TongCapSo.soHoSoDXL}}</td>
                            <td> {{TongCapSo.soHoSoDXLTrongHan}}</td>
                            <td> {{TongCapSo.soHoSoDXLQuaHan}}</td>
                            <td> {{TongCapSo.tongSoVBXL}}</td>
                            <td> {{TongCapSo.vanban_QUAHAN}}</td>
                            <td> {{TongCapSo.vanban_THIEU_TPHS}}</td>
                            <td> {{TongCapSo.vanban_MAT_HS}}</td>
                            <td> {{TongCapSo.vanban_SAISOT}}</td>
                            <td> {{TongCapSo.tongSoVBXL}}</td>
                            <td> {{TongCapSo.tongSoVBXL}}</td>
                            <td> {{TongCapSo.soHoSoTON}}</td>
                            <td> {{TongCapSo.soHoSoTONCONHAN}}</td>
                            <td> {{TongCapSo.soHoSoTONQUAHAN}}</td>
                            <td> {{TongCapSo.soHoSoDXL_LK}}</td>
                            <td> {{TongCapSo.soHoSoDXLTrongHan_LK}}</td>
                            <td> {{TongCapSo.soHoSoDXLQuaHan_LK}}</td>
                            <td> {{TongCapSo.tongSoVBXL_LK}}</td>
                            <td> {{TongCapSo.tongSoVBXL_LK}}</td>
                            <td> {{TongCapSo.tongSoVBXL_LK}}</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>