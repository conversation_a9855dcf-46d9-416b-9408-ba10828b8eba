import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { ApiProviderService } from 'src/app/core/service/api-provider.service';
import { Observable } from 'rxjs';
import {SnackbarService} from "data/service/snackbar/snackbar.service";
import {EnvService} from "core/service/env.service";
import { saveAs } from 'file-saver';
import { DeploymentService } from 'src/app/data/service/deployment.service';
import { Workbook } from 'exceljs';
import * as fs from 'file-saver';
const EXCEL_TYPE = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8';
const EXCEL_EXTENSION = '.xlsx'; 
@Injectable({
  providedIn: 'root'
})
export class LateService {
  config = this.envService.getConfig();

  padmanReport  = this.deploymentService.getAppDeployment()?.padmanReport || false;
  // IGATESUPP-99544
  showApologyLetterStatus = this.deploymentService.getAppDeployment()?.showApologyLetterStatus || false;
  constructor(
    private http: HttpClient,
    private apiProviderService: ApiProviderService,
    private snackbar: SnackbarService,
    private envService: EnvService,
    private deploymentService: DeploymentService,
  ) { }

  private procedurePath = this.apiProviderService.getUrl('digo', 'basepad') + '/procedure/';
  private agencyPath = this.apiProviderService.getUrl('digo', 'basedata') + '/agency/';
  private sectorPath = this.apiProviderService.getUrl('digo', 'basepad') + '/sector/';
  private tagPath = this.apiProviderService.getUrl('digo', 'basecat') + '/tag/';
  private procostPath = this.apiProviderService.getUrl('digo', 'basepad') + '/procost/';
  private procostTypePath = this.apiProviderService.getUrl('digo', 'basepad') + '/procost-type/';
  private procedureFormPath = this.apiProviderService.getUrl('digo', 'basepad') + '/procedure-form/';
  private formPath = this.apiProviderService.getUrl('digo', 'basepad') + '/form/';
  private filePath = this.apiProviderService.getUrl('digo', 'fileman') + '/file/';
  private processPath = this.apiProviderService.getUrl('digo', 'basepad') + '/procedure-process-definition/';
  private modelingPath = this.apiProviderService.getUrl('digo', 'modeling') + '/v1/models/';
  private actModelingContentAPI = this.apiProviderService.getUrl('digo', 'bpm') + '/process-definition/models/';
  // private actModelingContentAPI = 'http://localhost:8080/process-definition/models/';
  private bpmProcessPath = this.apiProviderService.getUrl('digo', 'bpm') + '/process-definition/';

  private procedureConfigPath = this.apiProviderService.getUrl('digo', 'basepad') + '/procedure-config/';
  private agencyConfigPath = this.apiProviderService.getUrl('digo', 'basepad') + '/agency-config/';
  private defaultConfigPath = this.apiProviderService.getUrl('digo', 'basepad') + '/default-config/';
  private basecatPath = this.apiProviderService.getUrl('digo', 'basecat');
  private basepadPath = this.apiProviderService.getUrl('digo', 'basepad');
  private padmanPath = this.apiProviderService.getUrl('digo', 'padman');
  private padmanURLReport = this.apiProviderService.getUrl('digo', 'padmanReport');
  private padmanURL = this.apiProviderService.getUrl('digo', 'padman'); 
  private statisticPath = this.apiProviderService.getUrl('digo', 'statistics');

// new request
  getListAgencyType(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.basecatPath + '/tag/--by-category-id?category-id=5f3a491c4e1bd312a6f00005' + searchString, { headers });
  }
  getListDossierLate(searchString): Observable<any> {
    const URL = (!this.padmanReport ? this.padmanPath : this.padmanURLReport) + (this.showApologyLetterStatus ? '/dossier/late-hgi' : '/dossier/late') + searchString; //IGATESUPP-99544
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get( URL, { headers });
  }
  getListDossierLateHcm(searchString): Observable<any> {
    const URL = (!this.padmanReport ? this.padmanPath : this.padmanURLReport)+ '/dossier-statistic/late-hcm' + searchString;
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get( URL, { headers });
  }
  getListDossierLateHcmDay(searchString): Observable<any> {
    const URL = this.statisticPath + '/dossier-late/late-hcm-day' + searchString;
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get( URL, { headers });
  }
  getListDossierTime(searchString): Observable<any> {
    const URL = this.padmanPath + '/dossier/time' + searchString;
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get( URL, { headers });
  }

  getListDossierLateAddRequireTrue(searchString): Observable<any> {
    const URL = this.padmanPath + '/dossier/late-add-require-true' + searchString;
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get( URL, { headers });
  }

  getListDossierLateAddRequireTrueV2(searchString): Observable<any> {
    const URL = this.statisticPath  + '/dossier-late/late-add-require-true' + searchString;
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get( URL, { headers });
  }

  exportListDossierLateAddRequireTrueV2(searchString) {
    this.getExportListDossierLateAddRequireTrueV2(searchString).subscribe((response: Blob) => {
      saveAs(response);
    });
  }

  exportListDossierLateHcmDay(params: any) {
    return new Promise((resolve) => {
      this.http.get(this.statisticPath + '/dossier-late/--export-excel-statictis-dossier-late-hcm-day' + params, {
        observe: 'response',
        responseType: 'blob'
      }).toPromise().then(res => {
        const blob = new Blob([res.body], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'});
        let filename = 'thong_ke_ho_so_tre_han_hcm.xlsx';
        if (res.headers.get('content-disposition') != null) {
          filename = res.headers.get('content-disposition').split(';')[1].split('=')[1].replace(/\"/g, '');
        }
        const blobUrl = URL.createObjectURL(blob);

        const xhr = new XMLHttpRequest();
        xhr.responseType = 'blob';

        xhr.onload = () => {
          const recoveredBlob = xhr.response;
          const reader = new FileReader();
          reader.onload = () => {
            const base64data = reader.result.toString();
            const object = JSON.stringify({
              mimeType: filename.substring(filename.lastIndexOf('.') + 1),
              name: filename.substring(0, filename.lastIndexOf('.')),
              data: base64data.split(',')[1]
            });
            const anchor = document.createElement('a');
            anchor.download = filename;
            anchor.href = base64data;
            anchor.click();
          };
          reader.readAsDataURL(recoveredBlob);
        };

        xhr.open('GET', blobUrl);
        xhr.send();
        resolve(true);
      }).catch(err => {
        if (err.status === 500) {
          const message = {
            vi: 'Hệ thống tạm thời không thể xuất file excel!',
            en: 'The system is temporarily unable to export the excel file!'
          };
          this.snackbar.openSnackBar(0, '', message[localStorage.getItem('language')], 'error_notification', this.config.expiredTime);
        }
        resolve(false);
      });
    });
  }
  
  getExportListDossierLateAddRequireTrueV2(searchParams): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.set('Content-type', 'application/json');
    return this.http.get(this.statisticPath + '/dossier-late/--export-excel-statictis-dossier-late' + searchParams , {headers, responseType: 'blob' }).pipe();
  }

  getListDossierLateAll(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get((!this.padmanReport ? this.padmanPath : this.padmanURLReport) + '/dossier/late-all' + searchString, { headers });
  }
  getListDossierLateAllAgg(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get( this.padmanPath + '/dossier/late-all-agg' + searchString, { headers });
  }
  exportListDossierLateHCM(searchString){
    this.getListDossierLateHCMAll(searchString).subscribe((res: Blob) => {
      saveAs(res);
    })
  }
  getListDossierLateHCMAll(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get((!this.padmanReport ? this.padmanPath : this.padmanURLReport) + '/dossier-statistic/export-late-hcm' + searchString, {headers, responseType: 'blob' }).pipe();
  }
  getListDossierTimeAll(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get( this.padmanPath + '/dossier/time-all' + searchString, { headers });
  }
  getListProcedureConfig(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.procedureConfigPath + searchString, { headers });
  }

  getListAgencyConfig(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.agencyConfigPath + searchString, { headers });
  }
  getListAllDefaultConfig(): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.defaultConfigPath + '--all', { headers });
  }
  getFile(id): Observable<any>{
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/pdf');
    return this.http.get(this.filePath + id, { responseType: 'blob' as 'json' });
  }

  getListPattern(searchString): Observable<any>{
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.basecatPath + '/pattern/' + searchString, { headers });
  }

  getDetailPattern(id): Observable<any>{
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.basecatPath + '/pattern/' + id, { headers });
  }

  getDetailAgency(id): Observable<any>{
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.agencyPath + id, { headers });
  }

  getListProcedure(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.procedurePath + searchString, { headers });
  }
  getListAgency(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.agencyPath + 'name/' + searchString, { headers });
  }
  getListSector(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.sectorPath + searchString, { headers }).pipe();
  }
  getProcedureDetailVn(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', 'vn');
    return this.http.get(this.procedurePath + id, { headers });
  }
  getProcedureDetailEn(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', 'en');
    return this.http.get(this.procedurePath + id, { headers });
  }
  postProcedureConfig(requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.post<any>(this.procedureConfigPath, requestBody, { headers });
  }
  postAgencyConfig(requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.post<any>(this.agencyConfigPath, requestBody, { headers });
  }
  postDefaultConfig(requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.post<any>(this.defaultConfigPath, requestBody, { headers });
  }
  deleteProcedureConfig(id: string) {
    let headers = new HttpHeaders();
    headers = headers.append('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.delete(this.procedureConfigPath + id, { headers });
  }
  deleteAgencyConfig(id: string) {
    let headers = new HttpHeaders();
    headers = headers.append('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.delete(this.agencyConfigPath + id, { headers });
  }
  deleteDefaultConfig(id: string) {
    let headers = new HttpHeaders();
    headers = headers.append('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.delete(this.defaultConfigPath + id, { headers });
  }
  putProcedureConfig(id, data): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.put<any>(this.procedureConfigPath + id, data, { headers });
  }

  putAgencyConfig(id, data): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.put<any>(this.agencyConfigPath + id, data, { headers });
  }

  getListAgencyTag(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.agencyPath + 'name+tag' + searchString, { headers });
  }
// old request


  getListFullAgency(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.agencyPath + searchString, { headers });
  }

  getProcedureDetail(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.procedurePath + id, { headers });
  }

  getListTagByCategoryId(id: string, page: number, size: number, sort: string): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    // tslint:disable-next-line: max-line-length
    return this.http.get(this.tagPath + '--by-category-id?category-id=' + id + '&page=' + page + '&size=' + size + '&sort=' + sort, { headers }).pipe();
  }

  postProcedure(requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.post<any>(this.procedurePath, requestBody, { headers });
  }

  putProcedure(id, requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.put<any>(this.procedurePath + id, requestBody, { headers });
  }

  deleteProcedure(id: string) {
    let headers = new HttpHeaders();
    headers = headers.append('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.delete(this.procedurePath + id, { headers });
  }

  getProcedureProcost(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.procostPath + searchString, { headers });
  }

  getListProcostType(keyword: string, page: number, size: number): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    // tslint:disable-next-line: max-line-length
    return this.http.get(this.procostTypePath + '?keyword=' + keyword + '&page=' + page + '&size=' + size, { headers }).pipe();
  }

  postProcost(requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.post<any>(this.procostPath, requestBody, { headers });
  }

  getProcostDetail(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.procostPath + id, { headers });
  }

  putProcost(id, requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.put<any>(this.procostPath + id, requestBody, { headers });
  }

  deleteProcost(id: string) {
    let headers = new HttpHeaders();
    headers = headers.append('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.delete(this.procostPath + id, { headers });
  }

  putAssignedAgency(id, requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.put<any>(this.procedurePath + id + '/children', requestBody, { headers });
  }

  getProcedureForm(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.procedureFormPath + searchString, { headers });
  }

  getProcedureFormDetail(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.procedureFormPath + id, { headers });
  }


  getListForm(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.formPath + searchString, { headers });
  }

  postNewForm(requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.post<any>(this.formPath, requestBody, { headers });
  }

  uploadFiles(imgFile): Observable<any> {
    const formData: FormData = new FormData();
    const file: File = imgFile;
    formData.append('file', file, file.name);
    return this.http.post(this.filePath, formData).pipe();
  }

  uploadMultiFile(imgFiles, accountId): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.append('Accept', 'application/json');
    const formData: FormData = new FormData();
    imgFiles.forEach(files => {
      const file: File = files;
      formData.append('files', file, file.name);
    });
    formData.append('account-id', accountId);
    return this.http.post<any>(this.filePath + '--multiple', formData, { headers });
  }


  getFileNameSize(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.filePath + id + '/filename+size', { headers });
  }

  postProcedureForm(requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.post<any>(this.procedureFormPath, requestBody, { headers });
  }

  putProcedureForm(id, requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.put<any>(this.procedureFormPath + id, requestBody, { headers });
  }

  downloadFile(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.filePath + id, { responseType: 'blob' as 'json' }).pipe();
  }

  deleteFile(id) {
    return this.http.delete<any>(this.filePath + id).pipe();
  }

  deleteForm(id: string) {
    let headers = new HttpHeaders();
    headers = headers.append('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.delete(this.procedureFormPath + id, { headers });
  }

  getListProcedureProcess(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.processPath + searchString, { headers });
  }

  getModelDeploy(modelId) {
    const headers = new HttpHeaders().set('Accept', 'application/json');
    return this.http.get(this.actModelingContentAPI + modelId + '/content', { headers, responseType: 'blob' });
  }

  getUrlModel(modelId) {
    return this.actModelingContentAPI + modelId + '/content';
  }

  deleteProcess(id: string) {
    let headers = new HttpHeaders();
    headers = headers.append('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.delete(this.processPath + id, { headers });
  }

  getProcedureProcessDetail(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.processPath + id, { headers });
  }

  putProcedureProcessDetail(id, requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.put<any>(this.processPath + id, requestBody, { headers });
  }

  getListBPMProcess(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.bpmProcessPath + searchString, { headers });
  }
  exportToExcelDossierLate(params: any) {
    return new Promise((resolve) => {
      this.http.get((!this.padmanReport ? this.padmanPath : this.padmanURLReport) + (this.showApologyLetterStatus ? '/dossier-statistic/overdue/--export-hgi' : '/dossier-statistic/overdue/--export') + params, {
        observe: 'response',
        responseType: 'blob'
      }).toPromise().then(res => {
        const blob = new Blob([res.body], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'});
        let filename = 'bao_cao_ho_so_tre_han.xlsx';
        if (res.headers.get('content-disposition') != null) {
          filename = res.headers.get('content-disposition').split(';')[1].split('=')[1].replace(/\"/g, '');
        }
        const blobUrl = URL.createObjectURL(blob);

        const xhr = new XMLHttpRequest();
        xhr.responseType = 'blob';

        xhr.onload = () => {
          const recoveredBlob = xhr.response;
          const reader = new FileReader();
          reader.onload = () => {
            const base64data = reader.result.toString();
            const object = JSON.stringify({
              mimeType: filename.substring(filename.lastIndexOf('.') + 1),
              name: filename.substring(0, filename.lastIndexOf('.')),
              data: base64data.split(',')[1]
            });
            const anchor = document.createElement('a');
            anchor.download = filename;
            anchor.href = base64data;
            anchor.click();
          };
          reader.readAsDataURL(recoveredBlob);
        };

        xhr.open('GET', blobUrl);
        xhr.send();
        resolve(true);
      }).catch(err => {
        if (err.status === 500) {
          const message = {
            vi: 'Hệ thống tạm thời không thể xuất file excel!',
            en: 'The system is temporarily unable to export the excel file!'
          };
          this.snackbar.openSnackBar(0, '', message[localStorage.getItem('language')], 'error_notification', this.config.expiredTime);
        }
        resolve(false);
      });
    });

  }

  getListDossierLateQNM(searchString): Observable<any> {
    const URL = this.padmanPath + '/dossier/late-qnm' + searchString;
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get( URL, { headers });
  }

  exportDossier_ReportDVCLT(params: string): any {
    return new Promise((resolve) => {
        this.http.get(this.padmanURL + '/dvclt-dossier/export-excel-hcm' + params, {
            observe: 'response',
            responseType: 'blob'
        }).toPromise().then(res => {
            const blob = new Blob([res.body], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'});
            let filename = 'danh_sach_ho_so.xlsx';
            if (res.headers.get('content-disposition') != null) {
                filename = res.headers.get('content-disposition').split(';')[1].split('=')[1].replace(/"/g, '');
            }
            const blobUrl = URL.createObjectURL(blob);
            const xhr = new XMLHttpRequest();
            xhr.responseType = 'blob';

            xhr.onload = () => {
                const recoveredBlob = xhr.response;
                const reader = new FileReader();
                reader.onload = () => {
                    const base64data = reader.result.toString();
                    const anchor = document.createElement('a');
                    anchor.download = filename;
                    anchor.href = base64data;
                    anchor.click();
                };
                reader.readAsDataURL(recoveredBlob);
            };

            xhr.open('GET', blobUrl);
            xhr.send();
            resolve(true);
        }).catch(err => {
            if (err.status === 500) {
                const message = {
                    vi: 'Hệ thống tạm thời không thể xuất file excel!',
                    en: 'The system is temporarily unable to export the excel file!'
                };
                this.snackbar.openSnackBar(0, '', message[localStorage.getItem('language')], 'error_notification', this.config.expiredTime);
            }
            resolve(false);
        });
    });
  }
  getListDossierLateQBH(searchString): Observable<any> {
    const URL = this.padmanPath + '/dossier-statistic-qbh/late-qbh' + searchString;
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get( URL, { headers });
  }
  getListDossierLateQBHSectorProd(searchString): Observable<any> {
    const URL = this.padmanPath + '/dossier-statistic-qbh/late-qbh-sector-prod' + searchString;
    //const URL = 'http://localhost:8081/dossier-statistic-qbh/late-qbh-sector-prod' + searchString;

    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get( URL, { headers });
  }
  exportToExcelDossierLateQBH(params: any) {
    return new Promise((resolve) => {
      this.http.get(this.padmanPath + '/dossier-statistic-qbh/overdue/--export' + params, {
        observe: 'response',
        responseType: 'blob'
      }).toPromise().then(res => {
        const blob = new Blob([res.body], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'});
        let filename = 'bao_cao_ho_so_tre_han.xlsx';
        if (res.headers.get('content-disposition') != null) {
          filename = res.headers.get('content-disposition').split(';')[1].split('=')[1].replace(/\"/g, '');
        }
        const blobUrl = URL.createObjectURL(blob);

        const xhr = new XMLHttpRequest();
        xhr.responseType = 'blob';

        xhr.onload = () => {
          const recoveredBlob = xhr.response;
          const reader = new FileReader();
          reader.onload = () => {
            const base64data = reader.result.toString();
            const object = JSON.stringify({
              mimeType: filename.substring(filename.lastIndexOf('.') + 1),
              name: filename.substring(0, filename.lastIndexOf('.')),
              data: base64data.split(',')[1]
            });
            const anchor = document.createElement('a');
            anchor.download = filename;
            anchor.href = base64data;
            anchor.click();
          };
          reader.readAsDataURL(recoveredBlob);
        };

        xhr.open('GET', blobUrl);
        xhr.send();
        resolve(true);
      }).catch(err => {
        if (err.status === 500) {
          const message = {
            vi: 'Hệ thống tạm thời không thể xuất file excel!',
            en: 'The system is temporarily unable to export the excel file!'
          };
          this.snackbar.openSnackBar(0, '', message[localStorage.getItem('language')], 'error_notification', this.config.expiredTime);
        }
        resolve(false);
      });
    });

  }
  public exportToExcelStatisticLateQBH(
    subNameReport: string,
    subTitle: string,
    json: any[],
    excelFileName: string,
    sheetName: string
  ) {
    const data = json;
    const workbook = new Workbook();
    workbook.creator = 'Snippet Coder';
    workbook.lastModifiedBy = 'SnippetCoder';
    workbook.created = new Date();
    workbook.modified = new Date();
    const worksheet = workbook.addWorksheet(sheetName);

    worksheet.getColumn('A').font = {name: 'Times New Roman', size: 12};
    worksheet.getColumn('B').font = {name: 'Times New Roman', size: 12};
    worksheet.getColumn('C').font = {name: 'Times New Roman', size: 12};
    worksheet.getColumn('D').font = {name: 'Times New Roman', size: 12};

    // Thêm header
    worksheet.addRow([]);
    worksheet.mergeCells('A1:D1');
    worksheet.getCell('A1').value = "UBND Tỉnh Quảng Bình";
    worksheet.getCell('A1').alignment = {horizontal: 'center', vertical: 'middle'};

    worksheet.mergeCells('A2:D2');
    worksheet.getCell('A2').value = subNameReport;
    worksheet.getCell('A2').alignment = {horizontal: 'center', vertical: 'middle'};
    worksheet.getCell('A2').font = {size: 12, bold: true, name: 'Times New Roman'};

    worksheet.mergeCells('A3:D3');
    worksheet.getCell('A3').value = subTitle;
    worksheet.getCell('A3').alignment = {horizontal: 'center', vertical: 'middle'};
    worksheet.getCell('A3').font = {size: 12, bold: true, name: 'Times New Roman'};
    worksheet.getCell('A6').value = 'STT';
    worksheet.getCell('B6').value = 'Lĩnh vực/Thủ tục';
    worksheet.getCell('C6').value = 'Số hồ sơ trễ hạn';
    worksheet.getCell('D6').value = 'Cơ quan';
    
    // Thêm dữ liệu từ json array
    let stt = 1;
    data.forEach(item => {
        worksheet.addRow([
            stt,
            item.sector?.name, // Tên thủ tục hoặc lĩnh vực
            item.sohs, // Số hồ sơ trễ hạn
            item.agency?.name // Tên cơ quan
        ]);
        stt++;
    });

    // Định dạng các cột
    worksheet.getColumn('A').width = 10;
    worksheet.getColumn('B').width = 40;
    worksheet.getColumn('C').width = 20;
    worksheet.getColumn('D').width = 30;

    // Căn giữa các cột số liệu
    worksheet.getColumn('A').alignment = { horizontal: 'center' };
    worksheet.getColumn('C').alignment = { horizontal: 'center' };

    // Tạo border cho toàn bộ bảng
    const lastRow = worksheet.rowCount;
    worksheet.eachRow((row, rowNumber) => {
        if (rowNumber >= 6) { // Bắt đầu từ hàng dữ liệu
            row.eachCell((cell) => {
                cell.border = {
                    top: {style: 'thin'},
                    left: {style: 'thin'},
                    bottom: {style: 'thin'},
                    right: {style: 'thin'}
                };
            });
        }
    });

    // Xuất file Excel
    workbook.xlsx.writeBuffer().then((dataBuffer: ArrayBuffer) => {
        const blob = new Blob([dataBuffer], {type: EXCEL_TYPE});
        fs.saveAs(blob, excelFileName + EXCEL_EXTENSION);
    });
  }
  exportToExcelStatisticLateGeneralDetail(params: string): any {
    return new Promise((resolve) => {
      this.http.get(this.padmanPath + '/dossier-statistic-qbh/--detail-late/--export' + params, {
      //this.http.get('http://localhost:8081/dossier-statistic-qbh/--detail-late/--export' + params, {
        observe: 'response',
        responseType: 'blob'
      }).toPromise().then(res => {
        const blob = new Blob([res.body], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'});
        let filename = 'bao_cao_chi_tiet.xlsx';
        if (res.headers.get('content-disposition') != null) {
          filename = res.headers.get('content-disposition').split(';')[1].split('=')[1].replace(/"/g, '');
        }
        const blobUrl = URL.createObjectURL(blob);
        const xhr = new XMLHttpRequest();
        xhr.responseType = 'blob';

        xhr.onload = () => {
          const recoveredBlob = xhr.response;
          const reader = new FileReader();
          reader.onload = () => {
            const base64data = reader.result.toString();
            const anchor = document.createElement('a');
            anchor.download = filename;
            anchor.href = base64data;
            anchor.click();
          };
          reader.readAsDataURL(recoveredBlob);
        };

        xhr.open('GET', blobUrl);
        xhr.send();
        resolve(true);
      }).catch(err => {
        if (err.status === 500) {
          const message = {
            vi: 'Hệ thống tạm thời không thể xuất file excel!',
            en: 'The system is temporarily unable to export the excel file!'
          };
          this.snackbar.openSnackBar(0, '', message[localStorage.getItem('language')], 'error_notification', this.config.expiredTime);
        }
        resolve(false);
      });
    });
  }
}
