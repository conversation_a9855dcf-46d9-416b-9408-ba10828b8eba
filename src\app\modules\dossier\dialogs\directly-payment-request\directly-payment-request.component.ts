import { Component, OnInit, Inject, ViewChild } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { KeycloakService } from 'keycloak-angular';
import { DossierService } from 'src/app/data/service/dossier/dossier.service';
import { UserService } from 'src/app/data/service/user.service';
import * as ClassicEditor from '@ckeditor/ckeditor5-build-classic/build/ckeditor';
import '@ckeditor/ckeditor5-build-classic/build/translations/vi';
import { EnvService } from 'src/app/core/service/env.service';
import { SnackbarService } from 'src/app/data/service/snackbar/snackbar.service';
import { DatePipe } from '@angular/common';
import { ProcedureService } from 'src/app/data/service/procedure/procedure.service';
import { FormControl, FormGroup } from '@angular/forms';
import { NgxMatDateAdapter, NGX_MAT_DATE_FORMATS } from '@angular-material-components/datetime-picker';
import { PICK_FORMATS } from 'src/app/data/service/config.service';
import { CustomDatetimeAdapter } from 'src/app/shared/ts/custom-datetime-adapter';
import { DeploymentService } from 'src/app/data/service/deployment.service';
import * as tUtils from 'src/app/data/service/thoai.service';
import { NotificationService } from 'src/app/data/service/notification.service';
import { ProcessService } from 'src/app/data/service/process/process.service';
import { HomeService } from 'src/app/data/service/home/<USER>';
import { take } from 'rxjs/operators';
import {NotifyQNIService} from "shared/components/check-send-notify-qni/check-send-notify-qni.service";
import { PadmanService } from 'src/app/data/service/svc-padman/padman.service';
import { AgencyService } from 'src/app/data/service/basedata/agency.service';

@Component({
  selector: 'app-directly-payment-request',
  templateUrl: './directly-payment-request.component.html',
  styleUrls: ['./directly-payment-request.component.scss']
})
export class DirectlyPaymentRequestComponent implements OnInit {

  config = this.envService.getConfig();
  env: any = this.deploymentService.getAppDeployment()?.env;
  selectedLang = localStorage.getItem('language') || 'vi';
  selectedLangId = Number(localStorage.getItem('languageId')) || 228;
  dossierId: string;
  commentContent = '';
  descriptionContent = '';
  userName: string;
  accountId: string;
  dossierCode: string;
  isCKMaxlenght = false;
  dossierDetail: any;
  oldstatus = '';
  officers = [];
  agencyId: any;
  getApprovalAgency = '';
  currentTask: any;
  commentContentPlainText = '';

  public Editor = ClassicEditor;
  commentConfig = {
    placeholder: 'Nhập lý do...',
    language: 'vi',
    removePlugins: ['MediaEmbed', 'ImageUpload'],
    toolbarLocation: 'bottom'
  };
  descriptionConfig = {
    placeholder: 'Nhập nội dung...',
    language: 'vi',
    removePlugins: ['MediaEmbed', 'ImageUpload'],
    toolbarLocation: 'bottom'
  };
  dossierTaskStatus = { id: '', name: [] };
  dossierMenuTaskRemind = { id: '', name: [] };

  // ================================================= Upload file
  uploaded: boolean;
  blankVal: any;
  uploadedImage = null;
  files = [];
  urls = [];
  fileNames = [];
  fileNamesFull = [];
  listProcedureProcess = [];

  isFieldArrayInvalid = false;
  acceptFileExtension = this.config.acceptFileExtension;
  acceptFileType = this.config.acceptFileType;
  maxFileSize = this.env?.maxFileSize || this.config.maxFileSize;
  //Thu tuc co cau hinh dong bo BXD-LGSP Tan Tan
  isSyncDBNLGSPTanDanBXD = 0;
  statusName = "";
  fileTemplate = "";
  typeProcess = 1;

  updateForm = new FormGroup({
    numberPausePaidDay: new FormControl(1),
  });
  checkConfirmPayment = false;
  showConfirmPayment = false;
  disableConfirmPayment = false;
  showCheckConfirmPayment = this.deploymentService.env.OS_HCM.showCheckConfirmPayment;
  totalCost = '';

  directlyPayment = this.deploymentService.env?.OS_HCM?.directlyPayment ? 
    this.deploymentService.env?.OS_HCM?.directlyPayment : 
    {
      sentDirectlyPaymentNotificationTaskStatus: "6412aa8ef4a42852018da4fa",
      sentDirectlyPaymentNotificationTagId: "63fb7a261aee0a3ee93c8dac",
    } 
  sentDirectlyPaymentNotification = { id: '', name: []};
  directlyPaymentDue = "";
  receiveType = 2;
  isSmsQNM = this.env?.OS_QNM?.isSmsQNM ? true : false;
  updateRequireFieldForPayReDossier = this.deploymentService.getAppDeployment()?.updateRequireFieldForPayReDossier == 1 ? true : false;

  constructor(
    private userService: UserService,
    private envService: EnvService,
    private keycloakService: KeycloakService,
    public dialogRef: MatDialogRef<DirectlyPaymentRequestComponent>,
    @Inject(MAT_DIALOG_DATA) public data: ConfirmDirectlyPaymentRequestDialogModel,
    private snackbarService: SnackbarService,
    private datePipe: DatePipe,
    private notiService: NotificationService,
    private deploymentService: DeploymentService,
    private procedureService: ProcedureService,
    private dossierService: DossierService,
    private processService: ProcessService,
    private homeService : HomeService,
    private padmanService: PadmanService,
    private agencyService: AgencyService
  ) {
    this.dossierId = data.dossierId;
    this.dossierCode = data.dossierCode;
    this.typeProcess = data.typeProcess;
    if (this.typeProcess === 2){
      this.env.enableApprovalOfLeadership = 2;
    }
  }

  ngOnInit(): void {
    this.getUserAccount();
    this.getDetailDossier();
    this.getDossierTaskStatus();
    this.getDossierMenuTaskRemind();
    this.setEnvVariable();
  }

  setEnvVariable(){
    // tslint:disable-next-line:max-line-length
    this.fileTemplate = !!this.env?.fileTemplate?.requireAdditional ? this.env?.fileTemplate?.requireAdditional : this.config.requireAdditionalTemplateFile;
  }

  getUserAccount() {
    this.keycloakService.loadUserProfile().then(user => {
      // tslint:disable-next-line: no-string-literal
      this.accountId = user['attributes'].user_id[0];
      this.userService.getUserInfo(this.accountId).subscribe(data => {
        this.userName = data.fullname;
      });
    });
  }

  getDossierTaskStatus() {
    let tagId = this.directlyPayment.sentDirectlyPaymentNotificationTaskStatus;
    this.dossierService.getAgencyTag(tagId).subscribe(rs => {
      this.dossierTaskStatus.id = rs.id;
      this.dossierTaskStatus.name = rs.trans;
    }, err => {
      console.log(err);
    });
  }
  getDossierMenuTaskRemind() {
    let tagId = this.directlyPayment.sentDirectlyPaymentNotificationTagId;
    this.dossierService.getAgencyTag(tagId).subscribe(rs => {
      this.dossierMenuTaskRemind.id = rs.id;
      this.dossierMenuTaskRemind.name = rs.trans;
      this.statusName = rs?.trans.filter(t => t.languageId === Number(this.selectedLangId))?.name;
    }, err => {
      console.log(err);
    });
  }

  async getDetailDossier() {
    await this.getDossierFeeNotify();
    this.dossierService.getDossierDetail(this.dossierId).subscribe(data => {
      this.dossierDetail = data;
      this.getProcedureDetail(data?.procedure?.id);
      this.agencyId = !!data?.agency?.id ? data?.agency?.id : data?.agency?.parent?.id;
      if (!!data.currentTask && data.currentTask.length > 0){
        this.currentTask = data.currentTask[0];
      }
      this.notiService.checkSendSubject.next(
        {
          id: data?.procedureProcessDefinition?.id,
          phone: data?.applicant?.data?.phoneNumber,
          email: data?.applicant?.data?.email,
          currentTask: this.currentTask,
          contentParams: {
            parameters: {
              sector: !!data?.procedure?.sector?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name ? data?.procedure?.sector?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name : '',
              procedure: !!data?.procedure?.translate?.find(agency => agency.languageId === Number(this.selectedLangId))?.name ? data?.procedure?.translate?.find(agency => agency.languageId === Number(this.selectedLangId))?.name : '',
              agency: !!data?.agency?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name ? data?.agency?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name : '',
              agencyId: !!data?.agency?.id ? data?.agency?.id : data?.agency?.parent?.id,
              extend: {},
              applicantFullname: data?.applicant?.data?.fullname,
              officerFullname: '',
              subsystem: !!this.env?.subsystem?.name ? this.env?.subsystem?.name : "Hệ thống Một cửa điện tử",
              dossierCode: data?.nationCode ? data?.nationCode : data?.code,
              nextTask: !!this.env?.notify?.additionalRequirement?.nextTask ? this.env?.notify?.additionalRequirement?.nextTask : 'Yêu cầu bổ sung hồ sơ',
              // IGATESUPP-63184
              nextStatus: !!this.env?.notify?.directlyPaymentNotification?.nextStatus ? this.env?.notify?.directlyPaymentNotification?.nextStatus : (this.dossierDetail?.dossierTaskStatus?.name ?? ''), // cho bo sung, trang thai hien tai cua ho so
              dossierStatus: !!this.env?.notify?.directlyPaymentNotification?.dossierStatus ? this.env?.notify?.directlyPaymentNotification?.dossierStatus : (this.dossierDetail?.dossierStatus?.name?.find(t => t.languageId === Number(this.selectedLangId))?.name ?? ''), // cho bo sung, trang thai hien tai cua ho so
              dossierSearchPadsvcUrl: (!!this.env?.domain?.padsvc ? this.env?.domain?.padsvc : "http://padsvctest.vncitizens.vn/vi/") + "dossier/public",
              dossierDetailPadsvcUrl: (!!this.env?.domain?.padsvc ? this.env?.domain?.padsvc : "http://padsvctest.vncitizens.vn/vi/") + "dossier/detail/" + this.dossierId,
              dossierSearchUrl: (!!this.env?.domain?.onegate ? this.env?.domain?.onegate : "http://padsvctest.vncitizens.vn/vi/") + "dossier/search/",
              dossierDetailUrl: (!!this.env?.domain?.onegate ? this.env?.domain?.onegate : "http://padsvctest.vncitizens.vn/vi/") + "dossier/search/" + this.dossierId,
              dossierPayDetailPadsvcUrl: (!!this.env?.domain?.onegate ? this.env?.domain?.onegate : "http://padsvctest.vncitizens.vn/vi/") + "dossier/payment/" + this.dossierId,
              returnMethod: !!data?.dossierReceivingKind?.name?.find(recieve => recieve.languageId === Number(this.selectedLangId))?.name ? data?.dossierReceivingKind?.name?.find(recieve => recieve.languageId === Number(this.selectedLangId))?.name : '',
              receivingDate: '',
              appointmentDate: '',
              dossierFee: this.totalCost,
              reason: this.commentContentPlainText,
              directlyPaymentDue: this.directlyPaymentDue,
            }
          }
        }
      );
      if (this.dossierDetail.dossierTaskStatus === undefined && this.dossierDetail.dossierTaskStatus === null) {
        // this.putDossierStatus();
      }
      else {
        this.oldstatus = this.dossierDetail.dossierTaskStatus.name;
      }
    });
  }
  
  getDossierFeeNotify() {
    return new Promise<void>(resolve => {
      this.padmanService.getDossierFee(this.dossierId).toPromise().then(res => {
        if(res && res.length > 0)
        {
          let totalCostValue = 0;
          let totalPaidValue = 0;
          res.forEach(element => {
            totalCostValue += element.quantity * element.amount;
            totalPaidValue += element.paid;
          });
          const monetaryUnit = res[0]?.monetaryUnit ? res[0].monetaryUnit : 'VNĐ';
          let total = totalCostValue - totalPaidValue;
          this.totalCost = total >= 0 ? Number((total).toFixed(1)).toLocaleString() + ' ' + monetaryUnit : '';
        }
        resolve();
      }).catch(err => {
        resolve();
      });
    });
  }

  getProcedureDetail(procedureId) {
    this.procedureService.getProcedureDetail(procedureId).subscribe(async data => {
      this.isSyncDBNLGSPTanDanBXD = data?.isSyncDBNLGSPTanDanBXD ? Number(data?.isSyncDBNLGSPTanDanBXD) : 0;
      if(data && !!data.isConfirmPayment) {
        this.checkConfirmPayment = true;
      }
      if(data && !!data.isCheckConfirmPayment) {
        this.showConfirmPayment = true;
      }
    }, err => {
      console.log(err);
    });
  }

  putDossierStatus() {
    const requestBodyObj = [
      {
        id: this.dossierId,
        dossierStatus: 2,
        dossierTaskStatus: null
      }
    ];
    const requestBody = JSON.stringify(requestBodyObj, null, 2);
    this.dossierService.putDossierStatus(requestBody).subscribe(data => {
      if (data.affectedRows === 1) {
        this.getDetailDossier();
      }
    });
  }

  postHistory() {
    let newStatus = '';
    if (this.dossierTaskStatus.name.length > 0) {
      newStatus = this.dossierTaskStatus.name[0].name;
      this.dossierTaskStatus.name.forEach(element => {
        if (element.languageId === Number(localStorage.getItem('languageId'))) {
          newStatus = element.name;
        }
      });
    }
    const content = {
      groupId: 1,
      itemId: this.dossierId,
      user: {
        id: this.accountId,
        name: this.userName
      },
      type: 3,
      deploymentId: this.config.deploymentId,
      action: [
        {
          fieldNameRbk: 'lang.word.status',
          originalValue: this.oldstatus,
          newValue: newStatus
        }
      ]
    };
    this.dossierService.postHistory(JSON.stringify(content, null, 2)).subscribe(data => {

    });
  }

  GetListUserByPermission(agency) {
    return new Promise<void>((resolve) => {
      this.processService.getApprovalAgency(agency.id).pipe(take(1)).subscribe(async data => {
        console.log('data', data);
        if (!!data && data.content.length !== 0) {
          this.getApprovalAgency = data.content[0].approvalAgency.id;
        }
        else{
          if (!!agency.parent.id){
            await this.GetListUserByPermissionParent(agency.parent.id);
          }
        }
        if (this.getApprovalAgency == '') {
          this.getApprovalAgency = agency.id;
        }
        let permission = "additionalRequirementDossierApproval";
        this.processService.getListUserByPermissionCode(this.getApprovalAgency, permission).pipe(take(1)).subscribe(data2 => {
          this.officers = data2;
          resolve();
        }, (err) => {
          resolve();
        });
      });
    });
  }

  GetListUserByPermissionParent(agencyId) {
    return new Promise<void>((resolve) => {
      this.processService.getApprovalAgency(agencyId).pipe(take(1)).subscribe(data => {
        console.log('data', data);
        if (!!data && data.content.length !== 0) {
          this.getApprovalAgency = data.content[0].approvalAgency.id;
        }
        // if (this.getApprovalAgency == '') {
        //   this.getApprovalAgency = agencyId;
        // }
        resolve();
      }, (err) => {
        resolve();
      });
    });
  }


  async onConfirm() {
    this.disableConfirmPayment = true;
    if (this.commentContent.trim() === '') {
      this.disableConfirmPayment = false;
      const msgObj = {
        vi: 'Vui lòng nhập ý kiến xử lý!',
        en: 'Please enter a handling comments!'
      };
      this.snackbarService.openSnackBar(2, '', msgObj[this.selectedLang], 'warning_notification', this.config.expiredTime);
      return;
    }
    const formObj = this.updateForm.getRawValue();
    let check = true;
    if (formObj.numberPausePaidDay === '' && !!!formObj.numberPausePaidDay && this.dossierDetail.dossierStatus.id !== 0){
      this.disableConfirmPayment = false;
      check = false;
      const msgObj = {
        vi: 'Vui lòng nhập số ngày tạm dừng thanh toán!',
        en: 'Please enter the number of pause pay days!'
      };
      this.snackbarService.openSnackBar(2, '', msgObj[this.selectedLang], 'warning_notification', this.config.expiredTime);
      return;
    }
    //Hiện thông báo khi bật tham số và chưa check đồng ý
    if(this.showCheckConfirmPayment && !this.checkConfirmPayment && this.showConfirmPayment)
    {
      this.disableConfirmPayment = false;
      const msgObj = {
        vi: 'Vui lòng đồng ý để xác nhận số tiền yêu cầu thanh toán!',
        en: 'Please agree to confirm the amount requested for payment!'
      };
      this.snackbarService.openSnackBar(2, '', msgObj[this.selectedLang], 'warning_notification', this.config.expiredTime);
      return;
    }

    // update bắt buộc thanh toán cho phí > 0
    if (this.updateRequireFieldForPayReDossier) {
      this.dossierService.updateRequiredFee(this.dossierId).subscribe();
      await this.dossierService.updateRequiredFee(this.dossierId).toPromise();
    }

    if (this.commentContent.trim() !== '' && check) {
      await this.postTimesheetDirectlyPaymentNotification();
      let dosStatus = 18;
      // if (this.dossierDetail.dossierStatus.id !== 0){
      //   dosStatus = 17;
      // }
      let requestBodyObj = {
        // dossierStatus: this.env?.enableApprovalOfLeadership == 1 ? 8 : 1,
        dossierStatus: dosStatus,
        comment: '',
        description: '',
        dossierTaskStatus: this.dossierTaskStatus,
        dossierMenuTaskRemind: this.dossierMenuTaskRemind,
        directlyPaymentNotification: {
          numberWaitingDirectlyPaymentDay: formObj.numberPausePaidDay,
          directlyPaymentDue: this.directlyPaymentDue,
          sentNotificationBefore: this.dossierDetail?.extendHCM?.sentNotificationBefore ? true : false,
          updateOldData: this.dossierDetail?.dossierStatus?.id === 18 ? false : true,
        }
      };
      if (this.commentContent.trim() !== '') {
        this.postComment(this.commentContent.trim(), this.descriptionContent.trim());
        requestBodyObj.comment = this.commentContent.trim();
        requestBodyObj.description = this.descriptionContent.trim();
      } else {
        const msgObj = {
          vi: 'Yêu cầu thanh toán hồ sơ <b>' + this.dossierCode + '</b>',
          en: 'Request for payment of dossier <b>' + this.dossierCode + '</b>!'
        };
        this.postComment(msgObj[this.selectedLang]);
        requestBodyObj.comment = msgObj[this.selectedLang];
      }

      const requestBody = JSON.stringify(requestBodyObj, null, 2);

      this.dossierService.putDossierStatusWithComment(this.dossierId, requestBody).subscribe(async data => {
        if (data.affectedRows === 1) {
          const newDate = tUtils.newDate();
          this.postHistory();
          if (this.config.receivePromotionalProcedureUpdated === 1 || this.config.receivePromotionalProcedureUpdated === '1') {
            this.dossierService.getDossierDetail(this.dossierId).subscribe(data => {
              if (data.sync !== undefined) {
                const dataRequest: any = {};
                const agency = JSON.parse(localStorage.getItem('userAgency'));
                if (agency !== null && agency !== undefined) {
                  dataRequest.agencyId = agency.id;
                } else {
                  dataRequest.agencyId = this.config.rootAgency.id;
                }

                if (this.config.subsystemId !== null && this.config.subsystemId !== undefined) {
                  dataRequest.subsystemId = this.config.subsystemId;
                }

                dataRequest.agencyCode = data.agency.id;
                dataRequest.status = 5;
                dataRequest.code = data.code;
                dataRequest.sourceCode = data.sync.sourceCode;
                dataRequest.comment = this.commentContent.trim();
                dataRequest.date = this.datePipe.transform(newDate, 'yyyyMMddHHmmss');
                this.dossierService.postSynchronizePromotionStatus(dataRequest).subscribe(data => {
                });
              }
            });
          }
          const agencies = this.agencyService.getAgencies();
          const extend = {
            dossier:{
              id: this.dossierId,
              code: this.dossierCode,
            },
            agencies: agencies
          };
          await this.notiService.changeSendSubject.next(
            {
              id: this.dossierDetail?.procedureProcessDefinition?.id,
              phone: this.dossierDetail?.applicant?.data?.phoneNumber,
              email: this.dossierDetail?.applicant?.data?.email,
              currentTask: this.currentTask,
              contentParams: {
                parameters: {
                  sector: !!this.dossierDetail?.procedure?.sector?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name ? this.dossierDetail?.procedure?.sector?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name : '',
                  procedure: !!this.dossierDetail?.procedure?.translate?.find(agency => agency.languageId === Number(this.selectedLangId))?.name ? this.dossierDetail?.procedure?.translate?.find(agency => agency.languageId === Number(this.selectedLangId))?.name : '',
                  agency: !!this.dossierDetail?.agency?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name ? this.dossierDetail?.agency?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name : '',
                  agencyId: !!this.dossierDetail?.agency?.id ? this.dossierDetail?.agency?.id : this.dossierDetail?.agency?.parent?.id,
                  extend: this.isSmsQNM ? extend : {},
                  applicantFullname: this.dossierDetail?.applicant?.data?.fullname,
                  officerFullname: '',
                  subsystem: !!this.env?.subsystem?.name ? this.env?.subsystem?.name : "Hệ thống Một cửa điện tử",
                  dossierCode: this.dossierDetail?.nationCode ? this.dossierDetail?.nationCode : this.dossierDetail?.code ,
                  nextTask: !!this.env?.notify?.additionalRequirement?.nextTask ? this.env?.notify?.additionalRequirement?.nextTask : 'Yêu cầu bổ sung hồ sơ',
                  // IGATESUPP-63184
                  nextStatus: !!this.env?.notify?.directlyPaymentNotification?.nextStatus ? this.env?.notify?.directlyPaymentNotification?.nextStatus : (this.dossierDetail?.dossierTaskStatus?.name ?? ''), // cho bo sung, trang thai hien tai cua ho so
                  dossierStatus: !!this.env?.notify?.directlyPaymentNotification?.dossierStatus ? this.env?.notify?.directlyPaymentNotification?.dossierStatus : (this.dossierDetail?.dossierStatus?.name?.find(t => t.languageId === Number(this.selectedLangId))?.name ?? ''), // cho bo sung, trang thai hien tai cua ho so
                  dossierSearchPadsvcUrl: (!!this.env?.domain?.padsvc ? this.env?.domain?.padsvc : "http://padsvctest.vncitizens.vn/vi/") + "dossier/public",
                  dossierDetailPadsvcUrl: (!!this.env?.domain?.padsvc ? this.env?.domain?.padsvc : "http://padsvctest.vncitizens.vn/vi/") + "dossier/detail/" + this.dossierId,
                  dossierSearchUrl: (!!this.env?.domain?.onegate ? this.env?.domain?.onegate : "http://padsvctest.vncitizens.vn/vi/") + "dossier/search/",
                  dossierDetailUrl: (!!this.env?.domain?.onegate ? this.env?.domain?.onegate : "http://padsvctest.vncitizens.vn/vi/") + "dossier/search/" + this.dossierId,
                  dossierPayDetailPadsvcUrl: (!!this.env?.domain?.onegate ? this.env?.domain?.onegate : "http://padsvctest.vncitizens.vn/vi/") + "dossier/payment/" + this.dossierId,
                  returnMethod: !!this.dossierDetail?.dossierReceivingKind?.name?.find(recieve => recieve.languageId === Number(this.selectedLangId))?.name ? this.dossierDetail?.dossierReceivingKind?.name?.find(recieve => recieve.languageId === Number(this.selectedLangId))?.name : '',
                  receivingDate: '',
                  appointmentDate: '',
                  dossierFee: this.totalCost,
                  reason: this.commentContentPlainText,
                  directlyPaymentDue: this.datePipe.transform(new Date(this.directlyPaymentDue), 'dd/MM/yyyy HH:mm:ss'),
                }
              }
            }
          );
          this.notiService.confirmSendSubject.next({
            confirm: true,
            renewContent: true
          });

          // if (this.env?.enableApprovalOfLeadership == 1){
          //   const data = {
          //     type: 8,
          //     date: newDate,
          //     attachment: this.uploadedImage
          //   }
          //   this.dossierService.updateApprovalData(this.dossierId, data).subscribe(res => {});
          // }
          if(this.env?.enableApprovalOfLeadership === 0 && this.isSyncDBNLGSPTanDanBXD === 1){
            this.syncPostReceiveInforFileFromLocal();
          }

          this.disableConfirmPayment = false;
          this.dialogRef.close(true);
        } else {
          this.disableConfirmPayment = false;
          this.dialogRef.close(false);
        }
      }, err => {
        this.disableConfirmPayment = false;
        this.dialogRef.close(false);
      });
    }
  }

  onDismiss() {
    this.dialogRef.close();
  }

  postComment(commentContent, description?:string) {
    let msgObj = {};
    if(!!description){
      msgObj = {
        vi: `Yêu cầu thanh toán: ${commentContent}<br>Nội dung: ${description}`,
        en: `Payment request: ${commentContent}<br>Description: ${description}`
      };
    }else{
      msgObj = {
        vi: `Yêu cầu yêu cầu thanh toán: ${commentContent}`,
        en: `Payment request: ${commentContent}`
      };
    }
    const content = {
      groupId: 2,
      itemId: this.dossierId,
      user: {
        id: this.accountId,
        fullname: this.userName
      },
      content: msgObj[this.selectedLang],
      file: this.uploadedImage
    };
    const requestBody = JSON.stringify(content, null, 2);
    this.dossierService.postComment(requestBody).subscribe(data => { });
  }

  onCommentEditorChange(event) {
    this.commentContent = event.editor.getData();
    if (event.editor.getData().trim().length > 500) {
      this.isCKMaxlenght = true;
    } else {
      this.isCKMaxlenght = false;
    }
    this.commentContentPlainText = "";
    this.commentContentPlainText = this.getPlainText(this.commentContent);
  }

  getPlainText( strSrc ) {
    let resultStr = "";

    // Ignore the <p> tag if it is in very start of the text
    if(strSrc.indexOf('<p>') == 0)
        resultStr = strSrc.substring(3);
    else
        resultStr = strSrc;

    // Replace <p> with two newlines
    resultStr = resultStr.replace(/<p>/gi, "\r\n\r\n");
    // Replace <br /> with one newline
    resultStr = resultStr.replace(/<br \/>/gi, "\r\n");
    resultStr = resultStr.replace(/<br>/gi, "\r\n");

    //-+-+-+-+-+-+-+-+-+-+-+
    // Strip off other HTML tags.
    //-+-+-+-+-+-+-+-+-+-+-+

    return  resultStr.replace( /<[^<|>]+?>/gi,'' );
  }

  onContentEditorChange(event) {
    this.descriptionContent = event.editor.getData();
    if (event.editor.getData().trim().length > 500) {
      this.isCKMaxlenght = true;
    } else {
      this.isCKMaxlenght = false;
    }
  }

  uploadMultiFile(file, accountId) {
    return new Promise((resolve) => {
      this.procedureService.uploadMultiFile(file, accountId).subscribe(data => {
        this.uploadedImage = data;
        // this.formToJSON();
        resolve(true);
      }, err => {
        console.log(err);
        resolve(false);
      });
    });
  }

  onSelectFile(event) {
    if (event.target.files && event.target.files[0]) {
      for (const i of event.target.files) {
        if (i.size >= this.maxFileSize * 1024 * 1024) {
          const msgObj = {
            vi: 'Kích thước tệp tin quá lớn, tên tệp: ' + i.name,
            en: 'The file is too large, file name: ' + i.name
          };
          this.snackbarService.openSnackBar(0, '', msgObj[this.selectedLang], 'error_notification', this.config.expiredTime);
          return;
        }
        if (this.acceptFileType.filter(t => t === i.type.toLowerCase()).length > 0) {
          this.files.push(i);
          const extension = i.name.substring(i.name.lastIndexOf('.')).split('.').pop();
          this.urls.push(this.getFileIcon(extension));

          const reader = new FileReader();
          reader.onload = () => {
            this.uploaded = true;
          };
          if (i.name.length > 20) {
            const startText = i.name.substr(0, 5);
            const shortText = i
              .name
              .substring(i.name.length - 7, i.name.length);
            this.fileNames.push(startText + '...' + shortText);
            this.fileNamesFull.push(i.name);
          } else {
            this.fileNames.push(i.name);
            this.fileNamesFull.push(i.name);
          }
          reader.readAsDataURL(i);
        } else {
          const msgObj = {
            vi: 'Không hỗ trợ loại tệp tin ',
            en: 'File type is not supported '
          };
          // tslint:disable-next-line: max-line-length
          this.snackbarService.openSnackBar(0, '', msgObj[this.selectedLang] + i.type.substring(i.type.lastIndexOf('/') + 1).toUpperCase(), 'error_notification', this.config.expiredTime);
        }
      }
    }
  }

  removeItem(index: number) {
    this.urls.splice(index, 1);
    this.fileNames.splice(index, 1);
    this.fileNamesFull.splice(index, 1);
    this.files.splice(index, 1);
    this.blankVal = '';
  }

  getFileIcon(ext) {
    return this.config.cloudStaticURL + 'icon/files/300x300/' + ext + '.png';
  }

  downloadTemplate(){
    window.open(this.fileTemplate, '_self');
  }
  // DBN - BXD - dong bo thong hs qua LGSP Tan Dan
  syncPostReceiveInforFileFromLocal (){
    this.dossierService.getDossierDetail(this.dossierId).subscribe(data => {
      let dataApplicant =  data?.applicant?.data;
      let nhanThongTinHSTuDP = [];
      let thongTinNguoiNop = {
        HoTenNguoiNop : dataApplicant?.fullname, //*
        SoCMND : dataApplicant?.identityNumber, //*
        EmailNguoiNop: dataApplicant?.email, 
        SoDienThoaiNguoiNop: dataApplicant?.phoneNumber, //*
        DiaChiThuongTruNguoiNop: dataApplicant?.address + "," 
                               + dataApplicant?.village?.label + "," 
                               + dataApplicant?.district?.label + ","
                               + dataApplicant?.province?.label + ","
                               + dataApplicant?.nation?.label, //*
        MaDoiTuong: ""
      };
      let giayToTrongQuaTrinhXuLy = {
        MaGiayTo: "",
        TenGiayTo: "",
        NoiDungBase64: "",
        DinhDangTepTin: "", // dinh dang .pdf, .doc, .png,...
        MoTa: "",
        LoaiGiayTo: ""
      }
      let hinhThucTraKetQua = 0;
      switch(data?.dossierReceivingKind?.id) {
        case '5f8968888fffa53e4c073ded': 
        hinhThucTraKetQua = 0;
        break;
        case '604ecde877bed4110c1dd0d1': 
          hinhThucTraKetQua = 1;
          break;
        case '6163f1e20f363b04cf60b224':
          hinhThucTraKetQua = 1;
          break;
        case '5f8969018fffa53e4c073dee':
          hinhThucTraKetQua = 1;
          break;
      }
      let arrThongTinTienTrinh = [];
      let dossierStatus = this.statusName;
      data?.task.forEach(task => {
          if(task?.isCurrent === 1){
            let thongTinTienTrinh = {
              NguoiXuLy: task?.assignee?.fullname, //*
              ChucDanh: "", 
              ThoiDiemXuLy: this.datePipe.transform(task?.createdDate, 'yyyyMMddHHmmss'), //* yyyyMMddHHmmss
              PhongBanXuLy: task?.agency?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name, 
              NoiDungXuLy:  task?.bpmProcessDefinitionTask?.remind ?  task?.bpmProcessDefinitionTask?.remind?.name : "",
              NgayBatDau:   this.datePipe.transform(task?.createdDate, 'yyyyMMddHHmmss'), //yyyyMMddHHmmss
              NgayKetThucTheoQuyDinh: task?.isCurrent === 0? this.datePipe.transform(task?.updatedDate, 'yyyyMMddHHmmss') : ""
            }
            arrThongTinTienTrinh.push(thongTinTienTrinh);
          }
         
      });
      let dossierSync = {
        MaHoSo: data.code, //*
        TrangThaiHoSo: dossierStatus, // phu luc 2 *
        NgayTiepNhan: this.datePipe.transform(data?.acceptedDate, 'yyyyMMddHHmmss') ,// yyyyMMddHHmmss*
        NgayHenTraKetQua: this.datePipe.transform(data?.appointmentDate, 'yyyyMMddHHmmss'), //yyyyMMddHHmmss
        ThongTinNguoiNop : thongTinNguoiNop,
        GiayToTrongQuaTrinhXuLy : [],
        HinhThucTraKetQua: hinhThucTraKetQua, // 0: tra truc tiep 1: tra qua duong buu dien 2: tra kq truc tuyen
        ThongTinTienTrinh : arrThongTinTienTrinh,
        MaTTHC: data?.procedure?.code,
        TenTTHC: data?.procedure?.translate?.find(procedure => procedure.languageId === Number(this.selectedLangId))?.name ? data?.procedure?.translate?.find(procedure => procedure.languageId === Number(this.selectedLangId))?.name : '',
      }
      //nhanThongTinHSTuDP.push(dossierSync);
      let params = "agencyId=" + data?.agency.id + "&subsystemId=" + this.config?.subsystemId ; // &configId
      this.dossierService.postReceiveInforFileFromLocal(params, dossierSync).subscribe(async data => {
        console.log("===postReceiveInforFileFromLocal===");
        console.log(data);
      }, er => {
        console.log(er);
      })
    }, err => {
      console.log(err);
    });
  }
  async postTimesheetDirectlyPaymentNotification() {
    const currentDate = this.datePipe.transform(tUtils.newDate(), 'yyyy-MM-ddTHH:mm:ss.SSSZ');
    const formObj = this.updateForm.getRawValue();
    const listTimesheet = [];
    const processingTimeUnit = "d";
    listTimesheet.push({
      timesheet: {
        id:
          this.dossierDetail.procedureProcessDefinition?.processDefinition
            ?.timesheet !== undefined
            ? this.dossierDetail.procedureProcessDefinition
              .processDefinition.timesheet.id
            : this.config.defaultTimesheetId,
      },
      dossier: {
        id: this.dossierId,
      },
      duration: formObj.numberPausePaidDay,
      startDate: currentDate,
      endDate: null,
      checkOffDay: true,
      offTime: this.env?.limitedAppointmentTime ? this.env?.limitedAppointmentTime : null,
      processingTimeUnit: processingTimeUnit,
    });

    const requestBody = JSON.stringify(listTimesheet, null, 2);
    const data = await this.genTime(requestBody);
    console.log("due")
    console.log(data[0].due)

    if (!!data) {
      this.directlyPaymentDue = data[0].due;
    } else {
      return null;
    }
  }

  genTime(requestBody) {
    return new Promise(resolve => {
      this.dossierService.postTimesheet(requestBody).subscribe(data => {
        if (!!data && data.length !== 0) {
          resolve(data);
        } else {
          resolve(null);
        }
      }, (err) => {
        resolve(null);
      });
    });
  }

}

export class ConfirmDirectlyPaymentRequestDialogModel {
  constructor(public dossierId: string, public dossierCode: string, public typeProcess?) {
  }
}
