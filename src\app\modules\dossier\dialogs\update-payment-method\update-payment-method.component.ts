import { Component, Inject, OnInit } from '@angular/core';
import { FormControl, FormGroup } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { EnvService } from 'src/app/core/service/env.service';
import { BasecatService } from 'src/app/data/service/basecat/basecat.service';
import { DeploymentService } from 'src/app/data/service/deployment.service';
import { DossierService } from 'src/app/data/service/dossier/dossier.service';
import { SnackbarService } from 'src/app/data/service/snackbar/snackbar.service';
import { PadmanService } from 'src/app/data/service/svc-padman/padman.service';

@Component({
  selector: 'app-update-payment-method',
  templateUrl: './update-payment-method.component.html',
  styleUrls: ['./update-payment-method.component.scss']
})
export class UpdatePaymentMethodComponent implements OnInit {
  config = this.envService.getConfig();
  listPayment=[];
  enableShowFormOfPayment = this.deploymentService.getAppDeployment()?.showFormOfPayment?.enable ? (this.deploymentService.getAppDeployment().showFormOfPayment.enable > 0 ? true : false) : false;
  methodPaymentListShowFormOfPayment= this.deploymentService.getAppDeployment()?.showFormOfPayment?.methodPaymentList ? this.deploymentService.getAppDeployment().showFormOfPayment.methodPaymentList : [];
  agencyIdsShowFormOfPayment= this.deploymentService.getAppDeployment()?.showFormOfPayment?.agencyIds ? this.deploymentService.getAppDeployment().showFormOfPayment.agencyIds : [];
  enableChangePaymentMethodDossier= this.deploymentService.getAppDeployment()?.enableChangePaymentMethodDossier ? this.deploymentService.getAppDeployment().enableChangePaymentMethodDossier : 0;
  paymentMethodCategoryId = !!this.deploymentService.env?.paymentMethodCategoryId ? this.deploymentService.env?.paymentMethodCategoryId : "5f3a491c4e1bd312a6f00011";
  showDetailPaymentMethod = false;
  dossierId:any;
  accepterInfoId:any;
  accepterInfoFullName:any;
  oldPaymentMethod:any;
  selectedLang: string;
  oldPaymentMethodId: string;
  paymentForm = new FormGroup({
    paymentMethod: new FormControl('')
    });
  constructor(    
    public dialogRef: MatDialogRef<UpdatePaymentMethodComponent>,
    @Inject(MAT_DIALOG_DATA) public data: UpdatePaymentMethodDialogModel,
    private envService: EnvService,
   private basecatService: BasecatService,
   private deploymentService: DeploymentService,
   private padmanService: PadmanService,
   private snackbarService: SnackbarService,
   private dossierService: DossierService) { 
    this.dossierId = data.dossierId;
    this.accepterInfoId = data.accepterInfoId;
    this.accepterInfoFullName = data.accepterInfoFullName;
    this.oldPaymentMethod = data.oldPaymentMethod;
    this.oldPaymentMethodId = data.oldPaymentMethodId;
   }

  async ngOnInit(): Promise<void> {
    const userAgency = JSON.parse(localStorage.getItem('userAgency'));
    this.selectedLang = localStorage.getItem('language');
    if(this.enableShowFormOfPayment && this.methodPaymentListShowFormOfPayment.length > 0 && this.agencyIdsShowFormOfPayment.filter( item => item == userAgency.id || item == userAgency?.parent?.id  || item == userAgency?.ancestors?.id ).length == 0){
      
      // this.showDetailPaymentMethod = true;
      this.getListPaymentList();
    }


  }

  changePaymentMethod(event) {
  }

  onDismiss() {
    this.dialogRef.close();
  }

  async getListPaymentList() {
    let directPaymentMethodCategoryId = this.config.directPaymentMethodCategoryId;
    if(this.enableChangePaymentMethodDossier == 1){
      directPaymentMethodCategoryId = this.paymentMethodCategoryId;
    }
    let searchString= '?page=0&size=1000&status=1&category-id=' + directPaymentMethodCategoryId + '&ids=' + this.methodPaymentListShowFormOfPayment.toString();
    this.basecatService.getListTagByCategoryId(searchString).subscribe(async data => {
      let list = [];
      for (let i = 0; i < data.numberOfElements; i++) {
        list.push(data.content[i]);
      }
      this.listPayment = list;
      console.log("PaymentList", this.listPayment);
      if(!!this.oldPaymentMethodId){
        this.paymentForm.patchValue({
          paymentMethod: this.oldPaymentMethodId
        });
      }
    });
  }

  postHistory(type, col, oldVal, newVal) {
    const content = {
      groupId: 1,
      itemId: this.dossierId,
      user: {
        id: this.accepterInfoId,
        name: this.accepterInfoFullName,
      },
      type,
      deploymentId: this.config.deploymentId,
      action: [
        {
          fieldNameRbk: col,
          originalValue: oldVal,
          newValue: newVal,
        },
      ],
    };
    const requestBody = JSON.stringify(content, null, 2);
    this.dossierService.postHistory(requestBody).subscribe((data) => {});
  }

  onConfirm(){
    const paymentForm = this.paymentForm.getRawValue();
    if(paymentForm.paymentMethod == ''){
      const msgObj = {
        vi: 'Vui lòng chọn hình thức thanh toán!',
        en: 'Please choose your payment expressions!!'
      };
      this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', 2000);
      return;
    }
    if(paymentForm.paymentMethod != '' && paymentForm.paymentMethod != null){
      const requestBody ={
        paymentMethod: {
          id: paymentForm.paymentMethod,
          code: (this.enableChangePaymentMethodDossier==1 && !!this.listPayment.filter(item => item.id == paymentForm.paymentMethod)[0]?.code)? this.listPayment.filter(item => item.id == paymentForm.paymentMethod)[0]?.code : '',
          name: this.listPayment.filter(item => item.id == paymentForm.paymentMethod)[0].name
        }
      }
      this.dossierService.putDossierOnline(this.dossierId, JSON.stringify(requestBody, null, 2)).subscribe(data => {
        const msgObj = {
          vi: 'Cập nhật hình thức thanh toán thành công!',
          en: 'Update payment method successfully!'
        };
        this.snackbarService.openSnackBar(1, msgObj[this.selectedLang], '', 'success_notification', 2000);
        this.postHistory(3, 'Hình thức thanh toán',  this.oldPaymentMethod, this.listPayment.filter(item => item.id == paymentForm.paymentMethod)[0].name);
        window.location.reload(); 
        const result = {
          status: true
        };
        this.dialogRef.close(result);
      }, err => {
        const result = {
          status: false,
          code: err
        };
        this.dialogRef.close(result);
      });
    }
  }
}

export class UpdatePaymentMethodDialogModel {
  constructor(public dossierId: string, public accepterInfoId: string, public accepterInfoFullName: string, public oldPaymentMethod: string, public oldPaymentMethodId?: string) { }
}