import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { ApiProviderService } from 'src/app/core/service/api-provider.service';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class FilemanService {

  constructor(
    private http: HttpClient,
    private apiProviderService: ApiProviderService
  ) { }

  private fileman = this.apiProviderService.getUrl('digo', 'fileman');

  uploadMultiFile(imgFiles, accountId): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.append('Accept', 'application/json');
    const formData: FormData = new FormData();
   
    for(let i = 0; i < imgFiles.length; i++){
      const file: File = imgFiles[i];
      // formData.append('files', file, file.name);
      formData.append('files', file);
      const x = formData;
    }
    formData.append('account-id', accountId);
    return this.http.post<any>(this.fileman + '/file/--multiple?uuid=1', formData, { headers });
  }

  uploadFile(files): Observable<any> {
    let headers = new HttpHeaders();
    const formData: FormData = new FormData();
    files.forEach(files => {
      const file: File = files;
      formData.append('files', file, file.name);
    });
    return this.http.post<any>(this.fileman + '/file/--multiple?uuid=1', formData, { headers });
  }

  updateFile(id: string, name: string): Observable<any> {
    let headers = new HttpHeaders();
    headers.append('Access-Control-Allow-Credentials', 'true');
    const formData: FormData = new FormData();
    formData.append('name', name);
    return this.http.put(this.fileman + `/file/${id}/rename`, formData,{ headers });
  }


  getFileNameSize(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.fileman + '/file/' + id + '/filename+size', { headers });
  }

  downloadFile(id, uuid?): Observable<any> {    
    return this.http.get(this.fileman + '/file/' + id + '?uuid=' + uuid, { responseType: 'blob' as 'json' }).pipe();   
  }

  downloadFileWithCache(id, uuid?): Observable<any> {    
    return this.http.get(this.fileman + '/file/' + id + '/--content?uuid=' + uuid, { responseType: 'blob' as 'json' }).pipe();    
  }

  deleteFile(id) {    
    return this.http.delete<any>(this.fileman + '/file/' + id).pipe();
  }

  getFileHash(fileId, cert): Observable<any>{
    let headers = new HttpHeaders()
    headers = headers.append('Accept', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'))
    const body = { fileId: fileId, certChainBase64: cert };
    return this.http.post(this.fileman + '/file/--viettel-ca-hash-file', body, { headers });
  }

  insertSign(fileId, sign): Observable<any> {
    let headers = new HttpHeaders()
    headers = headers.append('Accept', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'))
    const body = { fileId: fileId, signature: sign };
    return this.http.post(this.fileman + '/file/--viettel-ca-insert-sign', body, { headers });
  }
  convertDoc2Pdf(id: string): Promise<any> {
    const headers = new HttpHeaders();
    return this.http
      .put(this.fileman + '/file/' + id + '/--to-pdf', { headers })
      .toPromise();
 }

  getFile(id): Observable<any> {
    let headers = new HttpHeaders();    
    return this.http.get<any>(this.fileman + '/file/' + id, { headers, responseType: 'blob' as 'json' }).pipe();    
  }

  getSignHistories(fileId): Observable<any>{
    const URL = `${this.fileman}/file/${fileId}/--signs`;
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(URL, { headers });
  }

  fileGetLink(id, dossierId?): Promise<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.post<any>(this.fileman + '/file/get-link?id=' + id + '&dossierId=' + dossierId, {}, { headers, responseType: 'text' as 'json' }).toPromise();
  }

  fileInfo(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    // return this.http.get<any>(this.apiProviderService.getUrl('digo', 'fileman') + '/wopi/files/' + id, {headers}).pipe();
    return this.http.get<any>(this.fileman + '/file/get-link/' + id, {headers}).pipe();
  }

  async getFileBase64(ids): Promise<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.fileman + '/file/--base64?ids=' + ids, { headers }).toPromise();
  }
  
  uploadFileByUrl(accountId, url): Observable<any> {
    let headers = new HttpHeaders()
      .set('Accept-Language', localStorage.getItem('language'))
    let searchString = "?account-id=" + accountId + "&file-url=" + url;
    return this.http.post<any>(this.apiProviderService.getUrl('digo', 'fileman') + '/file/--by-url' + searchString, { headers }).pipe();
  }

  updateSignedFile(id: string, formData: FormData): Observable<any> {
    let headers = new HttpHeaders();
    // headers = headers.set('Accept-Language', localStorage.getItem('language'));
    // headers = headers.set('Content-Type', 'multipart/form-data');
    headers.append('Access-Control-Allow-Credentials', 'true');
    return this.http.put(this.fileman + `/file/${id}/--update-signed-file`, formData,{ headers });
  }

  downloadFileWithDossierId(id, dossierId?, uuid?): Observable<any> {
    let headers = new HttpHeaders();
    //let uuid = localStorage.getItem("tempUID");
    switch (localStorage.getItem('isLoggedIn')) {
      case 'false':
        const token = localStorage.getItem('OAuth2TOKEN');
        headers = headers.append('Authorization', 'Bearer ' + token);
        return this.http.get(this.fileman + '/file/' + id + '?uuid=' + uuid + "&dossier-id=" + dossierId, { headers, responseType: 'blob' as 'json' }).pipe();
      case 'true':
        return this.http.get(this.fileman + '/file/' + id + '?uuid=' + uuid + "&dossier-id=" + dossierId, { responseType: 'blob' as 'json' }).pipe();
    }
  }
  convertFiletoPDF(files): Observable<any> {
    let headers = new HttpHeaders();
    const formData: FormData = new FormData();
    files.forEach(file => {
      formData.append('file', file, file.name);
    });
    return this.http.post<any>(this.fileman + '/file/--to-pdf', formData, { headers });
  }
}
