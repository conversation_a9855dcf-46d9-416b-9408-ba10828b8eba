.close-button {
    float: right;
    top: -24px;
    right: -24px;
}
.dialog_title {
    font-style: normal;
    font-weight: 500;
    font-size: 20px;
    color: #CE7A58;
}
.dialog_content {
    font-size: 15px;
}

.dialog_content .highlight{
    color: #CE7A58;
}

.dialog_content .cardContent .formFieldItems{
    flex-wrap: wrap;
    display:flex;
    justify-content: space-between;
}

.dialog_content .cardContent .formFieldItems .showHideOption{
    height: 3.3em;
    display:flex;
    padding-top: .3em;
}

.dialog_content .cardContent .formFieldItems .showHideOption .cbx_default{
    align-self: center;
    padding-right: 2em;
}

::ng-deep .dialog_content .showHideOption .mat-checkbox-indeterminate.mat-accent .mat-checkbox-background, 
::ng-deep .dialog_content .showHideOption .mat-checkbox-checked.mat-accent .mat-checkbox-background {
    background-color: #ce7a58;
}

::ng-deep .dialog_content .cardContent .mat-form-field-appearance-outline .mat-form-field-outline {
    color: transparent;
}

::ng-deep .dialog_content .cardContent .mat-form-field-appearance-outline .mat-form-field-outline-thick {
    color: #dddddd;
}

::ng-deep .dialog_content .cardContent .mat-form-field-appearance-outline .mat-form-field-outline {
    background-color: #eaebeb;
    border-radius: 5px;
}

::ng-deep .dialog_content .cardContent .mat-form-field-appearance-outline .mat-form-field-flex > .mat-form-field-infix {
    padding: 0.7em 0px !important;
}

::ng-deep .dialog_content .cardContent .mat-form-field-appearance-outline .mat-form-field-label-wrapper {
    top: -1.2em; 
}

::ng-deep .dialog_content .cardContent .mat-form-field-appearance-outline.mat-form-field-can-float.mat-form-field-should-float .mat-form-field-label {
    color: #ce7a58;
    margin-bottom: 1em;
    transform: translateY(-1.4em) scale(.75);
    width: 133.33333%;
}

::ng-deep .dialog_content .cardContent .prc_AgencyAutocomplete .mat-option-text {
    font-size: 14px !important;
}

::ng-deep .dialog_content .prcProcostPayment_tbl .mat-checkbox-indeterminate.mat-accent .mat-checkbox-background, 
::ng-deep .dialog_content .prcProcostPayment_tbl .mat-checkbox-checked.mat-accent .mat-checkbox-background {
    background-color: #ce7a58;
}

::ng-deep .dialog_content .mat-form-field .error_Msg {
    font-size: 12px !important;
    float: right;
    display: flex;
    color: #ce7a58;
}

::ng-deep .dialog_content .mat-form-field .error_Msg .err {
    background-color: #f2a63494;
    border-radius: 50%;
    width: 1.2em;
    height: 1.2em;
    justify-content: center;
    display: flex;
    margin-left: .5em;
}

::ng-deep .dialog_content .mat-form-field .error_Msg .err .mat-icon{
    color: #ce7a58;
    vertical-align: middle;
    align-self: center;
    transform: scale(0.8);
}

.dialog_content .advanceBtn {
    color: #ce7a58;
    padding: .2em;
}

::ng-deep .applyBtn {
    margin-top: 1em;
    background-color: #CE7A58;
    color: #fff;
    height: 3em;
}