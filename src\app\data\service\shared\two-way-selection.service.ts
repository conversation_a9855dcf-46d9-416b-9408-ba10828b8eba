import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable, of, Subject } from 'rxjs';
import { ApiProviderService } from 'src/app/core/service/api-provider.service';
import { EnvService } from 'src/app/core/service/env.service';

@Injectable({
    providedIn: 'root'
})
export class TwoWaySelectionService {

    constructor(
        private http: HttpClient,
        private envService: EnvService,
        private apiProviderService: ApiProviderService,
    ) { }

    getTagsUrl = this.apiProviderService.getUrl('digo', 'basecat') + '/tag/--by-category-id?category-id=';
    getProceduresUrl = this.apiProviderService.getUrl('digo', 'basepad') + '/procedure';
    getSectorUrl = this.apiProviderService.getUrl('digo', 'basepad') + '/sector/--by-list-agency-cto';
    getProcedureByAgencySectorUrl = this.apiProviderService.getUrl('digo', 'basepad') + '/procedure/--by-list-agency-sector-cto';
    public callApi(type, parent, keyword, page): Observable<any> {
        switch (type) {
            case 'tag':
                return this.getTagList(parent, page, keyword);
            case 'procedure':
                return this.getProcedureList(parent, page, keyword);
            case 'sector':
                return this.getSectorList(parent, page, keyword);
            case 'procedureCTO':
                return this.getProcedureByAgencySector(parent, page, keyword);
        }
    }

    getTagList(parent, page, keyword): Observable<any> {
        return this.http.get(this.getTagsUrl + parent + `&page=${page}` + `&size=5`+ `&keyword=${keyword}`);
    }

    getProcedureList(parent, page, keyword): Observable<any> {
        return this.http.get(this.getProceduresUrl + '?ledger=61b93d7beea077f35ba00001&spec=page' + `&agency-id=${parent}` + `&page=${page}` + `&size=5`+ `&keyword=${keyword}`);
    }

    getSectorList(parent, page, keyword): Observable<any> {
        return this.http.get(this.getSectorUrl + `?spec=page&page=${page}` + `&size=10` + `&arr-agency-id=${parent}` + `&keyword=${keyword}`);
    }

    getProcedureByAgencySector(parent, page, keyword): Observable<any> {
        return this.http.get(this.getProcedureByAgencySectorUrl + `?spec=page&page=${page}&size=10&status=1&keyword=${keyword}${parent}`);
    }
}
