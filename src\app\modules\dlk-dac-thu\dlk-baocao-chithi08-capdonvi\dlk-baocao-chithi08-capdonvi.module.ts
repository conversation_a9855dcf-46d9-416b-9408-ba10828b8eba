import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';

import { NgxPrinterModule } from 'ngx-printer';
import { SharedModule } from 'src/app/shared/shared.module';
import { DlkBaoCaoChiThi08CapDonViRoutingModule } from './dlk-baocao-chithi08-capdonvi-routing.module';
import { DlkBaoCaoChiThi08CapDonViComponent } from './dlk-baocao-chithi08-capdonvi.component';


@NgModule({
  declarations: [DlkBaoCaoChiThi08CapDonViComponent],
  imports: [
    CommonModule,
    DlkBaoCaoChiThi08CapDonViRoutingModule,
    SharedModule,
    NgxPrinterModule.forRoot({ printOpenWindow: true })
  ]
})
export class DlkBaoCaoChiThi08CapDonViModule { }
