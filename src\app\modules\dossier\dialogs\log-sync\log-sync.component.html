<button mat-icon-button class="close-button" (click)="onDismiss()">
    <mat-icon>close</mat-icon>
</button>
<h3 class="dialog_title" mat-dialog-title>Kiểm tra đồng bộ hồ sơ chưa xử lý</h3>

<div class="main-layout">
    <div mat-dialog-content class="dialog_content">
        <span>Thông tin log đồng bộ chưa xử lý cho hồ sơ </span><span
            class="highlight">{{dossierCode}}</span><span>?</span>
        <div class="commentEditor" fxLayout="column" fxLayout.xs="column" fxLayout.sm="column">
            <div class="frm_tbl_sector sector">
                <table mat-table
                    [dataSource]="dataSource">
                    <ng-container matColumnDef="stt">
                        <mat-header-cell *matHeaderCellDef>STT</mat-header-cell>
                        <mat-cell *matCellDef="let row" data-label="STT">{{row.stt}}</mat-cell>
                    </ng-container>

                    <ng-container matColumnDef="status">
                        <mat-header-cell *matHeaderCellDef >Trạng thái</mat-header-cell>
                        <mat-cell *matCellDef="let row" data-label="Trạng thái" class="cell_code"> {{row.statusName}}
                        </mat-cell>
                    </ng-container>

                    <ng-container matColumnDef="date">
                        <mat-header-cell *matHeaderCellDef>Ngày tạo</mat-header-cell>
                        <mat-cell *matCellDef="let row" data-label="Ngày tạo">
                            <span  #tooltip="matTooltip" matTooltip="{{row.name}}" mattooltipposition="above">{{row.createdDate | date : 'dd/MM/yyyy HH:mm:ss'}}</span>
                        </mat-cell>
                    </ng-container>

                    <ng-container matColumnDef="action">
                        <mat-header-cell *matHeaderCellDef i18n>Thao tác</mat-header-cell>
                        <mat-cell *matCellDef="let row" data-label="Thao tác" i18n-data-label>
                            <button mat-icon-button [matMenuTriggerFor]="actionMenu">
                                <mat-icon>more_horiz</mat-icon>
                            </button>
                            <mat-menu #actionMenu="matMenu" xPosition="before">
                                <button mat-menu-item class="menuAction" (click)="syncByCode(dossierCode)">
                                    <mat-icon>sync</mat-icon><span>Yêu cầu đồng bộ</span>
                                </button>
                                <!-- <button mat-menu-item class="menuAction" (click)="updateSectorDialog(row.id)">
                                    <mat-icon>edit</mat-icon> <span i18n>Cập nhật</span>
                                </button>
                                <button mat-menu-item class="menuAction" (click)="deleteSectorDialog(row.id, row.name)">
                                    <mat-icon>delete_outline</mat-icon> <span i18n>Xoá</span>
                                </button> -->
                            </mat-menu>
                        </mat-cell>
                    </ng-container>



                    <mat-header-row *matHeaderRowDef="displayedColumns"></mat-header-row>
                    <mat-row *matRowDef="let row; columns: displayedColumns;"></mat-row>
                </table>
            </div>
        </div>

    <mat-dialog-actions align="center">
        <button mat-flat-button fxFlex='20' class="rejectBtn" (click)="onDismiss()">
            <span>Đóng</span>
        </button>
    </mat-dialog-actions>
</div>

<!-- <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="column" fxLayoutAlign="center" fxLayoutGap="1rem">
    <button mat-flat-button fxFlex='20' class="applyBtn" (click)="onConfirm()">
        <span i18n>Đồng ý</span>
    </button>
    <button mat-flat-button fxFlex='20' class="rejectBtn" (click)="onDismiss()">
        <span>Không đồng ý</span>
    </button>
</div> -->