import { Component, OnInit, Inject, ViewChild, LOCALE_ID } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { FormGroup, Validators, FormControl } from '@angular/forms';
import { SectorService } from 'src/app/data/service/sector/sector.service';
import { Router } from '@angular/router';
import { Observable, ReplaySubject, Subject } from 'rxjs';
import { HomeService } from 'src/app/data/service/home/<USER>';
import { MatSelect } from '@angular/material/select';
import { ProcedureService } from 'src/app/data/service/procedure/procedure.service';
import { EnvService } from 'src/app/core/service/env.service';
import { MatExpansionPanel } from '@angular/material/expansion';
import { DeploymentService } from 'src/app/data/service/deployment.service';
import { ReceiptBookService } from 'src/app/data/service/cto-statistics/receipt-book.service';
import { SnackbarService } from 'src/app/data/service/snackbar/snackbar.service';
import { debounceTime } from 'rxjs/operators';
export interface Agency {
  id: string;
  name: string;
}

@Component({
  selector: 'app-add-receipt-book-agency',
  templateUrl: './add-receipt-book-agency.component.html',
  styleUrls: ['./add-receipt-book-agency.component.scss']
})
export class AddReceiptBookAgencyComponent implements OnInit {
  searchForm = new FormGroup({
    code: new FormControl('', [Validators.required]),
    parentId: new FormControl(''),
    agency: new FormControl(''),
    status: new FormControl('' + 1),
    search1: new FormControl(''),
    search2: new FormControl(''),
  });
  listAgency: Agency[] = [];
  listProcedureLevel = [];
  timeOutGetAgencyList: any = null;
  searchAgencyKeyword = "";
  listSector = [];
  // tslint:disable-next-line: ban-types
  languageIdUsed = [228];
  listLanguage = [
    {
      languageId: 228,
      name: 'Tiếng Việt'
    },
    {
      languageId: 46,
      name: 'English'
    }
  ];
  showSearch1 = false;
  showSearch2 = false;
  filteredAgencyOptions: Observable<Agency[]>;
  newAttribute: any = {
    languageId: 228,
    name: '',
    listLanguageItem: JSON.parse(JSON.stringify(this.listLanguage))
  };
  fieldArray: Array<any> = [this.newAttribute];
  status: Array<any> = [
    {
      status: 0,
      name: 'Đóng'
    },
    {
      status: 1,
      name: 'Mở'
    }
  ];
  nullAgency = {
    id: null,
    name: null
  };
  page = 0;
  ELEMENTDATA = [];
  dataSource = {};
  idPosted;
  test = 'abc';
  nameParent;
  searchSectorAgency = '';
  protected onDestroy = new Subject<void>();
  protected agencyAccept: Agency[] = this.listAgency;
  public agencyAcceptCtrl: FormControl = new FormControl();
  public agencyAcceptFilterCtrl: FormControl = new FormControl();
  public filteredAgencyAccept: ReplaySubject<Agency[]> = new ReplaySubject<Agency[]>(1);
  listFullAgencyPage = 0;
  isFullListAgency = false;
  searchAgency = '';
  searchAgencyPage = 0;
  isFullListSearchAgency = false;
  config = this.envService.getConfig();
  env: any = this.deploymentService.getAppDeployment()?.env;

  protected onDestroy2 = new Subject<void>();
  protected listChooseSector: Agency[] = [];
  listSectorPage = 0;
  isFullListSector = false;
  isFullListSearchSector = false;
  keySearchSector = '';
  keySearchSectorAgency = '';
  public sectorAcceptCtrl: FormControl = new FormControl();
  public sectorAcceptFilterCtrl: FormControl = new FormControl();
  public filteredSectorAccept: ReplaySubject<Agency[]> = new ReplaySubject<Agency[]>(1);
  searchSector = '';
  searchSectorPage = 0;

  listChooseSectorAgency = [];
  public sectorAgencyAcceptCtrl: FormControl = new FormControl();
  public sectorAgencyAcceptFilterCtrl: FormControl = new FormControl();
  public filteredSectorAgencyAccept: ReplaySubject<Agency[]> = new ReplaySubject<Agency[]>(1);
  listIdSectorAgency = [];

  detailAgency: any;
  @ViewChild('singleAgencyAcceptSelect', { static: true }) singleAgencyAcceptSelect: MatSelect;
  @ViewChild('panel1') firstPanel: MatExpansionPanel;
  @ViewChild('panel2') firstPanel2: MatExpansionPanel;
  addSectorCode = this.deploymentService.env.OS_QNM.addSectorCode;
  
  constructor(
    public dialogRef: MatDialogRef<AddReceiptBookAgencyComponent>,
    @Inject(MAT_DIALOG_DATA) public data: ConfirmReceiptBookAgencyModel,
    private homeService: HomeService,
    private mainService: SectorService,
    private procedureService: ProcedureService,
    private sectorService: SectorService,
    private envService: EnvService,
    private deploymentService: DeploymentService,
    private router: Router,
    private receiptBookService: ReceiptBookService,
    private snackbarService: SnackbarService,
    @Inject(LOCALE_ID) public localeId: string,) { }

  ngOnInit(): void {
    // tslint:disable-next-line: no-unused-expression
    this.searchForm.controls.code;
    const searchString = '?page=0&size=50&spec=page';
    const objectForm = this.searchForm.getRawValue();
    this.keySearchSector = '&allAgency=0';
    this.getListReceiptBookAgency(searchString + '&allAgency=1');
    this.getListReceiptBook();
    this.getListAgency();
    this.sectorAcceptFilterCtrl.valueChanges
    .pipe(debounceTime(500))
    .subscribe(() => {
      this.filterSectorAccept();
    });    
  }

  click1() {
    this.firstPanel.toggle();
  }
  click2() {
    this.firstPanel2.toggle();
  }


  getListAgency() {
    const userAgency = JSON.parse(localStorage.getItem('userAgency'));
    let rootAgencyId: any = '';
    if (userAgency !== null) {
      rootAgencyId = userAgency.id;
    } else {
      rootAgencyId = this.config.rootAgency.id;
    }
    let page = this.listFullAgencyPage;
    let keyword = '';
    if (this.searchAgency !== '') {
      page = this.searchAgencyPage;
      keyword = this.searchAgency;
    }
    let urlString = '';
    if (this.env.rootAgency !== null && this.env.rootAgency !== '' && this.env.rootAgency !== undefined)
      {
        urlString = '&tag-id=0000591c4e1bd312a6f00003&ancestor-id=' + this.env.rootAgency.id;
      }
    if (this.searchAgency === '' || this.isFullListSearchAgency !== true) {
      this.procedureService.getListAgencyWithParent('name+code/--full?page=' + page + '&related-token=true&keyword='
        + encodeURIComponent(keyword) + '&size=50&sort=name.name,asc&status=1' + urlString).subscribe(data => {
          if (this.searchAgency !== '') {
            this.searchAgencyPage++;
            this.isFullListSearchAgency = data.last;
          }
          else {
            this.listFullAgencyPage++;
            this.isFullListAgency = data.last;
          }
          for (let i = 0; i < data.numberOfElements; i++) {
            if (!this.listAgency.filter(agency => agency.id === data.content[i].id)[0]) {
              const arrData: any = {};
              arrData.id = data.content[i].id;
              arrData.code = data.content[i].code;
              for (let n = 0; n < data.content[i].name.length; n++)
              {
                if (data.content[i].name[n].languageId === Number(localStorage.getItem('languageId')))
                {
                  arrData.name = data.content[i].name[n].name;
                }
              }
              this.listAgency.push(arrData);
            }
          }
          this.listAgency = JSON.parse(JSON.stringify(this.listAgency).replace(/null/g, '""'));
          this.agencyAccept = JSON.parse(JSON.stringify(this.listAgency).replace(/null/g, '""'));
          // set initial selection
          this.filteredAgencyAccept.next(this.agencyAccept.filter(agency => (agency.name.toLowerCase().indexOf(keyword) > -1)));
        }, err => {
          console.log(err);
        });
    }
  }

  protected filterAgencyAccept(keyword) {
    this.searchSectorPage = 0;
    this.isFullListSearchSector = false;
    if (!this.agencyAccept) {
      return;
    }
    let search = keyword;
    if (!search) {
      this.filteredAgencyAccept.next(this.agencyAccept.slice());
      return;
    } else {
      search = search.toLowerCase().trim();
    }
    this.searchAgency = search;
      const userAgency = JSON.parse(localStorage.getItem('userAgency'));
      let rootAgencyId: any = '';
      if (userAgency !== null) {
        rootAgencyId = userAgency.id;
      } else {
        rootAgencyId = this.config.rootAgency.id;
      }
      let urlString = '';
      if (this.env.rootAgency !== null && this.env.rootAgency !== '' && this.env.rootAgency !== undefined)
      {
        urlString = '&tag-id=0000591c4e1bd312a6f00003&ancestor-id=' + this.env.rootAgency.id;
      }
      // tslint:disable-next-line:max-line-length
      this.procedureService.getListAgencyWithParent('name+code/--full?page=' + 0 + '&keyword=' + encodeURIComponent(search) + '&size=50&related-token=true&sort=name.name,asc&status=1' + urlString).subscribe(data => {
        this.searchAgency = search;
        this.searchAgencyPage = 1;
        this.isFullListSearchAgency = data.last;
        for (let i = 0; i < data.numberOfElements; i++) {
          if (!this.listAgency.filter(agency => agency.id === data.content[i].id)[0]) {
            const arrData: any = {};
            arrData.id = data.content[i].id;
            arrData.code = data.content[i].code;
            // tslint:disable-next-line: prefer-for-of
            for (let n = 0; n < data.content[i].name.length; n++) {
              if (data.content[i].name[n].languageId === Number(localStorage.getItem('languageId'))) {
                arrData.name = data.content[i].name[n].name;
              }
            }
            this.listAgency.push(arrData);
          }
        }
        this.listAgency = JSON.parse(JSON.stringify(this.listAgency).replace(/null/g, '""'));
        this.agencyAccept = this.listAgency;
        this.filteredAgencyAccept.next(
          this.agencyAccept.filter(agency => (agency.name.toLowerCase().indexOf(search) > -1))
        );
      }, err => {
        console.log(err);
      });
    // }
  }

  getListReceiptBook() {
    console.log('dsfdsfssssssssssss');
    console.log('keywork=' + this.searchSector);
    console.log('isFullListSector=' + this.isFullListSector);
    console.log('isFullListSearchSector=' + this.isFullListSearchSector);
    let page = this.listSectorPage;
    if (this.searchSector !== '') {
      page = this.searchSectorPage;
    }
    if ((this.searchSector === '' && this.isFullListSector !== true) || (this.searchSector !== '' && this.isFullListSearchSector !== true)) {
      this.receiptBookService.search('?page=' + page + '&keyword='
        + this.searchSector + '&size=50&sort=name.name,asc&status=1' + this.keySearchSector).subscribe(data => {
          if (this.searchSector !== '') {
            this.searchSectorPage++;
            this.isFullListSearchSector = data.last;
          }
          else {
            this.listSectorPage++;
            this.isFullListSector = data.last;
          }
          for (let i = 0; i < data.numberOfElements; i++) {
            if (!this.listChooseSector.filter(sector => sector.id === data.content[i].id)[0] &&
              this.listIdSectorAgency.indexOf(data.content[i].id) === -1) {
              this.listChooseSector.push(data.content[i]);
            }
          }
          this.filteredSectorAccept.next(
            this.listChooseSector.filter(agency => (agency.name.toLowerCase().indexOf(this.searchSector) > -1))
          );
        });
    }
  }

  check() {
    console.log(this.sectorAcceptFilterCtrl);
  }

  protected filterSectorAccept() {
    console.log(this.sectorAcceptFilterCtrl);
    if (!this.listChooseSector) {
      return;
    }
    let search = this.sectorAcceptFilterCtrl.value;
    if (!search || search === '') {
      this.filteredSectorAccept.next(this.listChooseSector.slice());
      return;
    } else {
      search = search.toLowerCase().trim();
    }
    this.searchSector = search;
    if (this.searchSector === '') {
      this.filteredSectorAccept.next(
        this.listChooseSector
      );
    }
    else {
      this.receiptBookService.search('?page=' + 0 + '&keyword='
        + search + '&size=50&sort=name.name,asc&status=1' + this.keySearchSector).subscribe(data => {
          this.searchSectorPage = 1;
          this.isFullListSearchSector = data.last;
          for (let i = 0; i < data.numberOfElements; i++) {
            if (!this.listChooseSector.filter(sector => sector.id === data.content[i].id)[0] &&
              this.listIdSectorAgency.indexOf(data.content[i].id) === -1) {
              this.listChooseSector.push(data.content[i]);
            }
          }
          this.filteredSectorAccept.next(
            this.listChooseSector.filter(agency => (agency.name.toLowerCase().indexOf(search) > -1))
          );
        });
    }
  }

  getListReceiptBookAgency(searchString) {
    this.receiptBookService.search(searchString).subscribe(data => {
      this.listChooseSectorAgency = [];
      for (let i = 0; i < data.content.length; i++) {
        this.listIdSectorAgency.push(data.content[i].id);
        this.listChooseSectorAgency.push(data.content[i]);
      }
      this.filteredSectorAgencyAccept.next(
        this.listChooseSectorAgency
      );
      this.sectorAgencyAcceptFilterCtrl.valueChanges.pipe().subscribe(() => {
        this.filterSectorAgencyAccept();
      });
    });
  }

  protected filterSectorAgencyAccept() {
    console.log(this.sectorAgencyAcceptFilterCtrl);
    if (!this.listChooseSectorAgency) {
      return;
    }
    let search = this.sectorAgencyAcceptFilterCtrl.value;
    if (!search || search === '') {
      this.filteredSectorAgencyAccept.next(this.listChooseSectorAgency.slice());
      return;
    } else {
      search = search.toLowerCase().trim();
    }
    this.searchSectorAgency = search;
    // set
    this.filteredSectorAgencyAccept.next(
      this.listChooseSectorAgency.filter(agency => (agency.name.toLowerCase().indexOf(search) > -1))
    );
  }

  addItemToList(id) {
    const items = this.listChooseSector.filter(sector => sector.id === id);
    console.log(items);
    if (items && items.length > 0) {
      this.listChooseSectorAgency.unshift(items[0]);
      this.listIdSectorAgency.unshift(items[0].id);
      this.filteredSectorAgencyAccept.next(
        this.listChooseSectorAgency
      );
      this.sectorAgencyAcceptFilterCtrl.setValue('');
      // delete
      this.listChooseSector = this.listChooseSector.filter(sector => sector.id !== id);
      this.filteredSectorAccept.next(
        this.listChooseSector.filter(agency => (agency.name.toLowerCase().indexOf(this.searchSector) > -1))
      );
    }
  }

  deleteItemToList(id) {
    const items = this.listChooseSectorAgency.filter(sector => sector.id === id);
    console.log(items);
    if (items && items.length > 0) {
      this.listChooseSector.unshift(items[0]);
      this.filteredSectorAccept.next(
        this.listChooseSector.filter(agency => (agency.name.toLowerCase().indexOf(this.searchSector) > -1))
      );
      this.listChooseSectorAgency = this.listChooseSectorAgency.filter(sector => sector.id !== id);
      this.listIdSectorAgency = this.listIdSectorAgency.filter(sector => sector !== id);
      this.filteredSectorAgencyAccept.next(
        this.listChooseSectorAgency.filter(agency => (agency.name.toLowerCase().indexOf(this.searchSectorAgency) > -1))
      );
    }
  }

  changeAgency() {
    const objectForm = this.searchForm.getRawValue();
    if (objectForm.agency !== '') {
      //this.keySearchSector = '&not-agency-id=' + objectForm.agency;
      this.keySearchSector = '';
      this.keySearchSectorAgency = '?agency=' + objectForm.agency;
      this.listChooseSector = [];
      this.listChooseSectorAgency = [];
      this.listIdSectorAgency = [];
      this.searchSector = '';
      this.searchSectorAgency = '';
      this.listSectorPage = 0;
      this.isFullListSector = false;
      this.isFullListSearchSector = false;
      this.searchSectorPage = 0;
      this.getListReceiptBookAgency(this.keySearchSectorAgency);
      this.getListReceiptBook();
      this.sectorAgencyAcceptFilterCtrl.setValue('');
      this.sectorAcceptFilterCtrl.setValue('');
      this.procedureService.getDetailAgencyFully(objectForm.agency).subscribe(data => {
        this.detailAgency = data;
      });

    }
    else {
      this.keySearchSector = '&all-agency=0';
      this.keySearchSectorAgency = '?all-agency=1';
      this.listChooseSector = [];
      this.listChooseSectorAgency = [];
      this.listIdSectorAgency = [];
      this.searchSector = '';
      this.searchSectorAgency = '';
      this.listSectorPage = 0;
      this.isFullListSector = false;
      this.isFullListSearchSector = false;
      this.searchSectorPage = 0;
      this.getListReceiptBookAgency(this.keySearchSectorAgency);
      this.getListReceiptBook();
      this.sectorAgencyAcceptFilterCtrl.setValue('');
      this.sectorAcceptFilterCtrl.setValue('');
    }
  }


  changeCode() {
    const objectForm = this.searchForm.getRawValue();
    this.searchForm.patchValue({
      code: objectForm.code.trim()
    });
  }

  onDismiss(): void {
    this.dialogRef.close();
  }

  save() {
    const objectForm = this.searchForm.getRawValue();
    if (objectForm.agency !== '') {
      const postObject = {
        agency: this.detailAgency,
        allAgency: 0,
        receiptBooks: this.listIdSectorAgency
      };
      this.putAgencySector(postObject);
    }
    else {
      const postObject = {
        agency: null,
        allAgency: 1,
        receiptBooks: this.listIdSectorAgency
      };
      this.putAgencySector(postObject);
    }
  }

  putAgencySector(postObject) {
    console.log(postObject);
    this.receiptBookService.updateReceiptBookAgency(postObject).subscribe({
      next: data => {
        if (data.affectedRows > 0) {
          const successMsg = {
            vi: 'Cập nhật sổ thành công',
            en: 'Successfully updated receipt book'
          };
          this.snackbarService.openSnackBar(
            1,
            successMsg[this.localeId],
            '',
            'success_notification',
            this.config.expiredTime
          );
    
          // Return the new item data
          this.dialogRef.close({
            status: true,
            data: postObject
          });
        }
        else {
          const errorMsg = {
            vi: 'Cật nhật sổ không thành công',
            en: 'Failed to update receipt book'
          };
          this.snackbarService.openSnackBar(
            0,
            errorMsg[this.localeId],
            '',
            'error_notification',
            this.config.expiredTime
          );
          this.dialogRef.close({
            status: false,
            data: postObject
          });          
        }
      },
      error: err => {
        const errorMsg = {
          vi: 'Cật nhật sổ không thành công',
          en: 'Failed to update receipt book'
        };
        this.snackbarService.openSnackBar(
          0,
          errorMsg[this.localeId],
          err?.error?.message || '',
          'error_notification',
          this.config.expiredTime
        );
        this.dialogRef.close({
          status: false,
          data: postObject
        });
      }
    });    
  }


  private getDetailSector(id) {
    // tslint:disable-next-line: prefer-const
    this.nameParent = [];
    this.sectorService.getDetailSector(id).subscribe(data => {
      this.nameParent = data.name;
    });
  }

  addSector(dataPost) {
    let nameReturn = dataPost.name[0].name;
    // tslint:disable-next-line: no-shadowed-variable
    dataPost.name.forEach(element => {
      if (Number(localStorage.getItem('languageId')) === element.languageId) {
        nameReturn = element.name;
      }
    });
    this.sectorService.postSector(dataPost).subscribe(data => {
      const result = {
        name: nameReturn,
        status: true
      };
      this.dialogRef.close(result);
    }, err => {
      const result = {
        name: nameReturn,
        status: false
      };
      this.dialogRef.close(result);
    });
  }


  getListProcedureLevel() {
    this.homeService.getListProcedureLevel().subscribe(data => {
      for (const level of data) {
        this.listProcedureLevel.push(level);
      }
    }, err => {
      console.log(err);
    });
  }

  private _filter(name: string): Agency[] {
    const filterValue = name.toString().toLowerCase();
    return this.listAgency.filter(option => option.name.toString().toLowerCase().includes(filterValue));
  }

  onEnter(event) {
        clearTimeout(this.timeOutGetAgencyList);
        this.timeOutGetAgencyList = setTimeout(async () => {
          this.searchAgencyKeyword = event.target.value;
          this.filterAgencyAccept(this.searchAgencyKeyword);
        }, 300);  
  }

  async resetSearchForm() {
        this.searchAgencyKeyword = '';
        this.filterAgencyAccept(this.searchAgencyKeyword);
  }
}

export class ConfirmReceiptBookAgencyModel {
  constructor() {
  }
}
