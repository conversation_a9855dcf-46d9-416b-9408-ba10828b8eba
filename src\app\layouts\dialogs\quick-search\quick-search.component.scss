.dialog_title {
    font-style: normal;
    font-weight: 500;
    font-size: 20px;
    color: #ce7a58;
}

.close-button {
    float: right;
    top: -24px;
    right: -24px;
}


::ng-deep .tbl_dialog_content::-webkit-scrollbar-track {
    box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.1);
    background-color: #f5f5f5;
}

::ng-deep .tbl_dialog_content::-webkit-scrollbar {
    width: 5px;
    background-color: #f5f5f5;
}

::ng-deep .tbl_dialog_content::-webkit-scrollbar-thumb {
    box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.1);
    background-color: #44444450;
}

.search-nested {
    width: 80% !important;
    height: 50px !important;
    margin-left: 15px !important;
}

.clear-search-nested {
    right: 0 !important;
    position: absolute !important;
    top: 0 !important;
}

::ng-deep {
    .quickSearchDialogContent {
        font-size: 15px;

        .searchForm {
            .mat-form-field-wrapper {
                padding-bottom: 2px;
            }

            .formFieldItems {
                flex-wrap: wrap;
            }
        }

        .edit {
            .mat-form-field.mat-focused .mat-form-field-label {
                color: #ce7a58;
            }
        
            .mat-form-field-appearance-outline .mat-form-field-outline {
                color: #ffffff !important;
                background-color: #eaebeb;
                border-radius: 5px;
            }
        
            .mat-form-field-appearance-outline.mat-focused .mat-form-field-outline-thick {
                color: #ce7a58 !important;
            }
        }

        .frm_Pagination {
            padding: unset !important;
            margin-top: 0.25em;
            margin-bottom: -1.25em;
        }

        .searchBtn {
            background-color: #ce7a58;
            color: #fff;
            height: 3em;
        }

        .resetBt {
            margin-bottom: 0.4em;
            background-color: #fafafa;
            color: #ce7a58;
            height: 3em ;
            border: 2px solid #ce7a58; /* Thêm viền màu */
            border-radius: 4px; /* Bo góc nhẹ (tuỳ chỉnh nếu cần) */
        }

        .tbl {
            .tbl_dialog_content {
                max-height: 55vh;
            }

            .mat-table {
                border-radius: 4px;
                border: 1px solid #ececec;
                width: 100%;
            }

            .mat-checkbox-indeterminate {
                &.mat-accent {
                    .mat-checkbox-background {
                        background-color: #ce7a58;
                    }
                }
            }

            .mat-checkbox-checked {
                &.mat-accent {
                    .mat-checkbox-background {
                        background-color: #ce7a58;
                    }
                }
            }

            .mat-header-row {
                background-color: #e8e8e8;
                min-height: 3.5em !important;

                .mat-header-cell {
                    color: #495057;
                    font-size: 14px;

                    p {
                        margin-bottom: 0;
                        font-weight: 400;
                        font-style: italic;
                    }
                }
            }

            .mat-row {
                border: none;

                &:nth-child(even) {
                    background-color: #fafafa;
                }

                &:nth-child(odd) {
                    background-color: #fff;
                }
            }

            .mat-cell {
                .disabled {
                    color: #ce7a58;
                    cursor: no-drop;
                }
            }

            .mat-column-code {
                flex: 0 0 10%;
                padding-right: 0.5em;
                
                .code {
                    color: blue;
                    font-weight: 500;
                    word-break: break-all;
                }
            }

            .mat-column-procedure {
                flex: 1 0 5%;
                padding-right: 0.5em;

                .procedure-code {
                    color: #ce7a58;
                }

                .procedure-name {
                    display: -webkit-box;
                    -webkit-line-clamp: 4;
                    -webkit-box-orient: vertical;
                    width: 100%;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }
            }

            .mat-column-timing {
                flex: 1 0 20%;
                padding-right: 0.5em;
            }

            .mat-column-applicant {
                flex: 0 0 12%;
                padding-right: 0.5em;
            }

            .mat-column-agency {
                flex: 0 0 12%;
                padding-right: 0.5em;
            }

            .mat-column-status {
                flex: 0 0 10%;
            }

            .mat-column-action {
                flex: 0 0 5%;
                float: right;
            }
        }
    }
}

@media screen and (max-width: 600px) {
    .tbl .mat-header-row {
        display: none;
    }

    .tbl .mat-table {
        border: 0;
        vertical-align: middle;
    }

    .tbl .mat-table caption {
        font-size: 1em;
    }

    .tbl .mat-table .mat-row {
        border-bottom: 5px solid #ddd;
        display: block;
        min-height: unset;
    }

    .tbl .mat-table .mat-cell {
        border-bottom: 1px solid #ddd;
        display: block;
        font-size: 14px;
        text-align: right;
        margin-bottom: 4%;
        padding: 0 0.5em;
    }

    .tbl .mat-table .mat-cell:before {
        content: attr(data-label);
        float: left;
        font-weight: 500;
        font-size: 14px;
    }

    .tbl .mat-table .mat-cell:last-child {
        border-bottom: 0;
    }

    .tbl .mat-table .mat-cell:first-child {
        margin-top: 4%;
    }

    ::ng-deep .tbl .mat-row:nth-child(even) {
        background-color: unset;
    }

    ::ng-deep .tbl .mat-row:nth-child(odd) {
        background-color: unset;
    }
}
