import { Injectable } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { ApiProviderService } from 'src/app/core/service/api-provider.service';
import { Observable } from 'rxjs';
import { SnackbarService } from 'data/service/snackbar/snackbar.service';
import { EnvService } from 'core/service/env.service';
import { Workbook } from 'exceljs';
import * as fs from 'file-saver';
import { each } from 'jquery';

const EXCEL_TYPE = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8';
const EXCEL_EXTENSION = '.xlsx';

@Injectable({
  providedIn: 'root'
})
export class QTIStatisticsService {
  language = localStorage.getItem('language') === 'vi' ? 228 : 46;
  config = this.envService.getConfig();

  constructor(
    private dialog: MatDialog,
    private http: HttpClient,
    private apiProviderService: ApiProviderService,
    private snackbar: SnackbarService,
    private envService: EnvService
  ) { }

  private basecatURL = this.apiProviderService.getUrl('digo', 'basecat');
  //  private basedataURL = this.apiProviderService.getUrl('digo', 'basedata');
  //  private basepadURL = this.apiProviderService.getUrl('digo', 'basepad');
    private padmanURL = this.apiProviderService.getUrl('digo', 'padman');
  // private padmanURL = "http://localhost:8081";//anpt
  // private basedataURL = "http://localhost:8888";
  // private basepadURL = "http://localhost:8069";
  private humanURL = this.apiProviderService.getUrl('digo', 'human')
 
  getDetailDossierTHSHHSQTI(search): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.padmanURL + '/qti-dossier-statistic/digitization-detail-THSHHS' + search, { headers }).pipe();
  }

  
  exportDossierStatisticDetailTHSHHSQTI(params: string): any {
    const url = this.padmanURL + '/qti-dossier-statistic/digitization-detail-THSHHS/--excel' + params;
    this.getFileExport(url).then();
  }
  getFileExport(url) {
    return new Promise((resolve) => {
      this.http.get(url, {
        observe: 'response',
        responseType: 'blob'
      }).toPromise().then(res => {
        const blob = new Blob([res.body], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
        let filename = 'bao_cao_chi_tiet_so_hoa.xlsx';
        if (res.headers.get('Content-Disposition') != null) {
          filename = res.headers.get('Content-Disposition').split(';')[1].split('=')[1].replace(/\"/g, '');
        }
        const blobUrl = URL.createObjectURL(blob);

        const xhr = new XMLHttpRequest();
        xhr.responseType = 'blob';

        xhr.onload = () => {
          const recoveredBlob = xhr.response;
          const reader = new FileReader();
          reader.onload = () => {
            const base64data = reader.result.toString();
            const object = JSON.stringify({
              mimeType: filename.substring(filename.lastIndexOf('.') + 1),
              name: filename.substring(0, filename.lastIndexOf('.')),
              data: base64data.split(',')[1]
            });
            const anchor = document.createElement('a');
            anchor.download = filename;
            anchor.href = base64data;
            anchor.click();
          };
          reader.readAsDataURL(recoveredBlob);
        };

        xhr.open('GET', blobUrl);
        xhr.send();
        resolve(true);
      }).catch(err => {
        if (err.status === 500) {
          const message = {
            vi: 'Hệ thống tạm thời không thể xuất file excel!',
            en: 'The system is temporarily unable to export the excel file!'
          };
          this.snackbar.openSnackBar(0, '', message[localStorage.getItem('language')], 'error_notification',
            this.config.expiredTime);
        }
        resolve(false);
      });
    });
  }

  //xuat excel
 public exportToExcel_bc_xuly_hoso(
        reportHeading: string,
        reportSubHeading: string,
        nameReport: string,
        subNameReport: string,
        json: any[],
        footerData: any[],
        excelFileName: string,
        sheetName: string
      ) {
        const data = json;
        // create workbook and worksheet
        const workbook = new Workbook();
        workbook.creator = 'Snippet Coder';
        workbook.lastModifiedBy = 'SnippetCoder';
        workbook.created = new Date();
        workbook.modified = new Date();
        const worksheet = workbook.addWorksheet(sheetName);
    
        worksheet.getColumn('A').font = {name: 'Times New Roman', size: 12};
        worksheet.getColumn('B').font = {name: 'Times New Roman', size: 12};
        worksheet.getColumn('C').font = {name: 'Times New Roman', size: 12};
        worksheet.getColumn('D').font = {name: 'Times New Roman', size: 12};
        worksheet.getColumn('E').font = {name: 'Times New Roman', size: 12};
        worksheet.getColumn('F').font = {name: 'Times New Roman', size: 12};
        worksheet.getColumn('G').font = {name: 'Times New Roman', size: 12};
        worksheet.getColumn('H').font = {name: 'Times New Roman', size: 12};
        worksheet.getColumn('I').font = {name: 'Times New Roman', size: 12};
        worksheet.getColumn('J').font = {name: 'Times New Roman', size: 12};
        worksheet.getColumn('K').font = {name: 'Times New Roman', size: 12};
        worksheet.getColumn('L').font = {name: 'Times New Roman', size: 12};
        worksheet.getColumn('M').font = {name: 'Times New Roman', size: 12};
    
        // NỘI DUNG TABLE-HEADER
    
        worksheet.mergeCells('A2:F2');
        worksheet.getCell('A2').value = reportHeading;
        worksheet.getCell('A2').alignment = {horizontal: 'center', vertical: 'middle'};
        worksheet.getCell('A2').font = {size: 15, bold: true, name: 'Times New Roman'};
    
        worksheet.mergeCells('A3:F3');
        worksheet.getCell('A3').value = reportSubHeading;
        worksheet.getCell('A3').alignment = {horizontal: 'center', vertical: 'middle'};
        worksheet.getCell('A3').font = {size: 12, italic: true, name: 'Times New Roman'};
    
    
       // worksheet.getCell('A5').value = 'STT';
        //worksheet.getCell('B5').value = 'Cơ quan';
    
    
        // worksheet.mergeCells('C5:E5');
        // worksheet.getCell('C5').value = 'Hồ sơ do cơ quan tiếp nhận liên thông giải quyết';
        // worksheet.mergeCells('F5:F5');
        // worksheet.getCell('F5').value = 'Hồ sơ do cơ quan tiếp nhận trực tiếp giải quyết';
    
    
        // worksheet.mergeCells('A5:A6');
        // worksheet.mergeCells('B5:B6');
        // worksheet.mergeCells('C5:C6');
        // worksheet.mergeCells('D5:D6');
        // worksheet.mergeCells('E5:E6');
        // worksheet.mergeCells('F5:F6');
    
        worksheet.getCell('A4').value = 'STT';
        worksheet.getCell('B4').value = 'Đơn vị';
        worksheet.getCell('C4').value = 'Số hồ sơ tiếp nhận';
        // worksheet.getCell('D4').value = '(4)';
        // worksheet.getCell('E4').value = '(5)';
        // worksheet.getCell('F4').value = '(6)';
        worksheet.getCell('G4').value = 'Số lượng hồ sơ đã giải quyết';
        // worksheet.getCell('H4').value = '(8)';
        // worksheet.getCell('I4').value = '(9)';
        // worksheet.getCell('J4').value = '(10)';
        worksheet.getCell('K4').value = 'Số lượng hồ sơ đang giải quyết';
        // worksheet.getCell('L4').value = '(12)';
        // worksheet.getCell('M4').value = '(13)';


       // worksheet.getCell('A5').value = '(1)';
     //   worksheet.getCell('B5').value = '(2)';
        worksheet.getCell('C5').value = 'Tổng số';
        worksheet.getCell('D5').value = 'Trong kỳ';
     //   worksheet.getCell('E5').value = '(5)';
        worksheet.getCell('F5').value = 'Từ kỳ trước';
        worksheet.getCell('G5').value = 'Tổng số';
        worksheet.getCell('H5').value = 'Trước hạn';
        worksheet.getCell('I5').value = 'Đúng hạn';
        worksheet.getCell('J5').value = 'Quá hạn';
        worksheet.getCell('K5').value = 'Tổng số';
        worksheet.getCell('L5').value = 'Trong hạn';
        worksheet.getCell('M5').value = 'Quá hạn';

      //  worksheet.getCell('A6').value = '(1)';
      //  worksheet.getCell('B6').value = '(2)';
      //  worksheet.getCell('C6').value = '(3)';
        worksheet.getCell('D6').value = 'Trực tuyến';
        worksheet.getCell('E6').value = 'Trực tiếp, dịch vụ bưu chính';
        // worksheet.getCell('F6').value = '(6)';
        // worksheet.getCell('G6').value = '(7)';
        // worksheet.getCell('H6').value = '(8)';
        // worksheet.getCell('I6').value = '(9)';
        // worksheet.getCell('J6').value = '(10)';
        // worksheet.getCell('K6').value = '(11)';
        // worksheet.getCell('L6').value = '(12)';
        // worksheet.getCell('M6').value = '(13)';
        worksheet.mergeCells('D5:E5')
        worksheet.mergeCells('A4:A6');
        worksheet.mergeCells('B4:B6');

        worksheet.mergeCells('C4:F4');
        worksheet.mergeCells('G4:J4');
        worksheet.mergeCells('K4:M4');

        worksheet.mergeCells('C5:C6');
        worksheet.mergeCells('F5:F6');
        worksheet.mergeCells('G5:G6');
        worksheet.mergeCells('H5:H6');
        worksheet.mergeCells('I5:I6');
        worksheet.mergeCells('J5:J6');
        worksheet.mergeCells('K5:K6');
        worksheet.mergeCells('L5:L6');
        worksheet.mergeCells('M5:M6');

        worksheet.getCell('A7').value = '(1)';
        worksheet.getCell('B7').value = '(2)';
        worksheet.getCell('C7').value = '(3) = (4)+(5)+(6)';
        worksheet.getCell('D7').value = '(4)';
        worksheet.getCell('E7').value = '(5)';
        worksheet.getCell('F7').value = '(6)';
        worksheet.getCell('G7').value = '(7) = (8)+(9)+(10)';
        worksheet.getCell('H7').value = '(8)';
        worksheet.getCell('I7').value = '(9)';
        worksheet.getCell('J7').value = '(10)';
        worksheet.getCell('K7').value = '(11) = (12)+(13)';
        worksheet.getCell('L7').value = '(12)';
        worksheet.getCell('M7').value = '(13)';
    
    
    
        worksheet.getColumn('B').width = 60;
        worksheet.getColumn('C').width = 20;
        worksheet.getColumn('D').width = 20;
        worksheet.getColumn('E').width = 20;
        worksheet.getColumn('F').width = 20;
        worksheet.getColumn('G').width = 20;
        worksheet.getColumn('H').width = 20;
        worksheet.getColumn('I').width = 20;
        worksheet.getColumn('J').width = 20;
        worksheet.getColumn('K').width = 20;
        worksheet.getColumn('L').width = 20;
        worksheet.getColumn('M').width = 20;


        worksheet.getColumn('C').alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
        worksheet.getColumn('D').alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
        worksheet.getColumn('E').alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
        worksheet.getColumn('F').alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
        worksheet.getColumn('G').alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
        worksheet.getColumn('H').alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
        worksheet.getColumn('I').alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
        worksheet.getColumn('J').alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
        worksheet.getColumn('K').alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
        worksheet.getColumn('L').alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
        worksheet.getColumn('M').alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};

    
        worksheet.properties.outlineLevelCol = 2;
        worksheet.properties.defaultRowHeight = 15;
    
         
        for(var r=1; r<14; r++)
        {
           var cell = worksheet.findCell(4, r);
            cell.font = {size: 13, bold: true, name: 'Times New Roman'};
            cell.border = {top: {style: 'thin'}, left: {style: 'thin'}, bottom: {style: 'thin'}, right: {style: 'thin'}};
            cell.alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
        }
      
        for(var r=1; r<14; r++)
        {
         var cell = worksheet.findCell(5, r);
          cell.font = {size: 13, bold: true, name: 'Times New Roman'};
          cell.border = {top: {style: 'thin'}, left: {style: 'thin'}, bottom: {style: 'thin'}, right: {style: 'thin'}};
          cell.alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
        }
    
        for(var r=1; r<14; r++)
        {
          var cell = worksheet.findCell(6, r);
          cell.font = {size: 13, bold: true, name: 'Times New Roman'};
          cell.border = {top: {style: 'thin'}, left: {style: 'thin'}, bottom: {style: 'thin'}, right: {style: 'thin'}};
          cell.alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
        }
    
        for(var r=1; r<14; r++)
        {
          var cell = worksheet.findCell(7, r);
          cell.font = {size: 13, bold: true, name: 'Times New Roman'};
          cell.border = {top: {style: 'thin'}, left: {style: 'thin'}, bottom: {style: 'thin'}, right: {style: 'thin'}};
          cell.alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
        }
    
        // get all columns from JSON
        let columnsArray: any[];
        for (const key in json) {
          if (json.hasOwnProperty(key)) {
            columnsArray = Object.keys(json[key]);
          }
        }
    
           // Add Data and Conditional Formatting
           data.forEach((element: any) => {
            const eachRow = [];
            columnsArray.forEach((column) => {
              eachRow.push(element[column]);
            });
           // eachRow.splice(0, 1);
            const borderrow = worksheet.addRow(eachRow);
            borderrow.eachCell((cell) => {
              cell.border = {top: {style: 'thin'}, left: {style: 'thin'}, bottom: {style: 'thin'}, right: {style: 'thin'}};
              cell.alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
            });
          });
    
    
    
    
    
        // footer data row
        if (footerData != null) {
          footerData.forEach((element: any) => {
            const eachRow = [];
            element.forEach((val: any) => {
              eachRow.push(val);
            });
            const footerRow = worksheet.addRow(eachRow);
            const cellMerge = 'A' + footerRow.number + ':B' + footerRow.number;
            worksheet.mergeCells(cellMerge);
            footerRow.eachCell((cell) => {
              cell.font = {size: 13, bold: true, name: 'Times New Roman'};
              cell.border = {top: {style: 'thin'}, left: {style: 'thin'}, bottom: {style: 'thin'}, right: {style: 'thin'}};
              cell.alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
            });
          });
        }
    
    
        // Save Excel File
        workbook.xlsx.writeBuffer().then((dataBuffer: ArrayBuffer) => {
          const blob = new Blob([dataBuffer], {type: EXCEL_TYPE});
          fs.saveAs(blob, excelFileName + EXCEL_EXTENSION);
        });
      }
      getListAccountUser(searchString): Observable<any> {
        let headers = new HttpHeaders();
      //  let header = "http://localhost:8081/user";
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        return this.http.get(this.humanURL + '/user/--report-account-user' + searchString, { headers });
      }
      getListAccountUserExport(searchString): Observable<any> {
        let headers = new HttpHeaders();
      //  let header = "http://localhost:8081/user";
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        return this.http.get(this.humanURL + '/user/--report-account-user-export' + searchString, { headers });
      }
    
      
    // end xuat excel


      //xuat excel
 public exportToExcel_tthc_theocap(
  reportHeading: string,
  reportSubHeading: string,
  nameReport: string,
  subNameReport: string,
  json: any[],
  footerData: any[],
  excelFileName: string,
  sheetName: string
) {
  const data = json;
  // create workbook and worksheet
  const workbook = new Workbook();
  workbook.creator = 'Snippet Coder';
  workbook.lastModifiedBy = 'SnippetCoder';
  workbook.created = new Date();
  workbook.modified = new Date();
  const worksheet = workbook.addWorksheet(sheetName);

  worksheet.getColumn('A').font = {name: 'Times New Roman', size: 12};
  worksheet.getColumn('B').font = {name: 'Times New Roman', size: 12};
  worksheet.getColumn('C').font = {name: 'Times New Roman', size: 12};
  worksheet.getColumn('D').font = {name: 'Times New Roman', size: 12};
  worksheet.getColumn('E').font = {name: 'Times New Roman', size: 12};
  worksheet.getColumn('F').font = {name: 'Times New Roman', size: 12};
  worksheet.getColumn('G').font = {name: 'Times New Roman', size: 12};
  worksheet.getColumn('H').font = {name: 'Times New Roman', size: 12};
  worksheet.getColumn('I').font = {name: 'Times New Roman', size: 12};
  worksheet.getColumn('J').font = {name: 'Times New Roman', size: 12};
  worksheet.getColumn('K').font = {name: 'Times New Roman', size: 12};
  worksheet.getColumn('L').font = {name: 'Times New Roman', size: 12};
  worksheet.getColumn('M').font = {name: 'Times New Roman', size: 12};
  worksheet.getColumn('N').font = {name: 'Times New Roman', size: 12};

  // NỘI DUNG TABLE-HEADER

  worksheet.mergeCells('A2:F2');
  worksheet.getCell('A2').value = reportHeading;
  worksheet.getCell('A2').alignment = {horizontal: 'center', vertical: 'middle'};
  worksheet.getCell('A2').font = {size: 15, bold: true, name: 'Times New Roman'};

  worksheet.mergeCells('A3:F3');
  worksheet.getCell('A3').value = reportSubHeading;
  worksheet.getCell('A3').alignment = {horizontal: 'center', vertical: 'middle'};
  worksheet.getCell('A3').font = {size: 12, italic: true, name: 'Times New Roman'};


 // worksheet.getCell('A5').value = 'STT';
  //worksheet.getCell('B5').value = 'Cơ quan';


  // worksheet.mergeCells('C5:E5');
  // worksheet.getCell('C5').value = 'Hồ sơ do cơ quan tiếp nhận liên thông giải quyết';
  // worksheet.mergeCells('F5:F5');
  // worksheet.getCell('F5').value = 'Hồ sơ do cơ quan tiếp nhận trực tiếp giải quyết';


  // worksheet.mergeCells('A5:A6');
  // worksheet.mergeCells('B5:B6');
  // worksheet.mergeCells('C5:C6');
  // worksheet.mergeCells('D5:D6');
  // worksheet.mergeCells('E5:E6');
  // worksheet.mergeCells('F5:F6');

  worksheet.getCell('A4').value = 'STT';
  worksheet.getCell('B4').value = 'Đơn vị';
  worksheet.getCell('C4').value = 'Số hồ sơ tiếp nhận';
  // worksheet.getCell('D4').value = '(4)';
  // worksheet.getCell('E4').value = '(5)';
  // worksheet.getCell('F4').value = '(6)';
  worksheet.getCell('G4').value = 'Số lượng hồ sơ đã giải quyết';
  // worksheet.getCell('H4').value = '(8)';
  // worksheet.getCell('I4').value = '(9)';
  // worksheet.getCell('J4').value = '(10)';
  worksheet.getCell('K4').value = 'Số lượng hồ sơ đang giải quyết';
  // worksheet.getCell('L4').value = '(12)';
  // worksheet.getCell('M4').value = '(13)';
  worksheet.getCell('N4').value = 'Tỷ lệ giải quyết đúng hạn';


 // worksheet.getCell('A5').value = '(1)';
//   worksheet.getCell('B5').value = '(2)';
  worksheet.getCell('C5').value = 'Tổng số';
  worksheet.getCell('D5').value = 'Trong kỳ';
//   worksheet.getCell('E5').value = '(5)';
  worksheet.getCell('F5').value = 'Từ kỳ trước';
  worksheet.getCell('G5').value = 'Tổng số';
  worksheet.getCell('H5').value = 'Trước hạn';
  worksheet.getCell('I5').value = 'Đúng hạn';
  worksheet.getCell('J5').value = 'Quá hạn';
  worksheet.getCell('K5').value = 'Tổng số';
  worksheet.getCell('L5').value = 'Trong hạn';
  worksheet.getCell('M5').value = 'Quá hạn';
 // worksheet.getCell('N5').value = 'Quá hạn';

//  worksheet.getCell('A6').value = '(1)';
//  worksheet.getCell('B6').value = '(2)';
//  worksheet.getCell('C6').value = '(3)';
  worksheet.getCell('D6').value = 'Trực tuyến';
  worksheet.getCell('E6').value = 'Trực tiếp, dịch vụ bưu chính';
  // worksheet.getCell('F6').value = '(6)';
  // worksheet.getCell('G6').value = '(7)';
  // worksheet.getCell('H6').value = '(8)';
  // worksheet.getCell('I6').value = '(9)';
  // worksheet.getCell('J6').value = '(10)';
  // worksheet.getCell('K6').value = '(11)';
  // worksheet.getCell('L6').value = '(12)';
  // worksheet.getCell('M6').value = '(13)';
  //worksheet.getCell('N6').value = '(14)';

  worksheet.mergeCells('D5:E5')
  worksheet.mergeCells('A4:A6');
  worksheet.mergeCells('B4:B6');

  worksheet.mergeCells('C4:F4');
  worksheet.mergeCells('G4:J4');
  worksheet.mergeCells('K4:M4');

  worksheet.mergeCells('C5:C6');
  worksheet.mergeCells('F5:F6');
  worksheet.mergeCells('G5:G6');
  worksheet.mergeCells('H5:H6');
  worksheet.mergeCells('I5:I6');
  worksheet.mergeCells('J5:J6');
  worksheet.mergeCells('K5:K6');
  worksheet.mergeCells('L5:L6');
  worksheet.mergeCells('M5:M6');
  worksheet.mergeCells('N4:N6');

  worksheet.getCell('A7').value = '(1)';
  worksheet.getCell('B7').value = '(2)';
  worksheet.getCell('C7').value = '(3) = (4)+(5)+(6)';
  worksheet.getCell('D7').value = '(4)';
  worksheet.getCell('E7').value = '(5)';
  worksheet.getCell('F7').value = '(6)';
  worksheet.getCell('G7').value = '(7) = (8)+(9)+(10)';
  worksheet.getCell('H7').value = '(8)';
  worksheet.getCell('I7').value = '(9)';
  worksheet.getCell('J7').value = '(10)';
  worksheet.getCell('K7').value = '(11) = (12)+(13)';
  worksheet.getCell('L7').value = '(12)';
  worksheet.getCell('M7').value = '(13)';
  worksheet.getCell('N7').value = '(14= ((8)+(9)) / (7))';



  worksheet.getColumn('B').width = 60;
  worksheet.getColumn('C').width = 20;
  worksheet.getColumn('D').width = 20;
  worksheet.getColumn('E').width = 20;
  worksheet.getColumn('F').width = 20;
  worksheet.getColumn('G').width = 20;
  worksheet.getColumn('H').width = 20;
  worksheet.getColumn('I').width = 20;
  worksheet.getColumn('J').width = 20;
  worksheet.getColumn('K').width = 20;
  worksheet.getColumn('L').width = 20;
  worksheet.getColumn('M').width = 20;
  worksheet.getColumn('N').width = 20;


  worksheet.getColumn('C').alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
  worksheet.getColumn('D').alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
  worksheet.getColumn('E').alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
  worksheet.getColumn('F').alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
  worksheet.getColumn('G').alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
  worksheet.getColumn('H').alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
  worksheet.getColumn('I').alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
  worksheet.getColumn('J').alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
  worksheet.getColumn('K').alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
  worksheet.getColumn('L').alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
  worksheet.getColumn('M').alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
  worksheet.getColumn('N').alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};

  worksheet.properties.outlineLevelCol = 2;
  worksheet.properties.defaultRowHeight = 15;

   
  for(var r=1; r<15; r++)
  {
     var cell = worksheet.findCell(4, r);
      cell.font = {size: 13, bold: true, name: 'Times New Roman'};
      cell.border = {top: {style: 'thin'}, left: {style: 'thin'}, bottom: {style: 'thin'}, right: {style: 'thin'}};
      cell.alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
  }

  for(var r=1; r<15; r++)
  {
   var cell = worksheet.findCell(5, r);
    cell.font = {size: 13, bold: true, name: 'Times New Roman'};
    cell.border = {top: {style: 'thin'}, left: {style: 'thin'}, bottom: {style: 'thin'}, right: {style: 'thin'}};
    cell.alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
  }

  for(var r=1; r<15; r++)
  {
    var cell = worksheet.findCell(6, r);
    cell.font = {size: 13, bold: true, name: 'Times New Roman'};
    cell.border = {top: {style: 'thin'}, left: {style: 'thin'}, bottom: {style: 'thin'}, right: {style: 'thin'}};
    cell.alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
  }

  for(var r=1; r<15; r++)
  {
    var cell = worksheet.findCell(7, r);
    cell.font = {size: 13, bold: true, name: 'Times New Roman'};
    cell.border = {top: {style: 'thin'}, left: {style: 'thin'}, bottom: {style: 'thin'}, right: {style: 'thin'}};
    cell.alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
  }

  
  // get all columns from JSON
  let columnsArray: any[];
  for (const key in json) {
    if (json.hasOwnProperty(key)) {
      columnsArray = Object.keys(json[key]);
    }
  }

     // Add Data and Conditional Formatting
     data.forEach((element: any) => {
      const eachRow = [];
      columnsArray.forEach((column) => {
        eachRow.push(element[column]);
      });
     // eachRow.splice(0, 1);
      const borderrow = worksheet.addRow(eachRow);
      borderrow.eachCell((cell) => {
        cell.border = {top: {style: 'thin'}, left: {style: 'thin'}, bottom: {style: 'thin'}, right: {style: 'thin'}};
        cell.alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
      });
    });





  // footer data row
  if (footerData != null) {
    footerData.forEach((element: any) => {
      const eachRow = [];
      element.forEach((val: any) => {
        eachRow.push(val);
      });
      const footerRow = worksheet.addRow(eachRow);
      const cellMerge = 'A' + footerRow.number + ':B' + footerRow.number;
      worksheet.mergeCells(cellMerge);
      footerRow.eachCell((cell) => {
        cell.font = {size: 13, bold: true, name: 'Times New Roman'};
        cell.border = {top: {style: 'thin'}, left: {style: 'thin'}, bottom: {style: 'thin'}, right: {style: 'thin'}};
        cell.alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
      });
    });
  }


  // Save Excel File
  workbook.xlsx.writeBuffer().then((dataBuffer: ArrayBuffer) => {
    const blob = new Blob([dataBuffer], {type: EXCEL_TYPE});
    fs.saveAs(blob, excelFileName + EXCEL_EXTENSION);
  });
}

// end xuat excel
}
