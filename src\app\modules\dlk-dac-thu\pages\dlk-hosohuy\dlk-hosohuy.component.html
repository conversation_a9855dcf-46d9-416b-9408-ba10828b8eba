<h2><PERSON><PERSON> <PERSON><PERSON><PERSON> h<PERSON> sơ hủy</h2>
<div class="prc_searchbar">
  <div fxLayout="row" fxLayout.xs="row" fxLayout.sm="row" class="formFieldOutline" fxLayoutAlign="space-between">
    <mat-form-field appearance="outline" fxFlex.gt-sm="33" fxFlex.gt-xs="49.5" fxFlex='grow'>
      <mat-label><PERSON>h<PERSON><PERSON> từ khóa</mat-label>
      <input type="text" [(ngModel)]="paramsQuery.keyword" matInput>
    </mat-form-field>
    <mat-form-field appearance="outline" fxFlex.gt-sm="33" fxFlex.gt-xs="49.5" fxFlex='grow'>
      <mat-label>Đơn vị tiếp nhận</mat-label>
      <mat-select msInfiniteScroll (infiniteScroll)="getAgencyScroll()"
        [complete]="totalPagesAgencyAccept <= currentPageAgencyAccept+1" [(ngModel)]="paramsQuery.agency"
        (selectionChange)="changeAgencyAccept()">
        <mat-option>
          <ngx-mat-select-search ngModel (ngModelChange)="searchAngency($event)" placeholderLabel="">
          </ngx-mat-select-search>
        </mat-option>
        <mat-option value="">Tất cả</mat-option>
        <mat-option *ngFor="let item of listAgencyAccept" [value]="item.id">{{item.name}}</mat-option>
      </mat-select>
    </mat-form-field>
    <mat-form-field appearance="outline" fxFlex.gt-sm="33" fxFlex.gt-xs="49.5" fxFlex='grow'>
      <mat-label>Lĩnh vực</mat-label>
      <mat-select msInfiniteScroll (infiniteScroll)="getListSectorScroll()"
        [complete]="totalPagesSector <= currentPageSector+1" [(ngModel)]="paramsQuery.sector" (selectionChange)="sectorChange()">
        <mat-option>
          <ngx-mat-select-search ngModel (ngModelChange)="searchSector($event)" placeholderLabel="">
          </ngx-mat-select-search>
        </mat-option>
        <mat-option value="">Tất cả</mat-option>
        <mat-option *ngFor="let item of listSectorfillter" [value]="item.id">{{item.name}}</mat-option>
      </mat-select>
    </mat-form-field>
  </div>
  <div fxLayout="row" fxLayout.xs="row" fxLayout.sm="row" class="formFieldOutline" fxLayoutAlign="space-between">
    <mat-form-field appearance="outline" fxFlex.gt-sm="33" fxFlex.gt-xs="49.5" fxFlex='grow'>
      <mat-label>Thủ tục</mat-label>
      <mat-select msInfiniteScroll (infiniteScroll)="getListProcedureScroll()" [complete]="totalPagesProcedure <= currentPageProcedure+1" [(ngModel)]="paramsQuery.procedure">
        <mat-option>
          <ngx-mat-select-search ngModel (ngModelChange)="searchProvedure($event)" placeholderLabel="">
          </ngx-mat-select-search>
        </mat-option>
        <mat-option value="">Tất cả</mat-option>
        <mat-option *ngFor="let item of listProcedurefillter"
                    [value]="item.id">{{item.name}}</mat-option>
      </mat-select>
    </mat-form-field>
    <div appearance="outline" fxFlex.gt-sm="33" fxFlex.gt-xs="49.5" fxFlex='grow'>
      <div fxLayout="row" fxLayout.xs="row" fxLayout.sm="row" class="formFieldOutline" fxLayoutAlign="space-between">
        <mat-form-field appearance="outline" fxFlex.gt-sm="49.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
          <mat-label>Tiếp nhận từ ngày</mat-label>
          <input matInput [matDatepicker]="pickerAcceptFrom" [(ngModel)]="startDate">
          <mat-datepicker-toggle matSuffix [for]="pickerAcceptFrom"></mat-datepicker-toggle>
          <mat-datepicker #pickerAcceptFrom></mat-datepicker>
        </mat-form-field>
        <mat-form-field appearance="outline" fxFlex.gt-sm="49.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
          <mat-label>Tiếp nhận đến ngày</mat-label>
          <input matInput [matDatepicker]="pickerAcceptTo" [(ngModel)]="endDate">
          <mat-datepicker-toggle matSuffix [for]="pickerAcceptTo"></mat-datepicker-toggle>
          <mat-datepicker #pickerAcceptTo></mat-datepicker>
        </mat-form-field>
      </div>
    </div>
    <div appearance="outline" fxFlex.gt-sm="33" fxFlex.gt-xs="49.5" fxFlex='grow'>
      <mat-form-field appearance="outline"
                      fxFlex='grow'>
        <mat-label>Loại trạng thái hồ sơ</mat-label>
        <mat-select [(ngModel)]="paramsQuery.dossierStatus" (selectionChange)="onChangeStatus($event)">
          <mat-option *ngFor="let item of listStatus"
                      [value]="item.value.toString()">
            {{item.text}}
          </mat-option>
        </mat-select>
        <!-- <mat-select msInfiniteScroll
                    (infiniteScroll)="getListSectorScroll()"
                    [complete]="totalPagesSector <= currentPageSector+1"
                    [(ngModel)]="paramsQuery.sector"
                    (selectionChange)="sectorChange()">
          <mat-option *ngFor="let item of listSectorfillter"
                      [value]="item.id">{{item.name}}</mat-option>
        </mat-select> -->
      </mat-form-field>
    </div>
  </div>

  <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="row" fxLayoutAlign="end" style="padding-bottom: 1em;">
    <button mat-flat-button fxFlex.lt-md="22" fxFlex.md="20" fxFlex.gt-md="12" fxFlex.gt-xs="49.5" fxFlex='grow'
      class="btn-search" type="submit" (click)="thongKe()" style="background-color: #ce7a58;color: white;">
      <mat-icon class="iconStatistical">bar_chart</mat-icon><span>Thống kê</span>
    </button>
    <div fxFlex='1'></div>
    <button mat-flat-button fxFlex.lt-md="22" fxFlex.md="20" fxFlex.gt-md="12" fxFlex.gt-xs="49.5" fxFlex='grow'
      class="btn-download-excel" style="background-color: #127905;color: white;" (click)="xuatExcel()">
      <mat-icon class="iconStatistical">cloud_download</mat-icon>
      <span> Xuất excel</span>
      <mat-progress-bar mode="indeterminate" *ngIf="waitingDownloadExcel"></mat-progress-bar>
    </button>
    <div fxFlex='1'></div>


    <!-- <div fxFlex='1'></div>
      <button mat-flat-button fxFlex.lt-md="22" fxFlex.md="20" fxFlex.gt-md="12" fxFlex.gt-xs="49.5" fxFlex='grow'
        class="btn-download-excel" [disabled]="waitingDownloadExcel">
        <mat-icon class="iconStatistical">cloud_download</mat-icon>
        <span>Xuất excel</span>
        <mat-progress-bar mode="indeterminate" *ngIf="waitingDownloadExcel"></mat-progress-bar>
      </button>
      <div fxFlex='1'></div>
      <button mat-flat-button fxFlex.lt-md="22" fxFlex.md="20" fxFlex.gt-md="12" fxFlex.gt-xs="49.5" fxFlex='grow'
        class="btn-print" [disabled]="waitingDownloadPrint">
        <mat-icon class="iconStatistical">print</mat-icon>
        <span>In danh sách</span>
        <mat-progress-bar mode="indeterminate" *ngIf="waitingDownloadPrint"></mat-progress-bar>
      </button> -->
  </div>
</div>

<div fxLayout="row" fxLayoutAlign="center">
  <div class="frm_main" fxFlex="grow">
    <div class="logbookTbl">
      <div class="logbookOnlyTbl">
        <table class="table">
          <tr>
            <th>STT</th>
            <th>Số hồ sơ</th>
            <th>Về việc</th>
            <th>Người đăng ký</th>
            <th>Ngày tiếp nhận</th>
            <th>Ngày {{isCancelStatus ? " hủy" : " dừng xử lý"}}</th>
            <th>Cán bộ thực hiện</th>
            <th>Lý do</th>
            <th>Lĩnh vực</th>
            <th>Bộ phận</th>
          </tr>
          <tr *ngFor="let item of listHoSo;;let i = index ">
            <td>{{ paramsQuery.page*paramsQuery.size + i+1}}</td>
            <td>{{item.code}}</td>
            <!-- <td>{{item.procedure?.code}} - {{item.procedureName}}</td> -->
            <td>{{item.procedureCode}} - {{item?.procedureName}}</td>
            <td>
              <span style="font-weight: bold;">{{getFullName(item.applicant?.data)}}</span> <br>
              <span>{{generateAddress(item.applicant?.data)}}</span>
            </td>
            <td>
              <span *ngIf="item.appliedDate != undefined">
                {{item?.acceptedDate | date:'dd/MM/yyyy HH:mm:ss'}}
              </span>
            </td>
            <!-- Thời gian xử lý -->
            <td *ngIf="isCancelStatus; else ngayDungXL"><span>{{getCancelInfo(item, 1)}}</span></td>
            <ng-template #ngayDungXL>
              <td><span>{{getPauseInfo(item, 1)}}</span></td>
            </ng-template>

            <!-- Người xử lý -->
            <td *ngIf="isCancelStatus; else userDungXL">{{getCancelInfo(item, 2)}}</td>
            <ng-template #userDungXL>
              <td><span>{{getPauseInfo(item, 2)}}</span></td>
            </ng-template>

            <!-- Nội dung xử lý -->
            <td *ngIf="isCancelStatus; else ndDungXL" [innerHTML]="getCancelInfo(item, 3)"></td>
            <ng-template #ndDungXL>
              <td [innerHTML]="getPauseInfo(item, 3)"></td>
            </ng-template>

            <td>{{item?.sectorName}}</td>
            <td>
              <span *ngIf="item?.agencyNameCurrentTask"> {{item?.agencyNameCurrentTask}}</span>
            </td>
          </tr>
        </table>
        <div class="frm_Pagination">
          <ul class="temp_Arr">
            <li
              *ngFor="let item of listHoSo  | paginate: {itemsPerPage: paramsQuery.size, currentPage: page, totalItems: countResult, id: 'pgnx'}">
            </li>
          </ul>
          <div class="pageSize">
            <span i18n>Hiển thị </span>
            <mat-form-field appearance="outline">
              <mat-select [(ngModel)]="paramsQuery.size" (valueChange)="paginate(0)">
                <mat-option *ngFor='let opt of pgSizeOptions;' [value]="opt">{{opt}}</mat-option>
              </mat-select>
            </mat-form-field>
            <span><span i18n>trên</span> {{countResult}} <span i18n>bản ghi</span></span>
          </div>
          <div class="control">
            <pagination-controls id="pgnx" (pageChange)="page = $event; paginate(page - 1)" responsive="true"
              previousLabel="" nextLabel="">
            </pagination-controls>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>