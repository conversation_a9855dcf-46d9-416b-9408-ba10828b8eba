import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { ApiProviderService } from 'src/app/core/service/api-provider.service';
import { Observable } from 'rxjs';


@Injectable({
  providedIn: 'root'
})
export class SurfeedService {

  constructor(
    private http: HttpClient,
    private apiProviderService: ApiProviderService
  ) { }

  private surfeed = this.apiProviderService.getUrl('digo', 'surfeed');
  private basecat = this.apiProviderService.getUrl('digo', 'basecat');
  private surfeedGuidePath = this.apiProviderService.getUrl('digo', 'surfeed') + '/guide/';
  private surfeedAnnouncementPath = this.apiProviderService.getUrl('digo', 'surfeed') + '/announcement/';

  getListGuide(tagId): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.append('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.surfeed + '/guide?spec=page&tag-id=' + tagId, { headers });
  }

  getListGuideList(searchString): Observable<any>{
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.surfeedGuidePath + searchString, { headers });
  }

  getListStaffGuideList(searchString): Observable<any>{
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.surfeedGuidePath+ '--staff-guide' + searchString + '&officerGuide=1', { headers });    
    
  }

  getDetailGuide(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.append('Content-Type', 'application/json');
    return this.http.get(this.surfeed + '/guide/' + id, { headers });  
  }

  deleteGuide(id) {
    return this.http.delete<any>(this.surfeedGuidePath + id).pipe();
  }

  updateGuide(search, requestBody) {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');     
    return this.http.put<any>(this.surfeedGuidePath + '--update-guide/'+ search, requestBody, { headers });    
  }

  createGuide(requestBody) {
    try{      
      let headers = new HttpHeaders();
      headers = headers.set('Content-Type', 'application/json');
      return this.http.post<any>(this.surfeedGuidePath + '--create', requestBody, { headers });       
    }catch(e){
      console.log(e)
    }   
  }

  getAgencyTag(id): Observable<any> {   
    try{      
      let headers = new HttpHeaders();
      headers = headers.set('Accept-Language', localStorage.getItem('language'));      
      return this.http.get(this.basecat + '/tag/' + id, { headers }).pipe();
    }catch(e){
      console.log(e)
    } 
  }  

  getListSurveyOfficer(searchString): Observable<any>{
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.surfeed+ '/statistic-hcm/officer' + searchString, { headers });  
  }

  getListSurveyOfficerDetail(searchString): Observable<any>{
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.surfeed+ '/statistic-hcm/--crieria-officer' + searchString, { headers });  
  }

  getListSurveyOfficerDetailExcel(searchString): Observable<any>{
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.surfeed+ '/statistic-hcm/--crieria-officer-no-page' + searchString, { headers });  
  }

  getListSurveySumPoint(searchString): Observable<any>{
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.surfeed+ '/statistic-hcm/--sum-point-officer' + searchString, { headers });  
  }

  getListSurveySumPointExcel(searchString): Observable<any>{
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.surfeed+ '/statistic-hcm/--sum-point-officer-no-page' + searchString, { headers });  
  }

  getListSurveyRating(searchString): Observable<any>{
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.surfeed+ '/statistic-hcm/--rating-officer' + searchString, { headers });  
  }

  getListSurveyDossier(searchString): Observable<any>{
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.surfeed+ '/statistic-hcm/--dossier-officer' + searchString, { headers });  
  }

  getListSurveyAgency(searchString): Observable<any>{
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.surfeed+ '/statistic-hcm/agency' + searchString, { headers });  
  }
  
  getListSurveyAgencyDetail(searchString): Observable<any>{
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.surfeed+ '/statistic-hcm/--crieria-agency' + searchString, { headers });  
  }

  getListSurveyRatingAgency(searchString): Observable<any>{
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.surfeed+ '/statistic-hcm/--rating-agency' + searchString, { headers });  
  }

  getListSurveyDossierAgency(searchString): Observable<any>{
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.surfeed+ '/statistic-hcm/--dossier-agency' + searchString, { headers });  
  }

  createAnnouncement(requestBody) {
    try{
      let headers = new HttpHeaders();
      headers = headers.set('Content-Type', 'application/json');
      return this.http.post<any>(this.surfeedAnnouncementPath, requestBody, { headers });
    }catch(err){
      console.log(err)
    }
  }

  updateAnnouncement(id, requestBody) {
    try{
      let headers = new HttpHeaders();
      headers = headers.set('Content-Type', 'application/json');     
      return this.http.put<any>(this.surfeedAnnouncementPath + '--update-announcement/'+ id, requestBody, { headers });    
    }catch(err){
      console.log(err)
    }
  }

  deleteAnnouncement(id) {
    return this.http.delete<any>(this.surfeedAnnouncementPath + id).pipe();
  }

  getListAnnouncement(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.surfeedAnnouncementPath + '--list' + searchString, { headers });    
  }

  markSeenAnnouncement(announcementId, userId) {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');     
    return this.http.put<any>(this.surfeedAnnouncementPath + '--mark-seen/' + announcementId, userId, { headers });    
  }

  markAllSeenAnnouncement(requestBody) {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');     
    return this.http.put<any>(this.surfeedAnnouncementPath + '--mark-all-seen/', requestBody, { headers });    
  }

  getReportRatingOfficerResultsAGG(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get( this.surfeed + '/rating-officer-results/--report-agg' + searchString, { headers });
  }
}
