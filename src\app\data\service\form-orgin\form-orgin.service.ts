import { Injectable } from '@angular/core';
import {HttpClient, HttpHeaders} from "@angular/common/http";
import {ApiProviderService} from "core/service/api-provider.service";
import {Observable} from "rxjs";
import { IFormOrginInFoInput } from 'shared/components/storage468/storage468.schema';

@Injectable({
  providedIn: 'root'
})
export class FormOrginService {
  private basepad = this.apiProviderService.getUrl('digo', 'basepad');
  private storage = this.apiProviderService.getUrl('digo', 'storage');
  private formOrginPath = `${this.storage}/form-origin`;
  constructor(private http: HttpClient,
              private apiProviderService: ApiProviderService) { }

  getPageFormOrgins(page?:number, size?:number, keyword?:string):Observable<any>{
    let URL = `${this.formOrginPath}/all?pagination=true&page=${page}&size=${size}`;
    if(keyword)
      URL += `&keyword=${keyword}`;
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(URL, { headers });
  }

  saveFileToStorage(req?: IFormOrginInFoInput):Observable<any>{
    if(req){
      let URL = `${this.storage}/form-origin-info`;
      let headers = new HttpHeaders();
      headers = headers.set('Content-Type', 'application/json');
      headers = headers.set('Accept-Language', localStorage.getItem('language'));
      return this.http.post(URL,req,{ headers });
    }else return null;
  }

  getAllFormOrginInfos(formOrginIds:string[], identityNumber:string, fullname:string):Observable<any>{
    let queryFormOrgins = formOrginIds.join('&form-orgin-id=');
    let URL = `${this.storage}/form-origin-info?identity-number=${identityNumber}&fullname=${fullname}&form-orgin-id=${queryFormOrgins}`;
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(URL, { headers });
  }

  asyncStorageToFileman(files):Observable<any>{
    const strFile = [].concat(files).join(',');
    let URL = `${this.storage}/file/--async-to-fileman`;
    URL += `?file-id=${strFile}`;
    let headers = new HttpHeaders();
    return this.http.post(URL,{}, { headers });
  }

  getFormOrginById(id:string):Promise<any>{
    return new Promise((resolve, rejects)=>{
      let URL = `${this.formOrginPath}/${id}`;
      let headers = new HttpHeaders();
      headers = headers.set('Accept-Language', localStorage.getItem('language'));
      this.http.get(URL, { headers }).subscribe(rs=>{
        resolve(rs);
      });
    });
  }
}
