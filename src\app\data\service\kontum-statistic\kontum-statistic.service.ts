import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { ApiProviderService } from 'src/app/core/service/api-provider.service';
import { EnvService } from 'src/app/core/service/env.service';
import { SnackbarService } from '../snackbar/snackbar.service';
import { Observable } from 'rxjs';
import { Workbook, Worksheet} from 'exceljs';
import * as fs from 'file-saver';
import { saveAs } from 'file-saver'

const EXCEL_TYPE = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8';
const EXCEL_EXTENSION = '.xlsx';
@Injectable({
  providedIn: 'root'
})
export class KontumStatisticService {

  constructor(
    private dialog: MatDialog,
    private http: HttpClient,
    private apiProviderService: ApiProviderService,
    private snackbar: SnackbarService,
    private envService: EnvService
  ) { }

  config = this.envService.getConfig();
  public baseExcelExportURL =
  this.apiProviderService.getUrl('digo', 'reporter') +
  '/dossiercounting/';

  public excelExportURL =
    this.apiProviderService.getUrl('digo', 'reporter') +
    '/dossiercounting/--export';
  private padmanURL = this.apiProviderService.getUrl('digo', 'padman');
  downloadExport(params: string){
    return this.http.get(this.excelExportURL  + params, { observe: 'response', responseType: 'blob' }).toPromise();
  }

  // Xuất file excel
  excelExport(params: string): Promise<any> {
    return new Promise((resolve) => {
      this.downloadExport(params).then(res => {
        const blob = new Blob([res.body], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
        //let filename = res.headers.get('content-disposition').split(';')[1].split('=')[1].replace(/\"/g, '');
        let filename = "BC_CHI_TIET.xlsx"
        if(!!res.headers.get('content-disposition')){
          filename = res.headers.get('content-disposition').split(';')[1].split('=')[1].replace(/\"/g, '');
        }

        var blobUrl = URL.createObjectURL(blob);

        var xhr = new XMLHttpRequest;
        xhr.responseType = 'blob';

        xhr.onload = function() {
          var recoveredBlob = xhr.response;
          var reader = new FileReader;
          reader.onload = function() {
            var base64data = reader.result.toString();
            var object = JSON.stringify({
              mimeType:filename.substring(filename.lastIndexOf(".")+1),
              name:filename.substring(0,filename.lastIndexOf(".")),
              data:base64data.split(',')[1]
            });
            let anchor = document.createElement("a");
            anchor.download = filename;
            anchor.href = base64data;
            anchor.click();
          };
          reader.readAsDataURL(recoveredBlob);
        };

        xhr.open('GET', blobUrl);
        xhr.send();
        resolve(true);
      })
        .catch(err => {
          if (err.status === 500) {
            this.snackbar.openSnackBar(0, '', 'Hệ thống tạm thời không thể xuất file excel!', 'error_notification', this.config.expiredTime);
          }
          resolve(false)
        });
    });
  }

  public excelExportPaymentURL = this.apiProviderService.getUrl('digo','padman') +'/dossier-payment/--export-excel-list-dossier-payment'
  downloadExportPayment(params: string){
    return this.http.get(this.excelExportPaymentURL + params, { observe: 'response', responseType: 'blob' }).toPromise();
  }

  excelExportPayment(params: string): Promise<any> {
    return new Promise((resolve) => {
      this.downloadExportPayment(params).then(res => {
        const blob = new Blob([res.body], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
        //let filename = res.headers.get('content-disposition').split(';')[1].split('=')[1].replace(/\"/g, '');
        let filename = "BC_CHI_TIET.xlsx"
        if(!!res.headers.get('content-disposition')){
          filename = res.headers.get('content-disposition').split(';')[1].split('=')[1].replace(/\"/g, '');
        }

        var blobUrl = URL.createObjectURL(blob);

        var xhr = new XMLHttpRequest;
        xhr.responseType = 'blob';

        xhr.onload = function() {
          var recoveredBlob = xhr.response;
          var reader = new FileReader;
          reader.onload = function() {
            var base64data = reader.result.toString();
            var object = JSON.stringify({
              mimeType:filename.substring(filename.lastIndexOf(".")+1),
              name:filename.substring(0,filename.lastIndexOf(".")),
              data:base64data.split(',')[1]
            });
            let anchor = document.createElement("a");
            anchor.download = filename;
            anchor.href = base64data;
            anchor.click();
          };
          reader.readAsDataURL(recoveredBlob);
        };

        xhr.open('GET', blobUrl);
        xhr.send();
        resolve(true);
      })
        .catch(err => {
          if (err.status === 500) {
            this.snackbar.openSnackBar(0, '', 'Hệ thống tạm thời không thể xuất file excel!', 'error_notification', this.config.expiredTime);
          }
          resolve(false)
        });
    });
  }
  downloadExportDossierListDetail(params: string){
    return this.http.get(this.baseExcelExportURL + "--export-list-dossier-detail"  + params, { observe: 'response', responseType: 'blob' }).toPromise();
  }

  // Xuất file excel
  excelExportDossierListDetail(params: string): Promise<any> {
    return new Promise((resolve) => {
      this.downloadExportDossierListDetail(params).then(res => {
        const blob = new Blob([res.body], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
        // let filename = res.headers.get('content-disposition').split(';')[1].split('=')[1].replace(/\"/g, '');
        let filename = "BC_CHI_TIET.xlsx"
        if(!!res.headers.get('content-disposition')){
          filename = res.headers.get('content-disposition').split(';')[1].split('=')[1].replace(/\"/g, '');
        }
        var blobUrl = URL.createObjectURL(blob);

        var xhr = new XMLHttpRequest;
        xhr.responseType = 'blob';

        xhr.onload = function() {
          var recoveredBlob = xhr.response;
          var reader = new FileReader;
          reader.onload = function() {
            var base64data = reader.result.toString();
            var object = JSON.stringify({
              mimeType:filename.substring(filename.lastIndexOf(".")+1),
              name:filename.substring(0,filename.lastIndexOf(".")),
              data:base64data.split(',')[1]
            });
            let anchor = document.createElement("a");
            anchor.download = filename;
            anchor.href = base64data;
            anchor.click();
          };
          reader.readAsDataURL(recoveredBlob);
        };

        xhr.open('GET', blobUrl);
        xhr.send();
        resolve(true);
      })
        .catch(err => {
          if (err.status === 500) {
            this.snackbar.openSnackBar(0, '', 'Hệ thống tạm thời không thể xuất file excel!', 'error_notification', this.config.expiredTime);
          }
          resolve(false)
        });
    });
  }

  getDossierStatisticVPUB(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.get(this.padmanURL + '/ktm-ubnd-report/--count' + searchString, { headers }).pipe();
  }

  getDossierStatisticVPUBDetail(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.get(this.padmanURL + '/ktm-ubnd-report/--detail' + searchString, { headers }).pipe();
  }

  exportToExcelStatisticVPUB(params: string): any {
    return new Promise((resolve) => {
      this.http.get(this.padmanURL + '/ktm-ubnd-report/--export-detail' + params, {
        observe: 'response',
        responseType: 'blob'
      }).toPromise().then(res => {
        const blob = new Blob([res.body], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'});
        let filename = 'bao_cao_theo_linh_vuc.xlsx';
        if (res.headers.get('content-disposition') != null) {
          filename = res.headers.get('content-disposition').split(';')[1].split('=')[1].replace(/"/g, '');
        }
        const blobUrl = URL.createObjectURL(blob);
        const xhr = new XMLHttpRequest();
        xhr.responseType = 'blob';

        xhr.onload = () => {
          const recoveredBlob = xhr.response;
          const reader = new FileReader();
          reader.onload = () => {
            const base64data = reader.result.toString();
            const anchor = document.createElement('a');
            anchor.download = filename;
            anchor.href = base64data;
            anchor.click();
          };
          reader.readAsDataURL(recoveredBlob);
        };

        xhr.open('GET', blobUrl);
        xhr.send();
        resolve(true);
      }).catch(err => {
        if (err.status === 500) {
          const message = {
            vi: 'Hệ thống tạm thời không thể xuất file excel!',
            en: 'The system is temporarily unable to export the excel file!'
          };
          this.snackbar.openSnackBar(0, '', message[localStorage.getItem('language')], 'error_notification', this.config.expiredTime);
        }
        resolve(false);
      });
    });
  };

  public exportToExcelStatisticListVPUB(
    reportHeading: string,
    reportSubHeading: string,
    nameReport: string,
    subNameReport: string,
    json: any[],
    footerData: any[],
    excelFileName: string,
    sheetName: string,
    type: number = 0
  ) {
    const data = json;
    // create workbook and worksheet
    const workbook = new Workbook();
    workbook.creator = 'Snippet Coder';
    workbook.lastModifiedBy = 'SnippetCoder';
    workbook.created = new Date();
    workbook.modified = new Date();
    const worksheet = workbook.addWorksheet(sheetName);

    worksheet.getColumn('A').font = {name: 'Times New Roman', size: 12};
    worksheet.getColumn('B').font = {name: 'Times New Roman', size: 12};
    worksheet.getColumn('C').font = {name: 'Times New Roman', size: 12};
    worksheet.getColumn('D').font = {name: 'Times New Roman', size: 12};
    worksheet.getColumn('E').font = {name: 'Times New Roman', size: 12};
    worksheet.getColumn('F').font = {name: 'Times New Roman', size: 12};
    worksheet.getColumn('G').font = {name: 'Times New Roman', size: 12};
    worksheet.getColumn('H').font = {name: 'Times New Roman', size: 12};
    worksheet.getColumn('I').font = {name: 'Times New Roman', size: 12};
    worksheet.getColumn('J').font = {name: 'Times New Roman', size: 12};
    worksheet.getColumn('K').font = {name: 'Times New Roman', size: 12};
    worksheet.getColumn('L').font = {name: 'Times New Roman', size: 12};
    worksheet.getColumn('M').font = {name: 'Times New Roman', size: 12};

    // Add header row
    worksheet.addRow([]);
    worksheet.mergeCells('A1:B1');
    worksheet.getCell('A1').value = nameReport;
    worksheet.getCell('A1').alignment = {horizontal: 'center', vertical: 'middle'};

    worksheet.mergeCells('A2:B2');
    worksheet.getCell('A2').value = subNameReport;
    worksheet.getCell('A2').alignment = {horizontal: 'center', vertical: 'middle'};

    worksheet.mergeCells('H1:M1');

    worksheet.getCell('H1').value = 'Đơn vị báo cáo: ';
 
    worksheet.getCell('H1').alignment = {horizontal: 'left', vertical: 'middle'};
    worksheet.getCell('H1').font = {size: 12, bold: true, name: 'Times New Roman'};

    worksheet.mergeCells('H2:M4');
  
    worksheet.getCell('H2').value =
      '+UBND cấp xã, cơ quan chuyên môn thuộc UBND cấp huyện.\n+Cơ quan chuyên môn thuộc UBND cấp tỉnh.\n+Cơ quan, đơn vị trực thuộc bộ, cơ quan ngang bộ.\n+Cơ quan trung ương tổ chức theo ngành dọc cấp xã.';

 
    worksheet.getRow(3).height = 33;
    worksheet.getRow(4).height = 33;

    worksheet.mergeCells('H5:M5');
    
    worksheet.getCell('H5').value = 'Đơn vị nhận báo cáo: ';
 
    worksheet.getCell('H5').alignment = {horizontal: 'left', vertical: 'middle'};
    worksheet.getCell('H5').font = {size: 12, bold: true, name: 'Times New Roman'};

    worksheet.mergeCells('H6:M7');
 
    worksheet.getCell('H6').value = '+UBND cấp huyện.\n+UBND cấp tỉnh.\n+Bộ, cơ quan ngang bộ.\n+Cơ quan trung ương tổ chức theo ngành dọc cấp huyện.';

    worksheet.getCell('H6').alignment = {horizontal: 'left', vertical: 'middle'};
    worksheet.getRow(7).height = 70;

    worksheet.mergeCells('K9:M9');
 
    worksheet.getCell('K9').value = 'Đơn vị tính: Số hồ sơ TTHC.';

    worksheet.mergeCells('C1:G4');
    worksheet.getCell('C1').value = reportHeading;
    worksheet.getCell('C1').alignment = {horizontal: 'center', vertical: 'middle'};
    worksheet.getCell('C1').font = {size: 15, bold: true, name: 'Times New Roman'};

    worksheet.mergeCells('C5:G5');

    worksheet.getCell('C5').value = 'Kỳ báo cáo: Quý.../Năm...';

    worksheet.getCell('C5').alignment = {horizontal: 'center', vertical: 'middle'};
    worksheet.getCell('C5').font = {size: 12, name: 'Times New Roman'};

    worksheet.mergeCells('C6:G6');
    worksheet.getCell('C6').value = reportSubHeading;
    worksheet.getCell('C6').alignment = {horizontal: 'center', vertical: 'middle'};
    worksheet.getCell('C6').font = {size: 12, italic: true, name: 'Times New Roman'};

    // NỘI DUNG TABLE-HEADER
    worksheet.mergeCells('A10:A12');
    worksheet.mergeCells('B10:B12');
    worksheet.mergeCells('C10:F10');
    worksheet.mergeCells('G10:I10');
    worksheet.mergeCells('K10:M10');
    worksheet.mergeCells('N10:N12');
    worksheet.mergeCells('O10:O12');
    worksheet.mergeCells('C11:C12');
    worksheet.mergeCells('F11:F12');
    worksheet.mergeCells('G11:G12');
    worksheet.mergeCells('H11:H12');
    worksheet.mergeCells('I11:I12');
    worksheet.mergeCells('D11:D12');
    worksheet.mergeCells('E11:E12');
    worksheet.getCell('A13').value = '(1)';
    worksheet.getCell('B13').value = '(2)';
    worksheet.getCell('C13').value = '(3)=(4)+(5)+(6)';
    worksheet.getCell('D13').value = '(4)';
    worksheet.getCell('E13').value = '(5)';
    worksheet.getCell('F13').value = '(6)';
    worksheet.getCell('G13').value = '(7)=(8)+(9)';
    worksheet.getCell('H13').value = '(8)';
    worksheet.getCell('I13').value = '(9)';


    worksheet.getCell('A10').value = 'STT';
    worksheet.getCell('B10').value = type == 1 ? 'Lĩnh vực giải quyết' : 'Đơn vị'
    worksheet.getCell('C10').value = 'Số lượng hồ sơ đã giải quyết';
    worksheet.getCell('G10').value = 'Số lượng hồ sơ đang giải quyết';
    worksheet.getCell('C11').value = 'Tổng số';
    worksheet.getCell('D11').value = 'Trước hạn';
    worksheet.getCell('E11').value = 'Đúng hạn';
    worksheet.getCell('F11').value = 'Quá hạn';
    worksheet.getCell('G11').value = 'Tổng số';
    worksheet.getCell('H11').value = 'Trong hạn';
    worksheet.getCell('I11').value = 'Quá hạn';


    worksheet.getColumn('B').width = 45;
    worksheet.getColumn('C').width = 17;
    worksheet.getColumn('E').width = 10;
    worksheet.getColumn('G').width = 17;
    worksheet.getColumn('K').width = 17;
    worksheet.getRow(2).height = 27;
    worksheet.getColumn('C').alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
    worksheet.getColumn('D').alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
    worksheet.getColumn('E').alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
    worksheet.getColumn('F').alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
    worksheet.getColumn('G').alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
    worksheet.getColumn('H').alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
    worksheet.getColumn('I').alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};

    worksheet.getCell('H1').style.alignment = {horizontal: 'left', vertical: 'middle', wrapText: true};
    worksheet.getCell('H2').style.alignment = {horizontal: 'left', vertical: 'middle', wrapText: true};
    worksheet.getCell('H5').style.alignment = {horizontal: 'left', vertical: 'middle', wrapText: true};
    worksheet.getCell('H6').style.alignment = {horizontal: 'left', vertical: 'middle', wrapText: true};

    worksheet.properties.outlineLevelCol = 2;
    worksheet.properties.defaultRowHeight = 15;

    let i = 10;
    const j = 13;
    for (i; i <= j; i++) {
      let k = 1;
      const l = 9;
      for (k; k <= l; k++) {
        worksheet.findCell(i, k).alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
        worksheet.findCell(i, k).fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: {argb: 'C0C0C0C0'},
          bgColor: {argb: 'FF0000FF'}
        };
        worksheet.findCell(i, k).border = {top: {style: 'thin'}, left: {style: 'thin'}, bottom: {style: 'thin'}, right: {style: 'thin'}};
        worksheet.findCell(i, k).font = {size: 12, bold: true, name: 'Times New Roman'};
      }
    }

    let columnsArray: any[];
    for (const key in json) {
      if (json.hasOwnProperty(key)) {
        columnsArray = Object.keys(json[key]);
      }
    }

    data.forEach((element: any) => {
      const eachRow = [];
      columnsArray.forEach((column) => {
        eachRow.push(element[column]);
      });
      eachRow.splice(0, 1);
      const borderrow = worksheet.addRow(eachRow);
      borderrow.eachCell((cell) => {
        cell.border = {top: {style: 'thin'}, left: {style: 'thin'}, bottom: {style: 'thin'}, right: {style: 'thin'}};
        cell.alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
      });
    });

    const dataLength = data.length;
    if (dataLength > 0) {
      for (i = 0; i < dataLength; i++) {
        worksheet.getCell('B' + (14 + i)).alignment = {horizontal: 'left', vertical: 'middle', wrapText: true};
      }
    }

    // footer data row
    if (footerData != null) {
      footerData.forEach((element: any) => {
        const eachRow = [];
        element.forEach((val: any) => {
          eachRow.push(val);
        });
        const footerRow = worksheet.addRow(eachRow);
        const cellMerge = 'A' + footerRow.number + ':B' + footerRow.number;
        worksheet.mergeCells(cellMerge);
        footerRow.eachCell((cell) => {
          cell.font = {size: 13, bold: true, name: 'Times New Roman'};
          cell.border = {top: {style: 'thin'}, left: {style: 'thin'}, bottom: {style: 'thin'}, right: {style: 'thin'}};
          cell.alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
        });
      });
    }


    workbook.xlsx.writeBuffer().then((dataBuffer: ArrayBuffer) => {
      const blob = new Blob([dataBuffer], {type: EXCEL_TYPE});
      fs.saveAs(blob, excelFileName + EXCEL_EXTENSION);
    });
  }



}
