<h2>S<PERSON> theo d<PERSON><PERSON> hồ sơ - Thông tư 01/2018/TT-VPCP</h2>
<div class="prc_searchbar">
  <div fxLayout="row" fxLayout.xs="row" fxLayout.sm="row" class="formFieldOutline" fxLayoutAlign="space-between">
    <mat-form-field appearance="outline" fxFlex.gt-sm="100" fxFlex.gt-xs="49.5" fxFlex='grow'>
      <mat-label>Nhập từ khóa</mat-label>
      <input type="text" [(ngModel)]="paramsQuery.keyword" matInput>
    </mat-form-field>
  </div>
  <div fxLayout="row" fxLayout.xs="row" fxLayout.sm="row" class="formFieldOutline" fxLayoutAlign="space-between">

    <mat-form-field appearance="outline" fxFlex.gt-sm="33" fxFlex.gt-xs="49.5" fxFlex='grow'>
      <mat-label><PERSON><PERSON><PERSON> vị tiế<PERSON> nhận</mat-label>
      <mat-select msInfiniteScroll (infiniteScroll)="getAgencyScroll()"
        [complete]="totalPagesAgencyAccept <= currentPageAgencyAccept+1" [(ngModel)]="paramsQuery.agency"
        (selectionChange)="changeAgencyAccept()">
        <!-- <div>
          <div>
            <input matInput #searchInputAgencyAccept (keyup)="onEnter($event)" (keydown)="$event.stopPropagation()"
              placeholder="Nhập từ khóa" class="search-nested" />
            <button mat-icon-button class="clear-search-nested" *ngIf="searchInputAgencyAccept.value !== ''"
              (click)="searchInputAgencyAccept.value = ''; resetSearchForm('agencyAccept')">
              <mat-icon> close </mat-icon>
            </button>
          </div>
        </div> -->
        <mat-option>
          <ngx-mat-select-search ngModel (ngModelChange)="searchAngency($event)" placeholderLabel="">
          </ngx-mat-select-search>
        </mat-option>
        <mat-option value="">Tất cả</mat-option>
        <mat-option *ngFor="let item of listAgencyAccept" [value]="item.id">{{item.name}}</mat-option>
      </mat-select>
    </mat-form-field>
    <mat-form-field appearance="outline" fxFlex.gt-sm="33" fxFlex.gt-xs="49.5" fxFlex='grow'>
      <mat-label>Lĩnh vực</mat-label>
      <mat-select msInfiniteScroll (infiniteScroll)="getListSectorScroll()"
        [complete]="totalPagesSector <= currentPageSector+1" [(ngModel)]="paramsQuery.sector"
        (selectionChange)="sectorChange()">
        <mat-option>
          <ngx-mat-select-search ngModel (ngModelChange)="searchSector($event)" placeholderLabel="">
          </ngx-mat-select-search>
        </mat-option>
        <mat-option value="">Tất cả</mat-option>
        <mat-option *ngFor="let item of listSectorfillter" [value]="item.id">{{item.name}}</mat-option>
      </mat-select>
    </mat-form-field>
    <mat-form-field appearance="outline" fxFlex.gt-sm="33" fxFlex.gt-xs="49.5" fxFlex='grow'>
      <mat-label>Thủ tục</mat-label>
      <mat-select msInfiniteScroll (infiniteScroll)="getListProcedureScroll()"
        [complete]="totalPagesProcedure <= currentPageProcedure+1" [(ngModel)]="paramsQuery.procedure">
        <mat-option>
          <ngx-mat-select-search ngModel (ngModelChange)="searchProvedure($event)" placeholderLabel="">
          </ngx-mat-select-search>
        </mat-option>
        <mat-option value="">Tất cả</mat-option>
        <mat-option *ngFor="let item of listProcedurefillter" [value]="item.id">{{item.name}}</mat-option>
      </mat-select>
    </mat-form-field>
  </div>
  <div fxLayout="row" fxLayout.xs="row" fxLayout.sm="row" class="formFieldOutline" fxLayoutAlign="space-between">
    <mat-form-field appearance="outline" fxFlex.gt-sm="33" fxFlex.gt-xs="49.5" fxFlex='grow'>
      <mat-label>Hình thức tiếp nhận</mat-label>
      <mat-select [(ngModel)]="paramsQuery.applyMethod">
        <mat-option value="-1">Tất cả</mat-option>
        <mat-option value="0">Trực tuyến</mat-option>
        <mat-option value="1">Trực tiếp</mat-option>
        <mat-option value="2">Import từ excel</mat-option>
      </mat-select>
    </mat-form-field>
    <mat-form-field appearance="outline" fxFlex.gt-sm="33" fxFlex.gt-xs="49.5" fxFlex='grow'>
      <mat-label>Hình thức nhận kết quả</mat-label>
      <mat-select [(ngModel)]="paramsQuery.receivingKind">
        <mat-option value="">Tất cả</mat-option>
        <mat-option *ngFor="let item of listHinhThucNhan" [value]="item.id">{{item.name}}</mat-option>
      </mat-select>
    </mat-form-field>
    <div appearance="outline" fxFlex.gt-sm="33" fxFlex.gt-xs="49.5" fxFlex='grow'>
      <div fxLayout="row" fxLayout.xs="row" fxLayout.sm="row" class="formFieldOutline" fxLayoutAlign="space-between">
        <mat-form-field appearance="outline" fxFlex.gt-sm="49.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
          <mat-label>Tiếp nhận từ ngày</mat-label>
          <input matInput [matDatepicker]="pickerAcceptFrom" [(ngModel)]="startDate">
          <mat-datepicker-toggle matSuffix [for]="pickerAcceptFrom"></mat-datepicker-toggle>
          <mat-datepicker #pickerAcceptFrom></mat-datepicker>
        </mat-form-field>
        <mat-form-field appearance="outline" fxFlex.gt-sm="49.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
          <mat-label>Tiếp nhận đến ngày</mat-label>
          <input matInput [matDatepicker]="pickerAcceptTo" [(ngModel)]="endDate">
          <mat-datepicker-toggle matSuffix [for]="pickerAcceptTo"></mat-datepicker-toggle>
          <mat-datepicker #pickerAcceptTo></mat-datepicker>
        </mat-form-field>
      </div>
    </div>
    <!-- <mat-form-field appearance="outline" fxFlex.gt-sm="32" fxFlex.gt-xs="49.5" fxFlex='grow'>
          <mat-label>Đơn vị tiếp nhận</mat-label>
          <mat-select msInfiniteScroll (infiniteScroll)="getAgencyScroll()"
            [complete]="totalPagesAgencyAccept <= currentPageAgencyAccept+1" formControlName="agencyCtrl"
            (selectionChange)="changeAgencyAccept()">
            <div>
              <div>
                <input matInput #searchInputAgencyAccept (keyup)="onEnter('agencyAccept', $event)"
                  (keydown)="$event.stopPropagation()" placeholder="Nhập từ khóa" class="search-nested" />
                <button mat-icon-button class="clear-search-nested" *ngIf="searchInputAgencyAccept.value !== ''"
                  (click)="searchInputAgencyAccept.value = ''; resetSearchForm('agencyAccept')">
                  <mat-icon> close </mat-icon>
                </button>
              </div>
            </div>
            <mat-option value="">Tất cả</mat-option>
            <mat-option *ngFor="let item of listAgencyAccept" [value]="item.id">{{item.name}}</mat-option>
          </mat-select>
        </mat-form-field> -->
  </div>

  <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="row" fxLayoutAlign="end" style="padding-bottom: 1em;">
    <button mat-flat-button fxFlex.lt-md="22" fxFlex.md="20" fxFlex.gt-md="12" fxFlex.gt-xs="49.5" fxFlex='grow'
      class="btn-search" type="submit" (click)="thongKe()" style="background-color: #ce7a58;color: white;">
      <mat-icon class="iconStatistical">bar_chart</mat-icon><span>Thống kê</span>
    </button>
    <div fxFlex='1'></div>
    <button mat-flat-button fxFlex.lt-md="22" fxFlex.md="20" fxFlex.gt-md="12" fxFlex.gt-xs="49.5" fxFlex='grow'
      class="btn-download-excel" style="background-color: #127905;color: white;" (click)="xuatExcel()">
      <mat-icon class="iconStatistical">cloud_download</mat-icon>
      <span> Xuất excel</span>
      <mat-progress-bar mode="indeterminate" *ngIf="waitingDownloadExcel"></mat-progress-bar>
    </button>
    <div fxFlex='1'></div>

    <!-- <div fxFlex='1'></div>
      <button mat-flat-button fxFlex.lt-md="22" fxFlex.md="20" fxFlex.gt-md="12" fxFlex.gt-xs="49.5" fxFlex='grow'
        class="btn-download-excel" [disabled]="waitingDownloadExcel">
        <mat-icon class="iconStatistical">cloud_download</mat-icon>
        <span>Xuất excel</span>
        <mat-progress-bar mode="indeterminate" *ngIf="waitingDownloadExcel"></mat-progress-bar>
      </button>
      <div fxFlex='1'></div>
      <button mat-flat-button fxFlex.lt-md="22" fxFlex.md="20" fxFlex.gt-md="12" fxFlex.gt-xs="49.5" fxFlex='grow'
        class="btn-print" [disabled]="waitingDownloadPrint">
        <mat-icon class="iconStatistical">print</mat-icon>
        <span>In danh sách</span>
        <mat-progress-bar mode="indeterminate" *ngIf="waitingDownloadPrint"></mat-progress-bar>
      </button> -->
  </div>
</div>

<div fxLayout="row" fxLayoutAlign="center">
  <div class="frm_main" fxFlex="grow">
    <div class="logbookTbl">
      <div class="logbookOnlyTbl">
        <table class="table">
          <tr>
            <th rowspan="2">STT</th>
            <th rowspan="2">Số hồ sơ</th>
            <th rowspan="2">Tên TTHC</th>
            <th rowspan="2">Tên cá nhân tổ chức</th>
            <th rowspan="2">Địa chỉ, số điện thoại</th>
            <th rowspan="2">Cơ quan chủ trì giải quyết</th>
            <th colspan="7">Nhận và trả kết quả</th>
            <th rowspan="2">Tiến độ thực hiện</th>
            <th rowspan="2">Hình thức tiếp nhận</th>
            <th rowspan="2">Ghi chú</th>
          </tr>
          <tr>
            <th>Nhận hồ sơ</th>
            <th>Hẹn trả kết quả</th>
            <th>Ngày liên thông (VPUB)</th>
            <th>Ngày có kết quả / YC trả lại dân</th>
            <th>Ngày trả kết quả / trả lại dân</th>
            <th>Hình thức nhận kết quả</th>
            <th>Ký nhận</th>
          </tr>
          <tr>
            <th>1</th>
            <th>2</th>
            <th>3</th>
            <th>4</th>
            <th>5</th>
            <th>6</th>
            <th>7</th>
            <th>8</th>
            <th>9</th>
            <th>10</th>
            <th>11</th>
            <th>12</th>
            <th>13</th>
            <th>14</th>
            <th>15</th>
            <th>16</th>
          </tr>
          <tr *ngFor="let item of listHoSo;;let i = index ">
            <td>{{ paramsQuery.page * paramsQuery.size + i + 1}}</td>
            <td>{{item.code}}</td>
            <td>{{item.procedureCode}} - {{item.procedureName}}</td>
            <td>{{getFullName(item.applicant?.data)}}</td>
            <td>{{generateAddress(item.applicant?.data)}}</td> <!-- xoa phonenumber -->
            <td>{{getTen(item.agency?.name)}}</td>
            <td>
              <span *ngIf="item.acceptedDate != undefined">
                {{item.acceptedDate | date : 'dd/MM/yyyy HH:mm:ss'}}
              </span>
            </td>
            <td>
              <span *ngIf="item.appointmentDate != undefined">
                {{item.appointmentDate | date : 'dd/MM/yyyy HH:mm:ss'}}
              </span>
            </td>
            <td>
              <span *ngIf="item.acceptedDate != undefined">
                {{item.acceptedDate | date : 'dd/MM/yyyy HH:mm:ss'}}
              </span>
            </td>
            <td>
              <span *ngIf="item.completedDate != undefined">
                {{item.completedDate | date : 'dd/MM/yyyy HH:mm:ss'}}
              </span>
            </td>
            <td>
              <span *ngIf="item.returnedDate != undefined">
                {{item.returnedDate | date : 'dd/MM/yyyy HH:mm:ss'}}
              </span>
            </td>
            <td>{{getTen(item.dossierReceivingKind?.name)}}</td>
            <td></td>
            <td>{{getTienDo(item)}}</td>
            <td>{{item.applyMethod?.name}}</td>
            <td>{{getTen(item.dossierStatus?.note)}}</td>
          </tr>
        </table>
        <div class="frm_Pagination">
          <ul class="temp_Arr">
            <li
              *ngFor="let item of listHoSo  | paginate: {itemsPerPage: paramsQuery.size, currentPage: page, totalItems: countResult, id: 'pgnx'}">
            </li>
          </ul>
          <div class="pageSize">
            <span i18n>Hiển thị </span>
            <mat-form-field appearance="outline">
              <mat-select [(ngModel)]="paramsQuery.size" (valueChange)="paginate(0)">
                <mat-option *ngFor='let opt of pgSizeOptions;' [value]="opt">{{opt}}</mat-option>
              </mat-select>
            </mat-form-field>
            <span><span i18n>trên</span> {{countResult}} <span i18n>bản ghi</span></span>
          </div>
          <div class="control">
            <pagination-controls id="pgnx" (pageChange)="page = $event; paginate(page - 1)" responsive="true"
              previousLabel="" nextLabel="">
            </pagination-controls>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>