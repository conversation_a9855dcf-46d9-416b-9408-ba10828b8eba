import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { ApiProviderService } from 'src/app/core/service/api-provider.service';
import { Observable, Subject } from 'rxjs';
import { EnvService } from 'src/app/core/service/env.service';
import { DeploymentService } from './deployment.service';
import {AgencyService} from 'data/service/basedata/agency.service';

@Injectable({
    providedIn: 'root'
})
export class NotificationService {

    //#region Init
    private env: any;
    private adapter = this.apiProviderService.getUrl('digo', 'adapter');
    private reporter = this.apiProviderService.getUrl('digo', 'reporter');
    public templateSubject: Subject<any> = new Subject<any>();
    public formSubject: Subject<any> = new Subject<any>();
    public checkSendSubject: Subject<any> = new Subject<any>();
    public confirmSendSubject: Subject<any> = new Subject<any>();
    public changeSendSubject: Subject<any> = new Subject<any>();
    public extendAgencies: any = {
      agencies: null
    }

    agencyAndAncestors = this.agencyService.getAgencyAndAncestors();
    showLogNotify = this.deploymentService?.env?.showLogNotify;
    
    constructor(
        private envService: EnvService,
        private http: HttpClient,
        private apiProviderService: ApiProviderService,
        private deploymentService: DeploymentService,
        private agencyService: AgencyService
    ) {
        this.env = this.deploymentService.getAppDeployment()?.env;
        // this.config = this.envService.getConfig();
    }
    //#endregion
    resetSendSubject()
    {
        this.changeSendSubject = new Subject<any>();
        this.checkSendSubject = new Subject<any>();
        this.confirmSendSubject = new Subject<any>();
    }
    //#region Get lst template
    private getLstTemplateUrl(typeId: string, page: number, size: number) {
        const subsystemId = this.env?.subsystem?.id;
        return this.reporter + `/template?type-id=${typeId}&subsystem-id=${subsystemId}&page=${page}&size=${size}`;
    }

    public getLstTemplate(typeId, page): Observable<any> {
        return this.http.get(this.getLstTemplateUrl(typeId, page, 50));
    }

    public getTemplateConfigId(type: string) {
        switch (type) {
            case 'sms':
                return this.envService.getConfig().reportCitizenSmsTypeId;
                break;
            case 'email':
                return this.envService.getConfig().reportCitizenEmailTypeId;
                break;
            case 'zalo':
                return this.envService.getConfig().reportCitizenZaloTypeId;
                break;
        }
    }

    public getOfficerTemplateConfigId(type: string) {
        switch (type) {
            case 'sms':
                return this.envService.getConfig().reportOfficerSmsTypeId;
                break;
            case 'email':
                return this.envService.getConfig().reportOfficerEmailTypeId;
                break;
            // case 'zalo':
            //     return this.envService.getConfig().reportOfficerZaloTypeId;
            //     break;
        }
    }
    //#endregion

    //#region Get notification config
    public getConfig(config: any, type: string = 'sms', citizenType: string = 'citizen',      functionType: string = 'processing'): NotifyConfig {
        if(this.showLogNotify){
            console.log(type + citizenType + functionType + "process", config);
            console.log(type + citizenType + functionType + "v2",this.env?.notify?.[functionType]?.[type]?.[citizenType]);
        }

        let ret = new NotifyConfig();
        if (type == 'zalo' && citizenType == 'officer') {
            if (!!config) {
                ret.id = config.id;
                ret.name = config.name;
                ret.templateId = config.templateId;
                ret.defaultSend = config.defaultSend;
            } else {
                ret.id = !!this.env?.notify?.[functionType]?.[type]?.[citizenType]?.default ? this.env?.notify?.[functionType]?.[type]?.[citizenType]?.default : this.getDefaultConfig(functionType, type, citizenType);
                ret.defaultSend = this.env?.notify?.[functionType]?.[type]?.[citizenType]?.send == true ? true : false;
            }
            ret.enable = this.env?.notify?.[functionType]?.[type]?.[citizenType]?.enable == true ? true : false;
            ret.canEdit = this.env?.notify?.[functionType]?.[type]?.[citizenType]?.canEdit == true ? true : false;
            ret.title = !!this.env?.notify?.[functionType]?.[type]?.[citizenType]?.title ? this.env?.notify?.title : "VNPT IGATE 2.0 Thông báo";
        } else if(functionType == "receptionReceiving"){
            if (!!config) {
                ret.id = config.id;
                ret.name = config.name;
                ret.templateId = config.templateId;
                ret.defaultSend = config.defaultSend;
            } else {
                ret.id = !!this.env?.notify?.[functionType]?.[type]?.[citizenType]?.default ? this.env?.notify?.[functionType]?.[type]?.[citizenType]?.default : this.getDefaultConfig(functionType, type, citizenType);
                ret.defaultSend = this.env?.notify?.[functionType]?.[type]?.[citizenType]?.send == false ? false : true;
            }
            ret.enable = this.env?.notify?.[functionType]?.[type]?.[citizenType]?.enable == false ? false : true;
            ret.canEdit = this.env?.notify?.[functionType]?.[type]?.[citizenType]?.canEdit == true ? true : false;
            ret.title = !!this.env?.notify?.[functionType]?.[type]?.[citizenType]?.title ? this.env?.notify?.title : "VNPT IGATE 2.0 Thông báo";
        } else {
            if (!!config) {
                ret.id = config.id;
                ret.name = config.name;
                ret.templateId = config.templateId;
                ret.defaultSend = config.defaultSend;
            } else {
                ret.id = !!this.env?.notify?.[functionType]?.[type]?.[citizenType]?.default ? this.env?.notify?.[functionType]?.[type]?.[citizenType]?.default : this.getDefaultConfig(functionType, type, citizenType);
                ret.defaultSend = this.env?.notify?.[functionType]?.[type]?.[citizenType]?.send == false ? false : true;
            }
            ret.enable = this.env?.notify?.[functionType]?.[type]?.[citizenType]?.enable == false ? false : true;
            ret.canEdit = this.env?.notify?.[functionType]?.[type]?.[citizenType]?.canEdit == false ? false : true;
            ret.title = !!this.env?.notify?.[functionType]?.[type]?.[citizenType]?.title ? this.env?.notify?.title : "VNPT IGATE 2.0 Thông báo";
        }
        return ret;
    }

    private getDefaultConfig(functionType: string = 'config', type: string = 'sms', citizenType: string = 'citizen'): string {
        const switchKey = functionType + type + citizenType;

        switch (switchKey) {
            // case 'processingsmsofficer': return '616f923399966aeafbd00001';
            // case 'processingemailofficer': return '616f923399966aeafbd00002';
            // case 'processingzaloofficer': return '616f923399966aeafbd00003';
            // case 'processingsmscitizen': return '616f923399966aeafbd00004';
            // case 'processingemailcitizen': return '616f923399966aeafbd00005';
            // case 'processingzalocitizen': return '616f923399966aeafbd00006';
            // case 'applyOnlinesmscitizen': return '616e35365eeeaf26a8c237b9';
            // case 'additionalRequirementsmscitizen': return '616e35365eeeaf26a8c237b9';
            // case 'receptionOnlinesmscitizen': return '616e35365eeeaf26a8c237b9';
            // case 'suspendDossiersmscitizen': return '616e35365eeeaf26a8c237b9';
            // case 'resumeDossiersmscitizen': return '616e35365eeeaf26a8c237b9';
            // case 'receptionReceivingsmscitizen': return '616e35365eeeaf26a8c237b9';
            // case 'extendProcessingDossiersmscitizen': return '616e35365eeeaf26a8c237b9';
            // case 'requestWithdrawDossiersmscitizen': return '616e35365eeeaf26a8c237b9';
            // case 'applyOnlineemailcitizen': return '616e35365eeeaf26a8c237b9';
            // case 'additionalRequirementemailcitizen': return '616e35365eeeaf26a8c237b9';
            // case 'receptionOnlineemailcitizen': return '616e35365eeeaf26a8c237b9';
            // case 'suspendDossieremailcitizen': return '616e35365eeeaf26a8c237b9';
            // case 'resumeDossieremailcitizen': return '616e35365eeeaf26a8c237b9';
            // case 'receptionReceivingemailcitizen': return '616e35365eeeaf26a8c237b9';
            // case 'extendProcessingDossieremailcitizen': return '616e35365eeeaf26a8c237b9';
            // case 'requestWithdrawDossieremailcitizen': return '616e35365eeeaf26a8c237b9';
            // case 'applyOnlinezalocitizen': return '616e35365eeeaf26a8c237b9';
            // case 'additionalRequirementzalocitizen': return '616e35365eeeaf26a8c237b9';
            // case 'receptionOnlinezalocitizen': return '616e35365eeeaf26a8c237b9';
            // case 'suspendDossierzalocitizen': return '616e35365eeeaf26a8c237b9';
            // case 'resumeDossierzalocitizen': return '616e35365eeeaf26a8c237b9';
            // case 'receptionReceivingzalocitizen': return '616e35365eeeaf26a8c237b9';
            // case 'extendProcessingDossierzalocitizen': return '616e35365eeeaf26a8c237b9';
            // case 'requestWithdrawDossierzalocitizen': return '616e35365eeeaf26a8c237b9';
            case 'processingsmsofficer': return '616f923399966aeafbd00001';
            case 'processingemailofficer': return '616f923399966aeafbd00002';
            case 'processingzaloofficer': return '616f923399966aeafbd00003';
            case 'processingsmscitizen': return '616f923399966aeafbd00004';
            case 'processingemailcitizen': return '616f923399966aeafbd00005';
            case 'processingzalocitizen': return '616f923399966aeafbd00006';
            case 'applyOnlinesmscitizen': return '616f923399966aeafbd00007';
            case 'additionalRequirementsmscitizen': return '616f923399966aeafbd00008';
            case 'receptionOnlinesmscitizen': return '616f923399966aeafbd00009';
            case 'suspendDossiersmscitizen': return '616f923399966aeafbd00010';
            case 'resumeDossiersmscitizen': return '616f923399966aeafbd00011';
            case 'receptionReceivingsmscitizen': return '616f923399966aeafbd00012';
            case 'extendProcessingDossiersmscitizen': return '616f923399966aeafbd00013';
            case 'requestWithdrawDossiersmscitizen': return '616f923399966aeafbd00014';
            case 'applyOnlineemailcitizen': return '616f923399966aeafbd00015';
            case 'additionalRequirementemailcitizen': return '616f923399966aeafbd00016';
            case 'receptionOnlineemailcitizen': return '616f923399966aeafbd00017';
            case 'suspendDossieremailcitizen': return '616f923399966aeafbd00018';
            case 'resumeDossieremailcitizen': return '616f923399966aeafbd00019';
            case 'receptionReceivingemailcitizen': return '616f923399966aeafbd00020';
            case 'extendProcessingDossieremailcitizen': return '616f923399966aeafbd00021';
            case 'requestWithdrawDossieremailcitizen': return '616f923399966aeafbd00022';
            case 'applyOnlinezalocitizen': return '616f923399966aeafbd00023';
            case 'additionalRequirementzalocitizen': return '616f923399966aeafbd00024';
            case 'receptionOnlinezalocitizen': return '616f923399966aeafbd00025';
            case 'suspendDossierzalocitizen': return '616f923399966aeafbd00026';
            case 'resumeDossierzalocitizen': return '616f923399966aeafbd00027';
            case 'receptionReceivingzalocitizen': return '616f923399966aeafbd00028';
            case 'extendProcessingDossierzalocitizen': return '616f923399966aeafbd00029';
            case 'requestWithdrawDossierzalocitizen': return '616f923399966aeafbd00030';
            case 'cancelDossierCitizensmscitizen': return '616f923399966aeafbd00031';
            case 'cancelDossierCitizenemailcitizen': return '616f923399966aeafbd00032';
            case 'cancelDossierCitizenzalocitizen': return '616f923399966aeafbd00033';
            case 'additionalRequirementOfficersmscitizen': return '616f923399966aeafbd00034';
            case 'additionalRequirementOfficeremailcitizen': return '616f923399966aeafbd00035';
            case 'additionalRequirementOfficerzalocitizen': return '616f923399966aeafbd00036';
            case 'suspendDossierOfficersmscitizen': return '616f923399966aeafbd00037';
            case 'suspendDossierOfficeremailcitizen': return '616f923399966aeafbd00038';
            case 'suspendDossierOfficerzalocitizen': return '616f923399966aeafbd00039';
            case 'extendProcessingDossierOfficersmscitizen': return '616f923399966aeafbd00040';
            case 'extendProcessingDossierOfficeremailcitizen': return '616f923399966aeafbd00041';
            case 'extendProcessingDossierOfficerzalocitizen': return '616f923399966aeafbd00042';
            case 'cancelDossierOfficersmscitizen': return '616f923399966aeafbd00043';
            case 'cancelDossierOfficeremailcitizen': return '616f923399966aeafbd00044';
            case 'cancelDossierOfficerzalocitizen': return '616f923399966aeafbd00045';
            case 'returnResultDossiersmsofficer': return '616f923399966aeafbd00001';
            case 'returnResultDossieremailofficer': return '616f923399966aeafbd00002';
            case 'returnResultDossiersmscitizen': return '616f923399966aeafbd00004';
            case 'returnResultDossieremailcitizen': return '616f923399966aeafbd00005';
            case 'returnResultDossierzalocitizen': return '616f923399966aeafbd00006';
        }
    }
    //#endregion

    //#region  Get content from reporter freemarker
    private getFreemarkerContentUrl = this.reporter + '/freemarker/--report';
    public getSmsContent(templateId, body) {
        return this.http.post(this.getFreemarkerContentUrl + '?id=' + templateId, body);
    }
    //#endregion

    //#region Send notify to adapter
    private sendSmsUrl = this.adapter + '/sms-brandname/--send-batch';
    private sendEmailUrl = this.adapter + '/email/--send-by-agency';
    private getSendZaloUrl(agencyId: string, subsystemId: string) {
        return this.adapter + `/zalov2/--send-template?agency-id=${agencyId}&subsystem-id=${subsystemId}`;
    }
    public sendSms(agencyId: string, subsystemId: string, content: string, templateId: string, lstPhone: string[], extend?: any): Observable<any> {
        if(extend){
          extend.agencies = this.agencyAndAncestors;
        } else {
          this.extendAgencies.agencies = this.agencyAndAncestors;
        }

        let formData: FormData = new FormData();
        formData.append('agencyId', agencyId);
        formData.append('subsystemId', subsystemId);
        formData.append('content', content);
        formData.append('templateId', templateId);
        lstPhone.forEach(phone => {
            formData.append('phoneNumber', phone);
        });
        if(!!extend){
            formData.append('extend',extend);
        } else {
            formData.append('extend',this.extendAgencies);
        }
        return this.http.post<any>(this.sendSmsUrl, formData).pipe();
    }

    public sendSmsAndWriteLog(agencyId: string, subsystemId: string, content: string, templateId: string, lstPhone: string[], extend?: any): Observable<any> {
      if(extend){
        extend.agencies = this.agencyAndAncestors;
      } else {
        this.extendAgencies.agencies = this.agencyAndAncestors;
      }
      const  URL = `${this.adapter}/sms-brandname/--send-batch-and-write-log`;
      let headers = new HttpHeaders();
      headers = headers.set('Content-Type', 'application/json');
      const requestBody = {
        agencyId:agencyId,
        subsystemId: subsystemId,
        content: content,
        templateId: templateId,
        phoneNumber: lstPhone,
        extend: extend ? extend : this.extendAgencies
      }
      return this.http.post<any>(URL, requestBody, { headers });
    }

    public sendSmsByTemplate(agencyId: string, subsystemId: string, contentTemplate: string, params: any, templateId: string, lstPhone: string[], extend?: any): Observable<any> {
        const  URL = `${this.adapter}/v2/sms-brandname/--send-with-template`;
        let headers = new HttpHeaders();
        headers = headers.set('Content-Type', 'application/json');
        const requestBody = {
            agencyId:agencyId,
            subsystemId: subsystemId,
            contentTemplate: contentTemplate,
            params: params,
            templateId: templateId,
            phoneNumber: lstPhone,
            extend: extend ? extend : this.extendAgencies
        }
        return this.http.post<any>(URL, requestBody, { headers });
    }

    public sendSmsByTemplateSync(requestBody: any): Observable<any> {
        const  URL = `${this.adapter}/v2/sms-brandname/--send-with-template-sync`;
        let headers = new HttpHeaders();
        headers = headers.set('Content-Type', 'application/json');
        return this.http.post<any>(URL, requestBody, { headers });
    }

    public sendSmsByTemplateQni(agencyId: string, subsystemId: string, contentTemplate: string, params: any, templateId: string, lstPhone: string[], extend?: any): Observable<any> {
        const  URL = `${this.adapter}/v2/sms-brandname/--send-with-template-qni`;
        let headers = new HttpHeaders();
        headers = headers.set('Content-Type', 'application/json');
        const requestBody = {
            agencyId:agencyId,
            subsystemId: subsystemId,
            contentTemplate: contentTemplate,
            params: params,
            templateId: templateId,
            phoneNumber: lstPhone,
            extend: extend ? extend : this.extendAgencies
        }
        return this.http.post<any>(URL, requestBody, { headers });
    }

    public sendSmsByTemplateQNM(agencyId: string, subsystemId: string, contentTemplate: string, params: any, templateId: string, lstPhone: string[], extend: any = null): Observable<any> {
        const  URL = `${this.adapter}/v2/sms-brandname/--send-with-template-qnm`;
        // const  URL = `http://localhost:8084/v2/sms-brandname/--send-with-template-qnm`;
        let headers = new HttpHeaders();
        headers = headers.set('Content-Type', 'application/json');
        const requestBody = {
            agencyId:agencyId,
            subsystemId: subsystemId,
            contentTemplate: contentTemplate,
            params: params,
            templateId: templateId,
            phoneNumber: lstPhone,
            extend: extend ? extend : this.extendAgencies
        }
        return this.http.post<any>(URL, requestBody, { headers });
    }

    public sendEmail(agencyId: string, subsystemId: string, content: string, subject: string, listEmail: string[], attachment?: File[], attachmentFile?: any): Observable<any> {
        const formData: FormData = new FormData();
        formData.append('agencyId', agencyId);
        formData.append('subsystemId', subsystemId);
        formData.append('content', content);
        formData.append('subject', subject);
        if(attachment && attachment.length > 0){
            for(var i = 0; i < attachment.length; i++){
                formData.append('attachment[${i}]', attachment[i]);
            }
        }
        listEmail.forEach(email => {
            formData.append('emailAddress', email);
        });
        if(attachmentFile && attachmentFile.length > 0){
            formData.append('attachmentFile', attachmentFile);
        }
        return this.http.post<any>(this.sendEmailUrl, formData).pipe();
    }

    public sendEmailByTemplate(agencyId: string, subsystemId: string, contentTemplate: string, params: any, subject: string, listEmail: string[], attachment?: File[], attachmentFile?: any[]): Observable<any> {
        const  URL = `${this.adapter}/v2/email/--send-with-template`;
        let headers = new HttpHeaders();
        headers = headers.set('Content-Type', 'application/json');
        const requestBody = {
            agencyId:agencyId,
            subsystemId: subsystemId,
            contentTemplate: contentTemplate,
            params: params,
            subject: subject,
            emailAddress: listEmail,
            attachment: attachment,
            attachmentFile: attachmentFile
        }
        return this.http.post<any>(URL, requestBody, { headers });
    }

    public sendEmailByTemplateSync(requestBody: any): Observable<any> {
        const  URL = `${this.adapter}/v2/email/--send-with-template-sync`;
        let headers = new HttpHeaders();
        headers = headers.set('Content-Type', 'application/json');
        return this.http.post<any>(URL, requestBody, { headers });
    }

    public sendZalo(agencyId: string, subsystemId: string, requestBody: any): Observable<any> {
        let headers = new HttpHeaders();
        headers = headers.set('Content-Type', 'application/json');
        return this.http.post<any>(this.getSendZaloUrl(agencyId, subsystemId), requestBody, { headers });
    }

    public sendZaloByStep(agencyId: string, subsystemId: string, requestBody: any): Observable<any> {
        let headers = new HttpHeaders();
        headers = headers.set('Content-Type', 'application/json');
        return this.http.post<any>(this.adapter + `/zalov2/--send-template-by-step?agency-id=${agencyId}&subsystem-id=${subsystemId}`, requestBody, { headers });
    }

    public sendZaloText(agencyId: string, subsystemId: string, requestBody: any): Observable<any> {
        let headers = new HttpHeaders();
        headers = headers.set('Content-Type', 'application/json');
        return this.http.post<any>(this.adapter + `/zalov2/--send/?agency-id=${agencyId}&subsystem-id=${subsystemId}`, requestBody, { headers });
      }

    public saveLogNotification(requestBody: any): Observable<any> {
        const  URL = `${this.adapter}/v2/sms-brandname/--save-log-noti-to-officer`;
        let headers = new HttpHeaders();
        headers = headers.set('Content-Type', 'application/json');
        return this.http.post<any>(URL, requestBody, { headers });
    }

    // IGATESUPP-114129 - Bổ sung thông báo trên app xử lý TTHC
    public sendNotificationAppTthcCTO(requestBody:any): Observable<any> {
        const  URL = `${this.adapter}/notification-mobile/--send-with-template-cto`;
        let headers = new HttpHeaders();
        headers = headers.set('Content-Type', 'application/json');
        return this.http.post<any>(URL, requestBody, { headers });
    }
    //#endregion
}

export class NotifyConfig {
    id: string = '';
    name: string = '';
    templateId: string = '';
    title: string = '';
    defaultSend: boolean = true;
    enable: boolean = true;
    canEdit: boolean = true;
}

export class NotifyData {
    public sms: CitizenType = new CitizenType();
    public email: CitizenType = new CitizenType();
    public zalo: CitizenType = new CitizenType();
    public notification: CitizenType = new CitizenType();
}

export class CitizenType {
    public citizen: NotiDetail = new NotiDetail();
    public officer: NotiDetail = new NotiDetail();
}

export class NotiDetail {
    public id: string = '';
    public templateId: string = '';
    public enable: boolean = true;
    public send: boolean = false;
    public content: string = '';
    public title: string = '';
    public to: any[] = [];
    public attachment: File[] = [];
    public canEdit: boolean = true;
    public isEdit: boolean = false;
    public disabled: boolean = false;
    public attachmentFile: any;
}

export class NotifyDialogClose {
    public status: boolean = true;
    public notify: NotifyData = new NotifyData();
    public dossierData: NotifyContentParameter;
    public noGateWayNextFlowEnd: boolean = true;
    public requirePaymentCTDT: boolean = false;
    constructor(ts: boolean, noti: NotifyData, dossier: NotifyContentParameter, gateway: boolean) {
        this.status = ts;
        this.notify = noti;
        this.noGateWayNextFlowEnd = gateway;
        this.dossierData = dossier;
    };
}

export class NotifyContentParameter {
    public parameters: NotifyContentParameterData;
    constructor(parameters: NotifyContentParameterData) {
        this.parameters = parameters;
    }
}

export class NotifyContentParameterData {
    constructor(
        public sector: string,
        public procedure: string,
        public agency: string,
        public agencyId: string,
        public applicantFullname: string,
        public officerFullname: string,
        public subsystem: string,
        public dossierCode: string,
        public nextTask: string,
        public dossierSearchPadsvcUrl: string,
        public dossierDetailPadsvcUrl: string,
        public dossierSearchUrl: string,
        public dossierDetailUrl: string,
        public returnMethod: string,
        public receivingDate: string,
        public appointmentDate: string,
        public dossierFee?: string,
        public nextStatus?: string,
        public dossierStatus?: string,
        // public currentTask: string
        public reason?: string,
        public receivingDue?: string,
        public processingDue?: string,
        public date?:string,
        public phut?:string,
        public gio?:string,
        public functionType?:string,
        public pureReceivingDate?: string, //ISOTIME 
        public pureAppointmentDate?: string, //ISOTIME 
        public deadlineForPaymentDossierDueDate?: string
    ){}
}

export class ConfigProcessNotify {
    constructor(
        public applyOnline: FunctionConfig,
        public additionalRequirement: FunctionConfig,
        public receptionOnline: FunctionConfig,
        public suspendDossier: FunctionConfig,
        public resumeDossier: FunctionConfig,
        public receptionReceiving: FunctionConfig,
        public extendProcessingDossier: FunctionConfig,
        public requestWithdrawDossier: FunctionConfig
    ) { }
}

export class IdNameTemplae {
    constructor(
        public id: string,
        public name: string,
        public templateId: string,
        public defaultSend: boolean
    ) { }
}

export class FunctionConfig {
    constructor(
        public sms: IdNameTemplae,
        public email: IdNameTemplae,
        public zalo: IdNameTemplae
    ) { }
}

export class ConfigOutput {
    constructor(
        public funtionType: string,
        public data: FunctionConfig
    ) { }
}

export class CheckSendNotify {
    constructor(
        public id: string,
        public phone: string,
        public email: string,
        public currentTask: any,
        public procedureProcessDefinitionId: string,
        public contentParams: NotifyContentParameter,
        public renewContent: boolean = true,
        public updateSend: boolean = false
    ){}
}

export class ConfirmSendNotify {
    constructor(
        public confirm: boolean,
        public renewContent: boolean,
        public extend: object
    ){}
}
