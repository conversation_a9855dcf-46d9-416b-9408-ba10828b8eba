import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { ApiProviderService } from 'src/app/core/service/api-provider.service';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class ConfigService {

  constructor(
    private http: HttpClient,
    private apiProviderService: ApiProviderService
  ) { }

  private procedurePath = this.apiProviderService.getUrl('digo', 'basepad') + '/procedure/';
  private agencyPath = this.apiProviderService.getUrl('digo', 'basedata') + '/agency/';
  private sectorPath = this.apiProviderService.getUrl('digo', 'basepad') + '/sector/';
  private tagPath = this.apiProviderService.getUrl('digo', 'basecat') + '/tag/';
  private procostPath = this.apiProviderService.getUrl('digo', 'basepad') + '/procost/';
  private procostTypePath = this.apiProviderService.getUrl('digo', 'basepad') + '/procost-type/';
  private procedureFormPath = this.apiProviderService.getUrl('digo', 'basepad') + '/procedure-form/';
  private formPath = this.apiProviderService.getUrl('digo', 'basepad') + '/form/';
  private filePath = this.apiProviderService.getUrl('digo', 'fileman') + '/file/';
  private processPath = this.apiProviderService.getUrl('digo', 'basepad') + '/procedure-process-definition/';
  private modelingPath = this.apiProviderService.getUrl('digo', 'modeling') + '/v1/models/';
  private actModelingContentAPI = this.apiProviderService.getUrl('digo', 'bpm') + '/process-definition/models/';
  // private actModelingContentAPI = 'http://localhost:8080/process-definition/models/';
  private bpmProcessPath = this.apiProviderService.getUrl('digo', 'bpm') + '/process-definition/';

  private procedureConfigPath = this.apiProviderService.getUrl('digo', 'basepad') + '/procedure-config/';
  private agencyConfigPath = this.apiProviderService.getUrl('digo', 'basepad') + '/agency-config/';
  private agencyConfigOfKioskPath = this.apiProviderService.getUrl('digo', 'basepad') + '/kiosk/agency-config/';
  private defaultConfigPath = this.apiProviderService.getUrl('digo', 'basepad') + '/default-config/';
  private defaultConfigOfKioskPath = this.apiProviderService.getUrl('digo', 'basepad') + '/kiosk/default-config/';
  private basecatPath = this.apiProviderService.getUrl('digo', 'basecat');
  private reporterPath = this.apiProviderService.getUrl('digo', 'reporter');
  private pathLocal = 'http://localhost:8080/';
  private basepadPath = this.apiProviderService.getUrl('digo', 'basepad') + '/';
// new request
  getListProcedureConfigByTemplate(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.basepadPath + 'procedure-config/template/--list' + searchString, { headers });
  }
  getListAgencyConfigByTemplate(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.basepadPath + 'agency-config/template/--list' + searchString, { headers });
  }

  getListTypeTemplate(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.basecatPath + '/tag/--by-category-id?category-id=5f5b27924e1bd312a6f3ae1e&sort=order' + searchString,
     { headers });
  }

  getDetailTemplate(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.reporterPath + '/template/' + id, { headers });
  }

  getListTemplate(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.reporterPath + '/template/' + searchString, { headers });
  }

  getListProcedureConfig(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.procedureConfigPath + searchString, { headers });
  }

  getListProcedureConfigPattern(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.procedureConfigPath + '/pattern/--list' + searchString, { headers });
  }

  getListProcedureConfigBusiness(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.procedureConfigPath + '/bussiness/--list' + searchString, { headers });
  }
  getListProcedureConfigTemplate(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.procedureConfigPath + '/template/--list' + searchString, { headers });
  }
  getListAgencyConfig(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.agencyConfigPath + searchString, { headers });
  }

  getListAgencyConfigPattern(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.agencyConfigPath + '/pattern/--list' + searchString, { headers });
  }
  getListAgencyConfigTemplate(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.agencyConfigPath + '/template/--list' + searchString, { headers });
  }
  getListAllDefaultConfig(): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.defaultConfigPath + '--all', { headers });
  }
  getFile(id): Observable<any>{
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/pdf');
    return this.http.get(this.filePath + id, { responseType: 'blob' as 'json' });
  }

  getListPattern(searchString): Observable<any>{
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.basecatPath + '/pattern/' + searchString, { headers });
  }

  getDetailPattern(id): Observable<any>{
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.basecatPath + '/pattern/' + id, { headers });
  }

  getDetailAgency(id): Observable<any>{
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.agencyPath + id, { headers });
  }

  getListProcedure(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.procedurePath + searchString, { headers });
  }
  getListAgency(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.agencyPath + searchString, { headers });
  }
  getListSector(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.sectorPath + searchString, { headers }).pipe();
  }
  getProcedureDetailVn(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', 'vn');
    return this.http.get(this.procedurePath + id, { headers });
  }
  getProcedureDetailEn(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', 'en');
    return this.http.get(this.procedurePath + id, { headers });
  }
  postProcedureConfig(requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.post<any>(this.procedureConfigPath, requestBody, { headers });
  }
  postAgencyConfig(requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.post<any>(this.agencyConfigPath, requestBody, { headers });
  }
  postDefaultConfig(requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.post<any>(this.defaultConfigPath, requestBody, { headers });
  }
  deleteProcedureConfig(id: string) {
    let headers = new HttpHeaders();
    headers = headers.append('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.delete(this.procedureConfigPath + id, { headers });
  }
  deleteProcedureConfigTemplate(id: string) {
    let headers = new HttpHeaders();
    headers = headers.append('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.delete(this.procedureConfigPath + 'delete-template/' + id, { headers });
  }
  deleteProcedureConfigPattern(id: string) {
    let headers = new HttpHeaders();
    headers = headers.append('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.delete(this.procedureConfigPath + 'delete-pattern/' + id, { headers });
  }

  deleteProcedureConfigBusiness(id: string) {
    let headers = new HttpHeaders();
    headers = headers.append('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.delete(this.procedureConfigPath + 'delete-bussiness/' + id, { headers });
  }
  deleteAgencyConfig(id: string) {
    let headers = new HttpHeaders();
    headers = headers.append('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.delete(this.agencyConfigPath + id, { headers });
  }

  deleteAgencyConfigPattern(id: string) {
    let headers = new HttpHeaders();
    headers = headers.append('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.delete(this.agencyConfigPath + 'delete-pattern/' + id, { headers });
  }

  deleteAgencyConfigTemplate(id: string) {
    let headers = new HttpHeaders();
    headers = headers.append('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.delete(this.agencyConfigPath + 'delete-template/' + id, { headers });
  }
  deleteDefaultConfig(id: string) {
    let headers = new HttpHeaders();
    headers = headers.append('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.delete(this.defaultConfigPath + id, { headers });
  }
  putProcedureConfig(id, data): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.put<any>(this.procedureConfigPath + id, data, { headers });
  }

  putDefaultConfig(id, data): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.put<any>(this.defaultConfigPath + id, data, { headers });
  }

  putAgencyConfig(id, data): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.put<any>(this.agencyConfigPath + id, data, { headers });
  }

  getListAgencyTag(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.agencyPath + 'name+tag' + searchString, { headers });
  }
// old request


  getListFullAgency(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.agencyPath + searchString, { headers });
  }

  getProcedureDetail(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.procedurePath + id, { headers });
  }

  getListTagByCategoryId(id: string, page: number, size: number, sort: string): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    // tslint:disable-next-line: max-line-length
    return this.http.get(this.tagPath + '--by-category-id?category-id=' + id + '&page=' + page + '&size=' + size + '&sort=' + sort, { headers }).pipe();
  }

  postProcedure(requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.post<any>(this.procedurePath, requestBody, { headers });
  }

  putProcedure(id, requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.put<any>(this.procedurePath + id, requestBody, { headers });
  }

  deleteProcedure(id: string) {
    let headers = new HttpHeaders();
    headers = headers.append('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.delete(this.procedurePath + id, { headers });
  }

  getProcedureProcost(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.procostPath + searchString, { headers });
  }

  getListProcostType(keyword: string, page: number, size: number): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    // tslint:disable-next-line: max-line-length
    return this.http.get(this.procostTypePath + '?keyword=' + keyword + '&page=' + page + '&size=' + size, { headers }).pipe();
  }

  postProcost(requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.post<any>(this.procostPath, requestBody, { headers });
  }

  getProcostDetail(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.procostPath + id, { headers });
  }

  putProcost(id, requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.put<any>(this.procostPath + id, requestBody, { headers });
  }

  deleteProcost(id: string) {
    let headers = new HttpHeaders();
    headers = headers.append('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.delete(this.procostPath + id, { headers });
  }

  putAssignedAgency(id, requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.put<any>(this.procedurePath + id + '/children', requestBody, { headers });
  }

  getProcedureForm(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.procedureFormPath + searchString, { headers });
  }

  getProcedureFormDetail(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.procedureFormPath + id, { headers });
  }


  getListForm(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.formPath + searchString, { headers });
  }

  postNewForm(requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.post<any>(this.formPath, requestBody, { headers });
  }

  uploadFiles(imgFile): Observable<any> {
    const formData: FormData = new FormData();
    const file: File = imgFile;
    formData.append('file', file, file.name);
    return this.http.post(this.filePath, formData).pipe();
  }

  uploadMultiFile(imgFiles, accountId): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.append('Accept', 'application/json');
    const formData: FormData = new FormData();
    imgFiles.forEach(files => {
      const file: File = files;
      formData.append('files', file, file.name);
    });
    formData.append('account-id', accountId);
    return this.http.post<any>(this.filePath + '--multiple', formData, { headers });
  }


  getFileNameSize(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.filePath + id + '/filename+size', { headers });
  }

  postProcedureForm(requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.post<any>(this.procedureFormPath, requestBody, { headers });
  }

  putProcedureForm(id, requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.put<any>(this.procedureFormPath + id, requestBody, { headers });
  }

  downloadFile(id, idCheck?): Observable<any> {
    let headers = new HttpHeaders({'Cache-Control': 'no-cache',
    'Pragma': 'no-cache'});
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    // id check co the la dossierID hoac procedureId
    if(!!idCheck){
      return this.http.get(this.filePath + id + '?dossier-id=' + idCheck, { responseType: 'blob' as 'json' }).pipe();
    }else{
      return this.http.get(this.filePath + id, { responseType: 'blob' as 'json' }).pipe();
    }
  }

  deleteFile(id) {
    return this.http.delete<any>(this.filePath + id).pipe();
  }

  deleteForm(id: string) {
    let headers = new HttpHeaders();
    headers = headers.append('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.delete(this.procedureFormPath + id, { headers });
  }

  getListProcedureProcess(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.processPath + searchString, { headers });
  }

  getModelDeploy(modelId) {
    const headers = new HttpHeaders().set('Accept', 'application/json');
    return this.http.get(this.actModelingContentAPI + modelId + '/content', { headers, responseType: 'blob' });
  }

  getUrlModel(modelId) {
    return this.actModelingContentAPI + modelId + '/content';
  }

  deleteProcess(id: string) {
    let headers = new HttpHeaders();
    headers = headers.append('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.delete(this.processPath + id, { headers });
  }

  getProcedureProcessDetail(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.processPath + id, { headers });
  }

  putProcedureProcessDetail(id, requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.put<any>(this.processPath + id, requestBody, { headers });
  }

  getListBPMProcess(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.bpmProcessPath + searchString, { headers });
  }

  getListAgencyConfigOfKioskByTemplate(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.agencyConfigOfKioskPath + 'template/--list' + searchString, { headers });
  }

  postAgencyConfigOfKiosk(requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.post<any>(this.agencyConfigOfKioskPath, requestBody, { headers });
  }

  putAgencyConfigOfKiosk(id, data): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.put<any>(this.agencyConfigOfKioskPath + id, data, { headers });
  }

  getListAgencyConfigOfKiosk(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.agencyConfigOfKioskPath + searchString, { headers });
  }

  getListAgencyConfigTemplateOfKiosk(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.agencyConfigOfKioskPath + 'template/--list' + searchString, { headers });
  }

  deleteAgencyConfigTemplateOfKiosk(id: string) {
    let headers = new HttpHeaders();
    headers = headers.append('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.delete(this.agencyConfigOfKioskPath + 'delete-template/' + id, { headers });
  }

  getListAllDefaultConfigOfKiosk(): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.defaultConfigOfKioskPath + '--all', { headers });
  }

  putDefaultConfigOfKiosk(id, data): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.put<any>(this.defaultConfigOfKioskPath + id, data, { headers });
  }

  
}
