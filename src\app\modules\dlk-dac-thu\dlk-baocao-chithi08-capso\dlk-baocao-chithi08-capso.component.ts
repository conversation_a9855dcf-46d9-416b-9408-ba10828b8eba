import { DatePipe } from '@angular/common';
import { AfterViewInit, ChangeDetectorRef, Component, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { MatSelect } from '@angular/material/select';
import { MatTableDataSource } from '@angular/material/table';
import { DeploymentService } from 'data/service/deployment.service';
import { Workbook } from 'exceljs';
import * as fs from 'file-saver';
import { Subject } from 'rxjs';
import { EnvService } from 'src/app/core/service/env.service';
import { DLKStatisticsService } from 'src/app/data/service/dlk-statistics/dlk-statistics.service';
import { MainService } from 'src/app/data/service/main/main.service';
import { ProcedureService } from 'src/app/data/service/procedure/procedure.service';
import { SnackbarService } from 'src/app/data/service/snackbar/snackbar.service';
import * as tUtils from 'src/app/data/service/thoai.service';


export interface Agency {
  id: string;
  name: string;
}

import { MatDialog } from '@angular/material/dialog';
import { BaocaoChithi08DialogComponent } from '../dialogs/baocao-chithi08-dialog/baocao-chithi08-dialog.component';
import {
  DossierDetailComponent,
  DossierDetailDialogModel
} from '../dialogs/view-detail.component';
@Component({
  selector: 'app-dlk-baocao-chithi08-capso',
  templateUrl: './dlk-baocao-chithi08-capso.component.html',
  styleUrls: [
    './dlk-baocao-chithi08-capso.component.scss',
    '/src/app/app.component.scss',
    '/src/app/shared/scss/form-field-outline.scss'
  ]
})
export class DlkBaoCaoChiThi08CapSoComponent implements OnInit, AfterViewInit, OnDestroy {

  nowDate = tUtils.newDate();

  // tslint:disable-next-line: max-line-length
  toDate = this.nowDate.getFullYear() + '-' + ((this.nowDate.getMonth() + 1) <= 9 ? '0' + (this.nowDate.getMonth() + 1) : (this.nowDate.getMonth() + 1)) + '-' + ((this.nowDate.getDate() <= 9) ? ('0' + this.nowDate.getDate()) : this.nowDate.getDate());
  // tslint:disable-next-line: max-line-length
  fromDate = this.nowDate.getFullYear() + '-' + ((this.nowDate.getMonth() + 1) <= 9 ? '0' + (this.nowDate.getMonth() + 1) : (this.nowDate.getMonth() + 1)) + '-01';

  keyword = '';
  config = this.envService.getConfig();

  selectedLang = localStorage.getItem('language') || 'vi';
  selectedLangId = Number(localStorage.getItem('languageId')) || 228;
  pageTitle = {
    vi: 'Báo cáo chỉ thị 08 cấp sở',
    en: 'Bao cao chi thi 08 cap so'
  };

  protected onDestroy = new Subject<void>();
  // =================== old
  env: any = this.deploymentService.getAppDeployment()?.env;
  ELEMENTDATA: any[] = [];
  dataSource: MatTableDataSource<any>;
  // listSector: Array<any> = [];
  keySearchSectorAgency = '';
  // Search Sector
  keywordSector = '';
  totalPagesSector = 0;
  currentPageSector = 0;
  pageSizeSector = 100;
  searchString = "";

  parentAgency = '';
  agencyId = '';
  agencyName = '';
  userAgency?: any = JSON.parse(localStorage.getItem('userAgency'));
  userAgencyCount?: any = JSON.parse(localStorage.getItem('listAgencyUser'))?.length || 0;
  ELEMENTDATAALL: any[] = [];
  dataSourceAll: MatTableDataSource<any>;

  listHoSoCapTinh = [];
  listHoSoCapHuyen = [];
  TongCong: any;
  TongCapHuyen: any;
  TongCongLK: any;
  TongCapHuyenLK: any;
  listHoSo = [];
  listThuTucHoSo = [];
  listHoSoLK = [];
  listThuTucHoSoLK = [];
  //procedureAgencyLevel = this.deploymentService.env.statistics.procedureAgencyLevel;
  Agency = this.deploymentService.env.OS_DLK;
  size = 10;
  pageIndex = 1;
  page = 1;
  countResult = 0;
  pgSizeOptions = this.config.pageSizeOptions;
  isAdmin?: boolean = sessionStorage.getItem('oneGateAdminMaster') === '1';
  paramsQuery = {
    page: 0,
    size: 10,
    fromDate: '',
    toDate: '',
    fromLKDate: '',
    toLKDate: '',
    agency: '',
    applyMethod: '',
    receivingKind: '',
    sector: '',
    keyword: '',
    procedure: '',
    sortId: '',
    isTTPVHCC: 1,
    agencyId: null,
    agencyLevel: "5f39f42d5224cf235e134c5a"
  };
  paramsDossier = {
    page: 0,
    size: 10,
    fromDate: null,
    toDate: null,
    agencyId: null,
    applyMethodId: null,
    receivingKind: null,
    hinhThucNop: null,
    keyword: '',
    dossierStatusId: null,
    procedureLevelId: null,
    code: ''
  };
  listAgencyAccept = [];
  listAgency = [];
  keywordAgencyAccept = '';
  totalPagesAgencyAccept = 0;
  currentPageAgencyAccept = 0;
  pageSizeAgencyAccept = 50;
  startDateCumulative = new Date();
  endDateCumulative = new Date();
  startDate = new Date();
  endDate = new Date();
  flagTTPVHCC = true;
  timeOutAgencyAccept: any = null;
  listSectorfillter: any[] = [];
  listSector: any[] = [];
  listSectorActive = [];
  listProcedure: any[] = [];
  listProcedurefillter: any[] = [];
  totalPagesProcedure = 0;
  currentPageProcedure = 0;
  searchProcedureKeyword = "";
  listHinhThucNhan: any[] = [];
  EXCEL_EXTENSION = '.xlsx';
  EXCEL_TYPE = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8';
  @ViewChild('procedureMatSelectInfiniteScroll', { static: true }) procedureMatSelectInfiniteScroll: MatSelect;

  listProp: any[] = [
    { name: 'p02_tiepnhan_tong', type: 'hs' },
    { name: 'p03_tiepnhan_tructiep', type: 'hs' },
    { name: 'p04_tiepnhan_bcci', type: 'hs' },
    { name: 'p05_tiepnhan_tructuyen_motphan', type: 'hs' },
    { name: 'p06_tiepnhan_tructuyen_motphan_tyle', type: 'tl' },
    { name: 'p07_tiepnhan_tructuyen_toantrinh', type: 'hs' },
    { name: 'p08_tiepnhan_tructuyen_toantrinh_tyle', type: 'tl' },
    { name: 'p09_tiepnhan_tructuyen_motphan_toantrinh', type: 'hs' }, // site v1 ko cho phep hien thi ds ~ type: tl
    { name: 'p10_tiepnhan_tructuyen_motphan_toantrinh_tyle', type: 'tl' },
    { name: 'p11_tiepnhan_tong_igate', type: 'hs' },
    { name: 'p12_tiepnhan_tong_igate_tyle', type: '', fixValue: '100' },
    { name: 'p13_traketqua_tong', type: 'hs' },
    { name: 'p14_traketqua_tructiep', type: 'hs' },
    { name: 'p15_traketqua_tructiep_tyle', type: 'tl' },
    { name: 'p16_traketqua_bcci', type: 'hs' },
    { name: 'p17_traketqua_bcci_tyle', type: 'tl' },
    { name: 'p18_traketqua_tructuyen', type: 'hs' },
    { name: 'p19_traketqua_tructuyen_tyle', type: 'tl' },
    { name: 'p20_traketqua_tong_igate', type: 'hs' },
    { name: 'p21_traketqua_sudung_kyso', type: '', fixValue: '-' }, // -: khong phat sinh
    { name: 'p22_dvctt_tong', type: 'tt' }, // tt: thu tuc
    { name: 'p23_dvctt_phatsinh_hoso', type: 'tt' },
    { name: 'p24_dvctt_phatsinh_hoso_tyle', type: 'tl' },
    { name: 'p25_dvctt_motphan', type: 'tt' },
    { name: 'p26_dvctt_motphan_phatsinh_hoso', type: 'tt' },
    { name: 'p27_dvctt_motphan_phatsinh_hoso_tyle', type: 'tl' },
    { name: 'p28_dvctt_toantrinh', type: 'tt' },
    { name: 'p29_dvctt_toantrinh_phatsinh_hoso', type: 'tt' },
    { name: 'p30_dvctt_toantrinh_phatsinh_hoso_tyle', type: 'tl' },
    { name: 'p31_tthc_bcci_tong', type: 'tt' }, // thu tuc - site v1 dang csrf token invaid
    { name: 'p32_tthc_bcci_phatsinh_hoso', type: 'tt' }, // thu tuc - site v1 dang csrf token invaid
    { name: 'p33_tthc_bcci_phatsinh_hoso_tyle', type: 'tl' }, // ty le
    { name: 'p34_tthc_tttt_tong', type: 'tt' }, // thu tuc - site v1 dang csrf token invaid
    { name: 'p35_tthc_tttt_hoso_phatsinh', type: 'hs' }, // ho so - site v1 dang csrf token invaid
    { name: 'p36_tthc_tttt_phatsinh_hoso', type: 'tt' }, // thu tuc - site v1 dang csrf token invaid
    { name: 'p37_tthc_tttt_phatsinh_hoso_tyle', type: 'tl' },
    { name: 'p38_tthc_tttt_phatsinh_hoso_dagiaiquyet', type: 'hs' }, // ho so - site v1 dang csrf token invaid
    { name: 'p39_tthc_tttt_phatsinh_hoso_dagiaiquyet_tyle', type: 'tl' },
  ];

  constructor(
    private envService: EnvService,
    public datepipe: DatePipe,
    private mainService: MainService,
    private cdRef: ChangeDetectorRef,
    private procedureService: ProcedureService,
    private datePipe: DatePipe,
    private deploymentService: DeploymentService,
    private dlkStatisticService: DLKStatisticsService,
    private snackbarService: SnackbarService,
    private dialog: MatDialog
  ) {
    this.dataSource = new MatTableDataSource(this.ELEMENTDATA);
    this.dataSourceAll = new MatTableDataSource(this.ELEMENTDATAALL);
  }

  ngOnInit(): void {
    this.mainService.setPageTitle(this.pageTitle[localStorage.getItem('language')]);
    this.startDate = new Date(this.fromDate);
    this.startDateCumulative = new Date(this.fromDate);
    this.endDate = new Date(this.toDate);
    if (this.userAgency !== null && this.userAgency !== undefined) {
      if (!!this.userAgency.parent && !!this.userAgency.parent.id) {
        this.parentAgency = this.userAgency.parent.id;
        this.agencyId = this.userAgency.id;
      } else {
        this.parentAgency = this.userAgency.id;
        this.agencyId = this.userAgency.id;
      }
      this.parentAgency = this.Agency.rootAgencyId; // dlk
      this.keySearchSectorAgency = '&agency-id=' + this.agencyId;
      this.searchString = "?arr-parent-id=" + this.parentAgency;
    }
  }

  ngAfterViewInit() {
    this.cdRef.detectChanges();
    // this.getListAgencyAccept(this.Agency.rootAgencyId, '', 0, 10000);
    this.getListAgencyAccept(this.Agency.rootAgencyId, '', 0, 2000, []);
  }

  ngOnDestroy() {
    this.onDestroy.next();
    this.onDestroy.complete();
  }

  colToLetter(number) {
    let result = '';
    // number = number - 1; // If starting from 1
    do {
      const letter = String.fromCharCode(65 + (number % 26));
      result = letter + result;
      number = Math.floor(number / 26) - 1;
    } while (number >= 0)
    return result.toLowerCase();
  }

  thongKe() {
    this.waitingDownloadExcel = true;
    this.paramsQuery.page = 0;
    this.page = 1;
    this.getListHoSo();
  }

  paginate(event) {
    this.paramsQuery.page = event;
  }

  getParentId(): string | null {
    const userAgency = JSON.parse(localStorage.getItem('userAgency'));

    if (userAgency) {
      if (!!userAgency.parent && !!userAgency.parent.id && !this.isAdmin) {
        return userAgency.parent.id;
      }
      else if (userAgency.id !== this.config.rootAgency.id || this.isAdmin) {
        return userAgency.id;
      }
    }
    return null;
  }

  //"60c868a4289bad69c7cbffea" tỉnh kontum
  getListHoSo() {
    if (this.validateForm()) {
      let promise = new Promise((resolve, reject) => {
        this.paramsQuery.fromDate = (this.startDate ? this.datePipe.transform(this.startDate, 'yyyy-MM-dd') + 'T00:00:00.000Z' : '');
        this.paramsQuery.toDate = (this.endDate ? this.datePipe.transform(this.endDate, 'yyyy-MM-dd') + 'T23:59:59.999Z' : '');
        this.paramsQuery.agencyId = this.agencyId;

        // Lấy số liệu báo cáo CT08 cấp Sở
        this.dlkStatisticService.getDataBaocaoCT08SH(this.paramsQuery.agencyId, this.paramsQuery.agencyLevel, this.paramsQuery.fromDate, this.paramsQuery.toDate, "SO").subscribe(res => {
          // this.waitingDownloadExcel = false;
          this.listHoSo = res;

          setTimeout(() => {
            this.BuilData();
          }, 1000);
        }, err => {
          console.log(err);
          this.waitingDownloadExcel = false;
        });

      });
      return promise;
    } else {
      this.waitingDownloadExcel = false;
    }
  }

  BuilData() {
    this.ListMain = [];

    this.TongCong = {
      p02_tiepnhan_tong_tky: 0,
      p03_tiepnhan_tructiep_tky: 0,
      p04_tiepnhan_bcci_tky: 0,
      p05_tiepnhan_tructuyen_motphan_tky: 0,
      p06_tiepnhan_tructuyen_motphan_tyle_tky: 0,
      p07_tiepnhan_tructuyen_toantrinh_tky: 0,
      p08_tiepnhan_tructuyen_toantrinh_tyle_tky: 0,
      p09_tiepnhan_tructuyen_motphan_toantrinh_tky: 0,
      p10_tiepnhan_tructuyen_motphan_toantrinh_tyle_tky: 0,
      p11_tiepnhan_tong_igate_tky: 0,
      p12_tiepnhan_tong_igate_tyle_tky: 0,
      p13_traketqua_tong_tky: 0,
      p14_traketqua_tructiep_tky: 0,
      p15_traketqua_tructiep_tyle_tky: 0,
      p16_traketqua_bcci_tky: 0,
      p17_traketqua_bcci_tyle_tky: 0,
      p18_traketqua_tructuyen_tky: 0,
      p19_traketqua_tructuyen_tyle_tky: 0,
      p20_traketqua_tong_igate_tky: 0,
      p21_traketqua_sudung_kyso_tky: 0,
      p22_dvctt_tong_tky: 0,
      p23_dvctt_phatsinh_hoso_tky: 0,
      p24_dvctt_phatsinh_hoso_tyle_tky: 0,
      p25_dvctt_motphan_tky: 0,
      p26_dvctt_motphan_phatsinh_hoso_tky: 0,
      p27_dvctt_motphan_phatsinh_hoso_tyle_tky: 0,
      p28_dvctt_toantrinh_tky: 0,
      p29_dvctt_toantrinh_phatsinh_hoso_tky: 0,
      p30_dvctt_toantrinh_phatsinh_hoso_tyle_tky: 0,
      p31_tthc_bcci_tong_tky: 0,
      p32_tthc_bcci_phatsinh_hoso_tky: 0,
      p33_tthc_bcci_phatsinh_hoso_tyle_tky: 0,
      p34_tthc_tttt_tong_tky: 0,
      p35_tthc_tttt_hoso_phatsinh_tky: 0,
      p36_tthc_tttt_phatsinh_hoso_tky: 0,
      p37_tthc_tttt_phatsinh_hoso_tyle_tky: 0,
      p38_tthc_tttt_phatsinh_hoso_dagiaiquyet_tky: 0,
      p39_tthc_tttt_phatsinh_hoso_dagiaiquyet_tyle_tky: 0,
    };

    // ho so toan huyen theo linh vuc
    for (let i = 0; i < this.listSector.length; i++) {
      let sectorId = this.listSector[i].sectorId;
      let sectorName = this.listSector[i].sectorName;
      let ListThuTuc = this.listProcedure.filter(f => f.sectorId !== null && f.sectorId === sectorId);

      var main = {
        id: sectorId,
        sector: sectorName,
        show: false,
        p02_tiepnhan_tong_tky: 0,
        p03_tiepnhan_tructiep_tky: 0,
        p04_tiepnhan_bcci_tky: 0,
        p05_tiepnhan_tructuyen_motphan_tky: 0,
        p06_tiepnhan_tructuyen_motphan_tyle_tky: '',
        p07_tiepnhan_tructuyen_toantrinh_tky: 0,
        p08_tiepnhan_tructuyen_toantrinh_tyle_tky: '',
        p09_tiepnhan_tructuyen_motphan_toantrinh_tky: 0,
        p10_tiepnhan_tructuyen_motphan_toantrinh_tyle_tky: '',
        p11_tiepnhan_tong_igate_tky: 0,
        p12_tiepnhan_tong_igate_tyle_tky: '',
        p13_traketqua_tong_tky: 0,
        p14_traketqua_tructiep_tky: 0,
        p15_traketqua_tructiep_tyle_tky: '',
        p16_traketqua_bcci_tky: 0,
        p17_traketqua_bcci_tyle_tky: '',
        p18_traketqua_tructuyen_tky: 0,
        p19_traketqua_tructuyen_tyle_tky: '',
        p20_traketqua_tong_igate_tky: 0,
        p21_traketqua_sudung_kyso_tky: 0,
        p22_dvctt_tong_tky: 0,
        p23_dvctt_phatsinh_hoso_tky: 0,
        p24_dvctt_phatsinh_hoso_tyle_tky: '',
        p25_dvctt_motphan_tky: 0,
        p26_dvctt_motphan_phatsinh_hoso_tky: 0,
        p27_dvctt_motphan_phatsinh_hoso_tyle_tky: '',
        p28_dvctt_toantrinh_tky: 0,
        p29_dvctt_toantrinh_phatsinh_hoso_tky: 0,
        p30_dvctt_toantrinh_phatsinh_hoso_tyle_tky: '',
        p31_tthc_bcci_tong_tky: 0,
        p32_tthc_bcci_phatsinh_hoso_tky: 0,
        p33_tthc_bcci_phatsinh_hoso_tyle_tky: '',
        p34_tthc_tttt_tong_tky: 0,
        p35_tthc_tttt_hoso_phatsinh_tky: 0,
        p36_tthc_tttt_phatsinh_hoso_tky: 0,
        p37_tthc_tttt_phatsinh_hoso_tyle_tky: '',
        p38_tthc_tttt_phatsinh_hoso_dagiaiquyet_tky: 0,
        p39_tthc_tttt_phatsinh_hoso_dagiaiquyet_tyle_tky: '',
        data: []
      }

      for (let j = 0; j < ListThuTuc.length; j++) {
        let ListThuTuctotal = this.listHoSo.filter(f => f.procedureId !== null && f.procedureId === ListThuTuc[j].procedureId);

        var arr = {
          status: ListThuTuc[j].status,
          sectorId: sectorId,
          sectorName: sectorName,
          procedureId: ListThuTuc[j].procedureId,
          procedureName: ListThuTuc[j].procedureName,
          MucDo: ListThuTuc[j].MucDo,
          p02_tiepnhan_tong_tky: 0,
          p03_tiepnhan_tructiep_tky: 0,
          p04_tiepnhan_bcci_tky: 0,
          p05_tiepnhan_tructuyen_motphan_tky: 0,
          p06_tiepnhan_tructuyen_motphan_tyle_tky: 0,
          p07_tiepnhan_tructuyen_toantrinh_tky: 0,
          p08_tiepnhan_tructuyen_toantrinh_tyle_tky: 0,
          p09_tiepnhan_tructuyen_motphan_toantrinh_tky: 0,
          p10_tiepnhan_tructuyen_motphan_toantrinh_tyle_tky: 0,
          p11_tiepnhan_tong_igate_tky: 0,
          p12_tiepnhan_tong_igate_tyle_tky: 0,
          p13_traketqua_tong_tky: 0,
          p14_traketqua_tructiep_tky: 0,
          p15_traketqua_tructiep_tyle_tky: 0,
          p16_traketqua_bcci_tky: 0,
          p17_traketqua_bcci_tyle_tky: 0,
          p18_traketqua_tructuyen_tky: 0,
          p19_traketqua_tructuyen_tyle_tky: 0,
          p20_traketqua_tong_igate_tky: 0,
          p21_traketqua_sudung_kyso_tky: 0,
          p22_dvctt_tong_tky: 0,
          p23_dvctt_phatsinh_hoso_tky: 0,
          p24_dvctt_phatsinh_hoso_tyle_tky: 0,
          p25_dvctt_motphan_tky: 0,
          p26_dvctt_motphan_phatsinh_hoso_tky: 0,
          p27_dvctt_motphan_phatsinh_hoso_tyle_tky: 0,
          p28_dvctt_toantrinh_tky: 0,
          p29_dvctt_toantrinh_phatsinh_hoso_tky: 0,
          p30_dvctt_toantrinh_phatsinh_hoso_tyle_tky: 0,
          p31_tthc_bcci_tong_tky: 0,
          p32_tthc_bcci_phatsinh_hoso_tky: 0,
          p33_tthc_bcci_phatsinh_hoso_tyle_tky: 0,
          p34_tthc_tttt_tong_tky: 0,
          p35_tthc_tttt_hoso_phatsinh_tky: 0,
          p36_tthc_tttt_phatsinh_hoso_tky: 0,
          p37_tthc_tttt_phatsinh_hoso_tyle_tky: 0,
          p38_tthc_tttt_phatsinh_hoso_dagiaiquyet_tky: 0,
          p39_tthc_tttt_phatsinh_hoso_dagiaiquyet_tyle_tky: 0
        }

        if (ListThuTuctotal.length > 0) {
          // Dạng số hồ sơ
          arr.p02_tiepnhan_tong_tky = ListThuTuctotal[0].p02_tiepnhan_tong_tky;
          arr.p03_tiepnhan_tructiep_tky = ListThuTuctotal[0].p03_tiepnhan_tructiep_tky;
          arr.p04_tiepnhan_bcci_tky = ListThuTuctotal[0].p04_tiepnhan_bcci_tky;
          arr.p05_tiepnhan_tructuyen_motphan_tky = ListThuTuctotal[0].p05_tiepnhan_tructuyen_motphan_tky;
          arr.p07_tiepnhan_tructuyen_toantrinh_tky = ListThuTuctotal[0].p07_tiepnhan_tructuyen_toantrinh_tky;
          arr.p09_tiepnhan_tructuyen_motphan_toantrinh_tky = ListThuTuctotal[0].p09_tiepnhan_tructuyen_motphan_toantrinh_tky;
          arr.p11_tiepnhan_tong_igate_tky = ListThuTuctotal[0].p11_tiepnhan_tong_igate_tky;
          arr.p13_traketqua_tong_tky = ListThuTuctotal[0].p13_traketqua_tong_tky;
          arr.p14_traketqua_tructiep_tky = ListThuTuctotal[0].p14_traketqua_tructiep_tky;
          arr.p16_traketqua_bcci_tky = ListThuTuctotal[0].p16_traketqua_bcci_tky;
          arr.p18_traketqua_tructuyen_tky = ListThuTuctotal[0].p18_traketqua_tructuyen_tky;
          arr.p20_traketqua_tong_igate_tky = ListThuTuctotal[0].p20_traketqua_tong_igate_tky;
          arr.p21_traketqua_sudung_kyso_tky = ListThuTuctotal[0].p21_traketqua_sudung_kyso_tky;
          arr.p22_dvctt_tong_tky = ListThuTuctotal[0].p22_dvctt_tong_tky;
          arr.p23_dvctt_phatsinh_hoso_tky = ListThuTuctotal[0].p23_dvctt_phatsinh_hoso_tky;
          arr.p25_dvctt_motphan_tky = ListThuTuctotal[0].p25_dvctt_motphan_tky;
          arr.p26_dvctt_motphan_phatsinh_hoso_tky = ListThuTuctotal[0].p26_dvctt_motphan_phatsinh_hoso_tky;
          arr.p28_dvctt_toantrinh_tky = ListThuTuctotal[0].p28_dvctt_toantrinh_tky;
          arr.p29_dvctt_toantrinh_phatsinh_hoso_tky = ListThuTuctotal[0].p29_dvctt_toantrinh_phatsinh_hoso_tky;
          arr.p31_tthc_bcci_tong_tky = ListThuTuctotal[0].p31_tthc_bcci_tong_tky;
          arr.p32_tthc_bcci_phatsinh_hoso_tky = ListThuTuctotal[0].p32_tthc_bcci_phatsinh_hoso_tky;
          arr.p34_tthc_tttt_tong_tky = ListThuTuctotal[0].p34_tthc_tttt_tong_tky;
          arr.p35_tthc_tttt_hoso_phatsinh_tky = ListThuTuctotal[0].p35_tthc_tttt_hoso_phatsinh_tky;
          arr.p36_tthc_tttt_phatsinh_hoso_tky = ListThuTuctotal[0].p36_tthc_tttt_phatsinh_hoso_tky;
          arr.p38_tthc_tttt_phatsinh_hoso_dagiaiquyet_tky = ListThuTuctotal[0].p38_tthc_tttt_phatsinh_hoso_dagiaiquyet_tky;

          // Dạng Tỷ lệ
          arr.p06_tiepnhan_tructuyen_motphan_tyle_tky = ListThuTuctotal[0].p06_tiepnhan_tructuyen_motphan_tyle_tky;
          arr.p08_tiepnhan_tructuyen_toantrinh_tyle_tky = ListThuTuctotal[0].p08_tiepnhan_tructuyen_toantrinh_tyle_tky;
          arr.p10_tiepnhan_tructuyen_motphan_toantrinh_tyle_tky = ListThuTuctotal[0].p10_tiepnhan_tructuyen_motphan_toantrinh_tyle_tky;
          arr.p12_tiepnhan_tong_igate_tyle_tky = ListThuTuctotal[0].p12_tiepnhan_tong_igate_tyle_tky;
          arr.p15_traketqua_tructiep_tyle_tky = ListThuTuctotal[0].p15_traketqua_tructiep_tyle_tky;
          arr.p17_traketqua_bcci_tyle_tky = ListThuTuctotal[0].p17_traketqua_bcci_tyle_tky;
          arr.p19_traketqua_tructuyen_tyle_tky = ListThuTuctotal[0].p19_traketqua_tructuyen_tyle_tky;
          arr.p24_dvctt_phatsinh_hoso_tyle_tky = ListThuTuctotal[0].p24_dvctt_phatsinh_hoso_tyle_tky;
          arr.p27_dvctt_motphan_phatsinh_hoso_tyle_tky = ListThuTuctotal[0].p27_dvctt_motphan_phatsinh_hoso_tyle_tky;
          arr.p30_dvctt_toantrinh_phatsinh_hoso_tyle_tky = ListThuTuctotal[0].p30_dvctt_toantrinh_phatsinh_hoso_tyle_tky;
          arr.p33_tthc_bcci_phatsinh_hoso_tyle_tky = ListThuTuctotal[0].p33_tthc_bcci_phatsinh_hoso_tyle_tky;
          arr.p37_tthc_tttt_phatsinh_hoso_tyle_tky = ListThuTuctotal[0].p37_tthc_tttt_phatsinh_hoso_tyle_tky;
          arr.p39_tthc_tttt_phatsinh_hoso_dagiaiquyet_tyle_tky = ListThuTuctotal[0].p39_tthc_tttt_phatsinh_hoso_dagiaiquyet_tyle_tky;

          // Dữ liệu lĩnh vực
          //// Dạng số hồ sơ
          // main.p02_tiepnhan_tong_tky += arr.p02_tiepnhan_tong_tky;
          main.p02_tiepnhan_tong_tky = this.getDataMain(main.p02_tiepnhan_tong_tky, arr.p02_tiepnhan_tong_tky, true);
          main.p03_tiepnhan_tructiep_tky = this.getDataMain(main.p03_tiepnhan_tructiep_tky, arr.p03_tiepnhan_tructiep_tky, true);
          main.p04_tiepnhan_bcci_tky = this.getDataMain(main.p04_tiepnhan_bcci_tky, arr.p04_tiepnhan_bcci_tky, true);
          main.p05_tiepnhan_tructuyen_motphan_tky = this.getDataMain(main.p05_tiepnhan_tructuyen_motphan_tky, arr.p05_tiepnhan_tructuyen_motphan_tky, true);
          main.p07_tiepnhan_tructuyen_toantrinh_tky = this.getDataMain(main.p07_tiepnhan_tructuyen_toantrinh_tky, arr.p07_tiepnhan_tructuyen_toantrinh_tky, true);
          main.p09_tiepnhan_tructuyen_motphan_toantrinh_tky = this.getDataMain(main.p09_tiepnhan_tructuyen_motphan_toantrinh_tky, arr.p09_tiepnhan_tructuyen_motphan_toantrinh_tky, true);
          main.p11_tiepnhan_tong_igate_tky = this.getDataMain(main.p11_tiepnhan_tong_igate_tky, arr.p11_tiepnhan_tong_igate_tky, true);
          main.p13_traketqua_tong_tky = this.getDataMain(main.p13_traketqua_tong_tky, arr.p13_traketqua_tong_tky, true);
          main.p14_traketqua_tructiep_tky = this.getDataMain(main.p14_traketqua_tructiep_tky, arr.p14_traketqua_tructiep_tky, true);
          main.p16_traketqua_bcci_tky = this.getDataMain(main.p16_traketqua_bcci_tky, arr.p16_traketqua_bcci_tky, true);
          main.p18_traketqua_tructuyen_tky = this.getDataMain(main.p18_traketqua_tructuyen_tky, arr.p18_traketqua_tructuyen_tky, true);
          main.p20_traketqua_tong_igate_tky = this.getDataMain(main.p20_traketqua_tong_igate_tky, arr.p20_traketqua_tong_igate_tky, true);
          main.p21_traketqua_sudung_kyso_tky = this.getDataMain(main.p21_traketqua_sudung_kyso_tky, arr.p21_traketqua_sudung_kyso_tky, true);
          main.p22_dvctt_tong_tky = this.getDataMain(main.p22_dvctt_tong_tky, arr.p22_dvctt_tong_tky, true);
          main.p23_dvctt_phatsinh_hoso_tky = this.getDataMain(main.p23_dvctt_phatsinh_hoso_tky, arr.p23_dvctt_phatsinh_hoso_tky, true);
          main.p25_dvctt_motphan_tky = this.getDataMain(main.p25_dvctt_motphan_tky, arr.p25_dvctt_motphan_tky, true);
          main.p26_dvctt_motphan_phatsinh_hoso_tky = this.getDataMain(main.p26_dvctt_motphan_phatsinh_hoso_tky, arr.p26_dvctt_motphan_phatsinh_hoso_tky, true);
          main.p28_dvctt_toantrinh_tky = this.getDataMain(main.p28_dvctt_toantrinh_tky, arr.p28_dvctt_toantrinh_tky, true);
          main.p29_dvctt_toantrinh_phatsinh_hoso_tky = this.getDataMain(main.p29_dvctt_toantrinh_phatsinh_hoso_tky, arr.p29_dvctt_toantrinh_phatsinh_hoso_tky, true);
          main.p31_tthc_bcci_tong_tky = this.getDataMain(main.p31_tthc_bcci_tong_tky, arr.p31_tthc_bcci_tong_tky, true);
          main.p32_tthc_bcci_phatsinh_hoso_tky = this.getDataMain(main.p32_tthc_bcci_phatsinh_hoso_tky, arr.p32_tthc_bcci_phatsinh_hoso_tky, true);
          main.p34_tthc_tttt_tong_tky = this.getDataMain(main.p34_tthc_tttt_tong_tky, arr.p34_tthc_tttt_tong_tky, true);
          main.p35_tthc_tttt_hoso_phatsinh_tky = this.getDataMain(main.p35_tthc_tttt_hoso_phatsinh_tky, arr.p35_tthc_tttt_hoso_phatsinh_tky, true);
          main.p36_tthc_tttt_phatsinh_hoso_tky = this.getDataMain(main.p36_tthc_tttt_phatsinh_hoso_tky, arr.p36_tthc_tttt_phatsinh_hoso_tky, true);
          main.p38_tthc_tttt_phatsinh_hoso_dagiaiquyet_tky = this.getDataMain(main.p38_tthc_tttt_phatsinh_hoso_dagiaiquyet_tky, arr.p38_tthc_tttt_phatsinh_hoso_dagiaiquyet_tky, true);

          //Tính tổng công
          this.TongCong.p02_tiepnhan_tong_tky += arr.p02_tiepnhan_tong_tky;
          this.TongCong.p03_tiepnhan_tructiep_tky += arr.p03_tiepnhan_tructiep_tky;
          this.TongCong.p04_tiepnhan_bcci_tky += arr.p04_tiepnhan_bcci_tky;
          this.TongCong.p05_tiepnhan_tructuyen_motphan_tky += arr.p05_tiepnhan_tructuyen_motphan_tky;
          this.TongCong.p07_tiepnhan_tructuyen_toantrinh_tky += arr.p07_tiepnhan_tructuyen_toantrinh_tky;
          this.TongCong.p09_tiepnhan_tructuyen_motphan_toantrinh_tky += arr.p09_tiepnhan_tructuyen_motphan_toantrinh_tky;
          this.TongCong.p11_tiepnhan_tong_igate_tky += arr.p11_tiepnhan_tong_igate_tky;
          this.TongCong.p13_traketqua_tong_tky += arr.p13_traketqua_tong_tky;
          this.TongCong.p14_traketqua_tructiep_tky += arr.p14_traketqua_tructiep_tky;
          this.TongCong.p16_traketqua_bcci_tky += arr.p16_traketqua_bcci_tky;
          this.TongCong.p18_traketqua_tructuyen_tky += arr.p18_traketqua_tructuyen_tky;
          this.TongCong.p20_traketqua_tong_igate_tky += arr.p20_traketqua_tong_igate_tky;
          this.TongCong.p21_traketqua_sudung_kyso_tky += arr.p21_traketqua_sudung_kyso_tky;
          this.TongCong.p22_dvctt_tong_tky += arr.p22_dvctt_tong_tky;
          this.TongCong.p23_dvctt_phatsinh_hoso_tky += arr.p23_dvctt_phatsinh_hoso_tky;
          this.TongCong.p25_dvctt_motphan_tky += arr.p25_dvctt_motphan_tky;
          this.TongCong.p26_dvctt_motphan_phatsinh_hoso_tky += arr.p26_dvctt_motphan_phatsinh_hoso_tky;
          this.TongCong.p28_dvctt_toantrinh_tky += arr.p28_dvctt_toantrinh_tky;
          this.TongCong.p29_dvctt_toantrinh_phatsinh_hoso_tky += arr.p29_dvctt_toantrinh_phatsinh_hoso_tky;
          this.TongCong.p31_tthc_bcci_tong_tky += arr.p31_tthc_bcci_tong_tky;
          this.TongCong.p32_tthc_bcci_phatsinh_hoso_tky += arr.p32_tthc_bcci_phatsinh_hoso_tky;
          this.TongCong.p34_tthc_tttt_tong_tky += arr.p34_tthc_tttt_tong_tky;
          this.TongCong.p35_tthc_tttt_hoso_phatsinh_tky += arr.p35_tthc_tttt_hoso_phatsinh_tky;
          this.TongCong.p36_tthc_tttt_phatsinh_hoso_tky += arr.p36_tthc_tttt_phatsinh_hoso_tky;
          this.TongCong.p38_tthc_tttt_phatsinh_hoso_dagiaiquyet_tky += arr.p38_tthc_tttt_phatsinh_hoso_dagiaiquyet_tky;
        }

        // Dạng tỷ lệ
        //// Tỷ lệ lĩnh vực
        main.p06_tiepnhan_tructuyen_motphan_tyle_tky = this.tinhTyLe(main.p05_tiepnhan_tructuyen_motphan_tky, main.p02_tiepnhan_tong_tky);
        main.p08_tiepnhan_tructuyen_toantrinh_tyle_tky = this.tinhTyLe(main.p07_tiepnhan_tructuyen_toantrinh_tky, main.p02_tiepnhan_tong_tky);
        main.p10_tiepnhan_tructuyen_motphan_toantrinh_tyle_tky = this.tinhTyLe(main.p09_tiepnhan_tructuyen_motphan_toantrinh_tky, main.p02_tiepnhan_tong_tky);
        main.p12_tiepnhan_tong_igate_tyle_tky = this.tinhTyLe(1, 1);
        main.p15_traketqua_tructiep_tyle_tky = this.tinhTyLe(main.p14_traketqua_tructiep_tky, main.p13_traketqua_tong_tky);
        main.p17_traketqua_bcci_tyle_tky = this.tinhTyLe(main.p16_traketqua_bcci_tky, main.p13_traketqua_tong_tky);
        main.p19_traketqua_tructuyen_tyle_tky = this.tinhTyLe(main.p18_traketqua_tructuyen_tky, main.p13_traketqua_tong_tky);
        main.p24_dvctt_phatsinh_hoso_tyle_tky = this.tinhTyLe(main.p23_dvctt_phatsinh_hoso_tky, main.p22_dvctt_tong_tky);
        main.p27_dvctt_motphan_phatsinh_hoso_tyle_tky = this.tinhTyLe(main.p26_dvctt_motphan_phatsinh_hoso_tky, main.p25_dvctt_motphan_tky);
        main.p30_dvctt_toantrinh_phatsinh_hoso_tyle_tky = this.tinhTyLe(main.p29_dvctt_toantrinh_phatsinh_hoso_tky, main.p28_dvctt_toantrinh_tky);
        main.p33_tthc_bcci_phatsinh_hoso_tyle_tky = this.tinhTyLe(main.p32_tthc_bcci_phatsinh_hoso_tky, main.p31_tthc_bcci_tong_tky);
        main.p37_tthc_tttt_phatsinh_hoso_tyle_tky = this.tinhTyLe(main.p36_tthc_tttt_phatsinh_hoso_tky, main.p34_tthc_tttt_tong_tky);
        main.p39_tthc_tttt_phatsinh_hoso_dagiaiquyet_tyle_tky = this.tinhTyLe(main.p38_tthc_tttt_phatsinh_hoso_dagiaiquyet_tky, main.p35_tthc_tttt_hoso_phatsinh_tky);

        //// Tỷ lệ tổng cộng
        this.TongCong.p06_tiepnhan_tructuyen_motphan_tyle_tky = this.tinhTyLe(this.TongCong.p05_tiepnhan_tructuyen_motphan_tky, this.TongCong.p02_tiepnhan_tong_tky);
        this.TongCong.p08_tiepnhan_tructuyen_toantrinh_tyle_tky = this.tinhTyLe(this.TongCong.p07_tiepnhan_tructuyen_toantrinh_tky, this.TongCong.p02_tiepnhan_tong_tky);
        this.TongCong.p10_tiepnhan_tructuyen_motphan_toantrinh_tyle_tky = this.tinhTyLe(this.TongCong.p09_tiepnhan_tructuyen_motphan_toantrinh_tky, this.TongCong.p02_tiepnhan_tong_tky);
        this.TongCong.p12_tiepnhan_tong_igate_tyle_tky = this.tinhTyLe(1, 1);
        this.TongCong.p15_traketqua_tructiep_tyle_tky = this.tinhTyLe(this.TongCong.p14_traketqua_tructiep_tky, this.TongCong.p13_traketqua_tong_tky);
        this.TongCong.p17_traketqua_bcci_tyle_tky = this.tinhTyLe(this.TongCong.p16_traketqua_bcci_tky, this.TongCong.p13_traketqua_tong_tky);
        this.TongCong.p19_traketqua_tructuyen_tyle_tky = this.tinhTyLe(this.TongCong.p18_traketqua_tructuyen_tky, this.TongCong.p13_traketqua_tong_tky);
        this.TongCong.p24_dvctt_phatsinh_hoso_tyle_tky = this.tinhTyLe(this.TongCong.p23_dvctt_phatsinh_hoso_tky, this.TongCong.p22_dvctt_tong_tky);
        this.TongCong.p27_dvctt_motphan_phatsinh_hoso_tyle_tky = this.tinhTyLe(this.TongCong.p26_dvctt_motphan_phatsinh_hoso_tky, this.TongCong.p25_dvctt_motphan_tky);
        this.TongCong.p30_dvctt_toantrinh_phatsinh_hoso_tyle_tky = this.tinhTyLe(this.TongCong.p29_dvctt_toantrinh_phatsinh_hoso_tky, this.TongCong.p28_dvctt_toantrinh_tky);
        this.TongCong.p33_tthc_bcci_phatsinh_hoso_tyle_tky = this.tinhTyLe(this.TongCong.p32_tthc_bcci_phatsinh_hoso_tky, this.TongCong.p31_tthc_bcci_tong_tky);
        this.TongCong.p37_tthc_tttt_phatsinh_hoso_tyle_tky = this.tinhTyLe(this.TongCong.p36_tthc_tttt_phatsinh_hoso_tky, this.TongCong.p34_tthc_tttt_tong_tky);
        this.TongCong.p39_tthc_tttt_phatsinh_hoso_dagiaiquyet_tyle_tky = this.tinhTyLe(this.TongCong.p38_tthc_tttt_phatsinh_hoso_dagiaiquyet_tky, this.TongCong.p35_tthc_tttt_hoso_phatsinh_tky);

        main.data.push(arr);
      }

      this.ListMain.push(main);
    }

    // Loại bỏ nhưng thủ tục ĐÓNG + Không phát sinh hồ sơ
    this.filterProcedureData(this.ListMain);

    // Loại bỏ những lĩnh vực ĐÓNG + Không phát sinh hồ sơ
    this.filterSectorData(this.ListMain);

    this.waitingDownloadExcel = false;
  }

  filterProcedureData(dataSector) {
    for (let i = 0; i < dataSector.length; i++) {
      let listProcedure = dataSector[i].data;
      if (listProcedure != null && listProcedure.length > 0) {
        for (let proIndex = 0; proIndex < listProcedure.length; proIndex++) {
          let procedureItemData = listProcedure[proIndex];
          if (procedureItemData != null && procedureItemData.status == 1) {
            continue;
          } else if (procedureItemData != null && procedureItemData.status == 0) {
            if (procedureItemData.p02_tiepnhan_tong_tky == 0 && procedureItemData.p22_dvctt_tong_tky == 0) {
              // Loại bỏ thủ tục khỏi lĩnh vực
              this.ListMain[i].data = this.ListMain[i].data.filter(item => item.procedureId != procedureItemData.procedureId);
            }
          }
        }
      }
    }
  }
  filterSectorData(data) {
    let listSectorActive = this.listSectorActive;

    if (data != null) {
      for (let i = 0; i < data.length; i++) {
        let currentData = data[i];
        // for (let j = 0; j < listSectorActive.length; j++) {
        if (listSectorActive.some(item => item.id == currentData.id)) {
          continue;
        } else {
          if (currentData.p02_tiepnhan_tong_tky > 0 || currentData.p22_dvctt_tong_tky > 0) {
            continue;
          } else if (currentData.p02_tiepnhan_tong_tky == 0) {
            // Trừ số liệu TongCong
            this.TongCong.p02_tiepnhan_tong_tky -= currentData.p02_tiepnhan_tong_tky;
            this.TongCong.p03_tiepnhan_tructiep_tky -= currentData.p03_tiepnhan_tructiep_tky;
            this.TongCong.p04_tiepnhan_bcci_tky -= currentData.p04_tiepnhan_bcci_tky;
            this.TongCong.p05_tiepnhan_tructuyen_motphan_tky -= currentData.p05_tiepnhan_tructuyen_motphan_tky;
            this.TongCong.p07_tiepnhan_tructuyen_toantrinh_tky -= currentData.p07_tiepnhan_tructuyen_toantrinh_tky;
            this.TongCong.p09_tiepnhan_tructuyen_motphan_toantrinh_tky -= currentData.p09_tiepnhan_tructuyen_motphan_toantrinh_tky;
            this.TongCong.p11_tiepnhan_tong_igate_tky -= currentData.p11_tiepnhan_tong_igate_tky;
            this.TongCong.p13_traketqua_tong_tky -= currentData.p13_traketqua_tong_tky;
            this.TongCong.p14_traketqua_tructiep_tky -= currentData.p14_traketqua_tructiep_tky;
            this.TongCong.p16_traketqua_bcci_tky -= currentData.p16_traketqua_bcci_tky;
            this.TongCong.p18_traketqua_tructuyen_tky -= currentData.p18_traketqua_tructuyen_tky;
            this.TongCong.p20_traketqua_tong_igate_tky -= currentData.p20_traketqua_tong_igate_tky;
            this.TongCong.p21_traketqua_sudung_kyso_tky -= currentData.p21_traketqua_sudung_kyso_tky;
            this.TongCong.p22_dvctt_tong_tky -= currentData.p22_dvctt_tong_tky;
            this.TongCong.p23_dvctt_phatsinh_hoso_tky -= currentData.p23_dvctt_phatsinh_hoso_tky;
            this.TongCong.p25_dvctt_motphan_tky -= currentData.p25_dvctt_motphan_tky;
            this.TongCong.p26_dvctt_motphan_phatsinh_hoso_tky -= currentData.p26_dvctt_motphan_phatsinh_hoso_tky;
            this.TongCong.p28_dvctt_toantrinh_tky -= currentData.p28_dvctt_toantrinh_tky;
            this.TongCong.p29_dvctt_toantrinh_phatsinh_hoso_tky -= currentData.p29_dvctt_toantrinh_phatsinh_hoso_tky;
            this.TongCong.p31_tthc_bcci_tong_tky -= currentData.p31_tthc_bcci_tong_tky;
            this.TongCong.p32_tthc_bcci_phatsinh_hoso_tky -= currentData.p32_tthc_bcci_phatsinh_hoso_tky;
            this.TongCong.p34_tthc_tttt_tong_tky -= currentData.p34_tthc_tttt_tong_tky;
            this.TongCong.p35_tthc_tttt_hoso_phatsinh_tky -= currentData.p35_tthc_tttt_hoso_phatsinh_tky;
            this.TongCong.p36_tthc_tttt_phatsinh_hoso_tky -= currentData.p36_tthc_tttt_phatsinh_hoso_tky;
            this.TongCong.p38_tthc_tttt_phatsinh_hoso_dagiaiquyet_tky -= currentData.p38_tthc_tttt_phatsinh_hoso_dagiaiquyet_tky;

            // Loại bỏ lĩnh vực trong lòng lặp khỏi danh sách
            this.ListMain = this.ListMain.filter(item => item.id != currentData.id);
          }
        }
        // }
      }
      //// Tính lại tỷ lệ tổng cộng
      this.TongCong.p06_tiepnhan_tructuyen_motphan_tyle_tky = this.tinhTyLe(this.TongCong.p05_tiepnhan_tructuyen_motphan_tky, this.TongCong.p02_tiepnhan_tong_tky);
      this.TongCong.p08_tiepnhan_tructuyen_toantrinh_tyle_tky = this.tinhTyLe(this.TongCong.p07_tiepnhan_tructuyen_toantrinh_tky, this.TongCong.p02_tiepnhan_tong_tky);
      this.TongCong.p10_tiepnhan_tructuyen_motphan_toantrinh_tyle_tky = this.tinhTyLe(this.TongCong.p09_tiepnhan_tructuyen_motphan_toantrinh_tky, this.TongCong.p02_tiepnhan_tong_tky);
      this.TongCong.p12_tiepnhan_tong_igate_tyle_tky = this.tinhTyLe(1, 1);
      this.TongCong.p15_traketqua_tructiep_tyle_tky = this.tinhTyLe(this.TongCong.p14_traketqua_tructiep_tky, this.TongCong.p13_traketqua_tong_tky);
      this.TongCong.p17_traketqua_bcci_tyle_tky = this.tinhTyLe(this.TongCong.p16_traketqua_bcci_tky, this.TongCong.p13_traketqua_tong_tky);
      this.TongCong.p19_traketqua_tructuyen_tyle_tky = this.tinhTyLe(this.TongCong.p18_traketqua_tructuyen_tky, this.TongCong.p13_traketqua_tong_tky);
      this.TongCong.p24_dvctt_phatsinh_hoso_tyle_tky = this.tinhTyLe(this.TongCong.p23_dvctt_phatsinh_hoso_tky, this.TongCong.p22_dvctt_tong_tky);
      this.TongCong.p27_dvctt_motphan_phatsinh_hoso_tyle_tky = this.tinhTyLe(this.TongCong.p26_dvctt_motphan_phatsinh_hoso_tky, this.TongCong.p25_dvctt_motphan_tky);
      this.TongCong.p30_dvctt_toantrinh_phatsinh_hoso_tyle_tky = this.tinhTyLe(this.TongCong.p29_dvctt_toantrinh_phatsinh_hoso_tky, this.TongCong.p28_dvctt_toantrinh_tky);
      this.TongCong.p33_tthc_bcci_phatsinh_hoso_tyle_tky = this.tinhTyLe(this.TongCong.p32_tthc_bcci_phatsinh_hoso_tky, this.TongCong.p31_tthc_bcci_tong_tky);
      this.TongCong.p37_tthc_tttt_phatsinh_hoso_tyle_tky = this.tinhTyLe(this.TongCong.p36_tthc_tttt_phatsinh_hoso_tky, this.TongCong.p34_tthc_tttt_tong_tky);
      this.TongCong.p39_tthc_tttt_phatsinh_hoso_dagiaiquyet_tyle_tky = this.tinhTyLe(this.TongCong.p38_tthc_tttt_phatsinh_hoso_dagiaiquyet_tky, this.TongCong.p35_tthc_tttt_hoso_phatsinh_tky);
    }
  }
  getDataMain(currentData, plusData, typeHS) {
    let data = 0;

    if (typeHS) {
      data = currentData + parseInt(plusData);
    } else {
      if (currentData != '-') {
        return parseFloat(currentData);
      }

    }

    return data;
  }

  tinhTyLe(a, b) {
    if (b != 0) {
      let ratio = (a / b) * 100;
      if (ratio == 0) {
        return "0";
      } else if (ratio == 100) {
        return "100";
      }
      return ratio.toFixed(2).toString();
    }
    return "-";
  }

  SelectedAgency = "";
  GetDetailDossier(AgencyId, AgencyName, TrongKy, DaXuLy, TrucTiep, BCCI, MotPhan, ToanTrinh, toltal) {
    if (TrongKy == 1) {
      this.paramsDossier.fromDate = (this.startDate ? this.datePipe.transform(this.startDate, 'yyyy-MM-dd') : '');
      this.paramsDossier.toDate = (this.endDate ? this.datePipe.transform(this.endDate, 'yyyy-MM-dd') : '');
    }
    else {
      this.paramsDossier.fromDate = (this.startDateCumulative ? this.datePipe.transform(this.startDateCumulative, 'yyyy-MM-dd') : '');
      this.paramsDossier.toDate = (this.endDateCumulative ? this.datePipe.transform(this.endDateCumulative, 'yyyy-MM-dd') : '');
    }

    // tslint:disable-next-line:max-line-length
    const dialogData = new DossierDetailDialogModel(AgencyId, AgencyName, TrongKy, DaXuLy, TrucTiep, BCCI, MotPhan, ToanTrinh, this.paramsDossier.fromDate, this.paramsDossier.toDate, toltal, 0);
    const dialogRef = this.dialog.open(DossierDetailComponent, {
      width: '95%',
      height: '90%',
      data: dialogData,
      disableClose: true,
      autoFocus: false
    });
    dialogRef.afterClosed().subscribe(() => {
    });
  }

  listSectorProcedure: any = [];
 

  getListProcedureofDossier() {
    let lstprocedure = [];
    this.listSector = [];
    this.listProcedure = [];
    // Sử dụng Map để lưu các sector với key là sectorId
    const sectorMap = new Map();

    this.procedureService
      .getAllProcedureWithAgencyDLK('?agency-id=' + this.agencyId)
      .subscribe(
        (res) => {
          this.listSectorProcedure = res.content;
          if (this.listSectorProcedure.length > 0) {
            this.listSectorProcedure.forEach((item) => {
              // Lưu thông tin thủ tục, với item.status: 1 (mở) hoặc 0 (đóng)
              lstprocedure.push({
                sectorName: item.sectorName,
                sectorId: item.sectorId,
                procedureId: item.id,
                procedureName: item.name,
                MucDo: item.procedureLevelName,
                status: item.status
              });

              // Nếu sector chưa tồn tại thì thêm vào (dù thủ tục có trạng thái đóng)
              if (!sectorMap.has(item.sectorId)) {
                sectorMap.set(item.sectorId, {
                  sectorId: item.sectorId,
                  sectorName: item.sectorName,
                  status: item.status
                });
              } else {
                // Nếu sector đã tồn tại và tên khác nhau
                let existing = sectorMap.get(item.sectorId);
                if (existing.sectorName !== item.sectorName) {
                  // Nếu thủ tục hiện tại mở (status === 1) và sector đã lưu có trạng thái đóng (status === 0) thì cập nhật lại
                  if (item.status === 1 && existing.status === 0) {
                    sectorMap.set(item.sectorId, {
                      sectorId: item.sectorId,
                      sectorName: item.sectorName,
                      status: item.status
                    });
                  }
                  // Trường hợp khác, giữ nguyên sector đã lưu
                }
              }
            });
          }
          // Chuyển Map thành mảng, chỉ giữ lại sectorId và sectorName
          this.listSector = Array.from(sectorMap.values()).map(
            ({ sectorId, sectorName }) => ({ sectorId, sectorName })
          );
          this.listProcedure = lstprocedure;

          // Lọc lại danh sách lĩnh vực cho cơ quan
          this.getListSector();
        },
        (err) => {
          console.log(err);
        }
      );
  }

  getListSector() {
    const searchString = '?keyword=&page=0&size=1000&spec=page&sort=name.name,asc&status=1&only-agency-id=1&agency-id=' + this.agencyId;;
    let listSector = [];
    this.procedureService.getListSector(searchString).subscribe(res => {
      if (res != null && res.content != null && res.content.length > 0) {
        listSector = res.content;
        this.listSectorActive = res.content;
      }
    }, err => {
      console.log(err);
    });
  }

  // getListAgencyAccept(prid, keyword, page, size) {
  //   const searchString = '?parent-id=' + this.Agency.rootAgencyId + '&keyword=' + keyword + '&page=' + page + '&size=' + size + '&sort=name.name,asc&status=1';
  //   this.procedureService.getListAgencyWithParent(searchString).subscribe(res => {
  //     this.listAgency = res.content;
  //     // this.listAgencyAccept = res.content;
  //     this.listAgencyAccept = res.content.filter(item => item.level != null && item.level.id == "5f39f42d5224cf235e134c5a");
  //     if (this.listAgencyAccept.length > 0) {
  //       this.agencyId = this.listAgencyAccept[0].id;
  //       this.agencyName = this.listAgencyAccept[0]?.name;
  //     }
  //     this.getListProcedureofDossier();
  //     //  let listid = [...new Set( this.listAgencyvilange.map(obj =>obj.id))]

  //   }, err => {
  //     console.log(err);
  //   });
  // }

  getListAgencyAccept(prid, keyword, page, size, accumulatedData) {
    const searchString = `?parent-id=${prid}&keyword=${keyword}&page=${page}&size=${size}&sort=name.name,asc&status=1`;

    this.procedureService.getListAgencyWithParent(searchString).subscribe(
      (res) => {
        const merged = [...accumulatedData, ...res.content];
        if (res.last) {
          this.listAgency = merged;
          this.listAgencyAccept = merged.filter(item => item.level != null && item.level.id == "5f39f42d5224cf235e134c5a");
          if (this.listAgencyAccept.length > 0) {
            this.agencyId = this.listAgencyAccept[0].id;
            this.agencyName = this.listAgencyAccept[0]?.name;
          }
          this.getListProcedureofDossier();
        } else {
          this.getListAgencyAccept(prid, keyword, page + 1, size, merged);
        }
      },
      (err) => {
        console.error('Lỗi khi gọi API lấy danh sách agency:', err);
      }
    );
  }

  changeAgency(event: any): void {
    const selectedValue = event.value.toString().trim();
    let agency = this.listAgencyAccept.find((element) => element.id === selectedValue);
    this.agencyName = agency?.name;
    this.getListProcedureofDossier();
  }

  ListMain = [];
  ViewDetail(i, show) {
    this.ListMain[i].show = !show;
  }

  onClickTd(prop: string, type: number) {
    //data dialog
    let dialogData: any = {
      prid: this.config.rootAgency.id,
      id: this.agencyId,
      agencyName: this.agencyName,
      prop: prop.slice(0, -4),
      type: type
    };

    dialogData.tuNgay = this.startDate;
    dialogData.denNgay = this.endDate;

    //mở dialog
    const dialogRef = this.dialog.open(BaocaoChithi08DialogComponent, {
      width: '95%',
      height: '90%',
      data: dialogData,
      disableClose: true,
      autoFocus: false
    });

    dialogRef.afterClosed().subscribe(() => {
    });
  }

  waitingDownloadExcel: boolean = false;
  async exportToExcel() {
    if ((this.validateForm)) {
      this.thongKe();
      const from = this.datePipe.transform(this.startDate, 'dd/MM/yyyy');
      const to = this.datePipe.transform(this.endDate, 'dd/MM/yyyy');
      const newDateshort = this.datePipe.transform(new Date(), "dd/MM/yyyy")
      const newDate = this.datePipe.transform(new Date(), "dd/MM/yyyy HH:mm:ss")
      const excelFileName = `Bao_cao_chi_thi_08_capso_${newDate}`;
      let headerXLS = {
        row1: "CỘNG HÒA XÃ HỘI CHỦ NGHĨA VIỆT NAM",
        row2: "Độc lập - Tự do - Hạnh phúc",
        row3: `BÁO CÁO CHỈ THỊ 08 CẤP SỞ`,
        row4: `(Từ ngày ${from} đến ngày ${to})`,
        row5: `Tỉnh Đắk Lắk, thống kê vào lúc ` + newDate,
      }

      const workbook = new Workbook();
      workbook.creator = 'Snippet Coder';
      workbook.lastModifiedBy = 'SnippetCoder';
      workbook.created = new Date();
      workbook.modified = new Date();
      const worksheet = workbook.addWorksheet("sheet1");

      // Add header row
      worksheet.addRow([]);
      worksheet.mergeCells('A1:V1');
      worksheet.getCell('A1').value = headerXLS.row1;
      worksheet.getCell('A1').alignment = { horizontal: 'center', vertical: 'middle' };
      worksheet.getCell('A1').font = { size: 13, name: 'Times New Roman' };

      worksheet.mergeCells('A2:V2');
      worksheet.getCell('A2').value = headerXLS.row2;
      worksheet.getCell('A2').alignment = { horizontal: 'center', vertical: 'middle' };
      worksheet.getCell('A2').font = { size: 13, underline: false, name: 'Times New Roman' };
      
      worksheet.mergeCells('A3:V3');
      worksheet.getCell('A3').value = "";
      worksheet.getCell('A3').alignment = { horizontal: 'center', vertical: 'middle' };
      worksheet.getCell('A3').font = { size: 13, bold: true, name: 'Times New Roman' };

      worksheet.mergeCells('A4:V4');
      worksheet.getCell('A4').value = headerXLS.row3;
      worksheet.getCell('A4').alignment = { horizontal: 'center', vertical: 'middle' };
      worksheet.getCell('A4').font = { size: 13, bold: true, name: 'Times New Roman' };

      worksheet.mergeCells('A5:V5');
      worksheet.getCell('A5').value = headerXLS.row4;
      worksheet.getCell('A5').alignment = { horizontal: 'center', vertical: 'middle' };
      worksheet.getCell('A5').font = { size: 11, italic: true, name: 'Times New Roman' };

      worksheet.mergeCells('O6:V6');
      worksheet.getCell('O6').value = headerXLS.row5;
      worksheet.getCell('O6').alignment = { horizontal: 'center', vertical: 'middle' };
      worksheet.getCell('O6').font = { size: 11, italic: true, name: 'Times New Roman' };

      worksheet.mergeCells('A7:A11');
      worksheet.getCell('A7').value = "STT";

      worksheet.mergeCells('B7:B11');
      worksheet.getCell('B7').value = "Tên lĩnh vực/thủ tục";

      worksheet.mergeCells('C7:C10');
      worksheet.getCell('C7').value = "Mức độ thủ tục";

      worksheet.mergeCells('D7:N7');
      worksheet.getCell('D7').value = "Tiếp nhận hồ sơ TTHC";
      worksheet.getCell('D7').alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
      worksheet.getCell('D7').font = { size: 11, bold: true, name: 'Times New Roman' };
      worksheet.getCell('D7').border = { top: { style: 'thin' }, left: { style: 'thin' }, bottom: { style: 'thin' }, right: { style: 'thin' } };

      worksheet.mergeCells('O7:W7');
      worksheet.getCell('O7').value = "Trả kết quả giải quyết TTHC";
      worksheet.getCell('O7').alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
      worksheet.getCell('O7').font = { size: 11, bold: true, name: 'Times New Roman' };
      worksheet.getCell('O7').border = { top: { style: 'thin' }, left: { style: 'thin' }, bottom: { style: 'thin' }, right: { style: 'thin' } };

      worksheet.mergeCells('X7:AF7');
      worksheet.getCell('X7').value = "Dịch vụ công trực tuyến (DVCTT)";
      worksheet.getCell('X7').alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
      worksheet.getCell('X7').font = { size: 11, bold: true, name: 'Times New Roman' };
      worksheet.getCell('X7').border = { top: { style: 'thin' }, left: { style: 'thin' }, bottom: { style: 'thin' }, right: { style: 'thin' } };

      worksheet.mergeCells('AG7:AI7');
      worksheet.getCell('AG7').value = "TTHC cung cấp dịch vụ BCCI";
      worksheet.getCell('AG7').alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
      worksheet.getCell('AG7').font = { size: 11, bold: true, name: 'Times New Roman' };
      worksheet.getCell('AG7').border = { top: { style: 'thin' }, left: { style: 'thin' }, bottom: { style: 'thin' }, right: { style: 'thin' } };

      worksheet.mergeCells('AJ7:AO7');
      worksheet.getCell('AJ7').value = "Thanh toán trực tuyến";
      worksheet.getCell('AJ7').alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
      worksheet.getCell('AJ7').font = { size: 11, bold: true, name: 'Times New Roman' };
      worksheet.getCell('AJ7').border = { top: { style: 'thin' }, left: { style: 'thin' }, bottom: { style: 'thin' }, right: { style: 'thin' } };

      // Tiếp nhận hồ sơ TTHC
      worksheet.mergeCells('D8:D10');
      worksheet.getCell('D8').value = "Tổng số hồ sơ tiếp nhận trong tháng";
      worksheet.mergeCells('E8:E10');
      worksheet.getCell('E8').value = "Trực tiếp";
      worksheet.mergeCells('F8:F10');
      worksheet.getCell('F8').value = "Qua BCCI";

      worksheet.mergeCells('G8:L8');
      worksheet.getCell('G8').value = "Trực tuyến";
      worksheet.mergeCells('G9:H9');
      worksheet.getCell('G9').value = "Một phần";
      worksheet.getCell('G10').value = "Số hồ sơ";
      worksheet.getCell('H10').value = "Tỷ lệ %";
      worksheet.mergeCells('I9:J9');
      worksheet.getCell('I9').value = "Toàn trình";
      worksheet.getCell('I10').value = "Số hồ sơ";
      worksheet.getCell('J10').value = "Tỷ lệ %";
      worksheet.mergeCells('K9:L9');
      worksheet.getCell('K9').value = "Trực tuyến (một phần và toàn trình)";
      worksheet.getCell('K10').value = "Số hồ sơ";
      worksheet.getCell('L10').value = "Tỷ lệ %";
      worksheet.mergeCells('M8:N9');
      worksheet.getCell('M8').value = "Cập nhật lên iGate";
      worksheet.getCell('M10').value = "Số hồ sơ tiếp nhận đã cập nhật lên iGate";
      worksheet.getCell('N10').value = "Tỷ lệ %";

      // Trả kết quả giải quyết TTHC
      worksheet.mergeCells('O8:O10');
      worksheet.getCell('O8').value = "Tổng số";
      worksheet.mergeCells('P8:Q9');
      worksheet.getCell('P8').value = "Trả trực tiếp";
      worksheet.getCell('P10').value = "Số hồ sơ";
      worksheet.getCell('Q10').value = "Tỷ lệ %";
      worksheet.mergeCells('R8:S9');
      worksheet.getCell('R8').value = "Trả qua dịch vụ BCCI";
      worksheet.getCell('R10').value = "Số hồ sơ";
      worksheet.getCell('S10').value = "Tỷ lệ %";
      worksheet.mergeCells('T8:U9');
      worksheet.getCell('T8').value = "Trả trực tuyến";
      worksheet.getCell('T10').value = "Số hồ sơ";
      worksheet.getCell('U10').value = "Tỷ lệ %";
      worksheet.mergeCells('V8:V10');
      worksheet.getCell('V8').value = "Cập nhật lên iGate";
      worksheet.mergeCells('W8:W10');
      worksheet.getCell('W8').value = "Số hồ sơ TTHC sử dụng ký số trong giải quyết";

      // Dịch vụ công trực tuyến (DVCTT)
      worksheet.mergeCells('X8:X10');
      worksheet.getCell('X8').value = "Tổng số TTHC của cơ quan, đơn vị";
      worksheet.mergeCells('Y8:Y10');
      worksheet.getCell('Y8').value = "Số DVCTT có phát sinh hồ sơ";
      worksheet.mergeCells('Z8:Z10');
      worksheet.getCell('Z8').value = "Tỷ lệ DVCTT có phát sinh hồ sơ";
      worksheet.mergeCells('AA8:AC9');
      worksheet.getCell('AA8').value = "Một phần";
      worksheet.getCell('AA10').value = "Số DVCTT một phần";
      worksheet.getCell('AB10').value = "Số DVCTT một phần có phát sinh hồ sơ";
      worksheet.getCell('AC10').value = "Tỷ lệ %";

      worksheet.mergeCells('AD8:AF9');
      worksheet.getCell('AD8').value = "Toàn trình";
      worksheet.getCell('AD10').value = "Số DVCTT toàn trình";
      worksheet.getCell('AE10').value = "Số DVCTT toàn trình có phát sinh hồ sơ";
      worksheet.getCell('AF10').value = "Tỷ lệ %";

      // TTHC cung cấp dịch vụ BCCI
      worksheet.mergeCells('AG8:AG10');
      worksheet.getCell('AG8').value = "Số TTHC cung cấp dịch vụ BCCI";

      worksheet.mergeCells('AH8:AI9');
      worksheet.getCell('AH8').value = "TTHC cung cấp dịch vụ BCCI có phát sinh hồ sơ";
      worksheet.getCell('AH10').value = "Số TTHC cung cấp dịch vụ BCCI có phát sinh hồ sơ";
      worksheet.getCell('AI10').value = "Tỷ lệ %";

      // Thanh toán trực tuyến
      worksheet.mergeCells('AJ8:AJ10');
      worksheet.getCell('AJ8').value = "Số TTHC cung cấp dịch vụ thanh toán trực tuyến";
      worksheet.mergeCells('AK8:AK10');
      worksheet.getCell('AK8').value = "Số hồ sơ TTHC phát sinh của các TTHC cung cấp dịch vụ thanh toán trực tuyến (bao gồm trực tiếp, trực tuyến, BCCI)";
      worksheet.mergeCells('AL8:AM9');
      worksheet.getCell('AL8').value = "Số TTHC cung cấp dịch vụ thanh toán trực tuyến có phát sinh hồ sơ";
      worksheet.getCell('AL10').value = "Số TTHC";
      worksheet.getCell('AM10').value = "Tỷ lệ %";
      worksheet.mergeCells('AN8:AO9');
      worksheet.getCell('AN8').value = "Thực hiện thanh toán trực tuyến";
      worksheet.getCell('AN10').value = "Số hồ sơ TTHC đã giải quyết sử dụng dịch vụ thanh toán trực tuyến";
      worksheet.getCell('AO10').value = "Tỷ lệ %";

      const rowStartHeaderContent = 11;
      const NumberCol = 41;
      for (let index = 0; index < NumberCol; index++) {
        worksheet.getCell(10, (index + 1)).alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
        worksheet.getCell(10, (index + 1)).font = { size: 11, bold: true, name: 'Times New Roman' };
        worksheet.getCell(10, (index + 1)).border = { top: { style: 'thin' }, left: { style: 'thin' }, bottom: { style: 'thin' }, right: { style: 'thin' } };

        worksheet.getCell(9, (index + 1)).alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
        worksheet.getCell(9, (index + 1)).font = { size: 11, bold: true, name: 'Times New Roman' };
        worksheet.getCell(9, (index + 1)).border = { top: { style: 'thin' }, left: { style: 'thin' }, bottom: { style: 'thin' }, right: { style: 'thin' } };

        worksheet.getCell(8, (index + 1)).alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
        worksheet.getCell(8, (index + 1)).font = { size: 11, bold: true, name: 'Times New Roman' };
        worksheet.getCell(8, (index + 1)).border = { top: { style: 'thin' }, left: { style: 'thin' }, bottom: { style: 'thin' }, right: { style: 'thin' } };

        if (index > 1) {
          worksheet.getCell(rowStartHeaderContent, (index + 1)).value = "(" + (index - 1).toString() + ")";
        }
        worksheet.getCell(rowStartHeaderContent, (index + 1)).alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
        worksheet.getCell(rowStartHeaderContent, (index + 1)).font = { size: 11, bold: true, name: 'Times New Roman' };
        worksheet.getCell(rowStartHeaderContent, (index + 1)).border = { top: { style: 'thin' }, left: { style: 'thin' }, bottom: { style: 'thin' }, right: { style: 'thin' } };
      }


      let rowStartContent = rowStartHeaderContent + 1;

      // cấp huyện
      const data = this.ListMain;
      for (let i = 0; i < data.length; i++) {
        var item = data[i];
        worksheet.getCell(rowStartContent, 1).value = i + 1;
        worksheet.mergeCells(rowStartContent, 2, rowStartContent, 3);
        worksheet.getCell(rowStartContent, 2).value = item.sector;
        worksheet.getCell(rowStartContent, 4).value = item.p02_tiepnhan_tong_tky;
        worksheet.getCell(rowStartContent, 5).value = item.p03_tiepnhan_tructiep_tky;
        worksheet.getCell(rowStartContent, 6).value = item.p04_tiepnhan_bcci_tky;
        worksheet.getCell(rowStartContent, 7).value = item.p05_tiepnhan_tructuyen_motphan_tky;
        worksheet.getCell(rowStartContent, 8).value = item.p06_tiepnhan_tructuyen_motphan_tyle_tky;
        worksheet.getCell(rowStartContent, 9).value = item.p07_tiepnhan_tructuyen_toantrinh_tky;
        worksheet.getCell(rowStartContent, 10).value = item.p08_tiepnhan_tructuyen_toantrinh_tyle_tky;
        worksheet.getCell(rowStartContent, 11).value = item.p09_tiepnhan_tructuyen_motphan_toantrinh_tky;
        worksheet.getCell(rowStartContent, 12).value = item.p10_tiepnhan_tructuyen_motphan_toantrinh_tyle_tky;
        worksheet.getCell(rowStartContent, 13).value = item.p11_tiepnhan_tong_igate_tky;
        worksheet.getCell(rowStartContent, 14).value = item.p12_tiepnhan_tong_igate_tyle_tky;
        worksheet.getCell(rowStartContent, 15).value = item.p13_traketqua_tong_tky;
        worksheet.getCell(rowStartContent, 16).value = item.p14_traketqua_tructiep_tky;
        worksheet.getCell(rowStartContent, 17).value = item.p15_traketqua_tructiep_tyle_tky;
        worksheet.getCell(rowStartContent, 18).value = item.p16_traketqua_bcci_tky;
        worksheet.getCell(rowStartContent, 19).value = item.p17_traketqua_bcci_tyle_tky;
        worksheet.getCell(rowStartContent, 20).value = item.p18_traketqua_tructuyen_tky;
        worksheet.getCell(rowStartContent, 21).value = item.p19_traketqua_tructuyen_tyle_tky;
        worksheet.getCell(rowStartContent, 22).value = item.p20_traketqua_tong_igate_tky;
        worksheet.getCell(rowStartContent, 23).value = item.p21_traketqua_sudung_kyso_tky;
        worksheet.getCell(rowStartContent, 24).value = item.p22_dvctt_tong_tky;
        worksheet.getCell(rowStartContent, 25).value = item.p23_dvctt_phatsinh_hoso_tky;
        worksheet.getCell(rowStartContent, 26).value = item.p24_dvctt_phatsinh_hoso_tyle_tky;
        worksheet.getCell(rowStartContent, 27).value = item.p25_dvctt_motphan_tky;
        worksheet.getCell(rowStartContent, 28).value = item.p26_dvctt_motphan_phatsinh_hoso_tky;
        worksheet.getCell(rowStartContent, 29).value = item.p27_dvctt_motphan_phatsinh_hoso_tyle_tky;
        worksheet.getCell(rowStartContent, 30).value = item.p28_dvctt_toantrinh_tky;
        worksheet.getCell(rowStartContent, 31).value = item.p29_dvctt_toantrinh_phatsinh_hoso_tky;
        worksheet.getCell(rowStartContent, 32).value = item.p30_dvctt_toantrinh_phatsinh_hoso_tyle_tky;
        worksheet.getCell(rowStartContent, 33).value = item.p31_tthc_bcci_tong_tky;
        worksheet.getCell(rowStartContent, 34).value = item.p32_tthc_bcci_phatsinh_hoso_tky;
        worksheet.getCell(rowStartContent, 35).value = item.p33_tthc_bcci_phatsinh_hoso_tyle_tky;
        worksheet.getCell(rowStartContent, 36).value = item.p34_tthc_tttt_tong_tky;
        worksheet.getCell(rowStartContent, 37).value = item.p35_tthc_tttt_hoso_phatsinh_tky;
        worksheet.getCell(rowStartContent, 38).value = item.p36_tthc_tttt_phatsinh_hoso_tky;
        worksheet.getCell(rowStartContent, 39).value = item.p37_tthc_tttt_phatsinh_hoso_tyle_tky;
        worksheet.getCell(rowStartContent, 40).value = item.p38_tthc_tttt_phatsinh_hoso_dagiaiquyet_tky;
        worksheet.getCell(rowStartContent, 41).value = item.p39_tthc_tttt_phatsinh_hoso_dagiaiquyet_tyle_tky;

        for (let c = 0; c < NumberCol; c++) {
          worksheet.getCell(rowStartContent, (c + 1)).alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
          worksheet.getCell(rowStartContent, 2).alignment = { horizontal: 'left', vertical: 'middle', wrapText: true };
          worksheet.getCell(rowStartContent, (c + 1)).font = { size: 11, name: 'Times New Roman', bold: true };
          worksheet.getCell(rowStartContent, (c + 1)).border = { top: { style: 'thin' }, left: { style: 'thin' }, bottom: { style: 'thin' }, right: { style: 'thin' } };
        }

        // lấy theo thủ tục của lvuc
        for (let r = 0; r < item.data.length; r++) {
          rowStartContent = rowStartContent + 1;
          worksheet.getCell(rowStartContent, 1).value = this.colToLetter(r);
          worksheet.getCell(rowStartContent, 2).value = item.data[r].procedureName;
          worksheet.getCell(rowStartContent, 3).value = item.data[r].MucDo;
          worksheet.getCell(rowStartContent, 4).value = item.data[r].p02_tiepnhan_tong_tky;
          worksheet.getCell(rowStartContent, 5).value = item.data[r].p03_tiepnhan_tructiep_tky;
          worksheet.getCell(rowStartContent, 6).value = item.data[r].p04_tiepnhan_bcci_tky;
          worksheet.getCell(rowStartContent, 7).value = item.data[r].p05_tiepnhan_tructuyen_motphan_tky;
          worksheet.getCell(rowStartContent, 8).value = item.data[r].p06_tiepnhan_tructuyen_motphan_tyle_tky;
          worksheet.getCell(rowStartContent, 9).value = item.data[r].p07_tiepnhan_tructuyen_toantrinh_tky;
          worksheet.getCell(rowStartContent, 10).value = item.data[r].p08_tiepnhan_tructuyen_toantrinh_tyle_tky;
          worksheet.getCell(rowStartContent, 11).value = item.data[r].p09_tiepnhan_tructuyen_motphan_toantrinh_tky;
          worksheet.getCell(rowStartContent, 12).value = item.data[r].p10_tiepnhan_tructuyen_motphan_toantrinh_tyle_tky;
          worksheet.getCell(rowStartContent, 13).value = item.data[r].p11_tiepnhan_tong_igate_tky;
          worksheet.getCell(rowStartContent, 14).value = item.data[r].p12_tiepnhan_tong_igate_tyle_tky;
          worksheet.getCell(rowStartContent, 15).value = item.data[r].p13_traketqua_tong_tky;
          worksheet.getCell(rowStartContent, 16).value = item.data[r].p14_traketqua_tructiep_tky;
          worksheet.getCell(rowStartContent, 17).value = item.data[r].p15_traketqua_tructiep_tyle_tky;
          worksheet.getCell(rowStartContent, 18).value = item.data[r].p16_traketqua_bcci_tky;
          worksheet.getCell(rowStartContent, 19).value = item.data[r].p17_traketqua_bcci_tyle_tky;
          worksheet.getCell(rowStartContent, 20).value = item.data[r].p18_traketqua_tructuyen_tky;
          worksheet.getCell(rowStartContent, 21).value = item.data[r].p19_traketqua_tructuyen_tyle_tky;
          worksheet.getCell(rowStartContent, 22).value = item.data[r].p20_traketqua_tong_igate_tky;
          worksheet.getCell(rowStartContent, 23).value = item.data[r].p21_traketqua_sudung_kyso_tky;
          worksheet.getCell(rowStartContent, 24).value = item.data[r].p22_dvctt_tong_tky;
          worksheet.getCell(rowStartContent, 25).value = item.data[r].p23_dvctt_phatsinh_hoso_tky;
          worksheet.getCell(rowStartContent, 26).value = item.data[r].p24_dvctt_phatsinh_hoso_tyle_tky;
          worksheet.getCell(rowStartContent, 27).value = item.data[r].p25_dvctt_motphan_tky;
          worksheet.getCell(rowStartContent, 28).value = item.data[r].p26_dvctt_motphan_phatsinh_hoso_tky;
          worksheet.getCell(rowStartContent, 29).value = item.data[r].p27_dvctt_motphan_phatsinh_hoso_tyle_tky;
          worksheet.getCell(rowStartContent, 30).value = item.data[r].p28_dvctt_toantrinh_tky;
          worksheet.getCell(rowStartContent, 31).value = item.data[r].p29_dvctt_toantrinh_phatsinh_hoso_tky;
          worksheet.getCell(rowStartContent, 32).value = item.data[r].p30_dvctt_toantrinh_phatsinh_hoso_tyle_tky;
          worksheet.getCell(rowStartContent, 33).value = item.data[r].p31_tthc_bcci_tong_tky;
          worksheet.getCell(rowStartContent, 34).value = item.data[r].p32_tthc_bcci_phatsinh_hoso_tky;
          worksheet.getCell(rowStartContent, 35).value = item.data[r].p33_tthc_bcci_phatsinh_hoso_tyle_tky;
          worksheet.getCell(rowStartContent, 36).value = item.data[r].p34_tthc_tttt_tong_tky;
          worksheet.getCell(rowStartContent, 37).value = item.data[r].p35_tthc_tttt_hoso_phatsinh_tky;
          worksheet.getCell(rowStartContent, 38).value = item.data[r].p36_tthc_tttt_phatsinh_hoso_tky;
          worksheet.getCell(rowStartContent, 39).value = item.data[r].p37_tthc_tttt_phatsinh_hoso_tyle_tky;
          worksheet.getCell(rowStartContent, 40).value = item.data[r].p38_tthc_tttt_phatsinh_hoso_dagiaiquyet_tky;
          worksheet.getCell(rowStartContent, 41).value = item.data[r].p39_tthc_tttt_phatsinh_hoso_dagiaiquyet_tyle_tky;

          for (let c = 0; c < NumberCol; c++) {
            worksheet.getCell(rowStartContent, (c + 1)).alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
            worksheet.getCell(rowStartContent, (c + 1)).font = { size: 11, name: 'Times New Roman' };
            worksheet.getCell(rowStartContent, (c + 1)).border = { top: { style: 'thin' }, left: { style: 'thin' }, bottom: { style: 'thin' }, right: { style: 'thin' } };
          }
        }

        rowStartContent = rowStartContent + 1;
      }

      //TỔNG CỘNG
      worksheet.mergeCells(rowStartContent, 1, rowStartContent, 3);
      worksheet.getCell(rowStartContent, 1).value = "TỔNG CỘNG";
      worksheet.getCell(rowStartContent, 1).alignment = { horizontal: 'center', vertical: 'middle', };
      worksheet.getCell(rowStartContent, 1).font = { size: 11, bold: true, name: 'Times New Roman' };
      worksheet.getCell(rowStartContent, 1).border = { top: { style: 'thin' }, left: { style: 'thin' }, bottom: { style: 'thin' }, right: { style: 'thin' } };
      worksheet.getCell(rowStartContent, 4).value = this.TongCong.p02_tiepnhan_tong_tky;
      worksheet.getCell(rowStartContent, 5).value = this.TongCong.p03_tiepnhan_tructiep_tky;
      worksheet.getCell(rowStartContent, 6).value = this.TongCong.p04_tiepnhan_bcci_tky;
      worksheet.getCell(rowStartContent, 7).value = this.TongCong.p05_tiepnhan_tructuyen_motphan_tky;
      worksheet.getCell(rowStartContent, 8).value = this.TongCong.p06_tiepnhan_tructuyen_motphan_tyle_tky;
      worksheet.getCell(rowStartContent, 9).value = this.TongCong.p07_tiepnhan_tructuyen_toantrinh_tky;
      worksheet.getCell(rowStartContent, 10).value = this.TongCong.p08_tiepnhan_tructuyen_toantrinh_tyle_tky;
      worksheet.getCell(rowStartContent, 11).value = this.TongCong.p09_tiepnhan_tructuyen_motphan_toantrinh_tky;
      worksheet.getCell(rowStartContent, 12).value = this.TongCong.p10_tiepnhan_tructuyen_motphan_toantrinh_tyle_tky;
      worksheet.getCell(rowStartContent, 13).value = this.TongCong.p11_tiepnhan_tong_igate_tky;
      worksheet.getCell(rowStartContent, 14).value = this.TongCong.p12_tiepnhan_tong_igate_tyle_tky;
      worksheet.getCell(rowStartContent, 15).value = this.TongCong.p13_traketqua_tong_tky;
      worksheet.getCell(rowStartContent, 16).value = this.TongCong.p14_traketqua_tructiep_tky;
      worksheet.getCell(rowStartContent, 17).value = this.TongCong.p15_traketqua_tructiep_tyle_tky;
      worksheet.getCell(rowStartContent, 18).value = this.TongCong.p16_traketqua_bcci_tky;
      worksheet.getCell(rowStartContent, 19).value = this.TongCong.p17_traketqua_bcci_tyle_tky;
      worksheet.getCell(rowStartContent, 20).value = this.TongCong.p18_traketqua_tructuyen_tky;
      worksheet.getCell(rowStartContent, 21).value = this.TongCong.p19_traketqua_tructuyen_tyle_tky;
      worksheet.getCell(rowStartContent, 22).value = this.TongCong.p20_traketqua_tong_igate_tky;
      worksheet.getCell(rowStartContent, 23).value = this.TongCong.p21_traketqua_sudung_kyso_tky;
      worksheet.getCell(rowStartContent, 24).value = this.TongCong.p22_dvctt_tong_tky;
      worksheet.getCell(rowStartContent, 25).value = this.TongCong.p23_dvctt_phatsinh_hoso_tky;
      worksheet.getCell(rowStartContent, 26).value = this.TongCong.p24_dvctt_phatsinh_hoso_tyle_tky;
      worksheet.getCell(rowStartContent, 27).value = this.TongCong.p25_dvctt_motphan_tky;
      worksheet.getCell(rowStartContent, 28).value = this.TongCong.p26_dvctt_motphan_phatsinh_hoso_tky;
      worksheet.getCell(rowStartContent, 29).value = this.TongCong.p27_dvctt_motphan_phatsinh_hoso_tyle_tky;
      worksheet.getCell(rowStartContent, 30).value = this.TongCong.p28_dvctt_toantrinh_tky;
      worksheet.getCell(rowStartContent, 31).value = this.TongCong.p29_dvctt_toantrinh_phatsinh_hoso_tky;
      worksheet.getCell(rowStartContent, 32).value = this.TongCong.p30_dvctt_toantrinh_phatsinh_hoso_tyle_tky;
      worksheet.getCell(rowStartContent, 33).value = this.TongCong.p31_tthc_bcci_tong_tky;
      worksheet.getCell(rowStartContent, 34).value = this.TongCong.p32_tthc_bcci_phatsinh_hoso_tky;
      worksheet.getCell(rowStartContent, 35).value = this.TongCong.p33_tthc_bcci_phatsinh_hoso_tyle_tky;
      worksheet.getCell(rowStartContent, 36).value = this.TongCong.p34_tthc_tttt_tong_tky;
      worksheet.getCell(rowStartContent, 37).value = this.TongCong.p35_tthc_tttt_hoso_phatsinh_tky;
      worksheet.getCell(rowStartContent, 38).value = this.TongCong.p36_tthc_tttt_phatsinh_hoso_tky;
      worksheet.getCell(rowStartContent, 39).value = this.TongCong.p37_tthc_tttt_phatsinh_hoso_tyle_tky;
      worksheet.getCell(rowStartContent, 40).value = this.TongCong.p38_tthc_tttt_phatsinh_hoso_dagiaiquyet_tky;
      worksheet.getCell(rowStartContent, 41).value = this.TongCong.p39_tthc_tttt_phatsinh_hoso_dagiaiquyet_tyle_tky;


      for (let index = 3; index < NumberCol; index++) {
        worksheet.getCell(rowStartContent, (index + 1)).alignment = { horizontal: 'center', vertical: 'middle', };
        worksheet.getCell(rowStartContent, (index + 1)).font = { size: 11, bold: true, name: 'Times New Roman' };
        worksheet.getCell(rowStartContent, (index + 1)).border = { top: { style: 'thin' }, left: { style: 'thin' }, bottom: { style: 'thin' }, right: { style: 'thin' } };
      }

      worksheet.getColumn(2).width = 40;
      worksheet.getColumn(2).alignment = { horizontal: 'left', vertical: 'middle', wrapText: true };
      worksheet.getColumn(1).width = 7;
      worksheet.getColumn(1).alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
      worksheet.getColumn(3).width = 20;

      // Save Excel File
      workbook.xlsx.writeBuffer().then((dataBuffer: ArrayBuffer) => {
        const blob = new Blob([dataBuffer], { type: this.EXCEL_TYPE });
        fs.saveAs(blob, excelFileName + this.EXCEL_EXTENSION);
      });
    }
  }

  // Cập nhật validate date trong form tìm kiếm
  validateForm() {
    // Chuyển đổi lại thời gian startDate / endDate
    this.startDate.setHours(0, 0, 0, 0);
    this.endDate.setHours(23, 59, 59, 999);

    let startTime = this.startDate.getTime();
    let endTime = this.endDate.getTime();
    let language = localStorage.getItem('language');

    let data = {
      errMessage: "",
      status: false
    }

    if (this.startDate == null || this.endDate == null) {
      data.errMessage = language == 'vi' ? 'Vui lòng nhập đầy đủ thông tin ngày tra' : 'Please enter complete information for the lookup';
    } else if (startTime > endTime) {
      data.errMessage = language == 'vi' ? 'Vui lòng nhập thông tin từ ngày nhỏ hơn đến ngày' : 'Please enter information from date less than to date';
    } else {
      data.status = true;
    }

    if (data == null || !data?.status) {
      this.snackbarService.openSnackBar(0, "Không hợp lệ", data.errMessage, 'error_notification', this.config.expiredTime);
      this.listHoSo = [];
      this.countResult = 0;
      return false;
    }

    return true;
  }
}