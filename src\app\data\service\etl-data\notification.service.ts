import {Injectable} from '@angular/core';
import {ApiProviderService} from 'src/app/core/service/api-provider.service';
import {Observable, Subject} from 'rxjs';
import {HttpClient, HttpHeaders} from '@angular/common/http';

@Injectable({
  providedIn: 'root'
})
export class NotificationService {
  private notificationURL = this.apiProviderService.getUrl('digo', 'notification') + '/notification-ws-service';
  private notificationWSURL = this.apiProviderService.getUrl('digo', 'notificationWS') + '/notification-ws-service';
  public notificationDataChangeSubject = new Subject<NotificationData[]>();

  constructor(
    private apiProviderService: ApiProviderService,
    private http: HttpClient,
  ) {
  }

  public getNotificationAgency(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.notificationURL + '/notification/agency?' + searchString, {headers});
  }

  public getNotificationCurrent(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.notificationURL + '/notification/current?' + searchString, {headers});
  }

  public getNotificationAssignee(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.notificationURL + '/notification/assignee?' + searchString, {headers});
  }

}


export interface NotificationData {

  id: string;

  name: string;

  stage: number;

  count: number;
}
