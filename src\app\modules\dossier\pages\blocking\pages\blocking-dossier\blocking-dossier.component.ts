import { AfterViewInit, ChangeDetectorRef, Component, OnInit, ViewChild } from '@angular/core';
import { FormControl, FormGroup } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import { KeycloakService } from 'keycloak-angular';
import { Router } from '@angular/router';
import { EnvService } from 'src/app/core/service/env.service';
import { MatTableDataSource } from '@angular/material/table';
import { SnackbarService } from 'src/app/data/service/snackbar/snackbar.service';
import { ProcedureService } from 'src/app/data/service/procedure/procedure.service';

import { MainService } from 'src/app/data/service/main/main.service';
import { MatPaginator } from '@angular/material/paginator';
import { DeploymentService } from 'src/app/data/service/deployment.service';
import {
  ConfirmDeleteDialogModel,
  DeleteDossierComponent
} from '../../dialogs/delete-dossier/delete-dossier.component';
import {
  AddDossierComponent,
  ConfirmAddBlockingDossierDialogModel
} from '../../dialogs/add-dossier/add-dossier.component';
import { UserService } from 'src/app/data/service/user.service';
import { ConfigService } from 'src/app/data/service/dossier/config.service';
import { BlockingDossierService } from 'data/service/qnm-blocking-dossier/blocking-dossier.service';
import { DatePipe } from '@angular/common';

export interface DossierBlocking {
  stt: number;
  certificateId: string;
  ownerFullname: string;
  slotId: string;
  blockDate: Date;
  content: string;
}

@Component({
  selector: 'app-blocking-dossier',
  templateUrl: './blocking-dossier.component.html',
  styleUrls: ['./blocking-dossier.component.scss']
})

export class BlockingDossierComponent implements OnInit , AfterViewInit {

  constructor(
    private router: Router,
    private keycloak: KeycloakService,
    private dialog: MatDialog,
    private datePipe: DatePipe,
    private cdRef: ChangeDetectorRef,
    private envService: EnvService,
    private mainService: MainService,
    private userService: UserService,
    private configService: ConfigService,
    private snackbarService: SnackbarService,
    private procedureService: ProcedureService,
    private deploymentService: DeploymentService,
    private blockingDossierService: BlockingDossierService,
  ) {
    this.dataSource = new MatTableDataSource(this.ELEMENTDATA);
  }
  config = this.envService.getConfig();
  env = this.deploymentService.getAppDeployment()?.env;
  pageTitle = {
    vi: `Quản lý hồ sơ ngăn chặn`,
    en: `Management blocking dossier`
  };

  searchForm = new FormGroup({
    keyword: new FormControl(''),
    statusId: new FormControl('')
  });

  formSearch: any = this.searchForm.getRawValue();
  listForm = [];
  displayedColumns: string[] = ['stt', 'certificateId', 'ownerFullname', 'slotId', 'blockDate', 'content', 'action'];
  listTemplate: any [] = [{
    id: 0,
    name: 'Chưa thông báo'
  },
  {
    id: 1,
    name: 'Đã thông báo'
  }];
  ELEMENTDATA: DossierBlocking[] = [];
  dataSource: MatTableDataSource<DossierBlocking>;

  size = 10;
  pageIndex = 1;
  countResult = 0;
  pgSizeOptions = this.config.pageSizeOptions;
  selectedLang: string;
  isCheckedAll = false;
  syncCode = -1;
  adapterError = false;

  agencyCodeString = '';
  currentDate = new Date();

  @ViewChild(MatPaginator) paginator: MatPaginator;
  ngOnInit(): void {
    const searchString = `?page=0&spec=page&sort=createdDate,desc&size=${this.size}`;
    this.selectedLang = localStorage.getItem('language');
    this.mainService.setPageTitle(this.pageTitle[localStorage.getItem('language')]);
    this.getListBlockingDossier(searchString);
    //  const permissions = this.userService.getUserPermissions();
    //  console.log('permissions',permissions)
  }
  ngAfterViewInit() {
    this.cdRef.detectChanges();

  }
  getUserInfo(): Promise<any> {
    return new Promise<any>((resolve, reject) => {
      this.keycloak.loadUserProfile().then(user => {
        resolve(user);
        console.log('user', user);
      }, err => {
        resolve(err);
      });
    });
  }
  filter(){
    this.formSearch = this.searchForm.getRawValue();
    this.pageIndex = 1;
    const searchString = `?page=0&spec=page&size=${this.size}&keyword=${this.formSearch.keyword}`;
    this.getListBlockingDossier(searchString);
  }
  paginate(event: any, type) {
    switch (type) {
      case 0:
        this.pageIndex = event;
        this.getListBlockingDossier(`?page=${this.pageIndex - 1}&size=${this.size}&keyword=${this.formSearch.keyword}&spec=page&sort=createdDate,desc`);
        break;
      case 1:
        this.pageIndex = 1;
        this.getListBlockingDossier(`?page=${this.pageIndex - 1}&size=${this.size}&keyword=${this.formSearch.keyword}&spec=page&sort=createdDate,desc`);
        break;
    }
  }

  getListBlockingDossier(searchString) {
    // noinspection DuplicatedCode
    this.blockingDossierService.getListBlockingDossier(searchString).subscribe(data => {
      console.log('data', data);
      this.ELEMENTDATA = [];
      this.countResult = data.totalElements;
      for (let i = 0; i < data.numberOfElements; i++) {
        data.content[i].stt = this.size * (this.pageIndex - 1) + (i + 1);
        data.content[i].certificateId = data.content[i]?.certificate?.certificateId;
        data.content[i].ownerFullname = data.content[i]?.certificate?.ownerFullname;
        data.content[i].slotId = data.content[i]?.certificate?.slotId;
        this.ELEMENTDATA.push(data.content[i]);
      }
      this.dataSource.data = this.ELEMENTDATA;
    });
  }

  addFormDialog(){
    const dialogData = new ConfirmAddBlockingDossierDialogModel(null, false);
    const dialogRef = this.dialog.open(AddDossierComponent, {
      width: '900px',
      data: dialogData,
      disableClose: true,
      autoFocus: false,
      position: {
        top: '3em'
      },
      maxHeight: '91vh'
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
      const res = dialogResult;
      console.log('res', res);
      if (res === true) {
        const msgObj = {
          vi: 'Đã thêm mới!',
          en: 'Created!'
        };
        this.snackbarService.openSnackBar(1, '',
          msgObj[this.selectedLang],
          'success_notification',
          this.config.expiredTime
        );
        this.paginate(this.pageIndex, 1);
      }
      if (res === false) {
        this.snackbarService.openSnackBar(0,
          this.envService.getTranslateNotificationLabel(this.selectedLang, 'error', 'post'),
          res.name, 'error_notification', this.config.expiredTime);
      }
    });
  }

  updateFormDialog(id){
    const dialogData = new ConfirmAddBlockingDossierDialogModel(id, true);
    const dialogRef = this.dialog.open(AddDossierComponent, {
      width: '900px',
      data: dialogData,
      disableClose: true,
      autoFocus: false
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
      const res = dialogResult;
      if (res === true) {
        const msgObj = {
          vi: 'Đã cập nhật!',
          en: 'Updated!'
        };
        this.snackbarService.openSnackBar(1, '',
          msgObj[this.selectedLang],
          'success_notification',
          this.config.expiredTime
        );
        this.paginate(this.pageIndex, 0);
      }
      if (res === false) {
        this.snackbarService.openSnackBar(0, this.envService.getTranslateNotificationLabel(this.selectedLang, 'error', 'put'), res.name, 'error_notification', this.config.expiredTime);
      }
    });
  }

  deleteFormDialog(id, name){
    const dialogData = new ConfirmDeleteDialogModel(id, name);
    const dialogRef = this.dialog.open(DeleteDossierComponent, {
      width: '500px',
      data: dialogData,
      disableClose: true,
      autoFocus: false
    });
    // noinspection DuplicatedCode
    dialogRef.afterClosed().subscribe(dialogResult => {
      const res = dialogResult;
      if (res === true) {
        const msgObj = {
          vi: 'Đã xoá!',
          en: 'Deleted!'
        };
        this.snackbarService.openSnackBar(1, '',
          msgObj[this.selectedLang],
          'success_notification',
          this.config.expiredTime
        );
        this.paginate(this.pageIndex, 0);
      }
      if (res === false) {
        const msgObj = {
          vi: 'Không thể xoá!',
          en: `Can't delete!`
        };
        this.snackbarService.openSnackBar(0, '',
          msgObj[this.selectedLang],
          'error_notification',
          this.config.expiredTime
        );
      }
    });
  }

  exportToExcelAll() {
    const searchString = `?page=${this.pageIndex - 1}&size=${this.size}&keyword=${this.formSearch.keyword}&spec=all&sort=createdDate,desc`;
    const reportTitle = `Danh sách hồ sơ ngăn chặn`.toUpperCase();
    const name = `Danh sach ho so ngan chan ${this.datePipe.transform(this.currentDate, 'dd/MM/yyyy')}`;
    this.blockingDossierService.getListBlockingDossier(searchString).subscribe(data => {
      console.log('data', data);
      if (data?.totalElements) {
        this.blockingDossierService.exportToExcel(reportTitle, data.content, name, 'Sheet1');
      } else {
        const msgObj = {
          vi: 'Không có dữ liệu để xuất ',
          en: 'Dont have data to export'
        };
        this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
      }
    });
  }

}
