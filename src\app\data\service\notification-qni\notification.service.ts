import { Injectable } from '@angular/core';
import { rejects } from 'assert';
import { resolve } from 'dns';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { ApiProviderService } from 'src/app/core/service/api-provider.service';
import { Observable } from 'rxjs';
import { IFormMap } from '../../schema/form-map';
@Injectable({
  providedIn: 'root'
})
export class NotificationQniService {
  private basepad = this.apiProviderService.getUrl('digo', 'basepad');
  private notifyPath = this.apiProviderService.getUrl('digo', 'padman') + '/dossier-notification';
  private adapter = this.apiProviderService.getUrl('digo', 'adapter');
  private filePath = this.apiProviderService.getUrl('digo', 'fileman') + '/file/';
  constructor(
    private http: HttpClient,
    private apiProviderService: ApiProviderService
  ) { }
  getListNotify(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    console.log('notifyPath:',this.notifyPath);
    console.log('searchString:', searchString);
    return this.http.get(this.notifyPath+"/search" + searchString, { headers });
  }


  createNotify(requestBody) {
    try{
      console.log('WS')
      let headers = new HttpHeaders();
      headers = headers.set('Content-Type', 'application/json');
      return this.http.post<any>(this.notifyPath, requestBody, { headers });
    }catch(e){
      console.log(e)
    }

  }

  getNotifyInfo(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.notifyPath +"/" + id, { headers });
  }

  updateNotify(id, requestBody) {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.put<any>(this.notifyPath +"/" + id, requestBody, { headers });
  }

  deleteNotify(id: string) {
    let headers = new HttpHeaders();
    headers = headers.append('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.delete(this.notifyPath +"/"+ id, { headers });
  }
  publishNotify(id, requestBody) {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.put<any>(this.notifyPath + id, requestBody, { headers });
  }
  uploadMultiFilePromise(files:File[],filename): Promise<any> {
    let headers = new HttpHeaders();
    headers = headers.append('Accept', 'application/json');
    const formData: FormData = new FormData();
    files.forEach(f => {
      console.log('files',f)
      const file: File = f;
      formData.append('files', file, filename);
    });
    return this.http.post<any>(this.filePath + '--multiple', formData, { headers }).toPromise();
  }
  uploadFile(formData:any): Promise<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.post<any>(this.filePath , formData, { headers }).toPromise();
  }
  syncOverDue() {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.put<any>(this.notifyPath + "/sync-dossier-notification", { headers });
  }
  syncResolve() {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.put<any>(this.notifyPath + "/sync-resolve-dossier-notification", { headers });
  }
  downloadFile(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.filePath + id, { responseType: 'blob' as 'json' }).pipe();
  }
}
