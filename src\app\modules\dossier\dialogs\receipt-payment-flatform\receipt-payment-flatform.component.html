<button mat-icon-button class="close-button" (click)="onDismiss()">
    <mat-icon>close</mat-icon>
</button>
<h3 class="dialog_title" mat-dialog-title>Danh sách {{paymentPlatformButtonNameLable ? paymentPlatformButtonNameLable : 'biên lai VNPT Payment Platform'}}</h3>
<div mat-dialog-content class="dialog_content">

    <div class="receipt" fxLayout="row" fxLayout.xs="column" fxLayout.sm="column">
        <mat-table [dataSource]="receiptDataSource" fxFlex='grow'>
            <ng-container matColumnDef="date">
                <mat-header-cell *matHeaderCellDef i18n>Ng<PERSON>y thực hiện</mat-header-cell>
                <mat-cell *matCellDef="let row" i18n-data-label="Ngày thực hiện">
                    {{row.createdDate | date : 'dd/MM/yyyy HH:mm:ss'}}
                </mat-cell>
            </ng-container>

            <ng-container matColumnDef="supplier">
                <mat-header-cell *matHeaderCellDef i18n>Nhà cung cấp</mat-header-cell>
                <mat-cell *matCellDef="let row" i18n-data-label="Nhà cung cấp">
                    {{row?.paymentMethod?.code == "E_PAYMENT_HCM_LGSP" ? "E-Payment HCM LGSP": "VNPT Payment Platform"}}
                </mat-cell>
            </ng-container>

            <ng-container matColumnDef="detail">
                <mat-header-cell *matHeaderCellDef i18n>Tổng tiền</mat-header-cell>
                <mat-cell *matCellDef="let row" i18n-data-label="Chi tiết">
                    <!-- <p class="items" *ngFor="let productObj of row.data.product">
                        <span>{{productObj.prodName}}:</span>
                        <span>{{productObj.prodAmount}} VNĐ</span>
                    </p> -->
                    {{row.totalString}}
                </mat-cell>
            </ng-container>

            <!-- <ng-container matColumnDef="person">
                <mat-header-cell *matHeaderCellDef i18n>Người thực hiện</mat-header-cell>
                <mat-cell *matCellDef="let row" i18n-data-label="Người thực hiện">
                    {{row.applicantName}}
                </mat-cell>
            </ng-container> -->

            <!-- <ng-container matColumnDef="status">
                <mat-header-cell *matHeaderCellDef i18n>Trạng thái</mat-header-cell>
                <mat-cell *matCellDef="let row" i18n-data-label="Trạng thái">
                    <span *ngIf="row.status === 0" i18n>Đã hủy</span>
                    <span *ngIf="row.status === 1" i18n>Đã phát hành</span>
                    <span *ngIf="row.status === 2" i18n>Đã thanh toán</span>
                </mat-cell>
            </ng-container> -->

            <ng-container matColumnDef="action">
                <mat-header-cell *matHeaderCellDef i18n>Thao tác</mat-header-cell>
                <mat-cell *matCellDef="let row" i18n-data-label="Thao tác">
                    <button mat-icon-button [matMenuTriggerFor]="menu" *ngIf="row.status !== 0">
                        <mat-icon>more_horiz</mat-icon>
                    </button>
                    <mat-menu #menu="matMenu">
                        <button mat-menu-item (click)="printReceipt(row)" *ngIf="row?.paymentMethod?.code != 'E_PAYMENT_HCM_LGSP'"><span i18n>Xem biên lai</span></button>
                        <button mat-menu-item (click)="rePrintReceipt(row)" *ngIf="row?.paymentMethod?.code != 'E_PAYMENT_HCM_LGSP'"><span>Lấy lại biên lai</span></button>
                        <button mat-menu-item (click)="getOrderInfoLGSPHCM(row)" *ngIf="row?.paymentMethod?.code == 'E_PAYMENT_HCM_LGSP'"><span>Thông tin thanh toán E-Payment</span></button>
                        <!-- <button mat-menu-item *ngIf="row.status === 2" (click)="printConvertReceipt(row)"><span i18n>In biên lai chuyển đổi</span></button>
                        <button mat-menu-item *ngIf="row.status === 2" (click)="unPaymentReceipt(row)"><span i18n>Hủy thanh toán</span></button>
                        <button mat-menu-item *ngIf="row.status === 1" (click)="paymentReceipt(row)"><span i18n>Thanh toán</span></button>
                        <button mat-menu-item *ngIf="row.status === 1 || row.status === 2" (click)="cancelReceipt(row)"><span i18n>Hủy phát hành</span></button> -->
                    </mat-menu>
                </mat-cell>
            </ng-container>

            <mat-header-row *matHeaderRowDef="receiptDisplayedColumns"></mat-header-row>
            <mat-row *matRowDef="let row; columns: receiptDisplayedColumns;"></mat-row>
        </mat-table>
    </div>
    <div class="frm_Pagination">
        <ul class="temp_Arr">
            <li
                *ngFor="let item of ELEMENTDATA  | paginate: {itemsPerPage: size, currentPage: page, totalItems: countResult, id: 'pgnx'}">
            </li>
        </ul>
        <div class="pageSize">
            <span class="lbl" i18n>Hiển thị </span>
            <mat-form-field appearance="outline">
                <mat-select [(value)]="size" (valueChange)="paginate(pageIndex, 1)">
                    <mat-option *ngFor='let opt of pgSizeOptions;' [value]="opt">{{opt}}</mat-option>
                </mat-select>
            </mat-form-field>
            <span class="lbl"><span i18n>trên</span> {{countResult}} <span i18n>bản ghi</span></span>
        </div>
        <div class="control">
            <pagination-controls id="pgnx" (pageChange)="page = $event; paginate(page, 0)" responsive="true"
                previousLabel="" nextLabel="">
            </pagination-controls>
        </div>
    </div>
</div>