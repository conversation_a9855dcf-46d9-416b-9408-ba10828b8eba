import { Injectable } from '@angular/core';
import { Workbook } from 'exceljs';
import { Observable } from 'rxjs';
import { MatDialog } from '@angular/material/dialog';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { EnvService } from 'core/service/env.service';
import { DeploymentService } from '../deployment.service';
import { SnackbarService } from 'data/service/snackbar/snackbar.service';
import { ApiProviderService } from 'src/app/core/service/api-provider.service';
import * as fs from 'file-saver';

const EXCEL_TYPE = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8';
const EXCEL_EXTENSION = '.xlsx';

@Injectable({
    providedIn: 'root'
})

export class KHAStatisticService {
    language = localStorage.getItem('language') === 'vi' ? 228 : 46;
    config = this.envService.getConfig();

    constructor(
        private dialog: MatDialog,
        private http: HttpClient,
        private envService: EnvService,
        private snackbar: SnackbarService,
        private deploymentService: DeploymentService,
        private apiProviderService: ApiProviderService,
    ) { }

    private padmanURL = this.apiProviderService.getUrl('digo', 'padman');
    private basedataURL = this.apiProviderService.getUrl('digo', 'basedata');
    private basepadURL = this.apiProviderService.getUrl('digo', 'basepad');

    // getDossierStatisticGeneralV2(body, iLisQNM): Observable<any> {
    //     let headers = new HttpHeaders();
    //     headers = headers.set('Accept-Language', localStorage.getItem('language'));
    //     headers = headers.append('Content-Type', 'application/json');
    //     headers.append('Access-Control-Allow-Origin', '*');

    //     // return this.http.post(this.padmanURL + '/qnm-dossier-statistic/--statistic-general-v2?ilis-qnm='+iLisQNM,  body, { headers }).pipe();
    //     return this.http.post(this.padmanURL + '/qnm-dossier-statistic/--statistic-general?ilis-qnm=' + iLisQNM, body, { headers }).pipe();
    // }

    getDossierStatistic01202006cKHA(body,iLisKHA): Observable<any> {
        let headers = new HttpHeaders();
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        headers = headers.append('Content-Type', 'application/json');
        headers.append('Access-Control-Allow-Origin', '*');
    
        //return this.http.post("http://localhost:8081" + '/dossier-kha/--statistic-general?ilis-kha='+iLisKHA,  body, { headers }).pipe();
        return this.http.post(this.padmanURL + '/dossier-kha/--statistic-general?ilis-kha='+iLisKHA,  body, { headers }).pipe();
    }
    getListSectorsByAgencyQNI(searchString): Observable<any> {
        let headers = new HttpHeaders();
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        return this.http.get(this.basepadURL + '/sector/--by-agency-qni' + searchString, { headers }).pipe();
    }

    getListProcedureByAgency(searchString): Observable<any> {
        let headers = new HttpHeaders();
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        return this.http.get(this.basepadURL + '/procedure/--find-all-public' + searchString, { headers }).pipe();
    }

    getListProcedureByAgencyQNI(searchString): Observable<any> {
        let headers = new HttpHeaders();
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        return this.http.get(this.basepadURL + '/procedure/--find-all-procedure-qni' + searchString, { headers }).pipe();
    }

    getListAgencyByTagId(searchString): Observable<any> {
        let headers = new HttpHeaders();
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        headers = headers.append('Content-Type', 'application/json');
        headers.append('Access-Control-Allow-Origin', '*');

        return this.http.get(this.basedataURL + '/qni-agency/--by-ancestor-id' + searchString, { headers }).pipe();
    }

    getListAgencyByParent(searchString): Observable<any> {
        let headers = new HttpHeaders();
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        headers = headers.append('Content-Type', 'application/json');
        headers.append('Access-Control-Allow-Origin', '*');
        return this.http.get(this.basedataURL + '/agency/--by-parent-agency' + searchString, { headers }).pipe();
    }

    getDossierStatisticGeneralDetail(searchString): Observable<any> {
        let headers = new HttpHeaders();
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        headers = headers.append('Content-Type', 'application/json');
        headers.append('Access-Control-Allow-Origin', '*');
        return this.http.get(this.padmanURL + '/dossier-kha/--detail' + searchString, { headers }).pipe();
        //return this.http.get("http://localhost:8081" + '/dossier-kha/--detail' + searchString, { headers }).pipe();
    }

    getDossierStatisticGeneralDetailV2(body, searchpage): Observable<any> {
        let headers = new HttpHeaders();
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        headers = headers.append('Content-Type', 'application/json');
        headers.append('Access-Control-Allow-Origin', '*');
        return this.http.post(this.padmanURL + '/dossier-kha/--detail' + searchpage, body, { headers }).pipe();
        //return this.http.post("http://localhost:8081" + '/dossier-kha/--detail' + searchpage, body, { headers }).pipe();
    }

    exportToExcelStatisticGeneralDetail(params: string): any {
        return new Promise((resolve) => {
            //this.http.get(this.padmanURL + '/qnm-dossier-statistic/--detail/--export' + params, {
            this.http.get(this.padmanURL + '/dossier-kha/--detail/--export' + params, {
            //this.http.get("http://localhost:8081" + '/dossier-kha/--detail/--export' + params, {
                observe: 'response',
                responseType: 'blob'
            }).toPromise().then(res => {
                const blob = new Blob([res.body], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
                let filename = 'bao_cao_chi_tiet.xlsx';
                if (res.headers.get('content-disposition') != null) {
                    filename = res.headers.get('content-disposition').split(';')[1].split('=')[1].replace(/"/g, '');
                }
                const blobUrl = URL.createObjectURL(blob);
                const xhr = new XMLHttpRequest();
                xhr.responseType = 'blob';

                xhr.onload = () => {
                    const recoveredBlob = xhr.response;
                    const reader = new FileReader();
                    reader.onload = () => {
                        const base64data = reader.result.toString();
                        const anchor = document.createElement('a');
                        anchor.download = filename;
                        anchor.href = base64data;
                        anchor.click();
                    };
                    reader.readAsDataURL(recoveredBlob);
                };

                xhr.open('GET', blobUrl);
                xhr.send();
                resolve(true);
            }).catch(err => {
                if (err.status === 500) {
                    const message = {
                        vi: 'Hệ thống tạm thời không thể xuất file excel!',
                        en: 'The system is temporarily unable to export the excel file!'
                    };
                    this.snackbar.openSnackBar(0, '', message[localStorage.getItem('language')], 'error_notification', this.config.expiredTime);
                }
                resolve(false);
            });
        });
    }

    public exportToExcelStatisticGeneral(
        reportHeading: string,
        reportSubHeading: string,
        nameReport: string,
        subNameReport: string,
        json: any[],
        footerData: any[],
        excelFileName: string,
        sheetName: string
    ) {
        const data = json;
        // create workbook and worksheet
        const workbook = new Workbook();
        workbook.creator = 'Snippet Coder';
        workbook.lastModifiedBy = 'SnippetCoder';
        workbook.created = new Date();
        workbook.modified = new Date();
        const worksheet = workbook.addWorksheet(sheetName);

        worksheet.getColumn('A').font = { name: 'Times New Roman', size: 12 };
        worksheet.getColumn('B').font = { name: 'Times New Roman', size: 12 };
        worksheet.getColumn('C').font = { name: 'Times New Roman', size: 12 };
        worksheet.getColumn('D').font = { name: 'Times New Roman', size: 12 };
        worksheet.getColumn('E').font = { name: 'Times New Roman', size: 12 };
        worksheet.getColumn('F').font = { name: 'Times New Roman', size: 12 };
        worksheet.getColumn('G').font = { name: 'Times New Roman', size: 12 };
        worksheet.getColumn('H').font = { name: 'Times New Roman', size: 12 };
        worksheet.getColumn('I').font = { name: 'Times New Roman', size: 12 };
        worksheet.getColumn('J').font = { name: 'Times New Roman', size: 12 };
        worksheet.getColumn('K').font = { name: 'Times New Roman', size: 12 };
        worksheet.getColumn('L').font = { name: 'Times New Roman', size: 12 };
        worksheet.getColumn('M').font = { name: 'Times New Roman', size: 12 };

        // Add header row
        worksheet.addRow([]);
        worksheet.mergeCells('A1:B1');
        worksheet.getCell('A1').value = nameReport;
        worksheet.getCell('A1').alignment = { horizontal: 'center', vertical: 'middle' };

        worksheet.mergeCells('A2:B2');
        worksheet.getCell('A2').value = subNameReport;
        worksheet.getCell('A2').alignment = { horizontal: 'center', vertical: 'middle' };

        worksheet.mergeCells('H1:M1');
        if (this.language === 228) {
            worksheet.getCell('H1').value = 'Đơn vị báo cáo: ';
        } else {
            worksheet.getCell('H1').value = 'Reporting unit: ';
        }
        worksheet.getCell('H1').alignment = { horizontal: 'left', vertical: 'middle' };
        worksheet.getCell('H1').font = { size: 12, bold: true, name: 'Times New Roman' };

        worksheet.mergeCells('H2:M4');
        if (this.language === 228) {
            worksheet.getCell('H2').value =
                '+UBND cấp xã, cơ quan chuyên môn thuộc UBND cấp huyện.\n+Cơ quan chuyên môn thuộc UBND cấp tỉnh.\n+Cơ quan, đơn vị trực thuộc bộ, cơ quan ngang bộ.\n+Cơ quan trung ương tổ chức theo ngành dọc cấp xã.';
        } else {
            worksheet.getCell('H2').value = '+Units under ministries, ministerial-level agencies;\n+Specialized Division of People\'s Committee of district / town / city ...;\n+Commune People\'s Committee';
        }
        worksheet.getRow(3).height = 33;
        worksheet.getRow(4).height = 33;

        worksheet.mergeCells('H5:M5');
        if (this.language === 228) {
            worksheet.getCell('H5').value = 'Đơn vị nhận báo cáo: ';
        } else {
            worksheet.getCell('H5').value = 'Unit receiving report: ';
        }
        worksheet.getCell('H5').alignment = { horizontal: 'left', vertical: 'middle' };
        worksheet.getCell('H5').font = { size: 12, bold: true, name: 'Times New Roman' };

        worksheet.mergeCells('H6:M7');
        if (this.language === 228) {
            worksheet.getCell('H6').value = '+UBND cấp huyện.\n+UBND cấp tỉnh.\n+Bộ, cơ quan ngang bộ.\n+Cơ quan trung ương tổ chức theo ngành dọc cấp huyện.';
        } else {
            worksheet.getCell('H6').value = '+Ministries, Ministerial-level unit \n(Office of ministries, ministerial-level agencies);\n+District People\'s Committee, Provincial People\'s Committee Office\n(Division of Administrative Procedure)';
        }
        worksheet.getCell('H6').alignment = { horizontal: 'left', vertical: 'middle' };
        worksheet.getRow(7).height = 70;

        worksheet.mergeCells('K9:M9');
        if (this.language === 228) {
            worksheet.getCell('K9').value = 'Đơn vị tính: Số hồ sơ TTHC.';
        } else {
            worksheet.getCell('K9').value = 'Unit: Number of dossiers.';
        }
        worksheet.mergeCells('C1:G4');
        worksheet.getCell('C1').value = reportHeading;
        worksheet.getCell('C1').alignment = { horizontal: 'center', vertical: 'middle' };
        worksheet.getCell('C1').font = { size: 15, bold: true, name: 'Times New Roman' };

        worksheet.mergeCells('C5:G5');
        if (this.language === 228) {
            worksheet.getCell('C5').value = 'Kỳ báo cáo: Quý.../Năm...';
        } else {
            worksheet.getCell('C5').value = 'Reporting period: Quarter.../year...';
        }
        worksheet.getCell('C5').alignment = { horizontal: 'center', vertical: 'middle' };
        worksheet.getCell('C5').font = { size: 12, name: 'Times New Roman' };

        worksheet.mergeCells('C6:G6');
        worksheet.getCell('C6').value = reportSubHeading;
        worksheet.getCell('C6').alignment = { horizontal: 'center', vertical: 'middle' };
        worksheet.getCell('C6').font = { size: 12, italic: true, name: 'Times New Roman' };

        // NỘI DUNG TABLE-HEADER
        worksheet.mergeCells('A10:A12');
        worksheet.mergeCells('B10:B12');
        worksheet.mergeCells('C10:F10');
        worksheet.mergeCells('G10:J10');
        worksheet.mergeCells('K10:M10');
        worksheet.mergeCells('N10:N12');
        worksheet.mergeCells('O10:O12');
        worksheet.mergeCells('P10:P12');
        worksheet.mergeCells('Q10:Q12');
        worksheet.mergeCells('C11:C12');
        worksheet.mergeCells('D11:E11');
        worksheet.mergeCells('F11:F12');
        worksheet.mergeCells('G11:G12');
        worksheet.mergeCells('H11:H12');
        worksheet.mergeCells('I11:I12');
        worksheet.mergeCells('J11:J12');
        worksheet.mergeCells('K11:K12');
        worksheet.mergeCells('L11:L12');
        worksheet.mergeCells('M11:M12');

        worksheet.getCell('A13').value = '(1)';
        worksheet.getCell('B13').value = '(2)';
        worksheet.getCell('C13').value = '(3)=(4)+(5)+(6)';
        worksheet.getCell('D13').value = '(4)';
        worksheet.getCell('E13').value = '(5)';
        worksheet.getCell('F13').value = '(6)';
        worksheet.getCell('G13').value = '(7)=(8)+(9)+(10)';
        worksheet.getCell('H13').value = '(8)';
        worksheet.getCell('I13').value = '(9)';
        worksheet.getCell('J13').value = '(10)';
        worksheet.getCell('K13').value = '(11)=(12)+(13)';
        worksheet.getCell('L13').value = '(12)';
        worksheet.getCell('M13').value = '(13)';
        worksheet.getCell('N13').value = '(14)';
        worksheet.getCell('O13').value = '(15)';
        worksheet.getCell('P13').value = '(16)';
        worksheet.getCell('Q13').value = '(17)';

        if (this.language === 228) {
            worksheet.getCell('A10').value = 'STT';
            worksheet.getCell('B10').value = 'Đơn vị';
            worksheet.getCell('C10').value = 'Số lượng hồ sơ tiếp nhận';
            worksheet.getCell('G10').value = 'Số lượng hồ sơ đã giải quyết';
            worksheet.getCell('K10').value = 'Số lượng hồ sơ đang giải quyết';

            worksheet.getCell('N10').value = 'Tổng số hồ sơ thanh toán trực tuyến';
            worksheet.getCell('O10').value = 'Tỷ lệ trước và đúng hạn';
            worksheet.getCell('P10').value = 'Tỷ lệ hồ sơ trực tuyến';
            worksheet.getCell('Q10').value = 'Tỷ lệ thanh toán trực tuyến';


            worksheet.getCell('C11').value = 'Tổng số';
            worksheet.getCell('D11').value = 'Trong kỳ';
            worksheet.getCell('D12').value = 'Trực tuyến';
            worksheet.getCell('E12').value = 'Trực tiếp, dịch vụ bưu chính';
            worksheet.getCell('F11').value = 'Từ kỳ trước';
            worksheet.getCell('G11').value = 'Tổng số';
            worksheet.getCell('H11').value = 'Trước hạn';
            worksheet.getCell('I11').value = 'Đúng hạn';
            worksheet.getCell('J11').value = 'Quá hạn';
            worksheet.getCell('K11').value = 'Tổng số';
            worksheet.getCell('L11').value = 'Trong hạn';
            worksheet.getCell('M11').value = 'Quá hạn';

        } else {
            worksheet.getCell('A10').value = 'No.';
            worksheet.getCell('B10').value = 'Name of sector';
            worksheet.getCell('C10').value = 'Number of dossiers received';
            worksheet.getCell('G10').value = 'Number of dossiers resolved';
            worksheet.getCell('K10').value = 'Number of dossiers being resolved';

            worksheet.getCell('N10').value = 'Number of dossiers payment online';
            worksheet.getCell('O10').value = 'Percentage early and on time';
            worksheet.getCell('P10').value = 'Percentage dossier online';
            worksheet.getCell('Q10').value = 'Percentage dossier payment online';

            worksheet.getCell('C11').value = 'Total';
            worksheet.getCell('D11').value = 'Current period';
            worksheet.getCell('D12').value = 'Online';
            worksheet.getCell('E12').value = 'Direct, Postal Service';
            worksheet.getCell('F11').value = 'Previous period';
            worksheet.getCell('G11').value = 'Total';
            worksheet.getCell('H11').value = 'Early';
            worksheet.getCell('I11').value = 'On time';
            worksheet.getCell('J11').value = 'Out of date';
            worksheet.getCell('K11').value = 'Total';
            worksheet.getCell('L11').value = 'On time';
            worksheet.getCell('M11').value = 'Out of date';
        }

        worksheet.getColumn('B').width = 45;
        worksheet.getColumn('C').width = 17;
        worksheet.getColumn('E').width = 10;
        worksheet.getColumn('G').width = 17;
        worksheet.getColumn('K').width = 17;

        worksheet.getRow(2).height = 27;
        worksheet.getColumn('C').alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
        worksheet.getColumn('D').alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
        worksheet.getColumn('E').alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
        worksheet.getColumn('F').alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
        worksheet.getColumn('G').alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
        worksheet.getColumn('H').alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
        worksheet.getColumn('I').alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
        worksheet.getColumn('J').alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
        worksheet.getColumn('K').alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
        worksheet.getColumn('L').alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
        worksheet.getColumn('M').alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
        worksheet.getColumn('N').alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
        worksheet.getColumn('O').alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
        worksheet.getColumn('P').alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
        worksheet.getColumn('Q').alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };

        worksheet.getCell('H1').style.alignment = { horizontal: 'left', vertical: 'middle', wrapText: true };
        worksheet.getCell('H2').style.alignment = { horizontal: 'left', vertical: 'middle', wrapText: true };
        worksheet.getCell('H5').style.alignment = { horizontal: 'left', vertical: 'middle', wrapText: true };
        worksheet.getCell('H6').style.alignment = { horizontal: 'left', vertical: 'middle', wrapText: true };

        worksheet.properties.outlineLevelCol = 2;
        worksheet.properties.defaultRowHeight = 15;

        let i = 10;
        const j = 13;
        for (i; i <= j; i++) {
            let k = 1;
            const l = 17;
            for (k; k <= l; k++) {
                worksheet.findCell(i, k).alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
                worksheet.findCell(i, k).fill = {
                    type: 'pattern',
                    pattern: 'solid',
                    fgColor: { argb: 'C0C0C0C0' },
                    bgColor: { argb: 'FF0000FF' }
                };
                worksheet.findCell(i, k).border = { top: { style: 'thin' }, left: { style: 'thin' }, bottom: { style: 'thin' }, right: { style: 'thin' } };
                worksheet.findCell(i, k).font = { size: 12, bold: true, name: 'Times New Roman' };
            }
        }

        // get all columns from JSON
        let columnsArray: any[];
        for (const key in json) {
            if (json.hasOwnProperty(key)) {
                columnsArray = Object.keys(json[key]);
            }
        }
        columnsArray = columnsArray.filter(column => !['sectorId', 'agencyId'].includes(column));

        // Add Data and Conditional Formatting
        data.forEach((element: any) => {
            const eachRow = [];
            columnsArray.forEach((column) => {
                eachRow.push(element[column]);
            });
            // eachRow.splice(0, 1);
            const borderrow = worksheet.addRow(eachRow);
            borderrow.eachCell((cell) => {
                cell.border = { top: { style: 'thin' }, left: { style: 'thin' }, bottom: { style: 'thin' }, right: { style: 'thin' } };
                cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
            });
        });

        const dataLength = data.length;
        if (dataLength > 0) {
            for (i = 0; i < dataLength; i++) {
                worksheet.getCell('B' + (14 + i)).alignment = { horizontal: 'left', vertical: 'middle', wrapText: true };
            }
        }

        // footer data row
        if (footerData != null) {
            footerData.forEach((element: any) => {
                const eachRow = [];
                element.forEach((val: any) => {
                    eachRow.push(val);
                });
                const footerRow = worksheet.addRow(eachRow);
                const cellMerge = 'A' + footerRow.number + ':B' + footerRow.number;
                worksheet.mergeCells(cellMerge);
                footerRow.eachCell((cell) => {
                    cell.font = { size: 13, bold: true, name: 'Times New Roman' };
                    cell.border = { top: { style: 'thin' }, left: { style: 'thin' }, bottom: { style: 'thin' }, right: { style: 'thin' } };
                    cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
                });
            });
        }

        // Save Excel File
        workbook.xlsx.writeBuffer().then((dataBuffer: ArrayBuffer) => {
            const blob = new Blob([dataBuffer], { type: EXCEL_TYPE });
            fs.saveAs(blob, excelFileName + EXCEL_EXTENSION);
        });
    }

    public exportToExcelStatisticGeneralGroupByAgency(
        reportHeading: string,
        reportSubHeading: string,
        nameReport: string,
        subNameReport: string,
        json: any[],
        footerData: any[],
        excelFileName: string,
        sheetName: string
    ) {
        const data = json;
        // create workbook and worksheet
        const workbook = new Workbook();
        workbook.creator = 'Snippet Coder';
        workbook.lastModifiedBy = 'SnippetCoder';
        workbook.created = new Date();
        workbook.modified = new Date();
        const worksheet = workbook.addWorksheet(sheetName);

        worksheet.getColumn('A').font = { name: 'Times New Roman', size: 12 };
        worksheet.getColumn('B').font = { name: 'Times New Roman', size: 12 };
        worksheet.getColumn('C').font = { name: 'Times New Roman', size: 12 };
        worksheet.getColumn('D').font = { name: 'Times New Roman', size: 12 };
        worksheet.getColumn('E').font = { name: 'Times New Roman', size: 12 };
        worksheet.getColumn('F').font = { name: 'Times New Roman', size: 12 };
        worksheet.getColumn('G').font = { name: 'Times New Roman', size: 12 };
        worksheet.getColumn('H').font = { name: 'Times New Roman', size: 12 };
        worksheet.getColumn('I').font = { name: 'Times New Roman', size: 12 };
        worksheet.getColumn('J').font = { name: 'Times New Roman', size: 12 };
        worksheet.getColumn('K').font = { name: 'Times New Roman', size: 12 };
        worksheet.getColumn('L').font = { name: 'Times New Roman', size: 12 };
        worksheet.getColumn('M').font = { name: 'Times New Roman', size: 12 };

        // Add header row
        worksheet.addRow([]);
        worksheet.mergeCells('A1:B1');
        worksheet.getCell('A1').value = nameReport;
        worksheet.getCell('A1').alignment = { horizontal: 'center', vertical: 'middle' };

        worksheet.mergeCells('A2:B2');
        worksheet.getCell('A2').value = subNameReport;
        worksheet.getCell('A2').alignment = { horizontal: 'center', vertical: 'middle' };

        worksheet.mergeCells('H1:M1');
        if (this.language === 228) {
            worksheet.getCell('H1').value = 'Đơn vị báo cáo: ';
        } else {
            worksheet.getCell('H1').value = 'Reporting unit: ';
        }
        worksheet.getCell('H1').alignment = { horizontal: 'left', vertical: 'middle' };
        worksheet.getCell('H1').font = { size: 12, bold: true, name: 'Times New Roman' };

        worksheet.mergeCells('H2:M4');
        if (this.language === 228) {
            worksheet.getCell('H2').value =
                '+UBND cấp xã, cơ quan chuyên môn thuộc UBND cấp huyện.\n+Cơ quan chuyên môn thuộc UBND cấp tỉnh.\n+Cơ quan, đơn vị trực thuộc bộ, cơ quan ngang bộ.\n+Cơ quan trung ương tổ chức theo ngành dọc cấp xã.';
        } else {
            worksheet.getCell('H2').value = '+Units under ministries, ministerial-level agencies;\n+Specialized Division of People\'s Committee of district / town / city ...;\n+Commune People\'s Committee';
        }
        worksheet.getRow(3).height = 33;
        worksheet.getRow(4).height = 33;

        worksheet.mergeCells('H5:M5');
        if (this.language === 228) {
            worksheet.getCell('H5').value = 'Đơn vị nhận báo cáo: ';
        } else {
            worksheet.getCell('H5').value = 'Unit receiving report: ';
        }
        worksheet.getCell('H5').alignment = { horizontal: 'left', vertical: 'middle' };
        worksheet.getCell('H5').font = { size: 12, bold: true, name: 'Times New Roman' };

        worksheet.mergeCells('H6:M7');
        if (this.language === 228) {
            worksheet.getCell('H6').value = '+UBND cấp huyện.\n+UBND cấp tỉnh.\n+Bộ, cơ quan ngang bộ.\n+Cơ quan trung ương tổ chức theo ngành dọc cấp huyện.';
        } else {
            worksheet.getCell('H6').value = '+Ministries, Ministerial-level unit \n(Office of ministries, ministerial-level agencies);\n+District People\'s Committee, Provincial People\'s Committee Office\n(Division of Administrative Procedure)';
        }
        worksheet.getCell('H6').alignment = { horizontal: 'left', vertical: 'middle' };
        worksheet.getRow(7).height = 70;

        worksheet.mergeCells('K9:M9');
        if (this.language === 228) {
            worksheet.getCell('K9').value = 'Đơn vị tính: Số hồ sơ TTHC.';
        } else {
            worksheet.getCell('K9').value = 'Unit: Number of dossiers.';
        }
        worksheet.mergeCells('C1:G4');
        worksheet.getCell('C1').value = reportHeading;
        worksheet.getCell('C1').alignment = { horizontal: 'center', vertical: 'middle' };
        worksheet.getCell('C1').font = { size: 15, bold: true, name: 'Times New Roman' };

        worksheet.mergeCells('C5:G5');
        if (this.language === 228) {
            worksheet.getCell('C5').value = 'Kỳ báo cáo: Quý.../Năm...';
        } else {
            worksheet.getCell('C5').value = 'Reporting period: Quarter.../year...';
        }
        worksheet.getCell('C5').alignment = { horizontal: 'center', vertical: 'middle' };
        worksheet.getCell('C5').font = { size: 12, name: 'Times New Roman' };

        worksheet.mergeCells('C6:G6');
        worksheet.getCell('C6').value = reportSubHeading;
        worksheet.getCell('C6').alignment = { horizontal: 'center', vertical: 'middle' };
        worksheet.getCell('C6').font = { size: 12, italic: true, name: 'Times New Roman' };

        // NỘI DUNG TABLE-HEADER
        worksheet.mergeCells('A10:A12');
        worksheet.mergeCells('B10:B12');
        worksheet.mergeCells('C10:F10');
        worksheet.mergeCells('G10:J10');
        worksheet.mergeCells('K10:M10');
        worksheet.mergeCells('N10:N12');
        worksheet.mergeCells('O10:O12');
        worksheet.mergeCells('P10:P12');
        worksheet.mergeCells('Q10:Q12');
        worksheet.mergeCells('C11:C12');
        worksheet.mergeCells('D11:E11');
        worksheet.mergeCells('F11:F12');
        worksheet.mergeCells('G11:G12');
        worksheet.mergeCells('H11:H12');
        worksheet.mergeCells('I11:I12');
        worksheet.mergeCells('J11:J12');
        worksheet.mergeCells('K11:K12');
        worksheet.mergeCells('L11:L12');
        worksheet.mergeCells('M11:M12');

        worksheet.getCell('A13').value = '(1)';
        worksheet.getCell('B13').value = '(2)';
        worksheet.getCell('C13').value = '(3)=(4)+(5)+(6)';
        worksheet.getCell('D13').value = '(4)';
        worksheet.getCell('E13').value = '(5)';
        worksheet.getCell('F13').value = '(6)';
        worksheet.getCell('G13').value = '(7)=(8)+(9)+(10)';
        worksheet.getCell('H13').value = '(8)';
        worksheet.getCell('I13').value = '(9)';
        worksheet.getCell('J13').value = '(10)';
        worksheet.getCell('K13').value = '(11)=(12)+(13)';
        worksheet.getCell('L13').value = '(12)';
        worksheet.getCell('M13').value = '(13)';
        worksheet.getCell('N13').value = '(14)';
        worksheet.getCell('O13').value = '(15)';
        worksheet.getCell('P13').value = '(16)';
        worksheet.getCell('Q13').value = '(17)';

        if (this.language === 228) {
            worksheet.getCell('A10').value = 'STT';
            worksheet.getCell('B10').value = 'Đơn vị';
            worksheet.getCell('C10').value = 'Số lượng hồ sơ tiếp nhận';
            worksheet.getCell('G10').value = 'Số lượng hồ sơ đã giải quyết';
            worksheet.getCell('K10').value = 'Số lượng hồ sơ đang giải quyết';
            worksheet.getCell('N10').value = 'Tổng số hồ sơ thanh toán trực tuyến';
            worksheet.getCell('O10').value = 'Tỷ lệ trước và đúng hạn';
            worksheet.getCell('P10').value = 'Tỷ lệ hồ sơ trực tuyến';
            worksheet.getCell('Q10').value = 'Tỷ lệ thanh toán trực tuyến';
            worksheet.getCell('C11').value = 'Tổng số';
            worksheet.getCell('D11').value = 'Trong kỳ';
            worksheet.getCell('D12').value = 'Trực tuyến';
            worksheet.getCell('E12').value = 'Trực tiếp, dịch vụ bưu chính';
            worksheet.getCell('F11').value = 'Từ kỳ trước';
            worksheet.getCell('G11').value = 'Tổng số';
            worksheet.getCell('H11').value = 'Trước hạn';
            worksheet.getCell('I11').value = 'Đúng hạn';
            worksheet.getCell('J11').value = 'Quá hạn';
            worksheet.getCell('K11').value = 'Tổng số';
            worksheet.getCell('L11').value = 'Trong hạn';
            worksheet.getCell('M11').value = 'Quá hạn';

        } else {
            worksheet.getCell('A10').value = 'No.';
            worksheet.getCell('B10').value = 'Name of sector';
            worksheet.getCell('C10').value = 'Number of dossiers received';
            worksheet.getCell('G10').value = 'Number of dossiers resolved';
            worksheet.getCell('K10').value = 'Number of dossiers being resolved';

            worksheet.getCell('N10').value = 'Number of dossiers payment online';
            worksheet.getCell('O10').value = 'Percentage early and on time';
            worksheet.getCell('P10').value = 'Percentage dossier online';
            worksheet.getCell('Q10').value = 'Percentage dossier payment online';
            worksheet.getCell('C11').value = 'Total';
            worksheet.getCell('D11').value = 'Current period';
            worksheet.getCell('D12').value = 'Online';
            worksheet.getCell('E12').value = 'Direct, Postal Service';
            worksheet.getCell('F11').value = 'Previous period';
            worksheet.getCell('G11').value = 'Total';
            worksheet.getCell('H11').value = 'Early';
            worksheet.getCell('I11').value = 'On time';
            worksheet.getCell('J11').value = 'Out of date';
            worksheet.getCell('K11').value = 'Total';
            worksheet.getCell('L11').value = 'On time';
            worksheet.getCell('M11').value = 'Out of date';
        }

        worksheet.getColumn('B').width = 45;
        worksheet.getColumn('C').width = 17;
        worksheet.getColumn('E').width = 10;
        worksheet.getColumn('G').width = 17;
        worksheet.getColumn('K').width = 17;

        worksheet.getRow(2).height = 27;
        worksheet.getColumn('C').alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
        worksheet.getColumn('D').alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
        worksheet.getColumn('E').alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
        worksheet.getColumn('F').alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
        worksheet.getColumn('G').alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
        worksheet.getColumn('H').alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
        worksheet.getColumn('I').alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
        worksheet.getColumn('J').alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
        worksheet.getColumn('K').alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
        worksheet.getColumn('L').alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
        worksheet.getColumn('M').alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
        worksheet.getColumn('N').alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
        worksheet.getColumn('O').alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
        worksheet.getColumn('P').alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
        worksheet.getColumn('Q').alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };

        worksheet.getCell('H1').style.alignment = { horizontal: 'left', vertical: 'middle', wrapText: true };
        worksheet.getCell('H2').style.alignment = { horizontal: 'left', vertical: 'middle', wrapText: true };
        worksheet.getCell('H5').style.alignment = { horizontal: 'left', vertical: 'middle', wrapText: true };
        worksheet.getCell('H6').style.alignment = { horizontal: 'left', vertical: 'middle', wrapText: true };

        worksheet.properties.outlineLevelCol = 2;
        worksheet.properties.defaultRowHeight = 15;

        let i = 10;
        const j = 13;
        for (i; i <= j; i++) {
            let k = 1;
            const l = 17;
            for (k; k <= l; k++) {
                worksheet.findCell(i, k).alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
                worksheet.findCell(i, k).fill = {
                    type: 'pattern',
                    pattern: 'solid',
                    fgColor: { argb: 'C0C0C0C0' },
                    bgColor: { argb: 'FF0000FF' }
                };
                worksheet.findCell(i, k).border = { top: { style: 'thin' }, left: { style: 'thin' }, bottom: { style: 'thin' }, right: { style: 'thin' } };
                worksheet.findCell(i, k).font = { size: 12, bold: true, name: 'Times New Roman' };
            }
        }

        // get all columns from JSON
        let columnsArray: any[];
        for (const key in json) {
            if (json.hasOwnProperty(key)) {
                columnsArray = Object.keys(json[key]);
            }
        }
        columnsArray = columnsArray.filter(column => !['sectorId', 'agencyId'].includes(column));

        // Add Data and Conditional Formatting
        data.forEach((element: any) => {
            console.log('element', element)
            const eachRow = [];
            columnsArray.forEach((column) => {

                eachRow.push(element[column]);
            });
            // eachRow.splice(0, 1);
            const borderrow = worksheet.addRow(eachRow);
            borderrow.eachCell((cell) => {
                if (element?.isGroupBy != true) {
                    cell.border = { top: { style: 'thin' }, left: { style: 'thin' }, bottom: { style: 'thin' }, right: { style: 'thin' } };
                } else {
                    cell.font = { size: 12, bold: true, name: 'Times New Roman' };
                }
                cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
            });
        });

        const dataLength = data.length;
        if (dataLength > 0) {
            for (i = 0; i < dataLength; i++) {
                worksheet.getCell('B' + (14 + i)).alignment = { horizontal: 'left', vertical: 'middle', wrapText: true };
            }
        }

        // footer data row
        if (footerData != null) {
            footerData.forEach((element: any) => {
                const eachRow = [];
                element.forEach((val: any) => {
                    eachRow.push(val);
                });
                const footerRow = worksheet.addRow(eachRow);
                const cellMerge = 'A' + footerRow.number + ':B' + footerRow.number;
                worksheet.mergeCells(cellMerge);
                footerRow.eachCell((cell) => {
                    cell.font = { size: 13, bold: true, name: 'Times New Roman' };
                    cell.border = { top: { style: 'thin' }, left: { style: 'thin' }, bottom: { style: 'thin' }, right: { style: 'thin' } };
                    cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
                });
            });
        }

        // Save Excel File
        workbook.xlsx.writeBuffer().then((dataBuffer: ArrayBuffer) => {
            const blob = new Blob([dataBuffer], { type: EXCEL_TYPE });
            fs.saveAs(blob, excelFileName + EXCEL_EXTENSION);
        });
    }
}