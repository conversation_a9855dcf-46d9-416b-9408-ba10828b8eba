import { Component, OnInit, Inject, ViewChild } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { KeycloakService } from 'keycloak-angular';
import { DossierService } from 'src/app/data/service/dossier/dossier.service';
import { UserService } from 'src/app/data/service/user.service';
import * as ClassicEditor from '@ckeditor/ckeditor5-build-classic/build/ckeditor';
import '@ckeditor/ckeditor5-build-classic/build/translations/vi';
import { EnvService } from 'src/app/core/service/env.service';
import { SnackbarService } from 'src/app/data/service/snackbar/snackbar.service';
import { DatePipe } from '@angular/common';
import { ProcedureService } from 'src/app/data/service/procedure/procedure.service';
import { FormControl, FormGroup } from '@angular/forms';
import { NgxMatDateAdapter, NGX_MAT_DATE_FORMATS } from '@angular-material-components/datetime-picker';
import { PICK_FORMATS } from 'src/app/data/service/config.service';
import { CustomDatetimeAdapter } from 'src/app/shared/ts/custom-datetime-adapter';
import { DeploymentService } from 'src/app/data/service/deployment.service';
import * as tUtils from 'src/app/data/service/thoai.service';
import { NotificationService } from 'src/app/data/service/notification.service';
import { ProcessService } from 'src/app/data/service/process/process.service';
import { HomeService } from 'src/app/data/service/home/<USER>';
import { take } from 'rxjs/operators';
import {NotifyQNIService} from "shared/components/check-send-notify-qni/check-send-notify-qni.service";
import { PadmanService } from 'src/app/data/service/svc-padman/padman.service';
import { async } from 'rxjs/internal/scheduler/async';
import { AgencyService } from 'src/app/data/service/basedata/agency.service';
import {AdapterService} from 'data/service/adapter/adapter.service';

@Component({
  selector: 'app-extend-time',
  templateUrl: './extend-time.component.html',
  styleUrls: ['./extend-time.component.scss'],
  providers: [
    {
      provide: NgxMatDateAdapter,
      useClass: CustomDatetimeAdapter
    },
    {
      provide: NGX_MAT_DATE_FORMATS,
      useValue: PICK_FORMATS
    }
  ]
})
export class ExtendTimeComponent implements OnInit {

  config = this.envService.getConfig();
  env = this.deploymentService.getAppDeployment()?.env;
  selectedLang = localStorage.getItem('language') || 'vi';
  selectedLangId = Number(localStorage.getItem('languageId')) || 228;
  dossierId: string;
  commentContent = '';
  fullname: string;
  userName: string;
  userId: string;
  accountId: string;
  dossierCode: string;
  isCKMaxlenght = false;
  dossierDetail: any;
  processDetail: any;
  public Editor = ClassicEditor;
  commentConfig = {
    placeholder: 'Nhập nội dung...',
    language: 'vi',
    removePlugins: ['MediaEmbed', 'ImageUpload'],
    toolbarLocation: 'bottom'
  };
  nowDate = tUtils.newDate();
  startDate = this.nowDate;
  updateForm = new FormGroup({
    startDate: new FormControl(this.nowDate),
    approvalAgencyId: new FormControl(''),
  });
  public dates: Date;
  public showSpinners = true;
  public showSeconds = true;
  public touchUi = true;
  public enableMeridian = false;
  public stepHour = 1;
  public stepMinute = 1;
  public stepSecond = 1;
  public defaultTime = ['00', '00', '00'];
  smsEmailContent = '';
  smsEmailMaxLength = null;
  willSend = false;
  willSendEmail = false;
  willSendSMS = false;
  listEmail = [];
  listPhone = [];
  textPhone = '';
  textEmail = '';
  appointmentDateOld: string;
  appointmentDateNew: string;
  checkExtendTime = false;
  // ================================================= Upload file
  acceptFileExtension = this.config.acceptFileExtension;
  acceptFileType = this.config.acceptFileType;
  maxFileSize = this.env?.maxFileSize || this.config.maxFileSize;
  uploaded: boolean;
  blankVal: any;
  uploadedImage = [];
  files = [];
  urls = [];
  fileNames = [];
  fileNamesFull = [];
  listProcedureProcess = [];
  requireAttachmentWhenPause = false;
  @ViewChild('startDatePicker') startDatePicker: any;
  fileTemplate = ""
  officers = [];
  agencyId: any;
  getApprovalAgency = '';
  approvalAgency = [];

  currentTask: any;
  notifyQNI = this.deploymentService.getAppDeployment()?.notifyqni?.enable;

  totalCost = '';

  ckeditorMaxLengthNotification = "";
  ckeditorMaxLength = this.deploymentService.env.OS_HCM.ckeditorMaxLength;
  visibleApprovalAgency = this.deploymentService.env.visibleApprovalAgency;
  isBoTNMTQNM = this.env?.OS_QNM?.boTNMT === true ? this.env?.OS_QNM?.boTNMT : false;
  isBoTNMTHCM = this.deploymentService.env.OS_HCM.maTTBoTNMT;
  isBoTNMTGLI = this.deploymentService.env.OS_GLI.boTNMT;

  // IGATESUPP-44607
  allowNextStepWaitingForApproval = this.deploymentService.env.OS_HCM.allowNextStepWaitingForApproval ? this.deploymentService.env.OS_HCM.allowNextStepWaitingForApproval : false;
  enableApproveExtensionCheckBox = false;
  dossierTaskStatusWaitingForApproval = { id: '', name: [] };
  dossierMenuTaskRemindWaitingForApproval = { id: '', name: [] };

  setOnlyApprovalAgency = this.deploymentService.env?.OS_QNI?.setOnlyApprovalAgency;
  isSmsQNM = this.env?.OS_QNM?.isSmsQNM ? true : false;
  extendTimeIsAppointmentDate = this.env?.OS_QTI?.extendTimeIsAppointmentDate === true ? this.env?.OS_QTI?.extendTimeIsAppointmentDate : false;
  disableButton = false;
  constructor(
    private userService: UserService,
    private envService: EnvService,
    private notiService: NotificationService,
    private deploymentService: DeploymentService,
    private keycloakService: KeycloakService,
    public dialogRef: MatDialogRef<ExtendTimeComponent>,
    @Inject(MAT_DIALOG_DATA) public data: ExtendTimeModel,
    private snackbarService: SnackbarService,
    private dossierService: DossierService,
    private datePipe: DatePipe,
    private procedureService: ProcedureService,
    private processService: ProcessService,
    private homeService : HomeService,
    private notifyQNIService: NotifyQNIService,
    private padmanService: PadmanService,
    private agencyService: AgencyService,
    private adapterService: AdapterService,
  ) {
    this.dossierId = data.dossierId;
    this.dossierCode = data.dossierCode;
  }

  ngOnInit(): void {
    this.getUserAccount();
    this.getDossierDetail();
    this.getConfigCloud();
    this.setEnvVariable();
    if (this.selectedLang === 'vi') {
      this.ckeditorMaxLengthNotification = "Nội dung không quá 500 ký tự!";
      if (this.ckeditorMaxLength > 0)
        this.ckeditorMaxLengthNotification = "Nội dung không quá "+ this.ckeditorMaxLength + " ký tự!";
    } else if (this.selectedLang === 'en') {
      this.ckeditorMaxLengthNotification = "Content must not exceed 500 characters!";
      if (this.ckeditorMaxLength > 0)
        this.ckeditorMaxLengthNotification = "Content must not exceed "+ this.ckeditorMaxLength + " characters!";
    }
  }

  setEnvVariable(){
    this.fileTemplate = !!this.env?.fileTemplate?.extendTime ? this.env?.fileTemplate?.extendTime : "";
  }

// IGATESUPP-51500 - Đánh giá lại chức năng phê duyệt hồ sơ - 19.06.2023
postOnlyApprovalAgencyOfDossier(code) {

  const formObj = this.updateForm.getRawValue();
  if (!!formObj.approvalAgencyId && formObj.approvalAgencyId != '') {
    this.getApprovalAgency = formObj.approvalAgencyId;
  }else{
    return;
  }

  const body = {
    id: this.getApprovalAgency
  };
  const requestBody = JSON.stringify(body, null, 2);
  this.dossierService.postOnlyApprovalAgencyOfDossier(requestBody, code).toPromise();
}

  getUserAccount() {
    this.keycloakService.loadUserProfile().then(user => {
      // tslint:disable-next-line: no-string-literal
      this.userId = user['attributes'].user_id[0];
      this.accountId = user['attributes'].account_id[0];
      this.userName =  user.username;
      this.userService.getUserInfo(this.userId).subscribe(data => {
        this.fullname = data.fullname;
      });
    });
  }
  getFullUserInfo(userId){
    this.userService.getFullUserInfo(userId).subscribe(data => {
    });
  }
  async getDossierDetail() {
    await this.getDossierFeeNotify();
    this.dossierService.getDossierDetail(this.dossierId).subscribe(data => {
      this.dossierDetail = data;
      this.agencyId = data?.agency?.id;
      if (!!data.currentTask && data.currentTask.length > 0){
        this.currentTask = data.currentTask[0];
        this.getApprovaledAgency(this.currentTask.candidateGroup[0]);
      }
      if (data.appointmentDate !== undefined && data.appointmentDate !== null){
        this.appointmentDateOld = this.datePipe.transform(data.appointmentDate, 'dd/MM/yyyy HH:mm:ss');
		 if(!!this.extendTimeIsAppointmentDate){
          this.updateForm.patchValue({
            startDate: new Date(data.appointmentDate)
          });
        }
      }else{
        if (data.acceptedDate !== undefined) {
          const dossierEndDate = new Date(data.acceptedDate);
          // tslint:disable-next-line: max-line-length
          if (data.processingTime !== undefined || data.processingTime != null) {
            dossierEndDate.setDate(dossierEndDate.getDate() + data.processingTime);
            this.appointmentDateOld = this.datePipe.transform(new Date(dossierEndDate), 'dd/MM/yyyy HH:mm:ss');
          }
        }
      }
      // info applicant
      //this.getFullUserInfo(data.applicant.userId);
      // tslint:disable-next-line:max-line-length
      if ( data.applicant.data.phoneNumber !== null && data.applicant.data.phoneNumber !== '' && data.applicant.data.phoneNumber !== undefined) {
        this.textPhone = data.applicant.data.phoneNumber;
        this.listPhone.push({
          number: data.applicant.data.phoneNumber,
          nextFlow: ''
        });
      }
      if ( data.applicant.data.email !== null && data.applicant.data.email !== '' && data.applicant.data.email !== undefined) {
        this.textEmail = data.applicant.data.email;
        this.listEmail.push({
          email: data.applicant.data.email,
          nextFlow: ''
        });
      }
      setTimeout(() => {
        if(!!this.notifyQNI){
          this.notifyQNIService.checkSendSubject.next(
            {
              id: data?.procedureProcessDefinition?.id,
              phone: data?.applicant?.data?.phoneNumber,
              email: data?.applicant?.data?.email,
              currentTask: this.currentTask,
              contentParams: {
                parameters: {
                  sector: !!data?.procedure?.sector?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name ? data?.procedure?.sector?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name : '',
                  procedure: !!data?.procedure?.translate?.find(agency => agency.languageId === Number(this.selectedLangId))?.name ? data?.procedure?.translate?.find(agency => agency.languageId === Number(this.selectedLangId))?.name : '',
                  agency: !!data?.agency?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name ? data?.agency?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name : '',
                  agencyId: data?.agency?.id,
                  applicantFullname: data?.applicant?.data?.fullname,
                  officerFullname: '',
                  subsystem: !!this.env?.subsystem?.name ? this.env?.subsystem?.name : "Hệ thống Một cửa điện tử",
                  dossierCode: data?.code,
                  nextTask: !!this.env?.notify?.extendProcessingDossier?.nextTask ? this.env?.notify?.extendProcessingDossier?.nextTask : 'Gia hạn hồ sơ',
                  dossierSearchPadsvcUrl: (!!this.env?.domain?.padsvc ? this.env?.domain?.padsvc : "http://padsvctest.vncitizens.vn/vi/") + "dossier/public",
                  dossierDetailPadsvcUrl: (!!this.env?.domain?.padsvc ? this.env?.domain?.padsvc : "http://padsvctest.vncitizens.vn/vi/") + "dossier/detail/" + this.dossierId,
                  dossierSearchUrl: (!!this.env?.domain?.onegate ? this.env?.domain?.onegate : "http://padsvctest.vncitizens.vn/vi/") + "dossier/search/",
                  dossierDetailUrl: (!!this.env?.domain?.onegate ? this.env?.domain?.onegate : "http://padsvctest.vncitizens.vn/vi/") + "dossier/search/" + this.dossierId,
                  returnMethod: !!data?.dossierReceivingKind?.name?.find(recieve => recieve.languageId === Number(this.selectedLangId))?.name ? data?.dossierReceivingKind?.name?.find(recieve => recieve.languageId === Number(this.selectedLangId))?.name : '',
                  receivingDate: '',
                  appointmentDate: '',
                  dossierFee: this.totalCost,
                  reason: ''
                }
              }
            }
          );
        } else if(this.isSmsQNM) {
          const agencies = this.agencyService.getAgencies();
          const extend = {
            dossier:{
              id: this.dossierId,
              code: this.dossierCode,
            },
            agencies: agencies
          };
          this.notiService.checkSendSubject.next(
            {
              id: data?.procedureProcessDefinition?.id,
              phone: data?.applicant?.data?.phoneNumber,
              email: data?.applicant?.data?.email,
              currentTask: this.currentTask,
              contentParams: {
                parameters: {
                  sector: !!data?.procedure?.sector?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name ? data?.procedure?.sector?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name : '',
                  procedure: !!data?.procedure?.translate?.find(agency => agency.languageId === Number(this.selectedLangId))?.name ? data?.procedure?.translate?.find(agency => agency.languageId === Number(this.selectedLangId))?.name : '',
                  agency: !!data?.agency?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name ? data?.agency?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name : '',
                  agencyId: data?.agency?.id,
                  applicantFullname: data?.applicant?.data?.fullname,
                  officerFullname: '',
                  subsystem: !!this.env?.subsystem?.name ? this.env?.subsystem?.name : "Hệ thống Một cửa điện tử",
                  dossierCode: data?.nationCode ? data?.nationCode : data?.code,
                  nextTask: !!this.env?.notify?.extendProcessingDossier?.nextTask ? this.env?.notify?.extendProcessingDossier?.nextTask : 'Gia hạn hồ sơ',
                  dossierSearchPadsvcUrl: (!!this.env?.domain?.padsvc ? this.env?.domain?.padsvc : "http://padsvctest.vncitizens.vn/vi/") + "dossier/public",
                  dossierDetailPadsvcUrl: (!!this.env?.domain?.padsvc ? this.env?.domain?.padsvc : "http://padsvctest.vncitizens.vn/vi/") + "dossier/detail/" + this.dossierId,
                  dossierSearchUrl: (!!this.env?.domain?.onegate ? this.env?.domain?.onegate : "http://padsvctest.vncitizens.vn/vi/") + "dossier/search/",
                  dossierDetailUrl: (!!this.env?.domain?.onegate ? this.env?.domain?.onegate : "http://padsvctest.vncitizens.vn/vi/") + "dossier/search/" + this.dossierId,
                  returnMethod: !!data?.dossierReceivingKind?.name?.find(recieve => recieve.languageId === Number(this.selectedLangId))?.name ? data?.dossierReceivingKind?.name?.find(recieve => recieve.languageId === Number(this.selectedLangId))?.name : '',
                  receivingDate: '',
                  appointmentDate: '',
                  dossierFee: this.totalCost,
                  reason: '',
                  extend: extend
                }
              }
            }
          );
        } else {
          this.notiService.checkSendSubject.next(
            {
              id: data?.procedureProcessDefinition?.id,
              phone: data?.applicant?.data?.phoneNumber,
              email: data?.applicant?.data?.email,
              currentTask: this.currentTask,
              contentParams: {
                parameters: {
                  sector: !!data?.procedure?.sector?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name ? data?.procedure?.sector?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name : '',
                  procedure: !!data?.procedure?.translate?.find(agency => agency.languageId === Number(this.selectedLangId))?.name ? data?.procedure?.translate?.find(agency => agency.languageId === Number(this.selectedLangId))?.name : '',
                  agency: !!data?.agency?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name ? data?.agency?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name : '',
                  agencyId: data?.agency?.id,
                  applicantFullname: data?.applicant?.data?.fullname,
                  officerFullname: '',
                  subsystem: !!this.env?.subsystem?.name ? this.env?.subsystem?.name : "Hệ thống Một cửa điện tử",
                  dossierCode: data?.nationCode ? data?.nationCode : data?.code,
                  nextTask: !!this.env?.notify?.extendProcessingDossier?.nextTask ? this.env?.notify?.extendProcessingDossier?.nextTask : 'Gia hạn hồ sơ',
                  // IGATESUPP-63184
                  nextStatus: !!this.env?.notify?.extendProcessingDossier?.nextStatus ? this.env?.notify?.extendProcessingDossier?.nextStatus : (data?.dossierTaskStatus?.name ?? ''), // cho bo sung, trang thai hien tai cua ho so
                  dossierStatus: !!this.env?.notify?.extendProcessingDossier?.dossierStatus ? this.env?.notify?.extendProcessingDossier?.dossierStatus : (data?.dossierStatus?.name?.find(t => t.languageId === Number(this.selectedLangId))?.name ?? ''), // cho bo sung, trang thai hien tai cua ho so
                  dossierSearchPadsvcUrl: (!!this.env?.domain?.padsvc ? this.env?.domain?.padsvc : "http://padsvctest.vncitizens.vn/vi/") + "dossier/public",
                  dossierDetailPadsvcUrl: (!!this.env?.domain?.padsvc ? this.env?.domain?.padsvc : "http://padsvctest.vncitizens.vn/vi/") + "dossier/detail/" + this.dossierId,
                  dossierSearchUrl: (!!this.env?.domain?.onegate ? this.env?.domain?.onegate : "http://padsvctest.vncitizens.vn/vi/") + "dossier/search/",
                  dossierDetailUrl: (!!this.env?.domain?.onegate ? this.env?.domain?.onegate : "http://padsvctest.vncitizens.vn/vi/") + "dossier/search/" + this.dossierId,
                  returnMethod: !!data?.dossierReceivingKind?.name?.find(recieve => recieve.languageId === Number(this.selectedLangId))?.name ? data?.dossierReceivingKind?.name?.find(recieve => recieve.languageId === Number(this.selectedLangId))?.name : '',
                  receivingDate: '',
                  appointmentDate: '',
                  dossierFee: this.totalCost,
                  reason: ''
                }
              }
            }
          );
        }
      }, this.config.expiredTime/2);
      this.getProcedureDetail(data?.procedure?.id);
      this.getProcedureProcessDetail(data.procedureProcessDefinition.id);
    });
  }
  
  getDossierFeeNotify() {
    return new Promise<void>(resolve => {
      this.padmanService.getDossierFee(this.dossierId).toPromise().then(res => {
        if(res && res.length > 0)
        {
          let totalCostValue = 0;
          let totalPaidValue = 0;
          res.forEach(element => {
            totalCostValue += element.quantity * element.amount;
            totalPaidValue += element.paid;
          });
          const monetaryUnit = res[0]?.monetaryUnit ? res[0].monetaryUnit : 'VNĐ';
          let total = totalCostValue - totalPaidValue;
          this.totalCost = total > 0 ? Number((total).toFixed(1)).toLocaleString() + ' ' + monetaryUnit : '';
        }
        resolve();
      }).catch(err => {
        resolve();
      });
    });
  }

  getProcedureDetail(procedureId) {
    this.procedureService.getProcedureDetail(procedureId).subscribe(async data => {
      this.enableApproveExtensionCheckBox = data?.extendHCM?.enableApproveExtensionCheckBox ? data?.extendHCM?.enableApproveExtensionCheckBox : false;
    }, err => {
      console.log(err);
    });
  }
  getProcedureProcessDetail(processId) {
    this.procedureService.getProcedureProcessDetail(processId).subscribe(data => {
      this.processDetail = data;
      this.checkedExtendTime();
    });
  }
  checkedExtendTime(){
    if(this.processDetail.numberExtendTime === -1 || this.dossierDetail.countExtendTime < this.processDetail.numberExtendTime){
      this.checkExtendTime = true;
    }
  }

  // getApprovaledAgency(agency) {
  //   if (!!agency.parent) {
  //     this.processService.getApprovalAgency(agency.parent.id).subscribe(data => {
  //       if (!!data.content[0]) {
  //         // data.content[0].approvalAgency.forEach(item => {
  //         //   item.name.forEach(name => {
  //         //     if (name.languageId == this.selectedLangId)
  //         //       item.DisplayName = name.name;
  //         //   })
  //         // });
  //         // this.approvalAgency = data.content[0].approvalAgency;
  //         data.content[0].approvalAgency.name.forEach(name => {
  //           if (name.languageId === this.selectedLangId){
  //           data.content[0].approvalAgency.DisplayName = name.name;
  //           }
  //         });
  //         this.approvalAgency = [data.content[0].approvalAgency];
  //       }
  //     });
  //   }

  //   if (!!this.approvalAgency) {
  //     this.processService.getApprovalAgency(agency.id).subscribe(data => {
  //       if (!!data.content[0]) {
  //         // data.content[0].approvalAgency.forEach(item => {
  //         //   item.name.forEach(name => {
  //         //     if (name.languageId == this.selectedLangId)
  //         //       item.DisplayName = name.name;
  //         //   })
  //         // });
  //         // this.approvalAgency = data.content[0].approvalAgency;
  //         data.content[0].approvalAgency.name.forEach(name => {
  //           if (name.languageId === this.selectedLangId){
  //           data.content[0].approvalAgency.DisplayName = name.name;
  //           }
  //         });
  //         this.approvalAgency = [data.content[0].approvalAgency];
  //       }
  //     });
  //   }
  //   console.log('approvalAgency', this.approvalAgency);
  // }

  getApprovaledAgency(agency) {
    this.approvalAgency = [];
    let enableApprovaledAgencyId = this.deploymentService.env.OS_QNI.enableApprovaledAgencyId;
    if (enableApprovaledAgencyId){
      if (agency && agency.id) {
        this.processService.getApprovalAgency(agency.id).subscribe(data2 => {    
          if (data2 && data2.content && data2.content.length > 0) {
            const uniqueAgencies = new Set();
            data2.content.forEach(item => {
              if (item.approvalAgency && item.approvalAgency.name) {
                item.approvalAgency.name.forEach(name => {
                  if (name.languageId === this.selectedLangId) {
                    item.approvalAgency.DisplayName = name.name;
                  }
                });
                if (!uniqueAgencies.has(item.approvalAgency.id)) {
                  uniqueAgencies.add(item.approvalAgency.id);
                  this.approvalAgency.push(item.approvalAgency);
                }
              }
            });
  
            if (this.approvalAgency.length === 1) {
              this.updateForm.patchValue({
                approvalAgencyId: this.approvalAgency[0].id,
              });
            }
          }
        });
      }
    }else{
      if (!!agency.parent) {
        this.processService.getApprovalAgency(agency.parent.id).subscribe(data => {
          if (data.content.length > 0){
            this.approvalAgency = [];
            // tslint:disable-next-line:prefer-for-of
            for (let i = 0; i < data.content.length; i++){
              data.content[i].approvalAgency.name.forEach(name => {
                if (name.languageId === this.selectedLangId){
                data.content[i].approvalAgency.DisplayName = name.name;
                }
              });
              if (this.approvalAgency.filter(item => item.id === data.content[i].approvalAgency.id).length === 0){
                this.approvalAgency.push(data.content[i].approvalAgency);
              }
            }
          }
          if (!!this.approvalAgency) {
            this.processService.getApprovalAgency(agency.id).subscribe(data2 => {
              if (data2.content.length > 0){
                // tslint:disable-next-line:prefer-for-of
                for (let i = 0; i < data2.content.length; i++){
                  data2.content[i].approvalAgency.name.forEach(name => {
                    if (name.languageId === this.selectedLangId){
                    data2.content[i].approvalAgency.DisplayName = name.name;
                    }
                  });
                  if (this.approvalAgency.filter(item => item.id === data2.content[i].approvalAgency.id).length === 0){
                    this.approvalAgency.push(data2.content[i].approvalAgency);
                  }
                }
              }
              if (!!this.approvalAgency &&  this.approvalAgency.length === 1){
                this.updateForm.patchValue({
                  approvalAgencyId: this.approvalAgency[0].id,
                });
              }
            });
          }
        });
      }
      console.log('approvalAgency', this.approvalAgency);
    }
  }

  GetListUserByPermission(agency) {
    return new Promise<void>((resolve) => {
      this.processService.getApprovalAgency(agency.id).pipe(take(1)).subscribe(async data => {
        console.log('data', data);
        if (!!data && data.content.length !== 0) {
          this.getApprovalAgency = data.content[0].approvalAgency.id;
        }
        else{
          if (!!agency?.parent?.id){
            await this.GetListUserByPermissionParent(agency.parent.id);
          }
        }
        if (this.getApprovalAgency == '') {
          this.getApprovalAgency = agency.id;
        }
        const formObj = this.updateForm.getRawValue();
        if(!!formObj.approvalAgencyId && formObj.approvalAgencyId!=''){
          this.getApprovalAgency = formObj.approvalAgencyId;
        }
        let permission = "extendDossierApproval";
        this.processService.getListUserByPermissionCode(this.getApprovalAgency, permission).pipe(take(1)).subscribe(data2 => {
          this.officers = data2;
          resolve();
        }, (err) => {
          resolve();
        });
      });
    });
  }

  GetListUserByPermissionParent(agencyId) {
    return new Promise<void>((resolve) => {
      this.processService.getApprovalAgency(agencyId).pipe(take(1)).subscribe(data => {
        console.log('data', data);
        if (!!data && data.content.length !== 0) {
          this.getApprovalAgency = data.content[0].approvalAgency.id;
        }
        // if (this.getApprovalAgency == '') {
        //   this.getApprovalAgency = agencyId;
        // }
        resolve();
      }, (err) => {
        resolve();
      });
    });
  }

  // GetListUserByPermission(agencyId) {
  //   return new Promise<void>((resolve) => {
  //     this.processService.getApprovalAgency(agencyId).pipe(take(1)).subscribe(data => {
  //       console.log('data', data);
  //       if (!!data && data.content.length !== 0) {
  //         this.getApprovalAgency = data.content[0].approvalAgency.id;
  //       }
  //       if (this.getApprovalAgency == '') {
  //         this.getApprovalAgency = agencyId;
  //       }
  //       let permission = "extendDossierApproval";
  //       this.processService.getListUserByPermissionCode(this.getApprovalAgency, permission).pipe(take(1)).subscribe(data => {
  //         this.officers = data;
  //         resolve();
  //       }, (err) => {
  //         resolve();
  //       });
  //     });
  //   });
  // }

  async onConfirm() {
    this.disableButton = true;
    // await this.GetListUserByPermission(this.agencyId);
    if (!!this.currentTask && !!this.currentTask.candidateGroup[0] && !!this.currentTask.candidateGroup[0].id){
      await this.GetListUserByPermission(this.currentTask.candidateGroup[0]);
    } else {
      await this.GetListUserByPermission({id: this.agencyId});
    }
    if (this.env?.enableApprovalOfLeadership != 2 && this.officers.length == 0) {
      this.homeService.error('Cán bộ phê duyệt hồ sơ của đơn vị chưa được cấu hình. \r\n Vui lòng cấu hình cán bộ phê duyệt để tiếp tục!',
        'Approval officer has not yet been configured \r\n Please configure the approval officer to continue!');
      const msgObj = {
        vi: 'Cán bộ phê duyệt hồ sơ của đơn vị chưa được cấu hình',
        en: 'Approval officer has not yet been configured'
      };
      this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
      this.disableButton = false;
      return
    }

    const formObj = this.updateForm.getRawValue();
    if (this.updateForm.invalid) {
      const msgObj = {
        vi: 'Vui lòng nhập đúng định dạng ngày!',
        en: 'Please enter the correct date format!'
      };
      this.snackbarService.openSnackBar(2, '', msgObj[this.selectedLang], 'warning_notification', this.config.expiredTime);
      this.disableButton = false;
    }

    if (this.visibleApprovalAgency && (!this.getApprovalAgency || this.getApprovalAgency === '')) {
      const msgObj = {
        vi: 'Đơn vị phê duyệt hồ sơ chưa được chọn!',
        en: 'The unit that approves the application has not been selected!'
      };
      this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
      this.disableButton = false;
      return
    }
    // check date
    let checkedDate = false;
    if (tUtils.newDate() < new Date(formObj.startDate)) {
        checkedDate = true;
    }else{
      this.disableButton = false;
      const msgObj = {
        vi: 'Vui lòng nhập thời hẹn trả lớn hơn thời gian hiện tại!',
        en: 'Please enter a payment appointment that is larger than the current time!'
      };
      this.snackbarService.openSnackBar(2, '', msgObj[this.selectedLang], 'warning_notification', this.config.expiredTime);
    }
    if (this.commentContent.trim() === ''){
      this.disableButton = false;
      const msgObj = {
        vi: 'Vui lòng nhập lý do gia hạn!',
        en: 'Please enter a reason for renewal!'
      };
      this.snackbarService.openSnackBar(2, '', msgObj[this.selectedLang], 'warning_notification', this.config.expiredTime);
    }
    if (this.isCKMaxlenght){
      this.disableButton = false;
      let msgObj;
      if (this.ckeditorMaxLength > 0) {
        msgObj = {
          vi: 'Nội dung không quá ' + this.ckeditorMaxLength + ' ký tự!',
          en: 'Content must not exceed ' + this.ckeditorMaxLength + ' characters!'
        };
      } else {
        msgObj = {
          vi: 'Nội dung không quá 500 ký tự!',
          en: 'Content must not exceed 500 characters!'
        };
      }
      this.snackbarService.openSnackBar(2, '', msgObj[this.selectedLang], 'warning_notification', this.config.expiredTime);
    }
    let checkRequireAttachment = true;
    if (this.requireAttachmentWhenPause && this.files.length === 0){
      this.disableButton = false;
      checkRequireAttachment = false;
      const msgObj = {
        vi: 'Vui lòng đính kèm tệp tin!',
        en: 'Please attach file!'
      };
      this.snackbarService.openSnackBar(2, '', msgObj[this.selectedLang], 'warning_notification', this.config.expiredTime);
    }
    if (!this.isCKMaxlenght && this.commentContent.trim() !== '' && !this.updateForm.invalid && checkedDate && checkRequireAttachment) {
      if (this.files.length > 0) {
        this.keycloakService.loadUserProfile().then(user => {
          // tslint:disable-next-line: no-string-literal
          const userId = user['attributes'].user_id[0];
          this.uploadMultiFile(this.files, userId);
        });
      } else {
        this.putExtendTime();
      }
    }
  }
  async putExtendTime(){
    const formObj = this.updateForm.getRawValue();
    const requestBodyObj = {
      // 3: gia hạn
      // 10: chờ duyệt gia hạn
      dossierStatus: this.env?.enableApprovalOfLeadership == 1 ? 10 : 3,
      dossierTaskStatus: null,
      dossierMenuTaskRemind: null,
      comment: '',
      extendTime: '',
      user: {
        id: this.userId,
        fullname: this.fullname,
        account: {
          id: this.accountId,
          username: [
            {
              value: this.userName
            }
          ]
        }
      },
      attachment: this.uploadedImage
    };
    if (this.commentContent.trim() !== '') {
      this.postComment(this.commentContent.trim());
      requestBodyObj.comment = this.commentContent.trim();
    } else {
      const msgObj = {
        vi: 'Gia hạn hồ sơ <b>' + this.dossierCode + '</b>',
        en: 'Dossier <b>' + this.dossierCode + '</b> has been extend time!'
      };
      this.postComment(msgObj[this.selectedLang]);
      requestBodyObj.comment = msgObj[this.selectedLang];
    }
    requestBodyObj.extendTime = this.datePipe.transform(formObj.startDate, 'yyyy-MM-dd\'T\'HH:mm:ss.SSSZ');
    const msgObjTemp = {
      vi: 'Ngày hẹn trả',
      en: 'Appointment date'
    };
    this.postHistory(3, msgObjTemp[this.selectedLang], this.appointmentDateOld, this.datePipe.transform(formObj.startDate, 'dd/MM/yyyy HH:mm:ss'));
    this.smsEmailContent = requestBodyObj.comment;

    
    if (this.allowNextStepWaitingForApproval && this.enableApproveExtensionCheckBox) {
      requestBodyObj.dossierStatus = 10;

      requestBodyObj.dossierTaskStatus = this.dossierTaskStatusWaitingForApproval;
      requestBodyObj.dossierMenuTaskRemind = this.dossierMenuTaskRemindWaitingForApproval;

      await this.setdossierTaskStatus();
      await this.setdossierTaskStatusRemind();

      requestBodyObj.dossierTaskStatus.id = this.dossierTaskStatusWaitingForApproval.id;
      requestBodyObj.dossierTaskStatus.name = this.dossierTaskStatusWaitingForApproval.name;
      requestBodyObj.dossierMenuTaskRemind.id = this.dossierMenuTaskRemindWaitingForApproval.id;
      requestBodyObj.dossierMenuTaskRemind.name =this.dossierMenuTaskRemindWaitingForApproval.name;

    }

    const requestBody = JSON.stringify(requestBodyObj, null, 2);
    this.dossierService.putDossierExtendTimeWithComment(this.dossierId, requestBody).subscribe(data => {
      if (data.affectedRows === 1) {
        if(!!this.notifyQNI){
          this.notifyQNIService.confirmSendSubject.next({
            confirm: true,
            renewContent: false,
          });
        } else {
          this.notiService.confirmSendSubject.next({
            confirm: true,
            renewContent: false,
          });
        }

        if (this.env?.enableApprovalOfLeadership == 1 || (this.allowNextStepWaitingForApproval && this.enableApproveExtensionCheckBox)){
          const data = {
            type: 10,
            date: tUtils.newDate(),
            attachment: this.uploadedImage,
            extendTime: requestBodyObj.extendTime
          }
          this.dossierService.updateApprovalData(this.dossierId, data).subscribe(res => {});
        }
        // if (this.willSendEmail) {
        //   this.postEmail(this.listEmail, 1);
        // }
        // if (this.willSendSMS) {
        //   this.postSMS(this.listPhone);
        // }
        if(this.isBoTNMTQNM || this.isBoTNMTHCM || this.isBoTNMTGLI){
          this.dossierService.updateDossierBoTNMT(this.dossierId).subscribe(test => {});
        }
        if (this.deploymentService.env?.OS_HBH?.isTnmtTriNam) {
          this.adapterService.sendTNMTTriNam(this.dossierId);
        }
        // IGATESUPP-51500 - Đánh giá lại chức năng phê duyệt hồ sơ - 19.06.2023
        if(this.setOnlyApprovalAgency && this.approvalAgency.length > 1){
          this.postOnlyApprovalAgencyOfDossier(this.dossierCode);
        }     
        this.dialogRef.close(true);
      } else {
        this.dialogRef.close(false);
      }
    }, err => {
      this.dialogRef.close(false);
    });
  }

  onDismiss() {
    this.dialogRef.close();
  }

  postComment(commentContent) {
    const msgObj = {
      vi: 'Lý do gia hạn: ',
      en: 'Reason of extend time: '
    };
    const content = {
      groupId: 2,
      itemId: this.dossierId,
      user: {
        id: this.userId,
        fullname: this.fullname
      },
      file: this.uploadedImage,
      content: msgObj[this.selectedLang] + commentContent.trim()
    };
    const requestBody = JSON.stringify(content, null, 2);
    this.dossierService.postComment(requestBody).subscribe(data => { });
  }
  postHistory(type, col, oldVal, newVal) {
    const content = {
      groupId: 1,
      itemId: this.dossierId,
      user: {
        id: this.userId,
        name: this.fullname
      },
      type,
      deploymentId: this.config.deploymentId,
      action: [
        {
          fieldNameRbk: col,
          originalValue: oldVal,
          newValue: newVal
        }
      ]
    };
    const requestBody = JSON.stringify(content, null, 2);
    this.dossierService.postHistory(requestBody).subscribe(data => {
    });
  }

  onCommentEditorChange(event) {
    this.commentContent = event.editor.getData();
    if (this.ckeditorMaxLength > 0) {
      if (event.editor.getData().trim().length > this.ckeditorMaxLength) {
        this.isCKMaxlenght = true;
      } else {
        this.isCKMaxlenght = false;
      }
    } else if (event.editor.getData().trim().length > 500) {
      this.isCKMaxlenght = true;
    } else {
      this.isCKMaxlenght = false;
    }
  }
  onCheckboxSendChange(type) {
    switch (type) {
      case 1:
        this.willSendSMS = !this.willSendSMS;
        if (this.willSendSMS === true) {
          this.smsEmailMaxLength = this.config.dossierEmailSMSZaloConfig.characterLimit;
        } else {
          this.smsEmailMaxLength = null;
        }
        break;
      case 2:
        this.willSendEmail = !this.willSendEmail;
        break;
    }
    if (this.willSendSMS || this.willSendEmail) {
      this.willSend = true;
    } else {
      this.willSend = false;
    }
  }
  postEmail(listEmail, type) {
    if (listEmail.length > 0) {
      listEmail.forEach(mail => {
        const emailConfig = this.config.dossierEmailSMSZaloConfig;
        let subject = emailConfig.increaseDue[this.selectedLang];
        subject = subject.replace('{{code}}', this.dossierDetail.code);
        const contentTemp = emailConfig.reasonExtend[this.selectedLang] + ' ' + this.smsEmailContent.replace(/<(?:.|\n)*?>/gm, '');
        const userAgency = JSON.parse(localStorage.getItem('userAgency'));
        let rootAgencyId: any = '';
        if (userAgency !== null) {
          rootAgencyId = userAgency.id;
        } else {
          rootAgencyId = this.config.rootAgency.id;
        }
        this.dossierService.postEmailByAgency(
          rootAgencyId,
          this.config.subsystemId,
          contentTemp,
          [mail.email],
          subject
        ).subscribe(emailRS => {
          console.log(emailRS);
        }, err => {
          console.log(err);
        });
      });
    }
  }

  postSMS(listPhoneNumber) {
    const emailConfig = this.config.dossierEmailSMSZaloConfig;
    let subject = emailConfig.increaseDue[this.selectedLang];
    subject = subject.replace('{{code}}', this.dossierDetail.code);
    subject += '. ' + emailConfig.reasonExtend[this.selectedLang] + ' ' + this.smsEmailContent.replace(/<(?:.|\n)*?>/gm, '');
    console.log(subject)
    if (listPhoneNumber.length > 0) {
      listPhoneNumber.forEach(phone => {
        const userAgency = JSON.parse(localStorage.getItem('userAgency'));
        let rootAgencyId: any = '';
        if (userAgency !== null) {
          rootAgencyId = userAgency.id;
        } else {
          rootAgencyId = this.config.rootAgency.id;
        }
        this.dossierService.postSMSByAgency(
          rootAgencyId,
          this.config.subsystemId,
          subject,
          [phone.number]
        ).subscribe(smsRS => {
          console.log(smsRS);
        }, err => {
          console.log(err);
        });
      });
    }
  }
  // ========= file
  uploadMultiFile(file, userId) {
    this.procedureService.uploadMultiFile(file, userId).subscribe(data => {
        this.uploadedImage = data;
        this.putExtendTime();
      }, err => {
        console.log(err);
    });
  }

  onSelectFile(event) {
      if (event.target.files && event.target.files[0]) {
        for (const i of event.target.files) {
          if (i.size >= this.maxFileSize * 1024 * 1024) {
            const msgObj = {
              vi: 'Kích thước tệp tin quá lớn, tên tệp: ' + i.name,
              en: 'The file is too large, file name: ' + i.name
            };
            this.snackbarService.openSnackBar(0, '', msgObj[this.selectedLang], 'error_notification', this.config.expiredTime);
            return;
          }
          if (this.acceptFileType.filter(t => t === i.type.toLowerCase()).length > 0) {
            this.files.push(i);
            const extension = i.name.substring(i.name.lastIndexOf('.')).split('.').pop();
            this.urls.push(this.getFileIcon(extension));

            const reader = new FileReader();
            reader.onload = () => {
              this.uploaded = true;
            };
            if (i.name.length > 20) {
              const startText = i.name.substr(0, 5);
              const shortText = i
                .name
                .substring(i.name.length - 7, i.name.length);
              this.fileNames.push(startText + '...' + shortText);
              this.fileNamesFull.push(i.name);
            } else {
              this.fileNames.push(i.name);
              this.fileNamesFull.push(i.name);
            }
            reader.readAsDataURL(i);
          }
          else {
            const msgObj = {
              vi: 'Không hỗ trợ loại tệp tin ',
              en: 'File type is not supported '
            };
            // tslint:disable-next-line: max-line-length
            this.snackbarService.openSnackBar(0, '', msgObj[this.selectedLang] + i.type.substring(i.type.lastIndexOf('/') + 1).toUpperCase(), 'error_notification', this.config.expiredTime);
          }
        }
      }
  }

  removeItem(index: number) {
      this.urls.splice(index, 1);
      this.fileNames.splice(index, 1);
      this.fileNamesFull.splice(index, 1);
      this.files.splice(index, 1);
      this.blankVal = '';
  }

  getFileIcon(ext) {
      return this.config.cloudStaticURL + 'icon/files/300x300/' + ext + '.png';
  }
  getConfigCloud() {
    const config = this.deploymentService.getAppDeployment();
    if (this.env?.OS_BDG?.isRequiredUploadFileBDG?.extendTime != undefined && this.env?.OS_BDG?.isRequiredUploadFileBDG?.extendTime != null) {
      this.requireAttachmentWhenPause = this.env?.OS_BDG?.isRequiredUploadFileBDG?.extendTime;
    } else {
      if (!!config?.env?.MCDT_DINH_KEM_FILE_KHI_TAM_DUNG) {
        this.requireAttachmentWhenPause = config?.env?.MCDT_DINH_KEM_FILE_KHI_TAM_DUNG;
      }
    }
  }

  downloadTemplate(){
    window.open(this.fileTemplate, '_self');
  }

  
  setdossierTaskStatus() {
    return new Promise<void>(resolve => {
      const tagWaitingForApprovalStatusId = this.allowNextStepWaitingForApproval ? this.deploymentService.env.dossierTaskStatus.waitingForApprovalToExtend.id : this.deploymentService.env.dossierTaskStatus.requestForAdditionalDocuments.id;
      this.dossierService.getAgencyTag(tagWaitingForApprovalStatusId).subscribe(data => {
        this.dossierTaskStatusWaitingForApproval.id = data.id;
        this.dossierTaskStatusWaitingForApproval.name = data.trans;
        resolve();
      }, err => {
        resolve();
      });
    });

  }

  setdossierTaskStatusRemind () {
    return new Promise<void>(resolve => {
      const tagWaitingForApprovalRemindId = this.allowNextStepWaitingForApproval ? this.deploymentService.env.dossierMenuTaskRemind.waitingForApprovalToExtend.id : this.deploymentService.env.dossierMenuTaskRemind.requestForAdditional.id;
      this.dossierService.getAgencyTag(tagWaitingForApprovalRemindId).subscribe(data => {
        this.dossierMenuTaskRemindWaitingForApproval.id = data.id;
        this.dossierMenuTaskRemindWaitingForApproval.name = data.trans;
        resolve();
      }, err => {
        resolve();
      });
    });
  }


}
export class ExtendTimeModel {
  constructor(public dossierId: string, public dossierCode: string) {
  }
}
