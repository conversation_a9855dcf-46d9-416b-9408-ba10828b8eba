<button mat-icon-button class="close-button" (click)="onDismiss()">
  <mat-icon>close</mat-icon>
</button>
<h3 class="dialog_title" mat-dialog-title><PERSON>h sách chi tiết hồ sơ chỉ thị 18</h3>
<div mat-dialog-content class="dialog_content">
  <div fxLayout="row" fxLayoutAlign="center">
    <div fxFlex="grow">
      <div class="tbl mat-elevation-z8">
        <table mat-table [dataSource]="dataSource">
          <ng-container matColumnDef="stt">
            <th *matHeaderCellDef>STT</th>
            <td *matCellDef="let row" data-label="STT" class="text-center"> {{ row.no }}
            </td>
          </ng-container>
          <ng-container matColumnDef="so_ho_so">
            <th *matHeaderCellDef>Số hồ sơ</th>
            <td *matCellDef="let row" data-label="<PERSON><PERSON> hồ sơ"> {{ row.dossierCode}}
            </td>
          </ng-container>
          <ng-container matColumnDef="thu_tuc">
            <th *matHeaderCellDef>Tên TTHC</th>
            <td *matCellDef="let row" data-label="Tên TTHC"> {{ row.procedureName }}
            </td>
          </ng-container>
          <ng-container matColumnDef="nguoi_nop">
            <th *matHeaderCellDef>Tên tổ chức, cá nhân</th>
            <td *matCellDef="let row" data-label="Tên tổ chức, cá nhân"> {{ row.tenToChuc }}<br/>{{ row.tenCaNhan }}
            </td>
          </ng-container>
          <ng-container matColumnDef="dia_chi">
            <th *matHeaderCellDef>Địa chỉ, số điện thoại</th>
            <td *matCellDef="let row" data-label="Địa chỉ, số điện thoại"> {{ row.soDienThoai }}<br/>{{ row.diaChi }}
            </td>
          </ng-container>
          <ng-container matColumnDef="co_quan_chu_tri">
            <th *matHeaderCellDef>Cơ quan chủ trì giải quyết</th>
            <td *matCellDef="let row" data-label="Cơ quan chủ trì giải quyết">{{ row.coQuan }}
            </td>
          </ng-container>
          <ng-container matColumnDef="don_vi_tiep_nhan">
            <th *matHeaderCellDef>Đơn vị tiếp nhận</th>
            <td *matCellDef="let row" data-label="Đơn vị tiếp nhận">{{ row.donViTiepNhan }}
            </td>
          </ng-container>
          <ng-container matColumnDef="ngay_nhan">
            <th *matHeaderCellDef>Nhận hồ sơ</th>
            <td *matCellDef="let row" data-label="Nhận hồ sơ" class="text-center"> {{ row.acceptedDate }}
            </td>
          </ng-container>
          <ng-container matColumnDef="ngay_hen_tra">
            <th *matHeaderCellDef>Hẹn trả kết quả</th>
            <td *matCellDef="let row" data-label="Hẹn trả kết quả" class="text-center">{{ row.appointmentDate }}
            </td>
          </ng-container>
          <ng-container matColumnDef="ngay_co_ket_qua">
            <th *matHeaderCellDef>Ngày có kết quả/YC trả lại dân</th>
            <td *matCellDef="let row" data-label="Ngày có kết quả/YC trả lại dân">{{ row.completedDate }}
            </td>
          </ng-container>
          <ng-container matColumnDef="ngay_tra_ket_qua">
            <th *matHeaderCellDef>Ngày trả kết quả/trả lại dân</th>
            <td *matCellDef="let row" data-label="Ngày trả kết quả/trả lại dân">{{ row.returnedDate }}
            </td>
          </ng-container>
          <ng-container matColumnDef="hinh_thuc_nhan_ket_qua">
            <th *matHeaderCellDef>Hình thức nhận kết quả</th>
            <td *matCellDef="let row" data-label="Hình thức nhận kết quả" class="text-center">{{ row.hinhThucTraKetQua }}
            </td>
          </ng-container>
          <ng-container matColumnDef="hinh_thuc_tiep_nhan">
            <th *matHeaderCellDef>Hình thức tiếp nhận</th>
            <td *matCellDef="let row" data-label="Hình thức tiếp nhận" class="text-center">{{ row.hinhThucTiepNhan }}
            </td>
          </ng-container>

          <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
        </table>
      </div>
    </div>
  </div>
  <div fxLayout="row" fxLayoutAlign="center">
    <div fxFlex="grow">
      <div class="frm_Pagination">
        <ul class="temp_Arr">
          <li
            *ngFor="let item of ELEMENTDATA  | paginate: {itemsPerPage: size, currentPage: page, totalItems: countResult, id: 'pgnx1'}">
          </li>
        </ul>
        <div class="pageSize">
          <span i18n>Hiển thị </span>
          <mat-form-field appearance="outline">
            <mat-select [(value)]="size" (valueChange)="paginate(pageIndex, 1)">
              <mat-option *ngFor='let opt of pgSizeOptions;' [value]="opt">{{opt}}</mat-option>
            </mat-select>
          </mat-form-field>
          <span><span i18n>trên</span> {{countResult}} <span i18n>bản ghi</span></span>
        </div>
        <div class="control">
          <pagination-controls id="pgnx1" (pageChange)="page = $event; paginate(page, 0)" responsive="true"
            previousLabel="" nextLabel="">
          </pagination-controls>
        </div>
      </div>
    </div>
  </div>
</div>
