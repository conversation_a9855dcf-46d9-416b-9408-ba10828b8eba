import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { ApiProviderService } from 'src/app/core/service/api-provider.service';
import { EnvService } from 'src/app/core/service/env.service';

@Injectable({
    providedIn: 'root'
})
export class LedgerDefinitionService {

    constructor(
        private http: HttpClient,
        private envService: EnvService,
        private apiProviderService: ApiProviderService,
    ) { }

    private searchUrls = this.apiProviderService.getUrl('digo', 'basepad') + '/ledger-definition/--search';
    private postUrls = this.apiProviderService.getUrl('digo', 'basepad') + '/ledger-definition';
    private getUpdateStatusUrls(id: string){
        return this.apiProviderService.getUrl('digo', 'basepad') + `/ledger-definition/${id}/--status`;
    }
    private getDetailsUrls(id: string){
        return this.apiProviderService.getUrl('digo', 'basepad') + `/ledger-definition/${id}`;
    }
    private getUpdateUrls(id: string){
        return this.apiProviderService.getUrl('digo', 'basepad') + `/ledger-definition/${id}`;
    }
    private getCloneLedgerDefinition(id: string){
        return this.apiProviderService.getUrl('digo', 'basepad') + `/cto-ledger-definition/${id}/--clone-ledger-definition`;
    }
    private postCTOUrls = this.apiProviderService.getUrl('digo', 'basepad') + '/cto-ledger-definition';

    search(query): Observable<any> {
        const endpoint = this.searchUrls + (!!query ? '?' + query : '');
        return this.http.get(endpoint);
    }

    post(body: any): Observable<any>{
        let headers = new HttpHeaders();
        headers = headers.set('Content-Type', 'application/json');
        return this.http.post<any>(this.postUrls, body, { headers });
    }

    update(id: string, body: any): Observable<any>{
        let headers = new HttpHeaders();
        headers = headers.set('Content-Type', 'application/json');
        return this.http.put<any>(this.getUpdateUrls(id), body, { headers });
    }

    updateStatus(id: string): Observable<any>{
        let headers = new HttpHeaders();
        headers = headers.set('Content-Type', 'application/json');
        return this.http.put<any>(this.getUpdateStatusUrls(id), '', { headers });
    }

    details(id: string): Observable<any>{
        return this.http.get(this.getDetailsUrls(id));
    }

    cloneLedgerDefinition(id: string, body: any): Observable<any> {
        let headers = new HttpHeaders();
        headers = headers.set('Content-Type', 'application/json');
        return this.http.put<any>(this.getCloneLedgerDefinition(id), body, { headers });
    }

    delete(id: string) {
        let headers = new HttpHeaders();
        headers = headers.append('Content-Type', 'application/json');
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        return this.http.delete(this.postUrls + '/' + id, { headers });
    }

    postCTO(body: any): Observable<any>{
        let headers = new HttpHeaders();
        headers = headers.set('Content-Type', 'application/json');
        return this.http.post<any>(this.postCTOUrls, body, { headers });
    }

    updateCTO(id: string, body: any): Observable<any>{
        let headers = new HttpHeaders();
        headers = headers.set('Content-Type', 'application/json');
        return this.http.put<any>(this.postCTOUrls + "/" + id, body, { headers });
    }

    detailsCTO(id: string): Observable<any>{
        return this.http.get<any>(this.postCTOUrls + '/' + id);
    }
}
