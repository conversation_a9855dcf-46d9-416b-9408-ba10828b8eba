export interface Pattern {
    id: string;
    name: string;
    status: number;
    delimiter: string;
    component: component[];
  }
  export interface component {
    tag: tag;
    value: string;
    format: string;
    order: number;
    rotate: number;
    minLength: number;
    originalValue: number;
  }
  export interface tag {
    id: string;
    name: string;
  }
  export interface PatternListResponse {
    content: Pattern[];
    size?: number;
    pageable?: { pageNumber: number };
    totalElements?: number;
    numberOfElements?: number;
    totalPages?: number;
  }
  
  export interface PatternResponse {
    affectedRows: number;
  }
  