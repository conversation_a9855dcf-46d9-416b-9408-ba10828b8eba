import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { DLKDigitizationReportComponent } from './dlk-digitization-report.component';
import { DlkDigitizationReportDetailComponent } from './dlk-digitization-report-detail/dlk-digitization-report-detail.component';
import { AuthGuard } from 'src/app/core/guard/auth.guard';


const routes: Routes = [
  {
    path: '',
    component: DLKDigitizationReportComponent
  },
  {
    path: 'chi-tiet-ho-so',
    loadChildren: () => import('./dlk-digitization-report-detail/dlk-digitization-report-detail.module').then(m => m.DlkDigitizationReportDetailModule),
    canActivate: [AuthGuard],
    data: {
      anyPermissions: ['dlkChiTietHoSo']
    }
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class DLKDigitizationReportRoutingModule { }
