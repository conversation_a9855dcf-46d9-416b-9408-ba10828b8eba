import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { ApiProviderService } from 'src/app/core/service/api-provider.service';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class LevelReportService {

  constructor(
    private http: HttpClient,
    private apiProviderService: ApiProviderService
  ) { }

  private basepad = this.apiProviderService.getUrl('digo', 'basepad');
  private procedurePath = this.apiProviderService.getUrl('digo', 'basepad') + '/procedure/';
  private agencyPath = this.apiProviderService.getUrl('digo', 'basedata') + '/agency/';
  private human = this.apiProviderService.getUrl('digo', 'human');
  private basedata = this.apiProviderService.getUrl('digo', 'basedata');
  private reporter = this.apiProviderService.getUrl('digo', 'reporter');

  getListAgency(search: string): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.agencyPath + 'tree-view' + search, { headers });
  }

  getStatisticProcedure(search: String): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.basepad + '/procedure/--statistic-agency' + search, { headers });
  }

  getDetailAgency(id: String): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.basedata + '/agency/' + id, { headers });
  }

  getStatisticHuman(search: String): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.human + '/user/statistic/--by-agencyId' + search, { headers });
  }

  getStatisticDossier(search: String): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.reporter + '/dossier/statistic/--by-level' + search, { headers });
  }

  getListSector(search: String): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.basepad + '/procedure/sector-by-agency' + search, { headers });
  }

  getDetailProcedure(id: String): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.basepad + '/procedure/' + id, { headers });
  }

  
}
