import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { AuthGuard } from 'src/app/core/guard/auth.guard';

const routes: Routes = [
  {
    path: 'household-info',
    loadChildren: () => import('./household-info/household-info.module').then(m => m.HouseholdInfoModule),
    canActivate: [AuthGuard],
    data: {
      anyPermissions: ['oneGateHouseholdInfo']
    }
  },
  {
    path: 'get-citizen-info',
    loadChildren: () => import('./get-citizen-info-bdh/get-citizen-info-bdh.module').then(m => m.GetCitizenInfoBdhModule),
    canActivate: [AuthGuard],
    data: {
      anyPermissions: ['oneGateGetCitizenInfo']
    }
  }

];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class BdhDacThuRoutingModule { }
