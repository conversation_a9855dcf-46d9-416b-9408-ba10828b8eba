import { Component, OnInit, Inject } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';

@Component({
  selector: 'app-error',
  templateUrl: './error.component.html',
  styleUrls: ['./error.component.scss']
})
export class ErrorComponent implements OnInit {
  vi: string;
  en: string;
  msg: string;
  selectedLang: string;

  constructor(
    public dialogRef: MatDialogRef<ErrorComponent>,
    @Inject(MAT_DIALOG_DATA) public data: ErrorDialogModel,
  ) {
    this.vi = data.vi;
    this.en = data.en;
  }

  ngOnInit(): void {
    this.selectedLang = localStorage.getItem('language');
    if (this.selectedLang === 'vi') {
      this.msg = this.vi;
    } else {
      this.msg = this.en;
    }
  }

  onDismiss() {
    this.dialogRef.close();
  }

}

export class ErrorDialogModel {
  constructor(public vi: string, public en: string) {
  }
}
