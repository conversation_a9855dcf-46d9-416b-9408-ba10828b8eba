import { Component, OnInit } from '@angular/core';
import { EnvService } from 'src/app/core/service/env.service';
import { DeploymentService } from 'src/app/data/service/deployment.service';

@Component({
  selector: 'app-account-integration',
  templateUrl: './account-integration.component.html',
  styleUrls: ['./account-integration.component.scss']
})
export class AccountIntegrationComponent implements OnInit {
  env = this.deploymentService.getAppDeployment()?.env;
  deploymentConfig = this.deploymentService.getAppDeployment();
  islinkAccount;
  constructor(private envService: EnvService,
    private deploymentService: DeploymentService) {

  }

  ngOnInit(): void {
    this.islinkAccount = this.env?.OS_KTM?.islinkAccount;
    console.log(this.islinkAccount);
  }


}
