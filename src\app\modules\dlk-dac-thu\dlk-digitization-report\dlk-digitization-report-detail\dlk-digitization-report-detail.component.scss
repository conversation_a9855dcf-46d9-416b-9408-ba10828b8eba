@import "~src/styles/pagination.scss";

.example-form-field {
    margin-right: 20px;
}

td.mat-footer-cell {
    text-align: center;
}

.search-nested {
    width: 80% !important;
    height: 50px !important;
    margin-left: 15px !important;
}

.clear-search-nested {
    right: 0 !important;
    position: absolute !important;
    top: 0 !important;
}

// ================================= searchForm
.hidden {
    display: none !important;
}

::ng-deep .frm_searchbar .searchForm .mat-form-field-appearance-outline .mat-form-field-outline {
    color: transparent;
}

::ng-deep .frm_searchbar .searchForm .mat-form-field-appearance-outline .mat-form-field-outline-thick {
    color: #dddddd;
}

::ng-deep .frm_searchbar .searchForm .mat-form-field-appearance-outline .mat-form-field-outline {
    background-color: #eaebeb;
    border-radius: 5px;
}

::ng-deep .frm_searchbar .searchForm .searchBtn {
    background-color: #ce7a58;
    color: #fff;
    height: 3.2em;
}

::ng-deep .frm_searchbar .searchForm .downloadExcel {
    background-color: #16a7eb;
    color: #fff;
    height: 3.2em;
}

::ng-deep .frm_searchbar .mat-form-field-appearance-outline .mat-form-field-infix {
    padding: 0.8em 0;
}

::ng-deep .frm_searchbar .mat-form-field-label-wrapper {
    top: -1em;
}

::ng-deep .frm_searchbar .mat-form-field.mat-focused .mat-form-field-label {
    color: #ce7a58;
    font-size: 18px;
}

::ng-deep .frm_searchbar .mat-form-field-appearance-outline.mat-form-field-can-float.mat-form-field-should-float .mat-form-field-label {
    color: #ce7a58;
    font-size: 18px;
}

// ================================= table + frm_tbl + tab 1
::ng-deep .frm_tbl0 {
    flex: 1;
    overflow: scroll;
    .logbookTbl {

        //margin-top: 1.5em;

        .logbookOnlyTbl {
            overflow-y: scroll;
        }

        table {
            border-collapse: collapse;
            width: 100%;
        }

        table tr th {
            border: 1px solid;
            text-align: center;
            font-weight: 500;
            background: #ce7a58;
            border: 1px solid rgb(182, 182, 182);
            padding: 5px;
            font-size: 14px;
        }

        table th {
            border: 1px solid rgb(182, 182, 182);
            text-align: center;
            padding: 5px;
            font-size: 14px;
        }
        
        table tr td {
            border: 1px solid rgb(182, 182, 182);
            text-align: center;
            padding: 5px;
            font-size: 14px;
        }

        table tr td a {
            cursor: pointer;
            color: #3c8dbc;
        }

        .sum {
            font-weight: 500;
            background-color: #dff0d8;
        }

        ;

        // table {
        //     width: 100%;
        //     border-radius: 4px;
        //     border: 1px solid #cccccc50;
        //     width: 100%;
        // }

        // th {
        //     //padding: .5em;
        //     border: 1px solid #cccccc50 !important;
        // }

        // .cell_code {
        //     color: #ce7a58;
        //     font-weight: 500;
        //     text-decoration: none;

        //     a {
        //         cursor: pointer;
        //     }
        // }

        // table {
        //     width: 100%;
        //     border-radius: 4px;
        //     border: 1px solid #ececec;
        //     width: 100%;
        // }

        // .th {
        //     padding: .5em;
        //     border: .2px solid #cccccc50 !important;
        // }

        // .tr {
        //     background-color: #e8e8e8;
        //     min-height: 3.5em !important;

        //     .th {
        //         color: #495057;
        //         font-size: 14px;
        //         padding: 0 .5em;
        //         border: .2px solid #cccccc50 !important;

        //         p {
        //             margin-bottom: 0;
        //             font-weight: 400;
        //             font-style: italic;
        //         }
        //     }
        // }

        // .mat-column-stt {
        //     padding-right: 0.5em;
        //     padding-left: 1em;
        //     flex: 0 0 5%;
        // }

        // .mat-column-code a {
        //     text-decoration: none;
        //     font-weight: 500;
        //     color: #ce7a58;
        // }

        // .mat-column-procedure {
        //     flex: 1 0 5%;
        //     padding: 0 0.5em;

        //     .procedureName {
        //         display: -webkit-box;
        //         -webkit-line-clamp: 4;
        //         -webkit-box-orient: vertical;
        //         width: 100%;
        //         overflow: hidden;
        //         text-overflow: ellipsis;
        //     }
        // }
    }
}

.data-label {
    word-wrap: break-word;
}

tr.mat-footer-row {
    font-weight: bold;
}

::ng-deep {
    .btnDisabled {
        cursor: not-allowed;
    }

    .mat-button-wrapper {
        display: flex;
        justify-content: center;

        .mat-spinner {
            margin-right: 0.3em;
            align-self: center;

            circle {
                // stroke: #ce7a58;
                stroke: white;
            }
        }
    }

    .iconStatistical {
        padding-top: 5px;
    }
}

::ng-deep .closeBtn {
    background-color: #ce7a58;
    color: #fff;
    height: 3em;
    padding: 0 3em;
    margin-left: 20px;
}

::ng-deep .exportExcelBtn {
    background-color: #16a7eb;
    color: #fff;
    height: 3em;
    padding: 0 3em;
}

.dialog_title {
    font-style: normal;
    font-weight: 500;
    font-size: 20px;
    color: #CE7A58;
}

.body {
    display: flex;
    height: 100%;
    flex-direction: column;
}

.tableSpinnerContainer {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
}

::ng-deep .mat-progress-spinner circle,
.mat-spinner circle {
    stroke: #CE7A58;
}

::ng-deep .exportExcelBtnContainer {

    button:disabled,
    button[disabled] {
        background-color: #16a7eb !important;
        color: #fff !important;
        height: 3em !important;
        padding: 0 3em !important;
    }
}

#overload {
    width: 100%;
    height: 100%;
    background-color: rgba(210, 210, 210, 0.33);
    position: fixed;
    top: 0;
    left: 0;
    z-index: 10000;
    display: none;
}

.loading {
    top: calc(50% - 44px);
    left: calc(50% - 44px);
    position: absolute;
}

.spinner-3 {
    width: 50px;
    padding: 8px;
    aspect-ratio: 1;
    border-radius: 50%;
    background: #d1681a;
    --_m: conic-gradient(#0000 10%, #000), linear-gradient(#000 0 0) content-box;
    -webkit-mask: var(--_m);
    mask: var(--_m);
    -webkit-mask-composite: source-out;
    mask-composite: subtract;
    animation: s3 1s infinite linear;
}

@keyframes s3 {
    to {
        transform: rotate(1turn)
    }
}
