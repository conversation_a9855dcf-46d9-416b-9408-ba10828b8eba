<button mat-icon-button class="close-button" (click)="onDismiss()">
  <mat-icon>close</mat-icon>
</button>
<h3 *ngIf="blockingDossierId == null" class="dialog_title" mat-dialog-title>T<PERSON><PERSON> mớ<PERSON> hồ sơ ngăn chặn</h3>
<h3 *ngIf="blockingDossierId != null" class="dialog_title" mat-dialog-title>C<PERSON><PERSON> nh<PERSON><PERSON> hồ sơ ngăn chặn</h3>
<form [formGroup]="addForm" class="addForm">
  <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="column">
    <mat-form-field appearance="outline" fxFlex='grow'>
      <mat-label>Tên người sử dụng đất</mat-label>
      <input type="text" matInput formControlName="ownerFullname" required>
    </mat-form-field>
  </div>
  <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="column">
    <mat-form-field appearance="outline" fxFlex='grow'>
      <mat-label>Số CMND/CCCD</mat-label>
      <input type="text" matInput formControlName="identityNumber" required>
    </mat-form-field>
  </div>
  <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="column">
    <mat-form-field appearance="outline" fxFlex='grow'>
      <mat-label>Số bìa đỏ</mat-label>
      <input type="text" matInput formControlName="certificateId" required>
    </mat-form-field>
  </div>
  <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="column">
    <mat-form-field appearance="outline" fxFlex='grow'>
      <mat-label>Thửa đất số</mat-label>
      <input type="text" matInput formControlName="slotId">
    </mat-form-field>
  </div>
  <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="column">
    <mat-form-field appearance="outline" fxFlex='grow'>
      <mat-label>Tờ bản đồ số</mat-label>
      <input type="text" matInput formControlName="mapId">
    </mat-form-field>
  </div>
  <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="column">
    <mat-form-field appearance="outline" fxFlex='grow'>
      <mat-label>Số vào sổ</mat-label>
      <input type="text" matInput formControlName="recordId">
    </mat-form-field>
  </div>
  <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="column">
    <mat-form-field appearance="outline" fxFlex='grow'>
      <mat-label>Ngày ký QĐ ngăn chặn</mat-label>
      <input matInput [matDatepicker]="pickerBlockDate" [max]="currentDate" formControlName="blockDate" maxlength="20">
      <mat-datepicker-toggle matSuffix [for]="pickerBlockDate"></mat-datepicker-toggle>
      <mat-datepicker #pickerBlockDate></mat-datepicker>
    </mat-form-field>
  </div>
  <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="column">
    <mat-form-field appearance="outline" fxFlex='grow'>
      <mat-label>Nội dung</mat-label>
      <textarea matInput formControlName="content" required></textarea>
    </mat-form-field>
  </div>
  <!--
  <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="column">
    <span class="editorLabel" fxFlex='grow'><span>Nội dung (*)</span></span>
  </div>
  <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="column">
    <textarea matInput formControlName="content" required style="display: none;"></textarea>
    <ckeditor class="prc_Editor" fxFlex='grow' formControlName="content"
              [editor]="Editor" [config]='editorConfig'
              (change)="onEditorChange('content', $event)"
              (focusout)="onEditorFocusOut('content', $event)">
    </ckeditor>
    <div class="errorMsg" *ngIf="CKMaxlength[0].required">
      <span>Nội dung này là bắt buộc</span>
      <div class="err">
        <mat-icon>priority_high</mat-icon>
      </div>
    </div>
    <div class="errorMsg" *ngIf="CKMaxlength[0].isMaxLength">
      <span>Nội dung không quá {{maxCkeditorLength}} ký tự</span>
      <div class="err">
        <mat-icon>priority_high</mat-icon>
      </div>
    </div>
  </div>
  -->
  <br>
  <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="column" fxLayoutAlign="center">
    <button mat-flat-button fxFlex='17' class="addBtn" (click)="onConfirm()" [disabled]="addForm.invalid">
      <span i18n>Lưu lại</span>
    </button>
  </div>
</form>

