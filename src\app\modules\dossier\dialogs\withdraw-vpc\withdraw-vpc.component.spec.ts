import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { WithdrawVpcComponent } from './withdraw-vpc.component';

describe('WithdrawVpcComponent', () => {
  let component: WithdrawVpcComponent;
  let fixture: ComponentFixture<WithdrawVpcComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ WithdrawVpcComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(WithdrawVpcComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
