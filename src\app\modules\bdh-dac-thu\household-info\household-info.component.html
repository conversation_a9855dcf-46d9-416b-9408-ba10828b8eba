<form [formGroup]="productForm">

    <!-- <h2>D<PERSON><PERSON> vụ xác thực thông tin hộ gia đình</h2> -->

    <div fxLayout="row" fxLayout.xs="row" fxLayout.sm="row" class="formFieldItems" fxLayoutAlign="space-between">
        <mat-form-field  appearance="outline" [fxFlex.gt-md]="checkIdFlex" fxFlex.gt-sm="49.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
            <mat-label>
                Họ và tên chủ hộ
                <mat-icon *ngIf="ELEMENTDATA?.info?.correct === 'true' || productForm.get('checkCH')?.value === '1'" style="color:green;">check_circle</mat-icon>
                <mat-icon *ngIf="ELEMENTDATA?.info?.correct === 'false'" style="color:red;">cancel</mat-icon>
            </mat-label>
            <input matInput formControlName="fullNameOwner" maxlength="500" [readonly] ="ELEMENTDATA?.info?.correct === 'true'">
        </mat-form-field>

        <mat-form-field  appearance="outline" [fxFlex.gt-md]="checkIdFlex" fxFlex.gt-sm="49.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
            <mat-label>CCCD chủ hộ
                <mat-icon *ngIf="ELEMENTDATA?.info?.correct === 'true' || productForm.get('checkCH')?.value === '1'" style="color:green;">check_circle</mat-icon>
                <mat-icon *ngIf="ELEMENTDATA?.info?.correct === 'false'" style="color:red;">cancel</mat-icon>
            </mat-label>
            <input matInput formControlName="identityOwner" maxlength="500" [readonly] ="ELEMENTDATA?.info?.correct === 'true'">
        </mat-form-field>

        <mat-form-field  appearance="outline" [fxFlex.gt-md]="checkIdFlex" fxFlex.gt-sm="49.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
            <mat-label>Ngày sinh chủ hộ
                <mat-icon *ngIf="ELEMENTDATA?.info?.correct === 'true' || productForm.get('checkCH')?.value === '1'" style="color:green;">check_circle </mat-icon>
                <mat-icon *ngIf="ELEMENTDATA?.info?.correct === 'false'" style="color:red;">cancel</mat-icon>
            </mat-label>
            <input matInput [matDatepicker]="pickerAcceptTo" formControlName="birthDayOwner" maxlength="500" [readonly] ="ELEMENTDATA?.info?.correct === 'true'">
            <mat-datepicker-toggle matSuffix [for]="pickerAcceptTo"></mat-datepicker-toggle>
            <mat-datepicker #pickerAcceptTo><mat-icon>image_search</mat-icon></mat-datepicker>
        </mat-form-field>

        <button fxFlex.gt-md="20" fxFlex='grow' *ngIf="(checkId !='1') && isShowHousehold" type="submit" mat-flat-button (click)="checkInfo()">
            <span>Xác thực thông tin chủ hộ</span>
        </button>
        <button *ngIf="productForm.get('checkCH')?.value === '1' && productForm.get('checkCH')?.value === '1' && !duplicateError && checkId !='1' && isShowHousehold"  fxFlex.gt-md="20" fxFlex='grow' type="submit" mat-flat-button (click)="checkInfoMember()">
            <span>Xác thực thông tin thành viên</span>
        </button>

    </div>
    <div>
        <div *ngIf="productForm.get('checkCH')?.value === '1' && productForm.get('checkTV')?.value ==='1'" class="messageTrue">
            [CSDLQGvDC] Hộ khẩu: Đã xác thực thông tin của chủ hộ và thành viên
        </div>
        <div *ngIf="productForm.get('checkCH')?.value != '1'" class="messageFalse">
            [CSDLQGvDC] Hộ khẩu: Xác thực thông tin không thành công
        </div>
        <div *ngIf="productForm.get('checkCH')?.value === '1' && productForm.get('checkTV')?.value !='1'" class="messageTrue">
            [CSDLQGvDC] Hộ khẩu: Đã xác thực chủ hộ. Có thông tin thành viên chưa được xác thực
        </div>
        <div *ngIf="productForm.get('checkCH')?.value === '1' && productForm.get('checkTV')?.value ==='1'" class="messageTrue">
            [CSDLQGvDC] Định danh: Đã xác thực thông tin của thành viên.
        </div>
        <div *ngIf="productForm.get('checkTV')?.value ===''" class="messageFalse">
            [CSDLQGvDC] Định danh: Xác thực thông tin không thành công.
        </div>
        <div *ngIf="productForm.get('checkTV')?.value !='1' && productForm.get('checkTV')?.value !=''" class="messageTrue">
            [CSDLQGvDC] Định danh: Có thông tin thành viên chưa được xác thực.
        </div>
    </div>

    <div formArrayName="quantities">
        <!-- <h2>Thông tin thành viên</h2> -->


        <div class="form-container">
                <!-- Lặp qua từng FormGroup trong FormArray -->
                <div *ngFor="let quantity of quantities().controls; let i = index" [formGroupName]="i" class="form-row-group">
                  <h4>Thành viên {{ i + 1 }}</h4>

                <!-- Hàng 1: Họ và tên, Ngày sinh, Số định danh/CCCD, Quan hệ -->
                  
                    <div fxLayout="row" fxLayout.xs="row" fxLayout.sm="row" class="formFieldItems" fxLayoutAlign="space-between">
                    <mat-form-field appearance="outline" fxFlex.gt-md="24.5" fxFlex.gt-sm="49.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                        <mat-label>
                            Họ và tên thành viên
                            <mat-icon *ngIf="quantity.get('checkInfo')?.value == 1" style="color:green;">check_circle</mat-icon>
                            <mat-icon *ngIf="quantity.get('checkInfo')?.value == 2" style="color:red;">cancel</mat-icon>
                        </mat-label>
                        <input matInput id="fullNameMember-{{ i }}" formControlName="fullNameMember" maxlength="500" [readonly] ="quantity.get('checkInfo')?.value == 1">
                    </mat-form-field>
                    <mat-form-field appearance="outline" fxFlex.gt-md="24.5" fxFlex.gt-sm="49.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                        <mat-label>Ngày sinh thành viên
                            <mat-icon *ngIf="quantity.get('checkInfo')?.value == 1" style="color:green;">check_circle </mat-icon>
                            <mat-icon *ngIf="quantity.get('checkInfo')?.value == 2" style="color:red;">cancel</mat-icon>
                        </mat-label>
                        <input matInput id="birthDayMember-{{ i }}" [matDatepicker]="pickerAcceptTo" formControlName="birthDayMember" maxlength="500" [readonly] ="quantity.get('checkInfo')?.value == 1">
                        <mat-datepicker-toggle matSuffix [for]="pickerAcceptTo"></mat-datepicker-toggle>
                        <mat-datepicker #pickerAcceptTo><mat-icon>image_search</mat-icon></mat-datepicker>
                    </mat-form-field>
                    <mat-form-field appearance="outline" fxFlex.gt-md="24.5" fxFlex.gt-sm="49.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                        <mat-label>Số định danh/CCCD
                            <mat-icon *ngIf="(checkIdentity?.quantities[i]?.check && checkIdentity.quantities[i].check === 'YES') || quantity.get('check')?.value === 'YES'" style="color:green;">check_circle</mat-icon>
                            <mat-icon *ngIf="(checkIdentity?.quantities[i]?.check && checkIdentity.quantities[i].check === 'NO') || quantity.get('check')?.value === 'NO'" style="color:red;">cancel</mat-icon>
                        </mat-label>
                        <input matInput id="identityMember-{{ i }}" formControlName="identityMember" maxlength="500" [readonly] ="checkIdentity?.quantities[i]?.check && checkIdentity.quantities[i].check === 'YES'" (change)="isDuplicate()" >
                        <div *ngIf="duplicateError" class="error-message">
                            Số định danh/CCCD đã bị trùng. Vui lòng nhập số khác.
                          </div>
                    </mat-form-field>
                    <mat-form-field *ngIf="quantity.get('checkInfo')?.value == 1" appearance="outline" fxFlex.gt-md="24.5" fxFlex.gt-sm="49.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                        <mat-label>Quan hệ với chủ hộ
                            <mat-icon *ngIf="quantity.get('checkInfo')?.value == 1" style="color:green;">check_circle</mat-icon>
                            <mat-icon *ngIf="quantity.get('checkInfo')?.value == 2" style="color:red;">cancel</mat-icon>
                        </mat-label>
                        <input matInput id="identityMember-{{ i }}" formControlName="relationMember" maxlength="500" [readonly] ="quantity.get('checkInfo')?.value == 1">
                    </mat-form-field>
                </div>
                  <!-- Hàng 2: Thường trú -->
                  <div *ngIf="quantity.get('checkInfo')?.value == 1" class="form-row">
                    <div class="form-column-full">
                        <mat-form-field appearance="outline" fxFlex.gt-sm="32" fxFlex.gt-xs="32" fxFlex='grow'>
                            <mat-label>Thường trú
                                <mat-icon *ngIf="quantity.get('checkInfo')?.value == 1" style="color:green;">check_circle</mat-icon>
                                <mat-icon *ngIf="quantity.get('checkInfo')?.value == 2" style="color:red;">cancel</mat-icon>
                            </mat-label>
                            <input matInput id="permanentAddressMember-{{ i }}" formControlName="permanentAddressMember" maxlength="1500" [readonly] ="quantity.get('checkInfo')?.value == 1">
                        </mat-form-field>
                    </div>
                  </div>
          
                  <!-- Hàng 3: Nơi ở hiện tại -->
                  <div *ngIf="quantity.get('checkInfo')?.value == 1" class="form-row">
                    <div class="form-column-full">
                        <mat-form-field appearance="outline" fxFlex.gt-sm="32" fxFlex.gt-xs="32" fxFlex='grow'>
                            <mat-label>Nơi ở hiện tại
                                <mat-icon *ngIf="quantity.get('checkInfo')?.value == 1" style="color:green;">check_circle</mat-icon>
                                <mat-icon *ngIf="quantity.get('checkInfo')?.value == 2" style="color:red;">cancel</mat-icon>
                            </mat-label>
                            <input matInput id="currentAddressMember-{{ i }}" formControlName="currentAddressMember" maxlength="1500" [readonly] ="quantity.get('checkInfo')?.value == 1">
                        </mat-form-field>
                    </div>
                  </div>
          
                </div>
          </div>



    </div>

</form>


<div *ngIf="checkId !='1'" fxLayout="row">
    <button fxFlex.gt-md="20" fxFlex='grow' type="button" mat-flat-button (click)="addQuantity()">
        <mat-icon>add</mat-icon>
        <span>Thêm dòng</span>
    </button>
</div>