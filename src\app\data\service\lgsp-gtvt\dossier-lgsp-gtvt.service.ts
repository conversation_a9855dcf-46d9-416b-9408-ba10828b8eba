import {Injectable} from '@angular/core';
import {HttpClient, HttpHeaders, HttpParams} from '@angular/common/http';
import {ApiProviderService} from 'src/app/core/service/api-provider.service';
import {Observable} from 'rxjs';
import {EnvService} from 'src/app/core/service/env.service';
import {SnackbarService} from "data/service/snackbar/snackbar.service";
import { IdName } from '../../schema/id-name';
import { ErrorComponent, ErrorDialogModel } from 'src/app/shared/components/dialogs/error/error.component';
import { AlertComponent, AlertDialogModel } from 'src/app/shared/components/dialogs/alert/alert.component';
import { MatDialog } from '@angular/material/dialog';
import { DeploymentService } from 'src/app/data/service/deployment.service';
import { Workbook } from 'exceljs';
import * as fs from 'file-saver';
import {DatePipe} from '@angular/common';


const EXCEL_TYPE = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8';
const EXCEL_EXTENSION = '.xlsx';

@Injectable({
  providedIn: 'root'
})
export class DossierLgspGTVTService {

  config = this.envService.getConfig();
  env = this.deploymentService.getAppDeployment()?.env;

  paginationType = this.deploymentService.env.paginationType;

  procedureCodeSLDTBXHWorkPermit = this.deploymentService.env?.OS_HCM?.procedureCodeSLDTBXHWorkPermit 
                    ? this.deploymentService.env.OS_HCM.procedureCodeSLDTBXHWorkPermit 
                    : {procedureCodeLicensingWorkPermit: "", procedureCodeRenewalWorkPermit: "", procedureCodeExtendWorkPermit: ""};

  constructor(
    private envService: EnvService,
    private http: HttpClient,
    private apiProviderService: ApiProviderService,
    private snackbar: SnackbarService,
    private dialog: MatDialog,
    private deploymentService: DeploymentService,
    private datePipe: DatePipe,
  ) { }

  // private dossierURL = 'http://localhost:8080/dossier/';
  // private padman = 'http://localhost:8081';
  private digitizeURL = this.apiProviderService.getUrl('digo', 'padman') + '/dbn-digitize/';
  private dossierURL = this.apiProviderService.getUrl('digo', 'padman') + '/dossier/';
  private dossierFeeURL = this.apiProviderService.getUrl('digo', 'padman') + '/dossier-fee/';
  private padman = this.apiProviderService.getUrl('digo', 'padman');
  private basedata = this.apiProviderService.getUrl('digo', 'basedata');
  private basecat = this.apiProviderService.getUrl('digo', 'basecat');
  private basepad = this.apiProviderService.getUrl('digo', 'basepad');
  //private basepad = 'http://localhost:8080';
  private rbo = this.apiProviderService.getUrl('digo', 'rbo');
  private adapter = this.apiProviderService.getUrl('digo', 'adapter');
  // private adapter = 'http://localhost:8084';
  // env = this.deploymentService.getAppDeployment()?.env;
  // put template sign

  getListDossierLgspGtvt(data): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.post(this.adapter + '/lgsp-bo-gtvt/--get-list-dossier', data, { headers });
  }
  getDossierByCmnd(code): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.adapter + '/integrated-gtvt/--get-list-dossier?cmnd=' + code, { headers });
  }

  postDossierGplxGtvt(requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.post<any>(this.dossierURL + 'create-dossier-gplx', requestBody, { headers });
  }
}
