<div fxLayout="row" fxLayoutAlign="center">
  <div fxFlex.gt-sm="88" fxFlex="95">
    <mat-tab-group class="st_tabs">
      <mat-tab style="color: red !important;"  label="TÌNH HÌNH XỬ LÝ HỒ SƠ NĂM {{currentYear}}">
        <div style="display: flex; width: 100%;  justify-content: center; padding-top: 1.5rem">
          <div style="color: red; font-size: 30px"> {{countTiLeDungHan}}% <PERSON><PERSON> sơ đúng hạn</div>
        </div>
        <div fxLayout="row" fxLayout.xs="row" fxLayout.sm="row" class="listAgency"
             infinite-scroll
             [infiniteScrollDistance]="2"
             [infiniteScrollThrottle]="50">
          <div class="st_item" fxFlex.gt-xs="31" fxFlex.gt-sm="22.8" fxFlex="95">
            <div class="head">
              <div class="agency_avatar">
              </div>
              <span #tooltip="matTooltip" matTooltip="Tồn đầu kỳ">T<PERSON><PERSON> đ<PERSON><PERSON> kỳ</span>
            </div>
            <div class="body">
                            <span class="data" style="display: block; text-align: center;"> <button mat-button style="color: red; min-width: 0px !important;
                            padding: 0px !important; font-size: 27px !important;" (click)="listDossierDauKy(4)" >{{countDauKy}}</button> Hồ sơ</span><br>
            </div>
          </div>
          <div class="st_item" fxFlex.gt-xs="31" fxFlex.gt-sm="22.8" fxFlex="95">
            <div class="head">
              <div class="agency_avatar">
              </div>
              <span #tooltip="matTooltip" matTooltip="Đã tiếp nhận">Đã tiếp nhận</span>
            </div>
            <div class="body">
              <span class="data" style="display: block; text-align: center;"> <b style="color: dodgerblue;">{{countDaTiepNhan}}</b> Hồ sơ</span><br>
              <span> Trực tuyến: <button style="min-width: 0px !important; padding: 0px !important;" mat-button  (click)="listDossier(0)"> {{countDaTiepNhanTrucTuyen}} </button> hồ sơ </span><br>
              <span> Trực tiếp: <button style="min-width: 0px !important; padding: 0px !important;" mat-button (click)="listDossier(1)"> {{countDaTiepNhanTrucTiep}} </button> hồ sơ </span><br>
              <!-- <span> Liên thông: <button style="min-width: 0px !important; padding: 0px !important;" mat-button (click)="listDossier(2)"> {{countDaTiepNhanLienThong}} </button> hồ sơ </span><br> -->
            </div>
          </div>
          <div class="st_item" fxFlex.gt-xs="31" fxFlex.gt-sm="22.8" fxFlex="95" >
            <div class="head">
              <div class="agency_avatar">
              </div>
              <span #tooltip="matTooltip" matTooltip="Đã giải quyết">Đã giải quyết</span>
            </div>
            <div class="body">
              <span class="data" style="display: block; text-align: center;"> <b style="color: dodgerblue;">{{countDaGiaiQuyet}}</b> Hồ sơ</span><br>
              <span> Đúng hạn: <button style="min-width: 0px !important; padding: 0px !important;" mat-button  (click)="listDossier(3)"> {{countDaGiaiQuyetDungHan}} </button> hồ sơ </span><br>
              <span> Quá hạn: <button style="min-width: 0px !important; padding: 0px !important;" mat-button (click)="listDossier(4)"> {{countDaGiaiQuyetQuaHan}} </button> hồ sơ </span><br>
            </div>
          </div>
          <div class="st_item" fxFlex.gt-xs="31" fxFlex.gt-sm="22.8" fxFlex="95">
            <div class="head">
              <div class="agency_avatar">
              </div>
              <span #tooltip="matTooltip" matTooltip="Đang giải quyết">Đang giải quyết</span>
            </div>
            <div class="body">
              <span class="data" style="display: block; text-align: center;"><b style="color: #35A9A2;">{{countDangGiaiQuyet}}</b> Hồ sơ</span><br>
              <span> Còn hạn: <button style="min-width: 0px !important; padding: 0px !important;" mat-button  (click)="listDossier(5)"> {{countDangGiaiQuyetDungHan}} </button> hồ sơ </span> <br/>
              <span> (Đến hạn: <button style="min-width: 0px !important; padding: 0px !important;" mat-button  (click)="listDossier(6)"> {{countDangGiaiQuyetDenHan}} </button> hồ sơ) </span><br>
              <span> Trễ hạn: <button style="min-width: 0px !important; padding: 0px !important;" mat-button (click)="listDossier(7)"> {{countDangGiaiQuyetQuaHan}} </button> hồ sơ </span><br>
            </div>
          </div>
        </div>
        <div class="frm_tbl_sector sector" style="height: 700px; overflow: auto;">
          <table mat-table
                 [dataSource]="dataSource">
            <!-- stt -->
            <ng-container matColumnDef="stt">
              <mat-header-cell *matHeaderCellDef i18n>STT</mat-header-cell>
              <mat-cell *matCellDef="let row" i18n-data-label data-label="STT"> {{row.stt}} </mat-cell>
            </ng-container>
            <ng-container matColumnDef="code">
              <mat-header-cell *matHeaderCellDef>Mã hồ sơ</mat-header-cell>
              <mat-cell *matCellDef="let row" data-label="Mã hồ sơ">
                <a  class = "btn" (click)="processingDossier(row.id, row.procedure.id, row?.currentTask)">{{row.code}}</a>
              </mat-cell>
            </ng-container>

            <ng-container matColumnDef="procedure">
              <mat-header-cell *matHeaderCellDef>Thủ tục hành chính</mat-header-cell>
              <mat-cell *matCellDef="let row" data-label="Thủ tục hành chính">

                   <span *ngIf = "row.procedure?.translate?.name != null && row.procedure?.translate?.name != 'underfined'">
                      {{row.procedure?.translate?.name}}
                  </span>
                <span *ngIf = "row.procedure?.translate.length > 0">
                    {{row.procedure?.translate[0]?.name}}
                </span>
              </mat-cell>
            </ng-container>

            <ng-container matColumnDef="required">
              <mat-header-cell *matHeaderCellDef>Yêu cầu giải quyết</mat-header-cell>
              <mat-cell *matCellDef="let row" data-label="Chủ hồ sơ">
                  <span>
                      {{row?.applicant?.data?.noidungyeucaugiaiquyet}}
                  </span>
              </mat-cell>
            </ng-container>

            <ng-container matColumnDef="user">
              <mat-header-cell *matHeaderCellDef>Thực hiện</mat-header-cell>
              <mat-cell *matCellDef="let row" data-label="Thực hiện">
                  <span>
                      {{row?.currentTask[0]?.assignee?.fullname ? row?.currentTask[0]?.assignee?.fullname : ''}}
                  </span>
              </mat-cell>
            </ng-container>

            <ng-container matColumnDef="date">
              <mat-header-cell *matHeaderCellDef>Thời hạn</mat-header-cell>
              <mat-cell *matCellDef="let row" data-label="Thời hạn">
                <span>{{row.appointmentDate|date: 'dd/MM/yyyy HH:mm:ss'}}</span>
              </mat-cell>
            </ng-container>
            <ng-container matColumnDef="No1">
              <mat-header-cell *matHeaderCellDef [attr.rowspan]="9999" style="color: red" >CÔNG VIỆC ĐẾN HẠN/ QUÁ HẠN CỦA CBCC</mat-header-cell>
            </ng-container>
            <mat-header-row *matHeaderRowDef="['No1']"></mat-header-row>
            <mat-header-row *matHeaderRowDef="displayedColumns"></mat-header-row>
            <mat-row *matRowDef="let row; columns: displayedColumns;"></mat-row>
          </table>
          <div class="frm_Pagination">
            <ul class="temp_Arr">
              <li *ngFor="let item of ELEMENTDATA  | paginate: {itemsPerPage: size, currentPage: page, totalItems: countResult, id: 'pageRemind'}"></li>
            </ul>
            <div class="pageSize">
              <span i18n>Hiển thị </span>
              <mat-form-field appearance="outline">
                <mat-select [(value)]="size" (valueChange)="paginate(pageIndex, 1)">
                  <mat-option *ngFor='let opt of pgSizeOptions;' [value]="opt">{{opt}}</mat-option>
                </mat-select>
              </mat-form-field>
              <span><span i18n>trên</span> {{countResult}} <span i18n>bản ghi</span></span>
            </div>
            <div class="control">
              <pagination-controls id="pageRemind" (pageChange)="page = $event; paginate(page, 0)" responsive="true"
                                   previousLabel="" nextLabel="">
              </pagination-controls>
            </div>
          </div>
        </div>
      </mat-tab>
    </mat-tab-group>
  </div>
</div>
