<button mat-icon-button class="close-button" (click)="onDismiss()">
    <mat-icon>close</mat-icon>
</button>
<div fxLayout="row" fxLayoutAlign="center" style="margin-top: 10px;">
    <div fxFlex="grow">
      <form [formGroup]="verifyForm">
         <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="row" fxLayoutAlign="space-between" style="padding: 0px 10px 0px 10px;">
          <mat-form-field appearance="outline" fxFlex.gt-md="32" fxFlex.gt-sm="32" fxFlex.gt-xs="49.5" fxFlex='grow'>
            <mat-label>Họ và tên</mat-label>
            <input matInput formControlName="fullname">
          </mat-form-field>
          <mat-form-field appearance="outline" fxFlex.gt-md="20" fxFlex.gt-sm="20" fxFlex.gt-xs="49.5" fxFlex='grow'>
            <mat-label>CMND/CCCD</mat-label>
            <input matInput formControlName="identityNumber">
          </mat-form-field>
          <mat-form-field appearance="outline" fxFlex.gt-md="20" fxFlex.gt-sm="20" fxFlex.gt-xs="49.5" fxFlex='grow'>
            <mat-label>Ngày sinh</mat-label>
            <input matInput [matDatepicker]="pickerAcceptTo" formControlName="birthday" placeholder="dd/mm/yyyy">
            <mat-datepicker-toggle matSuffix [for]="pickerAcceptTo"></mat-datepicker-toggle>
            <mat-datepicker #pickerAcceptTo></mat-datepicker>
          </mat-form-field>
          <button (click)="onConfirm()" mat-flat-button style="background-color:#ce7a58;color:#fff;line-height: 26px;height:max-content;" fxFlex.gt-sm="17" fxFlex.gt-md="17" fxFlex.gt-xs="49.5" fxFlex='grow'
                  class="check-citizen-button" type="submit">
            <mat-icon>done_all</mat-icon>
            <span style="word-wrap:break-word;white-space:normal;">Tra cứu thông tin CD từ CSDLQG về DC</span>
          </button>
        </div>
      </form>
    </div>
</div>
<div id="dvThongTinDanCu" *ngIf="isSuccess">
    <h3 class="dialog_title" mat-dialog-title><span>Thông tin khai thác từ CSDLQG về dân cư</span></h3>
    <div mat-dialog-content class="processHandleDialogContent">
        <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="column" fxLayoutAlign="center" fxLayoutAlign="space-between" id="printDiv">
            <div class="imgProcess">
                <div>
                    <div class="card">
                        <div class="cardTitle">
                            <span class="processName">
                                <span>Thông tin cá nhân</span>
                            </span>
                        </div>
    
                        <div class="cardContent">
    
                            <div fxLayout="column">
                                <table class="table-no-border">
                                    <tbody>
                                        <tr>
                                            <td fxFlex="25%">Họ và tên: <b>{{data?.HoVaTen?.Ten}}</b></td>
                                            <td fxFlex="20%">Số định danh: <b>{{data?.SoDinhDanh}} </b></td>
                                            <td fxFlex="15%">Số CMND: <b>{{data?.SoCMND}} </b></td>
                                            <td fxFlex="15%">Ngày sinh: <b>{{data?.NgayThangNamSinh?.NgayThangNam}}</b> </td>
                                            <td fxFlex="25%">Dân tộc: <b>{{data?.DanToc?.label}}</b></td>
                                        </tr>
                                        <tr>
                                            <td fxFlex="25%">Tình trạng hôn nhân: <b>{{data?.TinhTrangHonNhan}}</b></td>
                                            <td fxFlex="20%">Giới tính: <b>{{data?.GioiTinh}}</b></td>
                                            <td fxFlex="15%">Quốc tịch: <b>{{data?.QuocTich?.label}} </b></td>
                                            <td fxFlex="15%">Tôn giáo: <b>{{data?.TonGiao?.label?data?.TonGiao?.label:"Không"}} </b></td>
                                            <td fxFlex="25%">Nhóm máu: <b>{{data?.NhomMau}}</b> </td>
    
                                        </tr>
                                        <!-- các hàng khác -->
                                        <tr>
                                            <td>
                                              <span>Nơi ở hiện tại: <b *ngIf="data?.NoiOHienTai">
                                                {{ data?.NoiOHienTai?.ChiTiet }},
                                                <ng-container *ngIf="data?.NoiOHienTai?.PhuongXa?.label"> {{ data?.NoiOHienTai?.PhuongXa?.label }},</ng-container>
                                                <ng-container *ngIf="data?.NoiOHienTai?.QuanHuyen?.label"> {{ data?.NoiOHienTai?.QuanHuyen?.label }},</ng-container>
                                                <ng-container *ngIf="data?.NoiOHienTai?.TinhThanh?.label"> {{ data?.NoiOHienTai?.TinhThanh?.label }},</ng-container>
                                                <ng-container *ngIf="data?.NoiOHienTai?.QuocGia?.label"> {{ data?.NoiOHienTai?.QuocGia?.label }}</ng-container>
                                              </b></span>
                                            </td>
                                          </tr>
                                          
                                          <tr>
                                            <td>
                                              <span>Địa chỉ thường trú: <b *ngIf="data?.ThuongTru">
                                                {{ data?.ThuongTru?.ChiTiet }},
                                                <ng-container *ngIf="data?.ThuongTru?.PhuongXa?.label"> {{ data?.ThuongTru?.PhuongXa?.label }},</ng-container>
                                                <ng-container *ngIf="data?.ThuongTru?.QuanHuyen?.label"> {{ data?.ThuongTru?.QuanHuyen?.label }},</ng-container>
                                                <ng-container *ngIf="data?.ThuongTru?.TinhThanh?.label"> {{ data?.ThuongTru?.TinhThanh?.label }},</ng-container>
                                                <ng-container *ngIf="data?.NoiDangKyKhaiSinh?.QuocGia?.label"> {{ data?.NoiDangKyKhaiSinh?.QuocGia?.label }}</ng-container>
                                              </b></span>
                                            </td>
                                          </tr>
                                          
                                          <tr>
                                            <td>
                                              <span>Nơi đăng ký khai sinh: <b *ngIf="data?.NoiDangKyKhaiSinh">
                                                {{ data?.NoiDangKyKhaiSinh?.ChiTiet }},
                                                <ng-container *ngIf="data?.NoiDangKyKhaiSinh?.PhuongXa?.label"> {{ data?.NoiDangKyKhaiSinh?.PhuongXa?.label }},</ng-container>
                                                <ng-container *ngIf="data?.NoiDangKyKhaiSinh?.QuanHuyen?.label"> {{ data?.NoiDangKyKhaiSinh?.QuanHuyen?.label }},</ng-container>
                                                <ng-container *ngIf="data?.NoiDangKyKhaiSinh?.TinhThanh?.label"> {{ data?.NoiDangKyKhaiSinh?.TinhThanh?.label }},</ng-container>
                                                <ng-container *ngIf="data?.NoiDangKyKhaiSinh?.QuocGia?.label"> {{ data?.NoiDangKyKhaiSinh?.QuocGia?.label }}</ng-container>
                                              </b></span>
                                            </td>
                                          </tr>
                                          
                                          <tr>
                                            <td>
                                              <span>Quê quán: <b *ngIf="data?.QueQuan">
                                                {{ data?.QueQuan?.ChiTiet }},
                                                <ng-container *ngIf="data?.QueQuan?.PhuongXa?.label"> {{ data?.QueQuan?.PhuongXa?.label }},</ng-container>
                                                <ng-container *ngIf="data?.QueQuan?.QuanHuyen?.label"> {{ data?.QueQuan?.QuanHuyen?.label }},</ng-container>
                                                <ng-container *ngIf="data?.QueQuan?.TinhThanh?.label"> {{ data?.QueQuan?.TinhThanh?.label }},</ng-container>
                                                <ng-container *ngIf="data?.QueQuan?.QuocGia?.label"> {{ data?.QueQuan?.QuocGia?.label }}</ng-container>
                                              </b></span>
                                            </td>
                                          </tr>
                                    </tbody>
                                </table>
                            </div>
    
                        </div>
    
    
                    </div>
                </div>
                <div>
                    <div class="card">
                        <div class="cardTitle">
                            <span class="processName">
                                <span>Thông tin gia đình</span>
                            </span>
                        </div>
                        <div class="cardContent">
                            <div fxLayout="column">
                                <table>
                                    <thead>
                                        <tr>
                                            <td fxFlex="15%">Quan Hệ</td>
                                            <td fxFlex="25%">Họ và tên</td>
                                            <td fxFlex="20%">Số định danh</td>
                                            <td fxFlex="20%">Số CMND/CCCD</td>
                                            <td fxFlex="20%">Quốc tịch</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td fxFlex="15%">Cha</td>
                                            <td fxFlex="25%">{{data?.Cha?.HoVaTen}}</td>
                                            <td fxFlex="20%">{{data?.Cha?.SoDinhDanh}}</td>
                                            <td fxFlex="20%">{{data?.Cha?.SoCMND}}</td>
                                            <td fxFlex="20%">{{data?.Cha?.QuocTich?.label}}</td>
                                        </tr>
                                        <tr>
                                            <td fxFlex="15%">Mẹ</td>
                                            <td fxFlex="25%">{{data?.Me?.HoVaTen}}</td>
                                            <td fxFlex="20%">{{data?.Me?.SoDinhDanh}}</td>
                                            <td fxFlex="20%">{{data?.Me?.SoCMND}}</td>
                                            <td fxFlex="20%">{{data?.Me?.QuocTich.label}}</td>
                                        </tr>
                                        <tr>
                                            <td fxFlex="15%">Vợ hoặc chồng</td>
                                            <td fxFlex="25%">{{data?.VoChong?.HoVaTen}}</td>
                                            <td fxFlex="20%">{{data?.VoChong?.SoDinhDanh}}</td>
                                            <td fxFlex="20%">{{data?.VoChong?.SoCMND}}</td>
                                            <td fxFlex="20%">{{data?.VoChong?.QuocTich.label}}</td>
                                        </tr>
                                        <tr>
                                            <td fxFlex="15%">Người đại diện</td>
                                            <td fxFlex="25%">{{data?.NguoiDaiDien?.HoVaTen}}</td>
                                            <td fxFlex="20%">{{data?.NguoiDaiDien?.SoDinhDanh}}</td>
                                            <td fxFlex="20%">{{data?.NguoiDaiDien?.SoCMND}}</td>
                                            <td fxFlex="20%">{{data?.NguoiDaiDien?.QuocTich.label}}</td>
                                        </tr>
    
                                        <!-- các hàng khác -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                <div>
                    <div class="card">
                        <div class="cardTitle">
                            <span class="processName">
                                <span>Thông tin hộ khẩu (chủ hộ)</span>
                            </span>
                        </div>
                        <div class="cardContent">
                            <div fxLayout="column">
                                <table>
                                    <thead>
                                        <tr>
                                            <td fxFlex="20%">Số sổ hộ khẩu</td>
                                            <td fxFlex="15%">Quan hệ</td>
                                            <td fxFlex="25%">Họ và tên</td>
                                            <td fxFlex="20%">Số định danh</td>
                                            <td fxFlex="20%">Số CMND/CCCD</td>
    
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td fxFlex="20%">{{data?.SoSoHoKhau}}</td>
                                            <td fxFlex="15%">{{data?.ChuHo?.QuanHe}}</td>
                                            <td fxFlex="25%">{{data?.ChuHo?.HoVaTen}}</td>
                                            <td fxFlex="20%">{{data?.ChuHo?.SoDinhDanh}}</td>
                                            <td fxFlex="20%">{{data?.ChuHo?.SoCMND}}</td>
    
                                        </tr>
                                        <!-- các hàng khác -->
                                    </tbody>
                                </table>
                            </div>
    
                        </div>
                    </div>
                </div>
                <div id="contentXuat" style="margin-top: 40px; margin-left: 180px;">
    
                </div>
            </div>
    
        </div>
    
    </div>
</div>
<div fxLayout="row" fxLayout.xs="column" fxLayout.sm="column" fxLayoutAlign="center">
    <button mat-flat-button class="applyBtn" (click)="onDismissAndConfirm()" *ngIf="qbhSaveInfoCitizens && isSuccess">
        <mat-icon>settings_suggest</mat-icon>
        <span>Lưu giữ thông tin CD</span>
    </button>
</div>
