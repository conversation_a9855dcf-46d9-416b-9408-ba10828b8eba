import { DatePipe } from '@angular/common';
import { Component, OnInit, Inject } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import * as ClassicEditor from '@ckeditor/ckeditor5-build-classic/build/ckeditor';
import '@ckeditor/ckeditor5-build-classic/build/translations/vi';
import { EnvService } from 'src/app/core/service/env.service';
import { DossierService } from 'src/app/data/service/dossier/dossier.service';
import { ProcessService } from 'src/app/data/service/process/process.service';
import { NotifyData } from 'src/app/data/service/notification.service';
import { DeploymentService } from 'src/app/data/service/deployment.service';
import * as tUtils from 'src/app/data/service/thoai.service';
import { StatisticsService } from 'src/app/data/service/statistics/statistics.service';
import { FormControl, FormGroup } from '@angular/forms';
import { KHADossierExtendService } from 'src/app/data/service/kha/KHADossierExtendService.service';
import { ReplaySubject, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

@Component({
  selector: 'app-transfer-processing',
  templateUrl: './transfer-processing.component.html',
  styleUrls: ['./transfer-processing.component.scss']
})
export class TransferProcessingComponent implements OnInit {
  
  config = this.envService.getConfig();
  selectedLang = localStorage.getItem('language') || 'vi';
  selectedLangId = Number(localStorage.getItem('languageId')) || 228;
  dossierId: string;
  env: any = this.deploymentService.getAppDeployment()?.env;
  consultationId: string;
  dossierCode: string;
  summary: string;
  dossierDetail: any;
  public Editor = ClassicEditor;
  addForm = new FormGroup({
    agency: new FormControl(''),
    agencyCtrl: new FormControl(''),
    position: new FormControl(''),
    assignee: new FormControl(''),
    summary: new FormControl(''),
    required: new FormControl(''),
  });
  // Task
  listPosition = [];
  searchPositionCtrl: FormControl = new FormControl();
  searchAssigneeCtrl: FormControl = new FormControl();
  public positionFiltered: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  public assigneeFiltered: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);

  timeOutSearch = null;
  msTimeOutSearch = this.deploymentService.env.statistics.msTimeOutSearch;

  // agency/assignee
  rootAgency: any;
  agencyPage = 0;
  agencyList: Array<any> = [];
  isAgencyListFull = false;
  userAgency?: any = JSON.parse(localStorage.getItem('userAgency'));
  selectedAgencyId: string;
  selectedPositionId: string;

  // listAssignee
  listAssignee = [];
  assigneeInfo = [];

  protected positions = this.listPosition;
  protected assignees = this.listAssignee;
  protected onDestroy = new Subject<void>();

  constructor(
    private dossierService: DossierService,
    private envService: EnvService,
    private processService: ProcessService,
    public datepipe: DatePipe,
    private statisticsService: StatisticsService,
    public dialogRef: MatDialogRef<TransferProcessingComponent>,
    private deploymentService: DeploymentService,
    @Inject(MAT_DIALOG_DATA) public data: TransferProcessingDialogModel,
    private khaDossierExtendService: KHADossierExtendService
  ) {
    tUtils.addUniqueToArray();
    this.dossierId = data.dossierId;
    this.consultationId = data.consultationId;
    this.dossierCode = data.dossierCode;
    this.summary = data.summary;
  }

  async ngOnInit(): Promise<void> {
    this.rootAgency =
      this.userAgency.parent.id === this.userAgency?.ancestors[this.userAgency?.ancestors.length - 1].id
        ? this.userAgency
        : this.userAgency.parent;
    this.addForm.get('summary')?.setValue(this.summary);
    await this.getDossierDetail();
    await this.loadAgencyData();
  }

  async getDossierDetail() {
    this.dossierService.getDossierDetail(this.dossierId).subscribe(async data => {
      this.dossierDetail = data;
    });
  }
  notify: NotifyData = new NotifyData();

  async loadAgencyData() {
    const agencyId = this.rootAgency.id;
    const searchString = '?page=' + this.agencyPage + '&size=10&status=1&spec=slice'
      + '&parent-id=' + agencyId;
    await this.statisticsService.getAgencyList(searchString).subscribe(data => {
      this.agencyList = data.content;
    });
  }



  getAgencyList(scroll: boolean) {
    if (!scroll) {
      this.agencyPage = 0;
      this.isAgencyListFull = false;
      this.agencyList = [];
    }
    const agencyId = this.rootAgency.id;
    const formObj = this.addForm.getRawValue();
    const searchString = '?page=' + this.agencyPage + '&size=10&status=1&spec=slice'
      + '&parent-id=' + agencyId + '&keyword=' + encodeURIComponent(formObj.agencyCtrl.trim());
    this.statisticsService.getAgencyList(searchString).subscribe(data => {
      this.agencyList = [...this.agencyList, ...data.content];
      this.agencyList = Object.values(this.agencyList.reduce((acc, cur) => Object.assign(acc, {[cur.id]: cur}), {}));
      this.agencyPage++;
      this.isAgencyListFull = data.last;
    }, err => {
      console.log(err);
    });
  }

  async getListPosition(agency) {
    let agencyId = agency.id || this.rootAgency.id;
    this.listPosition = [];

    const data1: any = await this.processService.getListPosition(0, agencyId).toPromise();
    if (!!data1) {
      const temp = data1.totalPages;
      this.listPosition = this.listPosition.concat(data1.content).unique('id');
      for (let j = 1; j < temp; j++) {
        const data2: any = await this.processService.getListPosition(j, agencyId).toPromise();
        if (!!data2) {
          this.listPosition = this.listPosition.concat(data2.content).unique('id');
        }
      }
    }
    this.positions = JSON.parse(JSON.stringify(this.listPosition).replace(/null/g, '""'));
    this.positionFiltered.next(this.positions);
    this.searchPositionCtrl.valueChanges.pipe(takeUntil(this.onDestroy)).subscribe(() => {
      this.filterPosition();
    });
  }

  protected filterPosition() {
    if (!this.positions) {
      return;
    }
    let search = this.searchPositionCtrl.value.trim();
    if (!search) {
      this.positionFiltered.next(this.positions.slice());
      return;
    } else {
      search = search.toLowerCase();
    }
    this.positionFiltered.next(
      this.positions.filter(pos => pos.name.toLowerCase().indexOf(search) > -1)
    );
  }

  async getListAssignee(agencyId, positionId) {
    this.listAssignee = [];
    let searchString1 = '?agency-id=' + agencyId + '&size=500&page=0';
    if (!!positionId) {
      searchString1 += '&position-id=' + positionId;
    }
    const data1: any = await this.processService.getListUserWithParams(searchString1).toPromise();
    if (!!data1) {
      const temp = data1.totalPages;
      this.listAssignee = this.listAssignee.concat(data1.content).unique('id');
      for (let j = 1; j < temp; j++) {
        let searchString2 = '?agency-id=' + agencyId + '&size=500&page=' + j;
        if (!!positionId) {
          searchString2 += '&position-id=' + positionId;
        }
        const data2: any = await this.processService.getListUserWithParams(searchString2).toPromise();
        if (!!data2) {
          this.listAssignee = this.listAssignee.concat(data2.content).unique('id');
        }
      }
    }
    this.assignees = JSON.parse(JSON.stringify(this.listAssignee).replace(/null/g, '""'));
    this.assigneeFiltered.next(this.assignees);
    this.searchAssigneeCtrl.valueChanges.pipe(takeUntil(this.onDestroy)).subscribe(() => {
      this.filterAssignee();
    });
  }

  protected filterAssignee() {
    if (!this.assignees) {
      return;
    }
    let search = this.searchAssigneeCtrl.value.trim();
    if (!search) {
      this.assigneeFiltered.next(this.assignees.slice());
      return;
    } else {
      search = search.toLowerCase();
    }
    this.assigneeFiltered.next(
      this.assignees.filter(assign => assign.fullname.toLowerCase().indexOf(search) > -1)
    );
  }

  onAgencyChange(agency: any) {
    this.selectedAgencyId = agency?.id || '';
    this.getListPosition(agency);
  }

  onPositionChange(position: any) {
    this.selectedPositionId = position.id;
    this.getListAssignee(this.selectedAgencyId || this.rootAgency.id, this.selectedPositionId);
  }

  keyupSearch(key: string) {
    clearTimeout(this.timeOutSearch);
    if (key === 'agency') {
      this.timeOutSearch = setTimeout(() => {
        this.getAgencyList(false);
      }, this.msTimeOutSearch);
    }
  }

  clearList(key: string) {
    if (key === 'agency') {
      this.timeOutSearch = setTimeout(() => {
        this.addForm.patchValue({
          agencyCtrl: ''
        });
        this.getAgencyList(false);
      }, this.msTimeOutSearch);
    }
  }
  // =========== ON CHANGE
  async onConfirm() {
    if (this.addForm.invalid === false) {
      const formObj = this.addForm.getRawValue();

      let body = {
        id: this.consultationId,
        assignee: formObj.assignee,
        summary: formObj.summary,
        status: 1
      };

      await this.khaDossierExtendService.updateDossierConsultation(this.dossierId, this.consultationId, body).subscribe(data => {
        if (data) {
          this.dialogRef.close(true);
        } else {
          this.dialogRef.close(false);
        }
      }, err => {
        this.dialogRef.close(false);
      });
    }
  }

  onDismiss() {
    this.dialogRef.close();
  }
}

export class TransferProcessingDialogModel {
  constructor(public dossierId: string, public dossierCode: string, public consultationId: string, public summary: string) {
  }
}
