<button mat-icon-button class="close-button" (click)="onDismiss()">
  <mat-icon>close</mat-icon>
</button>
<h3 class="dialog_title" mat-dialog-title>Tr<PERSON> kết quả hồ sơ dừng xử lý</h3>
<div mat-dialog-content class="dialog_content">
  <span>Bạn có chắc chắn muốn trả kết quả dừng xử lý hồ sơ có mã : </span
  ><span class="highlight">{{ dossierCode }}</span
  ><span>?</span>

        <div class="commentEditor" fxLayout="column" fxLayout.xs="column" fxLayout.sm="column">
        <p class="lbl"><span i18n>Lý do</span><span style="color:#ce7a58;">&nbsp;*</span></p>
        <ckeditor debounce="10" [editor]="Editor" class="customCKEditor" (change)="onCommentEditorChange($event)" [formControl]="reason"
            fxFlex='grow' [config]='commentConfig'>
        </ckeditor>
        <div class="errorMsg" *ngIf="isCKMaxlenght">
            <!-- <span i18n>Nội dung không quá 500 ký tự</span> -->
            <span>{{ckeditorMaxLengthNotification}}</span>
            <div class="err">
                <mat-icon>priority_high</mat-icon>
            </div>
        </div>
    </div>

</div>

<div
  fxLayout="row"
  fxLayout.xs="column"
  fxLayout.sm="column"
  fxLayoutAlign="center"
  fxLayoutGap="16px"
>
  <button
    mat-flat-button
    fxFlex="30"
    class="applyBtn"
    (click)="onConfirm()"
  >
    <span i18n>Đồng ý</span>
  </button>
  <button mat-flat-button fxFlex="30" class="rejectBtn" (click)="onDismiss()">
    <span>Không đồng ý</span>
  </button>
</div>
