import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { LGSPHCMLogRoutingModule } from './lgsp-hcm-log-routing.module';
import { SharedModule } from 'src/app/shared/shared.module';

import { LGSPHCMLogComponent } from './pages/lgsp-hcm-log.component';
import { LGSPHCMLogDetailComponent } from './dialogs/view-detail/view-detail.component';

@NgModule({
  declarations: [
    LGSPHCMLogComponent,
    LGSPHCMLogDetailComponent
  ],
  imports: [
    CommonModule,
    LGSPHCMLogRoutingModule,
    SharedModule
  ]
})
export class LGSPHCMLogModule { }

