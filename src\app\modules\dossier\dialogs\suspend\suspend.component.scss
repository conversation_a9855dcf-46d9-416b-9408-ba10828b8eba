.dialog_title {
    font-style: normal;
    font-weight: 500;
    font-size: 20px;
    color: #ce7a58;
}

div.a {
    text-align: center;
}

.link-center {
    margin-top: 15px;
    align-items: center;
    display: flex;
    // height: 56px;
    // background-color: pink;
}

a.link-center:hover {
    cursor: pointer;
    color: #ce7a58;
}

.center-icon {
    color: #ce7a58;
}

.center-item {
    margin-left: 10px;
    font-weight: 500;
    // width: 800px;
    // background: #5F85DB;
    // color: #fff;
    // font-weight: bold;
    // font-family: Tahoma;
}

::ng-deep {
    .dialog_content {
        font-size: 15px;
        &::-webkit-scrollbar-track {
            box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.1);
            background-color: #f5f5f5;
        }
        &::-webkit-scrollbar {
            width: 5px;
            background-color: #f5f5f5;
        }
        &::-webkit-scrollbar-thumb {
            box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.1);
            background-color: #44444450;
        }
        .highlight {
            color: #ce7a58;
        }
        .formFieldOutline {
            .mat-form-field-wrapper {
                padding-bottom: unset !important;
                color: #1e2f41;
                .mat-select-disabled {
                    .mat-select-value {
                        color: #1e2f41;
                        cursor: no-drop !important;
                    }
                }
            }
            .assigneeBlock {
                border-top: 1px solid #dadada;
                padding-top: 0.5em;
                margin-top: 0.5em;
            }
        }
        .drag_upload_btn {
            position: relative;
            overflow: hidden;
            display: inline-block;
            width: 100%;
            text-align: center;
            align-self: center;
            border: 1px solid #9e9e9e96;
            border-style: dashed;
            cursor: pointer;
            z-index: 4;
            button {
                width: 100%;
                padding: 5em;
                a {
                    color: #1E2F41;
                    text-decoration: none;
                    cursor: pointer;
                    .txtUpload {
                        text-decoration: none;
                        font-weight: 500;
                        cursor: pointer;
                        align-self: center;
                        color: #CE7A58;
                    }
                    &:hover {
                        color: #1E2F41;
                    }
                    &:visited {
                        color: #1E2F41;
                    }
                }
                .mat-icon {
                    color: #CE7A58;
                    align-self: center;
                    margin-right: .2em;
                    padding-bottom: .2em;
                }
            }
            input[type=file] {
                font-size: 100px;
                position: absolute;
                left: 0;
                top: 0;
                opacity: 0;
                height: 100%;
                cursor: pointer;
            }
        }
        .file_drag_upload_preview {
            width: 100%;
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
        }
        .list_uploaded {
            height: 2.5em;
            width: 32%;
            margin: 0 0.5% 1em 0.5%;
            background-color: #3e3e3e17;
            display: flex;
            border-radius: 4px;
            .file_icon {
                width: 2em;
                height: 2em;
                background-position: center;
                background-size: 170%;
                align-self: center;
                background-repeat: no-repeat;
            }
        }
        .btn_uploaded {
            padding: 1em !important;
        }
        .clear_file_queue {
            padding: 5em !important;
        }
        .file_uploaded {
            border: 1px solid #9e9e9e96;
            border-style: dashed;
        }
        .no_boder {
            border: none;
        }
        .file_name {
            font-style: medium;
            font-weight: normal;
            align-self: center;
            color: #1E2F41;
            float: left;
        }
        .delete_file {
            margin-left: auto;
            align-self: center;
            color: #1E2F41;
        }
        .file_control {
            width: 100%;
        }
        .res_uploadFile {
            justify-content: center;
        }
        .res_upload_btn {
            position: relative;
            overflow: hidden;
            display: inline-block;
            width: 100%;
            text-align: center;
            align-self: center;
            border: 1px solid #9e9e9e96;
            border-style: dashed;
            cursor: pointer;
            z-index: 4;
            margin-left: auto;
            margin-right: auto;
            button {
                width: 100%;
                a {
                    color: #1E2F41;
                    text-decoration: none;
                    cursor: pointer;
                    .txtUpload {
                        text-decoration: none;
                        font-weight: 500;
                        cursor: pointer;
                        align-self: center;
                        color: #CE7A58;
                    }
                }
                .mat-icon {
                    color: #CE7A58;
                    align-self: center;
                    margin-right: .2em;
                    padding-bottom: .2em;
                }
                &:hover {
                    color: #1E2F41;
                }
                &:visited {
                    color: #1E2F41;
                }
            }
            input[type=file] {
                font-size: 100px;
                position: absolute;
                left: 0;
                top: 0;
                opacity: 0;
                height: 100%;
                cursor: pointer;
            }
            .res_upload_preview {
                width: 100%;
                display: flex;
                flex-wrap: wrap;
                justify-content: center;
                padding-top: 1em;
                .list_uploaded {
                    height: 2.5em;
                    margin: 0 0.5% 1em 0.5%;
                    background-color: #3e3e3e17;
                    display: flex;
                    border-radius: 4px;
                    .file_icon {
                        width: 2em;
                        height: 2em;
                        background-position: center;
                        background-size: 170%;
                        align-self: center;
                        background-repeat: no-repeat;
                    }
                }
            }
        }
        .addForm {
            .formFieldItems {
                flex-wrap: wrap;
                .showHideOption {
                    display: flex;
                    margin: 1em 0 1.5em 0;
                    flex-wrap: wrap;
                    .cbx_default {
                        align-self: center;
                        padding-right: 2em;
                    }
                    .mat-checkbox-indeterminate {
                        &.mat-accent {
                            .mat-checkbox-background {
                                background-color: #ce7a58;
                            }
                        }
                    }
                    .mat-checkbox-checked {
                        &.mat-accent {
                            .mat-checkbox-background {
                                background-color: #ce7a58;
                            }
                        }
                    }
                }
            }
            .prc_AgencyAutocomplete {
                .mat-option-text {
                    font-size: 14px !important;
                }
            }
            .drag_upload_btn {
                position: relative;
                overflow: hidden;
                display: inline-block;
                width: 100%;
                text-align: center;
                align-self: center;
                border: 1px solid #9e9e9e96;
                border-style: dashed;
                cursor: pointer;
                z-index: 4;
                button {
                    width: 100%;
                    padding: 5em;
                    a {
                        color: #1E2F41;
                        text-decoration: none;
                        cursor: pointer;
                        .txtUpload {
                            text-decoration: none;
                            font-weight: 500;
                            cursor: pointer;
                            align-self: center;
                            color: #CE7A58;
                        }
                        &:hover {
                            color: #1E2F41;
                        }
                        &:visited {
                            color: #1E2F41;
                        }
                    }
                    .mat-icon {
                        color: #CE7A58;
                        align-self: center;
                        margin-right: .2em;
                        padding-bottom: .2em;
                    }
                }
                input[type=file] {
                    font-size: 100px;
                    position: absolute;
                    left: 0;
                    top: 0;
                    opacity: 0;
                    height: 100%;
                    cursor: pointer;
                }
            }
            .file_drag_upload_preview {
                width: 100%;
                display: flex;
                flex-wrap: wrap;
                justify-content: center;
            }
            .list_uploaded {
                height: 2.5em;
                width: 32%;
                margin: 0 0.5% 1em 0.5%;
                background-color: #3e3e3e17;
                display: flex;
                border-radius: 4px;
                .file_icon {
                    width: 2em;
                    height: 2em;
                    background-position: center;
                    background-size: 170%;
                    align-self: center;
                    background-repeat: no-repeat;
                }
            }
            .btn_uploaded {
                padding: 1em !important;
            }
            .clear_file_queue {
                padding: 5em !important;
            }
            .file_uploaded {
                border: 1px solid #9e9e9e96;
                border-style: dashed;
            }
            .no_boder {
                border: none;
            }
            .file_name {
                font-style: medium;
                font-weight: normal;
                align-self: center;
                color: #1E2F41;
                float: left;
            }
            .delete_file {
                margin-left: auto;
                align-self: center;
                color: #1E2F41;
            }
            .file_control {
                width: 100%;
            }
            .res_uploadFile {
                justify-content: center;
            }
            .res_upload_btn {
                position: relative;
                overflow: hidden;
                display: inline-block;
                width: 100%;
                text-align: center;
                align-self: center;
                border: 1px solid #9e9e9e96;
                border-style: dashed;
                cursor: pointer;
                z-index: 4;
                margin-left: auto;
                margin-right: auto;
                button {
                    width: 100%;
                    a {
                        color: #1E2F41;
                        text-decoration: none;
                        cursor: pointer;
                        .txtUpload {
                            text-decoration: none;
                            font-weight: 500;
                            cursor: pointer;
                            align-self: center;
                            color: #CE7A58;
                        }
                    }
                    .mat-icon {
                        color: #CE7A58;
                        align-self: center;
                        margin-right: .2em;
                        padding-bottom: .2em;
                    }
                    &:hover {
                        color: #1E2F41;
                    }
                    &:visited {
                        color: #1E2F41;
                    }
                }
                input[type=file] {
                    font-size: 100px;
                    position: absolute;
                    left: 0;
                    top: 0;
                    opacity: 0;
                    height: 100%;
                    cursor: pointer;
                }
                .res_upload_preview {
                    width: 100%;
                    display: flex;
                    flex-wrap: wrap;
                    justify-content: center;
                    padding-top: 1em;
                    .list_uploaded {
                        height: 2.5em;
                        margin: 0 0.5% 1em 0.5%;
                        background-color: #3e3e3e17;
                        display: flex;
                        border-radius: 4px;
                        .file_icon {
                            width: 2em;
                            height: 2em;
                            background-position: center;
                            background-size: 170%;
                            align-self: center;
                            background-repeat: no-repeat;
                        }
                    }
                }
            }
        }
        .error_Msg {
            font-size: 12px !important;
            float: right;
            display: flex;
            color: #ce7a58;
            .err {
                background-color: #f2a63494;
                border-radius: 50%;
                width: 1.2em;
                height: 1.2em;
                justify-content: center;
                display: flex;
                margin-left: .5em;
                .mat-icon {
                    color: #ce7a58;
                    vertical-align: middle;
                    transform: scale(0.8);
                }
            }
        }
        .error_MsgCustom {
            font-size: 12px !important;
            display: flex;
            color: #ce7a58;
            margin-top: .5em;
            span {
                margin-left: auto;
                align-self: center;
            }
            .err {
                background-color: #f2a63494;
                border-radius: 50%;
                width: 1.2em;
                height: 1.2em;
                justify-content: center;
                display: flex;
                margin-left: .5em;
                align-self: center;
                .mat-icon {
                    color: #ce7a58;
                    vertical-align: middle;
                    align-self: center;
                    transform: scale(0.5);
                    justify-content: center;
                    margin-left: .05em;
                }
            }
        }
        .advanceBtn {
            color: #ce7a58;
            padding: .2em;
        }
        .multiRow {
            margin-bottom: 1em;
            .head {
                display: flex;
                padding: .2em .5em;
                background-color: #F4EADF;
                border-radius: 5px 5px 0 0;
                &.hiddenBtn {
                    padding: .75em .5em !important;
                }
                .title {
                    color: #1E2F41;
                    align-self: center;
                }
                .btn_add {
                    margin-left: auto;
                    color: #CE7A58;
                    align-self: center;
                    .mat-icon {
                        vertical-align: middle;
                    }
                }
            }
            .main_content {
                padding: 1em 0 1em .5em;
                margin: 0;
                border: 1px solid rgba(0, 0, 0, 0.08);
                border-top: none;
                border-radius: 0 0 5px 5px;
                .items {
                    display: flex;
                    padding: .5em 0;
                    .formField {
                        padding: .5em;
                        box-shadow: 0px 1px 15px rgba(69, 65, 78, 0.19);
                        flex-wrap: wrap;
                        .mat-form-field-wrapper {
                            padding-bottom: 0 !important;
                        }
                    }
                    .btn_delete {
                        align-self: center;
                        color: #CE7A58;
                    }
                }
            }
        }
    }
    .close-button {
        float: right;
        top: -24px;
        right: -24px;
    }
    .applyBtn {
        margin-top: 1em;
        background-color: #ce7a58;
        color: #fff;
        height: 3em;
    }
    .commentEditor {
        margin: 1em 0;
        .lbl {
            font-weight: 500;
            font-size: 16px;
            line-height: 27px;
            color: #1e2f41;
        }
        .editorLabel {
            font-weight: 500;
            color: #1e2f41;
            margin-bottom: 0.2em;
        }
        .customCKEditor {
            .ck.ck-toolbar {
                border-radius: 4px 4px 0 0;
                border: 1px solid #dedede;
            }
            .ck.ck-content {
                background-color: #eaebeb;
                border: none;
                border-radius: 0 0 4px 4px;
            }
            .ck-editor__editable {
                min-height: 5em !important;
            }
        }
        .chkGroup {
            margin: 1em 0;
            display: flex;
            flex-direction: column;
            .mat-checkbox-indeterminate.mat-accent .mat-checkbox-background,
            .mat-checkbox-checked.mat-accent .mat-checkbox-background {
                background-color: #ce7a58;
            }
        }
        .errorMsg {
            font-size: 12px !important;
            float: right;
            display: flex;
            color: #ce7a58;
            margin: .5em 0;
            span {
                margin-left: auto !important;
            }
            .err {
                background-color: #f2a63494;
                border-radius: 50%;
                width: 1.2em;
                height: 1.2em;
                justify-content: center;
                display: flex;
                margin-left: 0.5em;
                margin-top: 0.2em;
                .mat-icon {
                    color: #ce7a58;
                    vertical-align: middle;
                    align-self: center;
                    transform: scale(0.6);
                    margin-left: .05em;
                }
            }
        }
    }
    .sendEmailSMS {
        border-top: 1px solid #dadada;
        .chkGroup {
            margin: 1em 0;
            display: flex;
            flex-direction: column;
            .mat-checkbox-indeterminate.mat-accent .mat-checkbox-background,
            .mat-checkbox-checked.mat-accent .mat-checkbox-background {
                background-color: #ce7a58;
            }
        }
        .mat-form-field {
            width: 100%;
            .textLimit {
                float: right;
                padding-top: 0.2em;
            }
        }
    }
    .updateForm {
        .formFieldItems {
            flex-wrap: wrap;
            .showHideOption {
                display: flex;
                margin: 1em 0 1.5em 0;
                flex-wrap: wrap;
                .cbx_default {
                    align-self: center;
                    padding-right: 2em;
                }
                .mat-checkbox-indeterminate {
                    &.mat-accent {
                        .mat-checkbox-background {
                            background-color: #ce7a58;
                        }
                    }
                }
                .mat-checkbox-checked {
                    &.mat-accent {
                        .mat-checkbox-background {
                            background-color: #ce7a58;
                        }
                    }
                }
            }
        }
    }
    .err_checkExtend {
        color: #ce7a58;
        font-size: 15px;
        font-style: italic;
        padding-top: 0.5em;
    }
}