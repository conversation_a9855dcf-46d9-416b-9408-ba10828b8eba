import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { DlkBaocaoChithi25ToanSoRoutingModule } from './dlk-baocao-chithi25-toanso-routing.module';
import { DlkBaocaoChithi25ToanSoComponent } from './dlk-baocao-chithi25-toanso.component';
import { SharedModule } from 'src/app/shared/shared.module';
import { NgxPrinterModule } from 'ngx-printer';
import {DossierDetailComponent} from '../dialogs/view-detail.component';


@NgModule({
  declarations: [DlkBaocaoChithi25ToanSoComponent],
  imports: [
    CommonModule,
    DlkBaocaoChithi25ToanSoRoutingModule,    
    SharedModule,    
    NgxPrinterModule.forRoot({ printOpenWindow: true })
  ]
})
export class DlkBaocaoChithi25ToanSoModule { }
