import {Injectable} from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {ApiProviderService} from 'core/service/api-provider.service';
import {Observable} from 'rxjs';
import {DeploymentService} from "data/service/deployment.service";

@Injectable({
  providedIn: 'root'
})
export class DocumentTypeService  {
  env = this.deploymentService.getAppDeployment()?.env;
  private basedocUrl = this.env?.OS_TGG?.ioffice?.root_url_basedoc !== undefined ? this.env?.OS_TGG?.ioffice?.root_url_basedoc : '';

  constructor(
    private http: HttpClient,
    private deploymentService: DeploymentService,
  ) {
  }


  // Lấy danh sách loại văn bản
  getDocTypeByName(searchStr): Observable<any> {
    return this.http.get(this.basedocUrl + '/document-type/name' + searchStr);
  }

}
