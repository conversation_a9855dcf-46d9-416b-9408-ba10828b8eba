import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { ApiProviderService } from 'src/app/core/service/api-provider.service';
import { Observable } from 'rxjs';
import { DossierService } from './dossier/dossier.service';

@Injectable({
  providedIn: 'root'
})
export class CommonService {

  constructor(
    private http: HttpClient,
    private apiProviderService: ApiProviderService,
    private dossierService: DossierService,
  ) { }

  private adapter = this.apiProviderService.getUrl('digo', 'adapter');
  private basecat = this.apiProviderService.getUrl('digo', 'basecat');
  private basedata = this.apiProviderService.getUrl('digo', 'basedata');
  private basepad = this.apiProviderService.getUrl('digo', 'basepad');
  private bpm = this.apiProviderService.getUrl('digo', 'bpm');
  private fileman = this.apiProviderService.getUrl('digo', 'fileman');
  private padman = this.apiProviderService.getUrl('digo', 'padman');
  private reporter = this.apiProviderService.getUrl('digo', 'reporter');
  private surfeed = this.apiProviderService.getUrl('digo', 'surfeed');

  getListProcessDefinitionTask(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    switch (localStorage.getItem('isLoggedIn')) {
      case 'false':
        const token = localStorage.getItem('OAuth2TOKEN');
        headers = headers.append('Authorization', 'Bearer ' + token);
        return this.http.get(this.bpm + '/process-definition-task?process-definition-id=' + id + '&size=50', { headers }).pipe();
      case 'true':
        return this.http.get(this.bpm + '/process-definition-task?process-definition-id=' + id + '&size=50', { headers }).pipe();
    }
  }

  // svc-padman
  async getProcostDossiers(dossierIds) {
    return new Promise<Array<any>>(resolve => {
      this.dossierService.getDossierFeeByDossiers(dossierIds).subscribe(data => {
        resolve(data);
      });
    });
  }
  // svc-padman

}
