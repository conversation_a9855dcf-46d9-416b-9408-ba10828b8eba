import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { DlkBaoCaoSoHoaRoutingModule } from './dlk-baocao-sohoa-routing.module';
import { DlkBaoCaoSoHoaComponent } from './dlk-baocao-sohoa.component';
import { SharedModule } from 'src/app/shared/shared.module';
import { DlkChiTietBaoCaoSoHoaComponent } from './dlk-chitiet-baocao-sohoa/dlk-chitiet-baocao-sohoa.component';


@NgModule({
  declarations: [DlkBaoCaoSoHoaComponent, DlkChiTietBaoCaoSoHoaComponent],
  imports: [
    CommonModule,
    DlkBaoCaoSoHoaRoutingModule,
    SharedModule
  ]
})
export class DlkBaoCaoSoHoaModule { }
