import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { ApiProviderService } from 'src/app/core/service/api-provider.service';
import { Observable } from 'rxjs';
import { Title } from '@angular/platform-browser';
import { MatSidenav } from '@angular/material/sidenav';
import { EnvService } from 'src/app/core/service/env.service';

@Injectable({
  providedIn: 'root'
})
export class MainService {

  config = this.envService.getConfig();
  public sideNav: MatSidenav;
  constructor(
    private http: HttpClient,
    private apiProviderService: ApiProviderService,
    private titleService: Title,
    private envService: EnvService,
  ) { }

  private filePath = this.apiProviderService.getUrl('digo', 'fileman') + '/file/';
  private padmanPath = this.apiProviderService.getUrl('digo', 'padman') + '/dossier/';
  private basecatPath = this.apiProviderService.getUrl('digo', 'basecat');
  private dossierURL = this.apiProviderService.getUrl('digo', 'padman') + '/dossier/';
  private sysman = this.apiProviderService.getUrl('digo', 'sysman');
  private bpmPath = this.apiProviderService.getUrl('digo', 'bpm');
  // tslint:disable-next-line: no-string-literal
  private sysmanCloud = this.config.apiCloudURL + '/' + this.config.apiProviders['digo'].services['sysman'].path;

  getFile(id: string): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.append('Content-Type', 'application/json');
    return this.http.get(this.filePath + id, { headers, responseType: 'blob'});
  }

  getDossierRemind(search): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.append('Content-Type', 'application/json');    
    return this.http.get(this.dossierURL + 'remind' + search, { headers });
  }

  getNameDossierRemind(search): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.append('Content-Type', 'application/json');
    return this.http.get(this.basecatPath + '/tag/--by-category-id' + search, { headers });
  }

  setPageTitle(title) {
    this.titleService.setTitle(title);
  }

  generateAddress(placeObj) {
    let address = '';
    const langId = Number(localStorage.getItem('languageId'));
    const lang = localStorage.getItem('language');
    if (lang === 'vi') {
      if (placeObj !== undefined && placeObj !== null && placeObj.id !== undefined) {
        if (placeObj.type !== undefined && placeObj.type !== null) {
          if (placeObj.type.name !== undefined && placeObj.type.name !== null) {
            if (placeObj.type.name.filter(commune => commune.languageId === langId).length > 0) {
              address += placeObj.type.name.filter(commune => commune.languageId === langId)[0].name;
            }
          }
        }
        if (placeObj.name !== undefined && placeObj.name !== null) {
          address += ' ' + placeObj.name;
        }
        if (placeObj.ancestors !== undefined && placeObj.ancestors !== null && placeObj.ancestors.length > 0) {
          placeObj.ancestors.forEach(ancestor => {
            if (ancestor.type !== undefined && ancestor.type !== null) {
              if (ancestor.type.name !== undefined && ancestor.type.name !== null) {
                if (ancestor.type.name.filter(district => district.languageId === langId).length > 0) {
                  address += ', ' + ancestor.type.name.filter(district => district.languageId === langId)[0].name;
                }
              }
            }
            if (ancestor.name !== undefined && ancestor.name !== null) {
              address += ' ' + ancestor.name;
            }
          });
        }
        if (placeObj.nation !== undefined && placeObj.nation !== null) {
          address += ', ' + placeObj.nation.name;
        }
        return address;
      } else {
        return address;
      }
    } else {
      if (placeObj !== undefined && placeObj !== null && placeObj.id !== undefined) {
        if (placeObj.name !== undefined && placeObj.name !== null) {
          address += placeObj.name;
        }
        if (placeObj.type !== undefined && placeObj.type !== null) {
          if (placeObj.type.name !== undefined && placeObj.type.name !== null) {
            if (placeObj.type.name.filter(commune => commune.languageId === langId).length > 0) {
              address += ' ' + placeObj.type.name.filter(commune => commune.languageId === langId)[0].name;
            }
          }
        }
        if (placeObj.ancestors !== undefined && placeObj.ancestors !== null && placeObj.ancestors.length > 0) {
          placeObj.ancestors.forEach(ancestor => {
            if (ancestor.name !== undefined && ancestor.name !== null) {
              address += ', ' + ancestor.name;
            }
            if (ancestor.type !== undefined && ancestor.type !== null) {
              if (ancestor.type.name !== undefined && ancestor.type.name !== null) {
                if (ancestor.type.name.filter(district => district.languageId === langId).length > 0) {
                  address += ' ' + ancestor.type.name.filter(district => district.languageId === langId)[0].name;
                }
              }
            }
          });
        }
        if (placeObj.nation !== undefined && placeObj.nation !== null) {
          address += ', ' + placeObj.nation.name;
        }
        return address;
      } else {
        return address;
      }
    }
  }

  getNameDefinationTask(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.append('Content-Type', 'application/json');
    return this.http.get(this.bpmPath + '/process-definition-task/' + id, { headers });
  }

  getListAccountApps(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.append('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.sysman + '/account-app/--by-account-id' + searchString, { headers });
  }

  getReminderMenu(id): Promise<any> {
    let headers = new HttpHeaders();
    headers = headers.append('Content-Type', 'application/json');
    let reminderMenu = [];
    return this.http.get(this.basecatPath + `/tag/${id}`, { headers }).toPromise();
  }

}
