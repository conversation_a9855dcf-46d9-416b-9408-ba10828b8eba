import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SharedModule } from 'src/app/shared/shared.module';
import { ListReceiptBookRoutingModule } from './list-receipt-book-routing.module';
import { ListReceiptBookComponent } from './pages/list-receipt-book/list-receipt-book.component';
import { ReceiptBookDialogComponent } from './dialogs/receipt-book-dialog/receipt-book-dialog.component';
import { AddReceiptBookAgencyComponent } from './dialogs/add-receipt-book-agency/add-receipt-book-agency.component';
import { DeleteReceiptBookComponent } from './dialogs/delete-receipt-book/delete-receipt-book.component';
import { CloneReceiptBookComponent } from './dialogs/clone-receipt-book/clone-receipt-book.component';

@NgModule({
  declarations: [
    ListReceiptBookComponent,
    ReceiptBookDialogComponent,
    AddReceiptBookAgencyComponent,
    DeleteReceiptBookComponent,
    CloneReceiptBookComponent
  ],
  imports: [
    CommonModule,
    ListReceiptBookRoutingModule,
    SharedModule
  ]
})
export class ListReceiptBookModule { }
