import { rejects } from 'assert';
import { resolve } from 'dns';
import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { ApiProviderService } from 'src/app/core/service/api-provider.service';
import { Observable } from 'rxjs';
import { IFormMap } from '../../schema/form-map';

@Injectable({
  providedIn: 'root'
})
export class BankService {

  constructor(
    private http: HttpClient,
    private apiProviderService: ApiProviderService
  ) { }

  private basepad = this.apiProviderService.getUrl('digo', 'basepad');
  private formPath = this.apiProviderService.getUrl('digo', 'basepad') + '/form/';
  private bankPath = this.apiProviderService.getUrl('digo', 'basepad') + '/bank/';
  private adapter = this.apiProviderService.getUrl('digo', 'adapter');

  getLgspSyncForm(agencyCode, syncCode): Observable<any> {
    return this.http.get(this.adapter + `/lgsp-thanhPhanHoSo/--dongBoDanhMuc?agency-code=${agencyCode}&syncCode=${syncCode}`); 
  }

  getSyncStatus(code): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    const url = this.basepad + '/sync-form-status/--get-by-code?code=' + code;
    return this.http.get<any>(url, { headers });
  }

  getSyncTotalElements(agencyCode?:string): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    const url = this.adapter + `/lgsp-thanhPhanHoSo/--syncTotalElements?agency-code=${agencyCode}`;
    return this.http.get<any>(url, { headers });
  }

  getListBank(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.bankPath + searchString, { headers });
  }

  removeMapped(formId?:string):Observable<any>{
    let URL = `${this.formPath}${formId}/--remove-map`;
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.put<any>(URL,{ headers });
  }

  postNewBank(requestBody) {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    return this.http.post<any>(this.bankPath, requestBody, { headers });
  }

  getBankInfo(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.bankPath + id, { headers });
  }

  getFormInfoPromise(id): Promise<any> {
    return new Promise((resolve, rejects)=>{
      let headers = new HttpHeaders();
      headers = headers.set('Accept-Language', localStorage.getItem('language'));
      this.http.get(this.formPath + id, { headers }).subscribe(rs=>{
        resolve(rs);
      });
    });
  }

  putUpdateBank(id, requestBody) {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.put<any>(this.bankPath + id, requestBody, { headers });
  }

  deleteBank(id: string) {
    let headers = new HttpHeaders();
    headers = headers.append('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.delete(this.bankPath + id, { headers });
  }

  importData(file: File): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    let formData: FormData = new FormData();
    formData.append("file",file);
    return this.http.post(this.bankPath + "--import-excell", formData, { headers });
  }
}
