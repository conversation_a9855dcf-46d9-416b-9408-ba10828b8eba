import { Component, OnInit, Inject } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { KeycloakService } from 'keycloak-angular';
import { DossierService } from 'src/app/data/service/dossier/dossier.service';
import { UserService } from 'src/app/data/service/user.service';
import * as ClassicEditor from '@ckeditor/ckeditor5-build-classic/build/ckeditor';
import '@ckeditor/ckeditor5-build-classic/build/translations/vi';
import { EnvService } from 'src/app/core/service/env.service';
import { SnackbarService } from 'src/app/data/service/snackbar/snackbar.service';
import { NotificationService } from 'src/app/data/service/notification.service';
import { DeploymentService } from 'src/app/data/service/deployment.service';
import { PadmanService } from 'src/app/data/service/svc-padman/padman.service';
import * as tUtils from 'src/app/data/service/thoai.service';
import { ProcedureService } from 'src/app/data/service/procedure/procedure.service';
import { AgencyService } from 'src/app/data/service/basedata/agency.service';

@Component({
  selector: 'app-resume-processing',
  templateUrl: './resume-processing.component.html',
  styleUrls: ['./resume-processing.component.scss']
})
export class ResumeProcessingComponent implements OnInit {

  config = this.envService.getConfig();
  env = this.deploymentService.getAppDeployment()?.env;
  selectedLang = localStorage.getItem('language') || 'vi';
  selectedLangId = Number(localStorage.getItem('languageId')) || 228;
  dossierId: string;
  commentContent = '';
  userName: string;
  accountId: string;
  dossierCode: string;
  isCKMaxlenght = false;

  public Editor = ClassicEditor;
  commentConfig = {
    placeholder: 'Nhập nội dung...',
    language: 'vi',
    removePlugins: ['MediaEmbed', 'ImageUpload'],
    toolbarLocation: 'bottom'
  };
  dossierTaskStatus = { id: '', name: [] };
  dossierMenuTaskRemind = { id: '', name: [] };
  dossierDetail: any;
  oldstatus = '';

  totalCost = '';

  ckeditorMaxLengthNotification = "";
  ckeditorMaxLength = this.deploymentService.env.OS_HCM.ckeditorMaxLength;
  changeAppointmentDateWhenResume = this.deploymentService.env.OS_HCM.changeAppointmentDateWhenResume.enable;
  enableSmsResumeDossier = this.deploymentService.env?.OS_KHA?.enableSmsResumeDossier || false;
  changeAppointmentDateWhenResumeAgencyList = this.deploymentService.env.OS_HCM.changeAppointmentDateWhenResume.agencyList;
  resetProcressTimeCurrentTask = this.deploymentService.env.OS_HCM.resetProcressTimeCurrentTask ? this.deploymentService.env.OS_HCM.resetProcressTimeCurrentTask : 
  {
        turnOn : false,
        agencyList : []
  };
  agencyAccept = false;
  listAgencyResetTimeWhenResume : any[];
  isSmsQNM = this.env?.OS_QNM?.isSmsQNM ? true : false;
  //IGATESUPP-103472 -	QTI
  sumPauseProcessingTimeDueDate = this.deploymentService.getAppDeployment()?.addTimeForProcessing == 1 ? true: false;
  disableButton = false;
  constructor(
    private userService: UserService,
    private envService: EnvService,
    private keycloakService: KeycloakService,
    public dialogRef: MatDialogRef<ResumeProcessingComponent>,
    @Inject(MAT_DIALOG_DATA) public data: ResumeProcessingModel,
    private snackbarService: SnackbarService,
    private notiService: NotificationService,
    private deploymentService: DeploymentService,
    private dossierService: DossierService,
    private procedureService: ProcedureService,
    private padmanService: PadmanService,
    private agencyService: AgencyService
  ) {
    this.dossierId = data.dossierId;
    this.dossierCode = data.dossierCode;
  }

  ngOnInit(): void {
    this.getUserAccount();
    this.getDossierDetail();
    this.checkAgencyResetProcessingTime();
    if (this.selectedLang === 'vi') {
      this.ckeditorMaxLengthNotification = "Nội dung không quá 500 ký tự!";
      if (this.ckeditorMaxLength > 0)
        this.ckeditorMaxLengthNotification = "Nội dung không quá "+ this.ckeditorMaxLength + " ký tự!";
    } else if (this.selectedLang === 'en') {
      this.ckeditorMaxLengthNotification = "Content must not exceed 500 characters!";
      if (this.ckeditorMaxLength > 0)
        this.ckeditorMaxLengthNotification = "Content must not exceed "+ this.ckeditorMaxLength + " characters!";
    }
  }
  async getDossierDetail() {
    await this.getDossierFeeNotify();
    this.dossierService.getDossierDetail(this.dossierId).subscribe(data => {
      this.dossierDetail = data;

      for (const task of this.dossierDetail.task) {
        if (task.isCurrent === 1) {
          this.dossierTaskStatus.id = task.bpmProcessDefinitionTask.name.id;
          this.dossierTaskStatus.name = [{
            languageId: Number(localStorage.getItem('languageId')),
            name: task.bpmProcessDefinitionTask.name.name
          }];
          if (task.bpmProcessDefinitionTask.remind !== undefined && task.bpmProcessDefinitionTask.remind !== null){
            this.dossierMenuTaskRemind.id = task.bpmProcessDefinitionTask.remind.id;
            this.dossierMenuTaskRemind.name = [{
              languageId: Number(localStorage.getItem('languageId')),
              name: task.bpmProcessDefinitionTask.remind.name
            }];
          }
        }
      }

      if (this.dossierDetail.dossierTaskStatus === undefined && this.dossierDetail.dossierTaskStatus === null) {
        // this.putDossierStatus();
      }
      else{
        this.oldstatus = this.dossierDetail.dossierTaskStatus.name;
      }

      if(this.isSmsQNM) {
        const agencies = this.agencyService.getAgencies();
        const extend = {
          dossier:{
            id: this.dossierId,
            code: this.dossierCode,
          },
          agencies: agencies
        };
        this.notiService.checkSendSubject.next(
          {
            id: data?.procedureProcessDefinition?.id,
            phone: data?.applicant?.data?.phoneNumber,
            email: data?.applicant?.data?.email,
            contentParams: {
              parameters: {
                sector: !!data?.procedure?.sector?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name ? data?.procedure?.sector?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name : '',
                procedure: !!data?.procedure?.translate?.find(agency => agency.languageId === Number(this.selectedLangId))?.name ? data?.procedure?.translate?.find(agency => agency.languageId === Number(this.selectedLangId))?.name : '',
                agency: !!data?.agency?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name ? data?.agency?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name : '',
                applicantFullname: data?.applicant?.data?.fullname,
                officerFullname: '',
                subsystem: !!this.env?.subsystem?.name ? this.env?.subsystem?.name : "Hệ thống Một cửa điện tử",
                dossierCode: data?.code,
                nextTask: !!this.env?.notify?.resumeDossier?.nextTask ? this.env?.notify?.resumeDossier?.nextTask : 'Tiếp tục xử lý',
                dossierSearchPadsvcUrl: (!!this.env?.domain?.padsvc ? this.env?.domain?.padsvc : "http://padsvctest.vncitizens.vn/vi/") + "dossier/public",
                dossierDetailPadsvcUrl: (!!this.env?.domain?.padsvc ? this.env?.domain?.padsvc : "http://padsvctest.vncitizens.vn/vi/") + "dossier/detail/" + this.dossierId,
                dossierSearchUrl: '',
                dossierDetailUrl: '',
                returnMethod: !!data?.dossierReceivingKind?.name?.find(recieve => recieve.languageId === Number(this.selectedLangId))?.name ? data?.dossierReceivingKind?.name?.find(recieve => recieve.languageId === Number(this.selectedLangId))?.name : '',
                receivingDate: '',
                appointmentDate: '',
                dossierFee: this.totalCost,
                reason: '',
                extend: extend
              }
            }
          }
        );
      }
      else {
        this.notiService.checkSendSubject.next(
          {
            id: data?.procedureProcessDefinition?.id,
            phone: data?.applicant?.data?.phoneNumber,
            email: data?.applicant?.data?.email,
            contentParams: {
              parameters: {
                sector: !!data?.procedure?.sector?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name ? data?.procedure?.sector?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name : '',
                procedure: !!data?.procedure?.translate?.find(agency => agency.languageId === Number(this.selectedLangId))?.name ? data?.procedure?.translate?.find(agency => agency.languageId === Number(this.selectedLangId))?.name : '',
                agency: !!data?.agency?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name ? data?.agency?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name : '',
                applicantFullname: data?.applicant?.data?.fullname,
                officerFullname: '',
                subsystem: !!this.env?.subsystem?.name ? this.env?.subsystem?.name : "Hệ thống Một cửa điện tử",
                dossierCode: data?.code,
                nextTask: !!this.env?.notify?.resumeDossier?.nextTask ? this.env?.notify?.resumeDossier?.nextTask : 'Tiếp tục xử lý',
                // IGATESUPP-63184
                nextStatus: !!this.env?.notify?.resumeDossier?.nextStatus ? this.env?.notify?.resumeDossier?.nextStatus : (data?.dossierTaskStatus?.name ?? ''), // cho bo sung, trang thai hien tai cua ho so
                dossierStatus: !!this.env?.notify?.resumeDossier?.dossierStatus ? this.env?.notify?.resumeDossier?.dossierStatus : (data?.dossierStatus?.name?.find(t => t.languageId === Number(this.selectedLangId))?.name ?? ''), // cho bo sung, trang thai hien tai cua ho so
                dossierSearchPadsvcUrl: (!!this.env?.domain?.padsvc ? this.env?.domain?.padsvc : "http://padsvctest.vncitizens.vn/vi/") + "dossier/public",
                dossierDetailPadsvcUrl: (!!this.env?.domain?.padsvc ? this.env?.domain?.padsvc : "http://padsvctest.vncitizens.vn/vi/") + "dossier/detail/" + this.dossierId,
                dossierSearchUrl: '',
                dossierDetailUrl: '',
                returnMethod: !!data?.dossierReceivingKind?.name?.find(recieve => recieve.languageId === Number(this.selectedLangId))?.name ? data?.dossierReceivingKind?.name?.find(recieve => recieve.languageId === Number(this.selectedLangId))?.name : '',
                receivingDate: '',
                appointmentDate: '',
                dossierFee: this.totalCost,
                reason: ''
              }
            }
          }
        );
      }
    });
  }

  getDossierFeeNotify() {
    return new Promise<void>(resolve => {
      this.padmanService.getDossierFee(this.dossierId).toPromise().then(res => {
        if(res && res.length > 0)
        {
          let totalCostValue = 0;
          let totalPaidValue = 0;
          res.forEach(element => {
            totalCostValue += element.quantity * element.amount;
            totalPaidValue += element.paid;
          });
          const monetaryUnit = res[0]?.monetaryUnit ? res[0].monetaryUnit : 'VNĐ';
          let total = totalCostValue - totalPaidValue;
          this.totalCost = total > 0 ? Number((total).toFixed(1)).toLocaleString() + ' ' + monetaryUnit : '';
        }
        resolve();
      }).catch(err => {
        resolve();
      });
    });
  }

  putDossierStatus() {
    const requestBodyObj = [
      {
        id: this.dossierId,
        dossierStatus: 2,
        dossierTaskStatus: null
      }
    ];
    const requestBody = JSON.stringify(requestBodyObj, null, 2);
    this.dossierService.putDossierStatus(requestBody).subscribe(data => {
      if (data.affectedRows === 1) {
        this.getDossierDetail();
      }
    });
  }

  postHistory(){
    let newStatus = '';
    if (this.dossierTaskStatus.name.length > 0){
      newStatus = this.dossierTaskStatus.name[0].name;
      this.dossierTaskStatus.name.forEach(element => {
        if (element.languageId === Number(localStorage.getItem('languageId'))){
          newStatus = element.name;
        }
      });
    }
    const content = {
      groupId: 1,
      itemId: this.dossierId,
      user: {
        id: this.accountId,
        name: this.userName
      },
      type: 3,
      deploymentId: this.config.deploymentId,
      action: [
        {
          fieldNameRbk: 'lang.word.status',
          originalValue: this.oldstatus,
          newValue: newStatus
        }
      ]
    };
    this.dossierService.postHistory(JSON.stringify(content, null, 2)).subscribe(data => {

    });
  }
  getUserAccount() {
    this.keycloakService.loadUserProfile().then(user => {
      // tslint:disable-next-line: no-string-literal
      this.accountId = user['attributes'].user_id[0];
      this.userService.getUserInfo(this.accountId).subscribe(data => {
        this.userName = data.fullname;
      });
    });
  }

  onConfirm() {
    this.disableButton = true;
    if (!this.isCKMaxlenght) {
      const requestBodyObj = {
        dossierStatus: 2,
        comment: '',
        dossierTaskStatus: this.dossierTaskStatus.id === '' ? null : this.dossierTaskStatus,
        dossierMenuTaskRemind: this.dossierMenuTaskRemind.id === '' ? null : this.dossierMenuTaskRemind
      };
      if (this.commentContent.trim() !== '') {
        this.postComment(this.commentContent.trim());
        requestBodyObj.comment = this.commentContent.trim();
      } else {
        this.disableButton = false;
        let msgObj;
        if (this.ckeditorMaxLength > 0) {
          msgObj = {
            vi: 'Nội dung không quá ' + this.ckeditorMaxLength + ' ký tự!',
            en: 'Content must not exceed ' + this.ckeditorMaxLength + ' characters!'
          };
        } else {
          msgObj = {
            vi: 'Nội dung không quá 500 ký tự!',
            en: 'Content must not exceed 500 characters!'
          };
        }
        this.postComment(msgObj[this.selectedLang]);
        requestBodyObj.comment = msgObj[this.selectedLang];
      }

      const requestBody = JSON.stringify(requestBodyObj, null, 2);

      this.dossierService.putDossierStatusWithComment(this.dossierId, requestBody).subscribe(async data => {
        if (data.affectedRows === 1) {
          this.postHistory();
          const userAgency = JSON.parse(localStorage.getItem('userAgency'));
          if(this.changeAppointmentDateWhenResume && (this.changeAppointmentDateWhenResumeAgencyList.includes(userAgency.id) || (tUtils.nonNull(userAgency?.parent, 'id') && this.changeAppointmentDateWhenResumeAgencyList.includes(userAgency.parent.id)))){
            if(this.resetProcressTimeCurrentTask.turnOn && this.agencyAccept) {
              await this.dossierService.getProcessTimeResumeV2(this.dossierId,this.deploymentService.env.OS_HCM.notSumPauseDayToProcessingTime).subscribe(data => {});
            } else {
              await this.dossierService.getProcessTimeResume(this.dossierId).subscribe(data => {});
            }
          }
          //IGATESUPP-103472 -	QTI
          if(this.sumPauseProcessingTimeDueDate) {
            await this.dossierService.getDueDateResume(this.dossierId).subscribe(data => {});
          }
          if (this.deploymentService?.env?.OS_BTTTT?.isEnabledNotify) {
            // IGATESUPP-62366 thong bao cho nguoi dan
            this.dossierService.noticeDossier(this.dossierId, {comment: 'Ý kiến:&nbsp;' + this.commentContent.trim()}).subscribe(res => {});
          }
          this.notiService.confirmSendSubject.next({
            confirm: true,
            renewContent: false
          });
          this.dialogRef.close(true);
        } else {
          this.dialogRef.close(false);
        }
      }, err => {
        this.dialogRef.close(false);
      });
    } else {
      this.disableButton = false;
      const msgObj = {
        vi: 'Nội dung không quá 500 ký tự!',
        en: 'Content must not exceed 500 characters!'
      };
      this.snackbarService.openSnackBar(2, '', msgObj[this.selectedLang], 'warning_notification', this.config.expiredTime);
    }
  }
  onDismiss() {
    this.dialogRef.close();
  }

  postComment(commentContent) {
    const content = {
      groupId: 2,
      itemId: this.dossierId,
      user: {
        id: this.accountId,
        fullname: this.userName
      },
      content: commentContent.trim()
    };
    const requestBody = JSON.stringify(content, null, 2);
    this.dossierService.postComment(requestBody).subscribe(data => { });
  }

  onCommentEditorChange(event) {
    this.commentContent = event.editor.getData();
    if (this.ckeditorMaxLength > 0) {
      if (event.editor.getData().trim().length > this.ckeditorMaxLength) {
        this.isCKMaxlenght = true;
      } else {
        this.isCKMaxlenght = false;
      }
    } else if (event.editor.getData().trim().length > 500) {
      this.isCKMaxlenght = true;
    } else {
      this.isCKMaxlenght = false;
    }
  }

  async checkAgencyResetProcessingTime() {
    await this.checkAgencyAcceptResetTime();

    let userAgency = JSON.parse(localStorage.getItem('userAgency'));
    const index = this.listAgencyResetTimeWhenResume.findIndex(id => {
      return id === userAgency.id;
    });
    if (index != -1) {
      return this.agencyAccept = true;
    } else {
      return this.agencyAccept = false;
    }
    return this.agencyAccept = false;
  }

  checkAgencyAcceptResetTime() {
    if (this.resetProcressTimeCurrentTask.turnOn) {
      this.listAgencyResetTimeWhenResume = [];
      const searchString = '--by-parent-agency?arr-parent-id=' + this.resetProcressTimeCurrentTask.agencyList;
      return new Promise<void>(resolve => {
        this.procedureService.getListAgencyWithParent(searchString).toPromise().then(res => {
          res.forEach(agency => {
            this.listAgencyResetTimeWhenResume.push(agency.id);
          });
          resolve();
        }).catch(err => {
          resolve();
        })
      })
    }
    return
  }

}

export class ResumeProcessingModel {
  constructor(public dossierId: string, public dossierCode: string) {
  }
}
