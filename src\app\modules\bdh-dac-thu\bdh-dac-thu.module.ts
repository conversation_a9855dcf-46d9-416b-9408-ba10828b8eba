import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { BdhDacThuRoutingModule } from './bdh-dac-thu-routing.module';
// import { HouseholdInfoComponent } from './household-info/household-info.component';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { SharedModule } from 'src/app/shared/shared.module';


@NgModule({
  declarations: [],
  imports: [
    CommonModule,
    BdhDacThuRoutingModule,
    MatFormFieldModule,
    MatInputModule,
    SharedModule,
  ]
})
export class BdhDacThuModule { }
