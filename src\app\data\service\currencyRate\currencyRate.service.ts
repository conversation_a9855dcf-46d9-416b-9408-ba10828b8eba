import { HttpClient, HttpHeaders } from "@angular/common/http";
import { Inject, Injectable, LOCALE_ID } from "@angular/core";
import { ApiProviderService } from "src/app/core/service/api-provider.service";
import { EnvService } from "src/app/core/service/env.service";
import { SnackbarService } from "../snackbar/snackbar.service";
import { DeploymentService } from '../deployment.service';
import { Observable } from "rxjs/internal/Observable";


@Injectable({
    providedIn: 'root'
  })
export class CurrencyRateService {
    constructor(
        private http: HttpClient,
        private apiProviderService: ApiProviderService,
        private envservice: EnvService,
        private snackbarService: SnackbarService,
        @Inject(LOCALE_ID) protected localeId: string,
        private deploymentService: DeploymentService
      ) { }
      private getCurrencyRateURL = this.apiProviderService.getUrl('digo', 'basepad');
      getDetailCurrencyRateCode(code): Observable<any> {
        let headers = new HttpHeaders();
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        return this.http.get(this.getCurrencyRateURL + '/currency-rate/--code?code=' + code, {headers});
      }
}