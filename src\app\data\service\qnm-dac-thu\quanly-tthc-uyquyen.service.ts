import { Inject, Injectable, LOCALE_ID } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { ApiProviderService } from 'src/app/core/service/api-provider.service';
import { Observable } from 'rxjs';
import { EnvService } from 'src/app/core/service/env.service';
import { SnackbarService } from '../snackbar/snackbar.service';
import { Workbook } from 'exceljs';
import * as fs from 'file-saver';

const EXCEL_TYPE = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8';
const EXCEL_EXTENSION = '.xlsx';

@Injectable({
  providedIn: 'root'
})
export class QuanLyTTHCUyQuyenService {

  constructor(
    private http: HttpClient,
    private apiProviderService: ApiProviderService,
    private envservice: EnvService,
    private snackbarService: SnackbarService,
    @Inject(LOCALE_ID) protected localeId: string,
  ) { }
  config = this.envservice.getConfig();
  private proceduretrackingPath = this.apiProviderService.getUrl('digo', 'basepad') + '/qnm-procedure-tracking/';
  private proceduretrackingPostPath = this.apiProviderService.getUrl('digo', 'basepad') + '/qnm-procedure-tracking/transfer-tracking';

  getListSearchProcedureTracking(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.proceduretrackingPath + `--search${searchString}`, { headers });
  }
  postProcedureTracking(requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.post<any>(this.proceduretrackingPostPath, requestBody, { headers });
  }
  deleteProcedureTracking(id: string): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.append('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.delete(this.proceduretrackingPath + id, { headers });
  }
  putProcedureAuthorParam(id, requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.put<any>(this.proceduretrackingPath + id, requestBody, { headers });
  }
  getStatistic1ProcedureTracking(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.proceduretrackingPath + `--statistic/--01${searchString}`, { headers });
  }
  getStatistic2ProcedureTracking(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.proceduretrackingPath + `--statistic/--02${searchString}`, { headers });
  }
  exportToExcelStatistic2(params: any, fileName : any) {
    return new Promise((resolve) => {
      this.http.get(this.proceduretrackingPath + `--statistic/--02` + params, {
        observe: 'response',
        responseType: 'blob'
      }).toPromise().then(res => {
        const blob = new Blob([res.body], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'});
        let filename = fileName;
        if (res.headers.get('content-disposition') != null) {
          filename = res.headers.get('content-disposition').split(';')[1].split('=')[1].replace(/\"/g, '');
        }
        const blobUrl = URL.createObjectURL(blob);

        const xhr = new XMLHttpRequest();
        xhr.responseType = 'blob';

        xhr.onload = () => {
          const recoveredBlob = xhr.response;
          const reader = new FileReader();
          reader.onload = () => {
            const base64data = reader.result.toString();
            const object = JSON.stringify({
              mimeType: filename.substring(filename.lastIndexOf('.') + 1),
              name: filename.substring(0, filename.lastIndexOf('.')),
              data: base64data.split(',')[1]
            });
            const anchor = document.createElement('a');
            anchor.download = filename;
            anchor.href = base64data;
            anchor.click();
          };
          reader.readAsDataURL(recoveredBlob);
        };

        xhr.open('GET', blobUrl);
        xhr.send();
        resolve(true);
      }).catch(err => {
        if (err.status === 500) {
          const message = {
            vi: 'Hệ thống tạm thời không thể xuất file excel!',
            en: 'The system is temporarily unable to export the excel file!'
          };
          this.snackbarService.openSnackBar(0, '', message[localStorage.getItem('language')], 'error_notification', this.config.expiredTime);
        }
        resolve(false);
      });
    });
  }

  public exportToExcelStatistic1ProcedureQNM(
    reportHeading: string,
    json: any[],
    excelFileName: string,
    sheetName: string)
  {
  const data = json;
  // create workbook and worksheet
  const workbook = new Workbook();
  workbook.creator = 'Snippet Coder';
  workbook.lastModifiedBy = 'SnippetCoder';
  workbook.created = new Date();
  workbook.modified = new Date();
  const worksheet = workbook.addWorksheet(sheetName);

  worksheet.getColumn('A').font = {name: 'Times New Roman', size: 12};
  worksheet.getColumn('B').font = {name: 'Times New Roman', size: 12};
  worksheet.getColumn('C').font = {name: 'Times New Roman', size: 12};
  worksheet.getColumn('D').font = {name: 'Times New Roman', size: 12};
  worksheet.getColumn('E').font = {name: 'Times New Roman', size: 12};
  worksheet.getColumn('F').font = {name: 'Times New Roman', size: 12};
  worksheet.getColumn('G').font = {name: 'Times New Roman', size: 12};
  worksheet.getColumn('H').font = {name: 'Times New Roman', size: 12};
  worksheet.getColumn('I').font = {name: 'Times New Roman', size: 12};
  worksheet.getColumn('J').font = {name: 'Times New Roman', size: 12};
  worksheet.getColumn('K').font = {name: 'Times New Roman', size: 12};
  worksheet.getColumn('L').font = {name: 'Times New Roman', size: 12};
  worksheet.getColumn('M').font = {name: 'Times New Roman', size: 12};
  worksheet.getColumn('N').font = {name: 'Times New Roman', size: 12};
  worksheet.getColumn('O').font = {name: 'Times New Roman', size: 12};

  // Add header row
  worksheet.addRow([]);
  worksheet.mergeCells('D2:H2');
  worksheet.getCell('D2').value = reportHeading;
  worksheet.getCell('D2').alignment = {horizontal: 'center', vertical: 'middle'};
  worksheet.getCell('D2').font = {size: 16, bold: true, name: 'Times New Roman'};


  // NỘI DUNG TABLE-HEADER
  worksheet.mergeCells('A6:A8');
  worksheet.mergeCells('B6:B8');
  worksheet.mergeCells('C6:C8');
  worksheet.mergeCells('D6:D8');
  worksheet.mergeCells('E6:E8');

  worksheet.mergeCells('F7:F8');
  worksheet.mergeCells('G7:G8');

  worksheet.mergeCells('F6:G6');


  worksheet.mergeCells('H7:H8');
  worksheet.mergeCells('I7:I8');
  worksheet.mergeCells('J7:J8');
  worksheet.mergeCells('K7:K8');

  worksheet.mergeCells('H6:K6');


  worksheet.mergeCells('L6:L8');
  worksheet.mergeCells('M6:M8');
  worksheet.mergeCells('N6:N8');
  worksheet.mergeCells('O6:O8');




  worksheet.getCell('A7').value = 'STT';
  worksheet.getCell('B7').value = 'Mã thủ tục';
  worksheet.getCell('C7').value = 'Tên thủ tục';
  worksheet.getCell('D7').value = 'Đã ủy quyền';
  worksheet.getCell('E7').value = 'Chưa ủy quyền';
  worksheet.getCell('F6').value = 'Nơi tiếp nhận hồ sơ';
  worksheet.getCell('F7').value = 'TTPV HCC';
  worksheet.getCell('G7').value = 'Cơ quan đơn vị';
  worksheet.getCell('H6').value = 'Phương thức nộp hồ sơ (theo qđ)';
  worksheet.getCell('H7').value = 'Trực tiếp';
  worksheet.getCell('I7').value = 'Bưu điện';
  worksheet.getCell('J7').value = 'DVC3';
  worksheet.getCell('K7').value = 'DVC4';
  worksheet.getCell('L7').value = 'Tổng thời gian giải quyết theo qđ hiện hành';
  worksheet.getCell('M7').value = 'Thời gian trình UBND (theo qđ)';
  worksheet.getCell('N7').value = 'Số hồ sơ phát sinh';
  worksheet.getCell('O7').value = 'Ghi chú';

  worksheet.getColumn('B').width = 20;
  worksheet.getColumn('C').width = 50;

  worksheet.getColumn('D').width = 15;
  worksheet.getColumn('E').width = 15;
  worksheet.getColumn('F').width = 15;
  worksheet.getColumn('G').width = 15;
  worksheet.getColumn('H').width = 15;
  worksheet.getColumn('I').width = 15;
  worksheet.getColumn('J').width = 15;
  worksheet.getColumn('K').width = 15;
  worksheet.getColumn('L').width = 15;
  worksheet.getColumn('M').width = 15;
  worksheet.getColumn('N').width = 15;
  worksheet.getColumn('O').width = 15;


  worksheet.properties.outlineLevelCol = 2;
  worksheet.properties.defaultRowHeight = 15;

  let i=6;
  const j = 8;
  for (i; i <= j; i++) {
    let k = 1;
    const l = 15;
    for (k; k <= l; k++) {
      worksheet.findCell(i, k).alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
      worksheet.findCell(i, k).fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: {argb: 'C0C0C0C0'},
        bgColor: {argb: 'FF0000FF'}
      };
      worksheet.findCell(i, k).border = {top: {style: 'thin'}, left: {style: 'thin'}, bottom: {style: 'thin'}, right: {style: 'thin'}};
      worksheet.findCell(i, k).font = {size: 12, bold: true, name: 'Times New Roman'};
    }
  }

  // get all columns from JSON
  let columnsArray: any[];
  for (const key in json) {
    if (json.hasOwnProperty(key)) {
      columnsArray = Object.keys(json[key]);
    }
  }

  // Add Data and Conditional Formatting
  data.forEach((element: any) => {
    const eachRow = [];
    columnsArray.forEach((column) => {
      eachRow.push(element[column]);
    });
    console.log("🚀 ~ file: statistics.service.ts:1051 ~ StatisticsService ~ columnsArray.forEach ~ eachRow", eachRow)
    const borderrow = worksheet.addRow(eachRow);
    borderrow.eachCell((cell) => {
      cell.border = {top: {style: 'thin'}, left: {style: 'thin'}, bottom: {style: 'thin'}, right: {style: 'thin'}};
      cell.alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
    });
  });

  const dataLength = data.length;
  if (dataLength > 0) {
    for (i = 0; i < dataLength; i++) {
      worksheet.getCell('B' + (9 + i)).alignment = {horizontal: 'left', vertical: 'middle', wrapText: true};
      worksheet.getCell('C' + (9 + i)).alignment = {horizontal: 'left', vertical: 'middle', wrapText: true};
    }
  }
  // Save Excel File
  workbook.xlsx.writeBuffer().then((dataBuffer: ArrayBuffer) => {
    const blob = new Blob([dataBuffer], {type: EXCEL_TYPE});
    fs.saveAs(blob, excelFileName + EXCEL_EXTENSION);
  });
  }
  public exportToExcelStatistic2ProcedureQNM(
    reportHeading: string,
    json: any[],
    excelFileName: string,
    sheetName: string)
  {
  const data = json;
  // create workbook and worksheet
  const workbook = new Workbook();
  workbook.creator = 'Snippet Coder';
  workbook.lastModifiedBy = 'SnippetCoder';
  workbook.created = new Date();
  workbook.modified = new Date();
  const worksheet = workbook.addWorksheet(sheetName);

  worksheet.getColumn('A').font = {name: 'Times New Roman', size: 12};
  worksheet.getColumn('B').font = {name: 'Times New Roman', size: 12};
  worksheet.getColumn('C').font = {name: 'Times New Roman', size: 12};
  worksheet.getColumn('D').font = {name: 'Times New Roman', size: 12};
  worksheet.getColumn('E').font = {name: 'Times New Roman', size: 12};
  worksheet.getColumn('F').font = {name: 'Times New Roman', size: 12};
  worksheet.getColumn('G').font = {name: 'Times New Roman', size: 12};
  worksheet.getColumn('H').font = {name: 'Times New Roman', size: 12};


  // Add header row
  worksheet.addRow([]);
  worksheet.mergeCells('B2:H2');
  worksheet.getCell('B2').value = reportHeading;
  worksheet.getCell('B2').alignment = {horizontal: 'center', vertical: 'middle'};
  worksheet.getCell('B2').font = {size: 16, bold: true, name: 'Times New Roman'};


  // NỘI DUNG TABLE-HEADER
  worksheet.mergeCells('A7:A8');
  worksheet.mergeCells('B7:B8');
  worksheet.mergeCells('C7:C8');
  worksheet.mergeCells('D7:D8');
  worksheet.mergeCells('E7:E8');
  worksheet.mergeCells('F7:F8');
  worksheet.mergeCells('G7:G8');
  worksheet.mergeCells('H7:H8');


  worksheet.getCell('A7').value = 'STT';
  worksheet.getCell('B7').value = 'Mã thủ tục';
  worksheet.getCell('C7').value = 'Tên thủ tục';
  worksheet.getCell('D7').value = 'Cơ quan thực hiện';
  worksheet.getCell('E7').value = 'Lĩnh vực';
  worksheet.getCell('F7').value = 'Số quyết định';
  worksheet.getCell('G7').value = 'Ngày hết hạn';
  worksheet.getCell('H7').value = 'Ủy quyền';

  worksheet.getColumn('B').width = 20;
  worksheet.getColumn('C').width = 50;
  worksheet.getColumn('D').width = 20;
  worksheet.getColumn('E').width = 20;
  worksheet.getColumn('F').width = 20;
  worksheet.getColumn('G').width = 20;
  worksheet.getColumn('H').width = 20;

  worksheet.properties.outlineLevelCol = 2;
  worksheet.properties.defaultRowHeight = 15;

  let i=7;
  const j = 8;
  for (i; i <= j; i++) {
    let k = 1;
    const l = 8;
    for (k; k <= l; k++) {
      worksheet.findCell(i, k).alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
      worksheet.findCell(i, k).fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: {argb: 'C0C0C0C0'},
        bgColor: {argb: 'FF0000FF'}
      };
      worksheet.findCell(i, k).border = {top: {style: 'thin'}, left: {style: 'thin'}, bottom: {style: 'thin'}, right: {style: 'thin'}};
      worksheet.findCell(i, k).font = {size: 12, bold: true, name: 'Times New Roman'};
    }
  }

  // get all columns from JSON
  let columnsArray: any[];
  for (const key in json) {
    if (json.hasOwnProperty(key)) {
      columnsArray = Object.keys(json[key]);
    }
  }

  // Add Data and Conditional Formatting
  data.forEach((element: any) => {
    const eachRow = [];
    columnsArray.forEach((column) => {
      eachRow.push(element[column]);
    });
    console.log("🚀 ~ file: statistics.service.ts:1051 ~ StatisticsService ~ columnsArray.forEach ~ eachRow", eachRow)
    const borderrow = worksheet.addRow(eachRow);
    borderrow.eachCell((cell) => {
      cell.border = {top: {style: 'thin'}, left: {style: 'thin'}, bottom: {style: 'thin'}, right: {style: 'thin'}};
      cell.alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
    });
  });

  const dataLength = data.length;
  if (dataLength > 0) {
    for (i = 0; i < dataLength; i++) {
      worksheet.getCell('B' + (9 + i)).alignment = {horizontal: 'left', vertical: 'middle', wrapText: true};
      worksheet.getCell('C' + (9 + i)).alignment = {horizontal: 'left', vertical: 'middle', wrapText: true};
      worksheet.getCell('D' + (9 + i)).alignment = {horizontal: 'left', vertical: 'middle', wrapText: true};
      worksheet.getCell('E' + (9 + i)).alignment = {horizontal: 'left', vertical: 'middle', wrapText: true};
      worksheet.getCell('F' + (9 + i)).alignment = {horizontal: 'left', vertical: 'middle', wrapText: true};
    }
  }
  // Save Excel File
  workbook.xlsx.writeBuffer().then((dataBuffer: ArrayBuffer) => {
    const blob = new Blob([dataBuffer], {type: EXCEL_TYPE});
    fs.saveAs(blob, excelFileName + EXCEL_EXTENSION);
  });
  }
}
