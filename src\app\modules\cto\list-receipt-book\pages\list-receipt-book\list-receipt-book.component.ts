import {Component, OnInit, ChangeDetectorRef, AfterViewInit, Injectable, ViewChild, Inject, LOCALE_ID} from '@angular/core';
import { FormGroup, FormControl } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import { SectorService } from 'src/app/data/service/sector/sector.service';
import { EnvService } from 'src/app/core/service/env.service';
import { FormElement } from 'src/app/data/schema/form-element';
import { MatTableDataSource } from '@angular/material/table';
import { ConfirmReceiptBookDialogModel, ReceiptBookDialogComponent } from '../../dialogs/receipt-book-dialog/receipt-book-dialog.component';
import { ConfirmDeleteDialogModel, DeleteReceiptBookComponent } from '../../dialogs/delete-receipt-book/delete-receipt-book.component';
import { SnackbarService } from 'src/app/data/service/snackbar/snackbar.service';
import { MainService } from 'src/app/data/service/main/main.service';
import { AddReceiptBookAgencyComponent, ConfirmReceiptBookAgencyModel } from '../../dialogs/add-receipt-book-agency/add-receipt-book-agency.component';
import { DeploymentService } from 'src/app/data/service/deployment.service';
import { BasepadService } from 'src/app/data/service/basepad/basepad.service';
import {CloneReceiptBookComponent} from '../../dialogs/clone-receipt-book/clone-receipt-book.component';
import {DigoTableService} from 'data/service/shared/digo-table.service';
import { LedgerDefinitionService } from 'src/app/data/service/basepad/ledger-definition.service';
import { ReceiptBookService } from 'src/app/data/service/cto-statistics/receipt-book.service';
import { MatSelectionListChange } from '@angular/material/list';
import { NgModule } from '@angular/core';
import { InfiniteScrollModule } from 'ngx-infinite-scroll';
import { MatSelectModule } from '@angular/material/select';
import { Subject } from 'rxjs';
import { debounceTime } from 'rxjs/operators';

@NgModule({
  imports: [
    InfiniteScrollModule,
    MatSelectModule,
  ]
})
export class ListReceiptBookModule { }


interface IAgencySectorsChecked {
  id: string;
  isChecked: boolean;
}

@Injectable()

@Component({
  selector: 'app-list-receipt-book',
  templateUrl: './list-receipt-book.component.html',
  styleUrls: ['./list-receipt-book.component.scss'],
})
export class ListReceiptBookComponent implements OnInit, AfterViewInit {

  timeOut: any;

  @ViewChild('selectionAgencySectors') selectionAgencySectors: any = [];
  agencySectorsSet = new Map();
  checkPermissionImportGeneralInfomationData = false
  waitingDownload = false;
  enableBookList = this.deploymentService.getAppDeployment()?.enableBookList ?? false;
  configEnv = this.envService.getConfig();
  searchReceiptBookChanged = new Subject<string>();
  searchSectorChanged = new Subject<string>();
  constructor(
    private dialog: MatDialog,
    private sectorService: SectorService,
    private envService: EnvService,
    private mainService: MainService,
    private cdRef: ChangeDetectorRef,
    private snackbarService: SnackbarService,
    private deploymentService: DeploymentService,
    private basepadService: BasepadService,
    private digoTableService: DigoTableService,
    private ledgerDefinitionService: LedgerDefinitionService,
    private receiptBookService: ReceiptBookService,
    @Inject(LOCALE_ID) public localeId: string
  ) {
    this.dataSource = new MatTableDataSource(this.ELEMENTDATA);
    this.dataSource2 = new MatTableDataSource(this.ELEMENTDATA2);
    this.searchReceiptBookChanged
    .pipe(debounceTime(400))
    .subscribe(keyword => {
      this.listReceiptBook = [];
      this.keywordReceiptBook = keyword;
      this.currentReceiptBookPage = 0;
      this.getListReceiptBook(this.pageSizeReceiptBook, this.currentReceiptBookPage, this.keywordReceiptBook);
    });  
    this.searchSectorChanged.pipe(debounceTime(400)).subscribe(keyword => {
      this.keywordSector = keyword;
      if (this.isShowingOnlyCheckedSectors) {
        const selectedIds = new Set(this.selectedAgencySectors.map(s => s.id));
        let selectedFromLoaded = this.filterAgencySectors.filter(sector =>
          selectedIds.has(sector.id)
        );
        const missingSelected = this.selectedAgencySectors.filter(sel =>
          !this.filterAgencySectors.some(s => s.id === sel.id)
        );
        let combinedSelected = [...selectedFromLoaded, ...missingSelected];
  
        const lower = this.keywordSector.toLowerCase().trim();
        if (lower) {
          combinedSelected = combinedSelected.filter(
            s =>
              (s.code && s.code.toLowerCase().includes(lower)) ||
              (s.name && s.name.toLowerCase().includes(lower))
          );
        }
        this.filterAgencySectors = combinedSelected;
      } else {
        this.filterAgencySectors = [];
        this.listSector = [];
        this.currentSectorPage = 0;
        this.islastPageSector = false;
        this.getListSectorAll(this.pageSizeSector, this.currentSectorPage, this.keywordSector);
      }
    });     
  }
  config = this.envService.getConfig();
  env: any = this.deploymentService.getAppDeployment()?.env;
  selectedLang = localStorage.getItem('language') || 'vi';
  selectedLangId = Number(localStorage.getItem('languageId')) || 228;

  searchForm = new FormGroup({
    keyword: new FormControl(''),
    //year: new FormControl(''),
    //month: new FormControl('')
  });
  formSearch: any = this.searchForm.getRawValue();
  listForm = [];
  displayedColumns: string[] = ['stt', 'code', 'name', 'year', 'status', 'action'];
  ELEMENTDATA: FormElement[] = [];
  dataSource: MatTableDataSource<FormElement>;

  ELEMENTDATA2: FormElement[] = [];
  dataSource2: MatTableDataSource<FormElement>;

  size = 10;
  pageIndex = 1;
  page = 1;
  countResult = 0;
  size2 = 10;
  pageIndex2 = 1;
  page2 = 1;
  countResult2 = 0;
  pgSizeOptions = this.config.pageSizeOptions;
  pageTitle = {
    vi: `Danh mục sổ tiếp nhận`,
    en: `Receipt book catalog`
  };

  isShowSector = true;
  isShowSector_Agency = true;
  isShowSector_User = true;
  userId = localStorage.getItem("tempUID");
  checkNullSectorData = 0;
  checkNullSectorAgencyData = 0;

  islastPageSector = false;
  currentSectorPage = 0;
  keywordSector = "";
  pageSizeSector = 50;

  listReceiptBook = [];
  islastPageReceiptBook = false;
  currentReceiptBookPage = 0;
  keywordReceiptBook = "";
  pageSizeReceiptBook = 50;  

  isShowingOnlyCheckedSectors = false;
  selectedSector = [];

  ngOnInit(): void {
    this.mainService.setPageTitle(this.pageTitle[localStorage.getItem('language')]);
    const searchStringListSector = '?page=0&size=10&spec=page&keyword=';
    this.getListSector(searchStringListSector);
    const searchStringListSectorAgency = '?page=0&size=10&spec=page&related-token=true&keyword=';
    this.getListReceiptBookAgency(searchStringListSectorAgency);
    const userAgency = JSON.parse(localStorage.getItem('userAgency'));
    let rootAgencyId: any = '';
    if (userAgency !== null) {
      rootAgencyId = userAgency.id;
    } else {
      rootAgencyId = this.config.rootAgency.id;
    }
    this.getListSectorAll(this.pageSizeSector, this.currentSectorPage, this.keywordSector);
    this.loadAllReceiptBooks();
    this.getListReceiptBook(this.pageSizeReceiptBook, this.currentReceiptBookPage, this.keywordReceiptBook);
  }

  onConfirm() {
    let searchString = '?page=0&size=' + this.size + '&spec=page';
    this.pageIndex = 1;
    this.page = 1;
    let abc = this.searchForm.getRawValue().keyword;
    abc = abc.trim();
    this.searchForm.patchValue({
      keyword: abc
    });
    const formObj = this.searchForm.getRawValue();
    const rawKeyword = formObj.keyword || '';
    const cleanKeyword = rawKeyword.trim().replace(/[&\/\\#,+()$~%.'":*?<>{}\[\]]/g, '');    
    this.formSearch = this.searchForm.getRawValue();
    searchString = searchString + '&keyword=' + cleanKeyword;
    this.getListReceiptBookAgency(searchString);
    this.getListSector(searchString);
  }
  updateStatus(id){
    this.ledgerDefinitionService.updateStatus(id).subscribe(data => {
      const msgObj = {
        vi: 'Cập nhật trạng thái thành công',
        en: 'Update ledger definition status successful'
      };
      this.snackbarService.openSnackBar(1, msgObj[this.localeId], '', 'success_notification', this.configEnv.expiredTime);
      this.digoTableService.renewTable.next(true);
    }, err => {
      const msgObj = {
        vi: 'Không thể cập nhật trạng thái',
        en: 'Can not update ledger definition status'
      };
      this.snackbarService.openSnackBar(0, msgObj[this.localeId], err.error.message, 'error_notification', this.configEnv.expiredTime);
    });
  }
  clone(id){
    const dialogRef = this.dialog.open(CloneReceiptBookComponent, {
      width: '75vw',
      maxHeight: '75vh',
      data: {
        id: id
      },
      disableClose: true,
      autoFocus: false
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
      const res = dialogResult;
      if (res.status === true) {
        //const totalPage = Math.ceil((this.countResult + 1) / this.size);
        this.page = 1;
        this.searchForm.patchValue({
          keyword: ''
        });
        this.formSearch = this.searchForm.getRawValue();
        this.reloadListReceiptBook(1);
      }
    });
  }

  ngAfterViewInit() {
    this.cdRef.detectChanges();
  }

  addSectorAgency() {
    const dialogData = new ConfirmReceiptBookAgencyModel();
    const dialogRef = this.dialog.open(AddReceiptBookAgencyComponent, {
      width: '90%',
      height: '90%',
      data: dialogData,
      disableClose: true,
      autoFocus: false
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
      const res = dialogResult;
      let content = '' + 'Cấu hình' + '';
      if (this.selectedLang === 'en') {
        content = '' + 'Config' + '';
      }
      if (res.status === true) {
        const totalPage = Math.ceil((this.countResult + 1) / this.size);
        this.page = totalPage;
        this.searchForm.patchValue({
          keyword: ''
        });
        this.formSearch = this.searchForm.getRawValue();
        this.paginate2(totalPage, 1);
      }
    });
  }

  addReceiptBook() {
    const dialogRef = this.dialog.open(ReceiptBookDialogComponent, {
      width: '900px',
      height: '40%',
      data: {
        type: 0,
        id: null,
        data: null
      },
      disableClose: true,
      autoFocus: false
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
      const res = dialogResult;
      if (res.status === true) {
        //const totalPage = Math.ceil((this.countResult + 1) / this.size);
        this.page = 1;
        this.searchForm.patchValue({
          keyword: ''
        });
        this.formSearch = this.searchForm.getRawValue();      
        this.reloadListReceiptBook(1);
      }
    });
  }

  updateReceiptBook(id) {
    const dialogRef = this.dialog.open(ReceiptBookDialogComponent, {
      width: '900px',
      height: '40%',
      data: {
        type: 1,
        id: id,
        data: null
      },
      disableClose: true,
      autoFocus: false
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
      const res = dialogResult;
      if (res.status === true) {
        this.reloadListReceiptBook(this.pageIndex);
      }
    });
  }

  deleteReceiptBook(id, name) {
    const dialogData = new ConfirmDeleteDialogModel(id, name);
    const dialogRef = this.dialog.open(DeleteReceiptBookComponent, {
      width: '500px',
      data: dialogData,
      disableClose: true,
      autoFocus: false
    });
    const content = '' + name + '';
    dialogRef.afterClosed().subscribe(dialogResult => {
      const res = dialogResult;
      if (res === true) {
        const totalPage = Math.ceil((this.countResult - 1) / this.size);
        if (this.page > totalPage) {
          this.page = totalPage;
        }
        this.reloadListReceiptBook(this.page);
        this.snackbarService.openSnackBar(1, this.envService.getTranslateNotificationLabel(this.selectedLang, 'success', 'delete'), content, 'success_notification', this.config.expiredTime);
      }
      if (res === false) {
        const failed = {
          en: content + ' is used!',
          vi: content + ' đã được sử dụng'
        };
        // tslint:disable-next-line: max-line-length
        this.snackbarService.openSnackBar(0, this.envService.getTranslateNotificationLabel(this.selectedLang, 'error', 'delete'), failed[this.selectedLang], 'error_notification', this.config.expiredTime);
      }
    });
  }

  getListSector(searchString: string) {
    const languageId = Number(localStorage.getItem('languageId')) || 46;
    const isEnglish = languageId === 46;
    this.receiptBookService.search(searchString).subscribe(data => {
      this.checkNullSectorData = data.content.length > 0 ? 0 : 1;
      this.countResult = data.totalElements;
      this.ELEMENTDATA = data.content.map((item, index) => ({
        ...item,
        stt: this.size * (this.pageIndex - 1) + (index + 1),
        nameStatus: isEnglish
          ? (item.status ? 'Open' : 'Close')
          : (item.status ? 'Mở' : 'Đóng'),
        typeName: item.type?.name?.find(n => n.languageId === languageId)?.name || '',
        parent: item.parent && item.parent.id ? item.parent : { name: '' }
      }));
      this.dataSource.data = this.ELEMENTDATA;
    });
  }
  
  getListReceiptBookAgency(searchString: string) {
    this.receiptBookService.search(searchString).subscribe(data => {
      this.ELEMENTDATA2 = [];
      this.checkNullSectorAgencyData = data.content.length > 0 ? 0 : 1;
      this.countResult2 = data.totalElements;
      for (let i = 0; i < data.numberOfElements; i++) {
        const item = data.content[i];
        item.stt = this.size * (this.pageIndex - 1) + (i + 1);
        if (Number(localStorage.getItem('languageId')) === 46) {
          item.status = item.status ? 'Open' : 'Close';
        } else {
          item.status = item.status ? 'Mở' : 'Đóng';
        }
        if (!item.parent || !item.parent.id) {
          item.parent = { name: '' };
        }
        item.agencyName = '';
        item.agency = item.agency || [];
        item.sector = item.sector || [];
        if (item.allAgency === 1) {
          item.agencyName = this.selectedLang === 'en' ? 'All' : 'Tất cả';
        } else if (item.agency.length > 0) {
          for (let j = 0; j < item.agency.length && j < 2; j++) {
            const agencyNameObj = item.agency[j].name?.find(n => n.languageId === Number(localStorage.getItem('languageId')));
            const agencyName = agencyNameObj?.name || '';
            item.agencyName += agencyName; 
            if (j < item.agency.length - 1 && j < 1) {
              item.agencyName += ', ';
            }
            if (j === 1 && j < item.agency.length - 1) {
              item.agencyName += ',...';
            }
          }
        }
        this.ELEMENTDATA2.push(item);
      }
      this.dataSource2.data = this.ELEMENTDATA2;
    });
  }  

  paginate(event: any, type) {
    switch (type) {
      case 0:
        this.pageIndex = event;
        this.getListSector('?page=' + (this.pageIndex - 1) + '&size=' + this.size + '&spec=page&keyword=' + this.formSearch.keyword.replace(/[&\/\\#,+()$~%.'":*?<>{}\[\]]/g,''));
        break;
      case 1:
        this.pageIndex = 1;
        this.page = 1;
        this.getListSector('?page=' + (this.pageIndex - 1) + '&size=' + this.size + '&spec=page&keyword=' + this.formSearch.keyword.replace(/[&\/\\#,+()$~%.'":*?<>{}\[\]]/g,''));
        break;
    }
  }

  paginate2(event: any, type) {
    switch (type) {
      case 0:
        this.pageIndex2 = event;
        this.getListReceiptBookAgency('?page=' + (this.pageIndex2 - 1) + '&size=' + this.size2 + '&spec=page&related-token=true&keyword=' + this.formSearch.keyword.replace(/[&\/\\#,+()$~%.'":*?<>{}\[\]]/g,''));
        break;
      case 1:
        this.pageIndex2 = 1;
        this.page2 = 1;
        this.getListReceiptBookAgency('?page=' + (this.pageIndex2 - 1) + '&size=' + this.size2 + '&spec=page&related-token=true&keyword=' + this.formSearch.keyword.replace(/[&\/\\#,+()$~%.'":*?<>{}\[\]]/g,''));
        break;
    }
  }

  getStatus(status) {
    switch (status) {
      case 0:
        return 'Đóng';
      case 1:
        return 'Mở';
    }
  }
     
  filterReceiptBooks: any[] = [];
  receiptBooks: any[] = [];

  selectedAgencySectors: any;
  sectorsChecked: IAgencySectorsChecked[] = [];

  receiptBookCheckedSet: Set<string> = new Set();
  selectedReceiptBooks: any[] = [];

  filterAgencySectors: any = [];
  listSector: any = [];

  loadAllReceiptBooks(): void {
    this.receiptBookService.search('').subscribe(response => {
      this.filterReceiptBooks = response?.content || [];
      this.cdRef.detectChanges();
    });
  }  

  filterReceiptBooksChanged(filterText: string) {
    const lower = filterText.toLowerCase();
    this.filterReceiptBooks = this.receiptBooks.filter(item =>
      item.name?.toLowerCase().includes(lower)
    );
  }

  getSelectedReceiptBook(searchString: string) { 
    this.selectedAgencySectors = []; 
    this.sectorsChecked = [];
  
    this.receiptBookService.search(searchString).subscribe(data => {
      if (data && data.content && data.content.length > 0) {
        const sectorMap = new Map<string, any>();
        data.content.forEach(item => {
          if (item.sector && Array.isArray(item.sector)) {
            item.sector.forEach(sector => {
              if (!sectorMap.has(sector.id)) {
                let formatSector = {
                  id: sector.id,
                  code: sector.code,
                  name: sector.name[0]?.name
                };
                sectorMap.set(sector.id, formatSector);
              }
            });
          }
        });
  
        const sectors = Array.from(sectorMap.values());
  
        this.selectedAgencySectors = sectors;
        this.sectorsChecked = sectors.map(s => ({ id: s.id, isChecked: true }));

        if (this.isShowingOnlyCheckedSectors) {
          if (this.selectedAgencySectors && this.selectedAgencySectors.length > 0) {
            const selectedIds = new Set(this.selectedAgencySectors.map(s => s.id));
            const selectedFromLoaded = this.filterAgencySectors.filter(sector =>
              selectedIds.has(sector.id)
            );
            const missingSelected = this.selectedAgencySectors.filter(sel =>
              !this.filterAgencySectors.some(s => s.id === sel.id)
            );
            this.filterAgencySectors = [...selectedFromLoaded, ...missingSelected];
          } else {
            this.filterAgencySectors = [];
          }
        }
  
        console.log("getSelectedReceiptBook");
        console.log(data.content);
        console.log(this.selectedAgencySectors);
      }
    });
  }
  

  onReceiptBookSelectionChange(event: MatSelectionListChange): void {
    console.log("onReceiptBookSelectionChange");

    const selectedBook: any = event.option.value;
    const isSelected = event.option.selected;
  
    if (isSelected) {
      this.receiptBookCheckedSet.add(selectedBook.id);
      if (!this.selectedReceiptBooks.find(rb => rb.id === selectedBook.id)) {
        this.selectedReceiptBooks.push(selectedBook);
      }
    } else {
      this.receiptBookCheckedSet.delete(selectedBook.id);
      this.selectedReceiptBooks = this.selectedReceiptBooks.filter(rb => rb.id !== selectedBook.id);
    }
  
    const ids = Array.from(this.receiptBookCheckedSet);
    if (ids.length > 0) {
      const query = `?receiptBookIds=${ids.join(',')}`;
      this.getSelectedReceiptBook(query);
    } else {
      this.selectedAgencySectors = [];
      this.sectorsChecked = [];
      this.cdRef.detectChanges();
    }
  }

  isReceiptBookChecked(id: string): boolean {
    return this.receiptBookCheckedSet.has(id);
  }

  getListReceiptBook(size, page, keyword): any {
    if (keyword && keyword.trim() !== "") {
      keyword = keyword.trim().replace(/[&\/\\#,+()$~%.'":*?<>{}\[\]]/g, '');
    } else {
      keyword = "";
    }
    this.receiptBookService.search(`?page=${page}&size=${size}&keyword=${keyword}`).subscribe(data => {
      if(data != null){        
        for (const item of data.content) {           
          if (item.name && this.listReceiptBook.find(o => o.id == item.id) == undefined) {
            this.listReceiptBook.push(item);
          }
        }
        this.islastPageReceiptBook = data.last;        
      }
    }, err => {
      console.log(err);
    });    
  }

  getNextBatchReceiptBook() {
    if(!this.islastPageReceiptBook){
      this.currentReceiptBookPage += 1;
      this.getListReceiptBook(this.pageSizeReceiptBook, this.currentReceiptBookPage, this.keywordReceiptBook);
    }
  }

  onSearchReceiptBook(event) {
    const keyword = event.target.value;
    this.searchReceiptBookChanged.next(keyword);
  }		  


  getListSectorAll(size, page, keyword) {
    if (keyword && keyword.trim() !== "") {
      keyword = keyword.trim().replace(/[&\/\\#,+()$~%.'":*?<>{}\[\]]/g, '');
    } else {
      keyword = "";
    }
    this.sectorService.getListSector(`?page=${page}&size=${size}&status=1&keyword=${keyword}`)
      .subscribe(data => {
        if (data != null) {
          for (const item of data.content) {
            if (item.name && this.filterAgencySectors.find(o => o.id == item.id) == undefined) {
              this.filterAgencySectors.push(item);
            }
          }
          const selectedIds = new Set(this.selectedAgencySectors.map(s => s.id));
          const selected = this.filterAgencySectors.filter(s => selectedIds.has(s.id));
          const nonSelected = this.filterAgencySectors.filter(s => !selectedIds.has(s.id));
  
          this.filterAgencySectors = [
            ...selected,
            ...nonSelected
          ];
          this.islastPageSector = data.last;
        }
        this.listSector = data.content;
      });
  }
  
  getNextBatchSector() {
    if (!this.islastPageSector) {
      this.currentSectorPage += 1;
      this.getListSectorAll(this.pageSizeSector, this.currentSectorPage, this.keywordSector);
    }
  }
   
  onSearchSector(event) {
    this.searchSectorChanged.next(event.target.value);
  }
  
  onSelectSector($event: any) {
    this.agencySectorsSet.set(
      $event.option.value,
      !this.agencySectorsSet.get($event.option.value)
    );
  }  

  checked(sector: any) {
    const index = this.sectorsChecked.findIndex(c => c.id === sector.id);
    if (index !== -1) {
      this.sectorsChecked[index].isChecked = !this.sectorsChecked[index].isChecked;
      if (this.sectorsChecked[index].isChecked) {
        if (sector && sector.id) {
          this.selectedAgencySectors.push(sector);
        }
      } else {
        this.selectedAgencySectors = this.selectedAgencySectors.filter(s => s.id !== sector.id);
      }
    } else {
      if (sector && sector.id) {
        this.sectorsChecked.push({ id: sector.id, isChecked: true });
        this.selectedAgencySectors.push(sector);
      }
    }
    this.selectedAgencySectors = Array.from(
      new Map(this.selectedAgencySectors.map(s => [s.id, s])).values()
    );
  }
  
  
  isChecked(id: string): boolean {
    return this.sectorsChecked.some(item => item.id === id && item.isChecked);
  }  

  async onConfirm2() {
    if (!this.selectedReceiptBooks || this.selectedReceiptBooks.length === 0) {
      this.snackbarService.openSnackBar(0, 'Vui lòng chọn ít nhất 1 sổ tiếp nhận!', '', 'warning_notification', this.config.expiredTime);
      return;
    }
  
    const checkedSectors = this.sectorsChecked
      .filter(s => s.isChecked)
      .map(item => {
        const fullSector =
          this.filterAgencySectors.find(sector => sector.id === item.id) ||
          this.listSector.find(sector => sector.id === item.id) ||
          { id: item.id, code: '', name: [{ languageId: 228, name: '' }] };
  
        const sectorCode = fullSector.code || '';
        const sectorNameArray = Array.isArray(fullSector.name)
          ? fullSector.name.length > 0
            ? fullSector.name
            : [{ languageId: 228, name: '' }]
          : [{ languageId: 228, name: fullSector.name || '' }];
  
        return {
          id: fullSector.id,
          code: sectorCode,
          name: sectorNameArray
        };
      })
      .filter(s => s.code && s.name.some(n => n.name && n.name.trim() !== ''));
  
    const selectedReceiptBookIds = this.selectedReceiptBooks.map(book => book.id);
  
    const payload = {
      sectors: checkedSectors,
      receiptBooks: selectedReceiptBookIds
    };
  
    this.receiptBookService.updateReceiptBookSector(payload).subscribe(
      res => {
        this.snackbarService.openSnackBar(1, 'Thành công', '', 'success_notification', this.config.expiredTime);
      },
      err => {
        this.snackbarService.openSnackBar(0, 'Lỗi', '', 'error_notification', this.config.expiredTime);
      }
    );
  }
  
  async reloadListReceiptBook(page){    
    this.paginate(page, 0);
    this.paginate2(page, 0);
    this.listReceiptBook = [];
    this.currentReceiptBookPage = 0;
    this.getListReceiptBook(this.pageSizeReceiptBook, this.currentReceiptBookPage, this.keywordReceiptBook);  
  }

  toggleShowCheckedSectors() {
    if (!this.isShowingOnlyCheckedSectors) {
      if (this.selectedAgencySectors && this.selectedAgencySectors.length > 0) {
        const selectedIds = new Set(this.selectedAgencySectors.map(s => s.id));
        let selectedFromLoaded = this.filterAgencySectors.filter(sector =>
          selectedIds.has(sector.id)
        );
        const missingSelected = this.selectedAgencySectors.filter(sel =>
          !this.filterAgencySectors.some(s => s.id === sel.id)
        );
        let combinedSelected = [...selectedFromLoaded, ...missingSelected];
        const keyword = this.keywordSector?.trim().toLowerCase();
        if (keyword) {
          combinedSelected = combinedSelected.filter(
            s =>
              (s.code && s.code.toLowerCase().includes(keyword)) ||
              (s.name && s.name.toLowerCase().includes(keyword))
          );
        }
        this.filterAgencySectors = combinedSelected;
      } else {
        this.filterAgencySectors = [];
      }
    } else {
      this.currentSectorPage = 0;
      this.listSector = [];
      this.filterAgencySectors = [];
      this.islastPageSector = false;
      this.getListSectorAll(this.pageSizeSector, this.currentSectorPage, this.keywordSector);
    }
  
    this.isShowingOnlyCheckedSectors = !this.isShowingOnlyCheckedSectors;
  }
  
  
}
