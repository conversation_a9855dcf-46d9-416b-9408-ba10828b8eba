import { Component, OnInit, Output, Input, EventEmitter, OnChanges, SimpleChanges} from '@angular/core';
import { FormGroup, FormBuilder, FormArray, FormControl, Validators } from '@angular/forms';
import { EnvService } from 'src/app/core/service/env.service';
import { AdapterService } from 'src/app/data/service/adapter/adapter.service';
import { DeploymentService } from 'src/app/data/service/deployment.service';
import { SnackbarService } from 'src/app/data/service/snackbar/snackbar.service';
import {GetCitizenInfoService} from 'shared/components/get-citizen-info/get-citizen-info.service';
import {HttpClient} from "@angular/common/http";
import { MatDatepickerInputEvent } from '@angular/material/datepicker';
import {ConfirmReportComponent, ConfirmReportDialogModel} from 'shared/components/dialogs/confirm-report/confirm-report.component';
import * as tUtils from 'src/app/data/service/thoai.service';
import {MatDialog} from '@angular/material/dialog';
import {KeycloakService} from "keycloak-angular";
import { UserService } from 'data/service/user.service';
import { ProcedureService } from 'src/app/data/service/procedure/procedure.service';

@Component({
  selector: 'app-household-info',
  templateUrl: './household-info.component.html',
  styleUrls: ['./household-info.component.scss']
})
export class HouseholdInfoComponent implements OnInit, OnChanges {
  configV2 = this.deploymentService.getAppDeployment();
  isKTMCitizenStatistic = this.deploymentService.env?.OS_KTM?.isKTMCitizenStatistic ? this.deploymentService.env?.OS_KTM?.isKTMCitizenStatistic : false;
  limitSearchCitizenPros = this.configV2?.limitSearchCitizenPros ? this.configV2?.limitSearchCitizenPros > 0 : false;
  requiredCitizenInfoConfig = this.configV2?.requiredCitizenInfoConfig ? this.configV2?.requiredCitizenInfoConfig > 0 : false;
  searchProcedureId = null;

    memberIdentityNumbers = "";
    data = {
      anyPermissions: ['oneGateResidentialInfo' , 'oneGateHouseholdInfo'] // Danh sách quyền của user
    };
    @Output() formValueChanged = new EventEmitter<any>(); // Event để phát dữ liệu
    @Input() dataHouseHoldBDH: any;
    @Input() checkId: any;
    @Input() dossierId: string;
    @Input() dossierCode: string;
    // @Input() eformId:string;
    @Input() procedureId:string;
    memberForms: FormGroup;
    productForm: FormGroup;
    receiveData = new FormGroup({
      fullNameOwner: new FormControl(''),
      identityOwner: new FormControl(''),
      birthDayOwner: new FormControl(''),
  
    });
    pickers: any[] = [];
    birthdateErrors: string[] = [];
    fullNameOwner = "";
    identityOwner = "";
    birthDayOwner = "";
    identityMember = "";
    ELEMENTDATA = null;
    config = this.envService.getConfig();
    correct: any;
    message: string;
    items: any;
    key: '';
    value: '';
    eformId = '';
    agencyId = '';
    subsystemId = this.deploymentService.env.subsystemId;
    checkIdentity: any;
    checkCitizen = this.deploymentService.getAppDeployment()?.checkCitizen;
    checkCH ='';
    checkTV='';
    checkHT='';
    duplicateError = false; // Biến lưu trạng thái trùng lặp
    checkIdFlex: string = "20";
    getOriginInfo= this.checkCitizen?.getOriginInfo ? this.checkCitizen.getOriginInfo:false;
    env = this.deploymentService.getAppDeployment()?.env;
    userName: string;
    accountId: string;
    isShowHousehold=false;
  
    selectedLang: string;

    constructor(private fb: FormBuilder,
      private envService: EnvService,
      private snackbarService: SnackbarService,
      private adapterService: AdapterService,
      private deploymentService: DeploymentService,
      private getCitizenInfoService: GetCitizenInfoService,
      private http: HttpClient,
      private dialog: MatDialog,
      private keycloakService: KeycloakService,
      private userService: UserService,
      private procedureService: ProcedureService,
    ) {
      const userAgency = JSON.parse(localStorage.getItem('userAgency'));
      this.agencyId = userAgency.id;
      this.productForm = this.fb.group({
        fullNameOwner: '',
        identityOwner: '',
        birthDayOwner: '',
        checkCH: '',
        checkTV: '',
        quantities: this.fb.array([])
      });
      this.pickers = [true]; // Khởi tạo tham chiếu cho từng dòng
    }
  
    ngOnInit(): void {
      console.log('Dữ liệu nhận từ cha khi khởi tạo:', this.dataHouseHoldBDH);
      const permissions = this.userService.getUserPermissions();
      for(const p of permissions){
          if(p.permission.code ==="oneGateResidentialInfo" || p.permission.code ==="oneGateHouseholdInfo"){
              this.isShowHousehold = true
              break;
          }
      }
      this.checkIdFlex = this.checkId === '1' ? "33" : "20";
      if(typeof this.dataHouseHoldBDH !== 'undefined'){
        if(this.dataHouseHoldBDH?.length == 0 || this.dataHouseHoldBDH === undefined || this.dataHouseHoldBDH == null){
          this.addQuantity();
        }else{
          // Gán giá trị ban đầu cho chủ sở hữu (Owner)
          this.productForm.patchValue({
            fullNameOwner: this.dataHouseHoldBDH?.fullNameOwner,
            identityOwner: this.dataHouseHoldBDH?.identityOwner,
            birthDayOwner: new Date(this.dataHouseHoldBDH?.birthDayOwner)
          });
  
          // Thêm một phần tử vào FormArray 'quantities'
          if(typeof this.dataHouseHoldBDH?.memberHouseHold !== 'undefined'){
            for(let i =0; i< this.dataHouseHoldBDH?.memberHouseHold?.length; i++){
            this.addQuantityData({
              fullNameMember: this.dataHouseHoldBDH?.memberHouseHold[i].fullNameMember,
              birthDayMember: new Date(this.dataHouseHoldBDH?.memberHouseHold[i].birthDayMember),
              identityMember: this.dataHouseHoldBDH?.memberHouseHold[i].identityMember,
              relationMember: this.dataHouseHoldBDH?.memberHouseHold[i].relationMember,
              permanentAddressMember: this.dataHouseHoldBDH?.memberHouseHold[i].permanentAddressMember,
              currentAddressMember: this.dataHouseHoldBDH?.memberHouseHold[i].currentAddressMember,
              check: this.dataHouseHoldBDH?.memberHouseHold[i].check,
              checkInfo: this.dataHouseHoldBDH?.memberHouseHold[i].checkInfo
            });
            }
          }else{
            this.addQuantity();
          }
        }
      }else{
        this.addQuantity();
      }
      this.getUserAccount();
      // Lắng nghe thay đổi trong form
      this.productForm.valueChanges.subscribe((value) => {
        this.formValueChanged.emit(value); // Phát dữ liệu lên parent
      });
      
    }
    ngOnChanges(changes: SimpleChanges) {
      if (changes['dataHouseHoldBDH'] && changes['dataHouseHoldBDH'].currentValue) {
        console.log('Dữ liệu mới từ cha:', changes['dataHouseHoldBDH'].currentValue);
      }
      this.checkIdFlex = this.checkId === '1' ? "33" : "20";
      if(typeof this.dataHouseHoldBDH !== 'undefined'){
        if(this.dataHouseHoldBDH.length == 0 || this.dataHouseHoldBDH === undefined || this.dataHouseHoldBDH == null){
          
        }else{
          // Gán giá trị ban đầu cho chủ sở hữu (Owner)
          this.productForm.patchValue({
            fullNameOwner: this.dataHouseHoldBDH.fullNameOwner,
            identityOwner: this.dataHouseHoldBDH.identityOwner,
            birthDayOwner: new Date(this.dataHouseHoldBDH.birthDayOwner),
            checkCH: this.dataHouseHoldBDH.checkCH,
            checkTV: this.dataHouseHoldBDH.checkTV,
          });
  
          // Thêm một phần tử vào FormArray 'quantities'
          if(typeof this.dataHouseHoldBDH.memberHouseHold !== 'undefined'){
            for(let i =0; i< this.dataHouseHoldBDH.memberHouseHold.length; i++){
              if(i==0){
                if(this.quantities().length > 0){
                  this.quantities().at(i).patchValue({
                    fullNameMember: this.dataHouseHoldBDH.memberHouseHold[i].fullNameMember,
                    birthDayMember: new Date(this.dataHouseHoldBDH.memberHouseHold[i].birthDayMember),
                    identityMember: this.dataHouseHoldBDH.memberHouseHold[i].identityMember,
                    relationMember: this.dataHouseHoldBDH.memberHouseHold[i].relationMember,
                    permanentAddressMember: this.dataHouseHoldBDH.memberHouseHold[i].permanentAddressMember,
                    currentAddressMember: this.dataHouseHoldBDH.memberHouseHold[i].currentAddressMember,
                    check: this.dataHouseHoldBDH.memberHouseHold[i].check,
                    checkInfo: this.dataHouseHoldBDH.memberHouseHold[i].checkInfo
                  }); 
                }else{
                  this.addQuantityData({
                    fullNameMember: this.dataHouseHoldBDH.memberHouseHold[i].fullNameMember,
                    birthDayMember: new Date(this.dataHouseHoldBDH.memberHouseHold[i].birthDayMember),
                    identityMember: this.dataHouseHoldBDH.memberHouseHold[i].identityMember,
                    relationMember: this.dataHouseHoldBDH.memberHouseHold[i].relationMember,
                    permanentAddressMember: this.dataHouseHoldBDH.memberHouseHold[i].permanentAddressMember,
                    currentAddressMember: this.dataHouseHoldBDH.memberHouseHold[i].currentAddressMember,
                    check: this.dataHouseHoldBDH.memberHouseHold[i].check,
                    checkInfo: this.dataHouseHoldBDH.memberHouseHold[i].checkInfo
                  });
                }
              }else{
                this.addQuantityData({
                  fullNameMember: this.dataHouseHoldBDH.memberHouseHold[i].fullNameMember,
                  birthDayMember: new Date(this.dataHouseHoldBDH.memberHouseHold[i].birthDayMember),
                  identityMember: this.dataHouseHoldBDH.memberHouseHold[i].identityMember,
                  relationMember: this.dataHouseHoldBDH.memberHouseHold[i].relationMember,
                  permanentAddressMember: this.dataHouseHoldBDH.memberHouseHold[i].permanentAddressMember,
                  currentAddressMember: this.dataHouseHoldBDH.memberHouseHold[i].currentAddressMember,
                  check: this.dataHouseHoldBDH.memberHouseHold[i].check,
                  checkInfo: this.dataHouseHoldBDH.memberHouseHold[i].checkInfo
                });
              }
            }
          }
        }
      }
    }
    getUserAccount() {
      if (this.env?.OS_DBN?.checkCitizenWithUserName) {
        this.keycloakService.loadUserProfile().then(user => {
          // tslint:disable-next-line: no-string-literal
          this.accountId = user['attributes'].user_id[0];
          this.userService.getUserInfo(this.accountId).subscribe(data => {
            this.userName = (!!data?.account?.username && data?.account?.username.length > 0) ? data?.account?.username[0]?.value : null;
            if (!!this.userName && !!this.env?.OS_DBN?.checkCitizenWithUserNameValue ){
              this.userName = this.userName + this.env?.OS_DBN?.checkCitizenWithUserNameValue;
            }
          });
        });
      }
    }
    checkValidProcedure() {
      return new Promise((resolve) => {
        this.procedureService.getProcedureDetail(this.procedureId).subscribe(procedureDetail => {
          if (procedureDetail.limitCheckCitizenFields == null) {
            let code = procedureDetail.code;
            let agencyId = JSON.parse(localStorage.getItem('userAgency')).id;
            let searchStr = `?sort=createdDate,desc&page=0&size=50&spec=page&agency-id=${agencyId}&status=1&common-use=true&fix-role=false&keyword=${code}`;
  
            this.procedureService.getListSearchProcedure(searchStr).subscribe(procedureCommonUse => {
              if (procedureCommonUse.numberOfElements == 0 ) {
                if(this.requiredCitizenInfoConfig){
                  this.snackbarService.openSnackBar(0, '', "Thủ tục này chưa cấu hình Dữ liệu được cho phép tra cứu!", 'error_notification', this.config.expiredTime);
                  resolve(false);
                }
                else
                  resolve(true);
              } 
              else {
                this.procedureService.getProcedureDetail(procedureCommonUse.content[0].id).subscribe(detail => {
                  if (detail.limitCheckCitizenFields == null) {
                    if(this.requiredCitizenInfoConfig){
                      this.snackbarService.openSnackBar(0, '', "Thủ tục này chưa cấu hình Dữ liệu được cho phép tra cứu!", 'error_notification', this.config.expiredTime);
                      resolve(false);
                    }
                    else
                      resolve(true);
                  } 
                  else{
                    this.searchProcedureId = this.procedureId;
                    resolve(true);
                  }
                });
              }
            });
          } 
          else{
            this.searchProcedureId = this.procedureId;
            resolve(true);
          }
        });
      });
    }
    getClientIp() {
      this.http.get<{ ip: string }>(!!this.deploymentService.env?.iPCheckUrl ? this.deploymentService.env?.iPCheckUrl : 'https://ip.vnptioffice.vn/?format=json')
        .subscribe(data => {
          localStorage.setItem('clientIP', data.ip);
        });
    }
    quantities(): FormArray {
      return this.productForm.get("quantities") as FormArray
    }
    newQuantity(): FormGroup {
      return this.fb.group({
        fullNameMember: '',
        birthDayMember: '',
        identityMember: '',
        relationMember: '',
        permanentAddressMember: '',
        currentAddressMember: '',
        check: '',
        checkInfo: '',
      })
    }
    // add input
    addQuantity() {
      this.quantities().push(this.newQuantity());
      this.pickers.push(true); // Thêm tham chiếu cho dòng mới
    }
    // Phương thức thêm phần tử vào FormArray
  addQuantityData(data?: any): void {
    const quantityGroup = this.newQuantity();

    // Nếu có dữ liệu truyền vào, gán giá trị cho FormGroup
    if (data) {
      quantityGroup.patchValue(data);
    }

    // Thêm FormGroup vào FormArray
    this.quantities().push(quantityGroup);
  }
  
    // Thêm một form mới
    // addForm(): void {
    //   this.memberForms.push(this.createMemberForm());
    // }
    // remove input
    removeQuantity(i: number) {
      this.quantities().removeAt(i);
    }
    validateDate(index: number): void {
      let formObj = this.productForm.getRawValue();
      const birthdateValue =formObj.quantities[index].birthdate;
      if (!birthdateValue) {
        this.birthdateErrors[index] = 'Trường này không được để trống!';
        return;
      }
  
      // Regex kiểm tra định dạng ngày/tháng/năm hoặc năm
      const dateRegex = /^\d{2}\/\d{2}\/\d{4}$/; // dd/mm/yyyy
      const yearRegex = /^\d{4}$/; // yyyy
  
      if (dateRegex.test(birthdateValue)) {
        this.birthdateErrors[index] = ''; // Định dạng ngày/tháng/năm hợp lệ
      } else if (yearRegex.test(birthdateValue)) {
        this.birthdateErrors[index] = ''; // Định dạng năm hợp lệ
      } else {
        this.birthdateErrors[index] = 'Ngày sinh không đúng định dạng! Nhập ngày/tháng/năm hoặc năm (yyyy).';
      }
    }
     // Khi người dùng nhập liệu
  onInputChange(event: any, index: number): void {
    const inputValue = event.target.value;
    const yearRegex = /^\d{4}$/; // yyyy
    const dateRegex = /^\d{2}\/\d{2}\/\d{4}$/; // dd/mm/yyyy
    let formObj = this.productForm.getRawValue();

    if (yearRegex.test(inputValue)) {
      // Người dùng nhập chỉ năm
      
      const birthdateValue =formObj.quantities[index].birthdate;
      formObj.quantities[index].birthdate?.setValue(inputValue);
      this.birthdateErrors[index] = '';
    } else if (dateRegex.test(inputValue)) {
      // Người dùng nhập ngày/tháng/năm
      formObj.quantities[index].birthdate?.setValue(inputValue);
      this.birthdateErrors[index] = '';
    } else {
      // Sai định dạng
      this.birthdateErrors[index] = 'Vui lòng nhập ngày/tháng/năm hoặc năm (yyyy).';
    }
  }

  // Khi người dùng chọn từ lịch
  onDateChange(event: MatDatepickerInputEvent<Date>, index: number): void {
    const selectedDate = event.value;
    let formObj = this.productForm.getRawValue();
    if (selectedDate) {
      formObj.quantities[index].birthdate?.setValue(selectedDate.toISOString().split('T')[0]); // Lưu định dạng ISO (yyyy-MM-dd)
      this.birthdateErrors[index] = '';
    }
  }

  // Khi người dùng chọn năm từ "multi-year" view
  onYearSelected(event: Date, index: number,  picker: any): void {
    const year = event.getFullYear();
    let formObj = this.productForm.getRawValue();
    formObj.quantities[index].birthdate?.setValue(year.toString());
    picker.close(); // Đóng picker sau khi chọn năm
    this.birthdateErrors[index] = '';
  }
  // Kiểm tra trùng lặp
  isDuplicate(): void {
    this.checkHT = '';
    const quantities = this.productForm.get('quantities') as FormArray;
    // const quantityGroup = quantities.controls[index] as FormGroup;
    const identityValues = quantities.controls.map(control => control.get('identityMember')?.value);
    // const cccdValues = this.productForm.getRawValue().quantities.controls.map(control => control.get('identityMember')?.value);
    const uniqueValues = new Set(identityValues);
    this.duplicateError = uniqueValues.size !== identityValues.length;
  }
    async checkInfo() {
      let formObj = this.productForm.getRawValue();
      let formObj_ho = this.receiveData.getRawValue();
  
      let checkEmptyInput = await this.checkValidAllInput(formObj.fullNameOwner, formObj.identityOwner, formObj.birthDayOwner)
      
      if (checkEmptyInput) {
        let checkIdentity = await this.checkValidIdentity(formObj.identityOwner + '');
        // check valid identity member 
        for (let i = 0; i < formObj.quantities.length; i++) {
          const identityMember = formObj.quantities[i].identityMember;
        if (identityMember.length !== 12 && identityMember !='') {
            this.snackbarService.openSnackBar(0, '', `CCCD thành viên ${i + 1} chưa đúng định dạng! Vui lòng nhập lại!`, 'error_notification', this.config.expiredTime);
            return;
          }
        }
        if (checkIdentity) {
          let y = formObj.birthDayOwner.getFullYear();
          let m = formObj.birthDayOwner.getMonth() + 1;
          if (m < 10) {
            m = '0' + m;
          }
          let d = formObj.birthDayOwner.getDate();
          if (d < 10) {
            d = '0' + d;
          }
        
    let outputfullName = formObj.fullNameOwner.toLowerCase().replace(/\s/g, '').split(' ').map((s) => s.charAt(0).toUpperCase() + s.substring(1)).join('');
  
    outputfullName = outputfullName.replace(/\s/g, '');
  
          let req = '';
          req += '&identity-owner=' + formObj.identityOwner;
          req += '&fullname-owner=' + outputfullName;
          if(m =='01' && d == '01'){
            req += '&birthday-owner=' + y;
          }else{
            req += '&birthday-owner=' + y + m + d;
          }
          req += '&identity-member=';
  
          let count = 0;
          for (let i = 0; i < formObj.quantities.length; i++) {
            count++;
            req += formObj.quantities[i].identityMember;
            
            this.memberIdentityNumbers += this.memberIdentityNumbers != "" ? ", " + formObj.quantities[i].identityMember : formObj.quantities[i].identityMember;
            if (count < formObj.quantities.length) {
              req += "&identity-member="; 
            }
          }
          if (count == formObj.quantities.length) {
            this.callApi(req);
          }
        }
      }
  
    }
  
    callApi(req) {
      if(!localStorage.getItem('clientIP')){
        this.getClientIp();
      }
      let formObj = this.productForm.getRawValue();
      let formObj_ho = this.receiveData.getRawValue();
      let param = ""
      param += "agency-id=" + this.agencyId;
      param += "&subsystem-id=" + this.subsystemId;
      this.receiveData.value
      param += req
      this.adapterService.getCsdldc034(param).subscribe(data => {
        //KTM luu log
        if(this.isKTMCitizenStatistic){
          const formObj_ho = this.receiveData.getRawValue();
          const userAgency = JSON.parse(localStorage.getItem('userAgency'));
  
          const log = {
            identityNumber: formObj?.identityOwner,
            name: formObj?.fullNameOwner,
            birtDay: formObj?.birthDayOwner,
            status: true,
            function: 1,
            url: location.href,
            agency: {
              id: userAgency?.id ? userAgency.id : this.config?.rootAgency?.id,
              name: userAgency?.name ? userAgency.name : this.config?.rootAgency?.trans?.vi?.name,
              code: userAgency?.code ? userAgency.code : this.config?.rootAgency?.code
            },
            ipAddress: this.isKTMCitizenStatistic ? localStorage.getItem('clientIP') : '',
            memberIdentityNumbers : this.memberIdentityNumbers,
            menuCode: '034',
            dossierId: this.dossierId,
            dossierCode: this.dossierCode
          };
  
          this.getCitizenInfoService.writeCheckCitizenLog(log).subscribe(rp => {});
          this.memberIdentityNumbers = "";
        }
  
        this.ELEMENTDATA = null;
        this.ELEMENTDATA = data;
        this.items = this.ELEMENTDATA.info.message.split(',').map(item => {
  
          const [key, value] = item.split(':');
          return {
            key: key.trim(),
            value: value.trim()
  
          };
  
        });
        this.checkHT= '1';
        let dem = 0;
        for (let i = 0; i < formObj.quantities.length; i++) {
          if(formObj.quantities[i].identityMember != ''){
            dem ++;
            for (let j = 0; j < this.items.length; j++) {
              if (formObj.quantities[i].identityMember == this.items[j].key) {
                formObj.quantities[i].check = this.items[j].value.toUpperCase(); 
                this.quantities().at(i).patchValue({
                  check: this.items[j].value.toUpperCase(),
                });
                if(formObj.quantities[i].check == 'NO'){
                  this.checkHT= '0';
                }
              }
            }
          }
        }
        if(dem == 0){
          const quantities = this.productForm.get('quantities') as FormArray;
          const quantityGroup = quantities.controls[0] as FormGroup;
          quantityGroup.patchValue({
            fullNameMember: formObj.fullNameOwner,
            birthDayMember: formObj.birthDayOwner,
            identityMember: formObj.identityOwner,
            checkInfo: 1,
            check:'YES',
          });
        }
        this.checkIdentity = formObj;
        this.checkIdentity;
        if(this.ELEMENTDATA.info.correct == 'true'){
          this.productForm.patchValue({
            checkCH: '1'
          });
        }else{
          this.productForm.patchValue({
            checkCH: '0'
          });
        }
  
      })
    }

    checkValidIndentity(indentity) {
      return new Promise((resolve) => {
        try {
          if (indentity.length !== 9 && indentity.length !== 12) {
            const msgObj = {
              vi: 'Chứng minh nhân dân hoặc căn cước công dân chưa đúng!',
              en: 'Identity proof is not correct!'
            };
            // this.ELEMENTDATA = null;
            this.snackbarService.openSnackBar(0, '', msgObj[this.selectedLang], 'error_notification', this.config.expiredTime);
            resolve(false);
          } else {
            resolve(true);
          }
        } catch (error) {
          resolve(true);
        }
      });
    }
    async checkInfoMember() {
      let formObj = this.productForm.getRawValue();
      if(this.limitSearchCitizenPros){
        let checkValidProcedure = await this.checkValidProcedure();
        if(!checkValidProcedure)
          return;
      }
      let demXT = 0;
      for (let i = 0; i < formObj.quantities.length; i++) {
        let checkEmptyInput = await this.checkValidAllInput(formObj.quantities[i].fullNameMember, formObj.quantities[i].identityMember, formObj.quantities[i].birthDayMember)
        if (checkEmptyInput) {
          let checkIndentity = await this.checkValidIndentity(formObj.quantities[i].identityMember + '');
          if (checkIndentity) {
            let y = formObj.quantities[i].birthDayMember.getFullYear();
            let m = formObj.quantities[i].birthDayMember.getMonth() + 1;
            if (m < 10) {
              m = '0' + m;
            }
            let d = formObj.quantities[i].birthDayMember.getDate();
            if (d < 10) {
              d = '0' + d;
            }
            let searchString = '';
            let birthday = y + m + d;
            if(m =='01' && d == '01'){
              birthday = y;
            }
            searchString += '&indentity-number=' + formObj.quantities[i].identityMember;
            searchString += '&fullname=' + formObj.quantities[i].fullNameMember;
            searchString += '&birthday=' + birthday;
            let searchStringData = "/citizen/--info?"
            searchStringData += "agency-id=" + this.agencyId;
            searchStringData += "&subsystem-id=" + this.subsystemId;
            this.productForm.value
            searchStringData += "&get-origin-info=true";
            // const formObj = this.productForm.getRawValue();
            searchStringData += searchString;
            
            const userAgency = JSON.parse(localStorage.getItem('userAgency'));
            const log = {
              identityNumber: formObj.quantities[i].identityMember,
              name: formObj.quantities[i].fullNameMember,
              birtDay: formObj.quantities[i].birthDayMember,
              status: true,
              function: 1,
              url: location.href,
              agency: {
                id: userAgency?.id ? userAgency.id : this.config?.rootAgency?.id,
                name: userAgency?.name ? userAgency.name : this.config?.rootAgency?.trans?.vi?.name,
                code: userAgency?.code ? userAgency.code : this.config?.rootAgency?.code
              },
              ipAddress: this.isKTMCitizenStatistic ? localStorage.getItem('clientIP') : '',
              menuCode: '037',
              dossierId: this.dossierId,
              dossierCode: this.dossierCode
            };
            this.getCitizenInfoService.getCitizenIfo(formObj.quantities[i].identityMember,formObj.quantities[i].fullNameMember,birthday,this.eformId, this.getOriginInfo, this.userName, this.searchProcedureId, this.dossierId, this.dossierCode).subscribe(
              async data => {
                  if (await this.checkInfoNull(data.originInfo)) {
                    const quantities = this.productForm.get('quantities') as FormArray;

                    for (let i = 0; i < quantities.length; i++) {
                      const quantityGroup = quantities.controls[i] as FormGroup; // Truy cập từng FormGroup trong FormArray

                      if (quantityGroup.get('identityMember')?.value === data.originInfo?.SoDinhDanh) {
                        quantityGroup.patchValue({
                          checkInfo: 1,
                          relationMember: data.originInfo?.ChuHo?.QuanHe ? quanHeMap[data.originInfo?.ChuHo?.QuanHe] : null,
                          fullNameMember: data.originInfo?.HoVaTen?.Ten,
                          birthDayMember: data.originInfo?.NgayThangNamSinh?.NgayThangNam
                            ? this.formatBirthDayRespon(data.originInfo.NgayThangNamSinh.NgayThangNam)
                            : '',
                          permanentAddressMember:
                            data.originInfo?.ThuongTru?.ChiTiet +
                            ', ' +
                            data.originInfo?.ThuongTru?.PhuongXa?.label +
                            ', ' +
                            data.originInfo?.ThuongTru?.QuanHuyen?.label +
                            ', ' +
                            data.originInfo?.ThuongTru?.TinhThanh?.label,
                          currentAddressMember:
                            data.originInfo?.NoiOHienTai?.ChiTiet +
                            ', ' +
                            data.originInfo?.NoiOHienTai?.PhuongXa?.label +
                            ', ' +
                            data.originInfo?.NoiOHienTai?.QuanHuyen?.label +
                            ', ' +
                            data.originInfo?.NoiOHienTai?.TinhThanh?.label,
                        });
                      }
                    }
                    this.getCitizenInfoService.writeCheckCitizenLog(log).subscribe(rp => {});
                  }else{
                    this.productForm.patchValue({
                      checkTV: '2'
                    });
                    demXT++;
                    log.status = false;
                    if(!this.checkCitizen.offConfirmErr ){
                      this.confirmReportCheckCitizenDialog(log);
                    }else{
                      this.getCitizenInfoService.writeCheckCitizenLog(log).subscribe(rp => {});
                    }
                  }
              }, err => {
                debugger
                const msgObj = err.error.message;
                this.snackbarService.openSnackBar(0, msgObj, '', 'error_notification', this.config.expiredTime);
                log.status = false;
                if(!this.checkCitizen.offConfirmErr ){
                  this.confirmReportCheckCitizenDialog(log);
                }else{
                  this.getCitizenInfoService.writeCheckCitizenLog(log).subscribe(rp => {});
                }
              });
          }
        }
      }
      if(demXT == 0){
        this.productForm.patchValue({
          checkTV: '1'
        });
      }
    }
    formatBirthDayRespon(birthDay) { //yyyymmdd
      // let y = birthDay.slice(0, 4);
      // let m = birthDay.slice(4, 6);
      // let d = birthDay.slice(6, 8);
      // return d + '/' + m + '/' + y
      const year = parseInt(birthDay.substring(0, 4), 10);
      const month = parseInt(birthDay.substring(4, 6), 10) - 1; // Tháng bắt đầu từ 0
      const day = parseInt(birthDay.substring(6, 8), 10);

      return new Date(year, month, day);
    }
    checkValidIdentity(identityOwner) {
      return new Promise((resolve) => {
        try {
          if (identityOwner.length !== 12) {
            this.snackbarService.openSnackBar(0, '', 'Chưa đúng căn cước công dân!', 'error_notification', this.config.expiredTime);
            resolve(false);
          } else {
            resolve(true);
          }
        } catch (error) {
          resolve(true);
        }
      });
    }
  
    checkValidAllInput(fullNameOwner, identityOwner, birthDayOwner) {
      return new Promise((resolve) => {
        try {
          if (fullNameOwner == '' || identityOwner == '' || birthDayOwner == '') {
            this.snackbarService.openSnackBar(0, '', 'Bạn cần nhập đầy đủ thông tin!', 'error_notification', this.config.expiredTime);
            resolve(false);
          } else {
            resolve(true);
          }
        } catch (error) {
          resolve(true);
        }
      })
    }

    checkInfoNull(originInfo){
      return new Promise((resolve) => {
        try {
          if (originInfo === null) {
            const msgObj = {
              vi: 'Thông tin nhập vào chưa đúng, vui lòng kiểm tra lại!',
              en: 'You need to enter all the information!'
            };
            // this.ELEMENTDATA = null;
            this.snackbarService.openSnackBar(0, '', msgObj[this.selectedLang], 'error_notification', this.config.expiredTime);
            resolve(false);
          } else {
            resolve(true);
          }
        } catch (error) {
          resolve(true);
        }
      })
    }
  confirmReportCheckCitizenDialog(log) {
      const dialogData = new ConfirmReportDialogModel('Phản hồi ý kiến', 'Thông tin hiện tại không tìm thấy, nếu bạn chắc chắn thông tin tìm kiếm là đúng vui lòng phản hồi cho chúng tôi! xin cám ơn');
      const dialogRef = this.dialog.open(ConfirmReportComponent, {
        minWidth: '600px',
        minHeight: '40vh',
        data: dialogData,
        disableClose: true,
        autoFocus: false
      });
      dialogRef.afterClosed().subscribe(async dialogResult => {
        const res = dialogResult;
        log.report = '';
        if (res.status === true) {
          log.report = res.data;
        }
        this.getCitizenInfoService.writeCheckCitizenLog(log).subscribe(data => {});
      });
    }
    userPermissions = ['oneGateResidentialInfo', 'viewDashboard']; // Giả định quyền user

  // Kiểm tra nếu user có quyền
  hasPermission(permission: string): boolean {
    return this.data.anyPermissions.includes(permission);
  }

  // Kiểm tra nếu user có bất kỳ quyền nào trong danh sách
  hasAnyPermission(): boolean {
    return this.data.anyPermissions.some(permission => this.userPermissions.includes(permission));
  }
  
}
export interface QuanHeMap {
  [key: string]: string;
}
const quanHeMap: QuanHeMap = {
  "00" : "Chưa có thông tin",
  "02" : "Cha",
  "0210" : "Cha đẻ",
  "0211" : "Ba",
  "0212" : "Bố",
  "0213" : "Tía",
  "0214" : "Cha chồng",
  "0215" : "Cha nuôi",
  "0216" : "Cha vợ",
  "03" : "Mẹ",
  "0310" : "Mẹ đẻ",
  "0311" : "Mẹ chồng",
  "0312" : "Mẹ nuôi",
  "0313" : "Mẹ vợ",
  "04" : "Vợ",
  "05" : "Chồng",
  "06" : "Ông",
  "0610" : "Ông nội",
  "0611" : "Ông ngoại",
  "07" : "Bà",
  "0710" : "Bà nội",
  "0711" : "Bà ngoại",
  "08" : "Con",
  "0810" : "Con đẻ",
  "0811" : "Con chồng",
  "0812" : "Con dâu",
  "0813" : "Con nuôi",
  "0814" : "Con rể",
  "0815" : "Con vợ",
  "09" : "Anh",
  "0910" : "Anh ruột",
  "0911" : "Anh chồng",
  "0912" : "Anh họ",
  "0913" : "Anh rể",
  "0914" : "Anh vợ",
  "10" : "Chị",
  "1010" : "Chị ruột",
  "1011" : "Chị chồng",
  "1012" : "Chị dâu",
  "1013" : "Chị họ",
  "1014" : "Chị vợ",
  "11" : "Em",
  "1110" : "Em ruột",
  "1111" : "Em chồng",
  "1112" : "Em dâu",
  "1113" : "Em họ",
  "1114" : "Em rể",
  "1115" : "Em vợ",
  "12" : "Cháu",
  "1210" : "Cháu nội",
  "1211" : "Cháu ngoại",
  "1212" : "Cháu rể",
  "1213" : "Cháu dâu",
  "1214" : "Cháu họ",
  "13" : "Bác",
  "14" : "Thím",
  "15" : "Cô",
  "16" : "Cậu",
  "17" : "Dì",
  "18" : "Chú",
  "19" : "Bạn",
  "20" : "Chắt",
  "21" : "Cụ",
  "22" : "Người giám hộ",
  "23" : "Người được giám hộ",
  "24" : "Người được chăm sóc",
  "25" : "Người được trợ giúp",
  "26" : "Người được nuôi dưỡng",
  "27" : "Người thuê nhà",
  "28" : "Người mượn nhà",
  "29" : "Người ở nhờ",
  "30" : "Nhân khẩu tập thể",
  "31" : "Đồng nghiệp - CA",
  "32" : "Đồng nghiệp - QĐ",
  "33" : "Cùng ở thuê",
  "99" : "Khác",
  "CH01" : "Chủ hộ",
};

interface GioiTinhMap {
  [key: string]: string;
}
const gioiTinhMap: GioiTinhMap = {
  0: "Chưa có thông tin",
  1: "Giới tính Nam",
  2: "Giới tính Nữ",
};

interface TinhTrangHonNhanMap {
  [key: string]: string;
}
const tinhTrangHonNhanMap: TinhTrangHonNhanMap = {
  0: "Chưa có thông tin",
  1: "Chưa kết hôn",
  2: "Đang có vợ/chồng",
  3: "Đã ly hôn hoặc góa vợ/chồng"
};

interface NhomMauMap {
  [key: string]: string;
}
const nhomMauMap: NhomMauMap = {
  "00": "Chưa có thông tin",
  "01": "Nhóm máu A",
  "02": "Nhóm máu B",
  "03": "Nhóm máu AB",
  "04": "Nhóm máu O"
};