import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { ListScheduleCtdtComponent } from './pages/list-schedule-ctdt-cto/list-schedule-ctdt-cto.component';
import { ReceivingCtdtComponent } from './pages/receiving-cto/receiving-cto.component';

const routes: Routes = [
  {
    path: '',
    children: [
      { path: '', component: ListScheduleCtdtComponent },
      {
        path: 'receiving-cto/:id',
        component: ReceivingCtdtComponent
      }
    ]
  },

];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class ReceiveScheduleCtdtRoutingModule { }
