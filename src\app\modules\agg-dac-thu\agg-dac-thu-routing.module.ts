import {RouterModule, Routes} from "@angular/router";
import {AuthGuard} from "core/guard/auth.guard";
import {NgModule} from "@angular/core";

const routes: Routes = [
  {
    path: 'agg-justice-vneid-check',
    loadChildren: () => import('./pages/agg-justice-vneid-check/router/agg-justice-vneid-check.module').then(m => m.AggJusticeVneidCheckModule),
    canActivate: [AuthGuard],
    data: {
      anyPermissions: ['onegateAggJusticeVneidCheck']
    }
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class AggDacThuRoutingModule { }
