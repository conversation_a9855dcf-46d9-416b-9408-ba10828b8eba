import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { AuthGuard } from 'src/app/core/guard/auth.guard';
import { AdministrativeComponent } from './pages/administrative/administrative.component';
const routes: Routes = [
  {
    path: '',
    // tslint:disable-next-line:max-line-length
    children: [
        { path: '', component: AdministrativeComponent },
        {
          path: 'list-administrative-book',
          loadChildren: () => import('./pages/administrative/administrative.module').then(m => m.AdministrativeModule)
        },
      ],
      canActivate: [AuthGuard],
    data: {
      anyPermissions: ['oneGateAdminMaster', 'oneGateHCMAdmin', 'oneGateAdministrativeCategory'],
    }
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class AdministrativeRoutingModule { }
