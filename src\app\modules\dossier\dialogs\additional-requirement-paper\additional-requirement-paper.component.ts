import { HomeService } from '../../../../data/service/home/<USER>';
import { ProcessService } from 'src/app/data/service/process/process.service';
import { Component, OnInit, Inject } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { KeycloakService } from 'keycloak-angular';
import { DossierService } from 'src/app/data/service/dossier/dossier.service';
import { UserService } from 'src/app/data/service/user.service';
import * as ClassicEditor from '@ckeditor/ckeditor5-build-classic/build/ckeditor';
import '@ckeditor/ckeditor5-build-classic/build/translations/vi';
import { EnvService } from 'src/app/core/service/env.service';
import { SnackbarService } from 'src/app/data/service/snackbar/snackbar.service';
import { DatePipe } from '@angular/common';
import { NotificationService } from 'src/app/data/service/notification.service';
import { DeploymentService } from 'src/app/data/service/deployment.service';
import * as tUtils from 'src/app/data/service/thoai.service';
import { ProcedureService } from 'src/app/data/service/procedure/procedure.service';
import { take } from 'rxjs/operators';
import {NotifyQNIService} from "shared/components/check-send-notify-qni/check-send-notify-qni.service";
import { PadmanService } from 'src/app/data/service/svc-padman/padman.service';
import { AgencyService } from 'src/app/data/service/basedata/agency.service';

@Component({
  selector: 'app-additional-requirement-paper',
  templateUrl: './additional-requirement-paper.component.html',
  styleUrls: ['./additional-requirement-paper.component.scss']
})
export class AdditionalRequirementPaperComponent implements OnInit {

  config = this.envService.getConfig();
  env: any = this.deploymentService.getAppDeployment()?.env;
  selectedLang = localStorage.getItem('language') || 'vi';
  selectedLangId = Number(localStorage.getItem('languageId')) || 228;
  dossierId: string;
  commentContent = '';
  commentContentPlainText = '';
  descriptionContent = '';
  userName: string;
  accountId: string;
  dossierCode: string;
  isCKMaxlenght = false;
  isCKDescriptionMaxlenght = false;
  dossierDetail: any;
  oldstatus = '';
  officers = [];
  agencyId: any;
  getApprovalAgency = '';
  currentTask: any;


  public Editor = ClassicEditor;
  commentConfig = {
    placeholder: 'Nhập lý do...',
    language: 'vi',
    removePlugins: ['MediaEmbed', 'ImageUpload'],
    toolbarLocation: 'bottom'
  };
  dossierTaskStatus = { id: '', name: [] };
  dossierMenuTaskRemind = { id: '', name: [] };

  // ================================================= Upload file
  uploaded: boolean;
  blankVal: any;
  uploadedImage = null;
  files = [];
  urls = [];
  fileNames = [];
  fileNamesFull = [];
  listProcedureProcess = [];

  isFieldArrayInvalid = false;
  acceptFileExtension = this.config.acceptFileExtension;
  acceptFileType = this.config.acceptFileType;
  maxFileSize = this.env?.maxFileSize || this.config.maxFileSize;
  //Thu tuc co cau hinh dong bo BXD-LGSP Tan Tan
  isSyncDBNLGSPTanDanBXD = 0;
  statusName = "";
  fileTemplate = "";
  typeProcess = 1;

  notifyQNI = this.deploymentService.getAppDeployment()?.notifyqni?.enable;

  totalCost = '';

  ckeditorMaxLengthNotification = "";
  ckeditorMaxLength = this.deploymentService.env.OS_HCM.ckeditorMaxLength;
  checkRequireAdditionalRequest = this.env?.OS_BDG?.isRequiredUploadFileBDG?.additional == false ? false : true;
  requestToSubmitPaperApplication = this.deploymentService.env?.OS_HCM?.requestToSubmitPaperApplication ? this.deploymentService.env?.OS_HCM?.requestToSubmitPaperApplication : {
    dossierTaskStatus: "64111af15706bf6444283feb",
    dossierMenuTaskRemind: "640e796667b52157543bccd0",
  }; 
  requestToSubmitPaperApplicationStatusId = 21;
  isSmsQNM = this.env?.OS_QNM?.isSmsQNM ? true : false;

  constructor(
    private userService: UserService,
    private envService: EnvService,
    private keycloakService: KeycloakService,
    public dialogRef: MatDialogRef<AdditionalRequirementPaperComponent>,
    @Inject(MAT_DIALOG_DATA) public data: ConfirmAdditionalRequirementPaperDialogModel,
    private snackbarService: SnackbarService,
    private datePipe: DatePipe,
    private notiService: NotificationService,
    private deploymentService: DeploymentService,
    private procedureService: ProcedureService,
    private dossierService: DossierService,
    private processService: ProcessService,
    private homeService : HomeService,
    private notifyQNIService: NotifyQNIService,
    private padmanService: PadmanService,
    private agencyService: AgencyService
  ) {
    this.dossierId = data.dossierId;
    this.dossierCode = data.dossierCode;
    this.typeProcess = data.typeProcess;
    if (this.typeProcess === 2){
      this.env.enableApprovalOfLeadership = 2;
    }
  }

  ngOnInit(): void {
    this.getUserAccount();
    this.getDossierTaskStatus();
    this.getDossierMenuTaskRemind();
    this.getDetailDossier();
    this.setEnvVariable();
    if (this.selectedLang === 'vi') {
      this.ckeditorMaxLengthNotification = "Nội dung không quá 500 ký tự!";
      if (this.ckeditorMaxLength > 0)
        this.ckeditorMaxLengthNotification = "Nội dung không quá "+ this.ckeditorMaxLength + " ký tự!";
    } else if (this.selectedLang === 'en') {
      this.ckeditorMaxLengthNotification = "Content must not exceed 500 characters!";
      if (this.ckeditorMaxLength > 0)
        this.ckeditorMaxLengthNotification = "Content must not exceed "+ this.ckeditorMaxLength + " characters!";
    }
  }

  setEnvVariable(){
    this.fileTemplate = !!this.env?.fileTemplate?.requireAdditional ? this.env?.fileTemplate?.requireAdditional : this.config.requireAdditionalTemplateFile;
  }

  getUserAccount() {
    this.keycloakService.loadUserProfile().then(user => {
      // tslint:disable-next-line: no-string-literal
      this.accountId = user['attributes'].user_id[0];
      this.userService.getUserInfo(this.accountId).subscribe(data => {
        this.userName = data.fullname;
      });
    });
  }
  getDossierTaskStatus() {
    // const tagId = this.env?.enableApprovalOfLeadership == 1 ? this.deploymentService.env.dossierTaskStatus.waitingForApprovalToAddition.id : this.deploymentService.env.dossierTaskStatus.requestForAdditionalDocuments.id;
    const tagId = this.requestToSubmitPaperApplication.dossierTaskStatus;
    this.dossierService.getAgencyTag(tagId).subscribe(rs => {
      this.dossierTaskStatus.id = rs.id;
      this.dossierTaskStatus.name = rs.trans;
    }, err => {
      console.log(err);
    });
  }
  getDossierMenuTaskRemind() {
    // const tagId = this.env?.enableApprovalOfLeadership == 1 ? this.deploymentService.env.dossierMenuTaskRemind.waitingForApprovalToAddition.id : this.deploymentService.env.dossierMenuTaskRemind.requestForAdditional.id;
    const tagId = this.requestToSubmitPaperApplication.dossierMenuTaskRemind;
    this.dossierService.getAgencyTag(tagId).subscribe(rs => {
      this.dossierMenuTaskRemind.id = rs.id;
      this.dossierMenuTaskRemind.name = rs.trans;
      this.statusName = rs?.trans.filter(t => t.languageId === Number(this.selectedLangId))?.name;
    }, err => {
      console.log(err);
    });
  }

  async getDetailDossier() {
    await this.getDossierFeeNotify();
    this.dossierService.getDossierDetail(this.dossierId).subscribe(data => {
      this.dossierDetail = data;
      this.getProcedureDetail(data?.procedure?.id);
      this.agencyId = !!data?.agency?.id ? data?.agency?.id : data?.agency?.parent?.id;
      if (!!data.currentTask && data.currentTask.length > 0){
        this.currentTask = data.currentTask[0];
      }
      if(!!this.notifyQNI){
        this.notifyQNIService.checkSendSubject.next(
          {
            id: data?.procedureProcessDefinition?.id,
            phone: data?.applicant?.data?.phoneNumber,
            email: data?.applicant?.data?.email,
            currentTask: this.currentTask,
            contentParams: {
              parameters: {
                sector: !!data?.procedure?.sector?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name ? data?.procedure?.sector?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name : '',
                procedure: !!data?.procedure?.translate?.find(agency => agency.languageId === Number(this.selectedLangId))?.name ? data?.procedure?.translate?.find(agency => agency.languageId === Number(this.selectedLangId))?.name : '',
                agency: !!data?.agency?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name ? data?.agency?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name : '',
                agencyId: !!data?.agency?.id ? data?.agency?.id : data?.agency?.parent?.id,
                applicantFullname: data?.applicant?.data?.fullname,
                officerFullname: '',
                subsystem: !!this.env?.subsystem?.name ? this.env?.subsystem?.name : "Hệ thống Một cửa điện tử",
                dossierCode: data?.code,
                nextTask: !!this.env?.notify?.additionalRequirement?.nextTask ? this.env?.notify?.additionalRequirement?.nextTask : 'Yêu cầu bổ sung hồ sơ',
                dossierSearchPadsvcUrl: (!!this.env?.domain?.padsvc ? this.env?.domain?.padsvc : "http://padsvctest.vncitizens.vn/vi/") + "dossier/public",
                dossierDetailPadsvcUrl: (!!this.env?.domain?.padsvc ? this.env?.domain?.padsvc : "http://padsvctest.vncitizens.vn/vi/") + "dossier/detail/" + this.dossierId,
                dossierSearchUrl: (!!this.env?.domain?.onegate ? this.env?.domain?.onegate : "http://padsvctest.vncitizens.vn/vi/") + "dossier/search/",
                dossierDetailUrl: (!!this.env?.domain?.onegate ? this.env?.domain?.onegate : "http://padsvctest.vncitizens.vn/vi/") + "dossier/search/" + this.dossierId,
                returnMethod: !!data?.dossierReceivingKind?.name?.find(recieve => recieve.languageId === Number(this.selectedLangId))?.name ? data?.dossierReceivingKind?.name?.find(recieve => recieve.languageId === Number(this.selectedLangId))?.name : '',
                receivingDate: '',
                appointmentDate: '',
                dossierFee: this.totalCost,
                reason: this.commentContentPlainText
              }
            }
          }
        );
      } else {
        this.notiService.checkSendSubject.next(
          {
            id: data?.procedureProcessDefinition?.id,
            phone: data?.applicant?.data?.phoneNumber,
            email: data?.applicant?.data?.email,
            currentTask: this.currentTask,
            contentParams: {
              parameters: {
                sector: !!data?.procedure?.sector?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name ? data?.procedure?.sector?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name : '',
                procedure: !!data?.procedure?.translate?.find(agency => agency.languageId === Number(this.selectedLangId))?.name ? data?.procedure?.translate?.find(agency => agency.languageId === Number(this.selectedLangId))?.name : '',
                agency: !!data?.agency?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name ? data?.agency?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name : data?.agency?.parent?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name,
                agencyId: !!data?.agency?.id ? data?.agency?.id : data?.agency?.parent?.id,
                applicantFullname: data?.applicant?.data?.fullname,
                officerFullname: '',
                subsystem: !!this.env?.subsystem?.name ? this.env?.subsystem?.name : "Hệ thống Một cửa điện tử",
                dossierCode: data?.nationCode ? data?.nationCode : data?.code,
                nextTask: !!this.env?.notify?.additionalRequirement?.nextTask ? this.env?.notify?.additionalRequirement?.nextTask : 'Yêu cầu bổ sung hồ sơ',
                dossierSearchPadsvcUrl: (!!this.env?.domain?.padsvc ? this.env?.domain?.padsvc : "http://padsvctest.vncitizens.vn/vi/") + "dossier/public",
                dossierDetailPadsvcUrl: (!!this.env?.domain?.padsvc ? this.env?.domain?.padsvc : "http://padsvctest.vncitizens.vn/vi/") + "dossier/detail/" + this.dossierId,
                dossierSearchUrl: (!!this.env?.domain?.onegate ? this.env?.domain?.onegate : "http://padsvctest.vncitizens.vn/vi/") + "dossier/search/",
                dossierDetailUrl: (!!this.env?.domain?.onegate ? this.env?.domain?.onegate : "http://padsvctest.vncitizens.vn/vi/") + "dossier/search/" + this.dossierId,
                returnMethod: !!data?.dossierReceivingKind?.name?.find(recieve => recieve.languageId === Number(this.selectedLangId))?.name ? data?.dossierReceivingKind?.name?.find(recieve => recieve.languageId === Number(this.selectedLangId))?.name : '',
                receivingDate: '',
                appointmentDate: '',
                dossierFee: this.totalCost,
                reason: this.commentContentPlainText
              }
            }
          }
        );
      }
      if (this.dossierDetail.dossierTaskStatus === undefined && this.dossierDetail.dossierTaskStatus === null) {
        // this.putDossierStatus();
      }
      else {
        this.oldstatus = this.dossierDetail.dossierTaskStatus.name;
      }
    });
  }
  
  getDossierFeeNotify() {
    return new Promise<void>(resolve => {
      this.padmanService.getDossierFee(this.dossierId).toPromise().then(res => {
        if(res && res.length > 0)
        {
          let totalCostValue = 0;
          let totalPaidValue = 0;
          res.forEach(element => {
            totalCostValue += element.quantity * element.amount;
            totalPaidValue += element.paid;
          });
          const monetaryUnit = res[0]?.monetaryUnit ? res[0].monetaryUnit : 'VNĐ';
          let total = totalCostValue - totalPaidValue;
          this.totalCost = total > 0 ? Number((total).toFixed(1)).toLocaleString() + ' ' + monetaryUnit : '';
        }
        resolve();
      }).catch(err => {
        resolve();
      });
    });
  }

  getProcedureDetail(procedureId) {
    this.procedureService.getProcedureDetail(procedureId).subscribe(async data => {
      this.isSyncDBNLGSPTanDanBXD = data?.isSyncDBNLGSPTanDanBXD ? Number(data?.isSyncDBNLGSPTanDanBXD) : 0;
    }, err => {
      console.log(err);
    });
  }
  putDossierStatus() {
    const requestBodyObj = [
      {
        id: this.dossierId,
        dossierStatus: 2,
        dossierTaskStatus: null
      }
    ];
    const requestBody = JSON.stringify(requestBodyObj, null, 2);
    this.dossierService.putDossierStatus(requestBody).subscribe(data => {
      if (data.affectedRows === 1) {
        this.getDetailDossier();
      }
    });
  }

  postHistory() {
    let newStatus = '';
    if (this.dossierTaskStatus.name.length > 0) {
      newStatus = this.dossierTaskStatus.name[0].name;
      this.dossierTaskStatus.name.forEach(element => {
        if (element.languageId === Number(localStorage.getItem('languageId'))) {
          newStatus = element.name;
        }
      });
    }
    const content = {
      groupId: 1,
      itemId: this.dossierId,
      user: {
        id: this.accountId,
        name: this.userName
      },
      type: 3,
      deploymentId: this.config.deploymentId,
      action: [
        {
          fieldNameRbk: 'lang.word.status',
          originalValue: this.oldstatus,
          newValue: newStatus
        }
      ]
    };
    this.dossierService.postHistory(JSON.stringify(content, null, 2)).subscribe(data => {

    });
  }

  GetListUserByPermission(agency) {
    return new Promise<void>((resolve) => {
      this.processService.getApprovalAgency(agency.id).pipe(take(1)).subscribe(async data => {
        console.log('data', data);
        if (!!data && data.content.length !== 0) {
          this.getApprovalAgency = data.content[0].approvalAgency.id;
        }
        else{
          if (!!agency.parent?.id){
            await this.GetListUserByPermissionParent(agency.parent?.id);
          }
        }
        if (this.getApprovalAgency == '') {
          this.getApprovalAgency = agency.id;
        }
        let permission = "additionalRequirementDossierApproval";
        this.processService.getListUserByPermissionCode(this.getApprovalAgency, permission).pipe(take(1)).subscribe(data2 => {
          this.officers = data2;
          resolve();
        }, (err) => {
          resolve();
        });
      });
    });
  }

  GetListUserByPermissionParent(agencyId) {
    return new Promise<void>((resolve) => {
      this.processService.getApprovalAgency(agencyId).pipe(take(1)).subscribe(data => {
        console.log('data', data);
        if (!!data && data.content.length !== 0) {
          this.getApprovalAgency = data.content[0].approvalAgency.id;
        }
        // if (this.getApprovalAgency == '') {
        //   this.getApprovalAgency = agencyId;
        // }
        resolve();
      }, (err) => {
        resolve();
      });
    });
  }


  async onConfirm() {
    if (!!this.currentTask && !!this.currentTask.candidateGroup[0] && !!this.currentTask.candidateGroup[0].id){
      await this.GetListUserByPermission(this.currentTask.candidateGroup[0]);
    }
    if (this.env?.enableApprovalOfLeadership != 2 && this.officers.length == 0) {
      this.homeService.error('Cán bộ phê duyệt hồ sơ của đơn vị chưa được cấu hình. \r\n Vui lòng cấu hình cán bộ phê duyệt để tiếp tục!',
        'Approval officer has not yet been configured \r\n Please configure the approval officer to continue!');
      const msgObj = {
        vi: 'Cán bộ phê duyệt hồ sơ của đơn vị chưa được cấu hình',
        en: 'Approval officer has not yet been configured'
      };
      this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
      return
    }

    if (this.commentContent.trim() === '') {
      const msgObj = {
        vi: 'Vui lòng nhập ý lý do',
        en: 'Please enter a handling comments!'
      };
      this.snackbarService.openSnackBar(2, '', msgObj[this.selectedLang], 'warning_notification', this.config.expiredTime);
    }
    if (!this.isCKMaxlenght && this.commentContent.trim() !== '') {
      const requestBodyObj = {
        // dossierStatus: this.env?.enableApprovalOfLeadership == 1 ? 8 : 1,
        dossierStatus: this.requestToSubmitPaperApplicationStatusId,
        comment: '',
        description: '',
        dossierTaskStatus: this.dossierTaskStatus,
        dossierMenuTaskRemind: this.dossierMenuTaskRemind
      };
      if (this.commentContent.trim() !== '') {
        this.postComment(this.commentContent.trim(), '');
        requestBodyObj.comment = this.commentContent.trim();
        requestBodyObj.description = '';
      } else {
        const msgObj = {
          vi: 'Yêu cầu bổ sung hồ sơ <b>' + this.dossierCode + '</b>',
          en: 'Additional requirement with dossier <b>' + this.dossierCode + '</b>!'
        };
        this.postComment(msgObj[this.selectedLang]);
        requestBodyObj.comment = msgObj[this.selectedLang];
      }
      this.dossierService.putDossierStatusWithComment(this.dossierId, requestBodyObj).subscribe(data => {});
      const agencies = this.agencyService.getAgencies();
      const extend = {
        dossier:{
          id: this.dossierId,
          code: this.dossierCode,
        },
        agencies: agencies
      };
      if(this.isSmsQNM) {
        await this.notiService.changeSendSubject.next(
          {
            id: this.dossierDetail?.procedureProcessDefinition?.id,
            phone: this.dossierDetail?.applicant?.data?.phoneNumber,
            email: this.dossierDetail?.applicant?.data?.email,
            currentTask: this.currentTask,
            contentParams: {
              parameters: {
                sector: !!this.dossierDetail?.procedure?.sector?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name ? this.dossierDetail?.procedure?.sector?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name : '',
                procedure: !!this.dossierDetail?.procedure?.translate?.find(agency => agency.languageId === Number(this.selectedLangId))?.name ? this.dossierDetail?.procedure?.translate?.find(agency => agency.languageId === Number(this.selectedLangId))?.name : '',
                agency: !!this.dossierDetail?.agency?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name ? this.dossierDetail?.agency?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name : this.dossierDetail?.agency?.parent?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name,
                agencyId: !!this.dossierDetail?.agency?.id ? this.dossierDetail?.agency?.id : this.dossierDetail?.agency?.parent?.id,
                applicantFullname: this.dossierDetail?.applicant?.data?.fullname,
                officerFullname: '',
                subsystem: !!this.env?.subsystem?.name ? this.env?.subsystem?.name : "Hệ thống Một cửa điện tử",
                dossierCode: this.dossierDetail?.nationCode ? this.dossierDetail?.nationCode : this.dossierDetail?.code,
                nextTask: !!this.env?.notify?.additionalRequirement?.nextTask ? this.env?.notify?.additionalRequirement?.nextTask : 'Yêu cầu bổ sung hồ sơ',
                dossierSearchPadsvcUrl: (!!this.env?.domain?.padsvc ? this.env?.domain?.padsvc : "http://padsvctest.vncitizens.vn/vi/") + "dossier/public",
                dossierDetailPadsvcUrl: (!!this.env?.domain?.padsvc ? this.env?.domain?.padsvc : "http://padsvctest.vncitizens.vn/vi/") + "dossier/detail/" + this.dossierId,
                dossierSearchUrl: (!!this.env?.domain?.onegate ? this.env?.domain?.onegate : "http://padsvctest.vncitizens.vn/vi/") + "dossier/search/",
                dossierDetailUrl: (!!this.env?.domain?.onegate ? this.env?.domain?.onegate : "http://padsvctest.vncitizens.vn/vi/") + "dossier/search/" + this.dossierId,
                returnMethod: !!this.dossierDetail?.dossierReceivingKind?.name?.find(recieve => recieve.languageId === Number(this.selectedLangId))?.name ? this.dossierDetail?.dossierReceivingKind?.name?.find(recieve => recieve.languageId === Number(this.selectedLangId))?.name : '',
                receivingDate: '',
                appointmentDate: '',
                dossierFee: this.totalCost,
                reason: this.commentContentPlainText,
                extend: extend
              }
            }
          }
        );
      }
      else {
        await this.notiService.changeSendSubject.next(
          {
            id: this.dossierDetail?.procedureProcessDefinition?.id,
            phone: this.dossierDetail?.applicant?.data?.phoneNumber,
            email: this.dossierDetail?.applicant?.data?.email,
            currentTask: this.currentTask,
            contentParams: {
              parameters: {
                sector: !!this.dossierDetail?.procedure?.sector?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name ? this.dossierDetail?.procedure?.sector?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name : '',
                procedure: !!this.dossierDetail?.procedure?.translate?.find(agency => agency.languageId === Number(this.selectedLangId))?.name ? this.dossierDetail?.procedure?.translate?.find(agency => agency.languageId === Number(this.selectedLangId))?.name : '',
                agency: !!this.dossierDetail?.agency?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name ? this.dossierDetail?.agency?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name : this.dossierDetail?.agency?.parent?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name,
                agencyId: !!this.dossierDetail?.agency?.id ? this.dossierDetail?.agency?.id : this.dossierDetail?.agency?.parent?.id,
                applicantFullname: this.dossierDetail?.applicant?.data?.fullname,
                officerFullname: '',
                subsystem: !!this.env?.subsystem?.name ? this.env?.subsystem?.name : "Hệ thống Một cửa điện tử",
                dossierCode: this.dossierDetail?.nationCode ? this.dossierDetail?.nationCode : this.dossierDetail?.code,
                nextTask: !!this.env?.notify?.additionalRequirement?.nextTask ? this.env?.notify?.additionalRequirement?.nextTask : 'Yêu cầu bổ sung hồ sơ',
                dossierSearchPadsvcUrl: (!!this.env?.domain?.padsvc ? this.env?.domain?.padsvc : "http://padsvctest.vncitizens.vn/vi/") + "dossier/public",
                dossierDetailPadsvcUrl: (!!this.env?.domain?.padsvc ? this.env?.domain?.padsvc : "http://padsvctest.vncitizens.vn/vi/") + "dossier/detail/" + this.dossierId,
                dossierSearchUrl: (!!this.env?.domain?.onegate ? this.env?.domain?.onegate : "http://padsvctest.vncitizens.vn/vi/") + "dossier/search/",
                dossierDetailUrl: (!!this.env?.domain?.onegate ? this.env?.domain?.onegate : "http://padsvctest.vncitizens.vn/vi/") + "dossier/search/" + this.dossierId,
                returnMethod: !!this.dossierDetail?.dossierReceivingKind?.name?.find(recieve => recieve.languageId === Number(this.selectedLangId))?.name ? this.dossierDetail?.dossierReceivingKind?.name?.find(recieve => recieve.languageId === Number(this.selectedLangId))?.name : '',
                receivingDate: '',
                appointmentDate: '',
                dossierFee: this.totalCost,
                reason: this.commentContentPlainText
              }
            }
          }
        );
      }
      if(!!this.notifyQNI){
        this.notifyQNIService.confirmSendSubject.next({
          confirm: true,
          renewContent: true,
        });
      } else {
        this.notiService.confirmSendSubject.next({
          confirm: true,
          renewContent: true,
        });
      }
      this.dialogRef.close(true);
    }
  }

  onDismiss() {
    this.dialogRef.close();
  }

  postComment(commentContent, description?:string) {
    let msgObj = {};
    if(!!description){
      msgObj = {
        vi: `Yêu cầu bổ sung: ${commentContent}<br>Nội dung: ${description}`,
        en: `Additional requirement: ${commentContent}<br>Description: ${description}`
      };
    }else{
      msgObj = {
        vi: `Yêu cầu bổ sung: ${commentContent}`,
        en: `Additional requirement: ${commentContent}`
      };
    }
    const content = {
      groupId: 2,
      itemId: this.dossierId,
      user: {
        id: this.accountId,
        fullname: this.userName
      },
      content: msgObj[this.selectedLang],
      file: this.uploadedImage
    };
    const requestBody = JSON.stringify(content, null, 2);
    this.dossierService.postComment(requestBody).subscribe(data => { });
  }

  getPlainText( strSrc ) {
    let resultStr = "";

    // Ignore the <p> tag if it is in very start of the text
    if(strSrc.indexOf('<p>') == 0)
        resultStr = strSrc.substring(3);
    else
        resultStr = strSrc;

    // Replace <p> with two newlines
    resultStr = resultStr.replace(/<p>/gi, "\r\n\r\n");
    // Replace <br /> with one newline
    resultStr = resultStr.replace(/<br \/>/gi, "\r\n");
    resultStr = resultStr.replace(/<br>/gi, "\r\n");

    // Replace &nbsp; with space
    resultStr = resultStr.replace(/&nbsp;/gi, " ");

    //-+-+-+-+-+-+-+-+-+-+-+
    // Strip off other HTML tags.
    //-+-+-+-+-+-+-+-+-+-+-+

    return  resultStr.replace( /<[^<|>]+?>/gi,'' );
  }

  onCommentEditorChange(event) {
    this.commentContent = event.editor.getData();
    if (this.ckeditorMaxLength > 0) {
      if (event.editor.getData().trim().length > this.ckeditorMaxLength) {
        this.isCKMaxlenght = true;
      } else {
        this.isCKMaxlenght = false;
      }
    } else if (event.editor.getData().trim().length > 500) {
      this.isCKMaxlenght = true;
    } else {
      this.isCKMaxlenght = false;
    }

    this.commentContentPlainText = "";
    this.commentContentPlainText = this.getPlainText(this.commentContent);
  }
}

export class ConfirmAdditionalRequirementPaperDialogModel {
  constructor(public dossierId: string, public dossierCode: string, public typeProcess?) {
  }
}
