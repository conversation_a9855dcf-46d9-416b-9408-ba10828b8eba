import { Injectable, ViewChild } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { ApiProviderService } from 'src/app/core/service/api-provider.service';
import { Observable } from 'rxjs';
import { EnvService } from 'src/app/core/service/env.service';
import * as VNPTCA from 'src/assets/js/vnpt-plugin.js';
import { ConfigService } from 'src/app/data/service/dossier/config.service';
import { LogmanService } from 'src/app/data/service/logman/logman.service';
import { SnackbarService } from 'src/app/data/service/snackbar/snackbar.service';
import { DeploymentService } from 'src/app/data/service/deployment.service';
import { ProcedureService } from 'src/app/data/service/procedure/procedure.service';
import {  data } from 'jquery';
import { KeycloakService } from 'keycloak-angular';
import { UserService } from '../user.service';
import { DossierService } from '../dossier/dossier.service';
import { PDFDocument, StandardFonts, rgb } from 'pdf-lib';
import fontkit from '@pdf-lib/fontkit';
import { BasepadService } from '../basepad/basepad.service';
import { FilemanService } from '../svc-fileman/fileman.service';
import { ReceivingComponent } from 'src/app/modules/dossier/pages/online-reception/pages/receiving/receiving.component';
import { MatDialog } from '@angular/material/dialog';
import { SelectBoxDialogComponent } from 'src/app/shared/components/dialogs/select-box-dialog/select-box-dialog.component';


declare var vnpt_plugin: any;
declare var PdfSigner: any;

export interface VNPTCAResponse {
  soChungThuc: any;
  idFile: any;
  status: number;
  message: string;
  data: {
    uuid: string;
    fileId: string,
    filename: string,
    size: number
  };
}

export interface VNPTSignComment {
  commentItalic: any;
  Description: any;
  lly: any;
  llx: any;
  urx: any;
  page: any;
  ury: any;
  commentFontSize: any;
  OnlyDescription: any;
  commentBold?: any;
  // SigColorRGB?:any
}

@Injectable({
  providedIn: 'root'
})
export class DigitalSignatureService {

  private static vnpt_plugin_key : string = null;
  private config = this.envService.getConfig();
  private selectedLang = localStorage.getItem('language') || 'vi';
  private responseData:VNPTCAResponse = {
    status:0,
    message:'',
    data:{
      fileId: '',
      filename: '',
      size: 0,
      uuid: '',
    },
    idFile: "",
    soChungThuc:''
  }
  documentId
  selectedDocument
  soChungThuc
  lastPage
  formId
  result = [];
  procedureForm= [];
  dossierFormFileData: any = [];
  dossierData
  userInfo;
  procedureId
  dossierId
  private filemanUrl = this.apiProviderService.getUrl('digo', 'fileman');
  private integrationConfigUrl = this.apiProviderService.getUrl('digo', 'adapter') + '/integrated-configuration';
  private filename: string;
  private fileId: string
  dossierDetail
  imgSignBase64;
  idFileNew;
  possition= true;
  sendEmailSigned = this.deploymentService.newConfigV2.sendEmailSigned;
  sendSmsSigned = this.deploymentService.newConfigV2.sendSmsSigned;

  allowChangeImageVNPTCA = this.deploymentService.newConfigV2.allowChangeImageVNPTCA;
  tagIdSignInfoVNPTCA = this.deploymentService.newConfigV2.tagIdSignInfoVNPTCA;
  enablePopupSelecteSignVNPTCA = this.deploymentService.newConfigV2.enablePopupSelecteSignVNPTCA;
  // @ViewChild(ReceivingComponent) receivingComponent!: ReceivingComponent;

  private receivingComponent!: ReceivingComponent;

  constructor(
    private keycloak: KeycloakService,
    private procedureService: ProcedureService,
    private envService: EnvService,
    private http: HttpClient,
    private apiProviderService: ApiProviderService,
    private configService: ConfigService,
    private snackbarService: SnackbarService,
    private logmanService: LogmanService,
    private userService: UserService,
    private dossierService: DossierService,
    private basepadService: BasepadService,
    private filemanService: FilemanService,
    private deploymentService: DeploymentService,
    private dialog: MatDialog,
  ) {
   // config = this.envservice.getConfig();
    this.getBasse64imgcks()
  }

  setComponentInstance(instance: ReceivingComponent) {
    this.receivingComponent = instance;
  }






  async signWithVNPTCA(fileId, filename, dossierId): Promise<VNPTCAResponse>{

    const _vnpt_plugin_key = await this.getVnptCaToken();

    if(this.allowChangeImageVNPTCA && this.enablePopupSelecteSignVNPTCA){
      const success = await this.openSignaturePopup();
      if (!success) {
        return;
      }
    }

    this.fileId = fileId;
    this.filename = filename;
    // check plugin
    const checkPluginResponse = await vnpt_plugin.checkPlugin().then(data => {return data}).catch(e => {return null});
    if(checkPluginResponse == null){
      const msgObj = {
        vi: 'VNPT-CA Plugin chưa được cài đặt hoặc chưa được bật!',
        en: 'VNPT-CA Plugin is not installed or not enable!'
      };
      this.snackbarService.openSnackBar(0, '', msgObj[this.selectedLang], 'error_notification', this.config.expiredTime);
      this.responseData.status = -1;
      this.responseData.message = msgObj[this.selectedLang];
      return this.responseData;
    }

    // Check license
    const checkLicenseResponse = await vnpt_plugin.setLicenseKey(_vnpt_plugin_key).then(data => {return JSON.parse(data)}).catch(e => {return null});
    if(checkLicenseResponse == null || checkLicenseResponse.code == -1){
      const msgObj = {
        vi: 'License không hợp lệ hoặc đã hết hạn!',
        en: 'License is invalid or has expired!'
      };
      this.snackbarService.openSnackBar(0, '', msgObj[this.selectedLang], 'error_notification', this.config.expiredTime);
      this.responseData.status = -1;
      this.responseData.message = msgObj[this.selectedLang];
      return this.responseData;
    }

    // Download file to open with plugin
    const fileResponse = await this.configService.downloadFile(fileId, dossierId).toPromise().catch(res => {return null});
    if(fileResponse == null){
      const msgObj = {
        vi: 'Tệp tin bị lỗi hoặc đã bị xóa. Vui lòng thử lại!',
        en: 'The file is corrupted or has been deleted. Please try again later'
      };
      this.snackbarService.openSnackBar(0, '', msgObj[this.selectedLang], 'error_notification', this.config.expiredTime);
      this.responseData.status = -1;
      this.responseData.message = msgObj[this.selectedLang];
      return this.responseData;
    }
    const dataType = fileResponse.type;
    const binaryData = [];
    binaryData.push(fileResponse);

    const reader = new FileReader();
    reader.readAsDataURL(new Blob(binaryData, { type: dataType }));
    return new Promise<VNPTCAResponse>((resolve, reject) => {
      reader.onloadend = async () => {
        let base64data = reader.result.toString();
        base64data = base64data.substring(base64data.indexOf(",")+1);
        var fileExtension = filename.split(".").pop();
        if (fileExtension !== "xml" && fileExtension !== "pdf" && fileExtension !== "docx"
          && fileExtension !== "xlsx" && fileExtension !== "pptx" && fileExtension !== "txt")  {
          // alert("Định dạng file chưa được hỗ trợ");
          const msgObj = {
            vi: 'Định dạng file chưa được hỗ trợ!',
            en: 'Unsupported file format!'
          };
          this.snackbarService.openSnackBar(0, '', msgObj[this.selectedLang], 'error_notification', this.config.expiredTime);
          this.responseData.status = -1;
          this.responseData.message = msgObj[this.selectedLang];
          resolve(this.responseData);
        }
        //			
        var sigOptions = null;
        if (fileExtension.toLowerCase() === "pdf")
        {
          sigOptions = new PdfSigner();
          sigOptions.AdvancedCustom = true;
          if(this.allowChangeImageVNPTCA){
            if(!this.enablePopupSelecteSignVNPTCA){
              await this.getBasse64imgcks();
            }
            sigOptions.ImageBase64 = this.imgSignBase64;
            sigOptions.SigVisible = true;
            sigOptions.SigSignerVisible = true;
            sigOptions.SigEmailVisible = true;
            sigOptions.SigPositionVisible = false;
            sigOptions.SigSigningTimeVisible = true;
            sigOptions.AdvancedCustom = true;
            // if(sha256=='1'){
            //     sigOptions.DigestAlgorithm = "SHA256";
            // }
            sigOptions.ValidationOption = false;
            sigOptions.SigTextSize = 9;
            sigOptions.SetImageBackground = false;
            sigOptions.PagesArray = [1];
            sigOptions.SigType = 2;
            sigOptions.overideSigFormat = true
          }
        }				
        resolve(await this.SignAdvanced(base64data, fileExtension, sigOptions));
      };
    });
    // reader.onloadend = async () => {
    //   let base64data = reader.result.toString();
    //   base64data = base64data.substring(base64data.indexOf(",")+1);
    //   console.log("base64data",base64data.substring(0,50)+"...");
    //   var fileExtension = filename.split(".").pop();
    //   if (fileExtension !== "xml" && fileExtension !== "pdf" && fileExtension !== "docx"
    //     && fileExtension !== "xlsx" && fileExtension !== "pptx" && fileExtension !== "txt")  {
    //     // alert("Định dạng file chưa được hỗ trợ");
    //     const msgObj = {
    //       vi: 'Định dạng file chưa được hỗ trợ!',
    //       en: 'Unsupported file format!'
    //     };
    //     this.snackbarService.openSnackBar(0, '', msgObj[this.selectedLang], 'error_notification', this.config.expiredTime);
    //     this.responseData.status = -1;
    //     this.responseData.message = msgObj[this.selectedLang];
    //     return this.responseData;
    //   }
    //   //			
    //   var sigOptions = null;
    //   if (fileExtension.toLowerCase() === "pdf")
    //   {
    //     sigOptions = new PdfSigner();
    //     sigOptions.AdvancedCustom = true;
    //   }				
    //   return await this.SignAdvanced(base64data, fileExtension, sigOptions);
    // };
  }

  async getBasse64imgcks() {
    let imgId;
    let myData = localStorage.getItem('UID');
    
    try {
        const userData = await this.userService.getUserSigns(myData).toPromise();
        imgId = userData[0].image.id;
        const imageData = await this.filemanService.getFile(imgId).toPromise();
        await this.fileToBase64(imageData).then(base64data => {
          this.imgSignBase64 = base64data.substring(base64data.indexOf(",") + 1);
          }).catch(error => {
            console.error('Error converting file to base64:', error);
        });
    } catch (error) {
        console.error('Error getting image:', error);
        // Xử lý lỗi nếu cần
    }
  }

  async fileToBase64(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => resolve(reader.result as string);
        reader.onerror = error => reject(error);
        reader.readAsDataURL(file);
    });
}


  async signWithVNPTCACheck(dossierId, file, procedureId, dossierDetai, type, dossierFormFile, document?: string, documentId?: string, possition?: boolean): Promise<VNPTCAResponse> {

    this.dossierService.getDossierDetail(dossierId).toPromise().then(
      data=>{
        this.dossierFormFileData = data.dossierFormFile
      }
    )

    await this.getBasse64imgcks()
    this.dossierId = dossierId;
    this.selectedDocument = document;
    this.documentId = documentId;
    this.dossierDetail = dossierDetai;
    this.procedureId = procedureId.id;
   // this.dossierFormFileData = dossierFormFile
    this.possition = possition;
    let procedureFormId = null;
    for (let i = 0; i < dossierDetai.length && !procedureFormId; i++) {
      let dossier = dossierDetai[i];
      
      
      for (let j = 0; j < dossier.file.length && !procedureFormId; j++) {
          let files = dossier.file[j];
          
        
          if (files.id === file.idFile) {
              procedureFormId = dossier.procedureFormId;
          }
      }
  }
  if (procedureFormId) {
    this.formId = procedureFormId;
  }
    //this.formId = dossierDetai[0].procedureForm.id
    const _vnpt_plugin_key = await this.getVnptCaToken();

    this.fileId = file.idFile;
    this.filename = file.name;
    let fileRow = file;
    if(type === 1){
      if(this.filename.includes("sign-ld")){
        this.snackbarService.openSnackBar(2, 'File đã được ký số chứng thực', "", 'warning_notification', this.config.expiredTime);
      }
    }if(type === 2){
      if(this.filename.includes("sign-vt")){
        this.snackbarService.openSnackBar(2, 'File đã được đóng dấu chứng thực', "", 'warning_notification', this.config.expiredTime);
      }
    }
    // check plugin
    const checkPluginResponse = await vnpt_plugin.checkPlugin().then(data => { return data }).catch(e => { return null });
    if (checkPluginResponse == null) {
      const msgObj = {
        vi: 'VNPT-CA Plugin chưa được cài đặt hoặc chưa được bật!',
        en: 'VNPT-CA Plugin is not installed or not enable!'
      };
      this.snackbarService.openSnackBar(0, '', msgObj[this.selectedLang], 'error_notification', this.config.expiredTime);
      this.responseData.status = -1;
      this.responseData.message = msgObj[this.selectedLang];
      return this.responseData;
    }

    // Check license
    const checkLicenseResponse = await vnpt_plugin.setLicenseKey(_vnpt_plugin_key).then(data => { return JSON.parse(data) }).catch(e => { return null });
    if (checkLicenseResponse == null || checkLicenseResponse.code == -1) {
      const msgObj = {
        vi: 'License không hợp lệ hoặc đã hết hạn!',
        en: 'License is invalid or has expired!'
      };
      this.snackbarService.openSnackBar(0, '', msgObj[this.selectedLang], 'error_notification', this.config.expiredTime);
      this.responseData.status = -1;
      this.responseData.message = msgObj[this.selectedLang];
      return this.responseData;
    }

    // Download file to open with plugin
    const fileResponse = await this.configService.downloadFile(this.fileId, this.dossierId).toPromise().catch(res => {return null});
    if (fileResponse == null) {
      const msgObj = {
        vi: 'Tệp tin bị lỗi hoặc đã bị xóa. Vui lòng thử lại!',
        en: 'The file is corrupted or has been deleted. Please try again later'
      };
      this.snackbarService.openSnackBar(0, '', msgObj[this.selectedLang], 'error_notification', this.config.expiredTime);
      this.responseData.status = -1;
      this.responseData.message = msgObj[this.selectedLang];
      return this.responseData;
    }
  //  const dataType = fileResponse.type;
    const binaryData = [];
    binaryData.push(fileResponse);

    const reader = new FileReader();
    reader.readAsDataURL(new Blob(binaryData, { type: "pdf" }));
    return new Promise<VNPTCAResponse>((resolve, reject) => {
      reader.onloadend = async () => {
        let base64data = reader.result.toString();
        base64data = base64data.substring(base64data.indexOf(",") + 1);
        let abc = base64data
        console.log(base64data)
        var fileExtension = this.filename.split(".").pop();
        if ( fileExtension !== "pdf" ) {
          // alert("Định dạng file chưa được hỗ trợ");
          const msgObj = {
            vi: 'Định dạng file chưa được hỗ trợ!',
            en: 'Unsupported file format!'
          };
          this.snackbarService.openSnackBar(0, '', msgObj[this.selectedLang], 'error_notification', this.config.expiredTime);
          this.responseData.status = -1;
          this.responseData.message = msgObj[this.selectedLang];
          resolve(this.responseData);
        }

        let addPageLast
        if (type === 1) {
          addPageLast = await this.addPageCertification(base64data)
        } if (type === 2) {
          let checkPage = await this.addPageCertificationMark(base64data)
          addPageLast = base64data
        }
        console.log(addPageLast)
        var sigOptions = null;
       

        if (fileExtension.toLowerCase() === "pdf") {
          sigOptions = new PdfSigner();
          sigOptions.llx = 395;
          sigOptions.lly = 740;
          sigOptions.urx = 549;
          sigOptions.ury = 820;
         

    //      sigOptions.page = 1;
          //sigOptions.SigType = 1;	
          sigOptions.ImageBase64 = this.imgSignBase64;
          sigOptions.SigVisible = true;
          sigOptions.SigSignerVisible = true;
          sigOptions.SigEmailVisible = true;
          sigOptions.SigPositionVisible = false;
          sigOptions.SigSigningTimeVisible = true;
          sigOptions.AdvancedCustom = false;
          sigOptions.SigType = 2;
          //sigOptions.textAlignBottom = true;
          //sigOptions.TsaUrl = "http://ca.gov.vn/tsa";
          //sigOptions.SigningTime = "08:35:38 19/03/2019";
          sigOptions.ValidationOption = false;
          sigOptions.SigTextSize = 10;
          //sigOptions.labelDescription = true;


          //sigOptions.Description = "Ký bởi NextG";						
          //sigOptions.OnlyDescription = true;			
          //sigOptions.SigColorRGB = "255,6,230";
          sigOptions.SetImageBackground = false;
          //sigOptions.IsEncyptFile = false;

          /*var sigOptions = new XmlSigner();
          sigOptions.TagSigning = 'CipherData';
          sigOptions.TagSaveResult = 'CipherData';
          sigOptions.SigningType = 'Enveloped';
          sigOptions.SigningTime = '14:45:00 26/03/2020';
          sigOptions.DigsetMethod = 'SHA1';
          sigOptions.SignatureMethod = 'RSAwithSHA1';*/
          sigOptions.PagesArray = [this.lastPage];
          sigOptions.AdvancedCustom = true;
          if (type === 2) {
            sigOptions.llx = 346;
            sigOptions.lly = 740;
            sigOptions.urx = 500;
            sigOptions.ury = 820;
            await this.updateExpectedAuthNumber();
            let date = new Date()
            var day = date.getDate();
            var month = date.getMonth() + 1;
            var year = date.getFullYear();
      
            var formattedDay: string | number = (day < 10) ? '0' + day : day;
            var formattedMonth: string | number = (month < 10) ? '0' + month : month;
            if (this.soChungThuc < 10) {
              this.soChungThuc = "0" + this.soChungThuc;
            }
          
            let soChungThuc: VNPTSignComment = {
              "commentItalic": false,
              "Description": this.soChungThuc,
              "llx": 129,
              "lly": 759,
              "urx": 171,
              "ury": 780,
              "page": this.lastPage,
              "commentFontSize": 10,
              "OnlyDescription": true,
            //  "commentBold" : true
            }
      
            let quyenSoChungThuc: VNPTSignComment = {
              "commentItalic": false,
              "Description": this.selectedDocument,
              "llx": 251,
              "lly": 759,
              "urx": 336,
              "ury": 780,
              "page": this.lastPage,
              "commentFontSize": 10,
              "OnlyDescription": true,
          //    "commentBold" : true,
            }
      
            let ngayChungThuc: VNPTSignComment = {
              "commentItalic": false,
              "Description": `${formattedDay}/${formattedMonth}/${year}`,
              "llx": 142,
              "lly": 738,
              "urx": 211,
              "ury": 759,
              "page": this.lastPage,
              "commentFontSize": 10,
              "OnlyDescription": true,
          //    "commentBold" : true
            }
            let dataComment : VNPTSignComment[] = []
            dataComment.push(soChungThuc)
            dataComment.push(quyenSoChungThuc)
            dataComment.push(ngayChungThuc)
            sigOptions.listComment = dataComment
          }
         
        }
       
        console.log(base64data);
        let a = await vnpt_plugin.SignPDFMultiplePages(addPageLast
          , null, sigOptions).then(async data => {
            var jsonObject = JSON.parse(data);
            console.log(data)
            if(jsonObject.code === 0 ){
              let file = this.base64ToFile(jsonObject.data, this.filename, "application/PDF")           
            await this.savePDFSign(file, type)
            let dataReturn = {
              soChungThuc : this.soChungThuc ? this.soChungThuc : "true",
              idFile : this.idFileNew
            }

             return dataReturn;
            }else{
              this.snackbarService.openSnackBar(0, "Ký số thất bại", '', 'error_notification', this.config.expiredTime);
              return false
            }
            
          })
        resolve(a);
      };
    });
  }



  async updateExpectedAuthNumber(): Promise<void> {
    await this.basepadService.getNumberAuth(this.documentId).toPromise().then(
      (res) => {
        console.log('Số chứng thực dự kiến nhận được từ API:', res);
        this.soChungThuc = res;
      },

      (error) => {
        console.error('Lỗi khi gọi API để lấy số chứng thực dự kiến:', error);
      }
    );
  }

  async addPageCertificationMark(base64data) {
    const dataUri = 'data:application/pdf;base64,' + base64data
    const pdfDoc = await PDFDocument.load(dataUri)
    this.lastPage = pdfDoc.getPageCount();

    const pdfBytes = await pdfDoc.save();
   // const blob = new Blob([pdfBytes], { type: 'application/pdf' });
    const base64Data = await this.uint8ArrayToBase64(pdfBytes);
    return base64Data;
  }

  async addPageCertification(base64data) {
    const url = '../assets/time-blob.ttf';
    const fontBytes = await fetch(url).then((res) => res.arrayBuffer());
    const dataUri = 'data:application/pdf;base64,' + base64data;
    const pdfDoc = await PDFDocument.load(dataUri);
    pdfDoc.registerFontkit(fontkit);
    const customFont = await pdfDoc.embedFont(fontBytes);

    const page = pdfDoc.addPage();
    let text = 'CHỨNG THỰC BẢN SAO ĐÚNG VỚI BẢN CHÍNH';
    text = text.toUpperCase();
    const textSize = 12;

    // Lấy trang cuối cùng
    const lastPage = pdfDoc.getPages()[pdfDoc.getPages().length - 1];
    const { width, height } = lastPage.getSize();
    this.lastPage = pdfDoc.getPageCount();

    // Tính toán vị trí x và y cho văn bản ở góc trên bên phải
    const textWidth = customFont.widthOfTextAtSize(text, textSize);
    const x = width - textWidth - 20; // 20 là khoảng cách từ mép phải của trang
    const y = height - 4 * textSize; // Vị trí y cho dòng đầu tiên

    const textSo = 'Số chứng thực:                /ĐT Quyển số:            '
    // Ngày chứng thực: ${formattedDay}/${formattedMonth}/${year}`
 //   const textSize = 15

  //  const time =`Ngày chứng thực: ${formattedDay}/${formattedMonth}/${year}`
    const time ='Ngày chứng thực:              '

    // Lấy trang cuối cùng
    // const lastPage = pdfDoc.getPages()[pdfDoc.getPages().length - 1];
    // const { width, height } = lastPage.getSize();

    // this.lastPage = pdfDoc.getPageCount()


    // Draw the string of text on the page
    lastPage.drawText(textSo, {
      x: 50,
      y: height - 4 * textSize - 25,
      size: textSize,
      font: customFont,
      color: rgb(0.01, 0.1, 0.9),
    })

    lastPage.drawText(time, {
      x: 50,
      y: height - 4 * textSize - 45,
      size: textSize,
      font: customFont,
      color: rgb(0.01, 0.1, 0.9),
    })

    page.drawText(text.toUpperCase(), {
      x: 50,
      y: y,
      size: textSize,
      font: customFont,
      color: rgb(0.9, 0.1, 0.21),
    });


    const pdfBytes = await pdfDoc.save();
    const blob = new Blob([pdfBytes], { type: 'application/pdf' });
    const base64Data = await this.uint8ArrayToBase64(pdfBytes);
    console.log(base64Data)
    return base64Data;
}



  async addPageCertification1(base64data) {

    const url = '../assets/times.ttf'
    const fontBytes = await fetch(url).then((res) => res.arrayBuffer())
    const dataUri = 'data:application/pdf;base64,' + base64data
    const pdfDoc = await PDFDocument.load(dataUri)
    pdfDoc.registerFontkit(fontkit)
    const customFont = await pdfDoc.embedFont(fontBytes)
    // const helveticaFont = await pdfDoc.embedFont(StandardFonts.Helvetica);

    const page = pdfDoc.addPage()
    // Create a string of text and measure its width and height in our custom font
    const text = 'Chứng thực bản sao đúng với bản chính'
    const textSize = 15

    // Lấy trang cuối cùng
    const lastPage = pdfDoc.getPages()[pdfDoc.getPages().length - 1];
    const { width, height } = lastPage.getSize();
    this.lastPage = pdfDoc.getPageCount()

    // Tính toán vị trí x và y cho văn bản ở góc trên bên phải
    const textWidth = customFont.widthOfTextAtSize(text, textSize);
    const x = width - textWidth - 20; // 20 là khoảng cách từ mép phải của trang
    const y = height - 450; // 450 là khoảng cách từ trên xuống

    page.drawText(text, {
      x: 50,
      y: height - 4 * textSize,
      size: textSize,
      font: customFont,
      color: rgb(0.9, 0.1, 0.21),
    })
    const pdfBytes = await pdfDoc.save();
    const blob = new Blob([pdfBytes], { type: 'application/pdf' });
    const base64Data = await this.uint8ArrayToBase64(pdfBytes);
    return base64Data;
  }

  async uint8ArrayToBase64(uint8Array: Uint8Array): Promise<string> {
    let binaryString = '';
    uint8Array.forEach(byte => {
      binaryString += String.fromCharCode(byte);
    });
    return btoa(binaryString);
  }

  getProcedureForm() {
    const searchString = '?page=0&size=50&spec=page&procedure-id=' + this.procedureId;
    return new Promise<void>((resolve, reject) => {
      this.procedureService.getProcedureForm(searchString).subscribe(async data => {
        for await (const element of data.content) {
          this.procedureForm.push(element);
          element.detail.forEach((item, index) => {
            item.file = [];
            if (index === 0) {
              item.status = 1;
            } else {
              item.status = 0;
            }
          });

          if (!!element.procedureProcessDefinition && !!element.procedureProcessDefinition.id) {
            const indexOfResult = this.result.findIndex(res => res.case === element.procedureProcessDefinition.id);
            if (indexOfResult > -1) {
              this.result[indexOfResult].form.push({
                id: element.form.id,
                name: element.form.name,
                detail: element.detail,
                typeId: element.detail[0].type.id,
                typeName: element.detail[0].type.name,
                quantity: element.detail[0].quantity,
                status: 1
              });
            }
          }
        }
        resolve();
      }, err => {
        reject(err);
      });
    });
  }

  async updateFormFile(fileId, filename, size, strategy?: string) {
    await this.getProcedureForm()
    await this.getListProcedureProcess();
    this.procedureForm

    const newFile = {
      id: fileId,
      filename: filename,
      size: size,
    };

    await this.replaceFileInfo(this.dossierFormFileData, this.fileId, fileId, filename, size)

    await this.procedureForm.forEach(prcF => {
      prcF.file=this.dossierFormFileData[0].file
    });
    await this.updateDossierComposition()
  }

  async replaceFileInfo(dossierFormFile, oldFileId, fileId, filename, size) {
    const updatedDossierFormFile = dossierFormFile.map(formFile => {
        if (formFile.procedureFormId === this.formId) {
      
            return {
                ...formFile,
                file: formFile.file.map(file => {
                    if (file.id === oldFileId) {
                        return {
                            ...file,
                            filename: filename,
                            size: size,
                            id: fileId
                        };
                    }
                    return file;
                })
            };
        }
        return formFile;
    });
    this.dossierFormFileData = updatedDossierFormFile;
  }

  getListProcedureProcess() {
    const searchString = '?keyword=&spec=page&page=0&size=50&procedure-id=' + this.procedureId;
    return new Promise<any>((resolve, reject) => {
      this.procedureService.getListProcedureProcess(searchString).subscribe(async data => {
        const listProcedureProcess = [];
        for await (const element of data.content) {
          // if (element.status !== 0) {
          listProcedureProcess.push(element);
          this.result.push({
            case: element.id,
            caseName: element.processDefinition.name,
            form: []
          });
          // }
        }
        resolve(listProcedureProcess);
      }, err => {
        reject(err);
      });
    });
  }

  async updateDoc2PdfFormFile(formId, detailId, fileDocId, filePdfId, filename, size, strategy?: string) {
    const newFile = {
      id: fileDocId,
      idNew: filePdfId,
      filename: filename,
      size: size,
    };
    this.procedureForm.filter((prcF) => prcF.form.id === formId)[0].detail.forEach((detailProcedureForm) => {
      if (detailProcedureForm.type.id === detailId || (strategy == "updateMany" && detailProcedureForm.status == 1)) {
        for (const file of detailProcedureForm.file) {
          if (file.id == newFile.id) {
            file.id = newFile.idNew;
            file.filename = newFile.filename;
            file.size = newFile.size;
          }
        }
      }
    });
  }

  updateDossierComposition() {
    let dossierFormFileData = [];
    this.dossierFormFileData.forEach((item, index) => {
        // dossierFormFileData = [];
        dossierFormFileData.push(
          {
            procedureForm: {
              id: item.procedureFormId,
              name: item.formName
            },
            order: index + 1,
            case: {
              id: item.case
            },
            detail: {
              type: {
                id: item.requirement.typeId,
                name: item.requirement.typeName
              },
              quantity: item.requirement.quantity
            },
            file: item.file
          }
        );
      
    });
    this.dossierFormFileData = dossierFormFileData;
    const requestBody = {
      dossierFormFile: this.dossierFormFileData
    };
    this.dossierService.putDossierOnline(this.dossierId, JSON.stringify(requestBody, null, 2)).subscribe(data => {
      this.snackbarService.openSnackBar(1, "Lưu file ký số thành công", "",'success_notification', 2000)
      if((this.sendEmailSigned || this.sendSmsSigned) && !!this.receivingComponent){
        this.receivingComponent.setSMSSignFile();
      }
      this.dossierFormFileData = []
    }, err => {
    });
  }

  async updateDossierAttachment(newFile, dossierId) {
    let data = await this.dossierService.getDossierDetail(dossierId).toPromise();
    let dossierAttachment = [];
    if (data.attachment) {
      dossierAttachment = data.attachment;
    }
    if (newFile.idold)
      dossierAttachment = dossierAttachment.filter(e => e.id !== newFile.idold);
    else
      dossierAttachment = dossierAttachment.filter(e => e.id !== newFile.id);
    dossierAttachment.push(newFile);
    const putBody = {
      attachment: dossierAttachment
    };
    const requestBody = JSON.stringify(putBody, null, 2);
    this.dossierService.putDossierOnline(dossierId, requestBody).subscribe(data => {
      // if (data.affectedRows === 1) {
      //   this.getDossierAttachment(true);
      // } else {
      //   this.getDossierAttachment(false);
      // }
    }, err => {
      const msgObj = {
        vi: 'Cập nhật thất bại!',
        en: 'Update failed!'
      };
      // this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
    });
  }

  base64ToFile(base64Data, fileName, mimeType) {
    var byteCharacters = atob(base64Data);
    var byteNumbers = new Array(byteCharacters.length);
    for (var i = 0; i < byteCharacters.length; i++) {
      byteNumbers[i] = byteCharacters.charCodeAt(i);
    }
    var byteArray = new Uint8Array(byteNumbers);
    var file = new File([byteArray], fileName, { type: mimeType });
    return file;
  }


  async savePDFSign(file, type) {
    let idFileSign;
    let fileNameNew
    if (type === 1) {
      fileNameNew = "sign-ld-" + this.filename
    }
    if (type === 2) {
      fileNameNew = "sign-vt-" + this.filename
    }
    await this.procedureService.uploadFiles(file).toPromise().then(async data => {
      idFileSign = data.id;
      this.idFileNew = data.id;
      await this.updateFormFile(idFileSign, fileNameNew, file.size);
    });
  }



  downloadPDF(base64String, fileName) {
    // Chuyển đổi chuỗi Base64 thành dữ liệu binh thường
    var binaryData = atob(base64String);
    var len = binaryData.length;
    var buffer = new ArrayBuffer(len);
    var view = new Uint8Array(buffer);
    for (var i = 0; i < len; i++) {
      view[i] = binaryData.charCodeAt(i);
    }

    // Tạo đường dẫn URL cho tệp PDF
    var blob = new Blob([view], { type: "application/pdf" });
    var url = window.URL.createObjectURL(blob);

    // Tạo một liên kết và gắn đường dẫn URL đã tạo vào thuộc tính href của liên kết
    var link = document.createElement("a");
    link.href = url;
    link.download = fileName;

    // Tạo một sự kiện nhấp chuột giả để kích hoạt tải xuống tự động khi người dùng nhấp vào liên kết
    var clickEvent = new MouseEvent("click", {
      bubbles: true,
      cancelable: true,
      view: window
    });
    link.dispatchEvent(clickEvent);
  }

  private async SignAdvanced(data, type, option){
    var dataJS : any = {};
      
    var arrData = [];		
    dataJS.data = data;
    dataJS.type = type;
    dataJS.sigOptions = JSON.stringify(option);

    var jsData = "";
    jsData += JSON.stringify(dataJS);

    arrData.push(jsData);
    var serial = "";

    const result = await vnpt_plugin.signArrDataAdvanced(arrData, serial, true).then(data => {return data}).catch((e) => {
      // alert(e);
      this.responseData.status = 0;
      this.responseData.message = e.error;
      return null;
    });
    if(result == null){
      return this.responseData;
    }
    return await this.showMessage(result);
	}

  private async showMessage(data){
    var jsOb = JSON.parse(JSON.parse(data)[0]);
    let err = "";		
    let msgObj = {};
    switch (jsOb.code)
    {
      case 0:
        err = "Ký thành công";	
        // alert(err);
        const formData: FormData = new FormData();
        formData.append("filename",this.filename);
        // formData.append("file",null);
        formData.append("base64File",jsOb.data)
        const signedFileResponse = await this.updateSignedFile(this.fileId,formData).toPromise().catch(err => {
          console.log(err.error);
          return null;
        });
        if(signedFileResponse == null){
          const msgObj = {
            vi: 'Không thể tải lên tệp tin ký số. Vui lòng thử lại sau!',
            en: 'Failed to upload signed file. Please try again later!'
          };
          this.snackbarService.openSnackBar(0, '', msgObj[this.selectedLang], 'error_notification', this.config.expiredTime);
          this.responseData.status = -1;
          this.responseData.message = msgObj[this.selectedLang];
          return this.responseData; 
        }
        // write logs
        this.logmanService.postSignHistory(this.fileId,signedFileResponse.filename).subscribe();
        
        msgObj = {
          vi: 'Ký thành công',
          en: 'Sign succesully'
        };
        this.snackbarService.openSnackBar(1, '', msgObj[this.selectedLang], 'success_notification', this.config.expiredTime);
        if((this.sendEmailSigned || this.sendSmsSigned) && !!this.receivingComponent){
          this.receivingComponent.setSMSSignFile();
        }
        this.responseData.status = 1;
        this.responseData.message = msgObj[this.selectedLang];
        this.responseData.data.fileId = signedFileResponse.id;
        this.responseData.data.filename = signedFileResponse.filename;
        this.responseData.data.size = signedFileResponse.size;
        return this.responseData;
      case 1:
        err = "Dữ liệu đầu vào không đúng định dạng";						
        break;
      case 2:
        err = "Không lấy được thông tin chứng thư số";						
        break;
      case 3:
        err = "Có lỗi trong quá trình ký số";
        break;
      case 4:
        err = ("Chứng thư số không có khóa bí mật");						
        break;
      case 5:
        err = ("Lỗi không xác định");
        break;
      case 6:
        err = ("Ký pdf: không tìm thấy tham số số trang cần ký");
        break;
      case 7:
        err = ("Ký pdf: trang đặt chữ ký không tồn tại");
        break;
      case 8:
        err = ("Ký xml: không tìm thấy thẻ ký số");
        break;				
      case 9:
        err = ("Ký pdf: không tìm thấy id của thẻ ký số");
        break;
      case 10:
        err = ("Dữ liệu ký đã chứa một hoặc nhiều chữ ký không hợp lệ");
        break;
      case 11:
        err = ("Người dùng hủy bỏ");
        break;
      case 13:
        err = "Dữ liệu ký rỗng";
        break;
      default:
        err = ("Lỗi không xác định");
        break;					
    }
    console.log("VNPT-CA > result:",err);		
    this.responseData.status = 0;
    this.responseData.message = err;
    this.snackbarService.openSnackBar(0, '', err, 'error_notification', this.config.expiredTime);
    return this.responseData;
	}

  private updateSignedFile(id: string, formData: FormData): Observable<any> {
    let headers = new HttpHeaders();
    // headers = headers.set('Accept-Language', localStorage.getItem('language'));
    // headers = headers.set('Content-Type', 'multipart/form-data');
    headers.append('Access-Control-Allow-Credentials', 'true');
    // return this.http.put("http://localhost:8080" + `/file/6215875ce411b74273ba1d8b/--update-signed-file`, formData,{ headers });
    return this.http.put(this.filemanUrl + `/file/${id}/--update-signed-file`, formData,{ headers });
  }

  private async getVnptCaToken(): Promise<string>{
    if(!DigitalSignatureService.vnpt_plugin_key){
      let headers = new HttpHeaders();
      const response = await this.http.get(this.integrationConfigUrl + `/61a04ea9719131c01b89b036`, {headers}).toPromise().catch(err => {return null;});
      if(response != null){
        // Get token
        DigitalSignatureService.vnpt_plugin_key = response.parameters.find(x => x.key == 'vnpt-ca-license').value;
      }else{
        DigitalSignatureService.vnpt_plugin_key = null;
      }
    }
    return DigitalSignatureService.vnpt_plugin_key;
  }

  async openSignaturePopup(): Promise<boolean> {
    return new Promise<boolean>((resolve, reject) => {
      const myData = localStorage.getItem('UID');

      this.userService.getUserSigns(myData).subscribe({
        next: async (signsData: any[]) => {
          const filtered = signsData.filter(sign =>
            sign.signInfo?.some(info => info.serviceId === this.tagIdSignInfoVNPTCA)
          );

          if (filtered.length === 0) {
            this.snackbarService.openSnackBar(0, '', 'Chưa cấu hình loại chữ ký VNPT-CA', 'error_notification', this.config.expiredTime);
            return resolve(false);
          }

          if (filtered.length === 1) {
            const imgId = filtered[0]?.image?.id;
            if (!imgId) {
              this.snackbarService.openSnackBar(0, '', 'Chưa cấu hình ảnh chữ ký VNPT-CA', 'error_notification', this.config.expiredTime);
              return resolve(false);
            }
            try {
              const imageData = await this.filemanService.getFile(imgId).toPromise();
              const base64data = await this.fileToBase64(imageData);
              this.imgSignBase64 = base64data.substring(base64data.indexOf(",") + 1);
              resolve(true);
            } catch (error) {
              console.error('Lỗi khi xử lý file ảnh:', error);
              resolve(false);
            }
          } else {
            // Nhiều hơn 1 thì mở dialog chọn
            const dialogRef = this.dialog.open(SelectBoxDialogComponent, {
              width: '500px',
              data: { listTag: filtered, title: 'Chọn mẫu chữ ký' },
              autoFocus: false,
              restoreFocus: false
            });

            dialogRef.afterClosed().subscribe(async (selected) => {
              if (!!selected) {
                try {
                  if(!selected.image?.id){
                    this.snackbarService.openSnackBar(0, '', 'Chưa cấu hình ảnh chữ ký VNPT-CA', 'error_notification', this.config.expiredTime);
                    return resolve(false);
                  }
                  const imageData = await this.filemanService.getFile(selected.image.id).toPromise();
                  const base64data = await this.fileToBase64(imageData);
                  this.imgSignBase64 = base64data.substring(base64data.indexOf(",") + 1);
                  resolve(true);
                } catch (error) {
                  console.error('Lỗi khi xử lý file ảnh:', error);
                  resolve(false);
                }
              }else{
                resolve(false);
              }
            });
          }
        },
        error: (err) => {
          console.error('Lỗi khi lấy danh sách chữ ký:', err);
          resolve(false);
        }
      });
    });
  }

}


