import { DatePipe } from '@angular/common';
import { AfterViewInit, Component, OnDestroy, OnInit } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { DeploymentService } from 'data/service/deployment.service';
import { Workbook } from 'exceljs';
import * as fs from 'file-saver';
import { EnvService } from 'src/app/core/service/env.service';
import { DLKStatisticsService } from 'src/app/data/service/dlk-statistics/dlk-statistics.service';
import { MainService } from 'src/app/data/service/main/main.service';
import { ProcedureService } from 'src/app/data/service/procedure/procedure.service';
import { SnackbarService } from 'src/app/data/service/snackbar/snackbar.service';
import * as tUtils from 'src/app/data/service/thoai.service';
import { BaocaoChithi08DialogComponent } from '../dialogs/baocao-chithi08-dialog/baocao-chithi08-dialog.component';

export interface Agency {
  id: string;
  name: string;
}

@Component({
  selector: 'app-dlk-chithi08-ttcapxa',
  templateUrl: './dlk-chithi08-ttcapxa.component.html',
  styleUrls: [
    './dlk-chithi08-ttcapxa.component.scss',
    '/src/app/app.component.scss',
    '/src/app/shared/scss/form-field-outline.scss'
  ]
})
export class DlkChithi08TtcapxaComponent implements OnInit, AfterViewInit, OnDestroy {
  nowDate = tUtils.newDate();
  tuNgay = new Date(this.nowDate.getFullYear(), this.nowDate.getMonth(), 1);
  denNgay = this.nowDate;
  tuNgayLuyke = new Date(this.nowDate.getFullYear(), 0, 1);
  denNgayLuyke = this.nowDate;
  env: any = this.deploymentService.getAppDeployment()?.env;

  pageTitle = {
    vi: 'Báo cáo chỉ thị 08 toàn tỉnh cấp xã',
    en: 'Bao cao chi thi 08 toan tinh cap xa'
  };

  userAgency?: any = JSON.parse(localStorage.getItem('userAgency'));

  Agency = this.deploymentService.env.OS_DLK; //remove

  config = this.envService.getConfig();
  rootAgencyId = this.deploymentService.getConfig("env")?.OS_DLK?.rootAgencyId ?? "60b87fb59adb921904a0213e";

  listHoSoCapTinh = [];
  listHoSoCapHuyen = [];
  listAgency = [];
  listXaPhuongAgency = [];
  listPhongBanAgency = [];

  listHoSo = [];
  tblData: any[] = [];
  soData: any[] = [];
  soDataSum: any = {};
  dataSum: any = {};
  EXCEL_EXTENSION = '.xlsx';
  EXCEL_TYPE = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8';

  listProp: any[] = [
    { name: 'p02_tiepnhan_tong', type: 'hs' },
    { name: 'p03_tiepnhan_tructiep', type: 'hs' },
    { name: 'p04_tiepnhan_bcci', type: 'hs' },
    { name: 'p05_tiepnhan_tructuyen_motphan', type: 'hs' },
    { name: 'p06_tiepnhan_tructuyen_motphan_tyle', type: 'tl' },
    { name: 'p07_tiepnhan_tructuyen_toantrinh', type: 'hs' },
    { name: 'p08_tiepnhan_tructuyen_toantrinh_tyle', type: 'tl' },
    { name: 'p09_tiepnhan_tructuyen_motphan_toantrinh', type: 'hs' }, // site v1 ko cho phep hien thi ds ~ type: tl
    { name: 'p10_tiepnhan_tructuyen_motphan_toantrinh_tyle', type: 'tl' },
    { name: 'p11_tiepnhan_tong_igate', type: 'hs' },
    { name: 'p12_tiepnhan_tong_igate_tyle', type: '', fixValue: '100' },
    { name: 'p13_traketqua_tong', type: 'hs' },
    { name: 'p14_traketqua_tructiep', type: 'hs' },
    { name: 'p15_traketqua_tructiep_tyle', type: 'tl' },
    { name: 'p16_traketqua_bcci', type: 'hs' },
    { name: 'p17_traketqua_bcci_tyle', type: 'tl' },
    { name: 'p18_traketqua_tructuyen', type: 'hs' },
    { name: 'p19_traketqua_tructuyen_tyle', type: 'tl' },
    { name: 'p20_traketqua_tong_igate', type: 'hs' },
    { name: 'p21_traketqua_sudung_kyso', type: '', fixValue: '-' }, // -: khong phat sinh
    { name: 'p22_dvctt_tong', type: 'tt' }, // tt: thu tuc
    { name: 'p23_dvctt_phatsinh_hoso', type: 'tt' },
    { name: 'p24_dvctt_phatsinh_hoso_tyle', type: 'tl' },
    { name: 'p25_dvctt_motphan', type: 'tt' },
    { name: 'p26_dvctt_motphan_phatsinh_hoso', type: 'tt' },
    { name: 'p27_dvctt_motphan_phatsinh_hoso_tyle', type: 'tl' },
    { name: 'p28_dvctt_toantrinh', type: 'tt' },
    { name: 'p29_dvctt_toantrinh_phatsinh_hoso', type: 'tt' },
    { name: 'p30_dvctt_toantrinh_phatsinh_hoso_tyle', type: 'tl' },
    { name: 'p31_tthc_bcci_tong', type: 'tt' }, // thu tuc - site v1 dang csrf token invaid
    { name: 'p32_tthc_bcci_phatsinh_hoso', type: 'tt' }, // thu tuc - site v1 dang csrf token invaid
    { name: 'p33_tthc_bcci_phatsinh_hoso_tyle', type: 'tl' }, // ty le
    { name: 'p34_tthc_tttt_tong', type: 'tt' }, // thu tuc - site v1 dang csrf token invaid
    { name: 'p35_tthc_tttt_hoso_phatsinh', type: 'hs' }, // ho so - site v1 dang csrf token invaid
    { name: 'p36_tthc_tttt_phatsinh_hoso', type: 'tt' }, // thu tuc - site v1 dang csrf token invaid
    { name: 'p37_tthc_tttt_phatsinh_hoso_tyle', type: 'tl' },
    { name: 'p38_tthc_tttt_phatsinh_hoso_dagiaiquyet', type: 'hs' }, // ho so - site v1 dang csrf token invaid
    { name: 'p39_tthc_tttt_phatsinh_hoso_dagiaiquyet_tyle', type: 'tl' },
  ];

  constructor(
    private envService: EnvService,
    private datePipe: DatePipe,
    private deploymentService: DeploymentService,
    private dlkStatisticService: DLKStatisticsService,
    private dialog: MatDialog,
    private procedureService: ProcedureService,
    private mainService: MainService,
    private snackbarService: SnackbarService
  ) { }

  ngOnInit(): void {
    this.mainService.setPageTitle(this.pageTitle[localStorage.getItem('language')]);
  }

  getDataBaocao() {
    if (this.validateForm()) {
      let tuNgay = this.datePipe.transform(this.tuNgay, 'yyyy-MM-dd') + 'T00:00:00.000Z';
      let denNgay = this.datePipe.transform(this.denNgay, 'yyyy-MM-dd') + 'T23:59:59.999Z';

      this.dlkStatisticService.getDataDLKCT08TTCapXa(this.config.rootAgency.id, tuNgay, denNgay).subscribe(
        (data: any[]) => {
          this.listHoSo = this.processData(data);
          this.soData = data.filter(el => el.level == 1);
          this.soDataSum = this.tinhTong(this.soData);
        }
      );
    }
  }

  getAgencyParentId = (id, list) => list
    .find(obj => obj.id == id).parent?.id

  processData(data) {
    let listDVCap3 = [];
    listDVCap3.push(...this.listXaPhuongAgency, ...this.listPhongBanAgency)
    let listIdDVCap3 = listDVCap3.map(item => item.id)
    for (let ix = 0; ix < data.length; ix++) {
      if (listIdDVCap3.includes(data[ix].agencyParentId)) {
        if (this.getAgencyParentId(data[ix].agencyParentId, listDVCap3)) {
          data[ix].agencyParentId.agencyParentId = this.getAgencyParentId(data[ix].agencyParentId, listDVCap3)
        }
      }
    }

    return data;
  }

  tinhTong(data: any[]): any {
    let retData = {};
    //lặp theo listProp để tính sum
    this.listProp.forEach(p => {
      //apply cho hồ sơ hoặc tỷ lệ
      if (p.type == 'hs' || p.type == 'tt' || p.type == 'tl') {
        //init prop
        if (!retData.hasOwnProperty(p.name + '_tky')) {
          retData[p.name + '_tky'] = 0;
        }

        let dsTyLe_tky = [];
        //lặp theo data
        data.forEach(dt => {
          //hồ sơ thì tính tổng
          if (p.type == 'hs' || p.type == 'tt') {
            retData[p.name + '_tky'] += parseInt(dt[p.name + '_tky']);
          }
          //tỷ lệ thì tính trung bình (avg)
          if (p.type == 'tl') {
            if (dt[p.name + '_tky'] != '-') {
              dsTyLe_tky.push(parseFloat(dt[p.name + '_tky']));
            } else {
              dsTyLe_tky.push(0);
            }
          }
        })

        // tính tỷ lệ trong kỳ

        if (p.type == 'tl') {
          if (dsTyLe_tky.length > 0) {
            let sum = dsTyLe_tky.reduce((acc, num) => acc + num, 0);
            retData[p.name + '_tky'] = (sum / dsTyLe_tky.length).toFixed(2);
          }
        }
      }
    })

    // Tính tỷ lệ trong kỳ
    retData["p06_tiepnhan_tructuyen_motphan_tyle_tky"] = this.tinhTyLe(retData["p05_tiepnhan_tructuyen_motphan_tky"], retData["p02_tiepnhan_tong_tky"]);
    retData["p08_tiepnhan_tructuyen_toantrinh_tyle_tky"] = this.tinhTyLe(retData["p07_tiepnhan_tructuyen_toantrinh_tky"], retData["p02_tiepnhan_tong_tky"]);
    retData["p10_tiepnhan_tructuyen_motphan_toantrinh_tyle_tky"] = this.tinhTyLe(retData["p09_tiepnhan_tructuyen_motphan_toantrinh_tky"], retData["p02_tiepnhan_tong_tky"]);
    retData["p12_tiepnhan_tong_igate_tyle_tky"] = this.tinhTyLe(1, 1);
    retData["p15_traketqua_tructiep_tyle_tky"] = this.tinhTyLe(retData["p14_traketqua_tructiep_tky"], retData["p13_traketqua_tong_tky"]);
    retData["p17_traketqua_bcci_tyle_tky"] = this.tinhTyLe(retData["p16_traketqua_bcci_tky"], retData["p13_traketqua_tong_tky"]);
    retData["p19_traketqua_tructuyen_tyle_tky"] = this.tinhTyLe(retData["p18_traketqua_tructuyen_tky"], retData["p13_traketqua_tong_tky"]);
    retData["p24_dvctt_phatsinh_hoso_tyle_tky"] = this.tinhTyLe(retData["p23_dvctt_phatsinh_hoso_tky"], retData["p22_dvctt_tong_tky"]);
    retData["p27_dvctt_motphan_phatsinh_hoso_tyle_tky"] = this.tinhTyLe(retData["p26_dvctt_motphan_phatsinh_hoso_tky"], retData["p25_dvctt_motphan_tky"]);
    retData["p30_dvctt_toantrinh_phatsinh_hoso_tyle_tky"] = this.tinhTyLe(retData["p29_dvctt_toantrinh_phatsinh_hoso_tky"], retData["p28_dvctt_toantrinh_tky"]);
    retData["p33_tthc_bcci_phatsinh_hoso_tyle_tky"] = this.tinhTyLe(retData["p32_tthc_bcci_phatsinh_hoso_tky"], retData["p31_tthc_bcci_tong_tky"]);
    retData["p37_tthc_tttt_phatsinh_hoso_tyle_tky"] = this.tinhTyLe(retData["p36_tthc_tttt_phatsinh_hoso_tky"], retData["p34_tthc_tttt_tong_tky"]);
    retData["p39_tthc_tttt_phatsinh_hoso_dagiaiquyet_tyle_tky"] = this.tinhTyLe(retData["p38_tthc_tttt_phatsinh_hoso_dagiaiquyet_tky"], retData["p35_tthc_tttt_hoso_phatsinh_tky"]);

    return retData;
  }

  tinhTyLe(a, b) {
    if (b != 0) {
      let ratio = (a / b) * 100;
      if (ratio == 0) {
        return "0";
      } else if (ratio == 100) {
        return "100";
      }
      return ratio.toFixed(2).toString();
    }
    return "-";
  }

  ngAfterViewInit() {
    // this.getListAgencyAccept(this.Agency.rootAgencyId, '', 0, 10000);
    this.listHoSoCapTinh = [];
    this.listHoSoCapHuyen = [];
    this.getListAgencyAccept(this.Agency.rootAgencyId, '', 0, 2000, []);
    this.getListChildAgencyAccept();
  }

  ngOnDestroy() {
  }

  // getListAgencyAccept(prid, keyword, page, size) {
  //   const searchString = '?parent-id=' + prid + '&keyword=' + keyword + '&page=' + page + '&size=' + size + '&sort=name.name,asc&status=1';
  //   this.listHoSoCapTinh = [];
  //   this.listHoSoCapHuyen = [];
  //   this.procedureService.getListAgencyWithParent(searchString).subscribe(
  //     (res) => {
  //       this.listAgency = res.content;
  //     },
  //     (err) => {
  //       console.log(err);
  //     }
  //   );
  // }

  getListAgencyAccept(prid, keyword, page, size, accumulatedData) {
    const searchString = `?parent-id=${prid}&keyword=${keyword}&page=${page}&size=${size}&sort=name.name,asc&status=1`;

    this.procedureService.getListAgencyWithParent(searchString).subscribe(
      (res: any) => {
        const merged = [...accumulatedData, ...res.content];

        if (res.last) {
          this.listAgency = merged;
        } else {
          this.getListAgencyAccept(prid, keyword, page + 1, size, merged);
        }
      },
      (err) => {
        console.error('Lỗi khi gọi API lấy danh sách agency:', err);
      }
    );
  }

  getListChildAgencyAccept() {
    this.listHoSoCapTinh = [];
    this.listHoSoCapHuyen = [];
    this.dlkStatisticService.getListAgencyWithCT08(this.rootAgencyId).subscribe(
      (res) => {
        this.listXaPhuongAgency = res.content;
      },
      (err) => {
        console.log(err);
      }
    );
  }

  onClickTd(item, prop: string, type: number, level: string) {
    //data dialog
    let dialogData: any = {
      prid: this.config.rootAgency.id,
      id: item.id,
      agencyName: item.name,
      prop: prop.slice(0, -4),
      type: type,
      level
    };
    if (prop.endsWith('_tky')) {
      dialogData.tuNgay = this.tuNgay.toISOString();
      dialogData.denNgay = this.denNgay.toISOString();
    } else {
      dialogData.tuNgay = this.tuNgayLuyke.toISOString();
      dialogData.denNgay = this.denNgayLuyke.toISOString();
    }

    //mở dialog
    const dialogRef = this.dialog.open(BaocaoChithi08DialogComponent, {
      width: '95%',
      height: '90%',
      data: dialogData,
      disableClose: true,
      autoFocus: false
    });

    dialogRef.afterClosed().subscribe(() => {
    });
  }

  // Cập nhật validate date trong form tìm kiếm
  validateForm() {
    let data = {
      errMessage: "",
      status: false
    }

    // Chuyển đổi lại thời gian startDate / endDate
    // tuNgay denNgay tuNgayLuyke denNgayLuyke
    let tuNgayISOString = this.tuNgay?.setHours(0, 0, 0, 0) ?? 0;
    let denNgayISOString = this.denNgay?.setHours(23, 59, 59, 999) ?? 0;
    let tuNgayLkeISOString = this.tuNgayLuyke?.setHours(0, 0, 0, 0) ?? 0;
    let denNgayLkeISOString = this.denNgayLuyke?.setHours(23, 59, 59, 999) ?? 0;
    let language = localStorage.getItem('language');

    if (this.tuNgay == null || this.denNgay == null || this.tuNgayLuyke == null || this.denNgayLuyke == null
      || tuNgayISOString === 0 || denNgayISOString === 0 || tuNgayLkeISOString === 0 || denNgayLkeISOString === 0) {
      data.errMessage = language == 'vi' ? 'Vui lòng nhập đầy đủ thông tin ngày tra cứu' : 'Please enter complete information for the lookup';
    } else if (tuNgayISOString > denNgayISOString || tuNgayLkeISOString > denNgayLkeISOString) {
      data.errMessage = language == 'vi' ? 'Vui lòng nhập thông tin từ ngày nhỏ hơn đến ngày' : 'Please enter information from date less than to date';
    } else {
      data.status = true;
    }

    if (data == null || !data?.status) {
      this.snackbarService.openSnackBar(0, "Không hợp lệ", data.errMessage, 'error_notification', this.config.expiredTime);
      this.resetVariable();
      return false;
    }

    return true;
  }

  resetVariable() {
    this.soData = [];
    this.soDataSum = {};
    this.dataSum = {};
  }

  waitingDownloadExcel: boolean = false;

  async exportToExcel() {
    if ((this.validateForm)) {
      this.getDataBaocao();
      const from = this.datePipe.transform(this.tuNgay, 'dd/MM/yyyy');
      const to = this.datePipe.transform(this.denNgay, 'dd/MM/yyyy');

      const newDateshort = this.datePipe.transform(new Date(), "dd/MM/yyyy")
      const newDate = this.datePipe.transform(new Date(), "dd/MM/yyyy HH:mm:ss")
      const excelFileName = `Bao_cao_chi_thi_08__tt_capxa_${newDate}`;

      let headerXLS = {
        row1: "CỘNG HÒA XÃ HỘI CHỦ NGHĨA VIỆT NAM",
        row2: "Độc lập - Tự do - Hạnh phúc",
        row3: `BÁO CÁO CHỈ THỊ 08 TOÀN TỈNH CẤP XÃ`,
        row4: `(Từ ngày ${from} đến ngày ${to})`,
        row5: `Tỉnh Đắk Lắk, thống kê vào lúc ` + newDate,
      }

      const workbook = new Workbook();
      workbook.creator = 'Snippet Coder';
      workbook.lastModifiedBy = 'SnippetCoder';
      workbook.created = new Date();
      workbook.modified = new Date();
      const worksheet = workbook.addWorksheet("sheet1");

      // Add header row
      worksheet.addRow([]);
      worksheet.mergeCells('A1:V1');
      worksheet.getCell('A1').value = headerXLS.row1;
      worksheet.getCell('A1').alignment = { horizontal: 'center', vertical: 'middle' };
      worksheet.getCell('A1').font = { size: 13, name: 'Times New Roman' };

      worksheet.mergeCells('A2:V2');
      worksheet.getCell('A2').value = headerXLS.row2;
      worksheet.getCell('A2').alignment = { horizontal: 'center', vertical: 'middle' };
      worksheet.getCell('A2').font = { size: 13, underline: true, name: 'Times New Roman' };
      
      worksheet.mergeCells('A3:V3');
      worksheet.getCell('A3').value = "";
      worksheet.getCell('A3').alignment = { horizontal: 'center', vertical: 'middle' };
      worksheet.getCell('A3').font = { size: 13, bold: true, name: 'Times New Roman' };

      worksheet.mergeCells('A4:V4');
      worksheet.getCell('A4').value = headerXLS.row3;
      worksheet.getCell('A4').alignment = { horizontal: 'center', vertical: 'middle' };
      worksheet.getCell('A4').font = { size: 13, bold: true, name: 'Times New Roman' };

      worksheet.mergeCells('A5:V5');
      worksheet.getCell('A5').value = headerXLS.row4;
      worksheet.getCell('A5').alignment = { horizontal: 'center', vertical: 'middle' };
      worksheet.getCell('A5').font = { size: 11, italic: true, name: 'Times New Roman' };

      worksheet.mergeCells('O6:V6');
      worksheet.getCell('O6').value = headerXLS.row5;
      worksheet.getCell('O6').alignment = { horizontal: 'center', vertical: 'middle' };
      worksheet.getCell('O6').font = { size: 11, italic: true, name: 'Times New Roman' };

      worksheet.mergeCells('A7:A11');
      worksheet.getCell('A7').value = "STT";

      worksheet.mergeCells('B7:B11');
      worksheet.getCell('B7').value = "Tên lĩnh vực/thủ tục";

      worksheet.mergeCells('C7:C10');
      worksheet.getCell('C7').value = "Mức độ thủ tục";

      worksheet.mergeCells('D7:N7');
      worksheet.getCell('D7').value = "Tiếp nhận hồ sơ TTHC";
      worksheet.getCell('D7').alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
      worksheet.getCell('D7').font = { size: 11, bold: true, name: 'Times New Roman' };
      worksheet.getCell('D7').border = { top: { style: 'thin' }, left: { style: 'thin' }, bottom: { style: 'thin' }, right: { style: 'thin' } };

      worksheet.mergeCells('O7:W7');
      worksheet.getCell('O7').value = "Trả kết quả giải quyết TTHC";
      worksheet.getCell('O7').alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
      worksheet.getCell('O7').font = { size: 11, bold: true, name: 'Times New Roman' };
      worksheet.getCell('O7').border = { top: { style: 'thin' }, left: { style: 'thin' }, bottom: { style: 'thin' }, right: { style: 'thin' } };

      worksheet.mergeCells('X7:AF7');
      worksheet.getCell('X7').value = "Dịch vụ công trực tuyến (DVCTT)";
      worksheet.getCell('X7').alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
      worksheet.getCell('X7').font = { size: 11, bold: true, name: 'Times New Roman' };
      worksheet.getCell('X7').border = { top: { style: 'thin' }, left: { style: 'thin' }, bottom: { style: 'thin' }, right: { style: 'thin' } };

      worksheet.mergeCells('AG7:AI7');
      worksheet.getCell('AG7').value = "TTHC cung cấp dịch vụ BCCI";
      worksheet.getCell('AG7').alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
      worksheet.getCell('AG7').font = { size: 11, bold: true, name: 'Times New Roman' };
      worksheet.getCell('AG7').border = { top: { style: 'thin' }, left: { style: 'thin' }, bottom: { style: 'thin' }, right: { style: 'thin' } };

      worksheet.mergeCells('AJ7:AO7');
      worksheet.getCell('AJ7').value = "Thanh toán trực tuyến";
      worksheet.getCell('AJ7').alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
      worksheet.getCell('AJ7').font = { size: 11, bold: true, name: 'Times New Roman' };
      worksheet.getCell('AJ7').border = { top: { style: 'thin' }, left: { style: 'thin' }, bottom: { style: 'thin' }, right: { style: 'thin' } };

      // Tiếp nhận hồ sơ TTHC
      worksheet.mergeCells('D8:D10');
      worksheet.getCell('D8').value = "Tổng số hồ sơ tiếp nhận trong tháng";
      worksheet.mergeCells('E8:E10');
      worksheet.getCell('E8').value = "Trực tiếp";
      worksheet.mergeCells('F8:F10');
      worksheet.getCell('F8').value = "Qua BCCI";

      worksheet.mergeCells('G8:L8');
      worksheet.getCell('G8').value = "Trực tuyến";
      worksheet.mergeCells('G9:H9');
      worksheet.getCell('G9').value = "Một phần";
      worksheet.getCell('G10').value = "Số hồ sơ";
      worksheet.getCell('H10').value = "Tỷ lệ %";
      worksheet.mergeCells('I9:J9');
      worksheet.getCell('I9').value = "Toàn trình";
      worksheet.getCell('I10').value = "Số hồ sơ";
      worksheet.getCell('J10').value = "Tỷ lệ %";
      worksheet.mergeCells('K9:L9');
      worksheet.getCell('K9').value = "Trực tuyến (một phần và toàn trình)";
      worksheet.getCell('K10').value = "Số hồ sơ";
      worksheet.getCell('L10').value = "Tỷ lệ %";
      worksheet.mergeCells('M8:N9');
      worksheet.getCell('M8').value = "Cập nhật lên iGate";
      worksheet.getCell('M10').value = "Số hồ sơ tiếp nhận đã cập nhật lên iGate";
      worksheet.getCell('N10').value = "Tỷ lệ %";

      // Trả kết quả giải quyết TTHC
      worksheet.mergeCells('O8:O10');
      worksheet.getCell('O8').value = "Tổng số";
      worksheet.mergeCells('P8:Q9');
      worksheet.getCell('P8').value = "Trả trực tiếp";
      worksheet.getCell('P10').value = "Số hồ sơ";
      worksheet.getCell('Q10').value = "Tỷ lệ %";
      worksheet.mergeCells('R8:S9');
      worksheet.getCell('R8').value = "Trả qua dịch vụ BCCI";
      worksheet.getCell('R10').value = "Số hồ sơ";
      worksheet.getCell('S10').value = "Tỷ lệ %";
      worksheet.mergeCells('T8:U9');
      worksheet.getCell('T8').value = "Trả trực tuyến";
      worksheet.getCell('T10').value = "Số hồ sơ";
      worksheet.getCell('U10').value = "Tỷ lệ %";
      worksheet.mergeCells('V8:V10');
      worksheet.getCell('V8').value = "Cập nhật lên iGate";
      worksheet.mergeCells('W8:W10');
      worksheet.getCell('W8').value = "Số hồ sơ TTHC sử dụng ký số trong giải quyết";

      // Dịch vụ công trực tuyến (DVCTT)
      worksheet.mergeCells('X8:X10');
      worksheet.getCell('X8').value = "Tổng số TTHC của cơ quan, đơn vị";
      worksheet.mergeCells('Y8:Y10');
      worksheet.getCell('Y8').value = "Số DVCTT có phát sinh hồ sơ";
      worksheet.mergeCells('Z8:Z10');
      worksheet.getCell('Z8').value = "Tỷ lệ DVCTT có phát sinh hồ sơ";
      worksheet.mergeCells('AA8:AC9');
      worksheet.getCell('AA8').value = "Một phần";
      worksheet.getCell('AA10').value = "Số DVCTT một phần";
      worksheet.getCell('AB10').value = "Số DVCTT một phần có phát sinh hồ sơ";
      worksheet.getCell('AC10').value = "Tỷ lệ %";

      worksheet.mergeCells('AD8:AF9');
      worksheet.getCell('AD8').value = "Toàn trình";
      worksheet.getCell('AD10').value = "Số DVCTT toàn trình";
      worksheet.getCell('AE10').value = "Số DVCTT toàn trình có phát sinh hồ sơ";
      worksheet.getCell('AF10').value = "Tỷ lệ %";

      // TTHC cung cấp dịch vụ BCCI
      worksheet.mergeCells('AG8:AG10');
      worksheet.getCell('AG8').value = "Số TTHC cung cấp dịch vụ BCCI";

      worksheet.mergeCells('AH8:AI9');
      worksheet.getCell('AH8').value = "TTHC cung cấp dịch vụ BCCI có phát sinh hồ sơ";
      worksheet.getCell('AH10').value = "Số TTHC cung cấp dịch vụ BCCI có phát sinh hồ sơ";
      worksheet.getCell('AI10').value = "Tỷ lệ %";

      // Thanh toán trực tuyến
      worksheet.mergeCells('AJ8:AJ10');
      worksheet.getCell('AJ8').value = "Số TTHC cung cấp dịch vụ thanh toán trực tuyến";
      worksheet.mergeCells('AK8:AK10');
      worksheet.getCell('AK8').value = "Số hồ sơ TTHC phát sinh của các TTHC cung cấp dịch vụ thanh toán trực tuyến (bao gồm trực tiếp, trực tuyến, BCCI)";
      worksheet.mergeCells('AL8:AM9');
      worksheet.getCell('AL8').value = "Số TTHC cung cấp dịch vụ thanh toán trực tuyến có phát sinh hồ sơ";
      worksheet.getCell('AL10').value = "Số TTHC";
      worksheet.getCell('AM10').value = "Tỷ lệ %";
      worksheet.mergeCells('AN8:AO9');
      worksheet.getCell('AN8').value = "Thực hiện thanh toán trực tuyến";
      worksheet.getCell('AN10').value = "Số hồ sơ TTHC đã giải quyết sử dụng dịch vụ thanh toán trực tuyến";
      worksheet.getCell('AO10').value = "Tỷ lệ %";

      const rowStartHeaderContent = 11;
      const NumberCol = 41;
      for (let index = 0; index < NumberCol; index++) {
        worksheet.getCell(10, (index + 1)).alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
        worksheet.getCell(10, (index + 1)).font = { size: 11, bold: true, name: 'Times New Roman' };
        worksheet.getCell(10, (index + 1)).border = { top: { style: 'thin' }, left: { style: 'thin' }, bottom: { style: 'thin' }, right: { style: 'thin' } };

        worksheet.getCell(9, (index + 1)).alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
        worksheet.getCell(9, (index + 1)).font = { size: 11, bold: true, name: 'Times New Roman' };
        worksheet.getCell(9, (index + 1)).border = { top: { style: 'thin' }, left: { style: 'thin' }, bottom: { style: 'thin' }, right: { style: 'thin' } };

        worksheet.getCell(8, (index + 1)).alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
        worksheet.getCell(8, (index + 1)).font = { size: 11, bold: true, name: 'Times New Roman' };
        worksheet.getCell(8, (index + 1)).border = { top: { style: 'thin' }, left: { style: 'thin' }, bottom: { style: 'thin' }, right: { style: 'thin' } };

        if (index > 1) {
          worksheet.getCell(rowStartHeaderContent, (index + 1)).value = "(" + (index - 1).toString() + ")";
        }
        worksheet.getCell(rowStartHeaderContent, (index + 1)).alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
        worksheet.getCell(rowStartHeaderContent, (index + 1)).font = { size: 11, bold: true, name: 'Times New Roman' };
        worksheet.getCell(rowStartHeaderContent, (index + 1)).border = { top: { style: 'thin' }, left: { style: 'thin' }, bottom: { style: 'thin' }, right: { style: 'thin' } };
      }

      // I. ĐƠN VỊ PHƯỜNG XÃ
      let rowStartContent = rowStartHeaderContent + 1;
      worksheet.getCell(rowStartContent, 1).value = "I";
      worksheet.getCell(rowStartContent, 1).alignment = { horizontal: 'center', vertical: 'middle' };
      worksheet.getCell(rowStartContent, 1).font = { size: 13, bold: true, name: 'Times New Roman' };
      worksheet.mergeCells(rowStartContent, 2, rowStartContent, NumberCol);
      worksheet.getCell(rowStartContent, 2).value = "Đơn vị Phường/Xã";
      worksheet.getCell(rowStartContent, 2).alignment = { horizontal: 'left', vertical: 'middle' };
      worksheet.getCell(rowStartContent, 2).font = { size: 13, bold: true, name: 'Times New Roman' };
      worksheet.getCell(rowStartContent, 2).border = { top: { style: 'thin' }, left: { style: 'thin' }, bottom: { style: 'thin' }, right: { style: 'thin' } };
      rowStartContent = rowStartContent + 1;

      // CẤP ĐƠN VỊ PHƯỜNG XÃ DATA
      const data = this.soData;
      for (let i = 0; i < data.length; i++) {
        var item = data[i];
        var r = 0;

        worksheet.mergeCells(rowStartContent, 1, rowStartContent, 1);
        worksheet.getCell(rowStartContent, 1).value = i + 1;

        worksheet.mergeCells(rowStartContent, 2, rowStartContent, 2);
        worksheet.getCell(rowStartContent, 2).value = item.name;
        worksheet.getCell(rowStartContent + r, 3).value = "Trong kỳ";

        // Trong kỳ 
        var column = 4;
        for (let i = 0; i < this.listProp.length; i++) {
          worksheet.getCell(rowStartContent + r, column++).value = item[this.listProp[i].name + '_tky'].toString();
        }
        // format cell trong kỳ
        for (let c = 0; c < NumberCol; c++) {
          worksheet.getCell(rowStartContent + r, (c + 1)).alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
          worksheet.getCell(rowStartContent + r, (c + 1)).font = { size: 11, name: 'Times New Roman' };
          worksheet.getCell(rowStartContent + r, (c + 1)).border = { top: { style: 'thin' }, left: { style: 'thin' }, bottom: { style: 'thin' }, right: { style: 'thin' } };
        }

        rowStartContent = rowStartContent + 1;
      }

      //Tổng cấp tỉnh
      worksheet.mergeCells(rowStartContent, 1, rowStartContent, 2);
      worksheet.getCell(rowStartContent, 1).value = "TỔNG ĐƠN VỊ PHƯỜNG XÃ";
      worksheet.getCell(rowStartContent, 1).alignment = { horizontal: 'center', vertical: 'middle', };
      worksheet.getCell(rowStartContent, 1).font = { size: 11, bold: true, name: 'Times New Roman' };
      worksheet.getCell(rowStartContent, 1).border = { top: { style: 'thin' }, left: { style: 'thin' }, bottom: { style: 'thin' }, right: { style: 'thin' } };

      const dataSum = this.soDataSum;
      var column = 4;
      worksheet.getCell(rowStartContent, 3).value = "Trong kỳ";
      for (let i = 0; i < this.listProp.length; i++) {
        if (this.listProp[i].type) {
          worksheet.getCell(rowStartContent, column++).value = dataSum[this.listProp[i].name + '_tky'].toString();
        }
        else {
          worksheet.getCell(rowStartContent, column++).value = this.listProp[i].fixValue.toString();
        }
      }

      // FORMAT FILE
      for (let index = 2; index < NumberCol; index++) {
        worksheet.getCell(rowStartContent, (index + 1)).alignment = { horizontal: 'center', vertical: 'middle' };
        worksheet.getCell(rowStartContent, (index + 1)).font = { size: 11, bold: true, name: 'Times New Roman' };
        worksheet.getCell(rowStartContent, (index + 1)).border = { top: { style: 'thin' }, left: { style: 'thin' }, bottom: { style: 'thin' }, right: { style: 'thin' } };
      }

      worksheet.getColumn(1).width = 7;
      worksheet.getColumn(1).alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
      worksheet.getColumn(2).width = 25;
      worksheet.getColumn(2).alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
      worksheet.getColumn(3).width = 22;
      worksheet.getCell(12, 2).alignment = { horizontal: 'left', vertical: 'middle', wrapText: true };
      // Save Excel File
      workbook.xlsx.writeBuffer().then((dataBuffer: ArrayBuffer) => {
        const blob = new Blob([dataBuffer], { type: this.EXCEL_TYPE });
        fs.saveAs(blob, excelFileName + this.EXCEL_EXTENSION);
      });
    }
  }
}
