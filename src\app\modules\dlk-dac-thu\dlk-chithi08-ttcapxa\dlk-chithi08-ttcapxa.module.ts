import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { NgxPrinterModule } from 'ngx-printer';
import { SharedModule } from 'src/app/shared/shared.module';
import { DlkChithi08TtcapxaRoutingModule } from './dlk-chithi08-ttcapxa-routing.module';
import { DlkChithi08TtcapxaComponent } from './dlk-chithi08-ttcapxa.component';


@NgModule({
  declarations: [DlkChithi08TtcapxaComponent],
  imports: [
    CommonModule,
    DlkChithi08TtcapxaRoutingModule,
        SharedModule,
        NgxPrinterModule.forRoot({ printOpenWindow: true }),
  ]
})
export class DlkChithi08TtcapxaModule { }
