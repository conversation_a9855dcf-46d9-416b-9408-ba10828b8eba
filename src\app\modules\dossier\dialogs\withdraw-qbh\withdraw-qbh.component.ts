import { Component, OnInit, Inject, ViewChild } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { KeycloakService } from 'keycloak-angular';
import { DossierService } from 'src/app/data/service/dossier/dossier.service';
import { UserService } from 'src/app/data/service/user.service';
import * as ClassicEditor from '@ckeditor/ckeditor5-build-classic/build/ckeditor';
import '@ckeditor/ckeditor5-build-classic/build/translations/vi';
import { EnvService } from 'src/app/core/service/env.service';
import { SnackbarService } from 'src/app/data/service/snackbar/snackbar.service';
import { DatePipe } from '@angular/common';
import { ProcedureService } from 'src/app/data/service/procedure/procedure.service';
import { FormControl, FormGroup } from '@angular/forms';
import { NgxMatDateAdapter, NGX_MAT_DATE_FORMATS } from '@angular-material-components/datetime-picker';
import { PICK_FORMATS } from 'src/app/data/service/config.service';
import { CustomDatetimeAdapter } from 'src/app/shared/ts/custom-datetime-adapter';
import { DeploymentService } from 'src/app/data/service/deployment.service';
import * as tUtils from 'src/app/data/service/thoai.service';
import { CheckSendNotify, NotificationService } from 'src/app/data/service/notification.service';
import { PadmanService } from 'src/app/data/service/svc-padman/padman.service';
import { NotifyQNIService } from 'src/app/shared/components/check-send-notify-qni/check-send-notify-qni.service';
import {LogmanService} from 'data/service/logman/logman.service';
import { AgencyService } from 'src/app/data/service/basedata/agency.service';
import { FilemanService } from 'src/app/data/service/svc-fileman/fileman.service';
import { ConfigService } from 'src/app/data/service/dossier/config.service';
@Component({
  selector: 'app-withdraw-qbh',
  templateUrl: './withdraw-qbh.component.html',
  styleUrls: ['./withdraw-qbh.component.scss'],
  providers: [
    {
      provide: NgxMatDateAdapter,
      useClass: CustomDatetimeAdapter
    },
    {
      provide: NGX_MAT_DATE_FORMATS,
      useValue: PICK_FORMATS
    }
  ]
})
export class WithdrawQBHComponent implements OnInit {

  config = this.envService.getConfig();
  env = this.deploymentService.getAppDeployment()?.env;
  selectedLang = localStorage.getItem('language') || 'vi';
  selectedLangId = Number(localStorage.getItem('languageId')) || 228;
  dossierTaskStatusWithdraw = { id: '', name: []};
  dossierMenuTaskRemindWithdraw = {id: '', name: []};
  dossierId: string;
  commentContent = '';
  fullname: string;
  userName: string;
  userId: string;
  accountId: string;
  dossierCode: string;
  isDirectory: number;
  isAllAgencySearch: boolean;
  isCKMaxlenght = false;
  dossierDetail: any;
  getApprovalAgency = '';
  processDetail: any;
  public Editor = ClassicEditor;
  commentConfig = {
    placeholder: 'Nhập nội dung...',
    language: 'vi',
    removePlugins: ['MediaEmbed', 'ImageUpload'],
    toolbarLocation: 'bottom'
  };
  nowDate = tUtils.newDate();
  startDate = this.nowDate;
  public dates: Date;
  public showSpinners = true;
  public showSeconds = true;
  public touchUi = true;
  public enableMeridian = false;
  public stepHour = 1;
  public stepMinute = 1;
  public stepSecond = 1;
  public defaultTime = ['00', '00', '00'];
  smsEmailContent = '';
  smsEmailMaxLength = null;
  willSend = false;
  willSendEmail = false;
  willSendSMS = false;
  listEmail = [];
  listPhone = [];
  textPhone = '';
  textEmail = '';
  appointmentDateOld: string;
  appointmentDateNew: string;
  // ================================================= Upload file
  acceptFileExtension = this.config.acceptFileExtension;
  acceptFileType = this.config.acceptFileType;
  maxFileSize = this.env?.maxFileSize || this.config.maxFileSize;
  uploaded: boolean;
  blankVal: any;
  uploadedImage = [];
  files = [];
  urls = [];
  fileNames = [];
  fileNamesFull = [];
  listProcedureProcess = [];
  dossierTaskStatus = { id: '', name: []};
  dossierMenuTaskRemind = {id: '', name: []};
  @ViewChild('startDatePicker') startDatePicker: any;
  notifyContentBody: any;
  //Thu tuc co cau hinh dong bo BXD-LGSP Tan Tan
  isSyncDBNLGSPTanDanBXD = 0;
  statusName = "";
  fileTemplate = "";
  totalCost = '';
  notifyQNI = this.deploymentService.getAppDeployment()?.notifyqni?.enable;
  drawMsg = {
    vi: 'Đã rút hồ sơ!',
    en: 'Cancelled processing!'
  };
  requestdrawMsg = {
    vi: 'Đã gửi yêu cầu dừng xử lý!',
    en: 'Sent request for cancel processing!'
  };
  requireAttachmentWhenWithDraw = false;
  attachFiles = [];
  attachFilePreview = [];
  resultFiles = [];
  resultFilePreview = [];
  apologyTextFiles = [];
  apologyTextFilePreview = [];
  ckeditorMaxLengthNotification = "";
  fileConverted = [];
  ckeditorMaxLength = this.deploymentService.env.OS_HCM.ckeditorMaxLength;
  isBoTNMTQNM = this.env?.OS_QNM?.boTNMT === true ? this.env?.OS_QNM?.boTNMT : false;
  isBoTNMTHCM = this.deploymentService.env.OS_HCM.maTTBoTNMT;
  isBoTNMTGLI = this.deploymentService.env.OS_GLI.boTNMT;
  isSmsQNM = this.env?.OS_QNM?.isSmsQNM ? true : false;
  convertProcessingFile = this.deploymentService.getAppDeployment()?.convertProcessingFile;
    filePDF ={
    id: "",
    filename: ""
  };
  // config vbdlis
  vbdlisConfigId = this.deploymentService.env?.OS_QNI?.vbdlisConfigId  ? this.deploymentService.env?.OS_QNI?.vbdlisConfigId : "";
  constructor(
    private userService: UserService,
    private envService: EnvService,
    private notiService: NotificationService,
    private deploymentService: DeploymentService,
    private keycloakService: KeycloakService,
    public dialogRef: MatDialogRef<WithdrawQBHComponent>,
    @Inject(MAT_DIALOG_DATA) public data: WithdrawQBHModel,
    private snackbarService: SnackbarService,
    private dossierService: DossierService,
    private datePipe: DatePipe,
    private procedureService: ProcedureService,
    private padmanService: PadmanService,
    private notifyQNIService: NotifyQNIService,
    private logmanService: LogmanService,
    private agencyService: AgencyService,
    private filemanService: FilemanService,
    private configService: ConfigService,
  ) {
    this.dossierId = data.dossierId;
    this.dossierCode = data.dossierCode;
    this.isDirectory = data.isDirectory;
    this.isAllAgencySearch = data.isAllAgencySearch;
  }

  ngOnInit(): void {
    this.getUserAccount();
    this.getDossierDetail();
    this.getDossierTaskStatus();
    this.getDossierMenuTaskRemind();
    this.getDossierMenuTaskRemindDirect();
    this.getDossierTaskStatusWithDraw();
    this.getDossierAttachment()
    this.setEnvVariable();
    if (this.selectedLang === 'vi') {
      this.ckeditorMaxLengthNotification = "Nội dung không quá 500 ký tự!";
      if (this.ckeditorMaxLength > 0)
        this.ckeditorMaxLengthNotification = "Nội dung không quá "+ this.ckeditorMaxLength + " ký tự!";
    } else if (this.selectedLang === 'en') {
      this.ckeditorMaxLengthNotification = "Content must not exceed 500 characters!";
      if (this.ckeditorMaxLength > 0)
        this.ckeditorMaxLengthNotification = "Content must not exceed "+ this.ckeditorMaxLength + " characters!";
    }
  }

  setEnvVariable(){
    this.fileTemplate = !!this.env?.fileTemplate?.withdraw ? this.env?.fileTemplate?.withdraw : "";
    this.requireAttachmentWhenWithDraw = this.env?.OS_BDG?.isRequiredUploadFileBDG?.withdraw != undefined && this.env?.OS_BDG?.isRequiredUploadFileBDG?.withdraw != null ? this.env?.OS_BDG?.isRequiredUploadFileBDG?.withdraw : false;
  }

  downloadTemplate(){
    window.open(this.fileTemplate, '_self');
  }

  getProcedureDetail(procedureId) {
    this.procedureService.getProcedureDetail(procedureId).subscribe(async data => {
      this.isSyncDBNLGSPTanDanBXD = data?.isSyncDBNLGSPTanDanBXD ? Number(data?.isSyncDBNLGSPTanDanBXD) : 0;
    }, err => {
      console.log(err);
    });
  }

  getUserAccount() {
    this.keycloakService.loadUserProfile().then(user => {
      // tslint:disable-next-line: no-string-literal
      this.userId = user['attributes'].user_id[0];
      this.accountId = user['attributes'].account_id[0];
      this.userName =  user.username;
      this.userService.getUserInfo(this.userId).subscribe(data => {
        this.fullname = data.fullname;
      });
    });
  }
  getFullUserInfo(userId){
    this.userService.getFullUserInfo(userId).subscribe(data => {
    });
  }
  async getDossierDetail() {
    await this.getDossierFeeNotify();
    this.dossierService.getDossierDetail(this.dossierId).subscribe(data => {
      this.dossierDetail = data;
      if (data.appointmentDate !== undefined && data.appointmentDate !== null){
        this.appointmentDateOld = this.datePipe.transform(data.appointmentDate, 'dd/MM/yyyy HH:mm:ss');
      }else{
        if (data.acceptedDate !== undefined) {
          const dossierEndDate = new Date(data.acceptedDate);
          // tslint:disable-next-line: max-line-length
          if (data.processingTime !== undefined || data.processingTime != null) {
            dossierEndDate.setDate(dossierEndDate.getDate() + data.processingTime);
            this.appointmentDateOld = this.datePipe.transform(new Date(dossierEndDate), 'dd/MM/yyyy HH:mm:ss');
          }
        }
      }
      // info applicant
      //this.getFullUserInfo(data.applicant.userId);
      // tslint:disable-next-line:max-line-length
      if ( data.applicant.data.phoneNumber !== null && data.applicant.data.phoneNumber !== '' && data.applicant.data.phoneNumber !== undefined) {
        this.textPhone = data.applicant.data.phoneNumber;
        this.listPhone.push({
          number: data.applicant.data.phoneNumber,
          nextFlow: ''
        });
      }
      if ( data.applicant.data.email !== null && data.applicant.data.email !== '' && data.applicant.data.email !== undefined) {
        this.textEmail = data.applicant.data.email;
        this.listEmail.push({
          email: data.applicant.data.email,
          nextFlow: ''
        });
      }
      const agencies = this.agencyService.getAgencies();
      const extend = {
        dossier:{
          id: this.dossierId,
          code: this.dossierCode,
        },
        agencies: agencies
      };
      setTimeout(() => {
        if(!!this.notifyQNI){
          this.notifyQNIService.checkSendSubject.next(
            {
              id: data?.procedureProcessDefinition?.id,
              phone: data?.applicant?.data?.phoneNumber,
              email: data?.applicant?.data?.email,
              currentTask: "",
              contentParams: {
                parameters: {
                  sector: !!data?.procedure?.sector?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name ? data?.procedure?.sector?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name : '',
                  procedure: !!data?.procedure?.translate?.find(agency => agency.languageId === Number(this.selectedLangId))?.name ? data?.procedure?.translate?.find(agency => agency.languageId === Number(this.selectedLangId))?.name : '',
                  agency: !!data?.agency?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name ? data?.agency?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name : '',
                  agencyId: data?.agency?.id,
                  applicantFullname: data?.applicant?.data?.fullname,
                  officerFullname: '',
                  subsystem: !!this.env?.subsystem?.name ? this.env?.subsystem?.name : "Hệ thống Một cửa điện tử",
                  dossierCode: data?.code,
                  nextTask: !!this.env?.notify?.suspendDossier?.nextTask ? this.env?.notify?.suspendDossier?.nextTask : 'Tạm dừng xử lý',
                  dossierSearchPadsvcUrl: (!!this.env?.domain?.padsvc ? this.env?.domain?.padsvc : "http://padsvctest.vncitizens.vn/vi/") + "dossier/public",
                  dossierDetailPadsvcUrl: (!!this.env?.domain?.padsvc ? this.env?.domain?.padsvc : "http://padsvctest.vncitizens.vn/vi/") + "dossier/detail/" + this.dossierId,
                  dossierSearchUrl: (!!this.env?.domain?.onegate ? this.env?.domain?.onegate : "http://padsvctest.vncitizens.vn/vi/") + "dossier/search/",
                  dossierDetailUrl: (!!this.env?.domain?.onegate ? this.env?.domain?.onegate : "http://padsvctest.vncitizens.vn/vi/") + "dossier/search/" + this.dossierId,
                  returnMethod: !!data?.dossierReceivingKind?.name?.find(recieve => recieve.languageId === Number(this.selectedLangId))?.name ? data?.dossierReceivingKind?.name?.find(recieve => recieve.languageId === Number(this.selectedLangId))?.name : '',
                  receivingDate: '',
                  appointmentDate: '',
                  dossierFee: this.totalCost
                }
              }
            }
          );
        } else if(this.isSmsQNM) {
          this.notiService.checkSendSubject.next({
            id: data?.procedureProcessDefinition?.id,
            phone: data?.applicant?.data?.phoneNumber,
            email: data?.applicant?.data?.email,
            contentParams: {
              parameters: {
                sector: !!data?.procedure?.sector?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name ? data?.procedure?.sector?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name : '',
                procedure: !!data?.procedure?.translate?.find(agency => agency.languageId === Number(this.selectedLangId))?.name ? data?.procedure?.translate?.find(agency => agency.languageId === Number(this.selectedLangId))?.name : '',
                agency: !!data?.agency?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name ? data?.agency?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name : '',
                applicantFullname: data?.applicant?.data?.fullname,
                officerFullname: '',
                subsystem: !!this.env?.subsystem?.name ? this.env?.subsystem?.name : "Hệ thống Một cửa điện tử",
                dossierCode: data?.nationCode ? data?.nationCode : data?.code,
                nextTask: !!this.env?.notify?.requestWithdrawDossier?.nextTask ? this.env?.notify?.requestWithdrawDossier?.nextTask : 'Yêu cầu rút hồ sơ',
                dossierSearchPadsvcUrl: (!!this.env?.domain?.padsvc ? this.env?.domain?.padsvc : "http://padsvctest.vncitizens.vn/vi/") + "dossier/public",
                dossierDetailPadsvcUrl: (!!this.env?.domain?.padsvc ? this.env?.domain?.padsvc : "http://padsvctest.vncitizens.vn/vi/") + "dossier/detail/" + this.dossierId,
                dossierSearchUrl: '',
                dossierDetailUrl: '',
                returnMethod: !!data?.dossierReceivingKind?.name?.find(recieve => recieve.languageId === Number(this.selectedLangId))?.name ? data?.dossierReceivingKind?.name?.find(recieve => recieve.languageId === Number(this.selectedLangId))?.name : '',
                receivingDate: '',
                appointmentDate: '',
                dossierFee: this.totalCost,
                extend: extend
              }
            }
          });
        }
        else {
          this.notifyContentBody = {
            id: data?.procedureProcessDefinition?.id,
            phone: data?.applicant?.data?.phoneNumber,
            email: data?.applicant?.data?.email,
            contentParams: {
              parameters: {
                sector: !!data?.procedure?.sector?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name ? data?.procedure?.sector?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name : '',
                procedure: !!data?.procedure?.translate?.find(agency => agency.languageId === Number(this.selectedLangId))?.name ? data?.procedure?.translate?.find(agency => agency.languageId === Number(this.selectedLangId))?.name : '',
                agency: !!data?.agency?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name ? data?.agency?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name : '',
                applicantFullname: data?.applicant?.data?.fullname,
                officerFullname: '',
                subsystem: !!this.env?.subsystem?.name ? this.env?.subsystem?.name : "Hệ thống Một cửa điện tử",
                dossierCode: data?.nationCode ? data?.nationCode : data?.code,
                nextTask: !!this.env?.notify?.requestWithdrawDossier?.nextTask ? this.env?.notify?.requestWithdrawDossier?.nextTask : 'Yêu cầu rút hồ sơ',
                dossierSearchPadsvcUrl: (!!this.env?.domain?.padsvc ? this.env?.domain?.padsvc : "http://padsvctest.vncitizens.vn/vi/") + "dossier/public",
                dossierDetailPadsvcUrl: (!!this.env?.domain?.padsvc ? this.env?.domain?.padsvc : "http://padsvctest.vncitizens.vn/vi/") + "dossier/detail/" + this.dossierId,
                dossierSearchUrl: '',
                dossierDetailUrl: '',
                returnMethod: !!data?.dossierReceivingKind?.name?.find(recieve => recieve.languageId === Number(this.selectedLangId))?.name ? data?.dossierReceivingKind?.name?.find(recieve => recieve.languageId === Number(this.selectedLangId))?.name : '',
                receivingDate: '',
                appointmentDate: '',
                dossierFee: this.totalCost
              }
            }
          }
          this.notiService.checkSendSubject.next(this.notifyContentBody);
        }
      }, this.config?.reloadTimeout);
      this.getProcedureProcessDetail(data.procedureProcessDefinition.id);
      this.getProcedureDetail(data?.procedure?.id);
    });
  }
  
  getDossierFeeNotify() {
    return new Promise<void>(resolve => {
      this.padmanService.getDossierFee(this.dossierId).toPromise().then(res => {
        if(res && res.length > 0)
        {
          let totalCostValue = 0;
          let totalPaidValue = 0;
          res.forEach(element => {
            totalCostValue += element.quantity * element.amount;
            totalPaidValue += element.paid;
          });
          const monetaryUnit = res[0]?.monetaryUnit ? res[0].monetaryUnit : 'VNĐ';
          let total = totalCostValue - totalPaidValue;
          this.totalCost = total > 0 ? Number((total).toFixed(1)).toLocaleString() + ' ' + monetaryUnit : '';
        }
        resolve();
      }).catch(err => {
        resolve();
      });
    });
  }

  getProcedureProcessDetail(processId) {
    this.procedureService.getProcedureProcessDetail(processId).subscribe(data => {
      this.processDetail = data;
    });
  }
  getDossierTaskStatus() {
    this.dossierService.getAgencyTag(this.deploymentService.env.dossierTaskStatus.waitWithdrawn.id).subscribe(rs => {
      this.dossierTaskStatus.id = rs.id;
      this.dossierTaskStatus.name = rs.trans;
    }, err => {
      console.log(err);
    });
  }

  getDossierMenuTaskRemindDirect() {
    this.dossierService.getAgencyTag(this.deploymentService.env.dossierMenuTaskRemind.hadWithdrawn.id).subscribe(rs => {
      this.dossierMenuTaskRemindWithdraw.id = rs.id;
      this.dossierMenuTaskRemindWithdraw.name = rs.trans;
    }, err => {
      console.log(err);
    });
  }

  getDossierTaskStatusWithDraw() {
    this.dossierService.getAgencyTag(this.deploymentService.env.dossierTaskStatus.hasWithdrawn.id).subscribe(rs => {
      this.dossierTaskStatusWithdraw.id = rs.id;
      this.dossierTaskStatusWithdraw.name = rs.trans;
    }, err => {
      console.log(err);
    });
  }
  getDossierMenuTaskRemind() {
    this.dossierService.getAgencyTag(this.deploymentService.env.dossierMenuTaskRemind.withdrawalRequest.id).subscribe(rs => {
      this.dossierMenuTaskRemind.id = rs.id;
      this.dossierMenuTaskRemind.name = rs.trans;
      this.statusName = rs?.trans.filter(t => t.languageId === Number(this.selectedLangId))?.name;
    }, err => {
      console.log(err);
    });
  }

  async onConfirm() {

    // check date
    const checkedDate = true;
    // if (new Date() < new Date(formObj.startDate)) {
    //     checkedDate = true;
    // }else{
    //   const msgObj = {
    //     vi: 'Vui lòng nhập ngày trả hồ sơ lớn hơn thời gian hiện tại!',
    //     en: 'Please enter a payment appointment that is larger than the current time!'
    //   };
    //   this.snackbarService.openSnackBar(2, '', msgObj[this.selectedLang], 'warning_notification', this.config.expiredTime);
    // }
    if (this.commentContent.trim() === ''){
      const msgObj = {
        vi: 'Vui lòng nhập lý do rút hồ sơ!',
        en: 'Please enter a reason for withdrawal!'
      };
      this.snackbarService.openSnackBar(2, '', msgObj[this.selectedLang], 'warning_notification', this.config.expiredTime);
    }
    if (this.isCKMaxlenght){
      let msgObj;
      if (this.ckeditorMaxLength > 0) {
        msgObj = {
          vi: 'Nội dung không quá ' + this.ckeditorMaxLength + ' ký tự!',
          en: 'Content must not exceed ' + this.ckeditorMaxLength + ' characters!'
        };
      } else {
        msgObj = {
          vi: 'Nội dung không quá 500 ký tự!',
          en: 'Content must not exceed 500 characters!'
        };
      }
      this.snackbarService.openSnackBar(2, '', msgObj[this.selectedLang], 'warning_notification', this.config.expiredTime);
    }
    let checkRequireAttachment = true;
    if (this.requireAttachmentWhenWithDraw && this.files.length === 0){
      checkRequireAttachment = false;
      const msgObj = {
        vi: 'Vui lòng đính kèm tệp tin!',
        en: 'Please attach file!'
      };
      this.snackbarService.openSnackBar(2, '', msgObj[this.selectedLang], 'warning_notification', this.config.expiredTime);
    }
    if (!this.isCKMaxlenght && this.commentContent.trim() !== '' && checkedDate && checkRequireAttachment) {
      if (this.files.length > 0) {
         this.keycloakService.loadUserProfile().then(user => {
          // tslint:disable-next-line: no-string-literal
          const accountId = user['attributes'].user_id[0];
          
          this.uploadMultiFile(this.files, accountId);
        });
      } else {
        if ( this.env?.isDienBien){
          if ( this.isDirectory !== 1 ){
            this.putDossierWithdrawWithComment();
          }else{
            this.putDossierWithdrawWithComment2();
          }
        }else{
          this.putDossierWithdrawWithComment();
        }
      }
    }
  }

  async sendNotiToVBDLIS(code, comment){
    let isVbdlis = true;
    let messageError = "";
      const requestBodyObj = {
        SoBienNhan:  code,
        NoiDung: comment,
        LoaiThongBao: 0
      };
      const requestBody = JSON.stringify(requestBodyObj);
          const response = await this.dossierService.sendNotiToVbdlis(this.vbdlisConfigId, requestBody).toPromise().catch(error => {
            isVbdlis = false;
            console.log(error);
          });
          isVbdlis = response.affectedRows == 0 ? false : true;
          messageError = response.message ? response.message : "Gởi thông báo VBDLIS thất bại!";

          if (isVbdlis == true) {
            const msgObj = {
              vi: 'Gởi thông báo VBDLIS thành công!',
              en: 'Successful reception VBDLIS!',
            };
            this.snackbarService.openSnackBar(
              1,
              msgObj[this.selectedLang],
              '',
              'success_notification',
              this.config.expiredTime
            );

          } else {
            const msgObj = {
              vi: messageError,
              en: 'Failed reception VBDLIS!'
            };
            this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', 2000);
          }
  }

  async putDossierWithdrawWithComment(){
    if(this.convertProcessingFile){
      await this.convertToPDF();     
    }
    const requestBodyObj = {
      dossierStatus: 6,
      comment: '',
      withdrawDate: '',
      user: {
        id: this.userId,
        fullname: this.fullname,
        account: {
          id: this.accountId,
          username: [
            {
              value: this.userName
            }
          ]
        }
      },
      attachment: this.uploadedImage,
      dossierTaskStatus: this.dossierTaskStatus,
      dossierMenuTaskRemind :  this.dossierMenuTaskRemind,
    };
    if (this.commentContent.trim() !== '') {
      var commentTrim = this.commentContent.replace(/&nbsp;/g, '');
      this.postComment(commentTrim);
      requestBodyObj.comment = commentTrim;
    } else {
      const msgObj = {
        vi: 'Rút hồ sơ <b>' + this.dossierCode + '</b> <br /> Lý do:' + requestBodyObj.comment,
        en: 'Dossier <b>' + this.dossierCode + '</b> has been withdrawal <br /> Reason:' + requestBodyObj.comment
      };
      this.postComment(msgObj[this.selectedLang]);
      requestBodyObj.comment = msgObj[this.selectedLang];
    }
    const msgObjTemp = {
      vi: 'Ngày rút hồ sơ',
      en: 'Withdrawal date'
    };
    this.postHistory(3, msgObjTemp[this.selectedLang], '', this.datePipe.transform(this.nowDate, 'dd/MM/yyyy HH:mm:ss'));
    this.smsEmailContent = requestBodyObj.comment;
    const requestBody = JSON.stringify(requestBodyObj, null, 2);
    this.dossierService.putDossierWithdrawWithComment(this.dossierId, requestBody).subscribe(data => {
      if (data.affectedRows === 1) {

        // thông báo rút hồ sơ qua VBDLIS
        if(this.dossierDetail?.extendQNI?.isVbdlis == true){
          try {
            this.sendNotiToVBDLIS(this.dossierDetail?.code, requestBodyObj.comment);
          }
          catch (e) {
            console.log((e as Error).message);
          }          
        }         
        
        if(!!this.notifyQNI){
          this.notifyQNIService.confirmSendSubject.next({
            confirm: true,
            renewContent: false,
          });
        } else {
          this.notiService.confirmSendSubject.next({
            confirm: true,
            renewContent: false
          });
        }
        let msgObj = this.drawMsg;
        if (this.env?.enableApprovalOfLeadership == 1){
          msgObj = this.requestdrawMsg;
          const data = {
            type: 11,
            date: tUtils.newDate(),
            attachment: this.uploadedImage
          }
          this.dossierService.updateApprovalData(this.dossierId, data).subscribe(res => {});
        }
        // if (this.willSendEmail) {
        //   this.postEmail(this.listEmail, 1);
        // }
        // if (this.willSendSMS) {
        //   this.postSMS(this.listPhone);
        // }
        if(this.isSyncDBNLGSPTanDanBXD === 1){
          this.syncPostReceiveInforFileFromLocal();
        }
        if(this.isBoTNMTQNM || this.isBoTNMTHCM || this.isBoTNMTGLI){
          this.dossierService.updateDossierBoTNMT(this.dossierId).subscribe(test => {});
        }
        this.dialogRef.close(true);
        const dataLog = {
          dossierId: this.dossierId,
          code: this.dossierCode,
          body: requestBody
        };
        this.logmanService.postUserEventsLog('withdrawDossier', dataLog).subscribe();
      } else {
        this.dialogRef.close(false);
      }
    }, err => {
      this.dialogRef.close(false);
    });
  }

  async putDossierWithdrawWithComment2(){
    const requestBodyObj = {
      dossierStatus: 6,
      comment: '',
      withdrawDate: '',
      user: {
        id: this.userId,
        fullname: this.fullname,
        account: {
          id: this.accountId,
          username: [
            {
              value: this.userName
            }
          ]
        }
      },
      attachment: this.uploadedImage,
      dossierTaskStatus: this.dossierTaskStatus,
      dossierMenuTaskRemind :  this.dossierMenuTaskRemind,
    };
    if (this.commentContent.trim() !== '') {
      this.postComment(this.commentContent.trim());
      requestBodyObj.comment = this.commentContent.trim();
    } else {
      const msgObj = {
        vi: 'Rút hồ sơ <b>' + this.dossierCode + '</b> <br /> Lý do:' + requestBodyObj.comment,
        en: 'Dossier <b>' + this.dossierCode + '</b> has been withdrawal <br /> Reason:' + requestBodyObj.comment
      };
      this.postComment(msgObj[this.selectedLang]);
      requestBodyObj.comment = msgObj[this.selectedLang];
    }
    requestBodyObj.withdrawDate = this.datePipe.transform(this.nowDate, 'yyyy-MM-dd\'T\'HH:mm:ss.SSSZ');
    const msgObjTemp = {
      vi: 'Ngày rút hồ sơ',
      en: 'Withdrawal date'
    };
    this.postHistory(3, msgObjTemp[this.selectedLang], '', this.datePipe.transform(this.nowDate, 'dd/MM/yyyy HH:mm:ss'));
    this.smsEmailContent = requestBodyObj.comment;
    const requestBody = JSON.stringify(requestBodyObj, null, 2);
    this.dossierService.putDossierWithdrawWithComment(this.dossierId, requestBody).subscribe(async data => {
      if (data.affectedRows === 1) {

        // thông báo rút hồ sơ qua VBDLIS
        if(this.dossierDetail?.extendQNI?.isVbdlis == true){
          try {
            this.sendNotiToVBDLIS(this.dossierDetail?.code, requestBodyObj.comment);
          }
          catch (e) {
            console.log((e as Error).message);
          }          
        }  

        this.notiService.confirmSendSubject.next({
          confirm: true,
          renewContent: false
        });
        this.confirmHasWithdraw(requestBodyObj.comment);
        if ( this.isSyncDBNLGSPTanDanBXD === 1){
          this.syncPostReceiveInforFileFromLocal();
        }
        this.dialogRef.close(true);
        const dataLog = {
          dossierId: this.dossierId,
          code: this.dossierCode,
          body: requestBody
        };
        this.logmanService.postUserEventsLog('withdrawDossier', dataLog).subscribe();
      } else {
        this.dialogRef.close(false);
      }
    }, err => {
      this.dialogRef.close(false);
    });
  }

  onDismiss() {
    this.dialogRef.close();
  }

  async postComment(commentContent) {
    const msgObj = {
      vi: 'Lý do rút hồ sơ: ',
      en: 'Reason of withdraw: '
    };
    const content = {
      groupId: 2,
      itemId: this.dossierId,
      user: {
        id: this.userId,
        fullname: this.fullname
      },
      content: msgObj[this.selectedLang] + commentContent.trim(),
      file: this.uploadedImage
    };

    
    const requestBody = JSON.stringify(content, null, 2);
    this.dossierService.postComment(requestBody).subscribe(data => {{}});
    
  }
  async convertToPDF() {
    let filePdf, fileBefore;
    try {
      let filename = this.uploadedImage[0].filename;
      // Kiểm tra có phải file Word không
      if (filename.includes('.docx') || filename.includes('.doc')) {
        filePdf = await this.filemanService.convertDoc2Pdf(this.uploadedImage[0].id);
        if (filePdf && filePdf.id) {
          fileBefore = await this.filemanService.downloadFile(filePdf.id,1).toPromise();
          filename = filename.replace('.docx', '.pdf').replace('.doc', '.pdf');
          fileBefore.name = filename;
          fileBefore.id = filePdf.id;
          fileBefore.uuid = this.uploadedImage[0].uuid;
          let fileupload = [fileBefore];
          const user = await this.keycloakService.loadUserProfile();
          const accountId = user['attributes'].user_id[0];
          this.procedureService.uploadMultiFile(fileupload, accountId).subscribe(
            data => {
              this.uploadedImage = data;
              this.attachFilePreview.push(...this.uploadedImage);
              this.putDossierMultiAttachment('fileAttachDone', 3, 'lang.word.file', '', this.files);
            },
            err => {
              console.error('Lỗi uploadMultiFile:', err);
            }
          );
        } else {
          console.error('convertDoc2Pdf không trả về id hợp lệ:', filePdf);
        }
      }
      else{
        this.attachFilePreview.push(...this.uploadedImage);
        this.putDossierMultiAttachment('fileAttachDone', 3, 'lang.word.file', '', this.files);
      }
    } catch (err) {
      console.error('Lỗi trong convertToPDF:', err);
    }
  }
  async putDossierMultiAttachment(doneClass, type, rbk, oldName, newName) {
    const rs = [];
    this.attachFilePreview.forEach(file => {
      rs.push({
        id: file.id,
        filename: file.filename,
        size: file.size,
        group: this.config.dossierAttachedFileTagId,
        uuid: file.uuid
      });
    });
    this.resultFilePreview.forEach(file => {
      rs.push({
        id: file.id,
        filename: file.filename,
        size: file.size,
        group: this.config.dossierResultFileTagId,
        uuid: file.uuid
      });
    });
    this.apologyTextFilePreview.forEach(file => {
      rs.push({
        id: file.id,
        filename: file.filename,
        size: file.size,
        group: this.config.dossierApologyTextFileTagId
      });
    });
    const putBody = {
      attachment: rs
    };
    const requestBody = JSON.stringify(putBody, null, 2);
    this.dossierService.putDossierOnline(this.dossierId, requestBody).subscribe(data => {
      if (data.affectedRows === 1) {
        
      } else {
        const msgObj = {
          vi: 'Cập nhật thất bại!',
          en: 'Update failed!'
        };
        this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
       
      }
    });
  }
  getDossierAttachment() {
    this.dossierService.getDossierDetail(this.dossierId).subscribe(async data => {
      let count = 0;
      if (data.attachment) {
        this.attachFilePreview = [];
        this.resultFilePreview = [];
        const asyncFunction = new Promise<void>((resolve, reject) => {
          data.attachment.forEach(async att => {
            if (att.group === this.config.dossierAttachedFileTagId) {
              this.attachFilePreview.push({
                id: att.id,
                name: att.filename,
                size: att.size,
                uuid: att.uuid,
                icon: this.getFileIcon(att.filename.split('.').pop())
              });
            }
            if (att.group === this.config.dossierResultFileTagId) {
              this.resultFilePreview.push({
                id: att.id,
                name: att.filename,
                size: att.size,
                uuid: att.uuid,
                icon: this.getFileIcon(att.filename.split('.').pop()),
                lastUserAction: att.id,
              });
            }

            count++;
            if (count === data.attachment.length) { resolve(); }
          });
        });
      } else {
        this.attachFilePreview = [];
      }
    });
    localStorage.removeItem('checkVerifiled');
  }
  
  postHistory(type, col, oldVal, newVal) {
    const content = {
      groupId: 1,
      itemId: this.dossierId,
      user: {
        id: this.userId,
        name: this.fullname
      },
      type,
      deploymentId: this.config.deploymentId,
      action: [
        {
          fieldNameRbk: col,
          originalValue: oldVal,
          newValue: newVal
        }
      ]
    };
    const requestBody = JSON.stringify(content, null, 2);
    this.dossierService.postHistory(requestBody).subscribe(data => {
    });
  }

  onCommentEditorChange(event) {
    this.commentContent = event.editor.getData();
    if (this.ckeditorMaxLength > 0) {
      if (event.editor.getData().trim().length > this.ckeditorMaxLength) {
        this.isCKMaxlenght = true;
      } else {
        this.isCKMaxlenght = false;
      }
    } else if (event.editor.getData().trim().length > 500) {
      this.isCKMaxlenght = true;
    } else {
      this.isCKMaxlenght = false;
    }
  }
  onCheckboxSendChange(type) {
    switch (type) {
      case 1:
        this.willSendSMS = !this.willSendSMS;
        if (this.willSendSMS === true) {
          this.smsEmailMaxLength = this.config.dossierEmailSMSZaloConfig.characterLimit;
        } else {
          this.smsEmailMaxLength = null;
        }
        break;
      case 2:
        this.willSendEmail = !this.willSendEmail;
        break;
    }
    if (this.willSendSMS || this.willSendEmail) {
      this.willSend = true;
    } else {
      this.willSend = false;
    }
  }
  postEmail(listEmail, type) {
    if (listEmail.length > 0) {
      listEmail.forEach(mail => {
        const emailConfig = this.config.dossierEmailSMSZaloConfig;
        let subject = emailConfig.increaseDue[this.selectedLang];
        subject = subject.replace('{{code}}', this.dossierDetail.code);
        const contentTemp = emailConfig.reasonExtend[this.selectedLang] + '' + this.smsEmailContent.replace(/<(?:.|\n)*?>/gm, '');
        const userAgency = JSON.parse(localStorage.getItem('userAgency'));
        let rootAgencyId: any = '';
        if (userAgency !== null) {
          rootAgencyId = userAgency.id;
        } else {
          rootAgencyId = this.config.rootAgency.id;
        }
        this.dossierService.postEmailByAgency(
          rootAgencyId,
          this.config.subsystemId,
          contentTemp,
          [mail.email],
          subject
        ).subscribe(emailRS => {
          console.log(emailRS);
        }, err => {
          console.log(err);
        });
      });
    }
  }

  postSMS(listPhoneNumber) {
    const emailConfig = this.config.dossierEmailSMSZaloConfig;
    let subject = emailConfig.increaseDue[this.selectedLang];
    subject = subject.replace('{{code}}', this.dossierDetail.code);
    subject += '. ' + emailConfig.reasonExtend[this.selectedLang] + '' + this.smsEmailContent.replace(/<(?:.|\n)*?>/gm, '');
    console.log(subject)
    if (listPhoneNumber.length > 0) {
      listPhoneNumber.forEach(phone => {
        const userAgency = JSON.parse(localStorage.getItem('userAgency'));
        let rootAgencyId: any = '';
        if (userAgency !== null) {
          rootAgencyId = userAgency.id;
        } else {
          rootAgencyId = this.config.rootAgency.id;
        }
        this.dossierService.postSMSByAgency(
          rootAgencyId,
          this.config.subsystemId,
          subject,
          [phone.number]
        ).subscribe(smsRS => {
          console.log(smsRS);
        }, err => {
          console.log(err);
        });
      });
    }
  }

  // ========= file
  uploadMultiFile(file, accountId) {
    this.procedureService.uploadMultiFile(file, accountId).subscribe(data => {
      this.uploadedImage = data;
      this.putDossierWithdrawWithComment();
    }, err => {
      console.log(err);
    });
  }

  onSelectFile(event) {
    if (event.target.files && event.target.files[0]) {
      for (const i of event.target.files) {
        if (i.size >= this.maxFileSize * 1024 * 1024) {
          const msgObj = {
            vi: 'Kích thước tệp tin quá lớn, tên tệp: ' + i.name,
            en: 'The file is too large, file name: ' + i.name
          };
          this.snackbarService.openSnackBar(0, '', msgObj[this.selectedLang], 'error_notification', this.config.expiredTime);
          return;
        }
        if (this.acceptFileType.filter(t => t === i.type.toLowerCase()).length > 0) {
          this.files.push(i);
          const extension = i.name.substring(i.name.lastIndexOf('.')).split('.').pop();
          this.urls.push(this.getFileIcon(extension));

          const reader = new FileReader();
          reader.onload = () => {
            this.uploaded = true;
          };
          if (i.name.length > 20) {
            const startText = i.name.substr(0, 5);
            const shortText = i
              .name
              .substring(i.name.length - 7, i.name.length);
            this.fileNames.push(startText + '...' + shortText);
            this.fileNamesFull.push(i.name);
          } else {
            this.fileNames.push(i.name);
            this.fileNamesFull.push(i.name);
          }
          reader.readAsDataURL(i);
        }
        else {
          const msgObj = {
            vi: 'Không hỗ trợ loại tệp tin ',
            en: 'File type is not supported '
          };
          // tslint:disable-next-line: max-line-length
          this.snackbarService.openSnackBar(0, '', msgObj[this.selectedLang] + i.type.substring(i.type.lastIndexOf('/') + 1).toUpperCase(), 'error_notification', this.config.expiredTime);
        }
      }
    }
  }

  removeItem(index: number) {
    this.urls.splice(index, 1);
    this.fileNames.splice(index, 1);
    this.fileNamesFull.splice(index, 1);
    this.files.splice(index, 1);
    this.blankVal = '';
  }

  getFileIcon(ext) {
    return this.config.cloudStaticURL + 'icon/files/300x300/' + ext + '.png';
  }
   // DBN - BXD - dong bo thong hs qua LGSP Tan Dan
   syncPostReceiveInforFileFromLocal (){
    this.dossierService.getDossierDetail(this.dossierId).subscribe(data => {
      let dataApplicant =  data?.applicant?.data;
      let nhanThongTinHSTuDP = [];
      let thongTinNguoiNop = {
        HoTenNguoiNop : dataApplicant?.fullname, //*
        SoCMND : dataApplicant?.identityNumber, //*
        EmailNguoiNop: dataApplicant?.email,
        SoDienThoaiNguoiNop: dataApplicant?.phoneNumber, //*
        DiaChiThuongTruNguoiNop: dataApplicant?.address + ","
                               + dataApplicant?.village?.label + ","
                               + dataApplicant?.district?.label + ","
                               + dataApplicant?.province?.label + ","
                               + dataApplicant?.nation?.label, //*
        MaDoiTuong: ""
      };
      let giayToTrongQuaTrinhXuLy = {
        MaGiayTo: "",
        TenGiayTo: "",
        NoiDungBase64: "",
        DinhDangTepTin: "", // dinh dang .pdf, .doc, .png,...
        MoTa: "",
        LoaiGiayTo: ""
      }
      let hinhThucTraKetQua = 0;
      switch(data?.dossierReceivingKind?.id) {
        case '5f8968888fffa53e4c073ded':
        hinhThucTraKetQua = 0;
        break;
        case '604ecde877bed4110c1dd0d1':
          hinhThucTraKetQua = 1;
          break;
        case '6163f1e20f363b04cf60b224':
          hinhThucTraKetQua = 1;
          break;
        case '5f8969018fffa53e4c073dee':
          hinhThucTraKetQua = 1;
          break;
      }
      let arrThongTinTienTrinh = [];
      let dossierStatus = this.statusName;
      data?.task.forEach(task => {
          if(task?.isCurrent === 1){
            let thongTinTienTrinh = {
              NguoiXuLy: task?.assignee?.fullname, //*
              ChucDanh: "",
              ThoiDiemXuLy: this.datePipe.transform(task?.createdDate, 'yyyyMMddHHmmss'), //* yyyyMMddHHmmss
              PhongBanXuLy: task?.agency?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name,
              NoiDungXuLy:  task?.bpmProcessDefinitionTask?.remind ?  task?.bpmProcessDefinitionTask?.remind?.name : "",
              NgayBatDau:   this.datePipe.transform(task?.createdDate, 'yyyyMMddHHmmss'), //yyyyMMddHHmmss
              NgayKetThucTheoQuyDinh: task?.isCurrent === 0? this.datePipe.transform(task?.updatedDate, 'yyyyMMddHHmmss') : ""
            }
            arrThongTinTienTrinh.push(thongTinTienTrinh);
          }

      });
      let dossierSync = {
        MaHoSo: data.code, //*
        TrangThaiHoSo: dossierStatus, // phu luc 2 *
        NgayTiepNhan: this.datePipe.transform(data?.acceptedDate, 'yyyyMMddHHmmss') ,// yyyyMMddHHmmss*
        NgayHenTraKetQua: this.datePipe.transform(data?.appointmentDate, 'yyyyMMddHHmmss'), //yyyyMMddHHmmss
        ThongTinNguoiNop : thongTinNguoiNop,
        GiayToTrongQuaTrinhXuLy : [],
        HinhThucTraKetQua: hinhThucTraKetQua, // 0: tra truc tiep 1: tra qua duong buu dien 2: tra kq truc tuyen
        ThongTinTienTrinh : arrThongTinTienTrinh,
        MaTTHC: data?.procedure?.code,
        TenTTHC: data?.procedure?.translate?.find(procedure => procedure.languageId === Number(this.selectedLangId))?.name ? data?.procedure?.translate?.find(procedure => procedure.languageId === Number(this.selectedLangId))?.name : '',
      }
      //nhanThongTinHSTuDP.push(dossierSync);
      let params = "agencyId=" + data?.agency.id + "&subsystemId=" + this.config?.subsystemId ; // &configId
      this.dossierService.postReceiveInforFileFromLocal(params, dossierSync).subscribe(async data => {
        console.log("===postReceiveInforFileFromLocal===");
        console.log(data);
      }, er => {
        console.log(er);
      })
    }, err => {
      console.log(err);
    });
  }

  confirmHasWithdraw(commentOld){
    const requestBodyObj = {
      dossierStatus: 6,
      comment: commentOld,
      dossierTaskStatus: this.dossierTaskStatusWithdraw,
      dossierMenuTaskRemind: this.dossierMenuTaskRemindWithdraw
    };
    const msgObj = {
      vi: 'Hồ sơ <b>' + this?.dossierDetail?.id + '</b> đã rút',
      en: 'Dossier <b>' + this?.dossierDetail?.id + '</b> has been withdraw!'
    };
    const content = {
      groupId: 2,
      itemId: this?.dossierDetail?.id,
      user: {
        id: this.accountId,
        fullname: this.fullname
      },
      content: msgObj[this.selectedLang].trim()
    };
    this.postCommentDirect(content, this.dossierDetail?.id);
    const requestBody = JSON.stringify(requestBodyObj, null, 2);
    this.dossierService.putDossierStatusWithComment(this.dossierDetail.id, requestBody).subscribe(data => {
      let msgObj = this.drawMsg;
      if (this.env?.enableApprovalOfLeadership == 1 ){
        msgObj = this.requestdrawMsg;
        const data = {
          type: 11,
          date: tUtils.newDate(),
          attachment: this.uploadedImage
        }
        this.dossierService.updateApprovalData(this.dossierId, data).subscribe(res => {});
      }
    },
        err => {
    });
  }
  postOnlyApprovalAgencyOfDossier(code) {
    const body = {
      id: this.getApprovalAgency
    };
    const requestBody = JSON.stringify(body, null, 2);
    this.dossierService.postOnlyApprovalAgencyOfDossier(requestBody, code).toPromise();
  }
  postCommentDirect(commentContent, dossierId) {
    const content = {
      groupId: 2,
      itemId: dossierId,
      user: {
        id: this.accountId,
        fullname: this.fullname
      },
      content: commentContent.content.trim()
    };
    const requestBody = JSON.stringify(content, null, 2);
    this.dossierService.postComment(requestBody).subscribe(data => { });
  }

}
export class WithdrawQBHModel {
  constructor(
    public dossierId: string,
    public dossierCode: string,
    public isAllAgencySearch?: boolean,
    public isDirectory?: number) {
  }
}
