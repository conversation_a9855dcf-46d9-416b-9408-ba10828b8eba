import {<PERSON><PERSON>iew<PERSON>ni<PERSON>, Component, ElementRef, OnDestroy, OnInit, ViewChild} from '@angular/core';
import { FormGroup, FormControl } from '@angular/forms';
import { DossierSearchElement } from 'src/app/data/schema/dossier-search-element';
import { MatTableDataSource } from '@angular/material/table';
import { DossierService } from 'src/app/data/service/dossier/dossier.service';
import { ActivatedRoute, Router } from '@angular/router';
import { EnvService } from 'src/app/core/service/env.service';
import { MatAccordion } from '@angular/material/expansion';
import { DatePipe } from '@angular/common';
import { MainService } from 'src/app/data/service/main/main.service';
import { MatDialog } from '@angular/material/dialog';
import { SuspendComponent, SuspendModel } from 'src/app/modules/dossier/dialogs/suspend/suspend.component';
import { RefuseComponent, RefuseDialogModel } from 'src/app/modules/dossier/dialogs/refuse/refuse.component';
import { ResumeProcessingComponent, ResumeProcessingModel } from 'src/app/modules/dossier/dialogs/resume-processing/resume-processing.component';
import { SnackbarService } from 'src/app/data/service/snackbar/snackbar.service';
import { ProcedureService } from 'src/app/data/service/procedure/procedure.service';
import { AdminLayoutNavComponent } from 'src/app/layouts/admin/admin-layout-nav/admin-layout-nav.component';
import { ProcessHandleComponent, ProcessHandleDialogModel } from 'src/app/shared/components/process-handle/process-handle.component';
import { AdditionalRequirementComponent, ConfirmAdditionalRequirementDialogModel } from 'src/app/modules/dossier/dialogs/additional-requirement/additional-requirement.component';
import { DeleteDossierComponent, ConfirmDeleteDialogModel } from 'src/app/modules/dossier/pages/processing/dialogs/delete-dossier/delete-dossier.component';
import { WithdrawComponent, WithdrawModel } from 'src/app/modules/dossier/dialogs/withdraw/withdraw.component';
import { DeploymentService } from 'src/app/data/service/deployment.service';
import * as tUtils from 'src/app/data/service/thoai.service';
import * as configSvc from 'src/app/data/service/config.service';
import { UserService } from 'src/app/data/service/user.service';
import { ConfirmationDialogModel, ConfirmDialogComponent } from 'src/app/shared/components/dialogs/confirm-dialog/confirm-dialog.component';
import {ReplaySubject, Subject} from 'rxjs';
import {takeUntil} from 'rxjs/operators';
import {MatSelect} from '@angular/material/select';
import { NotificationDialogComponent, NotificationDialogModel } from 'src/app/shared/components/dialogs/notification-dialog/notification-dialog.component';
import { CommonService } from 'src/app/data/service/common.service';
import { ApplyChangeFeesComponent, ApplyChangeFeesDialogModel } from './dialogs/apply-change-fees/apply-change-fees.component';
import { LogmanService } from 'src/app/data/service/logman/logman.service';

@Component({
  selector: 'app-perform-charge-fees',
  templateUrl: './perform-charge-fees.component.html',
  styleUrls: ['./perform-charge-fees.component.scss', '/src/app/app.component.scss'],
})
export class PerformChargeFeesComponent implements OnInit {
  depConfig = this.deploymentService.getAppDeployment();
  userAgency = JSON.parse(localStorage.getItem('userAgency'));

  config = this.envService.getConfig();
  env = this.deploymentService.getAppDeployment()?.env;
  selectedLang = localStorage.getItem('language') || 'vi';
  selectedLangId = Number(localStorage.getItem('languageId')) || 228;

  fakeCompletedDate = this.deploymentService.env.fakeCompletedDate;
  taskCompletedStatusIds = this.deploymentService.env.taskCompletedStatusIds;

  enableForceEndProcess = this.deploymentService.env.enableForceEndProcess;
  enableReassignDossier = this.deploymentService.env.enableReassignDossier;

  hasParticipatedInProcessing = this.deploymentService.env.hasParticipatedInProcessing;

  paginationType = this.deploymentService.env.paginationType;

  finaceObligatingStatus = '60f6364e09cbf91d41f88859';

  statusNeedsCalculatorTiming = configSvc.STATUS_NEEDS_CALCULATOR_TIMING.map(item => item.id);
  isBoldInfor = this.env?.isBoldInfor? this.env?.isBoldInfor: false;

  @ViewChild(MatAccordion) accordion: MatAccordion;
  searchForm = new FormGroup({
    code: new FormControl(''),
    identityNumber: new FormControl(''),
    applicantName: new FormControl(''),
    ownerFullname: new FormControl(''),
    advSector: new FormControl(''),
    advProcedure: new FormControl(''),
    advNation: new FormControl(''),
    advProvince: new FormControl(''),
    advDistrict: new FormControl(''),
    advWard: new FormControl(''),
    advAddress: new FormControl(''),
    advTaskStatusId: new FormControl(''),
    advAcceptFrom: new FormControl(''),
    advAcceptTo: new FormControl(''),
    advAppointmentFrom: new FormControl(''),
    advAppointmentTo: new FormControl(''),
    advApplyMethod: new FormControl(''),
    advProcessStatus: new FormControl(''),
    avdResultReturnedFrom: new FormControl(''),
    avdResultReturnedTo: new FormControl(''),
    advAgency: new FormControl(''),
    chargeFees: new FormControl(''),
    typechargeFees: new FormControl(''),
  });
  pageTitle = {
    vi: `Tra cứu hồ sơ toàn cơ quan`,
    en: `Dossier lookup all agency`
  };

  syncForm = new FormGroup({
    code: new FormControl('')
  });

  listNation = [];
  listProvince = [];
  listDistrict = [];
  listWard = [];
  listDossierTaskName = [];
  listDossierTaskNamePage = 0;
  isFullListDossierTaskName = false;

  listAgency = [];
  listAgencyPage = 0;
  isFullListAgency = false;

  listSector = [];
  listSectorPage = 0;
  isFullListSector = false;
  searchSectorKeyword = '';
  keySearchSectorAgency = '';
  keywordSector = '';
  totalPagesSector = 0;
  currentPageSector = 0;
  pageSizeSector = 10;
  searchSectorCtrl: FormControl = new FormControl();
  protected onDestroy = new Subject<void>();
  protected sectors: any[] = this.listSector;
  public sectorFiltered: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  parentAgency = '';


  showStatusVnpost = this.env?.OS_HCM?.dossier?.showStatusVnpost ? this.env?.OS_HCM?.dossier?.showStatusVnpost : 0;

  // Procedure infinity scroll with search
  private listProcedure: any[] = [];
  listProcedurePage = 0;
  isFullListProcedure = false;
  searchProcedureKeyword = '';
  procedureCtrl: FormControl = new FormControl();
  searchProcedureCtrl: FormControl = new FormControl();
  protected procedures: any[] = this.listProcedure;
  public procedureFiltered: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  @ViewChild('procedureMatSelectInfiniteScroll', { static: true }) procedureMatSelectInfiniteScroll: MatSelect;

  isOwnerFullname = this.env?.isOwnerFullname === 1;

  displayedColumnsDefault: string[] = ['code','applicantName', 'procedureName', 'statusSync', 'action'];
  // tslint:disable-next-line:max-line-length
  // displayedColumns: string[] = this.env?.ShowFieldHandlingAcency  === 1 || this.isOwnerFullname ? this.env?.arrShowTable  : this.displayedColumnsDefault;
  ELEMENTDATA: DossierSearchElement[] = [];
  dataSource: MatTableDataSource<DossierSearchElement>;
  countResult = 0;
  size = this.env?.OS_KTM?.defaultPageSize ? this.env?.OS_KTM?.defaultPageSize : 10;
  page = 1;
  pageIndex = 1;

  @ViewChild('excelUploadInput')
  myInputVariable: ElementRef;


  pgSizeOptions = this.config.pageSizeOptions;
  xpandStatus = false;
  listTimesheet = [];
  isCheckedAll = false;
  searchDomain = '';
  receptionForm: any = [
    {
      id: 0,
      name: [
        {
          languageId: 228,
          name: 'Trực tuyến',
          content: ' tiếp nhận trực tuyến'
        },
        {
          languageId: 46,
          name: 'Online',
          content: ' online reception'
        }
      ]
    },
    {
      id: 1,
      name: [
        {
          languageId: 228,
          name: 'Trực tiếp',
          content: ' tiếp nhận trực tiếp'
        },
        {
          languageId: 46,
          name: 'Direct',
          content: ' direct reception'
        }
      ]
    },
    {
      id: 2,
      name: [
        {
          languageId: 228,
          name: 'Cổng dịch vụ công quốc gia',
          content: ' tiếp nhận từ cổng Dịch vụ công quốc gia'
        },
        {
          languageId: 46,
          name: 'National public service portal',
          content: ' received from the National Public Service portal'
        }
      ]
    }
  ];
  isDienBien = this.env?.isDienBien ? this.env?.isDienBien : false;
  checkProvineAdmin ? = JSON.parse(localStorage.getItem('superAdmin'));
  arrReceptionForm: any = [];
  justRegistered: any;
  checkAll = false;
  selectedDossiers = [];
  numberOfElements = 0;
  expandReminderMenu = true;
  remindId = '';
  listMenuRemind: any = [];
  agencyId: string;
  agencyIdForProcedure: string;
  maxDeleteDossierMulti = 10;
  lengthRemind = 0;
  isAdmin = false;
  isOneGateOfficer = false;
  hasDossierDeletePermission = false;
  userId = localStorage.getItem('UID');
  oneGateDeleteDossier = sessionStorage.getItem('oneGateDeleteDossier');
  isLGSPHCM = this.env?.OS_HCM?.showDossierLGSPHCMSync == true ? this.env?.OS_HCM?.showDossierLGSPHCMSync : false;
  enableRemoveVnpostFeeToAnotherTable = this.deploymentService.env?.OS_HCM?.enableRemoveVnpostFeeToAnotherTable ? this.deploymentService.env?.OS_HCM?.enableRemoveVnpostFeeToAnotherTable : 0;
  syncPaymentStatus = this.deploymentService.env.syncPaymentStatus;
  paymentRequestId = this.deploymentService.env.paymentRequest.id;
  paidDossierId = this.deploymentService.env.paidDossier.id;
  paidDossierName = this.deploymentService.env.paidDossier.name;
  isScanCode = true;

  keywordAgencyapprovalAgency = '';
  totalPagesAgencyapprovalAgency = 0;
  currentPageAgencyapprovalAgency = 0;
  pageSizeAgencyapprovalAgency = 10;
  timeOutAgencyapprovalAgency: any = null;
  listAgencyapprovalAgency: Array<any> = [];

  dossierFeeByDossier = new Map<String, any[]>();
  public scannerEnabled: boolean = true;
  constructor(
    private router: Router,
    private dossierService: DossierService,
    private userService: UserService,
    private activeRoute: ActivatedRoute,
    private envService: EnvService,
    private deploymentService: DeploymentService,
    private datePipe: DatePipe,
    private mainService: MainService,
    private dialog: MatDialog,
    private snackbarService: SnackbarService,
    private procedureService: ProcedureService,
    private adminLayoutNavComponent: AdminLayoutNavComponent,
    private commonService: CommonService,
    private logmanService: LogmanService,
  ) {
    this.dataSource = new MatTableDataSource(this.ELEMENTDATA);
    this.getConfig();
  }
  async ngOnInit(): Promise<void> {
    // tslint:disable-next-line: max-line-length
    this.size = Number(this.activeRoute.snapshot.queryParamMap.get('size')) === 0 ? this.size : Number(this.activeRoute.snapshot.queryParamMap.get('size'));
    // tslint:disable-next-line: max-line-length
    this.page = Number(this.activeRoute.snapshot.queryParamMap.get('page')) === 0 ? this.page : Number(this.activeRoute.snapshot.queryParamMap.get('page'));
    this.mainService.setPageTitle(this.pageTitle[localStorage.getItem('language')]);
    const permissions = this.userService.getUserPermissions();
    for (const p of permissions) {
      if (p.permission.code === 'admin' || p.permission.code === 'oneGateAdminMaster') {
        this.isAdmin = true;
      }
      if ( ['admin', 'oneGateAdminMaster', 'oneGateDossierLookup'].includes(p.permission.code)){
         this.isOneGateOfficer = true;
      }
      if (p.permission.code === 'admin' || p.permission.code === 'oneGateAdminMaster' || p.permission.code === 'oneGateDossierDelete') {
        this.hasDossierDeletePermission = true;
      }
    }
    console.log(this.isAdmin);
    if (this.userAgency !== null && this.userAgency !== undefined) {
      // this.agencyId = this.isAdmin ? '' : this.userAgency.id;
      if (!!this.userAgency.parent && !!this.userAgency.parent.id && !this.isAdmin) {
        this.agencyIdForProcedure = this.userAgency.parent.id;
      } else if (this.userAgency.id !== this.env?.rootAgency?.id || this.isAdmin) {
        this.agencyIdForProcedure = this.userAgency.id;
      }
      this.keySearchSectorAgency = '&agency-id=' +  this.agencyIdForProcedure;
    }
    // tslint:disable-next-line:prefer-for-of
    for (let i = 0; i < this.receptionForm.length; i++) {
      const arrName: any = {};
      // tslint:disable-next-line:prefer-for-of
      for (let j = 0; j < this.receptionForm[i].name.length; j++) {
        if (Number(localStorage.getItem('languageId')) === this.receptionForm[i].name[j].languageId) {
          arrName.id = this.receptionForm[i].id;
          arrName.name = this.receptionForm[i].name[j].name;
          arrName.content = this.receptionForm[i].name[j].content;
        }
      }
      this.arrReceptionForm.push(arrName);
    }
    // this.getListAgency();
    this.getListAgencyapprovalAgency(this.keywordAgencyapprovalAgency, this.currentPageAgencyapprovalAgency, this.pageSizeAgencyapprovalAgency);
    this.getListNation();
    this.getListDossierTaskName();
    this.getListSectorScroll();
    this.getListProcedure('');
    this.getDossierTaskStatus();
    this.getMaxDeleteDossierMulti();
    console.log('checkProvineAdmin        ', this.checkProvineAdmin);
    // await this.getRemindMenuTask();
    // Khong load danh sach ban dau - IGATESUPP-6445
    this.onConfirmSearch();
    if (this.userService.checkPermissionExists('oneGateDossierDelete')) {
      this.hasDossierDeletePermission = true;
    }


  }
  setInfo(barCode: string){
    console.log(barCode);
    if(!barCode){
      // console.log(barCode);
      return;
    }

    this.searchForm.patchValue({
      code: barCode
    })
  }
  applyChangeFeesDialog(row, type) {
    const dialogData = new ApplyChangeFeesDialogModel(row, type);
    const dialogRef = this.dialog.open(ApplyChangeFeesComponent, {
      width: '900px',
      height: '80%',
      data: dialogData,
      disableClose: true,
      autoFocus: false
      // position: {
      //   top: '10em'
      // }
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
      if(dialogResult.status == true){
        const msgObj = {
          vi: 'Thao tác thành công',
          en: 'Thao tác thành công'
        };
        this.snackbarService.openSnackBar(1, msgObj[this.selectedLang], '', 'success_notification', this.config.expiredTime);
        this.onConfirmSearch();
      }
      else{
        const msgObj = {
          vi: 'Thao tác thất bại',
          en: 'Thao tác thất bại'
        };
        this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
      }
    });
  }

  getNextBatch(type) {
    switch (type) {
      case 'agencyapprovalAgency': {
        this.currentPageAgencyapprovalAgency += 1;
        // tslint:disable-next-line:max-line-length
        this.getListAgencyapprovalAgency(this.keywordAgencyapprovalAgency, this.currentPageAgencyapprovalAgency, this.pageSizeAgencyapprovalAgency);
        break;
      }
    }
  }

  getListAgencyapprovalAgency(keyword, page, size) {
    const userAgency = JSON.parse(localStorage.getItem('userAgency'));
    let rootAgencyId: any = '';
    if (userAgency !== null) {
      rootAgencyId = userAgency.id;
    } else {
      rootAgencyId = this.config.rootAgency.id;
    }
    let agencyid = this.parentAgency;
    if (this.parentAgency === '') {
      agencyid = this.config.rootAgency.id;
    }
    const searchString = 'name+code/--full?keyword=' + keyword + '&tag-id=0000591c4e1bd312a6f00003&ancestor-id=' + this.env.rootAgency.id + '&page=' + page + '&size=' + size + '&sort=name.name,asc&status=1';

    this.procedureService.getListAgencyWithParent(searchString).subscribe(res => {
      for(let i = 0; i < res.content.length; i++){
        res.content[i].fullName = "";
        if(!!res.content[i].name && res.content[i].name.length > 0){
          for (let n = 0; n < res.content[i].name.length; n++)
            {
              if (res.content[i].name[n].languageId === Number(localStorage.getItem('languageId')))
              {
                res.content[i].fullName = res.content[i].name[n].name;
              }
            }
        }
      }
      if (page === 0) {
        this.listAgencyapprovalAgency = res.content;
      } else {
        this.listAgencyapprovalAgency = this.listAgencyapprovalAgency.concat(res.content);
      }
      this.totalPagesAgencyapprovalAgency = res.totalPages;
    }, err => {
      console.log(err);
    });
  }

  async resetaddForm(type) {
    switch (type) {
      case 'agencyapprovalAgency': {
        this.currentPageAgencyapprovalAgency = 0;
        this.keywordAgencyapprovalAgency = '';
        // tslint:disable-next-line:max-line-length
        this.getListAgencyapprovalAgency(this.keywordAgencyapprovalAgency, this.currentPageAgencyapprovalAgency, this.pageSizeAgencyapprovalAgency);
      }
    }
  }

  onEnter(type, event) {
    switch (type) {
      case 'agencyapprovalAgency': {
        clearTimeout(this.timeOutAgencyapprovalAgency);
        this.timeOutAgencyapprovalAgency = setTimeout(async () => {
          this.keywordAgencyapprovalAgency = event.target.value;
          this.currentPageAgencyapprovalAgency = 0;
          // tslint:disable-next-line:max-line-length
          this.getListAgencyapprovalAgency(this.keywordAgencyapprovalAgency, this.currentPageAgencyapprovalAgency, this.pageSizeAgencyapprovalAgency);

          this.searchForm.patchValue({
            advAgency: '',
          });

        }, 300);
        break;
      }
    }
  }

  onSelectFile(event){
    if (event.target.files.length > 0){
      const file = event.target.files[0];
      this.dossierService.importDataSync(file, this.isLGSPHCM).subscribe(res => {
        let successMessage = '';
        if (Number(localStorage.getItem('languageId')) === 228)
        {
          successMessage = 'Import thành công ' + res.successRows + ' dòng';
        }else{
          successMessage = 'Successfully imported ' + res.successRows + ' lines';
        }
        const firstContent  = (res.successRows > 0 || res.errorMessages.length === 0) ? successMessage : null;
        let secondContent  = '';
        const code = res.errorMessages.length === 0 ? 'success' : (res.successMessage <= 0 ? 'error' : 'warning');
        for (const msg of res.errorMessages) {
          secondContent += msg + '\n';
        }
        const dialogData = new NotificationDialogModel(firstContent, secondContent, code);
        const dialogRef = this.dialog.open(NotificationDialogComponent, {
          width: '500px',
          data: dialogData,
          disableClose: true,
          autoFocus: false
        });
        dialogRef.afterClosed().subscribe(rs => {
        });

        const searchString = '?page=0&size=10&spec=page&keyword=&sort=createdDate,desc';
        // this.getListBank(searchString);
      }, err => {
        let errMessage = '';
        if (err.error.code){
          errMessage = ' ' + err.error.message;
        }
        const msgObj = {
          vi: 'Nhập dữ liệu thất bại.',
          en: 'Failed to import data.'
        };
        this.snackbarService.openSnackBar(0, msgObj[this.selectedLang] + errMessage, '', 'error_notification', this.config.expiredTime);
      });
      this.myInputVariable.nativeElement.value = '';
    }
  }

  syncByCode(codeDossier?){
    const objectform = this.syncForm.getRawValue();
    if (!!codeDossier){
      objectform.code = codeDossier;
    }
    if (!!objectform.code &&  objectform.code.trim() !== ''){
        const listCode = [objectform.code];
        this.dossierService.syncDossierByCode(listCode, this.isLGSPHCM).subscribe(res => {
          let successMessage = '';
          if (Number(localStorage.getItem('languageId')) === 228)
          {
            successMessage = 'Đã gửi yêu cầu đồng bộ hồ sơ ' + objectform.code;
          }else{
            successMessage = 'A request to sync' + objectform.code + 'records has been sent';
          }

          const secondContent  = '';
          let code = 'success';
          if (res.affectedRows < 1){
            if (Number(localStorage.getItem('languageId')) === 228)
            {
              successMessage = 'Không gửi được yêu cầu đồng bộ hồ sơ ' + objectform.code;
            }else{
              successMessage = 'Unable to send request to sync ' + objectform.code + ' records';
            }
            code = 'warning';
          }
          const firstContent  = successMessage;
          const dialogData = new NotificationDialogModel(firstContent, secondContent, code);
          const dialogRef = this.dialog.open(NotificationDialogComponent, {
            width: '500px',
            data: dialogData,
            disableClose: true,
            autoFocus: false
          });
          dialogRef.afterClosed().subscribe(rs => {
          });
          this.onConfirmSearch();
        }, err => {
          let errMessage = '';
          if (err.error.code){
            errMessage = ' ' + err.error.message;
          }
          const msgObj = {
            vi: 'Nhập dữ liệu thất bại.',
            en: 'Failed to import data.'
          };
          this.snackbarService.openSnackBar(0, msgObj[this.selectedLang] + errMessage, '', 'error_notification', this.config.expiredTime);
        });
    }else{
      if (this.selectedDossiers.length > 0) {
        this.dossierService.syncDossierByCode(this.selectedDossiers, this.isLGSPHCM).subscribe(res => {
          let successMessage = '';
          if (Number(localStorage.getItem('languageId')) === 228) {
            successMessage = 'Đã gửi yêu cầu đồng bộ ' + res.affectedRows + ' hồ sơ';
          } else {
            successMessage = 'A request to sync ' + res.affectedRows + ' records has been sent';
          }

          const secondContent = '';
          let code = 'success';
          if (res.affectedRows < 1) {
            if (Number(localStorage.getItem('languageId')) === 228) {
              successMessage = 'Không gửi được yêu cầu đồng bộ ';
            } else {
              successMessage = 'Unable to send request to sync ';
            }
            code = 'warning';
          }
          const firstContent = successMessage;
          const dialogData = new NotificationDialogModel(firstContent, secondContent, code);
          const dialogRef = this.dialog.open(NotificationDialogComponent, {
            width: '500px',
            data: dialogData,
            disableClose: true,
            autoFocus: false
          });
          dialogRef.afterClosed().subscribe(rs => {
            this.selectedDossiers = [];
          });
          this.onConfirmSearch();
        }, err => {
          let errMessage = '';
          if (err.error.code) {
            errMessage = ' ' + err.error.message;
          }
          const msgObj = {
            vi: 'Nhập dữ liệu thất bại.',
            en: 'Failed to import data.'
          };
          this.snackbarService.openSnackBar(0, msgObj[this.selectedLang] + errMessage, '', 'error_notification', this.config.expiredTime);
        });
      }
    }
  }

  getConfig() {
    const config = this.deploymentService.getAppDeployment();
    if (!!config) {
      if (config.domain && config.domain.length > 0) {
        // tslint:disable-next-line:max-line-length
        const domain = config.domain.filter(listdomain => ((listdomain.domain.toLowerCase().indexOf(window.location.host) > -1) || (window.location.host.toLowerCase().indexOf(listdomain.domain) > -1)));
        if (domain && domain.length > 0 && domain[0].rootAgency) {
          this.searchDomain = '&domain-agency-id=' + domain[0].rootAgency.id;
        }
      }
    }
  }

  getListAgency() {
    // const userAgency = JSON.parse(localStorage.getItem('userAgency'));
    // let rootAgencyId: any = '';
    // if (userAgency !== null) {
    //   rootAgencyId = userAgency.id;
    // } else {
    //   rootAgencyId = this.config.rootAgency.id;
    // }
    if (this.isFullListAgency) { return; }
    this.procedureService.getListAgencyWithParent('?parent-id=' + this.config.rootAgency.id + '&page=' + this.listAgencyPage + '&size=50&sort=name.name,asc&status=1').subscribe(data => {
      if (data.content.length === 0) { return; }
      this.listAgency = this.listAgencyPage === 0 ? data.content : this.listAgency.concat(data.content);
      this.isFullListAgency = data.last;
      this.listAgencyPage++;
    });
  }

  getListDossierTaskName() {
    if (this.isFullListDossierTaskName) { return; }
    this.dossierService.getListTagByCategoryId(this.env?.dossierTaskNameCategoryId?.id, this.listDossierTaskNamePage).subscribe(data => {
      if (data.content.length === 0) { return; }
      this.listDossierTaskName = this.listDossierTaskNamePage === 0 ? data.content : this.listDossierTaskName.concat(data.content);
      this.isFullListDossierTaskName = data.last;
      this.listDossierTaskNamePage++;
    });
  }

  getListSector() {
    if (this.isFullListSector) { return; }
    this.dossierService.getListSector(this.listSectorPage).subscribe(data => {
      if (data.content.length === 0) { return; }
      this.listSector = this.listSectorPage === 0 ? data.content : this.listSector.concat(data.content);
      this.isFullListSector = data.last;
      this.listSectorPage++;
    });
  }

  getListSector1(keyword = '', page = null, size = null) {
    const searchByAgency  =  this.env?.isDienBien ? this.keySearchSectorAgency : '';
    // tslint:disable-next-line:max-line-length
    const searchString = '?keyword=' + keyword + '&page=' + page + '&size=' + size + '&spec=page&sort=name.name,asc&status=1' + searchByAgency;
    this.procedureService.getListSector(searchString).subscribe(res => {
      if (page === 0) {
        this.listSector = res.content;
      } else {
        this.listSector = this.listSector.concat(res.content);
      }
      this.totalPagesSector = res.totalPages;
      this.sectors = JSON.parse(JSON.stringify(this.listSector).replace(/null/g, '""'));
      this.sectorFiltered.next(this.sectors);
      this.searchSectorCtrl.valueChanges.pipe(takeUntil(this.onDestroy)).subscribe(() => {
        this.filterSector();
      });
    }, err => {
      console.log(err);
    });
  }
  getListSectorScroll() {
    this.getListSector1(this.keywordSector, this.currentPageSector, this.pageSizeSector);
    this.currentPageSector += 1;
  }

  protected filterSector() {
    if (!this.sectors) {
      return;
    }
    let search = this.searchSectorCtrl.value.trim();
    this.searchSectorKeyword = search;
    if (!search) {
      this.sectorFiltered.next(this.sectors.slice());
      return;
    } else {
      search = search.toLowerCase();
    }
    if (this.sectors.filter(sec => sec.name.toLowerCase().indexOf(search) > -1).length > 0) {
      this.sectorFiltered.next(
        this.sectors.filter(sec => sec.name.toLowerCase().indexOf(search) > -1)
      );
    } else {
      this.currentPageSector = 0;
      const searchByAgency  =  this.env?.isDienBien ? this.keySearchSectorAgency : '';
      const searchString = '?keyword=' + search + '&page=' + this.currentPageSector + '&size=' + 10 + '&spec=page&sort=name.name,asc&status=1' + searchByAgency;
      this.procedureService.getListSector(searchString).subscribe(data => {
        for (let i = 0; i < data.numberOfElements; i++) {
          this.listSector.push(data.content[i]);
        }
        this.sectors = JSON.parse(JSON.stringify(this.listSector).replace(/null/g, '""'));
      }, err => {
        console.log(err);
      });
    }
  }

  getListProcedureforAll(sectorId) {
    if (this.isFullListProcedure) { return; }
    this.dossierService.getListProcedure(sectorId, this.listProcedurePage).subscribe(data => {
      if (data.content.length === 0) { return; }
      this.listProcedure = this.listProcedurePage === 0 ? data.content : this.listProcedure.concat(data.content);
      this.isFullListProcedure = data.last;
      this.listProcedurePage++;
    });

  }

  getListProcedure(sectorId) {
    if (this.isFullListProcedure) {
      return;
    } else {
      let agencyIdSearch = '';
      if ( this.userAgency !== null && this.userAgency !== undefined && !this.isAdmin) {
        if (!!this.userAgency.parent && !!this.userAgency.parent.id) {
          agencyIdSearch = `&agency-id=${this.userAgency.parent.id}`;
        } else if (this.userAgency.id !== this.config.rootAgency.id) {
          agencyIdSearch = `&agency-id=${this.userAgency.id}`;
        }
      }
      agencyIdSearch = this.env?.filterProcedureByAgencyId ? agencyIdSearch : '';
      const searchByAgency  =  this.env?.isDienBien ? this.keySearchSectorAgency : '';
      const searchString =
        '?status=1&sort=translate.name,asc&keyword=' + this.searchProcedureKeyword +
        '&spec=page&page=' + this.listProcedurePage + '&size=50' +
        '&sector-id=' + sectorId + searchByAgency + agencyIdSearch;
      this.procedureService.getListProcedure(searchString).subscribe(data => {
        this.isFullListProcedure = data.last;
        this.listProcedurePage++;
        this.procedureFiltered = new ReplaySubject<any[]>(1);
        this.listProcedure = [];
        for (let i = 0; i < data.numberOfElements; i++) {
          this.listProcedure.push(data.content[i]);
        }
        this.procedures = JSON.parse(JSON.stringify(this.listProcedure).replace(/null/g, '""'));
        this.procedureFiltered.next(this.procedures);
        this.searchProcedureCtrl.valueChanges.pipe(takeUntil(this.onDestroy)).subscribe(() => {
          this.filterProcedure();
        });
      }, err => {
        console.log(err);
      });
    }
  }

  protected filterProcedure() {
    if (!this.procedures) {
      return;
    }
    let search = encodeURIComponent(this.searchProcedureCtrl.value.trim()).substring(0, 1000);
    this.searchProcedureKeyword = search;
    if (!search) {
      this.procedureFiltered.next(this.procedures.slice());
      return;
    } else {
      search = search.toLowerCase();
    }
    if (this.procedures.filter(proc => proc.name.toLowerCase().indexOf(search) > -1).length > 0) {
      this.procedureFiltered.next(
        this.procedures.filter(proc => proc.name.toLowerCase().indexOf(search) > -1)
      );
    } else {
      // tslint:disable-next-line: max-line-length
      let agencyIdSearch = '';
      if ( this.userAgency !== null && this.userAgency !== undefined && !this.isAdmin) {
        if (!!this.userAgency.parent && !!this.userAgency.parent.id) {
          agencyIdSearch = `&agency-id=${this.userAgency.parent.id}`;
        } else if (this.userAgency.id !== this.config.rootAgency.id) {
          agencyIdSearch = `&agency-id=${this.userAgency.id}`;
        }
      }
      agencyIdSearch = this.env?.filterProcedureByAgencyId ? agencyIdSearch : '';
      const searchString =
        '?status=1&sort=translate.name,asc&keyword=' + search +
        '&spec=page&page=0&size=50' +
        '&sector-id=' + this.searchForm.get('advSector').value + this.keySearchSectorAgency + agencyIdSearch;
      this.listProcedurePage = 0;
      this.procedureService.getListProcedure(searchString).subscribe(data => {
        this.procedureFiltered = new ReplaySubject<any[]>(1);
        this.listProcedure = [];
        for (let i = 0; i < data.numberOfElements; i++) {
          this.listProcedure.push(data.content[i]);
        }
        this.procedures = JSON.parse(JSON.stringify(this.listProcedure).replace(/null/g, '""'));
        this.procedureFiltered.next(
          this.procedures
        );
      }, err => {
        console.log(err);
      });
    }
  }

  getListDossier(searchString) {
    this.dossierService.getListDossier(searchString + this.searchDomain).subscribe(async data => {
        if (this.syncPaymentStatus) {
          this.dossierFeeByDossier.clear();
          const ids = data.content.map(item => item.id);
          const dossierFee = await this.commonService.getProcostDossiers(ids); 
          dossierFee.forEach(item => {
            if (!!!this.dossierFeeByDossier.get(item.dossier.id)) {
              this.dossierFeeByDossier.set(item.dossier.id, [])
            }
            this.dossierFeeByDossier.set(item.dossier.id, [item, ...this.dossierFeeByDossier.get(item.dossier.id)])
          })
        }
        
        if (!this.deploymentService.env.OS_QNI.dossierSyncListEnable) {
          this.ELEMENTDATA = [];
        }
        this.listTimesheet = [];
        this.numberOfElements = data.numberOfElements;
        let listId = [];
        
        for (let i = 0; i < data.numberOfElements; i++) {
            listId.push(data.content[i].id);
            let requireAdditional = true;
            data.content[i].stt = this.size * (this.pageIndex - 1) + (i + 1);

            if (!!data.content[i].task && data.content[i].task.length !== 0 && this.fakeCompletedDate) {
                if (this.taskCompletedStatusIds.findIndex(item => item === data.content[i].dossierTaskStatus.id) !== -1) {
                    data.content[i].completedDate = data.content[i].task[data.content[i].task.length - 1].assignedDate;
                    data.content[i].returnedDate = data.content[i].task[data.content[i].task.length - 1].assignedDate;
                }
            }

            if (!!data.content[i].currentTask && data.content[i].currentTask.length !== 0) {
                if (data.content[i].currentTask[0].bpmProcessDefinitionTask.remind.id === this.finaceObligatingStatus) {
                    data.content[i].dossierStatus.checkFinaceObligating = true;
                } else {
                    data.content[i].dossierStatus.checkFinaceObligating = false;
                }
            }

            if (!!data.content[i].sync && data.content[i].sync.typeId !== null
                && data.content[i].sync.typeId === 0 && !!data.content[i].sync.note) {
                data.content[i].statusSync = data.content[i].sync.note;
            } else {
                data.content[i].statusSync = 'Hồ sơ chưa đồng bộ';
            }

            // tslint:disable-next-line:prefer-for-of
            for (let m = 0; m < this.arrReceptionForm.length; m++) {
                if (data.content[i].applyMethod.id === this.arrReceptionForm[m].id) {
                    data.content[i].codeText = data.content[i].code + this.arrReceptionForm[m].content;
                }
            }
            data.content[i].due = [];
            if (data.content[i].acceptedDate !== undefined && data.content[i].processingTime !== undefined) {
                let processingTime = 0;
                const workingDayTime = 8;
                switch (data.content[i].processingTimeUnit) {
                    case 'y':
                        processingTime = data.content[i].processingTime * 365;
                        break;
                    case 'M':
                        processingTime = data.content[i].processingTime * 30;
                        break;
                    case 'd':
                        processingTime = data.content[i].processingTime;
                        break;
                    case 'H:m:s':
                        processingTime = data.content[i].processingTime / workingDayTime;
                        break;
                    case 'h':
                        processingTime = data.content[i].processingTime / workingDayTime;
                        break;
                    case 'm':
                        processingTime = data.content[i].processingTime / (workingDayTime * 60);
                        break;
                }

                let oneappointmentDate = null;
                if (!!data.content[i].appointmentDate) {
                    oneappointmentDate = data.content[i].appointmentDate;
                }

                if (this.env?.limitedAppointmentTime) {
                    this.listTimesheet.push({
                        timesheet: {
                            // tslint:disable-next-line: max-line-length
                            id: data.content[i].procedureProcessDefinition?.processDefinition?.timesheet !== undefined ? data.content[i].procedureProcessDefinition.processDefinition.timesheet.id : this.config.defaultTimesheetId
                        },
                        dossier: {
                            id: data.content[i].id
                        },
                        duration: this.deploymentService.env.timesheetV2 ? data.content[i].processingTime : processingTime,
                        startDate: data.content[i].acceptedDate,
                        endDate: '',
                        checkOffDay: true,
                        offTime: this.env?.limitedAppointmentTime,
                        processingTimeUnit: data.content[i].processingTimeUnit,
                        appointmentDate: oneappointmentDate
                    });
                } else if (this.deploymentService.env.OS_HCM?.limitedAppointmentTimeOnProcedure) {
                  this.listTimesheet.push({
                    timesheet: {
                        // tslint:disable-next-line: max-line-length
                        id: data.content[i].procedureProcessDefinition?.processDefinition?.timesheet !== undefined ? data.content[i].procedureProcessDefinition.processDefinition.timesheet.id : this.config.defaultTimesheetId
                    },
                    dossier: {
                        id: data.content[i].id
                    },
                    duration: this.deploymentService.env.timesheetV2 ? data.content[i].processingTime : processingTime,
                    startDate: data.content[i].acceptedDate,
                    endDate: '',
                    checkOffDay: true,
                    extendHCM: {
                      offTime: this.deploymentService.env.OS_HCM?.limitedAppointmentTimeOnProcedure,
                      startAtNextDay: data.content[i]?.extendHCM?.startAtNextDay ?? false,
                      appointmentAtNextDay: data.content[i]?.extendHCM?.appointmentAtNextDay ?? false
                    },
                    processingTimeUnit: data.content[i].processingTimeUnit,
                    appointmentDate: oneappointmentDate
                });
                } else {
                    this.listTimesheet.push({
                        timesheet: {
                            // tslint:disable-next-line:max-line-length
                            id: data.content[i].procedureProcessDefinition?.processDefinition?.timesheet !== undefined ? data.content[i].procedureProcessDefinition.processDefinition.timesheet.id : this.config.defaultTimesheetId
                        },
                        dossier: {
                            id: data.content[i].id
                        },
                        duration: this.deploymentService.env.timesheetV2 ? data.content[i].processingTime : processingTime,
                        startDate: data.content[i].acceptedDate,
                        endDate: '',
                        checkOffDay: true,
                        processingTimeUnit: data.content[i].processingTimeUnit,
                        appointmentDate: oneappointmentDate
                    });
                }
                data.content[i].dossierEndDate = null;
            }

            if (data.content[i].dossierTaskStatus !== undefined && data.content[i].dossierTaskStatus !== null) {
                data.content[i].dossierStatus.name = data.content[i].dossierTaskStatus.name;
                // tslint:disable-next-line:max-line-length
                if (data.content[i].dossierTaskStatus.id === this.deploymentService.env.dossierTaskStatus?.requestForAdditionalDocuments?.id || data.content[i].dossierTaskStatus.id === this.deploymentService.env.dossierTaskStatus?.dossierAdded?.id) {
                    requireAdditional = false;
                }
                if (this.syncPaymentStatus) {
                    if (data.content[i].dossierTaskStatus?.id == this.paymentRequestId) {
                        let isPaid = true;
                        if (this.enableRemoveVnpostFeeToAnotherTable == 1) {
                            let vnpostFee: any = null;
                            vnpostFee = await this.getVnpostFeeDossier(data.content[i].id);
                            if (vnpostFee != null) {
                                vnpostFee.forEach(element => {
                                    if (element.paid != (element.quantity * element.amount)) {
                                        isPaid = false;
                                    }
                                });
                            }
                            // const dossierFee = await this.getProcostDossier(data.content[i].id);
                            const dossierFee = this.dossierFeeByDossier.get(data.content[i].id);
                            dossierFee.forEach(element => {
                                if (element.paid != (element.quantity * element.amount)) {
                                    isPaid = false;
                                }
                            });
                        } else {
                            // const dossierFee = await this.getProcostDossier(data.content[i].id);
                            const dossierFee = this.dossierFeeByDossier.get(data.content[i].id);
                            dossierFee.forEach(element => {
                                if (element.paid != (element.quantity * element.amount)) {
                                    isPaid = false;
                                }
                            });
                        }
                        if (isPaid) {
                            data.content[i].dossierStatus.id = this.paidDossierId;
                            data.content[i].dossierStatus.name = this.paidDossierName;
                        }
                    }
                }
            } else {
                // tslint:disable-next-line: no-string-literal
                if (data.content[i].dossierStatus.id === 0 && this.justRegistered && this.justRegistered['trans']) {
                    // tslint:disable-next-line:max-line-length tslint:disable-next-line: no-string-literal
                    data.content[i].dossierStatus.name = this.justRegistered['trans'].filter(res => Number(res.languageId) === Number(localStorage.getItem('languageId')))[0].name;
                } else {
                    if (data.content[i].currentTask !== undefined && data.content[i].currentTask.length !== 0) {
                        // tslint:disable-next-line: no-string-literal
                        data.content[i].dossierStatus.name = data.content[i].currentTask[0].bpmProcessDefinitionTask.name['name'];
                    }
                }
            }
            data.content[i].requireAdditional = requireAdditional;
            const task = data.content[i]?.task ? data.content[i]?.task : [''];
            if (task.length > 0) {
                const taskCurrentCheck = task.filter(t => t.isCurrent === 1).length;
                const taskCurrent = taskCurrentCheck.lenght ? taskCurrentCheck[0] : [];
                const agencyName = taskCurrent?.agency?.name.filter(n => n.languageId === this.selectedLangId)[0]?.name;
                const assignee = taskCurrent?.assignee?.fullname ? ' - ' + taskCurrent.assignee?.fullname : '';
                data.content[i].tagDbn = agencyName ? agencyName + assignee : ' ';
            }

            data.content[i].canReassign = false;
            if (!!data.content[i].previousTask && data.content[i].previousTask.length !== 0) {
                if (data.content[i].previousTask[0]?.assignee?.id === this.userId) {
                    data.content[i].canReassign = true;
                }
            }

            this.ELEMENTDATA.push(data.content[i]);
        }
        console.log("this.ELEMENTDATA", this.ELEMENTDATA);
        
        this.dataSource.data = this.ELEMENTDATA;
        this.setTotalElements(data, this.paginationType);
        this.postTimesheet();
        this.dossierService.getListDossierFeeChange(listId).subscribe(async dataFee => {
          for (let i = 0; i < data.numberOfElements; i++) {
            data.content[i].dossierFee = dataFee[i].dossierFee;
            data.content[i].chargeFeesDossier = dataFee[i].chargeFeesDossier;
            data.content[i].paid = 0;
            data.content[i].total = 0;
            data.content[i].typeMoney = " đ";
            if(!!dataFee[i].chargeFeesDossier && dataFee[i].chargeFeesDossier.length > 0){
              data.content[i].paid = 1;
              data.content[i].total = (dataFee[i].chargeFeesDossier[0].total);
            }
            else{
              let totalFee = 0;
              for(let k = 0; k < dataFee[i].dossierFee.length; k++){
                if(dataFee[i].dossierFee[k].paid < (dataFee[i].dossierFee[k].amount * dataFee[i].dossierFee[k].quantity)){
                  totalFee += dataFee[i].dossierFee[k].amount*dataFee[i].dossierFee[k].quantity;
                }
              }
              data.content[i].total = totalFee;
            }
          }
        });
    });
  }
  async getProcostDossier(dossierId) {
    return new Promise<Array<any>>(resolve => {
      this.dossierService.getDossierFee(dossierId).subscribe(data => {
        resolve(data);
      });
    });
  }  

  async getVnpostFeeDossier(dossierId) {
    return new Promise<Array<any>>(resolve => {
      this.dossierService.getVnpostFee(dossierId).subscribe(data => {
        resolve(data);
      });
    });
  }
  getDossierTaskStatus() {
    this.dossierService.getAgencyTag(this.deploymentService.env.dossierTaskStatus?.justRegistered?.id).subscribe(rs => {
      this.justRegistered = rs;
    }, err => {
      console.log(err);
    });
  }
  advNationChange(event) {
    if ( this.searchForm.get('advNation').value){
      this.getListProvince();
    }else{
      this.listProvince = [];
      this.listDistrict = [];
      this.listWard = [];
    }
  }

  advProvinceChange(event) {
    this.getListDistrict();
  }

  advDistrictChange(event) {
    this.getListWard(event.value);
  }

  printReportChange(event) {
    event.value = '';
  }

  advSectorChange(event) {
    this.listProcedure = [];
    this.isFullListProcedure = false;
    this.listProcedurePage = 0;
    this.getListProcedure(event.value);
  }

  getListNation() {
    this.dossierService.getListNation().subscribe(data => {
      this.listNation = data;
      // this.searchForm.patchValue({
      //   advNation: data[0].id
      // });
      // this.getListProvince();
    }, err => {
      console.log(err);
    });
  }

  getListProvince() {
    this.dossierService.getListPlace(this.searchForm.get('advNation').value, null, this.config.placeProvinceTypeId)
      .subscribe(data => {
        // this.searchForm.patchValue({
        //   advProvince: this.config.placeDefaultProvinceId
        // });
        if (this.searchForm.get('advNation').value === '5f39f4a95224cf235e134c5c'){
          data.unshift({id: '', name: 'Tất cả'});
          this.listProvince = data;
        }else{
          this.listProvince = [];
          this.listDistrict = [];
          this.listWard = [];
        }
        this.getListDistrict();
      }, err => {
        console.log(err);
      });
  }

  getListDistrict() {
    if (this.searchForm.get('advProvince').value){
      // tslint:disable-next-line:max-line-length no-unused-expression
      this.dossierService.getListPlace(this.searchForm.get('advNation').value, this.searchForm.get('advProvince').value, this.config.placeDistrictTypeId)
        .subscribe(data => {
          if (this.searchForm.get('advNation').value === '5f39f4a95224cf235e134c5c'){
            data.unshift({id: '', name: 'Tất cả'});
            this.listDistrict = data;
          }else{
            this.listDistrict = [];
          }
          this.getListWard(null);
          // this.listDistrict = [];
          // data.unshift({id: '', name: 'Tất cả'});
          // this.listDistrict = data;
        }, err => {
          console.log(err);
        });
    }else {
      this.listDistrict = [];
    }
  }

  getListWard(value) {
    // tslint:disable-next-line: max-line-length
    if (this.searchForm.get('advDistrict').value && value && this.listDistrict.length > 0){
      // tslint:disable-next-line:max-line-length
      this.dossierService.getListPlace(this.searchForm.get('advNation').value, this.searchForm.get('advDistrict').value, this.config.placeWardTypeId)
        .subscribe(data => {
          if (this.searchForm.get('advNation').value === '5f39f4a95224cf235e134c5c'){
            data.unshift({id: '', name: 'Tất cả'});
            this.listWard = data;
          }else{
            this.listWard = [];
          }
        }, err => {
          console.log(err);
        });
    }else{
      this.listWard = [];
    }

  }

  getDate(dateTime) {
    return dateTime ? this.datePipe.transform(dateTime, 'dd/MM/yyyy') : '';
  }

  setAll(value) {
    this.isCheckedAll = value;
    // tslint:disable-next-line: prefer-for-of
    for (let i = 0; i < this.ELEMENTDATA.length; i++) {
      this.ELEMENTDATA[i].checked = value;
    }
  }

  onConfirmSearch() {
    const formObj = this.searchForm.getRawValue();
    const searchString = 'search?sort=updatedDate,desc&page=0&size=' + this.size + '&spec=' + this.paginationType + this.makeRequestUrl();
    this.pageIndex = 1;
    this.page = 1;
    this.getListDossier(searchString);
    // this.callRemindTask();
    // this.router.navigateByUrl('/dossier/search?page=' + (this.pageIndex) + '&size=' + this.size + '&spec=page' + this.makeRequestUrl());
  }

  makeRequestUrl() {
    let userAgencyId: any = '';
    if (this.userAgency !== null) {
      userAgencyId = this.userAgency.id;
      if (!!this.userAgency.parent) {
        userAgencyId = this.userAgency.parent.id;
      }
    }
    const formObj = this.searchForm.getRawValue();
    const ancestorAgencyExpand = formObj.advAgency ?  formObj.advAgency : this.env.rootAgency.id;
    let searchString = this.xpandStatus === false ? '&code=' + formObj.code.trim() +
      '&identity-number=' + formObj.identityNumber.trim() +
      '&applicant-name=' + formObj.applicantName.trim() +
      '&applicant-owner-name=' + formObj.ownerFullname.trim() +
      '&accepted-from=' + (formObj.advAcceptFrom ? this.datePipe.transform(formObj.advAcceptFrom, 'dd/MM/yyyy') : '') +
      '&accepted-to=' + (formObj.advAcceptTo ? this.datePipe.transform(formObj.advAcceptTo, 'dd/MM/yyyy') : '') +
      '&charge-fees=' + formObj.chargeFees +
      '&type-charge-fees=' + formObj.typechargeFees +
      '&ancestor-agency-id=' + ancestorAgencyExpand +
      '&remind-id=' + this.remindId
      // '&ancestor-agency-id=' + userAgencyId 
      :
      '&identity-number=' + formObj.identityNumber.trim() +
      '&applicant-name=' + formObj.applicantName.trim() +
      '&applicant-owner-name=' + formObj.ownerFullname.trim() +
      '&remind-id=' + this.remindId +
      '&code=' + formObj.code.trim() +
      '&sector-id=' + formObj.advSector +
      '&procedure-id=' + formObj.advProcedure +
      '&nation-id=' + formObj.advNation +
      '&province-id=' + formObj.advProvince +
      '&district-id=' + formObj.advDistrict +
      '&ward-id=' + formObj.advWard +
      '&address=' + formObj.advAddress.trim() +
      '&task-status-id=' + formObj.advTaskStatusId +
      '&dossier-status=' + formObj.advProcessStatus +
      '&apply-method-id=' + formObj.advApplyMethod +
      '&accepted-from=' + (formObj.advAcceptFrom ? this.datePipe.transform(formObj.advAcceptFrom, 'dd/MM/yyyy') : '') +
      '&accepted-to=' + (formObj.advAcceptTo ? this.datePipe.transform(formObj.advAcceptTo, 'dd/MM/yyyy') : '') +
      '&appointment-from=' + (formObj.advAppointmentFrom ? this.datePipe.transform(formObj.advAppointmentFrom, 'dd/MM/yyyy') : '') +
      '&appointment-to=' + (formObj.advAppointmentTo ? this.datePipe.transform(formObj.advAppointmentTo, 'dd/MM/yyyy') : '') +
      // tslint:disable-next-line: max-line-length
      '&result-returned-from=' + (formObj.avdResultReturnedFrom ? this.datePipe.transform(formObj.avdResultReturnedFrom, 'dd/MM/yyyy') : '') +
      '&result-returned-to=' + (formObj.avdResultReturnedTo ? this.datePipe.transform(formObj.avdResultReturnedTo, 'dd/MM/yyyy') : '') +
      '&ancestor-agency-id=' + ancestorAgencyExpand ;


    if (this.hasParticipatedInProcessing) {
      searchString += !this.xpandStatus ? '&task-ancestor-agency-id=' + userAgencyId : '&task-ancestor-agency-id=' + ancestorAgencyExpand;
    }

    return searchString;
  }

  onClickOpenAdvancedSearchBox() {
    this.xpandStatus = this.xpandStatus ? false : true;
    if(this.xpandStatus){
      if(this.depConfig?.addressDefault?.nationId){
        const nationId = this.searchForm.controls['advNation'].value;
        if(!nationId){
          const defaultNationId = this.depConfig?.addressDefault?.nationId;
          this.searchForm.patchValue({advNation: defaultNationId});
          this.advNationChange({value: defaultNationId});
        }
        setTimeout(() => {
          const provinceId = this.searchForm.controls['advProvince'].value;
          if(!provinceId){
            const defaultProvinceId = this.depConfig?.addressDefault?.provinceId;
            this.searchForm.patchValue({advProvince: defaultProvinceId});
            this.advProvinceChange({value: defaultProvinceId});
          }
        }, 2000);
      }
    }
  }

  paginate() {
    console.log('123');
    // tslint:disable-next-line: max-line-length
    this.getListDossier('search?sort=updatedDate,desc&page=' + (this.pageIndex - 1) + '&size=' + this.size + '&spec=' + this.paginationType + this.makeRequestUrl());
    // tslint:disable-next-line: max-line-length
    this.router.navigateByUrl('/charge-fees/apply-fee-dossiers?page=' + (this.pageIndex) + '&size=' + this.size + '&spec=' + this.paginationType + this.makeRequestUrl());
  }

  postTimesheet() {
    const requestBody = JSON.stringify(this.listTimesheet, null, 2);
    this.dossierService.postTimesheet(requestBody).subscribe(data => {
      const newDate = tUtils.newDate();
      let i = 0;
      data.forEach(dt => {
        dt.timesheet = {
          isOverDue: false,
          timer: {
            day: 0,
            hour: 0,
            minute: 0,
            second: 0
          }
        };

        let time = '';
        let dateDue = dt.due;
        if (!!this.listTimesheet[i] && !!this.listTimesheet[i].appointmentDate){
          dateDue = this.listTimesheet[i].appointmentDate;
        }
        if (new Date(dateDue).getTime() < newDate.getTime()) {
          dt.timesheet.isOverDue = true;
          time = ((newDate.getTime() - new Date(dateDue).getTime()) / 1000).toString();
        } else {
          time = ((new Date(dateDue).getTime() - newDate.getTime()) / 1000).toString();
        }

        // let seconds = time;
        let seconds = parseInt(time, 10);
        const days = Math.floor(seconds / (3600 * 24));
        seconds -= days * 3600 * 24;
        const hrs = Math.floor(seconds / 3600);
        seconds -= hrs * 3600;
        const mnts = Math.floor(seconds / 60);
        seconds -= mnts * 60;

        dt.timesheet.timer = {
          day: days,
          hour: hrs,
          minute: mnts,
          second: seconds
        };

        this.ELEMENTDATA.forEach(elem => {
          if (elem.id === dt.dossier.id) {
            elem.due.push(dt);
          }
        });
        i++;
      });

      data.forEach(dt => {
        this.ELEMENTDATA.forEach(elem => {
          if (elem.id === dt.dossier.id) {
            elem.dossierEndDate = new Date(dt.due);
          }
        });
      });

      this.dataSource.data = this.ELEMENTDATA;
    });
  }

  dossierDetail(dossierId, procedureId, task) {
    const queryParamsObject = {
      procedure: procedureId,
    };

    if (task !== undefined) {
      Object.assign(queryParamsObject, { task: task[task.length - 1].id });
    }

    const isOpenInNewTab = this.deploymentService.getAppDeployment()?.env?.OS_KTM?.openInNewTab === true ? true  : false;
    if (isOpenInNewTab) {
      const url = this.router.serializeUrl(
        this.router.createUrlTree(['dossier/search/' + dossierId], {
          queryParams: queryParamsObject
        })
      );

      window.open(url, '_blank');
      return;
    }
    this.router.navigate(['dossier/search/' + dossierId], {
      queryParams: queryParamsObject
    });
  }

  getStatusColor(type) {
    switch (type) {
      case 0: return '#000000de';
      case 1: return '#FF9800';
      case 2: return '#f39c12';
      case 3: return '#FF9800';
      case 4: return '#03A9F4';
      case 5: return '#03A9F4';
      case 6: return '#DE1212';
      default: return '#000000de';
    }
  }

  returnAccept(id, code) {
    // tslint:disable-next-line: max-line-length
    const dialogData = new ConfirmationDialogModel('Trả hồ sơ về chờ tiếp nhận', `Bạn có chắc chắn muốn trả hồ sơ ${code} về chờ tiếp nhận?`);
    const dialogRef = this.dialog.open(ConfirmDialogComponent, {
      width: '512px',
      data: dialogData,
      disableClose: true,
      autoFocus: false,
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
      if (!!dialogResult) {
        this.dossierService.putDossierReturnAccept(id).subscribe(data => {
          if (data.affectedRows === 1) {
            const msgObj = {
              vi: 'Trả hồ sơ thành công!',
              en: 'Return dossier successful!'
            };
            // tslint:disable-next-line: max-line-length
            this.snackbarService.openSnackBar(1, msgObj[localStorage.getItem('language')], '', 'success_notification', this.config.expiredTime);
            const dataLog = {
              dossierId: id,
              code : code
            };
            this.logmanService.postUserEventsLog('dossierReturnAccept PerformChargeFees', dataLog).subscribe();
            this.onConfirmSearch();
          } else {
            const msgObj = {
              vi: 'Trả hồ sơ thất bại!',
              en: 'Return dossier failed!'
            };
            // tslint:disable-next-line: max-line-length
            this.snackbarService.openSnackBar(0, msgObj[localStorage.getItem('language')], '', 'error_notification', this.config.expiredTime);
          }
          this.callRemindTask();
        });
      }
    });
  }

  forceEndProcess(id, code) {
    const checkbox = {
      enable: true,
      title: "Không cập nhật quá hạn trả hồ sơ"
    }
    const dialogData = new ConfirmationDialogModel('', `Bạn có chắc chắn muốn kết thúc hồ sơ "${code}" không?`, true, checkbox);
    const dialogRef = this.dialog.open(ConfirmDialogComponent, {
      width: '512px',
      data: dialogData,
      disableClose: true,
      autoFocus: false,
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
      if (!!dialogResult?.confirm) {
        const requestBody = [
          {
            id,
            code
          }
        ];
        this.dossierService.putDossierForceEndProcess(JSON.stringify(requestBody, null, 2), dialogResult?.checkboxVal).subscribe(data => {
          if (data.affectedRows === 1) {
            const msgObj = {
              vi: 'Kết thúc hồ sơ thành công!',
              en: 'Force end process successful!'
            };
            // tslint:disable-next-line: max-line-length
            this.snackbarService.openSnackBar(1, msgObj[localStorage.getItem('language')], '', 'success_notification', this.config.expiredTime);
            this.onConfirmSearch();
          } else {
            const msgObj = {
              vi: 'Kết thúc hồ sơ thất bại!',
              en: 'Force end process failed!'
            };
            // tslint:disable-next-line: max-line-length
            this.snackbarService.openSnackBar(0, msgObj[localStorage.getItem('language')], '', 'error_notification', this.config.expiredTime);
          }
          this.callRemindTask();
        });
      }
    });
  }

  viewProcess(dossierId, dossierCode) {
    const dialogData = new ProcessHandleDialogModel(dossierId, dossierCode, 'dossierSearch');
    const dialogRef = this.dialog.open(ProcessHandleComponent, {
      minWidth: '70vw',
      data: dialogData,
      disableClose: true,
      autoFocus: false
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
    });
  }

  additionalRequirement(dossierId, dossierCode) {
    if (dossierCode === undefined || dossierCode === null) {
      dossierCode = '';
    }
    const dialogData = new ConfirmAdditionalRequirementDialogModel(dossierId, dossierCode);
    const dialogRef = this.dialog.open(AdditionalRequirementComponent, {
      minWidth: '50vw',
      maxHeight: '85vh',
      data: dialogData,
      disableClose: true,
      autoFocus: false
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
      if (dialogResult === true) {
        const msgObj = {
          vi: 'Đã yêu cầu bổ sung!',
          en: 'Additional request sent!'
        };
        this.snackbarService.openSnackBar(1, msgObj[localStorage.getItem('language')], '', 'success_notification', this.config.expiredTime);
        this.onConfirmSearch();
      }

      if (dialogResult === false) {
        const msgObj = {
          vi: 'Yêu cầu bổ sung không thành công!',
          en: 'Additional request failed!'
        };
        this.snackbarService.openSnackBar(1, msgObj[localStorage.getItem('language')], '', 'success_notification', this.config.expiredTime);
      }
      this.callRemindTask();
    });
  }

  suspenDialogs(dossierId, dossierCode) {
    if (dossierCode === undefined || dossierCode === null) {
      dossierCode = '';
    }
    const dialogData = new SuspendModel(dossierId, dossierCode);
    const dialogRef = this.dialog.open(SuspendComponent, {
      minWidth: '50vw',
      data: dialogData,
      disableClose: true,
      autoFocus: false
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
      if (dialogResult === true) {
        const msgObj = {
          vi: 'Đã tạm dừng hồ sơ!',
          en: 'Dossier has been suspend!'
        };
        this.snackbarService.openSnackBar(1, msgObj[localStorage.getItem('language')], '', 'success_notification', this.config.expiredTime);
        this.onConfirmSearch();
      }

      if (dialogResult === false) {
        const msgObj = {
          vi: 'Tạm dừng hồ sơ không thành công!',
          en: 'Suspend failed!'
        };
        this.snackbarService.openSnackBar(1, msgObj[localStorage.getItem('language')], '', 'success_notification', this.config.expiredTime);
      }
    });
  }

  refuseDialog(dossierId, dossierCode) {
    if (dossierCode === undefined || dossierCode === null) {
      dossierCode = '';
    }
    const dialogData = new RefuseDialogModel(dossierId, dossierCode);
    const dialogRef = this.dialog.open(RefuseComponent, {
      minWidth: '50vw',
      maxHeight: '75vh',
      data: dialogData,
      disableClose: true,
      autoFocus: false
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
      if (dialogResult === true) {
        const msgObj = {
          vi: 'Đã tạm dừng hồ sơ!',
          en: 'Dossier has been suspend!'
        };
        this.snackbarService.openSnackBar(1, msgObj[localStorage.getItem('language')], '', 'success_notification', this.config.expiredTime);
        this.onConfirmSearch();
      }

      if (dialogResult === false) {
        const msgObj = {
          vi: 'Tạm dừng hồ sơ không thành công!',
          en: 'Suspend failed!'
        };
        this.snackbarService.openSnackBar(1, msgObj[localStorage.getItem('language')], '', 'success_notification', this.config.expiredTime);
      }
    });
  }

  resumeDialog(dossierId, dossierCode) {
    if (dossierCode === undefined || dossierCode === null) {
      dossierCode = '';
    }
    const dialogData = new ResumeProcessingModel(dossierId, dossierCode);
    const dialogRef = this.dialog.open(ResumeProcessingComponent, {
      minWidth: '50vw',
      data: dialogData,
      disableClose: true,
      autoFocus: false
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
      if (dialogResult === true) {
        const msgObj = {
          vi: 'Tiếp tục xử lý hồ sơ!',
          en: 'Dossier has been resumed!'
        };
        this.snackbarService.openSnackBar(1, msgObj[localStorage.getItem('language')], '', 'success_notification', this.config.expiredTime);
        this.onConfirmSearch();
      }

      if (dialogResult === false) {
        const msgObj = {
          vi: 'Tiếp tục xử lý hồ sơ không thành công!',
          en: 'Resume failed!'
        };
        this.snackbarService.openSnackBar(1, msgObj[localStorage.getItem('language')], '', 'success_notification', this.config.expiredTime);
      }
    });
  }

  deleteDialog(dossierId, dossierCode) {
    if (dossierCode === undefined || dossierCode === null) {
      dossierCode = '';
    }
    const dialogData = new ConfirmDeleteDialogModel(dossierId, dossierCode);
    const dialogRef = this.dialog.open(DeleteDossierComponent, {
      width: '500px',
      data: dialogData,
      disableClose: true,
      autoFocus: false
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
      if (dialogResult === 0) {
        const msgObj = {
          vi: 'Đã xoá hồ sơ!',
          en: 'Dossier deleted!'
        };
        this.snackbarService.openSnackBar(1, msgObj[localStorage.getItem('language')], '', 'success_notification', this.config.expiredTime);
        this.onConfirmSearch();
      }

      if (dialogResult === 1) {
        const msgObj = {
          vi: 'Đã hủy hồ sơ!',
          en: 'Dossier cancelled!'
        };
        this.snackbarService.openSnackBar(1, msgObj[localStorage.getItem('language')], '', 'success_notification', this.config.expiredTime);
        this.onConfirmSearch();
      }

      if (dialogResult === null) {
        const msgObj = {
          vi: 'Xoá không thành công!',
          en: 'Deletion failed!'
        };
        this.snackbarService.openSnackBar(0, msgObj[localStorage.getItem('language')], '', 'error_notification', this.config.expiredTime);
      }
      this.callRemindTask();
    });
  }
  checkAllItem(event) {
    if (event.checked) {
      this.checkAll = true;
      for (let i = 0; i < this.numberOfElements; i++) {
        if (this.selectedDossiers.indexOf(this.dataSource.data[i].code) === -1) {
          this.selectedDossiers.push(this.dataSource.data[i].code);
          this.dataSource.data[i].checked = true;
        }
      }
    } else {
      this.checkAll = false;
      for (let i = 0; i < this.numberOfElements; i++) {
        const index = this.selectedDossiers.indexOf(this.dataSource.data[i].code);
        if (index >= 0) {
          this.selectedDossiers.splice(index, 1);
          this.dataSource.data[i].checked = false;
        }
      }
    }
  }
  onExcelDossierSync(){
    const formObj = this.searchForm.getRawValue();   
    let ojectData = {
      "acceptedFrom" : (formObj.advAcceptFrom ? this.datePipe.transform(formObj.advAcceptFrom, 'dd/MM/yyyy') : null) ,
      "acceptedTo" : (formObj.advAcceptTo ? this.datePipe.transform(formObj.advAcceptTo, 'dd/MM/yyyy') : null),
      "taskAncestorAgencyId" : null,
      "ancestorAgencyId": null,
      "syncDossier": "false"
    }
    this.dossierService.getExportDossierExcelSync(ojectData).then((data)=>{
      console.log(data);
      // console.log(data);
      if (data) {
        const msgObj = {
          vi: 'Tải hồ sơ thành công',
          en: 'Sync successful'
        };
        this.snackbarService.openSnackBar(1, msgObj[this.selectedLang], '', 'success_notification', this.config.expiredTime);
        // this.dialogRef.close(true);
      }
   })
  }

  checkItem(event, id) {
    if (event.checked) {
      this.selectedDossiers.push(id);
      let countCheck = true;
      for (let i = 0; i < this.numberOfElements; i++) {
        if (this.dataSource.data[i].checked !== true) {
          countCheck = false;
          break;
        }
      }
      this.checkAll = countCheck;
    }
    else {
      this.checkAll = false;
      const i = this.selectedDossiers.indexOf(id);
      this.selectedDossiers.splice(i, 1);
    }
  }
  // ======== Menu remind

  agencyChange(event) {
    this.agencyId = event.value === '' ? null : event.value;
    this.keySearchSectorAgency = '&agency-id=' + this.agencyIdForProcedure;
    this.getListSectorScroll();
    this.getListProcedure('');
  }
  async getRemindMenuTask() {
    let userAgencyId: any = '';
    if (this.userAgency !== null) {
      userAgencyId = this.userAgency.id;
      if (!!this.userAgency.parent) {
        userAgencyId = this.userAgency.parent.id;
      }
    }
    const agency = this.searchForm.get('advAgency').value ? this.searchForm.get('advAgency').value : userAgencyId;
    let searchString = 'ancestor-agency-id=' + agency;
    const formObj = this.searchForm.getRawValue();
    const ancestorAgencyExpand = formObj.advAgency ?  formObj.advAgency : userAgencyId;
    if (this.hasParticipatedInProcessing) {
      searchString += !this.xpandStatus ? '&task-ancestor-agency-id=' + userAgencyId : '&task-ancestor-agency-id=' + ancestorAgencyExpand;
    }
    if(!this.deploymentService.getAppDeployment()?.disableMenuRemind)
    {
    this.dossierService.getDossierMenuTaskRemindAllAgency(searchString).subscribe(data => {
      this.listMenuRemind = data;
      if(this.env?.OS_HCM?.showAmountDossier){
        data.forEach(dossier => {
          this.lengthRemind += dossier.count;
        })
      } else {
        this.lengthRemind = data.length;
      }
      this.listMenuRemind = this.listMenuRemind.sort((a, b) => {
        return compare(a.count, b.count, false);
      });
    });
    }
  }
  
  onClickOpenReminderMenu() {
    this.expandReminderMenu = !this.expandReminderMenu;
    this.remindId = '';
    this.changeSearchRemind('');
  }
  changeSearchRemind(remindId) {
    this.remindId = remindId;
    this.pageIndex = 1;
    this.page = 1;
    // this.size = 10;
    const searchString = 'search?sort=updatedDate,desc&page=0&size=' + this.size + '&spec=' + this.paginationType + this.makeRequestUrl();
    this.getListDossier(searchString);
  }
  callRemindTask() {
    this.getRemindMenuTask();
    // this.adminLayoutNavComponent.getDossierRemind();
  }
  getMaxDeleteDossierMulti() {
    const config = this.deploymentService.getAppDeployment();
    if (!!config?.env?.maxDeleteDossierMulti) {
      this.maxDeleteDossierMulti = config?.env?.maxDeleteDossierMulti;
    }
  }
  withdrawDialogs(dossierId, dossierCode, isAllAgencySearch?, isDirectory?){
    if (dossierCode === undefined || dossierCode === null) {
      dossierCode = '';
    }
    const dialogData = new WithdrawModel(dossierId, dossierCode, isAllAgencySearch, isDirectory);
    const dialogRef = this.dialog.open(WithdrawComponent, {
      minWidth: '45vw',
      data: dialogData,
      disableClose: true,
      autoFocus: false
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
      if (dialogResult === true) {
        const msgObj = {
          vi: 'Đã rút hồ sơ!',
          en: 'Dossier has been withdraw!'
        };
        this.snackbarService.openSnackBar(1, msgObj[localStorage.getItem('language')], '', 'success_notification', this.config.expiredTime);
        if (this.env?.changeRemindAllagencyMenu){
          this.callRemindTask();
        }
        this.onConfirmSearch();
      }
      if (dialogResult === false) {
        const msgObj = {
          vi: 'Rút hồ sơ không thành công!',
          en: 'Withdraw dossier failed!'
        };
        this.snackbarService.openSnackBar(1, msgObj[localStorage.getItem('language')], '', 'success_notification', this.config.expiredTime);
      }
    });
  }

  setTotalElements(data, paginationType) {
    if (paginationType === 'page') {
      this.countResult = data.totalElements;
    } else {
      if (data.last) {
        if (!!data.number) {
          this.page = data.number + 1;
          this.countResult = this.size * this.page;
        } else {
          this.countResult = this.size * this.page;
        }
      } else {
        if (this.numberOfElements < this.size) {
          this.countResult = this.size * this.page;
        } else {
          this.countResult = this.countResult + this.ELEMENTDATA.length + 1;
        }
      }
    }
  }

  changePageOrSize(obj) {
    if (tUtils.hasOwnProperty(obj, 'currentPage')) {
      this.page = obj.currentPage;
      this.pageIndex = obj.currentPage;
    }
    if (tUtils.hasOwnProperty(obj, 'itemsPerPage')) {
      this.size = obj.itemsPerPage;
    }
    if (tUtils.hasOwnProperty(obj, 'totalItems')) {
      this.countResult = obj.totalItems;
    } else {
      this.paginate();
    }
  }

}
function compare(a: number | string, b: number | string, isAsc: boolean) {
  return (a < b ? -1 : 1) * (isAsc ? 1 : -1);
}
