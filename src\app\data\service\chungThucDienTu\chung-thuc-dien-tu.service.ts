import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { ApiProviderService } from 'src/app/core/service/api-provider.service';
import { Observable } from 'rxjs';
import { DeploymentService } from '../deployment.service';
@Injectable({
  providedIn: 'root'
})
export class ChungThucDienTuService {

  constructor(
    private http: HttpClient,
    private apiProviderService: ApiProviderService,
    private deploymentService: DeploymentService
  ) { }

  private adapter = this.apiProviderService.getUrl('digo', 'adapter');
  private padman = this.apiProviderService.getUrl('digo', 'padman');
  private adapterChungThuc = this.adapter + '/api/chungthuc';
  private adapterChungThucToken = this.adapter + '/api/chungthuc-token';
  private padmanDossier = this.padman + '/dossier';
  private padmanDossierAuthResult = this.padman + '/dossier-auth-result';

  env = this.deploymentService.getAppDeployment()?.env;
  chungThucDienTu = this.deploymentService.getAppDeployment()?.chungThucDienTu;
  private agencyId = JSON.parse(localStorage.getItem('userAgency'))?.id;
  private subsystemId = this.env?.subsystem?.id;
  private configId = this.chungThucDienTu?.configId;

  initChungThuc(body): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    let url = this.adapterChungThuc + "/--init";
    url += this.subsystemId ? `?subsystem-id=${this.subsystemId}` : ``;
    url += this.agencyId ? `&agency-id=${this.agencyId}` : ``;
    url += this.configId ? `&config-id=${this.configId}` : ``;
    return this.http.post(url, body, { headers });
  }

  updateFeeChungThuc(body): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    let url = this.adapterChungThuc + "/--update-fee";
    url += this.subsystemId ? `?subsystem-id=${this.subsystemId}` : ``;
    url += this.agencyId ? `&agency-id=${this.agencyId}` : ``;
    url += this.configId ? `&config-id=${this.configId}` : ``;
    return this.http.post(url, body, { headers });
  }

  downloadFileChungThuc(signUrl, dossierAuthResultId): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    let url = this.adapterChungThuc + "/--download-file";
    url += signUrl ? `?url=${signUrl}` : ``;
    url += dossierAuthResultId ? `&result-id=${dossierAuthResultId}` : ``;
    url += this.subsystemId ? `&subsystem-id=${this.subsystemId}` : ``;
    url += this.agencyId ? `&agency-id=${this.agencyId}` : ``;
    url += this.configId ? `&config-id=${this.configId}` : ``;
    // return this.http.post(url, null, { headers });
    return this.http.post(url, null, { responseType: 'blob' as 'json' }).pipe();
  }

  previewFileChungThuc(signUrl): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    let url = this.adapterChungThuc + "/--preview-file";
    url += signUrl ? `?url=${signUrl}` : ``;
    url += this.subsystemId ? `&subsystem-id=${this.subsystemId}` : ``;
    url += this.agencyId ? `&agency-id=${this.agencyId}` : ``;
    url += this.configId ? `&config-id=${this.configId}` : ``;
    return this.http.post(url, null, { headers });
  }

  updateStatusChungThuc(body): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.put(this.padmanDossier + '/--update-status-addonAuth', body, { headers });
  }

  reSyncResultCTDT(id, code): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    let url = this.adapterChungThuc + '/resync_get_result'+'&dossierId='+id+'&dossierCode='+code;
    return this.http.get(url, { headers });
  }

  getDossierAuthResult(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.padmanDossierAuthResult + '/' + id + '/--by-dossier-id', { headers });
  }

  getTokenId(): Observable<any>{
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.adapterChungThucToken + '/get-tokenid', { headers });
  }
}
