import { Component, OnInit, Inject } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { ReceiptBookService } from 'src/app/data/service/cto-statistics/receipt-book.service';

@Component({
  selector: 'app-delete-receipt-book',
  templateUrl: './delete-receipt-book.component.html',
  styleUrls: ['./delete-receipt-book.component.scss']
})
export class DeleteReceiptBookComponent implements OnInit {

  formId: string;
  formName: string;

  constructor(
    public dialogRef: MatDialogRef<DeleteReceiptBookComponent>,
    @Inject(MAT_DIALOG_DATA) public data: ConfirmDeleteDialogModel,
    private receiptBookService: ReceiptBookService
  ) {
    this.formId = data.id;
    this.formName = data.name;
  }

  ngOnInit(): void {
  }

  onConfirm() {
    this.receiptBookService.delete(this.formId).subscribe(
      (data: any) => {
        if (Number(data.affectedRows) === 1) {
          this.dialogRef.close(true);
        } else {
          this.dialogRef.close(false);
        }
      },
      err => {
        this.dialogRef.close(false);
      }
    );
  }
  

  onDismiss() {
    this.dialogRef.close();
  }

}

export class ConfirmDeleteDialogModel {
  constructor(public id: string, public name: string) {
  }
}
