import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { ApiProviderService } from 'src/app/core/service/api-provider.service';
import { EnvService } from 'src/app/core/service/env.service';

@Injectable({
    providedIn: 'root'
})
export class FieldDocumentManagementService {
    constructor(
        private http: HttpClient,
        private envService: EnvService,
        private apiProviderService: ApiProviderService,
    ) { }

    private fieldManagementPath = this.apiProviderService.getUrl('digo', 'basepad') + '/field-document/';

    getListField(searchString): Observable<any> {
        let headers = new HttpHeaders();
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        return this.http.get(this.fieldManagementPath + searchString, { headers });
    }

    deleteField(id): Observable<any> {
        let headers = new HttpHeaders();
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        return this.http.delete(this.fieldManagementPath + `${id}`, { headers });
    }

    addOrUpdateField(id, requestBody): Observable<any> {
        let headers = new HttpHeaders();
        headers = headers.set('Content-Type', 'application/json');
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        if(id == '0'){
            return this.http.post<any>(this.fieldManagementPath, requestBody, { headers });
        }
        return this.http.put<any>(this.fieldManagementPath + `${id}`, requestBody, { headers });
    }

    getFieldById(id): Observable<any> {
        let headers = new HttpHeaders();
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        return this.http.get(this.fieldManagementPath + `${id}`, { headers });
    }

    getListFieldIsShow(): Observable<any> {
        let headers = new HttpHeaders();
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        return this.http.get(this.fieldManagementPath + 'get-all-is-show', { headers });
    }
}
