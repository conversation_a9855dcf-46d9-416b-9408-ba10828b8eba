.dialog_title {
    font-style: normal;
    font-weight: 500;
    font-size: 20px;
    color: #ce7a58;
}

::ng-deep .applyBtn {
    margin-top: 1em;
    background-color: #ce7a58;
    color: #fff;
    height: 3em;
    padding: 0 3em;
}

::ng-deep mat-dialog-container::-webkit-scrollbar-track {
    box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.1);
    background-color: #f5f5f5;
}

::ng-deep mat-dialog-container::-webkit-scrollbar {
    width: 5px;
    background-color: #f5f5f5;
}

::ng-deep mat-dialog-container::-webkit-scrollbar-thumb {
    box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.1);
    background-color: #44444450;
}

.close-button {
    float: right;
    top: -24px;
    right: -24px;
}

::ng-deep .dialog_content {
    font-size: 15px;
    max-height: unset;
    overflow: unset;
    .highlight {
        color: #ce7a58;
    }

    .formFieldItems {
        flex-wrap: wrap;
    }
}

@media screen and (max-width: 800px) {
    ::ng-deep {
        .receipt {
            .mat-row {
                &:nth-child(even) {
                    background-color: unset;
                }
                &:nth-child(odd) {
                    background-color: unset;
                }
            }

            .mat-header-row {
                display: none;
            }
            .mat-table {
                border: 0;
                vertical-align: middle;
                margin-bottom: 1em;
 
                .mat-row {
                    border-bottom: 5px solid #ddd;
                    display: block;
                    min-height: unset;
                }
                .mat-cell {
                    border-bottom: 1px solid #ddd;
                    display: block;
                    font-size: 14px;
                    text-align: right;
                    margin-bottom: 4%;
                    padding: 0 0.5em;
                    &:before {
                        content: attr(data-label);
                        float: left;
                        font-weight: 500;
                        font-size: 14px;
                        text-align: left;
                    }
                    &:last-child {
                        border-bottom: 0;
                    }
                    &:first-child {
                        margin-top: 4%;
                    }
                }
            }
        }
    }
}