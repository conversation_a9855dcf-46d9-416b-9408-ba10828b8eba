import { NgModule, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';
import { GetCitizenInfoBdhRoutingModule } from './get-citizen-info-bdh-routing.module';
import { HttpClientModule } from '@angular/common/http';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { SharedModule } from 'src/app/shared/shared.module';
import { MatIconModule } from '@angular/material/icon';
// import { GetCitizenInfoBdhComponent } from './get-citizen-info-bdh.component';

@NgModule({
  declarations: [],
  imports: [
    CommonModule,
    GetCitizenInfoBdhRoutingModule,
    FormsModule,
    ReactiveFormsModule,
    HttpClientModule,
    SharedModule,
    MatIconModule
  ]
})
export class GetCitizenInfoBdhModule { }
