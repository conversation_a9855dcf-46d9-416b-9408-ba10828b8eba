<h2><PERSON><PERSON> mục sổ tiếp nhận</h2>
<div fxLayout="row" fxLayoutAlign="center">
  <div class="frm_searchbar" fxFlex="grow">
    <form [formGroup]="searchForm" (submit)="onConfirm()" class="searchForm">
      <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="row">
        <mat-form-field appearance="outline" fxFlex.gt-sm="82" fxFlex='79'>
          <mat-label i18n>Nhập từ khoá</mat-label>
          <input matInput formControlName="keyword">
        </mat-form-field>
        <div fxFlex='1'></div>
          <button mat-flat-button fxFlex.gt-sm="17" fxFlex='20' class="searchBtn" type="submit">
            <mat-icon>search</mat-icon> <span i18n>Tìm kiếm</span>
          </button>
        </div>
    </form>
  </div>
</div>
<div fxLayout="row" fxLayoutAlign="center">
  <div class="frm_main sector" fxFlex="grow">
    <mat-tab-group>
      <mat-tab label="Danh mục sổ tiếp nhận" *ngIf="isShowSector">
        <button mat-flat-button class="btn_add" (click)="addReceiptBook()">
          <mat-icon>add</mat-icon>
          <span i18n>Thêm mới</span>
        </button>
        <div *ngIf="checkNullSectorData === 1 then nullSectorData; else hasSectorData"></div>
        <ng-template #nullSectorData>
          <br />
          <div fxLayout="row" fxLayoutAlign="center">
            <div fxFlex="grow" style="text-align: center; font-size: 18px">Không có kết quả tìm kiếm</div>
          </div>
        </ng-template>
        <ng-template #hasSectorData>
          <div class="frm_tbl_sector sector">
            <table mat-table [dataSource]="dataSource">
              <ng-container matColumnDef="stt">
                <mat-header-cell *matHeaderCellDef i18n>STT</mat-header-cell>
                <mat-cell *matCellDef="let row" data-label="STT">{{row.stt}}</mat-cell>
              </ng-container>
              <ng-container matColumnDef="code">
                <mat-header-cell *matHeaderCellDef>Mã sổ</mat-header-cell>
                <mat-cell *matCellDef="let row" data-label="Mã lĩnh vực" class="cell_code" i18n-data-label> {{row.code}}</mat-cell>
              </ng-container>
              <ng-container matColumnDef="name">
                <mat-header-cell *matHeaderCellDef>Tên sổ</mat-header-cell>
                <mat-cell *matCellDef="let row" data-label="Tên lĩnh vực" i18n-data-label>
                  <span  #tooltip="matTooltip" matTooltip="{{row.name}}" mattooltipposition="above">{{row.name}}</span>
                </mat-cell>
              </ng-container>
              <ng-container matColumnDef="year">
                <mat-header-cell *matHeaderCellDef>Năm</mat-header-cell>
                <mat-cell *matCellDef="let row" data-label="Lĩnh vực cha" i18n-data-label> {{row.year}}</mat-cell>
              </ng-container>
              <ng-container matColumnDef="status">
                <mat-header-cell *matHeaderCellDef i18n>Trạng thái</mat-header-cell>
                <mat-cell *matCellDef="let row" data-label="Trạng thái" i18n-data-label> {{row.nameStatus}}</mat-cell>
              </ng-container>
              <ng-container matColumnDef="action">
                <mat-header-cell *matHeaderCellDef i18n>Thao tác</mat-header-cell>
                <mat-cell *matCellDef="let row" data-label="Thao tác" i18n-data-label>
                  <button mat-icon-button [matMenuTriggerFor]="actionMenu">
                    <mat-icon>more_horiz</mat-icon>
                  </button>
                  <mat-menu #actionMenu="matMenu" xPosition="before">
                    <button mat-menu-item class="menuAction" (click)="updateReceiptBook(row.id)">
                      <mat-icon>edit</mat-icon> <span>Cập nhật</span>
                    </button>
                    <button mat-menu-item class="menuAction" (click)="clone(row.id)">
                      <mat-icon>content_copy</mat-icon> <span>Clone sổ</span>
                    </button>                    
                    <button mat-menu-item class="menuAction" (click)="deleteReceiptBook(row.id, row.name)">
                      <mat-icon>delete_outline</mat-icon> <span>Xoá</span>
                    </button>
                  </mat-menu>
                </mat-cell>
              </ng-container>
              <mat-header-row *matHeaderRowDef="displayedColumns"></mat-header-row>
              <mat-row *matRowDef="let row; columns: displayedColumns;"></mat-row>
            </table>
            <div class="frm_Pagination">
              <ul class="temp_Arr">
                <li *ngFor="let item of ELEMENTDATA  | paginate: {itemsPerPage: size, currentPage: page, totalItems: countResult, id: 'pgnx'}"></li>
              </ul>
              <div class="pageSize">
                <span i18n>Hiển thị</span>
                <mat-form-field appearance="outline">
                  <mat-select [(value)]="size" (valueChange)="paginate(pageIndex, 1)">
                    <mat-option *ngFor='let opt of pgSizeOptions;' [value]="opt">{{opt}}</mat-option>
                  </mat-select>
                </mat-form-field>
                <span><span i18n>trên</span> {{countResult}} <span i18n>bản ghi</span></span>
              </div>
              <div class="control">
                <pagination-controls id="pgnx" (pageChange)="page = $event; paginate(page, 0)" responsive="true" previousLabel="" nextLabel="">
                </pagination-controls>
              </div>
            </div>
          </div>
        </ng-template>
      </mat-tab>
      <mat-tab label="Phân sổ tiếp nhận cho cơ quan" *ngIf="isShowSector_Agency">
        <button mat-flat-button class="btn_add" (click)="addSectorAgency()">
          <mat-icon>add</mat-icon>
          <span>Phân sổ tiếp nhận</span>
        </button>
        <div *ngIf="checkNullSectorAgencyData === 1 then nullSectorAgencyData; else hasSectorAgencyData"></div>
        <ng-template #nullSectorAgencyData>
          <br />
          <div fxLayout="row" fxLayoutAlign="center">
            <div fxFlex="grow" style="text-align: center; font-size: 18px">Không có kết quả tìm kiếm</div>
          </div>
        </ng-template>
        <ng-template #hasSectorAgencyData>
          <div class="frm_tbl_sector sector">
            <table mat-table [dataSource]="dataSource2">
              <ng-container matColumnDef="stt">
                <mat-header-cell *matHeaderCellDef i18n>STT</mat-header-cell>
                <mat-cell *matCellDef="let row" data-label="STT">{{row.stt}}</mat-cell>
              </ng-container>
              <ng-container matColumnDef="code">
                <mat-header-cell *matHeaderCellDef i18n>Mã sổ</mat-header-cell>
                  <mat-cell *matCellDef="let row" data-label="Mã lĩnh vực" class="cell_code" i18n-data-label> {{row.code}}
                </mat-cell>
              </ng-container>
              <ng-container matColumnDef="name">
                <mat-header-cell *matHeaderCellDef i18n>Tên sổ</mat-header-cell>
                <mat-cell *matCellDef="let row" data-label="Tên lĩnh vực" i18n-data-label>
                  <span #tooltip="matTooltip" matTooltip="{{row.name}}" mattooltipposition="above">{{row.name}}</span>
                </mat-cell>
              </ng-container>
              <ng-container matColumnDef="year">
                <mat-header-cell *matHeaderCellDef >Cơ quan</mat-header-cell>
                <mat-cell *matCellDef="let row" data-label="Cơ quan" >
                  <span *ngIf="!row.isExpanded">
                    {{row.agencyName}}
                  </span>
                  <div *ngIf="row.isExpanded">
                    <p *ngFor="let agency of row.agency">{{agency.name}}</p>
                    <span style="color: blue; font-style: italic; cursor: pointer;" (click)="row.isExpanded = !row.isExpanded">(Thu gọn)</span>
                  </div>
                </mat-cell>
              </ng-container>
              <ng-container matColumnDef="status">
                <mat-header-cell *matHeaderCellDef i18n>Trạng thái</mat-header-cell>
                <mat-cell *matCellDef="let row" data-label="Trạng thái" i18n-data-label> {{row.status}}
                </mat-cell>
              </ng-container>
              <ng-container matColumnDef="action">
                <mat-header-cell *matHeaderCellDef i18n>Thao tác</mat-header-cell>
                <mat-cell *matCellDef="let row" data-label="Thao tác" i18n-data-label>
                  <button mat-icon-button [matMenuTriggerFor]="actionMenu">
                    <mat-icon>more_horiz</mat-icon>
                  </button>
                  <mat-menu #actionMenu="matMenu" xPosition="before">
                    <button mat-menu-item class="menuAction" (click)="updateReceiptBook(row.id)">
                      <mat-icon>edit</mat-icon> <span i18n>Cập nhật</span>
                    </button>
                    <button mat-menu-item class="menuAction" (click)="deleteReceiptBook(row.id, row.name)">
                      <mat-icon>delete_outline</mat-icon> <span i18n>Xoá</span>
                    </button>
                  </mat-menu>
                </mat-cell>
              </ng-container>
              <mat-header-row *matHeaderRowDef="displayedColumns"></mat-header-row>
              <mat-row *matRowDef="let row; columns: displayedColumns;"></mat-row>
            </table>
            <div class="frm_Pagination">
              <ul class="temp_Arr">
                <li *ngFor="let item of ELEMENTDATA2  | paginate: {itemsPerPage: size2, currentPage: page2, totalItems: countResult2, id: 'pgnx2'}"></li>
              </ul>
              <div class="pageSize">
                <span i18n>Hiển thị</span>
                <mat-form-field appearance="outline">
                  <mat-select [(value)]="size2" (valueChange)="paginate2(pageIndex2, 1)">
                  <mat-option *ngFor='let opt of pgSizeOptions;' [value]="opt">{{opt}}</mat-option>
                </mat-select>
                </mat-form-field>
                <span><span i18n>trên</span> {{countResult2}} <span i18n>bản ghi</span></span>
              </div>
              <div class="control">
                <pagination-controls id="pgnx2" (pageChange)="page2 = $event; paginate2(page2, 0)" responsive="true" previousLabel="" nextLabel="">
                </pagination-controls>
              </div>
            </div>
          </div>
        </ng-template>
      </mat-tab>
      <mat-tab label="Phân lĩnh vực cho sổ tiếp nhận" *ngIf="isShowSector_User">
        <div class="applyAgencyDialogContent">
          <div style="display: flex; justify-content: space-between">
            <div class="prcAgency_tbl sector_user">
              <mat-form-field style="width: 100%;">
                <input matInput (keyup)="onSearchReceiptBook($event)" (keydown)="$event.stopPropagation()" placeholder="Tìm kiếm sổ tiếp nhận"/>
              </mat-form-field>
              <div class="prcAgenc_dialog_content" infiniteScroll [scrollWindow]="false" (scrolled)="getNextBatchReceiptBook()">
                <div class="listContainer" fxLayout="column" fxLayoutAlign="space-between left" fxFlex="100">
                  <mat-selection-list [multiple]="true" class="list" (selectionChange)="onReceiptBookSelectionChange($event)">
                    <mat-list-option *ngFor="let receiptBook of listReceiptBook"
                                      [value]="receiptBook"
                                      [selected]="isReceiptBookChecked(receiptBook.id)"
                                      (click)="checked(receiptBook.id)">
                                      {{ receiptBook?.name }}
                    </mat-list-option>
                  </mat-selection-list>
                </div>
              </div>
            </div>                                                                                          
            <div class="prcAgency_tbl sector_user">
              <mat-form-field style="width: 100%;">
                <input matInput (keyup)="onSearchSector($event)" (keydown)="$event.stopPropagation()" placeholder="Tìm kiếm lĩnh vực"/>
              </mat-form-field>             
              <div class="prcAgenc_dialog_content" infiniteScroll [scrollWindow]="false" (scrolled)="getNextBatchSector()">
                <div class="listContainer" fxLayout="column" fxLayoutAlign="space-between center" fxFlex="100">
                  <mat-selection-list [multiple]="true" class="list" (selectionChange)="onSelectSector($event)" style="width: 100%;">
                    <mat-list-option *ngFor="let sector of filterAgencySectors"
                                      [value]="sector"
                                      [selected]="isChecked(sector.id)"
                                      (click)="checked(sector)"
                                      [title]="sector.code + ' - ' + sector.name">
                      <div class="mat-list-text">                
                        {{ sector.code }} - {{ sector.name }}
                      </div>
                    </mat-list-option>
                  </mat-selection-list>
                </div>
              </div>
              <button mat-stroked-button type="button" (click)="toggleShowCheckedSectors()" style="margin-top: 10px;">
                {{ isShowingOnlyCheckedSectors ? 'Hiển thị tất cả lĩnh vực' : 'Hiển thị các lĩnh vực đã chọn' }}
              </button>              
            </div>
          </div>
        </div>
        <button mat-flat-button type="button" class="saveBtn" (click)="onConfirm2()">
          <span i18n>Lưu lại</span>
        </button>
      </mat-tab>
    </mat-tab-group>
  </div>
</div>