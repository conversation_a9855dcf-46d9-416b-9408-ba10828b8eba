<h2><PERSON>ra c<PERSON>u Log LGSP HCM LLTP VNeID</h2>
<div fxLayout="row" fxLayoutAlign="center">
    <div class="frm_searchbar" fxFlex="grow">
        <form [formGroup]="searchForm" (submit)="onConfirm()" class="searchForm">
            <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="row">
                <mat-form-field appearance="outline" fxFlex.gt-sm="43" fxFlex='45'>
                    <mat-label i18n="@@HCMLGSPLog_Keyword">Mã hồ sơ, tên hàm API</mat-label>
                    <input matInput formControlName="keyword" maxlength="200">
                </mat-form-field>
                <div fxFlex='1'></div>
                <mat-form-field appearance="outline"  fxFlex.gt-sm="18" fxFlex='20'>
                    <mat-label i18n="@@HCMLGSPLog_FromDate">Từ ngày</mat-label>
                    <input matInput [matDatepicker]="pickerAcceptFrom" formControlName="dateFrom" maxlength="20">
                    <mat-datepicker-toggle matSuffix [for]="pickerAcceptFrom"></mat-datepicker-toggle>
                    <mat-datepicker #pickerAcceptFrom></mat-datepicker>
                </mat-form-field>
                <div fxFlex='1'></div>
                <mat-form-field appearance="outline"  fxFlex.gt-sm="18" fxFlex='20'>
                    <mat-label i18n="@@HCMLGSPLog_ToDate">Đến ngày</mat-label>
                    <input matInput [matDatepicker]="pickerAcceptTo" formControlName="dateTo" maxlength="20">
                    <mat-datepicker-toggle matSuffix [for]="pickerAcceptTo"></mat-datepicker-toggle>
                    <mat-datepicker #pickerAcceptTo></mat-datepicker>
                </mat-form-field>
                
                <div fxFlex='1'></div>
                <button mat-flat-button fxFlex.gt-sm="20" fxFlex='20' class="searchBtn" type="submit">
                    <mat-icon>search</mat-icon> <span i18n>Tìm kiếm</span>
                </button>
            </div>
        </form>
        <div class="top-controlcancel" fxLayout="row" fxLayoutAlign="start">
            <mat-form-field appearance="outline" fxLayout="column" fxLayout.xs="column">
                <mat-label>Trạng thái</mat-label>
                <mat-select (selectionChange)="onReportTypeChange($event)">
                  <mat-option value='0'>Tất cả</mat-option>          
                  <mat-option value='1'>Thành công</mat-option>
                  <mat-option value='2'>Thất bại</mat-option>
                </mat-select>
              </mat-form-field>
            <button style = "margin-left: 10px; " mat-flat-button class="primary-btn" (click)="getListLGSPHCMExcel()" >
                <mat-icon style = "margin-right: 10px;">cloud_download</mat-icon>
                <span>Xuất excel</span>
            </button>
        </div>
    </div>
</div>
<div fxLayout="row" fxLayoutAlign="center" class="list-dossier-config">
    <div class="frm_main dossier-config" fxFlex="grow">
        <div class="frm_tbl">
            <table mat-table [dataSource]="dataSource">
                <ng-container matColumnDef="stt">
                    <mat-header-cell *matHeaderCellDef i18n>STT</mat-header-cell>
                    <mat-cell *matCellDef="let row"> {{row.stt}} </mat-cell>
                </ng-container>

                <ng-container matColumnDef="APIFunction">
                    <mat-header-cell *matHeaderCellDef i18n="@@HCMLGSPLog_APIFunction">Hàm API</mat-header-cell>
                    <mat-cell *matCellDef="let row"> {{row.apiFunction}}
                    </mat-cell>
                </ng-container>
                <ng-container matColumnDef="DossierCode">
                    <mat-header-cell *matHeaderCellDef i18n="@@HCMLGSPLog_DossierCode">Mã số hồ sơ</mat-header-cell>
                    <mat-cell *matCellDef="let row" style="color: #ce7a58"> {{row.dossierCode}}
                    </mat-cell>
                </ng-container>
                <ng-container matColumnDef="CallTime">
                    <mat-header-cell *matHeaderCellDef i18n="@@HCMLGSPLog_CallTime">Thời gian gọi</mat-header-cell>
                    <mat-cell *matCellDef="let row"> {{row.createdDate | date : 'dd/MM/yyyy HH:mm:ss'}}
                    </mat-cell>
                </ng-container>
                <ng-container matColumnDef="ResultName">
                    <mat-header-cell *matHeaderCellDef i18n="@@HCMLGSPLog_Result">Kết quả</mat-header-cell>
                    <mat-cell *matCellDef="let row" [style]="row.colorResult ? 'color: blue' : 'color: red'"> {{row.ResultName}}</mat-cell>
                </ng-container>
                <ng-container matColumnDef="action">
                    <mat-header-cell *matHeaderCellDef i18n>Thao tác</mat-header-cell>
                    <mat-cell *matCellDef="let row" data-label="Thao tác" i18n-data-label>
                        <button mat-icon-button (click)="viewDetail(row)">
                            <mat-icon>remove_red_eye</mat-icon>
                        </button>
                    </mat-cell>
                </ng-container>
                <mat-header-row *matHeaderRowDef="displayedColumns"></mat-header-row>
                <mat-row *matRowDef="let row; columns: displayedColumns;"></mat-row>
            </table>
            <div class="frm_Pagination">
                <ul class="temp_Arr">
                    <li
                        *ngFor="let item of ELEMENTDATA  | paginate: {itemsPerPage: size, currentPage: page, totalItems: countResult, id: 'pgnxPro'}">
                    </li>
                </ul>
                <div class="pageSize">
                    <span i18n>Hiển thị</span>
                    <mat-form-field appearance="outline">
                        <mat-select [(value)]="size"
                            (valueChange)="paginate(pageIndex, 1)">
                            <mat-option *ngFor='let opt of pgSizeOptions;' [value]="opt">{{opt}}</mat-option>
                        </mat-select>
                    </mat-form-field>
                    <span><span i18n>trên</span> {{countResult}} <span i18n>bản ghi</span></span>
                </div>
                <div class="control">
                    <pagination-controls id="pgnxPro"
                        (pageChange)="page = $event; paginate($event, 0)"
                        responsive="true" previousLabel="" nextLabel="">
                    </pagination-controls>
                </div>
            </div>
        </div>
    </div>
</div>