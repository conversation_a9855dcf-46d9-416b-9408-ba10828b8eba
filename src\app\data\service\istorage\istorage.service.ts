import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { ApiProviderService } from 'src/app/core/service/api-provider.service';

@Injectable({
  providedIn: 'root'
})
export class IstorageService {

  constructor(
    private http: HttpClient,
    private apiProviderService: ApiProviderService,
  ) { }

  private adapter = this.apiProviderService.getUrl('digo', 'adapter') + '/istorage/';

  getListTinhChatVatLyTriLieu(pageNo?): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.adapter + 'getListTinhChatVatLyTriLieu/?pageNo=' + pageNo, { headers });
  }

  getListTinhTrangVatLy(pageNo?): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.adapter + 'getListTtvl/?pageNo=' + pageNo, { headers });
  }

  getListThoiHanBaoHanh(pageNo?): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.adapter + 'getListThbq/?pageNo=' + pageNo, { headers });
  }

  getListTrangThaiHoSo(pageNo?): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.adapter + 'getListTrangThaiHoSo/?pageNo=' + pageNo, { headers });
  }

  getListLoaiVanBan(pageNo?): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.adapter + 'getListLoaiVanBan/?pageNo=' + pageNo, { headers });
  }

  getListMucDoTinCay(pageNo?): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.adapter + 'getListMucDoTinCay/?pageNo=' + pageNo, { headers });
  }

  getListDonVi(pageNo?): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.adapter + 'getListUnit/?pageNo=' + pageNo, { headers });
  }

  postListDeMuc(unitId,pageNo?): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.post<any>(this.adapter + 'getListDm/?id-don-vi='+ unitId + '&pageNo=' + pageNo , { headers });
  }

  postHoSo(requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.post<any>(this.adapter + 'saveHoSo/', requestBody , { headers });
  }

  postHoSoThanhPhan(requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.post<any>(this.adapter + 'saveHoSoThanhPhan/', requestBody , { headers });
  }

  updateStatuaSyncIStorage(id?): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.put(this.adapter + 'updateStatuaSyncIStorage/' +id, { headers });
  }
}
