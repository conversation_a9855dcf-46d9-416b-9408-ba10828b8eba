import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable } from 'rxjs';
import { ApiProviderService } from 'src/app/core/service/api-provider.service';
import { DeploymentService } from "data/service/deployment.service";

@Injectable({
  providedIn: 'root'
})
export class LanService {
  constructor(
    private http: HttpClient,
    private apiProviderService: ApiProviderService,
    private deploymentService: DeploymentService) {

  }
  env = this.deploymentService.getAppDeployment()?.env;

  private padman = this.apiProviderService.getUrl('digo', 'padman');
  private adapter = this.apiProviderService.getUrl('digo', 'adapter');

  getStatistisDvcLan(url): Observable<any> {
    let agencyIdProvince = "";
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    const token = localStorage.getItem('OAuth2TOKEN');
    headers = headers.append('Authorization', 'Bearer ' + token);
    return this.http.get(this.padman + "/api-lan/StatisticDvcLan?" + url, { headers });
  }
}
