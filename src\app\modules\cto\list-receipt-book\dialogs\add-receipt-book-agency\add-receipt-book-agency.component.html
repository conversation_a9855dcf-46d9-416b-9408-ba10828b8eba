<button mat-icon-button class="close-button" (click)="onDismiss()">
  <mat-icon>close</mat-icon>
</button>
<h3 class="dialog_title" mat-dialog-title><PERSON>ân sổ tiếp nhận cho cơ quan</h3>
<form [formGroup]="searchForm" class="searchForm edit">
    <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="column" class="marginRow">
        <mat-form-field appearance="outline" fxFlex.gt-sm="40" fxFlex.gt-xs="49.5" fxFlex='grow'>
            <mat-label i18n><PERSON><PERSON> quan thực hiện</mat-label>
            <mat-select formControlName="agency" #singleAgencyAcceptSelect
                msInfiniteScroll (infiniteScroll)="getListAgency()" [complete]="isFullListAgency == true" (selectionChange)="changeAgency()">
                <div>
                    <input matInput #searchAgency (keyup)="onEnter($event)" (keydown)="$event.stopPropagation()"
                        placeholder="Nhập từ khóa" class="search-nested" />
                    <button mat-icon-button *ngIf="searchAgency.value !== ''" (click)="searchAgency.value = ''; resetSearchForm()" class="clear-search-nested">
                        <mat-icon> close </mat-icon>
                    </button>
                </div>
                <mat-option value=''>
                    Tất cả
                </mat-option>
                <mat-option *ngFor="let agency of filteredAgencyAccept | async" value="{{agency.id}}">
                    {{agency.code}} - {{agency.name}}
                    <span *ngIf="agency.name == undefined || agency.name == null || agency.name.trim() == ''" i18n>(Không tìm thấy bản dịch)</span>
                </mat-option>
            </mat-select>
        </mat-form-field>
    </div>

    <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="column" class="marginRow" fxLayoutAlign="space-between">
        <div fxLayout="column" fxFlex.gt-xs="49" class = "listAllSector">
            <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="column" class="marginRow header-border-bottom"  fxLayoutAlign="space-between">
                <div class="headertable">
                    <span class="headertablechild">Danh mục sổ tiếp nhận của hệ thống</span>
                </div>
                <div >
                    <button  *ngIf="showSearch1 == false" mat-button  aria-label="Clear" (click)= "showSearch1 = true; click1()" class="header-button-search">
                        <mat-icon>search</mat-icon>
                        <span>Tìm kiếm</span>
                    </button>
                    <button  *ngIf="showSearch1 == true" mat-button matSuffix mat-icon-button aria-label="Clear" (click)= "showSearch1 = false; click1()">
                        <mat-icon>close</mat-icon>
                    </button>
                </div>
            </div>
            <mat-expansion-panel #panel1 [hideToggle]="!showSearch1">
                <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="column" class="marginRow" *ngIf="showSearch1 == true">             
                    <mat-form-field appearance="outline" fxFlex='grow'>
                        <mat-label i18n>Tìm kiếm</mat-label>
                        <input matInput [formControl]="sectorAcceptFilterCtrl" >
                        <button mat-button *ngIf="sectorAcceptFilterCtrl.value != ''" matSuffix mat-icon-button aria-label="Clear" (click)= "sectorAcceptFilterCtrl.setValue('')">
                            <mat-icon>close</mat-icon>
                        </button>
                    </mat-form-field>
                </div>
            </mat-expansion-panel>
            <div fxLayout="column" class="contentItem" [scrollWindow]="false" infiniteScroll [infiniteScrollDistance]="0.5" (scrolled)="getListReceiptBook()">
                <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="column" fxLayoutAlign="space-between" class="header-border-bottom" *ngFor="let item of filteredSectorAccept | async">
                    <div class="headeritem" *ngIf ="addSectorCode">
                        <span class="headeritemchild" style="white-space:nowrap">[{{item.code}}] {{item.name}}</span>
                    </div>
                    <div class="headeritem" *ngIf ="!addSectorCode">
                        <span class="headeritemchild" style="white-space:nowrap">{{item.name}}</span>
                    </div>
                    <div class="headeritemicon">
                        <button  mat-button (click) = "addItemToList(item.id)" aria-label="Clear" class="header-button-search header-button-search-item" >
                            <mat-icon>add</mat-icon>
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <!-- thông tin -->

        <div fxLayout="column" fxFlex.gt-xs="49" class = "listAllSector">
            <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="column" class="marginRow header-border-bottom"  fxLayoutAlign="space-between">
                <div class="headertable">
                    <span class="headertablechild">Danh mục sổ tiếp nhận của cơ quan</span>
                </div>
                <div>
                    <button  *ngIf="showSearch2 == false" mat-button  aria-label="Clear" (click)= "showSearch2 = true; click2()" class="header-button-search">
                        <mat-icon>search</mat-icon>
                        <span>Tìm kiếm</span>
                    </button>
                    <button  *ngIf="showSearch2 == true" mat-button matSuffix mat-icon-button aria-label="Clear" (click)= "showSearch2 = false; click2()">
                        <mat-icon>close</mat-icon>
                    </button>
                </div>
            </div>
            <mat-expansion-panel #panel2 [hideToggle]="!showSearch2">
                <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="column" class="marginRow" *ngIf="showSearch2 == true">             
                    <mat-form-field appearance="outline" fxFlex='grow'>
                        <mat-label i18n>Tìm kiếm</mat-label>
                        <input matInput [formControl]="sectorAgencyAcceptFilterCtrl" maxlength="100">
                        <button mat-button *ngIf="sectorAgencyAcceptFilterCtrl.value != ''" matSuffix mat-icon-button aria-label="Clear" (click)= "sectorAgencyAcceptFilterCtrl.setValue('')">
                            <mat-icon>close</mat-icon>
                        </button>
                    </mat-form-field>
                </div>
            </mat-expansion-panel>
            <div fxLayout="column" class="contentItem">
                <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="column" fxLayoutAlign="space-between" class="header-border-bottom" *ngFor="let item of filteredSectorAgencyAccept | async">
                    <div class="headeritem">
                        <span class="headeritemchild" style="white-space:nowrap">[{{item.code}}] {{item.name}}</span>
                    </div>
                    <div class="headeritemicon">
                        <button  mat-button (click)= "deleteItemToList(item.id)" aria-label="Clear" class="header-button-search header-button-search-item" >
                            <mat-icon>delete</mat-icon>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <br>
    <div fxLayout="row" fxLayoutAlign="center" fxLayout.xs="row" fxLayout.sm="row">
        <button mat-flat-button fxFlex='grow' class="searchBtn" (click)="save()">
            <span i18n>Lưu lại</span>
        </button>
    </div>
</form>