import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { ApiProviderService } from 'src/app/core/service/api-provider.service';
import { EnvService } from 'src/app/core/service/env.service';

@Injectable({
  providedIn: 'root'
})
export class LedgerAuthService {

  constructor(
    private http: HttpClient,
    private envService: EnvService,
    private apiProviderService: ApiProviderService,
  ) { }

  private searchUrls = this.apiProviderService.getUrl('digo', 'basepad') + '/ledger-auth/--search';
  private postUrls = this.apiProviderService.getUrl('digo', 'basepad') + '/ledger-auth';
  private getUpdateStatusUrls(id: string){
    return this.apiProviderService.getUrl('digo', 'basepad') + `/ledger-auth/${id}/--status`;
  }
  private getDetailsUrls(id: string){
    return this.apiProviderService.getUrl('digo', 'basepad') + `/ledger-auth/${id}`;
  }
  private getUpdateUrls(id: string){
    return this.apiProviderService.getUrl('digo', 'basepad') + `/ledger-auth/${id}`;
  }
  private getNextValueUrls(id: string){
    return this.apiProviderService.getUrl('digo', 'basepad') + `/ledger-auth/${id}/--next-index`;
  }

  private getPutNextValueUrls(id: string){
    return this.apiProviderService.getUrl('digo', 'basepad') + `/ledger-auth/${id}/--push-index`;
  }


  search(query): Observable<any> {
    const endpoint = this.searchUrls + (!!query ? '' + query : '');
    return this.http.get(endpoint);
  }

  post(body: any): Observable<any>{
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    return this.http.post<any>(this.postUrls, body, { headers });
  }

  update(id: string, body: any): Observable<any>{
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    return this.http.put<any>(this.getUpdateUrls(id), body, { headers });
  }

  updateStatus(id: string): Observable<any>{
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    return this.http.put<any>(this.getUpdateStatusUrls(id), '', { headers });
  }

  details(id: string): Observable<any>{
    return this.http.get(this.getDetailsUrls(id));
  }

  getNextValue(id: string): Observable<any>{
    return this.http.get(this.getNextValueUrls(id));
  }

  pushNextValue(id: string): Observable<any>{
    return this.http.get(this.getPutNextValueUrls(id));
  }
}
