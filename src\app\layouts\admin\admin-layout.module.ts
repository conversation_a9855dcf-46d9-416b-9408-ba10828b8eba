import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AdminLayoutComponent } from './admin-layout/admin-layout.component';
import { AdminLayoutNavComponent } from './admin-layout-nav/admin-layout-nav.component';
import { RouterModule } from '@angular/router';
import { SharedModule } from 'src/app/shared/shared.module';
import { QuickSearchComponent } from '../dialogs/quick-search/quick-search.component';
import { RingMenuDetail } from '../dialogs/ring-menu-detail/view-detail.component';
import { environment } from 'env/environment';
// import * as firebase from 'firebase/app';
import { AnnouncementPopupComponent } from 'src/app/modules/hcm/announcement/dialogs/announcement-popup/announcement-popup.component';

// if (!!environment && !!environment.firebase){
//   firebase.initializeApp(environment.firebase);
// }

@NgModule({
  declarations: [
    AdminLayoutComponent,
    AdminLayoutNavComponent,
    QuickSearchComponent,
    RingMenuDetail,
    AnnouncementPopupComponent
  ],
  imports: [
    CommonModule,
    RouterModule,
    SharedModule
  ],
  exports: [
    AdminLayoutComponent,
    AdminLayoutNavComponent,
    QuickSearchComponent,
    RingMenuDetail,
    AnnouncementPopupComponent
  ]
})
export class AdminLayoutModule { }
