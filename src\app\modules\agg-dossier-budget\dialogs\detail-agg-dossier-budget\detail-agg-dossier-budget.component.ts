import { Component, OnInit, Inject, ViewChild } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { AdapterService } from 'src/app/data/service/svc-adapter/adapter.service';
import { MatPaginator, PageEvent } from '@angular/material/paginator';
import { MatTableDataSource } from '@angular/material/table';
import { SnackbarService } from 'src/app/data/service/snackbar/snackbar.service';
import { EnvService } from 'src/app/core/service/env.service';

@Component({
  selector: 'app-detail-agg-dossier-budget',
  templateUrl: './detail-agg-dossier-budget.component.html',
  styleUrls: ['./detail-agg-dossier-budget.component.scss']
})
export class DetailAggDossierBudgetComponent implements OnInit {
  @ViewChild(MatPaginator) paginator: MatPaginator;
  config = this.envService.getConfig();
  hsid = '';
  pageIndex = 0;
  size = 10;
  page = 1;
  countResult = 0;
  pgSizeOptions = this.config.pageSizeOptions;
  budgetDossierDetail: any;
  ELEMENTDATADL2: any[] = [];
  dataSourceDL2: MatTableDataSource<any>;
  displayedColumnsDL2: string[] = ['NGAY_XL', 'TRANG_THAI_TEN', 'CAN_BO_XL', 'DON_VI_XL', 'NOI_DUNG_XL'];
  selectedLang = localStorage.getItem('language') || 'vi';
  selectedLangId = Number(localStorage.getItem('languageId')) || 228;
  constructor(
    public dialogRef: MatDialogRef<DetailAggDossierBudgetComponent>,
    @Inject(MAT_DIALOG_DATA) public data: DetailAggDossierBudgetComponentDialog,
    private adapterService: AdapterService,
    private snackbarService: SnackbarService,
    private envService: EnvService,
  ) {
    this.hsid = data.hsid;
  }

  ngOnInit(): void {
    this.onDetail();
    this.updateDataSource();
  }

  onDismiss() {
    this.dialogRef.close();
  }

  onConfirm() {
    this.dialogRef.close(true);
  }

  onDetail(){
    this.adapterService.getBudgetDossierDetailAgg(this.hsid).subscribe(data => {
      this.budgetDossierDetail = data;
      this.ELEMENTDATADL2 = data.row.DATA_PROCESS;
      // Sort ELEMENTDATADL2 based on NGAY_XL
      this.ELEMENTDATADL2.sort((a, b) => a.NGAY_XL.localeCompare(b.NGAY_XL));
      this.dataSourceDL2 = new MatTableDataSource<any>(this.ELEMENTDATADL2);
      this.dataSourceDL2.paginator = this.paginator; // Set paginator after assigning data source
      this.countResult = this.ELEMENTDATADL2.length;
      this.updateDataSource();
    }, err => {
      let msgObj = err.error.message || 'Lỗi không xác định';
      if (msgObj.includes('java.lang.NullPointerException')) {
        msgObj = {
          vi: 'Không tìm thấy hồ sơ',
          en: 'Dossier not found'
        };
        this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
        return;
      }
      this.snackbarService.openSnackBar(0, msgObj, '', 'error_notification', this.config.expiredTime);
    });
  }
  // Phân trang
  paginate(event: any, type) {
    switch (type) {
      case 0:
        this.pageIndex = event;
        this.pageIndex = this.pageIndex - 1;
        this.updateDataSource();
        break;
      case 1:
        this.pageIndex = 1;
        this.pageIndex = this.pageIndex - 1;
        this.updateDataSource();
        break;
    }
  }
  updateDataSource() {
    const startIndex = this.pageIndex * this.size;
    const endIndex = startIndex + this.size;
    const slicedData = this.ELEMENTDATADL2.slice(startIndex, endIndex);
    this.dataSourceDL2 = new MatTableDataSource<any>(slicedData);
  }
}

export class DetailAggDossierBudgetComponentDialog {
  constructor(
    public hsid: string
    ) {
  }
}