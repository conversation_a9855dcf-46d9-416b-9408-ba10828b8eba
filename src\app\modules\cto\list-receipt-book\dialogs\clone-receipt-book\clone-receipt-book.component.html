<div class="cloneLedger">
    <button mat-icon-button class="closeBtn" (click)="onDismiss()">
        <mat-icon>close</mat-icon>
    </button>
    <h3 class="dialog_title" mat-dialog-title> 
        <span><PERSON><PERSON> chép sổ tiếp nhận</span>
    </h3>
    <form [formGroup]="receiptBook" (submit)="onSubmit()">
        <div fxLayout="row" fxLayout.xs="column" fxLayoutAlign="space-between">
            <mat-form-field appearance="outline" fxFlex="33" fxFlex.xs="100">
                <mat-label>Tê<PERSON> sổ</mat-label>
                <input matInput formControlName="name" required>
            </mat-form-field>
            <mat-form-field appearance="outline" fxFlex="33" fxFlex.xs="100">
                <mat-label>Mã sổ</mat-label>
                <input matInput formControlName="code" required>
            </mat-form-field>
            <mat-form-field appearance="outline" fxFlex="33" fxFlex.xs="100">
                <mat-label>Năm</mat-label>
                <input matInput formControlName="year" maxlength="4" pattern="^[0-9]{4}$" required (keypress)="preventInvalidKeys($event)">
                <mat-error *ngIf="receiptBook.get('year')?.hasError('required')">
                    Năm là bắt buộc
                  </mat-error>
                  <mat-error *ngIf="receiptBook.get('year')?.hasError('pattern')">
                    Năm phải gồm 4 chữ số
                  </mat-error>
            </mat-form-field>
        </div>            
        <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="column" fxLayoutAlign="center">
            <button mat-button class="addBtn" type="submit" [disabled]="receiptBook.invalid">
                Đồng ý
            </button>
        </div>
    </form>    
</div>