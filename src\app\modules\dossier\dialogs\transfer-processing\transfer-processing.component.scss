mat-dialog-container #mat-dialog-0 {
  overflow: hidden !important;
}
::ng-deep .cdk-overlay-pane {
    top: 30% !important;
}
.mat-dialog-content {
    max-height: 55vh !important;
    overflow: auto !important;
}

::ng-deep .custom_mat_checkbox .mat-checkbox-frame {
    border-radius: 50px;
    transform: scale(1.3);
}

::ng-deep .custom_mat_checkbox.mat-checkbox-checked .mat-checkbox-background,
::ng-deep .custom_mat_checkbox.mat-checkbox-indeterminate .mat-checkbox-background {
    border-radius: 50px;
    transform: scale(1.3);
}

.tree_node {
    display: block !important;
    margin-bottom: 20px;
}

.dialog_title {
    font-style: normal;
    font-weight: 500;
    font-size: 20px;
    color: #ce7a58;
}

::ng-deep .applyBtn {
    margin-top: 1em;
    background-color: #ce7a58;
    color: #fff;
    height: 3em;
    padding-left: 2em;
    padding-right: 2em;

    .mat-button-wrapper {
        display: flex;
        justify-content: center;

        .mat-spinner {
            margin-right: 0.3em;
            align-self: center;
            circle {
                stroke: #ce7a58;
            }
        }
    }
}

::ng-deep mat-dialog-container::-webkit-scrollbar-track {
    box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.1);
    background-color: #f5f5f5;
}

::ng-deep mat-dialog-container::-webkit-scrollbar {
    width: 5px;
    background-color: #f5f5f5;
}

::ng-deep mat-dialog-container::-webkit-scrollbar-thumb {
    box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.1);
    background-color: #44444450;
}

.close-button {
    float: right;
    top: -24px;
    right: -24px;
}

::ng-deep {
    .mat-primary {
        .mat-option.mat-selected:not(.mat-option-disabled) {
            color: #ce7a58;
        }

        .mat-pseudo-checkbox-checked {
            background: #ce7a58;
        }

        .mat-pseudo-checkbox-indeterminate {
            background: #ce7a58;
        }
    }

    .dialog_content {
        font-size: 15px;
        max-height: unset;

        .highlight {
            color: #ce7a58;
        }

        .lbl {
            font-weight: 500;
            font-size: 16px;
            line-height: 27px;
            color: #1e2f41;
        }

        .branch {
            .formFieldOutline {
                .mat-form-field-wrapper {
                    padding-bottom: unset !important;
                    color: #1e2f41;

                    .mat-select-disabled {
                        .mat-select-value {
                            color: #1e2f41;
                            cursor: no-drop !important;
                        }
                    }
                }

                .assigneeBlock {
                    border-top: 1px solid #dadada;
                    padding-top: 0.5em;
                    margin-top: 0.5em;
                }
            }

            .ngwRequirement {
                .mat-icon {
                    vertical-align: middle;
                    color: #ce7a58;
                    margin-top: -0.1em;
                    margin-right: 0.2em;
                }
            }

            .agencyArray {
                flex-wrap: wrap;
            }

            .tblBranch {
                width: 100%;
                border-bottom: 1px solid #dadada;
                padding-bottom: 1em;
                margin-bottom: 1em;

                .hidden {
                    display: none;
                }

                tr {
                    &:nth-child(1) {
                        border-top: unset;
                    }

                    border-top: 0.1px solid #dadada;
                    padding-top: 0.5em;
                    margin-top: 0.5em;
                    display: flex;

                    th {
                        text-align: left;
                        font-weight: 500;
                        color: #1e2f41;
                    }

                    .chkLine {
                        padding: 0.5em 0;
                    }

                    td {
                        vertical-align: top;
                        display: flex;

                        span {
                            align-self: center;
                        }

                        button {
                            align-self: center;
                        }

                        .mat-checkbox {
                            align-self: center;

                            span {
                                width: 95%;
                                white-space: pre-wrap;
                            }
                        }

                        .requirement {
                            vertical-align: middle;
                            color: #ce7a58;
                            margin-top: -0.1em;
                            margin-right: 0.2em;
                        }

                        .update {
                            vertical-align: middle;
                            color: #858585;
                        }

                        .mat-radio-group {
                            display: flex;
                            flex-direction: column;
                            padding: 0 0.5em 0 1.7em;
                        }

                        .mat-radio-button {
                            margin-bottom: 5px;
                            align-self: center;

                            .mat-radio-inner-circle {
                                background-color: #ce7a58;
                            }

                            &.mat-accent.mat-radio-checked {
                                .mat-radio-outer-circle {
                                    border-color: #ce7a58;
                                }
                            }
                        }

                        .mat-checkbox-indeterminate.mat-accent .mat-checkbox-background,
                        .mat-checkbox-checked.mat-accent .mat-checkbox-background {
                            background-color: #ce7a58;
                        }
                    }
                }
            }
        }

        .commentEditor {
            margin: 1em 0;

            .editorLabel {
                font-weight: 500;
                color: #1e2f41;
                margin-bottom: 0.2em;
            }

            .customCKEditor {
                .ck.ck-toolbar {
                    border-radius: 4px 4px 0 0;
                    border: 1px solid #dedede;
                }

                .ck.ck-content {
                    background-color: #eaebeb;
                    border: none;
                    border-radius: 0 0 4px 4px;
                }

                .ck-editor__editable {
                    min-height: 5em !important;
                }
            }

            .errorMsg {
                font-size: 12px !important;
                float: right;
                display: flex;
                color: #ce7a58;
                margin: 0.5em 0;

                span {
                    margin-left: auto !important;
                }

                .err {
                    background-color: #f2a63494;
                    border-radius: 50%;
                    width: 1.2em;
                    height: 1.2em;
                    justify-content: center;
                    display: flex;
                    margin-left: 0.5em;
                    margin-top: 0.2em;

                    .mat-icon {
                        color: #ce7a58;
                        vertical-align: middle;
                        align-self: center;
                        transform: scale(0.6);
                        margin-left: 0.05em;
                    }
                }
            }
        }

        .sendEmailSMS {
            border-top: 1px solid #dadada;
            .chkGroup {
                margin: 1em 0;
                display: flex;
                flex-direction: column;
                .mat-checkbox-indeterminate.mat-accent .mat-checkbox-background,
                .mat-checkbox-checked.mat-accent .mat-checkbox-background {
                    background-color: #ce7a58;
                }
            }

            .chkEdit{
                background: url(src/assets/img/edit-field-icon.svg);
                background-repeat: no-repeat;
                background-size: auto;
                background-position: center;
                width: 24px;
                height: 24px;
                border: none;
                cursor: pointer;
            }

            .phoneAndEmail{
                color: #CE7A58;
            }

            .mat-form-field {
                width: 100%;

                .textLimit {
                    float: right;
                    padding-top: 0.2em;
                }
            }
        }
    }

    //KGG Outsource CSS
    .cardContent {
      color: #1e2f41;
      padding: 0.5em;
      border: 1px solid #e2e2e2;
      height: 85%;
      border-radius: 0 0 4px 4px;
      .mat-icon {
          vertical-align: middle;
          margin-right: 0.2em;
          color: #666;
      }
      p .lbl {
          font-weight: 500;
      }
      p .status {
          color: #fff;
          font-weight: 500;
          padding: 0.25em 1em;
          border-radius: 15px;
      }
      .tblInfo {
          width: 100%;
          tr {
              td:nth-child(1),
              td:nth-child(3) {
                  font-weight: 500;
                  padding: 0.3em 0;
              }
          }
      }
      .drag_upload_btn {
          position: relative;
          overflow: hidden;
          display: inline-block;
          width: 100%;
          min-height: 3em;
          text-align: center;
          align-self: center;
          cursor: pointer;
          z-index: 4;
          button {
              width: 100%;
              padding: 0.2em;
              .mat-icon {
                  margin-right: 0.3em;
                  color: #ce7a58;
              }
              a {
                  text-decoration: none;
                  font-weight: 500;
                  cursor: pointer;
                  &:hover {
                      color: #ce7a58;
                  }
                  &:visited {
                      color: #ce7a58;
                  }
              }
          }
          input[type="file"] {
              position: absolute;
              left: 0;
              top: 0;
              opacity: 0;
              height: 100%;
              width: 100%;
              cursor: pointer;
          }
          .fileUploadPreview {
              width: 100%;
              display: flex;
              flex-wrap: wrap;
              justify-content: left;
              .listUploaded {
                  display: flex;
                  height: 4em;
                  width: 100%;
                  margin: 0.5em 0;
                  .fileInfo {
                      height: 100%;
                      width: 100%;
                      margin: 0 0.5% 1em 0.5%;
                      background-color: #3e3e3e17;
                      border-radius: 4px;
                      display: flex;
                      .fileIcon {
                          width: 2.5em;
                          height: 2.5em;
                          background-position: center;
                          background-size: 100%;
                          align-self: center;
                          margin-left: 1em;
                          background-repeat: no-repeat;
                      }
                      .deleteFile {
                          margin-left: auto;
                          align-self: center;
                          color: #1e2f41;
                      }
                      .dGrid {
                          text-align: left;
                          align-self: center;
                          margin: 0 1em;
                          width: 75%;
                          .fileName {
                              color: #1e2f41;
                              font-weight: 500;
                              margin: 0;
                              width: 80%;
                              white-space: nowrap;
                              overflow: hidden;
                              text-overflow: ellipsis;
                          }
                          .fileSize {
                              color: #1e2f41;
                              margin: 0;
                              width: 80%;
                          }
                      }
                  }
                  .moreBtn {
                      align-self: center;
                      color: #1e2f41;
                  }
              }
          }
      }
      .countdownDate {
          display: inline-flex;
          .count-down {
              padding: 0 0.5em !important;
              .count-down-row:nth-child(1) {
                  div {
                      width: unset !important;
                      padding-right: 0.5em;
                      span {
                          letter-spacing: 0;
                          font-size: 15px;
                          color: #1e2f41;
                          text-align: center;
                          font-weight: 500;
                      }
                  }
              }
              .count-down-row:nth-child(2) {
                  display: none;
              }
          }
          &.transVI {
              .count-down {
                  .count-down-row:nth-child(1) {
                      div:nth-child(1)::after {
                          content: "ngày";
                      }
                      div:nth-child(2)::after {
                          content: "giờ";
                      }
                      div:nth-child(3)::after {
                          content: "phút";
                      }
                      div:nth-child(4)::after {
                          content: "giây";
                      }
                  }
              }
          }
          &.transEN {
              .count-down {
                  .count-down-row:nth-child(1) {
                      div:nth-child(1)::after {
                          content: "day";
                      }
                      div:nth-child(2)::after {
                          content: "hour";
                      }
                      div:nth-child(3)::after {
                          content: "minute";
                      }
                      div:nth-child(4)::after {
                          content: "second";
                      }
                  }
              }
          }
      }
      .overdue {
          color: #de1212;
      }
      .timer {
          padding: 0.3em 0.5em;
          background-color: #ececec;
          border-radius: 4px;
          margin-right: 0.2em;
      }
      .timeNumber {
          letter-spacing: 0;
          font-size: 15px;
          text-align: center;
          font-weight: 500;
      }
  }

  .lbl {
      font-weight: 500;
      font-size: 16px;
      line-height: 27px;
      color: #1e2f41;
  }

  .cardAction {
      align-self: center;
      color: #ce7a58;
      margin-left: auto;
      .mat-icon {
          margin-right: 0.3em;
      }
      .mat-spinner {
          display: none;
          margin-right: 1em;
          circle {
              stroke: #ce7a58;
          }
      }
      .done {
          display: none;
          background-color: #fff;
          padding: 0.3em 0.5em;
          border-radius: 30px;
          .mat-icon {
              vertical-align: middle;
          }
          span {
              font-size: 14px;
              vertical-align: middle;
          }
      }
  }
  //END KGG OS CSS
}

@media screen and (max-width: 600px) {
    .tblBranch {
        border: 0;
        border-bottom: unset !important;

        tr {
            border-bottom: 1px solid #ddd;
            display: block;

            .chkLine {
                text-align: left;
            }
        }

        th {
            display: none;
            &.hidden {
                display: none !important;
            }
        }

        td {
            padding: 0.5em 0;
            border-bottom: 1px solid #ddd;
            display: block;
            text-align: right;

            &.hidden {
                display: none !important;
            }

            &::before {
                content: attr(data-label);
                float: left;
                font-weight: 500;
            }

            &:last-child {
                border-bottom: 0;
            }
        }
    }
}
::ng-deep app-processing-info form{
  margin-top: 10px;
}
.hiddenHGI{
    display: none !important;
}

.step-compact {
  min-height: 0 !important;
  padding-top: 0 !important;
  padding-bottom: 0 !important;
}
.checklist-leaf-node {
  margin-top: 2px !important;
  margin-bottom: 2px !important;
  padding-top: 0 !important;
  padding-bottom: 0 !important;
}
