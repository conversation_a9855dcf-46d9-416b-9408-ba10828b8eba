import { Component, Inject, OnInit } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { FormService } from 'src/app/data/service/form/form.service';
import { BlockingDossierService } from 'data/service/qnm-blocking-dossier/blocking-dossier.service';

@Component({
  selector: 'app-delete-blocking-dossier',
  templateUrl: './delete-dossier.component.html',
  styleUrls: ['./delete-dossier.component.scss']
})
export class DeleteDossierComponent implements OnInit {
  objectId: string;
  objectName: string;
  constructor(public dialogRef: MatDialogRef<DeleteDossierComponent>,
              @Inject(MAT_DIALOG_DATA) public data: ConfirmDeleteDialogModel,
              private formService: FormService,
              private blockingDossierService: BlockingDossierService) {
      this.objectId = data.id;
      this.objectName = data.name;
  }

      ngOnInit(): void {
      }

      onConfirm() {
        this.blockingDossierService.deleteBlockingDossier(this.objectId).subscribe(data => {
          this.dialogRef.close(true);
        }, err => {
          this.dialogRef.close(false);
        });
      }

      onDismiss() {
        this.dialogRef.close();
      }

}
export class ConfirmDeleteDialogModel {
  constructor(public id: string, public name: string) {
  }
}
