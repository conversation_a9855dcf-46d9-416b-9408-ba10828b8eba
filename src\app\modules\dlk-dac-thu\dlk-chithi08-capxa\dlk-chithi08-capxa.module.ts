import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';

import { DlkChithi08CapxaRoutingModule } from './dlk-chithi08-capxa-routing.module';

import { NgxPrinterModule } from 'ngx-printer';
import { SharedModule } from 'src/app/shared/shared.module';
import { DlkChithi08CapxaComponent } from './dlk-chithi08-capxa.component';


@NgModule({
  declarations: [DlkChithi08CapxaComponent],
  imports: [
    CommonModule,
    DlkChithi08CapxaRoutingModule,
    SharedModule,
    NgxPrinterModule.forRoot({ printOpenWindow: true })
  ]
})
export class DlkChithi08CapxaModule { }
