import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { DlkHoSoTreHanComponent } from './dlk-hosotrehan.component';
import { SharedModule } from 'src/app/shared/shared.module';
import { NgxPrinterModule } from 'ngx-printer';
import { DlkHoSoTreHanRoutingModule } from './dlk-hosotrehan-routing.module';


@NgModule({
  declarations: [DlkHoSoTreHanComponent],
  imports: [
    CommonModule,
    DlkHoSoTreHanRoutingModule,
    SharedModule,
    NgxPrinterModule.forRoot({ printOpenWindow: true })
  ]
})
export class DlkHoSoTreHanModule { }
