<div fxLayout="row" fxLayoutAlign="space-between" fxLayout.sm="column" fxLayout.xs="column">
    <!-- <div fxFlex="25" fxFlex.sm="100" fxFlex.xs="100">
        <div (click)="onClickOpenReminderMenu()" mat-button class="title-reminder" fxLayout="row" fxLayoutAlign="space-between">
            <div class="content">
                <span fxFlex="100" class="title"><span i18n>Danh sách công việc</span> (<a class="count-task">{{lengthRemind}}</a>)</span>
                <mat-icon fxFlex="10">expand_more</mat-icon>
            </div>
        </div>
        <div class="menu_reminder">
            <mat-accordion class="advanced-box" multi>
                <mat-expansion-panel class="panel" *ngIf="expandReminderMenu" [(expanded)]="expandReminderMenu" [ngStyle]="{'height': xpandStatus ? '28rem' : '8rem' }">
                    <span *ngFor="let remind of listMenuRemind;">
                        <a id="submenu" mat-button active-link="active" [ngClass]="{ 'active': remindId === remind.id }" (click)="changeSearchRemind(remind.id)">
                            <mat-icon>receipt</mat-icon><span class="submenuTitle">{{remind.name}}</span>&nbsp;<span class="count">{{remind.count}}</span>
                    </a>
                    </span>
                </mat-expansion-panel>
            </mat-accordion>
        </div>
    </div> -->
    <div fxFlex="100" fxFlex.sm="100" fxFlex.xs="100" class="search">
        <h2>Thu phí, lệ phí hồ sơ</h2>
        <div class="prc_searchbar">
            <form [formGroup]="searchForm" (submit)="onConfirmSearch()" class="searchForm">
                <div fxLayout="row" fxLayout.xs="row" fxLayout.sm="row" class="formFieldItems" fxLayoutAlign="space-between">
                    <mat-form-field appearance="outline" fxFlex.gt-sm="24" fxFlex.gt-xs="49.5" fxFlex='grow'>
                        <mat-label i18n>Mã số hồ sơ</mat-label>
                        <input type="text" matInput formControlName="code">
                    </mat-form-field>
                    <!-- <mat-form-field appearance="outline" fxFlex.gt-sm="24" fxFlex.gt-xs="49.5" fxFlex='grow'>
                        <mat-label i18n>Cơ quan</mat-label>
                        <mat-select formControlName="advAgency" (selectionChange)="agencyChange($event)" msInfiniteScroll (infiniteScroll)="getListAgency()" [complete]="isFullListAgency == true">
                            <mat-option value=""><span i18n>Tất cả</span></mat-option>
                            <mat-option *ngFor='let agency of listAgency' value="{{agency.id}}">
                                {{agency.name}}
                                <span *ngIf="agency.name == undefined || agency.name == null || agency.name.trim() == ''" i18n>(Không tìm thấy bản dịch)</span>
                            </mat-option>
                        </mat-select>
                    </mat-form-field> -->

                        <mat-form-field appearance="outline" fxFlex.gt-sm="24" fxFlex.gt-xs="49.5" fxFlex='grow'>
                            <mat-label>Cơ quan</mat-label>
                            <mat-select msInfiniteScroll (infiniteScroll)="getNextBatch('agencyapprovalAgency')"
                                [complete]="totalPagesAgencyapprovalAgency <= currentPageAgencyapprovalAgency+1"
                                formControlName="advAgency">
                                <div>
                                    <div>
                                        <input matInput #searchInputAgencyapprovalAgency
                                            (keyup)="onEnter('agencyapprovalAgency', $event)" (keydown)="$event.stopPropagation()"
                                            placeholder="Nhập từ khóa" class="search-nested" />
                                        <button mat-icon-button class="clear-search-nested"
                                            *ngIf="searchInputAgencyapprovalAgency.value !== ''"
                                            (click)="searchInputAgencyapprovalAgency.value = ''; resetaddForm('agencyapprovalAgency')">
                                            <mat-icon> close </mat-icon>
                                        </button>
                                    </div>
                                </div>
                                <!-- <mat-option value="">Tất cả</mat-option> -->
                                <mat-option value=""><span i18n>Tất cả</span></mat-option>
                                <mat-option *ngFor="let item of listAgencyapprovalAgency" [value]="item.id">{{item.fullName}}</mat-option>
                            </mat-select>
                        </mat-form-field>
                    <mat-form-field appearance="outline" fxFlex.gt-sm="24" fxFlex.gt-xs="49.5" fxFlex='grow'>
                        <mat-label i18n>Tiếp nhận từ ngày</mat-label>
                        <input matInput [matDatepicker]="pickerAcceptFrom" formControlName="advAcceptFrom">
                        <mat-datepicker-toggle matSuffix [for]="pickerAcceptFrom"></mat-datepicker-toggle>
                        <mat-datepicker #pickerAcceptFrom></mat-datepicker>
                    </mat-form-field>
                    <mat-form-field appearance="outline" fxFlex.gt-sm="24" fxFlex.gt-xs="49.5" fxFlex='grow'>
                        <mat-label i18n>Tiếp nhận đến ngày</mat-label>
                        <input matInput [matDatepicker]="pickerAcceptTo" formControlName="advAcceptTo">
                        <mat-datepicker-toggle matSuffix [for]="pickerAcceptTo"></mat-datepicker-toggle>
                        <mat-datepicker #pickerAcceptTo></mat-datepicker>
                    </mat-form-field>
                    <mat-form-field appearance="outline" fxFlex='24.25' fxFlex.gt-sm="26" fxFlex.gt-xs="49.5" fxFlex='grow'>
                        <mat-label>Trạng thái thu</mat-label>
                        <mat-select formControlName="chargeFees">
                            <mat-option value="">Tất cả</mat-option>
                            <mat-option value="1">Đã thu</mat-option>
                            <mat-option value="0">Chưa thu</mat-option>
                        </mat-select>
                    </mat-form-field>
                    <mat-form-field appearance="outline" fxFlex='24.25' fxFlex.gt-sm="26" fxFlex.gt-xs="49.5" fxFlex='grow'>
                        <mat-label>Hình thức thu</mat-label>
                        <mat-select formControlName="typechargeFees">
                            <mat-option value="">Tất cả</mat-option>
                            <mat-option value="0">Tiền mặt</mat-option>
                            <mat-option value="1">Chuyển khoản</mat-option>
                        </mat-select>
                    </mat-form-field>
                    <app-button-scanner-barcode *ngIf="isScanCode"
                    fxFlex.gt-sm="12" fxFlex.gt-md="12" fxFlex.gt-xs="49.5" fxFlex='grow'
                    (barCode)="setInfo($event)"
                    [classButton]="'listen-scanner-button'"
                    [size]="25"></app-button-scanner-barcode>
                    <!-- <mat-form-field *ngIf="isOwnerFullname" appearance="outline" fxFlex.gt-sm="26" fxFlex.gt-xs="49.5" fxFlex='grow'>
                      <mat-label i18n>Chủ hồ sơ</mat-label>
                      <input type="text" matInput formControlName="ownerFullname">
                    </mat-form-field> -->
                    <button mat-flat-button fxFlex.gt-sm="16" fxFlex.gt-xs="49.5" fxFlex='grow' class="searchBtn" type="submit">
                <mat-icon>search</mat-icon><span i18n>Tìm kiếm</span>
            </button>
                </div>
            </form>
        </div>
    </div>
</div>


<div fxLayout="row" fxLayoutAlign="center">
    <div class="frm_main" fxFlex="grow">
        <div class="top-control">
                       
        </div>
        <div class="searchtbl syncListDossier">
            <table mat-table [dataSource]="dataSource">
                <!-- <ng-container matColumnDef="stt">
                    <mat-header-cell *matHeaderCellDef>
                        <mat-checkbox class="checkbox" [(ngModel)]="isCheckedAll" (change)="setAll($event.checked)">
                        </mat-checkbox>
                    </mat-header-cell>
                    <mat-cell *matCellDef="let row" data-label="">
                        <mat-checkbox class="checkbox" [checked]=row.checked></mat-checkbox>
                    </mat-cell>
                </ng-container> -->
                 <!-- <ng-container matColumnDef="select">
                    <mat-header-cell *matHeaderCellDef class="checkAllProcedureAdd">
                        <mat-checkbox [checked]="checkAll" (change)="checkAllItem($event)">
                        </mat-checkbox>
                    </mat-header-cell>
                    <mat-cell *matCellDef="let row; index as i" data-label="Chọn" class="checkAllProcedureAdd">
                        <mat-checkbox (change)="checkItem($event, row.code)" [(ngModel)]="row.checked">
                        </mat-checkbox>
                    </mat-cell>
                </ng-container>  -->

                <ng-container matColumnDef="code">
                    <mat-header-cell *matHeaderCellDef i18n>Mã số hồ sơ</mat-header-cell>
                    <mat-cell *matCellDef="let row" i18n-data-label data-label="Mã số hồ sơ" [ngClass]="{ 'cell_code_online': row.applyMethod.id === 0||4, 'cell_code_direct':   row.applyMethod.id === 1, 'cell_code_other':   row.applyMethod.id === 2 }" #tooltip="matTooltip"
                        matTooltip="{{row.codeText}}">
                        <a>{{row.code}}</a>
                    </mat-cell>
                </ng-container>

                <ng-container matColumnDef="procedureName">
                    <mat-header-cell *matHeaderCellDef >Về việc</mat-header-cell>
                    <mat-cell *matCellDef="let row" data-label="Về việc">
                        <span>
                            <a class="cell_code" (click)="dossierDetail(row.id, row.procedure.id, row.task)">{{row.procedure.code}}</a>
                            <span class="procedureName" *ngIf="row.procedure.translate == undefined"> - <span i18n>(Không tìm thấy bản dịch)</span></span>
                        <span class="procedureName" *ngIf="row.procedure.translate" #tooltip="matTooltip" matTooltip="{{row.procedure.translate.name}}"> - {{row.procedure.translate.name}}
                            <span *ngIf="row.procedure.translate != undefined && ( row.procedure.translate.name == undefined || row.procedure.translate.name == null || row.procedure.translate.name.trim() == '')" i18n>(Không tìm thấy bản dịch)</span></span>
                            
                            <span style="color: black;">Người tiếp nhận: <span *ngIf="!!row.accepter?.fullname">{{row.accepter?.fullname}}</span></span><br>
                            <!-- trang thái -->
                            <span [style.color]="getStatusColor(row.dossierStatus.id)">{{row.dossierStatus.name}}
                                <span *ngIf="env?.vnpost?.config === 1 || env?.vnpost?.config === '1'" style="color: black;">
                                    <br> {{row.vnpostStatusReturn}}
                                </span>
                            </span>
                                <span *ngIf="showStatusVnpost == 1 && row.vnpostStatus" style="color: black;">Trạng thái Vnpost:<br>{{row.vnpostStatus}}</span>
                            <span><br></span>
                            <span *ngIf = "row.extendQNI?.isVbdlis == true"> Đồng bộ VBDLIS </span>
                            <span style="color: red" *ngIf = "row.extendQNI?.isVbdlis !== undefined && row.extendQNI?.isVbdlis == false"> Đồng bộ VBDLIS thất bại </span>
                            <span style="color: red" *ngIf = "row.extendQNI?.isTBNOHTTTL !== undefined && row.extendQNI?.isTBNOHTTTL == false"> Đồng bộ tích hợp TBNOHTTTL thất bại </span>
                            <span *ngIf = "row.extendQNI?.isTBNOHTTTL == true"> Đồng bộ tích hợp TBNOHTTTL </span><br>
                            <span *ngIf = "row.extendQNI?.isIlis == true"> Đồng bộ ILIS </span>
                            <span style="color: red" *ngIf = "row.extendQNI?.isIlis !== undefined && row.extendQNI?.isIlis == false"> Đồng bộ ILIS thất bại </span>
                            <span *ngIf = "row.extendCMU?.isBhtn == true"> Đồng bộ BHTN </span>
                            <span style="color: red" *ngIf = "row.extendCMU?.isBhtn !== undefined && row.extendCMU?.isBhtn == false"> Đồng bộ BHTN thất bại </span>
                        </span>

                    </mat-cell>
                </ng-container>


                <ng-container matColumnDef="applicantName">
                    <mat-header-cell *matHeaderCellDef i18n>Người nộp</mat-header-cell>
                    <mat-cell *matCellDef="let row" [ngStyle]="{'color': row.due[0]?.timesheet?.color || 'black'}"  i18n-data-label data-label="Người nộp">
                        <ul>
                            <li [ngStyle]="{'font-weight': isBoldInfor ? '500': 'unset' }" *ngIf="row.applicant?.data?.fullname != '' && row.applicant?.data?.fullname">Người nộp: {{row.applicant?.data?.fullname}} </li>

                            <li *ngIf="row.applicant?.data?.address != '' && row.applicant?.data?.address">Địa chỉ chi tiết: {{row.applicant?.data?.address}} </li>
                            <li *ngIf="row.applicant?.data?.village?.label != '' && row.applicant?.data?.village?.label">Phường/xã : {{row.applicant?.data?.village?.label}} </li>
                            <li *ngIf="row.applicant?.data?.district?.label != '' && row.applicant?.data?.district?.label">Quận/huyện: {{row.applicant?.data?.district?.label}} </li>
                            <li *ngIf="row.applicant?.data?.province?.label != '' && row.applicant?.data?.province?.label">Tỉnh/thành phố: {{row.applicant?.data?.province?.label}} </li>

                        </ul>
                    </mat-cell>
                </ng-container>

                <ng-container matColumnDef="agency">
                    <mat-header-cell *matHeaderCellDef i18n>Cơ quan thực hiện</mat-header-cell>
                    <mat-cell *matCellDef="let row" i18n-data-label data-label="Cơ quan thực hiện"> {{row.agency.name}}
                    </mat-cell>
                </ng-container>

                <ng-container matColumnDef="status">
                    <mat-header-cell *matHeaderCellDef i18n>Trạng thái</mat-header-cell>
                    <mat-cell *matCellDef="let row" i18n-data-label data-label="Trạng thái" style="display: inline-block;">
                        <!-- <span *ngIf="row.dossierStatus != undefined && (row.dossierStatus.id == 5 || row.dossierStatus.id == 0 || row.dossierStatus.id == 6)" [style.color]="getStatusColor(row.dossierStatus.id)">
                            {{row.dossierStatus.name}}
                        </span>
                        <ng-container *ngIf="row.currentTask != undefined">
                            <span style="color: rgb(243, 156, 18);" *ngIf="row.currentTask[0]?.bpmProcessDefinitionTask?.name != undefined && row.dossierStatus.id !== 5 && row.dossierStatus.id !== 0">{{row.currentTask[0].bpmProcessDefinitionTask.name['name']}}</span>
                        </ng-container> -->
                        <span [style.color]="getStatusColor(row.dossierStatus.id)">{{row.dossierStatus.name}}
                            <span *ngIf="env?.vnpost?.config === 1 || env?.vnpost?.config === '1'" style="color: black;">
                                <br> {{row.vnpostStatusReturn}}
                            </span>
                        </span>
                            <span *ngIf="showStatusVnpost == 1 && row.vnpostStatus" style="color: black;">Trạng thái Vnpost:<br>{{row.vnpostStatus}}</span>
                        <span><br></span>
                        <span *ngIf = "row.extendQNI?.isVbdlis == true"> Đồng bộ VBDLIS </span>
                        <span style="color: red" *ngIf = "row.extendQNI?.isVbdlis !== undefined && row.extendQNI?.isVbdlis == false"> Đồng bộ VBDLIS thất bại </span>
                        <span style="color: red" *ngIf = "row.extendQNI?.isTBNOHTTTL !== undefined && row.extendQNI?.isTBNOHTTTL == false"> Đồng bộ tích hợp TBNOHTTTL thất bại </span>
                        <span *ngIf = "row.extendQNI?.isTBNOHTTTL == true"> Đồng bộ tích hợp TBNOHTTTL </span><br>
                        <span *ngIf = "row.extendQNI?.isIlis == true"> Đồng bộ ILIS </span>
                        <span style="color: red" *ngIf = "row.extendQNI?.isIlis !== undefined && row.extendQNI?.isIlis == false"> Đồng bộ ILIS thất bại </span>
                        <span *ngIf = "row.extendCMU?.isBhtn == true"> Đồng bộ BHTN </span>
                        <span style="color: red" *ngIf = "row.extendCMU?.isBhtn !== undefined && row.extendCMU?.isBhtn == false"> Đồng bộ BHTN thất bại </span>
                    </mat-cell>
                </ng-container>

                <ng-container matColumnDef="statusSync">
                    <mat-header-cell *matHeaderCellDef >Phí, lệ phí</mat-header-cell>
                    <mat-cell *matCellDef="let row" data-label="Phí, lệ phí" [ngStyle]="{'color': row.paid==1 ? 'rgb(0, 81, 255)' : 'red' }"><span> {{row.total | number}} {{row.typeMoney}}
                        <br><span *ngIf="row.paid==1">Đã thu</span><span *ngIf="row.paid==0">Chưa thu</span>
                        <br *ngIf="row.paid==1"><span *ngIf="row.paid==1">{{row.chargeFeesDossier[0].createdDate | date : 'dd/MM/yyyy HH:mm:ss'}}</span>
                    </span>
                    </mat-cell>
                </ng-container>

                <ng-container matColumnDef="action">
                    <mat-header-cell *matHeaderCellDef >Ngân hàng</mat-header-cell>
                    <mat-cell *matCellDef="let row"  data-label="Ngân hàng">
                        <button mat-icon-button *ngIf="row.paid==0" (click)="applyChangeFeesDialog(row, 'add')" [disabled]="row.total == 0">
                            <mat-icon>paid</mat-icon>
                        </button>
                        <button mat-icon-button *ngIf="row.paid==1" (click)="applyChangeFeesDialog(row, 'cancel')">
                            <mat-icon>cancel</mat-icon>
                        </button>
                        <!-- <mat-menu #actionMenu="matMenu" xPosition="before">
                            <ng-container>
                                <button mat-menu-item class="menuAction" (click)="syncByCode(row.code)">
                                    <mat-icon>sync</mat-icon><span>Yêu cầu đồng bộ</span>
                                </button>
                            </ng-container>
                        </mat-menu> -->
                    </mat-cell>
                </ng-container>

                <mat-header-row *matHeaderRowDef="displayedColumnsDefault"></mat-header-row>

                <mat-row *matRowDef="let row; columns: displayedColumnsDefault;"></mat-row>
            </table>
            <pagination-slice id="pgnx"
                    [itemsPerPage]="size"
                    [currentPage]="page"
                    [totalItems]="countResult"
                    [pageSizeOptions]="[].concat(pgSizeOptions)"
                    [dataSource]="ELEMENTDATA"
                    (change)="changePageOrSize($event)"
                    [type]="paginationType">
            </pagination-slice>
        </div>
    </div>
</div>
