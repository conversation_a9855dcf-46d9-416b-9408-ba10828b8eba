{"name": "web-onegate", "version": "0.0.0", "scripts": {"ng": "ng", "sonar": "sonar-scanner", "xi18n": "ng xi18n", "start": "ng serve", "build": "ng build", "build-i18n": "(NODE_OPTIONS=--max_old_space_size=12480 ng build --configuration=production-vi --prod)", "test": "ng test", "lint": "ng lint", "e2e": "ng e2e", "mserve": "set NODE_OPTIONS=--max_old_space_size=14480 && ng serve --open"}, "private": true, "dependencies": {"@angular-material-components/datetime-picker": "^2.0.4", "@angular-material-components/moment-adapter": "^2.0.2", "@angular/animations": "~9.1.3", "@angular/cdk": "^9.2.4", "@angular/common": "~9.1.3", "@angular/compiler": "~9.1.3", "@angular/core": "~9.1.3", "@angular/elements": "^10.1.6", "@angular/fire": "^6.0.5", "@angular/flex-layout": "^9.0.0-beta.31", "@angular/forms": "~9.1.3", "@angular/localize": "^9.1.4", "@angular/material": "^9.2.4", "@angular/platform-browser": "~9.1.3", "@angular/platform-browser-dynamic": "~9.1.3", "@angular/router": "~9.1.3", "@ckeditor/ckeditor5-angular": "^1.2.3", "@ckeditor/ckeditor5-build-classic": "^21.0.0", "@kolkov/angular-editor": "^1.2.0", "@ngx-loading-bar/core": "^5.1.0", "@ngx-loading-bar/http-client": "^5.1.0", "@ngx-loading-bar/router": "^5.1.0", "@pdf-lib/fontkit": "^1.1.1", "@pdf-lib/standard-fonts": "^1.0.0", "@stomp/stompjs": "^6.1.2", "@types/pdfmake": "^0.2.2", "@types/xml2js": "0.4.3", "angular-tree-grid": "^3.0.1", "ang-jsoneditor": "^1.10.5", "angular-formio": "^4.11.5", "angular-formio.css": "^1.0.1", "axios": "^0.20.0", "bpmn-js": "^7.3.0", "chart.js": "^2.9.4", "chartjs-plugin-labels": "^1.1.0", "docx": "^6.0.3", "exceljs": "^4.1.1", "file-saver": "^2.0.2", "firebase": "^8.10.1", "font-awesome": "^4.7.0", "fontkit": "^2.0.2", "hammerjs": "^2.0.8", "html-to-pdfmake": "^2.4.22", "html2canvas": "^1.0.0-rc.7", "html2pdf.js": "^0.9.3", "jquery": "^3.6.0", "jsoneditor": "^9.5.0", "jspdf": "^2.3.1", "jwt-decode": "^3.1.2", "keycloak-angular": "^7.2.0", "keycloak-js": "^9.0.3", "moment": "^2.29.1", "ng-mat-select-infinite-scroll": "^2.1.1", "ng2-charts": "^2.4.3", "ng2-pdf-viewer": "^7.0.2", "ngx-cookie-service": "^12.0.3", "ngx-extended-pdf-viewer": "^10.5.0", "ngx-filesaver": "^10.0.1", "ngx-image-compress": "^8.0.4", "ngx-infinite-scroll": "^9.1.0", "ngx-mask": "^10.0.1", "ngx-mat-select-search": "^3.1.0", "ngx-onlyoffice": "^0.0.9", "ngx-pagination": "^5.0.0", "ngx-print": "^1.2.0-beta.5", "ngx-printer": "^0.9.2", "ngx-ui-loader": "^9.1.1", "pdf-lib": "^1.17.1", "pdfmake": "^0.2.7", "rxjs": "~6.5.4", "screenfull": "^5.0.2", "stream": "0.0.2", "sweetalert2": "^11.1.4", "timers": "^0.1.1", "tslib": "^1.10.0", "xlsx": "^0.16.7", "xml2js": "0.4.19", "zone.js": "~0.10.2", "angular2-qrcode": "^2.0.3"}, "devDependencies": {"@angular-devkit/build-angular": "^0.901.15", "@angular/cli": "~9.1.3", "@angular/compiler-cli": "~9.1.3", "@angular/language-service": "~9.1.3", "@types/jasmine": "^3.5.13", "@types/jasminewd2": "~2.0.3", "@types/jquery": "^3.5.9", "@types/node": "^12.12.47", "codelyzer": "^5.1.2", "jasmine-core": "~3.5.0", "jasmine-spec-reporter": "~4.2.1", "karma": "~5.0.0", "karma-chrome-launcher": "~3.1.0", "karma-coverage-istanbul-reporter": "~2.1.0", "karma-jasmine": "~3.0.1", "karma-jasmine-html-reporter": "^1.4.2", "protractor": "~5.4.3", "ts-node": "~8.3.0", "tslint": "^6.1.3", "typescript": "~3.8.3"}}