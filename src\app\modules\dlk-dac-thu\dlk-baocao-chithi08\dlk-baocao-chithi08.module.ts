import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { DlkBaoCaoChiThi08RoutingModule } from './dlk-baocao-chithi08-routing.module';
import { DlkBaoCaoChiThi08Component } from './dlk-baocao-chithi08.component';
import { SharedModule } from 'src/app/shared/shared.module';
import { NgxPrinterModule } from 'ngx-printer';
import { DossierDetailComponent } from '../dialogs/view-detail.component';

@NgModule({
  declarations: [DlkBaoCaoChiThi08Component, DossierDetailComponent],
  imports: [
    CommonModule,
    DlkBaoCaoChiThi08RoutingModule,
    SharedModule,

    NgxPrinterModule.forRoot({ printOpenWindow: true })
  ]
})
export class DlkBaoCaoChiThi08Module { }
