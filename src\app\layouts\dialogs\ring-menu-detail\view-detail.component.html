<div class="body menu-detail">
    <h3 class="dialog_title" mat-dialog-title i18n></h3>

    <div class="frm_tbl0">
        <table mat-table [dataSource]="dataSource">
            <!-- Header row once group -->
            <ng-container matColumnDef="col-1">
                <th mat-header-cell *matHeaderCellDef i18n>STT</th>
            </ng-container>
            <ng-container matColumnDef="col-2">
                <th mat-header-cell *matHeaderCellDef i18n>Số hồ sơ</th>
            </ng-container>
            <ng-container matColumnDef="col-3">
                <th mat-header-cell *matHeaderCellDef i18n>Thủ tục hành chính</th>
            </ng-container>
            <ng-container matColumnDef="col-4">
                <th mat-header-cell *matHeaderCellDef i18n>Chủ hồ sơ</th>
            </ng-container>
            <ng-container matColumnDef="col-5">
                <th mat-header-cell *matHeaderCellDef i18n><PERSON><PERSON><PERSON> k<PERSON><PERSON> thú<PERSON><PERSON> lý</th>
            </ng-container>

            <!-- DATA -->
            <ng-container matColumnDef="no">
                <th mat-header-cell *matHeaderCellDef >STT</th>
                <td mat-cell *matCellDef="let row"> {{row.no}}</td>
            </ng-container>
             <ng-container matColumnDef="dossierCode">
                <th mat-header-cell *matHeaderCellDef i18n>Số hồ sơ</th>
                <td mat-cell *matCellDef="let row" class="cell_info" (click)="openDetail(row)"> {{row.dossierCode}} </td>
            </ng-container>
            <ng-container matColumnDef="procedureName">
                <th mat-header-cell *matHeaderCellDef i18n>Thủ tục hành chính</th>
                <td mat-cell *matCellDef="let row"> {{row.procedureName}} </td>
            </ng-container>
            <ng-container matColumnDef="applicantOwnerFullName">
                <th mat-header-cell *matHeaderCellDef i18n>Chủ hồ sơ</th>
                <td mat-cell *matCellDef="let row"> {{row.applicantOwnerFullName}} </td>
            </ng-container>
            <ng-container matColumnDef="completedDate">
                <th mat-header-cell *matHeaderCellDef i18n>Ngày kết thúc xử lý</th>
                <td mat-cell *matCellDef="let row"> {{row.completedDate | date : 'dd/MM/yyyy HH:mm:ss' }} </td>
            </ng-container>

            <tr mat-header-row *matHeaderRowDef="['col-1', 'col-2', 'col-3', 'col-4', 'col-5']"></tr>
            <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
        </table>
        <!-- <div *ngIf="isWaitingData" class="tableSpinnerContainer">
            <mat-spinner diameter="60">
            </mat-spinner>
        </div> -->
    </div>

    <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="column" fxLayoutAlign="center">
        <button mat-flat-button class="closeBtn" (click)="onClose()">
            <span i18n>Đóng</span>
        </button>
    </div>
</div>
