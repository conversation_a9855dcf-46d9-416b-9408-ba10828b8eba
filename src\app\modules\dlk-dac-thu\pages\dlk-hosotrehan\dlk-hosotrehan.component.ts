import { DatePipe } from '@angular/common';
import { AfterViewInit, ChangeDetectorRef, Component, On<PERSON><PERSON>roy, OnInit, ViewChild } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { MatSelect } from '@angular/material/select';
import { MatTableDataSource } from '@angular/material/table';
import { DeploymentService } from 'data/service/deployment.service';
import { Workbook } from 'exceljs';
import * as fs from 'file-saver';
import { Subject } from 'rxjs';
import { EnvService } from 'src/app/core/service/env.service';
import { DLKStatisticService } from 'src/app/data/service/dlk-dac-thu/dlk-statistic.service';
import { MainService } from 'src/app/data/service/main/main.service';
import { ProcedureService } from 'src/app/data/service/procedure/procedure.service';
import { SnackbarService } from 'src/app/data/service/snackbar/snackbar.service';
import { StatisticsService } from 'src/app/data/service/statistics/statistics.service';
import * as tUtils from 'src/app/data/service/thoai.service';
import { AddApologyTextComponent, ConfirmAddApologyTextComponent } from 'src/app/modules/dossier/pages/search/dialogs/add-apology-text/add-apology-text.component';
import { ConfirmSignApologyTextComponent } from 'src/app/modules/dossier/pages/search/dialogs/sign-apology-text/sign-apology-text.component';
import { ProcessHandleComponent, ProcessHandleDialogModel } from 'src/app/shared/components/process-handle/process-handle.component';
import { DlkSignApologyTextComponent } from '../../dialogs/dlk-sign-apology-text/dlk-sign-apology-text.component';

const EXCEL_TYPE = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8';
const EXCEL_EXTENSION = '.xlsx';
export interface Agency {
  id: string;
  name: string;
}

@Component({
  selector: 'app-dlk-hosotrehan',
  templateUrl: './dlk-hosotrehan.component.html',
  styleUrls: [
    './dlk-hosotrehan.component.scss',
    '/src/app/app.component.scss',
    '/src/app/shared/scss/form-field-outline.scss'
  ]
})
export class DlkHoSoTreHanComponent implements OnInit, AfterViewInit, OnDestroy {

  nowDate = tUtils.newDate();
  listHinhThucNhan: any[] = [
    { value: "0", text: "Tất cả", isSelected: true },
    { value: "1", text: "Chưa giải quyết", isSelected: true },
    { value: "2", text: "Đã giải quyết", isSelected: true }
  ];

  listLoaiNgay: any[] = [
    { value: "-1", text: "Lựa chọn", isSelected: false, isDisabled: true },
    { value: "0", text: "Ngày tiếp nhận", isSelected: true, isDisabled: false },
    { value: "1", text: "Hạn xử lý", isSelected: false, isDisabled: false }
  ]

  // tslint:disable-next-line: max-line-length
  toDate = this.nowDate.getFullYear() + '-' + ((this.nowDate.getMonth() + 1) <= 9 ? '0' + (this.nowDate.getMonth() + 1) : (this.nowDate.getMonth() + 1)) + '-' + ((this.nowDate.getDate() <= 9) ? ('0' + this.nowDate.getDate()) : this.nowDate.getDate());
  // tslint:disable-next-line: max-line-length
  fromDate = this.nowDate.getFullYear() + '-' + ((this.nowDate.getMonth() + 1) <= 9 ? '0' + (this.nowDate.getMonth() + 1) : (this.nowDate.getMonth() + 1)) + '-01';

  keyword = '';
  config = this.envService.getConfig();
  selectedLang = localStorage.getItem('language') || 'vi';
  selectedLangId = Number(localStorage.getItem('languageId')) || 228;
  pageTitle = {
    vi: 'Danh sách hồ sơ trễ hạn',
    en: 'Logbook statistics'
  };

  protected onDestroy = new Subject<void>();
  // =================== old
  env: any = this.deploymentService.getAppDeployment()?.env;
  ELEMENTDATA: any[] = [];
  dataSource: MatTableDataSource<any>;
  // listSector: Array<any> = [];
  keySearchSectorAgency = '';
  // Search Sector
  keywordSector = '';
  totalPagesSector = 0;
  currentPageSector = 0;
  pageSizeSector = 100;

  rootAgencyId = this.deploymentService.getConfig("env")?.OS_DLK?.rootAgencyId;
  parentAgency = '';
  childAgency = '';
  agencyId = '';
  userAgency?: any = JSON.parse(localStorage.getItem('userAgency'));
  userAgencyCount?: any = JSON.parse(localStorage.getItem('listAgencyUser'))?.length || 0;
  ELEMENTDATAALL: any[] = [];
  dataSourceAll: MatTableDataSource<any>;
  listHoSo = [];
  listExport = [];

  size = 10;
  pageIndex = 1;
  page = 1;
  countResult = 0;
  pgSizeOptions = this.config.pageSizeOptions;
  paramsQuery = {
    page: 0,
    size: 10,
    agency: '',
    parentAgency: '',
    childAgency: '',
    fromDate: '',
    toDate: '',
    applyMethod: "-1",
    receivingKind: '',
    sector: '',
    keyword: '',
    procedure: '',
    sortId: '',
    isTTPVHCC: 1,
    all: 0,
    agencyName: '',
    loaiNgay: "0",
    loaiGiaiQuyet: "0",
    childAgencyList: [],
    levelRoot: 0,
  };

  listAgency = [];
  listAgencyAccept = [];
  childAgencyList = [];
  listAgencyChild = [];
  listAgencyChildRoot = [];
  keywordAgencyAccept = '';
  totalPagesAgencyAccept = 0;
  currentPageAgencyAccept = 0;
  pageSizeAgencyAccept = 1000;
  startDate = new Date();
  endDate = new Date();
  flagTTPVHCC = true;
  timeOutAgencyAccept: any = null;
  listSectorfillter: any[] = [];
  listSector: any[] = [];
  listProcedure: any[] = [];
  listProcedurefillter: any[] = [];
  totalPagesProcedure = 0;
  currentPageProcedure = 0;
  searchProcedureKeyword = "";
  waitingDownloadExcel = false;
  disableDviSelect = true;

  agencyList: Array<any> = [];
  agencyPage = 0;
  isAgencyListFull = false;

  @ViewChild('procedureMatSelectInfiniteScroll', { static: true }) procedureMatSelectInfiniteScroll: MatSelect;

  constructor(
    private dialog: MatDialog,
    private envService: EnvService,
    public datepipe: DatePipe,
    private mainService: MainService,
    private cdRef: ChangeDetectorRef,
    private procedureService: ProcedureService,
    private datePipe: DatePipe,
    private snackbarService: SnackbarService,
    private deploymentService: DeploymentService,
    private dlkStatisticService: DLKStatisticService,
    private statisticsService: StatisticsService
  ) {
    this.dataSource = new MatTableDataSource(this.ELEMENTDATA);
    this.dataSourceAll = new MatTableDataSource(this.ELEMENTDATAALL);
  }

  ngOnInit(): void {
    this.mainService.setPageTitle(this.pageTitle[localStorage.getItem('language')]);
    this.startDate = new Date(this.fromDate);
    this.endDate = new Date(this.toDate);
    if (this.userAgency !== null && this.userAgency !== undefined) {
      // var isHanhChinh = false;
      // kiem tra neu khong phai co quan hanh chinh thi lay id cha
      // if (this.userAgency.tag?.length > 0) {
      //   for (var i = 0; i < this.userAgency.tag?.length; i++) {
      //     if (this.userAgency.tag[i] == "0000591c4e1bd312a6f00003") {
      //       isHanhChinh = true;
      //       break;
      //     }
      //   }
      // }

      if (!!this.userAgency.parent && !!this.userAgency.parent.id && this.userAgencyCount === 1) {
        this.parentAgency = this.userAgency.parent.id;
        this.agencyId = this.userAgency.parent.id;
        this.paramsQuery.agencyName = this.userAgency.parent.name;
      } else {
        this.parentAgency = this.userAgency.id;
        this.agencyId = this.userAgency.id;
        this.paramsQuery.agencyName = this.userAgency.name;
      }

      this.keySearchSectorAgency = '&agency-id=' + this.agencyId;
    }

    this.paramsQuery.parentAgency = this.parentAgency;
    this.getAgencyList(true);
    this.getListAgencyAccept(this.keywordAgencyAccept, this.currentPageAgencyAccept, this.pageSizeAgencyAccept);
    this.getListSector();
  }

  ngAfterViewInit() {
    this.cdRef.detectChanges();
    setTimeout(() => {
      // this.thongKe();
    }, 1000);
  }

  ngOnDestroy() {
    this.onDestroy.next();
    this.onDestroy.complete();
  }

  thongKe() {
    this.paramsQuery.page = 0;
    this.page = 1;
    this.paramsQuery.all = 0;
    this.getListHoSo();
  }
  paginate(event) {
    this.paramsQuery.page = event;
    this.paramsQuery.all = 0;
    this.getListHoSo();
  }

  getListHoSo() {
    this.paramsQuery.fromDate = (this.startDate ? this.datePipe.transform(this.startDate, 'dd/MM/yyyy') + 'T00:00:00.000Z' : '');
    this.paramsQuery.toDate = (this.endDate ? this.datePipe.transform(this.endDate, 'dd/MM/yyyy') + 'T23:59:59.999Z' : '');

    // Kiểm tra validate
    let data = this.validateForm();
    if (data == null || !data?.status) {
      this.snackbarService.openSnackBar(0, "Không hợp lệ", data.errMessage, 'error_notification', this.config.expiredTime);
      this.listHoSo = [];
      this.countResult = 0;
      return;
    }

    this.dlkStatisticService.getDossierTreHan(this.paramsQuery).subscribe(res => {
      this.listHoSo = res.content;
      this.countResult = res.totalElements;
    }, err => {
      console.log(err);
    });
  }

  getAgencyList(scroll: boolean) {
    if (!scroll) {
      this.agencyPage = 0;
      this.isAgencyListFull = false;
      this.agencyList = [];
    }

    // const formObj = this.searchForm.getRawValue();

    const searchString = '?page=0&size=1000&status=1&spec=slice'
      + '&parent-id=' + this.parentAgency + '&keyword=' + '';

    this.statisticsService.getAgencyList(searchString).subscribe(data => {
      if (this.userAgency !== null && this.userAgency !== undefined) {
        this.agencyList.push({ id: this.userAgency?.id, name: this.userAgency?.name });
      }
      this.agencyList = [...this.agencyList, ...data.content];
      this.agencyList = Object.values(this.agencyList.reduce((acc, cur) => Object.assign(acc, { [cur.id]: cur }), {}));
      this.agencyPage++;
      this.isAgencyListFull = data.last;
    }, err => {
      console.log(err);
    });
  }

  getAgencyScroll() {
    this.currentPageAgencyAccept += 1;
    this.getListAgencyAccept(this.keywordAgencyAccept, this.currentPageAgencyAccept, this.pageSizeAgencyAccept);
  }

  getListAgencyAccept(keyword, page, size) {
    const searchString = '?parent-id=' + this.parentAgency + '&keyword=' + keyword + '&page=' + page + '&size=' + size + '&sort=name.name,asc&status=1';
    this.procedureService.getListAgencyWithParent(searchString).subscribe(res => {
      if (page === 0) {
        this.listAgency = res.content;
      } else {
        this.listAgency = this.listAgency.concat(res.content);
      }

      if (this.listAgency != null && this.listAgency.length > 0) {
        this.listAgencyAccept = this.listAgency;
        this.listAgency.forEach(item => {
          if (item != null && item.id != null) {
            this.paramsQuery.childAgencyList.push(item.id);
            this.childAgencyList.push(item.id);
          }
        })
        // this.paramsQuery.childAgencyList = this.listAgency;
      }

      this.totalPagesAgencyAccept = res.totalPages;
    }, err => {
      console.log(err);
    });
  }

  getAgencyChildList(parentId) {
    if (parentId != null && parentId != '') {
      this.disableDviSelect = false;
      this.listAgencyChild = [];
      const searchString = '?parent-id=' + parentId + '&page=0&size=1000&spec=all&sort=name.name,asc&status=1';

      this.statisticsService.getAgencyList(searchString).subscribe(data => {
        this.listAgencyChildRoot = data.content;
        this.listAgencyChild = data.content;
      }, err => {
        console.log(err);
      });
    }
  }


  searchAngency(event) {
    if (event != "") {
      this.listAgencyAccept = this.listAgency.filter(agency =>
        agency.name.toLowerCase().trim().includes(event.toLowerCase()) == true);
    } else {
      this.listAgencyAccept = this.listAgency;
    }
  }

  searchAngencyChild(event) {
    if (event != "") {
      this.listAgencyChild = this.listAgencyChildRoot.filter(agency =>
        agency.name.toLowerCase().trim().includes(event.toLowerCase()) == true);
    } else {
      this.listAgencyChild = this.listAgencyChildRoot;
    }
  }

  clickTTPVHCC() {
    this.paramsQuery.isTTPVHCC = this.flagTTPVHCC ? 1 : 0;
  }

  onEnter(event) {
    clearTimeout(this.timeOutAgencyAccept);
    this.timeOutAgencyAccept = setTimeout(async () => {
      this.keywordAgencyAccept = event.target.value;
      this.currentPageAgencyAccept = 0;
      this.getListAgencyAccept(this.keywordAgencyAccept, this.currentPageAgencyAccept, this.pageSizeAgencyAccept);
      this.keywordSector = '';
      this.totalPagesSector = 0;
      this.currentPageSector = 0;
      this.pageSizeSector = 100;
      this.listSector = [];
      this.getListSector();
    }, 300);
  }

  async resetSearchForm(type) {
    switch (type) {
      case 'agencyAccept': {
        this.currentPageAgencyAccept = 0;
        this.keywordAgencyAccept = '';
        this.getListAgencyAccept(this.keywordAgencyAccept, this.currentPageAgencyAccept, this.pageSizeAgencyAccept);

        this.keywordSector = '';
        this.totalPagesSector = 0;
        this.currentPageSector = 0;
        this.pageSizeSector = 100;
        break;
      }
    }
  }

  changeAgencyAccept() {
    this.paramsQuery.sector = "";

    if (this.paramsQuery.agency !== '') {
      this.paramsQuery.childAgencyList = [];
      this.keySearchSectorAgency = '&agency-id=' + this.paramsQuery.agency;
    } else {
      this.paramsQuery.childAgencyList = this.childAgencyList;
      if (this.parentAgency !== '') {
        this.keySearchSectorAgency = '&agency-id=' + this.parentAgency;
      }
      else {
        this.keySearchSectorAgency = '';
      }
    }

    this.keywordSector = '';
    this.totalPagesSector = 0;
    this.currentPageSector = 0;
    this.pageSizeSector = 100;
    this.listSector = [];
    this.getListSector();
  }



  getListSector() {
    // tslint:disable-next-line:max-line-length
    // const searchString = '?keyword=' + this.keywordSector + '&page=' + this.currentPageSector + '&size=' + this.pageSizeSector + '&spec=page&sort=name.name,asc&status=1&only-agency-id=1' + this.keySearchSectorAgency;
    const searchString = '?&page=0&size=1000&spec=page&sort=name.name,asc';
    this.procedureService.getListSector(searchString).subscribe(res => {
      if (this.currentPageSector === 0) {
        this.listSector = res.content;
      } else {
        this.listSector = this.listSector.concat(res.content);
      }
      this.totalPagesSector = res.totalPages;
      this.listSectorfillter = this.listSector;
    }, err => {
      console.log(err);
    });
  }

  getListSectorScroll() {
    this.currentPageSector += 1;
    this.getListSector();
  }

  searchSector(event) {
    if (event != "") {
      this.listSectorfillter = this.listSector.filter(sector =>
        sector.name.toLowerCase().trim().includes(event.toLowerCase()) == true);

    } else {
      this.listSectorfillter = this.listSector;
    }
  }

  sectorChange() {
    this.listProcedure = [];
    this.listProcedurefillter = [];
    this.currentPageProcedure = 0;
    this.paramsQuery.procedure = "";
    this.getListProcedure();
  }

  getListProcedureScroll() {
    this.currentPageProcedure += 1;
    this.getListProcedure();
  }

  getListProcedure() {
    const searchString =
      '?status=1&sort=translate.name,asc&keyword=' + this.searchProcedureKeyword +
      '&spec=page&page=' + this.currentPageProcedure + '&size=50' +
      '&sector-id=' + this.paramsQuery.sector;
    this.procedureService.getListProcedure(searchString).subscribe(data => {
      if (this.currentPageProcedure == 0) {
        this.listProcedure = data.content;
      } else {
        this.listProcedure = this.listProcedure.concat(data.content);
      }
      this.totalPagesProcedure = data.totalPages;
      this.listProcedurefillter = this.listProcedure;
    }, err => {
      console.log(err);
    });
  }

  searchProvedure(event) {
    if (event != "") {
      this.searchProcedureKeyword = event;
      // this.listProcedurefillter = this.listProcedure.filter(pro =>
      //   pro.name.toLowerCase().trim().includes(event.toLowerCase()) == true);
    } else {
      this.searchProcedureKeyword = "";
      // this.currentPageProcedure = 0;
      // this.getListProcedure();
      //this.listProcedurefillter = this.listProcedure;
    }
    this.currentPageProcedure = 0;
    this.getListProcedure();
  }

  viewProcess(dossierId, dossierCode) {
    if (dossierCode === undefined || dossierCode === null) {
      dossierCode = '';
    }
    const dialogData = new ProcessHandleDialogModel(dossierId, dossierCode);
    const dialogRef = this.dialog.open(ProcessHandleComponent, {
      minWidth: '70vw',
      data: dialogData,
      disableClose: true,
      autoFocus: false
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
    });
  }

  addApologyText(dossierId) {
    const dialogData = new ConfirmAddApologyTextComponent(dossierId);
    const dialogRef = this.dialog.open(AddApologyTextComponent, {
      minWidth: '40vw',
      maxWidth: '60vw',
      maxHeight: '60vh',
      data: dialogData,
      disableClose: true,
      autoFocus: false
    });
  }

  signApologyText(dossierId) {
    const dialogData = new ConfirmSignApologyTextComponent(dossierId);
    const dialogRef = this.dialog.open(DlkSignApologyTextComponent, {
      minWidth: '50vw',
      maxWidth: '60vw',
      minHeight: '40vh',
      maxHeight: '70vh',
      data: dialogData,
      disableClose: true,
      autoFocus: false
    });

    dialogRef.afterClosed().subscribe(dialogResult => {
      if (dialogResult === true) {
        const msgObj = {
          vi: 'Đã lưu!',
          en: 'Saved!'
        };
        this.snackbarService.openSnackBar(1, msgObj[localStorage.getItem('language')], '', 'success_notification', this.config.expiredTime);
      } else if (dialogResult === false) {
        const msgObj = {
          vi: 'Lưu thất bại!',
          en: 'Save failed!'
        };
        this.snackbarService.openSnackBar(0, msgObj[localStorage.getItem('language')], '', 'error_notification', this.config.expiredTime);
      }
    });
  }

  generateAddress(data) {
    if (data != null) {
      const address = [];
      if (data?.address !== undefined && data?.address !== null && data?.address !== "") {
        address.push(data.address);
      }
      if (data?.village !== undefined && data?.village !== null && data?.address !== "") {
        data.village?.label ? address.push(data.village.label) : "";
      }
      if (data?.district !== undefined && data?.district !== null && data?.address !== "") {
        data.district?.label ? address.push(data.district.label) : "";
      }
      if (data?.province !== undefined && data?.province !== null && data?.address !== "") {
        data.province?.label ? address.push(data.province.label): "";
      }

      return address.join(', ');
    } else {
      return "";
    }

  }

  getFullName(data) {
    if (data != null) {
      if (data?.fullName != undefined && data?.fullName != null && data?.fullName != "") {
        return data.fullName;
      } else if (data?.fullname != undefined && data?.fullname != null && data?.fullname != "") {
        return data.fullname;
      } else if (data?.ownerFullname != undefined && data?.ownerFullname != null && data?.ownerFullname != "") {
        return data.ownerFullname;
      } else {
        return "";
      }
    }
  }

  getTienDo(data) {
    if (data != null) {
      var ngayHoanThanh = this.nowDate;
      var ngayHenTra = this.nowDate;
      var finish = false;
      if (data.appointmentDate != undefined && data.appointmentDate != "") {
        ngayHenTra = new Date(data.appointmentDate);
      } else {
        return "Còn hạn";
      }
      if (data.completedDate != undefined && data.completedDate != "") {
        ngayHoanThanh = new Date(data.completedDate);
        finish = true;
      }

      var result = ngayHoanThanh.getTime() - ngayHenTra.getTime();
      if (result < 0) {
        if (finish) {
          return "Đúng hạn";
        } else {
          return "Còn hạn";
        }
      } else {
        return "Quá hạn";
      }
    } else {
      return "";
    }

  }

  getTen(data) {
    if (data != null) {
      if (data.length > 0) {
        var ten = data[0].name;
        data.forEach(element => {
          if (element.languageId == 228) {
            ten = element.name;
          }
        });
        return ten;
      } else {
        return "";
      }
    } else {
      return "";
    }
  }

  xuatExcel() {
    this.paramsQuery.fromDate = (this.startDate ? this.datePipe.transform(this.startDate, 'dd/MM/yyyy') + 'T00:00:00.000Z' : '');
    this.paramsQuery.toDate = (this.endDate ? this.datePipe.transform(this.endDate, 'dd/MM/yyyy') + 'T23:59:59.999Z' : '');
    // Kiểm tra validate
    let data = this.validateForm();
    if (data == null || !data?.status) {
      this.snackbarService.openSnackBar(0, "Không hợp lệ", data.errMessage, 'error_notification', this.config.expiredTime);
      this.listHoSo = [];
      this.countResult = 0;
      return;
    }
    this.paramsQuery.all = 1;
    this.waitingDownloadExcel = true;
    
    this.dlkStatisticService.getDossierTreHan(this.paramsQuery).subscribe(res => {
      this.listExport = res.content;
      this.exportLogbookExcel(this.listExport);
      this.waitingDownloadExcel = false;
    }, err => {
      console.log(err);
      if (err != null && err?.status == 500) {
        if (err?.error?.message.includes("exceeded memory limit")) {
          console.log("exceeded memory limit");
          this.snackbarService.openSnackBar(0, 'Xuất excel lỗi', 'Đã vượt giới hạn bộ nhớ!!', 'error_notification', this.config.expiredTime);
        }
      }
      this.waitingDownloadExcel = false;
    });
  }

  public exportLogbookExcel(listExport) {
    // create workbook and worksheet
    const workbook = new Workbook();
    workbook.creator = 'Snippet Coder';
    workbook.lastModifiedBy = 'SnippetCoder';
    workbook.created = new Date();
    workbook.modified = new Date();
    const worksheet = workbook.addWorksheet("MauHoSoDenHan");
    worksheet.properties.outlineLevelCol = 2;
    worksheet.properties.defaultRowHeight = 16;

    // Add header row
    worksheet.mergeCells('A1:B1');
    worksheet.getCell('A1').value = "UBND TỈNH ĐẮK LẮK";
    // tslint:disable-next-line: max-line-length
    worksheet.getCell('A1').style = { font: { bold: true, name: 'Times New Roman', size: 11 }, alignment: { horizontal: 'center', vertical: 'middle' } };
    // Add header row
    worksheet.mergeCells('A2:B2');
    worksheet.getCell('A2').value = this.paramsQuery.agencyName;
    // tslint:disable-next-line: max-line-length
    worksheet.getCell('A2').style = { font: { bold: true, name: 'Times New Roman', size: 11 }, alignment: { horizontal: 'center', vertical: 'middle' } };

    worksheet.mergeCells('E1:F1');
    worksheet.getCell('E1').value = "CỘNG HÒA XÃ HỘI CHỦ NGHĨA VIỆT NAM";
    // tslint:disable-next-line: max-line-length
    worksheet.getCell('E1').style = { font: { bold: true, name: 'Times New Roman', size: 11 }, alignment: { horizontal: 'center', vertical: 'middle' } };

    worksheet.mergeCells('E2:F2');
    worksheet.getCell('E2').value = "Độc lập - Tự do - Hạnh phúc";
    // tslint:disable-next-line: max-line-length
    worksheet.getCell('E2').style = { font: { bold: true, name: 'Times New Roman', size: 11 }, alignment: { horizontal: 'center', vertical: 'middle' } };

    worksheet.mergeCells('A4:F4');
    worksheet.getCell('A4').value = "THỐNG KÊ HỒ SƠ TRỄ HẠN";
    // tslint:disable-next-line: max-line-length
    worksheet.getCell('A4').style = { font: { bold: true, name: 'Times New Roman', size: 16 }, alignment: { horizontal: 'center', vertical: 'middle' } };

    worksheet.mergeCells('A5:F5');
    worksheet.getCell('A5').value = "Từ ngày " + (this.paramsQuery.fromDate.split("T"))[0] + " đến " + (this.paramsQuery.toDate.split("T"))[0];
    // tslint:disable-next-line: max-line-length
    worksheet.getCell('A5').style = { font: { bold: false, name: 'Times New Roman', size: 11 }, alignment: { horizontal: 'center', vertical: 'middle' } };

    worksheet.getCell("A7").value = "STT";
    // tslint:disable-next-line: max-line-length
    worksheet.getCell("A7").style = { font: { bold: true, name: 'Times New Roman', size: 11 }, alignment: { horizontal: 'center', vertical: 'middle', wrapText: true }, fill: { type: 'pattern', pattern: 'darkVertical', fgColor: { argb: 'FFBFBFBF' }, bgColor: { argb: 'FFBFBFBF' } } };
    worksheet.getCell("A7").border = { top: { style: 'thin' }, bottom: { style: 'thin' }, left: { style: 'thin' }, right: { style: 'thin' } };

    worksheet.getCell("B7").value = "SỐ HỒ SƠ";
    // tslint:disable-next-line: max-line-length
    worksheet.getCell("B7").style = { font: { bold: true, name: 'Times New Roman', size: 11 }, alignment: { horizontal: 'center', vertical: 'middle', wrapText: true }, fill: { type: 'pattern', pattern: 'darkVertical', fgColor: { argb: 'FFBFBFBF' }, bgColor: { argb: 'FFBFBFBF' } } };
    worksheet.getCell("B7").border = { top: { style: 'thin' }, left: { style: 'thin' }, right: { style: 'thin' } };
    worksheet.getCell("B8").border = { bottom: { style: 'thin' }, left: { style: 'thin' }, right: { style: 'thin' } };

    worksheet.getCell("C7").value = "VỀ VIỆC";
    // tslint:disable-next-line: max-line-length
    worksheet.getCell("C7").style = { font: { bold: true, name: 'Times New Roman', size: 11 }, alignment: { horizontal: 'center', vertical: 'middle', wrapText: true }, fill: { type: 'pattern', pattern: 'darkVertical', fgColor: { argb: 'FFBFBFBF' }, bgColor: { argb: 'FFBFBFBF' } } };
    worksheet.getCell("C7").border = { top: { style: 'thin' }, bottom: { style: 'thin' }, left: { style: 'thin' }, right: { style: 'thin' } };

    worksheet.getCell("D7").value = "TGQĐ HỒ SƠ";
    // tslint:disable-next-line: max-line-length
    worksheet.getCell("D7").style = { font: { bold: true, name: 'Times New Roman', size: 11 }, alignment: { horizontal: 'center', vertical: 'middle', wrapText: true }, fill: { type: 'pattern', pattern: 'darkVertical', fgColor: { argb: 'FFBFBFBF' }, bgColor: { argb: 'FFBFBFBF' } } };
    worksheet.getCell("D7").border = { top: { style: 'thin' }, bottom: { style: 'thin' }, left: { style: 'thin' }, right: { style: 'thin' } };

    worksheet.getCell("E7").value = "NGƯỜI ĐĂNG KÝ";
    // tslint:disable-next-line: max-line-length
    worksheet.getCell("E7").style = { font: { bold: true, name: 'Times New Roman', size: 11 }, alignment: { horizontal: 'center', vertical: 'middle', wrapText: true }, fill: { type: 'pattern', pattern: 'darkVertical', fgColor: { argb: 'FFBFBFBF' }, bgColor: { argb: 'FFBFBFBF' } } };
    worksheet.getCell("E7").border = { top: { style: 'thin' }, bottom: { style: 'thin' }, left: { style: 'thin' }, right: { style: 'thin' } };

    worksheet.getCell("F7").value = "TÊN ĐƠN VỊ";
    // tslint:disable-next-line: max-line-length
    worksheet.getCell("F7").style = { font: { bold: true, name: 'Times New Roman', size: 11 }, alignment: { horizontal: 'center', vertical: 'middle', wrapText: true }, fill: { type: 'pattern', pattern: 'darkVertical', fgColor: { argb: 'FFBFBFBF' }, bgColor: { argb: 'FFBFBFBF' } } };
    worksheet.getCell("F7").border = { top: { style: 'thin' }, bottom: { style: 'thin' }, left: { style: 'thin' }, right: { style: 'thin' } };

    for (var i = 1; i <= 6; i++) {
      worksheet.getCell(8, i).value = "(" + i + ")";
      worksheet.getCell(8, i).style = { font: { bold: false, name: 'Times New Roman', size: 11 }, alignment: { horizontal: 'center', vertical: 'middle', wrapText: true } };
      worksheet.getCell(8, i).border = { top: { style: 'thin' }, bottom: { style: 'thin' }, left: { style: 'thin' }, right: { style: 'thin' } };
    }
    worksheet.getRow(8).height = 15;

    for (var i = 0; i < listExport.length; i++) {
      var cellA = "A" + (9 + i);
      worksheet.getCell(cellA).value = (i + 1);
      worksheet.getCell(cellA).style = { font: { bold: false, name: 'Times New Roman', size: 11 }, alignment: { horizontal: 'center', vertical: 'middle', wrapText: true } };
      worksheet.getCell(cellA).border = { top: { style: 'thin' }, bottom: { style: 'thin' }, left: { style: 'thin' }, right: { style: 'thin' } };

      var cellA = "B" + (9 + i);
      worksheet.getCell(cellA).value = listExport[i].code;
      worksheet.getCell(cellA).style = { font: { bold: false, name: 'Times New Roman', size: 11 }, alignment: { horizontal: 'center', vertical: 'middle', wrapText: true } };
      worksheet.getCell(cellA).border = { top: { style: 'thin' }, bottom: { style: 'thin' }, left: { style: 'thin' }, right: { style: 'thin' } };

      var cellA = "C" + (9 + i);
      worksheet.getCell(cellA).value = listExport[i].procedureCode + " - " + listExport[i].procedureName;
      worksheet.getCell(cellA).style = { font: { bold: false, name: 'Times New Roman', size: 11 }, alignment: { horizontal: 'left', vertical: 'middle', wrapText: true } };
      worksheet.getCell(cellA).border = { top: { style: 'thin' }, bottom: { style: 'thin' }, left: { style: 'thin' }, right: { style: 'thin' } };

      var cellA = "D" + (9 + i);
      var textD = listExport[i].stringDateWork +
        "\n- Ngày tiếp nhận: " + (listExport[i].acceptedDate ? this.datePipe.transform(listExport[i].acceptedDate, 'dd/MM/yyyy HH:mm:ss') : '') +
        "\n- Hạn xử lý: " + (listExport[i].appointmentDate ? this.datePipe.transform(listExport[i].appointmentDate, 'dd/MM/yyyy HH:mm:ss') : '') +
        "\n- Ngày hẹn trả: " + (listExport[i].appointmentDate ? this.datePipe.transform(listExport[i].appointmentDate, 'dd/MM/yyyy HH:mm:ss') : '');
      if (listExport[i].completedDate) {
        textD = textD + "\n- Ngày có KQ: " + this.datePipe.transform(listExport[i].completedDate, 'dd/MM/yyyy HH:mm:ss');
      }
      if (listExport[i].returnedDate) {
        textD = textD + "\n- Ngày trả KQ: " + this.datePipe.transform(listExport[i].returnedDate, 'dd/MM/yyyy HH:mm:ss');
      }

      worksheet.getCell(cellA).value = textD;
      worksheet.getCell(cellA).style = { font: { bold: false, name: 'Times New Roman', size: 11 }, alignment: { horizontal: 'left', vertical: 'middle', wrapText: true } };
      worksheet.getCell(cellA).border = { top: { style: 'thin' }, bottom: { style: 'thin' }, left: { style: 'thin' }, right: { style: 'thin' } };

      var cellA = "E" + (9 + i);
      worksheet.getCell(cellA).value = this.getFullName(listExport[i].applicant?.data) + "\n" + this.generateAddress(listExport[i].applicant?.data);
      worksheet.getCell(cellA).style = { font: { bold: false, name: 'Times New Roman', size: 11 }, alignment: { horizontal: 'left', vertical: 'middle', wrapText: true } };
      worksheet.getCell(cellA).border = { top: { style: 'thin' }, bottom: { style: 'thin' }, left: { style: 'thin' }, right: { style: 'thin' } };

      var cellA = "F" + (9 + i);
      worksheet.getCell(cellA).value = listExport[i].agencyName;
      worksheet.getCell(cellA).style = { font: { bold: false, name: 'Times New Roman', size: 11 }, alignment: { horizontal: 'center', vertical: 'middle', wrapText: true } };
      worksheet.getCell(cellA).border = { top: { style: 'thin' }, bottom: { style: 'thin' }, left: { style: 'thin' }, right: { style: 'thin' } };
    }

    worksheet.getColumn('A').width = 5;
    worksheet.getColumn('B').width = 30;
    worksheet.getColumn('C').width = 40;
    worksheet.getColumn('D').width = 40;
    worksheet.getColumn('E').width = 30;
    worksheet.getColumn('F').width = 25;

    var tenFile = this.datePipe.transform(this.nowDate, 'dd_MM') + "_hosotrehan";
    // Save Excel File
    workbook.xlsx.writeBuffer().then((data: ArrayBuffer) => {
      const blob = new Blob([data], { type: EXCEL_TYPE });
      fs.saveAs(blob, tenFile + EXCEL_EXTENSION);
    });
  }

  // Cập nhật validate date trong form tìm kiếm
  validateForm() {
    // Chuyển đổi lại thời gian startDate / endDate
    this.startDate.setHours(0, 0, 0, 0);
    this.endDate.setHours(23, 59, 59, 999);

    let startTime = this.startDate.getTime();
    let endTime = this.endDate.getTime();
    let language = localStorage.getItem('language');

    let data = {
      errMessage: "",
      status: false
    }

    if (this.startDate == null || this.endDate == null) {
      data.errMessage = language == 'vi' ? 'Vui lòng nhập đầy đủ thông tin ngày tra' : 'Please enter complete information for the lookup';
    } else if(startTime > endTime){
      data.errMessage = language == 'vi' ? 'Vui lòng nhập thông tin từ ngày nhỏ hơn đến ngày' : 'Please enter information from date less than to date';
    } else {
      data.status = true;
    }
    return data;
  }

}

