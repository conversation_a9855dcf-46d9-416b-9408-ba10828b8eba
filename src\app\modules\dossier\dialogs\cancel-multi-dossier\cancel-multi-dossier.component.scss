.drag_upload_btn {
    position: relative;
    overflow: hidden;
    display: inline-block;
    width: 100%;
    text-align: center;
    align-self: center;
    border: 1px solid #9e9e9e96;
    border-style: dashed;
    cursor: pointer;
    z-index: 4;

    button {
        width: 100%;
        padding: 5em;

        a {
            color: #1E2F41;
            text-decoration: none;
            cursor: pointer;

            .txtUpload {
                text-decoration: none;
                font-weight: 500;
                cursor: pointer;
                align-self: center;
                color: #CE7A58;
            }

            &:hover {
                color: #1E2F41;
            }

            &:visited {
                color: #1E2F41;
            }
        }

        .mat-icon {
            color: #CE7A58;
            align-self: center;
            margin-right: .2em;
            padding-bottom: .2em;
        }
    }

    input[type=file] {
        font-size: 100px;
        position: absolute;
        left: 0;
        top: 0;
        opacity: 0;
        height: 100%;
        cursor: pointer;
    }
}

.file_drag_upload_preview {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
}

.list_uploaded {
    height: 2.5em;
    width: 32%;
    margin: 0 0.5% 1em 0.5%;
    background-color: #3e3e3e17;
    display: flex;
    border-radius: 4px;

    .file_icon {
        width: 2em;
        height: 2em;
        background-position: center;
        background-size: 170%;
        align-self: center;
        background-repeat: no-repeat;
    }
}

.btn_uploaded {
    padding: 1em !important;
}

.clear_file_queue {
    padding: 5em !important;
}

.file_uploaded {
    border: 1px solid #9e9e9e96;
    border-style: dashed;
}

.no_boder {
    border: none;
}

.file_name {
    font-style: medium;
    font-weight: normal;
    align-self: center;
    color: #1E2F41;
    float: left;
}

.delete_file {
    margin-left: auto;
    align-self: center;
    color: #1E2F41;
}

.file_control {
    width: 100%;
}

.res_uploadFile {
    justify-content: center;
}

.res_upload_btn {
    position: relative;
    overflow: hidden;
    display: inline-block;
    width: 100%;
    text-align: center;
    align-self: center;
    border: 1px solid #9e9e9e96;
    border-style: dashed;
    cursor: pointer;
    z-index: 4;
    margin-left: auto;
    margin-right: auto;

    button {
        width: 100%;

        a {
            color: #1E2F41;
            text-decoration: none;
            cursor: pointer;

            .txtUpload {
                text-decoration: none;
                font-weight: 500;
                cursor: pointer;
                align-self: center;
                color: #CE7A58;
            }
        }

        .mat-icon {
            color: #CE7A58;
            align-self: center;
            margin-right: .2em;
            padding-bottom: .2em;
        }

        &:hover {
            color: #1E2F41;
        }

        &:visited {
            color: #1E2F41;
        }
    }

    input[type=file] {
        font-size: 100px;
        position: absolute;
        left: 0;
        top: 0;
        opacity: 0;
        height: 100%;
        cursor: pointer;
    }

    .res_upload_preview {
        width: 100%;
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        padding-top: 1em;

        .list_uploaded {
            height: 2.5em;
            margin: 0 0.5% 1em 0.5%;
            background-color: #3e3e3e17;
            display: flex;
            border-radius: 4px;

            .file_icon {
                width: 2em;
                height: 2em;
                background-position: center;
                background-size: 170%;
                align-self: center;
                background-repeat: no-repeat;
            }
        }
    }
}

.marginbottom{
    margin-bottom: 25px;
}

.lbl.title-weight{
    font-weight: 500;

    .downloadBtn{
        margin-left: 0.5em;
        // background-color: #fff;
        // color: #000;
        // height: 1.5rem;
        cursor: pointer;
        .download-text{
            color: #ce7a58;
            font-weight: 500;
        }
        .mat-icon{
            margin-right: 2px;
            vertical-align: sub;
        }
    }
}

.dialog_title {
    font-style: normal;
    font-weight: 500;
    font-size: 20px;
    color: #ce7a58;
}

.dialog_code {
    font-style: normal;
    font-weight: 500;
    font-size: 16px;
    color: #ce7a58;
}

::ng-deep {

    .dialog_content {
        font-size: 15px;

        &::-webkit-scrollbar-track {
            box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.1);
            background-color: #f5f5f5;
        }

        &::-webkit-scrollbar {
            width: 5px;
            background-color: #f5f5f5;
        }

        &::-webkit-scrollbar-thumb {
            box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.1);
            background-color: #44444450;
        }
        
        .highlight {
            color: #ce7a58;
        }
    }

    .close-button {
        float: right;
        top: -24px;
        right: -24px;
    }

    .applyBtn {
        margin-top: 1em;
        background-color: #ce7a58;
        color: #fff;
        height: 3em;
    }

    .rejectBtn {
        margin-top: 1em;
        background-color: #fff;
        color: #ce7a58;
        border: 1px solid #ce7a58;
        height: 3em;
    }

    .commentEditor {
        margin: 1em 0;

        .lbl {
            font-weight: 500;
            font-size: 15px;
            line-height: 27px;
            color: #1e2f41;
        }

        .editorLabel {
            font-weight: 500;
            color: #1e2f41;
            margin-bottom: 0.2em;
        }

        .customCKEditor {
            .ck.ck-toolbar {
                border-radius: 4px 4px 0 0;
                border: 1px solid #dedede;
            }

            .ck.ck-content {
                background-color: #eaebeb;
                border: none;
                border-radius: 0 0 4px 4px;
            }

            .ck-editor__editable {
                min-height: 5em !important;
            }
        }

        .chkGroup {
            margin: 1em 0;
            display: flex;
            flex-direction: column;
            .mat-checkbox-indeterminate.mat-accent .mat-checkbox-background,
            .mat-checkbox-checked.mat-accent .mat-checkbox-background {
                background-color: #ce7a58;
            }
        }

        .errorMsg {
            font-size: 12px !important;
            float: right;
            display: flex;
            color: #ce7a58;
            margin: .5em 0;

            span {
                margin-left: auto !important;
            }

            .err {
                background-color: #f2a63494;
                border-radius: 50%;
                width: 1.2em;
                height: 1.2em;
                justify-content: center;
                display: flex;
                margin-left: 0.5em;
                margin-top: 0.2em;
                
                .mat-icon {
                    color: #ce7a58;
                    vertical-align: middle;
                    align-self: center;
                    transform: scale(0.6);
                    margin-left: .05em;
                }
            }
        }
    }

    .sendEmailSMS {
        border-top: 1px solid #dadada;
        .chkGroup {
            margin: 1em 0;
            display: flex;
            flex-direction: column;
            .mat-checkbox-indeterminate.mat-accent .mat-checkbox-background,
            .mat-checkbox-checked.mat-accent .mat-checkbox-background {
                background-color: #ce7a58;
            }
        }

        .mat-form-field {
            width: 100%;

            .textLimit {
                float: right;
                padding-top: 0.2em;
            }
        }
    }
}