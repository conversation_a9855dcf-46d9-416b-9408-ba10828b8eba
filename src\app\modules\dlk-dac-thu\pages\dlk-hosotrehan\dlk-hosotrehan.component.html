<h2><PERSON><PERSON> <PERSON><PERSON><PERSON> hồ sơ trễ hạn</h2>
<div class="prc_searchbar">
  <div fxLayout="row"
       fxLayout.xs="row"
       fxLayout.sm="row"
       class="formFieldOutline"
       fxLayoutAlign="space-between">
  </div>
  <div fxLayout="row"
       fxLayout.xs="row"
       fxLayout.sm="row"
       class="formFieldOutline"
       fxLayoutAlign="space-between">
    <mat-form-field appearance="outline"
                    fxFlex.gt-sm="33"
                    fxFlex.gt-xs="100"
                    fxFlex='grow'>
      <mat-label>Nhập từ khóa</mat-label>
      <input type="text"
             [(ngModel)]="paramsQuery.keyword"
             matInput>
    </mat-form-field>
    <mat-form-field appearance="outline"
                    fxFlex.gt-sm="33"
                    fxFlex.gt-xs="49.5"
                    fxFlex='grow'>
      <mat-label><PERSON><PERSON><PERSON> vị tiếp nhận</mat-label>
      <mat-select msInfiniteScroll
                  (infiniteScroll)="getAgencyScroll()"
                  [complete]="totalPagesAgencyAccept <= currentPageAgencyAccept+1"
                  [(ngModel)]="paramsQuery.agency"
                  (selectionChange)="changeAgencyAccept()">
        <mat-option>
          <ngx-mat-select-search ngModel
                                 (ngModelChange)="searchAngency($event)"
                                 placeholderLabel="">
          </ngx-mat-select-search>
        </mat-option>
        <mat-option value="">Tất cả</mat-option>
        <mat-option *ngFor="let item of listAgencyAccept"
                    [value]="item.id">{{item.name}}</mat-option>
      </mat-select>
    </mat-form-field>
    <mat-form-field appearance="outline"
                    fxFlex.gt-sm="33"
                    fxFlex.gt-xs="49.5"
                    fxFlex='grow'>
      <mat-label>Hình thức giải quyết</mat-label>
      <mat-select [(ngModel)]="paramsQuery.loaiGiaiQuyet">
        <mat-option *ngFor="let item of listHinhThucNhan"
                    [value]="item.value.toString()">
          {{item.text}}
        </mat-option>
      </mat-select>
    </mat-form-field>
  </div>
  <div fxLayout="row"
       fxLayout.xs="row"
       fxLayout.sm="row"
       class="formFieldOutline"
       fxLayoutAlign="space-between">
    <mat-form-field appearance="outline"
                    fxFlex.gt-sm="33"
                    fxFlex.gt-xs="49.5"
                    fxFlex='grow'>
      <mat-label>Loại ngày tiếp nhận/xử lý</mat-label>
      <mat-select [(ngModel)]="paramsQuery.loaiNgay">
        <mat-option *ngFor="let item of listLoaiNgay"
                    [value]="item.value.toString()"
                    [disabled]="item.isDisabled">
          {{item.text}}
        </mat-option>
      </mat-select>
    </mat-form-field>
    <div appearance="outline"
         fxFlex.gt-sm="33"
         fxFlex.gt-xs="49.5"
         fxFlex='grow'>
      <div fxLayout="row"
           fxLayout.xs="row"
           fxLayout.sm="row"
           class="formFieldOutline"
           fxLayoutAlign="space-between">

        <mat-form-field appearance="outline"
                        fxFlex.gt-sm="49.5"
                        fxFlex.gt-xs="49.5"
                        fxFlex='grow'>
          <mat-label>Từ ngày</mat-label>
          <input matInput
                 [matDatepicker]="pickerAcceptFrom"
                 [(ngModel)]="startDate">
          <mat-datepicker-toggle matSuffix
                                 [for]="pickerAcceptFrom"></mat-datepicker-toggle>
          <mat-datepicker #pickerAcceptFrom></mat-datepicker>
        </mat-form-field>
        <mat-form-field appearance="outline"
                        fxFlex.gt-sm="49.5"
                        fxFlex.gt-xs="49.5"
                        fxFlex='grow'>
          <mat-label>Đến ngày</mat-label>
          <input matInput
                 [matDatepicker]="pickerAcceptTo"
                 [(ngModel)]="endDate">
          <mat-datepicker-toggle matSuffix
                                 [for]="pickerAcceptTo"></mat-datepicker-toggle>
          <mat-datepicker #pickerAcceptTo></mat-datepicker>
        </mat-form-field>
      </div>
    </div>
    <div appearance="outline" fxFlex.gt-sm="33" fxFlex.gt-xs="49.5" fxFlex='grow'></div>
  </div>
  <div fxLayout="row"
       fxLayout.xs="column"
       fxLayout.sm="row"
       fxLayoutAlign="end"
       style="padding-bottom: 1em;">
    <button mat-flat-button
            fxFlex.lt-md="22"
            fxFlex.md="20"
            fxFlex.gt-md="12"
            fxFlex.gt-xs="49.5"
            fxFlex='grow'
            class="btn-search"
            type="submit"
            (click)="thongKe()"
            style="background-color: #ce7a58;color: white;">
      <mat-icon class="iconStatistical">bar_chart</mat-icon><span>Thống kê</span>
    </button>
    <div fxFlex='1'></div>
    <button mat-flat-button
            fxFlex.lt-md="22"
            fxFlex.md="20"
            fxFlex.gt-md="12"
            fxFlex.gt-xs="49.5"
            fxFlex='grow'
            class="btn-download-excel"
            style="background-color: #127905;color: white;"
            (click)="xuatExcel()">
      <mat-icon class="iconStatistical">cloud_download</mat-icon>
      <span> Xuất excel</span>
      <mat-progress-bar mode="indeterminate"
                        *ngIf="waitingDownloadExcel"></mat-progress-bar>
    </button>
    <div fxFlex='1'></div>
  </div>
</div>

<div fxLayout="row"
     fxLayoutAlign="center">
  <div class="frm_main"
       fxFlex="grow">
    <div class="logbookTbl">
      <div class="logbookOnlyTbl">
        <table class="table">
          <tr>
            <th>STT</th>
            <th style="width: 200px;">Số hồ sơ</th>
            <th>Về việc</th>
            <th style="width: 280px;">TGQĐ hồ sơ</th>
            <th>Người đăng ký</th>
            <th>Tên đơn vị</th>
            <th>Thao tác</th>
          </tr>
          <tr *ngFor="let item of listHoSo;;let i = index ">
            <td>{{ paramsQuery.page*paramsQuery.size + i+1}}</td>
            <td style="cursor: pointer;color: #ce7a58;">{{item.code}}</td>
            <td>{{item.procedureCode}} - {{item?.procedureName}}</td>
            <td style="text-align: left !important; font-style: italic;">
              <div>
                <span style="color: #3300FF"> {{item?.stringDateWork}}</span><br>
                - <span>Ngày tiếp nhận</span>: {{item?.acceptedDate | date:'dd/MM/yyyy HH:mm:ss'}} <br>
                - <span>Hạn xử lý</span>: <span style="color: #CC00CC">
                  {{item?.appointmentDate | date:'dd/MM/yyyy HH:mm:ss'}}
                </span><br>
                - <span>Ngày hẹn trả</span>: {{item?.appointmentDate | date:'dd/MM/yyyy HH:mm:ss'}} <br>
                <span *ngIf="item?.completedDate">
                  - <span>Ngày có KQ</span>:
                  {{item?.completedDate | date:'dd/MM/yyyy HH:mm:ss'}}
                  <br>
                </span>
                <span *ngIf="item?.returnedDate">
                  - <span>Ngày trả KQ</span>:
                  {{item?.returnedDate | date:'dd/MM/yyyy HH:mm:ss'}}
                  <br>
                </span>
              </div>
            </td>
            <td>
              <span style="font-weight: bold;">{{getFullName(item.applicant?.data)}}</span> 
              <br />
              <span>{{generateAddress(item.applicant?.data)}}</span>
            </td>
            <td>{{item.agencyName}}</td>
            <td>
              <button mat-icon-button
                      [matMenuTriggerFor]="actionMenu">
                <mat-icon>more_vert</mat-icon>
              </button>
              <mat-menu #actionMenu="matMenu"
                        xPosition="before">
                <!-- <button mat-menu-item
                        class="menuAction"
                        (click)="viewProcess(item.id, item.code)">
                  <mat-icon>timeline</mat-icon>
                  <span>Xem quy trình</span>
                </button>
                <button mat-menu-item
                        class="menuAction"
                        (click)="addApologyText(item.id)">
                  <mat-icon>assignment_returned</mat-icon><span i18n>Tải xuống mẫu văn bản xin lỗi</span>
                </button> -->
                <button mat-menu-item
                        class="menuAction"
                        (click)="signApologyText(item.id)">
                  <mat-icon>note_add</mat-icon><span>Cập nhật văn bản xin lỗi</span>
                </button>
              </mat-menu>
            </td>
          </tr>
        </table>
        <div class="frm_Pagination">
          <ul class="temp_Arr">
            <li
                *ngFor="let item of listHoSo  | paginate: {itemsPerPage: paramsQuery.size, currentPage: page, totalItems: countResult, id: 'pgnx'}">
            </li>
          </ul>
          <div class="pageSize">
            <span i18n>Hiển thị </span>
            <mat-form-field appearance="outline">
              <mat-select [(ngModel)]="paramsQuery.size"
                          (valueChange)="paginate(0)">
                <mat-option *ngFor='let opt of pgSizeOptions;'
                            [value]="opt">{{opt}}</mat-option>
              </mat-select>
            </mat-form-field>
            <span><span i18n>trên</span> {{countResult}} <span i18n>bản ghi</span></span>
          </div>
          <div class="control">
            <pagination-controls id="pgnx"
                                 (pageChange)="page = $event; paginate(page - 1)"
                                 responsive="true"
                                 previousLabel=""
                                 nextLabel="">
            </pagination-controls>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>