import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { ApiProviderService } from 'src/app/core/service/api-provider.service';

@Injectable({
  providedIn: 'root'
})
export class Vnpost5343Service {

  constructor(
    private http: HttpClient,
    private apiProviderService: ApiProviderService,
  ) { }

  private adapter = this.apiProviderService.getUrl('digo', 'adapter');
  private padman = this.apiProviderService.getUrl('digo', 'padman');

  postOrder(requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.post<any>(this.padman + '/vnpost-5343/order/post',requestBody, { headers });
  }

  getDossierVNPOSTFee(search, requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.post<any>(this.adapter + '/service/vnpost-5343/get-price' + search, requestBody, { headers });
  }

}
