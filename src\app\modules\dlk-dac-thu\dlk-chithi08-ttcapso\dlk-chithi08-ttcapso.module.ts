import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { NgxPrinterModule } from 'ngx-printer';
import { SharedModule } from 'src/app/shared/shared.module';
import { DlkChithi08TtcapsoRoutingModule } from './dlk-chithi08-ttcapso-routing.module';
import { DlkChithi08TtcapsoComponent } from './dlk-chithi08-ttcapso.component';

@NgModule({
  declarations: [DlkChithi08TtcapsoComponent],
  imports: [
    CommonModule,
    DlkChithi08TtcapsoRoutingModule,
    SharedModule,
    NgxPrinterModule.forRoot({ printOpenWindow: true }),
  ]
})
export class DlkChithi08TtcapsoModule { }
