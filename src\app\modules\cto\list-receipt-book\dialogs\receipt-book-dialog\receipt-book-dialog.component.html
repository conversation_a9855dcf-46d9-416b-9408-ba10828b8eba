<div class="addReceiptBook">
    <button mat-icon-button class="closeBtn" (click)="onDismiss()">
        <mat-icon>close</mat-icon>
    </button>
    <h3 class="title">{{ formOption?.title[data?.type][localeId] }}</h3>
    <form [formGroup]="receiptBook" (submit)="onConfirm()">
        <div fxLayout="row" fxLayout.xs="column" fxLayoutAlign="space-between">
            <mat-form-field appearance="outline" fxFlex="100" fxFlex.xs="100">
                <mat-label>Tên sổ</mat-label>
                <input matInput formControlName="name" required>
            </mat-form-field>                       
        </div>
        <div fxLayout="row" fxLayout.xs="column" fxLayoutAlign="space-between">
            <mat-form-field appearance="outline" fxFlex="32" fxFlex.xs="100">
                <mat-label><PERSON><PERSON> sổ</mat-label>
                <input matInput formControlName="code" required>
            </mat-form-field>
            <mat-form-field appearance="outline" fxFlex="32" fxFlex.xs="100">
                <mat-label>Năm</mat-label>
                <input matInput formControlName="year" maxlength="4" pattern="^[0-9]{4}$" required (keypress)="preventInvalidKeys($event)">
                <mat-error *ngIf="receiptBook.get('year')?.hasError('required')">
                  Năm là bắt buộc
                </mat-error>
                <mat-error *ngIf="receiptBook.get('year')?.hasError('pattern')">
                  Năm phải gồm 4 chữ số
                </mat-error>
              </mat-form-field>
                 
            <mat-form-field appearance="outline" fxFlex="32" fxFlex.xs="100">
                <mat-label>Số thứ tự</mat-label>
                <input matInput formControlName="orderNumber">
            </mat-form-field>          
        </div>
        <div fxLayout="row" fxLayout.xs="column" fxLayoutAlign="space-between">
            <digo-select-box label="Loại sổ" type='tag' [required]="true" [disabled]="formOption?.disable" [parent]="formOption?.ledgerTypeId"
                             [formControl]="receiptBook.controls['type']" fxFlex="49" fxFlex.xs="100">
            </digo-select-box>          
            <mat-form-field appearance="outline" fxFlex="49" fxFlex.xs="100">
                <mat-label>Trạng thái</mat-label>
                <mat-select formControlName="status" [disabled]="formOption?.disable" required>
                  <mat-option [value]="true">Mở</mat-option>
                  <mat-option [value]="false">Đóng</mat-option>
                </mat-select>
                <mat-error *ngIf="receiptBook.get('status')?.hasError('required') && receiptBook.get('status')?.touched">
                  Vui lòng chọn trạng thái
                </mat-error>
              </mat-form-field>              
        </div>
        <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="column" fxLayoutAlign="center">
            <button mat-button *ngIf="!formOption?.disable" class="addBtn" type="submit" [disabled]="receiptBook.invalid">
                Đồng ý
            </button>
        </div>
    </form>
</div>