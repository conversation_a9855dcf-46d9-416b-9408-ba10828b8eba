import { Injectable } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { ApiProviderService } from 'src/app/core/service/api-provider.service';
import { Observable } from 'rxjs';
import { TOUCH_BUFFER_MS } from '@angular/cdk/a11y';
import { Pattern, PatternListResponse, PatternResponse } from '../../schema/pattern';

@Injectable({
  providedIn: 'root'
})
export class ConfigureReceiptCodeService {

  constructor(
    private dialog: MatDialog,
    private http: HttpClient,
    private apiProviderService: ApiProviderService
  ) { }

  private getProcedureURL = this.apiProviderService.getUrl('digo', 'basepad');
  private getAgencyURL = this.apiProviderService.getUrl('digo', 'basedata');
  private getSector = this.apiProviderService.getUrl('digo', 'basepad');
  private getProcost = this.apiProviderService.getUrl('digo', 'basepad');
  private getAdapter = this.apiProviderService.getUrl('digo', 'adapter');
  private getBasecat = this.apiProviderService.getUrl('digo', 'basecat');

  getListConfigure(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.getProcost + '/configure-receipt-code/' + searchString, {headers});
  }

  postConfigure(data): Observable<any> {
    // return this.http.post<any>(this.getSector + '/procost-type/' , data);
    return this.http.post<any>(this.getProcost + '/configure-receipt-code' , data);
  }

  postConfigureNew(data): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.post<any>(this.getProcost + '/configure-receipt-code/add-configure-receipt-code'+ data, headers);
  }

  deleteConfigure(id): Observable<any> {
    // return this.http.delete(this.getProcost + '/procost-type/' + id);
    return this.http.delete(this.getProcost + '/configure-receipt-code/' + id);
  }

  updateConfigure(id, data): Observable<any> {
    // return this.http.put<any>(this.getProcost + '/procost-type/' + id, data);
    return this.http.put<any>(this.getProcost + '/configure-receipt-code/' + id, data);
  }

  getListReceiptCode(options?: {
    keyword?: string,
    page?: number,
    size?: number
  }): Observable<PatternListResponse> {
    // return this.http.put<any>(this.getProcost + '/procost-type/' + id, data);
    return this.http.get<PatternListResponse>(this.getBasecat + '/pattern?' + "keyword=" + encodeURIComponent(options.keyword) + "&page=" + options.page + "&size=" + options.size);
  }

  // getListConfigureSync(searchString): Observable<any> {
  //   let headers = new HttpHeaders();
  //   headers = headers.set('Accept-Language', localStorage.getItem('language'));
  //   // return this.http.get(this.getProcost + '/procost/delete-type' + searchString, {headers});
  //   return this.http.get(this.getAdapter + '/npadsvc/--fee-service' + searchString, {headers});
  // }
}
