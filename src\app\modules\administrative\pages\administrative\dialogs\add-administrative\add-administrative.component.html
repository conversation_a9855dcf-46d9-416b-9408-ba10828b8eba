<button mat-icon-button class="close-button" (click)="onDismiss()">
    <mat-icon>close</mat-icon>
</button>
<h3 class="dialog_title" mat-dialog-title i18n>Thêm mới sổ hành ch<PERSON>h công</h3>
<form [formGroup]="addForm" class="addForm edit" (submit)="onConfirm()" id="ngAddForm">
    <div  *ngFor="let field of fieldArray; let i = index">
    <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="column" class="marginRow">
        <mat-form-field appearance="outline" fxFlex.gt-sm="98" fxFlex='98'>
            <mat-label i18n>Mã sổ</mat-label>
            <input type="text" matInput  (change)="changeCode(0 , $event)" value="{{fieldArray[0].code}}">
        </mat-form-field>
    </div>
    <br>
    <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="column" class="marginRow">
        <mat-form-field appearance="outline" fxFlex.gt-sm="98" fxFlex='98'>
            <mat-label i18n>Tên sổ</mat-label>
            <input type="text" matInput (change)="changeName(0 , $event)" value="{{fieldArray[0].name}}">
        </mat-form-field>
    </div>
    <combobox-lazy-load #searchAgency  fxLayout="row" fxLayout.xs="column" fxLayout.sm="column" class="marginRow"
                            *ngIf="showPublicAdministrativeAgency && showPublicAdministrativeAgency.enable"
                            hasSearch="false"
                            defaultValue=""
                            [defaultValue]="agencyId"
                            label="Cơ quan thực hiện"
                            searchPlaceholder="Nhập từ khóa..."
                            size="10"
                            class="none-pb w-100"
                            (onLoad)="loadAgency($event)">
    </combobox-lazy-load>
    <br>
    <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="row">
        <div fxFlex.gt-sm="98" fxFlex.lt-md="98" class="bn-left">
            <mat-form-field appearance="outline" fxFlex='grow'>
                <mat-label i18n>Trạng thái</mat-label>
                <mat-select formControlName="status">
                    <mat-option *ngFor='let element of status ; let i = index' value="{{element.status}}" formControlName="status">
                        {{ element.name }}</mat-option>
                </mat-select>
            </mat-form-field>
        </div>
    </div>
    <br>
    <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="row">
        <div fxFlex.gt-sm="26" fxFlex.lt-md="26" class="bn-left" >
            <mat-checkbox class="chkBranch" color="warn" formControlName="numberIncreaseAccordingBook">
                <span>Số nhảy theo sổ</span>
            </mat-checkbox>
        </div>
        <div fxFlex.gt-sm="26" fxFlex.lt-md="26" class="bn-left" >
            <mat-checkbox class="chkBranch" color="warn" formControlName="resetAccordingYear">
                <span>Reset theo năm</span>
            </mat-checkbox>
        </div>
    </div>
    </div>
    <div fxLayout="row" fxLayoutAlign="center" fxLayout.xs="row" fxLayout.sm="row" class="divAddBtn">
        <button [disabled]="isSubmit" mat-flat-button fxFlex='grow' class="saveBtn" type="submit" form="ngAddForm">
            <span i18n>Lưu lại</span>
        </button>
    </div>
</form>