import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SharedModule } from 'src/app/shared/shared.module';
import { AdministrativeRoutingModule } from './administrative-routing.module';
import { AdministrativeComponent } from './administrative.component';
import { AddAdministrativeComponent } from './dialogs/add-administrative/add-administrative.component';
import { UpdateAdministrativeComponent } from './dialogs/update-administrative/update-administrative.component';
import { DeleteAdministrativeComponent } from './dialogs/delete-administrative/delete-administrative.component';

@NgModule({
  declarations: [AdministrativeComponent, AddAdministrativeComponent, UpdateAdministrativeComponent, DeleteAdministrativeComponent],
  imports: [
    CommonModule,
    SharedModule,
    AdministrativeRoutingModule
  ]
})
export class AdministrativeModule { }
