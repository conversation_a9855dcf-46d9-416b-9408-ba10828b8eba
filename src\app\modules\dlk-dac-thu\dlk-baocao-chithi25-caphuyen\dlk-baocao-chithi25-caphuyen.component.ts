import {
  Component,
  OnInit,
  ChangeDetectorRef,
  AfterViewInit,
  ViewChild,
  OnDestroy,
} from '@angular/core';
import { FormGroup, FormControl } from '@angular/forms';
import { Router } from '@angular/router';
import { EnvService } from 'src/app/core/service/env.service';
import { MatTableDataSource } from '@angular/material/table';
import { DatePipe } from '@angular/common';
import { ExportExcelService } from 'src/app/data/service/export-excel/export-excel.service';
import { MainService } from 'src/app/data/service/main/main.service';
import { ProcedureService } from 'src/app/data/service/procedure/procedure.service';
import { takeUntil } from 'rxjs/operators';
import { ReplaySubject, Subject } from 'rxjs';
import { ActivatedRoute } from '@angular/router';
import { DossierService } from 'src/app/data/service/dossier/dossier.service';
import { NgxPrinterService } from 'ngx-printer';
import { SnackbarService } from 'src/app/data/service/snackbar/snackbar.service';
import { MatSelect } from '@angular/material/select';
import { ReportService } from 'data/service/report/report.service';
import { DeploymentService } from 'data/service/deployment.service';
import * as tUtils from 'src/app/data/service/thoai.service';
import { CookieService } from 'ngx-cookie-service';
import { DLKStatisticsService } from 'src/app/data/service/dlk-statistics/dlk-statistics.service';
import { StatisticsService } from 'src/app/data/service/statistics/statistics.service';
import { Workbook } from 'exceljs';
import * as fs from 'file-saver';
export interface Agency {
  id: string;
  name: string;
}
import {
  DossierDetail25DialogModel,
  DossierDetailComponent,
  procedureDetailDialogModel,
} from '../dialogs/view-detail.component';
import { MatDialog } from '@angular/material/dialog';
@Component({
  selector: 'app-dlk-baocao-chithi25-caphuyen',
  templateUrl: './dlk-baocao-chithi25-caphuyen.component.html',
  styleUrls: [
    './dlk-baocao-chithi25-caphuyen.component.scss',
    '/src/app/app.component.scss',
    '/src/app/shared/scss/form-field-outline.scss',
  ],
})
export class DlkBaocaoChithi25CapHuyenComponent
  implements OnInit, AfterViewInit, OnDestroy
{
  nowDate = tUtils.newDate();

  // tslint:disable-next-line: max-line-length
  toDate =
    this.nowDate.getFullYear() +
    '-' +
    (this.nowDate.getMonth() + 1 <= 9
      ? '0' + (this.nowDate.getMonth() + 1)
      : this.nowDate.getMonth() + 1) +
    '-' +
    (this.nowDate.getDate() <= 9
      ? '0' + this.nowDate.getDate()
      : this.nowDate.getDate());
  // tslint:disable-next-line: max-line-length
  fromDate =
    this.nowDate.getFullYear() +
    '-' +
    (this.nowDate.getMonth() + 1 <= 9
      ? '0' + (this.nowDate.getMonth() + 1)
      : this.nowDate.getMonth() + 1) +
    '-01';

  keyword = '';
  config = this.envService.getConfig();

  selectedLang = localStorage.getItem('language') || 'vi';
  selectedLangId = Number(localStorage.getItem('languageId')) || 228;
  pageTitle = {
    vi: 'Báo cáo chị thị 25 cấp cấp huyện',
    en: 'Report according to the template of Directive 25 for the entire province',
  };

  protected onDestroy = new Subject<void>();
  // =================== old
  env: any = this.deploymentService.getAppDeployment()?.env;
  ELEMENTDATA: any[] = [];
  dataSource: MatTableDataSource<any>;
  // listSector: Array<any> = [];
  keySearchSectorAgency = '';
  // Search Sector
  keywordSector = '';
  totalPagesSector = 0;
  currentPageSector = 0;
  pageSizeSector = 100;
  searchString = '';

  parentAgency = '';
  agencyId = '';
  agencyName = '';
  userAgency?: any = JSON.parse(localStorage.getItem('userAgency'));
  userAgencyCount?: any =
    JSON.parse(localStorage.getItem('listAgencyUser'))?.length || 0;
  ELEMENTDATAALL: any[] = [];
  dataSourceAll: MatTableDataSource<any>;

  listHoSoCapSo = [];
  listHoSoCapTinh = [];
  listHoSoCapHuyen = [];
  TongCapSo: any;
  TongCapSoLK: any;
  TongCapTinh: any;
  TongCapHuyen: any;
  TongCapTinhLK: any;
  TongCapHuyenLK: any;
  listHoSo = [];
  listHoSoLK = [];
  procedureAgencyLevel =
    this.deploymentService.env.statistics.procedureAgencyLevel;
  Agency = this.deploymentService.env.OS_DLK;
  size = 10;
  pageIndex = 1;
  page = 1;
  countResult = 0;
  pgSizeOptions = this.config.pageSizeOptions;
  isAdmin?: boolean = sessionStorage.getItem('oneGateAdminMaster') === '1';
  paramsQuery = {
    fromDate: '',
    toDate: '',
    fromLKDate: '',
    toLKDate: '',
    agencyId: null,
    page: 0,
  };
  paramsDossier = {
    page: 0,
    size: 10,
    fromDate: null,
    toDate: null,
    agencyId: null,
    applyMethodId: null,
    receivingKind: null,
    hinhThucNop: null,
    keyword: '',
    dossierStatusId: null,
    procedureLevelId: null,
    code: '',
  };

  listAgencyAccept = [];
  listAgency = [];
  listAgencyBC = [];
  keywordAgencyAccept = '';
  totalPagesAgencyAccept = 0;
  currentPageAgencyAccept = 0;
  pageSizeAgencyAccept = 50;
  startDateCumulative = new Date();
  endDateCumulative = new Date();
  startDate = new Date();
  endDate = new Date();
  flagTTPVHCC = true;
  timeOutAgencyAccept: any = null;
  listSectorfillter: any[] = [];
  listSector: any[] = [];
  listProcedure: any[] = [];
  listProcedurefillter: any[] = [];
  listSectorProcedure: any = [];
  ListMain = [];
  totalPagesProcedure = 0;
  currentPageProcedure = 0;
  searchProcedureKeyword = '';
  listHinhThucNhan: any[] = [];
  EXCEL_EXTENSION = '.xlsx';
  EXCEL_TYPE =
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8';
  @ViewChild('procedureMatSelectInfiniteScroll', { static: true })
  procedureMatSelectInfiniteScroll: MatSelect;

  constructor(
    private envService: EnvService,
    public datepipe: DatePipe,
    private mainService: MainService,
    private cdRef: ChangeDetectorRef,
    private procedureService: ProcedureService,
    private datePipe: DatePipe,
    private deploymentService: DeploymentService,
    private dlkStatisticService: DLKStatisticsService,
    private statisticsService: StatisticsService,
    private reportService: ReportService,
    private dialog: MatDialog,
    private snackbar: SnackbarService
  ) {
    this.dataSource = new MatTableDataSource(this.ELEMENTDATA);
    this.dataSourceAll = new MatTableDataSource(this.ELEMENTDATAALL);
  }
  ngOnInit(): void {
    this.mainService.setPageTitle(
      this.pageTitle[localStorage.getItem('language')]
    );
    this.startDate = new Date(this.fromDate);
    this.startDateCumulative = new Date(this.fromDate);
    this.endDate = new Date(this.toDate);
    if (this.userAgency !== null && this.userAgency !== undefined) {
      if (!!this.userAgency.parent && !!this.userAgency.parent.id) {
        this.parentAgency = this.userAgency.parent.id;
      } else {
        this.parentAgency = this.userAgency.id;
      }
      this.parentAgency = this.Agency.rootAgencyId; // dlk
      this.keySearchSectorAgency = '&agency-id=' + this.agencyId;
      this.searchString = '?arr-parent-id=' + this.parentAgency;
    }
  }

  ngAfterViewInit() {
    this.cdRef.detectChanges();
    this.getListAgencyAccept(this.Agency.rootAgencyId, '', 0, 10000);
    // this.getProcedureByAgencyIdDLK()
  }

  ngOnDestroy() {
    this.onDestroy.next();
    this.onDestroy.complete();
  }
  colToLetter(number) {
    let result = '';
    // number = number - 1; // If starting from 1
    do {
      const letter = String.fromCharCode(65 + (number % 26));
      result = letter + result;
      number = Math.floor(number / 26) - 1;
    } while (number >= 0);
    return  result.toLowerCase();
  }
  validateForm() {
    let language = localStorage.getItem('language');

    let data = {
      errMessage: "",
      status: false
    }
    if (this.startDate == null || this.endDate == null) {
      data.errMessage = language == 'vi' ? 'Vui lòng nhập đầy đủ thông tin ngày tháng bắt đầu và kết thúc' : 'Please enter complete information for the start and end dates.';
      this.snackbar.openSnackBar(0, data.errMessage, '', 'error_notification', this.config.expiredTime);

      return 0;
    } else if(this.startDate.getTime() > this.endDate.getTime()) {
      data.errMessage = language == 'vi' ? 'Ngày bắt đầu không được lớn hơn ngày kết thúc' : 'The start date cannot be later than the end date.'
      this.snackbar.openSnackBar(0, data.errMessage, '', 'error_notification', this.config.expiredTime);

      return 0;
    }else if (this.startDateCumulative == null || this.endDateCumulative == null) {
      data.errMessage = language == 'vi' ? 'Vui lòng nhập đầy đủ thông tin ngày tháng bắt đầu lũy kế  và kết thúc lũy kế' : 'Please enter complete information for the start and end dates.';
      this.snackbar.openSnackBar(0, data.errMessage, '', 'error_notification', this.config.expiredTime);

      return 0;
    } else if(this.startDateCumulative.getTime() > this.endDateCumulative.getTime()) {
      data.errMessage = language == 'vi' ? 'Ngày bắt đầu lũy kế không được lớn hơn ngày kết thúc lũy kế' : 'The start date cannot be later than the end date.'
      this.snackbar.openSnackBar(0, data.errMessage, '', 'error_notification', this.config.expiredTime);

      return 0;
    }else{
      return 1;
    }
  }
  thongKe() {
    if(this.validateForm() == 1){
      this.waitingDownloadExcel = true;
      this.paramsQuery.page = 0;
      this.page = 1;
      this.getListHoSo();
    }
  }
  paginate(event) {
    this.paramsQuery.page = event;
    // this.getListHoSo();
  }

  ViewDetail(i, show) {
    this.ListMain[i].show = !show;
  }

  getListProcedureofDossier() {
    let lstprocedure = [];
    this.listSector = [];
    this.listProcedure = [];
    // Sử dụng Map để lưu các sector với key là sectorId
    const sectorMap = new Map();

    this.dlkStatisticService
      .getListProcedureByAgencyDLK('?agency-id=' + this.agencyId)
      .subscribe(
        (res) => {
          this.listSectorProcedure = res.content;
          if (this.listSectorProcedure.length > 0) {
            this.listSectorProcedure.forEach((item) => {
              // Lưu thông tin thủ tục, với item.status: 1 (mở) hoặc 0 (đóng)
              lstprocedure.push({
                sectorName: item.sectorName,
                sectorId: item.sectorId,
                procedureId: item.id,
                procedureName: item.name,
                MucDo: item.procedureLevelName,
                status: item.status
              });

              // Nếu sector chưa tồn tại thì thêm vào (dù thủ tục có trạng thái đóng)
              if (!sectorMap.has(item.sectorId)) {
                sectorMap.set(item.sectorId, {
                  sectorId: item.sectorId,
                  sectorName: item.sectorName,
                  status: item.status
                });
              } else {
                // Nếu sector đã tồn tại và tên khác nhau
                let existing = sectorMap.get(item.sectorId);
                if (existing.sectorName !== item.sectorName) {
                  // Nếu thủ tục hiện tại mở (status === 1) và sector đã lưu có trạng thái đóng (status === 0) thì cập nhật lại
                  if (item.status === 1 && existing.status === 0) {
                    sectorMap.set(item.sectorId, {
                      sectorId: item.sectorId,
                      sectorName: item.sectorName,
                      status: item.status
                    });
                  }
                  // Trường hợp khác, giữ nguyên sector đã lưu
                }
              }
            });
          }
          // Chuyển Map thành mảng, chỉ giữ lại sectorId và sectorName
          this.listSector = Array.from(sectorMap.values()).map(
            ({ sectorId, sectorName }) => ({ sectorId, sectorName })
          );
          this.listProcedure = lstprocedure;
        },
        (err) => {
          console.log(err);
        }
      );
  }

  getListHoSo() {
    let toDate = new Date(this.endDate);
    let toDateLK = new Date(this.endDateCumulative);
    let promise = new Promise((resolve, reject) => {
      (this.paramsQuery.fromDate = this.startDate
        ?  this.datePipe.transform(this.startDate, 'yyyy-MM-dd')
        : ''),
        (this.paramsQuery.toDate = this.endDate
          ?  this.datePipe.transform(this.endDate, 'yyyy-MM-dd')
          : ''),
        (this.paramsQuery.fromLKDate = this.startDateCumulative
          ?  this.datePipe.transform(this.startDateCumulative, 'yyyy-MM-dd')
          : ''),
        (this.paramsQuery.toLKDate = this.endDateCumulative
          ? this.datePipe.transform(this.endDateCumulative, 'yyyy-MM-dd')
          : ''),
        (this.paramsQuery.agencyId = this.agencyId);
      this.dlkStatisticService
        .getlistDossierCT25SoHuyen(this.paramsQuery)
        .subscribe(
          (res) => {
            this.listHoSo = res;
            this.BuilData();
          },
          (err) => {
            console.log(err);
          }
        );
    });
    return promise;
  }  
  SelectedAgency = '';
  GetDetailProcedure(AgencyId, AgencyName, LevelId) {

    const dialogData = new procedureDetailDialogModel(
      AgencyId,
      AgencyName,
      LevelId,
      1
    );
    const dialogRef = this.dialog.open(DossierDetailComponent, {
      width: '85%',
      height: '90%',
      data: dialogData,
      disableClose: true,
      autoFocus: false,
    });
    dialogRef.afterClosed().subscribe(() => {});
  }
  GetDetailDossier(
    AgencyId,
    AgencyName,
    TrongKy,
    tongSoHoSo,
    soHoSoTonKyTruoc,
    soHoSoTN,
    soHoSoDXL,
    soHoSoDXLTrongHan,
    soHoSoDXLQuaHan,
    soHoSoCXL,
    soHoSoTONCONHAN,
    soHoSoTONQUAHAN,
    total
  ) {
    if (TrongKy == 1) {
      this.paramsDossier.fromDate = this.startDate
      ?  this.datePipe.transform(this.startDate, 'yyyy-MM-dd')
        : '';
      this.paramsDossier.toDate = this.endDate
      ?  this.datePipe.transform(this.endDate, 'yyyy-MM-dd')
      : '';
    } else {
      this.paramsDossier.fromDate = this.startDateCumulative
      ?  this.datePipe.transform(this.startDateCumulative, 'yyyy-MM-dd')
      : '';
      this.paramsDossier.toDate = this.endDateCumulative
      ? this.datePipe.transform(this.endDateCumulative, 'yyyy-MM-dd')
      : '';
    }
    // tslint:disable-next-line:max-line-length
    const procedureIds: string[] = this.listProcedure.map(item => item.procedureId);

    const dialogData = new DossierDetail25DialogModel(
      AgencyId,
      AgencyName,
      TrongKy,
      tongSoHoSo,
      soHoSoTonKyTruoc,
      soHoSoTN,
      soHoSoDXL,
      soHoSoDXLTrongHan,
      soHoSoDXLQuaHan,
      soHoSoCXL,
      soHoSoTONCONHAN,
      soHoSoTONQUAHAN,
      this.paramsDossier.fromDate,
      this.paramsDossier.toDate,
      this.listAgency,
      total,
      3,
      procedureIds,
      1
    );
    const dialogRef = this.dialog.open(DossierDetailComponent, {
      width: '95%',
      height: '90%',
      data: dialogData,
      disableClose: true,
      autoFocus: false,
    });
    dialogRef.afterClosed().subscribe(() => {});
  }

  PROCEDUREDATA: any[] = [];

  listproduretotal = [];

  getListAgencyAccept(prid, keyword, page, size) {
    const searchString =
      '?parent-id=' +
      prid +
      '&keyword=' +
      keyword +
      '&page=' +
      page +
      '&size=' +
      size +
      '&sort=name.name,asc&status=1';
    this.procedureService.getListAgencyWithParent(searchString).subscribe(
      (res) => {
        this.listAgencyAccept = res.content.filter(
          (item) =>
            item.level != null && item.level.id == '5f39f4155224cf235e134c59'
        );
        if (this.listAgencyAccept.length > 0) {
          this.agencyId = this.listAgencyAccept[0].id;
          this.agencyName = this.listAgencyAccept[0].name;
        }
        this.getListProcedureofDossier();
      },
      (err) => {
        console.log(err);
      }
    );
  }
  changeAgency() {
    const agencyCurrent = this.listAgencyAccept.find(item => item.id === this.agencyId)
    this.agencyName = agencyCurrent ? agencyCurrent.name : null;
    this.getListProcedureofDossier();
  }

  removeDuplicates(array) {
    // Sử dụng Set để lưu trữ các giá trị duy nhất từ cặp procedureId và procedureName
    const uniqueSet = new Set();
    return array.filter(item => {
      // Tạo chuỗi duy nhất từ procedureId và procedureName
      const uniqueKey = `${item.procedureId}-${item.procedureName}`;
      
      // Kiểm tra xem uniqueKey đã tồn tại trong Set chưa
      if (!uniqueSet.has(uniqueKey)) {
        uniqueSet.add(uniqueKey);
        return true;
      }
      return false;
    });
  }

  BuilData() {
    this.listHoSoCapSo = [];
    this.ListMain = [];

    this.TongCapSo = {
      TrongKy: 1,
      tongSoHoSo: 0,
      soHoSoTonKyTruoc: 0,
      soHoSoTN: 0,
      soHoSoDXLTrongHan: 0,
      soHoSoDXL: 0,
      soHoSoDXLQuaHan: 0,
      soHoSoTON: 0,
      soHoSoTONCONHAN: 0,
      soHoSoTONQUAHAN: 0,
      vanban_QUAHAN: 0,
      vanban_QUAHAN2: 0,
      vanban_MAT_HS: 0,
      vanban_SAISOT: 0,
      vanban_THIEU_TPHS: 0,
      vanban_MAT_HS2: 0,
      vanban_SAISOT2: 0,
      vanban_THIEU_TPHS2: 0,
      tongSoVBXL: 0,
      soHoSoDXL_LK: 0,
      soHoSoDXLTrongHan_LK: 0,
      soHoSoDXLQuaHan_LK: 0,
      vanban_QUAHAN_LK: 0,
      vanban_QUAHAN2_LK: 0,
      vanban_MAT_HS_LK: 0,
      vanban_SAISOT_LK: 0,
      vanban_THIEU_TPHS_LK: 0,
      vanban_MAT_HS2_LK: 0,
      vanban_SAISOT2_LK: 0,
      vanban_THIEU_TPHS2_LK: 0,
      tongSoVBXL_LK: 0,
    };

    for (let i = 0; i < this.listSector.length; i++) {
      let sectorId = this.listSector[i].sectorId;
      let sectorName = this.listSector[i].sectorName;
      let ListThuTuc = this.listProcedure.filter(
        (f) => f.sectorId !== null && f.sectorId === sectorId
      );

      var main = {
        id: sectorId,
        sector: sectorName,
        show: false,
        tongSoHoSo: 0,
        soHoSoTonKyTruoc: 0,
        soHoSoTN: 0,
        soHoSoDXLTrongHan: 0,
        soHoSoDXL: 0,
        soHoSoDXLQuaHan: 0,
        soHoSoTON: 0,
        soHoSoTONCONHAN: 0,
        soHoSoTONQUAHAN: 0,
        vanban_QUAHAN: 0,
        vanban_QUAHAN2: 0,
        vanban_MAT_HS: 0,
        vanban_SAISOT: 0,
        vanban_THIEU_TPHS: 0,
        vanban_MAT_HS2: 0,
        vanban_SAISOT2: 0,
        vanban_THIEU_TPHS2: 0,
        tongSoVBXL: 0,
        soHoSoDXL_LK: 0,
        soHoSoDXLTrongHan_LK: 0,
        soHoSoDXLQuaHan_LK: 0,
        vanban_QUAHAN_LK: 0,
        vanban_QUAHAN2_LK: 0,
        vanban_MAT_HS_LK: 0,
        vanban_SAISOT_LK: 0,
        vanban_THIEU_TPHS_LK: 0,
        vanban_MAT_HS2_LK: 0,
        vanban_SAISOT2_LK: 0,
        vanban_THIEU_TPHS2_LK: 0,
        tongSoVBXL_LK: 0,
        data: [],
      };
      for (let j = 0; j < ListThuTuc.length; j++) {
        let ListThuTuctotal = this.listHoSo.filter(
          (f) =>
            f.procedureId !== null &&
            f.procedureId === ListThuTuc[j].procedureId
        );
        var arr = {
          sectorId: sectorId,
          sectorName: sectorName,
          procedureId: ListThuTuc[j].procedureId,
          procedureName: ListThuTuc[j].procedureName,
          tongSoHoSo: 0,
          soHoSoTonKyTruoc: 0,
          soHoSoTN: 0,
          soHoSoDXL: 0,
          soHoSoDXLTrongHan: 0,
          soHoSoDXLQuaHan: 0,
          soHoSoTON: 0,
          soHoSoTONCONHAN: 0,
          soHoSoTONQUAHAN: 0,
          tongSoVBXL: 0,
          vanban_QUAHAN: 0,
          vanban_QUAHAN2: 0,
          vanban_MAT_HS: 0,
          vanban_SAISOT: 0,
          vanban_THIEU_TPHS: 0,
          vanban_MAT_HS2: 0,
          vanban_SAISOT2: 0,
          vanban_THIEU_TPHS2: 0,
          soHoSoDXL_LK: 0,
          soHoSoDXLTrongHan_LK: 0,
          soHoSoDXLQuaHan_LK: 0,
          vanban_QUAHAN_LK: 0,
          vanban_QUAHAN2_LK: 0,
          vanban_MAT_HS_LK: 0,
          vanban_SAISOT_LK: 0,
          vanban_THIEU_TPHS_LK: 0,
          vanban_MAT_HS2_LK: 0,
          vanban_SAISOT2_LK: 0,
          vanban_THIEU_TPHS2_LK: 0,
          tongSoVBXL_LK: 0
        };
        if (ListThuTuctotal.length > 0) {
          for (let k = 0; k < ListThuTuctotal.length; k++) {
            arr.tongSoHoSo +=
              ListThuTuctotal[k].soHoSoTonKyTruoc + ListThuTuctotal[k].soHoSoTN;
            arr.soHoSoTonKyTruoc += ListThuTuctotal[k].soHoSoTonKyTruoc;
            arr.soHoSoTN += ListThuTuctotal[k].soHoSoTN;
            arr.soHoSoDXL +=
              ListThuTuctotal[k].soHoSoDXLTrongHan +
              ListThuTuctotal[k].soHoSoDXLQuaHan;
            arr.soHoSoDXLTrongHan += ListThuTuctotal[k].soHoSoDXLTrongHan;
            arr.soHoSoDXLQuaHan += ListThuTuctotal[k].soHoSoDXLQuaHan;
            arr.soHoSoTON +=
              ListThuTuctotal[k].soHoSoTONCONHAN +
              ListThuTuctotal[k].soHoSoTONQUAHAN;
            arr.soHoSoTONCONHAN += ListThuTuctotal[k].soHoSoTONCONHAN;
            arr.soHoSoTONQUAHAN += ListThuTuctotal[k].soHoSoTONQUAHAN;
            arr.tongSoVBXL +=
              ListThuTuctotal[k].vanban_QUAHAN +
              ListThuTuctotal[k].vanban_QUAHAN2 +
              ListThuTuctotal[k].vanban_MAT_HS +
              ListThuTuctotal[k].vanban_MAT_HS2 +
              ListThuTuctotal[k].vanban_SAISOT +
              ListThuTuctotal[k].vanban_SAISOT2 +
              ListThuTuctotal[k].vanban_THIEU_TPHS+
              ListThuTuctotal[k].vanban_THIEU_TPHS2;
            arr.vanban_QUAHAN +=
              ListThuTuctotal[k].vanban_QUAHAN +
              ListThuTuctotal[k].vanban_QUAHAN2;
            arr.vanban_MAT_HS += 
              ListThuTuctotal[k].vanban_MAT_HS +
              ListThuTuctotal[k].vanban_MAT_HS2;
            arr.vanban_SAISOT += 
              ListThuTuctotal[k].vanban_SAISOT +
              ListThuTuctotal[k].vanban_SAISOT2;
            arr.vanban_THIEU_TPHS += 
            ListThuTuctotal[k].vanban_THIEU_TPHS +
            ListThuTuctotal[k].vanban_THIEU_TPHS2;

            arr.tongSoVBXL_LK +=
              ListThuTuctotal[k].vanban_QUAHAN_LK +
              ListThuTuctotal[k].vanban_QUAHAN2_LK +
              ListThuTuctotal[k].vanban_MAT_HS_LK +
              ListThuTuctotal[k].vanban_MAT_HS2_LK +
              ListThuTuctotal[k].vanban_SAISOT_LK +
              ListThuTuctotal[k].vanban_SAISOT2_LK +
              ListThuTuctotal[k].vanban_THIEU_TPHS2_LK + 
              ListThuTuctotal[k].vanban_THIEU_TPHS_LK; 
            arr.soHoSoDXL_LK +=
              ListThuTuctotal[k].soHoSoDXLTrongHan_LK +
              ListThuTuctotal[k].soHoSoDXLQuaHan_LK;
            arr.soHoSoDXLTrongHan_LK += ListThuTuctotal[k].soHoSoDXLTrongHan_LK;
            arr.soHoSoDXLQuaHan_LK += ListThuTuctotal[k].soHoSoDXLQuaHan_LK;
          }
        }
        main.data.push(arr);
      }
      /////////////////////////////////
      
      main.data = this.removeDuplicates(main.data);

      for (let i = 0; i < main.data.length; i++) {
        main.tongSoHoSo += main.data[i].tongSoHoSo;
        main.soHoSoTonKyTruoc += main.data[i].soHoSoTonKyTruoc;
        main.soHoSoTN += main.data[i].soHoSoTN;
        main.soHoSoDXL += main.data[i].soHoSoDXL;
        main.soHoSoDXLTrongHan += main.data[i].soHoSoDXLTrongHan;
        main.soHoSoDXLQuaHan += main.data[i].soHoSoDXLQuaHan;
        main.soHoSoTON += main.data[i].soHoSoTON;
        main.soHoSoTONCONHAN += main.data[i].soHoSoTONCONHAN;
        main.soHoSoTONQUAHAN += main.data[i].soHoSoTONQUAHAN;
        main.tongSoVBXL += main.data[i].tongSoVBXL;
        main.vanban_QUAHAN += main.data[i].vanban_QUAHAN;
        main.vanban_MAT_HS += main.data[i].vanban_MAT_HS;
        main.vanban_SAISOT += main.data[i].vanban_SAISOT;
        main.vanban_THIEU_TPHS += main.data[i].vanban_THIEU_TPHS;
        main.tongSoVBXL_LK += main.data[i].tongSoVBXL_LK;
        main.soHoSoDXL_LK += main.data[i].soHoSoDXL_LK;
        main.soHoSoDXLTrongHan_LK += main.data[i].soHoSoDXLTrongHan_LK;
        main.soHoSoDXLQuaHan_LK += main.data[i].soHoSoDXLQuaHan_LK;
      }      
      this.ListMain.push(main);

    }
    for (let i = 0; i < this.ListMain.length; i++) {
      this.TongCapSo.tongSoHoSo += this.ListMain[i].tongSoHoSo;
      this.TongCapSo.soHoSoTonKyTruoc += this.ListMain[i].soHoSoTonKyTruoc;
      this.TongCapSo.soHoSoTN += this.ListMain[i].soHoSoTN;
      this.TongCapSo.soHoSoDXL += this.ListMain[i].soHoSoDXL;
      this.TongCapSo.soHoSoDXLTrongHan += this.ListMain[i].soHoSoDXLTrongHan;
      this.TongCapSo.soHoSoDXLQuaHan += this.ListMain[i].soHoSoDXLQuaHan;
      this.TongCapSo.soHoSoTON += this.ListMain[i].soHoSoTON;
      this.TongCapSo.soHoSoTONCONHAN += this.ListMain[i].soHoSoTONCONHAN;
      this.TongCapSo.soHoSoTONQUAHAN += this.ListMain[i].soHoSoTONQUAHAN;
      this.TongCapSo.tongSoVBXL += this.ListMain[i].tongSoVBXL;
      this.TongCapSo.vanban_QUAHAN += this.ListMain[i].vanban_QUAHAN;
      this.TongCapSo.vanban_MAT_HS += this.ListMain[i].vanban_MAT_HS;
      this.TongCapSo.vanban_SAISOT += this.ListMain[i].vanban_SAISOT;
      this.TongCapSo.vanban_THIEU_TPHS += this.ListMain[i].vanban_THIEU_TPHS;
      this.TongCapSo.tongSoVBXL_LK += this.ListMain[i].tongSoVBXL_LK;
      this.TongCapSo.soHoSoDXL_LK += this.ListMain[i].soHoSoDXL_LK;
      this.TongCapSo.soHoSoDXLTrongHan_LK += this.ListMain[i].soHoSoDXLTrongHan_LK;
      this.TongCapSo.soHoSoDXLQuaHan_LK += this.ListMain[i].soHoSoDXLQuaHan_LK;
    }
    this.waitingDownloadExcel = false;
  }
  waitingDownloadExcel: boolean = false;
  async exportToExcel() {
    if(this.validateForm() == 1){
      this.waitingDownloadExcel = true;
      this.paramsQuery.page = 0;
      this.page = 1;
      this.getListHoSo();
      const from = this.datePipe.transform(this.startDate, 'dd-MM-yyyy');
      const to = this.datePipe.transform(this.endDate, 'dd-MM-yyyy');
      const toLK = this.endDateCumulative
        ? this.datePipe.transform(this.endDateCumulative, 'dd-MM-yyyy')
        : '';
      const newDateshort = this.datePipe.transform(new Date(), 'dd-MM-yyyy');
      const newDate = this.datePipe.transform(new Date(), 'dd-MM-yyyy HH:ss:mm');
      const excelFileName = `Bao_cao_chi_thi_25__caphuyen_${newDate}`;
      let headerXLS = {
        row1: 'CỘNG HÒA XÃ HỘI CHỦ NGHĨA VIỆT NAM',
        row2: 'Độc lập - Tự do - Hạnh phúc',
        row3: 'Đắk lắk, ' + newDateshort,
        row4: `BÁO CÁO CHỈ THỊ 25 CẤP HUYỆN`,
        row5: `(Từ ${from} đến ngày ${to})`,
      };

      const workbook = new Workbook();
      workbook.creator = 'Snippet Coder';
      workbook.lastModifiedBy = 'SnippetCoder';
      workbook.created = new Date();
      workbook.modified = new Date();
      const worksheet = workbook.addWorksheet('sheet1');

      // Add header row
      worksheet.addRow([]);
      worksheet.mergeCells('A1:X1');
      worksheet.getCell('A1').value = headerXLS.row1;
      worksheet.getCell('A1').alignment = {
        horizontal: 'center',
        vertical: 'middle',
      };
      worksheet.getCell('A1').font = {
        size: 13,
        bold: true,
        name: 'Times New Roman',
      };

      worksheet.mergeCells('A2:X2');
      worksheet.getCell('A2').value = headerXLS.row2;
      worksheet.getCell('A2').alignment = {
        horizontal: 'center',
        vertical: 'middle',
      };
      worksheet.getCell('A2').font = {
        size: 13,
        bold: true,
        name: 'Times New Roman',
      };

      worksheet.mergeCells('A3:X3');
      worksheet.getCell('A3').value = headerXLS.row3;
      worksheet.getCell('A3').alignment = {
        horizontal: 'center',
        vertical: 'middle',
      };
      worksheet.getCell('A3').font = {
        size: 13,
        underline: true,
        name: 'Times New Roman',
      };

      worksheet.mergeCells('A4:X4');
      worksheet.getCell('A4').value = headerXLS.row4;
      worksheet.getCell('A4').alignment = {
        horizontal: 'center',
        vertical: 'middle',
      };
      worksheet.getCell('A4').font = {
        size: 14,
        bold: true,
        name: 'Times New Roman',
      };

      worksheet.mergeCells('A5:X5');
      worksheet.getCell('A5').value = '';
      worksheet.getCell('A5').alignment = {
        horizontal: 'center',
        vertical: 'middle',
      };
      worksheet.getCell('A5').font = {
        size: 11,
        italic: true,
        name: 'Times New Roman',
      };

      worksheet.mergeCells('A6:X6');
      worksheet.getCell('A6').value = headerXLS.row5;
      worksheet.getCell('A6').alignment = {
        horizontal: 'center',
        vertical: 'middle',
      };
      worksheet.getCell('A6').font = {
        size: 11,
        italic: true,
        name: 'Times New Roman',
      };

      worksheet.mergeCells('A7:A10');
      worksheet.getCell('A7').value = 'STT';

      worksheet.mergeCells('B7:B10');
      worksheet.getCell('B7').value = 'Lĩnh vực, thủ tục';

      worksheet.mergeCells('C7:E7');
      worksheet.getCell('C7').value = 'Tổng số hồ sơ đã tiếp nhận trong tháng';
      worksheet.getCell('C7').alignment = {
        horizontal: 'center',
        vertical: 'middle',
        wrapText: true,
      };
      worksheet.getCell('C7').font = {
        size: 11,
        bold: true,
        name: 'Times New Roman',
      };
      worksheet.getCell('C7').border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };

      worksheet.mergeCells('F7:O7');
      worksheet.getCell('F7').value = 'Số hồ sơ đã giải quyết trong tháng';
      worksheet.getCell('F7').alignment = {
        horizontal: 'center',
        vertical: 'middle',
        wrapText: true,
      };
      worksheet.getCell('F7').font = {
        size: 11,
        bold: true,
        name: 'Times New Roman',
      };
      worksheet.getCell('F7').border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };

      worksheet.mergeCells('P7:R7');
      worksheet.getCell('P7').value = 'Số hồ sơ còn tồn chưa giải quyết';
      worksheet.getCell('P7').alignment = {
        horizontal: 'center',
        vertical: 'middle',
        wrapText: true,
      };
      worksheet.getCell('P7').font = {
        size: 11,
        bold: true,
        name: 'Times New Roman',
      };
      worksheet.getCell('P7').border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };

      worksheet.mergeCells('S7:X7');
      worksheet.getCell('S7').value = 'Lũy kế hồ sơ đã giải quyết từ đầu năm';
      worksheet.getCell('S7').alignment = {
        horizontal: 'center',
        vertical: 'middle',
        wrapText: true,
      };
      worksheet.getCell('S7').font = {
        size: 11,
        bold: true,
        name: 'Times New Roman',
      };
      worksheet.getCell('S7').border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };

      worksheet.mergeCells('C8:C10');
      worksheet.getCell('C8').value = 'Tổng số';

      worksheet.mergeCells('D8:E8');
      worksheet.getCell('D8').value = 'Trong đó';
      worksheet.getCell('D8').alignment = {
        horizontal: 'center',
        vertical: 'middle',
        wrapText: true,
      };
      worksheet.getCell('D8').font = {
        size: 11,
        bold: true,
        name: 'Times New Roman',
      };

      worksheet.mergeCells('F8:F10');
      worksheet.getCell('F8').value = 'Tổng số';

      worksheet.mergeCells('G8:O8');
      worksheet.getCell('G8').value = 'Trong đó';
      worksheet.getCell('G8').alignment = {
        horizontal: 'center',
        vertical: 'middle',
        wrapText: true,
      };
      worksheet.getCell('G8').font = {
        size: 11,
        bold: true,
        name: 'Times New Roman',
      };
      worksheet.mergeCells('P8:P10');
      worksheet.getCell('P8').value = 'Tổng số';

      worksheet.mergeCells('Q8:R8');
      worksheet.getCell('Q8').value = 'Trong đó';
      worksheet.getCell('Q8').alignment = {
        horizontal: 'center',
        vertical: 'middle',
        wrapText: true,
      };
      worksheet.getCell('Q8').font = {
        size: 11,
        bold: true,
        name: 'Times New Roman',
      };

      worksheet.mergeCells('S8:S10');
      worksheet.getCell('S8').value = 'Tổng số';

      worksheet.mergeCells('T8:X8');
      worksheet.getCell('T8').value = 'Trong đó';
      worksheet.getCell('T8').alignment = {
        horizontal: 'center',
        vertical: 'middle',
        wrapText: true,
      };
      worksheet.getCell('T8').font = {
        size: 11,
        bold: true,
        name: 'Times New Roman',
      };

      worksheet.mergeCells('D9:D10');
      worksheet.getCell('D9').value =
        'Số hồ sơ chưa giải quyết của tháng trước chuyển qua';

      worksheet.mergeCells('E9:E10');
      worksheet.getCell('E9').value = 'Tổng số hồ sơ tiếp nhận mới trong tháng';

      worksheet.mergeCells('G9:G10');
      worksheet.getCell('G9').value = 'Giải quyết trước, đúng hạn';

      worksheet.mergeCells('H9:H10');
      worksheet.getCell('H9').value = 'Giải quyết quá hạn';

      worksheet.mergeCells('I9:M9');
      worksheet.getCell('I9').value = 'Số văn bản xin lỗi';

      worksheet.mergeCells('N9:N10');
      worksheet.getCell('N9').value =
        'Công khai số văn bản xin lỗi lên hệ thông thông tin giải quyết TTHC (iGate)';

      worksheet.mergeCells('O9:O10');
      worksheet.getCell('O9').value =
        'Số văn bản xin lỗi đã niêm yết tại bộ phận 1 cửa';

      worksheet.mergeCells('Q9:Q10');
      worksheet.getCell('Q9').value = 'Số hồ sơ trước, đúng thời hạn';

      worksheet.mergeCells('R9:R10');
      worksheet.getCell('R9').value = 'Số hồ sơ quá thời hạn';

      worksheet.mergeCells('T9:T10');
      worksheet.getCell('T9').value = 'Giải quyết trước, đúng hạn';

      worksheet.mergeCells('U9:U10');
      worksheet.getCell('U9').value = 'Giải quyết quá hạn';

      worksheet.mergeCells('V9:V10');
      worksheet.getCell('V9').value = 'Số văn bản xin lỗi';

      worksheet.mergeCells('W9:W10');
      worksheet.getCell('W9').value =
        'Công khai Số văn bản xin lỗi lên hệ thông thông tin giải quyết TTHC';

      worksheet.mergeCells('X9:X10');
      worksheet.getCell('X9').value =
        'Số văn bản xin lỗi đã niêm yết tại bộ phận 1 cửa';

      worksheet.getCell('I10').value = 'Tổng số';

      worksheet.getCell('J10').value = 'Do giải quyết quá hạn';

      worksheet.getCell('K10').value = 'Do tiếp nhận thành phần hồ sơ không đủ';

      worksheet.getCell('L10').value = 'Do hồ sơ bị mất, thất lạc hoặc hư hỏng';

      worksheet.getCell('M10').value = 'Do sai sót trong kết quả giải quyết';

      const rowStartHeaderContent = 11;
      const NumberCol = 24;
      for (let index = 0; index < NumberCol; index++) {
        worksheet.getCell(10, index + 1).alignment = {
          horizontal: 'center',
          vertical: 'middle',
          wrapText: true,
        };
        worksheet.getCell(10, index + 1).font = {
          size: 11,
          bold: true,
          name: 'Times New Roman',
        };
        worksheet.getCell(10, index + 1).border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' },
        };

        worksheet.getCell(9, index + 1).alignment = {
          horizontal: 'center',
          vertical: 'middle',
          wrapText: true,
        };
        worksheet.getCell(9, index + 1).font = {
          size: 11,
          bold: true,
          name: 'Times New Roman',
        };
        worksheet.getCell(9, index + 1).border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' },
        };

        worksheet.getCell('T8').border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' },
        };

        worksheet.getCell(rowStartHeaderContent, index + 1).value =
          '(' + (index + 1).toString() + ')';
        worksheet.getCell(rowStartHeaderContent, index + 1).alignment = {
          horizontal: 'center',
          vertical: 'middle',
          wrapText: true,
        };
        worksheet.getCell(rowStartHeaderContent, index + 1).font = {
          size: 11,
          bold: true,
          name: 'Times New Roman',
        };
        worksheet.getCell(rowStartHeaderContent, index + 1).border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' },
        };
      }

      let rowStartContent = rowStartHeaderContent;

      rowStartContent = rowStartContent + 1;

      // cấp sở ban ngành
      const data = this.ListMain;
      for (let i = 0; i < data.length; i++) {
        var item = data[i];
        var r = 0;

        worksheet.getCell(rowStartContent, 1).value = i + 1;

        worksheet.getCell(rowStartContent, 2).value = item.sector;
        worksheet.getCell(rowStartContent, 1).font = {
          bold: true,
        };
        worksheet.getCell(rowStartContent, 2).font = {
          bold: true,
        };
        //trong kỳ
        worksheet.getCell(rowStartContent + r, 3).value = item.tongSoHoSo;
        worksheet.getCell(rowStartContent + r, 4).value =
          item.soHoSoTonKyTruoc;
        worksheet.getCell(rowStartContent + r, 5).value = item.soHoSoTN;
        worksheet.getCell(rowStartContent + r, 6).value = item.soHoSoDXL;
        worksheet.getCell(rowStartContent + r, 7).value =
          item.soHoSoDXLTrongHan;
        worksheet.getCell(rowStartContent + r, 8).value =
          item.soHoSoDXLQuaHan;
        worksheet.getCell(rowStartContent + r, 9).value = item.tongSoVBXL;
        worksheet.getCell(rowStartContent + r, 10).value =
          item.vanban_QUAHAN;
        worksheet.getCell(rowStartContent + r, 11).value =
          item.vanban_THIEU_TPHS;
        worksheet.getCell(rowStartContent + r, 12).value =
          item.vanban_MAT_HS;
        worksheet.getCell(rowStartContent + r, 13).value =
          item.vanban_SAISOT;
        worksheet.getCell(rowStartContent + r, 14).value =
          item.tongSoVBXL;
        worksheet.getCell(rowStartContent + r, 15).value =
          item.tongSoVBXL;
        worksheet.getCell(rowStartContent + r, 16).value = item.soHoSoTON;
        worksheet.getCell(rowStartContent + r, 17).value =
          item.soHoSoTONCONHAN;
        worksheet.getCell(rowStartContent + r, 18).value =
          item.soHoSoTONQUAHAN;
        worksheet.getCell(rowStartContent + r, 19).value =
          item.soHoSoDXL_LK;
        worksheet.getCell(rowStartContent + r, 20).value =
          item.soHoSoDXLTrongHan_LK;
        worksheet.getCell(rowStartContent + r, 21).value =
          item.soHoSoDXLQuaHan_LK;
        worksheet.getCell(rowStartContent + r, 22).value =
          item.tongSoVBXL_LK;
        worksheet.getCell(rowStartContent + r, 23).value =
          item.tongSoVBXL_LK;
        worksheet.getCell(rowStartContent + r, 24).value =
          item.tongSoVBXL_LK;

        for (let c = 0; c < NumberCol; c++) {
          worksheet.getCell(rowStartContent + r, c + 1).alignment = {
            horizontal: 'center',
            vertical: 'middle',
            wrapText: true,
          };
          worksheet.getCell(rowStartContent + r, c + 1).font = {
            size: 11,
            name: 'Times New Roman',
          };
          worksheet.getCell(rowStartContent + r, c + 1).border = {
            top: { style: 'thin' },
            left: { style: 'thin' },
            bottom: { style: 'thin' },
            right: { style: 'thin' },
          };
        }

        for (let c = 0; c < NumberCol; c++) {
          worksheet.getCell(rowStartContent + r, c + 1).alignment = {
            horizontal: 'center',
            vertical: 'middle',
            wrapText: true,
          };
          worksheet.getCell(rowStartContent + r, c + 1).font = {
            size: 11,
            name: 'Times New Roman',
          };
          worksheet.getCell(rowStartContent + r, c + 1).border = {
            top: { style: 'thin' },
            left: { style: 'thin' },
            bottom: { style: 'thin' },
            right: { style: 'thin' },
          };
        }
        rowStartContent = rowStartContent + 1;
        const data2 = data[i].data;
        for (let j = 0; j < data2.length; j++) {
          var it = data2[j];
          var r = 0;
          worksheet.getCell(rowStartContent, 1).value = this.colToLetter(j);
          worksheet.getCell(rowStartContent, 2).value = it.procedureName;
          //trong kỳ
          worksheet.getCell(rowStartContent + r, 3).value = it.tongSoHoSo;
          worksheet.getCell(rowStartContent + r, 4).value =
            it.soHoSoTonKyTruoc;
          worksheet.getCell(rowStartContent + r, 5).value = it.soHoSoTN;
          worksheet.getCell(rowStartContent + r, 6).value = it.soHoSoDXL;
          worksheet.getCell(rowStartContent + r, 7).value =
            it.soHoSoDXLTrongHan;
          worksheet.getCell(rowStartContent + r, 8).value =
            it.soHoSoDXLQuaHan;
          worksheet.getCell(rowStartContent + r, 9).value = it.tongSoVBXL;
          worksheet.getCell(rowStartContent + r, 10).value =
            it.vanban_QUAHAN;
          worksheet.getCell(rowStartContent + r, 11).value =
            it.vanban_THIEU_TPHS;
          worksheet.getCell(rowStartContent + r, 12).value =
            it.vanban_MAT_HS;
          worksheet.getCell(rowStartContent + r, 13).value =
            it.vanban_SAISOT;
          worksheet.getCell(rowStartContent + r, 14).value =
            it.tongSoVBXL;
          worksheet.getCell(rowStartContent + r, 15).value =
            it.tongSoVBXL;
          worksheet.getCell(rowStartContent + r, 16).value = it.soHoSoTON;
          worksheet.getCell(rowStartContent + r, 17).value =
            it.soHoSoTONCONHAN;
          worksheet.getCell(rowStartContent + r, 18).value =
            it.soHoSoTONQUAHAN;
          worksheet.getCell(rowStartContent + r, 19).value =
            it.soHoSoDXL_LK;
          worksheet.getCell(rowStartContent + r, 20).value =
            it.soHoSoDXLTrongHan_LK;
          worksheet.getCell(rowStartContent + r, 21).value =
            it.soHoSoDXLQuaHan_LK;
          worksheet.getCell(rowStartContent + r, 22).value =
            it.tongSoVBXL_LK;
          worksheet.getCell(rowStartContent + r, 23).value =
            it.tongSoVBXL_LK;
          worksheet.getCell(rowStartContent + r, 24).value =
            it.tongSoVBXL_LK;

          for (let c = 0; c < NumberCol; c++) {
            worksheet.getCell(rowStartContent + r, c + 1).alignment = {
              horizontal: 'center',
              vertical: 'middle',
              wrapText: true,
            };
            worksheet.getCell(rowStartContent + r, c + 1).font = {
              size: 11,
              name: 'Times New Roman',
            };
            worksheet.getCell(rowStartContent + r, c + 1).border = {
              top: { style: 'thin' },
              left: { style: 'thin' },
              bottom: { style: 'thin' },
              right: { style: 'thin' },
            };
          }

          for (let c = 0; c < NumberCol; c++) {
            worksheet.getCell(rowStartContent + r, c + 1).alignment = {
              horizontal: 'center',
              vertical: 'middle',
              wrapText: true,
            };
            worksheet.getCell(rowStartContent + r, c + 1).font = {
              size: 11,
              name: 'Times New Roman',
            };
            worksheet.getCell(rowStartContent + r, c + 1).border = {
              top: { style: 'thin' },
              left: { style: 'thin' },
              bottom: { style: 'thin' },
              right: { style: 'thin' },
            };
          }
          rowStartContent = rowStartContent + 1;
          
        }
      }

      //Tổng cấp tỉnh
      worksheet.mergeCells(rowStartContent, 1, rowStartContent, 2);
      worksheet.getCell(rowStartContent, 1).value = 'TỔNG';
      worksheet.getCell(rowStartContent, 1).alignment = {
        horizontal: 'center',
        vertical: 'middle',
      };
      worksheet.getCell(rowStartContent, 1).font = {
        size: 11,
        bold: true,
        name: 'Times New Roman',
      };
      worksheet.getCell(rowStartContent, 1).border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
      worksheet.getCell(rowStartContent, 3).value = this.TongCapSo.tongSoHoSo;
      worksheet.getCell(rowStartContent, 4).value =
        this.TongCapSo.soHoSoTonKyTruoc;
      worksheet.getCell(rowStartContent, 5).value = this.TongCapSo.soHoSoTN;
      worksheet.getCell(rowStartContent, 6).value = this.TongCapSo.soHoSoDXL;
      worksheet.getCell(rowStartContent, 7).value =
        this.TongCapSo.soHoSoDXLTrongHan;
      worksheet.getCell(rowStartContent, 8).value =
        this.TongCapSo.soHoSoDXLQuaHan;
      worksheet.getCell(rowStartContent, 9).value = this.TongCapSo.tongSoVBXL;
      worksheet.getCell(rowStartContent, 10).value =
        this.TongCapSo.vanban_QUAHAN;
      worksheet.getCell(rowStartContent, 11).value =
        this.TongCapSo.vanban_THIEU_TPHS;
      worksheet.getCell(rowStartContent, 12).value =
        this.TongCapSo.vanban_MAT_HS;
      worksheet.getCell(rowStartContent, 13).value =
        this.TongCapSo.vanban_SAISOT;
      worksheet.getCell(rowStartContent, 14).value =
        this.TongCapSo.tongSoVBXL;
      worksheet.getCell(rowStartContent, 15).value =
        this.TongCapSo.tongSoVBXL;
      worksheet.getCell(rowStartContent, 16).value = this.TongCapSo.soHoSoTON;
      worksheet.getCell(rowStartContent, 17).value =
        this.TongCapSo.soHoSoTONCONHAN;
      worksheet.getCell(rowStartContent, 18).value =
        this.TongCapSo.soHoSoTONQUAHAN;
      worksheet.getCell(rowStartContent, 19).value =
        this.TongCapSo.soHoSoDXL_LK;
      worksheet.getCell(rowStartContent, 20).value =
        this.TongCapSo.soHoSoDXLTrongHan_LK;
      worksheet.getCell(rowStartContent, 21).value =
        this.TongCapSo.soHoSoDXLQuaHan_LK;
      worksheet.getCell(rowStartContent, 22).value =
        this.TongCapSo.tongSoVBXL_LK;
      worksheet.getCell(rowStartContent, 23).value =
        this.TongCapSo.tongSoVBXL_LK;
      worksheet.getCell(rowStartContent, 24).value =
        this.TongCapSo.tongSoVBXL_LK;

      for (let index = 2; index < NumberCol; index++) {
        worksheet.getCell(rowStartContent, index + 1).alignment = {
          horizontal: 'center',
          vertical: 'middle',
        };
        worksheet.getCell(rowStartContent, index + 1).font = {
          size: 11,
          bold: true,
          name: 'Times New Roman',
        };
        worksheet.getCell(rowStartContent, index + 1).border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' },
        };

        worksheet.getCell(rowStartContent - 1, index + 1).alignment = {
          horizontal: 'center',
          vertical: 'middle',
        };
        worksheet.getCell(rowStartContent - 1, index + 1).font = {
          size: 11,
          bold: true,
          name: 'Times New Roman',
        };
        worksheet.getCell(rowStartContent - 1, index + 1).border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' },
        };
      }
      worksheet.getColumn(2).width = 100;
      worksheet.getColumn(2).alignment = {horizontal: 'left', vertical: 'middle', wrapText: true};
      worksheet.getCell(7,2).alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
      worksheet.getRow(1).alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
      worksheet.getRow(2).alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
      worksheet.getRow(3).alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
      worksheet.getRow(4).alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
      worksheet.getRow(5).alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
      worksheet.getRow(6).alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
      // Save Excel File
      workbook.xlsx.writeBuffer().then((dataBuffer: ArrayBuffer) => {
        const blob = new Blob([dataBuffer], { type: this.EXCEL_TYPE });
        fs.saveAs(blob, excelFileName + this.EXCEL_EXTENSION);
      });
    }
  }
}
