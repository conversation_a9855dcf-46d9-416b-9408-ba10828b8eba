.search-nested {
    width: 80% !important;
    height: 50px !important;
    margin-left: 15px !important;
}

.clear-search-nested {
    right: 0 !important;
    position: absolute !important;
    top: 0 !important;
}

.data-label {
    word-wrap: break-word;
}


.mat-tooltip {
    font-size: 13px;
}

.frm_main {
    border-radius: 4px;
    padding: 1em;
    box-shadow: 0px 1px 15px rgba(69, 65, 78, 0.19);

    .ctrl {
        .btnCtrl {
            background-color: #e8e8e8;
            color: #666;
            float: right;
            margin-left: 1em;

            .mat-icon {
                color: #ce7a58;
                margin-right: .2em;
            }
        }
    }

    .logbookTbl {

        //margin-top: 1.5em;

        .logbookOnlyTbl {
            overflow-y: scroll;
        }

        table {
            border-collapse: collapse;
            width: 100%;
        }

        table tr th {
            border: 1px solid;
            text-align: center;
            font-weight: 500;
            background: #ce7a58;
            color: white;
            //color: #fff;
            border: 1px solid rgb(182, 182, 182);
            padding: 5px;
            font-size: 14px;
        }

        table tr td {
            border: 1px solid rgb(182, 182, 182);
            text-align: center;
            padding: 5px;
            font-size: 14px;
        }

        table tr td a {
            cursor: pointer;
            color: #3c8dbc;
        }

        .sum {
            font-weight: 500;
            background-color: #dff0d8;
        }

        ;

        // table {
        //     width: 100%;
        //     border-radius: 4px;
        //     border: 1px solid #cccccc50;
        //     width: 100%;
        // }

        // th {
        //     //padding: .5em;
        //     border: 1px solid #cccccc50 !important;
        // }

        // .cell_code {
        //     color: #ce7a58;
        //     font-weight: 500;
        //     text-decoration: none;

        //     a {
        //         cursor: pointer;
        //     }
        // }

        // table {
        //     width: 100%;
        //     border-radius: 4px;
        //     border: 1px solid #ececec;
        //     width: 100%;
        // }

        // .th {
        //     padding: .5em;
        //     border: .2px solid #cccccc50 !important;
        // }

        // .tr {
        //     background-color: #e8e8e8;
        //     min-height: 3.5em !important;

        //     .th {
        //         color: #495057;
        //         font-size: 14px;
        //         padding: 0 .5em;
        //         border: .2px solid #cccccc50 !important;

        //         p {
        //             margin-bottom: 0;
        //             font-weight: 400;
        //             font-style: italic;
        //         }
        //     }
        // }

        // .mat-column-stt {
        //     padding-right: 0.5em;
        //     padding-left: 1em;
        //     flex: 0 0 5%;
        // }

        // .mat-column-code a {
        //     text-decoration: none;
        //     font-weight: 500;
        //     color: #ce7a58;
        // }

        // .mat-column-procedure {
        //     flex: 1 0 5%;
        //     padding: 0 0.5em;

        //     .procedureName {
        //         display: -webkit-box;
        //         -webkit-line-clamp: 4;
        //         -webkit-box-orient: vertical;
        //         width: 100%;
        //         overflow: hidden;
        //         text-overflow: ellipsis;
        //     }
        // }
    }
}

// ================================= searchForm
::ng-deep .prc_searchbar .searchForm .mat-form-field-appearance-outline .mat-form-field-outline {
    color: transparent;
}

::ng-deep .prc_searchbar .searchForm .mat-form-field-appearance-outline .mat-form-field-outline-thick {
    color: #dddddd;
}

::ng-deep .prc_searchbar .searchForm .mat-form-field-appearance-outline .mat-form-field-outline {
    background-color: #eaebeb;
    border-radius: 5px;
}

::ng-deep .prc_searchbar .searchForm .formFieldItems {
    flex-wrap: wrap;
}

::ng-deep .prc_searchbar .mat-form-field-appearance-outline .mat-form-field-infix {
    padding: 0.8em 0;
}

::ng-deep .prc_searchbar .mat-form-field-label-wrapper {
    top: -1em;
}

::ng-deep .prc_searchbar .mat-form-field.mat-focused .mat-form-field-label {
    color: #ce7a58;
    font-size: 14px;
}

::ng-deep .prc_searchbar .mat-form-field-appearance-outline.mat-form-field-can-float.mat-form-field-should-float .mat-form-field-label {
    color: #ce7a58;
    transform: translateY(-1.55em) scale(1);
    margin-bottom: 1em;
}

::ng-deep .prc_AgencyAutocomplete .mat-option-text {
    font-size: 14px !important;
}

// ================================= prc_main
div.prc_main {
    overflow-x: scroll !important;
}

.prc_main {
    border-radius: 4px;
    padding: 1em;
    box-shadow: 0px 1px 15px rgba(69, 65, 78, 0.19);
    margin-top: 15px;
    width: 100%;
}

.radio-margin {
    margin-right: 15px;
}

.aligncenter {
    display: flex;
    align-items: center;
    padding-left: 5px;
}

.prc_main .btn_add {
    background-color: #e8e8e8;
    color: #666;
    float: right;
}

.prc_main .btn_add .mat-icon {
    color: #ce7a58;
}

// ================================= prc_tbl
.prc_tbl {
    margin-top: 0em;
}

.prc_tbl table {
    border-radius: 4px;
    border: 1px solid #ececec;
    width: 100%;
}

::ng-deep .prc_tbl .mat-header-row {
    background-color: #e8e8e8;
    min-height: 3.5em !important;
}

::ng-deep .prc_tbl .mat-header-row .mat-header-cell {
    color: #495057;
    font-size: 14px;
    // display: grid;
}

::ng-deep .prc_tbl .mat-header-row .mat-header-cell p {
    margin-bottom: 0;
    font-weight: 400;
    font-style: italic;
}



::ng-deep .prc_tbl .mat-column-code {

    min-width: 100px;
}

::ng-deep .prc_tbl .mat-column-procedure {

    min-width: 60px;
}

::ng-deep .prc_tbl .mat-column-sector {

    min-width: 60px;
}

::ng-deep .prc_tbl .mat-column-acceptedDate {

    min-width: 80px;
}


::ng-deep .prc_tbl .mat-column-appointmentDate {

    min-width: 80px;
}

::ng-deep .prc_tbl .mat-column-completedDate {

    min-width: 80px;
}

::ng-deep .prc_tbl .mat-column-applicant {

    min-width: 60px;
}

::ng-deep .prc_tbl .mat-column-phoneNumber {

    min-width: 105px;
}




::ng-deep .prc_tbl .mat-column-dossierStatus {

    min-width: 50px;
}

::ng-deep .prc_tbl .mat-column-isDossierAttachedFileTag {

    min-width: 50px;
}

::ng-deep .prc_tbl .mat-column-dossierReceiving {

    min-width: 75px;
}

::ng-deep .prc_tbl .mat-column-isDossierResultFileTag {

    min-width: 50px;
}

::ng-deep .prc_tbl.dossier_late .mat-row {
    border: block;
}

::ng-deep .prc_tbl .mat-row:nth-child(even) {
    background-color: #FAFAFA;
}

::ng-deep .prc_tbl .mat-row:nth-child(odd) {
    background-color: #fff;
}

::ng-deep .menuAction {
    font-weight: 500;
}

::ng-deep .menuAction .mat-icon {
    color: #ce7a58;
}

.dossier_late .mat-cell {
    margin: 6px;
}

.dossier_late .mat-header-cell {
    margin: 6px;
}

@media screen and (max-width: 600px) {
    .prc_tbl .mat-header-row {
        display: none;
    }

    .prc_tbl .mat-table {
        border: 0;
        vertical-align: middle;
    }

    .prc_tbl .mat-table caption {
        font-size: 1em;
    }

    .prc_tbl .mat-table .mat-row {
        border-bottom: 5px solid #ddd;
        display: block;
        min-height: unset;
    }

    .prc_tbl .mat-table .mat-cell {
        border-bottom: 1px solid #ddd;
        display: block;
        font-size: 14px;
        text-align: right;
        margin-bottom: 4%;
        padding: 0 .5em;
    }

    .prc_tbl .mat-table .mat-cell:before {
        content: attr(data-label);
        float: left;
        font-weight: 500;
        font-size: 14px;
    }

    .prc_tbl .mat-table .mat-cell:last-child {
        border-bottom: 0;
    }

    .prc_tbl .mat-table .mat-cell:first-child {
        margin-top: 4%;
    }

    ::ng-deep .prc_tbl .mat-column-status {
        float: unset;
    }

    ::ng-deep .prc_tbl .mat-column-action {
        float: unset;
    }

    ::ng-deep .prc_tbl .mat-row:nth-child(even) {
        background-color: unset;
    }

    ::ng-deep .prc_tbl .mat-row:nth-child(odd) {
        background-color: unset;
    }
}

::ng-deep .prc_searchbar .searchForm {
    @import "~src/styles/buttons.scss";

    .btn-search {
        @extend .t-btn-search;
    }

    .btn-download-excel {
        @extend .t-btn-download-excel;
    }

    .iconStatistical {
        padding-right: 3px;
    }
}

.cell_code {
    color: #ce7a58;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    max-width: 100%;
    word-break: break-all;
}

.fileTag {
    text-align: center;
}

.titleText {
    text-align: center;
    font-weight: 900;
}

.sumToanTinhText {
    text-align: center;
    font-weight: 500;
    color: white;
    background-color: #ce7a58;
}

.sumCacCapText {
    text-align: start;
    color: white;
    background-color: #ce7a58;
    font-weight: 400;
}

.titleSum{
    text-align: center;
    font-weight: 500;
}
