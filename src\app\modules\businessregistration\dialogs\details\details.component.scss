::ng-deep .file_icon.mat-list-icon {
    color: rgba(0, 0, 0, 0.54);
    border-radius: unset !important;
}

.dialog_title {
    font-style: normal;
    font-weight: 500;
    font-size: 20px;
    color: #CE7A58;
}

.file_size {
    color: #666666;
}

.fill_space {
    flex: 1 1 auto;
}

.file_icon.mat-icon {
    background-color: #f2a6343d;
    color: #CE7A58;
    border-radius: 50% !important;
    padding: .2em !important;
    transform: scale(1.5);
}

.filename {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 80%;
}

.close-button {
    float: right;
    top: -24px;
    right: -24px;
}

.detail-title {
    font-weight: bold;
}