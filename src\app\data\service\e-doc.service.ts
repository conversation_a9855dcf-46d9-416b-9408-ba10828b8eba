import {HttpBackend, HttpClient, HttpHeaders, HttpParams} from '@angular/common/http';
import {Injectable} from '@angular/core';
import {Observable} from 'rxjs';
import {ApiProviderService} from 'core/service/api-provider.service';
import {EnvService} from 'core/service/env.service';
import {DeploymentService} from "data/service/deployment.service";

@Injectable({
  providedIn: 'root'
})
export class EDocService  {

  config = this.envService.getConfig();
  constructor(
    private envService: EnvService,
    private http: HttpClient,
    private apiProviderService: ApiProviderService,
    private deploymentService: DeploymentService,
    private handler: HttpBackend,
    private httpQLVB: HttpClient
  ) {
    this.httpQLVB = new HttpClient(this.handler);
   }

  private padman = this.apiProviderService.getUrl('digo', 'padman');
  env: any = this.deploymentService.getAppDeployment()?.env;
  private rootUrlBaseDoc = this.env?.OS_TGG?.ioffice?.root_url_basedoc !== undefined ? this.env?.OS_TGG?.ioffice?.root_url_basedoc : '';
  private qLVBPath = this.env?.OS_BDG?.QLVBMinhTue?.gateway;

  // Gửi gói tin theo các nghiệp vụ liên thông
  sendDocumentPacket(requestBody: any): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.put<any>(this.padman + '/e-doc/--send-document-packet', requestBody, { headers });
  }

  getDocumentPacket(): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.padman + '/e-doc/--receive-document-packets?rootUrlBaseDoc=' + encodeURIComponent(this.rootUrlBaseDoc), { headers });
  }

  getTokenQLVB(): Observable<any> {
    let body = new HttpParams().set('grant_type', 'client_credentials');
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.set('Content-Type', 'application/x-www-form-urlencoded');
    headers = headers.append('Authorization', 'Basic ' +  this.env?.OS_BDG?.QLVBMinhTue?.basictoken);
    return this.httpQLVB.post(this.qLVBPath + '/token', body, { headers }).pipe();
  }

  sendDocumentQLVBPacket(accessToken, requestBody: any): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Authorization', 'Bearer ' + accessToken);
    return this.httpQLVB.post(this.qLVBPath + '/ISODVQLVB/1.0/insert-dociso', requestBody, { headers });
  }

  putDossierQLVBId(id, qlvbId): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.put<any>(this.padman + '/dossier/' + id + '/--update-dossier-qlvbid?qlvbid=' + qlvbId, { headers }).pipe();
  }

  putDossierTaskRemind(id, taskRemindId): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.put<any>(this.padman + '/dossier/' + id + '/--update-qlvb-remind?taskRemindId=' + taskRemindId, { headers }).pipe();
  }

}
