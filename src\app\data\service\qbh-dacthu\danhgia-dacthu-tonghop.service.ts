import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable} from '@angular/core';
import { HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { ApiProviderService } from 'src/app/core/service/api-provider.service';

@Injectable({
  providedIn: 'root'
})
export class DanhgiaDacthuTonghopService {

  constructor(
    private http: HttpClient,
    private apiProviderService: ApiProviderService
  ) { }

  // private url = "http://127.0.0.1:8081/danhgia-dacthu-tonghop";
  private url = this.apiProviderService.getUrl('digo', 'padman') + '/danhgia-dacthu-tonghop';
  checkIsAdmin() {
    return localStorage.getItem("checkIsAdmin") ?? false;
  }
  
  list(paramlist) : Observable<any> {
    let params = new HttpParams();
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    Object.keys(paramlist).forEach(
      key => {
        params = params.set(key,paramlist[key]);
      }
    )
    return this.http.get(this.url,{params,headers});
  }

  retreive(id) : Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.url + `/${id}`,{headers});
  }

  create(body): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.set('Content-Type', 'application/json');
    return this.http.post(this.url,body,{headers});
  }

  update(id, body): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.set('Content-Type', 'application/json');
    return this.http.put(this.url + `/${id}`,body,{headers});
  }
  
  delete(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.set('Content-Type', 'application/json');
    return this.http.delete(this.url + `/${id}` ,{headers});
  }

}

interface IAgency {
  id: string ,
  name: string
}
interface IUser {
  id: string,
  fullname: string
}