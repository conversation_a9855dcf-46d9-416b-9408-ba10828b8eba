import { Injectable } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
// tslint:disable-next-line: max-line-length
// import { AdvancedSearchComponent, ConfirmSearchDialogModel } from 'src/app/modules/home/<USER>/advanced-search/advanced-search.component';
import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { ApiProviderService } from 'src/app/core/service/api-provider.service';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class ProcostTypeService {
  constructor(
    private dialog: MatDialog,
    private http: HttpClient,
    private apiProviderService: ApiProviderService
  ) { }

  private getProcedureURL = this.apiProviderService.getUrl('digo', 'basepad');
  private getAgencyURL = this.apiProviderService.getUrl('digo', 'basedata');
  private getSector = this.apiProviderService.getUrl('digo', 'basepad');
  private getProcost = this.apiProviderService.getUrl('digo', 'basepad');
  private adapter = this.apiProviderService.getUrl('digo', 'adapter');
  // advancedSearch() {
  //   const dialogData = new ConfirmSearchDialogModel();
  //   const dialogRef = this.dialog.open(AdvancedSearchComponent, {
  //     width: '600px',
  //     data: dialogData,
  //     disableClose: false,
  //     position: {
  //       top: '10em'
  //     }
  //   });
  //   dialogRef.afterClosed().subscribe(dialogResult => {
  //     // console.log(dialogResult);
  //   });
  // }

  getListProcostType(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.getProcost + '/procost-type/' + searchString, {headers});
  }

  postProcostType(data): Observable<any> {
    return this.http.post<any>(this.getSector + '/procost-type/' , data);
  }

  deleteProcostType(id): Observable<any> {
    return this.http.delete(this.getProcost + '/procost-type/' + id);
  }

  updateProcostType(id, data): Observable<any> {
    return this.http.put<any>(this.getProcost + '/procost-type/' + id, data);
  }

  getDetailProcostType(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.getProcost + '/procost-type/' + id, {headers});
  }

  getListProcedureLevel(): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.getProcedureURL + '/procedure-level', {headers}).pipe();
  }

  getListSector(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.getSector + '/sector/' + searchString, {headers});
  }

  getDetailSector(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.getSector + '/sector/' + id, {headers});
  }

  deleteSector(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.delete(this.getSector + '/sector/' + id, {headers});
  }

  updateSector(id, data): Observable<any> {
    return this.http.put<any>(this.getSector + '/sector/' + id, data);
  }

  postSector(data): Observable<any> {
    return this.http.post<any>(this.getSector + '/sector/' , data);
  }

  getListAgencyLevel(): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.getAgencyURL + '/agency-level', {headers}).pipe();
  }

  getListProcedure(searchString, type, pageSize): Observable<any> {
    switch (type) {
      case 0:
        const token = localStorage.getItem('OAuth2TOKEN');
        let headers = new HttpHeaders();
        headers = headers.append('Authorization', 'Bearer ' + token);
        headers = headers.append('Content-Type', 'application/json');
        headers.append('Access-Control-Allow-Origin', '*');
        return this.http.get(this.getProcedureURL + '/procedure' + searchString + '&page=0', { headers }).pipe();
      case 1:
        return this.http.get(this.getProcedureURL + '/procedure' + searchString + '&page=0').pipe();
    }
  }
  getDetailProcedure(id, type): Observable<any> {
    switch (type) {
      case 0:
        const token = localStorage.getItem('OAuth2TOKEN');
        let headers = new HttpHeaders();
        headers = headers.append('Authorization', 'Bearer ' + token);
        headers = headers.append('Content-Type', 'application/json');
        headers.append('Access-Control-Allow-Origin', '*');
        return this.http.get(this.getProcedureURL + '/procedure/' + id , { headers }).pipe();
      case 1:
        return this.http.get(this.getProcedureURL + '/procedure/' + id ).pipe();
    }
  }

  getListProcost(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.getProcost + '/procost/' + searchString, {headers});
  }

  getListProcostByTypeId(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.getProcost + '/procost/delete-type' + searchString, {headers});
    // return this.http.get('http://localhost:8080/procost/delete-type' + searchString, {headers});
  }
  
  getLgspSync(code, configId, agencyId, subsystemId): Observable<any> {
    let URL = this.adapter + "/lgsp-dongBoDanhMucPhiLePhi/danhMucPhiLePhi";
    URL += `?agency-code=${code}`;
    URL += `&config-id=${configId}`;
    URL += `&agency-id=${agencyId}`;
    URL += subsystemId?`&subsystem-id=${subsystemId}`:``;
    return this.http.get(URL); 
  }

}
