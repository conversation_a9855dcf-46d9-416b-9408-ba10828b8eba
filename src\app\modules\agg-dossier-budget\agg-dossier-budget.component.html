<h2>DANH SÁCH HỒ SƠ CẤP MÃ SỐ CHO ĐƠN VỊ CÓ QUAN HỆ NGÂN SÁCH</h2>
<div class="procedure-list-page">
    <form [formGroup]="searchForm" class="form-search">
        <div fxLayout="row" fxLayout.xs="row" fxLayout.sm="row" class="flex-wrap-wrap">
            <mat-form-field appearance="outline">
                <mat-label>M<PERSON> hồ sơ</mat-label>
                <input matInput formControlName="hsid">
            </mat-form-field>
            <button mat-flat-button fxFlex.gt-sm="10" fxFlex='10' class="btn-search" (click)="onDetailByHsid()">
                <span>Chi tiết</span>
            </button>
        </div>
        <div fxLayout="row" fxLayout.xs="row" fxLayout.sm="row" class="flex-wrap-wrap">
            <mat-form-field appearance="outline" fxFlex.gt-md="24.5" fxFlex.gt-sm="49.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                <mat-label>Từ ngày</mat-label>
                <input matInput [matDatepicker]="pickerFromDate" formControlName="fromdate" maxlength="20" required [max]="date">
                <mat-datepicker-toggle matSuffix [for]="pickerFromDate"></mat-datepicker-toggle>
                <mat-datepicker #pickerFromDate></mat-datepicker>
            </mat-form-field>
            <div fxFlex='1'></div>
            <mat-form-field appearance="outline" fxFlex.gt-md="24.5" fxFlex.gt-sm="49.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                <mat-label>Đến ngày</mat-label>
                <input matInput [matDatepicker]="pickerToDate" formControlName="todate" maxlength="20" required [max]="date">
                <mat-datepicker-toggle matSuffix [for]="pickerToDate"></mat-datepicker-toggle>
                <mat-datepicker #pickerToDate></mat-datepicker>
            </mat-form-field>
            <div fxFlex='1'></div>
            <button mat-flat-button fxFlex.gt-sm="10" fxFlex='10' class="btn-search" (click)="onConfirm()">
                <span>Tra cứu API</span>
            </button>
            <div fxFlex='1'></div>
            <button mat-flat-button fxFlex.gt-sm="10" fxFlex='10' class="btn-search" (click)="onInsert()">
                <span>Lưu vào CSDL</span>
            </button>
            <div fxFlex='1'></div>
            <button mat-flat-button fxFlex.gt-sm="10" fxFlex='10' class="btn-search" (click)="exportToExcelAll()">
                <span>Xuất Excel</span>
            </button>
            <div fxFlex='1'></div>
            <button mat-flat-button fxFlex.gt-sm="10" fxFlex='10' class="btn-search" (click)="onGetCSDL()">
                <span>Tra cứu CSDL</span>
            </button>
        </div>
    </form>

    <div fxLayout="row" fxLayoutAlign="center">
        <div fxFlex="grow">
            <input type="text" style="border-style: groove; height: 25px;background-color: #eaebeb;" placeholder="Tìm kiếm..." [(ngModel)]="searchTerm" (keyup)="applyFilter()">
            <div class="tbl">
                <table mat-table [dataSource]="dataSource">
                    <ng-container matColumnDef="stt">
                        <mat-header-cell *matHeaderCellDef style="font-size: 13px;">STT</mat-header-cell>
                        <mat-cell *matCellDef="let row" data-label="STT" style="font-size: 13px;"> {{row.stt}} </mat-cell>
                    </ng-container>
                    <ng-container matColumnDef="hsid">
                        <mat-header-cell *matHeaderCellDef style="font-size: 13px;padding: 0 0.5em;">Mã hồ sơ</mat-header-cell>
                        <mat-cell *matCellDef="let row" data-label="Mã hồ sơ" (click)="onDetail(row.hsid)" style="font-size: 13px;padding: 0 0.5em;" class="hyperlink">{{row.hsid}}</mat-cell>
                    </ng-container>
    
                    <ng-container matColumnDef="ten_hs">
                        <mat-header-cell *matHeaderCellDef style="font-size: 13px;padding: 0 0.5em;">Tên hồ sơ</mat-header-cell>
                        <mat-cell *matCellDef="let row" data-label="Tên hồ sơ" class="ofw-anywhere" style="font-size: 13px;padding: 0 0.5em;">{{row.ten_hs}}</mat-cell>
                    </ng-container>
    
                    <ng-container matColumnDef="ma">
                        <mat-header-cell *matHeaderCellDef style="font-size: 13px;padding: 0 0.5em;">Mã ĐVQHNS</mat-header-cell>
                        <mat-cell *matCellDef="let row" data-label="Mã ĐVQHNS" class="ofw-anywhere" style="font-size: 13px;padding: 0 0.5em;">{{row.ma}}</mat-cell>
                    </ng-container>
    
                    <ng-container matColumnDef="nguoi_dk">
                        <mat-header-cell *matHeaderCellDef style="font-size: 13px;padding: 0 0.5em;">Người nộp</mat-header-cell>
                        <mat-cell *matCellDef="let row" data-label="Người nộp" class="ofw-anywhere" style="font-size: 13px;padding: 0 0.5em;">{{row.nguoi_dk}}</mat-cell>
                    </ng-container>
    
                    <ng-container matColumnDef="email">
                        <mat-header-cell *matHeaderCellDef style="font-size: 13px;padding: 0 0.5em;">Email</mat-header-cell>
                        <mat-cell *matCellDef="let row" data-label="Email" class="ofw-anywhere" style="font-size: 13px;padding: 0 0.5em;">{{row.email}}</mat-cell>
                    </ng-container>
    
                    <ng-container matColumnDef="sdt_didong">
                        <mat-header-cell *matHeaderCellDef style="font-size: 13px;padding: 0 0.7em;">Số điện thoại</mat-header-cell>
                        <mat-cell *matCellDef="let row" data-label="Số điện thoại" style="font-size: 13px;padding: 0 0.7em;">{{row.sdt_didong}}</mat-cell>
                    </ng-container>
    
                    <ng-container matColumnDef="ngay_dk">
                        <mat-header-cell *matHeaderCellDef style="font-size: 13px;padding: 0 0.5em;">Ngày nộp hồ sơ</mat-header-cell>
                        <mat-cell *matCellDef="let row" data-label="Ngày nộp hồ sơ" style="font-size: 13px;padding: 0 0.5em;">{{row.ngay_dk}}</mat-cell>
                    </ng-container>
    
                    <ng-container matColumnDef="ngay_tao">
                        <mat-header-cell *matHeaderCellDef style="font-size: 13px;padding: 0 0.5em;">Ngày nhập tờ khai</mat-header-cell>
                        <mat-cell *matCellDef="let row" data-label="Ngày nhập tờ khai" style="font-size: 13px;padding: 0 0.5em;">{{row.ngay_tao}}</mat-cell>
                    </ng-container>
    
                    <ng-container matColumnDef="ngay_pd">
                        <mat-header-cell *matHeaderCellDef style="font-size: 13px;padding: 0 0.5em;">Ngày trả kết quả thực tế</mat-header-cell>
                        <mat-cell *matCellDef="let row" data-label="Ngày trả kết quả thực tế" style="font-size: 13px;padding: 0 0.5em;">{{row.ngay_pd}}</mat-cell>
                    </ng-container>
    
                    <ng-container matColumnDef="nguoi_pd">
                        <mat-header-cell *matHeaderCellDef style="font-size: 13px;padding: 0 0.5em;">Người thực hiện cấp mã</mat-header-cell>
                        <mat-cell *matCellDef="let row" data-label="Người thực hiện cấp mã" style="font-size: 13px;padding: 0 0.5em;">{{row.nguoi_pd}}</mat-cell>
                    </ng-container>
    
                    <ng-container matColumnDef="ten_kieu_tiep_nhan">
                        <mat-header-cell *matHeaderCellDef style="font-size: 13px;padding: 0 0.5em;">Kiểu tiếp nhận</mat-header-cell>
                        <mat-cell *matCellDef="let row" data-label="Kiểu tiếp nhận" style="font-size: 13px;padding: 0 0.5em;">
                            {{row.ten_kieu_tiep_nhan}}
                        </mat-cell>
                    </ng-container>
    
                    <ng-container matColumnDef="ten_trang_thai">
                        <mat-header-cell *matHeaderCellDef style="font-size: 13px;padding: 0 0.5em;">Trạng thái</mat-header-cell>
                        <mat-cell *matCellDef="let row" data-label="Trạng thái" style="font-size: 13px;padding: 0 0.5em;">
                            {{row.ten_trang_thai}}
                        </mat-cell>
                    </ng-container>
    
                    <ng-container matColumnDef="ngay_tra">
                        <mat-header-cell *matHeaderCellDef style="font-size: 13px;padding-right: 13px;">Ngày hẹn trả</mat-header-cell>
                        <mat-cell *matCellDef="let row" data-label="Ngày hẹn trả" style="font-size: 13px;padding-right: 13px;">{{row.ngay_tra}}</mat-cell>
                    </ng-container>

                    <ng-container matColumnDef="gcn">
                        <mat-header-cell *matHeaderCellDef style="font-size: 13px;">Giấy chứng nhận</mat-header-cell>
                        <mat-cell *matCellDef="let row" data-label="Giấy chứng nhận" (click)="onCertificate(row.ma)" style="font-size: 13px;" class="hyperlink">Xem</mat-cell>
                    </ng-container>
    
                    <mat-header-row *matHeaderRowDef="displayedColumns"></mat-header-row>
                    <mat-row *matRowDef="let row; columns: displayedColumns;"></mat-row>
                </table>
                <div class="pagination">
                    <ul class="temp-arr">
                        <li *ngFor="let item of ELEMENTDATA  | paginate: {itemsPerPage: size, currentPage: page, totalItems: countResult, id: 'pgnx'}">
                        </li>
                    </ul>
                    <div class="page-size">
                        <span i18n>Hiển thị </span>
                        <mat-form-field appearance="outline">
                            <mat-select [(value)]="size" (valueChange)="paginate(pageIndex, 1)">
                                <mat-option *ngFor='let opt of pgSizeOptions;' [value]="opt">{{opt}}</mat-option>
                            </mat-select>
                        </mat-form-field>
                        <span><span i18n>trên</span> {{countResult}} <span i18n>bản ghi</span></span>
                    </div>
                    <div class="control">
                        <pagination-controls id="pgnx" (pageChange)="page = $event; paginate(page, 0)" responsive="true" previousLabel="" nextLabel="">
                        </pagination-controls>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>