<div class="shared-confirm-dialog">
    <button mat-icon-button class="btn-close" (click)="onDismiss()">
        <mat-icon>close</mat-icon>
    </button>
    <div class="dialog-icon" *ngIf="isWarning"><mat-icon>error_outline</mat-icon></div>
    <h3 class="dialog-title" mat-dialog-title>{{dialogTitle}}</h3>
    <div mat-dialog-content class="dialog-content">
        <span [innerHTML]="dialogContent"></span>
      <div *ngIf="checkbox?.enable">
        <mat-checkbox (change)="checkboxChange($event)" class="chkDelete">{{checkbox?.title}}</mat-checkbox>
      </div>
    </div>
    <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="column" fxLayoutAlign="center" fxLayoutGap="1rem">
        <button mat-flat-button fxFlex='60' class="btn-apply" (click)="onConfirm()" disabled="disableButton">
            <span>Thực hiện yêu c<PERSON>u bổ sung</span>
        </button>
        <button mat-flat-button fxFlex='30' class="btn-cancel" (click)="onReject()" disabled="disableButton">
            <span>Quay lại</span>
        </button>
    </div>
</div>