import { Injectable } from '@angular/core';
import { MatSnackBar, MatSnackBarConfig } from '@angular/material/snack-bar';
import { SnackbarImportComponent } from 'src/app/shared/components/snackbarImport/snackbar.component';

@Injectable({
  providedIn: 'root'
})
export class SnackbarimportService {

  constructor(
    private snackBar: MatSnackBar
  ) { }

  openSnackBar(status: number, message: string, contentSuccess: string, contentErorr: string, contentRow: string,
               panelClass: string, expiredTime: number) {
    this.snackBar.openFromComponent(SnackbarImportComponent, {
      data: {
        message,
        contentSuccess,
        contentErorr,
        contentRow,
        status // 1: success - 0: error
      },
      panelClass,
      duration: expiredTime,
      verticalPosition: 'bottom',
      horizontalPosition: 'center'
    });
  }
}
