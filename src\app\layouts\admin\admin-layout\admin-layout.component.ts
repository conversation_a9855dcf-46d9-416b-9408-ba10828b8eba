import { Component, OnInit } from '@angular/core';
import { DeploymentService } from 'src/app/data/service/deployment.service';
@Component({
  selector: 'app-admin-layout',
  templateUrl: './admin-layout.component.html',
  styleUrls: ['./admin-layout.component.scss']
})
export class AdminLayoutComponent implements OnInit {
  isHomeDNI = this.deploymentService.getAppDeployment()?.isHomeDNI || false;
  constructor(
    private deploymentService: DeploymentService,
  ) { }

  ngOnInit(): void {
    this.isHomeDNI = this.deploymentService.getAppDeployment()?.isHomeDNI || false;
  }

}
