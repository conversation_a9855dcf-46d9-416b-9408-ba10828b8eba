import { Component, OnInit, Inject, ViewChild } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { KeycloakService } from 'keycloak-angular';
import { DossierService } from 'src/app/data/service/dossier/dossier.service';
import { UserService } from 'src/app/data/service/user.service';
import '@ckeditor/ckeditor5-build-classic/build/translations/vi';
import { EnvService } from 'src/app/core/service/env.service';
import { SnackbarService } from 'src/app/data/service/snackbar/snackbar.service';
import { DeploymentService } from 'src/app/data/service/deployment.service';
import { CheckSendNotifyComponent } from 'src/app/shared/components/check-send-notify/check-send-notify.component';
import * as ClassicEditor from '@ckeditor/ckeditor5-build-classic/build/ckeditor';
import { FormControl } from '@angular/forms';

@Component({
  selector: 'return-cancel-processing-dossier',
  templateUrl: './return-cancel-processing-dossier.html',
  styleUrls: ['./return-cancel-processing-dossier.scss'],
})
export class ReturnCancelProcessingDossierComponent implements OnInit {
  @ViewChild(CheckSendNotifyComponent) checkSendNotifyComponent;
  config = this.envService.getConfig();
  env: any = this.deploymentService.getAppDeployment()?.env;
  selectedLang = localStorage.getItem('language') || 'vi';
  selectedLangId = Number(localStorage.getItem('languageId')) || 228;
  dossierId: string;
  fullname: string;
  userName: string;
  userId: string;
  accountId: string;
  dossierCode: string;
  officerNotify: any;
  notifyQNI = this.deploymentService.getAppDeployment()?.notifyqni?.enable;
  reason = new FormControl('');
  isCKMaxlenght = false;
  isDisabled = false;
  ckeditorMaxLength = this.deploymentService.env.OS_HCM.ckeditorMaxLength;
  commentContent = '';
    commentContentPlainText = '';
    ckeditorMaxLengthNotification = '';


  // IGATESUPP-89251: [iGate2.0][QNI] - Luồng xử lý hồ sơ dừng xử lý khi đang yêu cầu bổ sung - 26.06.2024
  enablePauseWhenAdditional = this.deploymentService.getAppDeployment()
    ?.enablePauseWhenAdditional
    ? this.deploymentService.getAppDeployment()?.enablePauseWhenAdditional
    : false;
  returnAdditionalNotifyMessage = this.deploymentService.getAppDeployment()
    ?.returnAdditionalNotifyMessage
    ? this.deploymentService.getAppDeployment()?.returnAdditionalNotifyMessage
    : 'Thông báo trả do quá hạn bổ sung hồ sơ có mã biên nhận: ';
  constructor(
    private userService: UserService,
    private envService: EnvService,
    private keycloakService: KeycloakService,
    public dialogRef: MatDialogRef<ReturnCancelProcessingDossierComponent>,
    @Inject(MAT_DIALOG_DATA)
    public data: ReturnCancelProcessingDossierDialogModel,
    private snackbarService: SnackbarService,
    private deploymentService: DeploymentService,
    private dossierService: DossierService
  ) {
    this.dossierId = data.dossierId;
    this.dossierCode = data.dossierCode;
  }

  async ngOnInit(): Promise<void> {
    this.getUserAccount();
        if (this.selectedLang === 'vi') {
      this.ckeditorMaxLengthNotification = "Nội dung không quá 500 ký tự!";
      if (this.ckeditorMaxLength > 0)
        this.ckeditorMaxLengthNotification = "Nội dung không quá "+ this.ckeditorMaxLength + " ký tự!";
    } else if (this.selectedLang === 'en') {
      this.ckeditorMaxLengthNotification = "Content must not exceed 500 characters!";
      if (this.ckeditorMaxLength > 0)
        this.ckeditorMaxLengthNotification = "Content must not exceed "+ this.ckeditorMaxLength + " characters!";
    }
  }

  public Editor = ClassicEditor;
  commentConfig = {
    placeholder: 'Nhập lý do...',
    language: 'vi',
    removePlugins: ['MediaEmbed', 'ImageUpload'],
    toolbarLocation: 'bottom',
  };

  getUserAccount() {
    this.keycloakService.loadUserProfile().then((user) => {
      // tslint:disable-next-line: no-string-literal
      this.userId = user['attributes'].user_id[0];
      this.accountId = user['attributes'].account_id[0];
      this.userName = user.username;
      this.userService.getUserInfo(this.userId).subscribe((data) => {
        this.fullname = data.fullname;
      });
    });
  }

  async onConfirm() {
    if (this.isCKMaxlenght) {
      let msgObj;
      if (this.ckeditorMaxLength > 0) {
        msgObj = {
          vi: 'Nội dung không quá ' + this.ckeditorMaxLength + ' ký tự!',
          en:
            'Content must not exceed ' +
            this.ckeditorMaxLength +
            ' characters!',
        };
      } else {
        msgObj = {
          vi: 'Nội dung không quá 500 ký tự!',
          en: 'Content must not exceed 500 characters!',
        };
      }
      this.isDisabled = false;
      this.snackbarService.openSnackBar(
        2,
        '',
        msgObj[this.selectedLang],
        'warning_notification',
        this.config.expiredTime
      );
      return;
    }
    if (this.commentContent.trim() === '') {
      this.isDisabled = false;
      const msgObj = {
        vi: 'Vui lòng nhập ý kiến xử lý!',
        en: 'Please enter a handling comments!',
      };
      this.snackbarService.openSnackBar(
        2,
        '',
        msgObj[this.selectedLang],
        'warning_notification',
        this.config.expiredTime
      );
      return;
    }
    this.dossierService.returnCancelProcessingDossier(this.dossierId).subscribe((result) => {
      if (result.affectedRows === 1) {
           this.postComment(this.commentContent);  
          this.dialogRef.close(true);
        } else {
          const msgObj = {
            vi: 'Trả hồ sơ dừng sử lý không thành công!',
            en: 'Returned cancel prossing dossier unsuccessful!',
          };
          this.snackbarService.openSnackBar(
            0,
            '',
            msgObj[this.selectedLang],
            'warning_notification',
            this.config.expiredTime
          );
        }
    })
    
  }

    onCommentEditorChange(event) {
    this.commentContent = event.editor.getData();
    if (this.ckeditorMaxLength > 0) {
      if (event.editor.getData().trim().length > this.ckeditorMaxLength) {
        this.isCKMaxlenght = true;
      } else {
        this.isCKMaxlenght = false;
      }
    } else if (event.editor.getData().trim().length > 500) {
      this.isCKMaxlenght = true;
    } else {
      this.isCKMaxlenght = false;
    }

    this.commentContentPlainText = "";
    this.commentContentPlainText = this.getPlainText(this.commentContent).trim();
  }

    getPlainText( strSrc ) {
    let resultStr = "";

    // Ignore the <p> tag if it is in very start of the text
    if(strSrc.indexOf('<p>') == 0)
        resultStr = strSrc.substring(3);
    else
        resultStr = strSrc;

    // Replace <p> with two newlines
    resultStr = resultStr.replace(/<p>/gi, "\r\n\r\n");
    // Replace <br /> with one newline
    resultStr = resultStr.replace(/<br \/>/gi, "\r\n");
    resultStr = resultStr.replace(/<br>/gi, "\r\n");

    // Replace &nbsp; with space
    resultStr = resultStr.replace(/&nbsp;/gi, " ");
    //-+-+-+-+-+-+-+-+-+-+-+
    // Strip off other HTML tags.
    //-+-+-+-+-+-+-+-+-+-+-+

    return  resultStr.replace( /<[^<|>]+?>/gi,'' );
  }

  postComment(commentContent) {
    const msgObj = {
      vi: 'Trả hồ sơ dừng xử lý: ',
      en: 'Return cancel processing dossier: ',
    };
    const content = {
      groupId: 2,
      itemId: this.dossierId,
      user: {
        id: this.userId,
        fullname: this.fullname,
      },
      content: msgObj[this.selectedLang] + commentContent,
    };
    const requestBody = JSON.stringify(content, null, 2);
    this.dossierService.postComment(requestBody).subscribe((data) => {});
  }

  getUserInfo(): Promise<any> {
    return new Promise<any>((resolve, reject) => {
      this.keycloakService.loadUserProfile().then(
        (user) => {
          resolve(user);
          this.officerNotify = {
            id: user['attributes'].user_id[0],
            fullname: user['attributes'].fullname[0],
            account: {
              id: user['attributes'].account_id[0],
              username: [
                {
                  value: user['username'],
                },
              ],
            },
          };
        },
        (err) => {
          resolve(err);
        }
      );
    });
  }

    onDismiss() {
    this.commentContent = "";
    this.dialogRef.close();
  }
}

export class ReturnCancelProcessingDossierDialogModel {
  constructor(public dossierId: string, public dossierCode: string) {}
}
