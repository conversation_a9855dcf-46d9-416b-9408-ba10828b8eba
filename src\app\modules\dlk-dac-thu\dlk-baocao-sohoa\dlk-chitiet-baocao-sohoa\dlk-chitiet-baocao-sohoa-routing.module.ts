import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { DlkChiTietBaoCaoSoHoaComponent } from './dlk-chitiet-baocao-sohoa.component';


const routes: Routes = [
  {
    path: ':type',
    loadChildren: () => import('./dlk-chitiet-baocao-sohoa.module').then(m => m.DlkChiTietBaoCaoSoHoaModule),
    children: [
      {
        path: '',
        component: DlkChiTietBaoCaoSoHoaComponent
      }
    ]
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class DlkChiTietBaoCaoSoHoaRoutingModule { }
