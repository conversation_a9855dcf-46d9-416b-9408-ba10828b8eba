import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { AuthGuard } from 'src/app/core/guard/auth.guard';
import { AdministrativeComponent } from './administrative.component';
const routes: Routes = [
  {
    path: '',
    loadChildren: () => import('./administrative.module').then(m => m.AdministrativeModule),
    // tslint:disable-next-line:max-line-length
    children: [
        {
          path: '',
          component: AdministrativeComponent
        },
      ],
      canActivate: [AuthGuard],
    data: {
      anyPermissions: ['oneGateAdminMaster', 'oneGateHCMAdmin', 'oneGateAdministrativeCategory'],
    }
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class AdministrativeRoutingModule { }
