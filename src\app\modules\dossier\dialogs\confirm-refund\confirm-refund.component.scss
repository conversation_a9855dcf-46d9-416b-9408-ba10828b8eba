
.dialog_title {
    font-style: normal;
    font-weight: 500;
    font-size: 20px;
    color: #ce7a58;
}
.information {
    display: inline !important;
    font-size: 16px;
}
::ng-deep {

    .dialog_content {
        font-size: 15px;

        &::-webkit-scrollbar-track {
            box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.1);
            background-color: #f5f5f5;
        }

        &::-webkit-scrollbar {
            width: 5px;
            background-color: #f5f5f5;
        }

        &::-webkit-scrollbar-thumb {
            box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.1);
            background-color: #44444450;
        }
        
        .highlight {
            color: #ce7a58;
        }
    }

    .close-button {
        float: right;
        top: -24px;
        right: -24px;
    }

    .applyBtn {
        margin-top: 1em;
        background-color: #ce7a58;
        color: #fff;
        height: 3em;
        margin-right: 5px;
       
    }

    .cancelBtn {
        border: 1px solid #ce7a58;
        color: #ce7a58;
        height: 3em;
        text-wrap: wrap;
        line-height: normal;
        margin-left: 5px;
        margin-top: 1em;
    }

    .feeData {
        border: 1px solid #000;
    }

}