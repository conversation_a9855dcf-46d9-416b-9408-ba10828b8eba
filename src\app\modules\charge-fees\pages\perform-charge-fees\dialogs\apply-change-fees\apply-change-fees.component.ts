import { Component, OnInit, Inject } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA, MatDialog } from '@angular/material/dialog';
import { FormGroup, FormControl, Validators } from '@angular/forms';
import { ProcostTypeService } from 'src/app/data/service/procost-type/procost-type.service';
import { Router } from '@angular/router';
import { Observable } from 'rxjs';
import { HomeService } from 'src/app/data/service/home/<USER>';
import { FeeTypeService } from 'src/app/data/service/fee-type/fee-type.service';
import { ProcedureService } from 'src/app/data/service/procedure/procedure.service';
import { EnvService } from 'src/app/core/service/env.service';
import { ApprovalAgencyConfigService } from 'src/app/data/service/approval-agency-config/approval-agency-config.service';
import { BeneficiaryAccountAgencyService } from 'src/app/data/service/beneficiary-account-agency/beneficiary-account-agency.service';
import { BankService } from 'src/app/data/service/bank/bank.service';
import { DeploymentService } from 'src/app/data/service/deployment.service';
import * as tUtils from 'data/service/thoai.service';
import { KeycloakService } from 'keycloak-angular';
import { UserService } from 'src/app/data/service/user.service';
import { DatePipe } from '@angular/common';
import { DossierService } from 'src/app/data/service/dossier/dossier.service';
import { ConfirmComponent, ConfirmDialogModel } from 'src/app/shared/components/dialogs/confirm/confirm.component';
import { ConfirmDialogComponent, ConfirmationDialogModel } from 'src/app/shared/components/dialogs/confirm-dialog/confirm-dialog.component';

export interface Agency {
  id: string;
  name: string;
}

@Component({
  selector: 'app-apply-change-fees',
  templateUrl: './apply-change-fees.component.html',
  styleUrls: ['./apply-change-fees.component.scss', '/src/app/shared/scss/form-field-outline.scss']
})
export class ApplyChangeFeesComponent implements OnInit {
  addForm = new FormGroup({
    fee: new FormControl(''),
    phoneNumber: new FormControl(''),
    submitterName: new FormControl(''),
    dateSubmit: new FormControl(''),
    type: new FormControl('0'),
    note: new FormControl(''),
    bank: new FormControl(''),
    status: new FormControl('' + 1),
  });
  listAgency: Agency[] = [];
  listProcedureLevel = [];
  listSector = [];
  listLanguage = [
    {
      languageId: 228,
      name: 'Tiếng Việt'
    },
    {
      languageId: 46,
      name: 'English'
    }
  ];
  newAttribute: any = {
    languageId: 228,
    name: '',
    listLanguageItem: JSON.parse(JSON.stringify(this.listLanguage))
  };
  languageIdUsed = [228];
  fieldArray: Array<any> = [this.newAttribute];
  filteredAgencyOptions: Observable<Agency[]>;
  status: Array<any> = [
    {
      status: 0,
      name: 'Đóng'
    },
    {
      status: 1,
      name: 'Mở'
    }
  ];
  statusEn: Array<any> = [
    {
      status: 0,
      name: 'Close'
    },
    {
      status: 1,
      name: 'Open'
    }
  ];
  listType: Array<any> = [
    {
      id: 0,
      name: 'Phí'
    },
    {
      id: 1,
      name: 'Lệ phí'
    }
  ];
  listTypeEn: Array<any> = [
    {
      id: 0,
      name: 'Fee'
    },
    {
      id: 1,
      name: 'Charge'
    }
  ];
  listAllow: Array<any> = [
    {
      id: 0,
      name: 'Không'
    },
    {
      id: 1,
      name: 'Có'
    }
  ];
  listAllowEn: Array<any> = [
    {
      id: 0,
      name: 'No'
    },
    {
      id: 1,
      name: 'Yes'
    }
  ];
  page = 0;
  ELEMENTDATA = [];
  dataSource = {};
  idPosted;
  test = 'abc';
  nameParent;
  listFeeType = [];

  config = this.envService.getConfig();

  keywordAgencyapprovalAgency = '';
  totalPagesAgencyapprovalAgency = 0;
  currentPageAgencyapprovalAgency = 0;
  pageSizeAgencyapprovalAgency = 10;
  timeOutAgencyapprovalAgency: any = null;
  listAgencyapprovalAgency: Array<any> = [];
  parentAgency = '';
  userAgencyRoot: any;

  keywordAgencyapprovaledAgency = '';
  totalPagesAgencyapprovaledAgency = 0;
  currentPageAgencyapprovaledAgency = 0;
  pageSizeAgencyapprovaledAgency = 10;
  timeOutAgencyapprovaledAgency: any = null;
  listAgencyapprovaledAgency: Array<any> = [];

  keywordBank = '';
  totalPagesBank = 0;
  currentPageBank = 0;
  pageSizeBank = 10;
  timeOutBank: any = null;
  listBank: Array<any> = [];

  selectedLang: string;
  selectedLangId: number;

  agencyMaidId = '';

  OS_BDG = false;
  env = this.deploymentService.getAppDeployment()?.env;

  dossier: any;
  procedureName = "";
  user: any;
  typeAction = '';
  disableUpdate = false;
  beneficiaryAccount: any;
  currentDossierDetail: any;

  constructor(
    public dialogRef: MatDialogRef<ApplyChangeFeesComponent>,
    @Inject(MAT_DIALOG_DATA) public data: ApplyChangeFeesDialogModel,
    private beneficiaryAccountAgencyService: BeneficiaryAccountAgencyService,
    private dialog: MatDialog,
    private envService: EnvService,
    private keycloakService: KeycloakService,
    private homeService: HomeService,
    private feeTypeService: FeeTypeService,
    private userService: UserService,
    private router: Router,
    private datepipe:DatePipe,
    private dossierService: DossierService,
    private deploymentService: DeploymentService) {
      this.OS_BDG = this.env?.OS_BDG?.isEnableOs === true ? true : false;
      this.dossier = data.dossierData;
      this.typeAction = data.typeAction;
    }

  async ngOnInit(): Promise<void> {
    if (Number(localStorage.getItem('languageId')) === 46) {
      this.status = this.statusEn;
      this.listType = this.listTypeEn;
      this.listAllow = this.listAllowEn;
    }
    let rootAgencyId: any = '';
    let agencyTempId: any = '';
    rootAgencyId = this.config.rootAgency.id;
    this.selectedLang = localStorage.getItem('language');
    this.selectedLangId = Number(localStorage.getItem('languageId'));
    if(!!this.dossier.procedure?.translate){
      this.procedureName = this.dossier.procedure?.translate.name
    }
    this.getUserAccount();
    
    if(this.typeAction == "add"){
      this.checkAccountPay();
      this.getValueNew();
    }

    if(this.typeAction == "cancel"){
      this.cancelValueNew();
    }
    // this.mainService.setPageTitle(this.pageTitle[localStorage.getItem('language')]);
    setTimeout(() => {
      const userAgency = JSON.parse(localStorage.getItem('userAgency'));
      this.userAgencyRoot = userAgency;
      let parentAgency = rootAgencyId;
      if (userAgency !== null && userAgency !== undefined) {
        let agency = '';
        if (!!userAgency.parent && !!userAgency.parent.id) {
          parentAgency = userAgency.parent.id;
          this.parentAgency = userAgency.parent.id;
          agency = userAgency.id;
        }
        else if (userAgency.id !== this.config.rootAgency.id) {
          parentAgency = userAgency.id;
          this.parentAgency = userAgency.id;
          agency = '';
        }
        // this.searchForm = new FormGroup({
        //   fromDate: new FormControl(new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)),
        //   toDate: new FormControl(new Date()),
        //   sectorId: new FormControl(''),
        //   agencyCtrl: new FormControl(agency),
        //   procedureLevelId: new FormControl('')
        // });
        agencyTempId = this.parentAgency;
        // if (this.parentAgency !== '') {
        //   this.keySearchSectorAgency = '&agency-id=' + this.parentAgency;
        // }
        // if (agency !== '') {
        //   this.keySearchSectorAgency = '&agency-id=' + agency;
        //   agencyTempId = agency;
        // }
      }
      // tslint:disable-next-line:max-line-length
      // this.getListAgencyapprovaledAgency(this.keywordAgencyapprovaledAgency, this.currentPageAgencyapprovaledAgency, this.pageSizeAgencyapprovaledAgency);
    }, 500);
  }

  getValueNew(){
    if(this.dossier.total){
      this.addForm.patchValue({
        fee: this.dossier.total
      });
    }
    if(!!this.dossier.accepter && !!this.dossier.accepter.fullname){
      this.addForm.patchValue({
        submitterName: this.dossier.accepter.fullname
      });
    }
    const d = tUtils.newDate();
    this.addForm.patchValue({
      dateSubmit: d.getFullYear() + '-' + ('0' + (d.getMonth() + 1)).slice(-2) + '-' + ('0' + d.getDate()).slice(-2),
    });
    console.log(d.getFullYear());
    console.log(d.getMonth());
    console.log(d.getDay());
  }

  cancelValueNew(){
    let oldValue = this.dossier.chargeFeesDossier[0];
    if(oldValue?.total){
      this.addForm.patchValue({
        fee: oldValue.total
      });
    }
    if(!!this.dossier.accepter && !!this.dossier.accepter.fullname){
      this.addForm.patchValue({
        submitterName: this.dossier.accepter.fullname
      });
    }
    // const d = tUtils.newDate();
    // this.addForm.patchValue({
    //   dateSubmit: d.getFullYear() + '-' + ('0' + (d.getMonth() + 1)).slice(-2) + '-' + ('0' + d.getDate()).slice(-2),
    // });
    if(!!oldValue?.paymentMethod){
      this.addForm.patchValue({
        type: "" + oldValue?.paymentMethod
      });
    }
    if(!!oldValue?.phoneNumber){
      this.addForm.patchValue({
        phoneNumber: oldValue?.phoneNumber
      });
    }
    if(!!oldValue?.submitterName){
      this.addForm.patchValue({
        phoneNumber: oldValue?.submitterName
      });
    }
    if(!!oldValue?.submitDate){
      this.addForm.patchValue({
        dateSubmit: oldValue?.submitDate
      });
    }
    if(!!oldValue?.note){
      this.addForm.patchValue({
        note: oldValue?.note
      });
    }
  }

  async checkAccountPay() {
    // const findMethod = this.listPaymentMethod.find(x => x.id === this.paymentMethodId);
      // const currentDossierDetail = await this.dossierService.getDossierDetail(this.dossier.id).toPromise();
      this.disableUpdate = true;
      this.currentDossierDetail = await this.dossierService.getDossierDetail(this.dossier.id).toPromise();
      let agencyId = '';
      if(!!this.currentDossierDetail?.agency?.parent?.id){
        agencyId = this.currentDossierDetail?.agency?.parent?.id;
      }
      else if(!!this.currentDossierDetail?.agency?.id){
        agencyId = this.currentDossierDetail?.agency?.id;
      }
      else if(!!this.dossier.agency?.id){
        agencyId = this.dossier.agency?.id
      }
      let subsystemId = this.config.subsystemId;
      if(!subsystemId || subsystemId == ''){
        subsystemId = '5f7c16069abb62f511880006';
      }
      this.dossierService.getAcountPayment(agencyId, subsystemId, this.dossier.procedureProcessDefinition?.id).subscribe(data => {
        console.log(data);
        this.beneficiaryAccount = data;
        // if (!!data.beneficiaryAccount && data.beneficiaryAccount !== ''){
        //   resolve(true);
        // }
        // else{
        //   this.homeService.error('Không có tài khoản thụ hưởng', 'No beneficiary account');
        //   resolve(false);
        // }
        this.disableUpdate = false;
      },
      err => {
        // this.homeService.error('Không có tài khoản thụ hưởng', 'No beneficiary account');
        this.disableUpdate = false;
      });
  }

  onDismiss(): void {
    this.dialogRef.close();
  }

  async save() {
    if(this.typeAction == "add"){
      this.saveNew();
    }

    if(this.typeAction == "cancel"){
      this.cancel();
    }

  }

  saveNew(){
    if(this.addForm.valid){
      this.disableUpdate = true;
      const valueform = this.addForm.getRawValue();
      let postValue: any = {};
      postValue.total = this.dossier.total;
      postValue.status = 1;
      postValue.paymentMethod = valueform.type;
      postValue.dossierId = this.dossier.id;
      postValue.note = valueform.note;
      postValue.submitterName = valueform.submitterName;
      postValue.submitDate = valueform.dateSubmit;
      postValue.phoneNumber = valueform.phoneNumber;
      if(!!this.beneficiaryAccount){
        postValue.beneficiaryAccount = this.beneficiaryAccount;
      }
      // pay detail
      let payDetail = [];
      for(let i = 0; i < this.dossier.dossierFee.length; i++){
        const fee = {
          amount: this.dossier.dossierFee[i].amount,
          quantity: this.dossier.dossierFee[i].quantity,
          status: 0,
          dossierFee: this.dossier.dossierFee[i]
        }
        payDetail.push(fee);
      }
      postValue.paymentDetail = payDetail;
      this.user.content = "Thực hiện thanh toán";
      let date: string = "" 
      date = this.datepipe.transform(tUtils.newDate(), "yyyy-MM-ddThh:mm:ssZZZZZ")
      this.user.createdDate = date;
      postValue.userLogs = [this.user];
      this.dossierService.postChangeFees(postValue).subscribe(data => {
        this.disableUpdate = true;
        const result = {
            name: ' ',
            status: true
          };
          this.dialogRef.close(result);
      },
      err => {
        this.disableUpdate = true;
        const result = {
          name: ' ',
          status: false
        };
        this.dialogRef.close(result);
      }
      );
    }
  }

  cancel(){
    const dialogData = new ConfirmationDialogModel('Hủy thu phí', `Bạn có chắc chắn muốn hủy thu phí của hồ sơ ${this.dossier.code} ?`);
    const dialogRef = this.dialog.open(ConfirmDialogComponent, {
      width: '512px',
      data: dialogData,
      disableClose: true,
      autoFocus: false,
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
      if (!!dialogResult) {
        this.user.content = "Hủy thu phí";
        let date: string = "" 
        date = this.datepipe.transform(tUtils.newDate(), "yyyy-MM-ddThh:mm:ssZZZZZ")
        this.user.createdDate = date;
        if(!!this.dossier.chargeFeesDossier[0].userLogs && this.dossier.chargeFeesDossier[0].userLogs.length > 0){
          this.dossier.chargeFeesDossier[0].userLogs.push(this.user);
        }
        else{
          this.dossier.chargeFeesDossier[0].userLogs = [this.user];
        }
        this.disableUpdate = true;
        this.dossierService.cancelChangeFees(this.dossier.chargeFeesDossier[0].id, this.dossier.chargeFeesDossier[0]).subscribe(data => {
          this.disableUpdate = true;
          if(!!data.affectedRows && data.affectedRows > 0){
          const result = {
              name: ' ',
              status: true
            };
            this.dialogRef.close(result);
          }
          else{
            const result = {
              name: ' ',
              status: false
            };
            this.dialogRef.close(result);
          }
        },
        err => {
          this.disableUpdate = true;
          const result = {
            name: ' ',
            status: false
          };
          this.dialogRef.close(result);
        }
        );
      }
    });
  }

  getUserAccount() {
    this.keycloakService.loadUserProfile().then(user => {
      // tslint:disable-next-line: no-string-literal
      const accountId = user['attributes'].user_id[0];
      this.userService.getUserInfo(accountId).subscribe(data => {
        this.user = data;
      });
    });
  }

  // triggeragencyapprovaledEvent($event){
  //   console.log($event);
  // }



  private _filter(name: string): Agency[] {
    const filterValue = name.toString().toLowerCase();
    return this.listAgency.filter(option => option.name.toString().toLowerCase().includes(filterValue));
  }

}

export class ApplyChangeFeesDialogModel {
  constructor(public dossierData: any, public typeAction: string) {
  }
}
