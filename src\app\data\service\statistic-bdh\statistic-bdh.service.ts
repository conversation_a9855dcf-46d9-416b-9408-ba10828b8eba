import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { EnvService } from 'core/service/env.service';
import { ApiProviderService } from 'src/app/core/service/api-provider.service';

import * as fs from 'file-saver';
const EXCEL_TYPE = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8';
const EXCEL_EXTENSION = '.xlsx';
import { Workbook, Worksheet} from 'exceljs';
@Injectable({
  providedIn: 'root'
})
export class StatisticBdhService {
  language = localStorage.getItem('language') === 'vi' ? 228 : 46;
  config = this.envService.getConfig();

  constructor(
    private http: HttpClient,
    private apiProviderService: ApiProviderService,
    private envService: EnvService,
  ) { }

   private basedataURL = this.apiProviderService.getUrl('digo', 'basedata');
    private padmanURL = this.apiProviderService.getUrl('digo', 'padman');
  //  private padmanURL = "http://localhost:8081";
  // private basedataURL = "http://localhost:8888";
  // private basepadURL = "http://localhost:8069";

  getAgencyList(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.get(this.basedataURL + '/agency/--search' + searchString, { headers }).pipe();
  }

  getDigitizationDossierReport(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.get(this.padmanURL + '/bdh-dossier-statistic/digitization-by-agency' + searchString, { headers }).pipe();
  }

  getAgencyTags(agencyId): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.get(`${this.basedataURL}/agency/${agencyId}/name+tag`, { headers }).pipe();
  }
    public exportToExcelStatisticAll(
        reportHeading: string,
        reportSubHeading: string,
        nameReport: string,
        json: any[],
        excelFileName: string,
        sheetName: string,
        footerData: any[]
    ) {
        const data = json;
        // create workbook and worksheet
        const workbook = new Workbook();
        workbook.creator = 'Snippet Coder';
        workbook.lastModifiedBy = 'SnippetCoder';
        workbook.created = new Date();
        workbook.modified = new Date();
        const worksheet = workbook.addWorksheet(sheetName);

        worksheet.getColumn('A').font = {name: 'Times New Roman', size: 12};
        worksheet.getColumn('B').font = {name: 'Times New Roman', size: 12};
        worksheet.getColumn('C').font = {name: 'Times New Roman', size: 12};
        worksheet.getColumn('D').font = {name: 'Times New Roman', size: 12};
        worksheet.getColumn('E').font = {name: 'Times New Roman', size: 12};
        worksheet.getColumn('F').font = {name: 'Times New Roman', size: 12};
        worksheet.getColumn('G').font = {name: 'Times New Roman', size: 12};
        worksheet.getColumn('H').font = {name: 'Times New Roman', size: 12};
        worksheet.getColumn('I').font = {name: 'Times New Roman', size: 12};
        worksheet.getColumn('J').font = {name: 'Times New Roman', size: 12};
        worksheet.getColumn('K').font = {name: 'Times New Roman', size: 12};
        worksheet.getColumn('L').font = {name: 'Times New Roman', size: 12};
        worksheet.getColumn('M').font = {name: 'Times New Roman', size: 12};
        worksheet.getColumn('N').font = {name: 'Times New Roman', size: 12};
        // worksheet.getColumn('O').font = {name: 'Times New Roman', size: 12};
        // worksheet.getColumn('P').font = {name: 'Times New Roman', size: 12};
        // worksheet.getColumn('Q').font = {name: 'Times New Roman', size: 12};

        worksheet.mergeCells('C1:H2');
        worksheet.getCell('C1').value = reportHeading;
        worksheet.getCell('C1').alignment = { horizontal: 'center', vertical: 'middle' };
        worksheet.getCell('C1').font = { size: 15, bold: true, name: 'Times New Roman' };

        worksheet.mergeCells('C3:H3');
        worksheet.getCell('C3').value = reportSubHeading;
        worksheet.getCell('C3').alignment = { horizontal: 'center', vertical: 'middle' };
        worksheet.getCell('C3').font = { size: 12, italic: true, name: 'Times New Roman' };

        // NỘI DUNG TABLE-HEADER
        worksheet.mergeCells('A5:A7');
        worksheet.getCell('A5').value = 'STT';

        worksheet.mergeCells('B5:B7');
        worksheet.getCell('B5').value = 'Cơ quan, đơn vị';

        worksheet.mergeCells('C5:C7');
        worksheet.getCell('C5').value = 'Tổng hồ sơ tiếp nhận';

        worksheet.mergeCells('D5:G5');
        worksheet.getCell('D5').value = 'Trong đó';

        worksheet.mergeCells('D6:D7');
        worksheet.getCell('D6').value = 'Hồ sơ tiếp nhận (trực tiếp)';

        worksheet.mergeCells('E6:E7');
        worksheet.getCell('E6').value = 'Hồ sơ tiếp nhận (trực tuyến)';

        worksheet.mergeCells('F6:G6');
        worksheet.getCell('F6').value = 'Số hóa thành phần hồ sơ (trực tiếp)';

        worksheet.getCell('F7').value = 'Một phần';
        worksheet.getCell('G7').value = 'Toàn bộ';

        worksheet.mergeCells('H5:H7');
        worksheet.getCell('H5').value = 'Tỷ lệ số hóa TPHS (%)';

        worksheet.mergeCells('I5:I7');
        worksheet.getCell('I5').value = 'Tổng số hồ sơ đã giải quyết';

        worksheet.mergeCells('J5:K5');
        worksheet.getCell('J5').value = 'Trong đó';

        worksheet.mergeCells('J6:J7');
        worksheet.getCell('J6').value = 'Cấp kết quả điện tử';

        worksheet.mergeCells('K6:K7');
        worksheet.getCell('K6').value = 'Tỷ lệ cấp kết quả điện tử (%)';

        worksheet.mergeCells('L5:L7');
        worksheet.getCell('L5').value = 'Tổng hồ sơ tiếp nhận (Không bao gồm HS các TTHC không tính tái sử dụng)';

        worksheet.mergeCells('M5:M7');
        worksheet.getCell('M5').value = 'Hồ sơ sử dụng lại dữ liệu số hóa';

        worksheet.mergeCells('N5:N7');
        worksheet.getCell('N5').value = 'Tỷ lệ tái sử dụng KQ số hóa';

        // worksheet.mergeCells('D5:D7');
        // worksheet.getCell('D5').value = 'Hồ sơ có tài khoản DVCQG';
        // worksheet.mergeCells('E5:H5');
        // worksheet.getCell('E5').value = 'Trong đó';
        // worksheet.mergeCells('E6:E7');
        // worksheet.getCell('E6').value = 'Hồ sơ tiếp nhận (trực tiếp)';
        // worksheet.mergeCells('F6:F7');
        // worksheet.getCell('F6').value = 'Hồ sơ tiếp nhận (trực tuyến)';
        // worksheet.mergeCells('G6:H6');
        // worksheet.getCell('G6').value = 'Số hóa thành phần hồ sơ (trực tiếp)';
        // worksheet.getCell('G7').value = 'Một phần';
        // worksheet.getCell('H7').value = 'Toàn bộ';
        // worksheet.mergeCells('I5:I7');
        // worksheet.getCell('I5').value = 'Tỷ lệ số hóa TPHS (%)';
        // worksheet.mergeCells('J5:J7');
        // worksheet.getCell('J5').value = 'Tổng số hồ sơ đã giải quyết';
        // worksheet.mergeCells('K5:L5');
        // worksheet.getCell('K5').value = 'Trong đó';
        // worksheet.mergeCells('K6:K7');
        // worksheet.getCell('K6').value = 'Cấp kết quả điện tử';
        // worksheet.mergeCells('L6:L7');
        // worksheet.getCell('L6').value = 'Tỷ lệ cấp kết quả điện tử (%)';
        // worksheet.mergeCells('M5:M7');
        // worksheet.getCell('M5').value = 'Tổng hồ sơ tiếp nhận (Không bao gồm HS các TTHC không tính tái sử dụng)';
        // worksheet.mergeCells('N5:N7');
        // worksheet.getCell('N5').value = 'Hồ sơ sử dụng lại dữ liệu số hóa';
        // worksheet.mergeCells('O5:O7');
        // worksheet.getCell('O5').value = 'Tỷ lệ tái sử dụng KQ số hóa';
        // worksheet.mergeCells('P5:P7');
        // worksheet.getCell('P5').value = 'Hồ sơ công dân ý kiến về kết quả';
        // worksheet.mergeCells('Q5:Q7');
        // worksheet.getCell('Q5').value = 'Hồ sơ đã xử lý ý kiến của công dân về KQ';

        worksheet.getColumn('B').width = 50;
        worksheet.getColumn('C').width = 20;
        worksheet.getColumn('D').width = 20;
        worksheet.getColumn('E').width = 20;
        worksheet.getColumn('F').width = 20;
        worksheet.getColumn('G').width = 20;
        worksheet.getColumn('H').width = 20;
        worksheet.getColumn('I').width = 20;
        worksheet.getColumn('J').width = 20;
        worksheet.getColumn('K').width = 20;
        worksheet.getColumn('L').width = 20;
        worksheet.getColumn('M').width = 20;
        worksheet.getColumn('N').width = 20;
        // worksheet.getColumn('O').width = 20;
        // worksheet.getColumn('P').width = 20;
        // worksheet.getColumn('Q').width = 20;
        worksheet.getRow(7).height = 50;

        worksheet.getColumn('A').alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
        worksheet.getColumn('B').alignment = { horizontal: 'left', vertical: 'middle', wrapText: true };
        worksheet.getColumn('C').alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
        worksheet.getColumn('D').alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
        worksheet.getColumn('E').alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
        worksheet.getColumn('F').alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
        worksheet.getColumn('G').alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
        worksheet.getColumn('H').alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
        worksheet.getColumn('I').alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
        worksheet.getColumn('J').alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
        worksheet.getColumn('K').alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
        worksheet.getColumn('L').alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
        worksheet.getColumn('M').alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
        worksheet.getColumn('N').alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
        // worksheet.getColumn('O').alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
        // worksheet.getColumn('P').alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
        // worksheet.getColumn('Q').alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };

        worksheet.properties.outlineLevelCol = 2;
        worksheet.properties.defaultRowHeight = 15;
        let i = 5;
        const j = 7;
        for (i; i <= j; i++) {
            let k = 1;
            const l = 14;
            for (k; k <= l; k++) {
                worksheet.findCell(i, k).alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
                worksheet.findCell(i, k).fill = {
                    type: 'pattern',
                    pattern: 'solid',
                    fgColor: { argb: 'C0C0C0C0' },
                    bgColor: { argb: 'FF0000FF' }
                };
                worksheet.findCell(i, k).border = { top: { style: 'thin' }, left: { style: 'thin' }, bottom: { style: 'thin' }, right: { style: 'thin' } };
                worksheet.findCell(i, k).font = { size: 12, bold: true, name: 'Times New Roman' };
            }
        }

        // get all columns from JSON
        let columnsArray: any[];
        for (const key in json) {
            if (json.hasOwnProperty(key)) {
                columnsArray = Object.keys(json[key]);
            }
        }
        // Add Data and Conditional Formatting
        data.forEach((element: any) => {
            const eachRow = [];
            columnsArray.forEach((column) => {
                eachRow.push(element[column]);
            });

            const borderrow = worksheet.addRow(eachRow);
            borderrow.eachCell((cell) => {
                cell.border = { top: { style: 'thin' }, left: { style: 'thin' }, bottom: { style: 'thin' }, right: { style: 'thin' } };
                cell.alignment = { horizontal: 'left', vertical: 'middle', wrapText: true };
                cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
            });
        });

        // footer data row
        if (footerData != null) {
            footerData.forEach((element: any) => {
                const eachRow = [];
                element.forEach((val: any) => {
                    eachRow.push(val);
                });
                const footerRow = worksheet.addRow(eachRow);
                const cellMerge = 'A' + footerRow.number + ':B' + footerRow.number;
                worksheet.mergeCells(cellMerge);
                footerRow.eachCell((cell) => {
                    cell.font = { size: 13, bold: true, name: 'Times New Roman' };
                    cell.border = { top: { style: 'thin' }, left: { style: 'thin' }, bottom: { style: 'thin' }, right: { style: 'thin' } };
                    cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
                });
            }); 
        }
        // Save Excel File
        // tslint:disable-next-line:no-shadowed-variable
        workbook.xlsx.writeBuffer().then((data: ArrayBuffer) => {
            const blob = new Blob([data], { type: EXCEL_TYPE });
            fs.saveAs(blob, excelFileName + EXCEL_EXTENSION);
        });
    }

  getDetailDossier(search): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.padmanURL + '/bdh-dossier-statistic/digitization-detail' + search, { headers }).pipe();
  }

  getListAgencyWithParent(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.get(this.basedataURL + '/agency/' + searchString, { headers }).pipe();
  }
}
