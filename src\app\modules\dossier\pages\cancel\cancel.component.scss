::ng-deep {
    .checkAllProcedureAdd{
        margin-top: 16px;
    }
    .show{
        display: block;
    }
    .hiden{
        display: none;
    }
    .mat-tooltip {
        font-size: 13px;
    }
    .searchForm {
        .advanced-box {
            .panel {
                background-color: #f9f9f9;
                box-shadow: none;
                display: flex;
                flex-wrap: wrap;
            }
        }
        .advanced-button {
            margin: auto;
            color: #ce7a58;
            margin-bottom: 0.7em;
            cursor: pointer;
            .mat-icon {
                vertical-align: middle;
            }
        }
    }
    .mat-checkbox-frame {
        border-radius: 4px;
    }
    .mat-expansion-panel-body {
        padding: 5px 0 0 0 !important;
    }
    .frm_main {
        margin-top: 1em;
        background-color: #fff;
        box-shadow: 4px 0px 8px rgba(0, 0, 0, 0.1);
        padding: 1em 1em 1em 1em;
        .btn-claim {
            background-color: #e8e8e8;
            color: #666666;
            height: 3.2em !important;
            margin-top: 4px;
        }
        .mat-stroked-button {
            border: none !important;
        }
    }
    .mat-checkbox-checked.mat-accent {
        .mat-checkbox-background {
            background-color: #ce7a58 !important;
        }
    }
    .frm_tbl {
        .mat-menu-item {
            .mat-icon {
                margin-right: 16px;
                vertical-align: middle;
                color: #ce7a58;
            }
        }
        .mat-header-row {
            background-color: #e8e8e8;
            min-height: 3.5em !important;
            .mat-header-cell {
                color: #495057;
                font-size: 14px;
                font-weight: 500;
                p {
                    margin-bottom: 0;
                    font-weight: 400;
                    font-style: italic;
                }
            }
        }
        .mat-column-select {
            flex: 0 0 5%;
        }
        .mat-column-stt {
            flex: 0 0 5%;
            padding-left: 1em;
            cursor: pointer;
        }
        .mat-column-code {
            flex: 0 0 10%;
            padding-right: 0.5em;
        }
        .mat-column-procedureName {
            flex: 2 0 10%;
            padding: 0 0.5em 0 1em;
            .procedureName {
                display: -webkit-box;
                -webkit-line-clamp: 4;
                -webkit-box-orient: vertical;
                width: 100%;
                overflow: hidden;
                text-overflow: ellipsis;
            }
        }
        .mat-column-noidung {
            flex: 2 0 10%;
            padding: 0 0.5em 0 1em;
            
        }
        .mat-column-applicantName {
            flex: 1 0 15%;
            padding-right: 0.5em;
            ul {
                padding: 0;
                li:nth-child(1) {
                    letter-spacing: 0;
                    font-size: 15px;
                    font-weight: 500;
                    color: #1E2F41;
                }
            }
        }
        .mat-column-address {
            flex: 1 0 10%;
            padding: .5em 1em .5em .5em;
        }
        .mat-column-appliedDate, .mat-column-profileOwner{
            flex: 0 0 10%;
            padding-right: 0.5em;
        }
        .mat-column-reason {
            flex: 0 0 10%;
            padding-right: 0.5em;
        }
        .mat-column-citizenWithdrawComment {
            flex: 0 0 10%;
            padding-right: 0.5em;
        }
        .mat-column-status {
            flex: 0 0 10%;
            float: right;
        }
        .mat-column-action {
            flex: 0 0 5%;
            float: right;
        }
        .btn_downloadForm {
            padding: 0;
            color: #ce7a58;
            .mat-button-wrapper {
                display: flex;
                .download_icon {
                    .mat-icon {
                        vertical-align: middle;
                        margin-right: 0.2em;
                        background-color: #ce7a58;
                        color: #fff;
                        border-radius: 50%;
                        padding: 0.2em;
                        transform: scale(0.8);
                    }
                }
                span {
                    align-self: center;
                }
            }
        }
        .cell_code {
            color: #ce7a58;
            font-weight: 500;
            text-decoration: none;
            cursor: pointer;
            max-width: 100%;
            word-break: break-all;
        }
        .cell_code_online {
            color: #ce7a58;
            font-weight: 500;
            text-decoration: none;
            cursor: pointer;
            max-width: 100%;
            word-break: break-all;
        }
        .cell_code_direct {
            color: blue;
            font-weight: 500;
            text-decoration: none;
            cursor: pointer;
            max-width: 100%;
            word-break: break-all;
        }
        .cell_code_other {
            color: green;
            font-weight: 500;
            text-decoration: none;
            cursor: pointer;
            max-width: 100%;
            word-break: break-all;
        }
        .mat-row {
            border: none;
            &:nth-child(even) {
                background-color: #fafafa;
            }
            &:nth-child(odd) {
                background-color: #fff;
            }
        }
    }
    .menuAction {
        font-weight: 500;
        .mat-icon {
            color: #ce7a58;
        }
    }
}

.top-control {
    padding-bottom: 0.8em;
    .btn_ctrl {
        background-color: #e8e8e8;
        color: #666;
        float: right;
        .mat-icon:nth-child(1) {
            color: #ce7a58;
            margin-right: 0.2em;
        }
        .mat-icon:nth-child(2) {
            margin-left: 0.2em;
        }
    }
}

.frm_tbl {
    table {
        border-radius: 4px;
        border: 1px solid #ececec;
        width: 100%;
    }
    .checkbox {
        border-radius: 4px;
    }
}

@media screen and (max-width: 600px) {
    .frm_tbl {
        .mat-header-row {
            display: none;
        }
        .mat-table {
            border: 0;
            vertical-align: middle;
            caption {
                font-size: 1em;
            }
            .mat-row {
                border-bottom: 5px solid #ddd;
                display: block;
                min-height: unset;
            }
            .mat-cell {
                border-bottom: 1px solid #ddd;
                display: block;
                font-size: 14px;
                text-align: right;
                margin-bottom: 4%;
                padding: 0 0.5em;
                &:before {
                    content: attr(data-label);
                    float: left;
                    font-weight: 500;
                    font-size: 14px;
                }
                &:last-child {
                    border-bottom: 0;
                }
                &:first-child {
                    margin-top: 4%;
                }
            }
        }
    }
     ::ng-deep {
        .frm_tbl {
            .mat-column-status {
                float: unset;
            }
            .mat-column-action {
                float: unset;
            }
            .mat-row {
                &:nth-child(even) {
                    background-color: unset;
                }
                &:nth-child(odd) {
                    background-color: unset;
                }
            }
        }
    }
}

.title-reminder {
    cursor: pointer;
    padding-right: 1rem;
    .title {
        font: 500 20px/32px Roboto, "Helvetica Neue", sans-serif;
    }
    .content {
        padding-left: 0.5rem;
        width: 100%;
        vertical-align: middle;
        align-items: center !important;
    }
    .content:hover {
        border-radius: 10px;
        background-color: #f1f1f1;
    }
    .count-task {
        color: red;
    }
}

.menu_reminder {
    padding-right: 1rem;
    .panel {
        overflow-y: auto;
        span {
            height: 100%;
        }
        .active {
            color: #ce7a58;
            background-color: #f4eadf;
        }
        .interfaceWorkHorizontalClass {
            width: unset !important;
        }
        #submenu {
            margin: 0 0 0 0;
            width: 100%;
            text-align: left;
            padding: 0.2em 1em;
            .submenuTitle {
                //   display: block;
                //   width: 100%;
                line-height: 20px;
                white-space: pre-wrap;
                padding: 0.5em 0 0.5em 0.5em;
            }
            .count {
                min-width: 20%;
                background-color: #db3700 !important;
                padding: 2px 10px !important;
                border-radius: 15px !important;
                color: white !important;
                margin-right: 0.3em;
            }
        }
        #submenu:focus {
            background-color: 'blue';
        }
    }
}

::ng-deep .menu_reminder .mat-expansion-panel-body {
    padding: 5px 0 5px 0 !important;
}

.search {
    h2 {
        margin: 0 0 0 0;
    }
}

::-webkit-scrollbar-track {
    box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.1);
    background-color: #F5F5F5;
    border-radius: 10px;
}

::-webkit-scrollbar {
    width: 10px;
    background-color: #F5F5F5;
}

::-webkit-scrollbar-thumb {
    border-radius: 10px;
    background-color: #FFF;
    background-image: -webkit-gradient(linear, 40% 0%, 75% 84%, from(#ce7a58), to(#ce7a58), color-stop(.6, #ce7a58))
}
.top-controlcancel {
    padding-bottom: 0.8em;

    .btn_ctrl {
        background-color: #e8e8e8;
        color: #666;
        float: right;

        .mat-icon:nth-child(1) {
            color: #ce7a58;
            margin-right: 0.2em;
        }

        .mat-icon:nth-child(2) {
            margin-left: 0.2em;
        }
    }
}
.more-text-kha {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    word-break: break-word;
}
.config-kha-enabled {
    .frm_tbl .mat-row {
        padding: 1em 0;
    }
}

.primary-btn {
    background-color: #ce7a58;
    color: #fff;
    height: 2.8em;
    margin-top: 10px;
}

.dossier-name {
    display: -webkit-box;
    -webkit-line-clamp: 4;
    -webkit-box-orient: vertical;
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
}

.overflow-cell {
    width: 100%;
    max-height: 5rem;
    overflow: auto;
}