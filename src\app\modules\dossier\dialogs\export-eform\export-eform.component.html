<button mat-icon-button class="close-button" (click)="onDismiss()">
    <mat-icon>close</mat-icon>
</button>
<h3 class="dialog_title" mat-dialog-title><span i18n><PERSON>i<PERSON>u mẫu gi<PERSON>y tờ</span>: {{formName}}</h3>
<div>
    <div fxLayout="row" fxLayoutAlign="end" class = "exportFile">
        <button type="submit" mat-flat-button class="btn_next btnSecondary" [disabled]="isLoading" [matMenuTriggerFor]="moreMenu">
                <mat-spinner diameter="25" *ngIf="isLoading === true"></mat-spinner>
                <!-- <mat-spinner diameter="25" ></mat-spinner> -->
                <!-- <mat-icon>save</mat-icon> -->
                <span i18n>Xuất mẫu đơn</span>
        </button>
        <!-- <button class="btnMore" mat-flat-button [disabled]="isLoading" [matMenuTriggerFor]="moreMenu" *ngIf="!isLoading">
            <mat-icon>more_horiz</mat-icon>
        </button> -->
        <mat-menu #moreMenu="matMenu">
            <button mat-menu-item class="menuAction" (click)="changeFile('doc')" fxLayout="row">
                <div class="file_icon" [ngStyle]="{'background-image': 'url('+ this.config.cloudStaticURL + 'icon/files/300x300/' + 'doc' + '.png' +')'}"></div>
                <span>Xuất file Word</span>
            </button>
            <button mat-menu-item class="menuAction" (click)="changeFile('json')" fxLayout="row" *ngIf="enableExportJSONFormEform">
                <div class="file_icon" [ngStyle]="{'background-image': 'url('+ this.config.cloudStaticURL + 'icon/files/300x300/' + 'json' + '.png' +')'}"></div>
                <span>Xuất file JSON</span>
            </button>
            <button mat-menu-item class="menuAction" (click)="changeFile('xml')" fxLayout="row" *ngIf="enableExportXMLFormEform">
                <div class="file_icon" [ngStyle]="{'background-image': 'url('+ this.config.cloudStaticURL + 'icon/files/300x300/' + 'xml' + '.png' +')'}"></div>
                <span>Xuất file XML</span>
            </button>
            <button mat-menu-item class="menuAction" *ngIf="digitalSignatureEnable; else signDisabled" fxLayout="row" [matMenuTriggerFor]="morePdfOption">
                <div class="file_icon" [ngStyle]="{'background-image': 'url('+ this.config.cloudStaticURL + 'icon/files/300x300/' + 'pdf' + '.png' +')'}"></div>
                <span >Xuất file pdf và ký số</span>
            </button>
            <ng-template #signDisabled>
                <button mat-menu-item class="menuAction" (click)="changeFile('pdf')" fxLayout="row">
                    <div class="file_icon" [ngStyle]="{'background-image': 'url('+ this.config.cloudStaticURL + 'icon/files/300x300/' + 'pdf' + '.png' +')'}"></div>
                    <span >Xuất file pdf</span>
                </button>
            </ng-template>
            <mat-menu #morePdfOption="matMenu">
                <button mat-menu-item class="menuAction" *ngIf="digitalSignature?.VNPTSim" (click)="openPdfDigitalSignature(1)">
                    <mat-icon>verified</mat-icon>
                    <span>Ký số sim</span>
                </button>
                <button mat-menu-item class="menuAction" *ngIf="digitalSignature?.SmartCA" (click)="openPdfDigitalSignature()">
                    <mat-icon>verified</mat-icon>
                    <span>Ký số Smart CA</span>
                </button>
                <button mat-menu-item class="menuAction" *ngIf="digitalSignature?.VGCA" (click)="openVGCAPlugin()">
                    <mat-icon>verified</mat-icon>
                    <span *ngIf="!isEditSignTokenName; then thenBlock else elseBlock"></span>
                    <ng-template #thenBlock>Ký số ban cơ yếu</ng-template>
                    <ng-template #elseBlock>Ký số Token</ng-template> 
                </button>
                <button mat-menu-item class="menuAction" (click)="openVGCApluginCopy()" *ngIf="!!isVgcaSignCopy">
                    <mat-icon>verified</mat-icon>
                    <span>{{vgcaSignLabel}}</span>
                </button>
                <button mat-menu-item class="menuAction" *ngIf="digitalSignature?.VNPTCA" (click)="openVnptCaPlugin()">
                    <mat-icon>verified</mat-icon>
                    <span>Ký số VNPT-CA</span>
                </button>
                <button mat-menu-item class="menuAction" (click)="openNEAC()" *ngIf="digitalSignature?.NEAC">
                    <mat-icon class="mainColor">verified</mat-icon>
                    <span i18n="@@digitalSignNEAC">Ký số NEAC</span>
                  </button>
            </mat-menu>
        </mat-menu>
    </div>
    <div class="thoaiisolate" style="padding-top: 2em;">
        <form >
            <formio #formEFormComp [form]="applicantEForm.component" [submission]="applicantEForm.data" [renderOptions]="applicantEForm.renderOptions" [readOnly]="isLoading" [viewOnly]="isLoading" *ngIf="applicantEForm.id != undefined && applicantEForm.id != ''"
                (change)="onChangeForm($event)"></formio>
        </form>
    </div>
</div>