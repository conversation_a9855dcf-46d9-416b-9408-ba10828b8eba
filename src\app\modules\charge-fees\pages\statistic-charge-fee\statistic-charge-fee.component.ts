import { DatePipe } from "@angular/common";
import {
  ChangeDetector<PERSON><PERSON>,
  Component,
  OnInit,
  ViewChild,
  On<PERSON>est<PERSON>,
} from "@angular/core";
import { EnvService } from "src/app/core/service/env.service";
import { DeploymentService } from "src/app/data/service/deployment.service";
import { ExportExcelService } from "src/app/data/service/export-excel/export-excel.service";
import { SnackbarService } from "src/app/data/service/snackbar/snackbar.service";
import * as tUtils from "src/app/data/service/thoai.service";
import { MainService } from "data/service/main/main.service";
import { ProcedureService } from "data/service/procedure/procedure.service";
import { SectorService } from "data/service/sector/sector.service";
import { ActivatedRoute, Router } from "@angular/router";
import { LateService } from "data/service/dossier/late.service";
import { PeriodicReportsService } from "data/service/periodic-reports/periodic-reports.service";
import { BasedataService } from "src/app/data/service/basedata/basedata.service";
import { ReplaySubject, Subject } from "rxjs";
import { MatSelect } from "@angular/material/select";
import { MatOption } from "@angular/material/core";
import { PadmanService } from "src/app/data/service/svc-padman/padman.service";
import { MatDialog } from "@angular/material/dialog";
import { UserService } from "data/service/user.service";
import { ProcedureReportService } from "src/app/data/service/procedure-report/procedure-report.service";
import { QNIStatisticService } from "src/app/data/service/qni-statistics/qni-statistic.service";
import { StatisticsService } from "src/app/data/service/statistics/statistics.service";
import { FormControl, FormGroup } from "@angular/forms";
import { MatTableDataSource } from "@angular/material/table";
import { NgxPrinterService } from "ngx-printer";
import {
  animate,
  state,
  style,
  transition,
  trigger,
} from "@angular/animations";
import { DossierService } from "src/app/data/service/dossier/dossier.service";
import { BasepadService } from "src/app/data/service/basepad/basepad.service";
import { takeUntil, take, filter, debounceTime, tap} from 'rxjs/operators';
import {MatRadioModule} from '@angular/material/radio';
import { MatTabChangeEvent } from '@angular/material/tabs';


@Component({
  selector: 'app-statistic-charge-fee',
  templateUrl: './statistic-charge-fee.component.html',
  styleUrls: [
    './statistic-charge-fee.component.scss',
    "/src/app/app.component.scss",
    "/src/app/shared/scss/form-field-outline.scss"
  ],
  animations: [
    trigger("detailExpand", [
      state("collapsed", style({ height: "0px", minHeight: "0px" })),
      state("expanded", style({ height: "*" })),
      transition(
        "expanded <=> collapsed",
        animate("225ms cubic-bezier(0.4, 0.0, 0.2, 1)")
      ),
    ]),
  ],
})

export class StatisticChargeFeeComponent implements OnInit {
  paginationType = this.deploymentService.env.statistics.paginationType;

  dataSource: MatTableDataSource<any>;
  dataSource1: MatTableDataSource<any>;
  dataSource2: MatTableDataSource<any>;

  ELEMENTDATA: any[] = [];
  ELEMENTDATA1: any[] = [];
  ELEMENTDATA2: any[] = [];
  excelData: any = [];
  
  displayedColumns_1: string[] = ["stt","createdDate","payment","type","countA","countB","actualTransaction","sumA","sumB","value"];
  displayedColumns_2: string[] = ["stt1","agencyName","count","sum","note"];
  displayedColumns_3: string[] = ["stt2","dossierCode","payment","type","code","createdDate","time","accountingCode","documentNumber","sum","status"];

  countSynthesis = 0;
  sumSynthesis = 0;

  countSelect = 0;
  sumSelect = 0;

  columns: string[] = [];
  searchForm = new FormGroup({
    fromDateCtrl: new FormControl(""),
    toDateCtrl: new FormControl(""),
    agency: new FormControl(""),
    agencyCtrl: new FormControl(""),
    sector: new FormControl(""),
    sectorCtrl: new FormControl(""),
    code: new FormControl(""),
    reportType: new FormControl(""),
  });
  public searchSubAgencyCtrl: FormControl = new FormControl();
  public searchSectorCtrl: FormControl = new FormControl();
  public searchprocedureCtrl: FormControl = new FormControl();
  public searchSubAgencyAdvCtrl: FormControl = new FormControl();
  env = this.deploymentService.getAppDeployment()?.env;
  config = this.envService.getConfig();
  globalSearchString = "";
  footerData: any[][] = [];
  isAdmin = false;
  userAgency?: any = JSON.parse(localStorage.getItem("userAgency"));
  searching: boolean = false;
  selectedLang = localStorage.getItem("language") || "vi";
  nowDate = tUtils.newDate();
  toDate;
  fromDate;
  allSelectedSubAgency = false;
  allSelectedAgency = false;

  agencyCtrl: FormControl = new FormControl();
  reportType: FormControl = new FormControl();
  searchAgencyCtrl: FormControl = new FormControl();
  agencyId = "";
  agencyArr: Array<any> = [];
  listSubAgencyFilteredSearch: Array<any> = [];
  listAgencyFilteredSearch: Array<any> = [];

  @ViewChild("agencyMatSelectInfinite", { static: true })
  agencyMatSelect: MatSelect;
  @ViewChild("subAgencyMatSelectInfinite", { static: true })
  subAgencyMatSelect: MatSelect;

  @ViewChild('tabGroup') tabGroup;

  protected onDestroy = new Subject<void>();

  waitingDownloadExcel = false;
  waitingSearch = false;
  listAgencyLevelNotBSA: Array<any> =
    this.deploymentService?.env?.statistics
      ?.agencyLevelNotBelongStatisticalAgency;
  listAgencyLevel: Array<any> =
    this.deploymentService?.env?.statistics?.agencyLevel;
  showSearchAgency = true;
  showSelectAllAgency = true;
  selectedLangId = Number(localStorage.getItem("languageId")) || 228;

  savedItems: any = [];

  @ViewChild("levelReportMatSelectInfinite", { static: true })
  levelReportMatSelect: MatSelect;
  @ViewChild("subAgencyAdvMatSelectInfinite", { static: true })
  subAgencyAdvMatSelect: MatSelect;
  allSelectedSubAgencyAdv = false;
  allSelectedlevelReport = false;
  xpandStatus: boolean = false;
  listSubAgencyAdvFilteredSearch: any = [];
  listSubAgencyAdv: any = [];
  showSelectAllLevelReport: boolean = true;
  isPermissionDisplayAdvanced: boolean = false;

  size = 10;
  pageIndex = 0;
  page = 0;
  countResult = 0;
  pgSizeOptions = this.config.pageSizeOptions;

  //sector
  isFullListSector = false;
  listSector = [];
  listSectorPage = 0;
  searchSectorKeyword = '';
  timeOut = null;
  msTimeout = 400;
  protected sectors: any[] = this.listSector;
  public sectorFiltered: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);

  //change tab
  isTab1 = false;
  isTab2 = false;
  isTab3 = false;

  //agency
  agencyPage = 0;
  isAgencyListFull = false;
  agencyList = [];
  ancestorId = this.deploymentService?.env?.ancestorId ? this.deploymentService?.env?.ancestorId : '';
  timeOutSearch = null;

  //sector
  sectorPage = 0;
  isSectorListFull = false;
  sectorList = [];

  //exceldata
  excelDataSyn = [];
  sumAll = 0;

  constructor(
    private envService: EnvService,
    private procedureService: ProcedureService,
    public datePipe: DatePipe,
    private dossierService: DossierService,
    private snackbarService: SnackbarService,
    private printerService: NgxPrinterService,
    private deploymentService: DeploymentService,
    private dialog: MatDialog,
    private userService: UserService,
    private exportExcelService: ExportExcelService,
    private basepadService: BasepadService,
    private basedataService: BasedataService,
    private statisticsService: StatisticsService,
    private sectorService: SectorService

  ) {
    this.fromDate = new Date(
      this.datePipe.transform(
        this.nowDate.getFullYear() +
          "-" +
          (this.nowDate.getMonth() + 1 <= 9
            ? "0" + (this.nowDate.getMonth() + 1)
            : this.nowDate.getMonth() + 1) +
          "-01",
        "yyyy-MM-dd"
      )
    );
    this.toDate = this.nowDate; 

    this.dataSource = new MatTableDataSource(this.ELEMENTDATA);
    this.dataSource1 = new MatTableDataSource(this.ELEMENTDATA1);
    this.dataSource2 = new MatTableDataSource(this.ELEMENTDATA2);
  }

  ngOnInit(): void {
    this.searching = false;
    this.userAgency = JSON.parse(localStorage.getItem("userAgency"));
    this.getAgencyList(true);
    this.getSectorList(true);
  }

  changeMenu(tabNumber){
    switch (tabNumber){
      case 1:
        this.isTab1 = true;
        this.isTab2 = false;
        this.isTab3 = false;
        break;
      case 2:
        this.isTab1 = false;
        this.isTab2 = true;
        this.isTab3 = false;
        break;
      case 3:
        this.isTab1 = false;
        this.isTab2 = false;
        this.isTab3 = true;
    }
  }

  checkDateValid(startDate, endDate) {
    return new Promise((resolve) => {
      try {
        const _endDate = new Date(endDate);
        _endDate.setHours(23, 59, 59);
        if (startDate == null || startDate == "" || startDate == undefined) {
          const msgObj = {
            vi: "Vui lòng chọn thời gian bắt đầu!",
            en: "Please select a start time!",
          };
          this.searching = false;
          this.snackbarService.openSnackBar(0,"",msgObj[this.selectedLang],"error_notification",this.config.expiredTime);
          resolve(false);
        } else {
          if (endDate == null || endDate == "" || endDate == undefined) {
            const msgObj = {
              vi: "Vui lòng chọn thời gian kết thúc!",
              en: "Please select a end time!",
            };
            this.searching = false;
            this.snackbarService.openSnackBar(0,"",msgObj[this.selectedLang],"error_notification",this.config.expiredTime);
            resolve(false);
          } else {
            if (new Date(startDate) > _endDate) {
              const msgObj = {
                vi: "Ngày bắt đầu phải nhỏ hơn ngày kết thúc!",
                en: "Start date must be lesser than end date!",
              };
              this.searching = false;
              this.snackbarService.openSnackBar(0,"",msgObj[this.selectedLang],"error_notification",this.config.expiredTime);
              resolve(false);
            } else {
              resolve(true);
            }
          }
        }
      } catch (error) {
        resolve(true);
      }
    });
  }

  createSearchString(formObj){
    let fromDate = this.fromDate + "T00:00:00.000Z";
    let toDate = this.toDate + "T23:59:59.000Z";
    let searchString ="?from-date=" + fromDate + "&to-date=" + toDate;
    if (!!formObj.sector){
      searchString += "&sector-id=" + formObj.sector;
    }

    if (!!formObj.agency){
      searchString += "&agency-id=" + formObj.agency;
    }

    if(!!formObj.code){
      searchString += "&keyword=" + formObj.code.trim();
    }
    return searchString;
  }

  async getSynthesisReport(formObj){
    try {
      await this.pushDataSynthesis(formObj);
    } catch (e) {
        console.error("An error occurred:", e);
        this.waitingSearch = false;
    }
  }

  async pushDataSynthesis(formObj){
    let searchString1 = this.createSearchString(formObj);
    this.ELEMENTDATA = [];
    this.dossierService.getSynthesisReportFee(searchString1).toPromise().then(result2 => {
      if (Array.isArray(result2)){
        result2.forEach((item, index) => { // corrected syntax for forEach loop
          const obj = {
            stt: index + 1,
            createdDate: this.datePipe.transform(new Date(item?.submitDate), "dd-MM-yyyy"),
            payment: item?.paymentMethod ? "Chuyển khoản" : "Tiền mặt",
            type: '',
            countA: item?.count,
            countB: 0,
            actualTransaction: '',
            sumA: item?.sum,
            sumB: 0,
            value: ''
          };
          this.ELEMENTDATA.push(obj);
        });
      }
      this.dataSource.data = this.ELEMENTDATA;
    }).catch(error => {
      console.error("KL ", error);
    });
  }

  async getCollectReceipt(formObj){
    try {
      await this.pushDataCollect(formObj);
    } catch (e) {
        console.error("An error occurred:", e);
        this.waitingSearch = false;
    }
  }

  async pushDataCollect(formObj){
    let searchString = this.createSearchString(formObj);
    this.ELEMENTDATA1 = [];
    try{
      this.dossierService.getCollectReceipt(searchString).toPromise().then(result => {
        if (Array.isArray(result) && result.length > 0) {
          let i = 1;
          result.forEach(item => {
            const obj = {
              stt1: i,
              agencyName: item.agency.name[0].name,
              count: item?.count ? item.count : 0,
              sum: item?.sum ? item.sum : 0,
              note: item?.note ? item.note : ''
            }
            this.ELEMENTDATA1.push(obj);
            i++;
          });
        }else {
          console.error("Dữ liệu trả về không phải là một mảng!");
        }
      });
      this.dataSource1.data = this.ELEMENTDATA1;
    } catch (e) {
      console.error("Lỗi pushDataCollect:", e);
    }
  }

  async getDetailedReport(formObj, page, size){
    try {
      await this.pushDataDetail(formObj, page, size);
    } catch (e) {
        console.error("An error occurred:", e);
        this.waitingSearch = false;
    }
  }

  async pushDataDetail(formObj, page, size){
    let searchString = this.createSearchString(formObj) + "&page="+ page + "&size=" + size;
    this.ELEMENTDATA2 = [];
    try{
      this.dossierService.getDetailedReport(searchString).toPromise().then((result: any) => {
        if (!!result && result.content) {
          this.countResult = result.totalElements;
          let i = 1;
          result.content.forEach((item: any) => {
            const obj = {
                    stt2: i,
                    procedureCode: item.dossier.procedure.code,
                    procedureName: item.dossier.procedure.translate[0].name,
                    dossierCode: item.dossier.code,
                    payment: item?.paymentMethod ? "Chuyển khoản" : "Tiền mặt",
                    type: '',
                    applicantName: item?.dossier?.applicant?.fullname ? item.dossier.applicant.fullname : '',
                    agency: '',
                    stk: '',
                    code: '',
                    submitDate: this.datePipe.transform(item?.submitDate, "dd-MM-yyyy"),
                    createdDate: this.datePipe.transform(item?.createdDate, "dd-MM-yyyy"),
                    time: this.datePipe.transform(item?.createdDate, "HH:mm:ss"),
                    accountingCode: '',
                    documentNumber: '',
                    sum: item?.total,
                    status: 'Đã thu',
                    sectorCode: item.dossier.procedure.sector.code,
                    sectorName: item.dossier.procedure.sector.name[0].name
            };
            i++;
            this.ELEMENTDATA2.push(obj);
          });
        }
        this.dataSource2.data = this.ELEMENTDATA2;
      });    
    } catch (e) {
      console.error("Lỗi pushDataDetail:", e);
    }
  }

  async onConfirmSearch() {
    this.searching = false;
    this.waitingSearch = true;
    const formObj = this.searchForm.getRawValue();
    const checkDate = await this.checkDateValid(
      formObj.fromDateCtrl,
      formObj.toDateCtrl
    );
    const checkSelectReport = this.checkSelectRadioButton(formObj.reportType);
    if (checkDate && checkSelectReport) {
      this.fromDate = this.datePipe.transform(formObj.fromDateCtrl,"yyyy-MM-dd");
      this.toDate = this.datePipe.transform(formObj.toDateCtrl, "yyyy-MM-dd");
      this.page = 0;
      if (this.isTab1) {
        this.getSynthesisReport(formObj);
      } else if (this.isTab2) {
        this.getCollectReceipt(formObj);
      } else if (this.isTab3) {
        this.getDataExportExcel(formObj);
        this.getDetailedReport(formObj, this.page, this.size);
      }
    }
    this.waitingSearch = false;
  }

  checkSelectRadioButton(reportType){
    return new Promise((resolve) => {
      try {
        if (!!reportType) {
          resolve(true);
        } else {
          const msgObj = {
            vi: "Vui lòng chọn loại báo cáo!",
            en: "Please select a report type!",
          };
          this.searching = false;
          this.snackbarService.openSnackBar(0,"",msgObj[this.selectedLang],"error_notification",this.config.expiredTime);
          resolve(false);
        }
      } catch (error) {
        resolve(true);
      }
    });
  }

  protected filterSector() {
    if (!this.sectors) {
      return;
    }
    let search = this.searchSectorCtrl.value.trim();
    this.searchSectorKeyword = search;
    if (!search) {
      this.sectorFiltered.next(this.sectors.slice());
      return;
    } else {
      search = search.toLowerCase();
    }
    if (this.sectors.filter(sec => sec.name.toLowerCase().indexOf(search) > -1).length > 0) {
      this.sectorFiltered.next(
        this.sectors.filter(sec => sec.name.toLowerCase().indexOf(search) > -1)
      );
    } else {
      this.listSectorPage = 0;
      let searchString = '';
      let checkAll = 0;
      if (this.searchForm.getRawValue().agencyCtrl.id != null) {
        checkAll = 1;
        searchString += '?agency-id=' + this.searchForm.getRawValue().agencyCtrl.id + '&only-agency-id=1&page=' + this.listSectorPage + '&sort=name,asc&status=1&size=50';
      }
      if (checkAll === 0) {
        searchString += '?page=' + this.listSectorPage + '&status=1&sort=name,asc&size=50';
      }
      if (this.searchSectorKeyword !== null && this.searchSectorKeyword !== '') {
        searchString += '&keyword=' + this.searchSectorKeyword;
      }
      this.basepadService.getListSector(searchString).subscribe(data => {
        for (let i = 0; i < data.numberOfElements; i++) {
          this.listSector.push(data.content[i]);
        }
        this.sectors = JSON.parse(JSON.stringify(this.listSector).replace(/null/g, '""'));
        this.sectorFiltered.next(
          data.content
        );
      }, err => {
        console.log(err);
      });
    }
  }

  getListSector() {
    if (this.isFullListSector) {
      return;
    } else {
      let searchString = '';
      let checkAll = 0;
      if (this.searchForm.getRawValue().agencyCtrl.id != null) {
        checkAll = 1;
        searchString += '?agency-id=' + this.searchForm.getRawValue().agencyCtrl.id + '&only-agency-id=1&page=' + this.listSectorPage + '&sort=name,asc&status=1&size=50';
      }

      if (this.searchSectorKeyword !== null && this.searchSectorKeyword !== '') {
        searchString += '&keyword=' + this.searchSectorKeyword;
      }

      this.basepadService.getListSector(searchString).subscribe(data => {
        this.isFullListSector = data.last;
        this.listSectorPage++;
        for (let i = 0; i < data.numberOfElements; i++) {
          this.listSector.push(data.content[i]);
        }
        this.sectors = JSON.parse(JSON.stringify(this.listSector).replace(/null/g, '""'));
        this.sectorFiltered.next(this.sectors);
        this.searchSectorCtrl.valueChanges.pipe(takeUntil(this.onDestroy)).subscribe(() => {
          clearTimeout(this.timeOut);
          this.timeOut = setTimeout(() => {
            this.filterSector();
          }, this.msTimeout);
        });
      }, err => {
        console.log(err);
      });
    }
  }

  getDataExportExcel(formObj) {
    this.excelDataSyn = [];
    this.sumAll = 0;
    let searchString = this.createSearchString(formObj) + "&keyword=" + formObj.code.trim();
    try{
      this.dossierService.getDetailedReportExcel(searchString).subscribe(result => {
        if (Array.isArray(result) && result.length > 0 && result.length > 0) {
          let i = 1;
          result.forEach((item: any, index) => {
            this.sumAll += item.total;
            const obj = {
                    stt2: i,
                    procedureCode: item?.dossier?.procedure?.code ? item.dossier.procedure.code : '',
                    procedureName: item?.dossier?.procedure?.translate[0]?.name ? item.dossier.procedure.translate[0].name : '',
                    dossierCode: item?.dossier?.code ? item.dossier.code : '',
                    payment: "",
                    type: item?.paymentMethod ? "Chuyển khoản" : "Tiền mặt",
                    applicantName: item?.dossier?.applicant?.fullname ? item.dossier.applicant.fullname : '',
                    agency: item?.beneficiaryAccount?.agency?.name[0]?.name ? item.beneficiaryAccount.agency.name[0].name : '',
                    stk: item?.beneficiaryAccount?.beneficiaryAccount ? item.beneficiaryAccount.beneficiaryAccount : "",
                    acceptedDate: this.datePipe.transform(item.dossier.acceptedDate, "dd-MM-yyyy"),
                    createdDate: this.datePipe.transform(item.submitDate, "dd-MM-yyyy"),
                    time: this.datePipe.transform(item.createdDate, "HH:mm:ss"),
                    sum: item?.total ? item.total : 0,
                    status: 'Đã thu',
                    sectorCode: item?.dossier?.procedure?.sector?.code ? item.dossier.procedure.sector.code : '',
                    sectorName: item?.dossier?.procedure?.sector?.name[0]?.name ? item.dossier.procedure.sector.name[0].name : ''
            };
            this.excelDataSyn.push(obj);
            i++;
          });
        }
      });
    } catch (e) {
      console.error("Lỗi pushDataDetail:", e);
    }
  }
 
  exportExcelSynthesisReport(){
    const formObj = this.searchForm.getRawValue();
    let fdate = this.datePipe.transform(formObj.fromDateCtrl,"dd-MM-yyyy");
    let tdate = this.datePipe.transform(formObj.toDateCtrl,"dd-MM-yyyy");
    const filename = "BaoCaoTongHop";
    let agencyName = "Tất cả";
    if(!!formObj.agency){
      agencyName = this.agencyList.filter(x => x.id == formObj.agency)[0].name;
    }
    let fromDateToDate = "(Từ ngày " +fdate+ " đến ngày " +tdate+")";
    let countcount = this.sum('countA', this.ELEMENTDATA);
    let sumsum = this.sum('sumA', this.ELEMENTDATA);
    let excelData = this.ELEMENTDATA;
    const obj = {
      stt: "",
      createdDate: '###',
      payment: 'Tổng cộng:',
      type: '',
      countA: countcount,
      countB: 0,
      actualTransaction: '',
      sumA: sumsum,
      sumB: 0,
      value: ''
    }
    excelData.push(obj);
    this.exportExcelService.exportExcelSynthesisReport(excelData, filename, agencyName, fromDateToDate, countcount, sumsum);
  }

  exportExcelCollectRecepit(){
    const formObj = this.searchForm.getRawValue();
    const fdate = this.datePipe.transform(formObj.fromDateCtrl,"dd-MM-yyyy");
    const tdate = this.datePipe.transform(formObj.toDateCtrl,"dd-MM-yyyy");
    const header = ['STT','Cơ quan', 'Tổng số giao dịch', 'Tổng số tiền', 'Ghi chú'];
    const fromDateToDate = "(Từ ngày " +fdate+ " đến ngày " +tdate+")";
    const filename = "TongHopPhieuThu";
    let excelData = this.ELEMENTDATA1;
    const obj = {
      stt1: '',
      agencyName: 'Tổng cộng:',
      count: this.sum('count', this.ELEMENTDATA1),
      sum: this.sum('sum', this.ELEMENTDATA1),
      note: ''
    }
    excelData.push(obj);
    this.exportExcelService.exportExcelCollectRecepit(excelData, filename, fromDateToDate, header);
  }

  exportExcelDetailedReport(){
    const formObj = this.searchForm.getRawValue();
    const fdate = this.datePipe.transform(formObj.fromDateCtrl,"dd-MM-yyyy");
    const tdate = this.datePipe.transform(formObj.toDateCtrl,"dd-MM-yyyy");
    const header = ['STT','Mã thủ tục', 'Tên thủ tục', 'Mã khách hàng', 'Kênh giao dịch', 'Loại giao dịch', 'Tên khách', 'Cơ quan thu', 'Số tài khoản', 'Ngày tiếp nhận', 'Ngày thu tiền', 'Giờ thu tiền', 'Số tiền giao', 'Tình trạng giao dịch', 'Mã lĩnh vực thủ tục', 'Tên lĩnh vực thủ tục'];
    const fromDateToDate = "(Từ ngày " +fdate+ " đến ngày " +tdate+")";
    let agencyName = "Tất cả";
    if(!!formObj.agency){
      agencyName = this.agencyList.filter(x => x.id == formObj.agency)[0].name;
    }
    const filename = "BaoCaoChiTiet";
    const lastObj = {
      stt2: '',
      procedureCode: '',
      procedureName: '',
      dossierCode: 'Tổng cộng:',
      payment: '',
      type: '',
      applicantName: '',
      agency: '',
      stk: '',
      acceptedDate: '',
      createdDate: '',
      time: '',
      sum: this.sumAll,
      status: '',
      sectorCode: '',
      sectorName: ''
    }
    this.excelDataSyn.push(lastObj);
    this.exportExcelService.exportExcelDetailedReport(this.excelDataSyn,filename,fromDateToDate,header,agencyName);
  }

  async exportToExcel() {
    if (this.isTab1){
      this.exportExcelSynthesisReport();
    }else if(this.isTab2){
      this.exportExcelCollectRecepit();
    }else if(this.isTab3){
      this.exportExcelDetailedReport();
    }
  }

  paginate(event: any, type) {
    const formObj = this.searchForm.getRawValue();
    switch (type) {
      case 0:
        this.pageIndex = event;
        this.page = event;
        this.getDetailedReport(formObj, this.pageIndex-1, this.size);
        break;
      case 1:
        this.pageIndex = 1;
        this.page = 1;
        this.getDetailedReport(formObj, this.pageIndex-1, this.size);
        break;
    }
  }

  formatNumber(number: number) {
    return (number == null || number == undefined) ? '' : number.toString().replace(/(.)(?=(\d{3})+$)/g, '$1.');
  }

  sum(key: keyof any, data) {
    //return this.formatNumber(data.reduce((a, b) => a + (Number(b[key]) || 0), 0));
    return data.reduce((a, b) => a + (Number(b[key]) || 0), 0);
  }

  getAgencyList(scroll: boolean) {
    if (!scroll) {
      this.agencyPage = 0;
      this.isAgencyListFull = false;
      this.agencyList = [];
    }

    const formObj = this.searchForm.getRawValue();
    const searchString = '?page=' + this.agencyPage + '&size=10&status=1&spec=slice'
        + '&ancestor-id=' + this.ancestorId + '&keyword=' + encodeURIComponent(formObj.agencyCtrl.trim()) + '&sort=desc,_id';
    this.basedataService.getAgencyNameCode(searchString).subscribe(data => {
      this.agencyList = [...this.agencyList, ...data.content];
      this.agencyList = Object.values(this.agencyList.reduce((acc, cur) => Object.assign(acc, {[cur.id] : cur}), {}));
      this.agencyPage++;
      this.isAgencyListFull = data.last;
    }, err => {
      console.log(err);
    });
  }

  searchAgencyList() {
    clearTimeout(this.timeOutSearch);
    this.timeOutSearch = setTimeout(() => {
      this.getAgencyList(false);
    }, 400);
  }

  clearAgencyList() {
    this.searchForm.patchValue({
      agencyCtrl: ''
    });
    this.getAgencyList(false);
  }

  keyupSearch(key: string) {
    clearTimeout(this.timeOutSearch);
    if (key === 'sector') {
      this.timeOutSearch = setTimeout(() => {
        this.getSectorList(false);
      }, 400);
    } else if (key === 'agency') {
      this.timeOutSearch = setTimeout(() => {
        this.getAgencyList(false);
      }, 400);
    }
  }

  getSectorList(scroll: boolean) {
    if (!scroll) {
      this.sectorPage = 0;
      this.isSectorListFull = false;
      this.sectorList = [];
    }

    const formObj = this.searchForm.getRawValue();
    let searchString = '?page=' + this.sectorPage + '&size=10&status=1&spec=slice'
      + '&keyword=' + encodeURIComponent(formObj.sectorCtrl.trim());
      
    if (!!formObj.agency){
        searchString += "&agency-id=" + formObj.agency;
    }
    this.sectorService.getListSector(searchString).subscribe(data => {
      this.sectorList = [...this.sectorList, ...data.content];
      this.sectorList = Object.values(this.sectorList.reduce((acc, cur) => Object.assign(acc, { [cur.id]: cur }), {}));
      this.sectorPage++;
      this.isSectorListFull = data.last;
    }, err => {
      console.log(err);
    });
  }

}
