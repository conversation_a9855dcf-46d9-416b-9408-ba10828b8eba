import { Component, Inject, OnInit } from '@angular/core';
import { MatDialog, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { MatTableDataSource } from '@angular/material/table';
import { Router } from '@angular/router';
import { KeycloakService } from 'keycloak-angular';
import { Subscription } from 'rxjs';
import { EnvService } from 'src/app/core/service/env.service';
import { DeploymentService } from 'src/app/data/service/deployment.service';
import { DossierService } from 'src/app/data/service/dossier/dossier.service';
import { SnackbarService } from 'src/app/data/service/snackbar/snackbar.service';
import { UserService } from 'src/app/data/service/user.service';
import {Workbook} from "exceljs";
import moment from "moment/moment";
import * as fs from 'file-saver';
import {
  ProcessHandleComponent,
  ProcessHandleDialogModel
} from "shared/components/process-handle/process-handle.component";
import { QBHStatisticService } from 'src/app/data/service/qbh-statistics/qbh-statistic.service';
import { KhaRemindWorkService } from 'src/app/data/service/kha-remind-work/kha-remind-work.service';

@Component({
  selector: 'app-list-dossier-remind',
  templateUrl: './list-dossier-remind-dau-ky.component.html',
  styleUrls: ['./list-dossier-remind-dau-ky.component.scss']
})
export class ListDossierRemindDauKyComponent implements OnInit {

  constructor(
    public dialogRef: MatDialogRef<ListDossierRemindDauKyComponent>,
    @Inject(MAT_DIALOG_DATA) public data: ConfirmDossierRemindDialogModel,
    private deploymentService: DeploymentService,
    private keycloakService: KeycloakService,
    private userService: UserService,
    private envService: EnvService,
    private khaRemindWorkService: KhaRemindWorkService,
    private router: Router,
    private dialog: MatDialog,
    private dossierService: DossierService,
    private snackbarService: SnackbarService,
    private qbhStatisticsService: QBHStatisticService,
  ) {
    this.type =data.type;
    this.dataSource = new MatTableDataSource(this.ELEMENTDATA);
   }
  lblTitle: string = "quá hạn xử lý";
  type :number =0;
  userId:any;
  userInfo: any;
  agencyId: any;
  config = this.envService.getConfig();
  size = 10;
  pageIndex = 1;
  page = 1;
  countResult = 0;
  pgSizeOptions = this.config.pageSizeOptions;
  selectedLang: string;
  dataFirst: any = [];
  listForm = [];
  timeDueSoon:number =1;
  useDueDateRemindWork : boolean =false;
  isDue: number = 0;
  ELEMENTDATA: any[] = [];
  dataSource: MatTableDataSource<any>;
  env = this.deploymentService.getAppDeployment()?.env;
  displayedColumns: string[] = ['stt', 'code', 'procedure', 'sector', 'required' , 'owner','date'];
  searchDomain:String ="";
  userAgency: any;
  listUserAgency: any;
  userExperience: any;
  accepterInfo = {
    id: '',
    username: '',
    fullname: '',
    accountId: ''
  };
  subscription: Subscription;
  approvalAgencyList = [];
  agencyInfo = [];
  agencyTagName:string= "Cấp Tỉnh,Cấp Sở,Cấp Huyện,Cấp Xã,Cấp Xã/Phường/Thị trấn";
  labelDate : string = "Ngày nộp hồ sơ";
  timeDueByStep = 0;
  modifyTime : boolean =false;

  userAgencyLevel: any = JSON.parse(localStorage.getItem('levelAgency'));
  agencyCapSo: any = this.deploymentService.env.OS_QTI.qtiCapSo ? this.deploymentService.env.OS_QTI.qtiCapSo : '5ff6b1a706d0e31c6bf13e09';

  async ngOnInit(): Promise<void> {
    this.selectedLang = localStorage.getItem('language');
    await this.getUserAccount();
    let searchString ='?page=' + (this.pageIndex - 1) + '&size=' + this.size + '&spec=page';
    // if (!!this.userId) {
    //   searchString += `&userId=${this.userId}`;
    // }
    // if (!!this.userAgency?.parent) {
    //   searchString += `&parentId=${this.userAgency?.parent?.id}`;
    // }
    this.agencyId = await this.getUserAgencyId();
    var rootId = await this.findRootAgency(this.agencyId);
    if(rootId === '655dc9dececceb05ee44499f'){
      this.agencyId = '60b87fb59adb921904a0213e'; //UBND tỉnh Quảng Tri có 2 agency.id
    } else {
      this.agencyId = rootId;
    }
    this.getListDossier(this.type,searchString);
  }
  getUserAccount() {
    this.keycloakService.loadUserProfile().then(user => {
      // tslint:disable-next-line: no-string-literal
      this.userId = user['attributes'].user_id[0];
      console.log('userId',this.userId )
      this.userService.getUserInfo(this.userId).subscribe(async data => {
        this.userInfo = data;
        console.log('userInfo',this.userInfo )
      }, error => {
        console.log(error);
      });
    });
  }
  getUserAgencyId(): string {
    const userAgency = JSON.parse(localStorage.getItem('userAgency'));
    if (userAgency !== null) {
      return userAgency?.id;
    } else {
      return this.config?.rootAgency?.id;
    }
  }
  onDismiss(): void {
    this.dialogRef.close();
  }
  paginate(event: any, type) {
    switch (type) {
      case 0:
        this.pageIndex = event;
        this.page = event;
        this.getListDossier(this.type,'?page=' + (this.pageIndex - 1) + '&size=' + this.size + '&spec=page');
        break;
      case 1:
        this.pageIndex = 1;
        this.page = 1;
        this.getListDossier(this.type,'?page=' + (this.pageIndex - 1) + '&size=' + this.size + '&spec=page');
        break;
    }
  }
  getListDossier(type: number,searchString: string){
    // if (this.userAgencyLevel.id !== this.agencyCapSo) {
    //   const userAgency = JSON.parse(localStorage.getItem('userAgency'));
    //   searchString += `&agcencyId=${userAgency.id}`;
    // }
    // if (this.agencyId) {
    //   searchString += `&agcencyId=${this.agencyId}`;
    // }
    // if (!!this.userAgency?.parent) {
    //   searchString += `&parentId=${this.userAgency?.parent?.id}`;
    // }
    if (!!this.userId) {
      searchString += `&userId=${this.userId}`;
    }
    searchString += '&sort=appointmentDate,asc';
    this.khaRemindWorkService.getListFormTerm(searchString).subscribe(data => {
      this.ELEMENTDATA = [];
      this.countResult = data.totalElements;
      for (let i = 0; i < data.numberOfElements; i++) {
        data.content[i].stt = this.size * (this.pageIndex - 1) + (i + 1);
        this.ELEMENTDATA.push(data.content[i]);
      }
      this.dataSource = new MatTableDataSource(this.ELEMENTDATA);
      console.log('data source', JSON.stringify(this.dataSource.data));
    });
  }

  getConfig(){
    const config = this.deploymentService.getAppDeployment();
    if (!!config) {
        if (config.domain && config.domain.length > 0){
          // tslint:disable-next-line:max-line-length
          const domain = config.domain.filter(listdomain => ((listdomain.domain.toLowerCase().indexOf(window.location.host) > -1) || (window.location.host.toLowerCase().indexOf(listdomain.domain) > -1)));
          if (domain && domain.length > 0 && domain[0].rootAgency){
            this.searchDomain = '&domain-agency-id=' + domain[0].rootAgency.id;
          }
        }
    }
  }
  exportToExcel() {
    const userId = JSON.parse(localStorage.getItem('userAgency'));
    let result;

    // create workbook and worksheet
    const workbook = new Workbook();
    workbook.creator = 'Snippet Coder';
    workbook.lastModifiedBy = 'SnippetCoder';
    workbook.created = new Date();
    workbook.modified = new Date();
    const worksheet = workbook.addWorksheet();
    const fontHeader = {name: 'Times New Roman'};
    worksheet.getColumn('A').font = fontHeader;
    worksheet.getColumn('B').font = fontHeader;
    worksheet.getColumn('C').font = fontHeader;
    worksheet.getColumn('D').font = fontHeader;
    worksheet.getColumn('E').font = fontHeader;
    worksheet.getColumn('F').font = fontHeader;
    worksheet.getColumn('G').font = fontHeader;
    worksheet.getColumn('H').font = fontHeader;
    worksheet.getColumn('I').font = fontHeader;
    worksheet.getColumn('J').font = fontHeader;
    worksheet.getColumn('K').font = fontHeader;
    worksheet.getColumn('L').font = fontHeader;
    worksheet.getColumn('M').font = fontHeader;
    worksheet.getColumn('N').font = fontHeader;
    worksheet.getColumn('O').font = fontHeader;
    worksheet.getColumn('P').font = fontHeader;
    worksheet.getColumn('Q').font = fontHeader;
    worksheet.getColumn('R').font = fontHeader;
    worksheet.getColumn('S').font = fontHeader;
    worksheet.getColumn('T').font = fontHeader;
    worksheet.getColumn('U').font = fontHeader;

    // Add header row
    const today = new Date();
    worksheet.addRow([]);
    worksheet.mergeCells('A1:C1');
    worksheet.mergeCells('A2:C2');
    worksheet.getCell('A1').value = 'Tỉnh Khánh Hòa';
    worksheet.getCell('A2').value = `Đơn vị: ${userId?.name}`;

    worksheet.getCell('A1').alignment = {horizontal: 'center', vertical: 'middle'};
    worksheet.getCell('A1').font = { ...worksheet.getCell('A1').font,...{bold: true, size: 14}}
    worksheet.getCell('A2').alignment = {horizontal: 'center', vertical: 'middle'};
    worksheet.getCell('A2').font = { ...worksheet.getCell('A1').font,...{bold: true, size: 14}}

    // add report name

    worksheet.mergeCells('A4:E4');
    worksheet.mergeCells('A5:E5');

    worksheet.getCell('A5').value = `Ngày: ${moment().format("DD/MM/YYYY")} `;
    worksheet.getCell('A5').alignment = {horizontal: 'center', vertical: 'middle'};

    worksheet.getCell('A4').value = `THỐNG KÊ DANH SÁCH HỒ SƠ ĐẦU KỲ`;
    worksheet.getCell('A4').alignment = {horizontal: 'center', vertical: 'middle'};

    worksheet.getCell('A5').font = { ...worksheet.getCell('D3').font,...{ size: 14}}
    worksheet.getCell('A4').font = { ...worksheet.getCell('D4').font,...{ size: 14}}

    worksheet.getCell('E1').value = `CỘNG HÒA XÃ HỘI CHỦ NGHĨA VIỆT NAM`;
    worksheet.getCell('E1').alignment = {horizontal: 'center', vertical: 'middle'};

    worksheet.getCell('E2').value = `Độc lập - Tự do - Hạnh phúc`;
    worksheet.getCell('E2').alignment = {horizontal: 'center', vertical: 'middle'};

    worksheet.getCell('A7').value = "STT";
    worksheet.getCell('B7').value = "Mã hồ sơ";
    worksheet.getCell('C7').value = "Thủ tục hành chính";
    worksheet.getCell('D7').value = "Tên lĩnh vực";
    worksheet.getCell('E7').value = "Yêu cầu giải quyết";
    worksheet.getCell('F7').value = "Chủ hồ sơ";
    worksheet.getCell('G7').value = "Ngày nộp";

    worksheet.getColumn('A').width = 10;
    worksheet.getColumn('B').width = 30;
    worksheet.getColumn('C').width = 40;
    worksheet.getColumn('D').width = 30;
    worksheet.getColumn('E').width = 40;
    worksheet.getColumn('F').width = 20;
    worksheet.getColumn('G').width = 20;



    const mileRow = 8;
    const cellNumber = 7;

    worksheet.getRow(7).alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
    worksheet.getRow(7).font = {name: 'Times New Roman', size: 12, bold: true};

    for (let i = 1 ; i <= cellNumber ; i++) {
      const cell = worksheet.findCell(7, i );
      if (cell) {
        cell.border  =  { top: { style: 'thin' }, left: { style: 'thin' }, bottom : { style: 'thin' }, right: { style: 'thin' } };
        cell.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: {argb: 'FFFFFFFF'},
        };
      }
      else {
        console.log('không tìm thấy ', cell);
      }
    }

    // đổ data
    let indexArr = 0;

    let sttCount = 1;
    for(let i = mileRow ; i <=  mileRow + this.ELEMENTDATA.length-1 ; i++) {
      worksheet.getRow(i).height = 30;
      if (this.ELEMENTDATA[indexArr]?.initial != null) {
        const cell = worksheet.getCell(i, 1);
        cell.alignment = {horizontal: 'left', vertical: 'middle'};
        cell.font = { bold: true };
        cell.value = this.ELEMENTDATA[indexArr]?.initial ;
      }
      else
      {
        for (let j = 1; j <= cellNumber; j++) {
          const cell = worksheet.getCell(i, j);
          cell.font = {name: 'Arial', size: 10,};
          cell.alignment = {horizontal: 'left', vertical: 'middle'};
          cell.border = {top: {style: 'thin'}, left: {style: 'thin'}, bottom: {style: 'thin'}, right: {style: 'thin'}};
          let value = null;
          switch (j) {
            case 1:
              cell.value = sttCount; // index
              break;
            case 2:
              cell.alignment = {horizontal: 'center', vertical: 'middle'};
              cell.value = this.ELEMENTDATA[indexArr]?.code ;
              break;
            case 3:
              cell.alignment = {horizontal: 'center', vertical: 'middle'};
              let procedureName = '';
              if (this.ELEMENTDATA[indexArr].procedure?.translate?.name != null && this.ELEMENTDATA[indexArr].procedure) {
                procedureName = this.ELEMENTDATA[indexArr].procedure?.translate?.name;
              }
              if (this.ELEMENTDATA[indexArr].procedure?.translate.length > 0) {
                procedureName = this.ELEMENTDATA[indexArr].procedure?.translate[0]?.name;
              }
              cell.value = procedureName;
              break;
            case 4:
              cell.alignment = {horizontal: 'center', vertical: 'middle'};
              cell.value = this.ELEMENTDATA[indexArr]?.procedure?.sector?.name[0]?.name ?? "";
              break;
            case 5:
              cell.alignment = {horizontal: 'center', vertical: 'middle'};
              cell.value = this.ELEMENTDATA[indexArr]?.applicant?.data?.noidungyeucaugiaiquyet  ?? "";
              break;
            case 6:
              cell.alignment = {horizontal: 'center', vertical: 'middle'};
              cell.value = this.ELEMENTDATA[indexArr]?.applicant?.data?.ownerFullname ?? "";
              break;
            case 7:
              cell.alignment = {horizontal: 'center', vertical: 'middle'};
              cell.value = moment(this.ELEMENTDATA[indexArr]?.appliedDate).format("DD/MM/YYYY") ?? "";
              break;
          }
        }
        sttCount += 1;
      }
      indexArr++;
    }

    const EXCEL_TYPE = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8';
    // Save Excel File
    workbook.xlsx.writeBuffer().then((dataBuffer: ArrayBuffer) => {
      const blob = new Blob([dataBuffer], {type: EXCEL_TYPE});
      fs.saveAs(blob, `KHA_thong_ke_ho_so_dau_ky.xlsx`);
    });
  }

  viewProcess(dossierId, dossierCode) {
    console.log("viewProcess",dossierId,dossierCode);
    const dialogData = new ProcessHandleDialogModel(dossierId, dossierCode);
    const dialogRef = this.dialog.open(ProcessHandleComponent, {
      minWidth: '70vw',
      data: dialogData,
      disableClose: true,
      autoFocus: false
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
    });
  }

  dossierDetail(dossierId, procedureId, task) {
    const queryParamsObject = {
      procedure: procedureId,
    };

    if (task !== undefined) {
      Object.assign(queryParamsObject, { task: task[task.length - 1].id });
    }

      const url = this.router.serializeUrl(
        this.router.createUrlTree(['dossier/search/' + dossierId], {
          queryParams: queryParamsObject
        })
      );

      window.open(url, '_blank');
      return;
  }

  async findRootAgency(agencyId) {
    const data = await this.qbhStatisticsService.findRootAgency(agencyId,this.agencyTagName);
    return data?.id ?? null;
  }
}

export class ConfirmDossierRemindDialogModel {
  constructor(public type) {

  }
}
