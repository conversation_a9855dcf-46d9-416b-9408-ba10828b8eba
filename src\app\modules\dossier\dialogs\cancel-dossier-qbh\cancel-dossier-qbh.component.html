<button mat-icon-button class="close-button" (click)="onDismiss()">
    <mat-icon>close</mat-icon>
</button>

    <h3 class="dialog_title" mat-dialog-title>Từ chối gi<PERSON>i quyết hồ sơ</h3>
<div class="main-layout">
    <div mat-dialog-content class="dialog_content">
      
        <span >Bạn có chắc chắn muốn gửi yêu cầu từ chối giải quyết cho hồ sơ </span>
        <span class="highlight">{{dossierCode}}</span><span>?</span>
      <form [formGroup]="updateForm" class="updateForm" id="ngupdateForm">
        <div fxLayout="column" fxLayout.xs="column" fxLayout.sm="column" class="formFieldOutline"
             fxLayoutAlign="space-between">
          <mat-form-field appearance="outline" fxFlex.gt-sm="column" fxFlex.gt-xs="column" fxFlex='grow'
                          *ngIf="visibleApprovalAgency == 1 && approvalAgency.length >= 1">
            <mat-label i18n="@@approvaledAgency">Cơ quan phê duyệt</mat-label>
            <mat-select formControlName="approvalAgencyId">
              <mat-option value=""></mat-option>
              <mat-option *ngFor='let item of approvalAgency ; let i = index' value="{{item.id}}">
                {{ item.DisplayName }}
              </mat-option>
            </mat-select>
          </mat-form-field>
        </div>
      </form>
        <div class="commentEditor" fxLayout="column" fxLayout.xs="column" fxLayout.sm="column">
            <p class="lbl"><span i18n>Lý do</span><span style="color:#ce7a58;">&nbsp;*</span></p>
            <ckeditor debounce="10" [editor]="Editor" class="customCKEditor" (change)="onCommentEditorChange($event)"
                fxFlex='grow' [config]='commentConfig'>
            </ckeditor>
            <div class="errorMsg" *ngIf="isCKMaxlenght">
                <!-- <span i18n>Nội dung không quá 500 ký tự</span> -->
                <span>{{ckeditorMaxLengthNotification}}</span>
                <div class="err">
                    <mat-icon>priority_high</mat-icon>
                </div>
            </div>
        </div>
        <p class="lbl title-weight">
            <span i18n>Tệp tin đính kèm</span><span *ngIf="requireAttachmentWhenCancel && requireAttachmentWhenCancelHCM" style="color:#ce7a58;">&nbsp;*</span>
            <span class="downloadBtn" (click)="downloadTemplate()" *ngIf="!!fileTemplate">
                <mat-icon class="material-icons-outlined">cloud_download</mat-icon>
                <span class="download-text">Tải file mẫu</span>
            </span>
        </p>
        <div [ngClass]="{'file_uploaded': uploaded == true}" fxHide.lt-md class="marginbottom">
            <div class="drag_upload_btn" [ngClass]="{'no_boder': uploaded == true}">
                <button mat-button [ngClass]="{'btn_uploaded': uploaded == true, 'clear_file_queue': uploaded == false}"
                    fxFlex='grow'>
                    <mat-icon class="material-icons-outlined">cloud_upload</mat-icon> <a href="">
                        <span i18n>Kéo thả tệp tin hoặc </span><span class="txtUpload" i18n>Tải lên</span>
                    </a>
                    <div>
                        <span>Kích thước tối đa của một tệp tin:</span> {{ maxFileSize }}MB
                    </div>
                </button>
                <input id="file_upload" name="file_upload[]" type='file' (change)="onSelectFile($event)" multiple
                    [accept]="acceptFileExtension" value="{{blankVal}}" fxFlex='grow'>
            </div>
            <div class="file_drag_upload_preview">
                <div class="list_uploaded" *ngFor='let url of urls; let i = index;'>
                    <div class="file_icon" [ngStyle]="{'background-image': 'url('+ urls[i] +')'}"></div>
                    <span class="file_name" matTooltip="{{fileNamesFull[i]}}"
                        [matTooltipPosition]="'right'">{{fileNames[i]}}</span>
                    <a mat-icon-button class="delete_file">
                        <mat-icon (click)="removeItem(i)">close</mat-icon>
                    </a>
                </div>
            </div>
        </div>
        <div class="res_uploadFile marginbottom" fxShow="true" fxHide.gt-sm>
            <div class="res_upload_btn">
                <button mat-button fxFlex='grow'>
                    <mat-icon class="material-icons-outlined">cloud_upload</mat-icon>
                    <span class="txtUpload" i18n>Tải lên</span>
                </button>
                <input id="file_upload" name="file_upload[]" type='file' (change)="onSelectFile($event)" multiple
                    [accept]="acceptFileExtension" value="{{blankVal}}" fxFlex='grow'>
            </div>
            <div class="res_upload_preview">
                <div class="list_uploaded" *ngFor='let url of urls; let i = index;' fxFlex.gt-sm="49.5" fxFlex.gt-xs="48.5"
                    fxFlex='grow'>
                    <div class="file_icon" [ngStyle]="{'background-image': 'url('+ urls[i] +')'}"></div>
                    <span class="file_name" matTooltip="{{fileNamesFull[i]}}"
                        [matTooltipPosition]="'right'">{{fileNames[i]}}</span>
                    <a mat-icon-button class="delete_file">
                        <mat-icon (click)="removeItem(i)">close</mat-icon>
                    </a>
                </div>
            </div>
        </div>
    </div>

  <digo-check-send-notify *ngIf="!notifyQNI" functionType="cancelDossier" functionTypeOfficer="cancelDossierOfficer" [receiveType]="env.enableApprovalOfLeadership"></digo-check-send-notify>
  <check-send-notify-qni *ngIf="!!notifyQNI" functionType="cancelDossier" action="Request"></check-send-notify-qni>

    <mat-dialog-actions align="center">
        <button mat-flat-button fxFlex='20' class="applyBtn" (click)="onConfirm()" [disabled]="disableButton">
            <span i18n>Đồng ý</span>
        </button>
        <button mat-flat-button fxFlex='20' class="rejectBtn" (click)="onDismiss()" [disabled]="disableButton">
            <span>Không đồng ý</span>
        </button>
    </mat-dialog-actions>
</div>

<!-- <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="column" fxLayoutAlign="center" fxLayoutGap="1rem">
    <button mat-flat-button fxFlex='20' class="applyBtn" (click)="onConfirm()">
        <span i18n>Đồng ý</span>
    </button>
    <button mat-flat-button fxFlex='20' class="rejectBtn" (click)="onDismiss()">
        <span>Không đồng ý</span>
    </button>
</div> -->