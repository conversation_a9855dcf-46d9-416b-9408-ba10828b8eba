import { MediaMatcher } from '@angular/cdk/layout';
import { HttpBackend, HttpClient } from '@angular/common/http';
import {
    AfterViewInit,
    ChangeDetectorRef,
    Component,
    Inject,
    LOCALE_ID,
    OnDestroy,
    OnInit,
    ViewChild
} from '@angular/core';
import { MediaChange, MediaObserver } from '@angular/flex-layout';
import { FormControl } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import { MatSidenav } from '@angular/material/sidenav';
import { DomSanitizer, Title } from '@angular/platform-browser';
import { ActivatedRoute, Event, NavigationStart, Router } from '@angular/router';
import { ApprovalAgencyConfigService } from 'data/service/approval-agency-config/approval-agency-config.service';
import { EDocService } from "data/service/e-doc.service";
import { NotificationV2Service } from 'data/service/etl-data/notificationV2.service';
// import { getMessaging, getToken, onMessage } from 'firebase/messaging';
import { KeycloakService } from 'keycloak-angular';
import { ReplaySubject, Subscription } from 'rxjs';
import { ApiProviderService } from 'src/app/core/service/api-provider.service';
import { EnvService } from 'src/app/core/service/env.service';
import { ChungThucDienTuService } from 'src/app/data/service/chungThucDienTu/chung-thuc-dien-tu.service';
import { DeploymentService } from 'src/app/data/service/deployment.service';
import { DLKStatisticService } from 'src/app/data/service/dlk-dac-thu/dlk-statistic.service';
import { DossierService } from 'src/app/data/service/dossier/dossier.service';
import { MainService } from 'src/app/data/service/main/main.service';
import { ProcedureService } from 'src/app/data/service/procedure/procedure.service';
import { SectorService } from 'src/app/data/service/sector/sector.service';
import { SurfeedService } from 'src/app/data/service/svc-surfeed/surfeed.service';
import { UserService } from 'src/app/data/service/user.service';
import { AnnouncementPopupComponent, AnnouncementPopupDialogModel } from 'src/app/modules/hcm/announcement/dialogs/announcement-popup/announcement-popup.component';
import { QuickSearchComponent, QuickSearchDialogModel } from '../../dialogs/quick-search/quick-search.component';
import { RingMenuDetail, RingMenuDetailModel } from '../../dialogs/ring-menu-detail/view-detail.component';
import { EventSharedService } from 'src/app/data/service/shared/event-shared.service';

@Component({
    selector: 'app-admin-layout-nav',
    templateUrl: './admin-layout-nav.component.html',
    styleUrls: ['./admin-layout-nav.component.scss', '/src/app/app.component.scss']
})

export class AdminLayoutNavComponent implements OnInit, AfterViewInit, OnDestroy {
    agency:any;
    config = this.envService.getConfig();
    protected keycloakService: KeycloakService;
    approvalAgencyList = [];
    selectedLang = this.localeId;
    selectedLangId = Number(localStorage.getItem('languageId')) || 228;
    agencyName = '';
    displayAgencyName = '';
    siteName: any;
    isLoggedIn = false;
    userName = '';
    displayName = '';
    avatar: any;
    linkRemind: any = '';
    env = this.deploymentService.getAppDeployment()?.env;
    isQNM = this.env?.OS_QNM?.isQNM === true ? this.env?.OS_QNM?.isQNM : false;
    // kiem tra xem co phai Dong Nai de them menu
    isDNI = this.env?.OS_DNI?.isDNIDetailLogActivated;
    // IGATESUPP-113272
    useDNIDocumentNumberingLedger = this.deploymentService.getAppDeployment()?.useDNIDocumentNumberingLedger ? this.deploymentService.getAppDeployment()?.useDNIDocumentNumberingLedger : this.deploymentService?.useDNIDocumentNumberingLedger;

    //OS KGG
    listNotRemindTaskKgg = this.env?.OS_KGG?.listNotRemindTaskKgg ? this.env?.OS_KGG?.listNotRemindTaskKgg : [];

    //OS DLK
    remindDossierDueDlk = this.deploymentService.getConfig("enableRemindDossierDue");
    remindDDCount = 0;

    footer: any = this.deploymentService.getAppDeployment()?.footer;
    qbhrenamemenu  = this.deploymentService.env?.OS_QBH?.qbhrenamemenu ? this.deploymentService.env?.OS_QBH?.qbhrenamemenu : false;
    listPermissionOpt = this.env.opt?.listPermissionOpt ? this.env.opt?.listPermissionOpt?.trim().split(",") : [];
    hasPermissionOpt = false;
    menuLogbook = this.isQNM?'statistics/logbook-qnm':'statistics/logbook';
    mediaSubscription: Subscription;
    isAccountIntegration = this.deploymentService.env?.OS_KTM?.isAccountIntegration;
    viewDowloadApp = this.footer?.viewDowloadApp == true ? true :false;
    isAppNew  = this.footer?.isAppNew;
    linkIos  = this.footer?.linkIos;
    linkAndroid  = this.footer?.linkAndroid;

    //Footer Network Trust
    isShowNetworkTrust = this.footer?.isShowNetworkTrust ? true : false;
    linkIpv6 = this.footer?.linkIpv6;
    LogoIpv6 = this.footer?.LogoIpv6;
    linkATTT = this.footer?.linkATTT;
    LogoATTT = this.footer?.LogoATTT;

    staticCloudUrl = !!this.env?.staticCloudUrl ? this.env.staticCloudUrl : 'https://staticv2.vnptigate.vn/';
    digitizingDossierReportName = !!this.env?.digitizingDossierReportName ? this.env?.digitizingDossierReportName : 'Báo cáo thống kê số hóa hồ sơ';
    showQuickLookupDossier = this.deploymentService.env.OS_HCM.showQuickLookupDossier;
    onlinkHDSD = this.deploymentService.getAppDeployment()?.onlinkHDSD ? this.deploymentService.getAppDeployment()?.onlinkHDSD : 0;
    editlinkHDSD = this.deploymentService.getAppDeployment()?.editlinkHDSD ? this.deploymentService.getAppDeployment()?.editlinkHDSD : '';
    nameAttestation = this.deploymentService.getAppDeployment()?.nameAttestationMenu?.nameAttestation ? this.deploymentService.getAppDeployment()?.nameAttestationMenu?.nameAttestation : 'Chức chứng thực điện tử Kon Tum';
    nameAttestationTrans = this.deploymentService.getAppDeployment()?.nameAttestationMenu?.nameAttestationTrans ? this.deploymentService.getAppDeployment()?.nameAttestationMenu?.nameAttestationTrans : 'Kon Tum Attestation';

    //ReName report HGG
    dossierCancelName = this.deploymentService.getAppDeployment()?.dossierCancelName ? this.deploymentService.getAppDeployment()?.dossierCancelName : 'Thống kê hồ sơ hủy';
    reportVpcpOnlineName = this.deploymentService.getAppDeployment()?.reportVpcpOnlineName ? this.deploymentService.getAppDeployment()?.reportVpcpOnlineName : 'Báo cáo ứng dụng Dịch vụ công trực tuyến';
    konTumListOnlinePaymentReportName = this.deploymentService.getAppDeployment()?.konTumListOnlinePaymentReportName ? this.deploymentService.getAppDeployment()?.konTumListOnlinePaymentReportName : 'Báo cáo thanh toán trực tuyến';
    dossierSyncName = this.deploymentService.getAppDeployment()?.dossierSyncName ? this.deploymentService.getAppDeployment()?.dossierSyncName : 'Đồng bộ hồ sơ cổng Dịch vụ công Quốc gia';
    statisticProcedureBDGName = this.deploymentService.getAppDeployment()?.statisticProcedureBDGName ? this.deploymentService.getAppDeployment()?.statisticProcedureBDGName : '906 Thống kê thủ tục';
    overdueDossierReportBDGName = this.deploymentService.getAppDeployment()?.overdueDossierReportBDGName ? this.deploymentService.getAppDeployment()?.overdueDossierReportBDGName : '903 Thống kê hồ sơ trễ hạn';
    userStatisticsName = this.deploymentService.getAppDeployment()?.userStatisticsName ? this.deploymentService.getAppDeployment()?.userStatisticsName : '901 - Thống kê người dùng';
    dossierFeeDBNReportName = this.deploymentService.getAppDeployment()?.dossierFeeDBNReportName ? this.deploymentService.getAppDeployment()?.dossierFeeDBNReportName : 'ĐBN Thống kê lệ phí hồ sơ';


    //IGATESUPP-108390- BNV_Igate3 Chỉnh sửa text đổi với menu xử lý hồ sơ
    nameDossierRecepOnl = this.deploymentService.newConfigV2?.nameMenuNavBNV?.nameDossierRecepOnl || 'Hồ sơ chờ tiếp nhận';
    nameDossierRecepOnlTrans = this.deploymentService.newConfigV2?.nameMenuNavBNV?.nameDossierRecepOnlTrans || 'Dossier waiting to be received';
    nameDossierRecepDirect = this.deploymentService.newConfigV2?.nameMenuNavBNV?.nameDossierRecepDirect || 'Tiếp nhận hồ sơ';
    nameDossierRecepDirectTrans = this.deploymentService.newConfigV2?.nameMenuNavBNV?.nameDossierRecepDirectTrans || 'Dossier reception';

    //Tên menu KHA điều chỉnh
    onlineRecordProcessingLabel = this.deploymentService.getAppDeployment()?.onlineRecordProcessingLabel || 'Tiếp nhận hồ sơ trực tuyến';
    directRecordProcessingLabel = this.deploymentService.getAppDeployment()?.directRecordProcessingLabel || 'Tiếp nhận hồ sơ trực tiếp';
    recordsToBeProcessedLabel = this.deploymentService.getAppDeployment()?.recordsToBeProcessedLabel || 'Hồ sơ cần xử lý';
    IsCustomMenuEnabled = this.deploymentService.getAppDeployment()?.IsCustomMenuEnabled || false;

    generalReportTitle = this.deploymentService.getAppDeployment()?.generalReportTitle || 'Báo cáo chung';

    storageMenu = [
    {
      mainMenu: [
        {
          languageId: 228,
          name: 'Kho dữ liệu điện tử'
        },
        {
          languageId: 46,
          name: 'Citizens data warehouse'
        }
      ],
      img: 'assets/img/icon/nav-menu/database.png',
      code: 'main',
      active: false,
      route: 'storage'
    }
    ];
    // syncCheckPaymentStatusMenu = [
    //     {
    //       mainMenu: [
    //         {
    //           languageId: 228,
    //           name: 'Kiểm tra và đồng bộ trạng thái thanh toán'
    //         },
    //         {
    //           languageId: 46,
    //           name: 'Check and sync dossier payment status'
    //         }
    //       ],
    //       img: 'assets/img/icon/nav-menu/database.png',
    //       code: 'main',
    //       active: false,
    //       route: 'syncCheckPaymentStatusMenu'
    //     }
    //     ];
    chungThucMenu = [
      {
        mainMenu: [
          {
            languageId: 228,
            name: 'Chứng thực điện tử'
          },
          {
            languageId: 46,
            name: 'Chứng thực điện tử'
          }
        ],
        icon: 'spellcheck',
        active: false,
        permission: [
            {
                code: 'chungThucManager'
            },
            {
                code: 'chungThucAccepter'
            }
        ]
      }
    ];
    sidebarMenu = [
        {
            mainMenu: [
                {
                    languageId: 228,
                    name: 'Xử lý hồ sơ'
                },
                {
                    languageId: 46,
                    name: 'Processing dossiers'
                }
            ],
            icon: 'rule_folder',
            code: 'dossier',
            active: false,
            listSubMenu: [
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Tiếp nhận lịch hẹn Chứng thực điện tử'
                        },
                        {
                            languageId: 46,
                            name: 'Receive appointment for Electronic Certification'
                        }
                    ],
                    route: 'dossier/receive-schedule-ctdt',
                    permission: [
                        {
                            code: 'acceptAppointmentCTDT'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Hồ sơ HCC một cấp chờ tiếp nhận'
                        },
                        {
                            languageId: 46,
                            name: 'Dossier waiting to be received one level'
                        }
                    ],
                    route: 'dossier/online-reception-no-region',
                    permission: [
                        {
                            code: 'oneGateDossierOnlineReceptionNoRegion'
                        }
                    ]
                },
              {
                title: [
                  {
                    languageId: 228,
                    name: 'Hồ sơ cần xin ý kiến'
                  },
                  {
                    languageId: 46,
                    name: 'Dossier needing opinion'
                  }
                ],
                route: 'dossier/needing-opinion',
                permission: [
                  {
                    code: 'oneGatePendingApprovalProfiles'
                  }
                ]
              },
              {
                title: [
                  {
                    languageId: 228,
                    name: 'Tiếp nhận lịch hẹn'
                  },
                  {
                    languageId: 46,
                    name: 'Receive appointment'
                  }
                ],
                route: 'dossier/receive-schedule-ctdt-cto',
                permission: [
                  {
                    code: 'acceptAppointmentCTO'
                  }
                ]
              },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Hồ sơ chờ tiếp nhận qua Kiosk'
                        },
                        {
                            languageId: 46,
                            name: 'Dossier waiting to be received via Kiosk'
                        }
                    ],
                    route: 'dossier/online-reception-of-kiosk',
                    permission: [
                        {
                            code: 'kioskDossierAccepter'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: this.IsCustomMenuEnabled
                                ? this.onlineRecordProcessingLabel.find(item => item.languageId === 228)?.name
                                : this.nameDossierRecepOnl
                        },
                        {
                            languageId: 46,
                            name: this.IsCustomMenuEnabled
                                ? this.onlineRecordProcessingLabel.find(item => item.languageId === 46)?.name
                                : this.nameDossierRecepOnlTrans
                        }
                    ],
                    route: 'dossier/online-reception',
                    permission: [
                        {
                            code: 'oneGateAdminMaster'
                        },
                        {
                            code: 'oneGateDossierManager'
                        },
                        {
                            code: 'oneGateDossierAccepter'
                        },
                        {
                            code: 'oneGateDossierOnlineReception'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Tiếp nhận hồ sơ HCC một cấp'
                        },
                        {
                            languageId: 46,
                            name: 'Receiving one-level HCC applications'
                        }
                    ],
                    route: 'dossier/reception-one-level-hcc',
                    permission: [
                        {
                            code: 'oneGateDossierAccepterNoRegion'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: this.IsCustomMenuEnabled
                                ? this.directRecordProcessingLabel.find(item => item.languageId === 228)?.name
                                : this.nameDossierRecepDirect
                        },
                        {
                            languageId: 46,
                            name: this.IsCustomMenuEnabled
                                ? this.directRecordProcessingLabel.find(item => item.languageId === 46)?.name
                                : this.nameDossierRecepDirectTrans
                        }
                    ],
                    route: 'dossier/reception',
                    permission: [
                        {
                            code: 'oneGateAdminMaster'
                        },
                        {
                            code: 'oneGateDossierManager'
                        },
                        {
                            code: 'oneGateDossierAccepter'
                        },
                        {
                            code: 'oneGateDossierReception'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Import hồ sơ từ file excel'
                        },
                        {
                            languageId: 46,
                            name: 'Import dossier from excel'
                        }
                    ],
                    route: 'dossier/import',
                    permission: [
                        {
                            code: 'oneGateAdminMaster'
                        },
                        {
                            code: 'oneGateDossierManager'
                        },
                        {
                            code: 'oneGateDossierImport'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Import hồ sơ từ file excel v2'
                        },
                        {
                            languageId: 46,
                            name: 'Import dossier from excel v2'
                        }
                    ],
                    route: 'dossier/import-v2',
                    permission: [
                        {
                            code: 'oneGateDossierImportV2'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Chuyển hồ sơ đang xử lý cùng hệ thống'
                        },
                        {
                            languageId: 46,
                            name: 'Import dossier in system'
                        }
                    ],
                    route: 'dossier/import-dossier-in-system',
                    permission: [
                        {
                            code: 'oneGateInSystemDossierTransfer'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Đánh giá hồ sơ tại quầy tiếp nhận'
                        },
                        {
                            languageId: 46,
                            name: 'Review of application at reception desk'
                        }
                    ],
                    route: 'dossier/direct-rating',
                    permission: [
                        {
                            code: 'oneGatedirectRating'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: this.IsCustomMenuEnabled
                                ? this.recordsToBeProcessedLabel.find(item => item.languageId === 228)?.name
                                : 'Xử lý hồ sơ'
                        },
                        {
                            languageId: 46,
                            name: this.IsCustomMenuEnabled
                                ? this.recordsToBeProcessedLabel.find(item => item.languageId === 46)?.name
                                : 'Dossier processing'
                        }
                    ],
                    route: 'dossier/processing',
                    permission: [
                        {
                            code: 'oneGateAdminMaster'
                        },
                        {
                            code: 'oneGateDossierManager'
                        },
                        {
                            code: 'oneGateDossierProcessing'
                        },
                        {
                            code: 'oneGateHCMCancelDossier'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Quản lý hồ sơ HCC'
                        },
                        {
                            languageId: 46,
                            name: 'Dossier processing of public administrative'
                        }
                    ],
                    route: 'dossier/processing-hcc',
                    permission: [
                        {
                            code: 'oneGateDossierProcessingHCC'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Trả kết quả hồ sơ HCC một cấp'
                        },
                        {
                            languageId: 46,
                            name: 'Trả kết quả hồ sơ HCC một cấp'
                        }
                    ],
                    route: 'dossier/processing-one-level-hcc',
                    permission: [
                        {
                            code: 'oneGateDossierReturnResultNoRegion'
                        }
                    ]
                },
                {

                    title: [
                        {
                            languageId: 228,
                            name: 'Hồ sơ không cần xử lý'
                        },
                        {
                            languageId: 46,
                            name: 'Dossier cancel'
                        }
                    ],
                    route: 'dossier/cancel',
                    permission: [
                        {
                            code: 'oneGateAdminMaster'
                        },
                        {
                            code: 'oneGateDossierManager'
                        },
                        {
                            code: 'oneGateDossierCancel'
                        }
                    ]

                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Tra cứu hồ sơ toàn đơn vị'
                        },
                        {
                            languageId: 46,
                            name: 'Search dossier by all agencies'
                        }
                    ],
                    route: 'dossier/search-all-agency',
                    permission: [
                        {
                            code: 'oneGateAdminMaster'
                        },
                        {
                            code: 'oneGateDossierManager'
                        },
                        {
                            code: 'oneGateSearchByAgencyFullStatus'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Tra cứu hồ sơ theo đơn vị'
                        },
                        {
                            languageId: 46,
                            name: 'Search dossier by agency'
                        }
                    ],
                    route: 'dossier/search-by-agency',
                    permission: [
                        {
                            code: 'oneGateAdminMaster'
                        },
                        {
                            code: 'oneGateDossierManager'
                        },
                        {
                            code: 'oneGateDossierLookupByAgency'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Tra cứu hồ sơ HCC một cấp'
                        },
                        {
                            languageId: 46,
                            name: 'Search dossier by agency one level'
                        }
                    ],
                    route: 'dossier/search-by-agency-no-region',
                    permission: [
                        {
                            code: 'oneGateDossierLookupAccepterNoRegion'
                        }
                    ]
                },
                {
                  title: [
                      {
                          languageId: 228,
                          name: 'Tra cứu hồ sơ cá nhân từng xử lý'
                      },
                      {
                          languageId: 46,
                          name: 'Search dossier by agency'
                      }
                  ],
                  route: 'dossier/search-by-stepownuser-kgg',
                  permission: [
                      {
                            code: 'permissionDossierKGG'
                      }
                  ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Tra cứu hồ sơ toàn cơ quan'
                        },
                        {
                            languageId: 46,
                            name: 'Search dossier by all agency'
                        }
                    ],
                    route: 'dossier/search',
                    permission: [
                        {
                            code: 'oneGateAdminMaster'
                        },
                        {
                            code: 'oneGateDossierManager'
                        },
                        {
                            code: 'oneGateDossierLookup'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Tra cứu hồ sơ cũ toàn cơ quan'
                        },
                        {
                            languageId: 46,
                            name: 'Search dossier by all time'
                        }
                    ],
                    route: 'dossier/search-all-time',
                    permission: [
                        {
                            code: 'oneGateDossierOldLookup'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Tra cứu hồ sơ thanh toán'
                        },
                        {
                            languageId: 46,
                            name: 'Search dossier for payment'
                        }
                    ],
                    route: 'dossier/search-for-payment',
                    permission: [
                        {
                            code: 'oneGateDossierPaymentHGI'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Tra cứu hồ sơ cá nhân'
                        },
                        {
                            languageId: 46,
                            name: 'Search personal dossier'
                        }
                    ],
                    route: 'dossier/search-personal',
                    permission: [
                        {
                            code: 'oneGateAdminMaster'
                        },
                        {
                            code: 'oneGateDossierManager'
                        },
                        {
                            code: 'oneGateDossierLookupPersonal'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Cấu hình số hồ sơ'
                        },
                        {
                            languageId: 46,
                            name: 'Dossier configuration'
                        }
                    ],
                    route: 'dossier/config',
                    permission: [
                        {
                            code: 'oneGateAdminMaster'
                        },
                        {
                            code: 'oneGateDossierManager'
                        },
                        {
                            code: 'oneGateDossierConfiguration'
                        }
                    ]
                },
                {
                    title: [
                      {
                        languageId: 228,
                        name: 'Số hóa số hồ sơ'
                      },
                      {
                        languageId: 46,
                        name: 'Dossier Digitize'
                      }
                    ],
                    route: 'dossier/digitize',
                    permission: [
                      {
                        code: 'oneDbnDigitizeDossierManager'
                      }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Tra cứu hồ sơ'
                        },
                        {
                            languageId: 46,
                            name: 'Search dossier'
                        }
                    ],
                    route: 'dossier/search-one-agency',
                    permission: [
                        {
                            code: 'oneGateDossierOneAgency'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Cập nhật hồ sơ từ file excel'
                        },
                        {
                            languageId: 46,
                            name: 'Update dossier from excel'
                        }
                    ],
                    route: 'dossier/update-dossier-from-excel',
                    permission: [
                        {
                            code: 'oneGateUpdateDossierFromExcel'
                        },
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Thanh toán lệ phí'
                        },
                        {
                            languageId: 46,
                            name: 'Search dossier for payment CMU'
                        }
                    ],
                    route: 'dossier/search-for-payment-cmu',
                    permission: [
                        {
                            code: 'oneGateDossierPaymentCMU'
                        }
                    ]
                },
                {

                    title: [
                        {
                            languageId: 228,
                            name: 'Hồ sơ dừng xử lý'
                        },
                        {
                            languageId: 46,
                            name: 'Dossier withdraw'
                        }
                    ],
                    route: 'dossier/vpc-cancel',
                    permission: [
                        {
                            code: 'oneGateDossierWithdrawVpc'
                        }
                    ]
                },
                {
                    title: [
                        {
                          languageId: 228,
                          name: 'Hồ sơ chờ tiếp nhận v2'
                        },
                        {
                          languageId: 46,
                          name: 'Search dossier online reception v2'
                        }
                      ],
                    route: 'dossier/v2/online-reception',
                    permission: [
                        {
                          code: 'oneGateDossierAcceptOptimal'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Xử lý hồ sơ'
                        },
                        {
                            languageId: 46,
                            name: 'Dossier processing'
                        }
                    ],
                    route: 'dossier/v2/processing',
                    permission: [
                        {
                            code: 'oneGateDossierProcessingOptimal'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Tra cứu hồ sơ toàn cơ quan'
                        },
                        {
                            languageId: 46,
                            name: 'Search dossier by all agency'
                        }
                    ],
                    route: 'dossier/v2/search',
                    permission: [
                        {
                            code: 'oneGateDossierLookupAllAgencyOptimal'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Tra cứu hồ sơ theo đơn vị v2'
                        },
                        {
                            languageId: 46,
                            name: 'Search dossier by agency v2'
                        }
                    ],
                    route: 'dossier/v2/search-by-agency',
                    permission: [
                        {
                            code: 'oneGateDossierLookupByAgencyOptimal'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Tra cứu hồ sơ cá nhân v2'
                        },
                        {
                            languageId: 46,
                            name: 'Search personal dossier v2'
                        }
                    ],
                    route: 'dossier/v2/search-personal',
                    permission: [
                        {
                            code: 'oneGateDossierLookupPersonalOptimal'
                        }
                    ]
                },
                {
                    title: [
                        {
                          languageId: 228,
                          name: 'Hồ sơ không cần xử lý v2'
                        },
                        {
                          languageId: 46,
                          name: 'Dossier cancel v2'
                        }
                      ],
                    route: 'dossier/v2/cancel',
                    permission: [
                        {
                          code: 'oneGateDossierCancelOptimal'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Xuất/Nhập hồ sơ đang xử lý'
                        },
                        {
                            languageId: 46,
                            name: 'Export/Import Inprogress Dossier (Merge)'
                        }
                    ],
                    route: 'dossier/dossier-export-import',
                    permission: [
                        {
                            code: 'oneGateExportImportDossierMerge'
                        }
                    ]
                },
              ]
        },
        {
            mainMenu: [
                {
                    languageId: 228,
                    name: 'Dữ liệu một cửa'
                },
                {
                    languageId: 46,
                    name: 'Onegate data'
                }
            ],
            icon: 'device_hub',
            code: 'onegate-database',
            active: false,
            listSubMenu: [
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Danh mục lĩnh vực'
                        },
                        {
                            languageId: 46,
                            name: 'Sector category'
                        }
                    ],
                    route: 'onegate-data/list-sector',
                    permission: [
                        {
                            code: 'oneGateAdminMaster'
                        },
                        {
                            code: 'oneGateSectorCategory'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Danh mục nhóm giấy tờ'
                        },
                        {
                            languageId: 46,
                            name: 'Document group category'
                        }
                    ],
                    route: 'onegate-data/list-form-group',
                    permission: [
                        {
                            code: 'oneGateAdminMaster'
                        },
                        {
                            code: 'oneGateDocumentGroupCategory'
                        }
                    ]
                }
                ,
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Danh mục giấy tờ'
                        },
                        {
                            languageId: 46,
                            name: 'Document category'
                        }
                    ],
                    route: 'onegate-data/list-form',
                    permission: [
                        {
                            code: 'oneGateAdminMaster'
                        },
                        {
                            code: 'oneGateDocumentCategory'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Danh mục thủ tục'
                        },
                        {
                            languageId: 46,
                            name: 'Procedure category'
                        }
                    ],
                    route: 'procedure',
                    permission: [
                        {
                            code: 'oneGateAdminMaster'
                        },
                        {
                            code: 'oneGateProcedureCategory'
                        }
                    ]
                },
                // {
                //     title: [
                //         {
                //             languageId: 228,
                //             name: 'Cấu hình phiếu động cho quy trình'
                //         },
                //         {
                //             languageId: 46,
                //             name: `Process's ticket configuration`
                //         }
                //     ],
                //     route: 'process-report-config',
                //     permission: [
                //         {
                //             code: 'oneGateAdminMaster'
                //         },
                //         {
                //             code: 'oneGateProcessReportConfig'
                //         }
                //     ]
                // },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Danh mục loại phí/ lệ phí'
                        },
                        {
                            languageId: 46,
                            name: 'Fee/Expenses type category'
                        }
                    ],
                    route: 'onegate-data/list-fee-type',
                    permission: [
                        {
                            code: 'oneGateAdminMaster'
                        },
                        {
                            code: 'oneGateFeeTypeCategory'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Danh mục phí/ lệ phí'
                        },
                        {
                            languageId: 46,
                            name: 'Fee/Expenses category'
                        }
                    ],
                    route: 'onegate-data/list-procost-type',
                    permission: [
                        {
                            code: 'oneGateAdminMaster'
                        },
                        {
                            code: 'oneGateProcostCategory'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Cấu hình phiếu động'
                        },
                        {
                            languageId: 46,
                            name: `Template configuration`
                        }
                    ],
                    route: 'onegate-data/config-template',
                    permission: [
                        {
                            code: 'oneGateAdminMaster'
                        },
                        {
                            code: 'oneGateConfigTemplate'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Phân công cơ quan HCC một cấp'
                        },
                        {
                            languageId: 46,
                            name: `One-level HCC agency assignment`
                        }
                    ],
                    route: 'onegate-data/one-level-hcc-agency-assigment',
                    permission: [
                        {
                            code: 'agencyAssignment'
                        }
                    ]
                },
                // {
                //     title: [
                //         {
                //             languageId: 228,
                //             name: 'Cấu hình tra cứu doanh nghiệp cho thủ tục'
                //         },
                //         {
                //             languageId: 46,
                //             name: 'Business lookup configuration'
                //         }
                //     ],
                //     route: 'config-business',
                //     permission: [
                //         {
                //             code: 'oneGateAdminMaster'
                //         },
                //         {
                //             code: 'oneGateBusinessConfig'
                //         }
                //     ]
                // },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Danh mục quy trình'
                        },
                        {
                            languageId: 46,
                            name: 'Process catalog'
                        }
                    ],
                    route: 'onegate-data/list-process',
                    permission: [
                        {
                            code: 'oneGateAdminMaster'
                        },
                        {
                            code: 'oneGateProcessCatalog'
                        },
                        {
                            code: 'oneGateViewProcess'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Danh mục nhóm quy trình'
                        },
                        {
                            languageId: 46,
                            name: 'Group Process catalog'
                        }
                    ],
                    route: 'onegate-data/list-group-process',
                    permission: [
                        {
                            code: 'oneGateAdminMaster'
                        },
                        {
                            code: 'oneGateGroupProcessCatalog'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Danh mục sổ'
                        },
                        {
                            languageId: 46,
                            name: 'Ledger definition catalog'
                        }
                    ],
                    // IGATESUPP-113272
                    route: this.useDNIDocumentNumberingLedger? 'onegate-data/dni-ledger-definition': 'onegate-data/list-ledger-definition',
                    permission: [
                        {
                            code: 'oneGateAdminMaster'
                        },
                        {
                            code: 'oneGateLedgerDefinitionCatalog'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Danh mục quyển sổ'
                        },
                        {
                            languageId: 46,
                            name: 'Ledger catalog'
                        }
                    ],
                    route: 'onegate-data/list-ledger',
                    permission: [
                        {
                            code: 'oneGateAdminMaster'
                        },
                        {
                            code: 'oneGateLedgerCatalog'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Cấu hình đơn vị phê duyệt'
                        },
                        {
                            languageId: 46,
                            name: 'Approval Agency Config'
                        }
                    ],
                    route: 'onegate-data/list-approval-agency-config',
                    permission: [
                        {
                            code: 'oneGateAdminMaster'
                        },
                        {
                            code: 'oneGateApprovalAgencyConfigCatalog'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Danh mục tài khoản thụ hưởng'
                        },
                        {
                            languageId: 46,
                            name: 'Beneficiary account list'
                        }
                    ],
                    route: 'onegate-data/list-beneficiary-account-agency',
                    permission: [
                        {
                            code: 'oneGateAdminMaster'
                        },
                        {
                            code: 'oneGateBeneficiaryAccountAgencyCatalog'
                        }
                    ]
                },
              {
                title: [
                  {
                    languageId: 228,
                    name: 'Danh mục máy POS'
                  },
                  {
                    languageId: 46,
                    name: 'List of pos machines'
                  }
                ],
                route: 'onegate-data/list-pos-machines',
                permission: [
                  {
                    code: 'oneGateAdminMaster'
                  },
                  {
                    code: 'oneGateListPosMachines'
                  }
                ]
              },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Danh mục ngân hàng'
                        },
                        {
                            languageId: 46,
                            name: 'Bank category'
                        }
                    ],
                    route: 'onegate-data/list-bank',
                    permission: [
                        {
                            code: 'oneGateAdminMaster'
                        },
                        {
                            code: 'oneGateBankCategory'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Danh mục Sổ hành chính công'
                        },
                        {
                            languageId: 46,
                            name: 'List of public administration books'
                        }
                    ],
                    route: 'list-administrative-book',
                    permission: [
                        {
                            code: 'oneGateHCMAdmin'
                        },
                        {
                            code: 'oneGateAdministrativeCategory'
                        },
                        {
                            code: 'oneGateHCMAdmin'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Danh sách Bộ số thủ tục hành chính'
                        },
                        {
                            languageId: 46,
                            name: 'List of administrative procedure numbers'
                        }
                    ],
                    route: 'list-administrative-procedure-number',
                    permission: [
                        {
                            code: 'oneGateHCMAdmin'
                        },
                        {
                            code: 'oneGateAdministrativeProcedureCategory'
                        },
                        {
                            code: 'oneGateHCMAdmin'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Danh mục mã định danh'
                        },
                        {
                            languageId: 46,
                            name: 'List of identification code'
                        }
                    ],
                    route: 'onegate-data/list-identification-code',
                    permission: [
                      {
                          code: 'oneGateQNMAdmin'
                      },
                      {
                          code: 'oneGateListIdentificationCode'
                      }
                  ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Cấu hình mã số biên nhận'
                        },
                        {
                            languageId: 46,
                            name: 'Configure receipt code'
                        }
                    ],
                    route: 'onegate-data/configure-receipt-code',
                    permission: [
                      {
                          code: 'oneGateConfigureReceiptCode'
                      },
                    ]
                },
                {
                  title: [
                    {
                      languageId: 228,
                      name: 'Danh mục biểu mẫu'
                    },
                    {
                      languageId: 46,
                      name: 'eForm category'
                    }
                  ],
                  route: 'onegate-data/list-eform',
                  permission: [
                    {
                      code: 'oneGateAdminMaster'
                    },
                    {
                      code: 'oneGateEformCatalog'
                    }
                  ]
                },
                {
                    title: [
                      {
                        languageId: 228,
                        name: 'Danh mục số điện thoại liên hệ'
                      },
                      {
                        languageId: 46,
                        name: 'eForm category'
                      }
                    ],
                    route: 'onegate-data/hotlineqbh',
                    permission: [
                      {
                        code: 'oneGateAdminMaster'
                      },
                      {
                        code: 'oneGateEformCatalog'
                      }
                    ]
                  },
                  // tạm ẩn menu chưa sử dụng
                // {
                //     title: [
                //         {
                //             languageId: 228,
                //             name: 'Cấu hình tích hợp'
                //         },
                //         {
                //             languageId: 46,
                //             name: 'Integrated Configuration'
                //         }
                //     ],
                //     route: 'onegate-data/procedure-configuration-integration',
                //     permission: [
                //         {
                //             code: 'oneGateAdminMaster'
                //         },
                //         {
                //             code: 'oneGateProcedureIntegrationConfig'
                //         }
                //     ]
                // },
                {
                    title: [
                      {
                        languageId: 228,
                        name: 'Danh mục sổ tiếp nhận'
                      },
                      {
                        languageId: 46,
                        name: 'Receipt Book category'
                      }
                    ],
                    route: 'onegate-data/list-receipt-book',
                    permission: [
                      {
                        code: 'oneGateReceiptBookCatalog'
                      },                      
                    ]
                },                 
            ]
        },
        {
            mainMenu: [
                {
                    languageId: 228,
                    name: 'Đồng bộ dữ liệu'
                },
                {
                    languageId: 46,
                    name: 'Synchronize data'
                }
            ],
            icon: 'sync',
            code: 'onegate-database',
            active: false,
            listSubMenu: [
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Đồng bộ thủ tục cổng Dịch vụ công Quốc gia'
                        },
                        {
                            languageId: 46,
                            name: 'National Public Administrative Service\'s Procedure'
                        }
                    ],
                    route: 'sync/npadsvc-sync',
                    permission: [
                        {
                            code: 'oneGateAdminMaster'
                        },
                        {
                            code: 'oneGateNpadsvcSync'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Đẩy số liệu báo cáo tổng hợp lên Cổng DVC Quốc gia'
                        },
                        {
                            languageId: 46,
                            name: 'Pushing aggregated report data to the National DVC Portal'
                        }
                    ],
                    route: 'sync/synthesis-report-sync',
                    permission: [
                        {
                            code: 'oneGateAdminMaster'
                        },
                        {
                            code: 'oneGateDossierSync'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Kiểm tra liên thông DVCQG'
                        },
                        {
                            languageId: 46,
                            name: 'Check National Service Connection'
                        }
                    ],
                    route: 'sync/check-dvcqg',
                    permission: [
                        {
                            code: 'oneGateAdminMaster'
                        },
                        {
                            code: 'oneGateDossierSync'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Kiểm tra đối soát DVCQG'
                        },
                        {
                            languageId: 46,
                            name: 'Check Control DVCQG'
                        }
                    ],
                    route: 'sync/control-sync',
                    permission: [
                        {
                            code: 'oneGateAdminMaster'
                        },
                        {
                            code: 'oneGateDossierSync'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Tra cứu TTHC có sự thay đổi'
                        },
                        {
                            languageId: 46,
                            name: 'Look up administrative procedures changed'
                        }
                    ],
                    route: 'sync/procedure-sync',
                    permission: [
                        {
                            code: 'oneGateProcedureChangeSync'
                        }
                    ]
                },
                {
                title: [
                    {
                        languageId: 228,
                        name: 'Đồng bộ lại hồ sơ 766'
                    },
                    {
                        languageId: 46,
                        name: 'Sysnc Dossier 766'
                    }
                ],
                route: 'statistics/dossier-sync-qbh',
                permission: [
                    {
                        code: '766Resync'
                    }
                ]
                },

            ]
        },
        {
            mainMenu: [
                {
                    languageId: 228,
                    name: 'Thống kê báo cáo'
                },
                {
                    languageId: 46,
                    name: 'Statistics / Report'
                }
            ],
            icon: 'pie_chart',
            code: 'onegate-database',
            active: false,
            listSubMenu: [
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Báo cáo định kỳ'
                        },
                        {
                            languageId: 46,
                            name: 'Report weekly'
                        }
                    ],
                    route: 'statistics/ldg-bc-dinhky',
                    permission: [
                        {
                            code: 'ldgBaoCaoTuan'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Báo cáo quá hạn'
                        },
                        {
                            languageId: 46,
                            name: ''
                        }
                    ],
                    route: 'statistics/ldg-bc-hsquahan',
                    permission: [
                        {
                            code: 'ldgBaoCaoTuan'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Báo cáo dịch vụ công theo thủ tục'
                        },
                        {
                            languageId: 46,
                            name: 'ldgBaoCaoTuan'
                        }
                    ],
                    route: 'statistics/ldg-bc-dvcongtheothutuc',
                    permission: [
                        {
                            code: 'ldgBaoCaoTuan'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Báo cáo dịch vụ công'
                        },
                        {
                            languageId: 46,
                            name: ''
                        }
                    ],
                    route: 'statistics/ldg-bc-dvcong',
                    permission: [
                        {
                            code: 'ldgBaoCaoTuan'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Báo cáo số hóa'
                        },
                        {
                            languageId: 46,
                            name: 'Digitization Report '
                        }
                    ],
                    route: 'statistics/ldg-digitization-report',
                    permission: [
                        {
                            code: 'oneGateLamdongDigitizationReport'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Báo cáo hồ sơ sắp đến hạn'
                        },
                        {
                            languageId: 46,
                            name: 'Report Due Near'
                        }
                    ],
                    route: 'statistics/ldg-bc-sapdenhan',
                    permission: [
                        {
                            code: 'ldgBaoCaoTuan'
                        }
                    ]
                }
                ,
                 {
                    title: [
                        {
                            languageId: 228,
                            name: 'Báo cáo kiểm soát thủ tục hành chính'
                        },
                        {
                            languageId: 46,
                            name: 'statistic procedure report'
                        }
                    ],
                    route: 'statistics/ldg-bc-kiemsoattthc',
                    permission: [
                        {
                            code: 'ldgBaoCaoTuan'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Thống kê truy cập lấy giấy tờ từ Kho'
                        },
                        {
                            languageId: 46,
                            name: 'Warehouse access statistics'
                        }
                    ],
                    route: 'statistics/statistic-warehouse-access-qbh',
                    permission: [
                        {
                            code: 'oneGateReportDVCOnlineQBH'
                        },
                        {
                            code: 'saveLogDoc'
                        }
                    ]
                },
                {
                    title: [
                      {
                        languageId: 228,

                        name: 'Thống kê DVC LAN'
                      },
                      {
                        languageId: 46,
                        name: 'Thống kê DVC LAN'
                      }
                    ],
                    route: 'statistics/statistics-dvc-lan',
                    permission: [
                      {
                          code: 'onegateStaticsDvcLan'
                      }
                    ]
                },
                {
                    title: [
                      {
                        languageId: 228,

                        name: 'Thống kê hồ sơ nộp qua kios'
                      },
                      {
                        languageId: 46,
                        name: 'General Kios Dossier List HGI'
                      }
                    ],
                    route: 'statistics/thong-ke-ho-so-nop-qua-kios-hgi',
                    permission: [
                      {
                          code: 'oneGateKiosDossierList'
                      }
                    ]
                },
                {
                    title: [
                      {
                        languageId: 228,

                        name: 'Thống kê hồ sơ qua dịch vụ BCCI'
                      },
                      {
                        languageId: 46,
                        name: 'General BCCI Dossier List GLI'
                      }
                    ],
                    route: 'gli-dac-thu/report-dossier-bcci',
                    permission: [
                      {
                          code: 'oneGateBCCIDossierList'
                      }
                    ]
                },

                {
                    title: [
                      {
                        languageId: 228,

                        name: 'Thống kê hồ sơ thanh toán'
                      },
                      {
                        languageId: 46,
                        name: 'General Statistics Procedure List HGI'
                      }
                    ],
                    route: 'statistics/thong-ke-tong-hop-ho-so-thanh-toan-hgi',
                    permission: [
                      {
                          code: 'oneGatePaidDossierList'
                      }
                    ]
                },
                {
                    title: [
                      {
                        languageId: 228,

                        name: 'Thống kê hồ sơ số hóa tổng hợp',
                      },
                      {
                        languageId: 46,
                        name: 'Statistic Summarize Digitization List ',
                      },
                    ],
                    route: 'statistics/thong-ke-ho-so-so-hoa-tong-hop',
                    permission: [
                      {
                        code: 'oneGateSummarizeDigitizationList',
                      },
                    ],
                },
                {
                    title: [
                      {
                        languageId: 228,
                        name: 'Thống kê khách hàng tổ chức',
                      },
                      {
                        languageId: 46,
                        name: 'Statistic organization customer',
                      },
                    ],
                    route: 'statistics/statistic-organization-customer',
                    permission: [
                      {
                        code: 'RptOrganizationCustomerStatisticsKHAUC208',
                      },
                    ],
                },
                {
                    title: [
                      {
                        languageId: 228,
                        name: 'Thống kê sổ tiếp nhận và trả kết quả'
                      },
                      {
                        languageId: 46,
                        name: 'Statistics on Reception and Result-Return'
                      }
                    ],
                    route: 'statistics/statistic-reception-and-result',
                    permission: [
                      {
                          code: 'RptStatisticsontheReceiptandResultLogKHAUC211'
                      }
                    ]
                },
                {
                    title: [
                      {
                        languageId: 228,
                        name: 'Thống kê danh sách thủ tục'
                      },
                      {
                        languageId: 46,
                        name: 'General Statistics Procedure List HGI'
                      }
                    ],
                    route: 'statistics/danh-sach-thu-tuc',
                    permission: [
                      {
                          code: 'oneGateProcedureList'
                      }
                    ]
                },
                {
                    title: [
                      {
                        languageId: 228,
                        name: '[VPC] Thống kê hồ sơ tiếp nhận'
                      },
                      {
                        languageId: 46,
                        name: '[VPC] General Statistics Agency'
                      }
                    ],
                    route: 'statistics/report-dossier-received',
                    permission: [
                        {
                            code: 'oneGateReportReceivedVpc'
                        }
                    ]
                },
                {
                    title: [
                      {
                        languageId: 228,
                        name: 'Thống kê đánh giá chất lượng giải quyết TTHC'
                      },
                      {
                        languageId: 46,
                        name: 'Statistic rating procedure'
                      }
                    ],
                    route: 'statistics/statistic-rating',
                    permission: [
                        {
                            code: 'oneGateRatingReport'
                        }
                    ]
                },
                {
                    title: [
                      {
                        languageId: 228,
                        name: 'Thống kê danh mục TTHC',
                      },
                      {
                        languageId: 46,
                        name: 'Statistic procedure list',
                      },
                    ],
                    route: 'statistics/statistic-procedure-list',
                    permission: [
                      {
                        code: 'RptListofAdministrativeProceduresKHAUC214',
                      },
                    ],
                },
                {
                    title: [
                      {
                        languageId: 228,
                        name: 'Danh sách các IP bên ngoài tiếp nhận hồ sơ'
                      },
                      {
                        languageId: 46,
                        name: 'List of External IPs for Receiving Applications'
                      }
                    ],
                    route: 'statistics/ip-outside-bussiness',
                    permission: [
                        {
                            code: 'RptIPOutsideBusinessKHA'
                        }
                    ]
                },
                {
                    title: [
                      {
                        languageId: 228,
                        name: 'Tổng hợp đánh giá hài lòng theo đơn vị'
                      },
                      {
                        languageId: 46,
                        name: 'Total price satisfaction by unit'
                      }
                    ],
                    route: 'statistics/agency-rating',
                    permission: [
                      {
                          code: 'oneGateAgencyRating'
                      }
                    ]
                },
                {
                    title: [
                      {
                        languageId: 228,
                        name: 'Tổng hợp đánh giá hài lòng theo cán bộ'
                      },
                      {
                        languageId: 46,
                        name: 'Summary of satisfactory prices by set'
                      }
                    ],
                    route: 'statistics/accepter-rating',
                    permission: [
                      {
                          code: 'oneGateAccepterRating'
                      }
                    ]
                },
                {
                    title: [
                      {
                        languageId: 228,
                        name: 'Thống kê chung đơn vị HGI'
                      },
                      {
                        languageId: 46,
                        name: 'General Statistics Agency HGI'
                      }
                    ],
                    route: 'statistics/thong-ke-don-vi',
                    permission: [
                      {
                          code: 'oneGateAgencyStatisticalHGI'
                      }
                    ]
                },
                {
                    title: [
                      {
                        languageId: 228,
                        name: 'Thống kê theo dõi kết quả xử lý hồ sơ'
                      },
                      {
                        languageId: 46,
                        name: 'Statistic on tracking dossier processing results'
                      }
                    ],
                    route: 'statistics/dossier-processing-results',
                    permission: [
                      {
                          code: 'RptStatisticsonCaseProcessingResultsMonitoringKHAUC212'
                      }
                    ]
                },
                {
                    title: [
                      {
                        languageId: 228,
                        name: 'Thống kê hồ sơ thanh toán phí'
                      },
                      {
                        languageId: 46,
                        name: 'Statistic of fee payment dossier'
                      }
                    ],
                    route: 'statistics/statistic-fee-payment-record',
                    permission: [
                      {
                          code: 'RptStatisticsOfFeePaymentRecordsKHAUC210'
                      }
                    ]
                },
                {
                    title: [
                      {
                        languageId: 228,
                        name: 'Thống kê theo thủ tục'
                      },
                      {
                        languageId: 46,
                        name: 'General Statistics Procedure'
                      }
                    ],
                    route: 'statistics/thong-ke-thu-tuc',
                    permission: [
                      {
                        code: 'oneGateAllStatisticalHGI'
                      },
                      {
                          code: 'oneGateProcedureStatisticalHGI'
                      }
                    ]
                },
                {
                    title: [
                      {
                        languageId: 228,
                        name: 'Thống kê theo lĩnh vực'
                      },
                      {
                        languageId: 46,
                        name: 'General Statistics Sector'
                      }
                    ],
                    route: 'statistics/thong-ke-linh-vuc',
                    permission: [
                      {
                        code: 'oneGateAllStatisticalHGI'
                      },
                      {
                          code: 'oneGateSectorStatisticalHGI'
                      }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Thống kê theo văn bản 749/VNPT-IT-KV5'
                        },
                        {
                            languageId: 46,
                            name: '749/VNPT-IT-KV5 statistics'
                        }
                    ],
                    route: 'statistics/statistic-749-it5',
                    permission: [
                        {
                            code: 'oneGateReport'
                        },
                        {
                            code: 'oneGate749Statistical'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Thống kê xuất dữ liệu SNV'
                        },
                        {
                            languageId: 46,
                            name: 'Statistics export data SNV'
                        }
                    ],
                    route: 'statistics/statistic-export-data-snv',
                    permission: [
                        {
                            code: 'statisticExportDataSNV'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Thống kê tổng hợp Kiên Giang'
                        },
                        {
                            languageId: 46,
                            name: 'Summary statistics Kien Giang'
                        }
                    ],
                    route: 'statistics/statistic-procedure-kgg',
                    permission: [
                        {
                            code: 'statisticalProcedureKGG'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Thống kê tổng hợp hồ sơ nộp trực tuyến'
                        },
                        {
                            languageId: 46,
                            name: 'Summary statistics online dossier'
                        }
                    ],
                    route: 'statistics/statistic-online-dossier-kgg',
                    permission: [
                        {
                            code: 'statisticalOnlineDossierKGG'
                        },
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Thống kê báo cáo theo mẫu P.KSTTHC'
                        },
                        {
                            languageId: 46,
                            name: 'Summary statistics based on P.KSTTHC form'
                        }
                    ],
                    route: 'statistics/statistic-tkbc',
                    permission: [
                        {
                            code: 'oneGateTKBCStatistical'
                        }
                    ]
                },

                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Thống kê theo thông tư 01/2020/TT-VPCP HCM'
                        },
                        {
                            languageId: 46,
                            name: 'Circular 01/2020/TT-VPCP HCM Statistics'
                        }
                    ],
                    route: 'statistics/statistic-01-2020-hcm-overdue-by-day',
                    permission: [
                        {
                            code: 'oneGate012020StatisticalHCMOverdueByDay'
                        }
                    ]
                },

                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Tra cứu log hồ sơ'
                        },
                        {
                            languageId: 46,
                            name: 'Look up dossier log'
                        }
                    ],
                    route: 'statistics/statistic-dossier-log',
                    permission: [
                        {
                            code: 'oneGateReport'
                        },
                        {
                            code: 'oneGateDossierLogStatistical'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Tra cứu log CSDLDC'
                        },
                        {
                            languageId: 46,
                            name: 'Look up dossier log'
                        }
                    ],
                    route: 'statistics/log-search-csdldc-qbh',
                    permission: [
                        {
                            code: 'QBHlogcsdldc'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Quản lý báo cáo thống kê'
                        },
                        {
                            languageId: 46,
                            name: 'Administration statistics report'
                        }
                    ],
                    route: 'statistics/administration-report',
                    permission: [
                        {
                            code: 'oneGateReport'
                        },
                        {
                            code: 'oneGateHCMAdmin'
                        },
                        {
                            code: 'oneGateHCMLogbookStatistics'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Thống kê hồ sơ trực tuyến mức độ 3, 4'
                        },
                        {
                            languageId: 46,
                            name: 'Online profile statistics level 3, 4'
                        }
                    ],
                    route: 'statistics/statistic-level-3-4',
                    permission: [
                        {
                            code: 'oneGateReport'
                        }/*,
                        {
                            code: 'oneGateReportVpcpOnlineQNI'  // HUUMINH - COMMENT LAI -> dung chung quyen: Báo cáo ứng dụng Dịch vụ công trực tuyến-QNI  (Đạt BA)
                        }*/
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Thống kê hồ sơ theo mức độ thủ tục'
                        },
                        {
                            languageId: 46,
                            name: 'Online profile statistics level'
                        }
                    ],
                    route: 'statistics/statistic-level-hpg',
                    permission: [
                        {
                            code: 'oneGateReportLevelHpgOnline'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Thống kê theo thông tư 01/2020/TT-VPCP'
                        },
                        {
                            languageId: 46,
                            name: 'Circular 01/2020/TT-VPCP statistics'
                        }
                    ],
                    route: 'statistics/statistic-01-2020',
                    permission: [
                        {
                            code: 'oneGateReport'
                        },
                        {
                            code: 'oneGate012020Statistical'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Thống kê hồ sơ thanh toán trực tuyến cho Đồng Nai'
                        },
                        {
                            languageId: 46,
                            name: 'DNI Online Payment Statistics'
                        }
                    ],
                    route: 'statistics/dni-bc-04',
                    permission: [
                        {
                            code: 'oneGateDNI04Statistical'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: '[VPC]Thống kê theo thông tư 01/2020/TT-VPCP'
                        },
                        {
                            languageId: 46,
                            name: '[VPC]Circular 01/2020/TT-VPCP statistics'
                        }
                    ],
                    route: 'statistics/statistic-01-2020-vpc',
                    permission: [
                        {
                            code: 'oneGate012020StatisticalVpc'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: '[VPC]Thống kê hồ sơ theo mẫu 6g (TT02/2017)'
                        },
                        {
                            languageId: 46,
                            name: '[VPC]Circular 6g TT02/2017 statistic'
                        }
                    ],
                    route: 'statistics/statistic-nq17',
                    permission: [
                        {
                            code: 'oneGateStatisticNq17Vpc'
                        }
                    ]
                },
                //IGATESUPP-71093 Điều chỉnh cách tính quá hạn hồ sơ toàn trình theo ngày ở giao diện Thống kê theo thông tư 01/2020/TT-VPCP
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Thống kê theo thông tư 01/2020/TT-VPCP'
                        },
                        {
                            languageId: 46,
                            name: 'Circular 01/2020/TT-VPCP statistics'
                        }
                    ],
                    route: 'statistics/statistic-01-2020-day',
                    permission: [
                        {
                            code: 'oneGateReport'
                        },
                        {
                            code: 'oneGate012020StatisticalDay'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: '[AGG] Thống kê theo thông tư 01/2020/TT-VPCP'
                        },
                        {
                            languageId: 46,
                            name: '[AGG] Circular 01/2020/TT-VPCP statistics'
                        }
                    ],
                    route: 'statistics/statistic-01-2020-agg',
                    permission: [
                        {
                            code: 'oneGate012020StatisticalAgg'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: ' DNI - Báo cáo 6a, 6b, 6c'
                        },
                        {
                            languageId: 46,
                            name: 'DNI Circular 01/2020/TT-VPCP 6a, 6b, 6c statistics'
                        }
                    ],
                    route: 'statistics/dni-bc-6abc',
                    permission: [
                        {
                            code: 'oneGateReport'
                        },
                        {
                            code: 'oneGateDNISixABCStatistical'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Thống kê theo biểu mẫu số 01'
                        },
                        {
                            languageId: 46,
                            name: 'DNI Circular 01 statistics'
                        }
                    ],
                    route: 'statistics/dni-bc-01',
                    permission: [
                        {
                            code: 'oneGateReport'
                        },
                        {
                            code: 'oneGateDNI01Statistical'
                        }
                    ]
                },
                {
                    title: [
                        { languageId: 228, name: 'Thống kê kết quả số hóa hồ sơ cho Đồng Nai' },
                        { languageId: 46, name: 'Statistics on Result of Digitization in DongNai' }
                    ],
                    route: 'statistics/dni-digitization-dossier-report',
                    permission: [
                        { code: 'oneGateDNI05Statistical' }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Thống kê hồ sơ dừng xử lý'
                        },
                        {
                            languageId: 46,
                            name: 'DNI Circular 01 statistics'
                        }
                    ],
                    route: 'statistics/dni-bc-09',
                    permission: [
                        {
                            code: 'oneGateDNI09Statistical'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Thống kê số lượng hồ sơ trực tuyến chậm tiếp nhận, xử lý'
                        },
                        {
                            languageId: 46,
                            name: 'DNI Slow accepted dossier statistics'
                        }
                    ],
                    route: 'statistics/dni-bc-slow-accepted',
                    permission: [
                        {
                            code: 'oneGateDNILateAtAcceptanceStatistical'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Thống kê theo biểu mẫu số 03'
                        },
                        {
                            languageId: 46,
                            name: 'DNI Circular 03 statistics'
                        }
                    ],
                    route: 'statistics/dni-bc-03',
                    permission: [
                        {
                            code: 'oneGateDNI03Statistical'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: '[AGG] Thống kê cấu hình quy trình thủ tục'
                        },
                        {
                            languageId: 46,
                            name: '[AGG] Procedure Process Definition Statistics'
                        }
                    ],
                    route: 'statistics/agg-config-process-definition-procedure',
                    permission: [
                        {
                            code: 'oneGateAggConfigProcedureProcessDefinition'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: '[AGG] Thống kê báo cáo số hóa v2'
                        },
                        {
                            languageId: 46,
                            name: '[AGG] Digitized Statistics Report v2'
                        }
                    ],
                    route: 'statistics/agg-digitization-report-v2',
                    permission: [
                        {
                            code: 'oneGateAggDigitizationReportV2'
                        }
                    ]
                },
                {
                  title: [
                      {
                          languageId: 228,
                          name: 'Thống kê hồ sơ VPUB xử lý'
                      },
                      {
                          languageId: 46,
                          name: 'Circular VPUB statistics'
                      }
                  ],
                  route: 'statistics/statistic-VPUB',
                  permission: [
                      {
                          code: 'oneGateReport'
                      },
                      {
                          code: 'oneGateVPUBStatistical'
                      }
                  ]
              },
              {
                title: [
                    {
                        languageId: 228,
                        name: 'Thời gian xử lý toàn quy trình'
                    },
                    {
                        languageId: 46,
                        name: 'Processing time for the entire process'
                    }
                ],
                route: 'statistics/statistic-XLTQT',
                permission: [
                    {
                        code: 'oneGateXLTQTStatistical'
                    }
                ]
            },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Thống kê tổng hợp xử lý hồ sơ'
                        },
                        {
                            languageId: 46,
                            name: 'Summary Dossier statistics'
                        }
                    ],
                    route: 'statistics/statistic-dossier-agency-kgg',
                    permission: [
                        {
                            code: 'oneGateReport'
                        },
                        {
                            code: 'statisticalAgencyDossierKGG'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Thống kê tổng hợp xử lý hồ sơ v2'
                        },
                        {
                            languageId: 46,
                            name: 'Summary Dossier statistics v2'
                        }
                    ],
                    route: 'statistics/statistic-dossier-agency-kgg-v2',
                    permission: [
                        {
                            code: 'statisticalAgencyDossierHCMV2'
                        },
                        {
                            code: 'oneGateStatisticAgencyHcmV2ES'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Thống kê tổng hợp xử lý hồ sơ v3'
                        },
                        {
                            languageId: 46,
                            name: 'Summary Dossier statistics v3'
                        }
                    ],
                    route: 'statistics/statistic-dossier-agency-hcm-v3',
                    permission: [
                        {
                            code: 'oneGateStatisticalSyntheticDossierV3'
                        },
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Thống kê tổng hợp xử lý hồ sơ v4'
                        },
                        {
                            languageId: 46,
                            name: 'Summary Dossier statistics v4'
                        }
                    ],
                    route: 'statistics/statistic-dossier-agency-hcm-v4',
                    permission: [
                        {
                            code: 'statisticalXLHSV4'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Thống kê hồ sơ thanh toán không tiền mặt'
                        },
                        {
                            languageId: 46,
                            name: 'No cash dossier statistics'
                        }
                    ],
                    route: 'statistics/statisticsDossierNoCash',
                    permission: [
                        // {
                        //     code: 'showStatisticsDossierNoCash'
                        // },
                        {
                            code: 'showStatisticsDossierNoCash'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Thống kê tiến độ giải quyết hồ sơ'
                        },
                        {
                            languageId: 46,
                            name: 'Summary Dossier statistics'
                        }
                    ],
                    route: 'statistics/statistic-dossier-resolution-progress-v3',
                    permission: [
                        {
                            code: 'statisticalAgencyDossierKGGV3'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Thống kê tổng hợp số hóa hồ sơ'
                        },
                        {
                            languageId: 46,
                            name: 'General statistics and digitization of records'
                        }
                    ],
                    route: 'statistics/statistic-THSHHS-hpg',
                    permission: [
                        {
                            code: 'statisticTHSHHSHPG'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Thống kê tổng hợp số hóa hồ sơ'
                        },
                        {
                            languageId: 46,
                            name: 'General statistics and digitization of records'
                        }
                    ],
                    route: 'statistics/statistic-THSHHS',
                    permission: [
                        {
                            code: 'statisticTHSHHS'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Thống kê hồ sơ tái sử dụng'
                        },
                        {
                            languageId: 46,
                            name: 'Reused statistics'
                        }
                    ],
                    route: 'statistics/statistic-reuse',
                    permission: [
                        {
                            code: 'showStatisticsOfReuseRecords'
                        }
                    ]
                },
                {
                  title: [
                      {
                          languageId: 228,
                          name: 'Thống kê tổng hợp lĩnh vực (Theo thông tư 01/2020/TT-VPCP)'
                      },
                      {
                          languageId: 46,
                          name: 'Summary Dossier statistics'
                      }
                  ],
                  route: 'statistics/statistic-dossier-sector-kgg',
                  permission: [
                      {
                          code: 'statisticalSectorDossierKGG'
                      },
                      {
                          code: 'statisticalAgencyDossierKGG'
                      }
                  ]
                },
                {
                  title: [
                    {
                      languageId: 228,
                      name: 'Thống kê báo cáo tổng hợp xử lý hồ sơ cho DVC thiết yếu'
                    },
                    {
                      languageId: 46,
                      name: 'Summary Essential Public Service Application'
                    }
                  ],
                  route: 'statistics/statistic-dossier-procedure-kgg',
                  permission: [
                    {
                      code: 'statisticalProcedureDossierKGG'
                    }
                  ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Thống kê tổng hợp xử lý hồ sơ đất đai'
                        },
                        {
                            languageId: 46,
                            name: 'Summary Dossier statistics of Land'
                        }
                    ],
                    route: 'statistics/statistic-dossier-agency-land-kgg',
                    permission: [
                        {
                            code: 'oneGateReport'
                        },
                        {
                            code: 'statisticalAgencyDossierLandKGG'
                        }
                    ]
                },
                {
                  title: [
                      {
                          languageId: 228,
                          name: 'Báo cáo hồ sơ trực tuyến'
                      },
                      {
                          languageId: 46,
                          name: 'Dossier Report Online'
                      }
                  ],
                  route: 'statistics/kgg-dossier-report-online',
                  permission: [
                    {
                      code: 'oneGateDossierOnlineKGGReport'
                    }
                  ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Thống kê theo thông tư 01/2020/TT-VPCP'
                        },
                        {
                            languageId: 46,
                            name: 'Circular 01/2020/TT-VPCP statistics'
                        }
                    ],
                    route: 'statistics/circulars-01-2020',
                    permission: [
                        {
                            code: 'oneGateReport'
                        },
                        {
                            code: 'oneGate012020StatisticalOld'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Biểu số II.06b/VPCP/KSTT'
                        },
                        {
                            languageId: 46,
                            name: 'II.06b/VPCP/KSTT Statistics'
                        }
                    ],
                    route: 'statistics/statistic-ii06b',
                    permission: [
                        {
                            code: 'oneGate012020StatisticalII06b'
                        }
                    ]
                },
              {
                    title: [
                        {
                            languageId: 228,
                            name: 'Thống kê theo thông tư 01/2020/TT-VPCP/6c'
                        },
                        {
                            languageId: 46,
                            name: 'Circular 01/2020/TT-VPCP/6c statistics'
                        }
                    ],
                    route: 'statistics/statistic-01-2020-6c',
                    permission: [
                        {
                            code: 'oneGateReport'
                        },
                        {
                            code: 'oneGate0120206cStatistical'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Thống kê theo thông tư 01/2020/TT-VPCP/06 - Tỉnh Hà Giang'
                        },
                        {
                            languageId: 46,
                            name: 'Circular 01/2020/TT-VPCP/6c statistics'
                        }
                    ],
                    route: 'statistics/statistic-01-2020-06-hgg',
                    permission: [
                        {
                            code: 'oneGateReport'
                        },
                        {
                            code: 'oneGate0120206cHGGStatistical'
                        }
                    ]
                },
                {
                  title: [
                      {
                          languageId: 228,
                          name: '[VPC] Thống kê theo lĩnh vực'
                      },
                      {
                          languageId: 46,
                          name: '[VPC] Circular linh vuc statistics'
                      }
                  ],
                  route: 'statistics/statistic-bc-lv-vpc',
                  permission: [
                      {
                          code: 'oneGateReport'
                      },
                      {
                          code: 'oneGateLVVPCStatistical'
                      }
                  ]
              },
              {
                title: [
                    {
                        languageId: 228,
                        name: 'Báo cáo thống kê hồ sơ theo đối tượng nộp hồ sơ'
                    },
                    {
                        languageId: 46,
                        name: 'Báo cáo thống kê hồ sơ theo đối tượng nộp hồ sơ'
                    }
                ],
                route: 'statistics/statistic-applicant-vpc',
                permission: [
                    {
                        code: 'oneGateReport'
                    },
                    {
                        code: 'oneGateApplicantVPCStatistical'
                    }
                ]
              },
              {
                title: [
                  {
                    languageId: 228,
                    name: 'Thống kê hồ sơ có văn bản xin lỗi'
                  },
                  {
                    languageId: 46,
                    name: 'Statistics records with written apology'
                  }
                ],
                route: 'statistics/statistic-bc-xl-vpc',
                permission: [
                    {
                        code: 'oneGateReport'
                    },
                    {
                        code: 'oneGateXlVpcStatistical'
                    }
                ]
            },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Thống kê theo thông tư 01/2020/TT-VPCP/6c - Tổng hơp'
                        },
                        {
                            languageId: 46,
                            name: 'Circular 01/2020/TT-VPCP/6c composition statistics'
                        }
                    ],
                    route: 'statistics/statistic-01-2020-6c-tonghop',
                    permission: [
                        {
                            code: 'oneGateReport'
                        },
                        {
                            code: 'oneGate0120206cTonghopStatistical'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'QTI- Báo cáo thống kê toàn tỉnh'
                        },
                        {
                            languageId: 46,
                            name: 'Circular 01/2020/TT-VPCP statistics'
                        }
                    ],
                    route: 'statistics/qti-bc-toantinh',
                    permission: [
                        {
                            code: 'oneGateReport'
                        },
                        {
                            code: 'oneGateQtiStatistical'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'QTI- Báo cáo Hồ sơ tiếp nhận, trả kết quả qua BCCI'
                        },
                        {
                            languageId: 46,
                            name: 'Circular 01/2020/TT-VPCP statistics'
                        }
                    ],
                    route: 'statistics/qti-bc-hoso-bcci',
                    permission: [
                        {
                            code: 'oneGateReport'
                        },
                        {
                            code: 'oneGateReportBCVB16'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'QTI-kết quả xin lỗi, đề nghị gia hạn hồ sơ trễ hẹn'
                        },
                        {
                            languageId: 46,
                            name: 'Circular 01/2020/TT-VPCP statistics'
                        }
                    ],
                    route: 'statistics/qti-bc-ketqua-xinloi-giahan',
                    permission: [
                        {
                            code: 'oneGateReport'
                        },
                        {
                            code: 'oneGateQtiStatistical'
                        }
                    ]
                },

                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Kết quả giải quyết TTHC địa bàn Huyện'
                        },
                        {
                            languageId: 46,
                            name: 'Circular 01/2020/TT-VPCP statistics'
                        }
                    ],
                    route: 'statistics/qti-bc-xuly-hoso',
                    permission: [
                        {
                            code: 'oneGateReport'
                        },
                        {
                            code: 'oneGateQtiStatistical'
                        }
                    ]
                },

                {
                    title: [
                        {
                            languageId: 228,
                            name: 'QTI-Tỷ lệ giải quyết đúng hạn của 3 cấp Tỉnh, Huyện, Xã'
                        },
                        {
                            languageId: 46,
                            name: 'Circular 01/2020/TT-VPCP statistics'
                        }
                    ],
                    route: 'statistics/qti-bc-tthc-theocap',
                    permission: [
                        {
                            code: 'oneGateTTHCTHEOCAP'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'QTI-Báo cáo 6a, 6b, 6c'
                        },
                        {
                            languageId: 46,
                            name: 'Circular 01/2020/TT-VPCP statistics'
                        }
                    ],
                    route: 'statistics/qti-bc-6abc',
                    permission: [
                        {
                            code: 'oneGateReport'
                        },
                        {
                            code: 'oneGateQtiStatistical'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'QTI-Thống kê lịch sử tra cứu, khai thác CSDL quốc gia về Dân cư theo cơ quan, đơn vị, địa phương'
                        },
                        {
                            languageId: 46,
                            name: 'Circular 01/2020/TT-VPCP statistics'
                        }
                    ],
                    route: 'statistics/qti-bc-csdl-quocgia-dancu',
                    permission: [
                        {
                            code: 'oneGateReportBCCSDLDC'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Tỷ lệ TTHC có phát sinh giao dịch thanh toán trực tuyến'
                        },
                        {
                            languageId: 46,
                            name: 'Summary Dossier statistics'
                        }
                    ],
                    route: 'statistics/qti-bc-tl-tthc',
                    permission: [

                        {
                            code: 'oneGateBCPSTTTT'
                        }
                    ]
                  },
                  {
                    title: [
                        {
                            languageId: 228,
                            name: 'Báo cáo 6a,6b,6c'
                        },
                        {
                            languageId: 46,
                            name: 'Summary Dossier statistics'
                        }
                    ],
                    route: 'statistics/qti-bc-6abc',
                    permission: [
                        {
                            code: 'oneGateReport'
                        },
                        {
                            code: 'oneGateBCTHCCTTTT'
                        }
                    ]
                  },
                  {
                    title: [
                        {
                            languageId: 228,
                            name: 'QTI-Báo cáo danh sách hồ sơ tiếp nhận từ cổng DVCQG'
                        },
                        {
                            languageId: 46,
                            name: 'Summary Dossier statistics'
                        }
                    ],
                    route: 'statistics/qti-bc-hstndvcqg',
                    permission: [
                        {
                            code: 'oneGateReport'
                        },
                        {
                            code: 'oneGateQtiStatistical'
                        }
                    ]
                  },
                  {
                    title: [
                        {
                            languageId: 228,
                            name: 'Báo cáo tình hình thanh toán cổng QG'
                        },
                        {
                            languageId: 46,
                            name: 'Summary Dossier statistics'
                        }
                    ],
                    route: 'statistics/qti-bc-thanhtoan-congqg',
                    permission: [
                        {
                            code: 'oneGateBCTHCCTTTT'
                        }
                    ]
                  },
                  {
                    title: [
                        {
                            languageId: 228,
                            name: 'QTI Thống kê danh sách dịch vụ công trực tuyến (Trung tâm PV HCC)'
                        },
                        {
                            languageId: 46,
                            name: 'QTI TTPVKSTTHC Report'
                        }
                    ],
                    route: 'statistics/qti-bcdvc',
                    permission: [
                        {
                            code: 'oneGateReportTTPVKSTTHCQTI'
                        }
                    ]

                  },
                  {
                    title: [
                        {
                            languageId: 228,
                            name: 'Thống kê tài khoản thụ hưởng'
                        },
                        {
                            languageId: 46,
                            name: 'General statistics and digitization of records'
                        }
                    ],
                    route: 'statistics/statistic-BCTKTKHT-qti',
                    permission: [
                        {
                            code: 'oneGateReport'
                        },
                        {
                            code: 'oneGateBCTKTKHT'
                        }
                    ]
                },
                  {
                    title: [
                        {
                            languageId: 228,
                            name: 'Thống kê tổng hợp số hóa hồ sơ'
                        },
                        {
                            languageId: 46,
                            name: 'General statistics and digitization of records'
                        }
                    ],
                    route: 'statistics/statistic-THSHHS-qti',
                    permission: [
                        {
                            code: 'statisticTHSHHSQTI'
                        }
                    ]
                },

                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Tỷ lệ TTHC được triển khai thanh toán trực tuyến'
                        },
                        {
                            languageId: 46,
                            name: 'General statistics and digitization of records'
                        }
                    ],
                    route: 'statistics/qti-bc-tttt',
                    permission: [

                        {
                            code: 'oneGateBCTTTT'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Báo cáo thống kê phí, lệ phí thủ tục hành chính'
                        },
                        {
                            languageId: 46,
                            name: 'General statistics and digitization of records'
                        }
                    ],
                    route: 'statistics/bc-thongke-phi-TTHC-qti',
                    permission: [
                        {
                            code: 'oneGateQTITKPHILEPHI'
                        }
                    ]
                },

                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Trích xuất số liệu cập nhật lên hệ thống EMC'
                        },
                        {
                            languageId: 46,
                            name: 'General statistics and digitization of records'
                        }
                    ],
                    route: 'statistics/qti-trich-xuat-emc',
                    permission: [
                        {
                            code: 'TSSOLIEUEMC'
                        }
                    ]
                },


                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Thống kê theo thông tư 01/2018/TT-VPCP'
                        },
                        {
                            languageId: 46,
                            name: 'Circular 01/2018/TT-VPCP statistics'
                        }
                    ],
                    route: 'statistics/circulars-01-2018',
                    permission: [
                        {
                            code: 'oneGateReport'
                        },
                        {
                            code: 'oneGate012018StatisticalOld'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Thống kê theo thông tư 01/2018/TT-VPCP'
                        },
                        {
                            languageId: 46,
                            name: 'Circular 01/2018/TT-VPCP statistics'
                        }
                    ],
                    route: 'statistics/statistic-01-2018',
                    permission: [
                        {
                            code: 'oneGateReport'
                        },
                        {
                            code: 'oneGate012018Statistical'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Thống kê theo thông tư 02/2017/TT-VPCP'
                        },
                        {
                            languageId: 46,
                            name: 'Circular 02/2017/TT-VPCP statistics'
                        }
                    ],
                    route: 'statistics/circulars-02-2017',
                    permission: [
                        {
                            code: 'oneGateReport'
                        },
                        {
                            code: 'oneGate022017StatisticalOld'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Thống kê theo thông tư 02/2017/TT-VPCP'
                        },
                        {
                            languageId: 46,
                            name: 'Circular 02/2017/TT-VPCP statistics'
                        }
                    ],
                    route: 'statistics/statistic-02-2017',
                    permission: [
                        {
                            code: 'oneGateReport'
                        },
                        {
                            code: 'oneGate022017Statistical'
                        }
                    ]
                },
                { //IGATESUPP-71095: Thống kê theo thông tư 02/2017/TT-VPCP trên kiến trúc ElasticSearch.
                    title: [
                        {
                            languageId: 228,
                            name: 'Thống kê theo thông tư 02/2017/TT-VPCP'
                        },
                        {
                            languageId: 46,
                            name: 'Circular 02/2017/TT-VPCP statistics'
                        }
                    ],
                    route: 'statistics/statistic-02-2017-day',
                    permission: [
                        {
                            code: 'oneGateReport'
                        },
                        {
                            code: 'oneGate022017StatisticalDay'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Thống kê báo cáo Sở nội vụ'
                        },
                        {
                            languageId: 46,
                            name: 'Statistics DHA'
                        }
                    ],
                    route: 'statistics/statistic-02-2017-snv',
                    permission: [
                        {
                            code: 'oneGateReport'
                        },
                        {
                            code: 'oneGate022017Statistical'
                        },
                        {
                            code: 'oneGate022017StatisticalES'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Thống kê theo thủ tục hành chính'
                        },
                        {
                            languageId: 46,
                            name: 'Statistics CMU'
                        }
                    ],
                    route: 'statistics/statistic-02-2017-snv-cmu',
                    permission: [
                        {
                            code: 'oneGateReport'
                        },
                        {
                            code: 'oneGate022017StatisticalCMU'
                        },
                        {
                            code: 'oneGate022017StatisticalCMUES'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Thống kê báo cáo Sở Tài Nguyên Môi Trường'
                        },
                        {
                            languageId: 46,
                            name: 'Statistics ERB'
                        }
                    ],
                    route: 'statistics/statistic-01-2020-stnmt',
                    permission: [
                        {
                            code: 'oneGateReport'
                        },
                        {
                            code: 'oneGate012020StatisticalSTNMT'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Thống kê theo thông tư 02/2020/TT-VPC KHA'
                        },
                        {
                            languageId: 46,
                            name: 'Circular 02/2017/TT-VPCP statistics - KHA'
                        }
                    ],
                    route: 'statistics/statistic-02-2017-kha',
                    permission: [
                        {
                            code: 'oneGate022017KHAStatistical'
                        }
                    ]
                },
                { //quocpa-IGATESUPP-38153- 09/02/2023
                    title: [
                        {
                            languageId: 228,
                            name: 'Thống kê theo thông tư 01/2020/TT-VPCP HCM'
                        },
                        {
                            languageId: 46,
                            name: 'Circular 01/2020/TT-VPCP HCM Statistics'
                        }
                    ],
                    route: 'statistics/statistic-01-2020-hcm',
                    permission: [
                        {
                            code: 'oneGateReport'
                        },
                        {
                            code: 'oneGate012020StatisticalHCM'
                        }
                    ]
                },
                { //khanhpn-IGATESUPP-72752 - 21/02/2024
                    title: [
                        {
                            languageId: 228,
                            name: 'Thống kê theo thông tư 01/2020/TT-VPCP HCM'
                        },
                        {
                            languageId: 46,
                            name: 'Circular 01/2020/TT-VPCP HCM Statistics'
                        }
                    ],
                    route: 'statistics/statistic-01-2020-hcm-day',
                    permission: [
                        {
                            code: 'oneGateReport'
                        },
                        {
                            code: 'oneGate012020StatisticalHCMDay'
                        }
                    ]
                },
                {
                  title: [
                    {
                      languageId: 228,
                      name: this.deploymentService.env?.OS_QNI?.titleGeneralReport === "" ? this.deploymentService.env?.OS_HCM?.addMenuHCM.vi
                                                                      : this.deploymentService.env?.OS_QNI?.titleGeneralReport
                    },
                    {
                      languageId: 46,
                      name: this.deploymentService.env?.OS_QNI?.titleGeneralReport === "" ?this.deploymentService.env?.OS_HCM?.addMenuHCM.en
                                      : this.deploymentService.env?.OS_QNI?.titleGeneralReport
                    }
                  ],
                  route: 'statistics/statistic-general',
                  permission: [
                    {
                        code: 'oneGateReport'
                    },
                    {
                      code: 'oneGateAllStatistical_QNI'
                    },
                    {
                      code: 'oneGateReportAgency'
                    },
                    // {
                    //   code: 'oneGateGeneralReport'
                    // }
                  ]
                },
                {
                    title: [
                      {
                        languageId: 228,
                        name: this.deploymentService.env?.OS_QNI?.titleGeneralReport === "" ? this.deploymentService.env?.OS_HCM?.addMenuHCM.vi
                                                                        : this.deploymentService.env?.OS_QNI?.titleGeneralReport
                      },
                      {
                        languageId: 46,
                        name: this.deploymentService.env?.OS_QNI?.titleGeneralReport === "" ?this.deploymentService.env?.OS_HCM?.addMenuHCM.en
                                        : this.deploymentService.env?.OS_QNI?.titleGeneralReport
                      }
                    ],
                    route: 'statistics/statistic-general-day',
                    permission: [
                      {
                          code: 'oneGateReport'
                      },
                      // {
                      //   code: 'oneGateAllStatistical_QNI'
                      // },
                      // {
                      //   code: 'oneGateReportAgency'
                      // },
                      {
                          code: 'oneGateAllStatisticalDay'
                      },
                      // {
                      //   code: 'oneGateGeneralReport'
                      // }
                    ]
                  },
                  {
                    title: [
                        {
                            languageId: 228,
                            name: "HPG Báo cáo chung"
                        },
                        {
                            languageId: 46,
                            name: "HPG Statistic General"
                        },
                    ],
                    route: 'statistics/statistic-general-hpg',
                    permission: [
                        {
                            code: 'oneGateAllStatistical_HPG'
                        }
                    ]
                },
                // {
                //     title: [
                //         {
                //             languageId: 228,
                //             name: "QBH Báo cáo chung"
                //         },
                //         {
                //             languageId: 46,
                //             name: "QBH Statistic General"
                //         },
                //     ],
                //     route: 'statistics/statistic-general-qbh',
                //     permission: [
                //         {
                //             code: 'oneGateGeneralReportQbh'
                //         }
                //     ]
                // },
                {
                    title: [
                        {
                            languageId: 228,
                            name: "Báo cáo chung"
                        },
                        {
                            languageId: 46,
                            name: "QBH Statistic General"
                        },
                    ],
                    route: 'statistics/statistic-general-qbh2',
                    permission: [
                        {
                            code: 'oneGateGeneralReportQbh'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Thống kê theo cơ quan HCM'
                        },
                        {
                            languageId: 46,
                            name: 'Statistics By Agency HCM'
                        }
                    ],
                    route: 'statistics/statistic-general-hcm',
                    permission: [
                        {
                            code: 'oneGateReportAgencyHCM'
                        },
                        {
                            code: 'newStatisticGeneralHCM'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Thống kê hồ sơ theo cơ quan HCM'
                        },
                        {
                            languageId: 46,
                            name: 'Statistics By Agency HCM'
                        }
                    ],
                    route: 'statistics/statistic-general-hcm-day',
                    permission: [
                        {
                            code: 'oneGateReportAgencyHCMDay'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Thống kê hồ sơ theo cơ quan HCM V2'
                        },
                        {
                            languageId: 46,
                            name: 'Statistics By Agency HCM V2'
                        }
                    ],
                    route: 'statistics/statistic-general-hcm-v2',
                    permission: [
                        {
                            code: 'statisticGeneralHCMV2'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Thống kê theo cơ quan SCT'
                        },
                        {
                            languageId: 46,
                            name: 'Statistics By Agency SCT'
                        }
                    ],
                    route: 'statistics/statistic-general-sct',
                    permission: [
                        {
                            code: 'newStatisticGeneralSCT'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Thống kê chung'
                        },
                        {
                            languageId: 46,
                            name: 'General Statistics'
                        }
                    ],
                    route: 'statistics/statistic-all',
                    permission: [
                        {
                            code: 'oneGateReport'
                        },
                        {
                            code: 'oneGateAllStatistical'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Thống kê hồ sơ Lý lịch tư pháp VNeID'
                        },
                        {
                            languageId: 46,
                            name: 'Statistics of VNeID judicial records'
                        }
                    ],
                    route: 'statistics/statistic-vneid',
                    permission: [
                        {
                            code: 'onegateStaticsLLTPVNeIDHCM'
                        }
                    ]
                },
                {
                  title: [
                    {
                      languageId: 228,
                      name: 'Thống kê hồ sơ qua BCCI'
                    },
                    {
                      languageId: 46,
                      name: 'General Statistics'
                    }
                  ],
                  route: 'statistics/statistic-vnpost-report',
                  permission: [
                    {
                        code: 'oneGateReport'
                    },
                    {
                      code: 'oneGateAllVnpostReportDBN'
                    }
                  ]
                },
                {
                    title: [
                      {
                        languageId: 228,
                        name: 'Thống kê hồ sơ tiếp nhận, trả kết quả BCCI'
                      },
                      {
                        languageId: 46,
                        name: 'General Statistics'
                      }
                    ],
                    route: 'statistics/thong-ke-ho-so-bcci',
                    permission: [
                      {
                        code: 'oneGateBCCIList'
                      }
                    ]
                },
                {
                  title: [
                    {
                      languageId: 228,
                      name: 'Thống kê chung đơn vị'
                    },
                    {
                      languageId: 46,
                      name: 'General Statistics'
                    }
                  ],
                  route: 'statistics/statistic-all-dbn',
                  permission: [
                    {
                        code: 'oneGateReport'
                    },
                    {
                      code: 'oneGateAllStatisticalDBN'
                    }
                  ]
                },
                {
                    title: [
                      {
                        languageId: 228,
                        name: 'PTO Thống kê chung đơn vị'
                      },
                      {
                        languageId: 46,
                        name: 'General Statistics PTO'
                      }
                    ],
                    route: 'statistics/statistic-all-hbh',
                    permission: [
                      {
                        code: 'oneGateAllStatisticalHBH'
                      },
                      {
                        code: 'oneGateRepotHBH'
                      }
                    ]
                },
                {
                    title: [
                      {
                        languageId: 228,
                        name: 'PTO Thống kê theo TTHC'
                      },
                      {
                        languageId: 46,
                        name: 'General Statistics By Process PTO'
                      }
                    ],
                    route: 'statistics/statistic-procedure-hbh',
                    permission: [
                      {
                        code: 'oneGateProcedureStatisticalHBH'
                      },
                      {
                        code: 'oneGateRepotHBH'
                      }
                    ]
                },
                {
                    title: [
                      {
                        languageId: 228,
                        name: 'PTO Thống kê theo lĩnh vực'
                      },
                      {
                        languageId: 46,
                        name: 'General Statistics By Sector PTO'
                      }
                    ],
                    route: 'statistics/statistic-sector-hbh',
                    permission: [
                      {
                        code: 'oneGateSectorStatisticalHBH'
                      },
                      {
                        code: 'oneGateRepotHBH'
                      }
                    ]
                },
                {
                    title: [
                      {
                        languageId: 228,
                        name: 'PTO Thống kê hồ sơ theo mức độ'
                      },
                      {
                        languageId: 46,
                        name: 'General Statistics PTO'
                      }
                    ],
                    route: 'statistics/statistic-level-hbh',
                    permission: [
                      {
                          code: 'oneGateStatisticalLevelHBH'
                      }
                    ]
                },
                {
                    title: [
                      {
                        languageId: 228,
                        name: 'PTO Thống kê hồ sơ chủ tài khoản không phải chủ hồ sơ'
                      },
                      {
                        languageId: 46,
                        name: 'General Statistics PTO'
                      }
                    ],
                    route: 'statistics/statistic-own-hbh',
                    permission: [
                      {
                          code: 'oneGateStatisticOwn'
                      }
                    ]
                },
                {
                  title: [
                    {
                      languageId: 228,
                      name: 'Thống kê hồ sơ liên thông'
                    },
                    {
                      languageId: 46,
                      name: 'Connection Dossier Statistics'
                    }
                  ],
                  route: 'statistics/statistic-connection-dossier',
                  permission: [
                    {
                        code: 'oneGateReport'
                    },
                    {
                       code: 'oneGateStatisticConnectionDBN'
                    }
                  ]
                },
                {
                  title: [
                    {
                      languageId: 228,
                      name: 'Thống kê theo TTHC'
                    },
                    {
                      languageId: 46,
                      name: 'General Statistics'
                    }
                  ],
                  route: 'statistics/statistic-procedure-dbn',
                  permission: [
                    {
                        code: 'oneGateReport'
                    },
                    {
                      code: 'oneGateAllStatisticalDBN'
                    },
                    {
                        code: 'oneGateAllStatisticalHBH'
                    }
                  ]
                },
                {
                  title: [
                    {
                      languageId: 228,
                      name: 'Thống kê theo lĩnh vực'
                    },
                    {
                      languageId: 46,
                      name: 'General Statistics'
                    }
                  ],
                  route: 'statistics/statistic-sector-dbn',
                  permission: [
                    {
                        code: 'oneGateReport'
                    },
                    {
                      code: 'oneGateAllStatisticalDBN'
                    },
                    {
                        code: 'oneGateAllStatisticalHBH'
                    }
                  ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Báo cáo tổng hợp'
                        },
                        {
                            languageId: 46,
                            name: 'General Report'
                        }
                    ],
                    route: 'statistics/circulars-2020',
                    permission: [
                        {
                            code: 'oneGateReport'
                        },
                        {
                            code: 'oneGate2020Statistical'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Báo cáo tổng hợp (mới)'
                        },
                        {
                            languageId: 46,
                            name: 'General Report'
                        }
                    ],
                    route: 'statistics/circulars-2020-v2',
                    permission: [
                        {
                            code: 'oneGateReportv2'
                        },
                        {
                            code: 'oneGate2020v2Statistical'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Báo cáo tổng hợp (Đất Đai)'
                        },
                        {
                            languageId: 46,
                            name: 'DONRE Report'
                        }
                    ],
                    route: 'statistics/kontum-DONRE-report',
                    permission: [
                        {
                            code: 'oneGateReport'
                        },
                        {
                            code: 'oneGateKonTumDONREReport'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Báo cáo theo Lĩnh vực'
                        },
                        {
                            languageId: 46,
                            name: 'Dossier By Sector Report'
                        }
                    ],
                    route: 'statistics/kontum-dossier-sector-report',
                    permission: [
                        {
                            code: 'oneGateReport'
                        },
                        {
                            code: 'oneGateKonTumDossierSectorReport'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Báo cáo theo Lĩnh vực (mới)'
                        },
                        {
                            languageId: 46,
                            name: 'Dossier By Sector Report (new)'
                        }
                    ],
                    route: 'statistics/kontum-dossier-sector-report-v2',
                    permission: [
                        {
                            code: 'oneGateKonTumDossierSectorReportV2'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Báo cáo chi tiết'
                        },
                        {
                            languageId: 46,
                            name: 'List Dossier Report'
                        }
                    ],
                    route: 'statistics/kontum-list-dossier-report',
                    permission: [
                        {
                            code: 'oneGateReport'
                        },
                        {
                            code: 'oneGateKonTumListDossierReport'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Báo cáo chi tiết (mới)'
                        },
                        {
                            languageId: 46,
                            name: 'List Dossier Report'
                        }
                    ],
                    route: 'statistics/kontum-list-dossier-report-v2',
                    permission: [
                        {
                            code: 'oneGateReportV2'
                        },
                        {
                            code: 'oneGateKonTumListDossierReportV2'
                        }
                    ]
                },

                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Báo cáo trễ hạn Sở Tài Nguyên Môi Trường - Thuế'
                        },
                        {
                            languageId: 46,
                            name: 'Donre-Tax Report'
                        }
                    ],
                    route: 'statistics/kontum-donre-tax-report',
                    permission: [
                        {
                            code: 'oneGateReport'
                        },
                        {
                            code: 'oneGateKonTumDonreTaxReport'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Báo cáo hồ sơ liên thông VPUBND (mới)'
                        },
                        {
                            languageId: 46,
                            name: 'KTM-VPUBND Report'
                        }
                    ],
                    route: 'statistics/kontum-ubnd-report',
                    permission: [
                        {
                            code: 'oneGateReport'
                        },
                        {
                            code: 'oneGateKonTumVPUBReport'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Báo cáo tình hình số hóa hồ sơ'
                        },
                        {
                            languageId: 46,
                            name: 'Document Digitization Report'
                        }
                    ],
                    route: 'statistics/kontum-document-digitization-report',
                    permission: [
                        {
                            code: 'oneGateReport'
                        },
                        {
                            code: 'oneGateKonTumDocumentDigitizationReport'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Báo cáo thống kê DVCLT (mới)'
                        },
                        {
                            languageId: 46,
                            name: 'Report KTM DVCLT KSKT'
                        }
                    ],
                    route: 'statistics/kontum-report-dvclt-kskt',
                    permission: [
                        {
                            code: 'oneGateKonTumDVCLTKSKTReport'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Báo cáo tình hình số hóa hồ sơ (mới)'
                        },
                        {
                            languageId: 46,
                            name: 'Document Digitization Reportv2'
                        }
                    ],
                    route: 'statistics/kontum-document-digitization-reportv2',
                    permission: [
                        {
                            code: 'oneGateReportv2'
                        },
                        {
                            code: 'oneGateKonTumDocumentDigitizationReportv2'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: this.konTumListOnlinePaymentReportName
                        },
                        {
                            languageId: 46,
                            name: 'Report online payment'
                        }
                    ],
                    route: 'statistics/kontum-report-online-payment',
                    permission: [
                        {
                            code: 'oneGateReport'
                        },
                        {
                            code: 'oneGateKonTumListOnlinePaymentReport'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Thống kê lệ phí hồ sơ'
                        },
                        {
                            languageId: 46,
                            name: 'Dossier fee statistics'
                        }
                    ],
                    route: 'statistics/dossier-fee',
                    permission: [
                        {
                            code: 'oneGateReport'
                        },
                        {
                            code: 'oneGateDossierFeeReport'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Thống kê lệ phí hồ sơ BDH'
                        },
                        {
                            languageId: 46,
                            name: 'Dossier fee statistics BDH'
                        }
                    ],
                    route: 'statistics/dossier-fee-bdh',
                    permission: [
                        {
                            code: 'oneGateDossierFeeReportBDH'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Thống kê phí/lệ phí Biên lai hóa đơn BDH'
                        },
                        {
                            languageId: 46,
                            name: 'Dossier fee receipt statistics BDH'
                        }
                    ],
                    route: 'statistics/dossier-fee-receipt-bdh',
                    permission: [
                        {
                            code: 'oneGateDossierFeeReceiptReportBDH'
                        }
                    ]
                },
                {
                    title: [
                    {
                        languageId: 228,
                        name: 'PTO Thống kê chung đơn vị hồ sơ chậm tiếp nhận'
                    },
                    {
                        languageId: 46,
                        name: 'General Statistics Receive Overdue PTO'
                    }
                    ],
                    route: 'statistics/statistic-receive-overdue-hbh',
                    permission: [
                    {
                        code: 'oneGateStatisticReceiveOverdueHBH'
                    }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'PTO Thống kê lệ phí hồ sơ'
                        },
                        {
                            languageId: 46,
                            name: 'Dossier fee statistics PTO'
                        }
                    ],
                    route: 'statistics/dossier-fee-hbh',
                    permission: [
                        {
                            code: 'oneGateDossierFeeReportHBH'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Thống kê lệ phí hồ sơ'
                        },
                        {
                            languageId: 46,
                            name: 'Dossier fee statistics'
                        }
                    ],
                    route: 'statistics/dossier-fee-hpg',
                    permission: [
                        {
                            code: 'oneGateReport'
                        },
                        {
                            code: 'oneGateDossierFeeReportHPG'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Thống kê lệ phí hồ sơ Kiên Giang'
                        },
                        {
                            languageId: 46,
                            name: 'KGG Dossier fee statistics'
                        }
                    ],
                    route: 'statistics/dossier-fee-kgg',
                    permission: [
                        {
                            code: 'oneGateDossierFeeKGGReport'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: this.dossierFeeDBNReportName
                        },
                        {
                            languageId: 46,
                            name: 'DBN Dossier fee statistics'
                        }
                    ],
                    route: 'statistics/dossier-fee-dbn',
                    permission: [
                        {
                            code: 'oneGateDossierFeeDBNReport'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Thống kê lệ phí hồ sơ HCM'
                        },
                        {
                            languageId: 46,
                            name: 'HCM Dossier Fee Statistics'
                        }
                    ],
                    route: 'statistics/dossier-fee-hcm',
                    permission: [
                        {
                            code: 'oneGateReport'
                        },
                        {
                            code: 'oneGateHCMAdmin'
                        },
                        {
                            code: 'oneGateHCMDossierFeeReport'
                        }
                    ]
                },
                       //IGATESUPP-64658: Thêm chức năng thống kế lệ phí hồ sơ HGI
              {
                title: [
                    {
                        languageId: 228,
                        name: 'Thống kê lệ phí hồ sơ HGI'
                    },
                    {
                        languageId: 46,
                        name: 'HGI Dossier Fee Statistics'
                    }
                ],
                route: 'statistics/dossier-fee-hgi',
                permission: [
                    {
                        code: 'statictisDossierFeeHGI'
                    },
                    {
                        code: 'oneGateAllStatisticalHGI'
                    }

                ]
            },
            {
                title: [
                  {
                    languageId: 228,
                    name: 'Thống kê thủ tục cấu hình phí, lệ phí'
                  },
                  {
                    languageId: 46,
                    name: 'Procedure procost statistics'
                  }
                ],
                route: 'statistics/procedure-procost',
                permission: [
                  {
                    code: 'oneGateReportProcedureProcost'
                  }
                ]
              },
                {
                  title: [
                    {
                      languageId: 228,
                      name: 'Thống kê hồ sơ chưa tiếp nhận'
                    },
                    {
                      languageId: 46,
                      name: 'Unreceived dossier statistics'
                    }
                  ],
                  route: 'statistics/dossier-unreceived',
                  permission: [
                    {
                      code: 'oneGateReport'
                    },
                    {
                      code: 'oneGateOverdueDossierReport'
                    }
                  ]
                },
                {
                    title: [
                      {
                        languageId: 228,
                        name: 'Thống kê hồ sơ trực tuyến chưa tiếp nhận'
                      },
                      {
                        languageId: 46,
                        name: 'Unreceived online dossier statistics'
                      }
                    ],
                    route: 'statistics/dossier-unreceived-qni',
                    permission: [
                        {
                            code: 'oneGateOnlineUnreceivedDossierReport'
                        }
                    ]
                  },
                {
                    title: [
                      {
                        languageId: 228,
                        name: 'Thống kê hồ sơ chưa tiếp nhận HCM'
                      },
                      {
                        languageId: 46,
                        name: 'Unreceived dossier statistics HCM'
                      }
                    ],
                    route: 'statistics/dossier-unreceived-hcm',
                    permission: [
                      {
                        code: 'oneGateDossierUnreceivedHCM'
                      }
                    ]
                  },
                // {
                //   title: [
                //     {
                //       languageId: 228,
                //       name: 'Thống kê tổng hợp xử lý hồ sơ'
                //     },
                //     {
                //       languageId: 46,
                //       name: 'Summary Dossier statistics'
                //     }
                //   ],
                //   route: 'statistics/statistic-dossier-agency-kgg',
                //   permission: [
                //     {
                //       code: 'oneGateReport'
                //     },
                //     {
                //       code: 'statisticalAgencyDossierKGG'
                //     }
                //   ]
                // },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Thống kê hồ sơ trễ hạn'
                        },
                        {
                            languageId: 46,
                            name: 'Overdue dossier statistics'
                        }
                    ],
                    route: 'statistics/dossier-overdue',
                    permission: [
                        {
                            code: 'oneGateReport'
                        },
                        {
                            code: 'oneGateOverdueDossierReport'
                        },
                        {
                            code: 'oneGateAllStatisticalHGI'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Thống kê hồ sơ trễ hạn HCM'
                        },
                        {
                            languageId: 46,
                            name: 'Overdue dossier statistics HCM'
                        }
                    ],
                    route: 'statistics/dossier-overdue-hcm',
                    permission: [
                        {
                            code: 'oneGateOverdueDossierReportHCM'
                        },
                        {
                            code: 'oneGateOverdueDossierReportHCMNew'
                        }
                    ]
                },
                {
                  title: [
                      {
                          languageId: 228,
                          name: '[AGG] Thống kê hồ sơ trễ hạn'
                      },
                      {
                          languageId: 46,
                          name: '[AGG] Overdue dossier statistics'
                      }
                  ],
                  route: 'statistics/dossier-overdue-agg',
                  permission: [
                      {
                          code: 'oneGateStatisticDossierOverdueAgg'
                      }
                  ]
              },
              {
                title: [
                    {
                        languageId: 228,
                        name: 'Thống kê hồ sơ trễ hạn HCM'
                    },
                    {
                        languageId: 46,
                        name: 'Overdue dossier statistics HCM'
                    }
                ],
                route: 'statistics/dossier-overdue-hcm-day',
                permission: [
                    {
                        code: 'oneGateOverdueDossierReportHCMDay'
                    }
                ]
            },
            {
                title: [
                  {
                    languageId: 228,
                    name: '[AGG] Thống kê TTHCC mẫu 4 v2'
                  },
                  {
                    languageId: 46,
                    name: '[AGG] TTHCC statistics v4'
                  }
                ],
                route: 'statistics/public-procedure-statistics-form4-v2',
                permission: [
                  {
                    code: 'oneGateAggTkTTHCCM4V2'
                  }
                ]
            },
            {
                title: [
                  {
                    languageId: 228,
                    name: '[AGG] Báo cáo thanh toán trực tuyến V4'
                  },
                  {
                    languageId: 46,
                    name: '[AGG] Online procedure statistics v4'
                  }
                ],
                route: 'statistics/agg-online-procedure-statistics-v4',
                permission: [
                  {
                    code: 'oneGateAggTkOnlineProcedureV4'
                  }
                ]
            },
            {
              title: [
                {
                  languageId: 228,
                  name: '[AGG] Thống kê thủ tục trực tuyến có yêu cầu NVTC'
                },
                {
                  languageId: 46,
                  name: '[AGG] Statistic online procedure required NVTC '
                }
              ],
              route: 'statistics/agg-statistic-online-procedure-req-nvtc',
              permission: [
                {
                  code: 'oneGateAggOnlineProcedureReqNVTC'
                }
              ]
            },
            {
                    title: [
                        {
                            languageId: 228,
                            name: 'Thống kê hồ sơ đến hạn'
                        },
                        {
                            languageId: 46,
                            name: 'Due dossier statistics'
                        }
                    ],
                    route: 'statistics/dossier-due',
                    permission: [
                        {
                            code: 'oneGateReport'
                        },
                        {
                            code: 'oneGateDueDossierReport'
                        },
                        {
                            code: 'oneGateAllStatisticalHGI'
                        }
                    ]
                },
                {
                    title: [
                      {
                        languageId: 228,
                        name: 'Thống kê hồ sơ sắp đến hạn',
                      },
                      {
                        languageId: 46,
                        name: 'Statistic Dossier Due Soon',
                      },
                    ],
                    route: 'statistics/statistic-dossier-due-soon',
                    permission: [
                      {
                        code: 'RptStatisticsofupcomingduecasesKHAUC202',
                      },
                    ],
                  },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Thống kê khách hàng cá nhân'
                        },
                        {
                            languageId: 46,
                            name: 'Individual ustomer statistics'
                        }
                    ],
                    route: 'statistics/statistic-individual-customers',
                    permission: [
                        {
                            code: 'RptIndividualCustomerStatisticsKHAUC209'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: '[AGG] Thống kê hồ sơ đến hạn'
                        },
                        {
                            languageId: 46,
                            name: 'Due dossier statistics'
                        }
                    ],
                    route: 'statistics/dossier-due-agg',
                    permission: [
                        {
                            code: 'oneGateDueDossierReportAGG'
                        },
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Thống kê hồ sơ đã chuyển'
                        },
                        {
                            languageId: 46,
                            name: 'Transferred dossier statistics'
                        }
                    ],
                    route: 'statistics/dossier-transferred',
                    permission: [
                        {
                            code: 'oneGateReport'
                        },
                        {
                            code: 'oneGateTransferredDossierReport'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: this.dossierCancelName
                        },
                        {
                            languageId: 46,
                            name: 'Cancel dossier statistics'
                        }
                    ],
                    route: 'statistics/dossier-cancel',
                    permission: [
                        {
                            code: 'oneGateReport'
                        },
                        {
                            code: 'oneGateCancelDossierReport'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Thống kê sổ theo dõi'
                        },
                        {
                            languageId: 46,
                            name: 'Logbook statistics'
                        }
                    ],
                    route: this.menuLogbook,
                    permission: [
                        {
                            code: 'oneGateReport'
                        },
                        {
                            code: 'oneGateLogbookStatistics'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: '[AGG] Thống kê hồ sơ tạm ngưng'
                        },
                        {
                            languageId: 46,
                            name: '[AGG] Statistics of temporary records'
                        }
                    ],
                    route: 'statistics/dossier-pause-agg',
                    permission: [
                        {
                            code: 'oneGateStatisticDossierPauseAgg'
                        }
                    ]
                },
				{
                    title: [
                        {
                            languageId: 228,
                            name: 'Thống kê số lượng hồ sơ tuyển sinh đầu cấp'
                        },
                        {
                            languageId: 46,
                            name: 'HGG Logbook statistics'
                        }
                    ],
                    route: 'statistics/logbook-hgg',
                    permission: [
                        {
                            code: 'oneGateReport'
                        },
                        {
                            code: 'oneGateHGGLogbookStatistics'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Thống kê sổ theo dõi Quảng Trị'
                        },
                        {
                            languageId: 46,
                            name: 'QTI Logbook statistics'
                        }
                    ],
                    route: 'statistics/logbook-qti',
                    permission: [
                        {
                            code: 'oneGateLogbookStatisticsQTI'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Thống kê sổ theo dõi HCM'
                        },
                        {
                            languageId: 46,
                            name: 'HCM Logbook statistics'
                        }
                    ],
                    route: 'statistics/HCMlogbook',
                    permission: [
                        {
                            code: 'oneGateReport'
                        },
                        {
                            code: 'oneGateHCMAdmin'
                        },
                        {
                            code: 'oneGateHCMLogbookStatistics'
                        },
                        {
                            code: 'oneGateHCMLogbookStatisticsES'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Thống kê hồ sơ tiếp nhận và xử lý'
                        },
                        {
                            languageId: 46,
                            name: 'Statistics Dossiers Received And Processing'
                        }
                    ],
                    route: 'statistics/statistic-dossier-received-processing',
                    permission: [
                        {
                            code: 'StatisticDossiersReceivedAndProcessing'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Thống kê hồ sơ theo cá nhân xử lý'
                        },
                        {
                            languageId: 46,
                            name: 'Logbook statistics by personal'
                        }
                    ],
                    route: 'statistics/personal-log',
                    permission: [
                        {
                            code: 'oneGateHCMAdmin'
                        },
                        {
                            code: 'oneGatePersonalLogbookStatistics'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Thống kê hồ sơ tiếp nhận và xử lý'
                        },
                        {
                            languageId: 46,
                            name: 'Statistics Dossiers Received And Processing Day'
                        }
                    ],
                    route: 'statistics/statistic-dossier-received-processing-day',
                    permission: [
                        {
                            code: 'StatisticDossiersReceivedAndProcessingDay'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Thống kê sổ theo thủ tục'
                        },
                        {
                            languageId: 46,
                            name: 'Procedure statistics'
                        }
                    ],
                    route: 'statistics/statistic-procedure',
                    permission: [
                        {
                            code: 'oneGateReport'
                        },
                        {
                            code: 'oneGateProcedureStatistical'
                        },
                        {
                            code: 'oneGateProcedureStatisticalDay'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Thống kê hồ sơ liên thông Văn phòng UBNDTP'
                        },
                        {
                            languageId: 46,
                            name: 'Statistics on the registration of the UBNDTP Office'
                        }
                    ],
                    route: 'statistics/statistic-procedure-VPUB',
                    permission: [
                        {
                            code: 'oneGateInterDossierVPUBStatistical'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Thống kê hồ sơ không cần xử lý'
                        },
                        {
                            languageId: 46,
                            name: 'Statistical records do not need to be processed'
                        }
                    ],
                    route: 'statistics/statistic-dossier-not-handle',
                    permission: [
                        {
                            code: 'oneGateReport'
                        },
                        {
                            code: 'oneGateProcedureStatistical'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Thống kê hồ sơ không cần xử lý'
                        },
                        {
                            languageId: 46,
                            name: 'Statistical records do not need to be processed'
                        }
                    ],
                    route: 'statistics/statistic-dossier-not-handle-day',
                    permission: [
                        {
                            code: 'oneGateReport'
                        },
                        {
                            code: 'statisticalRecordsDoNotNeedToBeProcessedDay'
                        }
                    ]
                },
                {
                  title: [
                    {
                      languageId: 228,
                      name: 'Thống kê sổ theo thủ tục'
                    },
                    {
                      languageId: 46,
                      name: 'Procedure statistics'
                    }
                  ],
                  route: 'statistics/procedure',
                  permission: [
                    {
                        code: 'oneGateReport'
                    },
                    {
                      code: 'oneGateProcedureStatisticalOld'
                    }
                  ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Thống kê theo Quyết định 274/QĐ-TTG năm 2019'
                        },
                        {
                            languageId: 46,
                            name: 'Statistics according to Decision 274/QĐ-TTG 2019'
                        }
                    ],
                    route: 'statistics/decision-274-qd-ttg',
                    permission: [
                        {
                            code: 'oneGateReport'
                        },
                        {
                            code: 'oneGate247QDTTG'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Báo cáo UBMTTQ'
                        },
                        {
                            languageId: 46,
                            name: 'UBMTTQ Dossier Statistics Report'
                        }
                    ],
                    route: 'statistics/ubmttq-statistic',
                    permission: [
                        {
                            code: 'oneGateAdminMaster'
                        },
                        {
                            code: 'oneGateUbmttqStatistic'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Thống kê hồ sơ từ chối'
                        },
                        {
                            languageId: 46,
                            name: 'Refused dossier statistics'
                        }
                    ],
                    route: 'statistics/refused-statistic',
                    permission: [
                        {
                            code: 'oneGateAdminMaster'
                        },
                        {
                            code: 'oneGateRefusedStatistic'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Báo cáo tổng hợp theo đơn vị'
                        },
                        {
                            languageId: 46,
                            name: 'Summary report by agency'
                        }
                    ],
                    route: 'statistics/synthetic-fl-agency',
                    permission: [
                        {
                            code: 'oneGateReport'
                        },
                        {
                            code: 'oneGatePeriodicReport'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Báo cáo số liệu hồ sơ dịch vụ công theo mức độ'
                        },
                        {
                            languageId: 46,
                            name: 'Reporting public service dossiers data by level'
                        }
                    ],
                    route: 'statistics/dossier-fl-level',
                    permission: [
                        {
                            code: 'oneGateReport'
                        },
                        {
                            code: 'oneGateLevelReport'
                        }
                    ]
                },
                {
                  title: [
                    {
                      languageId: 228,
                      name: 'Báo cáo số liệu hồ sơ dịch vụ công theo mức độ',
                    },
                    {
                      languageId: 46,
                      name: 'Reporting public service dossiers data by level',
                    },
                  ],
                  route: 'statistics/report-service-profile-by-procedure-level',
                  permission: [
                    {
                      code: 'RptReportonPublicServiceCaseDatabyLevelKHAUC188',
                    },
                  ],
                },
                {
                    title: [
                      {
                        languageId: 228,
                        name: 'Thống kê sổ theo dõi (bổ sung bộ lọc)',
                      },
                      {
                        languageId: 46,
                        name: 'Logbook statistics (add filter)',
                      },
                    ],
                    route: 'statistics/statistic-logbook-by-column-filter',
                    permission: [
                      {
                        code: 'RptLogbookStatisticsByColumnsFilterKHAUC196',
                      }
                    ],
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Thống kê số lượng hồ sơ tiếp nhận trong năm'
                        },
                        {
                            languageId: 46,
                            name: 'Statistics of the number of dossiers received in the year'
                        }
                    ],
                    route: 'statistics/dossier-in-year',
                    permission: [
                        {
                            code: 'oneGateReport'
                        },
                        {
                            code: 'oneGateDossierInTheYear'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Báo cáo biên lai'
                        },
                        {
                            languageId: 46,
                            name: 'Report receipt'
                        }
                    ],
                    route: 'statistics/report-receipt',
                    permission: [
                        {
                            code: 'oneGateReport'
                        },
                        {
                            code: 'oneGateReceiptCreation'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Thống kê báo cáo biên lai giấy'
                        },
                        {
                            languageId: 46,
                            name: 'Paper Receipt Report'
                        }
                    ],
                    route: 'statistics/report-receipt-paper',
                    permission: [
                        {
                            code: 'oneGateReport'
                         },
                        {
                            code: 'oneGateReceiptPaper'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Thống kê người dùng'
                        },
                        {
                            languageId: 46,
                            name: 'User Statistics'
                        }
                    ],
                    route: 'statistics/ktm-user-statistic',
                    permission: [
                        {
                            code: 'userStatistics'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Thống kê HS đang ở cơ quan thuế'
                        },
                        {
                            languageId: 46,
                            name: 'Tax Report'
                        }
                    ],
                    route: 'statistics/kontum-tax-report',
                    permission: [
                        {
                            code: 'oneGateKonTumTaxReport'
                        },
                        {
                            code: 'oneGateReport'
                        },
                    ]
                },
                {
                title: [
                  {
                    languageId: 228,
                    name: 'Thống kê và quản lý sms'
                  },
                  {
                    languageId: 46,
                    name: 'Statistics and sms management'
                  }
                ],
                route: 'statistics/log-sms',
                permission: [
                    {
                        code: 'oneGateReport'
                    },
                    {
                        code: 'oneGateManageSms'
                    }
                ]
              },
              {
                title: [
                  {
                    languageId: 228,
                    name: 'Báo cáo thống kê SMS'
                  },
                  {
                    languageId: 46,
                    name: 'Statistics and sms management'
                  }
                ],
                route: 'statistics/log-sms-qbh',
                permission: [
                    {
                        code: 'oneGateReport'
                    },
                    {
                        code: 'qbhSMSReport'
                    }
                ]
              },
              {
                title: [
                    {
                        languageId: 228,
                        name: 'Thống kê tài khoản người dùng trên hệ thống'
                    },
                    {
                        languageId: 46,
                        name: 'Account User Statistics'
                    }
                ],
                route: 'statistics/account-user',
                permission: [
                  {
                      code: 'oneGateReportAccountUser'
                  }
                ]
            },
              {
                  title: [
                      {
                          languageId: 228,
                          name: this.userStatisticsName
                      },
                      {
                          languageId: 46,
                          name: '901 - User'
                      }
                  ],
                  route: 'statistics/user',
                  permission: [
                    {
                        code: 'oneGateReport'
                    },
                    {
                        code: 'oneGateUserStatistics'
                    }
                  ]
              },
              {
                title: [
                    {
                        languageId: 228,
                        name: '902 - Thống kê danh mục'
                    },
                    {
                        languageId: 46,
                        name: '902 - Category'
                    }
                ],
                route: 'statistics/category',
                permission: [
                    {
                        code: 'oneGateReport'
                    },
                    {
                        code: 'oneGateCategoryBDG'
                    }
                ]
              },
              {
                title: [
                    {
                        languageId: 228,
                        name: 'Báo cáo biểu số II.08'
                    },
                    {
                        languageId: 46,
                        name: 'Report no. II.08'
                    }
                ],
                route: 'statistics/cbg-report-08',
                permission: [
                    {
                        code: 'oneGateReport'
                    },
                    {
                        code: 'oneGateCBGReportII08'
                    }
                ]
              },
              {
                title: [
                    {
                        languageId: 228,
                        name: 'Báo cáo theo TT01, biểu số II.08.VPVP.KSTT'
                    },
                    {
                        languageId: 46,
                        name: ' Báo cáo theo TT01, biểu số II.08.VPVP.KSTT'
                    }
                ],
                route: 'statistics/report-08-qbh',
                permission: [
                    {
                        code: 'oneGateQBHReportII08'
                    }
                ]
              },
              {
                title: [
                    {
                        languageId: 228,
                        name: '[HGG] Báo cáo theo TT01, biểu số II.08.VPVP.KSTT'
                    },
                    {
                        languageId: 46,
                        name: '[HGG] Báo cáo theo TT01, biểu số II.08.VPVP.KSTT'
                    }
                ],
                route: 'statistics/report-08-hgg',
                permission: [
                    {
                        code: 'oneGateHGGReportII08'
                    }
                ]
              },
              {
                title: [
                    {
                        languageId: 228,
                        name: '800 Thống kê hồ sơ'
                    },
                    {
                        languageId: 46,
                        name: '800 Dossier Report'
                    }
                ],
                route: 'statistics/bdg-800-dossier-report',
                permission: [
                    {
                        code: 'oneGateReport'
                    },
                    {
                        code: 'onGateDossierStatisticsBDG'
                    }
                ]
              },
              {
                title: [
                    {
                        languageId: 228,
                        name: this.overdueDossierReportBDGName
                    },
                    {
                        languageId: 46,
                        name: '903 Statistics dossier overdue'
                    }
                ],
                route: 'statistics/bdg-dossier-overdue',
                permission: [
                    {
                        code: 'oneGateReport'
                    },
                    {
                        code: 'oneGateOverdueDossierReportBDG'
                    }
                ]
              },
              {
                title: [
                    {
                        languageId: 228,
                        name: '900 Thống kê rà soát hệ thống'
                    },
                    {
                        languageId: 46,
                        name: '900 Statistics check system'
                    }
                ],
                route: 'statistics/statistic-check-bdg',
                permission: [
                    {
                        code: 'oneGateReport'
                    },
                    {
                        code: 'oneGateStatisticCheckBDG'
                    }
                ]
              },
              {
                title: [
                    {
                        languageId: 228,
                        name: 'Thống kê hồ sơ chậm tiếp nhận'
                    },
                    {
                        languageId: 46,
                        name: 'Statistics of slow reception'
                    }
                ],
                route: 'statistics/report-slow-reception-qni',
                permission: [
                    {
                        code: 'slowReceptionNAST'
                    }
                ]
              },
              {
                title: [
                    {
                        languageId: 228,
                        name: this.statisticProcedureBDGName
                    },
                    {
                        languageId: 46,
                        name: '906 Statistic procedure'
                    }
                ],
                route: 'statistics/statistic-procedure-bdg',
                permission: [
                    {
                        code: 'oneGateReport'
                    },
                    {
                        code: 'oneGateStatisticProcedureBDG'
                    }
                ]
              },
              {
                title: [
                    {
                        languageId: 228,
                        name: 'Báo cáo chuyên môn Ban quản lý ATTP 2 - 01'
                    },
                    {
                        languageId: 46,
                        name: 'Professional report of Food Safety Management Board 2 - 01'
                    }
                ],
                route: 'statistics/hcm-BQL-ATTP2-01',
                permission: [
                    {
                        code: 'oneGateReport'
                    },
                    {
                        code: 'oneGateHCMAdmin'
                    },
                    {
                        code: 'oneGateStatisticProcedureHCM'
                    },
                    {
                        code: 'oneGateStatisticProcedureHCM01ES'
                    }
                ]
              },
              {
                    title: [
                        {
                            languageId: 228,
                            name: this.reportVpcpOnlineName
                        },
                        {
                            languageId: 46,
                            name: 'Online Public Service Application Report'
                        }
                    ],
                    route: 'statistics/report-vpcp-online',
                    permission: [
                        {
                            code: 'oneGateReport'
                        },
                        {
                            code: 'oneGateReportVpcpOnline'
                        }
                    ]
              },
              {
                title: [
                  {
                    languageId: 228,
                    name: 'Báo cáo xác nhận quảng cáo - ATTP'
                  },
                  {
                    languageId: 46,
                    name: 'Report confirm advertisement'
                  }
                ],
                route: 'statistics/report-confirm-advertisement',
                permission: [
                    {
                        code: 'oneGateReport'
                    },
                    {
                        code: 'oneGateHCMAdmin'
                    },
                    {
                        code: 'oneGateStatisticProcedureHCM'
                    },
                    {
                        code: 'oneGateStatisticProcedureHCMES'
                    },
                ]
            },
                {
                    title: [
                        {
                            languageId: 228,
                            name: this.digitizingDossierReportName
                        },
                        {
                            languageId: 46,
                            name: 'DBN Digitization Dossier Report'
                        }
                    ],
                    route: 'statistics/dbn-digitization-dossier-report',
                    permission: [
                        {
                            code: 'oneGateReport'
                        },
                        {
                            code: 'oneGateDbnDigitizationDossierReport',
                        },
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'AGG Báo cáo thống kê số hóa hồ sơ'
                        },
                        {
                            languageId: 46,
                            name: 'AGG Digitization Dossier Report'
                        }
                    ],
                    route: 'statistics/agg-digitization-dossier-report',
                    permission: [
                        {
                            code: 'oneGateAggDigitizationDossierReport',
                        },
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'AGG Cấu hình đơn vị cán bộ trễ hẹn'
                        },
                        {
                            languageId: 46,
                            name: 'AGG Configures the late staff unit'
                        }
                    ],
                    route: 'statistics/agg-config-agency-personal-overdue',
                    permission: [
                        {
                            code: 'oneGateAggConfigAgencyPersonalOverdue',
                        },
                        {
                            code: 'oneGateAggConfigAgencyPersonalOverdueWithEdit',
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'QNI Báo cáo thống kê số hóa hồ sơ'
                        },
                        {
                            languageId: 46,
                            name: 'QNI Digitization Dossier Report'
                        }
                    ],
                    route: 'statistics/qni-digitization-dossier-report',
                    permission: [
                        {
                            code: 'oneGateReport'
                        },
                        {
                            code: 'oneGateQniDigitizationDossierReport',
                        },
                    ]
                },
                {
                  title: [
                      {
                          languageId: 228,
                          name: 'Báo cáo số hóa hồ sơ'
                      },
                      {
                          languageId: 46,
                          name: 'Digitization Dossier Report KGG'
                      }
                  ],
                  route: 'statistics/kgg-digitization-dossier-report',
                  permission: [
                      {
                          code: 'oneGateKGGDigitizationDossierReport',
                      }
                  ]
                },
              {
                title: [
                  {
                    languageId: 228,
                    name: 'Thống kê kết quả giải quyết TTHC theo cán bộ'
                  },
                  {
                    languageId: 46,
                    name: 'Statistics on the results of administrative procedures by officials'
                  }
                ],
                route: 'statistics/statistic-procedure-by-officials',
                permission: [
                    {
                        code: 'oneGateReport'
                    },
                    {
                        code: 'oneGateStatisticProcedureByOfficials'
                    },
                    {
                        code: 'newOneGateStatisticProcedureByOfficials'
                    }
                ]
              },
              {
                title: [
                  {
                    languageId: 228,
                    name: 'Thống kê liên thông - Cơ quan phối hợp'
                  },
                  {
                    languageId: 46,
                    name: 'Interagency Statistics - Coordinating Agency'
                  }
                ],
                route: 'statistics/statistic-communication-coordination-agency',
                permission: [
                    {
                        code: 'RptIntegratedStatisticsCoordinatingAgencyKHAUC206'
                    }
                ]
              },
              {
                title: [
                  {
                    languageId: 228,
                    name: 'Thống kê kết quả giải quyết TTHC theo cán bộ'
                  },
                  {
                    languageId: 46,
                    name: 'Statistics on the results of administrative procedures by officials'
                  }
                ],
                route: 'statistics/statistic-procedure-by-officials-day',
                permission: [
                    {
                        code: 'oneGateReport'
                    },
                    {
                        code: 'oneGateStatisticProcedureByOfficialsDay'
                    }
                ]
              },
              {
                title: [
                  {
                    languageId: 228,
                    name: 'Thống kê kết quả giải quyết thủ tục hành chính - Theo lĩnh vực (Mới)'
                  },
                  {
                    languageId: 46,
                    name: 'Statistics on the Results of Administrative Procedures Resolution - By Field (New)'
                  }
                ],
                route: 'statistics/statistic-procedure-by-sector',
                permission: [
                    {
                        code: 'RptResultsofAdministrativeProcedureResolutionByFieldKHAUC203'
                    }
                ]
              },
              {
                title: [
                  {
                    languageId: 228,
                    name: 'Thống kê tổng hợp kết quả giải quyết thủ tục hành chính'
                  },
                  {
                    languageId: 46,
                    name: 'Summary statistics of administrative procedure resolution results'
                  }
                ],
                route: 'statistics/administrative-resolution-result',
                permission: [
                    {
                        code: 'RptAdministrativeResolutionResultKHA'
                    }
                ]
              },
              {
                title: [
                  {
                    languageId: 228,
                    name: 'Thống kê liên thông - Cơ quan chủ trì'
                  },
                  {
                    languageId: 46,
                    name: 'Inter-agency statistics - Lead agency'
                  }
                ],
                route: 'statistics/statistic-communication-lead-agency',
                permission: [
                    {
                        code: 'RptIntegratedStatisticsLeadAgencyKHAUC205'
                    }
                ]
              },
              {
                title: [
                    {
                        languageId: 228,
                        name: 'Báo cáo ứng dụng Dịch vụ công trực tuyến-QNI'
                    },
                    {
                        languageId: 46,
                        name: 'Online Public Service Application Report'
                    }
                ],
                route: 'statistics/qni-report-vpcp-online',
                permission: [
                    {
                        code: 'oneGateReport'
                    },
                    {
                        code: 'oneGateReportVpcpOnlineQNI'
                    }
                ]

              },
              {
                title: [
                    {
                        languageId: 228,
                        name: 'Thống kê TTHC cung cấp dịch vụ công trực tuyến (HGG)'
                    },
                    {
                        languageId: 46,
                        name: 'Online Public Service Application Report'
                    }
                ],
                route: 'statistics/hgg-report-vpcp-online',
                permission: [
                    {
                        code: 'oneGateReport'
                    },
                    {
                        code: 'oneGateReportVpcpOnlineHGG'
                    }
                ]

              },
              {
                title: [
                    {
                        languageId: 228,
                        name: 'Báo cáo TTPVKSTTHC'
                    },
                    {
                        languageId: 46,
                        name: 'TTPVKSTTHC Report'
                    }
                ],
                route: 'statistics/qni-report-TTPVKSTTHC',
                permission: [
                    {
                        code: 'oneGateReportTTPVKSTTHCQNI'
                    }
                ]

              },
              {
                title: [
                    {
                        languageId: 228,
                        name: 'Thống kê sổ đăng ký độc giả'
                    },
                    {
                        languageId: 46,
                        name: 'Reader registration statistics'
                    }
                ],
                route: 'statistics/hcm-registration-reader',
                permission: [
                    {
                        code: 'oneGateHCMAdmin'
                    },
                    {
                        code: 'oneGateRegistrationReaderHCM'
                    },
                    {
                        code: 'oneGateRegistrationReaderHCMES'
                    }
                ]
              },
              {
                title: [
                    {
                        languageId: 228,
                        name: 'Báo cáo chuyên môn Ban quản lý ATTP 2 - 02'
                    },
                    {
                        languageId: 46,
                        name: 'Professional report of Food Safety Management Board 2 - 02'
                    }
                ],
                route: 'statistics/hcm-BQL-ATTP2-02',
                permission: [
                    {
                        code: 'oneGateReport'
                    },
                    {
                        code: 'oneGateHCMAdmin'
                    },
                    {
                        code: 'oneGateStatisticProcedureHCM'
                    },
                    {
                        code: 'oneGateStatisticProcedureHCM02ES'
                    }
                ]
              },
              {
                title: [
                    {
                        languageId: 228,
                        name: 'Báo cáo chuyên môn Ban quản lý ATTP 2 - 03'
                    },
                    {
                        languageId: 46,
                        name: 'Professional report of Food Safety Management Board 2 - 03'
                    }
                ],
                route: 'statistics/hcm-BQL-ATTP2-03',
                permission: [
                    {
                        code: 'oneGateReport'
                    },
                    {
                        code: 'oneGateHCMAdmin'
                    },
                    {
                        code: 'oneGateStatisticProcedureHCM'
                    },
                    {
                        code: 'oneGateStatisticProcedureHCM03ES'
                    }
                ]
              },
              {
                title: [
                    {
                        languageId: 228,
                        name: 'Báo cáo cấp giấy chứng nhận ATTP'
                    },
                    {
                        languageId: 46,
                        name: 'Food safety certification report'
                    }
                ],
                route: 'statistics/hcm-certification-attp-report',
                permission: [
                    {
                        code: 'oneGateAdminMaster'
                    },
                    {
                        code: 'oneGateStatisticProcedureHCM'
                    }
                ]
               },
               {
                title: [
                    {
                        languageId: 228,
                        name: 'QNI - Báo cáo chi tiết'
                    },
                    {
                        languageId: 46,
                        name: 'Dossier joint profile report'
                    }
                ],
                route: 'statistics/dossier-joint-profile-report',
                permission: [
                    {
                        code: 'oneGateStatisticDossierProfileReportQNI'
                    }
                ]
               },
               {
                title: [
                    {
                        languageId: 228,
                        name: `${this.generalReportTitle}`
                    },
                    {
                        languageId: 46,
                        name: 'General Report'
                    }
                ],
                route: 'statistics/report-general',
                permission: [
                    {
                        code: 'reportQNI'
                    }
                ]
               },
               {
                title: [
                    {
                        languageId: 228,
                        name: 'Báo cáo thanh toán trực tuyến Quảng Ngãi'
                    },
                    {
                        languageId: 46,
                        name: 'Report online payment For Quang Ngai'
                    }
                ],
                route: 'statistics/report-online-payment-qni',
                permission: [
                    {
                        code: 'onlinePaymentReportsNAST'
                    }
                ]
               },
               {
                title: [
                    {
                        languageId: 228,
                        name: 'Báo cáo nghĩa vụ tài chính tỉnh Quảng Ngãi'
                    },
                    {
                        languageId: 46,
                        name: 'Financial obligation report For Quang Ngai'
                    }
                ],
                route: 'statistics/financial-obligation-report-qni',
                permission: [
                    {
                        code: 'financialObligationReportNAST'
                    }
                ]
               },
              {
                title: [
                    {
                        languageId: 228,
                        name: 'Thống kê biên lai thu lệ phí'
                    },
                    {
                        languageId: 46,
                        name: 'Statistics of fee collection receipts'
                    }
                ],
                route: 'statistics/dossier-fee-bill-hcm',
                permission: [
                    {
                        code: 'oneGateHCMDossierFeeBillReport'
                    }
                ]
              },
              {
                title: [
                    {
                        languageId: 228,
                        name: 'Thống kê sổ Hành chính công'
                    },
                    {
                        languageId: 46,
                        name: 'Document Book Statistics'
                    }
                ],
                route: 'statistics/hcm-adbook',
                permission: [
                    {
                        code: 'oneGateAdbookHCM'
                    }
                ]
               },
              {
                title: [
                    {
                        languageId: 228,
                        name: 'Báo cáo thu phí, lệ phí'
                    },
                    {
                        languageId: 46,
                        name: 'HCM fee report'
                    }
                ],
                route: 'statistics/hcm-fee-report',
                permission: [
                    {
                        code: 'oneGateHCMFeeReport'
                    },
                    {
                        code: 'oneGateHCMFeeReportES'
                    }
                ]
              },
               {
                title: [
                    {
                        languageId: 228,
                        name: '[QNM] Thống kê theo thông tư 01/2020/TT-VPCP/6c'
                    },
                    {
                        languageId: 46,
                        name: '[QNM] Circular 01/2020/TT-VPCP/6c statistics'
                    }
                ],
                route: 'statistics/statistic-01-2020-6c-qnm',
                permission: [
                    {
                        code: 'oneGateQNM0120206cStatistical'
                    }
                ]
            },
            {
                title: [
                    {
                        languageId: 228,
                        name: '[KHA] Thống kê theo thông tư 01/2020/TT-VPCP/6c'
                    },
                    {
                        languageId: 46,
                        name: '[KHA] Circular 01/2020/TT-VPCP/6c statistics'
                    }
                ],
                route: 'statistics/statistic-01-2020-6c-kha',
                permission: [
                    {
                        code: 'Rtp6c012020vpcpKHA'
                    }
                ]
            },
            {
                title: [
                  {
                    languageId: 228,
                    name: 'Thống kê liên thông - Xin ý kiến - Cơ quan phối hợp'
                  },
                  {
                    languageId: 46,
                    name: 'Interconnected statistics - Consulting - Coordinating agency'
                  }
                ],
                route: 'statistics/statistic-contact-and-comment',
                permission: [
                    {
                        code: 'RptIntegratedCasesRequestforOpinionCAKHAUC207'
                    }
                ]
              },
               {
                title: [
                    {
                        languageId: 228,
                        name: 'Báo cáo thống kê hồ sơ trực tuyến mức độ 3,4 KHA'
                    },
                    {
                        languageId: 46,
                        name: 'Online Dossier Level 3,4 Statistics'
                    }
                ],
                route: 'statistics/kha-report-vpcp-online',
                permission: [
                    {
                        code: 'cdtadmAccountManagementKHA'
                    },
                    {
                        code: 'roleReportStatisticsOnlineDossierLevel34KHA'
                    }
                ]
               },
               {
                title: [
                    {
                        languageId: 228,
                        name: 'Báo cáo thống kê hồ sơ trực tuyến'
                    },
                    {
                        languageId: 46,
                        name: 'Online Dossier Statistics'
                    }
                ],
                route: 'statistics/kha-report-vpcp-online-34',
                permission: [
                    {
                        code: 'RptOnlinestatisticalreportlevel34KHAUC201'
                    }
                ]
               },
               {
                title: [
                    {
                        languageId: 228,
                        name: 'Báo cáo thống kê hồ sơ online'
                    },
                    {
                        languageId: 46,
                        name: 'Online Dossier Statistics'
                    }
                ],
                route: 'statistics/statistic-online-kha',
                permission: [
                    {
                        code: 'RptOnlineCaseStatisticsKHAUC213'
                    }
                ]
               },
            {
                title: [
                    {
                        languageId: 228,
                        name: 'Thống kê mẫu cung cấp dữ liệu BQLATTP'
                    },
                    {
                        languageId: 46,
                        name: 'BQLATTP Logbook statistics'
                    }
                ],
                route: 'statistics/HCMlogbook-bqlattp',
                permission: [
                    {
                        code: 'oneGateHCMLogbookBQLATTPStatistics'
                    }
                ]
            },
            {
                title: [
                    {
                        languageId: 228,
                        name: 'Tổng hợp tình hình sử dụng SMS'
                    },
                    {
                        languageId: 46,
                        name: 'Summary of SMS usage situation'
                    }
                ],
                route: 'statistics/statistics-sms',
                permission: [
                    {
                        code: 'oneGateReportSmsQNM'
                    }
                ]
            },
            {
                title: [
                    {
                        languageId: 228,
                        name: 'Danh sách hồ sơ liên thông DVCLT'
                    },
                    {
                        languageId: 46,
                        name: 'Look up dossier DVCLT'
                    }
                ],
                route: 'statistics/statistic-dossier-dvclt',
                permission: [
                    {
                        code: 'oneGateDossierDVCLTStatistical'
                    }
                ]
            },
            {
                title: [
                    {
                        languageId: 228,
                        name: 'Thống kê TTHC theo trạng thái hồ sơ'
                    },
                    {
                        languageId: 46,
                        name: 'Procedure statistic by dossier status'
                    }
                ],
                route: 'statistics/hcm-procedural-dossier-statistic',
                permission: [
                    {
                        code: 'oneGateStatisticDossierStatusByProcedureHCM'
                    }
                ]
            },
            //danhdd.hcm-IGATESUPP-50652 Bổ sung thống kê "Thống kê danh sách yêu cầu cấp phiếu LLTP"
            {
                title: [
                    {
                        languageId: 228,
                        name: 'Thống kê danh sách yêu cầu cấp phiếu LLTP'
                    },
                    {
                        languageId: 46,
                        name: 'Statistical list of requests for vouchers'
                    }
                ],
                route: 'statistics/hcm-judicial-record-cards',
                permission: [
                    {
                        code: 'onegateStatisticsLLTPHCM'
                    }
                ]
            },
            //chinhtla: IGATESUPP-42937 -them menu
            {
                title: [
                    {
                        languageId: 228,
                        name: 'Báo cáo Dịch vụ công trực tuyến chi tiết'
                    },
                    {
                        languageId: 46,
                        name: 'Online Public Service Application Report'
                    }
                ],
                route: 'statistics/report-dvc-online-qbh',
                permission: [
                    {
                        code: 'oneGateReportDVCOnlineQBH'
                    }
                ]
            },
            {
                title: [
                    {
                        languageId: 228,
                        name: 'Báo cáo Dịch vụ công trực tuyến tổng hợp'
                    },
                    {
                        languageId: 46,
                        name: 'Online Public Service General Report'
                    }
                ],
                route: 'statistics/report-dvc-online-general-qbh',
                permission: [
                    {
                        code: 'reportOnlineSum'
                    }
                ]
            },
            {
                title: [
                    {
                        languageId: 228,
                        name: 'Báo cáo thanh toán trực tuyến'
                    },
                    {
                        languageId: 46,
                        name: 'Online Payment Report'
                    }
                ],
                route: 'statistics/statistic-payment-report-qbh',
                permission: [
                    {
                        code: 'paymentReport2'
                    }
                ]
            },
            {
                title: [
                    {
                        languageId: 228,
                        name: 'Số hóa hồ sơ/Kết quả'
                    },
                    {
                        languageId: 46,
                        name: 'Digital Report'
                    }
                ],
                route: 'statistics/statistic-digital-report',
                permission: [
                    {
                        code: 'oneGateReportStatisticDigitalQBH'
                    }
                ]
            },
            {
                title: [
                    {
                        languageId: 228,
                        name: 'Số hóa hồ sơ/kết quả'
                    },
                    {
                        languageId: 46,
                        name: 'Digital Report'
                    }
                ],
                route: 'statistics/statistic-digital-report-v1',
                permission: [
                    {
                        code: 'qbhbcsohoav1'
                    }
                ]
            },
            {
                title: [
                    {
                        languageId: 228,
                        name: 'Thống kê hồ sơ quá hạn'
                    },
                    {
                        languageId: 46,
                        name: 'Statistic Overdue Report'
                    }
                ],
                route: 'statistics/dossier-overdue-qbh',
                permission: [
                    {
                        code: 'overdueReport'
                    }
                ]
            },
            {
                title: [
                    {
                        languageId: 228,
                        name: 'Thống kê hồ sơ theo ngày hẹn trả'
                    },
                    {
                        languageId: 46,
                        name: 'Statistics dossier by appointment date'
                    }
                ],
                route: 'statistics/statistics-dossier-by-appointment-date',
                permission: [
                    {
                        code: 'oneGateAdminMaster'
                    },
                    {
                        code: 'oneGateStatisticDossierByAppointmentDate'
                    }
                ]
            },
              {
                title: [
                  {
                    languageId: 228,
                    name: 'Báo cáo phụ lục 8 (biểu mẫu II.08)'
                  },
                  {
                    languageId: 46,
                    name: 'Báo cáo phụ lục 8 (biểu mẫu II.08)'
                  }
                ],
                route: 'statistics/report-08-cmu',
                permission: [
                  {
                    code: 'oneGateCMUReport08'
                  }

                ]
              },
              {
                title: [
                  {
                    languageId: 228,
                    name: '[AGG] Báo cáo phụ lục 8 (biểu mẫu II.08)'
                  },
                  {
                    languageId: 46,
                    name: '[AGG] Báo cáo phụ lục 8 (biểu mẫu II.08)'
                  }
                ],
                route: 'statistics/report-08-agg',
                permission: [
                  {
                    code: 'oneGateAggReport08'
                  }

                ]
              },
            {
                title: [
                    {
                        languageId: 228,
                        name: 'Thống kê theo thông tư 01/2020/TT-VPCP/06c'
                    },
                    {
                        languageId: 46,
                        name: 'General statistics CMU'
                    }
                ],
                route: 'statistics/statistic-general-cmu',
                permission: [

                    {
                        code: 'oneGateReportAgencyCmu'
                    }
                ]
            },
            {
                title: [
                    {
                        languageId: 228,
                        name: '[QNM] Báo cáo thanh toán trực tuyến'
                    },
                    {
                        languageId: 46,
                        name: '[QNM] Report online payment'
                    }
                ],
                route: 'statistics/qnm-report-online-payment',
                permission: [
                    {
                        code: 'oneGateOnlinePaymentByPaidDateQNM'
                    }
                ]
            },
            {
                title: [
                    {
                        languageId: 228,
                        name: 'Báo cáo đánh giá đơn vị theo QĐ 25'
                    },
                    {
                        languageId: 46,
                        name: 'Survey agency'
                    }
                ],
                route: 'statistics/survey-agency-hcm',
                permission: [
                    {
                        code: 'oneGateSurveyAgencyHCM'
                    }
                ]
            },
            {
                title: [
                    {
                        languageId: 228,
                        name: 'Báo cáo đánh giá cán bộ theo QĐ 25'
                    },
                    {
                        languageId: 46,
                        name: 'Survey officer'
                    }
                ],
                route: 'statistics/survey-officer-hcm',
                permission: [
                    {
                        code: 'oneGateSurveyOfficerHCM'
                    }
                ]
            },
            // {   //IGATESUPP-75297 - [HCM iGATE V2] Bình Tân-Lỗi chức năng thống kê tình hình triển khai,xử lý [SD 3536] => Bắt sai quyền và dư menu
            //     title: [
            //         {
            //             languageId: 228,
            //             name: 'Tình hình triển khai, xử lý'
            //         },
            //         {
            //             languageId: 46,
            //             name: 'Statistics processing & handle'
            //         }
            //     ],
            //     route: 'statistics/statistic-processing-dlk',
            //     permission: [
            //         {
            //             code: 'oneGateAdminMaster'
            //         },
            //         {
            //             code: 'oneGateStatisticDossierByAppointmentDate'
            //         }
            //     ]
            // },
            {
                title: [
                    {
                        languageId: 228,
                        name: '[CMU] Báo cáo dịch vụ công trực tuyến'
                    },
                    {
                        languageId: 46,
                        name: '[QNM] Report online payment'
                    }
                ],
                route: 'statistics/cmu-digitization-dossier-report',
                permission: [
                    {
                        code: 'statisticalDossierDigitizingCMU'
                    }
                ]
            },
            {
                title: [
                    {
                        languageId: 228,
                        name: 'Báo cáo liên thông'
                    },
                    {
                        languageId: 46,
                        name: 'Connection Dossier Statistics'
                    }
                ],
                route: 'statistics/statistic-lienthong-qbh',
                permission: [
                    {
                        code: 'oneGateReportDVCOnlineQBH'
                    }
                ]
            },
            {
                title: [
                    {
                        languageId: 228,
                        name: 'Thống kê quy trình liên thông giải quyết TTHC'
                    },
                    {
                        languageId: 46,
                        name: 'Statistics on the interconnected process of resolving administrative procedures'
                    }
                ],
                route: 'statistics/statistic-lienthong-qbh',
                permission: [
                    {
                        code: 'oneGateReportDVCOnlineQBHHGG'
                    }
                ]
            },
            {
                title: [
                    {
                        languageId: 228,
                        name: 'Thống kê số liệu dịch vụ công liên thông'
                    },
                    {
                        languageId: 46,
                        name: 'Statistics of public service data'
                    }
                ],
                route: 'statistics/dvclt-by-agency',
                permission: [
                    {
                        code: 'oneGateDVCLTByAgency'
                    }
                ]
            },
            {
                title: [
                    {
                        languageId: 228,
                        name: '[CMU] Báo cáo phụ lục 4'
                    },
                    {
                        languageId: 46,
                        name: 'Online Public Service Application Report'
                    }
                ],
                route: 'statistics/cmu-report-vpcp-pl04',
                permission: [
                    {
                        code: 'oneGateReport'
                    },
                    {
                        code: 'oneGateReportVpcpPl04CMU'
                    }
                ]

              },
              {
                title: [
                    {
                        languageId: 228,
                        name: 'Thống kê hồ sơ online chưa tiếp nhận'
                    },
                    {
                        languageId: 46,
                        name: 'Statistics online dossier not yet received'
                    }
                ],
                route: 'statistics/online-dossier-unreceived',
                permission: [
                    {
                        code: 'dlkOnlineDossierUnreceived'
                    }
                ]
            },

                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Thống kê dữ liệu công bố hợp quy VLXD'
                        },
                        {
                            languageId: 46,
                            name: 'Materials Statistics'
                        }
                    ],
                    route: 'statistics/statistic-building-materials',
                    permission: [
                        {
                            code: 'oneGateVLXDStatistical'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Thống kê hồ sơ trễ hạn QNM'
                        },
                        {
                            languageId: 46,
                            name: 'Overdue dossier statistics QNM'
                        }
                    ],
                    route: 'statistics/dossier-overdue-qnm',
                    permission: [
                        {
                            code: 'oneGateOverdueDossierReportQNM'
                        }
                    ]
                },
            //   },
              {
                title: [
                    {
                        languageId: 228,
                        name: 'Thống kê dịch vụ công trực tuyến'
                    },
                    {
                        languageId: 46,
                        name: 'Statistics public service'
                    }
                ],
                route: 'statistics/dlk-statistic-public-service',
                permission: [
                    {
                        code: 'dlkStatisticPublicService'
                    }
                ]
            },
            {
                title: [
                    {
                        languageId: 228,
                        name: 'Báo cáo số hoá',
                    },
                    {
                        languageId: 46,
                        name: 'Statistic Summarize Digitization List ',
                    },
                ],
                route: 'dlk-dac-thu/dlk-bao-cao-so-hoa',
                permission: [
                    {
                        code: 'oneGateReport',
                    },
                    {
                        code: 'dlkBaoCaoSoHoa'
                    }
                ],
            },
            {
              title: [
                {
                  languageId: 228,
                  name: 'Báo cáo chỉ thị 18 toàn tỉnh'
                },
                {
                  languageId: 46,
                  name: 'Báo cáo chỉ thị 18 toàn tỉnh'
                }
              ],
              route: 'dlk-dac-thu/dlkchithi18',
              permission: [
                {
                    code: 'oneGateReport'
                },
                {
                  code: 'oneGateAdminMaster'
                },
                {
                  code: 'dlkCT18ToanTinh'
                }
              ]
          },
          {
            title: [
              {
                languageId: 228,
                name: 'Báo cáo chỉ thị 25 toàn tỉnh'
              },
              {
                languageId: 46,
                name: 'Báo cáo chỉ thị 25 toàn tỉnh'
              }
            ],
            route: 'dlk-dac-thu/dlk-chithi25-toantinh',
            permission: [
              {
                  code: 'dlkCT25ToanTinh'
              }
            ]
          },
                    {
            title: [
              {
                languageId: 228,
                name: 'Báo cáo chỉ thị 25 toàn tỉnh - cấp sở'
              },
              {
                languageId: 46,
                name: 'Báo cáo chỉ thị 25 toàn tỉnh - cấp sở'
              }
            ],
            route: 'dlk-dac-thu/dlk-chithi25-toantinh-cs',
            permission: [
              {
                  code: 'dlkCT25ToanTinhCapSo'
              }
            ]
          },
                    {
            title: [
              {
                languageId: 228,
                name: 'Báo cáo chỉ thị 25 toàn tỉnh - cấp xã'
              },
              {
                languageId: 46,
                name: 'Báo cáo chỉ thị 25 toàn tỉnh - cấp xã'
              }
            ],
            route: 'dlk-dac-thu/dlk-chithi25-toantinh-cx',
            permission: [
              {
                  code: 'dlkCT25ToanTinhCapXa'
              }
            ]
          },
          {
            title: [
              {
                languageId: 228,
                name: 'Báo cáo chỉ thị 25 cấp sở'
              },
              {
                languageId: 46,
                name: 'Báo cáo chỉ thị 25 cấp sở'
              }
            ],
            route: 'dlk-dac-thu/dlk-chithi25-capso',
            permission: [
              {
                  code: 'dlkCT25CapSo'
              }
            ]
          },
          {
            title: [
              {
                languageId: 228,
                name: 'Báo cáo chỉ thị 25 cấp huyện'
              },
              {
                languageId: 46,
                name: 'Báo cáo chỉ thị 25 cấp huyện'
              }
            ],
            route: 'dlk-dac-thu/dlk-chithi25-caphuyen',
            permission: [
              {
                  code: 'dlkCT25CapHuyen'
              }
            ]
          },
                    {
            title: [
              {
                languageId: 228,
                name: 'Báo cáo chỉ thị 25 cấp xã'
              },
              {
                languageId: 46,
                name: 'Báo cáo chỉ thị 25 cấp xã'
              }
            ],
            route: 'dlk-dac-thu/dlk-chithi25-capxa',
            permission: [
              {
                  code: 'dlkCT25CapXa'
              }
            ]
          },
          {
            title: [
              {
                languageId: 228,
                name: 'Báo cáo chỉ thị 25 cấp đơn vị'
              }
            ],
            route: 'dlk-dac-thu/dlk-chithi25-capdonvi',
            permission: [
              {
                  code: 'dlkCT25CapDonVi'
              }
            ]
          },
            {
                title: [
                  {
                    languageId: 228,
                    name: 'Báo cáo chỉ thị 08 toàn tỉnh'
                  },
                  {
                    languageId: 46,
                    name: 'Báo cáo chỉ thị 08 toàn tỉnh'
                  }
                ],
                route: 'dlk-dac-thu/dlk-baocao-chithi08',
                permission: [
                  {
                      code: 'dlkCT08ToanTinh'
                  }
                ]
            },
            {
                title: [
                    {
                        languageId: 228,
                        name: 'Báo cáo chỉ thị 08 cấp sở'
                    },
                    {
                        languageId: 46,
                        name: 'Báo cáo chỉ thị 08 cấp sở'
                    }
                ],
                route: 'dlk-dac-thu/dlk-baocao-chithi08-capso',
                permission: [
                    {
                        code: 'dlkCT08CapSo'
                    }
                ]
            },
            {
                title: [
                    {
                        languageId: 228,
                        name: 'Báo cáo chỉ thị 08 cấp huyện'
                    },
                    {
                        languageId: 46,
                        name: 'Báo cáo chỉ thị 08 cấp huyện'
                    }
                ],
                route: 'dlk-dac-thu/dlk-baocao-chithi08-caphuyen',
                permission: [
                    {
                        code: 'dlkCT08CapHuyen'
                    }
                ]
            },
            {
                title: [
                    {
                        languageId: 228,
                        name: 'Báo cáo chỉ thị 08 cấp đơn vị'
                    },
                    {
                        languageId: 46,
                        name: 'Báo cáo chỉ thị 08 cấp đơn vị'
                    }
                ],
                route: 'dlk-dac-thu/dlk-baocao-chithi08-capdonvi',
                permission: [
                    {
                        code: 'dlkCT08CapDonVi'
                    }
                ]
            },
            {
                title: [
                    {
                        languageId: 228,
                        name: 'Báo cáo chỉ thị 08 toàn tỉnh cấp sở'
                    },
                    {
                        languageId: 46,
                        name: 'Bao cao chi thi 08 toan tinh cap so'
                    }
                ],
                route: 'dlk-dac-thu/dlk-chithi08-ttcapso',
                permission: [
                    {
                        code: 'dlkCT08TTCapSo'
                    }
                ]
            },
            {
                title: [
                    {
                        languageId: 228,
                        name: 'Báo cáo chỉ thị 08 toàn tỉnh cấp xã'
                    },
                    {
                        languageId: 46,
                        name: 'Bao cao chi thi 08 toan tinh cap xa'
                    }
                ],
                route: 'dlk-dac-thu/dlk-chithi08-ttcapxa',
                permission: [
                    {
                        code: 'dlkCT08TTCapXa'
                    }
                ]
            },
            {
                title: [
                    {
                        languageId: 228,
                        name: 'Báo cáo chỉ thị 08 cấp xã'
                    },
                    {
                        languageId: 46,
                        name: 'Bao cao chi thi 08 cap xa'
                    }
                ],
                route: 'dlk-dac-thu/dlk-chithi08-capxa',
                permission: [
                    {
                        code: 'dlkCT08CapXa'
                    }
                ]
            },
            {
                title: [
                    {
                        languageId: 228,
                        name: 'Theo dõi hồ sơ - Thông tư 01/2018/TT-VPCP'
                    },
                    {
                        languageId: 46,
                        name: 'Theo dõi hồ sơ - Thông tư 01/2018/TT-VPCP'
                    }
                ],
                route: 'dlk-dac-thu/logbook-dlk',
                permission: [
                    {
                        code: 'dlkLogBook'
                    },
                    // {
                    //     code: 'oneGateAdminMaster'
                    // }
                ]
            },
            {
                title: [
                    {
                        languageId: 228,
                        name: 'Thống kê hồ sơ đến hạn'
                    },
                    {
                        languageId: 46,
                        name: 'Thống kê hồ sơ đến hạn'
                    }
                ],
                route: 'dlk-dac-thu/dlk-hosotoihan',
                permission: [
                    {
                        code: 'dlkHoSoToiHan'
                    },
                    // {
                    //     code: 'oneGateAdminMaster'
                    // }
                ]
            },
            {
                title: [
                    {
                        languageId: 228,
                        name: 'Thống kê hồ sơ trễ hạn'
                    },
                    {
                        languageId: 46,
                        name: 'Thống kê hồ sơ trễ hạn'
                    }
                ],
                route: 'dlk-dac-thu/dlk-hosotrehan',
                permission: [
                    {
                        code: 'dlkHoSoTreHan'
                    },
                    // {
                    //     code: 'oneGateAdminMaster'
                    // }
                ]
            },
            {
                title: [
                    {
                        languageId: 228,
                        name: 'Thống kê hồ sơ hủy'
                    },
                    {
                        languageId: 46,
                        name: 'Thống kê hồ sơ hủy'
                    }
                ],
                route: 'dlk-dac-thu/dlk-hosohuy',
                permission: [
                    {
                        code: 'dlkHoSoHuy'
                    },
                    // {
                    //     code: 'oneGateAdminMaster'
                    // }
                ]
            },
              {
                title: [
                  {
                    languageId: 228,
                    name: '[AGG]Thống kê hồ sơ hủy'
                  },
                  {
                    languageId: 46,
                    name: '[AGG]Thống kê hồ sơ hủy'
                  }
                ],
                route: 'statistics/statistic-dossier-cancel-agg',
                permission: [
                  {
                    code: 'oneGateStatisticDossierCancelAgg'
                  }
                ]
              },
            {
              title: [
                  {
                      languageId: 228,
                      name: '[AGG] Thống kê hồ sơ tiếp nhận'
                  },
                  {
                      languageId: 46,
                      name: 'Receive Dossier Statistics AGG'
                  }
              ],
              route: 'statistics/dossier-receive-agg',
              permission: [
                  {
                      code: 'oneGateDossierReceiveAgg'
                  }
              ]
          },
            {
                title: [
                    {
                        languageId: 228,
                        name: 'Tình hình triển khai, xử lý'
                    },
                    {
                        languageId: 46,
                        name: 'Statistics processing & handle'
                    }
                ],
                route: 'statistics/statistic-processing-dlk',
                permission: [
                    {
                        code: 'dlkStatisticProcessing'
                    }
                ]
            },
            {
                title: [
                    {
                        languageId: 228,
                        name: 'Tình hình triển khai, xử lý'
                    },
                    {
                        languageId: 46,
                        name: 'Statistics processing & handle'
                    }
                ],
                route: 'statistics/statistic-processing-dlk-day',
                permission: [
                    {
                        code: 'dlkStatisticProcessingDay'
                    }
                ]
            },
              {
                title: [
                  {
                    languageId: 228,
                    name: '[QNI] Thống kê tình hình, kết quả triển khai dịch vụ công'
                  },
                  {
                    languageId: 46,
                    name: '[QNI] Statistics on the situation and results of public service implementation'
                  }
                ],
                route: 'statistics/statistic-DVCLT-qni',
                permission: [
                  {
                    code: 'oneGateReportDVCLT'
                  }
                ]
              },
              {
                title: [
                  {
                    languageId: 228,
                    name: '[QNI] Thống kê số liệu dịch vụ công liên thông cấp huyện'
                  },
                  {
                    languageId: 46,
                    name: '[QNI] Statistics on district-level public service data'
                  }
                ],
                route: 'statistics/statistic-DVCLT-huyen-qni',
                permission: [
                  {
                    code: 'oneGateReportDVCLTHuyen'
                  }
                ]
              },
                {
                    title: [
                    {
                        languageId: 228,
                        name: 'Báo cáo TTHC'
                    },
                    {
                        languageId: 46,
                        name: 'Statistics detail procedure'
                    }
                    ],
                    route: 'statistics/statistic-tthc',
                    permission: [
                        {
                            code: 'oneGateAdminMaster'
                        },
                        {
                            code: 'oneGateReportTTHC'
                        }
                    ]
                },

            {
                title: [
                    {
                        languageId: 228,
                        name: 'Thống kê hồ sơ Lý lịch Tư Pháp tiếp nhận trực tuyến'
                    },
                    {
                        languageId: 46,
                        name: 'Statistics of Judicial Record records received online'
                    }
                ],
                route: 'statistics/statistic-lltp-dossier',
                permission: [
                    {
                        code: 'oneGateHCMAdmin'
                    },
                    {
                        code: 'oneGateReport'
                    },
                    {
                        code: 'oneGateHCMSYNCLLTPDossier'
                    },
                    {
                        code: 'oneGateHCMSYNCLLTPDossierES'
                    }
                ]
            },
            {
              title: [
                  {
                      languageId: 228,
                      name: '[AGG] Báo cáo thanh toán trực tuyến'
                  },
                  {
                      languageId: 46,
                      name: '[AGG] Report online payment'
                  }
              ],
              route: 'statistics/agg-report-online-payment',
              permission: [
                  {
                      code: 'oneGateOnlinePaymentByPaidDateAGG'
                  }
              ]
            },
            {
                title: [
                  {
                    languageId: 228,
                    name: 'Báo cáo tiếp nhận TTHCC'
                  },
                  {
                    languageId: 46,
                    name: 'Statistic Acept TTHCC'
                  }
                ],
                route: 'statistics/statistic-dossier-tthcc-qbh',
                permission: [
                  {
                      code: 'oneGateReport'
                  },
                  {
                    code: 'oneGateStatisticDossierTTHCCQBH'
                  }
                ]
              },
              {
                title: [
                    {
                        languageId: 228,
                        name: '[AGG] Thống kê hồ sơ đã trả kết quả'
                    },
                    {
                        languageId: 46,
                        name: '[AGG] Statistics dossier returned'
                    }
                ],
                route: 'statistics/agg-dossier-returned',
                permission: [
                    {
                        code: "agg_baocaotraketqua"
                    }
                ]
                },
                {
                title: [
                    {
                        languageId: 228,
                        name: '[AGG] Thống kê lệ phí'
                    },
                    {
                        languageId: 46,
                        name: '[AGG] Dossier fee statistics'
                    }
                ],
                route: 'statistics/dossier-fee-agg',
                permission: [
                    {
                        code: 'oneGateDossierFeeReportAgg'
                    }
                ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Thống kê thư xin lỗi'
                        },
                        {
                            languageId: 46,
                            name: 'Apology letter statistics'
                        }
                    ],
                    route: 'statistics/statistic-apology-letter',
                    permission: [
                        {
                            code: 'onegateStaticsApologyLetter'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: '[AGG] Thống kê theo thủ tục'
                        },
                        {
                            languageId: 46,
                            name: '[AGG] Procedure statistics'
                        }
                    ],
                    route: 'statistics/statistic-procedure-agg',
                    permission: [
                        {
                            code: 'oneGateProcedureStatisticalAGG'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Thống kê kết quả giải quyết thủ tục hành chính'
                        },
                        {
                            languageId: 46,
                            name: 'Statistics on results of administrative procedure settlement'
                        }
                    ],
                    route: 'statistics/statistic-procedure-by-agency',
                    permission: [
                        {
                            code: 'RptResultsofAPRByRAKHAUC204'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Thống kê số liệu theo thủ tục hành chính'
                        },
                        {
                            languageId: 46,
                            name: 'Statistics on results of administrative procedure settlement'
                        }
                    ],
                    route: 'statistics/statistic-by-procedure',
                    permission: [
                        {
                            code: 'RptStatisticsByAdministrativeProceduresKHA'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: '[AGG] Tra cứu danh sách hồ sơ Bộ Tài chính'
                        },
                        {
                            languageId: 46,
                            name: '[AGG] MOF Dossier'
                        }
                    ],
                    route: 'agg-dossier-budget',
                    permission: [
                        {
                            code: 'oneGateBudgetDossierAGG'
                        }
                    ]
                },
              {
                title: [
                    {
                        languageId: 228,
                        name: 'Thống kê hồ sơ VNPOST'
                    },
                    {
                        languageId: 46,
                        name: 'Vnpost dossier statistic'
                    }
                ],
                route: 'statistics/statistic-vnpost-dossier',
                permission: [
                    {
                        code: 'oneGateVNPOSTDossiers'
                    }
                ]
              },
              {
                title: [
                    {
                        languageId: 228,
                        name: 'Thống kê Kết xuất chi tiết hồ sơ'
                    },
                    {
                        languageId: 46,
                        name: 'Statistics BDTC HCM'
                    }
                ],
                route: 'statistics/statistics-bdtc-hcm',
                permission: [
                    {
                        code: 'statisticsBdtcHcm'
                    }
                ]
            },
            {
                title: [
                  {
                    languageId: 228,
                    name: '[AGG] Thống kê theo đơn vị'
                  },
                  {
                    languageId: 46,
                    name: '[AGG] General Statistics'
                  }
                ],
                route: 'statistics/statistic-all-agg',
                permission: [
                  {
                    code: 'oneGateAllStatisticalAGG'
                  }
                ]
            },
            {
                title: [
                  {
                    languageId: 228,
                    name: '[AGG] Thống kê báo cáo TTHCC Mẫu 4 V3'
                  },
                  {
                    languageId: 46,
                    name: '[AGG] Statistic Form 4 V3'
                  }
                ],
                route: 'statistics/statistic-m4-v3-agg',
                permission: [
                  {
                    code: 'statisticM4V3Agg'
                  }
                ]
            },
            {
              title: [
                  {
                      languageId: 228,
                      name: '[AGG] Báo cáo đánh giá cán bộ'
                  },
                  {
                      languageId: 46,
                      name: '[AGG] Survey officer'
                  }
              ],
              route: 'statistics/survey-officer-agg',
              permission: [
                  {
                      code: 'oneGateSurveyOfficerAGG'
                  }
              ]
            },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Biểu đồ thống kê tình hình giải quyết TTHC'
                        },
                        {
                            languageId: 46,
                            name: 'Statistical chart of the situation of handling administrative procedures'
                        }
                    ],
                    route: 'statistics/statistic-qd473',
                    permission: [
                        {
                            code: 'statisticQD473'
                        }
                    ]
                },
                {
                  title: [
                      {
                          languageId: 228,
                          name: '[AGG] Thống kê báo cáo TTHCC Mẫu 5'
                      },
                      {
                          languageId: 46,
                          name: '[AGG] Statistic TTHCC Form 5'
                      }
                  ],
                  route: 'statistics/agg-statistic-tthcc-form5',
                  permission: [
                      {
                          code: 'statisticTTHCCF5Agg'
                      }
                  ]
              },
               //Start IGATESUPP-97889
              {
                title: [
                  {
                    languageId: 228,
                    name: 'Danh sách bàn giao'
                  },
                  {
                    languageId: 46,
                    name: 'General Statistics TransferResultList HGI'
                  }
                ],
                route: 'statistics/danh-sach-ban-giao',
                permission: [
                  {
                      code: 'oneGateTransferResultsList'
                  }
                ]
            },
             //End IGATESUPP-97889

                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Thống kê hồ sơ tiếp nhận HCC một cấp'
                        },
                        {
                            languageId: 46,
                            name: 'Statistics of HCC Accepter at one level'
                        }
                    ],
                    route: 'statistics/statistic-accepter-no-region',
                    permission: [
                        {
                            code: 'oneGateReportAccepterNoRegion'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Thống kê hồ sơ trả kết quả HCC một cấp'
                        },
                        {
                            languageId: 46,
                            name: 'Statistics of HCC Recieve at one level'
                        }
                    ],
                    route: 'statistics/statistic-recieve-no-region',
                    permission: [
                        {
                            code: 'oneGateReportRecieveNoRegion'
                        }
                    ]
                },
                {
                    title: [
                      {
                        languageId: 228,
                        name: 'Hiện trạng triển khai HCC một cấp'
                      },
                      {
                        languageId: 46,
                        name: 'Current status of single-level HCC implementation'
                      }
                    ],
                    route: 'statistics/statistic-deploy-no-region',
                    permission: [
                      {
                          code: 'oneGateReportApplyNoRegion'
                      }
                    ]
                },
                {
                    title: [
                      {
                        languageId: 228,
                        name: 'Thống kê hồ sơ tiếp nhận tại TTHCC'
                      },
                      {
                        languageId: 46,
                        name: 'Statistics of dossier received at TTHCC'
                      }
                    ],
                    route: 'statistics/statistic-dossier-apply-no-region',
                    permission: [
                      {
                          code: 'oneGateReportDosierApplyNoRegion'
                      }
                    ]
                },
                {
                    title: [
                      {
                        languageId: 228,
                        name: 'Dashboard triển khai HCC một cấp'
                      },
                      {
                        languageId: 46,
                        name: 'Dashboard of deployment single-level HCC'
                      }
                    ],
                    route: 'statistics/dashboard-deployment-no-region',
                    permission: [
                      {
                          code: 'oneGateDashboardApplyNoRegion'
                      }
                    ]
                },
                {
                    title: [
                      {
                        languageId: 228,
                        name: '[AGG] Tra cứu hồ sơ đăng ký kinh doanh'
                      },
                      {
                        languageId: 46,
                        name: '[AGG] Business Registration'
                      }
                    ],
                    route: 'search/hoso-dkkd-agg',
                    permission: [
                      {
                        code: 'oneGateBusinessregistrationAgg'
                      }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Báo cáo thống kê hồ sơ tiếp nhận quá 8 giờ'
                        },
                        {
                            languageId: 46,
                            name: 'Statistical dossier overdue reception after 8 hours'
                        }
                    ],
                    route: 'statistics/bctk-hoso-tiepnhan-quahan-8h-vpc',
                    permission: [
                        {
                            code: 'oneGateVpcReportOverdue8h'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Thống kê sổ theo dõi - Hiển thị cột'
                        },
                        {
                            languageId: 46,
                            name: 'Logbook statistics'
                        }
                    ],
                    route: 'statistics/logbook-show-col',
                    permission: [
                        {
                            code: 'RptLogbookStatisticsByColKHAUC195'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Thống kê hồ sơ tiếp nhận qua 8h làm việc'
                        },
                        {
                            languageId: 46,
                            name: 'Statistics of next profile to receive job 8h'
                        }
                    ],
                    route: 'statistics/greater-than8-working-hours',
                    permission: [
                        {
                            code: 'RptGreaterThan8WorkingHoursKHA'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Thống kê tổng hợp số hóa hồ sơ v2'
                        },
                        {
                            languageId: 46,
                            name: 'General statistics and digitization of records v2'
                        }
                    ],
                    route: 'statistics/statistic-THSHHS-v2',
                    permission: [
                        {
                            code: 'statisticTHSHHSv2'
                        }
                    ]
                },
                {
                    title: [
                      {
                        languageId: 228,
                        name: 'Báo cáo nhật ký thao tác người dùng'
                      },
                      {
                        languageId: 46,
                        name: 'User action log report'
                      }
                    ],
                    route: 'statistics/user-action-log',
                    permission: [
                        {
                        code:'oneGateqtiUserActionLog'
                        }
                    ]
                  },
                {
                    title: [
                      {
                        languageId: 228,
                        name: 'Thống kê tài khoản không khai thác CSDLQG'
                      },
                      {
                        languageId: 46,
                        name: 'Inactive account statistics'
                      }
                    ],
                    route: 'statistics/inactive-account-csdldc',
                    permission: [
                        {
                        code:'oneGateInactiveAccountCSDLDC'
                        }
                    ]
                },
                {
                    title: [
                      {
                        languageId: 228,
                        name: 'Thống kê tài khoản không hoạt động'
                      },
                      {
                        languageId: 46,
                        name: 'Inactive account statistics'
                      }
                    ],
                    route: 'statistics/inactive-account',
                    permission: [
                        {
                        code:'oneGateInactiveAccount'
                        }
                    ]
                },
                {
                title: [
                    {
                        languageId: 228,
                        name: 'Thống kê hồ sơ online chưa tiếp nhận'
                    },
                    {
                        languageId: 46,
                        name: 'Statistics online dossier not yet received'
                    }
                    ],
                    route: 'dlk-dac-thu/dlk-hoso-online-chuatiepnhan',
                    permission: [
                        {
                            code: 'dlkHoSoOnlineChuaTiepNhan'
                        }
                    ]
                },
                {
                title: [
                    {
                        languageId: 228,
                        name: 'Báo cáo số hoá',
                    },
                    {
                        languageId: 46,
                        name: 'Statistic Summarize Digitization List ',
                    },
                    ],
                    route: 'dlk-dac-thu/dlk-baocao-sohoa',
                    permission: [
                        {
                            code: 'dlkBaoCaoSoHoav2'
                        }
                    ],
                },
                {
                title: [
                    {
                        languageId: 228,
                        name: 'Tình hình triển khai, xử lý',
                    },
                    {
                        languageId: 46,
                        name: 'Statistics processing & handle',
                    },
                    ],
                    route: 'dlk-dac-thu/dlk-tinhhinh-trienkhai-xuly',
                    permission: [
                        {
                            code: 'dlkTinhHinhTrienKhaiXuLy'
                        }
                    ],
                },
            ]
        },
        // tra cứu dân cư
        {
            mainMenu: [
                {
                    languageId: 228,
                    name: 'Tra cứu CSDLQG về dân cư'
                },
                {
                    languageId: 46,
                    name: 'Statistics / Report'
                }
            ],
            icon: 'people',
            code: 'onegate-database',
            active: false,
            listSubMenu: [
                {
                    title: [
                        {
                            languageId: 228,
                            name: ' Dịch vụ 033(Xác nhận số định danh cá nhân và chứng minh nhân dân)'
                        },
                        {
                            languageId: 46,
                            name: 'Verify personal identification number and identity card'
                        }
                    ],
                    route: 'population-data/auth-identity',
                    permission: [
                        {
                            code: 'oneGateAuthIdentity'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Dịch vụ 034(Xác thực thông tin hộ gia đình)'
                        },
                        {
                            languageId: 46,
                            name: 'Look up household information'
                        }
                    ],
                    route: 'population-data/household-info',
                    permission: [
                        {
                            code: 'oneGateHouseholdInfo'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Dịch vụ 037(Tra cứu thông tin công dân)'
                        },
                        {
                            languageId: 46,
                            name: 'Look up residential information'
                        }
                    ],
                    route: 'population-data/residential-info',
                    permission: [
                        {
                            code: 'oneGateResidentialInfo'
                        }
                    ]
                },
                {
                title: [
                  {
                    languageId: 228,
                    name: 'Báo cáo tổng hợp Tra cứu CSDL Dân Cư'
                  },
                  {
                    languageId: 46,
                    name: 'Report citizen Database Lookup'
                  }
                ],
                route: 'population-data/citizen-database-lookup',
                permission: [
                    {
                        code: 'citizenDatabaseLookupKHA'
                    }
                ]
              },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Thống kê lượt tra cứu thông tin công dân qua CSDLQGVDC'
                        },
                        {
                            languageId: 46,
                            name: 'Statistics of citizen information searches'
                        }
                    ],
                    route: 'population-data/statistics-of-citizen',
                    permission: [
                        {
                            code: 'oneGateStatisticsOfCitizen'
                        }
                    ]
                },
                {
                  title: [
                      {
                          languageId: 228,
                          name: ' PTO Thống kê lượt tra cứu thông tin công dân qua CSDLQGVDC'
                      },
                      {
                          languageId: 46,
                          name: 'Statistics of citizen information searches PTO'
                      }
                  ],
                  route: 'population-data/statistics-of-citizen-hbh',
                  permission: [
                      {
                          code: 'oneGateStatisticsOfCitizenHBH'
                      }
                  ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'VPC Thống kê lượt tra cứu thông tin công dân qua CSDLQGVDC'
                        },
                        {
                            languageId: 46,
                            name: 'Statistics of citizen information searches VPC'
                        }
                    ],
                    route: 'population-data/statistics-of-citizen-by-user',
                    permission: [
                        {
                            code: 'oneGateStatisticsOfCitizenByUser'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Tra cứu thông tin Doanh nghiệp/Hợp tác xã/Hộ kinh doanh'
                        },
                        {
                            languageId: 46,
                            name: 'Business/Cooperative/Household Business Lookup'
                        }
                    ],
                    route: 'population-data/search-info-business',
                    permission: [
                        {
                            code: 'oneGateSearchInformation'
                        }
                    ]
                },
            ]
        },
        {
        mainMenu: [
            {
                languageId: 228,
                name: 'AGG Chức năng đặt thù'
            },
            {
                languageId: 46,
                name: 'Statistics / Report'
            }
            ],
            icon: 'donut_large',
            code: 'onegateAggSpecialFunction',
            active: false,
            listSubMenu: [
            {
                title: [
                {
                    languageId: 228,
                    name: 'AGG Kiểm tra liên thông bộ tư pháp - VNEID'
                },
                {
                    languageId: 46,
                    name: 'Verify personal identification number and identity card'
                }
                ],
                route: 'agg-dac-thu/agg-justice-vneid-check',
                permission: [
                {
                    code: 'onegateAggJusticeVneidCheck'
                }
                ]
            }
            ]
        },
        {
            mainMenu: [
                {
                    languageId: 228,
                    name: 'Quản lý văn bản'
                },
                {
                    languageId: 46,
                    name: 'Document management'
                }
            ],
            icon: 'picture_as_pdf',
            code: 'document-management',
            active: false,
            listSubMenu: [
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Quản  lý cơ quan ban hành'
                        },
                        {
                            languageId: 46,
                            name: 'Agency management'
                        }
                    ],
                    route: 'document-management/agency-management',
                    permission: [
                        {
                            code: 'oneGateAdminMaster'
                        },
                        {
                            code: 'documentManagementRole'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Quản  lý thể loại văn bản'
                        },
                        {
                            languageId: 46,
                            name: 'Category management'
                        }
                    ],
                    route: 'document-management/category-management',
                    permission: [
                        {
                            code: 'oneGateAdminMaster'
                        },
                        {
                            code: 'documentManagementRole'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Quản  lý lĩnh vực văn bản'
                        },
                        {
                            languageId: 46,
                            name: 'Field management'
                        }
                    ],
                    route: 'document-management/field-management',
                    permission: [
                        {
                            code: 'oneGateAdminMaster'
                        },
                        {
                            code: 'documentManagementRole'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Danh sách văn bản'
                        },
                        {
                            languageId: 46,
                            name: 'Document list'
                        }
                    ],
                    route: 'document-management/document-list',
                    permission: [
                        {
                            code: 'oneGateAdminMaster'
                        },
                        {
                            code: 'documentManagementRole'
                        }
                    ]
                }
            ]
        }, // module chung thuc
        {
          mainMenu: [
            {
              languageId: 228,
              name: 'Hồ sơ chứng thực'
            },
            {
              languageId: 46,
              name: 'Authentication dossier'
            }
          ],
          icon: 'book',
          code: 'dossier-auth-data',
          active: false,
          listSubMenu: [
            {
              title: [
                {
                  languageId: 228,
                  name: 'Quản lý sổ danh mục'
                },
                {
                  languageId: 46,
                  name: 'Manage authentication ledger'
                }
              ],
              route: 'dossier-auth/list-ledger-auth',
              permission: [
                {
                  code: 'oneGateDossierAuthManager'
                }
              ]
            },
            {
              title: [
                {
                  languageId: 228,
                  name: 'Quản lý hồ sơ chứng thực'
                },
                {
                  languageId: 46,
                  name: 'Manage authentication dossier'
                }
              ],
              route: 'dossier-auth/list-dossier-auth',
              permission: [
                {
                  code: 'oneGateDossierAuthManager'
                }
              ]
            },
          ]
        }, // end module chung thuc
        {
            mainMenu: [
              {
                languageId: 228,
                name: 'Lưu trữ hồ sơ'
              },
              {
                languageId: 46,
                name: 'Record keeping'
              }
            ],
            icon: 'save',
            code: 'onegate-database',
            active: false,
            listSubMenu: [
              {
                title: [
                  {
                    languageId: 228,
                    name: 'Hồ sơ chờ lưu trữ'
                  },
                  {
                    languageId: 46,
                    name: 'Dossier Waiting Archived'
                  }
                ],
                route: 'istorage/dossier-waiting-archived',
                permission: [
                  {
                    code: 'dossierWaitingArchived'
                  }
                ]
              },
              {
                title: [
                  {
                    languageId: 228,
                    name: 'Hồ sơ đã lưu trữ'
                  },
                  {
                    languageId: 46,
                    name: 'Dossier Archived'
                  }
                ],
                route: 'istorage/dossier-Archived',
                permission: [
                  {
                    code: 'dossierArchived'
                  }
                ]
              },
            ]
          },
          {
            mainMenu: [
                {
                    languageId: 228,
                    name: 'Thu phí, lệ phí hồ sơ'
                },
                {
                    languageId: 46,
                    name: 'Charge fees dossier'
                }
            ],
            icon: 'paid',
            code: 'charge-fees',
            active: false,
            listSubMenu: [
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Thu phí, lệ phí hồ sơ'
                        },
                        {
                            languageId: 46,
                            name: 'Charge fees dossier'
                        }
                    ],
                    route: 'charge-fees/apply-fee-dossiers',
                    permission: [
                        {
                            code: 'oneGateAdminMaster'
                        },
                        {
                            code: 'onegateApplyFeeDossiers'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Báo cáo thu phí'
                        },
                        {
                            languageId: 46,
                            name: 'Báo cáo thu phí'
                        }
                    ],
                    route: 'charge-fees/statistic-charge-fee',
                    permission: [
                        {
                            code: 'oneGateAdminMaster'
                        },
                        {
                            code: 'onegateReportApplyFeeDossiers'
                        }
                    ]
                },

            ]
        },
    ];
    logoDefaultApp = '';
    listApps = [];
    listOnegateApps = [];

    position: string;
    codePosition: string;
    UID: string;
    roleUser = [];
    count = 0;
    countSubmenu = 0;
    countAvailableMenu = [];
    countAvailableSubmenu = [];
    countRoute = 0;
    userId: any = '';
    accountId: any = '';
    listDossierRemind: any = [];
    totalDossierRemind: any = 0;
    userPermissions = [];
    notificationEnable = 0;
    listAppEnable = 0;
    siteContent = {
        logoUrl: '',
        faviconUrl: '',
        name: {
            vi: '',
            en: ''
        },
        backgroundColor: '#ce7a58'
    };
    sidenav = {
        backgroundImg: ''
    };
    footerContent = {
        name: {},
        address: {},
        logoUrl: '',
        right: {
            name: {
            },
            logoUrl: '',
            version: {
            }
        }
    };
    userAgencyLocal?: any = JSON.parse(localStorage.getItem('userAgency'));
    userAgency = [];
    userAgencyProcess: any;
    homeUrl = null;
    selectedOption: any = '';
    userAgencyPositionName = '';
    userPositionName = '';

    mobileQuery: MediaQueryList;
    private mobileQueryListener: () => void;
    @ViewChild('snav') public snav: MatSidenav;

    dossierCode = new FormControl('');

    configs = this.deploymentService.getAppDeployment();
    enableIoffice = this.env?.OS_TGG?.ioffice?.enable === true ? this.env?.OS_TGG?.ioffice?.enable : false;
    //Lấy cấu hình cho phép hiện menu tra cứu log LGSP HCM
    showLGSPHCMLog = this.env?.OS_HCM?.showLGSPHCMLog === true ? this.env?.OS_HCM?.showLGSPHCMLog : false;
    showImportDossierStatus = this.env?.OS_HCM?.showImportDossierStatus === true ? this.env?.OS_HCM?.showImportDossierStatus : false;
    showSyncProcedureLGSPHCM = this.env?.OS_HCM?.showSyncProcedureLGSPHCM === true ? this.env?.OS_HCM?.showSyncProcedureLGSPHCM : false;
    showDossierLGSPHCMSync = this.env?.OS_HCM?.showDossierLGSPHCMSync === true ? this.env?.OS_HCM?.showDossierLGSPHCMSync : false;

    isQBH = this.env?.OS_QBH?.isQBH === true ? this.env?.OS_QBH?.isQBH : false;
    isQNINotify = this.env?.OS_QNI?.isQNINotify == 1 ? true : false;
    showTooltipAgencyPositionName = this.env?.showTooltipAgencyPositionName ? this.env?.showTooltipAgencyPositionName : false;
    enableApprovaledAgencyTreeView = this.env?.enableApprovaledAgencyTreeView ? this.env.enableApprovaledAgencyTreeView : false;

    typeComboBoxLanguage: number;

    //IGATESUPP-108394
    menuSpecificforBNV = this.deploymentService.getAppDeployment()?.menuSpecificforBNV ? this.deploymentService.getAppDeployment()?.menuSpecificforBNV : false;

    enableMenuGuideSiteOneGate = this.deploymentService.env.OS_HCM.enableMenuGuideSiteOneGate == 1 ? 1 : 0;
    showNPSLogHCM = this.env?.OS_HCM?.showNPSLogHCM === true ? this.env?.OS_HCM?.showNPSLogHCM : false;
    showMenu_DosPersonal = this.deploymentService.env.OS_HCM.showMenu_DosPersonal;
    enableMenuMinistryOfTransport = this.deploymentService.env?.OS_HCM?.enableMenuMinistryOfTransport === true ? this.deploymentService.env?.OS_HCM?.enableMenuMinistryOfTransport: false;
    enableMenuRequestCertification = this.deploymentService.env?.OS_HCM?.enableMenuRequestCertification == true ? this.deploymentService.env?.OS_HCM?.enableMenuRequestCertification:false;
    enableModifiedDossierGtvtQni = this.deploymentService.env?.OS_QNI?.enableModifiedDossierGtvtQni || false;
    hiddenWithdrawnRemind = this.deploymentService.env?.hiddenWithdrawnRemind === true ? this.deploymentService.env?.hiddenWithdrawnRemind: false;
    withdrawnRemindId = this.deploymentService.env?.withdrawnRemindId;
    //qni text
    QniHeThongText = this.deploymentService.env?.OS_QNI?.QniHeThongText;
    //phucnh.it2-IGATESUPP-42030_doi ten menu BanQLATTP


    changeMenuBanATTP = this.deploymentService.env.OS_HCM.changeMenuBanATTP;
    enableChangeNameAttp201 = this.changeMenuBanATTP.enableChangeNameAttp201;
    enableChangeNameAttp202 = this.changeMenuBanATTP.enableChangeNameAttp202;
    enableChangeNameAttp203 = this.changeMenuBanATTP.enableChangeNameAttp203;
    titleAttp201 = [
                    {
                        languageId: 228,
                        name: this.enableChangeNameAttp201 == true ? this.changeMenuBanATTP.attp201 : 'BÁO CÁO KIỂM DỊCH SẢN PHẨM ĐỘNG VẬT RA KHỎI ĐỊA BÀN THÀNH PHỐ HỒ CHÍ MINH'
                    },
                    {
                        languageId: 46,
                        name: this.enableChangeNameAttp201 == true ? this.changeMenuBanATTP.attp201e : 'CHECKLIST REPORT OF ANIMAL PRODUCTS FROM HO CHI MINH CITY'
                    }
                ];
    titleAttp202 = [
                    {
                        languageId: 228,
                        name: this.enableChangeNameAttp202 == true ? this.changeMenuBanATTP.attp202 :  'BÁO CÁO CHI TIẾT CẤP GIẤY CHỨNG NHÂN KIỂM DỊCH TẠI CÁC ĐỘI'
                    },
                    {
                        languageId: 46,
                        name: this.enableChangeNameAttp202 == true ? this.changeMenuBanATTP.attp202e : 'DETAILS REPORT ISSUANCE OF VERIFICATION CERTIFICATES IN TEAMS'
                    }
                ];
    titleAttp203 = [
                {
                    languageId: 228,
                    name: this.enableChangeNameAttp203 == true ? this.changeMenuBanATTP.attp203 :  'BÁO CÁO TỔNG HỢP CẤP GIẤY KIỂM DỊCH CHO CỞ SỞ'
                },
                {
                    languageId: 46,
                    name: this.enableChangeNameAttp203 == true ? this.changeMenuBanATTP.attp203e : 'SUMMARY REPORT ISSUANCE OF CHECKLIST PAPER FOR FACILITIES'
                }
            ];
    //IGATESUPP-41788: phucnh.it2: Thêm menu thống kê theo hình thức nhận kết quả vào site Onegate
    enableMenuStatisticsReceivingSiteOneGate = this.deploymentService.env.OS_HCM.enableMenuStatisticsReceivingSiteOneGate;
    //IGATESUPP-44995: Bật/ tắt menu thống kê phí biên lai HCM
    enableMenuReceiptFeeStatistics = this.deploymentService.env.OS_HCM.enableMenuReceiptFeeStatistics
    //phucnh.it2-IGATESUPP-44995: Bật/ tắt menu thống kê phí biên lai HCM
    enableMenuDossierStatisticsSYT = this.deploymentService.env.OS_HCM.enableMenuDossierStatisticsSYT;
    //phucnh.it2-IGATESUPP-59045-ThongKeHsSYT theo ngày hẹn trả
    enableMenuDossierStatisticsByAppointmentDateHCM = this.deploymentService.env.OS_HCM.enableMenuDossierStatisticsByAppointmentDateHCM;
    //phucnh.it2-IGATESUPP-64084-TraCuuLogVnpost
    enableVnpostLogFunction = this.deploymentService.env.OS_HCM.enableVnpostLogFunction;
    //phucnh.it2-IGATESUPP-67352-Menu Thong ke ho so bo sung
    enableStatisticDossierAdditionMenu = this.deploymentService.env.OS_HCM.enableStatisticDossierAdditionMenu
    //phucnh.it2-IGATESUPP-70766: đơn vị có trong list sẽ xem dc form đồng bộ trạng thái
    syncCheckPaymentStatusMenu = this.deploymentService.env.OS_HCM.syncCheckPaymentStatusMenu;
    //IGATESUPP-47961: thêm subnoti với các đơn vị được cấu hình
    dossierMenuTaskRingWithAgency = this.deploymentService.env.OS_HCM.DossierMenuTaskRingWithAgency ? this.deploymentService.env.OS_HCM.DossierMenuTaskRingWithAgency :
       {
        NearbyDueAcceptedWhen: 2,
        OverDueAcceptedWhen: 0,
        NearbyDueProcessingWhen: 2,
        OverDueProcessingWhen: 2,
        listAgency: [
            {
            AgencyID: "62f31441e13bef0ed2e582ca",
            NearbyDueAcceptedWhen: 4,
            OverDueAcceptedWhen: 0,
            NearbyDueProcessingWhen: 2,
            OverDueProcessingWhen: 0
            }
        ]
       }
       //showSubMenuTaskRingByAgency = this.deploymentService.env.OS_HCM.showSubMenuTaskRingByAgency ? this.deploymentService.env.OS_HCM.showSubMenuTaskRingByAgency : false;
       checkShowSubMenuNotiValue = this.checkShowSubMenuNoti();
       listAgencyCountDown8h = this.env?.OS_HCM.listAgencyCountDown8h ? this.env?.OS_HCM.listAgencyCountDown8h : [];
       subMenuRemindNoti = [];
       totalSubNotiMenu = 0;

    //end phucnh.it2-IGATESUPP-42030_doi ten menu BanQLATTP
    filterByPositionAgencyType = this.deploymentService.env.OS_QNM.filterByPositionAgencyType;  //IGATESUPP-51745- huuminh: [QNM] Bật/Tắt hiển thị chức vụ thay vì đơn vị
    selectAgencyId = localStorage.getItem('selectAgencyId') == null ? "" :localStorage.getItem('selectAgencyId'); //IGATESUPP-51745- huuminh: Lưu thông tin khi chọn chức vụ/ cơ quan


    isFullListSector = false;
    private listSector: any[] = [];
    listSectorPage = 0;
    searchSectorKeyword = '';
    paginationType = this.deploymentService.env.paginationType;
    protected sectors: any[] = this.listSector;
    public sectorFiltered: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
    listSectorId: any;
    listsectorSearch = [];
    listSectorUser = "";
    enableSectorUserSearch = this.deploymentService.env.OS_HCM.enableSectorUserSearch;
    enableSearchBySector = this.deploymentService.env.OS_BDG.enableSearchBySector;
    filterByCandidateGroup = !!this.env?.OS_QNM?.filterByCandidateGroup ? true : false;
    hideLanguageHeader = this.deploymentService.header.hideLanguage;


    isNotification = this.deploymentService.env.notification.isNotification;
    isNotificationRing = this.deploymentService.env.notification.isNotificationRing;
    isKTMDigitalAuthEnable  = this.env?.OS_KTM.isKTMDigitalAuthEnable  === true ? this.env?.OS_KTM.isKTMDigitalAuthEnable  : false;
    hiddenStatusDossierRemindOnRingHGG = this.deploymentService.env.OS_HGG.hiddenStatusDossierRemindOnRingHGG;
    isKGG = this.env?.OS_KGG?.isKGG ? true : false;
    isCMU = this.env?.OS_CMU?.isCMU ? true : true;
    menuSpecificforGLI = this.deploymentService?.getAppDeployment()?.menuSpecificforGLI ? true : false;

    isSyncConstructHPG = this.deploymentService?.syncConstructHPG?.isSyncConstructHPG;

    //IGATESUPP-106060 Sở Tư Pháp - Lưu log các API của luồng xử lý hồ sơ LLTP VNeID
    showMenuLGSPHCMLLTPVNeIDLog =  this.deploymentService.getAppDeployment()?.showMenuLGSPHCMLLTPVNeIDLog ?  this.deploymentService.getAppDeployment().showMenuLGSPHCMLLTPVNeIDLog : 0;

    isShowReportDossierDueQnm = this.deploymentService.getAppDeployment()?.isShowReportDossierDueQnm ?  this.deploymentService.getAppDeployment()?.isShowReportDossierDueQnm : false;
    isClean = false;
    showCleanUser = this.deploymentService.getAppDeployment()?.env?.showCleanUser;
    constructor(
        keycloakService: KeycloakService,
        changeDetectorRef: ChangeDetectorRef,
        media: MediaMatcher,
        private dialog: MatDialog,
        private userService: UserService,
        private mainService: MainService,
        private approvalAgencyConfigService: ApprovalAgencyConfigService,
        private sanitizer: DomSanitizer,
        private router: Router,
        private activatedRoute: ActivatedRoute,
        public mediaObserver: MediaObserver,
        private envService: EnvService,
        private titleService: Title,
        private deploymentService: DeploymentService,
        private dossierService: DossierService,
        private sectorService : SectorService,
        private procedureService: ProcedureService,
        private eDocService: EDocService,
        private http: HttpClient,
        private apiProviderService: ApiProviderService,
        private chungThucDienTuService: ChungThucDienTuService,
        private handler: HttpBackend,
        private surfeedService: SurfeedService,
        private notificationV2Service: NotificationV2Service,
        private dlkStatisticService: DLKStatisticService,
        private eventSharedService: EventSharedService,
        @Inject(LOCALE_ID) protected localeId: string
    ) {
        this.keycloakService = keycloakService;
        this.mobileQuery = media.matchMedia('(max-width: 600px)');
        this.mobileQueryListener = () => changeDetectorRef.detectChanges();
        // tslint:disable-next-line: deprecation
        this.mobileQuery.addListener(this.mobileQueryListener);
        this.http = new HttpClient(this.handler);
    }

    async ngOnInit(): Promise<void> {
        const permissions = this.userService.getUserPermissions();
        let showMenuStatisticDigitized= false
        let isShowProcedureProcessingTime=false
        for(const p of permissions){
            if(p.permission.code ==="procedureProcessingTime"){
                isShowProcedureProcessingTime = true
                break;
            }
        }
        for(const p of permissions){
            if(this.listPermissionOpt.includes(p.permission.code)){
                this.hasPermissionOpt = true;
                break;
            }
        }
        for (const p of permissions) {
            if (p.permission.code === 'oneGateStatisticDigitized' || p.permission.code ==='oneGateAdminMaster') {
                showMenuStatisticDigitized = true;
                break;
            }
        }
        if(this.deploymentService.env.OS_HCM?.enableShowMenu012020NewRequireAdd == true){
            this.sidebarMenu[3].listSubMenu.push({
                title: [
                    {
                        languageId: 228,
                        name: 'Thống kê theo thông tư 01/2020/TT-VPCP HCMC'
                    },
                    {
                        languageId: 46,
                        name: 'Circular 01/2020/TT-VPCP statistics HCMC'
                    }
                ],
                route: 'statistics/statistic-01-2020-hcmc',
                permission: [
                    {
                        code: 'oneGateReport'
                    },
                    {
                        code: 'oneGate012020Statistical'
                    }
                ]
            });
        }
        this.keycloakService.isLoggedIn().then(r => {
            this.isLoggedIn = r;
        });
        this.setOpenAccordion(this.router.url);

        if (this.enableMenuGuideSiteOneGate == 1) {
            this.addMenuGuideSiteOneGate();
        }

        if(this.enableMenuMinistryOfTransport){
            if(this.enableModifiedDossierGtvtQni){
                this.addMenuDossierSyncGTVTQni();
            }else{
                this.addMenuDossierSyncGTVT();
            }
        }

        if(this.enableMenuRequestCertification){
            this.addMenuRequestCertification();
        }
        this.addMenuOwnerRatingsSiteOneGate();



        this.sidebarMenu.forEach((e, index) => {
            e.listSubMenu.forEach(r => {
                if (this.enableChangeNameAttp201 && r.route == 'statistics/hcm-BQL-ATTP2-01'){
                    r.title = this.titleAttp201;
                }
                if (this.enableChangeNameAttp202 && r.route == 'statistics/hcm-BQL-ATTP2-02'){
                    r.title = this.titleAttp202;
                }
                if (this.enableChangeNameAttp203 && r.route == 'statistics/hcm-BQL-ATTP2-03'){
                    r.title = this.titleAttp203;
                }
                if (this.deploymentService.newConfigV2?.enableStatisticsOfCitizenV2 && r.route == 'population-data/statistics-of-citizen'){
                    r.route = 'population-data/statistics-of-citizen-v2';
                }
            })
         })

        this.addMenuStatisticsReceivingResultsToSiteOneGate();

        //phucnh.it2-IGATESUPP-67352
        if (this.enableStatisticDossierAdditionMenu){
            this.addStatisticDossierAdditionMenu();
        }

        //phucnh.it2-IGATESUPP-44995
        if (this.enableMenuReceiptFeeStatistics){
            this.addMenuReceiptFeeStatisticsToSiteOneGate();
        }

        //phucnh.it2-IGATESUPP-49255
        if (this.enableMenuDossierStatisticsSYT){
            this.addMenuMenuDossierStatisticsSYTToSiteOneGate();
        }
        //phucnh.it2-IGATESUPP-59045-ThongKeHsSYT theo ngày hẹn trả
        if (this.enableMenuDossierStatisticsByAppointmentDateHCM){
            this.addMenuDossierStatisticsByAppointmentDateHCMOneGate();
        }

        //phucnh.it2-IGATESUPP-64084-//Cho phép bật chức năng tra cứu log vnpost
        if (this.enableVnpostLogFunction){
            this.addMenuVnpostLogFunctionOneGate();
        }

        if (this.syncCheckPaymentStatusMenu.enable == true) {
            this.addMenuSyncCheckPaymentStatus();
        }

        if(this.isSyncConstructHPG ){
            this.addTraCuuBXDHPG();
        }


        this.router.events.subscribe((event: Event) => {
            if (event instanceof NavigationStart) {
                this.setOpenAccordion(event.url);
            }
        });

        this.roleUser = this.keycloakService.getUserRoles(true);
        const userAgencyProcess = JSON.parse(localStorage.getItem('userAgency'));

        if (!!userAgencyProcess) {
            this.userAgencyProcess = userAgencyProcess;
            await this.getListApprovalAgencyConfig(userAgencyProcess.id);
        }
        if(this.qbhrenamemenu)
        {

            const titlecQBH = [
                {
                    languageId: 228,
                    name: 'Hồ sơ không giải quyết'
                },
                {
                    languageId: 46,
                    name: 'Dossier cancel'
                }
              ];
              const titleaQBH = [
                {
                    languageId: 228,
                    name: 'Tra cứu hồ sơ theo phòng, ban, đơn vị'
                },
                {
                    languageId: 46,
                    name: 'Search dossier by agency'
                }
              ];
            this.sidebarMenu.forEach((e, index) => {
                e.listSubMenu.forEach(r => {
                    if ( r.route == 'dossier/cancel'){
                        r.title = titlecQBH;
                    }
                    if ( r.route == 'dossier/search-by-agency'){
                        r.title = titleaQBH;
                    }
                })
             });

        }
        // this.isAccountIntegration = this.env?.OS_KTM?.isAccountIntegration;
        const userInfoData = this.activatedRoute.snapshot.data.userInfo;
        this.UID = userInfoData.UID;
        this.userAgency = userInfoData.userAgency;
        this.selectedOption = userInfoData.selectedOption;
        this.siteName = userInfoData.siteName;
        this.agencyName = userInfoData.agencyName;
        this.accountId = userInfoData.accountId;
        this.userPermissions = userInfoData.userPermissions;
        this.typeComboBoxLanguage = this.env?.OS_BDG?.typeComboBoxLanguage;
        sessionStorage.setItem('userInfoData', userInfoData);
        if(this.filterByPositionAgencyType){ // Luồng cho QNM
        this.userAgency.forEach(x=>{
            x.id = x.agency.id +"-"+x.position?.id
        })
        this.selectedOption = this.selectAgencyId == "" ? this.userAgency[0].id: this.selectAgencyId;
        }

        //CMU hiển thị siteName nếu đơn vị có tagId là Đơn vị đo đạc đặc thù của Cà Mau
        if (this.userAgency && this.userAgency.length > 0) {
            if(this.deploymentService.env.OS_CMU.tagIdAgencyCustomSiteTitle != null && this.deploymentService.env.OS_CMU.siteNameCustomTitle != null){
                let tagIdAgencyCustomSiteTitle = this.deploymentService.env.OS_CMU.tagIdAgencyCustomSiteTitle;
                let agencyCustomSiteTitle = this.deploymentService.env.OS_CMU.siteNameCustomTitle;
                this.userAgency.forEach(x => {
                    if (x.agency && x.agency.tagId && x.agency.tagId === tagIdAgencyCustomSiteTitle) {
                        this.siteName = agencyCustomSiteTitle;
                    }
                });
            }
        }

        if (this.isDNI) {
            this.addMenuControlSyncForDNI();
            this.addMenuStatisticReceivedForDNI();
        }

        // IGATESUPP-59871 HCM Thêm menu thông báo popup
        if (this.deploymentService.env.OS_HCM.showAnnouncementPopup) {
            this.addMenuAnnouncement();
            this.getAnnoucementPopup();
        }

        // add menu đặc thù Quảng Nam
        if (this.isQNM)
        {
            this.addMenuSpecificforQNM(); // thêm mới menu đặc thù cho Quảng Nam
        }

        // minhvnt 20/04/2023 => add menu đặc thù Quảng Bình
        if (this.isQBH)
        {
            this.addMenuSpecificforQBH(); // thêm mới menu đặc thù cho Quảng Bình
            this.addMenuQBHSiteOneGate(); // thêm mới menu đặc thù cho Quảng Bình
            this.addMenuQBHBusinessRegistration();
        }
        if(this.isKTMDigitalAuthEnable ){
            this.addMenuChungThuc();
        }
        if (this.isKGG)
        {
            this.addMenuKGGSiteOneGate(); // thêm mới menu đặc thù cho Kiên Giang
        }

        if (this.isCMU){
            this.addMenuCMUSiteOneGate(); // thêm mới menu đặc thù cho Cà Mau
        }
        // IGATESUPP-113272
        if (this.useDNIDocumentNumberingLedger) {
            this.removeMenuLedger();
        }

        if(this.menuSpecificforGLI){
            this.addMenuSpecificforGLI();
        }

        //add Menu Notify
        if (this.isQNINotify)
            this.addMenuNotifyforQNI()

        // add menu đặc thù Bộ Nội Vụ, remove các phần không cần thiết
        if (this.menuSpecificforBNV){
            this.addMenuSpecificforBNV()
            this.setOpenAccordion(this.router.url);
        }

        this.sidebarMenu.forEach((e, index) => {
            //Lấy cấu hình và hiển thị menu tra cứu log LGSP HCM
            if (this.showLGSPHCMLog == true) {
                if (e.code == 'dossier') {
                    e.listSubMenu.push({
                        title: [
                            {
                                languageId: 228,
                                name: 'Tra cứu Log LGSP HCM'
                            },
                            {
                                languageId: 46,
                                name: 'LGSP HCM Log'
                            }
                        ],
                        route: 'dossier/lgsp-hcm-log',
                        permission: [
                            {
                                code: 'oneGateAdminMaster'
                            },
                            {
                                code: 'oneGateDossierManager'
                            },
                            {
                                code: 'oneGateDossierConfiguration'
                            },
                            {
                                code: 'oneGateLGSPChecker'
                            }
                        ]
                    });
                }
            }

            if (this.showMenuLGSPHCMLLTPVNeIDLog == 1) {
                if (e.code == 'dossier') {
                    e.listSubMenu.push({
                        title: [
                            {
                                languageId: 228,
                                name: 'Tra cứu Log LGSP HCM LLTP VNeID'
                            },
                            {
                                languageId: 46,
                                name: 'LGSP Judicial Record HCM VNeID Log'
                            }
                        ],
                        route: 'dossier/lgsp-hcm-lltp-vneid-log',
                        permission: [
                            {
                                code: 'oneGateLogLGSPLLTPVneID'
                            }
                        ]
                    });
                }
            }

            if (this.showSyncProcedureLGSPHCM == true) {
                if (e.code == 'onegate-database' && e.icon == 'sync') {
                    e.listSubMenu.push({
                        title: [
                            {
                                languageId: 228,
                                name: 'Đồng bộ thủ tục cổng Dịch vụ công Quốc gia (LGSP HCM)'
                            },
                            {
                                languageId: 46,
                                name: 'National Public Administrative Service\'s Procedure (LGSP HCM)'
                            }
                        ],
                        route: 'sync/lgsphcm-sync',
                        permission: [
                            {
                                code: 'oneGateAdminMaster'
                            },
                            {
                                code: 'oneGateNpadsvcSync'
                            }
                        ]
                    });
                }
            }

            if (this.showNPSLogHCM == true) {
                if (e.code == 'onegate-database' && e.icon == 'sync') {
                    e.listSubMenu.push({
                        title: [
                            {
                                languageId: 228,
                                name: 'Tra cứu log đồng bộ hồ sơ lên cổng DVCQG'
                            },
                            {
                                languageId: 46,
                                name: 'Look up log sync dossier on the National Public Service portal'
                            }
                        ],
                        route: 'sync/log-sync-nps',
                        permission: [
                            {
                                code: 'oneGateAdminMaster'
                            },
                            {
                                code: 'oneGateNpadsvcSync'
                            }
                        ]
                    });
                }
            }

            if(this.showImportDossierStatus == true)
            {
                if(e.code == 'onegate-database' && e.icon == 'sync')
                {
                    e.listSubMenu.push({
                        title: [
                            {
                                languageId: 228,
                                name: 'Import đồng bộ trạng thái DVCQG'
                            },
                            {
                                languageId: 46,
                                name: 'Import Sync Status National Public Service'
                            }
                        ],
                        route: 'sync/dossier-status-import',
                        permission: [
                            {
                                code: 'oneGateAdminMaster'
                            },
                            {
                                code: 'oneGateNpadsvcSync'
                            }
                        ]
                    });
                }
            }
            // 20/12/2022 Lay cau hinh va hien thi menu Thong Ke HoSo Ca Nhan
            // tslint:disable-next-line:align
            if (this.showMenu_DosPersonal === true) {
              if (e.code === 'onegate-database' && e.icon === 'pie_chart') {
                e.listSubMenu.push({
                  title: [
                    {
                      languageId: 228,
                      name: 'Thống kê hồ sơ cá nhân'
                    },
                    {
                      languageId: 46,
                      name: 'Personal dossier statistics'
                    }
                  ],
                  route: 'statistics/dossier-personal',
                  permission: [
                    {
                      code: 'oneGateAdminMaster'
                    },
                    {
                      code: 'oneGateOverdueDossierReport'
                    }
                  ]
                });
              }
            } // end if this.showMenu_DosPersonal == true

            //Thêm menu thống kê hồ sơ số hóa
            if(showMenuStatisticDigitized == true){
                if(e.code ==='onegate-database'&& e.icon ==='pie_chart'){
                    e.listSubMenu.push({
                        title: [
                          {
                            languageId: 228,
                            name: 'Thống kê hồ sơ số hóa'
                          },
                          {
                            languageId: 46,
                            name: 'Statistic Digitized'
                          }
                        ],
                        route: 'statistics/statistic-digitized',
                        permission: [
                            {
                                code: 'oneGateAdminMaster'
                            },
                            {
                            code:'oneGateStatisticDigitized'
                            }
                        ]
                      });
                }
            }
            if(showMenuStatisticDigitized == true){
                if(e.code ==='onegate-database'&& e.icon ==='pie_chart'){
                    e.listSubMenu.push({
                        title: [
                          {
                            languageId: 228,
                            name: 'Thống kê tổng hợp hồ sơ số hóa'
                        },
                          {
                            languageId: 46,
                            name: 'Statistic All Digitized '
                          }
                        ],
                        route: 'statistics/statistic-digitized-hbh',
                        permission: [
                            {
                            code:'oneGateStatisticDigitzedHBH'
                            }
                        ]
                      });
                      e.listSubMenu.push({
                        title: [
                          {
                            languageId: 228,
                            name: 'PTO Thống kê tổng hợp hồ sơ số hóa TPHS'
                        },
                          {
                            languageId: 46,
                            name: 'Statistic All Digitized PTO'
                          }
                        ],
                        route: 'statistics/statistic-digitized-form-hbh',
                        permission: [
                            {
                            code:'oneGateStatisticDigitzedFormHBH'
                            }
                        ]
                      });
                }
            }

            if(isShowProcedureProcessingTime == true){
                if(e.code ==='onegate-database'&& e.icon ==='pie_chart'){
                    e.listSubMenu.push({
                        title: [
                          {
                            languageId: 228,
                            name: 'Thống kê Thủ tục hành chính'
                          },
                          {
                            languageId: 46,
                            name: 'Procedure processing time'
                          }
                        ],
                        route: 'statistics/procedure-processing-time',
                        permission: [
                            {
                                code:'procedureProcessingTime'
                            }
                        ]
                      });
                }
            }


            if(this.showDossierLGSPHCMSync == true)
            {
                if(e.code == 'onegate-database' && e.icon == 'sync')
                {
                    e.listSubMenu.push({
                        title: [
                            {
                                languageId: 228,
                                name: 'Đồng bộ hồ sơ cổng Dịch vụ công Quốc gia của Hồ Chí Minh'
                            },
                            {
                                languageId: 46,
                                name: 'National Public Administrative Service\'s Dossier for Hồ Chí Minh City'
                            }
                        ],
                        route: 'sync/dossier-sync',
                        permission: [
                            {
                                code: 'oneGateAdminMaster'
                            },
                            {
                                code: 'oneGateDossierSync'
                            }
                        ]
                    });
                }
            }else{
                if(e.code == 'onegate-database' && e.icon == 'sync'){
                    e.listSubMenu.push({
                        title: [
                            {
                                languageId: 228,
                                name: this.dossierSyncName
                            },
                            {
                                languageId: 46,
                                name: 'National Public Administrative Service\'s Dossier'
                            }
                        ],
                        route: 'sync/dossier-sync',
                        permission: [
                            {
                                code: 'oneGateAdminMaster'
                            },
                            {
                                code: 'oneGateDossierSync'
                            }
                        ]
                    },);
                }
            }

            if (this.deploymentService.env.OS_QBH.isShowBtnAutoReceiveQBH == true) {
                if (e.code == 'dossier') {
                    e.listSubMenu.push({
                        title: [
                            {
                                languageId: 228,
                                name: 'Tự động tiếp nhận hồ sơ QBH'
                            },
                            {
                                languageId: 46,
                                name: 'Auto Receive Dosser'
                            }
                        ],
                        route: 'dossier/auto-receive-dossier-qbh',
                        permission: [
                            {
                                code: 'auto_tiepnhan'
                            }
                        ]
                    });
                }
            }

            // Chỉnh url của báo cáo đến hạn thêm -qnm
            if(this.isShowReportDossierDueQnm){
                if(e.code ==='onegate-database'&& e.icon ==='pie_chart'){
                   let itemMenu = e.listSubMenu.find(x=>x.route == 'statistics/dossier-due');
                   if(itemMenu == null){
                    e.listSubMenu.push({
                        title: [
                            {
                                languageId: 228,
                                name: '[QMM] Thống kê hồ sơ đến hạn'
                            },
                            {
                                languageId: 46,
                                name: 'Due dossier statistics'
                            }
                        ],
                        route: 'statistics/dossier-due-qnm',
                        permission: [
                            {
                                code: 'oneGateDueDossierReport'
                            },
                        ]
                    });
                   }else{
                     itemMenu.route = 'statistics/dossier-due-qnm';
                   }
                }
            }

            //Thêm menu thống kê hồ sơ số hóa BDH
            if(showMenuStatisticDigitized == true){
                if(e.code ==='onegate-database'&& e.icon ==='pie_chart'){
                    e.listSubMenu.push({
                        title: [
                            {
                            languageId: 228,
                            name: 'Thống kê hồ sơ số hóa Gia Lai'
                            },
                            {
                            languageId: 46,
                            name: 'Statistic Digitized'
                            }
                        ],
                        route: 'statistics/statistic-digital-bdh',
                        permission: [
                            {
                                code: 'oneGateReport'
                            },
                            {
                            code:'oneGateStatisticDigitizedBDH'
                            }
                        ]
                        });
                }
            }
            e.listSubMenu.forEach(r => {
                Object.assign(r, { isActive: false });
                r.permission.forEach(q => {
                    if (this.userPermissions.filter(uP => uP.code === q.code).length > 0) {
                        this.count = this.count + 1;
                        // tslint:disable-next-line: no-string-literal
                        r['isActive'] = true;
                    }
                });
            });
            this.countAvailableMenu.push(this.count);
            this.count = 0;
        });

        this.chungThucMenu.forEach(r => {
          Object.assign(r, { isActive: false });
          r.permission.forEach(q => {
              if (this.userPermissions.filter(uP => uP.code === q.code).length > 0) {
                  this.count = this.count + 1;
                  // tslint:disable-next-line: no-string-literal
                  r['isActive'] = true;
              }
          });
        });

        this.getAvatar();

        if (this.showCleanUser) {
          const userId = localStorage.getItem('tempUID');
          await this.userService.getFullUserInfo(userId).subscribe(data => {
            if (!!data?.clean) {
              this.isClean = true;
            }
          });
        }

        if (this.enableIoffice) {
            await this.eDocService.getDocumentPacket().toPromise().then(async data => {
            }).catch(error => {
                console.log(error);
            });
        }

        this.linkRemind = new URL(window.location.href).origin;
        this.mainService.sideNav = this.snav;
        // this.getListOnegateApps();
        this.checkUserPermissions();
        this.getPageConfiguration();
        this.getListAccountApps();
        this.getAgencyUserPositionName();
        this.getDossierDueQuantity();

        // const firebaseConfig = {
        //     apiKey: 'AIzaSyDnPuCtBHmZco-Zpo-k6RowFpKVGrzxtkM',
        //     authDomain: 'igate2-dev.firebaseapp.com',
        //     projectId: 'igate2-dev',
        //     storageBucket: 'igate2-dev.appspot.com',
        //     messagingSenderId: '*************',
        //     appId: '1:*************:web:a18e3bfaae0414c9c0ca7e'
        // };
        // // Initialize Firebase
        // const app = initializeApp(firebaseConfig);
        // const messaging = getMessaging(app);
        // tslint:disable-next-line: max-line-length
        // getToken(messaging, { vapidKey: 'AAAA9e_T0Vs:APA91bHfecSLXOgIykNaa80DWTpdSChetqO4GvdS_HgXOIaUmaFucAaCjfa3u7gc0Jv_LcGNfthbSA-FipAFgD4ZYIoJXul6uR6mZwoOHzmCIsywCBRzHwyY0DXdFt3DhQ62l3Em94fL' }).then((currentToken) => {
        //     if (currentToken) {
        //         console.log('Lấy đc rồi');
        //         console.log(currentToken);
        //       // Send the token to your server and update the UI if necessary
        //       // ...
        //     } else {
        //         console.log('Không lấy đc');
        //       // Show permission request UI
        //       // ...
        //     }
        //   }).catch((err) => {
        //     console.log('Lỗi Không lấy đc');
        //     console.log(err);
        //     // ...
        //   });

        // Get registration token. Initially this makes a network call, once retrieved
        // subsequent calls to getToken will return from cache.
        // const messaging = getMessaging();
        // // tslint:disable-next-line:max-line-length
        // getToken(messaging, { vapidKey: 'BALbkS9nJplcyqYO4ZfsyNoetIroBGMtTn_x2APe5R5TJILxpeAxSS0fgib2-clUrG_mPGzeKmg6jCYwThqZw7A' }).then((currentToken) => {
        //     if (currentToken) {
        //         console.log('Lấy đc rồi');
        //         console.log(currentToken);
        //         // Send the token to your server and update the UI if necessary
        //         // ...
        //     } else {
        //         console.log('Không lấy đc');
        //         // Show permission request UI
        //         // ...
        //     }
        // }).catch((err) => {
        //     console.log('Lỗi Không lấy đc');
        //     console.log(err);
        //     // ...
        // });
        //
        // onMessage(messaging, (payload) => {
        //     console.log('Đã nhận sms ', payload);
        //     // ...
        // });
        // this.getClientIp();


    }

    ngAfterViewInit() {
        const userAgency = JSON.parse(localStorage.getItem('userAgency'));
        this.agency = userAgency;
        if (userAgency !== null) {
            this.siteName = userAgency.name;
        } else {
            this.siteName = JSON.parse(localStorage.getItem('siteName'))[0]['' + this.localeId];
        }

        if (this.siteName === null) {
            // tslint:disable-next-line: no-shadowed-variable
            const userAgency = JSON.parse(localStorage.getItem('userAgency'));
            if (userAgency !== null) {
                this.siteName = userAgency.name;
            } else {
                if (this.localeId === 'vi') {
                    this.siteName = this.config.rootAgency.trans.vi.name;
                }
                if (this.localeId === 'en') {
                    this.siteName = this.config.rootAgency.trans.en.name;
                }
            }
        }
        // console.clear();
        if (this.hasPermissionOpt) {
            this.eventSharedService.getReloadRingNotify().subscribe(data => {
                if (data) {
                    this.getDossierRemind();
                }
            });
        }
    }

    login(): void {
        this.keycloakService.login();
    }

    logout(): void {
        localStorage.clear();
        sessionStorage.clear();
        this.keycloakService.logout();
    }

    ngOnDestroy(): void {
        // tslint:disable-next-line: deprecation
        this.mobileQuery.removeListener(this.mobileQueryListener);
    }

    isSidebarActive() {
        if (window.innerWidth < 960) {
            return false;
        } else {
            return true;
        }
    }

    isEnableToolbar() {
        const config = this.deploymentService.getAppDeployment();

        if (!!config) {
            if (config.toolbarResponsiveEnable !== undefined
                && config.toolbarResponsiveEnable !== null
                && config.toolbarResponsiveEnable === 0) {
                if (window.innerWidth < 960) {
                    return false;
                } else {
                    return true;
                }
            } else {
                return true;
            }
        } else {
            return true;
        }
    }

    async match(permissionOfMenu) {
        if (permissionOfMenu !== undefined && permissionOfMenu !== null && permissionOfMenu.length > 0) {
            permissionOfMenu.forEach(per => {
                if (this.userPermissions.filter(uP => uP.code === per.code).length > 0) {
                    return true;
                }
                else {
                    return false;
                }
            });
        } else {
            return false;
        }
    }

    checkAvalableMenu(permissionMenu) {
        return new Promise(resolve => {
            permissionMenu.forEach((pM, index, array) => {
                if (this.userPermissions.filter(uPer => uPer.code === pM.code)) {
                    resolve(true);
                } else {
                    if (index === (array.length - 1)) {
                        resolve(false);
                    }
                }
            });
        });
    }

    matchPosition(position: string, listSubMenu, index) {
        this.countRoute = 0;
        listSubMenu.forEach(r => {
            let currentRoute = r.route;
            if (r.route.lastIndexOf('/') > 0) {
                currentRoute = r.route.substring(0, r.route.lastIndexOf('/'));
            }
            if(this.menuSpecificforBNV){
                if (currentRoute === position && this.sidebarMenu[index].code === this.codePosition) {
                    this.countRoute = this.countRoute + 1;
                    this.sidebarMenu.forEach(menu => {
                        menu.active = false;
                    });
                    this.sidebarMenu[index].active = true;
                }
            }
            else{
                if (currentRoute === position) {
                    this.countRoute = this.countRoute + 1;
                    this.sidebarMenu.forEach(menu => {
                        menu.active = false;
                    });
                    this.sidebarMenu[index].active = true;
                }
            }
        });
        if (this.countRoute > 0) {
            return true;
        } else {
            return false;
        }
    }

    setOpenAccordion(url) {
        if (this.menuSpecificforBNV){
            //Lấy code của mainMenu thông qua router của listSubMenu
            const cleanRouter = url.startsWith('/') ? url.slice(1) : url;
            for (const menu of this.sidebarMenu) {
                const match = menu.listSubMenu.find(subMenu =>subMenu.route === cleanRouter);
                if (match) {
                    this.codePosition = menu.code;
                    break;
                }
            }
        }
        const path = url;
        if (path.lastIndexOf('?') < 0) {
            this.position = path.split('/', 2)[1];
        } else {
            const wtParams = path.split('?')[0];
            this.position = wtParams.split('/', 2)[1];
        }
    }

    getAvatar() {
        const tempUID = localStorage.getItem('tempUID');
        const tempAvatar = localStorage.getItem('tempAvatar');
        const tempUsername = localStorage.getItem('tempUsername');
        this.keycloakService.loadUserProfile().then(user => {
            sessionStorage.setItem('username_CD', !!user?.username ? user?.username : null);
            sessionStorage.setItem('fullname_CD', !!user?.firstName ? user?.firstName : null);
            if (tempUID === user['attributes'].user_id) {
                this.avatar = tempAvatar;
                this.userName = tempUsername;
                this.screenListener();
            } else {
                // tslint:disable-next-line: no-string-literal
                this.userService.getUserInfo(user['attributes'].user_id).subscribe(data => {
                    // tslint:disable-next-line: no-string-literal
                    this.UID = user['attributes'].user_id;
                    localStorage.setItem('tempUID', this.UID[0]);
                    this.userName = data.fullname;
                    this.screenListener();
                    localStorage.setItem('tempUsername', this.userName);
                    if (data.avatarId === null) {
                        this.avatar = 'url(' + this.config.cloudStaticURL + 'logo/HCC.png' + ')';
                        localStorage.setItem('tempAvatar', this.avatar);
                    } else {
                        this.userService.getUserAvatar(data.avatarId).subscribe(response => {
                            const dataType = response.type;
                            const binaryData = [];
                            binaryData.push(response);
                            const downloadLink = document.createElement('a');
                            downloadLink.href = window.URL.createObjectURL(new Blob(binaryData, { type: dataType }));
                            document.body.appendChild(downloadLink);
                            this.avatar = this.sanitizer.bypassSecurityTrustStyle('url(' + downloadLink.href + ')');

                            const reader = new FileReader();
                            reader.readAsDataURL(new Blob(binaryData, { type: dataType }));
                            // tslint:disable-next-line: only-arrow-functions
                            reader.onloadend = function () {
                                const base64data = reader.result;
                                localStorage.setItem('tempAvatar', 'url(' + base64data.toString() + ')');
                            };
                        }, err => {
                            this.avatar = 'url(' + this.config.cloudStaticURL + 'logo/HCC.png' + ')';
                            localStorage.setItem('tempAvatar', this.avatar);
                        });
                    }
                }, error => {
                    this.avatar = 'url(' + this.config.cloudStaticURL + 'logo/HCC.png' + ')';
                    localStorage.setItem('tempAvatar', this.avatar);
                });
            }
        });

    }

    getAppsLogo() {
        this.listApps.forEach(app => {
            if (app.logo.id !== '') {
                this.mainService.getFile(app.logo.id).subscribe(data => {
                    const reader = new FileReader();
                    reader.addEventListener('load', () => {
                        // tslint:disable-next-line: no-string-literal
                        app.logo['url'] = reader.result;
                    }, false);
                    reader.readAsDataURL(data);
                }, err => {
                    // tslint:disable-next-line: no-string-literal
                    app.logo['url'] = this.config.cloudStaticURL + 'logo/quoc-huy.png';
                    console.log(err);
                });
            } else {
                // tslint:disable-next-line: no-string-literal
                app.logo['url'] = 'assets/img/quoc-huy.png';
            }
        });
    }

    changeLanguage(lang, id) {
        if (this.localeId !== lang && this.typeComboBoxLanguage != 2) {
            localStorage.setItem('language', lang);
            localStorage.setItem('languageId', id);
            this.selectedLang = lang;
            const currentUrl = window.location.href;
            if (this.localeId === 'vi') {
                const url = currentUrl.replace('/vi/', '/en/');
                window.open(url, '_self');
            }
            if (this.localeId === 'en') {
                const url = currentUrl.replace('/en/', '/vi/');
                window.open(url, '_self');
            }
        }
    }

    handleQueryStatus(StatusesStr, p) {
        if (p?.permission?.code === 'additionalRequirementDossierApproval') {
          StatusesStr += ',8';
        }
        if (p?.permission?.code === 'pauseDossierApproval') {
          StatusesStr += ',9';
        }
        if (p?.permission?.code === 'extendDossierApproval') {
          StatusesStr += ',10';
        }
        if (p?.permission?.code === 'cancelDossierApproval') {
          StatusesStr += ',11';
        }
        return StatusesStr;
    }

  getDossierRemind() {
    this.totalDossierRemind = 0;
    this.listDossierRemind = [];
    this.keycloakService.loadUserProfile().then(user => {
      // tslint:disable-next-line: no-string-literal
      this.userService.getUserExperience(user['attributes'].user_id[0]).subscribe(data => {
        const userAgency = JSON.parse(localStorage.getItem('userAgency'));
        const permissions = this.userService.getUserPermissions();
        let statusList = '2,3,4,5,16,17';
        // tslint:disable-next-line:one-variable-per-declaration
        let reception =  false, processing =  false, deny =  false;
        let allStatus = '';
        for (const p of permissions) {
          statusList = this.handleQueryStatus(statusList, p);
          if (p.permission.code === 'admin' || p.permission.code === 'oneGateDossierManager' || p.permission.code === 'oneGateAdminMaster'){
            reception = true;
            processing = true;
            deny = true;
          }
          if ( p.permission.code === 'oneGateDossierProcessing' ){
            processing = true;
          }
          if ( p.permission.code === 'oneGateDossierOnlineReception' || p.permission.code === 'oneGateDossierAccepter' ){
            reception = true;
          }
          if ( p.permission.code === 'oneGateDossierCancel' ){
            deny = true;
          }
        }
        let approveAgencysId = '';
        for (let k = 0 ; k < this.approvalAgencyList.length; k++){
          approveAgencysId = approveAgencysId + this.approvalAgencyList[k];
          if (k < this.approvalAgencyList.length - 1){
            approveAgencysId = approveAgencysId + ',';
          }
        }
        const listUserAgency = JSON.parse(localStorage.getItem('listAgencyUser'));
        let userExperience = JSON.parse(localStorage.getItem('userExperienceAgency'));
        if (!userExperience && !!listUserAgency && listUserAgency.length !== 0 && !!this.userAgency) {
          userExperience = listUserAgency.find(item => item.agency?.id === userAgency?.id);
        }
        // BE split to get menu
        const recetionStatus = reception ? '0,1,3,12,14,15,19' : '';
        const ProcessingStatus = processing ? statusList : '';
        const nonProcessingStatus = deny ? '6' : '';
        allStatus =  ProcessingStatus + '--' + recetionStatus + '--' + nonProcessingStatus;
        data.forEach(async element => {
          if(element.primary){
            await this.getListSector();
            let sectorId = '';
            if(this.enableSearchBySector && !!this.listSectorId && this.listSectorId.length !== 0 && !this.listSectorId[0].id){
              sectorId  = this.listSectorId;
            }
            if(this.enableSectorUserSearch && !!this.listSectorUser){
              sectorId = this.listSectorUser;
            }

            let listRemindTaskStr = this.config.listStatusViewReception + ',' + this.config.listStatusViewCancel + ',' + this.config.listStatusViewReceptionPage;

            if(this.listNotRemindTaskKgg.length > 0){
                //
                let listRemindTask = listRemindTaskStr.split(',');
                //
                listRemindTask = listRemindTask.filter(item => !this.listNotRemindTaskKgg.includes(item));

                listRemindTaskStr = listRemindTask.join(',');
            }

            let search = 'user-id=' + user['attributes'].user_id[0]
              + '&agency-id=' + userAgency.id
              + '&position-id=' + (  userExperience?.position?.id )
              + '&status-list=' + allStatus
              + '&agency-parent-id=' + (userAgency.parent !== null ? userAgency.parent.id : '')
              + '&agency-tag-id=' + (!!userAgency.tag ? userAgency.tag.toString() : '')
              + '&list-remind-id=' + listRemindTaskStr
              + '&sector-id=' + sectorId;
            if (approveAgencysId !== ''){
                search += '&approve-agencys-id=' + approveAgencysId;
            }

            if (this.enableApprovaledAgencyTreeView) {
                search += '&enable-approvaled-agency-tree-view=' + this.enableApprovaledAgencyTreeView;
            }
            if (this.filterByCandidateGroup){
              search += '&filter-by-candidate-group=' + this.filterByCandidateGroup;
            }
            if(this.checkShowSubMenuNotiValue){
                const userAgency = JSON.parse(localStorage.getItem('userAgency'));
                const UID = localStorage.getItem('UID');
                //if(this.checkShowSubMenuNotiValue){
                    //let agencyIdIndex = this.dossierMenuTaskRingWithAgency.listAgency.indexOf(userAgency.id);
                    let agencyIdIndex = this.dossierMenuTaskRingWithAgency.listAgency.findIndex(object => {
                        return object.AgencyID === userAgency.id;
                      });
                    if (agencyIdIndex != -1) {
                        let nearByDueAcceptedWhenValue = this.dossierMenuTaskRingWithAgency?.listAgency[agencyIdIndex]?.NearbyDueAcceptedWhen != null ? this.dossierMenuTaskRingWithAgency.listAgency[agencyIdIndex].NearbyDueAcceptedWhen :
                                                            (this.dossierMenuTaskRingWithAgency?.NearbyDueAcceptedWhen != null ? this.dossierMenuTaskRingWithAgency.NearbyDueAcceptedWhen : 2);
                        let overDueAcceptedWhen = this.dossierMenuTaskRingWithAgency?.listAgency[agencyIdIndex]?.OverDueAcceptedWhen != null ? this.dossierMenuTaskRingWithAgency.listAgency[agencyIdIndex].OverDueAcceptedWhen :
                                                            (this.dossierMenuTaskRingWithAgency?.OverDueAcceptedWhen != null ? this.dossierMenuTaskRingWithAgency.OverDueAcceptedWhen : 0);
                        let nearbyDueProcessingWhen = this.dossierMenuTaskRingWithAgency?.listAgency[agencyIdIndex]?.NearbyDueProcessingWhen != null ? this.dossierMenuTaskRingWithAgency.listAgency[agencyIdIndex].NearbyDueProcessingWhen :
                                                            (this.dossierMenuTaskRingWithAgency?.NearbyDueProcessingWhen != null ? this.dossierMenuTaskRingWithAgency.NearbyDueProcessingWhen : 2);
                        let overDueProcessingWhen = this.dossierMenuTaskRingWithAgency?.listAgency[agencyIdIndex]?.OverDueProcessingWhen != null ? this.dossierMenuTaskRingWithAgency.listAgency[agencyIdIndex].OverDueProcessingWhen :
                                                            (this.dossierMenuTaskRingWithAgency?.OverDueProcessingWhen != null ? this.dossierMenuTaskRingWithAgency.OverDueProcessingWhen : 0);
                        let assigneeId = UID ? UID : null;
                        let candidateGroupId = userAgency.id;
                        let senderId = null;
                        let candidatePositionId = userExperience?.position?.id ? userExperience.position.id : null;
                        let candidateGroupParentId = userAgency?.parent?.id ? userAgency.parent.id : null;
                        let currentTaskAgencyTypeId = userAgency?.tag ? userAgency.tag.toString() : null;
                        let approveAgencysIds =  approveAgencysId ? approveAgencysId : null;
                        //let  agencyMaxDueReceive = this.dossierMenuTaskRingWithAgency?.agencyMaxDueReceive ?  this.dossierMenuTaskRingWithAgency.agencyMaxDueReceive : 8 ;

                        let searchString = 'agency-id=' + userAgency.parent.id
                        + '&nearByDueAcceptedWhen=' + nearByDueAcceptedWhenValue
                        + '&overDueAcceptedWhen=' + overDueAcceptedWhen
                        + '&nearbyDueProcessingWhen=' + nearbyDueProcessingWhen
                        + '&overDueProcessingWhen=' + overDueProcessingWhen
                        + '&assignee-id=' + assigneeId
                        + '&candidate-group-id=' + candidateGroupId
                        + '&sender-id='
                        + '&candidate-position-id=' + candidatePositionId
                        + '&candidate-group-parent-id=' + candidateGroupParentId
                        + '&current-task-agency-type-id=' + currentTaskAgencyTypeId
                        + '&approve-agencys-id=' + approveAgencysIds;
                        //+ '&agency-max-due-receive=' + agencyMaxDueReceive;

                        this.dossierService.getDossierMenuTaskRingSubMenu(searchString).subscribe(dataSubMenu => {
                            if(dataSubMenu) {
                                let totalNoti = 0;
                                if(this.listAgencyCountDown8h.length > 0) {
                                    if(this.listAgencyCountDown8h.includes(userAgency.id)) {
                                        this.subMenuRemindNoti = [
                                            {name : "Hồ sơ đến hạn (Tiếp nhận)", value : dataSubMenu.countDossierReceiveDue, type : 1},
                                            {name : "Hồ sơ trễ hạn (Tiếp nhận)", value : dataSubMenu.countDossierOverReceiveDue, type : 2},
                                        ]
                                        totalNoti =  dataSubMenu.countDossierReceiveDue + dataSubMenu.countDossierOverReceiveDue;
                                    }
                                }
                                this.subMenuRemindNoti.push(
                                    {name : "Hồ sơ đến hạn (Xử lý)", value : dataSubMenu.countDossierProcessingDue, type : 3},
                                    {name : "Hồ sơ trễ hạn (Xử lý)", value : dataSubMenu.countDossierOverProcessingDue, type :4}
                                );
                                totalNoti += (dataSubMenu.countDossierProcessingDue + dataSubMenu.countDossierOverProcessingDue);
                                this.totalSubNotiMenu = totalNoti;
                            } else {
                                    if(this.listAgencyCountDown8h.length > 0) {
                                        if(this.listAgencyCountDown8h.includes(userAgency.id)) {
                                            this.subMenuRemindNoti = [
                                                {name : "Hồ sơ đến hạn (Tiếp nhận)", value : 0},
                                                {name : "Hồ sơ trễ hạn (Tiếp nhận)", value : 0},
                                            ]
                                        }
                                    }
                                    this.subMenuRemindNoti.push(
                                        {name : "Hồ sơ đến hạn (Xử lý)", value : 0},
                                        {name : "Hồ sơ trễ hạn (Xử lý)", value : 0}
                                    );
                            }
                        })
                    }
               // }
            } else {
              // STAGE: Chuong thong bao
              if (this.isNotification && this.isNotificationRing) {
                  const userId = localStorage.getItem('UID');
                  const userExperienceAgency = JSON.parse(localStorage.getItem('userExperienceAgency'));
                  const userAgency = JSON.parse(localStorage.getItem('userAgency'));

                  const searchRemind = 'assignee-id=' + userId +
                  '&position-id=' + userExperienceAgency.position.id +
                  '&agency-id=' + userAgency.id;

                this.notificationV2Service.getNotificationRing(searchRemind).subscribe(data => {
                    if (this.hiddenWithdrawnRemind == true && !!this.withdrawnRemindId) {
                        for (let k = 0; k < data.length; k++) {
                            if (data[k].id == this.withdrawnRemindId) {
                                data.splice(k, 1);
                                k = k - 1;
                            }
                        }
                    }
                    this.listDossierRemind = [...this.listDossierRemind, ...data];

                    // tslint:disable-next-line:prefer-for-of
                    for (let j = 0; j < data.length; j++) {
                        this.totalDossierRemind += data[j].count;
                    }
                    if (this.totalDossierRemind > 99) {
                        this.totalDossierRemind = '99+';
                    }
                });
              } else {
                if(!this.deploymentService.getAppDeployment()?.disableMenuTaskRing)
                {
                    if(this.hasPermissionOpt){
                        this.dossierService.getDossierMenuTaskRingOpt(search).subscribe(data => {
                            if (data){
                                if(this.hiddenWithdrawnRemind == true && !!this.withdrawnRemindId){
                                    for(let k = 0; k < data.length; k++){
                                        if(data[k].id == this.withdrawnRemindId){
                                            data.splice(k, 1);
                                            k=k-1;
                                        }
                                    }
                                }
                                if(this.hiddenStatusDossierRemindOnRingHGG) { // IGATESUPP-77060 [HGG-iGate] Đề nghị bỏ nhắc việc hồ sơ Dừng xử lý, Từ chối, Đã huỷ
                                    data = data.filter(item => !this.deploymentService.env.OS_HGG.listStatusDossierRemindOnRingHGG.includes(item.id));
                                }
                                this.listDossierRemind = [...this.listDossierRemind, ...data];

                                // tslint:disable-next-line:prefer-for-of
                                for (let j = 0; j < data.length; j++) {
                                    this.totalDossierRemind += data[j].count;
                                }
                                if (this.totalDossierRemind > 99 ){
                                    this.totalDossierRemind = '99+';
                                }
                            }
                        });
                    }else{
                        this.dossierService.getDossierMenuTaskRing(search).subscribe(data => {
                            if (data){
                              if(this.hiddenWithdrawnRemind == true && !!this.withdrawnRemindId){
                                for(let k = 0; k < data.length; k++){
                                    if(data[k].id == this.withdrawnRemindId){
                                        data.splice(k, 1);
                                        k=k-1;
                                    }
                                }
                              }
                              if(this.hiddenStatusDossierRemindOnRingHGG) { // IGATESUPP-77060 [HGG-iGate] Đề nghị bỏ nhắc việc hồ sơ Dừng xử lý, Từ chối, Đã huỷ
                                data = data.filter(item => !this.deploymentService.env.OS_HGG.listStatusDossierRemindOnRingHGG.includes(item.id));
                              }
                              this.listDossierRemind = [...this.listDossierRemind, ...data];

                              // tslint:disable-next-line:prefer-for-of
                              for (let j = 0; j < data.length; j++) {
                                this.totalDossierRemind += data[j].count;
                              }
                              if (this.totalDossierRemind > 99 ){
                                this.totalDossierRemind = '99+';
                              }
                            }
                        });
                    }
                }
                  // tslint:disable-next-line:no-shadowed-variable
              }
            }
          }
          // if (element.primary) {
          //     const permissions = this.userService.getUserPermissions();
          //   // tslint:disable-next-line:one-variable-per-declaration
          //     let reception =  false, processing =  false, deny =  false;
          //     for (const p of permissions) {
          //       statusList = this.handleQueryStatus(statusList, p);
          //       if (p.permission.code === 'admin' || p.permission.code === 'oneGateDossierManager' || p.permission.code === 'oneGateAdminMaster'){
          //         reception = true;
          //         processing = true;
          //         deny = true;
          //       }
          //       if ( p.permission.code === 'oneGateDossierOnlineReception' || p.permission.code === 'oneGateDossierAccepter' ){
          //         reception = true;
          //       }
          //       if ( p.permission.code === 'oneGateDossierProcessing' ){
          //         processing = true;
          //       }
          //       if ( p.permission.code === 'oneGateDossierCancel' ){
          //         deny = true;
          //       }
          //     }
          //
          //     // tslint:disable-next-line: no-string-literal
          //     const search = 'assignee-id=' + user['attributes'].user_id[0]
          //         + '&candidate-group-id=' + userAgency.id
          //         + '&candidate-position-id=' + element?.position?.id
          //         + '&status-list=' + statusList + '--0,1,3,12--6'
          //         + '&candidate-group-parent-id=' + (userAgency.parent !== null ? userAgency.parent.id : '')
          //         + '&current-task-agency-type-id=' + (!!userAgency.tag ? userAgency.tag.toString() : '')
          //         + 'list-remind-id=' + this.config.listStatusViewReception
          //         + '&in-list=0';
          //     ;
          //     this.listDossierRemind = [];
          //     const search1 = 'dossier-status=' + '0,1,3,12'
          //       + '&parent-agency-id=' + (userAgency.parent !== null ? userAgency.parent.id : '')
          //       + '&agency-type-id=' + (!!userAgency.tag ? userAgency.tag.toString() : '')
          //       + '&list-status=' + this.config.listStatusViewReceptionPage
          //       + '&in-list=1'
          //       + '&agency-id=' + userAgency.id;
          //     let search2 = 'dossier-status=' + '6'
          //       + '&agency-id=' + userAgency.id
          //       + '&list-status=' + this.config.listStatusViewCancel
          //       + '&in-list=1';
          //     if ( !!userAgency?.parent?.id)
          //     {
          //       search2 += '&parent-agency-id=' + userAgency?.parent?.id;
          //     }
          //     const strStr = [search, search1, search2];
          //     this.totalDossierRemind = 0;
          //   // tslint:disable-next-line:prefer-for-of
          //     for (let i = 0; i < strStr.length; i++) {
          //       if ( this.checkPermisstionGetMenu(processing, i, 0)
          //         || this.checkPermisstionGetMenu(reception, i, 1)
          //         || this.checkPermisstionGetMenu(deny, i, 2))
          //       {
          //         continue;
          //       }
          //       // tslint:disable-next-line:no-shadowed-variable
          //       this.dossierService.getDossierMenuTaskRemindProcess(strStr[i]).subscribe(data => {
          //       if (data.content.length > 0) {
          //         // tslint:disable-next-line:prefer-for-of
          //         data.content.sort((a, b) => b.count - a.count );
          //         // tslint:disable-next-line:prefer-for-of
          //         for (let k = 0; k < data.content.length; k++){
          //             data.content[k].type = 0;
          //             if (i + 1 === 1){
          //                 data.content[k].name = data.content[k].name + ' (Xử lý)';
          //                 data.content[k].type = 1;
          //             }
          //             if (i + 1 === 2){
          //                 data.content[k].name = data.content[k].name + ' (Tiếp nhận)';
          //                 data.content[k].type = 2;
          //             }
          //             if (i + 1 === 3){
          //                 data.content[k].name = data.content[k].name + ' (Không xử lý)';
          //                 data.content[k].type = 3;
          //             }
          //         }
          //         this.listDossierRemind = [...this.listDossierRemind, ...data.content];
          //         // tslint:disable-next-line:prefer-for-of no-shadowed-variable
          //         for (let j = 0; j < data.content.length; j++) {
          //           this.totalDossierRemind += data.content[j].count;
          //         }
          //       }
          //     });
          //   }
          // }
        });
      });
    });
  }

  getListApprovalAgencyConfig(agencyId) {
    return new Promise<Array<any>>((resolve) => {
      // tslint:disable-next-line:max-line-length
      this.approvalAgencyConfigService.getListApprovalAgencyConfig('?page=0&size=5&status=1&approvalAgency-id=' + agencyId).subscribe(data => {
        if (data.content.length > 0){
          // tslint:disable-next-line:prefer-for-of
          for (let k = 0; k < data.content[0].approvaledAgency.length; k++){
            this.approvalAgencyList.push(data.content[0].approvaledAgency[k].id);
          }
          resolve(data.content);
        }
        else{
          if (!!this.userAgencyProcess?.parent && !!this.userAgencyProcess?.parent.id){
            // tslint:disable-next-line:max-line-length
            this.approvalAgencyConfigService.getListApprovalAgencyConfig('?page=0&size=5&status=1&approvalAgency-id=' + this.userAgencyProcess.parent.id).subscribe(data2 => {
                if (data2.content.length > 0){
                  // tslint:disable-next-line:prefer-for-of
                  for (let k = 0; k < data2.content[0].approvaledAgency.length; k++){
                    this.approvalAgencyList.push(data2.content[0].approvaledAgency[k].id);
                  }
                }
                else{
                  this.approvalAgencyList.push(agencyId);
                  this.approvalAgencyList.push(this.userAgencyProcess.parent.id);
                }
                resolve(data.content);
              },
              err => {
                resolve(null);
              });
          }
          else{
            this.approvalAgencyList.push(agencyId);
            resolve(data.content);
          }
        }
        // resolve(data.content);
      }, err => {
        resolve(null);
      });
    });
  }


  checkPermisstionGetMenu(permission, indexMenu, indexCompare){
      return permission === false && indexMenu === indexCompare;
    }

    getPageConfiguration() {
        const config = this.deploymentService.getAppDeployment();

        if (!!config) {
            if (config.site !== undefined && config.site !== null) {
                this.siteContent = config.site;
                if(!this.siteContent.backgroundColor)
                    this.siteContent.backgroundColor = '#ce7a58';
                this.titleService.setTitle(config.site.name[this.selectedLang]);
            }
            if (config.sidenav !== undefined && config.sidenav !== null) {
                this.sidenav = config.sidenav;
            }
            // get with domain
            const configCloud = this.deploymentService.getAppDeployment();
            if (configCloud.domain && configCloud.domain.length > 0) {
                // tslint:disable-next-line:max-line-length
                const domain = configCloud.domain.filter(listdomain => ((listdomain.domain.toLowerCase().indexOf(window.location.host) > -1) || (window.location.host.toLowerCase().indexOf(listdomain.domain) > -1)));
                if (domain && domain.length > 0) {
                    if (domain[0].footer) {
                        config.footer = domain[0].footer;
                    }
                    if (domain[0].header) {
                        config.header = domain[0].header;
                    }
                }
            }
            // end with domain
            if (config.header !== undefined && config.header !== null) {
                if (config.header.notificationEnable !== undefined && config.header.notificationEnable !== null) {
                    this.notificationEnable = config.header.notificationEnable;
                    if (this.notificationEnable === 1) {
                        this.getDossierRemind();
                    }
                }
                if (config.header.listAppEnable !== undefined && config.header.listAppEnable !== null) {
                    this.listAppEnable = config.header.listAppEnable;
                }
                if (config.header.homeUrl !== undefined && config.header.homeUrl !== null) {
                    this.homeUrl = config.header.homeUrl;
                }
                if (config.header.logoDefaultApp !== undefined && config.header.logoDefaultApp !== null) {
                    this.logoDefaultApp = config.header.logoDefaultApp;
                }
            }
            if (config.footer !== undefined && config.footer !== null) {
                this.footerContent = config.footer;
            }
        }
    }

    getListOnegateApps() {
        const searchString = '?account-id=' + this.accountId + '&tag-id=' + this.config.onegateListAppTagId + '&size=50';
        this.mainService.getListAccountApps(searchString).subscribe(data => {
            for (let i = 0; i < data.numberOfElements; i++) {
                if (data.content[i].app.logoId !== null && data.content[i].app.logoId !== '') {
                    this.mainService.getFile(data.content[i].app.logoId).subscribe(response => {
                        const dataType = response.type;
                        const binaryData = [];
                        binaryData.push(response);

                        const downloadLink = document.createElement('a');
                        downloadLink.href = window.URL.createObjectURL(new Blob(binaryData, { type: dataType }));
                        document.body.appendChild(downloadLink);
                        data.content[i].app.logoURL = this.sanitizer.bypassSecurityTrustStyle('url(' + downloadLink.href + ')');
                        this.listOnegateApps.push(data.content[i]);
                    }, err => {
                        data.content[i].app.logoURL = this.config.cloudStaticURL + 'logo/quoc-huy.png';
                        this.listOnegateApps.push(data.content[i]);
                    });
                } else {
                    data.content[i].app.logoURL = this.config.cloudStaticURL + 'logo/quoc-huy.png';
                    this.listOnegateApps.push(data.content[i]);
                }
            }
            this.listOnegateApps = JSON.parse(JSON.stringify(this.listOnegateApps).replace(/null/g, '""'));
        });
    }

    getListAccountApps() {
        const searchString = '?account-id=' + this.accountId + '&size=500&status=1';
        this.mainService.getListAccountApps(searchString).subscribe(data => {
            for (let i = 0; i < data.numberOfElements; i++) {
                if (data.content[i].app.logoId !== null && data.content[i].app.logoId !== '') {
                    this.mainService.getFile(data.content[i].app.logoId).subscribe(response => {
                        const dataType = response.type;
                        const binaryData = [];
                        binaryData.push(response);

                        const downloadLink = document.createElement('a');
                        downloadLink.href = window.URL.createObjectURL(new Blob(binaryData, { type: dataType }));
                        document.body.appendChild(downloadLink);
                        data.content[i].app.logoURL = this.sanitizer.bypassSecurityTrustStyle('url(' + downloadLink.href + ')');
                        // this.listApps.push(data.content[i]);
                    }, err => {
                        if (this.logoDefaultApp !== null && this.logoDefaultApp !== '') {
                            data.content[i].app.logoURL = 'url(' + this.logoDefaultApp + ')';
                        }
                        else {
                            data.content[i].app.logoURL = 'url(' + this.config.cloudStaticURL + '/logo/default_logo_application.png' + ')';
                        }
                        // this.listApps.push(data.content[i]);
                    });
                } else {
                    if (this.logoDefaultApp !== null && this.logoDefaultApp !== '') {
                        data.content[i].app.logoURL = 'url(' + this.logoDefaultApp + ')';
                    }
                    else {
                        data.content[i].app.logoURL = 'url(' + this.config.cloudStaticURL + '/logo/default_logo_application.png' + ')';
                    }
                    // this.listApps.push(data.content[i]);
                }
            }
            this.listApps = data.content;
            // this.listApps = JSON.parse(JSON.stringify(this.listApps).replace(/null/g, '""'));
        });
    }

    onAgencyChange(userExperienceAgency: any) {
        let agency = userExperienceAgency?.agency;
        this.siteName = agency.name;
        if(this.filterByPositionAgencyType){ //minhnh-it3: Luồng chạy cho QNM
        this.selectAgencyId = userExperienceAgency.id;
        localStorage.setItem('selectAgencyId',this.selectAgencyId );
        }
        this.dossierService.getAgencyInfo(agency.id).subscribe(res => {
            agency.tag = res.tag;
            localStorage.setItem('userAgency', JSON.stringify(agency));
            localStorage.setItem('userExperienceAgency', JSON.stringify(userExperienceAgency));
            localStorage.setItem('levelAgency', res.level ? JSON.stringify(res.level) : null);
          window.location.reload();
      });
    }
    checkUserPermissions() {
        const checkOneGateAdminMaster = this.userPermissions.filter(uP => uP.code === 'oneGateAdminMaster').length > 0 ? '1' : '0';
        const checkOneGateProcessCatalog = this.userPermissions.filter(uP => uP.code === 'oneGateProcessCatalog').length > 0 ? '1' : '0';
        // truong hop co quyen se an nut xoa.mac dinh se hien thi nut xoa cho cac can bo
        const oneGateDeleteComposition = this.userPermissions.filter(uP => uP.code === 'oneGateDeleteComposition').length > 0 ? '0' : '1';
        const oneGateDeleteDossier = this.userPermissions.filter(uP => uP.code === 'oneGateDeleteDossier').length > 0 ? '0' : '1';

        sessionStorage.setItem('oneGateAdminMaster', checkOneGateAdminMaster);
        sessionStorage.setItem('oneGateProcessCatalog', checkOneGateProcessCatalog);
        sessionStorage.setItem('oneGateDeleteDossier', oneGateDeleteDossier);
        sessionStorage.setItem('oneGateDeleteComposition', oneGateDeleteComposition);
    }
    onClickRemind(id, type) {
        let routerText = '';
        if (this.hasPermissionOpt) {
            routerText = '/dossier/v2/processing';
            if (type === 2) {
                routerText = '/dossier/v2/online-reception';
            }
            if (type === 3) {
                routerText = '/dossier/v2/cancel';
            }
        } else {
            routerText = '/dossier/processing';
            if (type === 2) {
                routerText = '/dossier/online-reception';
            }
            if (type === 3) {
                routerText = '/dossier/cancel';
            }
        }
        // if (this.config.listStatusViewReception.indexOf(id) !== -1) {
        //     routerText = '/dossier/online-reception';
        // } else if (this.config.listStatusViewCancel.indexOf(id) !== -1) {
        //     routerText = '/dossier/cancel';
        // }
        window.open(this.linkRemind + routerText + '?remindId=' + id, '_self');
        // this.router.navigate([routerText], {
        //     queryParams: {
        //         remindId: id
        //     }
        // });
    }

    openExternalLink = (link) => {
        window.open(link, '_blank');
    };

    openLink(type) {
        switch (type) {
          case 0:
            this.openExternalLink(this.linkIos);
            break;
          case 1:
            this.openExternalLink(this.linkAndroid);
            break;
        }
      }

    onSubmitQuickSearch() {
        const dialogData = new QuickSearchDialogModel(this.dossierCode.value);
        const dialogRef = this.dialog.open(QuickSearchComponent, {
            width: '80vw',
            data: dialogData,
            disableClose: true,
            autoFocus: false
        });
        dialogRef.afterClosed().subscribe(dialogResult => {
        });
    }

    screenListener() {
        if (this.mediaSubscription) {
            return;
        }
        this.mediaSubscription = this.mediaObserver.media$.subscribe((result: MediaChange) => {
            // this.mediaSubscription = this.mediaObserver.asObservable().subscribe((result: MediaChange[]) => {
            switch (result.mqAlias) {
                case 'xs':
                    this.handleDisplayName(7);
                    break;
                case 'sm':
                    this.handleDisplayName(7);
                    break;
                case 'md':
                    this.handleDisplayName(7);
                    break;
                case 'xl':
                    this.handleDisplayName(20);
                    break;
                case 'lg':
                    this.handleDisplayName(18);
                    break;
                default:
                    this.handleDisplayName(20);
                    break;
            }
        });
    }

    handleDisplayName(length: number) {
        const arrName = this.userName.trim().split(" ");
        this.displayName = this.shortenName(arrName, 1, length);
    }

    shortenName(arrName: string[], index, length) {
        const name = arrName.join(" ");
        if (index >= arrName.length - 1) {
            if (name.length <= length) {
                return name;
            }
            if (arrName[0].length + arrName[arrName.length - 1] <= length) {
                return arrName[0] + " " + arrName[arrName.length - 1];
            }
            return arrName[arrName.length - 1];
        }
        if (name.length <= length) {
            return name;
        }
        arrName[index] = arrName[index].substring(0, 1).toUpperCase() + ".";
        return this.shortenName(arrName, index + 1, length);
    }

    addMenuControlSyncForDNI() {
        let mainMenu = this.sidebarMenu.find(menu =>
            menu.mainMenu.some(item => item.languageId === 228 && item.name === 'Đồng bộ dữ liệu'));

        if (mainMenu) {
            // ✅ Nếu menu đã tồn tại, thêm submenu vào listSubMenu
            mainMenu.listSubMenu = mainMenu.listSubMenu || []; // Đảm bảo listSubMenu tồn tại
            mainMenu.listSubMenu.push({
                title: [
                    { languageId: 228, name: 'Kiểm tra đối soát DVCQG cho Đồng Nai' },
                    { languageId: 46, name: 'Check Control DVCQG for Dong Nai' }
                ],
                route: 'sync/control-sync-for-DNI',
                permission: [
                    { code: 'oneGateAdminMaster' },
                    { code: 'oneGateDossierSync' }
                ]
            });
        } else {
            // ❌ Nếu menu chưa tồn tại, tạo mới
            this.sidebarMenu.push({
                mainMenu: [
                    { languageId: 228, name: 'Tính năng đặc thù' },
                    { languageId: 46, name: 'Special feature' }
                ],
                icon: 'sync',
                code: 'onegate-database',
                active: false,
                listSubMenu: [{
                    title: [
                        { languageId: 228, name: 'Kiểm tra đối soát DVCQG cho Đồng Nai' },
                        { languageId: 46, name: 'Check Control DVCQG for Dong Nai' }
                    ],
                    route: 'sync/control-sync-for-DNI',
                    permission: [
                        { code: 'oneGateAdminMaster' },
                        { code: 'oneGateDossierSync' }
                    ]
                }]
            });
        }
    }
    // thêm menu thống kê hình thức tiếp nhận hồ sơ cho đồng nai
    addMenuStatisticReceivedForDNI() {
        let mainMenu = this.sidebarMenu.find(menu =>
            menu.mainMenu.some(item => item.languageId === 228 && item.name === 'Thống kê báo cáo'));

        if (mainMenu) {
            // ✅ Nếu menu đã tồn tại, thêm submenu vào listSubMenu
            mainMenu.listSubMenu = mainMenu.listSubMenu || []; // Đảm bảo listSubMenu tồn tại
            mainMenu.listSubMenu.push({
                title: [
                    { languageId: 228, name: 'Thống kê hình thức tiếp nhận hồ sơ DNI' },
                    { languageId: 46, name: 'Statistics on Methods of Receiving Applications' }
                ],
                route: 'statistics/statistic-received-dni',
                permission: [
                    {
                        code: 'oneGateDNI02Statistical'
                    }
                ]
            });
        } else {
            // ❌ Nếu menu chưa tồn tại, tạo mới
            this.sidebarMenu.push({
                mainMenu: [
                    { languageId: 228, name: 'Tính năng đặc thù cho Đồng Nai' },
                    { languageId: 46, name: 'Special feature for DNI' }
                ],
                icon: 'sync',
                code: 'onegate-database',
                active: false,
                listSubMenu: [{
                    title: [
                        { languageId: 228, name: 'Thống kê hình thức tiếp nhận hồ sơ' },
                        { languageId: 46, name: 'Statistics on Methods of Receiving Applications' }
                    ],
                    route: 'statistics/statistic-received-dni',
                    permission: [
                        { code: 'oneGateDNI02Statistical' }
                    ]
                }]
            });
        }
    }
    addMenuNotifyforQNI() {
        this.sidebarMenu.push({
            mainMenu: [
                {
                    languageId: 228,
                    name: 'Thông báo'
                },
                {
                    languageId: 46,
                    name: 'Notification'
                }
            ],
            icon: 'notifications',
            code: 'onegate-database',
            active: false,
            listSubMenu: [
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Hồ sơ dừng xử lý'
                        },
                        {
                            languageId: 46,
                            name: 'Hồ sơ dừng xử lý'
                        }
                    ],
                    route: 'notification/cancel-dossier',
                    permission: [
                        {
                            code: 'oneGateQNiNotifyCanceled'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Thông báo kết quả giải quyết hồ sơ'
                        },
                        {
                            languageId: 46,
                            name: 'Thông báo kết quả giải quyết hồ sơ'
                        }
                    ],
                    route: 'notification/result-dossier',
                    permission: [
                        {
                            code: 'oneGateQNiNotifyCompleted'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Thông báo hồ sơ bổ sung'
                        },
                        {
                            languageId: 46,
                            name: 'Thông báo hồ sơ bổ sung'
                        }
                    ],
                    route: 'notification/additional-dossier',
                    permission: [
                        {
                            code: 'oneGateQNiNotifyAddtional'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Quản lý hồ sơ trễ hạn'
                        },
                        {
                            languageId: 46,
                            name: 'Quản lý hồ sơ trễ hạn'
                        }
                    ],
                    route: 'notification/overdue-dossier',
                    permission: [
                        {
                            code: 'oneGateQNiNotifyOverdue'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Quản lý hồ sơ dừng tạm'
                        },
                        {
                            languageId: 46,
                            name: 'Quản lý hồ sơ  dừng tạm'
                        }
                    ],
                    route: 'notification/suspend-dossier',
                    permission: [
                        {
                            code: 'oneGateQNiNotifySuspend'
                        }
                    ]
                }
            ]
        });
    }
    getAgencyUserPositionName() {
        this.userAgencyPositionName = '';
        this.userPositionName = '';
        if (this.showTooltipAgencyPositionName) {
            const userExperienceAgency = JSON.parse(localStorage.getItem('userExperienceAgency'));
            const fullName = localStorage.getItem("tempUsername") ? localStorage.getItem("tempUsername") : '';
            if(this.filterByPositionAgencyType) //Hiển thị dạng [Chức vụ / Đơn vị]
            this.userAgencyPositionName = userExperienceAgency?.position?.name + " / "+ userExperienceAgency.agency?.name ;
            else
            this.userAgencyPositionName = userExperienceAgency.agency?.name + " (" + userExperienceAgency?.position?.name + ")";
            this.userPositionName = fullName + " (" + userExperienceAgency?.position?.name + ")";
        }
    }

    addMenuSpecificforBNV(){
        this.sidebarMenu.push({
            mainMenu: [
                {
                    languageId: 228,
                    name: 'Tra cứu hồ sơ'
                },
                {
                    languageId: 46,
                    name: 'Searching dossiers'
                }
            ],
            icon: 'search',
            code: 'dossier-search',
            active: false,
            listSubMenu: [
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Tra cứu hồ sơ toàn đơn vị'
                        },
                        {
                            languageId: 46,
                            name: 'Search dossier by all agencies'
                        }
                    ],
                    route: 'dossier/search-all-agency',
                    permission: [
                        {
                            code: 'oneGateAdminMaster'
                        },
                        {
                            code: 'oneGateDossierManager'
                        },
                        {
                            code: 'oneGateSearchByAgencyFullStatus'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Tra cứu hồ sơ theo đơn vị'
                        },
                        {
                            languageId: 46,
                            name: 'Search dossier by agency'
                        }
                    ],
                    route: 'dossier/search-by-agency',
                    permission: [
                        {
                            code: 'oneGateAdminMaster'
                        },
                        {
                            code: 'oneGateDossierManager'
                        },
                        {
                            code: 'oneGateDossierLookupByAgency'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Tra cứu hồ sơ toàn cơ quan'
                        },
                        {
                            languageId: 46,
                            name: 'Search dossier by all agency'
                        }
                    ],
                    route: 'dossier/search',
                    permission: [
                        {
                            code: 'oneGateAdminMaster'
                        },
                        {
                            code: 'oneGateDossierManager'
                        },
                        {
                            code: 'oneGateDossierLookup'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Tra cứu hồ sơ cá nhân'
                        },
                        {
                            languageId: 46,
                            name: 'Search personal dossier'
                        }
                    ],
                    route: 'dossier/search-personal',
                    permission: [
                        {
                            code: 'oneGateAdminMaster'
                        },
                        {
                            code: 'oneGateDossierManager'
                        },
                        {
                            code: 'oneGateDossierLookupPersonal'
                        }
                    ]
                },
              ]
        })
        const menuToRemove = ['dossier/search-all-agency','dossier/search-by-agency','dossier/search', 'dossier/search-personal'];
        this.sidebarMenu.forEach(menu => {
            if (menu.code === 'dossier') {
              menu.listSubMenu = menu.listSubMenu.filter(subMenu => !menuToRemove.includes(subMenu.route));
            }
          });
    }

    // minhvnt added 18/11/2022 => Thêm mới menu đặc thù cho Quảng Nam
    addMenuSpecificforQNM() {
        this.sidebarMenu.push({
            mainMenu: [
                {
                    languageId: 228,
                    name: 'Chức năng đặc thù'
                },
                {
                    languageId: 46,
                    name: 'Specific Function'
                }
            ],
            icon: 'donut_large',
            code: 'onegate-database',
            active: false,
            listSubMenu: [
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Quản lý lịch hẹn giao dịch'
                        },
                        {
                            languageId: 46,
                            name: 'Quản lý lịch hẹn giao dịch'
                        }
                    ],
                    route: 'qnm-dac-thu/quanly-lichhen',
                    permission: [
                        {
                            code: "qnm_quanlydatlichhen"
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Báo cáo trả kết quả'
                        },
                        {
                            languageId: 46,
                            name: 'Báo cáo trả kết quả'
                        }
                    ],
                    route: 'qnm-dac-thu/baocao-banky',
                    permission: [
                        {
                            code: "qnm_baocaotraketqua"
                        }
                    ]
                },
                {
                  title: [
                      {
                          languageId: 228,
                          name: 'Quản lý TTHC ủy quyền'
                      },
                      {
                          languageId: 46,
                          name: 'Quản lý TTHC ủy quyền'
                      }
                  ],
                  route: 'qnm-dac-thu/quanly-tthc-uyquyen',
                  permission: [
                      {
                          code: "qnm_quanlyTTHCuyquyen"
                      }
                  ]
                },
                {
                  title: [
                      {
                          languageId: 228,
                          name: 'Thống kê TTHC ủy quyền'
                      },
                      {
                          languageId: 46,
                          name: 'Thống kê TTHC ủy quyền'
                      }
                  ],
                  route: 'qnm-dac-thu/quanly-tthc-uyquyen-statistic-1',
                  permission: [
                      {
                          code: 'qnm_thongkeTTHCuyquyen'
                      }
                  ]
                },
                {
                  title: [
                      {
                          languageId: 228,
                          name: 'Báo cáo TTHC ủy quyền trình UB'
                      },
                      {
                          languageId: 46,
                          name: 'Báo cáo TTHC ủy quyền trình UB'
                      }
                  ],
                  route: 'qnm-dac-thu/quanly-tthc-uyquyen-statistic-2',
                  permission: [
                      {
                          code: 'qnm_baocaoTTHCuyquyentrinhUB'
                      }
                  ]
                },
                {
                  title: [
                    {
                      languageId: 228,
                      name: 'Hồ sơ ngăn chặn'
                    },
                    {
                      languageId: 46,
                      name: 'Blocking dossier'
                    }
                  ],
                  route: 'qnm-dac-thu/hoso-nganchan',
                  permission: [
                    {
                      code: 'qnm_hosonganchan'
                    }
                  ]
                },
                {
                    title: [
                      {
                        languageId: 228,
                        name: 'Tích hợp GPLX sở GTVT'
                      },
                      {
                        languageId: 46,
                        name: 'Integrate Driving license of Transport'
                      }
                    ],
                    route: '/dossier-gplx-gtvt',
                    permission: [
                      {
                        code: 'oneGateGPLXQNM'
                      }
                    ]
                  },
                {
                title: [
                    {
                        languageId: 228,
                        name: 'Quản lý thu phí'
                    },
                    {
                        languageId: 46,
                        name: 'Fee Management'
                    }
                ],
                route: 'qnm-dac-thu/quanly-thuphi',
                permission: [
                    {
                        code: "oneGateFeeManagement"
                    }
                ]
                },
                  {
                    title: [
                      {
                        languageId: 228,
                        name: 'Thống kê lệ phí hồ sơ'
                      },
                      {
                        languageId: 46,
                        name: 'Statistic dossier fee'
                      }
                    ],
                    route: 'qnm-dac-thu/statistic-dossier-fee',
                    permission: [
                      {
                        code: 'oneGateStatisticDossierFee'
                      }
                    ]
                  },
                  {
                   title: [
                      {
                       languageId: 228,
                       name: 'Thống kê hồ sơ hủy'
                      },
                     {
                       languageId: 46,
                       name: 'Cancel Dossier'
                      }
                    ],
                    route: 'qnm-dac-thu/qnm-dossier-cancel',
                    permission: [
                     {
                       code: 'oneGateDossierCancelQNM'
                     }
                    ]
                  },
                  {
                    title: [
                      {
                        languageId: 228,
                        name: 'Cấu hình phát hành biên lai tự động'
                      },
                      {
                        languageId: 46,
                        name: 'Config publish receipt auto'
                      }
                    ],
                    route: 'qnm-dac-thu/auto-receipt',
                    permission: [
                      {
                        code: 'oneGateConfigPublishAutoReceipt'
                      }
                    ]
                  },
                  {
                    title: [
                        {
                            languageId: 228,
                            name: 'Thống kê hồ sơ liên thông'
                        },
                        {
                            languageId: 46,
                            name: 'Report Connecting'
                        }
                    ],
                    route: 'qnm-dac-thu/qnm-report-connect',
                    permission: [
                        {
                            code: 'oneGateConnectReportQNM'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Báo cáo thống kê số hóa hồ sơ'
                        },
                        {
                            languageId: 46,
                            name: 'Statistic report digitization dossier'
                        }
                    ],
                    route: 'qnm-dac-thu/qnm-digitization-dossier-report',
                    permission: [
                        {
                            code: 'oneGateStatisticReportDigitizationDossierQnm'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Báo cáo phục vụ kiểm soát TTHC'
                        },
                        {
                            languageId: 46,
                            name: 'TTPVKSTTHC QNM Report'
                        }
                    ],
                    route: 'statistics/qnm-report-ttpvkstthc',
                    permission: [
                        {
                            code: 'oneGateReportTTPVKSTTHCQNM'
                        }
                    ]
                },
                {
                    title: [
                      {
                       languageId: 228,
                       name: 'Câu hỏi thường gặp cấp Tỉnh'
                     },
                      {
                        languageId: 46,
                        name: 'Frequently Asked Questions Province'
                     }
                    ],
                   route: 'qnm-dac-thu/qnm-cauhoi',
                    permission: [
                      {
                       code: 'oneGateQuestionFrequentlyQNM'
                     }
                   ]
                 },
                 {
                    title: [
                      {
                       languageId: 228,
                       name: 'Báo cáo ngoài danh mục TTHC'
                     },
                      {
                        languageId: 46,
                        name: 'Report Outside List Of TTHC'
                     }
                    ],
                   route: 'qnm-dac-thu/qnm-report-outside-TTHC',
                    permission: [
                      {
                       code: 'oneGateReportOutsideTTHC'
                     }
                   ]
                 },
                 {
                    title: [
                        {
                            languageId: 228,
                            name: 'Tra cứu Log hồ sơ liên thông iGate & iLis'
                        },
                        {
                            languageId: 46,
                            name: 'Reseach Log of dossier iGate & iLis'
                        }
                    ],
                    route: 'qnm-dac-thu/research-log-ilis',
                    permission: [
                        {
                            code: 'oneGateLogiGateILisQnm'
                        },
                        {
                            code: 'VPUB'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Báo cáo dịch vụ công trực tuyến'
                        },
                        {
                            languageId: 46,
                            name: 'Báo cáo dịch vụ công trực tuyến'
                        }
                    ],
                    route: 'qnm-dac-thu/statistic-online-public',
                    permission: [
                        {
                            code: 'oneGateStatisticOnlinePublic'
                        },
                        {
                            code: 'VPUB'
                        }
                    ]
                },
            ]
        },
        {
            mainMenu: [
                {
                    languageId: 228,
                    name: 'Tra cứu'
                },
                {
                    languageId: 46,
                    name: 'Search'
                }
            ],
            icon: 'search',
            code: 'onegate-database',
            active: false,
            listSubMenu: [
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Tra cứu thông tin bảo hiểm xã hội'
                        },
                        {
                            languageId: 46,
                            name: 'Social insurance'
                        }
                    ],
                    route: 'search/social-insurance',
                    permission: [
                        {
                            code: 'onegateSearch'
                        },
                        {
                            code: 'onegateSocialInsurance'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Tra cứu mã số ngân sách'
                        },
                        {
                            languageId: 46,
                            name: 'Budget code'
                        }
                    ],
                    route: 'budget-code',
                    permission: [
                        {
                            code: 'onegateSearch'
                        },
                        {
                            code: 'onegateBudgetCode'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Tra cứu đăng ký doanh nghiệp'
                        },
                        {
                            languageId: 46,
                            name: 'Business Registration'
                        }
                    ],
                    route: 'businessregistration',
                    permission: [
                        {
                            code: 'onegateSearch'
                        },
                        {
                            code: 'oneGateBusinessregistration'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Tra cứu dữ liệu giao thông vận tải'
                        },
                        {
                            languageId: 46,
                            name: 'Transportation Data'
                        }
                    ],
                    route: 'search/transportation-data',
                    permission: [
                        {
                            code: 'onegateSearch'
                        },
                        {
                            code: 'oneGateTransportationData'
                        }
                    ]
                },
            ]
        });
    }
     addMenuChungThuc() {
        this.sidebarMenu.push({
            mainMenu: [
                {
                    languageId: 228,
                    name: this.nameAttestation
                },
                {
                    languageId: 46,
                    name: this.nameAttestationTrans
                }
            ],
            icon: 'spellcheck',
            code: 'onegate-ktm-attestation',
            active: false,
            listSubMenu: [
               {
                title: [
                    {
                        languageId: 228,
                        name: 'Quản lý sổ chứng thực điện tử'
                    },
                    {
                        languageId: 46,
                        name: 'Attestation Book Management'
                    }
                ],
                route: 'ktm-chung-thuc/quan-ly-so-chung-thuc',
                permission: [
                    {
                        code: "ktmEAttestation"
                    }
                ]
                }

            ]

    })
}


    // minhvnt added 20/04/2023 => Thêm mới menu đặc thù cho Quảng Bình
    addMenuSpecificforQBH() {
        console.log('test add menu quảng bình');
        this.sidebarMenu.push({
            mainMenu: [
                {
                    languageId: 228,
                    name: 'Đánh giá giải quyết TTHC'
                },
                {
                    languageId: 46,
                    name: 'Evaluate Result of Administrative Formalities'
                }
            ],
            icon: 'post_add',
            code: 'onegate-database',
            active: false,
            listSubMenu: [
                {
                    title: [
                        { languageId: 228, name: 'Cập nhật phiếu khảo sát trực tiếp' },
                        { languageId: 46, name: 'Update survey sheet direct'}
                    ],
                    route: 'qbh-dac-thu/danhgia-tthc/danhgia-tructiep',
                    permission: [{ code: "oneGateQbhDanhGiaTrucTiep" }]
                },
                {
                    title: [
                        { languageId: 228, name: 'Kết quả đánh giá của đơn vị' },
                        { languageId: 46, name: 'Evaluation result of Department'}
                    ],
                    route: 'qbh-dac-thu/danhgia-tthc/ketqua-danhgia',
                    permission: [{code: "oneGateQbhKetQuaDanhGia" }]
                },
                {
                    title: [
                        { languageId: 228, name: 'Xem KQ thẩm định đánh giá đơn vị' },
                        { languageId: 46, name: 'Evaluate survey of Department'}
                    ],
                    route: 'qbh-dac-thu/danhgia-tthc/thamdinh-danhgia',
                    permission: [{ code: "oneGateQbhThamDinhDanhGia" }]
                },
                {
                    title: [
                        { languageId: 228, name: 'Cập nhật phiếu KS TTHC nhập trên HT khác' },
                        { languageId: 46, name: 'Update detail of survery sheet'}
                    ],
                    route: 'qbh-dac-thu/danhgia-tthc/danhgia-dacthu-chitiet',
                    permission: [{ code: "oneGateQbhDanhGiaDacThuChiTiet" }]
                },
                {
                    title: [
                        { languageId: 228, name: 'Cập nhật DVC 3,4 nhập trên HT khác' },
                        { languageId: 46, name: 'Update synthesis of survey sheet'}
                    ],
                    route: 'qbh-dac-thu/danhgia-tthc/danhgia-dacthu-tonghop',
                    permission: [{ code: "oneGateQbhDanhGiaDacThuTongHop" }]
                },
                {
                    title: [
                        { languageId: 228, name: 'Đồng bộ dữ liệu nhân công' },
                        { languageId: 46, name: 'Synchronous data'}
                    ],
                    route: 'qbh-dac-thu/danhgia-tthc/danhgia-dongbo',
                    permission: [{ code: "oneGateQbhDanhGiaDongBo" }]
                },
                {
                    title: [
                        { languageId: 228, name: ' Cấu hình TTHC/DVCTT 3,4 nhập trên HT khác' },
                        { languageId: 46, name: 'Config Category Specific'}
                    ],
                    route: 'qbh-dac-thu/danhgia-tthc/cauhinh-danhmuc-dacthu',
                    permission: [{ code: "oneGateQbhDanhMucDacThu" }]
                },
                {
                    title: [
                        { languageId: 228, name: ' Thống kê PAKN từ phiếu KS' },
                        { languageId: 46, name: 'Statictics compaint from survey'}
                    ],
                    route: 'qbh-dac-thu/danhgia-tthc/danhgia-thongke',
                    permission: [{ code: "oneGateQbhDanhGiaThongKe" }]
                },
                {
                    title: [
                        { languageId: 228, name: 'Quản lý biên lai tập trung' },
                        { languageId: 46, name: 'Management receipt'}
                    ],
                    route: 'qbh-dac-thu/bienlai-dientu',
                    permission: [{ code: "oneGateQbhDanhGiaDacThuChiTiet" }]
                },
            ]
        });
        console.log(this.sidebarMenu);
    }
    //phucnh.it2-IGATESUPP-44995: menu thống kê phí biên lai HCM
    addMenuReceiptFeeStatisticsToSiteOneGate() {
        this.sidebarMenu.forEach((e, index) => {
            if (e.icon == 'pie_chart' && e.code == 'onegate-database'){
                e.listSubMenu.push(
                    {
                        title: [
                            {
                                languageId: 228,
                                name: 'Thống kê phí biên lai điện tử'
                            },
                            {
                                languageId: 46,
                                name: 'Statistics of receipt fee'
                            }
                        ],
                        route: 'statistics/hcm-stp-receipt-fee-statistics',
                        permission: [
                            {
                                code: 'oneGateAdminMaster'
                            },
                            {
                                code: 'oneGateDossierManager'
                            },
                            {
                                code: 'onegatePermissionViewReceiptFeeStatistics'
                            },
                            {
                                code: 'onegatePermissionViewReceiptFeeStatisticsES'
                            }
                        ]
                    },
                )
            }
         })
    }
    //phucnh.it2-IGATESUPP-67352: menu thống kê hồ sơ bổ sung
    addStatisticDossierAdditionMenu() {
        this.sidebarMenu.forEach((e, index) => {
            if (e.icon == 'pie_chart' && e.code == 'onegate-database'){
                e.listSubMenu.push(
                    {
                        title: [
                            {
                                languageId: 228,
                                name: 'Thống kê hồ sơ bổ sung'
                            },
                            {
                                languageId: 46,
                                name: 'Statistic Dossier Addition'
                            }
                        ],
                        route: 'statistics/statistic-dossier-addition',
                        permission: [

                            {
                                code: 'permisionStatisticDossierAdditionMenu'
                            }
                        ]
                    },
                )
            }
         })
    }
    //49255
    addMenuMenuDossierStatisticsSYTToSiteOneGate(){
        this.sidebarMenu.forEach((e, index) => {
            if (e.icon == 'pie_chart' && e.code == 'onegate-database'){
                e.listSubMenu.push(
                    {
                        title: [
                            {
                                languageId: 228,
                                name: 'Báo cáo Sở Y Tế'
                            },
                            {
                                languageId: 46,
                                name: 'Statistics of SYT'
                            }
                        ],
                        route: 'statistics/hcm-syt-dossier-statistics',
                        permission: [
                            {
                                code: 'onegatePermissionViewDossierStatisticsSYT'
                            }
                        ]
                    },
                )
            }
         })
    }

    //phucnh.it2-IGATESUPP-64084-TraCuuLogVnpost
    addMenuVnpostLogFunctionOneGate(){ ////debugger
        this.sidebarMenu.forEach((e, index) => {
            if (e.icon == 'sync' && e.code == 'onegate-database'){
                e.listSubMenu.push(
                    {
                        title: [
                            {
                                languageId: 228,
                                name: 'Tra cứu log đồng bộ VNPOST'
                            },
                            {
                                languageId: 46,
                                name: 'Look up VNPOST synchronization log'
                            }
                        ],
                        route: 'sync/log-vnpost',
                        permission: [
                            {
                                code: 'permissionVnpostLogFunction'
                            }
                        ]
                    },
                )
            }
         })

    }

    ////phucnh.it2-IGATESUPP-59045-ThongKeHsSYT
    addMenuDossierStatisticsByAppointmentDateHCMOneGate(){
        this.sidebarMenu.forEach((e, index) => {
            if (e.icon == 'pie_chart' && e.code == 'onegate-database'){
                e.listSubMenu.push(
                    {
                        title: [
                            {
                                languageId: 228,
                                name: 'Thống kê toàn Sở Y Tế'
                            },
                            {
                                languageId: 46,
                                name: 'Statistics for the entire Department of Health'
                            }
                        ],
                        route: 'statistics/statistics-dossier-by-appointment-date-hcm',
                        permission: [
                            {
                                code: 'permissionAddMenuDossierStatisticsByAppointmentDateHCM'
                            }
                        ]
                    },
                )
            }
         })
    }

    //IGATESUPP-41788: phucnh.it2: Thêm menu thống kê theo hinh thuc nhận kết quả vào site Onegate
    addMenuStatisticsReceivingResultsToSiteOneGate() {
        this.sidebarMenu.forEach((e, index) => {
            if (e.icon == 'pie_chart' && e.code == 'onegate-database'){
                e.listSubMenu.push(
                    {
                        title: [
                            {
                                languageId: 228,
                                name: 'Thống kê hồ sơ theo hình thức nhận kết quả'
                            },
                            {
                                languageId: 46,
                                name: 'Statistics of records in the form of receiving results'
                            }
                        ],
                        route: 'statistics/hcm-bql-statisticsReceivingResults',
                        permission: [
                            {
                                code: 'oneGateAdminMaster'
                            },
                            {
                                code: 'oneGateDossierManager'
                            },
                            {
                                code: 'oneGateStatisticsReceivingResults'
                            }
                        ]
                    },
                )
            }
         })
    }
    //IGATESUPP-70766: phucnh.it2: Thêm menu tài liệu hướng dân syncCheckPaymentStatusMenu
    addMenuSyncCheckPaymentStatus() {
        this.sidebarMenu.forEach((e, index) => {
            if (e.icon == 'pie_chart' && e.code == 'onegate-database'){
                e.listSubMenu.push(
                    {
                        title: [
                            {
                                languageId: 228,
                                name: 'Kiểm tra và đồng bộ trạng thái thanh toán'
                            },
                            {
                            languageId: 46,
                            name: 'Check and sync dossier payment status'
                            }
                        ],
                        route: 'statistics/sync-check-payment-status',
                        permission: [
                            {
                                code: 'syncCheckPaymentStatusMenuPermission'
                            }
                        ]
                    },
                )
            }
         })
    }


    //IGATESUPP-30852: phucnh.it2: Thêm menu tài liệu hướng dân
    addMenuGuideSiteOneGate() {
        this.sidebarMenu.push({
            mainMenu: [
                {
                    languageId: 228,
                    name: 'Tài liệu hướng dẫn'
                },
                {
                    languageId: 46,
                    name: 'Document Guide'
                }
            ],
            icon: 'assignment',
            code: 'onegate-database',
            active: false,
            listSubMenu: [
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Danh sách tài liệu'
                        },
                        {
                            languageId: 46,
                            name: 'Document Guide List'
                        }
                    ],
                    route: 'support/tutorial',
                    permission: [
                        {
                            code: 'oneGateAdminMaster'
                        },
                        {
                            code: 'oneGateDossierManager'
                        },
                        {
                            code: 'oneGateTutorial'
                        }
                    ]
                },
            ]
        });
    }



        //IGATESUPP-58132: thuannm.tgg: Thêm menu đánh giá cán bộ QĐ 25 HCM
        addMenuOwnerRatingsSiteOneGate() {
            this.sidebarMenu.push({
                mainMenu: [
                    {
                        languageId: 228,
                        name: 'Đánh giá cán bộ'
                    },
                    {
                        languageId: 46,
                        name: 'Officer Ratings'
                    }
                ],
                icon: 'fact_check',
                code: 'onegate-database',
                active: false,
                listSubMenu: [
                    {
                        title: [
                            {
                                languageId: 228,
                                name: 'Đánh giá trực tiếp'
                            },
                            {
                                languageId: 46,
                                name: 'Direct Ratings'
                            }
                        ],
                        route: 'officer-rating/owner-rating',
                        permission: [
                            {
                                code: 'ownerRatings'
                            }
                        ]
                    },
                ]
            });
        }

    //nguyenttai.hcm - IGATESUPP-59871 - Thêm menu thông báo tính năng mới/thay đổi
    addMenuAnnouncement() {
        this.sidebarMenu.push({
            mainMenu: [
                {
                    languageId: 228,
                    name: 'Thông tin thông báo'
                },
                {
                    languageId: 46,
                    name: 'Announcement'
                }
            ],
            icon: 'notifications',
            code: 'announcement',
            active: false,
            listSubMenu: [
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Danh sách thông báo'
                        },
                        {
                            languageId: 46,
                            name: 'Announcement List'
                        }
                    ],
                    route: 'announcement/list',
                    permission: [
                        {
                            code: 'oneGateAdminMaster'
                        },
                        {
                            code: 'oneGateAnnouncement'
                        },
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Quản lý thông tin thông báo'
                        },
                        {
                            languageId: 46,
                            name: 'Announcement management'
                        }
                    ],
                    route: 'announcement/management',
                    permission: [
                        {
                            code: 'oneGateAdminMaster'
                        },
                    ]
                },
            ]
        });
    }

    getAnnoucementPopup() {
        let searchString = '?page=0' +
                            '&spec=page&sort=updatedDate,desc' +
                            '&size=10' +
                            '&status=1' +
                            '&userId=' + this.UID[0];
        let userAgency = JSON.parse(localStorage.getItem('userAgency'));
        // Kiểm tra thông báo cho cơ quan, đơn vị cha
        if (userAgency.parent != null) {
            searchString += '&agency=' + userAgency.id + ',' + userAgency.parent.id;
        } else {
            searchString += '&agency=' + userAgency.id;
        }
        this.surfeedService.getListAnnouncement(searchString).subscribe(data => {
            // Có thông báo chưa đọc, tạo cửa sổ thông báo
            if (data.numberOfElements > 0) {
                const dialogData = new AnnouncementPopupDialogModel(data, this.UID[0]);
                this.dialog.open(AnnouncementPopupComponent, {
                    minWidth: '70vw',
                    data: dialogData,
                    disableClose: true,
                    autoFocus: false
                });
            }
        });
    }

    getClientIp() {
        this.http.get<{ ip: string }>(!!this.deploymentService.env?.iPCheckUrl ? this.deploymentService.env?.iPCheckUrl : 'https://api.ipify.org/?format=json')
            .subscribe(data => {
                localStorage.setItem('clientIP', data.ip);
            });
    }

    addMenuDossierSyncGTVT() {
        this.sidebarMenu.push({
            mainMenu: [
                {
                    languageId: 228,
                    name: 'Quản lý hồ sơ của Bộ Giao Thông Vận Tải'
                },
                {
                    languageId: 46,
                    name: 'Manage dossier of the Ministry of Transport'
                }
            ],
            icon: 'rule_folder',
            code: 'onegate-database',
            active: false,
            listSubMenu: [
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Danh sách hồ sơ của Bộ Giao Thông Vận Tải'
                        },
                        {
                            languageId: 46,
                            name: 'Dossier List of the Ministry of Transport'
                        }
                    ],
                    route: 'dossier-gtvt/dossier-sync-gtvt',
                    permission: [
                        {
                            code: 'oneGateDossierManagerMOT'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Thống kê xử lý hồ sơ'
                        },
                        {
                            languageId: 46,
                            name: 'Dossier Statistic'
                        }
                    ],
                    route: 'dossier-gtvt/dossier-statistic-gtvt',
                    permission: [
                        {
                            code: 'oneGateDossierManagerMOT'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Quản lý hồ sơ đã đồng bộ'
                        },
                        {
                            languageId: 46,
                            name: 'Manage dossier'
                        }
                    ],
                    route: 'dossier-gtvt/dossier-storage-gtvt',
                    permission: [
                        {
                            code: 'oneGateDossierManagerMOT'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Hồ sơ Giấy phép lái xe'
                        },
                        {
                            languageId: 46,
                            name: 'Driver license Dossier'
                        }
                    ],
                    route: 'dossier-gtvt/dossier-driver-license-hgg',
                    permission: [
                        {
                            code: 'oneGateDossierDriverLicenseHGG'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Tra cứu hồ sơ từ Cục ĐBVN'
                        },
                        {
                            languageId: 46,
                            name: 'Tra cứu hồ sơ từ Cục ĐBVN'
                        }
                    ],
                    route: 'statistics/hcm-gplx-report',
                    permission: [
                        {
                            code: 'oneGateGPLXStatistical'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Thông tin giấy phép lái xe'
                        },
                        {
                            languageId: 46,
                            name: 'Driving License all'
                        }
                    ],
                    route: 'dossier-gtvt/driving-license-all',
                    permission: [
                        {
                            code: 'oneGateDossierManagerDrivingLicense'
                        }
                    ]
                }
            ]
        });
    }

    addMenuDossierSyncGTVTQni() {
        this.sidebarMenu.push({
            mainMenu: [
                {
                    languageId: 228,
                    name: 'Quản lý hồ sơ của Bộ Giao Thông Vận Tải'
                },
                {
                    languageId: 46,
                    name: 'Manage dossier of the Ministry of Transport'
                }
            ],
            icon: 'rule_folder',
            code: 'onegate-database',
            active: false,
            listSubMenu: [
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Danh sách hồ sơ của Bộ Giao Thông Vận Tải'
                        },
                        {
                            languageId: 46,
                            name: 'Dossier List of the Ministry of Transport'
                        }
                    ],
                    route: 'dossier-gtvt/dossier-sync-gtvt',
                    permission: [
                        {
                            code: 'oneGateDossierManagerMOT'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Thống kê xử lý hồ sơ'
                        },
                        {
                            languageId: 46,
                            name: 'Dossier Statistic'
                        }
                    ],
                    route: 'dossier-gtvt/dossier-statistic-gtvt',
                    permission: [
                        {
                            code: 'oneGateDossierManagerMOT'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Thông tin giấy phép lái xe'
                        },
                        {
                            languageId: 46,
                            name: 'Driving License Qni'
                        }
                    ],
                    route: 'dossier-gtvt/driving-license-qni',
                    permission: [
                        {
                            code: 'oneGateDossierGPLXManage'
                        }
                    ]
                }
            ]
        });
    }


    // IGATESUPP-52526: Thêm Menu "DỮ LIỆU TỔ CHỨC/CÁ NHÂN ĐỀ NGHỊ CẤP CHỨNG CHỈ"
    addMenuRequestCertification() {
        this.sidebarMenu.push({
            mainMenu: [
                {
                    languageId: 228,
                    name: 'Dữ liệu tổ chức/cá nhân đề nghị cấp chứng chỉ'
                },
                {
                    languageId: 46,
                    name: 'Organization/personal data requesting certification'
                }
            ],
            icon: 'rule_folder',
            code: 'onegate-database',
            active: false,
            listSubMenu: [
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Dữ liệu tổ chức đề nghị cấp chứng chỉ'
                        },
                        {
                            languageId: 46,
                            name: 'Organization data requesting certification'
                        }
                    ],
                    route: 'request-certification/organization',
                    permission: [
                        {
                            code: 'oneGateRequestCertification'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Dữ liệu cá nhân đề nghị cấp chứng chỉ'
                        },
                        {
                            languageId: 46,
                            name: 'Personal data requesting certification'
                        }
                    ],
                    route: 'request-certification/personal',
                    permission: [
                        {
                            code: 'oneGateRequestCertification'
                        }
                    ]
                }
            ]
        });
    }

    //IGATESUPP-43675: nanta: Thêm menu đặc thù QBH
    addMenuQBHSiteOneGate() {
        this.sidebarMenu.push({
            mainMenu: [
                {
                    languageId: 228,
                    name: 'Chức năng đặc thù'
                },
                {
                    languageId: 46,
                    name: 'Specific Function'
                }
            ],
            icon: 'donut_large',
            code: 'onegate-database',
            active: false,
            listSubMenu: [
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Tra cứu hồ sơ theo đơn vị'
                        },
                        {
                            languageId: 46,
                            name: 'Search dossier by agency'
                        }
                    ],
                    route: 'search-dossier-qbh',
                    permission: [
                        {
                            code: 'oneGateSearchDossierQBH'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Tra cứu log tự động tiếp nhận sau 8 tiếng'
                        },
                        {
                            languageId: 46,
                            name: 'Search auto log accept 8h'
                        }
                    ],
                    route: 'search-auto-log-8h-qbh',
                    permission: [
                        {
                            code: 'oneGateSearchAutoLog8hQbh'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Quản lý lịch hẹn giao dịch'
                        },
                        {
                            languageId: 46,
                            name: 'Quản lý lịch hẹn giao dịch'
                        }
                    ],
                    route: 'quanly-lichhen',
                    permission: [
                        {
                            code: "oneGateAppointmentScheduleQbh"
                        }
                    ]
                },
            ]
        });
    }

    // IGATESUPP-43675: nanta: Thêm menu KGG
    addMenuKGGSiteOneGate() {
        this.sidebarMenu.push(
            // Tra cứu Hồ sơ đăng ký doanh nghiệp
            {
                mainMenu: [
                    {
                        languageId: 228,
                        name: 'Tra cứu Đăng ký Doanh nghiệp'
                    },
                    {
                        languageId: 46,
                        name: 'Search / Business registration'
                    }
                ],
                icon: 'search',
                code: 'business-registration',
                active: false,
                listSubMenu: [
                    {
                        title: [
                            {
                                languageId: 228,
                                name: 'Danh sách Đăng ký doanh nghiệp'
                            },
                            {
                                languageId: 46,
                                name: 'List of business registration documents'
                            }
                        ],
                        route: 'search/enterprise-kgg',
                        permission: [
                            {
                                code: 'oneGateEnterpriseRegistration'
                            }
                        ]
                    },
                    {
                        title: [
                            {
                                languageId: 228,
                                name: 'Danh sách đăng ký hợp tác xã'
                            },
                            {
                                languageId: 46,
                                name: 'List of cooperative registration documents'
                            }
                        ],
                        route: 'search/cooperative-kgg',
                        permission: [
                            {
                                code: 'oneGateCooperativeRegistration'
                            }
                        ]
                    },
                    {
                        title: [
                            {
                                languageId: 228,
                                name: 'Danh sách đăng ký hộ kinh doanh'
                            },
                            {
                                languageId: 46,
                                name: 'List of households registration documents'
                            }
                        ],
                        route: 'search/households-kgg',
                        permission: [
                            {
                                code: 'oneGateHouseholdsRegistration'
                            }
                        ]
                    }
                ]
            }
        );
    }

    addTraCuuBXDHPG(){
        this.sidebarMenu.push({
            mainMenu: [
                {
                    languageId: 228,
                    name: 'Quản lý hồ sơ từ Bộ Xây Dựng'
                },
                {
                    languageId: 46,
                    name: 'Manage dossier from the Ministry of Construction'
                }
            ],
            icon: 'rule_folder',
            code: 'onegate-database',
            active: false,
            listSubMenu: [
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Tra cứu hồ sơ cấp phép xây dựng'
                        },
                        {
                            languageId: 46,
                            name: 'Look up construction permit documents'
                        }
                    ],
                    route: 'hpg_bxd/search_dossier',
                    permission: [
                        {
                            code: 'oneGateDossierBXD'
                        }
                    ]
                },
            ]
        });
    }

    checkShowSubMenuNoti () {
        let userAgency = JSON.parse(localStorage.getItem('userAgency'));
        const index = this.dossierMenuTaskRingWithAgency.listAgency.findIndex(object => {
            return object.AgencyID === userAgency.id;
            });
        if (index != -1) {
            return true;
        } else {
            return false;
        }
        return false;
    }

    async onClickSubMenuRemind(type: number) {
        let data : any[] = await this.getListInfoDossier(type);
        const dialogData = new RingMenuDetailModel(data);
        const dialogRef = this.dialog.open(RingMenuDetail, {
            width: '80vw',
            height: '90%',
            data: dialogData,
            disableClose: true,
            autoFocus: false
        });
        dialogRef.afterClosed().subscribe(dialogResult => {
        });
    }

    async getListInfoDossier (typeMenu : number) {
        let data: any[];

        const userAgency = JSON.parse(localStorage.getItem('userAgency'));
        const listUserAgency = JSON.parse(localStorage.getItem('listAgencyUser'));
        let userExperience;
        if (!!listUserAgency && listUserAgency.length !== 0 && !!this.userAgency) {
            userExperience = listUserAgency.find(item => item.agency?.id === userAgency?.id);
        }
        let approveAgencysId = '';
        for (let k = 0 ; k < this.approvalAgencyList.length; k++){
            approveAgencysId = approveAgencysId + this.approvalAgencyList[k];
            if (k < this.approvalAgencyList.length - 1){
                approveAgencysId = approveAgencysId + ',';
            }
        }
        const UID = localStorage.getItem('UID');
        if(this.checkShowSubMenuNotiValue){
            let agencyIdIndex = this.dossierMenuTaskRingWithAgency.listAgency.findIndex(object => {
                return object.AgencyID === userAgency.id;
            });
            if (agencyIdIndex != -1) {
                let nearByDueAcceptedWhenValue = this.dossierMenuTaskRingWithAgency?.listAgency[agencyIdIndex]?.NearbyDueAcceptedWhen != null ? this.dossierMenuTaskRingWithAgency.listAgency[agencyIdIndex].NearbyDueAcceptedWhen :
                                                (this.dossierMenuTaskRingWithAgency?.NearbyDueAcceptedWhen != null ? this.dossierMenuTaskRingWithAgency.NearbyDueAcceptedWhen : 2);
                let overDueAcceptedWhen = this.dossierMenuTaskRingWithAgency?.listAgency[agencyIdIndex]?.OverDueAcceptedWhen != null ? this.dossierMenuTaskRingWithAgency.listAgency[agencyIdIndex].OverDueAcceptedWhen :
                                                (this.dossierMenuTaskRingWithAgency?.OverDueAcceptedWhen != null ? this.dossierMenuTaskRingWithAgency.OverDueAcceptedWhen : 0);
                let nearbyDueProcessingWhen = this.dossierMenuTaskRingWithAgency?.listAgency[agencyIdIndex]?.NearbyDueProcessingWhen != null ? this.dossierMenuTaskRingWithAgency.listAgency[agencyIdIndex].NearbyDueProcessingWhen :
                                                (this.dossierMenuTaskRingWithAgency?.NearbyDueProcessingWhen != null ? this.dossierMenuTaskRingWithAgency.NearbyDueProcessingWhen : 2);
                let overDueProcessingWhen = this.dossierMenuTaskRingWithAgency?.listAgency[agencyIdIndex]?.OverDueProcessingWhen != null ? this.dossierMenuTaskRingWithAgency.listAgency[agencyIdIndex].OverDueProcessingWhen :
                                                (this.dossierMenuTaskRingWithAgency?.OverDueProcessingWhen != null ? this.dossierMenuTaskRingWithAgency.OverDueProcessingWhen : 0);
                let assigneeId = UID ? UID : null;
                let candidateGroupId = userAgency.id;
                let senderId = null;
                let candidatePositionId = userExperience?.position?.id ? userExperience.position.id : null;
                let candidateGroupParentId = userAgency?.parent?.id ? userAgency.parent.id : null;
                let currentTaskAgencyTypeId = userAgency?.tag ? userAgency.tag.toString() : null;
                let approveAgencysIds =  approveAgencysId ? approveAgencysId : null;
                //let  agencyMaxDueReceive = this.dossierMenuTaskRingWithAgency?.agencyMaxDueReceive ?  this.dossierMenuTaskRingWithAgency.agencyMaxDueReceive : 8 ;

                let searchString = 'agency-id=' + userAgency.parent.id
                    + '&nearByDueAcceptedWhen=' + nearByDueAcceptedWhenValue
                    + '&overDueAcceptedWhen=' + overDueAcceptedWhen
                    + '&nearbyDueProcessingWhen=' + nearbyDueProcessingWhen
                    + '&overDueProcessingWhen=' + overDueProcessingWhen
                    + '&assignee-id=' + assigneeId
                    + '&candidate-group-id=' + candidateGroupId
                    + '&sender-id='
                    + '&candidate-position-id=' + candidatePositionId
                    + '&candidate-group-parent-id=' + candidateGroupParentId
                    + '&current-task-agency-type-id=' + currentTaskAgencyTypeId
                    + '&approve-agencys-id=' + approveAgencysIds
                    //+ '&agency-max-due-receive=' + agencyMaxDueReceive
                    + '&type-menu=' + typeMenu;

                    await this.dossierService.getDossierMenuTaskRingSubMenuDetail(searchString).toPromise().then(res => {
                        if(res.length > 0) {
                            data = res;
                        }
                    });
            }
        }
        return data;
    }

    getListSector() {
        const search = !this.searchSectorKeyword.trim() ? "" : this.searchSectorKeyword.toLowerCase();
        return new Promise<void>(async resolve => {
            if (this.isFullListSector) {
                resolve();
            } else {
                let i = 0;
                setTimeout(() => {
                    let accountId = localStorage.getItem('tempUID');
                    let userAgency = JSON.parse(localStorage.getItem('userAgency'));
                    while (i < 5) {
                        if (accountId != null || accountId != '') {
                            break;
                        } else {
                            accountId = localStorage.getItem('tempUID');
                        }
                    }
                    this.sectorService.getUserSectorOnlyOne(accountId, userAgency.id).subscribe(data => {
                        console.log("data", data);
                        this.listSectorId = data.sectorIds;
                        this.listSectorUser = "";
                        if (!!this.listSectorId && this.listSectorId.length !== 0) {
                            for (let i = 0; i < data.sectors.length; i++) {
                                this.listSectorUser += data.sectors[i].id + ',';
                            }
                            this.sectors = JSON.parse(JSON.stringify(this.listSector).replace(/null/g, '""'));
                            // this.sectorFiltered.next(this.sectors);
                            this.listsectorSearch = JSON.parse(JSON.stringify(this.listsectorSearch).replace(/null/g, '""'));
                            if (this.searchSectorKeyword !== '') {
                                this.sectorFiltered.next(this.listsectorSearch.filter(sec => sec.name.toLowerCase().indexOf(search) > -1));
                            }
                            else {
                                this.sectorFiltered.next(this.sectors);
                            }
                        } else {
                            const searchString = '?keyword=' + this.searchSectorKeyword + '&agency-id=' + userAgency.id + '&page=' + this.listSectorPage + '&size=' + 1000 + '&spec=' + this.paginationType + '&sort=name.name,asc&status=1';
                            this.procedureService.getListSector(searchString).subscribe(data => {
                                this.listSectorId = data.content;
                                if (!!this.listSectorId && this.listSectorId.length !== 0) {
                                } else {
                                    const searchString = '?keyword=' + this.searchSectorKeyword + '&agency-id=' + userAgency?.parent?.id + '&page=' + this.listSectorPage + '&size=' + 1000 + '&spec=' + this.paginationType + '&sort=name.name,asc&status=1';
                                    this.procedureService.getListSector(searchString).subscribe(data => {
                                        this.listSectorId = data.content;
                                        if (!!this.listSectorId && this.listSectorId.length !== 0) {
                                        }
                                        else {
                                            const searchString = '?keyword=' + this.searchSectorKeyword + '&agency-id=' + '&page=' + this.listSectorPage + '&size=' + 1000 + '&spec=' + this.paginationType + '&sort=name.name,asc&status=1';
                                            this.procedureService.getListSector(searchString).subscribe(data => {
                                                this.listSectorId = data.content;
                                            });
                                        }
                                    });
                                }
                            });
                        }
                        resolve();
                    }, err => {
                        console.log(err);
                        const searchString = '?keyword=' + this.searchSectorKeyword + '&agency-id=' + userAgency.id + '&page=' + this.listSectorPage + '&size=' + 1000 + '&spec=' + this.paginationType + '&sort=name.name,asc&status=1';
                        this.procedureService.getListSector(searchString).subscribe(data => {
                            this.listSectorId = data.content;
                            if (!!this.listSectorId && this.listSectorId.length !== 0) {
                            } else {
                                if (!!userAgency?.parent?.id) {
                                    const searchString = '?keyword=' + this.searchSectorKeyword + '&agency-id=' + userAgency.parent.id + '&page=' + this.listSectorPage + '&size=' + 1000 + '&spec=' + this.paginationType + '&sort=name.name,asc&status=1';
                                    this.procedureService.getListSector(searchString).subscribe(data => {
                                        this.listSectorId = data.content;
                                        if (!!this.listSectorId && this.listSectorId.length !== 0) {
                                        } else {
                                            const searchString = '?keyword=' + this.searchSectorKeyword + '&agency-id=' + '&page=' + this.listSectorPage + '&size=' + 1000 + '&spec=' + this.paginationType + '&sort=name.name,asc&status=1';
                                            this.procedureService.getListSector(searchString).subscribe(data => {
                                                this.listSectorId = data.content;
                                            });
                                        }
                                    });
                                } else {
                                    const searchString = '?keyword=' + this.searchSectorKeyword + '&agency-id=' + '&page=' + this.listSectorPage + '&size=' + 1000 + '&spec=' + this.paginationType + '&sort=name.name,asc&status=1';
                                    this.procedureService.getListSector(searchString).subscribe(data => {
                                        this.listSectorId = data.content;
                                    });
                                }
                            }
                        });
                        resolve();
                    });
                }, 2000);
            }
        });
    }

    async redirectChungThuc(){
      let url = this.configs?.chungThucDienTu?.domain_chungthuc;
      const token = await this.chungThucDienTuService.getTokenId().toPromise();
      this.keycloakService.loadUserProfile().then(user => {
        let adapter = this.apiProviderService.getUrl('digo', 'adapter');
        adapter = adapter.replace("//integration","/integration");
        adapter = adapter.replace("//ad","/ad");
        url += user.username ? `?username=${user.username}` : ``;
        url += localStorage.getItem('userToken') ? `&ig_session=${token.id}` : ``;
        url += adapter ? `&urlcallback=${adapter}` : ``;
        url += window.location.origin ? `&urllogout=${window.location.origin}` : ``;
        window.open(url, '_blank');
      });
    }

    addMenuQBHAutoReceiveDossier() {
        this.sidebarMenu.push({
            mainMenu: [
                {
                    languageId: 228,
                    name: 'Tự động tiếp nhận'
                },
                {
                    languageId: 46,
                    name: 'Auto Receive Dosser'
                }
            ],
            icon: 'pie_chart',
            code: 'auto-receive-qbh',
            active: false,
            listSubMenu: [
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Tiếp nhận hàng loạt hồ sơ quá 8 tiếng'
                        },
                        {
                            languageId: 46,
                            name: 'Receive list Dossier more than 8 hours'
                        }
                    ],
                    route: 'business-regist/personal',
                    permission: [
                        {
                            code: 'BusinessRegistPersonal'
                        },
                        {
                            code: 'BusinessRegist'
                        }
                    ]
                }
            ]
        });
    }
     // Add menu Đăng ký Kinh doanh
     addMenuQBHBusinessRegistration() {
        this.sidebarMenu.push({
            mainMenu: [
                {
                    languageId: 228,
                    name: 'Đăng ký kinh doanh'
                },
                {
                    languageId: 46,
                    name: 'Business Registration'
                }
            ],
            icon: 'pie_chart',
            code: 'business-regist',
            active: false,
            listSubMenu: [
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Quản lý cơ sở kinh doanh hộ cá thể'
                        },
                        {
                            languageId: 46,
                            name: 'Management of individual business establishments'
                        }
                    ],
                    route: 'business-regist/personal',
                    permission: [
                        {
                            code: 'BusinessRegistPersonal'
                        },
                        {
                            code: 'BusinessRegist'
                        }
                    ]
                },
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Quản lý đăng ký kinh doanh hợp tác xã'
                        },
                        {
                            languageId: 46,
                            name: 'Managing cooperative business registration'
                        }
                    ],
                    route: 'business-regist/cooperative',
                    permission: [
                        {
                            code: 'BusinessRegist'
                        },
                        {
                            code: 'BusinessRegistCooperative'
                        }
                    ]
                },
            ]
        });
    }

    addMenuCMUSiteOneGate() {
        this.sidebarMenu.push(
            // Tra cứu Hồ sơ đăng ký doanh nghiệp
            {
                mainMenu: [
                    {
                        languageId: 228,
                        name: 'Tra cứu Đăng ký Doanh nghiệp'
                    },
                    {
                        languageId: 46,
                        name: 'Search / Business registration'
                    }
                ],
                icon: 'search',
                code: 'business-registration-cmu',
                active: false,
                listSubMenu: [
                    {
                        title: [
                            {
                                languageId: 228,
                                name: 'Danh sách Đăng ký doanh nghiệp'
                            },
                            {
                                languageId: 46,
                                name: 'List of business registration documents'
                            }
                        ],
                        route: 'search/enterprise-cmu',
                        permission: [
                            {
                                code: 'oneGateEnterpriseRegistration'
                            }
                        ]
                    },
                    {
                        title: [
                            {
                                languageId: 228,
                                name: 'Danh sách đăng ký hợp tác xã'
                            },
                            {
                                languageId: 46,
                                name: 'List of cooperative registration documents'
                            }
                        ],
                        route: 'search/cooperative-cmu',
                        permission: [
                            {
                                code: 'oneGateCooperativeRegistration'
                            }
                        ]
                    },
                    {
                        title: [
                            {
                                languageId: 228,
                                name: 'Danh sách đăng ký hộ kinh doanh'
                            },
                            {
                                languageId: 46,
                                name: 'List of households registration documents'
                            }
                        ],
                        route: 'search/households-cmu',
                        permission: [
                            {
                                code: 'oneGateHouseholdsRegistration'
                            }
                        ]
                    }
                ]
            }
        );
    }

    addMenuSpecificforGLI() {
        this.sidebarMenu.push({
            mainMenu: [
                {
                    languageId: 228,
                    name: 'Chức năng đặc thù'
                },
                {
                    languageId: 46,
                    name: 'Specific Function'
                }
            ],
            icon: 'donut_large',
            code: 'onegate-database',
            active: false,
            listSubMenu: [
                {
                    title: [
                        {
                            languageId: 228,
                            name: 'Báo cáo hồ sơ cấp đổi GPLX'
                        },
                        {
                            languageId: 46,
                            name: 'Báo cáo hồ sơ cấp đổi GPLX'
                        }
                    ],
                    route: 'gli-dac-thu/report-sync-gplx',
                    permission: [
                        {
                            code: "gliReportSyncGplx"
                        }
                    ]
                },
            ]
        })
    }

    getDossierDueQuantity() {
        let perUser = this.userService.getUserPermissions();
        let checkPer = perUser.find((element) => element?.permission?.code == "dlkHoSoToiHan");
        let userAgencyCount = JSON.parse(localStorage.getItem('listAgencyUser'))?.length || 0;

        if (this.remindDossierDueDlk != null && this.remindDossierDueDlk == 1 && checkPer != undefined) {
            let parentAgency = null;
            if (this.userAgency !== null && this.userAgency !== undefined) {
                if (!!this.userAgencyLocal.parent && !!this.userAgencyLocal.parent.id && userAgencyCount === 1) {
                    parentAgency = this.userAgencyLocal.parent.id;
                } else {
                    parentAgency = this.userAgencyLocal.id;
                }
            }

            if (parentAgency != null && parentAgency != "") {
                this.getListAgencyParams(parentAgency);
            }
        } else {
            this.remindDossierDueDlk = false;
        }
    }

    getListAgencyParams(parentAgencyId) {
        let childAgencyList = [];
        const searchString = '?parent-id=' + parentAgencyId + '&page=0&size=10000&sort=name.name,asc&status=1';

        this.procedureService.getListAgencyWithParent(searchString).subscribe(res => {
          let listAgency = res.content;

          if (listAgency != null && listAgency.length > 0) {
            listAgency.forEach(item => {
              if (item != null && item.id != null) {
                childAgencyList.push(item.id);
              }
            })
          }

          this.getListHoSo(parentAgencyId, childAgencyList);
        }, err => {
          console.log(err);
        });
      }

    getListHoSo(parentAgency, childAgencyList) {
        let data = {
            page: 0,
            size: 10,
            parentAgency: parentAgency,
            receivingKind: '',
            isTTPVHCC: 1,
            all: 0,
            fromDateNumber: 0,
            toDateNumber: 1,
            due: 1,
            childAgencyList: childAgencyList
        }

        this.dlkStatisticService.getDossierToiHan(data).subscribe(res => {
            this.remindDDCount = res.totalElements;
        }, err => {
            console.log(err);
        });
    }

    // IGATESUPP-113272
    removeMenuLedger() {
        // Dữ liệu một cửa
        let section = this.sidebarMenu[1];
        // route: 'onegate-data/list-ledger'
        let i = 0;
        while(i < section.listSubMenu.length) {
            let menuItem = section.listSubMenu[i];
            if (menuItem.route === 'onegate-data/list-ledger') {
                section.listSubMenu.splice(i, 1);
                break;
            }
            i++;
        }
    }
}
