// ================================= searchForm
.dossier-config .mat-cell{
    overflow-wrap: anywhere;
    margin: 6px;
}

.margin_button_add_pattern{
    margin-left: 6px;
}

.dossier-config .mat-header-cell {
    overflow-wrap: anywhere;
    margin: 6px;
}

::ng-deep {
    .frm_searchbar {
        .searchForm {
            .mat-form-field-appearance-outline {
                .mat-form-field-outline {
                    color: transparent;
                    background-color: #eaebeb;
                    border-radius: 5px;
                }
                .mat-form-field-outline-thick {
                    color: #dddddd;
                }
            }
            .searchBtn {
                margin-top: 0.3em;
                background-color: #ce7a58;
                color: #fff;
                height: 3.2em !important;
            }
            .searchBtn2 {
                margin-top: 0.3em;
                color: #ce7a58;
                height: 3.2em !important;
            }
            .formFieldItems {
                flex-wrap: wrap;
            }
            .mat-form-field-appearance-outline {
                .mat-form-field-infix {
                    padding: 0.8em 0;
                }
            }
            .mat-form-field-label-wrapper {
                top: -1em;
            }
            .mat-form-field.mat-focused {
                .mat-form-field-label {
                    color: #ce7a58;
                    font-size: 14px;
                }
            }
            .mat-form-field-appearance-outline.mat-form-field-can-float.mat-form-field-should-float {
                .mat-form-field-label {
                    color: #ce7a58;
                    transform: translateY(-1.55em) scale(1);
                    margin-bottom: 1em;
                }
            }
            .advanced-box {
                .panel {
                    background-color: #f9f9f9;
                    box-shadow: none;
                    flex-wrap: wrap;
                }
    
                .mat-expansion-panel-body {
                    padding: 0 !important;
                }
            }
    
            .advanced-button {
                margin: auto;
                color: #ce7a58;
                margin-bottom: 0.7em;
                cursor: pointer;
    
                .mat-icon {
                    vertical-align: middle;
                }
            }
    
        }
        .primary-btn {
            background-color: #ce7a58;
            color: #fff;
            height: 3.75em;
            margin-top: 0.25em;
        }
    }
}

.formdefault-config .updateBtn {
    margin-top: 0.3em;
    background-color: #ce7a58;
    color: #fff;
    height: 3.2em !important;
}


// ================================= frm_main
.frm_main {
    border-radius: 4px;
    padding: 1em;
    box-shadow: 0px 1px 15px rgba(69, 65, 78, 0.19);
}

.frm_main .btn_add {
    background-color: #e8e8e8;
    color: #666;
    float: right;
}

.frm_main .btn_add .mat-icon {
    color: #ce7a58;
}

// ================================= frm_tbl
.frm_tbl {
}

.frm_tbl table {
    border-radius: 4px;
    border: 1px solid #ececec;
    width: 100%;
}

.frm_searchbar .searchForm .formdefault-config{
    margin-top: 15px;
}

::ng-deep .frm_tbl .mat-header-row {
    background-color: #e8e8e8;
}

::ng-deep .frm_tbl .mat-header-row .mat-header-cell {
    color: #495057;
    font-size: 14px;
    // display: grid;
}

::ng-deep .frm_tbl .mat-header-row .mat-header-cell p {
    margin-bottom: 0;
    font-weight: 400;
    font-style: italic;
}

::ng-deep .frm_tbl .mat-column-stt {
    flex: 0 0 5%;
}

::ng-deep .frm_tbl .mat-column-code {
    flex: 0 0 20%;
}

::ng-deep .frm_tbl .mat-column-procedure {
    flex: 0 0 30%;
}

::ng-deep .frm_tbl .mat-column-name {
    flex: 1 0 5%;
}

::ng-deep .frm_tbl .mat-column-logo {
    flex: 0 0 10%;
}

::ng-deep .frm_tbl .mat-column-action {
    flex: 0 0 10%;
    float: right;
}

::ng-deep .frm_tbl .btn_downloadForm {
    padding: 0;
    color: #ce7a58;
}

::ng-deep .frm_tbl .cell_code {
    color: #ce7a58;
}

::ng-deep .frm_tbl .btn_downloadForm .mat-button-wrapper {
    display: flex;
}

::ng-deep .frm_tbl .btn_downloadForm .mat-button-wrapper .download_icon .mat-icon {
    vertical-align: middle;
    margin-right: 0.2em;
    background-color: #ce7a58;
    color: #fff;
    border-radius: 50%;
    padding: 0.2em;
    transform: scale(0.8);
}

::ng-deep .frm_tbl .btn_downloadForm .mat-button-wrapper span {
    align-self: center;
}

::ng-deep .frm_tbl .mat-row {
    border: none;
}

::ng-deep .frm_tbl .mat-row:nth-child(even) {
    background-color: #FAFAFA;
}

::ng-deep .frm_tbl .mat-row:nth-child(odd) {
    background-color: #fff;
}

::ng-deep .menuAction {
    font-weight: 500;
}

::ng-deep .menuAction .mat-icon {
    color: #ce7a58;
}

@media screen and (max-width: 600px) {
    .frm_tbl .mat-header-row {
        display: none;
    }

    .frm_tbl .mat-table {
        border: 0;
        vertical-align: middle;
    }

    .frm_tbl .mat-table caption {
        font-size: 1em;
    }

    .frm_tbl .mat-table .mat-row {
        border-bottom: 5px solid #ddd;
        display: block;
        min-height: unset;
    }

    .frm_tbl .mat-table .mat-cell {
        border-bottom: 1px solid #ddd;
        display: block;
        font-size: 14px;
        text-align: right;
        margin-bottom: 4%;
        padding: 0 .5em;
    }

    .frm_tbl .mat-table .mat-cell:before {
        content: attr(data-label);
        float: left;
        font-weight: 500;
        font-size: 14px;
    }

    .frm_tbl .mat-table .mat-cell:last-child {
        border-bottom: 0;
    }

    .frm_tbl .mat-table .mat-cell:first-child {
        margin-top: 4%;
    }

    ::ng-deep .frm_tbl .mat-column-status {
        float: unset;
    }
    
    ::ng-deep .frm_tbl .mat-column-action {
        float: unset;
    }

    ::ng-deep .frm_tbl .mat-column-logo {
        float: unset;
    }

    ::ng-deep .frm_tbl .mat-column-procedure {
        float: unset;
    }

    ::ng-deep .frm_tbl .mat-row:nth-child(even) {
        background-color: unset;
    }
    
    ::ng-deep .frm_tbl .mat-row:nth-child(odd) {
        background-color: unset;
    }
}

::ng-deep .list-dossier-config .frm_main .mat-tab-label {
    height: 48px;
    padding: 0 24px;
    cursor: pointer;
    box-sizing: border-box;
    opacity: .6;
    width: 33.33333%;
    min-width: 0px;
    text-align: center;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    white-space: nowrap;
    position: relative;
}

::ng-deep .list-dossier-config .frm_main .mat-tab-group.mat-primary .mat-ink-bar, .mat-tab-nav-bar.mat-primary .mat-ink-bar {
    background-color: #ce7a58;
}

::ng-deep .list-dossier-config .frm_main .mat-tab-label.mat-tab-label-active:not(.mat-tab-disabled){
  font-weight: 500;
  color: #ce7a58;
  opacity: 1;
}

.mat-tab-dossier-config .btn_add {
    margin-top: 15px;
    margin-bottom: 15px;
}

.agency-config-logo{
    width: 40px;
    height: 40px;
    border-radius: 100%;
    background-position: center;
    background-repeat: no-repeat;
    background-size: 100%;
    float: right;
    // text-align: right !important;
    border: 1px solid #fff;
    -webkit-box-shadow: 0 0 5px rgba(0, 0, 0, .2);
    -moz-box-shadow: 0 0 5px rgba(0, 0, 0, .2);
    box-shadow: 0 0 5px rgba(0, 0, 0, .2);
}
