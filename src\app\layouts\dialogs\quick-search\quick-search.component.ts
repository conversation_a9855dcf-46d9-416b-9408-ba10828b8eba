import { DatePipe } from '@angular/common';
import { Component, Inject, OnInit } from '@angular/core';
import { FormGroup, FormControl } from '@angular/forms';
import { MatDialog, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { MatTableDataSource } from '@angular/material/table';
import { Router } from '@angular/router';
import { EnvService } from 'src/app/core/service/env.service';
import { DossierService } from 'src/app/data/service/dossier/dossier.service';
import { ProcedureService } from 'src/app/data/service/procedure/procedure.service';
import { SnackbarService } from 'src/app/data/service/snackbar/snackbar.service';
import { SuspendComponent, SuspendModel } from 'src/app/modules/dossier/dialogs/suspend/suspend.component';
import { WithdrawComponent, WithdrawModel } from 'src/app/modules/dossier/dialogs/withdraw/withdraw.component';
import { ConfirmDeleteDialogModel, DeleteDossierComponent } from 'src/app/modules/dossier/pages/processing/dialogs/delete-dossier/delete-dossier.component';
import {
  AdditionalRequirementComponent,
  ConfirmAdditionalRequirementDialogModel
} from 'src/app/modules/dossier/dialogs/additional-requirement/additional-requirement.component';
import { ProcessHandleComponent, ProcessHandleDialogModel } from 'src/app/shared/components/process-handle/process-handle.component';
import { DeploymentService } from 'src/app/data/service/deployment.service';
import * as tUtils from 'src/app/data/service/thoai.service';
import {UserService} from "data/service/user.service";
import { CommonService } from 'src/app/data/service/common.service';
import { SigningDeskComponent, SigningDeskDialogModel } from 'src/app/modules/qnm-dac-thu/pages/signing-desk/signing-desk.component';
import { KeycloakService } from 'keycloak-angular';
import * as configSvc from 'src/app/data/service/config.service';

@Component({
  selector: 'app-quick-search',
  templateUrl: './quick-search.component.html',
  styleUrls: ['./quick-search.component.scss']
})
export class QuickSearchComponent implements OnInit {

  config = this.envService.getConfig();
  env = this.deploymentService.getAppDeployment()?.env;
  selectedLang = localStorage.getItem('language') || 'vi';
  selectedLangId = Number(localStorage.getItem('languageId')) || 228;

  // Search Agency
  keyword = '';
  totalPages = 0;
  currentPage = 0;
  pageSize = 10;
  timeOutSearchAgency: any = null;
  listAgency: Array<any> = [];

  // Search
  searchForm = new FormGroup({
    applicant: new FormControl(''),
    code: new FormControl(''),
    identityNumber: new FormControl(''),
    phoneNumber: new FormControl(''),
    agency: new FormControl(''),
    acceptFrom: new FormControl(''),
    acceptTo: new FormControl(''),
    advTaskStatusId: new FormControl(''),
    // acceptFrom: new FormControl(new Date(new Date().getTime() - 7 * 86400000)),
    // acceptTo: new FormControl(new Date())
  });

  listDossierTaskName = [];
  listDossierTaskNamePage = 0;
  isFullListDossierTaskName = false;

  // OS KGG
  isUserRQ = this.env?.OS_KGG?.isUserRQ ? this.env?.OS_KGG?.isUserRQ : false;
  // OS QNI
  isQniQS = false;
  // Table
  displayedColumns: string[] = ['code', 'procedure', 'timing', 'applicant', 'agency', 'status', 'action'];
  ELEMENTDATA: any[] = [];
  dataSource: MatTableDataSource<any>;

  size = 10;
  pageIndex = 1;
  page = 1;
  countResult = 0;
  pgSizeOptions = this.config.pageSizeOptions;
  // Accepter Info
  accepterInfo = {
    id: '',
    username: '',
    fullname: '',
    accountId: ''
  };
  listTimesheet = [];
  receptionForms = [
    {
      id: 0,
      name: [
        {
          languageId: 228,
          name: 'Trực tuyến',
          content: ' tiếp nhận trực tuyến'
        },
        {
          languageId: 46,
          name: 'Online',
          content: ' online reception'
        }
      ]
    },
    {
      id: 1,
      name: [
        {
          languageId: 228,
          name: 'Trực tiếp',
          content: ' tiếp nhận trực tiếp'
        },
        {
          languageId: 46,
          name: 'Direct',
          content: ' direct reception'
        }
      ]
    },
    {
      id: 2,
      name: [
        {
          languageId: 228,
          name: 'Cổng dịch vụ công quốc gia',
          content: ' tiếp nhận từ cổng Dịch vụ công quốc gia'
        },
        {
          languageId: 46,
          name: 'National public service portal',
          content: ' received from the National Public Service portal'
        }
      ]
    }
  ];
  arrReceptionForm: any = [];
  justRegistered: any;

  userAgency = JSON.parse(localStorage.getItem('userAgency'));
  visiableBtnDelete = !this.deploymentService.env.hideDeleteButton;
  isAdmin = false;
  calculateAppointmentDate = this.deploymentService.env?.OS_HCM?.calculateAppointmentDate;
  enableRemoveVnpostFeeToAnotherTable = this.deploymentService.env?.OS_HCM?.enableRemoveVnpostFeeToAnotherTable ? this.deploymentService.env?.OS_HCM?.enableRemoveVnpostFeeToAnotherTable : 0;
  syncPaymentStatus = this.deploymentService.env.syncPaymentStatus;
  paymentRequestId = this.deploymentService.env.paymentRequest.id;
  paidDossierId = this.deploymentService.env.paidDossier.id;
  paidDossierName = this.deploymentService.env.paidDossier.name;
   //QNI
  receivingPermission = false;
  enableWithdrawDossierBtn = this.deploymentService.env.OS_QNI.enableWithdrawDossierBtn;
  qbhmenuaction = this.deploymentService?.env?.OS_QBH?.qbhmenuaction ? this.deploymentService?.env?.OS_QBH?.qbhmenuaction : false;
  qbhEditQuickSearch  = this.deploymentService?.env?.OS_QBH?.qbhEditQuickSearch  ? this.deploymentService?.env?.OS_QBH?.qbhEditQuickSearch  : false;
  // qbhEditQuickSearch = true;

  //HPG
  quickSearchByAgency = this.deploymentService?.env?.OS_HPG?.quickSearchByAgency ? this.deploymentService?.env?.OS_HPG?.quickSearchByAgency : false;
  quickSearchHideWithdrawn = this.deploymentService?.env?.OS_HPG?.quickSearchHideWithdrawn ? this.deploymentService?.env?.OS_HPG?.quickSearchHideWithdrawn : false;

  dossierFeeByDossier = new Map<String, any[]>();

  hideRequestToWithdraw = this.deploymentService.env.OS_HCM.hideRequestToWithdraw.enable &&
                          tUtils.isAllowedAgency(localStorage, this.deploymentService.env.OS_HCM.hideRequestToWithdraw.agencyUsed);

  showSignDesk: boolean = this.deploymentService.env.OS_QNM.showSignDesk;
  //IGATESUPP-54548
  enableRequestAdditionalDossier = this.deploymentService?.env?.OS_QBH?.enableRequestAdditionalDossier ?? false;
  hideCalcutatorDue = this.deploymentService.env?.OS_HCM?.hideCalcutatorDue ? this.deploymentService.env?.OS_HCM?.hideCalcutatorDue : false;
  hideDueProcessInfo = this.deploymentService.getAppDeployment()?.hideDueProcessInfo ? this.deploymentService.getAppDeployment()?.hideDueProcessInfo : false;

  statusNeedsCalculatorTiming = configSvc.STATUS_NEEDS_CALCULATOR_TIMING.map(item => item.id);
  qbhAdditionRequirementTime  = this.deploymentService?.env?.OS_QBH?.qbhAdditionRequirementTime ? this.deploymentService?.env?.OS_QBH?.qbhAdditionRequirementTime : false;
  checkNullData =0;
  constructor(
    public dialogRef: MatDialogRef<QuickSearchComponent>,
    @Inject(MAT_DIALOG_DATA) public data: QuickSearchDialogModel,
    private envService: EnvService,
    private deploymentService: DeploymentService,
    private dialog: MatDialog,
    private router: Router,
    private datePipe: DatePipe,
    private snackbarService: SnackbarService,
    private dossierService: DossierService,
    private procedureService: ProcedureService,
    private userService: UserService,
    private commonService: CommonService,
    private keycloakService: KeycloakService,
  ) {
    this.dataSource = new MatTableDataSource(this.ELEMENTDATA);
    this.searchForm.patchValue({
      code: data.code
    });
  }

  async ngOnInit(): Promise<void> {
    if(this.env?.OS_QNI?.qni_advancedsearch === true){
      this.isQniQS = this.env?.OS_QNI?.isQniQS;
    }
    for await (const receptionForm of this.receptionForms) {
      const arrName: any = {};
      for await (const name of receptionForm.name) {
        if (this.selectedLangId === name.languageId) {
          arrName.id = receptionForm.id;
          arrName.name = name.name;
          arrName.content = name.content;
        }
      }
      this.arrReceptionForm.push(arrName);
    }

    this.getListAgency('', this.pageIndex - 1, this.size);
    this.getListDossierTaskName();
    await this.getDossierTaskStatus();
    this.getListDossier(this.pageIndex - 1, this.size);
    this.getAccepterInfo();
    const permissions = this.userService.getUserPermissions();
    for (const p of permissions) {
      if (p.permission.code === 'admin' || p.permission.code === 'oneGateAdminMaster') {
        this.isAdmin = true;
      }
      if (p.permission.code === 'oneGateDossierAccepter' || p.permission.code === 'oneGateDossierReception' || p.permission.code === 'oneGateDossierOnlineReception') {
        this.receivingPermission = true;
        break;
      }
    }
  }

  getListDossierTaskName() {
    if (this.isFullListDossierTaskName) { return; }
    this.dossierService.getListTagByCategoryId(this.env?.dossierTaskNameCategoryId?.id, this.listDossierTaskNamePage).subscribe(data => {
      if (data.content.length === 0) { return; }
      this.listDossierTaskName = this.listDossierTaskNamePage === 0 ? data.content : this.listDossierTaskName.concat(data.content);
      this.isFullListDossierTaskName = data.last;
      this.listDossierTaskNamePage++;
    });
  }

  onDismiss() {
    this.dialogRef.close();
  }

  // Search
  findDossier() {
    this.pageIndex = 1;
    this.page = 1;

    this.getListDossier(this.pageIndex - 1, this.size);
  }

  // Paginate
  paginate(event: any, type) {
    switch (type) {
      case 0:
        this.pageIndex = event;
        this.page = event;
        this.getListDossier(this.pageIndex - 1, this.size);
        break;
      case 1:
        this.pageIndex = 1;
        this.page = 1;
        this.getListDossier(this.pageIndex - 1, this.size);
        break;
    }
  }

  check(dossier){
    if(this.env.OS_HCM?.isNotReceivingAgency == true){
      if (dossier.currentTask !== undefined && dossier.currentTask.length > 0){
        if (!!dossier.currentTask[0].agency){
          // tslint:disable-next-line:max-line-length
          return dossier.currentTask[0].agency.name.find(v => v.languageId === this.selectedLangId).name;
        }
      }else if(dossier.previousTask !== undefined && dossier.previousTask.length > 0){
        if (!!dossier.previousTask[0].agency){
          // tslint:disable-next-line:max-line-length
          return dossier.previousTask[0].agency.name.find(v => v.languageId === this.selectedLangId).name;
        }
      }
    }else{
      return dossier.agency.name
    }
  }

  // Search Agency
  getNextBatch() {
    this.currentPage += 1;
    this.getListAgency(this.keyword, this.currentPage, this.pageSize);
  }

  onEnter(event) {
    clearTimeout(this.timeOutSearchAgency);
    this.timeOutSearchAgency = setTimeout(async () => {
      this.keyword = event.target.value;
      this.currentPage = 0;
      this.getListAgency(this.keyword, this.currentPage, this.pageSize);
    }, 300);
  }

  async resetSearchForm() {
    this.currentPage = 0;
    this.keyword = '';
    this.getListAgency(this.keyword, this.currentPage, this.pageSize);

  }

  // Get Function
  getListAgency(keyword, page, size) {
    // const userAgency = JSON.parse(localStorage.getItem('userAgency'));
    // let rootAgencyId: any = '';
    // if (userAgency !== null) {
    //   rootAgencyId = userAgency.id;
    // } else {
    //   rootAgencyId = this.config.rootAgency.id;
    // }

    let searchString = '?keyword=' + keyword.trim() + '&page=' + page + '&size=' + size + '&spec=page&sort=name.name,asc&status=1';

    if (this.quickSearchByAgency) {
      searchString += '&parent-id=' + this.userAgency.id;
    } else {
      searchString += '&parent-id=' + this.config.rootAgency.id;
    }

    this.procedureService.getListAgencyWithParent(searchString).subscribe(res => {
      if (page === 0) {
        this.listAgency = res.content;
      } else {
        this.listAgency = this.listAgency.concat(res.content);
      }
      this.totalPages = res.totalPages;
    }, err => {
      console.log(err);
    });
  }

  getDossierTaskStatus() {
    return new Promise<void>(resolve => {
      this.dossierService.getAgencyTag(this.deploymentService.env.dossierTaskStatus?.justRegistered?.id).subscribe(data => {
        this.justRegistered = data;
        resolve();
      }, err => {
        resolve();
      });
    });
  }

  getListDossier(page, size) {
    const formObj = this.searchForm.getRawValue();
    let extendSearch = '';
    if (this.env?.OS_KTM?.isKtm || this.quickSearchByAgency) {
      let userAgencyId: any = '';
      if (this.userAgency !== null) {
        userAgencyId = this.userAgency.id;
        if (!!this.userAgency.parent) {
          userAgencyId = this.userAgency.parent.id;
        }
      }

      if (!!formObj.agency) {
        extendSearch = '&ancestor-agency-id=' + formObj.agency
          + '&task-ancestor-agency-id=' + formObj.agency;
      } else {
        extendSearch = '&ancestor-agency-id=' + userAgencyId
          + '&task-ancestor-agency-id=' + userAgencyId;
      }
    } else {
      extendSearch = '&current-task-agency-id=' + formObj.agency;
    }

    const keyword = '&applicant-name=' + formObj.applicant.trim() + '&code=' + formObj.code.trim()
      + '&identity-number=' + formObj.identityNumber.trim() + '&phone-number=' + formObj.phoneNumber.trim()
      + '&task-status-id=' + formObj.advTaskStatusId
      + '&accepted-from=' + (formObj.acceptFrom ? this.datePipe.transform(formObj.acceptFrom, 'dd/MM/yyyy') : '')
      + '&accepted-to=' + (formObj.acceptTo ? this.datePipe.transform(formObj.acceptTo, 'dd/MM/yyyy') : '')
      + extendSearch;
    const searchString = 'search?page=' + page + '&size=' + size + '&spec=page&sort=createdDate,desc' + keyword;

    this.dossierService.getListDossier(searchString).subscribe(async data => {
      if (this.syncPaymentStatus) {
        this.dossierFeeByDossier.clear();
        const ids = data.content.map(item => item.id);
        const dossierFee = await this.commonService.getProcostDossiers(ids);
        dossierFee.forEach(item => {
          if (!!!this.dossierFeeByDossier.get(item.dossier.id)) {
            this.dossierFeeByDossier.set(item.dossier.id, [])
          }
          this.dossierFeeByDossier.set(item.dossier.id, [item, ...this.dossierFeeByDossier.get(item.dossier.id)])
        })
      }
      this.ELEMENTDATA = [];
      this.listTimesheet = [];
      if (data.content.length > 0) {
        this.checkNullData = 0;
      } else {
        this.checkNullData = 1;
      }
      this.countResult = data.totalElements;
      for (let i = 0; i < data.numberOfElements; i++) {
        let requireAdditional = false;
        data.content[i].stt = this.size * (this.pageIndex - 1) + (i + 1);
        // tslint:disable-next-line:prefer-for-of
        for (let m = 0; m < this.arrReceptionForm.length; m++) {
          if (data.content[i].applyMethod.id === this.arrReceptionForm[m].id) {
            data.content[i].codeText = data.content[i].code + this.arrReceptionForm[m].content;
          }
        }
        data.content[i].due = [];
        if (data.content[i].acceptedDate !== undefined) {
          let processingTime = 0;
          let dateDiffPayment = 0;
          let processingTimeWithUnit = data.content[i].processingTime;
          if (this.calculateAppointmentDate == 1 && data.content[i]?.paymentRequestData?.dateDiff !== null && data.content[i]?.paymentRequestData?.dateDiff !== undefined) {
              dateDiffPayment = data.content[i].paymentRequestData.dateDiff;
              processingTimeWithUnit += dateDiffPayment;
          }
          switch (data.content[i].processingTimeUnit) {
            case 'y':
              processingTime = processingTimeWithUnit * 365;
              break;
            case 'm':
              processingTime = processingTimeWithUnit * 30;
              break;
            case 'd':
              processingTime = processingTimeWithUnit;
              break;
            case 'H:m:s':
              processingTime = processingTimeWithUnit / 24;
              break;
            case 'h':
              processingTime = Number(processingTimeWithUnit) / 24;
              break;
            case 'm':
              processingTime = Number(processingTimeWithUnit) / (24 * 60);
              break;
          }

          if (this.env.limitedAppointmentTime) {
            this.listTimesheet.push({
              timesheet: {
                // id: this.dossierDetail[0].procedureProcessDefinition.processDefinition.timesheet.id
                // tslint:disable-next-line: max-line-length
                id: data.content[i].procedureProcessDefinition?.processDefinition?.timesheet !== undefined ? data.content[i].procedureProcessDefinition.processDefinition.timesheet.id : this.config.defaultTimesheetId
              },
              dossier: {
                id: data.content[i].id
              },
              duration: this.deploymentService.env.timesheetV2 ? (data.content[i].processingTime + dateDiffPayment) : processingTime,
              startDate: data.content[i].acceptedDate,
              // endDate: dossierEndDate,
              endDate: '',
              checkOffDay: true,
              offTime: this.env.limitedAppointmentTime,
              processingTimeUnit: data.content[i].processingTimeUnit
            });
          } else if (this.deploymentService.env.OS_HCM?.limitedAppointmentTimeOnProcedure) {
            this.listTimesheet.push({
              timesheet: {
                // id: this.dossierDetail[0].procedureProcessDefinition.processDefinition.timesheet.id
                // tslint:disable-next-line: max-line-length
                id: data.content[i].procedureProcessDefinition?.processDefinition?.timesheet !== undefined ? data.content[i].procedureProcessDefinition.processDefinition.timesheet.id : this.config.defaultTimesheetId
              },
              dossier: {
                id: data.content[i].id
              },
              duration: this.deploymentService.env.timesheetV2 ? (data.content[i].processingTime + dateDiffPayment) : processingTime,
              startDate: data.content[i].acceptedDate,
              // endDate: dossierEndDate,
              endDate: '',
              checkOffDay: true,
              extendHCM: {
                offTime: this.deploymentService.env.OS_HCM?.limitedAppointmentTimeOnProcedure,
                startAtNextDay: data.content[i]?.extendHCM?.startAtNextDay ?? false,
                appointmentAtNextDay: data.content[i]?.extendHCM?.appointmentAtNextDay ?? false
              },
              processingTimeUnit: data.content[i].processingTimeUnit
            });
          } else {
            this.listTimesheet.push({
              timesheet: {
                // tslint:disable-next-line:max-line-length
                id: data.content[i].procedureProcessDefinition?.processDefinition?.timesheet !== undefined ? data.content[i].procedureProcessDefinition.processDefinition.timesheet.id : this.config.defaultTimesheetId
              },
              dossier: {
                id: data.content[i].id
              },
              duration: this.deploymentService.env.timesheetV2 ? (data.content[i].processingTime + dateDiffPayment) : processingTime,
              startDate: data.content[i].acceptedDate,
              endDate: '',
              checkOffDay: true,
              processingTimeUnit: data.content[i].processingTimeUnit
            });
          }
          data.content[i].dossierEndDate = null;

        }
        // dossier task status
        if (data.content[i].dossierTaskStatus !== undefined && data.content[i].dossierTaskStatus !== null) {
          data.content[i].dossierStatus.name = data.content[i].dossierTaskStatus.name;
          // IGATESUPP-25994
          if ((data.content[i].applyMethod.id === 0 &&
            data.content[i].dossierTaskStatus.id === this.deploymentService.env.dossierTaskStatus?.justRegistered?.id) ||
            (data.content[i].applyMethod.id === 1 &&
            !(data.content[i].dossierTaskStatus.id === this.deploymentService.env.dossierTaskStatus?.waitingForAcceptance?.id ||
            data.content[i].dossierTaskStatus.id === this.deploymentService.env.dossierTaskStatus?.resultReturned?.id))) {
            requireAdditional = true;
          }
          if (this.syncPaymentStatus) {
              if (data.content[i].dossierTaskStatus?.id == this.paymentRequestId) {
                  let isPaid = true;
                  if (this.enableRemoveVnpostFeeToAnotherTable == 1) {
                      let vnpostFee: any = null;
                      vnpostFee = await this.getVnpostFeeDossier(data.content[i].id);
                      if (vnpostFee != null) {
                          vnpostFee.forEach(element => {
                              if (element.paid != (element.quantity * element.amount)) {
                                  isPaid = false;
                              }
                          });
                      }
                      // const dossierFee = await this.getProcostDossier(data.content[i].id);
                      const dossierFee = this.dossierFeeByDossier.get(data.content[i].id);
                      dossierFee.forEach(element => {
                          if (element.paid != (element.quantity * element.amount)) {
                              isPaid = false;
                          }
                      });
                  } else {
                      // const dossierFee = await this.getProcostDossier(data.content[i].id);
                      const dossierFee = this.dossierFeeByDossier.get(data.content[i].id);
                      dossierFee.forEach(element => {
                          if (element.paid != (element.quantity * element.amount)) {
                              isPaid = false;
                          }
                      });
                  }
                  if (isPaid) {
                      data.content[i].dossierStatus.id = this.paidDossierId;
                      data.content[i].dossierStatus.name = this.paidDossierName;
                  }
              }
          }
        } else {
          // tslint:disable-next-line: no-string-literal
          if (data.content[i].dossierStatus.id === 0 && this.justRegistered && this.justRegistered['trans']) {
            // tslint:disable-next-line:max-line-length tslint:disable-next-line: no-string-literal
            data.content[i].dossierStatus.name = this.justRegistered['trans'].filter(res => Number(res.languageId) === Number(localStorage.getItem('languageId')))[0].name;
          } else {
            if (data.content[i].currentTask !== undefined && data.content[i].currentTask.length !== 0) {
              // tslint:disable-next-line: no-string-literal
              data.content[i].dossierStatus.name = data.content[i].currentTask[0].bpmProcessDefinitionTask.name['name'];
            }
          }
        }
        if(this.qbhEditQuickSearch)
        {
          if (data?.content[i]?.task && data?.content[i]?.task.length > 0) {
            for (var j = 0; j < data.content[i].task.length; j++) {
              if(data.content[i].task[j]?.assignee?.id != null && (data.content[i].task[j].assignee.account.id === this.accepterInfo.accountId))
              {
                data.content[i].checkUser = true;
                break;
              }
              else if (data.content[i].task[j].agency.parent.id === this.userAgency.parent.id )
              {
                let userExperience = JSON.parse(localStorage.getItem('userExperienceAgency'));
                if(userExperience.position.id === data.content[i].task[j]?.candidatePosition?.id)
                {
                  data.content[i].checkUser = true;
                  break;
                }
              }
            }
          }
        }
        data.content[i].requireAdditional = requireAdditional;

        if(this.qbhAdditionRequirementTime){
          const statusMakeColor  = [...this.statusNeedsCalculatorTiming, 8, 9, 10, 11];
          if(statusMakeColor.includes(data.content[i].dossierStatus?.id)){
            data.content[i].qbhShowOverDueTime = true;
          }
          else{
            data.content[i].qbhShowOverDueTime = false;
          }
        }

        this.ELEMENTDATA.push(data.content[i]);
        if(this.qbhmenuaction == true)
        {
          this.ELEMENTDATA.forEach(id => {
          for (var i = 0; i < this.ELEMENTDATA.length; i++) {
            if (this.ELEMENTDATA[i].dossierTaskStatus.id === "61ee30eada2d36b037e00003" ||this.ELEMENTDATA[i].dossierStatus.id == 10 ) {
              this.ELEMENTDATA[i].dossierTaskStatus.name = "Xác nhận xin lỗi và xin gia hạn";
              this.ELEMENTDATA[i].dossierStatus.name = "Xác nhận xin lỗi và xin gia hạn";
            }
            if (this.ELEMENTDATA[i].dossierTaskStatus.id === "61ee30eada2d36b037e00004" ||this.ELEMENTDATA[i].dossierStatus.id == 11 ) {
              this.ELEMENTDATA[i].dossierTaskStatus.name = "Xác nhận từ chối giải quyết";
              this.ELEMENTDATA[i].dossierStatus.name = "Xác nhận từ chối giải quyết";
            }
            if (this.ELEMENTDATA[i].dossierTaskStatus.id === "61ee30eada2d36b037e00003" || this.ELEMENTDATA[i].dossierStatus.id == 10) {
              this.ELEMENTDATA[i].dossierTaskStatus.name = "Xác nhận xin lỗi và xin gia hạn";
              this.ELEMENTDATA[i].dossierStatus.name = "Xác nhận xin lỗi và xin gia hạn";
            }
            if (this.ELEMENTDATA[i].dossierTaskStatus.id === "61ee30eada2d36b037e00001" ||this.ELEMENTDATA[i].dossierStatus.id == 8 ) {
              this.ELEMENTDATA[i].dossierTaskStatus.name = "Xác nhận yêu cầu bổ sung";
              this.ELEMENTDATA[i].dossierStatus.name = "Xác nhận yêu cầu bổ sung";
            }
            if (this.ELEMENTDATA[i].dossierTaskStatus.id === "6151c771ba2a04299f949875" && this.ELEMENTDATA[i].dossierStatus.id == 6 ) {
              this.ELEMENTDATA[i].dossierTaskStatus.name = "Xác nhận rút";
              this.ELEMENTDATA[i].dossierStatus.name = "Xác nhận rút";
            }
           }
          })
        }

      }
      this.dataSource.data = this.ELEMENTDATA;
      console.log('data',data);
      console.log('userAgency',this.userAgency);
      console.log('userid', this.accepterInfo);
      this.postTimesheet();
    });
  }

  async getProcostDossier(dossierId) {
    return new Promise<Array<any>>(resolve => {
      this.dossierService.getDossierFee(dossierId).subscribe(data => {
        resolve(data);
      });
    });
  }

  async getVnpostFeeDossier(dossierId) {
    return new Promise<Array<any>>(resolve => {
      this.dossierService.getVnpostFee(dossierId).subscribe(data => {
        resolve(data);
      });
    });
  }

  postTimesheet() {
    const requestBody = JSON.stringify(this.listTimesheet, null, 2);
    this.dossierService.postTimesheet(requestBody).subscribe(data => {
      const newDate = tUtils.newDate();
      let i = 0;
      data.forEach(dt => {
        dt.timesheet = {
          isOverDue: false,
          timer: {
            day: 0,
            hour: 0,
            minute: 0,
            second: 0
          }
        };

        let time = '';
        let dateDue = dt.due;
        if (!!this.listTimesheet[i] && !!this.listTimesheet[i].appointmentDate) {
          dateDue = this.listTimesheet[i].appointmentDate;
        }
        if (new Date(dateDue).getTime() < newDate.getTime()) {
          dt.timesheet.isOverDue = true;
          time = ((newDate.getTime() - new Date(dateDue).getTime()) / 1000).toString();
        } else {
          time = ((new Date(dateDue).getTime() - newDate.getTime()) / 1000).toString();
        }

        // let seconds = time;
        let seconds = parseInt(time, 10);
        const days = Math.floor(seconds / (3600 * 24));
        seconds -= days * 3600 * 24;
        const hrs = Math.floor(seconds / 3600);
        seconds -= hrs * 3600;
        const mnts = Math.floor(seconds / 60);
        seconds -= mnts * 60;

        dt.timesheet.timer = {
          day: days,
          hour: hrs,
          minute: mnts,
          second: seconds
        };

        this.ELEMENTDATA.forEach(elem => {
          if (elem.id === dt.dossier.id) {
            elem.due.push(dt);
          }
        });
      });

      data.forEach(dt => {
        this.ELEMENTDATA.forEach(elem => {
          if (elem.id === dt.dossier.id) {
            elem.dossierEndDate = new Date(dt.due);
            if (elem.appointmentDate == null) {
              elem.appointmentDate = new Date(dt.due);
            }
          }
        });
      });

      this.dataSource.data = this.ELEMENTDATA;
    });
  }

  getStatusColor(type) {
    switch (type) {
      case 0: return '#000000de';
      case 1: return '#FF9800';
      case 2: return '#f39c12';
      case 3: return '#FF9800';
      case 4: return '#03A9F4';
      case 5: return '#03A9F4';
      case 6: return '#DE1212';
      default: return '#000000de';
    }
  }

  getTranslateName(translate: any[]) {
    return new Promise<any>(resolve => {
      let i = translate.length;
      while (i--) {
        if (!!translate[i] && !!translate[i].languageId && translate[i].languageId === this.selectedLangId) {
          resolve(translate[i].name);
        }
      }
      resolve('');
    });
  }

  // Action
  viewProcess(dossierId, dossierCode) {
    const dialogData = new ProcessHandleDialogModel(dossierId, dossierCode);
    const dialogRef = this.dialog.open(ProcessHandleComponent, {
      minWidth: '70vw',
      data: dialogData,
      disableClose: true,
      autoFocus: false
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
    });
  }

  dossierDetail(dossierId, procedureId, task) {
    const queryParamsObject = {
      procedure: procedureId,
    };

    if (task !== undefined) {
      Object.assign(queryParamsObject, { task: task[task.length - 1].id });
    }

    const url = this.router.serializeUrl(
      this.router.createUrlTree(['dossier/search/' + dossierId], {
        queryParams: queryParamsObject
      })
    );

    window.open(url, '_blank');
  }

  deleteDialog(dossierId, dossierCode) {
    if (!dossierCode) {
      dossierCode = '';
    }
    const dialogData = new ConfirmDeleteDialogModel(dossierId, dossierCode);
    const dialogRef = this.dialog.open(DeleteDossierComponent, {
      width: '500px',
      data: dialogData,
      disableClose: true,
      autoFocus: false
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
      if (dialogResult === 0) {
        const msgObj = {
          vi: 'Đã xoá hồ sơ!',
          en: 'Dossier deleted!'
        };
        this.snackbarService.openSnackBar(1, msgObj[this.selectedLang], '', 'success_notification', this.config.expiredTime);
        this.findDossier();
      }

      if (dialogResult === 1) {
        const msgObj = {
          vi: 'Đã hủy hồ sơ!',
          en: 'Dossier cancelled!'
        };
        this.snackbarService.openSnackBar(1, msgObj[this.selectedLang], '', 'success_notification', this.config.expiredTime);
        this.findDossier();
      }

      if (dialogResult === null) {
        const msgObj = {
          vi: 'Xoá không thành công!',
          en: 'Deletion failed!'
        };
        this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
      }
      // this.adminLayoutNavComponent.getDossierRemind();
    });
  }

  additionalRequirement(dossierId, dossierCode) {
    const dialogData = new ConfirmAdditionalRequirementDialogModel(dossierId, dossierCode, 2);
    const dialogRef = this.dialog.open(AdditionalRequirementComponent, {
      minWidth: '50vw',
      maxHeight: "85vh",
      data: dialogData,
      disableClose: true,
      autoFocus: false
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
      if (dialogResult === true) {
        const msgObj = {
          vi: 'Đã yêu cầu bổ sung!',
          en: 'Additional request sent!'
        };
        this.snackbarService.openSnackBar(1, msgObj[this.selectedLang], '', 'success_notification', this.config.expiredTime);
        this.findDossier();
      }
      if (dialogResult === false) {
        const msgObj = {
          vi: 'Yêu cầu bổ sung không thành công!',
          en: 'Additional request failed!'
        };
        this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
      }
    });
  }

  onKeyReset() {
    this.  searchForm = new FormGroup({
      applicant: new FormControl(''),
      code: new FormControl(''),
      identityNumber: new FormControl(''),
      phoneNumber: new FormControl(''),
      agency: new FormControl(''),
      acceptFrom: new FormControl(''),
      acceptTo: new FormControl(''),
      advTaskStatusId: new FormControl(''),
      // acceptFrom: new FormControl(new Date(new Date().getTime() - 7 * 86400000)),
      // acceptTo: new FormControl(new Date())
    });
    this.findDossier();
  }

  suspenDialog(dossierId, dossierCode) {
    if (!dossierCode) {
      dossierCode = '';
    }
    const dialogData = new SuspendModel(dossierId, dossierCode);
    const dialogRef = this.dialog.open(SuspendComponent, {
      minWidth: '50vw',
      data: dialogData,
      disableClose: true,
      autoFocus: false
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
      if (dialogResult === true) {
        const msgObj = {
          vi: 'Đã tạm dừng hồ sơ!',
          en: 'Dossier has been suspend!'
        };
        this.snackbarService.openSnackBar(1, msgObj[this.selectedLang], '', 'success_notification', this.config.expiredTime);
        this.findDossier();
      }

      if (dialogResult === false) {
        const msgObj = {
          vi: 'Tạm dừng hồ sơ không thành công!',
          en: 'Suspend failed!'
        };
        this.snackbarService.openSnackBar(1, msgObj[this.selectedLang], '', 'success_notification', this.config.expiredTime);
      }
    });
  }

  withdrawDialog(dossierId, dossierCode) {
    if (!dossierCode) {
      dossierCode = '';
    }
    const dialogData = new WithdrawModel(dossierId, dossierCode);
    const dialogRef = this.dialog.open(WithdrawComponent, {
      minWidth: '45vw',
      data: dialogData,
      disableClose: true,
      autoFocus: false
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
      if (dialogResult === true) {
        const msgObj = {
          vi: 'Đã rút hồ sơ!',
          en: 'Dossier has been withdraw!'
        };
        this.snackbarService.openSnackBar(1, msgObj[this.selectedLang], '', 'success_notification', this.config.expiredTime);
        // this.adminLayoutNavComponent.getDossierRemind();
        this.findDossier();
      }
      if (dialogResult === false) {
        const msgObj = {
          vi: 'Rút hồ sơ không thành công!',
          en: 'Withdraw dossier failed!'
        };
        this.snackbarService.openSnackBar(1, msgObj[this.selectedLang], '', 'success_notification', this.config.expiredTime);
      }
    });
  }
  signDeskQNM(id){
    const dialogData = new SigningDeskDialogModel(id,1);
    const dialogRef = this.dialog.open(SigningDeskComponent, {
      minWidth: '55vw',
      maxHeight: '90vh',
      data: dialogData,
      autoFocus: false
      // autoFocus: false
    });
    dialogRef.afterClosed().subscribe( async rs => {
    if(rs)
    this.snackbarService.openSnackBar(1, "Ký thành công", '', 'success_notification', this.config.expiredTime);
    else
    this.snackbarService.openSnackBar(0, "Ký thất bại", '', 'error_notification', this.config.expiredTime);
  })
  }
  getAccepterInfo() {
    this.keycloakService.loadUserProfile().then(user => {
      this.accepterInfo.username = user.username;
      // tslint:disable-next-line: no-string-literal
      this.accepterInfo.id = user['attributes'].user_id[0];
      // tslint:disable-next-line: no-string-literal
      this.accepterInfo.accountId = user['attributes'].account_id[0];
    });
  }
}

export class QuickSearchDialogModel {
  constructor(public code: string) { }
}
