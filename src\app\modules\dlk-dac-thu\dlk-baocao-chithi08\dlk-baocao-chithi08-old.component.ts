import { Component, OnInit, ChangeDetectorRef, AfterViewInit, ViewChild, OnDestroy } from '@angular/core';
import { FormGroup, FormControl } from '@angular/forms';
import { Router } from '@angular/router';
import { EnvService } from 'src/app/core/service/env.service';
import { MatTableDataSource } from '@angular/material/table';
import { DatePipe } from '@angular/common';
import { ExportExcelService } from 'src/app/data/service/export-excel/export-excel.service';
import { MainService } from 'src/app/data/service/main/main.service';
import { ProcedureService } from 'src/app/data/service/procedure/procedure.service';
import { takeUntil } from 'rxjs/operators';
import { ReplaySubject, Subject } from 'rxjs';
import { ActivatedRoute } from '@angular/router';
import { DossierService } from 'src/app/data/service/dossier/dossier.service';
import { NgxPrinterService } from 'ngx-printer';
import { SnackbarService } from 'src/app/data/service/snackbar/snackbar.service';
import { MatSelect } from '@angular/material/select';
import { ReportService } from 'data/service/report/report.service';
import { DeploymentService } from 'data/service/deployment.service';
import * as tUtils from 'src/app/data/service/thoai.service';
import { CookieService } from 'ngx-cookie-service';
import { DLKStatisticsService } from 'src/app/data/service/dlk-statistics/dlk-statistics.service';
import {StatisticsService} from 'src/app/data/service/statistics/statistics.service';
import { Workbook } from 'exceljs';
import * as fs from 'file-saver';
export interface Agency {
  id: string;
  name: string;
}
import {
  DossierDetailComponent,
  DossierDetailDialogModel,
  procedureDetailDialogModel
} from '../dialogs/view-detail.component';
import {MatDialog} from '@angular/material/dialog';
@Component({
  selector: 'app-dlk-baocao-chithi08',
  templateUrl: './dlk-baocao-chithi08-old.component.html',
  styleUrls: [
    './dlk-baocao-chithi08.component.scss',
    '/src/app/app.component.scss',
    '/src/app/shared/scss/form-field-outline.scss'
  ]
})
export class DlkBaoCaoChiThi08OldComponent implements OnInit, AfterViewInit, OnDestroy {

  nowDate = tUtils.newDate();

  // tslint:disable-next-line: max-line-length
  toDate = this.nowDate.getFullYear() + '-' + ((this.nowDate.getMonth() + 1) <= 9 ? '0' + (this.nowDate.getMonth() + 1) : (this.nowDate.getMonth() + 1)) + '-' + ((this.nowDate.getDate() <= 9) ? ('0' + this.nowDate.getDate()) : this.nowDate.getDate());
  // tslint:disable-next-line: max-line-length
  fromDate = this.nowDate.getFullYear() + '-' + ((this.nowDate.getMonth() + 1) <= 9 ? '0' + (this.nowDate.getMonth() + 1) : (this.nowDate.getMonth() + 1)) + '-01';

  keyword = '';
  config = this.envService.getConfig();
  
  selectedLang = localStorage.getItem('language') || 'vi';
  selectedLangId = Number(localStorage.getItem('languageId')) || 228;
  pageTitle = {
    vi: 'Thống kê sổ theo dõi',
    en: 'Logbook statistics'
  };

  protected onDestroy = new Subject<void>();
  // =================== old
  env: any = this.deploymentService.getAppDeployment()?.env;
  ELEMENTDATA: any[] = [];
  dataSource: MatTableDataSource<any>;
  // listSector: Array<any> = [];
  keySearchSectorAgency = '';
  // Search Sector
  keywordSector = '';
  totalPagesSector = 0;
  currentPageSector = 0;
  pageSizeSector = 100;
  searchString="";

  parentAgency = '';
  agencyId = '';
  userAgency?: any = JSON.parse(localStorage.getItem('userAgency'));
  userAgencyCount?: any = JSON.parse(localStorage.getItem('listAgencyUser'))?.length || 0;
  ELEMENTDATAALL: any[] = [];
  dataSourceAll: MatTableDataSource<any>;

  listHoSoCapTinh = [];
  listHoSoCapHuyen = [];
  TongCapTinh :any;
  TongCapHuyen :any;
  TongCapTinhLK :any;
  TongCapHuyenLK :any;
  listHoSo = [];
  listThuTucHoSo = [];
  listHoSoLK = [];
  listThuTucHoSoLK = [];
  procedureAgencyLevel = this.deploymentService.env.statistics.procedureAgencyLevel;
  Agency= this.deploymentService.env.OS_DLK;
  size = 10;
  pageIndex = 1;
  page = 1;
  countResult = 0;
  pgSizeOptions = this.config.pageSizeOptions;
  isAdmin?: boolean = sessionStorage.getItem('oneGateAdminMaster') === '1';
  paramsQuery = {
    page: 0,
    size: 10,
    fromDate: '',
    toDate: '',
    fromLKDate:'',
    toLKDate:'',
    agency: '',
    applyMethod: '',
    receivingKind: '',
    sector: '',
    keyword: '',
    procedure: '',
    sortId: '',
    isTTPVHCC: 1,
    agencyId:null,
  };
  paramsDossier= {
    page: 0,
    size: 10,
    fromDate: null,
    toDate:null,
    agencyId:null,
    applyMethodId:null,
    receivingKind: null,
    hinhThucNop: null,
    keyword: '',
    dossierStatusId:null,
    procedureLevelId:null,
    code: ''
  };
  listAgencyAccept = [];
  listAgency = [];
  keywordAgencyAccept = '';
  totalPagesAgencyAccept = 0;
  currentPageAgencyAccept = 0;
  pageSizeAgencyAccept = 50;
  startDateCumulative = new Date();
  endDateCumulative = new Date();
  startDate = new Date();
  endDate = new Date();
  flagTTPVHCC = true;
  timeOutAgencyAccept: any = null;
  listSectorfillter: any[] = [];
  //listSector: any[] = [];
  listProcedure: any[] = [];
  listProcedurefillter: any[] = [];
  totalPagesProcedure = 0;
  currentPageProcedure = 0;
  searchProcedureKeyword = "";
  listHinhThucNhan: any[] = [];
  EXCEL_EXTENSION = '.xlsx';
  EXCEL_TYPE = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8';
  @ViewChild('procedureMatSelectInfiniteScroll', { static: true }) procedureMatSelectInfiniteScroll: MatSelect;


  constructor(
    private envService: EnvService,
    public datepipe: DatePipe,
    private mainService: MainService,
    private cdRef: ChangeDetectorRef,
    private procedureService: ProcedureService,
    private datePipe: DatePipe,
    private deploymentService: DeploymentService,
    private dlkStatisticService: DLKStatisticsService,
    private statisticsService: StatisticsService,
    private reportService: ReportService,
    private dialog: MatDialog
  ) {
    this.dataSource = new MatTableDataSource(this.ELEMENTDATA);
    this.dataSourceAll = new MatTableDataSource(this.ELEMENTDATAALL);
  }
  ngOnInit(): void {
    this.mainService.setPageTitle(this.pageTitle[localStorage.getItem('language')]);
    this.startDate = new Date(this.fromDate);
    this.startDateCumulative = new Date(this.fromDate);
    this.endDate = new Date(this.toDate);
    if (this.userAgency !== null && this.userAgency !== undefined) {
      if (!!this.userAgency.parent && !!this.userAgency.parent.id ) {
        this.parentAgency = this.userAgency.parent.id;
        this.agencyId = this.userAgency.id;
      } else {
        this.parentAgency = this.userAgency.id;
        this.agencyId = this.userAgency.id;
      }
      this.parentAgency =this.Agency.rootAgencyId; // dlk
      this.keySearchSectorAgency = '&agency-id=' + this.agencyId;
       this.searchString  = "?arr-parent-id=" + this.parentAgency;

       this.getDataBaocao();
    }

  }

  getDataBaocao() {
    this.dlkStatisticService.getDataBaocao(this.parentAgency).subscribe(
      (data) => {
        console.log(data);
        
      }
    );
  }


  ngAfterViewInit() {
    this.cdRef.detectChanges();  
    this.getListAgencyAccept(this.Agency.rootAgencyId,'',0,10000);
   // this.getProcedureByAgencyIdDLK()
  }

  ngOnDestroy() {
    this.onDestroy.next();
    this.onDestroy.complete();
  }
colToLetter(number){
  let result = '';
  // number = number - 1; // If starting from 1
  do {
    const letter = String.fromCharCode(65 + (number % 26));
    result = letter + result;
    number = Math.floor(number / 26) - 1;
  } while (number >= 0)
  return result;
}
  thongKe() {
    this.waitingDownloadExcel = true;
    this.paramsQuery.page = 0;
    this.page = 1;
    this.getListHoSo();

    setTimeout(() => {  this.BuilData();

    }, 500);
  }
  paginate(event) {
    this.paramsQuery.page = event;
   // this.getListHoSo();
  }

  getParentId(): string | null {

    const userAgency = JSON.parse(localStorage.getItem('userAgency'));

    if (userAgency){
      if (!!userAgency.parent && !!userAgency.parent.id && !this.isAdmin) {
        return userAgency.parent.id;
      }
      else if (userAgency.id !== this.config.rootAgency.id || this.isAdmin) {
        return userAgency.id;
      }
    }
    return null;
  }
  //"60c868a4289bad69c7cbffea" tỉnh kontum
//   GetItemXL(Id) {
//     let promise = new Promise((resolve, reject) => {
//         this.service.GetItemDTXuLy(Id)
//             .subscribe(
//             (data) => {
//                 this.itemdt = data;
//                 if(this.itemdt.length>0)
//                 {
//                     this.GetItemXuLy(this.itemdt[0]);
//                     this.duLieuPuLic.IdDonThu = 0;
//                 }
//                 resolve(6);
//             },
//             er => {
//                 if (er.status == 401) {
//                     this.router.navigate(['./login']);
//                     this.duLieuPuLic.IdDonThu = 0;
//                 }
//                 reject(6);
//             });
//     });
//     return promise;
// }
   getListHoSo() {
          let promise = new Promise((resolve, reject) => {
           
            this.getTotalProcedureByALLAgency();
                  this.paramsQuery.fromDate = (this.startDate ? this.datePipe.transform(this.startDate, 'yyyy-MM-dd') : ''),
                    this.paramsQuery.toDate = (this.endDate ? this.datePipe.transform(this.endDate, 'yyyy-MM-dd') : ''),
                    this.paramsQuery.fromLKDate = (this.startDateCumulative ? this.datePipe.transform(this.startDateCumulative, 'yyyy-MM-dd') : ''),
                    this.paramsQuery.toLKDate = (this.endDateCumulative ? this.datePipe.transform(this.endDateCumulative, 'yyyy-MM-dd') : ''),
                    this.paramsQuery.agencyId = this.parentAgency;
                    this.dlkStatisticService.getlistDossier(this.paramsQuery).subscribe(res => {
                      this.listHoSo = res;
                    }, err => {
                      console.log(err);
                    });
                    this.dlkStatisticService.getTotalProcedureofDossier(this.paramsQuery).subscribe(res => {
                      this.listThuTucHoSo = res;
                    }, err => {
                      console.log(err);
                    });
                    let paramsQuery1 = {
                      fromDate: '',
                      toDate: '',
                      agencyId:null
                    };
                    paramsQuery1.fromDate = (this.startDateCumulative ? this.datePipe.transform(this.startDateCumulative, 'yyyy-MM-dd') : ''),
                    paramsQuery1.toDate = (this.endDateCumulative ? this.datePipe.transform(this.endDateCumulative, 'yyyy-MM-dd') : ''),
                    paramsQuery1.agencyId = this.parentAgency;
                    this.dlkStatisticService.getlistDossier(paramsQuery1).subscribe(res => {
                      this.listHoSoLK = res;
                    }, err => {
                      console.log(err);
                    });
                    this.dlkStatisticService.getTotalProcedureofDossier(paramsQuery1).subscribe(res => {
                      this.listThuTucHoSoLK = res;
                    }, err => {
                      console.log(err);
                    });    
                
            });
            return promise;
     
      }
      SelectedAgency="";
    GetDetailProcedure(AgencyId,AgencyName,LevelId) {

  
      // const dialogData = new ProcDetailDialogModel(AgencyId,AgencyName,LevelId,1);
      // const dialogRef = this.dialog.open(ProcDetailComponent, {
      //   width: '95%',
      //   height: '90%',
      //   data: dialogData,
      //   disableClose: true,
      //   autoFocus: false
      // });
      // dialogRef.afterClosed().subscribe(() => {
      // });
      // tslint:disable-next-line:max-line-length
      const dialogData = new procedureDetailDialogModel(AgencyId,AgencyName,LevelId,1);
      const dialogRef = this.dialog.open(DossierDetailComponent, {
        width: '85%',
        height: '90%',
        data: dialogData,
        disableClose: true,
        autoFocus: false
      });
      dialogRef.afterClosed().subscribe(() => {
      });
  
  } 
  GetDetailDossier(AgencyId,AgencyName,TrongKy,DaXuLy,TrucTiep,BCCI,MotPhan,ToanTrinh,toltal) {
    console.log("getlistDetailofDossier");

   
      if(TrongKy ==1)
      {
        this.paramsDossier.fromDate = (this.startDate ? this.datePipe.transform(this.startDate, 'yyyy-MM-dd') : '');
        this.paramsDossier.toDate = (this.endDate ? this.datePipe.transform(this.endDate, 'yyyy-MM-dd') : '');
      }
      else
      {
        this.paramsDossier.fromDate = (this.startDateCumulative ? this.datePipe.transform(this.startDateCumulative, 'yyyy-MM-dd') : '');
        this.paramsDossier.toDate = (this.endDateCumulative ? this.datePipe.transform(this.endDateCumulative, 'yyyy-MM-dd') : '');
        
      }
      // tslint:disable-next-line:max-line-length
      const dialogData = new DossierDetailDialogModel(AgencyId,AgencyName,TrongKy,DaXuLy,TrucTiep,BCCI,MotPhan,ToanTrinh,this.paramsDossier.fromDate,this.paramsDossier.toDate,toltal,0);
      const dialogRef = this.dialog.open(DossierDetailComponent, {
        width: '95%',
        height: '90%',
        data: dialogData,
        disableClose: true,
        autoFocus: false
      });
      dialogRef.afterClosed().subscribe(() => {
      });
  

  }


  PROCEDUREDATA: any[] = [];
  

  listproduretotal =[];
  getTotalProcedureByALLAgency()
  {
    let promise = new Promise((resolve, reject) => {
    let searchString  = "?agency-id=" + this.parentAgency  ;
    this.dlkStatisticService.getTotalProcedureByALLAgency(searchString ).subscribe(data => {
      
        this.listproduretotal = data;
      }, err => {
        console.log(err);
      });  
    });
    return promise;

  }
  
  getListAgencyAccept(prid,keyword, page, size) {
    const searchString = '?parent-id=' + prid + '&keyword=' + keyword + '&page=' + page + '&size=' + size + '&sort=name.name,asc&status=1';
    this.listHoSoCapTinh=[];
    this.listHoSoCapHuyen = [];
    this.procedureService.getListAgencyWithParent(searchString).subscribe(res => {

       this.listAgency = res.content;
    
    }, err => {
      console.log(err);
    });
  }
BuilData()
{
  let a = {
    TrongKy : 1,          
    tiepNhanTrongKy: 0,
    tiepNhanTrucTiep: 0,
    hoSoBuuChinh: 0,
    tiepNhanMotPhan: 0,
    tiepNhanToantrinh: 0,
    tongdaXuLy: 0,
    dxltiepNhanTrucTiep: 0,
    dxlhoSoBuuChinh: 0,
    dxltiepNhanTrucTuyen: 0,
    tthcBuuChinh: 0,
    tthcMotPhan: 0,
    tthcToanTrinh: 0,
    TongSoThuTuc : 0,
    ThutucMotPhan : 0,
    ThuTucToantrinh : 0
  }
  this.listHoSoCapTinh=[];
  this.listHoSoCapHuyen=[];
   this.TongCapTinh = {
    TrongKy : 1,          
    tiepNhanTrongKy: 0,
    tiepNhanTrucTiep: 0,
    hoSoBuuChinh: 0,
    tiepNhanMotPhan: 0,
    tiepNhanToantrinh: 0,
    tongdaXuLy: 0,
    dxltiepNhanTrucTiep: 0,
    dxlhoSoBuuChinh: 0,
    dxltiepNhanTrucTuyen: 0,
    tthcBuuChinh: 0,
    tthcMotPhan: 0,
    tthcToanTrinh: 0,
    TongSoThuTuc : 0,
    ThutucMotPhan : 0,
    ThuTucToantrinh : 0
  };
   this.TongCapTinhLK = {
    TrongKy : 2,          
    tiepNhanTrongKy: 0,
    tiepNhanTrucTiep: 0,
    hoSoBuuChinh: 0,
    tiepNhanMotPhan: 0,
    tiepNhanToantrinh: 0,
    tongdaXuLy: 0,
    dxltiepNhanTrucTiep: 0,
    dxlhoSoBuuChinh: 0,
    dxltiepNhanTrucTuyen: 0,
    tthcBuuChinh: 0,
    tthcMotPhan: 0,
    tthcToanTrinh: 0,
    TongSoThuTuc : 0,
    ThutucMotPhan : 0,
    ThuTucToantrinh : 0
  };
  this.TongCapHuyen={
    TrongKy : 1,          
    tiepNhanTrongKy: 0,
    tiepNhanTrucTiep: 0,
    hoSoBuuChinh: 0,
    tiepNhanMotPhan: 0,
    tiepNhanToantrinh: 0,
    tongdaXuLy: 0,
    dxltiepNhanTrucTiep: 0,
    dxlhoSoBuuChinh: 0,
    dxltiepNhanTrucTuyen: 0,
    tthcBuuChinh: 0,
    tthcMotPhan: 0,
    tthcToanTrinh: 0,
    TongSoThuTuc : 0,
    ThutucMotPhan : 0,
    ThuTucToantrinh : 0
  };
  this.TongCapHuyenLK  = {
    TrongKy : 2,          
    tiepNhanTrongKy: 0,
    tiepNhanTrucTiep: 0,
    hoSoBuuChinh: 0,
    tiepNhanMotPhan: 0,
    tiepNhanToantrinh: 0,
    tongdaXuLy: 0,
    dxltiepNhanTrucTiep: 0,
    dxlhoSoBuuChinh: 0,
    dxltiepNhanTrucTuyen: 0,
    tthcBuuChinh: 0,
    tthcMotPhan: 0,
    tthcToanTrinh: 0,
    TongSoThuTuc : 0,
    ThutucMotPhan : 0,
    ThuTucToantrinh : 0
  };
  for(let i = 0 ; i<this.listAgency.length;i++)
       {
        let agencyId = this.listAgency[i].id;
       //  let thutuccoquan = this.PROCEDUREDATA.filter(f =>f.agency.id !==null && f.agency.id === this.listAgency[i].id)
/*           let TongthuTuc = thutuccoquan.length;
          let TongthuMuc4= thutuccoquan.filter(f =>f.level.id !=="5f5b2c564e1bd312a6f3ae25").length;
          let TongthuMuc3= thutuccoquan.filter(f =>f.level.id !=="5f5b2c4b4e1bd312a6f3ae24").length;
          let TongthuTucXaThuocHuyen= this.ProduceVillage.filter(f =>f.agency.length >0 && f.agency[0].parent !==null && f.agency[0].parent.id === this.listAgency[i].id);
          let TongthuMuc4xa= TongthuTucXaThuocHuyen.filter(f =>f.level.id !=="5f5b2c564e1bd312a6f3ae25").length;
          let TongthuMuc3xa= TongthuTucXaThuocHuyen.filter(f =>f.level.id !=="5f5b2c4b4e1bd312a6f3ae24").length; */
          let TongthuTuc = 0;
          let TongthuMuc4= 0;
          let TongthuMuc3= 0;
         

          let SlhoSoCoQuan = this.listHoSo.filter(f =>(f.agencyId !==null && f.agencyId === this.listAgency[i].id)||(f.agencyParentId !==null && f.agencyParentId === this.listAgency[i].id))
          let SlThuTucHsCoQuan = this.listThuTucHoSo.filter(f =>f.agencyId !==null && f.agencyId === this.listAgency[i].id)

       
          var main ={
            id: this.listAgency[i].id,
            coQuan: this.listAgency[i].name,
            data :[]
          }
    // trong ky
            for(let ii = 0; ii<this.listproduretotal.length;ii++)
            {
              if(this.listproduretotal[ii].agencyId == agencyId || this.listproduretotal[ii].parentId == agencyId 
                || (this.listproduretotal[ii].ancestorsid!==null&&this.listproduretotal[ii].ancestorsid.includes(agencyId)))
                {
                  TongthuTuc = TongthuTuc + this.listproduretotal[ii].total
                  TongthuMuc3 = TongthuMuc3 + this.listproduretotal[ii].numberOfLevel3
                  TongthuMuc4 = TongthuMuc4 + this.listproduretotal[ii].numberOfLevel4
                }
            }
    
          var arr = {
            id: this.listAgency[i].id,
            coQuan: this.listAgency[i].name,
            TrongKy : 1,          
            tiepNhanTrongKy: 0,
            tiepNhanTrucTiep: 0,
            hoSoBuuChinh: 0,
            tiepNhanMotPhan: 0,
            tiepNhanToantrinh: 0,
            tongdaXuLy: 0,
            dxltiepNhanTrucTiep: 0,
            dxlhoSoBuuChinh: 0,
            dxltiepNhanTrucTuyen: 0,
            tthcBuuChinh: 0,
            tthcMotPhan: 0,
            tthcToanTrinh: 0,
            TongSoThuTuc : TongthuTuc,
            ThutucMotPhan : TongthuMuc3,
            ThuTucToantrinh : TongthuMuc4
          }
    
            if(SlhoSoCoQuan.length>0)
            {
              SlhoSoCoQuan.forEach(element => {
                arr.tiepNhanTrongKy+= element.tiepNhanTrongKy,
                arr.tiepNhanTrucTiep+=  element.tiepNhanTrucTiep,
                arr.hoSoBuuChinh+=  element.hoSoBuuChinh,
                arr.tiepNhanMotPhan+=  element.tiepNhanMotPhan,
                arr.tiepNhanToantrinh+= element.tiepNhanToantrinh,
                arr.tongdaXuLy+=  element.tongdaXuLy,
                arr.dxltiepNhanTrucTiep+=  element.dxltiepNhanTrucTiep,
                arr.dxlhoSoBuuChinh+=  element.dxlhoSoBuuChinh,
                arr.dxltiepNhanTrucTuyen+=  element.dxltiepNhanTrucTuyen
              });
              
            }
            if(SlThuTucHsCoQuan.length>0)
            {
              
              arr.tthcBuuChinh=  SlThuTucHsCoQuan[0].tthcBuuChinh,
              arr.tthcMotPhan= SlThuTucHsCoQuan[0].tthcMotPhan,
              arr.tthcToanTrinh=  SlThuTucHsCoQuan[0].tthcToanTrinh
            }
            main.data.push(arr);
          // luy ke 
          let SlhoSoCoQuanlk = this.listHoSoLK.filter(f =>(f.agencyId !==null && f.agencyId === this.listAgency[i].id)||(f.agencyParentId !==null && f.agencyParentId === this.listAgency[i].id))
          let SlThuTucHsCoQuanlk= this.listThuTucHoSoLK.filter(f =>f.agencyId !==null && f.agencyId === this.listAgency[i].id)

          var arr1 = {
            id: this.listAgency[i].id,
            coQuan: this.listAgency[i].name,
            TrongKy : 2,          
            tiepNhanTrongKy: 0,
            tiepNhanTrucTiep: 0,
            hoSoBuuChinh: 0,
            tiepNhanMotPhan: 0,
            tiepNhanToantrinh: 0,
            tongdaXuLy: 0,
            dxltiepNhanTrucTiep: 0,
            dxlhoSoBuuChinh: 0,
            dxltiepNhanTrucTuyen: 0,
            tthcBuuChinh: 0,
            tthcMotPhan: 0,
            tthcToanTrinh: 0,
            TongSoThuTuc : TongthuTuc,
            ThutucMotPhan : TongthuMuc3,
            ThuTucToantrinh : TongthuMuc4
          }
            if(SlhoSoCoQuanlk.length>0)
            {
              SlhoSoCoQuan.forEach(element => {
                arr1.tiepNhanTrongKy+= element.tiepNhanTrongKy,
                arr1.tiepNhanTrucTiep+=  element.tiepNhanTrucTiep,
                arr1.hoSoBuuChinh+=  element.hoSoBuuChinh,
                arr1.tiepNhanMotPhan+=  element.tiepNhanMotPhan,
                arr1.tiepNhanToantrinh+= element.tiepNhanToantrinh,
                arr1.tongdaXuLy+=  element.tongdaXuLy,
                arr1.dxltiepNhanTrucTiep+=  element.dxltiepNhanTrucTiep,
                arr1.dxlhoSoBuuChinh+=  element.dxlhoSoBuuChinh,
                arr1.dxltiepNhanTrucTuyen+=  element.dxltiepNhanTrucTuyen
              });
            }
            if(SlThuTucHsCoQuanlk.length>0)
            {
              
              arr1.tthcBuuChinh=  SlThuTucHsCoQuanlk[0].tthcBuuChinh,
              arr1.tthcMotPhan= SlThuTucHsCoQuanlk[0].tthcMotPhan,
              arr1.tthcToanTrinh=  SlThuTucHsCoQuanlk[0].tthcToanTrinh
            }
            main.data.push(arr1);
            let agencyLevel0 = "5f39f42d5224cf235e134c5a";
            let agencyLevel1 = "5f39f4155224cf235e134c59"
            /////////////////////////////////
          if(this.listAgency[i].level !== null && this.listAgency[i].level.id == agencyLevel0 )
          {
            this.listHoSoCapTinh.push(main);
            this.TongCapTinh.tiepNhanTrongKy += arr.tiepNhanTrongKy;
            this.TongCapTinh.tiepNhanTrucTiep += arr.tiepNhanTrucTiep;
            this.TongCapTinh.hoSoBuuChinh += arr.hoSoBuuChinh;
            this.TongCapTinh.tiepNhanMotPhan += arr.tiepNhanMotPhan;
            this.TongCapTinh.tiepNhanToantrinh += arr.tiepNhanToantrinh;
            this.TongCapTinh.tongdaXuLy += arr.tongdaXuLy;
            this.TongCapTinh.dxltiepNhanTrucTiep += arr.dxltiepNhanTrucTiep;
            this.TongCapTinh.dxlhoSoBuuChinh += arr.dxlhoSoBuuChinh;
            this.TongCapTinh.dxltiepNhanTrucTuyen += arr.dxltiepNhanTrucTuyen;
            this.TongCapTinh.tthcBuuChinh += arr.tthcBuuChinh;
            this.TongCapTinh.tthcMotPhan += arr.tthcMotPhan;
            this.TongCapTinh.tthcToanTrinh += arr.tthcToanTrinh;

            // luy ke
            this.TongCapTinhLK.tiepNhanTrongKy += arr1.tiepNhanTrongKy;
            this.TongCapTinhLK.tiepNhanTrucTiep += arr1.tiepNhanTrucTiep;
            this.TongCapTinhLK.hoSoBuuChinh += arr1.hoSoBuuChinh;
            this.TongCapTinhLK.tiepNhanMotPhan += arr1.tiepNhanMotPhan;
            this.TongCapTinhLK.tiepNhanToantrinh += arr1.tiepNhanToantrinh;
            this.TongCapTinhLK.tongdaXuLy += arr1.tongdaXuLy;
            this.TongCapTinhLK.dxltiepNhanTrucTiep += arr1.dxltiepNhanTrucTiep;
            this.TongCapTinhLK.dxlhoSoBuuChinh += arr1.dxlhoSoBuuChinh;
            this.TongCapTinhLK.dxltiepNhanTrucTuyen += arr1.dxltiepNhanTrucTuyen;
            this.TongCapTinhLK.tthcBuuChinh += arr1.tthcBuuChinh;
            this.TongCapTinhLK.tthcMotPhan += arr1.tthcMotPhan;
            this.TongCapTinhLK.tthcToanTrinh += arr1.tthcToanTrinh;
          }
            if(this.listAgency[i].level !== null &&this.listAgency[i].level.id == agencyLevel1 )
          {
            this.listHoSoCapHuyen.push(main);
            this.TongCapHuyen.tiepNhanTrongKy += arr.tiepNhanTrongKy;
            this.TongCapHuyen.tiepNhanTrucTiep += arr.tiepNhanTrucTiep;
            this.TongCapHuyen.hoSoBuuChinh += arr.hoSoBuuChinh;
            this.TongCapHuyen.tiepNhanMotPhan += arr.tiepNhanMotPhan;
            this.TongCapHuyen.tiepNhanToantrinh += arr.tiepNhanToantrinh;
            this.TongCapHuyen.tongdaXuLy += arr.tongdaXuLy;
            this.TongCapHuyen.dxltiepNhanTrucTiep += arr.dxltiepNhanTrucTiep;
            this.TongCapHuyen.dxlhoSoBuuChinh += arr.dxlhoSoBuuChinh;
            this.TongCapHuyen.dxltiepNhanTrucTuyen += arr.dxltiepNhanTrucTuyen;
            this.TongCapHuyen.tthcBuuChinh += arr.tthcBuuChinh;
            this.TongCapHuyen.tthcMotPhan += arr.tthcMotPhan;
            this.TongCapHuyen.tthcToanTrinh += arr.tthcToanTrinh;

            ///
            this.TongCapHuyenLK.tiepNhanTrongKy += arr1.tiepNhanTrongKy;
            this.TongCapHuyenLK.tiepNhanTrucTiep += arr1.tiepNhanTrucTiep;
            this.TongCapHuyenLK.hoSoBuuChinh += arr1.hoSoBuuChinh;
            this.TongCapHuyenLK.tiepNhanMotPhan += arr1.tiepNhanMotPhan;
            this.TongCapHuyenLK.tiepNhanToantrinh += arr1.tiepNhanToantrinh;
            this.TongCapHuyenLK.tongdaXuLy += arr1.tongdaXuLy;
            this.TongCapHuyenLK.dxltiepNhanTrucTiep += arr1.dxltiepNhanTrucTiep;
            this.TongCapHuyenLK.dxlhoSoBuuChinh += arr1.dxlhoSoBuuChinh;
            this.TongCapHuyenLK.dxltiepNhanTrucTuyen += arr1.dxltiepNhanTrucTuyen;
            this.TongCapHuyenLK.tthcBuuChinh += arr1.tthcBuuChinh;
            this.TongCapHuyenLK.tthcMotPhan += arr1.tthcMotPhan;
            this.TongCapHuyenLK.tthcToanTrinh += arr1.tthcToanTrinh;
          }
        
       } 
       this.waitingDownloadExcel = false;

  }
  waitingDownloadExcel:boolean = false;
  async exportToExcel() {
    //this.dataExport = await this.getDossierStatisticDetailExport();
    this.thongKe()
    const from = this.datePipe.transform(this.startDate, 'dd-MM-yyyy');
    const to = this.datePipe.transform(this.endDate, 'dd-MM-yyyy');
    const toLK = (this.endDateCumulative ? this.datePipe.transform(this.endDateCumulative, 'dd-MM-yyyy') : '');
    const newDateshort = this.datePipe.transform(new Date(), "dd-MM-yyyy")
    const newDate = this.datePipe.transform(new Date(), "dd-MM-yyyy HH:ss:mm")
    const excelFileName = `Bao_cao_chi_thi_08__toantinh_${newDate}`;
    let headerXLS = {
      row1: "CỘNG HÒA XÃ HỘI CHỦ NGHĨA VIỆT NAM",
      row2: "Độc lập - Tự do - Hạnh phúc",
      row3: "Đắk lắk, " + newDateshort,
      row4: `BÁO CÁO CHỈ THỊ 08 TOÀN TỈNH`,
      row5: `(Từ ${from} đến ngày ${to})`
    }


    const workbook = new Workbook();
    workbook.creator = 'Snippet Coder';
    workbook.lastModifiedBy = 'SnippetCoder';
    workbook.created = new Date();
    workbook.modified = new Date();
    const worksheet = workbook.addWorksheet("sheet1");

    // Add header row
    worksheet.addRow([]);
    worksheet.mergeCells('D1:V1');
    worksheet.getCell('D1').value = headerXLS.row1;
    worksheet.getCell('D1').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getCell('D1').font = {size: 13, bold: true, name: 'Times New Roman'};


    worksheet.mergeCells('D2:V2');
    worksheet.getCell('D2').value = headerXLS.row2;
    worksheet.getCell('D2').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getCell('D2').font = {size: 13,  bold: true, name: 'Times New Roman'};

    worksheet.mergeCells('D3:V3');
    worksheet.getCell('D3').value = headerXLS.row3;
    worksheet.getCell('D3').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getCell('D3').font = {size: 13, underline: true, name: 'Times New Roman'};

    worksheet.mergeCells('A4:V4');
    worksheet.getCell('A4').value = headerXLS.row4;
    worksheet.getCell('A4').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getCell('A4').font = {size: 14, bold: true, name: 'Times New Roman'};

    worksheet.mergeCells('A5:V5');
    worksheet.getCell('A5').value = "";
    worksheet.getCell('A5').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getCell('A5').font = {size: 11, italic: true, name: 'Times New Roman'};

    worksheet.mergeCells('A6:V6');
    worksheet.getCell('A6').value = headerXLS.row5;
    worksheet.getCell('A6').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getCell('A6').font = {size: 11, italic: true, name: 'Times New Roman'};


    worksheet.mergeCells('A7:A9');
    worksheet.getCell('A7').value ="STT";

    worksheet.mergeCells('B7:B9');
    worksheet.getCell('B7').value ="Tên cơ quan, đơn vị";

    worksheet.mergeCells('C7:C9');
    worksheet.getCell('C7').value ="Kỳ báo cáo";

    worksheet.mergeCells('D7:I7');
    worksheet.getCell('D7').value ="Tiếp nhận hồ sơ TTHC";
    worksheet.getCell('D7').alignment = { horizontal: 'center', vertical: 'middle',wrapText:true  };
    worksheet.getCell('D7').font = {size: 11, bold: true, name: 'Times New Roman'};
    worksheet.getCell('D7').border = {top: {style: 'thin'}, left: {style: 'thin'}, bottom: {style: 'thin'}, right: {style: 'thin'}};

    worksheet.mergeCells('J7:O7');
    worksheet.getCell('J7').value ="Số hồ sơ TTHC đã giải quyết";
    worksheet.getCell('J7').alignment = { horizontal: 'center', vertical: 'middle',wrapText:true  };
    worksheet.getCell('J7').font = {size: 11, bold: true, name: 'Times New Roman'};
    worksheet.getCell('J7').border = {top: {style: 'thin'}, left: {style: 'thin'}, bottom: {style: 'thin'}, right: {style: 'thin'}};

    worksheet.mergeCells('P7:V7');
    worksheet.getCell('P7').value ="Tỷ lệ TTHC cung cấp dịch vụ có phát sinh hồ sơ";
    worksheet.getCell('P7').alignment = { horizontal: 'center', vertical: 'middle',wrapText:true  };
    worksheet.getCell('P7').font = {size: 11, bold: true, name: 'Times New Roman'};
    worksheet.getCell('P7').border = {top: {style: 'thin'}, left: {style: 'thin'}, bottom: {style: 'thin'}, right: {style: 'thin'}};

    worksheet.mergeCells('D8:D9');
    worksheet.getCell('D8').value ="Tổng số";

    worksheet.mergeCells('E8:E9');
    worksheet.getCell('E8').value ="Trực tiếp";
    worksheet.mergeCells('F8:F9');
    worksheet.getCell('F8').value ="Qua BCCI";

    worksheet.mergeCells('G8:H8');
    worksheet.getCell('G8').value ="Trực tuyến";

    worksheet.getCell('G9').value ="Một phần";

    worksheet.getCell('H9').value ="Toàn trình";

    worksheet.mergeCells('I8:I9');
    worksheet.getCell('I8').value ="Cập nhật lên iGate";

    worksheet.mergeCells('J8:J9');
    worksheet.getCell('J8').value ="Tổng số";
    worksheet.mergeCells('K8:K9');
    worksheet.getCell('K8').value ="Trực tiếp";
    worksheet.mergeCells('L8:L9');
    worksheet.getCell('L8').value ="Qua BCCI";
    worksheet.mergeCells('M8:M9');
    worksheet.getCell('M8').value ="Trực tuyến";
    worksheet.mergeCells('N8:N9');
    worksheet.getCell('N8').value ="Cập nhật lên iGate";
    worksheet.mergeCells('O8:O9');
    worksheet.getCell('O8').value ="Số hồ sơ TTHC sử dụng ký số trong giải quyết";

    worksheet.mergeCells('P8:P9');
    worksheet.getCell('P8').value ="Tổng số TTHC của cơ quan";

    worksheet.mergeCells('Q8:R8');
    worksheet.getCell('Q8').value ="Một phần (trực tuyến)";
    worksheet.getCell('Q9').value ="Số TTHC cung cấp";
    worksheet.getCell('R9').value ="Số TTHC có phát sinh hồ sơ";

    worksheet.mergeCells('S8:T8');
    worksheet.getCell('S8').value ="Toàn trình";
    worksheet.getCell('S9').value ="Số TTHC cung cấp";
    worksheet.getCell('T9').value ="Số TTHC có phát sinh hồ sơ";

    worksheet.mergeCells('U8:V8');
    worksheet.getCell('U8').value ="BCCI";
    worksheet.getCell('U9').value ="Số TTHC cung cấp";
    worksheet.getCell('V9').value ="Số TTHC có phát sinh hồ sơ";

    const rowStartHeaderContent = 10;
    const NumberCol = 22;
    for (let index = 0; index < NumberCol; index++) {
      worksheet.getCell(9, (index + 1)).alignment = { horizontal: 'center', vertical: 'middle',wrapText:true  };
      worksheet.getCell(9, (index + 1)).font = {size: 11, bold: true, name: 'Times New Roman'};
      worksheet.getCell(9, (index + 1)).border = {top: {style: 'thin'}, left: {style: 'thin'}, bottom: {style: 'thin'}, right: {style: 'thin'}};

      worksheet.getCell(8, (index + 1)).alignment = { horizontal: 'center', vertical: 'middle', wrapText:true };
      worksheet.getCell(8, (index + 1)).font = {size: 11, bold: true, name: 'Times New Roman'};
      worksheet.getCell(8, (index + 1)).border = {top: {style: 'thin'}, left: {style: 'thin'}, bottom: {style: 'thin'}, right: {style: 'thin'}};


      worksheet.getCell(rowStartHeaderContent, (index + 1)).value = "(" + (index + 1).toString() + ")";
      worksheet.getCell(rowStartHeaderContent, (index + 1)).alignment = { horizontal: 'center', vertical: 'middle', wrapText:true };
      worksheet.getCell(rowStartHeaderContent, (index + 1)).font = {size: 11, bold: true, name: 'Times New Roman'};
      worksheet.getCell(rowStartHeaderContent, (index + 1)).border = {top: {style: 'thin'}, left: {style: 'thin'}, bottom: {style: 'thin'}, right: {style: 'thin'}};
    } 



    let rowStartContent = rowStartHeaderContent + 1;

    worksheet.getCell(rowStartContent,1).value = "I";
    worksheet.getCell(rowStartContent,1).alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getCell(rowStartContent,1).font = {size: 13, bold: true, name: 'Times New Roman'};
    worksheet.mergeCells(rowStartContent,2,rowStartContent,NumberCol);
    worksheet.getCell(rowStartContent,2).value = "Sở ban ngành";
    worksheet.getCell(rowStartContent,2).alignment = { horizontal: 'left', vertical: 'middle' };
    worksheet.getCell(rowStartContent,2).font = {size: 13, bold: true, name: 'Times New Roman'};
    worksheet.getCell(rowStartContent, 2).border = {top: {style: 'thin'}, left: {style: 'thin'}, bottom: {style: 'thin'}, right: {style: 'thin'}};

    rowStartContent = rowStartContent + 1;

    // cấp sở ban ngành
    const data = this.listHoSoCapTinh;
    for (let i = 0; i < data.length; i++) {
      var item = data[i];
      var r = 0;

      worksheet.mergeCells(rowStartContent,1,rowStartContent+1,1);
      worksheet.getCell(rowStartContent , 1).value = i+1;

      worksheet.mergeCells(rowStartContent ,2,rowStartContent +1,2);
      worksheet.getCell(rowStartContent , 2).value = item.coQuan;

      //trong kỳ 
      worksheet.getCell(rowStartContent + r, 3).value = "Trong kỳ";
      worksheet.getCell(rowStartContent + r, 4).value = item.data[r].tiepNhanTrongKy;
      worksheet.getCell(rowStartContent + r, 5).value = item.data[r].tiepNhanTrucTiep;
      worksheet.getCell(rowStartContent + r, 6).value = item.data[r].hoSoBuuChinh;
      worksheet.getCell(rowStartContent + r, 7).value = item.data[r].tiepNhanMotPhan ;
      worksheet.getCell(rowStartContent + r, 8).value = item.data[r].tiepNhanToantrinh;
      worksheet.getCell(rowStartContent + r, 9).value = item.data[r].tiepNhanTrongKy;
      worksheet.getCell(rowStartContent + r, 10).value = item.data[r].tongdaXuLy;
      worksheet.getCell(rowStartContent + r, 11).value = item.data[r].dxltiepNhanTrucTiep;
      worksheet.getCell(rowStartContent + r, 12).value = item.data[r].dxlhoSoBuuChinh;
      worksheet.getCell(rowStartContent + r, 13).value = item.data[r].dxltiepNhanTrucTuyen;
      worksheet.getCell(rowStartContent + r, 14).value = item.data[r].tongdaXuLy;
      worksheet.getCell(rowStartContent + r, 15).value = " ";
      worksheet.getCell(rowStartContent + r, 16).value = item.data[r].TongSoThuTuc;
      worksheet.getCell(rowStartContent + r, 17).value = item.data[r].ThutucMotPhan;
      worksheet.getCell(rowStartContent + r, 18).value = item.data[r].tthcMotPhan;
      worksheet.getCell(rowStartContent + r, 19).value = item.data[r].ThuTucToantrinh;
      worksheet.getCell(rowStartContent + r, 20).value = item.data[r].tthcToanTrinh;
      worksheet.getCell(rowStartContent + r, 21).value = " ";
      worksheet.getCell(rowStartContent + r, 22).value = item.data[r].tthcBuuChinh;
      
       for (let c = 0; c < NumberCol; c++) {
        worksheet.getCell(rowStartContent + r, (c + 1)).alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
        worksheet.getCell(rowStartContent + r, (c + 1)).font = {size: 11, name: 'Times New Roman'};
        worksheet.getCell(rowStartContent + r, (c + 1)).border = {top: {style: 'thin'}, left: {style: 'thin'}, bottom: {style: 'thin'}, right: {style: 'thin'}};
      }
      // Lũy kế
       r= r+1;
      worksheet.getCell(rowStartContent + r, 3).value = "Lũy kế đến " + toLK;
      worksheet.getCell(rowStartContent + r, 4).value = item.data[r].tiepNhanTrongKy;
      worksheet.getCell(rowStartContent + r, 5).value = item.data[r].tiepNhanTrucTiep;
      worksheet.getCell(rowStartContent + r, 6).value = item.data[r].hoSoBuuChinh;
      worksheet.getCell(rowStartContent + r, 7).value = item.data[r].tiepNhanMotPhan ;
      worksheet.getCell(rowStartContent + r, 8).value = item.data[r].tiepNhanToantrinh;
      worksheet.getCell(rowStartContent + r, 9).value = item.data[r].tiepNhanTrongKy;
      worksheet.getCell(rowStartContent + r, 10).value = item.data[r].tongdaXuLy;
      worksheet.getCell(rowStartContent + r, 11).value = item.data[r].dxltiepNhanTrucTiep;
      worksheet.getCell(rowStartContent + r, 12).value = item.data[r].dxlhoSoBuuChinh;
      worksheet.getCell(rowStartContent + r, 13).value = item.data[r].dxltiepNhanTrucTuyen;
      worksheet.getCell(rowStartContent + r, 14).value = item.data[r].tongdaXuLy;
      worksheet.getCell(rowStartContent + r, 15).value = " ";
      worksheet.getCell(rowStartContent + r, 16).value = item.data[r].TongSoThuTuc;
      worksheet.getCell(rowStartContent + r, 17).value = item.data[r].ThutucMotPhan;
      worksheet.getCell(rowStartContent + r, 18).value = item.data[r].tthcMotPhan;
      worksheet.getCell(rowStartContent + r, 19).value = item.data[r].ThuTucToantrinh;
      worksheet.getCell(rowStartContent + r, 20).value = item.data[r].tthcToanTrinh;
      worksheet.getCell(rowStartContent + r, 21).value = " ";
      worksheet.getCell(rowStartContent + r, 22).value = item.data[r].tthcBuuChinh;

      for (let c = 0; c < NumberCol; c++) {
        worksheet.getCell(rowStartContent + r, (c + 1)).alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
        worksheet.getCell(rowStartContent + r, (c + 1)).font = {size: 11, name: 'Times New Roman'};
        worksheet.getCell(rowStartContent + r, (c + 1)).border = {top: {style: 'thin'}, left: {style: 'thin'}, bottom: {style: 'thin'}, right: {style: 'thin'}};
      }
      rowStartContent = rowStartContent + 2;
    
    }

    //Tổng cấp tỉnh
    worksheet.mergeCells(rowStartContent ,1,rowStartContent +1,2);
    worksheet.getCell(rowStartContent , 1).value = "TỔNG SỞ BAN NGÀNH";
    worksheet.getCell(rowStartContent, 1).alignment = { horizontal: 'center', vertical: 'middle',  };
    worksheet.getCell(rowStartContent, 1).font = {size: 11, bold: true, name: 'Times New Roman'};
    worksheet.getCell(rowStartContent, 1).border = {top: {style: 'thin'}, left: {style: 'thin'}, bottom: {style: 'thin'}, right: {style: 'thin'}};

    worksheet.getCell(rowStartContent , 3).value = "Trong kỳ";
    worksheet.getCell(rowStartContent, 4).value = this.TongCapTinh.tiepNhanTrongKy;
    worksheet.getCell(rowStartContent, 5).value = this.TongCapTinh.tiepNhanTrucTiep;
    worksheet.getCell(rowStartContent, 6).value = this.TongCapTinh.hoSoBuuChinh;
    worksheet.getCell(rowStartContent, 7).value = this.TongCapTinh.tiepNhanMotPhan ;
    worksheet.getCell(rowStartContent, 8).value = this.TongCapTinh.tiepNhanToantrinh;
    worksheet.getCell(rowStartContent, 9).value = this.TongCapTinh.tiepNhanTrongKy;
    worksheet.getCell(rowStartContent, 10).value = this.TongCapTinh.tongdaXuLy;
    worksheet.getCell(rowStartContent, 11).value = this.TongCapTinh.dxltiepNhanTrucTiep;
    worksheet.getCell(rowStartContent, 12).value = this.TongCapTinh.dxlhoSoBuuChinh;
    worksheet.getCell(rowStartContent, 13).value = this.TongCapTinh.dxltiepNhanTrucTuyen;
    worksheet.getCell(rowStartContent, 14).value = this.TongCapTinh.tongdaXuLy;
    worksheet.getCell(rowStartContent, 15).value = " ";
    worksheet.getCell(rowStartContent, 16).value = this.TongCapTinh.TongSoThuTuc;
    worksheet.getCell(rowStartContent, 17).value = this.TongCapTinh.ThutucMotPhan;
    worksheet.getCell(rowStartContent, 18).value = this.TongCapTinh.tthcMotPhan;
    worksheet.getCell(rowStartContent, 19).value = this.TongCapTinh.ThuTucToantrinh;
    worksheet.getCell(rowStartContent, 20).value = this.TongCapTinh.tthcToanTrinh;
    worksheet.getCell(rowStartContent, 21).value = " ";
    worksheet.getCell(rowStartContent, 22).value = this.TongCapTinh.tthcBuuChinh;
    rowStartContent=rowStartContent+1;
 
    worksheet.getCell(rowStartContent , 3).value =  "Lũy kế đến "  + toLK;
    worksheet.getCell(rowStartContent, 4).value = this.TongCapTinhLK.tiepNhanTrongKy;
    worksheet.getCell(rowStartContent, 5).value = this.TongCapTinhLK.tiepNhanTrucTiep;
    worksheet.getCell(rowStartContent, 6).value = this.TongCapTinhLK.hoSoBuuChinh;
    worksheet.getCell(rowStartContent, 7).value = this.TongCapTinhLK.tiepNhanMotPhan ;
    worksheet.getCell(rowStartContent, 8).value = this.TongCapTinhLK.tiepNhanToantrinh;
    worksheet.getCell(rowStartContent, 9).value = this.TongCapTinhLK.tiepNhanTrongKy;
    worksheet.getCell(rowStartContent, 10).value = this.TongCapTinhLK.tongdaXuLy;
    worksheet.getCell(rowStartContent, 11).value = this.TongCapTinhLK.dxltiepNhanTrucTiep;
    worksheet.getCell(rowStartContent, 12).value = this.TongCapTinhLK.dxlhoSoBuuChinh;
    worksheet.getCell(rowStartContent, 13).value = this.TongCapTinhLK.dxltiepNhanTrucTuyen;
    worksheet.getCell(rowStartContent, 14).value = this.TongCapTinhLK.tongdaXuLy;
    worksheet.getCell(rowStartContent, 15).value = " ";
    worksheet.getCell(rowStartContent, 16).value = this.TongCapTinhLK.TongSoThuTuc;
    worksheet.getCell(rowStartContent, 17).value = this.TongCapTinhLK.ThutucMotPhan;
    worksheet.getCell(rowStartContent, 18).value = this.TongCapTinhLK.tthcMotPhan;
    worksheet.getCell(rowStartContent, 19).value = this.TongCapTinhLK.ThuTucToantrinh;
    worksheet.getCell(rowStartContent, 20).value = this.TongCapTinhLK.tthcToanTrinh;
    worksheet.getCell(rowStartContent, 21).value = " ";
    worksheet.getCell(rowStartContent, 22).value = this.TongCapTinhLK.tthcBuuChinh;

    for (let index = 2; index < NumberCol; index++) {
      worksheet.getCell(rowStartContent, (index + 1)).alignment = { horizontal: 'center', vertical: 'middle',  };
      worksheet.getCell(rowStartContent, (index + 1)).font = {size: 11, bold: true, name: 'Times New Roman'};
      worksheet.getCell(rowStartContent, (index + 1)).border = {top: {style: 'thin'}, left: {style: 'thin'}, bottom: {style: 'thin'}, right: {style: 'thin'}};

      worksheet.getCell(rowStartContent-1, (index + 1)).alignment = { horizontal: 'center', vertical: 'middle',  };
      worksheet.getCell(rowStartContent-1, (index + 1)).font = {size: 11, bold: true, name: 'Times New Roman'};
      worksheet.getCell(rowStartContent-1, (index + 1)).border = {top: {style: 'thin'}, left: {style: 'thin'}, bottom: {style: 'thin'}, right: {style: 'thin'}};
    }

    rowStartContent=rowStartContent+1;
    worksheet.getCell(rowStartContent,1).value = "II";
    worksheet.getCell(rowStartContent,1).alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getCell(rowStartContent,1).font = {size: 13, bold: true, name: 'Times New Roman'};
    worksheet.mergeCells(rowStartContent,2,rowStartContent,NumberCol);
    worksheet.getCell(rowStartContent,2).value = "Thành phố, huyện, thị xã";
    worksheet.getCell(rowStartContent,2).alignment = { horizontal: 'left', vertical: 'middle' };
    worksheet.getCell(rowStartContent,2).font = {size: 13, bold: true, name: 'Times New Roman'};
    worksheet.getCell(rowStartContent, 2).border = {top: {style: 'thin'}, left: {style: 'thin'}, bottom: {style: 'thin'}, right: {style: 'thin'}};

    rowStartContent = rowStartContent + 1;
    // List cấp huyện
    const data1 = this.listHoSoCapHuyen;
    for (let i = 0; i < data1.length; i++) {
      var item = data1[i];
      var r = 0;

      worksheet.mergeCells(rowStartContent + r,1,rowStartContent + r+1,1);
      worksheet.getCell(rowStartContent + r, 1).value = i+1;
      worksheet.mergeCells(rowStartContent + r,2,rowStartContent + r+1,2);
      worksheet.getCell(rowStartContent + r, 2).value = item.coQuan;

      //trong kỳ 
      worksheet.getCell(rowStartContent + r, 3).value = "Trong kỳ";
      worksheet.getCell(rowStartContent + r, 4).value = item.data[r].tiepNhanTrongKy;
      worksheet.getCell(rowStartContent + r, 5).value = item.data[r].tiepNhanTrucTiep;
      worksheet.getCell(rowStartContent + r, 6).value = item.data[r].hoSoBuuChinh;
      worksheet.getCell(rowStartContent + r, 7).value = item.data[r].tiepNhanMotPhan ;
      worksheet.getCell(rowStartContent + r, 8).value = item.data[r].tiepNhanToantrinh;
      worksheet.getCell(rowStartContent + r, 9).value = item.data[r].tiepNhanTrongKy;
      worksheet.getCell(rowStartContent + r, 10).value = item.data[r].tongdaXuLy;
      worksheet.getCell(rowStartContent + r, 11).value = item.data[r].dxltiepNhanTrucTiep;
      worksheet.getCell(rowStartContent + r, 12).value = item.data[r].dxlhoSoBuuChinh;
      worksheet.getCell(rowStartContent + r, 13).value = item.data[r].dxltiepNhanTrucTuyen;
      worksheet.getCell(rowStartContent + r, 14).value = item.data[r].tongdaXuLy;
      worksheet.getCell(rowStartContent + r, 15).value = " ";
      worksheet.getCell(rowStartContent + r, 16).value = item.data[r].TongSoThuTuc;
      worksheet.getCell(rowStartContent + r, 17).value = item.data[r].ThutucMotPhan;
      worksheet.getCell(rowStartContent + r, 18).value = item.data[r].tthcMotPhan;
      worksheet.getCell(rowStartContent + r, 19).value = item.data[r].ThuTucToantrinh;
      worksheet.getCell(rowStartContent + r, 20).value = item.data[r].tthcToanTrinh;
      worksheet.getCell(rowStartContent + r, 21).value = " ";
      worksheet.getCell(rowStartContent + r, 22).value = item.data[r].tthcBuuChinh;
      
       for (let c = 0; c < NumberCol; c++) {
        worksheet.getCell(rowStartContent + r, (c + 1)).alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
        worksheet.getCell(rowStartContent + r, (c + 1)).font = {size: 11, name: 'Times New Roman'};
        worksheet.getCell(rowStartContent + r, (c + 1)).border = {top: {style: 'thin'}, left: {style: 'thin'}, bottom: {style: 'thin'}, right: {style: 'thin'}};
      }
      // Lũy kế
       r= r+1;
      worksheet.getCell(rowStartContent + r, 3).value = "Lũy kế đến " + toLK;
      worksheet.getCell(rowStartContent + r, 4).value = item.data[r].tiepNhanTrongKy;
      worksheet.getCell(rowStartContent + r, 5).value = item.data[r].tiepNhanTrucTiep;
      worksheet.getCell(rowStartContent + r, 6).value = item.data[r].hoSoBuuChinh;
      worksheet.getCell(rowStartContent + r, 7).value = item.data[r].tiepNhanMotPhan ;
      worksheet.getCell(rowStartContent + r, 8).value = item.data[r].tiepNhanToantrinh;
      worksheet.getCell(rowStartContent + r, 9).value = item.data[r].tiepNhanTrongKy;
      worksheet.getCell(rowStartContent + r, 10).value = item.data[r].tongdaXuLy;
      worksheet.getCell(rowStartContent + r, 11).value = item.data[r].dxltiepNhanTrucTiep;
      worksheet.getCell(rowStartContent + r, 12).value = item.data[r].dxlhoSoBuuChinh;
      worksheet.getCell(rowStartContent + r, 13).value = item.data[r].dxltiepNhanTrucTuyen;
      worksheet.getCell(rowStartContent + r, 14).value = item.data[r].tongdaXuLy;
      worksheet.getCell(rowStartContent + r, 15).value = " ";
      worksheet.getCell(rowStartContent + r, 16).value = item.data[r].TongSoThuTuc;
      worksheet.getCell(rowStartContent + r, 17).value = item.data[r].ThutucMotPhan;
      worksheet.getCell(rowStartContent + r, 18).value = item.data[r].tthcMotPhan;
      worksheet.getCell(rowStartContent + r, 19).value = item.data[r].ThuTucToantrinh;
      worksheet.getCell(rowStartContent + r, 20).value = item.data[r].tthcToanTrinh;
      worksheet.getCell(rowStartContent + r, 21).value = " ";
      worksheet.getCell(rowStartContent + r, 22).value = item.data[r].tthcBuuChinh;

      for (let c = 0; c < NumberCol; c++) {
        worksheet.getCell(rowStartContent + r, (c + 1)).alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
        worksheet.getCell(rowStartContent + r, (c + 1)).font = {size: 11, name: 'Times New Roman'};
        worksheet.getCell(rowStartContent + r, (c + 1)).border = {top: {style: 'thin'}, left: {style: 'thin'}, bottom: {style: 'thin'}, right: {style: 'thin'}};
      }
      rowStartContent = rowStartContent + 2;
   
    }

    //Tổng cấp Huyện
    worksheet.mergeCells(rowStartContent ,1,rowStartContent +1,2);
    worksheet.getCell(rowStartContent , 1).value = "TỔNG THÀNH PHỐ, HUYỆN, THỊ XÃ";
    worksheet.getCell(rowStartContent, 1).alignment = { horizontal: 'center', vertical: 'middle',  };
    worksheet.getCell(rowStartContent, 1).font = {size: 11, bold: true, name: 'Times New Roman'};
    worksheet.getCell(rowStartContent, 1).border = {top: {style: 'thin'}, left: {style: 'thin'}, bottom: {style: 'thin'}, right: {style: 'thin'}};
    
 

    worksheet.getCell(rowStartContent , 3).value = "Trong kỳ";
    worksheet.getCell(rowStartContent, 4).value = this.TongCapHuyen.tiepNhanTrongKy;
    worksheet.getCell(rowStartContent, 5).value = this.TongCapHuyen.tiepNhanTrucTiep;
    worksheet.getCell(rowStartContent, 6).value = this.TongCapHuyen.hoSoBuuChinh;
    worksheet.getCell(rowStartContent, 7).value = this.TongCapHuyen.tiepNhanMotPhan ;
    worksheet.getCell(rowStartContent, 8).value = this.TongCapHuyen.tiepNhanToantrinh;
    worksheet.getCell(rowStartContent, 9).value = this.TongCapHuyen.tiepNhanTrongKy;
    worksheet.getCell(rowStartContent, 10).value = this.TongCapHuyen.tongdaXuLy;
    worksheet.getCell(rowStartContent, 11).value = this.TongCapHuyen.dxltiepNhanTrucTiep;
    worksheet.getCell(rowStartContent, 12).value = this.TongCapHuyen.dxlhoSoBuuChinh;
    worksheet.getCell(rowStartContent, 13).value = this.TongCapHuyen.dxltiepNhanTrucTuyen;
    worksheet.getCell(rowStartContent, 14).value = this.TongCapHuyen.tongdaXuLy;
    worksheet.getCell(rowStartContent, 15).value = " ";
    worksheet.getCell(rowStartContent, 16).value = this.TongCapHuyen.TongSoThuTuc;
    worksheet.getCell(rowStartContent, 17).value = this.TongCapHuyen.ThutucMotPhan;
    worksheet.getCell(rowStartContent, 18).value = this.TongCapHuyen.tthcMotPhan;
    worksheet.getCell(rowStartContent, 19).value = this.TongCapHuyen.ThuTucToantrinh;
    worksheet.getCell(rowStartContent, 20).value = this.TongCapHuyen.tthcToanTrinh;
    worksheet.getCell(rowStartContent, 21).value = " ";
    worksheet.getCell(rowStartContent, 22).value = this.TongCapHuyen.tthcBuuChinh;
    rowStartContent=rowStartContent+1;

    worksheet.getCell(rowStartContent , 3).value =  "Lũy kế đến " + toLK;
    worksheet.getCell(rowStartContent, 4).value = this.TongCapHuyenLK.tiepNhanTrongKy;
    worksheet.getCell(rowStartContent, 5).value = this.TongCapHuyenLK.tiepNhanTrucTiep;
    worksheet.getCell(rowStartContent, 6).value = this.TongCapHuyenLK.hoSoBuuChinh;
    worksheet.getCell(rowStartContent, 7).value = this.TongCapHuyenLK.tiepNhanMotPhan ;
    worksheet.getCell(rowStartContent, 8).value = this.TongCapHuyenLK.tiepNhanToantrinh;
    worksheet.getCell(rowStartContent, 9).value = this.TongCapHuyenLK.tiepNhanTrongKy;
    worksheet.getCell(rowStartContent, 10).value = this.TongCapHuyenLK.tongdaXuLy;
    worksheet.getCell(rowStartContent, 11).value = this.TongCapHuyenLK.dxltiepNhanTrucTiep;
    worksheet.getCell(rowStartContent, 12).value = this.TongCapHuyenLK.dxlhoSoBuuChinh;
    worksheet.getCell(rowStartContent, 13).value = this.TongCapHuyenLK.dxltiepNhanTrucTuyen;
    worksheet.getCell(rowStartContent, 14).value = this.TongCapHuyenLK.tongdaXuLy;
    worksheet.getCell(rowStartContent, 15).value = " ";
    worksheet.getCell(rowStartContent, 16).value = this.TongCapHuyenLK.TongSoThuTuc;
    worksheet.getCell(rowStartContent, 17).value = this.TongCapHuyenLK.ThutucMotPhan;
    worksheet.getCell(rowStartContent, 18).value = this.TongCapHuyenLK.tthcMotPhan;
    worksheet.getCell(rowStartContent, 19).value = this.TongCapHuyenLK.ThuTucToantrinh;
    worksheet.getCell(rowStartContent, 20).value = this.TongCapHuyenLK.tthcToanTrinh;
    worksheet.getCell(rowStartContent, 21).value = " ";
    worksheet.getCell(rowStartContent, 22).value = this.TongCapHuyenLK.tthcBuuChinh;

    for (let index = 2; index < NumberCol; index++) {
      worksheet.getCell(rowStartContent, (index + 1)).alignment = { horizontal: 'center', vertical: 'middle',  };
      worksheet.getCell(rowStartContent, (index + 1)).font = {size: 11, bold: true, name: 'Times New Roman'};
      worksheet.getCell(rowStartContent, (index + 1)).border = {top: {style: 'thin'}, left: {style: 'thin'}, bottom: {style: 'thin'}, right: {style: 'thin'}};

      worksheet.getCell(rowStartContent-1, (index + 1)).alignment = { horizontal: 'center', vertical: 'middle',  };
      worksheet.getCell(rowStartContent-1, (index + 1)).font = {size: 11, bold: true, name: 'Times New Roman'};
      worksheet.getCell(rowStartContent-1, (index + 1)).border = {top: {style: 'thin'}, left: {style: 'thin'}, bottom: {style: 'thin'}, right: {style: 'thin'}};
    }

    worksheet.getColumn(1).width = 7;
    worksheet.getColumn(1).alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
    worksheet.getColumn(2).width = 25;
    worksheet.getColumn(2).alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
    worksheet.getColumn(3).width = 22;
    // worksheet.getColumn(3).alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
    // worksheet.getColumn(4).width = 7;
    // worksheet.getColumn(4).alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
    // worksheet.getColumn(5).width = 7;
    // worksheet.getColumn(5).alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
    // worksheet.getColumn(6).width = 7;
    // worksheet.getColumn(6).alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
    // worksheet.getColumn(7).width = 7;
    // worksheet.getColumn(7).alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
    // worksheet.getColumn(8).width = 7;
    // worksheet.getColumn(8).alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
    // worksheet.getColumn(9).width = 7;
    // worksheet.getColumn(9).alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
    // worksheet.getColumn(10).width = 7;
    // worksheet.getColumn(10).alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
    // worksheet.getColumn(11).width = 7;
    // worksheet.getColumn(11).alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
    // worksheet.getColumn(12).width = 7;
    // worksheet.getColumn(12).alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
    // worksheet.getColumn(13).width = 7;
    // worksheet.getColumn(13).alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};

    // Save Excel File
    workbook.xlsx.writeBuffer().then((dataBuffer: ArrayBuffer) => {
      const blob = new Blob([dataBuffer], { type: this.EXCEL_TYPE });
      fs.saveAs(blob, excelFileName + this.EXCEL_EXTENSION);
    });
  }


}

