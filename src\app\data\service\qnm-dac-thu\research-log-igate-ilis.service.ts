import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { ApiProviderService } from 'src/app/core/service/api-provider.service';
import { Observable } from 'rxjs';
import { Workbook } from 'exceljs';
import { DatePipe } from '@angular/common';
import * as fs from 'file-saver';
const EXCEL_TYPE = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8';
const EXCEL_EXTENSION = '.xlsx';

@Injectable({
  providedIn: 'root'
})
export class EventLogiLisService {

  private adapterUrl = this.apiProviderService.getUrl('digo', 'adapter');
  private padmanUrl = this.apiProviderService.getUrl('digo', 'padman');
  private logiLis = this.adapterUrl + '/log-ilis/--search';
  private eventlogiLis = this.adapterUrl + '/log-ilis/';
  private updateUrl = this.adapterUrl + '/log-ilis/--update-body';
  private nextTaskUrl = this.padmanUrl + '/ilis/--next-task';
  constructor(
    private http: HttpClient,
    private datePipe: DatePipe,
    private apiProviderService: ApiProviderService
  ) { }

  getLogiLisService(searchStr:string): Observable<any> {
    let URL = this.logiLis + searchStr;
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.get(URL, { headers }).pipe();
  }
  public exportAsExcelDossierLogIis(
    reportHeading: string,
    reportSubHeading: string,
    json: any[],
    excelFileName: string,
    sheetName: string,  ) {
    const data = json;
    console.log('json',data)
    // create workbook and worksheet
    const workbook = new Workbook();
    workbook.creator = 'Snippet Coder';
    workbook.lastModifiedBy = 'SnippetCoder';
    workbook.created = new Date();
    workbook.modified = new Date();
    const worksheet = workbook.addWorksheet(sheetName);

    worksheet.getColumn('A').font = {name: 'Times New Roman', size: 12};
    worksheet.getColumn('B').font = {name: 'Times New Roman', size: 12};
    worksheet.getColumn('C').font = {name: 'Times New Roman', size: 12};
    worksheet.getColumn('D').font = {name: 'Times New Roman', size: 12};
    worksheet.getColumn('E').font = {name: 'Times New Roman', size: 12};
    worksheet.getColumn('F').font = {name: 'Times New Roman', size: 12};
    worksheet.getColumn('G').font = {name: 'Times New Roman', size: 12};
    worksheet.getColumn('H').font = {name: 'Times New Roman', size: 12};

    worksheet.getColumn('A').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getColumn('B').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getColumn('C').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getColumn('D').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getColumn('E').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getColumn('F').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getColumn('G').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getColumn('H').alignment = { horizontal: 'center', vertical: 'middle' };

    // worksheet.getColumn('A').width = 30;
    worksheet.getColumn('B').width = 40;
    worksheet.getColumn('C').width = 20;
    worksheet.getColumn('D').width = 20;
    worksheet.getColumn('E').width = 20;
    worksheet.getColumn('F').width = 20;
    worksheet.getColumn('G').width = 20;
    worksheet.getColumn('H').width = 20;

    // Add header row
    worksheet.addRow([]);
    worksheet.mergeCells('A1:H1');
    worksheet.getCell('A1').value = reportHeading;
    worksheet.getCell('A1').alignment = { horizontal: 'center', vertical: 'middle' };

    worksheet.addRow([]);
    worksheet.mergeCells('A2:H2');
    worksheet.getCell('A2').value = reportSubHeading;
    worksheet.getCell('A2').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getCell('A2').font = { size: 12, italic: true, name: 'Times New Roman' };

    worksheet.getCell('A4').value = 'STT';
    worksheet.getCell('B4').value = 'Mã hồ sơ';
    worksheet.getCell('C4').value = 'Loại đồng bộ';
    worksheet.getCell('D4').value = 'Trạng thái';
    worksheet.getCell('E4').value = 'Lỗi';
    worksheet.getCell('F4').value = 'Số lần đồng bộ';
    worksheet.getCell('G4').value = 'Ngày thực thi';
    worksheet.properties.outlineLevelCol = 2; worksheet.properties.defaultRowHeight = 15;
    let i = 4;
    const j = 4;
    for (i; i <= j; i++) {
      let k = 1;
      const l = 7; // column G
      for (k; k <= l; k++) {
        worksheet.findCell(i, k).alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
        worksheet.findCell(i, k).fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: 'C0C0C0C0' },
          bgColor: { argb: 'FF0000FF' }
        };
        worksheet.findCell(i, k).border = { top: { style: 'thin' }, left: { style: 'thin' }, bottom: { style: 'thin' }, right: { style: 'thin' } };
        worksheet.findCell(i, k).font = { size: 12, bold: true, name: 'Times New Roman' };
      }
    }

    // get all columns from JSON
    let columnsArray: any[];
    for (const key in json) {
      if (json.hasOwnProperty(key)) {
        columnsArray = Object.keys(json[key]);
      }
    }
    // Add Data and Conditional Formatting
    data.forEach((element: any) => {
      const eachRow = [];
      
      columnsArray.forEach((column) => {
        eachRow.push(element[column]);
      });
      const borderrow = worksheet.addRow(eachRow);
        borderrow.eachCell((cell) => {
          if(element?.isGroupBy != true)   {
            cell.border = {top: {style: 'thin'}, left: {style: 'thin'}, bottom: {style: 'thin'}, right: {style: 'thin'}};
            }else{
              cell.font = {size: 13, bold: true, name: 'Times New Roman'};
              worksheet.mergeCells('A'+cell['row']+":"+'G'+cell['row']);
            }
            cell.alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
          
        });
    });
    const dataLength = data.length;
    if (dataLength > 0) {
      for (i = 0; i < dataLength; i++) {
        worksheet.getCell('B' + (5 + i)).alignment = {horizontal: 'left', vertical: 'middle', wrapText: true};
      }
    }
    // Save Excel File
    // tslint:disable-next-line:no-shadowed-variable
    workbook.xlsx.writeBuffer().then((data: ArrayBuffer) => {
      const blob = new Blob([data], { type: EXCEL_TYPE });
      fs.saveAs(blob, excelFileName + EXCEL_EXTENSION);
    });
  }
  updateBody(pid,body:object): Observable<any> {
    let URL = this.updateUrl + "?pid="+pid;
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.put(URL, body, { headers }).pipe();
  }
  sendCallBackILis(uri: string, body:object):Observable<any>{

    let new_uri = this.adapterUrl + "/ilis/" + uri.split("/ilis/")[1];
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    const URL = new_uri;
    return this.http.post(URL, body, { headers });
  }
  getLogiLisServiceNoPage(searchStr:string): Observable<any> {
    let URL = this.eventlogiLis+"--search-no-page" + searchStr;
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.get(URL, { headers }).pipe();
  }
  writeLogiLisService(searchStr:string): Observable<any> {
    let URL = this.eventlogiLis +"--write-log-ilis"+ searchStr;
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.post(URL, { headers }).pipe();
  }
  getLogiLisByCode(searchStr:string): Observable<any> {
    let URL = this.eventlogiLis+"--detail-by-code" + searchStr;
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.get(URL, { headers }).pipe();
  }
  callBackAllFail(): Observable<any> {
    let URL = this.eventlogiLis+"--job-call-back-ilis-failed";
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.post(URL, { headers }).pipe();
  }
  nextTaskIlis(searchStr): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.put(this.nextTaskUrl+searchStr, { headers }).pipe();
  }
}
