import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { DirectlyPaymentNotificationComponent } from './directly-payment-notification.component';

describe('DirectlyPaymentNotificationComponent', () => {
  let component: DirectlyPaymentNotificationComponent;
  let fixture: ComponentFixture<DirectlyPaymentNotificationComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ DirectlyPaymentNotificationComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(DirectlyPaymentNotificationComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
