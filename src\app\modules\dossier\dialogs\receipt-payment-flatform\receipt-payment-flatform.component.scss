.dialog_title {
    font-style: normal;
    font-weight: 500;
    font-size: 20px;
    color: #ce7a58;
}

::ng-deep .applyBtn {
    margin-top: 1em;
    background-color: #ce7a58;
    color: #fff;
    height: 3em;
    padding: 0 3em;
}

::ng-deep mat-dialog-container::-webkit-scrollbar-track {
    box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.1);
    background-color: #f5f5f5;
}

::ng-deep mat-dialog-container::-webkit-scrollbar {
    width: 5px;
    background-color: #f5f5f5;
}

::ng-deep mat-dialog-container::-webkit-scrollbar-thumb {
    box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.1);
    background-color: #44444450;
}

.close-button {
    float: right;
    top: -24px;
    right: -24px;
}

::ng-deep .dialog_content {
    font-size: 15px;
    max-height: unset;
    overflow: unset;
    .highlight {
        color: #ce7a58;
    }

    .formFieldItems {
        flex-wrap: wrap;
    }

    .receiptForm {
        .mat-form-field-appearance-outline {
            width: 100%;

            .mat-form-field-infix {
                padding: 0.8em 0;
            }
            .mat-form-field-outline {
                color: transparent;
                background-color: #eaebeb;
                border-radius: 5px;
            }
            .mat-form-field-outline-thick {
                color: #dddddd;
            }
        }
        .mat-form-field-wrapper {
            // padding-bottom: unset !important;
        }
        .mat-form-field-label-wrapper {
            top: -1em;
            font-size: 14px;
        }
        .mat-form-field.mat-focused {
            .mat-form-field-label {
                color: #ce7a58;
                font-size: 14px;
            }
        }
        .mat-form-field-appearance-outline.mat-form-field-can-float.mat-form-field-should-float {
            .mat-form-field-label {
                color: #ce7a58;
                transform: translateY(-1.55em) scale(1);
                margin-bottom: 1em;
                font-size: 14px;
            }
        }

        .mat-form-field .error_Msg {
            font-size: 12px !important;
            float: right;
            display: flex;
            color: #ce7a58;
            .err {
                background-color: #f2a63494;
                border-radius: 50%;
                width: 1.2em;
                height: 1.2em;
                justify-content: center;
                display: flex;
                margin-left: 0.5em;
                .mat-icon {
                    color: #ce7a58;
                    vertical-align: middle;
                    align-self: center;
                    transform: scale(0.8);
                }
            }
        }
    }

    .receipt {
        padding: 0;
        border-radius: 6px;
        flex-wrap: wrap;

        .mat-table {
            border: 1px solid #33333325;
            border-radius: 4px;
            border-bottom: none;

            .mat-header-cell {
                background-color: #f2f2f2;
                font-weight: 500;
                color: #333;
                th {
                    font-weight: 500;
                    color: #333;
                }
            }

            .mat-column-date {
                flex: 0 0 20%;
                align-self: baseline;
            }

            .mat-column-supplier {
                padding: 0 .5em;
                align-self: baseline;
            }

            .mat-column-detail {
                padding: 0 1em;
                flex: 1 0 10%;
                display: grid;
                align-self: baseline;

                .items {
                    padding: .5em 0;
                    margin-bottom: 0;
                    width: 100%;

                    span:nth-child(2) {
                        float: right;
                    }
                }
            }

            .mat-column-person {
                padding: 0 .5em;
                align-self: baseline;
            }

            .mat-column-status {
                padding: 0 .5em;
                align-self: baseline;
            }

            .mat-column-action {
                padding: 0 .5em;
                align-self: baseline;
            }
        }
    }
}

@media screen and (max-width: 800px) {
    ::ng-deep {
        .receipt {
            .mat-row {
                &:nth-child(even) {
                    background-color: unset;
                }
                &:nth-child(odd) {
                    background-color: unset;
                }
            }

            .mat-header-row {
                display: none;
            }
            .mat-table {
                border: 0;
                vertical-align: middle;
                margin-bottom: 1em;
 
                .mat-row {
                    border-bottom: 5px solid #ddd;
                    display: block;
                    min-height: unset;
                }
                .mat-cell {
                    border-bottom: 1px solid #ddd;
                    display: block;
                    font-size: 14px;
                    text-align: right;
                    margin-bottom: 4%;
                    padding: 0 0.5em;
                    &:before {
                        content: attr(data-label);
                        float: left;
                        font-weight: 500;
                        font-size: 14px;
                        text-align: left;
                    }
                    &:last-child {
                        border-bottom: 0;
                    }
                    &:first-child {
                        margin-top: 4%;
                    }
                }
            }
        }
    }
}