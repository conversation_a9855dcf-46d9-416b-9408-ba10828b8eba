import { Injectable } from '@angular/core';
import {HttpClient, HttpHeaders, HttpParams} from '@angular/common/http';
import { DeploymentService } from '../deployment.service';
import {Observable} from "rxjs";
import { ApiProviderService } from 'src/app/core/service/api-provider.service';

@Injectable({
  providedIn: 'root'
})
export class IntegratedSidewalkService {

  constructor(
    private http: HttpClient,
    private deploymentService: DeploymentService,
    private apiProviderService: ApiProviderService
  ) { }

  private adapter = this.apiProviderService.getUrl('digo', 'adapter');

  private apiUrl = this.deploymentService.env.OS_HCM.integratedSidewalk.apiUrl;
  private apiUrnFee = this.deploymentService.env.OS_HCM.integratedSidewalk.apiUrnGetFee;
  private apiKey = this.deploymentService.env.OS_HCM.integratedSidewalk.apiAuthKey;
  private apiValue = this.deploymentService.env.OS_HCM.integratedSidewalk.apiAuthValue;

  getFees(areaID, purpose, routeID, arceage, dateStart, dateEnd) : Observable<any> {
    let headers = new HttpHeaders(); 
    headers.append("Access-Control-Allow-Headers", "*");
    let params = `?areaID=${areaID}&purpose=${purpose}&routeID=${routeID}`+
    `&arceage=${arceage}&useStart=${dateStart}&useEnd=${dateEnd}`+
    `&apiUrl=${this.apiUrl}&apiUrnGetFee=${this.apiUrnFee}&apiKeyName=${this.apiKey}&apiKeyValue=${this.apiValue}`;
    //return this.http.get("http://localhost:8084" + '/integrated-sidewalk/getFee' + params,{ headers }).pipe();
    return this.http.get(this.adapter + '/integrated-sidewalk/getFee' + params,{ headers }).pipe();
  }
}
