import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { DashboardComponent } from './dashboard.component';
import { AuthGuard } from 'src/app/core/guard/auth.guard';


const routes: Routes = [
  {
    path: '',
    component: DashboardComponent,
    canActivate: [AuthGuard],
    data: {
      anyPermissions: [
        'oneGateAdminMaster',                 // <PERSON><PERSON> toàn quyền
        'oneGateDossierManager',              // <PERSON><PERSON> toàn quyền xử lý hồ sơ
        'oneGateDossierAccepter',             // Chỉ tiếp nhận
        'oneGateDossierOnlineReception',      // <PERSON><PERSON> sơ chờ tiếp nhận
        'oneGateDossierReception',            // Tiếp nhận hồ sơ
        'oneGateDossierImport',               // Import hồ sơ từ file excel
        'oneGateDossierProcessing',           // <PERSON><PERSON> lý hồ sơ
        'oneGateDossierCancel',               // <PERSON><PERSON> sơ không cần x<PERSON> lý
        'oneGateDossierLookup',               // Tra cứu hồ sơ toàn cơ quan
        'oneGateDossierLookupByAgency',       // Tra cứu hồ sơ theo đơn vị
        'oneGateDossierLookupPersonal',       // Tra cứu hồ sơ cá nhân
        'oneGateDossierConfiguration',        // Cấu hình số hồ sơ
        'oneGateSectorCategory',              // Danh mục lĩnh vực
        'oneGateDocumentCategory',            // Danh mục giấy tờ
        'oneGateProcedureCategory',           // Danh mục thủ tục
        'oneGateFeeType',                     // Danh mục loại phí/ lệ phí
        'oneGateProcostCategory',             // Danh mục phí/ lệ phí
        'oneGateConfigTemplate',              // Cấu hình phiếu động
        'oneGateProcessCatalog',              // Danh mục quy trình
        'oneGateLedgerDefinitionCatalog',     // Danh mục sổ
        'oneGateLedgerCatalog',               // Danh mục quyển sổ
        'oneGateAdministrativeCategory',      // Danh mục sổ hành chính công
        'oneGate012020Statistical',           // Thống kê theo thông tư 01/2020/TT-VPCP
        'oneGateProcedureReport',             // Thống kê theo thông tư 01/2018/TT-VPCP
        'oneGate022017Statistical',           // Thống kê theo thông tư 02/2017/TT-VPCP
        'oneGate022017StatisticalCMU',        // Thống kê theo thông tư 02/2017/TT-VPCP - TTHC
        'oneGate022017StatisticalDay',        // Thống kê theo thông tư 02/2017/TT-VPCP trên kiến trúc ElasticSearch (IGATESUPP-71095)
        'oneGateAllStatistical',              // Thống kê chung
        'oneGateDossierFeeReport',            // Thống kê lệ phí hồ sơ
        'oneGateOverdueDossierReport',        // Thống kê hồ sơ trễ hạn
        'oneGateLogbookStatistics',           // Thống kê sổ theo dõi
        'oneGate247QDTTG',                    // Thống kê theo Quyết định 274/QĐ-TTG năm 2019
        'oneGatePeriodicReport',              // Báo cáo tổng hợp theo đơn vị
        'oneGateLevelReport',                 // Báo cáo số liệu hồ sơ dịch vụ công theo mức độ
        'oneGateDossierInTheYear',            // Thống kê số lượng hồ sơ tiếp nhận trong năm
        'oneGateCancelDossierReport',         // Thống kê hồ sơ hủy
        'oneGateLedgerCatalog',               // Danh mục quyển sổ
        'oneGateReportII08',                  // Báo cáo biểu số II.08
        'oneGateStatisticCheckBDG',           // Thống kê rà soát hệ thống
        'oneGateReportVpcpOnline',            // Báo cáo ứng dụng dịch vụ công trực tuyến
        'oneGateReportTTPVKSTTHC',            // Báo cáo TTPVKSTTHC QNI
        'oneGateReportAgency',                // Thống kê hồ sơ theo cơ quan
        'oneGateOverdueDossierReportHCM',     // Thống kê hồ sơ trễ hạn HCM
        'oneGateResidentialInfo',             // Tra cứu thông tin dân cư 037
        'oneGateAuthIdentity',                // Xác thực thông tin công dân 033
        'oneGateHouseholdInfo',               // Xác thực thông tin gia đình 034
        'oneGateDossierDVCLTStatistical',     // Thống kê hố sơ DVCLT
        'oneGateVPUBStatistical',             // Thống kê hồ sơ VPUB xử lý
        'oneGateStatisticsOfCitizen',         // Thống kê lượt tra cứu thông tin công dân qua cơ sở dữ liệu quốc gia về dân cư
        'oneGate749Statistical',              // Thống kê theo văn bản 749/VNPT-IT-KV5
        'oneGateDossierFeeReportAgg',         // Thống kê lệ phí AGG
        'oneGateViewProcess',                 // Chỉ xem quy trình
      ],
      permissions: []
    }
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class DashboardRoutingModule { }
