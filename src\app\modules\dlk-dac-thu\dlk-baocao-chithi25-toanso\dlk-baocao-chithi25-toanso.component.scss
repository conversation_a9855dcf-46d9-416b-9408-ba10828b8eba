.search-nested {
    width: 80% !important;
    height: 50px !important;
    margin-left: 15px !important;
}

.clear-search-nested {
    right: 0 !important;
    position: absolute !important;
    top: 0 !important;
}

.data-label {
    word-wrap: break-word;
}

.mat-tooltip {
    font-size: 13px;
}

.ml-20 {
    margin-left: 20px;
}

.frm_main {
    border-radius: 4px;
    padding: 1em;
    box-shadow: 0px 1px 15px rgba(69, 65, 78, 0.19);
    .ctrl {
        .btnCtrl {
            background-color: #e8e8e8;
            color: #666;
            float: right;
            margin-left: 1em;
            .mat-icon {
                color: #ce7a58;
                margin-right: .2em;
            }
        }
    }
    .logbookTbl {
        //margin-top: 1.5em;
        .logbookOnlyTbl {
            overflow-x: scroll;
        }
        table {
            border-collapse: collapse;
            width: 100%;
        }
        table tr th {
            border: 1px solid;
            text-align: center;
            font-weight: bold;
            background: #cccccc50;
            //color: #fff;
            border: 1px solid rgb(182, 182, 182);
            padding: 5px;
            font-size: 14px;
        }
        table tr td {
            border: 1px solid rgb(182, 182, 182);
            text-align: center;
            padding: 5px;
            font-size: 14px;
        }
        table tr td a {
            cursor: pointer;
            color: #3c8dbc;
        }
        .sum {
            font-weight: 500;
            background-color: #dff0d8;
        }
        ;
        // table {
        //     width: 100%;
        //     border-radius: 4px;
        //     border: 1px solid #cccccc50;
        //     width: 100%;
        // }
        // th {
        //     //padding: .5em;
        //     border: 1px solid #cccccc50 !important;
        // }
        // .cell_code {
        //     color: #ce7a58;
        //     font-weight: 500;
        //     text-decoration: none;
        //     a {
        //         cursor: pointer;
        //     }
        // }
        // table {
        //     width: 100%;
        //     border-radius: 4px;
        //     border: 1px solid #ececec;
        //     width: 100%;
        // }
        // .th {
        //     padding: .5em;
        //     border: .2px solid #cccccc50 !important;
        // }
        // .tr {
        //     background-color: #e8e8e8;
        //     min-height: 3.5em !important;
        //     .th {
        //         color: #495057;
        //         font-size: 14px;
        //         padding: 0 .5em;
        //         border: .2px solid #cccccc50 !important;
        //         p {
        //             margin-bottom: 0;
        //             font-weight: 400;
        //             font-style: italic;
        //         }
        //     }
        // }
        // .mat-column-stt {
        //     padding-right: 0.5em;
        //     padding-left: 1em;
        //     flex: 0 0 5%;
        // }
        // .mat-column-code a {
        //     text-decoration: none;
        //     font-weight: 500;
        //     color: #ce7a58;
        // }
        // .mat-column-procedure {
        //     flex: 1 0 5%;
        //     padding: 0 0.5em;
        //     .procedureName {
        //         display: -webkit-box;
        //         -webkit-line-clamp: 4;
        //         -webkit-box-orient: vertical;
        //         width: 100%;
        //         overflow: hidden;
        //         text-overflow: ellipsis;
        //     }
        // }
    }
}

::ng-deep .prc_searchbar .searchForm {
    @import "~src/styles/buttons.scss";
    .btn-search {
        @extend .t-btn-search;
    }
    .btn-download-excel {
        @extend .t-btn-download-excel;
    }
    .btn-select-display-col {
        @extend .t-btn-select-display-col;
    }
    .btn-print {
        @extend .t-btn-print;
    }
}

.logbookOnlyTbl_mobile {
    display: none;
}

@media screen and (max-width: 600px) {
    .logbookOnlyTbl {
        display: none;
    }
    .logbookOnlyTbl_mobile {
        display: block !important;
        .mat-header-row {
            display: none;
        }
        .mat-table {
            border: 0;
            vertical-align: middle;
            .mat-row {
                border-bottom: 5px solid #ddd;
                display: block;
                min-height: unset;
            }
            .mat-cell {
                border-bottom: 1px solid #ddd;
                display: block;
                font-size: 14px;
                text-align: right;
                margin-bottom: 4%;
                padding: 0 0.5em;
                &:before {
                    content: attr(data-label);
                    float: left;
                    font-weight: 500;
                    font-size: 14px;
                    width: 45%;
                    text-align: left;
                }
                &:last-child {
                    border-bottom: 0;
                }
                &:first-child {
                    margin-top: 4%;
                }
            }
        }
        .mat-row {
            &:nth-child(even) {
                background-color: unset;
            }
            &:nth-child(odd) {
                background-color: unset;
            }
        }
    }
}