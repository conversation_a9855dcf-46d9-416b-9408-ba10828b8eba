import { Injectable } from '@angular/core';
import {EnvService} from "core/service/env.service";
import {HttpBackend, HttpClient, HttpHeaders} from "@angular/common/http";
import {ApiProviderService} from "core/service/api-provider.service";
import {AgencyService} from "data/service/basedata/agency.service";
import {Observable} from "rxjs";
import { DeploymentService } from 'src/app/data/service/deployment.service';
import * as moment from 'moment';

@Injectable({
  providedIn: 'root'
})
export class VerifyCitizensLgspHcmService {
  config = this.envService.getConfig();
  private eform = this.config.formioURL + '/form';
  private adapter = this.apiProviderService.getUrl('digo', 'adapter');
  private httpWithoutAuth: HttpClient;
  constructor(private envService: EnvService,
              private http: HttpClient,
              private apiProviderService: ApiProviderService,
              private deploymentService: DeploymentService,
              private agencyService: AgencyService,
              private httpBackend: HttpBackend
            ) { 
              this.httpWithoutAuth = new HttpClient(this.httpBackend);
            }

  getCitizenInfoForm(): Observable<any> {
    const URL = `${this.eform}/62318a06d53aa5001f7d9d8a`;
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get<any>(URL, { headers }).pipe();
  }

  toBirthday(isoDate){
    let date = new Date(isoDate);
    let yyyy = date.getFullYear();
    let mm:any = date.getMonth()+1;
    let dd:any = date.getDate();

    if (dd < 10) {
      dd = `0${dd}`;
    }
    if (mm < 10) {
      mm = `0${mm}`;
    }

    return `${yyyy}${mm}${dd}`;
  }

  mergeObject(oldObj, newObj){
    let data = {};
    for (const [key, value] of Object.entries(oldObj)) {
      const newValue = newObj[key];
      if(newObj[key]){
        data[key] = newValue;
      }else {
        data[key] = value;
      }
    }
    return data;
  }

  getCitizenIfo(indentityNumber?:string, fullname?:string, birthday?:string, eformId?:string):Observable<any>{
    this.getClientIp();
    let headers = new HttpHeaders();
    // birthday = this.toBirthday(birthday);
    const subsystemId = this.config?.subsystemId;
    const agency = this.agencyService.getAgency(true);
    const agencyId = agency.rootAgencyId;
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    let URL = `${this.adapter}/citizen/--info?`;
    URL += `agency-id=${agencyId}`;
    URL += `&subsystem-id=${subsystemId}`;
    URL += `&indentity-number=${indentityNumber}`;
    URL += `&fullname=${fullname}`;
    URL += `&birthday=${birthday}`;
    URL += eformId?`&eform-id=${eformId}`:``;
    console.clear();
    console.log(`URL: ${URL}`);
    return this.http.get(URL,{ headers });
  }

  verifyCitizenLGSPHCM(indentityNumber?:string, fullname?:string, birthday?:string, eformId?:string):Observable<any>{
    let headers = new HttpHeaders();
    const subsystemId = this.config?.subsystemId;
    const agency = this.agencyService.getAgency(true);
    const agencyId = agency.rootAgencyId;
    fullname = this.removeVietnameseTones(fullname);
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    let URL = `${this.adapter}/citizen/--lgsp?`;
    URL += `agency-id=${agencyId}`;
    URL += `&subsystem-id=${subsystemId}`;
    URL += `&indentity-number=${indentityNumber}`;
    URL += `&fullname=${fullname}`;
    URL += `&birthday=${birthday}`;
    URL += eformId?`&eform-id=${eformId}`:``;
    console.clear();
    console.log(`URL: ${URL}`);
    return this.http.get(URL,{ headers });
  }

  removeVietnameseTones(str) {
    str = str.replace(/à|á|ạ|ả|ã|â|ầ|ấ|ậ|ẩ|ẫ|ă|ằ|ắ|ặ|ẳ|ẵ/g,"a"); 
    str = str.replace(/è|é|ẹ|ẻ|ẽ|ê|ề|ế|ệ|ể|ễ/g,"e"); 
    str = str.replace(/ì|í|ị|ỉ|ĩ/g,"i"); 
    str = str.replace(/ò|ó|ọ|ỏ|õ|ô|ồ|ố|ộ|ổ|ỗ|ơ|ờ|ớ|ợ|ở|ỡ/g,"o"); 
    str = str.replace(/ù|ú|ụ|ủ|ũ|ư|ừ|ứ|ự|ử|ữ/g,"u"); 
    str = str.replace(/ỳ|ý|ỵ|ỷ|ỹ/g,"y"); 
    str = str.replace(/đ/g,"d");
    str = str.replace(/À|Á|Ạ|Ả|Ã|Â|Ầ|Ấ|Ậ|Ẩ|Ẫ|Ă|Ằ|Ắ|Ặ|Ẳ|Ẵ/g, "A");
    str = str.replace(/È|É|Ẹ|Ẻ|Ẽ|Ê|Ề|Ế|Ệ|Ể|Ễ/g, "E");
    str = str.replace(/Ì|Í|Ị|Ỉ|Ĩ/g, "I");
    str = str.replace(/Ò|Ó|Ọ|Ỏ|Õ|Ô|Ồ|Ố|Ộ|Ổ|Ỗ|Ơ|Ờ|Ớ|Ợ|Ở|Ỡ/g, "O");
    str = str.replace(/Ù|Ú|Ụ|Ủ|Ũ|Ư|Ừ|Ứ|Ự|Ử|Ữ/g, "U");
    str = str.replace(/Ỳ|Ý|Ỵ|Ỷ|Ỹ/g, "Y");
    str = str.replace(/Đ/g, "D");
    // Some system encode vietnamese combining accent as individual utf-8 characters
    // Một vài bộ encode coi các dấu mũ, dấu chữ như một kí tự riêng biệt nên thêm hai dòng này
    str = str.replace(/\u0300|\u0301|\u0303|\u0309|\u0323/g, ""); // ̀ ́ ̃ ̉ ̣  huyền, sắc, ngã, hỏi, nặng
    str = str.replace(/\u02C6|\u0306|\u031B/g, ""); // ˆ ̆ ̛  Â, Ê, Ă, Ơ, Ư
    // Remove extra spaces
    // Bỏ các khoảng trắng liền nhau
    str = str.replace(/ + /g," ");
    str = str.trim();
    // Remove punctuations
    // Bỏ dấu câu, kí tự đặc biệt
    str = str.replace(/!|@|%|\^|\*|\(|\)|\+|\=|\<|\>|\?|\/|,|\.|\:|\;|\'|\"|\&|\#|\[|\]|~|\$|_|`|-|{|}|\||\\/g," ");
    str = str.replace(/\s/g, '');
    return str;
  }

  getClientIp() {
    if(!sessionStorage.getItem('clientIP')) {
      this.httpWithoutAuth.get<{ ip: string }>(!!this.deploymentService.env?.iPCheckUrl ? this.deploymentService.env?.iPCheckUrl : 'https://ip.vnptioffice.vn/?format=json')
      .subscribe(data => {
        sessionStorage.setItem('clientIP', data.ip);
      });
    }
  }
  
}
