import { Component, OnInit, ChangeDetectorRef, AfterViewInit } from '@angular/core';
import { FormGroup, FormControl } from '@angular/forms';
import { EnvService } from 'src/app/core/service/env.service';
import { FormElement } from 'src/app/data/schema/form-element';
import { MatTableDataSource } from '@angular/material/table';
import { SnackbarService } from 'src/app/data/service/snackbar/snackbar.service';
import { MainService } from 'src/app/data/service/main/main.service';
import { DeploymentService } from 'data/service/deployment.service';
import { PadmanService } from 'src/app/data/service/svc-padman/padman.service';
import { DatePipe } from '@angular/common';
import { MatDialog } from '@angular/material/dialog';
import { AdapterService } from 'src/app/data/service/svc-adapter/adapter.service';
import { ExportExcelService } from 'src/app/data/service/export-excel/export-excel.service';
import { DetailAggDossierBudgetComponent, DetailAggDossierBudgetComponentDialog } from './dialogs/detail-agg-dossier-budget/detail-agg-dossier-budget.component';
import { CertificateAggDossierBudgetComponent, CertificateAggDossierBudgetComponentDialog } from './dialogs/certificate-agg-dossier-budget/certificate-agg-dossier-budget.component';
import { MatPaginator, PageEvent } from '@angular/material/paginator';

@Component({
  selector: 'app-agg-dossier-budget',
  templateUrl: './agg-dossier-budget.component.html',
  styleUrls: ['./agg-dossier-budget.component.scss']
})

export class AggDossierBudgetComponent implements OnInit, AfterViewInit  {
  config = this.envService.getConfig();
  selectedLang = localStorage.getItem('language') || 'vi';
  selectedLangId = Number(localStorage.getItem('languageId')) || 228;
  env = this.deploymentService.getAppDeployment().env;

  date = new Date();
  
  searchForm = new FormGroup({
    fromdate: new FormControl(new Date(this.date.getTime() - 5 * 86400000)),
    todate: new FormControl(new Date()),
    hsid: new FormControl(''),
  });

  pageTitle = {
    vi: `Tra cứu danh sách hồ sơ Bộ Tài Chính`,
    en: `Dossier statistics`
  };

  displayedColumns: string[] = ['stt', 'hsid', 'ten_hs', 'ma', 'nguoi_dk', 'email', 'sdt_didong', 'ngay_dk', 'ngay_tao', 'ngay_pd', 'nguoi_pd', 'ten_kieu_tiep_nhan', 'ten_trang_thai', 'ngay_tra', 'gcn'];
  ELEMENTDATA: FormElement[] = [];
  dataSource: MatTableDataSource<any>;
  searchTerm: string = '';

  size = 10;
  pageIndex = 1;
  page = 1;
  countResult = 0;
  pgSizeOptions = this.config.pageSizeOptions;

  pagingType: any;
  
  constructor(
    private adapterService: AdapterService,
    private dialog: MatDialog,
    private envService: EnvService,
    private mainService: MainService,
    private padmanService: PadmanService,
    private cdRef: ChangeDetectorRef,
    private snackbarService: SnackbarService,
    private deploymentService: DeploymentService,
    private datepipe: DatePipe,
    private exportExcel: ExportExcelService
  ) { this.dataSource = new MatTableDataSource(this.ELEMENTDATA); }

  ngOnInit(): void {
    this.mainService.setPageTitle(this.pageTitle[localStorage.getItem('language')]);
  }

  onConfirm() {
    const formObj = this.searchForm.getRawValue();

    if (!formObj.fromdate || !formObj.todate) {
      this.snackbarService.openSnackBar(0, 'Ngày tra cứu không được để trống', '', 'error_notification', this.config.expiredTime);
      return;
    } else {
      if ((formObj.todate - formObj.fromdate) / (1000 * 60 * 60 * 24) > 5) {
        this.snackbarService.openSnackBar(0, 'Khoảng cách ngày bắt đầu và ngày kết thúc tối đa là 5 ngày!', '', 'error_notification', this.config.expiredTime);
        return
      } else {
        var fDate2 = this.datepipe.transform(formObj.fromdate, 'yyyy-MM-dd').toString();
        var tDate2 = this.datepipe.transform(formObj.todate, 'yyyy-MM-dd').toString();
        if (tDate2 <= fDate2) {
          this.snackbarService.openSnackBar(0, 'Ngày bắt đầu phải nhỏ hơn ngày kết thúc', '', 'error_notification', this.config.expiredTime);
          return;
        } else {
          var fDate = this.datepipe.transform(formObj.fromdate, 'ddMMyyyy').toString();
          var tDate = this.datepipe.transform(formObj.todate, 'ddMMyyyy').toString();
        }
      }
    }

    var searchString = "";
    searchString =
      '?sort=hsid,asc' + 
      '&spec=page&page=0' +
      '&size=1000' +
      '&from-date=' + fDate +
      '&to-date=' + tDate;
    this.page = 1;
    this.pageIndex = 0;
    this.getDossier(searchString);
  }

  onInsert() {
    const formObj = this.searchForm.getRawValue();

    if (!formObj.fromdate || !formObj.todate) {
      this.snackbarService.openSnackBar(0, 'Ngày tra cứu không được để trống', '', 'error_notification', this.config.expiredTime);
      return;
    } else {
      var fDate2 = this.datepipe.transform(formObj.fromdate, 'yyyy-MM-dd').toString();
      var tDate2 = this.datepipe.transform(formObj.todate, 'yyyy-MM-dd').toString();
      if (tDate2 <= fDate2) {
        this.snackbarService.openSnackBar(0, 'Ngày bắt đầu phải nhỏ hơn ngày kết thúc', '', 'error_notification', this.config.expiredTime);
        return;
      } else {
        var fDate = this.datepipe.transform(formObj.fromdate, 'ddMMyyyy').toString();
        var tDate = this.datepipe.transform(formObj.todate, 'ddMMyyyy').toString();
      }
    }

    this.adapterService.insertBudgetDossierListAgg(fDate, tDate).subscribe(() => {
      let successMsg = {
        vi: 'Lưu thành công',
        en: 'Success'
    };
    this.snackbarService.openSnackBar(1, successMsg[this.selectedLang], '', 'success_notification', this.config.expiredTime);
    }, err => {
      let msgObj = err.error.message || 'Lỗi không xác định';
      if (msgObj.includes('java.lang.NullPointerException')) {
        msgObj = {
          vi: 'Không tìm thấy hồ sơ',
          en: 'Dossier not found'
        };
        this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
        return;
      }
      this.snackbarService.openSnackBar(0, msgObj, '', 'error_notification', this.config.expiredTime);
    });
  }

  onGetCSDL() {
    const formObj = this.searchForm.getRawValue();

    if (!formObj.fromdate || !formObj.todate) {
      this.snackbarService.openSnackBar(0, 'Ngày tra cứu không được để trống', '', 'error_notification', this.config.expiredTime);
      return;
    } else {
      var fDate = this.datepipe.transform(formObj.fromdate, 'yyyy-MM-dd').toString();
      var tDate = this.datepipe.transform(formObj.todate, 'yyyy-MM-dd').toString();
      if (tDate <= fDate) {
        this.snackbarService.openSnackBar(0, 'Ngày bắt đầu phải nhỏ hơn ngày kết thúc', '', 'error_notification', this.config.expiredTime);
        return;
      }
    }

    var searchString = "";
    searchString =
      '?sort=hsid,asc' + 
      '&spec=page&page=0' +
      '&size=' + this.size +
      '&from-date=' + fDate +
      '&to-date=' + tDate;
    this.page = 1;
    this.pageIndex = 1;
    this.getDossierCSDL(searchString);
  }

  ngAfterViewInit() {
    this.cdRef.detectChanges();
  }
  
  getDossier(searchParams) {
    this.adapterService.getBudgetDossierListAggPage(searchParams).subscribe(data => {
      this.ELEMENTDATA = [];
      this.countResult = data.totalElements;
      for (let i = 0; i < data.numberOfElements; i++) {
        data.content[i].stt = this.size * (this.pageIndex) + (i + 1);
        this.ELEMENTDATA.push(data.content[i]);
      }
      this.dataSource.data = this.ELEMENTDATA;
      this.pagingType = 0;
      this.updateDataSource();
    }, err => {
      let msgObj = err.error.message || 'Lỗi không xác định';
      if (msgObj.includes('java.lang.NullPointerException')) {
        msgObj = {
          vi: 'Không tìm thấy hồ sơ',
          en: 'Dossier not found'
        };
        this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
        return;
      }
      this.snackbarService.openSnackBar(0, msgObj, '', 'error_notification', this.config.expiredTime);
    });
  }

  getDossierCSDL(searchParams) {
    this.padmanService.getAggDossierBudget(searchParams).subscribe(data => {
      this.ELEMENTDATA = [];
      this.countResult = data.totalElements;
      for (let i = 0; i < data.numberOfElements; i++) {
        data.content[i].stt = this.size * (this.pageIndex - 1) + (i + 1);
        this.ELEMENTDATA.push(data.content[i]);
      }
      this.dataSource.data = this.ELEMENTDATA;
      this.pagingType = 1;
    }, err => {
      let msgObj = err.error.message || 'Lỗi không xác định';
      if (msgObj.includes('java.lang.NullPointerException')) {
        msgObj = {
          vi: 'Không tìm thấy hồ sơ',
          en: 'Dossier not found'
        };
        this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
        return;
      }
      this.snackbarService.openSnackBar(0, msgObj, '', 'error_notification', this.config.expiredTime);
    });
  }

  // Phân trang
  paginate(event: any, type) {
    const formObj = this.searchForm.getRawValue();
    var searchString = "";
    if(this.pagingType === 1){
      searchString =
      '?sort=hsid,asc' + 
      '&spec=page&page=0' +
      '&size=' + this.size +
      '&from-date=' + this.datepipe.transform(formObj.fromdate, 'yyyy-MM-dd').toString() +
      '&to-date=' + this.datepipe.transform(formObj.todate, 'yyyy-MM-dd').toString();
      switch (type) {
        case 0:
          this.pageIndex = event;
          this.page = event;
          this.getDossierCSDL('?page=' + (this.pageIndex - 1) + '&size=' + this.size + '&spec=page' + searchString);
          break;
        case 1:
          this.pageIndex = 1;
          this.page = 1;
          this.getDossierCSDL('?page=' + (this.pageIndex - 1) + '&size=' + this.size + '&spec=page' + searchString);
          break;
      }
    }else{
      switch (type) {
        case 0:
          this.pageIndex = event;
          this.pageIndex = this.pageIndex - 1;
          this.page = event;
          this.updateDataSource();
          break;
        case 1:
          this.pageIndex = 1;
          this.pageIndex = this.pageIndex - 1;
          this.page = 1;
          this.updateDataSource();
          break;
      }
    }
  }

  applyFilter() {
    this.dataSource.filter = this.searchTerm.trim().toLowerCase();
  }

  exportToExcelAll() {
    const formObj = this.searchForm.getRawValue();

    var toDay = this.datepipe.transform(new Date(), 'dd_MM').toString();
    const header1 = `UBND TỈNH AN GIANG`;
    const header2 = `CỘNG HÒA XÃ HỘI CHỦ NGHĨA VIỆT NAM`;
    const header3 = `SỞ TÀI CHÍNH`;
    const header4 = `Độc lập - Tự do - Hạnh phúc`;
    let header5 = ``;
    if (formObj.fromdate && formObj.todate){
      header5 = `(Từ ngày ${this.datepipe.transform(formObj.fromdate, 'dd/MM/yyyy').toString()} đến ngày ${this.datepipe.transform(formObj.todate, 'dd/MM/yyyy').toString()})`;
    }
    const reportTitle = `DANH SÁCH HỒ SƠ CẤP MÃ SỐ CHO ĐƠN VỊ CÓ QUAN HỆ NGÂN SÁCH`;
    const name = `${toDay}_danhsachhoso`;
    if(this.pagingType === 0){
      if (!formObj.fromdate || !formObj.todate) {
        
        this.snackbarService.openSnackBar(0, 'Ngày tra cứu không được để trống', '', 'error_notification', this.config.expiredTime);
        return;
      } else {
        var fDate2 = this.datepipe.transform(formObj.fromdate, 'yyyy-MM-dd').toString();
        var tDate2 = this.datepipe.transform(formObj.todate, 'yyyy-MM-dd').toString();
        if (tDate2 <= fDate2) {
          this.snackbarService.openSnackBar(0, 'Ngày bắt đầu phải nhỏ hơn ngày kết thúc', '', 'error_notification', this.config.expiredTime);
          return;
        } else {
          var fDate = this.datepipe.transform(formObj.fromdate, 'ddMMyyyy').toString();
          var tDate = this.datepipe.transform(formObj.todate, 'ddMMyyyy').toString();
          var searchString = `?&size=1000&from-date=${fDate}&to-date=${tDate}`;
        }
      }
      this.adapterService.getBudgetDossierListAggPage(searchString).subscribe(data => {
        if (data?.totalElements) {
          this.exportExcel.exportToExcelAggBudget(header1, header2, header3, header4, header5, reportTitle, data.content, name, 'Sheet1');
        } else {
          const msgObj = {
            vi: 'Không có dữ liệu để xuất ',
            en: 'Dont have data to export'
          };
          this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
        }
      }, err => {
        let msgObj = err.error.message || 'Lỗi không xác định';
        if (msgObj.includes('java.lang.NullPointerException')) {
          msgObj = {
            vi: 'Không tìm thấy hồ sơ',
            en: 'Dossier not found'
          };
          this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
          return;
        }
        this.snackbarService.openSnackBar(0, msgObj, '', 'error_notification', this.config.expiredTime);
      });
    }else{
      if(this.pagingType === 1){
        if (!formObj.fromdate || !formObj.todate) {
          this.snackbarService.openSnackBar(0, 'Ngày tra cứu không được để trống', '', 'error_notification', this.config.expiredTime);
          return;
        } else {
          if (tDate <= fDate) {
            this.snackbarService.openSnackBar(0, 'Ngày bắt đầu phải nhỏ hơn ngày kết thúc', '', 'error_notification', this.config.expiredTime);
            return;
          } else {
            var fDate = this.datepipe.transform(formObj.fromdate, 'yyyy-MM-dd').toString();
            var tDate = this.datepipe.transform(formObj.todate, 'yyyy-MM-dd').toString();
            var searchString = `?&size=1000&from-date=${fDate}&to-date=${tDate}`;
          }
        }
        this.padmanService.getAggDossierBudget(searchString).subscribe(data => {
          if (data?.totalElements) {
            this.exportExcel.exportToExcelAggBudget(header1, header2, header3, header4, header5, reportTitle, data.content, name, 'Sheet1');
          } else {
            const msgObj = {
              vi: 'Không có dữ liệu để xuất ',
              en: 'Dont have data to export'
            };
            this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
          }
        }, err => {
          let msgObj = err.error.message || 'Lỗi không xác định';
          if (msgObj.includes('java.lang.NullPointerException')) {
            msgObj = {
              vi: 'Không tìm thấy hồ sơ',
              en: 'Dossier not found'
            };
            this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
            return;
          }
          this.snackbarService.openSnackBar(0, msgObj, '', 'error_notification', this.config.expiredTime);
        });
      }else{
        const msgObj = {
          vi: 'Không có dữ liệu để xuất ',
          en: 'Dont have data to export'
        };
        this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
      }
    }
  }

  onDetail(hsid){
    var rowSel = this.dataSource.data.find(x => x.hsid === hsid);
    const dialogData = new DetailAggDossierBudgetComponentDialog(rowSel.hsid);
    const dialogRef = this.dialog.open(DetailAggDossierBudgetComponent, {
      width: '1000px',
      data: dialogData,
      disableClose: false,
      autoFocus: false
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
      // console.log(dialogResult);
    });
  }

  onDetailByHsid(){
    const formObj = this.searchForm.getRawValue();
    if (!formObj.hsid) {
      this.snackbarService.openSnackBar(0, 'Mã hồ sơ không được để trống', '', 'error_notification', this.config.expiredTime);
      return;
    }
    const dialogData = new DetailAggDossierBudgetComponentDialog(formObj.hsid);
    const dialogRef = this.dialog.open(DetailAggDossierBudgetComponent, {
      width: '1000px',
      data: dialogData,
      disableClose: false,
      autoFocus: false
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
      // console.log(dialogResult);
    });
  }

  updateDataSource() {
    const startIndex = this.pageIndex * this.size;
    const endIndex = startIndex + this.size;
    const slicedData = this.ELEMENTDATA.slice(startIndex, endIndex);
    this.dataSource = new MatTableDataSource<any>(slicedData);
  }

  onCertificate(ma){
    var rowSel = this.dataSource.data.find(x => x.ma === ma);
    const dialogData = new CertificateAggDossierBudgetComponentDialog(rowSel.ma);
    const dialogRef = this.dialog.open(CertificateAggDossierBudgetComponent, {
      width: '1000px',
      data: dialogData,
      disableClose: false,
      autoFocus: false
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
    });
  }
}
