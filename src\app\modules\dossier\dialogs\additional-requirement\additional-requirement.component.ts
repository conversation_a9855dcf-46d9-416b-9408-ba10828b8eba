import { HomeService } from './../../../../data/service/home/<USER>';
import { ProcessService } from 'src/app/data/service/process/process.service';
import { Component, OnInit, Inject, ViewChild } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA, MatDialog } from '@angular/material/dialog';
import { KeycloakService } from 'keycloak-angular';
import { DossierService } from 'src/app/data/service/dossier/dossier.service';
import { UserService } from 'src/app/data/service/user.service';
import * as ClassicEditor from '@ckeditor/ckeditor5-build-classic/build/ckeditor';
import '@ckeditor/ckeditor5-build-classic/build/translations/vi';
import { EnvService } from 'src/app/core/service/env.service';
import { SnackbarService } from 'src/app/data/service/snackbar/snackbar.service';
import { DatePipe } from '@angular/common';
import { NotificationService } from 'src/app/data/service/notification.service';
import { DeploymentService } from 'src/app/data/service/deployment.service';
import * as tUtils from 'src/app/data/service/thoai.service';
import { ProcedureService } from 'src/app/data/service/procedure/procedure.service';
import { take } from 'rxjs/operators';
import {NotifyQNIService} from "shared/components/check-send-notify-qni/check-send-notify-qni.service";
import { PadmanService } from 'src/app/data/service/svc-padman/padman.service';
import {FormControl, FormGroup} from '@angular/forms';
import { CheckSendNotifyComponent } from 'src/app/shared/components/check-send-notify/check-send-notify.component';
import { interval, Subject } from 'rxjs';
import { AdapterService } from 'src/app/data/service/adapter/adapter.service';
import { HumanService } from 'src/app/data/service/human/human.service';
import { SocialProtectionKTMService } from 'src/app/data/service/ktm-social-protection/social-protection-ktm.service';
import { SocialProtectionService } from 'src/app/data/service/social-protection/social-protection.service';
import { AgencyService } from 'src/app/data/service/basedata/agency.service';
import { ErrorComponent, ErrorDialogModel } from 'src/app/shared/components/dialogs/error/error.component';
import { Message, MessageCode } from 'src/app/shared/ts/message';
import {TrinamService} from 'modules/hbh/trinam.service';
import { LogmanService } from 'src/app/data/service/logman/logman.service';
import { FileService } from 'src/app/data/service/file.service';
import { UploadService } from 'src/app/data/service/upload/upload.service';
import { ConfigService } from 'src/app/data/service/dossier/config.service';
import { DigitalSignatureService } from 'src/app/data/service/digital-signature/digital-signature.service';
import { PdfViewerComponent, PdfViewerDialog } from 'src/app/shared/components/pdf-viewer/pdf-viewer.component';
import { FilemanService } from 'src/app/data/service/svc-fileman/fileman.service';
declare var vgca_sign_approved: any;

@Component({
  selector: 'app-additional-requirement',
  templateUrl: './additional-requirement.component.html',
  styleUrls: ['./additional-requirement.component.scss']
})
export class AdditionalRequirementComponent implements OnInit {
  @ViewChild(CheckSendNotifyComponent) checkSendNotifyComponent;
  config = this.envService.getConfig();
  env: any = this.deploymentService.getAppDeployment()?.env;
  selectedLang = localStorage.getItem('language') || 'vi';
  selectedLangId = Number(localStorage.getItem('languageId')) || 228;
  dossierId: string;
  procedureProcessDetail : any;
  commentContent = '';
  commentContentPlainText = '';
  descriptionContent = '';
  userName: string;
  accountId: string;
  dossierCode: string;
  isCKMaxlenght = false;
  isCKDescriptionMaxlenght = false;
  dossierDetail: any;
  oldstatus = '';
  officers = [];
  agencyId: any;
  getApprovalAgency = '';
  currentTask: any;
  codeMap='';
  attachFilePreview = [];
  resultFiles = [];
  resultFilePreview = [];
  apologyTextFiles = [];
  apologyTextFilePreview = [];
  isDisabled = false; //nhipttt-IGATESUPP-74286 disabled nút Yêu cầu bổ sung sau khi nhấn
  digitalSignatureEnable: boolean = false;
  digitalSignature = {
    SmartCA:false,
    VGCA:false,
    VNPTCA:false,
    VNPTSim:false,
    NEAC:false,
    QNM: false
  }
  isEditSignTokenName = false;
  digitalsignatureButton = this.deploymentService.env?.OS_HCM?.digitalsignatureButton ? this.deploymentService.env?.OS_HCM?.digitalsignatureButton : false;
  
  // IGATESUPP-89251: [iGate2.0][QNI] - Luồng xử lý hồ sơ dừng xử lý khi đang yêu cầu bổ sung - 26.06.2024
  enablePauseWhenAdditional = this.deploymentService.getAppDeployment()?.enablePauseWhenAdditional ? this.deploymentService.getAppDeployment()?.enablePauseWhenAdditional : false
  convertProcessingFile = this.deploymentService.getAppDeployment()?.convertProcessingFile;

  public Editor = ClassicEditor;
  commentConfig = {
    placeholder: 'Nhập lý do...',
    language: 'vi',
    removePlugins: ['MediaEmbed', 'ImageUpload'],
    toolbarLocation: 'bottom'
  };
  descriptionConfig = {
    placeholder: 'Nhập nội dung...',
    language: 'vi',
    removePlugins: ['MediaEmbed', 'ImageUpload'],
    toolbarLocation: 'bottom'
  };
  dossierTaskStatus = { id: '', name: [] };
  dossierMenuTaskRemind = { id: '', name: [] };
  filePDF = {
    id: "",
    filename: ""
  };
  tempObj : any = {};
  checkIsDocFile = false;
  // ================================================= Upload file
  uploaded: boolean;
  blankVal: any;
  uploadedImage = null;
  files = [];
  urls = [];
  fileNames = [];
  uploadFileNames = [];
  fileNamesFull = [];
  listProcedureProcess = [];

  isFieldArrayInvalid = false;
  acceptFileExtension = this.config.acceptFileExtension;
  acceptFileType = this.config.acceptFileType;
  maxFileSize = this.env?.maxFileSize || this.config.maxFileSize;
  //Thu tuc co cau hinh dong bo BXD-LGSP Tan Tan
  isSyncDBNLGSPTanDanBXD = 0;
  isHasCodeLDXH = false; //check có mã thủ tục LĐXH không
  statusName = "";
  fileTemplate = "";
  typeProcess = 1;

  notifyQNI = this.deploymentService.getAppDeployment()?.notifyqni?.enable;

  totalCost = '';

  ckeditorMaxLengthNotification = "";
  ckeditorMaxLength = this.deploymentService.env.OS_HCM.ckeditorMaxLength;
  checkRequireAdditionalRequest = this.env?.OS_BDG?.isRequiredUploadFileBDG?.additional == false ? false : true;
  numberDateAdditionalRequirement = this.deploymentService.env.OS_HCM.numberDateAdditionalRequirement;
  numberDate: number;
  checkNumberDate: boolean = false;
  notifyCheck;
  isBoTNMTQNM = this.env?.OS_QNM?.boTNMT === true ? this.env?.OS_QNM?.boTNMT : false;
  isBoTNMTHCM = this.deploymentService.env.OS_HCM.maTTBoTNMT;
  isBoTNMTGLI = this.deploymentService.env.OS_GLI.boTNMT;
  updateForm = new FormGroup({
    approvalAgencyId: new FormControl(''),
  });
  reason = new FormControl('');
  tittle = new FormControl('');
  approvalAgency = [];
  visibleApprovalAgency = this.deploymentService.env.visibleApprovalAgency;

  ktmDossierToSyncStatusBLDTBXH = null;
  ktmSyncToBLDTBXHConfigId = this.deploymentService.env.OS_KTM.syncToBLDTBXHConfigId;
  ktmEnableSyncToBLDTBXH = this.deploymentService.env.OS_KTM.enableSyncToBLDTBXH;
  enableSyncDossierTNMTQni = this.deploymentService.env?.OS_QNI?.enableSyncDossierTNMTQni ? this.deploymentService.env?.OS_QNI?.enableSyncDossierTNMTQni : false;
  integratedTnmtConfigIdQni = this.deploymentService.env?.OS_QNI?.integratedTnmtConfigIdQni || "66554990ed9f4f516a5b81c7";
  dossierToSyncStatusBLDTBXH = null;
  syncToBLDTBXHConfigId = this.deploymentService.env.OS_QNM.syncToBLDTBXHConfigId;
  enableSyncToBLDTBXH = this.deploymentService.env.OS_QNM.enableSyncToBLDTBXH;
  showExportFileBtn = this.deploymentService.env.OS_HCM.showExportFileBtn;
  enableShowExportFileBtn = false;
  listAgencyShowExportFileBtn = this.deploymentService.env.OS_HCM?.listAgencyShowExportFileBtn; 
  template = this.deploymentService.env.OS_HCM.template;
  requireAdditionalTemplate = "";
  isSyncConstructKTM=this.deploymentService.env?.OS_KTM?.isSyncConstructKTM;
  constructConfigId=this.deploymentService.env?.OS_KTM?.constructConfigId;

  listAgencyIdShowButtonGetFilesFromResult = this.deploymentService.env.OS_HCM.listAgencyIdShowButtonGetFilesFromResult;
  showButtonGetFilesFromResult = false;

  // IGATESUPP-44607
  allowNextStepWaitingForApproval = this.deploymentService.env.OS_HCM.allowNextStepWaitingForApproval ? this.deploymentService.env.OS_HCM.allowNextStepWaitingForApproval : false;
  enableApproveAdditionalRequestCheckBox = false;
  dossierTaskStatusWaitingForApproval = { id: '', name: [] };
  dossierMenuTaskRemindWaitingForApproval = { id: '', name: [] };
  requireAttachmentWhenAdditionalRequest = this.deploymentService.env.OS_HCM.requireAttachmentWhenAdditionalRequest == false ? this.deploymentService.env.OS_HCM.requireAttachmentWhenAdditionalRequest : true;
  
  dossierStatusId = null;
  dosierTaskStatusId = "";
  dossierMenuTaskRemindId = "";

  setOnlyApprovalAgency = this.deploymentService.env?.OS_QNI?.setOnlyApprovalAgency;
  isSmsQNM = this.env?.OS_QNM?.isSmsQNM ? true : false;

  //IGATESUPP-54548
  enableRequestAdditionalDossier = this.deploymentService?.env?.OS_QBH?.enableRequestAdditionalDossier ?? false;
  flagDossierDetailCompletedSubject =  new Subject<string>();

  //IGATESUPP-86102
  isDVCLTEnable = this.deploymentService.getAppDeployment()?.isDVCLTEnable;
  // isDVCLT = true;
  // IGATESUPP-89251
  additionRequestDueDate : any = new Date();
  additionRequestMinDate: Date;
  isDueDateChecked: boolean = false;

  //ket qua xu ly 
  //
  isUploadResultProcessing = this.deploymentService.getAppDeployment()?.isUploadResultProcessing ? this.deploymentService.getAppDeployment()?.isUploadResultProcessing : false;

  isSyncConstructHPG = this.deploymentService?.syncConstructHPG?.isSyncConstructHPG;
  constructor(
    private userService: UserService,
    private envService: EnvService,
    private keycloakService: KeycloakService,
    public dialogRef: MatDialogRef<AdditionalRequirementComponent>,
    @Inject(MAT_DIALOG_DATA) public data: ConfirmAdditionalRequirementDialogModel,
    private snackbarService: SnackbarService,
    private datePipe: DatePipe,
    private notiService: NotificationService,
    private deploymentService: DeploymentService,
    private procedureService: ProcedureService,
    private dossierService: DossierService,
    private processService: ProcessService,
    private homeService : HomeService,
    private notifyQNIService: NotifyQNIService,
    private padmanService: PadmanService,
    private humanService: HumanService,
    private socialProtectionKTMService: SocialProtectionKTMService,
    private socialProtectionService: SocialProtectionService,
    private adapterService: AdapterService,
    private agencyService: AgencyService,
    private dialog: MatDialog,
    private trinamService: TrinamService,
    private logmanService: LogmanService,
    private fileService: FileService,
    private uploadService: UploadService,
    private configService: ConfigService,
    private digitalSignatureService: DigitalSignatureService,
    private filemanService: FilemanService,
  ) {
    this.dossierId = data.dossierId;
    this.dossierCode = data.dossierCode;
    this.typeProcess = data.typeProcess;
    if (this.typeProcess === 2){
      this.env.enableApprovalOfLeadership = 2;
    }
    this.additionRequestMinDate = new Date();
  }

  ngOnInit(): void {
    this.getUserAccount();
    this.getDossierTaskStatus();
    this.getDossierMenuTaskRemind();
    this.getDetailDossier();
    this.setEnvVariable();
    const userAgency = JSON.parse(localStorage.getItem('userAgency'));
    if(this.listAgencyShowExportFileBtn.filter(item => item == userAgency?.id || item == userAgency?.parent?.id ).length > 0){
      this.enableShowExportFileBtn = true;
    }
    if (this.selectedLang === 'vi') {
      this.ckeditorMaxLengthNotification = "Nội dung không quá 500 ký tự!";
      if (this.ckeditorMaxLength > 0)
        this.ckeditorMaxLengthNotification = "Nội dung không quá "+ this.ckeditorMaxLength + " ký tự!";
    } else if (this.selectedLang === 'en') {
      this.ckeditorMaxLengthNotification = "Content must not exceed 500 characters!";
      if (this.ckeditorMaxLength > 0)
        this.ckeditorMaxLengthNotification = "Content must not exceed "+ this.ckeditorMaxLength + " characters!";
    }
    if (this.enableRequestAdditionalDossier){
      this.checkOverDue();
    }
      // Check if digital signature supported
      if(this.digitalsignatureButton){
        this.digitalSignatureEnable = true;
        this.digitalSignature = {
          SmartCA:false,
          VGCA:false,
          VNPTCA:false,
          VNPTSim:false,
          NEAC:false,
          QNM: false
        }
        this.digitalSignature.SmartCA = this.env?.digitalSignature?.SmartCA == 1;
        this.digitalSignature.VGCA = this.env?.digitalSignature?.VGCA == 1;
        this.digitalSignature.VNPTCA = this.env?.digitalSignature?.VNPTCA == 1;
        this.digitalSignature.VNPTSim = this.env?.digitalSignature?.VNPTSim == 1;
        this.digitalSignature.NEAC = this.env?.digitalSignature?.NEAC == 1;
        this.digitalSignature.QNM = this.env?.digitalSignature?.QNM == 1;
        this.isEditSignTokenName = this.env?.OS_KTM?.isEditSignTokenName == true ? true : false ;
      }
  }

  // async checkOverDueByDuration(currentDossierDetail) {

  //   const listTimesheet = [];
  //   let processProcessingTime = currentDossierDetail.procedureProcessDefinition.processDefinition.processingTime;

  //   if ( processProcessingTime !== undefined && processProcessingTime != 0 ) {
  //     let processingTime = 0;
  //     switch (currentDossierDetail.procedureProcessDefinition.processDefinition.processingTimeUnit) {
  //       case 'y':
  //         processingTime = processProcessingTime * 365;
  //         break;
  //       case 'M':
  //         processingTime = processProcessingTime * 30;
  //         break;
  //       case 'd':
  //         processingTime = processProcessingTime;
  //         break;
  //       case 'H:m:s':
  //         processingTime = processProcessingTime / 24;
  //         break;
  //       case 'h':
  //         processingTime = Number(processProcessingTime) / 24;
  //         break;
  //       case 'm':
  //         processingTime = Number(processProcessingTime) / (24 * 60);
  //         break;
  //     }
  //     console.log("currentDossierDetail.acceptedDAte",currentDossierDetail.acceptedDate);
  //     console.log("currentDossierDetail.processingTime",processingTime);
  //     console.log("currentDossierDetail.processingTime",currentDossierDetail.procedureProcessDefinition.processDefinition.processingTimeUnit);
  //     listTimesheet.push({
  //       timesheet: {
  //         id:
  //         currentDossierDetail.procedureProcessDefinition?.processDefinition
  //             ?.timesheet !== undefined
  //             ? currentDossierDetail.procedureProcessDefinition
  //               .processDefinition.timesheet.id
  //             : this.config.defaultTimesheetId,
  //       },
  //       dossier: {
  //         id: this.dossierId,
  //       },
  //       duration: processingTime,
  //       startDate: currentDossierDetail.acceptedDate,
  //       endDate: null,
  //       checkOffDay: true,
  //       offTime: true,
  //       processingTimeUnit: "d",
  //     });
  //   }

  async checkOverDue(){
    const currentDossierDetail = await this.dossierService.getDossierDetail(this.dossierId).toPromise();
    if (this.dossierService.checkOverDue(currentDossierDetail)) {
      const message = Message.getMessage(MessageCode.EXTEND_TIME);
      this.dossierService.showWarningDialog(message,this.dialogRef,);
    }
  }


  setEnvVariable(){
    this.fileTemplate = !!this.env?.fileTemplate?.requireAdditional ? this.env?.fileTemplate?.requireAdditional : this.config.requireAdditionalTemplateFile;
    if(this.deploymentService.env?.OS_HCM?.allowShowInfoDossierWhenExportFileHCM?.enable == true){
      this.requireAdditionalTemplate = this.deploymentService.env?.OS_HCM?.allowShowInfoDossierWhenExportFileHCM?.templateHCM?.requireAdditionalTemplate ? this.deploymentService.env?.OS_HCM?.allowShowInfoDossierWhenExportFileHCM?.templateHCM?.requireAdditionalTemplate : "";
    } else {
      this.requireAdditionalTemplate = this.template?.requireAdditionalTemplate ? this.template?.requireAdditionalTemplate : "";
    }
  }

  getUserAccount() {
    this.keycloakService.loadUserProfile().then(user => {
      // tslint:disable-next-line: no-string-literal
      this.accountId = user['attributes'].user_id[0];
      this.userService.getUserInfo(this.accountId).subscribe(data => {
        this.userName = data.fullname;
      });
    });
  }
  getDossierTaskStatus() {
    const tagId = this.env?.enableApprovalOfLeadership == 1 ? this.deploymentService.env.dossierTaskStatus.waitingForApprovalToAddition.id : this.deploymentService.env.dossierTaskStatus.requestForAdditionalDocuments.id;
    this.dossierService.getAgencyTag(tagId).subscribe(rs => {
      this.dossierTaskStatus.id = rs.id;
      this.dossierTaskStatus.name = rs.trans;
    }, err => {
      console.log(err);
    });
  }
  getDossierMenuTaskRemind() {
    const tagId = this.env?.enableApprovalOfLeadership == 1 ? this.deploymentService.env.dossierMenuTaskRemind.waitingForApprovalToAddition.id : this.deploymentService.env.dossierMenuTaskRemind.requestForAdditional.id;
    this.dossierService.getAgencyTag(tagId).subscribe(rs => {
      this.dossierMenuTaskRemind.id = rs.id;
      this.dossierMenuTaskRemind.name = rs.trans;
      this.statusName = rs?.trans.filter(t => t.languageId === Number(this.selectedLangId))?.name;
    }, err => {
      console.log(err);
    });
  }

  async getDetailDossier() {
    await this.getDossierFeeNotify();
    this.dossierService.getDossierDetail(this.dossierId).subscribe(async data => {
      this.dossierDetail = data;
      if(this.listAgencyIdShowButtonGetFilesFromResult?.length > 0){
        let listFile = this.dossierDetail?.attachment?.filter(d => d.group == this.config.dossierResultFileTagId);
        let userAgency = JSON.parse(localStorage.getItem('userAgency'));
        if(listFile?.length > 0 && (this.listAgencyIdShowButtonGetFilesFromResult.includes(userAgency?.id) || 
            this.listAgencyIdShowButtonGetFilesFromResult.includes(userAgency?.parent?.id))){
          this.showButtonGetFilesFromResult = true;
        }
      }
      this.getProcedureDetail(data?.procedure?.id);
      this.agencyId = !!data?.agency?.id ? data?.agency?.id : data?.agency?.parent?.id;
      if (!!data.currentTask && data.currentTask.length > 0){
        this.currentTask = data.currentTask[0];
        await this.getApprovaledAgency(this.currentTask.candidateGroup[0]);
      }else{
        this.visibleApprovalAgency = 0; // hs mới đăng ký tắt tham số chọn cơ quan thực hiện
      }
      if(!!this.notifyQNI){
        this.notifyQNIService.checkSendSubject.next(
          {
            id: data?.procedureProcessDefinition?.id,
            phone: data?.applicant?.data?.phoneNumber,
            email: data?.applicant?.data?.email,
            currentTask: this.currentTask,
            contentParams: {
              parameters: {
                sector: !!data?.procedure?.sector?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name ? data?.procedure?.sector?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name : '',
                procedure: !!data?.procedure?.translate?.find(agency => agency.languageId === Number(this.selectedLangId))?.name ? data?.procedure?.translate?.find(agency => agency.languageId === Number(this.selectedLangId))?.name : '',
                agency: !!data?.agency?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name ? data?.agency?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name : '',
                agencyId: !!data?.agency?.id ? data?.agency?.id : data?.agency?.parent?.id,
                applicantFullname: data?.applicant?.data?.fullname,
                officerFullname: '',
                subsystem: !!this.env?.subsystem?.name ? this.env?.subsystem?.name : "Hệ thống Một cửa điện tử",
                dossierCode: data?.code,
                nextTask: !!this.env?.notify?.additionalRequirement?.nextTask ? this.env?.notify?.additionalRequirement?.nextTask : 'Yêu cầu bổ sung hồ sơ',
                dossierSearchPadsvcUrl: (!!this.env?.domain?.padsvc ? this.env?.domain?.padsvc : "http://padsvctest.vncitizens.vn/vi/") + "dossier/public",
                dossierDetailPadsvcUrl: (!!this.env?.domain?.padsvc ? this.env?.domain?.padsvc : "http://padsvctest.vncitizens.vn/vi/") + "dossier/detail/" + this.dossierId,
                dossierSearchUrl: (!!this.env?.domain?.onegate ? this.env?.domain?.onegate : "http://padsvctest.vncitizens.vn/vi/") + "dossier/search/",
                dossierDetailUrl: (!!this.env?.domain?.onegate ? this.env?.domain?.onegate : "http://padsvctest.vncitizens.vn/vi/") + "dossier/search/" + this.dossierId,
                returnMethod: !!data?.dossierReceivingKind?.name?.find(recieve => recieve.languageId === Number(this.selectedLangId))?.name ? data?.dossierReceivingKind?.name?.find(recieve => recieve.languageId === Number(this.selectedLangId))?.name : '',
                receivingDate: '',
                appointmentDate: '',
                dossierFee: this.totalCost,
                reason: this.commentContent
              }
            }
          }
        );
      } else {
        this.notiService.checkSendSubject.next(
          {
            id: data?.procedureProcessDefinition?.id,
            phone: data?.applicant?.data?.phoneNumber,
            email: data?.applicant?.data?.email,
            currentTask: this.currentTask,
            contentParams: {
              parameters: {
                sector: !!data?.procedure?.sector?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name ? data?.procedure?.sector?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name : '',
                procedure: !!data?.procedure?.translate?.find(agency => agency.languageId === Number(this.selectedLangId))?.name ? data?.procedure?.translate?.find(agency => agency.languageId === Number(this.selectedLangId))?.name : '',
                agency: !!data?.agency?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name ? data?.agency?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name : data?.agency?.parent?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name,
                agencyId: !!data?.agency?.id ? data?.agency?.id : data?.agency?.parent?.id,
                applicantFullname: data?.applicant?.data?.fullname,
                officerFullname: '',
                subsystem: !!this.env?.subsystem?.name ? this.env?.subsystem?.name : "Hệ thống Một cửa điện tử",
                dossierCode: data?.nationCode ? data?.nationCode : data?.code,
                nextTask: !!this.env?.notify?.additionalRequirement?.nextTask ? this.env?.notify?.additionalRequirement?.nextTask : 'Yêu cầu bổ sung hồ sơ',
                // IGATESUPP-63184
                nextStatus: !!this.env?.notify?.additionalRequirement?.nextStatus ? this.env?.notify?.additionalRequirement?.nextStatus : (data?.dossierTaskStatus?.name ?? ''), // cho bo sung, trang thai hien tai cua ho so
                dossierStatus: !!this.env?.notify?.additionalRequirement?.dossierStatus ? this.env?.notify?.additionalRequirement?.dossierStatus : (data?.dossierStatus?.name?.find(t => t.languageId === Number(this.selectedLangId))?.name ?? ''), // cho bo sung, trang thai hien tai cua ho so
                dossierSearchPadsvcUrl: (!!this.env?.domain?.padsvc ? this.env?.domain?.padsvc : "http://padsvctest.vncitizens.vn/vi/") + "dossier/public",
                dossierDetailPadsvcUrl: (!!this.env?.domain?.padsvc ? this.env?.domain?.padsvc : "http://padsvctest.vncitizens.vn/vi/") + "dossier/detail/" + this.dossierId,
                dossierSearchUrl: (!!this.env?.domain?.onegate ? this.env?.domain?.onegate : "http://padsvctest.vncitizens.vn/vi/") + "dossier/search/",
                dossierDetailUrl: (!!this.env?.domain?.onegate ? this.env?.domain?.onegate : "http://padsvctest.vncitizens.vn/vi/") + "dossier/search/" + this.dossierId,
                returnMethod: !!data?.dossierReceivingKind?.name?.find(recieve => recieve.languageId === Number(this.selectedLangId))?.name ? data?.dossierReceivingKind?.name?.find(recieve => recieve.languageId === Number(this.selectedLangId))?.name : '',
                receivingDate: '',
                appointmentDate: '',
                dossierFee: this.totalCost,
                reason: this.commentContent
              }
            }
          }
        );
      }
      if (this.dossierDetail.dossierTaskStatus === undefined && this.dossierDetail.dossierTaskStatus === null) {
        // this.putDossierStatus();
      }
      else {
        this.oldstatus = this.dossierDetail.dossierTaskStatus.name;
      }
    });
    // this.flagDossierDetailCompletedSubject.next(); 
  }

  async getNotify(){
    let data = this.dossierDetail;
    if(!!this.notifyQNI){
      await this.notifyQNIService.checkSendSubject.next(
        {
          id: data?.procedureProcessDefinition?.id,
          phone: data?.applicant?.data?.phoneNumber,
          email: data?.applicant?.data?.email,
          currentTask: this.currentTask,
          contentParams: {
            parameters: {
              sector: !!data?.procedure?.sector?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name ? data?.procedure?.sector?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name : '',
              procedure: !!data?.procedure?.translate?.find(agency => agency.languageId === Number(this.selectedLangId))?.name ? data?.procedure?.translate?.find(agency => agency.languageId === Number(this.selectedLangId))?.name : '',
              agency: !!data?.agency?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name ? data?.agency?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name : '',
              agencyId: !!data?.agency?.id ? data?.agency?.id : data?.agency?.parent?.id,
              applicantFullname: data?.applicant?.data?.fullname,
              officerFullname: '',
              subsystem: !!this.env?.subsystem?.name ? this.env?.subsystem?.name : "Hệ thống Một cửa điện tử",
              dossierCode: data?.code,
              nextTask: !!this.env?.notify?.additionalRequirement?.nextTask ? this.env?.notify?.additionalRequirement?.nextTask : 'Yêu cầu bổ sung hồ sơ',
              dossierSearchPadsvcUrl: (!!this.env?.domain?.padsvc ? this.env?.domain?.padsvc : "http://padsvctest.vncitizens.vn/vi/") + "dossier/public",
              dossierDetailPadsvcUrl: (!!this.env?.domain?.padsvc ? this.env?.domain?.padsvc : "http://padsvctest.vncitizens.vn/vi/") + "dossier/detail/" + this.dossierId,
              dossierSearchUrl: (!!this.env?.domain?.onegate ? this.env?.domain?.onegate : "http://padsvctest.vncitizens.vn/vi/") + "dossier/search/",
              dossierDetailUrl: (!!this.env?.domain?.onegate ? this.env?.domain?.onegate : "http://padsvctest.vncitizens.vn/vi/") + "dossier/search/" + this.dossierId,
              returnMethod: !!data?.dossierReceivingKind?.name?.find(recieve => recieve.languageId === Number(this.selectedLangId))?.name ? data?.dossierReceivingKind?.name?.find(recieve => recieve.languageId === Number(this.selectedLangId))?.name : '',
              receivingDate: '',
              appointmentDate: '',
              dossierFee: this.totalCost,
              reason: this.commentContentPlainText,
              numberDate: this.getNumberDate()
            }
          }
        }
      );
    } else {
      await this.notiService.checkSendSubject.next(
        {
          id: data?.procedureProcessDefinition?.id,
          phone: data?.applicant?.data?.phoneNumber,
          email: data?.applicant?.data?.email,
          currentTask: this.currentTask,
          contentParams: {
            parameters: {
              sector: !!data?.procedure?.sector?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name ? data?.procedure?.sector?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name : '',
              procedure: !!data?.procedure?.translate?.find(agency => agency.languageId === Number(this.selectedLangId))?.name ? data?.procedure?.translate?.find(agency => agency.languageId === Number(this.selectedLangId))?.name : '',
              agency: !!data?.agency?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name ? data?.agency?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name : data?.agency?.parent?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name,
              agencyId: !!data?.agency?.id ? data?.agency?.id : data?.agency?.parent?.id,
              applicantFullname: data?.applicant?.data?.fullname,
              officerFullname: '',
              subsystem: !!this.env?.subsystem?.name ? this.env?.subsystem?.name : "Hệ thống Một cửa điện tử",
              dossierCode: data?.code,
              nextTask: !!this.env?.notify?.additionalRequirement?.nextTask ? this.env?.notify?.additionalRequirement?.nextTask : 'Yêu cầu bổ sung hồ sơ',
              // IGATESUPP-63184
              nextStatus: !!this.env?.notify?.additionalRequirement?.nextStatus ? this.env?.notify?.additionalRequirement?.nextStatus : (data?.dossierTaskStatus?.name ?? ''), // cho bo sung, trang thai hien tai cua ho so
              dossierStatus: !!this.env?.notify?.additionalRequirement?.dossierStatus ? this.env?.notify?.additionalRequirement?.dossierStatus : (data?.dossierStatus?.name?.find(t => t.languageId === Number(this.selectedLangId))?.name ?? ''), // cho bo sung, trang thai hien tai cua ho so
              dossierSearchPadsvcUrl: (!!this.env?.domain?.padsvc ? this.env?.domain?.padsvc : "http://padsvctest.vncitizens.vn/vi/") + "dossier/public",
              dossierDetailPadsvcUrl: (!!this.env?.domain?.padsvc ? this.env?.domain?.padsvc : "http://padsvctest.vncitizens.vn/vi/") + "dossier/detail/" + this.dossierId,
              dossierSearchUrl: (!!this.env?.domain?.onegate ? this.env?.domain?.onegate : "http://padsvctest.vncitizens.vn/vi/") + "dossier/search/",
              dossierDetailUrl: (!!this.env?.domain?.onegate ? this.env?.domain?.onegate : "http://padsvctest.vncitizens.vn/vi/") + "dossier/search/" + this.dossierId,
              returnMethod: !!data?.dossierReceivingKind?.name?.find(recieve => recieve.languageId === Number(this.selectedLangId))?.name ? data?.dossierReceivingKind?.name?.find(recieve => recieve.languageId === Number(this.selectedLangId))?.name : '',
              receivingDate: '',
              appointmentDate: '',
              dossierFee: this.totalCost,
              reason: this.commentContentPlainText,
              numberDate: this.getNumberDate()
            }
          }
        }
      );
    }
    this.checkSendNotifyComponent.notify = this.notifyCheck;
    this.checkSendNotifyComponent.onInit();
  }

  getNumberDate()
  {
    let numberDate = '';
    if(this.numberDateAdditionalRequirement && this.numberDateAdditionalRequirement.enable){
      if(this.checkNumberDate && this.numberDate != undefined && this.numberDate != null && this.numberDate >= 0){
        let current = new Date();
        current.setHours(0, 0, 0, 0);
        const end = new Date(current);
        end.setDate(current.getDate() + this.numberDate);
        numberDate = this.datePipe.transform(end, 'dd/MM/yyyy');
      }
    }
    return numberDate;
  }

  getDateRequire()
  {
    if(this.numberDateAdditionalRequirement && this.numberDateAdditionalRequirement.enable){
      if(this.checkNumberDate && this.numberDate != undefined && this.numberDate != null && this.numberDate >= 0){
        let current = new Date();
        current.setHours(0, 0, 0, 0);
        const end = new Date(current);
        end.setDate(current.getDate() + this.numberDate);
        end.setHours(0, 0, 0, 0);
        return this.datePipe.transform(end, 'yyyy-MM-ddT00:00:00');
      }
    }
    return null;
  }

  getDossierFeeNotify() {
    return new Promise<void>(resolve => {
      this.padmanService.getDossierFee(this.dossierId).toPromise().then(res => {
        if(res && res.length > 0)
        {
          let totalCostValue = 0;
          let totalPaidValue = 0;
          res.forEach(element => {
            totalCostValue += element.quantity * element.amount;
            totalPaidValue += element.paid;
          });
          const monetaryUnit = res[0]?.monetaryUnit ? res[0].monetaryUnit : 'VNĐ';
          let total = totalCostValue - totalPaidValue;
          this.totalCost = total > 0 ? Number((total).toFixed(1)).toLocaleString() + ' ' + monetaryUnit : '';
        }
        resolve();
      }).catch(err => {
        resolve();
      });
    });
  }

  getProcedureDetail(procedureId) {
    this.procedureService.getProcedureDetail(procedureId).subscribe(async data => {
      this.isSyncDBNLGSPTanDanBXD = data?.isSyncDBNLGSPTanDanBXD ? Number(data?.isSyncDBNLGSPTanDanBXD) : 0;
      this.isHasCodeLDXH = !!data.btxhcode;
      this.enableApproveAdditionalRequestCheckBox = data?.extendHCM?.enableApproveAdditionalRequestCheckBox ? data.extendHCM?.enableApproveAdditionalRequestCheckBox : false;
      if(tUtils.nonNull(this.dossierDetail?.extendHCM, "officerDossierQualifiedRecept")){
        if(this.dossierDetail?.extendHCM?.officerDossierQualifiedRecept.length > 0){
          if(data?.extendHCM?.enableShowDossierQualifiedRecept){
            let listReason =[];
            this.dossierDetail?.extendHCM?.officerDossierQualifiedRecept.forEach(element => {
              if(element.result == 1){
                listReason.push(element.reason);
              }
            });
            this.reason.setValue(listReason.toString());
            this.tittle.setValue(listReason.toString());
          }
        }
      }
    }, err => {
      console.log(err);
    });
  }
  putDossierStatus() {
    const requestBodyObj = [
      {
        id: this.dossierId,
        dossierStatus: 2,
        dossierTaskStatus: null
      }
    ];
    const requestBody = JSON.stringify(requestBodyObj, null, 2);
    this.dossierService.putDossierStatus(requestBody).subscribe(data => {
      if (data.affectedRows === 1) {
        this.getDetailDossier();
      }
    });
  }

  postHistory() {
    let newStatus = '';
    if (this.dossierTaskStatus.name.length > 0) {
      newStatus = this.dossierTaskStatus.name[0].name;
      this.dossierTaskStatus.name.forEach(element => {
        if (element.languageId === Number(localStorage.getItem('languageId'))) {
          newStatus = element.name;
        }
      });
    }
    let description = this.descriptionContent.trim();
    let commentContent = this.commentContent.trim();
    
    let msgVi='Cán bộ: ' + this.userName ;
    let msgEn = 'Officier: ' + this.userName;
    if(this.checkNumberDate && this.numberDate != undefined && this.numberDate != null && this.numberDate >= 0){
      msgVi += '<br>Số ngày chờ bổ sung: ' + this.numberDate + ' ngày';
      msgEn += '<br>additional waiting days: ' + this.numberDate + 'date';
    } else {
      msgVi += '<br>Số ngày chờ bổ sung: Không xác định';
      msgEn += '<br>additional waiting days: undefined';
    }
    if(!!description){
      msgVi += `<br>Yêu cầu bổ sung: ${commentContent}<br>Nội dung: ${description}`,
      msgEn += `<br>Additional requirement: ${commentContent}<br>Description: ${description}`
     
    }else{
      msgVi += `<br>Yêu cầu bổ sung: ${commentContent}`,
      msgEn += `<br>Additional requirement: ${commentContent}`
     
    }
    let msgObj = {
      vi: msgVi,
      en: msgEn
    }
    const content = {
      groupId: 1,
      itemId: this.dossierId,
      user: {
        id: this.accountId,
        name: this.userName
      },
      type: 3,
      deploymentId: this.config.deploymentId,
      action: [
        {
          fieldNameRbk: 'lang.word.status',
          originalValue: this.oldstatus,
          newValue: newStatus,
          additionalRequire : {
            dateAdditionalRequire : msgObj[this.selectedLang],
            file: this.uploadFileNames
          }
        }
      ]
    };
    this.dossierService.postHistory(JSON.stringify(content, null, 2)).subscribe(data => {

    });
  }

  // IGATESUPP-51500 - Đánh giá lại chức năng phê duyệt hồ sơ - 19.06.2023
  postOnlyApprovalAgencyOfDossier(code) {

    const formObj = this.updateForm.getRawValue();
    if (!!formObj.approvalAgencyId && formObj.approvalAgencyId != '') {
      this.getApprovalAgency = formObj.approvalAgencyId;
    }else{
      return;
    }

    const body = {
      id: this.getApprovalAgency
    };
    const requestBody = JSON.stringify(body, null, 2);
    this.dossierService.postOnlyApprovalAgencyOfDossier(requestBody, code).toPromise();
  }

  GetListUserByPermission(agency) {
    return new Promise<void>((resolve) => {
      this.processService.getApprovalAgency(agency.id).pipe(take(1)).subscribe(async data => {
        console.log('data', data);
        if (!!data && data.content.length !== 0) {
          this.getApprovalAgency = data.content[0].approvalAgency.id;
        }
        else{
          if (!!agency.parent?.id){
            await this.GetListUserByPermissionParent(agency.parent?.id);
          }
        }
        if (this.getApprovalAgency == '') {
          this.getApprovalAgency = agency.id;
        }
        const formObj = this.updateForm.getRawValue();
        if (!!formObj.approvalAgencyId && formObj.approvalAgencyId != '') {
          this.getApprovalAgency = formObj.approvalAgencyId;
        }
        let permission = "additionalRequirementDossierApproval";
        this.processService.getListUserByPermissionCode(this.getApprovalAgency, permission).pipe(take(1)).subscribe(data2 => {
          this.officers = data2;
          resolve();
        }, (err) => {
          resolve();
        });
      });
    });
  }

  GetListUserByPermissionParent(agencyId) {
    return new Promise<void>((resolve) => {
      this.processService.getApprovalAgency(agencyId).pipe(take(1)).subscribe(data => {
        console.log('data', data);
        if (!!data && data.content.length !== 0) {
          this.getApprovalAgency = data.content[0].approvalAgency.id;
        }
        // if (this.getApprovalAgency == '') {
        //   this.getApprovalAgency = agencyId;
        // }
        resolve();
      }, (err) => {
        resolve();
      });
    });
  }

  // async onConfirm(){
  //   console.log("check date", this.additionRequestDueDate)
  //   console.log("check date", this.isDueDateChecked)
  // }


  async onConfirm() {
    this.isDisabled = true;
    console.log("this.uploadFileNames",this.uploadFileNames);
    // xóa dữ liệu file tạm
    // this.uploadFileNames.forEach(element => {
    //   this.procedureService.deleteFile(element.id).subscribe(res => {
    //   }, err => {
    //     console.log(err);
    //   });
    // });
    // this.uploadFileNames = [];
    //
    if (!!this.currentTask && !!this.currentTask.candidateGroup[0] && !!this.currentTask.candidateGroup[0].id){
      await this.GetListUserByPermission(this.currentTask.candidateGroup[0]);
    }else{
      await this.GetListUserByPermission({id: this.agencyId});
    }
    if (this.env?.enableApprovalOfLeadership != 2 && this.officers.length == 0) {
      this.isDisabled = false; 
      this.homeService.error('Cán bộ phê duyệt hồ sơ của đơn vị chưa được cấu hình. \r\n Vui lòng cấu hình cán bộ phê duyệt để tiếp tục!',
        'Approval officer has not yet been configured \r\n Please configure the approval officer to continue!');
      const msgObj = {
        vi: 'Cán bộ phê duyệt hồ sơ của đơn vị chưa được cấu hình',
        en: 'Approval officer has not yet been configured'
      };
      this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
      return
    }

    if (this.visibleApprovalAgency && (!this.getApprovalAgency || this.getApprovalAgency === '')) {
      this.isDisabled = false; 
      const msgObj = {
        vi: 'Đơn vị phê duyệt hồ sơ chưa được chọn!',
        en: 'The unit that approves the application has not been selected!'
      };
      this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
      return
    }

    let checkFile: any = false;
    if(this.requireAttachmentWhenAdditionalRequest) {
      if (this.uploadFileNames.length > 0) {
        // checkFile = await this.uploadMultiFile(this.files, this.accountId);
        checkFile = true;
        if (!checkFile) {
          this.isDisabled = false; 
          const msgObj = {
            vi: 'Upload file thất bại, vui lòng thử lại!',
            en: 'File upload failed, please try again!'
          };
          this.snackbarService.openSnackBar(2, '', msgObj[this.selectedLang], 'warning_notification', this.config.expiredTime);
        }
      } else if (this.checkRequireAdditionalRequest) {
        this.isDisabled = false; 
        const msgObj = {
          vi: 'Vui lòng chọn tệp văn bản',
          en: 'Please select the required text file!'
        };
        this.snackbarService.openSnackBar(2, '', msgObj[this.selectedLang], 'warning_notification', this.config.expiredTime);
      } else {
        checkFile = true;
      }
    } else {
        checkFile = true;
    }
    if (this.commentContent.trim() === '') {
      this.isDisabled = false; 
      const msgObj = {
        vi: 'Vui lòng nhập ý kiến xử lý!',
        en: 'Please enter a handling comments!'
      };
      this.snackbarService.openSnackBar(2, '', msgObj[this.selectedLang], 'warning_notification', this.config.expiredTime);
    }
    if (this.descriptionContent.trim() === '') {
      this.isDisabled = false; 
      const msgObj = {
        vi: 'Vui lòng nhập nội dung yêu cầu bổ sung!',
        en: 'Please enter additional request content!'
      };
      this.snackbarService.openSnackBar(2, '', msgObj[this.selectedLang], 'warning_notification', this.config.expiredTime);
    }
    if (this.isCKMaxlenght || this.isCKDescriptionMaxlenght) {
      let msgObj;
      if (this.ckeditorMaxLength > 0) {
        msgObj = {
          vi: 'Nội dung không quá ' + this.ckeditorMaxLength + ' ký tự!',
          en: 'Content must not exceed ' + this.ckeditorMaxLength + ' characters!'
        };
      } else {
        msgObj = {
          vi: 'Nội dung không quá 500 ký tự!',
          en: 'Content must not exceed 500 characters!'
        };
      }
      this.isDisabled = false; 
      this.snackbarService.openSnackBar(2, '', msgObj[this.selectedLang], 'warning_notification', this.config.expiredTime);
    }
    if(this.numberDateAdditionalRequirement && this.numberDateAdditionalRequirement.enable){
      //Kiểm tra nếu check mà không nhập số ngày hẹn
      if(this.checkNumberDate && (this.numberDate == undefined || this.numberDate == null || this.numberDate < 0)){
        this.isDisabled = false; 
        const msgObj = {
          vi: 'Vui lòng nhập số ngày chờ bổ sung!',
          en: 'Please enter the number of additional waiting day!'
        };
        this.snackbarService.openSnackBar(2, '', msgObj[this.selectedLang], 'warning_notification', this.config.expiredTime);
        return;
      }
    }
     if(this.convertProcessingFile){
      await this.convertToPDF();     
    }
    //KGG_OS 
    if(this.isUploadResultProcessing){
      //upload file kết quả xử lý
      await this.putDossierAttachmentMulti("", 3, 'lang.word.result', '', '', this.uploadFileNames)
    }

    if (!this.isCKMaxlenght && this.commentContent.trim() !== '' && !this.isCKDescriptionMaxlenght && this.descriptionContent.trim() !== '' && checkFile) {
      const requestBodyObj = {
        dossierStatus: this.env?.enableApprovalOfLeadership == 1 ? 8 : 1,
        comment: '',
        description: '',
        dossierTaskStatus: this.dossierTaskStatus,
        dossierMenuTaskRemind: this.dossierMenuTaskRemind,
        numberDateRequire: null,
        dateRequire: null,
        isAttachmentRequired: this.requireAttachmentWhenAdditionalRequest
      };
      if (this.commentContent.trim() !== '') {
        this.postComment(this.commentContent.trim(), this.descriptionContent.trim());
        requestBodyObj.comment = this.commentContent.trim();
        requestBodyObj.description = this.descriptionContent.trim();
      } else {
        const msgObj = {
          vi: 'Yêu cầu bổ sung hồ sơ <b>' + this.dossierCode + '</b>',
          en: 'Additional requirement with dossier <b>' + this.dossierCode + '</b>!'
        };
        this.postComment(msgObj[this.selectedLang]);
        requestBodyObj.comment = msgObj[this.selectedLang];
      }

      const agencies = this.agencyService.getAgencies();
      const extend = {
        dossier:{
          id: this.dossierId,
          code: this.dossierCode,
        },
        agencies: agencies
      };

      if(this.isSmsQNM) {
        await this.notiService.changeSendSubject.next(
          {
            id: this.dossierDetail?.procedureProcessDefinition?.id,
            phone: this.dossierDetail?.applicant?.data?.phoneNumber,
            email: this.dossierDetail?.applicant?.data?.email,
            updateSend: true,
            renewContent: true,
            currentTask: this.currentTask,
            contentParams: {
              parameters: {
                sector: !!this.dossierDetail?.procedure?.sector?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name ? this.dossierDetail?.procedure?.sector?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name : '',
                procedure: !!this.dossierDetail?.procedure?.translate?.find(agency => agency.languageId === Number(this.selectedLangId))?.name ? this.dossierDetail?.procedure?.translate?.find(agency => agency.languageId === Number(this.selectedLangId))?.name : '',
                agency: !!this.dossierDetail?.agency?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name ? this.dossierDetail?.agency?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name : this.dossierDetail?.agency?.parent?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name,
                agencyId: !!this.dossierDetail?.agency?.id ? this.dossierDetail?.agency?.id : this.dossierDetail?.agency?.parent?.id,
                applicantFullname: this.dossierDetail?.applicant?.data?.fullname,
                officerFullname: '',
                subsystem: !!this.env?.subsystem?.name ? this.env?.subsystem?.name : "Hệ thống Một cửa điện tử",
                dossierCode: this.dossierDetail?.nationCode ? this.dossierDetail?.nationCode : this.dossierDetail?.code,
                nextTask: !!this.env?.notify?.additionalRequirement?.nextTask ? this.env?.notify?.additionalRequirement?.nextTask : 'Yêu cầu bổ sung hồ sơ',
                dossierSearchPadsvcUrl: (!!this.env?.domain?.padsvc ? this.env?.domain?.padsvc : "http://padsvctest.vncitizens.vn/vi/") + "dossier/public",
                dossierDetailPadsvcUrl: (!!this.env?.domain?.padsvc ? this.env?.domain?.padsvc : "http://padsvctest.vncitizens.vn/vi/") + "dossier/detail/" + this.dossierId,
                dossierSearchUrl: (!!this.env?.domain?.onegate ? this.env?.domain?.onegate : "http://padsvctest.vncitizens.vn/vi/") + "dossier/search/",
                dossierDetailUrl: (!!this.env?.domain?.onegate ? this.env?.domain?.onegate : "http://padsvctest.vncitizens.vn/vi/") + "dossier/search/" + this.dossierId,
                returnMethod: !!this.dossierDetail?.dossierReceivingKind?.name?.find(recieve => recieve.languageId === Number(this.selectedLangId))?.name ? this.dossierDetail?.dossierReceivingKind?.name?.find(recieve => recieve.languageId === Number(this.selectedLangId))?.name : '',
                receivingDate: '',
                appointmentDate: '',
                dossierFee: this.totalCost,
                reason: this.commentContentPlainText,
                numberDate: this.getNumberDate(),
                extend: extend
              }
            }
          }
        );
      }
      else {
        await this.notiService.changeSendSubject.next(
          {
            id: this.dossierDetail?.procedureProcessDefinition?.id,
            phone: this.dossierDetail?.applicant?.data?.phoneNumber,
            email: this.dossierDetail?.applicant?.data?.email,
            updateSend: false,
            renewContent: true,
            currentTask: this.currentTask,
            contentParams: {
              parameters: {
                sector: !!this.dossierDetail?.procedure?.sector?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name ? this.dossierDetail?.procedure?.sector?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name : '',
                procedure: !!this.dossierDetail?.procedure?.translate?.find(agency => agency.languageId === Number(this.selectedLangId))?.name ? this.dossierDetail?.procedure?.translate?.find(agency => agency.languageId === Number(this.selectedLangId))?.name : '',
                agency: !!this.dossierDetail?.agency?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name ? this.dossierDetail?.agency?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name : this.dossierDetail?.agency?.parent?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name,
                agencyId: !!this.dossierDetail?.agency?.id ? this.dossierDetail?.agency?.id : this.dossierDetail?.agency?.parent?.id,
                applicantFullname: this.dossierDetail?.applicant?.data?.fullname,
                officerFullname: '',
                subsystem: !!this.env?.subsystem?.name ? this.env?.subsystem?.name : "Hệ thống Một cửa điện tử",
                dossierCode: this.dossierDetail?.nationCode ? this.dossierDetail?.nationCode : this.dossierDetail?.code,
                nextTask: !!this.env?.notify?.additionalRequirement?.nextTask ? this.env?.notify?.additionalRequirement?.nextTask : 'Yêu cầu bổ sung hồ sơ',
                // IGATESUPP-63184
                nextStatus: !!this.env?.notify?.additionalRequirement?.nextStatus ? this.env?.notify?.additionalRequirement?.nextStatus : (this.dossierDetail?.dossierTaskStatus?.name ?? ''), // cho bo sung, trang thai hien tai cua ho so
                dossierStatus: !!this.env?.notify?.additionalRequirement?.dossierStatus ? this.env?.notify?.additionalRequirement?.dossierStatus : (this.dossierDetail?.dossierStatus?.name?.find(t => t.languageId === Number(this.selectedLangId))?.name ?? ''), // cho bo sung, trang thai hien tai cua ho so
                dossierSearchPadsvcUrl: (!!this.env?.domain?.padsvc ? this.env?.domain?.padsvc : "http://padsvctest.vncitizens.vn/vi/") + "dossier/public",
                dossierDetailPadsvcUrl: (!!this.env?.domain?.padsvc ? this.env?.domain?.padsvc : "http://padsvctest.vncitizens.vn/vi/") + "dossier/detail/" + this.dossierId,
                dossierSearchUrl: (!!this.env?.domain?.onegate ? this.env?.domain?.onegate : "http://padsvctest.vncitizens.vn/vi/") + "dossier/search/",
                dossierDetailUrl: (!!this.env?.domain?.onegate ? this.env?.domain?.onegate : "http://padsvctest.vncitizens.vn/vi/") + "dossier/search/" + this.dossierId,
                returnMethod: !!this.dossierDetail?.dossierReceivingKind?.name?.find(recieve => recieve.languageId === Number(this.selectedLangId))?.name ? this.dossierDetail?.dossierReceivingKind?.name?.find(recieve => recieve.languageId === Number(this.selectedLangId))?.name : '',
                receivingDate: '',
                appointmentDate: '',
                dossierFee: this.totalCost,
                reason: this.commentContentPlainText,
                numberDate: this.getNumberDate(),
                extend: extend
              }
            }
          }
        );
      }

      if(this.numberDateAdditionalRequirement && this.numberDateAdditionalRequirement.enable){
        requestBodyObj.numberDateRequire = this.numberDate;
        requestBodyObj.dateRequire = this.getDateRequire();
      }

      if (this.allowNextStepWaitingForApproval && this.enableApproveAdditionalRequestCheckBox && (this.dossierDetail.acceptedDate != undefined && this.dossierDetail.acceptedDate !=null)) {
        requestBodyObj.dossierStatus = 8;

        await this.setdossierTaskStatus();
        await this.setdossierTaskStatusRemind();

        requestBodyObj.dossierTaskStatus.id = this.dossierTaskStatusWaitingForApproval.id;
        requestBodyObj.dossierTaskStatus.name = this.dossierTaskStatusWaitingForApproval.name;
        requestBodyObj.dossierMenuTaskRemind.id = this.dossierMenuTaskRemindWaitingForApproval.id;
        requestBodyObj.dossierMenuTaskRemind.name =this.dossierMenuTaskRemindWaitingForApproval.name;
      }

      const requestBody = JSON.stringify(requestBodyObj, null, 2);

      this.dossierService.putDossierStatusWithComment(this.dossierId, requestBody).subscribe(async data => {
        if (data.affectedRows === 1) {
          if (this.deploymentService?.env?.OS_BTTTT?.isEnabledNotify) {
            // IGATESUPP-62366 thong bao cho nguoi dan
            this.dossierService.noticeDossier(this.dossierId, {comment: `Lý do:&nbsp;${this.getPlainText(this.commentContent)}. Nội dung: ${this.getPlainText(this.descriptionContent)}`}).subscribe(res => {});
          }
          const newDate = tUtils.newDate();
          this.postHistory(); 
          if (this.config.receivePromotionalProcedureUpdated === 1 || this.config.receivePromotionalProcedureUpdated === '1') {
            this.dossierService.getDossierDetail(this.dossierId).subscribe(data => {
              if (data.sync !== undefined) {
                const dataRequest: any = {};
                const agency = JSON.parse(localStorage.getItem('userAgency'));
                if (agency !== null && agency !== undefined) {
                  dataRequest.agencyId = agency.id;
                } else {
                  dataRequest.agencyId = this.config.rootAgency.id;
                }

                if (this.config.subsystemId !== null && this.config.subsystemId !== undefined) {
                  dataRequest.subsystemId = this.config.subsystemId;
                }

                dataRequest.agencyCode = data.agency.id;
                dataRequest.status = 5;
                dataRequest.code = data.code;
                dataRequest.sourceCode = data.sync.sourceCode;
                dataRequest.comment = this.commentContent.trim();
                dataRequest.date = this.datePipe.transform(newDate, 'yyyyMMddHHmmss');
                this.dossierService.postSynchronizePromotionStatus(dataRequest).subscribe(data => {
                });
              }
            });
          }
          if(!!this.notifyQNI){
            this.notifyQNIService.confirmSendSubject.next({
              confirm: true,
              renewContent: false,
            });
          } else {
            this.notiService.confirmSendSubject.next({
              confirm: true,
              renewContent: false,
              component: 'additionalRequirement'
            });
          }

          if (this.env?.enableApprovalOfLeadership == 1 || (this.allowNextStepWaitingForApproval && this.enableApproveAdditionalRequestCheckBox)){
            if(this.enablePauseWhenAdditional && this.isDueDateChecked){
              const data = {
                type: 8,
                date: newDate,
                attachment: this.uploadFileNames,
                deadlineAdditionalDate: this.additionRequestDueDate
              }
              await this.dossierService.updateApprovalData(this.dossierId, data).toPromise();
            }else{
              const data = {
                type: 8,
                date: newDate,
                attachment: this.uploadFileNames
              }
              this.dossierService.updateApprovalData(this.dossierId, data).subscribe(res => {});
            }
          }
          if(this.env?.enableApprovalOfLeadership === 0 && this.isSyncDBNLGSPTanDanBXD === 1){
            this.syncPostReceiveInforFileFromLocal();
          }
          if(this.isSyncConstructKTM && this.isSyncDBNLGSPTanDanBXD ===1){
            this.syncDossierKTMToBXD();
          }
          if(this.isSyncConstructHPG && this.isSyncDBNLGSPTanDanBXD ===1){
            this.syncDossierHPGToBXD();
          }
          if(this.isBoTNMTQNM || this.isBoTNMTHCM || this.isBoTNMTGLI){
            this.dossierService.updateDossierBoTNMT(this.dossierId).subscribe(test => {});
          }
          if (this.deploymentService.env?.OS_HBH?.isTnmtTriNam) {
            this.adapterService.sendTNMTTriNam(this.dossierId);
          }

          if(this.ktmEnableSyncToBLDTBXH){
            this.dossierService.getDossierDetail(this.dossierId).subscribe(async dossierData => {
              const procedureData = await this.procedureService.getProcedureDetail(dossierData?.procedure?.id).toPromise();
              if(this.socialProtectionKTMService.checkIfDossierNeedSyncBTXH(procedureData)){
                this.syncDossierStatusKTMToBLDTBXH(dossierData);
              }
            });
          }
          if(this.enableSyncDossierTNMTQni){
            this.adapterService.syncDossierTNMTQni(this.integratedTnmtConfigIdQni, this.dossierId).subscribe(res => {
            }, err => {
            });
          }

          // Lay thong tin ho so de dong bo len Bo LDTBXH
          this.dossierService.getDossierDetail(this.dossierId).subscribe(async dossierData => {
            if (this.enableSyncToBLDTBXH && this.isHasCodeLDXH) {
              this.syncDossierStatusToBLDTBXH(dossierData);
            }
            if (this.isHasCodeLDXH && this.deploymentService?.env?.OS_HBH?.enableLDXHTriNam) {
              Object.assign(dossierData, {contentTask: 'Tạm dừng hồ sơ'});
              this.trinamService.syncTaskLDXH(dossierData);
            }
          });

          // IGATESUPP-51500 - Đánh giá lại chức năng phê duyệt hồ sơ - 19.06.2023
          if(this.setOnlyApprovalAgency && this.approvalAgency.length > 1){
            this.postOnlyApprovalAgencyOfDossier(this.dossierCode);
          }   
          this.isDisabled = false;       
          const dataLog = {
            dossierId: this.dossierId,
            code: this.dossierCode,
            body: requestBody
          };
          this.logmanService.postUserEventsLog('additionalRequirement', dataLog).subscribe();

          if(this.isDVCLTEnable && this.dossierDetail?.isDVCLT && this.dossierDetail?.nationCode != null) {
             this.capNhatTrangThaiDVCLT(2);
          }
          this.dialogRef.close(true);
        } else {
          this.dialogRef.close(false);
        }
      }, err => {
        this.dialogRef.close(false);
      });
    }
  }
  async convertToPDF() {
    let filePdf, fileBefore;
    try {
      let filename = this.uploadedImage[0].filename;
      // Kiểm tra có phải file Word không
      if (filename.includes('.docx') || filename.includes('.doc')) {
        filePdf = await this.filemanService.convertDoc2Pdf(this.uploadedImage[0].id);
        if (filePdf && filePdf.id) {
          fileBefore = await this.filemanService.downloadFile(filePdf.id,1).toPromise();
          filename = filename.replace('.docx', '.pdf').replace('.doc', '.pdf');
          fileBefore.name = filename;
          fileBefore.id = filePdf.id;
          fileBefore.uuid = this.uploadedImage[0].uuid;
          let fileupload = [fileBefore];
          const user = await this.keycloakService.loadUserProfile();
          const accountId = user['attributes'].user_id[0];
          this.procedureService.uploadMultiFile(fileupload, accountId).subscribe(
            data => {
              this.uploadedImage = data;
              this.attachFilePreview.push(...this.uploadedImage);
              this.putDossierMultiAttachment('fileAttachDone', 3, 'lang.word.file', '', this.files);
            },
            err => {
              console.error('Lỗi uploadMultiFile:', err);
            }
          );
        } else {
          console.error('convertDoc2Pdf không trả về id hợp lệ:', filePdf);
        }
      }
      else{
        this.attachFilePreview.push(...this.uploadedImage);
        this.putDossierMultiAttachment('fileAttachDone', 3, 'lang.word.file', '', this.files);
      }
    } catch (err) {
      console.error('Lỗi trong convertToPDF:', err);
    }
  }
  async putDossierMultiAttachment(doneClass, type, rbk, oldName, newName) {
    const rs = [];
    this.attachFilePreview.forEach(file => {
      rs.push({
        id: file.id,
        filename: file.filename,
        size: file.size,
        group: this.config.dossierAttachedFileTagId,
        uuid: file.uuid
      });
    });
    this.resultFilePreview.forEach(file => {
      rs.push({
        id: file.id,
        filename: file.filename,
        size: file.size,
        group: this.config.dossierResultFileTagId,
        uuid: file.uuid
      });
    });
    this.apologyTextFilePreview.forEach(file => {
      rs.push({
        id: file.id,
        filename: file.filename,
        size: file.size,
        group: this.config.dossierApologyTextFileTagId
      });
    });
    const putBody = {
      attachment: rs
    };
    const requestBody = JSON.stringify(putBody, null, 2);
    this.dossierService.putDossierOnline(this.dossierId, requestBody).subscribe(data => {
      if (data.affectedRows === 1) {
        
      } else {
        const msgObj = {
          vi: 'Cập nhật thất bại!',
          en: 'Update failed!'
        };
        this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
       
      }
    });
  }
  async capNhatTrangThaiDVCLT(trangThaiDVCLT:number){
    console.log(this.dossierDetail);
    const userExperienceAgency = JSON.parse(localStorage.getItem('userExperienceAgency'));
    const UID = localStorage.getItem('UID');
    const user = await this.humanService.getUserInfo3(UID).toPromise();

    const currentDate = new Date();
    let dataBody = {
      maTTHC: this.dossierDetail?.procedure?.dvcltProcedureCode,
      soHoSoLT: this.dossierDetail?.nationCode,
      coQuanXuLy: 5,
      maHoSo: this.dossierDetail?.code,
      trangThai: trangThaiDVCLT, 
      thoiGianThucHien: this.datePipe.transform(currentDate, 'dd/MM/yyyy HH:mm:ss'),
      ghiChu: !!this.commentContent ? this.commentContent.substring(3,this.commentContent.length - 4) : "Yêu cầu bổ sung",
      nguoiXuLy: user?.fullname,
      chucDanh: userExperienceAgency?.position?.name,
      phongBanXuLy: this.dossierDetail?.agency?.parent?.name[0]?.name,
      noiDungXuLy: !!this.descriptionContent ? this.descriptionContent.substring(3,this.descriptionContent.length - 4) : "Không có nội dung xử lý",
      // ngayBatDau: this.dossierDetail?.acceptedDate ? this.datePipe.transform(this.dossierDetail?.acceptedDate, 'dd/MM/yyyy HH:mm:ss') : this.datePipe.transform(currentDate, 'dd/MM/yyyy HH:mm:ss'),
      // ngayKetThucTheoQuyDinh: this.dossierDetail?.appointmentDate ? this.datePipe.transform(this.dossierDetail?.returnedDate, 'dd/MM/yyyy HH:mm:ss') : this.datePipe.transform(this.dossierDetail?.appliedDate, 'dd/MM/yyyy HH:mm:ss'),
      // ngayHenTraTruoc: this.dossierDetail?.appointmentDate ? this.datePipe.transform(this.dossierDetail?.appointmentDate, 'dd/MM/yyyy HH:mm:ss'): this.datePipe.transform(currentDate, 'dd/MM/yyyy HH:mm:ss'),
      // ngayHenTraMoi: this.dossierDetail?.appointmentDate ? this.datePipe.transform(this.dossierDetail?.appointmentDate, 'dd/MM/yyyy HH:mm:ss'): this.datePipe.transform(currentDate, 'dd/MM/yyyy HH:mm:ss'),
      hanBoSungHoSo: this.dossierDetail?.appointmentDate ? this.datePipe.transform(this.dossierDetail?.appointmentDate, 'dd/MM/yyyy HH:mm:ss'): this.datePipe.transform(currentDate, 'dd/MM/yyyy HH:mm:ss'),
      ketQuaXuLy:''
    };
    this.adapterService.capNhatTrangThaiDVCLT(dataBody).subscribe(res => {
      if(res){

      }
      
    }, err => {
    });
   }
  onDismiss() {
    // xóa dữ liệu file tạm
    this.uploadFileNames.forEach(element => {
      this.procedureService.deleteFile(element.id).subscribe(res => {
      }, err => {
        console.log(err);
      });
    });
    this.uploadFileNames = [];
    //
    this.dialogRef.close();
  }

  postComment(commentContent, description?:string) {
    let msgObj = {};
    if(!!description){
      msgObj = {
        vi: `Yêu cầu bổ sung: ${commentContent}<br>Nội dung: ${description}`,
        en: `Additional requirement: ${commentContent}<br>Description: ${description}`
      };
    }else{
      msgObj = {
        vi: `Yêu cầu bổ sung: ${commentContent}`,
        en: `Additional requirement: ${commentContent}`
      };
    }
    const content = {
      groupId: 2,
      itemId: this.dossierId,
      user: {
        id: this.accountId,
        fullname: this.userName
      },
      content: msgObj[this.selectedLang],
      file: this.uploadFileNames
    };
    const requestBody = JSON.stringify(content, null, 2);
    this.dossierService.postComment(requestBody).subscribe(data => { });
  }

  getPlainText( strSrc ) {
    let resultStr = "";

    // Ignore the <p> tag if it is in very start of the text
    if(strSrc.indexOf('<p>') == 0)
        resultStr = strSrc.substring(3);
    else
        resultStr = strSrc;

    // Replace <p> with two newlines
    resultStr = resultStr.replace(/<p>/gi, "\r\n\r\n");
    // Replace <br /> with one newline
    resultStr = resultStr.replace(/<br \/>/gi, "\r\n");
    resultStr = resultStr.replace(/<br>/gi, "\r\n");

    // Replace &nbsp; with space
    resultStr = resultStr.replace(/&nbsp;/gi, " ");
    //-+-+-+-+-+-+-+-+-+-+-+
    // Strip off other HTML tags.
    //-+-+-+-+-+-+-+-+-+-+-+

    return  resultStr.replace( /<[^<|>]+?>/gi,'' );
  }

  getPlainText1( strSrc ) {
    let resultStr = "";

    // Ignore the <p> tag if it is in very start of the text
    if(strSrc.indexOf('<p>') == 0)
        resultStr = strSrc.substring(3);
    else
        resultStr = strSrc;

    // Replace <p> with one newline
    resultStr = resultStr.replace(/<p>/gi, "\n"); // --> Diff between getPlainText && getPlainText1
    // Replace <br /> with one newline
    resultStr = resultStr.replace(/<br \/>/gi, "\r\n");
    resultStr = resultStr.replace(/<br>/gi, "\r\n");

    // Replace &nbsp; with space
    resultStr = resultStr.replace(/&nbsp;/gi, " ");
    //-+-+-+-+-+-+-+-+-+-+-+
    // Strip off other HTML tags.
    //-+-+-+-+-+-+-+-+-+-+-+

    return  resultStr.replace( /<[^<|>]+?>/gi,'' );
  }

  onCommentEditorChange(event) {
    this.commentContent = event.editor.getData();
    if (this.ckeditorMaxLength > 0) {
      if (event.editor.getData().trim().length > this.ckeditorMaxLength) {
        this.isCKMaxlenght = true;
      } else {
        this.isCKMaxlenght = false;
      }
    } else if (event.editor.getData().trim().length > 500) {
      this.isCKMaxlenght = true;
    } else {
      this.isCKMaxlenght = false;
    }

    this.commentContentPlainText = "";
    this.commentContentPlainText = this.getPlainText(this.commentContent).trim();
  }

  onContentEditorChange(event) {
    this.descriptionContent = event.editor.getData();
    if (this.ckeditorMaxLength > 0) {
      if (event.editor.getData().trim().length > this.ckeditorMaxLength) {
        this.isCKDescriptionMaxlenght = true;
      } else {
        this.isCKDescriptionMaxlenght = false;
      }
    } else if (event.editor.getData().trim().length > 500) {
      this.isCKDescriptionMaxlenght = true;
    } else {
      this.isCKDescriptionMaxlenght = false;
    }
  }

  uploadMultiFile(file, accountId) {
    return new Promise((resolve) => {
      this.procedureService.uploadMultiFile(file, accountId).subscribe(data => {
        if(!!data && data.length > 0){
          this.uploadedImage = data;
          this.uploadFileNames.push(...data);
          // this.formToJSON();
          resolve(true);
        } else {
          resolve(false);
        }
      }, err => {
        console.log(err);
        resolve(false);
      });
    });
  }

  async onSelectFile(event) {
    if (event.target.files && event.target.files[0]) {
      for (const i of event.target.files) {
        if (i.size >= this.maxFileSize * 1024 * 1024) {
          const msgObj = {
            vi: 'Kích thước tệp tin quá lớn, tên tệp: ' + i.name,
            en: 'The file is too large, file name: ' + i.name
          };
          this.snackbarService.openSnackBar(0, '', msgObj[this.selectedLang], 'error_notification', this.config.expiredTime);
          return;
        }
        if (this.acceptFileType.filter(t => t === i.type.toLowerCase()).length > 0) {
          let newFile = [];
          newFile.push(i);
          this.files.push(i);
          const extension = i.name.substring(i.name.lastIndexOf('.')).split('.').pop();
          this.urls.push(this.getFileIcon(extension));

          const reader = new FileReader();
          reader.onload = () => {
            this.uploaded = true;
          };
          if (i.name.length > 20) {
            const startText = i.name.substr(0, 5);
            const shortText = i
              .name
              .substring(i.name.length - 7, i.name.length);
            this.fileNames.push(startText + '...' + shortText);
            this.fileNamesFull.push(i.name);
          } else {
            this.fileNames.push(i.name);
            this.fileNamesFull.push(i.name);
          }
          reader.readAsDataURL(i);
          if (this.files.length > 0) {
            let checkFile = await this.uploadMultiFile(newFile, this.accountId);
            if (!checkFile) {
              const msgObj = {
                vi: 'Upload file thất bại!',
                en: 'File upload failed!'
              };
              this.snackbarService.openSnackBar(2, '', msgObj[this.selectedLang], 'warning_notification', this.config.expiredTime);
            }
          }
        } else {
          const msgObj = {
            vi: 'Không hỗ trợ loại tệp tin ',
            en: 'File type is not supported '
          };
          // tslint:disable-next-line: max-line-length
          this.snackbarService.openSnackBar(0, '', msgObj[this.selectedLang] + i.type.substring(i.type.lastIndexOf('/') + 1).toUpperCase(), 'error_notification', this.config.expiredTime);
        }
      }
    }
  }

  removeItem(index: number) {
    this.procedureService.deleteFile(this.uploadFileNames[index].id).subscribe(res => {
    }, err => {
      console.log(err);
    });
    this.uploadFileNames.splice(index, 1);
    this.urls.splice(index, 1);
    this.fileNames.splice(index, 1);
    this.fileNamesFull.splice(index, 1);
    this.files.splice(index, 1);
    this.blankVal = '';
  }
  

  getFileIcon(ext) {
    return this.config.cloudStaticURL + 'icon/files/300x300/' + ext + '.png';
  }

  downloadTemplate(){
    window.open(this.fileTemplate, '_self');
  }
  // DBN - BXD - dong bo thong hs qua LGSP Tan Dan
  syncPostReceiveInforFileFromLocal (){
    this.dossierService.getDossierDetail(this.dossierId).subscribe(data => {
      let dataApplicant =  data?.applicant?.data;
      let nhanThongTinHSTuDP = [];
      let thongTinNguoiNop = {
        HoTenNguoiNop : dataApplicant?.fullname, //*
        SoCMND : dataApplicant?.identityNumber, //*
        EmailNguoiNop: dataApplicant?.email,
        SoDienThoaiNguoiNop: dataApplicant?.phoneNumber, //*
        DiaChiThuongTruNguoiNop: dataApplicant?.address + ","
                               + dataApplicant?.village?.label + ","
                               + dataApplicant?.district?.label + ","
                               + dataApplicant?.province?.label + ","
                               + dataApplicant?.nation?.label, //*
        MaDoiTuong: ""
      };
      let giayToTrongQuaTrinhXuLy = {
        MaGiayTo: "",
        TenGiayTo: "",
        NoiDungBase64: "",
        DinhDangTepTin: "", // dinh dang .pdf, .doc, .png,...
        MoTa: "",
        LoaiGiayTo: ""
      }
      let hinhThucTraKetQua = 0;
      switch(data?.dossierReceivingKind?.id) {
        case '5f8968888fffa53e4c073ded':
        hinhThucTraKetQua = 0;
        break;
        case '604ecde877bed4110c1dd0d1':
          hinhThucTraKetQua = 1;
          break;
        case '6163f1e20f363b04cf60b224':
          hinhThucTraKetQua = 1;
          break;
        case '5f8969018fffa53e4c073dee':
          hinhThucTraKetQua = 1;
          break;
      }
      let arrThongTinTienTrinh = [];
      let dossierStatus = this.statusName;
      data?.task.forEach(task => {
          if(task?.isCurrent === 1){
            let thongTinTienTrinh = {
              NguoiXuLy: task?.assignee?.fullname, //*
              ChucDanh: "",
              ThoiDiemXuLy: this.datePipe.transform(task?.createdDate, 'yyyyMMddHHmmss'), //* yyyyMMddHHmmss
              PhongBanXuLy: task?.agency?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name,
              NoiDungXuLy:  task?.bpmProcessDefinitionTask?.remind ?  task?.bpmProcessDefinitionTask?.remind?.name : "",
              NgayBatDau:   this.datePipe.transform(task?.createdDate, 'yyyyMMddHHmmss'), //yyyyMMddHHmmss
              NgayKetThucTheoQuyDinh: task?.isCurrent === 0? this.datePipe.transform(task?.updatedDate, 'yyyyMMddHHmmss') : ""
            }
            arrThongTinTienTrinh.push(thongTinTienTrinh);
          }

      });
      let dossierSync = {
        MaHoSo: data.code, //*
        TrangThaiHoSo: dossierStatus, // phu luc 2 *
        NgayTiepNhan: this.datePipe.transform(data?.acceptedDate, 'yyyyMMddHHmmss') ,// yyyyMMddHHmmss*
        NgayHenTraKetQua: this.datePipe.transform(data?.appointmentDate, 'yyyyMMddHHmmss'), //yyyyMMddHHmmss
        ThongTinNguoiNop : thongTinNguoiNop,
        GiayToTrongQuaTrinhXuLy : [],
        HinhThucTraKetQua: hinhThucTraKetQua, // 0: tra truc tiep 1: tra qua duong buu dien 2: tra kq truc tuyen
        ThongTinTienTrinh : arrThongTinTienTrinh,
        MaTTHC: data?.procedure?.code,
        TenTTHC: data?.procedure?.translate?.find(procedure => procedure.languageId === Number(this.selectedLangId))?.name ? data?.procedure?.translate?.find(procedure => procedure.languageId === Number(this.selectedLangId))?.name : '',
      }
      //nhanThongTinHSTuDP.push(dossierSync);
      let params = "agencyId=" + data?.agency.id + "&subsystemId=" + this.config?.subsystemId ; // &configId
      this.dossierService.postReceiveInforFileFromLocal(params, dossierSync).subscribe(async data => {
        console.log("===postReceiveInforFileFromLocal===");
        console.log(data);
      }, er => {
        console.log(er);
      })
    }, err => {
      console.log(err);
    });
  }
  syncDossierKTMToBXD(){
    this.dossierService.getDossierDetail(this.dossierId).subscribe(data => {
      let arrThongTinTienTrinh=[]
      let dataApplicant =  data?.applicant?.data;
      let thongTinNguoiNop = {
        HoTenNguoiNop : dataApplicant?.fullname, //*
        SoCMND : dataApplicant?.identityNumber, //*
        EmailNguoiNop: dataApplicant?.email,
        SoDienThoaiNguoiNop: dataApplicant?.phoneNumber, //*
        DiaChiThuongTruNguoiNop: dataApplicant?.address + ","
                               + dataApplicant?.village?.label + ","
                               + dataApplicant?.district?.label + ","
                               + dataApplicant?.province?.label + ","
                               + dataApplicant?.nation?.label, //*
        MaDoiTuong: ""
      };
      let hinhThucTraKetQua = 0;
      switch(data?.dossierReceivingKind?.id) {
        case '5f8968888fffa53e4c073ded':
        hinhThucTraKetQua = 0;
        break;
        case '604ecde877bed4110c1dd0d1':
          hinhThucTraKetQua = 1;
          break;
        case '6163f1e20f363b04cf60b224':
          hinhThucTraKetQua = 1;
          break;
        case '5f8969018fffa53e4c073dee':
          hinhThucTraKetQua = 1;
          break;
      }    
      data?.task.forEach(task => {
        if(task?.isCurrent === 1){
          let thongTinTienTrinh = {
            NguoiXuLy: task?.assignee?.fullname, //*
            ChucDanh: "",
            ThoiDiemXuLy: this.datePipe.transform(task?.createdDate, 'yyyyMMddHHmmss'), //* yyyyMMddHHmmss
            PhongBanXuLy: task?.agency?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name,
            NoiDungXuLy:  task?.bpmProcessDefinitionTask?.remind ?  task?.bpmProcessDefinitionTask?.remind?.name : "",
            NgayBatDau:   this.datePipe.transform(task?.createdDate, 'yyyyMMddHHmmss'), //yyyyMMddHHmmss
            NgayKetThucTheoQuyDinh: task?.isCurrent === 0? this.datePipe.transform(task?.updatedDate, 'yyyyMMddHHmmss') : ""
          }
          arrThongTinTienTrinh.push(thongTinTienTrinh);
        }

    });
        let giayToTrongQuaTrinhXuLy = [];
        let filename=""
        for (let i = 0; i < this.uploadFileNames.length; i++) {
          filename=this.uploadFileNames[i].filename
          const nameDoc = filename.split(".")[0];
          const extension = filename.split(".").pop();
          const base64Filename = btoa(unescape(encodeURIComponent(filename)));
          giayToTrongQuaTrinhXuLy.push({
            MaGiayTo: null,
            TenGiayTo: nameDoc,
            NoiDungBase64: base64Filename,
            DinhDangTepTin:extension, // dinh dang .pdf, .doc, .png,...
            MoTa: null,
            LoaiGiayTo: "2"
          })
        }
        let maHoSo = data.code
        if(maHoSo.includes("000.00")){
            this.codeMap = maHoSo;
          }else{      
            const inputString = maHoSo;
            const parts = inputString.split("-");
            const prefixParts = parts[0].split(".");
            const prefix = `000.00.${prefixParts[1]}.${prefixParts[0]}`;
            this.codeMap = `${prefix}-${parts[1]}-${parts[2]}`;  
          }
      let originalString = data.procedure.code;
      let result = originalString.split(".")[0] + '.' + originalString.split(".")[1];
      let dossierSync = {
        data: [
          {
              MaHoSo: this.codeMap,
              MaTTHC:result,
              NgayTiepNhan: this.datePipe.transform(data?.acceptedDate, 'yyyyMMddHHmmss'),
              NgayHenTraKetQua: this.datePipe.transform(data?.appointmentDate, 'yyyyMMddHHmmss'),
              TrangThaiHoSo: "5",
              ThongTinNguoiNop: thongTinNguoiNop,
              GiayToTrongQuaTrinhXuLy:giayToTrongQuaTrinhXuLy,
              HinhThucTraKetQua: hinhThucTraKetQua,
              ThongTinTienTrinh: arrThongTinTienTrinh
                 
          }
      ]
  }
   let config = this.constructConfigId // &configId
      console.log(dossierSync)
      this.adapterService.syncDossierConstructKTM(config,dossierSync).subscribe(async data=>{
        console.log(data);
      })
    })
  }

  syncDossierHPGToBXD(){
    this.dossierService.getDossierDetail(this.dossierId).subscribe(data => {
      let arrThongTinTienTrinh=[]
      let dataApplicant =  data?.applicant?.data;
      let thongTinNguoiNop = {
        HoTenNguoiNop : dataApplicant?.ownerFullname, //*
        SoCMND : dataApplicant?.identityNumber, //*
        EmailNguoiNop: dataApplicant?.email,
        SoDienThoaiNguoiNop: dataApplicant?.phoneNumber, //*
        DiaChiThuongTruNguoiNop: dataApplicant?.address + ","
                               + dataApplicant?.village?.label + ","
                               + dataApplicant?.district?.label + ","
                               + dataApplicant?.province?.label + ","
                               + dataApplicant?.nation?.label, //*
        MaDoiTuong: ""
      };
      let hinhThucTraKetQua = 0;
      switch(data?.dossierReceivingKind?.id) {
        case '5f8968888fffa53e4c073ded':
        hinhThucTraKetQua = 0;
        break;
        case '604ecde877bed4110c1dd0d1':
          hinhThucTraKetQua = 1;
          break;
        case '6163f1e20f363b04cf60b224':
          hinhThucTraKetQua = 1;
          break;
        case '5f8969018fffa53e4c073dee':
          hinhThucTraKetQua = 1;
          break;
      }    
      data?.task.forEach(task => {
        if(task?.isCurrent === 1){
          let thongTinTienTrinh = {
            NguoiXuLy: task?.assignee?.fullname, //*
            ChucDanh: "",
            ThoiDiemXuLy: this.datePipe.transform(task?.createdDate, 'yyyyMMddHHmmss'), //* yyyyMMddHHmmss
            PhongBanXuLy: task?.agency?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name,
            NoiDungXuLy:  task?.bpmProcessDefinitionTask?.remind ?  task?.bpmProcessDefinitionTask?.remind?.name : "",
            NgayBatDau:   this.datePipe.transform(task?.createdDate, 'yyyyMMddHHmmss'), //yyyyMMddHHmmss
            NgayKetThucTheoQuyDinh: task?.isCurrent === 0? this.datePipe.transform(task?.updatedDate, 'yyyyMMddHHmmss') : ""
          }
          arrThongTinTienTrinh.push(thongTinTienTrinh);
        }
      });
      let giayToTrongQuaTrinhXuLy = [];
      let filename=""
      for (let i = 0; i < this.uploadFileNames.length; i++) {
        filename=this.uploadFileNames[i].filename
        const nameDoc = filename.split(".")[0];
        const extension = filename.split(".").pop();
        const base64Filename = btoa(unescape(encodeURIComponent(filename)));
        giayToTrongQuaTrinhXuLy.push({
          MaGiayTo: null,
          TenGiayTo: nameDoc,
          NoiDungBase64: base64Filename,
          DinhDangTepTin:extension, // dinh dang .pdf, .doc, .png,...
          MoTa: null,
          LoaiGiayTo: "2"
        })
      }
      let maHoSo = data.code
      let originalString = data.procedure.code;
      let result = originalString.split(".")[0] + '.' + originalString.split(".")[1];
      let dossierSync = {
        data: [
          {
            MaHoSo: maHoSo,
            MaTTHC:result,
            NgayTiepNhan: this.datePipe.transform(data?.acceptedDate, 'yyyyMMddHHmmss'),
            NgayHenTraKetQua: this.datePipe.transform(data?.appointmentDate, 'yyyyMMddHHmmss'),
            TrangThaiHoSo: "5",
            ThongTinNguoiNop: thongTinNguoiNop,
            GiayToTrongQuaTrinhXuLy:giayToTrongQuaTrinhXuLy,
            HinhThucTraKetQua: hinhThucTraKetQua,
            ThongTinTienTrinh: arrThongTinTienTrinh              
          }
        ]
      }
      this.adapterService.syncDossierConstructHPG(dossierSync).subscribe(async data=>{
        console.log(data);
      })
    })
  }

  // getApprovaledAgency(agency) {
  //   if(!!agency.parent){
  //     this.processService.getApprovalAgency(agency.parent.id).subscribe(data => {
  //       if (!!data.content[0]) {
  //         // data.content[0].approvalAgency.forEach(item => {
  //         //   item.name.forEach(name => {
  //         //     if (name.languageId == this.selectedLangId)
  //         //       item.DisplayName = name.name;
  //         //   })
  //         // });
  //         // this.approvalAgency = data.content[0].approvalAgency;
  //         data.content[0].approvalAgency.name.forEach(name => {
  //           if (name.languageId === this.selectedLangId){
  //           data.content[0].approvalAgency.DisplayName = name.name;
  //           }
  //         });
  //         this.approvalAgency = [data.content[0].approvalAgency];
  //       }
  //     });
  //   }

  //   if (!!this.approvalAgency) {
  //     this.processService.getApprovalAgency(agency.id).subscribe(data => {
  //       if (!!data.content[0]) {
  //         // data.content[0].approvalAgency.forEach(item => {
  //         //   item.name.forEach(name => {
  //         //     if (name.languageId == this.selectedLangId)
  //         //       item.DisplayName = name.name;
  //         //   })
  //         // });
  //         // this.approvalAgency = data.content[0].approvalAgency;
  //         data.content[0].approvalAgency.name.forEach(name => {
  //           if (name.languageId === this.selectedLangId){
  //           data.content[0].approvalAgency.DisplayName = name.name;
  //           }
  //         });
  //         this.approvalAgency = [data.content[0].approvalAgency];
  //       }
  //     });
  //   }
  //   console.log('approvalAgency', this.approvalAgency);
  // }
  getApprovaledAgency(agency) {
    this.approvalAgency = [];
    let enableApprovaledAgencyId = this.deploymentService.env.OS_QNI.enableApprovaledAgencyId;
    if (enableApprovaledAgencyId){
      if (agency && agency.id) {
        this.processService.getApprovalAgency(agency.id).subscribe(data2 => {    
          if (data2 && data2.content && data2.content.length > 0) {
            const uniqueAgencies = new Set();
            data2.content.forEach(item => {
              if (item.approvalAgency && item.approvalAgency.name) {
                item.approvalAgency.name.forEach(name => {
                  if (name.languageId === this.selectedLangId) {
                    item.approvalAgency.DisplayName = name.name;
                  }
                });
                if (!uniqueAgencies.has(item.approvalAgency.id)) {
                  uniqueAgencies.add(item.approvalAgency.id);
                  this.approvalAgency.push(item.approvalAgency);
                }
              }
            });
  
            if (this.approvalAgency.length === 1) {
              this.updateForm.patchValue({
                approvalAgencyId: this.approvalAgency[0].id,
              });
            }
          }
        });
      }
    }else{
      if (!!agency.parent) {
        this.processService.getApprovalAgency(agency.parent.id).subscribe(data => {
          if (data.content.length > 0){
            this.approvalAgency = [];
            // tslint:disable-next-line:prefer-for-of
            for (let i = 0; i < data.content.length; i++){
              data.content[i].approvalAgency.name.forEach(name => {
                if (name.languageId === this.selectedLangId){
                data.content[i].approvalAgency.DisplayName = name.name;
                }
              });
              if (this.approvalAgency.filter(item => item.id === data.content[i].approvalAgency.id).length === 0){
                this.approvalAgency.push(data.content[i].approvalAgency);
              }
            }
          }
          if (!!this.approvalAgency) {
            this.processService.getApprovalAgency(agency.id).subscribe(data2 => {
              if (data2.content.length > 0){
                // tslint:disable-next-line:prefer-for-of
                for (let i = 0; i < data2.content.length; i++){
                  data2.content[i].approvalAgency.name.forEach(name => {
                    if (name.languageId === this.selectedLangId){
                    data2.content[i].approvalAgency.DisplayName = name.name;
                    }
                  });
                  if (this.approvalAgency.filter(item => item.id === data2.content[i].approvalAgency.id).length === 0){
                    this.approvalAgency.push(data2.content[i].approvalAgency);
                  }
                }
              }
              if (!!this.approvalAgency &&  this.approvalAgency.length === 1){
                this.updateForm.patchValue({
                  approvalAgencyId: this.approvalAgency[0].id,
                });
              }
            });
          }
        });
      }
      console.log('approvalAgency', this.approvalAgency);
    }
  }

  async onCheckNumberDateChange($event){
    this.notifyCheck = this.checkSendNotifyComponent.notify;
    this.checkNumberDate = $event?.checked;
    //this.getDetailDossier();
    setTimeout(() => {
        this.getNotify();
    });
  }

  onNumberDateChange($event){
    if(this.numberDate < 0){
      this.numberDate = null;
      const msgObj = {
        vi: 'Vui lòng nhập số ngày chờ bổ sung phải lớn hơn hoặc bằng 0!',
        en: 'Please enter the number of additional waiting days must be greater than or equal to 0!'
      };
      this.snackbarService.openSnackBar(2, '', msgObj[this.selectedLang], 'warning_notification', this.config.expiredTime);
      setTimeout(() => {
        this.getNotify();
      });
      return;
    }
    if(this.numberDate > this.numberDateAdditionalRequirement.maxNumber){
      this.numberDate = null;
      const msgObj = {
        vi: 'Vui lòng nhập số ngày chờ bổ sung không được lớn hơn ' + this.numberDateAdditionalRequirement.maxNumber + '!',
        en: 'Please enter the number of additional waiting days no more than ' + this.numberDateAdditionalRequirement.maxNumber + '!'
      };
      this.snackbarService.openSnackBar(2, '', msgObj[this.selectedLang], 'warning_notification', this.config.expiredTime);
      setTimeout(() => {
        this.getNotify();
      });
      return;
    }

    setTimeout(() => {
      this.getNotify();
    });
  }
  async getDossierStatusSyncToBLDTBXH(dossierData){
    const userExperienceAgency = JSON.parse(localStorage.getItem('userExperienceAgency'));
    const UID = localStorage.getItem('UID');
    const user = await this.humanService.getUserInfo3(UID).toPromise();
    this.dossierToSyncStatusBLDTBXH = {
      "MaHoSo": dossierData.code,
      "TaiKhoanXuLy": (!!user?.account?.username && user?.account?.username.length > 0) ? user?.account?.username[0]?.value : "",
      "NguoiXuLy": user?.fullname,
      "ChucDanh": userExperienceAgency?.position?.name,
      "ThoiDiemXuLy": this.datePipe.transform(new Date(), 'dd/MM/yyyy'),
      "DonViXuLy": dossierData.agency?.parent?.name[0]?.name,
      "NoiDungXuLy": !!this.commentContent ? this.commentContent.substring(3,this.commentContent.length - 4) : "Không có nội dung xử lý",
      "StatusId" : "",
      "NgayBatDau": dossierData.acceptedDate ?
        this.datePipe.transform(dossierData.acceptedDate, 'dd/MM/yyyy') : this.datePipe.transform(dossierData.appliedDate, 'dd/MM/yyyy'),
      "NgayKetThucTheoQuyDinh": dossierData.appointmentDate ?
        this.datePipe.transform(dossierData.appointmentDate, 'dd/MM/yyyy') : this.datePipe.transform(dossierData.appliedDate, 'dd/MM/yyyy'),
      "UseridCreated": null,
      "UseridEdited": null,
      "DateCreated": null,
      "DateEdited": null
    }
  }

  async syncDossierStatusToBLDTBXH(dossierData){
    await this.getDossierStatusSyncToBLDTBXH(dossierData);
    if(this.dossierToSyncStatusBLDTBXH) {
      this.socialProtectionService.mappingStatusId(dossierData.dossierStatus?.id, dossierData.dossierMenuTaskRemind?.id).then(res => {
        const status = res;

        this.dossierToSyncStatusBLDTBXH.StatusId = status;
        const body = {
          "configId" : this.syncToBLDTBXHConfigId,
          "statusDossier" : this.dossierToSyncStatusBLDTBXH,
        }
        this.adapterService.syncDossierStatusToBLDTBXH(body).subscribe(res => {
          if(res){

          }
        });
      });
    }
  }

  async getDossierStatusKTMSyncToBLDTBXH(dossierData){
    const userExperienceAgency = JSON.parse(localStorage.getItem('userExperienceAgency'));
    const UID = localStorage.getItem('UID');
    const user = await this.humanService.getUserInfoKTM(UID).toPromise();
    this.ktmDossierToSyncStatusBLDTBXH = {
      "MaHoSo": dossierData.code,
      "TaiKhoanXuLy": (!!user?.account?.username && user?.account?.username.length > 0) ? user?.account?.username[0]?.value : "",
      "NguoiXuLy": user?.fullname,
      "ChucDanh": userExperienceAgency?.position?.name,
      "ThoiDiemXuLy": this.datePipe.transform(new Date(), 'dd/MM/yyyy'),
      "DonViXuLy": dossierData.agency?.parent?.name[0]?.name,
      "NoiDungXuLy": !!this.commentContent ? this.commentContent.substring(3,this.commentContent.length - 4) : "Không có nội dung xử lý",
      "StatusId" : "",
      "NgayBatDau": dossierData.acceptedDate ?
        this.datePipe.transform(dossierData.acceptedDate, 'dd/MM/yyyy') : this.datePipe.transform(dossierData.appliedDate, 'dd/MM/yyyy'),
      "NgayKetThucTheoQuyDinh": dossierData.appointmentDate ?
        this.datePipe.transform(dossierData.appointmentDate, 'dd/MM/yyyy') : this.datePipe.transform(dossierData.appliedDate, 'dd/MM/yyyy'),
      "UseridCreated": null,
      "UseridEdited": null,
      "DateCreated": null,
      "DateEdited": null
    }
  }

  async syncDossierStatusKTMToBLDTBXH(dossierData){
    await this.getDossierStatusKTMSyncToBLDTBXH(dossierData);
    if(this.ktmDossierToSyncStatusBLDTBXH) {
      this.socialProtectionKTMService.mappingStatusId(dossierData.dossierStatus?.id, dossierData.dossierMenuTaskRemind?.id).then(res => {
        const status = res;

        this.ktmDossierToSyncStatusBLDTBXH.StatusId = status;
        const body = {
          "configId" : this.ktmSyncToBLDTBXHConfigId,
          "statusDossier" : this.ktmDossierToSyncStatusBLDTBXH,
        }
        this.adapterService.syncDossierStatusKTMToBLDTBXH(body).subscribe(res => {
          if(res){

          }
        });
      });
    }
  }

  async changeFile(type){
    if (this.commentContent.trim() === '') {
      const msgObj = {
        vi: 'Vui lòng nhập ý kiến xử lý!',
        en: 'Please enter a handling comments!'
      };
      this.snackbarService.openSnackBar(2, '', msgObj[this.selectedLang], 'warning_notification', this.config.expiredTime);
      return
    }
    if (this.descriptionContent.trim() === '') {
      const msgObj = {
        vi: 'Vui lòng nhập nội dung yêu cầu bổ sung!',
        en: 'Please enter additional request content!'
      };
      this.snackbarService.openSnackBar(2, '', msgObj[this.selectedLang], 'warning_notification', this.config.expiredTime);
      return
    }
    if (this.isCKMaxlenght || this.isCKDescriptionMaxlenght) {
      let msgObj;
      if (this.ckeditorMaxLength > 0) {
        msgObj = {
          vi: 'Nội dung không quá ' + this.ckeditorMaxLength + ' ký tự!',
          en: 'Content must not exceed ' + this.ckeditorMaxLength + ' characters!'
        };
      } else {
        msgObj = {
          vi: 'Nội dung không quá 500 ký tự!',
          en: 'Content must not exceed 500 characters!'
        };
      }
      this.snackbarService.openSnackBar(2, '', msgObj[this.selectedLang], 'warning_notification', this.config.expiredTime);
      return
    }
    if (!this.requireAdditionalTemplate) {
      const msgObj = {
        vi: 'Không tìm thấy mẫu file',
        en: 'Template not found'
      };
      this.snackbarService.openSnackBar(0, '', msgObj[this.selectedLang], 'error_notification', this.config.expiredTime);
      return;
    }
    const url = this.requireAdditionalTemplate;
    let blob = await fetch(url).then(r => r.blob());
    let address = this.dossierDetail?.applicant?.data?.address ? this.dossierDetail?.applicant?.data?.address :"";
    let village = this.dossierDetail?.applicant?.data?.village?.label ? this.dossierDetail?.applicant?.data?.village?.label : "";
    let district = this.dossierDetail?.applicant?.data?.district?.label ? this.dossierDetail?.applicant?.data?.district?.label : "";
    let province = this.dossierDetail?.applicant?.data?.province?.label ? this.dossierDetail?.applicant?.data?.province?.label : "";
    const value = {
      "day": tUtils.newDate().getDate(),
      "month": tUtils.newDate().getMonth() + 1,
      "year" : tUtils.newDate().getFullYear(),
      "fullName": this.dossierDetail?.applicant?.data?.fullname,
      "code": this.dossierDetail?.code,
      "address": address + ', '+ village + ', ' + district + ', ' + province,
      "phoneNumber": this.dossierDetail?.applicant?.data?.phoneNumber + ' ',
      "email": this.dossierDetail?.applicant?.data?.email,
      "reason": this.getPlainText1(this.commentContent),
      "description": this.getPlainText1(this.descriptionContent),
      "nameOfficer": localStorage.getItem('tempUsername').trim()
    };
    const newComponent = [
      {
        "label" : "ngày",
        "key": "day"
      },
      {
        "label" : "tháng",
        "key" : "month"
      },
      {
        "label" : "năm",
        "key" : "year"
      },
      {
        "label" : "Hồ sơ của",
        "key" : "fullName"
      },
      {
        "label" : "Nội dung yêu cầu giải quyết",
        "key" : "code"
      },
      {
        "label" : "địa chỉ",
        "key" : "address"
      },
      {
        "label" : "số điện thoại",
        "key" : "phoneNumber"
      },
      {
        "label" : "email",
        "key" : "email"
      },{
        "label": "Lý do",
        "key": "reason"
      }, 
      {
        "label": "Nội dung",
        "key": "description"
      },
      {
        "label": "Ông bà liên hệ với",
        "key": "nameOfficer"
      }
    ];
    this.procedureService.getChangeFileFormEform(blob, JSON.stringify(value), JSON.stringify(newComponent), type).subscribe(async data => {
      const dataType = data.type;
      const binaryData = [];
      binaryData.push(data);
      const blob = new Blob(binaryData, { type: dataType });
      let fileName = `Mẫu file yêu cầu bổ sung hồ sơ.${type}`;
      const file = new File(binaryData, fileName);
      if (file.size >= this.maxFileSize * 1024 * 1024) {
        const msgObj = {
          vi: 'Kích thước tệp tin quá lớn, tên tệp: ' + file.name,
          en: 'The file is too large, file name: ' + file.name
        };
        this.snackbarService.openSnackBar(0, '', msgObj[this.selectedLang], 'error_notification', this.config.expiredTime);
        return;
      }
      let newFile =[];
      newFile.push(file);
      this.files.push(file);
      this.urls.push(this.getFileIcon(type));
      
      const reader = new FileReader();
      reader.onload = () => {
        this.uploaded = true;
      };
      if (fileName.length > 20) {
        const startText = fileName.substring(0, 5);
        const shortText = fileName
          .substring(fileName.length - 7, fileName.length);
        this.fileNames.push(startText + '...' + shortText);
        this.fileNamesFull.push(fileName);
      } else {
        this.fileNames.push(fileName);
        this.fileNamesFull.push(fileName);
      }
      reader.readAsDataURL(blob);
      if (this.files.length > 0) {
        let checkFile = await this.uploadMultiFile(newFile, this.accountId);
        if (!checkFile) {
          const msgObj = {
            vi: 'Upload file thất bại!',
            en: 'File upload failed!'
          };
          this.snackbarService.openSnackBar(2, '', msgObj[this.selectedLang], 'warning_notification', this.config.expiredTime);
        }
        console.log("this.uploadFileNames",this.uploadFileNames);
      }
   }, err => {
      const msgObj = {
        vi: 'Lỗi trong quá trình kết xuất file',
        en: 'Error occurred when extract file'
      };
      this.snackbarService.openSnackBar(0, '', msgObj[this.selectedLang], 'error_notification', this.config.expiredTime);
      return;
   });
  }

  getProcedureProcessDetail(prDefId) {
    return new Promise<void>((resolve) => {
      this.procedureService.getProcedureProcessDetail(prDefId).subscribe((data) => {
        this.procedureProcessDetail = data;
        resolve();
      }, (err) => {
        const msgObj = {
          vi: 'Không tìm thấy quy trình cho hồ sơ này!',
          en: 'No process found for this dossier!',
        };
        this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
        resolve();
      });
    });
  }

  async getFileFromResult(){
    let listFile = this.dossierDetail?.attachment?.filter(d => d.group == this.config.dossierResultFileTagId);
    if(listFile?.length > 0){
      for(var index = 0; index < listFile.length; index ++) {
        var item = this.files.filter(fi => fi?.id == listFile[index]?.id);
        if(item.length > 0){
          continue;
        } 
        //Kiểm tra file đã tồn tại
        try {
          if(this.uploadFileNames){
            //File đã tồn tại
            if(!this.uploadFileNames.find(o => o.filename == listFile[index].filename && o.size == listFile[index].size)){
              await this.downloadFileFromResult(listFile[index]);
            }
          }
        } catch (error) {
          console.log(error);
        }
      }
    } else {
      this.snackbarService.openSnackBar(0, 'Không có file đính kèm ở kết quả', '', 'error_notification', this.config.expiredTime);
    }
  }

  downloadFileExport(index: number){
    let file = this.uploadFileNames[index];
    this.downloadFile(file.id, file.filename);
  }

  downloadFile(id, filename) {
    this.procedureService.downloadFile(id, this.dossierId).subscribe(data => {
      const dataType = data.type;
      const binaryData = [];
      binaryData.push(data);
      const downloadLink = document.createElement('a');
      downloadLink.href = window.URL.createObjectURL(new Blob(binaryData, { type: dataType }));
      downloadLink.setAttribute('download', filename);
      document.body.appendChild(downloadLink);
      downloadLink.click();
    }, err => {
      const msgObj = {
        vi: 'Không tìm thấy file!',
        en: 'File not found!'
      };
      this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
      console.log(err);
    });
  }

  routerLink(file)
  {
    if(!file) return;
    let name = file.filename ? file.filename: file.name;
    name = name.toUpperCase();
    if(name.indexOf('.JPG') >= 0 || name.indexOf('.JPEG') >= 0 || name.indexOf('.PNG') >= 0)
      return ['/viewer-image/' + file.id, {dossierId: this.dossierId }];

    return ['/viewer/' + file.id, {dossierId: this.dossierId }];
  }

  async openPdfDigitalSignatureV2( attach, index, dsType?:number){
    // dsType => 1: ký số sim, default: smart-ca
    // type => 1: ý kiến xử lý, 2: kết quả, 3: thành phần hồ sơ
    let originDocFileName = attach.name ? attach.name : attach.filename;
    let filePdf, filePdfName, dialogData, checkIsDocFile = false;
    if(originDocFileName.indexOf('.docx') !== -1 || originDocFileName.indexOf('.doc') !== -1){
      filePdf = await this.fileService.convertDoc2Pdf(attach.id);
      filePdfName = originDocFileName.replace('.docx', '.pdf').replace('.doc', '.pdf');
      checkIsDocFile = true;
      dialogData = new PdfViewerDialog(filePdf.id, filePdfName, this.dossierId, dsType);
    } else {
      dialogData = new PdfViewerDialog(attach.id, attach.name ? attach.name : attach.filename, this.dossierId, dsType);
    }
    const dialogRef = this.dialog.open(PdfViewerComponent, {
      maxWidth: '100%',
      width: '100%',
      height: '100%',
      maxHeight: '100%',
      data: dialogData,
      disableClose: false,
      panelClass: 'custom-dialog-container'
      // autoFocus: false
    });
    dialogRef.afterClosed().subscribe( async rs => {
      if(rs){
        
        for await (const file of this.uploadFileNames) {
          if (file.id == attach.id) {
            let fileCurrent = this.uploadFileNames.filter(item => item.id == attach.id)[0];
            let index = this.uploadFileNames.indexOf(fileCurrent);
            if (rs.filename.length > 20) {
              const startText = rs.filename.substring(0, 5);
              const shortText = rs.filename
                .substring(rs.filename.length - 7, rs.filename.length);
              this.fileNames = this.fileNames.map((item, i) => i === index ? (startText + '...' + shortText) : item);
              const indexType = rs.filename.lastIndexOf(".");
              const extention = rs.filename.substring(indexType + 1);
              this.urls = this.urls.map((item, i) => i === index ? this.getFileIcon(extention) : item);
            }
            file.filename = rs.filename;
            file.size = rs.size;
            file.id = rs.id;
          }
        }  
        // await this.addFileSign( rs.id, rs.filename, rs.size);
        this.logmanService.postSignHistory(rs.id,rs.filename).subscribe();
      }
    });
  }

  async addFileSign( fileId, filename, size){
    const file = {
      id: fileId,
      filename: filename,
      size: size,
    };
    if (file.size >= this.maxFileSize * 1024 * 1024) {
      const msgObj = {
        vi: 'Kích thước tệp tin quá lớn, tên tệp: ' + file.filename,
        en: 'The file is too large, file name: ' + file.filename
      };
      this.snackbarService.openSnackBar(0, '', msgObj[this.selectedLang], 'error_notification', this.config.expiredTime);
      return;
    }
    let newFile = [];
    newFile.push(file);
    if (newFile.length > 0) {
      await this.uploadFileExport(newFile, this.accountId);
    }
        
  }

  uploadFileExport(file, accountId) {
    this.procedureService.uploadMultiFile(file, accountId).subscribe(data => {
      this.uploadFileNames.push(data[0]);
      console.log("this.uploadFileNames",this.uploadFileNames);
    }, err => {
      console.log(err);
    });
  }

  openPdfDigitalSignature( fileId, filename, index) {
    if(filename.indexOf('.docx') !== -1 || filename.indexOf('.doc') !== -1){
      const file = {
        id: fileId,
        name: filename
      };
      this.openPdfDigitalSignatureV2( file, null, 2);
      return;
    }
    const dialogData = new PdfViewerDialog(fileId, filename, this.dossierId);
    const dialogRef = this.dialog.open(PdfViewerComponent, {
      maxWidth: '100%',
      width: '100%',
      height: '100%',
      maxHeight: '100%',
      data: dialogData,
      disableClose: false,
      panelClass: 'custom-dialog-container',
      // autoFocus: false
    });
    dialogRef.afterClosed().subscribe(async (rs) => {
      if(rs){
       
        for await (const file of this.uploadFileNames) {
          if (file.id == fileId) {
            let fileCurrent = this.uploadFileNames.filter(item => item.id == fileId)[0];
            let index = this.uploadFileNames.indexOf(fileCurrent);
            if (rs.filename.length > 20) {
              const startText = rs.filename.substring(0, 5);
              const shortText = rs.filename
                .substring(rs.filename.length - 7, rs.filename.length);
              this.fileNames = this.fileNames.map((item, i) => i === index ? (startText + '...' + shortText) : item);
              const indexType = rs.filename.lastIndexOf(".");
              const extention = rs.filename.substring(indexType + 1);
              this.urls = this.urls.map((item, i) => i === index ? this.getFileIcon(extention) : item);
            }
            file.filename = rs.filename;
            file.size = rs.size;
            file.id = rs.id;
          }
        }  
        
        // await this.addFileSign( rs.id, rs.filename, rs.size);
        this.logmanService.postSignHistory(rs.id,rs.filename).subscribe();
      }
    });
  }

  async openNEAC( attach, index, dsType?:number){
    this.openPdfDigitalSignatureV2( attach, index, dsType);
  }

  async openVGCAplugin( fileId, filename, size){
    this.tempObj.fileId = fileId;
    this.tempObj.filename = filename;
    this.tempObj.size = size;
    this.filePDF.id = fileId;
    this.filePDF.filename = filename;
    this.checkIsDocFile = false;
    if( filename.indexOf('.docx') !== -1 || filename.indexOf('.doc') !== -1){
      const  filePdf = await this.fileService.convertDoc2Pdf(fileId);
      this.filePDF.filename = filename.replace('.docx', '.pdf').replace('.doc', '.pdf');
      this.filePDF.id  = filePdf.id;
      this.checkIsDocFile = true;
    }
    this.handleBinaryString(fileId);
  }

  handleBinaryString(fileId){

    this.configService.downloadFile( this.filePDF.id, this.dossierId).subscribe( async (file) => {
      var prms = {};
      prms["FileUploadHandler"] = this.adapterService.getVGCAAdapterCallBackUrl(fileId,this.tempObj.filename,this.accountId);
      prms["SessionId"] = "";
      // Create a public link
      const formData: FormData = new FormData();
      file.name = "example.pdf";
      formData.append('file', file, file.name);
      const fileResponse = await  this.uploadService.uploadFile(formData).toPromise();
      this.tempObj.tempFileId = fileResponse.id;
      const publicFileUrl = `${this.uploadService.publicFileUrl}/${fileResponse.id}.pdf`;
      console.log("publicFileUrl",publicFileUrl);
      // prms["FileName"] = "https://staticv2.vnptigate.vn/file/cmnd.pdf";
      prms["FileName"] = publicFileUrl;
      var json_prms = JSON.stringify(prms);
      vgca_sign_approved(json_prms, this.SignFileCallBack);
    });
  }

  SignFileCallBack = async rv => {
    var received_msg = JSON.parse(rv);
    if(this.tempObj.tempFileId){
      this.uploadService.deleteFile(this.tempObj.tempFileId).subscribe();
    }
    switch(received_msg.Status){
      case (14):{
        // Cancel :: do nothing
        const msgObj = {
          vi: 'Đã hủy ký số!',
          en: 'Cancel sign document!'
        };
        this.snackbarService.openSnackBar(2, msgObj[this.selectedLang], '', 'warning_notification', this.config.expiredTime);
        break;
      }
      case true: {
        // Sign file successfully
        break;
      }
      case false: {
        // Failed to sign file
        const msgObj = {
          vi: 'Ký số thất bại!',
          en: 'Signing failed!'
        };
        this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
        break;
      }
      case 0: {
        if (received_msg.FileServer === ''){
          const msgObj = {
            vi: 'Đã hủy ký số!',
            en: 'Cancel sign document!'
          };
          this.snackbarService.openSnackBar(2, msgObj[this.selectedLang], '', 'warning_notification', this.config.expiredTime);
          break;
        }
        // Sign file successfully
        let file = this.uploadFileNames.filter(item => item.id == this.tempObj.fileId);
        let index = this.uploadFileNames.indexOf(file);
        
        const newFilename = this.changeFilenameOfSignedFile(this.filePDF.filename);
        // await this.addFileSign( received_msg.FileServer, newFilename, this.tempObj.size);
        for await (const file of this.uploadFileNames) {
          if (file.id == this.tempObj.fileId) {
            let fileCurrent = this.uploadFileNames.filter(item => item.id == this.tempObj.fileId)[0];
            let index = this.uploadFileNames.indexOf(fileCurrent);
            if (newFilename.length > 20) {
              const startText = newFilename.substring(0, 5);
              const shortText = newFilename
                .substring(newFilename.length - 7, newFilename.length);
              this.fileNames = this.fileNames.map((item, i) => i === index ? (startText + '...' + shortText) : item);
              const indexType = newFilename.lastIndexOf(".");
              const extention = newFilename.substring(indexType + 1);
              this.urls = this.urls.map((item, i) => i === index ? this.getFileIcon(extention) : item);
            }
            file.filename = newFilename;
            file.size = this.tempObj.size;
            file.id = received_msg.FileServer;
          }
        }  
        
        this.logmanService.postSignHistory(received_msg.FileServer, newFilename).subscribe();
  
        const msgObjNoti = {
          vi: 'Ký số thành công',
          en: 'Signed successfully'
        };
        this.snackbarService.openSnackBar(1, msgObjNoti[localStorage.getItem('language')], '', 'success_notification', this.config.expiredTime);
        break;
      }
      default: {
        // Error
        console.log("error: ",received_msg.Message);
        const msgObj = {
          vi: 'Ký số thất bại!',
          en: 'Signing failed!'
        };
        this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
      }
    }
    return received_msg;
  }


  changeFilenameOfSignedFile(fullFilename:string):string{
    const signalStr = "_signed";
    const index = fullFilename.lastIndexOf(".");
    const extention = fullFilename.substring(index+1);
    const filename = fullFilename.substring(0,index);
    if(filename.length < 8){
      return `${filename}${signalStr}.${extention}`;
    }
    if(filename.slice(-7) != signalStr){
      return `${filename}${signalStr}.${extention}`;
    }
    return fullFilename;
  }

  checkIfDocFileOnly(filename): boolean {
    // File type
    const fileExtention = filename.split('.').pop();
    if (fileExtention.toLowerCase() == 'doc' ||  fileExtention.toLowerCase() == 'docx') {
      return true;
    }
    return false;
  }

  async openVnptCaPlugin( fileId, filename){
    if(this.checkIfDocFileOnly(filename))
        this.openVnptCaPluginForDocFile( fileId, filename);
      else
        this.openVnptCaPluginForPdfFile( fileId, filename);
    }
    async openVnptCaPluginForDocFile( fileId, filename){
      const filePdf = await this.fileService.convertDoc2Pdf(fileId);
      const fileNameDigitalSign = filename.split('.').slice(0, -1).join('.') + ".pdf";
      const result = await this.digitalSignatureService.signWithVNPTCA(filePdf.id,fileNameDigitalSign, this.dossierId);
      console.log("result",result);
      if(result.status == 1){
        for await (const file of this.uploadFileNames) {
          if (file.id == fileId) {
            let fileCurrent = this.uploadFileNames.filter(item => item.id == fileId)[0];
            let index = this.uploadFileNames.indexOf(fileCurrent);
            if (result.data.filename.length > 20) {
              const startText = result.data.filename.substring(0, 5);
              const shortText = result.data.filename
                .substring(result.data.filename.length - 7, result.data.filename.length);
              this.fileNames = this.fileNames.map((item, i) => i === index ? (startText + '...' + shortText) : item);
              const indexType = result.data.filename.lastIndexOf(".");
              const extention = result.data.filename.substring(indexType + 1);
              this.urls = this.urls.map((item, i) => i === index ? this.getFileIcon(extention) : item);
            }
            file.filename = result.data.filename;
            file.size = result.data.size;
            file.id = result.data.fileId;
          }
        }  
        
      }
    }
    async openVnptCaPluginForPdfFile( fileId, filename){
      const result = await this.digitalSignatureService.signWithVNPTCA(fileId,filename, this.dossierId);
      console.log("result",result);
      if(result.status == 1){
        for await (const file of this.uploadFileNames) {
          if (file.id == fileId) {
            let fileCurrent = this.uploadFileNames.filter(item => item.id == fileId)[0];
            let index = this.uploadFileNames.indexOf(fileCurrent);
            if (result.data.filename.length > 20) {
              const startText = result.data.filename.substring(0, 5);
              const shortText = result.data.filename
                .substring(result.data.filename.length - 7, result.data.filename.length);
              this.fileNames = this.fileNames.map((item, i) => i === index ? (startText + '...' + shortText) : item);
              const indexType = result.data.filename.lastIndexOf(".");
              const extention = result.data.filename.substring(indexType + 1);
              this.urls = this.urls.map((item, i) => i === index ? this.getFileIcon(extention) : item);
            }
            file.filename = result.data.filename;
            file.size = result.data.size;
          }
        }    
          
      }
    }

    checkIfFileIsSupported(filename): boolean {
      // File type
      const fileExtention = filename.split('.').pop();
      if (fileExtention.toLowerCase() == 'pdf' || 'doc' || 'docx') {
        return true;
      }
      return false;
    }
 



 async downloadFileFromResult(file){
    this.procedureService.downloadFile(file?.id, this.dossierId).subscribe(async data => {
      const dataType = data.type;
      const binaryData = [];
      binaryData.push(data);
      var blob = new Blob(binaryData, { type: dataType });
      var myFile = new File([blob], file.filename)
      this.files.push(myFile);
      const extension = file.filename.substring(file.filename.lastIndexOf('.')).split('.').pop();
       this.urls.push(this.getFileIcon(extension));

      // const reader = new FileReader();
      // reader.onload = () => {
      //   this.uploaded = true;
      // };
      
      if (file.filename.length > 20) {
        const startText = file.filename.substr(0, 5);
        const shortText = file.filename.substring(file.filename.length - 7, file.filename.length);
        this.fileNames.push(startText + '...' + shortText);
        this.fileNamesFull.push(file.filename);
      } else {
        this.fileNames.push(file.filename);
        this.fileNamesFull.push(file.filename);
      }
      //reader.readAsDataURL(blob);
      if (myFile) {
        let checkFile = await this.uploadMultiFile([myFile], this.accountId);
        if (!checkFile) {
          const msgObj = {
            vi: 'Upload file thất bại!',
            en: 'File upload failed!'
          };
          this.snackbarService.openSnackBar(2, '', msgObj[this.selectedLang], 'warning_notification', this.config.expiredTime);
        }
      }
    });
  }

  setdossierTaskStatus() {
    return new Promise<void>(resolve => {
      const tagWaitingForApprovalStatusId = this.allowNextStepWaitingForApproval ? this.deploymentService.env.dossierTaskStatus.waitingForApprovalToAddition.id : this.deploymentService.env.dossierTaskStatus.requestForAdditionalDocuments.id;
      this.dossierService.getAgencyTag(tagWaitingForApprovalStatusId).subscribe(data => {
        this.dossierTaskStatusWaitingForApproval.id = data.id;
        this.dossierTaskStatusWaitingForApproval.name = data.trans;
        resolve();
      }, err => {
        resolve();
      });
    });

  }

  setdossierTaskStatusRemind () {
    return new Promise<void>(resolve => {
      const tagWaitingForApprovalRemindId = this.allowNextStepWaitingForApproval ? this.deploymentService.env.dossierMenuTaskRemind.waitingForApprovalToAddition.id : this.deploymentService.env.dossierMenuTaskRemind.requestForAdditional.id;
      this.dossierService.getAgencyTag(tagWaitingForApprovalRemindId).subscribe(data => {
        this.dossierMenuTaskRemindWaitingForApproval.id = data.id;
        this.dossierMenuTaskRemindWaitingForApproval.name = data.trans;
        this.statusName = data?.trans.filter(t => t.languageId === Number(this.selectedLangId))?.name;
        resolve();
      }, err => {
        resolve();
      });
    });
  }

  //ket qua xu ly
  async putDossierAttachmentMulti(doneClass, type, rbk, oldName, newName, dataFile){
    const rs = [];
    //get dossier online 
    let dossierOnline = await this.dossierService.getDossierDetailKGG(this.dossierId)

    if(dossierOnline?.attachment !== null && dossierOnline?.attachment !== undefined){
      const attachment = dossierOnline.attachment;
      if (attachment && attachment.length > 0) {
        //check id include in attachment
        attachment.forEach(element => {
          rs.push(element);
        });
      }
    }

    //check include file
    if(type === 5) {
      // delete file in rs if id include in dataFile
      for (let i = 0; i < dataFile.length; i++) {
        if(rs.findIndex(x => x.id === dataFile[i].id) !== -1){
          rs.splice(rs.findIndex(x => x.id === dataFile[i].id), 1);
        }
      }
    } else {
      if(dataFile.length > 0){
        for (let i = 0; i < dataFile.length; i++) {
          //check dataFile[i].id include in rs
          if(rs.findIndex(x => x.id === dataFile[i].id) === -1){
            rs.push({
              id: dataFile[i].id,
              filename: dataFile[i].filename,
              uuid: dataFile[i].uuid,
              size: dataFile[i].size,
              group: this.config.dossierResultFileTagId
            });
          }
        }
      } else {
        const msgObj = {
          vi: 'Vui lòng chọn file!',
          en: 'Please choose file!'
        };

        this.snackbarService.openSnackBar(0, '', msgObj[this.selectedLang], 'error_notification', this.config.expiredTime);
        return;
      }
    }

    const putBody = {
      attachment: rs
    };

   const requestBody = JSON.stringify(putBody, null, 2);
    this.dossierService.putDossierOnline(this.dossierId, requestBody).subscribe(data => {
      if (data.affectedRows === 1) {
        for (let j = 0; j < dataFile.length; j++) {
          this.postHistoryV2(type, rbk, oldName, JSON.stringify({
            id: dataFile[j].id,
            type: 'file',
            name: dataFile[j].filename,
            uuid: dataFile[j].uuid,
          }));
        }
        // setTimeout(() => {
        //   this.showResult(doneClass);
        //   setTimeout(() => {
        //     this.hideSpinner(doneClass);
        //   }, 1000);
        // }, 1000);

      } else {
        const msgObj = {
          vi: 'Cập nhật thất bại!',
          en: 'Update failed!'
        };
        this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
        // setTimeout(() => {
        //   this.hideSpinner(doneClass);
        // }, 1000);
      }
    });
  }
  //
  postHistoryV2(type, col, oldVal, newVal) {
    const body = {
      groupId: 1,
      itemId: this.dossierId,
      user: {
        id: this.accountId,
        name: this.userName
      },
      type,
      deploymentId: this.config.deploymentId,
      action: [
        {
          fieldNameRbk: col,
          originalValue: oldVal,
          newValue: newVal
        }
      ]
    };
    const requestBody = JSON.stringify(body, null, 2);
    this.dossierService.postHistory(requestBody).subscribe(data => {
      if (data.affectedRows === 1) {
        // Post successful
        console.log("Post successful");
      } else {
        // Post failed
        console.log("Post failed");
      }
    });
  }

}

export class ConfirmAdditionalRequirementDialogModel {
  constructor(public dossierId: string, public dossierCode: string, public typeProcess?) {
  }
}


