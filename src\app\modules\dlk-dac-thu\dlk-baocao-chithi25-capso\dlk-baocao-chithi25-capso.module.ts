import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { DlkBaocaoChithi25CapSoRoutingModule } from './dlk-baocao-chithi25-capso-routing.module';
import { DlkBaocaoChithi25CapSoComponent } from './dlk-baocao-chithi25-capso.component';
import { SharedModule } from 'src/app/shared/shared.module';
import { NgxPrinterModule } from 'ngx-printer';
import {DossierDetailComponent} from '../dialogs/view-detail.component';


@NgModule({
  declarations: [DlkBaocaoChithi25CapSoComponent],
  imports: [
    CommonModule,
    DlkBaocaoChithi25CapSoRoutingModule,    
    SharedModule,
    
    
    NgxPrinterModule.forRoot({ printOpenWindow: true })
  ]
})
export class DlkBaocaoChithi25CapSoModule { }
