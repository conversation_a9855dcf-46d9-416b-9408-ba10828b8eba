import { Injectable } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { ApiProviderService } from 'src/app/core/service/api-provider.service';
import { Observable } from 'rxjs';
import { SnackbarService } from 'data/service/snackbar/snackbar.service';
import { EnvService } from 'core/service/env.service';
import { Workbook } from 'exceljs';
import * as fs from 'file-saver';
import { each } from 'jquery';

const EXCEL_TYPE = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8';
const EXCEL_EXTENSION = '.xlsx';

@Injectable({
  providedIn: 'root'
})
export class DLKStatisticsService {
  language = localStorage.getItem('language') === 'vi' ? 228 : 46;
  config = this.envService.getConfig();

  constructor(
    private dialog: MatDialog,
    private http: HttpClient,
    private apiProviderService: ApiProviderService,
    private snackbar: SnackbarService,
    private envService: EnvService
  ) { }

  private basecatURL = this.apiProviderService.getUrl('digo', 'basecat');
   private basedataURL = this.apiProviderService.getUrl('digo', 'basedata');
   private basepadURL = this.apiProviderService.getUrl('digo', 'basepad');
   private padmanURL = this.apiProviderService.getUrl('digo', 'padman');
   //private padmanURL = "http://localhost:8081";
  // private basedataURL = "http://localhost:8888";
  // private basepadURL = "http://localhost:8069";
  private humanURL = this.apiProviderService.getUrl('digo', 'human')
  private statisticsURL = this.apiProviderService.getUrl('digo', 'statistics');
  getAgencyFully(id): Promise<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.set('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.get(this.basedataURL + `/agency/${id}/--fully`, { headers }).toPromise();
  }
  getDataBaocao(id, tuNgay, denNgay, tuNgayLuyKe, denNgayLuyKe): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');

    return this.http.get(this.padmanURL + '/dlk-chithi08/dlk-ct08-data?id='+ id + '&tuNgay=' + tuNgay + '&denNgay=' + denNgay + '&tuNgayLuyKe=' + tuNgayLuyKe + '&denNgayLuyKe=' + denNgayLuyKe, { headers }).pipe();
  }
  getListAgencyWithCT08(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.basedataURL + '/agency/tree-view?keyword=&agencyName=&ancestor-id=' + id + '&code=&status=&levelId=&phone=&parent-id=&tag-id=&sort=', { headers });
  }
  getDataBaocaoDetail(prid, id, prop, type, level, tuNgay, denNgay, page, size): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');

    return this.http.get(this.padmanURL + '/dlk-chithi08/dlk-ct08-data-detail?prid=' + prid + '&id=' + id + '&prop=' + prop  + '&type=' + type + '&level=' + level + '&tuNgay=' + tuNgay + '&denNgay=' + denNgay + '&page=' + page + '&size=' + size, { headers }).pipe();
  }
  getDataBaocaoCT08SH(id, level, tuNgay, denNgay, type): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');

    return this.http.get(this.padmanURL + '/dlk-chithi08/dlk-ct08-sh-data?id=' + id + '&level=' + level + '&type=' + type + '&tuNgay=' + tuNgay + '&denNgay=' + denNgay, { headers }).pipe();
  }
  getDataBaocaoCT08Xa(id, tuNgay, denNgay): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');

    return this.http.get(this.padmanURL + '/dlk-chithi08/dlk-ct08-xa-data?id='+ id + '&tuNgay=' + tuNgay + '&denNgay=' + denNgay, { headers }).pipe();
  }
  getTotalProcedureByALLAgency(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');

    return this.http.get(this.basepadURL + '/procedure/report-dlk-processing-all'+ searchString , { headers }).pipe();
  }
  getDeatailProcedureByAgency(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');

    return this.http.get(this.basepadURL + '/procedure/report-dlk-detail-procedure'+ searchString , { headers }).pipe();
  }
  getListProcedureByAgencyDLK(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.basepadURL + '/procedure/--find-all-procedure-qni'+ searchString, { headers }).pipe();
  }
   getlistDossier(body): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');

    return this.http.post(this.padmanURL + '/dlk-chithi08/dlk-ct08',  body, { headers }).pipe();
  }

  getlistDossierCT25ToanSo(body): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');

    return this.http.post(this.padmanURL + '/dlk-chithi25-2cap/dlk-ct25-ts',  body, { headers }).pipe();
  }

  getlistDossierCT25ToanXa(body): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');

    return this.http.post(this.padmanURL + '/dlk-chithi25-2cap/dlk-ct25-tx',  body, { headers }).pipe();
  }

  getlistDossierCT25CapXa(body): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');

    return this.http.post(this.padmanURL + '/dlk-chithi25-2cap/dlk-ct25-dv',  body, { headers }).pipe();
  }

  getlistDossierCT25(body): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');

    return this.http.post(this.padmanURL + '/dlk-chithi25/dlk-ct25',  body, { headers }).pipe();
  }
  getlistDossierCT25SoHuyen(body): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');

    return this.http.post(this.padmanURL + '/dlk-chithi25/dlk-ct25-sh',  body, { headers }).pipe();
  }
  getlistDossierCT25DonVi(body): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');

    return this.http.post(this.padmanURL + '/dlk-chithi25/dlk-ct25-dv',  body, { headers }).pipe();
  }
  getListAgencyWithLevel25(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.basedataURL + '/agency/tree-view?keyword=&agencyName=&ancestor-id=' + id + '&code=&status=&levelId=&phone=&parent-id=&tag-id=&sort=', { headers });
  }
  getlistDossierVil(body): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');

    return this.http.post(this.padmanURL + '/dlk-chithi08/dlk-ct08-capxa',  body, { headers }).pipe();
  }
  getlistDossierGrProcedure(body): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');

    return this.http.post(this.padmanURL + '/dlk-chithi08/dlk-ct08-by-procedure',  body, { headers }).pipe();
  }
  getListAgencyByParentId(search): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.basedataURL + '/agency/--by-parent-id?parent-id=' + search, { headers }).pipe();
  }

  getTotalProcedureofDossier(body): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');

    return this.http.post(this.padmanURL + '/dlk-chithi08/dlk-ct08-procudure',  body, { headers }).pipe();
  }
  getProcedureofDossier(body): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');

    return this.http.post(this.padmanURL + '/dlk-chithi08/dlk-ct08-list-procudure-dossier',  body, { headers }).pipe();
  }
  getlistDetailofDossier(body): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.post(this.padmanURL + '/dlk-chithi08/dlk-ct08-dossier-detail'+ '?page=' + body.page + '&size=' + body.size,  body, { headers }).pipe();
  }
  getlistDetailof25Dossier(body): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.post(this.padmanURL + '/dlk-chithi25/dlk-ct25-dossier-detail'+ '?page=' + body.page + '&size=' + body.size,  body, { headers }).pipe();
  }
  getlistDetailof25SHDossier(body): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.post(this.padmanURL + '/dlk-chithi25/dlk-ct25-sh-dossier-detail'+ '?page=' + body.page + '&size=' + body.size,  body, { headers }).pipe();
  }
  getUserExperience(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.humanURL + '/user/' + id + '/experience', { headers });
  }

  getAgencyList(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.get(this.basedataURL + '/agency/--search' + searchString, { headers }).pipe();
  }

  getDossierProcessingStatisticDLK(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.get(this.padmanURL + '/dlk-dossier-statistic/statistic-processing' + searchString, { headers }).pipe();
  }

  getDossierProcessingStatisticDLKDay(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.get(this.statisticsURL + '/dlk-dossier-statistic/--statistic-processing' + searchString, { headers }).pipe();
  }

  getDetailDossier(search): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.padmanURL + '/dlk-dossier-statistic/digitization-detail' + search, { headers }).pipe();
  }

  getDetailDossierDay(search): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.statisticsURL + '/dlk-dossier-statistic/digitization-detail' + search, { headers }).pipe();
  }

  exportDossierStatisticDetail(params: string): any {
    const url = this.padmanURL + '/dlk-dossier-statistic/digitization-detail/--excel' + params;
    this.getFileExport(url).then();
  }

  exportDossierStatisticDetailDay(params: string): any {
    const url = this.statisticsURL + '/dlk-dossier-statistic/digitization-detail/--excel' + params;
    this.getFileExport(url).then();
  }

  getTotalByAgencyDLK(search: String): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.basepadURL + '/procedure/report-dlk-processing/--group-by-agency' + search, { headers });
  }

  getFileExport(url) {
    return new Promise((resolve) => {
      this.http.get(url, {
        observe: 'response',
        responseType: 'blob'
      }).toPromise().then(res => {
        const blob = new Blob([res.body], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
        let filename = 'bao_cao_chi_tiet_so_hoa.xlsx';
        if (res.headers.get('Content-Disposition') != null) {
          filename = res.headers.get('Content-Disposition').split(';')[1].split('=')[1].replace(/\"/g, '');
        }
        const blobUrl = URL.createObjectURL(blob);

        const xhr = new XMLHttpRequest();
        xhr.responseType = 'blob';

        xhr.onload = () => {
          const recoveredBlob = xhr.response;
          const reader = new FileReader();
          reader.onload = () => {
            const base64data = reader.result.toString();
            const object = JSON.stringify({
              mimeType: filename.substring(filename.lastIndexOf('.') + 1),
              name: filename.substring(0, filename.lastIndexOf('.')),
              data: base64data.split(',')[1]
            });
            const anchor = document.createElement('a');
            anchor.download = filename;
            anchor.href = base64data;
            anchor.click();
          };
          reader.readAsDataURL(recoveredBlob);
        };

        xhr.open('GET', blobUrl);
        xhr.send();
        resolve(true);
      }).catch(err => {
        if (err.status === 500) {
          const message = {
            vi: 'Hệ thống tạm thời không thể xuất file excel!',
            en: 'The system is temporarily unable to export the excel file!'
          };
          this.snackbar.openSnackBar(0, '', message[localStorage.getItem('language')], 'error_notification',
            this.config.expiredTime);
        }
        resolve(false);
      });
    });
  }

  public exportAsExcelProcessingReport(
    reportHeading: string,
    reportSubHeading: string,
    json: any[],
    footerData: any[],
    excelFileName: string,
    sheetName: string,
  ) {
    const data = json;
    console.log(data);
    // create workbook and worksheet
    const workbook = new Workbook();
    workbook.creator = 'Snippet Coder';
    workbook.lastModifiedBy = 'SnippetCoder';
    workbook.created = new Date();
    workbook.modified = new Date();
    const worksheet = workbook.addWorksheet(sheetName);

    worksheet.getColumn('A').font = { name: 'Times New Roman', size: 12 };
    worksheet.getColumn('B').font = { name: 'Times New Roman', size: 12 };
    worksheet.getColumn('C').font = { name: 'Times New Roman', size: 12 };
    worksheet.getColumn('D').font = { name: 'Times New Roman', size: 12 };
    worksheet.getColumn('E').font = { name: 'Times New Roman', size: 12 };
    worksheet.getColumn('F').font = { name: 'Times New Roman', size: 12 };
    worksheet.getColumn('G').font = { name: 'Times New Roman', size: 12 };
    worksheet.getColumn('H').font = { name: 'Times New Roman', size: 12 };
    worksheet.getColumn('I').font = { name: 'Times New Roman', size: 12 };
    worksheet.getColumn('J').font = { name: 'Times New Roman', size: 12 };
    worksheet.getColumn('K').font = { name: 'Times New Roman', size: 12 };
    worksheet.getColumn('L').font = { name: 'Times New Roman', size: 12 };
    worksheet.getColumn('M').font = { name: 'Times New Roman', size: 12 };
    worksheet.getColumn('N').font = { name: 'Times New Roman', size: 12 };
    worksheet.getColumn('O').font = { name: 'Times New Roman', size: 12 };
    worksheet.getColumn('P').font = { name: 'Times New Roman', size: 12 };
    worksheet.getColumn('Q').font = { name: 'Times New Roman', size: 12 };
    worksheet.getColumn('R').font = { name: 'Times New Roman', size: 12 };
    worksheet.getColumn('S').font = { name: 'Times New Roman', size: 12 };
    worksheet.getColumn('T').font = { name: 'Times New Roman', size: 12 };
    worksheet.getColumn('U').font = { name: 'Times New Roman', size: 12 };
    worksheet.getColumn('V').font = { name: 'Times New Roman', size: 12 };
    worksheet.getColumn('W').font = { name: 'Times New Roman', size: 12 };
    worksheet.getColumn('X').font = { name: 'Times New Roman', size: 12 };
    worksheet.getColumn('Y').font = { name: 'Times New Roman', size: 12 };
    worksheet.getColumn('Z').font = { name: 'Times New Roman', size: 12 };
    worksheet.getColumn('AA').font = { name: 'Times New Roman', size: 12 };
    worksheet.getColumn('AB').font = { name: 'Times New Roman', size: 12 };
    worksheet.getColumn('AC').font = { name: 'Times New Roman', size: 12 };
    // worksheet.getColumn('AD').font = { name: 'Times New Roman', size: 12 };
    // worksheet.getColumn('AE').font = { name: 'Times New Roman', size: 12 };

    worksheet.getColumn('A').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getColumn('B').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getColumn('C').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getColumn('D').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getColumn('E').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getColumn('F').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getColumn('G').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getColumn('H').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getColumn('I').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getColumn('J').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getColumn('K').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getColumn('L').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getColumn('M').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getColumn('N').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getColumn('O').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getColumn('P').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getColumn('Q').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getColumn('R').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getColumn('S').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getColumn('T').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getColumn('U').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getColumn('V').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getColumn('W').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getColumn('X').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getColumn('Y').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getColumn('Z').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getColumn('AA').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getColumn('AB').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getColumn('AC').alignment = { horizontal: 'center', vertical: 'middle' };
    // worksheet.getColumn('AD').alignment = { horizontal: 'center', vertical: 'middle' };
    // worksheet.getColumn('AE').alignment = { horizontal: 'center', vertical: 'middle' };

    // worksheet.getColumn('A').width = 30;
    worksheet.getColumn('B').width = 30;
    worksheet.getColumn('C').width = 10;
    worksheet.getColumn('D').width = 10;
    worksheet.getColumn('E').width = 10;
    worksheet.getColumn('F').width = 10;
    worksheet.getColumn('G').width = 10;
    worksheet.getColumn('H').width = 10;
    worksheet.getColumn('I').width = 10;
    worksheet.getColumn('J').width = 10;
    worksheet.getColumn('K').width = 10;
    worksheet.getColumn('L').width = 10;
    worksheet.getColumn('M').width = 10;
    worksheet.getColumn('N').width = 10;
    worksheet.getColumn('O').width = 10;
    worksheet.getColumn('P').width = 10;
    worksheet.getColumn('Q').width = 10;
    worksheet.getColumn('R').width = 10;
    worksheet.getColumn('S').width = 10;
    worksheet.getColumn('T').width = 10;
    worksheet.getColumn('U').width = 10;
    worksheet.getColumn('V').width = 10;
    worksheet.getColumn('W').width = 10;
    worksheet.getColumn('X').width = 10;
    worksheet.getColumn('Y').width = 10;
    worksheet.getColumn('Z').width = 10;
    worksheet.getColumn('AA').width = 10;
    worksheet.getColumn('AB').width = 10;
    worksheet.getColumn('AC').width = 10;
    // worksheet.getColumn('AD').width = 10;
    // worksheet.getColumn('AE').width = 10;

    // Add header row
    worksheet.addRow([]);
    worksheet.mergeCells('A1:AC1');
    worksheet.getCell('A1').value = reportHeading;
    worksheet.getCell('A1').alignment = { horizontal: 'center', vertical: 'middle' };

    worksheet.addRow([]);
    worksheet.mergeCells('A2:AC2');
    worksheet.getCell('A2').value = reportSubHeading;
    worksheet.getCell('A2').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getCell('A2').font = { size: 12, italic: true, name: 'Times New Roman' };

    worksheet.mergeCells('A4:A8');
    worksheet.getCell('A4').value = 'STT';
    worksheet.mergeCells('B4:B8');
    worksheet.getCell('B4').value = 'Tên cơ quan';
    // worksheet.mergeCells('C4:C8');
    // worksheet.getCell('C4').value = 'Ngày triển khai';
    worksheet.mergeCells('C4:O4');
    worksheet.getCell('C4').value = 'Tình hình triển khai';
    worksheet.mergeCells('P4:AC4');
    worksheet.getCell('P4').value = 'Tình hình xử lý';
    // worksheet.mergeCells('AE4:AE8');
    // worksheet.getCell('AE4').value = 'Ghi chú';
    worksheet.mergeCells('C5:D6');
    worksheet.getCell('C5').value = 'Tổng số';
    worksheet.mergeCells('E5:O5');
    worksheet.getCell('E5').value = 'Số hồ sơ tiếp nhận | thủ tục đã thực hiện';
    worksheet.mergeCells('P5:P8');
    worksheet.getCell('P5').value = 'Hồ sơ tồn';
    worksheet.mergeCells('Q5:R6');
    worksheet.getCell('Q5').value = 'Tiếp nhận';
    worksheet.mergeCells('S5:AA6');
    worksheet.getCell('S5').value = 'Đã giải quyết';
    worksheet.mergeCells('AB5:AB8');
    worksheet.getCell('AB5').value = 'Đang giải quyết';
    worksheet.mergeCells('AC5:AC8');
    worksheet.getCell('AC5').value = 'Tỷ lệ giải quyết';
    worksheet.mergeCells('E6:G6');
    worksheet.getCell('E6').value = 'Một phần (trực tiếp)';
    worksheet.mergeCells('H6:K6');
    worksheet.getCell('H6').value = 'Một phần (trực tuyến)';
    worksheet.mergeCells('L6:O6');
    worksheet.getCell('L6').value = 'Toàn trình';
    worksheet.mergeCells('Q7:Q8');
    worksheet.getCell('Q7').value = 'MCĐT';
    worksheet.mergeCells('R7:R8');
    worksheet.getCell('R7').value = 'Trực tuyến';
    worksheet.mergeCells('S7:U7');
    worksheet.getCell('S7').value = 'Một phần (trực tiếp)';
    worksheet.mergeCells('V7:X7');
    worksheet.getCell('V7').value = 'Một phần (trực tuyến)';
    worksheet.mergeCells('Y7:AA7');
    worksheet.getCell('Y7').value = 'Toàn trình';

    worksheet.mergeCells('C7:C8');
    worksheet.getCell('C8').value = 'HSTN';
    worksheet.mergeCells('D7:D8');
    worksheet.getCell('D8').value = 'TTHC';
    worksheet.mergeCells('E7:E8');
    worksheet.getCell('E8').value = 'HSTN';
    worksheet.mergeCells('F7:F8');
    worksheet.getCell('F8').value = 'HSTN BCCI';
    worksheet.mergeCells('G7:G8');
    worksheet.getCell('G8').value = 'TTHC';
    worksheet.mergeCells('H7:H8');
    worksheet.getCell('H8').value = 'HSTN trực tiếp';
    worksheet.mergeCells('I7:I8');
    worksheet.getCell('I8').value = 'HSTN trực tuyến';
    worksheet.mergeCells('J7:J8');
    worksheet.getCell('J8').value = 'HSTN BCCI';
    worksheet.mergeCells('K7:K8');
    worksheet.getCell('K8').value = 'TTHC';
    worksheet.mergeCells('L7:L8');
    worksheet.getCell('L8').value = 'HSTN trực tiếp';
    worksheet.mergeCells('M7:M8');
    worksheet.getCell('M8').value = 'HSTN trực tuyến';
    worksheet.mergeCells('N7:N8');
    worksheet.getCell('N8').value = 'HSTN BCCI';
    worksheet.mergeCells('O7:O8');
    worksheet.getCell('O8').value = 'TTHC';

    worksheet.getCell('S8').value = 'Trước hạn';
    worksheet.getCell('T8').value = 'Đúng hạn';
    worksheet.getCell('U8').value = 'Trễ hạn';
    worksheet.getCell('V8').value = 'Trước hạn';
    worksheet.getCell('W8').value = 'Đúng hạn';
    worksheet.getCell('X8').value = 'Trễ hạn';
    worksheet.getCell('Y8').value = 'Trước hạn';
    worksheet.getCell('Z8').value = 'Đúng hạn';
    worksheet.getCell('AA8').value = 'Trễ hạn';

    worksheet.getCell('A9').value = '(1)';
    worksheet.getCell('B9').value = '(2)';
    worksheet.getCell('C9').value = '(3)';
    worksheet.getCell('D9').value = '(4)';
    worksheet.getCell('E9').value = '(5)';
    worksheet.getCell('F9').value = '(6)';
    worksheet.getCell('G9').value = '(7)';
    worksheet.getCell('H9').value = '(8)';
    worksheet.getCell('I9').value = '(9)';
    worksheet.getCell('J9').value = '(10)';
    worksheet.getCell('K9').value = '(11)';
    worksheet.getCell('L9').value = '(12)';
    worksheet.getCell('M9').value = '(13)';
    worksheet.getCell('N9').value = '(14)';
    worksheet.getCell('O9').value = '(15)';
    worksheet.getCell('P9').value = '(16)';
    worksheet.getCell('Q9').value = '(17)';
    worksheet.getCell('R9').value = '(18)';
    worksheet.getCell('S9').value = '(19)';
    worksheet.getCell('T9').value = '(20)';
    worksheet.getCell('U9').value = '(21)';
    worksheet.getCell('V9').value = '(22)';
    worksheet.getCell('W9').value = '(23)';
    worksheet.getCell('X9').value = '(24)';
    worksheet.getCell('Y9').value = '(25)';
    worksheet.getCell('Z9').value = '(26';
    worksheet.getCell('AA9').value = '(27)';
    worksheet.getCell('AB9').value = '(28)';
    worksheet.getCell('AC9').value = '(29)';
    // worksheet.getCell('AD9').value = '(30)';
    // worksheet.getCell('AE9').value = '(31)';

    worksheet.properties.outlineLevelCol = 2; worksheet.properties.defaultRowHeight = 15;
    let i = 4;
    const j = 9;
    for (i; i <= j; i++) {
      let k = 1;
      const l = 29;
      for (k; k <= l; k++) {
        if( worksheet.findCell(i, k)){
          worksheet.findCell(i, k).alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
          worksheet.findCell(i, k).fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: 'C0C0C0C0' },
            bgColor: { argb: 'FF0000FF' }
          };
          worksheet.findCell(i, k).border = { top: { style: 'thin' }, left: { style: 'thin' }, bottom: { style: 'thin' }, right: { style: 'thin' } };
          worksheet.findCell(i, k).font = { size: 12, bold: true, name: 'Times New Roman' };
        }
      }
    }

    // get all columns from JSON
    let columnsArray: any[];
    for (const key in json) {
      if (json.hasOwnProperty(key)) {
        columnsArray = Object.keys(json[key]);
      }
    }
    columnsArray.pop();
    // Add Data and Conditional Formatting
    data.forEach((element: any) => {
      const eachRow = [];
      columnsArray.forEach((column) => {
        eachRow.push(element[column]);
      });
      eachRow.splice(0, 1);
      const borderrow = worksheet.addRow(eachRow);
      borderrow.eachCell((cell) => {
        cell.border = { top: { style: 'thin' }, left: { style: 'thin' }, bottom: { style: 'thin' }, right: { style: 'thin' } };
        cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
      });
    });

    // footer data row
    if (footerData != null) {
      footerData.forEach((element: any) => {
        const eachRow = [];
        element.forEach((val: any) => {
          eachRow.push(val);
        });
        const footerRow = worksheet.addRow(eachRow);
        const cellMerge = 'A' + footerRow.number + ':B' + footerRow.number;
        worksheet.mergeCells(cellMerge);
        footerRow.eachCell((cell) => {
          cell.font = { size: 13, bold: true, name: 'Times New Roman' };
          cell.border = { top: { style: 'thin' }, left: { style: 'thin' }, bottom: { style: 'thin' }, right: { style: 'thin' } };
          cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
        });
      });
    }

    // tslint:disable-next-line:no-shadowed-variable
    workbook.xlsx.writeBuffer().then((data: ArrayBuffer) => {
      const blob = new Blob([data], { type: EXCEL_TYPE });
      fs.saveAs(blob, excelFileName + EXCEL_EXTENSION);
    });
  }

  public exportAsExcelDigitizationReport(
    reportHeading: string,
    reportSubHeading: string,
    json: any[],
    footerData: any[],
    excelFileName: string,
    sheetName: string,
  ) {
    const data = json;
    // create workbook and worksheet
    const workbook = new Workbook();
    workbook.creator = 'Snippet Coder';
    workbook.lastModifiedBy = 'SnippetCoder';
    workbook.created = new Date();
    workbook.modified = new Date();
    const worksheet = workbook.addWorksheet(sheetName);

    worksheet.getColumn('A').font = { name: 'Times New Roman', size: 12 };
    worksheet.getColumn('B').font = { name: 'Times New Roman', size: 12 };
    worksheet.getColumn('C').font = { name: 'Times New Roman', size: 12 };
    worksheet.getColumn('D').font = { name: 'Times New Roman', size: 12 };
    worksheet.getColumn('E').font = { name: 'Times New Roman', size: 12 };
    worksheet.getColumn('F').font = { name: 'Times New Roman', size: 12 };
    worksheet.getColumn('G').font = { name: 'Times New Roman', size: 12 };
    worksheet.getColumn('H').font = { name: 'Times New Roman', size: 12 };
    worksheet.getColumn('I').font = { name: 'Times New Roman', size: 12 };
    worksheet.getColumn('J').font = { name: 'Times New Roman', size: 12 };
    worksheet.getColumn('K').font = { name: 'Times New Roman', size: 12 };
    worksheet.getColumn('L').font = { name: 'Times New Roman', size: 12 };
    worksheet.getColumn('M').font = { name: 'Times New Roman', size: 12 };
    worksheet.getColumn('N').font = { name: 'Times New Roman', size: 12 };

    worksheet.getColumn('A').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getColumn('B').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getColumn('C').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getColumn('D').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getColumn('E').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getColumn('F').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getColumn('G').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getColumn('H').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getColumn('I').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getColumn('J').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getColumn('K').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getColumn('L').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getColumn('M').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getColumn('N').alignment = { horizontal: 'center', vertical: 'middle' };

    // worksheet.getColumn('A').width = 30;
    worksheet.getColumn('B').width = 40;
    worksheet.getColumn('C').width = 15;
    worksheet.getColumn('D').width = 15;
    worksheet.getColumn('E').width = 15;
    worksheet.getColumn('F').width = 15;
    worksheet.getColumn('G').width = 15;
    worksheet.getColumn('H').width = 15;
    worksheet.getColumn('I').width = 15;
    worksheet.getColumn('J').width = 15;
    worksheet.getColumn('K').width = 15;
    worksheet.getColumn('L').width = 15;
    worksheet.getColumn('M').width = 15;
    worksheet.getColumn('N').width = 15;

    // Add header row
    worksheet.addRow([]);
    worksheet.mergeCells('A1:N1');
    worksheet.getCell('A1').value = reportHeading;
    worksheet.getCell('A1').alignment = { horizontal: 'center', vertical: 'middle' };

    worksheet.addRow([]);
    worksheet.mergeCells('A2:N2');
    worksheet.getCell('A2').value = reportSubHeading;
    worksheet.getCell('A2').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getCell('A2').font = { size: 12, italic: true, name: 'Times New Roman' };

    worksheet.mergeCells('A4:A5');
    worksheet.getCell('A4').value = 'STT';
    worksheet.mergeCells('B4:B5');
    worksheet.getCell('B4').value = 'Tên cơ quan';
    worksheet.mergeCells('C4:H4');
    worksheet.getCell('C4').value = 'Số hóa thành phần hồ sơ (TPHS)';
    worksheet.mergeCells('I4:N4');
    worksheet.getCell('I4').value = 'Số hóa kết quả giải quyết TTHC';

    worksheet.getCell('C5').value = 'Số HS tiếp nhận';
    worksheet.getCell('D5').value = 'Số HS chưa số hóa TPHS';
    worksheet.getCell('E5').value = 'Số HS có số hóa TPHS';
    worksheet.getCell('F5').value = 'Tỷ lệ số hóa TPHS';
    worksheet.getCell('G5').value = 'Số HS số hóa đầy đủ TPHS';
    worksheet.getCell('H5').value = 'Tỷ lệ số hóa đầy đủ TPHS';
    worksheet.getCell('I5').value = 'Số HS đã giải quyết';
    worksheet.getCell('J5').value = 'Số HS chưa số hóa kết quả giải quyết TTHC';
    worksheet.getCell('K5').value = 'Số HS có số hoá kết quả giải quyết TTHC';
    worksheet.getCell('L5').value = 'Tỷ lệ số hoá kết quả giải quyết TTHC';
    worksheet.getCell('M5').value = 'Số HS có số hoá TPHS và kết quả giải quyết TTHC';
    worksheet.getCell('N5').value = 'Tỷ lệ có số hoá TPHS và kết quả giải quyết TTHC';

    worksheet.getCell('A6').value = '(1)';
    worksheet.getCell('B6').value = '(2)';
    worksheet.getCell('C6').value = '(3)';
    worksheet.getCell('D6').value = '(4)';
    worksheet.getCell('E6').value = '(5)';
    worksheet.getCell('F6').value = '(6)=(5)/(3)';
    worksheet.getCell('G6').value = '(7)';
    worksheet.getCell('H6').value = '(8)=(7)+(3)';
    worksheet.getCell('I6').value = '(9)';
    worksheet.getCell('J6').value = '(10)';
    worksheet.getCell('K6').value = '(11)';
    worksheet.getCell('L6').value = '(12)=(11)/(9)';
    worksheet.getCell('M6').value = '(13)';
    worksheet.getCell('N6').value = '(14)=(13)/(9)';

    worksheet.properties.outlineLevelCol = 2; worksheet.properties.defaultRowHeight = 15;
    let i = 4;
    const j = 6;
    for (i; i <= j; i++) {
      let k = 1;
      const l = 14;
      for (k; k <= l; k++) {
        worksheet.findCell(i, k).alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
        worksheet.findCell(i, k).fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: 'C0C0C0C0' },
          bgColor: { argb: 'FF0000FF' }
        };
        worksheet.findCell(i, k).border = { top: { style: 'thin' }, left: { style: 'thin' }, bottom: { style: 'thin' }, right: { style: 'thin' } };
        worksheet.findCell(i, k).font = { size: 12, bold: true, name: 'Times New Roman' };
      }
    }

    // get all columns from JSON
    let columnsArray: any[];
    for (const key in json) {
      if (json.hasOwnProperty(key)) {
        columnsArray = Object.keys(json[key]);
      }
    }
    // Add Data and Conditional Formatting
    data.forEach((element: any) => {

      const eachRow = [];
      columnsArray.forEach((column) => {
        eachRow.push(element[column]);
      });
      eachRow.splice(0, 1);
      const borderrow = worksheet.addRow(eachRow);
      borderrow.eachCell((cell) => {
        cell.border = { top: { style: 'thin' }, left: { style: 'thin' }, bottom: { style: 'thin' }, right: { style: 'thin' } };
        cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
      });
    });

    // footer data row
    if (footerData != null) {
      footerData.forEach((element: any) => {
        const eachRow = [];
        element.forEach((val: any) => {
          eachRow.push(val);
        });
        const footerRow = worksheet.addRow(eachRow);
        const cellMerge = 'A' + footerRow.number + ':B' + footerRow.number;
        worksheet.mergeCells(cellMerge);
        footerRow.eachCell((cell) => {
          cell.font = { size: 13, bold: true, name: 'Times New Roman' };
          cell.border = { top: { style: 'thin' }, left: { style: 'thin' }, bottom: { style: 'thin' }, right: { style: 'thin' } };
          cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
        });
      });
    }

    // tslint:disable-next-line:no-shadowed-variable
    workbook.xlsx.writeBuffer().then((data: ArrayBuffer) => {
      const blob = new Blob([data], { type: EXCEL_TYPE });
      fs.saveAs(blob, excelFileName + EXCEL_EXTENSION);
    });
  }

  public exportAsExcelProcessingReportDay(searchString): any {
    return new Promise((resolve) => {
      this.http.get(this.statisticsURL + '/dlk-dossier-statistic/--statistic-processing-export' + searchString, {
          observe: 'response',
          responseType: 'blob'
      }).toPromise().then(res => {
          const blob = new Blob([res.body], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'});
          let filename = 'bao_cao_thong_ke_toan_tinh_theo_co_quan.xlsx';
          if (res.headers.get('content-disposition') != null) {
              filename = res.headers.get('content-disposition').split(';')[1].split('=')[1].replace(/"/g, '');
          }
          const blobUrl = URL.createObjectURL(blob);
          const xhr = new XMLHttpRequest();
          xhr.responseType = 'blob';

          xhr.onload = () => {
              const recoveredBlob = xhr.response;
              const reader = new FileReader();
              reader.onload = () => {
                  const base64data = reader.result.toString();
                  const anchor = document.createElement('a');
                  anchor.download = filename;
                  anchor.href = base64data;
                  anchor.click();
              };
              reader.readAsDataURL(recoveredBlob);
          };

          xhr.open('GET', blobUrl);
          xhr.send();
          resolve(true);
      }).catch(err => {
          if (err.status === 500) {
              const message = {
                  vi: 'Hệ thống tạm thời không thể xuất file excel!',
                  en: 'The system is temporarily unable to export the excel file!'
              };
              this.snackbar.openSnackBar(0, '', message[localStorage.getItem('language')], 'error_notification', this.config.expiredTime);
          }
          resolve(false);
      });
    });
  }

  getDossierStatisticDossierUnreceivedDetail(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.get(this.padmanURL + '/dlk-dossier-statistic/--online-dossier-unreceived' + searchString, { headers }).pipe();
  }

  getDossierStatisticDossierUnreceivedDetailExport(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.get(this.padmanURL + '/dlk-dossier-statistic/--online-dossier-unreceived/--export' + searchString, { headers }).pipe();
  }

  public exportAsExcelOnlineUnreceivedReport(
    reportHeading: string,
    reportSubHeading: string,
    json: any[],
    excelFileName: string,
    sheetName: string,
  ) {
    const data = json;
    // create workbook and worksheet
    const workbook = new Workbook();
    workbook.creator = 'Snippet Coder';
    workbook.lastModifiedBy = 'SnippetCoder';
    workbook.created = new Date();
    workbook.modified = new Date();
    const worksheet = workbook.addWorksheet(sheetName);

    worksheet.getColumn('A').font = { name: 'Times New Roman', size: 12 };
    worksheet.getColumn('B').font = { name: 'Times New Roman', size: 12 };
    worksheet.getColumn('C').font = { name: 'Times New Roman', size: 12 };
    worksheet.getColumn('D').font = { name: 'Times New Roman', size: 12 };
    worksheet.getColumn('E').font = { name: 'Times New Roman', size: 12 };
    worksheet.getColumn('F').font = { name: 'Times New Roman', size: 12 };
    worksheet.getColumn('G').font = { name: 'Times New Roman', size: 12 };

    worksheet.getColumn('A').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getColumn('B').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getColumn('C').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getColumn('D').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getColumn('E').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getColumn('F').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getColumn('G').alignment = { horizontal: 'center', vertical: 'middle' };

    // worksheet.getColumn('A').width = 30;
    worksheet.getColumn('B').width = 50;
    worksheet.getColumn('C').width = 20;
    worksheet.getColumn('D').width = 20;
    worksheet.getColumn('E').width = 20;
    worksheet.getColumn('F').width = 20;
    worksheet.getColumn('G').width = 30;

    // Add header row
    worksheet.addRow([]);
    worksheet.mergeCells('A1:N1');
    worksheet.getCell('A1').value = reportHeading;
    worksheet.getCell('A1').alignment = { horizontal: 'center', vertical: 'middle' };

    worksheet.addRow([]);
    worksheet.mergeCells('A2:N2');
    worksheet.getCell('A2').value = reportSubHeading;
    worksheet.getCell('A2').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getCell('A2').font = { size: 12, italic: true, name: 'Times New Roman' };

    worksheet.getCell('A4').value = 'STT';
    worksheet.getCell('B4').value = 'Cơ quan';
    worksheet.getCell('C4').value = 'Số hồ sơ';
    worksheet.getCell('D4').value = 'Mã thủ tục';
    worksheet.getCell('E4').value = 'Tên thủ tục';
    worksheet.getCell('F4').value = 'Ngày nộp';
    worksheet.getCell('G4').value = 'Thời gian đã nộp';

    worksheet.getCell('A5').value = '(1)';
    worksheet.getCell('B5').value = '(2)';
    worksheet.getCell('C5').value = '(3)';
    worksheet.getCell('D5').value = '(4)';
    worksheet.getCell('E5').value = '(5)';
    worksheet.getCell('F5').value = '(6)';
    worksheet.getCell('G5').value = '(7)';

    worksheet.properties.outlineLevelCol = 2; worksheet.properties.defaultRowHeight = 15;
    let i = 4;
    const j = 5;
    for (i; i <= j; i++) {
      let k = 1;
      const l = 7;
      for (k; k <= l; k++) {
        worksheet.findCell(i, k).alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
        worksheet.findCell(i, k).fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: 'C0C0C0C0' },
          bgColor: { argb: 'FF0000FF' }
        };
        worksheet.findCell(i, k).border = { top: { style: 'thin' }, left: { style: 'thin' }, bottom: { style: 'thin' }, right: { style: 'thin' } };
        worksheet.findCell(i, k).font = { size: 12, bold: true, name: 'Times New Roman' };
      }
    }

    // get all columns from JSON
    let columnsArray: any[];
    for (const key in json) {
      if (json.hasOwnProperty(key)) {
        columnsArray = Object.keys(json[key]);
      }
    }
    // Add Data and Conditional Formatting
    data.forEach((element: any) => {

      const eachRow = [];
      columnsArray.forEach((column) => {
        eachRow.push(element[column]);
      });
      const borderrow = worksheet.addRow(eachRow);
      borderrow.eachCell((cell) => {
        cell.border = { top: { style: 'thin' }, left: { style: 'thin' }, bottom: { style: 'thin' }, right: { style: 'thin' } };
        cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
      });
    });

    // tslint:disable-next-line:no-shadowed-variable
    workbook.xlsx.writeBuffer().then((data: ArrayBuffer) => {
      const blob = new Blob([data], { type: EXCEL_TYPE });
      fs.saveAs(blob, excelFileName + EXCEL_EXTENSION);
    });
  }

  getTotalAgencyByLevel(): Observable<any>{
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.basedataURL + '/agency/--total-by-level', { headers });
  }

  getStatisticPublicService(search :String): Observable<any>{
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.basepadURL + '/dlk-procedure/--statistic-public-service' + search, { headers });
  }

  public exportAsExcelPublicServiceReport(
    reportHeading: string,
    reportSubHeading: string,
    json: any[],
    excelFileName: string,
    sheetName: string,
  ) {
    const data = json;
    console.log(data);
    // create workbook and worksheet
    const workbook = new Workbook();
    workbook.creator = 'Snippet Coder';
    workbook.lastModifiedBy = 'SnippetCoder';
    workbook.created = new Date();
    workbook.modified = new Date();
    const worksheet = workbook.addWorksheet(sheetName);

    worksheet.getColumn('A').font = { name: 'Times New Roman', size: 12 };
    worksheet.getColumn('B').font = { name: 'Times New Roman', size: 12 };
    worksheet.getColumn('C').font = { name: 'Times New Roman', size: 12 };
    worksheet.getColumn('D').font = { name: 'Times New Roman', size: 12 };
    worksheet.getColumn('E').font = { name: 'Times New Roman', size: 12 };
    worksheet.getColumn('F').font = { name: 'Times New Roman', size: 12 };
    worksheet.getColumn('G').font = { name: 'Times New Roman', size: 12 };

    worksheet.getColumn('A').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getColumn('B').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getColumn('C').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getColumn('D').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getColumn('E').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getColumn('F').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getColumn('G').alignment = { horizontal: 'center', vertical: 'middle' };

    // worksheet.getColumn('A').width = 30;
    worksheet.getColumn('B').width = 30;
    worksheet.getColumn('C').width = 50;
    worksheet.getColumn('D').width = 20;
    worksheet.getColumn('E').width = 70;
    worksheet.getColumn('F').width = 20;
    worksheet.getColumn('G').width = 20;


    // Add header row
    worksheet.addRow([]);
    worksheet.mergeCells('A1:G1');
    worksheet.getCell('A1').value = reportHeading;
    worksheet.getCell('A1').alignment = { horizontal: 'center', vertical: 'middle' };

    worksheet.addRow([]);
    worksheet.mergeCells('A2:G2');
    worksheet.getCell('A2').value = reportSubHeading;
    worksheet.getCell('A2').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getCell('A2').font = { size: 12, italic: true, name: 'Times New Roman' };

    worksheet.getCell('A4').value = 'STT';
    worksheet.getCell('B4').value = 'Cơ quan';
    worksheet.getCell('C4').value = 'Lĩnh vực';
    worksheet.getCell('D4').value = 'Mã thủ tục';
    worksheet.getCell('E4').value = 'Thủ tục';
    worksheet.getCell('F4').value = 'Số lượng hồ sơ tiếp nhận trực tuyến';
    worksheet.getCell('G4').value = 'Tổng hồ sơ tiếp nhận (Bao gồm cả trực tuyến và trực tiếp)';

    worksheet.properties.outlineLevelCol = 2; worksheet.properties.defaultRowHeight = 15;
    let i = 4;
    const j = 4;
    for (i; i <= j; i++) {
      let k = 1;
      const l = 7;
      for (k; k <= l; k++) {
        worksheet.findCell(i, k).alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
        worksheet.findCell(i, k).fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: 'C0C0C0C0' },
          bgColor: { argb: 'FF0000FF' }
        };
        worksheet.findCell(i, k).border = { top: { style: 'thin' }, left: { style: 'thin' }, bottom: { style: 'thin' }, right: { style: 'thin' } };
        worksheet.findCell(i, k).font = { size: 12, bold: true, name: 'Times New Roman' };
      }
    }

    // get all columns from JSON
    let columnsArray: any[];
    for (const key in json) {
      if (json.hasOwnProperty(key)) {
        columnsArray = Object.keys(json[key]);
      }
    }
    // Add Data and Conditional Formatting
    let r = 5;
    data.forEach((element: any) => {
      const eachRow = [];
      columnsArray.forEach((column) => {
        eachRow.push(element[column]);
      });
      eachRow.splice(0, 2);
      const borderrow = worksheet.addRow(eachRow);
      if (element.isTitleHeader) {
        worksheet.mergeCells('B'+ r + ':G' + r);
        worksheet.findCell(r, 1).font = { size: 12, bold: true, name: 'Times New Roman' };
        worksheet.findCell(r, 2).font = { size: 12, bold: true, name: 'Times New Roman' };
        worksheet.findCell(r, 2).alignment = { horizontal: 'left', vertical: 'middle', wrapText: true };
      } else if (element.isTotalHeader) {
        worksheet.mergeCells('B'+ r + ':E' + r);
        worksheet.findCell(r, 2).alignment = { horizontal: 'left', vertical: 'middle', wrapText: true };
      } else {
        var c = 1;
        for (c; c <= 7; c++) {
          if (c<2 || c>5) {
            worksheet.findCell(r, c).alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
          } else {
            worksheet.findCell(r, c).alignment = { horizontal: 'left', vertical: 'middle', wrapText: true };
          }
        }
      }
      r++;
      borderrow.eachCell((cell) => {
        cell.border = { top: { style: 'thin' }, left: { style: 'thin' }, bottom: { style: 'thin' }, right: { style: 'thin' } };
      });
    });

    // tslint:disable-next-line:no-shadowed-variable
    workbook.xlsx.writeBuffer().then((data: ArrayBuffer) => {
      const blob = new Blob([data], { type: EXCEL_TYPE });
      fs.saveAs(blob, excelFileName + EXCEL_EXTENSION);
    });
  }

  getDetailDossierTHSHHS(search): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.padmanURL + '/hcm-dossier-statistic/digitization-detail-THSHHS' + search, { headers }).pipe();
  }

  exportDossierStatisticDetailTHSHHS(params: string): any {
    const url = this.padmanURL + '/hcm-dossier-statistic/digitization-detail-THSHHS/--excel' + params;
    this.getFileExport(url).then();
  }

  getListAgencyWithParent(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.basedataURL + "/agency/" + searchString, { headers });
  }

  getDataDLKCT08TTCapSo(id, tuNgay, denNgay): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');

    return this.http.get(this.padmanURL + '/dlk-ct08/dlk-ct08-data-ttcapso?id=' + id + '&tuNgay=' + tuNgay + '&denNgay=' + denNgay, { headers }).pipe();
  }

  getDataDLKCT08TTCapXa(id, tuNgay, denNgay): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');

    return this.http.get(this.padmanURL + '/dlk-ct08/dlk-ct08-data-ttcapxa?id=' + id + '&tuNgay=' + tuNgay + '&denNgay=' + denNgay, { headers }).pipe();
  }
  getDataDLKCT08CapXa(id, level, tuNgay, denNgay, type): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');

    return this.http.get(this.padmanURL + '/dlk-ct08/dlk-ct08-data-capxa?id=' + id + '&level=' + level + '&type=' + type + '&tuNgay=' + tuNgay + '&denNgay=' + denNgay, { headers }).pipe();
  }
  getDataDLKCT08Detail(prid, id, prop, type, level, tuNgay, denNgay, page, size): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');

    return this.http.get(this.padmanURL + '/dlk-ct08/dlk-ct08-data-detail?prid=' + prid + '&id=' + id + '&prop=' + prop + '&type=' + type + '&level=' + level + '&tuNgay=' + tuNgay + '&denNgay=' + denNgay + '&page=' + page + '&size=' + size, { headers }).pipe();
  }
}
