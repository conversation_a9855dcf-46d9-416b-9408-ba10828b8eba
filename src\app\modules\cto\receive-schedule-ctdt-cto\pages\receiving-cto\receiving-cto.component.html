<div fxLayout="row" fxLayoutAlign="center">
  <div class="frm_main_receiving" fxFlex="grow">
    <div *ngIf="isCheckingInfoEnterprise" class="waiting-container opacity-div">
      <div class="spinner-wrapper">
        <mat-spinner color="warn"></mat-spinner>
        <span class="spinner-text">Đang kiểm tra thông tin. Vui lòng chờ trong giây lát</span>
      </div>
    </div>
    <div class="backToPrevSite">
      <button mat-icon-button (click)="goBack()">
        <mat-icon>keyboard_backspace</mat-icon>
      </button>
      <span>Tiếp nhận lịch hẹn chứng thực điện tử</span>
    </div>
    <p><span style="color: blue; cursor: pointer;" (click)="showProcedureDetail = !showProcedureDetail">Xem đầy đủ thông tin</span></p>
    <div class="dossierDetail">
      <p class="procedureName">{{procedureDetail[0]?.name}}</p>
      <p class="agency-info" *ngIf="showProcedureDetail && procedureDetail[0]?.agency.length > 0">
        <span>Cơ quan thực hiện: </span>
        <span>{{procedureDetail[0]?.agency[0]?.name}} ({{procedureDetail[0]?.agency[0]?.code}})</span>
      </p>
      <p class="procedureLevel">{{procedureDetail[0]?.level?.name}}</p>
      <p class="sector">
        <span i18n>Lĩnh vực: </span>
        <span>{{procedureDetail[0]?.sector?.name}}</span>
      </p>
      <p class="process" *ngIf="selectedProcess != null">
        <span i18n>Quy trình: </span>
        <span *ngIf="selectedProcess.processDefinition.processingTimeUnit == 'y'">
                    {{selectedProcess?.processDefinition?.processingTime}} <span i18n>năm -</span>
                </span>
        <span *ngIf="selectedProcess.processDefinition.processingTimeUnit == 'M'">
                    {{selectedProcess?.processDefinition?.processingTime}} <span i18n>tháng -</span>
                </span>
        <span *ngIf="selectedProcess.processDefinition.processingTimeUnit == 'd' && selectedProcess?.processDefinition?.dynamicVariable?.isProcessingTimeUnitType === true">
                    {{selectedProcess?.processDefinition?.processingTime}} <span i18n>ngày làm việc -</span>
                </span>
        <span *ngIf="selectedProcess.processDefinition.processingTimeUnit == 'd' && selectedProcess?.processDefinition?.dynamicVariable?.isProcessingTimeUnitType !== true">
                    {{selectedProcess?.processDefinition?.processingTime}} <span i18n>ngày -</span>
                </span>
        <span *ngIf="selectedProcess.processDefinition.processingTimeUnit == 'H:m:s'">
                    {{selectedProcess?.processDefinition?.processingTime}} <span i18n>giờ làm việc -</span>
                </span>
        <span *ngIf="selectedProcess.processDefinition.processingTimeUnit == 'h'">
                    {{selectedProcess?.processDefinition?.processingTime}} <span i18n>giờ làm việc -</span>
                </span>
        <span *ngIf="selectedProcess.processDefinition.processingTimeUnit == 'm'">
                    {{selectedProcess?.processDefinition?.processingTime}} <span >phút -</span>
                </span>
        <span class="nameProcess"> {{selectedProcess?.processDefinition?.name}}</span>
      </p>
      <p class="process" *ngIf="selectedProcess != null && showProcedureDetail">
        <span>Mã quy trình: {{selectedProcess?.processDefinition?.code}}</span>
      </p>
      <p class="paymentMethod" *ngIf="isShowAppointmentDate">
        <span class="lblBold" i18n>Ngày hẹn trả</span>:
        <span>
                  {{showAppointmentDate}}
              </span>
      </p>
    </div>
    <ng-container *ngIf="checkBusinessRegistration === 1">
      <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="column" style="width: 100%;">
        <mat-form-field class="example-form-field" appearance="outline" style="margin-top: 10px;">
          <mat-label i18n>Mã số doanh nghiệp</mat-label>
          <input matInput type="text" [(ngModel)]="value" maxlength="15">
          <button mat-button *ngIf="value" matSuffix mat-icon-button aria-label="Clear" (click)="value=''">
            <mat-icon>close</mat-icon>
          </button>
        </mat-form-field>
        <button class="btnSearch" mat-flat-button fxFlex.gt-sm="15" fxFlex.gt-xs="27" fxFlex='grow' (click)="searchBussiness()" i18n>Tìm kiếm</button>
      </div>
    </ng-container>
    <digo-check-send-notify *ngIf="!notifyQNI && !isReceivingTemplate" functionType="receptionReceiving" rowTemplate="true" (checkAction)="checkActionCallback($event)"></digo-check-send-notify>


    <div class="infoTabs">
      <get-form-orgin
        [fullname]="!!storage468?.baseCitizenKey ? applicantEForm?.data?.data[storage468?.baseCitizenKey?.fullnameKey] : applicantEForm?.data?.data?.fullname"
        [identityNumber]="!!storage468?.baseCitizenKey ? applicantEForm?.data?.data[storage468?.baseCitizenKey?.identityNumberKey] : applicantEForm?.data?.data?.identityNumber"
        [birthday]="!!storage468?.baseCitizenKey ? applicantEForm?.data?.data[storage468?.baseCitizenKey?.birthdayKey]: applicantEForm?.data?.data?.birthday"
        #getFormOrgin>
      </get-form-orgin>
      <form [formGroup]="tabForm" id="tabForm" class="tabForm">
        <mat-tab-group (selectedTabChange)="onTabChange($event)" dynamicHeight [(selectedIndex)]="infoTabsIndex" (selectedTabChange)="onloadFormOrgin($event);">
          <mat-tab>
            <ng-template mat-tab-label>
              <mat-icon class="tabIcon">assignment_ind</mat-icon>
              <span i18n>Thông tin chung</span>
            </ng-template>
            <div *ngIf="isValidResidentialInfo" id="confirmResidentialInfo" class="valid-residential-info">
              <mat-icon class="check-icon">check</mat-icon>
              <span>Xác nhận danh tính số hợp lệ</span>
            </div>
            <!--            đã code ở đây IGATESUPP-115852-->
            <div class="thoaiisolate" style="padding-top: 2em;">
              <!--              <get-citizen-info *ngIf="showIdentityDigital" [eformId]="applicantEForm.id" [showPrint]="scheduleAppointment ? null :showPrintResidentInfoReception" [isSaveCSDLQGDC]="scheduleAppointment ? null :isProcedureAcceptSaveCSDLQGDC" [procedureId]="scheduleAppointment ? null : procedureId"-->
              <!--                                (onLoad)="onLoadCitizenInfo($event)">-->
              <!--              </get-citizen-info>-->
              <get-citizen-info *ngIf="showIdentityDigital" [eformId]="applicantEForm.id" [procedureId]="scheduleAppointment ? null : procedureId"
                                (onLoad)="onLoadCitizenInfo($event)">
              </get-citizen-info>
              <div *ngIf="isShowCopyDossierAppliedButtonApplicant">
                <br/>
                <button type="button" mat-stroked-button  class="btnSecondary" (click)="getListInformationDossierOld()" >
                  <mat-icon>save</mat-icon>
                  <span>Sao chép thông tin từ hồ sơ cũ</span>
                </button>
                <br/>
              </div>
              <cmu-get-citizen-info *ngIf="enableSyncFaceRecognition" (onLoad)="onLoadCmuCitizenInfo($event)"></cmu-get-citizen-info>
              <!--           kết thúc đã code ở đây IGATESUPP-115852-->
              <form (submit)="onGetForm()">
                <formio #applicantEFormComp [form]="applicantEForm.component"
                        [submission]="applicantEForm.data" [renderOptions]="applicantEForm.renderOptions"
                        [readOnly]="isLoading" [viewOnly]="isLoading"
                        *ngIf="applicantEForm.id != undefined && applicantEForm.id != ''"
                        (change)="onChangeForm($event)"
                        (customEvent)="eventAutofillEntireEfromEnable($event)"
                ></formio>
                <button type="submit" hidden="true" class="hidden">
                  <mat-icon>save</mat-icon>
                  <span i18n>Lưu</span>
                </button>
              </form>
            </div>
          </mat-tab>
          <mat-tab #tab2>
            <ng-template mat-tab-label>
              <mat-icon class="tabIcon">dns</mat-icon>
              <span i18n>Thành phần hồ sơ</span>
            </ng-template>
            <div class="procedureForm" *ngIf="dossierId == null || dossierId == ''">
              <div class="success_head" *ngIf="procedureForm.length == 0 || isNoForm">
                <p class="title" i18n>Không có thông tin giấy tờ</p>
              </div>
              <form [formGroup]="prForm">
                <!--Thêm loại procedureFormType hiển thị dạng bảng-->
                <div *ngIf="procedureFormType && procedureFormType == 1">
                  <table class="mat-table mat-table-form" style="width: 100%;margin-top: 15px;" *ngIf="procedureForm.length > 0">
                    <tr>
                      <th class="tablecolor1" i18n>STT</th>
                      <th class="tablecolor1" i18n="@@procedureName">Tên giấy tờ</th>
                      <!-- <th class="tablecolor1" width="100" i18n="@@procedureQuantity">Số lượng bản</th> -->
                      <th class="tablecolor1" width="100" i18n="@@procedureType">Loại bản</th>
                      <th class="tablecolor1" width="100" i18n="@@procedureTemplate">Mẫu giấy tờ</th>
                      <th class="tablecolor1" *ngIf="showCopies">Số bản</th>
                      <th class="tablecolor1" *ngIf="batLienThongCTDT && selectedProcess?.isCTDT">Số trang</th>
                      <th class="tablecolor1" *ngIf="batLienThongCTDT && selectedProcess?.isCTDT">Loại chứng thực</th>
                      <th class="tablecolor1" style="max-width: 150px;" i18n="@@procedureAttachFile">Đính kèm giấy tờ</th>
                      <!-- <th class="tablecolor1" width="140">
                          <span i18n="@@procedureHasFormOrgin">Lấy giấy tờ từ form nhập thông tin</span> -->
                      <!-- <div class="hasFormOrgin" i18n="@@procedureHasFormOrginDes">Giấy CMND hoặc hộ chiếu (bản photocopy)</div> -->
                      <!-- </th> -->
                    </tr>
                    <tr *ngIf="this.qbhTickAllTPHS == true">
                      <mat-checkbox  [ngStyle]="{'margin-left': '106%'}" (change)="checkAllCheckBox($event.checked)"
                                     [checked]="allComplete"
                                     [indeterminate]="someComplete()"> Chọn/Bỏ chọn tất cả
                      </mat-checkbox>
                    </tr>
                    <tr *ngFor="let form of procedureForm"  class="item table-row mat-cell cdk-cell colorTd cdk-column-code mat-column-code ng-star-inserted">
                      <td class="center">
                        <div class="head">{{form.stt}}</div>
                      </td>
                      <td>
                        <div class="head">
                          <div>
                            <mat-icon class="requirement" [ngClass]="{'disabledHide': form.requirement != 1}">
                              check_circle_outline
                            </mat-icon>
                            <mat-checkbox *ngIf="this.qbhTickAllTPHS == true" color="primary" [checked]="form.autoCheked"
                                          (change)="changeParentStatus($event, form.form)"
                                          [ngClass]="{'disabledHide': form.requirement == 1}"></mat-checkbox>
                            <mat-checkbox *ngIf="this.qbhTickAllTPHS == false" color="primary" [checked]="form.requirement !== -1 || form?.autoCheck"
                                          (change)="changeParentStatus($event, form.form)"
                                          [ngClass]="{'disabledHide': form.requirement == 1}"></mat-checkbox>
                            <span>{{ form.form.name }}</span>
                          </div>
                          <a (click)="exportEform(form.eForm, form.form.name, form.form.id)"
                             class="openEformOnline" *ngIf="form.checkEform == 1"><span>
                                              <span class="detailopenEformOnline" *ngIf="!openDocumentFormNameLable" i18n>(Click chuột vào đây để mở Biểu mẫu giấy tờ)</span>
                                              <span class="detailopenEformOnline" *ngIf="openDocumentFormNameLable">{{openDocumentFormNameLable}}</span>
                                            </span></a>
                        </div>
                        <btn-get-form-orgin [getFormOrgin]="getFormOrgin"
                                            (afterChoose)="afterChoose($event)"
                                            [form]="form?.form">
                        </btn-get-form-orgin>
                      </td>
                      <!-- <td class="body">
                        <mat-radio-group formControlName="rdo_File">
                          <ng-container *ngFor="let fd of form.detail; let j = index">
                            <div *ngIf="fd?.type?.id != '6204d0a679894379eae831c5'|| fd?.file?.length > 0" class="rdo_File">
                                <mat-radio-button value="{{ fd.type.id }}" [checked]="fd.status==1" (change)="onRadioChange(form.form.id, fd)">
                                </mat-radio-button>
                                <mat-form-field appearance="outline" class="quantity" style="width: 60px;">
                                  <input matInput value="{{ fd.quantity }}"
                                        (change)="onQuantityChange(form.form.id, fd, $event.target.value)"
                                        type="number" id="quantity_{{form.form.id}}_{{fd.type.id}}">
                                </mat-form-field>
                            </div>
                          </ng-container>
                        </mat-radio-group>
                      </td> -->
                      <td class="body">
                        <mat-radio-group formControlName="rdo_File">
                          <ng-container *ngFor="let fd of form.detail; let j = index">
                            <div *ngIf="fd?.type?.id != '6204d0a679894379eae831c5'|| fd?.file?.length > 0" class="rdo_File">
                              <div class="typeName">
                                {{ fd.quantity }} {{ fd?.type?.id !== '6204d0a679894379eae831c5'?fd.type.name:'Bản điện tử' }}
                              </div>
                            </div>
                          </ng-container>
                        </mat-radio-group>
                      </td>
                      <td class="body">
                        <mat-radio-group formControlName="rdo_File">
                          <ng-container *ngFor="let fd of form.detail; let j = index">
                            <div *ngIf="fd?.type?.id != '6204d0a679894379eae831c5'|| fd?.file?.length > 0" class="rdo_File">
                              <div fxLayout="column" class="divFormFile">
                                <div class="setfileForm" *ngIf="form.file!= null && form.file.length >= 1;">
                                  <mat-form-field appearance="fill" class="selectFileTemplate setfileForm"
                                                  *ngIf="form.file !== null && form.file.length > 1" fxFlex="93"
                                                  [style.z-index]="form.file != null && form.file.length > 0 ? 1 : -1">
                                    <mat-select placeholder="Xem mẫu đơn, tờ khai">
                                      <mat-option *ngFor="let file of form.file" value="{{ file.id }}"
                                                  (click)="downloadFile(file.id, file.filename)">
                                        {{ file.filename }}
                                      </mat-option>
                                    </mat-select>
                                  </mat-form-field>
                                </div>
                                <div class="setfileForm" *ngIf="form.file !== null && form.file.length === 1">
                                  <label i18n="@@procedureTemplate">Mẫu đơn, Tờ khai</label>
                                  <div class="file" *ngFor="let file of form.file" (click)="downloadFile(file.id, file.filename)" style="cursor:pointer" i18n-tooltip #tooltip="matTooltip" matTooltip="Tải xuống tệp tin">
                                    <div class="icon" [ngStyle]="{'background-image': 'url('+ getIconFilename(file)  +')'}"> </div>
                                    <div class="name">{{ file.filename }}</div>
                                  </div>
                                </div>
                                <div *ngIf="form.fileLink != null && form.fileLink != ''" class="linkfileForm">
                                  <a href="{{form.fileLink}}" target="_blank" *ngIf="form.fileLink != null && form.fileLink != ''">{{form?.fileLinkName ? form?.fileLinkName : 'Link file'}}</a>
                                </div>
                              </div>
                            </div>
                          </ng-container>
                        </mat-radio-group>
                      </td>
                      <td class="body" style="max-width: 60px" *ngIf="showCopies">
                        <mat-radio-group formControlName="rdo_File">
                          <div *ngFor="let fd of form.detail; let j = index">
                            <div *ngIf="fd?.type?.id != '6204d0a679894379eae831c5'|| fd?.file?.length > 0" class="rdo_File" fxLayout="row"
                                 fxLayout.xs="row" fxLayout.sm="row">
                              <div class="listLoaiChungThuc" fxFlex.gt-xs="grow" fxFlex='grow'>
                                <div class="wrapList" fxLayout.xs="row" fxLayout.sm="row" fxLayout="row" fxLayoutAlign="space-evenly">
                                  <div class="loaiChungThuc" fxFlex.gt-sm="95" fxFlex.gt-xs="95" fxFlex='grow'>
                                    <mat-form-field appearance="outline">
                                      <!-- <mat-label>Số bản</mat-label> -->
                                      <input matInput value="{{form.detail[0].quantity ? form.detail[0].quantity : 1}}"
                                             type="number" min="1" (input)="onQuantityChange(form.form.id, fd, $event.target.value)">
                                    </mat-form-field>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </mat-radio-group>
                      </td>
                      <td class="body" style="max-width: 50px" *ngIf="batLienThongCTDT && selectedProcess?.isCTDT">
                        <mat-radio-group formControlName="rdo_File">
                          <div *ngFor="let fd of form.detail; let j = index">
                            <div *ngIf="fd?.type?.id != '6204d0a679894379eae831c5'|| fd?.file?.length > 0" class="rdo_File" fxLayout="row"
                                 fxLayout.xs="row" fxLayout.sm="row">
                              <div class="listLoaiChungThuc" fxFlex.gt-xs="grow" fxFlex='grow'>
                                <div class="wrapList" fxLayout.xs="row" fxLayout.sm="row" fxLayout="row" fxLayoutAlign="space-evenly">
                                  <div class="loaiChungThuc" fxFlex.gt-sm="95" fxFlex.gt-xs="95" fxFlex='grow'>
                                    <mat-form-field appearance="outline">
                                      <!-- <mat-label>Số trang</mat-label> -->
                                      <input matInput value="{{form.authentication?.pageNumber ? form.authentication?.pageNumber : 1}}"
                                             type="number" min="1" (input)="onPageNumberAuthChange(form.form.id, fd, $event.target.value)">
                                    </mat-form-field>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </mat-radio-group>
                      </td>
                      <td class="body" style="max-width: 170px" *ngIf="batLienThongCTDT && selectedProcess?.isCTDT">
                        <mat-radio-group formControlName="rdo_File">
                          <div *ngFor="let fd of form.detail; let j = index">
                            <div *ngIf="fd?.type?.id != '6204d0a679894379eae831c5'|| fd?.file?.length > 0" class="rdo_File" fxLayout="row"
                                 fxLayout.xs="row" fxLayout.sm="row">
                              <div class="listLoaiChungThuc" fxFlex.gt-xs="grow" fxFlex='grow'>
                                <div class="wrapList" fxLayout.xs="row" fxLayout.sm="row" fxLayout="row" fxLayoutAlign="space-evenly">
                                  <div class="loaiChungThuc" fxFlex.gt-sm="95" fxFlex.gt-xs="95" fxFlex='grow'>
                                    <mat-form-field appearance="outline">
                                      <!-- <mat-label>Loại chứng thực</mat-label> -->
                                      <mat-select value="{{form.authentication.type.id ? form.authentication.type.id : defaultAuthTypeId}}"
                                                  (selectionChange)="authenticationTypeChanged($event, form.form)">
                                        <mat-option *ngFor='let element of listAuthenticationType' value="{{element.id}}">
                                          {{ element.name }}
                                        </mat-option>
                                      </mat-select>
                                    </mat-form-field>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </mat-radio-group>
                      </td>
                      <td class="body">
                        <mat-radio-group formControlName="rdo_File">
                          <ng-container *ngFor="let fd of form.detail; let j = index">
                            <div *ngIf="fd?.type?.id != '6204d0a679894379eae831c5'|| fd?.file?.length > 0" class="rdo_File" fxLayout="row"
                                 fxLayout.xs="row" fxLayout.sm="row">
                              <div class="listUploadedFile" fxFlex.gt-xs="grow" fxFlex='grow'>
                                <div class="wrapList" fxLayout.xs="row" fxLayout.sm="row" fxLayout="row"
                                     fxLayoutAlign="space-evenly">
                                  <div class="file" fxFlex='grow'
                                       *ngFor="let detailFile of fd.file; let f = index;">
                                    <div class="icon"
                                         [ngStyle]="{'background-image': 'url('+ getIconFilename(detailFile)  +')'}"
                                         (click)="downloadFile(detailFile.id, detailFile.filename)">
                                    </div>
                                    <div style="cursor: pointer" class="name"
                                         (click)="downloadFile(detailFile.id, detailFile.filename)">
                                      {{detailFile.filename ? detailFile.filename : detailFile.name}}</div>
                                    <button mat-icon-button class="menuAction" (click)="addFormRename(detailFile)">
                                      <mat-icon>edit</mat-icon>
                                    </button>
                                    <button mat-icon-button class="deleteFile" [matMenuTriggerFor]="actionMenu">
                                      <mat-icon>more_horiz</mat-icon>
                                    </button>
                                    <mat-menu #actionMenu="matMenu" xPosition="before">
                                      <a mat-menu-item class="menuAction" target="_blank" [routerLink]="routerLink(detailFile)">
                                        <mat-icon>format_size</mat-icon>
                                        <span i18n>Xem trước</span>
                                      </a>
                                      <button mat-menu-item class="menuAction" (click)="downloadFile(detailFile.id, detailFile.filename)">
                                        <mat-icon>cloud_download</mat-icon>
                                        <span i18n>Tải xuống tệp tin</span>
                                      </button>
                                      <button mat-menu-item class="menuAction"
                                              (click)="removeFormFile(form.form.id, fd.type.id, detailFile.id)">
                                        <mat-icon>close</mat-icon>
                                        <span>Xóa</span>
                                      </button>
                                      <ng-container *ngIf="fd?.type?.id != '6204d0a679894379eae831c5'">
                                        <button mat-menu-item class="menuAction"
                                                (click)="openPdfDigitalSignature(form.form.id, fd.type.id, detailFile, null, 1)"
                                                *ngIf="digitalSignature.VNPTSim && checkIfFileIsSupported(detailFile.filename)">
                                          <mat-icon>verified</mat-icon>
                                          <span>Ký số sim</span>
                                        </button>
                                        <button mat-menu-item class="menuAction"
                                                (click)="openPdfDigitalSignature(form.form.id, fd.type.id, detailFile, null)"
                                                *ngIf="digitalSignature.SmartCA && checkIfFileIsSupported(detailFile.filename)">
                                          <mat-icon>verified</mat-icon>
                                          <span>Ký số Smart CA</span>
                                        </button>
                                        <button mat-menu-item class="menuAction"
                                                (click)="openVGCAplugin(form.form.id, fd.type.id, detailFile.id, detailFile.filename, detailFile.size)"
                                                *ngIf="digitalSignature.VGCA && checkIfFileIsSupported(detailFile.filename)">
                                          <mat-icon>verified</mat-icon>
                                          <span *ngIf="!isEditSignTokenName; then thenBlock else elseBlock"></span>
                                          <ng-template #thenBlock>Ký số Ban Cơ yếu</ng-template>
                                          <ng-template #elseBlock>Ký số Token</ng-template>
                                        </button>
                                        <button mat-menu-item class="menuAction"
                                                (click)="openVGCAPluginSignIssue(form.form.id, fd.type.id, detailFile.id, detailFile.filename, detailFile.size)"
                                                *ngIf="!!allowVGCASignIssue">
                                          <mat-icon>verified</mat-icon>
                                          <span>Ký số văn thư Ban Cơ yếu</span>
                                        </button>
                                        <button mat-menu-item class="menuAction"
                                                (click)="openVGCAPluginSignCopy(form.form.id, fd.type.id, detailFile.id, detailFile.filename, detailFile.size)"
                                                *ngIf="!!isVgcaSignCopy">
                                          <mat-icon>verified</mat-icon>
                                          <span>{{vgcaSignLabel}}</span>
                                        </button>
                                        <button mat-menu-item class="menuAction"
                                                (click)="openVnptCaPlugin(form.form.id, fd.type.id, detailFile)"
                                                *ngIf="digitalSignature.VNPTCA && checkIfFileIsSupported(detailFile.filename)">
                                          <mat-icon>verified</mat-icon>
                                          <span>Ký số VNPT-CA</span>
                                        </button>
                                        <button mat-menu-item class="menuAction" (click)="openNEAC(form.form.id, fd.type.id, detailFile, null, 5)"
                                                *ngIf="digitalSignature.NEAC && checkIfFileIsSupported(detailFile.filename)">
                                          <mat-icon class="mainColor">verified</mat-icon>
                                          <span i18n="@@digitalSignNEAC">Ký số NEAC</span>
                                        </button>
                                        <button mat-menu-item class="menuAction" (click)="openPdfDigitalSignature(form.form.id, fd.type.id, detailFile, null, 6)"
                                                *ngIf="digitalSignature.QNM && checkIfFileIsSupported(detailFile.filename)">
                                          <mat-icon class="mainColor">verified</mat-icon>
                                          <span i18n="@@QNMDigitalSignature">Ký số tập trung QNM</span>
                                        </button>
                                        <!--                                                            <button mat-menu-item class="menuAction"-->
                                        <!--                                                                    (click)="openHistory(detailFile.id)">-->
                                        <!--                                                              <mat-icon>refresh</mat-icon>-->
                                        <!--                                                              <span i18n="@@history">Xem lịch sử ký số</span>-->
                                        <!--                                                            </button>-->
                                        <button mat-menu-item class="menuAction">
                                          <view-sign-history [fileId]="detailFile.id">
                                            <mat-icon>refresh</mat-icon>
                                            <span>Xem lịch sử ký số</span>
                                          </view-sign-history>
                                        </button>
                                        <btn-save-form-orgin [getFormOrgin]="getFormOrgin"
                                                             [form]="form?.form"
                                                             [files]="fd.file"
                                                             [fileId]="detailFile.id"
                                                             checkOverride="true"
                                                             btnClass="menuAction"
                                                             [storage468]="storage468">
                                        </btn-save-form-orgin>
                                      </ng-container>
                                      <ng-container *ngIf="fd?.type?.id == '6204d0a679894379eae831c5'">
                                        <view-sign-item btnClass="menuAction"
                                                        [fileId]="detailFile.id"
                                                        [dirId]="0">
                                        </view-sign-item>
                                      </ng-container>
                                    </mat-menu>
                                  </div>
                                  <ng-container *ngIf="fd?.type?.id != '6204d0a679894379eae831c5'">
                                    <div fxLayout="row" fxLayoutAlign="space-between" class="uploadBtn"
                                         fxFlex.gt-sm="98" fxFlex.gt-xs="98" fxFlex='grow'
                                         *ngIf="fd.file.length % 2 != 0 && displayScanFilePadsvc">
                                      <button mat-button fxFlex='99' type="button" class="btn_upload"
                                              (click)="getInfo(form.form.id, fd.type.id, form.proDef); scanToPdfWithThumbnails()">
                                        <mat-icon>scanner</mat-icon>
                                        <span class="text">Scan tệp tin</span>
                                      </button>
                                    </div>
                                    <div fxLayout="row" fxLayoutAlign="space-between" class="uploadBtn"
                                         fxFlex.gt-sm="98" fxFlex.gt-xs="98" fxFlex='grow'
                                         *ngIf="fd.file.length % 2 != 0">
                                      <button mat-button fxFlex='99' class="btn_upload">
                                        <mat-icon>attachment</mat-icon>
                                        <span class="text" i18n>Chọn tệp tin</span>
                                      </button>
                                      <input multiple id="file_upload" type='file'
                                             (change)="onSelectFile($event, form.form.id, fd.type.id)"
                                             [accept]="acceptFileExtension" value="{{blankVal}}">
                                    </div>
                                    <div fxLayout="row" fxLayoutAlign="space-between" class="uploadBtn"
                                         fxFlex.gt-sm="98" fxFlex.gt-xs="98" fxFlex='grow'
                                         *ngIf="fd.file.length % 2 == 0 && displayScanFilePadsvc">
                                      <button mat-button fxFlex='99' type="button" class="btn_upload"
                                              (click)="getInfo(form.form.id, fd.type.id, form.proDef); scanToPdfWithThumbnails()">
                                        <mat-icon>scanner</mat-icon>
                                        <span class="text">Scan tệp tin</span>
                                      </button>
                                    </div>
                                    <div fxLayout="row" fxLayoutAlign="space-between" class="uploadBtn"
                                         fxFlex.gt-sm="98" fxFlex.gt-xs="98" fxFlex='grow'
                                         *ngIf="fd.file.length % 2 == 0">
                                      <button mat-button fxFlex='99' class="btn_upload">
                                        <mat-icon>attachment</mat-icon>
                                        <span class="text">Chọn tệp tin</span>
                                      </button>
                                      <input multiple id="file_upload" type='file'
                                             (change)="onSelectFile($event, form.form.id, fd.type.id)"
                                             [accept]="acceptFileExtension" value="{{blankVal}}">
                                    </div>
                                    <div *ngIf="showMaxFileSize">Kích thước tối đa của tệp tin {{maxFileSize}} MB</div>
                                  </ng-container>
                                </div>
                              </div>
                            </div>
                          </ng-container>
                        </mat-radio-group>
                      </td>
                      <!-- <td>
                        <btn-get-form-orgin [getFormOrgin]="getFormOrgin"
                                            (afterChoose)="afterChoose($event)"
                                            [form]="form?.form">
                        </btn-get-form-orgin>
                      </td> -->
                    </tr>
                  </table>
                  <a mat-stroked-button class="btn_addNewFile" (click)="addForm()" *ngIf="showAddNewFileBtn">
                    <mat-icon>add</mat-icon>
                    <span>Thêm giấy tờ</span>
                  </a>
                </div>
                <!--end-->

                <div *ngIf="!procedureFormType || procedureFormType == 0">
                  <div *ngIf="this.qbhTickAllTPHS == true" >
                    <mat-checkbox  (change)="checkAllCheckBox($event.checked)"
                                   [checked]="allComplete"
                                   [indeterminate]="someComplete()"> Chọn/Bỏ chọn tất cả
                    </mat-checkbox>
                  </div>
                  <div class="item" *ngFor="let form of procedureForm"
                       [ngClass]="{'disabledHide':  !enableShowFullDocument && form.procedureProcessDefinition != null && selectedProcess.id != form.procedureProcessDefinition.id}">
                    <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="column">
                      <div class="head" fxFlex='grow' fxLayout="row" fxLayoutAlign="space-between"
                           fxLayout.xs="column" fxLayout.sm="column">
                        <div>
                          <mat-icon class="requirement" [ngClass]="{'disabledHide': form.requirement != 1}">
                            check_circle_outline
                          </mat-icon>
                          <mat-checkbox *ngIf="this.qbhTickAllTPHS == true" color="primary" [checked]="form.autoCheked"
                                        (change)="changeParentStatus($event, form.form)"
                                        [ngClass]="{'disabledHide': form.requirement == 1}"></mat-checkbox>
                          <mat-checkbox *ngIf="this.qbhTickAllTPHS == false"color="primary" [checked]="form.requirement !== -1 || form?.autoCheck"
                                        (change)="changeParentStatus($event, form.form)"
                                        [ngClass]="{'disabledHide': form.requirement == 1}"></mat-checkbox>
                          <span>{{ form.form.name }}</span>
                        </div>
                        <a (click)="exportEform(form.eForm, form.form.name, form.form.id)"
                           class="openEformOnline" *ngIf="form.checkEform == 1"><span>{{ form.form.name }}
                          <span class="detailopenEformOnline" *ngIf="!openDocumentFormNameLable" i18n>(Click chuột vào đây để mở Biểu mẫu giấy tờ)</span>
                                        <span class="detailopenEformOnline" *ngIf="openDocumentFormNameLable">{{openDocumentFormNameLable}}</span>
                                    </span></a>
                      </div>
                    </div>
                    <div class="body">
                      <mat-radio-group formControlName="rdo_File">
                        <ng-container *ngFor="let fd of form.detail; let j = index">
                          <div *ngIf="fd?.type?.id != '6204d0a679894379eae831c5'|| fd?.file?.length > 0" class="rdo_File" fxLayout="row"
                               fxLayout.xs="row" fxLayout.sm="row">
                            <mat-radio-button value="{{ fd.type.id }}" [checked]="fd.status==1"
                                              fxFlex.gt-sm='3' (change)="onRadioChange(form.form.id, fd)">
                            </mat-radio-button>

                            <div *ngIf="addOtherFile == '0'" style="display: flex;align-items:center" fxFlex.gt-sm='{{flexGtSmColumnQuantityVsTypeName}}'>
                              <mat-form-field appearance="outline" class="quantity" >
                                <input matInput value="{{ fd.quantity }}"
                                       (change)="onQuantityChange(form.form.id, fd, $event.target.value)"
                                       type="number" id="quantity_{{form.form.id}}_{{fd.type.id}}">
                              </mat-form-field>

                              <div class="typeName">
                                {{ fd?.type?.id !== '6204d0a679894379eae831c5'?fd.type.name:'Bản điện tử' }}
                              </div>
                            </div>

                            <div *ngIf="addOtherFile == '1' && isThuTucCTDTQBH == true" style="display: flex;align-items:first baseline;  flex-direction: column;" fxFlex.gt-sm='{{flexGtSmColumnQuantityVsTypeName}}'>
                              <div>
                                <span>Số lượng bản sao điện tử: 1 bản</span>
                              </div>
                              <div style="display: flex; align-items: first baseline;">
                                <div class="typeName">
                                  Số lượng bản sao giấy:
                                </div>

                                <mat-form-field appearance="outline" class="quantity" >
                                  <input matInput value="{{ fd.quantity }}"
                                         (change)="onQuantityChange(form.form.id, fd, $event.target.value)"
                                         type="number" id="quantity_{{form.form.id}}_{{fd.type.id}}">
                                </mat-form-field>
                              </div>
                            </div>

                            <div *ngIf="addOtherFile == '1' && isThuTucCTDTQBH == false" style="display: flex;align-items:center;" fxFlex.gt-sm='{{flexGtSmColumnQuantityVsTypeName}}'>

                              <mat-form-field appearance="outline" class="quantity" >
                                <input matInput value="{{ fd.quantity }}"
                                       (change)="onQuantityChange(form.form.id, fd, $event.target.value)"
                                       type="number" id="quantity_{{form.form.id}}_{{fd.type.id}}">
                              </mat-form-field>

                              <mat-form-field appearance="outline" class="typeQBH">
                                <mat-select id="typeqbh_{{form.form.id}}_{{fd.type.id}}" value="{{ fd.type.id }}" (selectionChange)="onTypeQBHChange(form.form.id, fd, $event.value)">
                                  <mat-option *ngFor='let type of listTypeQBH;' value="{{type.id}}"> {{ type.name }}</mat-option>
                                </mat-select>
                              </mat-form-field>



                            </div>

                            <!-- <mat-form-field appearance="fill" class="selectFileTemplate" fxFlex.gt-sm='20'
                                            fxFlex="93"
                                            [style.z-index]="form.file !== null && form.file.length > 0 ? 1 : -1"
                                            *ngIf="form.file !== null && form.file.length > 1">
                              <mat-select placeholder="Chọn mẫu đơn">
                                <mat-option *ngFor="let file of form.file" value="{{ file.id }}"
                                            (click)="downloadFile(file.id, file.filename)">
                                  {{ file.filename }}
                                </mat-option>
                              </mat-select>
                            </mat-form-field>
                            <div fxFlex.gt-sm='20' fxFlex="93" fxFlex='grow'
                                *ngIf="form.file !== null && form.file.length === 1">
                              <label i18n>Mẫu đơn</label>
                              <div class="file" *ngFor="let file of form.file"
                                  (click)="downloadFile(file.id, file.filename)" style="cursor:pointer"
                                  i18n-tooltip #tooltip="matTooltip" matTooltip="Tải xuống tệp tin">
                                <div class="icon"
                                    [ngStyle]="{'background-image': 'url('+ getIconFilename(file)  +')'}"></div>
                                <div class="name">{{ file.filename }}</div>
                              </div>
                            </div>

                            <div fxFlex.gt-sm='20' fxFlex="93"
                                *ngIf="form.file === null || form.file.length === 0">
                              <span class="disabledHide">Không có mẫu đơn nào</span>
                            </div> -->

                            <div fxLayout="column" fxFlex.gt-sm='{{flexGtSmColumnDivFormFile}}' fxFlex="93" class="divFormFile">
                              <div class="setfileForm" *ngIf="form.file!= null && form.file.length >= 1;">
                                <mat-form-field appearance="fill" class="selectFileTemplate setfileForm"
                                                *ngIf="form.file !== null && form.file.length > 1" fxFlex="93"
                                                [style.z-index]="form.file != null && form.file.length > 0 ? 1 : -1">
                                  <mat-select placeholder="Xem mẫu đơn, tờ khai">
                                    <mat-option *ngFor="let file of form.file" value="{{ file.id }}"
                                                (click)="downloadFile(file.id, file.filename)">
                                      {{ file.filename }}
                                    </mat-option>
                                  </mat-select>
                                </mat-form-field>
                              </div>
                              <div class="setfileForm" *ngIf="form.file !== null && form.file.length === 1">
                                <label i18n="@@procedureTemplate">Mẫu đơn, Tờ khai</label>
                                <div class="file" *ngFor="let file of form.file" (click)="downloadFile(file.id, file.filename)" style="cursor:pointer" i18n-tooltip #tooltip="matTooltip" matTooltip="Tải xuống tệp tin">
                                  <div class="icon" [ngStyle]="{'background-image': 'url('+ getIconFilename(file)  +')'}"> </div>
                                  <div class="name">{{ file.filename }}</div>
                                </div>
                              </div>
                              <div *ngIf="form.fileLink != null && form.fileLink != ''" class="linkfileForm" fxLayout="row">
                                <a href="{{form.fileLink}}" target="_blank" *ngIf="form.fileLink != null && form.fileLink != ''">{{form?.fileLinkName ? form?.fileLinkName : 'Link file'}}</a>
                              </div>
                            </div>

                            <div fxFlex="{{flexGtSmColumnEmpty}}"></div>

                            <div class="listLoaiChungThuc" fxFlex.gt-sm='7' fxFlex.gt-xs="grow" fxFlex='grow' *ngIf="showCopies">
                              <div class="wrapList" fxLayout.xs="row" fxLayout.sm="row" fxLayout="row" fxLayoutAlign="space-evenly">
                                <div class="loaiChungThuc" fxFlex.gt-sm="95" fxFlex.gt-xs="95" fxFlex='grow'>
                                  <mat-form-field appearance="outline">
                                    <mat-label>Số bản</mat-label>
                                    <input matInput value="{{form.detail[0].quantity ? form.detail[0].quantity : 1}}"
                                           type="number" min="1" (input)="onQuantityChange(form.form.id, fd, $event.target.value)">
                                  </mat-form-field>
                                </div>
                              </div>
                            </div>

                            <div class="listLoaiChungThuc" fxFlex.gt-sm='7' fxFlex.gt-xs="grow" fxFlex='grow' *ngIf="batLienThongCTDT && selectedProcess?.isCTDT">
                              <div class="wrapList" fxLayout.xs="row" fxLayout.sm="row" fxLayout="row" fxLayoutAlign="space-evenly">
                                <div class="loaiChungThuc" fxFlex.gt-sm="95" fxFlex.gt-xs="95" fxFlex='grow'>
                                  <mat-form-field appearance="outline">
                                    <mat-label>Số trang</mat-label>
                                    <input matInput value="{{form.authentication?.pageNumber ? form.authentication?.pageNumber : 1}}"
                                           type="number" min="1" (input)="onPageNumberAuthChange(form.form.id, fd, $event.target.value)">
                                  </mat-form-field>
                                </div>
                              </div>
                            </div>

                            <div class="listLoaiChungThuc" fxFlex.gt-sm='14' fxFlex.gt-xs="grow" fxFlex='grow' *ngIf="batLienThongCTDT && selectedProcess?.isCTDT">
                              <div class="wrapList" fxLayout.xs="row" fxLayout.sm="row" fxLayout="row" fxLayoutAlign="space-evenly">
                                <div class="loaiChungThuc" fxFlex.gt-sm="95" fxFlex.gt-xs="95" fxFlex='grow'>
                                  <mat-form-field appearance="outline">
                                    <mat-label>Loại chứng thực</mat-label>
                                    <mat-select value="{{form.authentication.type.id ? form.authentication.type.id : defaultAuthTypeId}}"
                                                (selectionChange)="authenticationTypeChanged($event, form.form)">
                                      <mat-option *ngFor='let element of listAuthenticationType' value="{{element.id}}">
                                        {{ element.name }}
                                      </mat-option>
                                    </mat-select>
                                  </mat-form-field>
                                </div>
                              </div>
                            </div>

                            <div class="listUploadedFile" fxFlex.gt-sm='{{flexGtSmColumnListUploadedFile}}' fxFlex.gt-xs="grow" fxFlex='grow'>
                              <div class="wrapList" fxLayout.xs="row" fxLayout.sm="row" fxLayout="row"
                                   fxLayoutAlign="space-evenly">
                                <div class="file" fxFlex.gt-sm="49" fxFlex.gt-xs="49" fxFlex='grow'
                                     *ngFor="let detailFile of fd.file; let f = index;">
                                  <div class="icon"
                                       [ngStyle]="{'background-image': 'url('+ getIconFilename(detailFile)  +')'}"
                                       (click)="downloadFile(detailFile.id, detailFile.filename)">
                                  </div>
                                  <div style="cursor: pointer" class="name"
                                       (click)="downloadFile(detailFile.id, detailFile.filename)">
                                    {{detailFile.filename ? detailFile.filename : detailFile.name}}</div>

                                  <button *ngIf="digitalSignatureKGGMenu" mat-icon-button class="signFileBtn" [matMenuTriggerFor]="digitalSignatureMenu">
                                    <mat-icon>edit</mat-icon>
                                    <span i18n>Ký số</span>
                                  </button>

                                  <button mat-icon-button class="deleteFile" [matMenuTriggerFor]="actionMenu">
                                    <mat-icon>more_horiz</mat-icon>
                                  </button>

                                  <mat-menu #digitalSignatureMenu="matMenu" xPosition="before">
                                    <ng-container *ngIf="fd?.type?.id != '6204d0a679894379eae831c5'"
                                                  [ngTemplateOutlet]="signItemsMenu"
                                                  [ngTemplateOutletContext]="{ form: form, fd: fd, detailFile: detailFile}"
                                    >
                                    </ng-container>
                                  </mat-menu>

                                  <mat-menu #actionMenu="matMenu" xPosition="before">
                                    <a mat-menu-item class="menuAction" target="_blank" [routerLink]="routerLink(detailFile)">
                                      <mat-icon>format_size</mat-icon>
                                      <span i18n>Xem trước</span>
                                    </a>
                                    <button mat-menu-item class="menuAction" (click)="downloadFile(detailFile.id, detailFile.filename)">
                                      <mat-icon>cloud_download</mat-icon>
                                      <span i18n>Tải xuống tệp tin</span>
                                    </button>
                                    <button mat-menu-item class="menuAction"
                                            (click)="removeFormFile(form.form.id, fd.type.id, detailFile.id)">
                                      <mat-icon>close</mat-icon>
                                      <span>Xóa</span>
                                    </button>
                                    <ng-container *ngIf="fd?.type?.id != '6204d0a679894379eae831c5'"
                                                  [ngTemplateOutlet]="signItemsMenu"
                                                  [ngTemplateOutletContext]="{ form: form, fd: fd, detailFile: detailFile}"
                                    >
                                    </ng-container>
                                    <ng-template #signItemsMenu let-form="form" let-fd="fd" let-detailFile="detailFile">
                                      <button mat-menu-item class="menuAction"
                                              (click)="openPdfDigitalSignature(form.form.id, fd.type.id, detailFile, null, 1)"
                                              *ngIf="digitalSignature.VNPTSim && checkIfFileIsSupported(detailFile.filename)">
                                        <mat-icon>verified</mat-icon>
                                        <span>Ký số sim</span>
                                      </button>
                                      <button mat-menu-item class="menuAction"
                                              (click)="openPdfDigitalSignature(form.form.id, fd.type.id, detailFile, null)"
                                              *ngIf="digitalSignature.SmartCA && checkIfFileIsSupported(detailFile.filename)">
                                        <mat-icon>verified</mat-icon>
                                        <span>Ký số Smart CA</span>
                                      </button>
                                      <button mat-menu-item class="menuAction"
                                              (click)="openVGCAplugin(form.form.id, fd.type.id, detailFile.id, detailFile.filename, detailFile.size)"
                                              *ngIf="digitalSignature.VGCA && checkIfFileIsSupported(detailFile.filename)">
                                        <mat-icon>verified</mat-icon>
                                        <span *ngIf="!isEditSignTokenName; then thenBlock else elseBlock"></span>
                                        <ng-template #thenBlock>Ký số Ban Cơ yếu</ng-template>
                                        <ng-template #elseBlock>Ký số Token</ng-template>
                                      </button>
                                      <button mat-menu-item class="menuAction"
                                              (click)="openVGCAPluginSignIssue(form.form.id, fd.type.id, detailFile.id, detailFile.filename, detailFile.size)"
                                              *ngIf="!!allowVGCASignIssue">
                                        <mat-icon>verified</mat-icon>
                                        <span>Ký số văn thư Ban Cơ yếu</span>
                                      </button>
                                      <button mat-menu-item class="menuAction"
                                              (click)="openVGCAPluginSignCopy(form.form.id, fd.type.id, detailFile.id, detailFile.filename, detailFile.size)"
                                              *ngIf="!!isVgcaSignCopy">
                                        <mat-icon>verified</mat-icon>
                                        <span>{{vgcaSignLabel}}</span>
                                      </button>
                                      <button mat-menu-item class="menuAction"
                                              (click)="openVnptCaPlugin(form.form.id, fd.type.id, detailFile)"
                                              *ngIf="digitalSignature.VNPTCA && checkIfFileIsSupported(detailFile.filename)">
                                        <mat-icon>verified</mat-icon>
                                        <span>Ký số VNPT-CA</span>
                                      </button>
                                      <button mat-menu-item class="menuAction" (click)="openNEAC(form.form.id, fd.type.id, detailFile, null, 5)"
                                              *ngIf="digitalSignature.NEAC && checkIfFileIsSupported(detailFile.filename)">
                                        <mat-icon class="mainColor">verified</mat-icon>
                                        <span i18n="@@digitalSignNEAC">Ký số NEAC</span>
                                      </button>
                                      <button mat-menu-item class="menuAction" (click)="openPdfDigitalSignature(form.form.id, fd.type.id, detailFile, null, 6)"
                                              *ngIf="digitalSignature.QNM && checkIfFileIsSupported(detailFile.filename)">
                                        <mat-icon class="mainColor">verified</mat-icon>
                                        <span i18n="@@QNMDigitalSignature">Ký số tập trung QNM</span>
                                      </button>
                                      <!--                                                  <button mat-menu-item class="menuAction"-->
                                      <!--                                                          (click)="openHistory(detailFile.id)">-->
                                      <!--                                                    <mat-icon>refresh</mat-icon>-->
                                      <!--                                                    <span i18n="@@history">Xem lịch sử ký số</span>-->
                                      <!--                                                  </button>-->
                                      <button mat-menu-item class="menuAction">
                                        <view-sign-history [fileId]="detailFile.id">
                                          <mat-icon>refresh</mat-icon>
                                          <span>Xem lịch sử ký số</span>
                                        </view-sign-history>
                                      </button>
                                      <btn-save-form-orgin [getFormOrgin]="getFormOrgin"
                                                           [form]="form?.form"
                                                           [files]="fd.file"
                                                           [fileId]="detailFile.id"
                                                           checkOverride="true"
                                                           btnClass="menuAction"
                                                           [storage468]="storage468">
                                      </btn-save-form-orgin>
                                    </ng-template>
                                    <ng-container *ngIf="fd?.type?.id == '6204d0a679894379eae831c5'">
                                      <view-sign-item btnClass="menuAction"
                                                      [fileId]="detailFile.id"
                                                      [dirId]="0">
                                      </view-sign-item>
                                    </ng-container>
                                  </mat-menu>
                                </div>
                                <ng-container *ngIf="fd?.type?.id != '6204d0a679894379eae831c5'">
                                  <div fxLayout="row" fxLayoutAlign="space-between" class="uploadBtn"
                                       fxFlex.gt-sm="24" fxFlex.gt-xs="24" fxFlex='grow'
                                       *ngIf="fd.file.length % 2 != 0 && displayScanFilePadsvc">
                                    <button mat-button fxFlex='99' type="button" class="btn_upload"
                                            (click)="getInfo(form.form.id, fd.type.id, form.proDef); scanToPdfWithThumbnails()">
                                      <mat-icon>scanner</mat-icon>
                                      <span class="text">Scan tệp tin</span>
                                    </button>
                                  </div>
                                  <div fxLayout="row" fxLayoutAlign="space-between" class="uploadBtn"
                                       fxFlex.gt-sm="24" fxFlex.gt-xs="24" fxFlex='grow'
                                       *ngIf="fd.file.length % 2 != 0">
                                    <button mat-button fxFlex='99' class="btn_upload">
                                      <mat-icon>attachment</mat-icon>
                                      <span class="text" i18n>Chọn tệp tin</span>
                                    </button>
                                    <input multiple id="file_upload" type='file'
                                           (change)="onSelectFile($event, form.form.id, fd.type.id)"
                                           [accept]="acceptFileExtension" value="{{blankVal}}">
                                  </div>
                                  <div fxLayout="row" fxLayoutAlign="space-between" class="uploadBtn"
                                       fxFlex.gt-sm="49" fxFlex.gt-xs="49" fxFlex='grow'
                                       *ngIf="fd.file.length % 2 == 0 && displayScanFilePadsvc">
                                    <button mat-button fxFlex='99' type="button" class="btn_upload"
                                            (click)="getInfo(form.form.id, fd.type.id, form.proDef); scanToPdfWithThumbnails()">
                                      <mat-icon>scanner</mat-icon>
                                      <span class="text">Scan tệp tin</span>
                                    </button>
                                  </div>
                                  <div fxLayout="row" fxLayoutAlign="space-between" class="uploadBtn"
                                       fxFlex.gt-sm="49" fxFlex.gt-xs="49" fxFlex='grow'
                                       *ngIf="fd.file.length % 2 == 0">
                                    <button mat-button fxFlex='99' class="btn_upload">
                                      <mat-icon>attachment</mat-icon>
                                      <span class="text">Chọn tệp tin</span>
                                    </button>
                                    <input multiple id="file_upload" type='file'
                                           (change)="onSelectFile($event, form.form.id, fd.type.id)"
                                           [accept]="acceptFileExtension" value="{{blankVal}}">
                                  </div>
                                </ng-container>
                              </div>
                            </div>
                          </div>
                        </ng-container>
                      </mat-radio-group>
                    </div>
                    <btn-get-form-orgin [getFormOrgin]="getFormOrgin"
                                        (afterChoose)="afterChoose($event)"
                                        [form]="form?.form">
                    </btn-get-form-orgin>
                  </div>
                  <a mat-stroked-button class="btn_addNewFile" (click)="addForm()">
                    <mat-icon>add</mat-icon>
                    <span i18n>Thêm giấy tờ</span>
                  </a>
                </div>
              </form>
            </div>
            <div class="procedureFormFile" *ngIf="dossierId != null && dossierId != ''">
              <form [formGroup]="prForm">
                <div class="item" *ngFor="let form of procedureFormData">
                  <!-- [ngClass]="{'disabledHide': selectedImplementer.id !== form.case.id}"> -->
                  <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="column">
                    <div class="head" fxFlex='grow'>
                      <mat-icon class="requirement">check_circle_outline</mat-icon>
                      <span>{{ form.form.name }}</span>
                    </div>
                  </div>
                  <div class="body">
                    <mat-radio-group formControlName="rdo_File">
                      <div *ngFor="let fd of form.detail; let j = index">
                        <div *ngIf="fd.file.length > 0" class="rdo_File" fxLayout="row" fxLayout.xs="row" fxLayout.sm="row" fxLayoutAlign="space-between">
                          <div class="typeName" fxFlex.gt-sm='{{flexGtSmColumnTypeName2}}' fxFlex="65">
                            {{ fd.quantity }} {{ fd.type.name }}
                          </div>

                          <mat-form-field *ngIf="form.file !== null && form.file.length !== 0" appearance="fill" class="selectFileTemplate" fxFlex.gt-sm='{{flexGtSmColumnSelectFileTemplate}}' fxFlex="93" [style.z-index]="form.file !== null && form.file.length > 0 ? 1 : -1">
                            <mat-select  placeholder="Xem mẫu đơn, tờ khai">
                              <mat-option *ngFor="let file of form.file" value="{{ file.id }}" (click)="downloadFile(file.id, file.filename)">
                                {{ file.filename }}
                              </mat-option>
                            </mat-select>
                          </mat-form-field>

                          <div fxFlex="{{flexGtSmColumnEmpty}}"></div>

                          <div class="listLoaiChungThuc" fxFlex.gt-sm='7' fxFlex.gt-xs="grow" fxFlex='grow' *ngIf="showCopies">
                            <div class="wrapList" fxLayout.xs="row" fxLayout.sm="row" fxLayout="row" fxLayoutAlign="space-evenly">
                              <div class="loaiChungThuc" fxFlex.gt-sm="95" fxFlex.gt-xs="95" fxFlex='grow'>
                                <mat-form-field appearance="outline">
                                  <mat-label>Số bản</mat-label>
                                  <input matInput value="{{form.detail[0].quantity ? form.detail[0].quantity : 1}}"
                                         type="number" min="1" (input)="onQuantityChange(form.form.id, fd, $event.target.value)">
                                </mat-form-field>
                              </div>
                            </div>
                          </div>

                          <div class="listLoaiChungThuc" fxFlex.gt-sm='7' fxFlex.gt-xs="grow" fxFlex='grow' *ngIf="batLienThongCTDT && selectedProcess?.isCTDT">
                            <div class="wrapList" fxLayout.xs="row" fxLayout.sm="row" fxLayout="row" fxLayoutAlign="space-evenly">
                              <div class="loaiChungThuc" fxFlex.gt-sm="95" fxFlex.gt-xs="95" fxFlex='grow'>
                                <mat-form-field appearance="outline">
                                  <mat-label>Số trang</mat-label>
                                  <input matInput value="{{form.authentication?.pageNumber ? form.authentication?.pageNumber : 1}}"
                                         type="number" min="1" (input)="onPageNumberAuthChange(form.form.id, fd, $event.target.value)">
                                </mat-form-field>
                              </div>
                            </div>
                          </div>

                          <div class="listLoaiChungThuc" fxFlex.gt-sm='14' fxFlex.gt-xs="grow" fxFlex='grow' *ngIf="batLienThongCTDT && selectedProcess?.isCTDT">
                            <div class="wrapList" fxLayout.xs="row" fxLayout.sm="row" fxLayout="row" fxLayoutAlign="space-evenly">
                              <div class="loaiChungThuc" fxFlex.gt-sm="95" fxFlex.gt-xs="95" fxFlex='grow'>
                                <mat-form-field appearance="outline">
                                  <mat-label>Loại chứng thực</mat-label>
                                  <mat-select value="{{form.authentication.type.id ? form.authentication.type.id : defaultAuthTypeId}}"
                                              (selectionChange)="authenticationTypeChanged($event, form.form)">
                                    <mat-option *ngFor='let element of listAuthenticationType' value="{{element.id}}">
                                      {{ element.name }}
                                    </mat-option>
                                  </mat-select>
                                </mat-form-field>
                              </div>
                            </div>
                          </div>

                          <div class="listUploadedFile" fxFlex.gt-sm='{{flexGtSmColumnListUploadedFile}}' fxFlex.gt-xs="grow" fxFlex='grow'>
                            <div class="wrapList" fxLayout.xs="row" fxLayout.sm="row" fxLayout="row" fxLayoutAlign="space-evenly">
                              <div class="file" fxFlex.gt-sm="49" fxFlex.gt-xs="49" fxFlex='grow' *ngFor="let detailFile of fd.file; let f = index;">
                                <div class="icon" [ngStyle]="{'background-image': 'url(' + config.cloudStaticURL + 'icon/files/512x512/docx.png)'}">
                                </div>
                                <div class="name">
                                  {{detailFile.filename ? detailFile.filename: detailFile.name}}
                                </div>
                                <button mat-icon-button class="viewFile" (click)="viewFile(detailFile.id)">
                                  <mat-icon>launch</mat-icon>
                                </button>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </mat-radio-group>
                  </div>
                </div>
              </form>
            </div>
          </mat-tab>

          <!-- Fee -->
          <mat-tab *ngIf="!hiddenDossierFeeTab">
            <ng-template mat-tab-label>
              <mat-icon class="tabIcon">attach_money</mat-icon>
              <span i18n>Lệ phí</span>
            </ng-template>

            <form [formGroup]="feeForm" class="cardContent">
              <div *ngIf="showMethodPaymentLocal" style="padding-top: 1em;">
                <mat-form-field appearance="outline" fxFlex.gt-sm="20" fxFlex.gt-xs="20" fxFlex='grow'>
                  <mat-label>Mặc định hình thức thanh toán địa phương</mat-label>
                  <mat-select formControlName="defaultPaymentLocal" (selectionChange)="changePaymentMethod($event)">
                    <mat-option *ngFor='let pro of listPayment;' [value]="pro.id">
                      {{pro.name}}
                      <span *ngIf="pro.name == undefined || pro.name == null || pro.name.trim() == ''" i18n>(Không tìm thấy bản dịch)</span>
                    </mat-option>
                  </mat-select>
                </mat-form-field>
              </div>
              <div *ngIf="(enableRemoveVnpostFeeToAnotherTable == 1 && (dossierReceivingKind == (env?.vnpost?.receiveResultsByAddress != undefined ? env?.vnpost?.receiveResultsByAddress : this.config.receiveResultsByAddress))) || transferPaperId == transferPaperDocumentsPostalId || dossierReceivingKind == receiveResultsAdministrativeCenterId">
                <div class="mt-2 fee_head" *ngIf="vnpostFeeDataSource?.data != ''">
                  <div *ngIf="checkVnpostDatasource" class="s_head mb-1">
                    <span >Thông tin phí chuyển hồ sơ</span>
                  </div>
                  <div *ngIf="!checkVnpostDatasource" class="s_head mb-1">
                    <span i18n="@@vnPostDescriptionTitleH1">Thông tin phí thu hồ sơ/trả kết quả tại nhà</span>
                  </div>
                  <div class="s_head mb-1">
                    <div class='payment-status-vnpost' i18n="@@vnPostDescriptionTitleH2">(Nhân viên bưu chính sẽ thu phí trực tiếp tại nhà khi thu hồ sơ/trả kết quả)</div>
                  </div>
                </div>
                <div class="procedureFee" fxLayout="column" fxLayout.xs="column" fxLayout.sm="column">
                  <mat-table [dataSource]="vnpostFeeDataSource" fxFlex='grow' class="tblFee" *ngIf="vnpostFeeDataSource?.data != ''">
                    <ng-container matColumnDef="required">
                      <mat-header-cell *matHeaderCellDef class="checkAllProcedureAdd">
                        Bắt buộc thanh toán
                      </mat-header-cell>
                      <mat-cell *matCellDef="let row; let i = index;" data-label="Chọn" class="checkAllProcedureAdd" i18n-data-label>
                        <mat-checkbox [checked]="row.checked">
                        </mat-checkbox>
                      </mat-cell>
                      <mat-footer-cell *matFooterCellDef></mat-footer-cell>
                    </ng-container>
                    <ng-container matColumnDef="procostType">
                      <mat-header-cell *matHeaderCellDef i18n>Loại lệ phí</mat-header-cell>
                      <mat-cell *matCellDef="let row" i18n-data-label data-label="Loại lệ phí"> {{row.typeName}}
                      </mat-cell>
                      <mat-footer-cell *matFooterCellDef>Tổng</mat-footer-cell>
                    </ng-container>

                    <ng-container matColumnDef="quantity">
                      <mat-header-cell *matHeaderCellDef i18n>Số lượng</mat-header-cell>
                      <mat-cell *matCellDef="let row; let i = index" i18n-data-label data-label="Số lượng">
                        <span>{{row.quantity}}</span>
                      </mat-cell>
                      <mat-footer-cell *matFooterCellDef></mat-footer-cell>
                    </ng-container>

                    <ng-container matColumnDef="cost">
                      <mat-header-cell *matHeaderCellDef i18n>Mức lệ phí</mat-header-cell>
                      <mat-cell *matCellDef="let row; let i = index" i18n-data-label data-label="Mức lệ phí">
                        <span>{{row.cost|number}} {{row.monetaryUnit}}</span>
                        <mat-select (selectionChange)="changeCount(row.stt, $event)"
                                    fxFlex.gt-sm="150" fxFlex.gt-xs="150" value="{{row.costCaseIndex}}">
                          <mat-option *ngFor='let cost of row.costCase; let j = index;' title="{{cost.cost | number}} {{row.monetaryUnit}} ({{cost.description}})"
                                      value="{{j}}">
                            {{cost.cost | number}} {{row.monetaryUnit}} ({{cost.description}})
                          </mat-option>
                        </mat-select>
                      </mat-cell>
                      <mat-footer-cell *matFooterCellDef></mat-footer-cell>
                    </ng-container>

                    <ng-container matColumnDef="amount">
                      <mat-header-cell *matHeaderCellDef i18n>Thành tiền</mat-header-cell>
                      <mat-cell *matCellDef="let row" i18n-data-label data-label="Thành tiền">
                        {{row.quantity*row.cost|number}}&nbsp;{{row.monetaryUnit}}
                      </mat-cell>
                      <mat-footer-cell *matFooterCellDef class="totalCell">{{vnpostFeeCost.total}}</mat-footer-cell>
                    </ng-container>

                    <ng-container matColumnDef="description">
                      <mat-header-cell *matHeaderCellDef i18n>Mô tả</mat-header-cell>
                      <mat-cell *matCellDef="let row" i18n-data-label data-label="Mô tả"> {{row.description}}
                      </mat-cell>
                      <mat-footer-cell *matFooterCellDef class="payment-status-vnpost" i18n="@@vnPostDescriptionTotal">Nhân viên bưu chính sẽ thu phí trực tiếp tại nhà khi thu hồ sơ/trả kết quả</mat-footer-cell>
                    </ng-container>
                    <ng-container matColumnDef="pay">
                      <mat-header-cell *matHeaderCellDef i18n>Đã thanh toán</mat-header-cell>
                      <mat-cell *matCellDef="let row; let i = index" data-label="Thanh toán">
                        <input type="checkbox" [checked]="row.paid > 0" disabled>
                        <!-- <mat-checkbox *ngIf="editFeeIndex===i" formControlName="paided">
                        </mat-checkbox> -->
                      </mat-cell>
                      <mat-footer-cell *matFooterCellDef class="totalCell">
                        {{vnpostFeeCost.paided}}
                        <br>
                        (Còn lại:{{vnpostFeeCost.debt}})
                      </mat-footer-cell>
                    </ng-container>

                    <ng-container matColumnDef="action">
                      <mat-header-cell *matHeaderCellDef i18n>Thao tác</mat-header-cell>
                      <mat-cell *matCellDef="let row; let i = index" i18n-data-label data-label="Cập nhật" class="text-center">
                        <span>...</span>
                      </mat-cell>
                      <mat-footer-cell *matFooterCellDef></mat-footer-cell>
                    </ng-container>

                    <mat-header-row *matHeaderRowDef="feeDisplayedColumns"></mat-header-row>
                    <mat-row *matRowDef="let row; columns: feeDisplayedColumns;"></mat-row>
                    <mat-footer-row *matFooterRowDef="feeDisplayedColumns" [ngClass]="{'hidden': hideTotal == true}">
                    </mat-footer-row>
                  </mat-table>
                </div>
              </div>
              <div>
                <div class="mt-2 fee_head" *ngIf="enableRemoveVnpostFeeToAnotherTable == 1 && dossierReceivingKind == (env?.vnpost?.receiveResultsByAddress != undefined ? env?.vnpost?.receiveResultsByAddress : this.config.receiveResultsByAddress)">
                  <div class="s_head mb-1">
                    <span>Thông tin phí, lệ phí</span>
                  </div>
                  <div class="s_head mb-1">
                    <div class='payment-status-vnpost' i18n="@@dossierFeeDescriptionTitle">(Hiện tại hệ thống chỉ áp dụng thu phí, lệ phí hồ sơ. *Lưu ý: Đối với phí dịch vụ bưu chính (nếu có) người dân vui lòng thanh toán trực tiếp cho nhân viên bưu chính khi nhận kết quả tại nhà)</div>
                  </div>
                </div>
                <div class="procedureFee" fxLayout="column" fxLayout.xs="column" fxLayout.sm="column" *ngIf="!qbhEditFee">
                  <mat-table [dataSource]="feeDataSource" fxFlex='grow' class="tblFee">
                    <ng-container matColumnDef="required">
                      <mat-header-cell *matHeaderCellDef class="checkAllProcedureAdd">
                        Bắt buộc thanh toán
                      </mat-header-cell>
                      <mat-cell *matCellDef="let row; let i = index;" data-label="Chọn" class="checkAllProcedureAdd" i18n-data-label>
                        <mat-checkbox [checked]="row.checked">
                        </mat-checkbox>
                      </mat-cell>
                      <mat-footer-cell *matFooterCellDef></mat-footer-cell>
                    </ng-container>
                    <ng-container matColumnDef="procostType">
                      <mat-header-cell *matHeaderCellDef i18n>Loại lệ phí</mat-header-cell>
                      <mat-cell *matCellDef="let row" i18n-data-label data-label="Loại lệ phí"> {{row.typeName}}
                      </mat-cell>
                      <mat-footer-cell *matFooterCellDef>Tổng</mat-footer-cell>
                    </ng-container>

                    <ng-container matColumnDef="quantity">
                      <mat-header-cell *matHeaderCellDef i18n>Số lượng</mat-header-cell>
                      <mat-cell *matCellDef="let row; let i = index" i18n-data-label data-label="Số lượng">
                        <span *ngIf="editFeeIndex !==i">{{row.quantity}}</span>
                        <mat-form-field *ngIf="editFeeIndex===i" appearance="outline" fxFlex.gt-sm="80" fxFlex.gt-xs="80">
                          <mat-label></mat-label>
                          <input type="text" matInput formControlName="quantity" onlynumber gte="1" (blur)="updateFee(i, false)">
                        </mat-form-field>
                      </mat-cell>
                      <mat-footer-cell *matFooterCellDef></mat-footer-cell>
                    </ng-container>

                    <ng-container matColumnDef="cost">
                      <mat-header-cell *matHeaderCellDef i18n>Mức lệ phí</mat-header-cell>
                      <mat-cell *matCellDef="let row; let i = index" i18n-data-label data-label="Mức lệ phí">
                        <span *ngIf="editFeeIndex!==i">{{row.cost|number}} {{row.monetaryUnit}}</span>
                        <mat-select (selectionChange)="changeCount(row.stt, $event)" *ngIf="editFeeIndex === i"
                                    fxFlex.gt-sm="150" fxFlex.gt-xs="150" value="{{row.costCaseIndex}}">
                          <mat-option *ngFor='let cost of row.costCase; let j = index;' title="{{cost.cost | number}} {{cost.monetaryUnit}} ({{cost.description}})"
                                      value="{{j}}">
                            {{cost.cost | number}} {{cost.monetaryUnit}} ({{cost.description}})
                          </mat-option>
                        </mat-select>
                      </mat-cell>
                      <mat-footer-cell *matFooterCellDef></mat-footer-cell>
                    </ng-container>

                    <ng-container matColumnDef="amount">
                      <mat-header-cell *matHeaderCellDef i18n>Thành tiền</mat-header-cell>
                      <mat-cell *matCellDef="let row" i18n-data-label data-label="Thành tiền">
                        {{row.quantity*row.cost|number}}&nbsp;{{row.monetaryUnit}}
                      </mat-cell>
                      <mat-footer-cell *matFooterCellDef class="totalCell">{{feeCost.total}}</mat-footer-cell>
                    </ng-container>

                    <ng-container matColumnDef="description">
                      <mat-header-cell *matHeaderCellDef i18n>Mô tả</mat-header-cell>
                      <mat-cell *matCellDef="let row" i18n-data-label data-label="Mô tả"> {{row.description}}
                      </mat-cell>
                      <mat-footer-cell *matFooterCellDef></mat-footer-cell>
                    </ng-container>
                    <ng-container matColumnDef="pay">
                      <mat-header-cell *matHeaderCellDef i18n>Đã thanh toán</mat-header-cell>
                      <mat-cell *matCellDef="let row; let i = index" data-label="Thanh toán">
                        <input *ngIf="editFeeIndex!==i" type="checkbox" [checked]="row.paid > 0" disabled>
                        <mat-checkbox *ngIf="editFeeIndex===i" formControlName="paided" (change)="updateFee(i, false)">
                        </mat-checkbox>
                      </mat-cell>
                      <mat-footer-cell *matFooterCellDef class="totalCell">
                        {{feeCost.paided}}
                        <br>
                        (Còn lại:{{feeCost.debt}})
                      </mat-footer-cell>
                    </ng-container>

                    <ng-container matColumnDef="action">
                      <mat-header-cell *matHeaderCellDef i18n>Thao tác</mat-header-cell>
                      <mat-cell *matCellDef="let row; let i = index" i18n-data-label data-label="Cập nhật" class="text-center">
                        <span *ngIf="editFeeIndex ===i">...</span>
                        <mat-icon style="cursor: pointer" (click)="updateFee(i)">edit</mat-icon>
                      </mat-cell>
                      <mat-footer-cell *matFooterCellDef></mat-footer-cell>
                    </ng-container>

                    <mat-header-row *matHeaderRowDef="feeDisplayedColumns"></mat-header-row>
                    <mat-row *matRowDef="let row; columns: feeDisplayedColumns;"></mat-row>
                    <mat-footer-row *matFooterRowDef="feeDisplayedColumns" [ngClass]="{'hidden': hideTotal == true}">
                    </mat-footer-row>
                  </mat-table>
                </div>
                <div class="procedureFee" fxLayout="column" fxLayout.xs="column" fxLayout.sm="column" *ngIf="qbhEditFee">
                  <mat-table [dataSource]="feeDataSource" fxFlex='grow' class="tblFee">
                    <ng-container matColumnDef="required">
                      <mat-header-cell *matHeaderCellDef class="checkAllProcedureAdd">
                        Bắt buộc thanh toán
                      </mat-header-cell>
                      <mat-cell *matCellDef="let row; let i = index;" data-label="Chọn" class="checkAllProcedureAdd" i18n-data-label>
                        <mat-checkbox [checked]="row.checked">
                        </mat-checkbox>
                      </mat-cell>
                      <mat-footer-cell *matFooterCellDef></mat-footer-cell>
                    </ng-container>
                    <ng-container matColumnDef="procostType">
                      <mat-header-cell *matHeaderCellDef i18n>Loại lệ phí</mat-header-cell>
                      <mat-cell *matCellDef="let row" i18n-data-label data-label="Loại lệ phí"> {{row.typeName}}
                      </mat-cell>
                      <mat-footer-cell *matFooterCellDef>Tổng</mat-footer-cell>
                    </ng-container>

                    <ng-container matColumnDef="quantity">
                      <mat-header-cell *matHeaderCellDef i18n>Số lượng</mat-header-cell>
                      <mat-cell *matCellDef="let row; let i = index" i18n-data-label data-label="Số lượng">
                        <span *ngIf="editFeeIndex !==i">{{row.quantity}}</span>
                        <mat-form-field *ngIf="editFeeIndex===i" appearance="outline" fxFlex.gt-sm="80" fxFlex.gt-xs="80">
                          <mat-label></mat-label>
                          <input type="text" matInput formControlName="quantity" onlynumber gte="1">
                        </mat-form-field>
                      </mat-cell>
                      <mat-footer-cell *matFooterCellDef></mat-footer-cell>
                    </ng-container>

                    <ng-container matColumnDef="costEdit">
                      <mat-header-cell *matHeaderCellDef i18n>Mức lệ phí</mat-header-cell>
                      <mat-cell *matCellDef="let row; let i = index" i18n-data-label data-label="Số lượng">
                        <span *ngIf="editFeeIndex !==i">{{row.cost|number}} {{row.monetaryUnit}}</span>
                        <mat-form-field *ngIf="editFeeIndex===i" appearance="outline" fxFlex.gt-sm="80" fxFlex.gt-xs="80">
                          <mat-label></mat-label>
                          <input type="text" matInput formControlName="costEdit" [(ngModel)]="costEdit" onlynumber>
                        </mat-form-field>
                      </mat-cell>
                      <mat-footer-cell *matFooterCellDef></mat-footer-cell>
                    </ng-container>

                    <ng-container matColumnDef="cost" *ngIf="showColumn">
                      <mat-header-cell *matHeaderCellDef i18n>Mức lệ phí</mat-header-cell>
                      <mat-cell *matCellDef="let row; let i = index" i18n-data-label data-label="Mức lệ phí">
                        <span *ngIf="editFeeIndex!==i">{{row.cost|number}} {{row.monetaryUnit}}</span>
                        <mat-select (selectionChange)="changeCount(row.stt, $event)" *ngIf="editFeeIndex === i"
                                    fxFlex.gt-sm="150" fxFlex.gt-xs="150" value="{{row.costCaseIndex}}">
                          <mat-option *ngFor='let cost of row.costCase; let j = index;' title="{{cost.cost | number}} {{row.monetaryUnit}} ({{cost.description}})"
                                      value="{{j}}">
                            {{cost.cost | number}} {{row.monetaryUnit}} ({{cost.description}})
                          </mat-option>
                        </mat-select>
                      </mat-cell>
                      <mat-footer-cell *matFooterCellDef></mat-footer-cell>
                    </ng-container>

                    <ng-container matColumnDef="amount">
                      <mat-header-cell *matHeaderCellDef i18n>Thành tiền</mat-header-cell>
                      <mat-cell *matCellDef="let row" i18n-data-label data-label="Thành tiền">
                        {{row.quantity*row.cost|number}}&nbsp;{{row.monetaryUnit}}
                      </mat-cell>
                      <mat-footer-cell *matFooterCellDef class="totalCell">{{feeCost.total}}</mat-footer-cell>
                    </ng-container>

                    <ng-container matColumnDef="description">
                      <mat-header-cell *matHeaderCellDef i18n>Mô tả</mat-header-cell>
                      <mat-cell *matCellDef="let row" i18n-data-label data-label="Mô tả"> {{row.description}}
                      </mat-cell>
                      <mat-footer-cell *matFooterCellDef></mat-footer-cell>
                    </ng-container>
                    <ng-container matColumnDef="pay">
                      <mat-header-cell *matHeaderCellDef i18n>Đã thanh toán</mat-header-cell>
                      <mat-cell *matCellDef="let row; let i = index" data-label="Thanh toán">
                        <input *ngIf="editFeeIndex!==i" type="checkbox" [checked]="row.paid > 0" disabled>
                        <mat-checkbox *ngIf="editFeeIndex===i" formControlName="paided">
                        </mat-checkbox>
                      </mat-cell>
                      <mat-footer-cell *matFooterCellDef class="totalCell">
                        {{feeCost.paided}}
                        <br>
                        (Còn lại:{{feeCost.debt}})
                      </mat-footer-cell>
                    </ng-container>

                    <ng-container matColumnDef="action">
                      <mat-header-cell *matHeaderCellDef i18n>Thao tác</mat-header-cell>
                      <mat-cell *matCellDef="let row; let i = index" i18n-data-label data-label="Cập nhật" class="text-center">
                        <span *ngIf="editFeeIndex ===i">...</span>
                        <mat-icon style="cursor: pointer" (click)="updateFeeQBH(i)">edit</mat-icon>
                      </mat-cell>
                      <mat-footer-cell *matFooterCellDef></mat-footer-cell>
                    </ng-container>

                    <mat-header-row *matHeaderRowDef="feeDisplayedColumnsQBH"></mat-header-row>
                    <mat-row *matRowDef="let row; columns: feeDisplayedColumnsQBH;"></mat-row>
                    <mat-footer-row *matFooterRowDef="feeDisplayedColumnsQBH" [ngClass]="{'hidden': hideTotal == true}">
                    </mat-footer-row>
                  </mat-table>
                </div>
              </div>
            </form>

          </mat-tab>


          <!-- Detail -->
          <mat-tab *ngIf="!hiddenInformationDetailTab">
            <ng-template mat-tab-label>
              <mat-icon class="tabIcon">info</mat-icon>
              <span i18n>Thông tin chi tiết</span>
            </ng-template>
            <div class="thoaiisolate" *ngIf="rerenderEform">
              <div *ngIf="isShowCopyDossierAppliedButton">
                <br/>
                <button type="button" mat-stroked-button  class="btnSecondary" (click)="getListDossierOld()" >
                  <mat-icon>save</mat-icon>
                  <span>Sao chép thông tin từ hồ sơ cũ</span>
                </button>
                <br/>
              </div>
              <br/>
              <div  *ngIf="isAutoFillBusinessRegistrationEnterpriseEform"  class="button-search-dkkd" (click)="checkInforEnterprise()">
                <span>Kiểm tra thông tin doanh nghiệp</span>
              </div>
              <br>
              <div *ngIf="isNotFoundInfoEnterprise">
                <p class="text-left">
                  <span class="no-data-text">**Không tìm thấy Mã số thuế, mời bạn kiểm tra lại</span>
                </p>
              </div>
              <br>
              <formio #eFormComp [form]="eForm.component" [submission]="eForm.data" [renderOptions]="eForm.renderOptions" [readOnly]="isLoading" [viewOnly]="isLoading" *ngIf="eForm.id != undefined && eForm.id != ''" (change)="onChange($event)"></formio>
            </div>
            <mat-form-field appearance="outline" *ngIf="enablePrintTicket && listMauPhieu.length > 1" fxFlex.gt-sm="50" fxFlex.gt-xs="grow" fxFlex='grow'>
              <mat-label i18n>Mẫu phiếu</mat-label>
              <mat-select formControlName="listMauPhieu" (selectionChange)="phieuChanged($event)"
                          required>
                <mat-option *ngFor="let kind of listMauPhieu" value="{{kind.id}}">
                  {{kind.name}}
                  <span *ngIf="kind.name == undefined || kind.name == null || kind.name.trim() == ''" i18n>(Không tìm thấy bản dịch)</span>
                </mat-option>
              </mat-select>
            </mat-form-field>
            <button type="button" mat-stroked-button *ngIf="enablePrintTicket" (click)="printTicket()" class="printButton" style="padding-bottom: 9px;">
              <mat-icon>print</mat-icon>
              <span i18n>In phiếu</span>
            </button>
          </mat-tab>
          <!-- Thông tin nội bộ-->
          <mat-tab *ngIf="isShowInternalForm == true">
            <ng-template mat-tab-label>
              <mat-icon class="tabIcon">info</mat-icon>
              <span>Thông tin nội bộ</span>
            </ng-template>
            <div class="thoaiisolate" *ngIf="rerenderEInternalForm && eInternalForm.id != '' &&eInternalForm.id != null ">

              <formio #eInternalFormComp [form]="eInternalForm.component" [submission]="eInternalForm.data" [renderOptions]="eInternalForm.renderOptions" [readOnly]="isLoading" [viewOnly]="isLoading" *ngIf="eInternalForm.id != undefined && eInternalForm.id != ''"></formio>
            </div>

          </mat-tab>

          <!-- Receive Address -->
          <mat-tab *ngIf="!hiddenReceivingMethodTab">
            <ng-template mat-tab-label>
              <mat-icon class="tabIcon">verified</mat-icon>
              <span i18n>Hình thức nhận kết quả</span>
            </ng-template>
            <div *ngIf="isReceivingTemplate">
              <div fxLayout="row" fxLayout.xs="row" fxLayout.sm="row" style="margin-top: 15px" class="dossierDetail">
                <p class="procedureName">Đăng ký nhận thông báo về việc giải quyết hồ sơ</p>
              </div>
              <digo-check-send-notify functionType="receptionReceiving" rowTemplate="true" (checkAction)="checkActionCallback($event)"></digo-check-send-notify>
              <div fxLayout="row" fxLayout.xs="row" fxLayout.sm="row" class="formFieldItems" style="margin-top: 25px"
                   fxLayoutAlign="start stretch" fxLayoutGap="1rem" *ngIf="dossierReceivingKind != (env?.vnpost?.receiveResultsByAddress != undefined ? env?.vnpost?.receiveResultsByAddress : this.config.receiveResultsByAddress)">
                <mat-form-field appearance="outline" fxFlex.gt-sm="50" fxFlex.gt-xs="grow" fxFlex='grow' *ngIf="enableShowComboboxDossierResult == false">
                  <mat-label>Đăng ký nhận kết quả TTHC</mat-label>
                  <mat-select [disabled]="isDisable()" formControlName="receivingKind" (selectionChange)="receivingKindChange($event)"
                              required>
                    <mat-option *ngFor="let kind of listDossierReceivingKind" value="{{kind.id}}">
                      {{kind.name}}
                      <span *ngIf="kind.name == undefined || kind.name == null || kind.name.trim() == ''" i18n>(Không tìm thấy bản dịch)</span>
                    </mat-option>
                  </mat-select>
                </mat-form-field>
                <mat-form-field appearance="outline" fxFlex.gt-sm="50" fxFlex.gt-xs="grow" fxFlex='grow' *ngIf="enableShowComboboxDossierResult == true">
                  <mat-label>Đăng ký nhận kết quả TTHC</mat-label>
                  <mat-select formControlName="receivingKind" (selectionChange)="receivingKindChange($event)" >
                    <mat-option *ngFor="let kind of listDossierResultKind" [value]="kind.id" >
                      {{kind.name}}
                      <span *ngIf="kind.name == undefined || kind.name == null || kind.name.trim() == ''" i18n>(Không tìm thấy bản dịch)</span>
                    </mat-option>
                  </mat-select>
                </mat-form-field>
                <mat-form-field appearance="outline" fxFlex.gt-sm="20" fxFlex='grow' *ngIf="enableAppointmentDateEdit">
                  <mat-label><span>Ngày hẹn trả</span></mat-label>
                  <input matInput [ngxMatDatetimePicker]="appointmentDate" formControlName="appointmentDate" [max]="maxDate" (change)="oncheckDate()" [readonly]="!editTimeline">
                  <mat-datepicker-toggle matSuffix [for]="appointmentDate" [disabled]="!editTimeline"></mat-datepicker-toggle>
                  <ngx-mat-datetime-picker #appointmentDate [showSpinners]="showSpinners"
                                           [showSeconds]="showSeconds" [stepHour]="stepHour"
                                           [stepMinute]="stepMinute" [stepSecond]="stepSecond"
                                           [touchUi]="touchUi" [enableMeridian]="enableMeridian"
                                           [defaultTime]="defaultTime"
                                           [disabled]="!editTimeline">
                  </ngx-mat-datetime-picker>
                </mat-form-field>
              </div>
              <div fxLayout="row" fxLayout.xs="row" fxLayout.sm="row" class="formFieldItems" fxLayoutAlign="start" *ngIf="dossierReceivingKind == (env?.vnpost?.receiveResultsByAddress != undefined ? env?.vnpost?.receiveResultsByAddress : this.config.receiveResultsByAddress)">
                <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                  <mat-label>Đăng ký nhận kết quả TTHC</mat-label>
                  <mat-select [disabled]="isDisable()" formControlName="receivingKind" (selectionChange)="receivingKindChange($event)">
                    <mat-option required *ngFor="let kind of listDossierReceivingKind" [value]="kind.id">
                      {{kind.name}}
                      <span *ngIf="kind.name == undefined || kind.name == null || kind.name.trim() == ''" i18n>(Không tìm thấy bản dịch)</span>
                    </mat-option>
                  </mat-select>
                </mat-form-field>
              </div>
              <div fxLayout="row" fxLayout.xs="row" fxLayout.sm="row" class="formFieldItems" fxLayoutAlign="space-between" *ngIf="dossierReceivingKind == (env?.vnpost?.receiveResultsByAddress != undefined ? env?.vnpost?.receiveResultsByAddress : this.config.receiveResultsByAddress)">
                <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                  <mat-label >Tên</mat-label>
                  <input type="text" matInput formControlName="rcName" maxlength="500" [disabled]="isDisable()">
                </mat-form-field>

                <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                  <mat-label >Số điện thoại</mat-label>
                  <input type="text" matInput formControlName="rcPhoneNumber" maxlength="11" [disabled]="isDisable()">
                </mat-form-field>

                <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                  <mat-label >Email</mat-label>
                  <input type="email" matInput formControlName="rcEmail" maxlength="500" [disabled]="isDisable()">
                </mat-form-field>

                <div class="groupbox" fxLayout="row wrap" fxFlex.gt-sm="100" fxFlex="grow" fxLayoutAlign="space-between">
                  <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                    <mat-label i18n>Tỉnh/TP</mat-label>
                    <mat-select required formControlName="rcProvince" (selectionChange)="rcProvinceChange()" [disabled]="isDisable()">
                      <mat-option *ngFor='let provinceOpt of listProvinceForRC;' value="{{provinceOpt.id}}">
                        {{provinceOpt.name}} </mat-option>
                    </mat-select>
                  </mat-form-field>

                  <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                    <mat-label>Phường/xã</mat-label>
                    <mat-select required formControlName="rcDistrict" (selectionChange)="rcDistrictChange()" [disabled]="this.tabForm.get('rcProvince').value == '' || isDisable()">
                      <mat-option *ngFor='let districtOpt of listDistrictForRC;' value="{{districtOpt.id}}">
                        {{districtOpt.name}} </mat-option>
                    </mat-select>
                  </mat-form-field>

                  <!-- <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                    <mat-label i18n>Phường/xã</mat-label>
                    <mat-select required formControlName="rcVillage" [disabled]="this.tabForm.get('rcDistrict').value == '' || isDisable()">
                      <mat-option *ngFor='let wardtOpt of listVillageForRC;' value="{{wardtOpt.id}}">
                        {{wardtOpt.name}}
                      </mat-option>
                    </mat-select>
                  </mat-form-field> -->

                  <mat-form-field appearance="outline" fxFlex.gt-sm="100" fxFlex.gt-xs="grow" fxFlex='grow'>
                    <mat-label i18n>Địa chỉ chi tiết</mat-label>
                    <input type="text" matInput formControlName="customAddress" [readonly]="isDisable()">
                  </mat-form-field>
                </div>
              </div>
            </div>
            <div *ngIf="!isReceivingTemplate">
              <div fxLayout="row" fxLayout.xs="row" fxLayout.sm="row" class="formFieldItems" style="margin-top: 15px"
                   fxLayoutAlign="start stretch" fxLayoutGap="1rem" *ngIf="dossierReceivingKind != (env?.vnpost?.receiveResultsByAddress != undefined ? env?.vnpost?.receiveResultsByAddress : this.config.receiveResultsByAddress)">
                <mat-form-field appearance="outline" fxFlex.gt-sm="50" fxFlex.gt-xs="grow" fxFlex='grow' *ngIf="enableShowComboboxDossierResult == false">
                  <mat-label i18n>Hình thức nhận kết quả</mat-label>
                  <mat-select [disabled]="isDisable()" formControlName="receivingKind" (selectionChange)="receivingKindChange($event)"
                              required>
                    <mat-option *ngFor="let kind of listDossierReceivingKind" value="{{kind.id}}">
                      {{kind.name}}
                      <span *ngIf="kind.name == undefined || kind.name == null || kind.name.trim() == ''" i18n>(Không tìm thấy bản dịch)</span>
                    </mat-option>
                  </mat-select>
                </mat-form-field>
                <mat-form-field appearance="outline" fxFlex.gt-sm="50" fxFlex.gt-xs="grow" fxFlex='grow' *ngIf="enableShowComboboxDossierResult == true">
                  <mat-label i18n>Hình thức nhận kết quả</mat-label>
                  <mat-select formControlName="receivingKind" (selectionChange)="receivingKindChange($event)" >
                    <mat-option *ngFor="let kind of listDossierResultKind" [value]="kind.id" >
                      {{kind.name}}
                      <span *ngIf="kind.name == undefined || kind.name == null || kind.name.trim() == ''" i18n>(Không tìm thấy bản dịch)</span>
                    </mat-option>
                  </mat-select>
                </mat-form-field>
                <mat-form-field appearance="outline" fxFlex.gt-sm="20" fxFlex='grow' *ngIf="enableAppointmentDateEdit">
                  <mat-label><span>Ngày hẹn trả</span></mat-label>
                  <input matInput [ngxMatDatetimePicker]="appointmentDate" formControlName="appointmentDate" [max]="maxDate" (change)="oncheckDate()" [readonly]="!editTimeline">
                  <mat-datepicker-toggle matSuffix [for]="appointmentDate" [disabled]="!editTimeline"></mat-datepicker-toggle>
                  <ngx-mat-datetime-picker #appointmentDate [showSpinners]="showSpinners"
                                           [showSeconds]="showSeconds" [stepHour]="stepHour"
                                           [stepMinute]="stepMinute" [stepSecond]="stepSecond"
                                           [touchUi]="touchUi" [enableMeridian]="enableMeridian"
                                           [defaultTime]="defaultTime"
                                           [disabled]="!editTimeline">
                  </ngx-mat-datetime-picker>
                </mat-form-field>
              </div>
              <div class="nhanKqQ6" *ngIf="customNhanKqQ6">
                Nhận trực tiếp tại Hệ thống Tiếp nhận và Trả hồ sơ tự động 24/7
                <span>chỉ áp dụng đối với các thủ tục hành chính thuộc thẩm quyền giải quyết của Uỷ ban nhân dân Quận 6</span>
              </div>
              <div fxLayout="row" fxLayout.xs="row" fxLayout.sm="row" class="formFieldItems" fxLayoutAlign="start" *ngIf="dossierReceivingKind == (env?.vnpost?.receiveResultsByAddress != undefined ? env?.vnpost?.receiveResultsByAddress : this.config.receiveResultsByAddress)">
                <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                  <mat-label i18n>Hình thức nhận kết quả</mat-label>
                  <mat-select [disabled]="isDisable()" formControlName="receivingKind" (selectionChange)="receivingKindChange($event)">
                    <mat-option required *ngFor="let kind of listDossierReceivingKind" [value]="kind.id">
                      {{kind.name}}
                      <span *ngIf="kind.name == undefined || kind.name == null || kind.name.trim() == ''" i18n>(Không tìm thấy bản dịch)</span>
                    </mat-option>
                  </mat-select>
                </mat-form-field>
              </div>
              <div fxLayout="row" fxLayout.xs="row" fxLayout.sm="row" class="formFieldItems" fxLayoutAlign="space-between" *ngIf="dossierReceivingKind == (env?.vnpost?.receiveResultsByAddress != undefined ? env?.vnpost?.receiveResultsByAddress : this.config.receiveResultsByAddress)">
                <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                  <mat-label >Tên</mat-label>
                  <input type="text" matInput formControlName="rcName" maxlength="500" [disabled]="isDisable()">
                </mat-form-field>

                <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                  <mat-label >Số điện thoại</mat-label>
                  <input type="text" matInput formControlName="rcPhoneNumber" maxlength="11" [disabled]="isDisable()">
                </mat-form-field>

                <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                  <mat-label >Email</mat-label>
                  <input type="email" matInput formControlName="rcEmail" maxlength="500" [disabled]="isDisable()">
                </mat-form-field>

                <div class="groupbox" fxLayout="row wrap" fxFlex.gt-sm="100" fxFlex="grow" fxLayoutAlign="space-between">
                  <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                    <mat-label i18n>Tỉnh/TP</mat-label>
                    <mat-select required formControlName="rcProvince" (selectionChange)="rcProvinceChange()" [disabled]="isDisable()">
                      <mat-option *ngFor='let provinceOpt of listProvinceForRC;' value="{{provinceOpt.id}}">
                        {{provinceOpt.name}} </mat-option>
                    </mat-select>
                  </mat-form-field>

                  <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                    <mat-label>Phường/xã</mat-label>
                    <mat-select required formControlName="rcDistrict" (selectionChange)="rcDistrictChange()" [disabled]="this.tabForm.get('rcProvince').value == '' || isDisable()">
                      <mat-option *ngFor='let districtOpt of listDistrictForRC;' value="{{districtOpt.id}}">
                        {{districtOpt.name}} </mat-option>
                    </mat-select>
                  </mat-form-field>

                  <!-- <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                    <mat-label i18n>Phường/xã</mat-label>
                    <mat-select required formControlName="rcVillage" [disabled]="this.tabForm.get('rcDistrict').value == '' || isDisable()">
                      <mat-option *ngFor='let wardtOpt of listVillageForRC;' value="{{wardtOpt.id}}">
                        {{wardtOpt.name}}
                      </mat-option>
                    </mat-select>
                  </mat-form-field> -->

                  <mat-form-field appearance="outline" fxFlex.gt-sm="100" fxFlex.gt-xs="grow" fxFlex='grow'>
                    <mat-label i18n>Địa chỉ chi tiết</mat-label>
                    <input type="text" matInput formControlName="customAddress" [readonly]="isDisable()">
                  </mat-form-field>
                </div>
              </div>
              <div fxLayout="row" fxLayout.xs="row" fxLayout.sm="row" class="formFieldItems" fxLayoutAlign="space-between"
                   *ngIf="dossierReceivingKind == receiveResultsAdministrativeCenterId">
                <div class="bordered-container">
                  <div class="label-text">Nơi gửi</div>
                  <form [formGroup]="tabSpecializedAgencyForm" novalidate>
                    <div fxLayout="row" fxLayout.xs="row" fxLayout.sm="row" class="formFieldItems" fxLayoutAlign="space-between">
                      <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                        <mat-label>Tên</mat-label>
                        <input type="text" matInput formControlName="rcName" maxlength="500">
                      </mat-form-field>

                      <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                        <mat-label>Số điện thoại</mat-label>
                        <input type="text" matInput formControlName="rcPhoneNumber" maxlength="11">
                      </mat-form-field>

                      <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                        <mat-label>Email</mat-label>
                        <input type="email" matInput formControlName="rcEmail" maxlength="500">
                      </mat-form-field>

                      <div class="groupbox" fxLayout="row wrap" fxFlex.gt-sm="100" fxFlex="grow" fxLayoutAlign="space-between">
                        <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                          <mat-label>Tỉnh/TP</mat-label>
                          <mat-select required formControlName="rcProvince" (selectionChange)="tabSpecializedAgencyFormProvinceChange()">
                            <mat-option *ngFor='let provinceOpt of listProvinceForSpecializedAgency;' value="{{provinceOpt.id}}">
                              {{provinceOpt.name}} </mat-option>
                          </mat-select>
                        </mat-form-field>

                        <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                          <mat-label>Phường/xã</mat-label>
                          <mat-select required formControlName="rcDistrict" (selectionChange)="tabSpecializedAgencyFormDistrictChange()"
                                      [disabled]="this.tabSpecializedAgencyForm.get('rcProvince').value == '' ">
                            <mat-option *ngFor='let districtOpt of listDistrictForSpecializedAgency;' value="{{districtOpt.id}}">
                              {{districtOpt.name}} </mat-option>
                          </mat-select>
                        </mat-form-field>

                        <!-- <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                          <mat-label>Phường/xã</mat-label>
                          <mat-select required formControlName="rcVillage"
                                      [disabled]="this.tabSpecializedAgencyForm.get('rcDistrict').value == '' ">
                            <mat-option *ngFor='let wardtOpt of listVillageForSpecializedAgency;' value="{{wardtOpt.id}}">
                              {{wardtOpt.name}}
                            </mat-option>
                          </mat-select>
                        </mat-form-field> -->

                        <mat-form-field appearance="outline" fxFlex.gt-sm="100" fxFlex.gt-xs="grow" fxFlex='grow'>
                          <mat-label>Địa chỉ chi tiết</mat-label>
                          <input type="text" matInput formControlName="customAddress">
                        </mat-form-field>
                      </div>
                    </div>
                  </form>
                </div>
                <div class="bordered-container">
                  <div class="label-text">Nơi nhận</div>
                  <form [formGroup]="tabAdministrativeCenterForm" novalidate>
                    <div fxLayout="row" fxLayout.xs="row" fxLayout.sm="row" class="formFieldItems" fxLayoutAlign="space-between">
                      <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                        <mat-label>Tên</mat-label>
                        <input type="text" matInput formControlName="rcName" maxlength="500">
                      </mat-form-field>

                      <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                        <mat-label>Số điện thoại</mat-label>
                        <input type="text" matInput formControlName="rcPhoneNumber" maxlength="11">
                      </mat-form-field>

                      <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                        <mat-label>Email</mat-label>
                        <input type="email" matInput formControlName="rcEmail" maxlength="500">
                      </mat-form-field>

                      <div class="groupbox" fxLayout="row wrap" fxFlex.gt-sm="100" fxFlex="grow" fxLayoutAlign="space-between">
                        <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                          <mat-label>Tỉnh/TP</mat-label>
                          <mat-select required formControlName="rcProvince" (selectionChange)="tabAdministrativeCenterFormProvinceChange()">
                            <mat-option *ngFor='let provinceOpt of listProvinceForAdministrativeCenter;' value="{{provinceOpt.id}}">
                              {{provinceOpt.name}} </mat-option>
                          </mat-select>
                        </mat-form-field>

                        <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                          <mat-label>Phường/xã</mat-label>
                          <mat-select required formControlName="rcDistrict" (selectionChange)="tabAdministrativeCenterFormDistrictChange()"
                                      [disabled]="this.tabAdministrativeCenterForm.get('rcProvince').value == '' ">
                            <mat-option *ngFor='let districtOpt of listDistrictForAdministrativeCenter;' value="{{districtOpt.id}}">
                              {{districtOpt.name}} </mat-option>
                          </mat-select>
                        </mat-form-field>
<!-- 
                        <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                          <mat-label>Phường/xã</mat-label>
                          <mat-select required formControlName="rcVillage"
                                      [disabled]="this.tabAdministrativeCenterForm.get('rcDistrict').value == '' ">
                            <mat-option *ngFor='let wardtOpt of listVillageForAdministrativeCenter;' value="{{wardtOpt.id}}">
                              {{wardtOpt.name}}
                            </mat-option>
                          </mat-select>
                        </mat-form-field> -->

                        <mat-form-field appearance="outline" fxFlex.gt-sm="100" fxFlex.gt-xs="grow" fxFlex='grow'>
                          <mat-label>Địa chỉ chi tiết</mat-label>
                          <input type="text" matInput formControlName="customAddress">
                        </mat-form-field>
                      </div>
                    </div>
                  </form>
                </div>
              </div>
              <div *ngIf="dossierReceivingKind == receiveResultsAdministrativeCenterId">
                <form [formGroup]="feeForm" class="cardContent">
                  <div *ngIf="vnpostReceiveResultFeeDataSource?.data != ''">
                    <div class="mt-2 fee_head">
                      <div class="s_head mb-1">
                        <span>Thông tin phí chuyển hồ sơ</span>
                      </div>
                      <!-- <div class="s_head mb-1">
                        <div class='payment-status-vnpost'>
                          <div class='payment-status-vnpost' i18n="@@dossierFeeDescriptionTitle">(Hiện tại hệ thống chỉ áp dụng thu phí,
                            lệ phí hồ sơ. *Lưu ý: Đối với phí dịch vụ bưu chính (nếu có) người dân vui lòng thanh toán trực tiếp cho
                            nhân viên bưu chính khi nhận kết quả tại nhà)</div>
                        </div>
                      </div> -->
                    </div>
                    <div class="procedureFee" fxLayout="column" fxLayout.xs="column" fxLayout.sm="column">
                      <mat-table [dataSource]="vnpostReceiveResultFeeDataSource" fxFlex='grow' class="tblFee">
                        <!-- <ng-container matColumnDef="required">
                          <mat-header-cell *matHeaderCellDef class="checkAllProcedureAdd">
                            Bắt buộc thanh toán
                          </mat-header-cell>
                          <mat-cell *matCellDef="let row; let i = index;" data-label="Chọn" class="checkAllProcedureAdd" i18n-data-label>
                            <mat-checkbox [checked]="row.checked">
                            </mat-checkbox>
                          </mat-cell>
                          <mat-footer-cell *matFooterCellDef></mat-footer-cell>
                        </ng-container> -->
                        <ng-container matColumnDef="procostType">
                          <mat-header-cell *matHeaderCellDef i18n>Loại lệ phí</mat-header-cell>
                          <mat-cell *matCellDef="let row" i18n-data-label data-label="Loại lệ phí"> {{row.typeName}}
                          </mat-cell>
                          <mat-footer-cell *matFooterCellDef>Tổng</mat-footer-cell>
                        </ng-container>

                        <ng-container matColumnDef="quantity">
                          <mat-header-cell *matHeaderCellDef i18n>Số lượng</mat-header-cell>
                          <mat-cell *matCellDef="let row; let i = index" i18n-data-label data-label="Số lượng">
                            <span>{{row.quantity}}</span>
                          </mat-cell>
                          <mat-footer-cell *matFooterCellDef></mat-footer-cell>
                        </ng-container>

                        <ng-container matColumnDef="cost">
                          <mat-header-cell *matHeaderCellDef i18n>Mức lệ phí</mat-header-cell>
                          <mat-cell *matCellDef="let row; let i = index" i18n-data-label data-label="Mức lệ phí">
                            <span>{{row.cost|number}} {{row.monetaryUnit}}</span>
                            <!-- <mat-select (selectionChange)="changeCount(row.stt, $event)" *ngIf="editFeeIndex === i"
                                        fxFlex.gt-sm="150" fxFlex.gt-xs="150" value="{{row.costCaseIndex}}">
                                <mat-option *ngFor='let cost of row.costCase; let j = index;' title="{{cost.cost | number}} {{row.monetaryUnit}} ({{cost.description}})"
                                            value="{{j}}">
                                    {{cost.cost | number}} {{row.monetaryUnit}} ({{cost.description}})
                                </mat-option>
                            </mat-select> -->
                          </mat-cell>
                          <mat-footer-cell *matFooterCellDef></mat-footer-cell>
                        </ng-container>

                        <ng-container matColumnDef="amount">
                          <mat-header-cell *matHeaderCellDef i18n>Thành tiền</mat-header-cell>
                          <mat-cell *matCellDef="let row" i18n-data-label data-label="Thành tiền">
                            {{row.quantity*row.cost|number}}&nbsp;{{row.monetaryUnit}}
                          </mat-cell>
                          <mat-footer-cell *matFooterCellDef class="totalCell">{{vnpostReceiveResultFeeCost.total}}</mat-footer-cell>
                        </ng-container>

                        <ng-container matColumnDef="description">
                          <mat-header-cell *matHeaderCellDef i18n>Mô tả</mat-header-cell>
                          <mat-cell *matCellDef="let row" i18n-data-label data-label="Mô tả"> {{row.description}}
                          </mat-cell>
                          <mat-footer-cell *matFooterCellDef></mat-footer-cell>
                        </ng-container>
                        <ng-container matColumnDef="pay">
                          <mat-header-cell *matHeaderCellDef i18n>Đã thanh toán</mat-header-cell>
                          <mat-cell *matCellDef="let row; let i = index" data-label="Thanh toán">
                            <input type="checkbox" [checked]="row.paid > 0" disabled>
                          </mat-cell>
                          <mat-footer-cell *matFooterCellDef class="totalCell">
                            {{vnpostReceiveResultFeeCost.paided}}
                            <br>
                            (Còn lại:{{vnpostReceiveResultFeeCost.debt}})
                          </mat-footer-cell>
                        </ng-container>

                        <ng-container matColumnDef="action">
                          <mat-header-cell *matHeaderCellDef i18n>Thao tác</mat-header-cell>
                          <mat-cell *matCellDef="let row; let i = index" i18n-data-label data-label="Cập nhật" class="text-center">
                            <span>...</span>
                          </mat-cell>
                          <mat-footer-cell *matFooterCellDef></mat-footer-cell>
                        </ng-container>

                        <mat-header-row *matHeaderRowDef="feeDisplayedColumns"></mat-header-row>
                        <mat-row *matRowDef="let row; columns: feeDisplayedColumns;"></mat-row>
                        <mat-footer-row *matFooterRowDef="feeDisplayedColumns" [ngClass]="{'hidden': hideTotal == true}">
                        </mat-footer-row>
                      </mat-table>
                    </div>
                  </div>
                </form>
              </div>
            </div>
          </mat-tab>

          <!-- 5343 -->
          <mat-tab #tab7 *ngIf="selectedProcess?.processDefinition?.dynamicVariable?.processType == 3">
            <ng-template mat-tab-label>
              <mat-icon class="tabIcon">verified</mat-icon>
              <span>Hình thức chuyển hồ sơ giấy</span>
            </ng-template>
            <div>
              <div fxLayout="row" fxLayout.xs="row" fxLayout.sm="row" class="formFieldItems" style="margin-top: 15px"
                   fxLayoutAlign="start stretch" fxLayoutGap="1rem">
                <mat-form-field appearance="outline" fxFlex.gt-sm="50" fxFlex.gt-xs="grow" fxFlex='grow'>
                  <mat-label>Hình thức chuyển hồ sơ giấy</mat-label>
                  <mat-select formControlName="transferPaper"
                              (selectionChange)="transferPaperChange($event)" required>
                    <mat-option value="">Không có hồ sơ giấy</mat-option>
                    <mat-option *ngFor="let kind of listTransferPaper" value="{{kind.id}}">
                      {{kind.name}}
                      <span *ngIf="kind.name == undefined || kind.name == null || kind.name.trim() == ''">(Không tìm thấy bản
                                  dịch)</span>
                    </mat-option>
                  </mat-select>
                </mat-form-field>
              </div>
              <div fxLayout="row" fxLayout.xs="row" fxLayout.sm="row" class="formFieldItems" fxLayoutAlign="space-between"
                   *ngIf="transferPaperId == transferPaperDocumentsPostalId">
                <div class="bordered-container">
                  <div class="label-text">Nơi gửi</div>
                  <form [formGroup]="tabAdministrativeCenterForm2" novalidate>
                    <div fxLayout="row" fxLayout.xs="row" fxLayout.sm="row" class="formFieldItems" fxLayoutAlign="space-between">
                      <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                        <mat-label>Tên</mat-label>
                        <input type="text" matInput formControlName="rcName" maxlength="500">
                      </mat-form-field>

                      <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                        <mat-label>Số điện thoại</mat-label>
                        <input type="text" matInput formControlName="rcPhoneNumber" maxlength="11">
                      </mat-form-field>

                      <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                        <mat-label>Email</mat-label>
                        <input type="email" matInput formControlName="rcEmail" maxlength="500">
                      </mat-form-field>

                      <div class="groupbox" fxLayout="row wrap" fxFlex.gt-sm="100" fxFlex="grow" fxLayoutAlign="space-between">
                        <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                          <mat-label>Tỉnh/TP</mat-label>
                          <mat-select required formControlName="rcProvince" (selectionChange)="tabAdministrativeCenterForm2ProvinceChange()">
                            <mat-option *ngFor='let provinceOpt of listProvinceForAdministrativeCenter2;' value="{{provinceOpt.id}}">
                              {{provinceOpt.name}} </mat-option>
                          </mat-select>
                        </mat-form-field>

                        <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                          <mat-label>Phường/xã</mat-label>
                          <mat-select required formControlName="rcDistrict" (selectionChange)="tabAdministrativeCenterForm2DistrictChange()"
                                      [disabled]="this.tabAdministrativeCenterForm2.get('rcProvince').value == '' ">
                            <mat-option *ngFor='let districtOpt of listDistrictForAdministrativeCenter2;' value="{{districtOpt.id}}">
                              {{districtOpt.name}} </mat-option>
                          </mat-select>
                        </mat-form-field>

                        <!-- <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                          <mat-label>Phường/xã</mat-label>
                          <mat-select required formControlName="rcVillage"
                                      [disabled]="this.tabAdministrativeCenterForm2.get('rcDistrict').value == '' ">
                            <mat-option *ngFor='let wardtOpt of listVillageForAdministrativeCenter2;' value="{{wardtOpt.id}}">
                              {{wardtOpt.name}}
                            </mat-option>
                          </mat-select>
                        </mat-form-field> -->

                        <mat-form-field appearance="outline" fxFlex.gt-sm="100" fxFlex.gt-xs="grow" fxFlex='grow'>
                          <mat-label>Địa chỉ chi tiết</mat-label>
                          <input type="text" matInput formControlName="customAddress">
                        </mat-form-field>
                      </div>
                    </div>
                  </form>
                </div>
                <div class="bordered-container">
                  <div class="label-text">Nơi nhận</div>
                  <form [formGroup]="tabSpecializedAgencyForm2" novalidate>
                    <div fxLayout="row" fxLayout.xs="row" fxLayout.sm="row" class="formFieldItems" fxLayoutAlign="space-between">
                      <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                        <mat-label>Tên</mat-label>
                        <input type="text" matInput formControlName="rcName" maxlength="500">
                      </mat-form-field>

                      <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                        <mat-label>Số điện thoại</mat-label>
                        <input type="text" matInput formControlName="rcPhoneNumber" maxlength="11">
                      </mat-form-field>

                      <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                        <mat-label>Email</mat-label>
                        <input type="email" matInput formControlName="rcEmail" maxlength="500">
                      </mat-form-field>

                      <div class="groupbox" fxLayout="row wrap" fxFlex.gt-sm="100" fxFlex="grow" fxLayoutAlign="space-between">
                        <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                          <mat-label>Tỉnh/TP</mat-label>
                          <mat-select required formControlName="rcProvince" (selectionChange)="tabSpecializedAgencyForm2ProvinceChange()">
                            <mat-option *ngFor='let provinceOpt of listProvinceForSpecializedAgency2;' value="{{provinceOpt.id}}">
                              {{provinceOpt.name}} </mat-option>
                          </mat-select>
                        </mat-form-field>

                        <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                          <mat-label>Phường/xã</mat-label>
                          <mat-select required formControlName="rcDistrict" (selectionChange)="tabSpecializedAgencyForm2DistrictChange()"
                                      [disabled]="this.tabSpecializedAgencyForm2.get('rcProvince').value == '' ">
                            <mat-option *ngFor='let districtOpt of listDistrictForSpecializedAgency2;' value="{{districtOpt.id}}">
                              {{districtOpt.name}} </mat-option>
                          </mat-select>
                        </mat-form-field>

                        <!-- <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                          <mat-label>Phường/xã</mat-label>
                          <mat-select required formControlName="rcVillage"
                                      [disabled]="this.tabSpecializedAgencyForm2.get('rcDistrict').value == '' ">
                            <mat-option *ngFor='let wardtOpt of listVillageForSpecializedAgency2;' value="{{wardtOpt.id}}">
                              {{wardtOpt.name}}
                            </mat-option>
                          </mat-select>
                        </mat-form-field> -->

                        <mat-form-field appearance="outline" fxFlex.gt-sm="100" fxFlex.gt-xs="grow" fxFlex='grow'>
                          <mat-label>Địa chỉ chi tiết</mat-label>
                          <input type="text" matInput formControlName="customAddress">
                        </mat-form-field>
                      </div>
                    </div>
                  </form>
                </div>
              </div>
              <div>
                <form [formGroup]="feeForm" class="cardContent">
                  <div  *ngIf="vnpostTransferPaperFeeDataSource?.data != ''">
                    <div class="mt-2 fee_head">
                      <div class="s_head mb-1">
                        <span>Thông tin phí chuyển hồ sơ</span>
                      </div>
                      <!-- <div class="s_head mb-1">
                        <div class='payment-status-vnpost'>(Hiện tại hệ thống chỉ áp dụng thu phí,
                          lệ phí hồ sơ. *Lưu ý: Đối với phí dịch vụ bưu chính (nếu có) người dân vui lòng thanh toán trực tiếp cho
                          nhân viên bưu chính khi nhận kết quả tại nhà)</div>
                      </div> -->
                    </div>
                    <div class="procedureFee" fxLayout="column" fxLayout.xs="column" fxLayout.sm="column">
                      <mat-table [dataSource]="vnpostTransferPaperFeeDataSource" fxFlex='grow' class="tblFee">
                        <!-- <ng-container matColumnDef="required">
                          <mat-header-cell *matHeaderCellDef class="checkAllProcedureAdd">
                            Bắt buộc thanh toán
                          </mat-header-cell>
                          <mat-cell *matCellDef="let row; let i = index;" data-label="Chọn" class="checkAllProcedureAdd" i18n-data-label>
                            <mat-checkbox [checked]="row.checked">
                            </mat-checkbox>
                          </mat-cell>
                          <mat-footer-cell *matFooterCellDef></mat-footer-cell>
                        </ng-container> -->
                        <ng-container matColumnDef="procostType">
                          <mat-header-cell *matHeaderCellDef i18n>Loại lệ phí</mat-header-cell>
                          <mat-cell *matCellDef="let row" i18n-data-label data-label="Loại lệ phí"> {{row.typeName}}
                          </mat-cell>
                          <mat-footer-cell *matFooterCellDef>Tổng</mat-footer-cell>
                        </ng-container>

                        <ng-container matColumnDef="quantity">
                          <mat-header-cell *matHeaderCellDef i18n>Số lượng</mat-header-cell>
                          <mat-cell *matCellDef="let row; let i = index" i18n-data-label data-label="Số lượng">
                            <span>{{row.quantity}}</span>
                          </mat-cell>
                          <mat-footer-cell *matFooterCellDef></mat-footer-cell>
                        </ng-container>

                        <ng-container matColumnDef="cost">
                          <mat-header-cell *matHeaderCellDef i18n>Mức lệ phí</mat-header-cell>
                          <mat-cell *matCellDef="let row; let i = index" i18n-data-label data-label="Mức lệ phí">
                            <span>{{row.cost|number}} {{row.monetaryUnit}}</span>
                            <!-- <mat-select (selectionChange)="changeCount(row.stt, $event)" *ngIf="editFeeIndex === i"
                                        fxFlex.gt-sm="150" fxFlex.gt-xs="150" value="{{row.costCaseIndex}}">
                                <mat-option *ngFor='let cost of row.costCase; let j = index;' title="{{cost.cost | number}} {{row.monetaryUnit}} ({{cost.description}})"
                                            value="{{j}}">
                                    {{cost.cost | number}} {{row.monetaryUnit}} ({{cost.description}})
                                </mat-option>
                            </mat-select> -->
                          </mat-cell>
                          <mat-footer-cell *matFooterCellDef></mat-footer-cell>
                        </ng-container>

                        <ng-container matColumnDef="amount">
                          <mat-header-cell *matHeaderCellDef i18n>Thành tiền</mat-header-cell>
                          <mat-cell *matCellDef="let row" i18n-data-label data-label="Thành tiền">
                            {{row.quantity*row.cost|number}}&nbsp;{{row.monetaryUnit}}
                          </mat-cell>
                          <mat-footer-cell *matFooterCellDef class="totalCell">{{vnpostTransferPaperFeeCost.total}}</mat-footer-cell>
                        </ng-container>

                        <ng-container matColumnDef="description">
                          <mat-header-cell *matHeaderCellDef i18n>Mô tả</mat-header-cell>
                          <mat-cell *matCellDef="let row" i18n-data-label data-label="Mô tả"> {{row.description}}
                          </mat-cell>
                          <mat-footer-cell *matFooterCellDef></mat-footer-cell>
                        </ng-container>
                        <ng-container matColumnDef="pay">
                          <mat-header-cell *matHeaderCellDef i18n>Đã thanh toán</mat-header-cell>
                          <mat-cell *matCellDef="let row; let i = index" data-label="Thanh toán">
                            <input  type="checkbox" [checked]="row.paid > 0" disabled>
                          </mat-cell>
                          <mat-footer-cell *matFooterCellDef class="totalCell">
                            {{vnpostTransferPaperFeeCost.paided}}
                            <br>
                            (Còn lại:{{vnpostTransferPaperFeeCost.debt}})
                          </mat-footer-cell>
                        </ng-container>

                        <ng-container matColumnDef="action">
                          <mat-header-cell *matHeaderCellDef i18n>Thao tác</mat-header-cell>
                          <mat-cell *matCellDef="let row; let i = index" i18n-data-label data-label="Cập nhật" class="text-center">
                            <span>...</span>
                          </mat-cell>
                          <mat-footer-cell *matFooterCellDef></mat-footer-cell>
                        </ng-container>

                        <mat-header-row *matHeaderRowDef="feeDisplayedColumns"></mat-header-row>
                        <mat-row *matRowDef="let row; columns: feeDisplayedColumns;"></mat-row>
                        <mat-footer-row *matFooterRowDef="feeDisplayedColumns" [ngClass]="{'hidden': hideTotal == true}">
                        </mat-footer-row>
                      </mat-table>
                    </div>
                  </div>
                </form>
              </div>
            </div>
          </mat-tab>
        </mat-tab-group>
      </form>
    </div>
    <div class="control" fxLayout="row" fxLayout.xs="row" fxLayout.sm="row" fxLayoutAlign="center">
      <!-- <button mat-flat-button type="button" class="btnCheck" color="primary" fxFlex.gt-md="15" fxFlex.gt-xs="27" fxFlex='grow' form="tabForm"
              (click)="searchDossierBlocking()" [ngClass]="{'btnDisabled': isLoading}"
              *ngIf="blockingDossierSectors.includes(procedureDetail[0]?.sector?.id)"
              [disabled]="isLoading || !identityCardNumber || !ownerFullname"
      >
        <mat-spinner diameter="25" *ngIf="isLoading === true && config.redirectProcess === '1'"></mat-spinner>
        <mat-icon *ngIf="!isLoading">search</mat-icon>
        <span>Kiểm tra hồ sơ</span>
      </button> -->
      <get-form-origin-item *ngIf="storage468?.getType == 'storage' && tabSelecte == 1"
                            (afterChoose)="afterChoose($event)"
                            [procedureId]="this.procedureId"
                            [fullname]="this.applicantEForm?.data?.data[fullname]"
                            [identityNumber]="this.applicantEForm?.data?.data[identityNumberKey]"
                            [taxcode]="this.applicantEForm?.data?.data[taxCodeKey]"
                            [processId]="processDefinitionId">
      </get-form-origin-item>
      <!--      IGATESUPP-115852 bước 1 tiếp nhận lịch hẹn-->
      <button class="btnSecondary" mat-flat-button fxFlex.gt-md="15" fxFlex.gt-xs="27" fxFlex='grow' type="submit" form="tabForm" (click)="receivingButton = true; onSubmit();" [disabled]="isLoading" [ngClass]="{'btnDisabled': isLoading, 'btnReceive': changColorButton}">
        <mat-spinner diameter="25" *ngIf="isLoading === true && config.redirectProcess === '1'"></mat-spinner>
        <mat-icon *ngIf="!isLoading">create_new_folder</mat-icon>
        <span i18n>Tiếp nhận</span>
      </button>
      <button class="btnSecondary" mat-flat-button fxFlex.gt-md="15" fxFlex.gt-xs="27" fxFlex='grow' type="submit" form="tabForm" (click)="receivingButton = false; callBack();" [disabled]="isLoading" [ngClass]="{'btnDisabled': isLoading, 'btnReceive': changColorButton}">
        <span>Quay lại</span>
      </button>
      <!-- <button *ngIf="vpcReceiveAndPrint == 1" class="btnSecondary" mat-flat-button fxFlex.gt-md="15" fxFlex.gt-xs="27" fxFlex='grow' type="submit" form="tabForm" (click)="receivingButton = true; receivingAndPrintButton = true; onSubmit();" [disabled]="isLoading" [ngClass]="{'btnDisabled': isLoading, 'btnReceiveAndPrint': changColorButton}">
        <mat-icon *ngIf="!isLoading">create_new_folder</mat-icon>
        <span>Tiếp nhận và in phiếu</span>
      </button>
      <button class="btnSecondary" *ngIf="changeProcessWhenReceiving && checkchangeProcessWhenReceiving && checkChangeprocess && !checkProcessOneLevelHCC" mat-flat-button fxFlex.gt-md="15" fxFlex.gt-xs="27" fxFlex='grow' type="submit" form="tabForm" (click)="changeProcessProcedure()" [disabled]="isLoading" [ngClass]="{'btnDisabled': isLoading}">
        <mat-icon>autorenew</mat-icon>
        <span i18n>Chuyển đổi quy trình</span>
      </button>
      <button *ngIf="enableOnlySaveBtn && !checkProcessOneLevelHCC" class="btnPrimary" mat-flat-button fxFlex.gt-md="15" fxFlex.gt-xs="27" fxFlex='grow' type="submit" form="tabForm" (click)="saveButton = true; isOnlySave = true; onSubmit();" [disabled]="isLoading" [ngClass]="{'btnDisabled': isLoading}">
          <mat-icon *ngIf="!isLoading">save</mat-icon>
          <span i18n>Lưu</span>
      </button>
      <button *ngIf="!checkProcessOneLevelHCC"  class="btnPrimary" mat-flat-button fxFlex.gt-xs="37" fxFlex.gt-sm="27" fxFlex.gt-md="20" fxFlex='grow' type="submit" form="tabForm" (click)="saveButton = true; onSubmit();" [disabled]="isLoading" [ngClass]="{'btnDisabled': isLoading, 'btnSave': changColorButton}">
          <mat-icon *ngIf="!isLoading">save</mat-icon>
          <span i18n>Lưu và chờ bổ sung</span>
      </button>
      <button class="btnMore" mat-flat-button [disabled]="isLoading" [matMenuTriggerFor]="moreMenu" *ngIf="!isLoading">
          <mat-icon>more_horiz</mat-icon>
      </button>
      <mat-menu #moreMenu="matMenu">
          <button mat-menu-item class="menuAction" (click)="listDossierByIdentityCard()">
              <mat-icon>list</mat-icon>
              <span>Danh sách hồ sơ theo CMND/CCCD</span>
          </button>
          <button mat-menu-item class="menuAction" (click)="viewProcess()" *ngIf="selectedProcess != undefined">
              <mat-icon>insights</mat-icon>
              <span i18n>Xem quy trình</span>
          </button>
          <button mat-menu-item class="menuAction" (click)="searchSimilarDossier()" *ngIf="dossierSimilarSearch == 1 || dossierSimilarSearch == '1'">
              <mat-icon>search</mat-icon>
              <span i18n>Tìm hồ sơ tương tự</span>
          </button>
          <button mat-menu-item class="menuAction" (click)="getSidewalkFees()" *ngIf="isIntegratedSidewalkFees">
            <mat-icon>attach_money</mat-icon>
            <span>Lấy phí thuê lòng đường hè phố</span>
          </button>
          <button mat-menu-item class="menuAction" [disabled]="isLoading" *ngIf="cloneReceptionConfig?.enable && procedureDetail[0]?.enableCloneDossier" (click)="notRedirect = true; receivingButton = true; onSubmit();">
            <mat-spinner diameter="25" *ngIf="isLoading === true && config.redirectProcess === '1'"></mat-spinner>
            <mat-icon *ngIf="!isLoading">file_copy</mat-icon>
            <span>{{cloneReceptionConfig?.buttonName}}</span>
          </button>
      </mat-menu> -->
    </div>
  </div>
</div>
