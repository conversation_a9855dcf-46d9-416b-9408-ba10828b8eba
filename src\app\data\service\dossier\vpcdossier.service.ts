import {HttpClient, HttpHeaders, HttpParams} from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { ApiProviderService } from 'src/app/core/service/api-provider.service';

@Injectable({
  providedIn: 'root'
})
export class VpcdossierService {

  private dossierURL = this.apiProviderService.getUrl('digo', 'padman') + '/VPCdossier/';
  constructor(
    private http: HttpClient,
    private apiProviderService: ApiProviderService,
  ) { }
  putDossierWithdrawWithComment(id, requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.put<any>(this.dossierURL + id + '/--withdraw', requestBody, { headers });
  }
  getListDossier(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    //return this.http.get('http://localhost:8081/dossier/' + searchString, { headers });
    return this.http.get(this.dossierURL + searchString, { headers });
  }
  putDossierStatusWithComment(id, requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.put<any>(this.dossierURL + id + '/status', requestBody, { headers });
  }
}
