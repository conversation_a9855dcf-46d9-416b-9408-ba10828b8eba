<br>
<div class="budget-code-content">
    <div fxLayout="row" fxLayoutAlign="center">
        <div fxFlex.gt-sm="88" fxFlex="95">
            <h2 class="m-0">Tra cứu Mã số có quan hệ ngân sách</h2>
        </div>
    </div>
    <br>
    <div fxLayout="row" fxLayoutAlign="center">
        <div fxFlex.gt-sm="88" fxFlex="95">
            <form [formGroup]="searchForm">
                <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="row">
                    <mat-form-field appearance="outline" fxFlex='grow'>
                        <mat-label i18n>Tra cứu:</mat-label>
                        <mat-select (selectionChange)="onChangeType($event)" [(value)]="selected">
                            <mat-option value="0"><PERSON><PERSON> s<PERSON><PERSON> hồ sơ theo ngày</mat-option>
                            <mat-option value="1">Th<PERSON>ng tin chi tiế<PERSON> hồ sơ</mat-option>
                            <mat-option value="2">Th<PERSON><PERSON> kê hồ sơ theo kỳ</mat-option>
                        </mat-select>
                    </mat-form-field>
                </div>
                <h1 *ngIf="selected == 0" class="tal-center">DANH SÁCH HỒ SƠ TIẾP NHẬN TRONG VÒNG {{sumDate}} NGÀY</h1>
                <h1 *ngIf="selected == 1" class="tal-center">THÔNG TIN CHI TIẾT HỒ SƠ</h1>
                <h1 *ngIf="selected == 2" class="tal-center">THỐNG KÊ HỒ SƠ THEO KỲ</h1>
                <div *ngIf="selected == 0" class="mat-elevation-z2">
                    <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="row" class="opt-search">
                        <div fxFlex.gt-sm="25" fxFlex.lt-md="25">
                            <mat-form-field appearance="outline">
                                <mat-label >Từ ngày</mat-label>
                                <input matInput [matDatepicker]="pickerfromDateDL" [max]="todayDate" required
                                (dateChange)="triggerToDate($event)" formControlName="fromDateDL">
                                <mat-datepicker-toggle matSuffix [for]="pickerfromDateDL"></mat-datepicker-toggle>
                                <mat-datepicker #pickerfromDateDL></mat-datepicker>
                            </mat-form-field>
                        </div>
                        <div fxFlex.gt-sm="0.7" fxFlex.lt-md="1"></div>
                        <div fxFlex.gt-sm="25" fxFlex.lt-md="25">
                            <mat-form-field appearance="outline">
                                <mat-label i18n>Đến ngày</mat-label>
                                <input matInput [matDatepicker]="pickertoDateDL" [max]="maxToDate" [min]="searchForm.controls['fromDateDL'].value" required formControlName="toDateDL">
                                <mat-datepicker-toggle matSuffix [for]="pickertoDateDL"></mat-datepicker-toggle>
                                <mat-datepicker #pickertoDateDL></mat-datepicker>
                            </mat-form-field>
                        </div>
                        <div fxFlex.gt-sm="0.7" fxFlex.lt-md="1"></div>
                        <button mat-flat-button class="btn-search" (click)="onClickSearch(0)">
                            <span i18n>Tìm kiếm</span>
                        </button>
                        <div fxFlex.gt-sm="0.7" fxFlex.lt-md="1"></div>
                    </div>
                    <div fxLayout="row" fxLayoutAlign="center">
                        <span class="sub-title">Lưu ý: <span class="tc-red fw-bold">Khoảng thời gian tìm kiếm không quá 5 ngày.</span></span>
                    </div>
                </div>
                <div *ngIf="selected == 1" class="mat-elevation-z2">
                    <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="row" class="opt-search">
                        <div fxFlex.gt-sm="25" fxFlex.lt-md="25">
                            <mat-form-field appearance="outline">
                                <mat-label>Nhập mã hồ sơ</mat-label>
                                <input matInput formControlName="id" required>
                            </mat-form-field>
                        </div>
                        <div fxFlex.gt-sm="0.7" fxFlex.lt-md="1"></div>
                        <button mat-flat-button class="btn-search" (click)="onClickSearch(1)">
                            <span i18n>Tìm kiếm</span>
                        </button>
                        <div fxFlex.gt-sm="0.7" fxFlex.lt-md="1"></div>
                    </div>
                </div>
                <div *ngIf="selected == 2" class="mat-elevation-z2">
                    <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="row" class="opt-search">
                        <div fxFlex.gt-sm="25" fxFlex.lt-md="25">
                            <mat-form-field appearance="outline">
                                <mat-label i18n>Từ ngày</mat-label>
                                <input matInput [matDatepicker]="pickerfromDateDBP" [max]="todayDate" required formControlName="fromDateDBP">
                                <mat-datepicker-toggle matSuffix [for]="pickerfromDateDBP"></mat-datepicker-toggle>
                                <mat-datepicker #pickerfromDateDBP></mat-datepicker>
                            </mat-form-field>
                        </div>
                        <div fxFlex.gt-sm="0.7" fxFlex.lt-md="1"></div>
                        <div fxFlex.gt-sm="25" fxFlex.lt-md="25">
                            <mat-form-field appearance="outline">
                                <mat-label i18n>Đến ngày</mat-label>
                                <input matInput [matDatepicker]="pickertoDateDBP" [max]="todayDate" required formControlName="toDateDBP">
                                <mat-datepicker-toggle matSuffix [for]="pickertoDateDBP"></mat-datepicker-toggle>
                                <mat-datepicker #pickertoDateDBP></mat-datepicker>
                            </mat-form-field>
                        </div>
                        <div fxFlex.gt-sm="0.7" fxFlex.lt-md="1"></div>
                        <button mat-flat-button class="btn-search" (click)="onClickSearch(2)">
                            <span i18n>Tìm kiếm</span>
                        </button>
                        <div fxFlex.gt-sm="0.7" fxFlex.lt-md="1"></div>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <br>
    <div fxLayout="row" fxLayoutAlign="center">
        <div fxFlex.gt-sm="88" fxFlex="95">
            <ng-container *ngIf="type == 0">
                <div class="tbl mat-elevation-z2">
                    <table mat-table [dataSource]="dataSourceDL">
                        <ng-container matColumnDef="hsid">
                            <mat-header-cell *matHeaderCellDef>Mã hồ sơ</mat-header-cell>
                            <mat-cell *matCellDef="let row" data-label="Mã hồ sơ">{{row.hsid}}</mat-cell>
                        </ng-container>
        
                        <ng-container matColumnDef="ten_hs">
                            <mat-header-cell *matHeaderCellDef>Tên hồ sơ</mat-header-cell>
                            <mat-cell *matCellDef="let row" data-label="Tên hồ sơ" class="ofw-anywhere">{{row.ten_hs}}</mat-cell>
                        </ng-container>
        
                        <ng-container matColumnDef="ngay_dk">
                            <mat-header-cell *matHeaderCellDef>Ngày đăng ký</mat-header-cell>
                            <mat-cell *matCellDef="let row" data-label="Ngày đăng ký">{{row.ngay_dk}}</mat-cell>
                        </ng-container>
        
                        <ng-container matColumnDef="email">
                            <mat-header-cell *matHeaderCellDef>Email</mat-header-cell>
                            <mat-cell *matCellDef="let row" data-label="Email" class="ofw-anywhere">{{row.email}}</mat-cell>
                        </ng-container>
        
                        <ng-container matColumnDef="nguoi_dk">
                            <mat-header-cell *matHeaderCellDef>Người nộp</mat-header-cell>
                            <mat-cell *matCellDef="let row" data-label="Người nộp" class="ofw-anywhere">{{row.nguoi_dk}}</mat-cell>
                        </ng-container>

                        <ng-container matColumnDef="sdt_didong">
                            <mat-header-cell *matHeaderCellDef>Số điện thoại</mat-header-cell>
                            <mat-cell *matCellDef="let row" data-label="Số điện thoại">{{row.sdt_didong}}</mat-cell>
                        </ng-container>

                        <ng-container matColumnDef="kieu_tiep_nhan">
                            <mat-header-cell *matHeaderCellDef>Kiểu tiếp nhận</mat-header-cell>
                            <mat-cell *matCellDef="let row" data-label="Kiểu tiếp nhận">
                                <span *ngIf="row.kieu_tiep_nhan === 1">Trực tuyến</span>
                                <span *ngIf="row.kieu_tiep_nhan === 2">Trực tiếp</span>
                            </mat-cell>
                        </ng-container>

                        <ng-container matColumnDef="trang_thai">
                            <mat-header-cell *matHeaderCellDef>Trạng thái</mat-header-cell>
                            <mat-cell *matCellDef="let row" data-label="Trạng thái">
                                <span *ngIf="row.trang_thai === 1">Chờ tiếp nhận</span>
                                <span *ngIf="row.trang_thai === 2">Từ chối tiếp nhận</span>
                                <span *ngIf="row.trang_thai === 3">Đã tiếp nhận</span>
                                <span *ngIf="row.trang_thai === 4">Đóng</span>
                                <span *ngIf="row.trang_thai === 5">Hủy</span>
                                <span *ngIf="row.trang_thai === 6">Đã phê duyệt</span>
                            </mat-cell>
                        </ng-container>
        
                        <mat-header-row *matHeaderRowDef="displayedColumnsDL"></mat-header-row>
                        <mat-row *matRowDef="let row; columns: displayedColumnsDL;"></mat-row>
                    </table>
                </div>
            </ng-container>
            <ng-container *ngIf="type == 1 && !!budgetDossierDetail">
                <div class="tbl mat-elevation-z2">
                    <div fxLayout="row" fxLayoutAlign="center">
                        <div fxFlex="42">
                            <ul>
                                <li>
                                    <span class="detail-title">Mã hồ sơ: </span>
                                    <span>{{budgetDossierDetail.hsid}}</span>
                                </li>
                                <li>
                                    <span class="detail-title">Tên hồ sơ: </span>
                                    <span>{{budgetDossierDetail.ten_hs}}</span>
                                </li>
                                <li>
                                    <span class="detail-title">Người đăng ký: </span>
                                    <span>{{budgetDossierDetail.nguoi_dk}}</span>
                                </li>
                                <li>
                                    <span class="detail-title">Số điện thoại: </span>
                                    <span>{{budgetDossierDetail.sdt_didong}}</span>
                                </li>
                                <li>
                                    <span class="detail-title">Email: </span>
                                    <span>{{budgetDossierDetail.email}}</span>
                                </li>
                            </ul>
                        </div>
                        <div fxFlex="28">
                            <ul>
                                <li>
                                    <span class="detail-title">Mã cơ quan tài chính: </span>
                                    <span>{{budgetDossierDetail.cqtc_ma}}</span>
                                </li>
                            </ul>
                        </div>
                        <div fxFlex="28">
                            <ul>
                                <li>
                                    <span class="detail-title">Mã số: </span>
                                    <span>{{budgetDossierDetail.ma}}</span>
                                </li>
                            </ul>
                        </div>
                    </div>
                    <mat-divider></mat-divider>
                    <div fxLayout="row" fxLayoutAlign="center">
                        <div fxFlex="42">
                            <ul>
                                <li>
                                    <span class="detail-title">Người thực hiện cấp mã: </span>
                                    <span>{{budgetDossierDetail.nguoi_pd}}</span>
                                </li>
                                <li>
                                    <span class="detail-title">Trạng thái hồ sơ: </span>
                                    <span *ngIf="budgetDossierDetail.trang_thai === 1">Chờ tiếp nhận</span>
                                    <span *ngIf="budgetDossierDetail.trang_thai === 2">Từ chối tiếp nhận</span>
                                    <span *ngIf="budgetDossierDetail.trang_thai === 3">Đã tiếp nhận</span>
                                    <span *ngIf="budgetDossierDetail.trang_thai === 4">Đóng</span>
                                    <span *ngIf="budgetDossierDetail.trang_thai === 5">Hủy</span>
                                    <span *ngIf="budgetDossierDetail.trang_thai === 6">Đã phê duyệt</span>
                                </li>
                                <li>
                                    <span class="detail-title">Kiểu tiếp nhận: </span>
                                    <span *ngIf="budgetDossierDetail.kieu_tiep_nhan === 1">Trực tuyến</span>
                                    <span *ngIf="budgetDossierDetail.kieu_tiep_nhan === 2">Trực tiếp</span>
                                </li>
                            </ul>
                        </div>
                        <div fxFlex="28"></div>
                        <div fxFlex="28"></div>
                    </div>
                    <mat-divider></mat-divider>
                    <div fxLayout="row" fxLayoutAlign="center">
                        <div fxFlex="42">
                            <ul>
                                <li>
                                    <span class="detail-title">Ngày nộp hồ sơ: </span>
                                    <span>{{budgetDossierDetail.ngay_tao}}</span>
                                </li>
                            </ul>
                        </div>
                        <div fxFlex="28">
                            <ul>
                                <li>
                                    <span class="detail-title">Ngày hẹn trả: </span>
                                    <span>{{budgetDossierDetail.ngay_tra}}</span>
                                </li>
                            </ul>
                        </div>
                        <div fxFlex="28">
                            <ul>
                                <li>
                                    <span class="detail-title">Ngày trả thực tế: </span>
                                    <span>{{budgetDossierDetail.ngay_pd}}</span>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </ng-container>
            <ng-container *ngIf="type == 2">
                <div class="tbl mat-elevation-z2">
                    <table mat-table [dataSource]="dataSourceDBP">
                        <ng-container matColumnDef="cqtc_ten">
                            <mat-header-cell *matHeaderCellDef>Tên cơ quan</mat-header-cell>
                            <mat-cell *matCellDef="let row" data-label="Tên cơ quan">{{row.cqtc_ten}}</mat-cell>
                        </ng-container>
        
                        <ng-container matColumnDef="hso_bo_sung">
                            <mat-header-cell *matHeaderCellDef>Số hồ sơ bổ sung và từ chối</mat-header-cell>
                            <mat-cell *matCellDef="let row" data-label="Số hồ sơ bổ sung và từ chối">{{row.hso_bo_sung}}</mat-cell>
                        </ng-container>
        
                        <ng-container matColumnDef="hso_cho_dung_han">
                            <mat-header-cell *matHeaderCellDef>Số hồ sơ chưa giải quyết còn hạn</mat-header-cell>
                            <mat-cell *matCellDef="let row" data-label="Số hồ sơ chưa giải quyết còn hạn">{{row.hso_cho_dung_han}}</mat-cell>
                        </ng-container>
        
                        <ng-container matColumnDef="hso_cho_qua_han">
                            <mat-header-cell *matHeaderCellDef>Số hồ sơ chưa giải quyết quá hạn</mat-header-cell>
                            <mat-cell *matCellDef="let row" data-label="Số hồ sơ chưa giải quyết quá hạn">{{row.hso_cho_qua_han}}</mat-cell>
                        </ng-container>
        
                        <ng-container matColumnDef="hso_dung_han">
                            <mat-header-cell *matHeaderCellDef>Số hồ sơ đúng hạn</mat-header-cell>
                            <mat-cell *matCellDef="let row" data-label="Số hồ sơ đúng hạn">{{row.hso_dung_han}}</mat-cell>
                        </ng-container>

                        <ng-container matColumnDef="hso_ky_truoc">
                            <mat-header-cell *matHeaderCellDef>Số hồ sơ kỳ trước chuyển sang</mat-header-cell>
                            <mat-cell *matCellDef="let row" data-label="Số hồ sơ kỳ trước chuyển sang">{{row.hso_ky_truoc}}</mat-cell>
                        </ng-container>

                        <ng-container matColumnDef="hso_qua_han">
                            <mat-header-cell *matHeaderCellDef>Số hồ sơ giải quyết quá hạn</mat-header-cell>
                            <mat-cell *matCellDef="let row" data-label="Số hồ sơ giải quyết quá hạn">{{row.hso_qua_han}}</mat-cell>
                        </ng-container>

                        <ng-container matColumnDef="hso_trong_ky">
                            <mat-header-cell *matHeaderCellDef>Số hồ sơ tiếp nhận trong kỳ</mat-header-cell>
                            <mat-cell *matCellDef="let row" data-label="Số hồ sơ tiếp nhận trong kỳ">{{row.hso_trong_ky}}</mat-cell>
                        </ng-container>

                        <ng-container matColumnDef="hso_truoc_han">
                            <mat-header-cell *matHeaderCellDef>Số hồ sơ trước hạn</mat-header-cell>
                            <mat-cell *matCellDef="let row" data-label="Số hồ sơ trước hạn">{{row.hso_truoc_han}}</mat-cell>
                        </ng-container>
        
                        <mat-header-row *matHeaderRowDef="displayedColumnsDBP"></mat-header-row>
                        <mat-row *matRowDef="let row; columns: displayedColumnsDBP;"></mat-row>
                    </table>
                </div>
            </ng-container>
        </div>
    </div>
</div>
<br>