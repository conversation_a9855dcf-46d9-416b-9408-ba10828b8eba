<button mat-icon-button class="close-button" (click)="onDismiss()">
    <mat-icon>close</mat-icon>
</button>
<h3 class="dialog_title" mat-dialog-title i18n>Thông tin chi tiết</h3>
<div class="row">
    <ng-container *ngIf="listDetails.length > 0; then tableD else noneData;"></ng-container>
    <ng-template #tableD>
        <ng-container *ngFor="let item of listDetails">
            <ul>
                <li *ngIf="item.IN_JOURNAL_NO">
                    <span class="detail-title" i18n>Mã số biên nhận củ<PERSON> hồ sơ:</span><span> {{item.IN_JOURNAL_NO}}</span>
                </li>
                <li *ngIf="item.DOCUMENT_TYPE">
                    <span class="detail-title" i18n>Loại hồ sơ đăng ký (đăng ký mới, đăng ký thay đổi, tạm ngừng, gi<PERSON><PERSON> thể,
                        hoạt động trở lại):</span><span> {{item.DOCUMENT_TYPE}}</span>
                </li>
                <li *ngIf="item.ENTERPRISE_CODE">
                    <span class="detail-title" i18n>Mã số nội bộ:</span><span> {{item.ENTERPRISE_CODE}}</span>
                </li>
                <li *ngIf="item.ENTERPRISE_GDT_CODE">
                    <span class="detail-title" i18n>Mã số doanh nghiệp:</span><span> {{item.ENTERPRISE_GDT_CODE}}</span>
                </li>
                <li *ngIf="item.NAME">
                    <span class="detail-title" i18n>Tên doanh nghiệp:</span><span> {{item.NAME}}</span>
                </li>
                <li *ngIf="item.ENTERPRISE_TYPE">
                    <span class="detail-title" i18n>Loại hình doanh nghiệp:</span><span> {{item.ENTERPRISE_TYPE}}</span>
                </li>
                <li *ngIf="item.SITE_ID">
                    <span class="detail-title" i18n>Mã cơ quan cấp đăng ký:</span><span> {{item.SITE_ID}}</span>
                </li>
                <li *ngIf="item.RECEIPT_DATE">
                    <span class="detail-title" i18n>Ngày tiếp nhận:</span><span> {{item.RECEIPT_DATE |
                        date:'dd/MM/yyyy'}}</span>
                </li>
                <li *ngIf="item.PLAN_DATE">
                    <span class="detail-title" i18n>Ngày hẹn trả kết quả:</span><span> {{item.PLAN_DATE |
                        date:'dd/MM/yyyy'}}</span>
                </li>
                <li *ngIf="item.PROCESS_STATUS">
                    <span class="detail-title" i18n>Tình trạng xử lý hồ sơ:</span><span> {{item.PROCESS_STATUS}}</span>
                </li>
                <li *ngIf="item.REGISTRATION_DATE">
                    <span class="detail-title" i18n>Ngày chấp thuận hồ sơ:</span><span> {{item.REGISTRATION_DATE}}</span>
                </li>
                <li *ngIf="item.SUPPLEMENT_DATE">
                    <span class="detail-title" i18n>Ngày tiếp nhận hồ sơ bổ sung:</span><span>
                        {{item.SUPPLEMENT_DATE}}</span>
                </li>
                <li *ngIf="item.SUBMISSION_TYPE">
                    <span class="detail-title" i18n>Kiểu tiếp nhận hồ sơ:</span><span> {{item.SUBMISSION_TYPE}}</span>
                </li>
                <li *ngIf="item.CONTACT_FULL_NAME">
                    <span class="detail-title" i18n>Họ tên người nộp hồ sơ:</span><span> {{item.CONTACT_FULL_NAME}}</span>
                </li>
                <li *ngIf="item.CONTACT_ADDRESS">
                    <span class="detail-title" i18n>Địa chỉ người nộp:</span><span> {{item.CONTACT_ADDRESS}}</span>
                </li>
                <li *ngIf="item.CONTACT_ID_NO">
                    <span class="detail-title" i18n>Số CMND/CCCD của người nộp:</span><span> {{item.CONTACT_ID_NO}}</span>
                </li>
                <li *ngIf="item.CONTACT_PHONE">
                    <span class="detail-title" i18n>Điện thoại người nộp:</span><span> {{item.CONTACT_PHONE}}</span>
                </li>
                <li *ngIf="item.CONTACT_EMAIL">
                    <span class="detail-title" i18n>Email người nộp:</span><span> {{item.CONTACT_EMAIL}}</span>
                </li>
            </ul>
        </ng-container>
    </ng-template>
    <ng-template #noneData>
        <span i18n>Không tồn tại thông tin hồ sơ doanh nghiệp</span>
    </ng-template>
</div>