import { Component, OnInit, Inject } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { MatTableDataSource } from '@angular/material/table';
import { EnvService } from 'src/app/core/service/env.service';
import { ProcedureService } from 'src/app/data/service/procedure/procedure.service';
import { EReceiptService } from 'src/app/data/service/invoice-receipt/ereceipt.service';
import { DossierService } from 'src/app/data/service/dossier/dossier.service';
import { MatDialog } from '@angular/material/dialog';
import { PrintTemplateComponent } from 'src/app/modules/dossier/pages/processing/pages/processing-detail/dialogs/print-template/print-template.component';
import { DomSanitizer } from '@angular/platform-browser';
import { SnackbarService } from 'src/app/data/service/snackbar/snackbar.service';
import { Router } from '@angular/router';
import { DeploymentService } from 'src/app/data/service/deployment.service';
import { ReceiptPaymentLGSPHCMComponent, ConfirmReceiptPaymentLGSPHCMDialogModel } from 'src/app/modules/dossier/dialogs/receipt-payment-lgsp-hcm/receipt-payment-lgsp-hcm.component';

@Component({
  selector: 'app-receipt-payment-flatform',
  templateUrl: './receipt-payment-flatform.component.html',
  styleUrls: ['./receipt-payment-flatform.component.scss']
})
export class ReceiptPaymentFlatformComponent implements OnInit {

  config = this.envService.getConfig();
  selectedLang: string;
  dossierId: string;
  dossierName: string;
  procedureId: string;
  listReceiptSupplier = [];
  isFullListReceiptSupplier = false;
  listReceiptSupplierPage = 0;
  supplierId: string;
  dossierDetail: any;
  htmlContent: string;
  dossierReceiptDetail: any;
  printData: any;

  // receiptForm
  receiptForm = new FormGroup({
    supplier: new FormControl(''),
  });
  supplier = new FormControl('', [Validators.required, Validators.pattern(this.config.regValidators)]);

  // receiptDataSource
  receiptDisplayedColumns: string[] = ['date', 'supplier', 'detail', 'action'];
  ELEMENTDATA: any[] = [];
  receiptDataSource: MatTableDataSource<any>;
  totalCost = '';
  hideTotal = false;

  countResult = 0;
  size = 10;
  page = 1;
  pageIndex = 1;
  pgSizeOptions = this.config.pageSizeOptions;
  getBillHCM = this.deploymentService.env?.OS_HCM?.getBillHCM;
  //IGATESUPP-33598-thuongld-[iGate HCM] Đổi tên nút hiển thị xem danh sách biên lai
  paymentPlatformButtonName = this.deploymentService.env?.OS_HCM?.paymentPlatformButtonName ? this.deploymentService.env?.OS_HCM?.paymentPlatformButtonName : null;
  paymentPlatformButtonNameLable: string;

  checkRecallBillFile = this.deploymentService.env?.OS_HCM?.checkRecallBillFile ? this.deploymentService.env?.OS_HCM?.checkRecallBillFile : false;

  constructor(
    private snackbarService: SnackbarService,
    private dialog: MatDialog,
    private dom: DomSanitizer,
    private router: Router,
    public dialogRef: MatDialogRef<ReceiptPaymentFlatformComponent>,
    @Inject(MAT_DIALOG_DATA) public data: ConfirmReceiptPaymentFlatformDialogModel,
    private envService: EnvService,
    private procedureService: ProcedureService,
    private eReceiptService: EReceiptService,
    private dossierService: DossierService,
    private deploymentService: DeploymentService,
  ) {
    this.dossierId = data.id;
    // this.procedureId = data.procedureId;
    this.receiptDataSource = new MatTableDataSource(this.ELEMENTDATA);
  }

  ngOnInit(): void {
    this.selectedLang = localStorage.getItem('language');
    // this.getDossierDetail();
    // this.getListReceiptSupplier();
    // this.getListDossierReceipt();
    this.autoSearch();
    //Lấy tên nút VNPT Payment Platform
    if(this.paymentPlatformButtonName)
    {
      this.paymentPlatformButtonNameLable = this.paymentPlatformButtonName[this.selectedLang].toLowerCase();
    }
  }


  onConfirm() {
    this.dialogRef.close(true);
  }

  onDismiss() {
    this.dialogRef.close();
  }

  autoSearch() {
    const searchString = this.generateSearchString('page', (this.page - 1), this.size, 'id,desc');
    this.getListDossierReceipt(searchString);
  }

  generateSearchString(spec, page, size, sort) {
    if (this.supplierId === undefined) {
      this.supplierId = '';
    }
    return '?spec=' + spec +
      '&page=' + page +
      '&size=' + size +
      '&sort=' + sort +
      '&dossier-id=' + this.dossierId +
      '&status=1&payment-method-code=VNPT_PAYMENT_PLATFORM';
  }

  paginate(event: any, type) {
    switch (type) {
      case 0:
        this.pageIndex = event;
        this.page = event;
        const searchString = this.generateSearchString('page', (this.pageIndex - 1), this.size, 'id,desc');
        this.getListDossierReceipt(searchString);
        break;
      case 1:
        this.pageIndex = 1;
        this.page = 1;
        const searchString2 = this.generateSearchString('page', (this.pageIndex - 1), this.size, 'id,desc');
        this.getListDossierReceipt(searchString2);
        break;
    }
  }


  getListDossierReceipt(searchString) {
    this.dossierService.getDossierPayment(searchString).subscribe(data => {
      if (data.content.length === 0) {
        const msgObj = {
          vi: 'Danh sách biên lai rỗng!',
          en: 'The list of issued receipts is empty!'
        };
        this.dialogRef.close();
        this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
      } else {
        this.ELEMENTDATA = [];
        this.countResult = data.totalElements;
        for (let i = 0; i < data.numberOfElements; i++) {
          data.content[i].totalString = Number( data.content[i].total.toFixed(1)).toLocaleString() + ' VNĐ';
          this.ELEMENTDATA.push(data.content[i]);
        }
        this.receiptDataSource.data = this.ELEMENTDATA;
      }
    });
  }

  getDossierDetail() {
    this.dossierService.getDossierDetail(this.dossierId).subscribe(data => {
      this.dossierDetail = data;
      this.autoSearch();
    }, err => {
      console.log(err);
    });
  }

  viewFile(id, row, uuid?) {
    this.dossierService.downloadFile(id,this.dossierId, "").subscribe(data => {
      if(this.checkRecallBillFile)
      {
        //Kiểm tra file nếu tồn tại lỗi thì recall lại để lấy bill
        let _this = this;
        var reader = new FileReader();
        reader.onload = function() {
          let checkFile;
          try {
            //Kiểm tra file lỗi không kết nối DVCQG không lấy được file
            if(reader.result.toString().indexOf('connection failure') >= 0){
              _this.recallFile(row);
              return;
            }
            else {
              checkFile = JSON.parse(reader.result.toString());
            }
          } catch{}
          if(checkFile && checkFile.err_code == "1")
          {
            _this.recallFile(row);
          }
          else
          {
            const dataType = data.type;
            const binaryData = [];
            binaryData.push(data);
            const downloadLink = document.createElement('a');
            downloadLink.href = window.URL.createObjectURL(new Blob(binaryData, { type: dataType }));
            document.body.appendChild(downloadLink);
            const blobURL: any = _this.dom.bypassSecurityTrustUrl(downloadLink.href);
            window.open(blobURL.changingThisBreaksApplicationSecurity, '_blank');
          }
        }
        reader.readAsText(data);
      }
      else
      {
        const dataType = data.type;
        const binaryData = [];
        binaryData.push(data);
        const downloadLink = document.createElement('a');
        downloadLink.href = window.URL.createObjectURL(new Blob(binaryData, { type: dataType }));
        document.body.appendChild(downloadLink);
        const blobURL: any = this.dom.bypassSecurityTrustUrl(downloadLink.href);
        window.open(blobURL.changingThisBreaksApplicationSecurity, '_blank');
      }
    },
    err => {
      // console.log(err);
      this.recallFile(row);
    }
    );

  }

  async printReceipt(row) {
    // this.dossierService.getDossierPaymentDetail(row.id).subscribe(data => {
      if (!!row.billFileId){
        this.viewFile(row.billFileId, row, null);
      }
      else {
        if(!!row.urlBienLai && row.urlBienLai != ''){
          let getBillByURL = {
            agencyId: row.dossier.agency.parent.id,
            subsystemId: this.config.systemId,
            urlBienLai: row.urlBienLai
          };
          this.dossierService.postVNPTPaymentBillByUrl(getBillByURL).subscribe(data => {
            if (!!data && !!data.id && data.id != '') {
              this.viewFile(data.id, row, null);
              this.dossierService.putBillPayment(row.id, { billFileId: data.id }).subscribe(data2 => {
                this.paginate(0, 1);
              });
            } else {
              alert('Lấy biên lai từ url thất bại 1');
            }
          },
          err => {
            alert('Lấy biên lai từ url thất bại');
          });
          return;
        }
        let getBill = {
          agencyId: row.dossier.agency.parent.id,
          subsystemId: this.config.systemId,
          MaDoiTac: row.paymentData.MaDoiTac,
          MaThamChieu: row.id,
          ThoiGianGD: row.paymentData.ThoiGianGD
        };
        if(this.getBillHCM){
          getBill.MaDoiTac= row.paymentData?.MaDoiTac ? row.paymentData?.MaDoiTac : "000.00.00.H29",
          getBill.ThoiGianGD= row.paymentData?.ThoiGianGD ? row.paymentData?.ThoiGianGD : row.paymentData?.payDate
        }
        
        // tslint:disable-next-line: no-shadowed-variable
        this.dossierService.postVNPTPaymentBillInit(getBill).subscribe(data => {
          if (data.maLoi === '00') {
            if (!!data.idFile){
              this.viewFile(data.idFile, row, null);
              this.dossierService.putBillPayment(row.id, {billFileId: data.idFile}).subscribe(data2 => {
                this.paginate(0, 1);
              });
            }
            else{
              window.open(data.viewUrl, '_blank');
            }
          } else {
            alert('Lấy biên lai thất bại 1');
          }
        },
          err => {
            alert('Lấy biên lai thất bại');
          });

      }
    // });

  }
  async rePrintReceipt(row) {
    if(!!row.urlBienLai && row.urlBienLai != ''){
      let getBillByURL = {
        agencyId: row.dossier.agency.parent.id,
        subsystemId: this.config.systemId,
        urlBienLai: row.urlBienLai
      };
      this.dossierService.postVNPTPaymentBillByUrl(getBillByURL).subscribe(data => {
        if (!!data && !!data.id && data.id != '') {
          this.viewFile(data.id, row, null);
          this.dossierService.putBillPayment(row.id, { billFileId: data.id }).subscribe(data2 => {
            this.paginate(0, 1);
          });
        } else {
          alert('Lấy biên lai từ url thất bại 1');
        }
      },
      err => {
        alert('Lấy biên lai từ url thất bại');
      });
      return;
    }
    let getBill = {
      agencyId: row.dossier.agency.parent.id,
      subsystemId: this.config.systemId,
      MaDoiTac: row.paymentData.MaDoiTac,
      MaThamChieu: row.id,
      ThoiGianGD: row.paymentData.ThoiGianGD
    };
    if(this.getBillHCM){
      getBill.MaDoiTac= row.paymentData?.MaDoiTac ? row.paymentData?.MaDoiTac : "000.00.00.H29",
      getBill.ThoiGianGD= row.paymentData?.ThoiGianGD ? row.paymentData?.ThoiGianGD : row.paymentData?.payDate
    }
    // tslint:disable-next-line: no-shadowed-variable
    this.dossierService.postVNPTPaymentBillInit(getBill).subscribe(data => {
      if (data.maLoi === '00') {
        if (!!data.idFile){
          this.viewFile(data.idFile, row, null);
          this.dossierService.putBillPayment(row.id, {billFileId: data.idFile}).subscribe(data2 => {
            this.paginate(0, 1);
          });
        }
        else{
          window.open(data.viewUrl, '_blank');
        }
      } else {
        alert('Lấy biên lai thất bại 1');
      }
    },
      err => {
        alert('Lấy biên lai thất bại');
    });

  }

  recallFile(row){
    const getBill = {
      agencyId: row.dossier.agency.parent.id,
      subsystemId: this.config.systemId,
      MaDoiTac: row.paymentData.MaDoiTac,
      MaThamChieu: row.id,
      ThoiGianGD: row.paymentData.ThoiGianGD
    };
    // tslint:disable-next-line: no-shadowed-variable
    this.dossierService.postVNPTPaymentBillInit(getBill).subscribe(data => {
      if (data.maLoi === '00') {
        if (!!data.idFile){
          this.dossierService.downloadFile(data.idFile, this.dossierId, "").subscribe(data3 => {
            const dataType = data3.type;
            const binaryData = [];
            binaryData.push(data3);
            const downloadLink = document.createElement('a');
            downloadLink.href = window.URL.createObjectURL(new Blob(binaryData, { type: dataType }));
            document.body.appendChild(downloadLink);
            const blobURL: any = this.dom.bypassSecurityTrustUrl(downloadLink.href);
            window.open(blobURL.changingThisBreaksApplicationSecurity, '_blank');
          },
            err => {

            }
          );
          this.dossierService.putBillPayment(row.id, {billFileId: data.idFile}).subscribe(data2 => {
            this.paginate(0, 1);
          });
        }
        else{
          window.open(data.viewUrl, '_blank');
        }
      } else {
        alert('Lấy biên lai thất bại 1');
      }
    },
      err => {
        alert('Lấy biên lai thất bại');
      });
  }

  getOrderInfoLGSPHCM(row){
    if(row.paymentData && row.paymentData?.payTransId && row.paymentData?.orderId)
    {
      const dialogData = new ConfirmReceiptPaymentLGSPHCMDialogModel(row);
      const dialogRef = this.dialog.open(ReceiptPaymentLGSPHCMComponent, {
        minWidth: '80vw',
        maxHeight: '80vh',
        data: dialogData,
        disableClose: true,
        autoFocus: false
      });
      dialogRef.afterClosed().subscribe(dialogResult => {
      });
    }
    else {
      const msgObj = {
        vi: 'Không tìm thấy thông tin thanh toán E-Payment HCM LGSP!',
        en: 'E-Payment HCM LGSP payment information could not be found!'
      };
      this.dialogRef.close();
      this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
    }
  }
}

export class ConfirmReceiptPaymentFlatformDialogModel {
  constructor(public id: string) {
  }
}
