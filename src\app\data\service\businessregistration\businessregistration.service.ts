import { Observable } from 'rxjs';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { ApiProviderService } from 'src/app/core/service/api-provider.service';
import { MainService } from 'src/app/data/service/main/main.service';
import { MatDialog } from '@angular/material/dialog';
import { KeycloakService } from 'keycloak-angular';

import { DeploymentService } from 'src/app/data/service/deployment.service';
@Injectable({
  providedIn: 'root'
})
export class BusinessregistrationService {

  constructor(
    private http: HttpClient,
    private main: MainService,
    private dialog: MatDialog,
    private apiProvider: ApiProviderService,
    private keycloakService: KeycloakService,
    private deploymentService: DeploymentService
  ) { }
  result: boolean;
  private padmanUrl = this.apiProvider.getUrl('digo', 'padman');
  private basepadUrl = this.apiProvider.getUrl('digo', 'basepad');
  private adapterUrl = this.apiProvider.getUrl('digo', 'adapter');
  // private formComponent: PublicComponent;
  // registerMyApp(myForm: PublicComponent) {
  //   this.formComponent = myForm;
  // }
  env = this.deploymentService.getAppDeployment()?.env;

  formErrorMessage(id: number) {
    switch (id) {
      case 1:
        return 'Vui lòng nhập mã chuẩn';
      case 2:
        return 'Vui lòng nhập tên giấy tờ';
      default:
        return 'You must enter a valid value';
    }
  }

  search(searchString: string, a: number): Observable<any> {
    if (a === 0){
    const token = localStorage.getItem('userToken');
    let headers = new HttpHeaders();
    headers = headers.append('Authorization', 'Bearer ' + token);
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.get(this.apiProvider.getUrl('digo', 'padman') + '/dossier/--public?' + searchString, {headers}).pipe();
    }
    else { return this.http.get(this.apiProvider.getUrl('digo', 'padman') + '/dossier/--public?' + searchString).pipe(); }
  }

  updateForm(requestBody, id) {
    let headers = new HttpHeaders();
    headers = headers.append('Content-Type', 'application/json');
    return this.http.put<any>(this.apiProvider.getUrl('digo', 'basepad') + '/form/' + id, requestBody, { headers }).pipe();
  }
  getDossierDetail(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.padmanUrl + '/dossier/' + id + '/--online', { headers }).pipe();
  }
  getProcostForOnline(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.padmanUrl + '/dossier-fee/?dossier-id=' + id, { headers }).pipe();
  }
  getDetailProcost(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.basepadUrl + '/procost/' + id, { headers }).pipe();
  }

  postDetails(search, data): Observable<any> {
    const token = localStorage.getItem('userToken');
    let headers = new HttpHeaders();
    headers = headers.append('Authorization', 'Bearer ' + token);
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');
    if(this.env?.businessRegistration && this.env?.businessRegistration?.typeGet == '1')
    {
      search += '&fromDate=' + data.from_date + '&toDate=' + data.to_date + '&offset=' + data.offset + '&limit=' + data.limit;
      // return this.http.get<any>(this.adapterUrl + '/dkdn/--get-detail-in-time' + search, { headers });
      return this.http.get<any>(this.adapterUrl + '/dkdn/--get-detail-in-time-get' + search, { headers });
    }else{
      return this.http.post<any>(this.adapterUrl + '/dkdn/--get-detail-in-time' + search, data, { headers });
    }
  }

  postDetailsFileRegister(search, data): Observable<any> {
    const token = localStorage.getItem('userToken');
    let headers = new HttpHeaders();
    headers = headers.append('Authorization', 'Bearer ' + token);
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');
    if(this.env?.businessRegistration && this.env?.businessRegistration?.typeGet == '1')
    {
      search += '&maHoSo=' + data.in_journal_no;
      // return this.http.get<any>(this.adapterUrl + '/dkdn/--get-detail-enterprise' + search, data, { headers });
      return this.http.get<any>(this.adapterUrl + '/dkdn/--get-detail-file-register-get' + search, { headers });
    }else{
      return this.http.post<any>(this.adapterUrl + '/dkdn/--get-detail-file-register' + search, data, { headers });
    } 
    
  }

  postDetailsEnterprise(search, data): Observable<any> {
    const token = localStorage.getItem('userToken');
    let headers = new HttpHeaders();
    headers = headers.append('Authorization', 'Bearer ' + token);
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');
    if(this.env?.businessRegistration && this.env?.businessRegistration?.typeGet == '1')
    {
      search += '&msdn=' + data.msdn;
      // return this.http.get<any>(this.adapterUrl + '/dkdn/--get-detail-enterprise' + search, data, { headers });
      return this.http.get<any>(this.adapterUrl + '/dkdn/--get-detail-enterprise-get' + search, { headers });
    }else{
      return this.http.post<any>(this.adapterUrl + '/dkdn/--get-detail-enterprise' + search, data, { headers });
    }    
  }

  postHandlingFileInDay(search, data): Observable<any> {
    const token = localStorage.getItem('userToken');
    let headers = new HttpHeaders();
    headers = headers.append('Authorization', 'Bearer ' + token);
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');
    if(this.env?.businessRegistration && this.env?.businessRegistration?.typeGet == '1')
    {
      search += '&fromTS=' + data.fromTS + '&toTS=' + data.toTS + '&offset=' + data.offset + '&limit=' + data.limit;
      // return this.http.get<any>(this.adapterUrl + '/dkdn/--get-detail-enterprise' + search, data, { headers });
      return this.http.get<any>(this.adapterUrl + '/dkdn/--get-handling-file-in-day-get' + search, { headers });
    }else{
      return this.http.post<any>(this.adapterUrl + '/dkdn/--get-handling-file-in-day' + search, data, { headers });
    } 
  }

  getConfigBusinessRegistration(search): Observable<any>
  {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.basepadUrl + '/procedure-config/bussiness/--list' + search, { headers }).pipe();
  }
}
