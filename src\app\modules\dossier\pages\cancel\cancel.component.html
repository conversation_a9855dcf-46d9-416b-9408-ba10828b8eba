<div fxLayout="{{interfaceWorkHorizontal == 1? 'column': 'row'}}" fxLayoutAlign="space-between" fxLayout.sm="column" fxLayout.xs="column">
    <!-- menu nhắc việc -->
    <div fxFlex="{{interfaceWorkHorizontal == 1? 100: 25}}" fxFlex.sm="100" fxFlex.xs="100">
        <div (click)="onClickOpenReminderMenu()" mat-button class="title-reminder" fxLayout="row" fxLayoutAlign="space-between">
            <div class="content">
                <span fxFlex="100" class="title"><span i18n>Danh sách công việc</span> (<a class="count-task">{{lengthRemind}}</a>)</span>
                <mat-icon fxFlex="10">expand_more</mat-icon>
            </div>
        </div>
        <div class="menu_reminder">
            <mat-accordion class="advanced-box" multi>
                <mat-expansion-panel class="panel" *ngIf="expandReminderMenu" [(expanded)]="expandReminderMenu" [ngStyle]="{'height': interfaceWorkHorizontal? 'unset': xpandStatus ? '19rem' : '6rem' }">
                    <span *ngFor="let remind of listMenuRemind;">
                        <a id="submenu" mat-button active-link="active" [ngClass]="{ 'active': remindId === remind.id , 'interfaceWorkHorizontalClass': interfaceWorkHorizontal == 1}" (click)="changeSearchRemind(remind.id)">
                            <mat-icon>receipt</mat-icon><span class="submenuTitle">{{remind.name}}</span>&nbsp;<span class="count">{{remind.count}}</span>
                    </a>
                    </span>
                </mat-expansion-panel>
            </mat-accordion>
        </div>

    </div>
    <!-- menu tìm kiếm -->
    <div fxFlex="{{interfaceWorkHorizontal == 1? 100: 75}}" fxFlex.sm="100" fxFlex.xs="100" class="search">
        <ng-container *ngIf="qbhrenamemenu == true ;then qbh else normal">
            
        </ng-container>
        <ng-template #normal>
            <h2 i18n>Hồ sơ không cần xử lý</h2>
        </ng-template>
        <ng-template #qbh>
            <h2 >Hồ sơ không giải quyết</h2>
        </ng-template>
        <div class="prc_searchbar">
            <form [formGroup]="searchForm" (submit)="onConfirmSearch()" class="searchForm">
                <div fxLayout="row" fxLayout.xs="row" fxLayout.sm="row" class="formFieldOutline" fxLayoutAlign="space-between">
                    <mat-form-field appearance="outline" fxFlex.gt-sm="20" fxFlex.gt-xs="49.5" fxFlex='grow'>
                        <mat-label i18n>Mã số hồ sơ</mat-label>
                        <input type="text" matInput formControlName="code" maxlength="500">
                    </mat-form-field>
                    <mat-form-field appearance="outline" fxFlex.gt-sm="20" fxFlex.gt-xs="49.5" fxFlex='grow'>
                        <mat-label >Số CMND/CCCD</mat-label>
                        <input type="text" matInput formControlName="identityNumber" maxlength="500">
                    </mat-form-field>
                    <mat-form-field *ngIf="!isOwnerFullname || (isOwnerFullname && showCreateSearchOwnerFullname)" appearance="outline" fxFlex.gt-sm="18" fxFlex.gt-xs="49.5" fxFlex='grow'>
                      <mat-label i18n>Tên người nộp</mat-label>
                      <input type="text" matInput formControlName="applicantName" maxlength="500">
                    </mat-form-field>
                    <mat-form-field *ngIf="isOwnerFullname" appearance="outline" fxFlex.gt-sm="20" fxFlex.gt-xs="49.5" fxFlex='grow'>
                      <mat-label i18n>Chủ hồ sơ</mat-label>
                      <input type="text" matInput formControlName="ownerFullname" maxlength="500">
                    </mat-form-field>
                    <mat-form-field *ngIf="getCheckAgencyShowFilterOrganization()" appearance="outline" fxFlex.gt-sm="20" fxFlex.gt-xs="49.5" fxFlex='grow'>
                        <mat-label i18n>Tên cơ quan doanh nghiệp</mat-label>
                        <input type="text" matInput formControlName="applicantOrganization" maxlength="500">
                    </mat-form-field>
                    <mat-form-field appearance="outline" fxFlex.gt-sm="20" fxFlex.gt-xs="49.5" fxFlex='grow'>
                        <mat-label i18n>Hình thức tiếp nhận</mat-label>
                        <mat-select formControlName="applyMethod">
                            <mat-option value="" i18n>Tất cả</mat-option>
                            <mat-option *ngFor="let r of arrReceptionForm;" value="{{r.id}}">{{r.name}}</mat-option>
                        </mat-select>
                    </mat-form-field>
                    <mat-form-field appearance="outline" fxFlex.gt-sm="15.5" fxFlex.gt-xs="49.5" fxFlex='grow' style="display: none;">
                        <mat-label>Trạng thái hồ sơ</mat-label>
                        <mat-select formControlName="dossierStatus">
                            <mat-option value="6" i18n>Tất cả</mat-option>
                            <mat-option value="0">Chờ tiếp nhận</mat-option>
                            <mat-option value="1">Chờ bổ sung</mat-option>
                        </mat-select>
                    </mat-form-field>
                    <button mat-flat-button fxFlex.gt-sm="16" fxFlex.gt-xs="49.5" fxFlex='grow' class="searchBtn" type="submit">
                <mat-icon>search</mat-icon><span i18n>Tìm kiếm</span>
            </button>
                </div>

                <div class="advanced-button" (click)='onClickOpenAdvancedSearchBox()'>
                    <div *ngIf="xpandStatus == false">
                        <span i18n>Tìm kiếm nâng cao</span>
                        <mat-icon>expand_more</mat-icon>
                    </div>
                    <div *ngIf="xpandStatus == true">
                        <span i18n>Thu gọn</span>
                        <mat-icon>expand_less</mat-icon>
                    </div>
                </div>

                <mat-accordion class="advanced-box" multi>
                    <mat-expansion-panel class="panel" [(expanded)]="xpandStatus">
                        <div fxLayout="row" fxLayout.xs="row" fxLayout.sm="row" fxLayoutAlign="center center" class="formFieldOutline" fxLayoutAlign="space-between">

                            <mat-form-field appearance="outline" fxFlex.gt-sm="49.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                                <mat-label i18n>Lĩnh vực</mat-label>
                                <mat-select #sectorMatSelectInfiniteScroll msInfiniteScroll (infiniteScroll)="getListSector()" formControlName="sectorCtrl" (selectionChange)="sectorChange($event)" [complete]="isFullListSector == true">
                                    <div fxLayout="row" fxLayout.xs="row" fxLayout.sm="row" style="height: 50px;">
                                        <input matInput #searchSector (keyup)="onEnter('sector', $event)" (keydown)="$event.stopPropagation()"
                                            placeholder="Nhập từ khóa" class="search-nested" />
                                        <button mat-icon-button *ngIf="searchSector.value !== ''"
                                            (click)="searchSector.value = ''; resetSearchForm('sector')" class="clear-search-nested">
                                            <mat-icon> close </mat-icon>
                                        </button>
                                    </div>
                                    <mat-option value="" i18n>Tất cả</mat-option>
                                    <mat-option *ngFor="let sector of sectorFiltered | async" value="{{sector.id}}">
                                        {{sector.name}}
                                        <span *ngIf="sector.name == undefined || sector.name == null || sector.name.trim() == ''" i18n>(Không tìm thấy bản dịch)</span>
                                    </mat-option>
                                </mat-select>
                            </mat-form-field>
                            

                            <mat-form-field appearance="outline" fxFlex.gt-sm="49.5" fxFlex.gt-xs="grow" fxFlex='grow'>
                                <mat-label i18n>Thủ tục</mat-label>
                                <mat-select #procedureMatSelectInfiniteScroll msInfiniteScroll (infiniteScroll)="getListProcedure()" formControlName="procedureCtrl" [complete]="isFullListProcedure == true">
                                    <mat-option>
                                        <ngx-mat-select-search [formControl]="searchProcedureCtrl" placeholderLabel="" [disableScrollToActiveOnOptionsChanged]="true" i18n-noEntriesFoundLabel noEntriesFoundLabel="Không tìm thấy kết quả nào!">
                                        </ngx-mat-select-search>
                                    </mat-option>
                                    <mat-option value="" i18n>Tất cả</mat-option>
                                    <mat-option matTooltip="{{procedure.name}}"  matTooltipDisabled="{{enableTooltip}}" *ngFor="let procedure of procedureFiltered | async" value="{{procedure.id}}">
                                        {{procedure.name}}
                                        <span *ngIf="procedure.name == undefined || procedure.name == null || procedure.name.trim() == ''" i18n>(Không tìm thấy bản dịch)</span>
                                    </mat-option>
                                </mat-select>
                            </mat-form-field>

                            <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                                <mat-label i18n>Quốc gia</mat-label>
                                <mat-select formControlName="nation" (selectionChange)="nationChange($event)">
                                    <mat-option value="" i18n>Tất cả</mat-option>
                                    <mat-option *ngFor='let nationOpt of listNation;' value="{{nationOpt.id}}">
                                        {{nationOpt.name}}
                                        <span *ngIf="nationOpt.name == undefined || nationOpt.name == null || nationOpt.name.trim() == ''" i18n>(Không tìm thấy bản dịch)</span>
                                    </mat-option>
                                </mat-select>
                            </mat-form-field>
                            <mat-form-field appearance="outline" fxFlex.gt-md="33" fxFlex.gt-sm="32" fxFlex.gt-xs="49.5" fxFlex='grow'>
                                <mat-label i18n>Tỉnh/TP người nộp</mat-label>
                                <mat-select formControlName="province" (selectionChange)="provinceChange($event)">
                                    <mat-option value="" i18n>Tất cả</mat-option>
                                    <mat-option *ngFor='let provinceOpt of listProvince;' value="{{provinceOpt.id}}">
                                        {{provinceOpt.name}}
                                        <span *ngIf="provinceOpt.name == undefined || provinceOpt.name == null || provinceOpt.name.trim() == ''" i18n>(Không tìm thấy bản dịch)</span>
                                    </mat-option>
                                </mat-select>
                            </mat-form-field>

                            <mat-form-field appearance="outline" fxFlex.gt-md="32.5" fxFlex.gt-sm="32" fxFlex.gt-xs="49.5" fxFlex='grow'>
                                <mat-label i18n *ngIf="twoLevelPublicAdministration">Quận/huyện người nộp</mat-label>
                                <mat-label i18n *ngIf="!twoLevelPublicAdministration">Phường/xã người nộp</mat-label>
                                <mat-select formControlName="district" (ange)="districtChange($event)">
                                    <mat-option value="" i18n>Tất cả</mat-option>
                                    <mat-option *ngFor='let districtOpt of listDistrict;' value="{{districtOpt.id}}">
                                        {{districtOpt.name}}
                                        <span *ngIf="districtOpt.name == undefined || districtOpt.name == null || districtOpt.name.trim() == ''" i18n>(Không tìm thấy bản dịch)</span>
                                    </mat-option>
                                </mat-select>
                            </mat-form-field>
                            <mat-form-field  *ngIf="twoLevelPublicAdministration" appearance="outline" fxFlex.gt-md="32.5" fxFlex.gt-sm="32" fxFlex.gt-xs="49.5" fxFlex='grow'>
                                <mat-label i18n>Phường/xã người nộp</mat-label>
                                <mat-select formControlName="village">
                                    <mat-option value="" i18n>Tất cả</mat-option>
                                    <mat-option *ngFor='let wardtOpt of listVillage;' value="{{wardtOpt.id}}">
                                        {{wardtOpt.name}}
                                        <span *ngIf="wardtOpt.name == undefined || wardtOpt.name == null || wardtOpt.name.trim() == ''" i18n>(Không tìm thấy bản dịch)</span>
                                    </mat-option>
                                </mat-select>
                            </mat-form-field>
                            <mat-form-field appearance="outline" fxFlex.gt-md="33" fxFlex.gt-sm="32" fxFlex.gt-xs="49.5" fxFlex='grow'>
                                <mat-label i18n>Tiếp nhận từ ngày</mat-label>
                                <input matInput [matDatepicker]="pickerAcceptFrom" formControlName="acceptFrom" maxlength="20">
                                <mat-datepicker-toggle matSuffix [for]="pickerAcceptFrom"></mat-datepicker-toggle>
                                <mat-datepicker #pickerAcceptFrom></mat-datepicker>
                            </mat-form-field>
                            <mat-form-field appearance="outline" fxFlex.gt-md="32.5" fxFlex.gt-sm="32" fxFlex.gt-xs="49.5" fxFlex='grow'>
                                <mat-label i18n>Tiếp nhận đến ngày</mat-label>
                                <input matInput [matDatepicker]="pickerAcceptTo" formControlName="acceptTo" maxlength="20">
                                <mat-datepicker-toggle matSuffix [for]="pickerAcceptTo"></mat-datepicker-toggle>
                                <mat-datepicker #pickerAcceptTo></mat-datepicker>
                            </mat-form-field>
                            <mat-form-field appearance="outline" fxFlex.gt-sm="48.75" fxFlex.gt-xs="49.5" fxFlex='grow' *ngIf="qni_advancedsearch">
                                <mat-label i18n>Nội dung yêu cầu giải quyết</mat-label> 
                                <input type="text" matInput formControlName="advnoidungyeucaugiaiquyet">
                            </mat-form-field>
                            <mat-form-field *ngIf="qni_advancedsearch" appearance="outline" fxFlex.gt-sm="48.75" fxFlex.gt-xs="49.5" fxFlex='grow'>
                                <mat-label i18n>Tên người nộp</mat-label>
                                <input type="text" matInput formControlName="applicantName" maxlength="500">
                              </mat-form-field>
                              <mat-form-field appearance="outline" fxFlex.gt-md="33" fxFlex.gt-sm="32" fxFlex.gt-xs="49.5" fxFlex='grow' *ngIf="isRefuse">
                                <mat-label i18n>Từ chối từ ngày</mat-label>
                                <input matInput [matDatepicker]="pickerCancelFrom" formControlName="cancelFrom" maxlength="20">
                                <mat-datepicker-toggle matSuffix [for]="pickerCancelFrom"></mat-datepicker-toggle>
                                <mat-datepicker #pickerCancelFrom></mat-datepicker>
                            </mat-form-field>
                            <mat-form-field appearance="outline" fxFlex.gt-md="32.5" fxFlex.gt-sm="32" fxFlex.gt-xs="49.5" fxFlex='grow' *ngIf="isRefuse">
                                <mat-label i18n>Từ chối đến ngày</mat-label>
                                <input matInput [matDatepicker]="pickerCancelTo" formControlName="cancelTo" maxlength="20">
                                <mat-datepicker-toggle matSuffix [for]="pickerCancelTo"></mat-datepicker-toggle>
                                <mat-datepicker #pickerCancelTo></mat-datepicker>
                            </mat-form-field>
                        </div>
                    </mat-expansion-panel>
                </mat-accordion>
            </form>
        </div>
    </div>
</div>
<div fxLayout="row" fxLayoutAlign="center">
    <div fxFlex="grow">
        <div class="top-controlcancel" fxLayout="row" fxLayoutAlign="start">
            <button mat-flat-button [disabled]="selectedDossiersHCM.length == 0" *ngIf="exportCancelDossiersOnPage" class="primary-btn" (click)="exportSelectedCancelDossiers()">
                <mat-icon style = "margin-right: 10px;">cloud_download</mat-icon>
                <span>Xuất excel hồ sơ trên trang</span>
            </button>
            <button style = "margin-left: 10px;" mat-flat-button *ngIf="ExportPageDossiersToExcelQNI"  class="primary-btn" (click)="getListDossierAllExcel_QNI()" >
                <mat-icon style = "margin-right: 10px;">cloud_download</mat-icon>
                <span>Xuất excel</span>
            </button>
            <button style = "margin-left: 10px;" mat-flat-button *ngIf="searchCancel"  class="primary-btn" (click)="exportExcelDossierQNM()" >
                <mat-icon style = "margin-right: 10px;">cloud_download</mat-icon>
                <span>Xuất excel</span>
            </button>
        </div>
    </div>
</div>

<div fxLayout="row" fxLayoutAlign="center">
    <div [ngClass]="{'config-kha-enabled': configWorkInterfaceKHA}" class="frm_main" fxFlex="grow">
        <div class="top-control" fxLayout="row" fxLayoutAlign="end">
            <button mat-flat-button class="btn_ctrl" [matMenuTriggerFor]="printMenu" style="display: none;">
                <mat-icon>print</mat-icon>
                <span i18n>In phiếu</span>
                <mat-icon>keyboard_arrow_down</mat-icon>
            </button>
        </div>
        <mat-menu #printMenu="matMenu">
            <button mat-menu-item i18n>Phiếu hướng dẫn</button>
        </mat-menu>
        
        <div *ngIf="qbhlistlayout == false then Normal; else QBHLayout"></div>   
        <ng-template #Normal>
            <div *ngIf="checkNullData === 1 then nullData; else hasData"></div>
            <ng-template #nullData>
                <br />
                <div fxLayout="row" fxLayoutAlign="center">
                    <div fxFlex="grow" style="text-align: center; font-size: 18px">Không có kết quả tìm kiếm</div>
                </div>
            </ng-template>
            <ng-template #hasData>
        <div class="frm_tbl">
            <table mat-table [dataSource]="dataSource">

                <!-- <ng-container matColumnDef="select">
                    <mat-header-cell *matHeaderCellDef class="checkAllProcedureAdd" [ngClass] ="(searchCancel || exportCancelDossiersOnPage) ? 'show':'hiden'">
                        <mat-checkbox (change)="$event ? masterToggle() : null" [checked]="selection.hasValue() && isAllSelected()" [indeterminate]="selection.hasValue() && !isAllSelected()" [aria-label]="checkboxLabel()">
                        </mat-checkbox>
                    </mat-header-cell>
                    <mat-cell *matCellDef="let row" data-label="Chọn" class="checkAllProcedureAdd" [ngClass] ="(searchCancel || exportCancelDossiersOnPage) ? 'show':'hiden'">
                        <mat-checkbox (click)="$event.stopPropagation()" (change)="$event ? selection.toggle(row) : null" [checked]="selection.isSelected(row)" [aria-label]="checkboxLabel(row)">
                        </mat-checkbox>
                    </mat-cell>
                </ng-container> -->

                <ng-container matColumnDef="select">
                    <mat-header-cell *matHeaderCellDef class="checkAllProcedureAdd" [ngClass] ="(searchCancel || exportCancelDossiersOnPage) ? 'show':'hiden'">
                        <mat-checkbox [checked]="checkAll" (change)="checkAllItem($event);masterToggle()">
                        </mat-checkbox>
                    </mat-header-cell>
                    <mat-cell *matCellDef="let row; index as i" data-label="Chọn" class="checkAllProcedureAdd" [ngClass] ="(searchCancel || exportCancelDossiersOnPage) ? 'show':'hiden'">
                        <mat-checkbox (change)="checkItem($event, row.id);selection.toggle(row)" [(ngModel)]="row.checked">
                        </mat-checkbox>
                    </mat-cell>
                </ng-container>

                <ng-container matColumnDef="stt">
                    <mat-header-cell *matHeaderCellDef i18n>STT</mat-header-cell>
                    <mat-cell *matCellDef="let row" i18n-data-label data-label="STT"> {{row.stt}} </mat-cell>
                </ng-container>

                <ng-container matColumnDef="code">
                    <mat-header-cell [ngStyle]="{'min-width': configWorkInterfaceKHA ? '200px' : 'unset'}" *matHeaderCellDef i18n>Mã số hồ sơ</mat-header-cell>
                    <mat-cell [ngStyle]="{'min-width': configWorkInterfaceKHA ? '200px' : 'unset', 'display': configWorkInterfaceKHA ? 'block' : null}" *matCellDef="let row" i18n-data-label data-label="Mã số hồ sơ" [ngClass]="{ 'cell_code_online': row.applyMethod.id === 0||4, 'cell_code_direct':   row.applyMethod.id === 1, 'cell_code_other':   row.applyMethod.id === 2 }" #tooltip="matTooltip"
                        matTooltip="{{row.codeText}}"
                        (click)="dossierDetail(row.id, row.procedure.id, row.task)" style="flex-direction: column">
                        <a>{{row.code}}</a>
                        <span class="more-text dossier-name" *ngIf="showDossierName" #tooltip="matTooltip"
                              matTooltip="{{row.applicant?.data?.tenHoSo}}" style="color: rgba(0,0,0,.87); font-weight: normal;"> {{row.applicant?.data?.tenHoSo}}
                        </span>
                        <span *ngIf="configWorkInterfaceKHA">
                            <br>
                            <span class="submission-method-badge" [ngClass]="{
                                    'online-badge': row.applyMethod.id === 0 || row.applyMethod.id === 4,
                                    'direct-badge': row.applyMethod.id === 1,
                                    'other-badge': row.applyMethod.id === 2
                                }">
                                <span *ngIf="row.applyMethod.id === 0 || row.applyMethod.id === 4"
                                    style="color: rgba(0,0,0,.87); font-weight: bold">[Trực tuyến]</span>
                                <span *ngIf="row.applyMethod.id === 1"
                                    style="color: rgba(0,0,0,.87); font-weight: bold">[Trực tiếp]</span>
                                <span *ngIf="row.applyMethod.id === 2"
                                    style="color: rgba(0,0,0,.87); font-weight: bold">[Khác]</span>
                            </span>
                            <ng-container *ngIf="row.applicant?.data?.is2Cap == 1; else oldInterfaceKHA">
                                <ng-container *ngIf="row.applicant?.data?.CaNhan_ToChuc === 'toChuc'; else elseBlockKHA">
                                    <span class="more-text-kha"
                                        style="color: rgba(0,0,0,.87); font-weight: bold">
                                        - Cá nhân/tổ chức nộp: <span style="font-weight: normal">{{row.applicant?.data?.tenToChuc}}</span>
                                    </span>
                                    <span *ngIf="row.applicant?.data?.address != '' && row.applicant?.data?.address" class="more-text-kha"
                                        style="color: rgba(0,0,0,.87); font-weight: bold">
                                        - Địa chỉ: <span style="font-weight: normal">{{row.applicant?.data?.address}}, {{generateAddressIs2Cap(row.applicant?.data)}}</span>
                                    </span>
                                    <span *ngIf="row.applicant?.data?.address1 != '' && row.applicant?.data?.address1" class="more-text-kha"
                                        style="color: rgba(0,0,0,.87); font-weight: bold">
                                        - Địa chỉ: <span style="font-weight: normal">{{row.applicant?.data?.address1}}</span>
                                    </span>
                                    <span *ngIf="row.applicant?.data?.noidungyeucaugiaiquyet != '' && row.applicant?.data?.noidungyeucaugiaiquyet" class="more-text-kha" 
                                            style="color: rgba(0,0,0,.87); font-weight: bold">
                                            - Nội dung xử lý: <span style="font-weight: normal">{{row.applicant.data.noidungyeucaugiaiquyet}}</span>
                                    </span>
                                    <span *ngIf="row.applicant?.data?.noidungyeucaugiaiquyet1 != '' && row.applicant?.data?.noidungyeucaugiaiquyet1" class="more-text-kha" 
                                        style="color: rgba(0,0,0,.87); font-weight: bold">
                                        - Nội dung xử lý: <span style="font-weight: normal">{{row.applicant.data.noidungyeucaugiaiquyet1}}</span>
                                    </span>
                                </ng-container>
                                <ng-template #elseBlockKHA>
                                        <span class="more-text-kha"
                                            style="color: rgba(0,0,0,.87); font-weight: bold">
                                            - Người nộp: <span style="font-weight: normal">{{row.applicant?.data?.fullname}}</span>
                                        </span>
                                        <span class="more-text-kha"
                                            style="color: rgba(0,0,0,.87); font-weight: bold">
                                            - Địa chỉ: <span style="font-weight: normal">{{row.applicant?.data?.address}}, {{generateAddressIs2Cap(row.applicant?.data)}}</span>
                                        </span>
                                        <span class="more-text-kha"
                                            style="color: rgba(0,0,0,.87); font-weight: bold">
                                            - Nội dung xử lý: <span style="font-weight: normal">{{row.applicant.data.noidungyeucaugiaiquyet}}</span>
                                        </span>
                                </ng-template>
                            </ng-container>
                            <ng-template #oldInterfaceKHA>
                                <ng-container *ngIf="row.applicant?.data?.CaNhan_ToChuc === 'toChuc'; else elseBlockKHA">
                                    <span class="more-text-kha"
                                        style="color: rgba(0,0,0,.87); font-weight: bold">
                                        - Cá nhân/tổ chức nộp: <span style="font-weight: normal">{{row.applicant?.data?.tenToChuc}}</span>
                                    </span>
                                    <span class="more-text-kha"
                                        style="color: rgba(0,0,0,.87); font-weight: bold">
                                        - Địa chỉ: <span style="font-weight: normal">{{row.applicant?.data?.address1}}</span>
                                    </span>
                                    <span *ngIf="row.applicant?.data?.noidungyeucaugiaiquyet != '' && row.applicant?.data?.noidungyeucaugiaiquyet" class="more-text-kha" 
                                            style="color: rgba(0,0,0,.87); font-weight: bold">
                                            - Nội dung xử lý: <span style="font-weight: normal">{{row.applicant.data.noidungyeucaugiaiquyet}}</span>
                                    </span>
                                    <span *ngIf="row.applicant?.data?.noidungyeucaugiaiquyet1 != '' && row.applicant?.data?.noidungyeucaugiaiquyet1" class="more-text-kha" 
                                        style="color: rgba(0,0,0,.87); font-weight: bold">
                                        - Nội dung xử lý: <span style="font-weight: normal">{{row.applicant.data.noidungyeucaugiaiquyet1}}</span>
                                    </span>
                                </ng-container>
                                <ng-template #elseBlockKHA>
                                        <span class="more-text-kha"
                                            style="color: rgba(0,0,0,.87); font-weight: bold">
                                            - Người nộp: <span style="font-weight: normal">{{row.applicant?.data?.fullname}}</span>
                                        </span>
                                        <span class="more-text-kha"
                                            style="color: rgba(0,0,0,.87); font-weight: bold">
                                            - Địa chỉ: <span style="font-weight: normal">{{row.applicant?.data?.address}}, {{generateAddressFix(row.applicant?.data)}}</span>
                                        </span>
                                        <span class="more-text-kha"
                                            style="color: rgba(0,0,0,.87); font-weight: bold">
                                            - Nội dung xử lý: <span style="font-weight: normal">{{row.applicant.data.noidungyeucaugiaiquyet}}</span>
                                        </span>
                                </ng-template>  
                            </ng-template>                                             
                        </span>     
                    </mat-cell>
                </ng-container>

                <ng-container matColumnDef="procedureName">
                    <mat-header-cell *matHeaderCellDef i18n>Thủ tục</mat-header-cell>
                    <mat-cell *matCellDef="let row" i18n-data-label data-label="Thủ tục">
                        <span>
                            <a class="cell_code" [ngStyle]="configWorkInterfaceKHA ? {'color': 'black', 'font-weight': 'bold'} : null" href="{{config.padsvcURL}}procedure/detail/{{row.procedure.id}}" target="blank">{{row.procedure.code}}</a>
                            <span class="procedureName" *ngIf="row.procedure.translate" #tooltip="matTooltip"
                            matTooltip="{{row.procedure.translate.name}}"> - {{row.procedure.translate.name}}
                            <span *ngIf="row.procedure.translate.name == undefined || row.procedure.translate.name == null || row.procedure.translate.name.trim() == ''" i18n>(Không tìm thấy bản dịch)</span>
                        </span>
                        </span>
                    </mat-cell>
                </ng-container>

                <ng-container matColumnDef="applicantName">
                    <mat-header-cell *matHeaderCellDef i18n>Người nộp</mat-header-cell>
                    <mat-cell *matCellDef="let row" i18n-data-label data-label="Người nộp">
                        <ul>
                            <li>{{row.applicant?.data?.fullname}}</li>
                            <li>{{row.applicant?.data?.address}}, {{generateAddress(row.applicant?.data)}}</li>
                        </ul>
                    </mat-cell>
                </ng-container>
                <ng-container matColumnDef="profileOwner">
                  <mat-header-cell *matHeaderCellDef >Chủ hồ sơ</mat-header-cell>
                  <mat-cell *matCellDef="let row"  i18n-data-label data-label="Chủ hồ sơ"> {{row.applicant?.data?.ownerFullname}}
                  </mat-cell>
                </ng-container>

                <ng-container matColumnDef="appliedDate">
                    <mat-header-cell *matHeaderCellDef i18n>Ngày nộp</mat-header-cell>
                    <mat-cell *matCellDef="let row" i18n-data-label data-label="Ngày nộp"> {{row.appliedDate | date : 'dd/MM/yyyy HH:mm:ss'}}
                    </mat-cell>
                </ng-container>
                <ng-container matColumnDef="reason">
                    <mat-header-cell *matHeaderCellDef i18n>Lý do từ chối</mat-header-cell>
                    <ng-container *ngIf="scrollOverflowRefuseReasonText == false then fullReason; else scrollReason"></ng-container>
                    <ng-template #fullReason>
                        <mat-cell *matCellDef="let row" i18n-data-label data-label="Lý do từ chối" [innerHTML]="row.dossierStatus.comment">
                        </mat-cell>
                    </ng-template>
                    <ng-template #scrollReason>
                        <mat-cell *matCellDef="let row" i18n-data-label data-label="Lý do từ chối">
                            <div [innerHTML]="row.dossierStatus.comment" class="overflow-cell"></div>
                        </mat-cell>
                    </ng-template>         
                </ng-container>

                <ng-container matColumnDef="citizenWithdrawComment" *ngIf="showCitizenWithdrawComment">
                    <mat-header-cell *matHeaderCellDef i18n>Lý do rút hồ sơ</mat-header-cell>
                    <mat-cell *matCellDef="let row" i18n-data-label data-label="Lý do rút hồ sơ" [innerHTML]="row.citizenWithdrawComment">
                    </mat-cell>
                </ng-container>

                <ng-container matColumnDef="status">
                    <mat-header-cell *matHeaderCellDef i18n>Trạng thái</mat-header-cell>
                    <mat-cell *matCellDef="let row" i18n-data-label data-label="Trạng thái">
                        <span [style.color]="getStatusColor(row.dossierStatus.id)">
                            {{row.dossierStatus.name}}<br>
                            <span *ngIf="showStatusVnpost == 1 && row.vnpostStatus" style="color: black;">Trạng thái Vnpost:<br>{{row.vnpostStatus}}</span>
                            <span *ngIf="this.showStatusCTDT && row.authenticationStatusCurrent" style="color: black;"><i>{{setAuthStatusName(row.authenticationStatusCurrent?.id)}}</i><br></span>
                            <span *ngIf="checkShowReturnProcessingDossierInf(row)" style="color: black;">{{row.dossierStatus?.description ? row.dossierStatus?.description : ""}}</span>
                        </span>
                        
                    </mat-cell>
                    
                </ng-container>

                <ng-container matColumnDef="action">
                    <mat-header-cell *matHeaderCellDef i18n>Thao tác</mat-header-cell>
                    <mat-cell *matCellDef="let row" i18n-data-label data-label="Thao tác">
                        <button mat-icon-button [matMenuTriggerFor]="actionMenu">
                            <mat-icon>more_horiz</mat-icon>
                        </button>
                        <mat-menu #actionMenu="matMenu" xPosition="before">
                            <ng-container *ngIf="this.qbhwithdrawprocess == true; then QBHmenu else normalmenu"></ng-container>
                            <ng-template #QBHmenu>
                                <button mat-menu-item class="menuAction" *ngIf="row.dossierTaskStatus?.id === config.dossierTaskStatus.waitWithdrawn.id && !allowOfficerToApproveWithdrawRequest" (click)="openApprovalQBHDialog(row.id, row.dossierStatus?.id)">
                                    <mat-icon>done_all</mat-icon><span >Xác nhận yêu cầu rút hồ sơ</span>
                                </button>
                            </ng-template>
                            <ng-template #normalmenu>
                                <button mat-menu-item class="menuAction" *ngIf="row.dossierTaskStatus?.id === config.dossierTaskStatus.waitWithdrawn.id && !allowOfficerToApproveWithdrawRequest" (click)="confirmHasWithdraw(row)">
                                    <mat-icon>done_all</mat-icon><span i18n>Xác nhận công dân đã rút HS</span>
                                </button>
                            </ng-template>
                            <button mat-menu-item class="menuAction" *ngIf="(row.dossierTaskStatus?.id === config.dossierTaskStatus.waitWithdrawn.id) && allowOfficerToApproveWithdrawRequest" (click)="withdrawDialogsHcm(row)">
                                <mat-icon>done_all</mat-icon><span>Phê duyệt yêu cầu rút hồ sơ</span>
                            </button>
                            <button *ngIf="row?.dossierStatus?.id === 6 && showButtonDetailDBN" mat-menu-item class="menuAction" (click)="dossierDetail(row.id, row.procedure.id, row.task)">
                                <mat-icon>info</mat-icon><span i18n>Chi tiết hồ sơ</span>
                            </button>
                             <button mat-menu-item class="menuAction" (click)="viewProcess(row.procedureProcessDefinition.id)" *ngIf="row.procedureProcessDefinition != undefined">
                                <mat-icon>insights</mat-icon><span i18n>Xem quy trình</span>
                            </button>
                            <button mat-menu-item class="menuAction" (click)="returnCancelProcessingDossier(row.id, row.code)" *ngIf="checkShowReturnCancelProcessingBtn(row)">
                                <mat-icon>arrow_back</mat-icon><span i18n="@@QNIRETURNCANCELPROCESSINGDOSSIER">Trả hồ sơ dừng xử lý</span>
                            </button>
                            <button mat-menu-item class="menuAction" (click)="deleteDialog(row.id, row.code)" *ngIf="checkProvineAdmin || (env?.hideDeleteButton !== true && isAdmin) || (hasDossierDeletePermission && row.dossierStatus?.id === 2 && row.currentTask[0]?.isFirst === 1 && row.applyMethod?.id !== 0)">
                                <mat-icon>delete_outline</mat-icon><span i18n>Xóa</span>
                            </button>
                            <button mat-menu-item class="menuAction" (click)="viewUpdateHistory(row.id, row.code)">
                                <mat-icon>update</mat-icon><span i18n>Xem lịch sử cập nhật</span>
                            </button>
                            <button mat-menu-item class="menuAction" (click)="restoreDialog(row.id, row.code)" *ngIf="this.showBtnRestoreDossier && (checkProvineAdmin || (env?.hideDeleteButton !== true && isAdmin) || (hasDossierDeletePermission && row.dossierStatus?.id === 2 && row.currentTask[0]?.isFirst === 1 && row.applyMethod?.id !== 0))">
                                <mat-icon>restore_from_trash</mat-icon><span i18n>Khôi phục</span>
                            </button>
                            <button mat-menu-item class="menuAction" (click)="downloadAllFile(row.id)">
                                <mat-icon>cloud_download</mat-icon>
                                <span>Tải văn bản của hồ sơ</span>
                            </button>
                        </mat-menu>
                    </mat-cell>
                </ng-container>

                <mat-header-row *matHeaderRowDef="getDisplayedColumns()"></mat-header-row>
                <mat-row *matRowDef="let row; columns: getDisplayedColumns();"></mat-row>
            </table>
            <!-- <div class="frm_Pagination">
                <ul class="temp_Arr">
                    <li *ngFor="let item of ELEMENTDATA  | paginate: {itemsPerPage: size, currentPage: page, totalItems: countResult, id: 'pgnx'}">
                    </li>
                </ul>
                <div class="pageSize">
                    <span i18n>Hiển thị </span>
                    <mat-form-field appearance="outline">
                        <mat-select [(value)]="size" (valueChange)="paginate(pageIndex, 1)">
                            <mat-option *ngFor='let opt of pgSizeOptions;' [value]="opt">{{opt}}</mat-option>
                        </mat-select>
                    </mat-form-field>
                    <span><span i18n>trên</span> {{countResult}} <span i18n>bản ghi</span></span>
                </div>
                <div class="control">
                    <pagination-controls id="pgnx" (pageChange)="page = $event; paginate(page, 0)" responsive="true" previousLabel="" nextLabel="">
                    </pagination-controls>
                </div>
            </div> -->
            <pagination-slice id="pgnx"
                    [itemsPerPage]="size"
                    [currentPage]="page"
                    [totalItems]="countResult"
                    [pageSizeOptions]="[].concat(pgSizeOptions)"
                    [dataSource]="ELEMENTDATA"
                    (change)="changePageOrSize($event)"
                    [type]="paginationType">
            </pagination-slice>

        </div>
     </ng-template>
    </ng-template>
    <ng-template #QBHLayout>
        <div class="frm_tbl">
            <table mat-table [dataSource]="dataSource">
                <ng-container matColumnDef="select">
                    <mat-header-cell *matHeaderCellDef class="checkAllProcedureAdd" [ngClass] ="searchCancel?'show':'hiden'">
                        <mat-checkbox (change)="$event ? masterToggle() : null" [checked]="selection.hasValue() && isAllSelected()" [indeterminate]="selection.hasValue() && !isAllSelected()" [aria-label]="checkboxLabel()">
                        </mat-checkbox>
                    </mat-header-cell>
                    <mat-cell *matCellDef="let row" data-label="Chọn" class="checkAllProcedureAdd" [ngClass] ="searchCancel?'show':'hiden'">
                        <mat-checkbox (click)="$event.stopPropagation()" (change)="$event ? selection.toggle(row) : null" [checked]="selection.isSelected(row)" [aria-label]="checkboxLabel(row)">
                        </mat-checkbox>
                    </mat-cell>
                </ng-container>

                <ng-container matColumnDef="stt">
                    <mat-header-cell *matHeaderCellDef i18n>STT</mat-header-cell>
                    <mat-cell *matCellDef="let row" i18n-data-label data-label="STT"> {{row.stt}} </mat-cell>
                </ng-container>

                <ng-container matColumnDef="code">
                    <mat-header-cell *matHeaderCellDef i18n>Mã số hồ sơ</mat-header-cell>
                    <mat-cell *matCellDef="let row" i18n-data-label data-label="Mã số hồ sơ" [ngClass]="{ 'cell_code_online': row.applyMethod.id === 0||4, 'cell_code_direct':   row.applyMethod.id === 1, 'cell_code_other':   row.applyMethod.id === 2 }" #tooltip="matTooltip"
                        matTooltip="{{row.codeText}}"
                        (click)="dossierDetail(row.id, row.procedure.id, row.task)" style="flex-direction: column">
                        <a>{{row.code}}</a>
                        <span class="more-text dossier-name" *ngIf="showDossierName" #tooltip="matTooltip"
                              matTooltip="{{row.applicant?.data?.tenHoSo}}" style="color: rgba(0,0,0,.87); font-weight: normal;"> {{row.applicant?.data?.tenHoSo}}
                        </span>
                    </mat-cell>
                </ng-container>

                <!--<ng-container matColumnDef="procedureName">
                    <mat-header-cell *matHeaderCellDef i18n>Thủ tục</mat-header-cell>
                    <mat-cell *matCellDef="let row" i18n-data-label data-label="Thủ tục">
                        <span>
                            <a class="cell_code" href="{{config.padsvcURL}}procedure/detail/{{row.procedure.id}}" target="blank">{{row.procedure.code}}</a>
                            <span class="procedureName" *ngIf="row.procedure.translate" #tooltip="matTooltip"
                            matTooltip="{{row.procedure.translate.name}}"> - {{row.procedure.translate.name}}
                            <span *ngIf="row.procedure.translate.name == undefined || row.procedure.translate.name == null || row.procedure.translate.name.trim() == ''" i18n>(Không tìm thấy bản dịch)</span>
                        </span>
                        </span>
                    </mat-cell>
                </ng-container>-->
                <ng-container  matColumnDef="noidung">
                    <mat-header-cell *matHeaderCellDef >Nội dung yêu cầu giải quyết</mat-header-cell>
                    <mat-cell  *matCellDef="let row"  i18n-data-label data-label="Nội dung yêu cầu giải quyết"  style="display: block;color: black" #tooltip="matTooltip" [ngClass]="{ 'cell_code_online': row.applyMethod.id === 0||4, 'cell_code_direct':   row.applyMethod.id === 1, 'cell_code_other':   row.applyMethod.id === 2 }"
                     matTooltip="{{row.applicant?.data?.noidungyeucaugiaiquyet}}">
                     <a (click)="dossierDetail(row.id, row.procedure.id, row.task)">{{row.applicant.data.noidungyeucaugiaiquyet}}</a>
                    </mat-cell>
                  </ng-container>


                <ng-container matColumnDef="applicantName">
                    <mat-header-cell *matHeaderCellDef i18n>Người nộp</mat-header-cell>
                    <mat-cell *matCellDef="let row" i18n-data-label data-label="Người nộp">
                        <ul>
                            <li>{{row.applicant?.data?.fullname}}</li>
                            <li>{{row.applicant?.data?.address}}, {{generateAddress(row.applicant?.data)}}</li>
                        </ul>
                    </mat-cell>
                </ng-container>
                <ng-container matColumnDef="profileOwner">
                  <mat-header-cell *matHeaderCellDef >Chủ hồ sơ</mat-header-cell>
                  <mat-cell *matCellDef="let row"  i18n-data-label data-label="Chủ hồ sơ"> {{row.applicant?.data?.ownerFullname}}
                  </mat-cell>
                </ng-container>

                <ng-container matColumnDef="appliedDate">
                    <mat-header-cell *matHeaderCellDef i18n>Ngày nộp</mat-header-cell>
                    <mat-cell *matCellDef="let row" i18n-data-label data-label="Ngày nộp"> {{row.appliedDate | date : 'dd/MM/yyyy HH:mm:ss'}}
                    </mat-cell>
                </ng-container>
                <ng-container matColumnDef="reason">
                    <mat-header-cell *matHeaderCellDef i18n>Lý do từ chối</mat-header-cell>
                    <mat-cell *matCellDef="let row" i18n-data-label data-label="Lý do từ chối" [innerHTML]="row.dossierStatus.comment">
                    </mat-cell>
                </ng-container>

                <ng-container matColumnDef="citizenWithdrawComment" *ngIf="showCitizenWithdrawComment">
                    <mat-header-cell *matHeaderCellDef i18n>Lý do rút hồ sơ</mat-header-cell>
                    <mat-cell *matCellDef="let row" i18n-data-label data-label="Lý do rút hồ sơ" [innerHTML]="row.citizenWithdrawComment">
                    </mat-cell>
                </ng-container>

                <ng-container matColumnDef="status">
                    <mat-header-cell *matHeaderCellDef i18n>Trạng thái</mat-header-cell>
                    <mat-cell *matCellDef="let row" i18n-data-label data-label="Trạng thái">
                        <span [style.color]="getStatusColor(row.dossierStatus.id)">
                            {{row.dossierStatus.name}}<br>
                            <span *ngIf="showStatusVnpost == 1 && row.vnpostStatus" style="color: black;">Trạng thái Vnpost:<br>{{row.vnpostStatus}}</span>
                        </span>
                        
                    </mat-cell>
                    
                </ng-container>

                <ng-container matColumnDef="action">
                    <mat-header-cell *matHeaderCellDef i18n>Thao tác</mat-header-cell>
                    <mat-cell *matCellDef="let row" i18n-data-label data-label="Thao tác">
                        <button mat-icon-button [matMenuTriggerFor]="actionMenu">
                            <mat-icon>more_horiz</mat-icon>
                        </button>
                        <mat-menu #actionMenu="matMenu" xPosition="before">
                            <ng-container *ngIf="this.qbhwithdrawprocess == true; then QBHmenu else normalmenu"></ng-container>
                            <ng-template #QBHmenu>
                                <button mat-menu-item class="menuAction" *ngIf="row.dossierTaskStatus?.id === config.dossierTaskStatus.waitWithdrawn.id && !allowOfficerToApproveWithdrawRequest" (click)="openApprovalQBHDialog(row.id, row.dossierStatus?.id)">
                                    <mat-icon>done_all</mat-icon><span >Xác nhận yêu cầu rút hồ sơ</span>
                                </button>
                            </ng-template>
                            <ng-template #normalmenu>
                                <button mat-menu-item class="menuAction" *ngIf="row.dossierTaskStatus?.id === config.dossierTaskStatus.waitWithdrawn.id && !allowOfficerToApproveWithdrawRequest" (click)="confirmHasWithdraw(row)">
                                    <mat-icon>done_all</mat-icon><span i18n>Xác nhận công dân đã rút HS</span>
                                </button>
                            </ng-template>
                            <button *ngIf="row?.dossierStatus?.id === 6 && showButtonDetailDBN" mat-menu-item class="menuAction" (click)="dossierDetail(row.id, row.procedure.id, row.task)">
                                <mat-icon>info</mat-icon><span i18n>Chi tiết hồ sơ</span>
                            </button>
                            <button mat-menu-item class="menuAction" (click)="returnCancelProcessingDossier(row.id, row.code)" *ngIf="checkShowReturnCancelProcessingBtn(row)">
                                <mat-icon>insights</mat-icon><span i18n="@@QNIRETURNCANCELPROCESSINGDOSSIER">Trả hồ sơ dừng xử lý</span>
                            </button>
                            <button mat-menu-item class="menuAction" (click)="viewProcess(row.procedureProcessDefinition.id)" *ngIf="row.procedureProcessDefinition != undefined">
                                <mat-icon>insights</mat-icon><span i18n>Xem quy trình</span>
                            </button>
                            <button mat-menu-item class="menuAction" (click)="deleteDialog(row.id, row.code)" *ngIf="checkProvineAdmin || (env?.hideDeleteButton !== true && isAdmin) || (hasDossierDeletePermission && row.dossierStatus?.id === 2 && row.currentTask[0]?.isFirst === 1 && row.applyMethod?.id !== 0)">
                                <mat-icon>delete_outline</mat-icon><span i18n>Xóa</span>
                            </button>
                            <button mat-menu-item class="menuAction" (click)="viewUpdateHistory(row.id, row.code)">
                                <mat-icon>update</mat-icon><span i18n>Xem lịch sử cập nhật</span>
                            </button>
                            <button mat-menu-item class="menuAction" (click)="restoreDialog(row.id, row.code)" *ngIf="this.showBtnRestoreDossier && (checkProvineAdmin || (env?.hideDeleteButton !== true && isAdmin) || (hasDossierDeletePermission && row.dossierStatus?.id === 2 && row.currentTask[0]?.isFirst === 1 && row.applyMethod?.id !== 0))">
                                <mat-icon>restore_from_trash</mat-icon><span i18n>Khôi phục</span>
                            </button>
                            <button mat-menu-item class="menuAction" (click)="downloadAllFile(row.id)">
                                <mat-icon>cloud_download</mat-icon>
                                <span>Tải văn bản của hồ sơ</span>
                            </button>
                        </mat-menu>
                    </mat-cell>
                </ng-container>

                <mat-header-row *matHeaderRowDef="getDisplayedColumns()"></mat-header-row>
                <mat-row *matRowDef="let row; columns: getDisplayedColumns();"></mat-row>
            </table>
            <!-- <div class="frm_Pagination">
                <ul class="temp_Arr">
                    <li *ngFor="let item of ELEMENTDATA  | paginate: {itemsPerPage: size, currentPage: page, totalItems: countResult, id: 'pgnx'}">
                    </li>
                </ul>
                <div class="pageSize">
                    <span i18n>Hiển thị </span>
                    <mat-form-field appearance="outline">
                        <mat-select [(value)]="size" (valueChange)="paginate(pageIndex, 1)">
                            <mat-option *ngFor='let opt of pgSizeOptions;' [value]="opt">{{opt}}</mat-option>
                        </mat-select>
                    </mat-form-field>
                    <span><span i18n>trên</span> {{countResult}} <span i18n>bản ghi</span></span>
                </div>
                <div class="control">
                    <pagination-controls id="pgnx" (pageChange)="page = $event; paginate(page, 0)" responsive="true" previousLabel="" nextLabel="">
                    </pagination-controls>
                </div>
            </div> -->
            <pagination-slice id="pgnx"
                    [itemsPerPage]="size"
                    [currentPage]="page"
                    [totalItems]="countResult"
                    [pageSizeOptions]="[].concat(pgSizeOptions)"
                    [dataSource]="ELEMENTDATA"
                    (change)="changePageOrSize($event)"
                    [type]="paginationType">
            </pagination-slice>

        </div>
    </ng-template>
    </div>
</div>
