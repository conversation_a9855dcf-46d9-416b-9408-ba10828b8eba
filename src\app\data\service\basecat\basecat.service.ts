import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { ApiProviderService } from 'src/app/core/service/api-provider.service';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class BasecatService {

  constructor(
    private http: HttpClient,
    private apiProviderService: ApiProviderService
  ) { }

  private basecat = this.apiProviderService.getUrl('digo', 'basecat');

  getAgencyTag(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.basecat + '/tag/' + id, { headers }).pipe();    
  }

  getListTagByCategoryId(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.basecat + '/tag/--by-category-id' + searchString, { headers }).pipe();    
  }

  getListGuideType(categoryId, page): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    const param = '?category-id=' + categoryId + '&page=' + page;
    return this.http.get(this.basecat + '/tag/--by-category-id' + param, { headers });
  }

  getDossierCode(dossierCodePattern, agencyCode): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.basecat + '/pattern/' + dossierCodePattern + '/--next-value?code=' + agencyCode, { headers });
  }

  getPatternCode(dossierCodePattern, agencyCode): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.put(this.basecat + '/pattern/' + dossierCodePattern + '/--get-next-value?code=' + agencyCode, { headers });
  }

  putDossierCode(dossierCodePattern, agencyCode): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.put<any>(this.basecat + '/pattern/' + dossierCodePattern + '/--next-value?code=' + agencyCode, { headers });
  }

  getDetailGuideType(guideTypeId): Promise<any> {
    return this.http.get(this.basecat + '/tag/' + guideTypeId).toPromise();
  }

  getTagNameList(categoryId?: string, page?: number, size?: number): Observable<any> {
    let category = categoryId ? categoryId : '5f3a491c4e1bd312a6f00005';
    let pages = page ? page : 0;
    let sizes = size ? size : 10;
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get<any>(this.basecat + '/tag/name?category-id=' + category + '&page=' + pages + '&size=' + sizes, { headers }).pipe();

  }

  getTagNameListPage(keyword?: string, categoryId?: string, page?: number, size?: number): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get<any>(this.basecat + '/tag/name?page=' + page + '&size=' + size + '&keyword=' + keyword + '&category-id=' + categoryId, { headers }).pipe();    
  }

  getTagList(search): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get<any>(this.basecat + '/tag/'+search, { headers }).pipe();    
  }

  getTagById(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get<any>(this.basecat + `/tag/${id}`, { headers }).pipe();    
  }

  getTagCategory(search): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get<any>(this.basecat + '/tag-category'+search, { headers }).pipe();    
  }
  getTagNameById(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get<any>(this.basecat + `/tag/name?id=${id}`, { headers }).pipe();    
  }

  getTimeSheetConfig(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get<any>(this.basecat + `/timesheet/${id}`, { headers }).pipe();    
  }
  
  getTimeSheetById(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get<any>(this.basecat + `/timesheet/${id}`)
  }
}