import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { BxdHpgComponent } from './bxd-hpg.component';

describe('BxdHpgComponent', () => {
  let component: BxdHpgComponent;
  let fixture: ComponentFixture<BxdHpgComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ BxdHpgComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(BxdHpgComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
