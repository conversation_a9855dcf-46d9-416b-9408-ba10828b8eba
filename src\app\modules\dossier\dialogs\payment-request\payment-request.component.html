<button mat-icon-button class="close-button" (click)="onDismiss()">
    <mat-icon>close</mat-icon>
</button>
<h3 class="dialog_title" mat-dialog-title><PERSON><PERSON><PERSON> cầu thanh toán</h3>
<div mat-dialog-content class="dialog_content">
    <span>Bạn c<PERSON> chắc chắn muốn gửi yêu cầu thanh toán cho hồ sơ </span><span
        class="highlight">{{dossierCode}}</span><span>?</span>
    <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="column" *ngIf="showCheckConfirmPayment && showConfirmPayment" style="padding: 10px 0;font-weight: 500;">
        <mat-checkbox [(ngModel)]="checkConfirmPayment"><span i18n="@@ConfirmPaymentLabel">Xá<PERSON> <PERSON>h<PERSON><PERSON> số tiền yê<PERSON> cầu thanh toán</span>{{totalCost ? ':' : ''}} <span class="highlight">{{totalCost}}</span>
        </mat-checkbox>
    </div>
    <form [formGroup]="updateForm" class="updateForm">
        <div fxLayout="column" fxLayout.xs="column" fxLayout.sm="column" class="formFieldOutline" fxLayoutAlign="space-between" *ngIf="isPaymentDayLimit==true && paymentDayLimit > 0">
            <mat-form-field appearance="outline" fxFlex.gt-md="49.5" fxFlex.gt-sm="49.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                <mat-label>Số ngày tạm dừng thanh toán</mat-label>
                <input type="number" matInput [(ngModel)]="paymentDayLimit" formControlName="numberPausePaidDay" [disabled]="true" required>
            </mat-form-field>
        </div>
        <div fxLayout="column" fxLayout.xs="column" fxLayout.sm="column" class="formFieldOutline" fxLayoutAlign="space-between" *ngIf="this.dossierDetail?.dossierStatus?.id !== 0 && (isPaymentDayLimit==false || paymentDayLimit < 1)">
            <mat-form-field appearance="outline" fxFlex.gt-md="49.5" fxFlex.gt-sm="49.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                <mat-label>Số ngày tạm dừng thanh toán</mat-label>
                <input type="number" matInput formControlName="numberPausePaidDay" required oninput="this.value = !!this.value && Math.abs(this.value) > 0 ? Math.abs(this.value) : null">
            </mat-form-field>
        </div>
    </form>
    <div *ngIf="this.deadlineForPaymentDossier==1">
        <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="column" style="padding: 10px 0;font-weight: 500;color: green;">
            <mat-checkbox [(ngModel)]="checkNumberDate" (change)="onCheckNumberDateChange($event)"><span>Hạn thanh toán hồ sơ:</span>
            </mat-checkbox>
        </div>
        <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="column" >
            <mat-form-field appearance="outline">
                <mat-label>Số ngày chờ thanh toán lệ phí hồ sơ</mat-label>
                <input matInput type="number" [readonly]="!checkNumberDate" [max]="this.deadlineForPaymentDossierNumberOfDays" [(ngModel)]="numberDate" (change)="onNumberDateChange($event)">
            </mat-form-field>
        </div>
    </div>
    <div class="rsProcedureFee" fxLayout="row" fxLayout.xs="column" fxLayout.sm="column" *ngIf="this.hideCurrencyConverter == 1 && this.allowCurrencyConvert == true && this.showExchangeRate == 0" >
        <mat-table [dataSource] = "FEE_EX_ELEMENTDATA_SOURCE" fxFlex='grow' class="tblFee">
            <ng-container matColumnDef="totalUSDCost">
                <mat-header-cell *matHeaderCellDef>Số tiền (USD)</mat-header-cell>
                <mat-cell *matCellDef="let row">{{row.totalUSDCost}}</mat-cell>
            </ng-container>
            <ng-container matColumnDef="totalVNDCost">
                <mat-header-cell *matHeaderCellDef>Quy đổi thành (VND)</mat-header-cell>
                <mat-cell  *matCellDef="let row" >{{row.totalVNDCost}}</mat-cell>
            </ng-container>
            <ng-container matColumnDef="timeExchange">
                <mat-header-cell *matHeaderCellDef>Thời gian quy đổi</mat-header-cell>
                <mat-cell *matCellDef="let row">{{row.timeExchange}}</mat-cell>
            </ng-container>
            <mat-header-row *matHeaderRowDef="feeDisplayedColumns"></mat-header-row>
            <mat-row *matRowDef="let row; columns: feeDisplayedColumns;"></mat-row>
        </mat-table> 
        
    </div> 
    <div class="rsProcedureFee" fxLayout="row" fxLayout.xs="column" fxLayout.sm="column" *ngIf="this.hideCurrencyConverter == 1 && this.allowCurrencyConvert == true && this.showExchangeRate == 1" >
        <mat-table [dataSource] = "FEE_EX_ELEMENTDATA_SOURCE" fxFlex='grow' class="tblFee">
            <ng-container matColumnDef="totalUSDCost">
                <mat-header-cell *matHeaderCellDef>Số tiền (USD)</mat-header-cell>
                <mat-cell *matCellDef="let row">{{row.totalUSDCost}}</mat-cell>
            </ng-container>
            <ng-container  matColumnDef="exchangeRate">
                <mat-header-cell *matHeaderCellDef>Tỷ giá (VND)</mat-header-cell>
                <mat-cell *matCellDef="let row">{{row.exratedCurrency}}</mat-cell>
            </ng-container>
            <ng-container matColumnDef="totalVNDCost">
                <mat-header-cell *matHeaderCellDef>Quy đổi thành (VND)</mat-header-cell>
                <mat-cell  *matCellDef="let row" >{{row.totalVNDCost}}</mat-cell>
            </ng-container>
            <ng-container matColumnDef="timeExchange">
                <mat-header-cell *matHeaderCellDef>Thời gian quy đổi</mat-header-cell>
                <mat-cell *matCellDef="let row">{{row.timeExchange}}</mat-cell>
            </ng-container>
            <mat-header-row *matHeaderRowDef="ExfeeDisplayedColumns"></mat-header-row>
            <mat-row *matRowDef="let row; columns: ExfeeDisplayedColumns;"></mat-row>
        </mat-table> 
    </div> 
    <div class="commentEditor" fxLayout="column" fxLayout.xs="column" fxLayout.sm="column" *ngIf="!hideReasonPay">
        <p class="lbl"><span i18n>Lý do</span><span style="color:#ce7a58;">&nbsp;*</span></p>
        <ckeditor debounce="10" [editor]="Editor" class="customCKEditor" (change)="onCommentEditorChange($event)"
            fxFlex='grow' [config]='commentConfig'>
        </ckeditor>
        <div class="errorMsg" *ngIf="isCKMaxlenght">
            <span i18n>Nội dung không quá 500 ký tự</span>
            <div class="err">
                <mat-icon>priority_high</mat-icon>
            </div>
        </div>
    </div>
    <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="column" style="margin: 0 0 1em 0" *ngIf="qbhAddInfoToFiles" >
        <button mat-flat-button fxFlex='20' class="applyBtn" (click)="changeFile('docx')">
            <span>Kết xuất file</span>
        </button>
    </div>
    <div [ngClass]="{'file_uploaded': uploaded == true}" fxHide.lt-md class="marginbottom" *ngIf="qbhAddInfoToFiles">
        <div class="drag_upload_btn" [ngClass]="{'no_boder': uploaded == true}">
            <button mat-button [ngClass]="{'btn_uploaded': uploaded == true, 'clear_file_queue': uploaded == false}"
                fxFlex='grow'>
                <mat-icon class="material-icons-outlined">cloud_upload</mat-icon> <a href="">
                    <span i18n>Kéo thả tệp tin hoặc </span><span class="txtUpload" i18n>Tải lên</span>
                </a>
                <div>
                    <span>Kích thước tối đa của một tệp tin:</span> {{ maxFileSize }}MB
                </div>
            </button>
            <input id="file_upload" name="file_upload[]" type='file' (change)="onSelectFile($event)" multiple
                [accept]="acceptFileExtension" value="{{blankVal}}" fxFlex='grow'>
        </div>
        <div class="file_drag_upload_preview">
            <div class="list_uploaded" *ngFor='let url of urls; let i = index;'>
                <div class="file_icon" [ngStyle]="{'background-image': 'url('+ urls[i] +')'}"></div>
                <span class="file_name" matTooltip="{{fileNamesFull[i]}}"
                    [matTooltipPosition]="'right'">{{fileNames[i]}}</span>
                <a mat-icon-button class="delete_file">
                    <mat-icon (click)="removeItem(i)">close</mat-icon>
                </a>
            </div>
        </div>
    </div>
    <div class="res_uploadFile marginbottom" fxShow="true" fxHide.gt-sm *ngIf="qbhAddInfoToFiles">
        <div class="res_upload_btn">
            <button mat-button fxFlex='grow'>
                <mat-icon class="material-icons-outlined">cloud_upload</mat-icon>
                <span class="txtUpload" i18n>Tải lên</span>
            </button>
            <input id="file_upload" name="file_upload[]" type='file' (change)="onSelectFile($event)" multiple
                [accept]="acceptFileExtension" value="{{blankVal}}" fxFlex='grow'>
        </div>
        <div class="res_upload_preview">
            <div class="list_uploaded" *ngFor='let url of urls; let i = index;' fxFlex.gt-sm="49.5" fxFlex.gt-xs="48.5"
                fxFlex='grow'>
                <div class="file_icon" [ngStyle]="{'background-image': 'url('+ urls[i] +')'}"></div>
                <span class="file_name" matTooltip="{{fileNamesFull[i]}}"
                    [matTooltipPosition]="'right'">{{fileNames[i]}}</span>
                <a mat-icon-button class="delete_file">
                    <mat-icon (click)="removeItem(i)">close</mat-icon>
                </a>
            </div>
        </div>
    </div>
    
</div>

<digo-check-send-notify [totalCost] = 'totalCost' functionType="paymentRequestDossier" functionTypeOfficer="" [receiveType]="env.enableApprovalOfLeadership"></digo-check-send-notify>

<div fxLayout="row" fxLayout.xs="column" fxLayout.sm="column" fxLayoutAlign="center">
    <button mat-flat-button fxFlex='30' [ngClass]="{'disapplyBtn': disableConfirm == true, 'applyBtn': disableConfirm == false}" (click)="onConfirm()" [disabled] = "disableConfirm">
        <span i18n>Đồng ý</span>
    </button>
</div>