import {Injectable} from '@angular/core';
import {DeploymentService} from 'data/service/deployment/deployment.service';
import {EnvService} from "core/service/env.service";
import {HttpClient, HttpHeaders} from '@angular/common/http';
import {ApiProviderService} from 'core/service/api-provider.service';

@Injectable({
  providedIn: 'root'
})
export class FirebaseService {
  configs = this.deploymentService.getConfig();
  private storage = this.apiProviderService.getUrl('digo', 'storage');
  constructor(private deploymentService: DeploymentService,
              private envService: EnvService,
              private http: HttpClient,
              private apiProviderService: ApiProviderService) {}
  fireConfig() {
    if(!this.deploymentService.getConfig()?.fireBase){
      return this.envService.getConfig().fireBase;
    }
    return this.deploymentService.getConfig().fireBase;
  }

  remove(topic:string,id:string) {
    let headers = new HttpHeaders()
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    let URL = `${this.storage}/fcm/--remove-fcm-message?topic=${topic}`;
    URL += id ? `&id=${id}` : ``;
    this.http.delete(URL, {headers}).subscribe(rs => {
    });
  }

  send(topic:string,id:string,message:any){
    let headers = new HttpHeaders()
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    let URL = `${this.storage}/fcm/--notification?topic=${topic}`;
    URL += id ? `&id=${id}` : ``;
    this.http.post(URL,message, {headers}).subscribe(rs => {});
  }
}

export function FirebaseInit(appConfig: FirebaseService) {
  return appConfig.fireConfig();
}
