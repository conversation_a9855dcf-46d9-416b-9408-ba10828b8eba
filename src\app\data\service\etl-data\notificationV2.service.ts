import {Injectable} from '@angular/core';
import {ApiProviderService} from 'src/app/core/service/api-provider.service';
import {Observable, Subject} from 'rxjs';
import {HttpClient, HttpHeaders} from '@angular/common/http';

@Injectable({
  providedIn: 'root'
})
export class NotificationV2Service {
  private notificationV2URL = this.apiProviderService.getUrl('digo', 'notification') + '/notification-ws-service';
  private notificationV2WSURL = this.apiProviderService.getUrl('digo', 'notificationWS') + '/notification-ws-service';
  public notificationDataChangeSubject = new Subject<NotificationData[]>();

  private humanURL = this.apiProviderService.getUrl('digo', 'human');
  private remindWork473 = this.apiProviderService.getUrl('digo', 'padman') + '/remind-notify-473';

  constructor(
      private apiProviderService: ApiProviderService,
      private http: HttpClient,
  ) {
  }

  public getNotificationAgency(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.notificationV2URL + '/notification/agency?' + searchString, {headers});
  }

  public getNotificationCurrent(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.notificationV2URL + '/notification/current?' + searchString, {headers});
  }

  public getNotificationAssignee(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.notificationV2URL + '/notification/assignee?' + searchString, {headers});
  }

  public getNotificationRing(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.notificationV2URL + '/notification/ring?' + searchString, {headers});
  }

  public getDashboardUserRemind(userId: string): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.humanURL + '/user-remind-473/' + userId, {headers});
  }

  getReceptionCount(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.remindWork473 + '/reception/--count' + searchString, { headers });
  }

  getReceptionDetail(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.remindWork473 + '/reception/--detail' + searchString, { headers });
  }

  getProcessingCount(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.remindWork473 + '/processing/--count' + searchString, { headers });
  }

  getProcessingDetail(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.remindWork473 + '/processing/--detail' + searchString, { headers });
  }

  getCancelCount(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.remindWork473 + '/cancel/--count' + searchString, { headers });
  }

  getCancelDetail(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.remindWork473 + '/cancel/--detail' + searchString, { headers });
  }

  getResolvedCount(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.remindWork473 + '/resolved/--count' + searchString, { headers });
  }

  getResolvedDetail(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.remindWork473 + '/resolved/--detail' + searchString, { headers });
  }

}


export interface NotificationData {

  id: string;

  name: string;

  stage: number;

  count: number;
}
