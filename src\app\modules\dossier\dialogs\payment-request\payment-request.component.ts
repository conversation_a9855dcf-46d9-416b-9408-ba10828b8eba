import { HomeService } from './../../../../data/service/home/<USER>';
import { ProcessService } from 'src/app/data/service/process/process.service';
import { Component, OnInit, Inject, OnChanges, SimpleChanges } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { KeycloakService } from 'keycloak-angular';
import { DossierService } from 'src/app/data/service/dossier/dossier.service';
import { UserService } from 'src/app/data/service/user.service';
import * as ClassicEditor from '@ckeditor/ckeditor5-build-classic/build/ckeditor';
import '@ckeditor/ckeditor5-build-classic/build/translations/vi';
import { EnvService } from 'src/app/core/service/env.service';
import { SnackbarService } from 'src/app/data/service/snackbar/snackbar.service';
import { DatePipe } from '@angular/common';
import { NotificationService } from 'src/app/data/service/notification.service';
import { DeploymentService } from 'src/app/data/service/deployment.service';
import * as tUtils from 'src/app/data/service/thoai.service';
import { ProcedureService } from 'src/app/data/service/procedure/procedure.service';
import { take } from 'rxjs/operators';
import { FormControl, FormGroup } from '@angular/forms';
import { PadmanService } from 'src/app/data/service/svc-padman/padman.service';
import { AgencyService } from 'src/app/data/service/basedata/agency.service';
import { TagService } from 'src/app/data/service/basecat/tag.service';
import {MatTableDataSource} from "@angular/material/table";
import {MatCheckboxChange} from "@angular/material/checkbox";

import { CurrencyRateService } from 'src/app/data/service/currencyRate/currencyRate.service';
// import { MatTableDataSource } from '@angular/material/table';
@Component({
  selector: 'app-payment-request',
  templateUrl: './payment-request.component.html',
  styleUrls: ['./payment-request.component.scss']
})
export class PaymentRequestComponent implements OnInit {

  config = this.envService.getConfig();
  env: any = this.deploymentService.getAppDeployment()?.env;
  selectedLang = localStorage.getItem('language') || 'vi';
  selectedLangId = Number(localStorage.getItem('languageId')) || 228;
  dossierId: string;
  commentContent = '';
  descriptionContent = '';
  userName: string;
  accountId: string;
  dossierCode: string;
  isCKMaxlenght = false;
  dossierDetail: any;
  oldstatus = '';
  officers = [];
  agencyId: any;
  getApprovalAgency = '';
  currentTask: any;
  isPaymentDayLimit = this.deploymentService.env?.OS_HCM?.paymentDayLimit ? this.deploymentService.env.OS_HCM.paymentDayLimit : false;
  zaloPaymentReuirementNextTask = this.deploymentService.env.zaloPaymentReuirementNextTask
  paymentDayLimit = 0;
  uploadFileNames = [];
  userInfo = {
    id: '',
    fullname: ''
  };
  dueWaitPaidDay='';
  numberDate = 0;
  checkNumberDate=false;

  public Editor = ClassicEditor;
  commentConfig = {
    placeholder: 'Nhập lý do...',
    language: 'vi',
    removePlugins: ['MediaEmbed', 'ImageUpload'],
    toolbarLocation: 'bottom'
  };
  descriptionConfig = {
    placeholder: 'Nhập nội dung...',
    language: 'vi',
    removePlugins: ['MediaEmbed', 'ImageUpload'],
    toolbarLocation: 'bottom'
  };
  feeDisplayedColumns: string[] = ['totalUSDCost', 'totalVNDCost', 'timeExchange'];
  ExfeeDisplayedColumns: string[] = ['totalUSDCost', "exchangeRate", 'totalVNDCost', 'timeExchange'];
  FEE_EX_ELEMENTDATA: any[] = [];
  FEE_EX_ELEMENTDATA_SOURCE = new MatTableDataSource<any>([]);
  convertedCurrency : boolean = false;
  exratedCurrency : string = "";
  dossierTaskStatus = { id: '', name: [] };
  dossierMenuTaskRemind = { id: '', name: [] };

  // ================================================= Upload file
  uploaded: boolean;
  blankVal: any;
  uploadedImage = null;
  files = [];
  urls = [];
  fileNames = [];
  fileNamesFull = [];
  listProcedureProcess = [];
  tagPaymentMethod = null;
  listDossierFees = [];
  paymentMethodObj = null;
  totalCostNumber = 0;
  listfee = [];

  isFieldArrayInvalid = false;
  acceptFileExtension = this.config.acceptFileExtension;
  acceptFileType = this.config.acceptFileType;
  maxFileSize = this.env?.maxFileSize || this.config.maxFileSize;
  //Thu tuc co cau hinh dong bo BXD-LGSP Tan Tan
  isSyncDBNLGSPTanDanBXD = 0;
  statusName = "";
  fileTemplate = "";
  typeProcess = 1;
  checkNVTC = false;
  disableConfirm = true;


  updateForm = new FormGroup({
    numberPausePaidDay: new FormControl(0),  
  });
  checkConfirmPayment = false;
  showConfirmPayment = false;
  allowCurrencyConvert = false;
  showCheckConfirmPayment = this.deploymentService.env.OS_HCM.showCheckConfirmPayment;
  totalCost = '';
  totalCostValue: any;
  isSmsQNM = this.env?.OS_QNM?.isSmsQNM ? true : false;
  showRequireDirectPayment = this.deploymentService.env?.OS_HCM?.showRequireDirectPayment;
  dataFee: any[] = [];
  feeId: any;


  qbhAddInfoToFiles = this.deploymentService.env?.OS_QBH?.qbhAddInfoToFiles ? this.deploymentService.env?.OS_QBH?.qbhAddInfoToFiles : false;
  qbhTemplateYctt = this.deploymentService.env?.OS_QBH?.qbhTemplateYctt ? this.deploymentService.env?.OS_QBH?.qbhTemplateYctt : "";
  //start HGI IGATESUPP-97597
  hideReasonPay=this.deploymentService.getAppDeployment()?.hideReasonPay ? this.deploymentService.getAppDeployment()?.hideReasonPay : false;
  hideCurrencyConverter = this.deploymentService.getAppDeployment()?.hideCurrencyConverter || 0;
  showExchangeRate = this.deploymentService.getAppDeployment()?.showExchangeRate || 0;
  statusIdSyncNVTC=this.deploymentService.getAppDeployment()?.statusIdSyncNVTC ? this.deploymentService.getAppDeployment()?.statusIdSyncNVTC : "631e4a0967411b0b0d000003";
    //end HGI IGATESUPP-97597
  listPaymentMethodIdsToChange = this.deploymentService.getAppDeployment()?.listPaymentMethodIdsToChange ? this.deploymentService.getAppDeployment()?.listPaymentMethodIdsToChange : []
  enableConfigPaymentProcessingDossier  =  this.deploymentService.getAppDeployment()?.enableConfigPaymentProcessingDossier ? this.deploymentService.getAppDeployment()?.enableConfigPaymentProcessingDossier : false;
  deadlineForPaymentDossierNumberOfDays = this.deploymentService.getAppDeployment()?.deadlineForPaymentDossierNumberOfDays || 0;
  deadlineForPaymentDossierAgencyIds = this.deploymentService.getAppDeployment()?.deadlineForPaymentDossierAgencyIds || 0;
  deadlineForPaymentDossier = this.deploymentService.getAppDeployment()?.deadlineForPaymentDossier || 0;
  deadlineForPaymentDossierDueDate="";
  notiFeeFirstTime = !!this.deploymentService.getAppDeployment()?.notiFeeFirstTime ? this.deploymentService.getAppDeployment()?.notiFeeFirstTime : '0';//[QBH - iGate 2.0] Hỗ trợ bổ sung thông tin ghi chú đối với hồ sơ được yêu cầu thanh toán lần đầu tại trang DVC
  isSendFlagNotifeeQBH = false;
  updateRequireFieldForPayReDossier = this.deploymentService.getAppDeployment()?.updateRequireFieldForPayReDossier == 1 ? true : false;
  constructor(
    private userService: UserService,
    private envService: EnvService,
    private keycloakService: KeycloakService,
    public dialogRef: MatDialogRef<PaymentRequestComponent>,
    @Inject(MAT_DIALOG_DATA) public data: ConfirmPaymentRequestDialogModel,
    private snackbarService: SnackbarService,
    private datePipe: DatePipe,
    private notiService: NotificationService,
    private deploymentService: DeploymentService,
    private procedureService: ProcedureService,
    private dossierService: DossierService,
    private tagService: TagService,
    private processService: ProcessService,
    private homeService : HomeService,
    private padmanService: PadmanService,
    private agencyService: AgencyService,
    private currencyRate: CurrencyRateService,
  ) {
    this.dossierId = data.dossierId;
    this.dossierCode = data.dossierCode;
    this.typeProcess = data.typeProcess;
    if (this.typeProcess === 2){
      this.env.enableApprovalOfLeadership = 2;
    }
    if (!!data.checkNVTC && data.checkNVTC === true){
      this.checkNVTC = true;
    }
  }

  ngOnInit(): void {
    if(this.deadlineForPaymentDossier){
      let userAgency = JSON.parse(localStorage.getItem('userAgency'));
      var parentAgencyId = (userAgency.parent != null && userAgency.parent != undefined) ? userAgency.parent.id : null;
      if(!(this.deadlineForPaymentDossierAgencyIds.includes(parentAgencyId) || this.deadlineForPaymentDossierAgencyIds.includes(userAgency.id))){
        this.deadlineForPaymentDossier = 0;
        console.log("deadlineForPaymentDossierAgencyIds "+this.deadlineForPaymentDossierAgencyIds);
        console.log("deadlineForPaymentDossier = 0");
      }
    }
    console.log("this.deadlineForPaymentDossier "+this.deadlineForPaymentDossier);
    this.getUserAccount();
    this.getDossierTaskStatus();
    this.getDossierMenuTaskRemind();
    this.getDetailDossier();
    this.setEnvVariable();
    console.log("hideReasonPay: ",this.hideReasonPay);
    console.log(this.deploymentService);
  }

  setEnvVariable(){
    // tslint:disable-next-line:max-line-length
    this.fileTemplate = !!this.env?.fileTemplate?.requireAdditional ? this.env?.fileTemplate?.requireAdditional : this.config.requireAdditionalTemplateFile;
  }

  getUserAccount() {
    this.keycloakService.loadUserProfile().then(user => {
      // tslint:disable-next-line: no-string-literal
      this.accountId = user['attributes'].user_id[0];
      this.userService.getUserInfo(this.accountId).subscribe(data => {
        this.userName = data.fullname;
      });
    });
  }
  getDossierTaskStatus() {
    // tslint:disable-next-line:max-line-length
    // const tagId = this.env?.enableApprovalOfLeadership === 1 ? this.deploymentService.env.dossierTaskStatus.waitingForApprovalToAddition.id : this.deploymentService.env.dossierTaskStatus.requestForAdditionalDocuments.id;
    let tagId = '62e35794a106a86e839f765b';
    // tslint:disable-next-line:max-line-length
    if (!!this.deploymentService.env.dossierTaskStatus && !!this.deploymentService.env.dossierTaskStatus.paymentRequest && !!this.deploymentService.env.dossierTaskStatus.paymentRequest.id){
      tagId = this.deploymentService.env.dossierTaskStatus.paymentRequest.id;
    }
    this.dossierService.getAgencyTag(tagId).subscribe(rs => {
      this.dossierTaskStatus.id = rs.id;
      this.dossierTaskStatus.name = rs.trans;
    }, err => {
      console.log(err);
    });
  }
  getDossierMenuTaskRemind() {
    // tslint:disable-next-line:max-line-length
    // const tagId = this.env?.enableApprovalOfLeadership === 1 ? this.deploymentService.env.dossierMenuTaskRemind.waitingForApprovalToAddition.id : this.deploymentService.env.dossierMenuTaskRemind.requestForAdditional.id;
    let tagId = '62e359c1a106a86e839f7667';
    // tslint:disable-next-line:max-line-length
    if (!!this.deploymentService.env.dossierMenuTaskRemind && !!this.deploymentService.env.dossierMenuTaskRemind.paymentRequest && !!this.deploymentService.env.dossierMenuTaskRemind.paymentRequest.id){
      tagId = this.deploymentService.env.dossierMenuTaskRemind.paymentRequest.id;
    }
    this.dossierService.getAgencyTag(tagId).subscribe(rs => {
      this.dossierMenuTaskRemind.id = rs.id;
      this.dossierMenuTaskRemind.name = rs.trans;
      this.statusName = rs?.trans.filter(t => t.languageId === Number(this.selectedLangId))?.name;
    }, err => {
      console.log(err);
    });
  }

  async getDetailDossier() {
    if(this.hideCurrencyConverter == 1)
    {
      let data = await  this.dossierService.getDossierDetail(this.dossierId).toPromise();
      let procedureDate = await  this.procedureService.getProcedureDetail(data?.procedure?.id).toPromise();
      if(procedureDate && !!procedureDate?.extendHCM?.allowCurrencyConvert)
      {
          if(procedureDate?.extendHCM?.allowCurrencyConvert == true)
          {
            let res = await this.currencyRate.getDetailCurrencyRateCode("USD").toPromise();
            await this.getDossierFeeNotify(parseFloat(res.transfer.replace(",", ".")) * 1000);
          }else
          {
            this.disableConfirm = false;
            await this.getDossierFeeNotify(1.0);
          }
      }else
      {
        this.disableConfirm = false;
        await this.getDossierFeeNotify(1.0);
      }
    }else
    {
      this.disableConfirm = false;
      await this.getDossierFeeNotify(1.0);
    }
    this.dossierService.getDossierDetail(this.dossierId).subscribe(async data => {
      //IGATESUPP-123178
      if(this.notiFeeFirstTime) {
        if(data?.extendQBH?.notiFeeFirstTime === undefined) {
          this.isSendFlagNotifeeQBH = true;
        }
      }
      console.log("this.isSendFlagNotifeeQBH" + this.isSendFlagNotifeeQBH);
      this.dossierDetail = data;
      this.getProcedureDetail(data?.procedure?.id);
      this.agencyId = !!data?.agency?.id ? data?.agency?.id : data?.agency?.parent?.id;
      if (!!data.currentTask && data.currentTask.length > 0){
        this.currentTask = data.currentTask[0];
      }
      const agencies = this.agencyService.getAgencies();
      const extend = {
        dossier:{
          id: this.dossierId,
          code: this.dossierCode,
        },
        agencies: agencies
      };
      console.log("extendHCM deadlineForPaymentDossierDueDate: "+data?.extendHCM?.paymentRequest?.deadlineForPaymentDossierDueDate);
      this.notiService.checkSendSubject.next(
        {
          id: data?.procedureProcessDefinition?.id,
          phone: data?.applicant?.data?.phoneNumber,
          email: data?.applicant?.data?.email,
          currentTask: this.currentTask,
          contentParams: {
            parameters: {
              sector: !!data?.procedure?.sector?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name ? data?.procedure?.sector?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name : '',
              procedure: !!data?.procedure?.translate?.find(agency => agency.languageId === Number(this.selectedLangId))?.name ? data?.procedure?.translate?.find(agency => agency.languageId === Number(this.selectedLangId))?.name : '',
              agency: !!data?.agency?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name ? data?.agency?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name : '',
              agencyId: !!data?.agency?.id ? data?.agency?.id : data?.agency?.parent?.id,
              extend: this.isSmsQNM ? extend : {},
              applicantFullname: data?.applicant?.data?.fullname,
              officerFullname: '',
              subsystem: !!this.env?.subsystem?.name ? this.env?.subsystem?.name : "Hệ thống Một cửa điện tử",
              dossierCode: data?.code,
              nextTask: this.zaloPaymentReuirementNextTask?.enable?this.zaloPaymentReuirementNextTask?.nextTask:(!!this.env?.notify?.additionalRequirement?.nextTask ? this.env?.notify?.additionalRequirement?.nextTask : 'Yêu cầu bổ sung hồ sơ'),
              // IGATESUPP-63184
              nextStatus: !!this.env?.notify?.feeRequirement?.nextStatus ? this.env?.notify?.feeRequirement?.nextStatus : (data?.dossierTaskStatus?.name ?? ''), // cho bo sung, trang thai hien tai cua ho so
              dossierStatus: !!this.env?.notify?.feeRequirement?.dossierStatus ? this.env?.notify?.feeRequirement?.dossierStatus : (data?.dossierStatus?.name?.find(t => t.languageId === Number(this.selectedLangId))?.name ?? ''), // cho bo sung, trang thai hien tai cua ho so
              dossierSearchPadsvcUrl: (!!this.env?.domain?.padsvc ? this.env?.domain?.padsvc : "http://padsvctest.vncitizens.vn/vi/") + "dossier/public",
              dossierDetailPadsvcUrl: (!!this.env?.domain?.padsvc ? this.env?.domain?.padsvc : "http://padsvctest.vncitizens.vn/vi/") + "dossier/detail/" + this.dossierId,
              dossierSearchUrl: (!!this.env?.domain?.onegate ? this.env?.domain?.onegate : "http://padsvctest.vncitizens.vn/vi/") + "dossier/search/",
              dossierDetailUrl: (!!this.env?.domain?.onegate ? this.env?.domain?.onegate : "http://padsvctest.vncitizens.vn/vi/") + "dossier/search/" + this.dossierId,
              dossierPayDetailPadsvcUrl: (!!this.env?.domain?.onegate ? this.env?.domain?.onegate : "http://padsvctest.vncitizens.vn/vi/") + "dossier/payment/" + this.dossierId,
              returnMethod: !!data?.dossierReceivingKind?.name?.find(recieve => recieve.languageId === Number(this.selectedLangId))?.name ? data?.dossierReceivingKind?.name?.find(recieve => recieve.languageId === Number(this.selectedLangId))?.name : '',
              receivingDate: '',
              appointmentDate: '',
              dossierFee: this.totalCost,
              reason: '',
              deadlineForPaymentDossierDueDate: !!data?.extendHCM?.paymentRequest?.deadlineForPaymentDossierDueDate ? this.datePipe.transform(data?.extendHCM?.paymentRequest?.deadlineForPaymentDossierDueDate, 'dd/MM/yyyy HH:mm:ss') : !!this.deadlineForPaymentDossierDueDate ? this.datePipe.transform(this.deadlineForPaymentDossierDueDate, 'dd/MM/yyyy HH:mm:ss') : "", //IGATESUPP-117667 Q11 -P7: Đề nghị thêm chức năng báo Quá hạn thanh toán hồ sơ
            }
          }
        }
      );
      if (this.dossierDetail.dossierTaskStatus === undefined && this.dossierDetail.dossierTaskStatus === null) {
        // this.putDossierStatus();
      }
      else {
        this.oldstatus = this.dossierDetail.dossierTaskStatus.name;
      }
    });
  }

  getDossierFeeNotify(transfer) {
    // //debugger
    this.dataFee = [];
    return new Promise<void>(resolve => {
      this.padmanService.getDossierFee(this.dossierId).toPromise().then(res => {
        console.log(res);
        res.forEach(x=>{
          const item = {
            id: x?.id,
            typeFee: x?.procost?.type?.name.find(z=>z.languageId == this.selectedLangId).name,
            quantity: x?.quantity,
            unitPrice: x?.amount,
            totalPriceItem: x?.quantity * x?.amount,
            monetaryUnit: res[0]?.monetaryUnit ? res[0].monetaryUnit : 'VNĐ',
            description: x?.procost?.description.find(z=>z.languageId == this.selectedLangId).name,
            file: {
              id: x?.file?.id,
              filename: x?.file?.filename,
              size:x?.file?.size
            }
          }
          this.dataFee.push(item);
        })
        if(res && res.length > 0)
        {
          let totalCostValue = 0;
          let totalPaidValue = 0;
          res.forEach(element => {
            totalCostValue += element.quantity * element.amount;
            totalPaidValue += element.paid;
          });
          const monetaryUnit = res[0]?.monetaryUnit ? res[0].monetaryUnit : 'VND';
          let total = totalCostValue - totalPaidValue;
          this.totalCostValue = total;
          //this.totalCost = total >= 0 ? Number((total).toFixed(1)).toLocaleString() + ' ' + monetaryUnit : '';
          if(this.checkNVTC && total <=0){
            const msgObj = {
              vi: 'Không có phí thanh toán!',
              en: 'Payment request failed!'
            };
            this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
            this.disableConfirm = true;
          }
          //this.totalCost = total >= 0 ? Number((total * transfer).toFixed(1)).toLocaleString() + ' ' + monetaryUnit : '';
          this.totalCost =  total >=0 ? this.showExchangeRate == 1 ?  Math.round(total * transfer).toLocaleString('vi-VN')  +  ' ' + monetaryUnit :  (total * transfer).toFixed(0).toLocaleString() +  ' ' + monetaryUnit : "";
        }
        else{
          if(this.checkNVTC){
            const msgObj = {
              vi: 'Không có phí thanh toán!',
              en: 'Payment request failed!'
            };
            this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
            this.disableConfirm = true;
          }
        }
        resolve();
      }).catch(err => {
        resolve();
      });
    });
  }

  getProcedureDetail(procedureId) {
    this.procedureService.getProcedureDetail(procedureId).subscribe(async data => {
      this.isSyncDBNLGSPTanDanBXD = data?.isSyncDBNLGSPTanDanBXD ? Number(data?.isSyncDBNLGSPTanDanBXD) : 0;
      if(data && !!data.isConfirmPayment) {
        this.checkConfirmPayment = true;
      }
      if(data && !!data.isCheckConfirmPayment) {
        this.showConfirmPayment = true;
      }
      if(data && !!data?.extendHCM?.paymentDayLimit){
        this.paymentDayLimit = data.extendHCM.paymentDayLimit;
        
        if(this.isPaymentDayLimit && this.paymentDayLimit > 0){
          this.updateForm.disable();
        }
      }
      if(this.hideCurrencyConverter == 1)
      {
         if(data && !!data?.extendHCM?.allowCurrencyConvert)
         {
              this.allowCurrencyConvert = data?.extendHCM?.allowCurrencyConvert; 
              if(this.allowCurrencyConvert == true)
              {
                this.padmanService.getDossierFee(this.dossierId).subscribe(dataFee => {

                  this.currencyRate.getDetailCurrencyRateCode("USD").subscribe(res => {
                    console.log(res.toString());
                    let rateTransfer : string  = res.transfer;
                    rateTransfer = rateTransfer.replace(",", ".")
                    let totalCostValue : number = 0;
                    let totalPaidValue : number = 0;
                    // code lấy tổng phí VND, tổng thủ tục USD, và thời gian quy đổi
                    try{
                      dataFee.forEach(element => {
                        if (element.quantity === undefined) 
                        {
                           element.quantity = 1;
                        }
                        totalCostValue += element.quantity * element.amount;
                        totalPaidValue += element.paid;
                      });
                      let total = totalCostValue - totalPaidValue;
                      let totalUSDCost = total >=0 ? (total).toLocaleString() : "";
                      let totalVNDCost = total >=0 ? this.showExchangeRate == 1 ?  Math.round(Number((total * parseFloat(rateTransfer) * 1000))).toLocaleString('vi-VN') :  (Number(total) * parseFloat(rateTransfer) * 1000).toFixed(0).toLocaleString() : "";
                      let timeExchange = new Date().toLocaleDateString() + " " + new Date().toLocaleTimeString()
                      let element = {totalUSDCost : "", totalVNDCost : "", timeExchange : "", exratedCurrency : ""};
                      element.totalUSDCost = totalUSDCost;
                      element.totalVNDCost = totalVNDCost;
                      element.timeExchange = timeExchange;
                      element.exratedCurrency = Number(parseFloat(rateTransfer) * 1000).toLocaleString('vi-VN');
                      this.FEE_EX_ELEMENTDATA.push(element);
                      this.FEE_EX_ELEMENTDATA_SOURCE = new MatTableDataSource(this.FEE_EX_ELEMENTDATA);
                      //this.totalCost = totalVNDCost + " VND"; // totalCost là phí hồ sơ được quy định luôn có đơn vị là VNĐ
                      console.log(this.FEE_EX_ELEMENTDATA);
                      this.convertedCurrency = true;
                      this.exratedCurrency = parseFloat(rateTransfer).toString();
                      this.disableConfirm = false;
                    }catch(e){
                         console.log("Lỗi quá trình quy đổi phí hồ sơ: " +  e.toString());
                         this.convertedCurrency = false;
                    }
                
                }, err => {
                    console.log(err);
                    this.convertedCurrency = false;
                  });
                           
                }, err => {
                  console.log(err);
                  this.convertedCurrency = false;
                }); 
                    
              }
         }
      }
    }, err => {
      console.log(err);
    });
  }

  updateConvertedCurrency()
  {
     this.dossierService.updateConvertedCurrency(this.dossierId,this.convertedCurrency).subscribe(async (res) =>{
              if(res.affectedRows  == 1){
                   console.log("Updated converted currency successfully");
              }else{
                   console.log("Update converted currency failed "); 
              }
     } ,err => {
      console.log(err);
    });
  }
  updateExrateCurrencyDossierFee()
  {
     this.padmanService.updateExrateCurrencyDossierFee(this.dossierId,this.exratedCurrency).subscribe(async (res) =>{
              if(res.affectedRows  == 1){
                   console.log("Updated converted currency dossier fee successfully");
              }else{
                   console.log("Update converted currency dossier fee failed "); 
              }
     } ,err => {
      console.log(err);
    });
  }
  putDossierStatus() {
    const requestBodyObj = [
      {
        id: this.dossierId,
        dossierStatus: 2,
        dossierTaskStatus: null
      }
    ];
    const requestBody = JSON.stringify(requestBodyObj, null, 2);
    this.dossierService.putDossierStatus(requestBody).subscribe(data => {
      if (data.affectedRows === 1) {
        this.getDetailDossier();
      }
    });
  }

  postHistory(fieldName?:string, newVal?:string, originalVal?:string, msgRequire?:string) {
    let content = null;
    if(newVal){
      content = {
        groupId: 1,
        itemId: this.dossierId,
        user: {
          id: this.accountId,
          name: this.userName
        },
        type: 3,
        deploymentId: this.config.deploymentId,
        action: [
          {
            fieldNameRbk: fieldName,
            originalValue: originalVal,
            newValue: newVal,
            additionalRequire : {
              dateAdditionalRequire : msgRequire,
              file: this.uploadFileNames
            }
          }
        ]
      };
    }else{
      let newStatus = '';
      if (this.dossierTaskStatus.name.length > 0) {
        newStatus = this.dossierTaskStatus.name[0].name;
        this.dossierTaskStatus.name.forEach(element => {
          if (element.languageId === Number(localStorage.getItem('languageId'))) {
            newStatus = element.name;
          }
        });
      }
      content = {
        groupId: 1,
        itemId: this.dossierId,
        user: {
          id: this.accountId,
          name: this.userName
        },
        type: 3,
        deploymentId: this.config.deploymentId,
        action: [
          {
            fieldNameRbk: 'lang.word.status',
            originalValue: this.oldstatus,
            newValue: newStatus
          }
        ]
      };
    }
    this.dossierService.postHistory(JSON.stringify(content, null, 2)).subscribe(data => {
    });
  }

  GetListUserByPermission(agency) {
    return new Promise<void>((resolve) => {
      this.processService.getApprovalAgency(agency.id).pipe(take(1)).subscribe(async data => {
        console.log('data', data);
        if (!!data && data.content.length !== 0) {
          this.getApprovalAgency = data.content[0].approvalAgency.id;
        }
        else{
          if (!!agency.parent.id){
            await this.GetListUserByPermissionParent(agency.parent.id);
          }
        }
        if (this.getApprovalAgency == '') {
          this.getApprovalAgency = agency.id;
        }
        let permission = "additionalRequirementDossierApproval";
        this.processService.getListUserByPermissionCode(this.getApprovalAgency, permission).pipe(take(1)).subscribe(data2 => {
          this.officers = data2;
          resolve();
        }, (err) => {
          resolve();
        });
      });
    });
  }

  GetListUserByPermissionParent(agencyId) {
    return new Promise<void>((resolve) => {
      this.processService.getApprovalAgency(agencyId).pipe(take(1)).subscribe(data => {
        console.log('data', data);
        if (!!data && data.content.length !== 0) {
          this.getApprovalAgency = data.content[0].approvalAgency.id;
        }
        // if (this.getApprovalAgency == '') {
        //   this.getApprovalAgency = agencyId;
        // }
        resolve();
      }, (err) => {
        resolve();
      });
    });
  }

  async checkPaymentDayLimit(){
    this.keycloakService.loadUserProfile().then(user => {
      if(user['attributes']){
        this.userInfo = {
          id: user['attributes'].user_id[0],
          fullname: user['attributes'].fullname[0]
        };
      }
    });
    let now = new Date().toISOString();
    let currentDate = now.slice(0,23) + 'Z';
    let requestTime = [
      {
        timesheet: {
          id: this.deploymentService.env?.OS_HCM?.timesheetIdPaymentDayLimit ? this.deploymentService.env.OS_HCM.timesheetIdPaymentDayLimit : "628866972afeb001f1e3c575"
        },
        dossier: {
          id: this.dossierId
        },
        duration: this.paymentDayLimit,
        startDate: currentDate,
        endDate: '',
        checkOffDay: true,
        processingTimeUnit: "d"
      }
    ];

    await this.getPostTimeSheet(requestTime, true);
  }

  addDaysToCurrentDate(numberOfDays: number): Date {
    const currentDate = new Date();
    currentDate.setDate(currentDate.getDate() + numberOfDays);
    return currentDate;
  }

  async getPostTimeSheet(requestTime, convertISOString=false){
    return new Promise<void>(resolve => {
      this.dossierService.postTimesheet(requestTime).toPromise().then(dt => {
        this.dueWaitPaidDay = dt[0].due;
        this.dueWaitPaidDay = this.dueWaitPaidDay.slice(0, 23) + 'Z';
        if(convertISOString == true){
          this.dueWaitPaidDay = this.convertDateTimeToIso(dt[0].due);
        }
        resolve();
      }).catch(err => {
        resolve();
      });
    });
  }

  convertDateTimeToIso(dateTimeString: string): string {
    // Parse the date string, assuming the format is yyyy-mm-ddThh:mi:ss+0700
    const parts = dateTimeString.split(/[:+.T-]/);
    const year = parseInt(parts[0], 10);
    const month = parseInt(parts[1], 10) - 1; // Month is 0-indexed
    const day = parseInt(parts[2], 10);
    const hours = parseInt(parts[3], 10);
    const minutes = parseInt(parts[4], 10);
    const seconds = parseInt(parts[5], 10);

    // Create a new Date object considering the timezone offset (+0700)
    const date = new Date(year, month, day, hours, minutes, seconds);

    // Convert the date to ISO format
    return date.toISOString();
  }

  async onConfirm() {
    if(this.deploymentService.env?.OS_HCM?.enableCheckStatusRequestPayment){
      const data = await this.dossierService.getDossierDetail(this.dossierId).toPromise();
        if((data?.dossierStatus?.id != 2 && data?.dossierStatus?.id != 4 && data?.dossierStatus?.id != 0 && data?.dossierStatus?.id != 21) || (tUtils.nonNull(data, 'currentTask') && data?.currentTask.length == 0 && tUtils.nonNull(data, 'task') && data?.task[data?.task.length - 1].isLast == 1)){
          const msgObj = {
            vi: 'Không đủ điều kiện yêu cầu thanh toán !!!',
            en: 'Please not eligible to request payment !!!'
          };
          this.snackbarService.openSnackBar(2, '', msgObj[this.selectedLang], 'warning_notification', this.config.expiredTime);
          return;
        }
    }
     if(!this.hideReasonPay){
      if (this.commentContent.trim() === '') {
        const msgObj = {
          vi: 'Vui lòng nhập ý kiến xử lý!',
          en: 'Please enter a handling comments!'
        };
        this.snackbarService.openSnackBar(2, '', msgObj[this.selectedLang], 'warning_notification', this.config.expiredTime);
      }
     }
    const formObj = this.updateForm.getRawValue();
    let check = true;
    if (formObj.numberPausePaidDay === '' && !!!formObj.numberPausePaidDay && this.dossierDetail.dossierStatus.id !== 0){
      check = false;
      const msgObj = {
        vi: 'Vui lòng nhập số ngày tạm dừng thanh toán!',
        en: 'Please enter the number of pause pay days!'
      };
      this.snackbarService.openSnackBar(2, '', msgObj[this.selectedLang], 'warning_notification', this.config.expiredTime);
    }
    //Hiện thông báo khi bật tham số và chưa check đồng ý
    if(this.showCheckConfirmPayment && !this.checkConfirmPayment && this.showConfirmPayment)
    {
      const msgObj = {
        vi: 'Vui lòng đồng ý để xác nhận số tiền yêu cầu thanh toán!',
        en: 'Please agree to confirm the amount requested for payment!'
      };
      this.snackbarService.openSnackBar(2, '', msgObj[this.selectedLang], 'warning_notification', this.config.expiredTime);
      return;
    }

    // update bắt buộc thanh toán cho phí > 0
    if (this.updateRequireFieldForPayReDossier) {
      this.dossierService.updateRequiredFee(this.dossierId).subscribe();
      await this.dossierService.updateRequiredFee(this.dossierId).toPromise();
    }
    
    if (this.commentContent.trim() !== '' && check) {
      let dosStatus = 14;
      if (this.dossierDetail.dossierStatus.id !== 0 && this.dossierDetail.dossierStatus.id !==21){
        dosStatus = 17;
      }
      const requestBodyObj: any = {
        // dossierStatus: this.env?.enableApprovalOfLeadership == 1 ? 8 : 1,
        dossierStatus: dosStatus,
        comment: '',
        description: '',
        numberPausePaidDay: formObj.numberPausePaidDay,
        dossierTaskStatus: this.dossierTaskStatus,
        dossierMenuTaskRemind: this.dossierMenuTaskRemind
      };
      
      if(this.userInfo.id == ""){
        this.userInfo.id = localStorage.getItem('tempUID');
        this.userInfo.fullname = localStorage.getItem('tempUsername');
      }
      if(this.isPaymentDayLimit && dosStatus == 14){
        await this.checkPaymentDayLimit();
        let currentDate = null;
        console.log("this.numberDate "+this.numberDate);
        if(this.deadlineForPaymentDossier && this.numberDate > 0){
          let addDate = this.addDaysToCurrentDate(this.numberDate).toISOString();
          currentDate = addDate.slice(0,23) + 'Z';
          this.deadlineForPaymentDossierDueDate = currentDate;
        }
        requestBodyObj.extendHCM = {
          paymentRequest:{
            userRequest: this.userInfo,
            numberWaitPaidDay: this.paymentDayLimit,
            dateWaitPaidDay: this.dueWaitPaidDay,
            sendSmsSuccess: false,
            deadlineForPaymentDossier:this.numberDate,
            deadlineForPaymentDossierDueDate: currentDate
          }
        };
        if(this.deadlineForPaymentDossier && this.numberDate > 0){
          const msgObj = {
            vi: 'Cán bộ: '+localStorage.getItem('tempUsername')+'<br>Số ngày chờ thanh toán: '+this.numberDate+'<br>Hạn thanh toán hồ sơ: '+this.datePipe.transform(currentDate, 'dd/MM/yyyy HH:mm:ss')+'<br>Yêu cầu thanh toán hồ sơ <b>' + this.dossierCode + '</b>: Đề nghị Ông/Bà thanh toán lệ phí hồ sơ',
            en: 'Officer: '+localStorage.getItem('tempUsername')+'<br>Number of days waiting for payment: '+this.numberDate+'<br>Application deadline: '+this.datePipe.transform(currentDate, 'dd/MM/yyyy HH:mm:ss')+'<br>Request for payment of dossier <b>' + this.dossierCode + '</b>: Please pay the application fee.'
          };
          this.postComment(msgObj[this.selectedLang]);
          this.postHistory('lang.word.status', this.dossierMenuTaskRemind?.name[0]?.name, this.oldstatus, msgObj[this.selectedLang]);
        }
      }else{
        let currentDate = null;
        if(this.deadlineForPaymentDossier && this.numberDate > 0){
          let addDate = this.addDaysToCurrentDate(this.numberDate).toISOString();
          currentDate = addDate.slice(0,23) + 'Z';
          this.deadlineForPaymentDossierDueDate = currentDate;
        }
        requestBodyObj.extendHCM = {
          paymentRequest:{
            userRequest: this.userInfo,
            numberWaitPaidDay: this.paymentDayLimit,
            dateWaitPaidDay: this.dueWaitPaidDay,
            sendSmsSuccess: false,
            deadlineForPaymentDossier:this.numberDate,
            deadlineForPaymentDossierDueDate: currentDate
          }
        }
        if(this.deadlineForPaymentDossier && this.numberDate > 0){
          const msgObj = {
            vi: 'Cán bộ: '+localStorage.getItem('tempUsername')+'<br>Số ngày chờ thanh toán: '+this.numberDate+'<br>Hạn thanh toán hồ sơ: '+this.datePipe.transform(currentDate, 'dd/MM/yyyy HH:mm:ss')+'<br>Yêu cầu thanh toán hồ sơ <b>' + this.dossierCode + '</b>: Đề nghị Ông/Bà thanh toán lệ phí hồ sơ',
            en: 'Officer: '+localStorage.getItem('tempUsername')+'<br>Number of days waiting for payment: '+this.numberDate+'<br>Application deadline: '+this.datePipe.transform(currentDate, 'dd/MM/yyyy HH:mm:ss')+'<br>Request for payment of dossier <b>' + this.dossierCode + '</b>: Please pay the application fee.'
          };
          this.postComment(msgObj[this.selectedLang]);
          this.postHistory('lang.word.status', this.dossierMenuTaskRemind?.name[0]?.name, this.oldstatus, msgObj[this.selectedLang]);
        }
      }
      if(!this.deadlineForPaymentDossier){
        if (this.commentContent.trim() !== '') {
          this.postComment(this.commentContent.trim(), this.descriptionContent.trim());
          requestBodyObj.comment = this.commentContent.trim();
          requestBodyObj.description = this.descriptionContent.trim();
        } else {
          const msgObj = {
            vi: 'Yêu cầu thanh toán hồ sơ <b>' + this.dossierCode + '</b>',
            en: 'Request for payment of dossier <b>' + this.dossierCode + '</b>!'
          };
          this.postComment(msgObj[this.selectedLang]);
          requestBodyObj.comment = msgObj[this.selectedLang];
        }
      }
      if(this.checkNVTC){
        requestBodyObj.dossierSyncStatus = {
          id: this.statusIdSyncNVTC,
          name: [{
            languageId: "228",
            name: "Nghĩa vụ tài chính"
          }]
        }
      }
      if(this.enableConfigPaymentProcessingDossier == true && this.checkNVTC == false){
        requestBodyObj.isConfigPaymentProcessingDossier = 1;
      }
      if(this.notiFeeFirstTime && this.isSendFlagNotifeeQBH) {
        requestBodyObj.extendQBH = {
          notiFeeFirstTime: true
        }
      }
      const requestBody = JSON.stringify(requestBodyObj, null, 2);
      this.dossierService.putDossierStatusWithComment(this.dossierId, requestBody).subscribe(data => {
        if (data.affectedRows === 1) {
          const newDate = tUtils.newDate();
          if(!this.deadlineForPaymentDossier){
            this.postHistory();
          }
          if (this.config.receivePromotionalProcedureUpdated === 1 || this.config.receivePromotionalProcedureUpdated === '1') {
            this.dossierService.getDossierDetail(this.dossierId).subscribe(data => {
              if (data.sync !== undefined) {
                const dataRequest: any = {};
                const agency = JSON.parse(localStorage.getItem('userAgency'));
                if (agency !== null && agency !== undefined) {
                  dataRequest.agencyId = agency.id;
                } else {
                  dataRequest.agencyId = this.config.rootAgency.id;
                }

                if (this.config.subsystemId !== null && this.config.subsystemId !== undefined) {
                  dataRequest.subsystemId = this.config.subsystemId;
                }

                dataRequest.agencyCode = data.agency.id;
                dataRequest.status = 5;
                dataRequest.code = data.code;
                dataRequest.sourceCode = data.sync.sourceCode;
                dataRequest.comment = this.commentContent.trim();
                dataRequest.date = this.datePipe.transform(newDate, 'yyyyMMddHHmmss');
                this.dossierService.postSynchronizePromotionStatus(dataRequest).subscribe(data => {
                });
              }
            });
          }
          this.notiService.changeSendSubject.next(
            {
              id: this.dossierDetail?.procedureProcessDefinition?.id,
              phone:  this.dossierDetail?.applicant?.data?.phoneNumber,
              email:  this.dossierDetail?.applicant?.data?.email,
              currentTask: this.currentTask,
              renewContent:true, 
              updateSend: false ,
              contentParams: {
                parameters: {
                  sector: !! this.dossierDetail?.procedure?.sector?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name ?  this.dossierDetail?.procedure?.sector?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name : '',
                  procedure: !! this.dossierDetail?.procedure?.translate?.find(agency => agency.languageId === Number(this.selectedLangId))?.name ?  this.dossierDetail?.procedure?.translate?.find(agency => agency.languageId === Number(this.selectedLangId))?.name : '',
                  agency: !! this.dossierDetail?.agency?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name ?  this.dossierDetail?.agency?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name : this.dossierDetail?.agency?.parent?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name?this.dossierDetail?.agency?.parent?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name: "",
                  agencyId: !! this.dossierDetail?.agency?.id ? data?.agency?.id : data?.agency?.parent?.id,
                  extend: this.isSmsQNM ?  this.dossierDetail.extend : {},
                  applicantFullname:  this.dossierDetail?.applicant?.data?.fullname,
                  officerFullname: '',
                  subsystem: !!this.env?.subsystem?.name ? this.env?.subsystem?.name : "Hệ thống Một cửa điện tử",
                  dossierCode:  this.dossierDetail?.code,
                  nextTask: this.zaloPaymentReuirementNextTask?.enable?this.zaloPaymentReuirementNextTask?.nextTask:(!!this.env?.notify?.additionalRequirement?.nextTask ? this.env?.notify?.additionalRequirement?.nextTask : 'Yêu cầu bổ sung hồ sơ'),
                  // IGATESUPP-63184
                  nextStatus: !!this.env?.notify?.feeRequirement?.nextStatus ? this.env?.notify?.feeRequirement?.nextStatus : ( this.dossierDetail?.dossierTaskStatus?.name ?? ''), // cho bo sung, trang thai hien tai cua ho so
                  dossierStatus: !!this.env?.notify?.feeRequirement?.dossierStatus ? this.env?.notify?.feeRequirement?.dossierStatus : ( this.dossierDetail?.dossierStatus?.name?.find(t => t.languageId === Number(this.selectedLangId))?.name ?? ''), // cho bo sung, trang thai hien tai cua ho so
                  dossierSearchPadsvcUrl: (!!this.env?.domain?.padsvc ? this.env?.domain?.padsvc : "http://padsvctest.vncitizens.vn/vi/") + "dossier/public",
                  dossierDetailPadsvcUrl: (!!this.env?.domain?.padsvc ? this.env?.domain?.padsvc : "http://padsvctest.vncitizens.vn/vi/") + "dossier/detail/" + this.dossierId,
                  dossierSearchUrl: (!!this.env?.domain?.onegate ? this.env?.domain?.onegate : "http://padsvctest.vncitizens.vn/vi/") + "dossier/search/",
                  dossierDetailUrl: (!!this.env?.domain?.onegate ? this.env?.domain?.onegate : "http://padsvctest.vncitizens.vn/vi/") + "dossier/search/" + this.dossierId,
                  dossierPayDetailPadsvcUrl: (!!this.env?.domain?.onegate ? this.env?.domain?.onegate : "http://padsvctest.vncitizens.vn/vi/") + "dossier/payment/" + this.dossierId,
                  returnMethod: !! this.dossierDetail?.dossierReceivingKind?.name?.find(recieve => recieve.languageId === Number(this.selectedLangId))?.name ?  this.dossierDetail?.dossierReceivingKind?.name?.find(recieve => recieve.languageId === Number(this.selectedLangId))?.name : '',
                  receivingDate: '',
                  appointmentDate: '',
                  dossierFee: this.totalCost,
                  reason: this.commentContent.trim(),
                  deadlineForPaymentDossierDueDate: !!this.dossierDetail?.extendHCM?.paymentRequest?.deadlineForPaymentDossierDueDate ? this.datePipe.transform(data?.extendHCM?.paymentRequest?.deadlineForPaymentDossierDueDate, 'dd/MM/yyyy HH:mm:ss') : !!this.deadlineForPaymentDossierDueDate ? this.datePipe.transform(this.deadlineForPaymentDossierDueDate, 'dd/MM/yyyy HH:mm:ss') : "", //IGATESUPP-117667 Q11 -P7: Đề nghị thêm chức năng báo Quá hạn thanh toán hồ sơ
                }
              }
            }
          );
          setTimeout(
            ()=>{ this.notiService.confirmSendSubject.next({
              confirm: true,
              renewContent: true
            })}, 1000
          )
         

          // if (this.env?.enableApprovalOfLeadership == 1){
          //   const data = {
          //     type: 8,
          //     date: newDate,
          //     attachment: this.uploadedImage
          //   }
          //   this.dossierService.updateApprovalData(this.dossierId, data).subscribe(res => {});
          // }
          if(this.env?.enableApprovalOfLeadership === 0 && this.isSyncDBNLGSPTanDanBXD === 1){
            this.syncPostReceiveInforFileFromLocal();
          }

          if (this.deploymentService?.env?.OS_BTTTT?.isEnabledNotify) {
            // IGATESUPP-62366 thong bao cho nguoi dan
            this.dossierService.noticeDossier(this.dossierId, {comment: 'Lý do:&nbsp;' + this.commentContent.trim()}).subscribe(res => {});
          }

          //đổi hình thức thanh toán IGATESUPP-71172
          if(this.showRequireDirectPayment.enable === true && (this.dossierDetail?.applyMethod?.id == 1 || !this.dossierDetail?.paymentMethod?.id || this.listPaymentMethodIdsToChange.includes(this.dossierDetail?.paymentMethod?.id ))){
            // let userAgency = JSON.parse(localStorage.getItem('userAgency'));
            // var parentAgencyId = (userAgency.parent != null && userAgency.parent != undefined) ? userAgency.parent.id : null;
            // if(!!parentAgencyId && this.showRequireDirectPayment.agency.includes(parentAgencyId)){
              let tagId = this.showRequireDirectPayment?.tagHinhThucThanhToanPayment ? this.showRequireDirectPayment?.tagHinhThucThanhToanPayment : '62f5d79b5c424b277f174318';
              this.tagService.getFullyDetails(tagId).subscribe(tag => {
                this.tagPaymentMethod = tag;
                if (this.tagPaymentMethod) {
                  this.paymentMethodObj = {
                    id: this.tagPaymentMethod.id,
                    name: this.tagPaymentMethod.trans,
                    code: this.tagPaymentMethod.code
                  };
                  this.dossierService.getDossierPayment('?page=0&size=50&spec=page&status=0&dossier-id=' + this.dossierId).subscribe(dt => {
                    this.padmanService.getDossierFee(this.dossierId).subscribe(res => {
                      if(res.length > 0){
                        this.listDossierFees = res;
                        this.listfee = [];
                        for(var i = 0; i < res.length; i++){
                          // console.log(this.listDossierFees[i]);
                          this.totalCostNumber += Number.parseInt(this.listDossierFees[i].amount) * Number.parseInt(this.listDossierFees[i].quantity);
                          let fee = {
                            amount : this.listDossierFees[i].amount,
                            quantity: this.listDossierFees[i].quantity,
                            status : 0,
                            dossierFee : this.listDossierFees[i]
                          };
                          this.listfee.push(fee);
                        }
                        let body = {
                          total: this.totalCostNumber,
                          status: 0,
                          paymentMethod: this.paymentMethodObj,
                          paymentDetail: this.listfee,
                          dossier: this.dossierDetail
                        };
                        if(dt.content.length > 0){
                          //Đã có thanh toán, chỉ cần update paymentMethod
                          let singleDossierPayment = dt.content;
                          this.dossierService.putDossierPayment(singleDossierPayment.id, body).subscribe(rs=>{
                            console.log(rs);
                          }, err => { console.log("update dossier payment"); console.log(err); });
                        }else{
                          //Thêm mới dossier payment
                          this.dossierService.postNewDossierPayment(body).subscribe(rs=>{
                            console.log(rs);
                          }, err => { console.log("create dossier payment"); console.log(err); });
                        }
                      }
                    }, error => {
                      console.log("Lỗi lấy danh sách lệ phí khi yêu cầu thanh toán payment-request r525");
                      console.log(error);
                    });
                  }, error => {
                    console.log("Lỗi không lấy được dossier payment tại file payment-request r550");
                    console.log(error);
                  });                
                }
              });
            // }
          }

          this.dialogRef.close(true);
        } else {
          this.dialogRef.close(false);
        }
      }, err => {
        this.dialogRef.close(false);
      });
    }

      //start HGI IGATESUPP-97597
      if (this.hideReasonPay) {
        let dosStatus = 14;
        if (this.dossierDetail.dossierStatus.id !== 0 && this.dossierDetail.dossierStatus.id !==21){
          dosStatus = 17;
        }
        const requestBodyObj: any = {
          // dossierStatus: this.env?.enableApprovalOfLeadership == 1 ? 8 : 1,
          dossierStatus: dosStatus,
          comment: '',
          description: '',
          numberPausePaidDay: formObj.numberPausePaidDay,
          dossierTaskStatus: this.dossierTaskStatus,
          dossierMenuTaskRemind: this.dossierMenuTaskRemind
        };
        if(this.isPaymentDayLimit && dosStatus == 14){
          await this.checkPaymentDayLimit();
          let currentDate = null;
          if(this.deadlineForPaymentDossier && this.numberDate > 0){
            let addDate = this.addDaysToCurrentDate(this.numberDate).toISOString();
            currentDate = addDate.slice(0,23) + 'Z';
            const viComment = 'Số ngày chờ thanh toán: '+this.numberDate+'<br>Yêu cầu thanh toán: Đề nghị Ông/Bà thanh toán lệ phí hồ sơ <b>'+this.dossierCode +'</b>, hạn thanh toán lệ phí hồ sơ: '+currentDate;
            const enComment = 'Number of days waiting for payment: '+this.numberDate+'<br>Request for payment of dossier <b>'+this.dossierCode +'</b>, application fee deadline: '+currentDate;
            this.postCommentDuePaidDate(viComment, enComment);
            this.deadlineForPaymentDossierDueDate = currentDate;
          }
          requestBodyObj.extendHCM = {
            paymentRequest:{
              userRequest: this.userInfo,
              numberWaitPaidDay: this.paymentDayLimit,
              dateWaitPaidDay: this.dueWaitPaidDay,
              sendSmsSuccess: false,
              deadlineForPaymentDossier:this.numberDate,
              deadlineForPaymentDossierDueDate: currentDate
            }
          };
        }
        if (this.commentContent.trim() !== '') {
          this.postComment(this.commentContent.trim(), this.descriptionContent.trim());
          requestBodyObj.comment = this.commentContent.trim();
          requestBodyObj.description = this.descriptionContent.trim();
        } else {
          const msgObj = {
            vi: 'Yêu cầu thanh toán hồ sơ <b>' + this.dossierCode + '</b>',
            en: 'Request for payment of dossier <b>' + this.dossierCode + '</b>!'
          };
          this.postComment(msgObj[this.selectedLang]);
          requestBodyObj.comment = msgObj[this.selectedLang];
        }

        if(this.checkNVTC){
          requestBodyObj.dossierSyncStatus = {
            id: this.statusIdSyncNVTC,
            name: [{
              languageId: "228",
              name: "Nghĩa vụ tài chính"
            }]
          }
        }
        if(this.enableConfigPaymentProcessingDossier == true && this.checkNVTC == false){
          requestBodyObj.isConfigPaymentProcessingDossier = 1;
        }
        if(this.notiFeeFirstTime && this.isSendFlagNotifeeQBH) {
          requestBodyObj.extendQBH = {
            notiFeeFirstTime: true
          }
        }
        console.log("===requestBody: " + requestBodyObj);
        const requestBody = JSON.stringify(requestBodyObj, null, 2);
        this.dossierService.putDossierStatusWithComment(this.dossierId, requestBody).subscribe(data => {
          if (data.affectedRows === 1) {
            const newDate = tUtils.newDate();
            this.postHistory();
            if (this.config.receivePromotionalProcedureUpdated === 1 || this.config.receivePromotionalProcedureUpdated === '1') {
              this.dossierService.getDossierDetail(this.dossierId).subscribe(data => {
                if (data.sync !== undefined) {
                  const dataRequest: any = {};
                  const agency = JSON.parse(localStorage.getItem('userAgency'));
                  if (agency !== null && agency !== undefined) {
                    dataRequest.agencyId = agency.id;
                  } else {
                    dataRequest.agencyId = this.config.rootAgency.id;
                  }
  
                  if (this.config.subsystemId !== null && this.config.subsystemId !== undefined) {
                    dataRequest.subsystemId = this.config.subsystemId;
                  }
  
                  dataRequest.agencyCode = data.agency.id;
                  dataRequest.status = 5;
                  dataRequest.code = data.code;
                  dataRequest.sourceCode = data.sync.sourceCode;
                  dataRequest.comment = this.commentContent.trim();
                  dataRequest.date = this.datePipe.transform(newDate, 'yyyyMMddHHmmss');
                  this.dossierService.postSynchronizePromotionStatus(dataRequest).subscribe(data => {
                  });
                }
              });
            }
            this.notiService.changeSendSubject.next(
              {
                id: this.dossierDetail?.procedureProcessDefinition?.id,
                phone:  this.dossierDetail?.applicant?.data?.phoneNumber,
                email:  this.dossierDetail?.applicant?.data?.email,
                currentTask: this.currentTask,
                renewContent:true, 
                updateSend: false ,
                contentParams: {
                  parameters: {
                    sector: !! this.dossierDetail?.procedure?.sector?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name ?  this.dossierDetail?.procedure?.sector?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name : '',
                    procedure: !! this.dossierDetail?.procedure?.translate?.find(agency => agency.languageId === Number(this.selectedLangId))?.name ?  this.dossierDetail?.procedure?.translate?.find(agency => agency.languageId === Number(this.selectedLangId))?.name : '',
                    agency: !! this.dossierDetail?.agency?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name ?  this.dossierDetail?.agency?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name : this.dossierDetail?.agency?.parent?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name?this.dossierDetail?.agency?.parent?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name: "",
                    agencyId: !! this.dossierDetail?.agency?.id ? data?.agency?.id : data?.agency?.parent?.id,
                    extend: this.isSmsQNM ?  this.dossierDetail.extend : {},
                    applicantFullname:  this.dossierDetail?.applicant?.data?.fullname,
                    officerFullname: '',
                    subsystem: !!this.env?.subsystem?.name ? this.env?.subsystem?.name : "Hệ thống Một cửa điện tử",
                    dossierCode:  this.dossierDetail?.code,
                    nextTask: this.zaloPaymentReuirementNextTask?.enable?this.zaloPaymentReuirementNextTask?.nextTask:(!!this.env?.notify?.additionalRequirement?.nextTask ? this.env?.notify?.additionalRequirement?.nextTask : 'Yêu cầu bổ sung hồ sơ'),
                    // IGATESUPP-63184
                    nextStatus: !!this.env?.notify?.feeRequirement?.nextStatus ? this.env?.notify?.feeRequirement?.nextStatus : ( this.dossierDetail?.dossierTaskStatus?.name ?? ''), // cho bo sung, trang thai hien tai cua ho so
                    dossierStatus: !!this.env?.notify?.feeRequirement?.dossierStatus ? this.env?.notify?.feeRequirement?.dossierStatus : ( this.dossierDetail?.dossierStatus?.name?.find(t => t.languageId === Number(this.selectedLangId))?.name ?? ''), // cho bo sung, trang thai hien tai cua ho so
                    dossierSearchPadsvcUrl: (!!this.env?.domain?.padsvc ? this.env?.domain?.padsvc : "http://padsvctest.vncitizens.vn/vi/") + "dossier/public",
                    dossierDetailPadsvcUrl: (!!this.env?.domain?.padsvc ? this.env?.domain?.padsvc : "http://padsvctest.vncitizens.vn/vi/") + "dossier/detail/" + this.dossierId,
                    dossierSearchUrl: (!!this.env?.domain?.onegate ? this.env?.domain?.onegate : "http://padsvctest.vncitizens.vn/vi/") + "dossier/search/",
                    dossierDetailUrl: (!!this.env?.domain?.onegate ? this.env?.domain?.onegate : "http://padsvctest.vncitizens.vn/vi/") + "dossier/search/" + this.dossierId,
                    dossierPayDetailPadsvcUrl: (!!this.env?.domain?.onegate ? this.env?.domain?.onegate : "http://padsvctest.vncitizens.vn/vi/") + "dossier/payment/" + this.dossierId,
                    returnMethod: !! this.dossierDetail?.dossierReceivingKind?.name?.find(recieve => recieve.languageId === Number(this.selectedLangId))?.name ?  this.dossierDetail?.dossierReceivingKind?.name?.find(recieve => recieve.languageId === Number(this.selectedLangId))?.name : '',
                    receivingDate: '',
                    appointmentDate: '',
                    dossierFee: this.totalCost,
                    reason: this.commentContent.trim(),
                    deadlineForPaymentDossierDueDate: !!this.dossierDetail?.extendHCM?.paymentRequest?.deadlineForPaymentDossierDueDate ? this.datePipe.transform(data?.extendHCM?.paymentRequest?.deadlineForPaymentDossierDueDate, 'dd/MM/yyyy HH:mm:ss') : !!this.deadlineForPaymentDossierDueDate ? this.datePipe.transform(this.deadlineForPaymentDossierDueDate, 'dd/MM/yyyy HH:mm:ss') : "", //IGATESUPP-117667 Q11 -P7: Đề nghị thêm chức năng báo Quá hạn thanh toán hồ sơ
                  }
                }
              }
            );
            setTimeout(
              ()=>{ this.notiService.confirmSendSubject.next({
                confirm: true,
                renewContent: true
              })}, 1000
            )
           
  
            // if (this.env?.enableApprovalOfLeadership == 1){
            //   const data = {
            //     type: 8,
            //     date: newDate,
            //     attachment: this.uploadedImage
            //   }
            //   this.dossierService.updateApprovalData(this.dossierId, data).subscribe(res => {});
            // }
            if(this.env?.enableApprovalOfLeadership === 0 && this.isSyncDBNLGSPTanDanBXD === 1){
              this.syncPostReceiveInforFileFromLocal();
            }
            
            if (this.deploymentService?.env?.OS_BTTTT?.isEnabledNotify) {
              // IGATESUPP-62366 thong bao cho nguoi dan
              this.dossierService.noticeDossier(this.dossierId, {comment: 'Lý do:&nbsp;' + this.commentContent.trim()}).subscribe(res => {});
            }
  
            //đổi hình thức thanh toán IGATESUPP-71172
            if(this.showRequireDirectPayment.enable === true  && this.dossierDetail?.applyMethod?.id == 1){
              // let userAgency = JSON.parse(localStorage.getItem('userAgency'));
              // var parentAgencyId = (userAgency.parent != null && userAgency.parent != undefined) ? userAgency.parent.id : null;
              // if(!!parentAgencyId && this.showRequireDirectPayment.agency.includes(parentAgencyId)){
                let tagId = this.showRequireDirectPayment?.tagHinhThucThanhToanPayment ? this.showRequireDirectPayment?.tagHinhThucThanhToanPayment : '62f5d79b5c424b277f174318';
                this.tagService.getFullyDetails(tagId).subscribe(tag => {
                  this.tagPaymentMethod = tag;
                  if (this.tagPaymentMethod) {
                    this.paymentMethodObj = {
                      id: this.tagPaymentMethod.id,
                      name: this.tagPaymentMethod.trans,
                      code: this.tagPaymentMethod.code
                    };
                    this.dossierService.getDossierPayment('?page=0&size=50&spec=page&status=0&dossier-id=' + this.dossierId).subscribe(dt => {
                      this.padmanService.getDossierFee(this.dossierId).subscribe(res => {
                        if(res.length > 0){
                          this.listDossierFees = res;
                          this.listfee = [];
                          for(var i = 0; i < res.length; i++){
                            // console.log(this.listDossierFees[i]);
                            this.totalCostNumber += Number.parseInt(this.listDossierFees[i].amount) * Number.parseInt(this.listDossierFees[i].quantity);
                            let fee = {
                              amount : this.listDossierFees[i].amount,
                              quantity: this.listDossierFees[i].quantity,
                              status : 0,
                              dossierFee : this.listDossierFees[i]
                            };
                            this.listfee.push(fee);
                          }
                          let body = {
                            total: this.totalCostNumber,
                            status: 0,
                            paymentMethod: this.paymentMethodObj,
                            paymentDetail: this.listfee,
                            dossier: this.dossierDetail
                          };
                          if(dt.content.length > 0){
                            //Đã có thanh toán, chỉ cần update paymentMethod
                            let singleDossierPayment = dt.content;
                            this.dossierService.putDossierPayment(singleDossierPayment.id, body).subscribe(rs=>{
                              console.log(rs);
                            }, err => { console.log("update dossier payment"); console.log(err); });
                          }else{
                            //Thêm mới dossier payment
                            this.dossierService.postNewDossierPayment(body).subscribe(rs=>{
                              console.log(rs);
                            }, err => { console.log("create dossier payment"); console.log(err); });
                          }
                        }
                      }, error => {
                        console.log("Lỗi lấy danh sách lệ phí khi yêu cầu thanh toán payment-request r525");
                        console.log(error);
                      });
                    }, error => {
                      console.log("Lỗi không lấy được dossier payment tại file payment-request r550");
                      console.log(error);
                    });                
                  }
                });
              // }
            }
  
            this.dialogRef.close(true);
          } else {
            this.dialogRef.close(false);
          }
        }, err => {
          this.dialogRef.close(false);
        });
      }
      if(this.hideCurrencyConverter == 1 && this.allowCurrencyConvert == true)
      {
         if(this.convertedCurrency == true)
         {
              this.updateConvertedCurrency();
              this.updateExrateCurrencyDossierFee();
         }else{
              this.updateConvertedCurrency();
         }
      }
                //end HGI IGATESUPP-97597
  }

  onDismiss() {
    this.dialogRef.close();
  }

  postComment(commentContent, description?:string) {
    let msgObj = {};
    if(!!description){
      msgObj = {
        vi: `Yêu cầu thanh toán: ${commentContent}<br>Nội dung: ${description}`,
        en: `Payment request: ${commentContent}<br>Description: ${description}`
      };
    }else{
      msgObj = {
        vi: `Yêu cầu yêu cầu thanh toán: ${commentContent}`,
        en: `Payment request: ${commentContent}`
      };
    }
    const content = {
      groupId: 2,
      itemId: this.dossierId,
      user: {
        id: this.accountId,
        fullname: this.userName
      },
      content: msgObj[this.selectedLang],
      file: this.uploadedImage
    };
    const requestBody = JSON.stringify(content, null, 2);
    this.dossierService.postComment(requestBody).subscribe(data => { });
  }

  postCommentDuePaidDate(viComment?:string, enComment?:string) {
    if(!!viComment && !!enComment){
      let msgObj = {
        vi: viComment,
        en: enComment
      };
      
      const content = {
        groupId: 2,
        itemId: this.dossierId,
        user: {
          id: this.accountId,
          fullname: this.userName
        },
        content: msgObj[this.selectedLang],
        file: this.uploadedImage
      };
      const requestBody = JSON.stringify(content, null, 2);
      this.dossierService.postComment(requestBody).subscribe(data => { });
    }
  }

  onCommentEditorChange(event) {
    this.commentContent = event.editor.getData();
    if (event.editor.getData().trim().length > 500) {
      this.isCKMaxlenght = true;
    } else {
      this.isCKMaxlenght = false;
    }
  }

  onContentEditorChange(event) {
    this.descriptionContent = event.editor.getData();
    if (event.editor.getData().trim().length > 500) {
      this.isCKMaxlenght = true;
    } else {
      this.isCKMaxlenght = false;
    }
  }

  uploadMultiFile(file, accountId) {
    return new Promise((resolve) => {
      this.procedureService.uploadMultiFile(file, accountId).subscribe(data => {
        this.uploadedImage = data;
        // this.formToJSON();
        resolve(true);
      }, err => {
        console.log(err);
        resolve(false);
      });
    });
  }

  onSelectFile(event) {
    if (event.target.files && event.target.files[0]) {
      for (const i of event.target.files) {
        if (i.size >= this.maxFileSize * 1024 * 1024) {
          const msgObj = {
            vi: 'Kích thước tệp tin quá lớn, tên tệp: ' + i.name,
            en: 'The file is too large, file name: ' + i.name
          };
          this.snackbarService.openSnackBar(0, '', msgObj[this.selectedLang], 'error_notification', this.config.expiredTime);
          return;
        }
        if (this.acceptFileType.filter(t => t === i.type.toLowerCase()).length > 0) {
          this.files.push(i);
          const extension = i.name.substring(i.name.lastIndexOf('.')).split('.').pop();
          this.urls.push(this.getFileIcon(extension));

          const reader = new FileReader();
          reader.onload = () => {
            this.uploaded = true;
          };
          if (i.name.length > 20) {
            const startText = i.name.substr(0, 5);
            const shortText = i
              .name
              .substring(i.name.length - 7, i.name.length);
            this.fileNames.push(startText + '...' + shortText);
            this.fileNamesFull.push(i.name);
          } else {
            this.fileNames.push(i.name);
            this.fileNamesFull.push(i.name);
          }
          reader.readAsDataURL(i);
        } else {
          const msgObj = {
            vi: 'Không hỗ trợ loại tệp tin ',
            en: 'File type is not supported '
          };
          // tslint:disable-next-line: max-line-length
          this.snackbarService.openSnackBar(0, '', msgObj[this.selectedLang] + i.type.substring(i.type.lastIndexOf('/') + 1).toUpperCase(), 'error_notification', this.config.expiredTime);
        }
      }
    }
  }
  async changeFile(type){
    if (this.commentContent.trim() === '') {
      const msgObj = {
        vi: 'Vui lòng nhập ý kiến xử lý!',
        en: 'Please enter a handling comments!'
      };
      this.snackbarService.openSnackBar(2, '', msgObj[this.selectedLang], 'warning_notification', this.config.expiredTime);
      return
    }
    if (!this.qbhTemplateYctt) {
      const msgObj = {
        vi: 'Không tìm thấy mẫu file',
        en: 'Template not found'
      };
      this.snackbarService.openSnackBar(0, '', msgObj[this.selectedLang], 'error_notification', this.config.expiredTime);
      return;
    }
    const url = this.qbhTemplateYctt;
    let blob = await fetch(url).then(r => r.blob());
    let address = this.dossierDetail?.applicant?.data?.address ? this.dossierDetail?.applicant?.data?.address :"";
    let village = this.dossierDetail?.applicant?.data?.village?.label ? this.dossierDetail?.applicant?.data?.village?.label : "";
    let district = this.dossierDetail?.applicant?.data?.district?.label ? this.dossierDetail?.applicant?.data?.district?.label : "";
    let province = this.dossierDetail?.applicant?.data?.province?.label ? this.dossierDetail?.applicant?.data?.province?.label : "";
    const value = {
      "day": tUtils.newDate().getDate(),
      "month": tUtils.newDate().getMonth() + 1,
      "year" : tUtils.newDate().getFullYear(),
      "fullName": this.dossierDetail?.applicant?.data?.fullname,
      "code": this.dossierDetail?.code,
      "address": address + ', '+ village + ', ' + district + ', ' + province,
      "phoneNumber": this.dossierDetail?.applicant?.data?.phoneNumber + ' ',
      "email": this.dossierDetail?.applicant?.data?.email,
      "reason": this.getPlainText1(this.commentContent),
      "description": this.getPlainText1(this.descriptionContent),
      "nameOfficer": localStorage.getItem('tempUsername').trim()
    };
    const newComponent = [   
      {
        "label" : "Nội dung yêu cầu giải quyết",
        "key" : "code"
      },
      {
        "label": "Lý do",
        "key": "reason"
      }
    ];
    this.procedureService.getChangeFileFormEform(blob, JSON.stringify(value), JSON.stringify(newComponent), type).subscribe(async data => {
      const dataType = data.type;
      const binaryData = [];
      binaryData.push(data);
      const blob = new Blob(binaryData, { type: dataType });
      let fileName = `Mẫu file yêu cầu thanh toán hồ sơ.${type}`;
      const file = new File(binaryData, fileName);
      if (file.size >= this.maxFileSize * 1024 * 1024) {
        const msgObj = {
          vi: 'Kích thước tệp tin quá lớn, tên tệp: ' + file.name,
          en: 'The file is too large, file name: ' + file.name
        };
        this.snackbarService.openSnackBar(0, '', msgObj[this.selectedLang], 'error_notification', this.config.expiredTime);
        return;
      }
      let newFile =[];
      newFile.push(file);
      this.files.push(file);
      this.urls.push(this.getFileIcon(type));
      
      const reader = new FileReader();
      reader.onload = () => {
        this.uploaded = true;
      };
      if (fileName.length > 20) {
        const startText = fileName.substring(0, 5);
        const shortText = fileName
          .substring(fileName.length - 7, fileName.length);
        this.fileNames.push(startText + '...' + shortText);
        this.fileNamesFull.push(fileName);
      } else {
        this.fileNames.push(fileName);
        this.fileNamesFull.push(fileName);
      }
      reader.readAsDataURL(blob);
      if (this.files.length > 0) {
        let checkFile = await this.uploadMultiFile(newFile, this.accountId);
        if (!checkFile) {
          const msgObj = {
            vi: 'Upload file thất bại!',
            en: 'File upload failed!'
          };
          this.snackbarService.openSnackBar(2, '', msgObj[this.selectedLang], 'warning_notification', this.config.expiredTime);
        }
        console.log("this.uploadFileNames",this.uploadFileNames);
      }
   }, err => {
      const msgObj = {
        vi: 'Lỗi trong quá trình kết xuất file',
        en: 'Error occurred when extract file'
      };
      this.snackbarService.openSnackBar(0, '', msgObj[this.selectedLang], 'error_notification', this.config.expiredTime);
      return;
   });
  }
  getPlainText1( strSrc ) {
    let resultStr = "";

    // Ignore the <p> tag if it is in very start of the text
    if(strSrc.indexOf('<p>') == 0)
        resultStr = strSrc.substring(3);
    else
        resultStr = strSrc;

    // Replace <p> with one newline
    resultStr = resultStr.replace(/<p>/gi, "\n"); // --> Diff between getPlainText && getPlainText1
    // Replace <br /> with one newline
    resultStr = resultStr.replace(/<br \/>/gi, "\r\n");
    resultStr = resultStr.replace(/<br>/gi, "\r\n");

    // Replace &nbsp; with space
    resultStr = resultStr.replace(/&nbsp;/gi, " ");
    //-+-+-+-+-+-+-+-+-+-+-+
    // Strip off other HTML tags.
    //-+-+-+-+-+-+-+-+-+-+-+

    return  resultStr.replace( /<[^<|>]+?>/gi,'' );
  }
  removeItem(index: number) {
    this.urls.splice(index, 1);
    this.fileNames.splice(index, 1);
    this.fileNamesFull.splice(index, 1);
    this.files.splice(index, 1);
    this.blankVal = '';
  }

  getFileIcon(ext) {
    return this.config.cloudStaticURL + 'icon/files/300x300/' + ext + '.png';
  }

  downloadTemplate(){
    window.open(this.fileTemplate, '_self');
  }
  // DBN - BXD - dong bo thong hs qua LGSP Tan Dan

  syncPostReceiveInforFileFromLocal (){
    this.dossierService.getDossierDetail(this.dossierId).subscribe(data => {
      let dataApplicant =  data?.applicant?.data;
      let nhanThongTinHSTuDP = [];
      let thongTinNguoiNop = {
        HoTenNguoiNop : dataApplicant?.fullname, //*
        SoCMND : dataApplicant?.identityNumber, //*
        EmailNguoiNop: dataApplicant?.email,
        SoDienThoaiNguoiNop: dataApplicant?.phoneNumber, //*
        DiaChiThuongTruNguoiNop: dataApplicant?.address + ","
                               + dataApplicant?.village?.label + ","
                               + dataApplicant?.district?.label + ","
                               + dataApplicant?.province?.label + ","
                               + dataApplicant?.nation?.label, //*
        MaDoiTuong: ""
      };
      let giayToTrongQuaTrinhXuLy = {
        MaGiayTo: "",
        TenGiayTo: "",
        NoiDungBase64: "",
        DinhDangTepTin: "", // dinh dang .pdf, .doc, .png,...
        MoTa: "",
        LoaiGiayTo: ""
      }
      let hinhThucTraKetQua = 0;
      switch(data?.dossierReceivingKind?.id) {
        case '5f8968888fffa53e4c073ded':
        hinhThucTraKetQua = 0;
        break;
        case '604ecde877bed4110c1dd0d1':
          hinhThucTraKetQua = 1;
          break;
        case '6163f1e20f363b04cf60b224':
          hinhThucTraKetQua = 1;
          break;
        case '5f8969018fffa53e4c073dee':
          hinhThucTraKetQua = 1;
          break;
      }
      let arrThongTinTienTrinh = [];
      let dossierStatus = this.statusName;
      data?.task.forEach(task => {
          if(task?.isCurrent === 1){
            let thongTinTienTrinh = {
              NguoiXuLy: task?.assignee?.fullname, //*
              ChucDanh: "",
              ThoiDiemXuLy: this.datePipe.transform(task?.createdDate, 'yyyyMMddHHmmss'), //* yyyyMMddHHmmss
              PhongBanXuLy: task?.agency?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name,
              NoiDungXuLy:  task?.bpmProcessDefinitionTask?.remind ?  task?.bpmProcessDefinitionTask?.remind?.name : "",
              NgayBatDau:   this.datePipe.transform(task?.createdDate, 'yyyyMMddHHmmss'), //yyyyMMddHHmmss
              NgayKetThucTheoQuyDinh: task?.isCurrent === 0? this.datePipe.transform(task?.updatedDate, 'yyyyMMddHHmmss') : ""
            }
            arrThongTinTienTrinh.push(thongTinTienTrinh);
          }

      });
      let dossierSync = {
        MaHoSo: data.code, //*
        TrangThaiHoSo: dossierStatus, // phu luc 2 *
        NgayTiepNhan: this.datePipe.transform(data?.acceptedDate, 'yyyyMMddHHmmss') ,// yyyyMMddHHmmss*
        NgayHenTraKetQua: this.datePipe.transform(data?.appointmentDate, 'yyyyMMddHHmmss'), //yyyyMMddHHmmss
        ThongTinNguoiNop : thongTinNguoiNop,
        GiayToTrongQuaTrinhXuLy : [],
        HinhThucTraKetQua: hinhThucTraKetQua, // 0: tra truc tiep 1: tra qua duong buu dien 2: tra kq truc tuyen
        ThongTinTienTrinh : arrThongTinTienTrinh,
        MaTTHC: data?.procedure?.code,
        TenTTHC: data?.procedure?.translate?.find(procedure => procedure.languageId === Number(this.selectedLangId))?.name ? data?.procedure?.translate?.find(procedure => procedure.languageId === Number(this.selectedLangId))?.name : '',
      }
      //nhanThongTinHSTuDP.push(dossierSync);
      let params = "agencyId=" + data?.agency.id + "&subsystemId=" + this.config?.subsystemId ; // &configId
      this.dossierService.postReceiveInforFileFromLocal(params, dossierSync).subscribe(async data => {
        console.log("===postReceiveInforFileFromLocal===");
        console.log(data);
      }, er => {
        console.log(er);
      })
    }, err => {
      console.log(err);
    });
  }
  //nhipttt-IGATESUPP-117667 Đề nghị thêm chức năng báo Quá hạn thanh toán hồ sơ
  onNumberDateChange($event){
    if(this.numberDate < 0){
      this.numberDate = null;
      const msgObj = {
        vi: 'Vui lòng nhập số ngày chờ thanh toán phải lớn hơn hoặc bằng 0!',
        en: 'Please enter the number of additional waiting days must be greater than or equal to 0!'
      };
      this.snackbarService.openSnackBar(2, '', msgObj[this.selectedLang], 'warning_notification', this.config.expiredTime);
      return;
    }
    if(this.numberDate > this.deadlineForPaymentDossierNumberOfDays){
      this.numberDate = null;
      const msgObj = {
        vi: 'Vui lòng nhập số ngày chờ thanh toán không được lớn hơn ' + this.deadlineForPaymentDossierNumberOfDays + '!',
        en: 'Please enter the number of additional waiting days no more than ' + this.deadlineForPaymentDossierNumberOfDays + '!'
      };
      this.snackbarService.openSnackBar(2, '', msgObj[this.selectedLang], 'warning_notification', this.config.expiredTime);
      return;
    }
    if(this.deadlineForPaymentDossier && this.numberDate > 0){
      let addDate = this.addDaysToCurrentDate(this.numberDate).toISOString();
      let currentDate = addDate.slice(0,23) + 'Z';
      this.deadlineForPaymentDossierDueDate = currentDate;
      this.notiService.changeSendSubject.next({
          parameters: {
            sector: !!this.dossierDetail?.procedure?.sector?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name ? this.dossierDetail?.procedure?.sector?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name : '',
            procedure: !!this.dossierDetail?.procedure?.translate?.find(agency => agency.languageId === Number(this.selectedLangId))?.name ? this.dossierDetail?.procedure?.translate?.find(agency => agency.languageId === Number(this.selectedLangId))?.name : '',
            agency: !!this.dossierDetail?.agency?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name ? this.dossierDetail?.agency?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name : '',
            agencyId: !!this.dossierDetail?.agency?.id ? this.dossierDetail?.agency?.id : this.dossierDetail?.agency?.parent?.id,
            extend: {},
            applicantFullname: this.dossierDetail?.applicant?.data?.fullname,
            officerFullname: '',
            subsystem: !!this.env?.subsystem?.name ? this.env?.subsystem?.name : "Hệ thống Một cửa điện tử",
            dossierCode: this.dossierDetail?.code,
            nextTask: this.zaloPaymentReuirementNextTask?.enable?this.zaloPaymentReuirementNextTask?.nextTask:(!!this.env?.notify?.additionalRequirement?.nextTask ? this.env?.notify?.additionalRequirement?.nextTask : 'Yêu cầu bổ sung hồ sơ'),
            // IGATESUPP-63184
            nextStatus: !!this.env?.notify?.feeRequirement?.nextStatus ? this.env?.notify?.feeRequirement?.nextStatus : (this.dossierDetail?.dossierTaskStatus?.name ?? ''), // cho bo sung, trang thai hien tai cua ho so
            dossierStatus: !!this.env?.notify?.feeRequirement?.dossierStatus ? this.env?.notify?.feeRequirement?.dossierStatus : (this.dossierDetail?.dossierStatus?.name?.find(t => t.languageId === Number(this.selectedLangId))?.name ?? ''), // cho bo sung, trang thai hien tai cua ho so
            dossierSearchPadsvcUrl: (!!this.env?.domain?.padsvc ? this.env?.domain?.padsvc : "http://padsvctest.vncitizens.vn/vi/") + "dossier/public",
            dossierDetailPadsvcUrl: (!!this.env?.domain?.padsvc ? this.env?.domain?.padsvc : "http://padsvctest.vncitizens.vn/vi/") + "dossier/detail/" + this.dossierId,
            dossierSearchUrl: (!!this.env?.domain?.onegate ? this.env?.domain?.onegate : "http://padsvctest.vncitizens.vn/vi/") + "dossier/search/",
            dossierDetailUrl: (!!this.env?.domain?.onegate ? this.env?.domain?.onegate : "http://padsvctest.vncitizens.vn/vi/") + "dossier/search/" + this.dossierId,
            dossierPayDetailPadsvcUrl: (!!this.env?.domain?.onegate ? this.env?.domain?.onegate : "http://padsvctest.vncitizens.vn/vi/") + "dossier/payment/" + this.dossierId,
            returnMethod: !!this.dossierDetail?.dossierReceivingKind?.name?.find(recieve => recieve.languageId === Number(this.selectedLangId))?.name ? this.dossierDetail?.dossierReceivingKind?.name?.find(recieve => recieve.languageId === Number(this.selectedLangId))?.name : '',
            receivingDate: '',
            appointmentDate: '',
            dossierFee: this.totalCost,
            reason: '',
            deadlineForPaymentDossierDueDate: !!this.deadlineForPaymentDossierDueDate ? this.datePipe.transform(this.deadlineForPaymentDossierDueDate, 'dd/MM/yyyy HH:mm:ss') : "", //IGATESUPP-117667 Q11 -P7: Đề nghị thêm chức năng báo Quá hạn thanh toán hồ sơ
          }
      });
    }
  }
  
  onCheckNumberDateChange($event){
    this.checkNumberDate = $event?.checked;
  }
  
  getNumberDate()
  {
    let numberDate = '';
    if(this.deadlineForPaymentDossier){
      if(this.checkNumberDate && this.numberDate != undefined && this.numberDate != null && this.numberDate >= 0){
        let current = new Date();
        current.setHours(0, 0, 0, 0);
        const end = new Date(current);
        end.setDate(current.getDate() + this.numberDate);
        numberDate = this.datePipe.transform(end, 'dd/MM/yyyy');
      }
    }
    return numberDate;
  }
  //end nhipttt-IGATESUPP-117667
}

export class ConfirmPaymentRequestDialogModel {
  constructor(public dossierId: string, public dossierCode: string, public typeProcess?, public checkNVTC?) {
  }
}
