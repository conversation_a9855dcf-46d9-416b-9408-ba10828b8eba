import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { ApiProviderService } from 'src/app/core/service/api-provider.service';
import { EnvService } from 'src/app/core/service/env.service';

@Injectable({
    providedIn: 'root'
})
export class WarnLoginUserService {

    constructor(
        private http: HttpClient,
        private envService: EnvService,
        private apiProviderService: ApiProviderService,
    ) { }
    private basecat = this.apiProviderService.getUrl('digo', 'basecat');
    private human = this.apiProviderService.getUrl('digo', 'human');
    getListLogWarnLogin():  Observable<any>{
        let headers = new HttpHeaders();
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        return this.http.get(this.basecat + '/number-of-visit', { headers }).pipe();    
    }
    getContactLogWarnLoginByUserId(id):  Observable<any>{
        let headers = new HttpHeaders();
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        return this.http.get(this.human + '/user-log-warn/'+id, { headers }).pipe(); 
    }
    plusTimeLogin( id: any,sessionId) : Observable<any>{
        let headers = new HttpHeaders();
        headers = headers.set('Content-Type', 'application/json');
        return this.http.put<any>(this.human +`/user-log-warn/${id}/update-times`+"?session-id="+sessionId, { headers });
    }
    getAllLogWarnLoginByUserId(id):  Observable<any>{
        let headers = new HttpHeaders();
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        return this.http.get(this.human + '/user-log-warn/'+id+'/--get-info-warn-login', { headers }).pipe(); 
    }
}
