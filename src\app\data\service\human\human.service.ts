import { Injectable } from '@angular/core';
import {HttpClient, HttpHeaders, HttpParams} from "@angular/common/http";
import {EnvService} from "core/service/env.service";
import {ApiProviderService} from "core/service/api-provider.service";
import {Observable} from "rxjs";
import {KeycloakService} from "keycloak-angular";

@Injectable({
  providedIn: 'root'
})
export class HumanService {
  private human = this.apiProviderService.getUrl('digo', 'human');
  constructor(private http: HttpClient,
              private envService: EnvService,
              private keycloakService: KeycloakService,
              private apiProviderService: ApiProviderService) { }

              
  getFullUserInfo(id: string): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.human + '/user/' + id + '/--fully', {headers});
  }

  getTanDanUser(page?:number, size?:number, keyword?:string, agencyCode?:string):Observable<any>{
    let URL = `${this.human}/user/fullname+experience?agency-code=${agencyCode}&page=${page}&size=${size}`;
    if(keyword)
      URL += `&keyword=${keyword}`;
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get<any>(URL,{ headers });
  }

  getUsers(searchStr: string): Observable<any>{
    const URL = `${this.human}/user/fullname+experience` + searchStr;
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get<any>(URL, { headers });
  }

  getUserInfo(): Promise<any> {
    return new Promise<any>((resolve, reject) => {
      this.keycloakService.loadUserProfile().then(user => {
        resolve(user);
      }, err => {
        resolve(err);
      });
    });
  }

  getUserInfo2(id: string): Observable<any> {
    let headers = new HttpHeaders();
    switch (localStorage.getItem('isLoggedIn')) {
      case 'false':
        const token = localStorage.getItem('OAuth2TOKEN');
        headers = headers.append('Authorization', 'Bearer ' + token);
        return this.http.get(this.human + '/user/' + id, {headers});
      case 'true':
        return this.http.get(this.human + '/user/' + id);
    }
  }

  getCheckUserAdmin(searchStr): Observable<any>{
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get<any>(this.human + '/user/--check-user-exists-by-agency'+ searchStr , { headers });
  }

  getUserHCC(searchStr): Observable<any>{
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get<any>(this.human + '/user/detail+experience'+ searchStr , { headers });
  }

  getUserTelephoneAndMail(id): Observable<any>{
    const URL = `${this.human}/user/${id}`;
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get<any>(URL, { headers });
  }
  
  getUserSign(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.human + '/user/' + id + '/sign', { headers });
  }

  getUserInfoKTM(id: string): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.human + '/user/' + id);
  }
  
  getUserInfo3(id: string): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.human + '/user/' + id);
  }

  getUserDetailExperience(search:any) : Observable<any>{
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    return this.http.get<any>(this.human +`/user/detail+experience`+ search, { headers }).pipe();
  }

  getUserTypeAgency(searchString: string): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.human + '/user/--cadres-agency' + searchString);
  }

  getUsers1(searchStr: string): Observable<any>{
    const URL = `${this.human}/user/` + searchStr;
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get<any>(URL, { headers });
  }

  getUserApproveFileSigned(searchStr: string, agencyId: string): Observable<any>{
    const URL = `${this.human}/user/role-status-position-mainAgency?role=` + searchStr + `&agency-id=`+agencyId;
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get<any>(URL, { headers });
  }
}
