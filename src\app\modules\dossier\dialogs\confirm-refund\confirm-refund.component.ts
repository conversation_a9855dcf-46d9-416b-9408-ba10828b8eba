import { HomeService } from '../../../../data/service/home/<USER>';
import { ProcessService } from 'src/app/data/service/process/process.service';
import { Component, OnInit, Inject } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { KeycloakService } from 'keycloak-angular';
import { DossierService } from 'src/app/data/service/dossier/dossier.service';
import { UserService } from 'src/app/data/service/user.service';
import * as ClassicEditor from '@ckeditor/ckeditor5-build-classic/build/ckeditor';
import '@ckeditor/ckeditor5-build-classic/build/translations/vi';
import { EnvService } from 'src/app/core/service/env.service';
import { SnackbarService } from 'src/app/data/service/snackbar/snackbar.service';
import { DatePipe } from '@angular/common';
import { NotificationService } from 'src/app/data/service/notification.service';
import { DeploymentService } from 'src/app/data/service/deployment.service';
import * as tUtils from 'src/app/data/service/thoai.service';
import { ProcedureService } from 'src/app/data/service/procedure/procedure.service';
import { take } from 'rxjs/operators';
import { FormControl, FormGroup } from '@angular/forms';
import { PadmanService } from 'src/app/data/service/svc-padman/padman.service';
import { AgencyService } from 'src/app/data/service/basedata/agency.service';
import { MatTableDataSource } from '@angular/material/table';

@Component({
    selector: 'confirm-refund',
    templateUrl: './confirm-refund.component.html',
    styleUrls: ['./confirm-refund.component.scss']
})

export class ConfirmRefundComponent implements OnInit {
    dossierId : string;
    dossierCode : string;
    feeRefundData : any;
    FEE_ELEMENTDATA: any[] = [];
    procedureId : string;
    dossierFee = [];
    feeDataSource: MatTableDataSource<any>;
    totalCost : string;
    totalRemaining : string;
    feeRefundForm = new FormGroup({
        feeRefund: new FormControl(''),
        description: new FormControl(''),
      });
    feeDisplayedColumns: string[] = ['procostType', 'amount', 'pay', 'typePay'];

    constructor(
        private userService : UserService,
        private dossierService: DossierService,
        private procedureService: ProcedureService,
        private snackbarService: SnackbarService,
        public dialogRef: MatDialogRef<ConfirmRefundComponent>,
        @Inject(MAT_DIALOG_DATA) public data: ConfirmRefundDialogModel
    ){        
        this.dossierCode = data.dossierCode;
        this.dossierId = data.dossierId;
        this.feeDataSource = new MatTableDataSource(this.FEE_ELEMENTDATA);
    }
    ngOnInit(): void {
        this.getFeeRefundData()
        this.getDossierFee()
    }

    onDismiss() {
        this.dialogRef.close();
    }
    getFeeRefundData(){
        this.dossierService.getDossierDetail(this.dossierId).subscribe(data =>{
            if(!!data?.feeRefundData){
                this.feeRefundData = data.feeRefundData
            }
            this.procedureId = data.procedure.id
        });
    }

    async getDossierFee(){
        this.dossierService.getDossierFee(this.dossierId).subscribe(async data =>{
            let cost = 0;
            let paid = 0;
            let dataPayment =  await this.dossierService.getDossierPayment('?page=0&size=50&spec=page&status=1&dossier-id=' + this.dossierId).toPromise()
            console.log("TMP1306")
            console.log(dataPayment)
            for(let i = 0; i < data.length ; i++){
                data[i].monetaryUnit = 'VNĐ'
                data[i].cost = data[i]?.amount
                data[i]?.procost?.type.name.forEach(name => {
                    if (name.languageId === Number(localStorage.getItem('languageId'))) {
                        data[i].typeName = name.name
                    }
                })
                data[i].typePay = 'Trực tiếp'
                for(let j = 0; j < dataPayment.numberOfElements; j++){
                    if(!!dataPayment.content[j]?.paymentMethod && 
                        dataPayment.content[j].paymentDetail.some(paymentDetail => paymentDetail.dossierFee.id == data[i].id)){
                            dataPayment.content[j].paymentMethod?.name.forEach(name =>{
                                if (name.languageId === Number(localStorage.getItem('languageId'))) {
                                    data[i].typePay = name.name
                                }
                            })
                    }
                }
                cost += data[i]?.quantity*data[i]?.cost
                paid += data[i]?.paid
                this.FEE_ELEMENTDATA.push(data[i])
            }
            this.feeDataSource.data = this.FEE_ELEMENTDATA
            this.totalCost = Number((cost).toFixed(1)).toLocaleString() + ' ' + 'VNĐ'
            this.totalRemaining = Number((cost - paid).toFixed(1)).toLocaleString() + ' ' + 'VNĐ'
        })
    }

    onConfirm(){
        let formObj = this.feeRefundForm.getRawValue()
        if(!formObj.feeRefund || !formObj.description){
            this.snackbarService.openSnackBar(0, '',
              "Vui lòng điền đầy đủ thông tin!",
              'error_notification',
              3000
            );
            return 
        }
        this.feeRefundData.username = localStorage.getItem("tempUsername");
        this.feeRefundData.userId = localStorage.getItem("UID");
        this.feeRefundData.feeRefund = formObj.feeRefund;
        this.feeRefundData.description = formObj.description
        console.log("TMP")
        console.log(this.feeRefundData)
        this.dossierService.confirmRefund(this.dossierId, this.feeRefundData).subscribe(data =>{
            if(data.affectedRows == 1){
                this.dialogRef.close(1)
            }else{
                this.dialogRef.close(0)
            }
        },err =>{
            this.dialogRef.close(1)
        })
    }
  

}

export class ConfirmRefundDialogModel {
    constructor(public dossierId: string, public dossierCode: string) {
    }
  }