import { Component, OnInit, Inject } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA, MatDialog } from '@angular/material/dialog';
import { KeycloakService } from 'keycloak-angular';
import { DossierService } from 'src/app/data/service/dossier/dossier.service';
import { UserService } from 'src/app/data/service/user.service';
import * as ClassicEditor from '@ckeditor/ckeditor5-build-classic/build/ckeditor';
import '@ckeditor/ckeditor5-build-classic/build/translations/vi';
import { EnvService } from 'src/app/core/service/env.service';
import { SnackbarService } from 'src/app/data/service/snackbar/snackbar.service';
import { DatePipe } from '@angular/common';
import { NotificationService } from 'src/app/data/service/notification.service';
import { DeploymentService } from 'src/app/data/service/deployment.service';
import * as tUtils from 'src/app/data/service/thoai.service';
import { ProcedureService } from 'src/app/data/service/procedure/procedure.service';
import { take } from 'rxjs/operators';
import { ProcessService } from 'src/app/data/service/process/process.service';
import { HomeService } from 'src/app/data/service/home/<USER>';
import {NotifyQNIService} from "shared/components/check-send-notify-qni/check-send-notify-qni.service";
import { PadmanService } from 'src/app/data/service/svc-padman/padman.service';
import {FormControl, FormGroup} from '@angular/forms';
import {AdapterService} from 'src/app/data/service/adapter/adapter.service';
import {LogmanService} from 'data/service/logman/logman.service';
import { AgencyService } from 'src/app/data/service/basedata/agency.service';
import { FormElement } from 'src/app/data/schema/form-element';
import { MatTableDataSource } from '@angular/material/table';
import { NotificationDialogComponent, NotificationDialogModel } from 'src/app/shared/components/dialogs/notification-dialog/notification-dialog.component';


@Component({
  selector: 'app-log-sync',
  templateUrl: './log-sync.component.html',
  styleUrls: ['./log-sync.component.scss']
})
export class LogSyncComponent implements OnInit {

  config = this.envService.getConfig();
  env: any = this.deploymentService.getAppDeployment()?.env;
  selectedLang = localStorage.getItem('language') || 'vi';
  selectedLangId = Number(localStorage.getItem('languageId')) || 228;
  dossierId: string;
  commentContent = '';
  userName: string;
  accountId: string;
  dossierCode: string;
  isCKMaxlenght = false;
  dossierDetail: any;
  oldstatus = '';
  officers = [];
  agencyId: any;
  getApprovalAgency = '';

  public Editor = ClassicEditor;
  commentConfig = {
    placeholder: 'Nhập nội dung...',
    language: 'vi',
    removePlugins: ['MediaEmbed', 'ImageUpload'],
    toolbarLocation: 'bottom'
  };
  dossierTaskStatus = { id: '', name: [] };
  dossierMenuTaskRemind = { id: '', name: [] };

  // ================================================= Upload file
  uploaded: boolean;
  blankVal: any;
  uploadedImage = null;
  files = [];
  urls = [];
  fileNames = [];
  fileNamesFull = [];
  listProcedureProcess = [];

  currentTask: any;

  isFieldArrayInvalid = false;
  acceptFileExtension = this.config.acceptFileExtension;
  acceptFileType = this.config.acceptFileType;
  maxFileSize = this.env?.maxFileSize || this.config.maxFileSize;
  isBoTNMTQNM = this.env?.OS_QNM?.boTNMT === true ? this.env?.OS_QNM?.boTNMT : false;
  isBoTNMTHCM = this.deploymentService.env.OS_HCM.maTTBoTNMT;
  isSyncConstructKTM=this.deploymentService.env?.OS_KTM?.isSyncConstructKTM;
  constructConfigId=this.deploymentService.env?.OS_KTM?.constructConfigId;

  // IGATESUPP-44607
  allowNextStepWaitingForApproval = this.deploymentService.env.OS_HCM.allowNextStepWaitingForApproval ? this.deploymentService.env.OS_HCM.allowNextStepWaitingForApproval : false;
  enableApproveStopProcessingCheckBox = false;
  dossierTaskStatusWaitingForApproval = { id: '', name: [] };
  dossierMenuTaskRemindWaitingForApproval = { id: '', name: [] };

  requestCancelMsg = {
    vi: 'Đã gửi yêu cầu dừng xử lý!',
    en: 'Sent request for cancel processing!'
  };
  cancelMsg = {
    vi: 'Đã dừng xử lý!',
    en: 'Cancelled processing!'
  };
  requestCancelComment = {
    vi: 'Yêu cầu dừng xử lý: ',
    en: 'Request for cancel dossier: '
  };
  cancelComment = {
    vi: 'Dừng xử lý: ',
    en: 'Cancel processing: '
  };
  title = '';
  //Thu tuc co cau hinh dong bo BXD-LGSP Tan Tan
  isSyncDBNLGSPTanDanBXD = 0;
  statusName = "";
  fileTemplate = "";

  totalCost = '';

  requireAttachmentWhenCancel = true;

  ckeditorMaxLengthNotification = "";
  ckeditorMaxLength = this.deploymentService.env.OS_HCM.ckeditorMaxLength;
  codeMap="";

  updateForm = new FormGroup({
    approvalAgencyId: new FormControl(''),
  });
  approvalAgency = [];
  visibleApprovalAgency = this.deploymentService.env.visibleApprovalAgency;
  setOnlyApprovalAgency = this.deploymentService.env?.OS_QNI?.setOnlyApprovalAgency;
  isSmsQNM = this.env?.OS_QNM?.isSmsQNM ? true : false;
  isLGSPHCM = this.env?.OS_HCM?.showDossierLGSPHCMSync == true ? this.env?.OS_HCM?.showDossierLGSPHCMSync : false;

  displayedColumns: string[] = ['stt', 'status', 'date', 'action'];
  ELEMENTDATA: FormElement[] = [];
  dataSource: MatTableDataSource<FormElement>;

  constructor(
    private userService: UserService,
    private envService: EnvService,
    private keycloakService: KeycloakService,
    public dialogRef: MatDialogRef<LogSyncComponent>,
    @Inject(MAT_DIALOG_DATA) public data: LogSyncDialogModel,
    private snackbarService: SnackbarService,
    private datePipe: DatePipe,
    private notiService: NotificationService,
    private deploymentService: DeploymentService,
    private procedureService: ProcedureService,
    private dossierService: DossierService,
    private processService: ProcessService,
    private homeService : HomeService,
    private notifyQNIService: NotifyQNIService,
    private padmanService: PadmanService,
    private dialog: MatDialog,
    private adapterService: AdapterService,
    private logmanService: LogmanService,
    private agencyService: AgencyService
  ) {
    this.dossierId = data.dossierId;
    this.dossierCode = data.dossierCode;
    this.dataSource = new MatTableDataSource(this.ELEMENTDATA);
  }

  ngOnInit(): void {
    this.getLogSyncDetailDossier();
  }

  downloadTemplate(){
    window.open(this.fileTemplate, '_self');
  }

// IGATESUPP-51500 - Đánh giá lại chức năng phê duyệt hồ sơ - 19.06.2023

  async getLogSyncDetailDossier() {
    this.dossierService.getLogSyncDossierDetail(this.dossierId).subscribe(data => {
      this.ELEMENTDATA = [];
      if(!!data && !!data.listSync && data.listSync.length > 0){
        for(let i = 0; i < data.listSync.length; i++) {
          data.listSync[i].stt = (i + 1);
          data.listSync[i].statusName = '';
          if(data.listSync[i].type = 'created'){
            data.listSync[i].statusName = 'Tạo mới hồ sơ';
          }
          if(data.listSync[i].type = 'returned'){
            data.listSync[i].statusName = 'Kết thúc hồ sơ';
          }
          if(!!data.listSync[i].name){
            data.listSync[i].statusName = data.listSync[i].name.name;
          }
          this.ELEMENTDATA.push(data.listSync[i]);
        }
        this.dataSource.data = this.ELEMENTDATA;
      }
    });
  }

  syncByCode(codeDossier?){

    const listCode = [codeDossier];
    this.dossierService.syncDossierByCode(listCode, this.isLGSPHCM).subscribe(res => {
      let successMessage = '';
      if (Number(localStorage.getItem('languageId')) === 228)
      {
        successMessage = 'Đã gửi yêu cầu đồng bộ hồ sơ ' + codeDossier;
      }else{
        successMessage = 'A request to sync' + codeDossier + 'records has been sent';
      }

      const secondContent  = '';
      let code = 'success';
      if (res.affectedRows < 1){
        if (Number(localStorage.getItem('languageId')) === 228)
        {
          successMessage = 'Không gửi được yêu cầu đồng bộ hồ sơ ' + codeDossier;
        }else{
          successMessage = 'Unable to send request to sync ' + codeDossier + ' records';
        }
        code = 'warning';
      }
      const firstContent  = successMessage;
      const dialogData = new NotificationDialogModel(firstContent, secondContent, code);
      const dialogRef = this.dialog.open(NotificationDialogComponent, {
        width: '500px',
        data: dialogData,
        disableClose: true,
        autoFocus: false
      });
      dialogRef.afterClosed().subscribe(rs => {
      });
      this.getLogSyncDetailDossier();
    }, err => {
      let errMessage = '';
      if (err.error.code){
        errMessage = ' ' + err.error.message;
      }
      const msgObj = {
        vi: 'Nhập dữ liệu thất bại.',
        en: 'Failed to import data.'
      };
      this.snackbarService.openSnackBar(0, msgObj[this.selectedLang] + errMessage, '', 'error_notification', this.config.expiredTime);
    });
  }
  


  onDismiss() {
    this.dialogRef.close();
  }

  postComment(commentContent) {
    const msgObj = this.env?.enableApprovalOfLeadership == 1 ? this.requestCancelComment : this.cancelComment;
    const content = {
      groupId: 2,
      itemId: this.dossierId,
      user: {
        id: this.accountId,
        fullname: this.userName
      },
      content: msgObj[this.selectedLang] + commentContent.trim(),
      file: this.uploadedImage
    };
    const requestBody = JSON.stringify(content, null, 2);
    this.dossierService.postComment(requestBody).subscribe(data => { });
  }

  onCommentEditorChange(event) {
    this.commentContent = event.editor.getData();
    if (this.ckeditorMaxLength > 0) {
      if (event.editor.getData().trim().length > this.ckeditorMaxLength) {
        this.isCKMaxlenght = true;
      } else {
        this.isCKMaxlenght = false;
      }
    } else if (event.editor.getData().trim().length > 500) {
      this.isCKMaxlenght = true;
    } else {
      this.isCKMaxlenght = false;
    }
  }

  uploadMultiFile(file, accountId) {
    return new Promise((resolve) => {
      this.procedureService.uploadMultiFile(file, accountId).subscribe(data => {
        this.uploadedImage = data;
        // this.formToJSON();
        resolve(true);
      }, err => {
        console.log(err);
        resolve(false);
      });
    });
  }

  onSelectFile(event) {
    if (event.target.files && event.target.files[0]) {
      for (const i of event.target.files) {
        if (i.size >= this.maxFileSize * 1024 * 1024) {
          const msgObj = {
            vi: 'Kích thước tệp tin quá lớn, tên tệp: ' + i.name,
            en: 'The file is too large, file name: ' + i.name
          };
          this.snackbarService.openSnackBar(0, '', msgObj[this.selectedLang], 'error_notification', this.config.expiredTime);
          return;
        }
        if (this.acceptFileType.filter(t => t === i.type.toLowerCase()).length > 0) {
          this.files.push(i);
          const extension = i.name.substring(i.name.lastIndexOf('.')).split('.').pop();
          this.urls.push(this.getFileIcon(extension));

          const reader = new FileReader();
          reader.onload = () => {
            this.uploaded = true;
          };
          if (i.name.length > 20) {
            const startText = i.name.substr(0, 5);
            const shortText = i
              .name
              .substring(i.name.length - 7, i.name.length);
            this.fileNames.push(startText + '...' + shortText);
            this.fileNamesFull.push(i.name);
          } else {
            this.fileNames.push(i.name);
            this.fileNamesFull.push(i.name);
          }
          reader.readAsDataURL(i);
        } else {
          const msgObj = {
            vi: 'Không hỗ trợ loại tệp tin ',
            en: 'File type is not supported '
          };
          // tslint:disable-next-line: max-line-length
          this.snackbarService.openSnackBar(0, '', msgObj[this.selectedLang] + i.type.substring(i.type.lastIndexOf('/') + 1).toUpperCase(), 'error_notification', this.config.expiredTime);
        }
      }
    }
  }

  removeItem(index: number) {
    this.urls.splice(index, 1);
    this.fileNames.splice(index, 1);
    this.fileNamesFull.splice(index, 1);
    this.files.splice(index, 1);
    this.blankVal = '';
  }

  getFileIcon(ext) {
    return this.config.cloudStaticURL + 'icon/files/300x300/' + ext + '.png';
  }
   // DBN - BXD - dong bo thong hs qua LGSP Tan Dan
  syncPostReceiveInforFileFromLocal (){
    this.dossierService.getDossierDetail(this.dossierId).subscribe(data => {
      let dataApplicant =  data?.applicant?.data;
      let nhanThongTinHSTuDP = [];
      let thongTinNguoiNop = {
        HoTenNguoiNop : dataApplicant?.fullname, //*
        SoCMND : dataApplicant?.identityNumber, //*
        EmailNguoiNop: dataApplicant?.email, 
        SoDienThoaiNguoiNop: dataApplicant?.phoneNumber, //*
        DiaChiThuongTruNguoiNop: dataApplicant?.address + "," 
                               + dataApplicant?.village?.label + "," 
                               + dataApplicant?.district?.label + ","
                               + dataApplicant?.province?.label + ","
                               + dataApplicant?.nation?.label, //*
        MaDoiTuong: ""
      };
      let giayToTrongQuaTrinhXuLy = {
        MaGiayTo: "",
        TenGiayTo: "",
        NoiDungBase64: "",
        DinhDangTepTin: "", // dinh dang .pdf, .doc, .png,...
        MoTa: "",
        LoaiGiayTo: ""
      }
      let hinhThucTraKetQua = 0;
      switch(data?.dossierReceivingKind?.id) {
        case '5f8968888fffa53e4c073ded': 
        hinhThucTraKetQua = 0;
        break;
        case '604ecde877bed4110c1dd0d1': 
          hinhThucTraKetQua = 1;
          break;
        case '6163f1e20f363b04cf60b224':
          hinhThucTraKetQua = 1;
          break;
        case '5f8969018fffa53e4c073dee':
          hinhThucTraKetQua = 1;
          break;
      }
      let arrThongTinTienTrinh = [];
      let dossierStatus = this.statusName;
      data?.task.forEach(task => {
          if(task?.isCurrent === 1){
            let thongTinTienTrinh = {
              NguoiXuLy: task?.assignee?.fullname, //*
              ChucDanh: "", 
              ThoiDiemXuLy: this.datePipe.transform(task?.createdDate, 'yyyyMMddHHmmss'), //* yyyyMMddHHmmss
              PhongBanXuLy: task?.agency?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name, 
              NoiDungXuLy:  task?.bpmProcessDefinitionTask?.remind ?  task?.bpmProcessDefinitionTask?.remind?.name : "",
              NgayBatDau:   this.datePipe.transform(task?.createdDate, 'yyyyMMddHHmmss'), //yyyyMMddHHmmss
              NgayKetThucTheoQuyDinh: task?.isCurrent === 0? this.datePipe.transform(task?.updatedDate, 'yyyyMMddHHmmss') : ""
            }
            arrThongTinTienTrinh.push(thongTinTienTrinh);
          }
         
      });
      let dossierSync = {
        MaHoSo: data.code, //*
        TrangThaiHoSo: dossierStatus, // phu luc 2 *
        NgayTiepNhan: this.datePipe.transform(data?.acceptedDate, 'yyyyMMddHHmmss') ,// yyyyMMddHHmmss*
        NgayHenTraKetQua: this.datePipe.transform(data?.appointmentDate, 'yyyyMMddHHmmss'), //yyyyMMddHHmmss
        ThongTinNguoiNop : thongTinNguoiNop,
        GiayToTrongQuaTrinhXuLy : [],
        HinhThucTraKetQua: hinhThucTraKetQua, // 0: tra truc tiep 1: tra qua duong buu dien 2: tra kq truc tuyen
        ThongTinTienTrinh : arrThongTinTienTrinh,
        MaTTHC: data?.procedure?.code,
        TenTTHC: data?.procedure?.translate?.find(procedure => procedure.languageId === Number(this.selectedLangId))?.name ? data?.procedure?.translate?.find(procedure => procedure.languageId === Number(this.selectedLangId))?.name : '',
      }
      //nhanThongTinHSTuDP.push(dossierSync);
      let params = "agencyId=" + data?.agency.id + "&subsystemId=" + this.config?.subsystemId ; // &configId
      this.dossierService.postReceiveInforFileFromLocal(params, dossierSync).subscribe(async data => {
        console.log("===postReceiveInforFileFromLocal===");
        console.log(data);
      }, er => {
        console.log(er);
      })
    }, err => {
      console.log(err);
    });
  }
  syncDossierKTMToBXD(){
    this.dossierService.getDossierDetail(this.dossierId).subscribe(data => {
      let dataApplicant =  data?.applicant?.data;
      let thongTinNguoiNop = {
        HoTenNguoiNop : dataApplicant?.fullname, //*
        SoCMND : dataApplicant?.identityNumber, //*
        EmailNguoiNop: dataApplicant?.email, 
        SoDienThoaiNguoiNop: dataApplicant?.phoneNumber, //*
        DiaChiThuongTruNguoiNop: dataApplicant?.address + "," 
                               + dataApplicant?.village?.label + "," 
                               + dataApplicant?.district?.label + ","
                               + dataApplicant?.province?.label + ","
                               + dataApplicant?.nation?.label, //*
        MaDoiTuong: ""
      };     
      let hinhThucTraKetQua = 0;
      switch(data?.dossierReceivingKind?.id) {
        case '5f8968888fffa53e4c073ded': 
        hinhThucTraKetQua = 0;
        break;
        case '604ecde877bed4110c1dd0d1': 
          hinhThucTraKetQua = 1;
          break;
        case '6163f1e20f363b04cf60b224':
          hinhThucTraKetQua = 1;
          break;
        case '5f8969018fffa53e4c073dee':
          hinhThucTraKetQua = 1;
          break;
      }
      let arrThongTinTienTrinh = [];
      data?.task.forEach(task => {
          if(task?.isCurrent === 1){
            let thongTinTienTrinh = {
              NguoiXuLy: task?.assignee?.fullname, //*
              ChucDanh: "", 
              ThoiDiemXuLy: this.datePipe.transform(task?.createdDate, 'yyyyMMddHHmmss'), //* yyyyMMddHHmmss
              PhongBanXuLy: task?.agency?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name, 
              NoiDungXuLy:  task?.bpmProcessDefinitionTask?.remind ?  task?.bpmProcessDefinitionTask?.remind?.name : "",
              NgayBatDau:   this.datePipe.transform(task?.createdDate, 'yyyyMMddHHmmss'), //yyyyMMddHHmmss
              NgayKetThucTheoQuyDinh: task?.isCurrent === 0? this.datePipe.transform(task?.updatedDate, 'yyyyMMddHHmmss') : ""
            }
            arrThongTinTienTrinh.push(thongTinTienTrinh);
          }
         
      });
      let giayToTrongQuaTrinhXuLy = [];
      let filename=""
      for (let i = 0; i < this.uploadedImage.length; i++) {
        filename=this.uploadedImage[i].filename
        const nameDoc = filename.split(".")[0];
        const extension = filename.split(".").pop();
        const base64Filename = btoa(unescape(encodeURIComponent(filename)));
        giayToTrongQuaTrinhXuLy.push({
          MaGiayTo: null,
          TenGiayTo: nameDoc,
          NoiDungBase64: base64Filename,
          DinhDangTepTin:extension, // dinh dang .pdf, .doc, .png,...
          MoTa: null,
          LoaiGiayTo: "2"
        })
      }
      let maHoSo = data.code
      if(maHoSo.includes("000.00")){
          this.codeMap = maHoSo;
        }else{      
          const inputString = maHoSo;
          const parts = inputString.split("-");
          const prefixParts = parts[0].split(".");
          const prefix = `000.00.${prefixParts[1]}.${prefixParts[0]}`;
          this.codeMap = `${prefix}-${parts[1]}-${parts[2]}`;  
        }
      let originalString = data.procedure.code;
      let result = originalString.split(".")[0] + '.' + originalString.split(".")[1];
      let dossierSync = { 
      data: [
        {
            MaHoSo: this.codeMap,
            MaTTHC: result,
            NgayTiepNhan: this.datePipe.transform(data?.acceptedDate, 'yyyyMMddHHmmss'),
            NgayHenTraKetQua: this.datePipe.transform(data?.appointmentDate, 'yyyyMMddHHmmss'),
            TrangThaiHoSo: "8",
            ThongTinNguoiNop: thongTinNguoiNop,
            GiayToTrongQuaTrinhXuLy:giayToTrongQuaTrinhXuLy,
            HinhThucTraKetQua:hinhThucTraKetQua,
            ThongTinTienTrinh: arrThongTinTienTrinh
               
        }
    ]
  }
     
      let config = this.constructConfigId// &configId
      this.adapterService.syncDossierConstructKTM(config,dossierSync).subscribe(async data=>{
        console.log(data);
      })
    }, err => {
      console.log(err);
    });
  }


  // getApprovaledAgency(agency) {
  //   if (!!agency.parent) {
  //     this.processService.getApprovalAgency(agency.parent.id).subscribe(data => {
  //       if (!!data.content[0]) {
  //         data.content[0].approvaledAgency.forEach(item => {
  //           item.name.forEach(name => {
  //             if (name.languageId == this.selectedLangId)
  //               item.DisplayName = name.name;
  //           })
  //         });
  //         this.approvaledAgency = data.content[0].approvaledAgency;
  //       }
  //     });
  //   }

  //   if (!!this.approvaledAgency) {
  //     this.processService.getApprovalAgency(agency.id).subscribe(data => {
  //       if (!!data.content[0]) {
  //         data.content[0].approvaledAgency.forEach(item => {
  //           item.name.forEach(name => {
  //             if (name.languageId == this.selectedLangId)
  //               item.DisplayName = name.name;
  //           })
  //         });
  //         this.approvaledAgency = data.content[0].approvaledAgency;
  //       }
  //     });
  //   }
  //   console.log('approvaledAgency', this.approvaledAgency);
  // }
  getApprovaledAgency(agency) {
    this.approvalAgency = [];
    let enableApprovaledAgencyId = this.deploymentService.env.OS_QNI.enableApprovaledAgencyId;
    if (enableApprovaledAgencyId){
      if (agency && agency.id) {
        this.processService.getApprovalAgency(agency.id).subscribe(data2 => {    
          if (data2 && data2.content && data2.content.length > 0) {
            const uniqueAgencies = new Set();
            data2.content.forEach(item => {
              if (item.approvalAgency && item.approvalAgency.name) {
                item.approvalAgency.name.forEach(name => {
                  if (name.languageId === this.selectedLangId) {
                    item.approvalAgency.DisplayName = name.name;
                  }
                });
                if (!uniqueAgencies.has(item.approvalAgency.id)) {
                  uniqueAgencies.add(item.approvalAgency.id);
                  this.approvalAgency.push(item.approvalAgency);
                }
              }
            });
  
            if (this.approvalAgency.length === 1) {
              this.updateForm.patchValue({
                approvalAgencyId: this.approvalAgency[0].id,
              });
            }
          }
        });
      }
    }else{
      if (!!agency.parent) {
        this.processService.getApprovalAgency(agency.parent.id).subscribe(data => {
          if (data.content.length > 0){
            this.approvalAgency = [];
            // tslint:disable-next-line:prefer-for-of
            for (let i = 0; i < data.content.length; i++){
              data.content[i].approvalAgency.name.forEach(name => {
                if (name.languageId === this.selectedLangId){
                data.content[i].approvalAgency.DisplayName = name.name;
                }
              });
              if (this.approvalAgency.filter(item => item.id === data.content[i].approvalAgency.id).length === 0){
                this.approvalAgency.push(data.content[i].approvalAgency);
              }
            }
          }
          if (!!this.approvalAgency) {
            this.processService.getApprovalAgency(agency.id).subscribe(data2 => {
              if (data2.content.length > 0){
                // tslint:disable-next-line:prefer-for-of
                for (let i = 0; i < data2.content.length; i++){
                  data2.content[i].approvalAgency.name.forEach(name => {
                    if (name.languageId === this.selectedLangId){
                    data2.content[i].approvalAgency.DisplayName = name.name;
                    }
                  });
                  if (this.approvalAgency.filter(item => item.id === data2.content[i].approvalAgency.id).length === 0){
                    this.approvalAgency.push(data2.content[i].approvalAgency);
                  }
                }
              }
              if (!!this.approvalAgency &&  this.approvalAgency.length === 1){
                this.updateForm.patchValue({
                  approvalAgencyId: this.approvalAgency[0].id,
                });
              }
            });
          }
        });
      }
      console.log('approvalAgency', this.approvalAgency);
    }
  }

  
  setdossierTaskStatus() {
    return new Promise<void>(resolve => {
      const tagWaitingForApprovalStatusId = this.allowNextStepWaitingForApproval ? this.deploymentService.env.dossierTaskStatus.waitingForApprovalToCancel.id : this.deploymentService.env.dossierTaskStatus.cancelDossier.id;
      this.dossierService.getAgencyTag(tagWaitingForApprovalStatusId).subscribe(data => {
        this.dossierTaskStatusWaitingForApproval.id = data.id;
        this.dossierTaskStatusWaitingForApproval.name = data.trans;
        resolve();
      }, err => {
        resolve();
      });
    });

  }

  setdossierTaskStatusRemind () {
    return new Promise<void>(resolve => {
      const tagWaitingForApprovalRemindId = this.allowNextStepWaitingForApproval ? this.deploymentService.env.dossierMenuTaskRemind.waitingForApprovalToCancel.id : this.deploymentService.env.dossierMenuTaskRemind.cancelDossier.id;
      this.dossierService.getAgencyTag(tagWaitingForApprovalRemindId).subscribe(data => {
        this.dossierMenuTaskRemindWaitingForApproval.id = data.id;
        this.dossierMenuTaskRemindWaitingForApproval.name = data.trans;
        this.statusName = data?.trans.filter(t => t.languageId === Number(this.selectedLangId))?.name;
        resolve();
      }, err => {
        resolve();
      });
    });
  }


}

export class LogSyncDialogModel {
  constructor(public dossierId: string, public dossierCode: string) {
  }
}
