
::ng-deep {
    .mat-dialog-container {
        .icon {
            width: 100%;
            text-align: center;
            padding: 2em 0;

            .mat-icon {
                transform: scale(5);
                background: linear-gradient(120deg, #EE5764 8%, #FFAB91 86%);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
            }
        }

        .content {
            margin-top: 1em;
            padding: .5em;
            text-align: center;
            color: #1E2F41;
            font-weight: 500;
            font-size: 16px;

            word-wrap: break-word;
            max-width: 100%;
            max-height: 400px;
            overflow-y: scroll;

            .highlight {
                color: #CE7A58;
                max-width: 100%;
            }
            p {
                display: block;
                #text {
                    max-width: 100%;
                }
            }
            span{
                font-size: 12px !important;
            }
        }

        .btn_ctrl {
            text-align: center;
            width: 100%;
            margin-top: 2em;

            .applyBtn {
                background-color: #CE7A58;
                color: #fff;
                height: 3em;
                width: 40%;
            }
        }
    }
}
