import { Component, Inject, OnInit } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { SnackbarService } from 'src/app/data/service/snackbar/snackbar.service';
import { Subscription } from 'rxjs';
import { MatTableDataSource } from '@angular/material/table';
import { EnvService } from 'core/service/env.service';
import { StatisticsService } from 'data/service/statistics/statistics.service';
import { DeploymentService } from 'data/service/deployment.service';
import { Router } from '@angular/router';
import { DossierService } from 'src/app/data/service/dossier/dossier.service';
import { DatePipe } from '@angular/common';
import { Workbook } from 'exceljs';
import * as fs from 'file-saver';
import { DLKStatisticsService } from 'src/app/data/service/dlk-statistics/dlk-statistics.service';
import { MatTableModule } from '@angular/material/table'  
export class DossierDetailDialogModel {
  // tslint:disable-next-line:max-line-length
  constructor(public AgencyId,public AgencyName,public TrongKy,public DaXuLy,public TrucTiep,public BCCI,public MotPhan,public ToanTrinh,public fromDate,public toDate,public totalElements,public flag) { }
}
export class DossierDetail25DialogModel {
  // tslint:disable-next-line:max-line-length
  constructor(public AgencyId,public AgencyName,public TrongKy,public tongSoHoSo,public soHoSoTonKyTruoc,public soHoSoTN,public soHoSoDXL,public soHoSoDXLTrongHan,public soHoSoDXLQuaHan,public soHoSoTON,public soHoSoTONCONHAN,public soHoSoTONQUAHAN,public fromDate,public toDate,public listAgency,public totalElements,public flag, public listProcedure,public isDV) { }
}
export class procedureDetailDialogModel {
  // tslint:disable-next-line:max-line-length
  constructor(public AgencyId,public AgencyName,public LevelId,public flag) { }
}

export interface DossierStatisticDetail {
  id: string;
  no: number;
  dossierCode: string;            // Mã số hồ sơ
  procedureName: string; 
  procedureLevel: string;         // Tên thủ tục
  sectorName: string;             // Tên lĩnh vực
  noiDungYeuCauGiaiQuyet: string; // QNI
  acceptedDate: string;           // Ngày tiếp nhận
  appointmentDate: string;        // Ngày hẹn trả
  completedDate: string;          // Ngày kết thúc xử lý
  applicantOwnerFullName: string; // Chủ hồ sơ
  applicantPhoneNumber: string;   // Số điện thoại
  assigneeFullname: string;       // Cán bộ xử lý hiện tại
  dossierStatusName: string;      // Trạng thái
  procedureId: string;
  PhoneNumber: string;      // Trạng thái
  Address: string;      // Trạng thái
  returnedDate: string;      // Ngày trả kq
  applyMethod: string;  // hình thức tiếp nhận
  ReceivingKind: string;  // hình thức  nhận kết quả
  
}
export interface ProcudureStatisticDetail {
  id: string;
  no: number;
  Code: string;            // Mã số hồ sơ
  sectorName: string; 
  procedureName: string;         // Tên thủ tục
  legalGrounds:string;
  Level: string;  
  agencyLevel: string; 
  AgencyName: string;          // Ngày tiếp nhận
}
@Component({
  selector: 'app-view-detail-CT08',
  templateUrl: './view-detail.component.html',
  styleUrls: ['./view-detail.component.scss']
})
export class DossierDetailComponent implements OnInit {
  config = this.envService.getConfig();
  subscription: Subscription;
  isAssignee = this.deploymentService.env.statistics.isAssignee;
  paginationType = this.deploymentService.env.statistics.paginationType;
  displayedColumnsDefault: string[] = ['no', 'dossierCode', 'procedureName', 'procedureLevel','sectorName','applicantOwnerFullName',  'applicantPhoneNumber','AgencyName','AgencyAccepterName', 'acceptedDate', 'appointmentDate', 'completedDate','withdrawDate',  'returnedDate','ReceivingKind','applyMethod'];
  displayedColumnsAssignee: string[] = ['no', 'dossierCode', 'procedureName', 'procedureLevel','sectorName','applicantOwnerFullName', 'applicantPhoneNumber', 'AgencyName','AgencyAccepterName', 'acceptedDate', 'appointmentDate', 'completedDate','withdrawDate', 'returnedDate','ReceivingKind', 'applyMethod'];
  // tslint:disable-next-line:max-line-lengt+h
  displayedColumns: string[] = this.isAssignee ? this.displayedColumnsAssignee : this.displayedColumnsDefault;
  dataSource: MatTableDataSource<DossierStatisticDetail>;
  ELEMENTDATA: DossierStatisticDetail[] = [];

  displayedColumnsDefault1: string[] = ['no', 'Code', 'sectorName','AgencyName', 'procedureName','agencyLevel','Level'];
  displayedColumnsAssignee1: string[] = ['no', 'Code', 'sectorName','AgencyName', 'procedureName','agencyLevel','Level'];  // tslint:disable-next-line:max-line-lengt+h
  displayedColumns1: string[] = this.isAssignee ? this.displayedColumnsAssignee1 : this.displayedColumnsDefault1;
  dataSource1: MatTableDataSource<ProcudureStatisticDetail>;
  ELEMENTDATA1: ProcudureStatisticDetail[] = [];

  size = 10;
  page = 1;
  totalPage = 0;
  pageIndex = 1; 
  countResult = 0;
  pgSizeOptions = this.config.pageSizeOptions;
  isWaitingData = false;
  isExportExcel = false;
  dataExport = [];
  EXCEL_EXTENSION = '.xlsx';
  loaiThongKe = "";
  EXCEL_TYPE = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8';
  constructor(
    public dialogRef: MatDialogRef<DossierDetailComponent>,
    @Inject(MAT_DIALOG_DATA) public data: DossierDetailDialogModel,
    @Inject(MAT_DIALOG_DATA) public data2: DossierDetail25DialogModel,
    @Inject(MAT_DIALOG_DATA) public data1: procedureDetailDialogModel,
    private snackbarService: SnackbarService,
    private envService: EnvService,
    private deploymentService: DeploymentService,
    private statisticsService: StatisticsService,
    private router: Router,
    private dossierService: DossierService,
    private datePipe: DatePipe,
    private dlkStatisticService: DLKStatisticsService
  ) {
    this.dataSource = new MatTableDataSource(this.ELEMENTDATA);
    this.dataSource1 = new MatTableDataSource(this.ELEMENTDATA1);
  }

  ngOnInit(): void {
    if(this.data.flag == 0)
    {
      this.GetDetailDossier(this.page-1,this.size);
    }
    if(this.data1.flag == 1)
    {
      this.GetDetailProcedure(this.page-1,this.size);
    }
    if(this.data2.flag == 2)
    {
      this.GetDetailCT25Dossier(this.page-1,this.size,false);      
    }
    if(this.data2.flag == 3)
    {
      this.GetDetailCT25Dossier(this.page-1,this.size,true);
    }
  
  }

  getDossierStatisticDetail(page, size) {
    this.ELEMENTDATA = [];
    this.dataSource.data = [];
    this.isWaitingData = true;

    let searchString = '?from-date=' + (this.data.fromDate + 'T00:00:00.000Z') + '&to-date=' + (this.data.toDate + 'T23:59:59.999Z')
      + '&agency-id=' + this.data.AgencyId ;//+ '&statistic-type=' + this.data.tabIndex;
    // if (!!this.data.procedureId) {
    //   searchString += '&procedure-id=' + this.data.procedureId;
    // }
    // if (!!this.data.procedureAgencyLevel) {
    //   searchString += '&procedure-agency-level=' + this.data.procedureAgencyLevel;
    // }
    // if (!!this.data.procedureAgencyId) {
    //   searchString += '&procedure-agency-id=' + this.data.procedureAgencyId;
    // }

    this.subscription = this.statisticsService.getDossierStatistic022017DetailSNV(searchString).subscribe(data => {
      this.ELEMENTDATA = data?.content;
      this.dataSource.data = this.ELEMENTDATA;
      this.countResult = data?.totalElements;
      this.totalPage = data?.totalPages;
      this.isWaitingData = false;
    }, () => {
      this.isWaitingData = false;
    });

  }
  GetDetailDossier(page, size) {
    var  paramsDossier= {
      page: page,
      size: size,
      fromDate: null,
      toDate:null,
      agencyId:null,
      applyMethodId:null,
      receivingKind: null,
      hinhThucNop: null,
      keyword: '',
      dossierStatusId:null,
      procedureLevelId:null,
      code: ''
    };
    paramsDossier.agencyId =this.data.AgencyId;
    paramsDossier.fromDate = (this.data.fromDate ? this.datePipe.transform(this.data.fromDate, 'yyyy-MM-dd') : '');
    paramsDossier.toDate = (this.data.toDate ? this.datePipe.transform(this.data.toDate, 'yyyy-MM-dd') : '');

    if(this.data.DaXuLy == 1)
    {
      paramsDossier.dossierStatusId  ="1";
    }
    if(this.data.BCCI == 1)
    {
      paramsDossier.hinhThucNop  ="1";
    }
    if(this.data.TrucTiep == 1)
    {
      paramsDossier.applyMethodId  ="1";
    }
    if(this.data.MotPhan ==2 && this.data.ToanTrinh == 2)
    {
      paramsDossier.applyMethodId  ="0";
    }
    if(this.data.MotPhan ==1)
    {
      paramsDossier.procedureLevelId  ="5f5b2c4b4e1bd312a6f3ae24";
    }
    if(this.data.ToanTrinh ==1)
    {
      paramsDossier.procedureLevelId  ="5f5b2c564e1bd312a6f3ae25";
    }

    this.dlkStatisticService.getlistDetailofDossier(paramsDossier).subscribe(data => {
      this.ELEMENTDATA = data.dossier;
      this.dataSource.data = this.ELEMENTDATA;
      this.countResult = data.totalRow[0].value;
      this.totalPage = Math.floor(this.data.totalElements/this.size);
      if((this.data.totalElements%this.size)>0)
      {
        this.totalPage ++;
      }

    }, err => {
      console.log(err);
    });
} 

GetDetailCT25Dossier(page, size,isSH) {
  var  paramsDossier= {
    page: page,
    size: size,
    fromDate: null,
    toDate:null,
    agencyId:null,
    tongSoHoSo:null,
    tongSoTon: null,
    tongSoTN: null,
    tongSoDungHan: null,
    tongSoDXL:null,
    tongSoCXL:null,
    tongSoQuaHan: null,
    tongSoTonQuaHan: null,
    tongSoTonConHan: null,
    keyword: '',
    dossierStatusId:null,
    procedureLevelId:null,
    listProcedureId:[],
    code: '',
    isDV: 0
  };
  paramsDossier.agencyId =this.data.AgencyId;
  paramsDossier.fromDate = (this.data.fromDate ? this.data.fromDate : '');
  paramsDossier.toDate = (this.data.toDate ? this.data.toDate : '');

  if(this.data2.isDV == 1){
    paramsDossier.listProcedureId = this.data2.listProcedure;
    paramsDossier.isDV = 1;
  }

  if(this.data2.tongSoHoSo == 1)
  {
      paramsDossier.tongSoHoSo  ="1";
      this.loaiThongKe = "SỐ LƯỢNG HỒ SƠ TRONG KỲ";
  }
  if(this.data2.soHoSoTonKyTruoc == 1)
  {
      paramsDossier.tongSoTon  ="1";
      this.loaiThongKe = "HỒ SƠ KỲ TRƯỚC CHUYỂN SANG";

  }
  if(this.data2.soHoSoTN == 1)
  {
      paramsDossier.tongSoTN  ="1";
      this.loaiThongKe = "HỒ SƠ TIẾP NHẬN TRONG KỲ";

  }
  if(this.data2.soHoSoDXL == 1)
  {
      paramsDossier.tongSoDXL  ="1";
      this.loaiThongKe = "HỒ SƠ ĐÃ XỬ LÝ";

  }
  if(this.data2.soHoSoDXLTrongHan == 1)
  {
      paramsDossier.tongSoDungHan  ="1";
      this.loaiThongKe = "HỒ SƠ ĐÃ XỬ LÝ ĐÚNG HẠN";

  }
  if(this.data2.soHoSoDXLQuaHan == 1)
  {
      paramsDossier.tongSoQuaHan  ="1";
      this.loaiThongKe = "HỒ SƠ ĐÃ XỬ LÝ QUÁ HẠN";

  }
  if(this.data2.soHoSoTON == 1)
  {
      paramsDossier.tongSoCXL  ="1";
      this.loaiThongKe = "HỒ SƠ CHƯA XỬ LÝ";

  }
  if(this.data2.soHoSoTONCONHAN == 1)
  {
      paramsDossier.tongSoTonConHan  ="1";
      this.loaiThongKe = "HỒ SƠ CHƯA XỬ LÝ CÒN HẠN";

  }
  if(this.data2.soHoSoTONQUAHAN == 1)
  {
      paramsDossier.tongSoTonQuaHan  ="1";
      this.loaiThongKe = "HỒ SƠ CHƯA XỬ QUÁ HẠN";

  }
  if(isSH == true){
    this.dlkStatisticService.getlistDetailof25SHDossier(paramsDossier).subscribe(data => {
      this.ELEMENTDATA =  data.dossier;
      this.dataSource.data = this.ELEMENTDATA;
      this.countResult = data.totalRow[0].value;
      //this.totalPage = data?.totalPages;
      this.totalPage = Math.floor(this.data.totalElements/this.size);
      if((this.data.totalElements%this.size)>0)
      {
        this.totalPage ++;
      }
    }, err => {
      console.log(err);
    });}
  else{
    this.dlkStatisticService.getlistDetailof25Dossier(paramsDossier).subscribe(data => {
      this.ELEMENTDATA =  data.dossier;
      this.dataSource.data = this.ELEMENTDATA;
      this.countResult = data.totalRow[0].value;
      //this.totalPage = data?.totalPages;
      this.totalPage = Math.floor(this.data.totalElements/this.size);
      if((this.data.totalElements%this.size)>0)
      {
        this.totalPage ++;
      }
  
    }, err => {
      console.log(err);
    });
  }
} 

exportexcel()
{
  if(this.data.flag == 0)
  {
    this.getDossierStatisticDetailExport();
  }
  if(this.data2.flag == 2)
    {
      this.getDossierStatisticDetailCT25Export(false);
    }
  if(this.data2.flag == 3)
    {
      this.getDossierStatisticDetailCT25Export(true);
    }
  if(this.data1.flag == 1)
  {
    this.getProcedureStatisticDetailExport();
  }
}
  getDossierStatisticDetailExport() {
    // this.isExportExcel = true;
    // let searchString = '?from-date=' + (this.data.fromDate + 'T00:00:00.000Z') + '&to-date=' + (this.data.toDate + 'T23:59:59.999Z')
    //   + '&agency-id=' + this.data.AgencyId 
    //       // if (!!this.data.procedureId) {
    // //   searchString += '&procedure-id=' + this.data.procedureId;
    // // }
    // // if (!!this.data.procedureAgencyLevel) {
    // //   searchString += '&procedure-agency-level=' + this.data.procedureAgencyLevel;
    // // }
    // // if (!!this.data.procedureAgencyId) {
    // //   searchString += '&procedure-agency-id=' + this.data.procedureAgencyId;
    // // }
    // return this.statisticsService.getDossierStatistic022017DetailSNVExport(searchString).toPromise();
    console.log("getDossierStatisticDetailExport");
    var  paramsDossier= {
      page: 0,
      size: 99999999,
      fromDate: null,
      toDate:null,
      agencyId:null,
      applyMethodId:null,
      receivingKind: null,
      hinhThucNop: null,
      keyword: '',
      dossierStatusId:null,
      procedureLevelId:null,
      code: ''
    };
    paramsDossier.agencyId =this.data.AgencyId;
    paramsDossier.fromDate = (this.data.fromDate ? this.datePipe.transform(this.data.fromDate, 'yyyy-MM-dd') : '');
    paramsDossier.toDate = (this.data.toDate ? this.datePipe.transform(this.data.toDate, 'yyyy-MM-dd') : '');

    if(this.data.DaXuLy == 1)
    {
      paramsDossier.dossierStatusId  ="1";
    }
    if(this.data.BCCI == 1)
    {
      paramsDossier.hinhThucNop  ="1";
    }
    if(this.data.TrucTiep == 1)
    {
      paramsDossier.applyMethodId  ="1";
    }
    if(this.data.MotPhan ==2 && this.data.ToanTrinh == 2)
    {
      paramsDossier.applyMethodId  ="0";
    }
    if(this.data.MotPhan ==1)
    {
      paramsDossier.procedureLevelId  ="5f5b2c4b4e1bd312a6f3ae24";
    }
    if(this.data.ToanTrinh ==1)
    {
      paramsDossier.procedureLevelId  ="5f5b2c564e1bd312a6f3ae25";
    }
    this.dlkStatisticService.getlistDetailofDossier(paramsDossier).subscribe(data => {
      this.dataExport = data.dossier;
      this.exportToExcelHCM() 
    }, err => {
      console.log(err);
    });
  }

  getDossierStatisticDetailCT25Export(isSH) {
    const paramsDossier = {
      page: 0,
      size: 7000,
      fromDate: this.data.fromDate ? this.data.fromDate : '',
      toDate: this.data.toDate ? this.data.toDate : '',
      agencyId: this.data.AgencyId,
      tongSoHoSo: null,
      tongSoTon: null,
      tongSoTN: null,
      tongSoDungHan: null,
      tongSoDXL: null,
      tongSoCXL: null,
      tongSoQuaHan: null,
      tongSoTonQuaHan: null,
      tongSoTonConHan: null,
      keyword: '',
      dossierStatusId: null,
      procedureLevelId: null,
      code: ''
    };
  
    if (this.data2.tongSoHoSo == 1) {
      paramsDossier.tongSoHoSo = "1";
      this.loaiThongKe = "SỐ LƯỢNG HỒ SƠ TRONG KỲ";
    }
    if (this.data2.soHoSoTonKyTruoc == 1) {
      paramsDossier.tongSoTon = "1";
      this.loaiThongKe = "HỒ SƠ KỲ TRƯỚC CHUYỂN SANG";
    }
    if (this.data2.soHoSoTN == 1) {
      paramsDossier.tongSoTN = "1";
      this.loaiThongKe = "HỒ SƠ TIẾP NHẬN TRONG KỲ";
    }
    if (this.data2.soHoSoDXL == 1) {
      paramsDossier.tongSoDXL = "1";
      this.loaiThongKe = "HỒ SƠ ĐÃ XỬ LÝ";
    }
    if (this.data2.soHoSoDXLTrongHan == 1) {
      paramsDossier.tongSoDungHan = "1";
      this.loaiThongKe = "HỒ SƠ ĐÃ XỬ LÝ ĐÚNG HẠN";
    }
    if (this.data2.soHoSoDXLQuaHan == 1) {
      paramsDossier.tongSoQuaHan = "1";
      this.loaiThongKe = "HỒ SƠ ĐÃ XỬ LÝ QUÁ HẠN";
    }
    if (this.data2.soHoSoTON == 1) {
      paramsDossier.tongSoCXL = "1";
      this.loaiThongKe = "HỒ SƠ CHƯA XỬ LÝ";
    }
    if (this.data2.soHoSoTONCONHAN == 1) {
      paramsDossier.tongSoTonConHan = "1";
      this.loaiThongKe = "HỒ SƠ CHƯA XỬ LÝ CÒN HẠN";
    }
    if (this.data2.soHoSoTONQUAHAN == 1) {
      paramsDossier.tongSoTonQuaHan = "1";
      this.loaiThongKe = "HỒ SƠ CHƯA XỬ QUÁ HẠN";
    }
  
    let allData = [];
    let currentPage = 0;
  
    const fetchPage = () => {
      paramsDossier.page = currentPage;
      let serviceCall;
      if (isSH) {
        serviceCall = this.dlkStatisticService.getlistDetailof25SHDossier(paramsDossier);
      } else {
        serviceCall = this.dlkStatisticService.getlistDetailof25Dossier(paramsDossier);
      }
      serviceCall.subscribe(data => {
        allData = allData.concat(data.dossier);
        const totalRows = data.totalRow[0].value;
        if (allData.length < totalRows) {
          currentPage++;
          fetchPage();
        } else {
          this.dataExport = allData;
          this.exportToExcelHCM();
        }
      }, err => {
        console.log(err);
      });
    };
  
    // Bắt đầu lấy dữ liệu từ trang đầu tiên
    fetchPage();
  }

  async exportToExcelHCM() {
    
    //this.dataExport = await this.getDossierStatisticDetailExport();
    this.isExportExcel = false;
    
    const from = this.datePipe.transform(this.data.fromDate, 'dd/MM/yyyy');
    const to = this.datePipe.transform(this.data.toDate, 'dd/MM/yyyy');
    const newDate = this.datePipe.transform(new Date(), "dd/MM/yyyy HH:ss:mm")
    const excelFileName = `Bao_cao_chi_tiet_theo_thu_tuc_${newDate}`;
    let headerXLS = {
      row1: "CỘNG HÒA XÃ HỘI CHỦ NGHĨA VIỆT NAM",
      row2: "Độc lập - Tự do - Hạnh phúc",
      row3: "BÁO CÁO TÌNH HÌNH TIẾP NHẬN VÀ GIẢI QUYẾT HỒ SƠ - CHI TIẾT HỒ SƠ  "+ this.loaiThongKe,
      row4: `(Từ ${from} đến ngày ${to})`,
      row5: `Thống kê vào lúc ${newDate}`
    }

    let footerXLS = {
      col1: "NGƯỜI LẬP",
      col2: "NGƯỜI BÁO CÁO"
    }
    const workbook = new Workbook();
    workbook.creator = 'Snippet Coder';
    workbook.lastModifiedBy = 'SnippetCoder';
    workbook.created = new Date();
    workbook.modified = new Date();
    const worksheet = workbook.addWorksheet("sheet1");

    // Add header row
    worksheet.addRow([]);
    worksheet.mergeCells('A1:K1');
    worksheet.getCell('A1').value = headerXLS.row1;
    worksheet.getCell('A1').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getCell('A1').font = {size: 13, name: 'Times New Roman'};


    worksheet.mergeCells('A2:K2');
    worksheet.getCell('A2').value = headerXLS.row2;
    worksheet.getCell('A2').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getCell('A2').font = {size: 13, underline: true, name: 'Times New Roman'};

    worksheet.mergeCells('A4:K4');
    worksheet.getCell('A4').value = headerXLS.row3;
    worksheet.getCell('A4').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getCell('A4').font = {size: 13, bold: true, name: 'Times New Roman'};

    worksheet.mergeCells('A5:K5');
    worksheet.getCell('A5').value = headerXLS.row4;
    worksheet.getCell('A5').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getCell('A5').font = {size: 11, italic: true, name: 'Times New Roman'};

    worksheet.mergeCells('H6:K6');
    worksheet.getCell('H6').value = headerXLS.row5;
    worksheet.getCell('H6').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getCell('H6').font = {size: 11, italic: true, name: 'Times New Roman'};

    const headerContent = ['STT', 'Số hồ sơ', 'Tên thủ tục hành chính', 'Mức độ','Lĩnh vực', 'Tổ chức, cá nhân', 'Địa chỉ, SĐT', '	Cơ quan chủ trì','	Cơ quan tiếp nhận', 'Ngày tiếp nhận', 
    'Ngày hẹn trả', 'Ngày có kết quả/yc trả lại dân', 'Ngày trả kết quả/trả lại dân','Hình thức nhận KQ','Hình thức tiếp nhận']
    const rowStartHeaderContent = 8;
    for (let index = 0; index < 15; index++) {
      worksheet.getCell(rowStartHeaderContent, (index + 1)).value = headerContent[index];
      worksheet.getCell(rowStartHeaderContent, (index + 1)).alignment = { horizontal: 'center', vertical: 'middle',  };
      worksheet.getCell(rowStartHeaderContent, (index + 1)).font = {size: 11, bold: true, name: 'Times New Roman'};
      worksheet.getCell(rowStartHeaderContent, (index + 1)).border = {top: {style: 'thin'}, left: {style: 'thin'}, bottom: {style: 'thin'}, right: {style: 'thin'}};
    }

    let rowStartContent = rowStartHeaderContent + 1;
    const data = this.dataExport;
    for (let r = 0; r < data.length; r++) {
      worksheet.getCell(rowStartContent + r, 1).value = r + 1;
      worksheet.getCell(rowStartContent + r, 2).value = data[r].dossierCode;
      worksheet.getCell(rowStartContent + r, 3).value = data[r].procedureName;
      worksheet.getCell(rowStartContent + r, 4).value = data[r].procedureLevel;
      worksheet.getCell(rowStartContent + r, 5).value = data[r].sectorName;
      worksheet.getCell(rowStartContent + r, 6).value = data[r].applicantOwnerFullName;
      worksheet.getCell(rowStartContent + r, 7).value = data[r].PhoneNumber + " " + data[r].Address ;
      worksheet.getCell(rowStartContent + r, 8).value = this.data.AgencyName;
      worksheet.getCell(rowStartContent + r, 9).value = data[r].agencyName;
      worksheet.getCell(rowStartContent + r, 10).value =  this.datePipe.transform(data[r].acceptedDate, "dd/MM/yyyy HH:ss:mm");
      worksheet.getCell(rowStartContent + r, 11).value =this.datePipe.transform(data[r].appointmentDate, "dd/MM/yyyy HH:ss:mm");  
      worksheet.getCell(rowStartContent + r, 12).value =this.datePipe.transform(data[r].completedDate, "dd/MM/yyyy HH:ss:mm");  
      worksheet.getCell(rowStartContent + r, 13).value =this.datePipe.transform(data[r].returnedDate, "dd/MM/yyyy HH:ss:mm"); 
      worksheet.getCell(rowStartContent + r, 14).value = data[r].ReceivingKind;
      worksheet.getCell(rowStartContent + r, 15).value = data[r].applyMethod;
      for (let c = 0; c < 15; c++) {
        if(c != 1){
          worksheet.getCell(rowStartContent + r, (c + 1)).alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
        }else{
          worksheet.getCell(rowStartContent + r, (c + 1)).alignment = { horizontal: 'left', vertical: 'middle', wrapText: true };
        }
        worksheet.getCell(rowStartContent + r, (c + 1)).font = {size: 11, name: 'Times New Roman'};
        worksheet.getCell(rowStartContent + r, (c + 1)).border = {top: {style: 'thin'}, left: {style: 'thin'}, bottom: {style: 'thin'}, right: {style: 'thin'}};
      }
      
     
    }

    let rowEndContent = rowStartContent + data.length;
    let rowFooter = rowEndContent + 2;

    worksheet.getCell(rowFooter, 2).value = footerXLS.col1;
    worksheet.getCell(rowFooter, 2).font = {size: 13, bold: true, name: 'Times New Roman'};

    worksheet.mergeCells("H" + rowFooter, "K" + rowFooter);
    worksheet.getCell(rowFooter, 8).value = footerXLS.col2;
    worksheet.getCell(rowFooter, 8).font = {size: 13, bold: true, name: 'Times New Roman'};

    worksheet.getColumn(1).width = 7;
    worksheet.getColumn(1).alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
    worksheet.getColumn(2).width = 20;
    worksheet.getColumn(2).alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
    worksheet.getColumn(3).width = 100;
    worksheet.getColumn(3).alignment = {horizontal: 'left', vertical: 'middle', wrapText: true};
    worksheet.getColumn(4).width = 20;
    worksheet.getColumn(4).alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
    worksheet.getColumn(5).width = 20;
    worksheet.getColumn(5).alignment = {horizontal: 'left', vertical: 'middle', wrapText: true};
    worksheet.getColumn(6).width = 25;
    worksheet.getColumn(6).alignment = {horizontal: 'left', vertical: 'middle', wrapText: true};
    worksheet.getColumn(7).width = 20;
    worksheet.getColumn(7).alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
    worksheet.getColumn(8).width = 20;
    worksheet.getColumn(8).alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
    worksheet.getColumn(9).width = 17;
    worksheet.getColumn(9).alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
    worksheet.getColumn(10).width = 17;
    worksheet.getColumn(10).alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
    worksheet.getColumn(11).width = 17;
    worksheet.getColumn(11).alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
    worksheet.getColumn(12).width = 15;
    worksheet.getColumn(12).alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
    worksheet.getColumn(13).width = 15;
    worksheet.getColumn(13).alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};

    worksheet.getRow(8).alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};


    // Save Excel File
    workbook.xlsx.writeBuffer().then((dataBuffer: ArrayBuffer) => {
      const blob = new Blob([dataBuffer], { type: this.EXCEL_TYPE });
      fs.saveAs(blob, excelFileName + this.EXCEL_EXTENSION);
    });
  }

  paginate(event: any, type) {
    if(this.data.flag == 0)
    {
      switch (type) {
        case 0:
          this.pageIndex = event;
          this.page = event;
          this.GetDetailDossier(this.pageIndex - 1, this.size);
          break;
        case 1:
          this.pageIndex = 1;
          this.page = 1;
          this.GetDetailDossier(this.pageIndex - 1, this.size);
          break;
      }
    }
    if(this.data2.flag == 2)
      {
        switch (type) {
          case 0:
            this.pageIndex = event;
            this.page = event;
            this.GetDetailCT25Dossier(this.pageIndex - 1, this.size,false);
            break;
          case 1:
            this.pageIndex = 1;
            this.page = 1;
            this.GetDetailCT25Dossier(this.pageIndex - 1, this.size,false);
            break;
        }
      }
      if(this.data2.flag == 3)
        {
          switch (type) {
            case 0:
              this.pageIndex = event;
              this.page = event;
              this.GetDetailCT25Dossier(this.pageIndex - 1, this.size,true);
              break;
            case 1:
              this.pageIndex = 1;
              this.page = 1;
              this.GetDetailCT25Dossier(this.pageIndex - 1, this.size,true);
              break;
          }
        }
    if(this.data1.flag == 1)
    {
      switch (type) {
        case 0:
          this.pageIndex = event;
          this.page = event;
          this.GetDetailProcedure(this.pageIndex - 1, this.size);
          break;
        case 1:
          this.pageIndex = 1;
          this.page = 1;
          this.GetDetailProcedure(this.pageIndex - 1, this.size);
          break;
      }
    }

  }

  onClose() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
    this.dialogRef.close();
  }

  GetDetailProcedure(page, size) {
    this.ELEMENTDATA1= [];
    //this.dataSource1.data = [];
    this.isWaitingData = true;
      let searchString  = "?agency-id=" + this.data.AgencyId +"&level-id="+ this.data1.LevelId+'&page=' + (page) + '&size=' + size;
      if(this.data1.LevelId =='0')
      {
        searchString  = "?agency-id=" + this.data.AgencyId +'&page=' + (page) + '&size=' + size;
      }
    
      this.dlkStatisticService.getDeatailProcedureByAgency(searchString ).subscribe(data => {
      

      console.log("GetDetailProcedure");     
      console.log(searchString);    
      console.log(data);
 
        this.ELEMENTDATA1 = data.dossier;
        this.dataSource1.data = this.ELEMENTDATA1;
        this.countResult = data.totalRow[0].value;
        this.isWaitingData = false;
        
      }, err => {
        this.isWaitingData = false;
        console.log(err);
    });  
  }
  getProcedureStatisticDetailExport() {
    let searchString  = "?agency-id=" + this.data1.AgencyId +"&level-id="+ this.data1.LevelId+'&page=' + (this.page-1) + '&size=99999' ;
    if(this.data1.LevelId =='0')
    {
      searchString  = "?agency-id=" + this.data1.AgencyId +'&page=' + (this.page-1) + '&size=99999';
    }
   
    this.dlkStatisticService.getDeatailProcedureByAgency(searchString ).subscribe(data => {
      console.log("GetDetailProcedure");
      console.log(data  );
      this.dataExport = data.dossier;
      this.exportToExcelProcedure() 
    }, err => {
      console.log(err);
  });   
  }

  async exportToExcelProcedure() {
    
    //this.dataExport = await this.getDossierStatisticDetailExport();
    this.isExportExcel = false;
    
    const newDate = this.datePipe.transform(new Date(), "dd-MM-yyyy HH:ss:mm")
    const excelFileName = `Bao_cao_chi_tiet_theo_thu_tuc_${newDate}`;
    let headerXLS = {
      row1: "CỘNG HÒA XÃ HỘI CHỦ NGHĨA VIỆT NAM",
      row2: "Độc lập - Tự do - Hạnh phúc",
      row3: "CHI TIẾT THỦ TỤC",
      row4: "",
      row5: `Thống kê vào lúc ${newDate}`
    }

    let footerXLS = {
      col1: "NGƯỜI LẬP",
      col2: "NGƯỜI BÁO CÁO"
    }
    const workbook = new Workbook();
    workbook.creator = 'Snippet Coder';
    workbook.lastModifiedBy = 'SnippetCoder';
    workbook.created = new Date();
    workbook.modified = new Date();
    const worksheet = workbook.addWorksheet("sheet1");

    // Add header row
    worksheet.addRow([]);
    worksheet.mergeCells('A1:K1');
    worksheet.getCell('A1').value = headerXLS.row1;
    worksheet.getCell('A1').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getCell('A1').font = {size: 13, name: 'Times New Roman'};


    worksheet.mergeCells('A2:K2');
    worksheet.getCell('A2').value = headerXLS.row2;
    worksheet.getCell('A2').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getCell('A2').font = {size: 13, underline: true, name: 'Times New Roman'};

    worksheet.mergeCells('A4:K4');
    worksheet.getCell('A4').value = headerXLS.row3;
    worksheet.getCell('A4').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getCell('A4').font = {size: 13, bold: true, name: 'Times New Roman'};

    worksheet.mergeCells('A5:K5');
    worksheet.getCell('A5').value = headerXLS.row4;
    worksheet.getCell('A5').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getCell('A5').font = {size: 11, italic: true, name: 'Times New Roman'};

    worksheet.mergeCells('H6:K6');
    worksheet.getCell('H6').value = headerXLS.row5;
    worksheet.getCell('H6').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getCell('H6').font = {size: 11, italic: true, name: 'Times New Roman'};

    const headerContent = ['STT', 'Tên tắt', 'Cơ quan', 'Lĩnh vực thủ tục', 'Thủ tục', 'Lệ phí', '	Quyết định', 'Cấp thủ tục','Mức độ']
    const rowStartHeaderContent = 8;
    for (let index = 0; index < 9; index++) {
      worksheet.getCell(rowStartHeaderContent, (index + 1)).value = headerContent[index];
      worksheet.getCell(rowStartHeaderContent, (index + 1)).alignment = { horizontal: 'center', vertical: 'middle',  };
      worksheet.getCell(rowStartHeaderContent, (index + 1)).font = {size: 11, bold: true, name: 'Times New Roman'};
      worksheet.getCell(rowStartHeaderContent, (index + 1)).border = {top: {style: 'thin'}, left: {style: 'thin'}, bottom: {style: 'thin'}, right: {style: 'thin'}};
    }

    let rowStartContent = rowStartHeaderContent + 1;
    const data = this.dataExport;
    for (let r = 0; r < data.length; r++) {
      worksheet.getCell(rowStartContent + r, 1).value = r+1;
      worksheet.getCell(rowStartContent + r, 2).value = data[r].Code;
      worksheet.getCell(rowStartContent + r, 3).value = data[r].AgencyName;
      worksheet.getCell(rowStartContent + r, 4).value = data[r].sectorName;
      worksheet.getCell(rowStartContent + r, 5).value = data[r].Name;
      worksheet.getCell(rowStartContent + r, 6).value = data[r].Fee ;
      worksheet.getCell(rowStartContent + r, 7).value = data[r].legalGrounds;
      worksheet.getCell(rowStartContent + r, 8).value = data[r].agencyLevel;
      worksheet.getCell(rowStartContent + r, 9).value = data[r].Level;
      for (let c = 0; c < 9; c++) {
        worksheet.getCell(rowStartContent + r, (c + 1)).alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
        worksheet.getCell(rowStartContent + r, (c + 1)).font = {size: 11, name: 'Times New Roman'};
        worksheet.getCell(rowStartContent + r, (c + 1)).border = {top: {style: 'thin'}, left: {style: 'thin'}, bottom: {style: 'thin'}, right: {style: 'thin'}};
      }
     
    }

    let rowEndContent = rowStartContent + data.length;
    let rowFooter = rowEndContent + 2;

    // worksheet.getCell(rowFooter, 2).value = footerXLS.col1;
    // worksheet.getCell(rowFooter, 2).font = {size: 13, bold: true, name: 'Times New Roman'};

    // worksheet.mergeCells("H" + rowFooter, "K" + rowFooter);
    // worksheet.getCell(rowFooter, 8).value = footerXLS.col2;
    // worksheet.getCell(rowFooter, 8).font = {size: 13, bold: true, name: 'Times New Roman'};

    worksheet.getColumn(1).width = 7;
    worksheet.getColumn(1).alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
    worksheet.getColumn(2).width = 20;
    worksheet.getColumn(2).alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
    worksheet.getColumn(3).width = 20;
    worksheet.getColumn(3).alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
    worksheet.getColumn(4).width = 20;
    worksheet.getColumn(4).alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
    worksheet.getColumn(5).width = 40;
    worksheet.getColumn(5).alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
    worksheet.getColumn(6).width = 20;
    worksheet.getColumn(6).alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
    worksheet.getColumn(7).width = 20;
    worksheet.getColumn(7).alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
    worksheet.getColumn(8).width = 20;
    worksheet.getColumn(8).alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
    worksheet.getColumn(9).width = 20;
    worksheet.getColumn(9).alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};

    // Save Excel File
    workbook.xlsx.writeBuffer().then((dataBuffer: ArrayBuffer) => {
      const blob = new Blob([dataBuffer], { type: this.EXCEL_TYPE });
      fs.saveAs(blob, excelFileName + this.EXCEL_EXTENSION);
    });
  }

  paginate1(event: any, type) {
    switch (type) {
      case 0:
        this.pageIndex = event;
        this.page = event;
        this.GetDetailDossier(this.pageIndex - 1, this.size);
        break;
      case 1:
        this.pageIndex = 1;
        this.page = 1;
        this.GetDetailDossier(this.pageIndex - 1, this.size);
        break;
    }
  }

    

}
