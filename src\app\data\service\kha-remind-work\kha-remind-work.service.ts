import { Injectable } from '@angular/core';
import { rejects } from 'assert';
import { resolve } from 'dns';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { ApiProviderService } from 'src/app/core/service/api-provider.service';
import { Observable } from 'rxjs';
import { IFormMap } from '../../schema/form-map';
@Injectable({
  providedIn: 'root'
})
export class KhaRemindWorkService {
  private remindWork = this.apiProviderService.getUrl('digo', 'padman') + '/kha-remind-notify';
  private human = this.apiProviderService.getUrl('digo', 'human');
  constructor(
    private http: HttpClient,
    private apiProviderService: ApiProviderService
  ) { }
  getListFormTerm(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    console.log('searchString:',searchString);
    return this.http.get(this.remindWork+"/first-term" + searchString, { headers });
  }
  getListAcceptTrucTiep(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    console.log('searchString:',searchString);
    return this.http.get(this.remindWork+"/accepted" + searchString + '&applyMethod=true', { headers });
  }
  getListAcceptTrucTuyen(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    console.log('searchString:',searchString);
    return this.http.get(this.remindWork+"/accepted" + searchString + '&applyMethod=false', { headers });
  }
  getListResoleDue(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    console.log('searchString:',searchString);
    return this.http.get(this.remindWork+"/resolve" + searchString + '&isDue=true', { headers });
  }
  getListResoleOverDue(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    console.log('searchString:',searchString);
    return this.http.get(this.remindWork+"/resolve" + searchString + '&isDue=false', { headers });
  }

  getListDueCBCC(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    console.log('searchString:',searchString);
    return this.http.get(this.remindWork+"/list-due-cbcc" + searchString, { headers });
  }


  getListUnresolveOnTime(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    console.log('searchString:',searchString);
    return this.http.get(this.remindWork+"/unresolve" + searchString, { headers });
  }


  getListUnresolveIsDue(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    console.log('searchString:',searchString);
    return this.http.get(this.remindWork+"/unresolve-isdue" + searchString, { headers });
  }


  getListUnresolveOverDue(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    console.log('searchString:',searchString);
    return this.http.get(this.remindWork+"/unresolve-overdue" + searchString, { headers });
  }

  getListLienThong(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    console.log('searchString:',searchString);
    return this.http.get(this.remindWork+"/search-connect" + searchString, { headers });
  }

  getUserRemind(userId: string): Observable<any>{
    const URL = `${this.human}/user-remind/${userId}`;
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get<any>(URL, { headers });
  }
}
