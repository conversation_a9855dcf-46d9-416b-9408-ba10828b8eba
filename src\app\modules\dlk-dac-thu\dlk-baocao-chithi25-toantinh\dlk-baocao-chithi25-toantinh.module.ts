import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { DlkBaocaoChithi25ToanTinhRoutingModule } from './dlk-baocao-chithi25-toantinh-routing.module';
import { DlkBaocaoChithi25ToanTinhComponent } from './dlk-baocao-chithi25-toantinh.component';
import { SharedModule } from 'src/app/shared/shared.module';
import { NgxPrinterModule } from 'ngx-printer';
import {DossierDetailComponent} from '../dialogs/view-detail.component';


@NgModule({
  declarations: [DlkBaocaoChithi25ToanTinhComponent],
  imports: [
    CommonModule,
    DlkBaocaoChithi25ToanTinhRoutingModule,    
    SharedModule,
    
    
    NgxPrinterModule.forRoot({ printOpenWindow: true })
  ]
})
export class DlkBaocaoChithi25ToanTinhModule { }
