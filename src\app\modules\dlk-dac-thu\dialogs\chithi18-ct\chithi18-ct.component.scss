.dialog_title {
  font-style: normal;
  font-weight: 500;
  font-size: 20px;
  color: #ce7a58;
}

::ng-deep .applyBtn {
  margin-top: 1em;
  background-color: #ce7a58;
  color: #fff;
  height: 3em;
  padding: 0 3em;
}

::ng-deep mat-dialog-container::-webkit-scrollbar-track {
  box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.1);
  background-color: #f5f5f5;
}

::ng-deep mat-dialog-container::-webkit-scrollbar {
  width: 5px;
  background-color: #f5f5f5;
}

::ng-deep mat-dialog-container::-webkit-scrollbar-thumb {
  box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.1);
  background-color: #44444450;
}

.close-button {
  float: right;
  top: -24px;
  right: -24px;
}

::ng-deep .dialog_content {
  font-size: 15px;
  max-height: unset;

  .highlight {
    color: #ce7a58;
  }

  .formFieldItems {
    flex-wrap: wrap;
  }

  .paymentHistory {
    padding: 0;
    border-radius: 6px;
    flex-wrap: wrap;

    .mat-table {
      border: 1px solid #33333325;
      border-radius: 4px;
      border-bottom: none;

      .mat-column-receiptNumber {
        flex: 0 0 10%;
      }

      .mat-column-payment {
        flex: 0 0 26%;
      }

      .mat-header-cell {
        background-color: #f2f2f2;
        font-weight: 500;
        color: #333;

        th {
          font-weight: 500;
          color: #333;
        }
      }

      .quantity {
        &.mat-form-field-appearance-outline {
          .mat-form-field-flex {
            min-width: 6em;
            height: 3em;
            margin: 0 0.5em 0 0;
          }

          .mat-form-field-wrapper {
            padding-bottom: 0;
          }

          .mat-form-field-infix {
            padding-top: 0.1em;
            font-size: 15px;
          }

          &.mat-focused {
            .mat-form-field-outline-thick {
              color: #33333350 !important;
            }
          }
        }
      }
    }

    .totalCell {}

    .total {
      color: #1E2F41;
      font-weight: 500;
      font-size: 14px;
      padding: .5em 1.8em;
      background-color: #f2f2f2;
      margin-top: .5em;
      border-radius: 4px;

      span:nth-child(2) {
        font-weight: 400;
        color: #ce7a58;
      }
    }

    ul {
      list-style-type: none;
      margin-left: -2em;
    }
  }
}

@media screen and (max-width: 800px) {
  ::ng-deep {
    .paymentHistory {
      .mat-column-procostType {
        float: unset;
      }

      .mat-column-pay {
        display: none;
      }

      .mat-row {
        &:nth-child(even) {
          background-color: unset;
        }

        &:nth-child(odd) {
          background-color: unset;
        }
      }

      .mat-header-row {
        display: none;
      }

      .mat-table {
        width: 100%;
        border: 0;
        vertical-align: middle;

        caption {
          font-size: 1em;
        }

        .mat-row {
          border-bottom: 5px solid #ddd;
          display: block;
          min-height: unset;
        }

        .mat-cell {
          border-bottom: 1px solid #ddd;
          display: block;
          font-size: 14px;
          text-align: right;
          margin-bottom: 4%;
          padding: 0 0.5em;

          &:before {
            content: attr(data-label);
            float: left;
            font-weight: 500;
            font-size: 14px;
          }

          &:last-child {
            border-bottom: 0;
          }

          &:first-child {
            margin-top: 4%;
          }
        }
      }
    }
  }
}

th,
td {
  border: 1px solid #ddd;
}
table.mat-table {
  width: 100%;
}

.text-justify {
  text-align: justify;
}

.text-center {
  text-align: center;
}

.bg-vnpt {
  background: #0066b3;
  color: #fff;
}

.text-bold {
  font-weight: bold;
}
