@import "~src/styles/framework.scss";
.btnReceive{
  background-color: #ce7a58 !important;
  color: white !important;
}

.btnSave{
  color: #ce7a58 !important;
  background-color: white !important;
  border: 1px solid #ce7a58 !important;
}
.groupbox{
  border: 1px solid #bdbdbd33;
}

.groupbox_header{
  color: #ce7a58;
}

::ng-deep .custom-tooltip {
  margin-top: 0px !important;
}

.divFormFile{
  margin: auto;
}
.setfileForm{
  margin-top: 5px;
  margin-bottom: 5px;
}

.openEformOnline{
  color:rgb(230, 6, 6);

  .detailopenEformOnline{
    color: black !important;
    font-weight: 100 !important;
    font-style: italic;
    font-size: 13px !important;
    padding-left: 1px !important;
  }
}

a.openEformOnline :hover{
  cursor: pointer;
}
.hidden {
  display: none !important;
}

@media screen and (max-width: 960px) {
  .openEformOnline{
    margin-top: 15px;
  }
}

::ng-deep {
  .menuAction {
    font-weight: 500;
    .mat-icon {
      color: #ce7a58 !important;
    }
  }
  .frm_main_receiving {
    margin-top: 1em;
    background-color: #fff;
    box-shadow: 4px 0px 8px rgba(0, 0, 0, 0.1);
    padding: 1em 1em 1em 1em;
    .backToPrevSite {
      margin-bottom: 0.5em;
      .mat-icon {
        color: #ce7a58;
        vertical-align: middle;
      }
      span {
        font-weight: 500;
        color: #1e2f41;
        font-size: 20px;
      }
    }
    .dossierDetail {
      .procedureName {
        font-weight: 500;
        font-size: 16px;
        line-height: 27px;
        color: #1e2f41;
      }
      .procedureLevel {
        color: #7c964c;
      }
      .sector, .agency-info {
        color: #1e2f41;
        span:nth-child(1) {
          font-weight: 500;
        }
      }
      .process {
        color: #1e2f41;
        .nameProcess {
          overflow-wrap: break-word;
        }
      }
    }
    .infoTabs {
      .valid-residential-info {
        @extend .dp-inline-flex;
        @extend .ali-center;
        @extend .color-theme;
        @extend .fw-bold;
        .check-icon {
          @extend .mr-05;
        }
      }
      .tabIcon {
        margin-right: 0.2em;
      }
      .printButton {
        color: #ce7a58;
        border: 1px solid #ce7a58;
        margin: 0.5em;
        .mat-icon {
          margin-right: 0.3em;
          vertical-align: middle;
          align-self: center;
        }
        .mat-button-wrapper {
          display: flex;
          justify-content: center;
          .mat-spinner {
            margin-right: 0.3em;
            align-self: center;
            circle {
              stroke: #ce7a58;
            }
          }
        }
      }
      .mat-tab-group.mat-primary .mat-ink-bar {
        background-color: #ce7a58;
        height: 3px;
        border-radius: 5em 5em 0 0;
      }
      .mat-tab-label {
        min-width: 20%;
        &.mat-tab-label-active {
          color: #ce7a58;
          opacity: 1;
        }
        .mat-form-field-appearance-outline {
          .mat-form-field-infix {
            padding: 0.8em 0;
          }
          .mat-form-field-outline {
            color: transparent;
            background-color: #eaebeb;
            border-radius: 5px;
          }
          .mat-form-field-outline-thick {
            color: #dddddd;
          }
        }
        .mat-form-field-label-wrapper {
          top: -1em;
        }
        .mat-form-field.mat-focused {
          .mat-form-field-label {
            color: #ce7a58;
            font-size: 14px;
          }
        }
        .mat-form-field-appearance-outline.mat-form-field-can-float.mat-form-field-should-float {
          .mat-form-field-label {
            color: #ce7a58;
            transform: translateY(-1.55em) scale(1);
            margin-bottom: 1em;
          }
        }
        .mat-form-field .error_Msg {
          font-size: 12px !important;
          float: right;
          display: flex;
          color: #ce7a58;
          .err {
            background-color: #f2a63494;
            border-radius: 50%;
            width: 1.2em;
            height: 1.2em;
            justify-content: center;
            display: flex;
            margin-left: 0.5em;
            .mat-icon {
              color: #ce7a58;
              vertical-align: middle;
              align-self: center;
              transform: scale(0.8);
            }
          }
        }
      }
      .mat-tab-body-wrapper {
        // padding: 1em 0;
      }
      .formFieldItems {
        flex-wrap: wrap;
      }
      .labelContentTitle {
        font-weight: 500;
        font-size: 14px;
        line-height: 24px;
        color: #1e2f41;
      }
      .infoForm {
        margin: 1em 0 2em 0;
      }
      .procedureForm {
        .success_head {
          text-align: center;
          font-weight: 500;
          color: #ce7a58;
          font-size: 18px;
          padding-top: 2em;
        }
        .item {
          .head {
            background-color: #bdbdbd25;
            padding: 1em;
            margin-bottom: 1em;
            border-radius: 4px;
            .requirement {
              vertical-align: middle;
              color: #ce7a58;
              margin-top: -0.1em;
            }
            span {
              padding: 0 0.5em;
              font-weight: 500;
              font-size: 15px;
              padding-top: 0.1em;
            }
            .mat-checkbox {
              .mat-checkbox-frame {
                transform: scale(1.2);
                border-width: 0.1px !important;
                opacity: 0.7;
              }
            }
            .mat-checkbox-checked {
              .mat-checkbox-background {
                transform: scale(1.2);
                background-color: #ce7a58;
              }
            }
          }
          .body {
            .mat-radio-group {
              display: flex;
              flex-flow: column nowrap;
              padding: 0 0 0 2.5em;
              .rdo_File {
                padding: 0 0.5em;
                border: 1px solid #e0e0e059;
                border-radius: 4px;
                margin-bottom: 0.5em;
                display: flex;
                flex-wrap: wrap;
                .mat-radio-button {
                  align-self: center;
                  &.mat-radio-checked {
                    .mat-radio-outer-circle {
                      border-color: #ce7a58;
                    }
                    .mat-radio-inner-circle {
                      background-color: #ce7a58;
                    }
                    &.mat-radio-disabled {
                      .mat-radio-outer-circle {
                        border-color: #33333350 !important;
                      }
                      .mat-radio-inner-circle {
                        background-color: #33333350 !important;
                      }
                    }
                  }
                }
                .file {
                  background-color: #8c8c8c2d;
                  padding-left: 0.5em;
                  border-radius: 4px;
                  margin: 0.25em 0;
                  display: flex;
                  .icon {
                    align-self: center;
                    width: 2em;
                    height: 2em;
                    background-position: center;
                    background-repeat: no-repeat;
                    background-size: 100%;
                    margin-right: 0.5em;
                  }
                  .name {
                    align-self: center;
                    color: #1e2f41;
                    width: 80%;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                  }
                  .deleteFile {
                    align-self: center;
                  }
                }
              }
            }
            .quantity {
              &.mat-form-field-appearance-outline {
                .mat-form-field-flex {
                  width: 4em;
                  height: 3em;
                  margin: 0 0.5em;
                }
                .mat-form-field-wrapper {
                  padding-bottom: 0;
                }
                .mat-form-field-infix {
                  padding-top: 0.1em;
                  font-size: 15px;
                }
                &.mat-focused {
                  .mat-form-field-outline-thick {
                    color: #33333350 !important;
                  }
                }
              }
            }
            .typeQBH {
              &.mat-form-field-appearance-outline {
                .mat-form-field-flex {
                  width: 10em;
                  height: 3em;
                  margin: 0 0.5em;
                }
                .mat-form-field-wrapper {
                  padding-bottom: 0;
                }
                .mat-form-field-infix {
                  padding-top: 0.1em;
                  font-size: 15px;
                }
                &.mat-focused {
                  .mat-form-field-outline-thick {
                    color: #33333350 !important;
                  }
                }
              }
            }
            .typeName {
              width: 20em;
              align-self: center;
            }
            .selectFileTemplate {
              align-self: center;
              line-height: 1.5;
              &.mat-form-field-appearance-fill {
                .mat-form-field-flex {
                  padding: 0 0.5em;
                  height: 3em;
                }
                .mat-form-field-infix {
                  padding: 0 0 0.5em 0;
                }
                .mat-select-arrow-wrapper {
                  padding-top: 1.5em;
                }
              }
              .mat-form-field-wrapper {
                padding: 0;
              }
              .mat-form-field-underline {
                display: none;
              }
              .mat-select-placeholder {
                color: #333;
              }
            }
            .btn_downloadFile {
              align-self: center;
              color: #ce7a58;
            }
            .uploadFile {
              align-self: center;
              position: relative;
              overflow: hidden;
              display: inline-block;
              width: 85%;
              text-align: center;
              z-index: 4;
              margin: 0.5em 0;
              button {
                float: right;
                color: #1e2f41;
                width: 100%;
                cursor: pointer;
                border: 1px solid #9e9e9e96;
                border-style: dashed;
                .mat-icon {
                  transform: rotate(-45deg);
                  color: #ce7a58;
                }
              }
              input[type="file"] {
                font-size: 100px;
                position: absolute;
                right: 0;
                top: 0;
                opacity: 0;
                height: 100%;
                width: 100%;
                cursor: pointer;
              }
            }
            .uploaded_File {
              align-self: center;
              position: relative;
              overflow: hidden;
              width: 100%;
              text-align: center;
              z-index: 4;
              display: none;
              .btn_upload {
                float: right;
                color: #1e2f41;
                width: 33%;
                cursor: pointer;
                border: 1px solid #9e9e9e96;
                border-style: dashed;
                margin: 0.5em 0;
                .text{
                  font-size: 12px;
                }
              }
              input[type="file"] {
                font-size: 100px;
                position: absolute;
                right: 0;
                top: 0;
                opacity: 0;
                height: 100%;
                width: 42%;
                cursor: pointer;
              }
              .mat-button .mat-icon {
                transform: rotate(-45deg);
                color: #ce7a58;
              }
            }
            .selectedFile {
              border: 1px solid #9e9e9e96;
              border-style: dashed;
              margin: 0.5em 0.9em 0.5em 0;
              border-radius: 4px;
              padding: 0 0.5em;
              width: 49.5%;
              display: flex;
              .fileIcon {
                align-self: center;
                padding-right: 0.2em;
                color: #ce7a58;
              }
              .fileName {
                align-self: center;
                font-weight: 500;
                width: 70%;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
                color: #1e2f41;
                text-align: left;
              }
              .fileRemove {
                margin-left: auto;
                align-self: center;
              }
            }
          }
        }
        .btn_addNewFile {
          color: #1e2f41;
          margin-top: 1em;
        }
      }
      .procedureFormFile {
        margin: 1.5em 0;
        .implementer {
          text-align: right;
        }
        .item {
          .head {
            background-color: #bdbdbd25;
            padding: 1em;
            margin-bottom: 1em;
            border-radius: 4px;
            .requirement {
              vertical-align: middle;
              color: #ce7a58;
              margin-top: -0.1em;
            }
            span {
              padding: 0 0.5em;
              font-weight: 500;
              font-size: 15px;
              padding-top: 0.1em;
            }
            .mat-checkbox {
              .mat-checkbox-frame {
                transform: scale(1.2);
                border-width: 0.1px !important;
                opacity: 0.7;
              }
            }
            .mat-checkbox-checked {
              .mat-checkbox-background {
                transform: scale(1.2);
                background-color: #ce7a58;
              }
              &.mat-checkbox-disabled {
                .mat-checkbox-background {
                  background-color: #33333350 !important;
                }
              }
              .mat-checkbox-indeterminate {
                .mat-checkbox-background {
                  background-color: #33333350 !important;
                }
              }
            }
          }
          .body {
            .mat-radio-group {
              display: flex;
              flex-flow: column nowrap;
              padding: 0 0 0 2.5em;
              .rdo_File {
                padding: 0 0.5em;
                border: 1px solid #e0e0e059;
                border-radius: 4px;
                margin-bottom: 0.5em;
                display: flex;
                flex-wrap: wrap;
                .mat-radio-button {
                  align-self: center;
                  &.mat-radio-checked {
                    .mat-radio-outer-circle {
                      border-color: #ce7a58;
                    }
                    .mat-radio-inner-circle {
                      background-color: #ce7a58;
                    }
                    &.mat-radio-disabled {
                      .mat-radio-outer-circle {
                        border-color: #33333350 !important;
                      }
                      .mat-radio-inner-circle {
                        background-color: #33333350 !important;
                      }
                    }
                  }
                }
              }
            }
            .quantity {
              align-self: center;
              &.mat-form-field-appearance-outline {
                .mat-form-field-flex {
                  width: 4em;
                  height: 3em;
                  margin: 0 0.5em;
                }
                .mat-form-field-wrapper {
                  padding-bottom: 0;
                }
                .mat-form-field-infix {
                  padding-top: 0.1em;
                  font-size: 15px;
                }
                &.mat-focused {
                  .mat-form-field-outline-thick {
                    color: #33333350 !important;
                  }
                }
              }
            }
            .typeName {
              width: 20em;
              align-self: center;
            }
            .selectFileTemplate {
              align-self: center;
              line-height: 1.5;
              &.mat-form-field-appearance-fill {
                .mat-form-field-flex {
                  padding: 0 0.5em;
                  height: 3em;
                }
                .mat-form-field-infix {
                  padding: 0 0 0.5em 0;
                }
                .mat-select-arrow-wrapper {
                  padding-top: 1.5em;
                }
              }
              .mat-form-field-wrapper {
                padding: 0;
              }
              .mat-form-field-underline {
                display: none;
              }
              .mat-select-placeholder {
                color: #333;
              }
            }
            .btn_downloadFile {
              align-self: center;
              color: #ce7a58;
            }
            .listUploadedFile {
              align-self: center;
              border-radius: 4px;
              border: 1px dashed #8f969c;
              padding: 0.25em 0;
              .wrapList {
                flex-wrap: wrap;
                .file {
                  background-color: #8c8c8c2d;
                  padding-left: 0.5em;
                  border-radius: 4px;
                  margin: 0.25em 0;
                  display: flex;
                  .icon {
                    align-self: center;
                    width: 2em;
                    height: 2em;
                    background-position: center;
                    background-repeat: no-repeat;
                    background-size: 100%;
                    margin-right: 0.5em;
                  }
                  .name {
                    align-self: center;
                    color: #1e2f41;
                    width: 80%;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                  }
                  .deleteFile {
                    align-self: center;
                  }
                }
                .uploadBtn {
                  position: relative;
                  overflow: hidden;
                  display: inline-block;
                  .mat-button {
                    background-color: #8c8c8c2d;
                    padding: 0.2em 1em;
                    border-radius: 4px;
                    margin: 0.25em 0;
                    display: flex;
                    color: #1e2f41;
                    width: 100%;
                    justify-content: center;
                    .mat-icon {
                      color: #ce7a58;
                      transform: rotate(45deg);
                    }
                  }
                  input[type="file"] {
                    position: absolute;
                    left: 0;
                    top: 0;
                    opacity: 0;
                    width: 100%;
                    height: 100%;
                    cursor: pointer;
                  }
                }
              }
            }
          }
        }
        .disabledHide {
          display: none;
        }
      }
      .procedureFee {
        background-color: #bdbdbd33;
        margin: 1em 0 2em 0;
        padding: 0;
        border-radius: 6px;
        .mat-table {
          border: 1px solid #33333325;
          border-radius: 4px;
          .mat-header-cell {
            background-color: #f2f2f2;
            font-weight: 500;
            color: #333;
            th {
              font-weight: 500;
              color: #333;
              font-size: 14px;
            }
          }
          .quantity {
            &.mat-form-field-appearance-outline {
              .mat-form-field-flex {
                min-width: 6em;
                height: 3em;
                margin: 0 0.5em 0 0;
              }
              .mat-form-field-wrapper {
                padding-bottom: 0;
              }
              .mat-form-field-infix {
                padding-top: 0.1em;
                font-size: 15px;
              }
              &.mat-focused {
                .mat-form-field-outline-thick {
                  color: #33333350 !important;
                }
              }
            }
          }
        }
        .totalCell {
          font-weight: 500;
          color: #ce7a58;
          font-size: 14px;
        }
        .hidden {
          display: none !important;
        }
      }
      .detailForm {
        margin: 1em 0 2em 0 !important;
      }
      .receiveForm {
        margin: 1em 0 2em 0;
      }
    }
    .control {
      flex-wrap: wrap;
      .btnSecondary {
        color: #ce7a58;
        border: 1px solid #ce7a58;
        margin: 0.5em;
        .mat-icon {
          margin-right: 0.3em;
          vertical-align: middle;
          align-self: center;
        }
        .mat-button-wrapper {
          display: flex;
          justify-content: center;
          .mat-spinner {
            margin-right: 0.3em;
            align-self: center;
            circle {
              stroke: #ce7a58;
            }
          }
        }
      }
      .btnPrimary {
        background-color: #ce7a58;
        color: #fff;
        margin: 0.5em;
        .mat-icon {
          margin-right: 0.3em;
          vertical-align: middle;
        }
      }
      .btnVerify {
        background-color: #ce7a58;
        color: #fff;
        margin: 1em 2em 0.5em 0.5em;
        .mat-icon {
          margin-right: 0.3em;
          vertical-align: middle;
        }
      }
      .btnMore {
        background-color: #e8e8e8;
        color: #666;
        margin: 0.5em;
        min-width: 2.5em !important;
        .mat-icon {
          color: #666666;
        }
      }
      .btnDisabled {
        cursor: not-allowed;
      }
      .btnCheck {
        margin: 0.5em;
        .mat-icon {
          margin-right: 0.3em;
          vertical-align: middle;
        }
      }
    }
    .mat-form-field-appearance-outline {
      .mat-form-field-infix {
        padding: 0.8em 0;
      }
      .mat-form-field-outline {
        color: transparent;
        background-color: #eaebeb;
        border-radius: 5px;
      }
      .mat-form-field-outline-thick {
        color: #dddddd;
      }
    }
    .mat-form-field-label-wrapper {
      top: -1em;
    }
    .mat-form-field.mat-focused {
      .mat-form-field-label {
        color: #ce7a58;
        font-size: 14px;
      }
    }
    .mat-form-field-appearance-outline.mat-form-field-can-float.mat-form-field-should-float {
      .mat-form-field-label {
        color: #ce7a58;
        transform: translateY(-1.55em) scale(1);
        margin-bottom: 1em;
      }
    }
    .mat-form-field .error_Msg {
      font-size: 12px !important;
      float: right;
      display: flex;
      color: #ce7a58;
      .err {
        background-color: #f2a63494;
        border-radius: 50%;
        width: 1.2em;
        height: 1.2em;
        justify-content: center;
        display: flex;
        margin-left: 0.5em;
        .mat-icon {
          color: #ce7a58;
          vertical-align: middle;
          align-self: center;
          transform: scale(0.8);
        }
      }
    }
  }
  .disabledHide {
    display: none;
  }
}

.btnSearch {
  max-width: 15%;
  height: 45px;
  margin-top: 14px;
  margin-left: 10px;
  background-color: #ce7a58;
  color: white;
}

.common-info button{
  margin-left: 10px;
  margin-right: 0px;
}

// CSS for file upload
.listUploadedFile {
  align-self: center;
  border-radius: 4px;
  border: 1px dashed #8f969c;
  padding: 0.25em 0;
  .wrapList {
    flex-wrap: wrap;
    .file {
      background-color: #8c8c8c2d;
      padding-left: 0.5em;
      border-radius: 4px;
      margin: 0.25em 0;
      display: flex;
      .icon {
        align-self: center;
        width: 2em;
        height: 2em;
        background-position: center;
        background-repeat: no-repeat;
        background-size: 100%;
        margin-right: 0.5em;
      }
      .name {
        align-self: center;
        color: #1e2f41;
        width: 80%;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .deleteFile {
        align-self: center;
      }
      .signFileBtn {
        width: auto;
      }
    }
    .uploadBtn {
      position: relative;
      overflow: hidden;
      display: inline-block;
      .mat-button {
        background-color: #8c8c8c2d;
        padding: 0.2em 1em;
        border-radius: 4px;
        margin: 0.25em 0;
        display: flex;
        color: #1e2f41;
        width: 100%;
        justify-content: center;
        .mat-icon {
          color: #ce7a58;
          transform: rotate(45deg);
        }
      }
      input[type="file"] {
        position: absolute;
        left: 0;
        top: 0;
        opacity: 0;
        width: 100%;
        height: 100%;
        cursor: pointer;
      }
    }
  }
}

::ng-deep .listLoaiChungThuc {
  align-self: center;
  margin: 0.25em 0;
  .wrapList {
    flex-wrap: wrap;
    .loaiChungThuc {
      .mat-form-field{
        display: block !important;
        .mat-form-field-wrapper {
          padding-bottom: 0px !important;
        }
      }
    }
  }
}

.save2storage{
  padding-left: 18px;
  button{
    color: #4CA2DD;
  }
}

.save2storage .title{
  color: #1e2f41!important;
  font-weight: initial;
}

.no-hover-effect:hover {
  background-color: #FFFFFF;
}


.procedureForm {
  .mat-table-form
  {
    border: 1px solid #33333325;
    border-radius: 4px;
  }
  .tablecolor1 {
    background-color: #f2f2f2;
    font-weight: 500;
    color: #333;
    font-size: 12px;
  }
  .table-row {
    border-bottom: 1px solid rgba(0,0,0,.12);
  }
  table td
  {
    border-bottom: 1px solid rgba(0,0,0,.12);
  }
  .table-row .center{
    text-align:center;
  }
  .table-row td{
    padding: 5px;
  }
  .hasFormOrgin
  {
    font-weight: normal;
    color: red;
  }
  .item
  {
    .head {
      background-color: white;
      padding: 1em;
      margin-bottom: 1em;
      border-radius: 4px;
      padding-left: 2px;
      text-align: justify;
    }
    .center
    {
      .head {
        text-align: center;
      }
    }
    .body .mat-radio-group
    {
      padding: 0px;
    }
    .mat-radio-group .rdo_File {
      padding: 0;
      border: none;
    }
    .setfileForm{
      padding: 0;
    }
    .typeName {
      width: auto;
      margin-left: 0;
      align-self: center;
    }
    .listUploadedFile {
      padding: 0;
      width: 100%;
    }
    .body .mat-radio-group .file .name {
      white-space: normal;
    }
    .mat-form-field-appearance-outline .mat-form-field-infix {
      padding: 0.5em 0!important;
    }
  }
}

.cardContent{
  .s_head {
    display: flex;
    margin-bottom: 0.2em;

    span {
      font-size: 16px;
      font-weight: 500;
      align-self: center;
    }
  }
  .payment-status-vnpost{
    color: red;
    font-style: italic;
    font-size: 14px!important;
    font-weight: normal!important;
  }
  .fee_head{
    padding-top: 10px;
  }
}

.nhanKqQ6 {
  color: red;
  margin-bottom: 1em;
  span {
    font-weight: 500;
  }
}
.hidden {
  display: none;
}
.invisible{
  visibility: hidden;
}



//=======đợi chuyển trang thanh toán
.waiting-container {
  position: fixed;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  background: rgba(255, 255, 255, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 99;
}

.waiting-container ::ng-deep .mat-progress-spinner circle, .mat-spinner circle {
  stroke: #ce7a58;
}

.spinner-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.spinner-text {
  font-size: 20px;
}

.opacity-div{
  opacity: 0.8;
}

.no-data-text{
  color: red;
  font-weight: 600;
  padding-left: 1em;
}

/* Định dạng nút lưu (nếu có) */
.search-button {
  background-color: #007bff;
  color: #fff;
  padding: 2px 15px;
  margin-bottom: 1rem;
  font-size: 16px;
}

.button-search-dkkd:hover {
  background-color: #0056b3;
}

.button-search-dkkd{
  visibility: visible;
  border-collapse: collapse;
  box-sizing: border-box;
  margin: 0;
  font-family: inherit;
  overflow: visible;
  text-transform: none;
  display: inline-block;
  font-weight: 400;
  text-align: center;
  vertical-align: middle;
  user-select: none;
  border: 1px solid transparent;
  padding: .375rem .75rem;
  font-size: 1rem;
  line-height: 1.5;
  border-radius: .25rem;
  transition: color .15s ease-in-out,background-color .15s ease-in-out,border-color .15s ease-in-out,box-shadow .15s ease-in-out;
  color: #fff;
  background-color: #007bff;
  border-color: #007bff;
  cursor: pointer;
}

.bordered-container {
  position: relative; /* Để định vị text label */
  border: 1px solid #ccc; /* Đường viền bao quanh */
  padding: 16px; /* Khoảng cách bên trong */
  margin-bottom: 16px; /* Khoảng cách dưới */
  border-radius: 4px; /* Bo góc cho đường viền */
}

.label-text {
  position: absolute; /* Đặt tuyệt đối bên trong thẻ div */
  top: -12px; /* Đẩy lên trên để hiển thị nằm trên đường viền */
  left: 16px; /* Đẩy sang trái */
  background-color: #fff; /* Màu nền để che đi phần bị cắt */
  padding: 0 8px; /* Khoảng cách xung quanh chữ */
  font-weight: bold; /* Đậm chữ */
  color: #ce7a58;
}

.cardContent{
  color: #1e2f41;
  padding: .5em;
  border: 1px solid #e2e2e2;
  height: 85%;
  border-radius: 0 0 4px 4px;
}
