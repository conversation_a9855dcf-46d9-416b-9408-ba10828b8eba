import { Component, OnInit, Inject, ViewChild } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA, MatDialog } from '@angular/material/dialog';
import { KeycloakService } from 'keycloak-angular';
import { DossierService } from 'src/app/data/service/dossier/dossier.service';
import { UserService } from 'src/app/data/service/user.service';
import * as ClassicEditor from '@ckeditor/ckeditor5-build-classic/build/ckeditor';
import '@ckeditor/ckeditor5-build-classic/build/translations/vi';
import { EnvService } from 'src/app/core/service/env.service';
import { SnackbarService } from 'src/app/data/service/snackbar/snackbar.service';
import { DatePipe } from '@angular/common';
import { NotificationService } from 'src/app/data/service/notification.service';
import { DeploymentService } from 'src/app/data/service/deployment.service';
import * as tUtils from 'src/app/data/service/thoai.service';
import { ProcedureService } from 'src/app/data/service/procedure/procedure.service';
import {NotifyQNIService} from "shared/components/check-send-notify-qni/check-send-notify-qni.service";
import { PadmanService } from 'src/app/data/service/svc-padman/padman.service';
import {FormControl, FormGroup} from '@angular/forms';
import { CheckSendNotifyComponent } from 'src/app/shared/components/check-send-notify/check-send-notify.component';
import { AdapterService } from 'src/app/data/service/adapter/adapter.service';
import { AgencyService } from 'src/app/data/service/basedata/agency.service';
import { LogmanService } from 'src/app/data/service/logman/logman.service';
import { FileService } from 'src/app/data/service/file.service';
import { UploadService } from 'src/app/data/service/upload/upload.service';
import { ConfigService } from 'src/app/data/service/dossier/config.service';
import { DigitalSignatureService } from 'src/app/data/service/digital-signature/digital-signature.service';
import { PdfViewerComponent, PdfViewerDialog } from 'src/app/shared/components/pdf-viewer/pdf-viewer.component';
declare var vgca_sign_approved: any;

@Component({
  selector: 'app-apology-letter',
  templateUrl: './apology-letter.component.html',
  styleUrls: ['./apology-letter.component.scss']
})
export class ApologyLetterComponent implements OnInit {
  @ViewChild(CheckSendNotifyComponent) checkSendNotifyComponent;
  config = this.envService.getConfig();
  env: any = this.deploymentService.getAppDeployment()?.env;
  selectedLang = localStorage.getItem('language') || 'vi';
  selectedLangId = Number(localStorage.getItem('languageId')) || 228;
  dossierId: string;
  procedureProcessDetail : any;
  commentContent = '';
  commentContentPlainText = '';
  userName: string;
  accountId: string;
  dossierCode: string;
  isCKMaxlenght = false;
  isCKDescriptionMaxlenght = false;
  dossierDetail: any;
  oldstatus = '';
  officers = [];
  agencyId: any;
  getApprovalAgency = '';
  currentTask: any;
  codeMap='';
  isDisabled = false; //nhipttt-IGATESUPP-74286 disabled nút Yêu cầu bổ sung sau khi nhấn
  digitalSignatureEnable: boolean = false;
  digitalSignature = {
    SmartCA:false,
    VGCA:false,
    VNPTCA:false,
    VNPTSim:false,
    NEAC:false,
    QNM: false
  }
  isEditSignTokenName = false;
  digitalsignatureButton = this.deploymentService.env?.OS_HCM?.digitalsignatureButton ? this.deploymentService.env?.OS_HCM?.digitalsignatureButton : false;

  public Editor = ClassicEditor;
  commentConfig = {
    placeholder: 'Nhập nội dung...',
    language: 'vi',
    removePlugins: ['MediaEmbed', 'ImageUpload'],
    toolbarLocation: 'bottom'
  };
  dossierTaskStatus = { id: '', name: [] };
  dossierMenuTaskRemind = { id: '', name: [] };
  filePDF = {
    id: "",
    filename: ""
  };
  tempObj : any = {};
  checkIsDocFile = false;
  // ================================================= Upload file
  uploaded: boolean;
  blankVal: any;
  uploadedImage = null;
  files = [];
  urls = [];
  fileNames = [];
  uploadFileNames = [];
  fileNamesFull = [];
  listProcedureProcess = [];

  isFieldArrayInvalid = false;
  acceptFileExtension = this.config.acceptFileExtension;
  acceptFileType = this.config.acceptFileType;
  maxFileSize = this.env?.maxFileSize || this.config.maxFileSize;
  //Thu tuc co cau hinh dong bo BXD-LGSP Tan Tan
  isSyncDBNLGSPTanDanBXD = 0;
  isHasCodeLDXH = false; //check có mã thủ tục LĐXH không
  statusName = "";
  fileTemplate = "";
  typeProcess = 1;

  notifyQNI = this.deploymentService.getAppDeployment()?.notifyqni?.enable;

  totalCost = '';

  ckeditorMaxLengthNotification = "";
  ckeditorMaxLength = this.deploymentService.env.OS_HCM.ckeditorMaxLength;
  checkRequireAdditionalRequest = this.env?.OS_BDG?.isRequiredUploadFileBDG?.additional == false ? false : true;
  numberDateAdditionalRequirement = this.deploymentService.env.OS_HCM.numberDateAdditionalRequirement;
  numberDate: number;
  checkNumberDate: boolean = false;
  notifyCheck;
  updateForm = new FormGroup({
    approvalAgencyId: new FormControl(''),
  });
  reason = new FormControl('');
  tittle = new FormControl('');
  approvalAgency = [];

  showExportFileBtn = this.deploymentService.env.OS_HCM.showExportFileBtn;
  enableShowExportFileBtn = false;
  listAgencyShowExportFileBtn = this.deploymentService.env.OS_HCM?.listAgencyShowExportFileBtn; 
  template = this.deploymentService.env.OS_HCM.template;
  requireAdditionalTemplate = "";
  constructConfigId=this.deploymentService.env?.OS_KTM?.constructConfigId;

  listAgencyIdShowButtonGetFilesFromResult = this.deploymentService.env.OS_HCM.listAgencyIdShowButtonGetFilesFromResult;
  showButtonGetFilesFromResult = false;

  // IGATESUPP-44607
  enableApproveAdditionalRequestCheckBox = false;
  dossierTaskStatusWaitingForApproval = { id: '', name: [] };
  dossierMenuTaskRemindWaitingForApproval = { id: '', name: [] };
  requireAttachmentWhenAdditionalRequest = this.deploymentService.env.OS_HCM.requireAttachmentWhenAdditionalRequest == false ? this.deploymentService.env.OS_HCM.requireAttachmentWhenAdditionalRequest : true;
  dossierStatusId = null;
  dosierTaskStatusId = "";
  dossierMenuTaskRemindId = "";
  constructor(
    private userService: UserService,
    private envService: EnvService,
    private keycloakService: KeycloakService,
    public dialogRef: MatDialogRef<ApologyLetterComponent>,
    @Inject(MAT_DIALOG_DATA) public data: ConfirmApologyLetterDialogModel,
    private snackbarService: SnackbarService,
    private datePipe: DatePipe,
    private notiService: NotificationService,
    private deploymentService: DeploymentService,
    private procedureService: ProcedureService,
    private dossierService: DossierService,
    private notifyQNIService: NotifyQNIService,
    private padmanService: PadmanService,
    private adapterService: AdapterService,
    private agencyService: AgencyService,
    private dialog: MatDialog,
    private logmanService: LogmanService,
    private fileService: FileService,
    private uploadService: UploadService,
    private configService: ConfigService,
    private digitalSignatureService: DigitalSignatureService
  ) {
    this.dossierId = data.dossierId;
    this.dossierCode = data.dossierCode;
    this.typeProcess = data.typeProcess;
    if (this.typeProcess === 2){
      this.env.enableApprovalOfLeadership = 2;
    }
  }

  ngOnInit(): void {
    this.getUserAccount();
    this.getDetailDossier();
    this.setEnvVariable();
    const userAgency = JSON.parse(localStorage.getItem('userAgency'));
    if(this.listAgencyShowExportFileBtn.filter(item => item == userAgency?.id || item == userAgency?.parent?.id ).length > 0){
      this.enableShowExportFileBtn = true;
    }
    if (this.selectedLang === 'vi') {
      this.ckeditorMaxLengthNotification = "Nội dung không quá 500 ký tự!";
      if (this.ckeditorMaxLength > 0)
        this.ckeditorMaxLengthNotification = "Nội dung không quá "+ this.ckeditorMaxLength + " ký tự!";
    } else if (this.selectedLang === 'en') {
      this.ckeditorMaxLengthNotification = "Content must not exceed 500 characters!";
      if (this.ckeditorMaxLength > 0)
        this.ckeditorMaxLengthNotification = "Content must not exceed "+ this.ckeditorMaxLength + " characters!";
    }
 
      // Check if digital signature supported
      if(this.digitalsignatureButton){
        this.digitalSignatureEnable = true;
        this.digitalSignature = {
          SmartCA:false,
          VGCA:false,
          VNPTCA:false,
          VNPTSim:false,
          NEAC:false,
          QNM: false
        }
        this.digitalSignature.SmartCA = this.env?.digitalSignature?.SmartCA == 1;
        this.digitalSignature.VGCA = this.env?.digitalSignature?.VGCA == 1;
        this.digitalSignature.VNPTCA = this.env?.digitalSignature?.VNPTCA == 1;
        this.digitalSignature.VNPTSim = this.env?.digitalSignature?.VNPTSim == 1;
        this.digitalSignature.NEAC = this.env?.digitalSignature?.NEAC == 1;
        this.digitalSignature.QNM = this.env?.digitalSignature?.QNM == 1;
        this.isEditSignTokenName = this.env?.OS_KTM?.isEditSignTokenName == true ? true : false ;
      }
  }





  setEnvVariable(){
    this.fileTemplate = !!this.env?.fileTemplate?.requireAdditional ? this.env?.fileTemplate?.requireAdditional : this.config.requireAdditionalTemplateFile;
    if(this.deploymentService.env?.OS_HCM?.allowShowInfoDossierWhenExportFileHCM?.enable == true){
      this.requireAdditionalTemplate = this.deploymentService.env?.OS_HCM?.allowShowInfoDossierWhenExportFileHCM?.templateHCM?.requireAdditionalTemplate ? this.deploymentService.env?.OS_HCM?.allowShowInfoDossierWhenExportFileHCM?.templateHCM?.requireAdditionalTemplate : "";
    } else {
      this.requireAdditionalTemplate = this.template?.requireAdditionalTemplate ? this.template?.requireAdditionalTemplate : "";
    }
  }

  getUserAccount() {
    this.keycloakService.loadUserProfile().then(user => {
      // tslint:disable-next-line: no-string-literal
      this.accountId = user['attributes'].user_id[0];
      this.userService.getUserInfo(this.accountId).subscribe(data => {
        this.userName = data.fullname;
      });
    });
  }

  async getDetailDossier() {
    await this.getDossierFeeNotify();
    this.dossierService.getDossierDetail(this.dossierId).subscribe(async data => {
      this.dossierDetail = data;
      if(this.listAgencyIdShowButtonGetFilesFromResult?.length > 0){
        let listFile = this.dossierDetail?.attachment?.filter(d => d.group == this.config.dossierResultFileTagId);
        let userAgency = JSON.parse(localStorage.getItem('userAgency'));
        if(listFile?.length > 0 && (this.listAgencyIdShowButtonGetFilesFromResult.includes(userAgency?.id) || 
            this.listAgencyIdShowButtonGetFilesFromResult.includes(userAgency?.parent?.id))){
          this.showButtonGetFilesFromResult = true;
        }
      }
      this.getProcedureDetail(data?.procedure?.id);
      this.agencyId = !!data?.agency?.id ? data?.agency?.id : data?.agency?.parent?.id;
     
      this.notiService.checkSendSubject.next(
        {
          id: data?.procedureProcessDefinition?.id,
          phone: data?.applicant?.data?.phoneNumber,
          email: data?.applicant?.data?.email,
          currentTask: this.currentTask,
          contentParams: {
            parameters: {
              sector: !!data?.procedure?.sector?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name ? data?.procedure?.sector?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name : '',
              procedure: !!data?.procedure?.translate?.find(agency => agency.languageId === Number(this.selectedLangId))?.name ? data?.procedure?.translate?.find(agency => agency.languageId === Number(this.selectedLangId))?.name : '',
              agency: !!data?.agency?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name ? data?.agency?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name : data?.agency?.parent?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name,
              agencyId: !!data?.agency?.id ? data?.agency?.id : data?.agency?.parent?.id,
              applicantFullname: data?.applicant?.data?.fullname,
              officerFullname: '',
              subsystem: !!this.env?.subsystem?.name ? this.env?.subsystem?.name : "Hệ thống Một cửa điện tử",
              dossierCode: data?.nationCode ? data?.nationCode : data?.code,
              nextTask: !!this.env?.notify?.additionalRequirement?.nextTask ? this.env?.notify?.additionalRequirement?.nextTask : 'Yêu cầu bổ sung hồ sơ',
              // IGATESUPP-63184
              nextStatus: !!this.env?.notify?.additionalRequirement?.nextStatus ? this.env?.notify?.additionalRequirement?.nextStatus : (data?.dossierTaskStatus?.name ?? ''), // cho bo sung, trang thai hien tai cua ho so
              dossierStatus: !!this.env?.notify?.additionalRequirement?.dossierStatus ? this.env?.notify?.additionalRequirement?.dossierStatus : (data?.dossierStatus?.name?.find(t => t.languageId === Number(this.selectedLangId))?.name ?? ''), // cho bo sung, trang thai hien tai cua ho so
              dossierSearchPadsvcUrl: (!!this.env?.domain?.padsvc ? this.env?.domain?.padsvc : "http://padsvctest.vncitizens.vn/vi/") + "dossier/public",
              dossierDetailPadsvcUrl: (!!this.env?.domain?.padsvc ? this.env?.domain?.padsvc : "http://padsvctest.vncitizens.vn/vi/") + "dossier/detail/" + this.dossierId,
              dossierSearchUrl: (!!this.env?.domain?.onegate ? this.env?.domain?.onegate : "http://padsvctest.vncitizens.vn/vi/") + "dossier/search/",
              dossierDetailUrl: (!!this.env?.domain?.onegate ? this.env?.domain?.onegate : "http://padsvctest.vncitizens.vn/vi/") + "dossier/search/" + this.dossierId,
              returnMethod: !!data?.dossierReceivingKind?.name?.find(recieve => recieve.languageId === Number(this.selectedLangId))?.name ? data?.dossierReceivingKind?.name?.find(recieve => recieve.languageId === Number(this.selectedLangId))?.name : '',
              receivingDate: '',
              appointmentDate: '',
              dossierFee: this.totalCost,
              reason: this.commentContent
            }
          }
        }
      );
      if (this.dossierDetail.dossierTaskStatus === undefined && this.dossierDetail.dossierTaskStatus === null) {
        // this.putDossierStatus();
      }
      else {
        this.oldstatus = this.dossierDetail.dossierTaskStatus.name;
      }
    });
  }

  async getNotify(){
    let data = this.dossierDetail;
    if(!!this.notifyQNI){
      await this.notifyQNIService.checkSendSubject.next(
        {
          id: data?.procedureProcessDefinition?.id,
          phone: data?.applicant?.data?.phoneNumber,
          email: data?.applicant?.data?.email,
          currentTask: this.currentTask,
          contentParams: {
            parameters: {
              sector: !!data?.procedure?.sector?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name ? data?.procedure?.sector?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name : '',
              procedure: !!data?.procedure?.translate?.find(agency => agency.languageId === Number(this.selectedLangId))?.name ? data?.procedure?.translate?.find(agency => agency.languageId === Number(this.selectedLangId))?.name : '',
              agency: !!data?.agency?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name ? data?.agency?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name : '',
              agencyId: !!data?.agency?.id ? data?.agency?.id : data?.agency?.parent?.id,
              applicantFullname: data?.applicant?.data?.fullname,
              officerFullname: '',
              subsystem: !!this.env?.subsystem?.name ? this.env?.subsystem?.name : "Hệ thống Một cửa điện tử",
              dossierCode: data?.code,
              nextTask: !!this.env?.notify?.additionalRequirement?.nextTask ? this.env?.notify?.additionalRequirement?.nextTask : 'Yêu cầu bổ sung hồ sơ',
              dossierSearchPadsvcUrl: (!!this.env?.domain?.padsvc ? this.env?.domain?.padsvc : "http://padsvctest.vncitizens.vn/vi/") + "dossier/public",
              dossierDetailPadsvcUrl: (!!this.env?.domain?.padsvc ? this.env?.domain?.padsvc : "http://padsvctest.vncitizens.vn/vi/") + "dossier/detail/" + this.dossierId,
              dossierSearchUrl: (!!this.env?.domain?.onegate ? this.env?.domain?.onegate : "http://padsvctest.vncitizens.vn/vi/") + "dossier/search/",
              dossierDetailUrl: (!!this.env?.domain?.onegate ? this.env?.domain?.onegate : "http://padsvctest.vncitizens.vn/vi/") + "dossier/search/" + this.dossierId,
              returnMethod: !!data?.dossierReceivingKind?.name?.find(recieve => recieve.languageId === Number(this.selectedLangId))?.name ? data?.dossierReceivingKind?.name?.find(recieve => recieve.languageId === Number(this.selectedLangId))?.name : '',
              receivingDate: '',
              appointmentDate: '',
              dossierFee: this.totalCost,
              reason: this.commentContentPlainText,
              numberDate: this.getNumberDate()
            }
          }
        }
      );
    } else {
      await this.notiService.checkSendSubject.next(
        {
          id: data?.procedureProcessDefinition?.id,
          phone: data?.applicant?.data?.phoneNumber,
          email: data?.applicant?.data?.email,
          currentTask: this.currentTask,
          contentParams: {
            parameters: {
              sector: !!data?.procedure?.sector?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name ? data?.procedure?.sector?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name : '',
              procedure: !!data?.procedure?.translate?.find(agency => agency.languageId === Number(this.selectedLangId))?.name ? data?.procedure?.translate?.find(agency => agency.languageId === Number(this.selectedLangId))?.name : '',
              agency: !!data?.agency?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name ? data?.agency?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name : data?.agency?.parent?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name,
              agencyId: !!data?.agency?.id ? data?.agency?.id : data?.agency?.parent?.id,
              applicantFullname: data?.applicant?.data?.fullname,
              officerFullname: '',
              subsystem: !!this.env?.subsystem?.name ? this.env?.subsystem?.name : "Hệ thống Một cửa điện tử",
              dossierCode: data?.code,
              nextTask: !!this.env?.notify?.additionalRequirement?.nextTask ? this.env?.notify?.additionalRequirement?.nextTask : 'Yêu cầu bổ sung hồ sơ',
              // IGATESUPP-63184
              nextStatus: !!this.env?.notify?.additionalRequirement?.nextStatus ? this.env?.notify?.additionalRequirement?.nextStatus : (data?.dossierTaskStatus?.name ?? ''), // cho bo sung, trang thai hien tai cua ho so
              dossierStatus: !!this.env?.notify?.additionalRequirement?.dossierStatus ? this.env?.notify?.additionalRequirement?.dossierStatus : (data?.dossierStatus?.name?.find(t => t.languageId === Number(this.selectedLangId))?.name ?? ''), // cho bo sung, trang thai hien tai cua ho so
              dossierSearchPadsvcUrl: (!!this.env?.domain?.padsvc ? this.env?.domain?.padsvc : "http://padsvctest.vncitizens.vn/vi/") + "dossier/public",
              dossierDetailPadsvcUrl: (!!this.env?.domain?.padsvc ? this.env?.domain?.padsvc : "http://padsvctest.vncitizens.vn/vi/") + "dossier/detail/" + this.dossierId,
              dossierSearchUrl: (!!this.env?.domain?.onegate ? this.env?.domain?.onegate : "http://padsvctest.vncitizens.vn/vi/") + "dossier/search/",
              dossierDetailUrl: (!!this.env?.domain?.onegate ? this.env?.domain?.onegate : "http://padsvctest.vncitizens.vn/vi/") + "dossier/search/" + this.dossierId,
              returnMethod: !!data?.dossierReceivingKind?.name?.find(recieve => recieve.languageId === Number(this.selectedLangId))?.name ? data?.dossierReceivingKind?.name?.find(recieve => recieve.languageId === Number(this.selectedLangId))?.name : '',
              receivingDate: '',
              appointmentDate: '',
              dossierFee: this.totalCost,
              reason: this.commentContentPlainText,
              numberDate: this.getNumberDate()
            }
          }
        }
      );
    }
    this.checkSendNotifyComponent.notify = this.notifyCheck;
    this.checkSendNotifyComponent.onInit();
  }

  getNumberDate()
  {
    let numberDate = '';
    if(this.numberDateAdditionalRequirement && this.numberDateAdditionalRequirement.enable){
      if(this.checkNumberDate && this.numberDate != undefined && this.numberDate != null && this.numberDate >= 0){
        let current = new Date();
        current.setHours(0, 0, 0, 0);
        const end = new Date(current);
        end.setDate(current.getDate() + this.numberDate);
        numberDate = this.datePipe.transform(end, 'dd/MM/yyyy');
      }
    }
    return numberDate;
  }

  getDateRequire()
  {
    if(this.numberDateAdditionalRequirement && this.numberDateAdditionalRequirement.enable){
      if(this.checkNumberDate && this.numberDate != undefined && this.numberDate != null && this.numberDate >= 0){
        let current = new Date();
        current.setHours(0, 0, 0, 0);
        const end = new Date(current);
        end.setDate(current.getDate() + this.numberDate);
        end.setHours(0, 0, 0, 0);
        return this.datePipe.transform(end, 'yyyy-MM-ddT00:00:00');
      }
    }
    return null;
  }

  getDossierFeeNotify() {
    return new Promise<void>(resolve => {
      this.padmanService.getDossierFee(this.dossierId).toPromise().then(res => {
        if(res && res.length > 0)
        {
          let totalCostValue = 0;
          let totalPaidValue = 0;
          res.forEach(element => {
            totalCostValue += element.quantity * element.amount;
            totalPaidValue += element.paid;
          });
          const monetaryUnit = res[0]?.monetaryUnit ? res[0].monetaryUnit : 'VNĐ';
          let total = totalCostValue - totalPaidValue;
          this.totalCost = total > 0 ? Number((total).toFixed(1)).toLocaleString() + ' ' + monetaryUnit : '';
        }
        resolve();
      }).catch(err => {
        resolve();
      });
    });
  }

  getProcedureDetail(procedureId) {
    this.procedureService.getProcedureDetail(procedureId).subscribe(async data => {
      this.isSyncDBNLGSPTanDanBXD = data?.isSyncDBNLGSPTanDanBXD ? Number(data?.isSyncDBNLGSPTanDanBXD) : 0;
      this.isHasCodeLDXH = !!data.btxhcode;
      this.enableApproveAdditionalRequestCheckBox = data?.extendHCM?.enableApproveAdditionalRequestCheckBox ? data.extendHCM?.enableApproveAdditionalRequestCheckBox : false;
      if(tUtils.nonNull(this.dossierDetail?.extendHCM, "officerDossierQualifiedRecept")){
        if(this.dossierDetail?.extendHCM?.officerDossierQualifiedRecept.length > 0){
          if(data?.extendHCM?.enableShowDossierQualifiedRecept){
            let listReason =[];
            this.dossierDetail?.extendHCM?.officerDossierQualifiedRecept.forEach(element => {
              if(element.result == 1){
                listReason.push(element.reason);
              }
            });
            this.reason.setValue(listReason.toString());
            this.tittle.setValue(listReason.toString());
          }
        }
      }
    }, err => {
      console.log(err);
    });
  }
  putDossierStatus() {
    const requestBodyObj = [
      {
        id: this.dossierId,
        dossierStatus: 2,
        dossierTaskStatus: null
      }
    ];
    const requestBody = JSON.stringify(requestBodyObj, null, 2);
    this.dossierService.putDossierStatus(requestBody).subscribe(data => {
      if (data.affectedRows === 1) {
        this.getDetailDossier();
      }
    });
  }

  postHistory() {
    let newStatus = '';
    if (this.dossierTaskStatus.name.length > 0) {
      newStatus = this.dossierTaskStatus.name[0].name;
      this.dossierTaskStatus.name.forEach(element => {
        if (element.languageId === Number(localStorage.getItem('languageId'))) {
          newStatus = element.name;
        }
      });
    }
    let commentContent = this.commentContent.trim();
    
    let msgVi='Cán bộ: ' + this.userName ;
    let msgEn = 'Officier: ' + this.userName;
    if(this.checkNumberDate && this.numberDate != undefined && this.numberDate != null && this.numberDate >= 0){
      msgVi += '<br>Số ngày chờ bổ sung: ' + this.numberDate + ' ngày';
      msgEn += '<br>additional waiting days: ' + this.numberDate + 'date';
    } else {
      msgVi += '<br>Số ngày chờ bổ sung: Không xác định';
      msgEn += '<br>additional waiting days: undefined';
    }
  
    let msgObj = {
      vi: msgVi,
      en: msgEn
    }
    const content = {
      groupId: 1,
      itemId: this.dossierId,
      user: {
        id: this.accountId,
        name: this.userName
      },
      type: 3,
      deploymentId: this.config.deploymentId,
      action: [
        {
          fieldNameRbk: 'lang.word.status',
          originalValue: this.oldstatus,
          newValue: newStatus,
          additionalRequire : {
            dateAdditionalRequire : msgObj[this.selectedLang],
            file: this.uploadFileNames
          }
        }
      ]
    };
    this.dossierService.postHistory(JSON.stringify(content, null, 2)).subscribe(data => {

    });
  }

  postOnlyApprovalAgencyOfDossier(code) {

    const formObj = this.updateForm.getRawValue();
    if (!!formObj.approvalAgencyId && formObj.approvalAgencyId != '') {
      this.getApprovalAgency = formObj.approvalAgencyId;
    }else{
      return;
    }

    const body = {
      id: this.getApprovalAgency
    };
    const requestBody = JSON.stringify(body, null, 2);
    this.dossierService.postOnlyApprovalAgencyOfDossier(requestBody, code).toPromise();
  }

  async onConfirm() {
    this.isDisabled = true;
    let checkFile: any = false;
    if(this.requireAttachmentWhenAdditionalRequest) {
      if (this.uploadFileNames.length > 0) {
        // checkFile = await this.uploadMultiFile(this.files, this.accountId);
        checkFile = true;
        if (!checkFile) {
          const msgObj = {
            vi: 'Upload file thất bại, vui lòng thử lại!',
            en: 'File upload failed, please try again!'
          };
          this.snackbarService.openSnackBar(2, '', msgObj[this.selectedLang], 'warning_notification', this.config.expiredTime);
        }
      } else if (this.checkRequireAdditionalRequest) {
        const msgObj = {
          vi: 'Vui lòng chọn tệp văn bản',
          en: 'Please select the required text file!'
        };
        this.snackbarService.openSnackBar(2, '', msgObj[this.selectedLang], 'warning_notification', this.config.expiredTime);
      } else {
        checkFile = true;
      }
    } else {
        checkFile = true;
    }
    if (this.commentContent.trim() === '') {
      const msgObj = {
        vi: 'Vui lòng nhập ý kiến xử lý!',
        en: 'Please enter a handling comments!'
      };
      this.snackbarService.openSnackBar(2, '', msgObj[this.selectedLang], 'warning_notification', this.config.expiredTime);
    }
    if (this.isCKMaxlenght || this.isCKDescriptionMaxlenght) {
      let msgObj;
      if (this.ckeditorMaxLength > 0) {
        msgObj = {
          vi: 'Nội dung không quá ' + this.ckeditorMaxLength + ' ký tự!',
          en: 'Content must not exceed ' + this.ckeditorMaxLength + ' characters!'
        };
      } else {
        msgObj = {
          vi: 'Nội dung không quá 500 ký tự!',
          en: 'Content must not exceed 500 characters!'
        };
      }
      this.snackbarService.openSnackBar(2, '', msgObj[this.selectedLang], 'warning_notification', this.config.expiredTime);
    }
    if(this.numberDateAdditionalRequirement && this.numberDateAdditionalRequirement.enable){
      //Kiểm tra nếu check mà không nhập số ngày hẹn
      if(this.checkNumberDate && (this.numberDate == undefined || this.numberDate == null || this.numberDate < 0)){
        const msgObj = {
          vi: 'Vui lòng nhập số ngày chờ bổ sung!',
          en: 'Please enter the number of additional waiting day!'
        };
        this.snackbarService.openSnackBar(2, '', msgObj[this.selectedLang], 'warning_notification', this.config.expiredTime);
        return;
      }
    }

    if (!this.isCKMaxlenght && this.commentContent.trim() !== '' && checkFile) {
      const requestBodyObj = {
        dossierStatus: this.env?.enableApprovalOfLeadership == 1 ? 8 : 1,
        comment: '',
        description: '',
        dossierTaskStatus: this.dossierTaskStatus,
        dossierMenuTaskRemind: this.dossierMenuTaskRemind,
        numberDateRequire: null,
        dateRequire: null,
        isAttachmentRequired: this.requireAttachmentWhenAdditionalRequest
      };
      if (this.commentContent.trim() !== '') {
        this.postComment(this.commentContent.trim());
        requestBodyObj.comment = this.commentContent.trim();
      } else {
        const msgObj = {
          vi: 'Thư xin lỗi <b>' + this.dossierCode + '</b>',
          en: 'Apology letter <b>' + this.dossierCode + '</b>!'
        };
        this.postComment(msgObj[this.selectedLang]);
        requestBodyObj.comment = msgObj[this.selectedLang];
      }

      const agencies = this.agencyService.getAgencies();
      const extend = {
        dossier:{
          id: this.dossierId,
          code: this.dossierCode,
        },
        agencies: agencies
      };

      await this.notiService.changeSendSubject.next(
        {
          id: this.dossierDetail?.procedureProcessDefinition?.id,
          phone: this.dossierDetail?.applicant?.data?.phoneNumber,
          email: this.dossierDetail?.applicant?.data?.email,
          updateSend: false,
          renewContent: true,
          currentTask: this.currentTask,
          contentParams: {
            parameters: {
              sector: !!this.dossierDetail?.procedure?.sector?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name ? this.dossierDetail?.procedure?.sector?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name : '',
              procedure: !!this.dossierDetail?.procedure?.translate?.find(agency => agency.languageId === Number(this.selectedLangId))?.name ? this.dossierDetail?.procedure?.translate?.find(agency => agency.languageId === Number(this.selectedLangId))?.name : '',
              agency: !!this.dossierDetail?.agency?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name ? this.dossierDetail?.agency?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name : this.dossierDetail?.agency?.parent?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name,
              agencyId: !!this.dossierDetail?.agency?.id ? this.dossierDetail?.agency?.id : this.dossierDetail?.agency?.parent?.id,
              applicantFullname: this.dossierDetail?.applicant?.data?.fullname,
              officerFullname: '',
              subsystem: !!this.env?.subsystem?.name ? this.env?.subsystem?.name : "Hệ thống Một cửa điện tử",
              dossierCode: this.dossierDetail?.nationCode ? this.dossierDetail?.nationCode : this.dossierDetail?.code,
              nextTask: !!this.env?.notify?.additionalRequirement?.nextTask ? this.env?.notify?.additionalRequirement?.nextTask : 'Yêu cầu bổ sung hồ sơ',
              // IGATESUPP-63184
              nextStatus: !!this.env?.notify?.additionalRequirement?.nextStatus ? this.env?.notify?.additionalRequirement?.nextStatus : (this.dossierDetail?.dossierTaskStatus?.name ?? ''), // cho bo sung, trang thai hien tai cua ho so
              dossierStatus: !!this.env?.notify?.additionalRequirement?.dossierStatus ? this.env?.notify?.additionalRequirement?.dossierStatus : (this.dossierDetail?.dossierStatus?.name?.find(t => t.languageId === Number(this.selectedLangId))?.name ?? ''), // cho bo sung, trang thai hien tai cua ho so
              dossierSearchPadsvcUrl: (!!this.env?.domain?.padsvc ? this.env?.domain?.padsvc : "http://padsvctest.vncitizens.vn/vi/") + "dossier/public",
              dossierDetailPadsvcUrl: (!!this.env?.domain?.padsvc ? this.env?.domain?.padsvc : "http://padsvctest.vncitizens.vn/vi/") + "dossier/detail/" + this.dossierId,
              dossierSearchUrl: (!!this.env?.domain?.onegate ? this.env?.domain?.onegate : "http://padsvctest.vncitizens.vn/vi/") + "dossier/search/",
              dossierDetailUrl: (!!this.env?.domain?.onegate ? this.env?.domain?.onegate : "http://padsvctest.vncitizens.vn/vi/") + "dossier/search/" + this.dossierId,
              returnMethod: !!this.dossierDetail?.dossierReceivingKind?.name?.find(recieve => recieve.languageId === Number(this.selectedLangId))?.name ? this.dossierDetail?.dossierReceivingKind?.name?.find(recieve => recieve.languageId === Number(this.selectedLangId))?.name : '',
              receivingDate: '',
              appointmentDate: '',
              dossierFee: this.totalCost,
              reason: this.commentContentPlainText,
              numberDate: this.getNumberDate(),
              extend: extend
            }
          }
        }
      );

      const requestBody = JSON.stringify(requestBodyObj, null, 2);

      this.postHistory();
      this.notiService.confirmSendSubject.next({
        confirm: true,
        renewContent: false,
      });
      const dataLog = {
        dossierId: this.dossierId,
        code: this.dossierCode,
        body: requestBody
      };
      this.logmanService.postUserEventsLog('apologyLetter', dataLog).subscribe();

      this.dialogRef.close(true);
          
    }
  }

  
  onDismiss() {
    // xóa dữ liệu file tạm
    this.uploadFileNames.forEach(element => {
      this.procedureService.deleteFile(element.id).subscribe(res => {
      }, err => {
        console.log(err);
      });
    });
    this.uploadFileNames = [];
    //
    this.dialogRef.close();
  }

  postComment(commentContent) {
    let msgObj = {};
    if(!!commentContent){
      msgObj = {
        vi: `Thư xin lỗi: ${commentContent}`,
        en: `Apology letter: ${commentContent}`
      };
    }else{
      msgObj = {
        vi: `Thư xin lỗi: ${commentContent}`,
        en: `Apology letter: ${commentContent}`
      };
    }
    const content = {
      groupId: 2,
      itemId: this.dossierId,
      user: {
        id: this.accountId,
        fullname: this.userName
      },
      content: msgObj[this.selectedLang],
      file: this.uploadFileNames
    };
    const requestBody = JSON.stringify(content, null, 2);
    this.dossierService.postComment(requestBody).subscribe(data => { });
    this.postApologyLetter(msgObj[this.selectedLang]);
  }

  postApologyLetter(letterContent) {
    const content = {
      phoneNumber: this.dossierDetail?.applicant?.data?.phoneNumber,
      content: letterContent,
      dossierId: this.dossierId,
      userId: this.accountId
    };
    const requestBody = JSON.stringify(content, null, 2);
    this.dossierService.postApologyLetter(requestBody).subscribe(data => { });
  }

  getPlainText( strSrc ) {
    let resultStr = "";

    // Ignore the <p> tag if it is in very start of the text
    if(strSrc.indexOf('<p>') == 0)
        resultStr = strSrc.substring(3);
    else
        resultStr = strSrc;

    // Replace <p> with two newlines
    resultStr = resultStr.replace(/<p>/gi, "\r\n\r\n");
    // Replace <br /> with one newline
    resultStr = resultStr.replace(/<br \/>/gi, "\r\n");
    resultStr = resultStr.replace(/<br>/gi, "\r\n");

    // Replace &nbsp; with space
    resultStr = resultStr.replace(/&nbsp;/gi, " ");
    //-+-+-+-+-+-+-+-+-+-+-+
    // Strip off other HTML tags.
    //-+-+-+-+-+-+-+-+-+-+-+

    return  resultStr.replace( /<[^<|>]+?>/gi,'' );
  }

  getPlainText1( strSrc ) {
    let resultStr = "";

    // Ignore the <p> tag if it is in very start of the text
    if(strSrc.indexOf('<p>') == 0)
        resultStr = strSrc.substring(3);
    else
        resultStr = strSrc;

    // Replace <p> with one newline
    resultStr = resultStr.replace(/<p>/gi, "\n"); // --> Diff between getPlainText && getPlainText1
    // Replace <br /> with one newline
    resultStr = resultStr.replace(/<br \/>/gi, "\r\n");
    resultStr = resultStr.replace(/<br>/gi, "\r\n");

    // Replace &nbsp; with space
    resultStr = resultStr.replace(/&nbsp;/gi, " ");
    //-+-+-+-+-+-+-+-+-+-+-+
    // Strip off other HTML tags.
    //-+-+-+-+-+-+-+-+-+-+-+

    return  resultStr.replace( /<[^<|>]+?>/gi,'' );
  }

  onCommentEditorChange(event) {
    this.commentContent = event.editor.getData();
    if (this.ckeditorMaxLength > 0) {
      if (event.editor.getData().trim().length > this.ckeditorMaxLength) {
        this.isCKMaxlenght = true;
      } else {
        this.isCKMaxlenght = false;
      }
    } else if (event.editor.getData().trim().length > 500) {
      this.isCKMaxlenght = true;
    } else {
      this.isCKMaxlenght = false;
    }

    this.commentContentPlainText = "";
    this.commentContentPlainText = this.getPlainText(this.commentContent).trim();
  }

  uploadMultiFile(file, accountId) {
    return new Promise((resolve) => {
      this.procedureService.uploadMultiFile(file, accountId).subscribe(data => {
        if(!!data && data.length > 0){
          this.uploadedImage = data;
          this.uploadFileNames.push(...data);
          // this.formToJSON();
          resolve(true);
        } else {
          resolve(false);
        }
      }, err => {
        console.log(err);
        resolve(false);
      });
    });
  }

  async onSelectFile(event) {
    if (event.target.files && event.target.files[0]) {
      for (const i of event.target.files) {
        if (i.size >= this.maxFileSize * 1024 * 1024) {
          const msgObj = {
            vi: 'Kích thước tệp tin quá lớn, tên tệp: ' + i.name,
            en: 'The file is too large, file name: ' + i.name
          };
          this.snackbarService.openSnackBar(0, '', msgObj[this.selectedLang], 'error_notification', this.config.expiredTime);
          return;
        }
        if (this.acceptFileType.filter(t => t === i.type.toLowerCase()).length > 0) {
          let newFile = [];
          newFile.push(i);
          this.files.push(i);
          const extension = i.name.substring(i.name.lastIndexOf('.')).split('.').pop();
          this.urls.push(this.getFileIcon(extension));

          const reader = new FileReader();
          reader.onload = () => {
            this.uploaded = true;
          };
          if (i.name.length > 20) {
            const startText = i.name.substr(0, 5);
            const shortText = i
              .name
              .substring(i.name.length - 7, i.name.length);
            this.fileNames.push(startText + '...' + shortText);
            this.fileNamesFull.push(i.name);
          } else {
            this.fileNames.push(i.name);
            this.fileNamesFull.push(i.name);
          }
          reader.readAsDataURL(i);
          if (this.files.length > 0) {
            let checkFile = await this.uploadMultiFile(newFile, this.accountId);
            if (!checkFile) {
              const msgObj = {
                vi: 'Upload file thất bại!',
                en: 'File upload failed!'
              };
              this.snackbarService.openSnackBar(2, '', msgObj[this.selectedLang], 'warning_notification', this.config.expiredTime);
            }
          }
        } else {
          const msgObj = {
            vi: 'Không hỗ trợ loại tệp tin ',
            en: 'File type is not supported '
          };
          this.snackbarService.openSnackBar(0, '', msgObj[this.selectedLang] + i.type.substring(i.type.lastIndexOf('/') + 1).toUpperCase(), 'error_notification', this.config.expiredTime);
        }
      }
    }
  }

  removeItem(index: number) {
    this.procedureService.deleteFile(this.uploadFileNames[index].id).subscribe(res => {
    }, err => {
      console.log(err);
    });
    this.uploadFileNames.splice(index, 1);
    this.urls.splice(index, 1);
    this.fileNames.splice(index, 1);
    this.fileNamesFull.splice(index, 1);
    this.files.splice(index, 1);
    this.blankVal = '';
  }
  

  getFileIcon(ext) {
    return this.config.cloudStaticURL + 'icon/files/300x300/' + ext + '.png';
  }

  downloadTemplate(){
    window.open(this.fileTemplate, '_self');
  }

  async onCheckNumberDateChange($event){
    this.notifyCheck = this.checkSendNotifyComponent.notify;
    this.checkNumberDate = $event?.checked;
    //this.getDetailDossier();
    setTimeout(() => {
        this.getNotify();
    });
  }

  async changeFile(type){
    if (this.commentContent.trim() === '') {
      const msgObj = {
        vi: 'Vui lòng nhập ý kiến xử lý!',
        en: 'Please enter a handling comments!'
      };
      this.snackbarService.openSnackBar(2, '', msgObj[this.selectedLang], 'warning_notification', this.config.expiredTime);
      return
    }
    if (this.isCKMaxlenght || this.isCKDescriptionMaxlenght) {
      let msgObj;
      if (this.ckeditorMaxLength > 0) {
        msgObj = {
          vi: 'Nội dung không quá ' + this.ckeditorMaxLength + ' ký tự!',
          en: 'Content must not exceed ' + this.ckeditorMaxLength + ' characters!'
        };
      } else {
        msgObj = {
          vi: 'Nội dung không quá 500 ký tự!',
          en: 'Content must not exceed 500 characters!'
        };
      }
      this.snackbarService.openSnackBar(2, '', msgObj[this.selectedLang], 'warning_notification', this.config.expiredTime);
      return
    }
    const url = "https://staticv2.vnptigate.vn/file/hcm/test-mau-phieu-thu-tu-choi-hcm.docx";
    let blob = await fetch(url).then(r => r.blob());
    let address = this.dossierDetail?.applicant?.data?.address ? this.dossierDetail?.applicant?.data?.address :"";
    let village = this.dossierDetail?.applicant?.data?.village?.label ? this.dossierDetail?.applicant?.data?.village?.label : "";
    let district = this.dossierDetail?.applicant?.data?.district?.label ? this.dossierDetail?.applicant?.data?.district?.label : "";
    let province = this.dossierDetail?.applicant?.data?.province?.label ? this.dossierDetail?.applicant?.data?.province?.label : "";
    const value = {
      "day": tUtils.newDate().getDate(),
      "month": tUtils.newDate().getMonth() + 1,
      "year" : tUtils.newDate().getFullYear(),
      "fullName": this.dossierDetail?.applicant?.data?.fullname,
      "code": this.dossierDetail?.code,
      "address": address + ', '+ village + ', ' + district + ', ' + province,
      "appliedDate": this.formatDateTimeExportFile(this.dossierDetail?.appliedDate),
      "appointmentDate": this.formatDateTimeExportFile(this.dossierDetail?.appointmentDate),
      "reason": this.getPlainText1(this.commentContent)
    };

    const newComponent = [
      {
          "label": "ngày",
          "key": "day"
      },
      {
          "label": "tháng",
          "key": "month"
      },
      {
          "label": "năm",
          "key": "year"
      },
      {
          "label": "Hồ sơ của",
          "key": "fullName"
      },
      {
          "label": "Nội dung yêu cầu giải quyết",
          "key": "code"
      },
      {
          "label": "địa chỉ",
          "key": "address"
      },
      {
          "label": "Lý do",
          "key": "reason"
      },
      {
          "label": "Thời gian nhận hồ sơ",
          "key": "appliedDate"
      },
      {
          "label": "Thời gian trả kết quả giải quyết hồ sơ",
          "key": "appointmentDate"
      }
  ];
    this.procedureService.getChangeFileFormEform(blob, JSON.stringify(value), JSON.stringify(newComponent), type).subscribe(async data => {
      const dataType = data.type;
      const binaryData = [];
      binaryData.push(data);
      const blob = new Blob(binaryData, { type: dataType });
      let fileName = `Mẫu file thư xin lỗi.${type}`;
      const file = new File(binaryData, fileName);
      if (file.size >= this.maxFileSize * 1024 * 1024) {
        const msgObj = {
          vi: 'Kích thước tệp tin quá lớn, tên tệp: ' + file.name,
          en: 'The file is too large, file name: ' + file.name
        };
        this.snackbarService.openSnackBar(0, '', msgObj[this.selectedLang], 'error_notification', this.config.expiredTime);
        return;
      }
      let newFile =[];
      newFile.push(file);
      this.files.push(file);
      this.urls.push(this.getFileIcon(type));
      
      const reader = new FileReader();
      reader.onload = () => {
        this.uploaded = true;
      };
      if (fileName.length > 20) {
        const startText = fileName.substring(0, 5);
        const shortText = fileName
          .substring(fileName.length - 7, fileName.length);
        this.fileNames.push(startText + '...' + shortText);
        this.fileNamesFull.push(fileName);
      } else {
        this.fileNames.push(fileName);
        this.fileNamesFull.push(fileName);
      }
      reader.readAsDataURL(blob);
      if (this.files.length > 0) {
        let checkFile = await this.uploadMultiFile(newFile, this.accountId);
        if (!checkFile) {
          const msgObj = {
            vi: 'Upload file thất bại!',
            en: 'File upload failed!'
          };
          this.snackbarService.openSnackBar(2, '', msgObj[this.selectedLang], 'warning_notification', this.config.expiredTime);
        }
        console.log("this.uploadFileNames",this.uploadFileNames);
      }
   }, err => {
      const msgObj = {
        vi: 'Lỗi trong quá trình kết xuất file',
        en: 'Error occurred when extract file'
      };
      this.snackbarService.openSnackBar(0, '', msgObj[this.selectedLang], 'error_notification', this.config.expiredTime);
      return;
   });
  }

  getProcedureProcessDetail(prDefId) {
    return new Promise<void>((resolve) => {
      this.procedureService.getProcedureProcessDetail(prDefId).subscribe((data) => {
        this.procedureProcessDetail = data;
        resolve();
      }, (err) => {
        const msgObj = {
          vi: 'Không tìm thấy quy trình cho hồ sơ này!',
          en: 'No process found for this dossier!',
        };
        this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
        resolve();
      });
    });
  }

  async getFileFromResult(){
    let listFile = this.dossierDetail?.attachment?.filter(d => d.group == this.config.dossierResultFileTagId);
    if(listFile?.length > 0){
      for(var index = 0; index < listFile.length; index ++) {
        var item = this.files.filter(fi => fi?.id == listFile[index]?.id);
        if(item.length > 0){
          continue;
        } 
        //Kiểm tra file đã tồn tại
        try {
          if(this.uploadFileNames){
            //File đã tồn tại
            if(!this.uploadFileNames.find(o => o.filename == listFile[index].filename && o.size == listFile[index].size)){
              await this.downloadFileFromResult(listFile[index]);
            }
          }
        } catch (error) {
          console.log(error);
        }
      }
    } else {
      this.snackbarService.openSnackBar(0, 'Không có file đính kèm ở kết quả', '', 'error_notification', this.config.expiredTime);
    }
  }

  downloadFileExport(index: number){
    let file = this.uploadFileNames[index];
    this.downloadFile(file.id, file.filename);
  }

  downloadFile(id, filename) {
    this.procedureService.downloadFile(id, this.dossierId).subscribe(data => {
      const dataType = data.type;
      const binaryData = [];
      binaryData.push(data);
      const downloadLink = document.createElement('a');
      downloadLink.href = window.URL.createObjectURL(new Blob(binaryData, { type: dataType }));
      downloadLink.setAttribute('download', filename);
      document.body.appendChild(downloadLink);
      downloadLink.click();
    }, err => {
      const msgObj = {
        vi: 'Không tìm thấy file!',
        en: 'File not found!'
      };
      this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
      console.log(err);
    });
  }

  routerLink(file)
  {
    if(!file) return;
    let name = file.filename ? file.filename: file.name;
    name = name.toUpperCase();
    if(name.indexOf('.JPG') >= 0 || name.indexOf('.JPEG') >= 0 || name.indexOf('.PNG') >= 0)
      return ['/viewer-image/' + file.id, {dossierId: this.dossierId }];

    return ['/viewer/' + file.id, {dossierId: this.dossierId }];
  }

  async openPdfDigitalSignatureV2( attach, index, dsType?:number){
    // dsType => 1: ký số sim, default: smart-ca
    // type => 1: ý kiến xử lý, 2: kết quả, 3: thành phần hồ sơ
    let originDocFileName = attach.name ? attach.name : attach.filename;
    let filePdf, filePdfName, dialogData, checkIsDocFile = false;
    if(originDocFileName.indexOf('.docx') !== -1 || originDocFileName.indexOf('.doc') !== -1){
      filePdf = await this.fileService.convertDoc2Pdf(attach.id);
      filePdfName = originDocFileName.replace('.docx', '.pdf').replace('.doc', '.pdf');
      checkIsDocFile = true;
      dialogData = new PdfViewerDialog(filePdf.id, filePdfName, this.dossierId, dsType);
    } else {
      dialogData = new PdfViewerDialog(attach.id, attach.name ? attach.name : attach.filename, this.dossierId, dsType);
    }
    const dialogRef = this.dialog.open(PdfViewerComponent, {
      maxWidth: '100%',
      width: '100%',
      height: '100%',
      maxHeight: '100%',
      data: dialogData,
      disableClose: false,
      panelClass: 'custom-dialog-container'
      // autoFocus: false
    });
    dialogRef.afterClosed().subscribe( async rs => {
      if(rs){
        
        for await (const file of this.uploadFileNames) {
          if (file.id == attach.id) {
            let fileCurrent = this.uploadFileNames.filter(item => item.id == attach.id)[0];
            let index = this.uploadFileNames.indexOf(fileCurrent);
            if (rs.filename.length > 20) {
              const startText = rs.filename.substring(0, 5);
              const shortText = rs.filename
                .substring(rs.filename.length - 7, rs.filename.length);
              this.fileNames = this.fileNames.map((item, i) => i === index ? (startText + '...' + shortText) : item);
              const indexType = rs.filename.lastIndexOf(".");
              const extention = rs.filename.substring(indexType + 1);
              this.urls = this.urls.map((item, i) => i === index ? this.getFileIcon(extention) : item);
            }
            file.filename = rs.filename;
            file.size = rs.size;
            file.id = rs.id;
          }
        }  
        // await this.addFileSign( rs.id, rs.filename, rs.size);
        this.logmanService.postSignHistory(rs.id,rs.filename).subscribe();
      }
    });
  }

  async addFileSign( fileId, filename, size){
    const file = {
      id: fileId,
      filename: filename,
      size: size,
    };
    if (file.size >= this.maxFileSize * 1024 * 1024) {
      const msgObj = {
        vi: 'Kích thước tệp tin quá lớn, tên tệp: ' + file.filename,
        en: 'The file is too large, file name: ' + file.filename
      };
      this.snackbarService.openSnackBar(0, '', msgObj[this.selectedLang], 'error_notification', this.config.expiredTime);
      return;
    }
    let newFile = [];
    newFile.push(file);
    if (newFile.length > 0) {
      await this.uploadFileExport(newFile, this.accountId);
    }
        
  }

  uploadFileExport(file, accountId) {
    this.procedureService.uploadMultiFile(file, accountId).subscribe(data => {
      this.uploadFileNames.push(data[0]);
      console.log("this.uploadFileNames",this.uploadFileNames);
    }, err => {
      console.log(err);
    });
  }

  openPdfDigitalSignature( fileId, filename, index) {
    if(filename.indexOf('.docx') !== -1 || filename.indexOf('.doc') !== -1){
      const file = {
        id: fileId,
        name: filename
      };
      this.openPdfDigitalSignatureV2( file, null, 2);
      return;
    }
    const dialogData = new PdfViewerDialog(fileId, filename, this.dossierId);
    const dialogRef = this.dialog.open(PdfViewerComponent, {
      maxWidth: '100%',
      width: '100%',
      height: '100%',
      maxHeight: '100%',
      data: dialogData,
      disableClose: false,
      panelClass: 'custom-dialog-container',
      // autoFocus: false
    });
    dialogRef.afterClosed().subscribe(async (rs) => {
      if(rs){
       
        for await (const file of this.uploadFileNames) {
          if (file.id == fileId) {
            let fileCurrent = this.uploadFileNames.filter(item => item.id == fileId)[0];
            let index = this.uploadFileNames.indexOf(fileCurrent);
            if (rs.filename.length > 20) {
              const startText = rs.filename.substring(0, 5);
              const shortText = rs.filename
                .substring(rs.filename.length - 7, rs.filename.length);
              this.fileNames = this.fileNames.map((item, i) => i === index ? (startText + '...' + shortText) : item);
              const indexType = rs.filename.lastIndexOf(".");
              const extention = rs.filename.substring(indexType + 1);
              this.urls = this.urls.map((item, i) => i === index ? this.getFileIcon(extention) : item);
            }
            file.filename = rs.filename;
            file.size = rs.size;
            file.id = rs.id;
          }
        }  
        
        // await this.addFileSign( rs.id, rs.filename, rs.size);
        this.logmanService.postSignHistory(rs.id,rs.filename).subscribe();
      }
    });
  }

  async openNEAC( attach, index, dsType?:number){
    this.openPdfDigitalSignatureV2( attach, index, dsType);
  }

  async openVGCAplugin( fileId, filename, size){
    this.tempObj.fileId = fileId;
    this.tempObj.filename = filename;
    this.tempObj.size = size;
    this.filePDF.id = fileId;
    this.filePDF.filename = filename;
    this.checkIsDocFile = false;
    if( filename.indexOf('.docx') !== -1 || filename.indexOf('.doc') !== -1){
      const  filePdf = await this.fileService.convertDoc2Pdf(fileId);
      this.filePDF.filename = filename.replace('.docx', '.pdf').replace('.doc', '.pdf');
      this.filePDF.id  = filePdf.id;
      this.checkIsDocFile = true;
    }
    this.handleBinaryString(fileId);
  }

  handleBinaryString(fileId){

    this.configService.downloadFile( this.filePDF.id, this.dossierId).subscribe( async (file) => {
      var prms = {};
      prms["FileUploadHandler"] = this.adapterService.getVGCAAdapterCallBackUrl(fileId,this.tempObj.filename,this.accountId);
      prms["SessionId"] = "";
      // Create a public link
      const formData: FormData = new FormData();
      file.name = "example.pdf";
      formData.append('file', file, file.name);
      const fileResponse = await  this.uploadService.uploadFile(formData).toPromise();
      this.tempObj.tempFileId = fileResponse.id;
      const publicFileUrl = `${this.uploadService.publicFileUrl}/${fileResponse.id}.pdf`;
      console.log("publicFileUrl",publicFileUrl);
      // prms["FileName"] = "https://staticv2.vnptigate.vn/file/cmnd.pdf";
      prms["FileName"] = publicFileUrl;
      var json_prms = JSON.stringify(prms);
      vgca_sign_approved(json_prms, this.SignFileCallBack);
    });
  }

  SignFileCallBack = async rv => {
    var received_msg = JSON.parse(rv);
    if(this.tempObj.tempFileId){
      this.uploadService.deleteFile(this.tempObj.tempFileId).subscribe();
    }
    switch(received_msg.Status){
      case (14):{
        // Cancel :: do nothing
        const msgObj = {
          vi: 'Đã hủy ký số!',
          en: 'Cancel sign document!'
        };
        this.snackbarService.openSnackBar(2, msgObj[this.selectedLang], '', 'warning_notification', this.config.expiredTime);
        break;
      }
      case true: {
        // Sign file successfully
        break;
      }
      case false: {
        // Failed to sign file
        const msgObj = {
          vi: 'Ký số thất bại!',
          en: 'Signing failed!'
        };
        this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
        break;
      }
      case 0: {
        if (received_msg.FileServer === ''){
          const msgObj = {
            vi: 'Đã hủy ký số!',
            en: 'Cancel sign document!'
          };
          this.snackbarService.openSnackBar(2, msgObj[this.selectedLang], '', 'warning_notification', this.config.expiredTime);
          break;
        }
        // Sign file successfully
        let file = this.uploadFileNames.filter(item => item.id == this.tempObj.fileId);
        let index = this.uploadFileNames.indexOf(file);
        
        const newFilename = this.changeFilenameOfSignedFile(this.filePDF.filename);
        // await this.addFileSign( received_msg.FileServer, newFilename, this.tempObj.size);
        for await (const file of this.uploadFileNames) {
          if (file.id == this.tempObj.fileId) {
            let fileCurrent = this.uploadFileNames.filter(item => item.id == this.tempObj.fileId)[0];
            let index = this.uploadFileNames.indexOf(fileCurrent);
            if (newFilename.length > 20) {
              const startText = newFilename.substring(0, 5);
              const shortText = newFilename
                .substring(newFilename.length - 7, newFilename.length);
              this.fileNames = this.fileNames.map((item, i) => i === index ? (startText + '...' + shortText) : item);
              const indexType = newFilename.lastIndexOf(".");
              const extention = newFilename.substring(indexType + 1);
              this.urls = this.urls.map((item, i) => i === index ? this.getFileIcon(extention) : item);
            }
            file.filename = newFilename;
            file.size = this.tempObj.size;
            file.id = received_msg.FileServer;
          }
        }  
        
        this.logmanService.postSignHistory(received_msg.FileServer, newFilename).subscribe();
  
        const msgObjNoti = {
          vi: 'Ký số thành công',
          en: 'Signed successfully'
        };
        this.snackbarService.openSnackBar(1, msgObjNoti[localStorage.getItem('language')], '', 'success_notification', this.config.expiredTime);
        break;
      }
      default: {
        // Error
        console.log("error: ",received_msg.Message);
        const msgObj = {
          vi: 'Ký số thất bại!',
          en: 'Signing failed!'
        };
        this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
      }
    }
    return received_msg;
  }


  changeFilenameOfSignedFile(fullFilename:string):string{
    const signalStr = "_signed";
    const index = fullFilename.lastIndexOf(".");
    const extention = fullFilename.substring(index+1);
    const filename = fullFilename.substring(0,index);
    if(filename.length < 8){
      return `${filename}${signalStr}.${extention}`;
    }
    if(filename.slice(-7) != signalStr){
      return `${filename}${signalStr}.${extention}`;
    }
    return fullFilename;
  }

  checkIfDocFileOnly(filename): boolean {
    // File type
    const fileExtention = filename.split('.').pop();
    if (fileExtention.toLowerCase() == 'doc' ||  fileExtention.toLowerCase() == 'docx') {
      return true;
    }
    return false;
  }

  async openVnptCaPlugin( fileId, filename){
    if(this.checkIfDocFileOnly(filename))
        this.openVnptCaPluginForDocFile( fileId, filename);
      else
        this.openVnptCaPluginForPdfFile( fileId, filename);
    }
    async openVnptCaPluginForDocFile( fileId, filename){
      const filePdf = await this.fileService.convertDoc2Pdf(fileId);
      const fileNameDigitalSign = filename.split('.').slice(0, -1).join('.') + ".pdf";
      const result = await this.digitalSignatureService.signWithVNPTCA(filePdf.id,fileNameDigitalSign, this.dossierId);
      console.log("result",result);
      if(result.status == 1){
        for await (const file of this.uploadFileNames) {
          if (file.id == fileId) {
            let fileCurrent = this.uploadFileNames.filter(item => item.id == fileId)[0];
            let index = this.uploadFileNames.indexOf(fileCurrent);
            if (result.data.filename.length > 20) {
              const startText = result.data.filename.substring(0, 5);
              const shortText = result.data.filename
                .substring(result.data.filename.length - 7, result.data.filename.length);
              this.fileNames = this.fileNames.map((item, i) => i === index ? (startText + '...' + shortText) : item);
              const indexType = result.data.filename.lastIndexOf(".");
              const extention = result.data.filename.substring(indexType + 1);
              this.urls = this.urls.map((item, i) => i === index ? this.getFileIcon(extention) : item);
            }
            file.filename = result.data.filename;
            file.size = result.data.size;
            file.id = result.data.fileId;
          }
        }  
        
      }
    }
    async openVnptCaPluginForPdfFile( fileId, filename){
      const result = await this.digitalSignatureService.signWithVNPTCA(fileId,filename, this.dossierId);
      console.log("result",result);
      if(result.status == 1){
        for await (const file of this.uploadFileNames) {
          if (file.id == fileId) {
            let fileCurrent = this.uploadFileNames.filter(item => item.id == fileId)[0];
            let index = this.uploadFileNames.indexOf(fileCurrent);
            if (result.data.filename.length > 20) {
              const startText = result.data.filename.substring(0, 5);
              const shortText = result.data.filename
                .substring(result.data.filename.length - 7, result.data.filename.length);
              this.fileNames = this.fileNames.map((item, i) => i === index ? (startText + '...' + shortText) : item);
              const indexType = result.data.filename.lastIndexOf(".");
              const extention = result.data.filename.substring(indexType + 1);
              this.urls = this.urls.map((item, i) => i === index ? this.getFileIcon(extention) : item);
            }
            file.filename = result.data.filename;
            file.size = result.data.size;
          }
        }    
          
      }
    }

    checkIfFileIsSupported(filename): boolean {
      // File type
      const fileExtention = filename.split('.').pop();
      if (fileExtention.toLowerCase() == 'pdf' || 'doc' || 'docx') {
        return true;
      }
      return false;
    }
 



 async downloadFileFromResult(file){
    this.procedureService.downloadFile(file?.id, this.dossierId).subscribe(async data => {
      const dataType = data.type;
      const binaryData = [];
      binaryData.push(data);
      var blob = new Blob(binaryData, { type: dataType });
      var myFile = new File([blob], file.filename)
      this.files.push(myFile);
      const extension = file.filename.substring(file.filename.lastIndexOf('.')).split('.').pop();
       this.urls.push(this.getFileIcon(extension));

      
      if (file.filename.length > 20) {
        const startText = file.filename.substr(0, 5);
        const shortText = file.filename.substring(file.filename.length - 7, file.filename.length);
        this.fileNames.push(startText + '...' + shortText);
        this.fileNamesFull.push(file.filename);
      } else {
        this.fileNames.push(file.filename);
        this.fileNamesFull.push(file.filename);
      }
      //reader.readAsDataURL(blob);
      if (myFile) {
        let checkFile = await this.uploadMultiFile([myFile], this.accountId);
        if (!checkFile) {
          const msgObj = {
            vi: 'Upload file thất bại!',
            en: 'File upload failed!'
          };
          this.snackbarService.openSnackBar(2, '', msgObj[this.selectedLang], 'warning_notification', this.config.expiredTime);
        }
      }
    });
  }

  public formatDateTimeExportFile(inputDate: string): string {
    const date = new Date(inputDate);
    if (isNaN(date.getTime())) {
      return "";
    }
  
    const day = String(date.getDate()).padStart(2, "0");
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const year = date.getFullYear();
    const hours = String(date.getHours()).padStart(2, "0");
    const minutes = String(date.getMinutes()).padStart(2, "0");
  
    return `${hours} giờ ${minutes} phút, ngày ${day} tháng ${month} năm ${year}`;
  }
}

export class ConfirmApologyLetterDialogModel {
  constructor(public dossierId: string, public dossierCode: string, public typeProcess?) {
  }
}


