import { Injectable } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { ApiProviderService } from 'src/app/core/service/api-provider.service';
import { Observable } from 'rxjs';
import { SnackbarService } from 'data/service/snackbar/snackbar.service';
import { EnvService } from 'core/service/env.service';
import { Workbook } from 'exceljs';
import * as fs from 'file-saver';
import { each } from 'jquery';

const EXCEL_TYPE = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8';
const EXCEL_EXTENSION = '.xlsx';

@Injectable({
  providedIn: 'root'
})
export class HPGStatisticsService {
  language = localStorage.getItem('language') === 'vi' ? 228 : 46;
  config = this.envService.getConfig();

  constructor(
    private dialog: MatDialog,
    private http: HttpClient,
    private apiProviderService: ApiProviderService,
    private snackbar: SnackbarService,
    private envService: EnvService
  ) { }

  private basecatURL = this.apiProviderService.getUrl('digo', 'basecat');
   private basedataURL = this.apiProviderService.getUrl('digo', 'basedata');
   private basepadURL = this.apiProviderService.getUrl('digo', 'basepad');
   private padmanURL = this.apiProviderService.getUrl('digo', 'padman');
   //private padmanURL = "http://localhost:8081";
  // private basedataURL = "http://localhost:8888";
  // private basepadURL = "http://localhost:8069";
  private humanURL = this.apiProviderService.getUrl('digo', 'human')
 
  getDetailDossierTHSHHSHPG(search): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.padmanURL + '/hpg-dossier-statistic/digitization-detail-THSHHS' + search, { headers }).pipe();
  }
  
  exportDossierStatisticDetailTHSHHSHPG(params: string): any {
    const url = this.padmanURL + '/hpg-dossier-statistic/digitization-detail-THSHHS/--excel' + params;
    this.getFileExport(url).then();
  }
  getFileExport(url) {
    return new Promise((resolve) => {
      this.http.get(url, {
        observe: 'response',
        responseType: 'blob'
      }).toPromise().then(res => {
        const blob = new Blob([res.body], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
        let filename = 'bao_cao_chi_tiet_so_hoa.xlsx';
        if (res.headers.get('Content-Disposition') != null) {
          filename = res.headers.get('Content-Disposition').split(';')[1].split('=')[1].replace(/\"/g, '');
        }
        const blobUrl = URL.createObjectURL(blob);

        const xhr = new XMLHttpRequest();
        xhr.responseType = 'blob';

        xhr.onload = () => {
          const recoveredBlob = xhr.response;
          const reader = new FileReader();
          reader.onload = () => {
            const base64data = reader.result.toString();
            const object = JSON.stringify({
              mimeType: filename.substring(filename.lastIndexOf('.') + 1),
              name: filename.substring(0, filename.lastIndexOf('.')),
              data: base64data.split(',')[1]
            });
            const anchor = document.createElement('a');
            anchor.download = filename;
            anchor.href = base64data;
            anchor.click();
          };
          reader.readAsDataURL(recoveredBlob);
        };

        xhr.open('GET', blobUrl);
        xhr.send();
        resolve(true);
      }).catch(err => {
        if (err.status === 500) {
          const message = {
            vi: 'Hệ thống tạm thời không thể xuất file excel!',
            en: 'The system is temporarily unable to export the excel file!'
          };
          this.snackbar.openSnackBar(0, '', message[localStorage.getItem('language')], 'error_notification',
            this.config.expiredTime);
        }
        resolve(false);
      });
    });
  }

}
