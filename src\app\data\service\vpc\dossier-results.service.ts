import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { ApiProviderService } from 'src/app/core/service/api-provider.service';

@Injectable({
  providedIn: 'root'
})
export class DossierResultsService {

  private apiURL = this.apiProviderService.getUrl('digo', 'padman');
  private reporter = this.apiProviderService.getUrl('digo', 'reporter');
  constructor(
    private http: HttpClient,
    private apiProviderService: ApiProviderService,
  ) { }

  getTemplateById(search): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.reporter + '/template/' + search);
  }
}
