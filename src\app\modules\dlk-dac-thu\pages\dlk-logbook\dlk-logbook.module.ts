import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { DlkLogbookRoutingModule } from './dlk-logbook-routing.module';
import { DlkLogbookComponent } from './dlk-logbook.component';
import { SharedModule } from 'src/app/shared/shared.module';
import { NgxPrinterModule } from 'ngx-printer';


@NgModule({
  declarations: [DlkLogbookComponent],
  imports: [
    CommonModule,
    DlkLogbookRoutingModule,
    SharedModule,
    NgxPrinterModule.forRoot({ printOpenWindow: true })
  ]
})
export class DlkLogbookModule { }
