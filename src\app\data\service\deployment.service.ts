import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Subject } from 'rxjs';
import { EnvService } from 'src/app/core/service/env.service';

@Injectable({
  providedIn: 'root'
})
export class DeploymentService {

  envConfig = this.envService.getConfig();

  renewDeploymentConfig = new Subject<boolean>();

  getAppDeploymentUrls = this.envService.getConfig().deploymentUrl +
    `/app-deployment?deployment-id=${this.envService.getConfig().deploymentId}&app-code=${this.envService.getConfig().appCode}`;
    inputCheckCSDLDC=[
      {
        "fullname": ["fullname"], //danh sách các trường tên người nộp hồ sơ
        "identityNumber" : ["identityNumber"], //danh sách các trường cccd người nộ<PERSON> hồ sơ
        "birthday" : ["birthday"] // danh sách các trường ngày sinh người nộp hồ sơ
      },
      {
        "ownerFullname": ["ownerFullname"], //danh sách các trường tên chủ hồ sơ
        "ownerIdentityNumber" : ["ownerIdentityNumber", "cccd1"], //danh sách các trường cccd chủ hồ sơ
        "ownerBirthday" : ["ownerBirthday"] // danh sách các trường ngày sinh chủ hồ sơ
      }
    ]
    kiosDossierListParam:{
      choTiepNhan: "60f52e0d09cbf91d41f88834",
      tuChoi: "60f52ed209cbf91d41f88838",
      daTiepNhan: "60f52e0d09cbf91d41f88834, 60f52ed209cbf91d41f88838",
      daXuLy: "61ee30eada2d36b037e00005, 60f52fa009cbf91d41f8883c, 60f52f0109cbf91d41f88839",
      dangXuLy: "60f52e0d09cbf91d41f88834, 60f52ed209cbf91d41f88838, 61ee30eada2d36b037e00005, 60f52fa009cbf91d41f8883c, 60f52f0109cbf91d41f88839",
      dungHen:  "61ee30eada2d36b037e00005, 60f52fa009cbf91d41f8883c, 60f52f0109cbf91d41f88839",
      treHen:  "61ee30eada2d36b037e00005, 60f52fa009cbf91d41f8883c, 60f52f0109cbf91d41f88839",
    }

  oneGateReceiptStatusList={}
  maxSizeFileHCM=0;
  StatisticsDossierUnreceivedShowDossierCode: false;
  oneGateDigitizationDossierStatusList={
    "0":4,
    "1":5
  }
  OnlinePaymentMethodIdList={
    "0": "5fd1c7991b53d8779bc9ea7a",
    "1": "6700f6b9309b545eec294887",
    "2": "5f7fca9fb80e603d5300dcf5"
  }

  //Tên menu KHA điều chỉnh
  onlineRecordProcessingLabel = [
    {
      "languageId": 228,
      "name": "Tiếp nhận hồ sơ trực tuyến"
    },
    {
      "languageId": 46,
      "name": "Online Record Processing"
    }
  ];
  directRecordProcessingLabel = [
    {
      "languageId": 228,
      "name": "Tiếp nhận hồ sơ trực tiếp"
    },
    {
      "languageId": 46,
      "name": "Direct record processing"
    }
  ];
  recordsToBeProcessedLabel = [
    {
      "languageId": 228,
      "name": "Hồ sơ cần xử lý"
    },
    {
      "languageId": 46,
      "name": "Records to be processed"
    }
  ];
  IsCustomMenuEnabled = false;

  enableDVCLT = false; // IGATESUPP-101972 Mở chế độ kiểm tra DVCLL
  enableCheckCSDLQGInfo = false; // IGATESUPP-110355 Kiểm tra thông tin từ CSDLQG

  iOfficeQbh = false; //IGATESUPP-120464 Kết nối vs hệ thống QLVB QBH

  disableCheckboxSendSMSKHA = false; //kiểm tra ẩn checkbox gửi tin nhắn SMS của KHA
  fullTextKHA = false; //IGATESUPP-123173 hiển thị full text của thủ tục và quy trình KHA
  isShowCheckboxDeletePermanentKHA = false; //IGATESUPP-123173 hiển thị checkbox xóa vĩnh viễn của KHA
  enableScrollStepListKHA = false; //Hiển thị scroll step list của KHA
  configViewProcessingKHA = false;
  configViewStepByStepProcessKHA = false;
  showListDSTTHC = []; //ds id dont show Procedure
  timeDistance = 0;

  checkHideTabSector = {
    isShowSector: true,
    isShowSector_Agency: true,
    isShowSector_User: true
  };
  showSourceLLTP = false; // IGATESUPP-102419 hiển thị nguồn đăng ký LLTP
  isUseApplyMethodAndHinhthucnop = false; //IGATESUPP-107437 Lỗi cách tính báo cáo chung
  KGGReportCancel = false; // IGATESUPP-107922 Hiển thị cột Số lượng hồ sơ rút/hủy và chưa tiếp nhận vào Thống kê tổng hợp xử lý hồ sơ
  residentialInfoHBH: false; //IGATESUPP-113441 [HBH-iGate] - Bổ sung trường mã hồ sơ khi tra cứu CSDLQGVDC
  statisticsOfCitizenHBH: false; // //IGATESUPP-113441 [HBH-iGate] - Bổ sung trường mã hồ trong báo cáo /population-data/statistics-of-citizen
  sysDVCLTHBHEnable: false  //IGATESUPP-117918 HBH Thêm nút đồng bộ kết quả HTTP trong chi tiết HS của tra cứu HS toàn cơ quan

  isInvoiceProviderActivated = 1; // IGATESUPP-117106: Ẩn đi select option Nhà cung cấp ở Popup Phát hành biên lai
  isPaymentMethodActivated = 1; // IGATESUPP-117106: Ẩn đi select option Hình thức thanh toán ở Popup Phát hành biên lai
  dniStatistics = {
    dniRootAgency: "60b87fb59adb921904a0213e",
    agencyLevel0: "5f6b17984e1bd312a6f3ae4b",
    agencyLevel1: "5f7dade4b80e603d5300dcc4",
    agencyLevel2: "5f6b177a4e1bd312a6f3ae4a",
    procedureLevel4: "62b529f524023d508ef38fc0",
    procedureLevel3: "62b529c424023d508ef38fbd",
    procedureLevel2: "5f5b2c564e1bd312a6f3ae25",
    procedureLevel2Old: "5f5b2c2b4e1bd312a6f3ae23",
    hasSuspendedOrCancelled: false,
    donvibaocaoTag: "0000591c4e1bd312a6f00003"
  }
  idMauPhieuThongTinBienNhan = "67ef99512d3bb868a9fcd350"; //IGATESUPP-121473 [QBH - iGate 2.0] Điều chỉnh nội dung thông tin biên nhận hồ sơ tại mục "Tài liệu, hồ sơ trong quá trình xử lý"
  pathPhieuBienNhanQBH = "64251063a9abd9426d1f9a5e/2025/4/4/67efa6abc9fff319d82b3d50.rptdesign";
  env = {
    showCleanUser: false, //IGATESUPP-83177 hiện chức năng làm sạch tài khoản công dân
    dossierReturnDuringTheDay: 0, //IGATESUPP-95395 [HCM iGATE V2] Offsite - Lỗi ngày hẹn trả của hồ sơ tiếp nhận sau 15h bị tính sai  [SD18127]
    listNotRequiredDigitizingProcedure: [],//IGATESUPP-92617 HBH Danh sách thủ tục không cần số hóa
    listNotRequiredDigitizingFormProcedure: [],// HBH Danh sách thủ tục không cần số hóa TPHS
    reusedCriteriaEnterprise: false,//IGATESUPP-86001 [HCM iGATE V2] Offsite - Ghi nhận hồ sơ cho chỉ tiêu "tái sử dụng" trên BĐTC khi có tra cứu doanh nghiệp [SD17451]
    reusedCriteriaResidential : false , //IGATESUPP-85602-Offsite - Ghi nhận hồ sơ cho chỉ tiêu "tái sử dụng" trên BĐTC khi sử dụng thông tin từ CSDL dân cư [SD17435]
    isHiddenLocationConfigTemplate: false, // IGATESUPP-82822 ẩn nút chọn ví trí xuất hiện nút in phiếu khi cấu hình phiếu động cho đơn vị và mặc định
    remindWork473: false, // IGATESUPP-82312 - Tham số bật/tắt dashboard nhắc việc
    feeRefund: false, //IGATESUPP-82540 [HCM iGATE V2] Offsite - Đánh giá hệ thống TTQGTTHC theo VB 473 mục 9.2 [SD17222]
    enableImportDossierFulltime: false,
    checkExitProcedureFormDetail: false,    //IGATESUPP-79224 loi do dong bo tu dvc quoc gia khong co truong detail => bo qua
    ancestorId: '', //IGATESUPP-80946 ID co quan goc
    oldVersionPdf: false, //IGATESUPP-96089 version pdf cũ
    notifyLoan: false, // IGATESUPP-77176-bật để hiển thị component checkbox sms, zalo, email
    defaultDirectly: false, //IGATESUPP-72841-[IGATE-2.0][QNM] - Giấy tờ đầu ra hiển thị trong thành phần hồ sơ nộp trực tuyến
    idFee: [], //ds id của loại phí
    idFees: [], //ds id của loại lệ phí
    isPaidStaticticalFee: 0, //=1 để hiển thị các hồ sơ đã thanh toán, =0 để hiển thị tất cả
    showReCheckPayment: true, // Mac dinh bac tham so
    connectrequestDocument: false,
    paymentMethodCategoryId: '5f3a491c4e1bd312a6f00011',
    OS_HGI:{
      hideFeeStatistic: {
        enable: false,            // bật/tắt cho phép thay đổi
        allowedMenu: [            // menu áp dụng
        ],
        notice: 'Tạm ẩn chức năng'
      },
      listAgencyUseNewTemplateStatisticReceipt:[  // list cơ quan áp dụng mẫu thống kê biên lai mới
       ],
       dossierFeeOnlyAgencyIdHGI: false, // (true: chỉ load lĩnh vực thuộc cơ quan, false: load lĩnh vực thuộc cơ quan + lĩnh vực được phân cho tất cả cơ quan)
       showStatisticsPaymentDossierFeeHgi: {// thêm hình thức thanh toán trên Menu Thống kê lệ phí hồ sơ
        enable: false,
        default: "Trực tiếp",
        listPayment: [
          { id: "5f7fca83b80e603d5300dcf4", name: "Trực tiếp"},
          { id: "62f5d79b5c424b277f174318", name: "Trực tuyến"},
        ]
      },
      isShowExcelDossierFeeHGI : false, //  Tính năng xuất Excel trên Menu Thống kê lệ phí hồ sơ( True Button Mở,False Button Đóng)
      citizenPaymentMethod: false, // hiển thị hình thức thanh toán thực tế
      isGetListSetorHGI:false, // IGATESUPP-73106,IGATESUPP-73081,IGATESUPP-73238 : Chỉ định sẽ lấy danh sách lĩnh vực theo HGI hoặc mặc định
      isOptionSearchHGI:false, // IGATESUPP-73106,IGATESUPP-73081 : Chỉ định sẽ ẩn các thuộc tính tìm kiếm nâng cao theo HGI
      isGetListProcedureHGI:false, //IGATESUPP-73106,IGATESUPP-73081 : Chỉ định sẽ lấy danh sách thủ tục theo HGI hoặc mặc định
      isGetListAgencyHGI:false,//IGATESUPP-73106,IGATESUPP-73081 : Chỉ định sẽ lấy danh sách cơ quản/ đơn vị theo HGI hoặc mặc định
      isSearchHGI:false,//IGATESUPP-73106,IGATESUPP-73081 : Chỉ định sẽ tìm kím ngay khi load trang theo HGI
      enableStatisticalAll: 0, //IGATESUPP-63375: Chỉnh sửa chức năng thống kế theo đơn vị // 0: không hiển thị, 1: hiển thị
      "agencyLevelId": {
        "level1": "5f7dade4b80e603d5300dcc4",
        "level2": "623300ca05955c108cf27eb2",
        "level3": "5f6b177a4e1bd312a6f3ae4a"
      },
      enableStatisticalProcedure: 0, //IGATESUPP-63370: Chỉnh sửa chức năng thống kế theo TTHC // 0: không hiển thị, 1: hiển thị
      isSTTorCheckBox: false, // khahc.hgi - IGATESUPP-72943 : chỉ định hiển thị số thứ tự hay checkbox ở danh mục tiếp nhận hồ sơ
      isRequestHGI: 0, // khahc.hgi - IGATESUPP-68733 : bật/tắt đường dẫn đồng bộ lý lịch tư pháp theo HGI hoặc mặc định 0 là mặc định cảu HCM 1 là của HGi_v1, 2 là của HGI V2
      isShowHGI: false,
      enableStatisticalSector: 0, //triph.hgi-IGATESUPP-62944: Chỉnh sửa chức năng thống kế theo lĩnh vực //0: không hiển thị, 1: hiển thị
      showHGICustomTextUnsupportedFileTypeSign: false, //khoitn.hgi-IGATESUPP-71247 Điều chỉnh cảnh báo ký số đối với file khác định dạng pdf và word; Tham số bật = true: hiển thị custom text cảnh báo là giá trị text của tham số HGICustomTextUnsupportedFileTypeSign; Tham số tắt = false: hiển thị cảnh báo mặc định
      HGICustomTextUnsupportedFileTypeSign: "Không thể xem loại tệp tin này", //khoitn.hgi-IGATESUPP-71247 Điều chỉnh cảnh báo ký số đối với file khác định dạng pdf và word; Khi tham số showHGICustomTextUnsupportedFileTypeSign = true thì hiển thị cảnh báo là giá trị text của tham số này
      identityNumber_Processing_HGI: false, //vush.hgi-IGATESUPP-73358 [HGI-OS] Chỉnh giao diện menu xử lý hồ sơ
      searchHighPerformenceMenuXLHS: false, //vush.hgi-IGATESUPP-73358 [HGI-OS] Chỉnh giao diện menu xử lý hồ sơ
      deleteDoissierHGI: false, //vush.hgi-IGATESUPP-73358 [HGI-OS] Chỉnh giao diện menu xử lý hồ sơ
      identityNumber_Reception_HGI: false, //vush.hgi-IGATESUPP-73082 Chỉnh sửa giao diện menu Hồ sơ chờ tiếp nhận trang web 1 cửa
      searchHighPerformenceHgi: false, //vush.hgi-IGATESUPP-73082 Chỉnh sửa giao diện menu Hồ sơ chờ tiếp nhận trang web 1 cửa
    },
    OS_BTTTT:{
      isEnabledNotify: false, // IGATESUPP-62366 iGate Bộ 4T: Giao diện - Bổ sung chuông thông báo khi công dân/doanh nghiệp đăng nhập
      isBoTTTT: false,
      agencyLevel0: "5f6b17984e1bd312a6f3ae4b", // IGATESUPP-115398 iGate Bộ 4T: Bổ sung Thống kê "Đánh giá chất lượng giải quyết TTHC" trên trang một cửa
      agencyLevel1: "5f7dade4b80e603d5300dcc4",
      agencyLevel2: "5f6b177a4e1bd312a6f3ae4a",
      procedureLevel4: "62b529f524023d508ef38fc0",
      procedureLevel3: "62b529c424023d508ef38fbd",
      procedureLevel2: "62b52a0224023d508ef38fc1",
      donvibaocaoTag: "63be60537b74d1683ae149c4",
      ratingAgencyId: "60c868a4289bad69c7cbffea",
      ratingAgencyName: {
        vi :"Bộ Thông tin và Truyền thông",
        en :"Ministry of Information and Communications"
      },
      statisticKGGGetSuppendedCancelled: true, // IGATESUPP-115398 iGate Bộ 4T: Bổ sung Thống kê "Đánh giá chất lượng giải quyết TTHC" trên trang một cửa
      applyDirect:{
        checkApplyDirect: 0, // 0: không hiển thị, 1: hiển thị
        textApplyDirect: 'Nộp hồ sơ trực tiếp' // Text hiển thị,
      },
      allowEditProcedureFee: false,
      hideOtherAgencyDossierSearch: false, // IGATESUPP-67604 bỏ chức năng tra cứu hồ sơ đơn vị khác của tài khoản cán bộ
      displayComboBoxTypeProcedure:false,
      listProcedure:[
          {
            id:1,
            stt:1,
            translate:[
              {
                languageId: 228,
                name: "Bưu chính"
              }
            ],
            code:"BC",
            link: "/icon/btttt/buu_chinh_icon.png"
          }
        ],
        formFileDirect: { // IGATESUPP-77306: iGate 2.0-Bộ TTTT: Bổ sung chức năng tích chọn để bỏ qua và nộp tphs trực tiếp
          enable: false,
          description: "Nộp giấy tờ trực tiếp"
        },
      },
    domain: {
      padsvc: ''
    },
    enablePopupSelectTask: false,
    enableCheckLogSyncDossier: false,
    enableClickRightPopupSelectTask: false, //IGATESUPP-59250-thuannm [HCM iGATE V2] Offsite_Lỗi cán bộ không xử lý được hồ sơ (Khi nhiều cán bộ cùng xử lý 1 bước) [SD2216], set mặc định true để tắt chức năng
    dossierTaskStatus: {
      waitingForAcceptance: {
        id: '61e7bc8627d8493f10d9c4ee',
        vi: 'Chờ tiếp nhận',
        en: 'Waiting for acceptance'
      },
      justRegistered: {
        id: '60e409823dfc9609723e493c',
        vi: 'Mới đăng ký',
        en: 'Just registered'
      },
      waitingAdditional: {
        id: '60e409983dfc9609723e493e',
        vi: 'Chờ bổ sung',
        en: 'Waiting additional'
      },
      requestForAdditionalDocuments: {
        id: '60ebf03109cbf91d41f87f8b',
        vi: 'Yêu cầu bổ sung giấy tờ',
        en: 'Request for additional documents'
      },
      dossierAdded: {
        id: '60ebf0db09cbf91d41f87f8c',
        vi: 'Đã bổ sung hồ sơ',
        en: 'Dossier added'
      },
      notAccepted: {
        id: '60ebf14b09cbf91d41f87f8d',
        vi: 'Không được tiếp nhận',
        en: 'Not accepted'
      },
      resultReturned: {
        id: '60ebf17309cbf91d41f87f8e',
        vi: 'Đã trả kết quả',
        en: 'Result returned'
      },
      cancelled: {
        id: '60ed1a5909cbf91d41f87f9f',
        vi: 'Hồ sơ đã hủy',
        en: 'Cancelled'
      },
      pending: {
        id: '60ed1b7c09cbf91d41f87fa0',
        vi: 'Đang tạm dừng',
        en: 'Pending'
      },
      hadResult: {
        id: '60ed1c3409cbf91d41f87fa1',
        vi: 'Có kết quả',
        en: 'Had result'
      },
      hasWithdrawn: {
        id: '6147e4c4ebfba925f1e89bf0',
        vi: 'Đã rút hồ sơ',
        en: 'Has Withdrawn'
      },
      waitWithdrawn: {
        id: '6151c771ba2a04299f949875',
        vi: 'Chờ rút hồ sơ',
        en: 'Wait Withdrawn'
      },
      waitingForApprovalToAddition: {
        id: '61ee30eada2d36b037e00001',
        vi: 'Chờ phê duyệt bổ sung',
        en: 'Waiting for approval to addtition'
      },
      waitingForApprovalToPause: {
        id: '61ee30eada2d36b037e00002',
        vi: 'Chờ phê duyệt tạm dừng',
        en: 'Waiting for approval to pause'
      },
      waitingForApprovalToExtend: {
        id: '61ee30eada2d36b037e00003',
        vi: 'Chờ phê duyệt gia hạn',
        en: 'Waiting for approval to extend'
      },
      waitingForApprovalToCancel: {
        id: '61ee30eada2d36b037e00004',
        vi: 'Chờ phê duyệt dừng xử lý',
        en: 'Waiting for approval to cancel'
      },
      cancelDossier: {
        id: '61ee30eada2d36b037e00005',
        vi: 'Dừng xử lý',
        en: 'Cancel processing'
      },
      paymentRequest: {
        id: '62e35794a106a86e839f765b',
        vi: 'Yêu cầu thanh toán',
        en: 'Request for payment'
      },
      paidDossier: {
        id: '62e35811a106a86e839f765f',
        vi: 'Đã thanh toán',
        en: 'Paid'
      }
    },                           //ThinhHP: Chuyen thong tin mac dinh trang thai tu environment sang
    dossierMenuTaskRemind: {
      waitingForAcceptance: {
        id: '61e7bc8627d8493f10d9c4ee',
        vi: 'Chờ tiếp nhận',
        en: 'Waiting for acceptance'
      },
      requestForAdditional: {
        id: '60f52e6a09cbf91d41f88836',
        vi: 'Yêu cầu bổ sung',
        en: 'Request for additional'
      },
      dossierAdded: {
        id: '60f52eaf09cbf91d41f88837',
        vi: 'Đã bổ sung hồ sơ',
        en: 'Dossier added'
      },
      notAccepted: {
        id: '60f52ed209cbf91d41f88838',
        vi: 'Từ chối',
        en: 'Not accepted'
      },
      requestForAdditionalDocuments: {
        id: '60f52e6a09cbf91d41f88836',
        vi: 'Yêu cầu bổ sung',
        en: 'Request for additional'
      },
      hasWithdrawn: {
        id: '6151c728ba2a04299f949871',
        vi: 'HS đã rút',
        en: 'Has Withdrawn'
      },
      justRegistered: {
        id: '60f52e0d09cbf91d41f88834',
        vi: 'Mới đăng ký',
        en: 'Just registered'
      },
      cancelled: {
        id: '60f52f3109cbf91d41f8883a',
        vi: 'Đã hủy',
        en: 'Cancelled'
      },
      pending: {
        id: '60f52f5209cbf91d41f8883b',
        vi: 'Tạm dừng',
        en: 'Pending'
      },
      hadResult: {
        id: '60f52fa009cbf91d41f8883c',
        vi: 'Có kết quả',
        en: 'Had result'
      },
      resultReturned: {
        id: '60f52f0109cbf91d41f88839',
        vi: 'Đã trả kết quả',
        en: 'Result returned'
      },
      withdrawalRequest: {
        id: '6147e72bebfba925f1e89bf4',
        vi: 'HS yêu cầu rút',
        en: 'Withdrawal request'
      },
      hadWithdrawn: {
        id: '6151c728ba2a04299f949871',
        vi: 'HS đã rút',
        en: 'Has withdrawn'
      },
      waitingForApprovalToAddition: {
        id: '61ee30eada2d36b037e00001',
        vi: 'Chờ phê duyệt bổ sung',
        en: 'Waiting for approval to addtition'
      },
      waitingForApprovalToPause: {
        id: '61ee30eada2d36b037e00002',
        vi: 'Chờ phê duyệt tạm dừng',
        en: 'Waiting for approval to pause'
      },
      waitingForApprovalToExtend: {
        id: '61ee30eada2d36b037e00003',
        vi: 'Chờ phê duyệt gia hạn',
        en: 'Waiting for approval to extend'
      },
      waitingForApprovalToCancel: {
        id: '61ee30eada2d36b037e00004',
        vi: 'Chờ phê duyệt dừng xử lý',
        en: 'Waiting for approval to cancel'
      },
      cancelDossier: {
        id: '61ee30eada2d36b037e00005',
        vi: 'Dừng xử lý',
        en: 'Cancel processing'
      },
      paymentRequest: {
        id: '62e359c1a106a86e839f7667',
        vi: 'Yêu cầu thanh toán hồ sơ',
        en: 'Request for payment of dossier'
      },
      paidDossier: {
        id: '62e35a51a106a86e839f7668',
        vi: 'Đã thanh toán hồ sơ',
        en: 'Paid dossier'
      },
      specializationRefused: {
        id: '691e4a0967411b0b0d000011',
        vi: 'Từ chối từ chuyên ngành',
        en: 'Specialization refused'
      }
    },                        //ThinhHP: Chuyen thong tin mac dinh trang thai tu environment sang
    autoTimesheetConfigProcess24h: false,
    syncToAsxhConfigId: '',                               // IGATESUPP-42717 ConfigId asxh
    securitySmsContent: true,                            // IGATESUPP-32257-mongtt: 10.4. Điều chỉnh luồng gửi tin nhắn lấy thông tin template từ service (ATBM)
    securityEmailContent: true,                            // IGATESUPP-48793-tuqn (ATBM)
    fakeCompletedDate: false,                            // IGATESUPP-17240-thoaidt: Tính hạn xử lý khác
    useOldStatusProcessReceptionAdditional: false,
    taskCompletedStatusIds: [                            // IGATESUPP-17240-thoaidt: Tính hạn xử lý khác
      '60ebf17309cbf91d41f87f8e',
      '60ed1c3409cbf91d41f87fa1'
    ],
    subsystemId: "5f7c16069abb62f511880003",             //
    enableAppointmentDateEdit: false,                    // IGATESUPP-thoaidt: Cập nhật ngày hẹn trả khi tiếp nhận
    enableProcessDateEdit: false,                        // IGATESUPP-thoaidt: Cập nhật ngày khi xem tiến trình
    enableForceEndProcess: false,                        // IGATESUPP-21054-thoaidt: Chức năng kết thúc sớm hồ sơ
    enableReassignDossier: false,                        // IGATESUPP-20911-thoaidt: Chức năng điều chỉnh người đang xử lý của hồ sơ
    hideDeleteButton: false,                             // IGATESUPP-24036-thuannm bổ sung điều kiện ẩn hiện cho admin
    fileFormat: ".JPG, .JPEG, .PNG",                     // IGATESUPP-23376-thuannm cấu hình địng dạng file
    enableFileFormat: true,                              // IGATESUPP-23376-thuannm cấu hình địng dạng file
    hasParticipatedInProcessing: false,                  // IGATESUPP-24383-thoaidt: Hiển thị hồ sơ mà cơ quan có tham gia vào xử lý
    paginationType: 'slice',                             // IGATESUPP-thoaidt: Phân trang theo page hoặc slice
    enableLoadChildAgencyForOwnProcess: false,           // IT360-649654-thoaidt: Load thêm đơn vị con đối với quy trình dùng riêng
    enableAgencyCbxPreviousTask: 1,                      // IGATESUPP-29367-thoaidt: Hỗ trợ mở cbx để chọn đơn vị đối với đơn vị load từ bước trước
    enableFunctRequiredInputEForm: false,                // IGATESUPP-30747-thoaidt: Chức năng cho phép không cần nhập thông tin chi tiết khi tiếp nhận trực tiếp
    receivingOnlySaveBtn: {                              // IGATESUPP-43323-thoaidt: Thêm nút Lưu ở chức năng Tiếp nhận hồ sơ trực tiếp
      enable: false,
      justRegisteredTaskStatus: '60e409823dfc9609723e493c',
      justRegisteredMenuTaskRemind: '60f52e0d09cbf91d41f88834'
    },
    enableMergeDeepDossier: {                            // IGATESUPP-thoaidt: Bật/tắt khả năng merge 2 object dossier
      onlineReception: true
    },
    searchConnectionDossier: false ,                         // IGATESUPP-29024: Hồ sơ liên thông
    enableButtonAIMS: false,                              //IGATESUPP-66459: Tích hợp iGate 2 & AIMS
    syncToAIMSConfigId:'',                                //IGATESUPP-66459: Tích hợp iGate 2 & AIMS
    swapFullName2ContactPersonForTBKM: {
      isSwap: false,
      procedureId: [
        "6226d7d9f5b1b47c066e2564",
        "6226d7d9f5b1b47c066e2566"
      ]
    },                                                   // IGATESUPP-27123-thinhHP: Doi vi tri fill thong tin truong Fullname va contactPerson cho thu tuc Thong bao khuyen mai
    rootAgencyId: this.envConfig.rootAgency.id,          // IGATESUPP-26776-thoaidt: Biến xác định id của đơn vị gốc
    vnpost: {                                            //IGATESUPP-28735-phucnh.it2: Biến này quy định hình thức trả kết quả tại nhà
      receiveResultsByAddress: "5f8969018fffa53e4c073dee",
      endpointVnpost: '', //Địa chỉ gọi dịch vụ tích hợp vd '/service/vnpost/send-order?system=vnpt'
      isUseVnpostV2: false //IGATESUPP-82790-Su dung cau hinh tich hop vnpost moi (true de su dung va nguoc lai)
    },
    uploadFileTypeAllows: [
        'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/pdf',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'text/plain',
      'image/jpeg', 'image/png'],           // duyan-IGATESUPP-27409: Biến xác định các định dạng file đc tải lên ở xử lý hồ sơ
    enableApprovaledAgencyTreeView: false,               // IGATESUPP-29067-thoaidt: Hiển thị đơn vị được phê duyệt dưới dạng cây
    maxFileSize: 10,                     // Cấu hình maxFile upload file
    updatedDateSortDossierSearch: false,                  // IGATESUPP-thoaidt: Cấu hình sort theo updatedDate ở menu tra cứu hồ sơ toàn cơ quan
    enableValidateResidentialInfo: false,                 // IGATESUPP-41703-thoaidt: Bật/tắt chức năng "Xác thực thông tin dân cư hợp lệ"
    sortMenuRemind: {                                     // IGATESUPP-44943-thoaidt: Cho phép sắp xếp danh sách công việc theo thứ tự alphabet
      enable: false,
      allowedMenu: [
        'dossier/search',
        'dossier/search-by-agency',
        'dossier/search-personal'
      ]
    },
    pageSizeOptions: {              // IGATESUPP-51006-thoaidt: Cho phép thay đổi giá trị hiển thị dữ liệu trên 1 trang
      enable: false,            // bật/tắt cho phép thay đổi
      allowedMenu: [            // menu áp dụng
        'dossier/processing'
      ],
      size: [5, 10, 20, 50]     // mảng số lượng hiển thị (cấu hình svc spring.data.web.pageable.max-page-size)
    },
    getDataApplyToForm : 0,         // IGATESUPP-45465-thuannm: thêm tham số apply data to eform
    hideAppointmentDateWhenSaveDossier: true, //IGATESUPP-47751 [HCM iGate V2] Sở Xây dựng - Hồ sơ chưa tiếp nhận nhưng thống kê quá hạn [SD 1419] => Sai nghiệp vụ nên để tham số chung
    disableAppointmentDateWhenSaveDossier : true, //IGATESUPP-61116-thuannm: Bỏ tự set ngày tiếp nhận và ngày hẹn trả khi lưu hồ sơ
    webPadsvcSubsystemId:'5f7c16069abb62f511880006',// IGATESUPP-71075
	moveMultipleDossier: 0,
	  OS_QTI: {
      extendTimeIsAppointmentDate: false, //IGATESUPP-73422 bật true ngày gia hạn hồ so là hạn xử lý toàn trình
      qtiDashboardMotcua: false,
      showFileResultEnable : false, //IGATESUPP-78518 Nhờ a/c kiểm tra chức năng trả không giải quyết
      changeTag: false,//IGATESUPP-72871 bật true đổi nhãn
      qtiCapSo: '5ff6b1a706d0e31c6bf13e09',
      // DICH VU CONG 766KTM
      dataTypeMapping : "9ace807a62681a8b4f9b2103",
      provinceKtmId: "60b87fb59adb921904a0213e",  //id của tỉnh
      tagDistrictId: "5f7dade4b80e603d5300dcc4",  // tag id của huyện
      tagCommuneId: "5f6b177a4e1bd312a6f3ae4a",  //tag id của xã
      idAgencyProvince766: "366612",   // id của tỉnh trên 766
      idTinhPaknTheoLoai: "H50", // Mã của tỉnh
      idTinhPaknTheoDiaBan: "H50",
      DS_HUYEN: [
        {id_str:"655dca90cecceb05ee4449a4", id:10691},//DHA
        {id_str:"655dca93cecceb05ee4449a5", id:10697},//CLO
        {id_str:"655dca9bcecceb05ee4449a8", id:10695},//GLI
        {id_str:"655dca99cecceb05ee4449a7", id:10694},//HHO
        {id_str:"655dca87cecceb05ee4449a1", id:10698},//HLA
        {id_str:"655dca96cecceb05ee4449a6", id:10726},//TPHO
        {id_str:"655dca9ececceb05ee4449a9", id:10693},//VLI
        {id_str:"655dca8dcecceb05ee4449a3", id:10696},//DRK
        {id_str:"655dca8acecceb05ee4449a2", id:10692},//QTI
      ],
      //END DICH VU CONG 766KTM

    },
    OS_KTM: {
      isKTMCitizenStatistic: true,
      lockCitizenStatisticDate: {
        enable: false,
        minFromNow: 30,
        maxFromNow: 0
      },
      enableSyncDossierNBRS: false,      // Tham số chức năng đồng bộ hs từ bộ KHĐT
      dvcltConfig: {
        provinceCode : 62,
        rootAgencyId : "60b87fb59adb921904a0213e",
        distristTagId: "5f7dade4b80e603d5300dcc4",
        listMappingTypeId: "5f417a5d9268af8bef919f04,5fcd8b5d92b5c3aa6f9e1104",
        procedureCodeKS: "1.011592",
        procedureCodeKT: "1.011733,1.011537"
      },
      isKTMDigitalAuthEnable: true,
      authProcedure: true,
      digitalAuthDocumentTypeId: "65d4637c849caa2feafe8332",
      enableCheckFormApply: false, //IGATESUPP-64319-Lỗi tiếp nhận hồ sơ có giấy tờ đã xóa
      isListAllAgency: false,         //IGATESUPP-62534 Chỉnh sửa Danh mục tài khoản thụ hưởng
      isEmbedStorage: false,         //IGATESUPP-51659 Tích hợp trang quankhikho vào trang motcua
      isUpdateKQ: false,             //IGATESUPP-55484 Chỉnh sửa chức năng tải giấy tờ lên đối với hồ sơ đã trả kết quả
      mustChooseAssignee: false,
      isAccountIntegration: false, //IGATESUPP-69031 Bật/ tắt chức năng tích hợp trang tài khoản vào trang một cửa
      isShowUserProcessingKTM: true,
      enableSyncToBLDTBXH : false,                         // IGATESUPP-39816-phuongtnd: Bật / tắt tích hợp Bộ LĐTBXH
      syncToBLDTBXHConfigId: "",                           // IGATESUPP-39816-phuongtnd: Tích hợp Bộ LĐTBXH ConfigId
      dossierReceivingDirectlyId : "5f8968888fffa53e4c073ded", // IGATESUPP-39816-phuongtnd: Hình thức nhận KQ trực tiếp
      verifyFinanceObligationId : "60f6364e09cbf91d41f88858", // IGATESUPP-39816-phuongtnd: Xác minh nghĩa vụ tài chính
      payFinanceObligationId : "60f6364e09cbf91d41f88859", // IGATESUPP-39816-phuongtnd: Thực hiện nghĩa vụ tài chính
      procedureProvinceLevelId : "60b8b5979adb921904a02150", // IGATESUPP-39816-phuongtnd: TTHC cấp tỉnh
      procedureProvinceDepartmentLevelId : "60b8b5a29adb921904a02151", // IGATESUPP-39816-phuongtnd: TTHC cấp phòng ban thuộc tỉnh
      procedureDistrictLevelId : "5f39f4155224cf235e134c59", // IGATESUPP-39816-phuongtnd: TTHC cấp huyện
      procedureCommueLevelId : "5febfe2295002b5c79f0fc9f", // IGATESUPP-39816-phuongtnd: TTHC cấp xã
      mappingDossierStatusId : "5fc07c9c8c1fc38bef9e5f04", // IGATESUPP-39816-phuongtnd: Service Map trạng thái hồ sơ id        // IGATESUPP-22921-tinhphKtm : HIển thị người xử lý ngoài menu tra cứu toàn cơ quan
      constructConfigId:"643f9849e72fb063475e9483",  // IGATESUPP-42889: Config-id BXD
      isSyncConstructKTM:false,  // IGATESUPP-42889: tham so bat tat dong bo ho so Bo Xay Dung
      parentIdUBNDKTM : "60b87fb59adb921904a0213e",             // Tham số parentId KTM
      changeExportEformMajor: false,    // thoaidt-IGATESUPP-51079: Thay đổi nghiệp vụ xuất biểu mẫu giấy tờ
      procedureId:[], //cuonghk-IGATESUPP-57631: danh sách ID Thủ tục hành chính
      agencyId:[], // cuonghk-IGATESUPP-57631 danh sách các đơn vị chi nhánh văn phòng
      isEnterpriseTax: false, // phuongtnd-IGATESUPP-76688 Thanh toán nghĩa vụ tài chính về đất đai của doanh nghiệp
      downloadByUuid : "61a70552a86e440edc81f5d8",  //IGATESUPP-80155 Lập trình thay đổi chức năng gửi file đính kèm tích hợp với Bộ LĐTBXH theo ATTT
      isCheckUuidDownload : false
    },

    OS_QNI: {
      "enableSyncDossierTNMTQni": false, //IGATESUPP-84613: [iGate2.0][QNI] - Tích hợp Hệ thống thông tin giải quyết TTHC trên toàn quốc Bộ TN&MT- 24.05.2024
      "integratedTnmtConfigIdQni": "66554990ed9f4f516a5b81c7", //IGATESUPP-84613: [iGate2.0][QNI] - Tích hợp Hệ thống thông tin giải quyết TTHC trên toàn quốc Bộ TN&MT- 24.05.2024
      "enableModifiedDigitizingReport": false,
      "downloadDetailDossierBtn": false,
      "enableShowPaymentOnlineQni": false,
      "integratedGPLXConfigId":"633fd6ff08128056384e0cd7",
      "iOfficeQni": false,
      "enableUpdateAttachmentVbdlis": false,
      "enableResendErrorSmsLog": false,
      "autoFillApprovalComments": false,
      "enableViewDossierProcessingTaskOneGate": false,
      "deleteDossierQni": false, // IGATESUPP-70516 - tham số bật/ tắt nút xóa hồ sơ
      "ShowParentAgencyQni": false,
      "enableModifiedDossierGtvtQni": false,// Điều chỉnh giao diện trang danh sách hồ sơ bộ giao thông vận tải QNI
      "enableCheckMultiDossierAssignee": false, //IGATESUPP-69056 - - Chức năng chuyển nhiều hồ sơ không bắt buột người nhận- 26.09.2023
      "isSortByOwnerFullname": false,
      "statisticDVCLTQni": false,
      "provinceCode": "51",
      "originalAppointmentDate": false, //IGATESUPP-65579 - tham số bật/tắt ngày hẹn trả ban đầu
      isFilterDoubbleForm : false, //IGATESUPP-66855-Lọc giấy tờ giống nhau thì chỉ lấy 1
      "RemoveStopProcessingButton": false, // IGATESUPP-64744 - bật/tắt nút dừng xử lý ở menu hồ sơ chờ tiếp nhận
      "isQNIColums": false,
      "enableChangeReporterDigitizingQni": false, //IGATESUPP-72566 thay đổi báo cáo thống kê số hóa QNI
      "enableLoaiCapNhatToVBDLIS": false, // IT360-958091 - [iGate 2.0][QNI] - Bổ sung LoaiCapNhat qua VBDLIS - 30.10.2023
      "enableViewProcessingVbdlisBtn": false, // IGATESUPP-63793 - Bổ sung chức năng xem quá trình xử lý của hồ sơ VBDLIS 06.11.2023
      mergeProcedureReportQni: false, //IGATESUPP-55009 - gộp các thủ tục giống nhau cho báo cáo chung theo thủ tục - Qni
      "enableUpdateProcedure": false, //IGATESUPP-58167 - Không tìm thấy thủ tục để tiếp nhận 06.09.2023
      "dossierSyncListEnable" : false,
      "enableShowFullDocument": false, //IGATESUPP-55628 - Không tìm thấy giấy tờ khi tiếp nhận
      "canceledProcessing": false, //IGATESUPP-57170 - Kiểm tra trạng thái hồ sơ - 29.03.2023 - Gấp
      enableGetLogSMSQNI: false, //IGATESUPP-54352 - Kiểm tra phát sih tin nhắn SMS- 28.07.2023 -Gấp
      showCheckStatusDossier : { //IGATESUPP-56398 - Cán bộ không thấy hồ sơ để phê duyệt
        enable : false,
        dossierStatusList: "12",
        message : "Trạng thái dừng xử lý thì không được phê duyệt"
      },
      "showFindAutoAppointmentDate" : false, // IGATESUPP-56536-lọc hồ sơ tự động
      showFinancialObligations: { // IGATESUPP-56746 - Lại lỗi bước "thực hiện nghĩa vụ tài chính"
        enable : false,
        messageComment : "Đồng ý phê duyệt yêu cầu gia hạn hồ sơ: <p>Đồng ý</p>",
        remindProcessDefinitionTaskId : "60f6364e09cbf91d41f88859",
        approvalDataType : 10
      },
      setOnlyApprovalAgency: false, // IGATESUPP-51500 - Đánh giá lại chức năng phê duyệt hồ sơ - 19.06.2023
      "enableApprovaledAgencyId": false,    // IGATESUPP-52192:[iGate2.0][QNI] - Kiểm tra luồng phê duyệt hồ sơ- 24.06.2023
      "isQNIVBDLIS": false,
      "showDateReWorkingDate": false,                 // IGATESUPP-26959-tuqn: Hiển thị 'ngày' theo tên của timesheet trong cấu hình quy trình
      "showListAssigneeAction": false,                // IGATESUPP-29113-yenlinh: Bật/tắt nút "Danh sách cán bộ xử lý" khi xem quy trình
      "showRemindWorkAtHome":false,                          //IGATESUPP-29256-huuminh: Hiển thị chức năng nhắc việc QNI - IGATESUPP-44938
      "useDueDateRemindWork": 1,                            //IGATESUPP-29256-huuminh: Giá trị 1: hạn xử lý toàn quy trình. Giá trị 0 hoặc không tồn tại tham số: ngày hẹn trả
      "enableFilterSubAgencyDueDossier": false, // IGATESUPP-33134: [iGate2.0][QNI] - Thống kê hồ sơ đến hạn - 09.08.2022
      "noCharacterCommentRefuse":0,    //IGATESUPP-33905 - [iGate2.0][QNI] - Mở rộng trường nhập thông tin từ chối - 29.11.2022
      "downloadExcelProcedure":false,    //IGATESUPP-33904 - [iGate2.0][QNI] - Tạo chức năng xuất DB danh mục thủ tục - 29.11.2022
      "showExtraReportQNi":false,
      "maxItemSearch":100,
      "titleGeneralReport":"",
      "isIntegratedTBNOHTTTL": false,
      vbdlisConfigId: "633fd6ff08128056384e0cd7", //thuan-IGATEUPP-39007 VBDLIS HCM giai đoạn 2
      "checkProcedureExist": true,
      "contentProcessingBtn": false, // nút xem nội dung xử lý
      "allowNextStepWaitingForApprovalQNI": false, // IGATESUPP-51195 QNI
      levelTagID:{
        capSo:"611e494a43b16972f62dfd36",
        capHuyen:"5f7dade4b80e603d5300dcc4",
        capXa:"5f6b177a4e1bd312a6f3ae4a",
        daiDai:"6399e7ae35e1b72daaa5a422"
        },
      "showCancelProcessingOnlineReception": false,
      QniHeThongText : false,
      showPublisherColumnQNI: false, // IGATESUPP-43659 Hiển thị cột người phát hành biên lai cho QNI
      filterDossierBySector: false, // IGATESUPP-45083 : [iGate 2.0][QNI] - phân quyền cán bộ vẫn hiển thị tất cả hồ sơ ở mục hồ sơ chờ tiếp nhận 12.04.2023(Khẩn cấp)
      enablePaymentName : false, //IGATESUPP-45246 Hiển thị thanh toán khi nộp hồ sơ trực tuyến
      enableDossierReceivingBtn : false,  //IGATESUPP-46738 Khóa chức năng tiếp nhận khi hồ sơ đã dừng xử lý
      "enableWithdrawDossierBtn": false,  // Quyền yêu cầu rút hồ sơ
      enableButtonUpdateVBDLIS: false, // Quyền hiển thị nút Cập nhật gia hạn/tạm dừng/ bổ sung qua VBDLIS
      GTVTQNI: false,  //  tuqn-IGATESUPP-70982 Tích hợp Hệ thống dịch vụ công lĩnh vực đường bộ trong nước - 29.12.2023
      GTVTQNIAgencyCode: "SGTVT8",  //  tuqn-IGATESUPP-70982 Tích hợp Hệ thống dịch vụ công lĩnh vực đường bộ trong nước - 29.12.2023
      GTVTQNIstatus: false,  //  tuqn-IGATESUPP-70982 Tích hợp Hệ thống dịch vụ công lĩnh vực đường bộ trong nước - 29.12.2023
      enableimplementationQNI : false,//hoannk-IGATESUPP-83097- Tra cứu dịch vụ công theo đối tượng
      enabledeterminationQNI : false //hoannk-IGATESUPP-83097- Tra cứu dịch vụ công theo đối tượng
    },
    OS_GLI:{
      boTNMT: false, //IGATESUPP-84717 Đồng bộ Bộ TNMT
      sectorsFilter : false //IGATESUPP-91933 //[Igate_GLI] Chỉnh sửa chức năng "Thống kê lệ phí hồ sơ"
    },
    OS_QNM: {
      agencyPaymentOnline: [],
      isQNM: false,                     //IGATESUPP-30137 HIỂN THỊ THÔNG TIN THANH TOÁN TRÊN HỒ SƠ CHỜ TRẢ KẾT QUẢ; isQNM: kiểm tra có phải là QNM hay không
      syncProcedureWithAgencySelect: true,     // IGATESUPP-37355-yenlinh : Bật/tắt khi đồng bộ thủ tục chỉ truyền những cơ quan đang chọn
      updateAssignee: false, //IGATESUPP-38727 Bật true bổ sung cập nhật người xử lý ở bước xác nhận hoàn thành
      styleSigndesk:{
        applicant: ".style_38",
        officer:".style_39"
      },
      showSignDesk : false,
      enableSyncToBLDTBXH : false,                         // IGATESUPP-39816-phuongtnd: Bật / tắt tích hợp Bộ LĐTBXH
      syncToBLDTBXHConfigId: "",                           // IGATESUPP-39816-phuongtnd: Tích hợp Bộ LĐTBXH ConfigId
      dossierReceivingDirectlyId : "5f8968888fffa53e4c073ded", // IGATESUPP-39816-phuongtnd: Hình thức nhận KQ trực tiếp
      verifyFinanceObligationId : "60f6364e09cbf91d41f88858", // IGATESUPP-39816-phuongtnd: Xác minh nghĩa vụ tài chính
      payFinanceObligationId : "60f6364e09cbf91d41f88859", // IGATESUPP-39816-phuongtnd: Thực hiện nghĩa vụ tài chính
      procedureProvinceLevelId : "5f39f42d5224cf235e134c5a", // IGATESUPP-39816-phuongtnd: TTHC cấp tỉnh
      procedureDistrictLevelId : "5f39f4155224cf235e134c59", // IGATESUPP-39816-phuongtnd: TTHC cấp huyện
      procedureCommueLevelId : "5febfe2295002b5c79f0fc9f", // IGATESUPP-39816-phuongtnd: TTHC cấp xã
      mappingDossierStatusId : "5fc07a5d9c1fc38bef9e5f04", // IGATESUPP-39816-phuongtnd: Service Map trạng thái hồ sơ id
      agencyLevelDefault: {
        so: "5f39f42d5224cf235e134c5a",
      },
      "searchAppointmentDate": false,                        //IGATESUPP-46304-nanta: Bật/ tắt tìm kiếm hồ sơ theo ngày hẹn trả ở menu Xử lý hồ sơ
      listPaymentMethodQNM: [],                              //IGATESUPP-47766-nanta: danh sách hình thức thanh toán
      searchPaymentMethod: false,                              //IGATESUPP-47766-nanta: Bật/tắt tìm kiems trạng thái và hình thức thanh toán ở menu Hồ sơ chờ tiếp nhận
      agency20206c:["000000000191c4e1bd300047","634cccb7947a0a27e1145aee"], //IGATESUPP-46108 UBND Tỉnh Quảng Nam và Trung tâm hành chính công tỉnh
      smartCAByToken: false, // Bật true ký số smartCA nhiều hồ sơ với 1 lần login
      exportExcelQNM: false,                                  //IGATESUPP-46307-nanta: Bật/tắt xuất excel QNM
      smartCAByManyPosition: false, // IGATESUPP-49246 Bật true ký số smartCA nhiều chữ ký trên 1 hồ sơ
      titleGeneralReport: "Thống kê theo thông tư 01/2020/TT-VPCP/06c",   //IGATESUPP-46687 minhnh Tên báo cáo
      levelTagID:{                           //IGATESUPP-46687 minhnh List loai don vị (tag-id)
        capSo:"632d106e90c1a314b8738100",    //SO-BAN-NGANH
        capHuyen:"5f7dade4b80e603d5300dcc4", //HUYEN-THIXA-THANHPHO
        capXa:"5f6b177a4e1bd312a6f3ae4a",    //XA-PHUONG-THITRAN
        datDai:"634d0781ed7d3f0dd3506276",  //Chi Nhanh VPDKDD của cua Huyen
      },
      maxLengthProcedueName: 1000, //IGATESUPP-53263 Cấu hình maxlength tên thủ tục biên lai quy định
      filterByPositionAgencyType: false, //IGATESUPP-51745: Bật/Tắt hiển thị chức vụ thay vì đơn vị
      searchCancel:false, //IGATESUPP-51959: Bật/tắt tìm kiếm ngày từ chối
      remindCancelId:"60f52ed209cbf91d41f88838", // IGATESUPP-51959: id từ chối
      actionSubmitQNM: false, //nanta-IGATESUPP-53040 Bật/ tắt hiển thị trường hình thức nộp hồ sơ và xuất excel
      sendEmailReceipt: false, //IGATESUPP-48103 bổ sung email khi phát hành biên lai
      isInfoReceiptFee : false,   //IGATESUPP-46672 Show payment for QNM
      changColorButton: false,   // nanta-IGATESUPP-56530 Bậc/ tắt màu sắc button ở menu hồ sơ chờ tiếp nhận
      isSmsQNM: false, // IGATESUPP-46194: Bật/Tắt cấu hình gửi sms QNM
      showAllSectorStatisticsDue: false, //IGATESUPP-57777 Bật true để hiển thị tất cả lĩnh vượt trong báo cáo đến hạn
      filterByCandidateGroup: false, //IGATESUPP-56274 bật true lọc danh sách xử lý theo đơn vị
      enableUpdateFeePerm: false, //IGATESUPP-56800 Bật true để tách quyền phát hành biên lai và cập nhật lệ phí
      addSectorCode:false, //IGATESUPP-59500 Bật true để thêm mã lĩnh vực - tên lĩnh vực
      autoSyncILisAfterRecievedSuccess: false, //IGATESUPP-54761 Bật true tự động đồng bộ hồ sơ qua iLis sau khi tiếp nhận thành công.
      integratedIlisReceivingDossier: false, // IGATESUPP-58941 Tích hợp nút Bổ sung hồ sơ qua iLis và Cập nhật thực hiện nghĩa vụ tài chính qua ILIS
      iLisQNM: false, //IGATESUPP-58806 Thống kê chỉnh lại cách tính số liệu hồ sơ của iLis (báo cáo riêng QNM)
      addTaxPaymentFileILIS: false, //IGATESUPP-62612 [QNM] - Hỗ trợ đính kèm file thông báo thuế về hệ thống iLis
      requiredTaxPaymentFileILIS: false, //IGATESUPP-62612 [QNM] - Hỗ trợ đính kèm file thông báo thuế về hệ thống iLis
      extensionFileILIS: [
        "application/msword",
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        "application/pdf"
      ], //IGATESUPP-62612 [QNM] - Hỗ trợ đính kèm file thông báo thuế về hệ thống iLis
      changeSignPosition: false, // IGATESUPP-59742 đổi vị trí chữ kí
      selectedOptionFee:false, //IGATESUPP-61053 bật true để vừa chọn phí, vừa sửa được phí
      filterFeeForProcedure: false, //IGATESUPP-67817 lọc theo phí cho Procedure.
      ilisCompleteConfirm: false, //IGATESUPP-69114 [QNM] - Xác nhận hoàn thành trên IGate đối với hồ sơ liên thống iLis
      isNotShowPauseEXtendTimeBtn: false, //IGATESUPP-78358 [QNM] - BỔ SUNG THAM SỐ THỰC HIỆN CẤU HÌNH LẠI TẤT CẢ CÁC QUY TRÌNH
      isQNMProcedureForm: false, //IGATESUPP-73930 [QNM]- Import thành phần hồ sơ
      isOrderByAppointmentDate: false  //IGATESUPP-82597 - Sắp xếp hồ sơ theo ngày hẹn trả tại menu xử lý hồ sơ
    },
    OS_DBN: {
      tandanDBN: false,
      "timeDefault" : 6000, // fix bất đồng bộ phí
      "enableUpdateDossierFee": false,
      showCheckDossierStatus : {
        dossierStatusList: "3,6,12,13",
        enable : false,
        message : "Hồ sơ ",
        message1 : " đang tạm dừng, rút hồ sơ theo yêu cầu, dừng xử lý thì không chuyển xử lý được"
      },
      "agencyLevelId": {
        "level1": "5f7dade4b80e603d5300dcc4",
        "level2": "0000591c4e1bd312a6f00003",
        "level3": "0000591c4e1bd312a6f00004"
      },
      "rootAgencyId": "60a4d2589b05fd04a5381ffd",
      stepZaloTemplate: {
        enable: true,
        actionBtn: "action.open.inapp",
        actionBtnIcon: "",
        actionBtnText: "",
        viewFileRefuseBtnText: "",
        viewFileAdditionalRequirementBtnText: "",
        viewFilePaymentRequirementBtnText: "",
        banner: "",
        applyOnline: "78f80c19305cd902804d",
        onlineReception: "462933c80f8de6d3bf9c",
        additionalRequirement: "d495a3749f31766f2f20",
        resultsReturned: "1abb6a5a561fbf41e60e",
        refuse: "6f1719f625b3cced95a2"
      },
      listAgencyType: {
          provincialType: '5f39f4335224cf235e134c5b',
          districtType: '5f39f4155224cf235e134c59',
          wardType: '5febfe2295002b5c79f0fc9f'
      },
      dvclt: {// phuongnt-IGATESUPP-70305 đinh nghĩa các tham số cho thống kê dvclt
        KS: '1.011592',
        KT: '1.011733,1.011537',
        provinceCode: '11',
      },
      listProcedureSyncBLDTBXH: ['2.000286.000.00.00.H18', '2.000282.000.00.00.H18', '2.000477.000.00.00.H18', '1.001776.000.00.00.H18', '1.001758.000.00.00.H18', '1.001753.000.00.00.H18', '1.001731.000.00.00.H18', '2.000777.000.00.00.H18', '1.001739.000.00.00.H18', '2.000744.000.00.00.H18', '2.000751.000.00.00.H18'],
      isConnectBxdDBN: false,    // IGATESUPP-43265-yenlinh: Tham số kết nối BXD nhà ở hình thành trong tương lai qua trục LGSP_TANDAN
      syncAllProcedureFormWithCloseOldForm: true,    // IGATESUPP-35952-yenlinh: Bật/tắt đồng bộ trong trường hợp All thì giấy tờ sẽ thêm mới/đóng cũ/cập nhật trùng
      procedureFormWithAcceptOutput: true,         // IGATESUPP-35952-yenlinh: Bật/tắt truyền tham số accept-output=1 khi lấy danh sách giấy tờ đồng bộ
      showActionSubmit: false, // dhhcuong-IGATESUPP-43539: Hiện/ẩn cột hình thức nộp = true là hiện, = false là ẩn
      checkCitizenWithUserName: false,
      checkCitizenWithUserNameValue: '@dienbien.gov.vn',
      isStatisticsDbnBk: false, //IGATESUPP-57251 Bật/tắt báo cáo phí lệ phí mới cho DBN
      isAddExtendDBN: false, //phuongnt-IGATESUPP-64038 Bật tắt lưu Fee vào dossier
    },
    hideTabHistory : false, // IGATESUPP-94817 Ẩn hiện tab lịch sử
    OS_HBH: {
      typeStatisticDigitizedFormHbh: 0,
      limitStatisticDigitizedFormHbh: 0,
      enableSyncToSoHoa: false,
      showSyncToSoHoa: false,
      sendCodeOneGate: false,
      isLimit: false,
      dayLimit: 5,
      showAllAgency: true,
      statisticDigitizedHidenColum: true,
      enableReportGeneralLevel2level3: false,
      isStatisticAllHBH: false, // donglm-IGATESUPP-71731: sửa báo cáo chung đơn vị theo yêu cầu sửa sở TTTT
      enableGetChildOfChild: false,
      enableReportGeneral: false, // donglm-IGATESUPP-71731: sửa báo cáo chung đơn vị theo yêu cầu sửa sở TTTT
      listGetChildStaticAll: [], // donglm-IGATESUPP-71731: sửa báo cáo chung đơn vị theo yêu cầu sửa sở TTTT
      enableSyncTriNam: false,//phuongnt-IGATESUPP-55145: Bật tham số cho cấu hình liên thông qua trục Trí Nam
      enableVnpostTriNam: false, //phuongnt-IGATESUPP-54582: THam số bật tắt tích hợp vnpost Trí Nam,
      mappingHttpAgency: '', //phuongnt-IGATESUPP-55145: config id map cơ quan hộ tịch
      configIdTriNam: '', //phuongnt-IGATESUPP-55309 Config id(nếu có/ k thì mặc định lấy theo serviceId)Hỗ trợ kết nối ĐKDN qua trục Trí Nam
      isConnectBxdHbh: false,    // haipv-IGATESUPP-60802: Tham số kết nối BXD nhà ở hình thành trong tương lai qua trục LGSP_TRINAM
      lblSyncHbhBxd: '',//'Đồng bộ DVC xây dựng qua LGSP - HBH'
      isTnmtTriNam: false, //phuongnt-IGATESUPP-60963 Bật tắt tham số để đồng bộ hs lên TNMT khi xử lý hồ sơ
      configIdTNMTTriNam: '', //phuongnt-IGATESUPP-60963
      isStatisticsDossierFeeHBH: false,//donglm.hbh - IGATESUPP-64592:
      enableLDXHTriNam: false,
      configIdLDXHTriNam: '', //configid trí nam LDXH
      enableOnlinePayment: false,
      fillMoreEformCitizen: {},  // tuqn-IGATESUPP-77011 [hbh][igate-v2] Hỗ trợ fill thông tin từ TK công dân đăng nhập vào eFORM
      deepLevelAgencies: ["63d86a058d04897589af1274"],
      ancestorId: '60b87fb59adb921904a0213e',
      tagIdCQHC: '0000591c4e1bd312a6f00003',
      listHaveResultDossierStatus: [4, 5],
      idAgency_STNMT: '63d721368d04897589af1092', // Sở tài nguyên môi trường
      idAgency_VPDKDDT: '63d86a058d04897589af1274', // Văn phòng đăng ký đất đai tỉnh
      listNotRequiredDigitizingProcedure : [], // các thủ tục không tính số hóa
      enableHbhShowDetailProcedure: false
    },
    OS_HCM: {
      warningFileOptional : false , // IGATESUPP-115935 [HCM iGATE V2] Quận Tân Bình - Hiệu chỉnh nút "Kết thúc nhiều hồ sơ"
      showMethodPayment: { //IGATESUPP-86485 lehoanganh.hcm [HCM iGATE V2] Offsite - Bổ sung thêm hình thức thanh toán trực tuyến trên nền tảng khác [SD 17228]
        enable: false,
        defaultMethod: '5f7fca83b80e603d5300dcf4',
        methodPaymentList: ['66826b187bc2ff36bf24a970', '66826ad57bc2ff36bf24a96f', '64103af25706bf6444283fd2', '5f7fca83b80e603d5300dcf4'],
        methodOnlineList: ['66826ad57bc2ff36bf24a96f', '66826b187bc2ff36bf24a970'],
        categoryList: ['5f3a491c4e1bd312a6f00011', '64103ac25706bf6444283fd1']
      },
      showQuickLookupDossier: true, // bổ sung tham số tắt tra cứu nhanh
      constraintGetResults: false,
      showColumnsBillingInformation: {
        enable: false // IGATESUPP-86045 thêm cột trạng thái thanh toán của hồ sơ trông menu thống kê hồ sơ bị từ chối
      },
      disableButtonSendMessage : {  // IGATESUPP-86596 Disable nút tick Gửi tin nhắn cho cán bộ kế tiếp
        enable: false,
      },
      sendListDosseirVNPOST: false,
      autoFillBusinessRegistrationEnterpriseEform:{ // IGATESUPP-83300 Thêm tính năng Autofill thông tin doanh nghiệp vào eform
        enable: false
      },
      thongKeLLTPfilterSort: {  // nguyenttai.hcm IGATESUPP-83298 [HCM - iGATE v2] Sở Tư Pháp - Bổ sung filter Sắp xếp vào menu Thống kê hồ sơ Lý lịch Tư Pháp tiếp nhận trực tuyến[SD17183]
        enable: false,
        agencyIds: []
      },
      beneficiaryAcc : false, //IGATESUPP-82008 [HCM iGATE V2] Offsite - Điều chỉnh để danh mục tài khoản thụ hưởng hiển thị theo đơn vị [SD17120]
      lgspChangedProcedures : {
        enable: false,
        agencyCode: 'H29',
        dateFormat: 'dd/MM/yyyy'
      },
      isBTDKT: false,
      idAgency_BTDKT: "62979f8a011f773c23acd32d",
      idAgency_SNV: "633d36aab043644e7d707e60",
      roleParallelProcess: false,
      hideCalcutatorDue:false, //IGATESUPP-81486 - [HCM iGate V2] Sở công thương - nhờ cập nhật lại trạng thái hồ sơ [SD17146]
      showStatusHTTPLTKH : false, // IGATESUPP-80856 Offsite - Tích hợp API trạng thái hộ tịch thủ tục "Liên thông kết hôn
      receiptDirect : {
        isDefaultElectronicReceipt: false, // Bật/tắt nút trong cấu hình thủ tục
        isDefaultBusinessInformation: false, // Bật/tắt chức năng mặc định
        suppliers: '63368ef843beb047e15596d8',
        formOfPayment: 'Trực tiếp',
        receiptBook: "1",
        isSingleDossierReceipt: false,
        isPrintReleasePees: false
      },
      receiptOnline : {
        isDefaultElectronicReceipt: false, // Bật/tắt nút trong cấu hình thủ tục
        isDefaultBusinessInformation: false, // Bật/tắt chức năng mặc định
        suppliers: '63368ef843beb047e15596d8',
        formOfPayment: 'Trực tiếp',
        receiptBook: "1",
        isSingleDossierReceipt: false,
        isPrintReleasePees: false
      },
      newStatisticVPUB:{
        enable:false,
        agencyIds:[""]
      }, //IGATESUPP-77509 [HCM iGATE V2] Offsite - Điều chỉnh menu "Thống kê hồ sơ VPUB xử lý" [SD3652]
      isExportHCM: false,
      newProcedureSelect : false, // IGATESUPP-77628 [HCM iGate V2] Sở Xây dựng - Không tìm thấy hồ sơ mới đăng ký khi chọn tìm kiếm theo tên thủ tục [SD 3731]
      integratedSidewalk : { // IGATESUPP-75802 - lehoanganh.hcm : tích hợp thu phí lòng đường vỉa hè
        enableFees : false,
        apiAuthKey : "DVC_API_Key",
        apiAuthValue : "02099139-5f4a-40c4-a1e8-f953e12950e5",
        apiUrl : "https://api-thuphiviahe.hcmtelecom.vn",
        apiUrnGetFee : "api/DVC/GetFee",
      },
      enableShowSMSAgencyParent: 0,
      statisticBdtcTagId: '0000591c4e1bd312a6f00003',//IGATESUPP-76297 [HCM iGATE V2] Offsite - Bổ sung thêm menu chức năng kết xuất chi tiết danh sách hồ sơ có các trường dữ liệu của BĐTC TPHCM [SD 3546]
      showReceivingAddress: false, // IGATESUPP-74783 [HCM iGATE V2] Quận Tân Bình - Thêm cột địa chỉ gửi kết quả đến người dân tại Menu "Thống kê sổ theo dõi HCM" [SD#3480]
      idTemplateEmailStatisticDossierAgencyKggV2: '', // IGATESUPP-73840 [HCM iGATE V2] Sở Công Thương - Lỗi xuất file thống kê báo lỗi [SD3383]
      isShowPopupConfirmApprove: {
        enable: false,
        agencyIds: []
      }, //IGATESUPP-73671 [HCM iGATE V2] - Huyện Hóc Môn, Chi nhánh VPDK Thêm chức năng xác nhận trước khi thao tác
      mergeFormFile: false, // IGATESUPP-73033 [HCM iGATE V2] - Huyện Củ Chi - Hồ sơ cập nhật thành phần hồ sơ bị lỗi [SD]
      showConfirmFees: false, //IGATESUPP-72216 [HCM iGATE V2] Sở GTVT - Bổ sung chức năng cảnh báo khi chưa cập nhật lệ phí [SD3226]
      isShowOrganizationName :{ //IGATESUPP-68633 : [HCM iGATE V2] Sở QHKT - Thêm cột Tên tổ chức/ cá nhân (Tên tổ chức/cá nhân đề nghị) trên menu HS chờ tiếp nhận và menu Xử lý hồ sơ [SD2333]
        enable: false,
        agencyIds: []
      },
      autoUpdatePayment:{
        enable:false,
        agencyIds:[],
        idReceipt:[]
      },//IGATESUPP-71249 [HCM iGATE V2] Sở GTVT - Tự động cập nhật đã thanh toán khi phát hành biên lai điên tử [SD3225]
      sendCusPhoneNumber : false, // IGATESUPP-71082 gửi thông tin số điện thoại qua Biên lai điện tử khi dùng chức năng "Phát hành biên lai"
      showRequirePaperAndPayment: {
        enable: false,
        agencyId : []
      }, //IGATESUPP-70749 [HCM iGate V2] Sở Y Tế - Thêm chức năng "Yêu cầu nộp hồ sơ giấy và thanh toán"[SD2993]
      displayInternalProcedure: false, // IGATESUPP-70037 Ẩn/hiện 3 trường Công bố, quy trình nội bộ và DVCTT
      sendEmailReceipt : false, // IGATESUPP-69239-hoangpnn.hcm Quận 10 - Thêm chức năng gửi email khi dùng chức năng "Phát hành biên lai" [SD 2939]
      requireAttachmentWhenWithdraw: false, // nguyenttai.hcm IGATESUPP-68977 [HCM iGATE V2] Sở GDĐT - Sở GDĐT báo lỗi về chức năng rút hồ sơ phía người dân khi cán bộ đang thẩm định [SD2979]
      sortByReleaseDate:false, //IGATESUPP-69443 [HCM iGate V2] Sở Y Tế - Sắp xếp menu thống kê sổ HCC theo ngày ban hành và số [SD3019]
      waitingPayment: { //IGATESUPP-50812-lehoanganh.hcm: Màn hình chờ chuyển trang thanh toán khi thanh toán hồ sơ yêu cầu thanh toán
        isWaitingPayment : false,
        enableWaitingPaymentRequest: false,
        isWaitingPaymentRequired : false,
        listAgencyId: ["62970e84011f773c23acd266"]
      },
      isEnableNotePayment:false,
      listProcostIdLLTPStatistic : { // IGATESUPP-64632 thống kê lltp tiếp nhận trực tuyến
        listProcostId : []
      },
      reReceptionAccountDossier: false, // IGATESUPP-67891 - [HCM iGate V2] Sở Y Tế - Điều chỉnh luồng YCBS của SYT [SD2865]
      showRequireDirectPayment: { //IGATESUPP-71172 - Thêm tính năng cho Cán bộ yêu cầu thanh toán trực tuyến đối với hồ sơ nộp trực tiếp
        //Tham số enable = true: hiển thị nút Yêu cầu thanh toán hồ sơ đối với hồ sơ tiếp nhận trực tiếp; enable = false: không hiển thị nút Yêu cầu thanh toán hồ sơ đối với hồ sơ tiếp nhận trực tiếp
        //agency: Danh sách cơ quan áp dụng (cơ quan là đơn vị có loại đơn vị là cơ quan hành chính), nhiều cơ quan cách nhau bởi dấu phẩy
        //tagHinhThucThanhToanPayment: id tag hình thức thanh toán trực tuyến payment mặc định là 62f5d79b5c424b277f174318
        enable: false,
        agency: [],
        tagHinhThucThanhToanPayment: '62f5d79b5c424b277f174318'
      },
      receiptFeeGreaterThan0: { //IGATESUPP-70765 - true: Ẩn checkbox để không hiển thị phí 0đ trong popup phát hành biên lai điện tử, false: hiển thị như bình thường, listAgency là id cơ quan
        enable: false,
        listAgency: []
      },
      listProcedure: [
        "1.009936.000.00.00.H29",
        "2.100015.000.00.00.H29",
        "1.009991.000.00.00.H29",
        "1.010977.000.00.00.H29",
        "1.010978.000.00.00.H29",
        "2.100011.000.00.00.H29",
        "2.100008.000.00.00.H29",
        "2.100009.000.00.00.H29",
        "2.100010.000.00.00.H29",
        "2.100014.000.00.00.H29",
        "2.100012.000.00.00.H29",
        "2.100013.000.00.00.H29"
      ], //IGATESUPP-58538-[HCM iGATE V2] Sở Xây Dựng - Thêm Báo cáo thời gian xử lý toàn quy trình [SD1810]
      isShowInternalForm : false, // IGATESUPP-67591 [HCM iGATE V2] Sở Xây Dựng - Yêu cầu bổ sung Eform dành riêng cho cán bộ [SD2070] - biến bật/tắt Eform bổ sung thông tin nội bộ cho toàn site
      isShowConstructionUnit :false, //IGATESUPP-52715 : [HCM iGATE V2] Sở GTVT - Thêm trích yếu ở mục hồ sơ chờ tiếp nhận [SD1713]
      disableReceivingBtn: {
        enable : false,
        agencyIds: []
      },//IGATESUPP-65448 [HCM iGATE V2] Ban Quản lý ATTP_Thêm chức năng khoá nút "Tiếp nhận" khi có " Trạng thái thanh toán" là "Chưa thanh toán" [SD2553]
      stepStatusOfProcessingProfessionalDossiers: {
        applicableAgency: [],
      },//IGATESUPP-66723 [HCM iGATE V2] BQLKCXKCN - Bổ sung chức năng ẩn nút chuyển xử lý khi chuyển chuyên ngành [SD1935]
      allowShowProcedureAndSubmitter : false, // IGATESUPP-65111 - [HCM iGATE V2] Quận Tân Bình - Thêm cột Thủ tục và Người nộp trên menu Thống kê hồ sơ theo hình thức nhận kết quả [SD2263]
      sendNotifyNearExpiry: { // datht.lan-IGATESUPP-51143 - Sở Xây Dựng - Bổ sung tính năng sms/email thông báo cho cán bộ khi hồ sơ sắp tới hạn xử lý [SD1538]
        listAgency: [],
        templateIdSms: "",
        templateIdEmail: "",
        subjectEmail: "",
        hasSms: false,
        hasEmail: false,
        minutesBefore: 30
      },
      allowGetFullProcedureList: false, // IGATESUPP-63907 [HCM iGATE V2] Sở GDĐT - Sắp xếp lại tên các thủ tục ở các mục lọc trong các thống kê báo cáo [SD2458]
      showDistricAndVillageDVCLT: 1, //[HCM - iGATE v2] Sở Tư Pháp - Hiệu chỉnh menu thống kê Danh sách hồ sơ liên thông DVCLT
      exportExcelDVCLT: 1, //[HCM - iGATE v2] Sở Tư Pháp - Hiệu chỉnh menu thống kê Danh sách hồ sơ liên thông DVCLT
      listProcedureRequestCertificationOrganization:[],
       //IGATESUPP-63410 [HCM iGate V2] Sở Xây dựng - Cập nhật thêm DS mã TTHC mới vào menu thống kê dữ liệu tổ chức/cá nhân [SD 2385]
      listProcedureRequestCertificationPersonal:[],
       //IGATESUPP-63410 [HCM iGate V2] Sở Xây dựng - Cập nhật thêm DS mã TTHC mới vào menu thống kê dữ liệu tổ chức/cá nhân [SD 2385]
      downloadFileDossier:false, //IGATESUPP-65584 [HCM iGate V2] [Sở Y Tế] Thêm chức năng có thể tùy chọn để tải nhiều thành phần giấy tờ hồ sơ xuống tại Tab TPHS [SD2381]
      showFilterDossierExtendTime:false, //IGATESUPP-64078 [HCM iGATE V2] Sở Nội vụ-Thêm Filter hồ sơ gia hạn [SD2347]
      autoFillVietInfo:{
        id:"",
        enable:false
      }, //IGATESUPP-64180 [HCM iGATE V2] Sở GTVT - Cập nhật tự động thông tin có sẵn vào các mục khi phát hành biên lai [SD2373]
      customMenuTaskRemindDossierSearch: {
        enable: false,
        agencyIds :[]
      },// IGATESUPP-64947 [HCM iGATE V2] Sở Nội vụ-Hiện hồ sơ sai nội dung trong danh sách công việc [SD2516]
      durationByWorkingTime : { // IGATESUPP-63061 : syt tính giờ theo lịch làm việc
        turnOn : false,
        agencyList : []
      },
      filterSectorByAgencyStatisticProcedureHCMV2: true, // IGATESUPP-57961 [HCM iGATE V2] Offsite - Nhờ kiểm tra Số liệu hs tiếp nhận khác nhau ở các Menu
      statisticProcedureHCMSectorOnlyAgencyIdV2: true, // IGATESUPP-57961 [HCM iGATE V2] Offsite - Nhờ kiểm tra Số liệu hs tiếp nhận khác nhau ở các Menu
      autoFillApplyMethod:{
        enable:false,
        apiFill:'hinhThucNop',
        direct:1,
        online:2,
      },
      //IGATESUPP-64263 [HCM - iGATE v2] Sở Tư Pháp - Autofill dữ liệu hình thức nộp vào trường dữ liệu trên eform [SD2479]

      autoFillCivil: { //phquan.hcm - IGATESUPP-68282 [HCM - iGATE v2] Offsite - Autofill giá trị NULL trên eform hộ tịch[SD2912]
        enable: false,
        apiFill: [
          'so', 'quyenSo', 'trangSo', 'chucVuNguoiKy', 'nguoiKy', 'meHoTen', 'meDanToc',
          'meQuocTich', 'chaHoTen', 'chaDanToc', 'chaQuocTich', 'nguoiThucHien', 'ghiChu',
          'nycHoTen', 'nycQuanHe', 'nktDanToc', 'nktNoiChet', 'chaDanToc', 'chaQuocTich',
          'khNoiDangKyKetHon', 'khSoDangKyKetHon', 'khNgayDangKyKetHon' // phquan.hcm - IGATESUPP-72519 [HCM - iGATE v2] Offsite - Autofill giá trị NULL trên eform hộ tịch [SD3315]
        ]
      },
      // IGATESUPP-81109: [HCM iGate V2] Quận 6 - Hiển thị thông tin tài khoản nộp hồ sơ [SD3822]
      displayAccountInformation: {
        agencyIds: [],
        enable: false
      },

      showQuantityCopies: { // phquan.hcm - IGATESUPP-73662 - [HCM iGate V2] Offsite - Thêm cột số lượng bản cho các thủ tục Chứng thực [SD3349]
        enable: false,
        idChungThuc: '629a2ad2db4662230f940a0b'
      },

      showEReceiptIssuerAndDate: { //IGATESUPP-58926Thêm tên cán bộ xuất biên lai và ngày tháng xuất biên lai trên Thống kê phí biên lai điện tử
        agencyId: [],
        enable: false
      },
      allowShowCurrentProcessingAgency: false, // IGATESUPP-65107 - [HCM iGATE V2] Sở QHKT - Thêm cột Phòng ban xử lý trên Danh sách hồ sơ Menu Thống kê sổ theo thủ tục [SD2239]
      statisticBuildingMaterial: { //lehoanganh.hcm - IGATESUPP-58400 - [HCM iGATE V2] Sở Xây Dựng - Menu Thống kê "Dữ liệu công bố hợp quy VLXD" [SD1957]
        procedureCodeList : ['2.100020.000.00.00.H29', '2.100670.000.00.00.H29'],
        taskEvaluateId: 'UserTask_10yazyt',
        taskRejectId:'UserTask_0511jgz',
        taskAcceptId:'UserTask_1mrc6he'
      },
      addInvestoraddressColumn: { //IGATESUPP-60861 Quận 11 - Thêm địa chỉ Chủ đầu tư lên Danh sách hồ sơ Thống kê kết quả giải quyết TTHC theo cán bộ
        turnOn : false,
        agencyListId : []
      },
      groupTaskID: [
        // {
        //   id: "654212d2881c9f480ddb9d8e",
        //   name: "Thanh toán"
        // },
        // {
        //   id: "6542143e881c9f480ddb9d90",
        //   name: "Xử lý hồ sơ"
        // } //IGATESUPP-63320 Tách các nhóm công việc theo loại: liên quan hồ sơ
      ],
      changeCheckbox:{
        enableChangeCheckbox:false,
        agencyId:[],
        procedureCode:[]
      },  //IGATESUPP-53193 [HCM iGATE V2] - Ban quản lý Khu Công nghệ cao - Yêu cầu điều chỉnh Checkbox để in Phôi giấy phép Lao động [SD1833]
      enableButtonReturnResult: false, //IGATESUPP-56640 Đồng bộ kết quả qua LGSP (SD1962)
      requestTypeFile: {
        listAgencyConfigProcedure: [], // IGATESUPP-54759 thêm tính năng ràng buộc người dân nộp file doc,docx,pdf
        enableConfigProcedure:false, // IGATESUPP-54759 thêm tính năng ràng buộc người dân nộp file doc,docx,pdf
      },
      sortDossiersByDueDateTask: { //IGATESUPP-56465 lehoanganh.hcm: sắp xếp hồ sơ sở lý theo hạn bước
        enableSort: false,
        agencyIds: ["62970e84011f773c23acd266"]
      },
      findResPerson: {
        agencyList: [],
        enable : false
      }, // IGATESUPP-59049 [HCM iGate V2] [Sở Y Tế] Tạo mục tìm kiếm "Người chịu trách nhiệm chuyên môn" cho đơn vị [SD2180]
      enableMenuRequestCertification:false, // IGATESUPP-52526 : Thêm Menu "DỮ LIỆU TỔ CHỨC/CÁ NHÂN ĐỀ NGHỊ CẤP CHỨNG CHỈ" [SD1735]
      hideInforAdditionalRequirementDateDossierList : ["60ebf0db09cbf91d41f87f8c"], //IGATESUPP-61110 [HCM iGate V2] [Sở Y Tế] Ẩn hạn hẹn trả, toàn trình khi hồ sơ bổ sung cho SYT
      hideInforAdditionalRequirementDate : false, //IGATESUPP-61110 [HCM iGate V2] [Sở Y Tế] Ẩn hạn hẹn trả, toàn trình khi hồ sơ bổ sung cho SYT
      setReceivingDateBold: false, // IGATESUPP-59040 [HCM iGate V2] [Sở Y Tế] Tô đâm ngày hẹn trả
      giaodientiepnhanxuly: false, // IGATESUPP-56821 - OFFSITE_ Điều chỉnh ui-ux trên một cửa
      enableResidentialInfoScanCitizen: false , //IGATESUPP-54790-lehoanganh.hcm: bật/tắt chức năng máy quét căn cước công dân trong tra cứu thông tin dân cư
      listAgencyShowDueStep: [], // IGATESUPP-53824 [HCM iGATE V2] Sở Xây Dựng - Hiện thời gian hết hạn xử lý của Bước hiện tại trên menu Xử lý hồ sơ [SD1798]
      getBillHCM: false, // IGATESUPP-56801 [HCM iGATE V2] Ban Quản lý ATTP_Lỗi biên lai xuất sai thông tin
      isShowSearchTaxCode: false, //IGATESUPP-57334 -Bổ sung tính năng tìm kiếm mã số doanh nghiệp, mã số thuế
      mustSignTypeOrgAgency:[""], //IGATESUPP-52173 - Huyện Bình Chánh - Lỗi không đóng dấu của tổ chức được khi cấu hình chọn loại chữ ký: Mẫu chữ ký tổ chức [SD1403]
      skipMustSignTypeOrg: false, //IGATESUPP-52173 - Huyện Bình Chánh - Lỗi không đóng dấu của tổ chức được khi cấu hình chọn loại chữ ký: Mẫu chữ ký tổ chức [SD1403]
      roleAllowSignTypeOrg: "", //IGATESUPP-52173 - Huyện Bình Chánh - Lỗi không đóng dấu của tổ chức được khi cấu hình chọn loại chữ ký: Mẫu chữ ký tổ chức [SD1403]
      mustSignTypeOrgconfig: false, //IGATESUPP-52173 - Huyện Bình Chánh - Lỗi không đóng dấu của tổ chức được khi cấu hình chọn loại chữ ký: Mẫu chữ ký tổ chức [SD1403]
      enableDisplayOfSpecializedProcessing: false, //IGATESUPP-53837 - Bổ sung màn hình hiển thị trạng thái chuyên ngành trên màn hình một cửa của cán bộ(SD1886)
      listAgencyStopProcessing:[],//IGATESUPP-50532 - Sở Lao Động - Yêu cầu Hồ sơ ở trạng thái Dừng xử lý hiển thị ở Menu Hồ sơ không cần xử lý
      isEnableLTTPStatusSearch: false,//IGATESUPP-46218 OS HCM Sở Tư Pháp - Bổ sung thêm filter trong mục Tìm kiếm
      dossierArrSortType: 4,
      enableReloadDossier : 1, //IGATESUPP-50790 - Sở Tư Pháp - Lỗi hiển thị menu Công việc sau khi chuyển nhiều hồ sơ cùng lúc
      isEnableYT: false, //IGATESUPP-48500 [HCM iGATE V2][Sở Y Tế] Bổ sung thêm chức năng cấp số chứng chỉ ngày cấp trên nút Cấp số
      showNote : {
        enable : false,
        allowAgency : ["61075a146e8bb4462db32064", "629567b2011f773c23acd1a1"]
      },
      turnOffPermissionProcessPerson :{
        enable: true,                                          //enable = true --> thì như cũ, false là ẩn đi
        listIdDonVi:["6433bf7af816412ba96a37f4"],						    //phucnh.it2-IGATESUPP-53560-Đơn vị có trong list sẽ áp dụng bỏ quyền của người xử lý//id so tu pháp: 6433bf7af816412ba96a37f4
        keyPermission: ["oneGateReceiptCreation"]
      },
      syncCheckPaymentStatusMenu:{                            //phucnh.it2-IGATESUPP-70766: đơn vị có trong list sẽ xem dc form đồng bộ trạng thái
        enable: false,                                    //enable = false: tắt chức năng ko dùng
        allowAgency:["6296de53011f773c23acd1b8"],
        idPhiSoLuongBan:["6402ea56a9a2347b01f87b06"],
        idLePhi:["6297297bdb4662230f9401d1"]
      },
      enableChangeDescriptionContentVnpost: false,            //phucnh.it2-IGATESUPP-55384: true: cho phép thêm thông tin hình thức thu gom vào trong thông tin mô tả gói tin gởi VNPOST
      turnOnElementSearchOnlineReception: true, 	//phucnh.it2-IGATESUPP-69297: Cho phép bật tắt cum "Tìm thấy ... kết quả tìm kiếm" ở trang hs chờ tiếp nhận
      listAgencyShowFilterSort: [//IGATESUPP-51996- Sở Tư Pháp - Bổ sung filter SẮP XẾP vào menu Tra cứu hồ sơ toàn cơ quan [SD1745]
        '000000000191c4e1bd300029'
      ],
      enableChangeSenderDescContentVnpost: false,               //phucnh.it2-IGATESUPP-55384: true: cho phép thay đổi nội dung trường SenderDesc thành Mã hồ sơ + tên thủ tục/ false: như cũ
      maTTBoTNMT: false, //IGATESUPP-48247-nanta hiển thị mã bộ TNMT
      showConstructionPermit: false, // IGATESUPP-45420: Sở GTVT - Bổ sung tên công trình đề nghị cấp phép
      enableMenuMinistryOfTransport: false, //quannna-IGATESUPP-45393 Hiển thị menu Quản lý hồ sơ Bộ giao thông vận tải
      investorEnable : false, // IGATESUPP-47454  Thêm tên chủ đầu tư trên menu Hồ sơ chờ tiếp nhận, menu Xử lý hồ sơ
      allowCreateReceiptWithAnyStatus:false, //IGATESUPP-46562-nvloi BAN QUẢN LÝ ATTP_Điều chỉnh lại chức năng phát hành biên lai [SD1214]
      createReceiptWithAnyStatusAgencyWhitelist:[],  //IGATESUPP-46562-nvloi BAN QUẢN LÝ ATTP_Điều chỉnh lại chức năng phát hành biên lai [SD1214]
      enableReasonDossierCbx : false, //nghiaht-IGATESUPP-43929 - OFFSITE_Bổ sung chức năng ghi log thống kê, báo cáo lại những hồ sơ bị xóa, khi sử dụng chức năng xóa hồ sơ
      listAgencyIdShowButtonGetFilesFromResult : [], //datht.lan_IGATESUPP-44369 Danh sách Đơn vị Hiên thị button "Lấy file từ Kết quả xử lý"
      listAgencyIdShowOverTime:{
        changeColor: false,
        listAgencyId:[],
        excludeNotTimeInLast: false
      }, //datht.lan_IGATESUPP-43367: danh sách đơn vị sẽ đổi màu khi bị trễ hạn bước
      listProcedureCodeShowTooltip: [], //datht.lan_IGATESUPP-44506: danh sách ProcedureCode hiển thị tooltip "địa chỉ" tại "Mã hồ sơ"
      isShowExcelDossierFeeHCM : false, // hoannk-IGATESUPP-46880 - Tính năng xuất Excel trên Menu Thống kê lệ phí hồ sơ HCM ( True Button Mở,False Button Đóng)
      showApplicantOrganization: {
        enable: false,
        allowedAgency: []
      }, 	//nghiaht-IGATESUPP-43273:Sở TNTM - Yêu cầu thêm cột Tên Công ty trong mục yêu cầu bổ sung
      agencyCheckPaymentBeforeEndDossier: [],    //IGATESUPP-42786-yenlinh: danh sách ID cơ quan muốn check thanh toán trước khi kết thúc hồ sơ
      paymentFormMethodCategoryId:'64103ac25706bf6444283fd1', //quannna-IGATESUPP-40736:Lưu thông tin id loại nhãn Hình thức thanh toán biên lai
      allowDisplayOfReceiptPaymentForm: false, //quannna-IGATESUPP-40736: Hiển thị hình thức thanh toán biên lai
      enableAddCompanyNameToDetailStatistic012020: false, 	//phucnh.it2-IGATESUPP-40697:Biến cấu hình bật /tắt thêm cột tên công ty vào chi tiết đo đạc và bản đồ báo cáo 01/2020
      allowAddAgencyColumnTo01Statistic:false,          //phucnh.it2-IGATESUPP-50732: cho phép thêm(true)/ẩn(false) cơ quan vào bảng chi tiết danh sách hồ sơ ở thống kê 01
      changeMenuBanATTP:{
        enableChangeNameAttp201: false,
        enableChangeNameAttp202: false,
        enableChangeNameAttp203: false,
        attp201: "BÁO CÁO KIỂM DỊCH SẢN PHẨM ĐỘNG VẬT RA KHỎI ĐỊA BÀN THÀNH PHỐ HỒ CHÍ MINH",
        attp201e:"CHECKLIST REPORT OF ANIMAL PRODUCTS FROM HO CHI MINH CITY",
        attp202: "BÁO CÁO CHI TIẾT CẤP GIẤY CHỨNG NHÂN KIỂM DỊCH TẠI CÁC ĐỘI",
        attp202e:"DETAILS REPORT ISSUANCE OF VERIFICATION CERTIFICATES IN TEAMS",
        attp203: "BÁO CÁO TỔNG HỢP CẤP GIẤY KIỂM DỊCH CHO CỞ SỞ",
        attp203e: "SUMMARY REPORT ISSUANCE OF CHECKLIST PAPER FOR FACILITIES"
      },
      eReceipt: {
        supplierDefaultLabelId: "64633969f84f141d5b148140", //nghiadd.bdg-IGATESUPP-46626: Id biên lại mặc định
        vietInfo:{
          enableShowReceiptCode: false,                   //nghiadd.bdg-IGATESUPP-46626: Bật/tắt hiển thị mã biên lai liên thông của VietInfo
          enablePropertiesDefaultTest: false,             //nghiadd.bdg-IGATESUPP-46626: Bật/tắt hiển thị các thuộc tính test biên lai
          supplierId: "64633969f84f141d5b148140",         //nghiadd.bdg-IGATESUPP-46626: Id biên lai VietInfo
          directPaymentMethodIdList: ["5f7fca83b80e603d5300dcf4"] //nghiadd.bdg-IGATESUPP-46626: List các phương thức thanh toán trực tiếp
        },
        addNationInfoToAddress: false,	               //phucnh.it2-IGATESUPP-44484: Bật/tắt chức năng thêm thông tin quốc gia vào dia chi khi phát hành biên lai điện tử
      },
      enableAddCriteriaDossierReceivingKindToSearch:false,  //phucnh.it2-IGATESUPP-45093: Bật/tắt chức năng: Thêm tiêu chí tìm kiếm là  hình thức nhận hs
      enableMenuStatisticsReceivingSiteOneGate:false,	//IGATESUPP-41788: phucnh.it2: Thêm menu thống kê theo hình thức nhận kết quả vào site Onegate
      enableMenuReceiptFeeStatistics: false,			  //phucnh.it2-IGATESUPP-44995: Bật/ tắt menu thống kê phí biên lai HCM
      enableMenuDossierStatisticsSYT: false,//phucnh.it2-IGATESUPP-44995: Bật/ tắt menu thống kê phí biên lai HCM
      enableMenuDossierStatisticsByAppointmentDateHCM: false, //phucnh.it2-IGATESUPP-59045-ThongKeHsSYT Bật/ tắt menu thống kê hồ sơ theo ngày hẹn trả
      enableVnpostLogFunction: false,       ////phucnh.it2-IGATESUPP-64084: Cho phép bật chức năng tra cứu log vnpost
      enableAddReceiptNumberToEformAllUnitScreen: false,	  //phucnh.it2-IGATESUPP-45629: Bật/tắt chức năng add số biên lai vào eForm tại màn hình chi tiết của hs tra cứu toàn cơ quan
      enableAddReceiptNumberToEform: false,                //phucnh.it2-IGATESUPP-43962: Bật/tắt chức năng add số biên lai vào eForm
      typeLoaiLePhi: "6297297bdb4662230f9401d1",           //phucnh.it2-IGATESUPP-43962: Cấu hình loại lệ phí
      enableStatisticDossierAdditionMenu:false,             //phucnh.it2-IGATESUPP-67352-Menu Thong ke ho so bo sung
      costValue2: "200000",
      costValue1: "100000",
      enableNewVnpostStatus:false,                         //phucnh.it2-IGATESUPP-40295: Bật/ tắt hiển thị trang thái mới nhất hs vnpost
      showCheckboxNotDeleteFile: false, //quannna-IGATESUPP-40704: Bật tắt chức năng Khóa thành phần hồ sơ khi yêu cầu bổ sung
      enableMappingNumberCopiesFees: false, 	          //phucnh.it2-IGATESUPP-38054: Bật/tắt chức năng mapping thông tin số lượng bản phi
      typeFee: ["6402ea56a9a2347b01f87b06"],               //phucnh.it2-IGATESUPP-38054: Biến cấu hình loại phí bản sao cấp thêm
      enableChangeFkeyToReceipt: false, ////phucnh.it2-IGATESUPP-39993: bật/tắt cấu hình, lấy thông tin [Mã định danh cơ quan].[Số hồ sơ].[Mã tự tăng] gắn vào Fkey chức năng phát hành biên lai
      enableAddPayStatusColumn:{                  //phucnh.it2-IGATESUPP-39497: Thêm cột trang thái thanh toán vào thống kê
        onlineReceptionScreen:false,		            //Thêm vào màn hình Hồ sơ chờ tiếp nhận
      },
      showUpdateDateOneGate:false,             ////phucnh.it2-IGATESUPP-51370: cho phép hiển thị ngày update hs của công dan ở menu chờ tiếp nhân site 1 cửa
      showNewColumn63039:false,                 //phucnh.it2-IGATESUPP-63039: cho phép hiển thị thêm 3 cột Tên cơ quan, mã số và dia chỉ doanh nghiệp
      idDossierTaskStatus: '60e409823dfc9609723e493c',
      enableDossierVnpostDetail: {
        enable: false,
        allowedAgency: [],
        textDossierVnpostDetailrcSendVi: " Đăng ký nộp hồ sơ tại nhà",
        textDossierVnpostDetailrcSendEn: " Register to submit your application at home",
        textDossierVnpostDetailrcReceiveVi: " Đăng ký trả kết quả tại nhà",
        textDossierVnpostDetailrcReceiveEn: " Register to pay results at home",
      },                                //phucnh.it2-IGATESUPP-47450: Cho phép hiển thị chi tiết nhận, trả kết quả của hình thức nhận kết quả qua vnpost
      showDossierEndDateInImplementingAgency: false, // IGATESUPP-39991: Hiển thị thêm thời gian thực hiện của cán bộ đến bước hiện tại cột Cơ quan thực hiện của chức năng xem danh sách Xử lý hồ sơ
      enableInsertProcedureNameToReceipt:false,            //phucnh.it2-IGATESUPP-39700: bật/tắt cấu hình, lấy thông tin tên thủ tục gắn vào tên lệ phí chức năng phát hành biên lai
      enableCheckReceiptIssued: false,    //[HCM - iGATE v2] Sở Tư Pháp - Lỗi phát hành được nhiều biên lai cho 1 phí[SD3806]
      enableFixBugReceipt:false, //phucnh.it2-IGATESUPP-79914: Bat tắt cấu hình fixbug sai thông tin eReceipt
      calculateUnresolvedProcedureStatistics: false, // IGATESUPP-38277 - Điều chỉnh Thống kê thủ tục: Chưa giải quyết: (Tiếp nhận + tồn) - Giải quyết - Hủy
      calculateAppointmentDate: 0, //IGATESUPP-38062 - Yêu cầu điều chỉnh cách tính ngày hẹn trả khi tiếp tục xử lý HS đã thanh toán trực tuyến
      enableSelectColumnDisplayInReportBtnGeneral:false, //IGATESUPP-37207-phucnh.it2: Tham số bật/tắt chức năng chọn cột hiển thi trong báo cáo của excel, thống kê ở menu: Thống kê sổ theo dõi //menu chuc nang chung
      enableSelectColumnDisplayInReportBtn: false,      //IGATESUPP-37207-phucnh.it2:Tham số bật/tắt chức năng chọn cột hiển thi trong báo cáo của excel, thống kê ở menu: Thống kê sổ theo dõi HCM //menu rieng cho HCM
      enableAddOrganizationInformationReceipt: false,  //IGATESUPP-35906-phucnh.it2: Bật/tắt chức năng Thêm thông tin của doanh nghiệp vào biên lai, mặc định tắt chức năng
      tagStaffGuide: "638aeedfe664a91066d4d473",        //IGATESUPP-30852-phucnh.it2: Biến cấu hình tag của loại tài liệu hướng dẫn cán bộ
      "dossier": {
        "showStatusVnpost":"0"                          //IGATESUPP-27318-phucnh.it2: Biến bật hiển thị trang thái vnpost trên lưới các menu: Hồ sơ không cần xử lý/ Tra cứu hồ sơ theo đơn vi/ Tra cứu hs toàn cơ quan/ Tra cứu hs cá nhân
      },
      "showRemainTime": true,                            //IGATESUPP-27369-luanngm: Hiển thị thời gian còn lại của các bước xử lí qui trình
      "searchByAgencyInDossierTransferred" : false,      //IGATESUPP-27586-tuqn: Lọc theo đơn vị trong chức năng thống kê hồ sơ đã chuyển
      enableAutoFillReceivingKindBCCI: 0,                //IGATESUPP-28735-phucnh.it2: Bật chức năng autofill thông tin nhận kết quả tại nhà = thông tin chung đã điền
      typeThuHo: {                                        //IGATESUPP-30264-phucnh.it2: Biến cấu hình thu hộ phí tại nhà
        id: "62f714990e23bd31c47c5dfc",
        name: [
          {
            languageId: "228",
            name: "Phí thu hồ sơ tại nhà"
          },
          {
            languageId: "46",
            name: "Fee collection dossier at home"
          }
        ],
        quantityEditable: "0",
        type: "1"
      },
      typeTraKetQua: {
        id: "62f715600e23bd31c47c5dfd",
        name: [
          {
            languageId: "228",
            name: "Phí trả kết quả hồ sơ tại nhà"
          },
          {
            languageId: "46",
            name: "Fee send result of dossier comback at home"
          }
        ],
        quantityEditable: "0",
        type: "1"
      },
      enableSaveFullAddress: 0,                       // IGATESUPP-29115-phucnh.it2: 0 = off; 1 = bật lưu thêm dia chi trả kết quả tại nhà
      enableMenuGuideSiteOneGate: 0,                        // IGATESUPP-30852: phucnh.it2: 0 = off; 1 = on, default = 0: Bật tắt menu hướng dẫn sử dụng site một cửa
      enableContentValidInfo : 0,         //IGATESUPP-32441-phucnh.it2: Biến ẩn hiện thêm thông tin valid thông tin ký số trong file upload trong chức năng xem lich sử lý số
      showCheckboxNotPublicProcedure: false,                 // IGATESUPP-28819: Điều chỉnh chức năng hiển thị danh sách DVCTT và TTHC
      openDocumentFormName: {     //IGATESUPP-29223-thuongld: Thay đổi tên nút Click chuột vào đây để mở Biểu mẫu giấy tờ
        vi: "(Click chuột vào đây để mở Biểu mẫu giấy tờ)",
        en: "(Click here to open the Document Form)"
      },
      procedureCodeHCMBQLATTP201 : "1.002338.000.00.00.H29", // IGATESUPP-30799 Khai báo mã thủ tục cho Báo cáo chuyên môn Ban QLATTP 2 - 01
      showAmountDossierFeeAndFullName: false, //IGATESUPP-30655 bật true hiển thị cột người nộp là fullname , lệ phí là amountDossierFee
      enableRemoveVnpostFeeToAnotherTable: 0, //IGATESUPP-31649-thuongld: Tham số tách phí VNPost và phí, lệ phí thành 2 bảng
      showMaxFileSize: false, // IGATESUPP-34216-phuongtnd : Tham số bật tắt hiển thị dung lượng tối đa upload lên hệ thống
      showAddNewFileBtn: false, //IGATESUPP-32710-thuongld:[HCM-Igate V2] THÊM CHỖ ĐỂ ĐÍNH KÈM HỒ SƠ BỔ SUNG
      ckeditorMaxLength: 0, // IGATESUPP-31695 Yêu cầu mở rộng thêm ký tự nhập vào các trường lý do.
      showChangeDossierFeeBtn: false, //IGATESUPP-32217-thuongld-[HCM-Igate V2] Thêm tính năng điều chỉnh lệ phí ở tài khoản cán bộ
      showPaymentMethod: false,   // IGATESUPP-32437 Tham số để hiển thị hình thức thanh toán
      showAppliedDateFilter: true, // IGATESUPP-34076-phuongtnd Tham số bật tắt bộ lọc ngày nộp trong mục "Hồ sơ chờ tiếp nhận"
      paymentPlatformButtonName: { //IGATESUPP-33598-thuongld-[iGate HCM] Đổi tên nút hiển thị xem danh sách biên lai
        vi: "Thông tin biên lai thanh toán",
        en: "Payment receipt information"
      },
      isDisableAgencyChoosen: [], //IGATESUPP-28946-phuongtnd : Tham số disable chọn đơn vị và chức vụ khi chuyển bước
      loadDataFromConfig: false, //IGATESUPP-58531 Chức năng "Chuyển xử lý" load thông tin mặc định từ config
      usedViewPDFFile: false, //IGATESUPP-33436-thuongld-[HCM iGate V2] Chức năng in và tài bản xem trước không hoạt động
      enableExportPageDossiersToExcel: false, // IGATESUPP-33975-trankequang: ẩn/hiện nút xuất excel hồ sơ trên trang ở trang xử lý hồ sơ
      isOpenInNewTabHCM: false, // IGATESUPP-34052 Tham số bật Tab mới khi click chuột phải và ctrl+chuột trái
      showToolTipsHCM: false,  // show tooltip tên thủ tục
      showDirectlyPaymentNotification: false, //IGATESUPP-34228-phuongtnd : Tham số bật tắt thông báo thanh toán trực tiếp
      listProcedureStatisticRegistrationReader: [], //IGATESUPP-35064-thuongld-[HCM-Igate V2] Trung tâm lưu trữ lịch sử_SNV_Thiết kế báo cáo thống kê Sổ đăng ký độc giả
      procedureCodeHCMBQLATTPConfirmAdvertisement : "2.100148.000.00.00.H29", // IGATESUPP-32758-trankequang: Khai báo mã thủ tục cho Báo cái xác nhận quảng cáo Ban QLATTP
      showStatusSyncDossierDVCQG: false, // IGATESUPP-32608 Bổ sung chức năng Đồng bộ hồ sơ Cổng DVCQG - Hiển thị trạng khi xem chi tiết hồ sơ
      enableDossierDueDateCheckBox: 0, //IGATESUPP-34908 : Tham số ẩn hiện check box hồ sơ tới hạn xử lý
      acceptedTimeGapBetweenCurrentDateAndDueDate: 4, //IGATESUPP-34908 : khoảng cách giờ giữa thời điểm hiện tại và due date để xác định hồ sơ tới hạn
      enableExportDossiersToExcel: false, //IGATESUPP-33409-trankequang: bật true hiển thị nút xuất excel ở mục xử lý hồ sơ, false thì ẩn.
      procedureCodeSLDTBXHWorkPermit: { //IGATESUPP-33409-trankequang: mã thủ tục xử lý giấy phép lao động cho người nước ngoài
        procedureCodeLicensingWorkPermit: "2.000205.000.00.00.H29", //IGATESUPP-33409-trankequang: mã thủ tục cấp giấy phép lao động cho người lao động nước ngoài
        procedureCodeRenewalWorkPermit: "2.000192.000.00.00.H29", //IGATESUPP-33409-trankequang: mã thủ tục cấp lại giấy phép lao động cho người lao động nước ngoài
        procedureCodeExtendWorkPermit: "1.009811.000.00.00.H29", //IGATESUPP-33409-trankequang: mã thủ tục gia hạn giấy phép lao động cho người lao động nước ngoài
      },
      showAgencyEnterprise: 0, // IGATESUPP-34975 : tham số bật tắt ô tìm kiếm theo tên cơ quan doanh nghiệp
      enableShowAdditionalRequirements: 0, // IGATESUPP-35546: Hiển thị nút cho phép yêu cầu bổ sung hồ sơ nhiều lần. Giá trị: 0: Không hiện nút, 1: Hiển thị nút
      hideAttachFilePreviewInGetComment: false,    //IGATESUPP-35674-yenlinh: Bật/Tắt gọi lại lấy danh sách ý kiến xử lý trong hàm lấy danh sách comment
      checkRecallBillFile: false, //IGATESUPP-35623-thuongld-[HCM iGATE V2]-Ưu tiên-Lỗi Không xem được Biên lai thanh toán trực tuyến
      showSelectDossierStatus: 0, //(0: Không hiện select, 1: Hiển thị select)
      showCheckboxIsEssentialPublicService: false, // IGATESUPP-36017-trankequang: Hiển thị checkbox dịch vụ công thiết yếu trên trang một cửa cập nhật dịch vụ công
      enableLogbookProcedureFilter: 0, // IGATESUPP-34778 : Bổ sung tham số ẩn hiện procedure filter trên trang thống kê sổ theo dõi
      showCheckboxLockConfigProcedure: false, // IGATESUPP-34973: Thêm chức năng và phân quyền TK cho phép khóa không cho tác động điều chỉnh TTHC
      enableRegistrationDossier:false,//IGATESUPP-34081 Hiển thị hồ sơ mới đăng ký
      isSortDueDateHcm: 0, //IGATESUPP-37298 bổ sung tham số sort theo duedate
      enableDownloadManyDossier: 0, // IGATESUPP-36322 : bố dung tính năng tải nhiều hồ sơ (có giá trị 0: Ẩn nút; 1: Hiển thị nút. Mặc định = 0).
      procedureCodeHCMBQLATTP203 : "1.002338.000.00.00.H29", // IGATESUPP-36793 Khai báo mã thủ tục cho Báo cáo chuyên môn Ban QLATTP 2 - 03
      showCitizenWithdrawComment: false, //IGATESUPP-36256-trankequang : hiển thị lý do rút hồ sơ của người dân
      reReceptionAdditionalDossier: {
        enable: false,
        dossierTaskStatus: ['60ebf0db09cbf91d41f87f8c']
      }, // IGATESUPP-37527: Tiếp nhận hồ sơ yêu cầu bổ sung tiếp tục với thời gian còn lại của quy trình
      showCheckboxCitizenPayment: false,  // IGATESUPP-43521-yenlinh: Bắt buộc công dân thanh toán
      showCheckConfirmPayment: false, //IGATESUPP-37901-thuongld: [HCM iGATE V2] Ban Quản lý ATTP - Thêm tính năng xác nhận số tiền phí trước khi gửi Yêu cầu thanh toán hồ sơ
      showAdditionalRequirementPaper: false, //IGATESUPP-38066-thuongld: [HCM iGATE V2] Sở Y Tế - Yêu cầu thêm tính năng Yêu cầu gửi hồ sơ bản giấy
      addMenuHCM:{
        vi:"Thống kê hồ sơ theo cơ quan",
        en:"Report by agency"
      },                    // IGATESUPP-33981 : Tham số bật tắt thêm menu thống kê hồ sơ toàn cơ quan
      hideReportTitle:false, // IGATESUPP-33981 : Tham số bật tắt tiêu đề báo cáo
      showSortComboBox: 0, // IGATESUPP-37926 - trankequang : hiển thị combobox chọn kiểu sắp xếp ở trang hồ sơ chờ tiếp nhận
      cancelDossierAgencyHCM: false, //IGATESUPP-38342-thuongld: [HCM iGATE V2] Sở 4T - Yêu cầu điểu chỉnh Menu 'Hồ sơ không cần xử lý'
      isNguoiNopSYT: false,//IGATESUPP-64362-dangquang [HCM iGate V2] [Sở Y Tế] Bổ sung thông tin vô excel khi xuất thống kê
	    // procedureCodeHCMBQLATTP203 : "1.002338.000.00.00.H29", // IGATESUPP-36793 Khai báo mã thủ tục cho Báo cáo chuyên môn Ban QLATTP 2 - 03
      allowResetSendSubjectNoti: false, //IGATESUPP-38373-thuongld: [HCM iGATE V2] Sở lao động thương binh xã hội - Lỗi sms thông báo từ chối hồ sơ
      enableComboboxPublicAdministration: 0, //1 à bật combobox sổ thủ tục, 0 là tắt combobox số thủ tục
      enableButtonTakeNumber:0,  // 1 là hiện nút cấp mã số, 0 là ẩn nút hiện mã số
      enableUpdateButtonTakeNumber: 0, //IGATESUPP-43478 1 là cho phép hiển nút cấp mã số để update, 0 là ẩn nút cấp mã số
      enableAllButtonTakeNumberWithAgency: [],
      hideNumberPauseDaySuspend: false, //IGATESUPP-46221 Sở Tư Pháp - Điều chỉnh chức năng Tạm dừng hồ sơ
      hideNumberPauseDaySuspendWithAgency: [],  //IGATESUPP-46221 Sở Tư Pháp - Điều chỉnh chức năng Tạm dừng hồ sơ
      defaultNumberPauseDay: 100,  //IGATESUPP-46221 Sở Tư Pháp - Điều chỉnh chức năng Tạm dừng hồ sơ
      receivingKinds: [
        {id: "5f8968888fffa53e4c073ded", name: "Nhận trực tiếp"},
        {id: "5f8969018fffa53e4c073dee", name: "Nhận qua bưu điện"}
      ],
      showPaymentInProcedure: false, //IGATESUPP-38525-thuongld: [HCM iGATE V2] [Gấp] Sở Tư Pháp - Yêu cầu không mở hình thức thanh toán trực tuyến đối với TTHC Mức 3
      isShowTakeNumber: false,
      allowAgencyApprovalLevel3: false, //IGATESUPP-38746-thuongld: [HCM iGATE V2] offsite_Yêu cầu bổ sung đơn vị thụ hưởng trong thủ tục có 2 cấp
      filterRelatedAgencies: false,// IGATESUPP-37486 Điều chỉnh tính năng lọc Cơ quan thực hiện trong Danh mục Thủ tục
      logBookNumberCopies: {
        enable: false,
        listProcedureCode: [],
      }, //IGATESUPP-37334-thuongld-[HCM iGATE V2] Sở GDĐT - Yêu cầu thêm tính năng trong Thống kê sổ theo dõi
      directlyPayment: {  //IGATESUPP-39894 - trankequang: Yêu cầu bổ sung tính năng Cho hồ sơ thanh toán trực tiếp
        sentDirectlyPaymentNotificationTaskStatus: "6412aa8ef4a42852018da4fa", // dossier task status
        sentDirectlyPaymentNotificationTagId: "63fb7a261aee0a3ee93c8dac", // dossier menu remind Id
        allowedAdministrativeAgency: [],
        showDirectPaymentRequestBtn: 0,
        showDirectlyPaymentOverdueRemind: false,
      },
      showInputMoneyReceipt: 0, // IGATESUPP-38520 Sở Tư Pháp - Thêm biến "Số biên lai" cho Phiếu động
      dossierFeeOnlyAgencyIdHCM: false, //IGATESUPP-38888-trankequang: [HCM iGATE V2] Sở 4T - Yêu cầu điều chỉnh Menu Thống kê lệ phí hồ sơ HCM
      showCitizenUpdatedDateInHistory: false, //trankequang-IGATESUPP-39106 - [HCM iGATE V2] [Gấp] BQLATTP - Yêu cầu hiển thị thời gian người dân cập nhật bổ sung trong Lịch sử
      filterSectorByAgencyStatisticProcedureHCM: true, //IGATESUPP-38321: [HCM iGATE V2] Sở 4T - Yêu cầu điều chỉnh Menu Thống kê sổ theo thủ tục
      statisticProcedureHCMSectorOnlyAgencyId: true, //trankequang-IGATESUPP-38321 - [HCM iGATE V2] Sở 4T - Yêu cầu điều chỉnh Menu Thống kê sổ theo thủ tục
      procedureCodeHCMBCGCNATTP : "1.002425.000.00.00.H29",  //  IGATESUPP-32785 Khai báo mã thủ tục cho Báo cáo cấp giấy chứng nhận QLATTP
      enableShowComboBoxSortByAppliedDate: 1, // IGATESUPP-39745 - hohuyluat : [HCM iGATE V2] Sở Xây Dựng - Thêm tính năng Sắp xếp hiển thị hồ sơ trong Thống kê sổ theo dõi
      numberReceiptAutoIncrease: '63f3363b1aee0a3ee93c8daa' ,// IGATESUPP-40044 - hohuyluat id Nhãn Số biên lai tự tăng
      idNumberReceiptAutoIncrease: '6406f7e69d61986f7565069a', //IGATESUPP-40044  id bộ số biên lai 200k mặc định
      enableShowPopUpReceiptNumber: true ,//IGATESUPP-40044  true hiển thị popup cấp số biên lai, true, tắt chức năng
      idNumberAutoBook: '63f336993137b97d1e1c3283', ////IGATESUPP-40044 id sổ thủ tục hành chính SOTUTANG
      isCheckID: {
        isShowCodeReceiptNumber: 0, //IGATESUPP-40044 1: hiển thị thông tin mã số biên lai được cấp, 0 : ẩn dòng thông tin mã số biên lai được cấp
        isCheckAccounting: "633ce88843beb047e1559703", ///IGATESUPP-51992 kiểm tra id kế toán
        isdossierTaskStatus: "6412aa8ef4a42852018da4fa", ///IGATESUPP-51992 id trạng thái hồ sơ "Đã thông báo thanh toán trực tiếp"
        agencyIdBQLATTP: "62970e84011f773c23acd266", ///IGATESUPP-51992 agency id Ban QLATTTP
      },
      showQuarantineAddress: {
        enable: false,
        listProcedureCode: [],
      }, //IGATESUPP-40128-thuongld-[HCM iGATE V2] Ban Quản lý ATTP - Hiển thị thêm địa chỉ kiểm dịch tại Hồ sơ chờ tiếp nhận [SD916]
      showImportDossierStatus: true, // quannna-IGATESUPP-39671: [IGATE-HCM] Bổ sung chức năng import excel và đồng bộ trạng thái hồ sơ cũ (hiển thị import trạng thái hồ sơ)
      showStatisticsPaymentDossierFeeHcm: {
        enable: false,
        default: "Trực tiếp",
        listPayment: [
          { id: "5f7fca83b80e603d5300dcf4", name: "Trực tiếp"},
          { id: "62f5d79b5c424b277f174318", name: "Trực tuyến"},
        ]
      }, //IGATESUPP-40204-thuongld: [HCM iGATE V2] [Gấp] Q.Phú Nhuận - thêm hình thức thanh toán trên Menu Thống kê lệ phí hồ sơ HCM [SD867]
      dossierOverdueHCMSectorOnlyAgencyId: true, //trankequang-IGATESUPP-38906 - [HCM iGATE V2] Sở 4T - Yêu cầu điều chỉnh Menu Thống kê hồ sơ trễ hạn
      showMenu_DosPersonal: false, // IGATESUPP-32572: Thêm tính năng từng Tài khoản có thể xem lại, thống kê , liệt kê các hồ sơ đã thực hiện
      numberDateAdditionalRequirement: {
        enable: false,
        maxNumber: 10,
        statusId: '640155cf1aee0a3ee93c8db1'
      }, //IGATESUPP-40441-thuongld-[HCM iGATE V2] [Ưu tiên] Sở TNTM - BQLATTP - Thêm tính năng cho Hồ sơ quá hạn bổ sung
      insertIdMoneyReceipt: {
        enable: false,
        apiName: 'idMoneyReceipt'
      }, //IGATESUPP-41415-thuongld-[HCM iGATE v2] Sở Tư Pháp - Lấy số biên lai đưa vào API tích hợp hệ thống Bộ Tư Pháp
      requestToSubmitPaperApplication: {
        dossierTaskStatus: "64111af15706bf6444283feb",
        dossierMenuTaskRemind: "640e796667b52157543bccd0",
      }, // IGATESUPP-IGATESUPP-41248 - Thay đổi trạng thái HS khi Yêu cầu gửi hồ sơ bản giấy
      paymentConfirmed: { //trankequang-IGATESUPP-40983: Ban Quản lý ATTP Điều chỉnh các chức năng thanh toán và biên lai điện tử
        dossierTaskStatus: "6411109d5706bf6444283fd8",
        dossierMenuTaskRemind: "6409826d1aee0a3ee93c8dbc",
        confirmPayment: false,
      },
      showAllowChangeProcessProcedure: true, //hohuyluat-IGATESUPP-39947  true hiển thị select box, false ẩn select box
      enableShowButtonAllowChangeProcess: 1, //hohuyluat-IGATESUPP-39947  1 hiển thị button, 0 ẩn button
      enableShowReleaseDate : false, // IGATESUPP-IGATESUPP-42400 true : hiển thị input, false : tắt hiển thị
      enableShowComboboxDossierResult: false, // IGATESUPP-IGATESUPP-42006 -hohuyluat true -hiển thị combobox hình thức nhận kết quả, false - ẩn combobox
      showIssuePaperReceiptBtn: false, //trankequang-IGATESUPP-42644: true: hiện false: ẩn nút phát hành biên lại giấy
      issuePaperReceiptForAllFees: false, //trankequang-IGATESUPP-42644: true: hiện false: ẩn nút phát hành biên lại giấy
      agencyDeleteAdditionalFeeDossier: [], //yenlinh-IGATESUPP-43202: danh sách cơ quan hiện nút xóa phí tự thêm trong hồ sơ
      onlyPaidFee: false, //trankequang-IGATESUPP-42644: true: hiện false: ẩn nút phát hành biên lại giấy
      showadditionalRequirementDate : true, //IGATESUPP-40055 : Sở GDĐT - Hiển thị thời gian bổ sung hồ sơ gần nhất tại Menu Hồ sơ chờ tiếp nhận
      enableLinkToDetailDossier: false, // hohuyluat-IGATESUPP-IGATESUPP-42366 true : cho phép link, false : không cho phép link to dossier
      showSectorByAgencyCondition: true, //IGATESUPP-37808 -Yêu cầu điều chỉnh mục Lọc theo Thủ tục trong Thống kê sổ theo dõi
      showSearchProcedureForHCMLogbook : true, //IGATESUPP-37808 -Yêu cầu điều chỉnh mục Lọc theo Thủ tục trong Thống kê sổ theo dõi
      showFileUpdateHistory : true, //IGATESUPP-42021 - BAN QUẢN LÝ ATTP_Yêu cầu xem lịch sử đính kèm file của cơ sở
      showAgencyParentStatisticsDossierOverdue: {
        enable: false,
        type: 2, //1 Lấy cơ quan cha, 2 gọi API lấy cơ quan root
        agencyTagId: "0000591c4e1bd312a6f00003" //Id Cơ quan hành chính
      }, //IGATESUPP-41964-thuongld-[HCM iGATE V2] [Gấp] Quận Thủ Đức - Điều chỉnh Thống kê hồ sơ trễ hạn [SD939]
      enablePrintReceiptPaper:{
        enable: false,
        idTagSampleReceiptPaper: "6425207af1075b5f485ad1cd"
      }, //IGATESUPP-42898 -hohuyluat- hiển thị cho phép in biên lai giấy
      enableAttachResultFileToEmail: false, //vqhuy.lan-IGATESUPP-47939 - [HCM iGATE V2] Sở TTTT - Đính kèm kết quả xử lý vào email gửi cho người dân [SD1396]
      hideDaysWaitingForPayment: [], //vqhuy.lan-IGATESUPP-42066 - [HCM-Igate V2] BAN QUẢN LÝ ATTP_Yêu cầu bỏ ràng buộc trường "Số ngày chờ thanh toán"
      show2HourDue:false,//IGATESUPP-41626 true: canh bao ho so con 2 gio bang mau cam, false: khong hien canh bao
      sortByAppointmentDate: {
        isSort: false,
        allowedAgency: []
      }, // IGATESUPP-40585-trankequang - Quận 11 - Tính năng Sắp xếp trong Menu Xử lý hồ sơ
      showGeneralStatisticAgency: false, // trankequang-IGATESUPP-41580 : Thêm cột Đơn vị trong Danh sách chi tiết hồ sơ Thống kê hồ sơ cơ quan
      showScrollToTopButton:false, // IGATESUPP-43647: [HCM iGate V2][Sở Y Tế] Tạo nút để roll lên đầu trang
      showPublicAdministrativeAgency: {
        enable: false,
        admin: true,
        agencyTagId: "0000591c4e1bd312a6f00003", //Id Cơ quan hành chính
        filterProcedureAdmin: true
      }, //IGATESUPP-42964-thuongld-[HCM iGATE V2] Thêm đơn vị cho danh mục sổ Hành chính công [Sở Y Tế]
      logBookNumberCopiesHCM: {
        enable: false,
        listProcedureCode: [],
      }, //IGATESUPP-43247-hohuyluat-[HCM iGATE V2]  - Yêu cầu thêm tính năng trong Thống kê sổ theo dõi HCM
      showPrintResidentInfo:false,
      showProcedureCheckCitizenInfo: false, //IGATESUPP-44583-thuongld: [HCM - iGATE v2] Sở Tư Pháp - Không cho phép chỉnh sửa thông tin Thông tin người nộp khi nộp hồ sơ trực tuyến
      showStatusLLTP: 0,
      showAppointmentNoLLTP: 0, // IGATESUPP-44582[HCM - iGATE v2] - Sở Tư Pháp - Hiển thị số phiếu LLTP ở menu Xử lý hồ sơ và Tra cứu hồ sơ toàn cơ quan
      showDossierFee: false, // trankequang-IGATESUPP-43898 : Thêm cột lệ phí ở tra cứu hồ sơ toàn cơ quan
      hideNationCodeIfExist2TypeOfCode: false,
      showReceiveDossierResultMethod: false,   //IGATESUPP-44349-hohuyluat bật tắt tính năng hình thức nhận kết quả
      callApiLGSPAfterGotReceipt: {
        showBtnTransferToLGSP: false,
        procostTypeId: "",
        procostTypeListId: [] //IGATESUPP-70964-thuongld-[HCM - iGATE v2] Sở Tư Pháp - Kiểm tra chức năng call API hồ sơ LLTP sau khi nhấn phát hành biên lai[SD3195]
      }, // IGATESUPP-43963 [HCM - iGATE v2] Sở Tư Pháp - Bổ sung nút chuyền hồ sơ sang phần mềm chuyên ngành [SD1220]
      showFilterPaymentStatus: false, //IGATESUPP-43964-thuongld-[HCM - iGATE v2] Sở Tư Pháp - Bổ sung cột trạng thái Trạng thái thanh toán và filter hồ sơ theo trạng thái thanh toán [SD1218]
      allowedAgencyEnableDirectlyPaymentStatus:['62970e84011f773c23acd266'], //IGATESUPP-43338-hohuyluat-cơ quan cho phép bật tính năng trạng thái đã thanh toán trực tiếp
      showListAdditionalRequest : false, // IGATESUPP-36241 : BQLATTP - Yêu cầu điều chỉnh lọc Danh sách công việc của Hồ sơ bổ sung
      enableProcedureCbx : false,  //nghiaht-IGATESUPP-44347 - Thống kê sổ theo thủ tục bổ sung thêm ô text cho search tên thủ tục

      isShowOnlyMemberInDepartment: false, //IGATESUPP-62905 search Thống kê theo combox Cán Bộ

      assignedCodeToResultDossier: {
        enable: false,
        config: {
          code: {
            tx: 100,
            ty: 750,
            fontSize: 13,
            bold: false
          },
          day: {
            tx: 500,
            ty: 750,
            fontSize: 13,
            bold: false
          },
          month: {
            tx: 550,
            ty: 750,
            fontSize: 13,
            bold: false
          }
        }
      }, //IGATESUPP-43609-thuongld-[HCM iGATE V2] [Gấp] Sở STTTT - Phần cho số Giấy phép và ngày tháng đóng dấu [SD956]
      paymentPlatformId: ["62f5d79b5c424b277f174318"], // trankequang-IGATESUPP-44266 -  Id thanh toan trực tiếp payment
      defaultPaymentId: "5f7fca83b80e603d5300dcf4", // trankequang-IGATESUPP-44266 -  Id thanh toan mặc định khi không có payment trong dossier
      showTakeNumberPopupHCM: false, //IGATESUPP-45560-hohuyluat - [HCM iGATE V2][Sở Y Tế] Cấp số ký hiệu cho nhiều file KẾT QUẢ giải quyết TTHC cùng một hồ sơ	bật tắt tính năng cấp mã số cho sở Y tế
      showProceAdminMultiHCM: false, //hohuyluat-IGATESUPP-45560 - bật tắt tính năng bộ số, sổ thủ tục Sở Y tế hcm
      citizenPaymentMethod: false, // IGATESUPP-44323-trankequang hiển thị hình thức thanh toán thực tế
      showExportFileBtn: false, // trankequang-IGATESUPP-43633 : ẩn hiện nút kết xuất dữ liệu vào file
      listAgencyShowExportFileBtn: [], // IGATESUPP-53821-hohuyluat list cơ quan hiển thị nút kết xuất file yêu cầu bổ sung, từ chối hồ sơ
      timeCountAfterReceiveHCM: false, //IGATESUPP-45293 Tính năng đếm ngược 8h khi tiếp nhận
      timeFinishWorkDay: 15,  //IGATESUPP-45293 Tham số cấu hình thời gian kết thúc ngày làm việc 14h, 15h, 16h .....
      listAgencyCountDown8h: [],//IGATESUPP-45293 Tham số cấu hình id các đơn vị cho phép sử dụng chức năng đếm ngược .....
      template: {
        requireAdditionalTemplate: "https://staticv2.vnptigate.vn/file/test-mau-phieu-yeu-cau-bo-xung-ho-so.docx",
        refuseTemplate: "https://staticv2.vnptigate.vn/file/test-mau-phieu-tu-choi-ho-so.docx",
      },
      allowShowInfoDossierWhenExportFileHCM: {
        enable : false, // IGATESUPP-53821 bật tắt hiển thị thêm thông tin hồ sơ khi kết xuất file
        templateHCM:{
          requireAdditionalTemplate: "https://staticv2.vnptigate.vn/file/test-mau-phieu-yeu-cau-bo-xung-ho-so-hcm.docx",
          refuseTemplate: "https://staticv2.vnptigate.vn/file/test-mau-phieu-tu-choi-ho-so-hcm.docx"
        }
      },
      enableAppliedDateFilter: 0, //quocpa 17/04/2023:IGATESUPP-44355-Bổ sung thêm chức năng thêm trường thông tin lọc "Ngày nộp":
      dossierProAdmins: {
        allowedAgency: [],
      }, // IGATESUPP-46405-trankequang hiện bảng mã được cấp
      agencyAllowSignerHCC: ["62948b09011f773c23acd171", "000000000191c4e1bd300029"],  //IGATESUPP-46401-hohuyluat cơ quan cho phép lấy danh sách người ký - được cấp quyền người ký
      allowSelectReceipt: {  // IGATESUPP-45955-thuongld-HCM-Igate V2] BAN QUẢN LÝ ATTP_Thêm chức năng phép chọn phát hành biên lai theo cá nhân hoặc doanh nghiệp khi thực hiện thanh toán payment
        enable: false,
      },
      allowEndProcessMultiDossier: false,//IGATESUPP-45029-thuongld-[HCM - iGATE v2] Sở Tư Pháp - Bổ sung chức năng cho phép kết thúc nhiều hồ sơ cùng lúc
      enableViewDetail022017 : false,// diemdtn-IGATESUPP-46592 bật true cho phép xem chi tiết số liệu báo cáo thông tư 02 2017
      showConfirmDirectlyPayment : 0, // IGATESUPP-46220 Ban Quản lý ATTP_Khóa "Xác nhận thanh toán" ở tài khoản chuyên viên 1 cửa (chỉ có kế toán có xác nhận) [SD1341]
      enableWithdrawDate: false, // quocpa 15/05/2023:IGATESUPP-45778 [HCM-Igate V2] Ban Quản lý ATTP_ Hiển thị thông tin ngày rút hồ sơ
      isDefaultElectronicReceipt: false,    //IGATESUPP-47060-thuannm [HCM - iGATE V2] Sở Tư Pháp - Điều chỉnh nút chức năng Phát hành biên lai [SD1401]
      isDefaultBusinessInformation: false,  //IGATESUPP-47060-thuannm [HCM - iGATE V2] Sở Tư Pháp - Điều chỉnh nút chức năng Phát hành biên lai [SD1401]
      setDefaultBusinessInformation : {     //IGATESUPP-47060-thuannm [HCM - iGATE V2] Sở Tư Pháp - Điều chỉnh nút chức năng Phát hành biên lai [SD1401]
        suppliers: '63368ef843beb047e15596d8',
        formOfPayment: 'Trực tiếp',
        receiptBook: "1"
      },
      isSingleDossierReceipt: false,           //IGATESUPP-47060-thuannm [HCM - iGATE V2] Sở Tư Pháp - Điều chỉnh nút chức năng Phát hành biên lai [SD1401]
      isPrintReleasePees: false,            //IGATESUPP-47060-thuannm [HCM - iGATE V2] Sở Tư Pháp - Điều c  hỉnh nút chức năng Phát hành biên lai [SD1401]
      showPrintResidentInfoOnlineReception: false, //IGATESUPP-47955-thuongld-[HCM iGATE V2] Quận Tân Bình - Thêm tính năng In thông tin tra cứu khi tiếp nhận hồ sơ [SD1414]
      showPrintResidentInfoReception: false, //IGATESUPP-47955-thuongld-[HCM iGATE V2] Quận Tân Bình - Thêm tính năng In thông tin tra cứu khi tiếp nhận hồ sơ [SD1414]
      showDocumentNoLLTP: 0, // IGATESUPP-48807-thuongld-[HCM - iGATE v2] Sở Tư Pháp - Lấy thêm trường documentNo đẩy vào số LL
      showLastPageButton:false, // IGATESUPP-46910 [HCM iGATE V2] Sở LĐTBXH - Thêm nút chuyển đến trang cuối [SD1327]
      showApprovalCondition : false, //IGATESUPP-44607 Sở TTTT - Tạm dừng, gia hạn, yêu cầu bổ sung, dừng xử lý hồ sơ cần được phê duyệt trước [SD999]
      allowNextStepWaitingForApproval : false, //IGATESUPP-44607 Sở TTTT - Tạm dừng, gia hạn, yêu cầu bổ sung, dừng xử lý hồ sơ cần được phê duyệt trước [SD999]
      requireAttachmentWhenAdditionalRequest : true, // IGATESUPP-48730 không bắt buộc file đính kèm mặc định true là bắt buộc giống cũ/ false là không bắt buộc.
      allowShowStatusSyncLGSP: false, //IGATESUPP-48209-thuongld-HCM iGATE V2] Sở Công Thương_ yêu cầu hiển thị thông báo gửi hồ sơ thành công qua chuyên ngành [SD1469]
      checkPaymentComplete: false,  // thuan-IGATESUPP-44874 [HCM iGATE V2] Sở Giáo dục và Đào tạo - Thêm tính năng nhắc hồ sơ chưa thanh toán cho cán bộ 1 cửa [SD1195]
      checkPaymentCompleteAgency: ["1.004889.000.00.00.H29", "1.005092.000.00.00.H29"],  // thuan-IGATESUPP-44874 [HCM iGATE V2] Sở Giáo dục và Đào tạo - Thêm tính năng nhắc hồ sơ chưa thanh toán cho cán bộ 1 cửa [SD1195]
      isEnableWarningLongName : false, //IGATESUPP-49160 tên file quá dài gây ra lỗi file khi xuống chuyển qua chương trình chuyên ngành
      enableSectorUserSearch : false,   // IGATESUPP-46241 Hiển thị hồ sơ với điều kiện lĩnh vực, menu hồ sơ chờ tiếp nhận
      allowReSendLLTPLgspHcm: {
        enable: false, //Cho phép gửi lại
        countSend: 3, //Số lần gửi lại
        deplay: 3000, //Thời gian gửi lại sau bao nhiêu ms
        keyword: ['Read timed out', 'Exception null', 'fail', '401', '500'], //Từ khóa sẽ recall
      }, //IGATESUPP-50128-thuongld-[HCM - iGATE v2] Sở Tư Pháp - Resend API tích hợp đẩy chuyên ngành khi hồ sơ báo lỗi timeout [SD1618]
      OS_HCM_SCT_TKDN : {  //hohuyluat-IGATESUPP-50210 cấu hình cho phép nộp hồ sơ đối với tài khoản doanh nghiệp
        isEnableTKDN: false ,
        type: "2"
      },
      departmentsAdministrativeProcedures:{  //IGATESUPP-49970-danhdd.hcm - [HCM iGATE V2] [Sở y tế ] Thêm tiện ích cho thống kê sổ HCC
        enable: false,
        agencyAncestorsId: []
      },
      syncLGSPHCM :{
        enable: false,
        listAgencyIdSyncLGSPHCM : ["628d9090011f773c23acd03b", "62970e84011f773c23acd266"]  // list cơ quan áp dụng hiển thị và lọc danh sách hồ sơ động bộ không thành công
      },
      enableAllowChangeLevelCode: false, //IGATESUPP-49941 bật tính năng chuyển đổi tên "cấp mã số " cho chuyên viên sang " thẩm định hồ sơ"
      allowReceptionSendLLTPLgspHcm: {
        enable: false,
        procedureList: ['2.000488.000.00.00.H29'],
        apiName: 'idMoneyReceipt'
      }, //IGATESUPP-49960-thuongld-[HCM - iGATE v2] Sở Tư Pháp - Bổ sung nút chức năng Chuyển hồ sơ sang PM chuyên ngành ở màn hình Xử lý hồ sơ
      allowShowMultiTimeProcess: false, //IGATESUPP-50540-thuongld-[HCM iGate V2] [Sở Y Tế] Chỉnh sửa hệ thống để có thể cấu hình đúng với quy trình tái cấu trúc được ban hành
      enableRenameReceipt : {    //IGATESUPP-51003-thuannm [HCM - iGATE v2] Sở Tư Pháp - Điều chỉnh tính năng phát hành biên lai điện tử [SD1643]
        agencyList: [
          ''
        ],
        renameConfig: [
          {
            id : '64a288d6a7f5da379a06caf2',
            name : 'Test phát hành biên lai đổi tên',
          },
          {
            id : '',
            name : '',
          },
        ]
      },
      listAgencyUseSortType: [],        // IGATESUPP-48319-ngochtt.hcm-[Sở Công Thương] - Sắp xếp theo thứ tự hồ sơ nộp trước nằm trên, hồ sơ nộp sau nằm dưới
      changeAppointmentDateWhenResume: {
        enable : false,   //IGATESUPP-50396-thuan: [HCM - iGATE v2] Sở Tư Pháp - Điều chỉnh tính năng Tạm dừng hồ sơ [SD1640]
        agencyList : [], //IGATESUPP-50396-thuan: [HCM - iGATE v2] Sở Tư Pháp - Điều chỉnh tính năng Tạm dừng hồ sơ [SD1640]
      },
      changeDateAdditionalRequirementDetail: false, // IGATESUPP-52632-thuannm: Ban Quản lý ATTP_Lỗi tính sai ngày "Thời hạn hết hạn bổ sung" [SD1600]
      allowShowMoreInfoStatistic012020HCM: {
        enable: false,
        agencyIds: []
      }, //IGATESUPP-47059-thuongld-[HCM iGATE V2] Sở Văn Hóa và Thể Thao - File excel Danh sách hồ sơ Thống kê theo thông tư 01 [SD1115]
      DossierMenuTaskRingWithAgency: { //IGATESUPP-47961 Q.Phú Nhuận - Thêm tính năng thông báo hồ sơ gần tới hạn tiếp nhận [SD1246] (2)
          NearbyDueAcceptedWhen: 2,
          OverDueAcceptedWhen: 0,
          NearbyDueProcessingWhen: 2,
          OverDueProcessingWhen: 2,
        listAgency: [
          {
            AgencyID: "62f31441e13bef0ed2e582ca",
            NearbyDueAcceptedWhen: 4,
            OverDueAcceptedWhen: 0,
            NearbyDueProcessingWhen: 2,
            OverDueProcessingWhen: 0
          }
        ]
      },
      enableSearchNotDossierRequireAdd: false,  //IGATESUPP-50314-hohuyluat bật tắt tính năng lọc hồ sơ quá hạn
      enableShowMenu012020NewRequireAdd: false,  //IGATESUPP-50314-hohuyluat bật tắt menu hồ sơ 01/2020 HCMC khi có nhu cầu hiển thị
      changeProcessWhenReceiving: {    // IGATESUPP-51390-thuan [HCM - iGATE v2] Sở Tư Pháp - Bổ sung nút chức năng cho phép chọn lại quy trình ở màn hình Tiếp nhận hồ sơ trực tiếp [SD1661]
        enableMenuReceiving : false,
        enableMenuProcedure : false,
      },
      enableButtonEnableNation: false,     // IGATESUPP-53303-thuannm: [HCM-Igate V2] BAN QUẢN LÝ ATTP_Thêm trường thông tin "Quốc gia" vào biên lai [SD1854]
      allowChangeAccepter: false, //IGATESUPP-51915 bật tính năng Thay đổi tên cán bộ 1 cửa khi tiếp nhận bổ sung
      enableCheckboxDraftText: false,    // IGATESUPP-53992-thuannm: [HCM iGATE V2][Sở Y Tế] Thêm/bỏ ràng buộc ở chức năng cho số và thêm dự thảo
      enableShowFilterOrganizationDossierCancel: {
        enable: false,
        agencyIds: []
      }, //IGATESUPP-52538-thuongld-[HCM iGATE V2] Sở Lao động - Thêm lọc tìm kiếm theo tên cơ quan doanh nghiệp trên "Menu Hồ sơ không cần xử lý" [SD1744]
      listAgencyUseNewTemplateStatisticReceipt:[  //hohuyluat - 48933 - list cơ quan áp dụng mẫu thống kê biên lai mới
        // "62948b09011f773c23acd171", "62970e84011f773c23acd266","62f31441e13bef0ed2e582ca","62b02263011f773c23acd65c"
      ],
      changeColorProcessingTime:{                 // IGATESUPP-52779-danhdd.hcm [HCM iGATE V2] [Sở Y Tế] Đổi màu thời hạn bước của hồ sơ khi sắp tới hạn [SD1712]
        enable: false,
        agencyList:["62b9314e011f773c23acd717"],
        processingTime: 0
      },
      requiredAttachProcedureForm: {            // IGATESUPP-47310-thoaidt: [HCM iGATE V2] Quận Gò Vấp - Ràng buộc cán bộ đính kèm tphs bắt buộc khi tiếp nhận trực tiếp [SD1398]
        enable: false,
        agencyUsed: []
      },
      procedureLevelID: {       //IGATESUPP-50408-danhdd.hcm-[HCM iGATE V2] Q.Tân Bình - Thêm Menu Thống kê tổng hợp xử lý hồ sơ [SD 1604]
        enable: false,
        procedureLevel4: "62875a992afeb001f1e3c574",
        procedureLevel3: "5f5b2c4b4e1bd312a6f3ae24",
        procedureLevel2: "62b14c7d5f1bc52207923491",
      },
      receiveMultiple: {      //IGATESUPP-52906-thuan [HCM iGate V2] [Sở Y Tế] Điều chỉnh chức năng cấp số luồng bổ sung và từ chối [SD1700]
        enable: false,
        agencyList: [

        ],
        statusList: [

        ]
      },
      turnOnCountDown8h : false, //IGATESUPP-50774 [HCM iGATE V2] Quận Tân Phú - Lọc hồ sơ quá hạn tiếp nhận [SD1570]
      listAgencyCountDown8hSetEndWorkTime: [ //IGATESUPP-50774 [HCM iGATE V2] Quận Tân Phú - Lọc hồ sơ quá hạn tiếp nhận [SD1570]
          {
            agencyID: "62f31441e13bef0ed2e582ca",
            endWorkTime: 15
          }
      ],
      enableIssuerOfReceipts : false, //IGATESUPP-53873 [HCM - iGATE v2] Sở Tư Pháp - Ghi lại thông tin tài khoản phát hành biên lai điện tử
      offDossierDetailUpdate : { //IGATESUPP-53735 [HCM iGATE V2] Sở Văn Hóa và Thể Thao - Yêu cầu tắt đi tính năng cập nhật lại hồ sơ thay công dân [SD1775]
        enable: false,
        agencyIdList: ["6297a57c011f773c23acd346"],
      },
      hideRequestToWithdraw: {  // IGATESUPP-52654-nguyenttai: [HCM iGATE V2] Sở VHTT - Ẩn tính năng cán bộ "yêu cầu rút hồ sơ" [SD1777]
        enable: false,
        agencyUsed: []
      },
      judicialRecordCardProcedure:{  //IGATESUPP-50652-danhdd.hcm - [HCM - iGATE v2] Sở Tư Pháp - Bổ sung thống kê "Thống kê danh sách yêu cầu cấp phiếu LLTP" [SD1615]
        enable: false,
        eFormId: "630c71ff0bc016da410c9d19" ,
        procedure: []
      },
      processingTimeAfterAddition: {           //IGATESUPP-50162 - danhdd.hcm - [HCM-Igate V2] Ban Quản lý ATTP_Điều chỉnh chức năng cách tính ngày hẹn trả đối với hồ sơ bổ sung [SD1542]
        enable: false,
        idStatus : "60ebf0db09cbf91d41f87f8c",
      },
      resetProcressTimeCurrentTask :
      {
        turnOn : false,
        agencyList : []
      }, // IGATESUPP-55450 Sở Tư Pháp - Cập nhật hạn xử lý ở bước khi tiếp tục xử lý hồ sơ tạm dừng [SD1894]
      hidenOverDueCalculation : {   //IGATESUPP-58187 [Sở Y Tế] Ẩn đồng hồ đếm ngược đối với các hồ sơ bổ sung
        enable : false,
        dossierTaskStatus: [],
        applyAgencies:[] // IGATESUPP-55997: [HCM iGATE V2] Q.11 - Điều chỉnh tính năng thông báo hạn xử lý khi hs đang ở trạng thái ycbs [SD1769]
      },
      processBranch: false,      // ThinhHP-IGATESUPP-58704: Bổ sung tham số cấu hình và thuộc tính đánh dấu nhánh của task

      enableExportExcelHCM: false, //IGATESUPP-57347 [HCM iGate V2] Sở Xây dựng - Không xuất được file excel ở menu "Xử lý hồ sơ" [SD 2096]
      updateFeeForDirectReceptionDossier: { // IGATESUPP-56208 [HCM - iGATE v2] Sở Tư Pháp - Cập nhật lệ phí cấp thêm sau khi cập nhật eform [SD1963]
        applyAgency: [],
        feeId: "64fe82241169872e57ead3fb"
      },
      exportCancelDossiersOnPage: {  // IGATESUPP-51604 - nguyenttai.hcm - [HCM iGATE V2] Quận Bình Tân - Thêm nút Xuất excel trên Menu Hồ sơ không cần xử lý [SD1584] (2)
        enable: false,
        agencyUsed: [],
      },
      enableShowSendEmailOfficer: false, //IGATE-55181 - cho phép gửi email đến cán bộ khi tạo báo cáo
      showAppointmentDate: false, // IGATESUPP-56206 - S.TTTT Yêu cầu dịch vụ bổ sung chức năng tính ngày hẹn trả (SD1997)
      hideFeeStatistic: {              // IGATESUPP-thoaidt: Bật/tắt chức năng thống kê phí, lệ phí
        enable: false,            // bật/tắt cho phép thay đổi
        allowedMenu: [            // menu áp dụng
          'statistics/hcm-stp-receipt-fee-statistics'
        ],
        notice: 'Tạm ẩn chức năng'
      },
      hideSaveBtnOnlineReception: {              // IGATESUPP-60534-thoaidt: Bật/tắt nút lưu ở menu hồ sơ chờ tiếp nhận
        enable: false,            // bật/tắt cho phép thay đổi
        agencyUsed: []            // id cơ quan áp dụng
      },
      stopPointFormIOUpdate: 2,                // IGATESUPP-59596-thoaidt: Điều kiện dừng ngăn không cho update thông tin chi tiết khi vừa vào menu xử lý hồ sơ
      processingDossierStatusParam: '2,3,4,5,16,17',       // IGATESUPP-69378-thoaidt: Fix lỗi không hiển thị hồ sơ ở menu xử lý
      showDossierQualifiedRecept: false, //IGATESUPP-59515 - hohuyluat -[HCM iGate V2] [Sở Y Tế] bật tắt hiển thị chức năng đánh dấu hồ sơ đó đạt đủ điều kiện tiếp nhận
      enableFeeReportNonAccepted : false,           // IGATESUPP-57224 - danhdd.hcm - [HCM iGATE V2] Ban Quản lý ATTP_Điều chỉnh báo cáo Báo cáo thu phí, lệ phí
      enableShowListRequirement: false, // IGATESUPP-59209 [HCM iGATE V2] - Sở LĐTBXH - Thông tin yêu cầu bổ sung hiện không chồng chéo khó đọc và xử lý
      implimentCheckRequireAdditional: false, // IGATESUPP - 59957 - hohuyluat - bật tắt áp dụng thực hiện check hồ sơ bổ sung cho thống kê trễ hạn
      enableDownloadManyDossierRecept: { //HCM iGate V2] [Sở Y Tế] Thêm chức năng dowload tất cả các file nhiều hồ sơ [SD2176]
        enable: false,
        listAgency: [],
      },
      surveyOfficerConfig: {
        criteria1: 'TC-001',
        criteria2: 'TC-002',
        criteria3: 'TC-003',
        criteria4: 'TC-004',
        criteria5: 'TC-005',
      },//IGATESUPP-59592-thuongld-[HCM iGATE V2] Offsite_Điều chỉnh - Đánh giá hài lòng theo QĐ 25 SRS 5 FE
      surveyAgencyConfig: {
        criteria6: 'TC-006;CS6',
        criteria7: 'TC-007;CS7',
        criteria8: 'TC-008;CS8',
        criteria9: 'TC-009;CS9',
        criteria10: 'TC-010;CS10',
      },//IGATESUPP-59594-thuongld-[HCM iGATE V2] Offsite_Điều chỉnh - Đánh giá hài lòng theo QĐ 25 SRS 6 FE
      urlSiteRatings : "https://khaosatdvcmc.hochiminhcity.gov.vn/vi/", //IGATESUPP-58132-thuannm Đánh giá hài lòng theo QĐ 25 SRS 7, tách phiếu 1 và 3
      listAgencyAllowShowAddressWhenHover: [],  //IGATESUPP-60633 -hohuyluat - Hiển thị địa chỉ khi rê chuột vào mã hồ sơ
      statisticsHCMlogbookStatusCancel: null, //IGATESUPP-59643-thuongld-[HCM iGATE V2] Sở Lao Động - Thắc mắc về Thống kê sổ theo dõi HCM có hồ sơ đã từ chối [SD2249]
      plitUpdateDossierAndFormFile: false,  //IGATESUPP-54538: Tách luồng xử lý cập nhật tphs và tths
      statisticsHCMlogbookStatusRemoveList: [], //IGATESUPP-61348-thuongld-[HCM iGATE V2] Sở GĐDT - Lỗi Thống kê sổ theo dõi HCM có hồ sơ chưa tiếp nhận[SD2340]
      allowShowCancelDossierReception: {
        enable: false,
        statusId: [12, 19]
      }, //IGATESUPP-61198-thuongld-[HCM iGATE V2] Sở TNMT - VPDKDD GV - Thêm nút chuyển hồ sơ Từ chối từ chuyên ngành vào Menu Hồ sơ không cần xử lý [SD2163]
      showAnnouncementPopup: false,  // IGATESUPP-59871 - nguyenttai.hcm - [HCM iGATE V2] Offsite - Hiển thị thông báo (popup) cho người dùng khi có tính năng mới,tính năng thay đổi và link đến tài liệu HDSD [SD2190]
      grantSmpCode:{           //IGATESUPP-50962 - nvloi - [HCM iGATE V2] Huyện Cần Giờ - Bổ sung chức năng cấp mã số đăng ký kinh doanh [SD1489] (2)
        applyAgencies:[],
        enable: false,
        allowConfigProcedure:false,
        allowFillEForm: false,
        allowFetchEformByGrantedCode:false,
        grantButtonAllowedPermission:[],
        grantedCodeEFormField:"grantedCode",
        grantedDateEFormField:"grantedDate",
        allowGrantCodeOnce:false,
      },
      limitedAppointmentTimeOnProcedure: '15:00', //IGATESUPP-51590: Thêm cấu hình hẹn trả ngày kế tiếp và bắt đầu tính giờ hẹn trả vào ngày kế tiếp
      allowFilterPaymentStatusListAgency: [], //IGATESUPP-64432-thuongld-[HCM - iGATE v2] Sở Tư Pháp - Tối ưu filter TRẠNG THÁI THANH TOÁN ở menu TRA CỨU HỒ SƠ TOÀN CƠ QUAN [SD2515]
      allowFilterPaymentStatusListAgency1: [], //IGATESUPP-64432-thuongld-[HCM - iGATE v2] Sở Tư Pháp - Tối ưu filter TRẠNG THÁI THANH TOÁN ở menu XỬ LÝ HỒ SƠ [SD2515]
      allowFilterAcceptedDateOptimization: {  //IGATESUPP-64432-thuongld-[HCM - iGATE v2] Sở Tư Pháp - Tối ưu filter TRẠNG THÁI THANH TOÁN ở menu TRA CỨU HỒ SƠ TOÀN CƠ QUAN [SD2515]
        enable: false,            // bật/tắt cho phép thay đổi
        allowedMenu: [            // menu áp dụng
          'dossier/processing',
          'dossier/search'
        ],
        rangeLimitDossierProcessing: 7,
        rangeLimitDossierSearch: 7
      },
      allowFilterAppliedDateOptimization: {
        enable: false,            // bật/tắt cho phép thay đổi
        allowedMenu: [            // menu áp dụng
          'dossier/online-reception',
          'dossier/search'
        ],
        rangeLimitDossierOnlineReception:0,
        rangeLimitDossierSearch:0,
      },

      allowFilterAcceptedDateOptimizationCustom: {   //IGATESUPP-65451 [HCM iGATE V2] Offsite_Bổ sung tính năng tăng thời gian giới hạn cho từng cơ quan riêng [SD 2598]
        enable: false,            // bật/tắt cho phép thay đổi
        agencyUsed:[],            // cơ quan áp dụng
        allowedMenu: [            // menu áp dụng
          'dossier/processing',
          'dossier/search',
          'dossier/processing-hcc'
        ],
        rangeLimitDossierProcessing: 7,
        rangeLimitDossierSearch: 7,
        rangeLimitDossierProcessingHCC: 10
      },
      allowFilterAppliedDateOptimizationCustom: { //IGATESUPP-65451 [HCM iGATE V2] Offsite_Bổ sung tính năng tăng thời gian giới hạn cho từng cơ quan riêng [SD 2598]
        enable: false,            // bật/tắt cho phép thay đổi
        agencyUsed:[],            // cơ quan áp dụng
        allowedMenu: [            // menu áp dụng
          'dossier/online-reception',
          'dossier/search'
        ],
        rangeLimitDossierOnlineReception:0,
        rangeLimitDossierSearch:0,
      },
      filterAcceptedDateMenuRemind:false,
      filterAppliedDateMenuRemind:false,
      filterAcceptedDateMenuRemindProcessingHCC: false,

      autoFillSummary:{ //IGATESUPP -63846- hohuyluat: tham số bật tắt tính năng tự động điền mã HS vào trích yếu dự thảo
        enable: false,
        agency: []
      },

      allowOnlineReceptionSendLLTPLgspHcm: false, //IGATESUPP-64664-thuongld-[HCM - iGATE v2] Sở Tư Pháp - Bổ sung chức năng gọi API tích hợp ở nút Tiếp nhận hồ sơ trực tuyến [SD2509]      agencySelectorWithTags:{ //IGATESUPP-62704: [HCM iGATE V2] Điều chỉnh Combobox Cơ quan thực hiện chỉ lấy lên cơ quan hành chính (Menu Danh mục thủ tục) [SD2425]
      agencySelectorWithTags:{ //IGATESUPP-62704: [HCM iGATE V2] Điều chỉnh Combobox Cơ quan thực hiện chỉ lấy lên cơ quan hành chính (Menu Danh mục thủ tục) [SD2425]
        enable:false,
        tagIds:[]
      },
      notSumPauseDayToProcessingTime: false,  // IGATESUPP-58022: không tính numberOfPauseDay vào thời gian xử lý của task
      startAtNextDayAfterOffTime: false,   // IGATESUPP-63991: lỗi hồ sơ tiếp nhận sau 15h không tính qua ngày hôm sau
      listEformKeyClearTimezone: [],  // IGATESUPP-55455: list key eform bỏ qua timezone khi hiển thị
      addItemSearchHCMAdbook: false, // IGATESUPP-63883 - hohuyluat : bật tắt phần tử Số HCC và Trích yếu trên thanh tìm kiếm menu thống kê Sổ hành chính công
      allowOfficerToApproveWithdrawRequest: { // IGATESUPP-53965 nguyenttai.hcm [HCM iGATE V2] Sở Giáo Dục - Cho phép công dân rút hồ sơ đang xử lý [SD1730]
        enable : false,
        agencyUsed: []
      },
      enableNotResetDate: false, //IGATESUPP-65422: bật tắt tính năng không reset lại date khi chuyển xử lý nhiều hồ sơ
      listAgencyAppliShowTooltipOrganization: [], //IGATESUPP-64946 : list cơ quan áp dụng hiển thị thông tin CQ/DN trong tooltip
      agencyEnableFunction: {  //IGATESUPP-61201-danhdd.hcm [HCM iGATE V2] Quận 1 - Góp ý UI UX Q.1
        enable: false,
        lltp:{
          appointmentNoLLTP: ["000000000191c4e1bd300029"],
          documentNoLLTP: ["000000000191c4e1bd300029"],
          btnStatusLLTP: ["000000000191c4e1bd300029"]
        }
      },
      disableChangeCodeWhenTakeNumber:{ // IGATESUPP - 65995 - hohuyluat : bật tắt khóa tính năng chỉnh tay mã số được cấp khi thẩm định hồ sơ Sở GTVT
        enable: false,
        agencyIds: []
      },
      hideTotalDossier: {  // IGATESUPP - 66027 -hohuyluat : bật tắt khung danh sách công việc
        enable: false,
        agency: []
      },
      enableShowCopyDossierAppliedButton: {  // IGATESUPP-65608 : bật tắt hiển thị tính năng nút chức năng sao chép thông tin hồ sơ cũ
        enable: 0,
        procedureShowButton:[]
      },
      updateDossierTemplateUrl: "https://staticv2.vnptigate.vn/file/updateDossierTemplate.xls", // IGATESUPP-67146: url file template update dossier from excel
      isFilterAppointmentDate: false, // dangquang-IGATESUPP-67276: bổ sung filter Hẹn trả từ/đến trong màn "Thống kê sổ theo dõi HCM"
      isViewProcedureLevel: {
        enable: false,
        listProcedureParse: []
      }, // IGATESUPP-65864 Quận 6 - Bổ sung cột mức độ thủ tục menu Thống kê theo cơ quan HCM [SD2695]: Thêm cột Mức Độ (data lấy từ mức độ thủ tục procedureLevelName) tại màn hình danh sách hiển thị và excell
      dossierReceiptCreationStatusConfig:{ //IGATESUPP-64637: [HCM - iGATE v2] Sở Tư Pháp - Bổ sung trạng thái ĐÃ PHÁT HÀNH BIÊN LAI vào menu Danh sách công việc [SD2493]
        enable:false,
        applyAgencies:[],
        receiptIssuedStatus:false,
        dossierTaskStatus: "",
        dossierMenuTaskRemind: "",
        dossierStatus:"",
        disableWhenDirectReception: false,
      },
      requireSaveIstorage: false, // IGATESUPP-67889 - hohuyluat : bật tắt ràng buộc lưu kho hồ sơ khi chuyển bước
      configChekBoxInFormConfigurePermissions:{  // IGATESUPP-81292: Bật/tắt các checkbox Gia hạn và Tạm dừng ở form cấu hình quyền.
        enable: true,
        CBCanIncreaseDue: false, // IGATESUPP-81292: Bật/tắt checkbox Gia hạn cho các process chưa được cấu hình.
        CBCanPaused: false, // IGATESUPP-81292: Bật/tắt checkbox Tạm dừng cho các process chưa được cấu hình.

      },
      disableChangeEditFileName:{   // dongoctanh-IGATESUPP-67276: bật tắt hiển thị chỉnh sữa tên tệp tin
        enable: false,
        agencyIds: []
      },
      isCheckTypeFileForAgency: {//dangquang-IGATESUPP-64632 chặn type file theo đơn vị
        enable: false,
        listFileForAgency: [],
      },
      showWaitUpdateDateSearchDossier: {
        enable: false,
        agencyIds: []
      }, //IGATESUPP-68676-thuongld-[HCM iGATE V2] Sở KHCN - Thêm ngày yêu cầu bổ sung (menu Tra cứu HS toàn cơ quan) [SD2437]
      reReceptionAdditionalDossierAgency:{ // IGATESUPP-68176 [HCM iGATE V2] Quận Tân Bình - Điều chỉnh luồng hồ sơ có yêu cầu bổ sung giấy tờ [SD 2793]
        enable: false,
        dossierTaskStatus:[],
        agency:[]
      },
      listAgencyRootAllowShowChild:["000000000191c4e1bd300029"], //IGATESUPP - 58173 - list cơ quan áp dụng cho phép hiển thị thống kê Tổng hợp xử lý chỉ lấy đến cơ quan/ đơn vị con
      listAgencyRootAllowShowChildHCM: false,//IGATESUPP-72521- list cơ quan áp dụng cho phép hiển thị thống kê Tổng hợp xử lý chỉ lấy đến cơ quan/ đơn vị con của HCM
      listTagNotAllowShowChild: [], //IGATESUPP - 58173 - list tag không cho phép hiển thị khi thống kê tổng hợp xử lý cho ủy ban nhân dân TP. chỉ lấy đến  Sở ban ngành và Quận Huyện
      isShowApplyMethodInLogBook: false, // IGATESUPP-69442 Quận Tân Bình - Thêm cột hình thức nộp tại Menu "Thống kê sổ theo dõi HCM"
      customNhanKqQ6: { // nguyenttai.hcm IGATESUPP-70628 [HCM iGATE V2] Quận 6 - Thêm thông báo khi click chọn hình thức Hệ thống tiếp nhận và Trả hồ sơ tự động 24/7 [SD3155]
        enable: false,
        tagId: ""
      },
      showCopyInformationDossierOld: false, // IGATESUPP - 69440 bật tắt hiển thị nút cấu hình thủ tục,cho phép sao chép thông tin chung từ hồ sơ cũ
      allowUseOldStatusProcessReceptionAdditional: {
        enable: false,
        agencyIds : ["62948b09011f773c23acd171"],
      }, //danhdd.hcm IGATESUPP-70289 [HCM iGate V2] Sở Y Tế - Lỗi hồ sơ bổ sung [SD3114]
      isFilterAddressOrganization: false, //dangquang-IGATESUPP-72196: Thêm tìm kiếm Địa chỉ Cơ quan/ Doanh nghiệp ở Tra cứu toàn cơ quan
      showEditQuantityDocument: false, //IGATESUPP-71812 [HCM iGATE V2] Sở TNMT - CNVPDKDD- Cho phép điều chỉnh số lượng bản đối với hồ sơ sau khi đã tiếp nhận [SD3244]
      applyTakeNumberNewMethod:{ // IGATESUPP-70972 - [HCM iGate V2] Sở Y Tế - Điều chỉnh lại tính năng nhảy số tự động của chức năng cho số văn bản [SD3033]
        enable: false,
        agencyId:[]
      },
      showAddDocuments :{
        enable: false,
        listDossierStatus: [
          "60e409823dfc9609723e493c", //Hs mới đăng ký
        ],
        agency: []
      }, //phucnh.it2-IGATESUPP-76958: Cho phép ẩn hiện nút thêm giấy tờ ở form chi tiết hồ sơ > Cập nhật thành phần hồ sơ
      receiptDetails: {
        enable : false,
        agencyIds: [],
      },	 //phucnh.it2-IGATESUPP-82887: Cho phép hiển thị check box “Lấy thông tin từ eform chi tiết của thủ tục khi phát hành biên lai” ở menu Danh mục thủ tục > Thêm mới
      updateProcessStepDue: false,  // hohuyluat - 72743 - [HCM iGATE V2] Quận 7 - Lỗi hồ sơ yêu cầu thanh toán mà vẫn tính giờ [SD3264]
      isNewSortExcelInProcessingDossier: false, // dangquang-IGATESUPP-73682:  Sở Y Tế - Xuất excel không đúng thức tự hồ sơ như đã sắp xếp trên hệ thống [SD3419]
      dateDiffEscapeWeekend: false, //IGATESUPP-73425 [HCM iGate V2] Sở Xây dựng - Lỗi tính trễ hạn xử lý bước đang xử lý [SD 3398]; true: không tính thời gian cuối tuần (tính trễ/sớm/đúng hạn hồ sơ API: bt/timesheet-gen/--by-dossier-id/)
      disableWarningFileOptional: false,  //IGATESUPP-76075 [HCM iGATE V2] Offsite - Không bắt buộc đính kèm file khi chứng thực hồ sơ giấy [SD3482]
      integratedReception: false, // thuannm-IGATESUPP-75122 [HCM - iGATEv2] Sở Tư Pháp - Bổ sung chức năng đáp ứng luồng xử lý hồ sơ LLTP trực tuyến[SD3507]
      paymentDayLimit: false, //IGATESUPP-75121 [HCM - iGATE v2] Sở Tư Pháp - Bổ sung chức năng hồ sơ quá hạn thanh toán trực tuyến sẽ chuyển trạng thái "Dừng xử lý"[SD3506]
      timesheetIdPaymentDayLimit: "628866972afeb001f1e3c575", //IGATESUPP-75121 - timesheet id mặc định là 628866972afeb001f1e3c575 có loại trừ thời gian nghỉ lễ/cuối tuần
      applyTakeCodeByYear:{ // IGATESUPP-76469 - cho phép lấy số theo năm
        enable: false,
        agencyId:[]
      },
      showCheckboxUpdateDueDate: false, //IGATESUPP-76051 - Quận Gò Vấp - Lỗi ngày tiếp nhận bị thay đổi- do chuyển bước nhiều hồ sơ có cập nhật lại hạn xử lý hồ sơ quá hạn
      showRejectType: { //thuannm-IGATESUPP-76161: [HCM iGate V2] Sở Y Tế - Đơn vị yêu cầu phân biệt rõ ràng hồ sơ bị từ chối khi chưa tiếp nhận và từ chối bằng văn bản [SD3404]
        enable: false,
        agency: []
      },
      isApplyGetRootAgency: false, //IGATESUPP-76861 - bật tắt sử dụng get root agency
      listTagAgency: [], // danh sách tag id các cấp
      showDossierUpdateTime :{
        enable: false,
        listDossierStatus: [],
        agency: []
      }, //phucnh.it2-IGATESUPP-77778: [HCM iGATE V2] SỞ ATTP - Hiển thị thời gian cập nhật hồ sơ gần nhất tại Menu Hồ sơ chờ tiếp nhận [SD 3712]
      applyShowInforDraft:{ // IGATESUPP-77935 - Sở Y Tế - cho phép hiển thị đầy đủ thông tin dự thảo
        enable: false,
        agencyId:[]
      },
      ratingPerLineQD25: false, // IGATESUPP-73900 Hiển thị tất cả lượt đánh giá của hồ sơ thay vì tính trung bình
      showChangeDirectPayment: {
        enable: false,
        statusId: "14,17",
        offCheckAllFee: false
      }, //nhipttt-IGATESUPP-79392 statusId dạng String, cấu hình dossierStatus từ 0 đến 14, các status cách nhau bằng dấu phẩy, [HCM iGate V2] Offsite - Chuyển hồ sơ về luồng thanh toán trực tiếp đối với hồ sơ tiếp nhận trực tiếp nếu yêu cầu thanh toán trực tuyến không thành công
      saveLogSendNotificationToOfficer: false, //IGATESUPP-79580 HCM iGATE V2] Sở TTTT - Cán bộ không nhận được tin nhắn hồ sơ đến dù có tick tin nhắn [SD 16976]
      allowApplyRenderDepartmentForRefuse: {
       // IGATESUPP-79627 -cho phép áp dụng kết xuất tên cơ quan đơn vị khi yêu cầu từ chối hồ sơ
        agencyId:[], // list cơ quan áp dụng
        template:{
          refuseTemplateHCM: "https://staticv2.vnptigate.vn/file/test-mau-phieu-tu-choi-ho-so-co-quan-hcm.docx",
          refuseTemplate: "https://staticv2.vnptigate.vn/file/test-mau-phieu-tu-choi-ho-so-co-quan.docx"
        }
      },
      serviceIntegrationGPLX: {
        donViXyLy: "79",
      },
      filterSubmissionDate: {
        enable : false,
        AgencyIds: ["62948b09011f773c23acd171", "62de0ecc640a9909ee041d59"],

      },//IGATESUPP-86664 Điều chỉnh chức năng lọc hồ sơ theo ngày nộp có thêm ngày ngày nộp bổ sung

      digitalsignatureButton: false, // IGATESUPP-79626 [HCM iGATE V2] Sở GTVT - Thêm chức năng ký số cho file đính kèm trước khi tiếp nhận hồ sơ
      enableCheckStatusRequestPayment: false, // IGATESUPP-80058 Kiểm tra trạng thái hồ sơ khi yêu cầu thanh toán
      requestForAdditionalRemindQTSS: "", // IGATESUPP-81846: Trạng thái YCBS cho Quy trình song song
      dossierAddedQTSS: "", // IGATESUPP-81846: Trạng thái ĐBSHS cho Quy trình song song
      listAgencyIdsQTSS: [], //IGATESUPP-91328 tối ưu nhắc việc
      reReceiveQTSS: "", // IGATESUPP-81846: Trạng thái Tiếp nhận lại cho Quy trình song song
      putTaskWhenAssign: false, // IGATESUPP-81841: Tuy chon asign Task khi chuyen nhieu user xu ly (Bat buoc bat khi dung quy trinh song song)
      paperDocumentsReasonsRequire: "", // IGATESUPP-82498 - Nội dung và lý do xử lý khi yêu cầu bổ sung bản giấy
      hidenButtonCreatReceipt: {  //IGATESUPP-86379 [HCM - iGATE v2] Sở Tư Pháp - Không cho phép tác động vào hồ sơ có trạng thái "Không được tiếp nhận"[SD17182]
        enable: true,
        listStatus: []
      }

    },

    OS_AGG:{
      KHDTConfigId: '',
      agencyLevelConfig: {
        province: {
          id: "5f39f42d5224cf235e134c5a",
          name: "Cấp Tỉnh"
        },
        district: {
          id: "5f39f4155224cf235e134c59",
          name: "Cấp Huyện"
        },
        village: {
          id: "5febfe2295002b5c79f0fc9f",
          name: "Cấp Xã"
        }
      },
      enableSearchNotDossierRequireAdd: false,
      enableLinkToDetailDossier: false,
      enableAddCompanyNameToDetailStatistic012020: false,
      isOpenInNewTabHCM: false,
      enableViewDetail022017 : false,
      enableLGSP_LLTP: 0, //IGATESUPP-58598 :biến enable cho phép bật tắt chức năng liên thông LLTP DLK
      fieldCode_HTTP:{},
      configThamSoId: "6503db12d9f19721ff628c13", //Cấu hình tham số v2 lý lịch tư pháp của DLK
      requestQty: 0,
      enableLGSP_HTTP: 0, //IGATESUPP-61324 :biến enable cho phép bật tắt chức năng liên thông Hộ tịch DLK
      idCapCoQuanHanhChinh_HTTP: "0000591c4e1bd312a6f00003",
      idMappingTypeAgency_HTTP : "5fc0707a62681a8bef000018",
      agencyLevelId: {
        level1: "5f7dade4b80e603d5300dcc4",
        level2: "0000591c4e1bd312a6f00003",
        level3: "0000591c4e1bd312a6f00004"
      },
      ProvinceLevelId : "60b8b5979adb921904a02150", // ITTHC cấp tỉnh
      ProvinceDepartmentLevelId : "60b8b5a29adb921904a02151", //TTHC cấp phòng ban thuộc tỉnh
      DistrictLevelId : "5f39f4155224cf235e134c59", //TTHC cấp huyện
      CommueLevelId : "5febfe2295002b5c79f0fc9f",
      rootAgencyId: "",// mã UBND tỉnh
      rootAgencyObjectId:"",
      payment_online_params:[],
      enableAgesb:true,
      GTVTAGG: false, //IGATESUPP-93997 Tra cứu hồ sơ BGTVT
      showStatusDKKD:1,//khoivt-IGATESUPP-83235-Tham số api check CCCD/CMND
      checkCardIdUrl:"http://dangkykinhdoanh.angiang.gov.vn/Services/swCheckData.asmx?op=CheckCardId",//khoivt-IGATESUPP-83235-Tham số api check CCCD/CMND
      intDistrict:1,//khoivt-IGATESUPP-83235-Tham số api check CCCD/CMND
      enableApiCheckCardId:false//khoivt-IGATESUPP-83235-Bật tắt API check CCCD/CMND
    },
    OS_BLU: {
      isHiddenDossierStatus: false, //kimloannt-IGATESUPP-84233-Ẩn nút tìm kiếm theo trạng thái ở menu "Tra cứu hồ sơ cá nhân"
    },
    OS_BDG: {
      changePaymentContent: false,
      enableSearchBySector: false,                       // IGATESUPP-26878-tuqn: Thêm tham số tìm kiếm theo lĩnh vực cán bộ được phân tại chức năng hồ sơ chờ tiếp nhận
      vilis: {                    //IGATESUPP-31793: Tham số cho Vilis
        existSync: false           // Bật tắt đồng bộ hồ sơ
      },
      connect:{                         //IGATESUPP-31793: Tham số liên thông chung bao gồm vilis
        existProcessConnect: false,     //Bật tắt tham số  quy trình gắn với thủ tục liên thông
        existProcedureConnect: false,    //Bật tắt tham số thủ tục liên thông
        existProcedureFormConnect: false, //Bật tắt tham số giấy tờ theo thủ tục
        existSectorConnect: false,         //Bật tắt tham số lĩnh vực liên thông
        enableSyncConnectMinistry: false
      },
      emc: {
        enableLog: false, //Cho phep dung log emc
        enableUsingCustomTracking: false //Cho phep sử dụng custom emc
      },
      nameFunctionRequestWithdraw: false, // Đổi tên "Yêu cầu rút hồ sơ" -> "Hồ sơ không đủ điều kiện giải quyết"
      isEnableLableOrganizationInformation: false, // Bật tắt hiển thị thông tin tổ chức ở lưới danh sách hồ sơ
      isEnableLableSignedFile: false,      //Bật tắt hiển thị chữ "Đã ký số"
      isEnableSetColorCountdownHandle: false,      //Bật tắt hiển thị màu chữ nổi bật trường "Thời gian xử lý còn lại"
      isEnableChkboxReceiveThroughPostOffice: false, // Bật tắt hiển thị tích chọn "Hồ sơ nộp qua bưu điện"
      notify:{                                           // Tham số thông báo
        subsystem1Gate: "Hệ thống Một cửa điện tử",
          urlViewDetail: "https://dichvucongtest.vnptigate.vn/vi/dossier/detail/",
        urlView1Gate: "https://motcuatest.vnptigate.vn",
        withDrawConnect:{                                      // Thông báo rút hồ sơ dành cho liên thông
          email:{
            officer:{
              enable: false,
              canEdit: false,
              send: false,
              title: "No-reply Hệ thống Một cửa điện tử",
              templateId: "63e1ac095609d2101fe273ce"
            }
          },
          sms:{
            officer:{
              enable: false,
              canEdit: false,
              send: false,
              title: "No-reply Hệ thống Một cửa điện tử",
              templateId: "63e1ac095609d2101fe273ce"
            }
          },
          zalo:{
            officer:{
              enable: false,
              canEdit: false,
              send: false,
              title: "No-reply Hệ thống Một cửa điện tử",
              templateId: "63e1ac095609d2101fe273ce"
            }
          }
        }
      }
    },
    OS_TGG: {

    },
    OS_KHA: {
      showAgencyParentStatisticsDossierUnreceived: {
        enable: false,
        type: 2, //1 Lấy cơ quan cha, 2 gọi API lấy cơ quan root
        agencyTagId: "5def47c5f47614018c000056", //Id Cơ quan hành chính
      }, //IGATESUPP-45058-longtt
      agencyLevel0: "5f39f4335224cf235e134c5b",
      agencyLevel1: "5f39f4155224cf235e134c59",
      agencyLevel2: "5febfe2295002b5c79f0fc9f",
      procedureLevel4: "60a70ef04aba560b6069e649",
      procedureLevel3: "60a70e6e4aba560b6069e648",
      procedureLevel2: "60a70e644aba560b6069e647",
      procedureLevel1: "60a70e584aba560b6069e646",
      enableSmsResumeDossier: false,
      isKHA: false,
      iLisKHA: true,
      DashboardMotcua: false
    },
    OS_KGG: {
      isDigitizeOutput: true,
      isOnlineAttachResults: false, //IGATESUPP-80324 Điều chỉnh Thống kê tổng hợp xử lý hồ sơ cột Trực tuyến lấy thêm điều kiện đã có kết quả và có đính kèm file
      isKGG: false, //IGATESUPP-78371 Bổ sung form tra cứu thông tin đăng ký doanh nghiệp KGG
      isCheckDossierReportOnline: false, //IGATESUPP-74144 Báo cáo Thanh toán trực tuyến chỉ tính các hồ sơ thuộc các TTHC có phí
      listProjectType: [ // Danh sách loại đề án
        {id: '1', name: 'Đề án 06'}
      ],
      isDossierFeeOfficerKGG: false, //IGATESUPP-74325 Thêm cột Cán bộ xác nhận vào báo cáo lệ phí
      isFeeNameProcedure: false, //IGATESUPP-74325 Thêm cột Tên TTHC vào báo cáo lệ phí
      isSortBySubmissionDateOnlineReception: false, //IGATESUPP-69891 Sắp xếp hồ sơ tại menu "Hồ sơ chờ tiếp nhận" theo ngày nộp tăng dần
      isDossierFeeStatisticNotCheckReceipt: false,
      enableCommentTransferMultiple: false, //IGATESUPP-56520 add thêm nội dung bình luận
      agencyIdsNotShowProcedureStatistic: [],
      procedureCodeEssentialList: [],
      listAgencyCheckSendSMSEmail: [],                     // IGATESUPP-38717-thaolk: list agency check tham số checkSendSMSCitizen, checkSendEmailCitizen, checkSendSMSOfficer, checkSendEmailOfficer
      checkSendSMSCitizen: true,                           // IGATESUPP-38717-thaolk: mặc định enable, check gửi tin nhắn cho người dân (toàn bộ các form)
      checkSendEmailCitizen: true,                         // IGATESUPP-38717-thaolk: mặc định enable, check gửi email cho người dân ...
      checkSendSMSOfficer: true,                           // IGATESUPP-38717-thaolk: mặc định enable, check gửi tin nhắn cho cán bộ ...
      checkSendEmailOfficer: true,                         // IGATESUPP-38717-thaolk: mặc định enable, check gửi email cho cán bộ ...
      hideDefaultFormIO: true,
      isEnableOS: true,
      isUserRQ: false,
      isShowContent: false,
      SCTKGG: {
        allowedAgencySCT: []
      },
      isSCTKGG: true,
      configLedgerAuthType: '63aa95684d44f42944856625',
      agencyLevel0: "5f6b17984e1bd312a6f3ae4b",
      agencyLevel1: "5f7dade4b80e603d5300dcc4",
      agencyLevel2: "5f6b177a4e1bd312a6f3ae4a",
      procedureLevel4: "62b529f524023d508ef38fc0",
      procedureLevel3: "62b529c424023d508ef38fbd",
      procedureLevel2: "62b52a0224023d508ef38fc1",
      procedureLevel2Old: "5f5b2c2b4e1bd312a6f3ae23",
      statisticKGGGetSuppendedCancelled: true,
      donvibaocaoTag: "63be60537b74d1683ae149c4",
      landProcedureSectorId: "62c4334fefefed5a4aaced53",
      downloadExcelProcedure:false,     //IGATESUPP-39233 - KGG - Thêm chức năng xuất exel các thủ tục hành chính
      dossierProcessingViewKGG: false,      //IGATESUPP-39348 - [KGG] - Điều chỉnh giao diện danh sách hồ sơ
      hideHandlingComments: false,
      DossierLand: {
        statusDossier: "5f3a491c4e1bd312a6f00013",
        statusArray: ["60f6364e09cbf91d41f88857","60f6364e09cbf91d41f88858","60f6364e09cbf91d41f88859"],
      }, //IGATESUPP-52583 [KGG - iGate 2.0] Chức năng thống kê hồ sơ chuyển nghĩa vụ tài chính và thực hiện nghĩa vụ tài chính
      dossierStatusHiddenResult: ["60ed1c3409cbf91d41f87fa3"], //IGATESUPP-53115
      statusOnTimeDossier: ["60f6364e09cbf91d41f88859"], // IGATESUPP-53907  - Hiển th ị đúng hạn khi thực hiện bước Thực hiện nghĩa vụ tài chính
      remindNotify: {
        showRemindWorkAtHome: false //IGATESUPP-57415
      },
      useDueDateRemindWork: false, //IGATESUPP-57415
      numberSortedProcess: '3', //IGATESUPP-63513
      listAgencySorted: [], //IGATESUPP-63513
	    listUnitHideScanCode: [], //IGATESUPP-65183
      isHideScanKGG: false, //IGATESUPP-65183
      listNotRemindTaskKgg: [], //IGATESUPP-71836
      isNameReceiptKGG: false, //IGATESUPP-72294
      isBienLaiViettelKgg: false,
      isBienLaiMobiKgg: false,
      nameBienLaiMobi: "Bienlai_Mobi",
      isBienLaiMisaKgg: false,
      nameBienLaiMisa: "Bienlai_Misa",
      determineFinancialsList: [], //IGATESUPP-75215
      isDetermineFinancials: false, //IGATESUPP-75215
      determineFinancialsStatusList: [], //IGATESUPP-75215
      isBienLaiLGSPMinhTue: false,
      exportCheckCitizenLogToExcelBtn: false // IGATESUPP-75223: Thêm nút xuất excel cho Thống kê lượt tra cứu thông tin công dân qua CSDLQGVDC
    },
	  OS_HGG: {
      enableMainAssigneeProcess: false,         // IGATESUPP-48114: Bổ sung ô chọn người xử lý chính khi cấu hình quy trình
      enablePositionAssignee: false,		// IGATESUPP-48118: [HGG-iGate] Thông tin user thêm chức vụ đằng sau user
      enableDescriptionFlow: false,           // IGATESUPP-47131 tiennb.it4: Bổ sung thông tin chú thích công việc xử lý ở giao diện xử lý hồ sơ
      enableSearchProcedureCode: false,
      configIdGPLXBGTVT: "660b683e707633326933395d",
      formatSignedFileName: false, // IGATESUPP-55380  thay đổi tên file ký số theo HGG (thêm signed  vào tên file gốc )
      showDossierName: false, // IT360-883034: [iGate2.0] Bổ sung hiển thị trường tên hồ sơ từ biểu mẫu ra ngoài thông tin danh sách hồ sơ ( Task IT360-867320 )
      keepFileWhenSignVgca: false, // IGATESUPP-58910 thêm tham số để không xoá file gốc sau khi ký số
      isShowOpenButton: false, // IGATESUPP-55041  nút đóng mở thông tin chi tiết hồ sơ theo quyền
      hideDeleteButtonDossier: false, // IT360-954240 [HGG-iGate] Đề nghị hỗ trợ ẩn các nút xoá hồ sơ ở giao diện của cán bộ, công dân
      hideDeleteButtonDetailHGG: false, // IT360-954240 [HGG-iGate] Đề nghị hỗ trợ ẩn các nút xoá hồ sơ ở giao diện của cán bộ, công dân
      hiddenStatisticDossierAgency: false, // IT360-1052401 [iGate2.0] Hỗ trợ chỉnh sửa, sắp xếp báo cáo thống kê tổng hợp xử lý hồ sơ trên hệ thống
      listAgencyHiddenForStatisticUsedByHGG: ["647db4f453ca8f385878ebde",
                                              "647db4f353ca8f385878ebcc",
                                              "647db4f353ca8f385878ebc9",
                                              "647db4f353ca8f385878ebc6",
                                              "647db4f353ca8f385878ebdb",
                                              "647db4f453ca8f385878ebe1",
                                              "647db4f353ca8f385878ebc3",
                                              "647db4f453ca8f385878ebe4"],
      hiddenStatusDossierRemindOnRingHGG: false, // IGATESUPP-77060 [HGG-iGate] Đề nghị bỏ nhắc việc hồ sơ Dừng xử lý, Từ chối, Đã huỷ
      listStatusDossierRemindOnRingHGG: ["61ee30eada2d36b037e00005",
                                         "60f52ed209cbf91d41f88838",
                                         "60f52f3109cbf91d41f8883a"],
      isShowBtnSyncDossier: false // IGATESUPP-83248 Cập nhật trạng thái hồ sơ
    },
    integratedTypesHPG :[ {
                            "name": "Cấp phép lao động nước ngoài - Sở LĐTBXH",
                            "id": "CPLDNN-SLD",
                            "enable": true
                          },
                          {
                            "name": "VBDLIS DLK",
                            "id": "VBDLIS_DLK",
                            "enable": true
                          }
                        ],
    OS_HPG: {
      showHistoryFile: false,
      quickSearchByAgency: false,
      quickSearchHideWithdrawn: false,
      receiptExtra2: false,
      vgca_sign_copy : false,
      vgca_sign_copy_label : "Ký số Bản sao điện tử",
      notSyncWithoutRecepted: false, //IGATESUPP-81943 - Chỉ đồng bộ hồ sơ lên DVCQG khi hồ sơ đã được tiếp nhận
      hpglistlayout: false, // IGATESUPP-76458 đánh dấu lấy theo table hiển thị của HPG
      dossierSearchShowAgency: false , // IGATESUPP-76458 // đánh dấu ẩn hiện cột agency trong table hiển thị của HPG
      showRemindWorkAtHome: false,                          //IGATESUPP-82347: Hiển thị chức năng nhắc việc HPG
      useDueDateRemindWork: 1,                           //IGATESUPP-82347: Giá trị 1: hạn xử lý toàn quy trình. Giá trị 0 hoặc không tồn tại tham số: ngày hẹn trả
      showAgencyLevel: false, // IGATESUPP-83561 // đánh dấu ẩn hiện cột agency level,
      statistics:{
        dossierUnreceived:{
          showDossierCode: false
        },
        procedure: {
          hideProcedureCheck: false,
          hideStatusProcedure: false
        }
      },
      isUploadDossierToIStorage: false,
    },

    OS_QBH: {
      quangBinhId: "60b87fb59adb921904a0213e",
      appointmentDateUpdatedEnable: false, // IGATESUPP-71245 Hỗ trợ kiểm tra lại chức năng điều chỉnh ngày hẹn trả kết quả khi tiếp nhận hồ sơ trực tiếp/tuyến
      isQBH: false,
	  enablemustPrintCoupons:   // IT360-805005 nhanht.qbh: Bổ sung chức năng bắt buộc in phiếu động QBH
        {
          enable: false
        },
      limitChooseAppointmentDate: false,
      enableControlHumanProcesse: false,
	  urlDichvucong: 'https://dichvucongtest.vnptigate.vn/vi/',
	  isShowConfigureReceiptCode: false,
      idLienThongHoSo: '63989eb467302055292f4d62',
      isGenerateReceiptCode: false,
      qbhSaveInfoCitizens: false, //sonnn-qbh-IGATESUPP-61957
	  configTotalDayProcessAgency: false, //sonnn.qbh-IGATESUPP-46020
      isDisplayLienThongInfo: false, //sonnn.qbh-IGATESUPP-46020
      isReceivingTemplate: false, //chinhtla-IGATESUPP-52983
      isPrintTemplate: false, //chinhtla-IGATESUPP-52900: giao diện in phiếu theo đặc thù QBH
      remindNotify:{            //IGATESUPP-53479 thông báo nhắc việc
        showRemindWorkAtHome: false,
        useDueDateRemindWork:1,
        timeDueByStep: 8,       // Thời gian tính để nhắc việc so với due
        modifyTime : false          // Biến sử dụng +- thời gian
       },
      convertPrintPaper: false, //nanta-IGATESUPP-53947: bậc/ tắt chuyển đổi bảng điện tử sang bảng in giấy
      idConfigNps:'62948261ce7a6d62d41c10c1', //nanta-IGATESUPP-53947: id cấu hình tham số National Public Service
      textConvert:'Xuat tai Cong dich vu cong truc tuyen Tinh Quang Binh- Website https://dichvucong.quangbinh.gov.vn', //nanta-IGATESUPP-53947: thông tin text Xuất tại PDF chuyển đổi bảng ký giấy lưu ý chỉ ghi tiếng viết ko dấu
      levelProcedure : [
        {
          id : "61f25eb372eb62396f2fed69",
          code: "MUCDO_1",
          name : "Mức độ 1"
        },
        {
          id : "5f5b2c2b4e1bd312a6f3ae23",
          code: "MUCDO_2",
          name : "Mức độ 2"
        },
        {
          id : "5f5b2c4b4e1bd312a6f3ae24",
          code: "MUCDO_3",
          name : "Mức độ 3"
        },
        {
          id : "5f5b2c564e1bd312a6f3ae25",
          code: "MUCDO_4",
          name : "Mức độ 4"
        }
      ],    //nanta-IGATESUPP-55928 mức động thủ tục hành chính đặc thù
      isFeePaymentTemplate : false, //chinhtla-IGATESUPP-54405: giao diện phí/lệ phí QBH
      configTimeSheetId :'63d78b9bee48c32f84775902',    //IGATESUPP-56111 minhnh- Thêm configTimeSheet
      qbhPrintBtn : false, //chinhtla-IGATESUPP-58182: tách nút in phiếu tại giao diện xử lý của cán bộ
      qbhLockOffline: false, //chinhtla-IGATESUPP-58536: Tiếp nhận hồ sơ trực tiếp áp dụng cho các TTHC chỉ cho phép nộp trực tuyến
      idLoaiPhieuTiepNhan : '5f7fc83eb80e603d5300dce7', //chinhtla-IGATESUPP-58182
      qbhlist_collapse : false, //chinhtla-IGATESUPP-58270: điều chỉnh chế độ hiển thị tìm kiếm hồ sơ
      qbhEditFee: false, //chinhtla-IGATESUPP-58448: cập nhật lại số tiền phí, lệ phí tại form tiếp nhận hồ sơ trực tiếp
      qbhPostOffice_Info : false, //IGATESUPP-58425: Hiển thị thông tin tài khoản cán bộ bưu điện được ủy quyền đối với hồ sơ trực tuyến
      renamerejectbtn: false, //nhanht.qbh-IGATESUPP-58663 điều chỉnh tên nút chức năng "Từ chối"
      qbhlistlayout: false, // nhanht-IT360-901166 chỉnh sửa giao diện ds hs  QBH
      qbhrenamemenu : false, // nhanht.qbh-IGATESUPP-58232 Đổi tên menu QBH
      qbhAdditionLog: false, //chỉnhtla-IGATESUPP-59712: Bổ sung thêm 01 dòng log nhằm hiển thị thông tin cán bộ 1 cửa đã tiếp nhận hồ sơ bổ sung của CD
      qbhmenuaction: false, //nhanht.qbh xử lý các nút của qbh
      qbhstopprocess: false, //nhanht.qhb-IT360-901166
      qbhadditionrequest: false, //nhanht.qbh-IGATESUPP-59980 điều chỉnh giao diện liên quan chức năng "Yêu cầu bổ sung"
      qbhextendprocess: false, // nhanht.qbh-IT360-926351 điều chỉnh một số tính năng liên quan chức năng "Xin gia hạn"
      qbhwithdrawprocess: false, //nhanht.qbh-IGATESUPP-60025 điều chỉnh  lại luồng rút hồ sơ QBH
      qbhlayoutformalities: false,// /nhanht.qbh-IGATESUPP-59237  Điều chỉnh lại form ds hs QBH
      qbhconfirmform : false, // IGATESUPP-58420 : thêm nút xác nhận hoàn thành
      printAfterTax : false, //chinhtla-IGATESUPP-58855: In phiếu tiếp nhận và hẹn trả kết quả lần 2 sau khi thực hiện xác định nghĩa vụ tài chính
      qbhAutoFillForm : false,// chỉnh sửa  trường thông tin nhập trong form tiếp nhận hồ sơ
      qbhrenamestep: false, // IGATESUPP-59867 Điều chỉnh hiển thị tên bước rẽ nhánh tại form xác nhận hoàn thành bước công việc
      qbhMustCheckFee: false, //IGATESUPP-71283: Thực hiện bổ sung ràng buộc kiểm tra thanh toán phí/lệ phí đối với hồ sơ trực tiếp/trực tuyến
      qbhRenameYKXL:false,//IGATESUPP-78526: đổi tên ý kiến sử lý
      qbhSelectFee1CDT : false, //IGATESUPP-80993: Điều chỉnh form cập nhật phí, lệ phí tại trang một cửa điện tử
      applyMethodId : { trucTiep : "5f7fca83b80e603d5300dcf4", trucTuyen : "5fd1c7991b53d8779bc9ea7a"},
      allowNextStepWaitingForApprovalQBH: false,
      uploadFileTypeDanhGiaTTHC: [
        "application/msword",
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        "application/pdf",
        "application/vnd.ms-excel",
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
      ],
      enableApologyRequest  : false, // nhquan-IGATESUPP-54548: yêu cầu xin lỗi và gian hạn hồ sơ khi ycbs
      enableRequestAdditionalDossier : false, // nhquan-IGATESUPP-54548: bat buoc in phieu tiep nhan lan 2 khi thuc hien luong ycbs qbh
      qbhEditQuickSearch :false, //IGATESUPP-62765 Điều chỉnh chức năng tìm kiếm hs nhanh QBH
      qbhAddOwnerInfo : false, //chinhtla-IGATESUPP-62611 Phân biệt thông tin người nộp hồ sơ và người chủ hồ sơ trên hệ thống 1 CĐT
      qbhsavelog_csdldc: false,//nhanht.qbh-IGATESUPP-59234 Lưu log search csdldc
      qbhTickAllTPHS : false, //IGATESUPP-75100: Thêm chức năng chọn tất cả TPHS
      qbhDetailFormSTP :false,//IGATESUPP-77068 Lưu giữ thông tin tại biểu mẫu eform và tự động điền vào biểu mẫu chi tiết
      qbhSmsOfficerNoti: false, //IT360-1119904: Tắt gửi SMS cho cán bộ khi yêu cầu phê duyệt
      qbh_AttachPhieuBienNhan: false, //IGATESUPP-81904
      qbhAddInfoToFiles: false, //IGATESUPP-85944Bổ sung file đính kèm thông tin yctt
      qbhTemplateYctt: "https://staticv2.vnptigate.vn/file/mau-phieu-yctt.docx",
      levelTagID:{
        capSo:"611e494a43b16972f62dfd36",
        // capSo:"623300ca05955c108cf27eb2",
        capHuyen:"5f7dade4b80e603d5300dcc4",
        capXa:"5f6b177a4e1bd312a6f3ae4a",
        capXa2:"613ab84ee642ef04aaaeb59e",
        daiDai:"6399e7ae35e1b72daaa5a422"
      },
      listPlaceType: {
        province: [
          '5ee304423167922ac55bea01', // Province/City
          '5ee304423167922ac55bea04', // Province
          '5ee304423167922ac55bea05', // City
        ],
        district: [
          '5ee304423167922ac55bea03', // District/Town
          '5ee304423167922ac55bea06', // District
          '5ee304423167922ac55bea07', // District ???
          '5ee304423167922ac55bea08', // Town
          '5ee304423167922ac55bea09', // City
        ],
        ward: [
          '5ee304423167922ac55bea03', // Ward/Commune
          '5ee304423167922ac55bea10', // Ward
          '5ee304423167922ac55bea11', // Commune
          '5ee304423167922ac55bea12', // Town
        ]
      },
      "policeOfficeTagId": "0000591c4e1bd312a6f00002",
      "nationId": "5f39f4a95224cf235e134c5c",
      "cityId": "5def47c5f47614018c000044",
      businessRegistrationTemplate: {
        businessHouseholdImportTemplate: "https://staticv2.vnptigate.vn/file/qbh-mau-import-kinhdoanh-hocathe.xls"
      },
      levelTagSo1Cua: "6461aa846e509c3ee8e01046",
      agencyTagName: ["Cấp Tỉnh","Cấp Sở","Cấp Huyện","Cấp Xã","Cấp Xã/Phường/Thị trấn"],
      tagAgencyPulic:{
        configDepartmentTagId:"611e494a43b16972f62dfd36",
        configDistrictTagId:"5f7dade4b80e603d5300dcc4",
        configCommuneTagId:"613ab84ee642ef04aaaeb59e",
        configVpDKDDTagId:"64a386e9993ccf1747073497",
        configSpecialTagId:"64c3220cb53c50120f3b8cf0", //tag-id: Loại đơn vị của đơn vị con của các sở ban ngành
        configRoomTagId:"0000591c4e1bd312a6f00004",
        configAdministrativeAgency:"0000591c4e1bd312a6f00003", //tag-id: Cơ quan chức năng
      },
      listDepartmentSpecial:["642bd8be99897648ca5b0d09","642bd8be99897648ca5b0d11"], //Danh sach các sở ban ngành có đơn vị con đặc biệt,
      sendZaloAdditionalReceiving: 0 ,// Send notfiy tiếp nhận sau bổ sung  khi có tham số  OS_HCM.reReceptionAdditionalDossier
      enableCheckAppointmentDate: false, // Kiểm tra ngày hẹn trả khi tiếp nhận hồ sơ
      isShowBtnAutoReceiveQBH: false, // IGATESUPP-78186 Hỗ trợ kiểm tra lại chức năng tự động tiếp nhận hồ sơ sau 08 tiếng làm việc
      isQbhDossierKm: false,
      qbhAdditionRequirementTime : false, //IGATESUPP-75824 Điều chỉnh cách tính ngày hẹn trả kết quả mới sau khi tiếp nhận hồ sơ yêu cầu bổ sung
      notification8h: 0 //IGATESUPP-98728 Hiển thị thông báo log tự động tiếp nhận 8h
    },
    limitedAppointmentTime: null,   // offtime giờ hành chính
    statistics : {
      // start QD473 clone menu dvc > motcua
      onlyAgencyList: '',
      yearNumber: 3,        // Số năm thống kê
      isVisibleCancelled: false, // hiện hồ sơ dừng và hủy
      filterByAgencySelected: false, // Bật = true để lọc hiển thị cơ quan đã chọn. Tắt để hiển thị tất cả
      hideTableStatisticAndMeshStatistic: true, // mặc định ẩn dạng bảng và dạng lưới
      labelReportQNI: 1,
      agencyLevelDVC: [                  // Loại đơn vị
        { level: '1', id: '5f6b17984e1bd312a6f3ae4b', name: 'Cấp Tỉnh' },
        { level: '2', id: '5f7dade4b80e603d5300dcc4', name: 'Cấp Huyện' },
        { level: '3', id: '5f6b177a4e1bd312a6f3ae4a', name: 'Cấp Xã' }
      ],
      procedureLevelDVC: [ // Mức độ thủ tục
        { level: '1', id: '61f25eb372eb62396f2fed69', name: 'Mức độ 1' },
        { level: '2', id: '5f5b2c2b4e1bd312a6f3ae23', name: 'Mức độ 2' },
        { level: '3', id: '5f5b2c4b4e1bd312a6f3ae24', name: 'Mức độ 3' },
        { level: '4', id: '5f5b2c564e1bd312a6f3ae25', name: 'Mức độ 4' }
      ],
      // end QD473 clone menu dvc > motcua
      paginationType: 'page',         // Phân trang báo cáo
      msTimeOutSearch: 400,           // Độ trễ gọi api danh mục
      isAssignee: true,
      enableSearchByKeyword: false, //IGATESUPP-32142 Bố sung tìm kiếm theo từ khóa sổ theo dõi
      agencyLevel: [                  // Loại đơn vị
        {id: '5f6b17984e1bd312a6f3ae4b', name: 'Cấp Tỉnh'},
        {id: '5f7dade4b80e603d5300dcc4', name: 'Cấp Huyện'},
        {id: '5f6b177a4e1bd312a6f3ae4a', name: 'Cấp Xã'}
      ],
      agencyLevelNotBelongStatisticalAgency: [ // Loại đơn vị
        {id: '0000591c4e1bd312a6f00004', name: 'Phòng ban chức năng'}
      ],
      procedureLevel: [
        {id: '61f25eb372eb62396f2fed69', name: 'Mức độ 1'},
        {id: '5f5b2c2b4e1bd312a6f3ae23', name: 'Mức độ 2'},
        {id: '5f5b2c4b4e1bd312a6f3ae24', name: 'Mức độ 3'},
        {id: '5f5b2c564e1bd312a6f3ae25', name: 'Mức độ 4'}
      ],
      procedureAgencyLevel: [
        {id: '5f39f42d5224cf235e134c5a', name: 'Cấp Tỉnh'},
        {id: '5f39f4155224cf235e134c59', name: 'Cấp Huyện'},
        {id: '5febfe2295002b5c79f0fc9f', name: 'Cấp Xã'}
      ],
      administrativeAgencyTag: [
        {id: '0000591c4e1bd312a6f00003', name: 'Cơ quan hành chính'}
      ],
      agencyLevel20206c: [                  // Cap don vi bao cao 01/2020/TT-VPCP/6c
        {id: '611e494a43b16972f62dfd36', name: 'Sở ngành', level: '1'},
        {id: '5f7dade4b80e603d5300dcc4', name: 'UBND Huyện/Thị xã/Thành phố', level: '2'},
        {id: '5f6b177a4e1bd312a6f3ae4a', name: 'UBND Xã, Phường, Thị trấn', level: '3'}
      ],
      agencyLevelSector: [                  // Cap don vi bao cao linh vuc
        {id: '5f6b17984e1bd312a6f3ae4b', name: 'Cấp Tỉnh', level: '1'},
        {id: '5f7dade4b80e603d5300dcc4', name: 'Cấp Huyện', level: '2'},
        {id: '5f6b177a4e1bd312a6f3ae4a', name: 'Cấp Xã', level: '3'}
      ],
      sectorVPCTTPVHCCAgencyTagConfig : '5f3a491c4e1bd312a6f00007',
      agencyByTagId: '0000591c4e1bd312a6f00003',
      isAssigneeTask: false,
      statistics0120206HGG: { // IGATESUPP-114723: [HGG-iGate] Đề nghị hỗ trợ kiểm tra báo cáo theo thông tư 01 2020 theo mẫu 06c
        menuTitle06: 'Thống kê theo thông tư 01/2020/TT-VPCP/06',
        agencyLevel: {
          province: {
            id: '5f6b17984e1bd312a6f3ae4b',   // Id tag "Cấp tỉnh"
            statisticsTag: '5f6b17984e1bd312a6f3ae4b',  // Id tag dùng để thống kê các sở ban ngành thuộc tỉnh
            name: 'Cấp Tỉnh', // Label hiển thị trong select box
            header: 'Tình hình, kết quả giải quyết TTHC  thuộc thẩm quyền của UBND cấp tỉnh' // Header hiển thị trong file excel
          },
          district: {
            id: '5f7dade4b80e603d5300dcc4', // Id tag "Cấp huyện"
            statisticsTag: '67ce6a72e4c27c1ba6f9a200',  // Id tag dùng để thống kê các phòng ban thuộc huyện
            name: 'Cấp Huyện',  // Label hiển thị trong select box
            header: 'Tình hình, kết quả giải quyết TTHC  thuộc thẩm quyền của UBND cấp huyện'  // Header hiển thị trong file excel
          },
          village: {
            id: '5f6b177a4e1bd312a6f3ae4a', // Id tag "Cấp xã"
            statisticsTag: '5f6b177a4e1bd312a6f3ae4a',  // Id tag dùng để thống kê các ubnd xã
            name: 'Cấp Xã',  // Label hiển thị trong select box
            header: 'Tổng hợp tình hình, kết quả giải quyết TTHC  thuộc thẩm quyền của các UBND cấp xã'  // Header hiển thị trong file excel
          }
        },
        exportExcel: {
          config06b: {
            number: 'Biểu số II.06b/VPCP/KSTT', // Biểu số trong file excel báo cáo 06b
            title: 'TỔNG HỢP TÌNH HÌNH, KẾT QUẢ GIẢI QUYẾT THỦ TỤC HÀNH CHÍNH CỦA UBND CẤP HUYỆN', // Title báo cáo hiển thị trong file excel báo cáo 06b
            sendAgenies: 'UBND cấp huyện.', // Đơn vị gửi trong file excel báo cáo 06b
            receiveAgenies: 'UBND cấp tỉnh.', // Đơn vị nhận trong file excel báo cáo 06b
          },
          config06c: {
            number: 'Biểu số II.06c/VPCP/KSTT', // Biểu số gửi trong file excel báo cáo 06c
            title: 'TỔNG HỢP TÌNH HÌNH, KẾT QUẢ GIẢI QUYẾT THỦ TỤC HÀNH CHÍNH CỦA UBND CẤP TỈNH', // Title báo cáo hiển thị trong file excel
            sendAgenies: 'UBND cấp tỉnh.', // Đơn vị gửi trong file excel báo cáo 06c
            receiveAgenies: 'Văn phòng Chính phủ.', // Đơn vị nhận trong file excel báo cáo 06c
          }
        }
      },
    },
    notification: {
      isNotification: false,
      isNotificationStage1: false,
      isNotificationStage2: false,
      isNotificationRing: false
    },
    OS_HCM_SCT_LV : { // hohuyluat-IGATESUPP-49264 [HCM iGATE V2] [Gấp] Sở Lao Động - tham số bật tắt list thủ tục được phân cho cơ quan
      isEnableFilter: false ,
      listAgencyIdUnit: []
    },
    updateAgencySectorByLevel: false,  //IGATESUPP-42433-yenlinh: đồng bộ cây THHC theo mức độ
    listProcedureAgencyLevel: [],   //IGATESUPP-42433-yenlinh: danh sách cấp thủ tục
    isCheckOpenAdvancedSearchBox: false, //thaolk-IGATESUPP-40567: Nếu trước đó có mở [Tìm kiếm nâng cao], sau khi back về giao diện menu [Xử lý hồ sơ] thì hiển thị luôn mục tìm kiếm nâng cao
    filterStatusProcedureForm: false,   //IGATESUPP-29705-ThinhHP: Lọc danh sách ProcedureForm theo trạng thái true: lọc status, false: không lọc
    filterByPositionAgencyType: false, //IGATESUPP-33110: Bật true lọc ds tiếp nhận hồ sơ theo chức vụ và loại đơn vị
    signNEACConfigId: "6375a3a2aa1eef3cf6eee657", //IGATESUPP-32881-thuongld Thêm cấu hình lấy chữ ký số NEAC
    timesheetV2: false, //IGATESUPP-35698 Bổ sung tham số lựa chọn api timesheet: true = v2, false = v1
    timesheetV3: false, // IGATESUPP-85047 Bổ sung tham số lựa chọn api timesheet: true = v3

    hideSectorNotPermission: false, // IGATESUPP-34422 Bật/tắt ẩn lĩnh vực không được phân cho cán bộ
    showCodeSectorSearch: false,   // yenlinh-IGATESUPP-38422 Bật/tắt hiển thị mã lĩnh vực trong combox tìm kiếm
    showAgencyProcedureSearch: false,   // yenlinh-IGATESUPP-38422 Bật/tắt hiển thị mã cơ quan thủ tục trong tooltip combox tìm kiếm
    visibleApprovalAgency: 0, // IGATESUPP-37312 Bật/tắt combobox lựa chọn đơn vị phê duyệt
    duplicateApprovalAgency: false, // IGATESUPP-37312 Cho phép cấu hình trùng cơ quan, đơn vị được phê duyệt
    disableCancelDossier: false, // IGATESUPP-37511 Bật/tắt truy cập hồ sơ dừng xử lý
    enableUpdateDueDate: false, //IGATESUPP-40043 Bật true bổ sung cập nhật duedate ở bước xác nhận hoàn thành
    subMenuEnable: false, //IGATESUPP-39932 Bật/tắt submenu. Giá trị: true - hiện, fasle - ẩn
    dossierHasFFOSwapDue: true, //IGATESUPP-41127 Bật/tắt hoán đổi giá trị hạn xử lý toàn quy trình cho thủ tục có bước thực hiện nghĩa vụ tài chính
    deadlineForAdditionalRequests: {
      enable: false,
      defaultDay: 90
    }, //IGATESUPP-40980 Thời gian hết hạn yêu cầu bổ sung.
    showCancelProcessingInFirstTask: 0,
    iLis:{
      isProcessIlis:false,
      ilisConfigId:"",
      synciLis:false
    },
    syncPaymentStatus: false, //thaolk-IGATESUPP-42794: Lỗi đồng bộ trạng thái thanh toán ở chức năng tìm kiếm
    paymentRequest: {
      id: '62e35794a106a86e839f765b',
      name: 'Yêu cầu thanh toán'
    },
    paidDossier: {
      id: '62e35811a106a86e839f765f',
      name: 'Đã thanh toán'
    },

    bhtn:{
      isProcessBhtn:false,
      bhtnConfigId:"",
      syncBhtn:false
    },
    checkRequireHTTP: 0, //dhhcuong-IGATESUPP-43848: bỏ check bắt buộc các trường dữ liệu hộ tịch = 1
    onlinePaymentMethodIdList: ['5f7fca9fb80e603d5300dcf5'],
    onlinePaymentMethodId: '5f7fca9fb80e603d5300dcf5',
    iPCheckUrl: 'https://api.ipify.org/?format=json',
    checkFeeFile: false, // IGATESUPP-48787 bật true để không lấy thông tin từ id procost, id form, id file
    disableOnline: false,// IGATESUPP-50987 bật true để hiện chức năng  thủ tục quy trình không nộp trực tuyến
    hiddenGetDocumentIoffice: false,
    dvcltIpssReplace: 'http://***********:8080/VXPAdapter',// IGATESUPP-52673 ip ss tỉnh
    dvcltIpssQg: 'http://ip-ss-quocgia:8080/VXPAdapter',// IGATESUPP-52673 ip ss qg
    hiddenWithdrawnRemind: false,
    withdrawnRemindId: "6151c728ba2a04299f949871",
    OS_CMU:{

      autoCheckRequiredCmu:false,
      showEditDocButton: false,
      titleGeneralReport:"Báo cáo chung",
      levelTagID:{
        capSo:"623300ca05955c108cf27eb2",
        capHuyen:"5f7dade4b80e603d5300dcc4",
        capXa:"5f6b177a4e1bd312a6f3ae4a",
        },
      syncFaceRecognition: false,
      showTableDossierFree: false,
      inputOptionFee: false,
      levelCodeIds: [
        "5f5b2c4b4e1bd312a6f3ae24",  //IGATESUPP-57735
        "5f5b2c564e1bd312a6f3ae25"
      ],

        agencyLevelConfig:{
          province:{
            id: "5f39f4335224cf235e134c5b",
            name:"Cấp Tỉnh"
          },
          district:{
            id: "5f39f4155224cf235e134c59",
            name:"Cấp Huyện"
          },
          village:{
            id: "5febfe2295002b5c79f0fc9f",
            name:"Cấp Xã"
          }
        },
      autoUpdateAcceptedDate:{
        enable:false,
        timeSheetId:"651fc5f56764867f23dcf366"
      },
      cmuEnableSyncResultReleasedToBLDTBXH: false,
      updatePaymentMethodTrucTiep: false,
      directPaymentMethodName: "Trực tiếp",
      checkRequirePaymentCMUAuto: false,    //IGATESUPP-75771 Tự động check bắt buộc thanh toán khi tiếp nhận hồ sơ
      disaleCheckPaymentCMU: false,          ////IGATESUPP-75771 disable nut check bắt buộc thanh toán
      tagIdAgencyCustomSiteTitle:"65f103a56e5af40331fef333",//IGATESUPP-76605: Bổ sung cấu hình hiển thị phân hệ đo đạc bản đồ địa chính tỉnh Cà Mau
      siteNameCustomTitle:"PHÂN HỆ ĐO ĐẠC BẢN ĐỒ ĐỊA CHÍNH TỈNH CÀ MAU",//IGATESUPP-76605
      configIdLGSPCaMau:"65f25877efb7c043e258af54",//IGATESUPP-76605
      soCongThuongAgencyId: "637d7434f217d52a06d6d0f4",//IGATESUPP-76605
      configIdSoCongThuong: "65f7f36588e2f03492cd95c2",//IGATESUPP-76605
      isCMU : false,
      isRequestCMU : false //IGATESUPP-94895
    },
    nationId: this.envConfig.rootAgency.nation.id,
    placeDefaultProvinceId: this.envConfig.placeDefaultProvinceId,
    placeDistrictTypeId: this.envConfig.placeDistrictTypeId,
    isQueryProcessingDossier: false, // IGATESUPP-64097
    isTrimRefuseReasonText: false, //IGATESUPP-64019: trim khoảng trắng và xuống dòng thừa trường lý do từ chối
    scrollOverflowRefuseReasonText: false, //IGATESUPP-64019: thu gọn và scroll trường lý do từ chối
    procedureCategoryFixRole: false, // IGATESUPP-66680: true- Chi lay thong tin danh sach thu tuc cua don vi dang dang nhap; false - lay tat ca thu tuc cua don vi dang cong tac
    OS_SLA: {
      dossierProcessingTimer: false,
    },
    showSubmitDossierKiosk: false, // IGATESUPP-58683
    showQRVNPTPayMerchant: false,
    systemIdOfKiosk: "5f7c16069abb62f511880007", // IGATESUPP-58683
    OS_DLK: {
      enableLGSP_LLTP: 0, //IGATESUPP-58598 :biến enable cho phép bật tắt chức năng liên thông LLTP DLK
      configThamSoId: "6503db12d9f19721ff628c13", //Cấu hình tham số v2 lý lịch tư pháp của DLK
      requestQty: 0,
      enableLGSP_HTTP: 0, //IGATESUPP-61324 :biến enable cho phép bật tắt chức năng liên thông Hộ tịch DLK
      idCapCoQuanHanhChinh_HTTP: "0000591c4e1bd312a6f00003",
      idMappingTypeAgency_HTTP : "5fc0707a62681a8bef000018",
      agencyLevelId: {
        level1: "5f7dade4b80e603d5300dcc4",
        level2: "0000591c4e1bd312a6f00003",
        level3: "0000591c4e1bd312a6f00004"
      },
      ProvinceLevelId : "60b8b5979adb921904a02150", // ITTHC cấp tỉnh
      ProvinceDepartmentLevelId : "60b8b5a29adb921904a02151", //TTHC cấp phòng ban thuộc tỉnh
      DistrictLevelId : "5f39f4155224cf235e134c59", //TTHC cấp huyện
      CommueLevelId : "5febfe2295002b5c79f0fc9f",
      //rootAgencyId: "60b87fb59adb921904a0213e"
      rootAgencyId: "60c868a4289bad69c7cbffea", // mã UBND tỉnh
      changeTag: true // đổi nhãn
    },
    oneGateEformCatalogByAgency: false, // IGATESUPP-68449: cấu hình cách lấy biểu mẫu khi cấu hình quy trình. Gía trị: false chức năng như cũ lấy biểu mẫu từ dữ liệu trang vẽ biểu mẫu. Gía trị: true: lấy biểu mẫu từ danh mục biểu mẫu ở trang một cửa
    openEformAction: 'edit', // IGATESUPP-68449: giá trị: 'view' chỉ để xem, giá trị: 'edit' để vẽ điều chỉnh
    allowVGCASignIssue: false, //IGATESUPP-70364: biến cho phép ký số văn thư ban cơ yếu, dùng khác HCM
    additionalRequirementDate: false, // IGATESUPP-32088: ẩn hiện ngày tiếp nhận bổ sung
    limitLengthDocumentName: 750, // IGATESUPP-76568 Tăng độ dài lưu trữ của tên thành phần hồ sơ lên 5000 ký tự
    dossierReselectAgencyProcessing: { // IGATESUPP-74606: quy định việc hiển thị thông tin cấu hình và áp dụng chức năng chọn lại cơ quan đơn vị khi chuyển bước hồ sơ
      config: false, // config = true thì hiển thị thông tin nút check chọn nút cấu hình “Chọn lại ...”
      enable:false // enable = true thì áp dụng phần chọn lại cơ quan đơn vị trong popup chuyển bước hồ sơ
    },
    tuqnCheckSMSNVTC: false, // check lỗi không gửi sms nvtc
    fulfillFinancialObligationsTemplate: "",//tham số mẫu mặc đinh gửi sms nvtc
    enableSMSDateTemplate: false,
    showLogNotify: false, // tham số show log notify để check mẫu sms, email
    enableUseCurrentMunite: false,
    subsystemIdCto: "5f7c16069abb62f511880003", // IGATESUPP-110701
    // IGATESUPP-114129 - Bổ sung thông báo trên app xử lý TTHC
    reportOfficerNotificationTypeId: "67b67fffd2166d08c50cd61c",
    digitalToPaper: {
      enable: false,
      textConvert: 'Xuat tai Cong dich vu cong truc tuyen TP. Ho Chi Minh - Website https://dichvucong.hochiminhcity.gov.vn', //Thông tin text Xuất tại PDF chuyển đổi bảng ký giấy lưu ý chỉ ghi tiếng viết ko dấu
    }, //thuongld-IGATESUPP-82538-[HCM iGATE V2] Offsite - Đánh giá hệ thống TTQGTTHC theo VB 473 mục 18.7 [SD17223]
	formEform: { //IGATESUPP-80387 Chức năng chuyển đổi biểu mẫu điện tử trước khi ký thành các định dạng tập tin phổ biến như: .pdf, .doc, .docx, .xml, .json
      enableExportJSON: false,
      enableExportXML: false
    },
    zaloResultReturnTitle:{ // title zalo trả về bước trả kết quả
      enable:false,
      title: "Đã trả kết quả"
    },
    resetSendWhenLeavePage: false, //reset send object when leave the page,
    zaloAdditionalRequirementReason: false, //insert reason to message,
    zaloPaymentReuirementNextTask: {
      enable: false,
      nextTask: "Yêu cầu thanh toán"
    },
    zaloOnlineRecieveingMaunualSendzaloDiable: false,
    zaloUseNodeStatus:false,// send node status zalo

    ChangeTimesheetDossierCalculation:{ // thay đổi cách lấy hạn hồ sơ sẽ lấy từ DB thay vì gọi API timesheet
      dueDateTaskProcessing: { // thay đối cách lấy hạn từng bước ở menu xử lý
        enable: false,
        exceptionAgency: [ // loại trừ cơ quan, nghiệp vụ đặc thù

        ]
      },
      dueDateProcessing: false, // thay đối cách lấy hạn toàn trình ở menu xử lý
      dueDateProcessingDetail: false,  // thay đối cách lấy hạn toàn trình ở menu chi tiết hồ sơ (xử lý)
      dueDateSearch: false, // thay đối cách lấy hạn toàn trình ở menu chi tiết hồ sơ (tra cứu toàn cơ quan)
      dueDateProcess: false, // thay đối cách lấy hạn toàn trình ở giao diện xem chi tiết quy trình
    },
    hideFormFileNullDetail: false, // IGATESUPP-84342: Ẩn giấy tờ không có loại bản
    dossierStatus: [
      {
        id: 0,
        vi: 'Chờ tiếp nhận',
        en: 'Waiting for receiving'
      },
      {
        id: 1,
        vi: 'Chờ bổ sung',
        en: 'Waiting for update'
      },
      {
        id: 2,
        vi: 'Đang xử lý',
        en: 'Inprogress'
      },
      {
        id: 3,
        vi: 'Đang tạm dừng',
        en: 'Pending'
      },
      {
        id: 4,
        vi: 'Có kết quả',
        en: 'Had result'
      },
      {
        id: 5,
        vi: 'Đã trả kết quả',
        en: 'Result returned'
      },
      {
        id: 6,
        vi: 'Đã hủy',
        en: 'Đã hủy'
      },
      {
        id: 8,
        vi: 'Chờ phê duyệt bổ sung',
        en: 'Waiting for approval to addtition'
      },
      {
        id: 9,
        vi: 'Chờ phê duyệt tạm dừng',
        en: 'Waiting for approval to pause'
      },
      {
        id: 10,
        vi: 'Chờ phê duyệt gia hạn',
        en: 'Waiting for approval to extend'
      },
      {
        id: 11,
        vi: 'Chờ phê duyệt dừng xử lý',
        en: 'Waiting for approval to cancel'
      },
      {
        id: 12,
        vi: 'Dừng xử lý',
        en: 'Cancel processing'
      },
      {
        id: 13,
        vi: 'Yêu cầu rút hồ sơ',
        en: 'Request for withdraw'
      },
      {
        id: 14,
        vi: 'Yêu cầu thanh toán',
        en: 'Payment request'
      },
      {
        id: 15,
        vi: 'Đã thanh toán',
        en: 'Paid'
      },
      {
        id: 16,
        vi: 'Đã thanh toán và đang xử lý',
        en: 'Paid in process'
      },
      {
        id: 17,
        vi: 'Đã thông báo thanh toán trực tiếp',
        en: 'Sent directly payment notification'
      },
      {
        id: 19,
        vi: 'Từ chối từ chuyên nghành',
        en: 'Specialization refused'
      },
      {
        id: 20,
        vi: 'Đã xác nhận thanh toán',
        en: 'Payment confirmed'
      },
      {
        id: 21,
        vi: 'Yêu cầu nộp hồ sơ bản giấy',
        en: 'Request to submit paper application'
      }
    ],
    // IGATESUPP-114129 - Bổ sung thông báo trên app xử lý TTHC
    OS_CTO:{
      enableNotarizationRepository: 0, //IGATESUPP-126962
      stepNotifyAppTemplate: {
        templateId: "67bd6082b99321071076b8ca",
      }
    }
  };

  isHiddenDateEmpty: false; //IGATESUPP: Thong ke linh vuc
  goBackTrueSite = false ; // IGATESUPP-108096 [AGG][iGate][2.0][GẤP] Lỗi khi trở về trang trước - site một cửa
  menuSpecificforBNV = false;
  noRequirementFormFile = false ;  //IGATESUPP-105216 [GLI][IGATE2.0] lỗi tiếp nhận không chọn TPHS nhưng khi tiếp nhận xong mặc định có tất cả tphs
  paymentOfFinancialObligations = false;
  statusIdSyncNVTC = "631e4a0967411b0b0d000003";



  procedureLedgerAuth: []; //KGG Chung Thuc Dien Tu
  showAbstractConstructionUnitEnable : 0; //IGATESUPP-98928  Sở GTVT - Thêm tên trích yếu hiển thị ở thông tin hồ sơ
  showAbstractConstructionUnitAgencyIds : [];  //IGATESUPP-98928  Sở GTVT - Thêm tên trích yếu hiển thị ở thông tin hồ sơ
  isBLU : false;// bao cao tong hop 60NĐ blu
  baoCaoTongHopBLU=
  {
    noResultContent: [],
    baoCaoTagId: '66cc2e5409f4a91e7f1cb5a3',
    is06cTonghop: false,
    ubndHuyenArray: ['64587a1095f7507a6bf81ad8','64587a1195f7507a6bf81ada', '64587a1195f7507a6bf81ae0', '64587a1195f7507a6bf81ae4', '64587a1195f7507a6bf81adc', '64587a1195f7507a6bf81ae2', '64587a1195f7507a6bf81ade'],
    role: 'oneGate6cTongHopAdmin',
    FreeMeansNoFee:false
  };

  changeTextStatisticDBN :''; //IGATESUPP-97003 - DBN_Igate 2 Sửa text Trễ hạn -> Quá hạn trên các biểu thống kê báo cáo
  FeeHoTich: false; // IGATESUPP-96538 - Điều chỉnh báo cáo trực tuyến theo NQ 02 của HĐND tỉnh
  isIgnoreFreeDossier: boolean = false; // IGATESUPP-107857 - Điều chỉnh báo cáo trực tuyến lọc bỏ những hồ sơ miễn phí
  showContentAdditionalRequest: false;
  showPrintTemplateComponentInRefuseDossierPopup = false; // IGATESUPP-120558 - Điều chỉnh thêm chức năng in phiếu ở popup từ chối của trang hồ sơ chờ tiếp nhận
  reportReceivedAgencyLevelConfig = [
    { id: '5f6b17984e1bd312a6f3ae4b', name: 'Cấp Tỉnh', level: 1 },
    { id: '5f7dade4b80e603d5300dcc4', name: 'Cấp Quận/Huyện', level: 2 },
    { id: '5f6b177a4e1bd312a6f3ae4a', name: 'Cấp Phường/Xã', level: 3 }
  ];
  procedureVpcLevelConfig = [
    { id: '5f5b2c2b4e1bd312a6f3ae23', name: 'Không trực tuyến', level: 2 },
    { id: '5f5b2c4b4e1bd312a6f3ae24', name: 'Một phần', level: 3 },
    { id: '5f5b2c564e1bd312a6f3ae25', name: 'Toàn trình', level: 4 }
  ];
  interfaceWorkHorizontal: 0;
  functionAppraisalDossier: false; //IGATESUPP-93353 - hiển thị checkbox Thẩm xét hồ sơ ở bước cấu hình bước quy trình

  releaseReceiptViettelSOAPFormat : false;  //phucnh.it2-IGATESUPP-88827: Tích hợp phát hành biên lai Viettel kiểu Soap, in HCM, = true bật chức năng, false: off chức năng
  vietTelSupplierIdHCM:"6540a2e4881c9f480ddb9d8d"; //phucnh.it2-IGATESUPP-88827: loại phát hành biên lai = Viettel
  showsInformationAddressReceiptVT: 0; //phucnh.it2-IGATESUPP-100348: cho phép bỏ hiển thị data undefined trong dia chỉ khi phát hành biên lai Viettel
  disableUpdatePaymentStatus: 0;                          //phucnh.it2-IGATESUPP-114826: Khóa chức năng cập nhật lệ phí khi yêu cầu thanh toán hồ sơ
  disableUpdatePaymentStatusAgencyIds: [];                 //phucnh.it2-IGATESUPP-114826: Tham số áp dụng chức năng cho đơn vị
  isShowHcmMDDCDTCheckBox : false; //IGATESUPP-93791 Tích hợp phần mềm dữ liệu chuyên ngàn

  editLinkFileTBXH : false; //IGATESUPP-94606 update link file đồng bộ qua Bộ LĐTBXH
  isHcmCTDT : false ; //IGATESUPP-104417 [HCM iGATE V2] Offsite - Lỗi mất TPHS sau khi đồng bộ qua PM chứng thực [SD18707]
  hideInvoiceAndPOSProcessingDossier: false; //IGATESUPP-107537 VNPT DNI - IGate 3.0 Ẩn 3 menu thanh toán qua máy POS
  notifyqni =
    {
      enableConfigDefault: false,
      agencyLevelId: '5f39f42d5224cf235e134c5a',
      configDepartment:
      {
        applyOnline:
        {
          sms:
          {
            defaultSend: true,
            id: '632a7c76a577bc63db57695f',
            name: '1.TTHCC - Mẫu tin nhắn gửi yêu cầu - Người dân - Nộp hồ sơ online',
            templateId: ''
          },
          gmail:
          {
            defaultSend: true,
            id: null,
            name: '',
            templateId: ''
          },
          zalo:
          {
            defaultSend: true,
            id: null,
            name: '',
            templateId: ''
          }
        },
        additionalRequirement:
        {
          sms:
          {
            defaultSend: true,
            id: '632a7ccca577bc63db5770b2',
            name: '2. TTHCC - Mẫu tin nhắn gửi yêu cầu - Người dân - Yêu cầu bổ sung',
            templateId: ''
          },
          gmail:
          {
            defaultSend: true,
            id: null,
            name: '',
            templateId: ''
          },
          zalo:
          {
            defaultSend: true,
            id: null,
            name: '',
            templateId: ''
          }
        },
        receptionOnline:
        {
          sms:
          {
            defaultSend: true,
            id: '632a7d35a577bc63db57735d',
            name: '3. TTHCC - Mẫu tin nhắn gửi yêu cầu - Người dân - Tiếp nhận hồ sơ online',
            templateId: ''
          },
          gmail:
          {
            defaultSend: true,
            id: null,
            name: '',
            templateId: ''
          },
          zalo:
          {
            defaultSend: true,
            id: null,
            name: '',
            templateId: ''
          }
        },
        suspendDossier:
        {
          sms:
          {
            defaultSend: true,
            id: '632a7da3a577bc63db577674',
            name: '4. TTHCC - Mẫu tin nhắn gửi yêu cầu - Người dân - Tạm dừng hồ sơ',
            templateId: ''
          },
          gmail:
          {
            defaultSend: true,
            id: null,
            name: '',
            templateId: ''
          },
          zalo:
          {
            defaultSend: true,
            id: null,
            name: '',
            templateId: ''
          }
        },
        resumeDossier:
        {
          sms:
          {
            defaultSend: true,
            id: '632a7e0aa577bc63db577979',
            name: '5. TTHCC - Mẫu tin nhắn gửi yêu cầu - Người dân - Tiếp tục hồ sơ',
            templateId: ''
          },
          gmail:
          {
            defaultSend: true,
            id: null,
            name: '',
            templateId: ''
          },
          zalo:
          {
            defaultSend: true,
            id: null,
            name: '',
            templateId: ''
          }
        },
        receptionReceiving:
        {
          sms:
          {
            defaultSend: true,
            id: '632a7e5ba577bc63db577f3f',
            name: '6. TTHCC - Mẫu tin nhắn gửi yêu cầu - Người dân - Tiếp nhận hồ sơ trực tiếp',
            templateId: ''
          },
          gmail:
          {
            defaultSend: true,
            id: null,
            name: '',
            templateId: ''
          },
          zalo:
          {
            defaultSend: true,
            id: null,
            name: '',
            templateId: ''
          }
        },
        extendProcessingDossier:
        {
          sms:
          {
            defaultSend: true,
            id: '632a7c10a577bc63db576683',
            name: '7. TTHCC - Mẫu tin nhắn gửi yêu cầu - Người dân - Yêu cầu gia hạn',
            templateId: ''
          },
          gmail:
          {
            defaultSend: true,
            id: null,
            name: '',
            templateId: ''
          },
          zalo:
          {
            defaultSend: true,
            id: null,
            name: '',
            templateId: ''
          }
        },
        requestWithdrawDossier:
        {
          sms:
          {
            defaultSend: true,
            id: '632a7f53a577bc63db5784e1',
            name: '8. TTHCC - Mẫu tin nhắn gửi yêu cầu - Người dân - Yêu cầu rút hồ sơ',
            templateId: ''
          },
          gmail:
          {
            defaultSend: true,
            id: null,
            name: '',
            templateId: ''
          },
          zalo:
          {
            defaultSend: true,
            id: null,
            name: '',
            templateId: ''
          }
        },
        cancelDossierCitizen:
        {
          sms:
          {
            defaultSend: true,
            id: '632a7fbaa577bc63db57896b',
            name: '9. TTHCC - Mẫu tin nhắn gửi yêu cầu - Người dân - Dừng xử lý hồ sơ',
            templateId: ''
          },
          gmail:
          {
            defaultSend: true,
            id: null,
            name: '',
            templateId: ''
          },
          zalo:
          {
            defaultSend: true,
            id: null,
            name: '',
            templateId: ''
          }
        },
        paymentRequestDossier:
        {
          sms:
          {
            defaultSend: true,
            id: '632a8000a577bc63db578b47',
            name: '10. TTHCC - Mẫu tin nhắn gửi yêu cầu - Người dân - Yêu cầu thanh toán',
            templateId: ''
          },
          gmail:
          {
            defaultSend: true,
            id: null,
            name: '',
            templateId: ''
          },
          zalo:
          {
            defaultSend: true,
            id: null,
            name: '',
            templateId: ''
          }
        },
        returnResultDossier:
        {
          sms:
          {
            defaultSend: true,
            id: '632a803ca577bc63db578f1c',
            name: '11. TTHCC - Mẫu tin nhắn gửi yêu cầu - Người dân - Trả kết quả',
            templateId: ''
          },
          gmail:
          {
            defaultSend: true,
            id: null,
            name: '',
            templateId: ''
          },
          zalo:
          {
            defaultSend: true,
            id: null,
            name: '',
            templateId: ''
          }
        },
        additionalRequirementApprove:
        {
          sms:
          {
            defaultSend: true,
            id: '632a7ccca577bc63db5770b2',
            name: '2. TTHCC - Mẫu tin nhắn gửi yêu cầu - Người dân - Yêu cầu bổ sung',
            templateId: ''
          },
          gmail:
          {
            defaultSend: true,
            id: null,
            name: '',
            templateId: ''
          },
          zalo:
          {
            defaultSend: true,
            id: null,
            name: '',
            templateId: ''
          }
        },
        extendProcessingDossierApprove:
        {
          sms:
          {
            defaultSend: true,
            id: '632a7c10a577bc63db576683',
            name: '7. TTHCC - Mẫu tin nhắn gửi yêu cầu - Người dân - Yêu cầu gia hạn',
            templateId: ''
          },
          gmail:
          {
            defaultSend: true,
            id: null,
            name: '',
            templateId: ''
          },
          zalo:
          {
            defaultSend: true,
            id: null,
            name: '',
            templateId: ''
          }
        },
        suspendDossierApprove:
        {
          sms:
          {
            defaultSend: true,
            id: '632a7da3a577bc63db577674',
            name: '4. TTHCC - Mẫu tin nhắn gửi yêu cầu - Người dân - Tạm dừng hồ sơ',
            templateId: ''
          },
          gmail:
          {
            defaultSend: true,
            id: null,
            name: '',
            templateId: ''
          },
          zalo:
          {
            defaultSend: true,
            id: null,
            name: '',
            templateId: ''
          }
        },
        cancelDossierApprove:
        {
          sms:
          {
            defaultSend: true,
            id: '632a7fbaa577bc63db57896b',
            name: '9. TTHCC - Mẫu tin nhắn gửi yêu cầu - Người dân - Dừng xử lý hồ sơ',
            templateId: ''
          },
          gmail:
          {
            defaultSend: true,
            id: null,
            name: '',
            templateId: ''
          },
          zalo:
          {
            defaultSend: true,
            id: null,
            name: '',
            templateId: ''
          }
        }
      },
      configDefault:
      {
        applyOnline:
        {
          sms:
          {
            defaultSend: true,
            id: '616f923399966aeafbd00007',
            name: 'Mẫu tin nhắn dừng xử lý hồ sơ - Công dân',
            templateId: ''
          },
          gmail:
          {
            defaultSend: true,
            id: null,
            name: '',
            templateId: ''
          },
          zalo:
          {
            defaultSend: true,
            id: null,
            name: '',
            templateId: ''
          }
        },
        additionalRequirement:
        {
          sms:
          {
            defaultSend: true,
            id: '616f923399966aeafbd00008',
            name: 'Mẫu tin nhắn yêu cầu bổ sung -  Công dân',
            templateId: ''
          },
          gmail:
          {
            defaultSend: true,
            id: null,
            name: '',
            templateId: ''
          },
          zalo:
          {
            defaultSend: true,
            id: null,
            name: '',
            templateId: ''
          }
        },
        receptionOnline:
        {
          sms:
          {
            defaultSend: true,
            id: '616f923399966aeafbd00009',
            name: 'Mẫu tin nhắn tiếp nhận hồ sơ online - Công dân',
            templateId: ''
          },
          gmail:
          {
            defaultSend: true,
            id: null,
            name: '',
            templateId: ''
          },
          zalo:
          {
            defaultSend: true,
            id: null,
            name: '',
            templateId: ''
          }
        },
        suspendDossier:
        {
          sms:
          {
            defaultSend: true,
            id: '616f923399966aeafbd00010',
            name: 'Mẫu tin nhắn tạm dừng hồ sơ - Công dân',
            templateId: ''
          },
          gmail:
          {
            defaultSend: true,
            id: null,
            name: '',
            templateId: ''
          },
          zalo:
          {
            defaultSend: true,
            id: null,
            name: '',
            templateId: ''
          }
        },
        resumeDossier:
        {
          sms:
          {
            defaultSend: true,
            id: '616f923399966aeafbd00011',
            name: 'Mẫu tin nhắn tiếp tục hồ sơ - Công dân',
            templateId: ''
          },
          gmail:
          {
            defaultSend: true,
            id: null,
            name: '',
            templateId: ''
          },
          zalo:
          {
            defaultSend: true,
            id: null,
            name: '',
            templateId: ''
          }
        },
        receptionReceiving:
        {
          sms:
          {
            defaultSend: true,
            id: '616f923399966aeafbd00012',
            name: 'Mẫu tin nhắn tiếp nhận trực tiếp - Công dân',
            templateId: ''
          },
          gmail:
          {
            defaultSend: true,
            id: null,
            name: '',
            templateId: ''
          },
          zalo:
          {
            defaultSend: true,
            id: null,
            name: '',
            templateId: ''
          }
        },
        extendProcessingDossier:
        {
          sms:
          {
            defaultSend: true,
            id: '616f923399966aeafbd00013',
            name: 'Mẫu tin nhắn gia hạn hồ sơ - Công dân',
            templateId: ''
          },
          gmail:
          {
            defaultSend: true,
            id: null,
            name: '',
            templateId: ''
          },
          zalo:
          {
            defaultSend: true,
            id: null,
            name: '',
            templateId: ''
          }
        },
        requestWithdrawDossier:
        {
          sms:
          {
            defaultSend: true,
            id: '616f923399966aeafbd00014',
            name: 'Mẫu tin nhắn yêu cầu rút hồ sơ - Công dân',
            templateId: ''
          },
          gmail:
          {
            defaultSend: true,
            id: null,
            name: '',
            templateId: ''
          },
          zalo:
          {
            defaultSend: true,
            id: null,
            name: '',
            templateId: ''
          }
        },
        cancelDossierCitizen:
        {
          sms:
          {
            defaultSend: true,
            id: '616f923399966aeafbd00031',
            name: 'Mẫu tin nhắn dừng xử lý hồ sơ',
            templateId: ''
          },
          gmail:
          {
            defaultSend: true,
            id: null,
            name: '',
            templateId: ''
          },
          zalo:
          {
            defaultSend: true,
            id: null,
            name: '',
            templateId: ''
          }
        },
        paymentRequestDossier:
        {
          sms:
          {
            defaultSend: true,
            id: '632a8000a577bc63db578b47',
            name: '10. TTHCC - Mẫu tin nhắn gửi yêu cầu - Người dân - Yêu cầu thanh toán',
            templateId: ''
          },
          gmail:
          {
            defaultSend: true,
            id: null,
            name: '',
            templateId: ''
          },
          zalo:
          {
            defaultSend: true,
            id: null,
            name: '',
            templateId: ''
          }
        },
        returnResultDossier:
        {
          sms:
          {
            defaultSend: true,
            id: '62aaf9534ff4b2672ba8ab80',
            name: 'Mẫu tin nhắn trả kết quả cho người dân',
            templateId: ''
          },
          gmail:
          {
            defaultSend: true,
            id: null,
            name: '',
            templateId: ''
          },
          zalo:
          {
            defaultSend: true,
            id: null,
            name: '',
            templateId: ''
          }
        },
        additionalRequirementApprove:
        {
          sms:
          {
            defaultSend: true,
            id: '63294863a577bc63db54f56a',
            name: 'Mẫu tin nhắn gửi yêu cầu - Người dân - Yêu cầu bổ sung',
            templateId: ''
          },
          gmail:
          {
            defaultSend: true,
            id: null,
            name: '',
            templateId: ''
          },
          zalo:
          {
            defaultSend: true,
            id: null,
            name: '',
            templateId: ''
          }
        },
        extendProcessingDossierApprove:
        {
          sms:
          {
            defaultSend: true,
            id: '6329cd73a577bc63db569634',
            name: 'Mẫu tin nhắn gửi yêu cầu - Người dân - Yêu cầu gia hạn',
            templateId: ''
          },
          gmail:
          {
            defaultSend: true,
            id: null,
            name: '',
            templateId: ''
          },
          zalo:
          {
            defaultSend: true,
            id: null,
            name: '',
            templateId: ''
          }
        },
        suspendDossierApprove:
        {
          sms:
          {
            defaultSend: true,
            id: '6329caeda577bc63db569449',
            name: 'Mẫu tin nhắn gửi yêu cầu - Người dân - Yêu cầu tạm dừng',
            templateId: ''
          },
          gmail:
          {
            defaultSend: true,
            id: null,
            name: '',
            templateId: ''
          },
          zalo:
          {
            defaultSend: true,
            id: null,
            name: '',
            templateId: ''
          }
        },
        cancelDossierApprove:
        {
          sms:
          {
            defaultSend: true,
            id: '6329c87ea577bc63db5691a0',
            name: 'Mẫu tin nhắn gửi yêu cầu - Người dân - Yêu cầu dừng xử lý',
            templateId: ''
          },
          gmail:
          {
            defaultSend: true,
            id: null,
            name: '',
            templateId: ''
          },
          zalo:
          {
            defaultSend: true,
            id: null,
            name: '',
            templateId: ''
          }
        }
      }
    }

  tooltipReportGeneral = "";

  notifykha =
  {
      enableConfigDefault: false,
      agencyLevelId: '5f39f42d5224cf235e134c5a',
      configDefault:
      {
        applyOnline:
        {
          sms:
          {
            defaultSend: true,
            id: '616f923399966aeafbd00007',
            name: 'Mẫu tin nhắn dừng xử lý hồ sơ - Công dân',
            templateId: ''
          }
        },

      }
    }

  chungThucDienTu = {
    BAT_LIEN_THONG: 0, // 0 tắt liên thông, 1 bật liên thông
    BAT_CHECK_QT_CT: 0, // 0 tắt, 1 bật check chọn quy trình chứng thực để cấu hình quy trình thủ tục
    validateEmail: 0, //  0 tắt, 1 bật yêu cầu bắt buộc phải nhập email với thủ tục chức thực điện tử
    validateFile: 0, //  0 tắt, 1 bật yêu cầu bắt buộc phải có file với thủ tục thực thực điện tử
    validateFilePdf: 0, // 0 tắt, 1 bật yêu cầu bắt buộc định dạng file chứng thực là PDF
    validateAuthType: 0, // 0 tắt, 1 cảnh báo khi không có giấy tờ nào cần chứng thực
    validateAuthTypeContent: "Không có thành phần hồ sơ cần chứng thực, hồ sơ sẽ không được chuyển qua hệ thống Chứng thực điện tử. Chọn 'Đồng ý' để tiếp tục tiếp nhận hồ sơ.", // Nội dung thông báo
    domain_chungthuc: "https://test-ctdt.vnpt.vn/web/login.jsp", //domain đăng nhập của hệ thống chứng thực (do hệ thống CTDT cung cấp)
    configId: "6501697367555d50c4e4b47a", // id của dịch vụ chứng thực điện tử (site Quản trị tích hợp)
    showStatus: 0, // 0 tắt, 1 bật hiển thị trạng thái chứng thực ở các menu
    showGridResult: 0, // 0 tắt, 1 bật table kết quả chứng thực tại màn hình chi tiết hồ sơ
    showBtnReSync: 0, // 0 tắt, 1 bật nút Đồng bộ lại chứng thực điện tử
	  showBtnReSyncResult: 0, // 0 tắt, 1 bật nút Đồng bộ lại kết quả chứng thực điện tử
    showBtnSaveToResultFile: 0, // 0 tắt, 1 bật nút Lưu file vào kết quả xử lý
    noticePaymentResult: 0, // 0 tắt, 1 yêu cầu thanh toán trước khi kết thúc
    rootAgency: {
      code: "000.00.00.H22" // Mã CoQuanChuQuan: Theo mã của DVCQG
    },
    defaultAuthTypeId: 0, // Loại chứng thực mặc định
    updateFeeContent: "Đã trả kết quả, hệ thống Chứng thực điện tử sẽ gửi kết quả cho người dân!", // Ở bước kết thúc, thực hiện update-fee để hệ thống CTDT gửi email cho người dân
    authenticationType: [ // Danh sách loại chứng thực
      {
        id: 0,
        name: "Chưa chứng thực"
      },
      {
        id: 1,
        name: "Chứng thực điện tử"
      },
      {
        id: 2,
        name: "Chứng thực giấy"
      },
      {
        id: 3,
        name: "Cả 2 loại"
      }
    ],
    authenticationStatus: [ // Danh sách trạng thái
      {
        id: 0,
        name: "Chưa đồng bộ chứng thực"
      },
      {
        id: 1,
        name: "Đồng bộ chứng thực thành công"
      },
      {
        id: 2,
        name: "Đồng bộ chứng thực thất bại"
      },
      {
        id: 3,
        name: "Cán bộ chứng thực yêu cầu bổ sung"
      },
      {
        id: 4,
        name: "Cán bộ chứng thực từ chối hồ sơ"
      },
      {
        id: 5,
        name: "Đã đồng bộ lại chứng thực hồ sơ yêu cầu bổ sung"
      },
      {
        id: 6,
        name: "Cán bộ chứng thực chuyển bước trả kết quả"
      }
    ],
    authStatusComplete: [ // Danh sách trạng thái hoàn thành chứng thực (Theo id)
      3,
      4,
      6
    ],
    authStatusCanReSync: [ // Danh sách trạng thái có thể hiện thị nút Đồng bộ lại chứng thực điện tử (Theo id)
      1,
      2,
      3,
      4
    ],
    enableShowCopyNumberElectronicAuthentication: 0, // IGATESUPP-116561 Hỗ trợ bổ sung trường thông tin SỐ BẢN đối với hồ sơ chứng thực điện tử
    enableJustGetCheckedFileAuthen: 0 //IGATESUPP-128788 Kiểm tra luồng đồng bộ hồ sơ chứng thực khi tiếp nhận ở menu "Hồ sơ chờ tiếp nhận"
  }

  header = {
    hideLanguage: false, //IGATESUPP-66850 [HCM iGATE V2] Offsite _ ĐÁNH GIÁ GÓP Ý UI/UX [SD 2852]
  }

  footer = {
    //IGATESUPP-76021 - [iGate2.0][QNI] - Gắn tín nhiệm mạng cho trang motcua
    isShowNetworkTrust: false,
    linkIpv6: "",
    LogoIpv6: "",
    linkATTT: "",
    LogoATTT: "",
    //-----------------------------------------------------------------------
  }
  dashboardAgencyTagName = "Cấp Tỉnh,Cấp Sở,Cấp Huyện,Cấp Xã,Cấp Xã/Phường/Thị trấn";

  cloneDossier = { // IGATESUPP-85860: [BTTTT] - Thực hiện chức năng clone hồ sơ trên Bộ Thông tin và Truyền thông
    procedureConfig: false,  // bât/tắt checkbox clone HS ở TTHC
    checkboxName: "Sao chép thông tin hồ sơ cũ", // tên hiển thị ở checkbox TTHC
    applyOnlineConfig: {
      enable: false,  // bât/tắt chức năng clone HS ở menu tiếp nhận HS trực tuyến
      maxClone: 10,  // số HS tối đa mỗi lần clone
      buttonName: "Sao chép hồ sơ" // tên nút hiển thị ở menu
    },
    receptionConfig: {
      enable: false,  // bât/tắt chức năng clone HS ở menu tiếp nhận HS trực tiếp
      buttonName: "Tiếp nhận và sao chép" // tên nút hiển thị
    }
  }
  notReceiveStopProcessing = false  // IGATESUPP-86673: hiện HS dừng xử lý ở menu không cần xử lý
  logBookPrintFormatter = false //IGATESUPP-91115:[BLU|iGate 2.0] Điều chỉnh chức năng "Thống kê sổ theo dõi"

  //new config v2
  newConfigV2 = {
    enableProcedureConfigLLTPVNeID: false, //IGATESUPP-87040 đồng bộ hồ sơ LLTP VNeID
    showDossierProgress: false, // IGATESUPP-88225: [BLU|iGate] Hỗ trợ kiểm tra bước cấu hình 0 giờ ghi nhận quá hạn
	  viewPhotoKiosk: false,  //IGATESUPP-87708 [DEV]- Thực hiện tích hợp camera trên Kiosk
    offSendZaloOfficer: true, // Mặc định chức năng zalo hiện tại không gửi cho cán bộ
    showAssignee: false,  // IGATESUPP-88540: hiển thị cán bộ đang xử lý trong menu "Tra cứu hồ sơ theo đơn vị"
    enableTransferMultiDossierAuto: false, // IGATESUPP-84284: Chuyển nhiều hồ sơ tự động
    numberTransferAuto: 10, // IGATESUPP-84284: Giới hạn số lượng hồ sơ chuyển tự động
    fixDelayTransferAuto: 1000, // IGATESUPP-84284: Giới hạn thời gian chuyển từng hồ sơ tự động
    enableTransferAutoEMC: true, // IGATESUPP-84284: Bật/tắt đẩy EMC
    enableTransferAutoEMCLog: true, // IGATESUPP-84284: Bật/tắt lưu log EMC
    showSearchAgencyTreeStatistic: false, // triph.hg-IGATESUPP-63370-62944 tham số hiển thị tìm kiếm cơ quan dạng tree
    listApplyMethodStatistic : [
      { id: "", name: "Tất cả"},
      { id: "1", name: "Trực tiếp"},
      { id: "0", name: "Trực tuyến"}
    ], // triph.hg-IGATESUPP-63370-62944 tham số hình thức tiếp nhận báo cáo thống kê
    processingMenuSearchByAgency: false, // IGATESUPP-91905 menu Xử lý hồ sơ lấy theo đơn vị
    fillMoreEformDetail: false, // IGATESUPP-94437: [HGI-IGATE] Nhờ hỗ trợ kết nối thông tin người thân từ CSDLQG vào thông tin chung"
    isViewProcessingNormal: false, //IGATESUPP-94703 điều chỉnh giao diện xử lý hồ sơ đơn giản lại
    defaultFormIO: {
      enable: false,
      eForm: '1',
      applicantEForm: '2'
    }, // Bổ sung tham số cấu hình form tiếp nhận mặc định
    isEnableButtonPause: true, // IGATESUPP-94142 tham số ẩn/hiện nút Tạm dừng menu Xử lý hồ sơ
    sortByConfig: 0, // IGATESUPP-91297 sắp xếp hồ sơ toàn cơ quan
    enableButtonSyncSector: false, // IGATESUPP-91412 Nút đẩy lĩnh vực qua reporter hiển thị ở cây cơ quan site DVC
    showSectorOnlineReception: false, // IGATESUPP-89774 [IGATE2.0]-GIALAI:Hỗ trợ phân lĩnh vực cho sở công thương
    showAgencyByParent: false, // IGATESUPP-88659 Danh sách Cơ quan, đơn vị tại chức năng cấu hình phiếu động
    viewFullAgency: false,  // IGATESUPP-98935: HGI - Hiện đầy đủ danh sách các cơ quan đã phân lĩnh vực (Tab Phân lĩnh vực cho cơ quan)
    transferPaperRequestForAdditionalFeeType: {
      id: "66f4b2100000000000000b03",
      name: [
        {
          languageId: 228,
          name: "Phí vận chuyển hồ sơ giấy yêu cầu bổ sung"
        },
        {
          languageId: 46,
          name: "Fee transfer paper request for additional at Administrative Center"
        }
      ],
      quantityEditable: "0",
      type: "1"
    }, //  tuqn-IGATESUPP-96418 6.12. Bổ sung chức năng yêu cầu bổ sung đối với hồ sơ HCC một cấp
    showButtonReceivingDossier5343: false, //  tuqn-IGATESUPP-96418 6.12. Bổ sung chức năng yêu cầu bổ sung đối với hồ sơ HCC một cấp
    showDossierByUserSector: false, // IGATESUPP-97355 Hiển thị hồ sơ theo lĩnh vực của cán bộ
    searchMappingDataType5343:{
      province: "5fc0707a62681a8bef000004",
      district : "5fc0707a62681a8bef000005"
    },
    tagAgency: "0000591c4e1bd312a6f00003", //IGATESUPP-96420 Bổ sung menu Tra cứu hồ sơ HCC một cấp
    listVNpostStatus : [
      {id: -2, value : "Chờ xác nhận đơn hàng"},
      {id: 100, value : "Thêm đơn hàng thành công"},
      {id: 101, value : "Thêm đơn hàng thất bại"},
      {id : 102, value : "Nhận hàng thành công"},
      {id : 130, value : "Giao hàng thành công"},
      {id : 131, value : "Giao hàng thất bại"},
      {id : 132, value : "Trả lại hàng"},
      {id : 21, value : "Hủy"}
    ],//IGATESUPP-96420 Bổ sung menu Tra cứu hồ sơ HCC một cấp
    showButtonProcessingDossier5343: false, //  thoaidt-IGATESUPP-96416 6.10. Điều chỉnh luồng xử lý hồ sơ ở cán bộ chuyên môn
    vnpost5343IdCoKetQua: "670395a854fa3cea2f088889", //  thoaidt-IGATESUPP-96416 6.10. Điều chỉnh luồng xử lý hồ sơ ở cán bộ chuyên môn
    checkAddAgencyRoot: true,
    changeAgencyOnlineReceiving: true, // đổi cơ quan tiếp nhận ở menu chờ tiếp nhận thành cơ quan của thủ tục 5343
    offSearchRemindId: false, //tắt khong tìm theo remind cho menu chờ tiếp nhận 5343
    checkComponentConfirmSendSubject: false, // IGATESUPP-103732 HPG IGATE 2: Kiểm tra email/tin nhắn từ chối tới công dân	(lỗi gửi)
    showDueTask: 0, // IGATESUPP-104673 Thực hiện bổ sung thông tin thời gian xử lý của bước
    showTimeRemainingTask: 0, // IGATESUPP-104673 Thực hiện bổ sung thông tin thời gian xử lý của bước
    qbhSetDefaultAttachment: false,
    enableStatisticsOfCitizenV2: false,  // IGATESUPP-102350: sử dụng tra cứu CSDLDCQG v2
    validTimesheetCSDLDCQG: '60d9efd03dfc9609723e3539',  // IGATESUPP-102350: timesheet tra cứu CSDLDCQG hợp lệ
    qtiShowBtnExtendTime: false, // IGATESUPP-105999 QTI tham số ẩn/hiện nút Gia hạn hồ sơ vừa tiếp nhận
    isHideCancelDossierGLI:false, //IGATESUPP-107334 [Igate_GLI] - Thống kê lệ phí hồ sơ có cả hồ sơ hủy
    listAgencyLevelIdCSDLDCLimit: [], //IGATESUPP-107091 [BLU|iGate] Thông tin tra cứu CSDLQGvDC vẫn hiển thị đủ sau khi cấu hình giới hạn
    enableUpdateCommonCSDLDCLimit: true, //IGATESUPP-107091 cho phép cấu hình giới hạn CSDLQGvDC thủ tục dùng chung
    nameMenuNavBNV:{
      nameDossierRecepOnl:"Hồ sơ chờ tiếp nhận",
      nameDossierRecepOnlTrans:"Dossier waiting to be received",
      nameDossierRecepDirect:"Tiếp nhận hồ sơ",
      nameDossierRecepDirectTrans:"Dossier reception"
    },
    showViewFileTPHS: false, //IGATESUPP-108804 nút xem file nhanh của tab Thành phần hồ sơ ở Menu Tiếp nhận hồ sơ
    endProcessMultiDossierErrorVBDLIS: false, //IGATESUPP-110161 Kiểm tra nút kết thúc nhiều hồ sơ bị lỗi currentTask lỗi
    enableSectorOfSynchronizeProcedure: false, //IGATESUPP-112413 Hỗ trợ bổ sung chức năng tìm kiếm thủ tục theo lĩnh vực tại chức năng đồng bộ thủ tục từ Cổng DVCQG
    enableUserManipulateFileConfig: false, // IGATESUPP-112982: [IGATE-GLI] Gấp - Hiển thị bổ sung thông tin cán bộ trình ký để lãnh đạo dễ phân biệt
    showHistoryUpdateFee: false, // IGATESUPP-114457: VNPT DNI - IGATE 3.0 - Hiển thị chi tiết "Lịch sử" cập nhật Giá trị cũ - Giá trị mới
    numberOfPhoneVNPostCharacter: 11, // IGATESUPP-115631: [iGate-HBH] - nhập số điện thoại người nhận khi chọn hình thức nhận kết quả tại nhà
    enableShowSignIconResultFile: false, // IGATESUPP-115596: [IGATE-GLI] Hiển thị nút tick xanh cho file khi đã ký xong (Kết quả xử lý)
    //showHistoryUpdateFee: false, // IGATESUPP-114457: VNPT DNI - IGATE 3.0 - Hiển thị chi tiết "Lịch sử" cập nhật Giá trị cũ - Giá trị mới
    disableTab5343: false,  // IGATESUPP-116529: Fix lỗi không thể chọn Hình thức nhận kết quả (tuqn đã confirm)
    disableTab5343TransferPaperDocuments: false,
    sendEmailSigned: 0,
    sendSmsSigned: 0,
    viettelPostReceiveResultsByAddress: '67ea1418ac40354c79812e38', // IGATESUPP-119776 - ID của hình thức nhận kết quả muốn tích hợp với Viettelpost
    viettelPostEnable: false, // IGATESUPP-119776 - Quy định việc bật tắt gửi thông tin hồ sơ qua ViettelPost
    viettelPostIdCoKetQua: '6206911d677d1a2e1527bcb3', // IGATESUPP-119776 - ID của trạng thái hồ sơ tại bước gửi thông tin trả kết quả cho ViettelPost
    viettelPostAgencyAddressEnable: false, // IGATESUPP-119776 - Bật/tắt hiển thị Địa chỉ cơ quan tiếp nhận hồ sơ
    eOfficeLabelKHA: false, // IGATESUPP-120075 Sửa đổi ioffice thành E-office của KHA
    autoSendSmsSignedSim: 0,
    // region Báo cáo Lâm Đồng
    statisticLDG: {
    },
    // endregion Báo cáo Lâm Đồng

    listLandcapeTemplate: [], //mặc định in ngang KHA
    timeOutNotify: 3000, // IGATESUPP-130147 Thêm tham số thời gian timeOut Notify
    sendQLVB:{  //IGATESUPP-133958 [iGate] Điều chỉnh tích hợp quản lý văn bản đà năng (Chức năng kiểm tra ký số và chuyển qlvb)
        originCode: "",
        autoSendLogEnable: false,
        procedureId: "6350a7c3ef957854d7f96e58",
        processId:"63a01db48c317b73cbd919b3",
        stepId:"63a01db58c317b73cbd919b4",
        commonEformId: "",
        detailEformId: ""
    },
    allowChangeImageVNPTCA: false, //IGATESUPP-134727 [NBH-iGate3.0] Hỗ trợ chỉnh sửa chức năng ký số VNPT - CA
    enablePopupSelecteSignVNPTCA: true, //IGATESUPP-134727 [NBH-iGate3.0] Hỗ trợ chỉnh sửa chức năng ký số VNPT - CA
    tagIdSignInfoVNPTCA: "686cc891b87cf204aca3de0e", // id nhãn ký số VNPTCA,
    enablePopupSignature: false, //IGATESUPP-136202 Điều chỉnh hiện thị chữ ký số
    checkPaidDate: false,
    checkPaymentMethod: false,
    enableConnectOtherBranches: false, // IGATESUPP-137074 Hiển thị combobox Liên thông khác nhánh ở quy trình duùng riêng
  }
  isPerformance = false;
  checkRequiredPayment = false;
  hideButtonRegisterAtHome = false;
  enableProcedureConfigLLTPVNeID = false;
  showsFilterAddressEnterpriseEnable = false; // IGATESUPP-87471 [HCM iGate V2] Sở Y Tế - Thêm chức năng lọc hồ sơ theo địa chỉ công ty [SD17471]
  showsFilterAddressEnterpriseAgencyIds = [];
  hiddenButtonIssueReceiptsDossierOnlineAgencyIds=[];
  disableUserNameVNPost = false; // IGATESUPP-87014 [HCM - iGATE v2] Sở Tư Pháp - Không cho phép điều chỉnh thông tin VNPOST[SD17141]
  hiddenCheckboxUpdateDueDate = false; //IGATESUPP-92153 Sở Xây dựng - Kiểm tra lại chức năng kết thúc nhiều hồ sơ
  disableUserNameVNPostAgencyIds = ['6296de53011f773c23acd1b8']
  limitTimeExportData = false;
  HCMCStatisticalEnable = 0;  // IGATESUPP-97663
  HCMCStatisticalAgencyIds = [];
  searchTimeOnPrintForm: 0; //IGATESUPP-116986


  showsInformationReceiptZeroVND = false; // IGATESUPP-87473 Quận Tân Bình - Hiển thị thông tin khi phát hành biên lai lệ phí 0 đồng
  showsInformationReceiptZeroVNDAgencyIds = []; // IGATESUPP-87473 Quận Tân Bình - Hiển thị thông tin khi phát hành biên lai lệ phí 0 đồng
  showContentProcessing = false; // IGATESUPP-89417 [KGG-iGate 2.0] Bổ sung hiển thị Nội dung xử lý
  isAddColumDataExport = false //IGATESUPP-90642 Bổ sung thêm trường thông tin chủ hồ sơ khi xuất file trong tra cứu hồ sơ
  showColumnsOrganEnable = false; // phquan.hcm - IGATESUPP-86048 - [HCM iGATE V2] Offsite - Bổ sung 2 trường thông tin "Cơ quan cấp Sở/Quận/Huyện và cơ quan cấp "Phường xã" [SD 17450]
  downloadFileDossierRar = 0 // IGATESUPP-93777 iGate Bộ 4T: Hỗ trợ khi tải "Văn bản của hồ sơ" tự động thành file rar.
  showValueInforCSDLDC = false //IGATESUPP-97442 [HCM iGATE V2] Offsite - Cấu hình giới hạn số trường dữ liệu tra cứu CSDLQGDC [SD18301]
  showCancelDossier = 0; // phquan.hcm - IGATESUPP-107787 [HCM iGATE V2] Offsite - Điều chỉnh chức năng Dừng xử lý hồ sơ
  requestReturnDocument = 0; // IGATESUPP-114211 [Igate_GLI] - Chỉnh sửa lại thao tác Rút Hồ sơ

  enableSoTNMTAGESB = false; // IGATESUPP-76412 Liên thông tích hợp phần mềm 1 cấp của Sở Tài nguyên và Môi trường
  enablePowaco: false; //IGATESUPP-71866 Liên thông Powaco
  configPowacoIntegration: ""; //IGATESUPP-71866 Liên thông Powaco
  listCodeProcedurePowaco: ""; //IGATESUPP-71866 Liên thông Powaco
  enableTransferVneidLltpAgesb = false; //IGATESUPP-100095 Liên thông Vneid LLTP trên trục LGSP AGESB
  agesbDMLoaiQT = []; // IGATESUPP-76412 Liên thông tích hợp phần mềm 1 cấp của Sở Tài nguyên và Môi trường
  agesbLienKetLoaiQT = []; // IGATESUPP-76412 Liên thông tích hợp phần mềm 1 cấp của Sở Tài nguyên và Môi trường
  agesbLienKetLinhVuc = [];
  agesbLienKetDichVu = [];
  agesbLienKetDonVi = [];
  agesbLienKetBoPhan = [];
  agesbDMBuocThucHien = [];
  agesbThietLapQT = [];
  agesbDMDoiTuong = [];
  agesbDMDoiTuongMienGiam = [];
  agesbDMTyLe = [];
  agesbDMDoiTuongThu = [];  // IGATESUPP-76412 Liên thông tích hợp phần mềm 1 cấp của Sở Tài nguyên và Môi trường

  showFeeColumn = 0; // lehoanganh.hcm-IGATESUPP-123923: Thêm cột Lệ phí vào menu Xử lý hồ sơ
  showFeeColumnAgencyIds = [];

  // IGATESUPP-90376 Cấu hinh mức độ thủ tục cho Bộ TTTT
  defineProcedureLevel = [{'value': 4, 'name': 'Toàn trình'},{'value': 2, 'name': 'Thông tin'},{'value': 3, 'name': 'Một phần'}];
  defineLevelCapBoGetData = '5ff6b1a706d0e31c6bf13e09'; // IGATESUPP-90376 Chỉ lấy cơ quan có Cấp đơn vị (Cấp Bộ)
  isStatisticBoTTTT = false;
  defineSelectBoxLevelAgency = [{'value': 1, 'name': 'Vụ/Cục/Trung tâm'}]; // IGATESUPP-90216 Định nghĩa selecbox cấp đơn vị bộ 4T
  constraintDigitalSignatureFileResults = false
  hiddenButtonIssueReceiptsDossierOnline = 0;
  arrangeFeesInOrder = 0; //phucnh.it2-IGATESUPP-93289: Biến tham số bật/tắt chức năng thêm trường số thứ tự hiển thị phí trong danh sách phí của hồ sơ hiển thị ở form nộp hs của công dân

  GLIGuiZaloCanBo = false; //IGATESUPP-92653 cấu hình gửi các bộ GLI
  notificationForApp = "0" //IGATESUPP-114129 Bổ sung thông báo trên app xử lý TTHC
  templateIdZaloAssigneeGLI=""; //IGATESUPP-92653 id template zalo gửi cán bộ của GLI
  urlSyncGetListDossierGPLX = '';
  syncGPLXSavis = false;
  urlSyncDownloadFileGPLX = '';
  isShowAnhChanDung = true;
  urlSyncGetDetailDossierGPLX = '';

  isStatisticVPC = false; // Bật/tắt tham số xác định các báo cáo của VPC (Là Tỉnh VPC)

  eFormIdWithFee = "";//IGATESUPP-90815
  isSetFee = false;//IGATESUPP-90815
  optimize = {                          // IGATESUPP-91321-thoaidt: Tối ưu tìm kiếm hồ sơ
    dossierSearch: {
      codeMatch: false,                  // thay đổi tìm kiếm tương đối sang chính xác
      isShowNationCodeCbx: false,       // bổ sung cbx nationCode
      sort: "",                         // sort hồ sơ động ở menu "tra cứu hồ sơ", ví dụ "appliedDate,asc"
      onlineReceptionSort: ""           // sort hồ sơ động ở menu "hs chờ tiếp nhận", ví dụ "appliedDate,asc"
    }
  }
  listEnterPriseImplementerSearch: [];//IGATESUPP-94989 : Hồ sơ có đơn vị thực hiện là Doanh Nghiệp Hoặc Tổ chức
  getEnterpriseDetailConfigId:"" ;//IGATESUPP-94989 : Thủ tục config để lấy dữ liệu doanh nghiệp qua API tích hợp với BTTTT
  uniqueProcedureSearch  = 0;
  padmanReport = false; //IGATESUPP-93976-thuongld-Phân tách nghiệp vụ Padman
  disableMenuRemind = false; //Tham số tắt nhắc việc
  disableMenuRemindV2 = false; //IGATESUPP-112325 Tham số nhắc việc cho menu v2
  disableFilterAppliedDateV2 = false;  //IGATESUPP-112325 Tham số bật tắt giới hạn ngày menu v2
  disableMenuTaskRing = false; //Tham số tắt chuông nhắc việc

  excelDoiGPLX = {//IGATESUPP-93610 Xuất dữ liệu excel thông tin hồ sơ cung cấp cho sở GTVT
    procedureID: "6645651e6ae9745d56b7753f",
    role: "oneGateExcelGPLX",
    excludeDossierStatusList: []
  };
  definitionKeyVanThuQLVBDH = ""; //IGATESUPP-89284 GLI-Igate - Tích hợp liên thông giữa 2 hệ thống QLVBĐH của tỉnh và Một cửa
  enableSyncQLVBDHSystem = false;
  idConfigNpsGLI = "62948261ce7a6d62d41c10c1"
  autofillEntireEfromEnable = false; // IGATESUPP-93280 [HCM iGATE V2] Offsite - Hiệu chỉnh tính năng Autofill thông doanh nghiệp [SD#17904]
  processRequireAgency = 0; //IGATESUPP-94581 [HGI-IGATE] Quy trình chung và các quy trình liên thông không tự động chọn được người nhận. Sau khi chọn xong vẫn có thể chọn lại
  notShowPaymentMethodApply = false;

  menuSpecificforGLI = false; //IGATESUPP-91917 menu đặc thù GLI,
  convertNameGplx = ""; //IGATESUPP-91917 mã nhận dạng của hồ sơ đẩy từ phần mềm GPLX GLI
  sendNotifyByTemplateSync = false; //IGATESUPP-92472 danhdd.hcm [IGATE-HCM][Tối ưu] - Mục 1.3.3 - Chuyển sang xử lý đa luồng cho các kết nối tới hệ thống bên thứ 3 - Sms/Email/Zalo
  sendVNPostOverLgspHcmSync = false; //IGATESUPP-94962 danhdd.hcm [IGATE-HCM][Tối ưu] - Mục 1.3.5 - Tối ưu kết nối tới VNPost (bao gồm Circuit breaker)
  uploadFileProcessingResultsOutside = 0; // IGATESUPP-95999 nguyenttai.hcm [HCM - iGATE v2] Sở Tư Pháp - Bổ sung nút chức năng đính file kết quả ở menu Xử lý hồ sơ[SD17500]
  uploadFileProcessingResultsOutsideAgencyIds = [];
  displayedColumnsStt=false;// GATESUPP-99262 tham số quản lý các cột colums lưới dữ liệu của tra cứu hồ sơ và tra cứu hồ sơ cũ toàn cơ quan (Có thêm cột stt lên lưới dữ liệu)
  bindingImportDossierFileEcxel = 0; // IGATESUPP-99543 [HCM iGATE V2] Offsite - Điều chỉnh ràng buộc quy định import hồ sơ từ nguồn ngoài iGate [SD18425]
  timeImportingFileEcxel = "17:00:00"; // IGATESUPP-99543 [HCM iGATE V2] Offsite - Điều chỉnh ràng buộc quy định import hồ sơ từ nguồn ngoài iGate [SD18425]
  sendLgspLltpHcmSync = false;  //IGATESUPP-94966 danhdd.hcm [IGATE-HCM][Tối ưu] - Mục 1.3.9 - Tối ưu kết nối tới LGSP-LLTP (bao gồm Circuit breaker)

  showTheNewCTGiay = false; //IGATESUPP-109264 nhipttt-Thêm lãnh đạo ký duyệt hồ sơ gửi qua PM Chứng thực
  //nhipttt-IGATESUPP-117667 Q11 -P7: Đề nghị thêm chức năng báo Quá hạn thanh toán hồ sơ
  deadlineForPaymentDossier=0;
  deadlineForPaymentDossierNumberOfDays=0;
  deadlineForPaymentDossierAgencyIds=[];
  //end IGATESUPP-117667
  adjustSYNCLLTPDossier = 0; //IGATESUPP-116899 CA TP - Bổ sung 2 cột và Bổ sung thêm selectbox chọn loại phiếu trong Thống kê hồ sơ Lý lịch Tư Pháp tiếp nhận trực tuyến
  listPaymentMethodIdsToChange = [] // IGATESUPP-113330 [HCM Igate V2] Sở NNPTNN - Hồ sơ đã thanh toán thành công mà không đổi trạng thái hồ sơ
  listRemindPageProcessing = {
    enable: false,
    list : []
  };
  listRemindPageSearch = {
    enable: false,
    list : []
  };
  listRemindPageOnlineReception = {
    enable: false,
    list : []
  };

  //IGATESUPP-99325 AGG Liên thông Tuyển sinh đầu cấp
  isTsdcAgg = false;
  listMaTruongTHPT = [];

  //IGATESUPP-92441 [HCM iGATE V2] Offsite - Bổ sung chức năng ràng buộc hình ảnh đính kèm hồ sơ
  minQualityDpiImage=0;
  minQualityDpiPdf=0;
  constraintImageQuality=true;
  //end IGATESUPP-92441
  //5343
  administrativeCenterTagId = '6703ad9c0000000000000001' // nhãn Trung tâm hành chính công
  receiveResultsAdministrativeCenterId = '66f4b2100000000000000a01' //HCC 1 cấp Nhận kết quả tại trung tâm hành chính công ID
  transferPaperDocumentsPostalId = '66f4b2100000000000000a02' //HCC 1 cấp Chuyển hồ sơ giấy qua bưu chính công ích ID
  transferPaperCategoryId = '66f4b2100000000000000a00'      // HCC 1 cấp Chuyển hồ sơ giấy category tag ID
  receiveResultsFeeType: {                                        //IGATESUPP-30264-phucnh.it2: Biến cấu hình thu hộ phí tại nhà
    id: "66f4b2100000000000000b01",
    name: [
      {
        languageId: "228",
        name: "Phí trả hồ sơ tại trung tâm thành chính công"
      },
      {
        languageId: "46",
        name: "Fee send result of dossier comback at Administrative Center"
      }
    ],
    quantityEditable: "0",
    type: "1"
  }
  transferPaperFeeType: {
    id: "66f4b2100000000000000b02",
    name: [
      {
        languageId: "228",
        name: "Phí vận chuyển hồ sơ giấy"
      },
      {
        languageId: "46",
        name: "Fee transfer paper at Administrative Center"
      }
    ],
    quantityEditable: "0",
    type: "1"
  }
  dossierAccepterNoRegionFull = 0

  syncConstructHPG = {
    isSyncConstructHPG: false,
    parentIdUBNDHPG: "60b87fb59adb921904a0213e",
    lableDaXuLyXong: "Đã xử lý xong",
    lableDaTraKetQua: "Đã trả kết quả"
  }
  agencyAllowConvertImageToPdf=""; //IGATESUPP-96536 Offsite - Convert file hình sang pdf khi người dân nộp hồ sơ
  buttonFunctionToSendApologyLetter = 0; // IGATESUPP-96693 [HCM iGATE V2] Bình Chánh - Bổ sung chức năng gửi thư xin lỗi cho người dân/doanh nghiệp khi hồ sơ trễ hạn [SD 17258]
  buttonFunctionToSendApologyLetterAgencyIds = [];
  showFacilityNameProductionAddress ={  //IGATESUPP-100595 Điều chỉnh thống kê sổ HCC [SD18442]
    enable: 0,
    agencyIds: []
  };
  filterByWorkHistoryAgency: { // phquan.hcm - IGATESUPP-114208 Huyện Củ Chi - Điều chỉnh thống kê theo cơ quan HCM
    enable: 0,
    agencyIds: []
  };
  disableRoundingForUSD: { // phquan.hcm - IGATESUPP-117323 - [HCM iGate V2] Sở Y Tế - Bổ sung trường thông tin cho thống kê sổ theo dõi HCM
    enable: 0,
    agencyIds: []
  };
  adjustStatisticalAgencyDossierHCMV2 = 0; // phquan.hcm - IGATESUPP-119859 - [HCM Igate V2] Sở TNMT - Hiệu chỉnh thống kê tổng hợp xử lý hồ sơ V2
  adjustStatisticalAgencyDossierHCMV2AgencyIds = [];
  showSelectboxAndAdjustSubmissionForm = 0; // phquan.hcm - IGATESUPP-120894 - [HCM iGATE V2] CA TP - Bổ sung thêm bộ lọc cho thống kê sổ theo dõi HCM
  showSelectboxAndAdjustSubmissionFormAgencyIds = [];

  showSubmissionDate = 0; // phquan.hcm - IGATESUPP-122622 - [HCM Igate V2] Sở TNMT - Hiệu chỉnh thống kê xử lý hồ sơ V2
  showSubmissionDateAgencyIds = [];
  extendSyncSearch = false; //IGATESUPP-110701
  //IGATESUPP-97393 [VPC-IGATE-V2] - BC - Thêm mới báo cáo thống kê theo thông tư 01/2020/TT-VPCP Cấu hình loại đơn vị

  showTranslateFrom = 0; //IGATESUPP-107213 Offsite - Bổ sung trường "DichTu" vào API cho phía CTĐT
  //IGATESUPP-97393 [VPC-IGATE-V2] - BC - Thêm mới báo cáo thống kê theo thông tư 01/2020/TT-VPCP Cấu hình loại đơn vị
  report012020AgencyLevelConfig = [
    { id: '5f6b17984e1bd312a6f3ae4b', name: 'Cấp Tỉnh', level: 1 },
    { id: '5f7dade4b80e603d5300dcc4', name: 'Cấp Quận/Huyện', level: 2 },
    { id: '5f6b177a4e1bd312a6f3ae4a', name: 'Cấp Phường/Xã', level: 3 }
  ];

  report012020VPCTTPVHCCAgencyTagConfig = '5f3a491c4e1bd312a6f00007'; //IGATESUPP-97393  [VPC-IGATE-V2] - BC - Thêm mới báo cáo thống kê theo thông tư 01/2020/TT-VPCP cấu hình loại đơn vị TTVPHCC
  report012020VPCAgencyTagLevel1Config = '5f6b17984e1bd312a6f3ae4b'; //IGATESUPP-97393  [VPC-IGATE-V2] - BC - Thêm mới báo cáo thống kê theo thông tư 01/2020/TT-VPCP Cấu hình id loại đơn vị cấp Tỉnh
  report012020VPCAgencyTagLevel2Config = '5f7dade4b80e603d5300dcc4'; //IGATESUPP-97393  [VPC-IGATE-V2] - BC - Thêm mới báo cáo thống kê theo thông tư 01/2020/TT-VPCP Cấu hình id loại đơn vị cấp Quận./Huyện

  listDistrictIdQNM = ["634a2ebae5f9855652a3869b","634a2ebae5f9855652a3869c","634a2ebae5f9855652a3869d","634a2ebae5f9855652a3869e","634a2ebae5f9855652a3869f","634a2ebae5f9855652a386a0","634a2ebae5f9855652a386a1","634a2ebae5f9855652a386a2","634a2ebbe5f9855652a386a3","634a2ebbe5f9855652a386a4","634a2ebbe5f9855652a386a5","634a2ebbe5f9855652a386a6","634a2ebbe5f9855652a386a7","634a2ebbe5f9855652a386a8","634a2ebbe5f9855652a386a9","634a2ebbe5f9855652a386aa","634a2ebce5f9855652a386ab","634a2ebce5f9855652a386ac"]
  listVPDKDDQNM =     ["634d08a9947a0a27e1145b02","634d08a9947a0a27e1145b03","634d08a9947a0a27e1145b04","634d08a9947a0a27e1145b05","634d08a9947a0a27e1145b06","634d08a9947a0a27e1145b07","634d08aa947a0a27e1145b08","634d08aa947a0a27e1145b09","634d08aa947a0a27e1145b0a","634d08aa947a0a27e1145b0b","634d08aa947a0a27e1145b0c","634d08aa947a0a27e1145b0d","634d08aa947a0a27e1145b0e","634d08ab947a0a27e1145b0f","634d08ab947a0a27e1145b10","634d08ab947a0a27e1145b11","634d08ab947a0a27e1145b12","634d08ab947a0a27e1145b13"]
  titleUpdateDueDate = "";

  //start HGI IGATESUPP-97597
  hideReasonPay=true;
  //end HGI IGATESUPP-97597

  isCheckAdministrativeAgencyTagConfig = false; //IGATESUPP-99128 có kiểm tra dơn vị hành chính cấp trên của đơn vị đăng nhập không

  useEPaymenHCMLGSP = false; //IGATESUPP-99497-thuongld-[HCM iGATE V2] Offsite - Bổ sung nghiệp vụ cấu hình tài khoản thụ hưởng theo đơn vị [SD18415]

  hiddenOldCheckCredential = 0; //IGATESUPP-107099 [HCM iGATE V2] Quận Tân Bình - Lỗi không hiển thị lịch sử kiểm tra danh tính số

  //region VPC - IGATESUPP-97647 - Tiếp nhận và in phiếu
  vpcReceiveAndPrint = 0;
  vpcIdPhieuTiepNhan = "";
  vpcAdditionalRequirement=0;
  vpcIdPhieuYeuCauBoSung="";
  //endregion
  //region VPC - IGATESUPP-101440 - Xác nhận đã nhận hồ sơ gốc
  vpcConfirmOriginalDossier = 0;
  //endregion

  useDNIDocumentNumberingLedger = false; // IGATESUPP-113272 sử dụng chức năng quản lý sổ số văn bản (DNL) cho DNI
  showApplicationFormViaVNeID = 0; //IGATESUPP-101635  Sở Tư Pháp - Bổ sung hình thức nộp hồ sơ qua VNeID vào thống kê
  isHomeDNI = false; //IGATESUPP-102321-thuongld-Điều chỉnh màu sắc giao diện các trang liên quan đến hệ thống iGATE
  hideCurrencyConverter = 0; //IGATESUPP-102276-thuongld-[HCM-offsite] - HCDC Quy đổi tỷ gia USD sang VND
  showAutoDeadlineAdditionDossier : {
    enable : 0,
    agencyId: ["628d9090011f773c23acd03b"]
  }//IGATESUPP-109398[HCM iGate V2] Sở công thương - Thêm chức năng tính Hạn bổ sung hồ sơ khi yêu bổ sung từ phần mềm chuyên ngành của sở công thương [SD18191]
  showExchangeRate = 0; //GATESUPP-106085 - [HCM iGATE V2] Sở Y tế - Điều chỉnh chức năng quy đổi tiền tệ khi yêu cầu thanh toán [SD18778] - Bổ sung thêm một số yêu cầu SYT
  showFilterRegisterApplicationVNeID = 0; //IGATESUPP-102806 Sở Tư Pháp - Bổ sung trạng thái và filter Hồ sơ đăng ký trên ứng dụng VNeID
  showFilterRegisterApplicationVNeIDAgencyId = []; //IGATESUPP-102806 Sở Tư Pháp - Bổ sung trạng thái và filter Hồ sơ đăng ký trên ứng dụng VNeID
  synchronousInformationFee : {   // IGATESUPP-103504 Sở Tư Pháp - Bổ sung chức năng đồng bộ thông tin lệ phí ở eform và lệ phí của hồ sơ VNeID
    enable : 0,
    tongPhiLLTPEform: "phiLLTP",
    tongPhiCapThemBanGiayEform: "phiCapThemBanGiay",
    idPhiLLTP:["631bf17f5a90b05d45c989a4"],
    idPhiCapThemBanGiay:["6402eb4aa9a2347b01f87b07"],
    phiMoiBan: 100000
  };
  listProcedureCodeApplySyncInforFee: []; // IGATESUPP-103504 Sở Tư Pháp - Bổ sung chức năng đồng bộ thông tin lệ phí ở eform và lệ phí của hồ sơ VNeID
  showButtonSendDossiorLLTP : {			//IGATESUPP-105223: [HCM - iGATE v2] Sở Tư Pháp - Bổ sung nút Gửi lại lý lịch ở menu Tra cứu hồ sơ toàn cơ quan [SD18724]
    enable: 0,
    agencyIds: []
  };
  //region VPC - IGATESUPP-100732 - Ký số phiếu tiếp nhận
  vpcSignVGCAReceivingBill = 0;
  vpcResolutionProgressDossier = 0;
  //endregion
  showSignCopyOrg = 0;
  addContentRequest = 0; //IGATESUPP-104677
  showColUnitQTI = 0; //IGATESUPP-104658 Thực hiện chức năng Bổ sung cột thông tin bộ phận, cán bộ xử lý ở chức năng tra cứu toàn cơ quan, tra cứu theo đơn vị

  sendDossierViaSoftwareQLVB = 0; //IGATESUPP-102514 VPUB - Bổ sung chức năng gửi hồ sơ quan phần mềm quản lý văn bản [SD 18554]

  showEPaymentLGSP : {  // IGATESUPP-104207 [HCM iGATE V2] Offsite - Hiện cảnh báo khi người người dân chọn thanh toán payment LGSP HCM [SD18632]
    enable: 0,
    listPaymentMethodHidden: []
  };
  displayAllHandlerName : { //IGATESUPP-105184 bật/tắt việc hiển thị tên tất cả cán bộ xử lý ở bước hiện tại đối với hồ sơ có quy trình song song hoặc quy trình nhiều nhánh trên file excel.
    AgencyIds: [],
    enable: 0
  };
  listSectorBTTTT=["60642df9af6d2c42ce508fdc"];//IGATESUPP-104188 [BTTTT] danh sách danh sách lĩnh vực cần lấy dữ liệu

  //region VPC - IGATESUPP-105687 - Ủy quyền nhận kết quả hồ sơ
  vpcAuthorizeReceiveResult = 0;
  //endregion

   //IGATESUPP-104984 Sở Tư Pháp - Bổ sung filter trạng thái LLTP cho hồ sơ VNeID
   showFilterStatusLLTPVNeID = 0;
   showFilterStatusLLTPVNeIDAgencyIds = [];
   // end IGATESUPP-104984
  showCreateSearchOwnerFullname = 0; // IGATESUPP-105009 - Bổ sung tìm kiếm theo Tên Chủ hồ sơ
  transmitDossierCodeViaVNPost = 0; // IGATESUPP-121018 - Bổ sung thêm mã hồ sơ khi gửi qua vnpost
   //IGATESUPP-106060 Sở Tư Pháp - Lưu log các API của luồng xử lý hồ sơ LLTP VNeID
   showMenuLGSPHCMLLTPVNeIDLog = 0;

  checkAgencyFillLastValue = false //kiểm tra giá trị đơn vị thực hiện cuối xem có giá trị chưa trước khi chuyển bước
  hcmTickAllTPHS = false;
  syncUpdateComposition = false;   //IGATESUPP-107954 đồng bộ cập nhật thành phần hồ sơ khi tiếp nhận bổ sung
  checkRefusedDossierOnce = false;  //IGATESUPP-108635 kiểm tra hồ sơ đã từ chối lần đầu
  showWarningNotify = false;     // IGATESUPP-106727 [HCM Igate V2]  Sở LĐTBXH - Lỗi cán bộ không nhận được tin nhắn khi có hồ sơ chuyển đến [SD 18823]
  showComboboxAgency :{  //IGATESUPP-108526 Sở Y Tế - Điều chỉnh thống kê hồ sơ từ chối
    enable: 0,
    agencyId : []
  };
  showDocumentAndoneGateExpertTCQ: { //IGATESUPP-109519 Sở Y Tế - Bổ sung tính năng cho chức năng cho số vb
    enable : 0,
    agencyIds: [],
  };
  addTimeForProcessing = 0; //IGATESUPP-103472	 cập nhật thời gian tạm dừng hồ sơ
  showDocumentCodeToEform: { // IGATESUPP-110576 bật tắt chức năng thêm số văn bản vào file kết quả
    enable : 0,
    agencyIds: [],
  };

  checkOnlinePayment = false; //IGATESUPP-110968 Xác định hiển thị hình thức thanh toán theo nghiệp vụ của đơn vị HGI khi xem chi tiết hồ sơ danh mục ở xử lý hồ sơ, tra cứu hồ sơ toàn cơ quan
  notShowNoFee = false; //IGATESUPP-110968 Nếu trường này bật lên, khi xem chi tiết hồ sơ sẽ ẩn luôn các dòng lệ phí = 0 ở danh mục xử lý hồ sơ, tra cứu hồ sơ toàn cơ quan
  enableCheckCitizenOnlineReceptionNAST = false; // IGATESUPP-112014 [iGate2.0][QNI] - Kiểm tra thông tin khai thác CSDLDCQG
  ignoreProcedureInPaymentReportNAST = "";
  processingFlowStopProcessingNAST = 0;
  //region VPC - IGATESUPP-106660 - Xác nhận hoàn thành và trả kết quả hồ sơ
  vpcSignaturePad = 0;
  //endregion

  showPaymentRequestButtonDNI = false; // IGATESUPP-109649 Hỗ trợ cấu hình bổ sung nút Yêu cầu thanh toán ngay trong Hồ sơ chi tiết đang xử lý
  dontReloadChangProcess = 0 ; // IGATESUPP-111274 - sẽ không chuyển hướng người dùng về giao diện danh sách hồ sơ
  //IGATESUPP-111366 Bổ sung note trong trường thành phần hồ sơ
  showNoteDocument = {"value": "", "enable": 0};
  //IGATESUPP-113753 Bổ sung các tham số tên menu theo VB 43 VP UBND tỉnh Hà Giang START
  dossierCancelName: "Thống kê hồ sơ hủy";
  reportVpcpOnlineName: "Báo cáo ứng dụng Dịch vụ công trực tuyến";
  konTumListOnlinePaymentReportName: "Báo cáo thanh toán trực tuyến";
  dossierSyncName: "Đồng bộ hồ sơ cổng Dịch vụ công Quốc gia";
  statisticProcedureBDGName: "906 Thống kê thủ tục";
  overdueDossierReportBDGName: "903 Thống kê hồ sơ trễ hạn";
  userStatisticsName: "901 - Thống kê người dùng";
  dossierFeeDBNReportName: "ĐBN Thống kê lệ phí hồ sơ";
  //IGATESUPP-113753 Bổ sung các tham số tên menu theo VB 43 VP UBND tỉnh Hà Giang END
  paymentRequestDetailProcessing = false; //IGATESUPP-109196 Bổ sung nút yêu cầu thanh toán ở trang chi tiết hồ sơ DNI
  showSearchPhoneNumber = 0; //IGATESUPP-111250 - Tham số tìm kiếm theo số điện thoại

  checkSignedFormFile = 0; // IGATESUPP-108865 kiểm tra file đã được ký số

  isLtGli = false; //IGATESUPP-110647 báo cáo hồ sơ liên thông
  showSearchOrganization = 0; // IGATESUPP-111230 QBH Hỗ trợ bổ sung chức năng cấu hình tra cứu doanh nghiệp cho thủ tục
  syncAgencyTreeDVC = false; // IGATESUPP-113278 LAN Chỉnh sửa số lượng thủ tục trên cây thủ tục
  agencyIdProvince = "60c868a4289bad69c7cbffea";
  agencyIdDistrict = "60c868a6289bad69c7cc0169";
  agencyIdWard = "60d55189e14aff3aabb8f452";
  viewOrderDetailsVNPOST = 0;
  adjustExcelExportNotReceivedNAST = false; //IGATESUPP-127056 [iGate2.0][QNI] - Kiểm tra không có nút xuất toàn bộ chức năng thống kê hồ sơ chưa tiếp nhận
  hiddenCancelDossierNAST = 0; //IGATESUPP-112596 [iGate2.0][QNI] - Bỏ quyền dừng xử lý
  applicationDVCTTNAST = false; // IGATESUPP-128229 - [iGate2.0][QNI] - Điều chỉnh bổ sung số liệu báo cáo ứng dụng DVCTT - 28.05.2025
  filterSectorFromAgency = 0; //IGATESUPP-112456 fil lĩnh vực theo thủ tục dùng riêng và cơ quan thực hiện.

  showCheckboxTTHCNoDigitized = 0; //IGATESUPP-112616 [HCM iGATE V2] Quận 11 - Thêm Checkbox Bỏ qua thủ tục chứng thực không quy định số hóa tại Menu Thống kê tổng hợp số hóa hồ sơ
  isDossierOrderByProcessingDate =1 ;// IGATESUPP-111325 sắp xếp hồ sơ đang xử lý theo thứ tự giảm dần của currentTask.updatedDate
  // IGATESUPP-111043 Thêm nút Yêu cầu thanh toán hồ sơ vào bên trong chi tiết hồ sơ và 2 nút chức năng từ chối, yêu cầu rút hồ sơ ở ngay bước đầu sau khi tiếp nhận hồ sơ
  showPaymentRequestToDetailsDossier = 0;
  showRequestWithdrawDossierToDetailsDossier = 0;

  allowImageToPdfForSigning = 0;   //IGATESUPP-127532 Bổ sung chức năng chuyển định dạng ảnh sang định dạng pdf

  isEnableDossierGroup = 0; //IGATESUPP-108559 : Cho phép hiển thị phân nhóm hồ sơ của thủ tục

  enableFeesDiscount = false; //IGATESUPP-110703 - yengdh: [iGate-CTO] - Bổ sung cập nhật tỉ lệ giảm phí, lệ phí
  limitShowFunctionAfterDraftDossier = 0 // IGATESUPP-114521 bật tắt, giới hạn áp dụng check hiển thị nút chức năng sau dự thảo

  tagIdAgencyCloseProcedure = '';
  enableConfigPaymentProcessingDossier = false;
  enableApplyExcelFormStatisticVpcpOnlineDNI = false; //IGATESUPP-114574 bật tắt áp dụng mẫu excel mới Báo cáo ứng dụng Dịch vụ công trực tuyến DNI
  showFormOfPayment: {  //IGATESUPP-115968 [HCM iGATE V2] Offsite - Bổ sung trường thông tin Hình thức thanh toán ở tab lệ phí khi thực hiện Cập nhật phí/lệ phí
    enable : 0,
    methodPaymentList: [],
    agencyIds: []
  }
  sameDayPayProfileBDH  = false; // Nâng cấp bổ sung chức năng hẹn trả trong ngày

  enableShowCSDLQGVDCBDH = false; //Bổ sung chức năng hộ khẩu BDH
  showInfoReplaceID = false; //IGATESUPP-109506 xác thực thay thế CCCD/CMND BDH

  isEnableCaptcha0 = false; //IGATESUPP-112600 cập nhật kiểm tra mã captcha
  isCheckCaptchaQti = false; //IGATESUPP-112600 cập nhật kiểm tra mã captcha
  isShowReportDossierDueQnm = false; //GATESUPP-114304 Hiển thị báo cáo Đến Hạn QNM
  processAutoFillAgency = 0; //IGATESUPP-115879 Hỗ trợ kiểm tra điều chỉnh trường hợp quy trình dùng chung chuyển bước cho cấp trên LẤY ĐÚNG ĐƠN VỊ CÓ CÁN BỘ THAO TÁC ,nếu cán bộ thao tác không thuộc bất cứ đơn vị nào trong ds đơn vị thì mới lấy đơn vị đầu tiên

  showMaxSizeAllFileLAN = false; // IT360-1375534 Cấu hình tổng dung lượng file đính kèm của thủ tục

  showAIClassifyAndUploadDossierDocCode = 0; //IGATESUPP-119363 [HCM iGATE V2] Offsite - Chức năng tự động tiếp nhận hồ sơ chứng thực điện tử
  showAIClassifyAndUploadDossierName = 0; //IGATESUPP-119363

  xrayTotalDiscount = 0; //IGATESUPP-126552 [HCM iGATE V2] Sở Khoa học và Công nghệ - Bổ sung chức năng tính giảm phí theo công thức
  xrayDeviceRanges = [[1],[2,3],[4,5],[6]];
  xrayDeviceDiscountPercents = [0, 0.95, 0.9, 0.85];
  xrayOnlineDiscountPercent = "0.9";
  xrayDeviceFieldMap = [["PhiTheoHTrang", 1265521],["PhiTheoHTvu", 1265522],["PhiTheoHTdidong", 1265523]];

  listAgencyTypeDNI = {

  } // IGATESUPP-116492 [DNI] [IGATE 3.0] hỗ trợ cấu hình báo cáo thống kê số hóa hồ sơ cấp Sở

  enableTimeNowStatistic = 0; //IGATESUPP-123066 [HCM Igate V2] SỞ TNMT – Hiệu chỉnh thống kê báo cáo thông tư 01/2020/TT-VPCP & Tổng hợp xử lý hồ sơ V2
  constructorDossierStatusStaticReport = {
    "status_default": [1, 2, 3, 8, 9, 10, 11],
    "status_default2": [4, 5],
    "defaultStatusList": [2, 3, 8, 9, 10, 11],
    "listMenuTaskRemind": ["60f6364e09cbf91d41f88859"],
    "listDossierTaskStatus_return": ["60ebf14b09cbf91d41f87f8d","61ee30eada2d36b037e00005"],
    "status_total_receiver": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12],
    "status_total_receiver2": [1, 2, 3, 4, 5, 7, 8, 9, 10, 11],
    "status_move_receiver": [1, 2, 3, 4, 5, 7, 8, 9, 10, 11],
    "status_on_receiver": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12],
    "status_online_receiver": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12],
    "status_offline_receiver": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12],
    "status_total_complete": [4, 5],
    "status_early_complete": [4, 5],
    "status_on_time_complete": [4, 5],
    "status_due_complete": [4, 5],
    "status_total": [2, 3, 8, 9, 10, 11],
    "status_total1": [4, 5],
    "status_on_time": [2, 3, 8, 9, 10, 11],
    "status_on_time1": [4, 5],
    "status_on_time2": [4, 5],
    "status_over_due": [4, 5],
    "status_withdraw": [6, 12],
    "status_return_dossier": [6, 12],
    "status_waiting_for_addition": [1],
    "status_reject_dossier": [12],
    "status_level2": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12],
    "status_level3": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12],
    "status_level4": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12],
    "procedureId_level2": ["5f5b2c2b4e1bd312a6f3ae23"],
    "procedureId_level3": ["5f5b2c4b4e1bd312a6f3ae24"],
    "procedureId_level4": ["5f5b2c564e1bd312a6f3ae25"],
    "applyMethodOnline": [0, 4],
    "applyMethodOffline": [1]
  };
  roundedNumber=2;


  dossierStatusRating = []; //IGATESUPP-105812 - tham số động cho báo cáo đánh giá hồ sơ
  timeDeplayDirectRating = 10; //IGATESUPP-105812 - Mặc định 10 giây deplay để lấy hồ sơ mời đánh giá mới
  dniNoDigitizedProcedures = '2.000033.000.00.00.H19';
  administrativeUnitMerge: { // IGATESUPP-118940
    incorporateAgencyToProcedure: 0,
    tagIdAgencyIncorporates: null;
  };

  listTagAgencyForComboboxMerge = '612c5c0f17a2264ee7f42fa4,5f7dade4b80e603d5300dcc4';

  showAIClassifyDossier = 0; // IGATESUPP-118965 tham số hiển thị checkbox “AI sơ loại” khi thêm mới/cập nhật thủ tục tại Danh mục thủ tục
  showAIClassifyDossierAgencyIds = [];
  showAIClassifyDossierPercentageValid = 0;
  listAgencyAdvanceSearch: []; //IGATESUPP-121861
  addSignedFileAfterDigitalSignature = 0; //IGATESUPP-120547 [HCM iGATE V2] Sở ATTP - Điều chỉnh chức năng ký số Ban cơ yếu
  addSignedFileAfterDigitalSignatureAgencyIds: []; //IGATESUPP-120547 [HCM iGATE V2] Sở ATTP - Điều chỉnh chức năng ký số Ban cơ yếu
  isNotCheckAppointmentDateQbh = false; // IGATESUPP-118634
  configWorkInterfaceKHA = false; //IGATESUPP-125949 - Thực hiện OS Chỉnh sửa giao diện các danh sách hồ sơ cho KHA
  enableLoadprimaryAgency = false;


  enableBookList = true; //IGATESUPP-112079 - Bổ sung chức năng danh mục sổ
  configLedgerTypeId = "61baa383975790432875b2e6"; //IGATESUPP-112079 - Bổ sung chức năng danh mục sổ
  configTemplateTypeId = ""; //IGATESUPP-112079 - Bổ sung chức năng danh mục sổ
  notiSaveDoc = false; //IGATESUPP-124840 [QBH - iGate 2.0] - Bổ sung cảnh báo lưu kho
  isShowTransferResponsibleUserKHA = false; //IGATESUPP-127710 [Hỗ trợ đánh giá chức năng cho phép 2 cán bộ thực hiện ký số tại 1 bước trong quy trình]
  twoLevelPublicAdministration = true;
  enableChangePaymentMethodDossier = 0;

  enableFilterAgencySectorBeforeSave = false; //IGATESUPP-124577 [HCM iGATE V2] Offsite - Kiểm tra cấu hình lĩnh vực cho cán bộ
  updateTitleForDocument = 0; ///IGATESUPP-125874 [HCM iGATE V2] Offsite - Bổ sung chức năng Cập nhật tiêu đề cho giấy tờ khi sàng lọc hồ sơ khi tiếp nhận không hợp lệ
  isAllowSelectAgencyLevelDNI = 0;
  showPaymentDayLimit = 0; //IGATESUPP-127824 [HCM Igate V2] Sở Công Thương - Hiệu chỉnh chức năng bắt buộc thanh toán, thêm cấu hình thời gian chờ thanh toán hồ sơ
  formatBusinessAddressCompact = 0;
  notifyVbdlis =
  {
    "enable": false,
    "cancelDossier": {
      "sms": {
        "citizen": {
          "id": "682aee754c1bdd6130704be4",
          "name": "Mẫu tin nhắn dừng xử lý hồ sơ - Công dân",
          "templateId": "",
          "defaultSend": true,
          "title": "",
          "enable": true,
          "canEdit": true
        }
      },
      "email": {
        "citizen": {
          "enable": false
        }
      },
      "zalo": {
        "citizen": {
          "enable": false
        }
      }
    }
  }

  isShowSearchTrangThaiDVCLTHoTich = false;
  integrationDomain = 'https://tichhoptest.vnptigate.vn/dvclt/civil';

  isShowSpecialAgencyLevelDNI = 0;
  timeSheetIdDNI = "66ecfb9b6a1c015803531e9b";
  mainWorkingIdDNI = "66ecfc426a1c01580353202c";
  addTimeToOnlineMessage = 0;
  isLevel3 = true;
  downloadTemplateAndProcImp = 1;
  httpNewV2 = 0;
  updateRequireFieldForPayReDossier = 0;

  // IGATESUPP-130898 - [iGate] - Rà soát và điều chỉnh "Báo cáo chung" theo mô hình 2 cấp
  generalReportTitle = "Báo cáo chung";

  updateDossierHasEnded = 0; //IGATESUPP-116105 [iGate-CTO] - Điều chỉnh chức năng cập nhật hồ sơ
  sortInvalidDocumentsFirst = 0; //IGATESUPP-126795 - [HCM iGATE V2] Sở Y Tế - RPA - Menu chờ tiếp nhận loại giấy tờ nào chưa hợp lệ thì hiển thị tên luôn
  adjustVillageCombobox = 0 // IGATESUPP-136065 [HCM iGATE V2] Offsite - Hiệu chỉnh menu "Danh sách hồ sơ DVCLT" theo mô hình chính quyền mới
  agencyProvinceParent = "5def47c5f47614018c000079" // IGATESUPP-136065 [HCM iGATE V2] Offsite - Hiệu chỉnh menu "Danh sách hồ sơ DVCLT" theo mô hình chính quyền mới
  isShowButtonReceivedILisAgg: false;
  enableGetFileDossier = 0; //IGATESUPP-130740 [iGate-CTO] - Bổ sung chọn sổ tiếp nhận hồ sơ
  convertImageToPdfVbdlis = 0; // IGATESUPP-135420 [HCM iGATE V2] Offsite - Hồ sơ đồng bộ VBDLIS thất bại khi đính kèm file hình ảnh
  hiddenResendDossierHT = 0; // IGATESUPP-135691 - [HCM iGATE V2] Offsite - Ẩn nút "Gửi lại hồ sơ hộ tịch" theo luồng cũ
  constructor(
    private http: HttpClient,
    private envService: EnvService,
  ) { }

  setAppDeployment(): any {
    let headers = new HttpHeaders();
    headers = headers.append('Content-Type', 'application/json');
    this.http.get(this.getAppDeploymentUrls, { headers, responseType: 'json' }).subscribe((res: any) => {
      localStorage.setItem('deploymentVariables', JSON.stringify(res));
      this.setEnvVariables(res.configuration);
      this.setNotifyQNIVariables(res.configuration?.notifyqni)
      this.setChungThucDienTuVariables(res.configuration?.chungThucDienTu);
      this.setHeaderVariables(res.configuration?.header);
      this.setTooltipReportGeneralQNIVariables(res.configuration?.tooltipReportGeneral);
      this.setCloneDossier(res.configuration?.cloneDossier);
      this.setNewConfigV2('useDNIDocumentNumberingLedger', res.configuration?.useDNIDocumentNumberingLedger);
      // this.setNewConfigV2('enableProcedureConfigLLTPVNeID', res.configuration?.enableProcedureConfigLLTPVNeID);
      this.setNewConfigV2('isPerformance', res.configuration?.isPerformance);
      this.setNewConfigV2('checkRequiredPayment', res.configuration?.checkRequiredPayment);
      this.setNewConfigV2('hideButtonRegisterAtHome', res.configuration?.hideButtonRegisterAtHome);
      this.setNewConfigV2('showsFilterAddressEnterpriseEnable', res.configuration?.showsFilterAddressEnterpriseEnable);
      this.setNewConfigV2('showsFilterAddressEnterpriseAgencyIds', res.configuration?.showsFilterAddressEnterpriseAgencyIds);
      this.setNewConfigV2('showListDSTTHC', res.configuration?.showListDSTTHC);
      this.setNewConfigV2('timeDistance', res.configuration?.timeDistance);
      this.setNewConfigV2('showsInformationReceiptZeroVND', res.configuration?.showsInformationReceiptZeroVND);
      this.setNewConfigV2('showsInformationReceiptZeroVNDAgencyIds', res.configuration?.showsInformationReceiptZeroVNDAgencyIds);
      this.setNewConfigV2('disableUserNameVNPost', res.configuration?.disableUserNameVNPost);
      this.setNewConfigV2('disableUserNameVNPostAgencyIds', res.configuration?.disableUserNameVNPostAgencyIds);
      this.setNewConfigV2('releaseReceiptViettelSOAPFormat',res.configuration?.releaseReceiptViettelSOAPFormat);
      this.setNewConfigV2('vietTelSupplierIdHCM',res.configuration?.vietTelSupplierIdHCM);
      this.setNewConfigV2('showColumnsOrganEnable',res.configuration?.showColumnsOrganEnable);
      this.setNewConfigV2('showCancelDossier', res.configuration?.showCancelDossier);
      this.setNewConfigV2('isSetFee', res.configuration?.isSetFee);
      this.setNewConfigV2('eFormIdWithFee', res.configuration?.eFormIdWithFee);
      this.setNewConfigV2('bindingImportDossierFileEcxel', res.configuration?.bindingImportDossierFileEcxel);
      this.setNewConfigV2('timeImportingFileEcxel', res.configuration?.timeImportingFileEcxel);
      this.setNewConfigV2('hiddenCheckboxUpdateDueDate',res.configuration?.hiddenCheckboxUpdateDueDate);
      this.setNewConfigV2('optimize',res.configuration?.optimize);
      this.setNewConfigV2('constraintDigitalSignatureFileResults',res.configuration?.constraintDigitalSignatureFileResults);
      this.setNewConfigV2('padmanReport',res.configuration?.padmanReport);
      this.setNewConfigV2('disableMenuRemind',res.configuration?.disableMenuRemind);
      this.setNewConfigV2('disableMenuTaskRing',res.configuration?.disableMenuTaskRing);
      this.setNewConfigV2('limitTimeExportData', res.configuration?.limitTimeExportData);
      this.setNewConfigV2('HCMCStatisticalEnable', res.configuration?.HCMCStatisticalEnable);
      this.setNewConfigV2('HCMCStatisticalAgencyIds', res.configuration?.HCMCStatisticalAgencyIds);
      this.setNewConfigV2('searchTimeOnPrintForm', res.configuration?.searchTimeOnPrintForm);
      this.setNewConfigV2('functionAppraisalDossier', res.configuration?.functionAppraisalDossier)
      this.setNewConfigV2('autofillEntireEfromEnable', res.configuration?.autofillEntireEfromEnable);
      this.setNewConfigV2('isViewProcessingNormal', res.configuration?.isViewProcessingNormal);
      this.setNewConfigV2('isShowHcmMDDCDTCheckBox', res.configuration?.isShowHcmMDDCDTCheckBox);
      this.setNewConfigV2('arrangeFeesInOrder',res.configuration?.arrangeFeesInOrder);
      this.setNewConfigV2('editLinkFileTBXH', res.configuration?.editLinkFileTBXH);
      this.setNewConfigV2('sendVNPostOverLgspHcmSync', res.configuration?.sendVNPostOverLgspHcmSync);
      this.setNewConfigV2('uploadFileProcessingResultsOutside', res.configuration?.uploadFileProcessingResultsOutside);
      this.setNewConfigV2('uploadFileProcessingResultsOutsideAgencyIds', res.configuration?.uploadFileProcessingResultsOutsideAgencyIds);
      this.setNewConfigV2('sendLgspLltpHcmSync', res.configuration?.sendLgspLltpHcmSync);
      this.setNewConfigV2('hiddenButtonIssueReceiptsDossierOnline',res.configuration?.hiddenButtonIssueReceiptsDossierOnline);
      this.setNewConfigV2('hiddenButtonIssueReceiptsDossierOnlineAgencyIds', res.configuration?.hiddenButtonIssueReceiptsDossierOnlineAgencyIds);
      this.setNewConfigV2('listRemindPageProcessing', res.configuration?.listRemindPageProcessing);
      this.setNewConfigV2('listRemindPageSearch', res.configuration?.listRemindPageSearch);
      this.setNewConfigV2('listRemindPageOnlineReception', res.configuration?.listRemindPageOnlineReception);
      this.setNewConfigV2('minQualityDpiPdf', res.configuration?.minQualityDpiPdf);
      this.setNewConfigV2('minQualityDpiImage', res.configuration?.minQualityDpiImage);
      this.setNewConfigV2('constraintImageQuality', res.configuration?.constraintImageQuality);
      this.setNewConfigV2('receiveResultsAdministrativeCenterId', res.configuration?.receiveResultsAdministrativeCenterId);
      this.setNewConfigV2('administrativeCenterTagId', res.configuration?.administrativeCenterTagId);
      this.setNewConfigV2('transferPaperDocumentsPostalId', res.configuration?.transferPaperDocumentsPostalId);
      this.setNewConfigV2('receiveResultsFeeType', res.configuration?.receiveResultsFeeType);
      this.setNewConfigV2('transferPaperFeeType', res.configuration?.transferPaperFeeType);
      this.setSyncConstructHPGVariables(res.configuration?.syncConstructHPG)
      this.setNewConfigV2('agencyAllowConvertImageToPdf', res.configuration?.agencyAllowConvertImageToPdf);
      this.setNewConfigV2('buttonFunctionToSendApologyLetter', res.configuration?.buttonFunctionToSendApologyLetter);
      this.setNewConfigV2('buttonFunctionToSendApologyLetterAgencyIds', res.configuration?.buttonFunctionToSendApologyLetterAgencyIds);
      this.setNewConfigV2('showValueInforCSDLDC', res.configuration?.showValueInforCSDLDC);
      this.setNewConfigV2('showFacilityNameProductionAddress', res.configuration?.showFacilityNameProductionAddress);
      this.setNewConfigV2('filterByWorkHistoryAgency', res.configuration?.filterByWorkHistoryAgency);
      this.setNewConfigV2('disableRoundingForUSD', res.configuration?.disableRoundingForUSD);
      this.setNewConfigV2('xrayTotalDiscount', res.configuration?.xrayTotalDiscount);
      this.setNewConfigV2('xrayDeviceRanges', res.configuration?.xrayDeviceRanges);
      this.setNewConfigV2('xrayDeviceDiscountPercents', res.configuration?.xrayDeviceDiscountPercents);
      this.setNewConfigV2('xrayDeviceFieldMap', res.configuration?.xrayDeviceFieldMap);
      this.setNewConfigV2('xrayOnlineDiscountPercent', res.configuration?.xrayOnlineDiscountPercent);
      this.setNewConfigV2('showSelectboxAndAdjustSubmissionForm', res.configuration?.showSelectboxAndAdjustSubmissionForm);
      this.setNewConfigV2('showSelectboxAndAdjustSubmissionFormAgencyIds', res.configuration?.showSelectboxAndAdjustSubmissionFormAgencyIds);
      this.setNewConfigV2('adjustStatisticalAgencyDossierHCMV2', res.configuration?.adjustStatisticalAgencyDossierHCMV2);
      this.setNewConfigV2('adjustStatisticalAgencyDossierHCMV2AgencyIds', res.configuration?.adjustStatisticalAgencyDossierHCMV2AgencyIds);
      this.setNewConfigV2('showSubmissionDate', res.configuration?.showSubmissionDate);
      this.setNewConfigV2('showSubmissionDateAgencyIds', res.configuration?.showSubmissionDateAgencyIds);
	  this.setNewConfigV2('titleUpdateDueDate',res.configuration?.titleUpdateDueDate);
      this.setNewConfigV2('hideReasonPay', res.configuration?.hideReasonPay);
      this.setNewConfigV2('useEPaymenHCMLGSP', res.configuration?.useEPaymenHCMLGSP);
      this.setNewConfigV2('hiddenOldCheckCredential', res.configuration?.hiddenOldCheckCredential);
      this.setNewConfigV2('OnlinePaymentMethodIdList', res.configuration?.OnlinePaymentMethodIdList);
      this.setNewConfigV2('showAbstractConstructionUnitEnable', res.configuration?.showAbstractConstructionUnitEnable);
      this.setNewConfigV2('showAbstractConstructionUnitAgencyIds', res.configuration?.showAbstractConstructionUnitAgencyIds);
      this.setNewConfigV2('showsInformationAddressReceiptVT',res.configuration?.showsInformationAddressReceiptVT);
      this.setNewConfigV2('disableUpdatePaymentStatus',res.configuration?.disableUpdatePaymentStatus);
      this.setNewConfigV2('disableUpdatePaymentStatusAgencyIds',res.configuration?.disableUpdatePaymentStatusAgencyIds);
      this.setNewConfigV2('showApplicationFormViaVNeID', res.configuration?.showApplicationFormViaVNeID);
      this.setNewConfigV2('showFilterRegisterApplicationVNeID', res.configuration?.showFilterRegisterApplicationVNeID);
      this.setNewConfigV2('showFilterRegisterApplicationVNeIDAgencyId', res.configuration?.showFilterRegisterApplicationVNeIDAgencyId);
      this.setNewConfigV2('hideCurrencyConverter', res.configuration?.hideCurrencyConverter);
      this.setNewConfigV2('showAutoDeadlineAdditionDossier', res.configuration?.showAutoDeadlineAdditionDossier);
      this.setNewConfigV2('showExchangeRate', res.configuration?.showExchangeRate);
      this.setNewConfigV2('enableConfigPaymentProcessingDossier', res.configuration?.enableConfigPaymentProcessingDossier);
      this.setNewConfigV2('synchronousInformationFee', res.configuration?.synchronousInformationFee);
      this.setNewConfigV2('listProcedureCodeApplySyncInforFee', res.configuration?.listProcedureCodeApplySyncInforFee);
      this.setNewConfigV2('isHomeDNI', res.configuration?.isHomeDNI);
      this.setNewConfigV2('transmitDossierCodeViaVNPost', res.configuration?.transmitDossierCodeViaVNPost);
      this.setNewConfigV2('oneGateDigitizationDossierStatusList', res.configuration?.oneGateDigitizationDossierStatusList);
      this.setNewConfigV2('oneGateReceiptStatusList', res.configuration?.oneGateReceiptStatusList);
      this.setNewConfigV2('maxSizeFileHCM', res.configuration?.maxSizeFileHCM);
      this.setNewConfigV2('sendDossierViaSoftwareQLVB', res.configuration?.sendDossierViaSoftwareQLVB);
      this.setNewConfigV2('showSignCopyOrg', res.configuration?.showSignCopyOrg)
      this.setNewConfigV2('showEPaymentLGSP', res.configuration?.showEPaymentLGSP);
      this.setNewConfigV2('listSectorBTTTT', res.configuration?.listSectorBTTTT);
      this.setNewConfigV2('filterSectorFromAgency', res.configuration?.filterSectorFromAgency);
      this.setNewConfigV2('showButtonSendDossiorLLTP', res.configuration?.showButtonSendDossiorLLTP);
      this.setNewConfigV2('displayAllHandlerName', res.configuration?.displayAllHandlerName);
      this.setNewConfigV2('showFilterStatusLLTPVNeID', res.configuration?.showFilterStatusLLTPVNeID);
      this.setNewConfigV2('showFilterStatusLLTPVNeIDAgencyIds', res.configuration?.showFilterStatusLLTPVNeIDAgencyIds);
      this.setNewConfigV2('checkAgencyFillLastValue', res.configuration?.checkAgencyFillLastValue);
      this.setNewConfigV2('noRequirementFormFile', res.configuration?.noRequirementFormFile );
      this.setNewConfigV2('paymentOfFinancialObligations', res.configuration?.paymentOfFinancialObligations );
      this.setNewConfigV2('statusIdSyncNVTC', res.configuration?.statusIdSyncNVTC );
      this.setNewConfigV2('hcmTickAllTPHS', res.configuration?.hcmTickAllTPHS);
      this.setNewConfigV2('showMenuLGSPHCMLLTPVNeIDLog', res.configuration?.showMenuLGSPHCMLLTPVNeIDLog);
      this.setNewConfigV2('kiosDossierListParam', res.configuration?.kiosDossierListParam);
      this.setNewConfigV2('syncUpdateComposition', res.configuration?.syncUpdateComposition);
      this.setNewConfigV2('showWarningNotify', res.configuration?.showWarningNotify);
      this.setNewConfigV2('showComboboxAgency', res.configuration?.showComboboxAgency);
      this.setNewConfigV2('checkRefusedDossierOnce', res.configuration?.checkRefusedDossierOnce);
      this.setNewConfigV2('showDocumentAndoneGateExpertTCQ', res.configuration?.showDocumentAndoneGateExpertTCQ);
      this.setNewConfigV2('showDocumentCodeToEform', res.configuration?.showDocumentCodeToEform);
      this.setNewConfigV2('showPaymentRequestButtonDNI', res.configuration?.showPaymentRequestButtonDNI);
	  this.setNewConfigV2('dontReloadChangProcess', res.configuration?.dontReloadChangProcess);
	  this.setNewConfigV2('showTranslateFrom', res.configuration?.showTranslateFrom);
      this.setNewConfigV2('isHcmCTDT', res.configuration?.isHcmCTDT);
      this.setNewConfigV2('hideInvoiceAndPOSProcessingDossier', res.configuration?.hideInvoiceAndPOSProcessingDossier);
      this.setNewConfigV2('showNoteDocument', res.configuration?.showNoteDocument);
      this.setNewConfigV2('paymentRequestDetailProcessing', res.configuration?.paymentRequestDetailProcessing);
      this.setNewConfigV2('showCheckboxTTHCNoDigitized', res.configuration?.showCheckboxTTHCNoDigitized);
      this.setNewConfigV2('enableSectorOfSynchronizeProcedure', res.configuration?.enableSectorOfSynchronizeProcedure);
      this.setNewConfigV2('inputCheckCSDLDC', res.configuration?.inputCheckCSDLDC);
      this.setNewConfigV2('showSearchOrganization', res.configuration?.showSearchOrganization);
      this.setNewConfigV2('showPaymentRequestToDetailsDossier', res.configuration?.showPaymentRequestToDetailsDossier);
      this.setNewConfigV2('showRequestWithdrawDossierToDetailsDossier', res.configuration?.showRequestWithdrawDossierToDetailsDossier);
      this.setNewConfigV2('listPaymentMethodIdsToChange', res.configuration?.listPaymentMethodIdsToChange);
      this.setNewConfigV2('enableFeesDiscount', res.configuration?.enableFeesDiscount);
      this.setNewConfigV2('disableMenuRemindV2',res.configuration?.disableMenuRemindV2);
      this.setNewConfigV2('disableFilterAppliedDateV2',res.configuration?.disableFilterAppliedDateV2);
      this.setNewConfigV2('limitShowFunctionAfterDraftDossier', res.configuration?.limitShowFunctionAfterDraftDossier);
      this.setNewConfigV2('configWorkInterfaceKHA', res.configuration?.configWorkInterfaceKHA);
      this.setNewConfigV2('enableApplyExcelFormStatisticVpcpOnlineDNI', res.configuration?.enableApplyExcelFormStatisticVpcpOnlineDNI);
      this.setNewConfigV2('dossierCancelName', res.configuration?.dossierCancelName);
      this.setNewConfigV2('reportVpcpOnlineName', res.configuration?.reportVpcpOnlineName);
      this.setNewConfigV2('agencyIdProvince', res.configuration?.agencyIdProvince);
      this.setNewConfigV2('agencyIdDistrict', res.configuration?.agencyIdDistrict);
      this.setNewConfigV2('agencyIdWard', res.configuration?.agencyIdWard);
      this.setNewConfigV2('konTumListOnlinePaymentReportName', res.configuration?.konTumListOnlinePaymentReportName);
      this.setNewConfigV2('dossierSyncName', res.configuration?.dossierSyncName);
      this.setNewConfigV2('statisticProcedureBDGName', res.configuration?.statisticProcedureBDGName);
      this.setNewConfigV2('overdueDossierReportBDGName', res.configuration?.overdueDossierReportBDGName);
      this.setNewConfigV2('userStatisticsName', res.configuration?.userStatisticsName);
      this.setNewConfigV2('dossierFeeDBNReportName', res.configuration?.dossierFeeDBNReportName);
      this.setNewConfigV2('tagIdAgencyCloseProcedure', res.configuration?.tagIdAgencyCloseProcedure);
      this.setNewConfigV2('requestReturnDocument', res.configuration?.requestReturnDocument);
      this.setNewConfigV2('showFormOfPayment', res.configuration?.showFormOfPayment);
      this.setNewConfigV2('showFeeColumn', res.configuration?.showFeeColumn);
      this.setNewConfigV2('showFeeColumnAgencyIds', res.configuration?.showFeeColumnAgencyIds);
      this.setNewConfigV2('adjustSYNCLLTPDossier', res.configuration?.adjustSYNCLLTPDossier);
      this.setNewConfigV2('showResultCode', res.configuration?.showResultCode);
      this.setNewConfigV2('processAutoFillAgency', res.configuration?.processAutoFillAgency);
      this.setNewConfigV2('listAgencyTypeDNI', res.configuration?.listAgencyTypeDNI);
      this.setNewConfigV2('sameDayPayProfileBDH',  res.configuration?.sameDayPayProfileBDH);
      this.setNewConfigV2('enableShowCSDLQGVDCBDH', res.configuration?.enableShowCSDLQGVDCBDH);
      this.setNewConfigV2('showInfoReplaceID', res.configuration?.showInfoReplaceID);
      this.setNewConfigV2('isInvoiceProviderActivated', res.configuration?.isInvoiceProviderActivated);
      this.setNewConfigV2('isPaymentMethodActivated', res.configuration?.isPaymentMethodActivated);
      this.setNewConfigV2('dniStatistics', res.configuration?.dniStatistics);
      this.setNewConfigV2('viewOrderDetailsVNPOST', res.configuration?.viewOrderDetailsVNPOST);
      this.setNewConfigV2('enableCheckCitizenOnlineReceptionNAST', res.configuration?.enableCheckCitizenOnlineReceptionNAST);
      this.setNewConfigV2('administrativeUnitMerge', res.configuration?.administrativeUnitMerge);
      this.setNewConfigV2('showMaxSizeAllFileLAN', res.configuration?.showMaxSizeAllFileLAN);
      this.setNewConfigV2('showAIClassifyDossier', res.configuration?.showAIClassifyDossier);
      this.setNewConfigV2('showAIClassifyDossierAgencyIds', res.configuration?.showAIClassifyDossierAgencyIds);
      this.setNewConfigV2('showAIClassifyDossierPercentageValid', res.configuration?.showAIClassifyDossierPercentageValid);
      this.setNewConfigV2('ignoreProcedureInPaymentReportNAST', res.configuration?.ignoreProcedureInPaymentReportNAST);
      this.setNewConfigV2('processingFlowStopProcessingNAST', res.configuration?.processingFlowStopProcessingNAST);
      this.setNewConfigV2('showAIClassifyAndUploadDossierName',  res.configuration?.showAIClassifyAndUploadDossierName);
      this.setNewConfigV2('showAIClassifyAndUploadDossierDocCode',  res.configuration?.showAIClassifyAndUploadDossierDocCode);
      this.setNewConfigV2('listAgencyAdvanceSearch', res.configuration?.listAgencyAdvanceSearch);
      this.setNewConfigV2('deadlineForPaymentDossier', res.configuration?.deadlineForPaymentDossier);
      this.setNewConfigV2('deadlineForPaymentDossierNumberOfDays', res.configuration?.deadlineForPaymentDossierNumberOfDays);
      this.setNewConfigV2('deadlineForPaymentDossierAgencyIds', res.configuration?.deadlineForPaymentDossierAgencyIds);
      this.setNewConfigV2('addSignedFileAfterDigitalSignature', res.configuration?.addSignedFileAfterDigitalSignature);
      this.setNewConfigV2('addSignedFileAfterDigitalSignatureAgencyIds', res.configuration?.addSignedFileAfterDigitalSignatureAgencyIds);
      this.setNewConfigV2('enableBookList', res.configuration?.enableBookList);
      this.setNewConfigV2('configLedgerTypeId', res.configuration?.configLedgerTypeId);
      this.setNewConfigV2('configTemplateTypeId', res.configuration?.configTemplateTypeId);
      this.setNewConfigV2('enableTimeNowStatistic', res.configuration?.enableTimeNowStatistic);
      this.setNewConfigV2('enableLoadprimaryAgency', res.configuration?.enableLoadprimaryAgency);
      this.setNewConfigV2('enableFilterAgencySectorBeforeSave', res.configuration?.enableFilterAgencySectorBeforeSave);
      this.setNewConfigV2('notiSaveDoc', res.configuration?.notiSaveDoc);
      this.setNewConfigV2('isShowCheckboxDeletePermanentKHA', res.configuration?.isShowCheckboxDeletePermanentKHA);
      this.setNewConfigV2('adjustExcelExportNotReceivedNAST',  res.configuration?.adjustExcelExportNotReceivedNAST);
      this.setNewConfigV2('hiddenCancelDossierNAST',  res.configuration?.hiddenCancelDossierNAST);
      this.setNewConfigV2('applicationDVCTTNAST',  res.configuration?.applicationDVCTTNAST);
      this.setNewConfigV2('updateTitleForDocument', res.configuration?.updateTitleForDocument);
      this.setNewConfigV2('isShowTransferResponsibleUserKHA', res.configuration?.isShowTransferResponsibleUserKHA);
      this.setNewConfigV2('isAllowSelectAgencyLevelDNI', res.configuration?.isAllowSelectAgencyLevelDNI);
      this.setNewConfigV2('showPaymentDayLimit', res.configuration?.showPaymentDayLimit);
      this.setNewConfigV2('twoLevelPublicAdministration', res.configuration?.twoLevelPublicAdministration);
      this.setNewConfigV2('notifyVbdlis', res.configuration?.notifyVbdlis);
      this.setNewConfigV2('isShowSpecialAgencyLevelDNI', res.configuration?.isShowSpecialAgencyLevelDNI);
      this.setNewConfigV2('timeSheetIdDNI', res.configuration?.timeSheetIdDNI);
      this.setNewConfigV2('mainWorkingIdDNI', res.configuration?.mainWorkingIdDNI);
      this.setNewConfigV2('allowImageToPdfForSigning', res.configuration?.allowImageToPdfForSigning);
      this.setNewConfigV2('enableChangePaymentMethodDossier', res.configuration?.enableChangePaymentMethodDossier);
      this.setNewConfigV2('formatBusinessAddressCompact', res.configuration?.formatBusinessAddressCompact);
      this.setNewConfigV2('addTimeToOnlineMessage', res.configuration?.addTimeToOnlineMessage);
      this.setNewConfigV2('dniNonDigitizedProcedures', res.configuration?.dniNoDigitizedProcedures);
      this.setNewConfigV2('isLevel3', res.configuration?.isLevel3);
      this.setNewConfigV2('downloadTemplateAndProcImp', res.configuration?.downloadTemplateAndProcImp);
      this.setNewConfigV2('listTagAgencyForComboboxMerge', res.configuration?.listTagAgencyForComboboxMerge);
      this.setNewConfigV2('configViewProcessingKHA', res.configuration?.configViewProcessingKHA);
      this.setNewConfigV2('configViewStepByStepProcessKHA', res.configuration?.configViewStepByStepProcessKHA);
      this.setNewConfigV2('updateRequireFieldForPayReDossier', res.configuration?.updateRequireFieldForPayReDossier);
      this.setNewConfigV2('generalReportTitle', res.configuration?.generalReportTitle);
      this.setNewConfigV2('updateDossierHasEnded', res.configuration?.updateDossierHasEnded);
      this.setNewConfigV2('sortInvalidDocumentsFirst', res.configuration?.sortInvalidDocumentsFirst);
      this.setNewConfigV2('adjustVillageCombobox', res.configuration?.adjustVillageCombobox);
      this.setNewConfigV2('agencyProvinceParent', res.configuration?.agencyProvinceParent);
      this.setNewConfigV2('hiddenResendDossierHT', res.configuration?.hiddenResendDossierHT);
      this.setNewConfigV2('isShowSearchTrangThaiDVCLTHoTich', res.configuration?.isShowSearchTrangThaiDVCLTHoTich);
      this.setNewConfigV2('integrationDomain', res.configuration?.integrationDomain);
      this.setNewConfigV2('enableGetFileDossier', res.configuration?.enableGetFileDossier);
      this.setNewConfigV2('enableNotarizationRepository', res.configuration?.enableNotarizationRepository);
      this.setNewConfigV2('convertImageToPdfVbdlis', res.configuration?.convertImageToPdfVbdlis);
      return res.configuration;
      // setTimeout(() => {
      //   this.renewDeploymentConfig.next(true);
      // }, this.envService.getConfig().timeOut);
    });
  }

  getAppDeployment(): any {
    const deploymentVariables = JSON.parse(localStorage.getItem('deploymentVariables'));
    if (deploymentVariables) {
      this.setEnvVariables(deploymentVariables.configuration);
      this.setNotifyQNIVariables(deploymentVariables.configuration?.notifyqni)
      this.setChungThucDienTuVariables(deploymentVariables.configuration?.chungThucDienTu);
      this.setHeaderVariables(deploymentVariables.configuration?.header);
      this.setTooltipReportGeneralQNIVariables(deploymentVariables.configuration?.tooltipReportGeneral);
      this.setNewConfigV2('useDNIDocumentNumberingLedger', deploymentVariables.configuration?.useDNIDocumentNumberingLedger);
      // this.setNewConfigV2('enableProcedureConfigLLTPVNeID', deploymentVariables.configuration?.enableProcedureConfigLLTPVNeID);
      this.setNewConfigV2('isPerformance', deploymentVariables.configuration?.isPerformance);
      this.setNewConfigV2('checkRequiredPayment', deploymentVariables.configuration?.checkRequiredPayment);
      this.setNewConfigV2('hideButtonRegisterAtHome', deploymentVariables.configuration?.hideButtonRegisterAtHome);
      this.setNewConfigV2('showsFilterAddressEnterpriseEnable', deploymentVariables.configuration?.showsFilterAddressEnterpriseEnable);
      this.setNewConfigV2('showsFilterAddressEnterpriseAgencyIds', deploymentVariables.configuration?.showsFilterAddressEnterpriseAgencyIds);
      this.setNewConfigV2('showListDSTTHC', deploymentVariables.configuration?.showListDSTTHC);
      this.setNewConfigV2('timeDistance', deploymentVariables.configuration?.timeDistance);
      this.setNewConfigV2('showsInformationReceiptZeroVND', deploymentVariables.configuration?.showsInformationReceiptZeroVND);
      this.setNewConfigV2('showsInformationReceiptZeroVNDAgencyIds', deploymentVariables.configuration?.showsInformationReceiptZeroVNDAgencyIds);
      this.setNewConfigV2('disableUserNameVNPost', deploymentVariables.configuration?.disableUserNameVNPost);
      this.setNewConfigV2('disableUserNameVNPostAgencyIds', deploymentVariables.configuration?.disableUserNameVNPostAgencyIds);
      this.setNewConfigV2('releaseReceiptViettelSOAPFormat', deploymentVariables.configuration?.releaseReceiptViettelSOAPFormat);
      this.setNewConfigV2('vietTelSupplierIdHCM', deploymentVariables.configuration?.vietTelSupplierIdHCM);
      this.setNewConfigV2('showColumnsOrganEnable', deploymentVariables.configuration?.showColumnsOrganEnable);
      this.setNewConfigV2('showCancelDossier', deploymentVariables.configuration?.showCancelDossier);
      this.setNewConfigV2('constraintDigitalSignatureFileResults', deploymentVariables.configuration?.constraintDigitalSignatureFileResults);
      this.setNewConfigV2('isSetFee', deploymentVariables.configuration?.isSetFee);
      this.setNewConfigV2('eFormIdWithFee', deploymentVariables.configuration?.eFormIdWithFee);
      this.setNewConfigV2('hiddenCheckboxUpdateDueDate', deploymentVariables.configuration?.hiddenCheckboxUpdateDueDate);
      this.setNewConfigV2('bindingImportDossierFileEcxel', deploymentVariables.configuration?.bindingImportDossierFileEcxel);
      this.setNewConfigV2('timeImportingFileEcxel', deploymentVariables.configuration?.timeImportingFileEcxel);
      this.setNewConfigV2('optimize', deploymentVariables.configuration?.optimize);
      this.setNewConfigV2('padmanReport', deploymentVariables.configuration?.padmanReport);
      this.setNewConfigV2('disableMenuRemind', deploymentVariables.configuration?.disableMenuRemind);
      this.setNewConfigV2('disableMenuTaskRing', deploymentVariables.configuration?.disableMenuTaskRing);
      this.setNewConfigV2('limitTimeExportData', deploymentVariables.configuration?.limitTimeExportData);
      this.setNewConfigV2('HCMCStatisticalEnable', deploymentVariables.configuration?.HCMCStatisticalEnable);
      this.setNewConfigV2('HCMCStatisticalAgencyIds', deploymentVariables.configuration?.HCMCStatisticalAgencyIds);
      this.setNewConfigV2('searchTimeOnPrintForm', deploymentVariables.configuration?.searchTimeOnPrintForm);
      this.setNewConfigV2('functionAppraisalDossier', deploymentVariables.configuration?.functionAppraisalDossier)
      this.setNewConfigV2('autofillEntireEfromEnable', deploymentVariables.configuration?.autofillEntireEfromEnable);
      this.setNewConfigV2('isViewProcessingNormal', deploymentVariables.configuration?.isViewProcessingNormal);
      this.setNewConfigV2('isShowHcmMDDCDTCheckBox', deploymentVariables.configuration?.isShowHcmMDDCDTCheckBox);
      this.setNewConfigV2('arrangeFeesInOrder', deploymentVariables.configuration?.arrangeFeesInOrder);
      this.setNewConfigV2('editLinkFileTBXH', deploymentVariables.configuration?.editLinkFileTBXH);
      this.setNewConfigV2('sendVNPostOverLgspHcmSync', deploymentVariables.configuration?.sendVNPostOverLgspHcmSync);
      this.setNewConfigV2('uploadFileProcessingResultsOutside', deploymentVariables.configuration?.uploadFileProcessingResultsOutside);
      this.setNewConfigV2('uploadFileProcessingResultsOutsideAgencyIds', deploymentVariables.configuration?.uploadFileProcessingResultsOutsideAgencyIds);
      this.setNewConfigV2('sendLgspLltpHcmSync', deploymentVariables.configuration?.sendLgspLltpHcmSync);

      this.setNewConfigV2('hiddenButtonIssueReceiptsDossierOnline',deploymentVariables.configuration?.hiddenButtonIssueReceiptsDossierOnline)
      this.setNewConfigV2('hiddenButtonIssueReceiptsDossierOnlineAgencyIds',deploymentVariables.configuration?.hiddenButtonIssueReceiptsDossierOnlineAgencyIds);
      this.setNewConfigV2('listRemindPageProcessing', deploymentVariables.configuration?.listRemindPageProcessing);
      this.setNewConfigV2('listRemindPageSearch', deploymentVariables.configuration?.listRemindPageSearch);
      this.setNewConfigV2('listRemindPageOnlineReception', deploymentVariables.configuration?.listRemindPageOnlineReception);
      this.setNewConfigV2('minQualityDpiPdf', deploymentVariables.configuration?.minQualityDpiPdf);
      this.setNewConfigV2('minQualityDpiImage', deploymentVariables.configuration?.minQualityDpiImage);
      this.setNewConfigV2('constraintImageQuality', deploymentVariables.configuration?.constraintImageQuality);
      this.setNewConfigV2('receiveResultsAdministrativeCenterId', deploymentVariables.configuration?.receiveResultsAdministrativeCenterId);
      this.setNewConfigV2('administrativeCenterTagId', deploymentVariables.configuration?.administrativeCenterTagId);
      this.setNewConfigV2('transferPaperDocumentsPostalId', deploymentVariables.configuration?.transferPaperDocumentsPostalId);
      this.setNewConfigV2('receiveResultsFeeType', deploymentVariables.configuration?.receiveResultsFeeType);
      this.setNewConfigV2('transferPaperFeeType', deploymentVariables.configuration?.transferPaperFeeType);
      this.setSyncConstructHPGVariables(deploymentVariables.configuration?.syncConstructHPG);
      this.setNewConfigV2('agencyAllowConvertImageToPdf', deploymentVariables.configuration?.agencyAllowConvertImageToPdf);
      this.setNewConfigV2('buttonFunctionToSendApologyLetter', deploymentVariables.configuration?.buttonFunctionToSendApologyLetter);
      this.setNewConfigV2('buttonFunctionToSendApologyLetterAgencyIds', deploymentVariables.configuration?.buttonFunctionToSendApologyLetterAgencyIds);
      this.setNewConfigV2('showValueInforCSDLDC', deploymentVariables.configuration?.showValueInforCSDLDC);
      this.setNewConfigV2('showFacilityNameProductionAddress', deploymentVariables.configuration?.showFacilityNameProductionAddress);
      this.setNewConfigV2('filterByWorkHistoryAgency', deploymentVariables.configuration?.filterByWorkHistoryAgency);
      this.setNewConfigV2('xrayTotalDiscount', deploymentVariables.configuration?.xrayTotalDiscount);
      this.setNewConfigV2('xrayDeviceRanges', deploymentVariables.configuration?.xrayDeviceRanges);
      this.setNewConfigV2('xrayDeviceDiscountPercents', deploymentVariables.configuration?.xrayDeviceDiscountPercents);
      this.setNewConfigV2('xrayOnlineDiscountPercent', deploymentVariables.configuration?.xrayOnlineDiscountPercent);
      this.setNewConfigV2('xrayDeviceFieldMap', deploymentVariables.configuration?.xrayDeviceFieldMap);
      this.setNewConfigV2('disableRoundingForUSD', deploymentVariables.configuration?.disableRoundingForUSD);
      this.setNewConfigV2('showSelectboxAndAdjustSubmissionForm', deploymentVariables.configuration?.showSelectboxAndAdjustSubmissionForm);
      this.setNewConfigV2('showSelectboxAndAdjustSubmissionFormAgencyIds', deploymentVariables.configuration?.showSelectboxAndAdjustSubmissionFormAgencyIds);
      this.setNewConfigV2('adjustStatisticalAgencyDossierHCMV2', deploymentVariables.configuration?.adjustStatisticalAgencyDossierHCMV2);
      this.setNewConfigV2('adjustStatisticalAgencyDossierHCMV2AgencyIds', deploymentVariables.configuration?.adjustStatisticalAgencyDossierHCMV2AgencyIds);
      this.setNewConfigV2('showSubmissionDate', deploymentVariables.configuration?.showSubmissionDate);
      this.setNewConfigV2('showSubmissionDateAgencyIds', deploymentVariables.configuration?.showSubmissionDateAgencyIds);
      this.setNewConfigV2('titleUpdateDueDate', deploymentVariables.configuration?.titleUpdateDueDate);
      this.setNewConfigV2('hideReasonPay', deploymentVariables.configuration?.hideReasonPay);
      this.setNewConfigV2('useEPaymenHCMLGSP', deploymentVariables.configuration?.useEPaymenHCMLGSP);
      this.setNewConfigV2('hiddenOldCheckCredential', deploymentVariables.configuration?.hiddenOldCheckCredential);
      this.setSyncConstructHPGVariables(deploymentVariables.configuration?.syncConstructHPG)
      this.setNewConfigV2('OnlinePaymentMethodIdList', deploymentVariables.configuration?.OnlinePaymentMethodIdList);
      this.setNewConfigV2('showAbstractConstructionUnitEnable', deploymentVariables.configuration?.showAbstractConstructionUnitEnable);
      this.setNewConfigV2('showAbstractConstructionUnitAgencyIds', deploymentVariables.configuration?.showAbstractConstructionUnitAgencyIds);
      this.setNewConfigV2('showsInformationAddressReceiptVT', deploymentVariables.configuration?.showsInformationAddressReceiptVT);
      this.setNewConfigV2('disableUpdatePaymentStatus', deploymentVariables.configuration?.disableUpdatePaymentStatus);
      this.setNewConfigV2('disableUpdatePaymentStatusAgencyIds',deploymentVariables.configuration?.disableUpdatePaymentStatusAgencyIds);
      this.setNewConfigV2('showApplicationFormViaVNeID', deploymentVariables.configuration?.showApplicationFormViaVNeID);
      this.setNewConfigV2('showFilterRegisterApplicationVNeID', deploymentVariables.configuration?.showFilterRegisterApplicationVNeID);
      this.setNewConfigV2('showFilterRegisterApplicationVNeIDAgencyId', deploymentVariables.configuration?.showFilterRegisterApplicationVNeIDAgencyId);
      this.setNewConfigV2('hideCurrencyConverter', deploymentVariables.configuration?.hideCurrencyConverter);
      this.setNewConfigV2('showAutoDeadlineAdditionDossier', deploymentVariables.configuration?.showAutoDeadlineAdditionDossier);
      this.setNewConfigV2('showExchangeRate', deploymentVariables.configuration?.showExchangeRate);
      this.setNewConfigV2('enableConfigPaymentProcessingDossier', deploymentVariables.configuration?.enableConfigPaymentProcessingDossier);
      this.setNewConfigV2('synchronousInformationFee', deploymentVariables.configuration?.synchronousInformationFee);
      this.setNewConfigV2('listProcedureCodeApplySyncInforFee', deploymentVariables.configuration?.listProcedureCodeApplySyncInforFee);
      this.setNewConfigV2('isHomeDNI', deploymentVariables.configuration?.isHomeDNI);
      this.setNewConfigV2('transmitDossierCodeViaVNPost', deploymentVariables.configuration?.transmitDossierCodeViaVNPost);
      this.setNewConfigV2('listSliderBanerDNI', deploymentVariables.configuration?.listSliderBanerDNI);
      this.setNewConfigV2('oneGateDigitizationDossierStatusList', deploymentVariables.configuration?.oneGateDigitizationDossierStatusList);
      this.setNewConfigV2('oneGateReceiptStatusList', deploymentVariables.configuration?.oneGateReceiptStatusList);
      this.setNewConfigV2('maxSizeFileHCM', deploymentVariables.configuration?.maxSizeFileHCM);
      this.setNewConfigV2('sendDossierViaSoftwareQLVB', deploymentVariables.configuration?.sendDossierViaSoftwareQLVB);
      this.setNewConfigV2('showEPaymentLGSP', deploymentVariables.configuration?.showEPaymentLGSP);
      this.setNewConfigV2('showButtonSendDossiorLLTP', deploymentVariables.configuration?.showButtonSendDossiorLLTP);
      this.setNewConfigV2('showSignCopyOrg', deploymentVariables.configuration?.showSignCopyOrg);
      this.setNewConfigV2('showTranslateFrom', deploymentVariables.configuration?.showTranslateFrom);
      this.setNewConfigV2('isHcmCTDT', deploymentVariables.configuration?.isHcmCTDT);
      this.setNewConfigV2('displayAllHandlerName', deploymentVariables.configuration?.displayAllHandlerName);
      this.setNewConfigV2('showFilterStatusLLTPVNeID', deploymentVariables.configuration?.showFilterStatusLLTPVNeID);
      this.setNewConfigV2('showFilterStatusLLTPVNeIDAgencyIds', deploymentVariables.configuration?.showFilterStatusLLTPVNeIDAgencyIds);
      this.setNewConfigV2('checkAgencyFillLastValue', deploymentVariables.configuration?.checkAgencyFillLastValue);
      this.setNewConfigV2('noRequirementFormFile', deploymentVariables.configuration?.noRequirementFormFile);
      this.setNewConfigV2('paymentOfFinancialObligations', deploymentVariables.configuration?.paymentOfFinancialObligations);
      this.setNewConfigV2('statusIdSyncNVTC', deploymentVariables.configuration?.statusIdSyncNVTC );
      this.setNewConfigV2('hcmTickAllTPHS', deploymentVariables.configuration?.hcmTickAllTPHS);
      this.setNewConfigV2('showMenuLGSPHCMLLTPVNeIDLog', deploymentVariables.configuration?.showMenuLGSPHCMLLTPVNeIDLog);
      this.setNewConfigV2('kiosDossierListParam', deploymentVariables.configuration?.kiosDossierListParam);
      this.setNewConfigV2('syncUpdateComposition', deploymentVariables.configuration?.syncUpdateComposition);
      this.setNewConfigV2('showWarningNotify', deploymentVariables.configuration?.showWarningNotify);
      this.setNewConfigV2('showComboboxAgency', deploymentVariables.configuration?.showComboboxAgency);
      this.setNewConfigV2('checkRefusedDossierOnce', deploymentVariables.configuration?.checkRefusedDossierOnce);
      this.setNewConfigV2('showDocumentAndoneGateExpertTCQ', deploymentVariables.configuration?.showDocumentAndoneGateExpertTCQ);
      this.setNewConfigV2('showDocumentCodeToEform', deploymentVariables.configuration?.showDocumentCodeToEform);
      this.setNewConfigV2('showPaymentRequestButtonDNI', deploymentVariables.configuration?.showPaymentRequestButtonDNI);
      this.setNewConfigV2('dontReloadChangProcess', deploymentVariables.configuration?.dontReloadChangProcess);
      this.setNewConfigV2('hideInvoiceAndPOSProcessingDossier', deploymentVariables.configuration?.hideInvoiceAndPOSProcessingDossier);
      this.setNewConfigV2('showNoteDocument', deploymentVariables.configuration?.showNoteDocument);
      this.setNewConfigV2('paymentRequestDetailProcessing', deploymentVariables.configuration?.paymentRequestDetailProcessing);
      this.setNewConfigV2('checkSignedFormFile', deploymentVariables.configuration?.checkSignedFormFile);
      this.setNewConfigV2('showCheckboxTTHCNoDigitized', deploymentVariables.configuration?.showCheckboxTTHCNoDigitized);
      this.setNewConfigV2('enableSectorOfSynchronizeProcedure', deploymentVariables.configuration?.enableSectorOfSynchronizeProcedure);
      this.setNewConfigV2('inputCheckCSDLDC', deploymentVariables.configuration?.inputCheckCSDLDC);
      this.setNewConfigV2('showSearchOrganization', deploymentVariables.configuration?.showSearchOrganization);
      this.setNewConfigV2('showPaymentRequestToDetailsDossier', deploymentVariables.configuration?.showPaymentRequestToDetailsDossier);
      this.setNewConfigV2('showRequestWithdrawDossierToDetailsDossier', deploymentVariables.configuration?.showRequestWithdrawDossierToDetailsDossier);
      this.setNewConfigV2('listPaymentMethodIdsToChange', deploymentVariables.configuration?.listPaymentMethodIdsToChange);
      this.setNewConfigV2('enableFeesDiscount', deploymentVariables.configuration?.enableFeesDiscount);
      this.setNewConfigV2('disableMenuRemindV2', deploymentVariables.configuration?.disableMenuRemindV2);
      this.setNewConfigV2('disableFilterAppliedDateV2', deploymentVariables.configuration?.disableFilterAppliedDateV2);
      this.setNewConfigV2('limitShowFunctionAfterDraftDossier', deploymentVariables.configuration?.limitShowFunctionAfterDraftDossier);
      this.setNewConfigV2('configWorkInterfaceKHA', deploymentVariables.configuration?.configWorkInterfaceKHA);
      this.setNewConfigV2('enableApplyExcelFormStatisticVpcpOnlineDNI', deploymentVariables.configuration?.enableApplyExcelFormStatisticVpcpOnlineDNI);
      this.setNewConfigV2('dossierCancelName', deploymentVariables.configuration?.dossierCancelName);
      this.setNewConfigV2('reportVpcpOnlineName', deploymentVariables.configuration?.reportVpcpOnlineName);
      this.setNewConfigV2('konTumListOnlinePaymentReportName', deploymentVariables.configuration?.konTumListOnlinePaymentReportName);
      this.setNewConfigV2('dossierSyncName', deploymentVariables.configuration?.dossierSyncName);
      this.setNewConfigV2('statisticProcedureBDGName', deploymentVariables.configuration?.statisticProcedureBDGName);
      this.setNewConfigV2('overdueDossierReportBDGName', deploymentVariables.configuration?.overdueDossierReportBDGName);
      this.setNewConfigV2('userStatisticsName', deploymentVariables.configuration?.userStatisticsName);
      this.setNewConfigV2('dossierFeeDBNReportName', deploymentVariables.configuration?.dossierFeeDBNReportName);
      this.setNewConfigV2('agencyIdProvince', deploymentVariables.configuration?.agencyIdProvince);
      this.setNewConfigV2('agencyIdDistrict', deploymentVariables.configuration?.agencyIdDistrict);
      this.setNewConfigV2('agencyIdWard', deploymentVariables.configuration?.agencyIdWard);
      this.setNewConfigV2('requestReturnDocument',  deploymentVariables.configuration?.requestReturnDocument);

      this.setNewConfigV2('onlineRecordProcessingLabel',  deploymentVariables.configuration?.onlineRecordProcessingLabel);
      this.setNewConfigV2('directRecordProcessingLabel',  deploymentVariables.configuration?.directRecordProcessingLabel);
      this.setNewConfigV2('recordsToBeProcessedLabel',  deploymentVariables.configuration?.recordsToBeProcessedLabel);
      this.setNewConfigV2('IsCustomMenuEnabled',  deploymentVariables.configuration?.IsCustomMenuEnabled);

      this.setNewConfigV2('showFormOfPayment', deploymentVariables.configuration?.showFormOfPayment);
      this.setNewConfigV2('showFeeColumn', deploymentVariables.configuration?.showFeeColumn);
      this.setNewConfigV2('showFeeColumnAgencyIds', deploymentVariables.configuration?.showFeeColumnAgencyIds);
      this.setNewConfigV2('adjustSYNCLLTPDossier', deploymentVariables.configuration?.adjustSYNCLLTPDossier);
      this.setNewConfigV2('showResultCode', deploymentVariables.configuration?.showResultCode);
      this.setNewConfigV2('processAutoFillAgency', deploymentVariables.configuration?.processAutoFillAgency);
      this.setNewConfigV2('listAgencyTypeDNI', deploymentVariables.configuration?.configuration?.listAgencyTypeDNI);
      this.setNewConfigV2('sameDayPayProfileBDH', deploymentVariables.configuration?.sameDayPayProfileBDH);
      this.setNewConfigV2('enableShowCSDLQGVDCBDH', deploymentVariables.configuration?.enableShowCSDLQGVDCBDH);
      this.setNewConfigV2('showInfoReplaceID', deploymentVariables.configuration?.showInfoReplaceID);
      this.setNewConfigV2('isInvoiceProviderActivated', deploymentVariables.configuration?.configuration?.isInvoiceProviderActivated);
      this.setNewConfigV2('isPaymentMethodActivated', deploymentVariables.configuration?.configuration?.isPaymentMethodActivated);
      this.setNewConfigV2('dniStatistics', deploymentVariables.configuration?.configuration?.dniStatistics);
      this.setNewConfigV2('viewOrderDetailsVNPOST', deploymentVariables.configuration?.configuration?.viewOrderDetailsVNPOST);
      this.setNewConfigV2('enableCheckCitizenOnlineReceptionNAST', deploymentVariables.configuration?.enableCheckCitizenOnlineReceptionNAST);
      this.setNewConfigV2('ignoreProcedureInPaymentReportNAST', deploymentVariables.configuration?.ignoreProcedureInPaymentReportNAST);
      this.setNewConfigV2('processingFlowStopProcessingNAST', deploymentVariables.configuration?.processingFlowStopProcessingNAST);
      this.setNewConfigV2('administrativeUnitMerge', deploymentVariables.configuration?.administrativeUnitMerge);
      this.setNewConfigV2('showMaxSizeAllFileLAN', deploymentVariables.configuration?.showMaxSizeAllFileLAN);
      this.setNewConfigV2('showAIClassifyDossier', deploymentVariables.configuration?.showAIClassifyDossier);
      this.setNewConfigV2('showAIClassifyDossierAgencyIds', deploymentVariables.configuration?.showAIClassifyDossierAgencyIds);
      this.setNewConfigV2('showAIClassifyDossierPercentageValid', deploymentVariables.configuration?.showAIClassifyDossierPercentageValid);
      this.setNewConfigV2('showAIClassifyAndUploadDossierName',  deploymentVariables.configuration?.showAIClassifyAndUploadDossierName);
      this.setNewConfigV2('showAIClassifyAndUploadDossierDocCode',  deploymentVariables.configuration?.showAIClassifyAndUploadDossierDocCode);
      this.setNewConfigV2('listAgencyAdvanceSearch', deploymentVariables.configuration?.listAgencyAdvanceSearch);
      this.setNewConfigV2('deadlineForPaymentDossierAgencyIds', deploymentVariables.configuration?.configuration?.deadlineForPaymentDossierAgencyIds);
      this.setNewConfigV2('deadlineForPaymentDossierNumberOfDays', deploymentVariables.configuration?.configuration?.deadlineForPaymentDossierNumberOfDays);
      this.setNewConfigV2('deadlineForPaymentDossier', deploymentVariables.configuration?.configuration?.deadlineForPaymentDossier);
      this.setNewConfigV2('addSignedFileAfterDigitalSignature', deploymentVariables.configuration?.addSignedFileAfterDigitalSignature);
      this.setNewConfigV2('addSignedFileAfterDigitalSignatureAgencyIds', deploymentVariables.configuration?.addSignedFileAfterDigitalSignatureAgencyIds);
      this.setNewConfigV2('enableBookList', deploymentVariables.configuration?.enableBookList);
      this.setNewConfigV2('configLedgerTypeId', deploymentVariables.configuration?.configLedgerTypeId);
      this.setNewConfigV2('configTemplateTypeId', deploymentVariables.configuration?.configTemplateTypeId);
      this.setNewConfigV2('enableTimeNowStatistic', deploymentVariables.configuration?.configuration?.enableTimeNowStatistic);
       this.setNewConfigV2('enableLoadprimaryAgency', deploymentVariables.configuration?.enableLoadprimaryAgency);
      this.setNewConfigV2('enableFilterAgencySectorBeforeSave', deploymentVariables.configuration?.configuration?.enableFilterAgencySectorBeforeSave);
      this.setNewConfigV2('notiSaveDoc', deploymentVariables.configuration?.notiSaveDoc);
      this.setNewConfigV2('isShowCheckboxDeletePermanentKHA', deploymentVariables.configuration?.isShowCheckboxDeletePermanentKHA);
      this.setNewConfigV2('adjustExcelExportNotReceivedNAST',  deploymentVariables.configuration?.adjustExcelExportNotReceivedNAST);
      this.setNewConfigV2('hiddenCancelDossierNAST',  deploymentVariables.configuration?.hiddenCancelDossierNAST);
      this.setNewConfigV2('applicationDVCTTNAST',  deploymentVariables.configuration?.applicationDVCTTNAST);
      this.setNewConfigV2('updateTitleForDocument', deploymentVariables.configuration?.updateTitleForDocument);
      this.setNewConfigV2('isShowTransferResponsibleUserKHA', deploymentVariables.configuration?.isShowTransferResponsibleUserKHA);
      this.setNewConfigV2('isAllowSelectAgencyLevelDNI', deploymentVariables.configuration?.isAllowSelectAgencyLevelDNI);
      this.setNewConfigV2('showPaymentDayLimit', deploymentVariables.configuration?.showPaymentDayLimit);
      this.setNewConfigV2('twoLevelPublicAdministration', deploymentVariables.configuration?.twoLevelPublicAdministration);
      this.setNewConfigV2('notifyVbdlis', deploymentVariables.configuration?.notifyVbdlis);
      this.setNewConfigV2('isShowSpecialAgencyLevelDNI', deploymentVariables.configuration?.isShowSpecialAgencyLevelDNI);
      this.setNewConfigV2('timeSheetIdDNI', deploymentVariables.configuration?.timeSheetIdDNI);
      this.setNewConfigV2('mainWorkingIdDNI', deploymentVariables.configuration?.mainWorkingIdDNI);
      this.setNewConfigV2('allowImageToPdfForSigning', deploymentVariables.configuration?.allowImageToPdfForSigning);
      this.setNewConfigV2('enableChangePaymentMethodDossier', deploymentVariables.configuration?.enableChangePaymentMethodDossier);
      this.setNewConfigV2('formatBusinessAddressCompact', deploymentVariables.configuration?.formatBusinessAddressCompact);
      this.setNewConfigV2('addTimeToOnlineMessage', deploymentVariables.configuration?.addTimeToOnlineMessage);
      this.setNewConfigV2('dniNonDigitizedProcedures', deploymentVariables.configuration?.dniNoDigitizedProcedures);
      this.setNewConfigV2('isLevel3', deploymentVariables.configuration?.isLevel3);
      this.setNewConfigV2('downloadTemplateAndProcImp', deploymentVariables.configuration?.downloadTemplateAndProcImp);
      this.setNewConfigV2('listTagAgencyForComboboxMerge', deploymentVariables.configuration?.listTagAgencyForComboboxMerge);
      this.setNewConfigV2('configViewProcessingKHA', deploymentVariables.configuration?.configViewProcessingKHA);
      this.setNewConfigV2('configViewStepByStepProcessKHA', deploymentVariables.configuration?.configViewStepByStepProcessKHA);
      this.setNewConfigV2('updateRequireFieldForPayReDossier', deploymentVariables.configuration?.updateRequireFieldForPayReDossier);
      this.setNewConfigV2('generalReportTitle', deploymentVariables.configuration?.generalReportTitle);
      this.setNewConfigV2('updateDossierHasEnded', deploymentVariables.configuration?.updateDossierHasEnded);
      this.setNewConfigV2('sortInvalidDocumentsFirst', deploymentVariables.configuration?.sortInvalidDocumentsFirst);
      this.setNewConfigV2('adjustVillageCombobox', deploymentVariables.configuration?.adjustVillageCombobox);
      this.setNewConfigV2('agencyProvinceParent', deploymentVariables.configuration?.agencyProvinceParent);
      this.setNewConfigV2('hiddenResendDossierHT', deploymentVariables.configuration?.hiddenResendDossierHT);
      this.setNewConfigV2('isShowSearchTrangThaiDVCLTHoTich', deploymentVariables.configuration?.isShowSearchTrangThaiDVCLTHoTich);
      this.setNewConfigV2('integrationDomain', deploymentVariables.configuration?.integrationDomain);
      this.setNewConfigV2('enableGetFileDossier', deploymentVariables.configuration?.enableGetFileDossier);
      this.setNewConfigV2('convertImageToPdfVbdlis', deploymentVariables.configuration?.convertImageToPdfVbdlis);
      this.setNewConfigV2('enableNotarizationRepository', deploymentVariables.configuration?.enableNotarizationRepository);
      return deploymentVariables.configuration;
    } else {
      return this.setAppDeployment();
      // setTimeout(() => {
      //   return this.getAppDeployment();
      // }, this.envService.getConfig().timeOut);
    }
  }

  getConfig(key): any {
    const config = this.getAppDeployment();
    return config[key];
  }

  getMapsConfig(): any {
    const config = this.getAppDeployment();
    return config.maps;
  }

  getRootUrl(){
    return this.envService.getConfig()?.apiProviders?.digo?.rootUrl + localStorage.getItem('language');
  }

  setEnvVariables(env) {
    if (!env) {
      return;
    }

    this.env = this.mergeDeep(this.env, env?.env);
    this.newConfigV2 = this.mergeDeepNewV2(this.newConfigV2, env);
  }

  setNotifyQNIVariables(notifyqni) {
    if (!notifyqni) {
      return;
    }

    this.notifyqni = this.mergeDeep(this.notifyqni, notifyqni);
  }
  setSyncConstructHPGVariables(syncConstructHPG) {
    if (!syncConstructHPG) {
      return;
    }

    this.syncConstructHPG = this.mergeDeep(this.syncConstructHPG, syncConstructHPG);
  }

  setTooltipReportGeneralQNIVariables(tooltipReportGeneral) {
    if (!tooltipReportGeneral) {
      return;
    }

    this.tooltipReportGeneral = tooltipReportGeneral;
  }

  setChungThucDienTuVariables(chungThucDienTu) {
    if (!chungThucDienTu) {
      return;
    }

    this.chungThucDienTu = this.mergeDeep(this.chungThucDienTu, chungThucDienTu);
  }

  setHeaderVariables(header) {
    if (!header) {
      return;
    }

    this.header = this.mergeDeep(this.header, header);
  }

  setCloneDossier (cloneDossier) {
    if (!cloneDossier) {
      return;
    }

    this.cloneDossier = this.mergeDeep(this.cloneDossier, cloneDossier);
  }

  setNewConfigV2 (key, value) {
    if (!key || !value) {
      return;
    }

    if (this[key] === undefined) {
      this[key] = {};
    }
    this[key] = this.mergeDeep(this[key], value);
  }

  isObject(item) {
    return (item && typeof item === 'object' && !Array.isArray(item) && item !== null);
  }

  mergeDeep(target, source) {
    if (this.isObject(target) && this.isObject(source)) {
      for (const key in source) {
        if (this.isObject(source[key])) {
          if (!target[key]) Object.assign(target, { [key]: {} });
          this.mergeDeep(target[key], source[key]);
        } else {
          Object.assign(target, { [key]: source[key] });
        }
      }
    }
    return target;
  }

  mergeDeepNewV2(target, source) {
    if (this.isObject(target) && this.isObject(source)) {
      for (const key in source) {
        if(key != 'env' && key != 'notifyqni' && key != 'chungThucDienTu' && key != 'header' && key != 'tooltipReportGeneral' && key != 'cloneDossier'){
          if (this.isObject(source[key])) {
            if (!target[key]) Object.assign(target, { [key]: {} });
            this.mergeDeep(target[key], source[key]);
          } else {
            Object.assign(target, { [key]: source[key] });
          }
        }
      }
    }
    return target;
  }

}
