<div class="prc_searchbar">
    <form class="searchForm" [formGroup]="searchForm">
        <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="row">
            <mat-form-field class="example-full-width" appearance="outline" fxLayout.sm="row" fxFlex.gt-sm="25"
                fxFlex.gt-xs="49.5" fxFlex='grow'>
                <mat-label i18n>Từ ngày</mat-label>
                <input formControlName="fromDate" matInput [matDatepicker]="picker1" [(ngModel)]="fromDate">
                <mat-datepicker-toggle matSuffix [for]="picker1"></mat-datepicker-toggle>
                <mat-datepicker #picker1></mat-datepicker>
            </mat-form-field>
            <div fxFlex='1'></div>
            <mat-form-field class="example-full-width" appearance="outline" fxLayout.sm="row" fxFlex.gt-sm="25"
                fxFlex.gt-xs="49.5" fxFlex='grow'>
                <mat-label i18n><PERSON><PERSON><PERSON> ng<PERSON>y</mat-label>
                <input formControlName="toDate" matInput [matDatepicker]="picker2" [(ngModel)]="toDate">
                <mat-datepicker-toggle matSuffix [for]="picker2"></mat-datepicker-toggle>
                <mat-datepicker #picker2></mat-datepicker>
            </mat-form-field>
            <div fxFlex='1'></div>
            <mat-form-field appearance="outline" fxFlex.gt-sm="32" fxFlex.gt-xs="32" fxFlex='grow' fxFlex.gt-xs="49.5">
                <mat-label>Cơ quan</mat-label>
                <mat-select msInfiniteScroll (infiniteScroll)="getAgencyList(true)" formControlName="agency"
                                [complete]="isAgencyListFull">
                        <mat-option>
                            <ngx-mat-select-search formControlName="agencyCtrl" placeholderLabel=""
                                                   (keyup)="searchAgencyList()"
                                                   [disableScrollToActiveOnOptionsChanged]="true"
                                                   [clearSearchInput]="false"
                                                   i18n-noEntriesFoundLabel
                                                   noEntriesFoundLabel="Không tìm thấy kết quả nào!">
                                <mat-icon ngxMatSelectSearchClear (click)="clearAgencyList()">close</mat-icon>
                            </ngx-mat-select-search>
                        </mat-option>
                        <mat-option value="" i18n>Tất cả</mat-option>
                        <mat-option *ngFor="let agency of agencyList" value="{{agency.id}}">
                            {{agency.name}}
                            <span *ngIf="agency.name == undefined || agency.name == null" >(Không tìm thấy bản dịch)</span>
                        </mat-option>
                    </mat-select>
            </mat-form-field>
            <div fxFlex='1'></div>
            <button type='button' mat-flat-button fxFlex.gt-sm="12" fxFlex='12' class="btn-download-excel"
                (click)="search(true)">
                <mat-icon class="iconStatistical" *ngIf="!isLoading">cloud_download</mat-icon>
                <span i18n>Xuất excel</span>
                <mat-progress-bar mode="indeterminate" *ngIf="isLoading"></mat-progress-bar>
            </button>
            <div fxFlex='1'></div>
            <button type="submit" (click)="search(false)" mat-flat-button fxFlex.gt-sm="12" fxFlex='12' class="btn-search">
                <mat-icon class="iconStatistical" *ngIf="!isLoading">bar_chart</mat-icon>
                <span i18n>Thống kê</span>
                <mat-progress-bar mode="indeterminate" *ngIf="isLoading"></mat-progress-bar>
            </button>
        </div>
    </form>
</div>
<div fxLayout="row" fxLayoutAlign="center">
    <div class="frm_main" fxFlex="grow">
        <div class="logbookTbl">
            <div>
                <table class="table">
                    <thead>
                        <tr>
                            <th rowspan="3">STT</th>
                            <th colspan="1" rowspan="3"> Tên cơ quan</th>
                            <th rowspan="2">Số HS tiếp nhận</th>
                            <th colspan="3">Số hoá hồ sơ TTHC khi tiếp nhận (TPHS)</th>
                            <th rowspan="2">Số HS đã giải quyết</th>
                            <th colspan="3">Số hoá kết quả giải quyết TTHC (KQ)</th>
                        </tr>
                        <tr>
                            <th colspan="1">Số HS có số hoá TPHS</th>
                            <th colspan="1">Tỷ lệ số hoá TPHS</th>
                            <th colspan="1">Số HS chưa số hoá TPHS</th>
                            <th colspan="1">Số HS có số hoá KQ</th>
                            <th colspan="1">Tỷ lệ số hoá KQ</th>
                            <th colspan="1">Số HS chưa số hoá KQ</th>
                        </tr>
                        <tr>
                            <th>(1)</th>
                            <th>(2)</th>
                            <th>(3)=(2)/(1)</th>
                            <th>(4)</th>
                            <th>(5)</th>
                            <th>(6)</th>
                            <th>(7)=(6)/(5)</th>
                            <th>(8)</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- Body tinh -->
                        <tr *ngIf="dsCapTinh.length > 0" class="sumCacCapText">
                            <td>I</td>
                            <td colspan="9" style="text-align: start;">CẤP TỈNH</td>
                        </tr>
                        <ng-container *ngFor="let item of dsCapTinh; index as i">
                            <tr>
                                <td>{{i+1}}</td>
                                <td style="text-align: start;">{{item.agency}}</td>
                                <td>{{item.soHoaHoSoTiepNhan}}</td>
                                <td>
                                    <a (click)="addFormDialog(item.agencyId,0,item.hsCoSoHoaTPHS,item.agency)"
                                        *ngIf="item.hsCoSoHoaTPHS>0" class="titleText">{{item.hsCoSoHoaTPHS}}</a>
                                    <label *ngIf="item.hsCoSoHoaTPHS==0">{{item.hsCoSoHoaTPHS}}</label>
                                </td>
                                <td>{{formatRation(item.tyLeSoHoaTPHS)}}</td>
                                <td>
                                    <a (click)="addFormDialog(item.agencyId,1,item.hsChuaSoHoaTPHS,item.agency)"
                                        *ngIf="item.hsChuaSoHoaTPHS>0" class="titleText">{{item.hsChuaSoHoaTPHS}}</a>
                                    <label *ngIf="item.hsChuaSoHoaTPHS==0">{{item.hsChuaSoHoaTPHS}}</label>
                                </td>
                                <td>{{item.soHsDaGiaiQuyet}}</td>
                                <td><a (click)="addFormDialog(item.agencyId,2,item.soHsCoSoHoaKQ,item.agency)"
                                        *ngIf="item.soHsCoSoHoaKQ>0" class="titleText">{{item.soHsCoSoHoaKQ}}</a>
                                    <label *ngIf="item.soHsCoSoHoaKQ==0">{{item.soHsCoSoHoaKQ}}</label>
                                </td>
                                <td>{{formatRation(item.tyLeSoHoaKQ)}}</td>
                                <td><a (click)="addFormDialog(item.agencyId,3,item.soHsChuaSoHoaKQ,item.agency)"
                                        *ngIf="item.soHsChuaSoHoaKQ>0" class="titleText">{{item.soHsChuaSoHoaKQ}}</a>
                                    <label *ngIf="item.soHsChuaSoHoaKQ==0">{{item.soHsChuaSoHoaKQ}}</label>
                                </td>
                            </tr>
                        </ng-container>
                        <tr *ngIf="dsCapTinh.length > 0" class="titleSum">
                            <td colspan="2">TỔNG CẤP TỈNH</td>
                            <td>{{sum("soHoaHoSoTiepNhan", dsCapTinh)}}</td>
                            <td>{{sum("hsCoSoHoaTPHS", dsCapTinh)}}</td>
                            <td>{{calTyLeSoHoaTPHS(dsCapTinh)}}</td>
                            <td>{{sum("hsChuaSoHoaTPHS", dsCapTinh)}}</td>
                            <td>{{sum("soHsDaGiaiQuyet", dsCapTinh)}}</td>
                            <td>{{sum("soHsCoSoHoaKQ", dsCapTinh)}}</td>
                            <td>{{calTyLeSoHoaKQ(dsCapTinh)}}</td>
                            <td>{{sum("soHsChuaSoHoaKQ", dsCapTinh)}}</td>
                        </tr>

                        <!-- Body huyen -->
                        <tr *ngIf="dsCapHuyen.length > 0" class="sumCacCapText">
                            <td>II</td>
                            <td colspan="9" style="text-align: start;">
                                CẤP HUYỆN</td>
                        </tr>
                        <ng-container *ngFor="let item of dsCapHuyen; index as i">
                            <tr>
                                <td>{{i+1}}</td>
                                <td style="text-align: start;">{{item.agency}}</td>
                                <td>{{item.soHoaHoSoTiepNhan}}</td>
                                <td>
                                    <a (click)="addFormDialog(item.agencyId,0,item.hsCoSoHoaTPHS,item.agency)"
                                        *ngIf="item.hsCoSoHoaTPHS>0" class="titleText">{{item.hsCoSoHoaTPHS}}</a>
                                    <label *ngIf="item.hsCoSoHoaTPHS==0">{{item.hsCoSoHoaTPHS}}</label>
                                </td>
                                <td>{{formatRation(item.tyLeSoHoaTPHS)}}</td>
                                <td>
                                    <a (click)="addFormDialog(item.agencyId,1,item.hsChuaSoHoaTPHS,item.agency)"
                                        *ngIf="item.hsChuaSoHoaTPHS>0" class="titleText">{{item.hsChuaSoHoaTPHS}}</a>
                                    <label *ngIf="item.hsChuaSoHoaTPHS==0">{{item.hsChuaSoHoaTPHS}}</label>
                                </td>
                                <td>
                                    {{item.soHsDaGiaiQuyet}}
                                </td>
                                <td><a (click)="addFormDialog(item.agencyId,2,item.soHsCoSoHoaKQ,item.agency)"
                                        *ngIf="item.soHsCoSoHoaKQ>0" class="titleText">{{item.soHsCoSoHoaKQ}}</a>
                                    <label *ngIf="item.soHsCoSoHoaKQ==0">{{item.soHsCoSoHoaKQ}}</label>
                                </td>
                                <td>{{formatRation(item.tyLeSoHoaKQ)}}</td>
                                <td><a (click)="addFormDialog(item.agencyId,3,item.soHsChuaSoHoaKQ,item.agency)"
                                        *ngIf="item.soHsChuaSoHoaKQ>0" class="titleText">{{item.soHsChuaSoHoaKQ}}</a>
                                    <label *ngIf="item.soHsChuaSoHoaKQ==0">{{item.soHsChuaSoHoaKQ}}</label>
                                </td>
                            </tr>
                        </ng-container>
                        <tr *ngIf="dsCapHuyen.length > 0" class="titleSum">
                            <td colspan="2">TỔNG CẤP HUYỆN</td>
                            <td>{{sum("soHoaHoSoTiepNhan", dsCapHuyen)}}</td>
                            <td>{{sum("hsCoSoHoaTPHS", dsCapHuyen)}}</td>
                            <td>{{calTyLeSoHoaTPHS(dsCapHuyen)}}</td>
                            <td>{{sum("hsChuaSoHoaTPHS", dsCapHuyen)}}</td>
                            <td>{{sum("soHsDaGiaiQuyet", dsCapHuyen)}}</td>
                            <td>{{sum("soHsCoSoHoaKQ", dsCapHuyen)}}</td>
                            <td>{{calTyLeSoHoaKQ(dsCapHuyen)}}</td>
                            <td>{{sum("soHsChuaSoHoaKQ", dsCapHuyen)}}</td>
                        </tr>
                        <!-- Body xa -->
                        <tr *ngIf="dsCapXa.length > 0" class="sumCacCapText">
                            <td>II</td>
                            <td colspan="9" style="text-align: start;">CẤP XÃ</td>
                        </tr>
                        <ng-container *ngFor="let item of dsCapXa; index as i">
                            <tr>
                                <td>{{i+1}}</td>
                                <td style="text-align: start;">{{item.agency}}</td>
                                <td>{{item.soHoaHoSoTiepNhan}}</td>
                                <td>
                                    <a (click)="addFormDialog(item.agencyId,0,item.hsCoSoHoaTPHS,item.agency)"
                                        *ngIf="item.hsCoSoHoaTPHS>0" class="titleText">{{item.hsCoSoHoaTPHS}}</a>
                                    <label *ngIf="item.hsCoSoHoaTPHS==0">{{item.hsCoSoHoaTPHS}}</label>
                                </td>
                                <td>{{formatRation(item.tyLeSoHoaTPHS)}}</td>
                                <td>
                                    <a (click)="addFormDialog(item.agencyId,1,item.hsChuaSoHoaTPHS,item.agency)"
                                        *ngIf="item.hsChuaSoHoaTPHS>0" class="titleText">{{item.hsChuaSoHoaTPHS}}</a>
                                    <label *ngIf="item.hsChuaSoHoaTPHS==0">{{item.hsChuaSoHoaTPHS}}</label>
                                </td>
                                <td>{{item.soHsDaGiaiQuyet}}</td>
                                <td><a (click)="addFormDialog(item.agencyId,2,item.soHsCoSoHoaKQ,item.agency)"
                                        *ngIf="item.soHsCoSoHoaKQ>0" class="titleText">{{item.soHsCoSoHoaKQ}}</a>
                                    <label *ngIf="item.soHsCoSoHoaKQ==0">{{item.soHsCoSoHoaKQ}}</label>
                                </td>
                                <td>{{formatRation(item.tyLeSoHoaKQ)}}</td>
                                <td><a (click)="addFormDialog(item.agencyId,3,item.soHsChuaSoHoaKQ,item.agency)"
                                        *ngIf="item.soHsChuaSoHoaKQ>0" class="titleText">{{item.soHsChuaSoHoaKQ}}</a>
                                    <label *ngIf="item.soHsChuaSoHoaKQ==0">{{item.soHsChuaSoHoaKQ}}</label>
                                </td>
                            </tr>
                        </ng-container>
                        <tr *ngIf="dsCapXa.length > 0" class="titleSum">
                            <td colspan="2">TỔNG CẤP XÃ</td>
                            <td>{{sum("soHoaHoSoTiepNhan", dsCapXa)}}</td>
                            <td>{{sum("hsCoSoHoaTPHS", dsCapXa)}}</td>
                            <td>{{calTyLeSoHoaTPHS(dsCapXa)}}</td>
                            <td>{{sum("hsChuaSoHoaTPHS", dsCapXa)}}</td>
                            <td>{{sum("soHsDaGiaiQuyet", dsCapXa)}}</td>
                            <td>{{sum("soHsCoSoHoaKQ", dsCapXa)}}</td>
                            <td>{{calTyLeSoHoaKQ(dsCapXa)}}</td>
                            <td>{{sum("soHsChuaSoHoaKQ", dsCapXa)}}</td>
                        </tr>

                        <!-- Body Loc -->
                        <ng-container *ngFor="let item of dsLoc; index as i">
                            <tr>
                                <td>{{i+1}}</td>
                                <td style="text-align: start;">{{item.agency}}</td>
                                <td>{{item.soHoaHoSoTiepNhan}}</td>
                                <td>
                                    <a (click)="addFormDialog(item.agencyId,0,item.hsCoSoHoaTPHS,item.agency)"
                                        *ngIf="item.hsCoSoHoaTPHS>0" class="titleText">{{item.hsCoSoHoaTPHS}}</a>
                                    <label *ngIf="item.hsCoSoHoaTPHS==0">{{item.hsCoSoHoaTPHS}}</label>
                                </td>
                                <td>{{formatRation(item.tyLeSoHoaTPHS)}}</td>
                                <td>
                                    <a (click)="addFormDialog(item.agencyId,1,item.hsChuaSoHoaTPHS,item.agency)"
                                        *ngIf="item.hsChuaSoHoaTPHS>0" class="titleText">{{item.hsChuaSoHoaTPHS}}</a>
                                    <label *ngIf="item.hsChuaSoHoaTPHS==0">{{item.hsChuaSoHoaTPHS}}</label>
                                </td>
                                <td>{{item.soHsDaGiaiQuyet}}</td>
                                <td><a (click)="addFormDialog(item.agencyId,2,item.soHsCoSoHoaKQ,item.agency)"
                                        *ngIf="item.soHsCoSoHoaKQ>0" class="titleText">{{item.soHsCoSoHoaKQ}}</a>
                                    <label *ngIf="item.soHsCoSoHoaKQ==0">{{item.soHsCoSoHoaKQ}}</label>
                                </td>
                                <td>{{formatRation(item.tyLeSoHoaKQ)}}</td>
                                <td><a (click)="addFormDialog(item.agencyId,3,item.soHsChuaSoHoaKQ,item.agency)"
                                        *ngIf="item.soHsChuaSoHoaKQ>0" class="titleText">{{item.soHsChuaSoHoaKQ}}</a>
                                    <label *ngIf="item.soHsChuaSoHoaKQ==0">{{item.soHsChuaSoHoaKQ}}</label>
                                </td>
                            </tr>
                        </ng-container>
                        <tr *ngIf="dsLoc.length > 0" class="sumCacCapText">
                            <td colspan="2">TỔNG</td>
                            <td>{{sum("soHoaHoSoTiepNhan",dsLoc)}}</td>
                            <td>{{sum("hsCoSoHoaTPHS",dsLoc)}}</td>
                            <td>{{calTyLeSoHoaTPHS(dsLoc)}}</td>
                            <td>{{sum("hsChuaSoHoaTPHS",dsLoc)}}</td>
                            <td>{{sum("soHsDaGiaiQuyet",dsLoc)}}</td>
                            <td>{{sum("soHsCoSoHoaKQ",dsLoc)}}</td>
                            <td>{{calTyLeSoHoaKQ(dsLoc)}}</td>
                            <td>{{sum("soHsChuaSoHoaKQ",dsLoc)}}</td>
                        </tr>

                    </tbody>
                    <tfoot *ngIf="dsCapHuyen.length > 0 || dsCapTinh.length > 0">
                        <tr class="sumToanTinhText">
                            <td colspan="2">TỔNG TOÀN TỈNH</td>
                            <td>{{sum("soHoaHoSoTiepNhan", dataSource.data)}}</td>
                            <td>{{sum("hsCoSoHoaTPHS", dataSource.data)}}</td>
                            <td>{{calTyLeSoHoaTPHS(dataSource.data)}}</td>
                            <td>{{sum("hsChuaSoHoaTPHS", dataSource.data)}}</td>
                            <td>{{sum("soHsDaGiaiQuyet", dataSource.data)}}</td>
                            <td>{{sum("soHsCoSoHoaKQ", dataSource.data)}}</td>
                            <td>{{calTyLeSoHoaKQ(dataSource.data)}}</td>
                            <td>{{sum("soHsChuaSoHoaKQ", dataSource.data)}}</td>
                        </tr>
                    </tfoot>
                </table>
            </div>
        </div>
    </div>
</div>