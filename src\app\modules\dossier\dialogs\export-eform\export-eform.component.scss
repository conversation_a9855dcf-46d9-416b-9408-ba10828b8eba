::ng-deep .exportFile .btnSecondary {
    color: #ce7a58;
    border: 1px solid #ce7a58;
    margin: 0.5em;
    .mat-icon {
        margin-right: 0.3em;
        vertical-align: middle;
        align-self: center;
    }
    .mat-button-wrapper {
        display: flex;
        justify-content: center;
        .mat-spinner {
            margin-right: 0.3em;
            align-self: center;
            circle {
                stroke: #ce7a58;
            }
        }
    }
}

.menuAction .file_icon {
    width: 2em;
    height: 2em;
    background-position: center;
    background-size: 170%;
    align-self: center;
    background-repeat: no-repeat;
    margin-right: 0px !important;
}
.dialog_title {
    font-style: normal;
    font-weight: 500;
    font-size: 20px;
    color: #000000;
}

.btn_next {
    background-color: #ce7a58;
    color: #fff;
    // width: 10em;
    margin: 0.2em 0.5em;
}

::ng-deep .addForm {
    .addBtn {
        margin-top: 1em;
        background-color: #ce7a58;
        color: #fff;
        height: 3em;
    }

    .mat-form-field-appearance-outline {
        .mat-form-field-wrapper {
            padding-bottom: 2px;
        }

        &.mat-focused {
            .mat-form-field-outline-thick {
                color: #33333350 !important;
            }

            .mat-form-field-label {
                color: #ce7a58 !important;
            }
        }
    }

}

::ng-deep .mat-option-text {
    font-size: 14px !important;
}

.close-button {
    float: right;
    top: -24px;
    right: -24px;
}
