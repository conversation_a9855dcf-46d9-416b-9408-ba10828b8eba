<button mat-icon-button class="close-button" (click)="onDismiss()">
    <mat-icon>close</mat-icon>
</button>
<h3 class="dialog_title" mat-dialog-title i18n><PERSON><PERSON><PERSON> c<PERSON><PERSON> bổ sung</h3>
<div mat-dialog-content class="dialog_content">
    <span i18n><PERSON><PERSON><PERSON> có chắc chắn muốn gửi yêu cầu bổ sung cho hồ sơ </span><span
        class="highlight">{{dossierCode}}</span><span>?</span>
  <form [formGroup]="updateForm" class="updateForm" id="ngupdateForm">
    <div fxLayout="column" fxLayout.xs="column" fxLayout.sm="column" class="formFieldOutline"
         fxLayoutAlign="space-between">
      <mat-form-field appearance="outline" fxFlex.gt-sm="column" fxFlex.gt-xs="column" fxFlex='grow'
                      *ngIf="visibleApprovalAgency == 1 && approvalAgency.length >= 1">
        <mat-label i18n="@@approvaledAgency"><PERSON><PERSON> quan phê duyệt</mat-label>
        <mat-select formControlName="approvalAgencyId">
          <mat-option value=""></mat-option>
          <mat-option *ngFor='let item of approvalAgency ; let i = index' value="{{item.id}}">
            {{ item.DisplayName }}
          </mat-option>
        </mat-select>
      </mat-form-field>
    </div>
  </form>
    <div class="commentEditor" fxLayout="column" fxLayout.xs="column" fxLayout.sm="column">
        <p class="lbl"><span i18n>Lý do</span><span style="color:#ce7a58;">&nbsp;*</span></p>
        <ckeditor debounce="10" [editor]="Editor" class="customCKEditor" (change)="onCommentEditorChange($event)" [formControl]="reason"
            fxFlex='grow' [config]='commentConfig'>
        </ckeditor>
        <div class="errorMsg" *ngIf="isCKMaxlenght">
            <!-- <span i18n>Nội dung không quá 500 ký tự</span> -->
            <span>{{ckeditorMaxLengthNotification}}</span>
            <div class="err">
                <mat-icon>priority_high</mat-icon>
            </div>
        </div>
    </div>
    
    <div class="commentEditor" fxLayout="column" fxLayout.xs="column" fxLayout.sm="column">
        <p class="lbl"><span i18n>Nội dung</span><span style="color:#ce7a58;">&nbsp;*</span></p>
        <ckeditor debounce="10" [editor]="Editor" class="customCKEditor" (change)="onContentEditorChange($event)" [formControl]="tittle"
            fxFlex='grow' [config]='descriptionConfig'>
        </ckeditor>
        <div class="errorMsg" *ngIf="isCKDescriptionMaxlenght">
            <!-- <span i18n>Nội dung không quá 500 ký tự</span> -->
            <span>{{ckeditorMaxLengthNotification}}</span>
            <div class="err">
                <mat-icon>priority_high</mat-icon>
            </div>
        </div>
    </div>
    <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="column" style="margin: 0 0 1em 0" *ngIf="enableShowExportFileBtn">
        <button mat-flat-button fxFlex='20' class="applyBtn" (click)="changeFile('docx')">
            <span>Kết xuất file</span>
        </button>
    </div>

    <div>
        <span class="lbl title-weight"><span i18n>Chọn tệp văn bản yêu cầu bổ sung đã ký</span><span style="color:#ce7a58;" *ngIf="checkRequireAdditionalRequest">&nbsp;*</span></span>
        <!-- <button mat-flat-button fxFlex='20' class="downloadBtn">
            <mat-icon>cloud_download</mat-icon>
            <span>Tải file mẫu</span>
        </button> -->
        <span class="downloadBtn" (click)="downloadTemplate()" *ngIf="!!fileTemplate">
            <mat-icon class="material-icons-outlined">cloud_download</mat-icon>
            <span class="download-text">Tải file mẫu</span>
        </span>
    </div>
    <div [ngClass]="{'file_uploaded': uploaded == true}" fxHide.lt-md class="marginbottom">
        <div class="drag_upload_btn" [ngClass]="{'no_boder': uploaded == true}">
            <button mat-button [ngClass]="{'btn_uploaded': uploaded == true, 'clear_file_queue': uploaded == false}"
                fxFlex='grow'>
                <mat-icon class="material-icons-outlined">cloud_upload</mat-icon> <a href="">
                    <span i18n>Kéo thả tệp tin hoặc </span><span class="txtUpload" i18n>Tải lên</span>
                </a>
                <div>
                    <span>Kích thước tối đa của một tệp tin:</span> {{ maxFileSize }}MB
                    <div *ngIf="showButtonGetFilesFromResult">
                        <button mat-button (click)="getFileFromResult()" style="padding: 0px !important; z-index: 9999999; max-width: 30%; background-color: #CE7A58; color: white;">Lấy file từ kết quả xử lý</button>
                    </div>
                </div>
            </button>
            <input id="file_upload" name="file_upload[]" type='file' (change)="onSelectFile($event)" multiple
                [accept]="acceptFileExtension" value="{{blankVal}}" fxFlex='grow'>
        </div>
        <div class="file_drag_upload_preview">
            <div class="list_uploaded" *ngFor='let url of urls; let i = index;'>
                <div class="file_icon" [ngStyle]="{'background-image': 'url('+ urls[i] +')'}"></div>
                <span class="file_name" matTooltip="{{uploadFileNames[i].filename}}"
                    [matTooltipPosition]="'right'">{{fileNames[i]}}</span>
                
                <button mat-icon-button class="deleteFile" [matMenuTriggerFor]="actionMenu">
                    <mat-icon>more_horiz</mat-icon>
                  </button>
                  <mat-menu #actionMenu="matMenu" xPosition="before">
                    <a mat-menu-item class="menuAction" target="_blank" [routerLink]="routerLink(uploadFileNames[i])" >
                        <mat-icon>format_size</mat-icon>
                        <span i18n>Xem trước</span>
                      </a>
                      <a mat-menu-item class="menuAction" (click)="downloadFileExport(i)">
                        <mat-icon>cloud_download</mat-icon>
                        <span i18n>Tải xuống tệp tin</span>
                    </a>
                      <a mat-menu-item class="menuAction" (click)="removeItem(i)">
                        <mat-icon>delete_outline</mat-icon>
                        <span i18n>Xóa</span>
                    </a>
                    <a mat-menu-item class="menuAction" (click)="openPdfDigitalSignatureV2( uploadFileNames[i], i, 1)" *ngIf="digitalSignature.VNPTSim && checkIfFileIsSupported(uploadFileNames[i].filename)">
                        <mat-icon>verified</mat-icon>
                        <span>Ký số sim</span>
                    </a>
                      <a mat-menu-item class="menuAction" (click)="openPdfDigitalSignature( uploadFileNames[i].id, uploadFileNames[i].filename,i)" *ngIf="digitalSignature.SmartCA && checkIfFileIsSupported(uploadFileNames[i].filename)">
                        <mat-icon>verified</mat-icon>
                        <span>Ký số Smart CA</span>
                      </a>
                      <a mat-menu-item class="menuAction" (click)="openVGCAplugin( uploadFileNames[i].id, uploadFileNames[i].filename, uploadFileNames[i].size)" *ngIf="digitalSignature.VGCA && checkIfFileIsSupported(uploadFileNames[i].filename)">
                        <mat-icon>verified</mat-icon>
                        <span *ngIf="!isEditSignTokenName; then thenBlock else elseBlock"></span>
                        <ng-template #thenBlock>Ký số ban cơ yếu</ng-template>
                        <ng-template #elseBlock>Ký số Token</ng-template>
                      </a>
                      <a mat-menu-item class="menuAction" (click)="openVnptCaPlugin(uploadFileNames[i].id, uploadFileNames[i].filename)" *ngIf="digitalSignature.VNPTCA && (checkIfFileIsSupported(uploadFileNames[i].filename) || checkIfDocFileOnly(uploadFileNames[i].filename))">
                        <mat-icon>verified</mat-icon>
                        <span>Ký số VNPT-CA</span>
                      </a>
                      <a mat-menu-item class="menuAction" (click)="openNEAC( uploadFileNames[i], i, 5)" *ngIf="digitalSignature.NEAC && checkIfFileIsSupported(uploadFileNames[i].filename)">
                        <mat-icon class="mainColor">verified</mat-icon>
                        <span i18n="@@digitalSignNEAC">Ký số NEAC</span>
                      </a>
                      <a mat-menu-item class="menuAction" (click)="openPdfDigitalSignatureV2(  uploadFileNames[i], i, 6)" *ngIf="digitalSignature.QNM && checkIfFileIsSupported(uploadFileNames[i].filename)">
                        <mat-icon class="mainColor">verified</mat-icon>
                        <span i18n="@@QNMDigitalSignature">Ký số tập trung QNM</span>
                      </a>
                </mat-menu>
            </div>
        </div>
    </div>
    <div class="res_uploadFile marginbottom" fxShow="true" fxHide.gt-sm>
        <div class="res_upload_btn">
            <button mat-button fxFlex='grow'>
                <mat-icon class="material-icons-outlined">cloud_upload</mat-icon>
                <span class="txtUpload" i18n>Tải lên</span>
            </button>
            <input id="file_upload" name="file_upload[]" type='file' (change)="onSelectFile($event)" multiple
                [accept]="acceptFileExtension" value="{{blankVal}}" fxFlex='grow'>
        </div>
        <div class="res_upload_preview">
            <div class="list_uploaded" *ngFor='let url of urls; let i = index;' fxFlex.gt-sm="49.5" fxFlex.gt-xs="48.5"
                fxFlex='grow'>
                <div class="file_icon" [ngStyle]="{'background-image': 'url('+ urls[i] +')'}"></div>
                <span class="file_name" matTooltip="{{fileNamesFull[i]}}"
                    [matTooltipPosition]="'right'" >{{fileNames[i]}}</span>
                    <button mat-icon-button class="deleteFile" [matMenuTriggerFor]="actionMenu">
                        <mat-icon>more_horiz</mat-icon>
                      </button>
                      <mat-menu #actionMenu="matMenu" xPosition="before">
                        <a mat-menu-item class="menuAction" target="_blank" [routerLink]="routerLink(uploadFileNames[i])" >
                            <mat-icon>format_size</mat-icon>
                            <span i18n>Xem trước</span>
                          </a>
                          <a mat-menu-item class="menuAction" (click)="downloadFileExport(i)">
                            <mat-icon>cloud_download</mat-icon>
                            <span i18n>Tải xuống tệp tin</span>
                        </a>
                          <a mat-menu-item class="menuAction" (click)="removeItem(i)">
                            <mat-icon>delete_outline</mat-icon>
                            <span i18n>Xóa</span>
                        </a>
                    </mat-menu>
            </div>
        </div>
    </div>
</div>
<div *ngIf="numberDateAdditionalRequirement && numberDateAdditionalRequirement.enable">
    <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="column" style="padding: 10px 0;font-weight: 500;color: green;">
        <mat-checkbox [(ngModel)]="checkNumberDate" (change)="onCheckNumberDateChange($event)"><span i18n="@@additionalRequirementDate">Hạn bổ sung hồ sơ:</span>
        </mat-checkbox>
    </div>
    <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="column" >
        <mat-form-field appearance="outline">
            <mat-label i18n="@@additionalRequirementNumberDate">Số ngày chờ bổ sung</mat-label>
            <input matInput type="number" [readonly]="!checkNumberDate" [max]="numberDateAdditionalRequirement.maxNumber" [(ngModel)]="numberDate" (change)="onNumberDateChange($event)">
        </mat-form-field>
    </div>
</div>
<div *ngIf="enablePauseWhenAdditional && typeProcess !== 2" fxLayout="row" fxLayout.xs="column" fxLayout.sm="column" fxLayoutAlign="end center" fxLayoutGap="16px" class="addition-request-due-date">
    <mat-checkbox [(ngModel)]="isDueDateChecked">
        <span class="title">Hạn bổ sung hồ sơ</span>
    </mat-checkbox>
    <mat-form-field class="no-padding-field" appearance="outline" fxLayout.sm="row" fxFlex.gt-md="24" fxFlex.gt-sm="32" fxFlex.gt-xs="49.5" fxFlex="grow">
        <mat-label>Hạn bổ sung</mat-label>
        <input class="no-padding-field" matInput [matDatepicker]="pickerAdditionDueDate" [(ngModel)]="additionRequestDueDate" [min]="additionRequestMinDate" name="additionDueDate" [disabled]="!isDueDateChecked"/>
        <mat-datepicker-toggle class="no-padding-field" matSuffix [for]="pickerAdditionDueDate"></mat-datepicker-toggle>
        <mat-datepicker class="no-padding-field" #pickerAdditionDueDate></mat-datepicker>
    </mat-form-field>
</div>
<!-- Reponsive -->
<!-- <div fxFlex.gt-sm="49.5" fxFlex.gt-xs="grow" fxFlex='grow' class="res_uploadFile" fxShow="true"
    fxHide.gt-sm>
    <div class="res_upload_btn">
        <button mat-button fxFlex='grow'>
            <mat-icon class="material-icons-outlined">cloud_upload</mat-icon>
            <span class="txtUpload" i18n>Tải lên</span>
        </button>
        <input id="file_upload" name="file_upload[]" type='file' (change)="onSelectFile($event)" multiple
            [accept]="acceptFileExtension" value="{{blankVal}}" fxFlex='grow'>
    </div>
    <div class="res_upload_preview">
        <div class="list_uploaded" *ngFor='let url of urls; let i = index;' fxFlex.gt-sm="49.5"
            fxFlex.gt-xs="48.5" fxFlex='grow'>
            <div class="file_icon" [ngStyle]="{'background-image': 'url('+ urls[i] +')'}"></div>
            <span class="file_name" matTooltip="{{fileNamesFull[i]}}"
                [matTooltipPosition]="'right'">{{fileNames[i]}}</span>
            <a mat-icon-button class="delete_file">
                <mat-icon (click)="removeItem(i)">close</mat-icon>
            </a>
        </div>
    </div>
</div> -->

<digo-check-send-notify *ngIf="!notifyQNI && !checkNumberDate" functionType="additionalRequirement" functionTypeOfficer="additionalRequirementOfficer" [receiveType]="env.enableApprovalOfLeadership"></digo-check-send-notify>
<digo-check-send-notify *ngIf="!notifyQNI && numberDateAdditionalRequirement && numberDateAdditionalRequirement.enable && checkNumberDate" functionType="additionalRequirementNumberDate" functionTypeOfficer="additionalRequirementOfficer" [receiveType]="env.enableApprovalOfLeadership"></digo-check-send-notify>
<check-send-notify-qni *ngIf="!!notifyQNI" functionType="additionalRequirement" action="Request"></check-send-notify-qni>

<div fxLayout="row" fxLayout.xs="column" fxLayout.sm="column" fxLayoutAlign="center">
    <button mat-flat-button fxFlex='30' [disabled]="isDisabled" class="applyBtn" (click)="onConfirm()">
        <span i18n>Đồng ý</span>
    </button>
</div>
