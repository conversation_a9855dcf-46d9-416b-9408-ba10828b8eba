export class TaskDefinitionElement {
    name: {
        id: string;
        name: string
    };
    activiti: {
        definitionKey: string;
        name: string
    };
    processDefinition: {
        id: string;
        name: string;
        activiti: {
            id: string
        };
        processingTime: number
    };
    isFirst: number;
    candidateGroup: [
        {
            id: string;
            code: string
        }
    ];
    candidateUser: [
        {
            id: string;
            fullname: string;
            account: {
                id: string;
                username: [
                    {
                        value: string
                    }
                ]
            }
        }
    ];
    variable: {
        canPaused: number;
        canPrintDossiersCostReport: number;
        canUploadResult: number;
        canIncreaseDue: number;
        canUseDigitalSign: number;
        canUpdateDossierComp: number;
        canUpdateApplicant: number;
        canCancelDosssier: number;
        canChoiceNextStep: number
        canUpdateDosssierDetail: number
        canResendDossierToPriviousStep: number
        canPrintReceiptTicket: number
        mustRatingOfficials: number
        mustPublishInvoice: number
        mustChooseAssignee: number
        mustSendSMSToApplicant: number
        mustAttachResultsFile: number
        mustConfirm: number
        mustPrintCoupons: number
        officerPhieuTemplate: {
            id: string;
            name: string
        };
        officerSMSTemplate: {
            id: string;
            name: string
        };
        officerZaloTemplate: {
            id: string;
            name: string
        };
        officerEmailTemplate: {
            id: string;
            name: string
        };
        citizenSMSTemplate: {
            id: string;
            name: string
        };
        citizenZaloTemplate: {
            id: string;
            name: string
        };
        citizenEmailTemplate: {
            id: string;
            name: string
        };
        form: {
            id: string;
            name: string
        };
        processComments: string
    };
    processingTime: number;
    processingTimeUnit: string;
    description: string;

    constructor() {
        this.variable = {
            canPaused: 0,
            canPrintDossiersCostReport: 0,
            canUploadResult: 0,
            canIncreaseDue: 0,
            canUseDigitalSign: 0,
            canUpdateDossierComp: 0,
            canUpdateApplicant: 0,
            canCancelDosssier: 0,
            canChoiceNextStep: 0,
            canUpdateDosssierDetail: 0,
            canResendDossierToPriviousStep: 0,
            canPrintReceiptTicket: 0,
            mustRatingOfficials: 1,
            mustPublishInvoice: 1,
            mustChooseAssignee: 1,
            mustSendSMSToApplicant: 1,
            mustAttachResultsFile: 1,
            mustConfirm: 1,
            mustPrintCoupons: 1,
            officerPhieuTemplate: {
                id: '',
                name: '',
            },
            officerSMSTemplate: {
                id: '',
                name: '',
            },
            officerZaloTemplate: {
                id: '',
                name: '',
            },
            officerEmailTemplate: {
                id: '',
                name: '',
            },
            citizenSMSTemplate: {
                id: '',
                name: '',
            },
            citizenZaloTemplate: {
                id: '',
                name: '',
            },
            citizenEmailTemplate: {
                id: '',
                name: '',
            },
            form: {
                id: '',
                name: '',
            },
            processComments: '',
        }
    }
}
