import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { EnvService } from 'core/service/env.service';
import { ApiProviderService } from 'src/app/core/service/api-provider.service';




@Injectable({
  providedIn: 'root'
})
export class DLKStatisticService {

  language = localStorage.getItem('language') === 'vi' ? 228 : 46;
  config = this.envService.getConfig();

  constructor(
    private http: HttpClient,
    private envService: EnvService,
    private apiProviderService: ApiProviderService,
  ) { }

  private padmanURL = this.apiProviderService.getUrl('digo', 'padman');

  private basedataURL = this.apiProviderService.getUrl('digo', 'basedata');
  private basepadURL = this.apiProviderService.getUrl('digo', 'basepad');
  private basecatURL = this.apiProviderService.getUrl('digo', 'basecat');


  public excelExportPaymentURL = this.apiProviderService.getUrl('digo', 'padman') + '/dossier-payment/--qnm-export-excel-list-dossier-payment'

  getDossierStatisticGeneral(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');

    return this.http.get(this.padmanURL + '/qnm-dossier-statistic/--statistic-general' + searchString, { headers }).pipe();

  }

  test(string) {
    var a = { test: string };
    return a;
  }
  getDossierTheoDoi(body): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');

    return this.http.post(this.padmanURL + '/dlk-dossier/--sotheodoi', body, { headers }).pipe();
  }

  getDossierToiHan(body): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');

    return this.http.post(this.padmanURL + '/dlk-dossier/--hosotoihan', body, { headers }).pipe();
  }

  getDossierTreHan(body): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');

    return this.http.post(this.padmanURL + '/dlk-dossier/--hosotrehan', body, { headers }).pipe();
  }

  getHinhThucNhan(): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.basecatURL + '/tag/--by-category-id?category-id=5f3a491c4e1bd312a6f00032&status=1', { headers }).pipe();
  }

  getDossierStatisticOnlDetail(search): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.padmanURL + '/qni-dossier-statistic-onl/statistic-onl/--detail' + search, { headers }).pipe();
  }



  downloadExportPayment(params: string) {
    return this.http.get(this.excelExportPaymentURL + params, { observe: 'response', responseType: 'blob' }).toPromise();
  }

}
