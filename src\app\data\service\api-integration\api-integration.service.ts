import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { ApiProviderService } from 'src/app/core/service/api-provider.service';

@Injectable({
  providedIn: 'root'
})
export class ApiIntegrationService {

  constructor(
    private http: HttpClient,
    private apiProviderService: ApiProviderService,
  ) { }

  private adapterUrl = this.apiProviderService.getUrl('digo', 'adapter');

  getListApiIntegration(page,size):Observable<any>{
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    const URL = `${this.adapterUrl}/api-integration?page=${page}&size=${size}`;
    return this.http.get(URL,{ headers });
  }
}
