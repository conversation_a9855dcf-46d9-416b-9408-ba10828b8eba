import { Component, OnInit, AfterViewInit ,OnD<PERSON>roy} from '@angular/core';
import { FormGroup, FormControl, Validators } from '@angular/forms';
import { EnvService } from 'src/app/core/service/env.service';
import { SnackbarService } from 'src/app/data/service/snackbar/snackbar.service';
import { MatDialog, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { Inject } from '@angular/core';
import { BasepadService } from 'src/app/data/service/basepad/basepad.service';
import { BasedataService } from 'src/app/data/service/basedata/basedata.service';
import { ReplaySubject, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { FormElement } from 'src/app/data/schema/form-element';
import { MatTableDataSource } from '@angular/material/table';
import { DeploymentService } from 'src/app/data/service/deployment.service';
import { AgencyService } from 'src/app/data/service/basedata/agency.service';

export interface Element {
  id: String;
  name: String;

}

const Agency: Element[] = [];

@Component({
  selector: 'app-update-administrative',
  templateUrl: './update-administrative.component.html',
  styleUrls: ['./update-administrative.component.scss']
})
export class UpdateAdministrativeComponent implements OnInit, AfterViewInit, OnDestroy {

  Id: String;
  countResult1 = 0;
  formTemp;
  pageIndex = 0;
  countResult = 0;
  // configDepartmentTagId = this.deploymentService.env.OS_HCM.configDepartmentTagId;
  config = this.envService.getConfig();
  selectedLang: string;
  accountId: any;
  guideTitle = [];
  listGuideType = [];
  listGuideTypePage = 0;
  isFullListGuideType = false;
  isSubmit = false;
  listAcceptExt = [];
  listAcceptFileType = [];
  blankVal = '';
  result = [];
  isFieldArrayInvalid = false;
  languageIdUsed = [228];
  listLanguage = [
    {
      languageId: 228,
      name: 'Tiếng Việt'
    },
    {
      languageId: 46,
      name: 'English'
    }
  ];
  newAttribute: any = {
    languageId: 228,
    name: '',
    listLanguageItem: JSON.parse(JSON.stringify(this.listLanguage))
  };
  newItemfield: any = {
    publicAmin: '',
    name:'',
    startValid: 0,
    endValid:0,
    nowValid:0,
    displaySyntax:'',
    status:''
  }
  fieldArray: Array<any> = [];
  status: Array<any> = [
    {
      status: 0,
      name: 'Đóng'
    },
    {
      status: 1,
      name: 'Mở'
    }
  ];
  statusVi: Array<any> = [
    {
      status: 0,
      name: 'Đóng'
    },
    {
      status: 1,
      name: 'Mở'
    }
  ];
  statusEn: Array<any> = [
    {
      status: 0,
      name: 'Close'
    },
    {
      status: 1,
      name: 'Open'
    }
  ];
  nameDefault = '';
  protected onDestroy = new Subject<void>();
  updateForm = new FormGroup({
    publicAmin: new FormControl(''),
    status: new FormControl('')
  });

  ELEMENTDATA: FormElement[] = [];
  dataSource: MatTableDataSource<FormElement>;
  isAdmin?: boolean = sessionStorage.getItem('oneGateAdminMaster') === '1';
  showPublicAdministrativeAgency = this.deploymentService.env.OS_HCM.showPublicAdministrativeAgency;
  constructor(
    private dialog: MatDialog,
    public dialogRef: MatDialogRef<UpdateAdministrativeComponent>,
    @Inject(MAT_DIALOG_DATA) public data: ConfirmUpdateDialogModel,
    private envService: EnvService,
    private snackbarService: SnackbarService,
    private basedataService: BasedataService,
    private basepadService: BasepadService,
    private deploymentService: DeploymentService,
    private agencyService: AgencyService,
  ) {
    this.Id = data.id;
    this.dataSource = new MatTableDataSource(this.ELEMENTDATA);
    
    
  }

  async getAllPublicAdministration(search){
    this.ELEMENTDATA = [];
    let isLast = false;
    do {
      const data = await this.basepadService.searchPublicAdministration(search).toPromise();
      // this.countResult = data.totalElements;
      for (let i = 0; i < data.numberOfElements; i++) {
        this.ELEMENTDATA.push(data.content[i]);
      }
      if(data.last == true){
        isLast = true;
        break;
      }
      this.pageIndex++;
      search = search.replace(/page=\d+/, "page=" + this.pageIndex);
    } while(isLast === false);
    this.dataSource.data = this.ELEMENTDATA;
    console.log('this.dataSource.data',this.dataSource.data);
  }

  getDetailProceAdmin(Id) {
    const info = {
      publicAmin: '',
      name:'',
      startValid:'',
      endValid:'',
      nowValid:'',
      displaySyntax: '',
      status:''
    }
    
    let search = '?id=' + Id;
    console.log('update', search);
    this.basepadService.searchProceAdministration(search).subscribe(async res => {
      let information = info;
      information.name = res.content[0].name;
      information.startValid = res.content[0].startValid;
      information.endValid = res.content[0].endValid;
      information.nowValid = res.content[0].nowValid;
      information.publicAmin = res.content[0].publicAmin[0].id;
      information.displaySyntax = res.content[0].displaySyntax;
      information.status= res.content[0].status;
      if (information.publicAmin !== undefined) {

        this.updateForm.patchValue({
          publicAmin: information.publicAmin,
        });
      }
      console.log('information', information);
      this.fieldArray.push(JSON.parse(JSON.stringify(information)));
      
      console.log('update', res);
    }, err => {
      console.log(err);
    });

  }

  async ngOnInit() {
    this.selectedLang = localStorage.getItem('language');
    this.nameDefault = '(Không tìm thấy bản dịch)';
    if (Number(localStorage.getItem('languageId')) === 46) {
      this.status = this.statusEn;
      this.nameDefault = '(No translation found)';
    }
    this.getDetailProceAdmin(this.Id);
    let searchString = '?page='+ this.pageIndex +'&size=1000&spec=page&status=1';
    if(this.showPublicAdministrativeAgency && this.showPublicAdministrativeAgency.enable && (!this.isAdmin || !this.showPublicAdministrativeAgency.admin)){
      let agencyId = this.getAgencyId();
      let rootAgency = await this.agencyService.getRootAgency(agencyId);
      if(rootAgency && rootAgency.tag.find(o => o.id == this.showPublicAdministrativeAgency.agencyTagId)){
        agencyId = rootAgency.id;
      }
      searchString += '&agency-id=' + agencyId;
    }
    await this.getAllPublicAdministration(searchString); 
  }

  getAgencyId(): string | null {
    const userAgency = JSON.parse(localStorage.getItem('userAgency'));
    if (userAgency){
        return userAgency.id;
    }
    return null;
  }

  ngAfterViewInit() {
    setTimeout(() => {
      console.clear();
    }, 2000);
  }

  onDismiss() {
    this.dialogRef.close();
  }

  onChangeStatus(event:any){
    let value= event.value;
    this.updateForm.controls.trangThai.setValue(value);
  }

ngOnDestroy() {
  this.onDestroy.next();
  this.onDestroy.complete();
}

checkDisplaySyntax() {
  let result = true;
  if (this.fieldArray.length >= 1) {
    this.fieldArray.forEach(element => {
      // const expression: RegExp = /^[(\w\d\W)+]+\{S+\T+\T+\}[\w+]+$/i;
      // if(element.displaySyntax=='{STT}'){
      //   console.log('so sánh','{STT}',element.displaySyntax);
      // }else{
      //   console.log('so sánh','{STT}');
      // }
      // const kq: boolean = expression.test(element.displaySyntax);

      // if (kq == false) {
      //   result = false;
      // }
      let text = element.displaySyntax;
      const myArray = text.split("{STT}");
      if(myArray.length<2){
        result = false;
      }
    });
  } else {
    result = false;
  }
  return result;
}



  async onConfirm() {
    const formObj = this.updateForm.getRawValue();
    // console.log('check display',this.checkDisplaySyntax(), this.fieldArray);
    this.isSubmit = true;
    if (this.fieldArray[0].startValid===null || this.fieldArray[0].endValid===null || formObj.publicAmin.length===0 || this.fieldArray[0].displaySyntax==='' || this.fieldArray[0].name ==='' || this.fieldArray[0].status==='') {
      this.isSubmit = false;
      const msgObj = {
        vi: 'Vui lòng điền đầy đủ thông tin!',
        en: 'Please fill full information!'
      };

      this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
    } else if (this.checkDisplaySyntax()==false) {
      this.isSubmit = false;
      const msgObj = {
        vi: 'Không đúng cú pháp hiển thị!',
        en: 'Please incorrect syntax displayed!'
      };
 
      this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
    } else if (this.fieldArray[0].startValid>=this.fieldArray[0].endValid) {
      this.isSubmit = false;
      const msgObj = {
        vi: 'Giá trị kết thúc không được nhỏ hơn hoặc bằng giá trị bắt đầu!',
        en: 'Please End value cannot be less than or equal to start value!'
      };
 
      this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
    }
    else if (this.fieldArray[0].nowValid > this.fieldArray[0].endValid || (this.fieldArray[0].nowValid != 0 && this.fieldArray[0].nowValid < this.fieldArray[0].startValid)) {
      this.isSubmit = false;
      const msgObj = {
        vi: 'Giá trị hiện tại vượt phạm vi bộ số!',
        en: 'Please now value is out of range!'
      };
  
      this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
    } else {

          let content = {
            publicAdmin:[],
            name: '',
            startValid:0,
            endValid: 0,
            nowValid:0,
            displaySyntax:'',
            status:''
          };
          
          let search= '?page=0&size=1000&spec=page&id='+ formObj.publicAmin;
          const res= await this.basepadService.searchPublicAdministration(search).toPromise();
          formObj.publicAmin=[{
            id: res.content[0].id,
            code: res.content[0].code,
            name: res.content[0].name,
            status: res.content[0].status,
            agencyId: res.content[0].agencyId,
            numberIncreaseAccordingBook: res.content[0].numberIncreaseAccordingBook,
            resetAccordingYear: res.content[0].resetAccordingYear
          }]
  
              content.publicAdmin= formObj.publicAmin;
              content.name = this.fieldArray[0].name;
              content.startValid= this.fieldArray[0].startValid;
              content.endValid= this.fieldArray[0].endValid;
              content.nowValid= this.fieldArray[0].nowValid;
              content.displaySyntax = this.fieldArray[0].displaySyntax;
              content.status= this.fieldArray[0].status;
              console.log('form', content);
              const requestBody = JSON.stringify(content, null, 2);
              console.log('form', content, requestBody);
              this.basepadService.putProceAdministration(content, this.Id).subscribe(data => {
                const result = {
                  status: true
                };
                this.dialogRef.close(result);
                // window.location.reload();
              }, err => {
                const result = {
                  status: false,
                  code: err
                };
                this.dialogRef.close(result);
              });
      
    }

  }


  changeStartValid(i, $event) {
    const value = $event.target.value;
    this.fieldArray[i].startValid = value.trim();
   
    setTimeout(() => {
     
    }, 200);
  }

  changeEndValid(i, $event) {
    const value = $event.target.value;
    this.fieldArray[i].endValid = value.trim();
    
    setTimeout(() => {
     
    }, 200);
  }

  changeNowValid(i, $event) {
    const value = $event.target.value;
    this.fieldArray[i].nowValid = value.trim();

    setTimeout(() => {

    }, 200);
  }

  changeName(i, $event) {
    const value = $event.target.value;
    this.fieldArray[i].name = value.trim();

    setTimeout(() => {

    }, 200);
  }

  changeStatus(i, $event) {
    const value = $event.value;
    this.fieldArray[i].status = value;
    this.checkFormTypeInvalid(i);
    setTimeout(() => {
      this.checkFormTypeInvalid3();
    }, 200);
  }
  isNullName = false;

  checkFormTypeInvalid(i) {
    if (this.fieldArray[i].name.trim().length === 0) {
      this.isFieldArrayInvalid = true;
      this.isNullName = true;
    } else {
      this.isFieldArrayInvalid = false;
      this.isNullName = false;
    }
  }

  checkFormTypeInvalid3() {
    let count = 0;
    this.fieldArray.forEach(frmType => {
      if (frmType.name.length === 0) {
        count++;
      }
    });
    if (count === 0) {
      this.isFieldArrayInvalid = false;
      this.isNullName = false;
    } else {
      this.isFieldArrayInvalid = true;
      this.isNullName = true;
    }
  }
  changeDisplaySyntax(i, $event) {
    const value = $event.target.value;
    this.fieldArray[i].displaySyntax = value.trim();
    
    setTimeout(() => {
      
    }, 200);
  }
  
  changeCode(i, $event) {
    const value = $event.target.value;
    this.fieldArray[i].code = value.trim();
    
    setTimeout(() => {
      
    }, 200);
  }


  
}

export class ConfirmUpdateDialogModel {
  constructor(public id: string) {
  }
}
