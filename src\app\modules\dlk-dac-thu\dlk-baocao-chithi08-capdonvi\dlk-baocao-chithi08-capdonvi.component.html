<h2>BÁO CÁO CHỈ THỊ 08 CẤP ĐƠN VỊ</h2>
<div class="prc_searchbar">
  <div fxLayout="row" fxLayout.xs="row" fxLayout.sm="row" class="formFieldOutline" fxLayoutAlign="space-between">
    <div appearance="outline"  fxFlex='grow'>
      <div fxLayout="row" fxLayout.xs="row" fxLayout.sm="row">
        <mat-form-field appearance="outline" fxFlex.gt-sm="24" fxFlex.gt-xs="49.5" fxFlex='grow'>
          <mat-label>Từ ngày</mat-label>
          <input matInput [matDatepicker]="pickerAcceptFrom" [(ngModel)]="startDate">
          <mat-datepicker-toggle matSuffix [for]="pickerAcceptFrom"></mat-datepicker-toggle>
          <mat-datepicker #pickerAcceptFrom></mat-datepicker>
        </mat-form-field>
        <div fxFlex='1'></div>
        <mat-form-field appearance="outline" fxFlex.gt-sm="24" fxFlex.gt-xs="49.5" fxFlex='grow'>
          <mat-label>Đến ngày</mat-label>
          <input matInput [matDatepicker]="pickerAcceptTo" [(ngModel)]="endDate">
          <mat-datepicker-toggle matSuffix [for]="pickerAcceptTo"></mat-datepicker-toggle>
          <mat-datepicker #pickerAcceptTo></mat-datepicker>
        </mat-form-field>
        <div fxFlex='1'></div>
        <mat-form-field appearance="outline" fxFlex.gt-sm="24" fxFlex.gt-xs="49.5" fxFlex='grow'>
          <mat-label>Cơ quan tiếp nhận</mat-label>
          <mat-select msInfiniteScroll
                      [(ngModel)]="agencyId" (selectionChange)="changeAgency($event)">
            <mat-option *ngFor="let item of listAgencyAccept" [value]="item.id">{{item.name}}</mat-option>
          </mat-select>
        </mat-form-field>
        <div fxFlex='1'></div>
        <mat-form-field appearance="outline" fxFlex.gt-sm="24" fxFlex.gt-xs="49.5" fxFlex='grow'>
          <mat-label>Đơn vị tiếp nhận</mat-label>
          <mat-select msInfiniteScroll
                      [(ngModel)]="agencyDonviId" (selectionChange)="changeAgencyDonVi($event)">
            <mat-option *ngFor="let item of listAgencyDonViAccept" [value]="item.id">{{item.name}}</mat-option>
          </mat-select>
        </mat-form-field>
      </div>
    </div>
  </div>

  <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="row" fxLayoutAlign="end" style="padding-bottom: 1em;">
    <button mat-flat-button fxFlex.lt-md="22" fxFlex.md="20" fxFlex.gt-md="12" fxFlex.gt-xs="49.5" fxFlex='grow' style="background-color: #ce7a58;color: white;" class="btn-search" type="submit" (click)="thongKe()">
      <mat-icon class="iconStatistical">bar_chart</mat-icon><span>Thống kê</span>
      <mat-progress-bar mode="indeterminate" *ngIf="waitingDownloadExcel"></mat-progress-bar>
    </button>
    <div fxFlex='1'></div>
    <button mat-flat-button fxFlex.lt-md="22" fxFlex.md="20" fxFlex.gt-md="12" fxFlex.gt-xs="49.5" fxFlex='grow' class="btn-download-excel" (click)="exportToExcel()" [disabled]="waitingDownloadExcel" style="background-color: #38A938;color: white;">
      <mat-icon class="iconStatistical">cloud_download</mat-icon>
      <span style="padding-right: 2px;"> Xuất excel</span>
      <mat-progress-bar mode="indeterminate" *ngIf="waitingDownloadExcel"></mat-progress-bar>
    </button>
  </div>
</div>

<div fxLayout="row" fxLayoutAlign="center">
  <div class="frm_main" fxFlex="grow">
    <div class="logbookTbl">
      <div class="logbookOnlyTbl">
        <table class="table">
          <thead>
            <tr class="cen">
                <th rowspan="3">STT</th>
                <th rowspan="3">Tên lĩnh vực, thủ tục</th>
                <th rowspan="3">Mức độ thủ tục</th>
                <th colspan="6">Tiếp nhận hồ sơ TTHC</th>
                <th colspan="6">Số hồ sơ TTHC đã giải quyết</th>
            </tr>
            <tr class="cen">
                <th rowspan="2">Tổng số</th>
                <th rowspan="2">Trực tiếp</th>
                <th rowspan="2">Qua BCCI</th>
                <th colspan="2">Trực tuyến</th>
                <th rowspan="2">Cập nhật lên iGate</th>
                <th rowspan="2">Tổng số</th>
                <th rowspan="2">Trực tiếp</th>
                <th rowspan="2">Qua BCCI</th>
                <th rowspan="2">Trực tuyến</th>
                <th rowspan="2">Cập nhật lên iGate</th>
                <th rowspan="2">Số hồ sơ TTHC sử dụng ký số trong giải quyết</th>
            </tr>
            <tr class="cen">
                <th>Một phần</th>
                <th>Toàn trình</th>
            </tr>
            <tr class="cen">
                <th>(1)</th>
                <th>(2)</th>
                <th>(3)</th>
                <th>(4)</th>
                <th>(5)</th>
                <th>(6)</th>
                <th>(7)</th>
                <th>(8)</th>
                <th>(9)</th>
                <th>(10)</th>
                <th>(11)</th>
                <th>(12)</th>
                <th>(13)</th>
                <th>(14)</th>
                <th>(15)</th>
            </tr>
        </thead>
          <tbody>
            <ng-container *ngFor="let item of ListMain;let i = index ">
              <tr *ngIf="ListMain.length>0">
                <td >{{i+1}}</td>
                <td ><a (click) ="ViewDetail(i,item.show)">{{item.sector}}</a></td>
                <td></td>
                <td>{{item.tiepNhanTrongKy}}</td>
                <td>{{item.tiepNhanTrucTiep}}</td>
                <td>{{item.hoSoBuuChinh}}</td>
                <td>{{item.tiepNhanMotPhan}}</td>
                <td>{{item.tiepNhanToantrinh}}</td>
                <td>{{item.tiepNhanTrongKy}}</td>
                <td>{{item.tongdaXuLy}}</td>
                <td>{{item.dxltiepNhanTrucTiep}}</td>
                <td>{{item.dxlhoSoBuuChinh}}</td>
                <td>{{item.dxltiepNhanTrucTuyen}}</td>
                <td>{{item.tongdaXuLy}}</td>
                <td></td>
              </tr>
              <ng-container *ngFor="let item1 of item.data;let j = index ">
                <tr *ngIf="item.show" >
                  <td >{{colToLetter(j)}}</td>
                  <td >{{item1.procedureName}}</td>
                  <td>{{item1.MucDo}}</td>
                  <td>{{item1.tiepNhanTrongKy}}</td>
                  <td>{{item1.tiepNhanTrucTiep}}</td>
                  <td>{{item1.hoSoBuuChinh}}</td>
                  <td>{{item1.tiepNhanMotPhan}}</td>
                  <td>{{item1.tiepNhanToantrinh}}</td>
                  <td>{{item1.tiepNhanTrongKy}}</td>
                  <td>{{item1.tongdaXuLy}}</td>
                  <td>{{item1.dxltiepNhanTrucTiep}}</td>
                  <td>{{item1.dxlhoSoBuuChinh}}</td>
                  <td>{{item1.dxltiepNhanTrucTuyen}}</td>
                  <td>{{item1.tongdaXuLy}}</td>
                  <td></td>
                </tr> 
             </ng-container>

          </ng-container>
          <tr *ngIf="ListMain.length>0">
            <td colspan="3"> TỔNG CỘNG</td>
            <td><a (click)="onClickTd('p02_tiepnhan_tong_tky', 0)"> {{TongCapTinh.tiepNhanTrongKy}}</a></td>
            <td><a (click)="onClickTd('p03_tiepnhan_tructiep_tky', 0)">{{TongCapTinh.tiepNhanTrucTiep}}</a></td>
            <td><a (click)="onClickTd('p04_tiepnhan_bcci_tky', 0)">{{TongCapTinh.hoSoBuuChinh}}</a></td>
            <td><a (click)="onClickTd('p05_tiepnhan_tructuyen_motphan_tky', 0)">{{TongCapTinh.tiepNhanMotPhan}}</a></td>
            <td><a (click)="onClickTd('p07_tiepnhan_tructuyen_toantrinh_tky', 0)">{{TongCapTinh.tiepNhanToantrinh}}</a></td>
            <td><a (click)="onClickTd('p02_tiepnhan_tong_tky', 0)">{{TongCapTinh.tiepNhanTrongKy}}</a></td>
            <td><a (click)="onClickTd('p13_traketqua_tong_tky', 0)">{{TongCapTinh.tongdaXuLy}}</a></td>
            <td><a (click)="onClickTd('p14_traketqua_tructiep_tky', 0)">{{TongCapTinh.dxltiepNhanTrucTiep}}</a></td>
            <td><a (click)="onClickTd('p16_traketqua_bcci_tky', 0)">{{TongCapTinh.dxlhoSoBuuChinh}}</a></td>
            <td><a (click)="onClickTd('p18_traketqua_tructuyen_tky', 0)">{{TongCapTinh.dxltiepNhanTrucTuyen}}</a></td>
            <td><a (click)="onClickTd('p13_traketqua_tong_tky', 0)">{{TongCapTinh.tongdaXuLy}}</a></td>
            <td><a ></a></td>
          </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</div>