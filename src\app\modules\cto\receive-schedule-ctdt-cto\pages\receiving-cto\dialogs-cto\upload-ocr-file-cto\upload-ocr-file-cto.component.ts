import {Component, Inject, OnInit} from '@angular/core';
import {MAT_DIALOG_DATA, MatDialog, MatDialogRef} from "@angular/material/dialog";
import {DeploymentService} from "data/service/deployment.service";
import {EnvService} from "core/service/env.service";
import {SnackbarService} from "data/service/snackbar/snackbar.service";
import {FormControl, FormGroup} from "@angular/forms";
import {UserService} from "data/service/user.service";
import {NotificationService} from "data/service/notification.service";
import {KeycloakService} from "keycloak-angular";
import {DossierService} from "data/service/dossier/dossier.service";
import {ProcedureService} from "data/service/procedure/procedure.service";
import {
  AddApologyTextComponent,
  ConfirmAddApologyTextComponent
} from "modules/dossier/pages/search/dialogs/add-apology-text/add-apology-text.component";
import {
  ConfirmSignApologyTextComponent,
  SignApologyTextComponent
} from "modules/dossier/pages/search/dialogs/sign-apology-text/sign-apology-text.component";
import {SuspendModel} from "modules/dossier/dialogs/suspend/suspend.component";
import { AgencyService } from 'src/app/data/service/basedata/agency.service';

@Component({
  selector: 'app-upload-ocr-file-cto',
  templateUrl: './upload-ocr-file-cto.component.html',
  styleUrls: ['./upload-ocr-file-cto.component.scss']
})
export class UploadOcrFileComponent implements OnInit {
  config = this.envService.getConfig();
  env = this.deploymentService.getAppDeployment()?.env;
  selectedLang = localStorage.getItem('language');
  dossierId: string;
  commentContent = '';
  userName: string;
  fullname: string;
  accountId: string;
  userId: string;
  dossierCode: string;
  isCKMaxlenght = false;
  extend = false;
  //public Editor = ClassicEditor;
  commentConfig = {
    placeholder: 'Nhập nội dung...',
    language: 'vi',
    removePlugins: ['MediaEmbed', 'ImageUpload'],
    toolbarLocation: 'bottom'
  };
  dossierTaskStatus = { id: '', name: []};
  dossierMenuTaskRemind = { id: '', name: [] };
  dossierDetail: any;
  updateForm = new FormGroup({
    numberPauseDay: new FormControl(1),
  });
  textPhone = '';
  textEmail = '';
  smsEmailContent = '';
  smsEmailMaxLength = null;
  willSend = false;
  willSendEmail = false;
  willSendSMS = false;
  listEmail = [];
  listPhone = [];
  processDetail: any;
  checkNumberPauses = false;
  // ================================================= Upload file
  acceptFileExtension = this.config.acceptFileExtension;
  acceptFileType = this.config.acceptFileType;
  maxFileSize = this.env?.maxFileSize || this.config.maxFileSize;
  uploaded: boolean;
  blankVal: any;
  uploadedImage = [];
  files = [];
  urls = [];
  fileNames = [];
  fileNamesFull = [];
  listProcedureProcess = [];
  requireAttachmentWhenPause = false;
  selectedLangId = Number(localStorage.getItem('languageId'));
  isSmsQNM = this.env?.OS_QNM?.isSmsQNM ? true : false;
  constructor(
    private dialog: MatDialog,
    private userService: UserService,
    private envService: EnvService,
    private notiService: NotificationService,
    private deploymentService: DeploymentService,
    private keycloakService: KeycloakService,
    public dialogRef: MatDialogRef<UploadOcrFileComponent>,
    @Inject(MAT_DIALOG_DATA) public data: SuspendModel,
    private snackbarService: SnackbarService,
    private dossierService: DossierService,
    private procedureService: ProcedureService,
    private agencyService: AgencyService
  ) {
    this.dossierId = data.dossierId;
    this.dossierCode = data.dossierCode;
  }

  oldstatus = '';

  ngOnInit(): void {
    this.getDetailDossier();
    this.getUserAccount();
    this.getDossierTaskStatus();
    this.getDossierMenuTaskRemind();
    this.getConfigCloud();
  }

  getUserAccount() {
    this.keycloakService.loadUserProfile().then(user => {
      // tslint:disable-next-line: no-string-literal
      this.userId = user['attributes'].user_id[0];
      this.accountId = user['attributes'].account_id[0];
      this.userName =  user.username;
      this.userService.getUserInfo(this.userId).subscribe(data => {
        this.fullname = data.fullname;
      });
    });
  }
  getDossierTaskStatus() {
    this.dossierService.getAgencyTag(this.deploymentService.env.dossierTaskStatus.pending.id).subscribe(rs => {
      this.dossierTaskStatus.id = rs.id;
      this.dossierTaskStatus.name = rs.trans;
    }, err => {
      console.log(err);
    });
  }
  getDossierMenuTaskRemind() {
    this.dossierService.getAgencyTag(this.deploymentService.env.dossierMenuTaskRemind.pending.id).subscribe(rs => {
      this.dossierMenuTaskRemind.id = rs.id;
      this.dossierMenuTaskRemind.name = rs.trans;
    }, err => {
      console.log(err);
    });
  }

  getDetailDossier(){
    this.dossierService.getDossierDetail(this.dossierId).subscribe(data => {
      if (data.acceptedDate !== undefined && data.appointmentDate !== undefined) {
        if (data.countExtendTime && data.countExtendTime > 0){
          this.extend = true;
        }
      }
      this.dossierDetail = data;
      if (this.dossierDetail.dossierTaskStatus === undefined && this.dossierDetail.dossierTaskStatus === null) {
        // this.putDossierStatus();
      }
      else{
        this.oldstatus = this.dossierDetail.dossierTaskStatus.name;
      }
      // tslint:disable-next-line:max-line-length
      if (data.applicant.data.phoneNumber !== null && data.applicant.data.phoneNumber !== '' && data.applicant.data.phoneNumber !== undefined) {
        this.textPhone = data.applicant.data.phoneNumber;
        this.listPhone.push({
          number: data.applicant.data.phoneNumber,
          nextFlow: ''
        });
      }
      if ( data.applicant.data.email !== null && data.applicant.data.email !== '' && data.applicant.data.email !== undefined) {
        this.textEmail = data.applicant.data.email;
        this.listEmail.push({
          email: data.applicant.data.email,
          nextFlow: ''
        });
      }
      const agencies = this.agencyService.getAgencies();
      const extend = {
        dossier:{
          id: this.dossierId,
          code: this.dossierCode
        },
        agencies: agencies
      };
      setTimeout(() => {
        this.notiService.checkSendSubject.next(
          {
            id: data?.procedureProcessDefinition?.id,
            phone: data?.applicant?.data?.phoneNumber,
            email: data?.applicant?.data?.email,
            contentParams: {
              parameters: {
                sector: !!data?.procedure?.sector?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name ? data?.procedure?.sector?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name : '',
                procedure: !!data?.procedure?.translate?.find(agency => agency.languageId === Number(this.selectedLangId))?.name ? data?.procedure?.translate?.find(agency => agency.languageId === Number(this.selectedLangId))?.name : '',
                agency: !!data?.agency?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name ? data?.agency?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name : '',
                applicantFullname: data?.applicant?.data?.fullname,
                officerFullname: '',
                subsystem: !!this.env?.subsystem?.name ? this.env?.subsystem?.name : "Hệ thống Một cửa điện tử",
                dossierCode: data?.code,
                nextTask: !!this.env?.notify?.suspendDossier?.nextTask ? this.env?.notify?.suspendDossier?.nextTask : 'Tạm dừng xử lý',
                dossierSearchPadsvcUrl: (!!this.env?.domain?.padsvc ? this.env?.domain?.padsvc : "http://padsvctest.vncitizens.vn/vi/") + "dossier/public",
                dossierDetailPadsvcUrl: (!!this.env?.domain?.padsvc ? this.env?.domain?.padsvc : "http://padsvctest.vncitizens.vn/vi/") + "dossier/detail/" + this.dossierId,
                dossierSearchUrl: '',
                dossierDetailUrl: '',
                returnMethod: !!data?.dossierReceivingKind?.name?.find(recieve => recieve.languageId === Number(this.selectedLangId))?.name ? data?.dossierReceivingKind?.name?.find(recieve => recieve.languageId === Number(this.selectedLangId))?.name : '',
                receivingDate: '',
                appointmentDate: '',
                reason: '',
                extend: this.isSmsQNM ? extend : null
              }
            }
          }
        );
      }, this.config?.reloadTimeout);
      this.getProcedureProcessDetail(data.procedureProcessDefinition.id);
    });
  }
  getProcedureProcessDetail(processId) {
    this.procedureService.getProcedureProcessDetail(processId).subscribe(data => {
      this.processDetail = data;
      this.checkedNumberPauses();
    });
  }
  checkedNumberPauses(){
    if(this.processDetail.numberPauses === -1 || this.dossierDetail.countPauses < this.processDetail.numberPauses){
      this.checkNumberPauses = true;
    }
  }

  putDossierStatus() {
    const requestBodyObj = [
      {
        id: this.dossierId,
        dossierStatus: 2,
        dossierTaskStatus: null
      }
    ];
    const requestBody = JSON.stringify(requestBodyObj, null, 2);
    this.dossierService.putDossierStatus(requestBody).subscribe(data => {
      if (data.affectedRows === 1) {
        this.getDetailDossier();
      }
    });
  }

  postHistory(){
    let newStatus = '';
    if (this.dossierTaskStatus.name.length > 0){
      newStatus = this.dossierTaskStatus.name[0].name;
      this.dossierTaskStatus.name.forEach(element => {
        if (element.languageId === Number(localStorage.getItem('languageId'))){
          newStatus = element.name;
        }
      });
    }
    const content = {
      groupId: 1,
      itemId: this.dossierId,
      user: {
        id: this.userId,
        name: this.fullname
      },
      type: 3,
      deploymentId: this.config.deploymentId,
      action: [
        {
          fieldNameRbk: 'lang.word.status',
          originalValue: this.oldstatus,
          newValue: newStatus
        }
      ]
    };
    this.dossierService.postHistory(JSON.stringify(content, null, 2)).subscribe(data => {

    });
  }

  onConfirm() {
    if (this.files.length !== 2){
      const msgObj = {
        vi: 'Vui lòng đính kèm đúng 2 ảnh mặt trước, mặt sau CMND!',
        en: 'Please attach file!'
      };
      this.snackbarService.openSnackBar(2, '', msgObj[this.selectedLang], 'warning_notification', this.config.expiredTime);
    }else {
      this.keycloakService.loadUserProfile().then(user => {
        const userId = user['attributes'].user_id[0];
        this.uploadMultiFile(this.files, userId).then(data=>{
          let hashes = [data[0].id, data[1].id];
          this.dialogRef.close(hashes);
        });
      });
    }
  }

  putDossierPause(){
    const formObj = this.updateForm.getRawValue();
    const requestBodyObj = {
      dossierStatus: 3,
      enableApprovalOfLeadership : 0,
      comment: '',
      dossierTaskStatus: this.dossierTaskStatus,
      dossierMenuTaskRemind: this.dossierMenuTaskRemind,
      numberPauseDay: formObj.numberPauseDay,
      attachment: this.uploadedImage,
      user: {
        id: this.userId,
        fullname: this.fullname,
        account: {
          id: this.accountId,
          username: [
            {
              value: this.userName
            }
          ]
        }
      },
    };
    if (this.commentContent.trim() !== '') {
      const msgObj = {
        vi: 'Tạm dừng hồ sơ <b>' + this.dossierCode + '</b> <br /> Lý do: ' + this.commentContent.trim(),
        en: 'Dossier <b>' + this.dossierCode + '</b> has been suspend! <br /> Reason: ' + this.commentContent.trim()
      };
      this.postComment(msgObj[this.selectedLang]);
      requestBodyObj.comment = this.commentContent.trim();
    } else {
      const msgObj = {
        vi: 'Tạm dừng hồ sơ <b>' + this.dossierCode + '</b>',
        en: 'Dossier <b>' + this.dossierCode + '</b> has been suspend!'
      };
      this.postComment(msgObj[this.selectedLang]);
      requestBodyObj.comment = msgObj[this.selectedLang];
    }
    const requestBody = JSON.stringify(requestBodyObj, null, 2);
    this.dossierService.putDossierPauseWithComment(this.dossierId, requestBody).subscribe(data => {
      if (data.affectedRows === 1) {
        this.postHistory();
        // if (this.willSendEmail) {
        //   this.postEmail(this.listEmail, 1);
        // }
        // if (this.willSendSMS) {
        //   this.postSMS(this.listPhone);
        // }
        this.notiService.confirmSendSubject.next({
          confirm: true,
          renewContent: false
        });
        this.dialogRef.close(true);
      } else {
        this.dialogRef.close(false);
      }
    }, err => {
      this.dialogRef.close(false);
    });
  }

  onDismiss() {
    this.dialogRef.close();
  }

  postComment(commentContent) {
    const content = {
      groupId: 2,
      itemId: this.dossierId,
      user: {
        id: this.userId,
        fullname: this.fullname
      },
      content: commentContent.trim()
    };
    const requestBody = JSON.stringify(content, null, 2);
    this.dossierService.postComment(requestBody).subscribe(data => { });
  }

  onCommentEditorChange(event) {
    this.commentContent = event.editor.getData();
    if (event.editor.getData().trim().length > 500) {
      this.isCKMaxlenght = true;
    } else {
      this.isCKMaxlenght = false;
    }
  }

  addApologyText(dossierId) {
    const dialogData = new ConfirmAddApologyTextComponent(dossierId);
    const dialogRef = this.dialog.open(AddApologyTextComponent, {
      minWidth: '40vw',
      maxWidth: '60vw',
      maxHeight: '60vh',
      data: dialogData,
      disableClose: true,
      autoFocus: false
    });
  }

  signApologyText(dossierId) {
    const dialogData = new ConfirmSignApologyTextComponent(dossierId);
    const dialogRef = this.dialog.open(SignApologyTextComponent, {
      minWidth: '50vw',
      maxWidth: '60vw',
      minHeight: '40vh',
      maxHeight: '70vh',
      data: dialogData,
      disableClose: true,
      autoFocus: false
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
      if (dialogResult === true) {
        const msgObj = {
          vi: 'Đã lưu!',
          en: 'Saved!'
        };
        this.snackbarService.openSnackBar(1, msgObj[localStorage.getItem('language')], '', 'success_notification', this.config.expiredTime);
      }else if (dialogResult === false) {
        const msgObj = {
          vi: 'Lưu thất bại!',
          en: 'Save failed!'
        };
        this.snackbarService.openSnackBar(0, msgObj[localStorage.getItem('language')], '', 'error_notification', this.config.expiredTime);
      }
    });
  }
  onCheckboxSendChange(type) {
    switch (type) {
      case 1:
        this.willSendSMS = !this.willSendSMS;
        if (this.willSendSMS === true) {
          this.smsEmailMaxLength = this.config.dossierEmailSMSZaloConfig.characterLimit;
        } else {
          this.smsEmailMaxLength = null;
        }
        break;
      case 2:
        this.willSendEmail = !this.willSendEmail;
        break;
    }
    if (this.willSendSMS || this.willSendEmail) {
      this.willSend = true;
    } else {
      this.willSend = false;
    }
  }
  postEmail(listEmail, type) {
    if (listEmail.length > 0) {
      listEmail.forEach(mail => {
        const emailConfig = this.config.dossierEmailSMSZaloConfig;
        let subject = emailConfig.increaseDue[this.selectedLang];
        subject = subject.replace('{{code}}', this.dossierDetail.code);
        const contentTemp = emailConfig.reasonExtend[this.selectedLang] + '' + this.smsEmailContent;
        const userAgency = JSON.parse(localStorage.getItem('userAgency'));
        let rootAgencyId: any = '';
        if (userAgency !== null) {
          rootAgencyId = userAgency.id;
        } else {
          rootAgencyId = this.config.rootAgency.id;
        }
        this.dossierService.postEmailByAgency(
          rootAgencyId,
          this.config.subsystemId,
          contentTemp,
          [mail.email],
          subject
        ).subscribe(emailRS => {
          console.log(emailRS);
        }, err => {
          console.log(err);
        });
      });
    }
  }

  postSMS(listPhoneNumber) {
    const emailConfig = this.config.dossierEmailSMSZaloConfig;
    let subject = emailConfig.increaseDue[this.selectedLang];
    subject = subject.replace('{{code}}', this.dossierDetail.code);
    subject += '. ' + emailConfig.reasonExtend[this.selectedLang] + '' + this.smsEmailContent;
    console.log(subject)
    if (listPhoneNumber.length > 0) {
      listPhoneNumber.forEach(phone => {
        const userAgency = JSON.parse(localStorage.getItem('userAgency'));
        let rootAgencyId: any = '';
        if (userAgency !== null) {
          rootAgencyId = userAgency.id;
        } else {
          rootAgencyId = this.config.rootAgency.id;
        }
        this.dossierService.postSMSByAgency(
          rootAgencyId,
          this.config.subsystemId,
          subject,
          [phone.number]
        ).subscribe(smsRS => {
          console.log(smsRS);
        }, err => {
          console.log(err);
        });
      });
    }
  }
  // ========= file


  uploadMultiFile(file, userId) {
    return new Promise((resolve, reject)=>{
      this.procedureService.uploadMultiFile(file, userId).subscribe(data => {
        this.uploadedImage = data;
        resolve(data);
      }, err => {
        console.log(err);
      });
    });
  }

  onSelectFile(event) {
    if (event.target.files && event.target.files[0]) {
      for (const i of event.target.files) {
        if (i.size >= this.maxFileSize * 1024 * 1024) {
          const msgObj = {
            vi: 'Kích thước tệp tin quá lớn, tên tệp: ' + i.name,
            en: 'The file is too large, file name: ' + i.name
          };
          this.snackbarService.openSnackBar(0, '', msgObj[this.selectedLang], 'error_notification', this.config.expiredTime);
          return;
        }
        if (this.acceptFileType.filter(t => t === i.type.toLowerCase()).length > 0) {
          this.files.push(i);
          const extension = i.name.substring(i.name.lastIndexOf('.')).split('.').pop();
          this.urls.push(this.getFileIcon(extension));

          const reader = new FileReader();
          reader.onload = () => {
            this.uploaded = true;
          };
          if (i.name.length > 20) {
            const startText = i.name.substr(0, 5);
            const shortText = i
              .name
              .substring(i.name.length - 7, i.name.length);
            this.fileNames.push(startText + '...' + shortText);
            this.fileNamesFull.push(i.name);
          } else {
            this.fileNames.push(i.name);
            this.fileNamesFull.push(i.name);
          }
          reader.readAsDataURL(i);
        }
        else {
          const msgObj = {
            vi: 'Không hỗ trợ loại tệp tin ',
            en: 'File type is not supported '
          };
          // tslint:disable-next-line: max-line-length
          this.snackbarService.openSnackBar(0, '', msgObj[this.selectedLang] + i.type.substring(i.type.lastIndexOf('/') + 1).toUpperCase(), 'error_notification', this.config.expiredTime);
        }
      }
    }
  }

  removeItem(index: number) {
    this.urls.splice(index, 1);
    this.fileNames.splice(index, 1);
    this.fileNamesFull.splice(index, 1);
    this.files.splice(index, 1);
    this.blankVal = '';
  }

  getFileIcon(ext) {
    return this.config.cloudStaticURL + 'icon/files/300x300/' + ext + '.png';
  }
  getConfigCloud() {
    const config = this.deploymentService.getAppDeployment();
    if (!!config?.env?.MCDT_DINH_KEM_FILE_KHI_TAM_DUNG) {
      this.requireAttachmentWhenPause = config?.env?.MCDT_DINH_KEM_FILE_KHI_TAM_DUNG;
    }
  }
}

export class UploadOcrFileDialogModel {
  constructor() {
  }
}
