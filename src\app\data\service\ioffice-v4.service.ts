import {HttpBackend, HttpClient, HttpHeaders, HttpParams} from '@angular/common/http';
import {Injectable} from '@angular/core';
import {Observable} from 'rxjs';
import {ApiProviderService} from 'core/service/api-provider.service';
import {EnvService} from 'core/service/env.service';
import {DeploymentService} from 'data/service/deployment.service';

@Injectable({
  providedIn: 'root'
})
export class IofficeV4Service  {

  config = this.envService.getConfig();
  constructor(
    private envService: EnvService,
    private http: HttpClient,
    private apiProviderService: ApiProviderService,
    private deploymentService: DeploymentService,
    private handler: HttpBackend,
    private httpQLVB: HttpClient
  ) {
    this.httpQLVB = new HttpClient(this.handler);
   }

  private padman = this.apiProviderService.getUrl('digo', 'padman');
  env: any = this.deploymentService.getAppDeployment()?.env;
  private rootUrlIofficeV4 = this.env?.OS_HGG?.iofficev4?.rootUrl !== undefined ? this.env?.OS_HGG?.iofficev4?.rootUrl : '';
  private qLVBPath = this.env?.OS_BDG?.QLVBMinhTue?.gateway;

  getThongTinCanBo(username): Promise<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.rootUrlIofficeV4 + '/api/can-bo/thong-tin-tai-khoan?username=' + username, { headers }).toPromise();
  }

  getThongTinDonVi(ma_ctcb): Promise<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.rootUrlIofficeV4 + '/api/can-bo/don-vi-quan-tri?ma_ctcb=' + ma_ctcb, { headers }).toPromise();
  }

  async uploadFileBase64(object: any): Promise<any> {
    const formData = new FormData();
    for (const property in object) {
      if (object.hasOwnProperty(property)) {
        formData.append(property, object[property]);
      }
    }
    return this.http.post(this.rootUrlIofficeV4 + '/api/quan-ly-tap-tin/tai-tap-tin-len-tu-base64', formData, { responseType: 'text' }).toPromise();
  }

  async sendDocumentToIoffice(object: any): Promise<any> {
    const formData = new FormData();
    for (const property in object) {
      if (object.hasOwnProperty(property)) {
        formData.append(property, object[property]);
      }
    }
    return this.http.post(this.rootUrlIofficeV4 + '/api/van-ban-di/chuyen-vien-luu-van-ban-di-igate', formData).toPromise();
  }

  updateDossierRemind(requestBody: any): Promise<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.post<any>(this.padman + '/iofficev4-hgg/cap-nhat-nhac-viec-ho-so', requestBody, { headers }).toPromise();
  }
}
