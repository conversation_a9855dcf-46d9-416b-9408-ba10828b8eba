<div fxLayout="{{interfaceWorkHorizontal == 1? 'column': 'row'}}" fxLayoutAlign="space-between" fxLayout.sm="column" fxLayout.xs="column">
    <div fxFlex="{{interfaceWorkHorizontal == 1? 100: 25}}" fxFlex.sm="100" fxFlex.xs="100" *ngIf="isShowMenuRemindTask == false">
        <div (click)="onClickOpenReminderMenu()" mat-button class="title-reminder" fxLayout="row" fxLayoutAlign="space-between">
            <div class="content">
                <span fxFlex="100" class="title"><span i18n>Danh sách công việc</span> (<a class="count-task">{{lengthRemind}}</a>)</span>
              <div *ngIf="expandReminderMenu === true then isExpand; else isNotExpand"></div>
              <ng-template #isExpand>
                <mat-icon fxFlex="10">expand_less</mat-icon>
              </ng-template>
              <ng-template #isNotExpand>
                <mat-icon fxFlex="10">expand_more</mat-icon>
              </ng-template>
            </div>
        </div>
        <div class="menu_reminder">
            <mat-accordion class="advanced-box" multi>
                <mat-expansion-panel class="panel" *ngIf="expandReminderMenu" [(expanded)]="expandReminderMenu" [ngStyle]="{'height': interfaceWorkHorizontal? 'unset': xpandStatus ? '28rem' : '8rem' }">
                    <span *ngFor="let remind of listMenuRemind;">
                        <a id="submenu" mat-button active-link="active" [ngClass]="{ 'active': remindId === remind.id , 'interfaceWorkHorizontalClass': interfaceWorkHorizontal == 1}" (click)="changeSearchRemind(remind.id,remind.name)">
                            <mat-icon>receipt</mat-icon><span class="submenuTitle">{{remind.name}}</span>&nbsp;<span class="count">{{remind.count}}</span>
                    </a>
                    </span>
                    <span *ngIf="showRemindSyncLGSPHCM">
                        <a id="submenu" mat-button active-link="active" [ngClass]="{ 'active': isSyncLGSPHCM }" (click)="getSyncLGSP()">
                            <mat-icon>receipt</mat-icon><span class="submenuTitle">Hồ sơ đồng bộ không thành công</span>&nbsp;<span class="count">{{countSyncLGSPHCM}}</span>
                        </a>
                    </span>
                </mat-expansion-panel>
            </mat-accordion>
        </div>
    </div>
    <div fxFlex="{{interfaceWorkHorizontal == 1? 100 : flex}}" fxFlex.sm="100" fxFlex.xs="100" class="search" >
        <h2 i18n>Tra cứu hồ sơ toàn cơ quan</h2>
        <div class="prc_searchbar">
            <form [formGroup]="searchForm" (submit)="onConfirmSearch()" class="searchForm">
                <div fxLayout="row" fxLayout.xs="row" fxLayout.sm="row" class="formFieldItems" fxLayoutGap="10px">
                    <mat-form-field appearance="outline" fxFlex.gt-sm="{{flexGtSm4Column}}" fxFlex.gt-xs="49.5" fxFlex='grow' fxFlex.gt-md="24">
                        <mat-label i18n>Mã số hồ sơ</mat-label>
                        <input type="text" matInput formControlName="code">
                    </mat-form-field>
                    <mat-form-field appearance="outline" fxFlex.gt-sm="{{flexGtSm4Column}}" fxFlex.gt-xs="49.5" fxFlex='grow' fxFlex.gt-md="24">
                        <mat-label >Số CMND/CCCD</mat-label>
                        <input type="text" matInput formControlName="identityNumber">
                    </mat-form-field>
                  <!--Lọc theo tên thủ tục -->
                    <mat-form-field *ngIf="enableSearchProcedureCode == true" appearance="outline" fxFlex.gt-sm="26" fxFlex.gt-xs="49.5" fxFlex='grow' fxFlex.gt-md="24">
                      <mat-label i18n>Mã thủ tục</mat-label>
                      <input type="text" matInput formControlName="procedureCode">
                    </mat-form-field>
                    <mat-form-field *ngIf="!isOwnerFullname || (isOwnerFullname && showCreateSearchOwnerFullname)" appearance="outline" fxFlex.gt-sm="{{flexGtSm4ColumnName}}" fxFlex.gt-xs="49.5" fxFlex='grow' fxFlex.gt-md="24">
                        <mat-label i18n>Tên người nộp</mat-label>
                        <input type="text" matInput formControlName="applicantName">
                    </mat-form-field>
                    <mat-form-field *ngIf="isShowAgencyEnterprise == 1" appearance="outline" fxFlex.gt-sm="26" fxFlex.gt-xs="49.5" fxFlex='grow' fxFlex.gt-md="24">
                        <mat-label i18n>Tên cơ quan doanh nghiệp</mat-label>
                        <input type="text" matInput formControlName="applicantOrganization">
                    </mat-form-field>
                    
                    <mat-form-field *ngIf="isShowNationCodeCbx" appearance="outline" fxFlex.gt-sm="24" fxFlex.gt-xs="49.5" fxFlex='grow' fxFlex.gt-md="24">
                        <mat-label>Mã hồ sơ Quốc Gia</mat-label>
                        <input type="text" matInput formControlName="nationCode">
                    </mat-form-field>
                    
                    <mat-form-field *ngIf="isShowSearchTaxCode" appearance="outline" fxFlex.gt-sm="24" fxFlex.gt-xs="49.5" fxFlex='grow' fxFlex.gt-md="24">
                        <mat-label i18n="@@taxCode">Mã Số Thuế</mat-label>
                        <input type="text" matTooltip="Mã số thuế" matInput formControlName="taxCode" maxlength="500">
                    </mat-form-field>

                    <mat-form-field appearance="outline" fxFlex.gt-md="24" fxFlex.gt-sm="32" fxFlex.gt-xs="49.5" fxFlex='grow' *ngIf="allowFilterAcceptedDateOptimization">
                        <mat-label i18n>Tiếp nhận từ ngày</mat-label>
                        <input matInput [matDatepicker]="pickerAcceptFrom" formControlName="advAcceptFrom" (dateChange)="changeFromDate($event)" readonly>
                        <mat-datepicker-toggle matSuffix [for]="pickerAcceptFrom" ></mat-datepicker-toggle>
                        <mat-datepicker #pickerAcceptFrom></mat-datepicker>
                    </mat-form-field>
                    <mat-form-field appearance="outline" fxFlex.gt-md="24" fxFlex.gt-sm="32" fxFlex.gt-xs="49.5" fxFlex='grow' *ngIf="allowFilterAcceptedDateOptimization">
                        <mat-label i18n>Tiếp nhận đến ngày</mat-label>
                        <input matInput [matDatepicker]="pickerAcceptTo" formControlName="advAcceptTo" (dateChange)="changeToDate($event)" readonly>
                        <mat-datepicker-toggle matSuffix [for]="pickerAcceptTo" ></mat-datepicker-toggle>
                        <mat-datepicker #pickerAcceptTo></mat-datepicker>
                    </mat-form-field>

                    <mat-form-field appearance="outline" fxFlex.gt-md="24" fxFlex.gt-sm="32" fxFlex.gt-xs="49.5" fxFlex='grow' *ngIf="isShowFilterApplyDate">
                        <mat-label>Ngày nộp từ ngày</mat-label>
                        <input matInput [matDatepicker]="pickerApplyFrom" formControlName="advFilingFrom" maxlength="20" (dateChange)="changeAppliedFromDate($event)" readonly>
                        <mat-datepicker-toggle matSuffix [for]="pickerApplyFrom"></mat-datepicker-toggle>
                        <mat-datepicker #pickerApplyFrom></mat-datepicker>
                    </mat-form-field>
        
                    <mat-form-field appearance="outline" fxFlex.gt-md="24" fxFlex.gt-sm="32" fxFlex.gt-xs="49.5" fxFlex='grow' *ngIf="isShowFilterApplyDate">
                        <mat-label>Ngày nộp đến ngày</mat-label>
                        <input matInput [matDatepicker]="pickerApplyTo" formControlName="advFilingTo" maxlength="20" (dateChange)="changeAppliedToDate($event)" readonly>
                        <mat-datepicker-toggle matSuffix [for]="pickerApplyTo"></mat-datepicker-toggle>
                        <mat-datepicker #pickerApplyTo></mat-datepicker>
                    </mat-form-field>

                    <mat-form-field *ngIf="isShowResPerson" appearance="outline" fxFlex.gt-sm="24" fxFlex.gt-xs="49.5" fxFlex='grow'>
                        <mat-label i18n="@@resPerson">Người chịu  trách nhiệm chuyên môn</mat-label>
                        <input type="text" matTooltip="Người chịu trách nhiệm chuyên môn" matInput formControlName="resPerson" maxlength="500">
                    </mat-form-field>

                    <mat-form-field *ngIf="isOwnerFullname" appearance="outline" fxFlex.gt-sm="24" fxFlex.gt-xs="49.5" fxFlex='grow'>
                      <mat-label i18n>Chủ hồ sơ</mat-label>
                      <input type="text" matInput formControlName="ownerFullname">
                    </mat-form-field>
                    <mat-form-field *ngIf="this.showAppointmentNoLLTP == 1" appearance="outline" fxFlex.gt-sm="24" fxFlex.gt-xs="49.5" fxFlex='grow'>
                        <mat-label i18n="@@appointmentNoLLTP">Số phiếu LLTP</mat-label>
                        <input type="text" matInput formControlName="appointmentNoLLTP">
                      </mat-form-field>

                    <mat-form-field *ngIf="remindAll?.enable" appearance="outline" fxFlex.gt-sm="49.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                        <mat-label >Danh sách công việc</mat-label>
                        <!-- (selectionChange)="changeSearchRemind($event.value)" -->
                        <mat-select formControlName="remindCtrl" >
                            <mat-option value="" (click)="onClickOpenReminderMenu()">Tất cả</mat-option>
                            <mat-option *ngFor="let remind of remindAll.list;" value="{{remind.id}}" (click)="changeSearchRemind(remind.id, remind.name)">{{remind?.name}}</mat-option>
                        </mat-select>
                    </mat-form-field>
                    
                    <button mat-flat-button fxFlex.gt-sm="16" fxFlex.gt-xs="49.5" fxFlex='grow' class="searchBtn btn-search"  type="submit" (click)="changeSearch($event)" [disabled]="isDisableSearch">
                        <mat-icon>search</mat-icon><span i18n>Tìm kiếm</span>

                    </button>


                </div>
                <div fxLayout="row" *ngIf="qni_listsearch" fxLayout.xs="row" fxLayout.sm="row" class="formFieldItems" fxLayoutAlign="space-between">
                    <mat-form-field  appearance="outline" fxFlex.gt-sm="{{flexGtSm4ColumnName}}" fxFlex.gt-xs="49.5" fxFlex='grow'>
                        <mat-label i18n>Tên người nộp</mat-label>
                        <input type="text" matInput formControlName="applicantName">
                    </mat-form-field>
                    <div appearance="outline" fxFlex.gt-sm="72" fxFlex.gt-xs="49.5" fxFlex='grow'>
                        <!--<mat-form-field appearance="outline" fxFlex.gt-sm="48" fxFlex.gt-xs="49.5" fxFlex='grow'>-->
                        <app-statistic-agency-tree
                          #statisticAgencyTree
                          [isTreeOpen]="isAgencyTreeOpen"
                          [checkboxType]="true"
                          [isHideCheckboxOptions]="true"
                          (change)="agencyTreeChange($event)"
                          (changeOpenTree)="onTreeOpenChange($event)"
                          [ngStyle]="{ maxHeight: '60vh' }">
                        </app-statistic-agency-tree>
                      </div>
                    <mat-form-field *ngIf="this.showAppointmentNoLLTP == 1" appearance="outline" fxFlex.gt-sm="26" fxFlex.gt-xs="49.5" fxFlex='grow'>
                      <mat-label i18n="@@appointmentNoLLTP">Số phiếu LLTP</mat-label>
                      <input type="text" matInput formControlName="appointmentNoLLTP">
                    </mat-form-field>
                    <mat-form-field *ngIf="this.showDocumentNoLLTP == 1" appearance="outline" fxFlex.gt-sm="26" fxFlex.gt-xs="49.5" fxFlex='grow'>
                        <mat-label i18n="@@appointmentNoLLTP">Số phiếu LLTP</mat-label>
                        <input type="text" matInput formControlName="documentNoLLTP">
                      </mat-form-field>
                    <mat-form-field *ngIf="isGenerateReceiptCode && isConfigureReceiptCode" appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                        <mat-label>Mã số biên nhận</mat-label>
                        <input type="text" matInput formControlName="receiptCode" maxlength="500">
                    </mat-form-field>
                </div>
                <div fxLayout="row" fxLayout.xs="row" fxLayout.sm="row" fxLayoutAlign="space-between" class="flex-wrap-wrap" *ngIf="isQNM">
                    <mat-form-field appearance="outline" fxFlex.gt-sm="20" fxFlex.gt-xs="49.5" fxFlex='grow' >
                        <mat-label i18n>Trạng thái thanh toán</mat-label>
                        <mat-select formControlName="payStatus"  (selectionChange)="onConfirmSearch()">
                            <mat-option value=""><span i18n>Tất cả</span></mat-option>
                            <mat-option value="1"><span i18n>Không tính phí</span></mat-option>
                            <mat-option value="2"><span i18n>Chưa thanh toán</span></mat-option>
                            <mat-option value="3"><span i18n>Đã thanh toán 1 phần</span></mat-option>
                            <mat-option value="4"><span i18n>Đã thanh toán</span></mat-option>
                        </mat-select>
                    </mat-form-field>
                </div>
                <div fxLayout="row" fxLayout.xs="row" fxLayout.sm="row" fxLayoutAlign="start" class="flex-wrap-wrap" fxLayoutGap="80px" *ngIf="showFilterPaymentStatus && isAllowFilterPaymentStatusListAgency">
                    <mat-form-field appearance="outline" fxFlex.gt-sm="20" fxFlex.gt-xs="49.5" fxFlex='grow' >
                        <mat-label i18n>Trạng thái thanh toán</mat-label>
                        <mat-select formControlName="paymentStatus" [(ngModel)]="selectedValue" [multiple]="true" (selectionChange)="changeSelectPayment($event)">
                            <mat-option value=""><span i18n>Tất cả</span></mat-option>
                            <mat-option value="1"><span i18n>Không tính phí</span></mat-option>
                            <mat-option value="2"><span i18n>Chưa thanh toán</span></mat-option>
                            <mat-option value="3"><span i18n>Đã thanh toán 1 phần</span></mat-option>
                            <mat-option value="4"><span i18n>Đã thanh toán</span></mat-option>
                        </mat-select>
                    </mat-form-field>
                    <mat-form-field *ngIf="isShowFilterSortHCM" appearance="outline" fxFlex.gt-sm="20" fxFlex.gt-xs="49.5" fxFlex='grow' >
                        <mat-label i18n="@@sortName">Sắp xếp</mat-label>
                        <mat-select formControlName="sortCtrlHCM" [(ngModel)]="sortHcmId" (selectionChange)="sortChange()">
                            <mat-option *ngFor="let r of arrSortType;" value="{{r.id}}">{{r.value}}</mat-option>
                        </mat-select>
                    </mat-form-field>
                </div>
                <div class="advanced-button" (click)='onClickOpenAdvancedSearchBox()'>
                    <span i18n>Tìm kiếm nâng cao</span>
                    <mat-icon class="advanced-expand-icon">expand_more</mat-icon>
                </div>
                <mat-accordion class="advanced-box" multi>
                    <mat-expansion-panel class="panel" [(expanded)]="xpandStatus">
                        <!--    Advanced search row-1 procedure            -->
                          <div fxLayout="row" fxLayout.xs="row" fxLayout.sm="row" fxLayoutAlign="center center" class="formFieldItems" fxLayoutAlign="space-between">
                            <mat-form-field appearance="outline" fxFlex.gt-sm="24" fxFlex.gt-xs="49.5" fxFlex='grow'>
                              <mat-label i18n>Lĩnh vực</mat-label>
                              <mat-select #sectorMatSelectInfiniteScroll
                                          msInfiniteScroll
                                          (infiniteScroll)="getListSectorScroll()"
                                          formControlName="advSector"
                                          (selectionChange)="advSectorChange($event)"
                                          [complete]="isFullListSector == true">
                                <mat-option>
                                  <ngx-mat-select-search
                                    [formControl]="searchSectorCtrl"
                                    placeholderLabel=""
                                    [disableScrollToActiveOnOptionsChanged]="true"
                                    i18n-noEntriesFoundLabel noEntriesFoundLabel="Không tìm thấy kết quả nào!">
                                  </ngx-mat-select-search>
                                </mat-option>
                                <mat-option value="" i18n *ngIf="!dossierSearchShowUserSectorOnly">Tất cả</mat-option>
                                <mat-option *ngFor="let sector of sectorFiltered | async" value="{{sector.id}}">
                                    <ng-container *ngIf ="addSectorCode">
                                        [{{sector.code}}] {{sector.name}}
                                    </ng-container>
                                    <ng-container *ngIf ="!addSectorCode">
                                        {{sector.name}}
                                    </ng-container>
                                  <span *ngIf="sector.name == undefined || sector.name == null || sector.name.trim() == ''" i18n>(Không tìm thấy bản dịch)</span>
                                </mat-option>
                              </mat-select>
                            </mat-form-field>
<!--                            <mat-form-field appearance="outline" fxFlex.gt-sm="74.5" fxFlex.gt-xs="49.5" fxFlex='grow'>-->
<!--                                <mat-label i18n>Thủ tục</mat-label>-->
<!--                                <mat-select -->
<!--                                  formControlName="advProcedure" -->
<!--                                  msInfiniteScroll (infiniteScroll)="getListProcedure(this.searchForm.get('advSector').value)" -->
<!--                                  [complete]="isFullListProcedure == true">-->
<!--                                    <mat-option value=""><span i18n>Tất cả</span></mat-option>-->
<!--                                    <mat-option *ngFor='let procedureOpt of listProcedure;' value="{{procedureOpt.id}}">-->
<!--                                        {{procedureOpt.name}}-->
<!--                                        <span *ngIf="procedureOpt.name == undefined || procedureOpt.name == null || procedureOpt.name.trim() == ''" i18n>(Không tìm thấy bản dịch)</span>-->
<!--                                    </mat-option>-->
<!--                                </mat-select>-->
<!--                            </mat-form-field>-->

                            <mat-form-field appearance="outline" fxFlex.gt-sm="74.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                              <mat-label i18n>Thủ tục</mat-label>
                              <mat-select
                                #procedureMatSelectInfiniteScroll
                                msInfiniteScroll (infiniteScroll)="getListProcedure(this.searchForm.get('advSector').value)"
                                formControlName="advProcedure" (selectionChange)="advProcedureChange($event)"
                                [complete]="isFullListProcedure == true">
                                <mat-option>
                                  <ngx-mat-select-search
                                    [formControl]="searchProcedureCtrl"
                                    placeholderLabel=""
                                    [disableScrollToActiveOnOptionsChanged]="true"
                                    i18n-noEntriesFoundLabel noEntriesFoundLabel="Không tìm thấy kết quả nào!">
                                  </ngx-mat-select-search>
                                </mat-option>
                                <mat-option value="" i18n>Tất cả</mat-option>
                                <mat-option matTooltip="{{procedure?.name}}{{procedure?.agencyName != '' ?  ' - ' +  procedure?.agencyName : ''}}"  matTooltipDisabled="{{enableTooltip}}" *ngFor="let procedure of procedureFiltered | async" value="{{procedure?.id}}">
                                  {{procedure?.name}}{{procedure?.agencyName != '' ?  ' - ' +  procedure?.agencyName : ''}}
                                  <span *ngIf="procedure?.name == undefined || procedure?.name == null || procedure?.name.trim() == ''" i18n>(Không tìm thấy bản dịch)</span>
                                </mat-option>
                              </mat-select>
                            </mat-form-field>
                            <!--    Advanced search row-2 address-1            -->
                            <mat-form-field appearance="outline" fxFlex.gt-md="24" fxFlex.gt-sm="32" fxFlex.gt-xs="49.5" fxFlex='grow' *ngIf="!isOptionSearchHGI">
                                <mat-label i18n>Quốc gia</mat-label>
                                <mat-select formControlName="advNation" (selectionChange)="advNationChange($event)">
                                    <mat-option value=""><span i18n>Tất cả</span></mat-option>
                                    <mat-option *ngFor='let nationOpt of listNation;' value="{{nationOpt.id}}">
                                        {{nationOpt.name}}
                                        <span *ngIf="nationOpt.name == undefined || nationOpt.name == null || nationOpt.name.trim() == ''" i18n>(Không tìm thấy bản dịch)</span>
                                    </mat-option>
                                </mat-select>
                            </mat-form-field>
                            <mat-form-field appearance="outline" fxFlex.gt-md="24" fxFlex.gt-sm="32" fxFlex.gt-xs="49.5" fxFlex='grow' *ngIf="!isOptionSearchHGI">
                                <mat-label i18n>Tỉnh/TP người nộp</mat-label>
                                <mat-select formControlName="advProvince" (selectionChange)="advProvinceChange($event)">
                                    <mat-option *ngFor='let provinceOpt of listProvince;' value="{{provinceOpt.id}}">
                                        {{provinceOpt.name}}
                                        <span *ngIf="provinceOpt.name == undefined || provinceOpt.name == null || provinceOpt.name.trim() == ''" i18n>(Không tìm thấy bản dịch)</span>
                                    </mat-option>
                                </mat-select>
                            </mat-form-field>
                            <mat-form-field appearance="outline" fxFlex.gt-md="24" fxFlex.gt-sm="32" fxFlex.gt-xs="49.5" fxFlex='grow' *ngIf="!isOptionSearchHGI">
                                <mat-label i18n *ngIf="twoLevelPublicAdministration">Quận/huyện người nộp</mat-label>
                                 <mat-label i18n *ngIf="!twoLevelPublicAdministration">Phường/xã người nộp</mat-label>
                                <mat-select formControlName="advDistrict" (selectionChange)="advDistrictChange($event)">
                                    <mat-option *ngFor='let districtOpt of listDistrict;' value="{{districtOpt.id}}">
                                        {{districtOpt.name}}
                                        <span *ngIf="districtOpt.name == undefined || districtOpt.name == null || districtOpt.name.trim() == ''" i18n>(Không tìm thấy bản dịch)</span>
                                    </mat-option>
                                </mat-select>
                            </mat-form-field>
                            <mat-form-field appearance="outline" fxFlex.gt-md="24" fxFlex.gt-sm="32" fxFlex.gt-xs="49.5" fxFlex='grow' *ngIf="!isOptionSearchHGI && twoLevelPublicAdministration">
                                <mat-label i18n>Phường/xã người nộp</mat-label>
                                <mat-select formControlName="advWard">
                                    <mat-option *ngFor='let wardtOpt of listWard;' value="{{wardtOpt.id}}">
                                        {{wardtOpt.name}}
                                        <span *ngIf="wardtOpt.name == undefined || wardtOpt.name == null || wardtOpt.name.trim() == ''" i18n>(Không tìm thấy bản dịch)</span>
                                    </mat-option>
                                </mat-select>
                            </mat-form-field>
                            <!--    Advanced search row-3 address-2          -->
                              <mat-form-field appearance="outline" fxFlex.gt-md="74.5" fxFlex.gt-sm="66" fxFlex.gt-xs="49.5" fxFlex='grow' *ngIf="!showCbImplementProgress && !isOptionSearchHGI">
                                <mat-label i18n>Địa chỉ người nộp</mat-label>
                                <input type="text" matInput formControlName="advAddress">
                              </mat-form-field>
                              <mat-form-field appearance="outline" fxFlex.gt-md="49.5" fxFlex.gt-sm="66" fxFlex.gt-xs="49.5" fxFlex='grow' *ngIf="showCbImplementProgress && !isOptionSearchHGI">
                                  <mat-label i18n>Địa chỉ người nộp</mat-label>
                                  <input type="text" matInput formControlName="advAddress">
                              </mat-form-field>
                              <mat-form-field appearance="outline" fxFlex.gt-md="24" fxFlex.gt-sm="32" fxFlex.gt-xs="49.5" fxFlex='grow' *ngIf="showCbImplementProgress">
                                <mat-label i18n>Tiến độ thực hiện</mat-label>
                                <mat-select formControlName="implementProgress">
                                  <mat-option value=""><span i18n>Tất cả</span></mat-option>
                                  <mat-option *ngFor="let item of arrImplementProgress" [value]="item.value">{{item.name}}</mat-option>
                                </mat-select>
                              </mat-form-field>
                              <mat-form-field appearance="outline" fxFlex.gt-md="24" fxFlex.gt-sm="32" fxFlex.gt-xs="49.5" fxFlex='grow'>
                                  <mat-label i18n>Hình thức nộp</mat-label>
                                  <mat-select formControlName="advApplyMethod">
                                      <mat-option value=""><span i18n>Tất cả</span></mat-option>
                                      <mat-option value="1"><span i18n>Trực tiếp</span></mat-option>
                                      <mat-option value="0"><span i18n>Trực tuyến</span></mat-option>
                                      <mat-option value="2"><span i18n>Bưu điện</span></mat-option>
                                      <mat-option  *ngIf="checkShowFilterRegisterApplicationVNeID" value="4"><span>Đăng ký trên ứng dụng VNeID</span></mat-option>
                                  </mat-select>
                              </mat-form-field>

                            <!--    Advanced search row-4            -->
                            <mat-form-field appearance="outline" fxFlex.gt-md="24" fxFlex.gt-sm="32" fxFlex.gt-xs="49.5" fxFlex='grow' *ngIf="allowFilterAcceptedDateOptimization == false">
                                <mat-label i18n>Tiếp nhận từ ngày</mat-label>
                                <input matInput [matDatepicker]="pickerAcceptFrom" formControlName="advAcceptFrom">
                                <mat-datepicker-toggle matSuffix [for]="pickerAcceptFrom"></mat-datepicker-toggle>
                                <mat-datepicker #pickerAcceptFrom></mat-datepicker>
                            </mat-form-field>
                            <mat-form-field appearance="outline" fxFlex.gt-md="24" fxFlex.gt-sm="32" fxFlex.gt-xs="49.5" fxFlex='grow'*ngIf="allowFilterAcceptedDateOptimization == false">
                                <mat-label i18n>Tiếp nhận đến ngày</mat-label>
                                <input matInput [matDatepicker]="pickerAcceptTo" formControlName="advAcceptTo">
                                <mat-datepicker-toggle matSuffix [for]="pickerAcceptTo"></mat-datepicker-toggle>
                                <mat-datepicker #pickerAcceptTo></mat-datepicker>
                            </mat-form-field>
                            <mat-form-field appearance="outline" fxFlex.gt-md="24" fxFlex.gt-sm="32" fxFlex.gt-xs="49.5" fxFlex='grow'>
                                <mat-label i18n>Hẹn trả từ ngày</mat-label>
                                <input matInput [matDatepicker]="pickerAppointmentFrom" formControlName="advAppointmentFrom">
                                <mat-datepicker-toggle matSuffix [for]="pickerAppointmentFrom"></mat-datepicker-toggle>
                                <mat-datepicker #pickerAppointmentFrom></mat-datepicker>
                            </mat-form-field>
                            <mat-form-field appearance="outline" fxFlex.gt-md="24" fxFlex.gt-sm="32" fxFlex.gt-xs="49.5" fxFlex='grow'>
                                <mat-label i18n>Hẹn trả đến ngày</mat-label>
                                <input matInput [matDatepicker]="pickerAppointmentTo" formControlName="advAppointmentTo">
                                <mat-datepicker-toggle matSuffix [for]="pickerAppointmentTo"></mat-datepicker-toggle>
                                <mat-datepicker #pickerAppointmentTo></mat-datepicker>
                            </mat-form-field>
                            <!--    Advanced search row-5            -->
                            <mat-form-field *ngIf="!hideOtherAgencyDossierSearch && !isOptionSearchHGI && !qni_listsearch" appearance="outline" fxFlex.gt-md="24" fxFlex.gt-sm="32" fxFlex.gt-xs="49.5" fxFlex='grow'>
                                <mat-label i18n>Cơ quan</mat-label>
                                <mat-select formControlName="advAgency" (selectionChange)="agencyChange($event)" msInfiniteScroll (infiniteScroll)="getListAgency()" [complete]="isFullListAgency == true">
                                    <mat-option value=""><span i18n>Tất cả</span></mat-option>
                                    <mat-option *ngFor='let agency of listAgency' value="{{agency.id}}">
                                        {{agency.name}}
                                        <span *ngIf="agency.name == undefined || agency.name == null || agency.name.trim() == ''" i18n>(Không tìm thấy bản dịch)</span>
                                    </mat-option>
                                </mat-select>
                            </mat-form-field>
                            <mat-form-field *ngIf="!hideOtherAgencyDossierSearch" appearance="outline" fxFlex.gt-md="24" fxFlex.gt-sm="32" fxFlex.gt-xs="49.5" fxFlex='grow'>
                                <mat-label i18n>Trạng thái hồ sơ</mat-label>
                                <mat-select formControlName="advTaskStatusId" msInfiniteScroll (infiniteScroll)="getListDossierTaskName()" [complete]="isFullListDossierTaskName == true">
                                    <mat-option value=""><span i18n>Tất cả</span></mat-option>
                                    <mat-option *ngFor='let taskNameOpt of listDossierTaskName' value="{{taskNameOpt.id}}">
                                        {{taskNameOpt.name}}
                                        <span *ngIf="taskNameOpt.name == undefined || taskNameOpt.name == null || taskNameOpt.name.trim() == ''" i18n>(Không tìm thấy bản dịch)</span>
                                    </mat-option>
                                </mat-select>
                            </mat-form-field>
                            <!-- <mat-form-field appearance="outline" fxFlex.gt-md="24" fxFlex.gt-sm="32" fxFlex.gt-xs="49.5" fxFlex='grow'>
                        <mat-label i18n>Trạng thái</mat-label>
                        <mat-select formControlName="advProcessStatus">
                            <mat-option value=""><span i18n>Tất cả</span></mat-option>
                            <mat-option value="CREATED"><span i18n>CREATED</span></mat-option>
                            <mat-option value="ASSIGNED"><span i18n>ASSIGNED</span></mat-option>
                            <mat-option value="SUSPENDED"><span i18n>SUSPENDED</span></mat-option>
                            <mat-option value="COMPLETED"><span i18n>COMPLETED</span></mat-option>
                            <mat-option value="CANCELLED"><span i18n>CANCELLED</span></mat-option>
                            <mat-option value="DELETED"><span i18n>DELETED</span></mat-option>
                            <mat-option value="OVERDUE"><span i18n>Trễ hạn</span></mat-option>
                            <mat-option value=""><span i18n>Tất cả</span></mat-option>
                            <mat-option value="1"><span i18n>Chờ bổ sung</span></mat-option>
                            <mat-option value="2"><span i18n>Đang xử lý</span></mat-option>
                            <mat-option value="3"><span i18n>Đang tạm dừng</span></mat-option>
                            <mat-option value="4"><span i18n>Có kết quả</span></mat-option>
                            <mat-option value="5"><span i18n>Đã trả kết quả</span></mat-option>
                            <mat-option value="6"><span i18n>Đã hủy</span></mat-option>
                            <mat-option value="7"><span i18n>Trễ hạn</span></mat-option>
                        </mat-select>
                    </mat-form-field> -->
                            <mat-form-field *ngIf="!hideOtherAgencyDossierSearch" appearance="outline" fxFlex.gt-md="24" fxFlex.gt-sm="32" fxFlex.gt-xs="49.5" fxFlex='grow'>
                                <mat-label i18n>Trả kết quả từ ngày</mat-label>
                                <input matInput [matDatepicker]="pickerResultFrom" formControlName="avdResultReturnedFrom">
                                <mat-datepicker-toggle matSuffix [for]="pickerResultFrom"></mat-datepicker-toggle>
                                <mat-datepicker #pickerResultFrom></mat-datepicker>
                            </mat-form-field>
                            <mat-form-field *ngIf="!hideOtherAgencyDossierSearch" appearance="outline" fxFlex.gt-md="24" fxFlex.gt-sm="32" fxFlex.gt-xs="49.5" fxFlex='grow'>
                                <mat-label i18n>Trả kết quả đến ngày</mat-label>
                                <input matInput [matDatepicker]="pickerResultTo" formControlName="avdResultReturnedTo">
                                <mat-datepicker-toggle matSuffix [for]="pickerResultTo"></mat-datepicker-toggle>
                                <mat-datepicker #pickerResultTo></mat-datepicker>
                            </mat-form-field>
                            <ng-container *ngIf="env?.vnpost?.config && env?.vnpost?.config === '1'">
                                <mat-form-field appearance="outline" fxFlex.gt-md="49.5" fxFlex.gt-sm="49.5" fxFlex.gt-xs="49.5" fxFlex='grow' >
                                      <mat-label>Trạng thái VNPOST</mat-label>
                                      <mat-select formControlName="vnpostStatus" >
                                        <mat-option>
                                            <ngx-mat-select-search
                                              [formControl]="searchVnPostCtrl"
                                              placeholderLabel=""
                                              [disableScrollToActiveOnOptionsChanged]="true"
                                              i18n-noEntriesFoundLabel noEntriesFoundLabel="Không tìm thấy kết quả nào!">
                                            </ngx-mat-select-search>
                                          </mat-option>
                                          <mat-option value="">Chưa chọn</mat-option>
                                          <mat-option *ngFor="let vnpostStatus of listVnpostStatus" value="{{vnpostStatus.id}}">
                                              {{vnpostStatus.name}}
                                              <span
                                                  *ngIf="vnpostStatus.name == undefined || vnpostStatus.name == null || vnpostStatus.name.trim() == ''"
                                                  i18n>(Không tìm thấy bản dịch)</span>
                                          </mat-option>
                                      </mat-select>
                                  </mat-form-field>
                              </ng-container>
                              <mat-form-field *ngIf="dossierSearchShowAgencyUnit" appearance="outline" fxFlex.gt-md="24" fxFlex.gt-sm="32" fxFlex.gt-xs="49.5" fxFlex='grow'>
                                <mat-label>Đơn vị</mat-label>
                                <mat-select formControlName="taskAgencyCtrl" msInfiniteScroll (selectionChange)="agencyUnitChange($event)" (infiniteScroll)="getListAgencyUnit()" [complete]="isLastAgencyUnit">
                                    <mat-option>
                                        <ngx-mat-select-search
                                          [formControl]="searchAgencyUnitCtrl"
                                          placeholderLabel=""
                                          [disableScrollToActiveOnOptionsChanged]="true"
                                          [clearSearchInput]="false"
                                          i18n-noEntriesFoundLabel noEntriesFoundLabel="Không tìm thấy kết quả nào!">
                                    </ngx-mat-select-search>
                                    </mat-option>
                                    <mat-option value=""><span i18n>Tất cả</span></mat-option>
                                    <mat-option *ngFor='let agency of listAgencyUnit' value="{{agency.id}}">
                                        {{agency.name}}
                                        <span *ngIf="agency.name == undefined || agency.name == null || agency.name.trim() == ''" i18n>(Không tìm thấy bản dịch)</span>
                                    </mat-option>
                                </mat-select>
                            </mat-form-field>
                            <mat-form-field *ngIf="dossierSearchShowAssignee" appearance="outline" fxFlex.gt-md="24" fxFlex.gt-sm="32" fxFlex.gt-xs="49.5" fxFlex='grow'>
                                <mat-label>Người xử lý</mat-label>
                                <mat-select formControlName="taskAssigneeCtrl" msInfiniteScroll (infiniteScroll)="getListAssignee()" [complete]="isLastAssignee">
                                    <ngx-mat-select-search
                                          [formControl]="searchAssigneeCtrl"
                                          placeholderLabel=""
                                          [disableScrollToActiveOnOptionsChanged]="true"
                                          [clearSearchInput]="false"
                                          i18n-noEntriesFoundLabel noEntriesFoundLabel="Không tìm thấy kết quả nào!">
                                    </ngx-mat-select-search>
                                    <mat-option value=""><span i18n>Tất cả</span></mat-option>
                                    <mat-option *ngFor='let assignee of listAssignee' value="{{assignee.id}}">
                                        {{assignee.fullname}}
                                        <span *ngIf="assignee.fullname == undefined || assignee.fullname == null || assignee.fullname.trim() == ''" i18n>(Không tìm thấy bản dịch)</span>
                                    </mat-option>
                                </mat-select>
                            </mat-form-field>
                              <mat-form-field *ngIf="showReceiveDossierResultMethod" appearance="outline" fxFlex.gt-md="49.5" fxFlex.gt-sm="49.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                                <mat-label >Hình thức nhận kết quả</mat-label>
                                <mat-select formControlName="receivingKindCtrl" >
                                    <mat-option value=""><span i18n>Tất cả</span></mat-option>
                                    <mat-option *ngFor="let r of receivingKinds;" value="{{r.id}}">{{r.name}}

                                    </mat-option>
                                </mat-select>

                            </mat-form-field>
                            <mat-form-field appearance="outline" fxFlex.gt-sm="48.75" fxFlex.gt-xs="49.5" fxFlex='grow' *ngIf="qni_advancedsearch">
                                <mat-label i18n>Nội dung yêu cầu giải quyết</mat-label>
                                <input type="text" matInput formControlName="advnoidungyeucaugiaiquyet">
                            </mat-form-field>
                            <mat-form-field *ngIf="qni_advancedsearch && !qni_listsearch" appearance="outline" fxFlex.gt-sm="48.75" fxFlex.gt-xs="49.5" fxFlex='grow'>
                                <mat-label i18n>Tên người nộp</mat-label>
                                <input type="text" matInput formControlName="applicantName" maxlength="500">
                              </mat-form-field>
                              <mat-form-field appearance="outline" fxFlex.gt-sm="48.75" fxFlex.gt-xs="49.5" fxFlex='grow' *ngIf="QNIapprovalStatusSearch">
                                <mat-label i18n="@@approvalStatus">Trạng thái phê duyệt</mat-label>
                                <mat-select formControlName="approvalstatus" msInfiniteScroll (infiniteScroll)="getListDossierTaskName()" >
                                    <mat-option value=""><span i18n="@@all">Tất cả</span></mat-option>
                                    <mat-option value="1"><span i18n="@@disagreeWithAdditionalRequest">Không đồng ý yêu cầu bổ sung</span></mat-option>
                                    <mat-option value="2"><span i18n="@@doNotAgreeToExtend">Không đồng ý gia hạn</span></mat-option>
                                    <mat-option value="3"><span i18n="@@doNotAgreeToStopProcessing">Không đồng ý dừng xử lý</span></mat-option>
                                    <mat-option value="4"><span i18n="@@doNotAgreeToPause">Không đồng ý dừng tạm</span></mat-option>
                                    <mat-option value="5"><span i18n="@@agreeToTheAdditionalRequest">Đồng ý yêu cầu bổ sung</span></mat-option>
                                    <mat-option value="6"><span i18n="@@agreeToExtend">Đồng ý gia hạn</span></mat-option>
                                    <mat-option value="7"><span i18n="@@agreeToStopProcessing">Đồng ý dừng xử lý</span></mat-option>
                                    <mat-option value="8"><span i18n="@@agreeToPause">Đồng ý dừng tạm</span></mat-option>
                                </mat-select>

                            </mat-form-field>
                            <!-- KGG OS-->
                            <mat-form-field appearance="outline" fxFlex.gt-md="74.5" fxFlex.gt-sm="66" fxFlex.gt-xs="49.5" fxFlex='grow' *ngIf="isUserRQ">
                                <mat-label i18n="@@coQuanTiepNhanKGG">Cơ quan tiếp nhận ban đầu</mat-label>
                                <mat-select formControlName="agencySearchKGG">
                                  <mat-option value="" selected>Chưa chọn</mat-option>
                                  <mat-option *ngFor="let item of listAgencyKGG" [value]="item.value">
                                    {{item.name}}
                                  </mat-option>
                                </mat-select>
                            </mat-form-field>
                            <!--kgg-->
                            <mat-form-field *ngIf="isUserRQ || isShowSortHCM" appearance="outline" fxFlex.gt-md="24" fxFlex.gt-sm="32" fxFlex.gt-xs="49.5" fxFlex='grow'>
                                <mat-label i18n="@@sortName">Sắp xếp</mat-label>
                                <mat-select formControlName="sortCtrl" [(ngModel)]="sortId" (selectionChange)="sortChange()">
                                    <mat-option *ngFor="let r of arrSortType;" value="{{r.id}}">{{r.value}}</mat-option>
                                </mat-select>
                            </mat-form-field>

                            <mat-form-field *ngIf="isShowContent" appearance="outline" fxFlex.gt-md="74.5" fxFlex.gt-sm="66" fxFlex.gt-xs="49.5" fxFlex='grow'>
                              <mat-label i18n>Nội dung trích yếu</mat-label>
                                <input type="text" matInput formControlName="contentMain">
                            </mat-form-field>
                            <!-- kgg end-->
                            <mat-form-field appearance="outline" fxFlex.gt-md="74.5" fxFlex.gt-sm="66" fxFlex.gt-xs="49.5" fxFlex='grow' *ngIf="isFilterAddressOrganization">
                                <mat-label >Địa chỉ Cơ quan/Doanh nghiệp</mat-label>
                                <input type="text" matInput formControlName="advAddressOrganization">
                            </mat-form-field>
                            <!-- Thursday 13/04/2023 - quocpa-IGATESUPP-44355-->
                            <ng-container *ngIf="enableFilterAppliedDate && !isShowFilterApplyDate">
                              <mat-form-field appearance="outline" fxFlex.gt-md="49.5" fxFlex.gt-sm="49.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                                <mat-label i18n="@@appliedDateFrom">Ngày nộp từ ngày</mat-label>
                                <input matInput [matDatepicker]="pickerFilingFrom" formControlName="advFilingFrom">
                                <mat-datepicker-toggle matSuffix [for]="pickerFilingFrom"></mat-datepicker-toggle>
                                <mat-datepicker #pickerFilingFrom></mat-datepicker>
                              </mat-form-field>
                              <mat-form-field appearance="outline" fxFlex.gt-md="49.5" fxFlex.gt-sm="49.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                                <mat-label i18n="@@appliedDateTo">Ngày nộp đến ngày</mat-label>
                                <input matInput [matDatepicker]="pickerFilingTo" formControlName="advFilingTo">
                                <mat-datepicker-toggle matSuffix [for]="pickerFilingTo"></mat-datepicker-toggle>
                                <mat-datepicker #pickerFilingTo></mat-datepicker>
                              </mat-form-field>
                            </ng-container>
                            <!--end Thursday 13/04/2023 - quocpa-IGATESUPP-44355-->
                            <!--qbh IGATESUPP-111250-->
                            <mat-form-field *ngIf="showSearchPhoneNumber" appearance="outline" fxFlex.gt-sm="48.75" fxFlex.gt-xs="49.5" fxFlex='grow'>
                                <mat-label>Số điện thoại người nộp</mat-label>
                                <input type="text" matInput formControlName="phoneNumberApply" maxlength="500">
                            </mat-form-field>
                        </div>
                    </mat-expansion-panel>
                </mat-accordion>
            </form>
        </div>
    </div>
</div>
<div fxLayout="row" fxLayoutAlign="center">
    <div fxFlex="grow">
        <div class="top-controlcancel" fxLayout="row" fxLayoutAlign="start">
            <button style = "margin-left: 10px;" mat-flat-button *ngIf="ExportPageDossiersToExcelQNI && !exportExcelQNM"  class="primary-btn" (click)="getListDossierAllExcel_QNI()" >
                <mat-icon style = "margin-right: 10px;">cloud_download</mat-icon>
                <span>Xuất excel</span>
            </button>
            <button *ngIf="showUpdateAgencyButton" style = "margin-left: 10px;" mat-flat-button class="primary-btn" (click)="showDialogUpdateAgency()" >
                <mat-icon style = "margin-right: 10px;">cloud_download</mat-icon>
                <span>Cập nhật đơn vị</span>
            </button>
        </div>
    </div>
</div>
<div fxLayout="row" fxLayoutAlign="center">
    <div fxFlex="grow">
        <div class="top-controlcancel" fxLayout="row" fxLayoutAlign="start">
            <button style = "margin-left: 10px;" mat-flat-button *ngIf="exportExcelQNM"  class="primary-btn" (click)="exportExcelDossierQNM()" >
                <mat-icon style = "margin-right: 10px;">cloud_download</mat-icon>
                <span>Xuất excel</span>
            </button>
            <button style = "margin-left: 10px;" mat-flat-button *ngIf="exportExcelQNM"  class="primary-btn" (click)="downloadAsPDF()" >
                <mat-icon style = "margin-right: 10px;">cloud_download</mat-icon>
                <span>Xuất PDF</span>
            </button>
            <button style = "margin-left: 10px;" mat-flat-button *ngIf="!!hasExcelGPLXRole"  class="primary-btn" (click)="excelDoiGPLX()" >
                <mat-icon style = "margin-right: 10px;">cloud_download</mat-icon>
                <span>Xuất GPLX</span>
            </button>
        </div>
    </div>
</div>
<div fxLayout="row" fxLayoutAlign="center">
    <div fxFlex="grow">
        <div class="top-controlcancel" fxLayout="row" fxLayoutAlign="start">
            <button style = "margin-left: 10px;" mat-flat-button *ngIf="showFinancialObligations.enable"  class="primary-btn" (click)="onShowFinancialObligations()" >
                <span>Đồng bộ ngày hẹn trả</span>
            </button>
        </div>
    </div>
</div>

<div *ngIf="checkNullData === 1 then nullData; else hasData"></div>
<ng-template #nullData>
  <br/>
  <br/>
  <br/>
  <div fxLayout="row" fxLayoutAlign="center">
    <div fxFlex="grow" style="text-align: center; font-size: 18px">Không có kết quả tìm kiếm</div>
  </div>
</ng-template>
<ng-template #hasData>
<div fxLayout="row" fxLayoutAlign="center">
    <div class="frm_main" fxFlex="grow">
        <div class="top-control">
            <button
                mat-flat-button
                [disabled]="selectedDossiers.length == 0"
                [ngClass]="{ 'primary-btn': selectedDossiers.length !== 0 , '' : selectedDossiers.length == 0 }"
                (click)="confirmOriginalDossier()"
                *ngIf="vpcConfirmOriginalDossier == 1"
                style="margin-right: 10px;"
            >
              <mat-icon>done_all</mat-icon>
              <span>Đã nhận hồ sơ gốc</span>
            </button>
            <!-- <button mat-stroked-button class="btn-claim" fxFlex.gt-md="12" fxFlex.gt-sm="32" fxFlex.gt-xs="40"
                fxFlex='grow'>
                <mat-icon>book_online</mat-icon><span i18n>Nhận xử lý</span>
            </button> -->

            <!-- <mat-form-field class="cbx-print-report" appearance="outline" fxFlex.gt-md="12" fxFlex.gt-sm="32"
                fxFlex.gt-xs="40" fxFlex='grow'>
                <span matPrefix style="margin-right: 8px;">
                    <mat-icon class="icon">print</mat-icon>
                </span>
                <mat-label><span i18n>In phiếu</span></mat-label>
                <mat-select (selectionChange)="printReportChange($event)">
                    <mat-option value="0"><span>Phiếu tiếp nhận</span></mat-option>
                    <mat-option value="1"><span>Phiếu lệ phí</span></mat-option>
                </mat-select>
            </mat-form-field> -->
            <button
                mat-flat-button
                fxFlex.gt-sm="12"
                fxFlex.gt-xs="49.5"
                fxFlex='grow'
                [disabled]="selectedDossiers.length == 0"
                [ngClass]="{ 'primary-btn': selectedDossiers.length !== 0, '':   selectedDossiers.length == 0 }"
                (click)="deleteMultiDossier()"
                *ngIf="hasDossierDeletePermission && oneGateDeleteDossier === '1' && !hideDeleteButtonDossier"
            >
                <mat-icon>delete_outline</mat-icon><span i18n>Xóa hồ sơ</span>
            </button>
            <!-- <button mat-flat-button class="btnSecondary" *ngFor="let bill of listConfigTemplate" (click)="viewPreview(bill.file.path)" *ngIf="showBtbBB == true">
                <mat-icon>insights</mat-icon>
                <span>Biên bản bàn giao</span>
            </button> -->
            <span class="space">&nbsp;</span>
            <button mat-flat-button class="btn-print" [disabled]="selectedDossiers.length == 0"
            [ngClass]="{ 'primary-btn': selectedDossiers.length !== 0, '':   selectedDossiers.length == 0 }" *ngIf="showBtbBB == true" [matMenuTriggerFor]="printMenu" #languageMenuTrigger="matMenuTrigger">
                <mat-icon>print</mat-icon>
                <span i18n="@@printTrans">In phiếu giao hs</span>
                <mat-icon>keyboard_arrow_down</mat-icon>
            </button>
            <mat-menu #printMenu="matMenu" [overlapTrigger]="false">
                <ng-container *ngIf="listConfigTemplate.length>0">
                    <div *ngFor="let bill of listConfigTemplate" fxLayout="row" fxLayoutAlign="space-between">
                        <button mat-menu-item (click)="viewPreview(bill.file.path)">
                            {{bill.name}}
                            </button>
                    </div>
                </ng-container>
            </mat-menu>
            <!-- <span class="space">&nbsp;</span>
            <button type='button' mat-flat-button  class="btn-print"
            [disabled]="selectedDossiers.length == 0" [ngClass]="{ 'primary-btn': selectedDossiers.length !== 0, '':   selectedDossiers.length == 0 }"
            (click)="viewPreview()" *ngIf="showBtbBB == true">
                <mat-icon class="iconStatistical">print</mat-icon>
                <span i18n="@@printTrans">In phiếu giao hs</span>
            </button> -->
            <span class="space">&nbsp;</span>
            <button type='button' mat-flat-button class="btn-download-excel"
            [disabled]="selectedDossiers.length == 0" [ngClass]="{ 'primary-btn': selectedDossiers.length !== 0, '':   selectedDossiers.length == 0 }"
            (click)="exportToExcel()" *ngIf="showBtbBB == true">
                <mat-icon class="iconStatistical">cloud_download</mat-icon>
                <span i18n>Xuất excel</span>
            </button>
        </div>
        <div class="searchtbl">
            <table mat-table [dataSource]="dataSource">
                <!-- <ng-container matColumnDef="stt">
                    <mat-header-cell *matHeaderCellDef>
                        <mat-checkbox class="checkbox" [(ngModel)]="isCheckedAll" (change)="setAll($event.checked)">
                        </mat-checkbox>
                    </mat-header-cell>
                    <mat-cell *matCellDef="let row" data-label="">
                        <mat-checkbox class="checkbox" [checked]=row.checked></mat-checkbox>
                    </mat-cell>
                </ng-container> -->
                <ng-container matColumnDef="select">
                    <mat-header-cell *matHeaderCellDef class="checkAllProcedureAdd">
                        <mat-checkbox [checked]="checkAll" (change)="checkAllItem($event)">
                        </mat-checkbox>
                    </mat-header-cell>
                    <mat-cell *matCellDef="let row; index as i" data-label="Chọn" class="checkAllProcedureAdd">
                        <mat-checkbox (change)="checkItem($event, row)" [(ngModel)]="row.checked">
                        </mat-checkbox>
                    </mat-cell>
                </ng-container>
                <!--QNI-->
                <ng-container matColumnDef="stt">
                    <mat-header-cell *matHeaderCellDef i18n>STT</mat-header-cell>
                    <mat-cell *matCellDef="let row" i18n-data-label data-label="STT"> {{row.stt}} </mat-cell>
                </ng-container>
                <!--QNI-->

                 <!--HPG-->
                 <ng-container matColumnDef="sector" >
                    <mat-header-cell *matHeaderCellDef >Lĩnh vực</mat-header-cell>
                    <mat-cell [ngStyle]="{'color': row.due[0]?.timesheet?.color || 'black'}" *matCellDef="let row"  i18n-data-label data-label="Lĩnh vực">
                        <span>
                            {{row.procedure?.sector?.code}}
                        </span>
                    </mat-cell>
                </ng-container>
                  <!--HPG-->


                <ng-container matColumnDef="code">
                    <mat-header-cell [ngStyle]="{'min-width': configWorkInterfaceKHA ? '300px' : 'unset'}" *matHeaderCellDef i18n [ngClass]="{'colum_code': isQNM }">Mã số hồ sơ</mat-header-cell>
                    <mat-cell *matCellDef="let row" [ngStyle]="{'min-width': configWorkInterfaceKHA ? '300px' : 'unset', 'display': configWorkInterfaceKHA ? 'block' : null}" i18n-data-label data-label="Mã số hồ sơ" style="display:block;"[ngClass]="{ 'cell_code_online': row.applyMethod?.id === 0||4, 'cell_code_direct':   row.applyMethod?.id === 1, 'cell_code_other':   row.applyMethod?.id === 2, 'colum_code': isQNM }" #tooltip="matTooltip"
                        [matTooltip]="showDossierName?row.codeText+' - '+row.applicant?.data?.tenHoSo:row.codeText" style="flex-direction: column">
                        <a (click)="dossierDetail($event,row.id, row.procedure?.id, row.task)"(contextmenu)="onRightClick(row.id,row.procedure?.id)">{{row.code}}<span *ngIf="!!row.nationCode && row.nationCode != ''"><br> ({{row.nationCode}})</span></a>
                        <li *ngIf="row.takeNumber!=null "><span [ngStyle]="{'color': row.due[0]?.timesheet?.color }" matTooltip="{{row.takeNumber}}"  >  Mã số được cấp: {{row.takeNumber}}</span></li>
                        <li *ngIf="!!row?.listCodeTakeNumber && row?.listCodeTakeNumber.length != 0"><span [ngStyle]="{'color': row.due[0]?.timesheet?.color }" matTooltip="{{row.listCodeTakeNumber}}" >  Mã số được cấp: {{row.listCodeTakeNumber}}</span></li>
                        <br>
                        <span class="more-text dossier-name" *ngIf="showDossierName" style="color: rgba(0,0,0,.87); font-weight: normal"> {{row.applicant?.data?.tenHoSo}}</span>
                        <span *ngIf="configWorkInterfaceKHA">
                            <span>
                                <span *ngIf="row.applyMethod.id === 0 || row.applyMethod.id === 4"
                                    style="color: rgba(0,0,0,.87); font-weight: bold">[Trực tuyến]</span>
                                <span *ngIf="row.applyMethod.id === 1"
                                    style="color: rgba(0,0,0,.87); font-weight: bold">[Trực tiếp]</span>
                                <span *ngIf="row.applyMethod.id === 2"
                                    style="color: rgba(0,0,0,.87); font-weight: bold">[Khác]</span>
                            </span>
                            <br>
                            <ng-container *ngIf="row.applicant?.data?.is2Cap == 1; else oldInterfaceKHA">
                                <ng-container *ngIf="row.applicant?.data?.CaNhan_ToChuc === 'toChuc'; else elseBlockKHA">
                                    <span class="more-text-kha"
                                        style="color: rgba(0,0,0,.87); font-weight: bold">
                                        - Cá nhân/tổ chức nộp: <span style="font-weight: normal">{{row.applicant?.data?.tenToChuc}}</span>
                                    </span>                   
                                    <span class="more-text-kha" *ngIf="(row.applicant?.data?.address1 != '' && row.applicant?.data?.address1) || (row.applicant?.data?.address != '' && row.applicant?.data?.address)"><span style="color: rgba(0,0,0,.87); font-weight: bold">- Địa chỉ chi tiết: <span style="font-weight: normal">{{row.applicant?.data?.address1 || row.applicant?.data?.address}} </span></span><br></span>
                                    <span class="more-text-kha" *ngIf="(row.applicant?.data?.village1?.label != '' && row.applicant?.data?.village1?.label) || (row.applicant?.data?.PhuongXa1?.label != '' && row.applicant?.data?.PhuongXa1?.label)"><span style="color: rgba(0,0,0,.87); font-weight: bold">- Phường/xã : <span style="font-weight: normal">{{row.applicant?.data?.village1?.label || row.applicant?.data?.PhuongXa1?.label}} </span></span><br></span>                                    
                                    <span class="more-text-kha" *ngIf="(row.applicant?.data?.province1?.label != '' && row.applicant?.data?.province1?.label) || (row.applicant?.data?.TinhTP1?.label != '' && row.applicant?.data?.TinhTP1?.label)"><span style="color: rgba(0,0,0,.87); font-weight: bold">- Tỉnh/thành phố: <span style="font-weight: normal">{{row.applicant?.data?.province1?.label || row.applicant?.data?.TinhTP1?.label}} </span></span><br></span>
                                    <span *ngIf="row.applicant?.data?.noidungyeucaugiaiquyet != '' && row.applicant?.data?.noidungyeucaugiaiquyet" class="more-text-kha" 
                                        style="color: rgba(0,0,0,.87); font-weight: bold">
                                        - Nội dung xử lý: <span style="font-weight: normal">{{row.applicant.data.noidungyeucaugiaiquyet}}</span>
                                    </span>
                                    <span *ngIf="row.applicant?.data?.noidungyeucaugiaiquyet1 != '' && row.applicant?.data?.noidungyeucaugiaiquyet1" class="more-text-kha" 
                                        style="color: rgba(0,0,0,.87); font-weight: bold">
                                        - Nội dung xử lý: <span style="font-weight: normal">{{row.applicant.data.noidungyeucaugiaiquyet1}}</span>
                                    </span>
                                </ng-container>
                                <ng-template #elseBlockKHA>
                                    <span class="more-text-kha"
                                        style="color: rgba(0,0,0,.87); font-weight: bold">
                                        - Người nộp: <span style="font-weight: normal">{{row.applicant?.data?.fullname}}</span>
                                    </span>
                                    <span class="more-text-kha" *ngIf="row.applicant?.data?.address != '' && row.applicant?.data?.address"><span style="color: rgba(0,0,0,.87); font-weight: bold">- Địa chỉ chi tiết: <span style="font-weight: normal">{{row.applicant?.data?.address}}</span></span><br></span>
                                    <span class="more-text-kha" *ngIf="row.applicant?.data?.village?.label != '' && row.applicant?.data?.village?.label"><span style="color: rgba(0,0,0,.87); font-weight: bold">- Phường/xã: <span style="font-weight: normal">{{row.applicant?.data?.village?.label}}</span></span><br></span>                                    
                                    <span class="more-text-kha" *ngIf="row.applicant?.data?.province?.label != '' && row.applicant?.data?.province?.label"><span style="color: rgba(0,0,0,.87); font-weight: bold">- Tỉnh/thành phố: <span style="font-weight: normal">{{row.applicant?.data?.province?.label}}</span></span><br></span>                                
                                    <span class="more-text-kha"
                                        style="color: rgba(0,0,0,.87); font-weight: bold">
                                        - Nội dung xử lý: <span style="font-weight: normal">{{row.applicant.data.noidungyeucaugiaiquyet}}</span>
                                    </span> 
                                </ng-template>
                            </ng-container>
                            <ng-template #oldInterfaceKHA>
                                <ng-container *ngIf="row.applicant?.data?.CaNhan_ToChuc === 'toChuc'; else elseBlockKHA">
                                    <span class="more-text-kha"
                                        style="color: rgba(0,0,0,.87); font-weight: bold">
                                        - Cá nhân/tổ chức nộp: <span style="font-weight: normal">{{row.applicant?.data?.tenToChuc}}</span>
                                    </span>                   
                                    <span class="more-text-kha" *ngIf="row.applicant?.data?.address1 != '' && row.applicant?.data?.address1"><span style="color: rgba(0,0,0,.87); font-weight: bold">- Địa chỉ chi tiết: <span style="font-weight: normal">{{row.applicant?.data?.address1}} </span></span><br></span>
                                    <span class="more-text-kha" *ngIf="row.applicant?.data?.village1?.label != '' && row.applicant?.data?.village1?.label"><span style="color: rgba(0,0,0,.87); font-weight: bold">Phường/xã : <span style="font-weight: normal">{{row.applicant?.data?.village1?.label}} </span></span><br></span>
                                    <span class="more-text-kha" *ngIf="row.applicant?.data?.district1?.label != '' && row.applicant?.data?.district1?.label"><span style="color: rgba(0,0,0,.87); font-weight: bold">Quận/huyện: <span style="font-weight: normal">{{row.applicant?.data?.district1?.label}} </span></span><br></span>
                                    <span class="more-text-kha" *ngIf="row.applicant?.data?.province1?.label != '' && row.applicant?.data?.province1?.label"><span style="color: rgba(0,0,0,.87); font-weight: bold">Tỉnh/thành phố: <span style="font-weight: normal">{{row.applicant?.data?.province1?.label}} </span></span><br></span>
                                    <span class="more-text-kha"
                                        style="color: rgba(0,0,0,.87); font-weight: bold">
                                        - Nội dung xử lý: <span style="font-weight: normal">{{row.applicant.data.noidungyeucaugiaiquyet1}}</span>
                                    </span> 
                                </ng-container>
                                <ng-template #elseBlockKHA>
                                    <span class="more-text-kha"
                                        style="color: rgba(0,0,0,.87); font-weight: bold">
                                        - Người nộp: <span style="font-weight: normal">{{row.applicant?.data?.fullname}}</span>
                                    </span>
                                    <span class="more-text-kha" *ngIf="row.applicant?.data?.address != '' && row.applicant?.data?.address"><span style="color: rgba(0,0,0,.87); font-weight: bold">- Địa chỉ chi tiết: <span style="font-weight: normal">{{row.applicant?.data?.address}}</span></span><br></span>
                                    <span class="more-text-kha" *ngIf="row.applicant?.data?.village?.label != '' && row.applicant?.data?.village?.label"><span style="color: rgba(0,0,0,.87); font-weight: bold">- Phường/xã: <span style="font-weight: normal">{{row.applicant?.data?.village?.label}}</span></span><br></span>
                                    <span class="more-text-kha" style="color: rgba(0,0,0,.87); font-weight: bold" *ngIf="row.applicant?.data?.district?.label != '' && row.applicant?.data?.district?.label"><span style="color: rgba(0,0,0,.87); font-weight: bold">- Quận/huyện: <span style="font-weight: normal">{{row.applicant?.data?.district?.label}}</span></span><br></span>                        
                                    <span class="more-text-kha" *ngIf="row.applicant?.data?.province?.label != '' && row.applicant?.data?.province?.label"><span style="color: rgba(0,0,0,.87); font-weight: bold">- Tỉnh/thành phố: <span style="font-weight: normal">{{row.applicant?.data?.province?.label}}</span></span><br></span>                                
                                    <span class="more-text-kha"
                                        style="color: rgba(0,0,0,.87); font-weight: bold">
                                        - Nội dung xử lý: <span style="font-weight: normal">{{row.applicant.data.noidungyeucaugiaiquyet}}</span>
                                    </span> 
                                </ng-template>    
                            </ng-template>                             
                        </span>                        
                    </mat-cell>
                </ng-container>

                <ng-container matColumnDef="procedureName">
                    <mat-header-cell *matHeaderCellDef i18n>Thủ tục</mat-header-cell>
                    <mat-cell  [ngStyle]="{'cell_code': !row.due[0]?.timesheet?.color}" *matCellDef="let row" i18n-data-label data-label="Thủ tục">
                        <span>
                            <a [ngStyle]="configWorkInterfaceKHA ? {'color': 'black', 'font-weight': 'bold'} : {'color': row.due[0]?.timesheet?.color}" class="cell_code" (click)="dossierDetail($event,row?.id, row?.procedure?.id, row?.task)">{{row.procedure?.code}}</a>
                            <span [ngStyle]="{'color': row.due[0]?.timesheet?.color }" class="procedureName" *ngIf="row?.procedure?.translate == undefined"> - <span i18n>(Không tìm thấy bản dịch)</span></span>
                        <span class="procedureName" *ngIf="row.procedure?.translate" #tooltip="matTooltip" matTooltip="{{row?.procedure?.translate?.name}}"> - {{row.procedure?.translate?.name}}
                            <span *ngIf="row?.procedure?.translate != undefined && ( row?.procedure?.translate?.name == undefined || row?.procedure?.translate?.name == null || row?.procedure?.translate?.name.trim() == '')" i18n>(Không tìm thấy bản dịch)</span></span>
                        </span>
                    </mat-cell>
                </ng-container>
                <ng-container *ngIf="qbhlistlayout == true" matColumnDef="noidungyeucaugiaiquyet">
                    <mat-header-cell *matHeaderCellDef [ngClass]="{'colum_noidungyeucaugiaiquyet_qbh': qbhlistlayout}">Nội dung yêu cầu giải quyết</mat-header-cell>
                    <mat-cell [ngStyle]="{'color': row.due[0]?.timesheet?.color || 'black'}" [ngClass]="{'colum_noidungyeucaugiaiquyet_qbh': qbhlistlayout}" *matCellDef="let row"  i18n-data-label data-label="Nội dung yêu cầu giải quyết">
                        <span [ngClass]="{'text-qbh': qbhlayoutformalities == true}">
                            {{row.applicant?.data?.noidungyeucaugiaiquyet}}
                        </span>
                    </mat-cell>
                  </ng-container>
                <!--QNI-->
                <ng-container *ngIf="qbhlistlayout == false" matColumnDef="noidungyeucaugiaiquyet">
                    <mat-header-cell *matHeaderCellDef [ngClass]="{'colum_noidungyeucaugiaiquyet': isQNM}">Nội dung yêu cầu giải quyết</mat-header-cell>
                    <mat-cell [ngStyle]="{'color': row.due[0]?.timesheet?.color || 'black'}" [ngClass]="{'colum_noidungyeucaugiaiquyet': isQNM}" *matCellDef="let row"  i18n-data-label data-label="Nội dung yêu cầu giải quyết">
                        <span>
                            {{row.applicant?.data?.noidungyeucaugiaiquyet}}
                        </span>
                    </mat-cell>
                  </ng-container>
                  <ng-container matColumnDef="phoneNumber">
                    <mat-header-cell *matHeaderCellDef >Số điện thoại</mat-header-cell>
                    <mat-cell [ngStyle]="{'color': row.due[0]?.timesheet?.color || 'black'}" *matCellDef="let row"  i18n-data-label data-label="Số điện thoại">
                        <span>
                            {{row.applicant?.data?.phoneNumber}}
                        </span>
                    </mat-cell>
                  </ng-container>
                <!--QNIEND-->

                <ng-container matColumnDef="processingTime">
                    <mat-header-cell *matHeaderCellDef i18n [ngClass]="{'colum_processingTime': isQNM }">Thời gian quy định</mat-header-cell>
                    <mat-cell *matCellDef="let row" [ngClass]="{'colum_processingTime': isQNM }" i18n-data-label data-label="Thời gian quy định">
                        <ul [ngStyle]="{'color': row.due[0]?.timesheet?.color || 'black'}">
                            <span *ngIf="!!row.acceptedDate && statusNeedsCalculatorTiming.includes(row.dossierStatus?.id) && row.undefindedCompleteTime === 0 && row.dossierStatus?.checkFinaceObligating === false && vpcResolutionProgressDossier == 0">
                                <span class="due" *ngIf="row.due.length > 0 && ([undefined,null].includes(row.due[0]?.timesheet?.color) || (!env.colorDue))">
                                    <span class="overdue" *ngIf="row.due[0].timesheet.isOverDue == true && row.checkHindenOverDueCalculation" >Đã quá hạn </span>
                                    <span *ngIf="row.due[0].timesheet.isOverDue == false && !row.due[0].timesheet.isEarlyDue">Còn lại </span>
                                    <span *ngIf="row.due[0].timesheet.isOverDue == false && row.due[0].timesheet?.isEarlyDue == true">Sớm hạn </span>
                                    <ng-container *ngIf="row.due[0].timesheet.isOverDue == true; then overDueTime else earrlyDueTime;"></ng-container>
                                    <ng-template #overDueTime>
                                        <span *ngIf="row.checkHindenOverDueCalculation" [ngClass]="{'overdue': row.due[0].timesheet.isOverDue == true}">
                                            <span class="timeNumber">{{row.due[0].timesheet.timer.day}}</span><span> ngày </span>
                                            <span class="timeNumber">{{row.due[0].timesheet.timer.hour}}</span><span> giờ </span>
                                            <span class="timeNumber">{{row.due[0].timesheet.timer.minute}}</span><span> phút </span>
                                            <span class="timeNumber">{{row.due[0].timesheet.timer.second}}</span><span> giây </span>
                                        </span>
                                    </ng-template>
                                    <ng-template #earrlyDueTime>
                                        <span [ngClass]="{'overdue': row.due[0].timesheet.isOverDue == true}">
                                            <span class="timeNumber">{{row.due[0].timesheet.timer.day}}</span><span> ngày </span>
                                            <span class="timeNumber">{{row.due[0].timesheet.timer.hour}}</span><span> giờ </span>
                                            <span class="timeNumber">{{row.due[0].timesheet.timer.minute}}</span><span> phút </span>
                                            <span class="timeNumber">{{row.due[0].timesheet.timer.second}}</span><span> giây </span>
                                        </span>
                                    </ng-template>
                                    <!-- <span [ngClass]="{'overdue': row.due[0].timesheet.isOverDue == true}">
                                        <span class="timeNumber">{{row.due[0].timesheet.timer.day}}</span><span> ngày </span>
                                        <span class="timeNumber">{{row.due[0].timesheet.timer.hour}}</span><span> giờ </span>
                                        <span class="timeNumber">{{row.due[0].timesheet.timer.minute}}</span><span> phút </span>
                                        <span class="timeNumber">{{row.due[0].timesheet.timer.second}}</span><span> giây </span>
                                    </span> -->
                                </span>
                            </span>
                            <span *ngIf="!!row.acceptedDate && vpcResolutionProgressDossier == 1 && vpcStatusNeedsCalculatorTiming.includes(row.dossierStatus?.id)">
                                <span class="due" *ngIf="row.due.length > 0 && ([undefined,null].includes(row.due[0]?.timesheet?.color) || (!env.colorDue))">
                                    <span class="overdue" *ngIf="row.due[0].timesheet.isOverDue == true && row.checkHindenOverDueCalculation" >Quá hạn </span>
                                    <span *ngIf="row.due[0].timesheet.isOverDue == false && !row.due[0].timesheet.isEarlyDue">Đúng hạn </span>
                                    <span *ngIf="row.due[0].timesheet.isOverDue == false && row.due[0].timesheet?.isEarlyDue == true">Trước hạn </span>
                                    <ng-container *ngIf="row.due[0].timesheet.isOverDue == true; then overDueTime else earrlyDueTime;"></ng-container>
                                    <ng-template #overDueTime>
                                        <span *ngIf="row.checkHindenOverDueCalculation" [ngClass]="{'overdue': row.due[0].timesheet.isOverDue == true}">
                                            <span class="timeNumber">{{row.due[0].timesheet.timer.day}}</span><span> ngày </span>
                                            <span class="timeNumber">{{row.due[0].timesheet.timer.hour}}</span><span> giờ </span>
                                            <span class="timeNumber">{{row.due[0].timesheet.timer.minute}}</span><span> phút </span>
                                            <span class="timeNumber">{{row.due[0].timesheet.timer.second}}</span><span> giây </span>
                                        </span>
                                    </ng-template>
                                    <ng-template #earrlyDueTime>
                                        <span [ngClass]="{'overdue': row.due[0].timesheet.isOverDue == true}">
                                            <span class="timeNumber">{{row.due[0].timesheet.timer.day}}</span><span> ngày </span>
                                            <span class="timeNumber">{{row.due[0].timesheet.timer.hour}}</span><span> giờ </span>
                                            <span class="timeNumber">{{row.due[0].timesheet.timer.minute}}</span><span> phút </span>
                                            <span class="timeNumber">{{row.due[0].timesheet.timer.second}}</span><span> giây </span>
                                        </span>
                                    </ng-template>
                                </span>
                                <span class="color-due" *ngIf="row.due.length > 0 && row.due[0]?.timesheet?.color &&  (env.colorDue) ">
                                    <span *ngIf="row.due[0].timesheet.isOverDue == true">Đã quá hạn </span>
                                    <span *ngIf="row.due[0].timesheet.isOverDue == false && !row.due[0].timesheet.isEarlyDue">Còn lại </span>
                                    <span *ngIf="row.due[0].timesheet.isOverDue == false && row.due[0].timesheet?.isEarlyDue == true">Sớm hạn </span>
                                    <span >
                                        <span class="timeNumber">{{row.due[0].timesheet.timer.day}}</span><span> ngày </span>
                                        <span class="timeNumber">{{row.due[0].timesheet.timer.hour}}</span><span> giờ </span>
                                        <span class="timeNumber">{{row.due[0].timesheet.timer.minute}}</span><span> phút </span>
                                        <span class="timeNumber">{{row.due[0].timesheet.timer.second}}</span><span> giây </span>
                                    </span>
                                </span>
                            </span>
                            <li [ngStyle]="{'font-weight': isBoldInfor ? '500': 'unset' }"><span>Ngày nộp: </span>{{row.appliedDate | date : 'dd/MM/yyyy HH:mm:ss'}}</li>
                            <li [ngStyle]="{'font-weight': isBoldInfor ? '500': 'unset' }" *ngIf="row.acceptedDate != undefined"><span>Ngày tiếp nhận: </span>{{(!!row.oldAcceptedDate? row.oldAcceptedDate :row.acceptedDate) | date : 'dd/MM/yyyy HH:mm:ss'}}</li>
                            <li *ngIf="additionalRequirementDate === true && !!row.additionalRequirementDetail?.acceptedDate"><span>Ngày tiếp nhận bổ sung: </span>{{row.additionalRequirementDetail?.acceptedDate | date : 'dd/MM/yyyy HH:mm:ss'}}</li>
                            <!-- <li *ngIf="row.acceptedDate != undefined"><span>Hạn xử lý toàn quy trình: </span>{{row.dossierEndDate | date : 'dd/MM/yyyy HH:mm:ss'}}</li> -->
                            <ng-container *ngIf="row?.procedure?.sameDayPayProfile == true && row?.processingTime == 1; else dueBlock2">
                                <span i18n>Hạn xử lý toàn quy trình: </span>
                                <ng-container *ngIf="row?.approvalData?.extendTime != null && row?.approvalData?.extendTime != '' && row?.approvalData?.extendTime != undefined; then extendTime else appointment;"></ng-container>
                                <ng-template #extendTime>
                                    <span>{{row?.approvalData?.extendTime | date : 'dd/MM/yyyy HH:mm:ss'}}</span>
                                </ng-template>
                                <ng-template #appointment>
                                    <span>{{row.appointmentDate | date : 'dd/MM/yyyy HH:mm:ss'}}</span>
                                </ng-template>
                            </ng-container>
                            <ng-template #dueBlock2>
                            <ng-container *ngIf="row.undefindedCompleteTime != 1 && !getCheckHideDossierTaskStatus(row); then existAppointment else notExistAppointment;"></ng-container>
                            <ng-template #existAppointment>
                                <li *ngIf="row.acceptedDate != undefined && !getCheckHideDossierTaskStatus(row)">
                                    <span>Hạn xử lý toàn quy trình: </span>{{ ((row?.dueDate && row?.convertType === 1) ? row?.dueDate  : row.dossierEndDate) | date : 'dd/MM/yyyy HH:mm:ss'}}</li>
<!--                                <li *ngIf="row.acceptedDate != undefined && (row.appointmentDate == null || row.appointmentDate == undefined)">-->
<!--                                    <span>Hạn xử lý toàn quy trình: </span>{{row.dossierEndDate | date : 'dd/MM/yyyy HH:mm:ss'}}</li>-->
                            </ng-template>
                            <ng-template #notExistAppointment>
                                <li *ngIf="!getCheckHideDossierTaskStatus(row)"><span>Hạn xử lý toàn quy trình: </span>(<i i18n>Không xác định thời hạn</i>)</li>
                            </ng-template>
                            </ng-template> 
                            <!-- <li *ngIf="row.acceptedDate != undefined && row.undefindedCompleteTime === 0"><span>Ngày hẹn trả: </span>{{row.appointmentDate | date : 'dd/MM/yyyy HH:mm:ss'}}</li> -->
                            <li [ngStyle]="{'font-weight': isBoldInfor ? '500': 'unset' }" *ngIf="row.acceptedDate != undefined && row.undefindedCompleteTime === 0 && !getCheckHideDossierTaskStatus(row)"><span i18n>Ngày hẹn trả: </span>
                                <ng-container *ngIf="row?.approvalData?.extendTime != null && row?.approvalData?.extendTime != '' && row?.approvalData?.extendTime != undefined; then extendTime else appointment;"></ng-container>
                                <ng-template #extendTime>
                                    <span>{{row?.approvalData?.extendTime | date : 'dd/MM/yyyy HH:mm:ss'}}</span>
                                </ng-template>
                                <ng-template #appointment>
                                    <span>{{row.appointmentDate | date : 'dd/MM/yyyy HH:mm:ss'}}</span>
                                </ng-template>
                            </li>

                            <li *ngIf="row.applicant?.data?.noiDung != '' && row.applicant?.data?.noiDung">Nội dung trích yếu: {{row.applicant?.data?.noiDung}} </li>
                        </ul>
                    </mat-cell>
                </ng-container>

                <ng-container matColumnDef="applicantName">
                    <mat-header-cell *matHeaderCellDef i18n>Người nộp</mat-header-cell>
                    <mat-cell [ngStyle]="{'color': row.due[0]?.timesheet?.color || 'black'}" *matCellDef="let row" i18n-data-label data-label="Người nộp">
                        <ul>
                            <li [ngStyle]="{'font-weight': isBoldInfor ? '500': 'unset' }" *ngIf="row.applicant?.data?.fullname != '' && row.applicant?.data?.fullname">Người nộp: {{row.applicant?.data?.fullname}} </li>
                            <li *ngIf="row.applicant?.data?.organization != '' && row.applicant?.data?.organization && showOrganizationInformation"><span class="fw-bold">Cơ quan/doanh nghiệp: {{row.applicant?.data?.organization}}</span></li>
                            <li *ngIf="row.applicant?.data?.address1 != '' && row.applicant?.data?.address1 && showOrganizationInformation">Địa chỉ chi tiết: {{row.applicant?.data?.address1}} </li>
                            <li *ngIf="row.applicant?.data?.village1?.label != '' && row.applicant?.data?.village1?.label && showOrganizationInformation">Phường/xã : {{row.applicant?.data?.village1?.label}} </li>
                            <li *ngIf="row.applicant?.data?.district1?.label != '' && row.applicant?.data?.district1?.label && showOrganizationInformation">Quận/huyện: {{row.applicant?.data?.district1?.label}} </li>
                            <li *ngIf="row.applicant?.data?.province1?.label != '' && row.applicant?.data?.province1?.label && showOrganizationInformation">Tỉnh/thành phố: {{row.applicant?.data?.province1?.label}} </li>
                            <li *ngIf="row.applicant?.data?.address != '' && row.applicant?.data?.address && isQNM">Địa chỉ chi tiết: {{row.applicant?.data?.address}} </li>
                            <li *ngIf="row.applicant?.data?.village?.label != '' && row.applicant?.data?.village?.label && isQNM">Phường/xã : {{row.applicant?.data?.village?.label}} </li>
                            <li *ngIf="row.applicant?.data?.district?.label != '' && row.applicant?.data?.district?.label && isQNM">Quận/huyện: {{row.applicant?.data?.district?.label}} </li>
                            <li *ngIf="row.applicant?.data?.province?.label != '' && row.applicant?.data?.province?.label && isQNM">Tỉnh/thành phố: {{row.applicant?.data?.province?.label}} </li>
                            <li *ngIf="row.eForm?.data?.CapPhepthiCong != '' && row.eForm?.data?.CapPhepthiCong && showConstructionPermit">Cấp phép thi công: {{row.eForm?.data?.CapPhepthiCong}} </li>
                            <div *ngIf="isShowConstructionUnit && !!row.eForm && !!row.eForm.data">
                                <li *ngIf="!!row.eForm.data?.CapPhepthiCong">Đơn vị thi công: {{row.eForm.data?.CapPhepthiCong}}</li>
                                <li *ngIf="!!row.eForm.data?.DiaChiCT">Địa chỉ công trình: {{row.eForm.data?.DiaChiCT}}</li>
                                <li *ngIf="!!row.eForm.data?.TenPT  ">Phương tiện: {{row.eForm.data?.TenPT}}</li>
                                <li *ngIf="!!row.eForm.data?.BienSoXe  ">Biển số: {{row.eForm.data?.BienSoXe}}</li>

                                <!-- <li *ngIf="row.quanTC.length>0">
                                    Quận/Huyện thi công :
                                    <span *ngFor="let quan of row.quanTC">
                                        {{quan +', '}}
                                    </span>
                                    <br>
                                    <span *ngIf="!!row.eForm?.data?.ViTriCT">Vị trí: {{row.eForm?.data?.ViTriCT}} <br> </span>
                                    <span *ngIf="!!row.eForm.data?.TenPT  ">Phương tiện: {{row.eForm.data?.TenPT}}  <br> </span>
                                    <span *ngIf="!!row.eForm.data?.BienSoXe  ">Biển số: {{row.eForm.data?.BienSoXe}}</span>

                                </li> -->
                            </div>  
                            <div *ngIf="onOffAbstractConstructionUnit">
                                <li *ngIf="!!row.eForm.data?.trichyeu" style="font-weight: 500">Trích yếu: 
                                    <span style="font-weight: normal">{{row.eForm.data?.trichyeu}}</span>
                                </li>
                            </div>
                            <li *ngIf="row.applicant?.data?.agency != '' && row.applicant?.data?.agency && isEnableLableOrganizationInformation"><b>Cơ quan/doanh nghiệp:</b> {{row.applicant?.data?.agency}}<span *ngIf="row.applicant?.data?.KCN?.label"> - {{row.applicant?.data?.KCN?.label}}</span> </li>
                            <li *ngIf="row.applicant?.data?.tenDoanhNghiep != '' && row.applicant?.data?.tenDoanhNghiep && isEnableLableOrganizationInformation"><b>Cơ quan/doanh nghiệp:</b> {{row.applicant?.data?.tenDoanhNghiep}}<span *ngIf="row.applicant?.data?.KCN?.label"> - {{row.applicant?.data?.KCN?.label}}</span> </li>
                        </ul>
                    </mat-cell>
                </ng-container>

                <ng-container matColumnDef="profileOwner">
                  <mat-header-cell *matHeaderCellDef >Chủ hồ sơ</mat-header-cell>
                  <mat-cell  [ngStyle]="{'color': row?.due[0]?.timesheet?.color || 'black'}" *matCellDef="let row" i18n-data-label data-label="Chủ hồ sơ">
                    <span *ngIf="!isUserRQ; else elseBlockKGG">
                        {{row?.applicant?.data?.ownerFullname}}
                    </span>
                    <ng-template #elseBlockKGG>
                        {{row?.applicant?.data?.ownerFullname ? row.applicant?.data?.ownerFullname : row?.applicant?.data?.fullname}}
                    </ng-template>
                  </mat-cell>
                </ng-container>

                <ng-container matColumnDef="pay" *ngIf="isAllowFilterPaymentStatusListAgency">
                    <mat-header-cell *matHeaderCellDef i18n>Trạng thái thanh toán</mat-header-cell>
                    <mat-cell *matCellDef="let row" i18n-data-label data-label="Trạng thái thanh toán">
                       <span>{{row?.status}}</span>
                    </mat-cell>
                </ng-container>

                <ng-container matColumnDef="pay" *ngIf="isAGG" >
                    <mat-header-cell *matHeaderCellDef i18n>Trạng thái thanh toán</mat-header-cell>
                    <mat-cell *matCellDef="let row" i18n-data-label data-label="Trạng thái thanh toán">
                       <span>{{row.payAGG}}</span>
                    </mat-cell>
                </ng-container>

                <ng-container matColumnDef="fee">
                    <mat-header-cell *matHeaderCellDef i18n>Lệ phí</mat-header-cell>
                    <mat-cell *matCellDef="let row" i18n-data-label data-label="Lệ phí">
                       <span>{{row?.fee}}</span>
                    </mat-cell>
                </ng-container>

                <ng-container matColumnDef="agency">
                    <mat-header-cell *matHeaderCellDef i18n>Cơ quan thực hiện</mat-header-cell>
                    <div *ngIf="showColUnitQTI != 1">
                    <div *ngIf="isShowUserProcessingKTM == false ">
                        <mat-cell [ngStyle]="{'color': row?.due[0]?.timesheet?.color || 'black'}" *matCellDef="let row" i18n-data-label data-label="Cơ quan thực hiện"> <span [ngClass]="{'file_name element-crop-text' : showNote == true }" matTooltip="{{check(row)}}">{{check(row)}}</span></mat-cell>
                    </div>
                    <div *ngIf="isShowUserProcessingKTM == true ">
                        <mat-cell [ngStyle]="{'color': row?.due[0]?.timesheet?.color || 'black'}" *matCellDef="let row" i18n-data-label data-label="Cơ quan thực hiện">
                            <ul>
                                <li><span i18n>Cơ quan</span>:&nbsp;<span>{{check(row)}}</span></li>
                                <li *ngIf="row?.userProcessing !== null && row?.userProcessing !== ''"><span >Cán bộ</span>:&nbsp;
                                    <span [ngClass]="{'file_name element-crop-text' : showNote == true }" matTooltip="{{row?.userProcessing}}">{{row.userProcessing}}</span></li>
                            </ul>
                        </mat-cell>
                    </div>
                   </div>
                    <div *ngIf="showColUnitQTI == 1 ">
                        <mat-cell [ngStyle]="{'color': row?.due[0]?.timesheet?.color || 'black'}" *matCellDef="let row" i18n-data-label data-label="Cơ quan thực hiện">
                            <ul>
                                <li *ngIf="row?.agencyNameQTI !== null && row?.agencyNameQTI !== ''"><span>{{row?.agencyNameQTI}}</span></li>
                                <li *ngIf="row?.userProcessing !== null && row?.userProcessing !== ''"><span>Người xử lý</span>:&nbsp;<span [ngClass]="{'file_name element-crop-text' : showNote == true }" matTooltip="{{row?.userProcessing}}">{{row.userProcessing}}</span></li>
                            </ul>
                        </mat-cell>
                    </div>
                </ng-container>
                <!-- <ng-container *ngIf="showNote" matColumnDef="note">
                    <mat-header-cell *matHeaderCellDef i18n="@@note">Ghi chú</mat-header-cell>
                    <mat-cell  *matCellDef="let row">
                        <div style="display: flex;width: 100%;flex-wrap: wrap;">
                            <ng-container *ngFor="let item of row?.extendHCM?.note" >
                                <span class="file_name element-crop-text"   matTooltip="{{item?.describe}}" >{{item?.describe}}</span>
                            </ng-container>
                        </div>
                    </mat-cell>
                </ng-container> -->
                <ng-container *ngIf="showNote" matColumnDef="note">
                    <mat-header-cell *matHeaderCellDef i18n="@@note">Ghi chú</mat-header-cell>
                    <mat-cell *matCellDef="let row">
                        <div *ngIf="checkPermistionNote(row?.currentTask); else ReadOnly" style="display: block; width: 100%; flex-wrap: wrap;">
                            <ng-container *ngFor="let item of row?.extendHCM?.note; let i = index">
                                <!-- Hiển thị danh sách ghi chú -->
                                <div *ngIf="!item.editing" (dblclick)="editItem(row, i)">
                                    <li>
                                        <span matTooltip="{{item?.describe}}">
                                          {{ item?.describe?.slice(0, 30) }}
                                          <span *ngIf="item?.describe?.length > 30">...</span>
                                        </span>
                                    </li>
                                </div>
                                <!-- Hiển thị input chỉnh sửa ghi chú và icon cập nhật, xóa -->
                                <div *ngIf="item.editing">
                                    <input [(ngModel)]="item.describe" (blur)="saveItem(row, i)" (keyup.enter)="saveItem(row, i)" />
                                    <mat-icon (click)="saveItem(row, i)">done</mat-icon>
                                    <mat-icon (click)="deleteItem(item, row)">close</mat-icon>
                                </div>
                            </ng-container>
                            <!-- Nút thêm ghi chú -->
                            <div *ngIf="showAddButton">
                                <mat-icon (click)="addItem(row)">add</mat-icon>
                            </div>
                        </div>
                        <ng-template #ReadOnly>
                            <div style="display: block;width: 100%;flex-wrap: wrap;">
                                <ng-container *ngFor="let item of row?.extendHCM?.note" >
                                    <li>
                                        <span matTooltip="{{item?.describe}}">
                                          {{ item?.describe?.slice(0, 30) }}
                                          <span *ngIf="item?.describe?.length > 30">...</span>
                                        </span>
                                    </li>
                                </ng-container>
                            </div>
                        </ng-template>
                    </mat-cell>
                </ng-container>

                 <!--HPG-->
                 <ng-container matColumnDef="agencyProcessing" >
                    <mat-header-cell *matHeaderCellDef >Bộ phận/CB đang xử lý</mat-header-cell>
                    <mat-cell  *matCellDef="let row" i18n-data-label data-label="Bộ phận/CB đang xử lý">
                        <span *ngIf="!!row.currentTask && !!row.currentTask[0]" >
                            {{row.currentTask[0]?.assignee?.fullname}}
                        </span>
                    </mat-cell>
                </ng-container>
                <!--HPG-->

                <ng-container matColumnDef="status">
                    <mat-header-cell *matHeaderCellDef i18n>Trạng thái</mat-header-cell>
                    <mat-cell *matCellDef="let row" i18n-data-label data-label="Trạng thái" style="display: inline-block;">
                        <!-- <span *ngIf="row.dossierStatus != undefined && (row.dossierStatus?.id == 5 || row.dossierStatus?.id == 0 || row.dossierStatus?.id == 6)" [style.color]="getStatusColor(row.dossierStatus?.id)">
                            {{row.dossierStatus?.name}}
                        </span>
                        <ng-container *ngIf="row.currentTask != undefined">
                            <span style="color: rgb(243, 156, 18);" *ngIf="row.currentTask[0]?.bpmProcessDefinitionTask?.name != undefined && row.dossierStatus?.id !== 5 && row.dossierStatus?.id !== 0">{{row.currentTask[0].bpmProcessDefinitionTask.name['name']}}</span>
                        </ng-container> -->
                        <span [style.color]="row.due[0]?.timesheet?.color ? row.due[0]?.timesheet?.color : getStatusColor(row.dossierStatus?.id)">{{row.dossierStatus?.name}}
                            <span *ngIf="env?.vnpost?.config === 1 || env?.vnpost?.config === '1'" style="color: black;">
                                <br> {{row.vnpostStatusReturn}} <br> 
                            </span>
                        </span>
                        <span *ngIf="this.vpcConfirmOriginalDossier == 1" style="color: red">
                          <br>
                          <span *ngIf="row.extendDossierOriginalVPC != null">
                            <span *ngIf="row.extendDossierOriginalVPC?.updateStatus?.id == 1">
                              (Đã nhận hồ sơ gốc)
                            </span>
                            <span *ngIf="row.extendDossierOriginalVPC?.updateStatus?.id == 2">
                              (Đã trả hồ sơ gốc)
                            </span>
                          </span>
                          <span *ngIf="row.extendDossierOriginalVPC == null">
                            (Chưa nhận hồ sơ gốc)
                          </span>
                        </span>
                        <span *ngIf="this.vpcAuthorizeReceiveResult == 1" style="color: red">
                          <br>
                          <span *ngIf="row.extendDossierAuthorizeResultVPC != null">
                            <span *ngIf="row.extendDossierAuthorizeResultVPC?.updateStatus?.id != 0">
                              (Đã ủy quyền nhận KQ)
                            </span>
                          </span>
                        </span>
                        <span *ngIf="this.listProcedureCodeHTTP.includes(row.procedure?.code) && row?.extendQNI?.httpStatus?.statusMessage && !row?.nationCode" style="color: black;">Trạng thái hộ tịch cổng tỉnh: {{row?.extendQNI?.httpStatus?.statusMessage}}<br></span>
                        <span *ngIf="this.showStatusCTDT && row.authenticationStatusCurrent" style="color: black;"><i>{{setAuthStatusName(row.authenticationStatusCurrent?.id)}}</i><br></span>
                        <span *ngIf="showStatusVnpost == 1 && row.vnpostStatus" style="color: black;">Trạng thái Vnpost: {{row.vnpostStatus}}<br></span>
                        <span *ngIf="viettelPostEnable && row?.dossierReceivingKind?.id == viettelPostReceiveResultsByAddress && row?.viettelPost?.vpSend && row?.viettelPost?.statusViettelPostS">ViettelPost - nộp: {{row?.viettelPost?.statusViettelPostS?.name}} <br></span>
                        <span *ngIf="viettelPostEnable && row?.dossierReceivingKind?.id == viettelPostReceiveResultsByAddress && row?.viettelPost?.vpReceive && row?.viettelPost?.statusViettelPostR">ViettelPost - nhận kết quả: {{row?.viettelPost?.statusViettelPostR?.name}} <br></span>
                        <span *ngIf="this.showAppointmentNoLLTP == 1 && row.extendHCM?.lltpStatus" style="color: black;">Số phiếu LLTP: {{row.extendHCM?.lltpStatus?.appointmentNo}}<br></span>
                        <span *ngIf="this.showDocumentNoLLTP == 1 && row.extendHCM?.lltpStatus" style="color: black;">Số phiếu LLTP: {{row.extendHCM?.lltpStatus?.documentNo}}<br></span>
                        <span *ngIf="this.showStatusLLTP == 1 && row.extendHCM?.lltpStatus" style="color: black;">Trạng thái lý lịch: {{row.extendHCM?.lltpStatus?.statusMessage}}<br></span>
                        <span *ngIf="this.isDVCLTEnable && row?.httpstatus?.statusMessage && row?.nationCode" style="color: black;">Trạng thái hộ tịch DVCLT: {{row?.httpstatus?.statusMessage}}<br></span>
                        <span *ngIf="this.showStatusHTTPLTKH && row.extendHCM?.httpLTKHStatus" style="color: black;">Trạng thái hộ tịch: {{row.extendHCM?.httpLTKHStatus?.statusLTKHMessage}}<br></span>
                        <span *ngIf="this.allowShowStatusSyncLGSP && row.extendHCM?.isSyncLGSPHCM" style="color: black;">Trạng thái CN: {{row.extendHCM?.isSyncLGSPHCM ? "Đồng bộ hồ sơ thành công": ""}}<br></span>
                        <span *ngIf="showRemindSyncLGSPHCM && isSyncLGSPHCM " style="color: black;">Trạng thái CN: Đồng bộ hồ sơ không thành công"<br></span>
                        <span *ngIf = "row.extendQNI?.isVbdlis == true"> Đồng bộ VBDLIS </span>
                        <span style="color: red" *ngIf = "row.extendQNI?.isVbdlis !== undefined && row.extendQNI?.isVbdlis == false"> Đồng bộ VBDLIS thất bại </span>
                        <span style="color: red" *ngIf = "row.extendQNI?.isTBNOHTTTL !== undefined && row.extendQNI?.isTBNOHTTTL == false"> Đồng bộ tích hợp TBNOHTTTL thất bại </span>
                        <span *ngIf = "row.extendQNI?.isTBNOHTTTL == true"> Đồng bộ tích hợp TBNOHTTTL </span><br>
                        <span style="color: red" *ngIf = "row.extendQNI?.isVbdlis !== undefined && row.extendQNI?.isVbdlis == false"> Đồng bộ VBDLIS thất bại </span>
                        <span *ngIf = "row.extendQNI?.isIlis == true"> Đồng bộ ILIS </span>
                        <span style="color: red" *ngIf = "row.extendQNI?.isIlis !== undefined && row.extendQNI?.isIlis == false"> Đồng bộ ILIS thất bại </span>
                        <span *ngIf = "row.extendCMU?.isBhtn == true"> Đồng bộ BHTN </span>
                        <span style="color: red" *ngIf = "row.extendCMU?.isBhtn !== undefined && row.extendCMU?.isBhtn == false"> Đồng bộ BHTN thất bại </span>
                        <span *ngIf="checkShowFilterRegisterApplicationVNeID && row?.receiveDossierVneid != null" style="color: red">Hồ sơ đăng ký trên ứng dụng VNeID</span>
                        <span *ngIf="this.enableSendDossierViaSoftwareQLVB && row.extendHCM?.statusVPUB !== undefined && row.extendHCM?.statusVPUB !== null" style="color: black;">Trạng thái CN: {{row.extendHCM?.statusVPUB ? 'Gửi văn bản thành công' : 'Gửi văn bản thất bại'}}<br></span>
                    </mat-cell>
                </ng-container>

                <ng-container matColumnDef="action">
                    <mat-header-cell *matHeaderCellDef i18n>Thao tác</mat-header-cell>
                    <mat-cell *matCellDef="let row" i18n-data-label data-label="Thao tác">
                        <button (click)="checkPermissionShowNote(row?.currentTask)" mat-icon-button [matMenuTriggerFor]="actionMenu">
                            <mat-icon>more_horiz</mat-icon>
                        </button>
                        <mat-menu #actionMenu="matMenu" xPosition="before">
                            <ng-container *ngIf="enableShowPaymentOnlineQni">
                                <button mat-menu-item class="menuAction" (click)="paymentOnlineOfficer(row)">
                                    <mat-icon>monetization_on</mat-icon>
                                    <span >Thanh toán trực tuyến</span>
                                </button>
                            </ng-container>                            
                            <ng-container *ngIf = "row.extendQNI?.isVbdlis == undefined && row.extendQNI?.isIlis == undefined && row.extendCMU?.isBhtn == undefined">
                                <ng-container *ngIf="hiddenButtonIssueReceiptsDossierOnline==false ||  row?.applyMethod?.id==1"><!-- ap dụng cho nộp trưc tuyến --> 
                                    <ng-container *ngIf="allowCreateReceiptWithAnyStatus == true; else elseNotDone">
                                        <button mat-menu-item class="menuAction" (click)="receiptCreation(row.id, row.code, row.procedure?.id, row.task, row.applyMethod?.id)" *ngIf="allowShowButtonReleaseViewReceipt == true || isShowDossierReceiptCreationStatus()">
                                            <mat-icon>receipt</mat-icon>
                                            <span i18>Phát hành biên lai</span> 
                                        </button>
                                    </ng-container>
                                    <ng-template #elseNotDone>
                                        <ng-container *ngIf="row | checkValidConfigButton">
                                            <button mat-menu-item class="menuAction" (click)="receiptCreation(row.id, row.code, row.procedure?.id, row.task, row.applyMethod?.id)" *ngIf="allowShowButtonReleaseViewReceipt == true || isShowDossierReceiptCreationStatus()">
                                                <mat-icon>receipt</mat-icon>
                                                <span i18>Phát hành biên lai</span>
                                            </button>
                                        </ng-container>
                                    </ng-template>
                                </ng-container>
                                <ng-container *ngIf="hiddenButtonIssueReceiptsDossierOnline==true &&  row?.applyMethod?.id==0">                                   
                                        <ng-container *ngIf="(row | checkValidConfigButton) && row?.status!='Chưa thanh toán' && row?.status!='Không tính phí' ">
                                            <button mat-menu-item class="menuAction" (click)="receiptCreation(row.id, row.code, row.procedure?.id, row.task, row.applyMethod?.id)" *ngIf="allowShowButtonReleaseViewReceipt == true || isShowDossierReceiptCreationStatus()">
                                                <mat-icon>receipt</mat-icon>
                                                <span i18>Phát hành biên lai</span>
                                            </button>
                                        </ng-container>
                                </ng-container>
                                <button mat-menu-item class="menuAction" (click)="receiptList(row.id, row.procedure?.id)" *ngIf="allowShowButtonReleaseViewReceipt == true">
                                    <mat-icon>preview</mat-icon>
                                    <span i18n>Xem biên lai</span>
                                </button>
                                <button mat-menu-item class="menuAction" (click)="checkPaymentByCode(row.code)" *ngIf="showReCheckPayment == true">
                                    <mat-icon>preview</mat-icon>
                                    <span>Kiểm tra lại thanh toán payment</span>
                                </button>
                                <button mat-flat-button class="btnSecondary" (click)=" $event.stopPropagation(); $event.preventDefault(); getListConfigTemplateDetail(row.agency.id, row.procedure?.id)" [matMenuTriggerFor]="printMenu" *ngIf="allowShowButtonPrint == true">
                                    <mat-icon>print</mat-icon>
                                    <span i18n>In phiếu</span>
                                    <mat-icon>keyboard_arrow_down</mat-icon>
                                </button>
                                <mat-menu #printMenu="matMenu">
                                    <button mat-menu-item *ngFor="let bill of listConfigTemplateDetail" (click)=" createPrintBill(bill.file.path, row.id, bill.id)"  >{{bill.name}}</button>
                                </mat-menu>
                                <button mat-menu-item class="menuAction" *ngIf="row.task != undefined && row.task.length > 0" (click)="viewProcess(row.id, row.code)">
                                    <mat-icon>insights</mat-icon><span i18n>Xem quy trình</span>
                                </button>
                                <!-- <button mat-menu-item class="menuAction" (click)="viewSendAnApologyLetter(row.id, row.code)" *ngIf="enableFunctionToSendApologyLetter">
                                    <mat-icon>email</mat-icon><span>Gửi thư xin lỗi</span>
                                </button> -->
                                <button *ngIf ="!!row.task && row.task.length > 0 && row.task[0].bpmProcessDefinitionTask?.dynamicVariable?.tbnohtttlQni == true && row.extendQNI?.isTBNOHTTTL == undefined" mat-menu-item (click)="sendInfoDossierTBNOHTTTLQNI(row.id)">
                                    <mat-icon>memory</mat-icon>
                                    <span>Tiếp nhận TBNOHTTTL QNI</span>
                                </button>
                                <button *ngIf ="row.extendQNI?.isTBNOHTTTL == true" mat-menu-item (click)="sendInfoProcessDossierTBNOHTTTLQNI(row.code)">
                                    <mat-icon>memory</mat-icon>
                                    <span>Đồng bộ trạng thái TBNOHTTTL QNI</span>
                                </button>
                                <ng-container *ngIf="enableWithdrawDossierBtn">
                                    <button mat-menu-item class="menuAction" *ngIf="(row.dossierStatus.id !== 5 && row.dossierStatus.id !== 4 && row.dossierStatus.id !== 12) && receivingPermission" (click)="withdrawDialogs(row.id, row.code)">
                                        <mat-icon>reply</mat-icon><span *ngIf="!isUserRQ;else isUserKGGTemplate">Yêu cầu rút hồ sơ</span>
                                        <ng-template #isUserKGGTemplate>
                                            <span>Rút hồ sơ theo yêu cầu</span>
                                        </ng-template>
                                    </button>
                                </ng-container>
                                <button mat-menu-item class="menuAction" (click)="dossierDetail($event, row.id, row.procedure?.id, row.task)">
                                    <mat-icon>info</mat-icon><span i18n>Chi tiết hồ sơ</span>
                                </button>
                                <button mat-menu-item class="menuAction" (click)="reassign(row.id, row.code, row?.currentTask, row.procedure?.id)" *ngIf="enableReassignDossier && !isAdmin && row.canReassign && [2, 4].includes(row.dossierStatus?.id) && !!row.previousTask && row.previousTask.length !== 0">
                                    <mat-icon>edit</mat-icon><span>Điều chỉnh lại người xử lý</span>
                                </button>
                                <button mat-menu-item class="menuAction" *ngIf="(isDienBien === true && (checkProvineAdmin === true || env?.hideDeleteButton !== true || (hasDossierDeletePermission && row.dossierStatus?.id === 2 && row.currentTask[0]?.isFirst === 1 && row.applyMethod?.id !== 0))) && oneGateDeleteDossier === '1' && !hideDeleteButtonDossier" (click)="deleteDialog(row.id, row.code)">
                                    <mat-icon>delete_outline</mat-icon><span i18n>Xóa</span>
                                </button>
                                <button mat-menu-item class="menuAction" *ngIf="row?.enableDisplayOfSpecializedProcessing" (click)="viewSpecializedProcess(row.code)">
                                    <mat-icon>insights</mat-icon><span i18n="@@specializedProcessing">Quy trình xử lý chuyên ngành</span>
                                </button>
                                <button mat-menu-item class="menuAction" *ngIf="this.enableSendDossierViaSoftwareQLVB && row.extendHCM?.statusVPUB !== undefined && row.extendHCM?.statusVPUB !== null" (click)="viewSpecializedProcessVPUB(row.id)">
                                    <mat-icon>insights</mat-icon><span>Quy trình phần mềm QLVB</span>
                                </button>
                                <button mat-menu-item class="menuAction" *ngIf="isShowBtnSyncDossier"  (click)="syncStatusDossier(row)">
                                    <mat-icon>sync</mat-icon><span >Đồng bộ lại trạng thái hồ sơ</span>
                                </button>
                                <ng-container *ngIf="isAdmin">
                                    <button mat-menu-item class="menuAction" *ngIf="row.dossierStatus?.id != 0 || (row.dossierStatus?.id == 0 && !!row.task && row.task.length != 0)" (click)="returnAccept(row.id, row.code)">
                                        <mat-icon>settings_backup_restore</mat-icon><span>Trả về chờ tiếp nhận</span>
                                    </button>
                                    <button mat-menu-item class="menuAction" *ngIf="(row.dossierStatus?.id != 0 || (row.dossierStatus?.id == 0 && !!row.task && row.task.length != 0)) && enableForceEndProcess" (click)="forceEndProcess(row.id, row.code)">
                                        <mat-icon>check</mat-icon><span>Kết thúc hồ sơ</span>
                                    </button>
                                    <button mat-menu-item class="menuAction" (click)="reassign(row.id, row.code, row?.currentTask, row.procedure?.id)" *ngIf="enableReassignDossier && [2, 4].includes(row.dossierStatus?.id) && !!row.previousTask && row.previousTask.length !== 0">
                                        <mat-icon>edit</mat-icon><span>Điều chỉnh lại người xử lý</span>
                                    </button>
                                    <button mat-menu-item class="menuAction" (click)="checkLogSync(row.id, row.code)"  *ngIf="enableCheckLogSyncDossier">
                                        <mat-icon>check</mat-icon><span>Kiểm tra đồng bộ</span>
                                    </button>
                                    <button mat-menu-item class="menuAction" *ngIf="isDienBien !== true && !hideDeleteButtonDossier" (click)="deleteDialog(row.id, row.code)">
                                        <mat-icon>delete_outline</mat-icon><span i18n>Xóa</span>
                                    </button>
                                    <button mat-menu-item class="menuAction" *ngIf="row.dossierStatus?.id != 1 && row.requireAdditional" (click)="additionalRequirement(row.id, row.code)">
                                        <mat-icon>reply</mat-icon><span i18n>Yêu cầu bổ sung</span>
                                    </button>
                                    <button mat-menu-item class="menuAction" *ngIf="row.dossierStatus?.id == 2 || row.dossierStatus?.id == 4" (click)="suspenDialogs(row.id, row.code)">
                                        <mat-icon>pause_circle_outline</mat-icon><span i18n>Tạm dừng</span>
                                    </button>
                                    <button mat-menu-item class="menuAction" *ngIf="row.dossierStatus?.id == 3" (click)="resumeDialog(row.id, row.code)">
                                        <mat-icon>autorenew</mat-icon><span i18n>Tiếp tục xử lý</span>
                                    </button>
                                    <button mat-menu-item class="menuAction" *ngIf="row.dossierStatus?.id == 0" (click)="refuseDialog(row.id, row.code)">
                                        <mat-icon>block</mat-icon><span i18n>Từ chối</span>
                                    </button>
                                    <button mat-menu-item class="menuAction" *ngIf="row.due.length > 0 && row.due[0].timesheet.isOverDue == true" (click)="addApologyText(row.id)">
                                        <mat-icon>assignment_returned</mat-icon><span i18n>Tải xuống mẫu văn bản xin lỗi</span>
                                    </button>
                                    <button mat-menu-item class="menuAction" *ngIf="row.due.length > 0 && row.due[0].timesheet.isOverDue == true" (click)="signApologyText(row.id)">
                                        <mat-icon>note_add</mat-icon><span i18n>Thêm văn bản xin lỗi</span>
                                    </button>
                                    <button mat-menu-item class="menuAction" (click)="downloadAllFile(row.id)">
                                        <mat-icon>cloud_download</mat-icon>
                                        <span>Tải văn bản của hồ sơ</span>
                                    </button>
                                    <button class="menuAction" mat-menu-item (click)="showVNPostInfo(row)" *ngIf="row?.vnpostStatus5343">
                                        <mat-icon>history</mat-icon>
                                        <span>Thông tin đơn hàng VNPOST</span>
                                    </button>
                                    <button mat-menu-item class="menuAction" (click)="UpdatePaymentMethod(row.id, row.paymentMethod)" *ngIf="oneGateUpdatePaymentMethod">
                                        <mat-icon>edit</mat-icon>
                                        <span>Cập nhật hình thức thanh toán</span>
                                    </button>
                                    <ng-container *ngIf="(row?.dossierStatus?.id !== 4 || row?.dossierStatus?.id !== 5) && !isDienBien
                                                  && (!noneWithdrawDossierConfig || (row?.currentTask[0]?.bpmProcessDefinitionTask?.variable.canEvictDossier == 1 && noneWithdrawDossierConfig)) && this.vpcwithdrawprocess == 0">
                                        <ng-container *ngIf="this.qbhwithdrawprocess == true && this.checkDrawQBH == true; else normal">
                                        <button mat-menu-item class="menuAction" *ngIf="!hideRequestToWithdraw" (click)="withdrawQBHDialogs(row.id, row.code)" >
                                            <mat-icon>reply</mat-icon><span *ngIf="!isUserRQ;else isUserKGGTemplate">Yêu cầu rút hồ sơ</span>
                                            <ng-template #isUserKGGTemplate>
                                                <span>Rút hồ sơ theo yêu cầu</span>
                                            </ng-template>
                                        </button>
                                        </ng-container>
                                        <ng-template #normal>
                                            <button mat-menu-item class="menuAction" *ngIf="!hideRequestToWithdraw" (click)="withdrawDialogs(row.id, row.code)" >
                                                <mat-icon>reply</mat-icon><span *ngIf="!isUserRQ;else isUserKGGTemplate">Yêu cầu rút hồ sơ</span>
                                                <ng-template #isUserKGGTemplate>
                                                    <span>Rút hồ sơ theo yêu cầu</span>
                                                </ng-template>
                                            </button>
                                        </ng-template>
                                    </ng-container>
                                    <ng-container *ngIf="(row?.dossierStatus?.id !== 4 || row?.dossierStatus?.id !== 5) && this.vpcwithdrawprocess == 1">
                                        <ng-container>
                                            <button mat-menu-item class="menuAction" *ngIf="!hideRequestToWithdraw" (click)="withdrawVPCDialogs(row.id, row.code)" >
                                            <mat-icon>reply</mat-icon><span>Yêu cầu rút hồ sơ</span>
                                            </button>
                                        </ng-container>
                                    </ng-container>
                                </ng-container>
                                <ng-container *ngIf="![4,5,6].includes(row?.dossierStatus?.id) && isDienBien && !row?.isDVCLT && isOneGateOfficer
                                              && (!noneWithdrawDossierConfig || (row?.currentTask?.length > 0 && row?.currentTask[0]?.bpmProcessDefinitionTask?.variable.canEvictDossier == 1 && noneWithdrawDossierConfig))">
                                  <ng-container *ngIf="[0,1].includes(row?.dossierStatus?.id ); then thenBlock else elseBlock"></ng-container>
                                  <ng-template #thenBlock>
                                    <button mat-menu-item class="menuAction" *ngIf="!hideRequestToWithdraw" (click)="withdrawDialogs(row.id, row.code,true,1)" >
                                      <mat-icon>reply</mat-icon><span *ngIf="!isUserRQ;else isUserKGGTemplate">Yêu cầu rút hồ sơ</span>
                                      <ng-template #isUserKGGTemplate>
                                        <span>Rút hồ sơ theo yêu cầu</span>
                                    </ng-template>
                                    </button>
                                  </ng-template>
                                  <ng-template #elseBlock>
                                    <ng-container *ngIf="this.qbhwithdrawprocess == true && this.checkDrawQBH == true; else normal2">
                                    <button mat-menu-item class="menuAction" *ngIf="!hideRequestToWithdraw" (click)="withdrawQBHDialogs(row.id, row.code,true)" >
                                      <mat-icon>reply</mat-icon><span *ngIf="!isUserRQ;else isUserKGGTemplate">Yêu cầu rút hồ sơ</span>
                                      <ng-template #isUserKGGTemplate>
                                        <span>Rút hồ sơ theo yêu cầu</span>
                                      </ng-template>
                                    </button>
                                    </ng-container>
                                    <ng-container *ngIf="this.vpcwithdrawprocess == 1 && this.checkDrawVPC == true; else normal2">
                                        <button mat-menu-item class="menuAction" *ngIf="!hideRequestToWithdraw" (click)="withdrawVPCDialogs(row.id, row.code)" >
                                          <mat-icon>reply</mat-icon><span>Yêu cầu rút hồ sơ</span>
                                        </button>
                                    </ng-container>
                                    <ng-template #normal2>
                                        <button mat-menu-item class="menuAction" *ngIf="!hideRequestToWithdraw  && this.checkDrawQBH == false" (click)="withdrawDialogs(row.id, row.code,true)" >
                                            <mat-icon>reply</mat-icon><span *ngIf="!isUserRQ;else isUserKGGTemplate">Yêu cầu rút hồ sơ</span>
                                            <ng-template #isUserKGGTemplate>
                                              <span>Rút hồ sơ theo yêu cầu</span>
                                            </ng-template>
                                          </button>
                                    </ng-template>
                                  </ng-template>
                                </ng-container>
                            </ng-container>

                            <ng-container *ngIf="row.extendQNI?.isVbdlis == true || row.extendQNI?.isVbdlis == false">
                                <button mat-menu-item class="menuAction" (click)="dossierDetail($event,row.id, row.procedure?.id, row.task)">
                                    <mat-icon>info</mat-icon><span i18n>Chi tiết hồ sơ</span>
                                </button>
                                <button mat-menu-item class="menuAction" *ngIf="isAdmin" (click)="deleteDialog(row.id, row.code)">
                                    <mat-icon>delete_outline</mat-icon><span i18n>Xóa</span>
                                </button>
                                <button mat-flat-button class="btnPrimary" (click)="syncPostUpdateFinishDossierVBDLIS(row.code)" *ngIf="row?.extendQNI?.isVbdlis == true &&  ( row.dossierStatus?.id === 5 || row.dossierStatus?.id === 6 || row.dossierStatus?.id === 12)">
                                    <mat-icon>done_all</mat-icon>
                                    <span>Cập nhật trạng thái trả kết quả qua VBDLIS</span>
                                </button>
                                <button mat-menu-item class="menuAction" *ngIf="enableButtonUpdateVBDLIS" (click)="syncAdditinalRequestIntoVBDLIS(row.code)">
                                    <mat-icon>info</mat-icon><span>Cập nhật gia hạn/tạm dừng/ bổ sung qua VBDLIS</span>
                                </button>
                                <button mat-menu-item class="menuAction" (click)="viewProcessingVBDLIS(row.id, row.code)" *ngIf="enableViewProcessingVbdlisBtn">
                                    <mat-icon>assignment</mat-icon><span>Xem quá trình xử lý VBDLIS</span>
                                </button>
                                <button mat-menu-item class="menuAction" (click)="checkPaymentByCode(row.code)" *ngIf="showReCheckPayment == true">
                                    <mat-icon>preview</mat-icon>
                                    <span>Kiểm tra lại thanh toán payment</span>
                                </button>
                                 <button mat-menu-item class="menuAction" *ngIf="(row.dossierStatus.id !== 5 && row.dossierStatus.id !== 4 && row.dossierStatus.id !== 12) && receivingPermission" (click)="withdrawDialogs(row.id, row.code)">
                                        <mat-icon>reply</mat-icon><span *ngIf="!isUserRQ;else isUserKGGTemplate">Yêu cầu rút hồ sơ</span>
                                        <ng-template #isUserKGGTemplate>
                                            <span>Rút hồ sơ theo yêu cầu</span>
                                        </ng-template>
                                </button>
                            </ng-container>
                            <ng-container *ngIf="row.extendQNI?.isIlis == true || row.extendQNI?.isIlis == false">
                                <button mat-menu-item class="menuAction" (click)="checkPaymentByCode(row.code)" *ngIf="showReCheckPayment == true">
                                    <mat-icon>preview</mat-icon>
                                    <span>Kiểm tra lại thanh toán payment</span>
                                </button>
                                <button mat-menu-item class="menuAction" *ngIf="row.task != undefined && row.task.length > 0" (click)="viewProcess(row.id, row.code)">
                                    <mat-icon>insights</mat-icon><span i18n>Xem quy trình</span>
                                </button>
                                <button mat-menu-item class="menuAction" (click)="dossierDetail($event,row.id, row.procedure?.id, row.task)">
                                    <mat-icon>info</mat-icon><span i18n>Chi tiết hồ sơ</span>
                                </button>
                                <button mat-menu-item class="menuAction" *ngIf="isAdmin" (click)="deleteDialog(row.id, row.code)">
                                    <mat-icon>delete_outline</mat-icon><span i18n>Xóa</span>
                                </button>
                                <button mat-menu-item class="menuAction" (click)="reassign(row.id, row.code, row?.currentTask, row.procedure?.id)" *ngIf="isAdmin && enableReassignDossier && [2, 4].includes(row.dossierStatus?.id) && !!row.previousTask && row.previousTask.length !== 0">
                                    <mat-icon>edit</mat-icon><span>Điều chỉnh lại người xử lý</span>
                                </button>
                                <button mat-menu-item class="menuAction" (click)="reassign(row.id, row.code, row?.currentTask, row.procedure?.id)" *ngIf="enableReassignDossier && !isAdmin && row.canReassign && [2, 4].includes(row.dossierStatus?.id) && !!row.previousTask && row.previousTask.length !== 0">
                                    <mat-icon>edit</mat-icon><span>Điều chỉnh lại người xử lý</span>
                                </button>
                                <button mat-menu-item class="menuAction" (click)="downloadAllFile(row.id)" *ngIf="isAdmin">
                                    <mat-icon>cloud_download</mat-icon>
                                    <span>Tải văn bản của hồ sơ</span>
                                </button>
                              
                            </ng-container>
                            <ng-container *ngIf="checkNotePermistionEnable">
                                <button mat-menu-item (click)="onShowNote(row)">
                                    <mat-icon>note_alt</mat-icon>
                                    <span i18n="@@note">Ghi chú</span>
                                </button>
                            </ng-container>
                            <ng-container *ngIf="row.extendCMU?.isBhtn == true || row.extendCMU?.isBhtn == false">
                                <button mat-menu-item class="menuAction" (click)="dossierDetail($event,row.id, row.procedure?.id, row.task)">
                                    <mat-icon>info</mat-icon><span i18n>Chi tiết hồ sơ</span>
                                </button>
                                <button mat-menu-item class="menuAction" *ngIf="isAdmin" (click)="deleteDialog(row.id, row.code)">
                                    <mat-icon>delete_outline</mat-icon><span i18n>Xóa</span>
                                </button>
                            </ng-container>
                            <ng-container *ngIf="row?.enableButtonReturnResult">
                                <button mat-menu-item (click)="syncDossierLGSPHCM(row)">
                                    <mat-icon>check</mat-icon>
                                    <span>Trả kết quả chuyên ngành</span>
                                </button>
                            </ng-container>
                            <button class="menuAction" mat-menu-item (click)="downloadZipFileDossier(row)" *ngIf="downloadFileDossierRar">
                                <mat-icon>cloud_download</mat-icon>
                                <span>Tải văn bản nén của hồ sơ</span>
                            </button>
                            <ng-container>
                                <button mat-menu-item class="menuAction" (click)="viewApprovalInfoCommentQni(row.id, row.code)" *ngIf="hasApprovalInfNAST">
                                    <mat-icon>assignment</mat-icon><span>Thông tin phê duyệt</span>
                                </button>
                            </ng-container>
                            <ng-container>
                                <button mat-menu-item class="menuAction" (click)="viewProcessingVBDLISOneGate(row.id, row.code)" *ngIf="enableViewDossierProcessingTaskOneGate && hasViewProcessPermission">
                                    <mat-icon>assignment</mat-icon><span>Xem quá trình xử lý Một cửa</span>
                                </button>
                            </ng-container>
                            <button mat-menu-item class="menuAction" (click)="viewVNPOSTDetail(row)" *ngIf="viewOrderDetailsVNPOST == 1 && row?.receivingPlace?.rcReceive == true">
                                <mat-icon>map</mat-icon>
                                <span>Xem chi tiết đơn hàng VNPOST</span>
                            </button>
                        </mat-menu>
                    </mat-cell>
                </ng-container>

                <mat-header-row *matHeaderRowDef="displayedColumns"></mat-header-row>

                <mat-row *matRowDef="let row; columns: displayedColumns;"></mat-row>
            </table>
            <pagination-slice id="pgnx"
                    [itemsPerPage]="size"
                    [currentPage]="page"
                    [totalItems]="countResult"
                    [pageSizeOptions]="[].concat(pgSizeOptions)"
                    [dataSource]="ELEMENTDATA"
                    (change)="changePageOrSize($event)"
                    [type]="paginationType">
            </pagination-slice>
            <div id="print-section" #pdfContent style="display: none;">
                <table style="width: 100%; border-collapse: collapse;">
                    <tr>
                      <th [ngStyle]="columnStyles[0]" style="vertical-align: middle;text-align: center;font-size: 13px;">STT</th>
                      <th [ngStyle]="columnStyles[1]" style="vertical-align: middle;text-align: center;font-size: 13px;">Số hồ sơ</th>
                      <th [ngStyle]="columnStyles[2]" style="vertical-align: middle;text-align: center;font-size: 13px;">Nội dung yêu cầu giải quyết</th>
                      <th [ngStyle]="columnStyles[3]" style="vertical-align: middle;text-align: center;font-size: 13px;">Ngày tiếp nhận /
                        <br>Ngày hẹn trả /
                        <br>Ngày kết thúc xử lý
                      </th>
                      <th [ngStyle]="columnStyles[4]" style="vertical-align: middle;text-align: center;font-size: 13px;">Chủ hồ sơ</th>
                      <th [ngStyle]="columnStyles[5]" style="vertical-align: middle;text-align: center;font-size: 13px;">Người nộp hồ sơ</th>
                      <th [ngStyle]="columnStyles[6]" style="vertical-align: middle;text-align: center;font-size: 13px;">{{ isAddColumDataExport ? 'Số điện thoại người nộp' : 'Số điện thoại '}}</th>
                      <th [ngStyle]="columnStyles[7]" style="vertical-align: middle;text-align: center;font-size: 13px;">Trạng thái hồ sơ</th>
                      <th *ngIf="isAddColumDataExport" [ngStyle]="columnStyles[8]" style="vertical-align: middle;text-align: center;font-size: 13px;">Số điện thoại chủ hồ sơ</th>
                      <th *ngIf="isAddColumDataExport" [ngStyle]="columnStyles[9]" style="vertical-align: middle;text-align: center;font-size: 13px;">Địa chỉ chủ hồ sơ</th>
                      <th *ngIf="isAddColumDataExport" [ngStyle]="columnStyles[10]" style="vertical-align: middle;text-align: center;font-size: 13px;">Địa chỉ người nộp</th>
                    </tr>
                    <tbody id='tbody'>
                    </tbody>
                  </table>
            </div>

        </div>
    </div>
</div>




</ng-template>
