import { Component, OnInit, Inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { BusinessregistrationService } from 'src/app/data/service/businessregistration/businessregistration.service';
import { KeycloakService } from 'keycloak-angular';
import { MainService } from 'src/app/data/service/main/main.service';
import { EnvService } from 'src/app/core/service/env.service';
import { DeploymentService } from 'src/app/data/service/deployment.service';

@Component({
  selector: 'app-details',
  templateUrl: './details.component.html',
  styleUrls: ['./details.component.scss']
})
export class DetailsComponent implements OnInit {

  isLoggedIn = (localStorage.getItem('isLoggedIn') === 'true');
  code: any;
  config = this.envService.getConfig();
  listDetails: any = [];
  env = this.deploymentService.getAppDeployment()?.env;

  constructor(
    private mainService: MainService,
    private keycloak: KeycloakService,
    private service: BusinessregistrationService,
    public dialogRef: MatDialogRef<DetailsComponent>,
    @Inject(MAT_DIALOG_DATA) public data: BusinessRegistrationDetailsDialogModel,
    private envService: EnvService,
    private deploymentService: DeploymentService
  ) {
    this.code = data.code;
  }

  ngOnInit(): void {
    this.getDetaisBusiness();
  }

  onDismiss(): void {
    this.dialogRef.close(false);
  }

  getDetaisBusiness() {
    let a = 0;
    if (this.isLoggedIn) {
      a = 1;
    }

    const dataPost: any = {};
    dataPost.in_journal_no = this.code;
    let search = '';    
    // if (this.env?.businessRegistration && this.env?.businessRegistration?.configId != null && this.env?.businessRegistration?.configId != '') {
    //   search = '?config-id=' + this.env?.businessRegistration?.configId;
    // } else {
    //   search = '?agency-id=' + this.config.rootAgency.id + '&subsystem-id=' + this.config.subsystemWebPadsvcId;
    // }
    search = '?agency-id=' + this.config.rootAgency.id + '&subsystem-id=' + this.config.subsystemId;
    this.service.postDetailsFileRegister(search, dataPost).subscribe(item => {
      this.listDetails = [];
      const dataSize = item.data.length;
      for (let i = 0; i < dataSize; i++) {
          this.listDetails.push(item.data[i]);
      }
    }, err => {
      console.log(err);
      // tslint:disable-next-line: align
    });
  }

}

export class BusinessRegistrationDetailsDialogModel {
  constructor(public code: any) {
  }
}
