import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { LGSPHCMLLTPVNEIDLogComponent } from './lgsp-hcm-lltp-vneid-log.component';

describe('ConfigComponent', () => {
  let component: LGSPHCMLLTPVNEIDLogComponent;
  let fixture: ComponentFixture<LGSPHCMLLTPVNEIDLogComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ LGSPHCMLLTPVNEIDLogComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(LGSPHCMLLTPVNEIDLogComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
