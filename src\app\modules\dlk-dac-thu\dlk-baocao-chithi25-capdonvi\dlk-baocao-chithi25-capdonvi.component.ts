import {
  Component,
  OnInit,
  ChangeDetectorRef,
  AfterViewInit,
  ViewChild,
  OnDestroy,
} from '@angular/core';
import { FormGroup, FormControl } from '@angular/forms';
import { Router } from '@angular/router';
import { EnvService } from 'src/app/core/service/env.service';
import { MatTableDataSource } from '@angular/material/table';
import { DatePipe } from '@angular/common';
import { ExportExcelService } from 'src/app/data/service/export-excel/export-excel.service';
import { MainService } from 'src/app/data/service/main/main.service';
import { ProcedureService } from 'src/app/data/service/procedure/procedure.service';
import { takeUntil } from 'rxjs/operators';
import { ReplaySubject, Subject } from 'rxjs';
import { ActivatedRoute } from '@angular/router';
import { DossierService } from 'src/app/data/service/dossier/dossier.service';
import { NgxPrinterService } from 'ngx-printer';
import { SnackbarService } from 'src/app/data/service/snackbar/snackbar.service';
import { MatSelect } from '@angular/material/select';
import { ReportService } from 'data/service/report/report.service';
import { DeploymentService } from 'data/service/deployment.service';
import * as tUtils from 'src/app/data/service/thoai.service';
import { CookieService } from 'ngx-cookie-service';
import { DLKStatisticsService } from 'src/app/data/service/dlk-statistics/dlk-statistics.service';
import { StatisticsService } from 'src/app/data/service/statistics/statistics.service';
import { Workbook } from 'exceljs';
import * as fs from 'file-saver';
export interface Agency {
  id: string;
  name: string;
}
import {
  DossierDetail25DialogModel,
  DossierDetailComponent,
  procedureDetailDialogModel,
} from '../dialogs/view-detail.component';
import { MatDialog } from '@angular/material/dialog';
@Component({
  selector: 'app-dlk-baocao-chithi25-capdonvi',
  templateUrl: './dlk-baocao-chithi25-capdonvi.component.html',
  styleUrls: [
    './dlk-baocao-chithi25-capdonvi.component.scss',
    '/src/app/app.component.scss',
    '/src/app/shared/scss/form-field-outline.scss',
  ],
})
export class DlkBaocaoChithi25CapDonViComponent
  implements OnInit, AfterViewInit, OnDestroy
{
  nowDate = tUtils.newDate();

  // tslint:disable-next-line: max-line-length
  toDate =
    this.nowDate.getFullYear() +
    '-' +
    (this.nowDate.getMonth() + 1 <= 9
      ? '0' + (this.nowDate.getMonth() + 1)
      : this.nowDate.getMonth() + 1) +
    '-' +
    (this.nowDate.getDate() <= 9
      ? '0' + this.nowDate.getDate()
      : this.nowDate.getDate());
  // tslint:disable-next-line: max-line-length
  fromDate =
    this.nowDate.getFullYear() +
    '-' +
    (this.nowDate.getMonth() + 1 <= 9
      ? '0' + (this.nowDate.getMonth() + 1)
      : this.nowDate.getMonth() + 1) +
    '-01';

  keyword = '';
  config = this.envService.getConfig();

  selectedLang = localStorage.getItem('language') || 'vi';
  selectedLangId = Number(localStorage.getItem('languageId')) || 228;
  pageTitle = {
    vi: 'Báo cáo chị thị 25 cấp đơn vị',
    en: 'Report according to the template of Directive 25 for the entire province',
  };

  protected onDestroy = new Subject<void>();
  // =================== old
  env: any = this.deploymentService.getAppDeployment()?.env;
  ELEMENTDATA: any[] = [];
  dataSource: MatTableDataSource<any>;
  // listSector: Array<any> = [];
  keySearchSectorAgency = '';
  // Search Sector
  keywordSector = '';
  totalPagesSector = 0;
  currentPageSector = 0;
  pageSizeSector = 100;
  searchString = '';

  parentAgency = '';
  agencyId = '';
  userAgency?: any = JSON.parse(localStorage.getItem('userAgency'));
  userAgencyCount?: any =
    JSON.parse(localStorage.getItem('listAgencyUser'))?.length || 0;
  ELEMENTDATAALL: any[] = [];
  dataSourceAll: MatTableDataSource<any>;

  listHoSoCapSo = [];
  listHoSoCapTinh = [];
  listHoSoCapHuyen = [];
  listHoSoCapXa = [];
  TongCapSo: any;
  TongCapSoLK: any;
  TongCapTinh: any;
  TongCapHuyen: any;
  TongCapTinhLK: any;
  TongCapHuyenLK: any;
  TongCapXa: any;
  listHoSo = [];
  listHoSoLK = [];
  procedureAgencyLevel =
    this.deploymentService.env.statistics.procedureAgencyLevel;
  Agency = this.env?.OS_DLK;
  size = 10;
  pageIndex = 1;
  page = 1;
  countResult = 0;
  pgSizeOptions = this.config.pageSizeOptions;
  isAdmin?: boolean = sessionStorage.getItem('oneGateAdminMaster') === '1';
  paramsQuery = {
    fromDate: '',
    toDate: '',
    fromLKDate: '',
    toLKDate: '',
    agencyId: null,
    listAgencyId: null,
    page: 0,
  };
  paramsDossier = {
    page: 0,
    size: 10,
    fromDate: null,
    toDate: null,
    agencyId: null,
    applyMethodId: null,
    receivingKind: null,
    hinhThucNop: null,
    keyword: '',
    dossierStatusId: null,
    procedureLevelId: null,
    code: '',
  };

  listAgencyAccept = [];
  listAgency = [];
  listAgencyBC = [];
  keywordAgencyAccept = '';
  totalPagesAgencyAccept = 0;
  currentPageAgencyAccept = 0;
  pageSizeAgencyAccept = 50;
  startDateCumulative = new Date();
  endDateCumulative = new Date();
  startDate = new Date();
  endDate = new Date();
  flagTTPVHCC = true;
  timeOutAgencyAccept: any = null;
  listSectorfillter: any[] = [];
  listSector: any[] = [];
  listProcedure: any[] = [];
  listProcedurefillter: any[] = [];
  listSectorProcedure: any = [];
  totalPagesProcedure = 0;
  currentPageProcedure = 0;
  searchProcedureKeyword = '';
  listHinhThucNhan: any[] = [];
  ListMainXa: any[] = [];
  EXCEL_EXTENSION = '.xlsx';
  EXCEL_TYPE =
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8';
  @ViewChild('procedureMatSelectInfiniteScroll', { static: true })
  procedureMatSelectInfiniteScroll: MatSelect;

  constructor(
    private envService: EnvService,
    public datepipe: DatePipe,
    private mainService: MainService,
    private cdRef: ChangeDetectorRef,
    private procedureService: ProcedureService,
    private datePipe: DatePipe,
    private deploymentService: DeploymentService,
    private dlkStatisticService: DLKStatisticsService,
    private statisticsService: StatisticsService,
    private reportService: ReportService,
    private dialog: MatDialog,
    private snackbar: SnackbarService
  ) {
    this.dataSource = new MatTableDataSource(this.ELEMENTDATA);
    this.dataSourceAll = new MatTableDataSource(this.ELEMENTDATAALL);
  }
  ngOnInit(): void {
    this.mainService.setPageTitle(
      this.pageTitle[localStorage.getItem('language')]
    );
    this.startDate = new Date(this.fromDate);
    this.startDateCumulative = new Date(this.fromDate);
    this.endDate = new Date(this.toDate);
    this.parentAgency = this.Agency?.rootAgencyId
      ? this.Agency?.rootAgencyId
      : '60b87fb59adb921904a0213e'; // dlk
  }

  ngAfterViewInit() {
    this.cdRef.detectChanges();
    this.getListAgencyAccept(this.Agency.rootAgencyId, '', 0, 10000);
    // this.getProcedureByAgencyIdDLK()
  }

  ngOnDestroy() {
    this.onDestroy.next();
    this.onDestroy.complete();
  }
  colToLetter(number) {
    let result = '';
    // number = number - 1; // If starting from 1
    do {
      const letter = String.fromCharCode(65 + (number % 26));
      result = letter + result;
      number = Math.floor(number / 26) - 1;
    } while (number >= 0);
    return result.toLowerCase();
  }
  thongKe() {
    if (this.validateForm() == 1) {
      this.waitingDownloadExcel = true;
      this.paramsQuery.page = 0;
      this.page = 1;
      this.getListHoSo();
    }
  }
  paginate(event) {
    this.paramsQuery.page = event;
    // this.getListHoSo();
  }

  async getListProcedureofDossier(AgencyId): Promise<void> {
    return new Promise<void>((resolve, reject) => {
        this.dlkStatisticService.getListProcedureByAgencyDLK(`?agency-id=${AgencyId}`).subscribe(
            (res) => {
                this.listSector = [];
                this.listProcedure = [];
                const lstprocedure = [];
                const sectorMap = new Map();

                if (res?.content?.length > 0) {
                    res.content.forEach((item) => {
                        lstprocedure.push({
                            sectorName: item.sectorName,
                            sectorId: item.sectorId,
                            procedureId: item.id,
                            procedureName: item.name,
                            MucDo: item.procedureLevelName,
                            status: item.status
                        });

                        if (!sectorMap.has(item.sectorId)) {
                            sectorMap.set(item.sectorId, {
                                sectorId: item.sectorId,
                                sectorName: item.sectorName,
                                status: item.status
                            });
                        } else {
                            let existing = sectorMap.get(item.sectorId);
                            if (existing.sectorName !== item.sectorName) {
                                if (item.status === 1 && existing.status === 0) {
                                    sectorMap.set(item.sectorId, {
                                        sectorId: item.sectorId,
                                        sectorName: item.sectorName,
                                        status: item.status
                                    });
                                }
                            }
                        }
                    });
                }

                this.listSector = Array.from(sectorMap.values()).map(({ sectorId, sectorName }) => ({
                    sectorId,
                    sectorName
                }));
                this.listProcedure = lstprocedure;

                resolve(); // Đánh dấu là hoàn tất
            },
            (err) => {
                console.error(err);
                reject(err); // Nếu có lỗi, reject Promise
            }
        );
    });
}

  getParentId(): string | null {
    const userAgency = JSON.parse(localStorage.getItem('userAgency'));

    if (userAgency) {
      if (!!userAgency.parent && !!userAgency.parent.id && !this.isAdmin) {
        return userAgency.parent.id;
      } else if (userAgency.id !== this.config.rootAgency.id || this.isAdmin) {
        return userAgency.id;
      }
    }
    return null;
  }
  async getListHoSo() {
    try {
        // Thiết lập tham số truy vấn
        this.paramsQuery.fromDate = this.startDate ? this.datePipe.transform(this.startDate, 'yyyy-MM-dd') : '';
        this.paramsQuery.toDate = this.endDate ? this.datePipe.transform(this.endDate, 'yyyy-MM-dd') : '';
        this.paramsQuery.fromLKDate = this.startDateCumulative ? this.datePipe.transform(this.startDateCumulative, 'yyyy-MM-dd') : '';
        this.paramsQuery.toLKDate = this.endDateCumulative ? this.datePipe.transform(this.endDateCumulative, 'yyyy-MM-dd') : '';
        this.paramsQuery.agencyId = this.agencyId;
        this.paramsQuery.listAgencyId = [this.agencyId];

        // Chuyển Observable thành Promise và đợi dữ liệu
        this.listHoSo = await this.dlkStatisticService.getlistDossierCT25(this.paramsQuery).toPromise();

        // Đợi API 2 hoàn tất
        this.listHoSoCapXa = await this.dlkStatisticService.getlistDossierCT25DonVi(this.paramsQuery).toPromise();

        // Gọi BuilData() sau khi cả 2 API đã hoàn tất
        await this.BuilData();
        console.log("BuilData() đã hoàn thành!");

    } catch (error) {
        console.error("Lỗi khi lấy dữ liệu:", error);
    }
  }
  SelectedAgency = '';
  GetDetailProcedure(AgencyId, AgencyName, LevelId) {
    const dialogData = new procedureDetailDialogModel(
      AgencyId,
      AgencyName,
      LevelId,
      1
    );
    const dialogRef = this.dialog.open(DossierDetailComponent, {
      width: '85%',
      height: '90%',
      data: dialogData,
      disableClose: true,
      autoFocus: false,
    });
    dialogRef.afterClosed().subscribe(() => {});
  }
  async GetDetailDossier(
    AgencyId,
    AgencyName,
    TrongKy,
    tongSoHoSo,
    soHoSoTonKyTruoc,
    soHoSoTN,
    soHoSoDXL,
    soHoSoDXLTrongHan,
    soHoSoDXLQuaHan,
    soHoSoCXL,
    soHoSoTONCONHAN,
    soHoSoTONQUAHAN,
    total
  ) {
    if (TrongKy == 1) {
      this.paramsDossier.fromDate = this.startDate
        ? this.datePipe.transform(this.startDate, 'yyyy-MM-dd')
        : '';
      this.paramsDossier.toDate = this.endDate
        ? this.datePipe.transform(this.endDate, 'yyyy-MM-dd')
        : '';
    } else {
      this.paramsDossier.fromDate = this.startDateCumulative
        ? this.datePipe.transform(this.startDateCumulative, 'yyyy-MM-dd')
        : '';
      this.paramsDossier.toDate = this.endDateCumulative
        ? this.datePipe.transform(this.endDateCumulative, 'yyyy-MM-dd')
        : '';
    }
    // tslint:disable-next-line:max-line-length
    await this.getListProcedureofDossier(AgencyId)
    const procedureIds: string[] = this.listProcedure.map(item => item.procedureId);
    const dialogData = new DossierDetail25DialogModel(
      AgencyId,
      AgencyName,
      TrongKy,
      tongSoHoSo,
      soHoSoTonKyTruoc,
      soHoSoTN,
      soHoSoDXL,
      soHoSoDXLTrongHan,
      soHoSoDXLQuaHan,
      soHoSoCXL,
      soHoSoTONCONHAN,
      soHoSoTONQUAHAN,
      this.paramsDossier.fromDate,
      this.paramsDossier.toDate,
      this.listAgency,
      total,
      2,
      procedureIds,
      1
    );
    const dialogRef = this.dialog.open(DossierDetailComponent, {
      width: '95%',
      height: '90%',
      data: dialogData,
      disableClose: true,
      autoFocus: false,
    });
    dialogRef.afterClosed().subscribe(() => {});
  }

  PROCEDUREDATA: any[] = [];

  listproduretotal = [];

  getListAgencyAccept(prid, keyword, page, size) {
    const searchString =
      '?parent-id=' +
      prid +
      '&keyword=' +
      keyword +
      '&page=' +
      page +
      '&size=' +
      size +
      '&sort=name.name,asc&status=1';
    this.procedureService.getListAgencyWithParent(searchString).subscribe(
      (res) => {
        this.listAgencyAccept = res.content.filter(
          (item) =>
            item.level != null && item.level.id == '5f39f4155224cf235e134c59'
        );
        if (this.listAgencyAccept.length > 0) {
          this.agencyId = this.listAgencyAccept[0].id;
          const searchChildString =
            '?parent-id=' +
            this.agencyId +
            '&keyword=' +
            keyword +
            '&page=' +
            page +
            '&size=' +
            size +
            '&sort=name.name,asc&status=1';
          this.procedureService
            .getListAgencyWithParent(searchChildString)
            .subscribe(
              (res) => {
                this.listAgency = res.content.filter(
                  (item) =>
                    item.level && item.level.id === '5febfe2295002b5c79f0fc9f'
                );
                this.getListProcedureofDossier(this.listAgency[0].id);
              },
              (err) => {
                console.log(err);
              }
            );
        }
      },
      (err) => {
        console.log(err);
      }
    );
  }

  validateForm() {
    let language = localStorage.getItem('language');

    let data = {
      errMessage: '',
      status: false,
    };
    if (this.startDate == null || this.endDate == null) {
      data.errMessage =
        language == 'vi'
          ? 'Vui lòng nhập đầy đủ thông tin ngày tháng bắt đầu và kết thúc'
          : 'Please enter complete information for the start and end dates.';
      this.snackbar.openSnackBar(
        0,
        data.errMessage,
        '',
        'error_notification',
        this.config.expiredTime
      );

      return 0;
    } else if (this.startDate.getTime() > this.endDate.getTime()) {
      data.errMessage =
        language == 'vi'
          ? 'Ngày bắt đầu không được lớn hơn ngày kết thúc'
          : 'The start date cannot be later than the end date.';
      this.snackbar.openSnackBar(
        0,
        data.errMessage,
        '',
        'error_notification',
        this.config.expiredTime
      );

      return 0;
    } else if (
      this.startDateCumulative == null ||
      this.endDateCumulative == null
    ) {
      data.errMessage =
        language == 'vi'
          ? 'Vui lòng nhập đầy đủ thông tin ngày tháng bắt đầu lũy kế  và kết thúc lũy kế'
          : 'Please enter complete information for the start and end dates.';
      this.snackbar.openSnackBar(
        0,
        data.errMessage,
        '',
        'error_notification',
        this.config.expiredTime
      );

      return 0;
    } else if (
      this.startDateCumulative.getTime() > this.endDateCumulative.getTime()
    ) {
      data.errMessage =
        language == 'vi'
          ? 'Ngày bắt đầu lũy kế không được lớn hơn ngày kết thúc lũy kế'
          : 'The start date cannot be later than the end date.';
      this.snackbar.openSnackBar(
        0,
        data.errMessage,
        '',
        'error_notification',
        this.config.expiredTime
      );

      return 0;
    } else {
      return 1;
    }
  }

  ViewDetail(i, show) {
    this.listHoSoCapSo[i].show = !show;
  }
  ViewDetailPro(i, ix, show) {
    this.listHoSoCapSo[i].data[0].data[ix].showPro = !show;
  }

  changeAgency() {
    const searchChildString =
      '?parent-id=' +
      this.agencyId +
      '&keyword=' +
      '&page=0' +
      '&size=10000' +
      '&sort=name.name,asc&status=1';
    this.procedureService.getListAgencyWithParent(searchChildString).subscribe(
      (res) => {
        this.listAgency = res.content.filter(
          (item) => item.level && item.level.id === '5febfe2295002b5c79f0fc9f'
        );
        this.getListProcedureofDossier(this.listAgency[0].id);
      },
      (err) => {
        console.log(err);
      }
    );
  }
  async BuilData() {
    this.listHoSoCapSo = [];

    this.TongCapSo = {
      TrongKy: 1,
      tongSoHoSo: 0,
      soHoSoTonKyTruoc: 0,
      soHoSoTN: 0,
      soHoSoDXLTrongHan: 0,
      soHoSoDXLTrongHan_LK: 0,
      soHoSoDXL: 0,
      soHoSoDXL_LK: 0,
      soHoSoDXLQuaHan: 0,
      soHoSoDXLQuaHan_LK: 0,
      soHoSoTON: 0,
      soHoSoTONCONHAN: 0,
      soHoSoTONQUAHAN: 0,
      vanban_QUAHAN: 0,
      vanban_QUAHAN2: 0,
      vanban_MAT_HS: 0,
      vanban_SAISOT: 0,
      vanban_THIEU_TPHS: 0,
      vanban_MAT_HS2: 0,
      vanban_SAISOT2: 0,
      vanban_THIEU_TPHS2: 0,
      vanban_QUAHAN_LK: 0,
      vanban_QUAHAN2_LK: 0,
      vanban_MAT_HS2_LK: 0,
      vanban_SAISOT2_LK: 0,
      vanban_THIEU_TPHS2_LK: 0,
      vanban_MAT_HS_LK: 0,
      vanban_SAISOT_LK: 0,
      vanban_THIEU_TPHS_LK: 0,
      tongSoVBXL: 0,
      tongSoVBXL_LK: 0,
    };

    for (let i = 0; i < this.listAgency.length; i++) {
      // let SlhoSoCoQuan = this.listHoSo.filter(
      //   (f) =>
      //     (f.agencyId !== null && f.agencyId === this.listAgency[i].id) ||
      //     (f.agencyParentId !== null &&
      //       f.agencyParentId === this.listAgency[i].id)
      // );
      await this.getListProcedureofDossier(this.listAgency[i].id);

      var main = {
        id: this.listAgency[i].id,
        coQuan: this.listAgency[i].name,
        data: [],
        show: false,
      };

      var arr = {
        id: this.listAgency[i].id,
        coQuan: this.listAgency[i].name,
        TrongKy: 1,
        tongSoHoSo: 0,
        soHoSoTonKyTruoc: 0,
        soHoSoTN: 0,
        soHoSoDXLTrongHan: 0,
        soHoSoDXLTrongHan_LK: 0,
        soHoSoDXL: 0,
        soHoSoDXL_LK: 0,
        soHoSoDXLQuaHan: 0,
        soHoSoDXLQuaHan_LK: 0,
        soHoSoTON: 0,
        soHoSoTONCONHAN: 0,
        soHoSoTONQUAHAN: 0,
        vanban_QUAHAN: 0,
        vanban_QUAHAN2: 0,
        vanban_MAT_HS2: 0,
        vanban_SAISOT2: 0,
        vanban_THIEU_TPHS2: 0,
        vanban_MAT_HS: 0,
        vanban_SAISOT: 0,
        vanban_THIEU_TPHS: 0,
        vanban_QUAHAN_LK: 0,
        vanban_QUAHAN2_LK: 0,
        vanban_MAT_HS2_LK: 0,
        vanban_SAISOT2_LK: 0,
        vanban_THIEU_TPHS2_LK: 0,
        vanban_MAT_HS_LK: 0,
        vanban_SAISOT_LK: 0,
        vanban_THIEU_TPHS_LK: 0,
        tongSoVBXL: 0,
        tongSoVBXL_LK: 0,
        data: [],
      };

      arr.data = this.BuilDataCoquan(arr.id, arr);
      main.data.push(arr);

      main.data[0].data.forEach((element) => {
        (main.data[0].tongSoHoSo +=
          element.soHoSoTonKyTruoc + element.soHoSoTN),
          (main.data[0].soHoSoTonKyTruoc += element.soHoSoTonKyTruoc),
          (main.data[0].soHoSoTN += element.soHoSoTN),
          (main.data[0].soHoSoDXL +=
            element.soHoSoDXLTrongHan + element.soHoSoDXLQuaHan),
          (main.data[0].soHoSoDXLTrongHan += element.soHoSoDXLTrongHan),
          (main.data[0].soHoSoDXLQuaHan += element.soHoSoDXLQuaHan),
          (main.data[0].soHoSoTON +=
            element.soHoSoTONCONHAN + element.soHoSoTONQUAHAN),
          (main.data[0].soHoSoTONCONHAN += element.soHoSoTONCONHAN),
          (main.data[0].soHoSoTONQUAHAN += element.soHoSoTONQUAHAN);

        main.data[0].tongSoVBXL +=
          element.vanban_QUAHAN +
          element.vanban_QUAHAN2 +
          element.vanban_MAT_HS +
          element.vanban_MAT_HS2 +
          element.vanban_SAISOT +
          element.vanban_SAISOT2 +
          element.vanban_THIEU_TPHS +
          element.vanban_THIEU_TPHS2;

        main.data[0].vanban_QUAHAN +=
          element.vanban_QUAHAN + element.vanban_QUAHAN2;

        main.data[0].vanban_MAT_HS +=
          element.vanban_MAT_HS + element.vanban_MAT_HS2;

        main.data[0].vanban_SAISOT +=
          element.vanban_SAISOT + element.vanban_SAISOT2;

        main.data[0].vanban_THIEU_TPHS +=
          element.vanban_THIEU_TPHS + element.vanban_THIEU_TPHS2;

        main.data[0].tongSoVBXL_LK +=
          element.vanban_QUAHAN_LK +
          element.vanban_QUAHAN2_LK +
          element.vanban_MAT_HS_LK +
          element.vanban_MAT_HS2_LK +
          element.vanban_SAISOT_LK +
          element.vanban_SAISOT2_LK +
          element.vanban_THIEU_TPHS_LK +
          element.vanban_THIEU_TPHS2_LK;
          
        (main.data[0].soHoSoDXL_LK +=
          element.soHoSoDXLTrongHan_LK + element.soHoSoDXLQuaHan_LK),
          (main.data[0].soHoSoDXLQuaHan_LK += element.soHoSoDXLQuaHan_LK),
          (main.data[0].soHoSoDXLTrongHan_LK += element.soHoSoDXLTrongHan_LK);
      });
      this.listHoSoCapSo.push(main);
      this.TongCapSo.tongSoHoSo += arr.tongSoHoSo;
      this.TongCapSo.soHoSoTonKyTruoc += arr.soHoSoTonKyTruoc;
      this.TongCapSo.soHoSoTN += arr.soHoSoTN;
      this.TongCapSo.soHoSoDXL += arr.soHoSoDXL;
      this.TongCapSo.soHoSoDXLTrongHan += arr.soHoSoDXLTrongHan;
      this.TongCapSo.soHoSoDXLQuaHan += arr.soHoSoDXLQuaHan;
      this.TongCapSo.soHoSoTON += arr.soHoSoTON;
      this.TongCapSo.soHoSoTONCONHAN += arr.soHoSoTONCONHAN;
      this.TongCapSo.soHoSoTONQUAHAN += arr.soHoSoTONQUAHAN;
      this.TongCapSo.tongSoVBXL += arr.tongSoVBXL;
      this.TongCapSo.vanban_QUAHAN += arr.vanban_QUAHAN;
      this.TongCapSo.vanban_MAT_HS += arr.vanban_MAT_HS;
      this.TongCapSo.vanban_SAISOT += arr.vanban_SAISOT;
      this.TongCapSo.vanban_THIEU_TPHS += arr.vanban_THIEU_TPHS;
      this.TongCapSo.soHoSoDXL_LK += arr.soHoSoDXL_LK;
      this.TongCapSo.tongSoVBXL_LK += arr.tongSoVBXL_LK;
      this.TongCapSo.soHoSoDXLQuaHan_LK += arr.soHoSoDXLQuaHan_LK;
      this.TongCapSo.soHoSoDXLTrongHan_LK += arr.soHoSoDXLTrongHan_LK;
    }
    this.waitingDownloadExcel = false;
  }

  BuilDataCoquan(AgencyId, item) {
    let data = [];
    let listSectorxa = [];
    let listProcedurexa = [];
    listSectorxa = this.listSector;
    listProcedurexa = this.listProcedure;
    for (let i = 0; i < listSectorxa.length; i++) {
        let sectorId = listSectorxa[i].sectorId;
        let ListThuTuc = listProcedurexa.filter( (f) => f.sectorId !== null && f.sectorId == sectorId);
        if (ListThuTuc.length > 0) {
            var main = {
                id: sectorId,
                sector: listSectorxa[i].sectorName,
                showPro: false,
                tongSoHoSo: 0,
                soHoSoTonKyTruoc: 0,
                soHoSoTN: 0,
                soHoSoDXLTrongHan: 0,
                soHoSoDXL: 0,
                soHoSoDXLQuaHan: 0,
                soHoSoTON: 0,
                soHoSoTONCONHAN: 0,
                soHoSoTONQUAHAN: 0,
                vanban_QUAHAN: 0,
                vanban_QUAHAN2: 0,
                vanban_MAT_HS: 0,
                vanban_SAISOT: 0,
                vanban_THIEU_TPHS: 0,
                vanban_MAT_HS2: 0,
                vanban_SAISOT2: 0,
                vanban_THIEU_TPHS2: 0,
                tongSoVBXL: 0,
                soHoSoDXL_LK: 0,
                soHoSoDXLTrongHan_LK: 0,
                soHoSoDXLQuaHan_LK: 0,
                vanban_QUAHAN_LK: 0,
                vanban_QUAHAN2_LK: 0,
                vanban_MAT_HS_LK: 0,
                vanban_SAISOT_LK: 0,
                vanban_THIEU_TPHS_LK: 0,
                vanban_MAT_HS2_LK: 0,
                vanban_SAISOT2_LK: 0,
                vanban_THIEU_TPHS2_LK: 0,
                tongSoVBXL_LK: 0,
                dataPro: [],
            };
            let listhosoxa = this.listHoSoCapXa.filter( (a) => a.agencyParentId == AgencyId);
            if (listhosoxa.length > 0) {
                for (let j = 0; j < ListThuTuc.length; j++) {
                    let listMatches = listhosoxa.filter(f => f.procedureId !== null && f.procedureId === ListThuTuc[j].procedureId);
                    var arr = {
                        procedureId: ListThuTuc[j].procedureId,
                        procedureName: ListThuTuc[j].procedureName,
                        MucDo: ListThuTuc[j].MucDo,
                        tongSoHoSo: 0,
                        soHoSoTonKyTruoc: 0,
                        soHoSoTN: 0,
                        soHoSoDXLTrongHan: 0,
                        soHoSoDXL: 0,
                        soHoSoDXLQuaHan: 0,
                        soHoSoTON: 0,
                        soHoSoTONCONHAN: 0,
                        soHoSoTONQUAHAN: 0,
                        vanban_QUAHAN: 0,
                        vanban_QUAHAN2: 0,
                        vanban_MAT_HS: 0,
                        vanban_SAISOT: 0,
                        vanban_THIEU_TPHS: 0,
                        vanban_MAT_HS2: 0,
                        vanban_SAISOT2: 0,
                        vanban_THIEU_TPHS2: 0,
                        tongSoVBXL: 0,
                        soHoSoDXL_LK: 0,
                        soHoSoDXLTrongHan_LK: 0,
                        soHoSoDXLQuaHan_LK: 0,
                        vanban_QUAHAN_LK: 0,
                        vanban_QUAHAN2_LK: 0,
                        vanban_MAT_HS_LK: 0,
                        vanban_SAISOT_LK: 0,
                        vanban_THIEU_TPHS_LK: 0,
                        vanban_MAT_HS2_LK: 0,
                        vanban_SAISOT2_LK: 0,
                        vanban_THIEU_TPHS2_LK: 0,
                        tongSoVBXL_LK: 0,
                    };
                    if (listMatches.length > 0) {
                        listMatches.forEach(item => {
                            arr.tongSoHoSo += item.soHoSoTonKyTruoc + item.soHoSoTN;
                            arr.soHoSoTonKyTruoc += item.soHoSoTonKyTruoc;
                            arr.soHoSoTN += item.soHoSoTN;
                            arr.soHoSoDXL += item.soHoSoDXLTrongHan + item.soHoSoDXLQuaHan;
                            arr.soHoSoDXLTrongHan += item.soHoSoDXLTrongHan;
                            arr.soHoSoDXLQuaHan += item.soHoSoDXLQuaHan;
                            arr.soHoSoTON += item.soHoSoTONCONHAN + item.soHoSoTONQUAHAN;
                            arr.soHoSoTONCONHAN += item.soHoSoTONCONHAN;
                            arr.soHoSoTONQUAHAN += item.soHoSoTONQUAHAN;
                            arr.vanban_QUAHAN += item.vanban_QUAHAN + item.vanban_QUAHAN2;
                            arr.vanban_MAT_HS += item.vanban_MAT_HS + item.vanban_MAT_HS2;
                            arr.vanban_SAISOT += item.vanban_SAISOT + item.vanban_SAISOT2;
                            arr.vanban_THIEU_TPHS += item.vanban_THIEU_TPHS + item.vanban_THIEU_TPHS2;
                            arr.tongSoVBXL += item.vanban_QUAHAN + item.vanban_QUAHAN2 + item.vanban_MAT_HS + item.vanban_MAT_HS2 + item.vanban_SAISOT + item.vanban_SAISOT2 + item.vanban_THIEU_TPHS + item.vanban_THIEU_TPHS2; 
                            arr.soHoSoDXL_LK += item.soHoSoDXLTrongHan_LK + item.soHoSoDXLQuaHan_LK;
                            arr.soHoSoDXLTrongHan_LK += item.soHoSoDXLTrongHan_LK;
                            arr.soHoSoDXLQuaHan_LK += item.soHoSoDXLQuaHan_LK;
                            arr.tongSoVBXL_LK +=  item.vanban_QUAHAN_LK + item.vanban_QUAHAN2_LK + item.vanban_MAT_HS_LK + arr.vanban_MAT_HS2_LK + item.vanban_SAISOT_LK + item.vanban_SAISOT2_LK + item.vanban_THIEU_TPHS_LK + item.vanban_THIEU_TPHS2_LK;
                        }
                        );
                        main.soHoSoTonKyTruoc += arr.soHoSoTonKyTruoc;
                        main.tongSoHoSo += arr.tongSoHoSo;
                        main.soHoSoTN += arr.soHoSoTN;
                        main.soHoSoDXL += arr.soHoSoDXL;
                        main.soHoSoDXLTrongHan += arr.soHoSoDXLTrongHan;
                        main.soHoSoDXLQuaHan += arr.soHoSoDXLQuaHan;
                        main.soHoSoTON += arr.soHoSoTON;
                        main.soHoSoTONCONHAN += arr.soHoSoTONCONHAN;
                        main.soHoSoTONQUAHAN += arr.soHoSoTONQUAHAN;
                        main.tongSoVBXL += arr.tongSoVBXL;
                        main.vanban_QUAHAN += arr.vanban_QUAHAN;
                        main.vanban_MAT_HS += arr.vanban_MAT_HS;
                        main.vanban_SAISOT += arr.vanban_SAISOT;
                        main.vanban_THIEU_TPHS += arr.vanban_THIEU_TPHS;
                        main.soHoSoDXL_LK += arr.soHoSoDXL_LK;
                        main.tongSoVBXL_LK += arr.tongSoVBXL_LK;
                        main.soHoSoDXLQuaHan_LK += arr.soHoSoDXLQuaHan_LK;
                        main.soHoSoDXLTrongHan_LK += arr.soHoSoDXLTrongHan_LK;
                    }
                    main.dataPro.push(arr);
                }
                data.push(main);
            }
        }
      }
    return data;
  }

  waitingDownloadExcel: boolean = false;
  async exportToExcel() {
    if (this.validateForm() == 1) {
      //this.dataExport = await this.getDossierStatisticDetailExport();
      this.waitingDownloadExcel = true;
      this.paramsQuery.page = 0;
      this.page = 1;
      this.getListHoSo();
      const from = this.datePipe.transform(this.startDate, 'dd-MM-yyyy');
      const to = this.datePipe.transform(this.endDate, 'dd-MM-yyyy');
      const toLK = this.endDateCumulative
        ? this.datePipe.transform(this.endDateCumulative, 'dd-MM-yyyy')
        : '';
      const newDateshort = this.datePipe.transform(new Date(), 'dd-MM-yyyy');
      const newDate = this.datePipe.transform(
        new Date(),
        'dd-MM-yyyy HH:ss:mm'
      );
      const excelFileName = `Bao_cao_chi_thi_25__capso_${newDate}`;
      let headerXLS = {
        row1: 'CỘNG HÒA XÃ HỘI CHỦ NGHĨA VIỆT NAM',
        row2: 'Độc lập - Tự do - Hạnh phúc',
        row3: 'Đắk lắk, ' + newDateshort,
        row4: `BÁO CÁO CHỈ THỊ 25 CẤP ĐƠN VỊ`,
        row5: `(Từ ${from} đến ngày ${to})`,
      };

      const workbook = new Workbook();
      workbook.creator = 'Snippet Coder';
      workbook.lastModifiedBy = 'SnippetCoder';
      workbook.created = new Date();
      workbook.modified = new Date();
      const worksheet = workbook.addWorksheet('sheet1');

      // Add header row
      worksheet.addRow([]);
      worksheet.mergeCells('A1:X1');
      worksheet.getCell('A1').value = headerXLS.row1;
      worksheet.getCell('A1').alignment = {
        horizontal: 'center',
        vertical: 'middle',
      };
      worksheet.getCell('A1').font = {
        size: 13,
        bold: true,
        name: 'Times New Roman',
      };

      worksheet.mergeCells('A2:X2');
      worksheet.getCell('A2').value = headerXLS.row2;
      worksheet.getCell('A2').alignment = {
        horizontal: 'center',
        vertical: 'middle',
      };
      worksheet.getCell('A2').font = {
        size: 13,
        bold: true,
        name: 'Times New Roman',
      };

      worksheet.mergeCells('A3:X3');
      worksheet.getCell('A3').value = headerXLS.row3;
      worksheet.getCell('A3').alignment = {
        horizontal: 'center',
        vertical: 'middle',
      };
      worksheet.getCell('A3').font = {
        size: 13,
        underline: true,
        name: 'Times New Roman',
      };

      worksheet.mergeCells('A4:X4');
      worksheet.getCell('A4').value = headerXLS.row4;
      worksheet.getCell('A4').alignment = {
        horizontal: 'center',
        vertical: 'middle',
      };
      worksheet.getCell('A4').font = {
        size: 14,
        bold: true,
        name: 'Times New Roman',
      };

      worksheet.mergeCells('A5:X5');
      worksheet.getCell('A5').value = '';
      worksheet.getCell('A5').alignment = {
        horizontal: 'center',
        vertical: 'middle',
      };
      worksheet.getCell('A5').font = {
        size: 11,
        italic: true,
        name: 'Times New Roman',
      };

      worksheet.mergeCells('A6:X6');
      worksheet.getCell('A6').value = headerXLS.row5;
      worksheet.getCell('A6').alignment = {
        horizontal: 'center',
        vertical: 'middle',
      };
      worksheet.getCell('A6').font = {
        size: 11,
        italic: true,
        name: 'Times New Roman',
      };

      worksheet.mergeCells('A7:A10');
      worksheet.getCell('A7').value = 'STT';

      worksheet.mergeCells('B7:B10');
      worksheet.getCell('B7').value = 'Tên cơ quan, đơn vị';

      worksheet.mergeCells('C7:E7');
      worksheet.getCell('C7').value = 'Tổng số hồ sơ đã tiếp nhận trong tháng';
      worksheet.getCell('C7').alignment = {
        horizontal: 'center',
        vertical: 'middle',
        wrapText: true,
      };
      worksheet.getCell('C7').font = {
        size: 11,
        bold: true,
        name: 'Times New Roman',
      };
      worksheet.getCell('C7').border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };

      worksheet.mergeCells('F7:O7');
      worksheet.getCell('F7').value = 'Số hồ sơ đã giải quyết trong tháng';
      worksheet.getCell('F7').alignment = {
        horizontal: 'center',
        vertical: 'middle',
        wrapText: true,
      };
      worksheet.getCell('F7').font = {
        size: 11,
        bold: true,
        name: 'Times New Roman',
      };
      worksheet.getCell('F7').border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };

      worksheet.mergeCells('P7:R7');
      worksheet.getCell('P7').value = 'Số hồ sơ còn tồn chưa giải quyết';
      worksheet.getCell('P7').alignment = {
        horizontal: 'center',
        vertical: 'middle',
        wrapText: true,
      };
      worksheet.getCell('P7').font = {
        size: 11,
        bold: true,
        name: 'Times New Roman',
      };
      worksheet.getCell('P7').border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };

      worksheet.mergeCells('S7:X7');
      worksheet.getCell('S7').value = 'Lũy kế hồ sơ đã giải quyết từ đầu năm';
      worksheet.getCell('S7').alignment = {
        horizontal: 'center',
        vertical: 'middle',
        wrapText: true,
      };
      worksheet.getCell('S7').font = {
        size: 11,
        bold: true,
        name: 'Times New Roman',
      };
      worksheet.getCell('S7').border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };

      worksheet.mergeCells('C8:C10');
      worksheet.getCell('C8').value = 'Tổng số';

      worksheet.mergeCells('D8:E8');
      worksheet.getCell('D8').value = 'Trong đó';
      worksheet.getCell('D8').alignment = {
        horizontal: 'center',
        vertical: 'middle',
        wrapText: true,
      };
      worksheet.getCell('D8').font = {
        size: 11,
        bold: true,
        name: 'Times New Roman',
      };

      worksheet.mergeCells('F8:F10');
      worksheet.getCell('F8').value = 'Tổng số';

      worksheet.mergeCells('G8:O8');
      worksheet.getCell('G8').value = 'Trong đó';
      worksheet.getCell('G8').alignment = {
        horizontal: 'center',
        vertical: 'middle',
        wrapText: true,
      };
      worksheet.getCell('G8').font = {
        size: 11,
        bold: true,
        name: 'Times New Roman',
      };
      worksheet.mergeCells('P8:P10');
      worksheet.getCell('P8').value = 'Tổng số';

      worksheet.mergeCells('Q8:R8');
      worksheet.getCell('Q8').value = 'Trong đó';
      worksheet.getCell('Q8').alignment = {
        horizontal: 'center',
        vertical: 'middle',
        wrapText: true,
      };
      worksheet.getCell('Q8').font = {
        size: 11,
        bold: true,
        name: 'Times New Roman',
      };

      worksheet.mergeCells('S8:S10');
      worksheet.getCell('S8').value = 'Tổng số';

      worksheet.mergeCells('T8:X8');
      worksheet.getCell('T8').value = 'Trong đó';
      worksheet.getCell('T8').alignment = {
        horizontal: 'center',
        vertical: 'middle',
        wrapText: true,
      };
      worksheet.getCell('T8').font = {
        size: 11,
        bold: true,
        name: 'Times New Roman',
      };

      worksheet.mergeCells('D9:D10');
      worksheet.getCell('D9').value =
        'Số hồ sơ chưa giải quyết của tháng trước chuyển qua';

      worksheet.mergeCells('E9:E10');
      worksheet.getCell('E9').value = 'Tổng số hồ sơ tiếp nhận mới trong tháng';

      worksheet.mergeCells('G9:G10');
      worksheet.getCell('G9').value = 'Giải quyết trước, đúng hạn';

      worksheet.mergeCells('H9:H10');
      worksheet.getCell('H9').value = 'Giải quyết quá hạn';

      worksheet.mergeCells('I9:M9');
      worksheet.getCell('I9').value = 'Số văn bản xin lỗi';

      worksheet.mergeCells('N9:N10');
      worksheet.getCell('N9').value =
        'Công khai số văn bản xin lỗi lên hệ thông thông tin giải quyết TTHC (iGate)';

      worksheet.mergeCells('O9:O10');
      worksheet.getCell('O9').value =
        'Số văn bản xin lỗi đã niêm yết tại bộ phận 1 cửa';

      worksheet.mergeCells('Q9:Q10');
      worksheet.getCell('Q9').value = 'Số hồ sơ trước, đúng thời hạn';

      worksheet.mergeCells('R9:R10');
      worksheet.getCell('R9').value = 'Số hồ sơ quá thời hạn';

      worksheet.mergeCells('T9:T10');
      worksheet.getCell('T9').value = 'Giải quyết trước, đúng hạn';

      worksheet.mergeCells('U9:U10');
      worksheet.getCell('U9').value = 'Giải quyết quá hạn';

      worksheet.mergeCells('V9:V10');
      worksheet.getCell('V9').value = 'Số văn bản xin lỗi';

      worksheet.mergeCells('W9:W10');
      worksheet.getCell('W9').value =
        'Công khai Số văn bản xin lỗi lên hệ thông thông tin giải quyết TTHC';

      worksheet.mergeCells('X9:X10');
      worksheet.getCell('X9').value =
        'Số văn bản xin lỗi đã niêm yết tại bộ phận 1 cửa';

      worksheet.getCell('I10').value = 'Tổng số';

      worksheet.getCell('J10').value = 'Do giải quyết quá hạn';

      worksheet.getCell('K10').value = 'Do tiếp nhận thành phần hồ sơ không đủ';

      worksheet.getCell('L10').value = 'Do hồ sơ bị mất, thất lạc hoặc hư hỏng';

      worksheet.getCell('M10').value = 'Do sai sót trong kết quả giải quyết';

      const rowStartHeaderContent = 11;
      const NumberCol = 24;
      for (let index = 0; index < NumberCol; index++) {
        worksheet.getCell(10, index + 1).alignment = {
          horizontal: 'center',
          vertical: 'middle',
          wrapText: true,
        };
        worksheet.getCell(10, index + 1).font = {
          size: 11,
          bold: true,
          name: 'Times New Roman',
        };
        worksheet.getCell(10, index + 1).border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' },
        };

        worksheet.getCell(9, index + 1).alignment = {
          horizontal: 'center',
          vertical: 'middle',
          wrapText: true,
        };
        worksheet.getCell(9, index + 1).font = {
          size: 11,
          bold: true,
          name: 'Times New Roman',
        };
        worksheet.getCell(9, index + 1).border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' },
        };

        worksheet.getCell('T8').border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' },
        };

        worksheet.getCell(rowStartHeaderContent, index + 1).value =
          '(' + (index + 1).toString() + ')';
        worksheet.getCell(rowStartHeaderContent, index + 1).alignment = {
          horizontal: 'center',
          vertical: 'middle',
          wrapText: true,
        };
        worksheet.getCell(rowStartHeaderContent, index + 1).font = {
          size: 11,
          bold: true,
          name: 'Times New Roman',
        };
        worksheet.getCell(rowStartHeaderContent, index + 1).border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' },
        };
      }

      let rowStartContent = rowStartHeaderContent;

      rowStartContent = rowStartContent + 1;

      // cấp sở ban ngành
      const data = this.listHoSoCapSo;
      for (let i = 0; i < data.length; i++) {
        var item = data[i];
        var r = 0;

        worksheet.getCell(rowStartContent, 1).value = i + 1;

        worksheet.getCell(rowStartContent, 2).value = item.coQuan;

        //trong kỳ
        worksheet.getCell(rowStartContent + r, 3).value =
          item.data[r].tongSoHoSo;
        worksheet.getCell(rowStartContent + r, 4).value =
          item.data[r].soHoSoTonKyTruoc;
        worksheet.getCell(rowStartContent + r, 5).value = item.data[r].soHoSoTN;
        worksheet.getCell(rowStartContent + r, 6).value =
          item.data[r].soHoSoDXL;
        worksheet.getCell(rowStartContent + r, 7).value =
          item.data[r].soHoSoDXLTrongHan;
        worksheet.getCell(rowStartContent + r, 8).value =
          item.data[r].soHoSoDXLQuaHan;
        worksheet.getCell(rowStartContent + r, 9).value =
          item.data[r].tongSoVBXL;
        worksheet.getCell(rowStartContent + r, 10).value =
          item.data[r].vanban_QUAHAN;
        worksheet.getCell(rowStartContent + r, 11).value =
          item.data[r].vanban_THIEU_TPHS;
        worksheet.getCell(rowStartContent + r, 12).value =
          item.data[r].vanban_MAT_HS;
        worksheet.getCell(rowStartContent + r, 13).value =
          item.data[r].vanban_SAISOT;
        worksheet.getCell(rowStartContent + r, 14).value =
          item.data[r].tongSoVBXL;
        worksheet.getCell(rowStartContent + r, 15).value =
          item.data[r].tongSoVBXL;
        worksheet.getCell(rowStartContent + r, 16).value =
          item.data[r].soHoSoTON;
        worksheet.getCell(rowStartContent + r, 17).value =
          item.data[r].soHoSoTONCONHAN;
        worksheet.getCell(rowStartContent + r, 18).value =
          item.data[r].soHoSoTONQUAHAN;
        worksheet.getCell(rowStartContent + r, 19).value =
          item.data[r].soHoSoDXL_LK;
        worksheet.getCell(rowStartContent + r, 20).value =
          item.data[r].soHoSoDXLTrongHan_LK;
        worksheet.getCell(rowStartContent + r, 21).value =
          item.data[r].soHoSoDXLQuaHan_LK;
        worksheet.getCell(rowStartContent + r, 22).value =
          item.data[r].tongSoVBXL_LK;
        worksheet.getCell(rowStartContent + r, 23).value =
          item.data[r].tongSoVBXL_LK;
        worksheet.getCell(rowStartContent + r, 24).value =
          item.data[r].tongSoVBXL_LK;

        for (let c = 0; c < NumberCol; c++) {
          worksheet.getCell(rowStartContent + r, c + 1).alignment = {
            horizontal: 'center',
            vertical: 'middle',
            wrapText: true,
          };
          worksheet.getCell(rowStartContent + r, c + 1).font = {
            size: 11,
            name: 'Times New Roman',
          };
          worksheet.getCell(rowStartContent + r, c + 1).border = {
            top: { style: 'thin' },
            left: { style: 'thin' },
            bottom: { style: 'thin' },
            right: { style: 'thin' },
          };
        }

        for (let c = 0; c < NumberCol; c++) {
          worksheet.getCell(rowStartContent + r, c + 1).alignment = {
            horizontal: 'center',
            vertical: 'middle',
            wrapText: true,
          };
          worksheet.getCell(rowStartContent + r, c + 1).font = {
            size: 11,
            name: 'Times New Roman',
          };
          worksheet.getCell(rowStartContent + r, c + 1).border = {
            top: { style: 'thin' },
            left: { style: 'thin' },
            bottom: { style: 'thin' },
            right: { style: 'thin' },
          };
        }
        
        rowStartContent = rowStartContent + 1;

        for (let iSec = 0; iSec < item.data[0].data.length; iSec++) {
          var itemSec = item.data[0].data[iSec];
          var r = 0;
  
          worksheet.getCell(rowStartContent, 1).value = this.colToLetter(iSec);
  
          worksheet.getCell(rowStartContent, 2).value = itemSec.sector;
  
          //trong kỳ
          worksheet.getCell(rowStartContent + r, 3).value =
            itemSec.tongSoHoSo;
          worksheet.getCell(rowStartContent + r, 4).value =
            itemSec.soHoSoTonKyTruoc;
          worksheet.getCell(rowStartContent + r, 5).value = 
            itemSec.soHoSoTN;
          worksheet.getCell(rowStartContent + r, 6).value =
            itemSec.soHoSoDXL;
          worksheet.getCell(rowStartContent + r, 7).value =
            itemSec.soHoSoDXLTrongHan;
          worksheet.getCell(rowStartContent + r, 8).value =
            itemSec.soHoSoDXLQuaHan;
          worksheet.getCell(rowStartContent + r, 9).value =
            itemSec.tongSoVBXL;
          worksheet.getCell(rowStartContent + r, 10).value =
            itemSec.vanban_QUAHAN;
          worksheet.getCell(rowStartContent + r, 11).value =
            itemSec.vanban_THIEU_TPHS;
          worksheet.getCell(rowStartContent + r, 12).value =
            itemSec.vanban_MAT_HS;
          worksheet.getCell(rowStartContent + r, 13).value =
            itemSec.vanban_SAISOT;
          worksheet.getCell(rowStartContent + r, 14).value =
            itemSec.tongSoVBXL;
          worksheet.getCell(rowStartContent + r, 15).value =
            itemSec.tongSoVBXL;
          worksheet.getCell(rowStartContent + r, 16).value =
            itemSec.soHoSoTON;
          worksheet.getCell(rowStartContent + r, 17).value =
            itemSec.soHoSoTONCONHAN;
          worksheet.getCell(rowStartContent + r, 18).value =
            itemSec.soHoSoTONQUAHAN;
          worksheet.getCell(rowStartContent + r, 19).value =
            itemSec.soHoSoDXL_LK;
          worksheet.getCell(rowStartContent + r, 20).value =
            itemSec.soHoSoDXLTrongHan_LK;
          worksheet.getCell(rowStartContent + r, 21).value =
            itemSec.soHoSoDXLQuaHan_LK;
          worksheet.getCell(rowStartContent + r, 22).value =
            itemSec.tongSoVBXL_LK;
          worksheet.getCell(rowStartContent + r, 23).value =
            itemSec.tongSoVBXL_LK;
          worksheet.getCell(rowStartContent + r, 24).value =
            itemSec.tongSoVBXL_LK;
  
          for (let c = 0; c < NumberCol; c++) {
            worksheet.getCell(rowStartContent + r, c + 1).alignment = {
              horizontal: 'center',
              vertical: 'middle',
              wrapText: true,
            };
            worksheet.getCell(rowStartContent + r, c + 1).font = {
              size: 11,
              name: 'Times New Roman',
            };
            worksheet.getCell(rowStartContent + r, c + 1).border = {
              top: { style: 'thin' },
              left: { style: 'thin' },
              bottom: { style: 'thin' },
              right: { style: 'thin' },
            };
          }
  
          for (let c = 0; c < NumberCol; c++) {
            worksheet.getCell(rowStartContent + r, c + 1).alignment = {
              horizontal: 'center',
              vertical: 'middle',
              wrapText: true,
            };
            worksheet.getCell(rowStartContent + r, c + 1).font = {
              size: 11,
              name: 'Times New Roman',
            };
            worksheet.getCell(rowStartContent + r, c + 1).border = {
              top: { style: 'thin' },
              left: { style: 'thin' },
              bottom: { style: 'thin' },
              right: { style: 'thin' },
            };
          }
          
          rowStartContent = rowStartContent + 1;
          for (let iPro = 0; iPro < itemSec.dataPro.length; iPro++) {
            var itemPro = itemSec.dataPro[iPro];
            var r = 0;
    
            worksheet.getCell(rowStartContent, 1).value = this.colToLetter(iSec) + "." + this.colToLetter(iPro);
    
            worksheet.getCell(rowStartContent, 2).value = itemPro.procedureName;
    
            //trong kỳ
            worksheet.getCell(rowStartContent + r, 3).value =
              itemPro.tongSoHoSo;
            worksheet.getCell(rowStartContent + r, 4).value =
              itemPro.soHoSoTonKyTruoc;
            worksheet.getCell(rowStartContent + r, 5).value = 
              itemPro.soHoSoTN;
            worksheet.getCell(rowStartContent + r, 6).value =
              itemPro.soHoSoDXL;
            worksheet.getCell(rowStartContent + r, 7).value =
              itemPro.soHoSoDXLTrongHan;
            worksheet.getCell(rowStartContent + r, 8).value =
              itemPro.soHoSoDXLQuaHan;
            worksheet.getCell(rowStartContent + r, 9).value =
              itemPro.tongSoVBXL;
            worksheet.getCell(rowStartContent + r, 10).value =
              itemPro.vanban_QUAHAN;
            worksheet.getCell(rowStartContent + r, 11).value =
              itemPro.vanban_THIEU_TPHS;
            worksheet.getCell(rowStartContent + r, 12).value =
              itemPro.vanban_MAT_HS;
            worksheet.getCell(rowStartContent + r, 13).value =
              itemPro.vanban_SAISOT;
            worksheet.getCell(rowStartContent + r, 14).value =
              itemPro.tongSoVBXL;
            worksheet.getCell(rowStartContent + r, 15).value =
              itemPro.tongSoVBXL;
            worksheet.getCell(rowStartContent + r, 16).value =
              itemPro.soHoSoTON;
            worksheet.getCell(rowStartContent + r, 17).value =
              itemPro.soHoSoTONCONHAN;
            worksheet.getCell(rowStartContent + r, 18).value =
              itemPro.soHoSoTONQUAHAN;
            worksheet.getCell(rowStartContent + r, 19).value =
              itemPro.soHoSoDXL_LK;
            worksheet.getCell(rowStartContent + r, 20).value =
              itemPro.soHoSoDXLTrongHan_LK;
            worksheet.getCell(rowStartContent + r, 21).value =
              itemPro.soHoSoDXLQuaHan_LK;
            worksheet.getCell(rowStartContent + r, 22).value =
              itemPro.tongSoVBXL_LK;
            worksheet.getCell(rowStartContent + r, 23).value =
              itemPro.tongSoVBXL_LK;
            worksheet.getCell(rowStartContent + r, 24).value =
              itemPro.tongSoVBXL_LK;
    
            for (let c = 0; c < NumberCol; c++) {
              worksheet.getCell(rowStartContent + r, c + 1).alignment = {
                horizontal: 'center',
                vertical: 'middle',
                wrapText: true,
              };
              worksheet.getCell(rowStartContent + r, c + 1).font = {
                size: 11,
                name: 'Times New Roman',
              };
              worksheet.getCell(rowStartContent + r, c + 1).border = {
                top: { style: 'thin' },
                left: { style: 'thin' },
                bottom: { style: 'thin' },
                right: { style: 'thin' },
              };
            }
    
            for (let c = 0; c < NumberCol; c++) {
              worksheet.getCell(rowStartContent + r, c + 1).alignment = {
                horizontal: 'center',
                vertical: 'middle',
                wrapText: true,
              };
              worksheet.getCell(rowStartContent + r, c + 1).font = {
                size: 11,
                name: 'Times New Roman',
              };
              worksheet.getCell(rowStartContent + r, c + 1).border = {
                top: { style: 'thin' },
                left: { style: 'thin' },
                bottom: { style: 'thin' },
                right: { style: 'thin' },
              };
            }
            
            rowStartContent = rowStartContent + 1;
          }
        }
      }

      //Tổng cấp tỉnh
      worksheet.mergeCells(rowStartContent, 1, rowStartContent, 2);
      worksheet.getCell(rowStartContent, 1).value = 'TỔNG';
      worksheet.getCell(rowStartContent, 1).alignment = {
        horizontal: 'center',
        vertical: 'middle',
      };
      worksheet.getCell(rowStartContent, 1).font = {
        size: 11,
        bold: true,
        name: 'Times New Roman',
      };
      worksheet.getCell(rowStartContent, 1).border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
      worksheet.getCell(rowStartContent + r, 3).value =
        this.TongCapSo.tongSoHoSo;
      worksheet.getCell(rowStartContent + r, 4).value =
        this.TongCapSo.soHoSoTonKyTruoc;
      worksheet.getCell(rowStartContent + r, 5).value = this.TongCapSo.soHoSoTN;
      worksheet.getCell(rowStartContent + r, 6).value =
        this.TongCapSo.soHoSoDXL;
      worksheet.getCell(rowStartContent + r, 7).value =
        this.TongCapSo.soHoSoDXLTrongHan;
      worksheet.getCell(rowStartContent + r, 8).value =
        this.TongCapSo.soHoSoDXLQuaHan;
      worksheet.getCell(rowStartContent + r, 9).value =
        this.TongCapSo.tongSoVBXL;
      worksheet.getCell(rowStartContent + r, 10).value =
        this.TongCapSo.vanban_QUAHAN;
      worksheet.getCell(rowStartContent + r, 11).value =
        this.TongCapSo.vanban_THIEU_TPHS;
      worksheet.getCell(rowStartContent + r, 12).value =
        this.TongCapSo.vanban_MAT_HS;
      worksheet.getCell(rowStartContent + r, 13).value =
        this.TongCapSo.vanban_SAISOT;
      worksheet.getCell(rowStartContent + r, 14).value =
        this.TongCapSo.tongSoVBXL;
      worksheet.getCell(rowStartContent + r, 15).value =
        this.TongCapSo.tongSoVBXL;
      worksheet.getCell(rowStartContent + r, 16).value =
        this.TongCapSo.soHoSoTON;
      worksheet.getCell(rowStartContent + r, 17).value =
        this.TongCapSo.soHoSoTONCONHAN;
      worksheet.getCell(rowStartContent + r, 18).value =
        this.TongCapSo.soHoSoTONQUAHAN;
      worksheet.getCell(rowStartContent + r, 19).value =
        this.TongCapSo.soHoSoDXL_LK;
      worksheet.getCell(rowStartContent + r, 20).value =
        this.TongCapSo.soHoSoDXLTrongHan_LK;
      worksheet.getCell(rowStartContent + r, 21).value =
        this.TongCapSo.soHoSoDXLQuaHan_LK;
      worksheet.getCell(rowStartContent + r, 22).value =
        this.TongCapSo.tongSoVBXL_LK;
      worksheet.getCell(rowStartContent + r, 23).value =
        this.TongCapSo.tongSoVBXL_LK;
      worksheet.getCell(rowStartContent + r, 24).value =
        this.TongCapSo.tongSoVBXL_LK;

      for (let index = 2; index < NumberCol; index++) {
        worksheet.getCell(rowStartContent, index + 1).alignment = {
          horizontal: 'center',
          vertical: 'middle',
        };
        worksheet.getCell(rowStartContent, index + 1).font = {
          size: 11,
          bold: true,
          name: 'Times New Roman',
        };
        worksheet.getCell(rowStartContent, index + 1).border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' },
        };

        worksheet.getCell(rowStartContent - 1, index + 1).alignment = {
          horizontal: 'center',
          vertical: 'middle',
        };
        worksheet.getCell(rowStartContent - 1, index + 1).font = {
          size: 11,
          bold: true,
          name: 'Times New Roman',
        };
        worksheet.getCell(rowStartContent - 1, index + 1).border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' },
        };
      }

      worksheet.getColumn(2).width = 60;
      worksheet.getColumn(2).alignment = {
        horizontal: 'left',
        vertical: 'middle',
        wrapText: true,
      };
      worksheet.getRow(1).alignment = {
        horizontal: 'center',
        vertical: 'middle',
        wrapText: true,
      };
      worksheet.getRow(2).alignment = {
        horizontal: 'center',
        vertical: 'middle',
        wrapText: true,
      };
      worksheet.getRow(3).alignment = {
        horizontal: 'center',
        vertical: 'middle',
        wrapText: true,
      };
      worksheet.getRow(4).alignment = {
        horizontal: 'center',
        vertical: 'middle',
        wrapText: true,
      };
      worksheet.getRow(5).alignment = {
        horizontal: 'center',
        vertical: 'middle',
        wrapText: true,
      };
      worksheet.getRow(6).alignment = {
        horizontal: 'center',
        vertical: 'middle',
        wrapText: true,
      };
      worksheet.getRow(7).alignment = {
        horizontal: 'center',
        vertical: 'middle',
        wrapText: true,
      };
      worksheet.getRow(8).alignment = {
        horizontal: 'center',
        vertical: 'middle',
        wrapText: true,
      };
      worksheet.getRow(9).alignment = {
        horizontal: 'center',
        vertical: 'middle',
        wrapText: true,
      };
      worksheet.getRow(10).alignment = {
        horizontal: 'center',
        vertical: 'middle',
        wrapText: true,
      };
      worksheet.getRow(11).alignment = {
        horizontal: 'center',
        vertical: 'middle',
        wrapText: true,
      };
      // Save Excel File
      workbook.xlsx.writeBuffer().then((dataBuffer: ArrayBuffer) => {
        const blob = new Blob([dataBuffer], { type: this.EXCEL_TYPE });
        fs.saveAs(blob, excelFileName + this.EXCEL_EXTENSION);
      });
    }
  }
}
