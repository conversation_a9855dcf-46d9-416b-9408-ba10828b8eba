import { Component, OnInit, ChangeDetectorRef, AfterViewInit, On<PERSON>estroy, ViewChild, ElementRef } from '@angular/core';
import { FormGroup, FormControl } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import { EnvService } from 'src/app/core/service/env.service';
import { FormElement } from 'src/app/data/schema/form-element';
import { MatTableDataSource } from '@angular/material/table';
import { AssignedAgencyComponent, ConfirmAssignedAgencyDialogModel } from 'src/app/modules/procedure/dialogs/assigned-agency/assigned-agency.component';
import { ConfirmDeleteDialogModel, DeleteAdministrativeComponent } from './dialogs/delete-administrative/delete-administrative.component';
import { SnackbarService } from 'src/app/data/service/snackbar/snackbar.service';
import { ProcedureService } from 'src/app/data/service/procedure/procedure.service';
import { DossierService } from 'src/app/data/service/dossier/dossier.service';
import { Observable, ReplaySubject, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { ActivatedRoute, Router } from '@angular/router';
import { MainService } from 'src/app/data/service/main/main.service';
import { MatSelect } from '@angular/material/select';
import { AssignedOfficerComponent, ConfirmAssignedOfficerDialogModel } from 'src/app/modules/procedure/dialogs/assigned-officer/assigned-officer.component';
import { DeploymentService } from 'src/app/data/service/deployment.service';
import { ProcessService } from 'src/app/data/service/process/process.service';
import { AgencyService } from 'src/app/data/service/basedata/agency.service';
import jwt_decode from 'jwt-decode';
import { LevelProcedureFailComponent, LevelProcedureFailDialogModel } from 'src/app/modules/procedure/dialogs/level-procedure-fail/level-procedure-fail.component';
import { AttachedFilesComponent, ConfirmAttachedFilesDialogModel } from 'src/app/modules/procedure/dialogs/attached-files/attached-files.component';
import { DownloadExcelComponent } from 'src/app/modules/procedure/dialogs/download-excel/download-excel.component';
import { UserService } from 'src/app/data/service/user.service';
import { AddAdministrativeComponent, ConfirmAddDialogModel } from './dialogs/add-administrative/add-administrative.component';
import { ConfirmUpdateDialogModel, UpdateAdministrativeComponent } from './dialogs/update-administrative/update-administrative.component';
import { BasepadService } from 'src/app/data/service/basepad/basepad.service';

export interface Agency {
  id: string;
  name: string;
}
@Component({
  selector: 'app-administrative',
  templateUrl: './administrative.component.html',
  styleUrls: ['./administrative.component.scss']
})
export class AdministrativeComponent implements OnInit, AfterViewInit, OnDestroy {

  config = this.envService.getConfig();
  selectedLang = localStorage.getItem('language') || 'vi';
  selectedLangId = Number(localStorage.getItem('languageId')) || 228;
  env = this.deploymentService.getAppDeployment().env;

  padsvcURL = this.env?.domain?.padsvc || this.config.padsvcURL;

  pageTitle = {
    vi: `Danh mục thủ tục`,
    en: `Procedure catalog`
  };
  provinceName = '';

  searchForm = new FormGroup({
    keyword: new FormControl(''),
  });

  displayedColumns: string[] = ['stt', 'code', 'name', 'start value','end value', 'now value', 'display syntax','status','action'];
  ELEMENTDATA: FormElement[] = [];
  dataSource: MatTableDataSource<FormElement>;

  listFullAgencyPage = 0;
  listProcedureLevel = [];

  listAgencyLevel = [];
  filteredAgencyOptions: Observable<Agency[]>;

  size = 10;
  pageIndex = 1;
  page = 1;
  countResult = 0;
  pgSizeOptions = this.config.pageSizeOptions;

 
  
  isAdmin?: boolean = sessionStorage.getItem('oneGateAdminMaster') === '1';
  showPublicAdministrativeAgency = this.deploymentService.env.OS_HCM.showPublicAdministrativeAgency;
  
  protected onDestroy = new Subject<void>();
 
  constructor(
    private dialog: MatDialog,
    private envService: EnvService,
    private deploymentService: DeploymentService,
    private cdRef: ChangeDetectorRef,
    private snackbarService: SnackbarService,
    private procedureService: ProcedureService,
    private processService: ProcessService,
    private dossierService: DossierService,
    private activeRoute: ActivatedRoute,
    private mainService: MainService,
    private agencyService: AgencyService,
    private router: Router,
    private userService: UserService,
    private basepadService: BasepadService
  ) {
    this.dataSource = new MatTableDataSource(this.ELEMENTDATA);

  }

  async ngOnInit(): Promise<void> {
    const permissions = this.userService.getUserPermissions();
    const formObj = this.searchForm.getRawValue();
    let searchString = '?page=0&size=' + this.size + '&spec=page&keyword=' + encodeURI(formObj.keyword.trim());
    if(this.showPublicAdministrativeAgency && this.showPublicAdministrativeAgency.enable && (!this.isAdmin || !this.showPublicAdministrativeAgency.admin) && this.showPublicAdministrativeAgency.filterProcedureAdmin){
      let agencyId = this.getAgencyId();
      let rootAgency = await this.agencyService.getRootAgency(agencyId);
      if(rootAgency && rootAgency.tag.find(o => o.id == this.showPublicAdministrativeAgency.agencyTagId)){
        agencyId = rootAgency.id;
      }
      searchString += '&agency-id=' + agencyId;
    }
    this.getAllProcedureAdministration(searchString); 
  }

  getAgencyId(): string | null {
    const userAgency = JSON.parse(localStorage.getItem('userAgency'));
    if (userAgency){
        return userAgency.id;
    }
    return null;
  }

  getAllProcedureAdministration(search){
    this.basepadService.searchProceAdministration(search).subscribe(async data =>{
      this.ELEMENTDATA = [];
      this.countResult = data.totalElements;
      for (let i = 0; i < data.numberOfElements; i++) {
        data.content[i].stt = this.size * (this.pageIndex - 1) + (i + 1);
        // data.content[i].agency=data.content[i].agency[0].name[0].name;
        // data.content[i].procedure= data.content[i].procedure[0].name[0].name;
        // tslint:disable-next-line: max-line-length
      //  const guideDetailType = await this.categoryService.getDetailGuideType(data.content[i].tag[0].id).catch(e => console.log('Error: ', e.message));
        // if (guideDetailType !== undefined) {
        //   // // tslint:disable-next-line: prefer-for-of
        //   // for (let j = 0; j < guideDetailType.trans.length; j++) {
        //   //   if (Number(guideDetailType.trans[j].languageId) === this.selectedLangId) {
        //   //     data.content[i].type = guideDetailType.trans[j].name;
        //   //   }
        //   // }
        // }
        if(data.content[i].status!=null){
          if(data.content[i].status==1){
            data.content[i].status='Mở';
          }else{
            data.content[i].status='Đóng';
          }
        }
        if(data.content[i].name==null){
          data.content[i].name='';
        }
        this.ELEMENTDATA.push(data.content[i]);
      }
      this.dataSource.data = this.ELEMENTDATA;
      console.log('this.dataSource.data',this.dataSource.data);
    });
  }

  ngOnDestroy() {
    this.onDestroy.next();
    this.onDestroy.complete();
  }

  updateDialog(id) {
    const dialogData = new ConfirmUpdateDialogModel(id);
    const dialogRef = this.dialog.open(UpdateAdministrativeComponent, {
      minWidth: '60vw',
      maxWidth: '60vw',
      maxHeight: '90vh',
      data: dialogData,
      disableClose: true,
      autoFocus: false
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
      const res = dialogResult;
      const content = '';
      if (res.status === true) {
        this.snackbarService.openSnackBar(1, this.envService.getTranslateNotificationLabel(this.selectedLang, 'success', 'put'), content, 'success_notification', this.config.expiredTime);
        this.paginate(this.pageIndex, 0);
      }
      if (res.status === false) {
        this.snackbarService.openSnackBar(0, this.envService.getTranslateNotificationLabel(this.selectedLang, 'error', 'put'), content, 'error_notification', this.config.expiredTime);
      }
    });
  }

  addInforDialog() {
    const dialogData = new ConfirmAddDialogModel();
    const dialogRef = this.dialog.open(AddAdministrativeComponent, {
      minWidth: '60vw',
      maxWidth: '60vw',
      maxHeight: '90vh',
      data: dialogData,
      disableClose: true,
      autoFocus: false
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
      const res = dialogResult;
      const content = '';
      if (res.status === true) {
        const totalPage = Math.ceil((this.countResult + 1) / this.size);
        this.page = totalPage;
        this.paginate(totalPage, 1);
        this.snackbarService.openSnackBar(1, this.envService.getTranslateNotificationLabel(this.selectedLang, 'success', 'post'), content, 'success_notification', this.config.expiredTime);
      }
      if (res.status === false) {
        this.snackbarService.openSnackBar(0, this.envService.getTranslateNotificationLabel(this.selectedLang, 'error', 'post'), content, 'error_notification', this.config.expiredTime);
      }
    });
  }


  async onConfirmSearch() {
    const formObj = this.searchForm.getRawValue();
    let searchString = '?page=0&size=' + this.size + '&spec=page&keyword=' + encodeURI(formObj.keyword.trim());
    if(this.showPublicAdministrativeAgency && this.showPublicAdministrativeAgency.enable && (!this.isAdmin || !this.showPublicAdministrativeAgency.admin) && this.showPublicAdministrativeAgency.filterProcedureAdmin){
      let agencyId = this.getAgencyId();
      let rootAgency = await this.agencyService.getRootAgency(agencyId);
      if(rootAgency && rootAgency.tag.find(o => o.id == this.showPublicAdministrativeAgency.agencyTagId)){
        agencyId = rootAgency.id;
      }
      searchString += '&agency-id=' + agencyId;
    }
    this.getAllProcedureAdministration(searchString);
    console.log('page', this.pageIndex, this.pgSizeOptions);
  }

  ngAfterViewInit() {
    this.cdRef.detectChanges();
  }

  // ========================================================== Manual function



  deleteDialog(id, code) {
    const dialogData = new ConfirmDeleteDialogModel(id, code);
    const dialogRef = this.dialog.open(DeleteAdministrativeComponent , {
      width: '500px',
      data: dialogData,
      disableClose: true,
      autoFocus: false
    });
    const content = '';
    dialogRef.afterClosed().subscribe(dialogResult => {
      const res = dialogResult;
      if (res === true) {
        const totalPage = Math.ceil((this.countResult - 1) / this.size);
        if (this.page > totalPage) {
          this.page = totalPage;
          this.paginate(totalPage, 0);
        }
        else { this.paginate(this.page, 0); }
        this.snackbarService.openSnackBar(1, this.envService.getTranslateNotificationLabel(this.selectedLang, 'success', 'delete'), content, 'success_notification', this.config.expiredTime);
      }
      if (res === false) {
        this.snackbarService.openSnackBar(0, this.envService.getTranslateNotificationLabel(this.selectedLang, 'error', 'delete'), content, 'error_notification', this.config.expiredTime);
      }
    });
  }

  async paginate(event: any, type) {
    const formObj = this.searchForm.getRawValue();

    let searchString = '';
    switch (type) {
      case 0:
        this.pageIndex = event;
        this.page = event;
        if (formObj.keyword.length !== 0) {
          // tslint:disable-next-line: max-line-length
          // this.getAllProcedureAdministration('?page=' + (this.pageIndex - 1) + '&size=' + this.size + '&spec=page&keyword=' + encodeURI(formObj.keyword.trim())
          //  );
           searchString = '?page=' + (this.pageIndex - 1) + '&size=' + this.size + '&spec=page&keyword=' + encodeURI(formObj.keyword.trim());
        } else {
          // tslint:disable-next-line: max-line-length
          //this.getAllProcedureAdministration('?page=' + (this.pageIndex - 1) + '&size=' + this.size + '&spec=page&keyword=' + encodeURI(formObj.keyword.trim()));
          searchString = '?page=' + (this.pageIndex - 1) + '&size=' + this.size + '&spec=page&keyword=' + encodeURI(formObj.keyword.trim());
        }
        break;
      case 1:
        this.pageIndex = 1;
        this.page = 1;
        if (formObj.keyword.length !== 0) {
          // tslint:disable-next-line: max-line-length
          // this.getAllProcedureAdministration('?page=' + (this.pageIndex - 1) + '&size=' + this.size + '&spec=page&keyword=' + encodeURI(formObj.keyword.trim())
          //   );
            searchString = '?page=' + (this.pageIndex - 1) + '&size=' + this.size + '&spec=page&keyword=' + encodeURI(formObj.keyword.trim());
        } else {
          // tslint:disable-next-line: max-line-length
          //this.getAllProcedureAdministration('?page=' + (this.pageIndex - 1) + '&size=' + this.size + '&spec=page&keyword=' + encodeURI(formObj.keyword.trim()));
          searchString = '?page=' + (this.pageIndex - 1) + '&size=' + this.size + '&spec=page&keyword=' + encodeURI(formObj.keyword.trim());
        }
        break;
    }
    if(this.showPublicAdministrativeAgency && this.showPublicAdministrativeAgency.enable && (!this.isAdmin || !this.showPublicAdministrativeAgency.admin) && this.showPublicAdministrativeAgency.filterProcedureAdmin){
      let agencyId = this.getAgencyId();
      let rootAgency = await this.agencyService.getRootAgency(agencyId);
      if(rootAgency && rootAgency.tag.find(o => o.id == this.showPublicAdministrativeAgency.agencyTagId)){
        agencyId = rootAgency.id;
      }
      searchString += '&agency-id=' + agencyId;
    }
    this.getAllProcedureAdministration(searchString);
  }


}

