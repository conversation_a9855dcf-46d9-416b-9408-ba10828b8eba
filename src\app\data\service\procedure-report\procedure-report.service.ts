import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { ApiProviderService } from 'src/app/core/service/api-provider.service';
import { Observable } from 'rxjs';
import { saveAs } from 'file-saver';

@Injectable({
  providedIn: 'root'
})
export class ProcedureReportService {

  constructor(
    private http: HttpClient,
    private apiProviderService: ApiProviderService
  ) { }

  private sectorPath = this.apiProviderService.getUrl('digo', 'basepad') + '/sector';
  private levelPath = this.apiProviderService.getUrl('digo', 'basecat') + '/tag/';
  private agencyPath = this.apiProviderService.getUrl('digo', 'basedata') + '/agency';
  private procedurePath = this.apiProviderService.getUrl('digo', 'basepad') + '/procedure';
  private procedureFormEFormPath = this.apiProviderService.getUrl('digo', 'basepad') + '/procedure-form-eform';
  private reporterPath = this.apiProviderService.getUrl('digo', 'reporter') + '/dossier';
  private user = this.apiProviderService.getUrl('digo', 'human') + '/user';
  private category = this.apiProviderService.getUrl('digo', 'basepad');
  private bpm = this.apiProviderService.getUrl('digo', 'bpm');
  getListSector(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.sectorPath + searchString, { headers });
  }

  getListAgency(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.agencyPath + searchString, { headers });
  }

  getAllAgency(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.agencyPath + '/name+code/--bdg' + searchString, { headers });
  }

  getListUser(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.user + '/getAllUsersBdg' + searchString, { headers });
  }

  getListCategory(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.category + searchString, { headers });
  }

  getListLevel(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.levelPath + '--by-category-id' + searchString, { headers });
  }

  getListDossierByDay(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.reporterPath + '/--by-day' + searchString, { headers });
  }

  getListProcedure(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.procedurePath + searchString, { headers });
  }

  getViewCount(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.procedurePath + '/' + id, { headers });
  }

  getReportTemplate(url): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Access-Control-Allow-Origin', '*');
    return this.http.get(url);
  }

  getReportTemplate_HTML(url): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Access-Control-Allow-Origin', '*');
    return this.http.get(url, { responseType: 'text' });
  }

  getListDossierByProcedure(search: String): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.reporterPath + '/statistic/--by-procedure' + search, { headers });
  }

  getListDossierByProcedureAll(search: String): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.reporterPath + '/statistic/--by-procedure-all' + search, { headers });
  }

  getListDuplicateForm(search :String): Observable<any>{
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.procedurePath + '/--find-duplicate-form' + search, { headers });
  }

  getListInvalidProcedureForm(search :String): Observable<any>{
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.procedureFormEFormPath + '/--find-invalid-document' + search, { headers });
  }

  getListMissingEForm(search :String): Observable<any>{
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.procedureFormEFormPath + '/--find-misssing-eform' + search, { headers });
  }

  getListProcedureStatisticBDG(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.procedurePath + `/--find-statistic-bdg${searchString}`, { headers });
  }

  getListProcedureDetailStatisticBDG(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.procedurePath + `/--find-statistic-bdg-detail${searchString}`, { headers });
  }

  getListProcessDefinitionNotApplicantEForm(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.bpm + '/process-definition' + `/--get-list-process-definition-not-applicant-eform${searchString}`, { headers });
  }

  getListProcessDefinitionDifferentStandard(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.bpm + '/process-definition' + `/--get-list-process-definition-different-standard${searchString}`, { headers });
  }

  getListProcedureNotProcedureForm(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.category + '/procedure' + `/--get-list-procedure-not-procedure-form${searchString}`, { headers });
  }

  getListProcedurePublic(listAgency): Observable<any> {
    let headers = new HttpHeaders();
    const searchString = "?agency-id="+listAgency;
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.procedurePath +`/--find-all-public${searchString}`, { headers });
  }

  getListProcedurePublicKGG(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.procedurePath +`/--find-all-public-kgg${searchString}`, { headers });
  }

  getListUserKtm(searchString): Observable<any> {
    let headers = new HttpHeaders();
  //  let header = "http://localhost:8081/user";
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.user + '/--report-user' + searchString, { headers });
  }

  exportListUserKtm(searchString): void{
    this.getlistExport(searchString).subscribe((response: Blob) => {
      const filename = 'thongketaikhoan.xlsx';
      saveAs(response, filename);
    });
  }

  getlistExport(searchString): Observable<any>{
   // const URL = "http://localhost:8081/user" + '/--report-user-export'
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get( this.user  + '/--report-user-export' + searchString, { headers, responseType: 'blob' })
  }
}
