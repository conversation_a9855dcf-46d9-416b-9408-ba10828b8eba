import { Injectable } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { ApiProviderService } from 'src/app/core/service/api-provider.service';
import { Observable } from 'rxjs';
import { ErrorComponent, ErrorDialogModel } from 'src/app/shared/components/dialogs/error/error.component';
import { AlertComponent, AlertDialogModel } from 'src/app/shared/components/dialogs/alert/alert.component';
import { LoadingDialogComponent, LoadingDialogModel } from 'src/app/shared/components/dialogs/loading-dialog/loading-dialog.component';

@Injectable({
  providedIn: 'root'
})
export class HomeService {
  constructor(
    private dialog: MatDialog,
    private http: HttpClient,
    private apiProviderService: ApiProviderService
  ) { }

  private getProcedureURL = this.apiProviderService.getUrl('digo', 'basepad');
  private basedataURL = this.apiProviderService.getUrl('digo', 'basedata');
  private tagPath = this.apiProviderService.getUrl('digo', 'basecat') + '/tag/';
  private frequentURL = this.apiProviderService.getUrl('digo', 'reporter') + '/procedure/--frequent';

  error(vi, en) {
    const dialogData = new ErrorDialogModel(vi, en);
    const dialogRef = this.dialog.open(ErrorComponent, {
      width: '500px',
      data: dialogData,
      disableClose: false,
      autoFocus: false
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
      // console.log(dialogResult);
    });
  }

  alert(vi, en, icon, reload, routerLink, queryParams, timer) {
    const dialogData = new AlertDialogModel(vi, en, icon, reload, routerLink, queryParams, timer);
    const dialogRef = this.dialog.open(AlertComponent, {
      width: '500px',
      data: dialogData,
      disableClose: false,
      autoFocus: false
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
      // console.log(dialogResult);
    });
  }

  getListProcedureLevel(): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.getProcedureURL + '/procedure-level', { headers }).pipe();
  }

  getListTagByCategoryId(id: string, page: number, size: number, sort: string, type): Observable<any> {
    let headers = new HttpHeaders();
    switch (type) {
      case 0:
        const token = localStorage.getItem('OAuth2TOKEN');
        headers = headers.append('Authorization', 'Bearer ' + token);
        headers = headers.append('Content-Type', 'application/json');
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        headers.append('Access-Control-Allow-Origin', '*');
        // tslint:disable-next-line: max-line-length
        return this.http.get(this.tagPath + '--by-category-id?category-id=' + id + '&page=' + page + '&size=' + size + '&sort=' + sort, { headers }).pipe();
      case 1:
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        // tslint:disable-next-line: max-line-length
        return this.http.get(this.tagPath + '--by-category-id?category-id=' + id + '&page=' + page + '&size=' + size + '&sort=' + sort, { headers }).pipe();
    }
  }

  getListSector(type): Observable<any> {
    let headers = new HttpHeaders();
    switch (type) {
      case 0:
        const token = localStorage.getItem('OAuth2TOKEN');
        headers = headers.append('Authorization', 'Bearer ' + token);
        headers = headers.append('Content-Type', 'application/json');
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        headers.append('Access-Control-Allow-Origin', '*');
        return this.http.get(this.getProcedureURL + '/sector', { headers }).pipe();
      case 1:
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        return this.http.get(this.getProcedureURL + '/sector', { headers }).pipe();
    }
  }

  getListAgencyLevel(): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.basedataURL + '/agency-level?sort=id,asc', { headers }).pipe();
  }

  getListProcedure(searchString, type, pageSize): Observable<any> {
    let headers = new HttpHeaders();
    switch (type) {
      case 0:
        const token = localStorage.getItem('OAuth2TOKEN');
        headers = headers.append('Authorization', 'Bearer ' + token);
        headers = headers.append('Content-Type', 'application/json');
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        headers.append('Access-Control-Allow-Origin', '*');
        return this.http.get(this.getProcedureURL + '/procedure' + searchString + '&page=0', { headers }).pipe();
      case 1:
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        return this.http.get(this.getProcedureURL + '/procedure' + searchString + '&page=0', { headers }).pipe();
    }
  }

  getListProvince(nation, parent, type): Observable<any> {
    let headers = new HttpHeaders();
    switch (type) {
      case 0:
        const token = localStorage.getItem('OAuth2TOKEN');
        headers = headers.append('Authorization', 'Bearer ' + token);
        headers = headers.append('Content-Type', 'application/json');
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        headers.append('Access-Control-Allow-Origin', '*');
        return this.http.get(this.basedataURL + '/place?nation-id=' + nation + '&parent-type-id=' + parent, { headers }).pipe();
      case 1:
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        return this.http.get(this.basedataURL + '/place?nation-id=' + nation + '&parent-type-id=' + parent, { headers }).pipe();
    }
  }

  getListProcedureFrequent(searchString, type): Observable<any> {
    let headers = new HttpHeaders();
    switch (type) {
      case 0:
        const token = localStorage.getItem('OAuth2TOKEN');
        headers = headers.append('Authorization', 'Bearer ' + token);
        headers = headers.append('Content-Type', 'application/json');
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        headers.append('Access-Control-Allow-Origin', '*');
        return this.http.get(this.frequentURL + searchString, { headers }).pipe();
      case 1:
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        return this.http.get(this.frequentURL + searchString, { headers }).pipe();
    }
  }

  getListAgency(searchString, type): Observable<any> {
    let headers = new HttpHeaders();
    switch (type) {
      case 0:
        const token = localStorage.getItem('OAuth2TOKEN');
        headers = headers.append('Authorization', 'Bearer ' + token);
        headers = headers.append('Content-Type', 'application/json');
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        headers.append('Access-Control-Allow-Origin', '*');
        return this.http.get(this.basedataURL + '/agency/name+logo-id' + searchString, { headers }).pipe();
      case 1:
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        return this.http.get(this.basedataURL + '/agency/name+logo-id' + searchString, { headers }).pipe();
    }
  }

  loading(vi, en) {
    const dialogData = new LoadingDialogModel(vi, en);
    const dialogRef = this.dialog.open(LoadingDialogComponent, {
      width: '500px',
      data: dialogData,
      disableClose: true,
      autoFocus: false
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
      // console.log(dialogResult);
    });
  }
}
