<form [formGroup]="productForm">
    <div>
        <button *ngIf="(checkId !='1') && isShowHousehold" fxFlex.gt-md="20" fxFlex='grow' type="submit" mat-flat-button (click)="checkInfoMember()">
            <span>Xác thực thông tin công dân</span>
        </button>
    </div>
    

    <div formArrayName="quantities">
        <!-- <h2>Thông tin công dân</h2> -->


        <div class="form-container">
                <!-- Lặp qua từng FormGroup trong FormArray -->
                <div *ngFor="let quantity of quantities().controls; let i = index" [formGroupName]="i" class="form-row-group">
                  <h4>Công dân {{ i + 1 }}</h4>
                  <div>
                    <div *ngIf="quantity.get('checkInfo')?.value == 2" class="messageFalse">
                        [CSDLQGvDC] Thông tin công dân xác thực không thành công.
                    </div>
                    <div *ngIf="quantity.get('checkInfo')?.value == 1" class="messageTrue">
                        [CSDLQGvDC] Thông tin công dân đã xác thực.
                    </div>
                </div>
                <!-- Hàng 1: Họ và tên, Ngày sinh, Số định danh/CCCD, Quan hệ -->
                  
                    <div fxLayout="row" fxLayout.xs="row" fxLayout.sm="row" class="formFieldItems" fxLayoutAlign="space-between">
                    <mat-form-field appearance="outline" fxFlex.gt-md="20" fxFlex.gt-sm="49.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                        <mat-label>
                            Họ và tên
                            <mat-icon *ngIf="quantity.get('checkInfo')?.value == 1"  style="color:green;">check_circle</mat-icon>
                            <mat-icon *ngIf="quantity.get('checkInfo')?.value == 2"  style="color:red;">cancel</mat-icon>
                        </mat-label>
                        <input matInput id="fullNameMember-{{ i }}" formControlName="fullNameMember" maxlength="500" [readonly] ="quantity.get('checkInfo')?.value == 1">
                    </mat-form-field>
                    <mat-form-field appearance="outline" fxFlex.gt-md="20" fxFlex.gt-sm="49.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                        <mat-label>Ngày sinh
                            <mat-icon *ngIf="quantity.get('checkInfo')?.value == 1"  style="color:green;">check_circle </mat-icon>
                            <mat-icon *ngIf="quantity.get('checkInfo')?.value == 2"  style="color:red;">cancel</mat-icon>
                        </mat-label>
                        <input matInput id="birthDayMember-{{ i }}" [matDatepicker]="pickerAcceptTo" formControlName="birthDayMember" maxlength="500" [readonly] ="quantity.get('checkInfo')?.value == 1">
                        <mat-datepicker-toggle matSuffix [for]="pickerAcceptTo"></mat-datepicker-toggle>
                        <mat-datepicker #pickerAcceptTo><mat-icon>image_search</mat-icon></mat-datepicker>
                    </mat-form-field>
                    <mat-form-field appearance="outline" fxFlex.gt-md="20" fxFlex.gt-sm="49.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                        <mat-label>Số CMND
                            <mat-icon *ngIf="quantity.get('checkInfo')?.value == 1"  style="color:green;">check_circle</mat-icon>
                            <mat-icon *ngIf="quantity.get('checkInfo')?.value == 2"  style="color:red;">cancel</mat-icon>
                        </mat-label>
                        <input matInput id="cmndMember-{{ i }}" formControlName="cmndMember" maxlength="500" [readonly] ="checkIdentity?.quantities[i]?.check && checkIdentity.quantities[i].check === 'YES'" (change)="isDuplicate()" >
                        <div *ngIf="duplicateError" class="error-message">
                            Số CMND đã bị trùng. Vui lòng nhập số khác.
                          </div>
                    </mat-form-field>
                    <mat-form-field appearance="outline" fxFlex.gt-md="20" fxFlex.gt-sm="49.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                        <mat-label>Số định danh/CCCD
                            <mat-icon *ngIf="quantity.get('checkInfo')?.value == 1"  style="color:green;">check_circle</mat-icon>
                            <mat-icon *ngIf="quantity.get('checkInfo')?.value == 2"  style="color:red;">cancel</mat-icon>
                        </mat-label>
                        <input matInput id="identityMember-{{ i }}" formControlName="identityMember" maxlength="500" [readonly] ="checkIdentity?.quantities[i]?.check && checkIdentity.quantities[i].check === 'YES'" (change)="isDuplicate()" >
                        <div *ngIf="duplicateError" class="error-message">
                            Số định danh/CCCD đã bị trùng. Vui lòng nhập số khác.
                          </div>
                    </mat-form-field>
                </div>
                  <!-- Hàng 2: Thường trú -->
                  <div *ngIf="quantity.get('checkInfo')?.value == 1" class="form-row">
                    <div class="form-column-full">
                        <mat-form-field appearance="outline" fxFlex.gt-sm="32" fxFlex.gt-xs="32" fxFlex='grow'>
                            <mat-label>Thường trú
                                <mat-icon *ngIf="quantity.get('checkInfo')?.value == 1"  style="color:green;">check_circle</mat-icon>
                                <mat-icon *ngIf="quantity.get('checkInfo')?.value == 2"  style="color:red;">cancel</mat-icon>
                            </mat-label>
                            <input matInput id="permanentAddressMember-{{ i }}" formControlName="permanentAddressMember" maxlength="1500" [readonly] ="quantity.get('checkInfo')?.value == 1">
                        </mat-form-field>
                    </div>
                  </div>
          
                  <!-- Hàng 3: Nơi ở hiện tại -->
                  <div *ngIf="quantity.get('checkInfo')?.value == 1" class="form-row">
                    <div class="form-column-full">
                        <mat-form-field appearance="outline" fxFlex.gt-sm="32" fxFlex.gt-xs="32" fxFlex='grow'>
                            <mat-label>Nơi ở hiện tại
                                <mat-icon *ngIf="quantity.get('checkInfo')?.value == 1"  style="color:green;">check_circle</mat-icon>
                                <mat-icon *ngIf="quantity.get('checkInfo')?.value == 2"  style="color:red;">cancel</mat-icon>
                            </mat-label>
                            <input matInput id="currentAddressMember-{{ i }}" formControlName="currentAddressMember" maxlength="1500" [readonly] ="quantity.get('checkInfo')?.value == 1">
                        </mat-form-field>
                    </div>
                  </div>
          
                </div>
          </div>



    </div>

</form>


<div *ngIf="checkId !='1'" fxLayout="row">
    <button fxFlex.gt-md="19.5" fxFlex='grow' type="button" mat-flat-button (click)="addQuantity()">
        <mat-icon>add</mat-icon>
        <span>Thêm dòng</span>
    </button>
</div>