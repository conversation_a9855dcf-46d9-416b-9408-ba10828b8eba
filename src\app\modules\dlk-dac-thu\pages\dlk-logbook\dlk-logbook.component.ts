import { DatePipe } from '@angular/common';
import { AfterViewInit, ChangeDetectorRef, Component, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { MatSelect } from '@angular/material/select';
import { MatTableDataSource } from '@angular/material/table';
import { Workbook } from 'exceljs';
import * as fs from 'file-saver';
import { Subject } from 'rxjs';
import { EnvService } from 'src/app/core/service/env.service';
import { DLKStatisticService } from 'src/app/data/service/dlk-dac-thu/dlk-statistic.service';
import { MainService } from 'src/app/data/service/main/main.service';
import { ProcedureService } from 'src/app/data/service/procedure/procedure.service';
import { SnackbarService } from 'src/app/data/service/snackbar/snackbar.service';
import * as tUtils from 'src/app/data/service/thoai.service';

const EXCEL_TYPE = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8';
const EXCEL_EXTENSION = '.xlsx';
export interface Agency {
  id: string;
  name: string;
}

@Component({
  selector: 'app-dlk-logbook',
  templateUrl: './dlk-logbook.component.html',
  styleUrls: [
    './dlk-logbook.component.scss',
    '/src/app/app.component.scss',
    '/src/app/shared/scss/form-field-outline.scss'
  ]
})

export class DlkLogbookComponent implements OnInit, AfterViewInit, OnDestroy {

  nowDate = tUtils.newDate();
  // tslint:disable-next-line: max-line-length
  toDate = this.nowDate.getFullYear() + '-' + ((this.nowDate.getMonth() + 1) <= 9 ? '0' + (this.nowDate.getMonth() + 1) : (this.nowDate.getMonth() + 1)) + '-' + ((this.nowDate.getDate() <= 9) ? ('0' + this.nowDate.getDate()) : this.nowDate.getDate()) + "T16:59:59.999Z";
  // tslint:disable-next-line: max-line-length
  fromDate = this.nowDate.getFullYear() + '-' + ((this.nowDate.getMonth() + 1) <= 9 ? '0' + (this.nowDate.getMonth() + 1) : (this.nowDate.getMonth() + 1)) + '-01T00:00:00.000Z';

  keyword = '';
  config = this.envService.getConfig();
  selectedLang = localStorage.getItem('language') || 'vi';
  selectedLangId = Number(localStorage.getItem('languageId')) || 228;
  pageTitle = {
    vi: 'Thống kê sổ theo dõi',
    en: 'Logbook statistics'
  };

  protected onDestroy = new Subject<void>();
  // =================== old
  //env: any = this.deploymentService.getAppDeployment()?.env;
  ELEMENTDATA: any[] = [];
  dataSource: MatTableDataSource<any>;
  // listSector: Array<any> = [];
  keySearchSectorAgency = '';
  // Search Sector
  keywordSector = '';
  totalPagesSector = 0;
  currentPageSector = 0;
  pageSizeSector = 100;

  parentAgency = '';
  agencyId = '';
  userAgency?: any = JSON.parse(localStorage.getItem('userAgency'));
  userAgencyCount?: any = JSON.parse(localStorage.getItem('listAgencyUser'))?.length || 0;
  ELEMENTDATAALL: any[] = [];
  dataSourceAll: MatTableDataSource<any>;
  listHoSo = [];
  listExport = [];

  size = 10;
  pageIndex = 1;
  page = 1;
  countResult = 0;
  pgSizeOptions = this.config.pageSizeOptions;
  paramsQuery = {
    page: 0,
    size: 10,
    agency: '',
    parentAgency: '',
    fromDate: '',
    toDate: '',
    applyMethod: "-1",
    receivingKind: '',
    sector: '',
    keyword: '',
    procedure: '',
    sortId: '',
    isTTPVHCC: 1,
    all: 0,
    agencyName: '',
    childAgencyList: []
  };

  listAgencyAccept = [];
  listAgency = [];
  childAgencyList = [];
  keywordAgencyAccept = '';
  totalPagesAgencyAccept = 0;
  currentPageAgencyAccept = 0;
  pageSizeAgencyAccept = 1000;
  startDate = new Date();
  endDate = new Date();
  flagTTPVHCC = true;
  timeOutAgencyAccept: any = null;
  listSectorfillter: any[] = [];
  listSector: any[] = [];
  listProcedure: any[] = [];
  listProcedurefillter: any[] = [];
  totalPagesProcedure = 0;
  currentPageProcedure = 0;
  searchProcedureKeyword = "";
  listHinhThucNhan: any[] = [];
  waitingDownloadExcel = false;
  @ViewChild('procedureMatSelectInfiniteScroll', { static: true }) procedureMatSelectInfiniteScroll: MatSelect;


  constructor(
    private envService: EnvService,
    public datepipe: DatePipe,
    private mainService: MainService,
    private cdRef: ChangeDetectorRef,
    private procedureService: ProcedureService,
    private datePipe: DatePipe,
    private snackbarService: SnackbarService,
    private dlkStatisticService: DLKStatisticService
  ) {
    this.dataSource = new MatTableDataSource(this.ELEMENTDATA);
    this.dataSourceAll = new MatTableDataSource(this.ELEMENTDATAALL);
  }
  ngOnInit(): void {
    this.mainService.setPageTitle(this.pageTitle[localStorage.getItem('language')]);
    this.startDate = new Date(this.fromDate);
    this.endDate = new Date(this.toDate);
    if (this.userAgency !== null && this.userAgency !== undefined) {
      if (!!this.userAgency.parent && !!this.userAgency.parent.id && this.userAgencyCount === 1) {
        this.parentAgency = this.userAgency.parent.id;
        this.agencyId = this.userAgency.id;
        this.paramsQuery.agencyName = this.userAgency.parent.name;
      } else {
        this.parentAgency = this.userAgency.id;
        this.agencyId = this.userAgency.id;
        this.paramsQuery.agencyName = this.userAgency.name;
      }

      this.keySearchSectorAgency = '&agency-id=' + this.agencyId;
    }

    this.paramsQuery.parentAgency = this.parentAgency;
    this.getListAgencyAccept(this.keywordAgencyAccept, this.currentPageAgencyAccept, this.pageSizeAgencyAccept);
    this.getListSector();
    this.getListHinhThucNhan();
  }

  ngAfterViewInit() {
    this.cdRef.detectChanges();
    setTimeout(() => {
      // console.clear();
      //this.autoSearch();
      // this.mainService.sideNav.close();
    }, 1000);
  }

  ngOnDestroy() {
    this.onDestroy.next();
    this.onDestroy.complete();
  }

  thongKe() {
    this.paramsQuery.page = 0;
    this.page = 1;
    this.paramsQuery.all = 0;
    this.getListHoSo();
  }

  paginate(event) {
    this.paramsQuery.page = event;
    this.paramsQuery.all = 0;
    this.getListHoSo();
  }

  getListHoSo() {
    this.paramsQuery.fromDate = (this.startDate ? this.datePipe.transform(this.startDate, 'dd/MM/yyyy') + 'T00:00:00.000Z' : '');
    this.paramsQuery.toDate = (this.endDate ? this.datePipe.transform(this.endDate, 'dd/MM/yyyy') + 'T23:59:59.999Z' : '');

    // Kiểm tra validate
    let data = this.validateForm();
    if (data == null || !data?.status) {
      this.snackbarService.openSnackBar(0, "Không hợp lệ", data.errMessage, 'error_notification', this.config.expiredTime);
      this.listHoSo = [];
      this.countResult = 0;
      return;
    }

    this.dlkStatisticService.getDossierTheoDoi(this.paramsQuery).subscribe(res => {
      this.listHoSo = res.content;
      this.countResult = res.totalElements;
    }, err => {
      console.log(err);
    });
  }
  getListHinhThucNhan() {
    // tslint:disable-next-line:max-line-length
    this.dlkStatisticService.getHinhThucNhan().subscribe(res => {
      this.listHinhThucNhan = res.content;

    }, err => {
      console.log(err);
    });
  }

  getAgencyScroll() {
    this.currentPageAgencyAccept += 1;
    this.getListAgencyAccept(this.keywordAgencyAccept, this.currentPageAgencyAccept, this.pageSizeAgencyAccept);
  }

  getListAgencyAccept(keyword, page, size) {
    const searchString = '?parent-id=' + this.parentAgency + '&keyword=' + keyword + '&page=' + page + '&size=' + size + '&sort=name.name,asc&status=1';

    this.procedureService.getListAgencyWithParent(searchString).subscribe(res => {
      if (page === 0) {
        this.listAgency = res.content;

      } else {
        this.listAgency = this.listAgency.concat(res.content);
      }
      this.listAgencyAccept = this.listAgency;
      this.totalPagesAgencyAccept = res.totalPages;

      if (this.listAgency != null && this.listAgency.length > 0) {
        this.listAgencyAccept = this.listAgency;
        this.listAgency.forEach(item => {
          if (item != null && item.id != null) {
            this.paramsQuery.childAgencyList.push(item.id);
            this.childAgencyList.push(item.id);
          }
        })
      }
    }, err => {
      console.log(err);
    });
  }

  searchAngency(event) {
    if (event != "") {
      this.listAgencyAccept = this.listAgency.filter(agency =>
        agency.name.toLowerCase().trim().includes(event.toLowerCase()) == true);
    } else {
      this.listAgencyAccept = this.listAgency;
    }
  }

  clickTTPVHCC() {
    this.paramsQuery.isTTPVHCC = this.flagTTPVHCC ? 1 : 0;
  }

  onEnter(event) {
    clearTimeout(this.timeOutAgencyAccept);
    this.timeOutAgencyAccept = setTimeout(async () => {
      this.keywordAgencyAccept = event.target.value;
      this.currentPageAgencyAccept = 0;
      this.getListAgencyAccept(this.keywordAgencyAccept, this.currentPageAgencyAccept, this.pageSizeAgencyAccept);
      this.keywordSector = '';
      this.totalPagesSector = 0;
      this.currentPageSector = 0;
      this.pageSizeSector = 100;
      this.listSector = [];
      this.getListSector();
    }, 300);
  }

  async resetSearchForm(type) {
    switch (type) {
      case 'agencyAccept': {
        this.currentPageAgencyAccept = 0;
        this.keywordAgencyAccept = '';
        this.getListAgencyAccept(this.keywordAgencyAccept, this.currentPageAgencyAccept, this.pageSizeAgencyAccept);

        this.keywordSector = '';
        this.totalPagesSector = 0;
        this.currentPageSector = 0;
        this.pageSizeSector = 100;
        break;
      }
    }
  }

  changeAgencyAccept() {
    //const formObject = this.searchForm.getRawValue();
    this.paramsQuery.sector = "";
    if (this.paramsQuery.agency !== '') {
      this.paramsQuery.childAgencyList = [];
      this.keySearchSectorAgency = '&agency-id=' + this.paramsQuery.agency;
    } else {
      if (this.parentAgency !== '') {
        this.paramsQuery.childAgencyList = this.childAgencyList;
        if (this.parentAgency !== '') {
          this.keySearchSectorAgency = '&agency-id=' + this.parentAgency;
        }
      }
      else {
        this.keySearchSectorAgency = '';
      }
    }

    this.keywordSector = '';
    this.totalPagesSector = 0;
    this.currentPageSector = 0;
    this.pageSizeSector = 100;
    this.listSector = [];
    this.getListSector();
  }

  getListSector() {
    // tslint:disable-next-line:max-line-length
    const searchString = '?keyword=' + this.keywordSector + '&page=' + this.currentPageSector + '&size=' + this.pageSizeSector + '&spec=page&sort=name.name,asc&status=1&only-agency-id=1' + this.keySearchSectorAgency;
    this.procedureService.getListSector(searchString).subscribe(res => {
      if (this.currentPageSector === 0) {
        this.listSector = res.content;
      } else {
        this.listSector = this.listSector.concat(res.content);
      }
      this.totalPagesSector = res.totalPages;
      this.listSectorfillter = this.listSector;
    }, err => {
      console.log(err);
    });
  }

  getListSectorScroll() {
    this.currentPageSector += 1;
    this.getListSector();
  }


  searchSector(event) {
    if (event != "") {
      this.listSectorfillter = this.listSector.filter(sector =>
        sector.name.toLowerCase().trim().includes(event.toLowerCase()) == true);

    } else {
      this.listSectorfillter = this.listSector;
    }
  }


  sectorChange() {
    this.listProcedure = [];
    this.listProcedurefillter = [];
    this.currentPageProcedure = 0;
    this.paramsQuery.procedure = "";
    this.getListProcedure();
  }

  getListProcedureScroll() {
    this.currentPageProcedure += 1;
    this.getListProcedure();
  }
  getListProcedure() {
    const searchString =
      '?status=1&sort=translate.name,asc&keyword=' + this.searchProcedureKeyword +
      '&spec=page&page=' + this.currentPageProcedure + '&size=50' +
      '&sector-id=' + this.paramsQuery.sector;
    this.procedureService.getListProcedure(searchString).subscribe(data => {
      if (this.currentPageProcedure == 0) {
        this.listProcedure = data.content;
      } else {
        this.listProcedure = this.listProcedure.concat(data.content);
      }
      this.totalPagesProcedure = data.totalPages;
      this.listProcedurefillter = this.listProcedure;
    }, err => {
      console.log(err);
    });
  }

  searchProvedure(event) {
    if (event != "") {
      this.searchProcedureKeyword = event;

      // this.listProcedurefillter = this.listProcedure.filter(pro =>
      //   pro.name.toLowerCase().trim().includes(event.toLowerCase()) == true);
    } else {
      this.searchProcedureKeyword = "";
      // this.currentPageProcedure = 0;
      // this.getListProcedure();
      //this.listProcedurefillter = this.listProcedure;
    }
    this.currentPageProcedure = 0;
    this.getListProcedure();
  }

  generateAddress(data) {
    // return this.mainService.generateAddress(placeObj);
    const address = [];
    if (data?.address !== undefined && data?.address !== null) {
      address.push(data.address);
    }
    if (data?.village !== undefined && data?.village !== null) {
      data.village?.label ? address.push(data.village.label) : "";
    }
    if (data?.district !== undefined && data?.district !== null) {
      data.district?.label ? address.push(data.district.label) : "";
    }
    if (data?.province !== undefined && data?.province !== null) {
      data.province?.label ? address.push(data.province.label) : "";
    }

    return address.join(', ');
  }

  getFullName(data) {
    if (data != null) {
      if (data?.fullName != undefined && data?.fullName != null && data?.fullName != "") {
        return data.fullName;
      } else if (data?.fullname != undefined && data?.fullname != null && data?.fullname != "") {
        return data.fullname;
      } else if (data?.ownerFullname != undefined && data?.ownerFullname != null && data?.ownerFullname != "") {
        return data.ownerFullname;
      } else {
        return "";
      }
    }
  }

  getTienDo(data) {
    var ngayHoanThanh = this.nowDate;
    var ngayHenTra = this.nowDate;
    var finish = false;
    if (data.appointmentDate != undefined && data.appointmentDate != "") {
      ngayHenTra = new Date(data.appointmentDate);
    } else {
      return "Còn hạn";
    }
    if (data.completedDate != undefined && data.completedDate != "") {
      ngayHoanThanh = new Date(data.completedDate);
      finish = true;
    }

    var result = ngayHoanThanh.getTime() - ngayHenTra.getTime();
    if (result < 0) {
      if (finish) {
        return "Đúng hạn";
      } else {
        return "Còn hạn";
      }
    } else {
      return "Quá hạn";
    }
  }

  getTen(data) {
    if (data != null && data?.length > 0) {
      var ten = data[0].name;
      data.forEach(element => {
        if (element.languageId == 228) {
          ten = element.name;
        }
      });
      return ten;
    } else {
      return "";
    }
  }

  xuatExcel() {
    this.paramsQuery.fromDate = (this.startDate ? this.datePipe.transform(this.startDate, 'dd/MM/yyyy') + 'T00:00:00.000Z' : '');
    this.paramsQuery.toDate = (this.endDate ? this.datePipe.transform(this.endDate, 'dd/MM/yyyy') + 'T23:59:59.999Z' : '');

    // Kiểm tra validate
    let data = this.validateForm();
    if (data == null || !data?.status) {
      this.snackbarService.openSnackBar(0, "Không hợp lệ", data.errMessage, 'error_notification', this.config.expiredTime);
      this.listHoSo = [];
      this.countResult = 0;
      return;
    }
    // this.paramsQuery.fromDate = (this.startDate ? this.datePipe.transform(this.startDate, 'dd/MM/yyyy') : '');
    // this.paramsQuery.toDate = (this.endDate ? this.datePipe.transform(this.endDate, 'dd/MM/yyyy') : '');
    this.waitingDownloadExcel = true;
    //this.dlkStatisticService.exportLogbookExcel();

    this.paramsQuery.all = 1;
    this.dlkStatisticService.getDossierTheoDoi(this.paramsQuery).subscribe(res => {
      this.listExport = res.content;
      this.exportLogbookExcel(this.listExport);
      this.waitingDownloadExcel = false;
    }, err => {
      console.log(err);
      this.waitingDownloadExcel = false;
    });
  }

  public exportLogbookExcel(listExport) {
    // create workbook and worksheet
    const workbook = new Workbook();
    workbook.creator = 'Snippet Coder';
    workbook.lastModifiedBy = 'SnippetCoder';
    workbook.created = new Date();
    workbook.modified = new Date();
    const worksheet = workbook.addWorksheet("SỔ THEO DÕI HỒ SƠ");
    worksheet.properties.outlineLevelCol = 2;
    worksheet.properties.defaultRowHeight = 16;

    // Add header row
    worksheet.mergeCells('A1:C1');
    worksheet.getCell('A1').value = "UBND TỈNH ĐẮK LẮK";
    // tslint:disable-next-line: max-line-length
    worksheet.getCell('A1').style = { font: { bold: true, name: 'Times New Roman', size: 11 }, alignment: { horizontal: 'center', vertical: 'middle' } };
    // Add header row
    worksheet.mergeCells('A2:C2');
    worksheet.getCell('A2').value = this.paramsQuery.agencyName;
    // tslint:disable-next-line: max-line-length
    worksheet.getCell('A2').style = { font: { bold: true, name: 'Times New Roman', size: 11 }, alignment: { horizontal: 'center', vertical: 'middle' } };

    worksheet.mergeCells('F1:H1');
    worksheet.getCell('F1').value = "CỘNG HÒA XÃ HỘI CHỦ NGHĨA VIỆT NAM";
    // tslint:disable-next-line: max-line-length
    worksheet.getCell('F1').style = { font: { bold: true, name: 'Times New Roman', size: 11 }, alignment: { horizontal: 'center', vertical: 'middle' } };

    worksheet.mergeCells('F2:H2');
    worksheet.getCell('F2').value = "Độc lập - Tự do - Hạnh phúc";
    // tslint:disable-next-line: max-line-length
    worksheet.getCell('F2').style = { font: { bold: true, name: 'Times New Roman', size: 11 }, alignment: { horizontal: 'center', vertical: 'middle' } };

    worksheet.mergeCells('A4:H4');
    worksheet.getCell('A4').value = "SỔ THEO DÕI HỒ SƠ";
    // tslint:disable-next-line: max-line-length
    worksheet.getCell('A4').style = { font: { bold: true, name: 'Times New Roman', size: 16 }, alignment: { horizontal: 'center', vertical: 'middle' } };

    worksheet.mergeCells('A5:H5');
    worksheet.getCell('A5').value = "Từ ngày " + (this.paramsQuery.fromDate.split("T"))[0] + " tới ngày " + (this.paramsQuery.toDate.split("T"))[0];
    // tslint:disable-next-line: max-line-length
    worksheet.getCell('A5').style = { font: { bold: false, name: 'Times New Roman', size: 11 }, alignment: { horizontal: 'center', vertical: 'middle' } };

    worksheet.mergeCells("A7:A8");
    worksheet.getCell("A7").value = "STT";
    // tslint:disable-next-line: max-line-length
    worksheet.getCell("A7").style = { font: { bold: true, name: 'Times New Roman', size: 11 }, alignment: { horizontal: 'center', vertical: 'middle', wrapText: true }, fill: { type: 'pattern', pattern: 'darkVertical', fgColor: { argb: 'FFBFBFBF' }, bgColor: { argb: 'FFBFBFBF' } } };
    worksheet.getCell("A7").border = { top: { style: 'thin' }, left: { style: 'thin' }, right: { style: 'thin' } };
    worksheet.getCell("A8").border = { bottom: { style: 'thin' }, left: { style: 'thin' }, right: { style: 'thin' } };

    worksheet.mergeCells("B7:B8");
    worksheet.getCell("B7").value = "Số hồ sơ";
    // tslint:disable-next-line: max-line-length
    worksheet.getCell("B7").style = { font: { bold: true, name: 'Times New Roman', size: 11 }, alignment: { horizontal: 'center', vertical: 'middle', wrapText: true }, fill: { type: 'pattern', pattern: 'darkVertical', fgColor: { argb: 'FFBFBFBF' }, bgColor: { argb: 'FFBFBFBF' } } };
    worksheet.getCell("B7").border = { top: { style: 'thin' }, left: { style: 'thin' }, right: { style: 'thin' } };
    worksheet.getCell("B8").border = { bottom: { style: 'thin' }, left: { style: 'thin' }, right: { style: 'thin' } };

    worksheet.mergeCells("C7:C8");
    worksheet.getCell("C7").value = "Tên TTHC";
    // tslint:disable-next-line: max-line-length
    worksheet.getCell("C7").style = { font: { bold: true, name: 'Times New Roman', size: 11 }, alignment: { horizontal: 'center', vertical: 'middle', wrapText: true }, fill: { type: 'pattern', pattern: 'darkVertical', fgColor: { argb: 'FFBFBFBF' }, bgColor: { argb: 'FFBFBFBF' } } };
    worksheet.getCell("C7").border = { top: { style: 'thin' }, left: { style: 'thin' }, right: { style: 'thin' } };
    worksheet.getCell("C8").border = { bottom: { style: 'thin' }, left: { style: 'thin' }, right: { style: 'thin' } };

    worksheet.mergeCells("D7:D8");
    worksheet.getCell("D7").value = "Tên cá nhân tổ chức";
    // tslint:disable-next-line: max-line-length
    worksheet.getCell("D7").style = { font: { bold: true, name: 'Times New Roman', size: 11 }, alignment: { horizontal: 'center', vertical: 'middle', wrapText: true }, fill: { type: 'pattern', pattern: 'darkVertical', fgColor: { argb: 'FFBFBFBF' }, bgColor: { argb: 'FFBFBFBF' } } };
    worksheet.getCell("D7").border = { top: { style: 'thin' }, left: { style: 'thin' }, right: { style: 'thin' } };
    worksheet.getCell("D8").border = { bottom: { style: 'thin' }, left: { style: 'thin' }, right: { style: 'thin' } };

    worksheet.mergeCells("E7:E8");
    worksheet.getCell("E7").value = "Địa chỉ, số điện thoại";
    // tslint:disable-next-line: max-line-length
    worksheet.getCell("E7").style = { font: { bold: true, name: 'Times New Roman', size: 11 }, alignment: { horizontal: 'center', vertical: 'middle', wrapText: true }, fill: { type: 'pattern', pattern: 'darkVertical', fgColor: { argb: 'FFBFBFBF' }, bgColor: { argb: 'FFBFBFBF' } } };
    worksheet.getCell("E7").border = { top: { style: 'thin' }, left: { style: 'thin' }, right: { style: 'thin' } };
    worksheet.getCell("E8").border = { bottom: { style: 'thin' }, left: { style: 'thin' }, right: { style: 'thin' } };

    worksheet.mergeCells("F7:F8");
    worksheet.getCell("F7").value = "Cơ quan chủ trì giải quyết";
    // tslint:disable-next-line: max-line-length
    worksheet.getCell("F7").style = { font: { bold: true, name: 'Times New Roman', size: 11 }, alignment: { horizontal: 'center', vertical: 'middle', wrapText: true }, fill: { type: 'pattern', pattern: 'darkVertical', fgColor: { argb: 'FFBFBFBF' }, bgColor: { argb: 'FFBFBFBF' } } };
    worksheet.getCell("F7").border = { top: { style: 'thin' }, left: { style: 'thin' }, right: { style: 'thin' } };
    worksheet.getCell("F8").border = { bottom: { style: 'thin' }, left: { style: 'thin' }, right: { style: 'thin' } };

    worksheet.mergeCells("G7:M7");
    worksheet.getCell("G7").value = "Nhận và trả kết quả";
    // tslint:disable-next-line: max-line-length
    worksheet.getCell("G7").style = { font: { bold: true, name: 'Times New Roman', size: 11 }, alignment: { horizontal: 'center', vertical: 'middle', wrapText: true }, fill: { type: 'pattern', pattern: 'darkVertical', fgColor: { argb: 'FFBFBFBF' }, bgColor: { argb: 'FFBFBFBF' } } };
    worksheet.getCell("G7").border = { top: { style: 'thin' }, bottom: { style: 'thin' }, left: { style: 'thin' }, right: { style: 'thin' } };
    worksheet.getCell("H7").border = { top: { style: 'thin' }, bottom: { style: 'thin' } };
    worksheet.getCell("I7").border = { top: { style: 'thin' }, bottom: { style: 'thin' } };
    worksheet.getCell("J7").border = { top: { style: 'thin' }, bottom: { style: 'thin' } };
    worksheet.getCell("K7").border = { top: { style: 'thin' }, bottom: { style: 'thin' } };
    worksheet.getCell("L7").border = { top: { style: 'thin' }, bottom: { style: 'thin' } };
    worksheet.getCell("M7").border = { top: { style: 'thin' }, bottom: { style: 'thin' }, right: { style: 'thin' } };

    worksheet.getCell("G8").value = "Nhận hồ sơ";
    // tslint:disable-next-line: max-line-length
    worksheet.getCell("G8").style = { font: { bold: true, name: 'Times New Roman', size: 11 }, alignment: { horizontal: 'center', vertical: 'middle', wrapText: true }, fill: { type: 'pattern', pattern: 'darkVertical', fgColor: { argb: 'FFBFBFBF' }, bgColor: { argb: 'FFBFBFBF' } } };
    worksheet.getCell("G8").border = { top: { style: 'thin' }, bottom: { style: 'thin' }, left: { style: 'thin' }, right: { style: 'thin' } };

    worksheet.getCell("H8").value = "Hẹn trả kết quả";
    // tslint:disable-next-line: max-line-length
    worksheet.getCell("H8").style = { font: { bold: true, name: 'Times New Roman', size: 11 }, alignment: { horizontal: 'center', vertical: 'middle', wrapText: true }, fill: { type: 'pattern', pattern: 'darkVertical', fgColor: { argb: 'FFBFBFBF' }, bgColor: { argb: 'FFBFBFBF' } } };
    worksheet.getCell("H8").border = { top: { style: 'thin' }, bottom: { style: 'thin' }, left: { style: 'thin' }, right: { style: 'thin' } };

    worksheet.getCell("I8").value = "Ngày liên thông (VPUB)";
    // tslint:disable-next-line: max-line-length
    worksheet.getCell("I8").style = { font: { bold: true, name: 'Times New Roman', size: 11 }, alignment: { horizontal: 'center', vertical: 'middle', wrapText: true }, fill: { type: 'pattern', pattern: 'darkVertical', fgColor: { argb: 'FFBFBFBF' }, bgColor: { argb: 'FFBFBFBF' } } };
    worksheet.getCell("I8").border = { top: { style: 'thin' }, bottom: { style: 'thin' }, left: { style: 'thin' }, right: { style: 'thin' } };

    worksheet.getCell("J8").value = "Ngày có kết quả / YC trả lại dân";
    // tslint:disable-next-line: max-line-length
    worksheet.getCell("J8").style = { font: { bold: true, name: 'Times New Roman', size: 11 }, alignment: { horizontal: 'center', vertical: 'middle', wrapText: true }, fill: { type: 'pattern', pattern: 'darkVertical', fgColor: { argb: 'FFBFBFBF' }, bgColor: { argb: 'FFBFBFBF' } } };
    worksheet.getCell("J8").border = { top: { style: 'thin' }, bottom: { style: 'thin' }, left: { style: 'thin' }, right: { style: 'thin' } };

    worksheet.getCell("K8").value = "Ngày trả kết quả / trả lại dân";
    // tslint:disable-next-line: max-line-length
    worksheet.getCell("K8").style = { font: { bold: true, name: 'Times New Roman', size: 11 }, alignment: { horizontal: 'center', vertical: 'middle', wrapText: true }, fill: { type: 'pattern', pattern: 'darkVertical', fgColor: { argb: 'FFBFBFBF' }, bgColor: { argb: 'FFBFBFBF' } } };
    worksheet.getCell("K8").border = { top: { style: 'thin' }, bottom: { style: 'thin' }, left: { style: 'thin' }, right: { style: 'thin' } };

    worksheet.getCell("L8").value = "Phương thức nhận kết";
    // tslint:disable-next-line: max-line-length
    worksheet.getCell("L8").style = { font: { bold: true, name: 'Times New Roman', size: 11 }, alignment: { horizontal: 'center', vertical: 'middle', wrapText: true }, fill: { type: 'pattern', pattern: 'darkVertical', fgColor: { argb: 'FFBFBFBF' }, bgColor: { argb: 'FFBFBFBF' } } };
    worksheet.getCell("L8").border = { top: { style: 'thin' }, bottom: { style: 'thin' }, left: { style: 'thin' }, right: { style: 'thin' } };

    worksheet.getCell("M8").value = "Ký nhận";
    // tslint:disable-next-line: max-line-length
    worksheet.getCell("M8").style = { font: { bold: true, name: 'Times New Roman', size: 11 }, alignment: { horizontal: 'center', vertical: 'middle', wrapText: true }, fill: { type: 'pattern', pattern: 'darkVertical', fgColor: { argb: 'FFBFBFBF' }, bgColor: { argb: 'FFBFBFBF' } } };
    worksheet.getCell("M8").border = { top: { style: 'thin' }, bottom: { style: 'thin' }, left: { style: 'thin' }, right: { style: 'thin' } };

    worksheet.mergeCells("N7:N8");
    worksheet.getCell("N7").value = "Tiến độ thực hiện";
    // tslint:disable-next-line: max-line-length
    worksheet.getCell("N7").style = { font: { bold: true, name: 'Times New Roman', size: 11 }, alignment: { horizontal: 'center', vertical: 'middle', wrapText: true }, fill: { type: 'pattern', pattern: 'darkVertical', fgColor: { argb: 'FFBFBFBF' }, bgColor: { argb: 'FFBFBFBF' } } };
    worksheet.getCell("N7").border = { top: { style: 'thin' }, left: { style: 'thin' }, right: { style: 'thin' } };
    worksheet.getCell("N8").border = { bottom: { style: 'thin' }, left: { style: 'thin' }, right: { style: 'thin' } };

    worksheet.mergeCells("O7:O8");
    worksheet.getCell("O7").value = "Hình thức tiếp nhận";
    // tslint:disable-next-line: max-line-length
    worksheet.getCell("O7").style = { font: { bold: true, name: 'Times New Roman', size: 11 }, alignment: { horizontal: 'center', vertical: 'middle', wrapText: true }, fill: { type: 'pattern', pattern: 'darkVertical', fgColor: { argb: 'FFBFBFBF' }, bgColor: { argb: 'FFBFBFBF' } } };
    worksheet.getCell("O7").border = { top: { style: 'thin' }, left: { style: 'thin' }, right: { style: 'thin' } };
    worksheet.getCell("O8").border = { bottom: { style: 'thin' }, left: { style: 'thin' }, right: { style: 'thin' } };

    worksheet.mergeCells("P7:P8");
    worksheet.getCell("P7").value = "Ghi chú";
    // tslint:disable-next-line: max-line-length
    worksheet.getCell("P7").style = { font: { bold: true, name: 'Times New Roman', size: 11 }, alignment: { horizontal: 'center', vertical: 'middle', wrapText: true }, fill: { type: 'pattern', pattern: 'darkVertical', fgColor: { argb: 'FFBFBFBF' }, bgColor: { argb: 'FFBFBFBF' } } };
    worksheet.getCell("P7").border = { top: { style: 'thin' }, left: { style: 'thin' }, right: { style: 'thin' } };
    worksheet.getCell("P8").border = { bottom: { style: 'thin' }, left: { style: 'thin' }, right: { style: 'thin' } };

    for (var i = 1; i <= 16; i++) {
      worksheet.getCell(9, i).value = i;
      worksheet.getCell(9, i).style = { font: { bold: false, name: 'Times New Roman', size: 11 }, alignment: { horizontal: 'center', vertical: 'middle', wrapText: true } };
      worksheet.getCell(9, i).border = { top: { style: 'thin' }, bottom: { style: 'thin' }, left: { style: 'thin' }, right: { style: 'thin' } };
    }
    worksheet.getRow(9).height = 15;

    for (var i = 0; i < listExport.length; i++) {
      var cellA = "A" + (10 + i);
      worksheet.getCell(cellA).value = (i + 1);
      worksheet.getCell(cellA).style = { font: { bold: false, name: 'Times New Roman', size: 11 }, alignment: { horizontal: 'center', vertical: 'middle', wrapText: true } };
      worksheet.getCell(cellA).border = { top: { style: 'thin' }, bottom: { style: 'thin' }, left: { style: 'thin' }, right: { style: 'thin' } };

      var cellA = "B" + (10 + i);
      worksheet.getCell(cellA).value = listExport[i].code;
      worksheet.getCell(cellA).style = { font: { bold: false, name: 'Times New Roman', size: 11 }, alignment: { horizontal: 'center', vertical: 'middle', wrapText: true } };
      worksheet.getCell(cellA).border = { top: { style: 'thin' }, bottom: { style: 'thin' }, left: { style: 'thin' }, right: { style: 'thin' } };

      var cellA = "C" + (10 + i);
      worksheet.getCell(cellA).value = listExport[i]?.procedureCode + " - " + listExport[i]?.procedureName;
      worksheet.getCell(cellA).style = { font: { bold: false, name: 'Times New Roman', size: 11 }, alignment: { horizontal: 'center', vertical: 'middle', wrapText: true } };
      worksheet.getCell(cellA).border = { top: { style: 'thin' }, bottom: { style: 'thin' }, left: { style: 'thin' }, right: { style: 'thin' } };

      var cellA = "D" + (10 + i);
      worksheet.getCell(cellA).value = this.getFullName(listExport[i].applicant?.data) ?? "";
      worksheet.getCell(cellA).style = { font: { bold: false, name: 'Times New Roman', size: 11 }, alignment: { horizontal: 'center', vertical: 'middle', wrapText: true } };
      worksheet.getCell(cellA).border = { top: { style: 'thin' }, bottom: { style: 'thin' }, left: { style: 'thin' }, right: { style: 'thin' } };

      var cellA = "E" + (10 + i);
      worksheet.getCell(cellA).value = this.generateAddress(listExport[i].applicant?.data) + "\n" + listExport[i].applicant?.data?.phoneNumber;
      worksheet.getCell(cellA).style = { font: { bold: false, name: 'Times New Roman', size: 11 }, alignment: { horizontal: 'center', vertical: 'middle', wrapText: true } };
      worksheet.getCell(cellA).border = { top: { style: 'thin' }, bottom: { style: 'thin' }, left: { style: 'thin' }, right: { style: 'thin' } };

      var cellA = "F" + (10 + i);
      worksheet.getCell(cellA).value = this.getTen(listExport[i].agency?.name);
      worksheet.getCell(cellA).style = { font: { bold: false, name: 'Times New Roman', size: 11 }, alignment: { horizontal: 'center', vertical: 'middle', wrapText: true } };
      worksheet.getCell(cellA).border = { top: { style: 'thin' }, bottom: { style: 'thin' }, left: { style: 'thin' }, right: { style: 'thin' } };

      var cellA = "G" + (10 + i);
      worksheet.getCell(cellA).value = this.datePipe.transform(listExport[i].acceptedDate, 'dd/MM/yyyy HH:mm:ss');
      worksheet.getCell(cellA).style = { font: { bold: false, name: 'Times New Roman', size: 11 }, alignment: { horizontal: 'center', vertical: 'middle', wrapText: true } };
      worksheet.getCell(cellA).border = { top: { style: 'thin' }, bottom: { style: 'thin' }, left: { style: 'thin' }, right: { style: 'thin' } };

      var cellA = "H" + (10 + i);
      worksheet.getCell(cellA).value = this.datePipe.transform(listExport[i].appointmentDate, 'dd/MM/yyyy HH:mm:ss');
      worksheet.getCell(cellA).style = { font: { bold: false, name: 'Times New Roman', size: 11 }, alignment: { horizontal: 'center', vertical: 'middle', wrapText: true } };
      worksheet.getCell(cellA).border = { top: { style: 'thin' }, bottom: { style: 'thin' }, left: { style: 'thin' }, right: { style: 'thin' } };

      var cellA = "I" + (10 + i);
      worksheet.getCell(cellA).value = this.datePipe.transform(listExport[i].acceptedDate, 'dd/MM/yyyy HH:mm:ss');
      worksheet.getCell(cellA).style = { font: { bold: false, name: 'Times New Roman', size: 11 }, alignment: { horizontal: 'center', vertical: 'middle', wrapText: true } };
      worksheet.getCell(cellA).border = { top: { style: 'thin' }, bottom: { style: 'thin' }, left: { style: 'thin' }, right: { style: 'thin' } };

      var cellA = "J" + (10 + i);
      worksheet.getCell(cellA).value = this.datePipe.transform(listExport[i].completedDate, 'dd/MM/yyyy HH:mm:ss');
      worksheet.getCell(cellA).style = { font: { bold: false, name: 'Times New Roman', size: 11 }, alignment: { horizontal: 'center', vertical: 'middle', wrapText: true } };
      worksheet.getCell(cellA).border = { top: { style: 'thin' }, bottom: { style: 'thin' }, left: { style: 'thin' }, right: { style: 'thin' } };

      var cellA = "K" + (10 + i);
      worksheet.getCell(cellA).value = this.datePipe.transform(listExport[i].returnedDate, 'dd/MM/yyyy HH:mm:ss');
      worksheet.getCell(cellA).style = { font: { bold: false, name: 'Times New Roman', size: 11 }, alignment: { horizontal: 'center', vertical: 'middle', wrapText: true } };
      worksheet.getCell(cellA).border = { top: { style: 'thin' }, bottom: { style: 'thin' }, left: { style: 'thin' }, right: { style: 'thin' } };

      var cellA = "L" + (10 + i);
      worksheet.getCell(cellA).value = this.getTen(listExport[i].dossierReceivingKind?.name);
      worksheet.getCell(cellA).style = { font: { bold: false, name: 'Times New Roman', size: 11 }, alignment: { horizontal: 'center', vertical: 'middle', wrapText: true } };
      worksheet.getCell(cellA).border = { top: { style: 'thin' }, bottom: { style: 'thin' }, left: { style: 'thin' }, right: { style: 'thin' } };

      var cellA = "M" + (10 + i);
      worksheet.getCell(cellA).value = "";
      worksheet.getCell(cellA).style = { font: { bold: false, name: 'Times New Roman', size: 11 }, alignment: { horizontal: 'center', vertical: 'middle', wrapText: true } };
      worksheet.getCell(cellA).border = { top: { style: 'thin' }, bottom: { style: 'thin' }, left: { style: 'thin' }, right: { style: 'thin' } };

      var cellA = "N" + (10 + i);
      worksheet.getCell(cellA).value = this.getTienDo(listExport[i]);
      worksheet.getCell(cellA).style = { font: { bold: false, name: 'Times New Roman', size: 11 }, alignment: { horizontal: 'center', vertical: 'middle', wrapText: true } };
      worksheet.getCell(cellA).border = { top: { style: 'thin' }, bottom: { style: 'thin' }, left: { style: 'thin' }, right: { style: 'thin' } };

      var cellA = "O" + (10 + i);
      worksheet.getCell(cellA).value = listExport[i].applyMethod?.name;
      worksheet.getCell(cellA).style = { font: { bold: false, name: 'Times New Roman', size: 11 }, alignment: { horizontal: 'center', vertical: 'middle', wrapText: true } };
      worksheet.getCell(cellA).border = { top: { style: 'thin' }, bottom: { style: 'thin' }, left: { style: 'thin' }, right: { style: 'thin' } };

      var cellA = "P" + (10 + i);
      worksheet.getCell(cellA).value = this.getTen(listExport[i].dossierStatus?.note);
      worksheet.getCell(cellA).style = { font: { bold: false, name: 'Times New Roman', size: 11 }, alignment: { horizontal: 'center', vertical: 'middle', wrapText: true } };
      worksheet.getCell(cellA).border = { top: { style: 'thin' }, bottom: { style: 'thin' }, left: { style: 'thin' }, right: { style: 'thin' } };
    }

    worksheet.getColumn('A').width = 5;
    worksheet.getColumn('B').width = 30;
    worksheet.getColumn('C').width = 30;
    worksheet.getColumn('D').width = 30;
    worksheet.getColumn('E').width = 25;
    worksheet.getColumn('F').width = 25;
    worksheet.getColumn('G').width = 20;
    worksheet.getColumn('H').width = 20;
    worksheet.getColumn('I').width = 20;
    worksheet.getColumn('J').width = 25;
    worksheet.getColumn('K').width = 20;
    worksheet.getColumn('L').width = 20;
    worksheet.getColumn('M').width = 20;
    worksheet.getColumn('N').width = 20;
    worksheet.getColumn('O').width = 20;
    worksheet.getColumn('P').width = 20;


    var tenFile = this.datePipe.transform(this.nowDate, 'dd_MM') + "_sotheodoihoso";
    // Save Excel File
    workbook.xlsx.writeBuffer().then((data: ArrayBuffer) => {
      const blob = new Blob([data], { type: EXCEL_TYPE });
      fs.saveAs(blob, tenFile + EXCEL_EXTENSION);
    });
  }

  // Cập nhật validate date trong form tìm kiếm
  validateForm() {
    // Chuyển đổi lại thời gian startDate / endDate
    this.startDate.setHours(0, 0, 0, 0);
    this.endDate.setHours(23, 59, 59, 999);

    let startTime = this.startDate.getTime();
    let endTime = this.endDate.getTime();
    let language = localStorage.getItem('language');

    let data = {
      errMessage: "",
      status: false
    }

    if (this.startDate == null || this.endDate == null) {
      data.errMessage = language == 'vi' ? 'Vui lòng nhập đầy đủ thông tin ngày tra' : 'Please enter complete information for the lookup';
    } else if (startTime > endTime) {
      data.errMessage = language == 'vi' ? 'Vui lòng nhập thông tin từ ngày nhỏ hơn đến ngày' : 'Please enter information from date less than to date';
    } else {
      data.status = true;
    }
    return data;
  }
}

