import { stream } from "exceljs";

export interface DossierSearchElement {
    stt: number;
    currentTask: any;
    dossierStatus: any;
    enableDeleteConfig: any;
    appointmentDate: any;
    acceptedDate: any;
    dossierEndDate: Date;
    undefindedCompleteTime: any;
    endDate: any;
    checked: boolean;
    id: string;
    code: string;
    due: [any];
    taskDue: [any];
    processingTime: string;
    status: string;
    dossierTaskStatus: any;
    dossierMenuTaskRemind: any;
    agency: any;
    sector: any;
    agencyProcessing: any;
    // procedure:object;
    // applicant: object;
    // dossierStatus: object;
    // appliedDate: String;
    // appointmentDate: String;
    // completedDate: String;
    // returnedDate: String;
    // cancelledDate: String;
    isFFOTask: any;
}
