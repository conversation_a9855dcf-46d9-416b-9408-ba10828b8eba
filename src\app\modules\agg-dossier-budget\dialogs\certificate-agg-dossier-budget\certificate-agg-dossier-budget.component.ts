import { Component, OnInit, Inject, ViewChild } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { AdapterService } from 'src/app/data/service/svc-adapter/adapter.service';
import { SnackbarService } from 'src/app/data/service/snackbar/snackbar.service';
import { EnvService } from 'src/app/core/service/env.service';
import { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';

@Component({
  selector: 'app-certificate-agg-dossier-budget',
  templateUrl: './certificate-agg-dossier-budget.component.html',
  styleUrls: ['./certificate-agg-dossier-budget.component.scss']
})
export class CertificateAggDossierBudgetComponent implements OnInit {
  config = this.envService.getConfig();
  ma = '';
  budgetDossierDetail: any;
  selectedLang = localStorage.getItem('language') || 'vi';
  selectedLangId = Number(localStorage.getItem('languageId')) || 228;
  pdfSrc: SafeResourceUrl | null = null;
  constructor(
    public dialogRef: MatDialogRef<CertificateAggDossierBudgetComponent>,
    @Inject(MAT_DIALOG_DATA) public data: CertificateAggDossierBudgetComponentDialog,
    private adapterService: AdapterService,
    private snackbarService: SnackbarService,
    private envService: EnvService,
    private sanitizer: DomSanitizer
  ) {
    this.ma = data.ma;
  }

  ngOnInit(): void {
    this.onDetail();
  }

  getSafePdfUrl(base64String: string): SafeResourceUrl {
    const binary = atob(base64String);
    const array = new Uint8Array(binary.length);
    for (let i = 0; i < binary.length; i++) {
      array[i] = binary.charCodeAt(i);
    }
    const blob = new Blob([array], { type: 'application/pdf' });
    const url = URL.createObjectURL(blob);
    return this.sanitizer.bypassSecurityTrustResourceUrl(url);
  }

  onDismiss() {
    this.dialogRef.close();
  }

  onConfirm() {
    this.dialogRef.close(true);
  }

  onDetail() {
    this.adapterService.getBudgetDossierCertificateAgg(this.ma).subscribe(data => {
      this.budgetDossierDetail = data;
  
      if (this.budgetDossierDetail?.data) {
        this.pdfSrc = this.getSafePdfUrl(this.budgetDossierDetail.data);
      }
    }, err => {
      let errorMsg = err.error?.message || 'Lỗi không xác định';
      if (errorMsg.includes('java.lang.NullPointerException')) {
        errorMsg = {
          vi: 'Không tìm thấy hồ sơ',
          en: 'Dossier not found'
        };
        this.snackbarService.openSnackBar(0, errorMsg[this.selectedLang], '', 'error_notification', this.config.expiredTime);
        return;
      }
      this.snackbarService.openSnackBar(0, errorMsg, '', 'error_notification', this.config.expiredTime);
    });
  }
}

export class CertificateAggDossierBudgetComponentDialog {
  constructor(
    public ma: string
    ) {
  }
}