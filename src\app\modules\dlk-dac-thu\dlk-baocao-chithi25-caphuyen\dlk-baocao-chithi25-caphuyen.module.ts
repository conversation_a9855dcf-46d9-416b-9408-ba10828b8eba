import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { DlkBaocaoChithi25CapHuyenRoutingModule } from './dlk-baocao-chithi25-caphuyen-routing.module';
import { DlkBaocaoChithi25CapHuyenComponent } from './dlk-baocao-chithi25-caphuyen.component';
import { SharedModule } from 'src/app/shared/shared.module';
import { NgxPrinterModule } from 'ngx-printer';
import {DossierDetailComponent} from '../dialogs/view-detail.component';


@NgModule({
  declarations: [DlkBaocaoChithi25CapHuyenComponent],
  imports: [
    CommonModule,
    DlkBaocaoChithi25CapHuyenRoutingModule,    
    SharedModule,
    
    
    NgxPrinterModule.forRoot({ printOpenWindow: true })
  ]
})
export class DlkBaocaoChithi25CapHuyenModule { }
