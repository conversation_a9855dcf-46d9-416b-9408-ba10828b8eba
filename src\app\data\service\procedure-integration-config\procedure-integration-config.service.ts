import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { ApiProviderService } from 'src/app/core/service/api-provider.service';

@Injectable({
  providedIn: 'root'
})
export class ProcedureIntegrationConfigService {

  constructor(
    private http: HttpClient,
    private apiProviderService: ApiProviderService
  ) { }

  private padmanUrl = this.apiProviderService.getUrl('digo', 'padman');

  getListProcedureConfigurationIntegration(requestBody):Observable<any>{
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    const URL = `${this.padmanUrl}/procedure-integration-config`+requestBody;
    return this.http.get(URL,{ headers });
  }

  getProcedureConfigurationIntegrationById(id):Observable<any>{
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    const URL = `${this.padmanUrl}/procedure-integration-config/`+id;
    return this.http.get(URL,{ headers });
  }

  postProcedureConfigurationIntegration(requestBody) {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    return this.http.post<any>(this.padmanUrl + '/procedure-integration-config', requestBody, { headers });
  }

  putProcedureConfigurationIntegration(id,requestBody) {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    return this.http.put<any>(this.padmanUrl + '/procedure-integration-config/'+id, requestBody, { headers });
  }
  
  deleteProcedureConfigurationIntegration(id: string) {
    let headers = new HttpHeaders();
    headers = headers.append('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.delete(this.padmanUrl + '/procedure-integration-config/' + id, { headers });
  }

}
