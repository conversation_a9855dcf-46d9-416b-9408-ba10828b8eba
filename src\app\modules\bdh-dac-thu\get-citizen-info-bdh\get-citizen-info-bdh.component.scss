button {
    margin-top: 3px;
    background-color: #ce7a58;
    height: 45px;
    font-size: 15px;
    color: #ffffff;
    font-weight: 500;
  
  }
  
  // .remove {
  //     margin-left: 10px;
  // }
  
  // .identityMember,
  // .birthDayMember {
  //     margin-left: 10px;
  // }
  .messageTrue {
  
    color:green;
    font-weight: bold;
  
  }
  .messageFalse{
    color:red;
    font-weight: bold;
  
  }
  .form-container {
    // max-width: 1000px;
    margin: 0 auto;
    padding: 20px;
    background: #f9f9f9;
    font-family: Arial, sans-serif;
    border: 1px solid #ccc;
    border-radius: 8px;
  }
  //   .error-message {
  //     color: red;
  //     font-weight: bold;
  //   }
  .form-row {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
    margin-bottom: 15px;
  }
  
  .form-column {
    display: flex;
    flex-direction: column;
  }
  
  .form-column-full {
    grid-column: span 4;
    display: flex;
    flex-direction: column;
  }
  
  //   label {
  //     font-weight: bold;
  //     margin-bottom: 5px;
  //   }
  
  .cell_info {
  color: blue;
  text-decoration: none;
  cursor: pointer;
  max-width: 100%;
  }
  
  .example-form-field {
  margin-right: 20px;
  }
  
  td.mat-footer-cell {
  text-align: center;
  }
  
  .hidden{
  display: none !important;
  }
  ::ng-deep .frm_searchbar .searchForm .mat-form-field-appearance-outline .mat-form-field-outline {
  color: transparent;
  }
  
  ::ng-deep .frm_searchbar .searchForm .mat-form-field-appearance-outline .mat-form-field-outline-thick {
  color: #dddddd;
  }
  
  ::ng-deep .frm_searchbar .searchForm .mat-form-field-appearance-outline .mat-form-field-outline {
  background-color: #eaebeb;
  border-radius: 5px;
  }
  
  
  ::ng-deep .frm_searchbar .mat-form-field-appearance-outline .mat-form-field-infix {
  padding: 0.8em 0;
  }
  
  ::ng-deep .frm_searchbar .mat-form-field-label-wrapper {
  top: -1em;
  }
  
  ::ng-deep .frm_searchbar .mat-form-field.mat-focused .mat-form-field-label {
  color: #ce7a58;
  font-size: 18px;
  margin-bottom: 1em;
  }
  
  ::ng-deep .frm_searchbar .mat-form-field-appearance-outline.mat-form-field-can-float.mat-form-field-should-float .mat-form-field-label {
  color: #ce7a58;
  font-size: 18px;
  margin-bottom: 1em;
  }
  // ================================= table + frm_tbl + tab 1
  ::ng-deep .frm_tbl0 table {
  width: 100%;
  margin-top: 1.5vh;
  }
  
  .data-label {
  word-wrap: break-word;
  }
  
  ::ng-deep .frm_tbl0 th.mat-header-cell, td.mat-cell, td.mat-footer-cell {
  text-align: center;
  border: 1px solid #CCC;
  padding: 0 !important;
  word-wrap: break-word;
  color: #495057;
  }
  
  ::ng-deep .frm_tbl0 .mat-header-row {
  background-color: #e8e8e8;
  word-wrap: break-word;
  // white-space: none;
  // white-space: normal;
  }
  
  ::ng-deep .frm_tbl0 .mat-header-row .mat-header-cell p {
  margin-bottom: 0;
  font-weight: 400;
  font-style: italic;
  word-wrap: break-word;
  }
  
  ::ng-deep .frm_tbl0 .mat-row:nth-child(even) {
  background-color: #FAFAFA;
  }
  
  ::ng-deep .frm_tbl0 .mat-row:nth-child(odd) {
  background-color: #fff;
  }
  
  tr.mat-footer-row {
  font-weight: bold;
  }
  .error-message {
    color: red;
    font-weight: 500;
    margin-top: 4px;
  }