import {AfterViewInit, Component, ElementRef, OnDestroy, OnInit, ViewChild, HostListener, Inject, LOCALE_ID, ChangeDetectionStrategy, ChangeDetectorRef} from '@angular/core';
import { FormGroup, FormControl, Validators, FormArray } from '@angular/forms';
import { DossierSearchElement } from 'src/app/data/schema/dossier-search-element';
import { MatTable, MatTableDataSource } from '@angular/material/table';
import { DossierService } from 'src/app/data/service/dossier/dossier.service';
import { ActivatedRoute, Router } from '@angular/router';
import { EnvService } from 'src/app/core/service/env.service';
import { MatAccordion } from '@angular/material/expansion';
import { DatePipe } from '@angular/common';
import { MainService } from 'src/app/data/service/main/main.service';
import { MatDialog } from '@angular/material/dialog';
import { SuspendComponent, SuspendModel } from 'src/app/modules/dossier/dialogs/suspend/suspend.component';
import { RefuseComponent, RefuseDialogModel } from 'src/app/modules/dossier/dialogs/refuse/refuse.component';
import { ResumeProcessingComponent, ResumeProcessingModel } from 'src/app/modules/dossier/dialogs/resume-processing/resume-processing.component';
import { SnackbarService } from 'src/app/data/service/snackbar/snackbar.service';
import { ProcedureService } from 'src/app/data/service/procedure/procedure.service';
import { AdminLayoutNavComponent } from 'src/app/layouts/admin/admin-layout-nav/admin-layout-nav.component';
import { ProcessHandleComponent, ProcessHandleDialogModel } from 'src/app/shared/components/process-handle/process-handle.component';
import { AdditionalRequirementComponent, ConfirmAdditionalRequirementDialogModel } from 'src/app/modules/dossier/dialogs/additional-requirement/additional-requirement.component';
import { DeleteDossierComponent, ConfirmDeleteDialogModel } from 'src/app/modules/dossier/pages/processing/dialogs/delete-dossier/delete-dossier.component';
import { AddApologyTextComponent, ConfirmAddApologyTextComponent } from '../../dialogs/add-apology-text/add-apology-text.component';
import { ConfirmSignApologyTextComponent, SignApologyTextComponent } from '../../dialogs/sign-apology-text/sign-apology-text.component';
import { DeleteMultiComponent, ConfirmDeleteMultiModel } from '../../dialogs/delete-multi/delete-multi.component';
import { WithdrawComponent, WithdrawModel } from 'src/app/modules/dossier/dialogs/withdraw/withdraw.component';
import { DeploymentService } from 'src/app/data/service/deployment.service';
import * as tUtils from 'src/app/data/service/thoai.service';
import * as configSvc from 'src/app/data/service/config.service';
import { UserService } from 'src/app/data/service/user.service';
import { ConfirmationDialogModel, ConfirmDialogComponent } from 'src/app/shared/components/dialogs/confirm-dialog/confirm-dialog.component';
import { ReassignComponent, ReassignDialogModel } from '../../dialogs/reassign/reassign.component';
import {ReplaySubject, Subject, forkJoin} from 'rxjs';
import { takeUntil, filter, catchError, map } from 'rxjs/operators';
import {MatSelect} from '@angular/material/select';
import { KeycloakService } from 'keycloak-angular';
import { ApiProviderService } from 'src/app/core/service/api-provider.service';
import { ProcedureReportService } from 'src/app/data/service/procedure-report/procedure-report.service';
import { ConfirmPrintTemplateDialogModel, PrintTemplateComponent } from 'src/app/modules/dossier/pages/search/dialogs/print-template/print-template.component';
import {ConfigService} from "data/service/dossier/config.service";
import {ExportExcelService} from 'src/app/data/service/export-excel/export-excel.service';
import { AgencyTreeComponent } from 'modules/statistics/pages/statistic-general/component/agency-tree/agency-tree.component';
import {QNIStatisticService} from 'src/app/data/service/qni-statistics/qni-statistic.service';
import { BasedataService } from 'src/app/data/service/basedata/basedata.service';
import { CommonService } from 'src/app/data/service/common.service';
import { NoteComponent } from 'src/app/shared/components/note/note.component';
import {LogmanService} from 'data/service/logman/logman.service';
import { SectorService } from 'src/app/data/service/sector/sector.service';
import { BpmService } from 'src/app/data/service/svc-bpm/bpm.service';
import * as pdfmake from 'pdfmake/build/pdfmake';
import * as htmlToPdfmake from 'html-to-pdfmake';
import * as pdfFonts from 'pdfmake/build/vfs_fonts';
import { ConfirmReceiptListDialogModel, ReceiptListComponent } from '../../../processing/pages/processing-detail/dialogs/receipt-list/receipt-list.component';
import { EReceiptService } from 'src/app/data/service/invoice-receipt/ereceipt.service';
import { AdapterService } from 'src/app/data/service/adapter/adapter.service';
import { ConfirmReceiptCreationDialogModel, ReceiptCreationComponent } from '../../../processing/pages/processing-detail/dialogs/receipt-creation/receipt-creation.component';
import { SpecializedProcessDialogModel, ViewSpecializedProcessComponent } from '../../dialogs/view-specialized-process/view-specialized-process.component';
import { ViewSpecializedProcessVPUBComponent } from '../../dialogs/view-specialized-process-vpub/view-specialized-process-vpub.component';
import { FinancialObligationsComponent } from '../../dialogs/financial-obligations/financial-obligations.component';
import { authenticationStatus } from 'src/app/data/service/config.service';
import { WithdrawQBHComponent, WithdrawQBHModel } from 'src/app/modules/dossier/dialogs/withdraw-qbh/withdraw-qbh.component';
import { AgencyService } from 'src/app/data/service/basedata/agency.service';
import {
  QniViewProcessingVbdlisComponent,
  QniViewProcessingVbdlisDialogModel
} from 'src/app/modules/dossier/pages/processing/dialogs/qni-view-processing-vbdlis/qni-view-processing-vbdlis.component';
import { ProcessService } from 'src/app/data/service/process/process.service';
import { LogSyncComponent, LogSyncDialogModel } from 'src/app/modules/dossier/dialogs/log-sync/log-sync.component';
import {NotificationV2Service} from 'data/service/etl-data/notificationV2.service';
import {TrinamService} from 'modules/hbh/trinam.service';
import { BasecatService } from 'src/app/data/service/basecat/basecat.service';
import { NotificationService } from 'src/app/data/service/notification.service';
import { TrackingBTTTTService } from 'src/app/data/service/tracking-btttt.service';
import { HttpParams } from '@angular/common/http';
import { HumanService } from 'src/app/data/service/human/human.service';
import { AddPaymentMethodComponent, AddPaymentMethodModel } from '../../../processing/pages/processing-detail/dialogs/add-payment-method/add-payment-method.component';
import { UpdateAgencyDialogComponent, UpdateAgencyDialogModel } from 'src/app/shared/components/dialogs/update-agency-dialog/update-agency-dialog.component';
import { SyncStatusDossierComponent, SyncStatusDossierDialogModel } from '../../dialogs/sync-status-dossier/sync-status-dossier.component';
import { ApologyLetterComponent, ConfirmApologyLetterDialogModel } from 'src/app/modules/dossier/dialogs/apology-letter/apology-letter.component';
import { WithdrawVpcComponent, WithdrawVPCModel } from 'src/app/modules/dossier/dialogs/withdraw-vpc/withdraw-vpc.component';
import { VPCConfirmDialogComponent } from 'src/app/shared/components/dialogs/VPCConfirmDialog/VPCConfirmDialog.component';
import { ConfirmVnpostInfoDialogModel, VnpostInfoComponent } from '../../../processing/pages/processing-detail/dialogs/vnpost-info/vnpost-info.component';
import { DossierOriginalService } from 'src/app/data/service/vpc/dossier-original.service';
import { QniViewProcessingTaskOnegateComponent, QniViewProcessingTaskOnegateDialogModel } from '../../../processing/dialogs/qni-view-processing-task-onegate/qni-view-processing-task-onegate.component';
import { QniViewCommentInfo, QniViewCommentInfoModel } from '../../../processing/dialogs/qni-view-comment-info/qni-view-comment-info';
import { ConfirmSelectTaskDialogModel, SelectTaskComponent } from '../../../processing/dialogs/select-task/select-task.component';

export type PageOrientation = 'portrait' | 'landscape';
@Component({
  selector: 'app-list-search',
  templateUrl: './list-search.component.html',
  styleUrls: ['./list-search.component.scss', '/src/app/app.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class ListSearchComponent implements OnInit {
  //tracuutoancoquan
  @ViewChild('pdfContent') pdfContent: ElementRef;
  depConfig = this.deploymentService.getAppDeployment();
  userAgency = JSON.parse(localStorage.getItem('userAgency'));

  config = this.envService.getConfig();
  env = this.deploymentService.getAppDeployment()?.env;
  selectedLang = localStorage.getItem('language') || 'vi';
  selectedLangId = Number(localStorage.getItem('languageId')) || 228;

  // IGATESUPP-76434
  dossierSearchShowUserSectorOnly: boolean = typeof this.env?.dossierSearchShowUserSectorOnly == "boolean" ? this.env?.dossierSearchShowUserSectorOnly : false;

  //thuc hien nghia vu tai chinh
  showFinancialObligations = this.deploymentService.env.OS_QNI.showFinancialObligations;

  //additionalRequirementDate
  additionalRequirementDate = this.env?.additionalRequirementDate

  isShowSearchTaxCode =this.deploymentService.env?.OS_HCM?.isShowSearchTaxCode
  chungThucDienTu = this.deploymentService.getAppDeployment()?.chungThucDienTu;
  showStatusCTDT = this.chungThucDienTu?.showStatus ? this.chungThucDienTu?.showStatus : 0;
  listAuthenticationStatus = this.chungThucDienTu?.authenticationStatus ? this.chungThucDienTu?.authenticationStatus : authenticationStatus;

  //them truong in dam thong tin nguoi nop, ngay nop, ngay tiep nhan
  isBoldInfor = this.env?.isBoldInfor? this.env?.isBoldInfor: false;

 // IGATESUPP-82006 Bổ sung chức năng cập nhật hình thức thanh toán cho quản trị
 oneGateUpdatePaymentMethod = false;

  isShowUserProcessingKTM = this.deploymentService.env.OS_KTM.isShowUserProcessingKTM;

  fakeCompletedDate = this.deploymentService.env.fakeCompletedDate;
  taskCompletedStatusIds = this.deploymentService.env.taskCompletedStatusIds;

  enableForceEndProcess = this.deploymentService.env.enableForceEndProcess;
  enableReassignDossier = this.deploymentService.env.enableReassignDossier;
  enableTooltip=  this.deploymentService.env.OS_HCM.showToolTipsHCM ;
  hasParticipatedInProcessing = this.deploymentService.env.hasParticipatedInProcessing;
  isFilterAddressOrganization = this.deploymentService.env.OS_HCM.isFilterAddressOrganization; //dangquang-IGATESUPP-72196: Thêm tìm kiếm Địa chỉ Cơ quan/ Doanh nghiệp ở Tra cứu toàn cơ quan

  paginationType = this.deploymentService.env.paginationType;

  finaceObligatingStatus = '60f6364e09cbf91d41f88859';

  statusNeedsCalculatorTiming = configSvc.STATUS_NEEDS_CALCULATOR_TIMING.map(item => item.id);
  vpcResolutionProgressDossier = this.deploymentService.getAppDeployment()?.vpcResolutionProgressDossier ? this.deploymentService.getAppDeployment()?.vpcResolutionProgressDossier : 0;  
  vpcStatusNeedsCalculatorTiming = [...this.statusNeedsCalculatorTiming, 5];
  searchConnectionDossier = this.deploymentService.env.searchConnectionDossier;

  exportExcelQNM = this.deploymentService.env.OS_QNM.exportExcelQNM;
  isSyncLGSPHCM = false;
  showRemindSyncLGSPHCM = false;
  countSyncLGSPHCM = 0;
  enableSyncLGSPHCM = this.deploymentService.env.OS_HCM.syncLGSPHCM?.enable ? this.deploymentService.env.OS_HCM.syncLGSPHCM?.enable: false;
  listAgencyIdSyncLGSPHCM = this.deploymentService.env.OS_HCM.syncLGSPHCM?.listAgencyIdSyncLGSPHCM ? this.deploymentService.env.OS_HCM.syncLGSPHCM?.listAgencyIdSyncLGSPHCM : [];
  //DNI
  isHomeDNI = this.deploymentService.getAppDeployment()?.isHomeDNI || false;  

  isQNM = this.env?.OS_QNM?.isQNM ? this.env?.OS_QNM?.isQNM : false;
  isQNIColums = this.env?.OS_QNI?.isQNIColums ? this.env?.OS_QNI?.isQNIColums : false;
  isExportHCM = this.deploymentService.env.OS_HCM.isExportHCM;
  QNIapprovalStatusSearch = this.env?.OS_QNI?.QNIapprovalStatusSearch ? this.env?.OS_QNI?.QNIapprovalStatusSearch : false;
  enableWithdrawDossierBtn = this.deploymentService.env.OS_QNI.enableWithdrawDossierBtn;
  receivingPermission = false;
  enableRegistrationDossier= this.deploymentService.env.OS_HCM.enableRegistrationDossier;
  updatedDateSort = this.deploymentService.env.updatedDateSortDossierSearch;
  //IGATESUPP-41626
  onlyDisplayNationCode = this.deploymentService.env.OS_HCM.hideNationCodeIfExist2TypeOfCode ? this.deploymentService.env.OS_HCM.hideNationCodeIfExist2TypeOfCode : false;
  isShowProceAdminMultiHCM = this.deploymentService.env.OS_HCM.showProceAdminMultiHCM;
  enableHideTotalDossier = this.deploymentService.env.OS_HCM?.hideTotalDossier?.enable ? this.deploymentService.env.OS_HCM?.hideTotalDossier?.enable : false;
  agencyHideTotalDossier = this.deploymentService.env.OS_HCM?.hideTotalDossier?.agency ? this.deploymentService.env.OS_HCM?.hideTotalDossier?.agency : [];
  isShowMenuRemindTask = false;
  flex = 75;
  showAppointmentNoLLTP = this.deploymentService.env.OS_HCM.showAppointmentNoLLTP;
  showDocumentNoLLTP = this.deploymentService.env.OS_HCM.showDocumentNoLLTP;
  showStatusLLTP = this.deploymentService.env.OS_HCM.showStatusLLTP;
  showFilterPaymentStatus = this.deploymentService.env.OS_HCM.showFilterPaymentStatus;
  //Tuesday 18/04/2023 - quocpa-IGATESUPP-44355
  enableFilterAppliedDate = this.deploymentService.env?.OS_HCM?.enableAppliedDateFilter ? this.deploymentService.env?.OS_HCM?.enableAppliedDateFilter : 0;

    // IGATESUPP-45420: Sở GTVT - Bổ sung tên công trình đề nghị cấp phép
  showConstructionPermit =  this.deploymentService.env?.OS_HCM.showConstructionPermit;
  isShowConstructionUnit = this.deploymentService.env?.OS_HCM?.isShowConstructionUnit
  allowShowStatusSyncLGSP = this.deploymentService.env?.OS_HCM.allowShowStatusSyncLGSP;
  isShowFilterSector = false;
  enableFilterSectorIs = this.deploymentService.env?.OS_HCM_SCT_LV?.isEnableFilter ? this.deploymentService.env?.OS_HCM_SCT_LV?.isEnableFilter : false;
  agencyUnitEnableFilterSector = this.deploymentService.env?.OS_HCM_SCT_LV?.listAgencyIdUnit ? this.deploymentService.env?.OS_HCM_SCT_LV?.listAgencyIdUnit : [];
  allowShowButtonReleaseViewReceipt = false;
  allowShowButtonPrint = false;

  showSectorOnlineReception = this.deploymentService?.newConfigV2?.showSectorOnlineReception;

  // IGATESUPP-67604
  hideOtherAgencyDossierSearch = this.deploymentService.env?.OS_BTTTT.hideOtherAgencyDossierSearch ? this.deploymentService.env.OS_BTTTT.hideOtherAgencyDossierSearch : false;
  //IGATESUPP-51996
  isShowFilterSortHCM = false;
  agencyShowFilterSortHCM = this.deploymentService.env?.OS_HCM?.listAgencyShowFilterSort ? this.deploymentService.env?.OS_HCM?.listAgencyShowFilterSort : [];
  rangeLimitDossierSearch = this.deploymentService.env.OS_HCM.allowFilterAppliedDateOptimization.rangeLimitDossierSearch;
  hideInforAdditionalRequirementDate = this.deploymentService.env.OS_HCM.hideInforAdditionalRequirementDate == true  ? true : false;
  hideInforAdditionalRequirementDateDossierList = this.deploymentService.env.OS_HCM.hideInforAdditionalRequirementDateDossierList;
  nowDate = tUtils.newDate();
  // tslint:disable-next-line: max-line-length
  toDate = this.nowDate.getFullYear() + '-' + ((this.nowDate.getMonth() + 1) <= 9 ? '0' + (this.nowDate.getMonth() + 1) : (this.nowDate.getMonth() + 1)) + '-' + ((this.nowDate.getDate() <= 9) ? ('0' + this.nowDate.getDate()) : this.nowDate.getDate());
  // tslint:disable-next-line: max-line-length
  fromDate ;
  // IGATESUPP-59049 [HCM iGate V2] [Sở Y Tế] Tạo mục tìm kiếm "Người chịu trách nhiệm chuyên môn" cho đơn vị [SD2180]
  isShowResPerson = false;
  enablefindResPerson =this.deploymentService.env?.OS_HCM?.findResPerson?.enable;
  listAgencyFindResperon = this.deploymentService.env?.OS_HCM?.findResPerson?.agencyList;
  hideDeleteButtonDossier = false;
  allowFilterAcceptedDateOptimization = this.deploymentService.env?.OS_HCM?.allowFilterAcceptedDateOptimization.enable;
  allowFilterPaymentStatusListAgency = this.deploymentService.env?.OS_HCM?.allowFilterPaymentStatusListAgency;
  rootAgency;
  isAllowFilterPaymentStatusListAgency = false;
  rangeLimitAcceptedDateFilter = this.deploymentService.env.OS_HCM.allowFilterAcceptedDateOptimization.rangeLimitDossierSearch;
  // config vbdlis
  vbdlisConfigId = this.env?.OS_QNI?.vbdlisConfigId  ? this.env?.OS_QNI?.vbdlisConfigId : "";
  isDisableSearch = false;
  enableViewProcessingVbdlisBtn = this.deploymentService.env?.OS_QNI?.enableViewProcessingVbdlisBtn;

  allowReSendLLTPLgspHcm = this.deploymentService.env?.OS_HCM.allowReSendLLTPLgspHcm;
  recallNumLLTP = 0;

  showReCheckPayment = this.deploymentService.env.showReCheckPayment;
  enableSendDossierViaSoftwareQLVB = this.deploymentService.getAppDeployment()?.sendDossierViaSoftwareQLVB == 1 ? this.deploymentService.getAppDeployment().sendDossierViaSoftwareQLVB : false;
  procedure;
  allowReceptionSendLLTPLgspHcm = this.deploymentService.env.OS_HCM?.allowReceptionSendLLTPLgspHcm;
  trackingBTTTT = this.deploymentService.getAppDeployment()?.trackingBTTTT;
  // Agency
  agencyInfo = [];
  isConnectionTypeEnable = !!this.env?.OS_KTM?.connectionType ? true : false;
  dossierCodePattern = null;
  // enableAppointmentDateEdit = this.deploymentService.env.enableAppointmentDateEdit;
  // appointmentDateLLTP:any = '';
  errGenTimeCount = 0;
  checkHcmLyLichTuPhapReceiving: any = 0;
  checkHcmHoTichTuPhapReceiving: any = 0;
  checkCriminalRecords: any = 0;
  checkCivilStatusJustice: any = 0;
  procedureProcessDetail: any;
  idProcedureInProcess: string;
  listProcedureProcess = [];
  dossierCode: any
  enableMergeDeepDossier = this.deploymentService.env.enableMergeDeepDossier.onlineReception;
  isLoadedAgency = false;
  tempDossier : any;
  sendNotifyNearExpiry = this.deploymentService?.env?.OS_HCM?.sendNotifyNearExpiry;
  allowOnlineReceptionSendLLTPLgspHcm = this.deploymentService.env.OS_HCM?.allowOnlineReceptionSendLLTPLgspHcm;
  tagDossierReceivingDirectlyId = this.env?.tagDossierReceivingDirectlyId ? this.env?.tagDossierReceivingDirectlyId : "5f8968888fffa53e4c073ded";
  acceptedDateOld : any;
  dossierTaskData: any;
  isNoGateWay = false;
  taskGateway = {
    sequenceFlow: [],
    type: 1,
    selectedAgency: '',
    isFullListUser: false,
    currentUserPage: 0,
    listUser: [],
  };
  selectedFlowForConfirm = [];
  resultContent = {
    id: '',
    variables: {
      dossierTask: [],
    },
    payloadType: 'CompleteTaskPayload'
  };
  nextFlowElementName = '';
  nextFlowTaskKey = '';
  processDefinitionTask: any;
  propertiesTask = new FormGroup({
    agency: new FormArray([], Validators.required),
    assignee: new FormControl('')
  });
  smsEmailContent = '';
  firstTaskData: any;
  dossierId;
  procedureId;
  currentTask = [
    {
      id: '',
      sender: {
        id: '',
        fullname: ''
      },
      assignee: {
        id: '',
        fullname: ''
      },
      agency:{
        id: '',
        name: []
      },
      candidateUser: [],
      candidateGroup: [],
      processDefinition: {
        id: ''
      },
      isCurrent: 1,
      dueDate: '',
      claimDate: '',
      resolvedDate: '',
      createdDate: '',
      updatedDate: '',
      activitiTask: {
        id: '',
        status: ''
      },
      bpmProcessDefinitionTask: {
        id: '',
        name: '',
        variable: {
          form: {
            id: ''
          }
        },
        definitionTask: {},
        processingTime: '',
        processingTimeUnit: ''
      },
      eForm: {
        id: ''
      },

    }
  ];
  isProcessingTimeAfterAddition = false;
  applicantEForm = {
    id: '',
    component: null,
    data: null,
    renderOptions: {
    }
  };
  listDossierReceivingKind = [];
  selectedProcess = {
    id: '',
    name: null,
    procedure: {},
    processDefinition: {
      id: '',
      name: '',
      activiti: {
        id: '',
        model: {
          id: '',
          name: '',
          project: {
            id: ''
          }
        }
      },
      processingTime: 10,
      processingTimeUnit: 'd',
      reportTemplate: [],
      applicantEForm: null,
      timesheet: null,
      eForm: null,
      dynamicVariable:{

      }
    },
    isCTDT: false,
    startDate: '',
    endDate: '',
    isApplied: 0,
    firstTask: null,
    status: 1
  };
  taskId: string;
  enableIntegratedReception = this.deploymentService.env.OS_HCM.integratedReception;

  showStatusHTTPLTKH = this.deploymentService.env.OS_HCM.showStatusHTTPLTKH ? this.deploymentService.env.OS_HCM.showStatusHTTPLTKH : false;
  isQbhDossierKm = this.deploymentService?.env?.OS_QBH?.isQbhDossierKm ?? false;

  showUpdateAgencyButton = this.env?.showUpdateAgencyButton ?? false;

  interfaceWorkHorizontal = this.deploymentService.getAppDeployment().interfaceWorkHorizontal == 1? 1 : 0;

  isShowBtnSyncDossier = this.deploymentService?.env?.OS_HGG?.isShowBtnSyncDossier ?? false
  listProcedureCodeHTTP = this.deploymentService.getAppDeployment()?.listProcedureCodeHTTP ? this.deploymentService.getAppDeployment().listProcedureCodeHTTP : [];


  procedureIdDoiGPLX = this.deploymentService?.getAppDeployment()?.excelDoiGPLX?.procedureID ? this.deploymentService.getAppDeployment().excelDoiGPLX.procedureID : "";
  excludeStatusIDs = this.deploymentService?.getAppDeployment()?.excelDoiGPLX?.excludeDossierStatusList ? this.deploymentService.getAppDeployment().excelDoiGPLX.excludeDossierStatusList : []
  roleDoiGPLX = this.deploymentService?.getAppDeployment()?.excelDoiGPLX?.role ? this.deploymentService.getAppDeployment().excelDoiGPLX.role : "";
  hasExcelGPLXRole = false;
  viewOrderDetailsVNPOST = this.deploymentService?.getAppDeployment()?.viewOrderDetailsVNPOST || 0;
  hasApprovalInfNAST = this.userService.checkPermissionExists("approvalInfNAST");
  enableViewDossierProcessingTaskOneGate = this.deploymentService.env?.OS_QNI?.enableViewDossierProcessingTaskOneGate || false;
  configWorkInterfaceKHA: boolean = this.deploymentService?.getAppDeployment()?.configWorkInterfaceKHA || false;
  hasViewProcessPermission = this.userService.checkPermissionExists("onegateViewProcessPermission");
  searchByAncestorAgency = this.deploymentService?.getAppDeployment()?.searchByAncestorAgency || false;
  enablePrintBillNew = this.deploymentService.getAppDeployment()?.enablePrintBillNew ? this.deploymentService.getAppDeployment().enablePrintBillNew : true;
  @ViewChild(MatAccordion) accordion: MatAccordion;
  searchForm = new FormGroup({
    code: new FormControl(''),
    nationCode: new FormControl(''),
    identityNumber: new FormControl(''),
    applicantName: new FormControl(''),
    procedureCode: new FormControl(''),
    ownerFullname: new FormControl(''),
    advSector: new FormControl(''),
    advProcedure: new FormControl(''),
    advNation: new FormControl(''),
    advProvince: new FormControl(''),
    advDistrict: new FormControl(''),
    advWard: new FormControl(''),
    advAddress: new FormControl(''),
    advAddressOrganization: new FormControl(''),
    advTaskStatusId: new FormControl(''),
    advAcceptFrom: new FormControl(''),
    advAcceptTo: new FormControl(''),
    advAppointmentFrom: new FormControl(''),
    advAppointmentTo: new FormControl(''),
    advApplyMethod: new FormControl(''),
    advProcessStatus: new FormControl(''),
    avdResultReturnedFrom: new FormControl(''),
    avdResultReturnedTo: new FormControl(''),
    advAgency: new FormControl(''),
    implementProgress: new FormControl(''),
    payStatus: new FormControl(''),
    advnoidungyeucaugiaiquyet: new FormControl(''),

    approvalstatus: new FormControl(''),
    applicantOrganization: new FormControl(''),
    //KGG Outsource
    agencySearchKGG: new FormControl(''),
    sortCtrl: new FormControl(''),
    appointmentNoLLTP: new FormControl(''),
    documentNoLLTP: new FormControl(''),
    vnpostStatus: new FormControl(''),
    receivingKindCtrl: new FormControl(''),
    paymentStatus: new FormControl(''),
    //Thursday 13/04/2023 - quocpa-IGATESUPP-44355
    advFilingFrom: new FormControl(''),
    advFilingTo: new FormControl(''),
    //end Thursday 13/04/2023 - quocpa-IGATESUPP- 44355
    receiptCode: new FormControl(''),
    sortCtrlHCM: new FormControl(''),
    contentMain: new FormControl(''),
    taxCode: new FormControl(''),
    resPerson: new FormControl(''),

    // IGATESUPP-76910
    taskAssigneeCtrl: new FormControl(''),
    taskAgencyCtrl: new FormControl(''),
    // END IGATESUPP-76910
    remindCtrl: new FormControl(''),
    //IGATESUPP-111250
    phoneNumberApply: new FormControl(''),
  });
  @ViewChild('statisticAgencyTree') statisticAgencyTree: AgencyTreeComponent;
  @HostListener('document:click', ['$event.path'])
  public onGlobalClick(targetElementPath: Array<any>) {
    if(!!targetElementPath){
    const elementRefInPath = targetElementPath.find(e => e === this.statisticAgencyTree);
    if (!elementRefInPath) {
      // this.isAgencyTreeOpen = false;
    }
  }
  }
  pageTitle = {
    vi: `Tra cứu hồ sơ toàn cơ quan`,
    en: `Dossier lookup all agency`
  };
  remindAll = this.deploymentService.getAppDeployment()?.listRemindPageSearch;
  checkDrawQBH = false;
  checkDrawVPC = false;
  listNation = [];
  listProvince = [];
  listDistrict = [];
  listWard = [];
  listDossierTaskName = [];
  listDossierTaskNamePage = 0;
  isFullListDossierTaskName = false;
  showReceiveDossierResultMethod = this.deploymentService.env.OS_HCM?.showReceiveDossierResultMethod ? this.deploymentService.env.OS_HCM?.showReceiveDossierResultMethod: false;
  listAgency = [];
  listAgencyPage = 0;
  isFullListAgency = false;
  receivingKinds = this.deploymentService.env?.OS_HCM?.receivingKinds ? this.deploymentService.env.OS_HCM.receivingKinds : [];
  showReceivingKindComboBox = this.receivingKinds.length > 0;
  listSector = [];
  listSectorPage = 0;
  isFullListSector = false;
  searchSectorKeyword = '';
  keySearchSectorAgency = '';
  keywordSector = '';
  totalPagesSector = 0;
  currentPageSector = 0;
  pageSizeSector = 1000;
  searchSectorCtrl: FormControl = new FormControl();
  protected onDestroy = new Subject<void>();
  protected sectors: any[] = this.listSector;
  public sectorFiltered: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  parentAgency = '';
  qni_advancedsearch = false;
  qni_listsearch = false;
  isAgencyTreeOpen = false;
  savedItems = [];

  // IGATESUPP-76910
  searchAgencyUnitCtrl: FormControl = new FormControl('');
  searchAgencyUnitTimeout: any;
  listAgencyUnit = [];
  pageAgencyUnit: number = 0;
  pageAgencyUnitSize: number = 50;
  isLastAgencyUnit: boolean = false;
  dossierSearchShowAgencyUnit: boolean = typeof this.env?.dossierSearchShowAgencyUnit == "boolean" ? this.env?.dossierSearchShowAgencyUnit : false;

  searchAssigneeCtrl: FormControl = new FormControl('');
  searchAssigneeTimeout: any;
  listAssignee = [];
  pageAssignee: number = 0;
  pageAssigneeSize: number = 50;
  isLastAssignee: boolean = false;
  dossierSearchShowAssignee: boolean = typeof this.env?.dossierSearchShowAssignee == "boolean" ? this.env?.dossierSearchShowAssignee : false;
  // END IGATESUPP-76910

  // IGATESUPP-79377
  dossierSearchAutofillAgency: boolean = typeof this.env?.dossierSearchAutofillAgency == "boolean" ? this.env?.dossierSearchAutofillAgency : false;
  userExperiences: any[] = [];
  agencyFilled: boolean = false;
  dossierSearchAutofillAgencyUnit: boolean = typeof this.env?.dossierSearchAutofillAgencyUnit == "boolean" ? this.env?.dossierSearchAutofillAgencyUnit : false;
  agencyUnitFilled: boolean = false;
  // END IGATESUPP-79377

  // excel
  excelData = [];

  waitingDownloadExcel = false;
  ExportPageDossiersToExcelQNI = false;
  showStatusVnpost = this.env?.OS_HCM?.dossier?.showStatusVnpost ? this.env?.OS_HCM?.dossier?.showStatusVnpost : 0;
  isShowAgencyEnterprise = this.deploymentService.env.OS_HCM.showAgencyEnterprise;
  enableSearchProcedureCode = this.deploymentService.env.OS_HGG.enableSearchProcedureCode ? this.deploymentService.env.OS_HGG.enableSearchProcedureCode : false;
  // OS KGG
  isUserRQ = this.env?.OS_KGG?.isUserRQ ? this.env?.OS_KGG?.isUserRQ : false;
  enableApprovaledAgencyTreeView = this.env?.enableApprovaledAgencyTreeView ? this.env.enableApprovaledAgencyTreeView : false;
  isShowContent = this.deploymentService.env?.OS_KGG?.isShowContent ? this.deploymentService.env?.OS_KGG?.isShowContent : false;
  qbhwithdrawprocess = this.deploymentService.env?.OS_QBH?.qbhwithdrawprocess ? this.deploymentService.env?.OS_QBH?.qbhwithdrawprocess : false;
  vpcwithdrawprocess = this.deploymentService.getAppDeployment()?.vpcWithdrawRequest ? this.deploymentService.getAppDeployment()?.vpcWithdrawRequest : 0;
  sortByConfig = this.deploymentService?.newConfigV2?.sortByConfig;

  // Procedure infinity scroll with search
  private listProcedure: any[] = [];
  listProcedurePage = 0;
  isFullListProcedure = false;
  searchProcedureKeyword = '';
  procedureCtrl: FormControl = new FormControl();
  searchProcedureCtrl: FormControl = new FormControl();
  searchVnPostCtrl: FormControl = new FormControl();
  protected procedures: any[] = this.listProcedure;
  public procedureFiltered: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  @ViewChild('procedureMatSelectInfiniteScroll', { static: true }) procedureMatSelectInfiniteScroll: MatSelect;
  showDossierFee = this.deploymentService.env.OS_HCM.showDossierFee;
  enableCheckLogSyncDossier = this.deploymentService.env.enableCheckLogSyncDossier;
  isOwnerFullname = this.env?.isOwnerFullname === 1;

  displayedColumnsDefault: string[] = ['select', 'code', 'procedureName', 'processingTime', 'applicantName', 'agency', 'status', 'action'];
  displayedColumnsDNI: string[] = ['select', 'stt', 'code', 'procedureName', 'processingTime', 'applicantName', 'agency', 'status', 'action'];

  displayedColumnsQNM: string[] = ['select','stt', 'code', 'procedureName','noidungyeucaugiaiquyet','profileOwner', 'applicantName','phoneNumber', 'processingTime', 'pay', 'status', 'action'];

  displayedColumnsHCM: string[] = ['select', 'code', 'procedureName', 'processingTime', 'applicantName', 'fee', 'agency', 'status', 'action'];
  displayedColumnsPayFeeHCM: string[] = ['select', 'code', 'procedureName', 'processingTime', 'applicantName', 'pay', 'fee', 'agency', 'status', 'action'];
  displayedColumnsPayFeeNoPayHCM: string[] = ['select', 'code', 'procedureName', 'processingTime', 'applicantName', 'pay', 'fee', 'agency', 'status', 'action'];
  displayedColumnsQNI: string[] = ['select','stt', 'code', 'noidungyeucaugiaiquyet','profileOwner','applicantName','phoneNumber', 'processingTime', 'status', 'action'];
  // tslint:disable-next-line:max-line-length
  // displayedColumns: string[] = this.isQNM ? this.displayedColumnsQNM : (this.env?.ShowFieldHandlingAcency  === 1 || this.isOwnerFullname) ? this.env?.arrShowTable  : this.displayedColumnsDefault;
  displayedColumns: string[] = (this.isHomeDNI ? this.displayedColumnsDNI : this.isQNIColums ? this.displayedColumnsQNI :  this.isQNM ? (this.showDossierFee ? this.displayedColumnsPayFeeHCM : this.displayedColumnsQNM) :  this.showDossierFee ? this.displayedColumnsHCM : (this.env?.ShowFieldHandlingAcency  === 1 || this.isOwnerFullname) ? this.env?.arrShowTable  :  this.displayedColumnsDefault);
  displayedColumnsQBH: string[]  = ['select','stt', 'code', 'noidungyeucaugiaiquyet','profileOwner','applicantName','phoneNumber', 'processingTime', 'status', 'action'];
  //HGI
  isHGIColums = this.deploymentService.getAppDeployment()?.displayedColumnsStt ? this.deploymentService.getAppDeployment()?.displayedColumnsStt : false;
  displayedColumnsHGI: string[] = ['stt','select', 'code', 'procedureName', 'processingTime', 'applicantName', 'agency', 'status', 'action'];

  hpglistlayout = this.deploymentService.env?.OS_HPG?.hpglistlayout ? this.deploymentService.env?.OS_HPG?.hpglistlayout : false;
  dossierSearchShowAgency = this.deploymentService.env?.OS_HPG?.dossierSearchShowAgency ? this.deploymentService.env?.OS_HPG?.dossierSearchShowAgency : false;

  displayedColumnsHPG: string[]  = ['select','stt','sector', 'code', 'procedureName', 'applicantName','agency','phoneNumber', 'processingTime','agencyProcessing', 'status', 'action'];

  ELEMENTDATA: DossierSearchElement[] = [];
  dataSource: MatTableDataSource<DossierSearchElement>;
  countResult = 0;
  size = this.env?.OS_KTM?.defaultPageSize ? this.env?.OS_KTM?.defaultPageSize : 10;
  page = 1;
  pageIndex = 1;
  flexGtSm4Column= 26;
  flexGtSm4ColumnName=26;
  pgSizeOptions = this.config.pageSizeOptions;
  xpandStatus = false;
  listTimesheet = [];
  listTimeSheetGenV2 = [];
  isCheckedAll = false;
  searchDomain = '';
  receptionForm: any = [
    {
      id: 0,
      name: [
        {
          languageId: 228,
          name: 'Trực tuyến',
          content: ' tiếp nhận trực tuyến'
        },
        {
          languageId: 46,
          name: 'Online',
          content: ' online reception'
        }
      ]
    },
    {
      id: 1,
      name: [
        {
          languageId: 228,
          name: 'Trực tiếp',
          content: ' tiếp nhận trực tiếp'
        },
        {
          languageId: 46,
          name: 'Direct',
          content: ' direct reception'
        }
      ]
    },
    {
      id: 2,
      name: [
        {
          languageId: 228,
          name: 'Cổng dịch vụ công quốc gia',
          content: ' tiếp nhận từ cổng Dịch vụ công quốc gia'
        },
        {
          languageId: 46,
          name: 'National public service portal',
          content: ' received from the National Public Service portal'
        }
      ]
    }
  ];
  isDienBien = this.env?.isDienBien ? this.env?.isDienBien : false;
  checkProvineAdmin ? = JSON.parse(localStorage.getItem('superAdmin'));
  arrReceptionForm: any = [];
  justRegistered: any;
  checkAll = false;
  selectedDossiers = [];
  selectedDossiersLog = [];
  numberOfElements = 0;
  expandReminderMenu = true;
  remindId = '';
  listMenuRemind: any = [];
  agencyId: string;
  agencyIdForProcedure: string;
  maxDeleteDossierMulti = 10;
  lengthRemind = 0;
  countDossier = false;
  isAdmin = false;
  isOneGateOfficer = false;
  hasDossierDeletePermission = false;
  userId = localStorage.getItem('UID');
  oneGateDeleteDossier = sessionStorage.getItem('oneGateDeleteDossier');
  // Check OS
 // showBtnBBBanGiao = this.env?.OS_QNM ? this.env?.OS_QNM?.showBBBanGiao: false;
  // isQNM = this.env?.isQNM ? this.env?.isQNM : false;
  showBtbBB = false;
  // Lấy cấu hình cho phép hiện combobox Tiến độ xử lý
  showCbImplementProgress = this.env?.OS_HCM?.showCbImplementProgress && this.env?.OS_HCM?.showCbImplementProgress === 1 ? true : false;
  arrImplementProgress = [
    {value : 1, name : 'Còn hạn'},
    {value : 2, name : 'Trễ hạn'}
  ];
  showOrganizationInformation = this.env?.OS_HCM?.showOrganizationInformation ? this.env?.OS_HCM?.showOrganizationInformation : false;
  calculateAppointmentDate = this.deploymentService.env?.OS_HCM?.calculateAppointmentDate;

  ////phucnh.it2-IGATESUPP-40295
  enableNewVnpostStatus = this.deploymentService.env.OS_HCM.enableNewVnpostStatus;
  undefindedCompleteTime = 0;
  undefindedCompleteTimeTask = 0;
  isSelectedProcedure = false;
  showBB: string = 'Chờ xác nhận kết quả';
  listConfigTemplate = [];
  listConfigTemplateDetail = [];
  templateDeliveryId = '621d6f5f1b89d23f9f1f54d2';
  reportType = '';
  signingPlace = 'Tiền Giang';
  listInfoDossier: any = [];
  columns: any[];
  agencyNameExcel = '';
  dem: number = 0;
  footerData: any[][] = [];
  listExcel: any = [];
  accepterInfo = {
    id: '',
    username: '',
    fullname: '',
    accountId: ''
  };
  listUserAgency: any;
  mobileNumberAgency: any;
  addressAgency: any;
  // KGG OS
  listAgencyKGG = [];
  arrSortType = [
    {id: 0, value : "Chưa chọn"},
    {id : 1, value : "Hồ sơ trễ hạn trước"},
    {id : 2, value : "Hồ sơ còn hạn trước"},
    {id : 3, value : "Ngày nộp tăng dần"},
    {id : 4, value : "Ngày nộp giảm dần"}
  ];
  sortId = '0';
  sortType = '';
  addressCityAgency: any;
  addressProvinceAgency: any;
  addressDistrictAgency: any;
  addressFull: any;
  listVnpostStatus:any = [];
  sortHcmId = '4';
  // config tbnohtttlQNI
  tbnohtttlQNIConfigId = this.env?.OS_QNI?.tbnohtttlQNIConfigId  ? this.env?.OS_QNI?.tbnohtttlQNIConfigId : "";

  paymentConfirmed = this.deploymentService.env?.OS_HCM?.paymentConfirmed ? this.deploymentService.env?.OS_HCM?.paymentConfirmed : {
    dossierTaskStatus: "6411109d5706bf6444283fd8",
    dossierMenuTaskRemind: "6409826d1aee0a3ee93c8dbc",
    confirmPayment: false,
  };
  confirmPayment = this.paymentConfirmed.confirmPayment;

  enableRemoveVnpostFeeToAnotherTable = this.deploymentService.env?.OS_HCM?.enableRemoveVnpostFeeToAnotherTable ? this.deploymentService.env?.OS_HCM?.enableRemoveVnpostFeeToAnotherTable : 0;
  syncPaymentStatus = this.deploymentService.env.syncPaymentStatus;
  paymentRequestId = this.deploymentService.env.paymentRequest.id;
  paidDossierId = this.deploymentService.env.paidDossier.id;
  paidDossierName = this.deploymentService.env.paidDossier.name;
  dossierFeeByDossier = new Map<String, any[]>();
  isDefaultElectronicReceipt = false;
  isSingleDossierReceipt = this.deploymentService.env.OS_HCM.isSingleDossierReceipt;

  //IGATESUPP-79630 lehoanganh.hcm - [HCM - iGATE v2] Sở Tư Pháp - Mặc định giá trị khi phát hành biên lai [SD16901] - Gộp giá trị cấu hình v2
  isDefaultElectronicReceiptForApplyMethod = false;
  isSingleDossierReceiptForApplyMethod = false;

  enableAddReceiptNumberToEformAllUnitScreen = this.deploymentService.env.OS_HCM.enableAddReceiptNumberToEformAllUnitScreen;
  isSortMenuRemind = false;
  typeLoaiLePhi = this.deploymentService.env.OS_HCM.typeLoaiLePhi;
  costValue1 = this.deploymentService.env.OS_HCM.costValue1;
  costValue2 = this.deploymentService.env.OS_HCM.costValue2;
  totalCost_VNP = '';
  totalRemaining_VNP = '';
  dossierPayment = [];
  apologyTextFilePreview = [];
  showNote = false;
  showAddButton = true;
  dataProcost: any;
  checkNotePermistionEnable = false;
  selectedValue: string[]=[];
  tempSelectedValue: string[]=[];
  qbhmenuaction = this.deploymentService?.env?.OS_QBH?.qbhmenuaction ? this.deploymentService?.env?.OS_QBH?.qbhmenuaction : false;
  isQBH = this.deploymentService?.env?.OS_QBH?.isQBH ? this.deploymentService?.env?.OS_QBH?.isQBH : false;
  isGenerateReceiptCode = this.deploymentService?.env?.OS_QBH?.isGenerateReceiptCode ? this.deploymentService?.env?.OS_QBH?.isGenerateReceiptCode : false;
  isConfigureReceiptCode = this.deploymentService?.env?.OS_QBH?.isShowConfigureReceiptCode ? this.deploymentService?.env?.OS_QBH?.isShowConfigureReceiptCode : false;
  checkHcmLyLichTuPhap: any = false;
  durationCalculateAppointmentDate = '';
  showDurationCalculateAppointmentDate = false;
  proAdminELEMENTDATA: any[] = [];
  listSigner: any[] = [];
  showDossierProAdmins = false;
  timesheet = {
    receivingDue: this.nowDate,
    dateDiff: '',
    endDate: this.nowDate,
    isOverDue: false,
    isTaskOverDue: false,
    dueTimer: {
      day: 0,
      hour: 0,
      minute: 0,
      second: 0
    },
    taskDueTimer: {
      day: 0,
      hour: 0,
      minute: 0,
      second: 0
    }
  };
  activitiProcessInstance: any;
  checkFinaceObligating = false;
  callApiLGSPAfterGotReceipt = this.deploymentService.env.OS_HCM.callApiLGSPAfterGotReceipt;
  isLoading = false;
  numberOfReceipt: any;
  dossier: any;
  dossierFee = [];
  listFeeId=[];
  hideTotal = false;
  hideTotal_VNP = false;
  formIO = {
    id: '',
    component: '',
    data: null
  };
  listDossierReceipt:any;
  TBTHUEDVCQG_ELEMENTDATA: any[] = [];
  tbthuedvcqgDataSource: MatTableDataSource<any>;
  listDossierReceiptNumberAllUnitScreen="";

  isDossierStatusHasFFO = false;
  justRegisteredText = '';
  citizenId = '';
  latestAdditionalDate = '';
  listHistory = [];

  hcmListDossierReceiptNumberAllUnitScreen = "";
  isEGateConvert = false;
  receiveForm = new FormGroup({
    receivingKind: new FormControl(''),
    rcNation: new FormControl(''),
    rcProvince: new FormControl(''),
    rcDistrict: new FormControl(''),
    rcVillage: new FormControl(''),
    customAddress: new FormControl('')
  });
  extendTimeApproval:any = '';
  dossierReceivingKind: any;
  attachFilePreview = [];
  resultFilePreview = [];
  listVillage = [];
  procedureForm = [];
  result = [];

  isShowSortHCM = false;
  enableDisplayOfSpecializedProcessing = this.deploymentService.env?.OS_HCM?.enableDisplayOfSpecializedProcessing ? this.deploymentService.env?.OS_HCM?.enableDisplayOfSpecializedProcessing : false;

  hideRequestToWithdraw = this.deploymentService.env.OS_HCM.hideRequestToWithdraw.enable &&
                          tUtils.isAllowedAgency(localStorage, this.deploymentService.env.OS_HCM.hideRequestToWithdraw.agencyUsed);

  showDossierName = this.deploymentService.env.OS_HGG.showDossierName;

  //IGATESUPP-58187 [Sở Y Tế] Ẩn đồng hồ đếm ngược đối với các hồ sơ bổ sung
  hidenOverDueCalculation = this.deploymentService.env?.OS_HCM?.hidenOverDueCalculation ? this.deploymentService.env.OS_HCM.hidenOverDueCalculation :
  {
    enable : false,
    dossierTaskStatus: []
  };
  qbhlistlayout = this.deploymentService?.env?.OS_QBH?.qbhlistlayout ? this.deploymentService?.env?.OS_QBH?.qbhlistlayout : false;
  qbhlayoutformalities  = this.deploymentService?.env?.OS_QBH?.qbhlayoutformalities  ? this.deploymentService?.env?.OS_QBH?.qbhlayoutformalities  : false;

  qbhlist_collapse = this.deploymentService?.env?.OS_QBH?.qbhlist_collapse ? this.deploymentService?.env?.OS_QBH?.qbhlist_collapse : false;
  addSectorCode = this.deploymentService.env.OS_QNM.addSectorCode;

  // config vbdlis
  vbdlisHCMConfigId = this.deploymentService?.env?.OS_QNI?.vbdlisConfigId;
  enableButtonUpdateVBDLIS = this.deploymentService.env.OS_QNI.enableButtonUpdateVBDLIS;

  enableButtonReturnResult = this.deploymentService?.env?.OS_HCM?.enableButtonReturnResult ? this.deploymentService?.env?.OS_HCM?.enableButtonReturnResult : false;
  allowFilterAppliedDateOptimization = this.deploymentService.env?.OS_HCM.allowFilterAppliedDateOptimization
  allowFilterAppliedDateOptimizationCustom = this.deploymentService.env?.OS_HCM.allowFilterAppliedDateOptimizationCustom
  isShowFilterApplyDate = false;
  customMenuTaskRemindDossierSearch = this.deploymentService.env.OS_HCM?.customMenuTaskRemindDossierSearch
  changeMenuTaskRemind = false
  enableShowPaymentOnlineQni = this.deploymentService.env?.OS_QNI?.enableShowPaymentOnlineQni || false;
  // IGATESUPP-63061 : syt tính giờ theo lịch làm việc
  durationByWorkingTime = this.deploymentService.env.OS_HCM.durationByWorkingTime ? this.deploymentService.env.OS_HCM.durationByWorkingTime :
  {
    turnOn : false,
    agencyList : []
  };
  agencyAcceptDurationByWorkingTime = false;

  dossierReceiptCreationStatusConfig = this.deploymentService.env.OS_HCM.dossierReceiptCreationStatusConfig;
  showReceiptCreationStatusWhiteList:any[] = [];

  isNotification = this.deploymentService.env.notification.isNotification;
  // OS_HGI
  isGetListSetorHGI = this.deploymentService?.env?.OS_HGI?.isGetListSetorHGI == true ? true : false;
  isGetListProcedureHGI = this.deploymentService?.env?.OS_HGI?.isGetListProcedureHGI == true ? true : false;
  isOptionSearchHGI = this.deploymentService?.env?.OS_HGI?.isOptionSearchHGI == true ? true:false;
  isSearchHGI  = this.deploymentService?.env?.OS_HGI?.isSearchHGI  == true ? true:false;

  checkRequirePaymentCMUAuto = this.deploymentService.env?.OS_CMU?.checkRequirePaymentCMUAuto == true ? this.deploymentService.env?.OS_CMU?.checkRequirePaymentCMUAuto : false;
  enableChangeDueDateCalculation = this.deploymentService.env.ChangeTimesheetDossierCalculation.dueDateSearch;
  allowCreateReceiptWithAnyStatus = this.deploymentService.env?.OS_HCM?.allowCreateReceiptWithAnyStatus;
  isDVCLTEnable = this.deploymentService.getAppDeployment()?.isDVCLTEnable;
  isAddColumDataExport = this.deploymentService.getAppDeployment()?.isAddColumDataExport || false;
  columnStyles = [];
  isShowNationCodeCbx = this.deploymentService.optimize.dossierSearch.isShowNationCodeCbx;
  downloadFileDossierRar = this.deploymentService.getAppDeployment()?.downloadFileDossierRar === 1;
  hiddenButtonIssueReceiptsDossierOnline = this.deploymentService.getAppDeployment()?.hiddenButtonIssueReceiptsDossierOnline == 1 && tUtils.isAllowedAgency(localStorage,this.deploymentService.getAppDeployment()?.hiddenButtonIssueReceiptsDossierOnlineAgencyIds);
  noneWithdrawDossierConfig = this.deploymentService?.getAppDeployment()?.noneWithdrawDossierConfig || false;

  enableButtonFunctionToSendApologyLetter = this.deploymentService?.getAppDeployment()?.buttonFunctionToSendApologyLetter == 1? true: false;
  enableButtonFunctionToSendApologyLetterAgencyIds = this.deploymentService?.getAppDeployment()?.buttonFunctionToSendApologyLetterAgencyIds ?? [];
  enableFunctionToSendApologyLetter = (this.enableButtonFunctionToSendApologyLetter == true) && tUtils.isAllowedAgency(localStorage,this.enableButtonFunctionToSendApologyLetterAgencyIds);
  
  //IGATESUPP-98928  Thêm tên trích yếu hiển thị ở thông tin hồ sơ
  showAbstractConstructionUnitEnable = this.deploymentService.getAppDeployment()?.showAbstractConstructionUnitEnable;
  showAbstractConstructionUnitAgencyIds = this.deploymentService.getAppDeployment()?.showAbstractConstructionUnitAgencyIds;
  onOffAbstractConstructionUnit = false;

  //IGATESUPP-102806 Sở Tư Pháp - Bổ sung trạng thái và filter Hồ sơ đăng ký trên ứng dụng VNeID
  showFilterRegisterApplicationVNeID = this.deploymentService.getAppDeployment()?.showFilterRegisterApplicationVNeID ?  this.deploymentService.getAppDeployment().showFilterRegisterApplicationVNeID : 0;
  showFilterRegisterApplicationVNeIDAgencyId = this.deploymentService.getAppDeployment()?.showFilterRegisterApplicationVNeIDAgencyId ?  this.deploymentService.getAppDeployment().showFilterRegisterApplicationVNeIDAgencyId : [];
  checkShowFilterRegisterApplicationVNeID = false;
  
  //OS BDG: Begin
  isEnableLableOrganizationInformation = this.deploymentService?.env?.OS_BDG?.isEnableLableOrganizationInformation;
  
  //region VPC - IGATESUPP-101440 - Xác nhận đã nhận hồ sơ gốc
  vpcConfirmOriginalDossier = this.deploymentService.getAppDeployment()?.vpcConfirmOriginalDossier ? this.deploymentService.getAppDeployment()?.vpcConfirmOriginalDossier : 0;
  //endregion

   isAGG= this.deploymentService.getAppDeployment()?.traCuuToanCoQuanAGG == 1 ? true : false;
  showColUnitQTI = this.deploymentService.getAppDeployment()?.showColUnitQTI ? this.deploymentService.getAppDeployment()?.showColUnitQTI : 0; 

  showCreateSearchOwnerFullname = this.deploymentService.getAppDeployment()?.showCreateSearchOwnerFullname === 1;
  configColumTable: string[] = !!this.deploymentService?.getAppDeployment()?.displayedColumns?.tracuuToanCoquan ?
        Object.values(this.deploymentService?.getAppDeployment()?.displayedColumns?.tracuuToanCoquan) : this.displayedColumnsDefault;
  configDisplayColumns: boolean = this.deploymentService?.getAppDeployment()?.configDisplayColumns || false;
  
  //region VPC - IGATESUPP-105687 - Ủy quyền nhận kết quả hồ sơ
  vpcAuthorizeReceiveResult = this.deploymentService.getAppDeployment()?.vpcAuthorizeReceiveResult ? this.deploymentService.getAppDeployment()?.vpcAuthorizeReceiveResult : 0;
  //endregion

  //IGATESUPP-111250
  showSearchPhoneNumber = this.deploymentService.getAppDeployment()?.showSearchPhoneNumber === 1;

  // set query params
  paramsQuery = {
    code: '',
    identity: '',
    applicant: '',
    page: '1',
    size: this.env?.OS_KTM?.defaultPageSize ? this.env?.OS_KTM?.defaultPageSize : 10,
    procedure: '',
    nation: '',
    sector: '',
    province: '',
    district: '',
    ward: '',
    acceptFrom: '',
    acceptTo: '',
    resultReturnedFrom: '',
    resultReturnedTo: '',
    status: '',
    bpmNameId: '',
    remindId: '',
    sortId: '',
    organization: '',
    receivingKindId: '',
    documentNoLLTP: '',
    appointmentFrom: '',
    appointmentTo: '',
    receiptCode: '',
    taxCode: '',
    resPerson: '',
    dossierProcessingStatus: '',
    phoneNumberApply: '',
    vnpostStatus: '',
    applicantOrganization: '',
    ownerFullname: ''
  };

  viettelPostEnable = this.deploymentService.newConfigV2.viettelPostEnable;
  viettelPostReceiveResultsByAddress = this.deploymentService.newConfigV2.viettelPostReceiveResultsByAddress;
  twoLevelPublicAdministration  =  this.deploymentService.getAppDeployment()?.twoLevelPublicAdministration ? this.deploymentService.getAppDeployment()?.twoLevelPublicAdministration : false;

  constructor(
    private router: Router,
    private dossierService: DossierService,
    private humanService: HumanService,
    private userService: UserService,
    private activeRoute: ActivatedRoute,
    private envService: EnvService,
    private deploymentService: DeploymentService,
    private datePipe: DatePipe,
    private mainService: MainService,
    private dialog: MatDialog,
    private snackbarService: SnackbarService,
    private procedureService: ProcedureService,
    private adminLayoutNavComponent: AdminLayoutNavComponent,
    private keycloakService: KeycloakService,
    private apiProviderService: ApiProviderService,
    private procedureReportService: ProcedureReportService,
    private configService: ConfigService,
    private exportExcel: ExportExcelService,
    private qniStatisticsService: QNIStatisticService,
    private basedataService:BasedataService,
    private commonService: CommonService,
    private logmanService: LogmanService,
    private sectorService: SectorService,
    private bpmService: BpmService,
    private eReceiptService: EReceiptService,
    private adapterService: AdapterService,
    private agencyService: AgencyService,
    private processService: ProcessService,
    private notificationV2Service: NotificationV2Service,
    private trinamService: TrinamService,
    private basecatService: BasecatService,
    private notiService: NotificationService,
    private trackingService: TrackingBTTTTService,
    private changeDetectorRef: ChangeDetectorRef,
    private dossierOriginalService: DossierOriginalService,
    @Inject(LOCALE_ID) protected localeId: string,
  ) {
    (window as any).pdfMake.vfs = pdfFonts.pdfMake.vfs;
    this.dataSource = new MatTableDataSource(this.ELEMENTDATA);
    this.getConfig();
    if(this.showFilterPaymentStatus){
      if (this.showDossierFee) {
        this.displayedColumns = this.displayedColumnsPayFeeHCM;
      }
      else {
        this.displayedColumns = this.displayedColumnsQNM;
      }
    }
    if (this.deploymentService.env.sortMenuRemind.allowedMenu.includes('dossier/search')) {
      this.isSortMenuRemind = this.deploymentService.env.sortMenuRemind.enable;
    }
    this.tbthuedvcqgDataSource = new MatTableDataSource(this.TBTHUEDVCQG_ELEMENTDATA);
    this.setSortId()

    if (this.allowFilterAcceptedDateOptimization) {
      if (this.deploymentService.env.OS_HCM.allowFilterAcceptedDateOptimization.allowedMenu.includes('dossier/search')) {
        this.allowFilterAcceptedDateOptimization = true;
      } else {
        this.allowFilterAcceptedDateOptimization = false;
      }
    }
    // QBH layout
    if(this.qbhlistlayout == true)
    {
      this.displayedColumns = this.displayedColumnsQBH;
    }
    // QBH layout
    if(this.isHGIColums == true)
    {
      this.displayedColumns = this.displayedColumnsHGI;
    }

     //HPG
     if(this.hpglistlayout == true)
     {
       if(!this.dossierSearchShowAgency) {
         this.displayedColumnsHPG = this.displayedColumnsHPG.filter(column => column !== 'agency');
       }
       this.displayedColumns = this.displayedColumnsHPG;
     }

     if(this.isAGG){
      this.displayedColumns = ['select', 'code', 'procedureName', 'processingTime', 'profileOwner', 'pay', 'agency', 'status', 'action']
      }


     if (this.configDisplayColumns) {
         this.displayedColumns = this.configColumTable;
     }
     this.changeStyleTable();

     // set query params

     if (this.activeRoute.snapshot.queryParamMap.get('code') != null) {
      this.paramsQuery.code = this.activeRoute.snapshot.queryParamMap.get('code');
      this.searchForm.patchValue({
        code: this.paramsQuery.code
      });
    }

    if (this.activeRoute.snapshot.queryParamMap.get('identity') != null) {
      this.paramsQuery.identity = this.activeRoute.snapshot.queryParamMap.get('identity');
      this.searchForm.patchValue({
        identityNumber: this.paramsQuery.identity
      });
    }

    if (this.activeRoute.snapshot.queryParamMap.get('applicant') != null) {
      this.paramsQuery.applicant = this.activeRoute.snapshot.queryParamMap.get('applicant');
      this.searchForm.patchValue({
        applicantName: this.paramsQuery.applicant
      });
    }

    if (this.activeRoute.snapshot.queryParamMap.get('page') != null) {
      this.paramsQuery.page = this.activeRoute.snapshot.queryParamMap.get('page');
    }
    if (this.activeRoute.snapshot.queryParamMap.get('size') != null) {
      this.paramsQuery.size = this.activeRoute.snapshot.queryParamMap.get('size');
    }

    if (this.activeRoute.snapshot.queryParamMap.get('procedure') != null) {
      this.paramsQuery.procedure = this.activeRoute.snapshot.queryParamMap.get('procedure');
      this.searchForm.patchValue({
        advProcedure: this.paramsQuery.procedure
      })
    };

    if (this.activeRoute.snapshot.queryParamMap.get('sector') != null) {
      this.paramsQuery.sector = this.activeRoute.snapshot.queryParamMap.get('sector');
      this.searchForm.patchValue({
        advSector: this.paramsQuery.sector
      });
    }

    if (this.activeRoute.snapshot.queryParamMap.get('applyMethod') != null) {
      this.searchForm.patchValue({
        advApplyMethod: this.activeRoute.snapshot.queryParamMap.get('applyMethod')
      });
    }
    
    if (this.activeRoute.snapshot.queryParamMap.get('province') != null) {
      this.paramsQuery.province = this.activeRoute.snapshot.queryParamMap.get('province');
      this.searchForm.patchValue({
        province: this.paramsQuery.province
      });
    }
    if (this.activeRoute.snapshot.queryParamMap.get('district') != null) {
      this.paramsQuery.district = this.activeRoute.snapshot.queryParamMap.get('district');
      this.searchForm.patchValue({
        district: this.paramsQuery.district
      });
    }
    if (this.activeRoute.snapshot.queryParamMap.get('ward') != null) {
      this.paramsQuery.ward = this.activeRoute.snapshot.queryParamMap.get('ward');
      this.searchForm.patchValue({
        ward: this.paramsQuery.ward
      });
    }
    
    if (this.activeRoute.snapshot.queryParamMap.get('acceptFrom') != null) {
      this.paramsQuery.acceptFrom = this.activeRoute.snapshot.queryParamMap.get('acceptFrom');
      if (this.paramsQuery.acceptFrom !== '') {
        this.searchForm.patchValue({
          advAcceptFrom: new Date(this.datePipe.transform(this.paramsQuery.acceptFrom, 'yyyy-MM-dd'))
        });
      }
    }

    if (this.activeRoute.snapshot.queryParamMap.get('acceptTo') != null) {
      this.paramsQuery.acceptTo = this.activeRoute.snapshot.queryParamMap.get('acceptTo');
      if (this.paramsQuery.acceptTo !== '') {
        this.searchForm.patchValue({
          advAcceptTo: new Date(this.datePipe.transform(this.paramsQuery.acceptTo, 'yyyy-MM-dd'))
        });
      }
    }
    
    // if (this.activeRoute.snapshot.queryParamMap.get('sectorName') != null) {
    //   this.sectorName = this.activeRoute.snapshot.queryParamMap.get('sectorName');
    // }
    
    if (this.activeRoute.snapshot.queryParamMap.get('status') != null) {
      this.paramsQuery.status = this.activeRoute.snapshot.queryParamMap.get('status');
      this.searchForm.patchValue({
        advTaskStatusId: this.paramsQuery.status
      });
    }
    
    if (this.activeRoute.snapshot.queryParamMap.get('remindId') != null) {
      this.remindId = this.paramsQuery.bpmNameId = this.activeRoute.snapshot.queryParamMap.get('remindId');
      this.searchForm.patchValue({
        remindId: this.paramsQuery.remindId
      });
    }
    if (this.activeRoute.snapshot.queryParamMap.get('sortId') != null) {
      this.sortId = this.paramsQuery.sortId = this.activeRoute.snapshot.queryParamMap.get('sortId');
      this.searchForm.patchValue({
        sortCtrl: this.paramsQuery.sortId
      });
    }

    if (this.activeRoute.snapshot.queryParamMap.get('organization') != null) {
      this.paramsQuery.appointmentFrom = this.activeRoute.snapshot.queryParamMap.get('organization');
      if (this.paramsQuery.appointmentFrom !== '') {
        this.searchForm.patchValue({
          advAddressOrganization: this.paramsQuery.appointmentFrom
        });
      }
    }
    
    if (this.activeRoute.snapshot.queryParamMap.get('appointmentFrom') != null) {
      this.paramsQuery.appointmentFrom = this.activeRoute.snapshot.queryParamMap.get('appointmentFrom');
      if (this.paramsQuery.appointmentFrom !== '') {
        this.searchForm.patchValue({
          advAppointmentFrom: new Date(this.datePipe.transform(this.paramsQuery.appointmentFrom, 'yyyy-MM-dd'))
        });
      }
    }
    if (this.activeRoute.snapshot.queryParamMap.get('appointmentTo') != null) {
      this.paramsQuery.appointmentTo = this.activeRoute.snapshot.queryParamMap.get('appointmentTo');
      if (this.paramsQuery.appointmentTo !== '') {
        this.searchForm.patchValue({
          advAppointmentTo: new Date(this.datePipe.transform(this.paramsQuery.appointmentTo, 'yyyy-MM-dd'))
        });
      }
    }

    if (this.activeRoute.snapshot.queryParamMap.get('resultReturnedFrom') != null) {
      this.paramsQuery.resultReturnedFrom = this.activeRoute.snapshot.queryParamMap.get('resultReturnedFrom');
      if (this.paramsQuery.resultReturnedFrom !== '') {
        this.searchForm.patchValue({
          avdResultReturnedFrom: new Date(this.datePipe.transform(this.paramsQuery.resultReturnedFrom, 'yyyy-MM-dd'))
        });
      }
    }

    if (this.activeRoute.snapshot.queryParamMap.get('resultReturnedTo') != null) {
      this.paramsQuery.resultReturnedTo = this.activeRoute.snapshot.queryParamMap.get('resultReturnedTo');
      if (this.paramsQuery.resultReturnedTo !== '') {
        this.searchForm.patchValue({
          avdResultReturnedTo: new Date(this.datePipe.transform(this.paramsQuery.resultReturnedTo, 'yyyy-MM-dd'))
        });
      }
    }

    if (this.activeRoute.snapshot.queryParamMap.get('receiptCode') != null) {
      this.paramsQuery.receiptCode = this.activeRoute.snapshot.queryParamMap.get('receiptCode');
      this.searchForm.patchValue({
        receiptCode: this.paramsQuery.receiptCode
      });
    }

    if (this.activeRoute.snapshot.queryParamMap.get('taxCode') != null) {
      this.paramsQuery.taxCode = this.activeRoute.snapshot.queryParamMap.get('taxCode');
      this.searchForm.patchValue({
        taxCode: this.paramsQuery.taxCode
      });
    }
    
    if (this.activeRoute.snapshot.queryParamMap.get('resPerson') != null) {
      this.paramsQuery.resPerson = this.activeRoute.snapshot.queryParamMap.get('resPerson');
      this.searchForm.patchValue({
        resPerson: this.paramsQuery.resPerson
      });
    }

    if (this.activeRoute.snapshot.queryParamMap.get('phoneNumberApply') != null) {
      this.paramsQuery.phoneNumberApply = this.activeRoute.snapshot.queryParamMap.get('phoneNumberApply');
      this.searchForm.patchValue({
        phoneNumberApply: this.paramsQuery.phoneNumberApply
      });
    }

    if (this.activeRoute.snapshot.queryParamMap.get('vnpostStatus') != null) {
      this.paramsQuery.vnpostStatus = this.activeRoute.snapshot.queryParamMap.get('vnpostStatus');
      this.searchForm.patchValue({
        vnpostStatus: this.paramsQuery.vnpostStatus
      });
    }

    if (this.activeRoute.snapshot.queryParamMap.get('applicantOrganization') != null) {
      this.paramsQuery.applicantOrganization = this.activeRoute.snapshot.queryParamMap.get('applicantOrganization');
      this.searchForm.patchValue({
        applicantOrganization: this.paramsQuery.applicantOrganization
      });
    }

    if (this.activeRoute.snapshot.queryParamMap.get('ownerFullname') != null) {
      this.paramsQuery.ownerFullname = this.activeRoute.snapshot.queryParamMap.get('ownerFullname');
      this.searchForm.patchValue({
        ownerFullname: this.paramsQuery.ownerFullname
      });
    }
    

    // if (this.activeRoute.snapshot.queryParamMap.get('dossierProcessingStatus') != null) {
    //   this.paramsQuery.dossierProcessingStatus = this.activeRoute.snapshot.queryParamMap.get('dossierProcessingStatus');
    //   this.searchForm.patchValue({
    //     dossierProcessingStatus: this.paramsQuery.dossierProcessingStatus
    //   });
    // }

  }
  async ngOnInit(): Promise<void> {
    const userAgency = JSON.parse(localStorage.getItem('userAgency'));
    // if(this.allowFilterAppliedDateOptimizationCustom.enable && this.allowFilterAppliedDateOptimizationCustom.allowedMenu.includes('dossier/search')){
    //   if(!!userAgency?.code){
    //     for(let i in this.allowFilterAppliedDateOptimizationCustom.agencyUsed){
    //       userAgency.code.split('.').reduce((acc, part, index) => {
    //         const partialValue = index === 0 ? part : `${acc[index - 1]}.${part}`;
    //         acc.push(partialValue);
    //         if(partialValue == this.allowFilterAppliedDateOptimizationCustom.agencyUsed[i]){
    //           this.isShowFilterApplyDate =true;
    //           this.rangeLimitDossierSearch = this.allowFilterAppliedDateOptimizationCustom.rangeLimitDossierSearch
    //         }
    //         return acc;
    //       }, []);
    //     }
    //   }
    // }
    if(this.allowFilterAppliedDateOptimizationCustom.enable
        && this.allowFilterAppliedDateOptimizationCustom.allowedMenu.includes('dossier/search')
        && tUtils.isAllowedAgency(localStorage, this.allowFilterAppliedDateOptimizationCustom.agencyUsed)
    ){
      this.isShowFilterApplyDate =true;
      this.rangeLimitDossierSearch = this.allowFilterAppliedDateOptimizationCustom.rangeLimitDossierSearch
    }
    if(this.customMenuTaskRemindDossierSearch.enable && tUtils.isAllowedAgency(localStorage, this.customMenuTaskRemindDossierSearch.agencyIds)){
      this.changeMenuTaskRemind = true;
    }
    if(this.allowFilterAppliedDateOptimization?.enable && this.allowFilterAppliedDateOptimization?.allowedMenu?.includes('dossier/search')){
      this.isShowFilterApplyDate = true
    }
    if(this.showFilterPaymentStatus){
      this.selectedValue = ['','1','2', '3','4'];
      this.tempSelectedValue = this.selectedValue;
    }
    if(this.enableSyncLGSPHCM == true ){
      if( this.listAgencyIdSyncLGSPHCM.filter( item => item == userAgency.id || item == userAgency?.parent?.id  || item == userAgency?.ancestors?.id ).length > 0){
        this.showRemindSyncLGSPHCM = true;
      }
    }
    if(this.showRemindSyncLGSPHCM){
      let isLGSPHCM = true;
    }
    if(this.enablefindResPerson && this.listAgencyFindResperon.filter(item => item == userAgency.id || item == userAgency?.parent?.id || item == userAgency?.ancestors?.id).length > 0){
      this.isShowResPerson = true ;
    }

    if(this.enableHideTotalDossier && this.agencyHideTotalDossier.filter(item => item == userAgency.id || item == userAgency?.parent?.id || item == userAgency?.ancestors?.id).length > 0){
      this.isShowMenuRemindTask = true ;
      this.flex = 100;
    }

    if(this.enableFilterSectorIs){
       if(this.agencyUnitEnableFilterSector.filter(item => item == userAgency.id || item == userAgency?.parent?.id || item == userAgency?.ancestors?.id).length > 0) {
        this.isShowFilterSector = true;
       }
    }
    if(this.env?.OS_QNI?.qni_advancedsearch === true){
      this.qni_advancedsearch= this.env?.OS_QNI?.qni_advancedsearch;
    }
    if(this.env?.OS_QNI?.ExportPageDossiersToExcelQNI === true){
      this.ExportPageDossiersToExcelQNI= this.env?.OS_QNI?.ExportPageDossiersToExcelQNI;
    }
    //IGATESUPP-51996
    if (this.agencyShowFilterSortHCM.filter(id =>
      id == userAgency.id
      || id == userAgency?.parent?.id
      || id == userAgency?.ancestors?.id
    ).length > 0) {
      // this.sortHcmId = 4;
      this.isShowFilterSortHCM = true;
    }
    // tslint:disable-next-line: max-line-length
    this.size = Number(this.activeRoute.snapshot.queryParamMap.get('size')) === 0 ? this.size : Number(this.activeRoute.snapshot.queryParamMap.get('size'));
    // tslint:disable-next-line: max-line-length
    this.page = Number(this.activeRoute.snapshot.queryParamMap.get('page')) === 0 ? this.page : Number(this.activeRoute.snapshot.queryParamMap.get('page'));
    this.mainService.setPageTitle(this.pageTitle[localStorage.getItem('language')]);
    const permissions = this.userService.getUserPermissions();

    for (const p of permissions) {
      if (p.permission.code === 'admin' || p.permission.code === 'oneGateAdminMaster') {
        this.isAdmin = true;
      }
      if ( ['admin', 'oneGateAdminMaster', 'oneGateDossierLookup'].includes(p.permission.code)){
         this.isOneGateOfficer = true;
      }
      if (p.permission.code === 'admin' || p.permission.code === 'oneGateAdminMaster' || p.permission.code === 'oneGateDossierDelete') {
        this.hasDossierDeletePermission = true;
      }
      if (p.permission.code === 'qni_listsearch') {
        this.qni_listsearch = true;
      }
      if ( ['oneGateReleaseViewReceipt'].includes(p.permission.code)){
        this.allowShowButtonReleaseViewReceipt = true;
      }

      if ( ['oneGatePrintPage'].includes(p.permission.code)){
        this.allowShowButtonPrint = true;

      }
      if(['oneGateUpdatePaymentMethod'].includes(p.permission.code)){
        this.oneGateUpdatePaymentMethod = true;
      }
      if ( p.permission.code == this.roleDoiGPLX){
        this.hasExcelGPLXRole = true;
      }
      if (p.permission.code === 'oneGateDossierAccepter' || p.permission.code === 'oneGateDossierReception' || p.permission.code === 'oneGateDossierOnlineReception') {
        this.receivingPermission = true;
      }
    }
    console.log(this.isAdmin);
    if (this.userAgency !== null && this.userAgency !== undefined) {
      // this.agencyId = this.isAdmin ? '' : this.userAgency.id;
      if (!!this.userAgency.parent && !!this.userAgency.parent.id && !this.isAdmin) {
        this.agencyIdForProcedure = this.userAgency.parent.id;
      } else if (this.userAgency.id !== this.env?.rootAgency?.id || this.isAdmin) {
        this.agencyIdForProcedure = this.userAgency.id;
      }
      this.keySearchSectorAgency = '&agency-id=' +  this.agencyIdForProcedure;
    }
    // tslint:disable-next-line:prefer-for-of
    for (let i = 0; i < this.receptionForm.length; i++) {
      const arrName: any = {};
      // tslint:disable-next-line:prefer-for-of
      for (let j = 0; j < this.receptionForm[i].name.length; j++) {
        if (Number(localStorage.getItem('languageId')) === this.receptionForm[i].name[j].languageId) {
          arrName.id = this.receptionForm[i].id;
          arrName.name = this.receptionForm[i].name[j].name;
          arrName.content = this.receptionForm[i].name[j].content;
        }
      }
      this.arrReceptionForm.push(arrName);
      if(this.isShowAgencyEnterprise == 1){
        this.flexGtSm4Column= 18;
        this.flexGtSm4ColumnName=20;
      }
    }
    this.getListAgency();
    // IGATESUPP-76910
    if (this.dossierSearchShowAgencyUnit) {
      this.searchAgencyUnitCtrl.valueChanges.pipe(takeUntil(this.onDestroy)).subscribe(() => {
        if (this.searchAgencyUnitTimeout != null && this.searchAgencyUnitTimeout != undefined) {
          clearTimeout(this.searchAgencyUnitTimeout);
        }
        this.searchAgencyUnitTimeout = setTimeout(async () => {
          this.pageAgencyUnit = 0;
          this.isLastAgencyUnit = false;
          await this.getListAgencyUnit({keyword: this.searchAgencyUnitCtrl.value});
          if (this.listAgencyUnit.find(e => e == this.searchForm.get('taskAgencyCtrl').value) == undefined) {
            this.searchForm.get('taskAgencyCtrl').setValue("");
          }
        }, 1000);
      });
    }
    if (this.dossierSearchShowAssignee) {
      this.searchAssigneeCtrl.valueChanges.pipe(takeUntil(this.onDestroy)).subscribe(() => {
        if (this.searchAssigneeTimeout != null && this.searchAssigneeTimeout != undefined) {
          clearTimeout(this.searchAssigneeTimeout);
        }
        this.searchAssigneeTimeout = setTimeout(async () => {
          this.pageAssignee = 0;
          this.isLastAssignee = false;
          await this.getListAssignee({fullname: this.searchAssigneeCtrl.value});
          if (this.listAssignee.find(e => e == this.searchForm.get('taskAssigneeCtrl').value) == undefined) {
            this.searchForm.get('taskAssigneeCtrl').setValue("");
          }
        }, 1000);
      })
    }
    // END IGATESUPP-76910
    this.getListNation();
    this.getListDossierTaskName();
    this.getListSectorScroll();
    this.getListProcedure('');
    this.searchProcedureCtrl.valueChanges.pipe(takeUntil(this.onDestroy)).subscribe(() => {
      if (this.isSelectedProcedure) {
        this.isSelectedProcedure = false;
      } else {
        this.isFullListProcedure = false;
        this.filterProcedure();

        if (this.listProcedurePage == 0) {
          this.listProcedurePage++;
        } else {
          this.listProcedurePage = 0;
          this.listProcedure = [];
        }
      }
    });
    this.getDossierTaskStatus();
    this.getMaxDeleteDossierMulti();
    this.getAccepterInfo();
    this.checkagencyAcceptDurationByWorkingTime();
    await this.checkShowAbstractConstructionUnit();
    console.log('checkProvineAdmin        ', this.checkProvineAdmin);
    this.countDossier = true;
    if (false) {  // FIXME
    await this.getCountSyncLGSP();
    }
    if(this.isShowMenuRemindTask == false ){
      await this.getRemindMenuTask();
    }
    await this.getListConfigTemplate();
    this.checkPremissonQBH();
    // Khong load danh sach ban dau - IGATESUPP-6445
    // this.onConfirmSearch();
    if (this.userService.checkPermissionExists('oneGateDossierDelete')) {
      this.hasDossierDeletePermission = true;
    }
    if (this.userService.checkPermissionExists('oneGateDeleteDossier')) {
      this.oneGateDeleteDossier = '1';
    }
    if(this.templateDeliveryId !== undefined){
      this.configService.getDetailTemplate(this.templateDeliveryId).subscribe(data => {
        this.reportType = data?.file?.path;
      }, error => {
        console.log(error);
      });
    }
    if(this.env?.vnpost?.config === '1' && this.env?.vnpost)
    {
      this.getListVNPostStatus();
      this.searchVnPostCtrl.valueChanges.pipe(takeUntil(this.onDestroy)).subscribe(()=>{
        this.filterVnpost();
      })
    }
    this.showNote = tUtils.isAllowedAgency(localStorage, this.deploymentService.env.OS_HCM.showNote.allowAgency);
    if (this.showNote) {
      this.displayedColumns.splice(6, 0, 'note');
    }
    if(this.qbhlist_collapse){
      this.xpandStatus = true;
      if(this.depConfig?.addressDefault?.nationId){
        const nationId = this.searchForm.controls['advNation'].value;
        if(!nationId){
          const defaultNationId = this.depConfig?.addressDefault?.nationId;
          this.searchForm.patchValue({advNation: defaultNationId});
          this.advNationChange({value: defaultNationId});
        }
        setTimeout(() => {
          const provinceId = this.searchForm.controls['advProvince'].value;
          if(!provinceId){
            const defaultProvinceId = this.depConfig?.addressDefault?.provinceId;
            this.searchForm.patchValue({advProvince: defaultProvinceId});
            this.advProvinceChange({value: defaultProvinceId});
          }
        }, 2000);
      }
    }

    await this.checkShowFilterRegisterApplicationVNeIDValue();

    if(this.allowFilterAcceptedDateOptimization){
      if(this.rangeLimitAcceptedDateFilter > 0){
        this.fromDate = tUtils.getPastDate(this.rangeLimitAcceptedDateFilter);
        this.searchForm.patchValue({
          advAcceptFrom: this.fromDate,
          advAcceptTo: this.toDate,
        })
      } else {
      this.searchForm.patchValue({
        advAcceptFrom: this.toDate,
        advAcceptTo: this.toDate,
      })
    }
    }
    if(this.isShowFilterApplyDate){
      if(this.rangeLimitDossierSearch > 0){
        this.fromDate = tUtils.getPastDate(this.rangeLimitDossierSearch);
        this.searchForm.patchValue({
          advFilingFrom: this.fromDate,
          advFilingTo: this.toDate,
        })
      } else {
      this.searchForm.patchValue({
        advFilingFrom: this.toDate,
        advFilingTo: this.toDate,
      })
    }

    }
    let agencyId = this.getAgencyId();
    try{
      if(agencyId){
        this.rootAgency = await this.agencyService.getRootAgency(agencyId);
      }
    }catch(err){
      this.rootAgency = JSON.parse(localStorage.getItem('userAgency'));
    }

    if(this.allowFilterPaymentStatusListAgency && this.allowFilterPaymentStatusListAgency.length > 0 && this.checkAgency()){
      this.isAllowFilterPaymentStatusListAgency = true;

    }
    if (false) {
    if(this.isAllowFilterPaymentStatusListAgency == false){
      this.displayedColumns = this.displayedColumnsPayFeeNoPayHCM;
    }
    }
    if(!this.isAllowFilterPaymentStatusListAgency){
      this.displayedColumns = this.displayedColumns.filter(item => item != 'pay')
    }
    if(this.isAGG){
      this.displayedColumns = ['select', 'code', 'procedureName', 'processingTime', 'profileOwner', 'pay', 'agency', 'status', 'action']
      }

    if(this.dossierReceiptCreationStatusConfig.enable){
      this.showReceiptCreationStatusWhiteList = await this.agencyService.getAgencyById(userAgency?.id).pipe(
        catchError((err)=>{
          return null
        }),
        map((rs)=>{
          return [rs.id,...(rs.ancestors ??[]).map(as=>as.id)]
        })
      ).toPromise();
     }

    if (this.deploymentService.env.OS_HGG.hideDeleteButtonDossier) {
      this.hideDeleteButtonDossier = true;
    }
    if(this.isSearchHGI){
      this.onConfirmSearch();
    }
    if(this.showColUnitQTI == 1){
      if(this.displayedColumns.indexOf('agency') == -1)
      this.displayedColumns.splice(this.displayedColumns.length -3,0,'agency');
    }
  }


  changeFromDate($event){
    const formObj = this.searchForm.getRawValue();

    if (this.rangeLimitAcceptedDateFilter > 0) {
      const fromDate = new Date(formObj.advAcceptFrom);
      const toDate = new Date(formObj.advAcceptTo);
      let sumDate = Number(toDate) - Number(fromDate);

      if (sumDate > 0) {
        let constDate = Math.floor(sumDate / 1000 / 60 / 60 / 24);
        if (constDate > this.rangeLimitAcceptedDateFilter) {
          let toDate = tUtils.getFutureDate(this.rangeLimitAcceptedDateFilter, formObj.advAcceptFrom);
          this.searchForm.patchValue({
            advAcceptTo: toDate
          })
        }
      }
    }
  }

  changeToDate($event) {
    const formObj = this.searchForm.getRawValue();

    if (this.rangeLimitAcceptedDateFilter > 0) {
      const fromDate = new Date(formObj.advAcceptFrom);
      const toDate = new Date(formObj.advAcceptTo);
      let sumDate = Number(toDate) - Number(fromDate);

      if (sumDate > 0) {
        let constDate = Math.floor(sumDate / 1000 / 60 / 60 / 24);
        if (constDate > this.rangeLimitAcceptedDateFilter) {
          let fromDate = tUtils.getPastDate(this.rangeLimitAcceptedDateFilter, formObj.advAcceptTo);
          this.searchForm.patchValue({
            advAcceptFrom: fromDate
          })
        }
      }
    }
  }

  changeAppliedFromDate($event) {
    const formObj = this.searchForm.getRawValue();

    if (this.rangeLimitDossierSearch > 0) {
      const fromDate = new Date(formObj.advFilingFrom);
      const toDate = new Date(formObj.advFilingTo);
      let sumDate = Number(toDate) - Number(fromDate);

      if (sumDate > 0) {
        let constDate = Math.floor(sumDate / 1000 / 60 / 60 / 24);
        if (constDate > this.rangeLimitDossierSearch) {
          let toDate = tUtils.getFutureDate(this.rangeLimitDossierSearch, formObj.advFilingFrom);
          this.searchForm.patchValue({
            advFilingTo: toDate
          })
        }
      }
    }
  }


  changeAppliedToDate($event) {
    const formObj = this.searchForm.getRawValue();
    if (this.rangeLimitDossierSearch > 0) {
      const fromDate = new Date(formObj.advFilingFrom);
      const toDate = new Date(formObj.advFilingTo);
      let sumDate = Number(toDate) - Number(fromDate);

      if (sumDate > 0) {
        let constDate = Math.floor(sumDate / 1000 / 60 / 60 / 24);
        if (constDate > this.rangeLimitDossierSearch) {
          let fromDate = tUtils.getPastDate(this.rangeLimitDossierSearch, formObj.advFilingTo);
          this.searchForm.patchValue({
            advFilingFrom: fromDate
          })
        }
      }
    }
  }


  checkAgency(){
    try {
      if(this.allowFilterPaymentStatusListAgency && this.allowFilterPaymentStatusListAgency.length > 0){
        if(this.rootAgency && this.allowFilterPaymentStatusListAgency.find(o => o == this.rootAgency.code)){
          return true;
        }
      }
    } catch (error) {
      console.log(error);
    }
    return false;
  }
  getAgencyId(): string | null {
    const userAgency = JSON.parse(localStorage.getItem('userAgency'));
    if (userAgency){
        return userAgency.id;
    }
    return null;
  }

  onShowFinancialObligations(){
    let data = {
      messageComment : this.showFinancialObligations.messageComment,
      remindProcessDefinitionTaskId : this.showFinancialObligations.remindProcessDefinitionTaskId,
      approvalDataType : this.showFinancialObligations.approvalDataType
    }
    const dialogRef = this.dialog.open(FinancialObligationsComponent, {
      minWidth: '50%',
      data: data,
      disableClose: true,
      autoFocus: false
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
      if (dialogResult) {
        this.onConfirmSearch();
      }
    });
  }
  checkPermissionShowNote(currentTask){
    let rs = false;
    let userId = localStorage.getItem("tempUID");
    //let checkPer = this.userService.checkPermissionExists("oneGateNoteHCM") && this.showNote == true ? true : false;
    let checkPer = this.deploymentService.env.OS_HCM.showNote.enable && this.userService.checkPermissionExists("oneGateNoteHCM") && this.showNote ? true : false;
    if (Array.isArray(currentTask) && currentTask.length) {
      currentTask.forEach((item)=>{
        if (item.assignee.id == userId && checkPer) {
          console.log("đúng dk show note");
          rs = true;
        }else{

        }
      })
    }
    this.checkNotePermistionEnable = rs;
    console.log("this.checkNotePermistionEnable" , this.checkNotePermistionEnable);
  }

  viewVNPOSTDetail(row: any) {
    if (!row?.itemCodeVNPost) {
      const msgObj = {
        vi: 'Mã vận đơn không tồn tại',
        en: 'VNPOST Code is missing'
      };
      this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
      return;
    };
    
    const url = `https://vietnampost.vn/ca-nhan/chuyen-phat/chuyen-phat-trong-nuoc?code=${row?.itemCodeVNPost}&_captcha=C_CAP#lookup`;
    window.open(url, '_blank');
  }
  
  

  checkPermistionNote(currentTask){
    let check = false;
    let userId = localStorage.getItem("tempUID");
    let checkPer = this.deploymentService.env.OS_HCM.showNote.enable && this.userService.checkPermissionExists("oneGateNoteHCM") && this.showNote ? true : false;
    if (Array.isArray(currentTask) && currentTask.length) {
      currentTask.forEach((item)=>{
        if (item?.assignee?.id == userId && checkPer) {
          console.log("đúng dk edit note");
          check = true;
        }
      })
    }
    return check;
  }

  onShowNote(row){
    let data = {
      listNote : row?.extendHCM?.note,
      dossierId : row.id
    }
    const dialogRef = this.dialog.open(NoteComponent, {
      minWidth: '70%',
      data: data,
      disableClose: true,
      autoFocus: false
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
      this.onConfirmSearch();
    });
  }

  // Chỉnh sửa ghi chú trực tiếp----------------------------------------------------------------------
  checkNullNote(listNote){
    let check = false;
    listNote.forEach((item)=>{
      if (item.describe === '' || item.describe === null || item.describe === undefined) {
        check = true;
      }
    });
    return check;
  }

  async deleteRow(item, row) {
    let dossierIdNote = row.id;
    row.extendHCM.note = row.extendHCM.note.filter(i => i !== item);
    try {
      let data = {listNote : row.extendHCM.note };
      const rs = await this.dossierService.putDossierNote(dossierIdNote, data).toPromise();
      if (rs.affectedRows == 1) {
        const msgObj = {
          vi: 'Xóa ghi chú thành công',
          en: 'Delete note success'
        };
        this.snackbarService.openSnackBar(1, msgObj[this.selectedLang], '', 'success_notification', this.config.expiredTime);
        }else{
          const msgObj = {
            vi: 'Xóa ghi chú không thành công',
            en: 'Delete note failed'
          };
          this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
        }
    }catch (error) {}

  }

  async syncPostUpdateFinishDossierVBDLIS(code) {
    let isVbdlis = true;
    let messageError = "";
    const responseCreateVbdlis = await this.dossierService.postUpdateFinishDossierVbdlis(code, this.vbdlisHCMConfigId).toPromise().catch(error => {
      isVbdlis = false;
      console.log(error);
    });
    isVbdlis = responseCreateVbdlis.affectedRows == 0 ? false : true;
    messageError = responseCreateVbdlis.message ? responseCreateVbdlis.message : "Cập trạng thái đã trả kết quả VBDLIS thất bại!";

    if (isVbdlis == true) {
      const msgObj = {
        vi: 'Cập trạng thái đã trả kết quả VBDLIS thành công!',
        en: 'Successful update finish dossier VBDLIS!',
      };
      this.snackbarService.openSnackBar(
        1,
        msgObj[this.selectedLang],
        '',
        'success_notification',
        this.config.expiredTime
      );
    } else {
      const msgObj = {
        vi: messageError,
        en: 'Failed update finish dossier VBDLIS!'
      };
      this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', 2000);
    }
  }

  async save(row) {
    try {
      let data = { listNote: row?.extendHCM?.note };
      if (!this.checkNullNote(row?.extendHCM?.note)){
        const rs = await this.dossierService.putDossierNote(row.id, data).toPromise();
        if (rs.affectedRows == 1) {
          const msgObj = {
            vi: 'Thêm ghi chú thành công',
            en: 'Add note success'
          };
          this.snackbarService.openSnackBar(1, msgObj[this.selectedLang], '', 'success_notification', this.config.expiredTime);
        } else {
          const msgObj = {
            vi: 'Thêm ghi chú không thành công',
            en: 'Add note failed'
          };
          this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
        }
      }else{
        const msgObj = {
          vi: 'Ghi chú không được phép rỗng',
          en: 'Add note failed, fill this note pls!'
        };
        this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
      }
    } catch (error) {}
  }

  addItem(row) {
    let data = {
      describe : "ghi chú mới",
      editing: false
    }
    const noteList = [];
    noteList.push(data);
    if (row?.extendHCM === undefined || row?.extendHCM === null || row?.extendHCM === ''){
      const temp = {note: noteList};
      row.extendHCM = temp;
      this.save(row);
    }
    else{
      if(row?.extendHCM?.note === undefined || row?.extendHCM?.note === null || row?.extendHCM?.note === ''){
        row.extendHCM.note = noteList;
      }else{
        row.extendHCM.note.push(data);
      }
      this.save(row);

    }
  }

  editItem(row, index) {
    row.extendHCM.note[index].editing = true;
    this.showAddButton = false;
  }

  saveItem(row, index) {
    const editedNote = row.extendHCM.note[index];
    this.save(row);
    if (this.checkNullNote(row?.extendHCM?.note)){
      editedNote.editing = true;
      this.showAddButton = false;
    } else{
      editedNote.editing = false;
      this.showAddButton = true;
    }
  }

  deleteItem(item, row) {
    this.deleteRow(item, row);
    this.showAddButton = true;
  }

  //---------------------------------------------------------------------

  filterVnpost(){
    let search = this.searchVnPostCtrl.value.trim();

    if (!search) {
      // this.procedureFiltered.next(this.procedures.slice());

      this.listVnpostStatus = [];

      this.getListVNPostStatus();

      return;
    } else {
      search = search.toLowerCase();
    }
    if (this.listVnpostStatus.filter(item => item.name.toLowerCase().indexOf(search) > -1).length > 0) {
       this.listVnpostStatus = this.listVnpostStatus.filter(item => item.name.toLowerCase().indexOf(search) > -1)
    }
  }
  getListVNPostStatus()
  {
    if (this.env?.vnpost?.listStatus) {
      this.listVnpostStatus = this.env?.vnpost?.listStatus;
    } else {
      this.listVnpostStatus = [
        {
          id: -1,
          name: "Tất cả"
        },
        {
          id: 100,
          name: "Thêm mới đơn hàng thành công"
        },
        {
          id: 101,
          name: "Thêm mới đơn hàng thất bại"
        },
        {
          id: 4,
          name: "Nhận tin"
        },
        {
          id: 8,
          name: "Báo hủy"
        },
        {
          id: 13,
          name: "Đến lấy nhưng chưa có hàng lần 1"
        },
        {
          id: 14,
          name: "Đến lấy nhưng chưa có hàng lần 2"
        },
        {
          id: 15,
          name: "Đến lấy nhưng chưa có hàng lần 3"
        },
        {
          id: 16,
          name: "Đến lấy nhưng chưa có hàng trên 3 lần"
        },
        {
          id: 21,
          name: "Hủy"
        },
        {
          id: 22,
          name: "Đã nhận báo hủy"
        },
        {
          id: 102,
          name: "Nhận hàng thành công"
        }
      ];
    }
    this.listVnpostStatus = this.listVnpostStatus.sort((a, b) => a.name.localeCompare(b.name));
    this.searchVnPostCtrl.valueChanges.pipe(takeUntil(this.onDestroy)).subscribe(()=>{
      this.filterVnpost();
    })
  }



  createPrintBill(reportType, dossierId, id?) {
    let url = '';
    this.keycloakService.getToken().then(token => {
        let dataPost = {};
        if(!!this.enablePrintBillNew){
            dataPost = {
                "report": reportType,
                "apiGateway": this.apiProviderService.getUrl('digo', 'padman'),
                "dossierId":  dossierId,
                "id": (id != null ? id : ''),
            };
        }else{
      url = this.config.birtviewerURL
        + 'output?__report=' + reportType
        + '&&displayNone=true&__dpi=96&__format=html&__pageoverflow=0&__overwrite=false'
        + '&token=' + token
        + '&apiGateway=' + this.apiProviderService.getUrl('digo', 'padman')
        + '&dossierId=' + dossierId
        + (id != null ? '&id=' + id : '');
        }
      //this.procedureReportService.getReportTemplate(url).subscribe(data => {
      //  console.log(data);
      //}, err => {
        const dialogData = new ConfirmPrintTemplateDialogModel(url, this.enablePrintBillNew, dataPost);
        const dialogRef = this.dialog.open(PrintTemplateComponent, {
          minWidth: '55vw',
          minHeight: '90vh',
          data: dialogData,
          autoFocus: false
        });
      //});
    });
  }

  async sendLLTPLgspHCM(dossierId, code, procostTypeListId?: any) {
    if (this.checkHcmLyLichTuPhap && this.callApiLGSPAfterGotReceipt.showBtnTransferToLGSP) {
      await this.getDossierFee(dossierId);
      //if (this.numberOfReceipt == 0 && !!this.dossier.task && this.dossier.task.length !== 0) {

        let dossierFeeIds = [];

        if (this.dossierFee && this.dossierFee.length > 0) {
          this.dossierFee.forEach(item => {
            if (this.callApiLGSPAfterGotReceipt.procostTypeId == item.procost.type.id || this.callApiLGSPAfterGotReceipt.procostTypeListId.includes(item.procost.type.id)) {
              dossierFeeIds.push(item.id);
            }
          });

          this.numberOfReceipt = await this.eReceiptService.getNumberOfReceipt(dossierFeeIds).toPromise();
        }
      //}
      if(!this.callApiLGSPAfterGotReceipt.procostTypeListId || this.callApiLGSPAfterGotReceipt.procostTypeListId?.length == 0){
        //Không đẩy HS với Phí cấp bản sao
        if(procostTypeListId){
          let check = procostTypeListId.includes(this.callApiLGSPAfterGotReceipt.procostTypeId);
          if(!check){
            this.numberOfReceipt = 0;
          }
        }
      }

      if (this.numberOfReceipt > 0) {
        console.log("-------startSendLLTPLgspHcm----- ");
        this.isLoading = true;

        const search = "";
        let dataPost = {
          id: dossierId,
          code: code
        };
        return new Promise(resolve => {
          this.adapterService.sendLLTPLgspHcm(search, dataPost).subscribe(data => {
            console.log('responseLgspHCM', data);

            if (data && (data.status == 'OK' || data.status == '501')) {
              const msgObj = {
                vi: 'Chuyển hồ sơ sang PM chuyên ngành thành công!',
                en: 'Successful reception!'
              };
              this.snackbarService.openSnackBar(1, msgObj[this.selectedLang], '', 'success_notification', 2000);
              this.recallNumLLTP = 0;
            } else {
              const msgObj = {
                vi: 'Chuyển hồ sơ sang PM chuyên ngành thất bại! Lỗi: ' + data?.message,
                en: 'Failed reception! Error: ' + data?.message
              };
              this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', 10000);
              //Recall lại
              if(this.allowReSendLLTPLgspHcm && this.allowReSendLLTPLgspHcm.enable){
                //Kiểm tra từ khóa
                let recall = false;
                this.allowReSendLLTPLgspHcm.keyword.forEach(key => {
                  if(data?.message?.indexOf(key) >= 0)
                    recall = true;
                });
                if(recall){
                  this.recallNumLLTP++;
                  if(this.recallNumLLTP <= this.allowReSendLLTPLgspHcm.countSend){
                    setTimeout(() => {
                      const msgObj = {
                        vi: 'Đang gửi lại hồ sơ sang PM chuyên ngành lần ' + this.recallNumLLTP + '!',
                        en: 'Successful reception!'
                      };
                      this.snackbarService.openSnackBar(1, msgObj[this.selectedLang], '', 'success_notification', 2000);
                      this.sendLLTPLgspHCM(dossierId, code, procostTypeListId);
                    }, this.allowReSendLLTPLgspHcm.deplay);
                  }
                  else {
                    this.recallNumLLTP = 0;
                  }
                }
              }
            }
            this.isLoading = false;
          },err => {
            console.log(err);
            const msgObj = {
              vi: 'Chuyển hồ sơ sang PM chuyên ngành thất bại!',
              en: 'Failed reception!'
            };
            this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', 2000);
            this.isLoading = false;
          });
        });
      }
    }
  }







  async receiptCreation(dossierId, code, procedureId, dossierTask, applyMethod){
    this.checkHcmLyLichTuPhap = false;
    const data = await this.procedureService.getProcedureDetail(procedureId).toPromise();
    this.procedure = data;
    this.isDefaultElectronicReceipt = !!data?.extendHCM?.isDefaultElectronicReceipt ? data.extendHCM.isDefaultElectronicReceipt : false;
    this.procedure = data;
    const dossierForCheckDefaultElectronicReceipt = await this.dossierService.getDossierDetail(dossierId).toPromise();
    console.log("Dossier applymethod id ElectronicReceipt:",dossierForCheckDefaultElectronicReceipt?.applyMethod?.id);
    if (dossierForCheckDefaultElectronicReceipt?.applyMethod?.id === 1) {
      this.isSingleDossierReceiptForApplyMethod = this.deploymentService.env.OS_HCM?.receiptDirect?.isSingleDossierReceipt;
      this.isDefaultElectronicReceiptForApplyMethod = !!data?.extendHCM?.isDefaultElectronicReceiptDirect ? data.extendHCM.isDefaultElectronicReceiptDirect : false;
    } else {
      this.isSingleDossierReceiptForApplyMethod = this.deploymentService.env.OS_HCM?.receiptOnline?.isSingleDossierReceipt;
      this.isDefaultElectronicReceiptForApplyMethod = !!data?.extendHCM?.isDefaultElectronicReceiptOnline ? data.extendHCM.isDefaultElectronicReceiptOnline : false;
    }

    const dialogData = new ConfirmReceiptCreationDialogModel(dossierId, code, procedureId, this.userId);
    const dialogRef = this.dialog.open(ReceiptCreationComponent, {
      minWidth: '60vw',
      maxHeight: '80vh',
      data: dialogData,
      disableClose: true,
      autoFocus: false
    });
    dialogRef.afterClosed().subscribe(async dialogResult => {
      if (dialogResult) {
        const msgObj = {
          vi: 'Phát hành biên lai thành công!',
          en: 'Issue receipt successfully!'
        };

        console.log("vào tới đây 1 ")
        if(this.isShowDossierReceiptCreationStatus() && dialogResult?.issuedAllFee && !(this.dossierReceiptCreationStatusConfig.disableWhenDirectReception && applyMethod!= 0)){
          const {dossierTaskStatus,dossierMenuTaskRemind} = await forkJoin({
            dossierTaskStatus:this.dossierService.getAgencyTag(this.dossierReceiptCreationStatusConfig.dossierTaskStatus).pipe(catchError((err)=>null)),
            dossierMenuTaskRemind:this.dossierService.getAgencyTag(this.dossierReceiptCreationStatusConfig.dossierMenuTaskRemind).pipe(catchError((err)=>null)),
          }).pipe(
            map(({dossierTaskStatus,dossierMenuTaskRemind}:any)=>{
              if(!dossierTaskStatus || !dossierMenuTaskRemind){
                return {dossierTaskStatus,dossierMenuTaskRemind};
              }
              return{
                dossierTaskStatus: {
                  id:dossierTaskStatus.id,
                  name:dossierTaskStatus.trans
                },
                dossierMenuTaskRemind: {
                  id:dossierMenuTaskRemind.id,
                  name:dossierMenuTaskRemind.trans
                }
              }
            })
          ).toPromise();
          let requestBodyObj = {
            dossierStatus: this.dossierReceiptCreationStatusConfig.dossierStatus,
            dossierTaskStatus: dossierTaskStatus,
            dossierMenuTaskRemind: dossierMenuTaskRemind,
            updateOldData: false,
          };
          if(dossierTaskStatus && dossierMenuTaskRemind){
            const rs = await this.dossierService.putDossierStatusWithComment(dossierId, requestBodyObj).pipe(catchError(err=>null)).toPromise();
            if(rs){
              const updatedDossier = await this.dossierService.getDossierDetail(dossierId).toPromise();
              const index = this.dataSource.data.findIndex(dossier=>dossier.id === updatedDossier.id);
              if(index !== -1){
                let data = [...this.dataSource.data];

                data[index]= {
                  ...this.dataSource.data[index],
                  dossierStatus: {
                    ...this.dataSource.data[index].dossierStatus,
                    id:updatedDossier.dossierStatus.id,
                    name:updatedDossier.dossierTaskStatus.name
                  },
                }
                this.dataSource.data = [
                  ...data
                ]
              }
            }
          }
        }

        console.log("vào tới đây 2 ")
        this.snackbarService.openSnackBar(1, msgObj[this.selectedLang], '', 'success_notification', this.config.expiredTime);
        if (this.enableAddReceiptNumberToEformAllUnitScreen){
          //await this.getDossierDetail();
          // await this.getListDossierReceipt(dossierId);
          // await this.setSelectedData(procedureId);
          // console.log("this.formIO.data.data",this.formIO.data.data);
        }

        console.log("vào tới đây 3 ")
        if (!!dossierTask && dossierTask.length !== 0) {
          this.checkHcmLyLichTuPhap = dossierTask[0].bpmProcessDefinitionTask?.dynamicVariable?.hcmLyLichTuPhap;
        }
        this.sendLLTPLgspHCM(dossierId, code, dialogResult.procostTypeListId);
        if((this.isSingleDossierReceipt == true && this.isDefaultElectronicReceipt) || (this.isSingleDossierReceiptForApplyMethod == true && this.isDefaultElectronicReceiptForApplyMethod)){
          // this.getDossierFee(dossierId, procedureId);
          this.receiptList(dossierId, procedureId);
        }

        this.dossierId = dossierId;
        await this.getDossierDetail();
        console.log("dataaa ", data);
        if(this.enableIntegratedReception && data.extendHCM?.enableIntegratedReception
          && (this.dossier?.applyMethod?.id === 0 || this.dossier?.applyMethod?.id === '0' )
          && !!!this.dossier?.task){
          this.getListTagDossierReceivingKind(this.config.dossierReceivingKindCategoryId, 0, 50, 'order,asc');

          console.log("-------------- start receving ------------------");
          this.getConnectedInformation();
          await this.getProcedureProcessDetail(this.dossier.procedureProcessDefinition.id);
          await this.keycloakService.loadUserProfile().then(async user => {
            this.accepterInfo.username = user.username;
            // tslint:disable-next-line: no-string-literal
            this.accepterInfo.id = user['attributes'].user_id[0];
            // tslint:disable-next-line: no-string-literal
            this.accepterInfo.accountId = user['attributes'].account_id[0];
            // tslint:disable-next-line: no-string-literal
            this.userService.getUserInfo(user['attributes'].user_id).subscribe(data => {
              this.accepterInfo.fullname = data.fullname;
            });
            const userAgency = JSON.parse(localStorage.getItem('userAgency'));
            if (!!userAgency) {
              this.userAgency = userAgency;
              await this.getAgencyInfoReceiving(userAgency.id);
            }
          });
          await this.formToJson();
        }

      }

      if (!dialogResult) {
        const msgObj = {
          vi: 'Phát hành biên lai thất bại!',
          en: 'Issue receipt failed!'
        };
        this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
      }
    });
  }


  receiptList(dossierId, procedureId) {
    const dialogData = new ConfirmReceiptListDialogModel(dossierId, procedureId);
    const dialogRef = this.dialog.open(ReceiptListComponent, {
      minWidth: '80vw',
      maxHeight: '80vh',
      data: dialogData,
      disableClose: true,
      autoFocus: false
    });
    dialogRef.afterClosed().subscribe(async dialogResult => {

      // this.getDossierDetail(dossierId);
      // this.getDossierFee(dossierId, procedureId);
      // this.adminLayoutNavComponent.getDossierRemind();
      // if (this.enableAddReceiptNumberToEformAllUnitScreen){
        //await this.getDossierDetail();
        // await this.getListDossierReceipt(dossierId);
        // if (!!this.dossierDetailS[0].eForm) {
        //   if (this.formIO.id === this.dossierDetailS[0].eForm.id) {
        //     this.formIO.data = {
        //       data: this.dossierDetailS[0].eForm.data
        //     };

        //     if (this.enableAddReceiptNumberToEformAllUnitScreen){
        //       this.hcmListDossierReceiptNumberAllUnitScreen = localStorage.getItem("hcmListDossierReceiptNumberAllUnitScreen");
        //       this.hcmListDossierReceiptNumberAllUnitScreen = this.hcmListDossierReceiptNumberAllUnitScreen.substring(0,this.hcmListDossierReceiptNumberAllUnitScreen.length - 1);
        //       this.formIO.data.data.idMoneyReceipt = this.hcmListDossierReceiptNumberAllUnitScreen;
        //       localStorage.setItem("hcmListDossierReceiptNumberAllUnitScreen","");
        //       this.hcmListDossierReceiptNumberAllUnitScreen = "";
        //     }

        //   }
        // }
        //await this.setSelectedData();
      //   console.log("this.formIO.data.data",this.formIO.data.data);
      // }

      //   console.log("this.formIO.data.data",this.formIO.data.data);

    });
  }

  checkPaymentByCode(code){
    let num = 0;
    this.dossierService.checkPaymentByCode(code).subscribe(data => {
      if(!!data){
        for(let i = 0; i < data.length; i ++){
          if(!!data[i] && data[i].trangThaiGD === "1"){
            num = num + 1;
          }
        }
        const msgObj = {
          vi: 'Hồ sơ có ' + num + ' thanh toán thành công',
          en: 'Hồ sơ có ' + num + ' thanh toán thành công'
        };
        this.snackbarService.openSnackBar(1, msgObj[this.selectedLang], '', 'success_notification', this.config.expiredTime);
      }
    },
    err => {
      const msgObj = {
        vi: 'Hồ sơ có ' + num + ' thanh toán thành công',
        en: 'Hồ sơ có ' + num + ' thanh toán thành công'
      };
      this.snackbarService.openSnackBar(1, msgObj[this.selectedLang], '', 'success_notification', this.config.expiredTime);
    });
  }

  showDialogUpdateAgency() {
    const userAgency = JSON.parse(localStorage.getItem('userAgency'));
    console.log(userAgency)
    let dossierCode= ''
    let agencyId=''
    const dialogData = new UpdateAgencyDialogModel('Cập nhật đơn vị', `Bạn có chắc chắn muốn cập nhật đơn vị ${agencyId} cho hồ sơ ${dossierCode}?`);
    const dialogRef = this.dialog.open(UpdateAgencyDialogComponent, {
      width: '512px',
      data: dialogData,
      disableClose: true,
      autoFocus: false,
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
      if (dialogResult == true) {
        const msgObj = {
          vi: 'Cập nhật đơn vị thành công!',
          en: 'Update agency successfully!'
        };
        this.snackbarService.openSnackBar(1, msgObj[this.selectedLang], '', 'success_notification', this.config.expiredTime);
      } else if (dialogResult == false){
        const msgObj = {
          vi: 'Cập nhật đơn vị thất bại!',
          en: 'Update agency failed!'
        };
        this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
      }
    })
  }

  getConfig() {
    const config = this.deploymentService.getAppDeployment();
    if (!!config) {
      if (config.domain && config.domain.length > 0) {
        // tslint:disable-next-line:max-line-length
        const domain = config.domain.filter(listdomain => ((listdomain.domain.toLowerCase().indexOf(window.location.host) > -1) || (window.location.host.toLowerCase().indexOf(listdomain.domain) > -1)));
        if (domain && domain.length > 0 && domain[0].rootAgency) {
          this.searchDomain = '&domain-agency-id=' + domain[0].rootAgency.id;
        }
      }
    }
  }

  ngAfterViewInit() {
    setTimeout(() => {
      if(this.isUserRQ) {
        this.getListAgencyKGG();
      }
    }, 1000);

  }

  async sendInfoDossierTBNOHTTTLQNI(dossierId){
    let isVbdlis = true
          const responseCreateVbdlis = await this.dossierService.postInfoDosserTBNOHTTTLQNI(dossierId, this.tbnohtttlQNIConfigId).toPromise().catch(error => {
            isVbdlis = false;
            console.log(error);
          });
          isVbdlis = responseCreateVbdlis.affectedRows == 0 ? false : true;
          console.log("vbdlis" + responseCreateVbdlis.data);

          if (isVbdlis == true) {
            const msgObj = {
              vi: 'Tiếp nhận TBNOHTTTL thành công!',
              en: 'Successful reception TBNOHTTTL!',
            };
            this.snackbarService.openSnackBar(
              1,
              msgObj[this.selectedLang],
              '',
              'success_notification',
              this.config.expiredTime
            );

            //await this.getDossierDetail();
            this.onConfirmSearch();

          } else {
            const msgObj = {
              vi: 'Tiếp nhận TBNOHTTTL thất bại!',
              en: 'Failed reception TBNOHTTTL!'
            };
            this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', 2000);
            this.onConfirmSearch();
          }
  }

  async sendInfoProcessDossierTBNOHTTTLQNI(code){
    let isVbdlis = true
          const responseCreateVbdlis = await this.dossierService.postInfoProcessDosserTBNOHTTTLQNI(code, this.tbnohtttlQNIConfigId).toPromise().catch(error => {
            isVbdlis = false;
            console.log(error);
          });
          isVbdlis = responseCreateVbdlis.affectedRows == 0 ? false : true;
          console.log("vbdlis" + responseCreateVbdlis.data);

          if (isVbdlis == true) {
            const msgObj = {
              vi: 'Cập nhật trạng thái TBNOHTTTL thành công!',
              en: 'Successful update status TBNOHTTTL!',
            };
            this.snackbarService.openSnackBar(
              1,
              msgObj[this.selectedLang],
              '',
              'success_notification',
              this.config.expiredTime
            );

            //await this.getDossierDetail();
            this.onConfirmSearch();

          } else {
            const msgObj = {
              vi: 'Cập nhật trạng thái TBNOHTTTL thất bại!',
              en: 'Failed update status TBNOHTTTL!'
            };
            this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', 2000);
            this.onConfirmSearch();
          }
  }

  async getListAgency() {
    // const userAgency = JSON.parse(localStorage.getItem('userAgency'));
    // let rootAgencyId: any = '';
    // if (userAgency !== null) {
    //   rootAgencyId = userAgency.id;
    // } else {
    //   rootAgencyId = this.config.rootAgency.id;
    // }
    if (this.isFullListAgency) { return; }
    const userAgency = JSON.parse(localStorage.getItem('userAgency'));
    const data = await this.procedureService.getListAgencyWithParent('?parent-id=' + (this.searchByAncestorAgency ? userAgency.id : this.env?.rootAgency?.id) + '&page=' + this.listAgencyPage + '&size=50&sort=name.name,asc&status=1').toPromise();
    if (data.content.length === 0) { return; }
    this.listAgency = this.listAgencyPage === 0 ? data.content : this.listAgency.concat(data.content);
    this.isFullListAgency = data.last;
    this.listAgencyPage++;
    await this.autofillAgency();
  }

  // IGATESUPP-79377
  async autofillAgency() {
    if (this.dossierSearchAutofillAgency && !this.agencyFilled) {
      // Get user experiences
      const userId = localStorage.getItem("tempUID");
      if (this.userExperiences.length == 0) {
        const experiences = await this.userService.getUserExperience(userId).toPromise();
        if (Array.isArray(experiences)) {
          this.userExperiences = experiences;
        }
      }
      // Find experience primary
      for (let index = 0; index < this.userExperiences.length; index++) {
        const experience = this.userExperiences[index];
        if (typeof experience == "object" && typeof experience["agency"] == "object" && experience["primary"] == true) {
          // Find experience in agencies
          let agency = null;
          let experienceAgencyIds = [];
          if (typeof experience["agency"]["parent"] == "object" && typeof experience["agency"]["parent"]["id"] == "string") {
            experienceAgencyIds.push(experience["agency"]["parent"]["id"]);
          }
          if (typeof experience["agency"]["id"] == "string") {
            experienceAgencyIds.push(experience["agency"]["id"]);
          }
          agency = this.listAgency.find((e) => typeof e["id"] == "string" && experienceAgencyIds.includes(e["id"]));
          if (agency != null && typeof agency["id"] == "string") {
            this.agencyChange({value: agency["id"]});
            this.searchForm.get('advAgency').setValue(agency["id"]);
            this.agencyFilled = true;
          } else if (!this.isFullListAgency) {
            await this.getListAgency();
          }
        }
      }
    }
  }

  getListDossierTaskName() {
    if (this.isFullListDossierTaskName) { return; }
    this.dossierService.getListTagByCategoryId(this.env?.dossierTaskNameCategoryId?.id, this.listDossierTaskNamePage).subscribe(data => {
      if (data.content.length === 0) { return; }
      this.listDossierTaskName = this.listDossierTaskNamePage === 0 ? data.content : this.listDossierTaskName.concat(data.content);
      this.isFullListDossierTaskName = data.last;
      this.listDossierTaskNamePage++;
    });
  }

  getListSector() {
    if (this.isFullListSector) { return; }
    this.dossierService.getListSectorSort(this.listSectorPage).subscribe(data => {
      if (data.content.length === 0) { return; }
      this.listSector = this.listSectorPage === 0 ? data.content : this.listSector.concat(data.content);
      this.isFullListSector = data.last;
      this.listSectorPage++;
      // this.listSector.sort((a, b) => a.name.localeCompare(b.name));
    });
  }

  protected filterSectorS() {
    if (!this.sectors) {
      return;
    }
    let search = this.searchSectorCtrl.value.trim();
    this.searchSectorKeyword = search;
    if (!search) {
      this.sectors = JSON.parse(JSON.stringify(this.listSector).replace(/null/g, '""'));
      this.sectorFiltered.next(this.sectors);
      return;
    } else {
      search = search.toLowerCase();
    }

    if (this.sectors.filter(sec => sec.name.toLowerCase().indexOf(search) > -1).length > 0) {
      this.sectorFiltered.next(
        this.sectors.filter(sec => sec.name.toLowerCase().indexOf(search) > -1)
      );
    } else {
    if (this.isFullListSector == true) {
      this.sectors = JSON.parse(JSON.stringify(this.listSector).replace(/null/g, '""'));
      this.sectorFiltered.next(
        this.sectors.filter(sec => sec.name.toLowerCase().indexOf(search) > -1)
      );
    } else {

      this.sectorService.getListSector('?page=' + this.listSectorPage +  '&size=50&sort=name.name,asc&status=1&all-agency=0').subscribe(data => {
        console.log('api sector ', data);

        this.listSectorPage++;
        this.isFullListSector = data.last;
        for (let i = 0; i < data.numberOfElements ; i++) {

          this.listSector.push(data.content[i]);
        }
        // this.listSector.sort((a, b) => a.name.localeCompare(b.name));
        this.sectors = JSON.parse(JSON.stringify(this.listSector).replace(/null/g, '""'));
        this.sectorFiltered.next(this.sectors);
      } , err => {
        console.log(err);
      }) ;
    }
  }
  }


  async getListSector1(keyword = '', page = null, size = null) {
    if(this.isShowFilterSector || this.showSectorOnlineReception){
      let searchString =  '?agency-id=' + this.userAgency.id;
      if (this.showSectorOnlineReception) {
        searchString =  '?agency-id=' + this.userAgency.parent.id;
      }
      const data1 = await this.sectorService.getListSectorAll(searchString  +'&only-agency-id=1').toPromise();
      const data2 = await this.sectorService.getListSector('?page='  + this.listSectorPage + '&size=' + 1000 + '&spec=page&sort=name.name,asc&status=1').toPromise();
       if(data1.length > 0){
        const data = data1;
        this.isFullListSector = true;
        for (let i = 0; i < data.length ; i++) {
          data[i].name = data[i].name[0].name;
          this.listSector.push(data[i]);
        }
        this.listSector.sort((a, b) => a.name.localeCompare(b.name));
        this.sectors = JSON.parse(JSON.stringify(this.listSector).replace(/null/g, '""'));
        this.sectorFiltered.next(this.sectors);
        this.searchSectorCtrl.valueChanges.pipe(takeUntil(this.onDestroy)).subscribe(() => {
          this.filterSectorS();
        });
      }
      else {
        const searchByAgency  =  this.env?.isDienBien ? this.keySearchSectorAgency : '';
        // tslint:disable-next-line:max-line-length
        const searchString = '?keyword=' + keyword + '&page=' + page + '&size=' + size + '&spec=page&sort=name.name,asc&status=1' + searchByAgency;
        this.procedureService.getListSector(searchString).subscribe(res => {
          if (page === 0) {
            this.listSector = res.content;
          } else {
            this.listSector = this.listSector.concat(res.content);
          }
          this.totalPagesSector = res.totalPages;
          // this.listSector.sort((a, b) => a.name.localeCompare(b.name));
          this.sectors = JSON.parse(JSON.stringify(this.listSector).replace(/null/g, '""'));
          this.sectorFiltered.next(this.sectors);
          this.searchSectorCtrl.valueChanges.pipe(takeUntil(this.onDestroy)).subscribe(() => {
            this.filterSector();
          });
        }, err => {
          console.log(err);
        });
    }
    } else {
      // IGATESUPP-76434
      if (this.dossierSearchShowUserSectorOnly) {
        const userAgency = JSON.parse(localStorage.getItem('userAgency'));
        this.sectorService.getUserSectorOnlyOne(this.userId, userAgency.id ?? this.agencyId).subscribe(async res => {
          if (typeof res == "object" && Array.isArray(res["sectors"])) {
            this.listSector = res["sectors"];
            this.sectors = JSON.parse(JSON.stringify(this.listSector).replace(/null/g, '""'));
            this.sectorFiltered.next(this.sectors);
            if (this.sectors.length > 0) {
              this.searchForm.get("advSector").setValue(this.sectors[0]["id"]);
            }
            this.searchSectorCtrl.valueChanges.pipe(takeUntil(this.onDestroy)).subscribe(() => {
              this.filterSector();
            });
          }
        });
      } else {
        const searchByAgency  =  this.env?.isDienBien ? this.keySearchSectorAgency : '';
        // tslint:disable-next-line:max-line-length
        const searchString = '?keyword=' + keyword + '&page=' + page + '&size=' + size + '&spec=page&sort=name.name,asc&status=1' + searchByAgency;
        this.procedureService.getListSector(searchString).subscribe(res => {
          if (page === 0) {
            this.listSector = res.content;
          } else {
            this.listSector = this.listSector.concat(res.content);
          }
          this.totalPagesSector = res.totalPages;
          // this.listSector.sort((a, b) => a.name.localeCompare(b.name));
          this.sectors = JSON.parse(JSON.stringify(this.listSector).replace(/null/g, '""'));
          this.sectorFiltered.next(this.sectors);
          this.searchSectorCtrl.valueChanges.pipe(takeUntil(this.onDestroy)).subscribe(() => {
            this.filterSector();
          });
        }, err => {
          console.log(err);
        });
      }
  }
  }
  getListSectorScroll() {
    this.getListSector1(this.keywordSector, this.currentPageSector, this.pageSizeSector);
    this.currentPageSector += 1;
  }

  protected filterSector() {
    if (!this.sectors) {
      return;
    }
    let search = this.searchSectorCtrl.value.trim();
    this.searchSectorKeyword = search;
    if (!search) {
      this.sectorFiltered.next(this.sectors.slice());
      return;
    } else {
      search = search.toLowerCase();
    }
    // IGATESUPP-76434
    if (this.dossierSearchShowUserSectorOnly) {
      if (search == "") {
        this.sectorFiltered.next(this.sectors);
      } else {
        this.sectorFiltered.next(
          this.sectors.filter(sec => sec.name.toLowerCase().indexOf(search) > -1)
        );
      }
      return;
    }
    if (this.sectors.filter(sec => sec.name.toLowerCase().indexOf(search) > -1).length > 0) {
      this.sectorFiltered.next(
        this.sectors.filter(sec => sec.name.toLowerCase().indexOf(search) > -1)
      );
    } else {
      this.currentPageSector = 0;
      let searchByAgency  =  this.env?.isDienBien ? this.keySearchSectorAgency : '';
      if(this.isGetListSetorHGI){
        searchByAgency = this.keySearchSectorAgency;
      }
      const searchString = '?keyword=' + search + '&page=' + this.currentPageSector + '&size=' + 1000 + '&spec=page&sort=name.name,asc&status=1' + searchByAgency;
      this.procedureService.getListSector(searchString).subscribe(data => {
        for (let i = 0; i < data.numberOfElements; i++) {
          this.listSector.push(data.content[i]);
        }
        // this.listSector.sort((a, b) => a.name.localeCompare(b.name));
        this.sectors = JSON.parse(JSON.stringify(this.listSector).replace(/null/g, '""'));
      }, err => {
        console.log(err);
      });
    }
  }

  getListProcedureforAll(sectorId) {
    if (this.isFullListProcedure) { return; }
    this.dossierService.getListProcedure(sectorId, this.listProcedurePage).subscribe(data => {
      if (data.content.length === 0) { return; }
      this.listProcedure = this.listProcedurePage === 0 ? data.content : this.listProcedure.concat(data.content);
      this.isFullListProcedure = data.last;
      this.listProcedurePage++;
    });

  }

  getListProcedure(sectorId) {
    if (this.isFullListProcedure) {
      return;
    } else {
      const userAgency = JSON.parse(localStorage.getItem('userAgency'));
      let agencyIdSearch = '';
      if ( userAgency !== null && userAgency !== undefined && !this.isAdmin) {
        if (!!userAgency.parent && !!userAgency.parent.id) {
          agencyIdSearch = `&agency-id=${userAgency.parent.id}`;
        } else if (userAgency.id !== this.env?.rootAgency?.id) {
          agencyIdSearch = `&agency-id=${userAgency.id}`;
        }
      }
      agencyIdSearch = this.env?.filterProcedureByAgencyId ? agencyIdSearch : '';
      const searchByAgency  =  this.env?.isDienBien ? this.keySearchSectorAgency : '';
      let searchString =
        '?status=1&sort=translate.name,asc&keyword=' + this.searchProcedureKeyword +
        '&spec='+ this.paginationType+  '&page=' + this.listProcedurePage + '&size=' + this.size +
        '&sector-id=' + sectorId + searchByAgency +  agencyIdSearch;
      if(this.isGetListProcedureHGI){
        searchString =
        '?status=1&sort=translate.name,asc&keyword=' + this.searchProcedureKeyword +
        '&spec='+ this.paginationType+  '&page=' + this.listProcedurePage + '&size=' + this.size +
        '&sector-id=' + sectorId + this.keySearchSectorAgency;
      }
      this.procedureService.getListProcedure(searchString).subscribe(data => {
        this.isFullListProcedure = data.numberOfElements < data.size;
        this.listProcedurePage++;
        this.procedureFiltered = new ReplaySubject<any[]>(1);
        // this.listProcedure = [];
        for (let i = 0; i < data.numberOfElements; i++) {
          this.listProcedure.push(data.content[i]);
        }
        // try {
          this.procedures = JSON.parse(JSON.stringify(this.listProcedure).replace(/null/g, '""'));
        // } catch (e) {
        //   this.procedures = this.listProcedure;
        // }
        this.procedureFiltered.next(this.procedures);
      }, err => {
        console.log(err);
      });
    }
  }

  protected filterProcedure() {
    if (!this.procedures) {
      return;
    }
    let search = encodeURIComponent(this.searchProcedureCtrl.value.trim()).substring(0, 1000);
    this.searchProcedureKeyword = search;
    if (!search) {
      // this.procedureFiltered.next(this.procedures.slice());

      this.searchProcedureKeyword = '';
      this.listProcedurePage = 0;
      this.listProcedure = [];

      this.getListProcedure('');

      return;
    } else {
      search = search.toLowerCase();
    }
    if (this.procedures.filter(proc => proc.name.toLowerCase().indexOf(search) > -1).length > 0) {
      this.procedureFiltered.next(
        this.procedures.filter(proc => proc.name.toLowerCase().indexOf(search) > -1)
      );
    } else {
      // tslint:disable-next-line: max-line-length
      const userAgency = JSON.parse(localStorage.getItem('userAgency'));
      let agencyIdSearch = '';
      if ( userAgency !== null && userAgency !== undefined && !this.isAdmin) {
        if (!!userAgency.parent && !!userAgency.parent.id) {
          agencyIdSearch = `&agency-id=${userAgency.parent.id}`;
        } else if (userAgency.id !== this.env?.rootAgency?.id) {
          agencyIdSearch = `&agency-id=${userAgency.id}`;
        }
      }
      agencyIdSearch = this.env?.filterProcedureByAgencyId ? agencyIdSearch : '';
      let searchString =
        '?status=1&sort=translate.name,asc&keyword=' + search +
        '&spec='+ this.paginationType +'&page=0&size='+ this.size +
        '&sector-id=' + this.searchForm.get('advSector').value + this.keySearchSectorAgency  +  agencyIdSearch;

      // Fix lỗi duplicate agency-id trong param
      if (this.keySearchSectorAgency === agencyIdSearch) {
        searchString =
        '?status=1&sort=translate.name,asc&keyword=' + search +
        '&spec='+ this.paginationType +'&page=0&size='+ this.size +
        '&sector-id=' + this.searchForm.get('advSector').value + this.keySearchSectorAgency;
      }

      if(this.isGetListProcedureHGI){
        searchString =
        '?status=1&sort=translate.name,asc&keyword=' + search +
        '&spec='+ this.paginationType +'&page=0&size='+ this.size +
        '&sector-id=' + this.searchForm.get('advSector').value +  this.keySearchSectorAgency;
      }
      this.listProcedurePage = 0;
      this.procedureService.getListProcedure(searchString).subscribe(data => {
        this.procedureFiltered = new ReplaySubject<any[]>(1);
        this.listProcedure = [];
        for (let i = 0; i < data.numberOfElements; i++) {
          this.listProcedure.push(data.content[i]);
        }
        try {
          this.procedures = JSON.parse(JSON.stringify(this.listProcedure).replace(/null/g, '""'));
        } catch (e) {
          this.procedures = this.listProcedure;
        }
        this.procedureFiltered.next(
          this.procedures
        );
      }, err => {
        console.log(err);
      });
    }
  }
  checkNullData = 0;
  async getListDossier(searchString) {

    searchString += '&isAgencySearch=true';
    const userAgency = JSON.parse(localStorage.getItem('userAgency'));
    let array: string[] = this.deploymentService.env.OS_HCM.listAgencyUseSortType;
    var parentAgencyId = (userAgency.parent != null && userAgency.parent != undefined) ? userAgency.parent.id : null;
    if(array.filter((value) => (value.includes(userAgency.id) || (parentAgencyId != null &&  value.includes(parentAgencyId)))).length > 0 && this.env?.OS_HCM?.dossierArrSortType !== undefined){
      if (!sessionStorage.getItem('dossierArrSortType')) {
        sessionStorage.setItem('dossierArrSortType', this.env?.OS_HCM?.dossierArrSortType);
      }
      this.sortId = sessionStorage.getItem('dossierArrSortType');
    }else{
      sessionStorage.removeItem('dossierArrSortType');
    };
    searchString = this.remakeRequestUrl(searchString);
    this.dossierService.getListDossier(searchString + this.searchDomain).subscribe(async data => {
      if (this.isQNM || this.showDossierFee || this.syncPaymentStatus || this.showFilterPaymentStatus || this.isAGG) {
        this.dossierFeeByDossier.clear();
        this.dossierFeeByDossier = new Map<String, any[]>();
        const ids = data.content.map(item => item.id);
        ids.forEach(item => this.dossierFeeByDossier.set(item, []));
        const dossierFee = await this.commonService.getProcostDossiers(ids);
        dossierFee.forEach(item => {
          if (this.dossierFeeByDossier.get(item.dossier.id).filter(i => i.id == item.id).length == 0) {
            this.dossierFeeByDossier.set(item.dossier.id, [item, ...this.dossierFeeByDossier.get(item.dossier.id)]);
          }
        })
      }


      this.ELEMENTDATA = [];
      this.listTimesheet = [];
      this.listTimeSheetGenV2 = [];
      this.numberOfElements = data.numberOfElements;

      let checkNewDate = tUtils.newDate();
      for (let i = 0; i < data.numberOfElements; i++) {
        //IGATESUPP-41626
        if(this.onlyDisplayNationCode && !!data.content[i].nationCode && data.content[i].nationCode != ''){
          data.content[i].code = data.content[i].nationCode;
          data.content[i].nationCode = '';
        }
        let requireAdditional = true;
        data.content[i].userProcessing = '';
        data.content[i].stt = this.size * (this.pageIndex - 1) + (i + 1);

        if (!!data.content[i]?.task && data.content[i]?.task.length !== 0 && this.fakeCompletedDate) {
          if (this.taskCompletedStatusIds.findIndex(item => item === data.content[i]?.dossierTaskStatus.id) !== -1) {
            data.content[i].completedDate = data.content[i]?.task[data.content[i].task.length - 1]?.assignedDate;
            data.content[i].returnedDate = data.content[i]?.task[data.content[i].task.length - 1]?.assignedDate;
          }
        }

        if (!!data.content[i]?.currentTask && data.content[i]?.currentTask.length !== 0) {
          if (data.content[i]?.currentTask[0]?.bpmProcessDefinitionTask?.remind?.id === this.finaceObligatingStatus) {
            data.content[i].dossierStatus.checkFinaceObligating = true;
          }else{
            data.content[i].dossierStatus.checkFinaceObligating = false;
          }
        }

        if (data.content[i]?.currentTask !== undefined && data.content[i]?.currentTask.length > 0){
          // if (!!data.content[i].currentTask[0].agency){
          //   data.content[i].agencyProcessing = data.content[i].currentTask[0].agency.name.find(v => v.languageId === this.selectedLangId).name;
          // }
          if (data.content[i]?.currentTask[0]?.assignee && data.content[i]?.currentTask[0]?.assignee?.fullname){
            data.content[i].userProcessing = data.content[i].currentTask[0].assignee.fullname;
          }     
        }
        if(this.showColUnitQTI == 1){
          data.content[i].userProcessing =  this.getAssignName(data.content[i]?.task);
          data.content[i].agencyNameQTI = this.checkQTI(data.content[i]?.task);
        }


        // tslint:disable-next-line:prefer-for-of
        for (let m = 0; m < this.arrReceptionForm.length; m++) {
          if (data.content[i].applyMethod?.id === this.arrReceptionForm[m]?.id) {
            data.content[i].codeText = data.content[i]?.code + this.arrReceptionForm[m]?.content;
          }
        }
        data.content[i].due = [];
        if (data.content[i]?.acceptedDate !== undefined && data.content[i]?.processingTime !== undefined) {
          let processingTime = 0;
          let dateDiffPayment = 0;
          let processingTimeWithUnit = data.content[i].processingTime;
          if(this.deploymentService.env?.OS_HCM?.processingTimeAfterAddition?.enable && !!data.content[i]?.acceptedDate && !!data.content[i]?.previousTask && data.content[i]?.additionalRequirementDetail?.count >= 1
            && data.content[i].oldData?.dossierTaskStatus.id == this.deploymentService.env?.OS_HCM?.processingTimeAfterAddition?.idStatus){
            const dataProcessDetail = await this.processService.getProcessDetail(data.content[i].procedureProcessDefinition?.processDefinition?.id).toPromise();
            if(!!dataProcessDetail.dynamicVariable.processingTimeAfterAddition){
              processingTimeWithUnit = Number(dataProcessDetail.dynamicVariable.processingTimeAfterAddition);
              data.content[i].processingTime = processingTimeWithUnit;
              data.content[i].isProcessingTimeAfterAddition = true;
            }
          }
          if (this.calculateAppointmentDate == 1 && data.content[i]?.paymentRequestData?.dateDiff !== null && data.content[i]?.paymentRequestData?.dateDiff !== undefined) {
            dateDiffPayment = data.content[i].paymentRequestData.dateDiff;
            processingTimeWithUnit += dateDiffPayment;
          }
          const workingDayTime = 24;
          switch (data.content[i]?.processingTimeUnit) {
            case 'y':
              processingTime = Number(processingTimeWithUnit) * 365;
              break;
            case 'M':
              processingTime = Number(processingTimeWithUnit) * 30;
              break;
            case 'd':
              processingTime = processingTimeWithUnit;
              break;
            case 'H:m:s':
              processingTime = Number(processingTimeWithUnit) / workingDayTime;
              break;
            case 'h':
              processingTime = Number(processingTimeWithUnit) / workingDayTime;
              break;
            case 'm':
              processingTime = Number(processingTimeWithUnit) / (workingDayTime * 60);
              break;
          }

          let oneappointmentDate = null;
          if (!!data.content[i]?.appointmentDate){
            oneappointmentDate = data.content[i]?.appointmentDate;
          }
          let oneDueDate = oneappointmentDate;

          if (!!data.content[i]?.dueDate){
            oneDueDate = data.content[i].dueDate;
          }

          if (this.env?.limitedAppointmentTime) {
            this.listTimesheet.push({
              timesheet: {
                // tslint:disable-next-line: max-line-length
                id: data?.content[i]?.procedureProcessDefinition?.processDefinition?.timesheet !== undefined ? data?.content[i]?.procedureProcessDefinition?.processDefinition?.timesheet?.id : this.config?.defaultTimesheetId
              },
              dossier: {
                id: data?.content[i]?.id
              },
              duration: this.deploymentService?.env?.timesheetV2 ? (data?.content[i]?.processingTime + dateDiffPayment) : processingTime,
              startDate: data?.content[i]?.acceptedDate,
              endDate: '',
              checkOffDay: true,
              offTime: this.env?.limitedAppointmentTime,
              processingTimeUnit: data?.content[i]?.processingTimeUnit,
              dueDate: oneDueDate,
              appointmentDate: oneappointmentDate
            });
            let startDateGenV2 = checkNewDate;
            let endDateGenV2 = checkNewDate;
            let overDue = false;
            if (new Date(oneappointmentDate).getTime() < checkNewDate.getTime()) {
              startDateGenV2 = oneappointmentDate;
              overDue = true;
            } else {
              endDateGenV2 = oneappointmentDate;
            }
            this.listTimeSheetGenV2.push({
              timesheet : {
                id : data?.content[i]?.procedureProcessDefinition?.processDefinition?.timesheet !== undefined ? data?.content[i]?.procedureProcessDefinition?.processDefinition?.timesheet?.id : this.config?.defaultTimesheetId
              },
              dossier: {
                id: data?.content[i]?.id
              },
              startDate: startDateGenV2,
              endDate : endDateGenV2,
              overDue : overDue,
              dueDate: oneDueDate,
              checkOffDay : true
            });
          } else if (this.deploymentService?.env?.OS_HCM?.limitedAppointmentTimeOnProcedure) {
            this.listTimesheet.push({
              timesheet: {
                // tslint:disable-next-line: max-line-length
                id: data?.content[i]?.procedureProcessDefinition?.processDefinition?.timesheet !== undefined ? data?.content[i]?.procedureProcessDefinition?.processDefinition?.timesheet?.id : this.config?.defaultTimesheetId
              },
              dossier: {
                id: data?.content[i]?.id
              },
              duration: this.deploymentService?.env?.timesheetV2 ? (data?.content[i]?.processingTime + dateDiffPayment) : processingTime,
              startDate: data?.content[i]?.acceptedDate,
              endDate: '',
              checkOffDay: true,
              extendHCM: {
                offTime: this.deploymentService?.env?.OS_HCM?.limitedAppointmentTimeOnProcedure,
                startAtNextDay: data?.content[i]?.extendHCM?.startAtNextDay ?? false,
                appointmentAtNextDay: data?.content[i]?.extendHCM?.appointmentAtNextDay ?? false
              },
              processingTimeUnit: data?.content[i]?.processingTimeUnit,
              dueDate: oneDueDate,
              appointmentDate: oneappointmentDate
            });
            let startDateGenV2 = checkNewDate;
            let endDateGenV2 = checkNewDate;
            let overDue = false;
            if (new Date(oneappointmentDate).getTime() < checkNewDate.getTime()) {
              startDateGenV2 = oneappointmentDate;
              overDue = true;
            } else {
              endDateGenV2 = oneappointmentDate;
            }
            this.listTimeSheetGenV2.push({
              timesheet : {
                id : data?.content[i].procedureProcessDefinition?.processDefinition?.timesheet !== undefined ? data?.content[i]?.procedureProcessDefinition?.processDefinition?.timesheet?.id : this.config?.defaultTimesheetId
              },
              dossier: {
                id: data?.content[i].id
              },
              startDate: startDateGenV2,
              endDate : endDateGenV2,
              overDue : overDue,
              dueDate: oneDueDate,
              checkOffDay : true
            });
          } else {
            this.listTimesheet.push({
              timesheet: {
                // tslint:disable-next-line:max-line-length
                id: data?.content[i]?.procedureProcessDefinition?.processDefinition?.timesheet !== undefined ? data?.content[i]?.procedureProcessDefinition?.processDefinition?.timesheet?.id : this.config?.defaultTimesheetId
              },
              dossier: {
                id: data?.content[i]?.id
              },
              duration: this.deploymentService?.env.timesheetV2 ? (data?.content[i]?.processingTime + dateDiffPayment) : processingTime,
              startDate: data?.content[i]?.acceptedDate,
              endDate: '',
              checkOffDay: true,
              processingTimeUnit: data?.content[i]?.processingTimeUnit,
              dueDate: oneDueDate,
              appointmentDate: oneappointmentDate
            });
              let startDateGenV2 = checkNewDate;
              let endDateGenV2 = checkNewDate;
              let overDue = false;
              if (new Date(oneappointmentDate).getTime() < checkNewDate.getTime()) {
                startDateGenV2 = oneappointmentDate;
                overDue =true;
              } else {
                endDateGenV2 = oneappointmentDate;
              }
              this.listTimeSheetGenV2.push({
                timesheet : {
                  id : data?.content[i].procedureProcessDefinition?.processDefinition?.timesheet !== undefined ? data?.content[i]?.procedureProcessDefinition?.processDefinition?.timesheet?.id : this.config?.defaultTimesheetId
                },
                dossier: {
                  id: data?.content[i].id
                },
                startDate: startDateGenV2,
                endDate : endDateGenV2,
                overDue : overDue,
                dueDate: oneDueDate,
                checkOffDay : true
              });
          }
          data.content[i].dossierEndDate = null;
        }

        if (data?.content[i]?.dossierTaskStatus !== undefined && data?.content[i]?.dossierTaskStatus !== null) {
          data.content[i].dossierStatus.name = data?.content[i]?.dossierTaskStatus?.name;
          // tslint:disable-next-line:max-line-length
          if (data?.content[i]?.dossierTaskStatus?.id === this.deploymentService?.env?.dossierTaskStatus?.requestForAdditionalDocuments?.id || data?.content[i]?.dossierTaskStatus?.id === this.deploymentService?.env?.dossierTaskStatus?.dossierAdded?.id) {
            requireAdditional = false;
          }
          if (this.syncPaymentStatus) {
              if (data?.content[i]?.dossierTaskStatus?.id == this.paymentRequestId) {
                  let isPaid = true;
                  if (this.enableRemoveVnpostFeeToAnotherTable == 1) {
                      let vnpostFee: any = null;
                      vnpostFee = await this.getVnpostFeeDossier(data?.content[i]?.id);
                      if (vnpostFee != null) {
                          vnpostFee.forEach(element => {
                              if (element?.paid != (element?.quantity * element?.amount)) {
                                  isPaid = false;
                              }
                          });
                      }
                      // const dossierFee = await this.getProcostDossier(data?.content[i].id);
                      const dossierFee = this.dossierFeeByDossier.get(data?.content[i]?.id);
                      dossierFee.forEach(element => {
                          if (element.paid != (element?.quantity * element?.amount)) {
                              isPaid = false;
                          }
                      });
                  } else {
                      // const dossierFee = await this.getProcostDossier(data?.content[i].id);
                      const dossierFee = this.dossierFeeByDossier.get(data?.content[i]?.id);
                      dossierFee.forEach(element => {
                          if (element.paid != (element?.quantity * element?.amount)) {
                              isPaid = false;
                          }
                      });
                  }
                  if (isPaid) {
                      data.content[i].dossierStatus.id = this.paidDossierId;
                      data.content[i].dossierStatus.name = this.paidDossierName;
                  }
              }
          }
        } else {
          // tslint:disable-next-line: no-string-literal
          if (data?.content[i]?.dossierStatus?.id === 0 && this.justRegistered && this.justRegistered['trans']) {
            // tslint:disable-next-line:max-line-length tslint:disable-next-line: no-string-literal
            data.content[i].dossierStatus.name = this.justRegistered['trans'].filter(res => Number(res.languageId) === Number(localStorage.getItem('languageId')))[0]?.name;
          } else {
            if (data?.content[i]?.currentTask !== undefined && data?.content[i]?.currentTask.length !== 0) {
              // tslint:disable-next-line: no-string-literal
              data.content[i].dossierStatus.name = data?.content[i]?.currentTask[0]?.bpmProcessDefinitionTask?.name['name'];
            }
          }
        }
        data.content[i].requireAdditional = requireAdditional;
        const task  = data?.content[i]?.task ? data?.content[i]?.task : [''];
        if ( task.length > 0){
          const taskCurrentCheck = task.filter(t => t.isCurrent === 1).length;
          const taskCurrent =  taskCurrentCheck.lenght ? taskCurrentCheck[0] : [] ;
          const agencyName = taskCurrent?.agency?.name.filter(n => n.languageId === this.selectedLangId)[0]?.name;
          const assignee = taskCurrent?.assignee?.fullname ?  ' - ' + taskCurrent.assignee?.fullname : '';
          data.content[i].tagDbn = agencyName ? agencyName + assignee  : ' ';
        }

        data.content[i].canReassign = false;
        if (!!data.content[i].previousTask && data?.content[i]?.previousTask.length !== 0) {
          if (data.content[i]?.previousTask[0]?.assignee?.id === this.userId) {
            data.content[i].canReassign = true;
          }
        }

        if (this.deploymentService?.env?.OS_HCM?.isShowTakeNumber) {
          const dossier= await  this.dossierService.getDossierDetail(data?.content[i]?.id).toPromise();
          if(dossier.codeTakeNumber != null){
            data.content[i].takeNumber= dossier.codeTakeNumber.toString();
          } else {
            data.content[i].takeNumber= '';
          }
        }

        //Ẩn/Hiện nút trả kết quả chuyên ngành
        if(this.enableButtonReturnResult){
          if(!!data?.content[i]?.task){
            if(data?.content[i]?.task.length > 0){
              if(data?.content[i]?.task[0]?.bpmProcessDefinitionTask?.dynamicVariable?.hcmLGSP === true){
                if(data?.content[i].dossierStatus.id == 4){
                  data.content[i].enableButtonReturnResult = true;
                }else{
                  data.content[i].enableButtonReturnResult = false;
                }
              }else{
                data.content[i].enableButtonReturnResult = false;
              }
            }
          }
        }

        //Kiểm tra điều kiện ẩn hiện nút Quy trình xử lý chuyên ngành
        if(this.enableDisplayOfSpecializedProcessing){
          if(data.content[i]?.extendHCM?.isSyncLGSPHCM == 1){// kiểm tra hồ sơ đã đồng bộ LGSP HCM
            data.content[i].enableDisplayOfSpecializedProcessing = true;
          }else{
            data.content[i].enableDisplayOfSpecializedProcessing = false;
          }
        }

        if (this.isShowProceAdminMultiHCM) {
          if (tUtils.nonNull(data?.content[i].extendHCM, 'procedureAdministrationWithAssignedCode')) {
            data.content[i].listCodeTakeNumber = "";
            data.content[i]?.extendHCM?.procedureAdministrationWithAssignedCode.forEach(element => {
              data.content[i].listCodeTakeNumber = data?.content[i].listCodeTakeNumber + (!!element?.code ? element?.code.toString() + '; ' : '') ;
            });
          } else {
            data.content[i].listCodeTakeNumber = null;
          }
        }

        //phucnh.it2-IGATESUPP-40295
        if (this.enableNewVnpostStatus){
          let newId = data?.content[i].id + 'tkq';
          let vnpostStatusSearchString = "?orderNumber="+newId ;   //const newDossier= await  this.dossierService.getDossierDetail(data?.content[i].id).toPromise();
          console.log(vnpostStatusSearchString);
          const dataVnpostStatus= await  this.dossierService.getVnpostStatus(vnpostStatusSearchString).toPromise();
          if (dataVnpostStatus.length > 0) {
            data.content[i].vnpostStatus = dataVnpostStatus[0].statusMessage;
            console.log(data?.content[i].vnpostStatus);
          }else
            data.content[i].vnpostStatus = "";
        }

        if(this.hidenOverDueCalculation?.enable && this.hidenOverDueCalculation?.dossierTaskStatus.includes(data?.content[i]?.dossierTaskStatus?.id)) {
          data.content[i].checkHindenOverDueCalculation = false;
        }else {
          data.content[i].checkHindenOverDueCalculation = true;
        }

        //End phucnh.it2-IGATESUPP-40295
        if (this.isAGG){
          //linhnvv.agg-IGATESUPP-103872
          let dossierFee:any  = null;
          dossierFee = this.dossierFeeByDossier.get(data.content[i].id)? this.dossierFeeByDossier.get(data.content[i].id) : [];
          data.content[i].payAGG = this.getTotalCostAGG(dossierFee);
          //this.ELEMENTDATA?.push(data?.content[i]);
          //console.log(this.ELEMENTDATA);
          //linhnvv.agg-IGATESUPP-103872
        }

        if(this.showDossierFee || this.showFilterPaymentStatus) {
          let dossierFee:any  = null;
          // dossierFee = await this.getProcostDossier(data?.content[i].id);
          dossierFee = this.dossierFeeByDossier.get(data?.content[i]?.id);

          let cost = 0;
          let idCostTemp = [];
          dossierFee.forEach(element => {
            if(idCostTemp.indexOf(element?.id) == -1){
              cost += element?.quantity * element?.amount;
              idCostTemp.push(element?.id);
            }
          });
          data.content[i].fee = cost;

          data.content[i].status = this.getTotalCost(dossierFee).value;
          const formObj = this.searchForm.getRawValue();
          if(formObj.payStatus == '' || formObj.payStatus == this.getTotalCost(dossierFee)?.id){
            this.ELEMENTDATA?.push(data?.content[i]);
          }
        } else if (this.isQNM) {
          let dossierFee:any  = null;
          // dossierFee = await this.getProcostDossier(data?.content[i].id);
          dossierFee = this.dossierFeeByDossier.get(data?.content[i]?.id);

          let cost = 0;
          dossierFee.forEach(element => {
            cost += element?.quantity * element?.amount;
          });
          data.content[i].fee = cost;

          const tongTienTemp = data?.content[i]?.extendQNM?.tongTien;
          const thanhToanTemp = data?.content[i]?.extendQNM?.thanhToan;
          const dossierFeeTemp = data?.content[i]?.extendQNM?.dossierFee;
          const totalCostQNM = this.getTotalCostQNM(tongTienTemp, thanhToanTemp, dossierFeeTemp);
          data.content[i].status = totalCostQNM?.value;
          data.content[i].paidStatus = totalCostQNM.id;
          const formObj = this.searchForm.getRawValue();
          if (formObj.payStatus === '' || formObj.payStatus === totalCostQNM?.id) {
            this.ELEMENTDATA?.push(data?.content[i]);
          }
        } else{
          this.ELEMENTDATA?.push(data?.content[i]);
        }
      }
      if(this.ELEMENTDATA?.length > 0) {
        this.checkNullData = 0;
      } else {
        this.checkNullData = 1;
      }
      // đổi tên trạng thái ycbs QBH
      if(this.qbhmenuaction == true)
      {
        this.ELEMENTDATA?.forEach(id => {
          for (var i = 0; i < this.ELEMENTDATA?.length; i++) {
            if (this.ELEMENTDATA[i]?.dossierTaskStatus?.id === "61ee30eada2d36b037e00001" ||this.ELEMENTDATA[i]?.dossierStatus?.id === 8 ) {
              this.ELEMENTDATA[i].dossierTaskStatus.name = "Xác nhận yêu cầu bổ sung";
              this.ELEMENTDATA[i].dossierStatus.name = "Xác nhận yêu cầu bổ sung";
            }
            if (this.ELEMENTDATA[i]?.dossierTaskStatus?.id === "61ee30eada2d36b037e00003" ||this.ELEMENTDATA[i]?.dossierStatus?.id === 10 ) {
              this.ELEMENTDATA[i].dossierTaskStatus.name = "Xác nhận xin lỗi và xin gia hạn";
              this.ELEMENTDATA[i].dossierStatus.name = "Xác nhận xin lỗi và xin gia hạn";
            }
            if (this.ELEMENTDATA[i]?.dossierTaskStatus?.id === "61ee30eada2d36b037e00004" ||this.ELEMENTDATA[i]?.dossierStatus?.id === 11 ) {
              this.ELEMENTDATA[i].dossierTaskStatus.name = "Xác nhận từ chối giải quyết";
              this.ELEMENTDATA[i].dossierStatus.name = "Xác nhận từ chối giải quyết";
            }
            if (this.ELEMENTDATA[i]?.dossierTaskStatus?.id === "61ee30eada2d36b037e00005" ||this.ELEMENTDATA[i]?.dossierStatus?.id === 12 ) {
             this.ELEMENTDATA[i].dossierTaskStatus.name = "Từ chối giải quyết";
             this.ELEMENTDATA[i].dossierStatus.name = "Từ chối giải quyết";
           }
           if (this.ELEMENTDATA[i]?.dossierTaskStatus?.id === "6151c771ba2a04299f949875" && this.ELEMENTDATA[i]?.dossierStatus?.id == 6 ) {
            this.ELEMENTDATA[i].dossierTaskStatus.name = "Xác nhận rút";
            this.ELEMENTDATA[i].dossierStatus.name = "Xác nhận rút";
          }
          }
        })
      }
      this.dataSource.data = this.ELEMENTDATA;
      if(this.isDisableSearch){
        this.isDisableSearch = false;
      }
      this.setTotalElements(data, this.paginationType);
      if(this.enableChangeDueDateCalculation){
        if (this.agencyAcceptDurationByWorkingTime) {
          this.postTimesheetv2ChangeCalculation();
        } else {
          this.postTimesheetChangeCalculation();
        }
      }else{
        if (this.agencyAcceptDurationByWorkingTime) {
          this.postTimesheetv2();
        } else {
          this.postTimesheet();
        }
      }
      this.changeDetectorRef.detectChanges();
    });
  }

  changeSearch($event){
    if(this.isDisableSearch){
      return;
    }
    this.isDisableSearch = true;
    this.onConfirmSearch();
  }

  async getVnpostFeeDossier(dossierId) {
    return new Promise<Array<any>>(resolve => {
      this.dossierService.getVnpostFee(dossierId).subscribe(data => {
        resolve(data);
      });
    });
  }

  async getProcostDossier(dossierId) {

    return new Promise<Array<any>>(resolve => {
      this.dossierService.getDossierFee(dossierId).subscribe(data => {
        resolve(data);
      });
    });
  }

  getTotalCost(data) {
    let cost = 0;
    let paid = 0;
    let result ={
      id: 0,
      value: ''
    };
    if(data.length == 0 ){
      result ={
        id: 1,
        value: 'Không tính phí'
      };
    }else{
      data.forEach(element => {
        cost += element.quantity * element.amount;
        paid += element.paid;
      });
      if(paid == 0 && cost != 0){
        result ={
          id: 2,
          value: 'Chưa thanh toán'
        };
      }else{

        if(cost == paid && cost != 0){
          result ={
            id: 4,
            value: 'Đã thanh toán'
          };
        }else{
          if(cost > paid && paid != 0){
            result ={
              id: 3,
              value: 'Đã thanh toán 1 phần'
            };
          }else{
            result ={
              id: 1,
              value: 'Không tính phí'
            };
          }
        }
      }
    }
    return result;
  }

  getTotalCostQNM(cost, paid, dossierFee) {
    // cost: tong tien
    // paid: đa thanh toan

    let result = {
      id: '0',
      value: ''
    };
    if (!dossierFee || dossierFee?.length === 0) {
      result = {
        id: '1',
        value: 'Không tính phí'
      };
    } else {
      if (paid === 0 && cost !== 0) {
        result = {
          id: '2',
          value: 'Chưa thanh toán'
        };
      } else {
        if (cost === paid && cost !== 0) {
          result = {
            id: '4',
            value: 'Đã thanh toán'
          };
        } else {
          if (cost > paid && paid !== 0) {
            result = {
              id: '3',
              value: 'Đã thanh toán 1 phần'
            };
          } else {
            result = {
              id: '1',
              value: 'Không tính phí'
            };
          }
        }
      }
    }
    return result;
  }

  //AGG
  getTotalCostAGG(data) {
    let result = '';
    if(data?.length > 0){
      const tongTien = data?.reduce((sum, current) => sum + (current.amount * current.quantity), 0);
      const daThanhToan = data?.reduce((sum, current) => sum + current.paid, 0);
      if(daThanhToan >= tongTien) {
        result = "Đã thanh toán";
      }else if(daThanhToan > 0 && daThanhToan < tongTien){
        result = "Đã thanh toán một phần";
      }else{
        result = "Chưa thanh toán";
      }
    }else{
      result = "Không có lệ phí";
    }
    return result;
  }
  //AGG

  onTreeOpenChange(value) {
    this.isAgencyTreeOpen = value;
  }
  agencyTreeChange(obj) {
    if (Array.isArray(obj)) {
      this.savedItems = obj.map(item => item.id);
    }
  }
  getDossierTaskStatus() {
    this.dossierService.getAgencyTag(this.deploymentService.env.dossierTaskStatus?.justRegistered?.id).subscribe(rs => {
      this.justRegistered = rs;
    }, err => {
      console.log(err);
    });
  }

  advProcedureChange(event) {
    this.isSelectedProcedure = true;
  }

  advNationChange(event) {
    if ( this.searchForm.get('advNation').value){
      this.getListProvince();
    }else{
      this.listProvince = [];
      this.listDistrict = [];
      this.listWard = [];
    }
  }

  advProvinceChange(event) {
    this.getListDistrict();
  }

  advDistrictChange(event) {
    this.getListWard(event.value);
  }

  printReportChange(event) {
    event.value = '';
  }

  advSectorChange(event) {
    this.listProcedure = [];
    this.isFullListProcedure = false;
    this.listProcedurePage = 0;
    this.getListProcedure(event.value);
  }

  getListNation() {
    this.dossierService.getListNation().subscribe(data => {
      this.listNation = data;
      // this.searchForm.patchValue({
      //   advNation: data[0].id
      // });
      // this.getListProvince();
    }, err => {
      console.log(err);
    });
  }

  getListProvince() {
    this.dossierService.getListPlace(this.searchForm.get('advNation').value, null, this.config.placeProvinceTypeId)
      .subscribe(data => {
        // this.searchForm.patchValue({
        //   advProvince: this.config.placeDefaultProvinceId
        // });
        if (this.searchForm.get('advNation').value === '5f39f4a95224cf235e134c5c'){
          data.unshift({id: '', name: 'Tất cả'});
          this.listProvince = data;
        }else{
          this.listProvince = [];
          this.listDistrict = [];
          this.listWard = [];
        }
        this.getListDistrict();
      }, err => {
        console.log(err);
      });
  }

  getListDistrict() {
    if (this.searchForm.get('advProvince').value){
      // tslint:disable-next-line:max-line-length no-unused-expression
      this.dossierService.getListPlace(this.searchForm.get('advNation').value, this.searchForm.get('advProvince').value, this.config.placeDistrictTypeId)
        .subscribe(data => {
          if (this.searchForm.get('advNation').value === '5f39f4a95224cf235e134c5c'){
            data.unshift({id: '', name: 'Tất cả'});
            this.listDistrict = data;
          }else{
            this.listDistrict = [];
          }
          this.getListWard(null);
          // this.listDistrict = [];
          // data.unshift({id: '', name: 'Tất cả'});
          // this.listDistrict = data;
        }, err => {
          console.log(err);
        });
    }else {
      this.listDistrict = [];
    }
  }

  getListWard(value) {
    // tslint:disable-next-line: max-line-length
    if (this.searchForm.get('advDistrict').value && value && this.listDistrict.length > 0){
      // tslint:disable-next-line:max-line-length
      this.dossierService.getListPlace(this.searchForm.get('advNation').value, this.searchForm.get('advDistrict').value, this.config.placeWardTypeId)
        .subscribe(data => {
          if (this.searchForm.get('advNation').value === '5f39f4a95224cf235e134c5c'){
            data.unshift({id: '', name: 'Tất cả'});
            this.listWard = data;
          }else{
            this.listWard = [];
          }
        }, err => {
          console.log(err);
        });
    }else{
      this.listWard = [];
    }

  }

  getDate(dateTime) {
    return dateTime ? this.datePipe.transform(dateTime, 'dd/MM/yyyy') : '';
  }

  setAll(value) {
    this.isCheckedAll = value;
    // tslint:disable-next-line: prefer-for-of
    for (let i = 0; i < this.ELEMENTDATA.length; i++) {
      this.ELEMENTDATA[i].checked = value;
    }
  }

  async onConfirmSearch() {
    if(this.isOptionSearchHGI){
      this.searchForm.controls.advNation.setValue('');
      this.searchForm.controls.advProvince.setValue('');
      this.searchForm.controls.advDistrict.setValue('');
      this.searchForm.controls.advWard.setValue('');
      this.searchForm.controls.advAddress.setValue('');
      this.searchForm.controls.advAgency.setValue('');
    }
    const formObj = this.searchForm.getRawValue();

    let addSearch = "";
    this.setQueryParams();
    if(this.isShowContent) {
      let contentMain = formObj.contentMain;
      if(contentMain != null && contentMain != undefined && contentMain != ''){
        contentMain = contentMain.trim();
        addSearch  = '&noidung=' + contentMain;
      }
    }

    if (this.isUserRQ) {
      switch(this.sortId){
        case '0':{
          this.sortType = 'updatedDate,desc';
          break;
        }
        case '1':{
          this.sortType = 'dueDate,asc';
          break;
        }
        case '2':{
          this.sortType = 'dueDate,desc';
          break;
        }
        case '3':{
          this.sortType = 'appliedDate,asc';
          break;
        }
        case '4':{
          this.sortType = 'appliedDate,desc';
          break;
        }
        default: {
          this.sortType = 'updatedDate,desc';
        }
      }
      let searchString = 'search?sort=' + this.sortType + '&page=0&size=' + this.size +
      '&applicant-organization='+ encodeURIComponent(formObj.applicantOrganization.trim()).substring(0, 1000) + '&spec=' + this.paginationType + this.makeRequestUrl();
      searchString += '&taxCode=' + formObj.taxCode +'&resPerson=' +formObj.resPerson ;
      
      if (!this.confirmPayment) {
        searchString += "&remove-status=18";
      }
      if(this.showFilterPaymentStatus){
        if(formObj.paymentStatus)
        {
          searchString += '&payment-status=' + formObj.paymentStatus.toString();
        }
      }

      // IGATESUPP-76910
      if (formObj.taskAgencyCtrl) {
        searchString += `&task-agency-id=${formObj.taskAgencyCtrl.toString()}`
      }
      if (formObj.taskAssigneeCtrl) {
        searchString += `&task-assignee-id=${formObj.taskAssigneeCtrl.toString()}`
      }

      searchString += addSearch;

      this.pageIndex = 1;
      this.page = 1;
      console.log(searchString);
      this.getListDossier(searchString);
      this.countDossier = false;
      this.callRemindTask();
      this.listExcel = [];
      this.selectedDossiers = [];
    }
    else
    {
      let searchString = this.isQNM?'search?sort=updatedDate,desc&page=0&size=' + this.size +
      '&applicant-organization='+ encodeURIComponent(formObj.applicantOrganization.trim()).substring(0, 1000) + '&spec=' + this.paginationType + this.makeRequestUrl()+'&paystatus=' + formObj.payStatus:'search?page=0&size=' + this.size +
      '&applicant-organization='+ encodeURIComponent(formObj.applicantOrganization.trim()).substring(0, 1000) + '&spec=' + this.paginationType + this.makeRequestUrl();
      if (this.qni_listsearch)
    {
      let listSubAgencys:any = [];
      let listAgencys = [];
      if (this.savedItems.length == 0) {
      const idTemp = await this.qniStatisticsService.getRootAgencies(`?id=${this.userAgency?.id}`).toPromise();
      if(idTemp != null && idTemp.length > 0)
      listAgencys = [idTemp[0]?.id];
      else
      listAgencys = [this.userAgency?.id]
      let search = "?parent-id="+listAgencys
      listSubAgencys = await this.basedataService.getSubAgencyByParentId(search).toPromise();
      if(listSubAgencys != null && listSubAgencys.length>0)
      listAgencys = [...listSubAgencys.map(x=>x.id)] ;
      }else
      {
        listAgencys = [...this.savedItems];
        searchString += "&agency-ids=" + listAgencys.join(',');
      }

    }
      if (this.isShowFilterSortHCM) {
        searchString += '&sort=' + this.getSortHcmType()
          + '&is-sort-hcm=true';
      } else if (this.updatedDateSort) {
        searchString += '&sort=updatedDate,desc';
      }else {
        if (this.deploymentService.env.OS_QNI.isSortByOwnerFullname && formObj.ownerFullname.trim().length > 0){
          searchString += '&sort=applicant.data.ownerFullname,acs';
        }
      }
      if (!this.confirmPayment) {
        searchString += "&remove-status=18";
      }
      if (this.showAppointmentNoLLTP == 1 && formObj.appointmentNoLLTP != "") {
        searchString += ('&appointment-no-lltp='+ encodeURIComponent(formObj.appointmentNoLLTP.trim()).substring(0, 1000));
      }
      if (this.showDocumentNoLLTP == 1 && formObj.documentNoLLTP != "") {
        searchString += ('&document-no-lltp='+ encodeURIComponent(formObj.documentNoLLTP.trim()).substring(0, 1000));
      }
      if(this.showFilterPaymentStatus){
        if(formObj.paymentStatus)
        {
          searchString += '&payment-status=' + formObj.paymentStatus.toString();
        }
      }
      if(this.allowFilterAcceptedDateOptimization){
        searchString += "&filter-payment-appointment-date=true";
      }
      if(this.allowFilterPaymentStatusListAgency && this.allowFilterPaymentStatusListAgency.length > 0 && this.checkAgency()){
        searchString += "&filter-payment-agency=true";
      }
      searchString += addSearch;

      // IGATESUPP-76910
      if (formObj.taskAgencyCtrl) {
        searchString += `&task-agency-id=${formObj.taskAgencyCtrl.toString()}`
      }
      if (formObj.taskAssigneeCtrl) {
        searchString += `&task-assignee-id=${formObj.taskAssigneeCtrl.toString()}`
      }

      if (this.isQbhDossierKm) {
        searchString += '&qbh-dossier-km=true'
      }

      if (this.sortByConfig !== 0) {
        switch (this.sortByConfig) {
          case 1:
            this.sortType = 'updatedDate,asc';
            break;
          case 2:
            this.sortType = 'updatedDate,desc';
            break;
          case 3:
            this.sortType = 'dueDate,asc';
            break;
          case 4:
            this.sortType = 'dueDate,desc';
            break;
          case 5:
            this.sortType = 'appliedDate,asc';
            break;
          case 6:
            this.sortType = 'appliedDate,desc';
            break;
        }
        searchString += '&sort=' + this.sortType;
      }

      if (this.checkShowFilterRegisterApplicationVNeID) {
          searchString += "&includeVNeID=true";
      }
      //IGATESUPP-111250
      if (this.showSearchPhoneNumber) {
        searchString += '&phone-number=' + formObj.phoneNumberApply.trim();
      }
      
      this.pageIndex = 1;
      this.page = 1;
      searchString+= '&taxCode='+formObj.taxCode + '&resPerson=' +formObj.resPerson
      this.getListDossier(searchString);
      this.countDossier = false;
      this.callRemindTask();
      this.listExcel = [];
      this.selectedDossiers = [];

      // this.router.navigateByUrl('/dossier/search?page=' + (this.pageIndex) + '&size=' + this.size + '&spec=page' + this.makeRequestUrl());
    }
    //Tuesday 18/04/2023 - quocpa-IGATESUPP-44355
    if (this.enableFilterAppliedDate === 1) {
      this.checkDateValid(formObj.advFilingFrom, formObj.advFilingTo);
    }//Tuesday end 18/04/2023 - quocpa-IGATESUPP-44355

  }

  changeSelectPayment(event){
    console.log('formObj.paymentStatus',event);
    let check = 1;
    event.value.forEach(element => {
      if(element ==''){
        check = 0;
      }
    });
    if(check == 0 ){
     if(event.value.length >= this.tempSelectedValue.length){
      this.tempSelectedValue = ['', '1', '2', '3', '4'];
      this.selectedValue = this.tempSelectedValue;
     } else {
      this.tempSelectedValue =[];
      event.value.forEach(ele => {
        if(ele !=''){
          this.tempSelectedValue.push(ele);
        }
      });
      this.selectedValue = this.tempSelectedValue;
     }
    } else {
      if(event.value.length == 4 ){
        if(event.value.length >= this.tempSelectedValue.length){
        this.tempSelectedValue = ['', '1', '2', '3', '4'];
        this.selectedValue = this.tempSelectedValue;
        } else {
        this.tempSelectedValue = [];
        this.selectedValue = this.tempSelectedValue;
      }
    } else {
      this.tempSelectedValue = this.selectedValue;
    }
    }
    console.log('this.selectedValue',this.selectedValue);

  }

  makeRequestUrl() {
    let userAgencyId: any = '';
    if (this.userAgency !== null) {
      userAgencyId = this.userAgency.id;
      if (!!this.userAgency.parent && !this.searchByAncestorAgency) {
        userAgencyId = this.userAgency.parent.id;
      }
    }
    if(this.isOptionSearchHGI){
      this.searchForm.controls.advNation.setValue('');
      this.searchForm.controls.advProvince.setValue('');
      this.searchForm.controls.advDistrict.setValue('');
      this.searchForm.controls.advWard.setValue('');
      this.searchForm.controls.advAddress.setValue('');
      this.searchForm.controls.advAgency.setValue('');
    }
    const formObj = this.searchForm.getRawValue();
    const ancestorAgencyExpand = formObj.advAgency ?  formObj.advAgency : userAgencyId;
    let searchString = this.xpandStatus === false ? '&code=' + formObj.code.trim() +
      '&receipt-code=' + formObj.receiptCode.trim() +
      '&identity-number=' + formObj.identityNumber.trim() +
      '&applicant-name=' + formObj.applicantName.trim() +
      '&applicant-owner-name=' + formObj.ownerFullname.trim() +
      '&remind-id=' + this.remindId +
      '&procedure-code=' + formObj.procedureCode.trim() +
      '&accepted-from=' + (formObj.advAcceptFrom ? this.datePipe.transform(formObj.advAcceptFrom, 'dd/MM/yyyy') : '') +
      '&accepted-to=' + (formObj.advAcceptTo ? this.datePipe.transform(formObj.advAcceptTo, 'dd/MM/yyyy') : '') +
      '&ancestor-agency-id=' + (this.dossierSearchAutofillAgency ? ancestorAgencyExpand : userAgencyId) :
      '&identity-number=' + formObj.identityNumber.trim() +
      '&applicant-name=' + formObj.applicantName.trim() +
      '&applicant-owner-name=' + formObj.ownerFullname.trim() +
      '&code=' + formObj.code.trim() +
      '&sector-id=' + formObj.advSector +
      '&procedure-id=' + formObj.advProcedure +
      '&nation-id=' + formObj.advNation +
      '&province-id=' + formObj.advProvince +
      '&district-id=' + formObj.advDistrict +
      '&ward-id=' + formObj.advWard +
      '&address=' + formObj.advAddress.trim() +
      (this.isFilterAddressOrganization ? '&address1=' + formObj.advAddressOrganization.trim()  : '') +
      '&task-status-id=' + formObj.advTaskStatusId +
      '&dossier-status=' + formObj.advProcessStatus +
      '&apply-method-id=' + formObj.advApplyMethod +
      '&accepted-from=' + (formObj.advAcceptFrom ? this.datePipe.transform(formObj.advAcceptFrom, 'dd/MM/yyyy') : '') +
      '&accepted-to=' + (formObj.advAcceptTo ? this.datePipe.transform(formObj.advAcceptTo, 'dd/MM/yyyy') : '') +
      '&appointment-from=' + (formObj.advAppointmentFrom ? this.datePipe.transform(formObj.advAppointmentFrom, 'dd/MM/yyyy') : '') +
      '&appointment-to=' + (formObj.advAppointmentTo ? this.datePipe.transform(formObj.advAppointmentTo, 'dd/MM/yyyy') : '') +
      '&result-returned-from=' + (formObj.avdResultReturnedFrom ? this.datePipe.transform(formObj.avdResultReturnedFrom, 'dd/MM/yyyy') : '') +
      '&result-returned-to=' + (formObj.avdResultReturnedTo ? this.datePipe.transform(formObj.avdResultReturnedTo, 'dd/MM/yyyy') : '') +
      '&noidungyeucaugiaiquyet=' + formObj.advnoidungyeucaugiaiquyet.trim() +
      '&approvalstatus=' + formObj.approvalstatus +
      '&receipt-code=' + formObj.receiptCode.trim() +
      '&ancestor-agency-id=' + ancestorAgencyExpand ;

      if(this.showRemindSyncLGSPHCM && this.isSyncLGSPHCM == true){
        searchString  +=  '&sync-LGSPHCM=0';
      } else {
        searchString  +=  '&remind-id=' +   this.remindId ;
      }
      
      if (this.checkShowFilterRegisterApplicationVNeID) {
        searchString += "&includeVNeID=true";
      }

    if (this.hasParticipatedInProcessing) {
      searchString += !this.xpandStatus ? '&task-ancestor-agency-id=' + userAgencyId : '&task-ancestor-agency-id=' + ancestorAgencyExpand;
    }

    if(this.env?.vnpost?.config && this.env?.vnpost?.config === '1')
    {
      searchString += '&vnpost-status-return-code=' + formObj.vnpostStatus;
    }
    if( this.showReceiveDossierResultMethod){
    searchString += '&receiving-kind-id=' + formObj.receivingKindCtrl ;
    }
    if (this.showCbImplementProgress && formObj.implementProgress !== '') {
      searchString += '&implement-progress=' + formObj.implementProgress + '&is-form-process=0';
    }
    // KGG OS
    if(formObj.agencySearchKGG !== null && formObj.agencySearchKGG !== "") {
      searchString += '&agency-search-kgg=' + formObj.agencySearchKGG;
    }

    //Tuesday 18/04/2023 - quocpa-IGATESUPP-44355
    if (this.enableFilterAppliedDate === 1 || this.isShowFilterApplyDate) {
      searchString += '&applied-from=' + (formObj.advFilingFrom ? this.datePipe.transform(formObj.advFilingFrom, 'dd/MM/yyyy') : '') +
          '&applied-to=' + (formObj.advFilingTo ? this.datePipe.transform(formObj.advFilingTo, 'dd/MM/yyyy') : this.datePipe.transform(new Date(), 'dd/MM/yyyy'));
    }//Tuesday end 18/04/2023 - quocpa-IGATESUPP-44355

    return searchString;
  }

  remakeRequestUrl(searchString) {
    const formObj = this.searchForm.getRawValue();

    searchString = searchString.replace('search?', '');
    const searchStringObj = tUtils.parseParams(searchString);
    if (this.deploymentService.optimize.dossierSearch.codeMatch) {
      searchStringObj['code-match'] = searchStringObj['code'];
      delete searchStringObj['code'];

      searchStringObj['nation-code-match'] = formObj.nationCode;
    }

    if (this.deploymentService.optimize.dossierSearch.sort.length !== 0) {
      searchStringObj['sort'] = this.deploymentService.optimize.dossierSearch.sort;
    }

    const params = new URLSearchParams(searchStringObj);
    searchString = 'search?' + params.toString();

    return searchString;
  }

  //KGG OutSource
  getListAgencyKGG() {
    // tslint:disable-next-line:max-line-length
    let str = this.makeRequestUrl();
    // const searchString = str + '&list-status=' + this.config.listStatusViewReception + ',' + this.config.listStatusViewCancel
    //                     + '&in-list=0';
    if (this.enableApprovaledAgencyTreeView) {
      str += '&enable-approvaled-agency-tree-view=' + this.enableApprovaledAgencyTreeView;
    }

    this.dossierService.getAgencySearchKGG(str).subscribe(data => {
      for(let i = 0; i < data.length; i++) {
        let obj = {}
        obj['value'] = data[i].id;
        obj['name'] = data[i].name;
        this.listAgencyKGG.push(obj);
      }
    });

  }

  onClickOpenAdvancedSearchBox() {
    this.xpandStatus = this.xpandStatus ? false : true;
    if(this.xpandStatus){
      if(this.depConfig?.addressDefault?.nationId){
        const nationId = this.searchForm.controls['advNation'].value;
        if(!nationId){
          const defaultNationId = this.depConfig?.addressDefault?.nationId;
          this.searchForm.patchValue({advNation: defaultNationId});
          this.advNationChange({value: defaultNationId});
        }
        setTimeout(() => {
          const provinceId = this.searchForm.controls['advProvince'].value;
          if(!provinceId){
            const defaultProvinceId = this.depConfig?.addressDefault?.provinceId;
            this.searchForm.patchValue({advProvince: defaultProvinceId});
            this.advProvinceChange({value: defaultProvinceId});
          }
        }, 2000);
      }
    }
  }

  async exportExcelDossierQNM() {
    const formObj = this.searchForm.getRawValue();
    this.waitingDownloadExcel = true;
    let searchString = '';
    if(this.selectedDossiers.length > 0){
      searchString = 'export-excel-dossier-qnm?dossier-ids='+ this.selectedDossiers;
    }else{
      searchString = 'export-excel-dossier-qnm?&applicant-organization='+ encodeURIComponent(formObj.applicantOrganization.trim()).substring(0, 1000)  + this.makeRequestUrl();
    }

    console.log("nè he:" + searchString);
    if (this.isExportHCM) {
      if(this.selectedDossiers.length > 0){
        searchString = 'export-excel-dossier-hcm?dossier-ids='+ this.selectedDossiers;
      }else{
        searchString = 'export-excel-dossier-hcm?&applicant-organization='+ encodeURIComponent(formObj.applicantOrganization.trim()).substring(0, 1000)  + this.makeRequestUrl();
      }
      await this.dossierService.exportToExcelStatisticHCM(searchString);
      this.waitingDownloadExcel = false;
    } else {
      this.dossierService.getListDossier(searchString).subscribe(async result => {
        this.excelData = result;
        this.dossierService.exportPageDossierToExcelQNM(this.excelData, "danh sách hồ sơ"," Sheet1");
        this.waitingDownloadExcel = false;
      }, error => {
        console.log(error);
        this.waitingDownloadExcel = false;
      });

    }
  }

  async getListDossierAllExcel_QNI() {
    const formObj = this.searchForm.getRawValue();
    this.waitingDownloadExcel = true;
    let searchString = 'export-excel-qni?&applicant-organization='+ encodeURIComponent(formObj.applicantOrganization.trim()).substring(0, 1000)  + this.makeRequestUrl();
    console.log("nè he:" + searchString);
    if (this.qni_listsearch)
    {
        let listSubAgencys:any = [];
        let listAgencys = [];
        if (this.savedItems.length == 0) {
        const idTemp = await this.qniStatisticsService.getRootAgencies(`?id=${this.userAgency?.id}`).toPromise();
        if(idTemp != null && idTemp.length > 0)
        listAgencys = [idTemp[0]?.id];
        else
        listAgencys = [this.userAgency?.id]
        let search = "?parent-id="+listAgencys
        listSubAgencys = await this.basedataService.getSubAgencyByParentId(search).toPromise();
        if(listSubAgencys != null && listSubAgencys.length>0)
        listAgencys = [...listSubAgencys.map(x=>x.id)] ;
        }else
        {
          listAgencys = [...this.savedItems];
          searchString += "&agency-ids=" + listAgencys.join(',');
        }
  
    }
    this.dossierService.getListDossier(searchString).subscribe(async result => {
      this.excelData = result;
      this.dossierService.exportPageDossierToExcelQni(this.excelData, "danh sách hồ sơ"," Sheet1");
      this.waitingDownloadExcel = false;
    }, error => {
      console.log(error);
      this.waitingDownloadExcel = false;
    });
  }
  downloadAllFile(id) {
    this.dossierService.getAllFileDossier(id).subscribe(data => {

      data.forEach(element => {
        this.procedureService.downloadFile(element.id, id).subscribe(data => {
          const dataType = data.type;
          const binaryData = [];
          binaryData.push(data);
          const downloadLink = document.createElement('a');
          downloadLink.href = window.URL.createObjectURL(new Blob(binaryData, { type: dataType }));
          downloadLink.setAttribute('download', element.filename);
          document.body.appendChild(downloadLink);
          downloadLink.click();
        }, err => {
          const msgObj = {
            vi: 'Không tìm thấy file!',
            en: 'File not found!'
          };
          this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
          console.log(err);
        });
      });

    }, err => {
      const msgObj = {
        vi: 'Không tìm thấy văn bản của hồ sơ!',
        en: 'File dossier not found!'
      };
      this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
      console.log(err);
    });
  }
  paginate() {
    const formObj = this.searchForm.getRawValue();
    if (this.isQNM) {
      // tslint:disable-next-line: max-line-length
      this.getListDossier('search?sort=updatedDate,desc&page=' + (this.pageIndex - 1) + '&size=' + this.size + '&spec=' + this.paginationType + this.makeRequestUrl() + '&paystatus=' + formObj.payStatus);
      // tslint:disable-next-line: max-line-length
      this.router.navigateByUrl('/dossier/search?page=' + (this.pageIndex) + '&size=' + this.size + '&spec=' + this.paginationType + this.makeRequestUrl() + '&paystatus=' + formObj.payStatus);
    } else {
      let userAgencyId: any = '';
      let agencyTypeIds = [];
      const userAgency = JSON.parse(localStorage.getItem('userAgency'));
      if (this.userAgency !== null) {
        userAgencyId = this.userAgency.id;
        if (!!this.userAgency.parent) {
          userAgencyId = this.userAgency.parent.id;
        }
        if (!!userAgency.tag) {
          agencyTypeIds = userAgency.tag;
        }
      }

      if (this.enableRegistrationDossier && this.remindId === '60f52e0d09cbf91d41f88834') {
        let searchStringHCM = 'search?page=' + (this.pageIndex - 1) + '&size=' + this.size
          + '&spec=' + this.paginationType
          + '&in-list=1'
          + '&agency-id=' + userAgency.id
          + '&parent-agency-id=' + userAgencyId
          + '&agency-type-id=' + agencyTypeIds.toString()
          + '&dossier-status=0,1,3,12,14,15,19'
          + '&remind-id=60f52e0d09cbf91d41f88834'
          + '&change-menu-task-remind=' + this.changeMenuTaskRemind;
        if (this.isShowFilterSortHCM) {
          searchStringHCM += '&sort=' + this.getSortHcmType()
            + '&is-sort-hcm=true';
        } else if (this.updatedDateSort) {
          searchStringHCM += '&sort=updatedDate,desc';
        }
        if(this.showFilterPaymentStatus){

          if(formObj.paymentStatus)
          {
            searchStringHCM += '&payment-status=' + formObj.paymentStatus.toString();
          }
        }
        if(this.allowFilterAcceptedDateOptimization){
          searchStringHCM += "&filter-payment-appointment-date=true";
        }
        if(this.allowFilterPaymentStatusListAgency && this.allowFilterPaymentStatusListAgency.length > 0 && this.checkAgency()){
          searchStringHCM += "&filter-payment-agency=true";
        }
        if(this.allowFilterAcceptedDateOptimization){
          searchStringHCM += "&filter-payment-appointment-date=true";
        }
        if(this.allowFilterPaymentStatusListAgency && this.allowFilterPaymentStatusListAgency.length > 0 && this.checkAgency()){
          searchStringHCM += "&filter-payment-agency=true";
        }
        this.getListDossier(searchStringHCM);
        this.router.navigateByUrl('/dossier/search?page=' + (this.pageIndex) + '&size=' + this.size + '&spec=' + this.paginationType
          + '&in-list=1'
          + '&agency-id=' + userAgency.id
          + '&parent-agency-id=' + userAgencyId
          + '&agency-type-id=' + agencyTypeIds.toString()
          + '&dossier-status=0,1,3,12,14,15,19'
          + '&remind-id=60f52e0d09cbf91d41f88834');
      } else {
        let searchString = 'search?page=' + (this.pageIndex - 1) + '&size=' + this.size + '&spec=' + this.paginationType + this.makeRequestUrl();
        searchString +='&change-menu-task-remind=' + this.changeMenuTaskRemind;
        if (this.isShowFilterSortHCM) {
          searchString += '&sort=' + this.getSortHcmType()
            + '&is-sort-hcm=true';
        } else if (this.updatedDateSort) {
          searchString += '&sort=updatedDate,desc';
        }else {
          if (this.deploymentService.env.OS_QNI.isSortByOwnerFullname && formObj.ownerFullname.trim().length > 0){
            searchString += '&sort=applicant.data.ownerFullname,desc';
          }
        }
        if (!this.confirmPayment) {
          searchString += "&remove-status=18";
        }
        if(this.showFilterPaymentStatus){
          if(formObj.paymentStatus)
          {
            searchString += '&payment-status=' + formObj.paymentStatus.toString();
          }
        }
        searchString+='&taxCode='+ formObj.taxCode + '&resPerson=' +formObj.resPerson
        if(this.allowFilterAcceptedDateOptimization){
          searchString += "&filter-payment-appointment-date=true";
        }
        if(this.allowFilterPaymentStatusListAgency && this.allowFilterPaymentStatusListAgency.length > 0 && this.checkAgency()){
          searchString += "&filter-payment-agency=true";
        }
        if(this.allowFilterAcceptedDateOptimization){
          searchString += "&filter-payment-appointment-date=true";
        }
        if(this.allowFilterPaymentStatusListAgency && this.allowFilterPaymentStatusListAgency.length > 0 && this.checkAgency()){
          searchString += "&filter-payment-agency=true";
        }
        if (this.isUserRQ)
        {
          this.getListDossier('search?sort=' + this.sortType + '&page=' + (this.pageIndex - 1) + '&size=' + this.size +'&taxCode='+formObj.taxCode+  '&resPerson=' +formObj.resPerson +'&spec=' + this.paginationType + this.makeRequestUrl());
        }
        else
        {
          this.getListDossier(searchString);
        }
        // tslint:disable-next-line: max-line-length
        this.router.navigateByUrl('/dossier/search?page=' + (this.pageIndex) + '&size=' + this.size + '&spec=' + this.paginationType + this.makeRequestUrl());
      }
    }
  }

  postTimesheet() {
    const formObj = this.searchForm.getRawValue();
    const requestBody = JSON.stringify(this.listTimesheet, null, 2);
    this.dossierService.postTimesheet(requestBody).subscribe(data => {
      const newDate = tUtils.newDate();
      let i = 0;
      data.forEach(dt => {
        dt.timesheet = {
          isOverDue: false,
          timer: {
            day: 0,
            hour: 0,
            minute: 0,
            second: 0
          }
        };

        let dateDue = dt.due;
        if (!!this.listTimesheet[i] && !!this.listTimesheet[i].appointmentDate) {
          dateDue = this.listTimesheet[i].appointmentDate;
        }
        let time = '';
        const timesheetIdDossier = this.listTimesheet[i].timesheet.id;
        const dossierIdDossier = this.listTimesheet[i].dossier.id;
        let dueDateDossier = this.listTimesheet[i].appointmentDate;
        let nowDate = newDate;
        const checkOffDayDossier = true;
        if (new Date(dateDue).getTime() < newDate.getTime()) {
          dt.timesheet.isOverDue = true;
          time = ((newDate.getTime() - new Date(dateDue).getTime()) / 1000).toString();
        } else {
          time = ((new Date(dateDue).getTime() - newDate.getTime()) / 1000).toString();
        }

        // let seconds = time;
        let seconds = parseInt(time, 10);
        const days = Math.floor(seconds / (3600 * 24));
        seconds -= days * 3600 * 24;
        const hrs = Math.floor(seconds / 3600);
        seconds -= hrs * 3600;
        const mnts = Math.floor(seconds / 60);
        seconds -= mnts * 60;

        dt.timesheet.timer = {
          day: days,
          hour: hrs,
          minute: mnts,
          second: seconds
        };

        this.ELEMENTDATA.forEach(elem => {
          if (elem.id === dt.dossier.id) {

            elem.due.push(dt);
            const statusMakeColor  = [...this.statusNeedsCalculatorTiming, 8, 9, 10, 11];
            // tslint:disable-next-line:max-line-length
            const isDossierTaskStatusChangeColor  = ['60ed1b7c09cbf91d41f87fa0', '60ebf0db09cbf91d41f87f8c'].includes(elem?.dossierTaskStatus?.id);
            // tslint:disable-next-line:max-line-length
            const checkHideOverdue = !!elem?.acceptedDate && ((statusMakeColor.includes(elem.dossierStatus.id) && elem?.undefindedCompleteTime === 0 && elem.dossierStatus?.checkFinaceObligating === false) || isDossierTaskStatusChangeColor);
            if (elem.due[0].timesheet.isOverDue === true && checkHideOverdue){
              if (this.env?.colorDue ){
                dt.timesheet.color = 'red';
              }
            }
            if (parseInt(elem?.processingTime, 10) <= 6 && dt.timesheet.isOverDue === false && checkHideOverdue){
                if (this.env?.colorDue && days <= (parseInt(elem?.processingTime, 10) / 2) ) {
                  dt.timesheet.color = 'rgb(188 194 14 / 87%)';
                }
            }else{
              if (this.env?.colorDue && days <= 3 && dt.timesheet.isOverDue === false && checkHideOverdue) {
                dt.timesheet.color = 'rgb(188 194 14 / 87%)';
              }
            }
          }
        });
        i++;
      });

      data.forEach(dt => {
        this.ELEMENTDATA.forEach(elem => {
          if (elem.appointmentDate == null || this.hasIsProcessingTimeAfterAddition(elem)) {
            elem.appointmentDate = new Date(dt.due);
          }
          if (elem.id === dt.dossier.id) {
            elem.dossierEndDate = new Date(dt.due);
          }
        });
      });

      this.dataSource.data = this.ELEMENTDATA;
      this.changeDetectorRef.detectChanges();
    });
  }

  dossierDetail(event:MouseEvent,dossierId, procedureId, task) {
    const queryParamsObject = {
      procedure: procedureId,
    };

    if (task !== undefined) {
      Object.assign(queryParamsObject, { task: task[task.length - 1].id });
    }
    const isOpenInNewTabHCM = this.deploymentService.env?.OS_HCM?.isOpenInNewTabHCM
    const isOpenInNewTab = this.deploymentService.getAppDeployment()?.env?.OS_KTM?.openInNewTab === true ? true  : false;
    if (isOpenInNewTab) {
      const url = this.router.serializeUrl(
        this.router.createUrlTree(['dossier/search/' + dossierId], {
          queryParams: queryParamsObject
        })
      );

      window.open(url, '_blank');
      return;
    }
    if(isOpenInNewTabHCM&& event.ctrlKey){
      const url = this.router.serializeUrl(
        this.router.createUrlTree(['dossier/search/' + dossierId],{
          queryParams: queryParamsObject
        }),
      );
      window.open(url, '_blank');
      return;
    }
    const formObj = this.searchForm.getRawValue();
    this.router.navigate(['dossier/search/' + dossierId], {
      // queryParams: queryParamsObject
      queryParams: {
        code: formObj.code.trim(),
        identity: formObj.identityNumber?.trim(),
        applicant: formObj.applicantName?.trim(),
        // procedure: formObj.advProcedure,
        procedure: procedureId,
        sector: formObj.advSector,
        applyMethod: formObj.advApplyMethod,
        province: formObj.advProvince,
        district: formObj.advdistrict,
        ward: formObj.advWard,
        ownerFullname:formObj.ownerFullname?.trim(),
        acceptFrom: (formObj.advAcceptFrom ? this.datePipe.transform(formObj.advAcceptFrom, 'yyyy-MM-dd') : ''),
        acceptTo: (formObj.advAcceptTo ? this.datePipe.transform(formObj.advAcceptTo, 'yyyy-MM-dd') : ''),
        status: formObj.advTaskStatusId,
        remindId: this.remindId,
        sortId: this.sortId,
        // organization: formObj.advAddressOrganization?.trim(),
        applicantOrganization: formObj.applicantOrganization.trim(),
        appointmentFrom: (formObj.advAppointmentFrom ? this.datePipe.transform(formObj.advAppointmentFrom, 'yyyy-MM-dd') : ''),
        appointmentTo: (formObj.advAppointmentTo ? this.datePipe.transform(formObj.advAppointmentTo, 'yyyy-MM-dd') : ''),
        resultReturnedFrom: (formObj.avdResultReturnedFrom ? this.datePipe.transform(formObj.avdResultReturnedFrom, 'dd/MM/yyyy') : ''),
        resultReturnedTo: (formObj.avdResultReturnedTo ? this.datePipe.transform(formObj.avdResultReturnedTo, 'dd/MM/yyyy') : ''),
        // checkConnectProcess: formObj.checkConnectProcess,
        receiptCode: formObj.receiptCode?.trim(),
        taxCode: formObj.taxCode,
        resPerson: formObj.resPerson,
        dossierProcessingStatus: formObj.advProcessStatus,
        phoneNumberApply: formObj.phoneNumberApply?.trim(),
        vnpostStatus: formObj.vnpostStatus
      }
    });
  }

  //   onRightClick(event,dossierId, procedureId, task){
  //     this.dossierDetail(event,dossierId, procedureId, task)
  // }
  onRightClick(dossierId,procedureId){
    
    const queryParamsObject = {
      procedure: procedureId,

    };
    const isOpenInNewTabHCM = this.deploymentService.env?.OS_HCM?.isOpenInNewTabHCM
    if(isOpenInNewTabHCM){
      const url = this.router.serializeUrl(
        this.router.createUrlTree(['dossier/search/' + dossierId],{
          queryParams: queryParamsObject
        }),
      );
      window.open(url, '_blank');
      return;
    }
    return false;
  }


  getStatusColor(type) {
    switch (type) {
      case 0: return '#c47a04';
      case 1: return '#FF9800';
      case 2: return '#c47a04';
      case 3: return '#FF9800';
      case 4: return '#03A9F4';
      case 5: return '#03A9F4';
      case 6: return '#DE1212';
      default: return '#c47a04';
    }
  }

  setAuthStatusName(statusId){
    const status = this.listAuthenticationStatus.find(status => status.id == statusId);
    if (status) {
      return status.name;
    } else {
      return "Không xác định";
    }
  }

  check(dossier){
    if(this.env.OS_HCM?.isNotReceivingAgency == true){
      if (dossier.currentTask !== undefined && dossier.currentTask.length > 0){
        if (!!dossier.currentTask[0].agency){
          // tslint:disable-next-line:max-line-length
          return dossier.currentTask[0].agency.name.find(v => v.languageId === this.selectedLangId).name;
        }
      }else if(dossier.previousTask !== undefined && dossier.previousTask.length > 0){
        if (!!dossier.previousTask[0].agency){
          // tslint:disable-next-line:max-line-length
          return dossier.previousTask[0].agency.name.find(v => v.languageId === this.selectedLangId).name;
        }
      }
    }else{
      return dossier.agency?.name ?? "N/A";
    }
  } 

  returnAccept(id, code) {
    // tslint:disable-next-line: max-line-length
    const dialogData = new ConfirmationDialogModel('Trả hồ sơ về chờ tiếp nhận', `Bạn có chắc chắn muốn trả hồ sơ ${code} về chờ tiếp nhận?`);
    const dialogRef = this.dialog.open(ConfirmDialogComponent, {
      width: '512px',
      data: dialogData,
      disableClose: true,
      autoFocus: false,
    });
    dialogRef.afterClosed().subscribe(async dialogResult => {
      if (!!dialogResult) {
        await this.getDossierFee(id);
        //Kiem tra ho so co phi, neu cmu == true -> check bat buoc thanh toan, neu tinh khac = false k thay doi
      if(this.checkRequirePaymentCMUAuto){
        const dossierFeeData = [];
        const dossierFeeNotRequire = this.dossierFee.filter(el=>el.paid == 0);
        console.log("dossierFeeNotRequire" , dossierFeeNotRequire);
        console.log("this.dossierFee" , this.dossierFee);
        dossierFeeNotRequire.forEach(datasource => {
          dossierFeeData.push({
            procost: {
              id: datasource.procost.id,
              type: {
                id: datasource.procost?.type?.id,
                type: datasource.procost.type?.type,
                quantityEditable: datasource.procost?.type.quantityEditable,
                name: datasource.procost?.type.name
              },
              description: datasource.procost.description
            },
            amount: datasource.amount,
            quantity: datasource.quantity,
            paid: datasource.paid,
            required: 0
          });
        });
        let requestBody = {
          dossierFee: dossierFeeData,
        };

        if(dossierFeeData.length > 0){
          this.dossierService.putDossierOnline(id, JSON.stringify(requestBody, null, 2)).subscribe(async data => {
            console.log('Đã cập nhật');
            console.log(requestBody);
          });
        }
      }


        this.dossierService.putDossierReturnAccept(id).subscribe(async data => {
          if (data.affectedRows === 1) {
            const msgObj = {
              vi: 'Trả hồ sơ thành công!',
              en: 'Return dossier successful!'
            };
            if (this.deploymentService?.env?.OS_HBH?.enableLDXHTriNam) {
              const dataDossier = await this.dossierService.getDossierDetail(id).toPromise();
              const procedureData = await this.procedureService.getProcedureDetail(dataDossier?.procedure?.id).toPromise();
              if (!!procedureData?.btxhcode) {
                Object.assign(dataDossier, {contentTask: 'Trả hồ sơ về chờ tiếp nhận'});
                this.trinamService.syncTaskLDXH(dataDossier);
              }
            }
            // tslint:disable-next-line: max-line-length
            this.snackbarService.openSnackBar(1, msgObj[localStorage.getItem('language')], '', 'success_notification', this.config.expiredTime);
            this.onConfirmSearch();
            const dataLog = {
              dossierId: id,
              code : code
            };
            this.logmanService.postUserEventsLog('dossierReturnAccept ListSearch', dataLog).subscribe();
          } else {
            const msgObj = {
              vi: 'Trả hồ sơ thất bại!',
              en: 'Return dossier failed!'
            };
            // tslint:disable-next-line: max-line-length
            this.snackbarService.openSnackBar(0, msgObj[localStorage.getItem('language')], '', 'error_notification', this.config.expiredTime);
          }
          this.countDossier = true;
          this.callRemindTask();
        });
      }
    });
  }

  async syncAdditinalRequestIntoVBDLIS(code){
    let isVbdlis = true;
    let messageError = "";
          const responseCreateVbdlis = await this.dossierService.postDossierAdditionalRequestVbdlis(code, this.vbdlisConfigId).toPromise().catch(error => {
            isVbdlis = false;
            console.log(error);
          });
          isVbdlis = responseCreateVbdlis.affectedRows == 0 ? false : true;
          messageError = responseCreateVbdlis.message ? responseCreateVbdlis.message : "Cập nhật bổ sung VBDLIS thất bại!";
          console.log("vbdlis" + responseCreateVbdlis.data);

          if (isVbdlis == true) {
            const msgObj = {
              vi: 'Bổ sung/gia hạn/tạm dừng VBDLIS thành công!',
              en: 'Successful reception VBDLIS!'
            };
            this.snackbarService.openSnackBar(1, msgObj[this.selectedLang], '', 'success_notification', this.config.expiredTime);
          } else {
            const msgObj = {
              vi: messageError,
              en: 'Failed reception VBDLIS!'
            };
            this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', 2000);
          }
  }

  forceEndProcess(id, code) {
    const checkbox = {
      enable: true,
      title: "Không cập nhật quá hạn trả hồ sơ"
    };
    const dialogData = new ConfirmationDialogModel('', `Bạn có chắc chắn muốn kết thúc hồ sơ "${code}" không?`, true, checkbox);
    const dialogRef = this.dialog.open(ConfirmDialogComponent, {
      width: '512px',
      data: dialogData,
      disableClose: true,
      autoFocus: false,
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
      if (!!dialogResult?.confirm) {
        const requestBody = [
          {
            id,
            code
          }
        ];
        this.dossierService.putDossierForceEndProcess(JSON.stringify(requestBody, null, 2), dialogResult?.checkboxVal).subscribe(async data => {
          if (data.affectedRows === 1) {
            const msgObj = {
              vi: 'Kết thúc hồ sơ thành công!',
              en: 'Force end process successful!'
            };
            if (this.deploymentService?.env?.OS_HBH?.enableLDXHTriNam) {
              const dataDossier = await this.dossierService.getDossierDetail(id).toPromise();
              const procedureData = await this.procedureService.getProcedureDetail(dataDossier?.procedure?.id).toPromise();
              if (!!procedureData?.btxhcode) {
                Object.assign(dataDossier, {contentTask: 'Kết thúc hồ sơ'});
                this.trinamService.syncTaskLDXH(dataDossier);
              }
            }
            if(this.deploymentService?.env?.OS_HBH?.enableSyncToSoHoa){
              this.trinamService.sendSoHoaHBH(code);
            }
            // tslint:disable-next-line: max-line-length
            this.snackbarService.openSnackBar(1, msgObj[localStorage.getItem('language')], '', 'success_notification', this.config.expiredTime);
            this.onConfirmSearch();
            this.logmanService.postUserEventsLog('dossierForceEndProcess', requestBody[0]).subscribe();
          } else {
            const msgObj = {
              vi: 'Kết thúc hồ sơ thất bại!',
              en: 'Force end process failed!'
            };
            // tslint:disable-next-line: max-line-length
            this.snackbarService.openSnackBar(0, msgObj[localStorage.getItem('language')], '', 'error_notification', this.config.expiredTime);
          }
          this.countDossier = true;
          this.callRemindTask();
        });
      }
    });
  }

  reassign(id, code, currentTask, procedureId) {
    if (!!currentTask && currentTask.length == 1) {
      const dialogData = new ReassignDialogModel(id, code, currentTask[0].id);
      const dialogRef = this.dialog.open(ReassignComponent, {
        minWidth: '60vw',
        maxHeight: '80vh',
        data: dialogData,
        disableClose: true,
        autoFocus: false
      });

      dialogRef.afterClosed().subscribe(async (dialogResult) => {
        if (dialogResult === true) {
          const msgObj = {
            vi: 'Điều chỉnh thành công!',
            en: 'Reassign successful!'
          };
          this.snackbarService.openSnackBar(1, msgObj[this.selectedLang], '', 'success_notification', this.config.expiredTime);
        } else if (dialogResult === false) {
          const msgObj = {
            vi: 'Điều chỉnh thất bại!',
            en: 'Reassign failed!'
          };
          this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
        }
      });
    } else if (!!currentTask && currentTask.length > 1) this.onSelectTaskAndReasign(id, code, currentTask, procedureId);
  }
  
  onSelectTaskAndReasign(id, code, currentTask, procedureId) {
    const dialogData = new ConfirmSelectTaskDialogModel(currentTask, id, procedureId, null, true);
    const dialogRef = this.dialog.open(SelectTaskComponent, {
      minWidth: '30vw',
      maxHeight: '80vh',
      data: dialogData,
      disableClose: true,
      autoFocus: false
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
      const selectedTask = currentTask?.filter(task => task.id == dialogResult);
      this.reassign(id, code, selectedTask, procedureId);
    });
  }

  checkLogSync(id, code) {

    const dialogData = new LogSyncDialogModel(id, code);
    const dialogRef = this.dialog.open(LogSyncComponent, {
      minWidth: '60vw',
      maxHeight: '80vh',
      data: dialogData,
      disableClose: true,
      autoFocus: false
    });

    dialogRef.afterClosed().subscribe(async (dialogResult) => {
      if (dialogResult === true) {
        const msgObj = {
          vi: 'Điều chỉnh thành công!',
          en: 'Reassign successful!'
        };
        this.snackbarService.openSnackBar(1, msgObj[this.selectedLang], '', 'success_notification', this.config.expiredTime);
      } else if (dialogResult === false) {
        const msgObj = {
          vi: 'Điều chỉnh thất bại!',
          en: 'Reassign failed!'
        };
        this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
      }
    });
  }

  viewProcess(dossierId, dossierCode) {
    const dialogData = new ProcessHandleDialogModel(dossierId, dossierCode, 'dossierSearch');
    const dialogRef = this.dialog.open(ProcessHandleComponent, {
      minWidth: '70vw',
      data: dialogData,
      disableClose: true,
      autoFocus: false
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
    });
  }

  viewSpecializedProcess(dossierCode) {
    const dialogData = new SpecializedProcessDialogModel(dossierCode);
    const dialogRef = this.dialog.open(ViewSpecializedProcessComponent, {
      minWidth: '70vw',
      data: dialogData,
      disableClose: true,
      autoFocus: false
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
    });
  }

  viewSpecializedProcessVPUB(dossierCode) {
    const dialogData = new SpecializedProcessDialogModel(dossierCode);
    const dialogRef = this.dialog.open(ViewSpecializedProcessVPUBComponent, {
      minWidth: '70vw',
      data: dialogData,
      disableClose: true,
      autoFocus: false
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
    });
  }

  additionalRequirement(dossierId, dossierCode) {
    if (dossierCode === undefined || dossierCode === null) {
      dossierCode = '';
    }
    const dialogData = new ConfirmAdditionalRequirementDialogModel(dossierId, dossierCode);
    const dialogRef = this.dialog.open(AdditionalRequirementComponent, {
      minWidth: '50vw',
      maxHeight: '85vh',
      data: dialogData,
      disableClose: true,
      autoFocus: false
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
      if (dialogResult === true) {
        const msgObj = {
          vi: 'Đã yêu cầu bổ sung!',
          en: 'Additional request sent!'
        };
        this.snackbarService.openSnackBar(1, msgObj[localStorage.getItem('language')], '', 'success_notification', this.config.expiredTime);
        this.onConfirmSearch();
      }

      if (dialogResult === false) {
        const msgObj = {
          vi: 'Yêu cầu bổ sung không thành công!',
          en: 'Additional request failed!'
        };
        this.snackbarService.openSnackBar(1, msgObj[localStorage.getItem('language')], '', 'success_notification', this.config.expiredTime);
      }
      this.countDossier = true;
      this.callRemindTask();
    });
  }

  suspenDialogs(dossierId, dossierCode) {
    if (dossierCode === undefined || dossierCode === null) {
      dossierCode = '';
    }
    const dialogData = new SuspendModel(dossierId, dossierCode);
    const dialogRef = this.dialog.open(SuspendComponent, {
      minWidth: '50vw',
      data: dialogData,
      disableClose: true,
      autoFocus: false
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
      if (dialogResult === true) {
        const msgObj = {
          vi: 'Đã tạm dừng hồ sơ!',
          en: 'Dossier has been suspend!'
        };
        this.snackbarService.openSnackBar(1, msgObj[localStorage.getItem('language')], '', 'success_notification', this.config.expiredTime);
        this.onConfirmSearch();
      }

      if (dialogResult === false) {
        const msgObj = {
          vi: 'Tạm dừng hồ sơ không thành công!',
          en: 'Suspend failed!'
        };
        this.snackbarService.openSnackBar(1, msgObj[localStorage.getItem('language')], '', 'success_notification', this.config.expiredTime);
      }
    });
  }

  refuseDialog(dossierId, dossierCode) {
    if (dossierCode === undefined || dossierCode === null) {
      dossierCode = '';
    }
    const dialogData = new RefuseDialogModel(dossierId, dossierCode);
    const dialogRef = this.dialog.open(RefuseComponent, {
      minWidth: '50vw',
      maxHeight: '75vh',
      data: dialogData,
      disableClose: true,
      autoFocus: false
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
      if (dialogResult === true) {
        const msgObj = {
          vi: 'Đã tạm dừng hồ sơ!',
          en: 'Dossier has been suspend!'
        };
        this.snackbarService.openSnackBar(1, msgObj[localStorage.getItem('language')], '', 'success_notification', this.config.expiredTime);
        this.onConfirmSearch();
      }

      if (dialogResult === false) {
        const msgObj = {
          vi: 'Tạm dừng hồ sơ không thành công!',
          en: 'Suspend failed!'
        };
        this.snackbarService.openSnackBar(1, msgObj[localStorage.getItem('language')], '', 'success_notification', this.config.expiredTime);
      }
    });
  }

  resumeDialog(dossierId, dossierCode) {
    if (dossierCode === undefined || dossierCode === null) {
      dossierCode = '';
    }
    const dialogData = new ResumeProcessingModel(dossierId, dossierCode);
    const dialogRef = this.dialog.open(ResumeProcessingComponent, {
      minWidth: '50vw',
      data: dialogData,
      disableClose: true,
      autoFocus: false
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
      if (dialogResult === true) {
        const msgObj = {
          vi: 'Tiếp tục xử lý hồ sơ!',
          en: 'Dossier has been resumed!'
        };
        this.snackbarService.openSnackBar(1, msgObj[localStorage.getItem('language')], '', 'success_notification', this.config.expiredTime);
        this.onConfirmSearch();
      }

      if (dialogResult === false) {
        const msgObj = {
          vi: 'Tiếp tục xử lý hồ sơ không thành công!',
          en: 'Resume failed!'
        };
        this.snackbarService.openSnackBar(1, msgObj[localStorage.getItem('language')], '', 'success_notification', this.config.expiredTime);
      }
    });
  }
   UpdatePaymentMethod(rowId, rowPaymentMethod){
    let dialogContent = '';
    const applyMethodTypeId = {
      onl: {
        id: "62f5d79b5c424b277f174318",
        name: 'Thanh toán trực tuyến payment',
        code: 'PAYMENT_PLATFORM_HCM_LGSP'
      },
      off: {
        id: "5f7fca83b80e603d5300dcf4",
        name: 'Trực tiếp'
      }
    };
    let requestBody = {
      id: null,
      name: null,
      code: null,
    }
    if(!!rowPaymentMethod){
      if(rowPaymentMethod.id === applyMethodTypeId.onl.id){
        dialogContent = `Bạn có chắc chắn muốn chuyển hồ sơ này sang thanh toán ${applyMethodTypeId.off.name}?`
        requestBody.id = applyMethodTypeId.off.id;
        requestBody.name = applyMethodTypeId.off.name;
      }else if(rowPaymentMethod.id === applyMethodTypeId.off.id){
        dialogContent = `Bạn có chắc chắn muốn chuyển hồ sơ này sang ${applyMethodTypeId.onl.name}?`
        requestBody.id = applyMethodTypeId.onl.id;
        requestBody.name = applyMethodTypeId.onl.name;
        requestBody.code = applyMethodTypeId.onl.code
      }
    }else{ // mặc định là thanh toán trực tiếp
      dialogContent = `Bạn có chắc chắn muốn chuyển hồ sơ này sang ${applyMethodTypeId.onl.name}?`
      requestBody.id = applyMethodTypeId.onl.id;
      requestBody.name = applyMethodTypeId.onl.name;
      requestBody.code = applyMethodTypeId.onl.code
    }
    const dialogData = new ConfirmationDialogModel('',dialogContent, true);
    const dialogRef = this.dialog.open(ConfirmDialogComponent, {
      width: '512px',
      data: dialogData,
      disableClose: true,
      autoFocus: false,
    });
    dialogRef.afterClosed().subscribe(dialogResult =>  {
      if (dialogResult) {
          this.dossierService.putPaymentMethodDossier(rowId,requestBody).subscribe(data => {
            if (data.affectedRows === 1) {
              const msgObj = {
                vi: 'Cập nhật hình thức thanh toán thành công!',
                en: 'Update your form of payment successfully!'
              };
              this.snackbarService.openSnackBar(1, msgObj[localStorage.getItem('language')], '', 'success_notification', this.config.expiredTime);
              this.logmanService.postUserEventsLog('updatePaymentMethod', rowId).subscribe();
              this.onConfirmSearch();
            } else {
              const msgObj = {
                vi: 'Cập nhật hình thức thanh toán thất bại!',
                en: 'Payment form update failed!'
              };
              this.snackbarService.openSnackBar(0, msgObj[localStorage.getItem('language')], '', 'error_notification', this.config.expiredTime);
            }
          })
      }
    })
  }

  deleteDialog(dossierId, dossierCode) {
    if (dossierCode === undefined || dossierCode === null) {
      dossierCode = '';
    }

    const dialogData = new ConfirmDeleteDialogModel(dossierId, dossierCode);
    const dialogRef = this.dialog.open(DeleteDossierComponent, {
      width: '500px',
      data: dialogData,
      disableClose: true,
      autoFocus: false
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
      if (dialogResult === 0) {
        const msgObj = {
          vi: 'Đã xoá hồ sơ!',
          en: 'Dossier deleted!'
        };
        this.snackbarService.openSnackBar(1, msgObj[localStorage.getItem('language')], '', 'success_notification', this.config.expiredTime);
        this.onConfirmSearch();
      }

      if (dialogResult === 1) {
        const msgObj = {
          vi: 'Đã hủy hồ sơ!',
          en: 'Dossier cancelled!'
        };
        this.snackbarService.openSnackBar(1, msgObj[localStorage.getItem('language')], '', 'success_notification', this.config.expiredTime);
        this.onConfirmSearch();
      }

      if (dialogResult === null) {
        const msgObj = {
          vi: 'Xoá không thành công!',
          en: 'Deletion failed!'
        };
        this.snackbarService.openSnackBar(0, msgObj[localStorage.getItem('language')], '', 'error_notification', this.config.expiredTime);
      }
      this.countDossier = true;
      this.callRemindTask();
    });
  }

  addApologyText(dossierId) {
    const dialogData = new ConfirmAddApologyTextComponent(dossierId);
    const dialogRef = this.dialog.open(AddApologyTextComponent, {
      minWidth: '40vw',
      maxWidth: '60vw',
      maxHeight: '60vh',
      data: dialogData,
      disableClose: true,
      autoFocus: false
    });
  }

  signApologyText(dossierId) {
    const dialogData = new ConfirmSignApologyTextComponent(dossierId);
    const dialogRef = this.dialog.open(SignApologyTextComponent, {
      minWidth: '50vw',
      maxWidth: '60vw',
      minHeight: '40vh',
      maxHeight: '70vh',
      data: dialogData,
      disableClose: true,
      autoFocus: false
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
      if (dialogResult === true) {
        const msgObj = {
          vi: 'Đã lưu!',
          en: 'Saved!'
        };
        this.snackbarService.openSnackBar(1, msgObj[localStorage.getItem('language')], '', 'success_notification', this.config.expiredTime);
      } else if (dialogResult === false) {
        const msgObj = {
          vi: 'Lưu thất bại!',
          en: 'Save failed!'
        };
        this.snackbarService.openSnackBar(0, msgObj[localStorage.getItem('language')], '', 'error_notification', this.config.expiredTime);
      }
      this.countDossier = true;
      this.callRemindTask();
    });
  }
  viewProcessingVBDLISOneGate(dossierId, dossierCode) {
    if (dossierCode === undefined || dossierCode === null) {
      dossierId = '';
    }
    const dialogData = new QniViewProcessingTaskOnegateDialogModel(dossierId, dossierCode);
    const dialogRef = this.dialog.open(QniViewProcessingTaskOnegateComponent, {
      minWidth: '95vw',
      data: dialogData,
      disableClose: true,
      autoFocus: false
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
    });
  }
  viewApprovalInfoCommentQni(dossierId, dossierCode) {
    if (dossierCode === undefined || dossierCode === null) {
      return;
    }
    const dialogData = new QniViewCommentInfoModel(dossierId, dossierCode);
    const dialogRef = this.dialog.open(QniViewCommentInfo, {
      minWidth: '95vw',
      minHeight: '80vh',
      data: dialogData,
      disableClose: true,
      autoFocus: false
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
    });
  }
  checkAllItem(event) {
    if (event.checked) {
      this.checkAll = true;
      for (let i = 0; i < this.numberOfElements; i++) {
        if (this.selectedDossiers.indexOf(this.dataSource.data[i].id) === -1) {
          this.selectedDossiers.push(this.dataSource.data[i].id);
          let dossierMenuTaskRemind = null;
          try {
            dossierMenuTaskRemind = {
              id : this.dataSource.data[i].dossierMenuTaskRemind?.id,
              name : this.dataSource.data[i].dossierMenuTaskRemind?.name[0].name
            }
          } catch (error) {

          }
          let dossierTaskStatus = null;
          try {
            dossierTaskStatus = {
              id : this.dataSource.data[i].dossierTaskStatus.id,
              name : this.dataSource.data[i].dossierTaskStatus.name[0].name
            }
          } catch (error) {

          }
          let dossierStatus = null;
          try {
            dossierStatus = {
              id : this.dataSource.data[i].dossierStatus.id,
              name : this.dataSource.data[i].dossierStatus.name[0].name
            }
          } catch (error) {

          }
          this.selectedDossiersLog.push({id : this.dataSource.data[i].id, code : this.dataSource.data[i]?.code, dossierStatus : dossierStatus , dossierTaskStatus: dossierTaskStatus , dossierMenuTaskRemind : dossierMenuTaskRemind});
          this.dataSource.data[i].checked = true;
          const dataDosierTest : any = this.dataSource.data[i];

          const itemExcel: any = {
            ids: this.dataSource.data[i].id,
            // stt: this.dem,
            code: this.dataSource.data[i].code,
            applicantFullName: dataDosierTest.applicant.data.fullname,
            address: dataDosierTest.applicant.data.address + ", " +dataDosierTest.applicant.data.village.label + ", "+dataDosierTest.applicant.data.district.label + ", "+dataDosierTest.applicant.data.province.label + ", "+dataDosierTest.applicant.data.nation.label,
            mobile: dataDosierTest.applicant.data.phoneNumber,
            procedureName: dataDosierTest.procedure.translate.name,
            appliedDate: dataDosierTest.appliedDate ? this.datePipe.transform(dataDosierTest.appliedDate, 'dd/MM/yyyy HH:mm') : '',
            appointmentDate: dataDosierTest.appointmentDate ? this.datePipe.transform(dataDosierTest.appointmentDate, 'dd/MM/yyyy HH:mm') : ''
          };
          this.listExcel.push(itemExcel)
        }
      }
    } else {
      this.checkAll = false;
      for (let i = 0; i < this.numberOfElements; i++) {
        const index = this.selectedDossiers.indexOf(this.dataSource.data[i].id);
        if (index >= 0) {
          this.selectedDossiers.splice(index, 1);
          this.dataSource.data[i].checked = false;
          this.listExcel.splice(index, 1);
        }
        const index1 = this.selectedDossiersLog.findIndex(function(element) {
          return element.id === this.dataSource.data[i].id;
        });
        if (index1 >= 0) {
            this.selectedDossiersLog.splice(index1, 1);
        }
      }

    }
    console.log("this.selectedDossiers", this.selectedDossiers);
  }

  checkItem(event, item) {
    if (event.checked) {
      this.selectedDossiers.push(item.id);
      let dossierMenuTaskRemind = null;
          try {
            dossierMenuTaskRemind = {
              id : item.dossierMenuTaskRemind?.id,
              name : item.dossierMenuTaskRemind?.name[0].name
            }
          } catch (error) {

          }
          let dossierTaskStatus = null;
          try {
            dossierTaskStatus = {
              id : item.dossierTaskStatus.id,
              name : item.dossierTaskStatus.name[0].name
            }
          } catch (error) {

          }
          let dossierStatus = null;
          try {
            dossierStatus = {
              id : item.dossierStatus.id,
              name : item.dossierStatus.name[0].name
            }
          } catch (error) {

          }
      this.selectedDossiersLog.push({id : item.id, code : item.code, dossierStatus : dossierStatus, dossierTaskStatus: dossierTaskStatus, dossierMenuTaskRemind : dossierMenuTaskRemind } );
      let countCheck = true;
      for (let i = 0; i < this.numberOfElements; i++) {
        if (this.dataSource.data[i].checked !== true) {
          countCheck = false;
          break;
        }
      }
      // get list dossier for QNM
      const dataDossier: any = this.dataSource.data.find((obj) =>{
        return obj.id === item.id;
      })
      this.dem++;
      const itemExcel: any = {
        ids: item.id,
        // stt: this.dem,
        code: dataDossier.code,
        applicantFullName: dataDossier.applicant.data.fullname,
        address: dataDossier.applicant.data.address + ", " +dataDossier.applicant.data.village.label + ", "+dataDossier.applicant.data.district.label + ", "+dataDossier.applicant.data.province.label + ", "+dataDossier.applicant.data.nation.label,
        mobile: dataDossier.applicant.data.phoneNumber,
        procedureName: dataDossier.procedure.translate.name,
        appliedDate: dataDossier.appliedDate ? this.datePipe.transform(dataDossier.appliedDate, 'dd/MM/yyyy HH:mm') : '',
        appointmentDate: dataDossier.appointmentDate ? this.datePipe.transform(dataDossier.appointmentDate, 'dd/MM/yyyy HH:mm') : ''
      };
      // const itemDosier: any = {
      //   id: id,
      //   stt: this.dem,
      //   code: dataDossier.code,
      //   applicantFullName: dataDossier.applicant.data.fullname,
      //   address: dataDossier.applicant.data.address,
      //   mobile: dataDossier.applicant.data.phoneNumber,
      //   procedureName: dataDossier.procedure.translate.name,
      //   appliedDate: dataDossier.appliedDate ? this.datePipe.transform(dataDossier.appliedDate, 'dd/MM/yyyy hh:mm') : '',
      //   appointmentDate: dataDossier.appointmentDate ? this.datePipe.transform(dataDossier.appointmentDate, 'dd/MM/yyyy hh:mm') : ''
      // };
      // this.listInfoDossier.push(itemExcel);
      this.listExcel.push(itemExcel);
      this.checkAll = countCheck;
    }
    else {
      this.checkAll = false;
      const i = this.selectedDossiers.indexOf(item.id);
      this.selectedDossiers.splice(i, 1);
      this.listExcel =  this.listExcel.filter(li => li.ids !== item.id);
      const index1 = this.selectedDossiersLog.findIndex(function(element) {
        return element.id === item.id;
      });
      this.selectedDossiersLog.splice(index1, 1);
    }
  }
  async deleteMultiDossier() {
    if (this.selectedDossiers.length <= this.maxDeleteDossierMulti) {
      const dialogData = new ConfirmDeleteMultiModel(this.selectedDossiers, this.selectedDossiersLog);
      const dialogRef = this.dialog.open(DeleteMultiComponent, {
        width: '40%',
        data: dialogData,
        autoFocus: false
      });
      dialogRef.afterClosed().subscribe(dialogResult => {
        const res = dialogResult;
        if (res.success === true) {
          this.snackbarService.openSnackBar(1,
            '',
            res.message,
            'success_notification',
            this.config.expiredTime
          );
          this.pageIndex = 1;
          this.page = 1;
          // this.size = 10;
          this.selectedDossiers = [];
          this.selectedDossiersLog = [];
          // tslint:disable-next-line: max-line-length
          let searchString = 'search?page=0&size=' + this.size + '&spec=' + this.paginationType + this.makeRequestUrl();
          if (this.isShowFilterSortHCM) {
            searchString += '&sort=' + this.getSortHcmType()
              + '&is-sort-hcm=true';
          } else if (this.updatedDateSort) {
            searchString += '&sort=updatedDate,desc';
          }
          if (!this.confirmPayment) {
            searchString += "&remove-status=18";
          }
          this.getListDossier(searchString);
          this.countDossier = true;
          this.callRemindTask();
        } else if (res.success === false) {
          this.snackbarService.openSnackBar(0,
            '',
            res.message,
            'error_notification',
            this.config.expiredTime
          );
        }
      });
    } else {
      const msgObj = {
        vi: 'Giới hạn số hồ sơ được xóa là ',
        en: 'The limit on the number of dossier that can be deleted is '
      };
      this.snackbarService.openSnackBar(0,
        '',
        msgObj[localStorage.getItem('language')] + this.maxDeleteDossierMulti,
        'error_notification',
        this.config.expiredTime
      );
    }
  }
  // ======== Menu remind

  agencyChange(event) {
    this.agencyId = event.value === '' ? null : event.value;
    this.keySearchSectorAgency = '&agency-id=' + this.agencyIdForProcedure;
    this.getListSectorScroll();
    this.getListProcedure('');
    // IGATESUPP-76910
    this.searchForm.get('taskAgencyCtrl').setValue("");
    this.pageAgencyUnit = 0;
    this.getListAgencyUnit();
  }
  async getRemindMenuTask() {
    let userAgencyId: any = '';
    let agencyTypeIds = [];
    const userAgency = JSON.parse(localStorage.getItem('userAgency'));
    if (this.userAgency !== null) {
      userAgencyId = this.userAgency.id;
      if (!!this.userAgency.parent && !this.searchByAncestorAgency) {
        userAgencyId = this.userAgency.parent.id;
      }
      if (!!userAgency.tag) {
        agencyTypeIds = userAgency.tag;
      }
    }
    // STAGE: => Tra cuu ho so toan co quan
    if (this.isNotification) {
      const searchRemind = '&agency-id=' + userAgencyId;

      this.notificationV2Service.getNotificationAgency(searchRemind).subscribe(data => {
        this.listMenuRemind = data;
        this.lengthRemind = data.length;
        this.listMenuRemind = this.listMenuRemind.sort((a, b) => {
          return compare(a.count, b.count, false);
        });
        if (this.isSortMenuRemind) {
          this.listMenuRemind = tUtils.sortObjectsByName(this.listMenuRemind);
        }
      });
    } else {
    const agency = this.searchForm.get('advAgency').value ? this.searchForm.get('advAgency').value : userAgencyId;
    let searchString = 'ancestor-agency-id=' + agency;
    const formObj = this.searchForm.getRawValue();
    const ancestorAgencyExpand = formObj.advAgency ?  formObj.advAgency : userAgencyId;
    if (this.hasParticipatedInProcessing) {
      searchString += !this.xpandStatus ? '&task-ancestor-agency-id=' + userAgencyId : '&task-ancestor-agency-id=' + ancestorAgencyExpand;
    }
    if (this.searchConnectionDossier){
      searchString += '&search-connection-dossier=' + this.searchConnectionDossier;
    }
    if (!this.confirmPayment) {
      searchString += "&remove-status=18";
    }
    searchString += '&change-menu-task-remind=' + this.changeMenuTaskRemind;
    let searchStringHCM =  'remind-id=60f52e0d09cbf91d41f88834'
    + '&in-list=1'
    + '&agency-id=' + userAgency.id
    + '&parent-agency-id=' + userAgencyId
    + '&agency-type-id=' + agencyTypeIds.toString()
    + '&dossier-status=0,1,3,12,14,15,19'
    + '&change-menu-task-remind=' + this.changeMenuTaskRemind;
    if(!this.deploymentService.getAppDeployment()?.disableMenuRemind)
    {
    this.dossierService.getDossierMenuTaskRemindAllAgency(searchString).subscribe(data => {
      this.listMenuRemind = data;
       // đổi tên trạnh thái ycsl QBH
       if(this.qbhmenuaction == true)
       {
         this.listMenuRemind.forEach(id => {
           for (var i = 0; i < this.listMenuRemind.length; i++) {
             if (this.listMenuRemind[i].id === "61ee30eada2d36b037e00001") {
               this.listMenuRemind[i].name = "Xác nhận yêu cầu bổ sung";
             }
             if (this.listMenuRemind[i].id === "61ee30eada2d36b037e00003") {
              this.listMenuRemind[i].name = "Xác nhận xin lỗi và xin gia hạn";
            }
            if (this.listMenuRemind[i].id === "60f52ed209cbf91d41f88838") {
              this.listMenuRemind[i].name = "Từ chối giải quyết";
            }
            if (this.listMenuRemind[i].id === "61ee30eada2d36b037e00004") {
              this.listMenuRemind[i].name = "Xác nhận từ chối giải quyết";
            }
           }
         })
       }
      if(this.env?.OS_HCM?.showAmountDossier){
        if(this.countDossier){
          this.lengthRemind = 0;
          data.forEach(dossier => {
            this.lengthRemind += dossier.count;
          })
        }
      }

      if(this.enableRegistrationDossier){
        const removeIndex = data.map(item => item.id).indexOf("60f52e0d09cbf91d41f88834");
        data.splice(removeIndex,1)
        if(!this.deploymentService.getAppDeployment()?.disableMenuRemind)
        {
        this.dossierService.getDossierMenuTaskRemindAllAgency(searchStringHCM).subscribe(data1=>{
          let resultArr=data.concat(data1)
          this.listMenuRemind = resultArr
          this.lengthRemind = resultArr.length;
          resultArr.sort((a,b)=>{
            return compare(a.count, b.count, false);
          })
          if (this.isSortMenuRemind) {
            resultArr = tUtils.sortObjectsByName(resultArr);
          }
        })
        }
      }
      else {
        this.lengthRemind = data.length;
      }
      this.listMenuRemind = this.listMenuRemind.sort((a, b) => {
        return compare(a.count, b.count, false);
      });
      if (this.isSortMenuRemind) {
        this.listMenuRemind = tUtils.sortObjectsByName(this.listMenuRemind);
      }
    });
    }
    }
  }
  onClickOpenReminderMenu() {
    this.expandReminderMenu = !this.expandReminderMenu;
    this.remindId = '';
    this.changeSearchRemind('','');
  }
  changeSearchRemind(remindId,remindName) {
    this.isSyncLGSPHCM = false;
    const formObj = this.searchForm.getRawValue();
    const userAgency = JSON.parse(localStorage.getItem('userAgency'));
    let userAgencyId: any = '';
    let agencyTypeIds = [];
    this.remindId = remindId;
    this.pageIndex = 1;
    this.page = 1;
    this.size = 10;
    //QNM
    if(this.isQNM === true && remindName === this.showBB) {
      this.showBtbBB = true;
    } else {
      this.showBtbBB = false;
    }
    if (this.userAgency !== null) {
      userAgencyId = this.userAgency.id;
      if (!!this.userAgency.parent) {
        userAgencyId = this.userAgency.parent.id;
      }
      if (!!userAgency.tag) {
        agencyTypeIds = userAgency.tag;
      }
    }
      if(this.enableRegistrationDossier && remindId==='60f52e0d09cbf91d41f88834'){
        let searchStringHCM = 'search?page=0&size=' + this.size
        + '&in-list=1'
        + '&agency-id=' + userAgency.id
        + '&parent-agency-id=' + userAgencyId
        + '&agency-type-id=' + agencyTypeIds.toString()
        + '&dossier-status=0,1,3,12,14,15,19'
        + '&change-menu-task-remind=' + this.changeMenuTaskRemind
        +'&applicant-organization='+ encodeURIComponent(formObj.applicantOrganization.trim()).substring(0, 1000) + '&spec=' + this.paginationType;

        if (this.isShowFilterSortHCM) {
          searchStringHCM += '&sort=' + this.getSortHcmType()
            + '&is-sort-hcm=true';
        } else if (this.updatedDateSort) {
          searchStringHCM += '&sort=updatedDate,desc';
        }
        if(this.showRemindSyncLGSPHCM && this.isSyncLGSPHCM){
          searchStringHCM  +=  '&sync-LGSPHCM=0';
        } else {
          searchStringHCM  +=  + '&remind-id=60f52e0d09cbf91d41f88834' ;
        }
        if(this.allowFilterAcceptedDateOptimization){
          searchStringHCM += "&filter-payment-appointment-date=true";
        }
        if(this.allowFilterPaymentStatusListAgency && this.allowFilterPaymentStatusListAgency.length > 0 && this.checkAgency()){
          searchStringHCM += "&filter-payment-agency=true";
        }
        this.getListDossier(searchStringHCM);
      }
    else{
      let searchString = 'search?page=0&size=' + this.size  +
      '&applicant-organization='+ encodeURIComponent(formObj.applicantOrganization.trim()).substring(0, 1000) + '&spec=' + this.paginationType + this.makeRequestUrl();
      if (this.isShowFilterSortHCM) {
        searchString += '&sort=' + this.getSortHcmType()
          + '&is-sort-hcm=true';
      } else if (this.updatedDateSort) {
        searchString += '&sort=updatedDate,desc';
      }
      // if(this.showRemindSyncLGSPHCM && this.isSyncLGSPHCM){
      //   searchString +=  '&sync-LGSPHCM=0';
      // }
      if (!this.confirmPayment) {
        searchString += "&remove-status=18";
      }
      if(this.allowFilterAcceptedDateOptimization){
        searchString += "&filter-payment-appointment-date=true";
      }
      if(this.allowFilterPaymentStatusListAgency && this.allowFilterPaymentStatusListAgency.length > 0 && this.checkAgency()){
        searchString += "&filter-payment-agency=true";
      }
      searchString +='&change-menu-task-remind=' + this.changeMenuTaskRemind
      this.getListDossier(searchString);
    }
  }

  async getCountSyncLGSP(){
    const formObj = this.searchForm.getRawValue();
    const userAgency = JSON.parse(localStorage.getItem('userAgency'));
    let userAgencyId: any = '';
    let agencyTypeIds = [];

    this.pageIndex = 1;
    this.page = 1;
    this.size = 10;
    //QNM

    if (this.userAgency !== null) {
      userAgencyId = this.userAgency.id;
      if (!!this.userAgency.parent) {
        userAgencyId = this.userAgency.parent.id;
      }
      if (!!userAgency.tag) {
        agencyTypeIds = userAgency.tag;
      }
    }

      let searchString = 'search?page=0&size=' + this.size  +
      '&applicant-organization='+ encodeURIComponent(formObj.applicantOrganization.trim()).substring(0, 1000) + '&spec=page' + this.makeRequestUrl();
      if (this.isShowFilterSortHCM) {
        searchString += '&sort=' + this.getSortHcmType()
          + '&is-sort-hcm=true';
      } else if (this.updatedDateSort) {
        searchString += '&sort=updatedDate,desc';
      }
      if(this.showRemindSyncLGSPHCM ){
        searchString +=  '&sync-LGSPHCM=0';
      }
      if (!this.confirmPayment) {
        searchString += "&remove-status=18";
      }
      if(this.showRemindSyncLGSPHCM ){
        let isLGSPHCM = true;
      }
      const dataSyncLGSP = await this.dossierService.getListDossier(searchString + this.searchDomain).toPromise();
      this.countSyncLGSPHCM = dataSyncLGSP.totalElements;
      console.log('this.countSyncLGSPHCM', this.countSyncLGSPHCM);
  }

  async getSyncLGSP(){
    this.isSyncLGSPHCM = true;
    this.remindId ='';
    const formObj = this.searchForm.getRawValue();
    const userAgency = JSON.parse(localStorage.getItem('userAgency'));
    let userAgencyId: any = '';
    let agencyTypeIds = [];

    this.pageIndex = 1;
    this.page = 1;
    this.size = 10;
    //QNM

    if (this.userAgency !== null) {
      userAgencyId = this.userAgency.id;
      if (!!this.userAgency.parent) {
        userAgencyId = this.userAgency.parent.id;
      }
      if (!!userAgency.tag) {
        agencyTypeIds = userAgency.tag;
      }
    }

      let searchString = 'search?page=0&size=' + this.size  +
      '&applicant-organization='+ encodeURIComponent(formObj.applicantOrganization.trim()).substring(0, 1000) + '&spec=' + this.paginationType + this.makeRequestUrl();
      if (this.isShowFilterSortHCM) {
        searchString += '&sort=' + this.getSortHcmType()
          + '&is-sort-hcm=true';
      } else if (this.updatedDateSort) {
        searchString += '&sort=updatedDate,desc';
      }
      if(this.showRemindSyncLGSPHCM ){
        searchString +=  '&sync-LGSPHCM=0';
      }
      if (!this.confirmPayment) {
        searchString += "&remove-status=18";
      }
      if(this.allowFilterAcceptedDateOptimization){
        searchString += "&filter-payment-appointment-date=true";
      }
      if(this.allowFilterPaymentStatusListAgency && this.allowFilterPaymentStatusListAgency.length > 0 && this.checkAgency()){
        searchString += "&filter-payment-agency=true";
      }
      this.getListDossier(searchString);

  }


  callRemindTask() {
    this.getRemindMenuTask();
    // this.adminLayoutNavComponent.getDossierRemind();
  }
  getMaxDeleteDossierMulti() {
    const config = this.deploymentService.getAppDeployment();
    if (!!config?.env?.maxDeleteDossierMulti) {
      this.maxDeleteDossierMulti = config?.env?.maxDeleteDossierMulti;
    }
  }
  withdrawDialogs(dossierId, dossierCode, isAllAgencySearch?, isDirectory?){
    if (dossierCode === undefined || dossierCode === null) {
      dossierCode = '';
    }
    const dialogData = new WithdrawModel(dossierId, dossierCode, isAllAgencySearch, isDirectory);
    const dialogRef = this.dialog.open(WithdrawComponent, {
      minWidth: '45vw',
      data: dialogData,
      disableClose: true,
      autoFocus: false
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
      if (dialogResult === true) {
        const msgObj = {
          vi: 'Đã rút hồ sơ!',
          en: 'Dossier has been withdraw!'
        };
        this.snackbarService.openSnackBar(1, msgObj[localStorage.getItem('language')], '', 'success_notification', this.config.expiredTime);
        if (this.env?.changeRemindAllagencyMenu){
          this.countDossier = true;
          this.callRemindTask();
        }
        this.onConfirmSearch();
      }
      if (dialogResult === false) {
        const msgObj = {
          vi: 'Rút hồ sơ không thành công!',
          en: 'Withdraw dossier failed!'
        };
        this.snackbarService.openSnackBar(1, msgObj[localStorage.getItem('language')], '', 'success_notification', this.config.expiredTime);
      }
    });
  }
  withdrawQBHDialogs(dossierId, dossierCode, isAllAgencySearch?, isDirectory?){
    if (dossierCode === undefined || dossierCode === null) {
      dossierCode = '';
    }
    const dialogData = new WithdrawQBHModel(dossierId, dossierCode, isAllAgencySearch, isDirectory);
    const dialogRef = this.dialog.open(WithdrawQBHComponent, {
      minWidth: '45vw',
      data: dialogData,
      disableClose: true,
      autoFocus: false
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
      if (dialogResult === true) {
        const msgObj = {
          vi: 'Đã rút hồ sơ!',
          en: 'Dossier has been withdraw!'
        };
        this.snackbarService.openSnackBar(1, msgObj[localStorage.getItem('language')], '', 'success_notification', this.config.expiredTime);
        if (this.env?.changeRemindAllagencyMenu){
          this.countDossier = true;
          this.callRemindTask();
        }
        this.onConfirmSearch();
      }
      if (dialogResult === false) {
        const msgObj = {
          vi: 'Rút hồ sơ không thành công!',
          en: 'Withdraw dossier failed!'
        };
        this.snackbarService.openSnackBar(1, msgObj[localStorage.getItem('language')], '', 'success_notification', this.config.expiredTime);
      }
    });
  }
  withdrawVPCDialogs(dossierId, dossierCode, isAllAgencySearch?, isDirectory?){
    if (dossierCode === undefined || dossierCode === null) {
      dossierCode = '';
    }
    const dialogDataConfirm = new ConfirmationDialogModel('', `Bạn có chắc chắn muốn rút hồ sơ?`,true);
    const dialogConfirmRef = this.dialog.open(VPCConfirmDialogComponent, {
      width: '512px',
      data: dialogDataConfirm,
      disableClose: true,
      autoFocus: false,
    });
    dialogConfirmRef.afterClosed().subscribe(dialogConfirmResult => {
      if (dialogConfirmResult) {
        const dialogData = new WithdrawVPCModel(dossierId, dossierCode, isAllAgencySearch, isDirectory);
        const dialogRef = this.dialog.open(WithdrawVpcComponent, {
          minWidth: '45vw',
          data: dialogData,
          disableClose: true,
          autoFocus: false
        });
        dialogRef.afterClosed().subscribe(dialogResult => {
          if (dialogResult === true) {
            const msgObj = {
              vi: 'Đã rút hồ sơ!',
              en: 'Dossier has been withdraw!'
            };
            this.snackbarService.openSnackBar(1, msgObj[localStorage.getItem('language')], '', 'success_notification', this.config.expiredTime);
            if (this.env?.changeRemindAllagencyMenu){
              this.countDossier = true;
              this.callRemindTask();
            }
            this.onConfirmSearch();
          }
          if (dialogResult === false) {
            const msgObj = {
              vi: 'Rút hồ sơ không thành công!',
              en: 'Withdraw dossier failed!'
            };
            this.snackbarService.openSnackBar(1, msgObj[localStorage.getItem('language')], '', 'success_notification', this.config.expiredTime);
          }
        });
      }
    });
  }
  checkPremissonQBH()
  {
    const permissions = this.userService.getUserPermissions();
    if (permissions) {
      permissions.forEach(p => {
        if (p?.permission?.code === 'qbhwithdrawprocess' && this.qbhwithdrawprocess == true) {
          this.checkDrawQBH = true;
        }
      });
    }
  }
  setTotalElements(data, paginationType) {
    if (paginationType === 'page') {
      this.countResult = data.totalElements;
    } else {
      if (data.last) {
        if (!!data.number) {
          this.page = data.number + 1;
          this.countResult = this.size * this.page;
        } else {
          this.countResult = this.size * this.page;
        }
      } else {
        if (this.numberOfElements < this.size) {
          this.countResult = this.size * this.page;
        } else {
          this.countResult = this.countResult + this.ELEMENTDATA.length + 1;
        }
      }
    }
  }

  changePageOrSize(obj) {
    if (tUtils.hasOwnProperty(obj, 'currentPage')) {
      this.page = obj.currentPage;
      this.pageIndex = obj.currentPage;
    }
    if (tUtils.hasOwnProperty(obj, 'itemsPerPage')) {
      this.size = obj.itemsPerPage;
    }
    if (tUtils.hasOwnProperty(obj, 'totalItems')) {
      this.countResult = obj.totalItems;
    } else {
      this.paginate();
    }
  }

  getListConfigTemplateDetail(agencyId, procedureId) {
    const searchString = '?procedure-id=' + procedureId + '&agency-id=' + agencyId;
    this.procedureService.getListConfigTemplate(searchString).subscribe(data => {
      this.listConfigTemplateDetail = data;
    });
  }


  getListConfigTemplate() {
    const searchString = '?agency-id=' + this.userAgency.id;
    this.procedureService.getListConfigTemplate(searchString).subscribe(data => {
      this.listConfigTemplate = data;
    });
  }

  //IGATESUPP-51996
  getSortHcmType() {
    switch (this.sortHcmId) {
      case '0': {
        return 'updatedDate,desc';
      }
      case '1': {
        return 'dueDate,asc';
      }
      case '2': {
        return 'dueDate,desc';
      }
      case '3': {
        return 'appliedDate,asc';
      }
      case '4': {
        return 'appliedDate,desc';
      }
    }
    return 'updatedDate,desc';
  }

  viewPreview(reportType) {
    let agencyRootName: string = '';
    const fullname = localStorage.getItem("tempUsername");
    const userAgency = JSON.parse(localStorage.getItem('userAgency'));
    if (userAgency !== null) {
      agencyRootName = userAgency.name;
    } else {
      if (Number(localStorage.getItem('languageId')) === 228) {
        agencyRootName = this.config.rootAgency.trans.vi.name;
      }
      else {
        agencyRootName = this.config.rootAgency.trans.vi.name;
      }
    }
    let url = '';
    let dossierId = this.selectedDossiers.toString();
    this.keycloakService.getToken().then(token => {
        let dataPost = {};
        if(!!this.enablePrintBillNew){
            dataPost = {
                "report": reportType,
                "apiGateway": this.apiProviderService.getUrl('digo', 'padman'),
                "dossierId": dossierId,
                "agencyUser": agencyRootName.replace(/&/g, "%26").replace(/>/g, "&gt;").replace(/</g, "&lt;").replace(/"/g, "&quot;"),
                "transferredUser": fullname,
                "numberUser" : this.mobileNumberAgency,
                "addressUser" : this.addressFull
            };
        }else{
      url = this.config.birtviewerURL
        + 'output?__report=' + reportType
        + '&&displayNone=true&__dpi=96&__format=html&__pageoverflow=0&__overwrite=false'
        + '&token=' + token
        + '&apiGateway=' + this.apiProviderService.getUrl('digo', 'padman')
        + '&dossierId=' + dossierId
        + '&agencyUser=' + agencyRootName.replace(/&/g, "%26").replace(/>/g, "&gt;").replace(/</g, "&lt;").replace(/"/g, "&quot;")
        + '&transferredUser=' + fullname
        + '&numberUser=' + this.mobileNumberAgency
        + '&addressUser=' + this.addressFull;
        }
      //this.procedureReportService.getReportTemplate(url).subscribe(data => {
      //  console.log(data);
      //}, err => {
        const dialogData = new ConfirmPrintTemplateDialogModel(url, this.enablePrintBillNew, dataPost);
        const dialogRef = this.dialog.open(PrintTemplateComponent, {
          minWidth: '55vw',
          minHeight: '90vh',
          data: dialogData,
          autoFocus: false
        });
      //});
    });
  }

  exportToExcel() {
    let title = '';
    let textBanner = '';
    let subtextBanner = '';
    let total = '';
    let subColumns: any[] = [];
    let agencyRootName = '';
    let sign = '';
    let subTitle = '';
    let dateExcel = '';
    let from = [];
    let listInfo = [];
    const fullNameText = localStorage.getItem("tempUsername");
    for (let m = 0; m < this.listExcel.length ; m++) {
      const listDossier : any = {
        stt: m + 1,
        code: this.listExcel[m]?.code,
        applicantFullName: this.listExcel[m]?.applicantFullName,
        address: this.listExcel[m]?.address,
        mobile: this.listExcel[m]?.mobile,
        procedureName: this.listExcel[m]?.procedureName,
        appliedDate: this.listExcel[m]?.appliedDate,
        appointmentDate: this.listExcel[m]?.appointmentDate,
      };
      listInfo.push(listDossier);
    }

    dateExcel = this.datePipe.transform(new Date(), 'dd/MM/yyyy');
    from = dateExcel.split('/');
    dateExcel = 'Ngày ' + from[0] + ' tháng ' + from[1] + ' năm ' + from[2];

    const userAgency = JSON.parse(localStorage.getItem('userAgency'));
    let count: number = this.listExcel.length;
    if (userAgency !== null) {
      agencyRootName = userAgency.name;
    } else {
      if (Number(localStorage.getItem('languageId')) === 228) {
        agencyRootName = this.config.rootAgency.trans.vi.name;
      }
      else {
        agencyRootName = this.config.rootAgency.trans.vi.name;
      }
    }
    let name = 'Biên bản bàn giao phiếu ';
    if (localStorage.getItem('language') === 'vi') {
      title = 'BIÊN BẢN BÀN GIAO PHIẾU';
      textBanner = 'CỘNG HÒA XÃ HỘI CHỦ NGHĨA VIỆT NAM';
      subtextBanner = 'Độc lập - Tự do - Hạnh phúc';
      total = 'Tổng cộng';
      this.columns = ['STT', 'MÃ HỒ SƠ', 'TÊN NGƯỜI NỘP', 'ĐỊA CHỈ', 'SỐ ĐIỆN THOẠI','NỘI DUNG HỒ SƠ','NGÀY KÝ', 'NGÀY TRẢ THEO QUY ĐỊNH'];
      subColumns = [];
      sign = 'Người lập báo cáo';
    } else if (localStorage.getItem('language') === 'en') {
      title = 'REPORT HANDOVER';
      textBanner = 'CỘNG HÒA XÃ HỘI CHỦ NGHĨA VIỆT NAM';
      subtextBanner = 'Độc lập - Tự do - Hạnh phúc';
      total = 'Total';
      this.columns = ['NO.', 'DOSSIER CODE', 'NAME OF APPLICANT', 'ADDRESS', 'MOBILE NUMBER', 'CONTENT', 'SIGN DAY', 'PAY DAY'];
      subColumns = [];
      sign = 'Reporter';
    }
    this.footerData = [[
      total, "","", count +" hồ sơ"
    ]];
    subTitle = dateExcel;
    this.exportExcel.exportAsExcelFilerReportDossier(title, subTitle, this.columns, subColumns, listInfo, this.footerData, name, 'Sheet1', '', this.agencyNameExcel, textBanner, subtextBanner, agencyRootName, fullNameText, this.mobileNumberAgency, this.addressFull,sign);
  }

  //get for QNM
  getAccepterInfo() {
    this.keycloakService.loadUserProfile().then(user => {
      this.accepterInfo.username = user.username;
      // tslint:disable-next-line: no-string-literal
      this.accepterInfo.id = user['attributes'].user_id[0];
      // tslint:disable-next-line: no-string-literal
      this.accepterInfo.accountId = user['attributes'].account_id[0];
      // tslint:disable-next-line: no-string-literal
      this.userService.getUserInfo(user['attributes'].user_id[0]).subscribe(data => {
        this.accepterInfo.fullname = data.fullname;
      });
      const listUserAgency = JSON.parse(localStorage.getItem('listAgencyUser'));
      if (!!listUserAgency && listUserAgency.length !== 0) {
        this.listUserAgency = listUserAgency;
      }

      const userAgency = JSON.parse(localStorage.getItem('userAgency'));
      const userExperienceAgency = JSON.parse(localStorage.getItem('userExperienceAgency'));
      if (!!userAgency) {
        this.userAgency = userAgency;
        this.getAgencyInfo(userAgency.id);
        this.getAgencyFullAddressInfo(userAgency.id);
      }
    });
  }

  async getAgencyInfo(agencyId) {
    return new Promise(resolve => {
      this.dossierService.getAgencyInfo(agencyId).subscribe(data => {
        this.mobileNumberAgency = data.telephone === null ? "" : data.telephone;
        resolve(true);
      }, err => {
        resolve(false);
      });
    });

  }
  async getAgencyFullAddressInfo(agencyId) {
    return new Promise(resolve => {
      this.dossierService.getAgencyFullAddress(agencyId).subscribe(rs => {
          this.addressAgency = rs.address === null ? "" : rs.address;
          this.addressCityAgency =  rs.place?.ancestors == null ? "" : rs.place?.ancestors[0]?.name;
          this.addressProvinceAgency =  rs.place?.ancestors == null ? "" : rs.place?.ancestors[1]?.name;
          this.addressDistrictAgency = rs.place == null ? "" : rs.place?.name;
        this.addressFull = this.addressAgency + ','+ this.addressDistrictAgency + ','+ this.addressCityAgency + ',' + this.addressProvinceAgency
        resolve(true);
      }, err => {
        this.addressFull = '';
        resolve(false);
      });
    });
  }

  //Tuesday 18/04/2023 - quocpa-IGATESUPP-44355
  checkDateValid(startDate, endDate) {
    return new Promise((resolve) => {
      try {
        const checkDate: Date = tUtils.newDate();
        const dateTo: string = checkDate.getFullYear() + '-' + ('0' + (checkDate.getMonth() + 1)).slice(-2) + '-' + ('0' + checkDate.getDate()).slice(-2);
        if ((startDate === null || startDate === '' || startDate === undefined) && (endDate !== null || endDate !== '' || endDate !== undefined)) {
          const msgObj = {
            vi: 'Vui lòng chọn Ngày nộp từ ngày!',
            en: 'Please select Date of submission from!'
          };
          this.snackbarService.openSnackBar(0, '', msgObj[this.selectedLang], 'error_notification', this.config.expiredTime);
          resolve(false);
        } else {
          if ((endDate === null || endDate === '' || endDate === undefined) && (startDate !== null || startDate !== '')) {
            this.searchForm.patchValue({advFilingTo: dateTo});
            resolve(true);
          } else {
            if (new Date(startDate) > new Date(endDate)) {
              const msgObj = {
                vi: 'Ngày nộp từ ngày không được lớn hơn Ngày nộp đến ngày!',
                en: 'The date of submission from date cannot be greater than the date of submission to date!'
              };
              this.snackbarService.openSnackBar(0, '', msgObj[this.selectedLang], 'error_notification', this.config.expiredTime);
              resolve(false);
            } else {
              resolve(true);
            }
          }
        }
      }
      catch (error) {
        resolve(true);
      }
    });
  }
  sortChange() {
    if(sessionStorage.getItem('dossierArrSortType')){
      sessionStorage.setItem('dossierArrSortType', this.sortId);
    }
  }
  setSortId(){
    const userAgency = JSON.parse(localStorage.getItem('userAgency'));
    let array: string[] = this.deploymentService.env.OS_HCM.listAgencyUseSortType;
    var parentAgencyId = (userAgency.parent != null && userAgency.parent != undefined) ? userAgency.parent.id : null;
    if(array.filter((value) => (value.includes(userAgency.id) || (parentAgencyId != null &&  value.includes(parentAgencyId)))).length > 0 && this.env?.OS_HCM?.dossierArrSortType !== undefined){
      if (!sessionStorage.getItem('dossierArrSortType')) {
        sessionStorage.setItem('dossierArrSortType', this.env?.OS_HCM?.dossierArrSortType);
      }
      this.sortId = sessionStorage.getItem('dossierArrSortType');
      this.isShowSortHCM = true;
    }
    this.sortHcmId = '4';
    // else{
    //   sessionStorage.removeItem('dossierArrSortType');
    // }
  }

  async downloadAsPDF() {
    const formObj = this.searchForm.getRawValue();
    this.waitingDownloadExcel = true;
    let searchString = '';
    if(this.selectedDossiers.length > 0){
      searchString = 'export-excel-dossier-qnm?dossier-ids='+ this.selectedDossiers;
    }else{
      searchString = 'export-excel-dossier-qnm?&applicant-organization='+ encodeURIComponent(formObj.applicantOrganization.trim()).substring(0, 1000)  + this.makeRequestUrl();
    }
    console.log("nè he:" + searchString);

    this.dossierService.getListDossier(searchString).subscribe(async data => {
      console.log("result", data);
      var string ="";
      for (let i = 0; i < data.length; i++) {
        data[i].stt = i + 1;
        var noidung = data[i].applicant?.data?.noidungyeucaugiaiquyet != null && data[i].applicant?.data?.noidungyeucaugiaiquyet != undefined?data[i].applicant?.data?.noidungyeucaugiaiquyet:'';
        var acceptedDate = this.datePipe.transform(data[i].acceptedDate, 'dd/MM/yyyy HH:mm:ss')!= null? this.datePipe.transform(data[i].acceptedDate, 'dd/MM/yyyy HH:mm:ss'):'';
        var appointmentDate = this.datePipe.transform(data[i].appointmentDate, 'dd/MM/yyyy HH:mm:ss') != null?this.datePipe.transform(data[i].appointmentDate, 'dd/MM/yyyy HH:mm:ss'):'';
        var completedDate = this.datePipe.transform(data[i].completedDate, 'dd/MM/yyyy HH:mm:ss')!=null?this.datePipe.transform(data[i].completedDate, 'dd/MM/yyyy HH:mm:ss'):'';
        var ownerFullname = data[i].applicant?.data?.ownerFullname!=undefined?data[i].applicant?.data?.ownerFullname:'';
        let ownerPhoneNumber = !!data[i].applicant?.data?.ownerPhoneNumber ? data[i].applicant?.data?.ownerPhoneNumber : '';
        let ownerAddress = !!data[i].applicant?.data?.ownerAddress ? data[i].applicant?.data?.ownerAddress : '';
        let address = !!data[i].applicant?.data?.address ? data[i].applicant?.data?.address : ''
          + (!!data[i].applicant?.data?.village?.label ? ',' + data[i].applicant?.data?.village?.label : '')
          + (!!data[i].applicant?.data?.district?.label ? ',' + data[i].applicant?.data?.district?.label : '')
          + (!!data[i].applicant?.data?.province?.label ? ',' + data[i].applicant?.data?.province?.label : '');

        string += '<tr>';
        string += '<td style="text-align:center;vertical-align: middle;font-size: 13px;">'+data[i].stt+'</td>';
        string += '<td style="vertical-align: middle;font-size: 13px;">'+data[i].code+'</td>';
        string += '<td style="vertical-align: middle;font-size: 13px;">'+noidung+'</td>';
        string += '<td style="vertical-align: middle;font-size: 13px;">Ngày tiếp nhận:'+acceptedDate+'<br>Ngày hẹn trả:'+appointmentDate+'<br>Ngày kết thúc xử lý:'+completedDate+'</td>';
        string += '<td style="vertical-align: middle;font-size: 13px;">'+ownerFullname+'</td>';
        string += '<td style="vertical-align: middle;font-size: 13px;">'+data[i].applicant?.data?.fullname+'</td>';
        string += '<td style="vertical-align: middle;font-size: 13px;">'+data[i].applicant?.data?.phoneNumber+'</td>';
        string += '<td style="vertical-align: middle;font-size: 13px;">'+data[i].dossierTaskStatus?.name[0]?.name+'</td>';
        if (this.isAddColumDataExport) {
            string += '<td style="vertical-align: middle;font-size: 13px;">'+ownerPhoneNumber+'</td>';
            string += '<td style="vertical-align: middle;font-size: 13px;">'+ownerAddress+'</td>';
            string += '<td style="vertical-align: middle;font-size: 13px;">'+ address +'</td>';
        }
        string += '</tr>';
      }
      document.getElementById("tbody").innerHTML = string;
      const pdf = htmlToPdfmake(this.pdfContent.nativeElement.innerHTML, { tableAutoSize:true});
      let str:string = 'landscape';
      let pageOrientation: PageOrientation = str as PageOrientation;
      var documentDefinition = { header: '', content: pdf, pageOrientation : pageOrientation};
      console.log("pdf", pdf, documentDefinition);
      pdfmake.createPdf(documentDefinition).download('DanhSachHoSo.pdf');
      this.waitingDownloadExcel = false;
    }, error => {
      console.log(error);
      this.waitingDownloadExcel = false;
    });


  }

  getCheckHideDossierTaskStatus(row){
    if (this.hideInforAdditionalRequirementDate && this.hideInforAdditionalRequirementDateDossierList.includes(row?.dossierTaskStatus?.id)) {
      return true;
    }
    //IGATESUPP-81849
    if(row?.dossierStatus?.id === this.dossierService?.StatusYCBS){
      return true;
    }
    return false;
  }

  syncDossierLGSPHCM(dossier){
    let dossierId = dossier.id;
    let dossierCode = dossier.code;
    const content = {
      dossier: {
        id: dossierId,
        code: dossierCode
      },
      packageType: "4", //Loại gói tin:  4 - Gói tin đồng bộ kết quả
    };
    const requestBody = JSON.stringify(content, null, 2);

    this.adapterService.syncDossierLGSPHCM(requestBody)
    .subscribe((data: any) => {
      if(data.error_code != "0") // 0 thành công #0 thất bại
      {
        const msgObjNoti = {
          vi: 'Hồ sơ ' + dossierCode + ' gửi về LGSP HCM thất bại!',
          en: 'Dossier ' + dossierCode + ' sent to LGSP HCM failed!'
        };
        this.snackbarService.openSnackBar(0, msgObjNoti[localStorage.getItem('language')], '', 'error_notification', this.config.expiredTime);
      }else{
        const msgObjNoti = {
          vi: 'Hồ sơ ' + dossierCode + ' về LGSP HCM thành công!',
          en: 'Dossier ' + dossierCode + ' sent to LGSP HCM success!'
        };
        this.snackbarService.openSnackBar(1, msgObjNoti[localStorage.getItem('language')], '', 'success_notification', this.config.expiredTime);
      }

    }, error => {
      const msgObjNoti = {
        vi: 'Hồ sơ ' + dossierCode + ' gửi về LGSP HCM thất bại!',
        en: 'Dossier ' + dossierCode + ' sent to LGSP HCM failed!'
      };
      this.snackbarService.openSnackBar(0, msgObjNoti[localStorage.getItem('language')], '', 'error_notification', this.config.expiredTime);
    });
  }

  viewProcessingVBDLIS(dossierId, dossierCode) {
    if (dossierCode === undefined || dossierCode === null) {
      dossierId = '';
    }
    const dialogData = new QniViewProcessingVbdlisDialogModel(dossierId, dossierCode);
    const dialogRef = this.dialog.open(QniViewProcessingVbdlisComponent, {
      minWidth: '95vw',
      data: dialogData,
      disableClose: true,
      autoFocus: false
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
    });
  }
  isShowDossierReceiptCreationStatus():boolean{
    return this.dossierReceiptCreationStatusConfig.enable
    && this.showReceiptCreationStatusWhiteList.some(id=>this.dossierReceiptCreationStatusConfig.applyAgencies.includes(id))
  }

  checkagencyAcceptDurationByWorkingTime() {
    if (this.durationByWorkingTime  && this.durationByWorkingTime.turnOn) {
      let userAgency = JSON.parse(localStorage.getItem('userAgency'));
      if((this.durationByWorkingTime.agencyList.includes(userAgency.id) || (tUtils.nonNull(userAgency?.parent, 'id') && this.durationByWorkingTime.agencyList.includes(userAgency.parent.id)))) {
        return this.agencyAcceptDurationByWorkingTime = true;
      } else {
        return this.agencyAcceptDurationByWorkingTime = false;
      }
    }
  }

  async postTimesheetv2 () {
    const requestBodyForTimeSheetV2 = JSON.stringify(this.listTimeSheetGenV2, null, 2);
    let dossierDurationList: any[] = [];
    await this.dossierService.genDurationTimesheetV2(requestBodyForTimeSheetV2).toPromise().then(res => {
      if(res && res.length > 0)
      {
        dossierDurationList = res;
      }
    });
    const formObj = this.searchForm.getRawValue();
    const requestBody = JSON.stringify(this.listTimesheet, null, 2);
    this.dossierService.postTimesheet(requestBody).subscribe(data => {
      const newDate = tUtils.newDate();
      let i = 0;
      data.forEach(dt => {
        dt.timesheet = {
          isOverDue: false,
          timer: {
            day: 0,
            hour: 0,
            minute: 0,
            second: 0
          }
        };

        let dateDue = dt.due;
        if (!!this.listTimesheet[i] && !!this.listTimesheet[i].appointmentDate) {
          dateDue = this.listTimesheet[i].appointmentDate;
        }
        let time = '';
        let days = 0;
        let hrs = 0;
        let mnts = 0;
        let sct = 0;
        if (new Date(dateDue).getTime() < newDate.getTime()) {
          dt.timesheet.isOverDue = true;
          //time = ((newDate.getTime() - new Date(dateDue).getTime()) / 1000).toString();
        }
          const index = dossierDurationList.findIndex(duration => {
            return duration.dossier.id === dt.dossier.id;
          });
          if (index != -1) {
            let durationDossierWorkingTime = dossierDurationList[index].duration;
            let remainingTime = 0;
            let days = Math.floor(durationDossierWorkingTime / 1);
            remainingTime = durationDossierWorkingTime - days;
            hrs = Math.floor(remainingTime * 8);
            remainingTime = (remainingTime * 8) - hrs;
            mnts = Math.floor(remainingTime * 60);
            remainingTime = (remainingTime * 60) - mnts;
            sct = Math.floor(remainingTime * 60);

            dt.timesheet.timer = {
              day: days,
              hour: hrs,
              minute: mnts,
              second: sct
            };
          }

        this.ELEMENTDATA.forEach(elem => {
          if (elem.id === dt.dossier.id) {

            elem.due.push(dt);
            const statusMakeColor  = [...this.statusNeedsCalculatorTiming, 8, 9, 10, 11];
            // tslint:disable-next-line:max-line-length
            const isDossierTaskStatusChangeColor  = ['60ed1b7c09cbf91d41f87fa0', '60ebf0db09cbf91d41f87f8c'].includes(elem?.dossierTaskStatus?.id);
            // tslint:disable-next-line:max-line-length
            const checkHideOverdue = !!elem?.acceptedDate && ((statusMakeColor.includes(elem.dossierStatus.id) && elem?.undefindedCompleteTime === 0 && elem.dossierStatus?.checkFinaceObligating === false) || isDossierTaskStatusChangeColor);
            if (elem.due[0].timesheet.isOverDue === true && checkHideOverdue){
              if (this.env?.colorDue ){
                dt.timesheet.color = 'red';
              }
            }
            if (parseInt(elem?.processingTime, 10) <= 6 && dt.timesheet.isOverDue === false && checkHideOverdue){
                if (this.env?.colorDue && days <= (parseInt(elem?.processingTime, 10) / 2) ) {
                  dt.timesheet.color = 'rgb(188 194 14 / 87%)';
                }
            }else{
              if (this.env?.colorDue && days <= 3 && dt.timesheet.isOverDue === false && checkHideOverdue) {
                dt.timesheet.color = 'rgb(188 194 14 / 87%)';
              }
            }
          }
        });
        i++;
      });

      data.forEach(dt => {
        this.ELEMENTDATA.forEach(elem => {
          if (elem.id === dt.dossier.id) {
            elem.dossierEndDate = new Date(dt.due);
          }
        });
      });

      this.dataSource.data = this.ELEMENTDATA;
      this.changeDetectorRef.detectChanges();
    });
  }

  postTimesheetChangeCalculation() {
    const formObj = this.searchForm.getRawValue();
    const requestBody = JSON.stringify(this.listTimesheet, null, 2);
    // this.dossierService.postTimesheet(requestBody).subscribe(data => {
    const newDate = tUtils.newDate();
    let i = 0;
    this.listTimesheet.forEach(dt => {
      dt.timesheet = {
        isOverDue: false,
        timer: {
          day: 0,
          hour: 0,
          minute: 0,
          second: 0
        }
      };

      let dateDue = dt.dueDate;
      if (!!this.listTimesheet[i] && !!this.listTimesheet[i].appointmentDate) {
        dateDue = this.listTimesheet[i].appointmentDate;
      }
      let time = '';
      if (new Date(dateDue).getTime() < newDate.getTime()) {
        dt.timesheet.isOverDue = true;
        time = ((newDate.getTime() - new Date(dateDue).getTime()) / 1000).toString();
      } else {
        time = ((new Date(dateDue).getTime() - newDate.getTime()) / 1000).toString();
      }

      // let seconds = time;
      let seconds = parseInt(time, 10);
      const days = Math.floor(seconds / (3600 * 24));
      seconds -= days * 3600 * 24;
      const hrs = Math.floor(seconds / 3600);
      seconds -= hrs * 3600;
      const mnts = Math.floor(seconds / 60);
      seconds -= mnts * 60;

      dt.timesheet.timer = {
        day: days,
        hour: hrs,
        minute: mnts,
        second: seconds
      };

      this.ELEMENTDATA.forEach(elem => {
        if (elem.id === dt.dossier.id) {

          elem.due.push(dt);
          const statusMakeColor = [...this.statusNeedsCalculatorTiming, 8, 9, 10, 11];
          // tslint:disable-next-line:max-line-length
          const isDossierTaskStatusChangeColor = ['60ed1b7c09cbf91d41f87fa0', '60ebf0db09cbf91d41f87f8c'].includes(elem?.dossierTaskStatus?.id);
          // tslint:disable-next-line:max-line-length
          const checkHideOverdue = !!elem?.acceptedDate && ((statusMakeColor.includes(elem.dossierStatus.id) && elem?.undefindedCompleteTime === 0 && elem.dossierStatus?.checkFinaceObligating === false) || isDossierTaskStatusChangeColor);
          if (elem.due[0].timesheet.isOverDue === true && checkHideOverdue) {
            if (this.env?.colorDue) {
              dt.timesheet.color = 'red';
            }
          }
          if (parseInt(elem?.processingTime, 10) <= 6 && dt.timesheet.isOverDue === false && checkHideOverdue) {
            if (this.env?.colorDue && days <= (parseInt(elem?.processingTime, 10) / 2)) {
              dt.timesheet.color = 'rgb(188 194 14 / 87%)';
            }
          } else {
            if (this.env?.colorDue && days <= 3 && dt.timesheet.isOverDue === false && checkHideOverdue) {
              dt.timesheet.color = 'rgb(188 194 14 / 87%)';
            }
          }
        }
      });
      i++;
    });

    this.listTimesheet.forEach(dt => {
      this.ELEMENTDATA.forEach(elem => {
        if (elem.appointmentDate == null || this.hasIsProcessingTimeAfterAddition(elem)) {
          elem.appointmentDate = new Date(dt.appointmentDate);
        }
        if (elem.id === dt.dossier.id) {
          elem.dossierEndDate = new Date(dt.appointmentDate);
        }
      });
    });

    this.dataSource.data = this.ELEMENTDATA;
    // });
  }

  async postTimesheetv2ChangeCalculation() {
    const formObj = this.searchForm.getRawValue();
    const requestBody = JSON.stringify(this.listTimesheet, null, 2);
    // this.dossierService.postTimesheet(requestBody).subscribe(data => {
    const newDate = tUtils.newDate();
    let i = 0;
    this.listTimesheet.forEach(dt => {
      dt.timesheet = {
        isOverDue: false,
        timer: {
          day: 0,
          hour: 0,
          minute: 0,
          second: 0
        }
      };

      let dateDue = dt.dueDate;
      if (!!this.listTimesheet[i] && !!this.listTimesheet[i].appointmentDate) {
        dateDue = this.listTimesheet[i].appointmentDate;
      }
      let days = 0;
      let hrs = 0;
      let mnts = 0;

      const dueDateMilliseconds = (new Date(dateDue)).getTime();
      const dueDateSeconds = Math.floor(dueDateMilliseconds / 1000);
      //---
      const newDateMilliseconds = (new Date(newDate)).getTime();
      const newDateSeconds = Math.floor(newDateMilliseconds / 1000);
      let timeDueDate = dueDateSeconds - newDateSeconds;
      console.log("dueDate- newdate", dueDateSeconds - newDateSeconds);
      if (timeDueDate < 0) {
        dt.timesheet.isOverDue = true;
        timeDueDate = -timeDueDate;
        //time = ((newDate.getTime() - new Date(dateDue).getTime()) / 1000).toString();
      }

      let secondsTask = timeDueDate;
      days = Math.floor(secondsTask / (3600 * 24));
      secondsTask -= days * 3600 * 24;
      hrs = Math.floor(secondsTask / 3600);
      secondsTask -= hrs * 3600;
      mnts = Math.floor(secondsTask / 60);
      secondsTask -= mnts * 60;
      dt.timesheet.timer = {
        day: days,
        hour: hrs,
        minute: mnts,
        second: secondsTask
      };


      this.ELEMENTDATA.forEach(elem => {
        if (elem.id === dt.dossier.id) {

          elem.due.push(dt);
          const statusMakeColor = [...this.statusNeedsCalculatorTiming, 8, 9, 10, 11];
          // tslint:disable-next-line:max-line-length
          const isDossierTaskStatusChangeColor = ['60ed1b7c09cbf91d41f87fa0', '60ebf0db09cbf91d41f87f8c'].includes(elem?.dossierTaskStatus?.id);
          // tslint:disable-next-line:max-line-length
          const checkHideOverdue = !!elem?.acceptedDate && ((statusMakeColor.includes(elem.dossierStatus.id) && elem?.undefindedCompleteTime === 0 && elem.dossierStatus?.checkFinaceObligating === false) || isDossierTaskStatusChangeColor);
          if (elem.due[0].timesheet.isOverDue === true && checkHideOverdue) {
            if (this.env?.colorDue) {
              dt.timesheet.color = 'red';
            }
          }
          if (parseInt(elem?.processingTime, 10) <= 6 && dt.timesheet.isOverDue === false && checkHideOverdue) {
            if (this.env?.colorDue && days <= (parseInt(elem?.processingTime, 10) / 2)) {
              dt.timesheet.color = 'rgb(188 194 14 / 87%)';
            }
          } else {
            if (this.env?.colorDue && days <= 3 && dt.timesheet.isOverDue === false && checkHideOverdue) {
              dt.timesheet.color = 'rgb(188 194 14 / 87%)';
            }
          }
        }
      });
      i++;
    });

    this.listTimesheet.forEach(dt => {
      this.ELEMENTDATA.forEach(elem => {
        if (elem.id === dt.dossier.id) {
          elem.dossierEndDate = new Date(dt.appointmentDate);
        }
      });
    });

    this.dataSource.data = this.ELEMENTDATA;
    // });
  }
  async getDossierFee(dossierId) {
    await this.getDossierPayment(dossierId);

    return new Promise<void>(resolve => {
      this.dossierService.getDossierFee(dossierId).subscribe(data => {
        this.dossierFee = data;
        //this.getProcostForOnline();
        resolve();
      }, err => {
        resolve();
      });
    });
  }

  getDossierPayment(dossierId) {
    return new Promise<void>(resolve => {
      this.dossierService.getDossierPayment('?page=0&size=50&spec=page&status=1&dossier-id=' + dossierId).subscribe(data => {
        this.dossierPayment = data.content;
        // tslint:disable-next-line:prefer-for-of
        for (let i = 0 ; i < this.dossierPayment.length; i++){
          this.dossierPayment[i].paymentName = '';
          if (!!this.dossierPayment[i].paymentMethod){
            const paymentMethod = this.dossierPayment[i].paymentMethod;
            if (!!paymentMethod.name && paymentMethod.name.length > 0){
              this.dossierPayment[i].paymentName = paymentMethod.name[0].name;
              const nameList = paymentMethod.name;
              // tslint:disable-next-line:prefer-for-of
              for (let k = 0 ; k < nameList.length; k++){
                if (nameList[k].language === this.localeId){
                  this.dossierPayment[i].paymentName = nameList[k].name;
                  break;
                }
              }
            }
          }
        }
        resolve();
      }, err => {
        resolve();
      });
    });
  }

  hasIsProcessingTimeAfterAddition(obj: any): obj is DossierSearchElement & { isProcessingTimeAfterAddition: boolean } {
    return obj.isProcessingTimeAfterAddition === true;
  }

  async postTimesheetReceiving(type?: number, newDate?) {
    const newDateDefault = tUtils.newDate();
    // type = 1: appointmentDate
    const listTimesheet = [];
    let processProcessingTime = this.dossier.procedureProcessDefinition.processDefinition.processingTime;
    let timeAfterAddition = 0;
    // if(this.deploymentService.env?.OS_HCM?.processingTimeAfterAddition?.enable
    //   && this.dossierDetail.dossierTaskStatus.id == this.deploymentService.env?.OS_HCM?.processingTimeAfterAddition?.idStatus){
    //   const dataProcessDetail = await this.processService.getProcessDetail(this.dossier.procedureProcessDefinition?.processDefinition?.id).toPromise();
    //   if(!!dataProcessDetail.dynamicVariable.processingTimeAfterAddition){
    //     processProcessingTime = Number(dataProcessDetail.dynamicVariable.processingTimeAfterAddition);
    //   }
    // }
    if (processProcessingTime !== undefined && processProcessingTime != 0) {
      let processingTime = 0;
      switch (this.dossier.procedureProcessDefinition.processDefinition.processingTimeUnit) {
        case 'y':
          processingTime = processProcessingTime * 365;
          break;
        case 'M':
          processingTime = processProcessingTime * 30;
          break;
        case 'd':
          processingTime = processProcessingTime;
          break;
        case 'H:m:s':
          processingTime = processProcessingTime / 24;
          break;
        case 'h':
          processingTime = Number(processProcessingTime) / 24;
          break;
        case 'm':
          processingTime = Number(processProcessingTime) / (24 * 60);
          break;
      }
      listTimesheet.push({
        timesheet: {
          id:
            this.dossier.procedureProcessDefinition?.processDefinition
              ?.timesheet !== undefined
              ? this.dossier.procedureProcessDefinition
                .processDefinition.timesheet.id
              : this.config.defaultTimesheetId,
        },
        dossier: {
          id: this.dossierId,
        },
        duration: this.deploymentService.env.timesheetV2 ? processProcessingTime : processingTime,
        startDate: newDate || newDateDefault,
        endDate: null,
        checkOffDay: true,
        offTime: (this.env?.limitedAppointmentTime && type == 1) ? this.env?.limitedAppointmentTime : null,
        extendHCM: this.deploymentService.env.OS_HCM?.limitedAppointmentTimeOnProcedure ? {
          offTime: this.deploymentService.env.OS_HCM?.limitedAppointmentTimeOnProcedure,
          startAtNextDay: this.procedure.extendHCM?.startAtNextDay ?? false,
          appointmentAtNextDay: this.procedure.extendHCM?.appointmentAtNextDay ?? false
        } : null,
        processingTimeUnit: this.dossier.procedureProcessDefinition.processDefinition.processingTimeUnit,
      });
    }

    const requestBody = JSON.stringify(listTimesheet, null, 2);
    const data = await this.genTime(requestBody);

    if (!!data) {
      return data[0].due;
    } else {
      return null;
    }
  }

  genTime(requestBody) {
    return new Promise(resolve => {
      this.dossierService.postTimesheet(requestBody).subscribe(data => {
        if (!!data && data.length !== 0) {
          resolve(data);
        } else {
          this.errGenTimeCount += 1;
          resolve(null);
        }
      }, (err) => {
        this.errGenTimeCount += 1;
        resolve(null);
      });
    });
  }

  getIsDVCBC(dossier: any):any {
    return dossier?.dossierReceivingKind?.id == this.trackingBTTTT?.dvcbcId ? 1 : dossier?.dossierReceivingKind?.id == this.tagDossierReceivingDirectlyId ? 0 : 2;
  }

//   async getDossierDetailData() {
//     return new Promise(resolve => {
//       this.dossierService.getDossierDetail(this.dossierId).subscribe(data => {
//         resolve(data);
//       }, err => {
//         resolve(null);
//       });
//     });
//   }

  async checkBeforeStartTime (timesheetId, curent): Promise<any> {
    return new Promise((resolve, reject) =>{
      this.basecatService.getTimeSheetConfig(timesheetId).subscribe(res => {
        const startTime = new Date(Date.parse(res?.config[0]?.period[0]?.startHours));
        const startTimeDate: Date = new Date(curent);
        startTimeDate.setHours(startTime.getUTCHours(), startTime.getMinutes(), 0, 0);
        console.log('startDate: ' + startTimeDate)
        console.log('compare: ' + (curent.getTime() < startTimeDate.getTime()))
        resolve(curent.getTime() < startTimeDate.getTime());
      }, () => reject(false))
    })
  }

  subtractOneDay(inputDate) {
    let resultDate = new Date(inputDate);
    resultDate.setDate(resultDate.getDate() - 1);

    return resultDate;
  }

  checkOverOffTime (curent, offTime) {
    if (offTime) {
      const offTimeArray: string[] = offTime.split(":");
      const offTimeDate: Date = new Date(curent);
      offTimeDate.setHours(Number(offTimeArray[0]), Number(offTimeArray[1]), 0, 0);

      return curent.getTime() > offTimeDate.getTime();
    } else {
      return false;
    }
  }

  sendLLTPLgspHcmReceiving(id, resultJson) {
    console.log("-------startSendLLTPLgspHcm----- ");
    let resultJsonArr:any = JSON.parse(resultJson);
    let resultD:any = resultJsonArr.variables.dossier;
    let datePromissoryDec1: any = '';
    let dateReceivedDec1: any = '';
    let idMoneyReceipt1: any = '0';
    const currentDate = tUtils.newDate();

    console.log("-------resultJsonArr----- " + resultJsonArr);
    if (this.dossier.applicant.data && resultJsonArr.variables.dossier) {
      const data = resultD.applicant.data;
      const dataE:any = resultJsonArr.variables.dossier.eForm.data;
      if(resultD != null){
        if (resultD.appointmentDate) {
          datePromissoryDec1 = this.datePipe.transform(resultD.appointmentDate, 'dd/MM/yyyy');
        } else {
          datePromissoryDec1 = this.datePipe.transform(currentDate, 'dd/MM/yyyy');
        }
      }
    }
    if (resultD.acceptedDate) {
      dateReceivedDec1 = this.datePipe.transform(resultD.acceptedDate, 'dd/MM/yyyy');
    } else {
      dateReceivedDec1 = this.datePipe.transform(currentDate, 'dd/MM/yyyy');
    }
    const search = "";
    // let dataPost = this.eForm.data.data;
    // dataPost.datePromissoryDec = datePromissoryDec1;
    // dataPost.dateReceivedDec = dateReceivedDec1;
    // dataPost.idMoneyReceipt = idMoneyReceipt1 != '0' ? idMoneyReceipt1 : '0';
    // dataPost.idReceivedDec = id;
    // dataPost.declarationForm.declareDate = this.datePipe.transform(currentDate, 'dd/MM/yyyy');

    let dataPost = {
      id: this.dossierId,
      code: this.dossier.code
    };
    console.log("-------dataPostLLTPLgspHCM----- " + dataPost);
    return new Promise(resolve => {
      this.adapterService.sendLLTPLgspHcm(search, dataPost).subscribe(data => {
        console.log('responseLgspHCM', data);
        // if (data.status == 501) {
        //   // [1] Danh dau lay ho so nop truc tiep thanh cong
        //   // [2] Danh dau lay trang thai ho so thanh cong
        //   // [3] Danh dau lay ho so nop truc tuyen thanh cong
        //   var danhDauHoSo = {
        //     declarationId: data.id,
        //     infoType: 1,
        //   };

        //   this.adapterService.danhDauNhanHSThanhCongLgspHcm(search, danhDauHoSo).subscribe();

        // } else {
        //   console.log('sync failed', data);
        // }
        resolve(true);
      },err => {
        console.log(err);
        resolve(false);
      });
    });
  }

  sendHTTPLgspHcm(id, resultJson) {
    console.log("-------startSendHTTPLgspHcm----- ");
    let resultJsonArr:any = JSON.parse(resultJson);
    let resultD:any = resultJsonArr.variables.dossier;
    let datePromissoryDec1: any = '';
    let dateReceivedDec1: any = '';
    let idMoneyReceipt1: any = '0';
    const currentDate = tUtils.newDate();

    console.log("-------resultJsonArr----- " + resultJsonArr);
    if (this.dossier.applicant.data && resultJsonArr.variables.dossier) {
      const data = resultD.applicant.data;
      const dataE:any = resultJsonArr.variables.dossier.eForm.data;
      if(resultD != null){
        if (resultD.appointmentDate) {
          datePromissoryDec1 = this.datePipe.transform(resultD.appointmentDate, 'dd/MM/yyyy');
        } else {
          datePromissoryDec1 = this.datePipe.transform(currentDate, 'dd/MM/yyyy');
        }
      }
    }
    if (resultD.acceptedDate) {
      dateReceivedDec1 = this.datePipe.transform(resultD.acceptedDate, 'dd/MM/yyyy');
    } else {
      dateReceivedDec1 = this.datePipe.transform(currentDate, 'dd/MM/yyyy');
    }
    const search = "";
    // let dataPost = this.eForm.data.data;
    // dataPost.datePromissoryDec = datePromissoryDec1;
    // dataPost.dateReceivedDec = dateReceivedDec1;
    // dataPost.idMoneyReceipt = idMoneyReceipt1 != '0' ? idMoneyReceipt1 : '0';
    // dataPost.idReceivedDec = id;
    // dataPost.declarationForm.declareDate = this.datePipe.transform(currentDate, 'dd/MM/yyyy');

    let dataPost = {
      id: this.dossierId,
      code: this.dossier.code
    };
    console.log("-------dataPostHTTPLgspHCM----- " + dataPost);
    return new Promise(resolve => {
      this.adapterService.sendHTTPLgspHcm(search, dataPost).subscribe(data => {
        console.log('responseLgspHCM', data);
        // if (data.status == 501) {
        //   // [1] Danh dau lay ho so nop truc tiep thanh cong
        //   // [2] Danh dau lay trang thai ho so thanh cong
        //   // [3] Danh dau lay ho so nop truc tuyen thanh cong
        //   var danhDauHoSo = {
        //     declarationId: data.id,
        //     infoType: 1,
        //   };

        //   this.adapterService.danhDauNhanHSThanhCongLgspHcm(search, danhDauHoSo).subscribe();

        // } else {
        //   console.log('sync failed', data);
        // }
        resolve(true);
      },err => {
        console.log(err);
        resolve(false);
      });
    });
  }

  putReporterCounting(resultJson) {
    const newDate = tUtils.newDate();
    return new Promise<void>((resolve) => {
      if (
        this.dossier.agency !== undefined &&
        this.dossier.agency !== null
      ) {
        const body = {
          type: 1,
          number: 1,
        };
        const searchString =
          '?year=' +
          newDate.getFullYear() +
          '&month=' +
          (newDate.getMonth() + 1);
        this.dossierService
          .putReporterCounting(
            this.dossier.agency.id,
            searchString,
            JSON.stringify(body)
          )
          .subscribe(
            (data) => {
              resolve();
            },
            (err) => {
              resolve();
            }
          );
      } else {
        resolve();
      }
    });
  }

  async receivedAndNextStep() {
    return new Promise(resolve => {
      this.dossierService.getDossierDetail(this.dossierId).subscribe(async data => {
        this.dossierTaskData = data.task[0];
        let activitiTaskId;
        if (data.currentTask[0].activitiTask !== undefined && data.currentTask[0].activitiTask.id !== undefined) {
          activitiTaskId = data.currentTask[0].activitiTask.id;
        }
        const body = {
          assignee: this.accepterInfo.username,
          id: this.accepterInfo.id,
          payloadType: 'AssignTaskPayload',
          taskId: activitiTaskId
        };
        const requestBody = JSON.stringify(body, null, 2);
        await this.dossierService.postAssignTask(activitiTaskId, requestBody).toPromise();
        await this.getCheckGateway(data.task[0].activitiTask.id);
        await this.getNextFlowElement();
        let assignee: any = {};
        let agency: any = {};
        let candidateUser = [];
        let fromAgency = {};
        let parent = {};
        let candidateGroup = [];
        let definitionKey = '';
        // let agencyType = {};
        // Sender infomation
        const sender = {
          id: this.accepterInfo.id,
          fullname: this.accepterInfo.fullname,
          account: {
            id: this.accepterInfo.accountId,
            username: [
              {
                value: this.accepterInfo.username
              }
            ]
          }
        };
        // Parent task
        if (data.task.length > 0) {
          this.resultContent.id = this.dossierTaskData.activitiTask.id;
          parent = {
            id: data.task[0].id
          };
        }
        // From Agency
        if (data.agency !== undefined && data.agency !== null) {
          fromAgency = data.agency;
        }
        // this.isNoGateWay = true;
        setTimeout(async () => {
          if (this.isNoGateWay === true) {
            // if (this.processDefinitionTask.assignee !== null) {
            //   assignee = await this.getAccountByUserId(this.processDefinitionTask.assignee[0]);
            // } else {
              candidateUser = this.processDefinitionTask.candidateUser;
            // }
            agency = this.processDefinitionTask.candidateGroup[0];
            candidateGroup = this.processDefinitionTask.candidateGroup;
            definitionKey = this.nextFlowTaskKey;
            // agencyType = !!this.assigneeInfo[0].agencyType ? this.assigneeInfo[0].agencyType : {};
            // Agency & Assignee
            this.resultContent.variables.dossierTask.push({
              // assignee,
              sender,
              candidateUser,
              candidateGroup,
              parent,
              fromAgency,
              agency,
              bpmProcessDefinitionTask: {
                definitionTask: {
                  definitionKey
                }
              }
            });
            const requestBody = JSON.stringify(this.resultContent, null, 2);
            this.postCompleteTask(requestBody, data);
          } else {
            // this.router.navigate([], {
            //   queryParams: {
            //     processDefinition: this.processDefinitionId,
            //     eForm: this.dossier.eForm.id,
            //     applicantEForm: this.applicantEForm.id,
            //     task: data.currentTask[0].id,
            //   }
            // });
            // setTimeout(() => {
            //   this.confirmationCompleted(data);
            // }, 300);
          }
        }, 1000);
        resolve(true);
      });
    });
  }

  async getCheckGateway(dossierTaskId) {
    this.dossierService.getCheckGateway(dossierTaskId).subscribe(data => {
      if (data !== null) {
        if (data.type === 0) {
          this.isNoGateWay = true;
        } else {
          if (data.sequenceFlow.length > 0) {
            const processDef = this.dossierTaskData.processDefinition.id;
            data.sequenceFlow.forEach((flow, index) => {
              flow.candidateGroup = [];
              flow.candidateUser = [];
              this.processService.getTaskByProcessDefAndTaskKey(processDef, flow.targetRef).subscribe(res => {
                if (res.assignee.agency !== null && res.assignee.agency !== undefined) {
                  flow.treeAgency = res.assignee.agency;
                  flow.assigneeAgency = this.getAssigneeAgencyHGW(res.assignee.agency);
                } else if (res.candidateGroup.length > 0) {
                  flow.treeAgency = res.candidateGroup[0];
                  flow.candidateGroupAgency = res.candidateGroup[0];
                }
                if (res.assignee !== null) {
                  flow.assignee = res.assignee;
                }
                if (res.candidateGroup !== undefined && res.candidateGroup !== null) {
                  flow.candidateGroup = res.candidateGroup;
                }
                if (res.candidateUser !== undefined && res.candidateUser !== null) {
                  flow.candidateUser = res.candidateUser;
                }
              });

              if (flow.targetRef.includes('EndEvent')) {
                flow.isEnd = true;
              } else {
                flow.isEnd = false;
              }

              if (flow.targetRef.includes('Gateway')) {
                flow.isNextGateWay = true;
              } else {
                flow.isNextGateWay = false;
              }

              flow.selectedAgency = '';
              flow.isFullListUser = false;
              flow.currentUserPage = 0;
              flow.listUser = [];
              flow.selectedUser = '';
            });
            this.taskGateway = data;

            // console.log('this.taskGateway', this.taskGateway);
            if (this.taskGateway.sequenceFlow.length > 0) {
              this.taskGateway.sequenceFlow.forEach((flow, index) => {
                if (this.taskGateway.type === 1 && index === 0) {
                  this.selectedFlowForConfirm.push(flow.targetRef);
                }
                if (this.taskGateway.type === 3) {
                  this.selectedFlowForConfirm.push(flow.targetRef);
                }
                if (flow.condition !== undefined && flow.condition !== null && flow.condition !== '') {
                  const strCondition = flow.condition.substring(flow.condition.indexOf('{') + 1, flow.condition.indexOf('}'));
                  const conditionKey = strCondition.substring(0, strCondition.lastIndexOf('=') - 1);
                  this.resultContent.variables[conditionKey] = '';
                }
              });
            }
          } else {
            this.isNoGateWay = true;
          }
        }
      } else {
        this.isNoGateWay = true;
      }
    }, err => {
      console.log('getCheckGateway err', err);
    });
  }

  getAssigneeAgencyHGW(agency) {
    const result = {
      id: '',
      name: ''
    };

    if (agency.ancestors.length > 0) {
      agency.ancestors.forEach(element => {
        result.id = element.id;
        this.dossierService.getAgencyInfo(element.id).subscribe(data => {
          data.name.forEach(aName => {
            if (aName.languageId === this.selectedLangId) {
              result.name = aName.name;
            }
          });
        });
      });
    }
    if (agency.parent !== null && agency.parent !== undefined) {
      result.id = agency.parent.id;
      this.dossierService.getAgencyInfo(agency.parent.id).subscribe(data => {
        data.name.forEach(aName => {
          if (aName.languageId === this.selectedLangId) {
            result.name = aName.name;
          }
        });
      });
    }
    if (agency.id !== undefined && agency.id !== null) {
      result.id = agency.id;
      this.dossierService.getAgencyInfo(agency.id).subscribe(data => {
        data.name.forEach(aName => {
          if (aName.languageId === this.selectedLangId) {
            result.name = aName.name;
          }
        });
      });
    }

    return result;
  }

  async getNextFlowElement() {
    const requestString = '?level=2&process-definition-id=' + this.dossierTaskData.processDefinition.id;
    this.dossierService.getNextFlowElement(this.dossierTaskData.activitiTask.id, requestString).subscribe(data => {
      if (data.length > 0) {
        try {
          this.nextFlowElementName = data.filter(flow => flow.targetNode.outSequence[0] === null)[0].targetNode.detail.name;
          if (this.taskGateway.sequenceFlow.length > 0 && this.isNoGateWay === false) {
            this.nextFlowElementName = '';
            this.taskGateway.sequenceFlow.forEach(flow => {
              if (flow.targetName !== undefined) {
                this.nextFlowElementName += flow.targetName + ' ';
              }
            });
          }
          this.nextFlowTaskKey = data.filter(flow => flow.targetNode.outSequence[0] === null)[0].targetNode.detail.id;
          // tslint:disable-next-line: max-line-length
          this.processService.getTaskByProcessDefAndTaskKey(this.dossierTaskData.processDefinition.id, this.nextFlowTaskKey).subscribe(res => {
            this.processDefinitionTask = res;
            if (res.assignee !== null) {
              res.assignee = [res.assignee.id];
            }
            this.propertiesTask.patchValue(res);
          });
        } catch (error) {
          console.log(error);
        }
      }
      this.setEmailContent();
    });
  }

  setEmailContent() {
    const emailConfig = this.config.dossierEmailSMSZaloConfig;
    let content = emailConfig[this.selectedLang];
    content = content.replace('{{code}}', this.dossier.code);
    content = content.replace('{{step}}', this.nextFlowElementName);
    this.smsEmailContent = content;
  }

  postCompleteTask(resultJson, dossierDetail) {
    this.dossierService.postCompleteTask(this.dossierTaskData.activitiTask.id, resultJson).subscribe(data => {
      // this.adapterService.updateProcessDossierConnectTo(this.dossierId).subscribe();
      setTimeout(async () => {
        const msgObj = {
          vi: 'Hồ sơ: ' + this.dossier.code + ' chuyển bước ' + this.processDefinitionTask.activiti.name,
          en: 'Dossier: ' + this.dossier.code + ' has moved to step ' + this.processDefinitionTask.activiti.name
        };
        this.postComment(msgObj[this.selectedLang]);
        this.snackbarService.openSnackBar(1, msgObj[this.selectedLang], '', 'success_notification', this.config.expiredTime);
        // this.adminLayoutNavComponent.getDossierRemind();
        //  mở trang mới nếu cần
        // setTimeout(() => {
        //   this.router.navigate(['dossier/search/' + this.dossierId], {
        //     queryParams: {
        //       procedure: this.procedureId
        //     }
        //   });
        // });
        this.isLoading = false;
        // this.getNew();
      }, 1000);
    }, err => {
    });
  }

async formToJson() {
  const bpmProcess = await this.getProcessDefinitionTask(
    this.procedureProcessDetail.processDefinition.id
  );
  if (bpmProcess === true) {
    this.isLoading = true;
    let receivingKind = {};
    if (this.dossierReceivingKind !== undefined) {
      receivingKind = {
        id: this.listDossierReceivingKind.filter(
          (kind) => kind.id === this.dossierReceivingKind
        )[0].id,
        name: [
          {
            languageId: this.selectedLangId,
            name: this.listDossierReceivingKind.filter(
              (kind) => kind.id === this.dossierReceivingKind
            )[0].name,
          },
        ],
      };
    }

    if (
      this.dossierCode === undefined ||
      this.dossierCode === null ||
      this.dossierCode === ''
    ) {
      const msgObj = {
        vi: 'Lỗi mã số hồ sơ!',
        en: `Dossier code error!`,
      };
      this.snackbarService.openSnackBar(
        0,
        msgObj[this.selectedLang],
        '',
        'error_notification',
        this.config.expiredTime
      );
      this.isLoading = false;
    } else {
      let newDate = tUtils.newDate();
      const isBeforeStartTime = await this.checkBeforeStartTime(this.dossier.procedureProcessDefinition.processDefinition.timesheet.id, newDate);
      // if(this.autoUpdateAcceptedDateCmu){
      //   const updatedDate = await this.timeSheetService.updateDateBasedOnConfig(moment(newDate));
      //   newDate = updatedDate.toDate();
      // }
      const nextDate = await this.dossierService.getNextWorkingDate({
        timesheet: {
          id: this.dossier.procedureProcessDefinition?.processDefinition?.timesheet !== undefined
          ? this.dossier.procedureProcessDefinition.processDefinition.timesheet.id
          : this.config.defaultTimesheetId
        },
        startDate: isBeforeStartTime ? this.subtractOneDay(newDate) : newDate
      });
      let acceptedDate = ((this.checkOverOffTime(newDate, this.deploymentService.env?.limitedAppointmentTime) || isBeforeStartTime) && this.deploymentService.env.OS_HCM?.startAtNextDayAfterOffTime)
          ? new Date(Date.parse(nextDate?.date))
          : newDate;

      const selectedKind = this.listDossierReceivingKind.filter(
        (kind) => kind.id === this.dossierReceivingKind
      )[0];
      // const rcObj = this.tabForm.getRawValue();

      let dossierTemp: any;
      if (this.enableMergeDeepDossier) {
        dossierTemp = await this.dossierService.getDossierDetail(this.dossierId).toPromise();
      }

      const contentObj: any = {
        id: this.dossierId,
        code: this.dossierCode,
        nationCode: '',
        codePattern: {},
        procedure: this.dossier.procedure,
        applyMethod: this.dossier.applyMethod,
        dossierReceivingKind: {},
        receivingPlace: {},
        applicant: {},
        agency: {},
        accepter: {
          id: this.accepterInfo.id,
          fullname: this.accepterInfo.fullname
        },
        assignee: {
          id: this.accepterInfo.id,
          fullname: this.accepterInfo.fullname,
          account: {
            id: this.accepterInfo.accountId,
            username: [
              {
                value: this.accepterInfo.username,
              },
            ],
          },
        },
        procedureProcessDefinition: {
          id: '',
          processDefinition: {
            id: '',
            processingTime: '',
            processingTimeUnit: '',
            eForm: {},
            applicantEForm: {},
            timesheet: {},
          },
        },
        eForm: {},
        deploymentId: this.config.deploymentId,
        dossierStatus: {
          id: 2,
          name: [
            {
              languageId: 228,
              name: 'Đang xử lý',
            },
            {
              languageId: 46,
              name: 'Inprogress',
            },
          ],
          comment: '',
        },
        createdDate: this.dossier.createdDate,
        updatedDate: newDate,
        acceptedDate: acceptedDate,
        dossierFormFile: [],
        extendHCM:this.dossier.extendHCM
      };


      console.log('contentObj.receivingPlace');
      console.log(contentObj.receivingPlace);

      if (this.isConnectionTypeEnable){
        const connectedStatus = {
          connectionType: !!this.procedure?.connectionType ? this.procedure?.connectionType.code : 0,
          isWaitting: true
        };

        Object.assign(contentObj, { connectedStatus });
      }

      // Agency
      if (this.agencyInfo.length !== 0) {
        contentObj.agency = this.agencyInfo[0];
      } else {
        delete contentObj.agency;
      }

      console.log("aaaa this.applicantEForm.id", this.applicantEForm)
      // Applicant Info
      const applicantEFormData = this.dossier.applicant.data;


      // tslint:disable-next-line: max-line-length
      if (
        applicantEFormData.identityNumber !== undefined &&
        applicantEFormData.identityNumber !== null &&
        applicantEFormData.identityNumber !== ''
      ) {
        applicantEFormData.identityNumber = String(
          applicantEFormData.identityNumber
        );
      }

      if (
        this.dossier.applicant.userId !== undefined &&
        this.dossier.applicant.userId !== null
      ) {
        Object.assign(contentObj.applicant, {
          userId: this.dossier.applicant.userId,
        });
      }

      if (
        this.applicantEForm.id !== undefined &&
        this.applicantEForm.id !== null &&
        this.applicantEForm.id !== ''
      ) {
        Object.assign(contentObj.applicant, {
          eformId: this.applicantEForm.id,
        });
        Object.assign(contentObj.applicant, { data: applicantEFormData });
      } else {
        delete contentObj.applicant;
      }

      if (selectedKind !== undefined) {
        contentObj.dossierReceivingKind = {
          id: selectedKind.id,
          name: [
            {
              languageId: this.selectedLangId,
              name: selectedKind.name,
            },
          ],
        };


      // if (this.enableSaveFullAddress == 1){

      //   contentObj.receivingPlace = {
      //     id: rcObj.rcVillage,
      //     fullAddress: rcObj.customAddress,
      //     nationName: this.listNation.filter(
      //       (nation) => nation.id === this.defaultNation
      //     )[0].name,
      //     rcSend: rcObj.rcSend,
      //     rcReceive: rcObj.rcReceive,
      //     name: rcObj.rcName,
      //     phoneNumber: rcObj.rcPhoneNumber,
      //     email: rcObj.rcEmail,
      //     fullAddressR: rcObj.customAddressR,
      //     resultReceivingPlaceId: ((rcObj.rcVillageR != '' &&  rcObj.rcVillageR != null) ? rcObj.rcVillageR : null),
      //     rcDistrict: rcObj.rcDistrict,
      //     rcDistrictR: rcObj.rcDistrictR,
      //     rcProvince: rcObj.rcProvince,
      //     rcProvinceR: rcObj.rcProvinceR,
      //     rcVillage: rcObj.rcVillage,
      //     rcVillageR: rcObj.rcVillageR,
      //   };

      // } else {
      //   if (selectedKind.id === this.env?.vnpost?.receiveResultsByAddress) {
      //     contentObj.receivingPlace = {
      //       id: rcObj.rcVillage,
      //       fullAddress: rcObj.customAddress,
      //       nationName: this.listNation.filter(
      //         (nation) => nation.id === this.defaultNation
      //       )[0].name,
      //       rcSend: rcObj.rcSend,
      //       rcReceive: rcObj.rcReceive,
      //       name: rcObj.rcName,
      //       phoneNumber: rcObj.rcPhoneNumber,
      //       email: rcObj.rcEmail,
      //       fullAddressR: rcObj.customAddressR,
      //       resultReceivingPlaceId: ((rcObj.rcVillageR != '' &&  rcObj.rcVillageR != null) ? rcObj.rcVillageR : null)
      //     };
      //   }
      // }

      } else {
        delete contentObj.dossierReceivingKind;
        delete contentObj.receivingPlace;
      }


      console.log('contentObj');
      console.log(contentObj);

      // Applicant Info
      const eFormData = this.dossier.eForm.data;
      if (
        this.dossier.eForm.id !== undefined &&
        this.dossier.eForm.id !== null &&
        this.dossier.eForm.id !== ''
      ) {
        Object.assign(contentObj.eForm, { id: this.dossier.eForm.id });
        Object.assign(contentObj.eForm, { data: eFormData });
      }

      if (
        this.dossier.procedureProcessDefinition !== undefined &&
        this.dossier.procedureProcessDefinition !== null
      ) {
        contentObj.procedureProcessDefinition =
          this.dossier.procedureProcessDefinition;
        Object.assign(
          contentObj.procedureProcessDefinition.processDefinition,
          {
            eForm: {
              id: this.dossier.eForm.id
              // design: this.eForm.component,
            },
          }
        );
        Object.assign(
          contentObj.procedureProcessDefinition.processDefinition,
          {
            applicantEForm: {
              id: this.applicantEForm.id
              // design: this.applicantEForm.component,
            },
          }
        );
      } else {
        delete contentObj.procedureProcessDefinition;
      }

      if (
        this.dossier.appliedDate !== undefined &&
        this.dossier.appliedDate !== null
      ) {
        Object.assign(contentObj, {
          appliedDate: this.dossier.appliedDate,
        });
      }
      Object.assign(contentObj, { acceptedDate: acceptedDate });


      let appointmentDate = await this.postTimesheetReceiving(1, acceptedDate);
      // if (this.enableAppointmentDateEdit) {
      //   appointmentDate = rcObj.appointmentDate;
      // }else {
        if (!!appointmentDate) {
          appointmentDate = new Date(appointmentDate.toString());
        }
        // else {
        //   appointmentDate = rcObj.appointmentDate;
        // }
      // }

      if (appointmentDate !== undefined && appointmentDate !== null) {
        Object.assign(contentObj, { appointmentDate });
      }
      // if(this.originalAppointmentDate.getTime() != appointmentDate.getTime()){
      //   Object.assign(contentObj, { useEditedAppointmentDate: true });
      // }

      // dossierFormFile
      // contentObj.dossierFormFile = this.dossierFormFileData;

      const taskVariable = {
        assignee: {
          id: this.accepterInfo.id,
          fullname: this.accepterInfo.fullname,
          account: {
            id: this.accepterInfo.accountId,
            username: [
              {
                value: this.accepterInfo.username,
              },
            ],
          },
        },
        candidateGroup: [this.agencyInfo[0]],
        agency: this.agencyInfo[0],
      };
      Object.assign(contentObj, { taskVariable });

      // // Thông tin người dân đã được xác thực từ CSDLQG Dân cư
      // if (tUtils.nonNull(this.validateResidentialInfo, 'fullname') && tUtils.nonNull(this.applicantEForm.data.data, 'fullname')) {
      //   if (!(this.validateResidentialInfo?.fullname == this.applicantEForm.data?.data?.fullname
      //     && this.validateResidentialInfo?.identityNumber == this.applicantEForm.data?.data?.identityNumber
      //     && this.validateResidentialInfo?.birthday == this.applicantEForm.data?.data?.birthday)) {
      //     this.isValidResidentialInfo = false;
      //     if (tUtils.nonNull(this.dossier, 'validateResidentialInfo')) {
      //       this.validateResidentialInfo = this.dossier.validateResidentialInfo;
      //       this.validateResidentialInfo.confirm = false;
      //       Object.assign(contentObj, { validateResidentialInfo: this.validateResidentialInfo });
      //     }
      //   }
      // }

      // if (tUtils.nonNull(this.validateResidentialInfo, 'fullname') && this.isValidResidentialInfo) {
      //   Object.assign(contentObj, { validateResidentialInfo: this.validateResidentialInfo });
      // }

      // Object.assign(contentObj, {
      //   extendQBH: {
      //     notify: this.listCheckReceiveNoti
      //   }
      // });

      // if (this.checkConfigHoTichDLK === 1) {
      //   await this.isCheckProcedureProcessHoTichDLK(this.procedureProcessDetail.processDefinition.id);
      //   if (this.isEnableHTTP_DLK == true) {
      //     const http = {
      //       statusMessage: '',
      //       status: 0,
      //       statusCode: 0,
      //       decStatusId: 0
      //     };
      //     Object.assign(contentObj, {
      //       extendDLK: {
      //         http: http
      //       }
      //     });
      //   }
      // }

      let mergedDossier = JSON.parse(JSON.stringify(contentObj));

      if (this.enableMergeDeepDossier) {
        mergedDossier = tUtils.mergeDeep(dossierTemp, JSON.parse(JSON.stringify(contentObj)));
        delete mergedDossier.dossierTaskStatus;
        delete mergedDossier.dossierMenuTaskRemind;
      }

      const resultObj = {
        processDefinitionId:
          this.procedureProcessDetail.processDefinition.activiti.id,
        payloadType: 'StartProcessPayload',
        variables: {
          dossier: mergedDossier,
        },
        commandType: 'StartProcessInstanceCmd',
      };

      console.log('resultObj');
      console.log(resultObj);

      const resultJson = JSON.stringify(resultObj, null, 2);
      this.tempDossier = resultJson;
      // await this.updateComposition();

      this.startProcess(resultJson);

    }
  } else {
    const msgObj = {
      vi: 'Không tìm thấy công việc cho quy trình này!',
      en: 'No task found for this process!',
    };
    this.snackbarService.openSnackBar(
      0,
      msgObj[this.selectedLang],
      '',
      'error_notification',
      this.config.expiredTime
    );
  }
}

getProcessDefinitionTask(id) {
  const searchString =
    '?page=0&size=50&spec=page&process-definition-id=' + id;
  return new Promise((resolve) => {
    this.procedureService.getBPMProcessDefinitionTask(searchString).subscribe(
      (data) => {
        resolve(true);
      },
      (err) => {
        resolve(false);
      }
    );
  });
}

getProcedureProcessDetail(prDefId) {
  return new Promise<void>((resolve) => {
    this.procedureService.getProcedureProcessDetail(prDefId).subscribe((data) => {
      this.procedureProcessDetail = data;
      this.idProcedureInProcess= data.procedure.id;
      this.listProcedureProcess.push(this.procedureProcessDetail);
      this.selectedProcess = this.listProcedureProcess[0];
      this.result.push({
        case: data.id,
        caseName: data.processDefinition.name,
        form: [],
      });
      resolve();
    }, (err) => {
      const msgObj = {
        vi: 'Không tìm thấy quy trình cho hồ sơ này!',
        en: 'No process found for this dossier!',
      };
      this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
      resolve();
    });
  });
}

async getAgencyInfoReceiving(agencyId) {
  return new Promise((resolve) => {
    this.dossierService.getAgencyInfo(agencyId).subscribe(async data => {
        const agencyTag = [];
        data.tag.forEach((tagId) => {
          this.dossierService.getAgencyTag(tagId).subscribe((rs) => {
            agencyTag.push({
              id: rs.id,
              name: rs.trans,
            });
          });
        });
        const agencyInfoData = {
          id: agencyId,
          code: data.code,
          name: data.name,
          tag: agencyTag,
          parent: null,
          ancestors: null,
        };

        // Parent
        if (data.parent !== null || data.parent !== '') {
          const parentTag = [];
          data.tag.forEach((tagId) => {
            this.dossierService.getAgencyTag(tagId).subscribe((tg) => {
              parentTag.push({
                id: tg.id,
                name: tg.trans,
              });
            });
          });

          this.dossierService.getAgencyInfo(data.parent).subscribe((res) => {
            agencyInfoData.parent = {
              id: data.parent,
              name: res.name,
              tag: parentTag,
            };
          });
        }
        // Ancestors
        if (!!data.ancestors && data.ancestors.length !== 0) {
          agencyInfoData.ancestors = [];
          data.ancestors.forEach(anc => {
            agencyInfoData.ancestors.push({
              id: anc.id,
              name: [{
                languageId: this.selectedLangId,
                name: anc.name,
              }]
            });
          });
        }

        if (this.agencyInfo.length !== 0) {
        this.agencyInfo[0] = agencyInfoData;
      } else {
        this.agencyInfo.push(agencyInfoData);
      }

      delete agencyInfoData.code;   // xóa agency.code để lấy code từ parent agency

      if (!agencyInfoData.code) {
        this.agencyInfo[0].code = await this.getAgencyCodeRecursive(agencyInfoData, data.parent);
      }
      // lay id pattern
      if (!!data.parent) {
        this.dossierService.getDossierCodePattern(data.parent, this.procedureId).subscribe(response => {
          this.dossierCodePattern = response.id;
        });
      } else {
        this.dossierService.getDossierCodePattern(agencyId, this.procedureId).subscribe(response => {
          this.dossierCodePattern = response.id;
        });
      }

      this.isLoadedAgency = true;
      resolve(agencyInfoData);
    },
      (err) => {
        resolve(null);
      }
    );
  });
  }

  getAgencyCodeRecursive(agencyInfo, parentId) {
    let info = agencyInfo;
    let id = parentId;
    let check = true;

    return new Promise<any>(async resolve => {
      while (check) {
        if (!!info.code) {
          resolve(info.code);
          check = false;
        } else {
          await this.dossierService.getAgencyInfo(id).toPromise().then(data => {
            info = data;
            id = data.parent;
            check = true;
          }).catch(err => {
            resolve(info.code);
            check = false;
          });
        }
      }
    });
  }

  // ========================================================== PUT
  startProcess(resultJson) {

    this.dossierService.startReceivingDossier(resultJson).subscribe(
      async (data) => {
        const createProcessBody: any = {
          processInstanceId: data.entry.id
        };

        let userAgency = JSON.parse(localStorage.getItem("userAgency"));
        let typeNotify = 0;
        if (this.sendNotifyNearExpiry.listAgency.length > 0
          && (this.sendNotifyNearExpiry.listAgency.includes(userAgency?.id)
            || this.sendNotifyNearExpiry.listAgency.includes(userAgency?.parent?.id))) {
          if (this.sendNotifyNearExpiry.hasSms && this.sendNotifyNearExpiry.hasEmail
            && this.sendNotifyNearExpiry.templateIdSms != ""
            && this.sendNotifyNearExpiry.templateIdEmail != "") {
            typeNotify = 3;
          } else if (this.sendNotifyNearExpiry.hasSms && this.sendNotifyNearExpiry.templateIdSms != "") {
            typeNotify = 1;
          } else if (this.sendNotifyNearExpiry.hasEmail && this.sendNotifyNearExpiry.templateIdEmail != "") {
            typeNotify = 2;
          }
        }
        if (typeNotify != 0) {
          let sendNotifyData = {
            minutesBefore: this.sendNotifyNearExpiry.minutesBefore,
            templateIdSms: this.sendNotifyNearExpiry.templateIdSms,
            templateIdEmail: this.sendNotifyNearExpiry.templateIdEmail,
            subjectEmail: this.sendNotifyNearExpiry.subjectEmail,
            typeNotify: typeNotify,
            subsystemId: this.config.subsystemId,
          }
          createProcessBody.sendNotifyData = sendNotifyData;
        }

        let isSuccess = true;

        // if(this.ktmEnableSyncToBLDTBXH){
        //   const dossierData = await this.dossierService.getDossierDetail(this.dossierId).toPromise();
        //   const procedureData = await this.procedureService.getProcedureDetail(dossierData?.procedure?.id).toPromise();
        //   if(this.socialProtectionKTMService.checkIfDossierNeedSyncBTXH(procedureData)){
        //     this.getDossierStatusSyncKTMToBLDTBXH(data);
        //   }
        // }
        const dossierData = await this.dossierService.getDossierDetail(this.dossierId).toPromise();
        // if (this.enableSyncToBLDTBXH && this.isHasCodeLDXH) {
        //   this.getDossierStatusSyncToBLDTBXH(data);
        // }

        await this.dossierService.postDossierCreateProcess(createProcessBody).toPromise().then(data => {
          if (data.affectedRows === 0) {
            isSuccess = false;
          }
        }).catch(error => {
          isSuccess = false;
          console.log(error);
        });
        await this.getDossierDetail();

        // //Check quyền Hộ tịch DLK
        // const msgObjHTTPDLK = {
        //   vi: 'Liên thông hộ tích tư pháp thất bại!',
        //   en: 'Civil status connection failed!'
        // };
        // if (this.checkConfigHoTichDLK === 1) {
        //   await this.isCheckProcedureProcessHoTichDLK(this.procedureProcessDetail.processDefinition.id);
        //   if (this.isEnableHTTP_DLK == true) {
        //     //Gọi API nhận hồ sơ đăng ký LLTP LGSP
        //     await this.getDossierDetail();
        //     await this.getMaDonviLienThongHTTPDLK(this.dossierDetail.agency?.ancestors);
        //     this.sendHoTichDLK(this.dossierCode, resultJson);
        //   }
        //   // else {
        //   //   this.snackbarService.openSnackBar(0, msgObjHTTPDLK[this.selectedLang], '', 'error_notification', 800);
        //   // }
        // }

        // //Check quyền LLTP DLK
        // const msgObjLLTPDLK = {
        //   vi: 'Liên thông lý lịch tư pháp thất bại!',
        //   en: 'Criminal record connection failed!'
        // };

        // if (this.checkCriminalRecordsDLK === 1) {
        //   await this.isCheckProcedureProcessLLTPDLK(this.procedureProcessDetail.processDefinition.id);
        //   if (this.isEnableLLTP_DLK == true) {
        //     //Gọi API nhận hồ sơ đăng ký LLTP LGSP
        //     await this.getDossierDetail();
        //     this.sendLLTP_DLK(this.dossierId, resultJson);
        //   }
        // }
        // else{
        //   this.snackbarService.openSnackBar(0, msgObjLLTPDLK[this.selectedLang], '', 'error_notification', 800);
        // }

        // if (this.checkCivilStatusJusticeTriNam === 1) {
        //   await this.triNamService.sendHTTPTriNam(dossierData);
        // }
        // if (this.checkJudicialRecordsTriNam === 1) {
        //   this.triNamService.sendLLTPTriNam(dossierData);
        // }
        // if (this.checkCivilStatusJustice === 1) {
        //   await this.getDossierDetail();
        //   this.sendHTTPTanDan(this.dossierId, resultJson);
        // }
        // if (this.checkCriminalRecordsTanDan === 1) {
        //   await this.getDossierDetail();
        //   this.sendLLTPTanDan(this.dossierId, resultJson);
        // }
        // if (this.checkCriminalRecords === 1) {
        //   await this.getDossierDetail();
        //   if (this.env?.judicalRecords?.qniConfig?.getTokenLGSP && (this.env?.judicalRecords?.qniConfig?.getTokenLGSP == '1' || this.env?.judicalRecords?.qniConfig?.getTokenLGSP == 1)) {
        //     await this.getTokenLGSPLyLich();
        //   }
        //   await this.sendLLTP(this.dossierId, resultJson);
        // }
        // if (this.checkCivilStatusJustice === 1 && !this.env?.http?.procedureCode.includes(this.procedureDetail[0].code)) { // Bổ sung điều kiện tránh sử dung các thủ tục dành cho điện biên
        //   await this.getDossierDetail();
        //   console.log('this.dossierDetail');
        //   console.log(this.dossierDetail);
        //   if (this.env?.civilStatusJustice?.agency && this.env?.civilStatusJustice?.agency != null && this.env?.civilStatusJustice?.agency != '') { } else {
        //     await this.getAgencyCodeForHTTP();
        //   }
        //   if (this.env?.civilStatusJustice?.qniConfig?.getTokenLGSP && (this.env?.civilStatusJustice?.qniConfig?.getTokenLGSP == '1' || this.env?.civilStatusJustice?.qniConfig?.getTokenLGSP == 1)) {
        //     await this.getTokenLGSPHoTich();
        //   }
        //   await this.sendHTTP(this.dossierCode, resultJson);
        // }
        // if (this.checkCriminalRecordsMinhTue === 1) {
        //   await this.getDossierDetail();
        //   this.sendLLTPMinhTue(this.dossierId, resultJson);
        // }
        // if (this.checkCivilStatusJusticeMinhTue === 1) {
        //   await this.getDossierDetail();
        //   this.sendHTTPMinhTue(this.dossierId, resultJson);
        // }
        let sendLLTPLgspHcm = false;
        try {
          if (this.allowReceptionSendLLTPLgspHcm && this.allowReceptionSendLLTPLgspHcm.enable && this.allowOnlineReceptionSendLLTPLgspHcm) {
            await this.getDossierDetail();
            if (this.allowReceptionSendLLTPLgspHcm.procedureList && this.dossier?.procedure?.code) {
              let chk = this.allowReceptionSendLLTPLgspHcm.procedureList.find(o => o == this.dossier?.procedure?.code);
              //Kiểm tra đúng thủ tục
              if (chk && this.dossier?.eForm?.data[this.allowReceptionSendLLTPLgspHcm.apiName] && (
                this.dossier?.eForm?.data[this.allowReceptionSendLLTPLgspHcm.apiName] == '0' || this.dossier?.eForm?.data[this.allowReceptionSendLLTPLgspHcm.apiName] == 0)) {
                sendLLTPLgspHcm = true;
              }
            }
          }
        } catch (error) { console.log(error); }
        if (this.checkHcmLyLichTuPhapReceiving === 1) {
          // await this.getDossierDetail();
          this.sendLLTPLgspHcmReceiving(this.dossierId, resultJson);
        }
        if (this.checkHcmHoTichTuPhapReceiving === 1) {
          await this.getDossierDetail();
          this.sendHTTPLgspHcm(this.dossierId, resultJson);
        }

        if (isSuccess) {
          let dossierData = await this.dossierService.getDossierDetail(this.dossierId).toPromise();
          if (!!dossierData) {
            console.log("code fix incident");
            if (!dossierData?.task) {
              dossierData = await this.retryCreateProcess(createProcessBody);
            } else {
              this.taskId = dossierData?.task ? dossierData?.task[0]?.id : null;
            }

            this.notiService.confirmSendSubject.next({
              confirm: true,
              renewContent: false,
            });

            const dossier = JSON.parse(resultJson)?.variables?.dossier;

            // if (this.enableSyncConnectMinistry) {
            //   this.adapterService.sendDossierConnectTo(this.dossierId).subscribe();
            // }

            // // DBN - Dong bo ho so sang he thong nha o hinh thanh trong tuong lai
            // if (this.isConnectBxdDBN) {
            //   this.adapterService.postDossierConnectBxdDBN(this.dossierId).subscribe();
            // }

            // if (this.enableUsingCustomTracking) {

            // } else {
              let paymentMethod: number;

              const dossierFeeData = await this.dossierService.getDossierFee(this.dossierId).toPromise();

              if (!!dossierFeeData && dossierFeeData.length > 0) {
                let checkIfPaymentIsNull = true;
                for (let i = 0; i < dossierFeeData.length; i++) {
                  if (dossierFeeData[i].amount > 0) {
                    checkIfPaymentIsNull = false;
                    break;
                  }
                }
                if (!checkIfPaymentIsNull) {
                  let searchString = "?dossier-id=" + this.dossierId;
                  await this.dossierService.getPaymentDossierKTM(searchString).toPromise().then(res => {
                    if (res?.content?.length > 0) {
                      paymentMethod = 1;
                    } else {
                      paymentMethod = 2;
                    }
                  });

                } else {
                  paymentMethod = null;
                }
              } else {
                paymentMethod = null;
              }

              this.trackingService.pushTracking({
                dossierCode: dossier?.code,
                procedureCode: dossier?.procedure?.code,
                procedureName: !!dossier?.procedure?.translate?.find(agency => agency.languageId === Number(this.selectedLangId))?.name ? dossier?.procedure?.translate?.find(agency => agency.languageId === Number(this.selectedLangId))?.name : '',
                status: 1,
                level: this.procedure.level?.id == this.trackingBTTTT?.procedureLevel3 ? 3 : this.procedure.level?.id == this.trackingBTTTT?.procedureLevel4 ? 4 : 0,
                fromDVCQG: 0,
                isDVCBC: this.getIsDVCBC(dossier),
                receptionMethod: 1,
                paymentMethod: paymentMethod,
                userId: dossier?.applicant?.userId,
                data: '',
                nationCode: dossier?.nationCode
              });
            // }

            // if (this.ktmEnableSyncToBLDTBXH && this.isHasCodeLDXH) {
            //   this.syncDossierStatusKTMToBLDTBXH(dossierData);
            // }

            // if (this.enableSyncToBLDTBXH && this.isHasCodeLDXH) {
            //   this.syncDossierStatusToBLDTBXH(dossierData);
            // }
            // if (this.isHasCodeLDXH && this.enableLDXHTriNam) {
            //   Object.assign(dossierData, { contentTask: 'Hồ sơ được tiếp nhận' });
            //   this.triNamService.syncTaskLDXH(dossierData);
            // }

            console.log('Vào hàm tiếp nhận 33');
            // let fileDigitize = localStorage.getItem('fileDigitize');

            // if (fileDigitize?.length > 0) {
            //   let listSearchDigitize = [];
            //   listSearchDigitize.push(`doID=${this.dossierId}`);
            //   let searchStringDigitize = '?' + listSearchDigitize.join('&');
            //   this.dossierService.updateStatusDigitizeBDG(searchStringDigitize).subscribe(data => {
            //     console.log(data);
            //   });
            // }
            // if (this.isBoTNMTQNM || this.isBoTNMTHCM) {
            //   this.dossierService.updateDossierBoTNMT(this.dossierId).subscribe(test => { });
            // }
            // if (this.deploymentService.env?.OS_HBH?.isTnmtTriNam) {
            //   this.adapterService.sendTNMTTriNam(this.dossierId);
            // }
            const msgObj = {
              vi: 'Tiếp nhận thành công!',
              en: 'Successful reception!',
            };
            let updateProcessStepDue = this.deploymentService?.env.OS_HCM?.updateProcessStepDue ? this.deploymentService?.env.OS_HCM?.updateProcessStepDue : false;
            if (updateProcessStepDue) {
              if (tUtils.nonNull(this.dossier, 'acceptedDate') && (tUtils.nonNull(this.dossier, 'paymentRequestData') && tUtils.nonNull(this.dossier?.paymentRequestData, 'paidDate'))) {
                let acceptedDateOld = new Date(this.acceptedDateOld.toString());
                let paidDate = new Date(this.dossier?.paymentRequestData?.paidDate.toString());

                if (paidDate > acceptedDateOld) {

                  let currentTask = this.dossier.currentTask.filter(item => item.isCurrent == 1);
                  let requestDate = new Date(this.dossier?.paymentRequestData?.requestDate.toString());
                  let acceptedDate = tUtils.newDate();
                  let distance = Math.ceil((acceptedDate.getTime() - requestDate.getTime())) / (24 * 60 * 60 * 1000);
                  if (currentTask.length > 0 && distance > 0) {
                    let startDate = new Date(Date.parse(currentTask[0]?.dueDate))
                    const dueDate = this.datePipe.transform(await this.postTimesheetTaskAfterPayment(currentTask[0], distance, 1, startDate), 'dd/MM/yyyy HH:mm:ss');
                    console.log('dueDate', dueDate);
                    let requestBody = {
                      taskId: currentTask[0]?.id,
                      dueDate: this.datePipe.transform(dueDate, 'yyyy-MM-ddTHH:mm:ss.SSSZ')
                    }
                    this.dossierService.putDueDateCurrentTask(this.dossierId, JSON.stringify(requestBody, null, 2)).subscribe(data => {

                      if (data?.affectedDate == 1) {
                        const msgObj = {
                          vi: 'chuyển đổi thành công ngày hiện trả bước quy trình!',
                          en: 'Successful change dueDate of task in process!',
                        };
                        this.snackbarService.openSnackBar(
                          1,
                          msgObj[this.selectedLang],
                          '',
                          'success_notification',
                          this.config.expiredTime
                        );
                      }
                    })
                  }
                }
              }
            }
            this.snackbarService.openSnackBar(
              1,
              msgObj[this.selectedLang],
              '',
              'success_notification',
              this.config.expiredTime
            );
            await this.putReporterCounting(resultJson);

            if (!!this.dossier.vnpostStatus) {
              await this.updateVNPostRsult();
            }

            // if (this.batLienThongCTDT && this.procedureProcessDetail?.isCTDT) {
            //   this.initChungThuc(dossier);
            // }

            // this.adminLayoutNavComponent.getDossierRemind();
            // if (this.isSyncDBNLGSPTanDanBXD === 1) {
            //   this.syncPostReceiveInforFileFromLocal();
            // }
            //Bo sung gui zalo cho buoc tiep nhan
            let userAgencyId;
            const userAgency = JSON.parse(localStorage.getItem('userAgency'));
            if (userAgency !== null) {
              userAgencyId = userAgency.id;
            } else {
              userAgencyId = this.config.rootAgency.id;
            }
            const requestJson = {
              phone: dossierData?.applicant?.data?.phoneNumber?.replace('+84', '0'),
              title: 'VNPT IGATE 2.0 Thông báo',
              templateId: null,
              templates: {
                sector: !!dossierData?.procedure?.sector?.name?.filter(item => item.languageId === Number(this.selectedLangId))[0]?.name ? dossierData?.procedure?.sector?.name?.filter(item => item.languageId === Number(this.selectedLangId))[0]?.name : null,
                procedure: !!dossierData?.procedure?.translate?.filter(item => item.languageId === Number(this.selectedLangId))[0]?.name ? dossierData?.procedure?.translate?.filter(item => item.languageId === Number(this.selectedLangId))[0]?.name : null,
                agency: !!dossierData?.agency?.name?.filter(agency => agency.languageId === Number(this.selectedLangId))[0]?.name ? dossierData?.agency?.name?.filter(agency => agency.languageId === Number(this.selectedLangId))[0]?.name : null,
                fullname: dossierData?.applicant?.data?.fullname,
                dossierCode: dossierData?.code,
                statusTitle: !!dossierData?.currentTask[0]?.bpmProcessDefinitionTask?.name?.name ? dossierData?.currentTask[0]?.bpmProcessDefinitionTask?.name?.name : null,
                statusDetail: null,
                returnMethod: !!dossierData?.dossierReceivingKind?.name?.filter(recieve => recieve.languageId === Number(this.selectedLangId))[0]?.name ? dossierData?.dossierReceivingKind?.name?.filter(recieve => recieve.languageId === Number(this.selectedLangId))[0]?.name : null,
                receivingDate: !!dossierData?.currentTask[0]?.assignedDate ? this.datePipe.transform(new Date(dossierData?.currentTask[0]?.assignedDate), 'dd/MM/yyyy HH:mm:ss') : null,
                appointmentDate: !!dossierData?.appointmentDate ? this.datePipe.transform(new Date(dossierData?.appointmentDate), 'dd/MM/yyyy HH:mm:ss') : null,
                step: null,
                banner: null,
                cta_1_icon: null,
                cta_1_type: null,
                cta_1_text: null
              },
              detailLink: null
            };
            // IGATESUPP-61533
            if (dossierData.applyMethod.id === 1 && requestJson.templates.receivingDate === null) {
              requestJson.templates.receivingDate = this.datePipe.transform(new Date(), 'dd/MM/yyyy HH:mm:ss')
            }
            if (dossierData.applyMethod.id === 1 && requestJson.templates.statusTitle === null) {
              let current: any = !!dossierData.task.filter(selectedtask => selectedtask.isCurrent === 1) ? [0] : undefined
              requestJson.templates.statusTitle = (current !== undefined && current !== null) ? current.bpmProcessDefinitionTask?.name?.name : '[Rỗng]'
            }
            //end  IGATESUPP-61533
            try {
              if (this.deploymentService.env?.OS_DBN?.stepZaloTemplate?.enable) {
                let send = false;
                // config
                requestJson.templates.banner = this.deploymentService.env?.OS_DBN?.stepZaloTemplate?.banner;
                requestJson.templates.cta_1_icon = this.deploymentService.env?.OS_DBN?.stepZaloTemplate?.actionBtnIcon;
                requestJson.templateId = this.deploymentService.env?.OS_DBN?.stepZaloTemplate?.onlineReception;
                requestJson.templates.step = 'onlineReception';
                //set invalid data
                const requestBody = JSON.stringify(requestJson, null, 2);
                this.notiService.sendZaloByStep(userAgencyId, !!this.env?.subsystem?.id ? this.env?.subsystem?.id : '5f7c16069abb62f511880003', requestBody).subscribe(zaloRS => {
                  if (zaloRS.message === 'Lỗi User has not followed OA') {
                    const msgObj = {
                      vi:
                        'Người dùng chưa theo dõi zalo OA',
                      en:
                        'User has not followed OA'
                    };
                    this.snackbarService.openSnackBar(0, '', msgObj[localStorage.getItem('language')], 'error_notification', this.config.expiredTime);
                  }
                });
              }
            } catch (error) {
              console.log(error);
            }
            // //Bổ sung tự động đồng bộ hồ sơ sau khi tiếp nhận qua iLIS - QNM
            // if (this.autoSyncILisAfterRecievedSuccess) {
            //   console.log('autoSyncILisAfterRecievedSuccess')
            //   this.isIlis = dossierData?.task[0]?.bpmProcessDefinitionTask?.dynamicVariable?.iLis || dossierData?.extendQNI?.isIlis == true || dossierData?.extendQNI?.isIlis == false;
            //   if (this.isIlis) {
            //     await this.syncILIS(this.dossierId);
            //   }
            // }

            //Bổ sung tự động cập nhật hồ sơ qua iLis sau khi tiếp nhận lại Yêu cầu bổ sung từ hệ thống một cửa QNM
            // if (this.isCheckIlisProcess && this.integratedIlisReceivingDossier) {
            //   await this.syncAdditinalRequestIntoILIS(dossierData.code);
            // }

            if (this.deploymentService?.env?.OS_BTTTT?.isEnabledNotify) {
              // IGATESUPP-62366 thong bao cho nguoi dan
              this.dossierService.noticeDossier(this.dossierId, { comment: '' }).subscribe(res => { });
            }
            // this.router.navigate(['dossier/processing/' + this.dossierId], {
            //   queryParams: {
            //     procedure: this.procedureId,
            //     task: this.taskId
            //   }
            // });
            let autoReceiveAndMoveNextStep = true;
            if (autoReceiveAndMoveNextStep) {
              setTimeout(async () => {
                await this.receivedAndNextStep();
              }, 1000);
            }
          }
        } else {
          const msgObj = {
            vi: 'Tiếp nhận thất bại!',
            en: 'Failed reception!'
          };
          this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', 800);
        }
      },
      (err) => {
        const msgObj = {
          vi: 'Không thể bắt đầu quy trình!',
          en: 'The process could not be started!',
        };
        this.snackbarService.openSnackBar(
          0,
          msgObj[this.selectedLang],
          '',
          'error_notification',
          this.config.expiredTime
        );
        this.isLoading = false;
      }
    );
  }

  retryCreateProcess(createProcessBody) {
    return new Promise((resolve) => {
      setTimeout(async () =>{
        const processCreated = await this.dossierService.postDossierCreateProcess(createProcessBody).toPromise();
        const dossierData = await this.dossierService.getDossierDetail(this.dossierId).toPromise();
        this.taskId = dossierData?.task ? dossierData?.task[0]?.id : null;
        resolve(dossierData);
      }, this.config.expiredTime);
    });
  }

  async postTimesheetTaskAfterPayment(currentTask, processProcessingTime, type?:number, newDate?) {
    const newDateDefault = tUtils.newDate();
    // type = 1: appointmentDate
    const listTimesheet = [];
    let timeAfterAddition = 0;
    // if(this.deploymentService.env?.OS_HCM?.processingTimeAfterAddition?.enable
    //   && this.dossierDetail.dossierTaskStatus.id == this.deploymentService.env?.OS_HCM?.processingTimeAfterAddition?.idStatus){
    //   const dataProcessDetail = await this.processService.getProcessDetail(this.dossier.procedureProcessDefinition?.processDefinition?.id).toPromise();
    //   if(!!dataProcessDetail.dynamicVariable.processingTimeAfterAddition){
    //     processProcessingTime = Number(dataProcessDetail.dynamicVariable.processingTimeAfterAddition);
    //   }
    // }
    if ( processProcessingTime !== undefined && processProcessingTime != 0 ) {
      let processingTime = processProcessingTime;
      listTimesheet.push({
        timesheet: {
          id:
            this.dossier.procedureProcessDefinition?.processDefinition
              ?.timesheet !== undefined
              ? this.dossier.procedureProcessDefinition
                .processDefinition.timesheet.id
              : this.config.defaultTimesheetId,
        },
        dossier: {
          id: this.dossierId,
        },
        duration: this.deploymentService.env.timesheetV2 ? processProcessingTime : processingTime,
        startDate: newDate || newDateDefault,
        endDate: null,
        checkOffDay: true,
        offTime: (this.env?.limitedAppointmentTime && type == 1) ? this.env?.limitedAppointmentTime : null,
        extendHCM: this.deploymentService.env.OS_HCM?.limitedAppointmentTimeOnProcedure ? {
            offTime: this.deploymentService.env.OS_HCM?.limitedAppointmentTimeOnProcedure,
            startAtNextDay: this.procedure.extendHCM?.startAtNextDay ?? false,
            appointmentAtNextDay: this.procedure.extendHCM?.appointmentAtNextDay ?? false
          } : null,
        processingTimeUnit: currentTask?.bpmProcessDefinitionTask?.processingTimeUnit,
      });
    }

    const requestBody = JSON.stringify(listTimesheet, null, 2);
    const data = await this.genTime(requestBody);

    if (!!data) {
      return data[0].due;
    } else {
      return null;
    }
  }

  updateVNPostRsult()
  {
    return new Promise<void>(async (resolve) => {
      let bodySuccess = {
        id: this.dossier.vnpostStatus.id,
        customerCode: this.dossier.vnpostStatus.customerCode,
        statusMessage: this.dossier.vnpostStatus.statusMessage,
        statusCode: this.dossier.vnpostStatus.statusCode
      };
      let searchResult = '/dossier/'+this.dossierId+'/result/--vnpost-status';
      this.dossierService.sendVNPostResult(searchResult, bodySuccess).subscribe(dataR => {
        resolve();
      });
    });

  }

  getConnectedInformation() {
    // LLTP
    if (this.env?.interconnectionInfo === '1') {
      this.checkCriminalRecords = 0;
      this.checkCivilStatusJustice = 0;

     if (this.firstTaskData?.dynamicVariable?.criminalRecord) {
        this.checkCriminalRecords = 1;
      }
      if (this.firstTaskData?.dynamicVariable?.civilStatusJustice) {
        this.checkCivilStatusJustice = 1;
      }

      if (this.firstTaskData?.dynamicVariable?.hcmLyLichTuPhap) {
        this.checkHcmLyLichTuPhapReceiving = 1;
      }

      if (this.firstTaskData?.dynamicVariable?.hcmHoTichTuPhap) {
        this.checkHcmHoTichTuPhapReceiving = 1;
      }
    }
  }

  getDossierDetail() {
    return new Promise<void>((resolve, reject) => {
      this.dossierService.getDossierDetail(this.dossierId).subscribe(async data => {
        this.dossier = data; //hunghs

        if(data?.applyMethod?.id === 0) {
          this.isSingleDossierReceipt = this.deploymentService.env.OS_HCM?.receiptOnline?.isSingleDossierReceipt;
        } else {
          this.isSingleDossierReceipt = this.deploymentService.env.OS_HCM?.receiptDirect?.isSingleDossierReceipt;
        }

        if(!!data?.task && !!data?.task[0] && !!data?.task[0]?.bpmProcessDefinitionTask ){
          this.firstTaskData = data?.task[0]?.bpmProcessDefinitionTask;
        }

        this.dossierCode = data.code;
        this.acceptedDateOld = this.dossier?.acceptedDate;
        this.getConnectedInformation();
        //phucnh.it2-IATESUPP-45629
        //Kiem tra bật tham số và có cấu hình chuyển hs sang lltp//&& data?.task[0]?.bpmProcessDefinitionTask?.dynamicVariable?.hcmLyLichTuPhap == true
        if (tUtils.nonNull(data, 'task') && this.enableAddReceiptNumberToEformAllUnitScreen && data?.task[0]?.bpmProcessDefinitionTask?.dynamicVariable?.hcmLyLichTuPhap == true){
          console.log('this.listDossierReceipt' , data.listDossierReceipt);
          // this.checkDossierReceipt = false;
          this.listFeeId = [];
            if (data.listDossierReceipt.length > 0 ){
              this.listDossierReceipt = data.listDossierReceipt;
              console.log('this.listDossierReceipt' , this.listDossierReceipt);
              this.listDossierReceiptNumberAllUnitScreen = "";
              //kiem tra hồ sơ có cấu hình gửi sang lý lịch
              //Lấy thông tin lệ phí, duyet cac loai phi cua thu tuc, neu co loai = let phí và giá = 200; or 100k thì lấy id lưu lại
              if (this.dataProcost != null && this.dataProcost != undefined){
                console.log('this.dataProcost' , this.dataProcost);
                this.dataProcost.forEach(element => {
                  if (element?.type?.id == this.typeLoaiLePhi && (element?.cost == this.costValue1 || element?.cost == this.costValue2) ){
                    this.listFeeId.push(element?.id);
                  }
                })
                let listDossierReceiptNumberAllUnitScreen = '';
                data.listDossierReceipt.forEach(item =>{
                  let itemProduct = item?.data?.product;
                  if (itemProduct.length > 0 && item.status != 0){
                    itemProduct.forEach( ip => {
                      if (this.listFeeId.includes(ip?.prodId)) {
                        //kiem tra xem 7 ky tu chưa
                        let newNumber = '';
                        if (item?.data?.responeReceiptInfo?.listReceiptNumber[0]?.numberReceipt?.length < 7)
                        {
                          for(let i=0; i< (7 - item?.data?.responeReceiptInfo?.listReceiptNumber[0]?.numberReceipt?.length) ; i++){
                            newNumber += '0';
                          }
                          newNumber += item?.data?.responeReceiptInfo?.listReceiptNumber[0]?.numberReceipt
                        }
                        listDossierReceiptNumberAllUnitScreen += newNumber + ',';
                        // this.checkDossierReceipt = true;
                      }
                    });
                  }
                });
                console.log('this.listDossierReceiptNumberAllUnitScreen' , listDossierReceiptNumberAllUnitScreen);
                localStorage.setItem("hcmListDossierReceiptNumberAllUnitScreen", listDossierReceiptNumberAllUnitScreen);
              }
            }
          }else {
            this.listDossierReceiptNumberAllUnitScreen = "";
            localStorage.setItem("hcmListDossierReceiptNumberAllUnitScreen", "");
          }
          //phucnh.it2-IATESUPP-45629

        if (!!data.task && data.task.length !== 0 && this.callApiLGSPAfterGotReceipt.showBtnTransferToLGSP) {
          this.checkHcmLyLichTuPhap = data.task[0].bpmProcessDefinitionTask?.dynamicVariable?.hcmLyLichTuPhap;

          let dossierFeeIds = [];

          if (this.checkHcmLyLichTuPhap && this.dossierFee && this.dossierFee.length > 0) {
            this.dossierFee.forEach(item => {
              if (this.callApiLGSPAfterGotReceipt.procostTypeId == item.procost.type.id || this.callApiLGSPAfterGotReceipt.procostTypeListId.includes(item.procost.type.id)) {
                dossierFeeIds.push(item.id);
              }
            });

            this.numberOfReceipt = await this.eReceiptService.getNumberOfReceipt(dossierFeeIds).toPromise();
          }
        }

        if (!!data?.task && data?.task.length !== 0 && this.fakeCompletedDate) {
          if (this.taskCompletedStatusIds.findIndex(item => item === data?.dossierTaskStatus?.id) !== -1) {
            data.completedDate = data.task[data.task.length - 1].assignedDate;
            data.returnedDate = data.task[data.task.length - 1].assignedDate;
          }
        }

        if (!!data?.task && data?.task.length !== 0 && data?.task.findIndex(item => item.bpmProcessDefinitionTask?.remind?.id === '60f6364e09cbf91d41f88859') !== -1) {
          this.isDossierStatusHasFFO = true;
        }

        if(data?.approvalData && data?.approvalData?.type == 10 &&  !!data?.approvalData?.extendTime)
        {
          this.extendTimeApproval = data.approvalData.extendTime;
        }
        // IGATESUPP-41626
        if(this.onlyDisplayNationCode && !!data.nationCode && data.nationCode != ''){
          data.code = data.nationCode;
          data.nationCode = '';
        }
        this.procedureId = data.procedure.id;
        // this.appliedDate = data.appliedDate;
        // this.isWithdraw = data.isWithdraw;

        // this.checkEnableButtonResendLGSP(this.dossier);

        // if (!!this.dossier?.extendHCM?.citizenWithdrawComment) {
        //   this.reasonWithdraw = this.dossier.extendHCM.citizenWithdrawComment.replace('<p>', '').replace('</p>', '');
        // }

        // if (!!data.code) {
        //   this.getTbthueDvcqg(data.code);
        // }

        // if (this.dossier.dossierStatus === undefined) {
        //   this.putDossierStatus();
        // }

        if (!!data?.currentTask && data?.currentTask.length > 0) {
          this.currentTask = data.currentTask;
          if (data.currentTask[0].bpmProcessDefinitionTask?.remind?.id === this.finaceObligatingStatus) {
            this.checkFinaceObligating = true;
          }else{
            this.checkFinaceObligating = false;
          }
        } else {
          if (!!data.task && data.task.length > 0) {
            this.currentTask.push(data.task[data.task.length - 1]);
          }
        }

        if (this.currentTask[0].sender === undefined) {
          this.currentTask[0].sender = {
            id: '',
            fullname: ''
          };
        }
        // try {
        //   //sonnn.qbh-IGATESUPP-46020
        //   this.userTaskCurrent = data.task.filter(t => t.isCurrent === 1);
        //   if(!!this.userTaskCurrent && this.userTaskCurrent.length > 0) {
        //     this.nameAgencyAccepter = data.agency.parent.name[0].name;
        //     this.nameAgencyJoinProcess = this.userTaskCurrent[0].agency.parent.name[0].name;
        //     this.timeAccepter = this.userTaskCurrent[0].createdDate;
        //     this.getProcessDefinitionTaskById(this.userTaskCurrent[0].bpmProcessDefinitionTask.id);
        //   }
        // } catch (error) {
        //   console.log(error);
        // }


        if (this.dossier.activitiProcessInstance !== undefined) {
          this.activitiProcessInstance = this.dossier.activitiProcessInstance;
        }

        this.applicantEForm.id = this.dossier.applicant.eformId;
        this.dossierService.getFormIoData(this.applicantEForm.id).subscribe(applicantEForm => {
          this.applicantEForm.component = applicantEForm;
          if (this.dossier.applicant.data !== undefined && this.dossier.applicant.data !== null) {
            if (this.applicantEForm.id === this.dossier.applicant.eformId) {
              this.applicantEForm.data = {
                data: this.dossier.applicant.data
              };
            }
          } else {
            this.applicantEForm.data = {
              data: {}
            };
          }
        }, err => {
          if (err.status === 401) {
            window.location.reload();
          }
        });

        this.formIO.id = !!this.dossier.eForm && !!this.dossier.eForm.id ? this.dossier.eForm.id : null;
        this.dossierService.getFormIoData(this.formIO.id).subscribe(formIO => {
          this.formIO.component = formIO;
          if (this.dossier.eForm.data !== undefined && this.dossier.eForm.data !== null) {
            if (this.formIO.id === this.dossier.eForm.id) {
              this.formIO.data = {
                data: this.dossier.eForm.data
              };


            }
          } else {
            this.formIO.data = {
              data: {}
            };
          }
        });

        // dossier task status
        if (this.dossier?.dossierTaskStatus === undefined || this.dossier?.dossierTaskStatus === null) {
          if (this.dossier?.dossierStatus.id === 0) {
            // tslint:disable-next-line:max-line-length tslint:disable-next-line: no-string-literal
            // this.dossier.dossierTaskStatus.name = this.justRegisteredText;
            this.dossier.dossierTaskStatus = {
              name: this.justRegisteredText
            };
          } else {
            // tslint:disable-next-line: no-string-literal
            // this.dossier.dossierTaskStatus.name = this.dossier.currentTask[0].bpmProcessDefinitionTask.name['name'];
            if(this.dossier.currentTask?.length > 0){
              this.dossier.dossierTaskStatus = {
                name: this.dossier.currentTask[0]?.bpmProcessDefinitionTask.name['name']
              };
            }else{
              this.dossier.dossierTaskStatus = {
                name: ""
              };
            }


          }
        }

        // if (tUtils.nonNull(this.dossier, 'validateResidentialInfo')) {
        //   this.isValidResidentialInfo = this.dossier.validateResidentialInfo.confirm;
        // }

        // dossier citizen latest updated date
        if (!!this.dossier.applicant?.userId) {
          this.citizenId = this.dossier.applicant?.userId;
        }
        if (!!this.dossier.additionalRequirementDetail?.latestAdditionalDate) {
          this.latestAdditionalDate = this.dossier.additionalRequirementDetail?.latestAdditionalDate;
        }
        // this.addDossierCitizenLatestAdditionalDateToHistory();

        // dossier reception method
        // for (const receptionMethod of this.listReceptionMethod) {
        //   if (data.applyMethod.id === receptionMethod.id) {
        //     // tslint:disable-next-line: max-line-length
        //     receptionMethod.content = receptionMethod.name.filter(item => item.languageId === Number(localStorage.getItem('languageId')))[0].content;
        //     this.listReceptionMethod.push(receptionMethod);
        //     break;
        //   }
        // }

        let processingTime = 0;
        let showDurationCalculateAppointmentDate = false;
        this.durationCalculateAppointmentDate = '';
        let dateDiffPayment = 0;
        if (this.dossier.processingTime !== '' && this.dossier.processingTime !== null) {
          let processingTimeWithUnit = this.dossier.processingTime;
          if(this.deploymentService.env?.OS_HCM?.processingTimeAfterAddition?.enable && !!this.dossier?.acceptedDate && !!this.dossier?.previousTask && this.dossier?.additionalRequirementDetail?.count >= 1
            && this.dossier.oldData?.dossierTaskStatus.id == this.deploymentService.env?.OS_HCM?.processingTimeAfterAddition?.idStatus){
            const dataProcessDetail = await this.processService.getProcessDetail(this.dossier.procedureProcessDefinition?.processDefinition?.id).toPromise();
            if(!!dataProcessDetail.dynamicVariable.processingTimeAfterAddition){
              processingTimeWithUnit = Number(dataProcessDetail.dynamicVariable.processingTimeAfterAddition);
              this.dossier.processingTime = processingTimeWithUnit;
              this.isProcessingTimeAfterAddition = true
            }
          }
          if (this.calculateAppointmentDate == 1 && this.dossier?.paymentRequestData?.dateDiff !== null && this.dossier?.paymentRequestData?.dateDiff !== undefined) {
            dateDiffPayment = this.dossier.paymentRequestData.dateDiff;
            processingTimeWithUnit += dateDiffPayment;
            showDurationCalculateAppointmentDate = true;
          }
          switch (this.dossier.processingTimeUnit) {
            case 'y':
              processingTime = Number(processingTimeWithUnit) * 365;
              break;
            case 'M':
              processingTime = Number(processingTimeWithUnit) * 30;
              break;
            case 'd':
              processingTime = Number(processingTimeWithUnit);
              break;
            case 'H:m:s':
              processingTime = Number(processingTimeWithUnit) / 24;
              break;
            case 'h':
              processingTime = Number(processingTimeWithUnit) / 24;
              break;
            case 'm':
              processingTime = Number(processingTimeWithUnit) / (24 * 60);
              break;
          }
        }
        if (showDurationCalculateAppointmentDate) {
          const d = Math.floor(processingTime);
          if (d > 0) {
            this.durationCalculateAppointmentDate = d + ' ngày';
          } else {
            const h = (processingTime - d) * 24;
            if (h > 0) {
                this.durationCalculateAppointmentDate = Math.floor(h) + ' giờ';
            } else {
                const m = (h - Math.floor(h)) * 60;
                if (m > 0) {
                    this.durationCalculateAppointmentDate = Math.floor(m) + ' phút';
                } else {
                    const s = (m - Math.floor(m)) * 60;
                    if (s > 0) {
                        this.durationCalculateAppointmentDate = Math.floor(s) + ' giây';
                    }
                }
            }
          }
          this.showDurationCalculateAppointmentDate = true;
        }

        //trankequang - IGATESUPP-46405
        // if (this.checkProcAdminAgency()) {
        //   await this.getProAdmin(this.dossier);
        //   if (this.proAdminELEMENTDATA.length <= 0) {
        //     this.showDossierProAdmins = false;
        //   }
        //   else {
        //     this.showDossierProAdmins = true;
        //   }
        // }

        //phucnh.it2-IGATESUPP-40295
        if (this.enableNewVnpostStatus){
          let newId = this.dossier.id + 'tkq';
          let vnpostStatusSearchString = "?orderNumber="+newId ;   //const newDossier= await  this.dossierService.getDossierDetail(data.content[i].id).toPromise();
          console.log(vnpostStatusSearchString);
          const dataVnpostStatus= await  this.dossierService.getVnpostStatus(vnpostStatusSearchString).toPromise();
          if (dataVnpostStatus.length > 0) {
            this.dossier.vnpostStatus = dataVnpostStatus[0];
            console.log(dataVnpostStatus[0]);
          }
        }
        //End phucnh.it2-IGATESUPP-40295
        // thay đổi tên status qbh
        // đổi tên trạng thái hồ sơ
        if ( this.qbhmenuaction )
        {
          if( this.dossier?.dossierStatus.id == 11 || this.dossier?.dossierTaskStatus.id == "61ee30eada2d36b037e00004" )
          {
            this.dossier.dossierTaskStatus = {
              name: "Xác nhận từ chối giải quyết"
            };
          }
          if( this.dossier?.dossierStatus.id == 12 || this.dossier?.dossierTaskStatus.id == "61ee30eada2d36b037e00005" )
          {
            this.dossier.dossierTaskStatus = {
              name: "Từ chối giải quyết"
            };
          }
          if( this.dossier?.dossierStatus.id == 10 || this.dossier?.dossierTaskStatus.id == "61ee30eada2d36b037e00003" )
          {
            this.dossier.dossierTaskStatus = {
              name: "Xác nhận xin lỗi và xin gia hạn"
            };
          }
          if( this.dossier?.dossierStatus.id == 8 || this.dossier?.dossierTaskStatus.id == "61ee30eada2d36b037e00001" )
          {
            this.dossier.dossierTaskStatus = {
              name: "Xác nhận yêu cầu bổ sung"
            };
          }
          if( this.dossier?.dossierStatus.id == 6 && this.dossier?.dossierTaskStatus.id == "6151c771ba2a04299f949875" )
          {
            this.dossier.dossierTaskStatus = {
              name: "Xác nhận rút hồ sơ"
            };
          }
        }
        // check ho so khong xac dinh thoi han
        this.undefindedCompleteTime = data.undefindedCompleteTime;
        this.undefindedCompleteTimeTask = Number(this.currentTask[0].bpmProcessDefinitionTask.processingTime) === 0 ? 1 : 0;
        if(this.dossier.procedureProcessDefinition){
          let requestBodyObj:any = [];
          if (this.env.limitedAppointmentTime) {
            requestBodyObj = [
              {
                timesheet: {
                  id: this.dossier.procedureProcessDefinition.processDefinition.timesheet.id
                },
                dossier: {
                  id: this.dossierId
                },
                duration: this.deploymentService.env.timesheetV2 ? (this.dossier.processingTime + dateDiffPayment) : processingTime,
                startDate: this.dossier?.additionalRequirementDetail?.acceptedDate!== null &&
                this.dossier?.additionalRequirementDetail?.acceptedDate !== undefined ?
                this.dossier?.additionalRequirementDetail?.acceptedDate: this.dossier.acceptedDate,
                endDate: '',
                checkOffDay: true,
                offTime: this.env.limitedAppointmentTime,
                processingTimeUnit: this.dossier.processingTimeUnit
              }
            ];
          } else if (this.deploymentService.env.OS_HCM?.limitedAppointmentTimeOnProcedure) {
            requestBodyObj = [
              {
                timesheet: {
                  id: this.dossier.procedureProcessDefinition.processDefinition.timesheet.id
                },
                dossier: {
                  id: this.dossierId
                },
                duration: this.deploymentService.env.timesheetV2 ? (this.dossier.processingTime + dateDiffPayment) : processingTime,
                startDate: this.dossier?.additionalRequirementDetail?.acceptedDate!== null &&
                this.dossier?.additionalRequirementDetail?.acceptedDate !== undefined ?
                this.dossier?.additionalRequirementDetail?.acceptedDate: this.dossier.acceptedDate,
                endDate: '',
                checkOffDay: true,
                extendHCM: {
                  offTime: this.deploymentService.env.OS_HCM?.limitedAppointmentTimeOnProcedure,
                  startAtNextDay: this.dossier?.extendHCM?.startAtNextDay ?? false,
                  appointmentAtNextDay: this.dossier?.extendHCM?.appointmentAtNextDay ?? false
                },
                processingTimeUnit: this.dossier.processingTimeUnit
              }
            ];
          } else {
            requestBodyObj = [
              {
                timesheet: {
                  id: this.dossier.procedureProcessDefinition.processDefinition.timesheet.id
                },
                dossier: {
                  id: this.dossierId
                },
                duration: this.deploymentService.env.timesheetV2 ? (this.dossier.processingTime + dateDiffPayment) : processingTime,
                startDate: this.dossier?.additionalRequirementDetail?.acceptedDate!== null &&
                this.dossier?.additionalRequirementDetail?.acceptedDate !== undefined ?
                this.dossier?.additionalRequirementDetail?.acceptedDate: this.dossier.acceptedDate,
                endDate: '',
                checkOffDay: true,
                processingTimeUnit: this.dossier.processingTimeUnit
              }
            ];
          }
          const requestBody = JSON.stringify(requestBodyObj, null, 2);
          this.dossierService.postTimesheet(requestBody).subscribe(res => {
            this.timesheet.endDate = res[0].due;
            if(this.isProcessingTimeAfterAddition){
              this.dossier.appointmentDate = this.timesheet.endDate ;
            }
            if (this.isDossierStatusHasFFO && this.deploymentService.env.dossierHasFFOSwapDue) {
              this.timesheet.endDate = this.dossier?.appointmentDate;
            }
            this.postTimesheet();
          }, err => {
            console.log(err);
          });
        }
        // this.dossierReceivingKind_VNP = data.dossierReceivingKind?.id == (this.env?.vnpost?.receiveResultsByAddress != undefined ? this.env?.vnpost?.receiveResultsByAddress : this.config.receiveResultsByAddress);

        console.log("check hideInforAdditionalRequirementDate");
        if (this.deploymentService.env.OS_HCM.hideInforAdditionalRequirementDate && this.deploymentService.env.OS_HCM.hideInforAdditionalRequirementDateDossierList.includes(data?.dossierTaskStatus?.id)) {
          console.log("check ***");
          this.hideInforAdditionalRequirementDate = true;
        }
        //IGATESUPP-81849
        if(data?.dossierStatus?.id === this.dossierService?.StatusYCBS){
          this.hideInforAdditionalRequirementDate = true;
        }
        console.log(this.hideInforAdditionalRequirementDate);

        resolve();
      }, err => {
        reject(err);
      });
    });
  }

  async getAccountByUserId(userId) {
    // this.userService.getFullUserInfo
    return new Promise(resolve => {
      let result = {};
      this.userService.getFullUserInfo(userId).subscribe(data => {
        result = {
          id: data.id,
          fullname: data.fullname,
          account: data.account
        };
        resolve(result);
      });
    });
  }

  postComment(commentContent, description?:string) {
    const userId = localStorage.getItem("tempUID");
    const content = {
      groupId: 2,
      itemId: this.dossierId,
      user: {
        id: userId,
        fullname: this.accepterInfo.fullname
      },
      content: commentContent,
      // file: this.uploadedImage
    };
    const requestBody = JSON.stringify(content, null, 2);
    this.dossierService.postComment(requestBody).subscribe(data => { });
    }

    updatePaymentMeThodOnline(dossier : any) {
      let typeDossier = 1;
      if (!!dossier.applyMethod && dossier.applyMethod.id !== null){
        typeDossier = dossier.applyMethod.id;
      }
      const dialogData = new AddPaymentMethodModel(dossier , dossier.id, dossier.procedure.id, typeDossier);
      const dialogRef = this.dialog.open(AddPaymentMethodComponent, {
        minWidth: '80vw',
        maxHeight: '80vh',
        data: dialogData,
        disableClose: true,
        autoFocus: false
      });
      dialogRef.afterClosed().subscribe(dialogResult => {
        if (dialogResult === true) {
          // this.postHistory(3, 'Lệ phí hồ sơ', 'Cập nhật', 'Cập nhật');
          const msgObj = {
            vi: 'Cập nhật thành công!',
            en: 'Update successfully!'
          };
          this.snackbarService.openSnackBar(1, msgObj[this.selectedLang], '', 'success_notification', this.config.expiredTime);
        }
        if (dialogResult === false) {
          const msgObj = {
            vi: 'Cập nhật thất bại!',
            en: 'Update failed!'
          };
          this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
        }
      });
    }

    paymentOnlineOfficer(row : any){
      this.dossierService.getDossierDetail(row.id).subscribe(data =>{
        this.updatePaymentMeThodOnline(data);
      })
    }

    getListTagDossierReceivingKind(id, page, size, sort) {
      this.procedureService.getListTagByCategoryId(id, page, size, sort).subscribe(data => {
        for (let i = 0; i < data.numberOfElements; i++) {
          this.listDossierReceivingKind.push(data.content[i]);
        }
      }, err => {
        console.log(err);
      });
    }

    // IGATESUPP-76910
  async getListAgencyUnit(options?: { keyword?: string }) {
    if (!this.dossierSearchShowAgencyUnit) {
      return;
    }
    const params = new HttpParams({
      fromObject: {
        "keyword": options?.keyword ?? "",
        "parent-id": this.agencyId ?? "",
        "page": this.pageAgencyUnit.toString(),
        "size": this.pageAgencyUnitSize.toString(),
        "sort": "name.name,asc",
        "status": "1",
      }
    });
    if (typeof this.agencyId != "string" || this.agencyId?.trim() == "") {
      this.listAgencyUnit = [];
      this.listAssignee = [];
    } else {
      const data = await this.procedureService.getListAgencyWithParent(`?${params.toString()}`).toPromise();
      if (typeof data == "object" && Array.isArray(data.content)) {
        this.listAgencyUnit = this.pageAgencyUnit === 0 ? data.content : this.listAgencyUnit.concat(data.content);
        this.isLastAgencyUnit = data.last;
        this.pageAgencyUnit++;
        await this.autofillAgencyUnit();
      }
    }
  }

  // IGATESUPP-79377
  async autofillAgencyUnit() {
    if (this.dossierSearchAutofillAgencyUnit && !this.agencyUnitFilled) {
      // Get user experiences
      const userId = localStorage.getItem("tempUID");
      if (this.userExperiences.length == 0) {
        const experiences = await this.userService.getUserExperience(userId).toPromise();
        if (Array.isArray(experiences)) {
          this.userExperiences = experiences;
        }
      }
      // Find experience primary
      for (let index = 0; index < this.userExperiences.length; index++) {
        const experience = this.userExperiences[index];
        if (typeof experience == "object" && typeof experience["agency"] =="object" && experience["primary"] == true) {
          // Choose agency id
          let agencyId = null;
          if (typeof experience["agency"]["parent"] == "object" && typeof experience["agency"]["id"] == "string") {
            agencyId = experience["agency"]["id"];
          }
          if (agencyId != null) {
            // Find experience in agencies
            const founded = this.listAgencyUnit.find(e => typeof e["id"] == "string" && e["id"] == agencyId);
            if (founded != undefined && typeof founded["id"] == "string") {
              this.searchForm.get('taskAgencyCtrl').setValue(founded["id"]);
              await this.agencyUnitChange({value: founded["id"]});
              this.agencyUnitFilled = true;
            } else if (!this.isLastAgencyUnit) {
              await this.getListAgencyUnit();
            }
          }
        }
      }
    }
  }

  async agencyUnitChange(event) {
    this.pageAssignee = 0;
    this.isLastAssignee = false;
    await this.getListAssignee({agencyId: event.value});
  }

  async getListAssignee(options?: { fullname?: string, agencyId?: string }) {
    if (this.isLastAssignee) { return; }
    const agencyId = options?.agencyId ?? this.searchForm.get("taskAgencyCtrl").value ?? "";
    if (typeof agencyId != "string" || agencyId == "") {
      this.listAssignee = [];
      this.isLastAssignee = true;
      return;
    }
    const params = new HttpParams({
      fromObject: {
        "fullname": options?.fullname ?? "",
        "agency-id": agencyId,
        "page": this.pageAssignee.toString(),
        "size": this.pageAssigneeSize.toString(),
        "sortType": "asc",
        "type": "3",
        "ldap": "0",
      }
    });
    this.humanService.getUsers1(`?${params.toString()}`).subscribe(data => {
      if (typeof data == "object" && Array.isArray(data.content)) {
        this.listAssignee = this.pageAssignee === 0 ? data.content : this.listAssignee.concat(data.content);
        this.isLastAssignee = data.last;
        this.pageAssignee++;
      }
    });
  }
  //  END IGATESUPP-76910

  excelDoiGPLX(){
    const formObj = this.searchForm.getRawValue();

    var nextDate = new Date();
    nextDate.setDate(nextDate.getDate()+1);

    let fromDate = this.datePipe.transform(this.getMonday(new Date()), 'dd/MM/yyyy');
    let toDate = this.datePipe.transform(nextDate, 'dd/MM/yyyy');

    if(!formObj.advAcceptFrom && formObj.advAcceptTo){
      const msgObj = {
        vi: 'Vui lòng chọn thêm tiếp nhận từ ngày',
        en: 'Please input applied from date',
      };
      this.snackbarService.openSnackBar(
        0,
        msgObj[this.selectedLang],
        '',
        'error_notification',
        this.config.expiredTime
      );
      return;
    }

    if(formObj.advAcceptTo){
      var tDate = new Date(formObj.advAcceptTo);
      tDate.setDate(tDate.getDate()+1);
      toDate = this.datePipe.transform(tDate, 'dd/MM/yyyy');
    }

    if(formObj.advAcceptFrom){
      fromDate = this.datePipe.transform(formObj.advAcceptFrom, 'dd/MM/yyyy');
    }

    let searchString = `?procedure-id=${this.procedureIdDoiGPLX}&from-applied-date=${fromDate}&to-applied-date=${toDate}&exclude-status-ids=${this.excludeStatusIDs.join()}`;

    this.dossierService.getListDoiGPLXExport(searchString).subscribe((data) =>
      {
        this.dossierService.downloadDoiGPLX(data, `f-${fromDate}-t-${toDate}`);
      },
      (err) =>{
        const msgObj = {
          vi: 'Không thể xuất file excel',
          en: 'Cannot export excel',
        };
        this.snackbarService.openSnackBar(
          0,
          msgObj[this.selectedLang],
          '',
          'error_notification',
          this.config.expiredTime
        );
      }

      );
  }

  getMonday(d) {
    d = new Date(d);
    var day = d.getDay(),
      diff = d.getDate() - day + (day == 0 ? -6 : 1); // adjust when day is sunday
    return new Date(d.setDate(diff));
  }

  // IGATESUPP-83248
  syncStatusDossier(dossier: any) {
    const bpmProcessDefinitionTaskId = !!dossier?.currentTask?.length ? dossier?.currentTask[0]?.bpmProcessDefinitionTask.id : null;
    const msgObjNoData = {
      vi: 'Chưa có dữ liệu để đồng bộ!',
      en: 'There is no data to sync!'
    };
    const msgObjDossierCorrect = {
      vi: 'Dữ liệu đã đúng không đồng bộ được!',
      en: 'Correct data is not synchronized!'
    };
    if(!bpmProcessDefinitionTaskId) {
      return this.snackbarService.openSnackBar(0, msgObjNoData[this.selectedLang], '', 'error_notification', this.config.expiredTime);
    }
    const listProcessDefinitionTask = JSON.stringify([bpmProcessDefinitionTaskId], null, 2)
    this.dossierService.getListDossierDefinitionTask(listProcessDefinitionTask).subscribe(data => {
      const msgObjError = {
        vi: 'Đồng bộ thất bại!',
        en: 'Sync Faild!'
      };
      const dossierTaskStatusId = dossier?.dossierTaskStatus.id;
      const bpmData = !!data?.length ? data[0] : undefined
      if(!!bpmData && !!dossierTaskStatusId)  {
        const dialogData = new SyncStatusDossierDialogModel(bpmData,dossier?.id);
        const dialogRef = this.dialog.open(SyncStatusDossierComponent, {
          minWidth: '40vw',
          maxHeight: '80vh',
          data: dialogData,
          disableClose: true,
          autoFocus: false
        });

    dialogRef.afterClosed().subscribe(async (dialogResult) => {
      if (dialogResult === true) {
        const msgObjSuccess = {
          vi: 'Đồng bộ Thành công!',
          en: 'Sync Successfully!'
        };
        this.snackbarService.openSnackBar(1, msgObjSuccess[this.selectedLang], '', 'success_notification', this.config.expiredTime);
        this.onConfirmSearch();
      } else if (dialogResult === false) {
        this.snackbarService.openSnackBar(0, msgObjError[this.selectedLang], '', 'error_notification', this.config.expiredTime);
      }
    });
      }else {
        return this.snackbarService.openSnackBar(0, msgObjDossierCorrect[this.selectedLang], '', 'error_notification', this.config.expiredTime);
    }
    })

  }
  changeStyleTable() {
     if (!this.isAddColumDataExport) {
         this.columnStyles = [
             { 'width': '7%' }, { 'width': '15%' }, { 'width': '10%' }, { 'width': '15%' }, { 'width': '13%' },
             { 'width': '15%' }, { 'width': '15%' }, { 'width': '10%' }
         ];
     } else {
         this.columnStyles = [
             { 'width': '3%' }, { 'width': '10%' }, { 'width': '10%' }, { 'width': '10%' },
             { 'width': '10%' }, { 'width': '10%' }, { 'width': '7%' }, { 'width': '10%' },
             { 'width': '7%' }, { 'width': '10%' }, { 'width': '13%' },
         ];
     }
  }
  downloadZipFileDossier(row: any) {
    this.dossierService.downloadAllFile({
        id: row.id, //dossierId
        code: row.code //dossierCode
    });
  }
  viewSendAnApologyLetter(dossierId, dossierCode) {
    if (dossierCode === undefined || dossierCode === null) {
      dossierCode = '';
    }
    const dialogData = new ConfirmApologyLetterDialogModel(dossierId, dossierCode);
    const dialogRef = this.dialog.open(ApologyLetterComponent, {
      minWidth: '50vw',
      maxHeight: '90vh',
      data: dialogData,
      disableClose: true,
      autoFocus: false
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
      if(dialogResult ==  true){
        console.log("khanh: " + JSON.stringify(dialogResult));
      }
    });
  }

  checkShowAbstractConstructionUnit() {
    const userAgency = JSON.parse(localStorage.getItem('userAgency'));
    if (this.showAbstractConstructionUnitEnable == 1 && this.showAbstractConstructionUnitAgencyIds.length > 0) {
      if(this.showAbstractConstructionUnitAgencyIds.filter(item => item == userAgency.id).length > 0){
        this.onOffAbstractConstructionUnit = true;
      } else {
        this.onOffAbstractConstructionUnit = false;
      }
    }
  }

  showVNPostInfo(row) {
    const dialogData = new ConfirmVnpostInfoDialogModel(row?.id);
    const dialogRef = this.dialog.open(VnpostInfoComponent, {
      width: '800px',
      data: dialogData,
      disableClose: true,
      autoFocus: false
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
    });
  }

  checkShowFilterRegisterApplicationVNeIDValue(){
    if (this.showFilterRegisterApplicationVNeID == 1) {
        const userAgency = JSON.parse(localStorage.getItem('userAgency'));
        if (this.showFilterRegisterApplicationVNeIDAgencyId.filter(item => item == userAgency.id).length >0) {
          this.checkShowFilterRegisterApplicationVNeID = true;
        } else {
          this.checkShowFilterRegisterApplicationVNeID = false;
        }
    }
  }
   checkQTI(tasks){
    let fullName = "";
    try{     
       if(!!tasks){
              var taskSize = tasks.length;
              if(taskSize == 0)
               return "";
              var lastTask = tasks[taskSize -1];
              if (!!lastTask?.agency && !!lastTask?.agency?.name) {
                for  (let name of lastTask?.agency?.name) {
                  if (name?.languageId === this.selectedLangId) {
                    fullName = name?.name;
                  }
                }
                if (!!lastTask?.agency?.parent && !!lastTask?.agency?.parent?.name) {
                  for  (let name of lastTask?.agency?.parent?.name) {
                    if (name?.languageId === this.selectedLangId) {
                      fullName += ' - ' + name?.name;
                    }
                  }
                }
              }
       }
      return fullName;
    }catch{return fullName;}
  }
   getAssignName(tasks){
    let fullName = "";
    try{     
       if(!!tasks){
              var taskSize = tasks.length;
              if(taskSize == 0)
               return "";
              var lastTask = tasks[taskSize -1];
              if (!!lastTask?.assignee && !!lastTask?.assignee?.id) {
                fullName = lastTask?.assignee?.fullname;
              } else if (lastTask?.candidateUser?.length > 0) {
                const arrUser = lastTask?.candidateUser.map(item => item?.fullname);
                fullName = arrUser.join(',');
              }
              else {
                fullName = '';
              }
            }         
       return fullName;
    }catch(e){
      console.log(e)
      return fullName;
    }
  }
  
  //region VPC - IGATESUPP-101440 - Xác nhận đã nhận hồ sơ gốc
  async confirmOriginalDossier() {
    let dossierCodes = [];
    for await (const id of this.selectedDossiers) {
      const dossier: any = this.dataSource.data.find(item => item.id === id);
      if (!!dossier) {
        dossierCodes.push(dossier.id);
      }
    }
    const requestBodyObj = {
      updateObjectId: dossierCodes,
      updateUser: {
        id: this.userId,
        fullname: this.accepterInfo.fullname,
        account: {
          id: this.accepterInfo.accountId,
          username: [
            {
              value: this.accepterInfo.username
            }
          ]
        }
      }
    };
    const requestBody = JSON.stringify(requestBodyObj, null, 2);

    const dialogData = new ConfirmationDialogModel('Đã nhận hồ sơ gốc', `Xác nhận bạn đã nhận được hồ sơ gốc từ công dân?`);
    const dialogRef = this.dialog.open(ConfirmDialogComponent, {
      width: '512px',
      data: dialogData,
      disableClose: true,
      autoFocus: false,
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
      if (!!dialogResult) {
        this.dossierOriginalService.putConfirmOriginalDossier(requestBody).subscribe(data => {
          if (data.affectedRows == 1) {
            for (const id of dossierCodes){
              this.postCommenVPC(id, "Xác nhận đã nhận hồ sơ gốc");
              this.postHistoryVPC(id, 3, 'Xác nhận hồ sơ gốc', 'Cập nhật', 'Cập nhật thành công');
            }
            const msgObj = {
              vi: 'Xác nhận đã nhận hồ sơ gốc thành công',
              en: 'Confirm success'
            };
            this.snackbarService.openSnackBar(1, msgObj[this.selectedLang], '', 'success_notification', this.config.expiredTime);
            setTimeout(() => {
              this.reloadPageVPC();
              this.onConfirmSearch();
            }, 1000);
          } else {
            for (const id of dossierCodes){
              this.postHistoryVPC(id, 3, 'Xác nhận hồ sơ gốc', 'Cập nhật', 'Cập nhật không thành công');
            }
            const msgObj = {
              vi: 'Xác nhận đã nhận hồ sơ gốc không thành công',
              en: 'Confirm failed'
            };
            this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
          }
        });
      }
    });
  }
  
  reloadPageVPC() {
    this.router.navigateByUrl('/dossier/search', { skipLocationChange: true }).then(() => {
      this.router.navigate(['/dossier/search']);
    });
  }
  
  postCommenVPC(dossierId, commentContent) {
    const content = {
      groupId: 2,
      itemId: dossierId,
      user: {
        id: this.accepterInfo.id,
        fullname: this.accepterInfo.fullname
      },
      content: commentContent
    };
    const requestBody = JSON.stringify(content, null, 2);
    this.dossierService.postComment(requestBody).subscribe(data => { });
  }
  
  postHistoryVPC(dossierId, type, col, oldVal, newVal) {
    const body = {
      groupId: 1,
      itemId: dossierId,
      user: {
        id: this.accepterInfo.accountId,
        name: this.accepterInfo.fullname
      },
      type,
      deploymentId: this.config.deploymentId,
      action: [
        {
          fieldNameRbk: col,
          originalValue: oldVal,
          newValue: newVal
        }
      ]
    };
    const requestBody = JSON.stringify(body, null, 2);
    this.dossierService.postHistory(requestBody).subscribe();
  }
  //endregion
  setQueryParams(){
    // const formObj = this.searchForm.value;
    const formObj = this.searchForm.getRawValue();
    this.router.navigate(['/dossier/search'], {
      queryParams: {
        code: formObj.code?.trim(),
        identity: formObj.identityNumber?.trim(),
        applicant: formObj.applicantName?.trim(),
        page: 1,
        size: this.size.toString(),
        procedure: formObj.procedureId,
        sector: formObj.advSector,
        applyMethod: formObj.advApplyMethod,
        province: formObj.advProvince,
        district: formObj.advdistrict,
        ward: formObj.advWard,
        ownerFullname:formObj.ownerFullname?.trim(),
        acceptFrom: (formObj.advAcceptFrom ? this.datePipe.transform(formObj.advAcceptFrom, 'yyyy-MM-dd') : ''),
        acceptTo: (formObj.advAcceptTo ? this.datePipe.transform(formObj.advAcceptTo, 'yyyy-MM-dd') : ''),
        status: formObj.advTaskStatusId,
        remindId: this.remindId,
        sortId: this.sortId,
        // organization: formObj.advAddressOrganization?.trim(),
        applicantOrganization: formObj.applicantOrganization.trim(),
        appointmentFrom: (formObj.advAppointmentFrom ? this.datePipe.transform(formObj.advAppointmentFrom, 'yyyy-MM-dd') : ''),
        appointmentTo: (formObj.advAppointmentTo ? this.datePipe.transform(formObj.advAppointmentTo, 'yyyy-MM-dd') : ''),
        resultReturnedFrom: (formObj.avdResultReturnedFrom ? this.datePipe.transform(formObj.avdResultReturnedFrom, 'dd/MM/yyyy') : ''),
        resultReturnedTo: (formObj.avdResultReturnedTo ? this.datePipe.transform(formObj.avdResultReturnedTo, 'dd/MM/yyyy') : ''),
        // checkConnectProcess: formObj.checkConnectProcess,
        receiptCode: formObj.receiptCode?.trim(),
        taxCode: formObj.taxCode,
        resPerson: formObj.resPerson,
        dossierProcessingStatus: formObj.advProcessStatus,
        vnpostStatus: formObj.vnpostStatus,
        phoneNumberApply: formObj.phoneNumberApply?.trim(),
      }
    });
  }
}
function compare(a: number | string, b: number | string, isAsc: boolean) {
  return (a < b ? -1 : 1) * (isAsc ? 1 : -1);
}
