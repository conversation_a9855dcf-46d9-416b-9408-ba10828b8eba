import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { EnvService } from 'core/service/env.service';
import { ApiProviderService } from 'src/app/core/service/api-provider.service';
import { SnackbarService } from '../snackbar/snackbar.service';
import { Workbook } from 'exceljs';

import * as fs from 'file-saver';
const EXCEL_TYPE = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8';
const EXCEL_EXTENSION = '.xlsx';


@Injectable({
  providedIn: 'root'
})
export class ReportSyncGplxService {

  language = localStorage.getItem('language') === 'vi' ? 228 : 46;
  config = this.envService.getConfig();

  constructor(
    private http: HttpClient,
    private envService: EnvService,
    private apiProviderService: ApiProviderService,
    private snackbar: SnackbarService,
  ) { }

  private padmanURL = this.apiProviderService.getUrl('digo', 'padman');
  private basedataURL = this.apiProviderService.getUrl('digo', 'basedata');
  private basepadURL = this.apiProviderService.getUrl('digo', 'basepad');
  private basecatURL = this.apiProviderService.getUrl('digo', 'basecat');


  public excelExportPaymentURL = this.apiProviderService.getUrl('digo', 'padman') + '/dac-thu-gli/--export-excel-dossier-sync-gplx'

  getDossierSyncGplx(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');

    return this.http.get(this.padmanURL + '/dac-thu-gli/list-dossier-sync-gplx' + searchString, { headers }).pipe();

  }
  getDossierSyncGplxforExcel(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');

    return this.http.get(this.padmanURL + '/dac-thu-gli/--export-excel-dossier-sync-gplx' + searchString, { headers }).pipe();

  }

  getDossierBCCIGli(body): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');

    return this.http.post(this.padmanURL + '/dac-thu-gli/report-dossier-bcci', body,{ headers }).pipe();

  }
public exportAsExcelDossierSyncGplx(
  reportHeading: string,
  reportSubHeading: string,
  nameReport: string,
  subNameReport: string,
  json: any[],
  excelFileName: string,
  sheetName: string,
  agencyName: string
) {
  const data = json;
  // create workbook and worksheet
  const workbook = new Workbook();
  workbook.creator = 'Snippet Coder';
  workbook.lastModifiedBy = 'SnippetCoder';
  workbook.created = new Date();
  workbook.modified = new Date();
  const worksheet = workbook.addWorksheet(sheetName);

  worksheet.getColumn('A').font = {name: 'Times New Roman', size: 12};
  worksheet.getColumn('B').font = {name: 'Times New Roman', size: 12};
  worksheet.getColumn('C').font = {name: 'Times New Roman', size: 12};
  worksheet.getColumn('D').font = {name: 'Times New Roman', size: 12};
  worksheet.getColumn('E').font = {name: 'Times New Roman', size: 12};
  worksheet.getColumn('F').font = {name: 'Times New Roman', size: 12};
  worksheet.getColumn('G').font = {name: 'Times New Roman', size: 12};
  worksheet.getColumn('H').font = {name: 'Times New Roman', size: 12};

  worksheet.getColumn('A').alignment = { horizontal: 'center', vertical: 'middle' };
  worksheet.getColumn('B').alignment = { horizontal: 'center', vertical: 'middle' };
  worksheet.getColumn('C').alignment = { horizontal: 'center', vertical: 'middle' };
  worksheet.getColumn('D').alignment = { horizontal: 'center', vertical: 'middle' };
  worksheet.getColumn('E').alignment = { horizontal: 'center', vertical: 'middle' };
  worksheet.getColumn('F').alignment = { horizontal: 'center', vertical: 'middle' };
  worksheet.getColumn('G').alignment = { horizontal: 'center', vertical: 'middle' };
  worksheet.getColumn('H').alignment = { horizontal: 'center', vertical: 'middle' };

  // worksheet.getColumn('A').width = 30;
  worksheet.getColumn('B').width = 40;
  worksheet.getColumn('C').width = 20;
  worksheet.getColumn('D').width = 20;
  worksheet.getColumn('E').width = 20;
  worksheet.getColumn('F').width = 20;
  worksheet.getColumn('G').width = 20;
  worksheet.getColumn('H').width = 20;

  // Add header row
  worksheet.addRow([]);
  worksheet.mergeCells('A1:H1');
  worksheet.getCell('A1').value = reportHeading;
  worksheet.getCell('A1').alignment = { horizontal: 'center', vertical: 'middle' };

  worksheet.addRow([]);
  worksheet.mergeCells('A2:H2');
  worksheet.getCell('A2').value = reportSubHeading;
  worksheet.getCell('A2').alignment = { horizontal: 'center', vertical: 'middle' };
  worksheet.getCell('A2').font = { size: 12, italic: true, name: 'Times New Roman' };

  worksheet.getCell('A4').value = 'STT';
  worksheet.getCell('B4').value = 'Mã hồ sơ';
  worksheet.getCell('C4').value = 'Nội dung giải quyết hồ sơ';
  worksheet.getCell('D4').value = 'Người nộp hồ sơ';
  worksheet.getCell('E4').value = 'Địa chỉ';
  worksheet.getCell('F4').value = 'Ngày nộp';
  worksheet.getCell('G4').value = 'Ngày hoàn thành';
  worksheet.getCell('H4').value = 'Trạng thái đồng bộ';
  worksheet.getCell('I4').value = 'Ngày đồng bộ';
  worksheet.properties.outlineLevelCol = 2; worksheet.properties.defaultRowHeight = 15;
  let i = 4;
  const j = 4;
  for (i; i <= j; i++) {
    let k = 1;
    const l = 9;
    for (k; k <= l; k++) {
      worksheet.findCell(i, k).alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
      worksheet.findCell(i, k).fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'C0C0C0C0' },
        bgColor: { argb: 'FF0000FF' }
      };
      worksheet.findCell(i, k).border = { top: { style: 'thin' }, left: { style: 'thin' }, bottom: { style: 'thin' }, right: { style: 'thin' } };
      worksheet.findCell(i, k).font = { size: 12, bold: true, name: 'Times New Roman' };
    }
  }

  // get all columns from JSON
  let columnsArray: any[];
  for (const key in json) {
    if (json.hasOwnProperty(key)) {
      columnsArray = Object.keys(json[key]);
    }
  }
  // Add Data and Conditional Formatting
  data.forEach((element: any) => {
    const eachRow = [];
    columnsArray.forEach((column) => {
      eachRow.push(element[column]);
    });
    const borderrow = worksheet.addRow(eachRow);
      borderrow.eachCell((cell) => {
        cell.border = { top: { style: 'thin' }, left: { style: 'thin' }, bottom: { style: 'thin' }, right: { style: 'thin' } };
        cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
      });
  });

  // Save Excel File
  // tslint:disable-next-line:no-shadowed-variable
  workbook.xlsx.writeBuffer().then((data: ArrayBuffer) => {
    const blob = new Blob([data], { type: EXCEL_TYPE });
    fs.saveAs(blob, excelFileName + EXCEL_EXTENSION);
  });
}

  public exportToExcelDossierBCCI(
      reportHeading: string,
      reportSubHeading: string,
      json: any[],
      footerData: any[],
      excelFileName: string,
      sheetName: string
    ) {
      const data = json;
      // create workbook and worksheet
      const workbook = new Workbook();
      workbook.creator = 'Snippet Coder';
      workbook.lastModifiedBy = 'SnippetCoder';
      workbook.created = new Date();
      workbook.modified = new Date();
      const worksheet = workbook.addWorksheet(sheetName);
  
      worksheet.getColumn('A').font = {name: 'Times New Roman', size: 12};
      worksheet.getColumn('B').font = {name: 'Times New Roman', size: 12};
      worksheet.getColumn('C').font = {name: 'Times New Roman', size: 12};
      worksheet.getColumn('D').font = {name: 'Times New Roman', size: 12};
      worksheet.getColumn('E').font = {name: 'Times New Roman', size: 12};
      worksheet.getColumn('F').font = {name: 'Times New Roman', size: 12};
      worksheet.getColumn('G').font = {name: 'Times New Roman', size: 12};
      worksheet.getColumn('H').font = {name: 'Times New Roman', size: 12};
      worksheet.getColumn('I').font = {name: 'Times New Roman', size: 12};
      worksheet.getColumn('J').font = {name: 'Times New Roman', size: 12};
      worksheet.getColumn('K').font = {name: 'Times New Roman', size: 12};
      worksheet.getColumn('L').font = {name: 'Times New Roman', size: 12};
      worksheet.getColumn('M').font = {name: 'Times New Roman', size: 12};
      worksheet.getColumn('N').font = {name: 'Times New Roman', size: 12};
      worksheet.getColumn('O').font = {name: 'Times New Roman', size: 12};
  
      // Add header row
  
      worksheet.mergeCells('B2:L4');
      worksheet.getCell('B2').value = reportHeading;
      worksheet.getCell('B2').alignment = {horizontal: 'center', vertical: 'middle'};
      worksheet.getCell('B2').font = {size: 15, bold: true, name: 'Times New Roman'};
  
      worksheet.mergeCells('B5:G5');
      if (this.language === 228) {
        worksheet.getCell('B5').value = '(Kèm theo Công văn số: 2362/STTTT-BCVT ngày 04/12/2024 của Sở TT&TT)';
      } else {
        worksheet.getCell('B5').value = '(Attached with Official Dispatch No. 2362/STTTT-BCVT dated December 4, 2024 of the Department of Information and Communications)';
      }
      worksheet.getCell('B5').alignment = {horizontal: 'center', vertical: 'middle'};
      worksheet.getCell('B5').font = {size: 12, name: 'Times New Roman'};
  
  
      // NỘI DUNG TABLE-HEADER
      worksheet.mergeCells('A10:A11');
      worksheet.mergeCells('B10:B11');
      worksheet.mergeCells('C10:D10');
      worksheet.mergeCells('E10:F10');
      worksheet.mergeCells('G10:G11');
      worksheet.mergeCells('H10:H11');
      worksheet.mergeCells('I10:I11');
      worksheet.mergeCells('J10:J11');
      worksheet.mergeCells('K10:K11');
      worksheet.mergeCells('L10:L11');
      worksheet.mergeCells('M10:M11');
      worksheet.mergeCells('N10:N11');
      worksheet.mergeCells('O10:O11');
  
      worksheet.getCell('A12').value = '(A)';
      worksheet.getCell('B12').value = '(B)';
      worksheet.getCell('C12').value = '(1)';
      worksheet.getCell('D12').value = '(2)';
      worksheet.getCell('E12').value = '(3)';
      worksheet.getCell('F12').value = '(4)';
      worksheet.getCell('G12').value = '(5)';
      worksheet.getCell('H12').value = '(6=1+5)';
      worksheet.getCell('I12').value = '(7=3+5)';
      worksheet.getCell('J12').value = '(8=2+6)';
      worksheet.getCell('K12').value = '(9=4+7)';
      worksheet.getCell('L12').value = '(10=6/8*100)';
      worksheet.getCell('M12').value = '(11=7/9*100)';
  
      if (this.language === 228) {
        worksheet.getCell('A10').value = 'STT';
        worksheet.getCell('B10').value = 'Tên TTCH có phát sinh hồ sơ tiếp nhận hồ sơ, trả kết quả qua dịch vụ BCCI';
        worksheet.getCell('C10').value = 'Số lượng hồ sơ đã thực hiện tiếp nhận';
        worksheet.getCell('E10').value = 'Số lượng hồ sơ đã thực hiện trả';
        worksheet.getCell('G10').value = 'Số lượng hồ sơ đồng thời sử dụng dịch vụ tiếp nhận và trả kết quả qua dịch vụ BCCI';
        worksheet.getCell('H10').value = 'Tổng số hồ sơ tiếp nhận qua dịch vụ BCCI';
        worksheet.getCell('I10').value = 'Tổng số kết quả giải quyết TTHC được trả qua dịch vụ BCCI';
        worksheet.getCell('J10').value = 'Tổng số hồ sơ tiếp nhận qua tất cả các hình thức';
        worksheet.getCell('K10').value = 'Tổng số kết quả giải quyết TTHC được trả qua tất cả các hình thức';
        worksheet.getCell('L10').value = 'Tỷ lệ hồ sơ TTHC được tiếp nhận qua dịch vụ BCCI (%)';
        worksheet.getCell('M10').value = 'Tỷ lệ kết quả giải quyết TTHC được trả qua dịch vụ BCCI (%)';
        
        
        worksheet.getCell('C11').value = 'Qua dịch vụ BCCI';
        worksheet.getCell('D11').value = 'Qua hình thức khác';
        worksheet.getCell('E11').value = 'Qua dịch vụ BCCI';
        worksheet.getCell('F11').value = 'Qua hình thức khác';
  
      } else {
        worksheet.getCell('A10').value = 'STT';
        worksheet.getCell('B10').value = 'Name procedure';
        worksheet.getCell('C10').value = 'Number of applications received';
        worksheet.getCell('E10').value = 'Number of records returned results';
        worksheet.getCell('G10').value = 'Number of records received and returned via BCCI service';
        worksheet.getCell('H10').value = 'Number of records received through BCCI service';
        worksheet.getCell('I10').value = 'Number of records returned through BCCI service';
        worksheet.getCell('J10').value = 'Number of applications received through all forms';
        worksheet.getCell('K10').value = 'Number of records returned through all forms';
        worksheet.getCell('L10').value = 'Percentage of applications received through BCCI service';
        worksheet.getCell('M10').value = 'Percentage of records returned results via BCCI service';
        
        
        worksheet.getCell('C11').value = 'Via BCCI service';
        worksheet.getCell('D11').value = 'Through other forms';
        worksheet.getCell('E11').value = 'Via BCCI service';
        worksheet.getCell('F11').value = 'Through other forms';
      }
  
      worksheet.getColumn('B').width = 45;
      worksheet.getColumn('C').width = 17;
      worksheet.getColumn('E').width = 10;
      worksheet.getColumn('G').width = 17;
      worksheet.getColumn('K').width = 17;
  
      worksheet.getRow(2).height = 27;
      worksheet.getColumn('C').alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
      worksheet.getColumn('D').alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
      worksheet.getColumn('E').alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
      worksheet.getColumn('F').alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
      worksheet.getColumn('G').alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
      worksheet.getColumn('H').alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
      worksheet.getColumn('I').alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
      worksheet.getColumn('J').alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
      worksheet.getColumn('K').alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
      worksheet.getColumn('L').alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
      worksheet.getColumn('M').alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
      worksheet.getColumn('N').alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
      worksheet.getColumn('O').alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
      worksheet.getColumn('P').alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
      worksheet.getColumn('Q').alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
  
      worksheet.getCell('H1').style.alignment = {horizontal: 'left', vertical: 'middle', wrapText: true};
      worksheet.getCell('H2').style.alignment = {horizontal: 'left', vertical: 'middle', wrapText: true};
      worksheet.getCell('H5').style.alignment = {horizontal: 'left', vertical: 'middle', wrapText: true};
      worksheet.getCell('H6').style.alignment = {horizontal: 'left', vertical: 'middle', wrapText: true};
  
      worksheet.properties.outlineLevelCol = 2;
      worksheet.properties.defaultRowHeight = 15;
  
      let i = 10;
      const j = 12;
      for (i; i <= j; i++) {
        let k = 1;
        const l = 13;
        for (k; k <= l; k++) {
          worksheet.findCell(i, k).alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
          worksheet.findCell(i, k).fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: {argb: 'C0C0C0C0'},
            bgColor: {argb: 'FF0000FF'}
          };
          worksheet.findCell(i, k).border = {top: {style: 'thin'}, left: {style: 'thin'}, bottom: {style: 'thin'}, right: {style: 'thin'}};
          worksheet.findCell(i, k).font = {size: 12, bold: true, name: 'Times New Roman'};
        }
      }
  
      // get all columns from JSON
      let columnsArray: any[];
      for (const key in json) {
        if (json.hasOwnProperty(key)) {
          columnsArray = Object.keys(json[key]);
        }
      }
  
      // Add Data and Conditional Formatting
      data.forEach((element: any) => {
        const eachRow = [];
        columnsArray.forEach((column) => {
          eachRow.push(element[column]);
        });
        // eachRow.splice(0, 1);
        const borderrow = worksheet.addRow(eachRow);
        borderrow.eachCell((cell) => {
          cell.border = {top: {style: 'thin'}, left: {style: 'thin'}, bottom: {style: 'thin'}, right: {style: 'thin'}};
          cell.alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
        });
      });
  
      const dataLength = data.length;
      if (dataLength > 0) {
        for (i = 0; i < dataLength; i++) {
          worksheet.getCell('B' + (13 + i)).alignment = {horizontal: 'left', vertical: 'middle', wrapText: true};
        }
      }
  
      // footer data row
      if (footerData != null) {
        footerData.forEach((element: any) => {
          const eachRow = [];
          element.forEach((val: any) => {
            eachRow.push(val);
          });
          const footerRow = worksheet.addRow(eachRow);
          const cellMerge = 'A' + footerRow.number + ':B' + footerRow.number;
          worksheet.mergeCells(cellMerge);
          footerRow.eachCell((cell) => {
            cell.font = {size: 13, bold: true, name: 'Times New Roman'};
            cell.border = {top: {style: 'thin'}, left: {style: 'thin'}, bottom: {style: 'thin'}, right: {style: 'thin'}};
            cell.alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
          });
        });
      }
  
      // Save Excel File
      workbook.xlsx.writeBuffer().then((dataBuffer: ArrayBuffer) => {
        const blob = new Blob([dataBuffer], {type: EXCEL_TYPE});
        fs.saveAs(blob, excelFileName + EXCEL_EXTENSION);
      });
    }
}
