import { Component, OnInit, Inject } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { KeycloakService } from 'keycloak-angular';
import { DossierService } from 'src/app/data/service/dossier/dossier.service';
import { UserService } from 'src/app/data/service/user.service';
import * as ClassicEditor from '@ckeditor/ckeditor5-build-classic/build/ckeditor';
import '@ckeditor/ckeditor5-build-classic/build/translations/vi';
import { EnvService } from 'src/app/core/service/env.service';
import { SnackbarService } from 'src/app/data/service/snackbar/snackbar.service';
import { DatePipe } from '@angular/common';
import { NotificationService } from 'src/app/data/service/notification.service';
import { DeploymentService } from 'src/app/data/service/deployment.service';
import * as tUtils from 'src/app/data/service/thoai.service';
import { ProcedureService } from 'src/app/data/service/procedure/procedure.service';
import { take } from 'rxjs/operators';
import { ProcessService } from 'src/app/data/service/process/process.service';
import { HomeService } from 'src/app/data/service/home/<USER>';
import { NotifyQNIService } from "shared/components/check-send-notify-qni/check-send-notify-qni.service";
import { PadmanService } from 'src/app/data/service/svc-padman/padman.service';
import { FormControl, FormGroup } from '@angular/forms';
import { AdapterService } from 'src/app/data/service/adapter/adapter.service';
import { LogmanService } from 'data/service/logman/logman.service';
import { AgencyService } from 'src/app/data/service/basedata/agency.service';

@Component({
  selector: 'app-cancel-multi-dossier',
  templateUrl: './cancel-multi-dossier.component.html',
  styleUrls: ['./cancel-multi-dossier.component.scss']
})
export class CancelMultiDossierComponent implements OnInit {

  config = this.envService.getConfig();
  env: any = this.deploymentService.getAppDeployment()?.env;
  selectedLang = localStorage.getItem('language') || 'vi';
  selectedLangId = Number(localStorage.getItem('languageId')) || 228;
  dossierId: string;
  commentContent = '';
  userName: string;
  accountId: string;
  dossierCode: string;
  isCKMaxlenght = false;
  dossierDetail: any;
  oldstatus = '';
  officers = [];
  agencyId: any;
  getApprovalAgency = '';
  listDossierCode: [];
  listDossierId: [];
  listCode = '';
  public Editor = ClassicEditor;
  commentConfig = {
    placeholder: 'Nhập nội dung...',
    language: 'vi',
    removePlugins: ['MediaEmbed', 'ImageUpload'],
    toolbarLocation: 'bottom'
  };
  dossierTaskStatus = { id: '', name: [] };
  dossierMenuTaskRemind = { id: '', name: [] };

  // ================================================= Upload file
  uploaded: boolean;
  blankVal: any;
  uploadedImage = null;
  files = [];
  urls = [];
  fileNames = [];
  fileNamesFull = [];
  listProcedureProcess = [];

  currentTask: any;

  isFieldArrayInvalid = false;
  acceptFileExtension = this.config.acceptFileExtension;
  acceptFileType = this.config.acceptFileType;
  maxFileSize = this.env?.maxFileSize || this.config.maxFileSize;
  isBoTNMTQNM = this.env?.OS_QNM?.boTNMT === true ? this.env?.OS_QNM?.boTNMT : false;
  isBoTNMTHCM = this.deploymentService.env.OS_HCM.maTTBoTNMT;
  isBoTNMTGLI = this.deploymentService.env.OS_GLI.boTNMT;
  isSyncConstructKTM = this.deploymentService.env?.OS_KTM?.isSyncConstructKTM;
  constructConfigId = this.deploymentService.env?.OS_KTM?.constructConfigId;

  // IGATESUPP-44607
  allowNextStepWaitingForApproval = this.deploymentService.env.OS_HCM.allowNextStepWaitingForApproval ? this.deploymentService.env.OS_HCM.allowNextStepWaitingForApproval : false;
  enableApproveStopProcessingCheckBox = false;
  dossierTaskStatusWaitingForApproval = { id: '', name: [] };
  dossierMenuTaskRemindWaitingForApproval = { id: '', name: [] };

  requestCancelMsg = {
    vi: 'Đã gửi yêu cầu dừng xử lý!',
    en: 'Sent request for cancel processing!'
  };
  cancelMsg = {
    vi: 'Đã dừng xử lý!',
    en: 'Cancelled processing!'
  };
  requestCancelComment = {
    vi: 'Yêu cầu dừng xử lý: ',
    en: 'Request for cancel dossier: '
  };
  cancelComment = {
    vi: 'Dừng xử lý: ',
    en: 'Cancel processing: '
  };
  title = '';
  //Thu tuc co cau hinh dong bo BXD-LGSP Tan Tan
  isSyncDBNLGSPTanDanBXD = 0;
  statusName = "";
  fileTemplate = "";

  notifyQNI = this.deploymentService.getAppDeployment()?.notifyqni?.enable;

  totalCost = '';

  requireAttachmentWhenCancel = true;

  ckeditorMaxLengthNotification = "";
  ckeditorMaxLength = this.deploymentService.env.OS_HCM.ckeditorMaxLength;
  codeMap = "";

  updateForm = new FormGroup({
    approvalAgencyId: new FormControl(''),
  });
  approvalAgency = [];
  visibleApprovalAgency = this.deploymentService.env.visibleApprovalAgency;
  setOnlyApprovalAgency = this.deploymentService.env?.OS_QNI?.setOnlyApprovalAgency;
  isSmsQNM = this.env?.OS_QNM?.isSmsQNM ? true : false;

  isSyncConstructHPG = this.deploymentService?.syncConstructHPG?.isSyncConstructHPG;
  disableButton = false;
  constructor(
    private userService: UserService,
    private envService: EnvService,
    private keycloakService: KeycloakService,
    public dialogRef: MatDialogRef<CancelMultiDossierComponent>,
    @Inject(MAT_DIALOG_DATA) public data: CancelMultiDossierDialogModel,
    private snackbarService: SnackbarService,
    private datePipe: DatePipe,
    private notiService: NotificationService,
    private deploymentService: DeploymentService,
    private procedureService: ProcedureService,
    private dossierService: DossierService,
    private processService: ProcessService,
    private homeService: HomeService,
    private notifyQNIService: NotifyQNIService,
    private padmanService: PadmanService,
    private adapterService: AdapterService,
    private logmanService: LogmanService,
    private agencyService: AgencyService
  ) {
    this.dossierId = '';
    this.dossierCode = '';
    this.listDossierCode = data.dossierCode;
    this.listDossierId = data.dossierId;
  }

  ngOnInit(): void {
    this.getUserAccount();
    this.getDossierTaskStatus();
    this.getDossierMenuTaskRemind();
    this.setStaticData();
    this.setEnvVariable();
    this.listDossierCode.forEach(element => {
      this.listCode += element + ', ';
    });

    if (this.selectedLang === 'vi') {
      this.ckeditorMaxLengthNotification = "Nội dung không quá 500 ký tự!";
      if (this.ckeditorMaxLength > 0)
        this.ckeditorMaxLengthNotification = "Nội dung không quá " + this.ckeditorMaxLength + " ký tự!";
    } else if (this.selectedLang === 'en') {
      this.ckeditorMaxLengthNotification = "Content must not exceed 500 characters!";
      if (this.ckeditorMaxLength > 0)
        this.ckeditorMaxLengthNotification = "Content must not exceed " + this.ckeditorMaxLength + " characters!";
    }
  }

  setEnvVariable() {
    this.fileTemplate = !!this.env?.fileTemplate?.cancelProcessing ? this.env?.fileTemplate?.cancelProcessing : "";
    this.requireAttachmentWhenCancel = this.env?.OS_BDG?.isRequiredUploadFileBDG?.cancel != undefined && this.env?.OS_BDG?.isRequiredUploadFileBDG?.cancel != null ? this.env?.OS_BDG?.isRequiredUploadFileBDG?.cancel : true;
  }

  downloadTemplate() {
    window.open(this.fileTemplate, '_self');
  }

  setStaticData() {
    if (this.env?.enableApprovalOfLeadership == 1) {
      this.title = this.selectedLang == 'en' ? 'Request for cancel processing' : 'Yêu cầu dừng xử lý hồ sơ';
    } else {
      this.title = this.selectedLang == 'en' ? 'Cancel processing' : 'Dừng xử lý hồ sơ'
    }
  }

  // IGATESUPP-51500 - Đánh giá lại chức năng phê duyệt hồ sơ - 19.06.2023
  postOnlyApprovalAgencyOfDossier(code) {

    const formObj = this.updateForm.getRawValue();
    if (!!formObj.approvalAgencyId && formObj.approvalAgencyId != '') {
      this.getApprovalAgency = formObj.approvalAgencyId;
    } else {
      return;
    }

    const body = {
      id: this.getApprovalAgency
    };
    const requestBody = JSON.stringify(body, null, 2);
    this.dossierService.postOnlyApprovalAgencyOfDossier(requestBody, code).toPromise();
  }

  getUserAccount() {
    this.keycloakService.loadUserProfile().then(user => {
      // tslint:disable-next-line: no-string-literal
      this.accountId = user['attributes'].user_id[0];
      this.userService.getUserInfo(this.accountId).subscribe(data => {
        this.userName = data.fullname;
      });
    });
  }
  getDossierTaskStatus() {
    const tagId = this.env?.enableApprovalOfLeadership == 1 ? this.deploymentService.env.dossierTaskStatus.waitingForApprovalToCancel.id : this.deploymentService.env.dossierTaskStatus.cancelDossier.id;
    this.dossierService.getAgencyTag(tagId).subscribe(rs => {
      this.dossierTaskStatus.id = rs.id;
      this.dossierTaskStatus.name = rs.trans;
    }, err => {
      console.log(err);
    });
  }
  getDossierMenuTaskRemind() {
    const tagId = this.env?.enableApprovalOfLeadership == 1 ? this.deploymentService.env.dossierMenuTaskRemind.waitingForApprovalToCancel.id : this.deploymentService.env.dossierMenuTaskRemind.cancelDossier.id;
    this.dossierService.getAgencyTag(tagId).subscribe(rs => {
      this.dossierMenuTaskRemind.id = rs.id;
      this.dossierMenuTaskRemind.name = rs.trans;
      this.statusName = rs?.trans.filter(t => t.languageId === Number(this.selectedLangId))?.name;
    }, err => {
      console.log(err);
    });
  }


  getProcedureDetail(procedureId) {
    this.procedureService.getProcedureDetail(procedureId).subscribe(async data => {
      this.isSyncDBNLGSPTanDanBXD = data?.isSyncDBNLGSPTanDanBXD ? Number(data?.isSyncDBNLGSPTanDanBXD) : 0;
      this.enableApproveStopProcessingCheckBox = data?.extendHCM?.enableApproveStopProcessingCheckBox ? data?.extendHCM?.enableApproveStopProcessingCheckBox : false;
    }, err => {
      console.log(err);
    });
  }



  postHistory(dossierDetail) {
    let newStatus = '';
    if (this.dossierTaskStatus.name.length > 0) {
      newStatus = this.dossierTaskStatus.name[0].name;
      this.dossierTaskStatus.name.forEach(element => {
        if (element.languageId === Number(localStorage.getItem('languageId'))) {
          newStatus = element.name;
        }
      });
    }
    const content = {
      groupId: 1,
      itemId: dossierDetail?.id,
      user: {
        id: this.accountId,
        name: this.userName
      },
      type: 3,
      deploymentId: this.config.deploymentId,
      action: [
        {
          fieldNameRbk: 'lang.word.status',
          originalValue: dossierDetail.dossierTaskStatus.name,
          newValue: newStatus
        }
      ]
    };
    this.dossierService.postHistory(JSON.stringify(content, null, 2)).subscribe(data => {

    });
  }

  GetListUserByPermission(agency) {
    return new Promise<void>((resolve) => {
      this.processService.getApprovalAgency(agency.id).pipe(take(1)).subscribe(async data => {
        console.log('data', data);
        if (!!data && data.content.length !== 0) {
          this.getApprovalAgency = data.content[0].approvalAgency.id;
        }
        else {
          if (!!agency.parent?.id) {
            await this.GetListUserByPermissionParent(agency.parent?.id);
          }
        }
        if (this.getApprovalAgency == '') {
          this.getApprovalAgency = agency.id;
        }
        const formObj = this.updateForm.getRawValue();
        if (!!formObj.approvalAgencyId && formObj.approvalAgencyId != '') {
          this.getApprovalAgency = formObj.approvalAgencyId;
        }
        let permission = "cancelDossierApproval";
        this.processService.getListUserByPermissionCode(this.getApprovalAgency, permission).pipe(take(1)).subscribe(data2 => {
          this.officers = data2;
          resolve();
        }, (err) => {
          resolve();
        });
      });
    });
  }

  GetListUserByPermissionParent(agencyId) {
    return new Promise<void>((resolve) => {
      this.processService.getApprovalAgency(agencyId).pipe(take(1)).subscribe(data => {
        console.log('data', data);
        if (!!data && data.content.length !== 0) {
          this.getApprovalAgency = data.content[0].approvalAgency.id;
        }
        // if (this.getApprovalAgency == '') {
        //   this.getApprovalAgency = agencyId;
        // }
        resolve();
      }, (err) => {
        resolve();
      });
    });
  }
  // GetListUserByPermission(agencyId) {
  //   return new Promise<void>((resolve) => {
  //     this.processService.getApprovalAgency(agencyId).pipe(take(1)).subscribe(data => {
  //       console.log('data', data);
  //       if (!!data && data.content.length !== 0) {
  //         this.getApprovalAgency = data.content[0].approvalAgency.id;
  //       }
  //       if (this.getApprovalAgency == '') {
  //         this.getApprovalAgency = agencyId;
  //       }
  //       let permission = "cancelDossierApproval";
  //       this.processService.getListUserByPermissionCode(this.getApprovalAgency, permission).pipe(take(1)).subscribe(data => {
  //         this.officers = data;
  //         resolve();
  //       }, (err) => {
  //         resolve();
  //       });
  //     });
  //   });
  // }

  async setNotify(dossierDetail) {
    const dossierFee = await this.padmanService.getDossierFee(dossierDetail.id).toPromise();
    let totalCost = 0;
    if (dossierFee && dossierFee.length > 0) {
      let totalCostValue = 0;
      let totalPaidValue = 0;
      dossierFee.forEach(element => {
        totalCostValue += element.quantity * element.amount;
        totalPaidValue += element.paid;
      });
      const monetaryUnit = dossierFee[0]?.monetaryUnit ? dossierFee[0].monetaryUnit : 'VNĐ';
      let total = totalCostValue - totalPaidValue;
      this.totalCost = total > 0 ? Number((total).toFixed(1)).toLocaleString() + ' ' + monetaryUnit : '';
    }
    const procedureDetail = await this.procedureService.getProcedureDetail(dossierDetail?.procedure?.id).toPromise();
    let isSyncDBNLGSPTanDanBXD = procedureDetail?.isSyncDBNLGSPTanDanBXD ? Number(procedureDetail?.isSyncDBNLGSPTanDanBXD) : 0;
    let enableApproveStopProcessingCheckBox = procedureDetail?.extendHCM?.enableApproveStopProcessingCheckBox ? procedureDetail?.extendHCM?.enableApproveStopProcessingCheckBox : false;
    let agencyId = dossierDetail?.agency?.id;
    let approvalAgency = [];
    let currentTask;
      if (!!dossierDetail.currentTask && dossierDetail.currentTask.length > 0){
        currentTask = dossierDetail.currentTask[0];
        approvalAgency = this.getApprovaledAgency(this.currentTask.candidateGroup[0]);
      }
      if(!!this.notifyQNI){
        this.notifyQNIService.checkSendSubject.next(
          {
            id: dossierDetail?.procedureProcessDefinition?.id,
            phone: dossierDetail?.applicant?.data?.phoneNumber,
            email: dossierDetail?.applicant?.data?.email,
            currentTask: currentTask,
            contentParams: {
              parameters: {
                sector: !!dossierDetail?.procedure?.sector?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name ? dossierDetail?.procedure?.sector?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name : '',
                procedure: !!dossierDetail?.procedure?.translate?.find(agency => agency.languageId === Number(this.selectedLangId))?.name ? dossierDetail?.procedure?.translate?.find(agency => agency.languageId === Number(this.selectedLangId))?.name : '',
                agency: !!dossierDetail?.agency?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name ? dossierDetail?.agency?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name : '',
                agencyId: dossierDetail?.agency?.id,
                applicantFullname: dossierDetail?.applicant?.data?.fullname,
                officerFullname: '',
                subsystem: !!this.env?.subsystem?.name ? this.env?.subsystem?.name : "Hệ thống Một cửa điện tử",
                dossierCode: dossierDetail?.nationCode ? dossierDetail?.nationCode : dossierDetail?.code,
                nextTask: !!this.env?.notify?.additionalRequirement?.nextTask ? this.env?.notify?.additionalRequirement?.nextTask : 'Yêu cầu dừng xử lý hồ sơ',
                dossierSearchPadsvcUrl: (!!this.env?.domain?.padsvc ? this.env?.domain?.padsvc : "http://padsvctest.vncitizens.vn/vi/") + "dossier/public",
                dossierDetailPadsvcUrl: (!!this.env?.domain?.padsvc ? this.env?.domain?.padsvc : "http://padsvctest.vncitizens.vn/vi/") + "dossier/detail/" + dossierDetail?.id,
                dossierSearchUrl: (!!this.env?.domain?.onegate ? this.env?.domain?.onegate : "http://padsvctest.vncitizens.vn/vi/") + "dossier/search/",
                dossierDetailUrl: (!!this.env?.domain?.onegate ? this.env?.domain?.onegate : "http://padsvctest.vncitizens.vn/vi/") + "dossier/search/" + dossierDetail?.id,
                returnMethod: !!dossierDetail?.dossierReceivingKind?.name?.find(recieve => recieve.languageId === Number(this.selectedLangId))?.name ? dossierDetail?.dossierReceivingKind?.name?.find(recieve => recieve.languageId === Number(this.selectedLangId))?.name : '',
                receivingDate: '',
                appointmentDate: '',
                dossierFee: totalCost,
                reason: ''
              }
            }
          }
        );
      } else if(this.isSmsQNM) {
        const agencies = this.agencyService.getAgencies();
        const extend = {
          dossier:{
            id: dossierDetail?.id,
            code: dossierDetail?.code,
          },
          agencies: agencies
        };
        this.notiService.checkSendSubject.next(
          {
            id: dossierDetail?.procedureProcessDefinition?.id,
            phone: dossierDetail?.applicant?.data?.phoneNumber,
            email: dossierDetail?.applicant?.data?.email,
            currentTask: currentTask,
            contentParams: {
              parameters: {
                sector: !!dossierDetail?.procedure?.sector?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name ? dossierDetail?.procedure?.sector?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name : '',
                procedure: !!dossierDetail?.procedure?.translate?.find(agency => agency.languageId === Number(this.selectedLangId))?.name ? dossierDetail?.procedure?.translate?.find(agency => agency.languageId === Number(this.selectedLangId))?.name : '',
                agency: !!dossierDetail?.agency?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name ? dossierDetail?.agency?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name : '',
                agencyId: dossierDetail?.agency?.id,
                applicantFullname: dossierDetail?.applicant?.data?.fullname,
                officerFullname: '',
                subsystem: !!this.env?.subsystem?.name ? this.env?.subsystem?.name : "Hệ thống Một cửa điện tử",
                dossierCode: dossierDetail?.code,
                nextTask: !!this.env?.notify?.additionalRequirement?.nextTask ? this.env?.notify?.additionalRequirement?.nextTask : 'Yêu cầu dừng xử lý hồ sơ',
                dossierSearchPadsvcUrl: (!!this.env?.domain?.padsvc ? this.env?.domain?.padsvc : "http://padsvctest.vncitizens.vn/vi/") + "dossier/public",
                dossierDetailPadsvcUrl: (!!this.env?.domain?.padsvc ? this.env?.domain?.padsvc : "http://padsvctest.vncitizens.vn/vi/") + "dossier/detail/" + dossierDetail?.id,
                dossierSearchUrl: (!!this.env?.domain?.onegate ? this.env?.domain?.onegate : "http://padsvctest.vncitizens.vn/vi/") + "dossier/search/",
                dossierDetailUrl: (!!this.env?.domain?.onegate ? this.env?.domain?.onegate : "http://padsvctest.vncitizens.vn/vi/") + "dossier/search/" + dossierDetail?.id,
                returnMethod: !!dossierDetail?.dossierReceivingKind?.name?.find(recieve => recieve.languageId === Number(this.selectedLangId))?.name ? dossierDetail?.dossierReceivingKind?.name?.find(recieve => recieve.languageId === Number(this.selectedLangId))?.name : '',
                receivingDate: '',
                appointmentDate: '',
                dossierFee: totalCost,
                reason: '',
                extend: extend
              }
            }
          }
        );
      } else {
        this.notiService.checkSendSubject.next(
          {
            id: dossierDetail?.procedureProcessDefinition?.id,
            phone: dossierDetail?.applicant?.data?.phoneNumber,
            email: dossierDetail?.applicant?.data?.email,
            currentTask: this.currentTask,
            contentParams: {
              parameters: {
                sector: !!dossierDetail?.procedure?.sector?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name ? dossierDetail?.procedure?.sector?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name : '',
                procedure: !!dossierDetail?.procedure?.translate?.find(agency => agency.languageId === Number(this.selectedLangId))?.name ? dossierDetail?.procedure?.translate?.find(agency => agency.languageId === Number(this.selectedLangId))?.name : '',
                agency: !!dossierDetail?.agency?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name ? dossierDetail?.agency?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name : '',
                agencyId: dossierDetail?.agency?.id,
                applicantFullname: dossierDetail?.applicant?.data?.fullname,
                officerFullname: '',
                subsystem: !!this.env?.subsystem?.name ? this.env?.subsystem?.name : "Hệ thống Một cửa điện tử",
                dossierCode: dossierDetail?.code,
                nextTask: !!this.env?.notify?.additionalRequirement?.nextTask ? this.env?.notify?.additionalRequirement?.nextTask : 'Yêu cầu dừng xử lý hồ sơ',
                dossierSearchPadsvcUrl: (!!this.env?.domain?.padsvc ? this.env?.domain?.padsvc : "http://padsvctest.vncitizens.vn/vi/") + "dossier/public",
                dossierDetailPadsvcUrl: (!!this.env?.domain?.padsvc ? this.env?.domain?.padsvc : "http://padsvctest.vncitizens.vn/vi/") + "dossier/detail/" + dossierDetail?.id,
                dossierSearchUrl: (!!this.env?.domain?.onegate ? this.env?.domain?.onegate : "http://padsvctest.vncitizens.vn/vi/") + "dossier/search/",
                dossierDetailUrl: (!!this.env?.domain?.onegate ? this.env?.domain?.onegate : "http://padsvctest.vncitizens.vn/vi/") + "dossier/search/" + dossierDetail?.id,
                returnMethod: !!dossierDetail?.dossierReceivingKind?.name?.find(recieve => recieve.languageId === Number(this.selectedLangId))?.name ? dossierDetail?.dossierReceivingKind?.name?.find(recieve => recieve.languageId === Number(this.selectedLangId))?.name : '',
                receivingDate: '',
                appointmentDate: '',
                dossierFee: totalCost,
                reason: ''
              }
            }
          }
        );
      }
      // if (this.dossierDetail.dossierTaskStatus === undefined && this.dossierDetail.dossierTaskStatus === null) {
      //   // this.putDossierStatus();
      // }
      // else {
      //   this.oldstatus = this.dossierDetail.dossierTaskStatus.name;
      // }
  }

  async onConfirm() {
    this.disableButton = true;
    let listDosserCancelFail = [];
    if (this.env?.enableApprovalOfLeadership != 2 && this.officers.length == 0) {
      this.disableButton = false;
      this.homeService.error('Cán bộ phê duyệt hồ sơ của đơn vị chưa được cấu hình. \r\n Vui lòng cấu hình cán bộ phê duyệt để tiếp tục!',
        'Approval officer has not yet been configured \r\n Please configure the approval officer to continue!');
      const msgObj = {
        vi: 'Cán bộ phê duyệt hồ sơ của đơn vị chưa được cấu hình',
        en: 'Approval officer has not yet been configured'
      };
      this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
      return
    }
    let ckeckFile = false;
    if (this.files.length <= 0) {
      this.disableButton = false;
      if (this.requireAttachmentWhenCancel) {
        const msgObj = {
          vi: 'Vui lòng chọn tệp văn bản',
          en: 'Please select the required text file!'
        };
        this.snackbarService.openSnackBar(2, '', msgObj[this.selectedLang], 'warning_notification', this.config.expiredTime);
        return;
      } else {
        ckeckFile = true;
      }
    }
    else {
      await this.uploadMultiFile(this.files, this.accountId);
      ckeckFile = true;
    }
    if (this.commentContent.trim() === '') {
      this.disableButton = false;
      const msgObj = {
        vi: 'Vui lòng nhập ý kiến xử lý!',
        en: 'Please enter a handling comments!'
      };
      this.snackbarService.openSnackBar(2, '', msgObj[this.selectedLang], 'warning_notification', this.config.expiredTime);
      return;
    }
    if (this.isCKMaxlenght) {
      this.disableButton = false;
      let msgObj;
      if (this.ckeditorMaxLength > 0) {
        msgObj = {
          vi: 'Nội dung không quá ' + this.ckeditorMaxLength + ' ký tự!',
          en: 'Content must not exceed ' + this.ckeditorMaxLength + ' characters!'
        };
      } else {
        msgObj = {
          vi: 'Nội dung không quá 500 ký tự!',
          en: 'Content must not exceed 500 characters!'
        };
      }
      this.snackbarService.openSnackBar(2, '', msgObj[this.selectedLang], 'warning_notification', this.config.expiredTime);
      return;
    }
    this.listDossierId.forEach(async dossierId => {
      const dossierDetail = await this.dossierService.getDossierDetail(dossierId).toPromise();
      // await this.GetListUserByPermission(this.agencyId);
      if (!!dossierDetail.currentTask[0] && !!dossierDetail.currentTask[0].candidateGroup[0] && !!dossierDetail.currentTask[0].candidateGroup[0].id) {
        await this.GetListUserByPermission(dossierDetail.currentTask[0].candidateGroup[0]);
      } else {
        await this.GetListUserByPermission({ id: dossierDetail?.agency?.id });
      }
      // const formObj = this.updateForm.getRawValue();
      // if (this.visibleApprovalAgency && (!this.getApprovalAgency || this.getApprovalAgency === '')) {
      //   const msgObj = {
      //     vi: 'Đơn vị phê duyệt hồ sơ chưa được chọn!',
      //     en: 'The unit that approves the application has not been selected!'
      //   };
      //   this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
      //   return
      // }

      if (!this.isCKMaxlenght && this.commentContent.trim() !== '' && ckeckFile) {
        const requestBodyObj = {
          dossierStatus: this.env?.enableApprovalOfLeadership == 1 ? 11 : 12,
          comment: '',
          dossierTaskStatus: this.dossierTaskStatus,
          dossierMenuTaskRemind: this.dossierMenuTaskRemind,
          isAttachmentRequired: this.requireAttachmentWhenCancel,
        };
        if (this.commentContent.trim() !== '') {
          this.postComment(this.commentContent.trim(), dossierId);
          requestBodyObj.comment = this.commentContent.trim();
        } else {
          const msgObj = {
            vi: 'Yêu cầu không xử lý hồ sơ <b>' + dossierDetail.code + '</b>',
            en: 'Request for cancel dossier <b>' + dossierDetail.code + '</b>!'
          };
          this.postComment(msgObj[this.selectedLang], dossierId);
          requestBodyObj.comment = msgObj[this.selectedLang];
        }

        if (this.allowNextStepWaitingForApproval && this.enableApproveStopProcessingCheckBox) {
          requestBodyObj.dossierStatus = 11;

          await this.setdossierTaskStatus();
          await this.setdossierTaskStatusRemind();

          requestBodyObj.dossierTaskStatus.id = this.dossierTaskStatusWaitingForApproval.id;
          requestBodyObj.dossierTaskStatus.name = this.dossierTaskStatusWaitingForApproval.name;
          requestBodyObj.dossierMenuTaskRemind.id = this.dossierMenuTaskRemindWaitingForApproval.id;
          requestBodyObj.dossierMenuTaskRemind.name = this.dossierMenuTaskRemindWaitingForApproval.name;
        }

        const requestBody = JSON.stringify(requestBodyObj, null, 2);

        this.dossierService.putDossierStatusWithComment(dossierId, requestBody).subscribe(async data => {
          if (data.affectedRows === 1) {
            this.postHistory(dossierDetail);
            if (!!this.notifyQNI) {
              this.notifyQNIService.confirmSendSubject.next({
                confirm: true,
                renewContent: false,
              });
            } else {
              this.notiService.confirmSendSubject.next({
                confirm: true,
                renewContent: false,
              });
            }
            let msgObj = this.cancelMsg;
            if (this.env?.enableApprovalOfLeadership == 1 || (this.allowNextStepWaitingForApproval && this.enableApproveStopProcessingCheckBox)) {
              msgObj = this.requestCancelMsg;
              const data = {
                type: 11,
                date: tUtils.newDate(),
                attachment: this.uploadedImage
              }
              this.dossierService.updateApprovalData(dossierId, data).subscribe(res => { });
            }
            if (this.env?.enableApprovalOfLeadership === 0 && this.isSyncDBNLGSPTanDanBXD === 1) {
              this.syncPostReceiveInforFileFromLocal(dossierId);
            }
            if (this.isSyncConstructKTM && this.isSyncDBNLGSPTanDanBXD === 1) {
              this.syncDossierKTMToBXD(dossierId);
            }
            if (this.isSyncConstructHPG && this.isSyncDBNLGSPTanDanBXD === 1) {
              this.syncDossierHPGToBXD(dossierId);
            }
            // this.snackbarService.openSnackBar(1, msgObj[this.selectedLang], '', 'success_notification', this.config.expiredTime);
            if (this.isBoTNMTQNM || this.isBoTNMTHCM || this.isBoTNMTGLI) {
              this.dossierService.updateDossierBoTNMT(dossierId).subscribe(test => { });
            }
            // IGATESUPP-51500 - Đánh giá lại chức năng phê duyệt hồ sơ - 19.06.2023
            if (this.setOnlyApprovalAgency && this.approvalAgency.length > 1) {
              this.postOnlyApprovalAgencyOfDossier(dossierDetail.code);
            }
            // await this.setNotify(dossierDetail);
            this.dialogRef.close(true);
            const dataLog = {
              dossierId: dossierId,
              code: dossierDetail.code,
              body: requestBody
            };
            this.logmanService.postUserEventsLog('cancelDossier', dataLog).subscribe();
          } else {
            listDosserCancelFail.push(dossierDetail.code);
            // const msgObj = {
            //   vi: 'Yêu cầu dừng xử lý không thành công!',
            //   en: 'Failed to request for cancel processing!'
            // };
            // this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
          }
        }, err => {
          listDosserCancelFail.push(dossierDetail.code);
          // const msgObj = {
          //   vi: 'Yêu cầu dừng xử lý không thành công!',
          //   en: 'Failed to request for cancel processing!'
          // };
          // this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
        });
      }
    });
    if (listDosserCancelFail.length > 0) {
      const msgObj = {
        vi: 'Yêu cầu dừng xử lý không thành công!!! Hồ sơ xảy ra lỗi: ' + listDosserCancelFail.toString(),
        en: 'Failed to request for cancel processing!!! Dossier Fail: ' + listDosserCancelFail.toString()
      };
      this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
      this.dialogRef.close(false);
    } else {
      const msgObj = {
        vi: 'Dừng xử lý thành công nhiều hồ sơ!',
        en: 'Successfully deleted many records!'
      };
      this.snackbarService.openSnackBar(1, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
    }
  }

  onDismiss() {
    this.dialogRef.close();
  }

  postComment(commentContent, dossierId) {
    const msgObj = this.env?.enableApprovalOfLeadership == 1 ? this.requestCancelComment : this.cancelComment;
    const content = {
      groupId: 2,
      itemId: dossierId,
      user: {
        id: this.accountId,
        fullname: this.userName
      },
      content: msgObj[this.selectedLang] + commentContent.trim(),
      file: this.uploadedImage
    };
    const requestBody = JSON.stringify(content, null, 2);
    this.dossierService.postComment(requestBody).subscribe(data => { });
  }

  onCommentEditorChange(event) {
    this.commentContent = event.editor.getData();
    if (this.ckeditorMaxLength > 0) {
      if (event.editor.getData().trim().length > this.ckeditorMaxLength) {
        this.isCKMaxlenght = true;
      } else {
        this.isCKMaxlenght = false;
      }
    } else if (event.editor.getData().trim().length > 500) {
      this.isCKMaxlenght = true;
    } else {
      this.isCKMaxlenght = false;
    }
  }

  uploadMultiFile(file, accountId) {
    return new Promise((resolve) => {
      this.procedureService.uploadMultiFile(file, accountId).subscribe(data => {
        this.uploadedImage = data;
        // this.formToJSON();
        resolve(true);
      }, err => {
        console.log(err);
        resolve(false);
      });
    });
  }

  onSelectFile(event) {
    if (event.target.files && event.target.files[0]) {
      for (const i of event.target.files) {
        if (i.size >= this.maxFileSize * 1024 * 1024) {
          const msgObj = {
            vi: 'Kích thước tệp tin quá lớn, tên tệp: ' + i.name,
            en: 'The file is too large, file name: ' + i.name
          };
          this.snackbarService.openSnackBar(0, '', msgObj[this.selectedLang], 'error_notification', this.config.expiredTime);
          return;
        }
        if (this.acceptFileType.filter(t => t === i.type.toLowerCase()).length > 0) {
          this.files.push(i);
          const extension = i.name.substring(i.name.lastIndexOf('.')).split('.').pop();
          this.urls.push(this.getFileIcon(extension));

          const reader = new FileReader();
          reader.onload = () => {
            this.uploaded = true;
          };
          if (i.name.length > 20) {
            const startText = i.name.substr(0, 5);
            const shortText = i
              .name
              .substring(i.name.length - 7, i.name.length);
            this.fileNames.push(startText + '...' + shortText);
            this.fileNamesFull.push(i.name);
          } else {
            this.fileNames.push(i.name);
            this.fileNamesFull.push(i.name);
          }
          reader.readAsDataURL(i);
        } else {
          const msgObj = {
            vi: 'Không hỗ trợ loại tệp tin ',
            en: 'File type is not supported '
          };
          // tslint:disable-next-line: max-line-length
          this.snackbarService.openSnackBar(0, '', msgObj[this.selectedLang] + i.type.substring(i.type.lastIndexOf('/') + 1).toUpperCase(), 'error_notification', this.config.expiredTime);
        }
      }
    }
  }

  removeItem(index: number) {
    this.urls.splice(index, 1);
    this.fileNames.splice(index, 1);
    this.fileNamesFull.splice(index, 1);
    this.files.splice(index, 1);
    this.blankVal = '';
  }

  getFileIcon(ext) {
    return this.config.cloudStaticURL + 'icon/files/300x300/' + ext + '.png';
  }
  // DBN - BXD - dong bo thong hs qua LGSP Tan Dan
  syncPostReceiveInforFileFromLocal(dossierId) {
    this.dossierService.getDossierDetail(dossierId).subscribe(data => {
      let dataApplicant = data?.applicant?.data;
      let nhanThongTinHSTuDP = [];
      let thongTinNguoiNop = {
        HoTenNguoiNop: dataApplicant?.fullname, //*
        SoCMND: dataApplicant?.identityNumber, //*
        EmailNguoiNop: dataApplicant?.email,
        SoDienThoaiNguoiNop: dataApplicant?.phoneNumber, //*
        DiaChiThuongTruNguoiNop: dataApplicant?.address + ","
          + dataApplicant?.village?.label + ","
          + dataApplicant?.district?.label + ","
          + dataApplicant?.province?.label + ","
          + dataApplicant?.nation?.label, //*
        MaDoiTuong: ""
      };
      let giayToTrongQuaTrinhXuLy = {
        MaGiayTo: "",
        TenGiayTo: "",
        NoiDungBase64: "",
        DinhDangTepTin: "", // dinh dang .pdf, .doc, .png,...
        MoTa: "",
        LoaiGiayTo: ""
      }
      let hinhThucTraKetQua = 0;
      switch (data?.dossierReceivingKind?.id) {
        case '5f8968888fffa53e4c073ded':
          hinhThucTraKetQua = 0;
          break;
        case '604ecde877bed4110c1dd0d1':
          hinhThucTraKetQua = 1;
          break;
        case '6163f1e20f363b04cf60b224':
          hinhThucTraKetQua = 1;
          break;
        case '5f8969018fffa53e4c073dee':
          hinhThucTraKetQua = 1;
          break;
      }
      let arrThongTinTienTrinh = [];
      let dossierStatus = this.statusName;
      data?.task.forEach(task => {
        if (task?.isCurrent === 1) {
          let thongTinTienTrinh = {
            NguoiXuLy: task?.assignee?.fullname, //*
            ChucDanh: "",
            ThoiDiemXuLy: this.datePipe.transform(task?.createdDate, 'yyyyMMddHHmmss'), //* yyyyMMddHHmmss
            PhongBanXuLy: task?.agency?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name,
            NoiDungXuLy: task?.bpmProcessDefinitionTask?.remind ? task?.bpmProcessDefinitionTask?.remind?.name : "",
            NgayBatDau: this.datePipe.transform(task?.createdDate, 'yyyyMMddHHmmss'), //yyyyMMddHHmmss
            NgayKetThucTheoQuyDinh: task?.isCurrent === 0 ? this.datePipe.transform(task?.updatedDate, 'yyyyMMddHHmmss') : ""
          }
          arrThongTinTienTrinh.push(thongTinTienTrinh);
        }

      });
      let dossierSync = {
        MaHoSo: data.code, //*
        TrangThaiHoSo: dossierStatus, // phu luc 2 *
        NgayTiepNhan: this.datePipe.transform(data?.acceptedDate, 'yyyyMMddHHmmss'),// yyyyMMddHHmmss*
        NgayHenTraKetQua: this.datePipe.transform(data?.appointmentDate, 'yyyyMMddHHmmss'), //yyyyMMddHHmmss
        ThongTinNguoiNop: thongTinNguoiNop,
        GiayToTrongQuaTrinhXuLy: [],
        HinhThucTraKetQua: hinhThucTraKetQua, // 0: tra truc tiep 1: tra qua duong buu dien 2: tra kq truc tuyen
        ThongTinTienTrinh: arrThongTinTienTrinh,
        MaTTHC: data?.procedure?.code,
        TenTTHC: data?.procedure?.translate?.find(procedure => procedure.languageId === Number(this.selectedLangId))?.name ? data?.procedure?.translate?.find(procedure => procedure.languageId === Number(this.selectedLangId))?.name : '',
      }
      //nhanThongTinHSTuDP.push(dossierSync);
      let params = "agencyId=" + data?.agency.id + "&subsystemId=" + this.config?.subsystemId; // &configId
      this.dossierService.postReceiveInforFileFromLocal(params, dossierSync).subscribe(async data => {
        console.log("===postReceiveInforFileFromLocal===");
        console.log(data);
      }, er => {
        console.log(er);
      })
    }, err => {
      console.log(err);
    });
  }
  syncDossierKTMToBXD(dossierId) {
    this.dossierService.getDossierDetail(dossierId).subscribe(data => {
      let dataApplicant = data?.applicant?.data;
      let thongTinNguoiNop = {
        HoTenNguoiNop: dataApplicant?.fullname, //*
        SoCMND: dataApplicant?.identityNumber, //*
        EmailNguoiNop: dataApplicant?.email,
        SoDienThoaiNguoiNop: dataApplicant?.phoneNumber, //*
        DiaChiThuongTruNguoiNop: dataApplicant?.address + ","
          + dataApplicant?.village?.label + ","
          + dataApplicant?.district?.label + ","
          + dataApplicant?.province?.label + ","
          + dataApplicant?.nation?.label, //*
        MaDoiTuong: ""
      };
      let hinhThucTraKetQua = 0;
      switch (data?.dossierReceivingKind?.id) {
        case '5f8968888fffa53e4c073ded':
          hinhThucTraKetQua = 0;
          break;
        case '604ecde877bed4110c1dd0d1':
          hinhThucTraKetQua = 1;
          break;
        case '6163f1e20f363b04cf60b224':
          hinhThucTraKetQua = 1;
          break;
        case '5f8969018fffa53e4c073dee':
          hinhThucTraKetQua = 1;
          break;
      }
      let arrThongTinTienTrinh = [];
      data?.task.forEach(task => {
        if (task?.isCurrent === 1) {
          let thongTinTienTrinh = {
            NguoiXuLy: task?.assignee?.fullname, //*
            ChucDanh: "",
            ThoiDiemXuLy: this.datePipe.transform(task?.createdDate, 'yyyyMMddHHmmss'), //* yyyyMMddHHmmss
            PhongBanXuLy: task?.agency?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name,
            NoiDungXuLy: task?.bpmProcessDefinitionTask?.remind ? task?.bpmProcessDefinitionTask?.remind?.name : "",
            NgayBatDau: this.datePipe.transform(task?.createdDate, 'yyyyMMddHHmmss'), //yyyyMMddHHmmss
            NgayKetThucTheoQuyDinh: task?.isCurrent === 0 ? this.datePipe.transform(task?.updatedDate, 'yyyyMMddHHmmss') : ""
          }
          arrThongTinTienTrinh.push(thongTinTienTrinh);
        }

      });
      let giayToTrongQuaTrinhXuLy = [];
      let filename = ""
      for (let i = 0; i < this.uploadedImage.length; i++) {
        filename = this.uploadedImage[i].filename
        const nameDoc = filename.split(".")[0];
        const extension = filename.split(".").pop();
        const base64Filename = btoa(unescape(encodeURIComponent(filename)));
        giayToTrongQuaTrinhXuLy.push({
          MaGiayTo: null,
          TenGiayTo: nameDoc,
          NoiDungBase64: base64Filename,
          DinhDangTepTin: extension, // dinh dang .pdf, .doc, .png,...
          MoTa: null,
          LoaiGiayTo: "2"
        })
      }
      let maHoSo = data.code
      if (maHoSo.includes("000.00")) {
        this.codeMap = maHoSo;
      } else {
        const inputString = maHoSo;
        const parts = inputString.split("-");
        const prefixParts = parts[0].split(".");
        const prefix = `000.00.${prefixParts[1]}.${prefixParts[0]}`;
        this.codeMap = `${prefix}-${parts[1]}-${parts[2]}`;
      }
      let originalString = data.procedure.code;
      let result = originalString.split(".")[0] + '.' + originalString.split(".")[1];
      let dossierSync = {
        data: [
          {
            MaHoSo: this.codeMap,
            MaTTHC: result,
            NgayTiepNhan: this.datePipe.transform(data?.acceptedDate, 'yyyyMMddHHmmss'),
            NgayHenTraKetQua: this.datePipe.transform(data?.appointmentDate, 'yyyyMMddHHmmss'),
            TrangThaiHoSo: "8",
            ThongTinNguoiNop: thongTinNguoiNop,
            GiayToTrongQuaTrinhXuLy: giayToTrongQuaTrinhXuLy,
            HinhThucTraKetQua: hinhThucTraKetQua,
            ThongTinTienTrinh: arrThongTinTienTrinh

          }
        ]
      }

      let config = this.constructConfigId// &configId
      this.adapterService.syncDossierConstructKTM(config, dossierSync).subscribe(async data => {
        console.log(data);
      })
    }, err => {
      console.log(err);
    });
  }

  syncDossierHPGToBXD(dossierId) {
    this.dossierService.getDossierDetail(dossierId).subscribe(data => {
      let dataApplicant = data?.applicant?.data;
      let thongTinNguoiNop = {
        HoTenNguoiNop: dataApplicant?.ownerFullname, //*
        SoCMND: dataApplicant?.identityNumber, //*
        EmailNguoiNop: dataApplicant?.email,
        SoDienThoaiNguoiNop: dataApplicant?.phoneNumber, //*
        DiaChiThuongTruNguoiNop: dataApplicant?.address + ","
          + dataApplicant?.village?.label + ","
          + dataApplicant?.district?.label + ","
          + dataApplicant?.province?.label + ","
          + dataApplicant?.nation?.label, //*
        MaDoiTuong: ""
      };
      let hinhThucTraKetQua = 0;
      switch (data?.dossierReceivingKind?.id) {
        case '5f8968888fffa53e4c073ded':
          hinhThucTraKetQua = 0;
          break;
        case '604ecde877bed4110c1dd0d1':
          hinhThucTraKetQua = 1;
          break;
        case '6163f1e20f363b04cf60b224':
          hinhThucTraKetQua = 1;
          break;
        case '5f8969018fffa53e4c073dee':
          hinhThucTraKetQua = 1;
          break;
      }
      let arrThongTinTienTrinh = [];
      data?.task.forEach(task => {
        if (task?.isCurrent === 1) {
          let thongTinTienTrinh = {
            NguoiXuLy: task?.assignee?.fullname, //*
            ChucDanh: "",
            ThoiDiemXuLy: this.datePipe.transform(task?.createdDate, 'yyyyMMddHHmmss'), //* yyyyMMddHHmmss
            PhongBanXuLy: task?.agency?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name,
            NoiDungXuLy: task?.bpmProcessDefinitionTask?.remind ? task?.bpmProcessDefinitionTask?.remind?.name : "",
            NgayBatDau: this.datePipe.transform(task?.createdDate, 'yyyyMMddHHmmss'), //yyyyMMddHHmmss
            NgayKetThucTheoQuyDinh: task?.isCurrent === 0 ? this.datePipe.transform(task?.updatedDate, 'yyyyMMddHHmmss') : ""
          }
          arrThongTinTienTrinh.push(thongTinTienTrinh);
        }

      });
      let giayToTrongQuaTrinhXuLy = [];
      let filename = ""
      for (let i = 0; i < this.uploadedImage.length; i++) {
        filename = this.uploadedImage[i].filename
        const nameDoc = filename.split(".")[0];
        const extension = filename.split(".").pop();
        const base64Filename = btoa(unescape(encodeURIComponent(filename)));
        giayToTrongQuaTrinhXuLy.push({
          MaGiayTo: null,
          TenGiayTo: nameDoc,
          NoiDungBase64: base64Filename,
          DinhDangTepTin: extension, // dinh dang .pdf, .doc, .png,...
          MoTa: null,
          LoaiGiayTo: "2"
        })
      }
      let maHoSo = data.code;
      let originalString = data.procedure.code;
      let result = originalString.split(".")[0] + '.' + originalString.split(".")[1];
      let dossierSync = {
        data: [
          {
            MaHoSo: maHoSo,
            MaTTHC: result,
            NgayTiepNhan: this.datePipe.transform(data?.acceptedDate, 'yyyyMMddHHmmss'),
            NgayHenTraKetQua: this.datePipe.transform(data?.appointmentDate, 'yyyyMMddHHmmss'),
            TrangThaiHoSo: "8",
            ThongTinNguoiNop: thongTinNguoiNop,
            GiayToTrongQuaTrinhXuLy: giayToTrongQuaTrinhXuLy,
            HinhThucTraKetQua: hinhThucTraKetQua,
            ThongTinTienTrinh: arrThongTinTienTrinh

          }
        ]
      }
      this.adapterService.syncDossierConstructHPG(dossierSync).subscribe(async data => {
        console.log(data);
      })
    }, err => {
      console.log(err);
    });
  }


  // getApprovaledAgency(agency) {
  //   if (!!agency.parent) {
  //     this.processService.getApprovalAgency(agency.parent.id).subscribe(data => {
  //       if (!!data.content[0]) {
  //         data.content[0].approvaledAgency.forEach(item => {
  //           item.name.forEach(name => {
  //             if (name.languageId == this.selectedLangId)
  //               item.DisplayName = name.name;
  //           })
  //         });
  //         this.approvaledAgency = data.content[0].approvaledAgency;
  //       }
  //     });
  //   }

  //   if (!!this.approvaledAgency) {
  //     this.processService.getApprovalAgency(agency.id).subscribe(data => {
  //       if (!!data.content[0]) {
  //         data.content[0].approvaledAgency.forEach(item => {
  //           item.name.forEach(name => {
  //             if (name.languageId == this.selectedLangId)
  //               item.DisplayName = name.name;
  //           })
  //         });
  //         this.approvaledAgency = data.content[0].approvaledAgency;
  //       }
  //     });
  //   }
  //   console.log('approvaledAgency', this.approvaledAgency);
  // }
  getApprovaledAgency(agency) {
    let approvalAgency = [];
    let enableApprovaledAgencyId = this.deploymentService.env.OS_QNI.enableApprovaledAgencyId;
    if (enableApprovaledAgencyId) {
      if (agency && agency.id) {
        this.processService.getApprovalAgency(agency.id).subscribe(data2 => {
          if (data2 && data2.content && data2.content.length > 0) {
            const uniqueAgencies = new Set();
            data2.content.forEach(item => {
              if (item.approvalAgency && item.approvalAgency.name) {
                item.approvalAgency.name.forEach(name => {
                  if (name.languageId === this.selectedLangId) {
                    item.approvalAgency.DisplayName = name.name;
                  }
                });
                if (!uniqueAgencies.has(item.approvalAgency.id)) {
                  uniqueAgencies.add(item.approvalAgency.id);
                  approvalAgency.push(item.approvalAgency);
                }
              }
            });

            if (approvalAgency.length === 1) {
              this.updateForm.patchValue({
                approvalAgencyId: approvalAgency[0].id,
              });
            }
          }
        });
      }
    } else {
      if (!!agency.parent) {
        this.processService.getApprovalAgency(agency.parent.id).subscribe(data => {
          if (data.content.length > 0) {
            approvalAgency = [];
            // tslint:disable-next-line:prefer-for-of
            for (let i = 0; i < data.content.length; i++) {
              data.content[i].approvalAgency.name.forEach(name => {
                if (name.languageId === this.selectedLangId) {
                  data.content[i].approvalAgency.DisplayName = name.name;
                }
              });
              if (approvalAgency.filter(item => item.id === data.content[i].approvalAgency.id).length === 0) {
                approvalAgency.push(data.content[i].approvalAgency);
              }
            }
          }
          if (!!approvalAgency) {
            this.processService.getApprovalAgency(agency.id).subscribe(data2 => {
              if (data2.content.length > 0) {
                // tslint:disable-next-line:prefer-for-of
                for (let i = 0; i < data2.content.length; i++) {
                  data2.content[i].approvalAgency.name.forEach(name => {
                    if (name.languageId === this.selectedLangId) {
                      data2.content[i].approvalAgency.DisplayName = name.name;
                    }
                  });
                  if (approvalAgency.filter(item => item.id === data2.content[i].approvalAgency.id).length === 0) {
                    approvalAgency.push(data2.content[i].approvalAgency);
                  }
                }
              }
              if (!!approvalAgency && approvalAgency.length === 1) {
                this.updateForm.patchValue({
                  approvalAgencyId: approvalAgency[0].id,
                });
              }
            });
          }
        });
      }
      console.log('approvalAgency', approvalAgency);
    }
    return approvalAgency;
  }


  setdossierTaskStatus() {
    return new Promise<void>(resolve => {
      const tagWaitingForApprovalStatusId = this.allowNextStepWaitingForApproval ? this.deploymentService.env.dossierTaskStatus.waitingForApprovalToCancel.id : this.deploymentService.env.dossierTaskStatus.cancelDossier.id;
      this.dossierService.getAgencyTag(tagWaitingForApprovalStatusId).subscribe(data => {
        this.dossierTaskStatusWaitingForApproval.id = data.id;
        this.dossierTaskStatusWaitingForApproval.name = data.trans;
        resolve();
      }, err => {
        resolve();
      });
    });

  }

  setdossierTaskStatusRemind() {
    return new Promise<void>(resolve => {
      const tagWaitingForApprovalRemindId = this.allowNextStepWaitingForApproval ? this.deploymentService.env.dossierMenuTaskRemind.waitingForApprovalToCancel.id : this.deploymentService.env.dossierMenuTaskRemind.cancelDossier.id;
      this.dossierService.getAgencyTag(tagWaitingForApprovalRemindId).subscribe(data => {
        this.dossierMenuTaskRemindWaitingForApproval.id = data.id;
        this.dossierMenuTaskRemindWaitingForApproval.name = data.trans;
        this.statusName = data?.trans.filter(t => t.languageId === Number(this.selectedLangId))?.name;
        resolve();
      }, err => {
        resolve();
      });
    });
  }


}


export class CancelMultiDossierDialogModel {
  constructor(public dossierId: any, public dossierCode: any) {
  }
}
