import { Component, OnInit, ChangeDetectorRef, AfterViewInit, ViewChild, forwardRef } from '@angular/core';
import { FormGroup, FormControl } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import { EnvService } from 'src/app/core/service/env.service';
import { FormElement } from 'src/app/data/schema/form-element';
import { MatTableDataSource } from '@angular/material/table';

import { SnackbarService } from 'src/app/data/service/snackbar/snackbar.service';
import { ProcedureService } from 'src/app/data/service/procedure/procedure.service';
import { Observable, ReplaySubject, Subject } from 'rxjs';


import { ActivatedRoute, Router } from '@angular/router';
import { ExportExcelService } from 'src/app/data/service/export-excel/export-excel.service';
import { DatePipe } from '@angular/common';
import { MainService } from 'src/app/data/service/main/main.service';
import { EReceiptService } from 'src/app/data/service/invoice-receipt/ereceipt.service';
import { DossierService } from 'src/app/data/service/dossier/dossier.service';
import { ReportService } from 'src/app/data/service/report/report.service';
import { EInvoiceService } from 'src/app/data/service/invoice-receipt/einvoice.service';
import * as tUtils from 'src/app/data/service/thoai.service';
import { DeploymentService } from 'src/app/data/service/deployment.service';
import { takeUntil } from 'rxjs/operators';
import { PrintTemplateComponent } from 'src/app/modules/dossier/pages/processing/pages/processing-detail/dialogs/print-template/print-template.component';
import { DomSanitizer } from '@angular/platform-browser';
import { ChiThi18CtComponent, ChiThi18CtDialogModel } from 'src/app/modules/dlk-dac-thu/dialogs/chithi18-ct/chithi18-ct.component';
import { MatSelect } from '@angular/material/select';
import { PadmanService } from 'src/app/data/service/svc-padman/padman.service';

interface Agency {
  id: string;
  name: string;
}

interface Sector {
  id: string;
  name: string;
}

@Component({
  selector: 'app-chithi18',
  templateUrl: './chithi18.component.html',
  styleUrls: ['./chithi18.component.scss', '/src/app/app.component.scss']
})
export class ChiThi18Component implements OnInit {

  config = this.envService.getConfig();
  selectedLang: string;

  protected onDestroy = new Subject<void>();
  date = new Date();
  fromdate = new Date(this.date.getFullYear(), this.date.getMonth() - 1, 15);
  todate = new Date(this.date.getFullYear(), this.date.getMonth(), 14);
  fromLKdate = new Date(this.date.getFullYear() - 1, 11, 15);
  toLKdate = new Date(this.date.getFullYear(), 11, 14);

  searchForm = new FormGroup({
    fromdate: new FormControl(this.fromdate.toISOString()),
    todate: new FormControl(this.todate.toISOString()),
    fromLKdate: new FormControl(this.fromLKdate.toISOString()),
    toLKdate: new FormControl(this.toLKdate.toISOString())
  });

  pageTitle = {
    vi: `Báo cáo chỉ thị 18 tỉnh Đắk Lắk`,
    en: `Directive Report 18 Dak Lak Province`
  };

  displayedColumns: string[] = ['stt', 'thutuc',
                                'tongHS_thang', 'HS_ton', 'HS_tiepnhan',
                                'tongHS_giaiquyet', 'HS_truochan', 'HS_dunghan', 'HS_quahan', 'VBGQ', 'VBGQ_web', 'VBGQ_1cua',
                                'tongHS_ton', 'HS_tonconhan', 'HS_tonquahan', 'VBton', 'VBton_web', 'VBton_1cua',
                                'tongHS_luyke', 'LK_HS_truochan', 'LK_HS_dunghan', 'LK_HS_quahan', 'LK_VB', 'LK_VB_web', 'LK_VB_1cua'];
  ELEMENTDATA: FormElement[] = [];
  dataSource: MatTableDataSource<any>;
  env = this.deploymentService.getAppDeployment().env;
  dataAgency = new Array();
  constructor(
    private dialog: MatDialog,
    private envService: EnvService,
    private cdRef: ChangeDetectorRef,
    private snackbarService: SnackbarService,
    private mainService: MainService,
    private padmanService: PadmanService,
    private deploymentService: DeploymentService,
    private datepipe: DatePipe,
  ) {
    this.dataSource = new MatTableDataSource(this.ELEMENTDATA);
  }

  async ngOnInit(): Promise<void> {
    this.mainService.setPageTitle(this.pageTitle[localStorage.getItem('language')]);
    const requestBody = {
      fromDate: this.datepipe.transform(this.fromdate, 'yyyy-MM-dd').toString(),
      toDate: this.datepipe.transform(this.todate, 'yyyy-MM-dd').toString(),
      fromLKDate:   this.datepipe.transform(this.fromLKdate, 'yyyy-MM-dd').toString(),
      toLKDate: this.datepipe.transform(this.toLKdate, 'yyyy-MM-dd').toString()
    };

    // Lấy danh sách báo cáo
    this.getDuLieuCT18(requestBody);
  }

  ngOnDestroy() {
    this.onDestroy.next();
    this.onDestroy.complete();
  }
  ngAfterViewInit() {
    this.cdRef.detectChanges();
  }

  onConfirmSearch() {
    const formObj = this.searchForm.getRawValue();

    if (!formObj.fromdate || !formObj.todate) {
      this.snackbarService.openSnackBar(0, 'Vui lòng chọn ngày hẹn!', '', 'error_notification', this.config.expiredTime);
      return;
    } else {
      var fDate = this.datepipe.transform(formObj.fromdate, 'yyyy-MM-dd').toString();
      var tDate = this.datepipe.transform(formObj.todate, 'yyyy-MM-dd').toString();
      var fLKDate = this.datepipe.transform(formObj.fromLKdate, 'yyyy-MM-dd').toString();
      var tLKDate = this.datepipe.transform(formObj.toLKdate, 'yyyy-MM-dd').toString();
      if (tDate < fDate) {
        this.snackbarService.openSnackBar(0, 'Đến ngày không được nhỏ hơn từ ngày', '', 'error_notification', this.config.expiredTime);
        return;
      }
      else if (fLKDate > tLKDate) {
        this.snackbarService.openSnackBar(0, 'Đến ngày lũy kế không được nhỏ hơn từ ngày lũy kế', '', 'error_notification', this.config.expiredTime);
        return;
      }else {
        const requestBody = {
          fromDate: fDate,
          toDate: tDate,
          fromLKDate: fLKDate,
          toLKDate: tLKDate
        }

        this.getDuLieuCT18(requestBody);
      }
    }
  }

  getDuLieuCT18(searchString) {
    let content = JSON.stringify(searchString);
    this.padmanService.getDLKChiThi18(content).subscribe(data => {
      this.dataSource.data = data;
    }, err => {
      console.log(err);
    });
  }


  xemChiTiet(type, agencyId) {
    const formObj = this.searchForm.getRawValue();
    console.log(type);
    if (!formObj.fromdate || !formObj.todate) {
      this.snackbarService.openSnackBar(0, 'Vui lòng chọn ngày xem báo cáo!', '', 'error_notification', this.config.expiredTime);
      return;
    } else {
      var fDate = this.datepipe.transform(formObj.fromdate, 'yyyy-MM-dd').toString();
      var tDate = this.datepipe.transform(formObj.todate, 'yyyy-MM-dd').toString();
      var fLKDate = this.datepipe.transform(formObj.fromLKdate, 'yyyy-MM-dd').toString();
      var tLKDate = this.datepipe.transform(formObj.toLKdate, 'yyyy-MM-dd').toString();
      if (tDate < fDate) {
        this.snackbarService.openSnackBar(0, 'Đến ngày không được nhỏ hơn từ ngày', '', 'error_notification', this.config.expiredTime);
        return;
      }
      else if (fLKDate > tLKDate) {
        this.snackbarService.openSnackBar(0, 'Đến ngày lũy kế không được nhỏ hơn từ ngày lũy kế', '', 'error_notification', this.config.expiredTime);
        return;
      }else {
        const dialogData = new ChiThi18CtDialogModel(fDate, tDate, fLKDate, tLKDate, type, agencyId);
        const dialogRef = this.dialog.open(ChiThi18CtComponent, {
          width: '90vw',
          height: '90vh',
          data: dialogData,
          disableClose: true,
          autoFocus: false
        });
        dialogRef.afterClosed().subscribe(dialogResult => { });
      }
    }

  }
}

