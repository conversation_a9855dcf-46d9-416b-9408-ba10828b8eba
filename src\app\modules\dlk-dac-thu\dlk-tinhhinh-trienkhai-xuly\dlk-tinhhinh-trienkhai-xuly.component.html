<h2><PERSON><PERSON><PERSON> hình triể<PERSON> khai, <PERSON><PERSON> lý</h2>
<div fxLayout="row" fxLayoutAlign="center">
    <div class="frm_searchbar" fxFlex="grow">
        <form class="searchForm" [formGroup]="searchForm">
            <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="row">
                <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                    <mat-label i18n>Từ ngày</mat-label>
                    <input matInput [matDatepicker]="pickerFromDate" formControlName="fromDate"
                        (dateChange)="onChangeFromDate($event)">
                    <mat-datepicker-toggle matSuffix [for]="pickerFromDate"></mat-datepicker-toggle>
                    <mat-datepicker #pickerFromDate></mat-datepicker>
                </mat-form-field>
                <div fxFlex='1'></div>
                <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                    <mat-label i18n>Đến ngày</mat-label>
                    <input matInput [matDatepicker]="pickerToDate" formControlName="toDate"
                        (dateChange)="onChangeToDate($event)">
                    <mat-datepicker-toggle matSuffix [for]="pickerToDate"></mat-datepicker-toggle>
                    <mat-datepicker #pickerToDate></mat-datepicker>
                </mat-form-field>
                <div fxFlex='1'></div>
                <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                    <mat-label i18n>Cơ quan</mat-label>
                    <mat-select msInfiniteScroll (infiniteScroll)="getAgencyList(true)" formControlName="agency"
                                [complete]="isAgencyListFull">
                        <mat-option>
                            <ngx-mat-select-search formControlName="agencyCtrl" placeholderLabel=""
                                                   (keyup)="searchAgencyList()"
                                                   [disableScrollToActiveOnOptionsChanged]="true"
                                                   [clearSearchInput]="false"
                                                   i18n-noEntriesFoundLabel
                                                   noEntriesFoundLabel="Không tìm thấy kết quả nào!">
                                <mat-icon ngxMatSelectSearchClear (click)="clearAgencyList()">close</mat-icon>
                            </ngx-mat-select-search>
                        </mat-option>
                        <mat-option value="" i18n>Tất cả</mat-option>
                        <mat-option *ngFor="let agency of agencyList" value="{{agency.id}}">
                            {{agency.name}}
                            <span *ngIf="agency.name == undefined || agency.name == null" >(Không tìm thấy bản dịch)</span>
                        </mat-option>
                    </mat-select>
                </mat-form-field>
            </div>
            <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="row" fxLayoutAlign="end">
                <div fxFlex='1'></div>
                <button mat-flat-button fxFlex.gt-sm="12" fxFlex='12' class="btn-download-excel"
                    (click)="exportDataToExcel()">
                    <mat-icon class="iconStatistical">cloud_download</mat-icon>
                    <span i18n>Xuất excel</span>
                </button>
                <div fxFlex='1'></div>
                <button mat-flat-button fxFlex.gt-sm="12" fxFlex='12' class="btn-search" (click)="searchBtn()"
                    [disabled]="isLoading">
                    <mat-icon class="iconStatistical" *ngIf="!isLoading">bar_chart</mat-icon>
                    <mat-spinner diameter="25" class="iconStatistical" *ngIf="isLoading"></mat-spinner>
                    <span i18n>Thống kê</span>
                </button>

            </div>
            <div fxHide.gt-sm style="display: flex;justify-content: center; width: 100%; margin-top: 1em;">
                <mat-button-toggle-group style="width: 100%;" fxLayout.sm="row" fxLayout.xs="column" name="fontStyle"
                    #group (change)="onValChange($event.value)" aria-label="Font Style" value="tabIndex">
                    <mat-button-toggle fxFlex.xs="100" fxFlex.sm="30" value="0">Tình hình triển khai, xử
                        lý</mat-button-toggle>
                </mat-button-toggle-group>
            </div>
        </form>
    </div>
</div>
<div fxHide.lt-md fxLayout="row" fxLayoutAlign="center">
    <div class="frm_main" fxFlex="grow">
        <div class="frm_tbl0">
            <table mat-table [dataSource]="dataProcessingSource" id="table-mau-6a">
                <caption></caption>
                <!-- Header row-->
                <ng-container matColumnDef="A1">
                    <th mat-header-cell *matHeaderCellDef [attr.rowspan]="5" style="width: 3%;">STT</th>
                </ng-container>
                <ng-container matColumnDef="A2">
                    <th mat-header-cell *matHeaderCellDef [attr.rowspan]="5" style="width: 11.2%;">Tên cơ quan
                    </th>
                </ng-container>
                <ng-container matColumnDef="A4">
                    <th mat-header-cell *matHeaderCellDef [attr.colspan]="13" style="width: 44.2%;">Tình hình
                        triển khai</th>
                </ng-container>
                <ng-container matColumnDef="A5">
                    <th mat-header-cell *matHeaderCellDef [attr.colspan]="14" style="width: 44.2%;">Tình hình xử
                        lý</th>
                </ng-container>
                <!-- group row 1 -->
                <ng-container matColumnDef="B1">
                    <th mat-header-cell *matHeaderCellDef [attr.rowspan]="2" [attr.colspan]="2"
                        style="width: 6.8%;">Tổng số</th>
                </ng-container>
                <ng-container matColumnDef="B2">
                    <th mat-header-cell *matHeaderCellDef [attr.colspan]="11" style="width: 34%;">Số hồ sơ tiếp
                        nhận | thủ tục đã thực hiện</th>
                </ng-container>
                <ng-container matColumnDef="B3">
                    <th mat-header-cell *matHeaderCellDef [attr.rowspan]="4" style="width: 3%;">Hồ sơ tồn</th>
                </ng-container>
                <ng-container matColumnDef="B4">
                    <th mat-header-cell *matHeaderCellDef [attr.rowspan]="2" [attr.colspan]="2"
                        style="width: 6.8%;">Tiếp nhận</th>
                </ng-container>
                <ng-container matColumnDef="B5">
                    <th mat-header-cell *matHeaderCellDef [attr.rowspan]="2" [attr.colspan]="9"
                        style="width: 30.6%;">Đã giải quyết</th>
                </ng-container>
                <ng-container matColumnDef="B6">
                    <th mat-header-cell *matHeaderCellDef [attr.rowspan]="4" style="width: 3%;">Đang giải quyết
                    </th>
                </ng-container>
                <ng-container matColumnDef="B7">
                    <th mat-header-cell *matHeaderCellDef [attr.rowspan]="4" style="width: 3%;">Tỷ lệ giải quyết
                    </th>
                </ng-container>
                <!-- group row 2 -->
                <ng-container matColumnDef="C1">
                    <th mat-header-cell *matHeaderCellDef [attr.colspan]="3" style="width: 10.2%;">Cung cấp
                        thông tin trực tuyến</th>
                </ng-container>
                <ng-container matColumnDef="C2">
                    <th mat-header-cell *matHeaderCellDef [attr.colspan]="4" style="width: 10.2%;">Trực tuyến
                        một phần</th>
                </ng-container>
                <ng-container matColumnDef="C3">
                    <th mat-header-cell *matHeaderCellDef [attr.colspan]="4" style="width: 10.2%;">Trực tuyến
                        toàn trình</th>
                </ng-container>
                <!-- group row 3 -->
                <ng-container matColumnDef="D1">
                    <th mat-header-cell *matHeaderCellDef [attr.rowspan]="2" style="width: 3%;">HSTN</th>
                </ng-container>
                <ng-container matColumnDef="D2">
                    <th mat-header-cell *matHeaderCellDef [attr.rowspan]="2" style="width: 3%;">TTHC</th>
                </ng-container>
                <ng-container matColumnDef="D3">
                    <th mat-header-cell *matHeaderCellDef [attr.rowspan]="2" style="width: 3%;">HSTN</th>
                </ng-container>
                <ng-container matColumnDef="D4">
                    <th mat-header-cell *matHeaderCellDef [attr.rowspan]="2" style="width: 3%;">HSTN BCCI</th>
                </ng-container>
                <ng-container matColumnDef="D5">
                    <th mat-header-cell *matHeaderCellDef [attr.rowspan]="2" style="width: 3%;">TTHC</th>
                </ng-container>
                <ng-container matColumnDef="D6">
                    <th mat-header-cell *matHeaderCellDef [attr.rowspan]="2" style="width: 3%;">HSTN trực tiếp
                    </th>
                </ng-container>
                <ng-container matColumnDef="D7">
                    <th mat-header-cell *matHeaderCellDef [attr.rowspan]="2" style="width: 3%;">HSTN trực tuyến
                    </th>
                </ng-container>
                <ng-container matColumnDef="D8">
                    <th mat-header-cell *matHeaderCellDef [attr.rowspan]="2" style="width: 3%;">HSTN BCCI</th>
                </ng-container>
                <ng-container matColumnDef="D9">
                    <th mat-header-cell *matHeaderCellDef [attr.rowspan]="2" style="width: 3%;">TTHC</th>
                </ng-container>
                <ng-container matColumnDef="D10">
                    <th mat-header-cell *matHeaderCellDef [attr.rowspan]="2" style="width: 3%;">HSTN trực tiếp
                    </th>
                </ng-container>
                <ng-container matColumnDef="D11">
                    <th mat-header-cell *matHeaderCellDef [attr.rowspan]="2" style="width: 3%;">HSTN trực tuyến
                    </th>
                </ng-container>
                <ng-container matColumnDef="D12">
                    <th mat-header-cell *matHeaderCellDef [attr.rowspan]="2" style="width: 3%;">HSTN BCCI</th>
                </ng-container>
                <ng-container matColumnDef="D13">
                    <th mat-header-cell *matHeaderCellDef [attr.rowspan]="2" style="width: 3%;">TTHC</th>
                </ng-container>
                <ng-container matColumnDef="D14">
                    <th mat-header-cell *matHeaderCellDef [attr.rowspan]="2" style="width: 3%;">MCĐT</th>
                </ng-container>
                <ng-container matColumnDef="D15">
                    <th mat-header-cell *matHeaderCellDef [attr.rowspan]="2" style="width: 3%;">Trực tuyến</th>
                </ng-container>
                <ng-container matColumnDef="D16">
                    <th mat-header-cell *matHeaderCellDef [attr.colspan]="3" style="width: 10.2%;">Cung cấp
                        thông tin trực tuyến</th>
                </ng-container>
                <ng-container matColumnDef="D17">
                    <th mat-header-cell *matHeaderCellDef [attr.colspan]="3" style="width: 10.2%;">Trực tuyến
                        một phần</th>
                </ng-container>
                <ng-container matColumnDef="D18">
                    <th mat-header-cell *matHeaderCellDef [attr.colspan]="3" style="width: 10.2%;">Trực tuyến
                        toàn trình</th>
                </ng-container>
                <!-- group row 4 -->
                <ng-container matColumnDef="E1">
                    <th mat-header-cell *matHeaderCellDef style="width: 3%;">Trước hạn</th>
                </ng-container>
                <ng-container matColumnDef="E2">
                    <th mat-header-cell *matHeaderCellDef style="width: 3%;">Đúng hạn</th>
                </ng-container>
                <ng-container matColumnDef="E3">
                    <th mat-header-cell *matHeaderCellDef style="width: 3%;">Trễ hạn</th>
                </ng-container>
                <ng-container matColumnDef="E4">
                    <th mat-header-cell *matHeaderCellDef style="width: 3%;">Trước hạn</th>
                </ng-container>
                <ng-container matColumnDef="E5">
                    <th mat-header-cell *matHeaderCellDef style="width: 3%;">Đúng hạn</th>
                </ng-container>
                <ng-container matColumnDef="E6">
                    <th mat-header-cell *matHeaderCellDef style="width: 3%;">Trễ hạn</th>
                </ng-container>
                <ng-container matColumnDef="E7">
                    <th mat-header-cell *matHeaderCellDef style="width: 3%;">Trước hạn</th>
                </ng-container>
                <ng-container matColumnDef="E8">
                    <th mat-header-cell *matHeaderCellDef style="width: 3%;">Đúng hạn</th>
                </ng-container>
                <ng-container matColumnDef="E9">
                    <th mat-header-cell *matHeaderCellDef style="width: 3%;">Trễ hạn</th>
                </ng-container>
                <!-- group row 5 -->
                <ng-container matColumnDef="F1">
                    <th mat-header-cell *matHeaderCellDef style="width: 3%;">(1)</th>
                </ng-container>
                <ng-container matColumnDef="F2">
                    <th mat-header-cell *matHeaderCellDef style="width: 11.2%;">(2)</th>
                </ng-container>
                <ng-container matColumnDef="F4">
                    <th mat-header-cell *matHeaderCellDef style="width: 3%;">(3)</th>
                </ng-container>
                <ng-container matColumnDef="F5">
                    <th mat-header-cell *matHeaderCellDef style="width: 3%;">(4)</th>
                </ng-container>
                <ng-container matColumnDef="F6">
                    <th mat-header-cell *matHeaderCellDef style="width: 3%;">(5)</th>
                </ng-container>
                <ng-container matColumnDef="F7">
                    <th mat-header-cell *matHeaderCellDef style="width: 3%;">(6)</th>
                </ng-container>
                <ng-container matColumnDef="F8">
                    <th mat-header-cell *matHeaderCellDef style="width: 3%;">(7)</th>
                </ng-container>
                <ng-container matColumnDef="F9">
                    <th mat-header-cell *matHeaderCellDef style="width: 3%;">(8)</th>
                </ng-container>
                <ng-container matColumnDef="F10">
                    <th mat-header-cell *matHeaderCellDef style="width: 3%;">(9)</th>
                </ng-container>
                <ng-container matColumnDef="F11">
                    <th mat-header-cell *matHeaderCellDef style="width: 3%;">(10)</th>
                </ng-container>
                <ng-container matColumnDef="F12">
                    <th mat-header-cell *matHeaderCellDef style="width: 3%;">(11)</th>
                </ng-container>
                <ng-container matColumnDef="F13">
                    <th mat-header-cell *matHeaderCellDef style="width: 3%;">(12)</th>
                </ng-container>
                <ng-container matColumnDef="F14">
                    <th mat-header-cell *matHeaderCellDef style="width: 3%;">(13)</th>
                </ng-container>
                <ng-container matColumnDef="F15">
                    <th mat-header-cell *matHeaderCellDef style="width: 3%;">(14)</th>
                </ng-container>
                <ng-container matColumnDef="F16">
                    <th mat-header-cell *matHeaderCellDef style="width: 3%;">(15)</th>
                </ng-container>
                <ng-container matColumnDef="F17">
                    <th mat-header-cell *matHeaderCellDef style="width: 3%;">(16)</th>
                </ng-container>
                <ng-container matColumnDef="F18">
                    <th mat-header-cell *matHeaderCellDef style="width: 3%;">(17)</th>
                </ng-container>
                <ng-container matColumnDef="F19">
                    <th mat-header-cell *matHeaderCellDef style="width: 3%;">(18)</th>
                </ng-container>
                <ng-container matColumnDef="F20">
                    <th mat-header-cell *matHeaderCellDef style="width: 3%;">(19)</th>
                </ng-container>
                <ng-container matColumnDef="F21">
                    <th mat-header-cell *matHeaderCellDef style="width: 3%;">(20)</th>
                </ng-container>
                <ng-container matColumnDef="F22">
                    <th mat-header-cell *matHeaderCellDef style="width: 3%;">(21)</th>
                </ng-container>
                <ng-container matColumnDef="F23">
                    <th mat-header-cell *matHeaderCellDef style="width: 3%;">(22)</th>
                </ng-container>
                <ng-container matColumnDef="F24">
                    <th mat-header-cell *matHeaderCellDef style="width: 3%;">(23)</th>
                </ng-container>
                <ng-container matColumnDef="F25">
                    <th mat-header-cell *matHeaderCellDef style="width: 3%;">(24)</th>
                </ng-container>
                <ng-container matColumnDef="F26">
                    <th mat-header-cell *matHeaderCellDef style="width: 3%;">(25)</th>
                </ng-container>
                <ng-container matColumnDef="F27">
                    <th mat-header-cell *matHeaderCellDef style="width: 3%;">(26)</th>
                </ng-container>
                <ng-container matColumnDef="F28">
                    <th mat-header-cell *matHeaderCellDef style="width: 3%;">(27)</th>
                </ng-container>
                <ng-container matColumnDef="F29">
                    <th mat-header-cell *matHeaderCellDef style="width: 3%;">(28)</th>
                </ng-container>
                <ng-container matColumnDef="F30">
                    <th mat-header-cell *matHeaderCellDef style="width: 3%;">(29)</th>
                </ng-container>
                <!-- <ng-container matColumnDef="F31">
                    <th mat-header-cell *matHeaderCellDef style="width: 3%;">(31)</th>
                </ng-container> -->
                <!-- Main data -->
                <ng-container matColumnDef="stt">
                    <td mat-cell *matCellDef="let row;" style="width: 3%;">{{row.stt}}</td>
                    <td mat-footer-cell *matFooterCellDef></td>
                </ng-container>
                <ng-container matColumnDef="agencyName" [attr.colspan]="3">
                    <td mat-cell *matCellDef="let row;" style="width: 11.2%;text-align: start;"><p style="margin: 0px 5px 0px 5px;">{{row.agencyName}}</p></td>
                    <td mat-footer-cell *matFooterCellDef>Tổng cộng</td>
                </ng-container>
                <ng-container matColumnDef="totalHSTN">
                    <td mat-cell *matCellDef="let row;" style="width: 3%;" class="cell_info">
                        {{formatNumber(row.totalDossier)}}</td>
                    <td mat-footer-cell *matFooterCellDef class="cell_info">{{sum("totalDossier",
                        dataProcessingSource)}}</td>
                </ng-container>
                <ng-container matColumnDef="totalTTHC">
                    <td mat-cell *matCellDef="let row;" style="width: 3%;" class="cell_info">
                        {{formatNumber(row.totalProcedure)}}</td>
                    <td mat-footer-cell *matFooterCellDef class="cell_info">{{sum("totalProcedure",
                        dataProcessingSource)}}</td>
                </ng-container>
                <ng-container matColumnDef="directPartHSTN">
                    <td mat-cell *matCellDef="let row;" style="width: 3%;" class="cell_info">
                        {{formatNumber(row.dossierLevel2)}}</td>
                    <td mat-footer-cell *matFooterCellDef class="cell_info">{{sum("dossierLevel2",
                        dataProcessingSource)}}</td>
                </ng-container>
                <ng-container matColumnDef="directPartHSTNBCCI">
                    <td mat-cell *matCellDef="let row;" style="width: 3%;" class="cell_info">
                        {{formatNumber(row.dossierBCCILevel2)}}</td>
                    <td mat-footer-cell *matFooterCellDef class="cell_info">{{sum("dossierBCCILevel2",
                        dataProcessingSource)}}</td>
                </ng-container>
                <ng-container matColumnDef="directPartTTHC">
                    <td mat-cell *matCellDef="let row;" style="width: 3%;" class="cell_info">
                        {{formatNumber(row.procedureLevel2)}}</td>
                    <td mat-footer-cell *matFooterCellDef class="cell_info">{{sum("procedureLevel2",
                        dataProcessingSource)}}</td>
                </ng-container>
                <ng-container matColumnDef="onlinePartHSTNDirect">
                    <td mat-cell *matCellDef="let row;" style="width: 3%;" class="cell_info">
                        {{formatNumber(row.dossierLevel3)}}</td>
                    <td mat-footer-cell *matFooterCellDef class="cell_info">{{sum("dossierLevel3",
                        dataProcessingSource)}}</td>
                </ng-container>
                <ng-container matColumnDef="onlinePartHSTNOnline">
                    <td mat-cell *matCellDef="let row;" style="width: 3%;" class="cell_info">
                        {{formatNumber(row.dossierOnlineLevel3)}}</td>
                    <td mat-footer-cell *matFooterCellDef class="cell_info">{{sum("dossierOnlineLevel3",
                        dataProcessingSource)}}</td>
                </ng-container>
                <ng-container matColumnDef="onlinePartHSTNBCCI">
                    <td mat-cell *matCellDef="let row;" style="width: 3%;" class="cell_info">
                        {{formatNumber(row.dossierBCCILevel3)}}</td>
                    <td mat-footer-cell *matFooterCellDef class="cell_info">{{sum("dossierBCCILevel3",
                        dataProcessingSource)}}</td>
                </ng-container>
                <ng-container matColumnDef="onlinePartTTHC">
                    <td mat-cell *matCellDef="let row;" style="width: 3%;" class="cell_info">
                        {{formatNumber(row.procedureLevel3)}}</td>
                    <td mat-footer-cell *matFooterCellDef class="cell_info">{{sum("procedureLevel3",
                        dataProcessingSource)}}</td>
                </ng-container>
                <ng-container matColumnDef="wholeProcessHSTNDirect">
                    <td mat-cell *matCellDef="let row;" style="width: 3%;" class="cell_info">
                        {{formatNumber(row.dossierLevel4)}}</td>
                    <td mat-footer-cell *matFooterCellDef class="cell_info">{{sum("dossierLevel4",
                        dataProcessingSource)}}</td>
                </ng-container>
                <ng-container matColumnDef="wholeProcessHSTNonline">
                    <td mat-cell *matCellDef="let row;" style="width: 3%;" class="cell_info">
                        {{formatNumber(row.dossierOnlineLevel4)}}</td>
                    <td mat-footer-cell *matFooterCellDef class="cell_info">{{sum("dossierOnlineLevel4",
                        dataProcessingSource)}}</td>
                </ng-container>
                <ng-container matColumnDef="wholeProcessHSTNBCCI">
                    <td mat-cell *matCellDef="let row;" style="width: 3%;" class="cell_info">
                        {{formatNumber(row.dossierBCCILevel4)}}</td>
                    <td mat-footer-cell *matFooterCellDef class="cell_info">{{sum("dossierBCCILevel4",
                        dataProcessingSource)}}</td>
                </ng-container>
                <ng-container matColumnDef="wholeProcessTTHC">
                    <td mat-cell *matCellDef="let row;" style="width: 3%;" class="cell_info">
                        {{formatNumber(row.procedureLevel4)}}</td>
                    <td mat-footer-cell *matFooterCellDef class="cell_info">{{sum("procedureLevel4",
                        dataProcessingSource)}}</td>
                </ng-container>
                <ng-container matColumnDef="recordsExist">
                    <td mat-cell *matCellDef="let row;" style="width: 3%;" class="cell_info">
                        {{formatNumber(row.dossierExist)}}</td>
                    <td mat-footer-cell *matFooterCellDef class="cell_info">{{sum("dossierExist",
                        dataProcessingSource)}}</td>
                </ng-container>
                <ng-container matColumnDef="receiveMCDT">
                    <td mat-cell *matCellDef="let row;" style="width: 3%;" class="cell_info">
                        {{formatNumber(row.dossierReceived)}}</td>
                    <td mat-footer-cell *matFooterCellDef class="cell_info">{{sum("dossierReceived",
                        dataProcessingSource)}}</td>
                </ng-container>
                <ng-container matColumnDef="onlineReception">
                    <td mat-cell *matCellDef="let row;" style="width: 3%;" class="cell_info">
                        {{formatNumber(row.dossierOnlineReceived)}}</td>
                    <td mat-footer-cell *matFooterCellDef class="cell_info">{{sum("dossierOnlineReceived",
                        dataProcessingSource)}}</td>
                </ng-container>
                <ng-container matColumnDef="redirectAheadOfTime">
                    <td mat-cell *matCellDef="let row;" style="width: 3%;" class="cell_info">
                        {{formatNumber(row.resolvedEarlyLevel2)}}</td>
                    <td mat-footer-cell *matFooterCellDef class="cell_info">{{sum("resolvedEarlyLevel2",
                        dataProcessingSource)}}</td>
                </ng-container>
                <ng-container matColumnDef="redirectOnTime">
                    <td mat-cell *matCellDef="let row;" style="width: 3%;" class="cell_info">
                        {{formatNumber(row.resolvedOnTimeLevel2)}}</td>
                    <td mat-footer-cell *matFooterCellDef class="cell_info">{{sum("resolvedOnTimeLevel2",
                        dataProcessingSource)}}</td>
                </ng-container>
                <ng-container matColumnDef="redirectLate">
                    <td mat-cell *matCellDef="let row;" style="width: 3%;" class="cell_info">
                        {{formatNumber(row.resolvedOverdueLevel2)}}</td>
                    <td mat-footer-cell *matFooterCellDef class="cell_info">{{sum("resolvedOverdueLevel2",
                        dataProcessingSource)}}</td>
                </ng-container>
                <ng-container matColumnDef="onlineAheadOfTime">
                    <td mat-cell *matCellDef="let row;" style="width: 3%;" class="cell_info">
                        {{formatNumber(row.resolvedEarlyLevel3)}}</td>
                    <td mat-footer-cell *matFooterCellDef class="cell_info">{{sum("resolvedEarlyLevel3",
                        dataProcessingSource)}}</td>
                </ng-container>
                <ng-container matColumnDef="onlineOnTime">
                    <td mat-cell *matCellDef="let row;" style="width: 3%;" class="cell_info">
                        {{formatNumber(row.resolvedOnTimeLevel3)}}</td>
                    <td mat-footer-cell *matFooterCellDef class="cell_info">{{sum("resolvedOnTimeLevel3",
                        dataProcessingSource)}}</td>
                </ng-container>
                <ng-container matColumnDef="onlineLate">
                    <td mat-cell *matCellDef="let row;" style="width: 3%;" class="cell_info">
                        {{formatNumber(row.resolvedOverdueLevel3)}}</td>
                    <td mat-footer-cell *matFooterCellDef class="cell_info">{{sum("resolvedOverdueLevel3",
                        dataProcessingSource)}}</td>
                </ng-container>
                <ng-container matColumnDef="wholeProcessAheadOfTime">
                    <td mat-cell *matCellDef="let row;" style="width: 3%;" class="cell_info">
                        {{formatNumber(row.resolvedEarlyLevel4)}}</td>
                    <td mat-footer-cell *matFooterCellDef class="cell_info">{{sum("resolvedEarlyLevel4",
                        dataProcessingSource)}}</td>
                </ng-container>
                <ng-container matColumnDef="wholeProcessOnTime">
                    <td mat-cell *matCellDef="let row;" style="width: 3%;" class="cell_info">
                        {{formatNumber(row.resolvedOnTimeLevel4)}}</td>
                    <td mat-footer-cell *matFooterCellDef class="cell_info">{{sum("resolvedOnTimeLevel4",
                        dataProcessingSource)}}</td>
                </ng-container>
                <ng-container matColumnDef="wholeProcessLate">
                    <td mat-cell *matCellDef="let row;" style="width: 3%;" class="cell_info">
                        {{formatNumber(row.resolvedOverdueLevel4)}}</td>
                    <td mat-footer-cell *matFooterCellDef class="cell_info">{{sum("resolvedOverdueLevel4",
                        dataProcessingSource)}}</td>
                </ng-container>
                <ng-container matColumnDef="solving">
                    <td mat-cell *matCellDef="let row;" style="width: 3%;" class="cell_info">
                        {{formatNumber(row.unresolved)}}</td>
                    <td mat-footer-cell *matFooterCellDef class="cell_info">{{sum("unresolved",
                        dataProcessingSource)}}</td>
                </ng-container>
                <ng-container matColumnDef="settlementRate">
                    <td mat-cell *matCellDef="let row;" style="width: 3%;" class="cell_info">
                        {{formatNumber(row.resolveRate)}}%</td>
                    <td mat-footer-cell *matFooterCellDef class="cell_info">{{calculateResolutionRate()}}</td>
                </ng-container>
                <!--  -->
                <tr mat-header-row *matHeaderRowDef="['A1', 'A2', 'A4', 'A5']"></tr>
                <tr mat-header-row *matHeaderRowDef="['B1', 'B2', 'B3', 'B4', 'B5', 'B6', 'B7']"></tr>
                <tr mat-header-row *matHeaderRowDef="['C1', 'C2', 'C3']"></tr>
                <tr mat-header-row
                    *matHeaderRowDef="['D1', 'D2', 'D3', 'D4', 'D5', 'D6', 'D7', 'D8', 'D9', 'D10', 'D11', 'D12', 'D13', 'D14', 'D15', 'D16', 'D17', 'D18']">
                </tr>
                <tr mat-header-row *matHeaderRowDef="['E1', 'E2', 'E3', 'E4', 'E5', 'E6', 'E7', 'E8', 'E9']">
                </tr>
                <tr mat-header-row
                    *matHeaderRowDef="['F1', 'F2', 'F4', 'F5', 'F6', 'F7', 'F8', 'F9', 'F10', 'F11', 'F12', 'F13', 'F14', 'F15', 'F16', 'F17', 'F18', 'F19', 'F20', 'F21', 'F22', 'F23', 'F24', 'F25', 'F26', 'F27', 'F28', 'F29', 'F30']">
                </tr>
                <tr mat-row *matRowDef="let row; columns: displayedProcessingColumns;"></tr>
                <tr mat-footer-row *matFooterRowDef="displayedProcessingColumns; sticky: true"></tr>
            </table>
        </div>
    </div>
</div>