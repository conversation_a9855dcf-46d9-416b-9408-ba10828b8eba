import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { KeycloakService } from 'keycloak-angular';
import { EnvService } from 'src/app/core/service/env.service';
import { DeploymentService } from 'src/app/data/service/deployment.service';
import { QbhRemindWorkService } from 'src/app/data/service/qbh-remind-work/qbh-remind-work.service';
import { QniRemindWorkService } from 'src/app/data/service/qni-remind-work/qni-remind-work.service';
import { KggRemindWorkService } from 'src/app/data/service/kgg-remind-work/kgg-remind-work.service';
import { UserService } from 'src/app/data/service/user.service';
import {QtiRemindWorkService} from "data/service/qti-remind-work/qti-remind-work.service";
import {NotificationV2Service} from 'data/service/etl-data/notificationV2.service';
import { DatePipe } from '@angular/common';
import { StringifyOptions } from 'querystring';
import { ViewDialogInfoQBHModel, ViewInfoQBHComponent } from './dialogs/view-info-qbh.component';
import { MatDialog } from '@angular/material/dialog';
import { QBHStatisticService } from 'src/app/data/service/qbh-statistics/qbh-statistic.service';
import { WarnLoginUserService } from 'src/app/data/service/basecat/warn-login-user.service';
import { NotificationService } from 'src/app/data/service/notification.service';

@Component({
  selector: 'app-dashboard',
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.scss']
})

export class DashboardComponent implements OnInit {
  config = this.envService.getConfig();
  env = this.deploymentService.getAppDeployment().env;
  showRemindWork: boolean = false; //IGATESUPP-29256 thêm để check hiển thị từ cấu hình trong quản trị người dùng
  userId: string;
  showRemindWorkAtHome_QNI: boolean = false;// Check QNI bật tham số mới chạy luồng này;
  showRemindWorkAtHome_QBH: boolean = false;// IGATESUPP-53479 QBH bật tham số mới chạy luồng này;
  showRemindWorkAtHome_KGG: boolean = false;// IGATESUPP-57415
  showRemindWorkAtHome_QTI: boolean = false; // IGATESUPP-74830
  showRemindWorkAtHome_KHA: boolean = false; // IGATESUPP-136677
  remindWork473 = false;
  // showNotification8h = 0; //IGATESUPP-98728
  userName: string; //IGATESUPP-98728
  showInfo: string;
  showNotification8h = this.deploymentService.env.OS_QBH.notification8h === 1 ? true : false;
  snackbarService: any;
  countDossierFail;
  listDosseirFail;
  fromDate;
  toDate;
  todayDateString;
  userNameTemp;
  qtiThietLapCanhbao = this.deploymentService.getAppDeployment()?.QtiThietLapCanhbao == 1 ? 1 : 0;
  userAgency = JSON.parse(localStorage.getItem('userAgency'));
  constructor(
    private envService: EnvService,
    private router: Router,
    private deploymentService: DeploymentService,
    private remindService: QniRemindWorkService,
    private remindQBHService: QbhRemindWorkService,
    private remindQTIService: QtiRemindWorkService,
    private remindKGGService: KggRemindWorkService,
    private keycloakService: KeycloakService,
    private userService: UserService,
    private notificationV2Service: NotificationV2Service,
    private datePipe: DatePipe,
    private dialog: MatDialog,
    private qbhStatisticService: QBHStatisticService,
    private warnLoginUserService: WarnLoginUserService,
    private notifyService: NotificationService,
  ) {
    this.showRemindWorkAtHome_QNI = this.env?.OS_QNI?.showRemindWorkAtHome == true ? true : false ;
    this.showRemindWorkAtHome_QBH = this.deploymentService.env.OS_QBH.remindNotify.showRemindWorkAtHome == true ? true : false ;
    this.showRemindWorkAtHome_KGG = this.deploymentService.env.OS_KGG.remindNotify.showRemindWorkAtHome == true ? true : false ;
    this.showRemindWorkAtHome_QTI =  this.deploymentService.env?.OS_QTI?.qtiDashboardMotcua == true ? true : false;
    this.showRemindWorkAtHome_KHA =  this.deploymentService.env?.OS_KHA?.DashboardMotcua == true ? true : false;
    this.remindWork473 = this.deploymentService.env?.remindWork473 || false;
    


    if(this.showRemindWorkAtHome_QNI || this.showRemindWorkAtHome_QBH || this.showRemindWorkAtHome_KGG || this.showRemindWorkAtHome_KHA){
      this.getUserAccount();
    }
    if(this.showRemindWorkAtHome_QTI) {  
        this.router.navigate(['/qti-remind-work']);

    }
    if(this.showRemindWorkAtHome_KHA) {  
        this.router.navigate(['/dashboard-kha']);

    }
    this.getUserAccount();
   }

  async ngOnInit(): Promise<void> {
    if(this.showNotification8h) {
      await this.getUserAccountQbh();
    }
    if(this.qtiThietLapCanhbao == 1){
      console.log('qtiThietLapCanhbao')
      this.checkWarnLoginQti();
    }
  }
  getUserRemind(userId: string){
    this.remindService.getUserRemind(userId).subscribe(data => {
      // Lấy thông tin Bật/ Tắt trong cấu hình tài khoản người dùng
        this.showRemindWork = data.showRemindHome;
        if(this.showRemindWork)
        this.router.navigate(['/qni-remind-work']);
    });

  }
  getUserRemindQBH(userId: string){
    this.remindQBHService.getUserRemind(userId).subscribe(data => {
      // Lấy thông tin Bật/ Tắt trong cấu hình tài khoản người dùng
        this.showRemindWork = data.showRemindHome;
        if(this.showRemindWork)
        this.router.navigate(['/qbh-remind-work']);
    });

  }
  getUserRemindKGG(userId: string){
    this.remindKGGService.getUserRemind(userId).subscribe(data => {
      // Lấy thông tin Bật/ Tắt trong cấu hình tài khoản người dùng
        this.showRemindWork = data.showRemindHome;
        if(this.showRemindWork)
        this.router.navigate(['/kgg-remind-work']);
    });
  }

  getUserRemind473(userId: string){
    this.notificationV2Service.getDashboardUserRemind(userId).subscribe(data => {
      const showRemind = data?.showRemind || false;
      if (showRemind) {
        this.router.navigate(['/remind-work-473']);
      }
    });
  }

  getUserAccount() {
    this.keycloakService.loadUserProfile().then(user => {
      this.userId = user['attributes'].user_id[0];
      this.userService.getUserInfo(this.userId).subscribe(data => {
        console.log('userInfo',data )
        if(this.showRemindWorkAtHome_QBH){
          this.getUserRemindQBH(data.account.id);
        }else if(this.showRemindWorkAtHome_QNI){
        this.getUserRemind(data.account.id);
        } else if(this.showRemindWorkAtHome_KGG){
          this.getUserRemindKGG(data.account.id);
        } else if (this.remindWork473) {
          this.getUserRemind473(this.userId);
        }
      }, error => {
        console.log(error);
      });
    });
  }
  async getShowInfoQbh() {
    let userAgencyId: any = '';
    let isAdmin = false;
    let isRootAgency = false;
    let isPermissionDisplayPVUB: boolean = false;
    let userNameTemp;
    const today = new Date();
    this.todayDateString = this.datePipe.transform(today, 'dd/MM/yyyy');
    
    const permissions = this.userService.getUserPermissions();

    for (const p of permissions) {
      if (p.permission.code === 'admin' || p.permission.code === 'oneGateQbhShowVPUB') {
        isAdmin = true;
        isPermissionDisplayPVUB = true;
        localStorage.setItem('checkIsAdmin', 'true');
        break;
      }
    }
    if(isAdmin || isPermissionDisplayPVUB) {
      this.userNameTemp = "";
      userAgencyId = this.config.rootAgency.id;
      isRootAgency = true;
    } else {
      this.userNameTemp = this.userName;
      userAgencyId = this.userAgency.id;
      isRootAgency = false;
    }
    this.countDossierFailQbh(userAgencyId, isRootAgency);
    // this.showInfo = "Tính đến ngày " + todayDateString + " số hồ sơ tự động tiếp nhận bị thất bại liên quan đến Anh/Chị " + userNameTemp + "là " + this.countDossierFail + " hồ sơ."; 

    
    
  }
   async getUserAccountQbh() {
     this.keycloakService.loadUserProfile().then(user => {
      // tslint:disable-next-line: no-string-literal
      this.userId = user['attributes'].user_id[0];
      this.userName =  user.username;
      this.getShowInfoQbh();
    });
    
  }

  openViewInfoNotiQbh(todayDateString: string, userNameTemp: string, info: string, listDosseirFail: any = [], countDossierFail : number, fromDate: string, toDate: string) {
    const dialogData = new ViewDialogInfoQBHModel(todayDateString, userNameTemp, info, listDosseirFail, countDossierFail, fromDate, toDate);
    const dialogRef = this.dialog.open(ViewInfoQBHComponent, {
        width: '95%',
        height: '20%',
        data: dialogData,
        disableClose: true,
        autoFocus: false
    });
    dialogRef.afterClosed().subscribe(() => {});
  }
  async countDossierFailQbh(userAgencyId: any, isRootAgency: boolean) {
    const date = new Date();
    date.setDate(date.getDate() - 1)
    this.fromDate = this.datePipe.transform(date, 'yyyy-MM-dd') + 'T19:00:00.000Z' ;
    this.toDate = this.datePipe.transform(new Date(), 'yyyy-MM-dd') + 'T03:00:0.000Z';
    let searchString = '?fromDate=' + this.fromDate
          + '&toDate=' + this.toDate
          + '&userAgencyId=' + userAgencyId
          + '&isRootAgency=' + isRootAgency;
       await this.qbhStatisticService.getTotalDossierFailLog8h(searchString).subscribe(res => {
        // this.ELEMENTDATA =[];
          if (res.resultMessageFaild === "")  {
            this.countDossierFail = 0;
            this.listDosseirFail = "";
          } else {
            this.countDossierFail = res.resultMessageFaild.split(',').length;
            this.listDosseirFail = res.resultMessageFaild;
          }
          this.openViewInfoNotiQbh(this.todayDateString,this.userNameTemp,this.showInfo,this.listDosseirFail, this.countDossierFail, this.fromDate, this.toDate);
      }, err => {
          console.log(err);
          // this.isLoading = false;
          this.snackbarService.openSnackBar(0, 'Có lỗi xảy ra', err.error.message, 'error_notification', this.config.expiredTime);
      });      
  }

  async checkWarnLoginQti(){
    try{
    let userId = localStorage.getItem("tempUID");
    let agencyId = JSON.parse(localStorage.getItem("userAgency"))?.id;
    const keycloakInstance: Keycloak.KeycloakInstance = this.keycloakService.getKeycloakInstance();
    this.warnLoginUserService.getAllLogWarnLoginByUserId(userId).subscribe(info =>{
      let userInfoWarn = info?.userLogWarnLogin;

      console.log('userLogWarnLogin:',info)
   
      const maxtimeLogin = info?.maxTimes;
      const contentWarn = info?.warnContent;
      const countLogin = userInfoWarn?.countLog?.counter || 0;
      let   currSession = userInfoWarn?.countLog?.sessionId || "";
      const phoneNumber = userInfoWarn?.phoneNumber ||  "";
      const email = userInfoWarn?.email ||  "";
   
      if(countLogin >= maxtimeLogin){
        // Gửi thông báo qua sms và email
        if(phoneNumber != '')
          this.sendSMS(agencyId,contentWarn,phoneNumber);
        if(email != ''){
          this.sendEmail(agencyId,contentWarn,email);
        }
      }else
      if(currSession !== keycloakInstance?.sessionId){
        currSession = keycloakInstance?.sessionId;
        this.warnLoginUserService.plusTimeLogin(userId,currSession).subscribe(x=>{});
      }
    });
  }catch(e){console.log("loi ",e)}
 }
 sendSMS(agencyId:any, content:any , phoneNumber:any ){
   console.log('sendSMS:',phoneNumber)
   return this.notifyService.sendSmsAndWriteLog(agencyId, this.env?.subsystem?.id, content, "", [phoneNumber]);
 }
 sendEmail(agencyId:any ,content:any ,email:any){
   return this.notifyService.sendEmail(agencyId, this.env?.subsystem?.id, content, 'Cảnh báo vượt số lần đăng nhập trong ngày', [email]);
 }
}
