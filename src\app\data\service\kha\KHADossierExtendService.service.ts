import {Injectable} from '@angular/core';
import {HttpClient, HttpHeaders} from '@angular/common/http';
import {EnvService} from 'core/service/env.service';
import {ApiProviderService} from 'core/service/api-provider.service';
import {Observable} from 'rxjs';

@Injectable({
  providedIn: 'root'
})

export class KHADossierExtendService {
  language = localStorage.getItem('language') === 'vi' ? 228 : 46;
  config = this.envService.getConfig();
  private padmanURL = this.apiProviderService.getUrl('digo', 'padman');

  constructor(
    private http: HttpClient,
    private envService: EnvService,
    private apiProviderService: ApiProviderService,
  ) {
  }

  getDossierConsultations(dossierId): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');
    // return this.http.get('http://localhost:8081/' + 'dossier-kha/--list-consultations?dossier-id=' + dossierId, {headers}).pipe();
    return this.http.get(this.padmanURL + '/dossier-kha/--list-consultations?dossier-id=' + dossierId, {headers}).pipe();
  }

  saveDossierConsultation(dossierId, body): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');
    // return this.http.post('http://localhost:8081/' + 'dossier-kha/--save-consultation?dossier-id=' + dossierId, body, {headers}).pipe();
    return this.http.post(this.padmanURL + '/dossier-kha/--save-consultation?dossier-id=' + dossierId, body, {headers}).pipe();
  }

  updateDossierConsultation(dossierId, consultationId, body): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');
    // return this.http.put('http://localhost:8081/' + 'dossier-kha/--update-consultation?dossier-id=' + dossierId + '&consultation-id=' + consultationId, body, {headers}).pipe();
    return this.http.put(this.padmanURL + '/dossier-kha/--update-consultation?dossier-id=' + dossierId + '&consultation-id=' + consultationId, body, {headers}).pipe();
  }

  deleteDossierConsultation(dossierId, dossierConsultationId): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');

    return this.http.get(this.padmanURL + '/dossier-kha/--delete-consultation?dossier-id=' + dossierId + '&consultation-id=' + dossierConsultationId, {headers}).pipe();
  }

  getDossierConsultationById(dossierId, dossierConsultationId): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');
    // return this.http.get('http://localhost:8081/' + '/dossier-kha/--get-consultation?dossier-id=' + dossierId + '&consultation-id=' + dossierConsultationId, {headers}).pipe();
    return this.http.get(this.padmanURL + '/dossier-kha/--get-consultation?dossier-id=' + dossierId + '&consultation-id=' + dossierConsultationId, {headers}).pipe();
  }

}
