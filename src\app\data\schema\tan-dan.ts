export interface IDinhKem {
  Ten?: string;
  Base64: any;
  Link?: string;
}

export interface INguoiNop {
  Ten?: string;
  CMND?: string;
  SoDienThoai?: string;
  DiaChi?: string;
}

export interface INguoiKy {
  Ten?: string;
  TaiKhoan?: string;
}

export interface IDonVi {
  Ten?: string;
  MaDinhDanh?: string;
}

export interface IVBDuThao {
  TrichYeu?: string;
  NgayTao?: string;
  DinhKem?: IDinhKem[];
}

export interface IRequestTanDanDoc {
  IDHoSo?:string;
  MaHoSo?: string;
  ChuHoSo?:INguoiNop;
  NguoiNop?:INguoiNop;
  NguoiKy?:INguoiKy;
  NguoiTrinhKy?:INguoiKy;
  DonVi?: IDonVi;
  VanBanDuThao?:IVBDuThao;
  ThanhPhanHoSo?: any[];
  TenHoSo?: string;
  ThuTuc?: string;
  ThamSoAn?: string;
  HccLinkAPI?: string;
  LoaiHSLienthong?: string;
}