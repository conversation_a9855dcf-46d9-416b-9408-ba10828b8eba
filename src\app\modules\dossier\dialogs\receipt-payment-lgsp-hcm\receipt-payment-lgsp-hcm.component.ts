import { Component, OnInit, Inject } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { PadsvcService } from 'src/app/data/service/padsvc/padsvc.service';

@Component({
  selector: 'app-receipt-payment-lgsp-hcm',
  templateUrl: './receipt-payment-lgsp-hcm.component.html',
  styleUrls: ['./receipt-payment-lgsp-hcm.component.scss', '/src/app/app.component.scss', '/src/app/shared/scss/form-field-outline.scss']
})
export class ReceiptPaymentLGSPHCMComponent implements OnInit {
  dossierPayment:any = {};
  orderInfo:any = {};
  constructor(
    public dialogRef: MatDialogRef<ReceiptPaymentLGSPHCMComponent>,
    @Inject(MAT_DIALOG_DATA) public data: ConfirmReceiptPaymentLGSPHCMDialogModel,
    private padsvcService: PadsvcService,
  ) {
    this.dossierPayment = data.dossierPayment;
  }

  ngOnInit(): void {
    this.getOrderInfo();
  }

  getOrderInfo(){
    const initData: any = {
      transactionNo: this.dossierPayment?.paymentData?.payTransId,
      orderId: this.dossierPayment?.paymentData?.orderId
    };
    this.padsvcService.postPaymentPlatformLGSPHCMOrderInfo(initData).subscribe(
      (data) => {
        console.log(data);
        this.orderInfo = {};
        if (data.error_code === 'SUCCESSFUL') {
          this.orderInfo = data.data;
        }
      },
      (err) => {
      }
    );
  }

  onConfirm() {
    this.dialogRef.close(true);
  }

  onDismiss() {
    this.dialogRef.close();
  }
}

export class ConfirmReceiptPaymentLGSPHCMDialogModel {
  constructor(public dossierPayment: any) {
  }
}
