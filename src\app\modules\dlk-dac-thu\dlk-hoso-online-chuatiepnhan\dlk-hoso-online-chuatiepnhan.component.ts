import { DatePipe } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { FormControl, FormGroup } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import { MatTableDataSource } from '@angular/material/table';
import { DeploymentService } from 'src/app/data/service/deployment.service';
import { SnackbarService } from 'src/app/data/service/snackbar/snackbar.service';
import * as tUtils from 'src/app/data/service/thoai.service';
import { StatisticsService } from 'data/service/statistics/statistics.service';
import { EnvService } from 'core/service/env.service';
import { Router } from '@angular/router';
import { ExportExcelService } from "data/service/export-excel/export-excel.service";
import { DLKStatisticsService } from 'src/app/data/service/dlk-statistics/dlk-statistics.service';
import { MainService } from 'src/app/data/service/main/main.service';

// export interface DossierAgency {
//   id: string;
//   code: string;
//   tag: Array<any>;
//   level: number;
//   name: string;
// }

export interface DossierStatistic {
  no: number;
  agencyName: string;
  code: string;
  procedureCode: string;
  procedureName: string;
  appliedDate: string;
  appliedTime: string;
}

@Component({
  selector: 'app-dlk-hoso-online-chuatiepnhan',
  templateUrl: './dlk-hoso-online-chuatiepnhan.component.html',
  styleUrls: ['./dlk-hoso-online-chuatiepnhan.component.scss']
})

export class DlkHoSoOnlineChuaTiepNhanComponent implements OnInit {

  config = this.envService.getConfig();
  // excel
  excelData = [];
  agencyNameExcel: any = '';
  columns: any[];
  footerData: any[][] = [];
  waitingDownloadExcel = false;

  dataSource: MatTableDataSource<DossierStatistic>;
  ELEMENTDATA: DossierStatistic[] = [];
  displayedColumns: string[] = ['no'
    , 'agencyName'
    , 'code'
    , 'procedureCode'
    , 'procedureName'
    , 'appliedDate'
    , 'appliedTime'
  ];

  searchForm = new FormGroup({
    fromDate: new FormControl(''),
    toDate: new FormControl(''),
    agency: new FormControl(''),
    agencyCtrl: new FormControl('')
  });

  userAgency?: any = JSON.parse(localStorage.getItem('userAgency'));
  agencyId = '';
  agencyList: Array<any> = [];
  isLoading = false;
  isAgencyListFull = false;
  agencyPage = 0;
  timeOutSearch = null;
  msTimeOutSearch = this.deploymentService.env.statistics.msTimeOutSearch;
  agencyLevel0 = this.deploymentService.env.OS_KHA.agencyLevel0;
  agencyLevel1 = this.deploymentService.env.OS_KHA.agencyLevel1;
  agencyLevel2 = this.deploymentService.env.OS_KHA.agencyLevel2;
  agencyByLevels = [[], [], []];
  agencyByLevelsDone = false;
  procedureLevel4 = this.deploymentService.env.OS_KHA.procedureLevel4;
  procedureLevel3 = this.deploymentService.env.OS_KHA.procedureLevel3;
  procedureLevel2 = this.deploymentService.env.OS_KHA.procedureLevel2;
  rootAgencyId = this.deploymentService.env.rootAgencyId;
  statisticKGGGetSuppendedCancelled = this.deploymentService.env.OS_KGG.statisticKGGGetSuppendedCancelled ?? true;
  donvibaocaoTag = this.deploymentService.env.OS_KGG.donvibaocaoTag;
  agencyReport: any = null;

  size = 10;
  page = 1;
  countResult = 0;
  pgSizeOptions = this.config.pageSizeOptions;
  paginationType = this.deploymentService.env.statistics.paginationType;
  pageIndex = 1;
  parentAgency = '';

  constructor(
    private router: Router,
    private envService: EnvService,
    private snackbarService: SnackbarService,
    private deploymentService: DeploymentService,
    private statisticsService: DLKStatisticsService,
    private datePipe: DatePipe,
    private exportExcel: ExportExcelService,
    private mainService: MainService,
    private dialog: MatDialog
  ) {
    if (this.userAgency !== null) {
      this.rootAgencyId = !!this.userAgency?.parent?.id ? this.userAgency?.parent?.id : this.config.rootAgency.id;
      this.parentAgency = this.userAgency?.parent?.id ? this.userAgency?.parent?.id : this.userAgency.id;
    } else {
      this.rootAgencyId = this.config.rootAgency.id;
    }
    this.dataSource = new MatTableDataSource(this.ELEMENTDATA);
  }

  pageTitle = {
    vi: `Thống kê hồ sơ trực tuyến chưa tiếp nhận`,
    en: `Statistics online dossier not yet received`
  };
  ngOnInit(): void {
    this.mainService.setPageTitle(this.pageTitle[localStorage.getItem('language')]);
    const d = tUtils.newDate();
    this.searchForm.patchValue({
      fromDate: d.getFullYear() + '-' + ('0' + (d.getMonth() + 1)).slice(-2) + '-' + '01',
      toDate: d.getFullYear() + '-' + ('0' + (d.getMonth() + 1)).slice(-2) + '-' + ('0' + d.getDate()).slice(-2),
      hinhthucBaocao: '0',
    });
    this.getAgencyList(true);
  }

  changePageOrSize(obj) {
    if (tUtils.hasOwnProperty(obj, 'currentPage')) {
      this.page = obj.currentPage;
      this.pageIndex = obj.currentPage;
    }
    if (tUtils.hasOwnProperty(obj, 'itemsPerPage')) {
      this.size = obj.itemsPerPage;
    }
    if (tUtils.hasOwnProperty(obj, 'totalItems')) {
      this.countResult = obj.totalItems;
    } else {
      this.paginate(this.pageIndex, 0);
    }
  }

  searchAgencyList() {
    clearTimeout(this.timeOutSearch);
    this.timeOutSearch = setTimeout(() => {
      this.getAgencyList(false);
    }, this.msTimeOutSearch);
  }

  getAgencyList(scroll: boolean) {
    if (!scroll) {
      this.agencyPage = 0;
      this.isAgencyListFull = false;
      this.agencyList = [];
    }

    const formObj = this.searchForm.getRawValue();
    const searchString = '?page=' + this.agencyPage + '&size=50&status=1&spec=slice'
      + '&parent-id=' + this.parentAgency + '&keyword=' + encodeURIComponent(formObj.agencyCtrl.trim());
    this.statisticsService.getListAgencyWithParent(searchString).subscribe(data => {
      this.agencyList = [...this.agencyList, ...data.content];
      this.agencyList = Object.values(this.agencyList.reduce((acc, cur) => Object.assign(acc, {[cur.id]: cur}), {}));
      this.agencyPage++;
      this.isAgencyListFull = data.last;
      if(!this.isAgencyListFull){
        this.getAgencyList(true);
      }
    }, err => {
      console.log(err);
    });
  }

  clearAgencyList() {
    this.searchForm.patchValue({
      agencyCtrl: ''
    });
    this.getAgencyList(false);
  }

  searchBtn() {
    this.pageIndex = 1;
    this.page = 1;
    this.size = Number(this.size);
    const searchString = this.generateSearchString('page', (this.pageIndex - 1), this.size);
    this.getListProcedure(searchString);
  }

  getListProcedure(searchString) {
    this.statisticsService.getDossierStatisticDossierUnreceivedDetail(searchString).subscribe(data => {
      this.ELEMENTDATA = [];
      this.countResult = data.totalElements;
      if (data.content) {
        let array = data.content;
        for (let i = 0; i < array.length; i++) {
          this.ELEMENTDATA.push({
            no: this.page * this.size + i - this.size + 1,
            agencyName: array[i].agencyName ? array[i].agencyName : array[i].agency.parent?.name[0]?.name,
            code: array[i].dossierCode,
            procedureCode: array[i].procedureCode,
            procedureName: array[i].procedureName,
            appliedDate: array[i].appliedDate,
            appliedTime: array[i].appliedTime,
          });
        }
      }
      this.dataSource.data = this.ELEMENTDATA;
      this.isLoading = false;
    }, () => {
      this.isLoading = false;
    });
  }

  paginate(event: any, type) {
    switch (type) {
      case 0:
        this.pageIndex = event;
        this.page = event;
        const searchString = this.generateSearchString('page', (this.pageIndex - 1), this.size);
        this.getListProcedure(searchString);
        this.router.navigate([], {
          queryParams: {
            page: this.pageIndex,
            size: this.size
          }
        });
        break;
      case 1:
        this.pageIndex = 1;
        this.page = 1;
        const searchString2 = this.generateSearchString('page', (this.pageIndex - 1), this.size);
        this.getListProcedure(searchString2);
        this.router.navigate([], {
          queryParams: {
            page: this.pageIndex,
            size: this.size
          }
        });
        break;
    }
  }

  generateSearchString(spec, page, size) {
    const formObj = this.searchForm.getRawValue();

    if (formObj.toDate === '' || formObj.fromDate === '') {
      const message = {
        vi: 'Ngày thống kê không được để trống!',
        en: 'Statistical date cannot be blank!'
      };
      this.snackbarService.openSnackBar(0, '', message[localStorage.getItem('language')], 'error_notification', 3000);
      return;
    } else if (new Date(formObj.toDate) < new Date(formObj.fromDate)) {
      const message = {
        vi: 'Ngày bắt đầu phải nhỏ hơn hoặc bằng ngày kết thúc!',
        en: 'Start date must be less than or equal to end date!'
      };
      this.snackbarService.openSnackBar(0, '', message[localStorage.getItem('language')], 'error_notification', 3000);
      return;
    }

    this.isLoading = true;
    if (formObj.agency) {
      this.agencyId = formObj.agency;
    } else {
      this.agencyId = this.parentAgency;
    }

    let searchString = '?agency-id=' + this.agencyId
      + '&page=' + page
      + '&size=' + size
      + '&spec=' + spec;
    if (formObj.agency) {
      searchString += '&agencyStr=';
    } else {
      searchString += '&agencyStr=' + this.genAgencyStr(this.agencyList);
    }
    return searchString;
  }

  getListDossierAllExcel() {
    this.waitingDownloadExcel = true;
    const searchString = this.generateSearchString(null, null, null);
    this.statisticsService.getDossierStatisticDossierUnreceivedDetailExport(searchString).subscribe(data => {
      this.excelData = [];
      for (let i = 0; i < data.length; i++) {
        const itemExcel: any = {
          no: i + 1 + "",
          agencyName: data[i].agencyName ? data[i].agencyName : data[i].agency.parent?.name[0]?.name,
          code: data[i].dossierCode,
          procedureCode: data[i].procedureCode,
          procedureName: data[i].procedureName,
          appliedDate: data[i].appliedDate,
          appliedTime: data[i].appliedTime,
        };
        this.excelData.push(itemExcel);
      }
      this.exportToExcelAll();
      this.waitingDownloadExcel = false;
      this.isLoading = false;
    }, error => {
      console.log(error);
      this.waitingDownloadExcel = false;
      this.isLoading = false;
    });
  }

  exportToExcelAll() {
    const newDate = tUtils.newDate();
    let title = '';

    const formObj = this.searchForm.getRawValue();
    let fromDateExcel = '';
    let from = [];
    let toDateExcel = '';
    let to = [];
    let name = '';
    let subTitle = '';

    if (formObj.fromDate != null || formObj.fromDate !== '') {
      fromDateExcel = formObj.fromDate;
    }
    if (formObj.toDate != null || formObj.toDate !== '') {
      toDateExcel = formObj.toDate;
    }

    if (localStorage.getItem('language') === 'vi') {
      title = 'THỐNG KÊ HỒ SƠ ONLINE CHƯA TIẾP NHẬN';
      name = 'bao_cao_ho_so_online_chua_tiep_nhan' + this.datePipe.transform(newDate, 'dd/MM/yyyy');
    } else if (localStorage.getItem('language') === 'en') {
      name = 'online_unreceived_report ' + this.datePipe.transform(newDate, 'dd/MM/yyyy');
      title = 'ONLINE DOSSIER UNRECEIVED REPORT';
    }
    this.statisticsService.exportAsExcelOnlineUnreceivedReport(title, subTitle, this.excelData, name, 'ProcessingReport');
  }

  formatNumber(n: number) {
    return (n == null || false) ? '' : n.toString().replace(/(.)(?=(\d{3})+$)/g, '$1.');
  }

  sum(key: keyof any) {
    return this.formatNumber(this.dataSource.data.reduce((a, b) => a + (Number(b[key]) || 0), 0));
  }

  sumWithoutFormat(key: keyof any) {
    return this.dataSource.data.reduce((a, b) => a + (Number(b[key]) || 0), 0);
  }

  genAgencyStr(agencyList) {
    var agencyStr = "";
    for (var i = 0; i < agencyList.length; i++) {
      agencyStr += agencyList[i].id + ',';
    }
    return agencyStr;
  }
}
