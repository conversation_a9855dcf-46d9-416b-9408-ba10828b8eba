<div fxLayout="row" fxLayoutAlign="space-between" fxLayout.sm="column" fxLayout.xs="column">
  <div fxFlex="25" fxFlex.sm="100" fxFlex.xs="100">
    <div  mat-button class="title-reminder" fxLayout="row" fxLayoutAlign="space-between">
      <div class="content">
        <span fxFlex="100" class="title"><span i18n>Danh sách công việc</span> (<a class="count-task">1</a>)</span>
        <div *ngIf="expandReminderMenu === true then isExpand; else isNotExpand"></div>
        <ng-template #isExpand>
          <mat-icon fxFlex="10">expand_less</mat-icon>
        </ng-template>
        <ng-template #isNotExpand>
          <mat-icon fxFlex="10">expand_more</mat-icon>
        </ng-template>
      </div>
    </div>
    <div class="menu_reminder">
      <mat-accordion class="advanced-box" multi>
        <mat-expansion-panel class="panel" [(expanded)]="expandReminderMenu">
          <a id="submenu" mat-button active-link="active">
            <mat-icon>receipt</mat-icon><span class="submenuTitle">Chờ tiếp nhận lịch hẹn</span>&nbsp;<span class="count">{{countResult}}</span>
          </a>
        </mat-expansion-panel>
      </mat-accordion>
    </div>
  </div>
  <div fxFlex="100" fxFlex.sm="100" fxFlex.xs="100" class="search">
    <h2>Tiếp nhận lịch hẹn Chứng thực điện tử</h2>

    <div class="prc_searchbar">
      <form [formGroup]="searchForm" (submit)="onConfirmSearch()" class="searchForm">
        <div fxLayout="row" fxLayout.xs="row" fxLayout.sm="row" class="formFieldItems" fxLayoutGap="10px">
          <mat-form-field appearance="outline" fxFlex.gt-sm="49.5" fxFlex.gt-xs="49.5" fxFlex="grow" fxFlex.gt-md="24">
            <mat-label>Nhập từ khóa</mat-label>
            <input type="text" matInput formControlName="identityNumber">
          </mat-form-field>

          <!-- Chọn từ ngày -->
          <mat-form-field appearance="outline" fxFlex.gt-md="24" fxFlex.gt-sm="32" fxFlex.gt-xs="49.5" fxFlex="grow">
            <mat-label>Ngày đăng ký (Từ ngày)</mat-label>
            <input matInput [matDatepicker]="pickerAppointmentFrom" formControlName="appointmentFromDate">
            <mat-datepicker-toggle matSuffix [for]="pickerAppointmentFrom"></mat-datepicker-toggle>
            <mat-datepicker #pickerAppointmentFrom></mat-datepicker>
          </mat-form-field>

          <!-- Chọn đến ngày -->
          <mat-form-field appearance="outline" fxFlex.gt-md="24" fxFlex.gt-sm="32" fxFlex.gt-xs="49.5" fxFlex="grow">
            <mat-label>Ngày đăng ký (Đến ngày)</mat-label>
            <input matInput [matDatepicker]="pickerAppointmentTo" formControlName="appointmentToDate">
            <mat-datepicker-toggle matSuffix [for]="pickerAppointmentTo"></mat-datepicker-toggle>
            <mat-datepicker #pickerAppointmentTo></mat-datepicker>
          </mat-form-field>

          <button mat-flat-button fxFlex.gt-sm="16" fxFlex.gt-xs="49.5" fxFlex='grow' class="searchBtn" type="submit">
            <mat-icon>search</mat-icon><span i18n>Tìm kiếm</span>
          </button>
        </div>
      </form>
    </div>
  </div>
</div>

<!--<div fxLayout="row" fxLayoutAlign="center">-->
<!--  <div fxFlex="grow">-->
<!--    <div class="top-controlcancel" fxLayout="row" fxLayoutAlign="start">-->
<!--      <button style = "margin-left: 10px;" mat-flat-button  class="primary-btn" (click)="exportExcelDossier()" >-->
<!--        <mat-icon style = "margin-right: 10px;">cloud_download</mat-icon>-->
<!--        <span>Xuất excel hồ sơ trên trang</span>-->
<!--      </button>-->
<!--    </div>-->
<!--  </div>-->
<!--</div>-->

<div fxLayout="row" fxLayoutAlign="center">
  <div class="frm_main" fxFlex="grow">
    <div class="top-control">
    </div>
    <div class="searchtbl">
      <table mat-table [dataSource]="dataSource">
        <ng-container matColumnDef="stt">
          <mat-header-cell *matHeaderCellDef i18n> STT </mat-header-cell>
          <mat-cell *matCellDef="let row" data-label="STT"> {{row.stt}} </mat-cell>
        </ng-container>

        <ng-container matColumnDef="applicantName">
          <mat-header-cell *matHeaderCellDef>Họ và tên</mat-header-cell>
          <mat-cell *matCellDef="let row" i18n-data-label data-label="Người nộp">
                        <span> <span > {{row?.applicant?.fullname}} </span><br>
                        </span>
          </mat-cell>
        </ng-container>

        <ng-container matColumnDef="applicantPhone">
          <mat-header-cell *matHeaderCellDef i18n>Số điện thoại</mat-header-cell>
          <mat-cell *matCellDef="let row" i18n-data-label data-label="Số điện thoại">
                        <span> <span> {{row?.applicant?.phoneNumber}} </span><br>
                        </span>
          </mat-cell>
        </ng-container>

        <ng-container matColumnDef="procedureName">
          <mat-header-cell *matHeaderCellDef i18n>Thủ tục</mat-header-cell>
          <mat-cell *matCellDef="let row" i18n-data-label data-label="Thủ tục">
                        <span class="procedureName"  #tooltip="matTooltip" matTooltip="{{row?.procedureName}}">

                            <span>{{row?.procedureName}}</span>
                        </span>
          </mat-cell>
        </ng-container>



        <ng-container matColumnDef="applicantDate">
          <mat-header-cell *matHeaderCellDef>Ngày giờ hẹn</mat-header-cell>
          <mat-cell *matCellDef="let row" data-label="Ngày nộp">
            <span>{{ convertFormatDateTime(row?.appointmentDateTime) }}</span>
          </mat-cell>
        </ng-container>

        <ng-container matColumnDef="status">
          <mat-header-cell *matHeaderCellDef i18n class="status-header">Trạng thái</mat-header-cell>
          <mat-cell class="status-cell" *matCellDef="let row" i18n-data-label data-label="Trạng thái">
            <span class="cell_cto" [ngClass]="getAppointmentStatusClass(row?.appointmentDateTime, row?.appointmentTime)">
              {{ checkAppointmentStatus(row?.appointmentDateTime, row?.appointmentTime) }}
            </span>
          </mat-cell>

        </ng-container>


        <ng-container matColumnDef="action">
          <mat-header-cell *matHeaderCellDef i18n>Thao tác</mat-header-cell>
          <mat-cell *matCellDef="let row" i18n-data-label data-label="Thao tác">
            <button mat-icon-button [matMenuTriggerFor]="actionMenu">
              <mat-icon>more_horiz</mat-icon>
            </button>
            <mat-menu #actionMenu="matMenu" xPosition="before">
              <!--              IGATESUPP-115852 bước 1- tải eform-->
              <button mat-menu-item class="menuAction"
                      (click)="receivingDossier(row?.id,row?.procedureId,row?.procedureProcessDefinitionId,row?.eFormId,row?.applicantEFormId)">
                <mat-icon>create_new_folder</mat-icon><span>Tiếp nhận lịch hẹn</span>
              </button>
              <!--              IGATESUPP-115852 bước 1 từ chối lịch hẹn-->
              <button mat-menu-item class="menuAction" (click) = "cancelDialog(row?.id)">
                <mat-icon>do_disturb</mat-icon><span>Từ chối lịch hẹn</span>
              </button>
            </mat-menu>
          </mat-cell>
        </ng-container>

        <mat-header-row *matHeaderRowDef="displayedColumns"></mat-header-row>

        <mat-row *matRowDef="let row; columns: displayedColumns;"></mat-row>
      </table>
      <pagination-slice id="pgnx" [itemsPerPage]="size" [currentPage]="page" [totalItems]="countResult" [pageSizeOptions]="[].concat(pgSizeOptions)"
                        [dataSource]="ELEMENTDATA" (change)="changePageOrSize($event)" [type]="paginationType">
      </pagination-slice>
    </div>
  </div>
</div>
