<div class="content">
    <div fxLayout="row" fxLayoutAlign="center">
        <div class="head" fxFlex.gt-sm="88" fxFlex="95">
            <h2 id="title" i18n>Tra cứu thông tin đăng ký doanh nghiệp</h2>
            <span class="fill_space"></span>
        </div>
    </div>
    <br>
    <div fxLayout="row" fxLayoutAlign="center">
        <div fxFlex.gt-sm="88" fxFlex="95" class="formFieldOutline">
            <form [formGroup]="searchForm" class="publishSearch">
                <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="row">
                    <mat-form-field class="input-nofull-width" appearance="outline" fxFlex='grow'>
                        <mat-label i18n>Tra cứu:</mat-label>
                        <mat-select (selectionChange)="typeChange($event)" style="height: 15px;" [(value)]="selected">
                            <mat-option value="0" style="font-size: 15ox;" i18n>Thông tin chi tiết doanh nghiệp</mat-option>
                            <mat-option value="1" style="font-size: 15ox;" i18n>Danh sách hồ sơ tiếp nhận trong khoảng thời gian</mat-option>
                            <mat-option value="2" style="font-size: 15ox;" i18n>Danh sách hồ sơ xử lý trong ngày</mat-option>
                        </mat-select>
                    </mat-form-field>
                </div>
                <div *ngIf="selected == 0">
                    <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="row" style="padding-top: 15px; padding-bottom: 20px;">
                        <div fxFlex.gt-sm="25" fxFlex.lt-md="25">
                            <mat-form-field class="input-nofull-width" appearance="outline" fxFlex='grow'>
                                <mat-label i18n>Mã số doanh nghiệp:</mat-label>
                                <input #keyword2 matInput formControlName="id" placeholder="" maxlength="15">
                            </mat-form-field>
                        </div>
                        <div fxFlex.gt-sm="0.7" fxFlex.lt-md="1"></div>
                        <button mat-flat-button class="searchBt" (click)="onKeySearch(0)" >
                            <span i18n>Tìm kiếm</span>
                        </button>
                        <div fxFlex.gt-sm="0.7" fxFlex.lt-md="1"></div>
                    </div>
                    <div fxLayout="row" fxLayoutAlign="center">
                        <span class="subTitle">{{request0}}</span>
                    </div>
                </div>


                <div *ngIf="selected == 1">
                    <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="row" style="padding-top: 15px;">
                        <div fxFlex.gt-sm="25" fxFlex.lt-md="25">
                            <mat-form-field appearance="outline" style="width: 100%;">
                                <mat-label i18n>Từ ngày</mat-label>
                                <input matInput [matDatepicker]="pickerfromDate04" formControlName="fromDate"  [max]="maxFromDate" required (dateChange)="triggerToDate($event)">
                                <mat-datepicker-toggle matSuffix [for]="pickerfromDate04"></mat-datepicker-toggle>
                                <mat-datepicker #pickerfromDate04></mat-datepicker>
                            </mat-form-field>
                        </div>
                        <div fxFlex.gt-sm="0.7" fxFlex.lt-md="1"></div>
                        <div fxFlex.gt-sm="25" fxFlex.lt-md="25">
                            <mat-form-field appearance="outline" style="width: 100%;">
                                <mat-label i18n>Đến ngày</mat-label>
                                <input matInput [matDatepicker]="pickertoDate04" formControlName="toDate" [min]="searchForm.controls['fromDate'].value" [max]="maxToDate" required >
                                <mat-datepicker-toggle matSuffix [for]="pickertoDate04"></mat-datepicker-toggle>
                                <mat-datepicker #pickertoDate04></mat-datepicker>
                            </mat-form-field>
                        </div>
                        <div fxFlex.gt-sm="0.7" fxFlex.lt-md="1"></div>
                        <button mat-flat-button class="searchBt" (click)="onKeySearch(1)" style="height: 3.7em;">
                            <span i18n>Tìm kiếm</span>
                        </button>
                        <div fxFlex.gt-sm="0.7" fxFlex.lt-md="1"></div>
                    </div>
                    <div fxLayout="row" fxLayoutAlign="center">
                        <span class="subTitle">Lưu ý: <span class="tc-red fw-bold">{{request1}}</span></span>
                    </div>
                </div>

                <div *ngIf="selected == 2">
                    <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="row" style="padding-top: 15px;">
                        <div fxFlex.gt-sm="25" fxFlex.lt-md="25">
                            <mat-form-field appearance="outline" style="width: 100%;">
                                <mat-label i18n>Từ</mat-label>
                                <input matInput type="time" formControlName="fromTime">
                            </mat-form-field>
                        </div>
                        <div fxFlex.gt-sm="0.7" fxFlex.lt-md="1"></div>
                        <div fxFlex.gt-sm="25" fxFlex.lt-md="25">
                            <mat-form-field appearance="outline" style="width: 100%;">
                                <mat-label i18n>Đến</mat-label>
                                <input matInput type="time" formControlName="toTime">
                            </mat-form-field>
                        </div>
                        <div fxFlex.gt-sm="0.7" fxFlex.lt-md="1"></div>
                        <button mat-flat-button class="searchBt" (click)="onKeySearch(2)" style="height: 3.7em;">
                            <span i18n>Tìm kiếm</span>
                        </button>
                        <div fxFlex.gt-sm="0.7" fxFlex.lt-md="1"></div>
                    </div>
                    <div fxLayout="row" fxLayoutAlign="center">
                        <span class="subTitle">Lưu ý: <span class="tc-red fw-bold">{{request2}}</span></span>
                    </div>
                </div>

            </form>
        </div>
    </div>

    <div fxLayout="row" fxLayoutAlign="center">
        <div id="main" class="hidden" fxFlex.gt-sm="88" fxFlex="95">
            <ng-container *ngIf="type == 1">
                <p><span i18n>Tìm thấy</span> {{countResult}} <span i18n>kết quả</span> </p>
                <div class="mat-elevation-z2 frm_tbl">
                    <div class="hidden2">
                        <ng-container *ngIf="countResult > 0">
                            <table mat-table [dataSource]="dataSource" class="mat-elevation-z4">
                                <ng-container matColumnDef="stt">
                                    <mat-header-cell *matHeaderCellDef i18n>STT</mat-header-cell>
                                    <mat-cell *matCellDef="let element" data-label="STT" i18n-data-label>
                                        {{element.stt}}
                                    </mat-cell>
                                </ng-container>

                                <ng-container matColumnDef="inJournalNo">
                                    <mat-header-cell *matHeaderCellDef i18n>Mã số biên nhận của hồ sơ</mat-header-cell>
                                    <mat-cell *matCellDef="let element" data-label="Mã số biên nhận của hồ sơ" i18n-data-label>
                                        {{element.IN_JOURNAL_NO}}
                                    </mat-cell>
                                </ng-container>

                                <ng-container matColumnDef="documentType">
                                    <mat-header-cell *matHeaderCellDef i18n>Loại hồ sơ đăng ký</mat-header-cell>
                                    <mat-cell *matCellDef="let element" data-label="Loại hồ sơ đăng ký" i18n-data-label>
                                        {{element.DOCUMENT_TYPE}}
                                    </mat-cell>
                                </ng-container>

                                <ng-container matColumnDef="enterpriseCode">
                                    <mat-header-cell *matHeaderCellDef i18n>Mã số nội bộ</mat-header-cell>
                                    <mat-cell *matCellDef="let element" data-label="Mã số nội bộ" i18n-data-label>
                                        {{element.ENTERPRISE_CODE}}
                                    </mat-cell>
                                </ng-container>

                                <ng-container matColumnDef="enterpriseGDTCode">
                                    <mat-header-cell *matHeaderCellDef i18n>Mã số doanh nghiệp</mat-header-cell>
                                    <mat-cell *matCellDef="let element" data-label="Mã số doanh nghiệp" i18n-data-label>
                                        {{element.ENTERPRISE_GDT_CODE}}
                                    </mat-cell>
                                </ng-container>
                                <ng-container matColumnDef="name">
                                    <mat-header-cell *matHeaderCellDef i18n>Tên doanh nghiệp</mat-header-cell>
                                    <mat-cell *matCellDef="let element" data-label="Tên doanh nghiệp" i18n-data-label>
                                        {{element.NAME}}
                                    </mat-cell>
                                </ng-container>
                                <ng-container matColumnDef="siteID">
                                    <mat-header-cell *matHeaderCellDef i18n>Mã cơ quan cấp đăng ký
                                    </mat-header-cell>
                                    <mat-cell *matCellDef="let element" data-label="Mã cơ quan cấp đăng ký" i18n-data-label>
                                        <div [style.color]="element.color">
                                            {{element.SITE_ID}}
                                        </div>
                                    </mat-cell>
                                </ng-container>

                                <ng-container matColumnDef="receiptDate">
                                    <mat-header-cell *matHeaderCellDef i18n>Ngày tiếp nhận</mat-header-cell>
                                    <mat-cell *matCellDef="let element" data-label="Ngày tiếp nhận" i18n-data-label>
                                        <div [style.color]="element.color">
                                            {{element.RECEIPT_DATE | date:'dd/MM/yyyy HH:mm:ss' }}
                                        </div>
                                    </mat-cell>
                                </ng-container>

                                <ng-container matColumnDef="planDate">
                                    <mat-header-cell *matHeaderCellDef i18n>Ngày hẹn trả kết quả</mat-header-cell>
                                    <mat-cell *matCellDef="let element" data-label="Ngày hẹn trả kết quả" i18n-data-label>
                                        <div [style.color]="element.color">
                                            {{element.PLAN_DATE | date:'dd/MM/yyyy HH:mm:ss'}}
                                        </div>
                                    </mat-cell>
                                </ng-container>

                                <ng-container matColumnDef="processStatus">
                                    <mat-header-cell *matHeaderCellDef i18n>Tình trạng xử lý hồ sơ</mat-header-cell>
                                    <mat-cell *matCellDef="let element" data-label="Tình trạng xử lý hồ sơ" i18n-data-label>
                                        <div [style.color]="element.color">
                                            {{element.PROCESS_STATUS}}
                                        </div>
                                    </mat-cell>
                                </ng-container>

                                <ng-container matColumnDef="options" >
                                    <mat-header-cell *matHeaderCellDef i18n>Thao tác</mat-header-cell>
                                    <mat-cell *matCellDef="let element" data-label="Thao tác" i18n-data-label>
                                        <button mat-icon-button [matMenuTriggerFor]="menu"
                                            aria-label="Example icon-button with a menu">
                                            <mat-icon>more_horiz</mat-icon>
                                        </button>
                                        <mat-menu #menu="matMenu" xPosition="before">
                                            <button mat-menu-item (click)="showListFile(element.IN_JOURNAL_NO)">
                                                <mat-icon>assignment</mat-icon>
                                                <span i18n>Xem chi tiết</span>
                                            </button>
                                        </mat-menu>
                                    </mat-cell>
                                </ng-container>
                                <mat-header-row *matHeaderRowDef="displayedColumns"></mat-header-row>
                                <mat-row *matRowDef="let row; columns: displayedColumns;"></mat-row>
                            </table>
                            <div fxLayout="row" fxLayoutAlign="center">
                                <div class="frm_Pagination" fxFlex.gt-sm="88" fxFlex="95">
                                    <ul class="temp_Arr">
                                        <li
                                            *ngFor="let item of displayedColumns  | paginate: {itemsPerPage: size, currentPage: page, totalItems: countResult, id: 'pgnx'}">
                                        </li>
                                    </ul>
                                    <div class="pageSize" style="width: 100%;">
                                        <span i18n>Hiển thị </span>
                                        <mat-form-field appearance="outline">
                                            <mat-select [(value)]="size" (valueChange)="paginate(pageIndex, 1, 1)">
                                                <mat-option *ngFor='let opt of pgSizeOptions;' [value]="opt">{{opt}}
                                                </mat-option>
                                            </mat-select>
                                        </mat-form-field>
                                        <span i18n>trên </span>{{countResult}}<span i18n> bản ghi</span>
                                    </div>
                                    <div class="control" style="width: 100%;">
                                        <pagination-controls id="pgnx"
                                            (pageChange)="page = $event; paginate(page, 0, 1)" responsive="true"
                                            previousLabel="" nextLabel="">
                                        </pagination-controls>
                                    </div>
                                </div>
                            </div>
                        </ng-container>
                    </div>
                </div>
            </ng-container>
            <ng-container *ngIf="type == 0">
                <!-- <p><span>Tìm thấy</span> {{totalElements}} <span>kết quả</span> </p> -->
                <!-- 
                    <div *cdkVirtualFor="let item of items" class="example-item">{{item}}</div>
                  </cdk-virtual-scroll-viewport> -->
                <div class="mat-elevation-z2 frm_tbl">
                    <cdk-virtual-scroll-viewport itemSize="50" class="example-viewport">
                    <div class="hidden2" style="padding: 10px 0px;">
                        <ul>
                            <ng-container *ngIf="mainInformationcheck">
                                <span class="detail-title-row" i18n>THÔNG TIN CƠ BẢN</span>
                                <ng-container *ngFor="let item of mainInformation">
                                    <li *ngIf="item.ENTERPRISE_ID">
                                        <span class="detail-title" i18n>ID của doanh nghiệp:</span><span>
                                            {{item.ENTERPRISE_ID}}</span>
                                    </li>
                                    <li *ngIf="item.ENTERPRISE_GDT_CODE">
                                        <span class="detail-title" i18n>Mã số doanh nghiệp (Mã số ĐKKD và MST đã gộp làm
                                            một):</span><span> {{item.ENTERPRISE_GDT_CODE}}</span>
                                    </li>
                                    <li *ngIf="item.IMP_BUSINESS_CODE">
                                        <span class="detail-title" i18n>Số Giấy chứng nhận ĐKKD cũ:</span><span>
                                            {{item.IMP_BUSINESS_CODE}}</span>
                                    </li>
                                    <li *ngIf="item.ENTERPRISE_TYPE_ID">
                                        <span class="detail-title" i18n>Loại hình doanh nghiệp:</span><span>
                                            {{item.ENTERPRISE_TYPE_ID}}</span>
                                    </li>
                                    <li *ngIf="item.ENTERPRISE_TYPE_NAME">
                                        <span class="detail-title" i18n>Tên loại hình doanh nghiệp:</span><span>
                                            {{item.ENTERPRISE_TYPE_NAME}}</span>
                                    </li>
                                    <li *ngIf="item.NAME">
                                        <span class="detail-title" i18n>Tên tiếng Việt:</span><span>
                                            {{item.NAME}}</span>
                                    </li>
                                    <li *ngIf="item.SHORT_NAME">
                                        <span class="detail-title" i18n>Tên viết tắt:</span><span>
                                            {{item.SHORT_NAME}}</span>
                                    </li>
                                    <li *ngIf="item.NAME_F">
                                        <span class="detail-title" i18n>Tên bằng tiếng nước ngoài:</span><span>
                                            {{item.NAME_F}}</span>
                                    </li>
                                    <li *ngIf="item.FOUNDING_DATE">
                                        <span class="detail-title" i18n>Ngày thành lập:</span><span>
                                            {{item.FOUNDING_DATE | date:'dd/MM/yyyy'}}</span>
                                    </li>
                                    <li *ngIf="item.LAST_AMEND_DATE">
                                        <span class="detail-title" i18n>Ngày đăng ký thay đổi gần nhất:</span><span>
                                            {{item.LAST_AMEND_DATE | date:'dd/MM/yyyy'}}</span>
                                    </li>
                                    <li *ngIf="item.NUMBER_CHANGES">
                                        <span class="detail-title" i18n>Số lần đăng ký thay đổi:</span><span>
                                            {{item.NUMBER_CHANGES}}</span>
                                    </li>
                                    <li *ngIf="item.ENTERPRISE_STATUS_NAME">
                                        <span class="detail-title" i18n>Tình trạng hoạt động của doanh nghiệp:</span><span>
                                            {{item.ENTERPRISE_STATUS_NAME}}</span>
                                    </li>
                                    <li *ngIf="item.CAPITAL_AMOUNT">
                                        <span class="detail-title" i18n>Vốn điều lệ:</span><span>
                                            {{item.CAPITAL_AMOUNT}}</span>
                                    </li>
                                </ng-container>
                            </ng-container>
                            <ng-container *ngIf="hoadresscheck">
                                <span class="detail-title-row" i18n>ĐỊA CHỈ TRỤ SỞ CHÍNH</span>
                                <ng-container *ngFor="let item of hoadress">
                                    <li *ngIf="item.CityID">
                                        <span class="detail-title" i18n>Mã tỉnh/thành phố:</span><span>
                                            {{item.CityID}}</span>
                                    </li>
                                    <li *ngIf="item.CityName">
                                        <span class="detail-title" i18n>Tên tỉnh/thành phố:</span><span>
                                            {{item.CityName}}</span>
                                    </li>
                                    <li *ngIf="item.DistrictID">
                                        <span class="detail-title" i18n>Mã quận/huyện:</span><span>
                                            {{item.DistrictID}}</span>
                                    </li>
                                    <li *ngIf="item.DistrictName">
                                        <span class="detail-title" i18n>Tên quận/huyện:</span><span>
                                            {{item.DistrictName}}</span>
                                    </li>
                                    <li *ngIf="item.WardID">
                                        <span class="detail-title" i18n>Mã phường/xã:</span><span> {{item.WardID}}</span>
                                    </li>
                                    <li *ngIf="item.WardName">
                                        <span class="detail-title" i18n>Tên phường/xã:</span><span> {{item.WardName}}</span>
                                    </li>
                                    <li *ngIf="item.StreetNumber">
                                        <span class="detail-title" i18n>Địa chỉ số nhà, thôn ấp...:</span><span>
                                            {{item.StreetNumber}}</span>
                                    </li>
                                    <li *ngIf="item.AddressFullText">
                                        <span class="detail-title" i18n>Địa chỉ đầy đủ:</span><span>
                                            {{item.AddressFullText}}</span>
                                    </li>
                                </ng-container>
                            </ng-container>
                            <ng-container *ngIf="businessActivitiescheck">
                                <span class="detail-title-row" i18n>NGÀNH NGHỀ KINH DOANH</span>
                                <ng-container *ngFor="let item of businessActivities">
                                    <ng-container *ngFor="let item1 of item">
                                        <li *ngIf="item1.CODE">
                                            <span class="detail-title" i18n>Mã ngành:</span><span>
                                                {{item1.CODE}}</span>
                                        </li>
                                        <span class="detail-title" *ngIf="item1.NAME" i18n>Tên ngành:</span><span>
                                            {{item1.NAME}}</span>
                                        
                                        <div>
                                            <span class="detail-title" *ngIf="item1.MAIN === true" i18n>Là ngành chính.</span>
                                        </div>    
                                    </ng-container>
                                </ng-container>
                            </ng-container>
                            <ng-container *ngIf="membercheck">
                                <span class="detail-title-row" i18n>KHỐI DANH SÁCH THÀNH VIÊN GÓP VỐN</span>
                                <ng-container *ngFor="let item of member">
                                    <li *ngIf="item.MEMBER_NAME">
                                        <span class="detail-title" i18n>Tên thành viên:</span><span>
                                            {{item.MEMBER_NAME}}</span>
                                    </li>
                                    <li *ngIf="item.AMOUNT">
                                        <span class="detail-title" i18n>Vốn góp (VNĐ):</span><span> {{item.AMOUNT}}</span>
                                    </li>
                                    <li *ngIf="item.RATIO_PERCENT">
                                        <span class="detail-title" i18n>Tỷ lệ phần trăm vốn góp:</span><span>
                                            {{item.RATIO_PERCENT}}</span>
                                    </li>
                                    <li *ngIf="item.COUNTRY">
                                        <span class="detail-title" i18n>Quốc gia:</span><span> {{item.COUNTRY}}</span>
                                    </li>
                                </ng-container>
                            </ng-container>
                        </ul>
                    </div>
                </cdk-virtual-scroll-viewport>
                </div>
            </ng-container>
            <ng-container *ngIf="type == 2">

                <p><span i18n>Tìm thấy</span> {{countResult1}} <span i18n>kết quả</span> </p>
                <div class="mat-elevation-z2 frm_tbl">
                    <div class="hidden2">
                        <ng-container *ngIf="countResult1 > 0">
                            <table mat-table [dataSource]="dataSource1" class="mat-elevation-z4">
                                <ng-container matColumnDef="stt">
                                    <mat-header-cell *matHeaderCellDef i18n>STT</mat-header-cell>
                                    <mat-cell *matCellDef="let element" data-label="STT" i18n-data-label>
                                        {{element.stt}}
                                    </mat-cell>
                                </ng-container>

                                <ng-container matColumnDef="inJournalNo">
                                    <mat-header-cell *matHeaderCellDef i18n>Mã số biên nhận của hồ sơ</mat-header-cell>
                                    <mat-cell *matCellDef="let element" data-label="Mã số biên nhận của hồ sơ" i18n-data-label>
                                        {{element.IN_JOURNAL_NO}}
                                    </mat-cell>
                                </ng-container>

                                <ng-container matColumnDef="documentType">
                                    <mat-header-cell *matHeaderCellDef i18n>Loại hình đăng ký</mat-header-cell>
                                    <mat-cell *matCellDef="let element" data-label="Loại hình đăng ký" i18n-data-label>
                                        {{element.DOCUMENT_TYPE}}
                                    </mat-cell>
                                </ng-container>

                                <ng-container matColumnDef="enterpriseGDTCode">
                                    <mat-header-cell *matHeaderCellDef i18n>Mã số doanh nghiệp</mat-header-cell>
                                    <mat-cell *matCellDef="let element" data-label="Mã số doanh nghiệp" i18n-data-label>
                                        {{element.ENTERPRISE_GDT_CODE}}
                                    </mat-cell>
                                </ng-container>

                                <ng-container matColumnDef="nameTime">
                                    <mat-header-cell *matHeaderCellDef i18n>Tên doanh nghiệp</mat-header-cell>
                                    <mat-cell *matCellDef="let element" data-label="Tên doanh nghiệp" i18n-data-label>
                                        {{element.NAME}}
                                    </mat-cell>
                                </ng-container>
                                <ng-container matColumnDef="siteID">
                                    <mat-header-cell *matHeaderCellDef i18n>Mã cơ quan cấp đăng ký
                                    </mat-header-cell>
                                    <mat-cell *matCellDef="let element" data-label="Mã cơ quan cấp đăng ký" i18n-data-label>
                                        <div [style.color]="element.color">
                                            {{element.SITE_ID}}
                                        </div>
                                    </mat-cell>
                                </ng-container>

                                <ng-container matColumnDef="processStatus">
                                    <mat-header-cell *matHeaderCellDef i18n>Tình trạng xử lý hồ sơ</mat-header-cell>
                                    <mat-cell *matCellDef="let element" data-label="Tình trạng xử lý hồ sơ" i18n-data-label>
                                        <div [style.color]="element.color">
                                            {{element.PROCESS_STATUS}}
                                        </div>
                                    </mat-cell>
                                </ng-container>

                                <ng-container matColumnDef="processedDate">
                                    <mat-header-cell *matHeaderCellDef i18n>Ngày thay đổi tình trạng hồ sơ</mat-header-cell>
                                    <mat-cell *matCellDef="let element" data-label="Ngày thay đổi tình trạng hồ sơ" i18n-data-label>
                                        <div [style.color]="element.color">
                                            {{element.PROCESSED_DATE | date:'dd/MM/yyyy HH:mm:ss'}}
                                        </div>
                                    </mat-cell>
                                </ng-container>

                                <!-- <ng-container matColumnDef="options">
                                    <mat-header-cell *matHeaderCellDef i18n>Thao tác</mat-header-cell>
                                    <mat-cell *matCellDef="let element" data-label="Thao tác" i18n-data-label>
                                        <button mat-icon-button [matMenuTriggerFor]="menu"
                                            aria-label="Example icon-button with a menu">
                                            <mat-icon>more_horiz</mat-icon>
                                        </button>
                                        <mat-menu #menu="matMenu" xPosition="before">
                                            <button mat-menu-item (click)="showListFile(element.IN_JOURNAL_NO)">
                                                <mat-icon>assignment</mat-icon>
                                                <span>Xem chi tiết</span>
                                            </button>
                                        </mat-menu>
                                    </mat-cell>
                                </ng-container> -->
                                <mat-header-row *matHeaderRowDef="displayedColumns1"></mat-header-row>
                                <mat-row *matRowDef="let row; columns: displayedColumns1;"></mat-row>
                            </table>
                            <div fxLayout="row" fxLayoutAlign="center">
                                <div class="frm_Pagination" fxFlex.gt-sm="88" fxFlex="95">
                                    <ul class="temp_Arr">
                                        <li
                                            *ngFor="let item of displayedColumns1  | paginate: {itemsPerPage: size, currentPage: page, totalItems: countResult, id: 'pgnx'}">
                                        </li>
                                    </ul>
                                    <div class="pageSize" style="width: 100%;">
                                        <span i18n>Hiển thị </span>
                                        <mat-form-field appearance="outline">
                                            <mat-select [(value)]="size" (valueChange)="paginate(pageIndex, 1, 2)">
                                                <mat-option *ngFor='let opt of pgSizeOptions;' [value]="opt">{{opt}}
                                                </mat-option>
                                            </mat-select>
                                        </mat-form-field>
                                        <span i18n>trên </span>{{countResult1}}<span i18n> bản ghi</span>
                                    </div>
                                    <div class="control" style="width: 100%;">
                                        <pagination-controls id="pgnx"
                                            (pageChange)="page = $event; paginate(page, 0, 2)" responsive="true"
                                            previousLabel="" nextLabel="">
                                        </pagination-controls>
                                    </div>
                                </div>
                            </div>
                        </ng-container>
                    </div>
                </div>
            </ng-container>
        </div>
    </div>
</div>
<br>