// ================================= searchForm
::ng-deep .sector .mat-tab-body-content {
    padding-top: 15px !important;
    height: unset;
    overflow: unset;
  }
  ::ng-deep .frm_searchbar .searchForm .mat-form-field-appearance-outline .mat-form-field-outline {
    color: transparent;
  }
  
  ::ng-deep .frm_searchbar .searchForm .mat-form-field-appearance-outline .mat-form-field-outline-thick {
    color: #dddddd;
  }
  
  ::ng-deep .frm_searchbar .searchForm .mat-form-field-appearance-outline .mat-form-field-outline {
    background-color: #eaebeb;
    border-radius: 5px;
  }
  
  ::ng-deep .frm_searchbar .searchForm .searchBtn {
    margin-top: 0.3em;
    background-color: #ce7a58;
    color: #fff;
    height: 3.2em;
  }
  
  ::ng-deep .frm_searchbar .mat-form-field-appearance-outline .mat-form-field-infix {
    padding: 0.8em 0;
  }
  
  ::ng-deep .frm_searchbar .mat-form-field-label-wrapper {
    top: -1em;
  }
  
  ::ng-deep .frm_searchbar .mat-form-field.mat-focused .mat-form-field-label {
    color: #ce7a58;
    font-size: 18px;
    margin-bottom: 1em;
  }
  
  ::ng-deep
  .frm_searchbar
  .mat-form-field-appearance-outline.mat-form-field-can-float.mat-form-field-should-float
  .mat-form-field-label {
    color: #ce7a58;
    font-size: 18px;
    margin-bottom: 1em;
  }
  
  .sector .mat-header-row .mat-header-cell {
    margin: 5px;
  }
  
  .sector  .mat-cell {
    margin: 5px;
  }
  
  
  ::ng-deep .mat-primary .mat-option.mat-selected:not(.mat-option-disabled) {
    color: #ce7a58 !important;
  }
  ::ng-deep .mat-form-field.mat-focused.mat-primary .mat-select-arrow {
    color: #ce7a58;
  }
  
  // ================================= frm_main
  .frm_main {
    border-radius: 4px;
    padding: 1em;
    box-shadow: 0px 1px 15px rgba(69, 65, 78, 0.19);
  }
  
  .frm_main .btn_add {
    background-color: #e8e8e8;
    color: #666;
    float: right;
  }
  
  .frm_main .space {
    float: right;
  }
  
  .frm_main .btn_add .mat-icon {
    color: #ce7a58;
  }
  
  // ================================= frm_tbl_sector
  .frm_tbl_sector {
    margin-top: 3.5em;
  }
  
  .frm_tbl_sector table {
    border-radius: 4px;
    border: 1px solid #ececec;
    width: 100%;
  }
  
  ::ng-deep .frm_tbl_sector .mat-header-row {
    background-color: #e8e8e8;
  }
  
  ::ng-deep .frm_tbl_sector .mat-header-row .mat-header-cell {
    color: #495057;
    font-size: 14px;
    // display: grid;
  }
  
  ::ng-deep .frm_tbl_sector .mat-header-row .mat-header-cell p {
    margin-bottom: 0;
    font-weight: 400;
    font-style: italic;
  }
  
  ::ng-deep .frm_tbl_sector .mat-column-stt {
    flex: 0 0 5%;
  }
  
  ::ng-deep .frm_tbl_sector .mat-column-code {
    flex: 0 0 25%;
  }
  
  ::ng-deep {
    .mat-tooltip {
      font-size: 13px;
      max-width: unset !important;
      overflow-wrap: anywhere;
    }
  }
  
  ::ng-deep .frm_tbl_sector .mat-column-name {
    flex: 1 0 5%;
    // text-decoration: none;
    // color: #333;
    // display: -webkit-box;
    // -webkit-line-clamp: 4;
    // -webkit-box-orient: vertical;
    // width: 100%;
    // overflow: hidden;
    // text-overflow: ellipsis;
    // overflow-wrap: anywhere;
  }
  
  ::ng-deep .frm_tbl_sector .mat-column-status {
    flex: 0 0 10%;
    float: right;
  }
  
  ::ng-deep .frm_tbl_sector .mat-column-action {
    flex: 0 0 10%;
    float: right;
  }
  
  ::ng-deep .frm_tbl_sector .btn_downloadForm {
    padding: 0;
    color: #ce7a58;
  }
  
  ::ng-deep .frm_tbl_sector .cell_code {
    color: #ce7a58;
  }
  
  ::ng-deep .frm_tbl_sector .btn_downloadForm .mat-button-wrapper {
    display: flex;
  }
  
  ::ng-deep .frm_tbl_sector .btn_downloadForm .mat-button-wrapper .download_icon .mat-icon {
    vertical-align: middle;
    margin-right: 0.2em;
    background-color: #ce7a58;
    color: #fff;
    border-radius: 50%;
    padding: 0.2em;
    transform: scale(0.8);
  }
  
  ::ng-deep .frm_tbl_sector .btn_downloadForm .mat-button-wrapper span {
    align-self: center;
  }
  
  ::ng-deep .frm_tbl_sector .mat-row {
    border: none;
  }
  
  ::ng-deep .frm_tbl_sector .mat-row:nth-child(even) {
    background-color: #fafafa;
  }
  
  ::ng-deep .frm_tbl_sector .mat-row:nth-child(odd) {
    background-color: #fff;
  }
  
  ::ng-deep .menuAction {
    font-weight: 500;
  }
  
  ::ng-deep .menuAction .mat-icon {
    color: #ce7a58;
  }
  
  @media screen and (max-width: 600px) {
    .frm_tbl_sector .mat-header-row {
      display: none;
    }
  
    .frm_tbl_sector .mat-table {
      border: 0;
      vertical-align: middle;
    }
  
    .frm_tbl_sector .mat-table caption {
      font-size: 1em;
    }
  
    .frm_tbl_sector .mat-table .mat-row {
      border-bottom: 5px solid #ddd;
      display: block;
      min-height: unset;
    }
  
    .frm_tbl_sector .mat-table .mat-cell {
      border-bottom: 1px solid #ddd;
      display: block;
      font-size: 14px;
      text-align: right;
      margin-bottom: 4%;
      padding: 0 0.5em;
    }
  
    .frm_tbl_sector .mat-table .mat-cell:before {
      content: attr(data-label);
      float: left;
      font-weight: 500;
      font-size: 14px;
    }
  
    .frm_tbl_sector .mat-table .mat-cell:last-child {
      border-bottom: 0;
    }
  
    .frm_tbl_sector .mat-table .mat-cell:first-child {
      margin-top: 4%;
    }
  
    ::ng-deep .frm_tbl_sector .mat-column-status {
      float: unset;
    }
  
    ::ng-deep .frm_tbl_sector .mat-column-action {
      float: unset;
    }
  
    ::ng-deep .frm_tbl_sector .mat-row:nth-child(even) {
      background-color: unset;
    }
  
    ::ng-deep .frm_tbl_sector .mat-row:nth-child(odd) {
      background-color: unset;
    }
  }
  
  ::ng-deep .prc_searchbar .searchForm .formFieldItems {
    flex-wrap: wrap;
  }
  
  .formInsideTab div > *{
    margin-left: 5px;
    margin-right: 5px;
  }
  
  .formInsideTab div > :first-child{
    margin-left: 0px;
  }
  
  .formInsideTab div > :last-child{
    margin-right: 0px;
  }
  
  .formInsideTab button{
    background-color: #E8E8E8;
    color: #ce7a58;
  }

  .ellipsis {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
  }
