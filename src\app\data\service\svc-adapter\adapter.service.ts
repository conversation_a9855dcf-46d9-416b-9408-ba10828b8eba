import { Observable } from 'rxjs';
import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { ApiProviderService } from 'src/app/core/service/api-provider.service';
import { DeploymentService } from '../deployment.service';
import { EnvService } from 'src/app/core/service/env.service';

@Injectable({
  providedIn: 'root'
})
export class AdapterService {

  constructor(
    private http: HttpClient,
    private apiProvider: ApiProviderService,
    private envService: EnvService,
    private deploymentService: DeploymentService
  ) {}

  result: boolean;
  env = this.deploymentService.getAppDeployment()?.env;
  private adapter = this.apiProvider.getUrl('digo', 'adapter');
  private adapterUrl = this.apiProvider.getUrl('digo', 'adapter');
  private padsvcSubsystem: string = this.envService.getConfig().subsystemWebPadsvcId;

  postVNPTPaymentBillInit(body): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    return this.http.post<any>(this.adapter + '/payment-platform/--get-bill', body, { headers });
  }

  getMinistryQuestion(agencyId, subsystemId): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    const token = localStorage.getItem('OAuth2TOKEN');
    headers = headers.append('Authorization', 'Bearer ' + token);
    return this.http.get(this.adapter + '/npadsvc/--question-ministry?subsystem-id=' + subsystemId + '&agency-id=' + agencyId, { headers });
  }

  getQuestionLGSPHCM(agencyId, subsystemId, configId): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    const token = localStorage.getItem('OAuth2TOKEN');
    headers = headers.append('Authorization', 'Bearer ' + token);
    return this.http.get(this.adapter + '/lgsp-hcm-question-answer/--get-question-lgsp-hcm?configId='+ configId +'&subsystem-id=' + subsystemId + '&agency-id=' + agencyId, { headers });
  }

  postSMSByAgency(agencyId, subsystemId, content, listPhoneNumber): Observable<any> {
    const formData: FormData = new FormData();
    formData.append('agencyId', agencyId);
    formData.append('subsystemId', subsystemId);
    formData.append('content', content);
    listPhoneNumber.forEach(email => {
      formData.append('phoneNumber', email);
    });
    return this.http.post<any>(this.adapter + '/sms-brandname/--send-by-agency', formData).pipe();
  }

  getVGCAAdapterCallBackUrl(fileId,filename,accountId?):string{
    if(fileId == undefined || fileId == null) fileId = "";
    if(filename == undefined || filename == null) filename = "";
    const userId = accountId || "";
    return this.adapter + `/vgca/--upload-signed-file?file-id=${fileId}&name=${filename}&user-id=${userId}`;
  }

  signDocumentBySmartCA(fileId: string, position: string, page: number, username: string, password: string, visibleType?: string, signature?: any): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    if(!visibleType){
      visibleType = "TEXT_ONLY";
    }
    const requestBody = {
      signatureFileId: fileId,
      position: position,
      page: page,
      username: username,
      password: password,
      visibleType: visibleType,
      signature: signature
    }
    return this.http.post(this.adapter + `/smart-ca/--sign`, requestBody,{ headers });
  }

  VNPTSimSignDocument(data): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));

    const formData: FormData = new FormData();
    formData.append('agencyId', data.agencyId);
    formData.append('subsystemId', data.subsystemId);
    formData.append('fileId', data.fileId);
    if (Number(data.signType) !== 0) {
      formData.append('imageId', data.imageId);
    }
    // formData.append('imageId', data.imageId);
    formData.append('signType', data.signType);
    formData.append('phone', data.phone);
    formData.append('messageDisplay', data.messageDisplay);
    formData.append('reason', data.reason);
    formData.append('location', data.location);
    formData.append('signPosition', data.signPosition);
    formData.append('textSearch', data.textSearch);
    formData.append('autoSign', data.autoSign);
    formData.append('signPage', data.signPage);
    formData.append('side', "BOTTOM");
    formData.append('width', "70");
    formData.append('height', "35");
    return this.http.post<any>(this.adapter + '/digital-signature/--sign', formData, { headers });
  }

  getBudgetDossierList(config, fromDate, toDate): Observable<any> {
    const token = localStorage.getItem('OAuth2TOKEN');
    let headers = new HttpHeaders();
    headers = headers.append('Authorization', 'Bearer ' + token);
    headers = headers.set('Accept-Language', localStorage.getItem('language'));

    const params = `${config}&from-date=${fromDate}&to-date=${toDate}&size=1000`;
    return this.http.get<any>(this.adapter + '/budget/--list' + params, { headers });
  }

  /*
  * @author: duypd.hcm
  * @description: viết lại hàm lấy danh sách hồ sơ theo ngày (getBudgetDossierList) dựa vào API của LGSP
  * @date: 05/07/2022
  */
  getBudgetDossierListLGSP(config, fromDate, toDate): Observable<any> {
    const token = localStorage.getItem('OAuth2TOKEN');
    let headers = new HttpHeaders();
    headers = headers.append('Authorization', 'Bearer ' + token);
    headers = headers.set('Accept-Language', localStorage.getItem('language'));

    const params = `${config}&tuNgay=${fromDate}&denNgay=${toDate}`;
    return this.http.get<any>(this.adapter + '/lgsp-msns/--get-msnstn' + params, { headers });
  }

  getBudgetDossierDetail(config, id): Observable<any> {
    const token = localStorage.getItem('OAuth2TOKEN');
    let headers = new HttpHeaders();
    headers = headers.append('Authorization', 'Bearer ' + token);
    headers = headers.set('Accept-Language', localStorage.getItem('language'));

    const params = `${config}&id=${id}`;

    return this.http.get<any>(this.adapter + '/budget/--detail' + params, { headers });
  }

  /*
  * @author: duypd.hcm
  * @description: viết lại hàm lấy chi tiết hồ sơ (getBudgetDossierDetail) dựa vào API của LGSP
  * @date: 05/07/2022
  */

  getBudgetDossierDetailLGSP(config, id): Observable<any> {
    const token = localStorage.getItem('OAuth2TOKEN');
    let headers = new HttpHeaders();
    headers = headers.append('Authorization', 'Bearer ' + token);
    headers = headers.set('Accept-Language', localStorage.getItem('language'));

    const params = `${config}&id=${id}`;

    return this.http.get<any>(this.adapter + '/lgsp-msns/--get-chitieths' + params, { headers });
  }


  getBudgetDossierByPeriod(config, fromDate, toDate): Observable<any> {
    const token = localStorage.getItem('OAuth2TOKEN');
    let headers = new HttpHeaders();
    headers = headers.append('Authorization', 'Bearer ' + token);
    headers = headers.set('Accept-Language', localStorage.getItem('language'));

    const params = `${config}&from-date=${fromDate}&to-date=${toDate}`;

    return this.http.get<any>(this.adapter + '/budget/--by-period' + params, { headers });
  }

  /*
  * @author: duypd.hcm
  * @description: viết lại hàm thống kê hồ sơ theo kỳ  (getBudgetDossierByPeriod) dựa vào API của LGSP
  * @date: 05/07/2022
  */
  getBudgetDossierByPeriodLGSP(config, fromDate, toDate): Observable<any> {
    const token = localStorage.getItem('OAuth2TOKEN');
    let headers = new HttpHeaders();
    headers = headers.append('Authorization', 'Bearer ' + token);
    headers = headers.set('Accept-Language', localStorage.getItem('language'));

    const params = `${config}&tuNgay=${fromDate}&denNgay=${toDate}`;
    return this.http.get<any>(this.adapter + '/lgsp-msns/--get-hstheoky' + params, { headers });
  }

  getDetailBusiness(config, id): Observable<any> {
    const token = localStorage.getItem('OAuth2TOKEN');
    let headers = new HttpHeaders();
    headers = headers.append('Authorization', 'Bearer ' + token);
    headers = headers.set('Accept-Language', localStorage.getItem('language'));

    const params = `${config}&msdn=${id}`;

    return this.http.get<any>(this.adapter + '/lgsp-tandan/--detail-business' + params, { headers });
  }

  getListFile(config, fromTime, toTime, offset, limit): Observable<any> {
    const token = localStorage.getItem('OAuth2TOKEN');
    let headers = new HttpHeaders();
    headers = headers.append('Authorization', 'Bearer ' + token);
    headers = headers.set('Accept-Language', localStorage.getItem('language'));

    const params = `${config}&from_ts=${fromTime}&to_ts=${toTime}&offset=${offset}&limit=${limit}`;

    return this.http.get<any>(this.adapter + '/lgsp-tandan/--list-file' + params, { headers });
  }

  getListReceptionRecord(config, fromDate, toDate, offset, limit): Observable<any> {
    const token = localStorage.getItem('OAuth2TOKEN');
    let headers = new HttpHeaders();
    headers = headers.append('Authorization', 'Bearer ' + token);
    headers = headers.set('Accept-Language', localStorage.getItem('language'));

    const params = `${config}&fromdate=${fromDate}&todate=${toDate}&offset=${offset}&limit=${limit}`;

    return this.http.get<any>(this.adapter + '/lgsp-tandan/--reception-record' + params, { headers });
  }

  getDetailRecord(config, maHoSo): Observable<any> {
    const token = localStorage.getItem('OAuth2TOKEN');
    let headers = new HttpHeaders();
    headers = headers.append('Authorization', 'Bearer ' + token);
    headers = headers.set('Accept-Language', localStorage.getItem('language'));

    const params = `${config}&journalno=${maHoSo}`;

    return this.http.get<any>(this.adapter + '/lgsp-tandan/--detail-record' + params, { headers });
  }

  getBHXHCode(body: any, agency: string) {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    switch (localStorage.getItem('isLoggedIn')) {
      case 'false':
        const token = localStorage.getItem('OAuth2TOKEN');
        headers = headers.append('Authorization', 'Bearer ' + token);
        return this.http.post<any>(
          this.adapter +
            '/bhxh/--get-masobhxh-by-tieuchi?subsystem-id=' +
            this.padsvcSubsystem +
            `&agency-id=${agency}`,
          body,
          { headers }
        );
      case 'true':
        return this.http.post<any>(
          this.adapter +
            '/bhxh/--get-masobhxh-by-tieuchi?subsystem-id=' +
            this.padsvcSubsystem +
            `&agency-id=${agency}`,
          body,
          { headers }
        );
    }
    // tslint:disable-next-line:max-line-length
    // return this.http.post<any>(this.adapter + '/bhxh/--get-masobhxh-by-tieuchi?subsystem-id=' + this.padsvcSubsystem + `&agency-id=${agency}`, body, { headers });
  }

  getBHXHCodeBdg(body: any, agency: string) {
    console.log(this.env);
    let configid = '';
    if (this.env.OS_BDG?.isEnableOs) {
      configid = '&config-id=' + this.env.OS_BDG.configLGSPMinhTueId;
    }
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    switch (localStorage.getItem('isLoggedIn')) {
      case 'false':
        const token = localStorage.getItem('OAuth2TOKEN');
        headers = headers.append('Authorization', 'Bearer ' + token);
        return this.http.post<any>(
          this.adapter +
            '/bhxh/--get-masobhxh-by-tieuchi-bdg?subsystem-id=' +
            this.padsvcSubsystem +
            `&agency-id=${agency}` +
            configid,
          body,
          { headers }
        );
      case 'true':
        return this.http.post<any>(
          this.adapter +
            '/bhxh/--get-masobhxh-by-tieuchi-bdg?subsystem-id=' +
            this.padsvcSubsystem +
            `&agency-id=${agency}` +
            configid,
          body,
          { headers }
        );
    }
    // tslint:disable-next-line:max-line-length
    // return this.http.post<any>(this.adapter + '/bhxh/--get-masobhxh-by-tieuchi?subsystem-id=' + this.padsvcSubsystem + `&agency-id=${agency}`, body, { headers });
  }
  getBHXHCodeHCM(body: any, agency: string) {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    switch (localStorage.getItem('isLoggedIn')) {
      case 'false':
        const token = localStorage.getItem('OAuth2TOKEN');
        headers = headers.append('Authorization', 'Bearer ' + token);
        return this.http.post<any>(
          this.adapter +
            '/bhxh/hcm/--get-masobhxh-by-tieuchi?subsystem-id=' +
            this.padsvcSubsystem +
            `&agency-id=${agency}`,
          body,
          { headers }
        );
      case 'true':
        return this.http.post<any>(
          this.adapter +
            '/bhxh/hcm/--get-masobhxh-by-tieuchi?subsystem-id=' +
            this.padsvcSubsystem +
            `&agency-id=${agency}`,
          body,
          { headers }
        );
    }
    // tslint:disable-next-line:max-line-length
    // return this.http.post<any>(this.adapter + '/bhxh/--get-masobhxh-by-tieuchi?subsystem-id=' + this.padsvcSubsystem + `&agency-id=${agency}`, body, { headers });
  }

  getFamilyBdg(code: string, agency: string) {
    let configid = '';
    if (this.env.OS_BDG?.isEnableOs) {
      configid = '&config-id=' + this.env.OS_BDG.configLGSPMinhTueId;
    }
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    switch (localStorage.getItem('isLoggedIn')) {
      case 'false':
        const token = localStorage.getItem('OAuth2TOKEN');
        headers = headers.append('Authorization', 'Bearer ' + token);
        return this.http.post<any>(
          this.adapter +
            '/bhxh/--get-tthgd-by-masobhxh-bdg?subsystem-id=' +
            this.padsvcSubsystem +
            `&agency-id=${agency}` +
            configid,
          { maSoBhxh: code },
          { headers }
        );
      case 'true':
        return this.http.post<any>(
          this.adapter +
            '/bhxh/--get-tthgd-by-masobhxh-bdg?subsystem-id=' +
            this.padsvcSubsystem +
            `&agency-id=${agency}` +
            configid,
          { maSoBhxh: code },
          { headers }
        );
    }
  }

  getInfoFamily(body: any, agency: string) {
    console.log(this.env);
    let configid = '';
    if (this.env.OS_BDG?.isEnableOs) {
      configid = '&config-id=' + this.env.OS_BDG.configLGSPMinhTueId;
    }
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    switch (localStorage.getItem('isLoggedIn')) {
      case 'false':
        const token = localStorage.getItem('OAuth2TOKEN');
        headers = headers.append('Authorization', 'Bearer ' + token);
        return this.http.post<any>(
          this.adapter +
            '/bhxh/--get-info-tthgd-bdg?subsystem-id=' +
            this.padsvcSubsystem +
            `&agency-id=${agency}` +
            configid,
          body,
          { headers }
        );
      case 'true':
        return this.http.post<any>(
          this.adapter +
            '/bhxh/--get-info-tthgd-bdg?subsystem-id=' +
            this.padsvcSubsystem +
            `&agency-id=${agency}` +
            configid,
          body,
          { headers }
        );
    }
  }
  getInfoFamilyHCM(body: any, agency: string) {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    switch (localStorage.getItem('isLoggedIn')) {
      case 'false':
        const token = localStorage.getItem('OAuth2TOKEN');
        headers = headers.append('Authorization', 'Bearer ' + token);
        return this.http.post<any>(
          this.adapter +
            '/bhxh/hcm/--get-tthgd-by-tieuchi?subsystem-id=' +
            this.padsvcSubsystem +
            `&agency-id=${agency}`, 
            body, 
            { headers }
        );
      case 'true':
        return this.http.post<any>(
          this.adapter +
            '/bhxh/hcm/--get-tthgd-by-tieuchi?subsystem-id=' +
            this.padsvcSubsystem +
            `&agency-id=${agency}`,
            body,
          { headers }
        );
    }
  }
  getFamilyByBHXHCode(code: string, agency: string) {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    switch (localStorage.getItem('isLoggedIn')) {
      case 'false':
        const token = localStorage.getItem('OAuth2TOKEN');
        headers = headers.append('Authorization', 'Bearer ' + token);
        return this.http.post<any>(
          this.adapter +
            '/bhxh/--get-tthgd-by-masobhxh?subsystem-id=' +
            this.padsvcSubsystem +
            `&agency-id=${agency}`,
          { maSoBhxh: code },
          { headers }
        );
      case 'true':
        return this.http.post<any>(
          this.adapter +
            '/bhxh/--get-tthgd-by-masobhxh?subsystem-id=' +
            this.padsvcSubsystem +
            `&agency-id=${agency}`,
          { maSoBhxh: code },
          { headers }
        );
    }
  }
  getFamilyByBHXHCodeHCM(code: string, agency: string) {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    switch (localStorage.getItem('isLoggedIn')) {
      case 'false':
        const token = localStorage.getItem('OAuth2TOKEN');
        headers = headers.append('Authorization', 'Bearer ' + token);
        return this.http.post<any>(
          this.adapter +
            '/bhxh/hcm/--get-tthgd-by-masobhxh?subsystem-id=' +
            this.padsvcSubsystem +
            `&agency-id=${agency}`,
          { maSoBhxh: code },
          { headers }
        );
      case 'true':
        return this.http.post<any>(
          this.adapter +
            '/bhxh/hcm/--get-tthgd-by-masobhxh?subsystem-id=' +
            this.padsvcSubsystem +
            `&agency-id=${agency}`,
          { maSoBhxh: code },
          { headers }
        );
    }
  }

  getLLTPCategory(type: number, agencyId: any): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers.append('Access-Control-Allow-Origin', '*');
    const subsystemId = this.env?.subsystem?.id;
    return this.http.get(
      this.adapterUrl +
        `/lgsp-tandan-jr/--category?infoType=${type}&agency-id=${agencyId}&subsystem-id=${subsystemId}`,
      { headers }
    );
  }
  getDbnBudgetDossierList(config, fromDate, toDate): Observable<any> {
    const token = localStorage.getItem('OAuth2TOKEN');
    let headers = new HttpHeaders();
    headers = headers.append('Authorization', 'Bearer ' + token);
    headers = headers.set('Accept-Language', localStorage.getItem('language'));

    const params = `${config}&tuNgay=${fromDate}&denNgay=${toDate}&size=1000`;

    return this.http.get<any>(this.adapter+'/budget-relationship/--list-file' + params, { headers });
  }

  getDbnBudgetDossierDetail(config, id): Observable<any> {
    const token = localStorage.getItem('OAuth2TOKEN');
    let headers = new HttpHeaders();
    headers = headers.append('Authorization', 'Bearer ' + token);
    headers = headers.set('Accept-Language', localStorage.getItem('language'));

    const params = `${config}&id=${id}`;

    return this.http.get<any>(this.adapter+'/budget-relationship/--file-details' + params, { headers });
  }

  getDbnBudgetDossierByPeriod(config, fromDate, toDate): Observable<any> {
    const token = localStorage.getItem('OAuth2TOKEN');
    let headers = new HttpHeaders();
    headers = headers.append('Authorization', 'Bearer ' + token);
    headers = headers.set('Accept-Language', localStorage.getItem('language'));

    const params = `${config}&tuNgay=${fromDate}&denNgay=${toDate}`;

    return this.http.get<any>(this.adapter+'/budget-relationship/--results-list' + params, { headers });
  }

  getTokenVNPost(search) {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers.append('Access-Control-Allow-Origin', '*');
    // return this.http.get<any>(this.adapter + '/vnpost/token' + search, { headers });
    return this.http.get<any>(this.adapter + '/vnpost/token' + search, { headers });
  }
 
  signViettelSim(fileId, body){
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.post<any>(this.adapter + '/viettel-pki/' + fileId + '/--sign-by-file', body , { headers });
  }

  //Tra cứu doanh nghiệp LGSP
  getDetailBusinessLGSPHCM(config, id): Observable<any> {
    const token = localStorage.getItem('OAuth2TOKEN');
    let headers = new HttpHeaders();
    headers = headers.append('Authorization', 'Bearer ' + token);
    headers = headers.set('Accept-Language', localStorage.getItem('language'));

    const params = `${config}&msdn=${id}`;

    return this.http.get<any>(this.adapter + '/lgsp-dkdn/--layThongTinChiTietDoanhNghiep' + params, { headers });
  }

  //Danh sách hồ sơ xử lý trong ngày LGSP
  getListFileLGSPHCM(config, fromTime, toTime, offset, limit): Observable<any> {
    const token = localStorage.getItem('OAuth2TOKEN');
    let headers = new HttpHeaders();
    headers = headers.append('Authorization', 'Bearer ' + token);
    headers = headers.set('Accept-Language', localStorage.getItem('language'));

    const params = `${config}&from_ts=${fromTime}&to_ts=${toTime}&offset=${offset}&limit=${limit}`;

    return this.http.get<any>(this.adapter + '/lgsp-dkdn/--layDanhSachHoSoTrongNgay' + params, { headers });
  }

  //Danh sách hồ sơ tiếp nhận LGSP
  getListReceptionRecordLGSPHCM(config, fromDate, toDate, offset, limit): Observable<any> {
    const token = localStorage.getItem('OAuth2TOKEN');
    let headers = new HttpHeaders();
    headers = headers.append('Authorization', 'Bearer ' + token);
    headers = headers.set('Accept-Language', localStorage.getItem('language'));

    const params = `${config}&from_date=${fromDate}&to_date=${toDate}&offset=${offset}&limit=${limit}`;

    return this.http.get<any>(this.adapter + '/lgsp-dkdn/--layDanhSachHoSoTrongKhoangThoiGian' + params, { headers });
  }

  //Thông tin chi tiết hồ sơ đăng ký LGSP
  getDetailRecordLGSPHCM(config, maHoSo): Observable<any> {
    const token = localStorage.getItem('OAuth2TOKEN');
    let headers = new HttpHeaders();
    headers = headers.append('Authorization', 'Bearer ' + token);
    headers = headers.set('Accept-Language', localStorage.getItem('language'));

    const params = `${config}&in_journal_no=${maHoSo}`;

    return this.http.get<any>(this.adapter + '/lgsp-dkdn/--layTinhTrangHoSo' + params, { headers });
  }

  getCancelVnpostDossier(search): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');
    // return this.http.get("http://localhost:8084" + '/vnpostBdg/--delete-order' + search, { headers }).pipe();
    return this.http.get(this.adapter + '/vnpostBdg/--delete-order' + search, { headers }).pipe();
  }

  postFpt1GatePayment(dossierOnlineApplyId, dossierId): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.post(this.adapter + '/bdg-connect-log/--post-fpt1gate-payment?dossier-online-apply-id=' + dossierOnlineApplyId + '&dossier-id=' + dossierId , { headers }).pipe();
  }
  getCertNEAC(body){
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.post<any>(this.adapter + '/neac-sign/--get-certificate', body , { headers });
  }

  signNEAC(body){
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.post<any>(this.adapter + '/neac-sign/--sign', body , { headers });
  }

  getStatisticalSms(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.get(this.adapterUrl + '/sms-statistics/--statistics-by-agency' + searchString, { headers }).pipe();
  }

  getBudgetDossierListAggPage(search): Observable<any> {
    const token = localStorage.getItem('OAuth2TOKEN');
    let headers = new HttpHeaders();
    headers = headers.append('Authorization', 'Bearer ' + token);
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.adapter + '/agg-budget/--list' + search, { headers }).pipe();
  }
  insertBudgetDossierListAgg(fromDate, toDate): Observable<any> {
    const token = localStorage.getItem('OAuth2TOKEN');
    let headers = new HttpHeaders();
    headers = headers.append('Authorization', 'Bearer ' + token);
    headers = headers.set('Accept-Language', localStorage.getItem('language'));

    const params = `?from-date=${fromDate}&to-date=${toDate}&size=1000`;
    return this.http.get<any>(this.adapter + '/agg-budget/--insert' + params, { headers });
  }

  getBudgetDossierDetailAgg(id): Observable<any> {
    const token = localStorage.getItem('OAuth2TOKEN');
    let headers = new HttpHeaders();
    headers = headers.append('Authorization', 'Bearer ' + token);
    headers = headers.set('Accept-Language', localStorage.getItem('language'));

    const params = `?id=${id}`;

    return this.http.get<any>(this.adapter + '/agg-budget/--detail' + params, { headers });
  }

  getBudgetDossierByPeriodAgg(fromDate, toDate): Observable<any> {
    const token = localStorage.getItem('OAuth2TOKEN');
    let headers = new HttpHeaders();
    headers = headers.append('Authorization', 'Bearer ' + token);
    headers = headers.set('Accept-Language', localStorage.getItem('language'));

    const params = `?from-date=${fromDate}&to-date=${toDate}`;

    return this.http.get<any>(this.adapter + '/agg-budget/--by-period' + params, { headers });
  }

  getBudgetDossierCertificateAgg(dvqhns): Observable<any> {
    const token = localStorage.getItem('OAuth2TOKEN');
    let headers = new HttpHeaders();
    headers = headers.append('Authorization', 'Bearer ' + token);
    headers = headers.set('Accept-Language', localStorage.getItem('language'));

    const params = `?dvqhns=${dvqhns}`;

    return this.http.get<any>(this.adapter + '/agg-budget/--certificate' + params, { headers });
  }

  getRequestAdapterAgg(searchParams, path): Observable<any> {
    let headers = new HttpHeaders();
    const params = new HttpParams({ fromObject: searchParams });
    const token = localStorage.getItem('OAuth2TOKEN');
    headers = headers.append('Authorization', 'Bearer ' + token);
    return this.http.get<any>(this.adapter + '/' + path, { headers, params}).pipe();
    
  }

  postDptracuuthanhtoanhs(configId, body): Observable<any> {
    let headers = new HttpHeaders();
    headers.append('Content-Type', 'application/json');
    if(localStorage.getItem('isLoggedIn') === 'false')
      headers = headers.append('Authorization', 'Bearer ' + localStorage.getItem('OAuth2TOKEN'));
    return this.http.post<any>(this.adapter + '/payment-platform/dptracuuthanhtoanhs-ktm?config-id='+configId, body, { headers });
  }
}
