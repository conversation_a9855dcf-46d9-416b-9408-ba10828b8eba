<button mat-icon-button class="close-button" (click)="onDismiss()">
    <mat-icon>close</mat-icon>
</button>
<h3 class="dialog_title" mat-dialog-title>Cậ<PERSON> nh<PERSON>t hình thức thanh toán</h3>
<div mat-dialog-content class="dialog_content">
<form [formGroup]="paymentForm" class="cardContent">
    <div  style="padding-top: 1em;">
      <mat-form-field appearance="outline" fxFlex.gt-sm="98" fxFlex.gt-xs="98" fxFlex='grow'>
          <mat-label><PERSON><PERSON><PERSON> thức thanh toán</mat-label>
          <mat-select formControlName="paymentMethod" (selectionChange)="changePaymentMethod($event)" required>
              <mat-option *ngFor='let pro of listPayment;' [value]="pro.id">
                  {{pro.name}}
                  <span *ngIf="pro.name == undefined || pro.name == null || pro.name.trim() == ''" i18n>(<PERSON>h<PERSON>ng t<PERSON><PERSON> thấy bản dịch)</span>
              </mat-option>
          </mat-select>
      </mat-form-field>
    </div>
  </form>
  </div>
<div fxLayout="row" fxLayout.xs="column" fxLayout.sm="column" fxLayoutAlign="center">
    <button mat-flat-button class="applyBtn" (click)="onConfirm()">
        <span i18n>Đồng ý</span>
    </button>
</div>