<div class="body">
    <h3 class="dialog_title" mat-dialog-title i18n><PERSON><PERSON> s<PERSON><PERSON> hồ sơ</h3>

    <div class="frm_tbl0" *ngIf="data.flag == 0 || data2.flag == 2 || data2.flag == 3 ">
        <table mat-table [dataSource]="dataSource">
            <ng-container matColumnDef="no">
                <th mat-header-cell *matHeaderCellDef>STT</th>
                <td mat-cell *matCellDef="let row; let i = index"> {{size * (pageIndex - 1) + (i + 1)}} </td>
            </ng-container>
            <ng-container matColumnDef="dossierCode">
                <th mat-header-cell *matHeaderCellDef>S<PERSON> hồ sơ</th>
                <td mat-cell *matCellDef="let row">
                    <a>{{row.dossierCode}}</a>
                </td>
            </ng-container>
            <ng-container matColumnDef="procedureName">
                <th mat-header-cell *matHeaderCellDef i18n>Tên thủ tục hành chính</th>
                <td style="text-align: left; max-width: 400px;" mat-cell *matCellDef="let row"> {{row.procedureName}} </td>
            </ng-container>
            <ng-container matColumnDef="procedureLevel">
                <th mat-header-cell *matHeaderCellDef i18n>Mức độ</th>
                <td mat-cell *matCellDef="let row"> {{row.procedureLevel}} </td>
            </ng-container>
            <ng-container matColumnDef="sectorName">
                <th mat-header-cell *matHeaderCellDef i18n>Lĩnh vực</th>
                <td mat-cell *matCellDef="let row"> {{row.sectorName}} </td>
            </ng-container>
            <ng-container matColumnDef="applicantOwnerFullName">
                <th mat-header-cell *matHeaderCellDef>Tổ chức, cá nhân</th>
                <td style="text-align: left;" mat-cell *matCellDef="let row"> {{row.applicantOwnerFullName}} </td>
            </ng-container>
            <ng-container matColumnDef="applicantPhoneNumber">
                <th mat-header-cell *matHeaderCellDef>Địa chỉ, SĐT</th>
                <td style="text-align: left;" mat-cell *matCellDef="let row"> {{row.PhoneNumber}}, {{row.Address}} </td>
            </ng-container>
            <ng-container matColumnDef="AgencyName">
                <th mat-header-cell *matHeaderCellDef>Cơ quan chủ trì</th>
                <td style="text-align: left;" mat-cell *matCellDef="let row"> {{data.AgencyName}} </td>
            </ng-container>
            <ng-container matColumnDef="AgencyAccepterName">
                <th mat-header-cell *matHeaderCellDef>Cơ quan tiếp nhận</th>
                <td style="text-align: left;" mat-cell *matCellDef="let row"> {{row.agencyName}} </td>
            </ng-container>
            <ng-container matColumnDef="acceptedDate">
                <th mat-header-cell *matHeaderCellDef i18n>Ngày tiếp nhận</th>
                <td mat-cell *matCellDef="let row"> {{row.acceptedDate | date: 'dd/MM/yyyy HH:mm:ss'}} </td>
            </ng-container>
            <ng-container matColumnDef="appointmentDate">
                <th mat-header-cell *matHeaderCellDef i18n>Ngày hẹn trả</th>
                <td mat-cell *matCellDef="let row"> {{row.appointmentDate| date: 'dd/MM/yyyy HH:mm:ss'}} </td>
            </ng-container>
            <ng-container matColumnDef="completedDate">
                <th mat-header-cell *matHeaderCellDef>Ngày có kết quả</th>
                <td mat-cell *matCellDef="let row"> {{row.completedDate| date: 'dd/MM/yyyy HH:mm:ss'}} </td>
            </ng-container>
            <ng-container matColumnDef="withdrawDate">
                <th mat-header-cell *matHeaderCellDef>Ngày yêu cầu trả lại dân</th>
                <td mat-cell *matCellDef="let row"> {{row.withdrawDate| date: 'dd/MM/yyyy HH:mm:ss'}} </td>
            </ng-container>
            <ng-container matColumnDef="returnedDate">
                <th mat-header-cell *matHeaderCellDef>Ngày trả kết quả/trả lại dân</th>
                <td mat-cell *matCellDef="let row"> {{row.returnedDate| date: 'dd/MM/yyyy HH:mm:ss'}} </td>
            </ng-container>

            <ng-container matColumnDef="ReceivingKind">
                <th mat-header-cell *matHeaderCellDef>Hình thức nhận KQ</th>
                <td mat-cell *matCellDef="let row"> {{row.ReceivingKind}} </td>
            </ng-container>
            <ng-container matColumnDef="applyMethod">
                <th mat-header-cell *matHeaderCellDef>Hình thức tiếp nhận</th>
                <td mat-cell *matCellDef="let row"> {{row.applyMethod}} </td>
            </ng-container>

            <tr mat-header-row *matHeaderRowDef="displayedColumns; sticky: true"></tr>
            <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
        </table>
        <div *ngIf="isWaitingData" class="tableSpinnerContainer">
            <mat-spinner diameter="60">
            </mat-spinner>
        </div>
    </div>

    <!-- Chi tiet thu tuc -->
    <div class="frm_tbl0" *ngIf="data1.flag == 1">
        <table mat-table [dataSource]="dataSource1" style="width: 100%;">
            <ng-container matColumnDef="no">
                <th mat-header-cell *matHeaderCellDef>STT</th>
                <td mat-cell *matCellDef="let row; let i = index"> {{size * (pageIndex - 1) + (i + 1)}} </td>
            </ng-container>
            <ng-container matColumnDef="Code">
                <th mat-header-cell *matHeaderCellDef>Tên tắt</th>
                <td mat-cell *matCellDef="let row">
                    <a>{{row.Code}}</a>
                </td>
            </ng-container>
            <ng-container matColumnDef="AgencyName">
                <th mat-header-cell *matHeaderCellDef>Cơ quan</th>
                <td mat-cell *matCellDef="let row"> {{row.AgencyName}} </td>
            </ng-container>
            <ng-container matColumnDef="sectorName">
                <th mat-header-cell *matHeaderCellDef>Lĩnh vực thủ tục</th>
                <td mat-cell *matCellDef="let row"> {{row.sectorName}} </td>
            </ng-container>
            <ng-container matColumnDef="procedureName">
                <th mat-header-cell *matHeaderCellDef>Thủ tục</th>
                <td mat-cell *matCellDef="let row"> {{row.procedureName}} </td>
            </ng-container>
            <ng-container matColumnDef="Fee">
                <th mat-header-cell *matHeaderCellDef>Lệ phí</th>
                <td mat-cell *matCellDef="let row"> {{row.Fee}} </td>
            </ng-container>
            <ng-container matColumnDef="legalGrounds">
                <th mat-header-cell *matHeaderCellDef>Quyết định</th>
                <td mat-cell *matCellDef="let row"> {{row.legalGrounds}} </td>
            </ng-container>
            <ng-container matColumnDef="agencyLevel">
                <th mat-header-cell *matHeaderCellDef>Cấp thủ tục</th>
                <td mat-cell *matCellDef="let row"> {{row.agencyLevel}} </td>
            </ng-container>
            <ng-container matColumnDef="Level">
                <th mat-header-cell *matHeaderCellDef>Mức độ</th>
                <td mat-cell *matCellDef="let row"> {{row.Level}} </td>
            </ng-container>

            <tr mat-header-row *matHeaderRowDef="displayedColumns1; sticky: true"></tr>
            <tr mat-row *matRowDef="let row; columns: displayedColumns1;"></tr>
        </table>
        <div *ngIf="isWaitingData" class="tableSpinnerContainer">
            <mat-spinner diameter="60">
            </mat-spinner>
        </div>
    </div>
    <!--------------------------------------------->
    <div class="pagination">
        <ul class="temp-arr">
            <li *ngFor="let item of ELEMENTDATA  | paginate: {itemsPerPage: size, currentPage: page, totalItems: countResult, id: 'pgnx'}">
            </li>
        </ul>
        <div class="page-size">
            <span i18n>Hiển thị </span>
            <mat-form-field appearance="outline">
                <mat-select [(value)]="size" (valueChange)="paginate(pageIndex, 1)">
                    <mat-option *ngFor='let opt of pgSizeOptions;' [value]="opt">{{opt}}</mat-option>
                </mat-select>
            </mat-form-field>
            <span><span i18n>trên</span> {{countResult}} <span i18n>bản ghi</span></span>
        </div>
        <div class="control">
            <pagination-controls id="pgnx" (pageChange)="page = $event; paginate(page, 0)" responsive="true" previousLabel="" nextLabel="">
            </pagination-controls>
        </div>
    </div>

    <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="column" fxLayoutAlign="center">
        <div class="exportExcelBtnContainer">
            <button mat-flat-button class="exportExcelBtn" (click)="exportexcel()">
                <!-- <mat-icon class="iconStatistical" *ngIf="!isExportExcel">cloud_download</mat-icon>
                <mat-spinner diameter="25" class="iconStatistical" *ngIf="isExportExcel"></mat-spinner> -->
                <span>Xuất excel</span>
            </button>
        </div>
        <button mat-flat-button class="closeBtn" (click)="onClose()">
            <span i18n>Đóng</span>
        </button>
    </div>
</div>