import {HttpClient, HttpHeaders} from '@angular/common/http';
import {Injectable} from '@angular/core';
import {Observable} from 'rxjs';
import {ApiProviderService} from 'core/service/api-provider.service';
import {EnvService} from 'core/service/env.service';
import {DeploymentService} from "data/service/deployment.service";

@Injectable({
  providedIn: 'root'
})
export class AgencyEdocService  {

  config = this.envService.getConfig();
  constructor(
    private envService: EnvService,
    private http: HttpClient,
    private deploymentService: DeploymentService,
  ) { }

  env = this.deploymentService.getAppDeployment()?.env;
  private basedocUrl = this.env?.OS_TGG?.ioffice?.root_url_basedoc !== undefined ? this.env?.OS_TGG?.ioffice?.root_url_basedoc : '';

  getEdocAgencies(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.basedocUrl + '/edoc-agency' + searchString, { headers });
  }
}
