
import { Component, OnInit, Inject, ViewChild } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA, MatDialog } from '@angular/material/dialog';
import { KeycloakService } from 'keycloak-angular';
import { DossierService } from 'src/app/data/service/dossier/dossier.service';
import { UserService } from 'src/app/data/service/user.service';
import * as ClassicEditor from '@ckeditor/ckeditor5-build-classic/build/ckeditor';
import '@ckeditor/ckeditor5-build-classic/build/translations/vi';
import { EnvService } from 'src/app/core/service/env.service';
import { SnackbarService } from 'src/app/data/service/snackbar/snackbar.service';
import { DatePipe } from '@angular/common';
import { DeploymentService } from 'src/app/data/service/deployment.service';
import { ProcedureService } from 'src/app/data/service/procedure/procedure.service';
import {FormControl, FormGroup} from '@angular/forms';
import { CheckSendNotifyComponent } from 'src/app/shared/components/check-send-notify/check-send-notify.component';
import {ApiProviderService} from 'src/app/core/service/api-provider.service';
import * as tUtils from 'src/app/data/service/thoai.service';
@Component({
  selector: 'app-send-dossier-to-vpub-dialog',
  templateUrl: './send-dossier-to-vpub.component.html',
  styleUrls: ['./send-dossier-to-vpub.component.scss']
})
export class SendDossierToVpubComponent implements OnInit {
  @ViewChild(CheckSendNotifyComponent) checkSendNotifyComponent;
  config = this.envService.getConfig();
  env: any = this.deploymentService.getAppDeployment()?.env;
  selectedLang = localStorage.getItem('language') || 'vi';
  selectedLangId = Number(localStorage.getItem('languageId')) || 228;
  dossierId: string;
  commentContent = '';
  userName: string;
  accountId: string;
  dossierCode: string;
  agencyId: any;
  isDisabled = false; //nhipttt-IGATESUPP-74286 disabled nút Yêu cầu bổ sung sau khi nhấn

  public Editor = ClassicEditor;
  // ================================================= Upload file
  uploaded: boolean;
  blankVal: any;
  files = [];
  urls = [];
  fileNames = [];
  uploadFileNames = [];
  fileNamesFull = [];

  isFieldArrayInvalid = false;
  acceptFileExtension = this.config.acceptFileExtension;
  acceptFileType = this.config.acceptFileType;
  maxFileSize = this.env?.maxFileSize || this.config.maxFileSize;
  fileTemplate = "";

  searchForm = new FormGroup({
    SoToTrinhVub: new FormControl(''),
    CoQuanBanHanhVub: new FormControl(''),
    TrichYeuVanBanVub: new FormControl(''),
    LoaiVanBanVub: new FormControl(''),
    NguoiKyToTrinh: new FormControl(''),
    NgayBanHanhVub: new FormControl(''),
  });

  listFile: any[] = [];

  constructor(
    private userService: UserService,
    private envService: EnvService,
    private keycloakService: KeycloakService,
    public dialogRef: MatDialogRef<SendDossierToVpubComponent>,
    @Inject(MAT_DIALOG_DATA) public data: SendDossierToVpubModel,
    private snackbarService: SnackbarService,
    private datePipe: DatePipe,
    private deploymentService: DeploymentService,
    private procedureService: ProcedureService,
    private dossierService: DossierService,
    private apiProviderService: ApiProviderService,
  ) {
    this.dossierId = data.dossierId;
    this.dossierCode = data.dossierCode;
    this.listFile = data.listURLFile;
  }

  ngOnInit(): void {
    this.getUserAccount();
    this.setEnvVariable();
    this.initLoadFile(this.listFile);
  }





  setEnvVariable(){
    this.fileTemplate = !!this.env?.fileTemplate?.requireAdditional ? this.env?.fileTemplate?.requireAdditional : this.config.requireAdditionalTemplateFile;
  }

  getUserAccount() {
    this.keycloakService.loadUserProfile().then(user => {
      // tslint:disable-next-line: no-string-literal
      this.accountId = user['attributes'].user_id[0];
      this.userService.getUserInfo(this.accountId).subscribe(data => {
        this.userName = data.fullname;
      });
    });
  }
  
  


  async onConfirm() {
    let checkFile: any = false;
    const formObj = this.searchForm.getRawValue();
    let listUrlFile = [];
    if (this.uploadFileNames.length > 0) {
      // checkFile = await this.uploadMultiFile(this.files, this.accountId);
      checkFile = true;
      if (!checkFile) {
        const msgObj = {
          vi: 'Upload file thất bại, vui lòng thử lại!',
          en: 'File upload failed, please try again!'
        };
        this.snackbarService.openSnackBar(2, '', msgObj[this.selectedLang], 'warning_notification', this.config.expiredTime);
        return;
      }
    } 
    if (formObj.SoToTrinhVub.trim() === '' || formObj.CoQuanBanHanhVub.trim() === '' || formObj.TrichYeuVanBanVub.trim() === '' || formObj.LoaiVanBanVub.trim() === '' || formObj.NguoiKyToTrinh.trim() === '' || formObj.NgayBanHanhVub === null || this.uploadFileNames.length == 0) {
      const msgObj = {
        vi: 'Yêu cầu nhập đủ thông tin!',
        en: 'Please enter a handling comments!'
      };
      this.snackbarService.openSnackBar(2, '', msgObj[this.selectedLang], 'warning_notification', this.config.expiredTime);
      return;
    }
    
    this.uploadFileNames.forEach(element => {
      let strUrl = this.apiProviderService.getUrl('digo', 'adapter') + '/nps-dossier/630f09bc4e2cad5d7776361a/downloadfile' + '?fileid=' + element.id + '&uuid=' + element.uuid;
      let item = {
        tenFile : element.filename,
        dinhDangFile : element.filename.split('.').pop(),
        dungLuongFile: String(element.size),
        fileId : element.id,
        urldownload: strUrl
      }
      listUrlFile.push(item);
    });
    console.log(listUrlFile);


    

    let body = {
      SoToTrinhVub: formObj.SoToTrinhVub,
      CoQuanBanHanhVub: formObj.CoQuanBanHanhVub,
      TrichYeuVanBanVub: formObj.TrichYeuVanBanVub,
      LoaiVanBanVub: formObj.LoaiVanBanVub,
      NguoiKyToTrinh: formObj.NguoiKyToTrinh,
      NgayBanHanhVub: this.datePipe.transform(formObj.NgayBanHanhVub, 'yyyy-MM-dd') + 'T00:00:00.000Z',
      dossierId: this.dossierId,
      urlFile : listUrlFile
    }

    this.dossierService.postDossierQLVB(body).subscribe(data => {
        console.log(data);
        if(data.affectedRows  == 1){
          this.snackbarService.openSnackBar(1, '', 'Gửi văn bản thành công', 'success_notification', this.config.expiredTime);
          this.onDismiss();
        }else{
          this.snackbarService.openSnackBar(2, '', 'Gửi văn bản thất bại', 'warning_notification', this.config.expiredTime);
        }
        
    },err => {
          console.log(err);
        } )
  }

   
  onDismiss() {
    this.dialogRef.close();
  }

  uploadMultiFile(file, accountId) {
    return new Promise((resolve) => {
      this.procedureService.uploadMultiFile(file, accountId).subscribe(data => {
        if(!!data && data.length > 0){
          this.uploadFileNames.push(...data);
          // this.formToJSON();
          resolve(true);
        } else {
          resolve(false);
        }
      }, err => {
        console.log(err);
        resolve(false);
      });
    });
  }


  initLoadFile(listFileName: any[]) {
    this.uploadFileNames = listFileName;
    for (const i of listFileName) {
      let newFile = [];
        newFile.push(i);
        this.files.push(i);
        const extension = i.filename.substring(i.filename.lastIndexOf('.')).split('.').pop();
        this.urls.push(this.getFileIcon(extension));
        if (i.filename.length > 20) {
          const startText = i.filename.substr(0, 5);
          const shortText = i
            .filename
            .substring(i.filename.length - 7, i.filename.length);
          this.fileNames.push(startText + '...' + shortText);
          this.fileNamesFull.push(i.filename);
        } else {
          this.fileNames.push(i.filename);
          this.fileNamesFull.push(i.filename);
        }
    }
  }

  async onSelectFile(event) {
    if (event.target.files && event.target.files[0]) {
      for (const i of event.target.files) {
        if (i.size >= this.maxFileSize * 1024 * 1024) {
          const msgObj = {
            vi: 'Kích thước tệp tin quá lớn, tên tệp: ' + i.name,
            en: 'The file is too large, file name: ' + i.name
          };
          this.snackbarService.openSnackBar(0, '', msgObj[this.selectedLang], 'error_notification', this.config.expiredTime);
          return;
        }
        if (this.acceptFileType.filter(t => t === i.type.toLowerCase()).length > 0) {
          let newFile = [];
          newFile.push(i);
          this.files.push(i);
          const extension = i.name.substring(i.name.lastIndexOf('.')).split('.').pop();
          this.urls.push(this.getFileIcon(extension));

          const reader = new FileReader();
          reader.onload = () => {
            this.uploaded = true;
          };
          if (i.name.length > 20) {
            const startText = i.name.substr(0, 5);
            const shortText = i
              .name
              .substring(i.name.length - 7, i.name.length);
            this.fileNames.push(startText + '...' + shortText);
            this.fileNamesFull.push(i.name);
          } else {
            this.fileNames.push(i.name);
            this.fileNamesFull.push(i.name);
          }
          reader.readAsDataURL(i);
          if (this.files.length > 0) {
            let checkFile = await this.uploadMultiFile(newFile, this.accountId);
            if (!checkFile) {
              const msgObj = {
                vi: 'Upload file thất bại!',
                en: 'File upload failed!'
              };
              this.snackbarService.openSnackBar(2, '', msgObj[this.selectedLang], 'warning_notification', this.config.expiredTime);
            }
          }
        } else {
          const msgObj = {
            vi: 'Không hỗ trợ loại tệp tin ',
            en: 'File type is not supported '
          };
          this.snackbarService.openSnackBar(0, '', msgObj[this.selectedLang] + i.type.substring(i.type.lastIndexOf('/') + 1).toUpperCase(), 'error_notification', this.config.expiredTime);
        }
      }
    }
  }

  removeItem(index: number) {
    this.uploadFileNames.splice(index, 1);
    this.urls.splice(index, 1);
    this.fileNames.splice(index, 1);
    this.fileNamesFull.splice(index, 1);
    this.files.splice(index, 1);
  }
  

  getFileIcon(ext) {
    return this.config.cloudStaticURL + 'icon/files/300x300/' + ext + '.png';
  }

  downloadTemplate(){
    window.open(this.fileTemplate, '_self');
  }

  


  downloadFileExport(index: number){
    let file = this.uploadFileNames[index];
    this.downloadFile(file.id, file.filename);
  }

  downloadFile(id, filename) {
    this.procedureService.downloadFile(id, this.dossierId).subscribe(data => {
      const dataType = data.type;
      const binaryData = [];
      binaryData.push(data);
      const downloadLink = document.createElement('a');
      downloadLink.href = window.URL.createObjectURL(new Blob(binaryData, { type: dataType }));
      downloadLink.setAttribute('download', filename);
      document.body.appendChild(downloadLink);
      downloadLink.click();
    }, err => {
      const msgObj = {
        vi: 'Không tìm thấy file!',
        en: 'File not found!'
      };
      this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
      console.log(err);
    });
  }

  routerLink(file)
  {
    if(!file) return;
    let name = file.filename ? file.filename: file.name;
    name = name.toUpperCase();
    if(name.indexOf('.JPG') >= 0 || name.indexOf('.JPEG') >= 0 || name.indexOf('.PNG') >= 0)
      return ['/viewer-image/' + file.id, {dossierId: this.dossierId }];

    return ['/viewer/' + file.id, {dossierId: this.dossierId }];
  }

  uploadFileExport(file, accountId) {
    this.procedureService.uploadMultiFile(file, accountId).subscribe(data => {
      this.uploadFileNames.push(data[0]);
      console.log("this.uploadFileNames",this.uploadFileNames);
    }, err => {
      console.log(err);
    });
  }
 

 async downloadFileFromResult(file){
    this.procedureService.downloadFile(file?.id, this.dossierId).subscribe(async data => {
      const dataType = data.type;
      const binaryData = [];
      binaryData.push(data);
      var blob = new Blob(binaryData, { type: dataType });
      var myFile = new File([blob], file.filename)
      this.files.push(myFile);
      const extension = file.filename.substring(file.filename.lastIndexOf('.')).split('.').pop();
       this.urls.push(this.getFileIcon(extension));

      
      if (file.filename.length > 20) {
        const startText = file.filename.substr(0, 5);
        const shortText = file.filename.substring(file.filename.length - 7, file.filename.length);
        this.fileNames.push(startText + '...' + shortText);
        this.fileNamesFull.push(file.filename);
      } else {
        this.fileNames.push(file.filename);
        this.fileNamesFull.push(file.filename);
      }
      //reader.readAsDataURL(blob);
      if (myFile) {
        let checkFile = await this.uploadMultiFile([myFile], this.accountId);
        if (!checkFile) {
          const msgObj = {
            vi: 'Upload file thất bại!',
            en: 'File upload failed!'
          };
          this.snackbarService.openSnackBar(2, '', msgObj[this.selectedLang], 'warning_notification', this.config.expiredTime);
        }
      }
    });
  }

  public formatDateTimeExportFile(inputDate: string): string {
    const date = new Date(inputDate);
    if (isNaN(date.getTime())) {
      return "";
    }
  
    const day = String(date.getDate()).padStart(2, "0");
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const year = date.getFullYear();
    const hours = String(date.getHours()).padStart(2, "0");
    const minutes = String(date.getMinutes()).padStart(2, "0");
  
    return `${hours} giờ ${minutes} phút, ngày ${day} tháng ${month} năm ${year}`;
  }

  changeToDate($event) {
    // const formObj = this.searchForm.getRawValue();
    // if (this.rangeLimitDossierSearch > 0) {
    //   const fromDate = new Date(formObj.advFilingFrom);
    //   const toDate = new Date(formObj.advFilingTo);
    //   let sumDate = Number(toDate) - Number(fromDate);

    //   if (sumDate > 0) {
    //     let constDate = Math.floor(sumDate / 1000 / 60 / 60 / 24);
    //     if (constDate > this.rangeLimitDossierSearch) {
    //       let fromDate = tUtils.getPastDate(this.rangeLimitDossierSearch, formObj.advFilingTo);
    //       this.searchForm.patchValue({
    //         advFilingFrom: fromDate
    //       })
    //     }
    //   }
    // }
  }
}

export class SendDossierToVpubModel {
  constructor(public dossierId: string, public dossierCode: string, public listURLFile: any[]) {
  }
}


