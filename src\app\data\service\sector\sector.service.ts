import { Injectable } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
// tslint:disable-next-line: max-line-length
// import { AdvancedSearchComponent, ConfirmSearchDialogModel } from 'src/app/modules/home/<USER>/advanced-search/advanced-search.component';
import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { ApiProviderService } from 'src/app/core/service/api-provider.service';
import { Observable } from 'rxjs';
import { DeploymentService } from '../deployment.service';

@Injectable({
  providedIn: 'root'
})
export class SectorService {
  constructor(
    private dialog: MatDialog,
    private http: HttpClient,
    private apiProviderService: ApiProviderService,
    private deploymentService: DeploymentService
  ) { }

  private getProcedureURL = this.apiProviderService.getUrl('digo', 'basepad');
  private getAgencyURL = this.apiProviderService.getUrl('digo', 'basedata');
  private getSector = this.apiProviderService.getUrl('digo', 'basepad');
  private SectorSpecific = this.apiProviderService.getUrl('digo', 'basepad')+'/qbh-sector-specific/';
  // private getSector = 'http://localhost:8080';
  private adapter = this.apiProviderService.getUrl('digo', 'adapter');
  // advancedSearch() {
  //   const dialogData = new ConfirmSearchDialogModel();
  //   const dialogRef = this.dialog.open(AdvancedSearchComponent, {
  //     width: '600px',
  //     data: dialogData,
  //     disableClose: false,
  //     position: {
  //       top: '10em'
  //     }
  //   });
  //   dialogRef.afterClosed().subscribe(dialogResult => {
  //     // console.log(dialogResult);
  //   });
  // }

  getListProcedureLevel(): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.getProcedureURL + '/procedure-level', { headers }).pipe();
  }

  getListSector(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.getSector + '/sector/' + searchString, { headers });
  }

  getListSectorAll(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.getSector + '/sector/--all' + searchString, { headers });
  }

  getDetailSector(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.getSector + '/sector/' + id, { headers });
  }

  deleteSector(id): Observable<any> {
    return this.http.delete(this.getSector + '/sector/' + id);
  }

  getCheckIsUsed(id): Observable<any>{
    return this.http.get(this.getSector + '/sector/' + id+'/--check-used');
  }

  updateSector(id, data): Observable<any> {
    return this.http.put<any>(this.getSector + '/sector/' + id, data);
  }

  postSector(data): Observable<any> {
    return this.http.post<any>(this.getSector + '/sector/', data);
  }

  checkExistCode(data): Observable<any> {
    const token = localStorage.getItem('OAuth2TOKEN');
    let headers = new HttpHeaders();
    headers = headers.append('Authorization', 'OAuth 2.0' + token);
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.getSector + '/sector/--check-exist-code' + data, {headers} );
  }

  getListAgencyLevel(): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.getAgencyURL + '/agency-level', { headers }).pipe();
  }

  getSyncSector(agencyId, agencyCode, subsystemId): Observable<any> {
    return this.http.get(this.adapter + '/npadsvc/--sectors?agency-id=' + agencyId +
      '&subsystem-id=' + subsystemId + '&agency-code=' + agencyCode).pipe();
  }

  getLgspSyncSector(agencyCode): Observable<any> {
    return this.http.get(this.adapter + "/lgsp-sync-sector/--sector?agency-code=" + agencyCode).pipe();
  }

  getSyncSectorHue(agencyId, subsystemId): Observable<any> {
    return this.http.get(this.adapter + '/tthc-hue/--sync-sector?agency-id=' + agencyId +
      '&subsystem-id=' + subsystemId).pipe();
  }

  postSyncSector(data): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.append('Content-Type', 'application/json');
    return this.http.post<any>(this.getSector + '/sector/--sync-npad-svc', data);
  }

  getListProcedure(searchString, type, pageSize): Observable<any> {
    switch (type) {
      case 0:
        const token = localStorage.getItem('OAuth2TOKEN');
        let headers = new HttpHeaders();
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        headers = headers.append('Authorization', 'Bearer ' + token);
        headers = headers.append('Content-Type', 'application/json');
        headers.append('Access-Control-Allow-Origin', '*');
        return this.http.get(this.getProcedureURL + '/procedure' + searchString + '&page=0', { headers }).pipe();
      case 1:
        return this.http.get(this.getProcedureURL + '/procedure' + searchString + '&page=0', { headers }).pipe();
    }
  }
  getDetailProcedure(id, type): Observable<any> {
    switch (type) {
      case 0:
        const token = localStorage.getItem('OAuth2TOKEN');
        let headers = new HttpHeaders();
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        headers = headers.append('Authorization', 'Bearer ' + token);
        headers = headers.append('Content-Type', 'application/json');
        headers.append('Access-Control-Allow-Origin', '*');
        return this.http.get(this.getProcedureURL + '/procedure/' + id, { headers }).pipe();
      case 1:
        return this.http.get(this.getProcedureURL + '/procedure/' + id, { headers }).pipe();
    }
  }

  getEnableHueSync(): boolean{
    const config = this.deploymentService.getAppDeployment();
    if (!!config?.env?.sync?.hue?.enable){
      return config?.env?.sync?.hue?.enable;
    } else {
      return false;
    }
  }

  updateSectorAgency(data): Observable<any> {
    return this.http.put<any>(this.getSector + '/sector/--agency-sector' , data);
  }

  assignUserSector(data): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');

    return this.http.put<any>(this.getSector + '/sector-user', data, { headers });
  }

  getUserSectorOnlyOne(userId, agencyId): Observable<any> {
    const params = `?user-id=${userId}&agency-id=${agencyId}`;
    const URL = `${this.getSector}/sector-user/--only-one${params}`;
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.set('Content-Type', 'application/json');
    return this.http.get<any>(URL, { headers });
  }

  checkExistCodeSectorSpecific(data): Observable<any> {
    const token = localStorage.getItem('OAuth2TOKEN');
    let headers = new HttpHeaders();
    headers = headers.append('Authorization', 'OAuth 2.0' + token);
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.SectorSpecific + '--check-exist-code' + data, {headers} );
  }

  postSectorSpecific(data): Observable<any> {
    return this.http.post<any>(this.SectorSpecific, data);
  }

  getListQBHSectorSpecific(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.SectorSpecific + searchString, { headers });
  }

  getDetailSectorSpecific(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.SectorSpecific + id + '/--full', { headers });
  }

  updateSectorSpecific(id, data): Observable<any> {
    return this.http.put<any>(this.SectorSpecific + id, data);
  }

  deleteSectorSpecific(id, data): Observable<any> {
    return this.http.put<any>(this.SectorSpecific + id+'/--delete', data);
  }


}
