import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { ApiProviderService } from 'src/app/core/service/api-provider.service';
import { EnvService } from 'src/app/core/service/env.service';
import {IAgency} from "data/schema/agency";
import { Utils} from "shared/ts/utils";

@Injectable({
    providedIn: 'root'
})
export class AgencyService {
    private basedata = this.apiProviderService.getUrl('digo', 'basedata');
    config = this.envService.getConfig();
    constructor(
        private http: HttpClient,
        private envService: EnvService,
        private apiProviderService: ApiProviderService,
    ) { }

    private getNameCodeFullyUrl(id: string): string{
        return this.apiProviderService.getUrl('digo', 'basedata') + `/agency/${id}/name+code/--fully`;
    }
    agencyUrl = this.apiProviderService.getUrl('digo', 'basedata') + '/agency/';
    // agencyUrl = 'http://127.0.0.1:8888/agency/';
    public getNameCodeFully(id: string) : Observable<any> {
        return this.http.get(this.getNameCodeFullyUrl(id));
    }

    public getRelateWithIds(ids: string, keyword: string, page: number, size: number) : Observable<any> {
        let headers = new HttpHeaders();
        headers = headers.set('Content-Type', 'application/json');
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        let searchStr = `page=${page}&size=${size}`;
        if(ids){
            searchStr += `&ids=${ids}`;
        }
        if(keyword){
            searchStr += `&keyword=${keyword}`;
        }
        return this.http.get<any>(this.agencyUrl + `/--relate-with-ids?${searchStr}`, { headers });
    }

    public getRelatedWithIdsAndTags(options:{ids?: string[],tagIds?: string[], keyword?: string, page?: number, size?: number}) : Observable<any> {
      const {ids,tagIds, keyword, page, size} = options;
      let headers = new HttpHeaders();
      headers = headers.set('Content-Type', 'application/json');
      headers = headers.set('Accept-Language', localStorage.getItem('language'));
      const params:any = {
        page:page,
        size:size
      };
      if(ids && ids.length>0){
          params.ids = ids.join(',');
      }
      if(tagIds && tagIds.length>0){
        params['tag-ids'] = tagIds;
      }
      if(keyword){
        params.keyword = keyword;
      }
      return this.http.get<any>(this.agencyUrl + '--relate-with-ids', { params,headers });
    }

  public getAgency(isRoot:boolean):IAgency{
    let agency:IAgency = {
      rootAgencyId: this.config.rootAgency.id,
      rootAgencyCode: this.config.rootAgency.code
    };

    if(!isRoot){
      const userAgency = JSON.parse(localStorage.getItem('userAgency'));
      if(userAgency){
        agency = {
          rootAgencyId: userAgency.id,
          rootAgencyCode: userAgency.code
        }
      }
    }

    return agency;
  }

  public getAgencies():any{
    const agency = JSON.parse(localStorage.getItem('userAgency'));
    let agencies = {
      agency: {},
      rootAgency:{
        id: this.config.rootAgency.id,
        code: this.config.rootAgency.code,
        name: this.config.rootAgency.trans.vi.name
      }
    };
    if(agency){
      agencies.agency = {
        id: agency.id,
        name: agency.name,
        code: agency.code
      }
    }
    return agencies;
  }

  public getAgencyAndAncestors():any{
    const agency = JSON.parse(localStorage.getItem('userAgency'));
    let ancestor = agency?.ancestors;

    if(!!ancestor && ancestor.length !== 0){
      ancestor.forEach(item => {
        delete item['code'];
        delete item['parent'];
        item.name = item.name[0].name;
      });
    }

    let agencies = {
      agency: {},
      ancestors: []
    }

    agencies.agency = {
      id: agency?.id,
      name: agency?.name
    }

    agencies.ancestors = ancestor;

    return agencies;
  }

  getAgencyReceiveTanDanDocument(page?:number, size?:number, keyword?:string, ancestorId?:string):Observable<any>{
      let URL = `${this.basedata}/agency/name+code?tag-id=0000591c4e1bd312a6f00003&ancestor-id=${ancestorId}&page=${page}&size=${size}`;
      if(keyword)
        URL += `&keyword=${keyword}`;
      let headers = new HttpHeaders();
      headers = headers.set('Accept-Language', localStorage.getItem('language'));
      return this.http.get<any>(URL,{ headers });
    }
  
    getRootAgency(agencyId: string): Promise<any> {
      let headers = new HttpHeaders();
      headers = headers.set('Accept-Language', localStorage.getItem('language'));
      headers = headers.append('Content-Type', 'application/json');
      headers.append('Access-Control-Allow-Origin', '*');
      return this.http.get(this.agencyUrl + `${agencyId}/--find-root`, {headers}).toPromise();
    }

    getPageAgency(page?: number, size?: number, parentId?: string, tagId?: string): Observable<any> {
      let headers = new HttpHeaders();
      headers = headers.set('Accept-Language', localStorage.getItem('language'));
      let URL = this.agencyUrl + `/--search?page=${page}&size=${size}&sort=name.name,asc&status=1`;
      if (parentId) {
        URL += `&parent-id=${parentId}`;
      }
      if (tagId) {
        URL += `&tag-id=${tagId}`;
      }
      return this.http.get(URL, { headers }).pipe();
    }

    getAgencyById(id:String):Observable<any>{
      let headers = new HttpHeaders();
      headers = headers.set('Accept-Language', localStorage.getItem('language'));
      return this.http.get(this.agencyUrl+id,{headers});
    }

    getAgencyByBatch(agencyFiltered) : Observable<any> {
      const filterd = Utils.removeNullProperties(agencyFiltered);
      let headers = new HttpHeaders();
      headers = headers.set('Accept-Language', localStorage.getItem('language'));
      let params = new HttpParams();
      Object.keys(filterd).forEach( 
        key => {
          params = params.set(key,filterd[key]);
        }
      )
      return this.http.get(this.agencyUrl + '--get-agency-ids-batch',{headers,params});
    }

    getParentAgencyByTagId(tagId) : Observable<any> {
      let headers = new HttpHeaders();
      headers = headers.set('Accept-Language', localStorage.getItem('language'));
      return this.http.get(this.agencyUrl + `${tagId}/--find-parent-agency`,{headers});
    }

    getAgencyList(searchString): Observable<any> {
      let headers = new HttpHeaders();
      headers = headers.set('Accept-Language', localStorage.getItem('language'));
      headers = headers.append('Content-Type', 'application/json');
      headers.append('Access-Control-Allow-Origin', '*');
      return this.http.get(this.agencyUrl + '--search' + searchString, { headers }).pipe();
    }

    getIdAgencyCoQuanHanhChinhHTTPDLK(ltsIdAgency, tagId) : Promise<any> {
      let headers = new HttpHeaders();
      headers = headers.set('Accept-Language', localStorage.getItem('language'));
      let search = `?tag-id=${tagId}&agency-ids=${ltsIdAgency}`;
      return this.http.get(this.agencyUrl + '/--id-agency-CQHC-HTTP-DLK' + search, {headers}).toPromise();
    }

    getAgencyByParent(searchString) :Observable<any> {
      let headers = new HttpHeaders();
      headers = headers.set('Accept-Language', localStorage.getItem('language'));
      return this.http.get(this.agencyUrl + '--by-parent-agency' + searchString, {headers});
    }
}
