<div class="body">
    <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="column" fxLayoutAlign="space-between">
        <h3 class="dialog_title" mat-dialog-title i18n><PERSON><PERSON> s<PERSON>ch hồ sơ</h3>
        <div fxLayout="row" fxLayoutAlign="center">
            <div>
                <button mat-flat-button class="exportExcelBtn" (click)="exportToExcelBtn()" [disabled]="isLoading">
                    <mat-icon class="iconStatistical" *ngIf="!isLoading">cloud_download</mat-icon>
                    <mat-spinner diameter="25" class="iconStatistical" *ngIf="isLoading"></mat-spinner>
                    <div style="width: 5px;"></div>
                    <span>Xuất excel</span>
                </button>
            </div>
            <button mat-flat-button class="closeBtn" (click)="onDismiss()">
                <span i18n>Đóng</span>
            </button>
        </div>
    </div>
    <div style="height: 12px;"></div>
    <div class="frm_tbl0">
        <div class="logbookTbl">
            <table style="width: 100%;" class="table">
                <thead>
                    <th>STT</th>
                    <th>Số hồ sơ</th>
                    <th>Tên thủ tục hành chính</th>
                    <th>Tên lĩnh vực</th>
                    <th>Đơn vị</th>
                    <th>Ngày tiếp nhận</th>
                    <th>Ngày hẹn trả</th>
                    <th>Ngày kết thúc xử lý</th>
                    <th>Trạng thái hồ sơ</th>
                    <th>Hình thức nhận kết quả</th>
                </thead>
                <tbody>
                    <ng-container *ngFor="let row of dataSource.data">
                        <tr>
                            <td>{{row.stt}}</td>
                            <td>{{row.code}}</td>
                            <td style="text-align: start;">{{row.procedureName}}</td>
                            <td>{{row.sectorName}}</td>
                            <td>{{row.agencyName}}</td>
                            <td>{{row.acceptedDate|date:'dd/MM/yyyy HH:mm:ss'}}</td>
                            <td>{{row.appointmentDate|date:'dd/MM/yyyy HH:mm:ss'}}</td>
                            <td>{{row.completedDate|date:'dd/MM/yyyy HH:mm:ss'}}</td>
                            <td>{{row.dossierStatusName}}</td>
                            <td>{{row.receivingKind}}</td>
                        </tr>
                    </ng-container>
                </tbody>
            </table>
        </div>
    </div>
    <pagination-slice id="pgnx" [itemsPerPage]="size" [currentPage]="page" [totalItems]="countResult"
        [pageSizeOptions]="[].concat(pgSizeOptions)" [dataSource]="ELEMENTDATA" (change)="changePageOrSize($event)"
        [type]="paginationType">
    </pagination-slice>

</div>