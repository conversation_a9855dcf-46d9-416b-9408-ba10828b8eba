<button mat-icon-button class="close-button" (click)="onDismiss()">
    <mat-icon>close</mat-icon>
</button>
<h3 class="dialog_title" mat-dialog-title><PERSON>ra <PERSON><PERSON><PERSON> <PERSON><PERSON>h hồ sơ</h3>
<div class="quickSearchDialogContent">
    <!-- Search Form -->
    <form [formGroup]="searchForm" (submit)="findDossier()" class="searchForm edit">
        <div fxLayout="row" fxLayout.xs="row" fxLayout.sm="row" fxLayoutAlign="center center" class="formFieldItems" fxLayoutAlign="space-between">
            <mat-form-field appearance="outline" fxFlex.gt-md="24" fxFlex.gt-sm="32" fxFlex.gt-xs="49.5" fxFlex='grow'>
                <mat-label>Tên người nộp</mat-label>
                <input type="text" matInput formControlName="applicant">
            </mat-form-field>
            <mat-form-field appearance="outline" fxFlex.gt-md="24" fxFlex.gt-sm="32" fxFlex.gt-xs="49.5" fxFlex='grow'>
                <mat-label>Mã số hồ sơ</mat-label>
                <input type="text" matInput formControlName="code">
            </mat-form-field>
            <mat-form-field appearance="outline" fxFlex.gt-md="24" fxFlex.gt-sm="32" fxFlex.gt-xs="49.5" fxFlex='grow'>
                <mat-label>Số CMND/CCCD người nộp</mat-label>
                <input type="text" matInput formControlName="identityNumber">
            </mat-form-field>
            <mat-form-field appearance="outline" fxFlex.gt-md="24" fxFlex.gt-sm="32" fxFlex.gt-xs="49.5" fxFlex='grow'>
                <mat-label>Số điện thoại người nộp</mat-label>
                <input type="text" matInput formControlName="phoneNumber">
            </mat-form-field>
            <ng-container *ngIf="this.env.OS_HCM?.quickSearchByTaskStatusId === 1 ? true : false; else normalQuickSearch">
                <mat-form-field appearance="outline" fxFlex.gt-md="24" fxFlex.gt-sm="32" fxFlex.gt-xs="49.5" fxFlex='grow'>
                    <mat-label>Đơn vị đang thực hiện</mat-label>
                    <mat-select formControlName="agency" msInfiniteScroll (infiniteScroll)="getNextBatch()" [complete]="totalPages <= currentPage+1">
                        <div>
                            <div>
                                <input matInput #searchInput (keyup)="onEnter($event)" (keydown)="$event.stopPropagation()"
                                    placeholder="Nhập từ khóa" class="search-nested" />
                                <button mat-icon-button class="clear-search-nested" *ngIf="searchInput.value !== ''"
                                    (click)="searchInput.value = ''; resetSearchForm()">
                                    <mat-icon> close </mat-icon>
                                </button>
                            </div>
                        </div>
                        <mat-option value="">Tất cả</mat-option>
                        <mat-option *ngFor="let item of listAgency" value="{{ item.id }}"> {{ item.name }} </mat-option>
                    </mat-select>
                </mat-form-field>
                <mat-form-field appearance="outline" fxFlex.gt-md="24" fxFlex.gt-sm="32" fxFlex.gt-xs="49.5" fxFlex='grow'>
                    <mat-label i18n>Trạng thái hồ sơ</mat-label>
                    <mat-select formControlName="advTaskStatusId" msInfiniteScroll (infiniteScroll)="getListDossierTaskName()" [complete]="isFullListDossierTaskName == true">
                        <mat-option value=""><span i18n>Tất cả</span></mat-option>
                        <mat-option *ngFor='let taskNameOpt of listDossierTaskName' value="{{taskNameOpt.id}}">
                            {{taskNameOpt.name}}
                            <span *ngIf="taskNameOpt.name == undefined || taskNameOpt.name == null || taskNameOpt.name.trim() == ''" i18n>(Không tìm thấy bản dịch)</span>
                        </mat-option>
                    </mat-select>
                </mat-form-field>
            </ng-container>
            <ng-template #normalQuickSearch>
                <mat-form-field appearance="outline" fxFlex.gt-md="49.5" fxFlex.gt-sm="32" fxFlex.gt-xs="49.5" fxFlex='grow'>
                    <mat-label>Đơn vị đang thực hiện</mat-label>
                    <mat-select formControlName="agency" msInfiniteScroll (infiniteScroll)="getNextBatch()" [complete]="totalPages <= currentPage+1">
                        <div>
                            <div>
                                <input matInput #searchInput (keyup)="onEnter($event)" (keydown)="$event.stopPropagation()"
                                    placeholder="Nhập từ khóa" class="search-nested" />
                                <button mat-icon-button class="clear-search-nested" *ngIf="searchInput.value !== ''"
                                    (click)="searchInput.value = ''; resetSearchForm()">
                                    <mat-icon> close </mat-icon>
                                </button>
                            </div>
                        </div>
                        <mat-option value="">Tất cả</mat-option>
                        <mat-option *ngFor="let item of listAgency" value="{{ item.id }}"> {{ item.name }} </mat-option>
                    </mat-select>
                </mat-form-field>
            </ng-template>
            <mat-form-field appearance="outline" fxFlex.gt-md="24" fxFlex.gt-sm="32" fxFlex.gt-xs="49.5" fxFlex='grow'>
                <mat-label>Từ ngày</mat-label>
                <input matInput [matDatepicker]="pickerAcceptFrom" formControlName="acceptFrom">
                <mat-datepicker-toggle matSuffix [for]="pickerAcceptFrom"></mat-datepicker-toggle>
                <mat-datepicker #pickerAcceptFrom></mat-datepicker>
            </mat-form-field>
            <mat-form-field appearance="outline" fxFlex.gt-md="24" fxFlex.gt-sm="32" fxFlex.gt-xs="49.5" fxFlex='grow'>
                <mat-label>Đến ngày</mat-label>
                <input matInput [matDatepicker]="pickerAcceptTo" formControlName="acceptTo">
                <mat-datepicker-toggle matSuffix [for]="pickerAcceptTo"></mat-datepicker-toggle>
                <mat-datepicker #pickerAcceptTo></mat-datepicker>
            </mat-form-field>
        </div>

        <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="column" fxLayoutAlign="center" fxLayoutGap="8px">
            <button mat-flat-button fxFlex='20' class="searchBtn" type="submit">
                <span>Tìm kiếm</span>
            </button>
        </div>
    </form>

    <!-- Table -->
    <div *ngIf="checkNullData === 1 then nullData; else hasData"></div>
    <ng-template #nullData>
        <br />
        <div fxLayout="row" fxLayoutAlign="center">
            <div fxFlex="grow" style="text-align: center; font-size: 18px">Không có kết quả tìm kiếm</div>
        </div>
    </ng-template>
    <ng-template #hasData>
    <div class="tbl">
        <mat-dialog-content class="tbl_dialog_content">
            <mat-table [dataSource]="dataSource">
                <ng-container matColumnDef="code">
                    <mat-header-cell *matHeaderCellDef>Mã số hồ sơ</mat-header-cell>
                    <mat-cell *matCellDef="let row" data-label="Mã số hồ sơ">
                        <span class="code">{{row.code}}</span>
                    </mat-cell>
                </ng-container>

                <ng-container matColumnDef="procedure">
                    <mat-header-cell *matHeaderCellDef>Thủ tục</mat-header-cell>
                    <mat-cell *matCellDef="let row" data-label data-label="Thủ tục">
                        <div>
                            <span class="procedure-code">{{row.procedure.code}}</span>
                            <span class="procedure-name" *ngIf="row.procedure.translate" #tooltip="matTooltip" matTooltip="{{row?.procedure?.translate?.name}}">
                                - {{row?.procedure?.translate?.name}}
                            </span>
                        </div>
                    </mat-cell>
                </ng-container>

                <ng-container matColumnDef="timing">
                    <mat-header-cell *matHeaderCellDef>Thời gian quy định</mat-header-cell>
                    <mat-cell *matCellDef="let row" data-label="Thời gian quy định">
                        <ul style="margin-left: -25px">
                            <span *ngIf="!qbhAdditionRequirementTime && ((row.acceptedDate != undefined && row.dossierStatus.id < 5 && row.undefindedCompleteTime === 0) || (hideCalcutatorDue && row.acceptedDate != undefined && row.dossierStatus.id < 4 && row.undefindedCompleteTime === 0))" class="due">
                                <span *ngIf="row.due.length > 0">
                                    <span *ngIf="row.due[0].timesheet.isOverDue == true" class="overdue">Đã quá hạn </span>
                            <span *ngIf="row.due[0].timesheet.isOverDue == false">Còn lại </span>
                            <span [ngClass]="{'overdue': row.due[0].timesheet.isOverDue == true}">
                                        <span class="timeNumber">{{row.due[0].timesheet.timer.day}}</span><span> ngày </span>
                            <span class="timeNumber">{{row.due[0].timesheet.timer.hour}}</span><span> giờ </span>
                            <span class="timeNumber">{{row.due[0].timesheet.timer.minute}}</span><span> phút </span>
                            <span class="timeNumber">{{row.due[0].timesheet.timer.second}}</span><span> giây </span>
                            </span>
                            </span>
                            </span>
                            <span *ngIf="qbhAdditionRequirementTime && ((row.acceptedDate != undefined && row.dossierStatus.id < 5 && row.undefindedCompleteTime === 0) || (hideCalcutatorDue && row.acceptedDate != undefined && row.dossierStatus.id < 4 && row.undefindedCompleteTime === 0)) && row.qbhShowOverDueTime" class="due">
                                <span *ngIf="row.due.length > 0">
                                    <span *ngIf="row.due[0].timesheet.isOverDue == true" class="overdue">Đã quá hạn </span>
                            <span *ngIf="row.due[0].timesheet.isOverDue == false">Còn lại </span>
                            <span [ngClass]="{'overdue': row.due[0].timesheet.isOverDue == true}">
                                        <span class="timeNumber">{{row.due[0].timesheet.timer.day}}</span><span> ngày </span>
                            <span class="timeNumber">{{row.due[0].timesheet.timer.hour}}</span><span> giờ </span>
                            <span class="timeNumber">{{row.due[0].timesheet.timer.minute}}</span><span> phút </span>
                            <span class="timeNumber">{{row.due[0].timesheet.timer.second}}</span><span> giây </span>
                            </span>
                            </span>
                            </span>
                            <li><span>Ngày nộp: </span>{{row.appliedDate | date : 'dd/MM/yyyy HH:mm:ss'}}</li>
                            <li *ngIf="row.acceptedDate != undefined"><span>Ngày tiếp nhận: </span>{{row.acceptedDate | date : 'dd/MM/yyyy HH:mm:ss'}}</li>
                            <li *ngIf="row.acceptedDate != undefined && hideDueProcessInfo == false"><span>Hạn xử lý toàn quy trình: </span>
                                <ng-container *ngIf="enableRequestAdditionalDossier; else dueBlock">
                                    {{ (row?.dueDate? row?.dueDate :row.dossierEndDate)  | date : 'dd/MM/yyyy HH:mm:ss'}} -
                                </ng-container>
                                <ng-template #dueBlock>
                                    {{row.dossierEndDate | date : 'dd/MM/yyyy HH:mm:ss'}}
                                  </ng-template>
                            </li>
                            <li *ngIf="row.acceptedDate != undefined && row.undefindedCompleteTime === 0"><span>Ngày hẹn trả: </span>{{row.appointmentDate | date : 'dd/MM/yyyy HH:mm:ss'}}</li>
                        </ul>
                    </mat-cell>
                </ng-container>

                <ng-container matColumnDef="applicant">
                    <mat-header-cell *matHeaderCellDef>Người nộp</mat-header-cell>
                    <mat-cell *matCellDef="let row" data-label="Người nộp">
                        <span>{{row?.applicant?.data?.fullname}}</span>
                    </mat-cell>
                </ng-container>

                <ng-container matColumnDef="agency">
                    <mat-header-cell *matHeaderCellDef>Cơ quan thực hiện</mat-header-cell>
                    <mat-cell *matCellDef="let row" data-label="Cơ quan thực hiện">
                        <span>{{check(row)}}</span>
                        <!-- <span>{{row.agency.name}}</span>-->
                    </mat-cell>
                </ng-container>

                <ng-container matColumnDef="status">
                    <mat-header-cell *matHeaderCellDef>Trạng thái</mat-header-cell>
                    <mat-cell *matCellDef="let row" data-label="Trạng thái">
                        <span [style.color]="getStatusColor(row.dossierStatus.id)">{{row.dossierStatus.name}}</span>
                    </mat-cell>
                </ng-container>

                <ng-container matColumnDef="action">
                    <mat-header-cell *matHeaderCellDef>Thao tác</mat-header-cell>
                    <ng-container *ngIf="this.qbhEditQuickSearch;then qbhSearch else normalS"></ng-container>
                    <ng-template #normalS>
                    <mat-cell *matCellDef="let row" data-label="Thao tác">
                        <button mat-icon-button [matMenuTriggerFor]="actionMenu">
                            <mat-icon>more_horiz</mat-icon>
                        </button>
                        <mat-menu #actionMenu="matMenu" xPosition="before">
                            <button mat-menu-item class="menuAction" *ngIf="row.task != undefined && row.task.length > 0" (click)="viewProcess(row.id, row.code)">
                                <mat-icon>insights</mat-icon><span>Xem quy trình</span>
                            </button>
                            <button mat-menu-item class="menuAction" (click)="dossierDetail(row.id, row.procedure.id, row.task)">
                                <mat-icon>info</mat-icon><span>Chi tiết hồ sơ</span>
                            </button>
                            <button *ngIf="visiableBtnDelete && isAdmin" mat-menu-item class="menuAction" (click)="deleteDialog(row.id, row.code)">
                                <mat-icon>delete_outline</mat-icon><span>Xóa</span>
                            </button>
                            <button mat-menu-item class="menuAction" 
                                *ngIf="
                                    (this.env.OS_HCM?.quickSearchByTaskStatusId === 1 ? true : false)
                                    && row.dossierStatus.id != 1 
                                    && row.requireAdditional" 
                                    (click)="additionalRequirement(row.id, row.code)">
                                <mat-icon>reply</mat-icon><span>Yêu cầu bổ sung</span>
                            </button>
                            <button mat-menu-item class="menuAction" *ngIf="(row.dossierStatus.id == 2 || row.dossierStatus.id == 4) && !isQniQS" (click)="suspenDialog(row.id, row.code)">
                                <mat-icon>pause_circle_outline</mat-icon><span>Tạm dừng</span>
                            </button>
                            <!-- <button mat-menu-item class="menuAction" *ngIf="row.dossierStatus.id == 3" (click)="resumeDialog(row.id, row.code)">
                                <mat-icon>autorenew</mat-icon><span>Tiếp tục xử lý</span>
                            </button>
                            <button mat-menu-item class="menuAction" *ngIf="row.dossierStatus.id == 0" (click)="refuseDialog(row.id, row.code)">
                                <mat-icon>block</mat-icon><span>Từ chối</span>
                            </button>
                            <button mat-menu-item class="menuAction" *ngIf="row.due.length > 0 && row.due[0].timesheet.isOverDue == true" (click)="addApologyText(row.id)">
                                <mat-icon>assignment_returned</mat-icon><span>Tải xuống mẫu văn bản xin lỗi</span>
                            </button>
                            <button mat-menu-item class="menuAction" *ngIf="row.due.length > 0 && row.due[0].timesheet.isOverDue == true" (click)="signApologyText(row.id)">
                                <mat-icon>note_add</mat-icon><span>Thêm văn bản xin lỗi</span>
                            </button> -->
                            <ng-container *ngIf="enableWithdrawDossierBtn; else normalwithdrawn">
                                <button mat-menu-item class="menuAction" *ngIf="(row.dossierStatus.id !== 5 && row.dossierStatus.id !== 4 && row.dossierStatus.id !== 12) && receivingPermission && !quickSearchHideWithdrawn" (click)="withdrawDialog(row.id, row.code)">
                                    <mat-icon>reply</mat-icon><span *ngIf="!isUserRQ;else isUserKGGTemplate">Yêu cầu rút hồ sơ</span>
                                    <ng-template #isUserKGGTemplate>
                                        <span>Rút hồ sơ theo yêu cầu</span>
                                    </ng-template>
                                </button>
                            </ng-container>
                            <ng-template #normalwithdrawn>
                            <button mat-menu-item class="menuAction" *ngIf="(row.dossierStatus.id !== 5 || row.dossierStatus.id !== 4) && !hideRequestToWithdraw && !quickSearchHideWithdrawn" (click)="withdrawDialog(row.id, row.code)">
                                <mat-icon>reply</mat-icon><span *ngIf="!isUserRQ;else isUserKGGTemplate">Yêu cầu rút hồ sơ</span>
                                <ng-template #isUserKGGTemplate>
                                    <span>Rút hồ sơ theo yêu cầu</span>
                                </ng-template>
                            </button>
                            <button mat-menu-item class="menuAction" *ngIf="showSignDesk && row.dossierStatus.id == 5" (click)="signDeskQNM(row.id)">
                                <mat-icon>edit</mat-icon><span>Ký nhận hồ sơ</span>
                            </button>
                            </ng-template>
                        </mat-menu>
                    </mat-cell>
                </ng-template>
                <ng-template #qbhSearch>
                    <mat-cell *matCellDef="let row" data-label="Thao tác">
                        <button mat-icon-button [matMenuTriggerFor]="actionMenu">
                            <mat-icon>more_horiz</mat-icon>
                        </button>
                        <mat-menu #actionMenu="matMenu" xPosition="before">
                            <button mat-menu-item class="menuAction" *ngIf="row.task != undefined && row.task.length > 0" (click)="viewProcess(row.id, row.code)">
                                <mat-icon>insights</mat-icon><span>Xem quy trình</span>
                            </button>
                            <ng-container *ngIf="row.checkUser == true">
                            <button mat-menu-item class="menuAction" (click)="dossierDetail(row.id, row.procedure.id, row.task)">
                                <mat-icon>info</mat-icon><span>Chi tiết hồ sơ</span>
                            </button>
                            <button *ngIf="visiableBtnDelete && isAdmin" mat-menu-item class="menuAction" (click)="deleteDialog(row.id, row.code)">
                                <mat-icon>delete_outline</mat-icon><span>Xóa</span>
                            </button>
                            <button mat-menu-item class="menuAction" 
                                *ngIf="
                                    (this.env.OS_HCM?.quickSearchByTaskStatusId === 1 ? true : false)
                                    && row.dossierStatus.id != 1 
                                    && row.requireAdditional" 
                                    (click)="additionalRequirement(row.id, row.code)">
                                <mat-icon>reply</mat-icon><span>Yêu cầu bổ sung</span>
                            </button>
                            <button mat-menu-item class="menuAction" *ngIf="(row.dossierStatus.id == 2 || row.dossierStatus.id == 4) && !isQniQS" (click)="suspenDialog(row.id, row.code)">
                                <mat-icon>pause_circle_outline</mat-icon><span>Tạm dừng</span>
                            </button>
                            <!-- <button mat-menu-item class="menuAction" *ngIf="row.dossierStatus.id == 3" (click)="resumeDialog(row.id, row.code)">
                                <mat-icon>autorenew</mat-icon><span>Tiếp tục xử lý</span>
                            </button>
                            <button mat-menu-item class="menuAction" *ngIf="row.dossierStatus.id == 0" (click)="refuseDialog(row.id, row.code)">
                                <mat-icon>block</mat-icon><span>Từ chối</span>
                            </button>
                            <button mat-menu-item class="menuAction" *ngIf="row.due.length > 0 && row.due[0].timesheet.isOverDue == true" (click)="addApologyText(row.id)">
                                <mat-icon>assignment_returned</mat-icon><span>Tải xuống mẫu văn bản xin lỗi</span>
                            </button>
                            <button mat-menu-item class="menuAction" *ngIf="row.due.length > 0 && row.due[0].timesheet.isOverDue == true" (click)="signApologyText(row.id)">
                                <mat-icon>note_add</mat-icon><span>Thêm văn bản xin lỗi</span>
                            </button> -->
                            <ng-container *ngIf="enableWithdrawDossierBtn; else normalwithdrawn">
                                <button mat-menu-item class="menuAction" *ngIf="(row.dossierStatus.id !== 5 && row.dossierStatus.id !== 4 && row.dossierStatus.id !== 12) && receivingPermission && !quickSearchHideWithdrawn" (click)="withdrawDialog(row.id, row.code)">
                                    <mat-icon>reply</mat-icon><span *ngIf="!isUserRQ;else isUserKGGTemplate">Yêu cầu rút hồ sơ</span>
                                    <ng-template #isUserKGGTemplate>
                                        <span>Rút hồ sơ theo yêu cầu</span>
                                    </ng-template>
                                </button>
                            </ng-container>
                            <ng-template #normalwithdrawn>
                            <button mat-menu-item class="menuAction" *ngIf="(row.dossierStatus.id !== 5 || row.dossierStatus.id !== 4) && !hideRequestToWithdraw && !quickSearchHideWithdrawn" (click)="withdrawDialog(row.id, row.code)">
                                <mat-icon>reply</mat-icon><span *ngIf="!isUserRQ;else isUserKGGTemplate">Yêu cầu rút hồ sơ</span>
                                <ng-template #isUserKGGTemplate>
                                    <span>Rút hồ sơ theo yêu cầu</span>
                                </ng-template>
                            </button>
                            <button mat-menu-item class="menuAction" *ngIf="showSignDesk && row.dossierStatus.id == 5" (click)="signDeskQNM(row.id)">
                                <mat-icon>edit</mat-icon><span>Ký nhận hồ sơ</span>
                            </button>
                            </ng-template>
                        </ng-container>
                        </mat-menu>
                    </mat-cell>
                </ng-template>
                </ng-container>

                <mat-header-row *matHeaderRowDef="displayedColumns; sticky: true"></mat-header-row>
                <mat-row *matRowDef="let row; columns: displayedColumns;"></mat-row>
            </mat-table>
        </mat-dialog-content>
        <div class="frm_Pagination">
            <ul class="temp_Arr">
                <li
                    *ngFor="let item of ELEMENTDATA  | paginate: {itemsPerPage: size, currentPage: page, totalItems: countResult, id: 'pgnxx'}">
                </li>
            </ul>
            <div class="pageSize">
                <span>Hiển thị </span>
                <mat-form-field appearance="outline">
                    <mat-select [(value)]="size" (valueChange)="paginate(pageIndex, 1)">
                        <mat-option *ngFor='let opt of pgSizeOptions;' [value]="opt">{{opt}}</mat-option>
                    </mat-select>
                </mat-form-field>
                <span><span>trên</span> {{countResult}} <span>bản ghi</span></span>
            </div>
            <div class="control">
                <pagination-controls id="pgnxx" (pageChange)="page = $event; paginate(page, 0)" responsive="true"
                    previousLabel="" nextLabel="">
                </pagination-controls>
            </div>
        </div>
    </div>
    </ng-template>
</div>