import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { DlkBaocaoChithi25CapXaRoutingModule } from './dlk-baocao-chithi25-capxa-routing.module';
import { DlkBaocaoChithi25CapXaComponent } from './dlk-baocao-chithi25-capxa.component';
import { SharedModule } from 'src/app/shared/shared.module';
import { NgxPrinterModule } from 'ngx-printer';
import {DossierDetailComponent} from '../dialogs/view-detail.component';


@NgModule({
  declarations: [DlkBaocaoChithi25CapXaComponent],
  imports: [
    CommonModule,
    DlkBaocaoChithi25CapXaRoutingModule,    
    SharedModule,
    
    
    NgxPrinterModule.forRoot({ printOpenWindow: true })
  ]
})
export class DlkBaocaoChithi25CapXaModule { }
