import java.text.SimpleDateFormat

def sdf = new SimpleDateFormat("yyyyMMddHHmmss")
def dateDefaultValue = sdf.format(new Date())

def version = env.BRANCH_NAME
def agentLabel = "211"

pipeline {
    agent { label agentLabel }
    options { disableConcurrentBuilds() }
    environment {
        project = "applications-s4t-web-onegate"
        nexus_repo_name = "docker-hosted"
        nexus_registry = "crelease.devops.s4t.vn:10141"
        NEXUS = credentials('NEXUS')
        rancherUrl = "https://rancher4t.vnpt.vn/p/local:p-n66st/workload/deployment:applications-s4t:web-onegate"
    }
    stages {
        stage('Prepare') {
            steps {
                script {
                    version = version + "-" + dateDefaultValue
                }
                echo 'Prepare ... from ' + env.Server_IP + ' - build-numer: ' + env.BUILD_ID
                echo 'Prepare from source: ' + env.WORKSPACE
                echo 'Prepare version: ' + version
            }
        }
        stage('Build Docker images and Push to local registry') {
            steps {
                script {
                    echo '##### Build docker file for ' + env.BRANCH_NAME + ' branch.'
                    sh "/bin/bash ./charts/build.sh ${project} $BRANCH_NAME Dockerfile ${nexus_registry} ${NEXUS_USR} ${NEXUS_PSW} ${version}"
                }
            }
        }
    }

    post {
        success {            
            withCredentials([string(credentialsId: 'TOKEN', variable: 'TOKEN_TELEGRAM'), string(credentialsId: 'CHAT_ID', variable: 'CHAT_ID_TELEGRAM'), usernamePassword(credentialsId: 'jenkinslog', passwordVariable: 'PASSWORD_JENKINS', usernameVariable: 'USER_JENKINS')]) {
                sh ("""
                    curl -s -X POST "https://api.telegram.org/bot${TOKEN_TELEGRAM}/sendMessage?chat_id=${CHAT_ID_TELEGRAM}&parse_mode=html&text=+<b>Build</b>: <i>$BUILD_URL</i> Published is OK %0A+<b>Image</b>: <i>${nexus_registry}/${project}:${version}</i>  %0A+<b>Re-deploy project</b>: <i>${rancherUrl}</i>"
                """)
            }
        }
        aborted {     
            withCredentials([string(credentialsId: 'TOKEN', variable: 'TOKEN_TELEGRAM'), string(credentialsId: 'CHAT_ID', variable: 'CHAT_ID_TELEGRAM'), usernamePassword(credentialsId: 'jenkinslog', passwordVariable: 'PASSWORD_JENKINS', usernameVariable: 'USER_JENKINS')]) {
                sh ("""
                    curl -s -X POST "https://api.telegram.org/bot${TOKEN_TELEGRAM}/sendMessage?chat_id=${CHAT_ID_TELEGRAM}&parse_mode=html&text=*****<b>${env.JOB_NAME}</b>***** %0A+<b>iGate Branch</b>: <i>${env.GIT_BRANCH}</i> %0A+<b>Build</b>: <i>$BUILD_URL</i> Published is Aborted"
                """)
            }
        }
        failure {  
            withCredentials([string(credentialsId: 'TOKEN', variable: 'TOKEN_TELEGRAM'), string(credentialsId: 'CHAT_ID', variable: 'CHAT_ID_TELEGRAM'), usernamePassword(credentialsId: 'jenkinslog', passwordVariable: 'PASSWORD_JENKINS', usernameVariable: 'USER_JENKINS')]) {
                sh ("""#!/bin/bash
                    curl -s -S  -u $USER_JENKINS:$PASSWORD_JENKINS ${env.BUILD_URL}logText/progressiveText?start=0 -o ${project}_${env.BRANCH_NAME}.txt
                    curl -s -X POST "https://api.telegram.org/bot${TOKEN_TELEGRAM}/sendMessage?chat_id=${CHAT_ID_TELEGRAM}&parse_mode=html&text=*****<b>${env.JOB_NAME}</b>***** %0A+<b>iGate Branch</b>: <i>${env.GIT_BRANCH}</i> %0A+<b>Build</b>: <i>$BUILD_URL</i> Published is Failure"
                    curl -F document=@${project}_${env.BRANCH_NAME}.txt https://api.telegram.org/bot${TOKEN_TELEGRAM}/sendDocument?chat_id=${CHAT_ID_TELEGRAM}
                """)
            }
        }
    }

}
