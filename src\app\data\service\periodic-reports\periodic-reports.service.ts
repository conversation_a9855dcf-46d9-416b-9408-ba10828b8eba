import { Injectable } from '@angular/core';
import {HttpClient, HttpHeaders, HttpParams} from '@angular/common/http';
import { ApiProviderService } from 'src/app/core/service/api-provider.service';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class PeriodicReportsService {

  constructor(
    private http: HttpClient,
    private apiProviderService: ApiProviderService
  ) { }

  private basepad = this.apiProviderService.getUrl('digo', 'basepad');
  private procedurePath = this.apiProviderService.getUrl('digo', 'basepad') + '/procedure/';
  private agencyPath = this.apiProviderService.getUrl('digo', 'basedata') + '/agency/';
  private human = this.apiProviderService.getUrl('digo', 'human');
  private basedata = this.apiProviderService.getUrl('digo', 'basedata');
  private reporter = this.apiProviderService.getUrl('digo', 'reporter');
  private padman = this.apiProviderService.getUrl('digo', 'padman');

  getListAgency(search: string): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.agencyPath + 'tree-view' + search, { headers });
  }

  getStatisticProcedure(search: String): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.basepad + '/procedure/--statistic-agency' + search, { headers });
  }

  getDetailAgency(id: String): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.basedata + '/agency/' + id, { headers });
  }

  getStatisticHuman(search: String): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.human + '/user/statistic/--by-agencyId' + search, { headers });
  }

  getStatisticDossier(search: String): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.reporter + '/dossier/statistic/--by-agency' + search, { headers });
  }
  getSectorByAgencyLevel(search: String): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.basepad + '/procedure/--get-sector-by-agency-level'+ search, { headers });
  }
  getProcedureBySector(search: String): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.basepad + '/procedure/--by-sector?id=' + search, { headers });
  }
  getProcedureLevelAndNpad(search: String): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.basepad + '/procedure/--get-level-and-npad?id=' + search, { headers });
  }
  countDossierByProcedure(search: String): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.reporter + '/dossier/statistic/--by-procedure-report-II08' + search, { headers });
  }

  countDossierByProcedureId(search: String): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.reporter + '/dossier/statistic/--by-procedure-report-II08' + search, { headers });
  }

  getTotalByAgency(search: String): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.basepad + '/procedure/report-dvc-onl/--group-by-agency' + search, { headers });
  }
  getSectorByAgencyLevelQBH(search: String): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.basepad + '/procedure/--get-sector-public-by-agency-level'+ search, { headers });
  }
  countDossierByProcedureQBH(search: String): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.reporter + '/dossier/statistic/--by-agency-report-II08-qbh' + search, { headers });
    //return this.http.get('http://localhost:8093/dossier/statistic/--by-agency-report-II08-qbh' + search, { headers });
  }
  countDossierByProcedureCMU(search: String): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.reporter + '/dossier/statistic/--by-agency-report-II08-cmu' + search, { headers });
    //return this.http.get('http://localhost:8080'+ '/dossier/statistic/--by-agency-report-II08-cmu' + search, { headers });
  }
  getSectorByAgencyLevelCMU(agencyId: String, levelCodes: String[]): Observable<any> {
    let headers = new HttpHeaders();
    const params: any ={
      "agency-level-id": agencyId,
      "level-codes": levelCodes
    }
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.basepad + '/procedure/--get-sector-public-by-agency-level-cmu', { headers:headers, params: params });
  }
  getSectorByAgencyLevelAGG(agencyId: string, levelCodes: string[]): Observable<any> {
    const headers = new HttpHeaders().set('Accept-Language', localStorage.getItem('language'));
    const params = new HttpParams()
      .set('agency-level-id', agencyId)
      .set('level-codes', levelCodes.join(','));

    return this.http.get(this.basepad + '/procedure/--get-sector-public-by-agency-level-agg', { headers, params });
  }
  countDossierByProcedureAGG(search: String): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.reporter + '/dossier/statistic/--by-agency-report-II08-agg' + search, { headers });
  }
  getSectorProcedureByLevelId(search: String): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.basepad + '/procedure/--get-sector-by-agency-hgg' + search, { headers });
  }

  getDossierSectorProcedureByLevelId(searchStr: String): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.padman + '/dossier-statistic/--by-agency-report-II08Hgg' + searchStr, { headers });
  }
}
