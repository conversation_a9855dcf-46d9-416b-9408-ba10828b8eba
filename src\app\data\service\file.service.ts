import {Injectable} from "@angular/core";
import {HttpClient, HttpHeaders} from "@angular/common/http";
import {ApiProviderService} from 'core/service/api-provider.service';
import {Observable} from "rxjs";

@Injectable({
  providedIn: 'root'
})
export class FileService {

  private filePath = this.apiProviderService.getUrl('digo', 'fileman') + '/file/';

  constructor(
    private http: HttpClient,
    private apiProviderService: ApiProviderService,
  ) {
  }
  getFileContent(url: string): Observable<Blob> {
    // Set headers if needed (content-type, authorization, etc.)
    const headers = new HttpHeaders();
    return this.http.get(url, {
      headers: headers,
      responseType: 'blob' // Ensure response type is blob
    });
  }
  getContentFileWatermark(id: string, watermark: boolean = true) {
    return this.apiProviderService.getUrl('digo', 'fileman') + '/file/' + id + '/contents/--water-mark?water-mark=' + watermark;
  }

  downloadFileWatermark(id, watermark: boolean = true): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.filePath + id + '/--water-mark?water-mark=' + watermark, { responseType: 'blob' as 'json' }).pipe();
  }
  
  convertDoc2Pdf(id: string): Promise<any> {
    const headers = new HttpHeaders();
    return this.http
      .put(this.filePath + id + '/--to-pdf', { headers })
      .toPromise();
  }

  fileGetLink(id, dossierId?): Promise<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.post(this.filePath + 'get-link?id=' + id + '&dossierId=' + dossierId, {}, { headers, responseType: 'text' as 'json' }).toPromise();
  }


  fileInfo(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    // return this.http.get<any>(this.apiProviderService.getUrl('digo', 'fileman') + '/wopi/files/' + id, { headers }).pipe();
    return this.http.get<any>(this.filePath + 'get-link/' + id, {headers}).pipe();
  }

  fileInfo2(id): Promise<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    // return this.http.get<any>(this.apiProviderService.getUrl('digo', 'fileman') + '/wopi/files/' + id, { headers }).toPromise();
    return this.http.get<any>(this.filePath + 'get-link/' + id, {headers}).toPromise();
  }

  //KGG OS
  convertToAuthElectronic(file: Blob, text: string, type: string): Promise<any> {
    const headers = new HttpHeaders().set('Authorization', 'Bearer ' + localStorage.getItem('token'));
    const formData: FormData = new FormData();
    formData.append('file', file);
    formData.append('text', text);
    formData.append('type', type);

    return this.http
      .post(this.filePath + '--convert-to-auth-electronic', formData, {
        headers,
        responseType: 'arraybuffer'  // Use 'arraybuffer' directly, not as 'json'
      })
      .toPromise()
      .then(response => {
        // Convert arraybuffer to Blob if needed
        return new Blob([response], { type: 'application/pdf' });
      })
      .catch(error => {
        console.error("Error occurred during file conversion:", error);
        throw error;
      });
}

}
