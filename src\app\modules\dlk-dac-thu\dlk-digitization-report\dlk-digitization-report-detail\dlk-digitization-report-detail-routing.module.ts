import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { DlkDigitizationReportDetailComponent } from './dlk-digitization-report-detail.component';


const routes: Routes = [
  {
    path: ':type',
    loadChildren: () => import('./dlk-digitization-report-detail.module').then(m => m.DlkDigitizationReportDetailModule),
    children: [
      {
        path: '',
        component: DlkDigitizationReportDetailComponent
      }
    ]
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class DlkDigitizationReportDetailRoutingModule { }
