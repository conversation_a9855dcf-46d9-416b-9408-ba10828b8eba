import { DatePipe } from '@angular/common';
import { AfterViewInit, ChangeDetectorRef, Component, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { FormControl, FormGroup } from '@angular/forms';
import { MatSelect } from '@angular/material/select';
import { MatTableDataSource } from '@angular/material/table';
import { DeploymentService } from 'data/service/deployment.service';
import { Workbook } from 'exceljs';
import { ReplaySubject, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { EnvService } from 'src/app/core/service/env.service';
import { DLKStatisticsService } from 'src/app/data/service/dlk-statistics/dlk-statistics.service';
import { MainService } from 'src/app/data/service/main/main.service';
import { ProcedureService } from 'src/app/data/service/procedure/procedure.service';
import { SnackbarService } from 'src/app/data/service/snackbar/snackbar.service';
import * as tUtils from 'src/app/data/service/thoai.service';
import * as fs from 'file-saver';
import { BaocaoChithi08DialogComponent } from '../dialogs/baocao-chithi08-dialog/baocao-chithi08-dialog.component';
import { MatDialog } from '@angular/material/dialog';

export interface Agency {
  id: string;
  name: string;
}
@Component({
  selector: 'app-dlk-baocao-chithi08-capdonvi',
  templateUrl: './dlk-baocao-chithi08-capdonvi.component.html',
  styleUrls: [
    './dlk-baocao-chithi08-capdonvi.component.scss',
    '/src/app/app.component.scss',
    '/src/app/shared/scss/form-field-outline.scss'
  ]
})
export class DlkBaoCaoChiThi08CapDonViComponent implements OnInit, AfterViewInit, OnDestroy {

  nowDate = tUtils.newDate();

  // tslint:disable-next-line: max-line-length
  toDate = this.nowDate.getFullYear() + '-' + ((this.nowDate.getMonth() + 1) <= 9 ? '0' + (this.nowDate.getMonth() + 1) : (this.nowDate.getMonth() + 1)) + '-' + ((this.nowDate.getDate() <= 9) ? ('0' + this.nowDate.getDate()) : this.nowDate.getDate());
  // tslint:disable-next-line: max-line-length
  fromDate = this.nowDate.getFullYear() + '-' + ((this.nowDate.getMonth() + 1) <= 9 ? '0' + (this.nowDate.getMonth() + 1) : (this.nowDate.getMonth() + 1)) + '-01';

  keyword = '';
  config = this.envService.getConfig();

  selectedLang = localStorage.getItem('language') || 'vi';
  selectedLangId = Number(localStorage.getItem('languageId')) || 228;
  pageTitle = {
    vi: 'Báo cáo chỉ thị 08 cấp đơn vị',
    en: 'Bao cao chi thi 08 cap don vi'
  };

  protected onDestroy = new Subject<void>();
  // =================== old
  env: any = this.deploymentService.getAppDeployment()?.env;
  ELEMENTDATA: any[] = [];
  dataSource: MatTableDataSource<any>;
  // listSector: Array<any> = [];
  keySearchSectorAgency = '';
  // Search Sector
  keywordSector = '';
  totalPagesSector = 0;
  currentPageSector = 0;
  pageSizeSector = 100;
  searchString = "";

  parentAgency = '';
  agencyId = '';
  agencyName = '';
  agencyDonviId = '';
  agencyDonViName = '';
  userAgency?: any = JSON.parse(localStorage.getItem('userAgency'));
  userAgencyCount?: any = JSON.parse(localStorage.getItem('listAgencyUser'))?.length || 0;
  ELEMENTDATAALL: any[] = [];
  dataSourceAll: MatTableDataSource<any>;
  waitingDownloadExcel: false;
  Agency = this.deploymentService.env.OS_DLK;

  isShowFilterSubAgency = false;
  isFullListSubAgency = false;
  listHoSoCapTinh = [];
  listHoSoCapHuyen = [];
  TongCapTinh: any;
  TongCapHuyen: any;
  TongCapTinhLK: any;
  TongCapHuyenLK: any;
  listHoSo = [];
  listThuTucHoSo = [];
  listHoSoLK = [];
  listThuTucHoSoLK = [];
  procedureAgencyLevel = this.deploymentService.env.statistics.procedureAgencyLevel;
  size = 10;
  pageIndex = 1;
  page = 1;
  countResult = 0;
  pgSizeOptions = this.config.pageSizeOptions;
  isAdmin?: boolean = sessionStorage.getItem('oneGateAdminMaster') === '1';
  paramsQuery = {
    page: 0,
    size: 10,
    fromDate: '',
    toDate: '',
    fromLKDate: '',
    toLKDate: '',
    agency: '',
    applyMethod: '',
    receivingKind: '',
    sector: '',
    keyword: '',
    procedure: '',
    sortId: '',
    isTTPVHCC: 1,
    agencyId: null,
    agencyName: '',
    agencyDonViId: null,
    agencyDonViName: '',
    childAgencyList: []
  };
  paramsDossier = {
    page: 0,
    size: 10,
    fromDate: null,
    toDate: null,
    agencyId: null,
    applyMethodId: null,
    receivingKind: null,
    hinhThucNop: null,
    keyword: '',
    dossierStatusId: null,
    procedureLevelId: null,
    code: ''
  };

  searchForm = new FormGroup({
    fromDate: new FormControl(''),
    toDate: new FormControl(''),
    sector: new FormControl(''),
    typeAgency: new FormControl(''),
    agencyLevel: new FormControl(''),
    agency: new FormControl(''),
    procedure: new FormControl(''),
    subAgencyCtrl: new FormControl(''),
    searchAgencyCtrl: new FormControl(''),
    searchSubAgencyCtrl: new FormControl('')
  });

  listAgencyAccept = [];
  listAgencyDonViAccept = [];
  listAgency = [];
  listAgencyDonVi = [];
  keywordAgencyAccept = '';
  totalPagesAgencyAccept = 0;
  currentPageAgencyAccept = 0;
  pageSizeAgencyAccept = 50;
  startDateCumulative = new Date();
  endDateCumulative = new Date();
  startDate = new Date();
  endDate = new Date();
  flagTTPVHCC = true;
  timeOutAgencyAccept: any = null;
  listSectorfillter: any[] = [];
  listSector: any[] = [];
  listSectorActive = [];
  listProcedure: any[] = [];
  listProcedurefillter: any[] = [];
  totalPagesProcedure = 0;
  currentPageProcedure = 0;
  searchProcedureKeyword = "";
  listHinhThucNhan: any[] = [];
  childAgencyList: [];
  EXCEL_EXTENSION = '.xlsx';
  EXCEL_TYPE = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8';

  @ViewChild('procedureMatSelectInfiniteScroll', { static: true }) procedureMatSelectInfiniteScroll: MatSelect;

  constructor(
    private envService: EnvService,
    public datepipe: DatePipe,
    private mainService: MainService,
    private cdRef: ChangeDetectorRef,
    private procedureService: ProcedureService,
    private datePipe: DatePipe,
    private deploymentService: DeploymentService,
    private dlkStatisticService: DLKStatisticsService,
    private snackbarService: SnackbarService,
    private dialog: MatDialog
  ) {
    this.dataSource = new MatTableDataSource(this.ELEMENTDATA);
    this.dataSourceAll = new MatTableDataSource(this.ELEMENTDATAALL);
  }

  ngOnInit(): void {
    this.mainService.setPageTitle(this.pageTitle[localStorage.getItem('language')]);
    this.startDate = new Date(this.fromDate);
    this.startDateCumulative = new Date(this.fromDate);
    this.endDate = new Date(this.toDate);

    if (this.userAgency !== null && this.userAgency !== undefined) {
      if (!!this.userAgency.parent && !!this.userAgency.parent.id && this.userAgencyCount === 1) {
        this.parentAgency = this.userAgency.parent.id;
        this.agencyId = this.userAgency.id;
        this.paramsQuery.agencyName = this.userAgency.parent.name;
      } else {
        this.parentAgency = this.userAgency.id;
        this.agencyId = this.userAgency.id;
        this.paramsQuery.agencyName = this.userAgency.name;
      }
      this.keySearchSectorAgency = '&agency-id=' + this.agencyId;
      this.searchString = "?arr-parent-id=" + this.parentAgency;
    }

    // this.getAgencyList(true);
    // this.getListAgencyAccept();
    this.getListAgencyAccept(this.Agency.rootAgencyId, '', 0, 2000, []);

  }

  ngAfterViewInit() {
    this.cdRef.detectChanges();
  }

  ngOnDestroy() {
    this.onDestroy.next();
    this.onDestroy.complete();
  }

  colToLetter(number) {
    let result = '';
    // number = number - 1; // If starting from 1
    do {
      const letter = String.fromCharCode(65 + (number % 26));
      result = letter + result;
      number = Math.floor(number / 26) - 1;
    } while (number >= 0)
    return result.toLowerCase();
  }

  thongKe() {
    this.paramsQuery.page = 0;
    this.page = 1;
    this.getListHoSo();
  }

  paginate(event) {
    this.paramsQuery.page = event;
  }

  getParentId(): string | null {
    const userAgency = JSON.parse(localStorage.getItem('userAgency'));
    if (userAgency) {
      if (!!userAgency.parent && !!userAgency.parent.id && !this.isAdmin) {
        return userAgency.parent.id;
      }
      else if (userAgency.id !== this.config.rootAgency.id || this.isAdmin) {
        return userAgency.id;
      }
    }
    return null;
  }

  listSectorProcedure: any = [];
  getListProcedureofDossier() {
    let lstprocedure = [];
    this.listSector = [];
    this.listProcedure = [];
    // Sử dụng Map để lưu các sector với key là sectorId
    const sectorMap = new Map();

    this.procedureService
      .getAllProcedureWithAgencyDLK('?agency-id=' + this.agencyId)
      .subscribe(
        (res) => {
          this.listSectorProcedure = res.content;
          if (this.listSectorProcedure.length > 0) {
            this.listSectorProcedure.forEach((item) => {
              // Lưu thông tin thủ tục, với item.status: 1 (mở) hoặc 0 (đóng)
              lstprocedure.push({
                sectorName: item.sectorName,
                sectorId: item.sectorId,
                procedureId: item.id,
                procedureName: item.name,
                MucDo: item.procedureLevelName,
                status: item.status
              });

              // Nếu sector chưa tồn tại thì thêm vào (dù thủ tục có trạng thái đóng)
              if (!sectorMap.has(item.sectorId)) {
                sectorMap.set(item.sectorId, {
                  sectorId: item.sectorId,
                  sectorName: item.sectorName,
                  status: item.status
                });
              } else {
                // Nếu sector đã tồn tại và tên khác nhau
                let existing = sectorMap.get(item.sectorId);
                if (existing.sectorName !== item.sectorName) {
                  // Nếu thủ tục hiện tại mở (status === 1) và sector đã lưu có trạng thái đóng (status === 0) thì cập nhật lại
                  if (item.status === 1 && existing.status === 0) {
                    sectorMap.set(item.sectorId, {
                      sectorId: item.sectorId,
                      sectorName: item.sectorName,
                      status: item.status
                    });
                  }
                  // Trường hợp khác, giữ nguyên sector đã lưu
                }
              }
            });
          }
          // Chuyển Map thành mảng, chỉ giữ lại sectorId và sectorName
          this.listSector = Array.from(sectorMap.values()).map(
            ({ sectorId, sectorName }) => ({ sectorId, sectorName })
          );
          this.listProcedure = lstprocedure;

          // Lọc lại danh sách lĩnh vực cho cơ quan
          this.getListSector();
        },
        (err) => {
          console.log(err);
        }
      );
  }

  getListSector() {
    const searchString = '?keyword=&page=0&size=1000&spec=page&sort=name.name,asc&status=1&only-agency-id=1&agency-id=' + this.agencyId;;
    let listSector = [];
    this.procedureService.getListSector(searchString).subscribe(res => {
      if (res != null && res.content != null && res.content.length > 0) {
        listSector = res.content;
        this.listSectorActive = res.content;
      }
    }, err => {
      console.log(err);
    });
  }

  // getListAgencyAccept() {
  //   const searchString = '?parent-id=' + this.Agency.rootAgencyId + '&keyword=&page=0&size=1000&sort=name.name,asc&status=1';
  //   this.procedureService.getListAgencyWithParent(searchString).subscribe(
  //     res => {
  //       this.listAgency = res.content;
  //       this.listAgencyAccept = res.content.filter(item => item.level != null && item.level.id == "5f39f42d5224cf235e134c5a");
  //       if (this.listAgencyAccept != null && this.listAgencyAccept.length > 0) {
  //         this.agencyId = this.listAgencyAccept[0].id;
  //         this.agencyName = this.listAgencyAccept[0]?.name;
  //         this.getListAgencyDonViAccept(this.agencyId);
  //       }
  //     }, err => {
  //       console.log(err);
  //     });
  // }

  getListAgencyAccept(prid, keyword, page, size, accumulatedData) {
    const searchString = `?parent-id=${prid}&keyword=${keyword}&page=${page}&size=${size}&sort=name.name,asc&status=1`;

    this.procedureService.getListAgencyWithParent(searchString).subscribe(
      (res: any) => {
        const merged = [...accumulatedData, ...res.content];

        if (res.last) {
          this.listAgency = merged;
          this.listAgencyAccept = merged.filter(item => item.level != null && item.level.id == "5f39f42d5224cf235e134c5a");
          if (this.listAgencyAccept != null && this.listAgencyAccept.length > 0) {
            this.agencyId = this.listAgencyAccept[0].id;
            this.agencyName = this.listAgencyAccept[0]?.name;
            this.getListAgencyDonViAccept(this.agencyId, 0 , []);
          }
        } else {
          this.getListAgencyAccept(prid, keyword, page + 1, size, merged);
        }
      },
      (err) => {
        console.error('Lỗi khi gọi API lấy danh sách agency:', err);
      }
    );
  }

  // getListAgencyDonViAccept(agencyCoQuan) {
  //   const searchString = '?parent-id=' + agencyCoQuan + '&keyword=&page=0&size=1000&sort=name.name,asc&status=1';
  //   this.procedureService.getListAgencyWithParent(searchString).subscribe(res => {
  //     this.listAgencyDonViAccept = res.content;
  //     if (this.listAgencyDonViAccept != null && this.listAgencyDonViAccept.length > 0) {
  //       this.agencyDonviId = this.listAgencyDonViAccept[0].id;
  //       this.agencyDonViName = this.listAgencyDonViAccept[0]?.name;
  //     }

  //     this.getListProcedureofDossier();
  //   }, err => {
  //     console.log(err);
  //   });
  // }

  getListAgencyDonViAccept(agencyCQ, page, accumulatedData) {
    const searchString = `?parent-id=${agencyCQ}&keyword=&page=${page}&size=2000&sort=name.name,asc&status=1`;

    this.procedureService.getListAgencyWithParent(searchString).subscribe(
      (res) => {
        const merged = [...accumulatedData, ...res.content];
        if (res.last) {
          this.listAgencyDonViAccept = merged;
          if (this.listAgencyDonViAccept != null && this.listAgencyDonViAccept.length > 0) {
            this.agencyDonviId = this.listAgencyDonViAccept[0].id;
            this.agencyDonViName = this.listAgencyDonViAccept[0]?.name;
          }

          this.getListProcedureofDossier();
        } else {
          this.getListAgencyDonViAccept(agencyCQ, page + 1, merged);
        }
      },
      (err) => {
        console.error('Lỗi khi gọi API lấy danh sách agency:', err);
      }
    );
  }

  getListHoSo() {
    if (this.validateForm()) {
      this.paramsQuery.fromDate = (this.startDate ? (this.datePipe.transform(this.startDate, 'yyyy-MM-dd') + 'T00:00:00.000Z') : '');
      this.paramsQuery.toDate = (this.endDate ? (this.datePipe.transform(this.endDate, 'yyyy-MM-dd') + 'T23:59:59.999Z') : '');
      this.paramsQuery.agencyId = this.agencyDonviId;

      this.dlkStatisticService.getlistDossierGrProcedure(this.paramsQuery).subscribe(res => {
        this.listHoSo = res;
        console.log(res);
        setTimeout(() => {
          // Gom nhóm procedure
          this.groupProcedure();
          this.BuilData();
        }, 1000);
      }, err => {
        console.log(err);
      });
    }
  }

  groupProcedure() {
    let groupedData = [];

    this.listProcedure.forEach(listProc => {
      const matchingData = this.listHoSo.filter(data => data.procedureId == listProc.procedureId);
      let procedureItem = {
        agencyId: '',
        agencyParentId: '',
        tiepNhanTrongKy: 0,
        tiepNhanTrucTiep: 0,
        hoSoBuuChinh: 0,
        tiepNhanMotPhan: 0,
        tiepNhanToantrinh: 0,
        dxltiepNhanTrucTiep: 0,
        dxlhoSoBuuChinh: 0,
        dxltiepNhanTrucTuyen: 0,
        tongdaXuLy: 0,
        sectorId: '',
        sectorName: '',
        procedureId: '',
        procedureName: '',
      };

      if (matchingData.length > 0) {
        matchingData.forEach(data => {
          procedureItem.agencyId = data.agencyId;
          procedureItem.agencyParentId = data.agencyParentId;
          procedureItem.tiepNhanTrongKy += data.tiepNhanTrongKy;
          procedureItem.tiepNhanTrucTiep += data.tiepNhanTrucTiep;
          procedureItem.hoSoBuuChinh += data.hoSoBuuChinh;
          procedureItem.tiepNhanMotPhan += data.tiepNhanMotPhan;
          procedureItem.tiepNhanToantrinh += data.tiepNhanToantrinh;
          procedureItem.dxltiepNhanTrucTiep += data.dxltiepNhanTrucTiep;
          procedureItem.dxlhoSoBuuChinh += data.dxlhoSoBuuChinh;
          procedureItem.dxltiepNhanTrucTuyen += data.dxltiepNhanTrucTuyen;
          procedureItem.sectorId = data.sectorId;
          procedureItem.sectorName = data.sectorName;
          procedureItem.procedureId = data.procedureId;
          procedureItem.procedureName = data.procedureName;
          procedureItem.tongdaXuLy += data.tongdaXuLy;
        });
        groupedData.push(procedureItem);
      }
    });
    this.listHoSo = groupedData;
  }

  SelectedAgency = "";
  GetDetailDossier(AgencyId, AgencyName, TrongKy, DaXuLy, TrucTiep, BCCI, MotPhan, ToanTrinh, All) {
    console.log("getlistDetailofDossier");
    this.paramsDossier.agencyId = AgencyId;
    this.SelectedAgency = AgencyName;
    if (TrongKy == 1) {
      this.paramsDossier.fromDate = (this.startDate ? this.datePipe.transform(this.startDate, 'yyyy-MM-dd') : '');
      this.paramsDossier.toDate = (this.endDate ? this.datePipe.transform(this.endDate, 'yyyy-MM-dd') : '');
    }
    else {
      this.paramsDossier.fromDate = (this.startDateCumulative ? this.datePipe.transform(this.startDateCumulative, 'yyyy-MM-dd') : '');
      this.paramsDossier.toDate = (this.endDateCumulative ? this.datePipe.transform(this.endDateCumulative, 'yyyy-MM-dd') : '');
    }
    if (DaXuLy == 1) {
      this.paramsDossier.dossierStatusId = 1;
    }
    if (BCCI == 1) {
      this.paramsDossier.hinhThucNop = 1;
    }
    if (TrucTiep == 1) {
      this.paramsDossier.applyMethodId = 1;
    }
    if (MotPhan == 2 && ToanTrinh == 2) {
      this.paramsDossier.applyMethodId = 0;
    }
    if (MotPhan == 1) {
      this.paramsDossier.procedureLevelId = "5f5b2c4b4e1bd312a6f3ae24";
    }
    if (ToanTrinh == 1) {
      this.paramsDossier.procedureLevelId = "5f5b2c564e1bd312a6f3ae25";
    }
    this.dlkStatisticService.getlistDetailofDossier(this.paramsDossier).subscribe(res => {
      console.log("getlistDetailofDossier");
      console.log(res);
    }, err => {
      console.log(err);
    });
  }

  changeAgency($event) {
    this.paramsQuery.agencyId = this.agencyId;
    this.getListAgencyDonViAccept(this.agencyId, 0, []);
    const value = $event.value;
    this.agencyId = value;
  }

  changeAgencyDonVi($event) {
    this.paramsQuery.agencyDonViId = this.agencyDonviId;
    const value = $event.value;
    this.agencyDonviId = value;
  }

  ListMain = [];
  ViewDetail(i, show) {
    console.log(!show);
    this.ListMain[i].show = !show;
  }

  BuilData() {
    this.ListMain = [];

    this.TongCapTinh = {
      tiepNhanTrongKy: 0,
      tiepNhanTrucTiep: 0,
      hoSoBuuChinh: 0,
      tiepNhanMotPhan: 0,
      tiepNhanToantrinh: 0,
      tongdaXuLy: 0,
      dxltiepNhanTrucTiep: 0,
      dxlhoSoBuuChinh: 0,
      dxltiepNhanTrucTuyen: 0
    };
    for (let i = 0; i < this.listSector.length; i++) {
      let sectorId = this.listSector[i]?.sectorId;
      let sectorName = this.listSector[i]?.sectorName;
      let ListThuTuc = this.listProcedure.filter(f => f.sectorId !== null && f.sectorId === sectorId);

      var main = {
        id: sectorId,
        sector: sectorName,
        show: false,
        tiepNhanTrongKy: 0,
        tiepNhanTrucTiep: 0,
        hoSoBuuChinh: 0,
        tiepNhanMotPhan: 0,
        tiepNhanToantrinh: 0,
        tongdaXuLy: 0,
        dxltiepNhanTrucTiep: 0,
        dxlhoSoBuuChinh: 0,
        dxltiepNhanTrucTuyen: 0,
        data: []
      }

      for (let j = 0; j < ListThuTuc.length; j++) {
        let ListThuTuctotal = this.listHoSo.filter(f => f.procedureId !== null && f.procedureId === ListThuTuc[j].procedureId);

        var arr = {
          status: ListThuTuc[j].status,
          sectorId: sectorId,
          sectorName: sectorName,
          procedureId: ListThuTuc[j].procedureId,
          procedureName: ListThuTuc[j].procedureName,
          MucDo: ListThuTuc[j].MucDo,
          tiepNhanTrongKy: 0,
          tiepNhanTrucTiep: 0,
          hoSoBuuChinh: 0,
          tiepNhanMotPhan: 0,
          tiepNhanToantrinh: 0,
          tongdaXuLy: 0,
          dxltiepNhanTrucTiep: 0,
          dxlhoSoBuuChinh: 0,
          dxltiepNhanTrucTuyen: 0
        }

        if (ListThuTuctotal.length > 0) {
          arr.tiepNhanTrongKy = ListThuTuctotal[0].tiepNhanTrongKy;
          arr.tiepNhanTrucTiep = ListThuTuctotal[0].tiepNhanTrucTiep;
          arr.hoSoBuuChinh = ListThuTuctotal[0].hoSoBuuChinh;
          arr.tiepNhanMotPhan = ListThuTuctotal[0].tiepNhanMotPhan;
          arr.tiepNhanToantrinh = ListThuTuctotal[0].tiepNhanToantrinh;
          arr.tongdaXuLy = ListThuTuctotal[0].tongdaXuLy;
          arr.dxltiepNhanTrucTiep = ListThuTuctotal[0].dxltiepNhanTrucTiep;
          arr.dxlhoSoBuuChinh = ListThuTuctotal[0].dxlhoSoBuuChinh;
          arr.dxltiepNhanTrucTuyen = ListThuTuctotal[0].dxltiepNhanTrucTuyen;


          main.tiepNhanTrongKy += arr.tiepNhanTrongKy;
          main.tiepNhanTrucTiep += arr.tiepNhanTrucTiep;
          main.hoSoBuuChinh += arr.hoSoBuuChinh;
          main.tiepNhanMotPhan += arr.tiepNhanMotPhan;
          main.tiepNhanToantrinh += arr.tiepNhanToantrinh;
          main.tongdaXuLy += arr.tongdaXuLy;
          main.dxltiepNhanTrucTiep += arr.dxltiepNhanTrucTiep;
          main.dxlhoSoBuuChinh += arr.dxlhoSoBuuChinh;
          main.dxltiepNhanTrucTuyen += arr.dxltiepNhanTrucTuyen;

          //this.listHoSo
          this.TongCapTinh.tiepNhanTrongKy += arr.tiepNhanTrongKy;
          this.TongCapTinh.tiepNhanTrucTiep += arr.tiepNhanTrucTiep;
          this.TongCapTinh.hoSoBuuChinh += arr.hoSoBuuChinh;
          this.TongCapTinh.tiepNhanMotPhan += arr.tiepNhanMotPhan;
          this.TongCapTinh.tiepNhanToantrinh += arr.tiepNhanToantrinh;
          this.TongCapTinh.tongdaXuLy += arr.tongdaXuLy;
          this.TongCapTinh.dxltiepNhanTrucTiep += arr.dxltiepNhanTrucTiep;
          this.TongCapTinh.dxlhoSoBuuChinh += arr.dxlhoSoBuuChinh;
          this.TongCapTinh.dxltiepNhanTrucTuyen += arr.dxltiepNhanTrucTuyen;
        }
        main.data.push(arr);
      }
      this.ListMain.push(main);
    }

    // Loại bỏ nhưng thủ tục ĐÓNG + Không phát sinh hồ sơ
    this.filterProcedureData(this.ListMain);
  }

  filterProcedureData(dataSector) {
    for (let i = 0; i < dataSector.length; i++) {
      let listProcedure = dataSector[i].data;
      if (listProcedure != null && listProcedure.length > 0) {
        for (let proIndex = 0; proIndex < listProcedure.length; proIndex++) {
          let procedureItemData = listProcedure[proIndex];
          if (procedureItemData != null && procedureItemData.status == 1) {
            continue;
          } else if (procedureItemData != null && procedureItemData.status == 0) {
            if (procedureItemData.p02_tiepnhan_tong_tky == 0 && procedureItemData.p22_dvctt_tong_tky == 0) {
              // Loại bỏ thủ tục khỏi lĩnh vực
              this.ListMain[i].data = this.ListMain[i].data.filter(item => item.procedureId != procedureItemData.procedureId);
            }
          }
        }
      }
    }
  }

  private listSubAgency: any[] = [];
  public subAgencyFiltered: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  protected subAgencies: any[] = this.listSubAgency;
  searchSubAgencyCtrl: FormControl = new FormControl();
  subAgengyId: any = '';
  listSubAgencyPage = 0;
  searchSubAgencyKeyword = '';

  getListSubAgency(parentId) {
    if (this.isFullListSubAgency) {
      return;
    } else {
      const userAgency = JSON.parse(localStorage.getItem('userAgency'));
      let rootAgencyId: any = '';
      rootAgencyId = this.config.rootAgency.id;
      const searchString = '?parent-id=' + parentId + '&page=' + this.listSubAgencyPage + '&size=15&sort=name.name,asc&keyword=' + this.searchSubAgencyKeyword;
      this.procedureService.getListAgencyWithParent(searchString).subscribe(data => {
        this.isFullListSubAgency = data.last;
        this.listSubAgencyPage++;
        for (let i = 0; i < data.numberOfElements; i++) {
          this.listSubAgency.push(data.content[i]);
        }
        this.subAgencies = JSON.parse(JSON.stringify(this.listSubAgency).replace(/null/g, '""'));
        this.subAgencyFiltered.next(this.subAgencies);
        this.searchSubAgencyCtrl.valueChanges.pipe(takeUntil(this.onDestroy)).subscribe(() => {
          this.filterSubAgency();
        });
      }, err => {
        console.log(err);
      });
    }
  }

  changeSubAgency($event) {
    this.subAgengyId = this.agencyId;
    const value = $event.value;
    if (value != null && value != '') {
      this.agencyId = value;
    } else {
      this.agencyId = this.subAgengyId;
    }
  }

  protected filterSubAgency() {
    if (!this.subAgencies) {
      return;
    }
    let search = this.searchSubAgencyCtrl.value.trim();
    this.searchSubAgencyKeyword = search;
    if (!search) {
      this.subAgencyFiltered.next(this.subAgencies.slice());
      return;
    } else {
      search = search.toLowerCase();
    }
    if (this.subAgencies.filter(agen => agen.name.toLowerCase().indexOf(search) > -1).length > 0) {
      this.subAgencyFiltered.next(
        this.subAgencies.filter(agen => agen.name.toLowerCase().indexOf(search) > -1)
      );
    } else {
      const userAgency = JSON.parse(localStorage.getItem('userAgency'));
      let rootAgencyId: any = '';
      rootAgencyId = this.env?.rootAgency.id;
      const searchString = '?parent-id=' + this.agencyId + '&page=0&size=15&sort=name.name,asc&keyword=' + search;
      this.listSubAgencyPage = 0;
      this.procedureService.getListAgencyWithParent(searchString).subscribe(data => {
        for (let i = 0; i < data.numberOfElements; i++) {
          this.listSubAgency.push(data.content[i]);
        }
        this.subAgencies = JSON.parse(JSON.stringify(this.listSubAgency).replace(/null/g, '""'));
        this.subAgencyFiltered.next(this.subAgencies);
      }, err => {
        console.log(err);
      });
    }
  }

  onClickTd(prop: string, type: number) {
    //data dialog
    let dialogData: any = {
      prid: this.agencyId,
      id: this.agencyDonviId,
      agencyName: this.agencyDonViName,
      prop: prop.slice(0, -4),
      type: type
    };

    dialogData.tuNgay = this.startDate;
    dialogData.denNgay = this.endDate;

    //mở dialog
    const dialogRef = this.dialog.open(BaocaoChithi08DialogComponent, {
      width: '95%',
      height: '90%',
      data: dialogData,
      disableClose: true,
      autoFocus: false
    });

    dialogRef.afterClosed().subscribe(() => {
    });
  }

  async exportToExcel() {
    if (this.validateForm) {
      this.thongKe();
      const from = this.datePipe.transform(this.startDate, 'dd/MM/yyyy');
      const to = this.datePipe.transform(this.endDate, 'dd/MM/yyyy');
      const newDateshort = this.datePipe.transform(new Date(), "dd/MM/yyyy")
      const newDate = this.datePipe.transform(new Date(), "dd/MM/yyyy HH:mm:ss")
      const excelFileName = `Bao_cao_chi_thi_08_capso_${newDate}`;
      let headerXLS = {
        row1: "CỘNG HÒA XÃ HỘI CHỦ NGHĨA VIỆT NAM",
        row2: "Độc lập - Tự do - Hạnh phúc",
        row3: "Đắk lắk, " + newDateshort,
        row4: `BÁO CÁO CHỈ THỊ 08 CẤP ĐƠN VỊ`,
        row5: `(Từ ${from} đến ngày ${to})`
      }

      const workbook = new Workbook();
      workbook.creator = 'Snippet Coder';
      workbook.lastModifiedBy = 'SnippetCoder';
      workbook.created = new Date();
      workbook.modified = new Date();
      const worksheet = workbook.addWorksheet("sheet1");

      // Add header row
      worksheet.addRow([]);
      worksheet.mergeCells('A1:O1');
      worksheet.getCell('A1').value = headerXLS.row1;
      worksheet.getCell('A1').alignment = { horizontal: 'center', vertical: 'middle' };
      worksheet.getCell('A1').font = { size: 13, bold: true, name: 'Times New Roman' };

      worksheet.mergeCells('A2:O2');
      worksheet.getCell('A2').value = headerXLS.row2;
      worksheet.getCell('A2').alignment = { horizontal: 'center', vertical: 'middle' };
      worksheet.getCell('A2').font = { size: 13, bold: true, name: 'Times New Roman' };

      worksheet.mergeCells('A3:O3');
      worksheet.getCell('A3').value = headerXLS.row3;
      worksheet.getCell('A3').alignment = { horizontal: 'center', vertical: 'middle' };
      worksheet.getCell('A3').font = { size: 13, underline: false, name: 'Times New Roman' };

      worksheet.mergeCells('A4:O4');
      worksheet.getCell('A4').value = headerXLS.row4;
      worksheet.getCell('A4').alignment = { horizontal: 'center', vertical: 'middle' };
      worksheet.getCell('A4').font = { size: 14, bold: true, name: 'Times New Roman' };

      worksheet.mergeCells('A5:O5');
      worksheet.getCell('A5').value = "";
      worksheet.getCell('A5').alignment = { horizontal: 'center', vertical: 'middle' };
      worksheet.getCell('A5').font = { size: 11, italic: true, name: 'Times New Roman' };

      worksheet.mergeCells('A6:O6');
      worksheet.getCell('A6').value = headerXLS.row5;
      worksheet.getCell('A6').alignment = { horizontal: 'right', vertical: 'middle' };
      worksheet.getCell('A6').font = { size: 11, italic: true, name: 'Times New Roman' };

      worksheet.mergeCells('A7:A10');
      worksheet.getCell('A7').value = "STT";

      worksheet.mergeCells('B7:B10');
      worksheet.getCell('B7').value = "Tên lĩnh vực/thủ tục";

      worksheet.mergeCells('C7:C10');
      worksheet.getCell('C7').value = "Mức độ thủ tục";

      worksheet.mergeCells('D7:I7');
      worksheet.getCell('D7').value = "Tiếp nhận hồ sơ TTHC";
      worksheet.getCell('D7').alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
      worksheet.getCell('D7').font = { size: 11, bold: true, name: 'Times New Roman' };
      worksheet.getCell('D7').border = { top: { style: 'thin' }, left: { style: 'thin' }, bottom: { style: 'thin' }, right: { style: 'thin' } };

      worksheet.mergeCells('J7:O7');
      worksheet.getCell('J7').value = "Trả kết quả giải quyết TTHC";
      worksheet.getCell('J7').alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
      worksheet.getCell('J7').font = { size: 11, bold: true, name: 'Times New Roman' };
      worksheet.getCell('J7').border = { top: { style: 'thin' }, left: { style: 'thin' }, bottom: { style: 'thin' }, right: { style: 'thin' } };

      // Tiếp nhận hồ sơ TTHC
      worksheet.mergeCells('D8:D10');
      worksheet.getCell('D8').value = "Tổng số hồ sơ tiếp nhận trong tháng";
      worksheet.mergeCells('E8:E10');
      worksheet.getCell('E8').value = "Trực tiếp";
      worksheet.mergeCells('F8:F10');
      worksheet.getCell('F8').value = "Qua BCCI";

      worksheet.mergeCells('G8:H8');
      worksheet.getCell('G8').value = "Trực tuyến";
      worksheet.mergeCells('G9:G10');
      worksheet.getCell('G9').value = "Một phần";
      worksheet.mergeCells('H9:H10');
      worksheet.getCell('H9').value = "Toàn trình";
      worksheet.mergeCells('I9:I10');
      worksheet.getCell('I9').value = "Cập nhật lên iGate";

      // Trả kết quả giải quyết TTHC
      worksheet.mergeCells('J8:J10');
      worksheet.getCell('J8').value = "Tổng số";
      worksheet.mergeCells('K8:K10');
      worksheet.getCell('K8').value = "Trả trực tiếp";
      worksheet.mergeCells('L8:L10');
      worksheet.getCell('L8').value = "Trả qua dịch vụ BCCI";
      worksheet.mergeCells('M8:M10');
      worksheet.getCell('M8').value = "Trả trực tuyến";
      worksheet.mergeCells('N8:N10');
      worksheet.getCell('N8').value = "Cập nhật lên iGate";
      worksheet.mergeCells('O8:O10');
      worksheet.getCell('O8').value = "Số hồ sơ TTHC sử dụng ký số trong giải quyết";

      const rowStartHeaderContent = 11;
      const NumberCol = 15;
      for (let index = 0; index < NumberCol; index++) {
        worksheet.getCell(10, (index + 1)).alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
        worksheet.getCell(10, (index + 1)).font = { size: 11, bold: true, name: 'Times New Roman' };
        worksheet.getCell(10, (index + 1)).border = { top: { style: 'thin' }, left: { style: 'thin' }, bottom: { style: 'thin' }, right: { style: 'thin' } };

        worksheet.getCell(9, (index + 1)).alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
        worksheet.getCell(9, (index + 1)).font = { size: 11, bold: true, name: 'Times New Roman' };
        worksheet.getCell(9, (index + 1)).border = { top: { style: 'thin' }, left: { style: 'thin' }, bottom: { style: 'thin' }, right: { style: 'thin' } };

        worksheet.getCell(8, (index + 1)).alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
        worksheet.getCell(8, (index + 1)).font = { size: 11, bold: true, name: 'Times New Roman' };
        worksheet.getCell(8, (index + 1)).border = { top: { style: 'thin' }, left: { style: 'thin' }, bottom: { style: 'thin' }, right: { style: 'thin' } };

        worksheet.getCell(rowStartHeaderContent, (index + 1)).value = "(" + (index + 1).toString() + ")";
        worksheet.getCell(rowStartHeaderContent, (index + 1)).alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
        worksheet.getCell(rowStartHeaderContent, (index + 1)).font = { size: 11, bold: true, name: 'Times New Roman' };
        worksheet.getCell(rowStartHeaderContent, (index + 1)).border = { top: { style: 'thin' }, left: { style: 'thin' }, bottom: { style: 'thin' }, right: { style: 'thin' } };
      }


      let rowStartContent = rowStartHeaderContent + 1;

      // cấp huyện
      const data = this.ListMain;
      for (let i = 0; i < data.length; i++) {
        var item = data[i];
        worksheet.getCell(rowStartContent, 1).value = i + 1;
        worksheet.mergeCells(rowStartContent, 2, rowStartContent, 3);
        worksheet.getCell(rowStartContent, 2).value = item.sector;
        worksheet.getCell(rowStartContent, 4).value = item.tiepNhanTrongKy;
        worksheet.getCell(rowStartContent, 5).value = item.tiepNhanTrucTiep;
        worksheet.getCell(rowStartContent, 6).value = item.hoSoBuuChinh;
        worksheet.getCell(rowStartContent, 7).value = item.tiepNhanMotPhan;
        worksheet.getCell(rowStartContent, 8).value = item.tiepNhanTrongKy;
        worksheet.getCell(rowStartContent, 9).value = item.tiepNhanToantrinh;
        worksheet.getCell(rowStartContent, 10).value = item.tongdaXuLy;
        worksheet.getCell(rowStartContent, 11).value = item.dxltiepNhanTrucTiep;
        worksheet.getCell(rowStartContent, 12).value = item.dxlhoSoBuuChinh;
        worksheet.getCell(rowStartContent, 13).value = item.dxltiepNhanTrucTuyen;
        worksheet.getCell(rowStartContent, 14).value = item.tongdaXuLy;
        worksheet.getCell(rowStartContent, 15).value = '-';

        for (let c = 0; c < NumberCol; c++) {
          worksheet.getCell(rowStartContent, (c + 1)).alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
          worksheet.getCell(rowStartContent, 2).alignment = { horizontal: 'left', vertical: 'middle', wrapText: true };
          worksheet.getCell(rowStartContent, (c + 1)).font = { size: 11, name: 'Times New Roman', bold: true };
          worksheet.getCell(rowStartContent, (c + 1)).border = { top: { style: 'thin' }, left: { style: 'thin' }, bottom: { style: 'thin' }, right: { style: 'thin' } };
        }

        // lấy theo thủ tục của lvuc
        for (let r = 0; r < item.data.length; r++) {
          rowStartContent = rowStartContent + 1;
          worksheet.getCell(rowStartContent, 1).value = this.colToLetter(r);
          worksheet.getCell(rowStartContent, 2).value = item.data[r].procedureName;
          worksheet.getCell(rowStartContent, 3).value = item.data[r].MucDo;
          worksheet.getCell(rowStartContent, 4).value = item.data[r].tiepNhanTrongKy;
          worksheet.getCell(rowStartContent, 5).value = item.data[r].tiepNhanTrucTiep;
          worksheet.getCell(rowStartContent, 6).value = item.data[r].hoSoBuuChinh;
          worksheet.getCell(rowStartContent, 7).value = item.data[r].tiepNhanMotPhan;
          worksheet.getCell(rowStartContent, 8).value = item.data[r].tiepNhanTrongKy;
          worksheet.getCell(rowStartContent, 9).value = item.data[r].tiepNhanToantrinh;
          worksheet.getCell(rowStartContent, 10).value = item.data[r].tongdaXuLy;
          worksheet.getCell(rowStartContent, 11).value = item.data[r].dxltiepNhanTrucTiep;
          worksheet.getCell(rowStartContent, 12).value = item.data[r].dxlhoSoBuuChinh;
          worksheet.getCell(rowStartContent, 13).value = item.data[r].dxltiepNhanTrucTuyen;
          worksheet.getCell(rowStartContent, 14).value = item.data[r].tongdaXuLy;
          worksheet.getCell(rowStartContent, 15).value = '-';

          for (let c = 0; c < NumberCol; c++) {
            worksheet.getCell(rowStartContent, (c + 1)).alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
            worksheet.getCell(rowStartContent, (c + 1)).font = { size: 11, name: 'Times New Roman' };
            worksheet.getCell(rowStartContent, (c + 1)).border = { top: { style: 'thin' }, left: { style: 'thin' }, bottom: { style: 'thin' }, right: { style: 'thin' } };
          }

          worksheet.getCell(rowStartContent, 2).alignment = { horizontal: 'left', vertical: 'middle', wrapText: true };
        }

        rowStartContent = rowStartContent + 1;
      }

      //TỔNG CỘNG
      worksheet.mergeCells(rowStartContent, 1, rowStartContent, 3);
      worksheet.getCell(rowStartContent, 1).value = "TỔNG CỘNG";
      worksheet.getCell(rowStartContent, 1).alignment = { horizontal: 'center', vertical: 'middle', };
      worksheet.getCell(rowStartContent, 1).font = { size: 11, bold: true, name: 'Times New Roman' };
      worksheet.getCell(rowStartContent, 1).border = { top: { style: 'thin' }, left: { style: 'thin' }, bottom: { style: 'thin' }, right: { style: 'thin' } };
      worksheet.getCell(rowStartContent, 4).value = this.TongCapTinh.tiepNhanTrongKy;
      worksheet.getCell(rowStartContent, 5).value = this.TongCapTinh.tiepNhanTrucTiep;
      worksheet.getCell(rowStartContent, 6).value = this.TongCapTinh.hoSoBuuChinh;
      worksheet.getCell(rowStartContent, 7).value = this.TongCapTinh.tiepNhanMotPhan;
      worksheet.getCell(rowStartContent, 8).value = this.TongCapTinh.tiepNhanToantrinh;
      worksheet.getCell(rowStartContent, 9).value = this.TongCapTinh.tiepNhanTrongKy;
      worksheet.getCell(rowStartContent, 10).value = this.TongCapTinh.tongdaXuLy;
      worksheet.getCell(rowStartContent, 11).value = this.TongCapTinh.dxltiepNhanTrucTiep;
      worksheet.getCell(rowStartContent, 12).value = this.TongCapTinh.dxlhoSoBuuChinh;
      worksheet.getCell(rowStartContent, 13).value = this.TongCapTinh.dxltiepNhanTrucTuyen;
      worksheet.getCell(rowStartContent, 14).value = this.TongCapTinh.tongdaXuLy;

      for (let index = 3; index < NumberCol; index++) {
        worksheet.getCell(rowStartContent, (index + 1)).alignment = { horizontal: 'center', vertical: 'middle', };
        worksheet.getCell(rowStartContent, (index + 1)).font = { size: 11, bold: true, name: 'Times New Roman' };
        worksheet.getCell(rowStartContent, (index + 1)).border = { top: { style: 'thin' }, left: { style: 'thin' }, bottom: { style: 'thin' }, right: { style: 'thin' } };
      }

      worksheet.getColumn(1).width = 7;
      worksheet.getColumn(1).alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
      worksheet.getColumn(2).width = 40;
      worksheet.getColumn(3).width = 20;
      worksheet.getCell(4, 1).alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
      worksheet.getCell(6, 1).alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
      worksheet.getColumn(15).width = 20;
      worksheet.getCell('A1').alignment = { horizontal: 'center', vertical: 'middle' };
      worksheet.getCell('A2').alignment = { horizontal: 'center', vertical: 'middle' };
      worksheet.getCell('A3').alignment = { horizontal: 'center', vertical: 'middle' };
      // Save Excel File
      workbook.xlsx.writeBuffer().then((dataBuffer: ArrayBuffer) => {
        const blob = new Blob([dataBuffer], { type: this.EXCEL_TYPE });
        fs.saveAs(blob, excelFileName + this.EXCEL_EXTENSION);
      });
    }
  }

  // Cập nhật validate date trong form tìm kiếm
  validateForm() {
    // Chuyển đổi lại thời gian startDate / endDate
    this.startDate.setHours(0, 0, 0, 0);
    this.endDate.setHours(23, 59, 59, 999);

    let startTime = this.startDate.getTime();
    let endTime = this.endDate.getTime();
    let language = localStorage.getItem('language');

    let data = {
      errMessage: "",
      status: false
    }

    if (this.startDate == null || this.endDate == null) {
      data.errMessage = language == 'vi' ? 'Vui lòng nhập đầy đủ thông tin ngày tra' : 'Please enter complete information for the lookup';
    } else if (startTime > endTime) {
      data.errMessage = language == 'vi' ? 'Vui lòng nhập thông tin từ ngày nhỏ hơn đến ngày' : 'Please enter information from date less than to date';
    } else {
      data.status = true;
    }

    if (data == null || !data?.status) {
      this.snackbarService.openSnackBar(0, "Không hợp lệ", data.errMessage, 'error_notification', this.config.expiredTime);
      this.listHoSo = [];
      this.countResult = 0;
      return false;
    }

    return true;
  }
}

