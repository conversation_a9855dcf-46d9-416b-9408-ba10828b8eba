import { Injectable } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { ApiProviderService } from 'src/app/core/service/api-provider.service';
import { Observable } from 'rxjs';
import { Workbook } from 'exceljs';
import * as fs from 'file-saver';

const EXCEL_TYPE = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8';
const EXCEL_EXTENSION = '.xlsx';

@Injectable({
  providedIn: 'root'
})
export class IdentificationService {

  constructor(
    private dialog: MatDialog,
    private http: HttpClient,
    private apiProviderService: ApiProviderService
  ) { }

  private getProcedureURL = this.apiProviderService.getUrl('digo', 'basepad');
  private getAgencyURL = this.apiProviderService.getUrl('digo', 'basedata');
  private getSector = this.apiProviderService.getUrl('digo', 'basepad');
  private getProcost = this.apiProviderService.getUrl('digo', 'basepad');
  private getAdapter = this.apiProviderService.getUrl('digo', 'adapter');

  getListIdentification(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    // return this.http.get('http://localhost:8081/identifier/--search' + searchString, {headers});
    return this.http.get(this.getAgencyURL + '/identifier/--search' + searchString, {headers});
  }

  public exportToExcelListIdentification(
    reportHeading: string,
    json: any[],
    excelFileName: string,
    sheetName: string)
  {
    const data = json;
    // create workbook and worksheet
    const workbook = new Workbook();
    workbook.creator = 'Snippet Coder';
    workbook.lastModifiedBy = 'SnippetCoder';
    workbook.created = new Date();
    workbook.modified = new Date();
    const worksheet = workbook.addWorksheet(sheetName);

    worksheet.getColumn('A').font = {name: 'Times New Roman', size: 12};
    worksheet.getColumn('B').font = {name: 'Times New Roman', size: 12};
    worksheet.getColumn('C').font = {name: 'Times New Roman', size: 12};
    worksheet.getColumn('D').font = {name: 'Times New Roman', size: 12};
    worksheet.getColumn('E').font = {name: 'Times New Roman', size: 12};
    worksheet.getColumn('F').font = {name: 'Times New Roman', size: 12};
    worksheet.getColumn('G').font = {name: 'Times New Roman', size: 12};
    worksheet.getColumn('H').font = {name: 'Times New Roman', size: 12};
    worksheet.getColumn('I').font = {name: 'Times New Roman', size: 12};
    worksheet.getColumn('J').font = {name: 'Times New Roman', size: 12};

    // Add header row
    worksheet.addRow([]);
    worksheet.mergeCells('A2:J2');
    worksheet.getCell('A2').value = reportHeading;
    worksheet.getCell('A2').alignment = {horizontal: 'center', vertical: 'middle'};
    worksheet.getCell('A2').font = {size: 16, bold: true, name: 'Times New Roman'};

    // NỘI DUNG TABLE-HEADER
    worksheet.mergeCells('A7:A8');
    worksheet.mergeCells('B7:B8');
    worksheet.mergeCells('C7:C8');
    worksheet.mergeCells('D7:D8');
    worksheet.mergeCells('E7:E8');
    worksheet.mergeCells('F7:F8');
    worksheet.mergeCells('G7:G8');
    worksheet.mergeCells('H7:H8');
    worksheet.mergeCells('I7:I8');
    worksheet.mergeCells('J7:J8');

    worksheet.getCell('A7').value = 'STT';
    worksheet.getCell('B7').value = 'Mã';
    worksheet.getCell('C7').value = 'Tên cơ quan, đơn vị';
    worksheet.getCell('D7').value = 'Địa chỉ';
    worksheet.getCell('E7').value = 'Điện thoại';
    worksheet.getCell('F7').value = 'Email';
    worksheet.getCell('G7').value = 'Website';
    worksheet.getCell('H7').value = 'Văn bản ban hành / sửa đổi';
    worksheet.getCell('I7').value = 'Ngày ban hành VB';
    worksheet.getCell('J7').value = 'Cơ quan ban hành VB';

    worksheet.getColumn('B').width = 20;
    worksheet.getColumn('C').width = 50;
    worksheet.getColumn('D').width = 20;
    worksheet.getColumn('E').width = 20;
    worksheet.getColumn('F').width = 20;
    worksheet.getColumn('G').width = 20;
    worksheet.getColumn('H').width = 30;
    worksheet.getColumn('I').width = 20;
    worksheet.getColumn('J').width = 50;

    worksheet.properties.outlineLevelCol = 2;
    worksheet.properties.defaultRowHeight = 15;

    let i=7;
    const j = 8;
    for (i; i <= j; i++) {
      let k = 1;
      const l = 10;
      for (k; k <= l; k++) {
        worksheet.findCell(i, k).alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
        worksheet.findCell(i, k).fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: {argb: 'C0C0C0C0'},
          bgColor: {argb: 'FF0000FF'}
        };
        worksheet.findCell(i, k).border = {top: {style: 'thin'}, left: {style: 'thin'}, bottom: {style: 'thin'}, right: {style: 'thin'}};
        worksheet.findCell(i, k).font = {size: 12, bold: true, name: 'Times New Roman'};
      }
    }

    // get all columns from JSON
    let columnsArray: any[];
    for (const key in json) {
      if (json.hasOwnProperty(key)) {
        columnsArray = Object.keys(json[key]);
      }
    }

    // Add Data and Conditional Formatting
    data.forEach((element: any) => {
      const eachRow = [];
      columnsArray.forEach((column) => {
        eachRow.push(element[column]);
      });
      const borderrow = worksheet.addRow(eachRow);
      borderrow.eachCell((cell) => {
        cell.border = {top: {style: 'thin'}, left: {style: 'thin'}, bottom: {style: 'thin'}, right: {style: 'thin'}};
        cell.alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
      });
    });

    const dataLength = data.length;
    if (dataLength > 0) {
      for (i = 0; i < dataLength; i++) {
        worksheet.getCell('B' + (9 + i)).alignment = {horizontal: 'left', vertical: 'middle', wrapText: true};
        worksheet.getCell('C' + (9 + i)).alignment = {horizontal: 'left', vertical: 'middle', wrapText: true};
        worksheet.getCell('D' + (9 + i)).alignment = {horizontal: 'left', vertical: 'middle', wrapText: true};
        worksheet.getCell('E' + (9 + i)).alignment = {horizontal: 'left', vertical: 'middle', wrapText: true};
        worksheet.getCell('F' + (9 + i)).alignment = {horizontal: 'left', vertical: 'middle', wrapText: true};
        worksheet.getCell('G' + (9 + i)).alignment = {horizontal: 'left', vertical: 'middle', wrapText: true};
        worksheet.getCell('H' + (9 + i)).alignment = {horizontal: 'left', vertical: 'middle', wrapText: true};
        worksheet.getCell('J' + (9 + i)).alignment = {horizontal: 'left', vertical: 'middle', wrapText: true};
      }
    }

      // Save Excel File
    workbook.xlsx.writeBuffer().then((dataBuffer: ArrayBuffer) => {
      const blob = new Blob([dataBuffer], {type: EXCEL_TYPE});
      fs.saveAs(blob, excelFileName + EXCEL_EXTENSION);
    });

  }

  getExportListIden(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.getAgencyURL + '/identifier/--exportData' + searchString, {headers});
  }
}
