import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { ApiProviderService } from 'src/app/core/service/api-provider.service';
import { EnvService } from 'src/app/core/service/env.service';

@Injectable({
    providedIn: 'root'
})
export class AgencyDocumentManagementService {
    constructor(
        private http: HttpClient,
        private envService: EnvService,
        private apiProviderService: ApiProviderService,
    ) { }

    private agencyManagementPath = this.apiProviderService.getUrl('digo', 'basepad') + '/agency-document/';

    getListAgency(searchString): Observable<any> {
        let headers = new HttpHeaders();
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        return this.http.get(this.agencyManagementPath + searchString, { headers });
    }

    deleteAgency(id): Observable<any> {
        let headers = new HttpHeaders();
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        return this.http.delete(this.agencyManagementPath + `${id}`, { headers });
    }

    addOrUpdateAgency(id, requestBody): Observable<any> {
        let headers = new HttpHeaders();
        headers = headers.set('Content-Type', 'application/json');
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        if(id == '0'){
            return this.http.post<any>(this.agencyManagementPath, requestBody, { headers });
        }
        return this.http.put<any>(this.agencyManagementPath + `${id}`, requestBody, { headers });
    }

    getAgencyById(id): Observable<any> {
        let headers = new HttpHeaders();
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        return this.http.get(this.agencyManagementPath + `${id}`, { headers });
    }

    getListAgencyIsShow(): Observable<any> {
        let headers = new HttpHeaders();
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        return this.http.get(this.agencyManagementPath + 'get-all-is-show', { headers });
    }
}
