import { Injectable } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { ApiProviderService } from 'src/app/core/service/api-provider.service';
import { Observable } from 'rxjs';
import {SnackbarService} from 'data/service/snackbar/snackbar.service';
import {ConfigService} from "data/service/dossier/config.service";
import {EnvService} from "core/service/env.service";

@Injectable({
  providedIn: 'root'
})
export class StatisticalService {
  constructor(
    private dialog: MatDialog,
    private http: HttpClient,
    private apiProviderService: ApiProviderService,
    private snackbar: SnackbarService,
    private envService: EnvService
  ) { }

  config = this.envService.getConfig();
  private getReporterURL = this.apiProviderService.getUrl('digo', 'reporter');
  private getAgencyURL = this.apiProviderService.getUrl('digo', 'basedata');
  private basepadURL = this.apiProviderService.getUrl('digo', 'basepad');
  private reporterURL = this.apiProviderService.getUrl('digo', 'reporter');
  private padmanURL = this.apiProviderService.getUrl('digo', 'padman');
  private humanURL = this.apiProviderService.getUrl('digo', 'human');

  public excelExportURL =
    this.apiProviderService.getUrl('digo', 'reporter') +
    '/dossier/--export';

  getReportForDossier6a(searchString): Observable<any> {
    const token = localStorage.getItem('OAuth2TOKEN');
    let headers = new HttpHeaders();
    headers = headers.append('Authorization', 'OAuth 2.0' + token);
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.get(this.getReporterURL + '/dossier/--by-6a' + searchString, { headers }).pipe();
    // return this.http.get('http://localhost:8080' + '/dossier/--by-6a' + searchString, { headers }).pipe();
  }

  getReportForDossierProvince6a(searchString): Observable<any> {
    const token = localStorage.getItem('OAuth2TOKEN');
    let headers = new HttpHeaders();
    headers = headers.append('Authorization', 'OAuth 2.0' + token);
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.get(this.getReporterURL + '/dossier/--by-province-6a' + searchString, { headers }).pipe();
  }

  getReportForDossierProvince6b(searchString): Observable<any> {
    const token = localStorage.getItem('OAuth2TOKEN');
    let headers = new HttpHeaders();
    headers = headers.append('Authorization', 'OAuth 2.0' + token);
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.get(this.getReporterURL + '/dossier/--by-province-6b' + searchString, { headers }).pipe();
  }

  getReportForDossierProvince6c(searchString): Observable<any> {
    const token = localStorage.getItem('OAuth2TOKEN');
    let headers = new HttpHeaders();
    headers = headers.append('Authorization', 'OAuth 2.0' + token);
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.get(this.getReporterURL + '/dossier/--by-province-6c' + searchString, { headers }).pipe();
  }

  getReportForDossier6b(searchString): Observable<any> {
    const token = localStorage.getItem('OAuth2TOKEN');
    let headers = new HttpHeaders();
    headers = headers.append('Authorization', 'OAuth 2.0' + token);
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.get(this.getReporterURL + '/dossier/--by-6b' + searchString, { headers }).pipe();
    // return this.http.get('http://localhost:8080' + '/dossier/--by-6b' + searchString, { headers }).pipe();
  }

  getReportForDossier6d(searchString): Observable<any> {
    const token = localStorage.getItem('OAuth2TOKEN');
    let headers = new HttpHeaders();
    headers = headers.append('Authorization', 'OAuth 2.0' + token);
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.get(this.getReporterURL + '/dossier/--by-6d' + searchString, { headers }).pipe();
    // return this.http.get('http://localhost:8080' + '/dossier/--by-6d' + searchString, { headers }).pipe();
  }

  getProcedureReport(searchString): Observable<any> {
    const token = localStorage.getItem('OAuth2TOKEN');
    let headers = new HttpHeaders();
    headers = headers.append('Authorization', 'OAuth 2.0 ' + token);
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.get(this.getReporterURL + '/dossier/--by-procedure?' + searchString, { headers }).pipe();
    // return this.http.get('http://localhost:8080' + '/dossier/--by-procedure' + searchString, { headers }).pipe();
  }

  getSectorReport(searchString): Observable<any> {
    const token = localStorage.getItem('OAuth2TOKEN');
    let headers = new HttpHeaders();
    headers = headers.append('Authorization', 'OAuth 2.0 ' + token);
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.get(this.getReporterURL + '/dossier/--by--unresolved-overdue?' + searchString, { headers }).pipe();
    // return this.http.get('http://localhost:8080' + '/dossier/--by--unresolved-overdue' + searchString, { headers }).pipe();
  }

  getListAgency(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    let kq = '';
    if (id !== null && id !== ''){
      kq = '&parent-id=' + id;
    }
    return this.http.get(this.getAgencyURL + '/agency/' + '?page=0&size=50' + kq, { headers }).pipe();
  }

  downloadExport(params: string){
    return this.http.get(this.excelExportURL  + params, { observe: 'response', responseType: 'blob' }).toPromise();
  }

  // Xuất file excel
  excelExport(params: string): any {
    return new Promise((resolve) => {
      this.downloadExport(params).then(res => {
        const blob = new Blob([res.body], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
        let filename = res.headers.get('content-disposition').split(';')[1].split('=')[1].replace(/\"/g, '');
        var blobUrl = URL.createObjectURL(blob);

        var xhr = new XMLHttpRequest;
        xhr.responseType = 'blob';

        xhr.onload = function() {
          var recoveredBlob = xhr.response;
          var reader = new FileReader;
          reader.onload = function() {
            var base64data = reader.result.toString();
            var object = JSON.stringify({
              mimeType:filename.substring(filename.lastIndexOf(".")+1),
              name:filename.substring(0,filename.lastIndexOf(".")),
              data:base64data.split(',')[1]
            });
            let anchor = document.createElement("a");
            anchor.download = filename;
            anchor.href = base64data;
            anchor.click();
          };
          reader.readAsDataURL(recoveredBlob);
        };

        xhr.open('GET', blobUrl);
        xhr.send();
        resolve(true);
      })
        .catch(err => {
          if (err.status === 500) {
            this.snackbar.openSnackBar(0, '', 'Hệ thống tạm thời không thể xuất file excel!', 'error_notification', this.config.expiredTime);
          }
          resolve(false)
        });
    });
  }

  getListSectorByAgencyId(search): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.get(this.basepadURL + '/procedure/sector-by-agency' + search, { headers }).pipe();
    // return this.http.get('http://localhost:8080' + '/procedure/sector-by-agency' + search, { headers }).pipe();
  }

  getListDossierForReport6b(search): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.get(this.reporterURL + '/dossier/statistic/--by-ancestor' + search, { headers }).pipe();
    // return this.http.get('http://localhost:8081' + '/dossier/statistic/--by-ancestor' + search, { headers }).pipe();
  }

  getDetailAgency(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.getAgencyURL + '/agency/' + id, { headers }).pipe();
  }

  getListAgencyByParentId(search): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.getAgencyURL + '/agency/--by-parent-id?parent-id=' + search, { headers }).pipe();
  }

  getInactiveAccountCSDLDC(isCSDLDC: boolean, param: string): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    let url = this.humanURL + (isCSDLDC ? '/user/--csdldc-check' : '/user/--inactive-user');
    if (param) {
      url += '?agency-id=' + param;
    }
    return this.http.get(url, { headers }).pipe();
  }

}
