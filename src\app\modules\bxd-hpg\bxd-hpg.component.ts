import { DatePipe } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { FormControl, FormGroup } from '@angular/forms';
import { MatTableDataSource } from '@angular/material/table';
import { EnvService } from 'src/app/core/service/env.service';
import { AdapterService } from 'src/app/data/service/adapter/adapter.service';
import { pageSizeOptions } from 'src/app/data/service/config.service';
import { MainService } from 'src/app/data/service/main/main.service';
import { SnackbarService } from 'src/app/data/service/snackbar/snackbar.service';

@Component({
  selector: 'app-bxd-hpg',
  templateUrl: './bxd-hpg.component.html',
  styleUrls: ['./bxd-hpg.component.scss']
})
export class BxdHpgComponent implements OnInit {
  searchForm = new FormGroup({
    fromDate:  new FormControl(new Date(new Date().getTime() - 30 * 86400000)),
    toDate: new FormControl(new Date()),
  });
  currentDate = new Date();
  maxToDate = new Date();
  maxFromDate = new Date();
  config = this.envService.getConfig();
  size = 5;
  page = 1;
  countResult = 0;
  pgSizeOptions = this.config.pageSizeOptions;
  paginationType= "page"
  numberOfElements = 0;
  dataSource: MatTableDataSource<any>;
  ELEMENT_DATA : any = [];
  pageSizes = pageSizeOptions;
  displayedColumns: string[] = ['STT','MaHoSo','HoTenNguoiNop','TrangThaiHoSo', 'NgayTiepNhan','NgayHenTraKetQua','HinhThucTraKetQua'];
  number: number = 1;
  pageTitle = {
    vi: `Tra cứu hồ sơ cấp phép xây dựng`,
    en: `Look up construction permit documents`
  };
  selectedLang = localStorage.getItem('language') || 'vi';
  constructor(
    private envService : EnvService,
    private snackbarService: SnackbarService,
    private apdaterService: AdapterService,
    private datePipe: DatePipe,
    private mainService: MainService,
  ) { }

  ngOnInit(): void {
    this.mainService.setPageTitle(this.pageTitle[this.selectedLang]);
    this.getListDossier();

  }

  changePageOrSize(e: any) {
      if(e.currentPage) {
        this.page = e.currentPage
      }
      if(e.itemsPerPage) {
        this.size = e.itemsPerPage;
      }
    this.getListDossier();
  }
  setTrangThaiHoSo(trangThai: any) {
    switch(trangThai) {
      case '1':
        return "Mới đăng ký";
      case '2':
        return "Được tiếp nhận";
      case '3':
        return "Không được tiếp nhận";
      case '4':
        return "Đang xử lý";
      case '5':
        return "Yêu cầu bổ sung giấy tờ";
      case '6':
        return "Yêu cầu thực hiện nghĩa vụ tài chính";
      case '7':
        return "Công dân yêu cầu rút hồ sơ";
      case '8':
        return "Dừng xử lý";
      case '9':
        return "Đã xử lý xong";
      case '10':
        return "Đã trả kết quả";
      default:
        return "Được tiếp nhận"
        
    }
  }
  setHinhThucTraKetQua(trangThai: any) {
    switch(trangThai) {
      case '0':
        return "Trả kết quả tại bộ phận tiếp nhận và trả kết quả";
      case '1':
        return "Trả kết quả qua đường bưu điện.";
      case '2':
        return "Trả kết quả trực tuyến";
    }
  }
  getListDossier() {
    const params = {
      "toDate": this.datePipe.transform(this.searchForm.get("toDate").value, 'dd/MM/yyyy'),
      "fromDate": this.datePipe.transform(this.searchForm.get("fromDate").value, 'dd/MM/yyyy'),
      "pageSize": this.size,
      "pageIndex": this.page
    }
    this.apdaterService.searchDossierBXD(params).subscribe(res => {
      this.ELEMENT_DATA = res.data;
      this.dataSource = this.ELEMENT_DATA;
      if(res.total == null) {
        this.countResult = 0
      }else {
        this.countResult = res.total;
      }
    })
  }
  submit() {
    if(!this.searchForm.valid) {
      this.snackbarService.openSnackBar(0, "Vui lòng nhập đủ thông tin!", '', 'error_notification', this.config.expiredTime);
      return;
    }
    if(this.searchForm.value.fromDate > this.searchForm.value.toDate){
      this.snackbarService.openSnackBar(0, " Thời gian từ ngày phải nhỏ hơn hoặc bằng thời gian đến ngày!", '', 'error_notification', this.config.expiredTime);
      return;
    }
   this.getListDossier();

  }

}

