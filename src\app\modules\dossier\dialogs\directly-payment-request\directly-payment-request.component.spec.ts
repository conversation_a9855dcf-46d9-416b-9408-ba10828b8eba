import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { DirectlyPaymentRequestComponent } from './directly-payment-request.component';

describe('DirectlyPaymentRequestComponent', () => {
  let component: DirectlyPaymentRequestComponent;
  let fixture: ComponentFixture<DirectlyPaymentRequestComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ DirectlyPaymentRequestComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(DirectlyPaymentRequestComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
