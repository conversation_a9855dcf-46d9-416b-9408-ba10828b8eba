import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { SearchByAgencyOneLevelComponent } from './search-by-agency-one-level.component';
import { DetailSearchComponent } from '../search-by-agency/pages/detail-search/detail-search.component';


const routes: Routes = [
  {
    path: '',
    children: [
      { path: '', component: SearchByAgencyOneLevelComponent },
      { path: ':id', component: DetailSearchComponent },
    ]
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class SearchByAgencyOneLevelRoutingModule { }
