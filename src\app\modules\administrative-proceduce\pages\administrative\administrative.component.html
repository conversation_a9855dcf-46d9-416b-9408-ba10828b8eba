<h2 i18n><PERSON><PERSON> sách bộ số thủ tục hành chính</h2>
<div class="procedure-list-page">
    <form [formGroup]="searchForm" (submit)="onConfirmSearch()" class="form-search">
        <div fxLayout="row" fxLayout.xs="row" fxLayout.sm="row" fxLayoutAlign="space-between" class="flex-wrap-wrap">
            <mat-form-field appearance="outline" fxFlex.gt-sm="68" fxFlex.gt-xs="49.5" fxFlex='grow'>
                <mat-label i18n>Nhậ<PERSON> từ khoá</mat-label>
                <input matInput formControlName="keyword" maxlength="500">
            </mat-form-field>
            <button mat-flat-button fxFlex.gt-sm="30" fxFlex.gt-xs="49.5" fxFlex='grow' class="btn-search" type="submit">
                <mat-icon>search</mat-icon>
                <span i18n>T<PERSON><PERSON> kiếm</span>
            </button>
        </div>
    </form>

    <div fxLayout="row" fxLayoutAlign="center" class="pdb-1">
        <div fxFlex="grow">
            <button mat-flat-button class="btn_add" style="float:right;background-color: #e8e8e8;
            color: #666;" (click)="addInforDialog()">
                <mat-icon>add</mat-icon>
                <span i18n>Thêm mới</span>
            </button>
        </div>
    </div>

    <div fxLayout="row" fxLayoutAlign="center">
        <div fxFlex="grow">
            <div class="tbl mat-elevation-z2">
                <table mat-table [dataSource]="dataSource">
                    <ng-container matColumnDef="stt">
                        <mat-header-cell *matHeaderCellDef i18n>STT</mat-header-cell>
                        <mat-cell *matCellDef="let row" i18n-data-label data-label="STT"> {{row.stt}} </mat-cell>
                    </ng-container>
    
                    <ng-container matColumnDef="code">
                        <mat-header-cell *matHeaderCellDef i18n>Mã sổ</mat-header-cell>
                        <mat-cell *matCellDef="let row" i18n-data-label data-label="Mã thủ tục">
                            <a> {{row.publicAmin[0].code}} </a>
                        </mat-cell>
                    </ng-container>

                    <ng-container matColumnDef="name">
                        <mat-header-cell *matHeaderCellDef i18n>Tên bộ số</mat-header-cell>
                        <mat-cell *matCellDef="let row" i18n-data-label  #tooltip="matTooltip" matTooltip="{{row.name}}">
                            <span class="more-text">
                                {{row.name}}
                                <span *ngIf="row.name == undefined || row.name == null || row.name.trim() == ''" i18n>(Không tìm thấy bản dịch)</span>
                            </span>
                        </mat-cell>
                    </ng-container>
    
                    <ng-container matColumnDef="start value">
                        <mat-header-cell *matHeaderCellDef i18n>Giá trị bắt đầu</mat-header-cell>
                        <mat-cell *matCellDef="let row" i18n-data-label data-label="Tên thủ tục" #tooltip="matTooltip" matTooltip="{{row.name}}">
                            <span>
                                {{row.startValid}}
                            </span>
                        </mat-cell>
                    </ng-container>
    
                    <ng-container matColumnDef="end value">
                        <mat-header-cell *matHeaderCellDef i18n>Giá trị kết thúc</mat-header-cell>
                        <mat-cell *matCellDef="let row" i18n-data-label data-label="Cơ quan thực hiện">
                            {{row.endValid}}
                        </mat-cell>
                    </ng-container>

                    <ng-container matColumnDef="now value">
                        <mat-header-cell *matHeaderCellDef >Giá trị hiện tại</mat-header-cell>
                        <mat-cell *matCellDef="let row" data-label data-label="giá trị hiện tại">
                            {{row?.nowValid}}
                        </mat-cell>
                    </ng-container>

                    <ng-container matColumnDef="display syntax">
                        <mat-header-cell *matHeaderCellDef i18n>Cú pháp hiển thị</mat-header-cell>
                        <mat-cell *matCellDef="let row" i18n-data-label data-label="Cơ quan thực hiện">
                            {{row.displaySyntax}}
                        </mat-cell>
                    </ng-container>

                    <ng-container matColumnDef="status">
                        <mat-header-cell *matHeaderCellDef i18n>Trạng thái</mat-header-cell>
                        <mat-cell *matCellDef="let row" i18n-data-label >
                            {{row.status}}
                        </mat-cell>
                    </ng-container>
    
                    <ng-container matColumnDef="action">
                        <mat-header-cell *matHeaderCellDef i18n>Thao tác</mat-header-cell>
                        <mat-cell *matCellDef="let row" i18n-data-label data-label="Thao tác">
                            <button mat-icon-button [matMenuTriggerFor]="actionMenu">
                                <mat-icon>more_horiz</mat-icon>
                            </button>
                            <mat-menu #actionMenu="matMenu" xPosition="before" class="mat-menu-panel menu-action">
                                <button mat-menu-item class="menuAction" (click)="updateDialog(row.id)">
                                    <mat-icon>edit</mat-icon> <span i18n>Cập nhật</span>
                                </button>
                                <button mat-menu-item (click)="deleteDialog(row.id, row.publicAmin[0].code)">
                                    <mat-icon>delete_outline</mat-icon>
                                    <span i18n>Xóa</span>
                                </button>
                            </mat-menu>
                        </mat-cell>
                    </ng-container>
    
                    <mat-header-row *matHeaderRowDef="displayedColumns"></mat-header-row>
                    <mat-row *matRowDef="let row; columns: displayedColumns;"></mat-row>
                </table>
                <div class="pagination">
                    <ul class="temp-arr">
                        <li *ngFor="let item of ELEMENTDATA  | paginate: {itemsPerPage: size, currentPage: page, totalItems: countResult, id: 'pgnx'}">
                        </li>
                    </ul>
                    <div class="page-size">
                        <span i18n>Hiển thị </span>
                        <mat-form-field appearance="outline">
                            <mat-select [(value)]="size" (valueChange)="paginate(pageIndex, 1)">
                                <mat-option *ngFor='let opt of pgSizeOptions;' [value]="opt">{{opt}}</mat-option>
                            </mat-select>
                        </mat-form-field>
                        <span><span i18n>trên</span> {{countResult}} <span i18n>bản ghi</span></span>
                    </div>
                    <div class="control">
                        <pagination-controls id="pgnx" (pageChange)="page = $event; paginate(page, 0)" responsive="true" previousLabel="" nextLabel="">
                        </pagination-controls>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>