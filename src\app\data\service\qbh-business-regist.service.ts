import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable} from '@angular/core';
import { HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { ApiProviderService } from 'src/app/core/service/api-provider.service';
import { Workbook } from 'exceljs';
import * as fs from 'file-saver';
import { Utils } from 'src/app/shared/ts/utils';
import { DatePipe } from '@angular/common';
import * as moment from 'moment';
@Injectable({
  providedIn: 'root'
})
export class QbhBusinessRegistService {

  constructor(
    private http: HttpClient,
    private apiProviderService: ApiProviderService,
    private datePipe: DatePipe
  ) { }

  private url = this.apiProviderService.getUrl('digo', 'basedata') + '/business-registration';
  private basedata = this.apiProviderService.getUrl('digo', 'basedata');
  // checkIsAdmin() {
  //   return localStorage.getItem("checkIsAdmin") ?? false;
  // }
  
  create(body): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.set('Content-Type', 'application/json');
    return this.http.post(this.url,body,{headers});
  }

  update(id,body): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.set('Content-Type', 'application/json');
    return this.http.put(this.url+ `/${id}`,body,{headers});
  }
  get(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers.append('Access-Control-Allow-Origin', '*');

    if (localStorage.getItem('isLoggedIn') == 'false') {
      const token = localStorage.getItem('OAuth2TOKEN');
      headers = headers.append('Authorization', 'Bearer ' + token);
      headers.append('Access-Control-Allow-Origin', '*');

    }
    return this.http.get(this.basedata+"/business-registration/" + searchString, { headers }).pipe(); 
  }
  getLogHCT(id,searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers.append('Access-Control-Allow-Origin', '*');

    if (localStorage.getItem('isLoggedIn') == 'false') {
      const token = localStorage.getItem('OAuth2TOKEN');
      headers = headers.append('Authorization', 'Bearer ' + token);
      headers.append('Access-Control-Allow-Origin', '*');

    }
    return this.http.get(this.basedata+"/business-registration/--get-log-hct/" + id+searchString, { headers }).pipe(); 
  }
  updateTaxCode(searchString):Observable<any>{
    console.log("url:",this.basedata+searchString)
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers.append('Access-Control-Allow-Origin', '*');
    if (localStorage.getItem('isLoggedIn') == 'false') {
      const token = localStorage.getItem('OAuth2TOKEN');
      headers = headers.append('Authorization', 'Bearer ' + token);
      headers.append('Access-Control-Allow-Origin', '*');
    }
    return this.http.put<any>(this.basedata+"/business-registration"+searchString,null,{headers});
  }
  updateSuspend(searchString):Observable<any>{
    console.log("url:",this.basedata+searchString)
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers.append('Access-Control-Allow-Origin', '*');
    if (localStorage.getItem('isLoggedIn') == 'false') {
      const token = localStorage.getItem('OAuth2TOKEN');
      headers = headers.append('Authorization', 'Bearer ' + token);
      headers.append('Access-Control-Allow-Origin', '*');
    }
    return this.http.put<any>(this.basedata+"/business-registration"+searchString,null,{headers});
  }
  suspendRestored(id):Observable<any>{
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers.append('Access-Control-Allow-Origin', '*');
    if (localStorage.getItem('isLoggedIn') == 'false') {
      const token = localStorage.getItem('OAuth2TOKEN');
      headers = headers.append('Authorization', 'Bearer ' + token);
      headers.append('Access-Control-Allow-Origin', '*');
    }
    return this.http.put<any>(this.basedata+"/business-registration/"+id+"/cancleSuspend/",null,{headers});
  }
  permanentClosureRestored(id):Observable<any>{
    console.log("huy nghi han",this.basedata+"/business-registration"+id+"/canclePermanentClosure/")
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers.append('Access-Control-Allow-Origin', '*');
    if (localStorage.getItem('isLoggedIn') == 'false') {
      const token = localStorage.getItem('OAuth2TOKEN');
      headers = headers.append('Authorization', 'Bearer ' + token);
      headers.append('Access-Control-Allow-Origin', '*');
    }
    return this.http.put<any>(this.basedata+"/business-registration/"+id+"/canclePermanentClosure/",null,{headers});
  }
  permanentClosureInfo(searchString):Observable<any>{
    console.log("url:",this.basedata+searchString)
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers.append('Access-Control-Allow-Origin', '*');
    if (localStorage.getItem('isLoggedIn') == 'false') {
      const token = localStorage.getItem('OAuth2TOKEN');
      headers = headers.append('Authorization', 'Bearer ' + token);
      headers.append('Access-Control-Allow-Origin', '*');
    }
    return this.http.put<any>(this.basedata+"/business-registration"+searchString,null,{headers});
  }
  deleteBusiness(searchString):Observable<any>{
    console.log("url:",this.basedata+searchString)
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers.append('Access-Control-Allow-Origin', '*');
    if (localStorage.getItem('isLoggedIn') == 'false') {
      const token = localStorage.getItem('OAuth2TOKEN');
      headers = headers.append('Authorization', 'Bearer ' + token);
      headers.append('Access-Control-Allow-Origin', '*');
    }
    return this.http.delete<any>(this.basedata+"/business-registration/"+searchString+"/deleteBusiness/",{headers});
  }

  list(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.set('Content-Type', 'application/json');
    return this.http.get(this.url+ `/${searchString}`,{headers});
  }


  createHTX(body): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.set('Content-Type', 'application/json');
    return this.http.post(this.url + '/createHTX', body, {headers});
  }

  getListHTX(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.url + '/searchListHTX' + searchString, {headers});
  }

  getListWardsQBH(nationId, cityId): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    let param = '?nation-id=' + nationId;
    param += cityId ? '&city-id=' + cityId : '';
    return this.http.get(this.basedata + '/place/--get-list-wards-QBH' + param, { headers });
  }
  
  exportExcelBusinessHouseHold(
    status: string,
    provinceName: string,
    districtName: string,
    reportName: string,
    json: any[],
    sheetName: string
  ) {
    const data = json;
    // create workbook and worksheet
    const workbook = new Workbook();
    workbook.creator = 'Snippet Coder';
    workbook.lastModifiedBy = 'SnippetCoder';
    workbook.created = new Date();
    workbook.modified = new Date();
    const worksheet = workbook.addWorksheet(sheetName);
    const fontHeader = {name: 'Times New Roman'};
    worksheet.getColumn('A').font = fontHeader;
    worksheet.getColumn('B').font = fontHeader;
    worksheet.getColumn('C').font = fontHeader;
    worksheet.getColumn('D').font = fontHeader;
    worksheet.getColumn('E').font = fontHeader;
    worksheet.getColumn('F').font = fontHeader;
    worksheet.getColumn('G').font = fontHeader;
    worksheet.getColumn('H').font = fontHeader;
    worksheet.getColumn('I').font = fontHeader;
    worksheet.getColumn('J').font = fontHeader;
    worksheet.getColumn('K').font = fontHeader;
    worksheet.getColumn('L').font = fontHeader;
    worksheet.getColumn('M').font = fontHeader;
    worksheet.getColumn('N').font = fontHeader;
    worksheet.getColumn('O').font = fontHeader;
    worksheet.getColumn('P').font = fontHeader;
    worksheet.getColumn('Q').font = fontHeader;
    worksheet.getColumn('R').font = fontHeader;
    worksheet.getColumn('S').font = fontHeader;
    worksheet.getColumn('T').font = fontHeader;
    worksheet.getColumn('U').font = fontHeader;
    


    // Add header row
    const today = new Date();
    worksheet.addRow([]);
    worksheet.mergeCells('A1:C1');
    worksheet.mergeCells('A2:C2');
    worksheet.mergeCells('A3:C3');
    worksheet.getCell('A1').value = "Tỉnh " + provinceName;
    worksheet.getCell('A2').value = `Ngày ${today.getDate()} tháng ${today.getMonth() + 1} năm ${today.getFullYear()}`;
    worksheet.getCell('A3').value = districtName;
    worksheet.getCell('A1').alignment = {horizontal: 'center', vertical: 'middle'};
    worksheet.getCell('A2').alignment = {horizontal: 'center', vertical: 'middle'};
    worksheet.getCell('A3').alignment = {horizontal: 'center', vertical: 'middle'};
    
    worksheet.getCell('A1').font = { ...worksheet.getCell('A1').font,...{bold: true, size: 14}}
    worksheet.getCell('A2').font = { ...worksheet.getCell('A1').font,...{bold: true, size: 14}}
    worksheet.getCell('A3').font = { ...worksheet.getCell('A1').font,...{bold: true, size: 14}}

    // add report name
    worksheet.mergeCells('D3:J3');
    worksheet.mergeCells('D4:J4');
    worksheet.getCell('D3').value = "DANH SÁCH KINH DOANH  CÁ THỂ";
    worksheet.getCell('D4').value = `Trạng thái: ${status}`;
    worksheet.getCell('D3').alignment = {horizontal: 'center', vertical: 'middle'};
    worksheet.getCell('D4').alignment = {horizontal: 'center', vertical: 'middle'};

    worksheet.getCell('D3').font = { ...worksheet.getCell('D3').font,...{bold: true, size: 14}}
    worksheet.getCell('D4').font = { ...worksheet.getCell('D4').font,...{bold: true, size: 14}}

    // add column name
    worksheet.getCell('A5').value = "STT";
    worksheet.getCell('B5').value = "Mã";
    worksheet.getCell('C5').value = "Tên kinh doanh";
    worksheet.getCell('D5').value = "Địa điểm";
    worksheet.getCell('E5').value = "Vốn";
    worksheet.getCell('F5').value = "Ngành nghề";
    worksheet.getCell('G5').value = "Mã giấy phép";
    worksheet.getCell('H5').value = "Ngày đăng kí";
    worksheet.getCell('I5').value = "Phường";
    worksheet.getCell('J5').value = "Trạng thái";
    worksheet.getCell('K5').value = "Ngày tạm nghỉ/ Nghỉ hẳn";
    worksheet.getCell('L5').value = "Mã số thuế";
    worksheet.getCell('M5').value = "Tên chủ doanh nghiệp";
    worksheet.getCell('N5').value = "Giới tính";
    worksheet.getCell('O5').value = "Ngày sinh";
    worksheet.getCell('P5').value = "Số CMND/CCCD";
    worksheet.getCell('Q5').value = "Ngày cấp CMND";
    worksheet.getCell('R5').value = "Cơ quan cấp";
    worksheet.getCell('S5').value = "Hộ khẩu";
    worksheet.getCell('T5').value = "Chổ ở";
    worksheet.getCell('U5').value = "Điện thoại";


    worksheet.getColumn('A').width = 10;
    worksheet.getColumn('B').width = 20;
    worksheet.getColumn('C').width = 30;
    worksheet.getColumn('D').width = 35;
    worksheet.getColumn('E').width = 20;
    worksheet.getColumn('F').width = 30;
    worksheet.getColumn('G').width = 20;
    worksheet.getColumn('H').width = 20;
    worksheet.getColumn('I').width = 20;
    worksheet.getColumn('J').width = 20;
    worksheet.getColumn('K').width = 20;
    worksheet.getColumn('L').width = 20;
    worksheet.getColumn('M').width = 20;
    worksheet.getColumn('N').width = 20;
    worksheet.getColumn('O').width = 20;
    worksheet.getColumn('P').width = 20;
    worksheet.getColumn('Q').width = 20;
    worksheet.getColumn('R').width = 20;  
    worksheet.getColumn('S').width = 20;
    worksheet.getColumn('T').width = 20;
    worksheet.getColumn('U').width = 20;

    const mileRow = 6;
    const cellNumber = 21;

    worksheet.getRow(5).height = 40;
    worksheet.getRow(5).alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
    worksheet.getRow(5).font = {name: 'Times New Roman', size: 12, bold: true};
    
    for (let i = 1 ; i <= cellNumber ; i++) {
      const cell = worksheet.findCell(5,i);
      cell.border  =  { top: { style: 'thin' }, left: { style: 'thin' }, bottom: { style: 'thin' }, right: { style: 'thin' } };
      cell.fill = {
        type: 'pattern',
          pattern: 'solid',
          fgColor: {argb: 'ff99ffad'},  
          bgColor: {argb: 'ff99ffad'}
      };
    }
    

    // đổ data
    let indexArr = 0;
    for(let i = mileRow ; i <=  mileRow + data.length ; i++) {
      worksheet.getRow(i).height = 30;
      if (data[indexArr]) {
        for(let j = 1 ; j <= cellNumber ; j++) {
          const cell = worksheet.getCell(i,j);
          cell.font = {name: 'Arial', size: 10,}
          cell.alignment = {horizontal: 'left', vertical: 'middle'};
          cell.border  =  { top: { style: 'thin' }, left: { style: 'thin' }, bottom: { style: 'thin' }, right: { style: 'thin' } };
          let value = null;
          switch(j) {
            case 1: 
              cell.alignment = {horizontal: 'center', vertical: 'middle'};
              cell.value = indexArr + 1 ; // index 
              break;
           case 2: // mã
              cell.alignment = {horizontal: 'center', vertical: 'middle'};
              cell.value = data[indexArr].individualBussinessInfor?.licenseCode ?? "";
              break;
           case 3:
              cell.alignment = {horizontal: 'center', vertical: 'middle'};
              cell.value = data[indexArr].individualBussinessInfor?.name ?? "";
              break;   
           case 4:
              cell.value = data[indexArr].individualBussinessInfor?.place ??  "";
              break;
           case 5:
              cell.alignment = {horizontal: 'right', vertical: 'middle'};
              cell.value = Utils.changeCurrency(data[indexArr].individualBussinessInfor?.capital) ??  "";
              break;
           case 6:
              cell.value =  data[indexArr]?.individualBussinessInfor?.bussinessSectors?.map(e => e?.name)?.join(",") ??  "";
              break;
           case 7:
              cell.value = data[indexArr].individualBussinessInfor?.licenseCode ?? "";
              break;   
           case 8:
                value = data[indexArr].individualBussinessInfor?.registrationDate ?? null;
                const date = new Date(value);
                cell.value = value? `${date.getDate()}-${date.getMonth() + 1}-${date.getFullYear()}`: "";
                break;      
           case 9:
              cell.value = data[indexArr].individualBussinessInfor?.ward?.name ?? "";
              break;
           case 10:
              cell.alignment = {horizontal: 'center', vertical: 'middle'};
              value = data[indexArr].individualBussinessInfor?.status ?? null;
              cell.value = this.retrieveBusinessStatusName(value) ?? "";
              break;
           case 11:
              cell.alignment = {horizontal: 'center', vertical: 'middle'};
              const checkCloure = data[indexArr].individualBussinessInfor?.permanentClosure?.isPermanentlyClosed ?? false;
              const checkSuspense = data[indexArr].individualBussinessInfor?.suspension?.isSuspension ?? false;
              if (checkCloure) {
                value = data[indexArr].individualBussinessInfor?.permanentClosure?.closureDate ?? null;
              }
              if (checkSuspense) {
                value = data[indexArr].individualBussinessInfor?.suspension?.startDate ?? null;
              }
              if (value == null) break;
              const tmpdate = new Date(value);
              cell.value = tmpdate? `${tmpdate.getDate()}-${tmpdate.getMonth() + 1}-${tmpdate.getFullYear()}`: "";
              break;
           case 12:
              cell.alignment = {horizontal: 'center', vertical: 'middle'};
              cell.value = data[indexArr].individualBussinessInfor?.taxCode ?? "";    
              break;
           case 13:
              cell.alignment = {horizontal: 'center', vertical: 'middle'};
              cell.value = data[indexArr].representativeInfor?.fullName ?? "";    
              break;
           case 14:
              cell.alignment = {horizontal: 'center', vertical: 'middle'};
              value =  data[indexArr].representativeInfor?.gender ?? "";
              cell.value = this.retrieveGenderName(value);   
              break;     
           case 15:
              cell.alignment = {horizontal: 'center', vertical: 'middle'};
              value = data[indexArr].representativeInfor?.birthDate ?? null;
              const brithDate = new Date(value);
              cell.value = value? `${brithDate.getDate()}-${brithDate.getMonth() + 1}-${brithDate.getFullYear()}`: "";
              break;   
           case 16:
              cell.alignment = {horizontal: 'center', vertical: 'middle'};
              cell.value = data[indexArr].representativeInfor?.idCitizen ?? "";    
              break;  
           case 17:
              cell.alignment = {horizontal: 'center', vertical: 'middle'};
              value = data[indexArr].representativeInfor?.idCitizenDate ?? null;
              const idCitizenDate = new Date(value);
              cell.value = value? `${idCitizenDate.getDate()}-${idCitizenDate.getMonth() + 1}-${idCitizenDate.getFullYear()}`: "";
              break; 
           case 18: 
              cell.value =  data[indexArr].representativeInfor?.idCitizenPlace?.name ?? "";   
              break;
           case 19:
              cell.value =  data[indexArr].representativeInfor?.permanentAddress ?? "";   
              break; 
           case 20:
              cell.value =  data[indexArr].representativeInfor?.currentAddress ?? "";   
              break; 
           case 21:
              cell.value =  data[indexArr].representativeInfor?.phoneNumber ?? "";   
              break;        
          }
        }
      }
      indexArr++;
    }



    const EXCEL_TYPE = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8';
    // Save Excel File
    workbook.xlsx.writeBuffer().then((dataBuffer: ArrayBuffer) => {
      const blob = new Blob([dataBuffer], {type: EXCEL_TYPE});
      fs.saveAs(blob, `danh_sach_ho_ca_the_${this.datePipe.transform(new Date(),"ddMMyyy")}.xlsx`);
    });
  }

  retrieveBusinessStatusName(statusId) {
    switch(statusId) {
      case -1: return "Tất cả";
      case 1: return "Bình thường";
      case 2: return "Tạm ngừng hoạt động";
      case 3: return "Giải thể";
      case 4: return "Bị thu hồi GCN ĐKKD";
      case 5: return "Bị phá sản";
      case 6: return "Nghỉ hẳn"; 
      case 7: return "Cấp mới"; 
      default: return "Không có trạng thái";
    }
  }

  retrieveGenderName(id) {
    switch(id){
      case 0 : return "không xác định";
      case 1 : return "Nam";
      case 2 : return "Nữ";
      default: return "";
    }
  }

  retrieveBusinessStatusNameHTX(statusId) {
    switch(statusId) {
      case 1: return "Cấp mới";
      case 2: return "Tạm ngừng hoạt động";
      case 3: return "Giải thể";
      case 4: return "Bị thu hồi GCN ĐKKD";
      case 5: return "Bị phá sản";
      case 6: return "Nghỉ hẳn"; 
      default: return "Không có trạng thái";
    }
  }

  exportExcelBusinessCooperative(
    status: string,
    provinceName: string,
    districtName: string,
    reportName: string,
    json: any[],
    sheetName: string
  ) {
    const data = json;
    // create workbook and worksheet
    const workbook = new Workbook();
    workbook.creator = 'Snippet Coder';
    workbook.lastModifiedBy = 'SnippetCoder';
    workbook.created = new Date();
    workbook.modified = new Date();
    const worksheet = workbook.addWorksheet(sheetName);
    const fontHeader = {name: 'Times New Roman'};
    worksheet.getColumn('A').font = fontHeader;
    worksheet.getColumn('B').font = fontHeader;
    worksheet.getColumn('C').font = fontHeader;
    worksheet.getColumn('D').font = fontHeader;
    worksheet.getColumn('E').font = fontHeader;
    worksheet.getColumn('F').font = fontHeader;
    worksheet.getColumn('G').font = fontHeader;
    worksheet.getColumn('H').font = fontHeader;
    worksheet.getColumn('I').font = fontHeader;
    worksheet.getColumn('J').font = fontHeader;
    worksheet.getColumn('K').font = fontHeader;
    worksheet.getColumn('L').font = fontHeader;
    worksheet.getColumn('M').font = fontHeader;
    worksheet.getColumn('N').font = fontHeader;
    worksheet.getColumn('O').font = fontHeader;
    worksheet.getColumn('P').font = fontHeader;
    worksheet.getColumn('Q').font = fontHeader;
    worksheet.getColumn('R').font = fontHeader;
    worksheet.getColumn('S').font = fontHeader;
    worksheet.getColumn('T').font = fontHeader;
    worksheet.getColumn('U').font = fontHeader;
    


    // Add header row
    const today = new Date();
    worksheet.addRow([]);
    worksheet.mergeCells('A1:C1');
    worksheet.mergeCells('A2:C2');
    worksheet.mergeCells('A3:C3');
    worksheet.getCell('A1').value = "Tỉnh " + provinceName;
    worksheet.getCell('A2').value = `Ngày ${ moment().format('DD') } tháng ${moment().format('MM')} năm ${today.getFullYear()}`;
    worksheet.getCell('A3').value = districtName;
    worksheet.getCell('A1').alignment = {horizontal: 'center', vertical: 'middle'};
    worksheet.getCell('A2').alignment = {horizontal: 'center', vertical: 'middle'};
    worksheet.getCell('A3').alignment = {horizontal: 'center', vertical: 'middle'};
    
    worksheet.getCell('A1').font = { ...worksheet.getCell('A1').font,...{bold: true, size: 14}}
    worksheet.getCell('A2').font = { ...worksheet.getCell('A1').font,...{bold: true, size: 14}}
    worksheet.getCell('A3').font = { ...worksheet.getCell('A1').font,...{bold: true, size: 14}}

    // add report name
    worksheet.mergeCells('D3:J3');
    worksheet.mergeCells('D4:J4');
    worksheet.getCell('D3').value = reportName;
    worksheet.getCell('D4').value = `Trạng thái: ${status}`;
    worksheet.getCell('D3').alignment = {horizontal: 'center', vertical: 'middle'};
    worksheet.getCell('D4').alignment = {horizontal: 'center', vertical: 'middle'};

    worksheet.getCell('D3').font = { ...worksheet.getCell('D3').font,...{bold: true, size: 14}}
    worksheet.getCell('D4').font = { ...worksheet.getCell('D4').font,...{bold: true, size: 14}}

    // add column name
    worksheet.getCell('A5').value = "STT";
    worksheet.getCell('B5').value = "Mã";
    worksheet.getCell('C5').value = "Tên hợp tác xã";
    worksheet.getCell('D5').value = "Địa điểm kinh doanh";
    worksheet.getCell('E5').value = "Vốn";
    worksheet.getCell('F5').value = "Ngành nghề";
    worksheet.getCell('G5').value = "Mã giấy phép";
    worksheet.getCell('H5').value = "Ngày đăng kí";
    worksheet.getCell('I5').value = "Phường";
    worksheet.getCell('J5').value = "Trạng thái";
    worksheet.getCell('K5').value = "Mã số thuế";
    worksheet.getCell('L5').value = "Tên người đại diện";
    worksheet.getCell('M5').value = "Giới tính";
    worksheet.getCell('N5').value = "Ngày sinh";
    worksheet.getCell('O5').value = "Số CMND/CCCD";
    worksheet.getCell('P5').value = "Ngày cấp CMND";
    worksheet.getCell('Q5').value = "Cơ quan cấp";
    worksheet.getCell('R5').value = "Hộ khẩu";
    worksheet.getCell('S5').value = "Chổ ở";
    worksheet.getCell('T5').value = "Điện thoại";
    worksheet.getCell('U5').value = "Email liên hệ";

    worksheet.getColumn('A').width = 10;
    worksheet.getColumn('B').width = 20;
    worksheet.getColumn('C').width = 30;
    worksheet.getColumn('D').width = 35;
    worksheet.getColumn('E').width = 20;
    worksheet.getColumn('F').width = 30;
    worksheet.getColumn('G').width = 20;
    worksheet.getColumn('H').width = 20;
    worksheet.getColumn('I').width = 20;
    worksheet.getColumn('J').width = 20;
    worksheet.getColumn('K').width = 20;
    worksheet.getColumn('L').width = 20;
    worksheet.getColumn('M').width = 20;
    worksheet.getColumn('N').width = 20;
    worksheet.getColumn('O').width = 20;
    worksheet.getColumn('P').width = 20;
    worksheet.getColumn('Q').width = 20;
    worksheet.getColumn('R').width = 20;  
    worksheet.getColumn('S').width = 20;
    worksheet.getColumn('T').width = 20;
    worksheet.getColumn('U').width = 20;

    const mileRow = 6;
    const cellNumber = 21;

    worksheet.getRow(5).height = 40;
    worksheet.getRow(5).alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
    worksheet.getRow(5).font = {name: 'Times New Roman', size: 12, bold: true};
    
    for (let i = 1 ; i <= cellNumber ; i++) {
      const cell = worksheet.findCell(5,i);
      cell.border  =  { top: { style: 'thin' }, left: { style: 'thin' }, bottom: { style: 'thin' }, right: { style: 'thin' } };
      cell.fill = {
        type: 'pattern',
          pattern: 'solid',
          fgColor: {argb: 'ff99ffad'},  
          bgColor: {argb: 'ff99ffad'}
      };
    }

    // đổ data
    let indexArr = 0;
    for(let i = mileRow ; i <=  mileRow + data.length ; i++) {
      worksheet.getRow(i).height = 30;
      if (data[indexArr]) {
        for(let j = 1 ; j <= cellNumber ; j++) {
          const cell = worksheet.getCell(i,j);
          cell.font = {name: 'Arial', size: 10,}
          cell.alignment = {horizontal: 'left', vertical: 'middle'};
          cell.border  =  { top: { style: 'thin' }, left: { style: 'thin' }, bottom: { style: 'thin' }, right: { style: 'thin' } };
          let value = null;
          switch(j) {
            case 1: 
              cell.value = indexArr + 1 ; // index 
              break;
           case 2: // mã
              cell.alignment = {horizontal: 'center', vertical: 'middle'};
              cell.value = data[indexArr].cooperativeBussinessInfor?.licenseCode ?? "";
              break;
           case 3: //ten htx
              cell.alignment = {horizontal: 'center', vertical: 'middle'};
              cell.value = data[indexArr].cooperativeBussinessInfor?.name ?? "";
              break;   
           case 4:// dia diem kinh doanh
              cell.value = data[indexArr].cooperativeBussinessInfor?.place ??  "";
              break;
           case 5: // von
              cell.alignment = {horizontal: 'right', vertical: 'middle'};
              cell.value = Utils.changeCurrency(data[indexArr].cooperativeBussinessInfor?.totalCapital) ??  "";
              break;
           case 6: //ngành nghề
              cell.value =  data[indexArr]?.cooperativeBussinessInfor?.bussinessSectors?.map(e => e?.name)?.join(",") ??  "";
              break;
           case 7: //mã giấy phép
              cell.value = data[indexArr].cooperativeBussinessInfor?.licenseCode ?? "";
              break;   
           case 8: // ngày đăng ký
                value = data[indexArr].createdDate ?? null;
                const date = new Date(value);
                cell.value = value ? moment(new Date(value)).format('DD/MM/YYYY'): "";
                break;      
           case 9: //phường
              cell.value = data[indexArr].cooperativeBussinessInfor?.ward?.name ?? "";
              break;
           case 10: // trạng thái
              value = data[indexArr].cooperativeBussinessInfor?.status ?? null;
              cell.value = this.retrieveBusinessStatusNameHTX(value) ?? "";
              break;
           case 11: //Mã số thuế
              cell.value = "";
              break;
           case 12: //tên ng đại diện
              cell.alignment = {horizontal: 'center', vertical: 'middle'};
              cell.value = data[indexArr].representativeInfor?.fullName ?? "";    
              break;
           case 13: // giới tính
              cell.alignment = {horizontal: 'center', vertical: 'middle'};
              value =  data[indexArr].representativeInfor?.gender ?? "";
              cell.value = this.retrieveGenderName(value);   
              break;
           case 14: // ngày sinh
              cell.alignment = {horizontal: 'center', vertical: 'middle'};
              value = data[indexArr].representativeInfor?.birthDate ?? null;
              // const brithDate = new Date(value);
              cell.value = value ? moment(new Date(value)).format('DD/MM/YYYY'): "";
              break;     
           case 15: //cMND
              cell.alignment = {horizontal: 'center', vertical: 'middle'};
              cell.value = data[indexArr].representativeInfor?.idCitizen ?? "";   
              break;   
           case 16: // Ngày cấp CMNND
              cell.alignment = {horizontal: 'center', vertical: 'middle'};
              value = data[indexArr].representativeInfor?.idCitizenDate ?? null;
              cell.value = value ? moment(new Date(value)).format('DD/MM/YYYY'): "";
              // const idCitizenDate = new Date(value);
              // cell.value = value? `${idCitizenDate.getDate()}-${idCitizenDate.getMonth() + 1}-${idCitizenDate.getFullYear()}`: "";  
              break;  
           case 17: //cơ quan cấp
              cell.alignment = {horizontal: 'center', vertical: 'middle'};
              cell.value =  data[indexArr].representativeInfor?.idCitizenPlace?.name ?? "";   
              break; 
           case 18: //hộ khẩu
              cell.value =  data[indexArr].representativeInfor?.permanentAddress ?? "";
              break;
           case 19: //chỗ ở
              cell.value =  data[indexArr].representativeInfor?.currentAddress ?? ""; 
              break; 
           case 20: //điện thoại
              cell.value =  data[indexArr].representativeInfor?.phoneNumber ?? "";   
              break; 
           case 21: //email
              cell.value =  data[indexArr].representativeInfor?.email ?? "";   
              break;        
          }
        }
      }
      indexArr++;
    }

    const EXCEL_TYPE = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8';
    // Save Excel File
    workbook.xlsx.writeBuffer().then((dataBuffer: ArrayBuffer) => {
      const blob = new Blob([dataBuffer], {type: EXCEL_TYPE});
      fs.saveAs(blob, `danh_sach_hop_tac_xa_${this.datePipe.transform(new Date(),"dd-MM-yyyy")}.xlsx`);
    });
  }

  
  importExcelBusinessHouseHold(formData: FormData):Observable<any>{
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.post(this.url+ `/import-business-household-excel`,formData,{headers});
  }


  checkIfLicenseCodeExisted(licenseCode):Observable<any>{
    let params = new HttpParams();
    params = params.append("license-code",licenseCode);
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.url+ `/check-license-code-exsited`,{headers,params});
  }
  
  
}

interface IAgency {
  id: string ,
  name: string
}
interface IUser {
  id: string,
  fullname: string
}