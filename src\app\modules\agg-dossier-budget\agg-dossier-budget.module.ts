import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AggDossierBudgetRoutingModule } from './agg-dossier-budget-routing.module';
import { SharedModule } from 'src/app/shared/shared.module';
import { AggDossierBudgetComponent } from './agg-dossier-budget.component';
import { DetailAggDossierBudgetComponent } from './dialogs/detail-agg-dossier-budget/detail-agg-dossier-budget.component';
import { CertificateAggDossierBudgetComponent } from './dialogs/certificate-agg-dossier-budget/certificate-agg-dossier-budget.component';


@NgModule({
  declarations: [AggDossierBudgetComponent, DetailAggDossierBudgetComponent, CertificateAggDossierBudgetComponent],
  imports: [
    CommonModule,
    AggDossierBudgetRoutingModule,
    SharedModule,
  ]
})
export class AggDossierBudgetModule { }
