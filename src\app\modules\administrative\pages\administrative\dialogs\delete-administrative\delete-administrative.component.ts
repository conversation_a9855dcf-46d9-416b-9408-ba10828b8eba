import { Component, OnInit, Inject } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { BasepadService } from 'src/app/data/service/basepad/basepad.service';
import { BasedataService } from 'src/app/data/service/basedata/basedata.service';


@Component({
  selector: 'app-delete-administrative',
  templateUrl: './delete-administrative.component.html',
  styleUrls: ['./delete-administrative.component.scss']
})

export class DeleteAdministrativeComponent implements OnInit {
  Id: string;
  name: string;
  infoDetail: any;
  constructor(
    public dialogRef: MatDialogRef<DeleteAdministrativeComponent>,
    @Inject(MAT_DIALOG_DATA) public data: ConfirmDeleteDialogModel,
    private basedataService: BasedataService,
    private basepadService: BasepadService
  ) {
    console.log('delete', data);
    this.Id = data.id;
    this.name = data.name;
  }

  ngOnInit(): void {
    
  }

  onConfirm() {
    this.basepadService.deletePublicAdministration(this.Id).subscribe(data => {
        this.dialogRef.close(true);
      }, err => {
        this.dialogRef.close(false);
      });

  }

  onDismiss() {
    this.dialogRef.close();
  }

  getDetail() {
    // this.basedataService.getDetailAgency(this.Id).subscribe(response => {
    //   this.infoDetail = response;
    // });
  }

}

export class ConfirmDeleteDialogModel {
  constructor(public id: string, public name: string) {
  }
}