<h2>BÁO CÁO CHỈ THỊ 08 TOÀN TỈNH</h2>
<div class="prc_searchbar">

  <div fxLayout="row" fxLayout.xs="row" fxLayout.sm="row" class="formFieldOutline" fxLayoutAlign="space-between">
    <div appearance="outline"  fxFlex='grow'>
      <div fxLayout="row" fxLayout.xs="row" fxLayout.sm="row" class="formFieldOutline" fxLayoutAlign="space-between">

        <mat-form-field appearance="outline" fxFlex.gt-sm="24" fxFlex.gt-xs="49.5" fxFlex='grow'>
          <mat-label>Từ ngày</mat-label>
          <input matInput [matDatepicker]="pickerAcceptFrom" [(ngModel)]="startDate">
          <mat-datepicker-toggle matSuffix [for]="pickerAcceptFrom"></mat-datepicker-toggle>
          <mat-datepicker #pickerAcceptFrom></mat-datepicker>
        </mat-form-field>
        <mat-form-field appearance="outline" fxFlex.gt-sm="24" fxFlex.gt-xs="49.5" fxFlex='grow'>
          <mat-label>Đến ngày</mat-label>
          <input matInput [matDatepicker]="pickerAcceptTo" [(ngModel)]="endDate">
          <mat-datepicker-toggle matSuffix [for]="pickerAcceptTo"></mat-datepicker-toggle>
          <mat-datepicker #pickerAcceptTo></mat-datepicker>
        </mat-form-field>
        <mat-form-field appearance="outline" fxFlex.gt-sm="24" fxFlex.gt-xs="49.5" fxFlex='grow'>
          <mat-label>Từ ngày lũy kế</mat-label>
          <input matInput [matDatepicker]="pickerAcceptFrom1" [(ngModel)]="startDateCumulative">
          <mat-datepicker-toggle matSuffix [for]="pickerAcceptFrom1"></mat-datepicker-toggle>
          <mat-datepicker #pickerAcceptFrom1></mat-datepicker>
        </mat-form-field>
        <mat-form-field appearance="outline" fxFlex.gt-sm="24" fxFlex.gt-xs="49.5" fxFlex='grow'>
          <mat-label>Đến ngày lũy kế</mat-label>
          <input matInput [matDatepicker]="pickerAcceptTo1" [(ngModel)]="endDateCumulative">
          <mat-datepicker-toggle matSuffix [for]="pickerAcceptTo1"></mat-datepicker-toggle>
          <mat-datepicker #pickerAcceptTo1></mat-datepicker>
        </mat-form-field>
     
      </div>
    </div>


  </div>
 

  <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="row" fxLayoutAlign="end" style="padding-bottom: 1em;">
    <button mat-flat-button fxFlex.lt-md="22" fxFlex.md="20" fxFlex.gt-md="12" fxFlex.gt-xs="49.5" fxFlex='grow' style="background-color: #ce7a58;"
      class="btn-search" type="submit" (click)="thongKe()"  [disabled]="waitingDownloadExcel"> 
      <mat-icon class="iconStatistical">bar_chart</mat-icon><span>Thống kê</span>
      <mat-progress-bar mode="indeterminate" *ngIf="waitingDownloadExcel"></mat-progress-bar>
    </button>
    <div fxFlex='1'></div>
    <button mat-flat-button fxFlex.lt-md="22" fxFlex.md="20" fxFlex.gt-md="12" fxFlex.gt-xs="49.5" fxFlex='grow'
      class="btn-download-excel"  (click)="exportToExcel()" [disabled]="waitingDownloadExcel" style="background-color: #38A938;">
      <mat-icon class="iconStatistical">cloud_download</mat-icon>
      <span>Xuất excel</span>
      <mat-progress-bar mode="indeterminate" *ngIf="waitingDownloadExcel"></mat-progress-bar>
    </button>

  </div>
</div>

<div fxLayout="row" fxLayoutAlign="center">
  <div class="frm_main" fxFlex="grow">
    <div class="logbookTbl">
      <div class="logbookOnlyTbl">
        <table class="table">
          <thead>
            <tr >
                <th rowspan="3">STT</th>
                <th rowspan="3">Tên cơ quan, đơn vị</th>
                <th rowspan="3">Kỳ báo cáo</th>
                <th colspan="6">Tiếp nhận hồ sơ TTHC</th>
                <th colspan="6">Số hồ sơ TTHC đã giải quyết</th>
                <th colspan="7">Tỷ lệ TTHC cung cấp dịch vụ có phát sinh hồ sơ</th>
            </tr>
            <tr>
                <th rowspan="2">Tổng số</th>
                <th rowspan="2">Trực tiếp</th>
                <th rowspan="2">Qua BCCI</th>
                <th colspan="2">Trực tuyến</th>
                <th rowspan="2">Cập nhật lên iGate</th>
                <th rowspan="2">Tổng số</th>
                <th rowspan="2">Trực tiếp</th>
                <th rowspan="2">Qua BCCI</th>
                <th rowspan="2">Trực tuyến</th>
                <th rowspan="2">Cập nhật lên iGate</th>
                <th rowspan="2">Số hồ sơ TTHC sử dụng ký số trong giải quyết</th>
                <th rowspan="2">Tổng số TTHC của cơ quan</th>
                <th colspan="2">Một phần (trực tuyến)</th>
                <th colspan="2">Toàn trình</th>
                <th colspan="2">BCCI</th>
            </tr>
            <tr >
                <th>Một phần</th>
                <th>Toàn trình</th>
                <th>Số TTHC cung cấp</th>
                <th>Số TTHC có phát sinh hồ sơ</th>
                <th>Số TTHC cung cấp</th>
                <th>Số TTHC có phát sinh hồ sơ</th>
                <th>Số TTHC cung cấp</th>
                <th>Số TTHC có phát sinh hồ sơ</th>
            </tr>
            <tr >
                <th>(1)</th>
                <th>(2)</th>
                <th>(3)</th>
                <th>(4)</th>
                <th>(5)</th>
                <th>(6)</th>
                <th>(7)</th>
                <th>(8)</th>
                <th>(9)</th>
                <th>(10)</th>
                <th>(11)</th>
                <th>(12)</th>
                <th>(13)</th>
                <th>(14)</th>
                <th>(15)</th>
                <th>(16)</th>
                <th>(17)</th>
                <th>(18)</th>
                <th>(19)</th>
                <th>(20)</th>
                <th>(21)</th>
                <th>(22)</th>
            </tr>
          </thead>
          <tbody>
       
            <tr>
              <td colspan="1" style="text-align: center; font-weight: bold">I</td>
              <td colspan="24" style="text-align: left; font-weight: bold">Sở ban ngành</td>
            </tr>
       
            <ng-container *ngFor="let item of listHoSoCapTinh;let i = index ">
              <tr *ngIf="listHoSoCapTinh.length>0">
                <td rowspan="2">{{i+1}}</td>
                <td rowspan="2">{{item.coQuan}}</td>
                <td>Trong kỳ</td>
                <td><a (click)="GetDetailDossier(item.id,item.coQuan,item.data[0].TrongKy,0,0,0,0,0,0)">{{item.data[0].tiepNhanTrongKy}}</a></td>
                <td><a (click)="GetDetailDossier(item.id,item.coQuan,item.data[0].TrongKy,0,1,0,0,0,0)">{{item.data[0].tiepNhanTrucTiep}}</a></td>
                <td><a (click)="GetDetailDossier(item.id,item.coQuan,item.data[0].TrongKy,0,0,1,0,0,0)">{{item.data[0].hoSoBuuChinh}}</a></td>
                <td><a (click)="GetDetailDossier(item.id,item.coQuan,item.data[0].TrongKy,0,0,0,1,0,0)">{{item.data[0].tiepNhanMotPhan}}</a></td>
                <td><a (click)="GetDetailDossier(item.id,item.coQuan,item.data[0].TrongKy,0,0,0,0,1,0)">{{item.data[0].tiepNhanToantrinh}}</a></td>
                <td><a (click)="GetDetailDossier(item.id,item.coQuan,item.data[0].TrongKy,0,0,0,0,0,0)">{{item.data[0].tiepNhanTrongKy}}</a></td>
                <td><a (click)="GetDetailDossier(item.id,item.coQuan,item.data[0].TrongKy,1,0,0,0,0,0)">{{item.data[0].tongdaXuLy}}</a></td>
                <td><a (click)="GetDetailDossier(item.id,item.coQuan,item.data[0].TrongKy,1,1,0,0,0,0)">{{item.data[0].dxltiepNhanTrucTiep}}</a></td>
                <td><a (click)="GetDetailDossier(item.id,item.coQuan,item.data[0].TrongKy,1,0,1,0,0,0)">{{item.data[0].dxlhoSoBuuChinh}}</a></td>
                <td><a (click)="GetDetailDossier(item.id,item.coQuan,item.data[0].TrongKy,1,0,0,2,2,0)">{{item.data[0].dxltiepNhanTrucTuyen}}</a></td>
                <td><a (click)="GetDetailDossier(item.id,item.coQuan,item.data[0].TrongKy,1,0,0,0,0,0)">{{item.data[0].tongdaXuLy}}</a></td>
                <td><a ></a></td>
                <td><a (click) ="GetDetailProcedure(item.id,item.coQuan,'0')">{{item.data[0].TongSoThuTuc}}</a></td>
                <td><a (click) ="GetDetailProcedure(item.id,item.coQuan,'5f5b2c4b4e1bd312a6f3ae24')">{{item.data[0].ThutucMotPhan}}</a></td>
                <td><a (click)="GetDetailDossier(item.id,item.coQuan,item.data[0].TrongKy,0,0,0,1,0,item.data[0].tiepNhanMotPhan)">{{item.data[0].tthcMotPhan}}</a></td>
                <td><a (click) ="GetDetailProcedure(item.id,item.coQuan,'5f5b2c564e1bd312a6f3ae25')">{{item.data[0].ThuTucToantrinh}}</a></td>
               <td><a (click)="GetDetailDossier(item.id,item.coQuan,item.data[0].TrongKy,0,0,0,0,1,0)">{{item.data[0].tthcToanTrinh}}</a></td>
                <td><a ></a></td>
                <td><a (click)="GetDetailDossier(item.id,item.coQuan,item.data[0].TrongKy,0,0,1,0,0,0)">{{item.data[0].tthcBuuChinh}}</a></td>

              </tr>
              <tr *ngIf="listHoSoCapTinh.length>0">
                <td>Lỹ kế đến {{endDateCumulative | date: 'dd/MM/yyyy'}}</td>
                <td><a (click)="GetDetailDossier(item.id,item.coQuan,item.data[1].TrongKy,0,0,0,0,0,0)">{{item.data[1].tiepNhanTrongKy}}</a></td>
                <td><a (click)="GetDetailDossier(item.id,item.coQuan,item.data[1].TrongKy,0,1,0,0,0,0)">{{item.data[1].tiepNhanTrucTiep}}</a></td>
                <td><a (click)="GetDetailDossier(item.id,item.coQuan,item.data[1].TrongKy,0,0,1,0,0,0)">{{item.data[1].hoSoBuuChinh}}</a></td>
                <td><a (click)="GetDetailDossier(item.id,item.coQuan,item.data[1].TrongKy,0,0,0,1,0,0)">{{item.data[1].tiepNhanMotPhan}}</a></td>
                <td><a (click)="GetDetailDossier(item.id,item.coQuan,item.data[1].TrongKy,0,0,0,0,1,0)">{{item.data[1].tiepNhanToantrinh}}</a></td>
                <td><a (click)="GetDetailDossier(item.id,item.coQuan,item.data[1].TrongKy,0,0,0,0,0,0)">{{item.data[1].tiepNhanTrongKy}}</a></td>
                <td><a (click)="GetDetailDossier(item.id,item.coQuan,item.data[1].TrongKy,1,0,0,0,0,0)">{{item.data[1].tongdaXuLy}}</a></td>
                <td><a (click)="GetDetailDossier(item.id,item.coQuan,item.data[1].TrongKy,1,1,0,0,0,0)">{{item.data[1].dxltiepNhanTrucTiep}}</a></td>
                <td><a (click)="GetDetailDossier(item.id,item.coQuan,item.data[1].TrongKy,1,0,1,0,0,0)">{{item.data[1].dxlhoSoBuuChinh}}</a></td>
                <td><a (click)="GetDetailDossier(item.id,item.coQuan,item.data[1].TrongKy,1,0,0,2,2,0)">{{item.data[1].dxltiepNhanTrucTuyen}}</a></td>
                <td><a (click)="GetDetailDossier(item.id,item.coQuan,item.data[1].TrongKy,1,0,0,0,0,0)">{{item.data[1].tongdaXuLy}}</a></td>
                <td><a ></a></td>
                <td><a (click) ="GetDetailProcedure(item.id,item.coQuan,'0')">{{item.data[1].TongSoThuTuc}}</a></td>
                <td><a (click) ="GetDetailProcedure(item.id,item.coQuan,'5f5b2c4b4e1bd312a6f3ae24')">{{item.data[1].ThutucMotPhan}}</a></td>
                <td><a (click)="GetDetailDossier(item.id,item.coQuan,item.data[1].TrongKy,0,0,0,1,0,item.data[1].tiepNhanMotPhan)">{{item.data[1].tthcMotPhan}}</a></td>
                <td><a (click) ="GetDetailProcedure(item.id,item.coQuan,'5f5b2c564e1bd312a6f3ae25')">{{item.data[1].ThuTucToantrinh}}</a></td>          
                 <td><a (click)="GetDetailDossier(item.id,item.coQuan,item.data[1].TrongKy,0,0,0,0,1,item.data[1].tiepNhanToantrinh)">{{item.data[1].tthcToanTrinh}}</a></td>
                <td><a ></a></td>
                <td><a (click)="GetDetailDossier(item.id,item.coQuan,item.data[1].TrongKy,0,0,1,0,0,0)">{{item.data[1].tthcBuuChinh}}</a></td>

              </tr>
          </ng-container>
          <tr *ngIf="listHoSoCapTinh.length>0" class="sum">
              
            <td colspan="2" rowspan="2"> TỔNG SỞ BAN NGÀNH</td>
            <td>Trong kỳ</td>
            <td> {{TongCapTinh.tiepNhanTrongKy}}</td>
            <td>{{TongCapTinh.tiepNhanTrucTiep}}</td>
            <td>{{TongCapTinh.hoSoBuuChinh}}</td>
            <td>{{TongCapTinh.tiepNhanMotPhan}}</td>
            <td>{{TongCapTinh.tiepNhanToantrinh}}</td>
            <td>{{TongCapTinh.tiepNhanTrongKy}}</td>
            <td>{{TongCapTinh.tongdaXuLy}}</td>
            <td>{{TongCapTinh.dxltiepNhanTrucTiep}}</td>
            <td>{{TongCapTinh.dxlhoSoBuuChinh}}</td>
            <td>{{TongCapTinh.dxltiepNhanTrucTuyen}}</td>
            <td>{{TongCapTinh.tongdaXuLy}}</td>
            <td></td>
            <td>{{TongCapTinh.TongSoThuTuc}}</td>
            <td>{{TongCapTinh.ThutucMotPhan}}</td>
            <td>{{TongCapTinh.tthcMotPhan}}</td>
            <td>{{TongCapTinh.ThuTucToantrinh}}</td>
            <td>{{TongCapTinh.tthcToanTrinh}}</td>
            <td></td>
            <td>{{TongCapTinh.tthcBuuChinh}}</td>
            
          </tr>
          <tr *ngIf="listHoSoCapTinh.length>0" class="sum">
              <td>Lỹ kế đến {{endDateCumulative | date: 'dd/MM/yyyy'}}</td>
              <td>{{TongCapTinhLK.tiepNhanTrongKy}}</td>
              <td>{{TongCapTinhLK.tiepNhanTrucTiep}}</td>
              <td>{{TongCapTinhLK.hoSoBuuChinh}}</td>
              <td>{{TongCapTinhLK.tiepNhanMotPhan}}</td>
              <td>{{TongCapTinhLK.tiepNhanToantrinh}}</td>
              <td>{{TongCapTinhLK.tiepNhanTrongKy}}</td>
              <td>{{TongCapTinhLK.tongdaXuLy}}</td>
              <td>{{TongCapTinhLK.dxltiepNhanTrucTiep}}</td>
              <td>{{TongCapTinhLK.dxlhoSoBuuChinh}}</td>
              <td>{{TongCapTinhLK.dxltiepNhanTrucTuyen}}</td>
              <td>{{TongCapTinhLK.tongdaXuLy}}</td>
              <td></td>
              <td>{{TongCapTinhLK.TongSoThuTuc}}</td>
              <td>{{TongCapTinhLK.ThutucMotPhan}}</td>
              <td>{{TongCapTinhLK.tthcMotPhan}}</td>
              <td>{{TongCapTinhLK.ThuTucToantrinh}}</td>
              <td>{{TongCapTinhLK.tthcToanTrinh}}</td>
              <td></td>
              <td>{{TongCapTinhLK.tthcBuuChinh}}</td>
            </tr>

            <tr>
              
              <td colspan="1" style="text-align: center; font-weight: bold">II</td>
              <td colspan="24" style="text-align: left; font-weight: bold">Thành phố, huyện, thị xã</td>
            </tr>
            <ng-container *ngFor="let item of listHoSoCapHuyen;let i = index ">
              <tr >
                <td rowspan="2">{{i+1}}</td>
                <td rowspan="2">{{item.coQuan}}</td>
                <td>Trong kỳ</td>
                <td><a (click)="GetDetailDossier(item.id,item.coQuan,item.data[0].TrongKy,0,0,0,0,0,item.data[0].tiepNhanTrongKy)">{{item.data[0].tiepNhanTrongKy}}</a></td>
                <td><a (click)="GetDetailDossier(item.id,item.coQuan,item.data[0].TrongKy,0,1,0,0,0,item.data[0].tiepNhanTrucTiep)">{{item.data[0].tiepNhanTrucTiep}}</a></td>
                <td><a (click)="GetDetailDossier(item.id,item.coQuan,item.data[0].TrongKy,0,0,1,0,0,item.data[0].hoSoBuuChinh)">{{item.data[0].hoSoBuuChinh}}</a></td>
                <td><a (click)="GetDetailDossier(item.id,item.coQuan,item.data[0].TrongKy,0,0,0,1,0,item.data[0].tiepNhanMotPhan)">{{item.data[0].tiepNhanMotPhan}}</a></td>
                <td><a (click)="GetDetailDossier(item.id,item.coQuan,item.data[0].TrongKy,0,0,0,0,1,item.data[0].tiepNhanToantrinh)">{{item.data[0].tiepNhanToantrinh}}</a></td>
                <td><a (click)="GetDetailDossier(item.id,item.coQuan,item.data[0].TrongKy,0,0,0,0,0,item.data[0].tiepNhanTrongKy)">{{item.data[0].tiepNhanTrongKy}}</a></td>
                <td><a (click)="GetDetailDossier(item.id,item.coQuan,item.data[0].TrongKy,1,0,0,0,0,item.data[0].tongdaXuLy)">{{item.data[0].tongdaXuLy}}</a></td>
                <td><a (click)="GetDetailDossier(item.id,item.coQuan,item.data[0].TrongKy,1,1,0,0,0,item.data[0].dxltiepNhanTrucTiep)">{{item.data[0].dxltiepNhanTrucTiep}}</a></td>
                <td><a (click)="GetDetailDossier(item.id,item.coQuan,item.data[0].TrongKy,1,0,1,0,0,item.data[0].dxlhoSoBuuChinh)">{{item.data[0].dxlhoSoBuuChinh}}</a></td>
                <td><a (click)="GetDetailDossier(item.id,item.coQuan,item.data[0].TrongKy,1,0,0,2,2,item.data[0].dxltiepNhanTrucTuyen)">{{item.data[0].dxltiepNhanTrucTuyen}}</a></td>
                <td><a (click)="GetDetailDossier(item.id,item.coQuan,item.data[0].TrongKy,1,0,0,0,0,item.data[0].tongdaXuLy)">{{item.data[0].tongdaXuLy}}</a></td>
                <td><a ></a></td>
                <td><a (click) ="GetDetailProcedure(item.id,item.coQuan,'0')">{{item.data[0].TongSoThuTuc}}</a></td>
                <td><a (click) ="GetDetailProcedure(item.id,item.coQuan,'5f5b2c4b4e1bd312a6f3ae24')">{{item.data[0].ThutucMotPhan}}</a></td>
                <td><a (click)="GetDetailDossier(item.id,item.coQuan,item.data[0].TrongKy,0,0,0,1,0,item.data[0].tiepNhanMotPhan)">{{item.data[0].tthcMotPhan}}</a></td>
                <td><a (click) ="GetDetailProcedure(item.id,item.coQuan,'5f5b2c564e1bd312a6f3ae25')">{{item.data[0].ThuTucToantrinh}}</a></td>
                <td><a (click)="GetDetailDossier(item.id,item.coQuan,item.data[0].TrongKy,0,0,0,0,1,item.data[0].tiepNhanToantrinh)">{{item.data[0].tthcToanTrinh}}</a></td>
                <td><a ></a></td>
                <td><a (click)="GetDetailDossier(item.id,item.coQuan,item.data[0].TrongKy,0,0,1,0,0,item.data[0].hoSoBuuChinh)">{{item.data[0].tthcBuuChinh}}</a></td>

              </tr>
              <tr >
                <td>Lỹ kế đến {{endDateCumulative | date: 'dd/MM/yyyy'}}</td>
                <td><a (click)="GetDetailDossier(item.id,item.coQuan,item.data[1].TrongKy,0,0,0,0,0,item.data[1].tiepNhanTrongKy)">{{item.data[1].tiepNhanTrongKy}}</a></td>
                <td><a (click)="GetDetailDossier(item.id,item.coQuan,item.data[1].TrongKy,0,1,0,0,0,item.data[1].tiepNhanTrucTiep)">{{item.data[1].tiepNhanTrucTiep}}</a></td>
                <td><a (click)="GetDetailDossier(item.id,item.coQuan,item.data[1].TrongKy,0,0,1,0,0,item.data[1].hoSoBuuChinh)">{{item.data[1].hoSoBuuChinh}}</a></td>
                <td><a (click)="GetDetailDossier(item.id,item.coQuan,item.data[1].TrongKy,0,0,0,1,0,item.data[1].tiepNhanMotPhan)">{{item.data[1].tiepNhanMotPhan}}</a></td>
                <td><a (click)="GetDetailDossier(item.id,item.coQuan,item.data[1].TrongKy,0,0,0,0,1,item.data[1].tiepNhanToantrinh)">{{item.data[1].tiepNhanToantrinh}}</a></td>
                <td><a (click)="GetDetailDossier(item.id,item.coQuan,item.data[1].TrongKy,0,0,0,0,0,item.data[1].tiepNhanTrongKy)">{{item.data[1].tiepNhanTrongKy}}</a></td>
                <td><a (click)="GetDetailDossier(item.id,item.coQuan,item.data[1].TrongKy,1,0,0,0,0,item.data[1].tongdaXuLy)">{{item.data[1].tongdaXuLy}}</a></td>
                <td><a (click)="GetDetailDossier(item.id,item.coQuan,item.data[1].TrongKy,1,1,0,0,0,item.data[1].dxltiepNhanTrucTiep)">{{item.data[1].dxltiepNhanTrucTiep}}</a></td>
                <td><a (click)="GetDetailDossier(item.id,item.coQuan,item.data[1].TrongKy,1,0,1,0,0,item.data[1].dxlhoSoBuuChinh)">{{item.data[1].dxlhoSoBuuChinh}}</a></td>
                <td><a (click)="GetDetailDossier(item.id,item.coQuan,item.data[1].TrongKy,1,0,0,2,2,item.data[1].dxltiepNhanTrucTuyen)">{{item.data[1].dxltiepNhanTrucTuyen}}</a></td>
                <td><a (click)="GetDetailDossier(item.id,item.coQuan,item.data[1].TrongKy,1,0,0,0,0,item.data[1].tongdaXuLy)">{{item.data[1].tongdaXuLy}}</a></td>
                <td><a ></a></td>
                <td><a (click) ="GetDetailProcedure(item.id,item.coQuan,'0')">{{item.data[1].TongSoThuTuc}}</a></td>
                <td><a (click) ="GetDetailProcedure(item.id,item.coQuan,'5f5b2c4b4e1bd312a6f3ae24')">{{item.data[1].ThutucMotPhan}}</a></td>
                <td><a (click)="GetDetailDossier(item.id,item.coQuan,item.data[1].TrongKy,0,0,0,1,0,item.data[1].tiepNhanMotPhan)">{{item.data[1].tthcMotPhan}}</a></td>
                <td><a (click) ="GetDetailProcedure(item.id,item.coQuan,'5f5b2c564e1bd312a6f3ae25')">{{item.data[1].ThuTucToantrinh}}</a></td>     
                <td><a (click)="GetDetailDossier(item.id,item.coQuan,item.data[1].TrongKy,0,0,0,0,1,item.data[1].tiepNhanToantrinh)">{{item.data[1].tthcToanTrinh}}</a></td>
                <td><a ></a></td>
                <td><a (click)="GetDetailDossier(item.id,item.coQuan,item.data[1].TrongKy,0,0,1,0,0,item.data[1].hoSoBuuChinh)">{{item.data[1].tthcBuuChinh}}</a></td>

              </tr>
          </ng-container>
          <tr *ngIf="listHoSoCapHuyen.length>0" class="sum">
              
            <td colspan="2" rowspan="2" >TỔNG THÀNH PHỐ, HUYỆN, THỊ XÃ</td>
            <td>Trong kỳ</td>
            <td>{{TongCapHuyen.tiepNhanTrongKy}}</td>
            <td>{{TongCapHuyen.tiepNhanTrucTiep}}</td>
            <td>{{TongCapHuyen.hoSoBuuChinh}}</td>
            <td>{{TongCapHuyen.tiepNhanMotPhan}}</td>
            <td>{{TongCapHuyen.tiepNhanToantrinh}}</td>
            <td>{{TongCapHuyen.tiepNhanTrongKy}}</td>
            <td>{{TongCapHuyen.tongdaXuLy}}</td>
            <td>{{TongCapHuyen.dxltiepNhanTrucTiep}}</td>
            <td>{{TongCapHuyen.dxlhoSoBuuChinh}}</td>
            <td>{{TongCapHuyen.dxltiepNhanTrucTuyen}}</td>
            <td>{{TongCapHuyen.tongdaXuLy}}</td>
            <td></td>
            <td>{{TongCapHuyen.TongSoThuTuc}}</td>
            <td>{{TongCapHuyen.ThutucMotPhan}}</td>
            <td>{{TongCapHuyen.tthcMotPhan}}</td>
            <td>{{TongCapHuyen.ThuTucToantrinh}}</td>
            <td>{{TongCapHuyen.tthcToanTrinh}}</td>
            <td></td>
            <td>{{TongCapHuyen.tthcBuuChinh}}</td>
            
          </tr>
          <tr *ngIf="listHoSoCapHuyen.length>0" class="sum">
              <td> Lỹ kế đến {{endDateCumulative | date: 'dd/MM/yyyy'}}</td>
              <td>{{TongCapHuyenLK.tiepNhanTrongKy}}</td>
              <td>{{TongCapHuyenLK.tiepNhanTrucTiep}}</td>
              <td>{{TongCapHuyenLK.hoSoBuuChinh}}</td>
              <td>{{TongCapHuyenLK.tiepNhanMotPhan}}</td>
              <td>{{TongCapHuyenLK.tiepNhanToantrinh}}</td>
              <td>{{TongCapHuyenLK.tiepNhanTrongKy}}</td>
              <td>{{TongCapHuyenLK.tongdaXuLy}}</td>
              <td>{{TongCapHuyenLK.dxltiepNhanTrucTiep}}</td>
              <td>{{TongCapHuyenLK.dxlhoSoBuuChinh}}</td>
              <td>{{TongCapHuyenLK.dxltiepNhanTrucTuyen}}</td>
              <td>{{TongCapHuyenLK.tongdaXuLy}}</td>
              <td></td>
              <td>{{TongCapHuyenLK.TongSoThuTuc}}</td>
              <td>{{TongCapHuyenLK.ThutucMotPhan}}</td>
              <td>{{TongCapHuyenLK.tthcMotPhan}}</td>
              <td>{{TongCapHuyenLK.ThuTucToantrinh}}</td>
              <td>{{TongCapHuyenLK.tthcToanTrinh}}</td>
              <td></td>
              <td>{{TongCapHuyenLK.tthcBuuChinh}}</td>

          </tr>

          </tbody>
        </table>

      </div>
    </div>
  </div>
</div>