import { Component, OnInit, Inject } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { FormGroup, Validators, FormControl } from '@angular/forms';
import { Router } from '@angular/router';
import { Observable, of, Subscription } from 'rxjs';
import { EnvService } from 'src/app/core/service/env.service';

@Component({
  selector: 'app-view-detail',
  templateUrl: './view-detail.component.html',
  styleUrls: ['./view-detail.component.scss']
})

export class LGSPHCMLLTPVNEIDLogDetailComponent implements OnInit {
  constructor(
    public dialogRef: MatDialogRef<LGSPHCMLLTPVNEIDLogDetailComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any,
    private envService: EnvService,
    private router: Router) {
  }
  ngOnInit() {
  }

  onDismiss(): void {
    this.dialogRef.close();
  }
}
