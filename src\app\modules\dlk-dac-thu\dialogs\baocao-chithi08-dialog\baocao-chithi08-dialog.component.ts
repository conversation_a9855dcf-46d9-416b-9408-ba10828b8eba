import { DatePipe } from '@angular/common';
import { Component, Inject, OnInit } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { MatTableDataSource } from '@angular/material/table';
import { DeploymentService } from 'data/service/deployment.service';
import { Workbook } from 'exceljs';
import * as fs from 'file-saver';
import { EnvService } from 'src/app/core/service/env.service';
import { DLKStatisticsService } from 'src/app/data/service/dlk-statistics/dlk-statistics.service';

export interface DossierStatisticDetail {
  id: string;
  no: number; // 1. STT
  dossierCode: string; // 2. <PERSON><PERSON> hồ sơ
  procedureName: string; // 3. Tên TTHC
  procedureLevel: string; // 4. Mức độ
  applicantOwnerFullName: string; // 5. Tên tổ chức, cá nhân
  Address: string; // 6.1 Đ<PERSON>a chỉ, 
  PhoneNumber: string; // 6.2 cá nhân
  // 7. <PERSON><PERSON> quan chủ trì giải quyết
  // Nhận và trả kết quả
  acceptedDate: string; // 8. Nhận hồ sơ
  appointmentDate: string; // 9. Hẹn trả kết quả
  completedDate: string; // 10. Ngày có kết quả/YC trả lại dân
  returnedDate: string; // 11. Ngày trả kết quả/trả lại dân
  ReceivingKind: string;  // 12. Hình thức nhận kết quả
  applyMethod: string;  // 13. Hình thức tiếp nhận
  agencyName: string;

  sectorName: string;  // Tên lĩnh vực
  noiDungYeuCauGiaiQuyet: string; // QNI
  applicantPhoneNumber: string; // Số điện thoại
  assigneeFullname: string; // Cán bộ xử lý hiện tại
  dossierStatusName: string; // Trạng thái
}
export interface ProcedureStatisticDetail {
  id: string;
  no: number;
  code: string; // Mã số hồ sơ
  sectorName: string;
  procedureName: string; // Tên thủ tục
  legalGrounds: string;
  Level: string;
  levelName: string;
  agencyLevel: string;
  agencyName: string; // Cơ quan chủ trì giải quyết
}

@Component({
  selector: 'app-baocao-chithi08-dialog',
  templateUrl: './baocao-chithi08-dialog.component.html',
  styleUrls: ['./baocao-chithi08-dialog.component.scss']
})
export class BaocaoChithi08DialogComponent implements OnInit {
  config = this.envService.getConfig();
  isAssignee = true;
  paginationType = this.deploymentService.env.statistics.paginationType;
  displayedColumnsDefault: string[] = ['no', 'dossierCode', 'procedureName', 'procedureLevel', 'applicantOwnerFullName', 'applicantPhoneNumber', 'agencyName', 
    'agencyAccepterName','acceptedDate', 'appointmentDate', 'completedDate', 'returnedDate', 'ReceivingKind', 'applyMethod'];
  displayedColumnsProdure: string[] = ['no', 'code', 'procedureName', 'sectorName', 'agencyName', 'agencyLevel', 'levelName'];
  // tslint:disable-next-line:max-line-lengt+h
  displayedColumns: string[] = this.data.type ? this.displayedColumnsProdure : this.displayedColumnsDefault;
  dataSource: MatTableDataSource<DossierStatisticDetail>;
  procedureDataSource: MatTableDataSource<ProcedureStatisticDetail>;
  ELEMENTDATA: DossierStatisticDetail[] = [];
  PROCEDUREDATA: ProcedureStatisticDetail[] = [];
  isWaitingData = false;

  size = 20;
  page = 1;
  totalPage = 0;
  pageIndex = 1;
  countResult = 0;
  pgSizeOptions = this.config.pageSizeOptions;

  dataExport = [];
  isExportExcel = false;
  EXCEL_EXTENSION = '.xlsx';
  EXCEL_TYPE = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8';

  constructor(public dialogRef: MatDialogRef<BaocaoChithi08DialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any,
    private datePipe: DatePipe,
    private envService: EnvService,
    private dlkStatisticService: DLKStatisticsService,
    private deploymentService: DeploymentService,
  ) {
    this.dataSource = new MatTableDataSource(this.ELEMENTDATA);
    this.procedureDataSource = new MatTableDataSource(this.PROCEDUREDATA);
  }

  ngOnInit(): void {
    this.getData();
  }

  getData() {
    this.ELEMENTDATA = [];
    this.PROCEDUREDATA = [];
    this.dataSource.data = [];
    this.procedureDataSource.data = [];
    this.isWaitingData = true;
    let tuNgay = this.datePipe.transform(this.data.tuNgay, 'yyyy-MM-dd') + 'T00:00:00.000Z';
    let denNgay = this.datePipe.transform(this.data.denNgay, 'yyyy-MM-dd') + 'T23:59:59.999Z';

    this.dlkStatisticService.getDataDLKCT08Detail(this.data.prid, this.data.id, this.data.prop, this.data.type, 
    // this.dlkStatisticService.getDataBaocaoDetail(this.data.prid, this.data.id, this.data.prop, this.data.type, 
      this.data.level, tuNgay, denNgay, this.page - 1, this.size).subscribe(
      (res) => {
        if (!this.data.type) {
          this.ELEMENTDATA = res?.dossier;
          this.dataSource.data = this.ELEMENTDATA;
          this.countResult = res?.totalRow[0]?.value;
        } else {
          this.PROCEDUREDATA = res?.procedures;
          this.procedureDataSource.data = this.PROCEDUREDATA;
          this.countResult = res?.totalRow[0]?.value;
        }

        this.totalPage = Math.floor(this.countResult / this.size);
        // this.dataSource.data = [];
        if ((this.countResult % this.size) > 0) {
          this.totalPage++;
        }
        this.isWaitingData = false;
      }, () => {
        this.isWaitingData = false;
      }
    );
  }

  paginate(event: any, type) {
    switch (type) {
      case 0:
        this.pageIndex = event;
        this.page = event;
        this.getData();
        break;
      case 1:
        this.pageIndex = 1;
        this.page = 1;
        this.getData();
        break;
    }
  }

  exportExcel() {
    this.getDossierExport();
  }

  getDossierExport() {
    let tuNgay = this.datePipe.transform(this.data.tuNgay, 'yyyy-MM-dd') + 'T00:00:00.000Z';
    let denNgay = this.datePipe.transform(this.data.denNgay, 'yyyy-MM-dd') + 'T23:59:59.999Z';

      let allData = [];
      let currentPage = 0;
      let currentSize = 10000;

      const fetchPage = () => {
        this.dlkStatisticService.getDataDLKCT08Detail(this.data.prid, this.data.id, this.data.prop, 
        // this.dlkStatisticService.getDataBaocaoDetail(this.data.prid, this.data.id, this.data.prop, 
          this.data.type, this.data.level, tuNgay, denNgay, currentPage, currentSize)
          .subscribe(data => {
            const totalRows = data.totalRow[0].value;

            if(this.data?.type) {
              allData = allData.concat(data?.procedures);
            } else {
              allData = allData.concat(data?.dossier);
            }

            if (allData.length < totalRows) {
              currentPage++;
              fetchPage();
            } else {
              this.dataExport = allData;
              this.exportToExcel()
            }
          }, err => {
            console.log(err);
          });
      };
    
      // Bắt đầu lấy dữ liệu từ trang đầu tiên
      fetchPage();
  }

  getFullName(data) {
    if (data != null) {
      if (data?.applicantOwnerFullName != undefined && data?.applicantOwnerFullName != null && data?.applicantOwnerFullName != "") {
        return data.applicantOwnerFullName;
      } else if (data?.applicantFullName != undefined && data?.applicantFullName != null && data?.applicantFullName != "") {
        return data.applicantFullName;
      } else if (data?.applicantFullName1 != undefined && data?.applicantFullName1 != null && data?.applicantFullName1 != "") {
        return data.applicantFullName1;
      } else {
        return "";
      }
    }
  }

  getAgencyName(data) {
    return data?.split(',')[0];
  }

  getCompletedDate(item) {
    let dateStr = "";
    const completedDate = item?.completedDate;
    const withdrawDate = item?.withdrawDate;

    if (completedDate != null && completedDate?.toString().trim() != "") {
      dateStr = this.datePipe.transform(completedDate, 'dd/MM/yyyy HH:mm:ss');
    } else if(withdrawDate != null && withdrawDate?.toString().trim() != "") {
      dateStr = this.datePipe.transform(withdrawDate, 'dd/MM/yyyy HH:mm:ss') + " ";
    }

    return dateStr;
  }

  async exportToExcel() {
    this.isExportExcel = false;
    let excelFileName = "";
    const workbook = new Workbook();

    if (!this.data.type) {
      // XUẤT BÁO CÁO HỒ SƠ
      const from = this.datePipe.transform(this.data.tuNgay, 'dd/MM/yyyy');
      const to = this.datePipe.transform(this.data.denNgay, 'dd/MM/yyyy');
      const newDate = this.datePipe.transform(new Date(), "dd/MM/yyyy HH:mm:ss")
      excelFileName = `Bao_cao_chi_tiet_theo_thu_tuc_${newDate}`;
      let headerXLS = {
        row1: "CỘNG HÒA XÃ HỘI CHỦ NGHĨA VIỆT NAM",
        row2: "Độc lập - Tự do - Hạnh phúc",
        row3: "BÁO CÁO TÌNH HÌNH TIẾP NHẬN VÀ GIẢI QUYẾT HỒ SƠ",
        row4: `(Từ ${from} đến ngày ${to})`,
        row5: `Thống kê vào lúc ${newDate}`
      }

      let footerXLS = {
        col1: "NGƯỜI LẬP",
        col2: "NGƯỜI BÁO CÁO"
      }
      workbook.creator = 'Snippet Coder';
      workbook.lastModifiedBy = 'SnippetCoder';
      workbook.created = new Date();
      workbook.modified = new Date();
      const worksheet = workbook.addWorksheet("sheet1");

      // Add header row
      worksheet.addRow([]);
      worksheet.mergeCells('A1:K1');
      worksheet.getCell('A1').value = headerXLS.row1;
      worksheet.getCell('A1').alignment = { horizontal: 'center', vertical: 'middle' };
      worksheet.getCell('A1').font = { size: 13, name: 'Times New Roman' };

      worksheet.mergeCells('A2:K2');
      worksheet.getCell('A2').value = headerXLS.row2;
      worksheet.getCell('A2').alignment = { horizontal: 'center', vertical: 'middle' };
      worksheet.getCell('A2').font = { size: 13, underline: true, name: 'Times New Roman' };

      worksheet.mergeCells('A4:K4');
      worksheet.getCell('A4').value = headerXLS.row3;
      worksheet.getCell('A4').alignment = { horizontal: 'center', vertical: 'middle' };
      worksheet.getCell('A4').font = { size: 13, bold: true, name: 'Times New Roman' };

      worksheet.mergeCells('A5:K5');
      worksheet.getCell('A5').value = headerXLS.row4;
      worksheet.getCell('A5').alignment = { horizontal: 'center', vertical: 'middle' };
      worksheet.getCell('A5').font = { size: 11, italic: true, name: 'Times New Roman' };

      worksheet.mergeCells('H6:K6');
      worksheet.getCell('H6').value = headerXLS.row5;
      worksheet.getCell('H6').alignment = { horizontal: 'center', vertical: 'middle' };
      worksheet.getCell('H6').font = { size: 11, italic: true, name: 'Times New Roman' };

      const headerContent = ['STT', 'Số hồ sơ', 'Tên thủ tục hành chính', 'Mức độ', 'Tổ chức, cá nhân', 'Địa chỉ, SĐT', '	Cơ quan chủ trì', 'Cơ quan tiếp nhận',
        'Ngày tiếp nhận', 'Ngày hẹn trả', 'Ngày có kết quả/yc trả lại dân', 'Ngày trả kết quả/trả lại dân', 'Hình thức nhận KQ', 'Hình thức tiếp nhận']
      const rowStartHeaderContent = 8;
      for (let index = 0; index < 14; index++) {
        worksheet.getCell(rowStartHeaderContent, (index + 1)).value = headerContent[index];
        worksheet.getCell(rowStartHeaderContent, (index + 1)).alignment = { horizontal: 'center', vertical: 'middle', };
        worksheet.getCell(rowStartHeaderContent, (index + 1)).font = { size: 11, bold: true, name: 'Times New Roman' };
        worksheet.getCell(rowStartHeaderContent, (index + 1)).border = { top: { style: 'thin' }, left: { style: 'thin' }, bottom: { style: 'thin' }, right: { style: 'thin' } };
      }

      let rowStartContent = rowStartHeaderContent + 1;
      const data = this.dataExport;
      for (let r = 0; r < data.length; r++) {
        worksheet.getCell(rowStartContent + r, 1).value = r + 1;
        worksheet.getCell(rowStartContent + r, 2).value = data[r].dossierCode;
        worksheet.getCell(rowStartContent + r, 3).value = data[r].procedureName;
        worksheet.getCell(rowStartContent + r, 4).value = data[r].procedureLevel;
        worksheet.getCell(rowStartContent + r, 5).value = this.getFullName(data[r]);
        worksheet.getCell(rowStartContent + r, 6).value = (data[r]?.PhoneNumber ?? "") + " " + (data[r]?.Address ?? "");
        worksheet.getCell(rowStartContent + r, 7).value = this.data.agencyName;
        worksheet.getCell(rowStartContent + r, 8).value = data[r]?.agencyName;
        worksheet.getCell(rowStartContent + r, 9).value = this.datePipe.transform(data[r].acceptedDate, "dd/MM/yyyy HH:mm:ss");
        worksheet.getCell(rowStartContent + r, 10).value = this.datePipe.transform(data[r].appointmentDate, "dd/MM/yyyy HH:mm:ss");
        worksheet.getCell(rowStartContent + r, 11).value = this.getCompletedDate(data[r]);
        worksheet.getCell(rowStartContent + r, 12).value = this.datePipe.transform(data[r].returnedDate, "dd/MM/yyyy HH:mm:ss");
        worksheet.getCell(rowStartContent + r, 13).value = data[r].ReceivingKind;
        worksheet.getCell(rowStartContent + r, 14).value = data[r].applyMethod;
        for (let c = 0; c < 14; c++) {
          worksheet.getCell(rowStartContent + r, (c + 1)).alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
          worksheet.getCell(rowStartContent + r, (c + 1)).font = { size: 11, name: 'Times New Roman' };
          worksheet.getCell(rowStartContent + r, (c + 1)).border = { top: { style: 'thin' }, left: { style: 'thin' }, bottom: { style: 'thin' }, right: { style: 'thin' } };
        }
      }

      let rowEndContent = rowStartContent + data.length;
      let rowFooter = rowEndContent + 2;

      worksheet.getCell(rowFooter, 2).value = footerXLS.col1;
      worksheet.getCell(rowFooter, 2).font = { size: 13, bold: true, name: 'Times New Roman' };

      worksheet.mergeCells("H" + rowFooter, "K" + rowFooter);
      worksheet.getCell(rowFooter, 8).value = footerXLS.col2;
      worksheet.getCell(rowFooter, 8).font = { size: 13, bold: true, name: 'Times New Roman' };

      worksheet.getColumn(1).width = 7;
      worksheet.getColumn(1).alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
      worksheet.getColumn(2).width = 20;
      worksheet.getColumn(2).alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
      worksheet.getColumn(3).width = 40;
      worksheet.getColumn(3).alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
      worksheet.getColumn(4).width = 20;
      worksheet.getColumn(4).alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
      worksheet.getColumn(5).width = 20;
      worksheet.getColumn(5).alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
      worksheet.getColumn(6).width = 25;
      worksheet.getColumn(6).alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
      worksheet.getColumn(7).width = 20;
      worksheet.getColumn(7).alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
      worksheet.getColumn(8).width = 20;
      worksheet.getColumn(8).alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
      worksheet.getColumn(9).width = 20;
      worksheet.getColumn(9).alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
      worksheet.getColumn(10).width = 17;
      worksheet.getColumn(10).alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
      worksheet.getColumn(11).width = 17;
      worksheet.getColumn(11).alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
      worksheet.getColumn(12).width = 17;
      worksheet.getColumn(12).alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
      worksheet.getColumn(13).width = 15;
      worksheet.getColumn(13).alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
      worksheet.getColumn(14).width = 15;
      worksheet.getColumn(14).alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
    }
    else { // XUẤT BÁO CAO THỦ TỤC
      const newDate = this.datePipe.transform(new Date(), "dd/MM/yyyy HH:mm:ss")
      excelFileName = `Bao_cao_chi_tiet_theo_thu_tuc_${newDate}`;
      let headerXLS = {
        row1: "CỘNG HÒA XÃ HỘI CHỦ NGHĨA VIỆT NAM",
        row2: "Độc lập - Tự do - Hạnh phúc",
        row3: "DANH SÁCH CHI TIẾT THỦ TỤC HÀNH CHÍNH",
        row4: "",
        row5: `Thống kê vào lúc ${newDate}`
      }

      let footerXLS = {
        col1: "NGƯỜI LẬP",
        col2: "NGƯỜI BÁO CÁO"
      }

      workbook.creator = 'Snippet Coder';
      workbook.lastModifiedBy = 'SnippetCoder';
      workbook.created = new Date();
      workbook.modified = new Date();
      const worksheet = workbook.addWorksheet("sheet1");

      // Add header row
      worksheet.addRow([]);
      worksheet.mergeCells('A1:G1');
      worksheet.getCell('A1').value = headerXLS.row1;
      worksheet.getCell('A1').alignment = { horizontal: 'center', vertical: 'middle' };
      worksheet.getCell('A1').font = { size: 13, name: 'Times New Roman' };

      worksheet.mergeCells('A2:G2');
      worksheet.getCell('A2').value = headerXLS.row2;
      worksheet.getCell('A2').alignment = { horizontal: 'center', vertical: 'middle' };
      worksheet.getCell('A2').font = { size: 13, underline: true, name: 'Times New Roman' };

      worksheet.mergeCells('A4:G4');
      worksheet.getCell('A4').value = headerXLS.row3;
      worksheet.getCell('A4').alignment = { horizontal: 'center', vertical: 'middle' };
      worksheet.getCell('A4').font = { size: 13, bold: true, name: 'Times New Roman' };

      worksheet.mergeCells('A5:G5');
      worksheet.getCell('A5').value = headerXLS.row4;
      worksheet.getCell('A5').alignment = { horizontal: 'center', vertical: 'middle' };
      worksheet.getCell('A5').font = { size: 11, italic: true, name: 'Times New Roman' };

      worksheet.mergeCells('E6:G6');
      worksheet.getCell('E6').value = headerXLS.row5;
      worksheet.getCell('E6').alignment = { horizontal: 'center', vertical: 'middle' };
      worksheet.getCell('E6').font = { size: 11, italic: true, name: 'Times New Roman' };

      // const headerContent = ['STT', 'Tên tắt', 'Lĩnh vực thủ tục', 'Cơ quan', 'Thủ tục', 'Lệ phí', '	Quyết định', 'Cấp thủ tục', 'Mức độ'];
      const headerContent = ['STT', 'Tên tắt', 'Thủ tục', 'Lĩnh vực thủ tục', 'Cơ quan', 'Cấp thủ tục', 'Mức độ'];
      const rowStartHeaderContent = 8;
      for (let index = 0; index < 7; index++) {
        worksheet.getCell(rowStartHeaderContent, (index + 1)).value = headerContent[index];
        worksheet.getCell(rowStartHeaderContent, (index + 1)).alignment = { horizontal: 'center', vertical: 'middle', };
        worksheet.getCell(rowStartHeaderContent, (index + 1)).font = { size: 11, bold: true, name: 'Times New Roman' };
        worksheet.getCell(rowStartHeaderContent, (index + 1)).border = { top: { style: 'thin' }, left: { style: 'thin' }, bottom: { style: 'thin' }, right: { style: 'thin' } };
      }

      let rowStartContent = rowStartHeaderContent + 1;
      const data = this.dataExport;
      for (let r = 0; r < data.length; r++) {
        worksheet.getCell(rowStartContent + r, 1).value = r + 1;
        worksheet.getCell(rowStartContent + r, 2).value = data[r].code;
        worksheet.getCell(rowStartContent + r, 3).value = data[r].procedureName;
        worksheet.getCell(rowStartContent + r, 4).value = data[r].sectorName;
        worksheet.getCell(rowStartContent + r, 5).value = this.getAgencyName(data[r].agencyName);
        // worksheet.getCell(rowStartContent + r, 6).value = data[r].cost ?? "Không";
        // worksheet.getCell(rowStartContent + r, 7).value = data[r].legalGrounds;
        worksheet.getCell(rowStartContent + r, 6).value = this.getAgencyName(data[r].agencyLevel);
        worksheet.getCell(rowStartContent + r, 7).value = data[r].levelName;
        for (let c = 0; c < 7; c++) {
          worksheet.getCell(rowStartContent + r, (c + 1)).alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
          worksheet.getCell(rowStartContent + r, (c + 1)).font = { size: 11, name: 'Times New Roman' };
          worksheet.getCell(rowStartContent + r, (c + 1)).border = { top: { style: 'thin' }, left: { style: 'thin' }, bottom: { style: 'thin' }, right: { style: 'thin' } };
        }
      }

      let rowEndContent = rowStartContent + data.length;
      let rowFooter = rowEndContent + 2;

      worksheet.getCell(rowFooter, 2).value = footerXLS.col1;
      worksheet.getCell(rowFooter, 2).font = {size: 13, bold: true, name: 'Times New Roman'};
      worksheet.mergeCells("F" + rowFooter, "G" + rowFooter);
      worksheet.getCell(rowFooter, 6).value = footerXLS.col2;
      worksheet.getCell(rowFooter, 6).font = {size: 13, bold: true, name: 'Times New Roman'};
      worksheet.getColumn(1).width = 7;
      worksheet.getColumn(1).alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
      worksheet.getColumn(2).width = 20;
      worksheet.getColumn(2).alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
      worksheet.getColumn(3).width = 40;
      worksheet.getColumn(3).alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
      worksheet.getColumn(4).width = 20;
      worksheet.getColumn(4).alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
      worksheet.getColumn(5).width = 40;
      worksheet.getColumn(5).alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
      worksheet.getColumn(6).width = 20;
      worksheet.getColumn(6).alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
      worksheet.getColumn(7).width = 20;
      worksheet.getColumn(7).alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
    }

    // Save Excel File
    workbook.xlsx.writeBuffer().then((dataBuffer: ArrayBuffer) => {
      const blob = new Blob([dataBuffer], { type: this.EXCEL_TYPE });
      fs.saveAs(blob, excelFileName + this.EXCEL_EXTENSION);
    });
  }

  onClose() {
    this.dialogRef.close();
  }
}