import { DatePipe } from '@angular/common';
import { AfterViewInit, ChangeDetectorRef, Component, OnInit, ViewChild } from '@angular/core';
import { FormControl, FormGroup } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import { MatSelect } from '@angular/material/select';
import { MatTableDataSource } from '@angular/material/table';
import { MatTabChangeEvent } from '@angular/material/tabs';
import { Router } from '@angular/router';
import { KeycloakService } from 'keycloak-angular';
import { ReplaySubject, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { EnvService } from 'src/app/core/service/env.service';
import { DeploymentService } from 'src/app/data/service/deployment.service';
import { MainService } from 'src/app/data/service/main/main.service';
import { ProcedureService } from 'src/app/data/service/procedure/procedure.service';
import { ReportService } from 'src/app/data/service/report/report.service';
import { SnackbarService } from 'src/app/data/service/snackbar/snackbar.service';
import { ComboboxLazyLoadComponent } from 'src/app/shared/components/combobox-lazy-load/combobox-lazy-load.component';
import { FormElement } from '../statistic-01-2018/statistic-01-2018.component';
import * as tUtils from 'src/app/data/service/thoai.service';
import { DossierService } from 'src/app/data/service/dossier/dossier.service';
import { StatisticsService } from 'data/service/statistics/statistics.service';
import { EReceiptService } from 'src/app/data/service/invoice-receipt/ereceipt.service';
import { UserService } from 'src/app/data/service/user.service';
@Component({
  selector: 'app-dossier-fee-hcm',
  templateUrl: './dossier-fee-hcm.component.html',
  styleUrls: ['./dossier-fee-hcm.component.scss']
})
export class DossierFeeHcmComponent implements OnInit, AfterViewInit {
  @ViewChild('searchAgencyType') searchAgencyType: ComboboxLazyLoadComponent;
  @ViewChild('searchAgency') searchAgency: ComboboxLazyLoadComponent;
  @ViewChild('searchSector') searchSector: ComboboxLazyLoadComponent;

  nowDate = tUtils.newDate();
  tabLoadTimes: any;
  typeAgency = [];
  procedure = [];
  agency = [];
  sector = [];
  agencyLevel = [];
  tag = 1;
  ELEMENTDATA: FormElement[] = [];
  dataSource: MatTableDataSource<FormElement>;
  ELEMENTDATA1: FormElement[] = [];
  dataSource1: MatTableDataSource<FormElement>;
  ELEMENTDATA2: FormElement[] = [];
  dataSource2: MatTableDataSource<FormElement>;
  ELEMENTDATA3: FormElement[] = [];
  dataSource3: MatTableDataSource<FormElement>;
  size = 10;
  pageIndex = 1;
  page = 1;
  countResult = 0;
  config = this.envService.getConfig();
  pgSizeOptions = this.config.pageSizeOptions;
  size1 = 10;
  pageIndex1 = 1;
  page1 = 1;
  countResult1 = 0;
  size2 = 10;
  pageIndex2 = 1;
  page2 = 1;
  countResult2 = 0;
  size3 = 10;
  pageIndex3 = 1;
  page3 = 1;
  countResult3 = 0;
  selectedLang: string;

  sectorId: any = '';
  sectorVal: any;
  typeAgencyVal: any;
  agencyLevelVal: any;
  agencyVal: any;
  procedureVal: any;

  label01 = '';
  label02 = '';
  label03 = '';
  label04 = '';
  pageTitle = {
    vi: `Thống kê lệ phí hồ sơ`,
    en: `Dossier fee statistics`
  };
  isAddAgencyRoot = false; // check điều kiện bổ sung agency parent
  arrData: any = [];
  ////
  timeout: any = null;
  isAdmin?: boolean = sessionStorage.getItem('oneGateAdminMaster') === '1';
  userAgency?: any = JSON.parse(localStorage.getItem('userAgency'));
  getAgencyIdForProcedure: any = '';
  getAgencyName: string;
  // Sector infinity scroll with search
  private listSector: any[] = [];
  listSectorPage = 0;
  isFullListSector = false;
  searchSectorKeyword = '';
  sectorCtrl: FormControl = new FormControl();
  protected sectors: any[] = this.listSector;
  @ViewChild('sectorMatSelectInfiniteScroll', { static: true }) sectorMatSelectInfiniteScroll: MatSelect;
  @ViewChild('tabGroup') tabGroup;
  selectedLangId = Number(localStorage.getItem('languageId')) || 228;
  parentAgency = '';
  agencyId = '';
  listAgencySearch: any;
  // Procedure infinity scroll with search
  //
  isShowComboboxNewTemplate = false;
  listAgencyUseNewTemplateStatisticReceipt = this.deploymentService.env.OS_HCM?.listAgencyUseNewTemplateStatisticReceipt ? this.deploymentService.env.OS_HCM?.listAgencyUseNewTemplateStatisticReceipt : [];
  private listProcedure: any[] = [];
  listProcedurePage = 0;
  isFullListProcedure = false;
  searchProcedureKeyword = '';
  procedureCtrl: FormControl = new FormControl();
  searchProcedureCtrl: FormControl = new FormControl();
  protected procedures: any[] = this.listProcedure;
  public procedureFiltered: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  @ViewChild('procedureMatSelectInfiniteScroll', { static: true }) procedureMatSelectInfiniteScroll: MatSelect;

  dossierFeeOnlyAgencyIdHCM = this.deploymentService.env?.OS_HCM?.dossierFeeOnlyAgencyIdHCM ? this.deploymentService.env.OS_HCM.dossierFeeOnlyAgencyIdHCM : false;
  administrativeAgencyTag = this.deploymentService.env?.statistics?.administrativeAgencyTag ? this.deploymentService.env?.statistics?.administrativeAgencyTag : [];
  showStatisticsPaymentDossierFeeHcm = this.deploymentService.env?.OS_HCM?.showStatisticsPaymentDossierFeeHcm;
  isShowExcelDossierFeeHCM = this.deploymentService.env?.OS_HCM?.isShowExcelDossierFeeHCM;

  administrativeAgency = {
    loaded: false,
    id: '',
  }
  protected onDestroy = new Subject<void>();
  RIBook: any;
  searchForm = new FormGroup({
    fromDate: new FormControl(''),
    toDate: new FormControl(''),
    typeAgency: new FormControl(''),
    agencyLevel: new FormControl(''),
    agency: new FormControl(''),
    sectorCtrl: new FormControl(''),
    searchSectorCtrl: new FormControl(),
    procedureCtrl: new FormControl(''),
    searchProcedureCtrl: new FormControl(),
    bookName: new FormControl(''),
    invoicePattern: new FormControl(''),
    invoiceSerial: new FormControl('')
  });
  displayedColumns: string[] = ['stt', 'code', 'name', 'receiptNumber', 'address', 'applyMethod', 'accepted', 'paid', 'sum', 'procedure', 'sector'];
  displayedColumns1: string[] = ['stt1', 'procedure1', 'dossiers1', 'sum1'];
  displayedColumns2: string[] = ['stt2', 'sector2', 'dossiers2', 'sum2'];
  displayedColumns3: string[] = ['stt3', 'agency3', 'dossiers3', 'sum3'];
  env: any = this.deploymentService.getAppDeployment()?.env;
  subsystemWebOneGateId = !!this.config?.subsystemWebOneGateId ? this.config?.subsystemWebOneGateId : this.env?.subsystemWebOneGateId;
  exportExcelData = [];
  implimentElasticSearch = false;
  citizenPaymentMethod = this.deploymentService.env.OS_HCM.citizenPaymentMethod;

  hideFeeStatistic = this.deploymentService.env.OS_HCM.hideFeeStatistic.enable;
  noticeHideFeeStatistic = this.deploymentService.env.OS_HCM.hideFeeStatistic.notice;

  constructor(
    private reportService: ReportService,
    private dialog: MatDialog,
    private envService: EnvService,
    private router: Router,
    private procedureService: ProcedureService,
    private keycloak: KeycloakService,
    private mainService: MainService,
    private cdRef: ChangeDetectorRef,
    private snackbarService: SnackbarService,
    private deploymentService: DeploymentService,
    private datePipe: DatePipe,
    private dossierService: DossierService,
    private statisticsService: StatisticsService,
    private eReceiptService: EReceiptService,
    private userService: UserService,
  ) {
    this.dataSource = new MatTableDataSource(this.ELEMENTDATA);
    this.dataSource1 = new MatTableDataSource(this.ELEMENTDATA1);
    this.dataSource2 = new MatTableDataSource(this.ELEMENTDATA2);
    this.dataSource3 = new MatTableDataSource(this.ELEMENTDATA3);

    if (this.hideFeeStatistic) {
      if (this.deploymentService.env.OS_HCM.hideFeeStatistic.allowedMenu.includes('statistics/dossier-fee-hcm')) {
        this.hideFeeStatistic = true;
      } else {
        this.hideFeeStatistic = false;
      }
    }
  }

  async ngOnInit(): Promise<void> {
    if (this.hideFeeStatistic) {
      return;
    }
    const userAgency = JSON.parse(localStorage.getItem('userAgency'));
    // if (this.listAgencyUseNewTemplateStatisticReceipt.filter(item => item == userAgency.id || item == userAgency.parent.id || item == userAgency.ancestors.id).length > 0) {
    //   this.isShowComboboxNewTemplate = true;
    //   console.log("đã phân quyền");
    //   await this.getUserExperience();
    // }
    const permissions = this.userService.getUserPermissions();
    for (const p of permissions) {
      if (p.permission.code === 'oneGateHCMDossierFeeReportElastic') {
        this.implimentElasticSearch = true;
        break;
      }
    }
    if (this.listAgencyUseNewTemplateStatisticReceipt.length > 0){
      this.listAgencyUseNewTemplateStatisticReceipt.filter(async element => {
        if((userAgency != null && tUtils.nonNull(userAgency,'id') && element == userAgency.id )
          || (userAgency != null && tUtils.nonNull(userAgency,'parent') && tUtils.nonNull(userAgency.parent,'id') && element == userAgency.parent.id) 
          || (userAgency != null && tUtils.nonNull(userAgency,'ancestors') && tUtils.nonNull(userAgency.ancestors,'id') && element == userAgency.ancestors.id)){
          
            this.isShowComboboxNewTemplate = true;
            console.log("đã phân quyền");
            await this.getUserExperience();
        }

        
      });
	  }



    if (this.showStatisticsPaymentDossierFeeHcm && this.showStatisticsPaymentDossierFeeHcm.enable) {
      this.displayedColumns = ['stt', 'code', 'name', 'receiptNumber', 'address', 'applyMethod', 'accepted', 'paymentMethod', 'paid', 'sum', 'procedure', 'sector'];
      if (this.isShowComboboxNewTemplate) {
        this.displayedColumns = ['stt', 'code', 'name', 'receiptNumber', 'invoicePattern', 'invoiceSerial', 'address', 'applyMethod', 'receiptStatus', 'accepted', 'paymentMethod', 'paid', 'sum', 'procedure', 'sector'];
      }
    } else {
      if (this.isShowComboboxNewTemplate) {
        this.displayedColumns = ['stt', 'code', 'name', 'receiptNumber', 'invoicePattern', 'invoiceSerial', 'address', 'applyMethod', 'receiptStatus', 'accepted', 'paid', 'sum', 'procedure', 'sector'];
      }
    }
    const d = tUtils.newDate();
    const from = d.getFullYear() + '-' + ('0' + (d.getMonth() + 1)).slice(-2) + '-' + '01';
    const to = d.getFullYear() + '-' + ('0' + (d.getMonth() + 1)).slice(-2) + '-' + ('0' + d.getDate()).slice(-2);
    this.searchForm.patchValue({
      fromDate: from,
      toDate: to
    });

    this.mainService.setPageTitle(this.pageTitle[localStorage.getItem('language')]);

    if (localStorage.getItem('language') === 'vi') {
      this.label01 = 'Theo hồ sơ';
      this.label02 = 'Theo thủ tục';
      this.label03 = 'Theo lĩnh vực';
      this.label04 = 'Theo cơ quan/đơn vị';
    } else if (localStorage.getItem('language') === 'en') {
      this.label01 = 'According to the dossier';
      this.label02 = 'According to the procedure';
      this.label03 = 'According to the sector';
      this.label04 = 'According to the agency';
    }
    await this.getAdministrativeAgencyByLoggedInUser();
    this.getListAgencyLevel();
    this.getListProcedure();

    if (this.userAgency !== null && this.userAgency !== undefined) {
      this.agencyId = this.isAdmin ? '' : this.userAgency.id;
      if (!!this.userAgency.parent && !!this.userAgency.parent.id && !this.isAdmin) {
        this.parentAgency = this.userAgency.parent.id;
      } else if (this.userAgency.id !== this.config.rootAgency.id || this.isAdmin) {
        this.parentAgency = this.userAgency.id;
      }
    }
  // Bỏ chạy thống kê ngày khi vừa truy cập vào menu
  //   if(this.implimentElasticSearch){
  //     this.getListDossierFee({
  //       page: 0,
  //       size: this.size,
  //       spec: 'page',
  //       'start-date': new Date(from).toISOString(),
  //       'end-date': new Date(to).toISOString(),
  //       'deployment-id': this.config.deploymentId,
  //       'agency-id': this.agencyId,
  //       'agency-parent-id': this.parentAgency,
  //       'isHCM': true,
  //       "citizen-payment-method": this.citizenPaymentMethod,
  //     });
  //   } else {
  //   this.getReportForDossier({
  //     page: 0,
  //     size: this.size,
  //     spec: 'page',
  //     'start-date': new Date(from).toISOString(),
  //     'end-date': new Date(to).toISOString(),
  //     'deployment-id': this.config.deploymentId,
  //     'agency-id': this.agencyId,
  //     'agency-parent-id': this.parentAgency,
  //     'isHCM': true,
  //     "citizen-payment-method": this.citizenPaymentMethod,
  //   });
  // }

  }

  onConfirm() {
    const formObj = this.searchForm.getRawValue();
    let toDate = new Date(formObj.toDate);
    toDate.setHours(23);
    toDate.setMinutes(59);
    toDate.setSeconds(59);
    toDate.setMilliseconds(165);

    if (formObj.toDate === '' || formObj.fromDate === '') {
      const message = {
        vi: 'Ngày thống kê không được để trống!',
        en: 'Statistical date cannot be blank!'
      };
      this.snackbarService.openSnackBar(0, '', message[localStorage.getItem('language')], 'error_notification', 3000);
      return;
    } else if (toDate < new Date(formObj.fromDate)) {
      const message = {
        vi: 'Ngày bắt đầu phải nhỏ hơn hoặc bằng ngày kết thúc!',
        en: 'Start date must be less than or equal to end date!'
      };
      this.snackbarService.openSnackBar(0, '', message[localStorage.getItem('language')], 'error_notification', 3000);
      return;
    }
    const fromDate = new Date(formObj.fromDate);
    const endDate = new Date(formObj.toDate);
    if(this.implimentElasticSearch){
      const form = this.tabGroup.selecItedndex ? this.tabGroup.selectedIndex : 0;
      this.getReportForDossierElastic(form);
    } else {
    this.getReportForDossier({
      page: 0,
      size: this.size,
      spec: 'page',
      'start-date': this.datePipe.transform(formObj.fromDate, 'yyyy-MM-dd') + 'T00:00:00.000Z',
      'end-date': this.datePipe.transform(formObj.toDate, 'yyyy-MM-dd') + 'T23:59:59.999Z',
      'sector-id': this.searchSector.value[0],
      'procedure-id': formObj.procedureCtrl,
      'agency-type-id': this.searchAgencyType.value[0],
      'agency-level-id': formObj.agencyLevel,
      'deployment-id': this.config.deploymentId,
      'agency-id': this.searchAgency.value[0],
      'agency-parent-id': this.parentAgency,
      'isHCM': true,
      "citizen-payment-method": this.citizenPaymentMethod
    });
  }
  }

  getReportForDossierElastic(form){
    const formObj = this.searchForm.getRawValue();
    if(form === 0){
      this.pageIndex = 1;
      this.page = 1;
      this.getListDossierFee({
        page: 0,
        size: this.size,
        spec: 'page',
        'start-date': this.datePipe.transform(formObj.fromDate, 'yyyy-MM-dd') + 'T00:00:00.000Z',
        'end-date': this.datePipe.transform(formObj.toDate, 'yyyy-MM-dd') + 'T23:59:59.999Z',
        'sector-id': this.searchSector.value[0],
        'procedure-id': formObj.procedureCtrl,
        'agency-type-id': this.searchAgencyType.value[0],
        'agency-level-id': formObj.agencyLevel,
        'deployment-id': this.config.deploymentId,
        'agency-id': this.searchAgency.value[0],
        'agency-parent-id': this.parentAgency,
        'isHCM': true,
        "citizen-payment-method": this.citizenPaymentMethod
      })
    } else if(form === 1){
      this.pageIndex1 = 1;
      this.page1 = 1;
      this.getListDossierFeeByProcedure({
        page: 0,
        size: this.size,
        spec: 'page',
        'start-date': this.datePipe.transform(formObj.fromDate, 'yyyy-MM-dd') + 'T00:00:00.000Z',
        'end-date': this.datePipe.transform(formObj.toDate, 'yyyy-MM-dd') + 'T23:59:59.999Z',
        'sector-id': this.searchSector.value[0],
        'procedure-id': formObj.procedureCtrl,
        'agency-type-id': this.searchAgencyType.value[0],
        'agency-level-id': formObj.agencyLevel,
        'deployment-id': this.config.deploymentId,
        'agency-id': this.searchAgency.value[0],
        'agency-parent-id': this.parentAgency,
        'isHCM': true,
        "citizen-payment-method": this.citizenPaymentMethod
      })
    } else if(form === 2){
      this.pageIndex2 = 1;
      this.page2 = 1;
      this.getListDossierFeeBySector({
        page: 0,
        size: this.size,
        spec: 'page',
        'start-date': this.datePipe.transform(formObj.fromDate, 'yyyy-MM-dd') + 'T00:00:00.000Z',
        'end-date': this.datePipe.transform(formObj.toDate, 'yyyy-MM-dd') + 'T23:59:59.999Z',
        'sector-id': this.searchSector.value[0],
        'procedure-id': formObj.procedureCtrl,
        'agency-type-id': this.searchAgencyType.value[0],
        'agency-level-id': formObj.agencyLevel,
        'deployment-id': this.config.deploymentId,
        'agency-id': this.searchAgency.value[0],
        'agency-parent-id': this.parentAgency,
        'isHCM': true,
        "citizen-payment-method": this.citizenPaymentMethod
      })
    } else if(form === 3){
      this.pageIndex3 = 1;
      this.page3 = 1;
      this.getListDossierFeeByAgency({
        page: 0,
        size: this.size,
        spec: 'page',
        'start-date': this.datePipe.transform(formObj.fromDate, 'yyyy-MM-dd') + 'T00:00:00.000Z',
        'end-date': this.datePipe.transform(formObj.toDate, 'yyyy-MM-dd') + 'T23:59:59.999Z',
        'sector-id': this.searchSector.value[0],
        'procedure-id': formObj.procedureCtrl,
        'agency-type-id': this.searchAgencyType.value[0],
        'agency-level-id': formObj.agencyLevel,
        'deployment-id': this.config.deploymentId,
        'agency-id': this.searchAgency.value[0],
        'agency-parent-id': this.parentAgency,
        'isHCM': true,
        "citizen-payment-method": this.citizenPaymentMethod
      })
    }
  }


  getListDossierFee(searchParams) {
    if (!!this.administrativeAgency.id) {
      searchParams["sector-agency-id"] = this.administrativeAgency.id;
      searchParams["procedure-agency-id"] = this.administrativeAgency.id;
    }
    if (this.dossierFeeOnlyAgencyIdHCM) {
      searchParams["sector-only-agency"] = 1;
    }
    const formObj = this.searchForm.getRawValue();
    if (formObj.bookName != '') {
      searchParams["book-name"] = formObj.bookName;
    }
    if (formObj.invoicePattern != '') {
      searchParams["invoice-pattern"] = formObj.invoicePattern;

    }
    if (formObj.invoiceSerial != '') {
      searchParams["invoice-serial"] = formObj.invoiceSerial;
    }
    this.reportService.getReportForDossierElastis(searchParams).subscribe(data => {
      this.ELEMENTDATA = [];
      // this.ELEMENTDATA1 = [];
      // this.ELEMENTDATA2 = [];
      // this.ELEMENTDATA3 = [];
      // this.arrData = data.content;
      const dataTemp = [];
      let i = 0;
      // let sumFee = 0;  // Phí
      // let sumFees = 0; // Lệ phí
      let paidDateTemp = '';
      let totalFee = 0;

      let paymentMethods = [];

      data.content.forEach(element => {
        i++;

        if (element.paidDate !== undefined && element.fee > 0) {
          paidDateTemp = element.paidDate === undefined ? '' : new Date(element.paidDate).toLocaleString('en-GB');
        } else {
          paidDateTemp = '';
        }

        let addressArr = [];
        if (!!element.address) {
          addressArr.push(element.address);
        }
        if (!!element.village?.label) {
          addressArr.push(element.village?.label);
        }
        if (!!element.district?.label) {
          addressArr.push(element.district?.label);
        }
        if (!!element.province?.label) {
          addressArr.push(element.province?.label);
        }

        paymentMethods = [];
        if (this.citizenPaymentMethod) {
          
            paymentMethods.push(element.paymentMethod)
        
        }
        let status = "";
        if (element.status == 1) {
          status = 'phát hành';
        } else if (element.status == 2) {
          status = 'Đã thanh toán';
        } else {
          status = 'Đã hủy';
        }
        let receiptNumber = "";
        if (element?.numberReceipt != undefined && element?.numberReceipt != null) {

          receiptNumber = element?.numberReceipt ? element?.numberReceipt : "";
          if (receiptNumber.length > 0 && receiptNumber.length < 7) {
            for (let k = receiptNumber.length; k < 7; k++) {
              receiptNumber = '0' + receiptNumber;
            }
          }
        }

        if (this.isShowComboboxNewTemplate) {
          dataTemp.push({
            stt: this.size * (this.pageIndex - 1) + i,
            code: element.code,
            fullName: element.applicantName,
            acceptedDate: element.acceptedDate === undefined ? '' : new Date(element.acceptedDate).toLocaleString('en-GB'),
            paidDate: paidDateTemp,
            // SumFee: sumFee,
            // SumFees: sumFees,
            sumTotal: element.fee,
            receiptStatus: status,
            serial: element?.data?.responeReceiptInfo?.serial ? element?.data?.responeReceiptInfo?.serial : "",
            pattern: element?.data?.responeReceiptInfo?.pattern ? element?.data?.responeReceiptInfo?.pattern : "",
            receiptNumber: receiptNumber,
            address: addressArr.join(", "),
            applyMethod: element.applyMethod?.name ? element.applyMethod?.name : "",
            paymentMethod: this.citizenPaymentMethod ? this.getPaymentMethodNames(paymentMethods) : this.getPaymentMethodName(element.paymentMethod),
            procedure: element?.procedure?.translate?.find(translate => translate.languageId === Number(this.selectedLangId))?.name,
            sector: element?.procedure?.sector?.name?.find(name => name.languageId === Number(this.selectedLangId))?.name
          });
        } else {
          dataTemp.push({
            stt: this.size * (this.pageIndex - 1) + i,
            code: element.code,
            fullName: element.applicantName,
            acceptedDate: element.acceptedDate === undefined ? '' : new Date(element.acceptedDate).toLocaleString('en-GB'),
            paidDate: paidDateTemp,
            // SumFee: sumFee,
            // SumFees: sumFees,
            sumTotal: element.fee,
            receiptNumber: element.idMoneyReceipt ? element.idMoneyReceipt : "",
            address: addressArr.join(", "),
            applyMethod: element.applyMethod?.name ? element.applyMethod?.name : "",
            paymentMethod: this.citizenPaymentMethod ? this.getPaymentMethodNames(paymentMethods) : this.getPaymentMethodName(element.paymentMethod),
            procedure: element?.procedure?.translate?.find(translate => translate.languageId === Number(this.selectedLangId))?.name,
            sector: element?.procedure?.sector?.name?.find(name => name.languageId === Number(this.selectedLangId))?.name
          });
        }
      });

      this.countResult = data.totalElements;
      this.ELEMENTDATA = dataTemp;
      this.dataSource.data = this.ELEMENTDATA;
    });
  }

  getListDossierFeeByProcedure(searchParams) {
    if (!!this.administrativeAgency.id) {
      searchParams["sector-agency-id"] = this.administrativeAgency.id;
      searchParams["procedure-agency-id"] = this.administrativeAgency.id;
    }
    if (this.dossierFeeOnlyAgencyIdHCM) {
      searchParams["sector-only-agency"] = 1;
    }
    const formObj = this.searchForm.getRawValue();
    if (formObj.bookName != '') {
      searchParams["book-name"] = formObj.bookName;
    }
    if (formObj.invoicePattern != '') {
      searchParams["invoice-pattern"] = formObj.invoicePattern;

    }
    if (formObj.invoiceSerial != '') {
      searchParams["invoice-serial"] = formObj.invoiceSerial;
    }
    this.reportService.getReportForDossierElastisByProcedure(searchParams).subscribe(data => {
      this.ELEMENTDATA1=[];
      const dataTemp = [];
      let i = 0;


     data.content.forEach(element => {
      i++;
        dataTemp.push({
          stt1: this.size1 * (this.pageIndex1 - 1) + i,
          proceName: element.procedureName,
          sumDossier: element.amountDossier,
          // sumFee: sumFeeTemp,
          // sumFees: sumFeesTemp,
          total: element.totalFee
        });
      });

      this.countResult1 = data.totalElements;
      this.ELEMENTDATA1 = dataTemp;
      this.dataSource1.data = this.ELEMENTDATA1;
    });
  }

  getListDossierFeeBySector(searchParams) {
    if (!!this.administrativeAgency.id) {
      searchParams["sector-agency-id"] = this.administrativeAgency.id;
      searchParams["procedure-agency-id"] = this.administrativeAgency.id;
    }
    if (this.dossierFeeOnlyAgencyIdHCM) {
      searchParams["sector-only-agency"] = 1;
    }
    const formObj = this.searchForm.getRawValue();
    if (formObj.bookName != '') {
      searchParams["book-name"] = formObj.bookName;
    }
    if (formObj.invoicePattern != '') {
      searchParams["invoice-pattern"] = formObj.invoicePattern;

    }
    if (formObj.invoiceSerial != '') {
      searchParams["invoice-serial"] = formObj.invoiceSerial;
    }
    this.reportService.getReportForDossierElastisBySector(searchParams).subscribe(data => {
      this.ELEMENTDATA2 = [];
      const dataTemp = [];
      let i =0;

     data.content.forEach(element => {
      i++;
        dataTemp.push({
          stt2: this.size2 * (this.pageIndex2 - 1) + i,
          sectorName: element.sectorName,
          sumDossier: element.amountDossier,
          // sumFee: sumFeeTemp,
          // sumFees: sumFeesTemp,
          total: element.totalFee
        });
      });

      this.countResult2 = data.totalElements;
      this.ELEMENTDATA2 = dataTemp;
      this.dataSource2.data = this.ELEMENTDATA2;
    });
  }

  getListDossierFeeByAgency(searchParams) {
    if (!!this.administrativeAgency.id) {
      searchParams["sector-agency-id"] = this.administrativeAgency.id;
      searchParams["procedure-agency-id"] = this.administrativeAgency.id;
    }
    if (this.dossierFeeOnlyAgencyIdHCM) {
      searchParams["sector-only-agency"] = 1;
    }
    const formObj = this.searchForm.getRawValue();
    if (formObj.bookName != '') {
      searchParams["book-name"] = formObj.bookName;
    }
    if (formObj.invoicePattern != '') {
      searchParams["invoice-pattern"] = formObj.invoicePattern;

    }
    if (formObj.invoiceSerial != '') {
      searchParams["invoice-serial"] = formObj.invoiceSerial;
    }
    this.reportService.getReportForDossierElastisByAgency(searchParams).subscribe(data => {
      this.ELEMENTDATA3 = [];
      const dataTemp = [];
      let i =0;

     data.content.forEach(element => {
      i++;
        dataTemp.push({
          stt3: this.size3 * (this.pageIndex3 - 1) + i,
          agencyName: element.agencyName,
          sumDossier: element.amountDossier,
          // sumFee: sumFeeTemp,
          // sumFees: sumFeesTemp,
          total: element.totalFee
        });
      });

      this.countResult3 = data.totalElements;
      this.ELEMENTDATA3 = dataTemp;
      this.dataSource3.data = this.ELEMENTDATA3;
    });
  }



  ngAfterViewInit() {
    this.cdRef.detectChanges();
  }

  getReportForDossier(searchParams) {
    if (!!this.administrativeAgency.id) {
      searchParams["sector-agency-id"] = this.administrativeAgency.id;
      searchParams["procedure-agency-id"] = this.administrativeAgency.id;
    }
    if (this.dossierFeeOnlyAgencyIdHCM) {
      searchParams["sector-only-agency"] = 1;
    }
    const formObj = this.searchForm.getRawValue();
    if (formObj.bookName != '') {
      searchParams["book-name"] = formObj.bookName;
    }
    if (formObj.invoicePattern != '') {
      searchParams["invoice-pattern"] = formObj.invoicePattern;

    }
    if (formObj.invoiceSerial != '') {
      searchParams["invoice-serial"] = formObj.invoiceSerial;
    }
    this.reportService.getReportForDossier(searchParams).subscribe(data => {
      this.ELEMENTDATA = [];
      this.ELEMENTDATA1 = [];
      this.ELEMENTDATA2 = [];
      this.ELEMENTDATA3 = [];
      this.arrData = data.content;
      const form = this.tabGroup.selecItedndex ? this.tabGroup.selectedIndex : 0;
      this.getStatistical(form);
    });
  }

  groupBy(list, keyGetter) {
    const map = new Map();
    list.forEach((item) => {
      const key = keyGetter(item);
      if (!map.has(key)) {
        map.set(key, [item]);
      } else {
        map.get(key).push(item);
      }
    });
    return map;
  }

  getTabLoaded(tabChangeEvent: MatTabChangeEvent): void {
    this.tabGroup.selecItedndex = tabChangeEvent.index;

    if (this.tabGroup.selecItedndex === 0 && this.ELEMENTDATA.length === 0) {
      if(this.implimentElasticSearch){
        this.getReportForDossierElastic(0);
      } else {
        this.getStatistical(0);
      }
    } else if (this.tabGroup.selecItedndex === 1 && this.ELEMENTDATA1.length === 0) {
      if(this.implimentElasticSearch){
        this.getReportForDossierElastic(1);
      } else {
        this.getStatistical(1);
      }
    } else if (this.tabGroup.selecItedndex === 2 && this.ELEMENTDATA2.length === 0) {
      if(this.implimentElasticSearch){
        this.getReportForDossierElastic(2);
      } else {
        this.getStatistical(2);
      }
    } else if (this.tabGroup.selecItedndex === 3 && this.ELEMENTDATA3.length === 0) {
      if(this.implimentElasticSearch){
        this.getReportForDossierElastic(3);
      } else {
        this.getStatistical(3);
      }
    }
  }

  async getUserExperience() {
    const listUserAgency = JSON.parse(localStorage.getItem('listAgencyUser'));
    const userAgency = JSON.parse(localStorage.getItem('userAgency'));

    if (!!listUserAgency && listUserAgency.length !== 0 && !!userAgency) {
      let userExperience = listUserAgency.find(item => item.agency.id === userAgency.id);
      // if (!!this.userExperience.agency.parent) {
      //   this.userExperience.agency.id = this.userExperience.agency.parent.id;
      // }

      await this.eReceiptService
        .getDetailIntegratedConfig(userExperience.agency.id, '5f7c16069abb62f511890008', this.subsystemWebOneGateId)
        .subscribe(async res => {
          for await (const p of res.parameters) {
            if (p.key === 'invoice-book') {
              this.RIBook = p;
              this.RIBook.value = JSON.parse(this.RIBook.value);
              const temp = [];
              for await (const v of this.RIBook.value) {
                if (v.status) {
                  temp.push(v);
                }
              }
              this.RIBook.value = temp;
            }
          }

          // if (!!this.RIBook && this.RIBook.value.length !== 0) {
          //   this.fxFlex4 = '24.5';
          //   this.fxFlex3 = '32.5';
          // }
        });
    }
  }


  getStatistical(form: number) {
    if (form === 0) {
      const mapTemp = this.groupBy(this.arrData, x => x.dossierId);
      const dataTemp = [];
      let i = 0;
      // let sumFee = 0;  // Phí
      // let sumFees = 0; // Lệ phí
      let paidDateTemp = '';
      let totalFee = 0;

      let paymentMethods = [];

      for (const [key, value] of mapTemp) {
        i++;
        totalFee = 0;
        value.forEach((arr) => {
          totalFee += arr.amount * arr.quantity;
          // if (arr.procost.type.type === 0) {
          //   sumFee += arr.amount * arr.quantity;
          // } else if (arr.procost.type.type === 1) {
          //   sumFees += arr.amount * arr.quantity;
          // }
        });

        if (value[0].paidDate !== undefined && value[0].paid > 0) {
          paidDateTemp = value[0].paidDate === undefined ? '' : new Date(value[0].paidDate).toLocaleString('en-GB');
        } else {
          paidDateTemp = '';
        }

        let addressArr = [];
        if (!!value[0].address) {
          addressArr.push(value[0].address);
        }
        if (!!value[0].village?.label) {
          addressArr.push(value[0].village?.label);
        }
        if (!!value[0].district?.label) {
          addressArr.push(value[0].district?.label);
        }
        if (!!value[0].province?.label) {
          addressArr.push(value[0].province?.label);
        }

        paymentMethods = [];
        if (this.citizenPaymentMethod) {
          value.forEach(item => {
            paymentMethods.push(item.paymentMethod)
          });
        }
        let status = "";
        if (value[0].status == 1) {
          status = 'phát hành';
        } else if (value[0].status == 2) {
          status = 'Đã thanh toán';
        } else {
          status = 'Đã hủy';
        }
        let receiptNumber = "";
        if (value[0]?.data?.responeReceiptInfo?.listReceiptNumber != undefined) {

          receiptNumber = value[0]?.data?.responeReceiptInfo?.listReceiptNumber[0]?.numberReceipt ? value[0]?.data?.responeReceiptInfo?.listReceiptNumber[0]?.numberReceipt : "";
          if (receiptNumber.length > 0 && receiptNumber.length < 7) {
            for (let k = receiptNumber.length; k < 7; k++) {
              receiptNumber = '0' + receiptNumber;
            }
          }
        }

        if (this.isShowComboboxNewTemplate) {
          dataTemp.push({
            stt: this.size * (this.pageIndex - 1) + i,
            code: value[0].code,
            fullName: value[0].applicantName,
            acceptedDate: value[0].acceptedDate === undefined ? '' : new Date(value[0].acceptedDate).toLocaleString('en-GB'),
            paidDate: paidDateTemp,
            // SumFee: sumFee,
            // SumFees: sumFees,
            sumTotal: totalFee,
            receiptStatus: status,
            serial: value[0]?.data?.responeReceiptInfo?.serial ? value[0]?.data?.responeReceiptInfo?.serial : "",
            pattern: value[0]?.data?.responeReceiptInfo?.pattern ? value[0]?.data?.responeReceiptInfo?.pattern : "",
            receiptNumber: receiptNumber,
            address: addressArr.join(", "),
            applyMethod: value[0].applyMethod?.name ? value[0].applyMethod?.name : "",
            paymentMethod: this.citizenPaymentMethod ? this.getPaymentMethodNames(paymentMethods) : this.getPaymentMethodName(value[0].paymentMethod),
            procedure: value[0]?.procedure?.translate?.find(translate => translate.languageId === Number(this.selectedLangId))?.name,
            sector: value[0]?.procedure?.sector?.name?.find(name => name.languageId === Number(this.selectedLangId))?.name
          });
        } else {
          dataTemp.push({
            stt: this.size * (this.pageIndex - 1) + i,
            code: value[0].code,
            fullName: value[0].applicantName,
            acceptedDate: value[0].acceptedDate === undefined ? '' : new Date(value[0].acceptedDate).toLocaleString('en-GB'),
            paidDate: paidDateTemp,
            // SumFee: sumFee,
            // SumFees: sumFees,
            sumTotal: totalFee,
            receiptNumber: value[0].idMoneyReceipt ? value[0].idMoneyReceipt : "",
            address: addressArr.join(", "),
            applyMethod: value[0].applyMethod?.name ? value[0].applyMethod?.name : "",
            paymentMethod: this.citizenPaymentMethod ? this.getPaymentMethodNames(paymentMethods) : this.getPaymentMethodName(value[0].paymentMethod),
            procedure: value[0]?.procedure?.translate?.find(translate => translate.languageId === Number(this.selectedLangId))?.name,
            sector: value[0]?.procedure?.sector?.name?.find(name => name.languageId === Number(this.selectedLangId))?.name
          });
        }
      }

      this.countResult = dataTemp.length;
      this.ELEMENTDATA = dataTemp;
      this.paginate(this.page, 1);
    } else if (form === 1) {
      const mapTemp = this.groupBy(this.arrData, x => x.procedureId);
      const dataTemp = [];
      let mapProcedureTemp = new Map();
      let i = 0;
      // let sumFeeTemp = 0;  // Phí
      // let sumFeesTemp = 0; // Lệ phí
      let sumDossierTemp = 0;
      let totalFee = 0;

      for (const [k, v] of mapTemp) {
        i++;
        // sumFeeTemp = 0;
        // sumFeesTemp = 0;
        sumDossierTemp = 0;
        mapProcedureTemp = this.groupBy(v, x => x.dossierId);
        totalFee = 0;

        for (const [key, value] of mapProcedureTemp) {
          sumDossierTemp++;
          value.forEach((arr) => {
            totalFee += arr.amount * arr.quantity;
            // if (arr.procost.type.type === 0) {
            //   sumFeeTemp += arr.amount * arr.quantity;
            // } else if (arr.procost.type.type === 1) {
            //   sumFeesTemp += arr.amount * arr.quantity;
            // }
          });
        }

        dataTemp.push({
          stt1: this.size1 * (this.pageIndex1 - 1) + i,
          proceName: v[0].procedure?.translate.filter(t => t.languageId === this.selectedLangId)[0]?.name,
          sumDossier: sumDossierTemp,
          // sumFee: sumFeeTemp,
          // sumFees: sumFeesTemp,
          total: totalFee
        });
      }

      this.countResult1 = dataTemp.length;
      this.ELEMENTDATA1 = dataTemp;
      this.paginate1(this.page1, 0);
    } else if (form === 2) {
      const mapTemp = this.groupBy(this.arrData, x => x.sectorId);
      const dataTemp = [];
      let mapSectorTemp = new Map();
      let i = 0;
      // let sumFeeTemp = 0;  // Phí
      // let sumFeesTemp = 0; // Lệ phí
      let totalFee = 0;
      let sumDossierTemp = 0;

      for (const [k, v] of mapTemp) {
        i++;
        // sumFeeTemp = 0;
        // sumFeesTemp = 0;
        sumDossierTemp = 0;
        mapSectorTemp = this.groupBy(v, x => x.dossierId);
        let totalFee = 0;

        for (const [key, value] of mapSectorTemp) {
          sumDossierTemp++;
          value.forEach((arr) => {
            totalFee += arr.amount * arr.quantity;
            // if (arr.procost.type.type === 0) {
            //   sumFeeTemp += arr.amount * arr.quantity;
            // } else if (arr.procost.type.type === 1) {
            //   sumFeesTemp += arr.amount * arr.quantity;
            // }
          });
        }

        dataTemp.push({
          stt2: this.size2 * (this.pageIndex2 - 1) + i,
          sectorName: v[0].procedure?.sector?.name?.filter(t => t.languageId === this.selectedLangId)[0]?.name,
          sumDossier: sumDossierTemp,
          // sumFee: sumFeeTemp,
          // sumFees: sumFeesTemp,
          total: totalFee
        });
      }

      this.countResult2 = dataTemp.length;
      this.ELEMENTDATA2 = dataTemp;
      this.paginate2(this.page2, 0);
    } else if (form === 3) {
      const mapTemp = this.groupBy(this.arrData, x => x.agencyId);
      const dataTemp = [];
      let mapAgencyTemp = new Map();
      let i = 0;
      // let sumFeeOnlTemp = 0;  // Phí Trực tuyến
      // let sumFeesOnlTemp = 0; // Lệ phí Trực tuyến
      // let sumFeeTTTemp = 0;   // Phí Trực tiếp
      // let sumFeesTTTemp = 0;  // Lệ phí trực tiếp
      let sumDossierTemp = 0;
      let totalFee = 0;

      for (const [k, v] of mapTemp) {
        i++;
        mapAgencyTemp = this.groupBy(v, x => x.dossierId);

        // sumFeeOnlTemp = 0;
        // sumFeesOnlTemp = 0;
        // sumFeeTTTemp = 0;
        // sumFeesTTTemp = 0;
        sumDossierTemp = 0;
        let totalFee = 0;

        for (const [key, value] of mapAgencyTemp) {
          sumDossierTemp++;
          value.forEach((arr) => {
            totalFee += arr.amount * arr.quantity;
            // if (arr.procost.type.type === 0) {
            //   if (arr.applyMethodId === 0) {
            //     sumFeeOnlTemp += arr.amount * arr.quantity;
            //   } else if (arr.applyMethodId === 1) {
            //     sumFeeTTTemp += arr.amount * arr.quantity;
            //   }
            // } else if (arr.procost.type.type === 1) {
            //   if (arr.applyMethodId === 0) { // Trực tuyến
            //     sumFeesOnlTemp += arr.amount * arr.quantity;
            //   } else if (arr.applyMethodId === 1) {
            //     sumFeesTTTemp += arr.amount * arr.quantity;
            //   }
            // }
          });
        }

        dataTemp.push({
          stt3: this.size3 * (this.pageIndex3 - 1) + i,
          agencyName: v[0].agency?.name?.filter(t => t.languageId === this.selectedLangId)[0]?.name,
          sumDossier: sumDossierTemp,
          // sumFeeOnl: sumFeeOnlTemp,
          // sumFeesOnl: sumFeesOnlTemp,
          // sumFeeTT: sumFeeTTTemp,
          // sumFeesTT: sumFeesTTTemp,
          total: totalFee
        });
      }

      this.countResult3 = dataTemp.length;
      this.ELEMENTDATA3 = dataTemp;
      this.paginate3(this.page3, 0);
    }
  }

  getPaymentMethodName(item) {
    if (this.showStatisticsPaymentDossierFeeHcm && this.showStatisticsPaymentDossierFeeHcm.listPayment) {
      if (item) {
        let pay = this.showStatisticsPaymentDossierFeeHcm.listPayment.find(o => o.id == item.id);
        if (pay) return pay.name;
      }
      else return this.showStatisticsPaymentDossierFeeHcm.default;
    }
    return "";
  }

  getPaymentMethodNames(items) {
    if (this.showStatisticsPaymentDossierFeeHcm && this.showStatisticsPaymentDossierFeeHcm.listPayment) {
      let listPay = new Set<string>();
      let pay;
      if (items && items.length > 0) {
        items.forEach(item => {
          pay = this.showStatisticsPaymentDossierFeeHcm.listPayment.find(o => o.id == item.id);
          if (pay) {
            listPay.add(pay.name);
          }
        })
        return Array.from(listPay).join(", ");
      }
      else return this.showStatisticsPaymentDossierFeeHcm.default;
    }
    return "";
  }

  getListAgencyTag() {
    this.reportService.getListAgencyTag('5f3a491c4e1bd312a6f00005').subscribe(data => {
      for (const i of data.content) {
        this.typeAgency.push({
          id: i.id,
          name: i.name
        });
      }
    });
  }

  getListAgency(id = '') {
    const userAgency = JSON.parse(localStorage.getItem('userAgency'));
    let rootAgencyId: any = '';
    rootAgencyId = this.config.rootAgency.id;
    // if (userAgency !== null) {
    //   rootAgencyId = userAgency.id;
    // } else {
    //   rootAgencyId = this.config.rootAgency.id;
    // }
    id = rootAgencyId;
    this.reportService.getListAgency(id).subscribe(data => {
      for (const i of data.content) {
        this.agency.push({
          id: i.id,
          name: i.name,
          telephone: i.telephone
        });
      }
    });
  }

  getListAgencyLevel() {
    this.reportService.getListAgencyLevel().subscribe(data => {
      for (const i of data.content) {
        this.agencyLevel.push({
          id: i.id,
          name: i.name
        });
      }
    });
  }

  sectorChange(event) {
    this.sectorId = event.value;
    this.router.navigate([], {
      queryParams: {
        sector: event.value
      },
      queryParamsHandling: 'merge'
    });
    this.listProcedure = [];
    this.isFullListProcedure = false;
    this.listProcedurePage = 0;
    this.getListProcedure();
  }

  protected filterProcedure() {
    if (!this.procedures) {
      return;
    }
    let search = this.searchProcedureCtrl.value.trim();
    this.searchProcedureKeyword = search;
    if (!search) {
      this.procedureFiltered.next(this.procedures.slice());
      return;
    } else {
      search = search.toLowerCase();
    }
    if (this.procedures.filter(proc => proc.name.toLowerCase().indexOf(search) > -1).length > 0) {
      this.procedureFiltered.next(
        this.procedures.filter(proc => proc.name.toLowerCase().indexOf(search) > -1)
      );
    } else {
      // tslint:disable-next-line: max-line-length
      let searchString =
        '?status=-1&sort=translate.name,asc&keyword=' + search +
        '&spec=page&page=0&size=50' +
        '&sector-id=' + this.sectorId;

      if (!!this.administrativeAgency.id) {
        searchString += '&agency-id=' + this.administrativeAgency.id + '&sector-agency-id=' + this.administrativeAgency.id
      }
      if (this.dossierFeeOnlyAgencyIdHCM) {
        searchString += "&only-agency-id=1";
      }

      this.listProcedurePage = 0;
      this.procedureService.getListProcedure(searchString).subscribe(data => {
        for (let i = 0; i < data.numberOfElements; i++) {
          this.listProcedure.push(data.content[i]);
        }
        this.procedures = JSON.parse(JSON.stringify(this.listProcedure).replace(/null/g, '""'));
      }, err => {
        console.log(err);
      });
    }
  }

  getListProcedure() {
    if (this.isFullListProcedure) {
      return;
    } else {

      let searchString =
        '?status=1&keyword=' + this.searchProcedureKeyword +
        '&spec=page&page=' + this.listProcedurePage + '&size=50' +
        '&sector-id=' + this.sectorId + '&sort=translate.name,asc';

      if (!!this.administrativeAgency.id) {
        searchString += '&agency-id=' + this.administrativeAgency.id + '&sector-agency-id=' + this.administrativeAgency.id
      }
      if (this.dossierFeeOnlyAgencyIdHCM) {
        searchString += "&sector-only-agency-id=1";
      }

      this.procedureService.getListProcedure(searchString).subscribe(data => {
        this.isFullListProcedure = data.last;
        this.listProcedurePage++;
        for (let i = 0; i < data.numberOfElements; i++) {
          this.listProcedure.push(data.content[i]);
        }
        this.procedures = JSON.parse(JSON.stringify(this.listProcedure).replace(/null/g, '""'));
        this.procedureFiltered.next(this.procedures);
        this.searchProcedureCtrl.valueChanges.pipe(takeUntil(this.onDestroy)).subscribe(() => {
          clearTimeout(this.timeout);
          this.timeout = setTimeout(() => {
            this.filterProcedure();
          }, 500);
        });
      }, err => {
        console.log(err);
      });
    }
  }

  // paginate
  paginate(event: any, type) {
    switch (type) {
      case 0:
        this.pageIndex = event;
        if(this.implimentElasticSearch){
          const formObj = this.searchForm.getRawValue();
          this.getListDossierFee({
            page: event - 1 ,
            size: this.size,
            spec: 'page',
            'start-date': this.datePipe.transform(formObj.fromDate, 'yyyy-MM-dd') + 'T00:00:00.000Z',
            'end-date': this.datePipe.transform(formObj.toDate, 'yyyy-MM-dd') + 'T23:59:59.999Z',
            'sector-id': this.searchSector.value[0],
            'procedure-id': formObj.procedureCtrl,
            'agency-type-id': this.searchAgencyType.value[0],
            'agency-level-id': formObj.agencyLevel,
            'deployment-id': this.config.deploymentId,
            'agency-id': this.searchAgency.value[0],
            'agency-parent-id': this.parentAgency,
            'isHCM': true,
            "citizen-payment-method": this.citizenPaymentMethod
          });
        } else {
          this.dataSource.data = this.ELEMENTDATA.slice(this.size * (this.pageIndex - 1), this.size * this.pageIndex);
        }
        break;
      case 1:
        this.pageIndex = 1;
        this.page = 1;
        if(this.implimentElasticSearch){
          const formObj = this.searchForm.getRawValue();
          this.getListDossierFee({
            page: 0,
            size: this.size,
            spec: 'page',
            'start-date': this.datePipe.transform(formObj.fromDate, 'yyyy-MM-dd') + 'T00:00:00.000Z',
            'end-date': this.datePipe.transform(formObj.toDate, 'yyyy-MM-dd') + 'T23:59:59.999Z',
            'sector-id': this.searchSector.value[0],
            'procedure-id': formObj.procedureCtrl,
            'agency-type-id': this.searchAgencyType.value[0],
            'agency-level-id': formObj.agencyLevel,
            'deployment-id': this.config.deploymentId,
            'agency-id': this.searchAgency.value[0],
            'agency-parent-id': this.parentAgency,
            'isHCM': true,
            "citizen-payment-method": this.citizenPaymentMethod
          });
        } else {
        this.dataSource.data = this.ELEMENTDATA.slice(this.size * (this.pageIndex - 1), this.size * this.pageIndex);
        }
        break;
    }
  }

  paginate1(event: any, type) {
    switch (type) {
      case 0:
        this.pageIndex1 = event;
        if(this.implimentElasticSearch){
          const formObj = this.searchForm.getRawValue();
          this.getListDossierFeeByProcedure({
            page: event - 1,
            size: this.size,
            spec: 'page',
            'start-date': this.datePipe.transform(formObj.fromDate, 'yyyy-MM-dd') + 'T00:00:00.000Z',
            'end-date': this.datePipe.transform(formObj.toDate, 'yyyy-MM-dd') + 'T23:59:59.999Z',
            'sector-id': this.searchSector.value[0],
            'procedure-id': formObj.procedureCtrl,
            'agency-type-id': this.searchAgencyType.value[0],
            'agency-level-id': formObj.agencyLevel,
            'deployment-id': this.config.deploymentId,
            'agency-id': this.searchAgency.value[0],
            'agency-parent-id': this.parentAgency,
            'isHCM': true,
            "citizen-payment-method": this.citizenPaymentMethod
          })
        } else {
          this.dataSource1.data = this.ELEMENTDATA1.slice(this.size1 * (this.pageIndex1 - 1), this.size1 * this.pageIndex1);
        }
        break;
      case 1:
        this.pageIndex1 = 1;
        this.page1 = 1;
        if(this.implimentElasticSearch){
          const formObj = this.searchForm.getRawValue();
          this.getListDossierFeeByProcedure({
            page: 0,
            size: this.size,
            spec: 'page',
            'start-date': this.datePipe.transform(formObj.fromDate, 'yyyy-MM-dd') + 'T00:00:00.000Z',
            'end-date': this.datePipe.transform(formObj.toDate, 'yyyy-MM-dd') + 'T23:59:59.999Z',
            'sector-id': this.searchSector.value[0],
            'procedure-id': formObj.procedureCtrl,
            'agency-type-id': this.searchAgencyType.value[0],
            'agency-level-id': formObj.agencyLevel,
            'deployment-id': this.config.deploymentId,
            'agency-id': this.searchAgency.value[0],
            'agency-parent-id': this.parentAgency,
            'isHCM': true,
            "citizen-payment-method": this.citizenPaymentMethod
          })
        } else {
          this.dataSource1.data = this.ELEMENTDATA1.slice(this.size1 * (this.pageIndex1 - 1), this.size1 * this.pageIndex1);
        }
        break;
    }
  }

  paginate2(event: any, type) {
    switch (type) {
      case 0:
        this.pageIndex2 = event;
        if(this.implimentElasticSearch){
          const formObj = this.searchForm.getRawValue();
          this.getListDossierFeeBySector({
            page: event - 1 ,
            size: this.size,
            spec: 'page',
            'start-date': this.datePipe.transform(formObj.fromDate, 'yyyy-MM-dd') + 'T00:00:00.000Z',
            'end-date': this.datePipe.transform(formObj.toDate, 'yyyy-MM-dd') + 'T23:59:59.999Z',
            'sector-id': this.searchSector.value[0],
            'procedure-id': formObj.procedureCtrl,
            'agency-type-id': this.searchAgencyType.value[0],
            'agency-level-id': formObj.agencyLevel,
            'deployment-id': this.config.deploymentId,
            'agency-id': this.searchAgency.value[0],
            'agency-parent-id': this.parentAgency,
            'isHCM': true,
            "citizen-payment-method": this.citizenPaymentMethod
          })
        } else {
          this.dataSource2.data = this.ELEMENTDATA2.slice(this.size2 * (this.pageIndex2 - 1), this.size2 * this.pageIndex2);
        }
        break;
      case 1:
        this.pageIndex2 = 1;
        this.page2 = 1;
        if(this.implimentElasticSearch){
          const formObj = this.searchForm.getRawValue();
          this.getListDossierFeeBySector({
            page: 0,
            size: this.size,
            spec: 'page',
            'start-date': this.datePipe.transform(formObj.fromDate, 'yyyy-MM-dd') + 'T00:00:00.000Z',
            'end-date': this.datePipe.transform(formObj.toDate, 'yyyy-MM-dd') + 'T23:59:59.999Z',
            'sector-id': this.searchSector.value[0],
            'procedure-id': formObj.procedureCtrl,
            'agency-type-id': this.searchAgencyType.value[0],
            'agency-level-id': formObj.agencyLevel,
            'deployment-id': this.config.deploymentId,
            'agency-id': this.searchAgency.value[0],
            'agency-parent-id': this.parentAgency,
            'isHCM': true,
            "citizen-payment-method": this.citizenPaymentMethod
          })
        } else {
          this.dataSource2.data = this.ELEMENTDATA2.slice(this.size2 * (this.pageIndex2 - 1), this.size2 * this.pageIndex2);
        }
        break;
    }
  }

  paginate3(event: any, type) {
    switch (type) {
      case 0:
        this.pageIndex3 = event;
        if(this.implimentElasticSearch){
          const formObj = this.searchForm.getRawValue();
          this.getListDossierFeeByAgency({
            page: event - 1,
            size: this.size,
            spec: 'page',
            'start-date': this.datePipe.transform(formObj.fromDate, 'yyyy-MM-dd') + 'T00:00:00.000Z',
            'end-date': this.datePipe.transform(formObj.toDate, 'yyyy-MM-dd') + 'T23:59:59.999Z',
            'sector-id': this.searchSector.value[0],
            'procedure-id': formObj.procedureCtrl,
            'agency-type-id': this.searchAgencyType.value[0],
            'agency-level-id': formObj.agencyLevel,
            'deployment-id': this.config.deploymentId,
            'agency-id': this.searchAgency.value[0],
            'agency-parent-id': this.parentAgency,
            'isHCM': true,
            "citizen-payment-method": this.citizenPaymentMethod
          })
        } else {
          this.dataSource3.data = this.ELEMENTDATA3.slice(this.size3 * (this.pageIndex3 - 1), this.size3 * this.pageIndex3);
        }
        break;
      case 1:
        this.pageIndex3 = 1;
        this.page3 = 1;
        if(this.implimentElasticSearch){
          const formObj = this.searchForm.getRawValue();
          this.getListDossierFeeByAgency({
            page: 0,
            size: this.size,
            spec: 'page',
            'start-date': this.datePipe.transform(formObj.fromDate, 'yyyy-MM-dd') + 'T00:00:00.000Z',
            'end-date': this.datePipe.transform(formObj.toDate, 'yyyy-MM-dd') + 'T23:59:59.999Z',
            'sector-id': this.searchSector.value[0],
            'procedure-id': formObj.procedureCtrl,
            'agency-type-id': this.searchAgencyType.value[0],
            'agency-level-id': formObj.agencyLevel,
            'deployment-id': this.config.deploymentId,
            'agency-id': this.searchAgency.value[0],
            'agency-parent-id': this.parentAgency,
            'isHCM': true,
            "citizen-payment-method": this.citizenPaymentMethod
          })
        } else {
          this.dataSource3.data = this.ELEMENTDATA3.slice(this.size3 * (this.pageIndex3 - 1), this.size3 * this.pageIndex3);
        }
        break;
    }
  }

  paginateValue3() {
    const arrValue: any = [];
    const start = (this.pageIndex3 - 1) * this.size3;
    const end = this.pageIndex3 * this.size3;
    for (let i = 0; i < this.ELEMENTDATA3.length; i++) {
      if (i >= start && i < end) {
        arrValue.push(this.ELEMENTDATA3[i]);
      }
    }
    this.dataSource3.data = arrValue;
  }

  paginateValue2() {
    const arrValue: any = [];
    const start = (this.pageIndex2 - 1) * this.size2;
    const end = this.pageIndex2 * this.size2;
    for (let i = 0; i < this.ELEMENTDATA2.length; i++) {
      if (i >= start && i < end) {
        arrValue.push(this.ELEMENTDATA2[i]);
      }
    }
    this.dataSource2.data = arrValue;
  }

  paginateValue1() {
    const arrValue: any = [];
    const start = (this.pageIndex1 - 1) * this.size1;
    const end = this.pageIndex1 * this.size1;
    for (let i = 0; i < this.ELEMENTDATA1.length; i++) {
      if (i >= start && i < end) {
        arrValue.push(this.ELEMENTDATA1[i]);
      }
    }
    this.dataSource1.data = arrValue;
  }

  async loadSectors(obj) {
    if (!this.administrativeAgency.loaded) {
      await this.getAdministrativeAgencyByLoggedInUser();
    }
    const keyword = obj?.keyword || "";
    const page = obj?.page || 0;
    const size = obj?.size || 10;
    const parentAgency = this.getParentId();
    let searchString = "?spec=page&keyword=" + keyword +
      "&page=" + page + "&size=" + size;

    if (!!this.administrativeAgency.id) {
      searchString += "&agency-id=" + this.administrativeAgency.id;
    }
    if (this.dossierFeeOnlyAgencyIdHCM) {
      searchString += "&only-agency-id=1";
    }
    // this.reportService.getPageSectors(page, size, keyword).subscribe((rs) => {
    //   const data = [].concat(rs.content);
    //   const hasNext = !rs.last;
    //   this.searchSector.update(data, hasNext);
    // });
    this.procedureService.getListSector(searchString).subscribe((rs) => {
      const data = [].concat(rs.content);
      const hasNext = !rs.last;
      this.searchSector.update(data, hasNext);
    })
  }

  getParentId(): string | null {
    const userAgency = JSON.parse(localStorage.getItem('userAgency'));
    if (userAgency) {
      if (!!userAgency.parent) {
        this.getAgencyName = userAgency.parent.name;
      } else {
        this.getAgencyName = userAgency.name;
      }
      if (!!userAgency.parent && !!userAgency.parent.id && !this.isAdmin) {
        this.getAgencyIdForProcedure = userAgency.parent.id;
        if (userAgency.parent !== null && userAgency.parent.id == undefined) {
          this.getAgencyIdForProcedure = userAgency.parent;
        } else {
          this.getAgencyIdForProcedure = userAgency.parent.id;
        }
        return userAgency.parent.id;
      }
      else if (userAgency.id !== this.config.rootAgency.id || this.isAdmin) {
        this.getAgencyIdForProcedure = userAgency.id;
        return userAgency.id;
      }
    }
    return null;
  }

  checkExistAgencyRoot() {
    let result = true;
    this.searchAgency.items.forEach(element => {
      if (element.id == this.getAgencyIdForProcedure) {
        result = false;
        console.log('exist agency root');
      }
    });
    return result;
  }
  async addAgencyRoot(keyword?: string) {
    const rs = await this.dossierService.getAgencyFully(this.getAgencyIdForProcedure);
    rs.name = rs.name[0].name;
    if (keyword) {
      if (!rs.name || rs.name.toLowerCase().indexOf(keyword.toLowerCase().trim()) < 0) {
        return;
      }
    }
    const data = [].concat(rs);
    const hasNext = !rs.last;
    this.searchAgency.update(data, hasNext);
    this.isAddAgencyRoot = true;
  }

  loadAgency(obj) {
    const keyword = obj?.keyword || "";
    const page = obj?.page || 0;

    // Add root agency again when reload combobox
    this.isAddAgencyRoot = page != 0;

    const size = obj?.size || 10;
    const parentId = this.getParentId();
    this.reportService.getPageAgency(page, size, parentId, keyword).subscribe((rs) => {
      const data = [].concat(rs.content);
      const hasNext = !rs.last;
      this.searchAgency.update(data, hasNext);
      this.listAgencySearch = this.searchAgency;
      console.log('agenyc', this.searchAgency);
      if (this.isAddAgencyRoot == false && this.checkExistAgencyRoot() == true) {
        this.addAgencyRoot(keyword);
      }
    });
  }

  loadAgencyType(obj) {
    const keyword = obj?.keyword || "";
    const page = obj?.page || 0;
    const size = obj?.size || 10;
    this.reportService.getPageAgencyTag(page, size, '5f3a491c4e1bd312a6f00005', keyword).subscribe((rs) => {
      const data = [].concat(rs.content);
      const hasNext = !rs.last;
      this.searchAgencyType.update(data, hasNext);
    });
  }

  async getAdministrativeAgencyByLoggedInUser() {
    const userAgency = JSON.parse(localStorage.getItem('userAgency'));
    const userAgencyTag: string[] = userAgency.tag ? userAgency.tag : [];
    if (this.administrativeAgencyTag.length > 0) {
      if (userAgencyTag.length > 0) {
        const index = userAgencyTag.findIndex(item => this.administrativeAgencyTag.map(tag => tag.id).includes(item))
        if (index > -1) {
          this.administrativeAgency.id = userAgency.id;
          this.administrativeAgency.loaded = true;
          return;
        }
      }

      let parentAgencyId = userAgency.parent?.id ? userAgency.parent.id : "";
      let parentAgency;
      do {
        if (parentAgencyId === "") {
          this.administrativeAgency.loaded = true;
          break;
        }
        parentAgency = await this.dossierService.getAgencyFully(parentAgencyId);
        if (!!parentAgency) {
          if (!!parentAgency.tag && parentAgency.tag.length > 0) {
            const index = parentAgency.tag.findIndex(item => this.administrativeAgencyTag.map(tag => tag.id).includes(item.id));
            if (index > -1) {
              this.administrativeAgency.id = parentAgency.id;
              this.administrativeAgency.loaded = true;
              break
            }
          }
          parentAgencyId = parentAgency.parent?.id ? parentAgency.parent.id : "";
        }
        else {
          this.administrativeAgency.loaded = true;
          break;
        }
        console.log("dossier fee hcm q1");
      }
      while (true);
    }
  }


  getExportExcelData(type) {
    this.exportExcelData = []
    const arrData = this.arrData;
    switch (type) {
      case 0:
        const mapTemp = this.groupBy(arrData, x => x.dossierId);
        let i = 0;
        // let sumFee = 0;  // Phí
        // let sumFees = 0; // Lệ phí
        let paidDateTemp = '';
        let totalFee = 0;

        let paymentMethods = [];

        for (const [key, value] of mapTemp) {
          i++;
          totalFee = 0;
          value.forEach((arr) => {
            totalFee += arr.amount * arr.quantity;
            // if (arr.procost.type.type === 0) {
            //   sumFee += arr.amount * arr.quantity;
            // } else if (arr.procost.type.type === 1) {
            //   sumFees += arr.amount * arr.quantity;
            // }
          });

          if (value[0].paidDate !== undefined && value[0].paid > 0) {
            paidDateTemp = value[0].paidDate === undefined ? '' : new Date(value[0].paidDate).toLocaleString('en-GB');
          } else {
            paidDateTemp = '';
          }

          let addressArr = [];
          if (!!value[0].address) {
            addressArr.push(value[0].address);
          }
          if (!!value[0].village?.label) {
            addressArr.push(value[0].village?.label);
          }
          if (!!value[0].district?.label) {
            addressArr.push(value[0].district?.label);
          }
          if (!!value[0].province?.label) {
            addressArr.push(value[0].province?.label);
          }

          paymentMethods = [];
          if (this.citizenPaymentMethod) {
            value.forEach(item => paymentMethods.push(item.paymentMethod));
          }
          let stt = this.size * (this.pageIndex - 1) + i;
          if (this.showStatisticsPaymentDossierFeeHcm && this.showStatisticsPaymentDossierFeeHcm.enable) {
            this.exportExcelData.push({
              stt: stt, // size = 50,
              code: value[0].code,
              acceptedDate: value[0].acceptedDate === undefined ? '' : this.datePipe.transform(new Date(value[0].acceptedDate), 'dd/MM/yyyy'),
              receiptNumber: value[0].idMoneyReceipt ? value[0].idMoneyReceipt : "",
              paidDate: paidDateTemp,
              sumTotal: totalFee,
              fullName: value[0].applicantName,
              address: addressArr.join(", "),
              applyMethod: value[0].applyMethod?.name ? value[0].applyMethod?.name : "",
              sector: value[0]?.procedure?.sector?.name?.find(name => name.languageId === Number(this.selectedLangId))?.name,
              procedure: value[0]?.procedure?.translate?.find(translate => translate.languageId === Number(this.selectedLangId))?.name,
              paymentMethod: this.citizenPaymentMethod ? this.getPaymentMethodNames(paymentMethods) : this.getPaymentMethodName(value[0].paymentMethod)
              // code: value[0].code,
              // paidDate: paidDateTemp,
              // SumFee: sumFee,
              // SumFees: sumFees,
            });

          }
          else {
            this.exportExcelData.push({
              stt: stt, // size = 50,
              code: value[0].code,
              acceptedDate: value[0].acceptedDate === undefined ? '' : this.datePipe.transform(new Date(value[0].acceptedDate), 'dd/MM/yyyy'),
              receiptNumber: value[0].idMoneyReceipt ? value[0].idMoneyReceipt : "",
              paidDate: paidDateTemp,
              sumTotal: totalFee,
              fullName: value[0].applicantName,
              address: addressArr.join(", "),
              applyMethod: value[0].applyMethod?.name ? value[0].applyMethod?.name : "",
              sector: value[0]?.procedure?.sector?.name?.find(name => name.languageId === Number(this.selectedLangId))?.name,
              procedure: value[0]?.procedure?.translate?.find(translate => translate.languageId === Number(this.selectedLangId))?.name,

              // code: value[0].code,
              // paidDate: paidDateTemp,
              // SumFee: sumFee,
              // SumFees: sumFees,
            });
          }
          if (this.isShowComboboxNewTemplate) {
            this.exportExcelData.map(item => { if (item.stt == stt) { item.invoicePattern = value[0]?.data?.responeReceiptInfo?.pattern ? value[0]?.data?.responeReceiptInfo?.pattern : "" } });
            this.exportExcelData.map(item => { if (item.stt == stt) { item.invoiceSerial = value[0]?.data?.responeReceiptInfo?.serial ? value[0]?.data?.responeReceiptInfo?.serial : "" } });
            let receiptNumber = "";
            if (value[0]?.data?.responeReceiptInfo?.listReceiptNumber != undefined) {
              receiptNumber = value[0]?.data?.responeReceiptInfo?.listReceiptNumber[0]?.numberReceipt ? value[0]?.data?.responeReceiptInfo?.listReceiptNumber[0]?.numberReceipt : "";
              if (receiptNumber.length > 0 && receiptNumber.length < 7) {
                for (let k = receiptNumber.length; k < 7; k++) {
                  receiptNumber = '0' + receiptNumber;
                }
              }
            }
            this.exportExcelData.map(item => { if (item.stt == stt) { item.receiptNumber = receiptNumber } });
            let status = "";
            if (value[0].status == 1) {
              status = 'phát hành';
            } else if (value[0].status == 2) {
              status = 'Đã thanh toán';
            } else {
              status = 'Đã hủy';
            }
            this.exportExcelData.map(item => { if (item.stt == stt) { item.receiptStatus = status } });
          }
        }
        break;
      ////GETDATA BY PROCEDURE
      case 1:
        //
        const mapTemp2 = this.groupBy(this.arrData, x => x.procedureId);
        let mapProcedureTemp2 = new Map();
        let j = 0;
        let sumDossierTemp2 = 0;
        let totalFee2 = 0;

        for (const [k, v] of mapTemp2) {
          j++;
          sumDossierTemp2 = 0;
          mapProcedureTemp2 = this.groupBy(v, x => x.dossierId);
          totalFee2 = 0;

          for (const [key, value] of mapProcedureTemp2) {
            sumDossierTemp2++;
            value.forEach((arr) => {
              totalFee2 += arr.amount * arr.quantity;
            });
          }
          this.exportExcelData.push({
            stt1: this.size1 * (this.pageIndex1 - 1) + j,
            proceName: v[0].procedure?.translate.filter(t => t.languageId === this.selectedLangId)[0]?.name,
            sumDossier: sumDossierTemp2,
            total: totalFee2
          });
        }
        break;
      //GETDATA BY SECTOR
      case 2:
        const mapTemp3 = this.groupBy(this.arrData, x => x.sectorId);
        let mapProcedureTemp3 = new Map();
        let z = 0;
        let sumDossierTemp3 = 0;
        let totalFee3 = 0;

        for (const [k, v] of mapTemp3) {
          z++;
          sumDossierTemp3 = 0;
          mapProcedureTemp3 = this.groupBy(v, x => x.dossierId);
          totalFee3 = 0;

          for (const [key, value] of mapProcedureTemp3) {
            sumDossierTemp3++;
            value.forEach((arr) => {
              totalFee3 += arr.amount * arr.quantity;
            });
          }
          this.exportExcelData.push({
            stt1: this.size1 * (this.pageIndex1 - 1) + z,
            sectorName: v[0].procedure?.sector?.name?.filter(t => t.languageId === this.selectedLangId)[0]?.name,
            sumDossier: sumDossierTemp3,
            total: totalFee3
          });
        }
        break;
      //GETDATA BY AGENCY
      case 3:
        const mapTemp4 = this.groupBy(this.arrData, x => x.agencyId);
        let mapProcedureTemp4 = new Map();
        let w = 0;
        let sumDossierTemp4 = 0;
        let totalFee4 = 0;

        for (const [k, v] of mapTemp4) {
          w++;
          sumDossierTemp4 = 0;
          mapProcedureTemp4 = this.groupBy(v, x => x.dossierId);
          totalFee4 = 0;

          for (const [key, value] of mapProcedureTemp4) {
            sumDossierTemp4++;
            value.forEach((arr) => {
              totalFee4 += arr.amount * arr.quantity;
            });
          }
          this.exportExcelData.push({
            stt1: this.size1 * (this.pageIndex1 - 1) + w,
            agencyName: v[0].agency?.parent?.name?.filter(t => t.languageId === this.selectedLangId)[0]?.name,
            sumDossier: sumDossierTemp4,
            total: totalFee4
          });
        }
        break;
    }
  }

  exportExcelAll(type) {
    const selectedTab = this.tabGroup.selecItedndex ? this.tabGroup.selecItedndex : 0;
    this.getExportExcelData(selectedTab);
    const formObj = this.searchForm.getRawValue();
    const newDate = tUtils.newDate();
    const excelFileName = 'Thong ke phi co so bien lai ' + this.datePipe.transform(newDate, 'dd/MM/yyyy');

    let fromDateExcel = '';
    let toDateExcel = '';
    if (formObj.fromDate != null && formObj.fromDate !== '') {
      fromDateExcel = this.datePipe.transform(formObj.fromDate, 'dd/MM/yyyy');
      fromDateExcel = 'Từ ngày: ' + fromDateExcel;
    }
    if (formObj.toDate != null && formObj.toDate !== '') {
      toDateExcel = this.datePipe.transform(formObj.toDate, 'dd/MM/yyyy');
      toDateExcel = 'Đến ngày: ' + toDateExcel;
    }

    let sheetName = "Sheet 1"
    if(this.implimentElasticSearch){
    let subAgencyName = this.getAgencyName;
    let searchString ={
      page: 0,
      size: this.size,
      spec: 'page',
      'start-date': this.datePipe.transform(formObj.fromDate, 'yyyy-MM-dd') + 'T00:00:00.000Z',
      'end-date': this.datePipe.transform(formObj.toDate, 'yyyy-MM-dd') + 'T23:59:59.999Z',
      'sector-id': this.searchSector.value[0],
      'procedure-id': formObj.procedureCtrl,
      'agency-type-id': this.searchAgencyType.value[0],
      'agency-level-id': formObj.agencyLevel,
      'deployment-id': this.config.deploymentId,
      'agency-id': this.searchAgency.value[0],
      'agency-parent-id': this.parentAgency,
      'isHCM': true,
      'name': subAgencyName,
      "citizen-payment-method": this.citizenPaymentMethod
    }
    if (!!this.administrativeAgency.id) {
      searchString["sector-agency-id"] = this.administrativeAgency.id;
      searchString["procedure-agency-id"] = this.administrativeAgency.id;
    }
    if (this.dossierFeeOnlyAgencyIdHCM) {
      searchString["sector-only-agency"] = 1;
    }
    if (formObj.bookName != '') {
      searchString["book-name"] = formObj.bookName;
    }
    if (formObj.invoicePattern != '') {
      searchString["invoice-pattern"] = formObj.invoicePattern;

    }
    if (formObj.invoiceSerial != '') {
      searchString["invoice-serial"] = formObj.invoiceSerial;
    }
    if(this.showStatisticsPaymentDossierFeeHcm?.enable){
      searchString["is-show-statistic-payment-method"] = true;
    }
    if(this.isShowComboboxNewTemplate){
      searchString["is-show-combobox-new-template"] = true;
    }
    switch (type) {
      case "HoSo":
        
        this.dossierService.downloadReportDossierFeeStatictis(searchString, excelFileName);
       
        break;
      case "ThuTuc":
        this.dossierService.downloadExcelDossierFeeByProcedure(searchString,excelFileName);
        
        break;
      case "LinhVuc":
        this.dossierService.downloadExcelDossierFeeBySector(searchString, excelFileName);
     
        break;
      case "CoQuanDonVi":
        this.dossierService.downloadExcelDossierFeeByAgency(searchString, excelFileName);
      
        break;
    }
  } else {
    switch (type) {
      case "HoSo":
        let subAgencyName = this.getAgencyName;
        this.statisticsService.exportToExcelDossierFeeWithReceiptNumber(fromDateExcel, toDateExcel, this.exportExcelData, excelFileName, sheetName, this.showStatisticsPaymentDossierFeeHcm?.enable, subAgencyName, this.isShowComboboxNewTemplate);
        break;
      case "ThuTuc":
        this.statisticsService.exportToExcelDossierFeeByProcedure(fromDateExcel, toDateExcel, this.exportExcelData, excelFileName, sheetName, this.showStatisticsPaymentDossierFeeHcm?.enable);
        break;
      case "LinhVuc":
        this.statisticsService.exportToExcelDossierFeeBySector(fromDateExcel, toDateExcel, this.exportExcelData, excelFileName, sheetName, this.showStatisticsPaymentDossierFeeHcm?.enable);
        break;
      case "CoQuanDonVi":
        this.statisticsService.exportToExcelDossierFeeByAgency(fromDateExcel, toDateExcel, this.exportExcelData, excelFileName, sheetName, this.showStatisticsPaymentDossierFeeHcm?.enable);
        break;
    }
  }

  }
}
