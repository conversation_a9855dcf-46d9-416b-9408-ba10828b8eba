.close-button {
    float: right;
    top: -24px;
    right: -24px;
}

.dialog_title {
    font-style: normal;
    font-weight: 500;
    font-size: 20px;
    color: #ce7a58;
}

::ng-deep {
    .processHandleDialogContent {
        @import "~src/styles/framework.scss";
        @import "~src/styles/custom.scss";

        font-size: 15px;
        min-height: 72vh;
        max-height: 75vh;
        width: 100%;

        ::-webkit-scrollbar{
            width: 5px;
            height: 5px;
            background-color: #f5f5f5;
        }

        ::-webkit-scrollbar-track{
            box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.1);
            background-color: #f5f5f5;
        }

        ::-webkit-scrollbar-thumb {
            box-shadow: inset 0 0 6px rgba(0, 0, 0, .1);
            background-color: #44444450;
        }

        .highlight {
            color: #ce7a58;
        }

        .processName {
            font-weight: 500;
            color: #1e2f41;
            max-width: 100%;
            word-wrap: break-word;
        }

        .imgProcess {
            width: 100%;

            .card {
                flex-wrap: wrap;
                height: 92%;
                overflow-y: scroll;
                .cardTitle {
                    background-color: rgba(88,129,206,.18823529411764706);
                    color: #1e2f41;
                    padding: 0.5em;
                    font-weight: 500;
                    font-size: 16px;
                    border-radius: 4px 4px 0 0;
                    // height: 2em;
                    display: flex;

                    span {
                        align-self: center;
                    }

                    .mat-icon {
                        vertical-align: middle;
                        margin-right: 0.2em;
                    }

                    .cardAction {
                        align-self: center;
                        color: #ce7a58;
                        margin-left: auto;

                        .mat-icon {
                            margin-right: 0.3em;
                        }

                        .mat-spinner {
                            display: none;
                            margin-right: 1em;
                            circle {
                                stroke: #ce7a58;
                            }
                        }

                        .done {
                            display: none;
                            background-color: #fff;
                            padding: 0.3em 0.5em;
                            border-radius: 30px;

                            .mat-icon {
                                vertical-align: middle;
                            }

                            span {
                                font-size: 14px;
                                vertical-align: middle;
                            }
                        }
                    }
                }

                .cardContent {
                    color: #1e2f41;
                    padding: 0.5em;
                    border: 1px solid #e2e2e2;
                    border-radius: 0 0 4px 4px;

                    .date-form {
                        @extend .cmat-form-field;
                    }

                    .mat-icon {
                        vertical-align: bottom;
                        margin-right: 0.2em;
                        color: #666;
                    }

                    p .lbl {
                        font-weight: 500;
                    }

                    p .status {
                        color: #fff;
                        font-weight: 500;
                        padding: 0.25em 1em;
                        border-radius: 15px;
                    }

                    .tblInfo {
                        width: 100%;

                        tr {
                            td:nth-child(1),
                            td:nth-child(3) {
                                font-weight: 500;
                                padding: 0.3em 0;
                            }
                        }
                    }

                    .drag_upload_btn {
                        position: relative;
                        overflow: hidden;
                        display: inline-block;
                        width: 100%;
                        min-height: 3em;
                        text-align: center;
                        align-self: center;
                        cursor: pointer;
                        z-index: 4;

                        button {
                            width: 100%;
                            padding: 0.2em;

                            .mat-icon {
                                margin-right: 0.3em;
                                color: #ce7a58;
                            }

                            a {
                                text-decoration: none;
                                font-weight: 500;
                                cursor: pointer;

                                &:hover {
                                    color: #ce7a58;
                                }

                                &:visited {
                                    color: #ce7a58;
                                }
                            }
                        }

                        input[type="file"] {
                            position: absolute;
                            left: 0;
                            top: 0;
                            opacity: 0;
                            height: 100%;
                            width: 100%;
                            cursor: pointer;
                        }

                        .fileUploadPreview {
                            width: 100%;
                            display: flex;
                            flex-wrap: wrap;
                            justify-content: left;

                            .listUploaded {
                                display: flex;
                                height: 4em;
                                width: 100%;
                                margin: 0.5em 0;
                                .fileInfo {
                                    height: 100%;
                                    width: 90%;
                                    margin: 0 0.5% 1em 0.5%;
                                    background-color: #3e3e3e17;
                                    border-radius: 4px;
                                    display: flex;

                                    .fileIcon {
                                        width: 2.5em;
                                        height: 2.5em;
                                        background-position: center;
                                        background-size: 100%;
                                        align-self: center;
                                        margin-left: 1em;
                                        background-repeat: no-repeat;
                                    }

                                    .deleteFile {
                                        margin-left: auto;
                                        align-self: center;
                                        color: #1e2f41;
                                    }

                                    .dGrid {
                                        text-align: left;
                                        align-self: center;
                                        margin: 0 1em;
                                        width: 75%;

                                        .fileName {
                                            color: #1e2f41;
                                            font-weight: 500;
                                            margin: 0;
                                            width: 80%;
                                            white-space: nowrap;
                                            overflow: hidden;
                                            text-overflow: ellipsis;
                                        }

                                        .fileSize {
                                            color: #1e2f41;
                                            margin: 0;
                                            width: 80%;
                                        }
                                    }
                                }
                                .moreBtn {
                                    align-self: center;
                                    color: #1e2f41;
                                }
                            }
                        }
                    }

                    .countdownDate {
                        display: inline-flex;

                        .count-down {
                            padding: 0 0.5em !important;

                            .count-down-row:nth-child(1) {
                                div {
                                    width: unset !important;
                                    padding-right: 0.5em;

                                    span {
                                        letter-spacing: 0;
                                        font-size: 15px;
                                        color: #1e2f41;
                                        text-align: center;
                                        font-weight: 500;
                                    }
                                }
                            }

                            .count-down-row:nth-child(2) {
                                display: none;
                            }
                        }

                        &.transVI {
                            .count-down {
                                .count-down-row:nth-child(1) {
                                    div:nth-child(1)::after {
                                        content: "ngày";
                                    }

                                    div:nth-child(2)::after {
                                        content: "giờ";
                                    }

                                    div:nth-child(3)::after {
                                        content: "phút";
                                    }

                                    div:nth-child(4)::after {
                                        content: "giây";
                                    }
                                }
                            }
                        }

                        &.transEN {
                            .count-down {
                                .count-down-row:nth-child(1) {
                                    div:nth-child(1)::after {
                                        content: "day";
                                    }

                                    div:nth-child(2)::after {
                                        content: "hour";
                                    }

                                    div:nth-child(3)::after {
                                        content: "minute";
                                    }

                                    div:nth-child(4)::after {
                                        content: "second";
                                    }
                                }
                            }
                        }
                    }

                    .overdue {
                        color: #de1212;
                    }

                    .timer {
                        padding: 0.3em 0.5em;
                        background-color: #ececec;
                        border-radius: 4px;
                        margin-right: 0.2em;
                    }

                    .timeNumber {
                        letter-spacing: 0;
                        font-size: 15px;
                        text-align: center;
                        font-weight: 500;
                    }
                }
            }

            .diagramViewer {
                .diagram-container {
                    height: 39.7vh;
                    border: 1px solid #e2e2e2;
                    border-radius: 0 0 4px 4px
                }
            }
        }

        .taskProcess {
            overflow: scroll;
            height: 60vh;
            .card {
                flex-wrap: wrap;
                margin: 0 0 1em 0;
                .cardTitle {
                    background-color: rgba(88,129,206,.18823529411764706);
                    color: #1e2f41;
                    padding: 0.5em;
                    font-weight: 500;
                    font-size: 16px;
                    border-radius: 4px 4px 0 0;
                    // height: 2em;
                    display: flex;

                    span {
                        align-self: center;
                    }

                    .mat-icon {
                        vertical-align: middle;
                        margin-right: 0.2em;
                    }

                    .cardAction {
                        align-self: center;
                        color: #ce7a58;
                        margin-left: auto;

                        .mat-icon {
                            margin-right: 0.3em;
                        }

                        .mat-spinner {
                            display: none;
                            margin-right: 1em;
                            circle {
                                stroke: #ce7a58;
                            }
                        }

                        .done {
                            display: none;
                            background-color: #fff;
                            padding: 0.3em 0.5em;
                            border-radius: 30px;

                            .mat-icon {
                                vertical-align: middle;
                            }

                            span {
                                font-size: 14px;
                                vertical-align: middle;
                            }
                        }
                    }
                }

                .cardContent {
                    color: #1e2f41;
                    padding: 0.5em;
                    border: 1px solid #e2e2e2;
                    height: 80%;
                    border-radius: 0 0 4px 4px;

                    .date-form {
                        @extend .cmat-form-field;

                        .div-assignee {
                            margin-bottom: 12px;
                            display: flex;
                            justify-content: space-between;
                            align-items: center;

                            .btnListAssignee{
                                background-color: #CE7A58;
                                color: #fff;
                                margin: 0px;
                                padding: 0px 5px
                            }
                        }
                    }

                    .mat-icon {
                        vertical-align: bottom;
                        margin-right: 0.2em;
                        color: #666;
                    }

                    p .lbl {
                        font-weight: 500;
                    }

                    p .status {
                        color: #fff;
                        font-weight: 500;
                        padding: 0.25em 1em;
                        border-radius: 15px;
                    }

                    .tblInfo {
                        width: 100%;

                        tr {
                            td:nth-child(1),
                            td:nth-child(3) {
                                font-weight: 500;
                                padding: 0.3em 0;
                            }
                        }
                    }

                    .drag_upload_btn {
                        position: relative;
                        overflow: hidden;
                        display: inline-block;
                        width: 100%;
                        min-height: 3em;
                        text-align: center;
                        align-self: center;
                        cursor: pointer;
                        z-index: 4;

                        button {
                            width: 100%;
                            padding: 0.2em;

                            .mat-icon {
                                margin-right: 0.3em;
                                color: #ce7a58;
                            }

                            a {
                                text-decoration: none;
                                font-weight: 500;
                                cursor: pointer;

                                &:hover {
                                    color: #ce7a58;
                                }

                                &:visited {
                                    color: #ce7a58;
                                }
                            }
                        }

                        input[type="file"] {
                            position: absolute;
                            left: 0;
                            top: 0;
                            opacity: 0;
                            height: 100%;
                            width: 100%;
                            cursor: pointer;
                        }

                        .fileUploadPreview {
                            width: 100%;
                            display: flex;
                            flex-wrap: wrap;
                            justify-content: left;

                            .listUploaded {
                                display: flex;
                                height: 4em;
                                width: 100%;
                                margin: 0.5em 0;
                                .fileInfo {
                                    height: 100%;
                                    width: 90%;
                                    margin: 0 0.5% 1em 0.5%;
                                    background-color: #3e3e3e17;
                                    border-radius: 4px;
                                    display: flex;

                                    .fileIcon {
                                        width: 2.5em;
                                        height: 2.5em;
                                        background-position: center;
                                        background-size: 100%;
                                        align-self: center;
                                        margin-left: 1em;
                                        background-repeat: no-repeat;
                                    }

                                    .deleteFile {
                                        margin-left: auto;
                                        align-self: center;
                                        color: #1e2f41;
                                    }

                                    .dGrid {
                                        text-align: left;
                                        align-self: center;
                                        margin: 0 1em;
                                        width: 75%;

                                        .fileName {
                                            color: #1e2f41;
                                            font-weight: 500;
                                            margin: 0;
                                            width: 80%;
                                            white-space: nowrap;
                                            overflow: hidden;
                                            text-overflow: ellipsis;
                                        }

                                        .fileSize {
                                            color: #1e2f41;
                                            margin: 0;
                                            width: 80%;
                                        }
                                    }
                                }
                                .moreBtn {
                                    align-self: center;
                                    color: #1e2f41;
                                }
                            }
                        }
                    }

                    .countdownDate {
                        display: inline-flex;

                        .count-down {
                            padding: 0 0.5em !important;

                            .count-down-row:nth-child(1) {
                                div {
                                    width: unset !important;
                                    padding-right: 0.5em;

                                    span {
                                        letter-spacing: 0;
                                        font-size: 15px;
                                        color: #1e2f41;
                                        text-align: center;
                                        font-weight: 500;
                                    }
                                }
                            }

                            .count-down-row:nth-child(2) {
                                display: none;
                            }
                        }

                        &.transVI {
                            .count-down {
                                .count-down-row:nth-child(1) {
                                    div:nth-child(1)::after {
                                        content: "ngày";
                                    }

                                    div:nth-child(2)::after {
                                        content: "giờ";
                                    }

                                    div:nth-child(3)::after {
                                        content: "phút";
                                    }

                                    div:nth-child(4)::after {
                                        content: "giây";
                                    }
                                }
                            }
                        }

                        &.transEN {
                            .count-down {
                                .count-down-row:nth-child(1) {
                                    div:nth-child(1)::after {
                                        content: "day";
                                    }

                                    div:nth-child(2)::after {
                                        content: "hour";
                                    }

                                    div:nth-child(3)::after {
                                        content: "minute";
                                    }

                                    div:nth-child(4)::after {
                                        content: "second";
                                    }
                                }
                            }
                        }
                    }

                    .overdue {
                        color: #de1212;
                    }

                    .timer {
                        padding: 0.3em 0.5em;
                        background-color: #ececec;
                        border-radius: 4px;
                        margin-right: 0.2em;
                    }

                    .timeNumber {
                        letter-spacing: 0;
                        font-size: 15px;
                        text-align: center;
                        font-weight: 500;
                    }
                }
            }
        }
    }

    .applyBtn {
        margin: 1em .5em;
        background-color: #CE7A58;
        color: #fff;
        height: 3em;
        min-width: 20%;
    
        .mat-icon {
            vertical-align: middle;
            margin-right: .2em;
        }
    }
}