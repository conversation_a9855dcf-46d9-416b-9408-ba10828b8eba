import { Component, OnInit, ChangeDetectorRef, AfterViewInit, ViewChild, OnDestroy } from '@angular/core';
import { FormGroup, FormControl } from '@angular/forms';
import { Router } from '@angular/router';
import { EnvService } from 'src/app/core/service/env.service';
import { MatTableDataSource } from '@angular/material/table';
import { DatePipe } from '@angular/common';
import { ExportExcelService } from 'src/app/data/service/export-excel/export-excel.service';
import { MainService } from 'src/app/data/service/main/main.service';
import { ProcedureService } from 'src/app/data/service/procedure/procedure.service';
import { takeUntil } from 'rxjs/operators';
import { ReplaySubject, Subject } from 'rxjs';
import { ActivatedRoute } from '@angular/router';
import { DossierService } from 'src/app/data/service/dossier/dossier.service';
import { NgxPrinterService } from 'ngx-printer';
import { SnackbarService } from 'src/app/data/service/snackbar/snackbar.service';
import { MatSelect } from '@angular/material/select';
import { ReportService } from 'data/service/report/report.service';
import { DeploymentService } from 'data/service/deployment.service';
import * as tUtils from 'src/app/data/service/thoai.service';
import { CookieService } from 'ngx-cookie-service';
import { DLKStatisticService } from 'src/app/data/service/dlk-dac-thu/dlk-statistic.service';
import { Workbook } from 'exceljs';
import * as fs from 'file-saver';

const EXCEL_TYPE = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8';
const EXCEL_EXTENSION = '.xlsx';
export interface Agency {
  id: string;
  name: string;
}

@Component({
  selector: 'app-dlk-hosohuy',
  templateUrl: './dlk-hosohuy.component.html',
  styleUrls: [
    './dlk-hosohuy.component.scss',
    '/src/app/app.component.scss',
    '/src/app/shared/scss/form-field-outline.scss'
  ]
})

export class DlkHoSoHuyComponent implements OnInit, AfterViewInit, OnDestroy {
  listStatus: any[] = [
    { value: "6", text: "Đã hủy", isDisabled: true },
    { value: "12", text: "Dừng xử lý", isDisabled: false },
  ];
  nowDate = tUtils.newDate();

  // tslint:disable-next-line: max-line-length
  toDate = this.nowDate.getFullYear() + '-' + ((this.nowDate.getMonth() + 1) <= 9 ? '0' + (this.nowDate.getMonth() + 1) : (this.nowDate.getMonth() + 1)) + '-' + ((this.nowDate.getDate() <= 9) ? ('0' + this.nowDate.getDate()) : this.nowDate.getDate());
  // tslint:disable-next-line: max-line-length
  fromDate = this.nowDate.getFullYear() + '-' + ((this.nowDate.getMonth() + 1) <= 9 ? '0' + (this.nowDate.getMonth() + 1) : (this.nowDate.getMonth() + 1)) + '-01';

  keyword = '';
  config = this.envService.getConfig();
  selectedLang = localStorage.getItem('language') || 'vi';
  selectedLangId = Number(localStorage.getItem('languageId')) || 228;
  pageTitle = {
    vi: 'Danh sách hồ sơ hủy',
    en: 'Logbook statistics'
  };

  protected onDestroy = new Subject<void>();
  // =================== old
  env: any = this.deploymentService.getAppDeployment()?.env;
  ELEMENTDATA: any[] = [];
  dataSource: MatTableDataSource<any>;
  // listSector: Array<any> = [];
  keySearchSectorAgency = '';
  // Search Sector
  keywordSector = '';
  totalPagesSector = 0;
  currentPageSector = 0;
  pageSizeSector = 100;

  parentAgency = '';
  agencyId = '';
  userAgency?: any = JSON.parse(localStorage.getItem('userAgency'));
  userAgencyCount?: any = JSON.parse(localStorage.getItem('listAgencyUser'))?.length || 0;
  ELEMENTDATAALL: any[] = [];
  dataSourceAll: MatTableDataSource<any>;
  listHoSo = [];
  listExport = [];

  size = 10;
  pageIndex = 1;
  page = 1;
  countResult = 0;
  pgSizeOptions = this.config.pageSizeOptions;
  paramsQuery = {
    page: 0,
    size: 10,
    agency: '',
    parentAgency: '',
    fromDate: '',
    toDate: '',
    applyMethod: "-1",
    receivingKind: '',
    sector: '',
    keyword: '',
    procedure: '',
    sortId: '',
    isTTPVHCC: 1,
    all: 0,
    agencyName: '',
    dossierStatus: "6",
    childAgencyList: []
  };
  isCancelStatus: boolean = true;

  listAgencyAccept = [];
  listAgency = [];
  listAgencyRoot = [];
  keywordAgencyAccept = '';
  totalPagesAgencyAccept = 0;
  currentPageAgencyAccept = 0;
  pageSizeAgencyAccept = 1000;
  startDate = new Date();
  endDate = new Date();
  flagTTPVHCC = true;
  timeOutAgencyAccept: any = null;
  listSectorfillter: any[] = [];
  listSector: any[] = [];
  listProcedure: any[] = [];
  listProcedurefillter: any[] = [];
  totalPagesProcedure = 0;
  currentPageProcedure = 0;
  searchProcedureKeyword = "";
  listHinhThucNhan: any[] = [];
  waitingDownloadExcel = false;
  @ViewChild('procedureMatSelectInfiniteScroll', { static: true }) procedureMatSelectInfiniteScroll: MatSelect;


  constructor(
    private envService: EnvService,
    public datepipe: DatePipe,
    private mainService: MainService,
    private cdRef: ChangeDetectorRef,
    private exportExcelService: ExportExcelService,
    private procedureService: ProcedureService,
    private activeRoute: ActivatedRoute,
    private datePipe: DatePipe,
    private dossierService: DossierService,
    private snackbarService: SnackbarService,
    private router: Router,
    private printerService: NgxPrinterService,
    private reportService: ReportService,
    private deploymentService: DeploymentService,
    private cookieService: CookieService,
    private dlkStatisticService: DLKStatisticService
  ) {
    this.dataSource = new MatTableDataSource(this.ELEMENTDATA);
    this.dataSourceAll = new MatTableDataSource(this.ELEMENTDATAALL);
  }
  ngOnInit(): void {
    this.mainService.setPageTitle(this.pageTitle[localStorage.getItem('language')]);
    this.startDate = new Date(this.fromDate);
    this.endDate = new Date(this.toDate);

    // Kiem tra agency
    if (this.userAgency !== null && this.userAgency !== undefined) {
      if (!!this.userAgency.parent && !!this.userAgency.parent.id && this.userAgencyCount === 1) {
        this.parentAgency = this.userAgency.parent.id;
        this.agencyId = this.userAgency.id;
        this.paramsQuery.agencyName = this.userAgency.parent.name;
      } else {
        this.parentAgency = this.userAgency.id;
        this.agencyId = this.userAgency.id;
        this.paramsQuery.agencyName = this.userAgency.name;
      }

      this.keySearchSectorAgency = '&agency-id=' + this.agencyId;
    }

    this.paramsQuery.parentAgency = this.parentAgency;
    this.getListAgencyAccept(this.keywordAgencyAccept, this.currentPageAgencyAccept, this.pageSizeAgencyAccept);
    this.getListSector();
  }

  ngAfterViewInit() {
    this.cdRef.detectChanges();
  }

  ngOnDestroy() {
    this.onDestroy.next();
    this.onDestroy.complete();
  }

  thongKe() {
    this.paramsQuery.page = 0;
    this.page = 1;
    this.paramsQuery.all = 0;
    this.getListHoSo();
  }

  paginate(event) {
    this.paramsQuery.page = event;
    this.paramsQuery.all = 0;
    this.getListHoSo();
  }

  getListHoSo() {
    this.isCancelStatus = this.paramsQuery.dossierStatus == "6";
    this.paramsQuery.fromDate = (this.startDate ? this.datePipe.transform(this.startDate, 'dd/MM/yyyy') + 'T00:00:00.000Z' : '');
    this.paramsQuery.toDate = (this.endDate ? this.datePipe.transform(this.endDate, 'dd/MM/yyyy') + 'T23:59:59.999Z' : '');

    // Kiểm tra validate
    if (this.validateForm()) {
      this.dlkStatisticService.getDossierTheoDoi(this.paramsQuery).subscribe(res => {
        this.listHoSo = res.content;
        this.countResult = res.totalElements;
      }, err => {
        console.log(err);
      });
    }
  }

  getListHinhThucNhan() {
    // tslint:disable-next-line:max-line-length
    this.dlkStatisticService.getHinhThucNhan().subscribe(res => {
      this.listHinhThucNhan = res.content;

    }, err => {
      console.log(err);
    });
  }

  getAgencyScroll() {
    this.currentPageAgencyAccept += 1;
    this.getListAgencyAccept(this.keywordAgencyAccept, this.currentPageAgencyAccept, this.pageSizeAgencyAccept);
  }

  getListAgencyAccept(keyword, page, size) {
    const searchString = '?parent-id=' + this.parentAgency + '&keyword=' + keyword + '&page=' + page + '&size=' + size + '&sort=name.name,asc&status=1';

    this.procedureService.getListAgencyWithParent(searchString).subscribe(res => {
      if (page === 0) {
        this.listAgency = res.content;
        this.listAgencyRoot = res.content;
      } else {
        this.listAgency = this.listAgency.concat(res.content);
        this.listAgencyRoot = this.listAgency.concat(res.content);
      }

      this.totalPagesAgencyAccept = res.totalPages;
      this.listAgencyAccept = this.listAgency;
      this.paramsQuery.childAgencyList = [];

      if (this.listAgency != null && this.listAgency.length > 0) {
        // Khoi tao danh sách child agency
        this.listAgency.forEach(item => {
          if (item != null && item.id != null) {
            this.paramsQuery.childAgencyList.push(item.id);
          }
        })

        // Kiem tra la parent root he thong thi lay tat ca agency so, huyen
        if (this.userAgency != null && (this.userAgency?.parent == null || this.userAgency?.parent == "")) {
          for (let i = 0; i < this.listAgency.length; i++) {
            if (this.listAgency[i] != null && this.listAgency[i].id != null) {
              let params = '?parent-id=' + this.parentAgency + '&page=0&size=1000&sort=name.name,asc&status=1';

              this.procedureService.getListAgencyWithParent(params).subscribe(res => {
                if (res != null && res.content?.length > 0) {
                  res.content.forEach(item => {
                    if (item != null && item.id != null) {
                      this.paramsQuery.childAgencyList.push(item.id);
                    }
                  })
                }
              })
            }
          }
        }
      }
    }, err => {
      console.log(err);
    });
  }

  getListAgencyParams(keyword, page, size, agencyId) {
    const searchString = '?parent-id=' + agencyId + '&keyword=' + keyword + '&page=' + page + '&size=' + size + '&sort=name.name,asc&status=1';

    this.procedureService.getListAgencyWithParent(searchString).subscribe(res => {
      this.listAgency = res.content;
      this.paramsQuery.childAgencyList = [];

      if (this.listAgency != null && this.listAgency.length > 0) {
        this.listAgency.forEach(item => {
          if (item != null && item.id != null) {
            this.paramsQuery.childAgencyList.push(item.id);
          }
        })
      }
    }, err => {
      console.log(err);
    });
  }

  searchAngency(event) {
    if (event != "") {
      this.listAgencyAccept = this.listAgencyRoot.filter(agency =>
        agency.name.toLowerCase().trim().includes(event.toLowerCase()) == true);
    } else {
      this.listAgencyAccept = this.listAgencyRoot;
    }
  }

  onEnter(event) {
    clearTimeout(this.timeOutAgencyAccept);
    this.timeOutAgencyAccept = setTimeout(async () => {
      this.keywordAgencyAccept = event.target.value;
      this.currentPageAgencyAccept = 0;
      this.getListAgencyAccept(this.keywordAgencyAccept, this.currentPageAgencyAccept, this.pageSizeAgencyAccept);
      this.keywordSector = '';
      this.totalPagesSector = 0;
      this.currentPageSector = 0;
      this.pageSizeSector = 100;
      this.listSector = [];
      this.getListSector();
    }, 300);
  }

  async resetSearchForm(type) {
    switch (type) {
      case 'agencyAccept': {
        this.currentPageAgencyAccept = 0;
        this.keywordAgencyAccept = '';
        this.getListAgencyAccept(this.keywordAgencyAccept, this.currentPageAgencyAccept, this.pageSizeAgencyAccept);

        this.keywordSector = '';
        this.totalPagesSector = 0;
        this.currentPageSector = 0;
        this.pageSizeSector = 100;
        break;
      }
    }
  }

  changeAgencyAccept() {
    console.log("changeAgencyAccept: ", this.paramsQuery?.agency);
    if (this.paramsQuery?.agency) {
      this.getListAgencyParams(this.keywordAgencyAccept, this.currentPageAgencyAccept, this.pageSizeAgencyAccept, this.paramsQuery?.agency);
    } else {
      this.getListAgencyAccept(this.keywordAgencyAccept, this.currentPageAgencyAccept, this.pageSizeAgencyAccept);
    }

    if (this.paramsQuery.agency !== '') {
      this.keySearchSectorAgency = '&agency-id=' + this.paramsQuery.agency;
    } else {
      if (this.parentAgency !== '') {
        this.keySearchSectorAgency = '&agency-id=' + this.parentAgency;
      }
      else {
        this.keySearchSectorAgency = '';
      }
    }
    this.keywordSector = '';
    this.totalPagesSector = 0;
    this.currentPageSector = 0;
    this.pageSizeSector = 100;
    this.listSector = [];
    this.getListSector();
  }

  getListSector() {
    // tslint:disable-next-line:max-line-length
    const searchString = '?keyword=' + this.keywordSector + '&page=' + this.currentPageSector + '&size=' + this.pageSizeSector + '&spec=page&sort=name.name,asc&status=1&only-agency-id=1' + this.keySearchSectorAgency;
    this.procedureService.getListSector(searchString).subscribe(res => {
      if (this.currentPageSector === 0) {
        this.listSector = res.content;
      } else {
        this.listSector = this.listSector.concat(res.content);
      }
      this.totalPagesSector = res.totalPages;
      this.listSectorfillter = this.listSector;
    }, err => {
      console.log(err);
    });
  }

  getListSectorScroll() {
    this.currentPageSector += 1;
    this.getListSector();
  }

  searchSector(event) {
    if (event != "") {
      this.listSectorfillter = this.listSector.filter(sector =>
        sector.name.toLowerCase().trim().includes(event.toLowerCase()) == true);
    } else {
      this.listSectorfillter = this.listSector;
    }
  }

  sectorChange() {
    this.listProcedure = [];
    this.listProcedurefillter = [];
    this.currentPageProcedure = 0;
    this.paramsQuery.procedure = "";
    if (this.paramsQuery.sector != "") {
      this.getListProcedure();
    }
  }

  onChangeStatus(e) {
  }

  getListProcedureScroll() {
    this.currentPageProcedure += 1;
    this.getListProcedure();
  }
  getListProcedure() {
    const searchString =
      '?status=1&sort=translate.name,asc&keyword=' + this.searchProcedureKeyword +
      '&spec=page&page=' + this.currentPageProcedure + '&size=50' +
      '&sector-id=' + this.paramsQuery.sector;
    this.procedureService.getListProcedure(searchString).subscribe(data => {
      if (this.currentPageProcedure == 0) {
        this.listProcedure = data.content;
      } else {
        this.listProcedure = this.listProcedure.concat(data.content);
      }
      this.totalPagesProcedure = data.totalPages;
      this.listProcedurefillter = this.listProcedure;
    }, err => {
      console.log(err);
    });
  }

  searchProvedure(event) {
    if (event != "") {
      this.searchProcedureKeyword = event;
      // this.listProcedurefillter = this.listProcedure.filter(pro =>
      //   pro.name.toLowerCase().trim().includes(event.toLowerCase()) == true);
    } else {
      this.searchProcedureKeyword = "";
      // this.currentPageProcedure = 0;
      // this.getListProcedure();
      //this.listProcedurefillter = this.listProcedure;
    }
    this.currentPageProcedure = 0;
    this.getListProcedure();
  }

  generateAddress(data) {
    if (data != null) {
      const address = [];
      if (data?.address !== undefined && data?.address !== null) {
        address.push(data.address);
      }
      if (data?.village !== undefined && data?.village !== null) {
        address.push(data.village.label);
      }
      if (data?.district !== undefined && data?.district !== null) {
        address.push(data.district.label);
      }
      if (data?.province !== undefined && data?.province !== null) {
        address.push(data.province.label);
      }
      return address.join(', ');
    } else {
      return "";
    }
  }

  getFullName(data) {
    if (data != null) {
      if (data?.fullName != undefined && data?.fullName != null && data?.fullName != "") {
        return data.fullName;
      } else if (data?.fullname != undefined && data?.fullname != null && data?.fullname != "") {
        return data.fullname;
      } else if (data?.ownerFullname != undefined && data?.ownerFullname != null && data?.ownerFullname != "") {
        return data.ownerFullname;
      } else {
        return "";
      }
    }
  }

  getCancelInfo(data, index) {
    let value = "";

    // Get Time
    if (index == 1) {
      if (data?.userWithdraw?.length > 0 && data?.userWithdraw[0]?.updatedDate != undefined
        && data?.userWithdraw[0]?.updatedDate != "") {
        value = this.datePipe.transform(data?.userWithdraw[0]?.updatedDate, 'dd/MM/yyyy HH:mm:ss');
      }
    } else if (data?.updatedDate != undefined && data?.updatedDate != "") {
      value = data?.updatedDate
    }

    // Get username
    if (index == 2) {
      if (data?.userWithdraw?.length > 0) {
        if (data?.userWithdraw[0]?.user?.fullname != undefined && data?.userWithdraw[0]?.user?.fullname != null && data?.userWithdraw[0]?.user?.fullname != "") {
          value = data?.userWithdraw[0]?.user?.fullname;
        } else {
          value = data?.userWithdraw[0]?.user?.fullName;
        }
      } else if (data.userRefuse != undefined) {
        if (data.userRefuse.length > 0) {
          if (data?.userRefuse[0]?.user?.fullname != undefined && data?.userRefuse[0]?.user?.fullname != null && data?.userRefuse[0]?.user?.fullname != "") {
            value = data.userRefuse[0]?.user?.fullname;
          } else {
            value = data.userRefuse[0]?.user?.fullName;
          }
        }
      }
    }

    // Get comment
    if (index == 3) {
      if (data?.userWithdraw?.length > 0 && data?.userWithdraw[0]?.comment != null && data?.userWithdraw[0]?.comment != "") {
        value = data?.userWithdraw[0]?.comment;
      } else if (data?.dossierStatus?.comment != null && data?.dossierStatus?.comment != "") {
        value = data?.dossierStatus?.comment;
      } else if (data.userRefuse != undefined) {
        if (data.userRefuse.length > 0) {
          value = data.userRefuse[0]?.comment;
        }
      }
    }

    return value ?? "";
  }

  getPauseInfo(data, index) {
    let value = "";

    // Get Time
    if (index == 1) {
      if (data?.stateChangeDate != undefined && data?.stateChangeDate != "") {
        value = this.datePipe.transform(data?.stateChangeDate, 'dd/MM/yyyy HH:mm:ss');
      } else if (data?.updatedDate != undefined && data?.updatedDate != "") {
        value = data?.updatedDate
      }
    }

    // Get username
    if (index == 2) {
      if (data?.userWithdraw != undefined && data?.userWithdraw?.length > 0) {
        if (data?.userWithdraw[0]?.user?.fullname != undefined && data?.userWithdraw[0]?.user?.fullname != null && data?.userWithdraw[0]?.user?.fullname != "") {
          value = data?.userWithdraw[0]?.user?.fullname;
        } else {
          value = data?.userWithdraw[0]?.user?.fullName;
        }
      } else if (data.userPauses != undefined) {
        if (data.userPauses.length > 0) {
          if (data?.userPauses[0]?.user?.fullname != undefined && data?.userPauses[0]?.user?.fullname != null && data?.userPauses[0]?.user?.fullname != "") {
            value = data.userPauses[0]?.user?.fullname;
          } else {
            value = data.userPauses[0]?.user?.fullName;
          }
        }
      } else if (data.currentTask != undefined && data.currentTask?.length > 0 && data.currentTask[0]?.sender?.fullname != undefined) {
        if (data?.currentTask[0]?.sender?.fullname != undefined && data?.currentTask[0]?.sender?.fullname != null && data?.currentTask[0]?.sender?.fullname != "") {
          value = data.currentTask[0]?.sender?.fullname;
        } else {
          value = data.currentTask[0]?.sender?.fullName;
        }
      }
    }

    // Get comment
    if (index == 3) {
      if (data?.dossierStatus?.comment != null && data?.dossierStatus?.comment != "") {
        value = data?.dossierStatus?.comment;
      } else if (data?.notifyContent != undefined) {
        value = data?.notifyContent;
      }
    }

    return value;
  }

  getTienDo(data) {
    var ngayHoanThanh = this.nowDate;
    var ngayHenTra = this.nowDate;
    var finish = false;
    if (data.appointmentDate != undefined && data.appointmentDate != "") {
      ngayHenTra = new Date(data.appointmentDate);
    } else {
      return "Còn hạn";
    }
    if (data.completedDate != undefined && data.completedDate != "") {
      ngayHoanThanh = new Date(data.completedDate);
      finish = true;
    }

    var result = ngayHoanThanh.getTime() - ngayHenTra.getTime();
    if (result < 0) {
      if (finish) {
        return "Đúng hạn";
      } else {
        return "Còn hạn";
      }
    } else {
      return "Quá hạn";
    }
  }

  getTen(data) {
    if (data != null && data.length > 0) {
      var ten = data[0].name;
      data.forEach(element => {
        if (element.languageId == 228) {
          ten = element.name;
        }
      });
      return ten;
    } else {
      return "";
    }
  }

  getHuyHoSo(data, loai) {
    // loai = 0 : get nguoi huy, loai = 1: get noi dung huy
    if (data != null) {
      if (data.userRefuse != undefined) {
        if (data.userRefuse.length > 0) {
          return loai == 0 ? data.userRefuse[0]?.user?.fullname : data.userRefuse[0]?.comment;
        } else {
          return "";
        }

      } else {
        return "";
      }

    } else {
      return "";
    }
  }

  xuatExcel() {
    this.paramsQuery.all = 1;
    this.paramsQuery.fromDate = (this.startDate ? this.datePipe.transform(this.startDate, 'dd/MM/yyyy') + 'T00:00:00.000Z' : '');
    this.paramsQuery.toDate = (this.endDate ? this.datePipe.transform(this.endDate, 'dd/MM/yyyy') + 'T23:59:59.999Z' : '');

    if (this.validateForm()) {
      this.waitingDownloadExcel = true;
      this.dlkStatisticService.getDossierTheoDoi(this.paramsQuery).subscribe(res => {
        this.listExport = res.content;
        this.exportLogbookExcel(this.listExport);
        this.waitingDownloadExcel = false;
      }, err => {
        console.log(err);
        this.waitingDownloadExcel = false;
      });
    }
  }

  public exportLogbookExcel(listExport) {
    // create workbook and worksheet
    const workbook = new Workbook();
    workbook.creator = 'Snippet Coder';
    workbook.lastModifiedBy = 'SnippetCoder';
    workbook.created = new Date();
    workbook.modified = new Date();
    const worksheet = workbook.addWorksheet("SỔ THEO DÕI HỒ SƠ");
    worksheet.properties.outlineLevelCol = 2;
    worksheet.properties.defaultRowHeight = 16;

    // Add header row
    worksheet.mergeCells('A1:C1');
    worksheet.getCell('A1').value = "UBND TỈNH ĐẮK LẮK";
    // tslint:disable-next-line: max-line-length
    worksheet.getCell('A1').style = { font: { bold: true, name: 'Times New Roman', size: 11 }, alignment: { horizontal: 'center', vertical: 'middle' } };
    // Add header row
    worksheet.mergeCells('A2:C2');
    worksheet.getCell('A2').value = this.paramsQuery.agencyName;
    // tslint:disable-next-line: max-line-length
    worksheet.getCell('A2').style = { font: { bold: true, name: 'Times New Roman', size: 11 }, alignment: { horizontal: 'center', vertical: 'middle' } };

    worksheet.mergeCells('F1:H1');
    worksheet.getCell('F1').value = "CỘNG HÒA XÃ HỘI CHỦ NGHĨA VIỆT NAM";
    // tslint:disable-next-line: max-line-length
    worksheet.getCell('F1').style = { font: { bold: true, name: 'Times New Roman', size: 11 }, alignment: { horizontal: 'center', vertical: 'middle' } };

    worksheet.mergeCells('F2:H2');
    worksheet.getCell('F2').value = "Độc lập - Tự do - Hạnh phúc";
    // tslint:disable-next-line: max-line-length
    worksheet.getCell('F2').style = { font: { bold: true, name: 'Times New Roman', size: 11 }, alignment: { horizontal: 'center', vertical: 'middle' } };

    worksheet.mergeCells('A4:H4');
    worksheet.getCell('A4').value = "DANH SÁCH HỒ SƠ " + (this.paramsQuery.dossierStatus == "6" ? "HỦY" : "DỪNG XỬ LÝ");
    // tslint:disable-next-line: max-line-length
    worksheet.getCell('A4').style = { font: { bold: true, name: 'Times New Roman', size: 16 }, alignment: { horizontal: 'center', vertical: 'middle' } };

    worksheet.mergeCells('A5:H5');
    worksheet.getCell('A5').value = "Từ ngày " + (this.paramsQuery.fromDate.split("T"))[0] + " tới ngày " + (this.paramsQuery.toDate.split("T"))[0];
    // tslint:disable-next-line: max-line-length
    worksheet.getCell('A5').style = { font: { bold: false, name: 'Times New Roman', size: 11 }, alignment: { horizontal: 'center', vertical: 'middle' } };

    worksheet.getCell("A7").value = "STT";
    // tslint:disable-next-line: max-line-length
    worksheet.getCell("A7").style = { font: { bold: true, name: 'Times New Roman', size: 11 }, alignment: { horizontal: 'center', vertical: 'middle', wrapText: true }, fill: { type: 'pattern', pattern: 'darkVertical', fgColor: { argb: 'FFBFBFBF' }, bgColor: { argb: 'FFBFBFBF' } } };
    worksheet.getCell("A7").border = { top: { style: 'thin' }, bottom: { style: 'thin' }, left: { style: 'thin' }, right: { style: 'thin' } };

    worksheet.getCell("B7").value = "Số hồ sơ";
    // tslint:disable-next-line: max-line-length
    worksheet.getCell("B7").style = { font: { bold: true, name: 'Times New Roman', size: 11 }, alignment: { horizontal: 'center', vertical: 'middle', wrapText: true }, fill: { type: 'pattern', pattern: 'darkVertical', fgColor: { argb: 'FFBFBFBF' }, bgColor: { argb: 'FFBFBFBF' } } };
    worksheet.getCell("B7").border = { top: { style: 'thin' }, left: { style: 'thin' }, right: { style: 'thin' } };
    worksheet.getCell("B8").border = { bottom: { style: 'thin' }, left: { style: 'thin' }, right: { style: 'thin' } };

    worksheet.getCell("C7").value = "Về vệc";
    // tslint:disable-next-line: max-line-length
    worksheet.getCell("C7").style = { font: { bold: true, name: 'Times New Roman', size: 11 }, alignment: { horizontal: 'center', vertical: 'middle', wrapText: true }, fill: { type: 'pattern', pattern: 'darkVertical', fgColor: { argb: 'FFBFBFBF' }, bgColor: { argb: 'FFBFBFBF' } } };
    worksheet.getCell("C7").border = { top: { style: 'thin' }, bottom: { style: 'thin' }, left: { style: 'thin' }, right: { style: 'thin' } };

    worksheet.getCell("D7").value = "Người đăng ký";
    // tslint:disable-next-line: max-line-length
    worksheet.getCell("D7").style = { font: { bold: true, name: 'Times New Roman', size: 11 }, alignment: { horizontal: 'center', vertical: 'middle', wrapText: true }, fill: { type: 'pattern', pattern: 'darkVertical', fgColor: { argb: 'FFBFBFBF' }, bgColor: { argb: 'FFBFBFBF' } } };
    worksheet.getCell("D7").border = { top: { style: 'thin' }, bottom: { style: 'thin' }, left: { style: 'thin' }, right: { style: 'thin' } };

    worksheet.getCell("E7").value = "Địa chỉ";
    // tslint:disable-next-line: max-line-length
    worksheet.getCell("E7").style = { font: { bold: true, name: 'Times New Roman', size: 11 }, alignment: { horizontal: 'center', vertical: 'middle', wrapText: true }, fill: { type: 'pattern', pattern: 'darkVertical', fgColor: { argb: 'FFBFBFBF' }, bgColor: { argb: 'FFBFBFBF' } } };
    worksheet.getCell("E7").border = { top: { style: 'thin' }, bottom: { style: 'thin' }, left: { style: 'thin' }, right: { style: 'thin' } };

    worksheet.getCell("F7").value = "Ngày" + (this.paramsQuery?.dossierStatus == "6" ? " hủy" : " dừng xử lý");
    // tslint:disable-next-line: max-line-length
    worksheet.getCell("F7").style = { font: { bold: true, name: 'Times New Roman', size: 11 }, alignment: { horizontal: 'center', vertical: 'middle', wrapText: true }, fill: { type: 'pattern', pattern: 'darkVertical', fgColor: { argb: 'FFBFBFBF' }, bgColor: { argb: 'FFBFBFBF' } } };
    worksheet.getCell("F7").border = { top: { style: 'thin' }, bottom: { style: 'thin' }, left: { style: 'thin' }, right: { style: 'thin' } };

    worksheet.getCell("G7").value = "Cán bộ thực hiện";
    // tslint:disable-next-line: max-line-length
    worksheet.getCell("G7").style = { font: { bold: true, name: 'Times New Roman', size: 11 }, alignment: { horizontal: 'center', vertical: 'middle', wrapText: true }, fill: { type: 'pattern', pattern: 'darkVertical', fgColor: { argb: 'FFBFBFBF' }, bgColor: { argb: 'FFBFBFBF' } } };
    worksheet.getCell("G7").border = { top: { style: 'thin' }, bottom: { style: 'thin' }, left: { style: 'thin' }, right: { style: 'thin' } };

    worksheet.getCell("H7").value = "Lý do";
    // tslint:disable-next-line: max-line-length
    worksheet.getCell("H7").style = { font: { bold: true, name: 'Times New Roman', size: 11 }, alignment: { horizontal: 'center', vertical: 'middle', wrapText: true }, fill: { type: 'pattern', pattern: 'darkVertical', fgColor: { argb: 'FFBFBFBF' }, bgColor: { argb: 'FFBFBFBF' } } };
    worksheet.getCell("H7").border = { top: { style: 'thin' }, bottom: { style: 'thin' }, left: { style: 'thin' }, right: { style: 'thin' } };

    worksheet.getCell("I7").value = "Lĩnh vực";
    // tslint:disable-next-line: max-line-length
    worksheet.getCell("I7").style = { font: { bold: true, name: 'Times New Roman', size: 11 }, alignment: { horizontal: 'center', vertical: 'middle', wrapText: true }, fill: { type: 'pattern', pattern: 'darkVertical', fgColor: { argb: 'FFBFBFBF' }, bgColor: { argb: 'FFBFBFBF' } } };
    worksheet.getCell("I7").border = { top: { style: 'thin' }, bottom: { style: 'thin' }, left: { style: 'thin' }, right: { style: 'thin' } };

    worksheet.getCell("J7").value = "Bộ phận";
    // tslint:disable-next-line: max-line-length
    worksheet.getCell("J7").style = { font: { bold: true, name: 'Times New Roman', size: 11 }, alignment: { horizontal: 'center', vertical: 'middle', wrapText: true }, fill: { type: 'pattern', pattern: 'darkVertical', fgColor: { argb: 'FFBFBFBF' }, bgColor: { argb: 'FFBFBFBF' } } };
    worksheet.getCell("J7").border = { top: { style: 'thin' }, bottom: { style: 'thin' }, left: { style: 'thin' }, right: { style: 'thin' } };

    for (var i = 1; i <= 10; i++) {
      worksheet.getCell(8, i).value = i;
      worksheet.getCell(8, i).style = { font: { bold: false, name: 'Times New Roman', size: 11 }, alignment: { horizontal: 'center', vertical: 'middle', wrapText: true } };
      worksheet.getCell(8, i).border = { top: { style: 'thin' }, bottom: { style: 'thin' }, left: { style: 'thin' }, right: { style: 'thin' } };
    }
    worksheet.getRow(8).height = 15;

    for (var i = 0; i < listExport.length; i++) {
      var cellA = "A" + (9 + i);
      worksheet.getCell(cellA).value = (i + 1);
      worksheet.getCell(cellA).style = { font: { bold: false, name: 'Times New Roman', size: 11 }, alignment: { horizontal: 'center', vertical: 'middle', wrapText: true } };
      worksheet.getCell(cellA).border = { top: { style: 'thin' }, bottom: { style: 'thin' }, left: { style: 'thin' }, right: { style: 'thin' } };

      var cellA = "B" + (9 + i);
      worksheet.getCell(cellA).value = listExport[i].code;
      worksheet.getCell(cellA).style = { font: { bold: false, name: 'Times New Roman', size: 11 }, alignment: { horizontal: 'center', vertical: 'middle', wrapText: true } };
      worksheet.getCell(cellA).border = { top: { style: 'thin' }, bottom: { style: 'thin' }, left: { style: 'thin' }, right: { style: 'thin' } };

      var cellA = "C" + (9 + i);
      worksheet.getCell(cellA).value = listExport[i].procedureCode + " - " + listExport[i].procedureName;
      worksheet.getCell(cellA).style = { font: { bold: false, name: 'Times New Roman', size: 11 }, alignment: { horizontal: 'center', vertical: 'middle', wrapText: true } };
      worksheet.getCell(cellA).border = { top: { style: 'thin' }, bottom: { style: 'thin' }, left: { style: 'thin' }, right: { style: 'thin' } };

      var cellA = "D" + (9 + i);
      worksheet.getCell(cellA).value = this.getFullName(listExport[i].applicant?.data);
      worksheet.getCell(cellA).style = { font: { bold: false, name: 'Times New Roman', size: 11 }, alignment: { horizontal: 'center', vertical: 'middle', wrapText: true } };
      worksheet.getCell(cellA).border = { top: { style: 'thin' }, bottom: { style: 'thin' }, left: { style: 'thin' }, right: { style: 'thin' } };

      var cellA = "E" + (9 + i);
      worksheet.getCell(cellA).value = this.generateAddress(listExport[i].applicant?.data);
      worksheet.getCell(cellA).style = { font: { bold: false, name: 'Times New Roman', size: 11 }, alignment: { horizontal: 'center', vertical: 'middle', wrapText: true } };
      worksheet.getCell(cellA).border = { top: { style: 'thin' }, bottom: { style: 'thin' }, left: { style: 'thin' }, right: { style: 'thin' } };

      var cellA = "F" + (9 + i);
      // var ngay = this.paramsQuery.dossierStatus == "6"
      //   ? this.datePipe.transform(this.listExport[i]?.userWithdraw[0]?.updatedDate, 'dd/MM/yyyy HH:mm:ss')
      //   : this.datePipe.transform(, 'dd/MM/yyyy HH:mm:ss');
      var ngay = this.paramsQuery.dossierStatus == "6" ? this.getCancelInfo(listExport[i], 1) : this.getPauseInfo(listExport[i], 1);
      worksheet.getCell(cellA).value = ngay;
      worksheet.getCell(cellA).style = { font: { bold: false, name: 'Times New Roman', size: 11 }, alignment: { horizontal: 'center', vertical: 'middle', wrapText: true } };
      worksheet.getCell(cellA).border = { top: { style: 'thin' }, bottom: { style: 'thin' }, left: { style: 'thin' }, right: { style: 'thin' } };

      var cellA = "G" + (9 + i);
      let nguoiXL = this.paramsQuery.dossierStatus == "6" ? this.getCancelInfo(listExport[i], 2) : this.getPauseInfo(listExport[i], 2);
      worksheet.getCell(cellA).value = nguoiXL;
      worksheet.getCell(cellA).style = { font: { bold: false, name: 'Times New Roman', size: 11 }, alignment: { horizontal: 'center', vertical: 'middle', wrapText: true } };
      worksheet.getCell(cellA).border = { top: { style: 'thin' }, bottom: { style: 'thin' }, left: { style: 'thin' }, right: { style: 'thin' } };

      var cellA = "H" + (9 + i);
      let comment = this.paramsQuery.dossierStatus == "6" ? this.getCancelInfo(listExport[i], 3) : this.getPauseInfo(listExport[i], 3);
      let parser = new DOMParser();
      let doc = parser.parseFromString(comment, 'text/html');
      let lido = doc.body.textContent || doc.body.innerText;
      // let lido = comment?.replace("<p>", "").replace("</p>", "");

      worksheet.getCell(cellA).value = lido?? "";
      worksheet.getCell(cellA).style = { font: { bold: false, name: 'Times New Roman', size: 11 }, alignment: { horizontal: 'center', vertical: 'middle', wrapText: true } };
      worksheet.getCell(cellA).border = { top: { style: 'thin' }, bottom: { style: 'thin' }, left: { style: 'thin' }, right: { style: 'thin' } };

      var cellA = "I" + (9 + i);
      worksheet.getCell(cellA).value = listExport[i]?.sectorName;
      worksheet.getCell(cellA).style = { font: { bold: false, name: 'Times New Roman', size: 11 }, alignment: { horizontal: 'center', vertical: 'middle', wrapText: true } };
      worksheet.getCell(cellA).border = { top: { style: 'thin' }, bottom: { style: 'thin' }, left: { style: 'thin' }, right: { style: 'thin' } };

      var cellA = "J" + (9 + i);
      worksheet.getCell(cellA).value = listExport[i]?.agencyNameCurrentTask;
      worksheet.getCell(cellA).style = { font: { bold: false, name: 'Times New Roman', size: 11 }, alignment: { horizontal: 'center', vertical: 'middle', wrapText: true } };
      worksheet.getCell(cellA).border = { top: { style: 'thin' }, bottom: { style: 'thin' }, left: { style: 'thin' }, right: { style: 'thin' } };
    }

    worksheet.getColumn('A').width = 5;
    worksheet.getColumn('B').width = 30;
    worksheet.getColumn('C').width = 30;
    worksheet.getColumn('D').width = 30;
    worksheet.getColumn('E').width = 25;
    worksheet.getColumn('F').width = 25;
    worksheet.getColumn('G').width = 25;
    worksheet.getColumn('H').width = 25;
    worksheet.getColumn('I').width = 25;
    worksheet.getColumn('J').width = 25;

    var tenFile = this.datePipe.transform(this.nowDate, 'dd_MM') + "_hoso" + (this.paramsQuery.dossierStatus == "6" ? "huy" : "dungxuly");
    // Save Excel File
    workbook.xlsx.writeBuffer().then((data: ArrayBuffer) => {
      const blob = new Blob([data], { type: EXCEL_TYPE });
      fs.saveAs(blob, tenFile + EXCEL_EXTENSION);
    });
  }

  // Cập nhật validate date trong form tìm kiếm
  validateForm() {
    // Chuyển đổi lại thời gian startDate / endDate
    this.startDate.setHours(0, 0, 0, 0);
    this.endDate.setHours(23, 59, 59, 999);

    let startTime = this.startDate.getTime();
    let endTime = this.endDate.getTime();
    let language = localStorage.getItem('language');

    let data = {
      errMessage: "",
      status: false
    }

    if (this.startDate == null || this.endDate == null) {
      data.errMessage = language == 'vi' ? 'Vui lòng nhập đầy đủ thông tin ngày tra' : 'Please enter complete information for the lookup';
    } else if (startTime > endTime) {
      data.errMessage = language == 'vi' ? 'Vui lòng nhập thông tin từ ngày nhỏ hơn đến ngày' : 'Please enter information from date less than to date';
    } else {
      data.status = true;
    }

    if (data == null || !data?.status) {
      this.snackbarService.openSnackBar(0, "Không hợp lệ", data.errMessage, 'error_notification', this.config.expiredTime);
      this.listHoSo = [];
      this.countResult = 0;
      return false;
    }

    return true;
  }
}

