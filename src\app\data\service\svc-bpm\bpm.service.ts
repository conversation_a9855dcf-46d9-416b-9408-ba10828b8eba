import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { ApiProviderService } from 'src/app/core/service/api-provider.service';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class BpmService {

  constructor(
    private http: HttpClient,
    private apiProviderService: ApiProviderService,
  ) {}

  private bpm = this.apiProviderService.getUrl('digo', 'bpm');

  getListProcessDefinitionTask(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    switch (localStorage.getItem('isLoggedIn')) {
      case 'false':
        const token = localStorage.getItem('OAuth2TOKEN');
        headers = headers.append('Authorization', 'Bearer ' + token);
        return this.http.get(this.bpm + '/process-definition-task?process-definition-id=' + id + '&size=50', { headers }).pipe();
      case 'true':
        return this.http.get(this.bpm + '/process-definition-task?process-definition-id=' + id + '&size=50', { headers }).pipe();
    }
  }

  getListSearchProject(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.bpm + `/project-definition/--search${searchString}`, { headers });
  }

  getProjectDetail(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.bpm + "/project-definition/" + id, { headers });
  }

  postProject(requestBody: any): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.post<any>(this.bpm + "/project-definition", requestBody, { headers });
  }

  putProject(requestBody: any): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.put<any>(this.bpm + "/project-definition", requestBody, { headers });
  }

  deleteProject(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    return this.http.delete<any>(this.bpm + "/project-definition/" + id, { headers });
  }

  postLog(requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.post<any>(this.bpm + '/action-logs', requestBody, { headers });
  }

  getListActonLogs(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.bpm + '/action-logs/' + searchString, { headers });
  }

  postEForm(requestBody: any): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.post<any>(this.bpm + "/eform-catalog", requestBody, { headers });
  }

  putEForm(requestBody: any): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.put<any>(this.bpm + "/eform-catalog", requestBody, { headers });
  }

  deleteEForm(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    return this.http.delete<any>(this.bpm + "/eform-catalog/" + id, { headers });
  }

  getListSearchEForm(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.bpm + `/eform-catalog/--search${searchString}`, { headers });
  }

  getListProcessDefinitionTaskByProcessId(id: any): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get<any>(this.bpm + "/process-definition-task/get-list-by-process-definition-id?id=" + id, { headers });
  }
}
