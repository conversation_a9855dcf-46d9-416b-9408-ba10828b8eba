import { Component, OnInit, ChangeDetectorRef, Inject } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { MatTableDataSource } from '@angular/material/table';
import { EnvService } from 'src/app/core/service/env.service';
import { SnackbarService } from 'src/app/data/service/snackbar/snackbar.service';
import { PadmanService } from 'src/app/data/service/svc-padman/padman.service';

@Component({
  selector: 'app-chithi18-ct',
  templateUrl: './chithi18-ct.component.html',
  styleUrls: ['./chithi18-ct.component.scss', '/src/app/app.component.scss']
})
export class ChiThi18CtComponent implements OnInit {

  config = this.envService.getConfig();
  // dataSource
  displayedColumns: string[] = ['stt','so_ho_so','thu_tuc','nguoi_nop','dia_chi','co_quan_chu_tri','don_vi_tiep_nhan',
                                       'ngay_nhan', 'ngay_hen_tra', 'ngay_co_ket_qua', 'ngay_tra_ket_qua',
                                       'hinh_thuc_nhan_ket_qua', 'hinh_thuc_tiep_nhan'];
  ELEMENTDATA: any[] = [];
  dataSource: MatTableDataSource<any>;
  countResult = 0;
  size = 10;
  page = 1;
  pageIndex = 1;
  pgSizeOptions = this.config.pageSizeOptions;

  fromDate: string;
  toDate: string;
  fromLKDate: string;
  toLKDate: string;
  type: string;
  agencyId: string;
  selectedLang: string;

  allTotal = 0;

  constructor(
    public dialogRef: MatDialogRef<ChiThi18CtComponent>,
    @Inject(MAT_DIALOG_DATA) public data: ChiThi18CtDialogModel,
    private padmanService: PadmanService,
    private snackbarService: SnackbarService,
    private envService: EnvService,
    private cdRef: ChangeDetectorRef,
  ) {
    this.fromDate = data.fromDate;
    this.toDate = data.toDate;
    this.fromLKDate = data.fromLKDate;
    this.toLKDate = data.toLKDate;
    this.type = data.type;
    this.agencyId = data.agencyId;
    this.dataSource = new MatTableDataSource(this.ELEMENTDATA);
  }

  ngOnInit(): void {
    this.selectedLang = localStorage.getItem('language');
    this.autoSearch();
  }

  ngAfterViewInit() {
    this.cdRef.detectChanges();
  }

  autoSearch() {
    const searchString = this.generateSearchString('page', (this.page - 1), this.size, 'id,desc');
    this.getDSChiThi18CT(searchString);
  }

  generateSearchString(spec, page, size, sort) {
    return '?spec=' + spec +
      '&page=' + page +
      '&size=' + size +
      '&sort=' + sort +
      '&fromDate=' + this.fromDate +
      '&toDate=' + this.toDate +
      '&fromLKDate=' + this.fromLKDate +
      '&toLKDate=' + this.toLKDate +
      '&type=' + this.type +
      '&agencyId=' + this.agencyId
      ;
  }

  getDSChiThi18CT(searchString) {
    this.padmanService.getDLKChiThi18ChiTiet(searchString).subscribe(data => {
      if (data.content.length === 0) {
        const msgObj = {
          vi: 'Danh sách chi tiết hồ sơ rỗng!',
          en: 'The list of issued is empty!'
        };
        this.dialogRef.close();
        this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
      } else {
        this.ELEMENTDATA = data.content;
        this.countResult = data.totalElements;
        this.dataSource.data = this.ELEMENTDATA;
        console.log(data);
      }
    },err => {
      console.log(err);
    });
  }

  onConfirm() {
    this.dialogRef.close(true);
  }

  onDismiss() {
    this.dialogRef.close();
  }

  paginate(event: any, type) {
    switch (type) {
      case 0:
        this.pageIndex = event;
        this.page = event;
        const searchString = this.generateSearchString('page', (this.pageIndex - 1), this.size, 'id,desc');
        this.getDSChiThi18CT(searchString);
        break;
      case 1:
        this.allTotal = 0;
        this.pageIndex = 1;
        this.page = 1;
        const searchString2 = this.generateSearchString('page', (this.pageIndex - 1), this.size, 'id,desc');
        this.getDSChiThi18CT(searchString2);
        break;
    }
  }

}

export class ChiThi18CtDialogModel {
  constructor(
    public fromDate: string,
    public toDate: string,
    public fromLKDate: string,
    public toLKDate: string,
    public type: string,
    public agencyId: string) {
  }
}
