import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { Observable, of, Subject } from 'rxjs';
import { ApiProviderService } from 'src/app/core/service/api-provider.service';
import { saveAs } from 'file-saver';

@Injectable({
  providedIn: 'root'
})
export class StatisticApologyLetterService {
  private basedata = this.apiProviderService.getUrl('digo', 'basedata');
  agencyUrl = this.apiProviderService.getUrl('digo', 'basedata') + '/agency/';
  private urlApologyLetter = this.apiProviderService.getUrl('digo', 'padman') + '/apology-letter/statistic/';
  constructor(
    private http: HttpClient,
    private apiProviderService: ApiProviderService,
  ) { }

  getAgencyTreeView(options, isAgencyTemplatePaging: boolean = false): Observable<any> {
    return this.http.get<any>(this.agencyUrl +
      `/tree-view?keyword=` + options.keyword +
      `&agencyName=${options.agencyName}` +
      `&ancestor-id=${options['ancestor-id']}` +
      `&code=${options.code}` +
      `&status=${options.status}` +
      `&levelId=${options.agencyLevel}` +
      `&phone=${options.phone}` +
      `&parent-id=${options.parentId}` +
      `&tag-id=${options.tagId}` +
      //(options.agencyID.trim() != ''  ? `&agencyID=${options.agencyID.trim()}` :``)+
      (isAgencyTemplatePaging == true ? `&page=${options.page}` :``)+
      (isAgencyTemplatePaging == true ? `&size=${options.size}`: ``) +
      `&sort=${options.sort}`);
  }

  listAgencyTreeView(keyword: string, status: number, parentId: any, page?: number, size?: number, isAgencyTemplatePaging: boolean = false): Observable<any> {
    return this.http.get<any>(this.agencyUrl
      + (
        isAgencyTemplatePaging == true
        ? `/tree-view?keyword=${keyword}&status=${status}&parent-id=${parentId}&page=${page}&size=${size}`
        : `/tree-view?keyword=${keyword}&status=${status}&parent-id=${parentId}`
      )
    );
  }

  getAgencyName(keyword?: string, page?: number, size?: number): Observable<any> {
    let urlAgencyName;
    let data = {
      keyword: keyword ? keyword : '',
      page: page ? page : 0,
      size: size ? size : 50
    }
    let searching = '';
    searching = '?keyword=' + data.keyword + '&page=' + data.page + '&size=' + data.size;
    urlAgencyName = this.agencyUrl + '/name' + searching;
    return this.http.get<any>(urlAgencyName);
  }

  getCountApologyLetterByAgencyId(searchString: string) : Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers.append('Access-Control-Allow-Origin', '*'); 
    // return this.http.get('http://localhost:8081/apology-letter/statistic/' + 'id' + searchString, { headers });
    return this.http.get(this.urlApologyLetter + 'id' + searchString, { headers });
  }

  getCountApologyLetterByAgencyIdList(searchString: string) : Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers.append('Access-Control-Allow-Origin', '*');
    // return this.http.get('http://localhost:8081/apology-letter/statistic/' + 'ids' + searchString, { headers });
    return this.http.get(this.urlApologyLetter + 'ids' + searchString, { headers });
  }

  getDetailApologyLetterByAgencyId(searchString: string) : Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers.append('Access-Control-Allow-Origin', '*');
    // return this.http.get('http://localhost:8081/apology-letter/statistic/' + 'detail' + searchString, { headers });
    return this.http.get(this.urlApologyLetter + 'detail' + searchString, { headers });
  }

  exportListDossierHaveApologyLetter(searchString: string) {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers.append('Access-Control-Allow-Origin', '*');
    // this.http.get('http://localhost:8081/apology-letter/statistic/' + 'detail/excel' + searchString, {
    this.http.get(this.urlApologyLetter + 'detail/excel' + searchString, {
      headers,
      observe: 'response',
      responseType: 'blob'
    }).subscribe((response) => {
      const contentDisposition = response.headers.get('Content-Disposition');
      let filename = 'Thong_ke.xlsx';

      if (contentDisposition) {
        filename = response.headers.get('content-disposition').split(';')[1].split('=')[1].replace(/\"/g, '');
      }
 
      const blob = response.body;
      if (blob) {
        saveAs(blob, filename);
      }
    }, (error) => {
      console.error('Error while exporting dossier statistics:', error);
    });
  }

}
