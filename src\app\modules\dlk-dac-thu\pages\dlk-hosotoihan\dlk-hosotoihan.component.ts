import { Component, OnInit, ChangeDetectorRef, AfterViewInit, ViewChild, OnDestroy } from '@angular/core';
import { FormGroup, FormControl } from '@angular/forms';
import { Router } from '@angular/router';
import { EnvService } from 'src/app/core/service/env.service';
import { MatTableDataSource } from '@angular/material/table';
import { DatePipe } from '@angular/common';
import { ExportExcelService } from 'src/app/data/service/export-excel/export-excel.service';
import { MainService } from 'src/app/data/service/main/main.service';
import { ProcedureService } from 'src/app/data/service/procedure/procedure.service';
import { takeUntil } from 'rxjs/operators';
import { ReplaySubject, Subject } from 'rxjs';
import { ActivatedRoute } from '@angular/router';
import { DossierService } from 'src/app/data/service/dossier/dossier.service';
import { NgxPrinterService } from 'ngx-printer';
import { SnackbarService } from 'src/app/data/service/snackbar/snackbar.service';
import { MatSelect } from '@angular/material/select';
import { ReportService } from 'data/service/report/report.service';
import { DeploymentService } from 'data/service/deployment.service';
import * as tUtils from 'src/app/data/service/thoai.service';
import { CookieService } from 'ngx-cookie-service';
import { DLKStatisticService } from 'src/app/data/service/dlk-dac-thu/dlk-statistic.service';
import { Workbook } from 'exceljs';
import * as fs from 'file-saver';

const EXCEL_TYPE = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8';
const EXCEL_EXTENSION = '.xlsx';
export interface Agency {
  id: string;
  name: string;
}

@Component({
  selector: 'app-dlk-hosotoihan',
  templateUrl: './dlk-hosotoihan.component.html',
  styleUrls: [
    './dlk-hosotoihan.component.scss',
    '/src/app/app.component.scss',
    '/src/app/shared/scss/form-field-outline.scss'
  ]
})
export class DlkHoSoToiHanComponent implements OnInit, AfterViewInit, OnDestroy {

  nowDate = tUtils.newDate();

  // tslint:disable-next-line: max-line-length
  toDate = this.nowDate.getFullYear() + '-' + ((this.nowDate.getMonth() + 1) <= 9 ? '0' + (this.nowDate.getMonth() + 1) : (this.nowDate.getMonth() + 1)) + '-' + ((this.nowDate.getDate() <= 9) ? ('0' + this.nowDate.getDate()) : this.nowDate.getDate());
  // tslint:disable-next-line: max-line-length
  fromDate = this.nowDate.getFullYear() + '-' + ((this.nowDate.getMonth() + 1) <= 9 ? '0' + (this.nowDate.getMonth() + 1) : (this.nowDate.getMonth() + 1)) + '-01';

  keyword = '';
  config = this.envService.getConfig();
  selectedLang = localStorage.getItem('language') || 'vi';
  selectedLangId = Number(localStorage.getItem('languageId')) || 228;
  pageTitle = {
    vi: 'Danh sách hồ sơ tới hạn',
    en: 'Statistics due dossier'
  };

  protected onDestroy = new Subject<void>();
  // =================== old
  env: any = this.deploymentService.getAppDeployment()?.env;
  ELEMENTDATA: any[] = [];
  dataSource: MatTableDataSource<any>;
  // listSector: Array<any> = [];
  keySearchSectorAgency = '';
  // Search Sector
  keywordSector = '';
  totalPagesSector = 0;
  currentPageSector = 0;
  pageSizeSector = 100;
  lastSector = false;

  parentAgency = '';
  agencyId = '';
  userAgency?: any = JSON.parse(localStorage.getItem('userAgency'));
  userAgencyCount?: any = JSON.parse(localStorage.getItem('listAgencyUser'))?.length || 0;
  ELEMENTDATAALL: any[] = [];
  dataSourceAll: MatTableDataSource<any>;
  listHoSo = [];
  listExport = [];

  size = 10;
  pageIndex = 1;
  page = 1;
  countResult = 0;
  pgSizeOptions = this.config.pageSizeOptions;
  paramsQuery = {
    page: 0,
    size: 10,
    agency: '',
    parentAgency: '',
    fromDate: '',
    toDate: '',
    applyMethod: "-1",
    receivingKind: '',
    sector: '',
    keyword: '',
    procedure: '',
    sortId: '',
    isTTPVHCC: 1,
    all: 0,
    agencyName: '',
    fromDateNumber: 0,
    toDateNumber: 1,
    due: 1,
    childAgencyList: []
  };

  listAgencyAccept = [];
  listAgency = [];
  childAgencyList = [];
  keywordAgencyAccept = '';
  totalPagesAgencyAccept = 0;
  currentPageAgencyAccept = 0;
  pageSizeAgencyAccept = 1000;
  startDate = new Date();
  endDate = new Date();
  flagTTPVHCC = true;
  timeOutAgencyAccept: any = null;
  listSectorfillter: any[] = [];
  listSector: any[] = [];
  listProcedure: any[] = [];
  listProcedurefillter: any[] = [];
  totalPagesProcedure = 0;
  currentPageProcedure = 0;
  searchProcedureKeyword = "";
  listHinhThucNhan: any[] = [];
  waitingDownloadExcel = false;
  isDateFieldInvalid = false;
  messageDateFieldInvalid = "";
  callDataFirst = true;
  @ViewChild('procedureMatSelectInfiniteScroll', { static: true }) procedureMatSelectInfiniteScroll: MatSelect;


  constructor(
    private envService: EnvService,
    public datepipe: DatePipe,
    private mainService: MainService,
    private cdRef: ChangeDetectorRef,
    private exportExcelService: ExportExcelService,
    private procedureService: ProcedureService,
    private activeRoute: ActivatedRoute,
    private datePipe: DatePipe,
    private dossierService: DossierService,
    private snackbarService: SnackbarService,
    private router: Router,
    private printerService: NgxPrinterService,
    private reportService: ReportService,
    private deploymentService: DeploymentService,
    private cookieService: CookieService,
    private dlkStatisticService: DLKStatisticService
  ) {
    this.dataSource = new MatTableDataSource(this.ELEMENTDATA);
    this.dataSourceAll = new MatTableDataSource(this.ELEMENTDATAALL);
  }
  ngOnInit(): void {
    this.mainService.setPageTitle(this.pageTitle[localStorage.getItem('language')]);
    this.startDate = new Date(this.fromDate);
    this.endDate = new Date(this.toDate);
    if (this.userAgency !== null && this.userAgency !== undefined) {
      if (!!this.userAgency.parent && !!this.userAgency.parent.id && this.userAgencyCount === 1) {
        this.parentAgency = this.userAgency.parent.id;
        this.agencyId = this.userAgency.id;
        this.paramsQuery.agencyName = this.userAgency.parent.name;
      } else {
        this.parentAgency = this.userAgency.id;
        this.agencyId = this.userAgency.id;
        this.paramsQuery.agencyName = this.userAgency.name;
      }

      // this.keySearchSectorAgency = '&agency-id=' + this.agencyId;
      this.keySearchSectorAgency = '';
      this.paramsQuery.parentAgency = this.parentAgency;
    }

    this.getListAgencyAccept(this.keywordAgencyAccept, this.currentPageAgencyAccept, this.pageSizeAgencyAccept);
    this.getListSector();
  }

  ngAfterViewInit() {
    this.cdRef.detectChanges();
    setTimeout(() => {
      //this.autoSearch();
      // this.mainService.sideNav.close();
    }, 1000);
  }

  ngOnDestroy() {
    this.onDestroy.next();
    this.onDestroy.complete();
  }

  thongKe() {
    this.paramsQuery.page = 0;
    this.page = 1;
    this.paramsQuery.all = 0;
    this.getListHoSo();
  }
  paginate(event) {
    this.paramsQuery.page = event;
    this.paramsQuery.all = 0;
    this.getListHoSo();
  }

  getListHoSo() {
    if (!this.searchFormInvalid()) {
      this.paramsQuery.fromDate = (this.startDate ? this.datePipe.transform(this.startDate, 'dd/MM/yyyy') + 'T00:00:00.000Z' : '');
      this.paramsQuery.toDate = (this.endDate ? this.datePipe.transform(this.endDate, 'dd/MM/yyyy') + 'T23:59:59.999Z' : '');
      this.dlkStatisticService.getDossierToiHan(this.paramsQuery).subscribe(res => {
        this.listHoSo = res.content;
        this.countResult = res.totalElements;
      }, err => {
        console.log(err);
      });
    }
  }

  getDataByParams() {
    let isRemind = false;
    this.activeRoute.queryParams.subscribe(params => {
      isRemind = params?.isRemind;

      if (isRemind) {
        this.getListHoSo();
      }
    }
    );
  }

  searchFormInvalid(): boolean {
    // Kiểm tra thời gian thống kê
    this.resetFormInvalid();
    let fromDateNum = this.paramsQuery.fromDateNumber;
    let toDateNum = this.paramsQuery.toDateNumber;

    if (fromDateNum < 0) {
      this.messageDateFieldInvalid = "Thời gian Từ ngày phải lớn hơn 0";
      this.isDateFieldInvalid = true;
    } else if (toDateNum < 0) {
      this.messageDateFieldInvalid = "Thời gian Đến ngày phải lớn hơn 0";
      this.isDateFieldInvalid = true;
    } else if (fromDateNum >= toDateNum) {
      this.messageDateFieldInvalid = "Thời gian giải quyết Từ ngày phải nhỏ hơn Đến ngày";
      this.isDateFieldInvalid = true;
    }

    if (this.isDateFieldInvalid) {
      this.resetTableData();
      this.snackbarService.openSnackBar(0, 'Không hợp lệ', this.messageDateFieldInvalid, 'error_notification', this.config.expiredTime);
      return true;
    }

    return false;
  }

  resetFormInvalid() {
    this.isDateFieldInvalid = false;
    this.messageDateFieldInvalid = null;
  }

  resetTableData() {
    this.listHoSo = null;
    this.pageIndex = 1;
    this.page = 1;
    this.countResult = 0;
    this.pgSizeOptions = this.config.pageSizeOptions;
  }

  getListHinhThucNhan() {
    // tslint:disable-next-line:max-line-length
    this.dlkStatisticService.getHinhThucNhan().subscribe(res => {
      this.listHinhThucNhan = res.content;

    }, err => {
      console.log(err);
    });
  }

  getAgencyScroll() {
    this.currentPageAgencyAccept += 1;
    this.getListAgencyAccept(this.keywordAgencyAccept, this.currentPageAgencyAccept, this.pageSizeAgencyAccept);
  }

  getListAgencyAccept(keyword, page, size) {
    const searchString = '?parent-id=' + this.parentAgency + '&keyword=' + keyword + '&page=' + page + '&size=' + size + '&sort=name.name,asc&status=1';

    this.procedureService.getListAgencyWithParent(searchString).subscribe(res => {
      if (page === 0) {
        this.listAgency = res.content;
      } else {
        this.listAgency = this.listAgency.concat(res.content);
      }
      this.listAgencyAccept = this.listAgency;
      this.totalPagesAgencyAccept = res.totalPages;

      if (this.listAgency != null && this.listAgency.length > 0) {
        this.listAgencyAccept = this.listAgency;
        this.listAgency.forEach(item => {
          if (item != null && item.id != null) {
            this.paramsQuery.childAgencyList.push(item.id);
            this.childAgencyList.push(item.id);
          }
        })
      }

      // Kiem tra goi kiem tra ho so tu thong bao icon tại nav-header
      if (this.callDataFirst) {
        this.callDataFirst = false;
        this.getDataByParams();
      }
    }, err => {
      console.log(err);
    });
  }

  searchAngency(event) {
    if (event != "") {
      this.listAgencyAccept = this.listAgency.filter(agency =>
        agency.name.toLowerCase().trim().includes(event.toLowerCase()) == true);
    } else {
      this.listAgencyAccept = this.listAgency;
    }
  }

  clickTTPVHCC() {
    this.paramsQuery.isTTPVHCC = this.flagTTPVHCC ? 1 : 0;
  }

  onEnter(event) {
    clearTimeout(this.timeOutAgencyAccept);
    this.timeOutAgencyAccept = setTimeout(async () => {
      this.keywordAgencyAccept = event.target.value;
      this.currentPageAgencyAccept = 0;
      this.getListAgencyAccept(this.keywordAgencyAccept, this.currentPageAgencyAccept, this.pageSizeAgencyAccept);
      this.keywordSector = '';
      this.totalPagesSector = 0;
      this.currentPageSector = 0;
      this.pageSizeSector = 1000;
      this.listSector = [];
      this.getListSector();
    }, 300);
  }

  async resetSearchForm(type) {
    switch (type) {
      case 'agencyAccept': {
        this.currentPageAgencyAccept = 0;
        this.keywordAgencyAccept = '';
        this.getListAgencyAccept(this.keywordAgencyAccept, this.currentPageAgencyAccept, this.pageSizeAgencyAccept);

        this.keywordSector = '';
        this.totalPagesSector = 0;
        this.currentPageSector = 0;
        this.pageSizeSector = 1000;
        break;
      }
    }
  }

  changeAgencyAccept() {
    this.paramsQuery.sector = "";
    if (this.paramsQuery.agency !== '') {
      this.paramsQuery.childAgencyList = [];
      this.keySearchSectorAgency = '&agency-id=' + this.paramsQuery.agency;
    }
    else {
      this.paramsQuery.childAgencyList = this.childAgencyList;
      if (this.parentAgency !== '') {
        this.keySearchSectorAgency = '&agency-id=' + this.parentAgency;
      }
      else {
        this.keySearchSectorAgency = '';
      }
    }

    this.keywordSector = '';
    this.totalPagesSector = 0;
    this.currentPageSector = 0;
    this.pageSizeSector = 1000;
    this.listSector = [];
    this.getListSector();
  }

  getListSector() {
    // tslint:disable-next-line:max-line-length
    // const searchString = '?keyword=' + this.keywordSector + '&page=' + this.currentPageSector + '&size=' + this.pageSizeSector + '&spec=page&sort=name.name,asc&status=1' + this.keySearchSectorAgency;
    const searchString = '?keyword=' + this.keywordSector + '&page=' + this.currentPageSector + '&size=' + this.pageSizeSector + '&spec=page&sort=name.name,asc&status=1&only-agency-id=1' + this.keySearchSectorAgency;
    // const searchString = '?keyword=' + this.searchSectorKeyword + '&page=' + this.listSectorPage + '&size=' + 10 + '&spec=page&sort=name.name,asc&status=1';
    // const searchString = '?&page=' + page + '&size=&spec=page&sort=name.name,asc&status=1';

    this.procedureService.getListSector(searchString).subscribe(res => {
      console.log(res);
      if (this.currentPageSector === 0) {
        this.listSector = res.content;
        this.lastSector = res.last;
      } else {
        this.listSector = this.listSector.concat(res.content);
      }
      this.totalPagesSector = res.totalPages;
      this.listSectorfillter = this.listSector;
    }, err => {
      console.log(err);
    });
  }

  getListSectorScroll() {
    this.currentPageSector += 1;
    this.getListSector();
  }


  isFullListSector = false;
  searchSectorKeyword = '';
  listSectorPage = 0;
  private listSector1: any[] = [];
  protected sectors: any[] = this.listSector1;
  public sectorFiltered: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  searchSectorCtrl: FormControl = new FormControl();

  getListAllSector() {
    if (this.isFullListSector) {
      return;
    } else {
      const searchString = '?keyword=' + this.searchSectorKeyword + '&page=' + this.listSectorPage + '&size=' + 10 + '&spec=page&sort=name.name,asc&status=1';
      this.procedureService.getListSector(searchString).subscribe(data => {
        this.isFullListSector = data.last;
        this.listSectorPage++;
        for (let i = 0; i < data.numberOfElements; i++) {
          this.listSector1.push(data.content[i]);
        }
        this.sectors = JSON.parse(JSON.stringify(this.listSector1).replace(/null/g, '""'));
        this.sectorFiltered.next(this.sectors);
        this.searchSectorCtrl.valueChanges.pipe(takeUntil(this.onDestroy)).subscribe(() => {
        });
      }, err => {
        console.log(err);
      });
    }
  }

  searchSector(event) {
    if (event != "") {
      this.listSectorfillter = this.listSector.filter(sector =>
        sector.name.toLowerCase().trim().includes(event.toLowerCase()) == true);

    } else {
      this.listSectorfillter = this.listSector;
    }
  }

  sectorChange() {
    console.log("searchSector");
    
    this.listProcedure = [];
    this.listProcedurefillter = [];
    this.currentPageProcedure = 0;
    this.paramsQuery.procedure = "";
    if (this.paramsQuery.sector != "") {
      this.getListProcedure();
    }
  }

  getListProcedureScroll() {
    this.currentPageProcedure += 1;
    this.getListProcedure();
  }

  getListProcedure() {
    const searchString =
      '?status=1&sort=translate.name,asc&keyword=' + this.searchProcedureKeyword +
      '&spec=page&page=' + this.currentPageProcedure + '&size=50' +
      '&sector-id=' + this.paramsQuery.sector;
    this.procedureService.getListProcedure(searchString).subscribe(data => {
      if (this.currentPageProcedure == 0) {
        this.listProcedure = data.content;
      } else {
        this.listProcedure = this.listProcedure.concat(data.content);
      }
      this.totalPagesProcedure = data.totalPages;
      this.listProcedurefillter = this.listProcedure;
    }, err => {
      console.log(err);
    });
  }

  searchProvedure(event) {
    if (event != "") {
      this.searchProcedureKeyword = event;

      // this.listProcedurefillter = this.listProcedure.filter(pro =>
      //   pro.name.toLowerCase().trim().includes(event.toLowerCase()) == true);
    } else {
      this.searchProcedureKeyword = "";
      // this.currentPageProcedure = 0;
      // this.getListProcedure();
      //this.listProcedurefillter = this.listProcedure;
    }
    this.currentPageProcedure = 0;
    this.getListProcedure();
  }

  generateAddress(data) {
    // return this.mainService.generateAddress(placeObj);
    const address = [];
    if (data?.address !== undefined && data?.address !== null) {
      address.push(data.address);
    }
    if (data?.village !== undefined && data?.village !== null) {
      address.push(data.village.label);
    }
    if (data?.district !== undefined && data?.district !== null) {
      address.push(data.district.label);
    }
    if (data?.province !== undefined && data?.province !== null) {
      address.push(data.province.label);
    }
    // if (data?.nation !== undefined && data?.nation !== null) {
    //   address.push(data.nation.label);
    // }
    return address.join(', ');
  }

  getFullName(data) {
    if (data != null) {
      if (data?.fullName != undefined && data?.fullName != null && data?.fullName != "") {
        return data.fullName;
      } else if (data?.fullname != undefined && data?.fullname != null && data?.fullname != "") {
        return data.fullname;
      } else if (data?.ownerFullname != undefined && data?.ownerFullname != null && data?.ownerFullname != "") {
        return data.ownerFullname;
      } else {
        return "";
      }
    }
  }

  getTienDo(data) {
    var ngayHoanThanh = this.nowDate;
    var ngayHenTra = this.nowDate;
    var finish = false;
    if (data.appointmentDate != undefined && data.appointmentDate != "") {
      ngayHenTra = new Date(data.appointmentDate);
    } else {
      return "Còn hạn";
    }
    if (data.completedDate != undefined && data.completedDate != "") {
      ngayHoanThanh = new Date(data.completedDate);
      finish = true;
    }

    var result = ngayHoanThanh.getTime() - ngayHenTra.getTime();
    if (result < 0) {
      if (finish) {
        return "Đúng hạn";
      } else {
        return "Còn hạn";
      }
    } else {
      return "Quá hạn";
    }
  }

  getTen(data) {
    if (data != null && data.length > 0) {
      var ten = data[0].name;
      data.forEach(element => {
        if (element.languageId == 228) {
          ten = element.name;
        }
      });
      return ten;
    } else {
      return "";
    }
  }

  getHuyHoSo(data, loai) {
    // loai = 0 : get nguoi huy, loai = 1: get noi dung huy
    if (data.userRefuse != undefined) {
      if (data.userRefuse.length > 0) {
        return loai == 0 ? data.userRefuse[0].user.fullname : data.userRefuse[0].comment;
      } else {
        return "";
      }

    } else {
      return "";
    }
  }
  
  xuatExcel() {
    this.waitingDownloadExcel = true;
    //this.dlkStatisticService.exportLogbookExcel();

    this.paramsQuery.all = 1;
    this.paramsQuery.fromDate = (this.startDate ? this.datePipe.transform(this.startDate, 'dd/MM/yyyy') : ''),
      this.paramsQuery.toDate = (this.endDate ? this.datePipe.transform(this.endDate, 'dd/MM/yyyy') : ''),
      this.dlkStatisticService.getDossierToiHan(this.paramsQuery).subscribe(res => {
        this.listExport = res.content;
        this.exportLogbookExcel(this.listExport);
        this.waitingDownloadExcel = false;
      }, err => {
        console.log(err);
        this.waitingDownloadExcel = false;
      });
  }

  public formatDate(date) {
    let day = String(date.getDate()).padStart(2, '0');
    let month = String(date.getMonth() + 1).padStart(2, '0');
    let year = date.getFullYear();
    let hours = String(date.getHours()).padStart(2, '0');
    let minutes = String(date.getMinutes()).padStart(2, '0');
    let seconds = String(date.getSeconds()).padStart(2, '0');

    return `${day}/${month}/${year} ${hours}:${minutes}:${seconds}`;
  }

  public exportLogbookExcel(listExport) {
    let currentDate = new Date();
    let fromDate = new Date(currentDate);
    let toDate = new Date(currentDate);
    fromDate.setDate(currentDate.getDate() + this.paramsQuery.fromDateNumber);
    toDate.setDate(currentDate.getDate() + this.paramsQuery.toDateNumber);

    // create workbook and worksheet
    const workbook = new Workbook();
    workbook.creator = 'Snippet Coder';
    workbook.lastModifiedBy = 'SnippetCoder';
    workbook.created = new Date();
    workbook.modified = new Date();
    const worksheet = workbook.addWorksheet("MauHoSoDenHan");
    worksheet.properties.outlineLevelCol = 2;
    worksheet.properties.defaultRowHeight = 16;

    // Add header row
    worksheet.mergeCells('A1:C1');
    worksheet.getCell('A1').value = "UBND TỈNH ĐẮK LẮK";
    // tslint:disable-next-line: max-line-length
    worksheet.getCell('A1').style = { font: { bold: true, name: 'Times New Roman', size: 11 }, alignment: { horizontal: 'center', vertical: 'middle' } };
    // Add header row
    worksheet.mergeCells('A2:C2');
    worksheet.getCell('A2').value = this.paramsQuery.agencyName;
    // tslint:disable-next-line: max-line-length
    worksheet.getCell('A2').style = { font: { bold: true, name: 'Times New Roman', size: 11 }, alignment: { horizontal: 'center', vertical: 'middle' } };

    worksheet.mergeCells('F1:H1');
    worksheet.getCell('F1').value = "CỘNG HÒA XÃ HỘI CHỦ NGHĨA VIỆT NAM";
    // tslint:disable-next-line: max-line-length
    worksheet.getCell('F1').style = { font: { bold: true, name: 'Times New Roman', size: 11 }, alignment: { horizontal: 'center', vertical: 'middle' } };

    worksheet.mergeCells('F2:H2');
    worksheet.getCell('F2').value = "Độc lập - Tự do - Hạnh phúc";
    // tslint:disable-next-line: max-line-length
    worksheet.getCell('F2').style = { font: { bold: true, name: 'Times New Roman', size: 11 }, alignment: { horizontal: 'center', vertical: 'middle' } };

    worksheet.mergeCells('A4:H4');
    worksheet.getCell('A4').value = "MẪU HỒ SƠ ĐẾN HẠN CỦA " + this.paramsQuery.agencyName.toUpperCase();
    // tslint:disable-next-line: max-line-length
    worksheet.getCell('A4').style = { font: { bold: true, name: 'Times New Roman', size: 16 }, alignment: { horizontal: 'center', vertical: 'middle' } };

    worksheet.mergeCells('A5:H5');
    worksheet.getCell('A5').value = `Thời hạn giải quyết hồ sơ từ ngày ${this.formatDate(fromDate) } đến ngày ${this.formatDate(toDate)} (Từ ${this.paramsQuery.fromDateNumber} đến ${this.paramsQuery.toDateNumber} ngày)`;
    // tslint:disable-next-line: max-line-length
    worksheet.getCell('A5').style = { font: { bold: false, name: 'Times New Roman', size: 11 }, alignment: { horizontal: 'center', vertical: 'middle' } };

    worksheet.getCell("A7").value = "STT";
    // tslint:disable-next-line: max-line-length
    worksheet.getCell("A7").style = { font: { bold: true, name: 'Times New Roman', size: 11 }, alignment: { horizontal: 'center', vertical: 'middle', wrapText: true }, fill: { type: 'pattern', pattern: 'darkVertical', fgColor: { argb: 'FFBFBFBF' }, bgColor: { argb: 'FFBFBFBF' } } };
    worksheet.getCell("A7").border = { top: { style: 'thin' }, bottom: { style: 'thin' }, left: { style: 'thin' }, right: { style: 'thin' } };

    worksheet.getCell("B7").value = "SỐ HỒ SƠ";
    // tslint:disable-next-line: max-line-length
    worksheet.getCell("B7").style = { font: { bold: true, name: 'Times New Roman', size: 11 }, alignment: { horizontal: 'center', vertical: 'middle', wrapText: true }, fill: { type: 'pattern', pattern: 'darkVertical', fgColor: { argb: 'FFBFBFBF' }, bgColor: { argb: 'FFBFBFBF' } } };
    worksheet.getCell("B7").border = { top: { style: 'thin' }, left: { style: 'thin' }, right: { style: 'thin' } };
    worksheet.getCell("B8").border = { bottom: { style: 'thin' }, left: { style: 'thin' }, right: { style: 'thin' } };

    worksheet.getCell("C7").value = "THỦ TỤC";
    // tslint:disable-next-line: max-line-length
    worksheet.getCell("C7").style = { font: { bold: true, name: 'Times New Roman', size: 11 }, alignment: { horizontal: 'center', vertical: 'middle', wrapText: true }, fill: { type: 'pattern', pattern: 'darkVertical', fgColor: { argb: 'FFBFBFBF' }, bgColor: { argb: 'FFBFBFBF' } } };
    worksheet.getCell("C7").border = { top: { style: 'thin' }, bottom: { style: 'thin' }, left: { style: 'thin' }, right: { style: 'thin' } };

    worksheet.getCell("D7").value = "TGQĐ HỒ SƠ";
    // tslint:disable-next-line: max-line-length
    worksheet.getCell("D7").style = { font: { bold: true, name: 'Times New Roman', size: 11 }, alignment: { horizontal: 'center', vertical: 'middle', wrapText: true }, fill: { type: 'pattern', pattern: 'darkVertical', fgColor: { argb: 'FFBFBFBF' }, bgColor: { argb: 'FFBFBFBF' } } };
    worksheet.getCell("D7").border = { top: { style: 'thin' }, bottom: { style: 'thin' }, left: { style: 'thin' }, right: { style: 'thin' } };

    worksheet.getCell("E7").value = "NGƯỜI ĐĂNG KÝ";
    // tslint:disable-next-line: max-line-length
    worksheet.getCell("E7").style = { font: { bold: true, name: 'Times New Roman', size: 11 }, alignment: { horizontal: 'center', vertical: 'middle', wrapText: true }, fill: { type: 'pattern', pattern: 'darkVertical', fgColor: { argb: 'FFBFBFBF' }, bgColor: { argb: 'FFBFBFBF' } } };
    worksheet.getCell("E7").border = { top: { style: 'thin' }, bottom: { style: 'thin' }, left: { style: 'thin' }, right: { style: 'thin' } };

    worksheet.getCell("F7").value = "ĐỊA CHỈ";
    // tslint:disable-next-line: max-line-length
    worksheet.getCell("F7").style = { font: { bold: true, name: 'Times New Roman', size: 11 }, alignment: { horizontal: 'center', vertical: 'middle', wrapText: true }, fill: { type: 'pattern', pattern: 'darkVertical', fgColor: { argb: 'FFBFBFBF' }, bgColor: { argb: 'FFBFBFBF' } } };
    worksheet.getCell("F7").border = { top: { style: 'thin' }, bottom: { style: 'thin' }, left: { style: 'thin' }, right: { style: 'thin' } };

    worksheet.getCell("G7").value = "LĨNH VỰC";
    // tslint:disable-next-line: max-line-length
    worksheet.getCell("G7").style = { font: { bold: true, name: 'Times New Roman', size: 11 }, alignment: { horizontal: 'center', vertical: 'middle', wrapText: true }, fill: { type: 'pattern', pattern: 'darkVertical', fgColor: { argb: 'FFBFBFBF' }, bgColor: { argb: 'FFBFBFBF' } } };
    worksheet.getCell("G7").border = { top: { style: 'thin' }, bottom: { style: 'thin' }, left: { style: 'thin' }, right: { style: 'thin' } };

    worksheet.getCell("H7").value = "BỘ PHẬN/CB ĐANG XỬ LÝ";
    // tslint:disable-next-line: max-line-length
    worksheet.getCell("H7").style = { font: { bold: true, name: 'Times New Roman', size: 11 }, alignment: { horizontal: 'center', vertical: 'middle', wrapText: true }, fill: { type: 'pattern', pattern: 'darkVertical', fgColor: { argb: 'FFBFBFBF' }, bgColor: { argb: 'FFBFBFBF' } } };
    worksheet.getCell("H7").border = { top: { style: 'thin' }, bottom: { style: 'thin' }, left: { style: 'thin' }, right: { style: 'thin' } };

    worksheet.getCell("I7").value = "GHI CHÚ";
    // tslint:disable-next-line: max-line-length
    worksheet.getCell("I7").style = { font: { bold: true, name: 'Times New Roman', size: 11 }, alignment: { horizontal: 'center', vertical: 'middle', wrapText: true }, fill: { type: 'pattern', pattern: 'darkVertical', fgColor: { argb: 'FFBFBFBF' }, bgColor: { argb: 'FFBFBFBF' } } };
    worksheet.getCell("I7").border = { top: { style: 'thin' }, bottom: { style: 'thin' }, left: { style: 'thin' }, right: { style: 'thin' } };

    for (var i = 1; i <= 9; i++) {
      worksheet.getCell(8, i).value = i;
      worksheet.getCell(8, i).style = { font: { bold: false, name: 'Times New Roman', size: 11 }, alignment: { horizontal: 'center', vertical: 'middle', wrapText: true } };
      worksheet.getCell(8, i).border = { top: { style: 'thin' }, bottom: { style: 'thin' }, left: { style: 'thin' }, right: { style: 'thin' } };
    }
    worksheet.getRow(8).height = 15;

    for (var i = 0; i < listExport.length; i++) {
      var cellA = "A" + (9 + i);
      worksheet.getCell(cellA).value = (i + 1);
      worksheet.getCell(cellA).style = { font: { bold: false, name: 'Times New Roman', size: 11 }, alignment: { horizontal: 'center', vertical: 'middle', wrapText: true } };
      worksheet.getCell(cellA).border = { top: { style: 'thin' }, bottom: { style: 'thin' }, left: { style: 'thin' }, right: { style: 'thin' } };

      var cellA = "B" + (9 + i);
      worksheet.getCell(cellA).value = listExport[i]?.code;
      worksheet.getCell(cellA).style = { font: { bold: false, name: 'Times New Roman', size: 11 }, alignment: { horizontal: 'center', vertical: 'middle', wrapText: true } };
      worksheet.getCell(cellA).border = { top: { style: 'thin' }, bottom: { style: 'thin' }, left: { style: 'thin' }, right: { style: 'thin' } };

      var cellA = "C" + (9 + i);
      worksheet.getCell(cellA).value = listExport[i]?.procedureCode + " - " + listExport[i]?.procedureName;
      worksheet.getCell(cellA).style = { font: { bold: false, name: 'Times New Roman', size: 11 }, alignment: { horizontal: 'center', vertical: 'middle', wrapText: true } };
      worksheet.getCell(cellA).border = { top: { style: 'thin' }, bottom: { style: 'thin' }, left: { style: 'thin' }, right: { style: 'thin' } };

      var cellA = "D" + (9 + i);
      worksheet.getCell(cellA).value = `${listExport[i].stringDateWork} (${listExport[i]?.remainingDateWord})\n- Ngày tiếp nhận: ${this.datePipe.transform(listExport[i]?.acceptedDate, 'dd/MM/yyyy HH:mm:ss')}\n- Ngày xử lý: ${this.datePipe.transform(listExport[i]?.appointmentDate, 'dd/MM/yyyy HH:mm:ss')}\n- Ngày hẹn trả: ${this.datePipe.transform(listExport[i]?.appointmentDate, 'dd/MM/yyyy HH:mm:ss')}`;
      worksheet.getCell(cellA).style = { font: { bold: false, name: 'Times New Roman', size: 11 }, alignment: { horizontal: 'left', vertical: 'middle', wrapText: true } };
      worksheet.getCell(cellA).border = { top: { style: 'thin' }, bottom: { style: 'thin' }, left: { style: 'thin' }, right: { style: 'thin' } };

      var cellA = "E" + (9 + i);
      worksheet.getCell(cellA).value = this.getFullName(listExport[i].applicant?.data) ?? "";
      worksheet.getCell(cellA).style = { font: { bold: false, name: 'Times New Roman', size: 11 }, alignment: { horizontal: 'center', vertical: 'middle', wrapText: true } };
      worksheet.getCell(cellA).border = { top: { style: 'thin' }, bottom: { style: 'thin' }, left: { style: 'thin' }, right: { style: 'thin' } };

      var cellA = "F" + (9 + i);
      worksheet.getCell(cellA).value = this.generateAddress(listExport[i].applicant?.data) ?? "";
      worksheet.getCell(cellA).style = { font: { bold: false, name: 'Times New Roman', size: 11 }, alignment: { horizontal: 'center', vertical: 'middle', wrapText: true } };
      worksheet.getCell(cellA).border = { top: { style: 'thin' }, bottom: { style: 'thin' }, left: { style: 'thin' }, right: { style: 'thin' } };

      var cellA = "G" + (9 + i);
      worksheet.getCell(cellA).value = (listExport[i]?.sectorName ?? "");
      worksheet.getCell(cellA).style = { font: { bold: false, name: 'Times New Roman', size: 11 }, alignment: { horizontal: 'center', vertical: 'middle', wrapText: true } };
      worksheet.getCell(cellA).border = { top: { style: 'thin' }, bottom: { style: 'thin' }, left: { style: 'thin' }, right: { style: 'thin' } };

      var cellA = "H" + (9 + i);
      worksheet.getCell(cellA).value = (listExport[i].assigneeFullNameCurrentTask ?? "") + " - " + (listExport[i].agencyNameCurrentTask ?? "");
      worksheet.getCell(cellA).style = { font: { bold: false, name: 'Times New Roman', size: 11 }, alignment: { horizontal: 'center', vertical: 'middle', wrapText: true } };
      worksheet.getCell(cellA).border = { top: { style: 'thin' }, bottom: { style: 'thin' }, left: { style: 'thin' }, right: { style: 'thin' } };

      var cellA = "I" + (9 + i);
      worksheet.getCell(cellA).style = { font: { bold: false, name: 'Times New Roman', size: 11 }, alignment: { horizontal: 'center', vertical: 'middle', wrapText: true } };
      worksheet.getCell(cellA).border = { top: { style: 'thin' }, bottom: { style: 'thin' }, left: { style: 'thin' }, right: { style: 'thin' } };
    }

    worksheet.getColumn('A').width = 5;
    worksheet.getColumn('B').width = 30;
    worksheet.getColumn('C').width = 20;
    worksheet.getColumn('D').width = 35;
    worksheet.getColumn('E').width = 30;
    worksheet.getColumn('F').width = 40;
    worksheet.getColumn('G').width = 20;
    worksheet.getColumn('H').width = 30;
    worksheet.getColumn('I').width = 20;

    var tenFile = this.datePipe.transform(this.nowDate, 'dd_MM') + "_mau_ho_so_den_han";
    // Save Excel File
    workbook.xlsx.writeBuffer().then((data: ArrayBuffer) => {
      const blob = new Blob([data], { type: EXCEL_TYPE });
      fs.saveAs(blob, tenFile + EXCEL_EXTENSION);
    });
  }

}

