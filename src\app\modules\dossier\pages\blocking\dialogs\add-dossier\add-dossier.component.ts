import { Component, Inject, OnInit } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { ProcessService } from 'src/app/data/service/process/process.service';
import { ProcedureService } from 'src/app/data/service/procedure/procedure.service';
import { EnvService } from 'src/app/core/service/env.service';
import { KeycloakService } from 'keycloak-angular';
import { HttpClient } from '@angular/common/http';
import { SnackbarService } from 'src/app/data/service/snackbar/snackbar.service';
import { DeploymentService } from 'data/service/deployment.service';
import * as ClassicEditor from '@ckeditor/ckeditor5-build-classic/build/ckeditor';
import { DatePipe } from '@angular/common';
import { MainService } from 'src/app/data/service/main/main.service';
import { BlockingDossierService } from 'data/service/qnm-blocking-dossier/blocking-dossier.service';

export interface Form {
  id: string;
  code: string;
  name: string;
  status: number;
}
@Component({
  selector: 'app-add-blocking-dossier',
  templateUrl: './add-dossier.component.html',
  styleUrls: ['./add-dossier.component.scss']
})
export class AddDossierComponent implements OnInit {

  selectedLang: string;
  env: any = this.deploymentService.getAppDeployment()?.env;
  config = this.envService.getConfig();
  addForm = new FormGroup({
    ownerFullname: new FormControl(''),
    identityNumber: new FormControl(''),
    certificateId: new FormControl(''),
    slotId: new FormControl(''),
    mapId: new FormControl(''),
    recordId: new FormControl(''),
    blockDate: new FormControl(new Date()),
    content: new FormControl(''),
  });
  public Editor = ClassicEditor;
  editorConfig = {
    placeholder: 'Nhập nội dung...',
    language: 'vi',
    removePlugins: ['MediaEmbed', 'ImageUpload'],
    toolbarLocation: 'bottom'
  };
  CKMaxlength = [
    {
      id: 'content',
      required: false,
      isMaxLength: false,
    }
  ];
  acceptUpdateProcess = !!this.env?.acceptUpdateProcess;
  isDisabled = false;
  maxCkeditorLength = this.env?.maxCkeditorLength || this.config.maxCkeditorLength;
  description = '';
  blockingDossierId: string;
  currentBlockingDossier: any;
  checkAmountProcess: number;
  processDetail;
  currentDate = new Date();
  xml: any = {};
  blankVal = '';
  userAgency: any;
  officer: any;
  acceptFileExtension = ['.XLS', '.XLSX', '.CVS', '.PDF', '.DOC', '.DOCX'];
  constructor(
    public dialogRef: MatDialogRef<AddDossierComponent>,
    @Inject(MAT_DIALOG_DATA) public data: ConfirmAddBlockingDossierDialogModel,
    private envService: EnvService,
    private mainService: MainService,
    private keycloak: KeycloakService,
    private processService: ProcessService,
    private snackbarService: SnackbarService,
    private procedureService: ProcedureService,
    private deploymentService: DeploymentService,
    private blockingDossierService: BlockingDossierService,
    private http: HttpClient,
    private datePipe: DatePipe,
  ) {
    this.blockingDossierId = data?.id;
    this.checkAmountProcess = data?.check;
  }

  ngOnInit(): void {
    this.selectedLang = localStorage.getItem('language');
    if (this.blockingDossierId != null) {
      this.getBlockingDossierDetail(this.blockingDossierId);
    }
    this.userAgency = JSON.parse(localStorage.getItem('userAgency'));
    console.log('userAgency', this.userAgency);
    this.getUserInfo().catch();
  }
  getUserInfo(): Promise<any> {
    return new Promise<any>((resolve, reject) => {
      this.keycloak.loadUserProfile().then((user: any) => {
        resolve(user);
        this.officer = {
          id : user.attributes.user_id[0],
          fullname : user.attributes.fullname[0],
          account : {
              id : user.attributes.account_id[0],
              username : [
                  {
                      value : user.username
                  }
              ]
          }
        };
      }, err => {
        resolve(err);
      });
    });
  }
  getBlockingDossierDetail(id) {
    this.blockingDossierService.getBlockingDossier(id).subscribe(data => {
      this.currentBlockingDossier = data;
      console.log('data', this.currentBlockingDossier);
      this.setViewData();
    });
  }
  createBlockingDossier(formObject){
    const createObj = {
      // title: dossierObject.name,
      content: formObject.content,
      blockDate: formObject.blockDate,
      createdDate: new Date(),
      updatedDate: new Date(),
      deploymentId: this.config.deploymentId,
      certificate: this.getLandCertificate(formObject)
    };
    console.log('createBlockingDossier', createObj);

    this.blockingDossierService.createBlockingDossier(createObj).subscribe(data => {
      this.dialogRef.close(true);
    }, err => {
      console.log(err);
      this.dialogRef.close(false);
    });
  }
  updateBlockingDossier(formObject){
    const updateObj = {
      // title: dossierObject.name,
      content: formObject.content,
      blockDate: formObject.blockDate,
      createdDate: formObject.createdDate,
      updatedDate: new Date(),
      deploymentId: this.config.deploymentId,
      certificate: this.getLandCertificate(formObject)
    };
    console.log('updateObj', updateObj);

    this.blockingDossierService.updateBlockingDossier(this.blockingDossierId, updateObj).subscribe(data => {
      this.dialogRef.close(true);
    }, err => {
      this.dialogRef.close(false);
    });
  }

  getLandCertificate(formObj) {
    return {
      certificateId: formObj.certificateId,
      slotId: formObj.slotId,
      mapId: formObj.mapId,
      recordId: formObj.recordId,
      ownerFullname: formObj.ownerFullname,
      identityNumber: formObj.identityNumber
    };
  }

  async onConfirm(): Promise<void> {
    const formObject = this.addForm.getRawValue();
    console.log('dossierObject', formObject);
    if (this.blockingDossierId !== null) {
      this.updateBlockingDossier(formObject);
    } else {
      this.createBlockingDossier(formObject);
    }
  }
  onDismiss(): void {
    this.dialogRef.close();
  }
  setViewData() {
    const blockDate = this.currentBlockingDossier?.blockDate ? new Date(this.currentBlockingDossier?.blockDate) : null;

    this.addForm = new FormGroup({
      certificateId: new FormControl(this.currentBlockingDossier?.certificate?.certificateId),
      slotId: new FormControl(this.currentBlockingDossier?.certificate?.slotId),
      mapId: new FormControl(this.currentBlockingDossier?.certificate?.mapId),
      recordId: new FormControl(this.currentBlockingDossier?.certificate?.recordId),
      ownerFullname: new FormControl(this.currentBlockingDossier?.certificate?.ownerFullname),
      identityNumber: new FormControl(this.currentBlockingDossier?.certificate?.identityNumber),
      blockDate: new FormControl(blockDate),
      content: new FormControl(this.currentBlockingDossier.content),
    });
  }
  onEditorChange(id, event) {
    // const data = event.editor.getData();
    // this.addForm.controls[id].setValue(data);

    // if (this.maxCkeditorLength === -8) {
    //   this.CKMaxlength.find(item => item.id === id).isMaxlenght = false;
    // } else {
    //   if (event.editor.getData().trim().length > this.maxCkeditorLength) {
    //     this.CKMaxlength.find(item => item.id === id).isMaxlenght = true;
    //   } else {
    //     this.CKMaxlength.find(item => item.id === id).isMaxlenght = false;
    //   }
    // }
  }

  onEditorFocusOut(id, event) {
    this.CKMaxlength[0].required = !this.addForm.getRawValue().content;
  }

}
export class ConfirmAddBlockingDossierDialogModel {
  constructor(public id, public check) {

  }
}
