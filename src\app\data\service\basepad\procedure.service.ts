import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { ApiProviderService } from 'src/app/core/service/api-provider.service';
import { EnvService } from 'src/app/core/service/env.service';

@Injectable({
    providedIn: 'root'
})
export class ProcedureService {

    constructor(
        private http: HttpClient,
        private envService: EnvService,
        private apiProviderService: ApiProviderService,
    ) { }

    private getProcedureDetailsUrl(id: string): string {
        return this.apiProviderService.getUrl('digo', 'basepad') + `/procedure/${id}/--full`;
    }

    public getProcedureDetails(id: string): Observable<any> {
        return this.http.get(this.getProcedureDetailsUrl(id));
    }
}
