import { HttpClient, HttpHeaders } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { EnvService } from "src/app/core/service/env.service";
import { DeploymentService } from "../deployment.service";
import { ApiProviderService } from "src/app/core/service/api-provider.service";
import { Observable } from "rxjs";

@Injectable({
    providedIn: 'root'
})
export class DossierTrackingService{
    config = this.envService.getConfig();
    env = this.deploymentService.getAppDeployment()?.env;
    envConfig = this.deploymentService.env;

    constructor(
        private envService: EnvService,
        private http: HttpClient,
        private apiProviderService: ApiProviderService,
        private deploymentService: DeploymentService,
    ) { }

    private dossierTrackingURL = this.apiProviderService.getUrl('digo', 'padman') + '/dossier-tracking';

    getDossierTrackingByCode(code): Observable<any> {
        const URL = `${this.dossierTrackingURL}` + '/--find-by-code' + '?code=' + code;
        let headers = new HttpHeaders();
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        return this.http.get(URL, { headers });
    }
}