import { Inject, Injectable } from '@angular/core';
import { LocationStrategy } from '@angular/common';
import { Router } from '@angular/router';
import * as moment from 'moment';

@Injectable({
  providedIn: 'root'
})
export class GeneralOptionService {
  private static baseSpacing = 4;
  private gapValue;
  private sideNavOpen = false;
  countData;
  private iconList: Array<{ name: string, src: string }>;
  spacingArr = [];
  TASK_PROCESS_DEFINITION_KEY = {
    TAO_LAP_DANH_MUC: 'Process_ho-so-tao-lap-dm',
    HO_SO_LUU_TRU_CO_QUAN: 'Process_ho-so-luu-tru-co-quan',
  };
  loading = false;
  get priorities() {
    return [
      { id: 0, name: $localize`:@@normal:Thường`, icon: 'star_outline', color: 'color-gray' },
      { id: 1, name: $localize`:@@urgent:Khẩn`, icon: 'star', color: 'color-orange' },
      { id: 2, name: $localize`:@@emergency:Th<PERSON><PERSON><PERSON> khẩn`, icon: 'star', color: 'color-red' },
      { id: 3, name: $localize`:@@express:Hỏa tốc`, icon: 'near_me', color: 'color-orange' },
      { id: 4, name: $localize`:@@expressTimer:Hỏa tốc hẹn giờ`, icon: 'near_me', color: 'color-red' },
    ];
  }

}
