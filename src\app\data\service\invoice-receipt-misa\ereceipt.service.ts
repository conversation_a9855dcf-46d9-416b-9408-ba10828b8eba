import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { ApiProviderService } from 'src/app/core/service/api-provider.service';
import { Observable } from 'rxjs';
import { EnvService } from 'src/app/core/service/env.service';

@Injectable({
  providedIn: 'root'
})
export class EReceiptMisaService {

  config = this.envService.getConfig();
  constructor(
    private envService: EnvService,
    private http: HttpClient,
    private apiProviderService: ApiProviderService
  ) { }

  private human = this.apiProviderService.getUrl('digo', 'human');
  private adapter = this.apiProviderService.getUrl('digo', 'adapter');
  private basecat = this.apiProviderService.getUrl('digo', 'basecat');
  private basedata = this.apiProviderService.getUrl('digo', 'basedata');
  private padman = this.apiProviderService.getUrl('digo', 'padman');
  private basepad = this.apiProviderService.getUrl('digo', 'basepad');

  getUserExperience(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.human + '/user/' + id + '/experience', { headers });
  }

  createEReceipt(data): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    return this.http.post(this.adapter + '/e-receipt-misa/--create', data, { headers });
  }

  issueEReceipt(data): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    return this.http.post(this.adapter + '/e-receipt-misa/--issue', data, { headers });
  }

  createPaymentEReceipt(data): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    return this.http.post(this.adapter + '/e-receipt-misa/--create-payment', data, { headers });
  }

  issuePaymentEReceipt(data): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    return this.http.post(this.adapter + '/e-receipt-misa/--issue-payment', data, { headers });
  }

  postDossierReceipt(data): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    return this.http.post(this.padman + '/dossier-receipt', data, { headers });
  }

  getListReceiptTagCategory(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.basecat + '/tag/--by-category-id?category-id=' + id + '&size=50', { headers });
  }

  getDetailTypeReceipt(id): Promise<any> {
    return this.http.get(this.basecat + '/tag/' + id).toPromise();
  }

  getAgency(id): Promise<any> {
    return this.http.get(this.basedata + '/agency/' + id).toPromise();
  }

  getPlace(id): Promise<any> {
    return this.http.get(this.basedata + '/place/' + id).toPromise();
  }

  getCountDossierReceipt(statusId, dossierId): Promise<any> {
    const param = '?status-id=' + statusId + '&dossier-id=' + dossierId + '&spec=page';
    return this.http.get(this.padman + '/dossier-receipt' + param).toPromise();
  }

  getDossierReceiptByProdId(statusId, prodId, dossierId): Promise<any> {
    // tslint:disable-next-line: max-line-length
    return this.http.get(this.padman + '/dossier-receipt/search-prod-id?status-id=' + statusId + '&prod-id=' + prodId + '&dossier-id=' + dossierId).toPromise();
  }

  checkIssued(statusIds, prodIds, dossierId): Observable<any> {
    // tslint:disable-next-line: max-line-length
    return this.http.get(this.padman + `/dossier-receipt/--check-issued?status-id=${statusIds}&prod-id=${prodIds}&dossier-id=${dossierId}`);
  }

  getProcostForOnline(id): Promise<any> {
    return this.http.get(this.basepad + '/procost/--for-online?procedure-id=' + id).toPromise();
  }

  getListTagByCategoryId(categoryId, page): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    const param = '?category-id=' + categoryId + '&page=' + page;
    return this.http.get(this.basecat + '/tag/--by-category-id' + param, { headers });
  }

  getListReceiptSupplier(id, page): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    const param = '&page=' + page;
    return this.http.get(this.basecat + '/tag/--by-category-id?category-id=' + id + param, { headers });
  }

  getListDossierReceipt(searchString): Observable<any> {
    return this.http.get(this.padman + '/dossier-receipt' + searchString);
  }

  printEReceipt(params): Promise<any> {
    return this.http.get(this.adapter + '/e-receipt-misa/--print' + params).toPromise();
  }

  unPaymentEReceipt(params): Observable<any> {
    return this.http.get(this.adapter + '/e-receipt-misa/--unpayment' + params);
  }

  paymentEReceipt(params): Observable<any> {
    return this.http.get(this.adapter + '/e-receipt-misa/--payment' + params);
  }

  cancelEReceipt(params): Observable<any> {
    return this.http.delete(this.adapter + '/e-receipt-misa/--cancel' + params);
  }

  printNoPayEReceipt(params): Promise<any> {
    return this.http.get(this.adapter + '/e-receipt-misa/--print-no-pay' + params).toPromise();
  }

  printConvertEReceipt(params): Promise<any> {
    return this.http.get(this.adapter + '/e-receipt-misa/--print-convert' + params).toPromise();
  }

  getDossierReceipt(id): Observable<any> {
    return this.http.get(this.padman + '/dossier-receipt/' + id);
  }

  putDossierReceipt(id, data): Observable<any> {
    return this.http.put(this.padman + '/dossier-receipt/' + id, data);
  }

  getDetailIntegratedConfig(agencyId, integrationServiceId, subsystemId): Observable<any> {
    const param = '?agency-id=' + agencyId + '&integration-service-id=' + integrationServiceId + '&subsystem-id=' + subsystemId;
    return this.http.get(this.adapter + '/integrated-configuration/' + param);
  }

  putPaidDossierFee(param, data): Observable<any> {
    return this.http.put(this.padman + '/dossier-fee/--pay-by-id' + param, data);
  }

  integratedConfigurationCheckApply(agencyId,subsystemId,name): Promise<any> {
    const param = '?subsystem-id=' + subsystemId + '&name=' + name + '&agency-id=' + agencyId;
    return this.http.get(this.adapter + '/e-receipt-misa/--check-apply' + param).toPromise();
  }
}
