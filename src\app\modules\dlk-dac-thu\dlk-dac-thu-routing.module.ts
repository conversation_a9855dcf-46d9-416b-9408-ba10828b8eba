import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { AuthGuard } from 'src/app/core/guard/auth.guard';
import { ChiThi18Component } from './pages/chithi18/chithi18.component';
const routes: Routes = [
  {
    path: 'logbook-dlk',
    loadChildren: () => import('./pages/dlk-logbook/dlk-logbook.module').then(m => m.DlkLogbookModule),
    canActivate: [AuthGuard],
    data: {
      anyPermissions: ['dlkLogBook']
    }
  },
  {
    path: 'dlk-hosohuy',
    loadChildren: () => import('./pages/dlk-hosohuy/dlk-hosohuy.module').then(m => m.DlkHoSoHuyModule),
    canActivate: [AuthGuard],
    data: {
      anyPermissions: ['dlkHoSoHuy']
    }
  },
  {
    path: 'dlk-hosotoihan',
    loadChildren: () => import('./pages/dlk-hosotoihan/dlk-hosotoihan.module').then(m => m.DlkHoSoToiHanModule),
    canActivate: [AuthGuard],
    data: {
      anyPermissions: ['dlkHoSoToiHan']
    }
  },
  {
    path: 'dlk-hosotrehan',
    loadChildren: () => import('./pages/dlk-hosotrehan/dlk-hosotrehan.module').then(m => m.DlkHoSoTreHanModule),
    canActivate: [AuthGuard],
    data: {
      anyPermissions: ['dlkHoSoTreHan']
    }
  },
  {
    path: 'dlkchithi18',
    component: ChiThi18Component,
    canActivate: [AuthGuard],
    data: {
      anyPermissions: ['dlkCT18ToanTinh', 'oneGateAdminMaster']
    }
  },
  {
    path: 'dlk-bao-cao-so-hoa',
    loadChildren: () => import('./dlk-digitization-report/dlk-digitization-report.module').then(m => m.DLKDigitizationReportModule),
    canActivate: [AuthGuard],
      data: {
        anyPermissions: ['dlkBaoCaoSoHoa', 'oneGateReport']
      }
  },
  {
    path: 'dlk-chithi25-toantinh-cs',
    loadChildren: () => import('./dlk-baocao-chithi25-toanso/dlk-baocao-chithi25-toanso.module').then(m => m.DlkBaocaoChithi25ToanSoModule),
    canActivate: [AuthGuard],
    data: {
      anyPermissions: ['dlkCT25ToanTinhCapSo']
    }
  },
  {
    path: 'dlk-chithi25-toantinh-cx',
    loadChildren: () => import('./dlk-baocao-chithi25-toanxa/dlk-baocao-chithi25-toanxa.module').then(m => m.DlkBaocaoChithi25ToanXaModule),
    canActivate: [AuthGuard],
    data: {
      anyPermissions: ['dlkCT25ToanTinhCapXa']
    }
  },
  {
    path: 'dlk-chithi25-toantinh',
    loadChildren: () => import('./dlk-baocao-chithi25-toantinh/dlk-baocao-chithi25-toantinh.module').then(m => m.DlkBaocaoChithi25ToanTinhModule),
    canActivate: [AuthGuard],
    data: {
      anyPermissions: ['dlkCT25ToanTinh']
    }
  },
  {
    path: 'dlk-chithi25-caphuyen',
    loadChildren: () => import('./dlk-baocao-chithi25-caphuyen/dlk-baocao-chithi25-caphuyen.module').then(m => m.DlkBaocaoChithi25CapHuyenModule),
    canActivate: [AuthGuard],
    data: {
      anyPermissions: ['dlkCT25CapHuyen']
    }
  },
  {
    path: 'dlk-chithi25-capxa',
    loadChildren: () => import('./dlk-baocao-chithi25-capxa/dlk-baocao-chithi25-capxa.module').then(m => m.DlkBaocaoChithi25CapXaModule),
    canActivate: [AuthGuard],
    data: {
      anyPermissions: ['dlkCT25CapXa']
    }
  },
  {
    path: 'dlk-chithi25-capso',
    loadChildren: () => import('./dlk-baocao-chithi25-capso/dlk-baocao-chithi25-capso.module').then(m => m.DlkBaocaoChithi25CapSoModule),
    canActivate: [AuthGuard],
    data: {
      anyPermissions: ['dlkCT25CapSo']
    }
  },
  {
    path: 'dlk-chithi25-capdonvi',
    loadChildren: () => import('./dlk-baocao-chithi25-capdonvi/dlk-baocao-chithi25-capdonvi.module').then(m => m.DlkBaocaoChithi25CapDonViModule),
    canActivate: [AuthGuard],
    data: {
      anyPermissions: ['dlkCT25CapDonVi']
    }
  },
  {
    path: 'dlk-baocao-chithi08',
    loadChildren: () => import('./dlk-baocao-chithi08/dlk-baocao-chithi08.module').then(m => m.DlkBaoCaoChiThi08Module),
    canActivate: [AuthGuard],
    data: {
      anyPermissions: ['dlkCT08ToanTinh']
    }
  },
  {
    path: 'dlk-baocao-chithi08-capso',
    loadChildren: () => import('./dlk-baocao-chithi08-capso/dlk-baocao-chithi08-capso.module').then(m => m.DlkBaoCaoChiThi08CapSoModule),
    canActivate: [AuthGuard],
    data: {
      anyPermissions: ['dlkCT08CapSo']
    }
  },
  {
    path: 'dlk-baocao-chithi08-caphuyen',
    loadChildren: () => import('./dlk-baocao-chithi08-caphuyen/dlk-baocao-chithi08-caphuyen.module').then(m => m.DlkBaoCaoChiThi08CapHuyenModule),
    canActivate: [AuthGuard],
    data: {
      anyPermissions: ['dlkCT08CapHuyen']
    }
  },
  {
    path: 'dlk-baocao-chithi08-capdonvi',
    loadChildren: () => import('./dlk-baocao-chithi08-capdonvi/dlk-baocao-chithi08-capdonvi.module').then(m => m.DlkBaoCaoChiThi08CapDonViModule),
    canActivate: [AuthGuard],
    data: {
      anyPermissions: ['dlkCT08CapDonVi']
    }
  },
  {
    path: 'dlk-hoso-online-chuatiepnhan',
    loadChildren: () => import('./dlk-hoso-online-chuatiepnhan/dlk-hoso-online-chuatiepnhan.module').then(m => m.DlkHoSoOnlineChuaTiepNhanModule),
    canActivate: [AuthGuard],
    data: {
      anyPermissions: ['dlkHoSoOnlineChuaTiepNhan']
    }
  },
  {
    path: 'dlk-baocao-sohoa',
    loadChildren: () => import('./dlk-baocao-sohoa/dlk-baocao-sohoa.module').then(m => m.DlkBaoCaoSoHoaModule),
    canActivate: [AuthGuard],
    data: {
      anyPermissions: ['dlkBaoCaoSoHoav2']
    }
  },
  {
    path: 'dlk-tinhhinh-trienkhai-xuly',
    loadChildren: () => import('./dlk-tinhhinh-trienkhai-xuly/dlk-tinhhinh-trienkhai-xuly.module').then(m => m.DlkTinhHinhTrienKhaiXuLyModule),
    canActivate: [AuthGuard],
    data: {
      anyPermissions: ['dlkTinhHinhTrienKhaiXuLy']
    }
  },
  {
    path: 'dlk-chithi08-ttcapso',
    loadChildren: () => import('./dlk-chithi08-ttcapso/dlk-chithi08-ttcapso.module').then(m => m.DlkChithi08TtcapsoModule),
    canActivate: [AuthGuard],
    data: {
      anyPermissions: ['dlkCT08TTCapSo']
    }
  },
  {
    path: 'dlk-chithi08-ttcapxa',
    loadChildren: () => import('./dlk-chithi08-ttcapxa/dlk-chithi08-ttcapxa.module').then(m => m.DlkChithi08TtcapxaModule),
    canActivate: [AuthGuard],
    data: {
      anyPermissions: ['dlkCT08TTCapXa']
    }
  },
  {
    path: 'dlk-chithi08-capxa',
    loadChildren: () => import('./dlk-chithi08-capxa/dlk-chithi08-capxa.module').then(m => m.DlkChithi08CapxaModule),
    canActivate: [AuthGuard],
    data: {
      anyPermissions: ['dlkCT08CapXa']
    }
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class DlkDacThuRoutingModule { }
