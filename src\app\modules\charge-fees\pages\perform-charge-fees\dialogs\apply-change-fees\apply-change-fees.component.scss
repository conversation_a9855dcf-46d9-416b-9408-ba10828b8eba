::ng-deep .formApprovalAgency .mat-form-field-type-mat-select:not(.mat-form-field-disabled) .mat-form-field-flex {
    margin-bottom: 12px;
}
::ng-deep .formApprovalAgency .mat-form-field-appearance-outline .mat-form-field-flex{
    margin-bottom: 12px;
}
.search-nested {
    width: 80% !important;
    height: 50px !important;
    margin-left: 15px !important;
}

.clear-search-nested {
    right: 0 !important;
    position: absolute !important;
    top: 0 !important;
}
.dialog_title {
    font-style: normal;
    font-weight: 500;
    font-size: 20px;
    color: #000000;
}

.close-button {
    float: right;
    top: -24px;
    right: -24px;
}

.used{
    color: #CE7A58;
}

::ng-deep .searchForm .mat-form-field-wrapper {
    padding-bottom: 2px;
}

.searchForm .searchBtn {
    margin-top: 1em;
    background-color: #CE7A58;
    color: #fff;
    height: 3em;
    max-width: 100px !important;
}

.searchForm .label {
    margin: .5em 0 .5em .2em;
    font-weight: 500;
}

.searchForm .rdoAgency .mat-radio-button {
    padding: 0 2em 1em 0;
}

::ng-deep .searchForm .rdoAgency .mat-radio-button .mat-radio-inner-circle {
    background-color: #CE7A58;
}

::ng-deep .searchForm .rdoAgency .mat-radio-button.mat-accent.mat-radio-checked .mat-radio-outer-circle {
    border-color: #CE7A58;
}
::ng-deep .mat-primary .mat-option.mat-selected:not(.mat-option-disabled) {
    color: #CE7A58 !important;
}
::ng-deep .mat-form-field.mat-focused.mat-primary .mat-select-arrow {
    color: #CE7A58;
}
.dialog_title {
    font-style: normal;
    font-weight: 500;
    font-size: 20px;
    color: #000000;
}

::ng-deep .searchForm .mat-form-field-wrapper {
    padding-bottom: 2px;
}

.searchForm .searchBtn {
    margin-top: 1em;
    background-color: #CE7A58;
    color: #fff;
    height: 3em;
}

.searchForm .label {
    margin: .5em 0 .5em .2em;
    font-weight: 500;
}

.searchForm .rdoAgency .mat-radio-button {
    padding: 0 2em 1em 0;
}

::ng-deep .searchForm .rdoAgency .mat-radio-button .mat-radio-inner-circle {
    background-color: #CE7A58;
}

::ng-deep .searchForm .rdoAgency .mat-radio-button.mat-accent.mat-radio-checked .mat-radio-outer-circle {
    border-color: #CE7A58;
}
.header-sector{
    background-color: #F4EADF;
    align-items: center;
    padding: 5px;
    //justify-content: center;
}

.header-sector1{
    display: flex;
    align-items: center;

    //justify-content: center;
}
.header-sector2{
    background-color: #F4EADF;
    // height: 30px;
}
.marginRow{
    margin-bottom: 10px;
}

.bn-add{
    float: right;
    margin-top: auto;
    margin-bottom: auto;
    align-items: center;
    color: #CE7A58
}
// ::ng-deep .frm_searchbar .mat-form-field-appearance-outline .mat-form-field-infix {
//     padding: 0.8em 0;
// }

// ::ng-deep .frm_searchbar .mat-form-field-label-wrapper {
//     top: -1em;
// }

// ::ng-deep .frm_searchbar .mat-form-field.mat-focused .mat-form-field-label {
//     color: #ce7a58;
//     font-size: 18px;
//     margin-bottom: 1em;
// }

// ::ng-deep .frm_searchbar .mat-form-field-appearance-outline.mat-form-field-can-float.mat-form-field-should-float .mat-form-field-label {
//     color: #ce7a58;
//     font-size: 18px;
//     margin-bottom: 1em;
// }
::ng-deep .edit .mat-form-field.mat-focused .mat-form-field-label {
    color: #ce7a58;
}
::ng-deep .edit .mat-form-field-appearance-outline .mat-form-field-outline {
    color: #ffffff!important;
    // opacity: 1!important;
}
::ng-deep .edit .mat-form-field-appearance-outline .mat-form-field-outline {
    background-color: #eaebeb;
    border-radius: 5px;
}
::ng-deep .edit .mat-form-field-appearance-outline.mat-focused .mat-form-field-outline-thick {
    color: #ce7a58!important;
}


// .bn-left{
//     float: right;
// }
.margin-sectorname{
    margin-top: 15px;
}
.padding-sectorname{
    margin-bottom: 17px;
    padding-bottom: 15px;
    border: 1px solid #dfdcdc;
}
.padding-itemsectorname{
    padding: 12px;
    border-radius: 5px;
}
.div-icon{
    display: flex;
    align-items: center;
    padding-left: 5px;
}
.icon-color{
    
    color:  #CE7A58;
    margin-top: auto;
    margin-bottom: auto;
    
}
a.aHover{
    cursor: pointer;
}
@media screen and (max-width: 600px) {
    .bn-add{
        float: left;
        margin-top: auto;
        margin-bottom: auto;
        align-items: center;
        color: #CE7A58;
    }
}
@media screen and (max-width: 960px) {
    .padding-itemsectorname{
        padding: 5px;
        border-radius: 5px;
    }
}