<h2><PERSON>ÁO CÁO CHỈ THỊ 08 TOÀN TỈNH</h2>
<div class="prc_searchbar">

  <div fxLayout="row" fxLayout.xs="row" fxLayout.sm="row" class="formFieldOutline" fxLayoutAlign="space-between">
    <div appearance="outline" fxFlex='grow'>
      <div fxLayout="row" fxLayout.xs="row" fxLayout.sm="row" class="formFieldOutline" fxLayoutAlign="space-between">

        <mat-form-field appearance="outline" fxFlex.gt-sm="24" fxFlex.gt-xs="49.5" fxFlex='grow'>
          <mat-label>Từ ngày</mat-label>
          <input matInput [matDatepicker]="pickerAcceptFrom" [(ngModel)]="tuNgay">
          <mat-datepicker-toggle matSuffix [for]="pickerAcceptFrom"></mat-datepicker-toggle>
          <mat-datepicker #pickerAcceptFrom></mat-datepicker>
        </mat-form-field>
        <mat-form-field appearance="outline" fxFlex.gt-sm="24" fxFlex.gt-xs="49.5" fxFlex='grow'>
          <mat-label>Đến ngày</mat-label>
          <input matInput [matDatepicker]="pickerAcceptTo" [(ngModel)]="denNgay">
          <mat-datepicker-toggle matSuffix [for]="pickerAcceptTo"></mat-datepicker-toggle>
          <mat-datepicker #pickerAcceptTo></mat-datepicker>
        </mat-form-field>
        <mat-form-field appearance="outline" fxFlex.gt-sm="24" fxFlex.gt-xs="49.5" fxFlex='grow'>
          <mat-label>Từ ngày lũy kế</mat-label>
          <input matInput [matDatepicker]="pickerAcceptFrom1" [(ngModel)]="tuNgayLuyke">
          <mat-datepicker-toggle matSuffix [for]="pickerAcceptFrom1"></mat-datepicker-toggle>
          <mat-datepicker #pickerAcceptFrom1></mat-datepicker>
        </mat-form-field>
        <mat-form-field appearance="outline" fxFlex.gt-sm="24" fxFlex.gt-xs="49.5" fxFlex='grow'>
          <mat-label>Đến ngày lũy kế</mat-label>
          <input matInput [matDatepicker]="pickerAcceptTo1" [(ngModel)]="denNgayLuyke">
          <mat-datepicker-toggle matSuffix [for]="pickerAcceptTo1"></mat-datepicker-toggle>
          <mat-datepicker #pickerAcceptTo1></mat-datepicker>
        </mat-form-field>
      </div>
    </div>
  </div>

  <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="row" fxLayoutAlign="end" style="padding-bottom: 1em;">
    <button mat-flat-button fxFlex.lt-md="22" fxFlex.md="20" fxFlex.gt-md="12" fxFlex.gt-xs="49.5" fxFlex='grow'
      style="background-color: #ce7a58;color: white;" class="btn-search" type="submit" (click)="getDataBaocao()">
      <mat-icon class="iconStatistical">bar_chart</mat-icon><span>Thống kê</span>
      <!-- <mat-progress-bar mode="indeterminate" *ngIf="waitingDownloadExcel"></mat-progress-bar> -->
    </button>
    <div fxFlex='1'></div>
    <button mat-flat-button fxFlex.lt-md="22" fxFlex.md="20" fxFlex.gt-md="12" fxFlex.gt-xs="49.5" fxFlex='grow'
      class="btn-download-excel" (click)="exportToExcel()" style="background-color: #38A938;color: white;">
      <mat-icon class="iconStatistical">cloud_download</mat-icon>
      <span style="padding-right: 2px;"> Xuất excel</span>
      <!-- <mat-progress-bar mode="indeterminate" *ngIf="waitingDownloadExcel"></mat-progress-bar> -->
    </button>
  </div>
</div>

<div fxLayout="row" fxLayoutAlign="center">
  <div class="frm_main" fxFlex="grow">
    <div class="logbookTbl">
      <div class="logbookOnlyTbl">
        <table class="table">
          <thead>
            <tr>
              <th rowspan="5">STT</th>
              <th rowspan="5">Tên cơ quan, đơn vị</th>
              <th rowspan="4">Kỳ báo cáo</th>
              <th colspan="11">Tiếp nhận hồ sơ TTHC</th>
              <th colspan="9">Trả kết quả giải quyết TTHC </th>
              <th colspan="9">Dịch vụ công trực tuyến (DVCTT)</th>
              <th colspan="3">TTHC cung cấp dịch vụ BCCI</th>
              <th colspan="6">Thanh toán trực tuyến</th>
            </tr>
            <tr>
              <th rowspan="3">Tổng số hồ sơ tiếp nhận trong tháng</th>
              <th rowspan="3">Trực tiếp</th>
              <th rowspan="3">BCCI</th>
              <th colspan="6">Trực tuyến</th>
              <th colspan="2" rowspan="2">Cập nhật lên iGate</th>
              <th rowspan="3">Tổng số</th>
              <th colspan="2" rowspan="2">Trả trực tiếp</th>
              <th colspan="2" rowspan="2">Trả qua dịch vụ BCCI</th>
              <th colspan="2" rowspan="2">Trả trực tuyến</th>
              <th rowspan="3">Cập nhật lên iGate</th>
              <th rowspan="3">Số hồ sơ TTHC sử dụng ký số trong giải quyết</th>
              <th rowspan="3">Tổng số TTHC của cơ quan, đơn vị</th>
              <th rowspan="3">Số DVCTT có phát sinh hồ sơ</th>
              <th rowspan="3">Tỷ lệ DVCTT có phát sinh hồ sơ</th>
              <th colspan="3" rowspan="2">Một phần</th>
              <th colspan="3" rowspan="2">Toàn trình</th>
              <th rowspan="3">Số TTHC cung cấp dịch vụ BCCI</th>
              <th colspan="2" rowspan="2">TTHC cung cấp dịch vụ BCCI có phát sinh hồ sơ</th>
              <th rowspan="3">Số TTHC cung cấp dịch vụ thanh toán trực tuyến</th>
              <th rowspan="3">Số hồ sơ TTHC phát sinh của các TTHC cung cấp dịch vụ thanh toán trực tuyến (bao gồm trực
                tiếp, trực tuyến, BCCI)</th>
              <th colspan="2" rowspan="2">Số TTHC cung cấp dịch vụ thanh toán trực tuyến có phát sinh hồ sơ</th>
              <th colspan="2" rowspan="2">Thực hiện thanh toán trực tuyến</th>
            </tr>
            <tr>
              <th colspan="2">Một phần</th>
              <th colspan="2">Toàn trình</th>
              <th colspan="2">Trực tuyến (một phần và toàn trình)</th>
            </tr>
            <tr>
              <th>Số hồ sơ</th>
              <th>Tỷ lệ %</th>
              <th>Số hồ sơ</th>
              <th>Tỷ lệ %</th>
              <th>Tổng số hồ sơ</th>
              <th>Tỷ lệ %</th>
              <th>Số hồ sơ tiếp nhận đã cập nhật lên iGate</th>
              <th>Tỷ lệ %</th>
              <th>Số hồ sơ</th>
              <th>Tỷ lệ %</th>
              <th>Số hồ sơ</th>
              <th>Tỷ lệ %</th>
              <th>Số hồ sơ</th>
              <th>Tỷ lệ %</th>
              <th>Số DVCTT một phần </th>
              <th>Số DVCTT một phần có phát sinh hồ sơ</th>
              <th>Tỷ lệ %</th>
              <th>Số DVCTT toàn trình</th>
              <th>Số DVCTT toàn trình có phát sinh hồ sơ</th>
              <th>Tỷ lệ %</th>
              <th>Số TTHC cung cấp dịch vụ BCCI có phát sinh hồ sơ</th>
              <th>Tỷ lệ %</th>
              <th>Số TTHC</th>
              <th>Tỷ lệ %</th>
              <th>Số hồ sơ TTHC đã giải quyết sử dụng dịch vụ thanh toán trực tuyến</th>
              <th>Tỷ lệ %</th>
            </tr>
            <tr>
              <th>(1)</th>
              <th>(2)</th>
              <th>(3)</th>
              <th>(4)</th>
              <th>(5)</th>
              <th>(6)</th>
              <th>(7)</th>
              <th>(8)</th>
              <th>(9)</th>
              <th>(10)</th>
              <th>(11)</th>
              <th>(12)</th>
              <th>(13)</th>
              <th>(14)</th>
              <th>(15)</th>
              <th>(16)</th>
              <th>(17)</th>
              <th>(18)</th>
              <th>(19)</th>
              <th>(20)</th>
              <th>(21)</th>
              <th>(22)</th>
              <th>(23)</th>
              <th>(24)</th>
              <th>(25)</th>
              <th>(26)</th>
              <th>(27)</th>
              <th>(28)</th>
              <th>(29)</th>
              <th>(30)</th>
              <th>(31)</th>
              <th>(32)</th>
              <th>(33)</th>
              <th>(34)</th>
              <th>(35)</th>
              <th>(36)</th>
              <th>(37)</th>
              <th>(38)</th>
              <th>(39)</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td style="font-weight: 500;">
                I
              </td>
              <td colspan="40" style="text-align: left;font-weight: 500;">
                Sở ban ngành
              </td>
            </tr>
            <ng-container *ngTemplateOutlet="reusableTemplate;context: { list: soData, total: soDataSum, level: 1 }"></ng-container>
            <tr>
              <td style="font-weight: 500;">
                II
              </td>
              <td colspan="40" style="text-align: left;font-weight: 500;">
                Thành phố, huyện, thị xã
              </td>
            </tr>
            <ng-container *ngTemplateOutlet="reusableTemplate;context: { list: huyenData, total: huyenDataSum, level: 2 }"></ng-container>
            <!-- total row -->
            <tr class="sum-tr-2">
              <td colspan="2" rowspan="2">
                TỔNG TOÀN TỈNH
              </td>
              <td>Trong kỳ</td>
              <td *ngFor="let prop of listProp">
                <ng-container *ngIf="prop.type">
                  {{dataSum[prop.name + '_tky']}}
                </ng-container>
                <ng-container *ngIf="prop.fixValue">
                  {{ prop.fixValue }}
                </ng-container>
              </td>
            </tr>
            <tr class="sum-tr-2">
              <td>Lũy kế</td>
              <td *ngFor="let prop of listProp">
                <ng-container *ngIf="prop.type">
                  {{dataSum[prop.name + '_lke']}}
                </ng-container>
                <ng-container *ngIf="prop.fixValue">
                  {{ prop.fixValue }}
                </ng-container>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</div>

<div class="congthuc_cover p-2">
  <p><strong>Công thức tính:</strong></p>
  <div fxLayout="row" fxLayout.xs="row" fxLayout.sm="row" class="formFieldOutline" fxLayoutAlign="space-between">
    <div fxFlex.gt-sm="24" fxFlex.gt-xs="49.5" fxFlex='grow'>
      <p>(2)=(3)+(4)+(5)+(7)</p>
      <p>(6)=(5)/(2)*100</p>
      <p>(8)=(7)/(2)*100</p>
      <p>(9)=(5)+(7)</p>
      <p>(10)=(9)/(2)*100</p>
    </div>

    <div fxFlex.gt-sm="24" fxFlex.gt-xs="49.5" fxFlex='grow'>
      <p>(12)=(11)/(2)*100</p>
      <p>(13)=(14)+(16)+(18)</p>
      <p>(15)=(14)/(13)*100</p>
      <p>(17)=(16)/(13)*100</p>
    </div>
    <div fxFlex.gt-sm="24" fxFlex.gt-xs="49.5" fxFlex='grow'>
      <p>(19)=(18)/(13)*100</p>
      <p>(22)=(25)+(28)</p>
      <p>(24)=(23)/(22)*100</p>
      <p>(27)=(26)/(25)*100</p>
    </div>
    <div fxFlex.gt-sm="24" fxFlex.gt-xs="49.5" fxFlex='grow'>
      <p>(30)=(29)/(28)*100</p>
      <p>(33)=(32)/(31)*100</p>
      <p>(37)=(36)/(34)*100</p>
      <p>(39)=(38)/(35)*100</p>
    </div>
  </div>
</div>

<ng-template #reusableTemplate let-list="list" let-total="total" let-level="level">
  <ng-container *ngFor="let item of list; let i = index;">
    <tr>
      <td rowspan="2">
        {{ i+ 1 }}
      </td>
      <td rowspan="2">
        {{ item.name }}
      </td>
      <td>Trong kỳ</td>
      <td *ngFor="let prop of listProp">
        <ng-container *ngIf="prop.type">
          <ng-container *ngIf="prop.type == 'hs'">
            <a (click)="onClickTd(item, prop.name + '_tky', 0, '')">
              {{ item[prop.name + '_tky'] }}
            </a>
          </ng-container>
          <ng-container *ngIf="prop.type == 'tt'">
            <a (click)="onClickTd(item, prop.name + '_tky', 1, (level == 2 ? 'HUYENXA' : 'SO'))">
              {{ item[prop.name + '_tky'] }}
            </a>
          </ng-container>
          <ng-container *ngIf="prop.type == 'tl'">
            {{ item[prop.name + '_tky'] }}
          </ng-container>
        </ng-container>
        <ng-container *ngIf="prop.fixValue">
          {{ prop.fixValue }}
        </ng-container>
      </td>
    </tr>
    <tr>
      <td>Lũy kế đến ngày {{denNgayLuyke | date: 'dd/MM/yyyy'}}</td>
      <td *ngFor="let prop of listProp">
        <ng-container *ngIf="prop.type">
          <ng-container *ngIf="prop.type == 'hs'">
            <a (click)="onClickTd(item, prop.name + '_lke', 0, '')">
              {{ item[prop.name + '_lke'] }}
            </a>
          </ng-container>
          <ng-container *ngIf="prop.type == 'tt'">
            <a (click)="onClickTd(item, prop.name + '_lke', 1, (level == 2 ? 'HUYENXA' : 'SO'))">
              {{ item[prop.name + '_lke'] }}
            </a>
          </ng-container>
          <ng-container *ngIf="prop.type == 'tl'">
            {{ item[prop.name + '_lke'] }}
          </ng-container>
        </ng-container>
        <ng-container *ngIf="prop.fixValue">
          {{ prop.fixValue }}
        </ng-container>
      </td>
    </tr>
  </ng-container>
  <!-- total row -->
  <tr class="sum-tr">
    <td colspan="2" rowspan="2">
      Tổng
    </td>
    <td>Trong kỳ</td>
    <td *ngFor="let prop of listProp">
      <ng-container *ngIf="prop.type">
        {{total[prop.name + '_tky']}}
      </ng-container>
      <ng-container *ngIf="prop.fixValue">
        {{ prop.fixValue }}
      </ng-container>
    </td>
  </tr>
  <tr class="sum-tr">
    <td>Lũy kế</td>
    <td *ngFor="let prop of listProp">
      <ng-container *ngIf="prop.type">
        {{total[prop.name + '_lke']}}
      </ng-container>
      <ng-container *ngIf="prop.fixValue">
        {{ prop.fixValue }}
      </ng-container>
    </td>
  </tr>
</ng-template>