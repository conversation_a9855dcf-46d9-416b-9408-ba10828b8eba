import { After<PERSON>iew<PERSON>nit, Component, On<PERSON><PERSON>roy, OnInit, ViewChild } from '@angular/core';
import { FormGroup, FormControl } from '@angular/forms';
import { DossierSearchElement } from 'src/app/data/schema/dossier-search-element';
import { MatTableDataSource } from '@angular/material/table';
import { DossierService } from 'src/app/data/service/dossier/dossier.service';
import { ActivatedRoute, Router } from '@angular/router';
import { EnvService } from 'src/app/core/service/env.service';
import { DatePipe } from '@angular/common';
import { SelectionModel } from '@angular/cdk/collections';
import { SnackbarService } from 'src/app/data/service/snackbar/snackbar.service';
import { MatDialog } from '@angular/material/dialog';
import {
  ProcessSelectionComponent,
  ConfirmProcessSelectionDialogModel
} from 'src/app/modules/dossier/pages/online-reception/dialogs/process-selection/process-selection.component';
import {
  DeleteD<PERSON>ierComponent,
  ConfirmDeleteDialogModel
} from 'src/app/modules/dossier/pages/processing/dialogs/delete-dossier/delete-dossier.component';
import { KeycloakService } from 'keycloak-angular';
import { UserService } from 'src/app/data/service/user.service';
import {
  UpdateHistoryComponent,
  UpdateHistoryDialogModel
} from 'src/app/modules/dossier/pages/processing/dialogs/update-history/update-history.component';
import { MainService } from 'src/app/data/service/main/main.service';
import { ProcedureService } from 'src/app/data/service/procedure/procedure.service';
import { ViewProcessDiagramComponent, ViewProcessDiagramDialogModel } from 'src/app/shared/components/view-process-diagram/view-process-diagram.component';
import { ReplaySubject, Subject } from 'rxjs';
import { MatSelect } from '@angular/material/select';
import { takeUntil } from 'rxjs/operators';
import { DeploymentService } from 'src/app/data/service/deployment.service';
import * as tUtils from 'src/app/data/service/thoai.service';
import { ConfirmRestoreDialogModel, RestoreDossierComponent } from './dialogs/restore-dossier/restore-dossier.component';
import { SectorService } from './../../../../data/service/sector/sector.service';
import { authenticationStatus } from 'src/app/data/service/config.service';
import { WithdrawComponentHcm, WithdrawModelHcm } from 'shared/components/approve-withdraw-hcm/approve-withdraw-hcm.component';
import { NotificationV2Service } from 'data/service/etl-data/notificationV2.service';
import {TrinamService} from 'modules/hbh/trinam.service';
import { ApprovalQBHComponent, ApprovalQBHDialog } from './../../pages/processing/dialogs/approval-qbh/approval-qbh.component';
import { ReturnCancelProcessingDossierComponent, ReturnCancelProcessingDossierDialogModel } from './dialogs/return-cancel-processing-dossier/return-cancel-processing-dossier';

@Component({
  selector: 'app-cancel',
  templateUrl: './cancel.component.html',
  styleUrls: ['./cancel.component.scss', '/src/app/app.component.scss', '/src/app/shared/scss/form-field-outline.scss']
})
export class CancelComponent implements OnInit, AfterViewInit, OnDestroy {
  depConfig = this.deploymentService.getAppDeployment();
  config = this.envService.getConfig();
  env = this.deploymentService.getAppDeployment()?.env;
  selectedLang: string;
  // excel
  excelData = [];
  waitingDownloadExcel = false;
  listNation = [];
  listProvince = [];
  listDistrict = [];
  listVillage = [];
  xpandStatus = false;
  isCheckedAll = false;
  pageTitle = {
    vi: `Tiếp nhận hồ sơ trực tuyến`,
    en: `Online dossier reception`
  };
  timeOutGetListSector: any = null;
  searchForm = new FormGroup({
    code: new FormControl(''),
    identityNumber: new FormControl(''),
    applicantName: new FormControl(''),
    ownerFullname: new FormControl(''),
    sectorCtrl: new FormControl(''),
    searchSectorCtrl: new FormControl(),
    procedureCtrl: new FormControl(''),
    searchProcedureCtrl: new FormControl(),
    nation: new FormControl(''),
    province: new FormControl(''),
    district: new FormControl(''),
    village: new FormControl(''),
    acceptFrom: new FormControl(''),
    acceptTo: new FormControl(''),
    applyMethod: new FormControl(''),
    dossierStatus: new FormControl('6'),
    advnoidungyeucaugiaiquyet: new FormControl(''),
    cancelFrom: new FormControl(''),
    cancelTo: new FormControl(''),
    applicantOrganization: new FormControl(''),
  });
  qni_advancedsearch = false;
  // Sector infinity scroll with search
  private listSector: any[] = [];
  listSectorPage = 0;
  isFullListSector = false;
  searchSectorKeyword = '';
  sectorCtrl: FormControl = new FormControl();
  searchSectorCtrl: FormControl = new FormControl();
  protected sectors: any[] = this.listSector;
  public sectorFiltered: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  @ViewChild('sectorMatSelectInfiniteScroll', { static: true }) sectorMatSelectInfiniteScroll: MatSelect;

  // Procedure infinity scroll with search
  private listProcedure: any[] = [];
  listProcedurePage = 0;
  isFullListProcedure = false;
  searchProcedureKeyword = '';
  procedureCtrl: FormControl = new FormControl();
  searchProcedureCtrl: FormControl = new FormControl();
  protected procedures: any[] = this.listProcedure;
  public procedureFiltered: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  @ViewChild('procedureMatSelectInfiniteScroll', { static: true }) procedureMatSelectInfiniteScroll: MatSelect;

  protected onDestroy = new Subject<void>();
  isOwnerFullname = this.env?.isOwnerFullname === 1;
  // ================================================= Table
  displayedColumnsQNI: string[] = ['select', 'stt', 'code', 'procedureName', 'profileOwner', 'appliedDate', 'reason', "citizenWithdrawComment", 'status', 'action'];
  displayedColumnsDefault: string[] = ['select', 'stt', 'code', 'procedureName', 'applicantName', 'appliedDate', 'reason', "citizenWithdrawComment", 'status', 'action'];
  displayedColumns: string[] =  this.env?.isOwnerFullname === 1 ? this.displayedColumnsQNI : this.displayedColumnsDefault;
  displayedColumnsQBH: string[] = ['select', 'stt', 'code', 'noidung', 'applicantName', 'appliedDate', 'reason', "citizenWithdrawComment", 'status', 'action'];
  ELEMENTDATA: DossierSearchElement[] = [];
  dataSource: MatTableDataSource<DossierSearchElement>;
  selection = new SelectionModel<any>(true, []);
  ExportPageDossiersToExcelQNI = false;
  //IGATESUPP-27318: phucnh.it2
  showStatusVnpost = this.env?.OS_HCM?.dossier?.showStatusVnpost ? this.env?.OS_HCM?.dossier?.showStatusVnpost : 0;
  //IGATESUPP-41626
  onlyDisplayNationCode = this.deploymentService.env.OS_HCM.hideNationCodeIfExist2TypeOfCode ? this.deploymentService.env.OS_HCM.hideNationCodeIfExist2TypeOfCode : false;
 
   // QBH 
   qbhlistlayout = this.deploymentService?.env?.OS_QBH?.qbhlistlayout ? this.deploymentService?.env?.OS_QBH?.qbhlistlayout : false;
  //IGATESUPP-50532
  showStopProcessing = false;
  listAgencyStopProcessing=this.deploymentService.env?.OS_HCM?.listAgencyStopProcessing
  // IGATESUPP-89251: [iGate2.0][QNI] - Luồng xử lý hồ sơ dừng xử lý khi đang yêu cầu bổ sung - 26.06.2024
  enablePauseWhenAdditional = this.deploymentService.getAppDeployment()?.enablePauseWhenAdditional ? this.deploymentService.getAppDeployment()?.enablePauseWhenAdditional : false
  // IGATESUPP-122003: [iGate2.0][QNI] - Điều chỉnh lại hồ sơ Dừng xử lý - 08.04.2025
  processingFlowStopProcessingNAST = this.deploymentService.getAppDeployment()?.processingFlowStopProcessingNAST ? this.deploymentService.getAppDeployment()?.processingFlowStopProcessingNAST : 0;
  overDueAdditionalMenuRemindTaskId = this.deploymentService.getAppDeployment()?.overDueAdditionalMenuRemindTaskId ? this.deploymentService.getAppDeployment()?.overDueAdditionalMenuRemindTaskId : "669f1083e374be63429b632a";
  returnOverDueAdditionalMenuRemindTaskId = this.deploymentService.getAppDeployment()?.returnOverDueAdditionalMenuRemindTaskId ? this.deploymentService.getAppDeployment()?.returnOverDueAdditionalMenuRemindTaskId : "66aadee3e374be63429b645c";

  showDossierByUserSector = this.deploymentService?.newConfigV2?.showDossierByUserSector;
  userSectorIds = '';

  enableTooltip=  this.deploymentService.env.OS_HCM.showToolTipsHCM ;
  countResult = 0;
  paginationType = this.deploymentService.env.paginationType;
  showButtonDetailDBN = this.env?.showButtonDetailDBN ? this.env?.showButtonDetailDBN : false;
  size = this.env?.OS_KTM?.defaultPageSize ? this.env?.OS_KTM?.defaultPageSize : 10;
  qbhrenamemenu = this.deploymentService.env?.OS_QBH?.qbhrenamemenu ? this.deploymentService.env?.OS_QBH?.qbhrenamemenu : false;
  qbhwithdrawprocess = this.deploymentService?.env?.OS_QBH?.qbhwithdrawprocess ? this.deploymentService?.env?.OS_QBH?.qbhwithdrawprocess : false;
  configWorkInterfaceKHA: boolean = this.deploymentService?.getAppDeployment()?.configWorkInterfaceKHA || false;
  twoLevelPublicAdministration  =  this.deploymentService.getAppDeployment()?.twoLevelPublicAdministration ? this.deploymentService.getAppDeployment()?.twoLevelPublicAdministration : false;
  page = 1;
  pageIndex = 1;
  pgSizeOptions = this.config.pageSizeOptions;
  paramsQuery = {
    code: '',
    identity: '',
    applicant: '',
    page: '1',
    size: this.env?.OS_KTM?.defaultPageSize ? this.env?.OS_KTM?.defaultPageSize : 10,
    procedure: '',
    nation: '',
    sector: '',
    province: '',
    district: '',
    village: '',
    acceptFrom: '',
    acceptTo: '',
    remindId: '',
    cancelFrom: '',
    cancelTo: '',
  };

  // Accepter Info
  accepterInfo = {
    id: ''
  };

  userAgency = JSON.parse(localStorage.getItem('userAgency'));

  receptionForm: any = [
    {
      id: 0,
      name: [
        {
          languageId: 228,
          name: 'Trực tuyến',
          content: ' tiếp nhận trực tuyến'
        },
        {
          languageId: 46,
          name: 'Online',
          content: ' online reception'
        }
      ]
    },
    {
      id: 1,
      name: [
        {
          languageId: 228,
          name: 'Trực tiếp',
          content: ' tiếp nhận trực tiếp'
        },
        {
          languageId: 46,
          name: 'Direct',
          content: ' direct reception'
        }
      ]
    },
    {
      id: 2,
      name: [
        {
          languageId: 228,
          name: 'Cổng dịch vụ công quốc gia',
          content: ' tiếp nhận từ cổng Dịch vụ công quốc gia'
        },
        {
          languageId: 46,
          name: 'National public service portal',
          content: ' received from the National Public Service portal'
        }
      ]
    }
  ];
  searchCancel = this.deploymentService.env.OS_QNM.searchCancel;
  remindCancelId = this.deploymentService.env.OS_QNM.remindCancelId;
  isRefuse = false;
  applyAgencyShowAutoDeadlineAdditionDossier: boolean = false;
  selectedDossiers = [];
  selectedDossiersHCM = [];
  arrReceptionForm: any = [];
  expandReminderMenu = true;
  remindId = '';
  listMenuRemind: any = [];
  notAcceptedStatus: any;
  cancelled: any;
  lengthRemind = 0;
  fullname: string;
  userName: string;
  userId: string;
  accountId: string;
  dossierTaskStatusWithdraw = { id: '', name: []};
  dossierMenuTaskRemindWithdraw = {id: '', name: []};
  isAdmin = false;
  hasDossierDeletePermission = false;
  checkProvineAdmin? = JSON.parse(localStorage.getItem('superAdmin'));
  showBtnRestoreDossier = false;
  showCitizenWithdrawComment = this.deploymentService.env?.OS_HCM?.showCitizenWithdrawComment ? this.deploymentService.env.OS_HCM.showCitizenWithdrawComment : false;
  cancelDossierAgencyHCM = this.deploymentService.env.OS_HCM.cancelDossierAgencyHCM;
  isShowFilterSector = false;
  enableFilterSectorIs = this.deploymentService.env?.OS_HCM_SCT_LV?.isEnableFilter ? this.deploymentService.env?.OS_HCM_SCT_LV?.isEnableFilter : false;
  agencyUnitEnableFilterSector = this.deploymentService.env?.OS_HCM_SCT_LV?.listAgencyIdUnit ? this.deploymentService.env?.OS_HCM_SCT_LV?.listAgencyIdUnit : [];
  enableShowFilterOrganizationDossierCancel = this.deploymentService.env.OS_HCM.enableShowFilterOrganizationDossierCancel;
  showDossierName = this.deploymentService.env.OS_HGG.showDossierName;
  allowOfficerToApproveWithdrawRequest = this.deploymentService.env.OS_HCM.allowOfficerToApproveWithdrawRequest.enable && tUtils.isAllowedAgency(localStorage, this.deploymentService.env.OS_HCM.allowOfficerToApproveWithdrawRequest.agencyUsed);

  showSectorOnlineReception = this.deploymentService?.newConfigV2?.showSectorOnlineReception;

  exportCancelDossiersOnPage = this.deploymentService.env.OS_HCM.exportCancelDossiersOnPage.enable &&
                             tUtils.isAllowedAgency(localStorage, this.deploymentService.env.OS_HCM.exportCancelDossiersOnPage.agencyUsed);
  listDisplayedDossier = [];
  checkAll = false;
  numberOfElements = 0;

  chungThucDienTu = this.deploymentService.getAppDeployment()?.chungThucDienTu;
  showStatusCTDT = this.chungThucDienTu?.showStatus ? this.chungThucDienTu?.showStatus : 0;
  listAuthenticationStatus = this.chungThucDienTu?.authenticationStatus ? this.chungThucDienTu?.authenticationStatus : authenticationStatus;
  
  //QBH
  qbhlist_collapse = this.deploymentService?.env?.OS_QBH?.qbhlist_collapse ? this.deploymentService?.env?.OS_QBH?.qbhlist_collapse : false;

  canceledProcessingQNI = this.env?.OS_QNI?.canceledProcessing ? this.env?.OS_QNI?.canceledProcessing : false;

  vbdlisConfigId = this.deploymentService?.env?.OS_QNI?.vbdlisConfigId;
  scrollOverflowRefuseReasonText = this.deploymentService?.env?.scrollOverflowRefuseReasonText;
  enableShowAutoDeadlineAdditionDossier = this.deploymentService.getAppDeployment()?.showAutoDeadlineAdditionDossier?.enable || 0;
  listShowAutoDeadlineAdditionDossierAgencyId = this.deploymentService.getAppDeployment()?.showAutoDeadlineAdditionDossier?.agencyId || [];

  isNotification = this.deploymentService.env.notification.isNotification;
  notReceiveStopProcessing = this.deploymentService.getAppDeployment().notReceiveStopProcessing === true ? true : false;  // IGATESUPP-86673: hiện HS dừng xử lý ở menu không cần xử lý
  interfaceWorkHorizontal = this.deploymentService.getAppDeployment().interfaceWorkHorizontal == 1? 1 : 0;
  showCreateSearchOwnerFullname = this.deploymentService.getAppDeployment()?.showCreateSearchOwnerFullname === 1;
  configColumTable: string[] = !!this.deploymentService?.getAppDeployment()?.displayedColumns?.hosoKhongCanXuLy ?
      Object.values(this.deploymentService?.getAppDeployment()?.displayedColumns?.hosoKhongCanXuLy) : this.displayedColumnsDefault;
  configDisplayColumns: boolean = this.deploymentService?.getAppDeployment()?.configDisplayColumns || false;
  checkNullData =0;
  constructor(
    private router: Router,
    private dossierService: DossierService,
    private activeRoute: ActivatedRoute,
    private envService: EnvService,
    private datePipe: DatePipe,
    private dialog: MatDialog,
    private snackbarService: SnackbarService,
    private keycloakService: KeycloakService,
    private userService: UserService,
    private procedureService: ProcedureService,
    private mainService: MainService,
    private deploymentService: DeploymentService,
    private sectorService: SectorService,
    private notificationV2Service: NotificationV2Service,
    private trinamService: TrinamService,
  ) {
    this.dataSource = new MatTableDataSource(this.ELEMENTDATA);
    if (this.activeRoute.snapshot.queryParamMap.get('code') != null) {
      this.paramsQuery.code = this.activeRoute.snapshot.queryParamMap.get('code');
      this.searchForm.patchValue({
        code: this.paramsQuery.code
      });
    }
    if (this.activeRoute.snapshot.queryParamMap.get('identity') != null) {
      this.paramsQuery.identity = this.activeRoute.snapshot.queryParamMap.get('identity');
      this.searchForm.patchValue({
        identityNumber: this.paramsQuery.identity
      });
    }
    if (this.activeRoute.snapshot.queryParamMap.get('applicant') != null) {
      this.paramsQuery.applicant = this.activeRoute.snapshot.queryParamMap.get('applicant');
      this.searchForm.patchValue({
        applicantName: this.paramsQuery.applicant
      });
    }
    if (this.activeRoute.snapshot.queryParamMap.get('sector') != null) {
      this.paramsQuery.sector = this.activeRoute.snapshot.queryParamMap.get('sector');
      this.searchForm.patchValue({
        sectorCtrl: this.paramsQuery.sector
      });
    }
    if (this.activeRoute.snapshot.queryParamMap.get('procedure') != null) {
      this.paramsQuery.procedure = this.activeRoute.snapshot.queryParamMap.get('procedure');
      this.searchForm.patchValue({
        procedureCtrl: this.paramsQuery.procedure
      });
    }
    if (this.activeRoute.snapshot.queryParamMap.get('nation') != null) {
      this.paramsQuery.nation = this.activeRoute.snapshot.queryParamMap.get('nation');
      this.searchForm.patchValue({
        nation: this.paramsQuery.nation
      });
    }
    if (this.activeRoute.snapshot.queryParamMap.get('province') != null) {
      this.paramsQuery.province = this.activeRoute.snapshot.queryParamMap.get('province');
    }
    if (this.activeRoute.snapshot.queryParamMap.get('district') != null) {
      this.paramsQuery.district = this.activeRoute.snapshot.queryParamMap.get('district');
    }
    if (this.activeRoute.snapshot.queryParamMap.get('village') != null) {
      this.paramsQuery.village = this.activeRoute.snapshot.queryParamMap.get('village');
    }
    if (this.activeRoute.snapshot.queryParamMap.get('acceptFrom') != null) {
      this.paramsQuery.acceptFrom = this.activeRoute.snapshot.queryParamMap.get('acceptFrom');
      if (this.paramsQuery.acceptFrom !== '') {
        this.searchForm.patchValue({
          acceptFrom: new Date(this.datePipe.transform(this.paramsQuery.acceptFrom, 'dd/MM/yyyy'))
        });
      }
    }
    if (this.activeRoute.snapshot.queryParamMap.get('acceptTo') != null) {
      this.paramsQuery.acceptTo = this.activeRoute.snapshot.queryParamMap.get('acceptTo');
      if (this.paramsQuery.acceptTo !== '') {
        this.searchForm.patchValue({
          acceptTo: new Date(this.datePipe.transform(this.paramsQuery.acceptTo, 'dd/MM/yyyy'))
        });
      }
    }
    if (this.activeRoute.snapshot.queryParamMap.get('page') != null) {
      this.paramsQuery.page = this.activeRoute.snapshot.queryParamMap.get('page');
    }
    if (this.activeRoute.snapshot.queryParamMap.get('size') != null) {
      this.paramsQuery.size = this.activeRoute.snapshot.queryParamMap.get('size');
    }
    if (this.activeRoute.snapshot.queryParamMap.get('remindId') != null) {
      this.remindId = this.paramsQuery.remindId = this.activeRoute.snapshot.queryParamMap.get('remindId');
    }
    if (this.activeRoute.snapshot.queryParamMap.get('organization') != null) {
      this.searchForm.patchValue({
        applicantOrganization: this.activeRoute.snapshot.queryParamMap.get('organization')
      });
    }

    if (this.activeRoute.snapshot.queryParamMap.get('cancelFrom') != null) {
      this.paramsQuery.cancelFrom = this.activeRoute.snapshot.queryParamMap.get('cancelFrom');
      if (this.paramsQuery.cancelFrom !== '') {
        this.searchForm.patchValue({
          cancelFrom: new Date(this.datePipe.transform(this.paramsQuery.cancelFrom, 'dd/MM/yyyy'))
        });
      }
    }
    if (this.activeRoute.snapshot.queryParamMap.get('cancelTo') != null) {
      this.paramsQuery.cancelTo = this.activeRoute.snapshot.queryParamMap.get('cancelTo');
      if (this.paramsQuery.cancelTo !== '') {
        this.searchForm.patchValue({
          cancelTo: new Date(this.datePipe.transform(this.paramsQuery.cancelTo, 'dd/MM/yyyy'))
        });
      }
    }
        // QBH layout
    if(this.qbhlistlayout == true)
    {
      this.displayedColumns = this.displayedColumnsQBH;
    }
    if (this.configDisplayColumns) {
      this.displayedColumns = this.configColumTable;
    }
  }

  ngOnInit(): void {
    console.log(this.depConfig)
    const userAgency = JSON.parse(localStorage.getItem('userAgency'));
    if(this.enableShowAutoDeadlineAdditionDossier == 1)
      {
            let agencyId = JSON.parse(localStorage.getItem("userAgency")).parent?.id || "";
            this.applyAgencyShowAutoDeadlineAdditionDossier =  this.listShowAutoDeadlineAdditionDossierAgencyId.filter(item => item == agencyId).length > 0; 
      }
    if(this.notReceiveStopProcessing || this.listAgencyStopProcessing.filter(item => item == userAgency.id || item == userAgency?.parent?.id || item == userAgency?.ancestors?.id).length > 0){
      this.showStopProcessing = true;
    }
    if(this.showStopProcessing){
      this.searchForm.patchValue({dossierStatus:'6,12'})
    }
    if(this.enableShowAutoDeadlineAdditionDossier == 1 && this.applyAgencyShowAutoDeadlineAdditionDossier == true)
    {
      this.searchForm.patchValue({dossierStatus:'6,12,24'})
    }
    if(this.enableFilterSectorIs){
       if(this.agencyUnitEnableFilterSector.filter(item => item == userAgency.id || item == userAgency?.parent?.id || item == userAgency?.ancestors?.id).length > 0) {
        this.isShowFilterSector = true;
       }
    }
    if(this.env?.OS_QNI?.qni_advancedsearch === true){ 
      this.qni_advancedsearch= this.env?.OS_QNI?.qni_advancedsearch; 
    }
    if(this.env?.OS_QNI?.ExportPageDossiersToExcelQNI === true){
      this.ExportPageDossiersToExcelQNI= this.env?.OS_QNI?.ExportPageDossiersToExcelQNI;
    }
    this.selectedLang = localStorage.getItem('language');
    // tslint:disable-next-line: prefer-for-of
    for (let i = 0; i < this.receptionForm.length; i++) {
      const arrName: any = {};
      // tslint:disable-next-line: prefer-for-of
      for (let j = 0; j < this.receptionForm[i].name.length; j++) {
        if (Number(localStorage.getItem('languageId')) === this.receptionForm[i].name[j].languageId) {
          arrName.id = this.receptionForm[i].id;
          arrName.name = this.receptionForm[i].name[j].name;
          arrName.content = this.receptionForm[i].name[j].content;
        }
      }
      this.arrReceptionForm.push(arrName);
    }
    this.mainService.setPageTitle(this.pageTitle[this.selectedLang]);
    const permissions = this.userService.getUserPermissions();
    for (const p of permissions) {
      if (p.permission.code === 'admin' || p.permission.code === 'oneGateAdminMaster') {
        this.isAdmin = true;
        break;
      }
    }
    for (const p of permissions) {
      if(p.permission.code === 'admin' || p.permission.code === 'oneGateAdminMaster' || p.permission.code === 'oneGateDossierDelete')
      {
        this.hasDossierDeletePermission = true;
        break;
      }
    }

    if (this.showDossierByUserSector) {
      let accountId = localStorage.getItem('tempUID');
      this.sectorService.getUserSectorOnlyOne(accountId, this.userAgency.id).subscribe(data => {
        if (!!data.sectorIds && data.sectorIds.length !== 0){
          this.userSectorIds = data.sectorIds;
        }
      })
    }

    this.getListNation();
    this.getListSector();
    this.getListProcedure();
    this.getAccepterInfo();
    this.getUserAccount();
    this.getDossierStatusNotAccepted();
    this.getDossierTaskStatus();
    this.getDossierMenuTaskRemind();

    this.showBtnRestoreDossier = this.env?.OS_HCM?.showBtnRestoreDossier == "1" ? true : false;

    if(this.qbhlist_collapse){
      this.xpandStatus = true;
      if(this.depConfig?.addressDefault?.nationId){
        const nationId = this.searchForm.controls['nation'].value;
        if(!nationId){
          const defaultNationId = this.depConfig?.addressDefault?.nationId;
          this.searchForm.patchValue({nation: defaultNationId});
          this.nationChange({value: defaultNationId});
        }
        setTimeout(() => {
          const provinceId = this.searchForm.controls[ 'province' ].value;
          if(!provinceId){
            const defaultProvinceId = this.depConfig?.addressDefault?.provinceId;
            this.searchForm.patchValue({province: defaultProvinceId});
            this.provinceChange({value: defaultProvinceId});
          }
        }, 2000);
      }
    }
  }

  getDisplayedColumns() {
    if (this.showCitizenWithdrawComment && this.remindId === this.dossierMenuTaskRemindWithdraw.id) {
      return this.displayedColumns;
    }
    else {
      return this.displayedColumns.filter(item => item !== "citizenWithdrawComment");
    }
  }

  getUserAccount() {
    this.keycloakService.loadUserProfile().then(user => {
      // tslint:disable-next-line: no-string-literal
      this.userId = user['attributes'].user_id[0];
      this.accountId = user['attributes'].account_id[0];
      this.userName =  user.username;
      this.userService.getUserInfo(this.userId).subscribe(data => {
        this.fullname = data.fullname;
      });
    });
  }

  ngAfterViewInit() {
    setTimeout(() => {
      // console.clear();
      this.autoSearch();
    }, 1000);
  }

  ngOnDestroy() {
    this.onDestroy.next();
    this.onDestroy.complete();
  }
  // ========================================================== GET
  getListDossier(searchString) {
    this.dossierService.getListDossier(searchString).subscribe(data => {
      this.numberOfElements = data.numberOfElements;
      this.ELEMENTDATA = [];
      if (data.content.length > 0) {
        this.checkNullData = 0;
      } else {
        this.checkNullData = 1;
      }
      this.pageIndex = data.number + 1;
      let isAllChecked = true;
      for (let i = 0; i < data.numberOfElements; i++) {
        //IGATESUPP-41626
        if(this.onlyDisplayNationCode && !!data.content[i].nationCode && data.content[i].nationCode != ''){
          data.content[i].code = data.content[i].nationCode;
          data.content[i].nationCode = '';
        }
        let requireAdditional = true;
        data.content[i].stt = this.size * (this.pageIndex - 1) + (i + 1);
        // tslint:disable-next-line: prefer-for-of
        for (let m = 0; m < this.arrReceptionForm.length; m++) {
          if (data.content[i].applyMethod.id === this.arrReceptionForm[m].id) {
            data.content[i].codeText = data.content[i].code + this.arrReceptionForm[m].content;
          }
        }
        // dossier task status
        if (data.content[i].dossierTaskStatus !== undefined && data.content[i].dossierTaskStatus !== null ){
          data.content[i].dossierStatus.name = data.content[i].dossierTaskStatus.name;
          // tslint:disable-next-line:max-line-length
          if (data.content[i].dossierTaskStatus.id === this.deploymentService.env.dossierTaskStatus?.requestForAdditionalDocuments.id || data.content[i].dossierTaskStatus.id === this.deploymentService.env.dossierTaskStatus?.dossierAdded.id){
            requireAdditional = false;
          }
        }else{
          if (data.content[i].dossierStatus.id === 0 && data.content[i].dossierStatus.comment === ''){
            // tslint:disable-next-line:max-line-length
            data.content[i].dossierStatus.name = this.cancelled['trans'].filter(res => Number(res.languageId) === Number(localStorage.getItem('languageId')))[0].name;
          }else if(data.content[i].dossierStatus.id === 0 && data.content[i].dossierStatus.comment !== ''){
            // tslint:disable-next-line:max-line-length
            data.content[i].dossierStatus.name = this.notAcceptedStatus['trans'].filter(res => Number(res.languageId) === Number(localStorage.getItem('languageId')))[0].name;
          }else{
            data.content[i].dossierStatus.name = data.content[i].currentTask[0].bpmProcessDefinitionTask.name['name'];
          }
        }

        // Handle export excel
        if (!this.listDisplayedDossier.filter(dossier => dossier.id === data.content[i].id)[0]) {
          this.listDisplayedDossier.push(data.content[i]);
        }
        // Handle checkbox
        for (let j=0; j<this.selectedDossiersHCM.length; j++) {
          if(data.content[i]["id"] == this.selectedDossiersHCM[j]){
            data.content[i].checked = true;
            break;
          }else{
            data.content[i].checked = false;
          }
        }
        if(!data.content[i].checked){
          isAllChecked = false;
        }
        // End Handle checkbox

        data.content[i].requireAdditional = requireAdditional;
        this.ELEMENTDATA.push(data.content[i]);
      }
      this.checkAll = isAllChecked;
      this.dataSource.data = this.ELEMENTDATA;
      this.setTotalElements(data, this.paginationType);
    });
  }
  getDossierStatusNotAccepted() {
    this.dossierService.getAgencyTag(this.deploymentService.env.dossierTaskStatus?.notAccepted?.id).subscribe(rs => {
      this.notAcceptedStatus = rs;
    }, err => {
      console.log(err);
    });
  }
  getDossierStatusCancelled() {
    this.dossierService.getAgencyTag(this.deploymentService.env.dossierTaskStatus?.cancelled?.id).subscribe(rs => {
      this.cancelled = rs;
    }, err => {
      console.log(err);
    });
  }

  protected filterSectorS() {
    if (!this.sectors) {
      return;
    }
    let search = this.searchSectorCtrl.value.trim();
    this.searchSectorKeyword = search;
    if (!search) {
      this.sectors = JSON.parse(JSON.stringify(this.listSector).replace(/null/g, '""'));
      this.sectorFiltered.next(this.sectors);
      return;
    } else {
      search = search.toLowerCase();
    }
   
    if (this.sectors.filter(sec => sec.name.toLowerCase().indexOf(search) > -1).length > 0) {
      this.sectorFiltered.next(
        this.sectors.filter(sec => sec.name.toLowerCase().indexOf(search) > -1)
      );
    } else {
    if (this.isFullListSector == true) {
      this.sectors = JSON.parse(JSON.stringify(this.listSector).replace(/null/g, '""'));
      this.sectorFiltered.next(
        this.sectors.filter(sec => sec.name.toLowerCase().indexOf(search) > -1)
      );
    } else {
    
      this.sectorService.getListSector('?page=' + this.listSectorPage + '&size=50&sort=name.name,asc&status=1&all-agency=0').subscribe(data => {
        console.log('api sector ', data);
              
        this.listSectorPage++;
        this.isFullListSector = data.last;
        for (let i = 0; i < data.numberOfElements ; i++) {
          
          this.listSector.push(data.content[i]);
        }
        // this.listSector.sort((a, b) => a.name.localeCompare(b.name));
        this.sectors = JSON.parse(JSON.stringify(this.listSector).replace(/null/g, '""'));
        this.sectorFiltered.next(this.sectors);
      } , err => {
        console.log(err);
      }) ;
    }
  }
  }

  async getListSector() {
    const search = !this.searchSectorKeyword.trim() ? "" : this.searchSectorKeyword.toLowerCase();
    if (this.isFullListSector) {
      return;
    } else {
      if(this.isShowFilterSector || this.showSectorOnlineReception){
        console.log('this.userAgency')
        console.log(this.userAgency)
        let searchString =  '?agency-id=' + this.userAgency.id;
        if (this.showSectorOnlineReception) {
          searchString =  '?agency-id=' + this.userAgency.parent.id;
        }
        const data1 = await this.sectorService.getListSectorAll(searchString + '&only-agency-id=1').toPromise();
        const data2 = await this.sectorService.getListSector('?page=' + this.listSectorPage + '&size=' + 1000 + '&spec=page&sort=name.name,asc&status=1').toPromise();
         console.log('data1', data1);
         if(data1.length > 0){
          const data = data1;
          console.log('api sector ', data);
          this.isFullListSector = true;
          for (let i = 0; i < data.length ; i++) {
            data[i].name = data[i].name[0].name;
            this.listSector.push(data[i]);
          }
          this.listSector.sort((a, b) => a.name.localeCompare(b.name));
          this.sectors = JSON.parse(JSON.stringify(this.listSector).replace(/null/g, '""'));
          this.sectorFiltered.next(this.sectors);
          this.searchSectorCtrl.valueChanges.pipe(takeUntil(this.onDestroy)).subscribe(() => {
            this.filterSectorS();
          });
        }
        else {
          const searchString = '?keyword=' + this.searchSectorKeyword + '&page=' + this.listSectorPage + '&size=' + 1000 + '&spec='+this.paginationType +'&sort=name.name,asc&status=1';
          this.procedureService.getListSector(searchString).subscribe(data => {
            this.isFullListSector = data.last;
            this.listSectorPage++;
            for (let i = 0; i < data.numberOfElements; i++) {
              this.listSector.push(data.content[i]);
            }
            // this.listSector.sort((a, b) => a.name.localeCompare(b.name));
            this.sectors = JSON.parse(JSON.stringify(this.listSector).replace(/null/g, '""'));
            this.sectorFiltered.next(
              this.sectors.filter(sec => sec.name.toLowerCase().indexOf(search) > -1)
            );
            this.searchSectorCtrl.valueChanges.pipe(takeUntil(this.onDestroy)).subscribe(() => {
              this.filterSector(this.searchSectorKeyword);
            });
          }, err => {
            console.log(err);
          });
       }
      } else {
          const searchString = '?keyword=' + this.searchSectorKeyword + '&page=' + this.listSectorPage + '&size=' + 1000 + '&spec='+this.paginationType +'&sort=name.name,asc&status=1';
          this.procedureService.getListSector(searchString).subscribe(data => {
            this.isFullListSector = data.last;
            this.listSectorPage++;
            for (let i = 0; i < data.numberOfElements; i++) {
              this.listSector.push(data.content[i]);
            }
            // this.listSector.sort((a, b) => a.name.localeCompare(b.name));
            this.sectors = JSON.parse(JSON.stringify(this.listSector).replace(/null/g, '""'));
            this.sectorFiltered.next(
              this.sectors.filter(sec => sec.name.toLowerCase().indexOf(search) > -1)
            );
            this.searchSectorCtrl.valueChanges.pipe(takeUntil(this.onDestroy)).subscribe(() => {
              this.filterSector(this.searchSectorKeyword);
            });
          }, err => {
            console.log(err);
          });
    }
    }
  }

  getListProcedure() {
    if (this.isFullListProcedure) {
      return;
    } else {
      let agencyIdSearch = '';
      if ( this.userAgency !== null && this.userAgency !== undefined && !this.isAdmin) {
        if (!!this.userAgency.parent && !!this.userAgency.parent.id) {
          agencyIdSearch = `&agency-id=${this.userAgency.parent.id}`;
        } else if (this.userAgency.id !== this.config.rootAgency.id) {
          agencyIdSearch = `&agency-id=${this.userAgency.id}`;
        }
      }
      agencyIdSearch = this.env?.filterProcedureByAgencyId ? agencyIdSearch : '';
      const searchString =
        '?status=1&sort=translate.name,asc&keyword=' + this.searchProcedureKeyword +
        '&spec='+this.paginationType +'&page=' + this.listProcedurePage + '&size=50' +
        '&sector-id=' + this.paramsQuery.sector + agencyIdSearch;
      this.procedureService.getListProcedure(searchString).subscribe(data => {
        this.isFullListProcedure = data.last;
        this.listProcedurePage++;
        for (let i = 0; i < data.numberOfElements; i++) {
          this.listProcedure.push(data.content[i]);
        }
        this.procedures = JSON.parse(JSON.stringify(this.listProcedure).replace(/null/g, '""'));
        this.procedureFiltered.next(this.procedures);
        this.searchProcedureCtrl.valueChanges.pipe(takeUntil(this.onDestroy)).subscribe(() => {
          this.filterProcedure();
        });
      }, err => {
        console.log(err);
      });
    }
  }

  getListNation() {
    this.dossierService.getListNation().subscribe(data => {
      this.listNation = data;
    }, err => {
      console.log(err);
    });
  }

  getListProvince() {
    this.dossierService.getListPlace(
      this.searchForm.get('nation').value,
      null,
      this.config.placeProvinceTypeId
    ).subscribe(data => {
      this.listProvince = data;
      this.searchForm.patchValue({
        province: this.paramsQuery.province
      });
      if (this.searchForm.get('province').value !== '') {
        this.getListDistrict();
      }
    }, err => {
      console.log(err);
    });
  }

  getListDistrict() {
    this.dossierService.getListPlace(
      this.searchForm.get('nation').value,
      this.searchForm.get('province').value,
      this.config.placeDistrictTypeId
    ).subscribe(data => {
      this.listDistrict = data;
      this.searchForm.patchValue({
        district: this.paramsQuery.district
      });
      if (this.searchForm.get('district').value !== '') {
        this.getListVillage();
      }
    }, err => {
      console.log(err);
    });
  }

  getListVillage() {
    this.dossierService.getListPlace(
      this.searchForm.get('nation').value,
      this.searchForm.get('district').value,
      this.config.placeWardTypeId
    ).subscribe(data => {
      this.listVillage = data;
      this.searchForm.patchValue({
        village: this.paramsQuery.village
      });
    }, err => {
      console.log(err);
    });
  }

  getStatusColor(type) {
    switch (type) {
      case 0: return '#000000de';
      case 1: return '#FF9800';
      case 2: return '#f39c12';
      case 3: return '#FF9800';
      case 4: return '#03A9F4';
      case 5: return '#03A9F4';
      case 6: return '#DE1212';
      case 12: return this.processingFlowStopProcessingNAST === 1 ? '#DE1212' : '#000000de';
      case 24: return '#DE1212';
      default: return '#000000de';
    }
  }

  setAuthStatusName(statusId){
    const status = this.listAuthenticationStatus.find(status => status.id == statusId);
    if (status) {
      return status.name;
    } else {
      return "Không xác định";
    }
  }

  getAccepterInfo() {
    this.keycloakService.loadUserProfile().then(user => {
      // tslint:disable-next-line: no-string-literal
      this.accepterInfo.id = user['attributes'].user_id[0];

      const userAgency = JSON.parse(localStorage.getItem('userAgency'));
      if (!!userAgency) {
        this.userAgency = userAgency;
        this.getRemindMenuTask();
      }
    });
  }
  // ========================================================== ON CHANGE
  nationChange(event) {
    this.getListProvince();
    this.listDistrict = [];
    this.listVillage = [];
  }

  provinceChange(event) {
    this.getListDistrict();
    this.listVillage = [];
  }

  districtChange(event) {
    this.getListVillage();
  }

  printReportChange(event) {
    event.value = '';
  }

  sectorChange(event) {
    this.paramsQuery.sector = event.value;
    this.router.navigate([], {
      queryParams: {
        sector: event.value
      },
      queryParamsHandling: 'merge'
    });
    this.onConfirmSearch();
    this.listProcedure = [];
    this.isFullListProcedure = false;
    this.listProcedurePage = 0;
    this.getListProcedure();
  }

  async onConfirmSearch() {
    console.log("selection", this.selection, this.isAllSelected());
    const formObj = this.searchForm.getRawValue();
    const checkDate = await this.checkDateValid(formObj.acceptFrom, formObj.acceptTo);
    const checkDateCancel = await this.checkDateValid(formObj.cancelFrom, formObj.cancelTo);
    if ((this.isRefuse && checkDate && checkDateCancel)|| (!this.isRefuse && checkDate)) {
      this.selection.clear();
      const searchString = this.generateSearchString('page', 0, this.size, 'id,desc');
      this.paramsQuery = {
        code: formObj.code.trim(),
        identity: formObj.identityNumber.trim(),
        applicant: formObj.applicantName.trim(),
        page: '1',
        size: this.size.toString(),
        procedure: formObj.procedureCtrl,
        nation: formObj.nation,
        sector: formObj.sectorCtrl,
        province: formObj.province,
        district: formObj.district,
        village: formObj.village,
        acceptFrom: (formObj.acceptFrom ? this.datePipe.transform(formObj.acceptFrom, 'dd/MM/yyyy') : ''),
        acceptTo: (formObj.acceptTo ? this.datePipe.transform(formObj.acceptTo, 'dd/MM/yyyy') : ''),
        remindId: this.remindId,
        cancelFrom: (formObj.cancelFrom ? this.datePipe.transform(formObj.cancelFrom, 'dd/MM/yyyy') : ''),
        cancelTo: (formObj.cancelTo ? this.datePipe.transform(formObj.cancelTo, 'dd/MM/yyyy') : ''),
      };
      this.pageIndex = 1;
      this.page = 1;

      this.router.navigate([], {
        queryParams: {
          identity: formObj.identityNumber.trim(),
          applicant: formObj.applicantName.trim(),
          page: 1,
          size: this.size.toString(),
          procedure: formObj.procedureCtrl,
          sector: formObj.sectorCtrl,
          province: formObj.province,
          district: formObj.district,
          village: formObj.village,
          acceptFrom: (formObj.acceptFrom ? this.datePipe.transform(formObj.acceptFrom, 'dd/MM/yyyy') : ''),
          acceptTo: (formObj.acceptTo ? this.datePipe.transform(formObj.acceptTo, 'dd/MM/yyyy') : ''),
          remindId: this.remindId,
          cancelFrom: (formObj.cancelFrom ? this.datePipe.transform(formObj.cancelFrom, 'dd/MM/yyyy') : ''),
          cancelTo: (formObj.cancelTo ? this.datePipe.transform(formObj.cancelTo, 'dd/MM/yyyy') : ''),
        }
      });
      this.getListDossier(searchString);
    }
  }

  onClickOpenAdvancedSearchBox() {
    this.xpandStatus = this.xpandStatus ? false : true;
    if(this.xpandStatus){
      if(this.depConfig?.addressDefault?.nationId){
        const nationId = this.searchForm.controls['nation'].value;
        if(!nationId){
          const defaultNationId = this.depConfig?.addressDefault?.nationId;
          this.searchForm.patchValue({nation: defaultNationId});
          this.nationChange({value: defaultNationId});
        }
        setTimeout(() => {
          const provinceId = this.searchForm.controls[ 'province' ].value;
          if(!provinceId){
            const defaultProvinceId = this.depConfig?.addressDefault?.provinceId;
            this.searchForm.patchValue({province: defaultProvinceId});
            this.provinceChange({value: defaultProvinceId});
          }
        }, 2000);
      }
    }
  }

  // ========================================================== FUNCTION
  generateAddressIs2Cap(data) {
    if (!data || typeof data !== 'object') {
        return '';
    }

    const addressParts = [];

    // Village - hỗ trợ nhiều trường village
    const villageData = data?.village2 || data?.village1 || data?.village || data?.PhuongXa || data?.PhuongXa1;
    if (villageData) {
        const villageName = villageData.label || villageData.name || (typeof villageData === 'string' ? villageData : '');
        if (villageName && typeof villageName === 'string') {
            addressParts.push(villageName.trim());
        }
    }

    // Province - hỗ trợ nhiều trường province
    const provinceData = data?.province2 || data?.province1 || data?.province || data?.TinhTP || data?.TinhTP1;
    if (provinceData) {
        const provinceName = provinceData.label || provinceData.name || (typeof provinceData === 'string' ? provinceData : '');
        if (provinceName && typeof provinceName === 'string') {
            addressParts.push(provinceName.trim());
        }
    }

    // Kết thúc bằng dấu chấm nếu có tỉnh/thành phố
    if (addressParts.length > 0) {
        if (addressParts.length === 2) {
            return addressParts[0] + ', ' + addressParts[1] + '.';
        }
        return addressParts.join(', ');
    }
    return '';
  }

  generateAddressFix(data) {
    // return this.mainService.generateAddress(placeObj);
    if (!data || typeof data !== 'object') {
        return '';
    }
    
    const addressParts = [];
    
    // Village
    if (data?.village?.label) {
        addressParts.push(data.village.label);
    }
    
    // District
    if (data?.district?.label) {
        addressParts.push(data.district.label);
    }
    
    // Province
    if (data?.province?.label) {
        addressParts.push(data.province.label);
    }
    
    // Nation
    if (data?.nation?.label) {
        addressParts.push(data.nation.label);
    }
    
    return addressParts.join(', ');
  }

  generateAddress(data) {
    // return this.mainService.generateAddress(placeObj);
    let address = '';
    if (data?.village !== undefined && data?.village !== null) {
      address += data.village.label + ', ';
    }
    if (data?.district !== undefined && data?.district !== null) {
      address += data.district.label + ', ';
    }
    if (data?.province !== undefined && data?.province !== null) {
      address += data.province.label + ', ';
    }
    if (data?.nation !== undefined && data?.nation !== null) {
      address += data.nation.label;
    }
    return address;
  }

  checkDateValid(startDate, endDate) {
    return new Promise((resolve) => {
      try {
        if (startDate.getTime() > endDate.getTime()) {
          const msgObj = {
            vi: 'Ngày bắt đầu phải nhỏ hơn ngày kết thúc!',
            en: 'Start date must be lesser than end date!'
          };
          this.snackbarService.openSnackBar(0, '', msgObj[this.selectedLang], 'error_notification', this.config.expiredTime);
          resolve(false);
        } else {
          resolve(true);
        }
      } catch (error) {
        resolve(true);
      }
    });
  }

  getCheckAgencyShowFilterOrganization() {
    if(this.enableShowFilterOrganizationDossierCancel && this.enableShowFilterOrganizationDossierCancel.enable){
      //Kiểm tra đơn vị
      let list = this.enableShowFilterOrganizationDossierCancel.agencyIds;
      const userAgency = JSON.parse(localStorage.getItem('userAgency'));
      if (userAgency){
        var check = list.find(o => o == userAgency.id);
        if(check){
          return true;
        }
        else {
          if (!!userAgency.parent && !!userAgency.parent.id) {
            check = list.find(o => o == userAgency.parent.id);
            if(check){
              return true;
            }
          }
        }
      }
      return false;
    }
  }

  generateSearchString(spec, page, size, sort) {
    const formObj = this.searchForm.getRawValue();
    let  remindId = this.remindId ? this.remindId : this.config.listStatusViewCancel
    if(this.enableShowAutoDeadlineAdditionDossier == 1 && this.applyAgencyShowAutoDeadlineAdditionDossier == true)
    {
      remindId = remindId + ",677787a96b55b06e54ba660e"; // id dossierTaskMenuRemind: quá hạn bổ sung hồ sơ
    }
    let searchString = 'search?code=' + encodeURIComponent(formObj.code.trim()).substring(0, 1000) +
      '&spec=' + this.paginationType +
      '&page=' + page +
      '&size=' + size +
      '&sort=' + sort +
      '&identity-number=' + encodeURIComponent(formObj.identityNumber.trim()).substring(0, 1000) +
      '&applicant-name=' + encodeURIComponent(formObj.applicantName.trim()).substring(0, 1000) +
      '&applicant-owner-name=' + encodeURIComponent(formObj.ownerFullname.trim()).substring(0, 1000) +
      '&nation-id=' + formObj.nation +
      '&province-id=' + formObj.province +
      '&district-id=' + formObj.district +
      '&ward-id=' + formObj.village +
      '&accepted-from=' + (formObj.acceptFrom ? this.datePipe.transform(formObj.acceptFrom, 'dd/MM/yyyy') : '') +
      '&accepted-to=' + (formObj.acceptTo ? this.datePipe.transform(formObj.acceptTo, 'dd/MM/yyyy') : '') +
      '&agency-id=' + this.userAgency.id +
      '&noidungyeucaugiaiquyet=' + formObj.advnoidungyeucaugiaiquyet.trim() +
      '&apply-method-id=' + formObj.applyMethod +
      '&applicant-organization='+ encodeURIComponent(formObj.applicantOrganization.trim()).substring(0, 1000) +
      '&remind-id=' +  remindId;

    if (this.canceledProcessingQNI){
      if(this.enablePauseWhenAdditional){
        searchString += '&dossier-status=1,6,12';
      }else{
        if(this.enableShowAutoDeadlineAdditionDossier == 1 && this.applyAgencyShowAutoDeadlineAdditionDossier == true)
          {
            searchString += '&dossier-status=6,12,24'; 
          }else
          {
            searchString += '&dossier-status=6,12';
          }
      }
    }else{
      if(this.enablePauseWhenAdditional){
        searchString += '&dossier-status=1,' + formObj.dossierStatus;
      }else{
        searchString += '&dossier-status=' + formObj.dossierStatus;
      }
    }
    if (formObj.sectorCtrl !== null && formObj.sectorCtrl !== '') {
      searchString += '&sector-id=' + formObj.sectorCtrl;
    } else if (this.showDossierByUserSector) {
      searchString += '&sector-id=' + this.userSectorIds;
    }
    if (formObj.procedureCtrl !== null) {
      searchString += '&procedure-id=' + formObj.procedureCtrl;
    }
    if (!!this.userAgency?.parent?.id && this.userAgency?.parent?.id !== '')
    {
      searchString += '&&parent-agency-id=' + this.userAgency?.parent?.id;
    }
    if(this.cancelDossierAgencyHCM){
      searchString += '&isHCM=' + this.cancelDossierAgencyHCM;
    }
    if(this.isRefuse){
      searchString += '&cancel-from=' + (formObj.cancelFrom ? this.datePipe.transform(formObj.cancelFrom, 'dd/MM/yyyy') : '');
      searchString += '&cancel-to=' + (formObj.cancelTo ? this.datePipe.transform(formObj.cancelTo, 'dd/MM/yyyy') : '');
    }
    return searchString;
  }

  autoSearch() {
    this.pageIndex = Number(this.paramsQuery.page);
    this.page = Number(this.paramsQuery.page);
    this.size = Number(this.paramsQuery.size);
    const searchString = this.generateSearchString('page', (this.page - 1), this.size, 'id,desc');
    this.getListDossier(searchString);
  }

  // paginate(event: any, type) {
  //   switch (type) {
  //     case 0:
  //       this.pageIndex = event;
  //       this.page = event;
  //       const searchString = this.generateSearchString('page', (this.pageIndex - 1), this.size, 'id,desc');
  //       this.getListDossier(searchString);
  //       this.router.navigate([], {
  //         queryParams: {
  //           code: this.paramsQuery.code,
  //           identity: this.paramsQuery.identity.trim(),
  //           applicant: this.paramsQuery.applicant.trim(),
  //           page: this.pageIndex,
  //           size: this.size,
  //           procedure: this.paramsQuery.procedure,
  //           sector: this.paramsQuery.sector,
  //           province: this.paramsQuery.province,
  //           district: this.paramsQuery.district,
  //           village: this.paramsQuery.village,
  //           acceptFrom: (this.paramsQuery.acceptFrom ? this.datePipe.transform(this.paramsQuery.acceptFrom, 'dd/MM/yyyy') : ''),
  //           acceptTo: (this.paramsQuery.acceptTo ? this.datePipe.transform(this.paramsQuery.acceptTo, 'dd/MM/yyyy') : ''),
  //           remindId: this.paramsQuery.remindId
  //         }
  //       });
  //       break;
  //     case 1:
  //       this.pageIndex = 1;
  //       this.page = 1;
  //       const searchString2 = this.generateSearchString('page', (this.pageIndex - 1), this.size, 'id,desc');
  //       this.getListDossier(searchString2);
  //       this.router.navigate([], {
  //         queryParams: {
  //           code: this.paramsQuery.code,
  //           identity: this.paramsQuery.identity.trim(),
  //           applicant: this.paramsQuery.applicant.trim(),
  //           page: 1,
  //           size: this.size,
  //           procedure: this.paramsQuery.procedure,
  //           sector: this.paramsQuery.sector,
  //           province: this.paramsQuery.province,
  //           district: this.paramsQuery.district,
  //           village: this.paramsQuery.village,
  //           acceptFrom: (this.paramsQuery.acceptFrom ? this.datePipe.transform(this.paramsQuery.acceptFrom, 'dd/MM/yyyy') : ''),
  //           acceptTo: (this.paramsQuery.acceptTo ? this.datePipe.transform(this.paramsQuery.acceptTo, 'dd/MM/yyyy') : ''),
  //           remindId: this.paramsQuery.remindId
  //         }
  //       });
  //       break;
  //   }
  // }
  paginate() {
    const searchString = this.generateSearchString('page', (this.pageIndex - 1), this.size, 'id,desc');
    this.getListDossier(searchString);
  }

  isAllSelected() {
    const numSelected = this.selection.selected.length;
    const numRows = this.dataSource.data.length;
    return numSelected === numRows;
  }

  masterToggle() {
    console.log("this.isAllSelected()", this.isAllSelected());
    this.isAllSelected() ?
      this.selection.clear() :
      this.dataSource.data.forEach(row => this.selection.select(row));
      console.log("this.selection()", this.selection);
  }

  checkboxLabel(row?: any): string {
    if (!row) {
      return `${this.isAllSelected() ? 'select' : 'deselect'} all`;
    }
    return `${this.selection.isSelected(row) ? 'deselect' : 'select'} row ${row.position + 1}`;
  }

  onEnter(type, event) {
    switch (type) {
      case 'sector': {
        clearTimeout(this.timeOutGetListSector);
        this.timeOutGetListSector = setTimeout(async () => {
          this.searchSectorKeyword = event.target.value;
          this.filterSector(this.searchSectorKeyword);
        }, 300);
        break;
      }
    }
  }

  async resetSearchForm(type) {
    switch (type) {
      case 'sector': {
        this.searchSectorKeyword = '';
        this.filterSector(this.searchSectorKeyword);
        break;
      }
    }
  }

  protected filterSector(search) {
    if (!this.sectors) {
      return;
    }
    // let search = this.searchSectorKeyword;
    this.searchSectorKeyword = search;
    if (!search) {
      this.sectorFiltered.next(this.sectors.slice());
      return;
    } else {
      search = search.toLowerCase();
    }
    if (this.sectors.filter(sec => sec.name.toLowerCase().indexOf(search) > -1).length > 0) {
      this.sectorFiltered.next(
        this.sectors.filter(sec => sec.name.toLowerCase().indexOf(search) > -1)
      );
    } else {
      this.listSectorPage = 0;
      const searchString = '?keyword=' + search + '&page=0&size=1000&spec='+this.paginationType +'&sort=name.name,asc&status=1';
      this.procedureService.getListSector(searchString).subscribe(data => {
        for (let i = 0; i < data.numberOfElements; i++) {
          this.listSector.push(data.content[i]);
        }
        // this.listSector.sort((a, b) => a.name.localeCompare(b.name));
        this.sectors = JSON.parse(JSON.stringify(this.listSector).replace(/null/g, '""'));
        this.sectorFiltered.next(
          this.sectors.filter(sec => sec.name.toLowerCase().indexOf(search) > -1)
        );
      }, err => {
        console.log(err);
      });
    }
  }

  

  protected filterProcedure() {
    if (!this.procedures) {
      return;
    }
    let search = encodeURIComponent(this.searchProcedureCtrl.value.trim()).substring(0, 1000);
    this.searchProcedureKeyword = search;
    if (!search) {
      this.procedureFiltered.next(this.procedures.slice());
      return;
    } else {
      search = search.toLowerCase();
    }
    if (this.procedures.filter(proc => proc.name.toLowerCase().indexOf(search) > -1).length > 0) {
      this.procedureFiltered.next(
        this.procedures.filter(proc => proc.name.toLowerCase().indexOf(search) > -1)
      );
    } else {
      // tslint:disable-next-line: max-line-length
      let agencyIdSearch = '';
      if ( this.userAgency !== null && this.userAgency !== undefined && !this.isAdmin) {
        if (!!this.userAgency.parent && !!this.userAgency.parent.id) {
          agencyIdSearch = `&agency-id=${this.userAgency.parent.id}`;
        } else if (this.userAgency.id !== this.config.rootAgency.id) {
          agencyIdSearch = `&agency-id=${this.userAgency.id}`;
        }
      }
      agencyIdSearch = this.env?.filterProcedureByAgencyId ? agencyIdSearch : '';
      const searchString =
        '?status=1&sort=translate.name,asc&keyword=' + search +
        '&spec='+this.paginationType +'&page=0&size=50' +
        '&sector-id=' + this.paramsQuery.sector + agencyIdSearch;;
      this.listProcedurePage = 0;
      this.procedureService.getListProcedure(searchString).subscribe(data => {
        for (let i = 0; i < data.numberOfElements; i++) {
          this.listProcedure.push(data.content[i]);
        }
        this.procedures = JSON.parse(JSON.stringify(this.listProcedure).replace(/null/g, '""'));
        this.procedureFiltered.next(
          this.procedures
        );
      }, err => {
        console.log(err);
      });
    }
  }

  // ========================================================== DIALOGS

  processSelection(procedureId, userName, dossierId, appliedDate) {
    const dialogData = new ConfirmProcessSelectionDialogModel(procedureId, userName, dossierId, appliedDate);
    const dialogRef = this.dialog.open(ProcessSelectionComponent, {
      minWidth: '50vw',
      data: dialogData,
      disableClose: true,
      autoFocus: false
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
      const res = dialogResult;
      if (res === true) {
      }
      if (res === false) {
      }
    });
  }

  deleteDialog(dossierId, dossierCode) {
    const dialogData = new ConfirmDeleteDialogModel(dossierId, dossierCode);
    const dialogRef = this.dialog.open(DeleteDossierComponent, {
      width: '500px',
      data: dialogData,
      disableClose: true,
      autoFocus: false
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
      if (dialogResult === 0) {
        const msgObj = {
          vi: 'Đã xoá hồ sơ!',
          en: 'Dossier deleted!'
        };
        this.snackbarService.openSnackBar(1, msgObj[this.selectedLang], '', 'success_notification', this.config.expiredTime);
        const searchString = this.generateSearchString('page', (this.pageIndex - 1), this.size, 'id,desc');
        this.getListDossier(searchString);
      }

      if (dialogResult === 1) {
        const msgObj = {
          vi: 'Đã hủy hồ sơ!',
          en: 'Dossier cancelled!'
        };
        this.snackbarService.openSnackBar(1, msgObj[this.selectedLang], '', 'success_notification', this.config.expiredTime);
        const searchString = this.generateSearchString('page', (this.pageIndex - 1), this.size, 'id,desc');
        this.getListDossier(searchString);
      }

      if (dialogResult === null) {
        const msgObj = {
          vi: 'Xoá không thành công!',
          en: 'Deletion failed!'
        };
        this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
      }
      this.callRemindTask();
    });
  }

  viewUpdateHistory(dossierId, dossierCode) {
    const dialogData = new UpdateHistoryDialogModel(dossierId, dossierCode);
    const dialogRef = this.dialog.open(UpdateHistoryComponent, {
      minWidth: '70vw',
      data: dialogData,
      disableClose: true,
      autoFocus: false
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
    });
  }

  viewProcess(prDefId) {
    this.procedureService.getProcedureProcessDetail(prDefId).subscribe(data => {
      if (data !== undefined && data !== null) {
        // tslint:disable-next-line: max-line-length
        const dialogData = new ViewProcessDiagramDialogModel(data.processDefinition.activiti.model.id, data.processDefinition.name, data.processDefinition.id);
        const dialogRef = this.dialog.open(ViewProcessDiagramComponent, {
          width: '80vw',
          minHeight: '80vh',
          data: dialogData,
          disableClose: true,
          autoFocus: false
        });
        dialogRef.afterClosed().subscribe(dialogResult => {
        });
      } else {
        const msgObj = {
          vi: 'Không tìm thấy quy trình cho hồ sơ này!',
          en: 'No process found for this dossier!'
        };
        this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
      }
    }, err => {
      const msgObj = {
        vi: 'Không tìm thấy quy trình cho hồ sơ này!',
        en: 'No process found for this dossier!'
      };
      this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
    });
  }
  dossierDetail(dossierId, procedureId, task) {
    const queryParamsObject = {
      procedure: procedureId,
    };

    if (task !== undefined) {
      Object.assign(queryParamsObject, {task: task[task.length - 1].id});
    }

    this.router.navigate(['dossier/cancel/' + dossierId], {
      queryParams: queryParamsObject
    });
  }
  // ======== Menu remind
  getRemindMenuTask(){
    // //debugger 
    this.listMenuRemind = [];
    // tslint:disable-next-line:max-line-length
    if(this.showStopProcessing){
      this.config.listStatusViewCancel+=',61ee30eada2d36b037e00005'
    }
    if(this.enableShowAutoDeadlineAdditionDossier == 1 && this.applyAgencyShowAutoDeadlineAdditionDossier == true){
         this.config.listStatusViewCancel+=',677787a96b55b06e54ba660e'
    }
    if(this.canceledProcessingQNI){
      this.config.listStatusViewCancel+=',61ee30eada2d36b037e00005';
    }
    if(this.enablePauseWhenAdditional){
      this.config.listStatusViewCancel+=`,${this.returnOverDueAdditionalMenuRemindTaskId}`;
    }
    if(this.processingFlowStopProcessingNAST == "1"){
      this.config.listStatusViewCancel+=',61ee30eada2d36b037e00005'
    }
    let searchString = 'remind-id=' + this.config.listStatusViewCancel
                     + '&in-list=1'
                     + '&agency-id=' + this.userAgency.id
                        
    if(this.showStopProcessing){
      searchString +='&dossier-status=6,12';
    }

    if(this.canceledProcessingQNI){
      if(this.enablePauseWhenAdditional){
        searchString +='&dossier-status=1,6,12';
      }else{
        if(this.enableShowAutoDeadlineAdditionDossier == 1 && this.applyAgencyShowAutoDeadlineAdditionDossier == true)
          {
            searchString += '&dossier-status=6,12,24'; 
          }else
          {
            searchString += '&dossier-status=6,12';
          }
      }
    }else{
      if(this.enablePauseWhenAdditional){
        searchString +='&dossier-status=1,6';
      }else{
        searchString += '&dossier-status=6'
      }
    }

    if (!!this.userAgency?.parent?.id && this.userAgency?.parent?.id !== '')
    {
      searchString += '&parent-agency-id=' + this.userAgency?.parent?.id;
    }
    if(this.cancelDossierAgencyHCM){
      searchString += '&isHCM=' + this.cancelDossierAgencyHCM;
    }

    // STAGE: 3 => Ho so khong can xu ly
    if (this.isNotification) {
      const userExperienceAgency = JSON.parse(localStorage.getItem('userExperienceAgency'));

      const searchRemind = 'stage=3' +
        '&assignee-id=' + localStorage.getItem('UID') +
        '&position-id=' + userExperienceAgency?.position?.id +
        '&agency-id=' + this.userAgency.id;

      this.notificationV2Service.getNotificationCurrent(searchRemind).subscribe(data => {
        this.listMenuRemind = data;
        this.lengthRemind = data.length;
        this.listMenuRemind = this.listMenuRemind.sort((a, b) => {
          return compare(a.count, b.count, false);
        });
        if (data.length === 0) {
          this.expandReminderMenu = false;
        }
      });
    } else {
    if(!this.deploymentService.getAppDeployment()?.disableMenuRemind)
    {
    this.dossierService.getDossierMenuTaskRemindAllAgency(searchString).subscribe(data => {
      // this.listMenuRemind = data.content;
      // tslint:disable-next-line:prefer-for-of
      for (let i = 0; i < data.length; i++){
        if (data[i].id !== this.config.dossierReminderRecallId){
          this.listMenuRemind.push(data[i]);
        }
      }
      if (this.canceledProcessingQNI && !this.processingFlowStopProcessingNAST){
        for (var i = 0; i < this.listMenuRemind.length; i++) {
          if (this.listMenuRemind[i].id === "61ee30eada2d36b037e00005") {
            this.listMenuRemind.splice(i,1);
            break;
          }
        }
      }
      this.listMenuRemind = this.listMenuRemind.sort((a, b) => {
        return compare(a.count, b.count, false);
      });
      this.lengthRemind = this.listMenuRemind.length;
      if (data.content.length === 0){
        this.expandReminderMenu = false;
      }
    });
    }
    console.log("this.listMenuRemind", this.listMenuRemind);
    }
  }
  onClickOpenReminderMenu(){
    this.remindId = '';
    this.expandReminderMenu = !this.expandReminderMenu;
    this.changeSearchRemind('');
  }
  changeSearchRemind(remindId){
    this.selection.clear();
    console.log("selection", this.selection, this.isAllSelected());
    this.remindId = remindId;
    if(this.searchCancel && this.remindId == this.remindCancelId){
      this.isRefuse = true;
    }else{
      this.isRefuse = false;
    }
    this.pageIndex = 1;
    this.page = 1;
    this.size = 10;
    const searchString = this.generateSearchString('page', 0, this.size, 'id,desc');
    this.getListDossier(searchString);
  }
  callRemindTask(){
    this.getRemindMenuTask();
    // this.adminLayoutNavComponent.getDossierRemind();
  }
  // === start xac nhan cong dan da rut hs
  getDossierTaskStatus() {
    this.dossierService.getAgencyTag(this.deploymentService.env.dossierTaskStatus?.hasWithdrawn?.id).subscribe(rs => {
      this.dossierTaskStatusWithdraw.id = rs.id;
      this.dossierTaskStatusWithdraw.name = rs.trans;
    }, err => {
      console.log(err);
    });
  }
  getDossierMenuTaskRemind() {
    this.dossierService.getAgencyTag(this.deploymentService.env.dossierMenuTaskRemind?.hadWithdrawn.id).subscribe(rs => {
      this.dossierMenuTaskRemindWithdraw.id = rs.id;
      this.dossierMenuTaskRemindWithdraw.name = rs.trans;
    }, err => {
      console.log(err);
    });
  }
  confirmHasWithdraw(row){
    let dossierId = row.id;
    let dossierCode = row.code;
    let commentOld = row.dossierStatus.comment;
    let requestBodyObj:any;
    requestBodyObj={
      dossierStatus: row.dossierStatus.id,
      comment: commentOld,
      dossierTaskStatus: this.dossierTaskStatusWithdraw,
      dossierMenuTaskRemind: this.dossierMenuTaskRemindWithdraw
    }
    const msgObj = {
      vi: 'Hồ sơ <b>' + dossierCode + '</b> đã rút',
      en: 'Dossier <b>' + dossierCode + '</b> has been withdraw!'
    };
    this.postComment(msgObj[this.selectedLang], dossierId);
    const requestBody = JSON.stringify(requestBodyObj, null, 2);
    this.dossierService.putDossierStatusWithComment(dossierId, requestBody).subscribe( async data => {
      if (data.affectedRows === 1) {
        if (this.deploymentService?.env?.OS_HBH?.enableLDXHTriNam) {
          const procedureData = await this.procedureService.getProcedureDetail(row?.procedure?.id).toPromise();
          if (!!procedureData?.btxhcode) {
            const dataDossier = await this.dossierService.getDossierDetail(row?.id).toPromise();
            Object.assign(dataDossier, {contentTask: 'Cán bộ xác nhận hồ sơ đã rút'});
            this.trinamService.syncTaskLDXH(dataDossier);
          }
        }
        const searchString = this.generateSearchString('page', (this.pageIndex - 1), this.size, 'id,desc');
        this.getListDossier(searchString);
        this.callRemindTask();
        const msgObjTemp = {
          vi: 'Ngày rút hồ sơ',
          en: 'Withdrawal date'
        };
        // tslint:disable-next-line: max-line-length
        this.postHistory(3, msgObjTemp[this.selectedLang], '' , this.datePipe.transform(tUtils.newDate(), 'dd/MM/yyyy HH:mm:ss'), dossierId);
        const msgObjNoti = {
          vi: 'Hồ sơ ' + dossierCode + ' đã rút thành công',
          en: 'Successful dossier ' + dossierCode + ' has been withdraw success!'
        };
        // tslint:disable-next-line:max-line-length
        this.snackbarService.openSnackBar(1, msgObjNoti[localStorage.getItem('language')], '', 'success_notification', this.config.expiredTime);

        //Gửi hồ sơ sang LGSP HCM
        this.cancelLGSPHCM(row);
         // đồng bộ qua VBDLIS nếu là hồ sơ vbdlis
         if(row.extendQNI?.isVbdlis){
          this.dossierService.postUpdateFinishDossierVbdlis(row.code, this.vbdlisConfigId).subscribe(rs => {
          }, err => {
            console.log(err);
          });
        }
      } else {
      }
    }, err => {
    });
  }
  // IGATESUPP-122003: [iGate2.0][QNI] - Điều chỉnh lại hồ sơ Dừng xử lý - 08.04.2025
  checkShowReturnCancelProcessingBtn(row: any){
    try {
      return this.processingFlowStopProcessingNAST === 1 && 
      row.dossierTaskStatus.id === "61ee30eada2d36b037e00005" && 
      row.dossierStatus.id === 12 && 
      row.dossierStatus?.description !== "Đã trả kết quả dừng";
    } catch (error) {
      return false
    }
  }
  checkShowReturnProcessingDossierInf(row: any) {
    try{
      return this.processingFlowStopProcessingNAST === 1 && 
      row.dossierTaskStatus.id === "61ee30eada2d36b037e00005" && 
      row.dossierStatus.id === 12
    }catch (error) {
      return false;
    }
  }
  returnCancelProcessingDossier(dossierId, dossierCode) {
      const dialogData = new ReturnCancelProcessingDossierDialogModel(dossierId, dossierCode);
      const dialogRef = this.dialog.open(ReturnCancelProcessingDossierComponent, {
        minWidth: '50vw',
        maxHeight: '75vh',
        data: dialogData,
        disableClose: true,
        autoFocus: false
      });
      dialogRef.afterClosed().subscribe(dialogResult => {
        if(dialogResult){
          const msgObj = {
            vi: 'Trả kết quả hồ sơ dừng xử lý thành công!',
            en: 'Returned cancel processing dossier successful!',
          };
          this.snackbarService.openSnackBar(
            1,
            '',
            msgObj[this.selectedLang],
            'warning_notification',
            this.config.expiredTime
          );
          this.onConfirmSearch();
        }
      });
  }
  // IGATESUPP-122003: [iGate2.0][QNI] - Điều chỉnh lại hồ sơ Dừng xử lý - 08.04.2025
  postComment(commentContent, dossierId) {
    const content = {
      groupId: 2,
      itemId: dossierId,
      user: {
        id: this.accountId,
        fullname: this.fullname
      },
      content: commentContent.trim()
    };
    const requestBody = JSON.stringify(content, null, 2);
    this.dossierService.postComment(requestBody).subscribe(data => { });
  }
  postHistory(type, col, oldVal, newVal, dossierId) {
    const content = {
      groupId: 1,
      itemId: dossierId,
      user: {
        id: this.userId,
        name: this.fullname
      },
      type,
      deploymentId: this.config.deploymentId,
      action: [
        {
          fieldNameRbk: col,
          originalValue: oldVal,
          newValue: newVal
        }
      ]
    };
    const requestBody = JSON.stringify(content, null, 2);
    this.dossierService.postHistory(requestBody).subscribe(data => {
    });
  }
  // === end xac nhan cong dan da rut hs

  //Gửi hồ sơ sang LGSP HCM
  cancelLGSPHCM(row) {
    let dossierId = row.id;
    let dossierCode = row.code;
    //Kiểm tra nếu hồ sơ có chứa thông tin trục LGSP thì mới gửi
    if(row.task && row.task.length > 0)
    {
      let task = row.task[0];
      if(task?.bpmProcessDefinitionTask?.dynamicVariable?.hcmLGSP)
      {
        const content = {
          dossier: {
            id: dossierId,
            code: dossierCode
          },
          packageType: "3", //Loại gói tin:  3 - Gói tin rút hồ sơ
        };
        const requestBody = JSON.stringify(content, null, 2);

        this.dossierService.cancelLGSPHCM(requestBody)
        .subscribe((data: any) => {
          if(data.error_code != "0") // 0 thành công #0 thất bại
          {
            const msgObjNoti = {
              vi: 'Hồ sơ ' + dossierCode + ' gửi về LGSP HCM thất bại!',
              en: 'Dossier ' + dossierCode + ' sent to LGSP HCM failed!'
            };
            this.snackbarService.openSnackBar(1, msgObjNoti[localStorage.getItem('language')], '', 'error_notification', this.config.expiredTime);
          }

        }, error => {
          const msgObjNoti = {
            vi: 'Hồ sơ ' + dossierCode + ' gửi về LGSP HCM thất bại!',
            en: 'Dossier ' + dossierCode + ' sent to LGSP HCM failed!'
          };
          this.snackbarService.openSnackBar(1, msgObjNoti[localStorage.getItem('language')], '', 'error_notification', this.config.expiredTime);
        });
      }
    }
  }
  setTotalElements(data, paginationType) {
    if (paginationType === 'page') {
      this.countResult = data.totalElements;
    } else {
      if (data.last) {
        if (!!data.number) {
          this.page = data.number + 1;
          this.countResult = this.size * this.page;
        } else {
          this.countResult = this.size * this.page;
        }
      } else {
        if (data.numberOfElements < this.size) {
          this.countResult = this.size * this.page;
        } else {
          this.countResult = this.countResult + this.ELEMENTDATA.length + 1;
        }
      }
    }
  }

  changePageOrSize(obj) {
    if (tUtils.hasOwnProperty(obj, 'currentPage')) {
      this.page = obj.currentPage;
      this.pageIndex = obj.currentPage;
    }
    if (tUtils.hasOwnProperty(obj, 'itemsPerPage')) {
      this.size = obj.itemsPerPage;
    }
    if (tUtils.hasOwnProperty(obj, 'totalItems')) {
      this.countResult = obj.totalItems;
    } else {
      this.paginate();
    }
  }
  //End gửi hồ sơ sang LGSP HCM

  restoreDialog(dossierId, dossierCode) {
    const dialogData = new ConfirmRestoreDialogModel(dossierId, dossierCode);
    const dialogRef = this.dialog.open(RestoreDossierComponent, {
      width: '500px',
      data: dialogData,
      disableClose: true,
      autoFocus: false
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
      if (dialogResult === 1) {
        const msgObj = {
          vi: 'Đã khôi phục hồ sơ!',
          en: 'Dossier restored!'
        };
        this.snackbarService.openSnackBar(1, msgObj[this.selectedLang], '', 'success_notification', this.config.expiredTime);
        const searchString = this.generateSearchString('page', (this.pageIndex - 1), this.size, 'id,desc');
        this.getListDossier(searchString);
      } else {
        const msgObj = {
          vi: 'Khôi phục không thành công!',
          en: 'Restoration failed!'
        };
        this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
      }
      this.callRemindTask();
    });
  }
  getListDossierAllExcel_QNI() {
    const formObj = this.searchForm.getRawValue();
    this.waitingDownloadExcel = true;
    const  checkIsRemindId = Number(this.remindId) === 0 ? '' : this.remindId;
    let searchString = 'export-excel-qni?code=' + encodeURIComponent(formObj.code.trim()).substring(0, 1000) +
      '&identity-number=' + encodeURIComponent(formObj.identityNumber.trim()).substring(0, 1000) +
      '&applicant-name=' + encodeURIComponent(formObj.applicantName.trim()).substring(0, 1000) +
      '&applicant-owner-name=' + encodeURIComponent(formObj.ownerFullname.trim()).substring(0, 1000) +
      '&nation-id=' + formObj.nation +
      '&province-id=' + formObj.province +
      '&district-id=' + formObj.district +
      '&ward-id=' + formObj.village +
      '&accepted-from=' + (formObj.acceptFrom ? this.datePipe.transform(formObj.acceptFrom, 'dd/MM/yyyy') : '') +
      '&accepted-to=' + (formObj.acceptTo ? this.datePipe.transform(formObj.acceptTo, 'dd/MM/yyyy') : '') +
      '&agency-id=' + this.userAgency.id +
      '&noidungyeucaugiaiquyet=' + formObj.advnoidungyeucaugiaiquyet.trim() +
      '&apply-method-id=' + formObj.applyMethod;
      '&remind-id=' +  checkIsRemindId;
    if (this.canceledProcessingQNI){
      searchString += '&dossier-status=6,12';
    }else{
      searchString += '&dossier-status=' + formObj.dossierStatus;
    }
    if (formObj.sectorCtrl !== null) {
      searchString += '&sector-id=' + formObj.sectorCtrl;
    }
    if (formObj.procedureCtrl !== null) {
      searchString += '&procedure-id=' + formObj.procedureCtrl;
    }
    if (!!this.userAgency?.parent?.id && this.userAgency?.parent?.id !== '')
    {
      searchString += '&&parent-agency-id=' + this.userAgency?.parent?.id;
    }
    this.dossierService.getListDossier(searchString).subscribe(async result => {
      this.excelData = result;
      this.dossierService.exportPageDossierToExcelQni(this.excelData, "danh sách hồ sơ"," Sheet1");
      this.waitingDownloadExcel = false;
    }, error => {
      console.log(error);
      this.waitingDownloadExcel = false;
    });
  }
  downloadAllFile(id) {
    this.dossierService.getAllFileDossier(id).subscribe(data => {

      data.forEach(element => {
        this.procedureService.downloadFile(element.id, element.id).subscribe(data => {
          const dataType = data.type;
          const binaryData = [];
          binaryData.push(data);
          const downloadLink = document.createElement('a');
          downloadLink.href = window.URL.createObjectURL(new Blob(binaryData, { type: dataType }));
          downloadLink.setAttribute('download', element.filename);
          document.body.appendChild(downloadLink);
          downloadLink.click();
        }, err => {
          const msgObj = {
            vi: 'Không tìm thấy file!',
            en: 'File not found!'
          };
          this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
          console.log(err);
        });
      });

    }, err => {
      const msgObj = {
        vi: 'Không tìm thấy văn bản của hồ sơ!',
        en: 'File dossier not found!'
      };
      this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
      console.log(err);
    });
  }
  openApprovalQBHDialog(id:string,type:number){
    const dialogData = new ApprovalQBHDialog(id,type);
    const dialogRef = this.dialog.open(ApprovalQBHComponent, {
      minWidth: '55vw',
      maxHeight: '90vh',
      data: dialogData,
      autoFocus: false
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
      if(dialogResult == true){
        // this.getRemindMenuTask();
        this.onConfirmSearch();
      }
    });
  }
  exportExcelDossierQNM() {
    const formObj = this.searchForm.getRawValue();
    this.waitingDownloadExcel = true;
    let searchString = '';
    const  remindId = this.remindId ? this.remindId : this.config.listStatusViewCancel;
    this.selectedDossiers = [];
    if(this.selection.selected.length > 0){
     
      for (let i = 0; i < this.selection.selected.length; i++) {
        if (this.selectedDossiers.indexOf(this.selection.selected[i].id) === -1) {
          this.selectedDossiers.push(this.selection.selected[i].id);
        }
      }
    }

    if(this.selectedDossiers.length > 0){
      searchString = 'export-excel-dossier-qnm?dossier-ids='+ this.selectedDossiers;
    }else{
      searchString = 'export-excel-dossier-qnm?code=' + encodeURIComponent(formObj.code.trim()).substring(0, 1000) +
      '&identity-number=' + encodeURIComponent(formObj.identityNumber.trim()).substring(0, 1000) +
      '&applicant-name=' + encodeURIComponent(formObj.applicantName.trim()).substring(0, 1000) +
      '&applicant-owner-name=' + encodeURIComponent(formObj.ownerFullname.trim()).substring(0, 1000) +
      '&nation-id=' + formObj.nation +
      '&province-id=' + formObj.province +
      '&district-id=' + formObj.district +
      '&ward-id=' + formObj.village +
      '&accepted-from=' + (formObj.acceptFrom ? this.datePipe.transform(formObj.acceptFrom, 'dd/MM/yyyy') : '') +
      '&accepted-to=' + (formObj.acceptTo ? this.datePipe.transform(formObj.acceptTo, 'dd/MM/yyyy') : '') +
      '&agency-id=' + this.userAgency.id +
      '&noidungyeucaugiaiquyet=' + formObj.advnoidungyeucaugiaiquyet.trim() +
      '&apply-method-id=' + formObj.applyMethod +
      '&remind-id=' +  remindId;

      if (this.canceledProcessingQNI){
        searchString += '&dossier-status=6,12';
      }else{
        searchString += '&dossier-status=' + formObj.dossierStatus;
      }

      if (formObj.sectorCtrl !== null) {
        searchString += '&sector-id=' + formObj.sectorCtrl;
      }
      if (formObj.procedureCtrl !== null) {
        searchString += '&procedure-id=' + formObj.procedureCtrl;
      }
      if (!!this.userAgency?.parent?.id && this.userAgency?.parent?.id !== '')
      {
        searchString += '&&parent-agency-id=' + this.userAgency?.parent?.id;
      }
      if(this.cancelDossierAgencyHCM){
        searchString += '&isHCM=' + this.cancelDossierAgencyHCM;
      }
      if(this.isRefuse){
        searchString += '&cancel-from=' + (formObj.cancelFrom ? this.datePipe.transform(formObj.cancelFrom, 'dd/MM/yyyy') : '');
        searchString += '&cancel-to=' + (formObj.cancelTo ? this.datePipe.transform(formObj.cancelTo, 'dd/MM/yyyy') : '');
      }
    }

    console.log("nè he:" + searchString);
    this.dossierService.getListDossier(searchString).subscribe(async result => {
      this.excelData = result;
      this.dossierService.exportPageDossierToExcelQNM(this.excelData, "danh sách hồ sơ không cần xử lý"," Sheet1", true);
      this.waitingDownloadExcel = false;
    }, error => {
      console.log(error);
      this.waitingDownloadExcel = false;
    });
  }

  checkAllItem(event){
    if(event.checked){
      this.checkAll = true;
      for(let i=0; i<this.numberOfElements;i++){
        if (this.selectedDossiersHCM.indexOf(this.dataSource.data[i].id) === -1){
          this.selectedDossiersHCM.push(this.dataSource.data[i].id);
          this.dataSource.data[i].checked = true;
        }
      }
    }else{
      this.checkAll = false;
      for(let i=0; i<this.numberOfElements;i++){
        const index = this.selectedDossiersHCM.indexOf(this.dataSource.data[i].id);
        if(index >= 0){
          this.selectedDossiersHCM.splice(index, 1);
          this.dataSource.data[i].checked = false;
        }
      }
    }
  }
  withdrawDialogsHcm(row){
    let dossierId = row.id;
    let dossierCode = row.code;
    if (dossierCode === undefined || dossierCode === null) {
      dossierCode = '';
    }
    const dialogData = new WithdrawModelHcm(dossierId, dossierCode);
    const dialogRef = this.dialog.open(WithdrawComponentHcm, {
      minWidth: '45vw',
      data: dialogData,
      disableClose: true,
      autoFocus: false
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
      // Cán bộ đồng ý phê duyệt rút hồ sơ
      if (dialogResult === true) {
        this.confirmHasWithdraw(row);
      }
      // Cán bộ không đồng ý phê duyệt rút hồ sơ
      // Đưa hồ sơ trở về trạng thái trước đó
      if (dialogResult === false) {
        this.refuseWithdraw(row);
      }
    });
  }

  checkItem(event, id){
    if (event.checked) {
      this.selectedDossiersHCM.push(id);
      let countCheck = true;
      for(let i=0; i<this.numberOfElements; i++){
        if(this.dataSource.data[i].checked != true){
          countCheck = false;
          break;
        }
      }
      this.checkAll = countCheck;
    }
    else{
      this.checkAll = false;
      const i = this.selectedDossiersHCM.indexOf(id);
      this.selectedDossiersHCM.splice(i, 1);
    }
  }

  exportSelectedCancelDossiers() {
    let name = '';
    const newDate = tUtils.newDate();
    name = 'Danh sách hồ sơ không cần xử lí ' + this.datePipe.transform(newDate, 'dd/MM/yyyy');

    const data = this.listDisplayedDossier.filter(dossier => this.selectedDossiersHCM.indexOf(dossier.id) != -1);
    return this.dossierService.exportCancelDossierToExcel(data, name, "DSHS");
  }

  refuseWithdraw(row){
    let dossierId = row.id;
    let dossierCode = row.code;
    this.dossierService.rejectWithdrawRequest(dossierId).subscribe(async data => {
      if (data.affectedRows === 1) {
        const searchString = this.generateSearchString('page', (this.pageIndex - 1), this.size, 'id,desc');
        this.getListDossier(searchString);
        this.callRemindTask();
        const msgObjNoti = 'Hồ sơ ' + dossierCode + ' đã quay lại trạng thái trước đó'
        this.snackbarService.openSnackBar(1, msgObjNoti, '', 'success_notification', this.config.expiredTime);
      }
    });
  }

}
function compare(a: number | string, b: number | string, isAsc: boolean) {
  return (a < b ? -1 : 1) * (isAsc ? 1 : -1);
}

