// ================================= searchForm
::ng-deep .prc_searchbar .searchForm .mat-form-field-appearance-outline .mat-form-field-outline {
    color: transparent;
  }
  
  ::ng-deep .prc_searchbar .searchForm .mat-form-field-appearance-outline .mat-form-field-outline-thick {
    color: #dddddd;
  }
  
  ::ng-deep .prc_searchbar .searchForm .mat-form-field-appearance-outline .mat-form-field-outline {
    background-color: #eaebeb;
    border-radius: 5px;
  }
  
  ::ng-deep .prc_searchbar .searchForm .formFieldItems {
    flex-wrap: wrap;
  }
  
  ::ng-deep .prc_searchbar .mat-form-field-appearance-outline .mat-form-field-infix {
    padding: 0.8em 0;
  }
  
  ::ng-deep .prc_searchbar .mat-form-field-label-wrapper {
    top: -1em;
  }
  
  ::ng-deep .prc_searchbar .mat-form-field.mat-focused .mat-form-field-label {
    color: #ce7a58;
    font-size: 14px;
  }
  
  ::ng-deep .prc_searchbar .mat-form-field-appearance-outline.mat-form-field-can-float.mat-form-field-should-float .mat-form-field-label {
    color: #ce7a58;
    transform: translateY(-1.55em) scale(1);
    margin-bottom: 1em;
  }
  
  ::ng-deep .prc_tbl.dossier_late .mat-column-procedureLevel {
  flex: 1 0 !important;
  margin-top: 10px;
  margin-bottom: 10px;
  }
  
  ::ng-deep .prc_AgencyAutocomplete .mat-option-text {
    font-size: 14px !important;
  }
  
  // ================================= prc_main
  .prc_main {
    border-radius: 4px;
    padding: 1em;
    box-shadow: 0px 1px 15px rgba(69, 65, 78, 0.19);
    margin-top: 15px;
  }
  
  .radio-margin{
    margin-right: 15px;
  }
  
  .aligncenter{
    display: flex;
    align-items: center;
    padding-left: 5px;
  }
  
  .prc_main .btn_add {
    background-color: #e8e8e8;
    color: #666;
    float: right;
  }
  
  .prc_main .btn_add .mat-icon {
    color: #ce7a58;
  }
  
  // ================================= prc_tbl
  .prc_tbl {
    margin-top: 0em;
  }
  
  .prc_tbl table {
    border-radius: 4px;
    border: 1px solid #ececec;
    width: 100%;
  }
  
  ::ng-deep .prc_tbl .mat-header-row {
    background-color: #e8e8e8;
    min-height: 3.5em !important;
  }
  
  ::ng-deep .prc_tbl .mat-header-row .mat-header-cell {
    color: #495057;
    font-size: 14px;
    // display: grid;
  }
  
  ::ng-deep .prc_tbl .mat-header-row .mat-header-cell p {
    margin-bottom: 0;
    font-weight: 400;
    font-style: italic;
  }
  
  ::ng-deep .prc_tbl .mat-column-stt {
    flex: 0 0 5%;
  }
  
  ::ng-deep .prc_tbl .mat-column-code {
    flex: 0 0 10%;
  }
  
  ::ng-deep .prc_tbl .mat-column-code a{
    text-decoration: none;
    font-weight: 500;
    color: #ce7a58;
  }
  
  ::ng-deep .prc_tbl .mat-column-name {
    flex: 1 0 17%;
    margin-top: 10px;
    margin-bottom: 10px;
  }
  
  ::ng-deep .prc_tbl .mat-cell {
    font-size: 13px;
  }
  
  ::ng-deep .prc_tbl .mat-column-status {
    flex: 0 0 10%;
    float: right;
  }
  
  ::ng-deep .prc_tbl .mat-column-action {
    flex: 0 0 5%;
  }
  
  ::ng-deep .prc_tbl .mat-column-agency {
    flex: 1 0 10%;
    padding-right: .5em;
  }
  
  ::ng-deep .prc_tbl .mat-column-sector {
    padding-right: .5em;
  }
  
  ::ng-deep .prc_tbl .btn_downloadForm {
    padding: 0;
    color: #ce7a58;
  }
  
  ::ng-deep .prc_tbl .btn_downloadForm .mat-button-wrapper {
    display: flex;
  }
  
  ::ng-deep .prc_tbl .btn_downloadForm .mat-button-wrapper .download_icon .mat-icon {
    vertical-align: middle;
    margin-right: 0.2em;
    background-color: #ce7a58;
    color: #fff;
    border-radius: 50%;
    padding: 0.2em;
    transform: scale(0.8);
  }
  
  ::ng-deep .prc_tbl .btn_downloadForm .mat-button-wrapper span {
    align-self: center;
  }
  
  ::ng-deep .prc_tbl.dossier_late .mat-row {
    border: block;
  }
  
  ::ng-deep .prc_tbl .mat-row:nth-child(even) {
    background-color: #FAFAFA;
  }
  
  ::ng-deep .prc_tbl .mat-row:nth-child(odd) {
    background-color: #fff;
  }
  
  ::ng-deep .menuAction {
    font-weight: 500;
  }
  
  ::ng-deep .menuAction .mat-icon {
    color: #ce7a58;
  }
  
  .dossier_late .mat-cell {
    margin: 6px;
  }
  
  .dossier_late .mat-header-cell {
    margin: 6px;
  }
  
  @media screen and (max-width: 600px) {
    .prc_tbl .mat-header-row {
        display: none;
    }
  
    .prc_tbl .mat-table {
        border: 0;
        vertical-align: middle;
    }
  
    .prc_tbl .mat-table caption {
        font-size: 1em;
    }
  
    .prc_tbl .mat-table .mat-row {
        border-bottom: 5px solid #ddd;
        display: block;
        min-height: unset;
    }
  
    .prc_tbl .mat-table .mat-cell {
        border-bottom: 1px solid #ddd;
        display: block;
        font-size: 14px;
        text-align: right;
        margin-bottom: 4%;
        padding: 0 .5em;
    }
  
    .prc_tbl .mat-table .mat-cell:before {
        content: attr(data-label);
        float: left;
        font-weight: 500;
        font-size: 14px;
    }
  
    .prc_tbl .mat-table .mat-cell:last-child {
        border-bottom: 0;
    }
  
    .prc_tbl .mat-table .mat-cell:first-child {
        margin-top: 4%;
    }
  
    ::ng-deep .prc_tbl .mat-column-status {
        float: unset;
    }
  
    ::ng-deep .prc_tbl .mat-column-action {
        float: unset;
    }
  
    ::ng-deep .prc_tbl .mat-row:nth-child(even) {
        background-color: unset;
    }
  
    ::ng-deep .prc_tbl .mat-row:nth-child(odd) {
        background-color: unset;
    }
  }
  
  ::ng-deep .prc_searchbar .searchForm{
    @import "~src/styles/buttons.scss";
  
    .btn-search {
        @extend .t-btn-search;
    }
  
    .btn-download-excel {
        @extend .t-btn-download-excel;
    }
  }
  .status-synced {
    color: rgb(0, 0, 211);
  }
  
  .status-result {
    color: rgb(0, 196, 0);
  }
  
  .status-pending {
    color: rgb(208, 208, 1);
  }
  
  .status-unknown {
    color: rgb(217, 4, 4);
  }
  .table-container {
    overflow-x: auto;
    display: block;
    
    
  }
  .mat-header-cell, .mat-cell {
    min-width: 150px;
    white-space: normal; 
    word-wrap: break-word; 
    word-break: break-word; 
    text-align: left;
  }
  .mat-header-cell.stt, .mat-cell.stt {
    min-width: 60px;
  }
  .status-message {
    border: 1px solid #c00080;
    padding: 8px;
    border-radius: 4px;
    background-color: #f9f9f9;
    color: #333;
  }
  
  
  