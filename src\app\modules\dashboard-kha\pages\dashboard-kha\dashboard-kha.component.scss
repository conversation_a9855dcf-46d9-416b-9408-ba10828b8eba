.mt-2 {
    margin-top: 2%;
}

.mb-1 {
    margin-bottom: 1%;
}

.menu {
    height: 2em;
    padding: 0 4em;
    background-color: #fff;
    border-bottom: 1px solid rgba(0, 0, 0, 0.09);
}

::ng-deep {
    .st_tbl {
        display: grid;

        .printCtrl {
            margin-bottom: 1em;

            .btn_ctrl {
                background-color: #e8e8e8;
                color: #666;
                float: right;
                margin-left: 1em;

                .mat-icon {
                    color: #ce7a58;
                    margin-right: 0.2em;
                }
            }
        }
        .mat-column-agencyName {
            span {
                display: -webkit-box;
                -webkit-line-clamp: 4;
                -webkit-box-orient: vertical;
                width: 100%;
                overflow: hidden;
                text-overflow: ellipsis;
            }
        }
    }

    .mat-tooltip {
        font-size: 13px;
        max-width: 60vw !important;
        overflow-wrap: anywhere;
        opacity: 1;
    }
}

::ng-deep .menu .mat-button {
    border-radius: 0;
}

::ng-deep .menu .mat-button .mat-button-wrapper {
    font-weight: 500 !important;
}

.activeMenuReponsive {
    color: #ce7a58;
    font-weight: 500;
}

.breadcrumb {
    padding: 1em 0;
}

.breadcrumb a {
    text-decoration: none;
    color: #333;
    transition: 0.3s;
}

.breadcrumb a:hover {
    color: #903938;
}

.breadcrumb .mat-icon {
    vertical-align: middle;
    color: #333;
}

::ng-deep .st_searchbar .searchForm .searchBtn {
    .mat-icon {
        margin-right: 0.3em;
        vertical-align: middle;
    }
    margin-top: 0.3em;
    background-color: #ce7a58;
    color: #fff;
    height: 3.2em;
}

::ng-deep .st_searchbar .mat-form-field-appearance-outline .mat-form-field-infix {
    padding: 0.8em 0;
}

::ng-deep .st_searchbar .mat-form-field-label-wrapper {
    top: -1em;
}

::ng-deep .st_tabs.mat-tab-group.mat-primary .mat-ink-bar {
    background-color: #ce7a58;
    height: 3px;
    border-radius: 5em 5em 0 0;
}

::ng-deep .st_tabs .mat-tab-label.mat-tab-label-active {
    color: #ce7a58;
    opacity: 1;
}

.listAgency {
    flex-wrap: wrap;
    margin: 1em 0;
    padding: 0 0.5em;
}

.listAgency .st_item {
    border-radius: 4px;
    box-shadow: 0px 0px 12px rgba(0, 0, 0, 0.18);
    margin: 1%;
}

.listAgency .st_item .head {
    display: flex;
    background-color: #ef480566;
    padding: 0.5em 1em;
    height: 4em;
    border-radius: 4px 4px 0 0;
}

.listAgency .st_item .head .agency_avatar {
    width: 3em;
    height: 3em;
    background-position: center;
    background-repeat: no-repeat;
    background-size: 100%;
    max-width: 30%;
    border-radius: 50%;
}

.listAgency .st_item .head span {
    align-self: center;
    padding-left: 0.5em;
    font-size: 15px;
    font-weight: 700;
    width: 85%;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
}

.listAgency .st_item .body {
    padding: 1em;
    align-content: center;
}

.listAgency .st_item .body .list {
    padding: 0.5em 0 0 0;
    color: #333;
    font-weight: 500;
    margin: 0;
    display: flex;
}



.listAgency .st_item .body {
    .title {
        align-self: center;
        text-align: left;
        color: #333;
    }
    .data {
        align-self: center;
        //margin-left: auto;
        text-align: center;
        color: #282828;
        b{

            font-size: 27px;
            margin-right: 6px;
        }
    }
}
.lableChart{
    display: flex;
    margin-top: 1.5em;
    font-size: 16px;
    font-weight: 500;
}
.lableChart span{
    margin-right: 3px;
}
.st_tbl {
    padding: 1em 0.5em;
}

.st_tbl table {
    width: 100%;
}

::ng-deep .st_tbl .mat-header-row {
    background-color: #8f969c35;
    border-bottom: 2.5px solid #ce7a58;
}

::ng-deep .st_tbl .mat-header-row .mat-header-cell {
    color: #495057;
    font-size: 14px;
    display: grid;
}

::ng-deep .st_tbl .mat-header-row .mat-header-cell p {
    margin-bottom: 0;
    font-weight: 400;
    font-style: italic;
}

::ng-deep .st_tbl .mat-cell:last-of-type,
::ng-deep .st_tbl .mat-header-cell:last-of-type,
::ng-deep .st_tbl .mat-footer-cell:last-of-type {
    padding-right: unset;
}

@media screen and (max-width: 600px) {
    .st_tbl .mat-header-row {
        display: none;
    }

    .st_tbl .mat-table {
        border: 0;
        vertical-align: middle;
    }

    .st_tbl .mat-table caption {
        font-size: 1em;
    }

    .st_tbl .mat-table .mat-row {
        border-bottom: 5px solid #ddd;
        display: block;
        min-height: unset;
    }

    .st_tbl .mat-table .mat-cell {
        border-bottom: 1px solid #ddd;
        display: block;
        font-size: 14px;
        text-align: right;
        height: 30px;
        margin-bottom: 4%;
        padding: 0;
    }

    .st_tbl .mat-table .mat-cell:before {
        content: attr(data-label);
        float: left;
        font-weight: 500;
        font-size: 14px;
    }

    .st_tbl .mat-table .mat-cell:last-child {
        border-bottom: 0;
    }

    .st_tbl .mat-table .mat-cell:first-child {
        margin-top: 4%;
    }
}

// ====================================================== Pagination
.stPagination {
    padding: 1em 0;
    display: flex;
}

::ng-deep .stPagination .pageSize .mat-form-field-appearance-outline .mat-form-field-wrapper {
    width: 4.5em;
    margin: 0 0.5em;
}

::ng-deep .stPagination .pageSize .mat-form-field-appearance-outline .mat-form-field-infix {
    padding: 0.5em 0;
}

::ng-deep .stPagination .pageSize .mat-form-field-appearance-outline .mat-select-arrow-wrapper {
    padding-top: 0.5em;
}

::ng-deep .stPagination .pageSize .mat-form-field-appearance-outline .mat-form-field-flex {
    padding: 0 0.5em 0.5em 0.5em;
}

.stPagination .control {
    text-align: right;
    margin-left: auto;
}

::ng-deep .stPagination .control .ngx-pagination a {
    outline: unset;
}

::ng-deep .stPagination .control .ngx-pagination .current {
    background: #ce7a58;
}

::ng-deep .stPagination .control .ngx-pagination .pagination-previous a::before,
::ng-deep .stPagination .control .ngx-pagination .pagination-previous.disabled::before {
    font-family: "Material Icons";
    content: "chevron_left";
    vertical-align: middle;
    transform: scale(1.5);
    margin-right: 0;
}

::ng-deep .stPagination .control .ngx-pagination .pagination-next a::after,
::ng-deep .stPagination .control .ngx-pagination .pagination-next.disabled::after {
    font-family: "Material Icons";
    content: "chevron_right";
    vertical-align: middle;
    transform: scale(1.5);
    margin-left: 0;
}
.bodybox{
    align-self: center;
    //margin-left: auto;
    text-align: center;
    color: #913938;
}

.frm_tbl_sector table {
  border-radius: 4px;
  border: 1px solid #ececec;
  width: 100%;
}

::ng-deep .frm_tbl_sector .mat-header-row {
  background-color: #e8e8e8;
}

::ng-deep .frm_tbl_sector .mat-header-row .mat-header-cell {
  color: #495057;
  font-size: 14px;
  // display: grid;
}
::ng-deep .table-scroll-remind .mat-tab-body-content {
  padding-top: 15px !important;
  height: calc(100vh - 122px);
  tbody a:hover{
    color: #CE7A58;
    cursor: pointer;
  }
  //overflow: auto;
}
::ng-deep .frm_tbl_sector .mat-header-row .mat-header-cell p {
  margin-bottom: 0;
  font-weight: 400;
  font-style: italic;
}

::ng-deep .frm_tbl_sector .mat-column-stt {
  flex: 0 0 5%;
}

::ng-deep .frm_tbl_sector .mat-column-code {
  flex: 0 0 25%;
}

::ng-deep {
  .mat-tooltip {
    font-size: 13px;
    max-width: unset !important;
    overflow-wrap: anywhere;
  }
}

::ng-deep .frm_tbl_sector .mat-column-name {
  flex: 1 0 5%;
  // text-decoration: none;
  // color: #333;
  // display: -webkit-box;
  // -webkit-line-clamp: 4;
  // -webkit-box-orient: vertical;
  // width: 100%;
  // overflow: hidden;
  // text-overflow: ellipsis;
  // overflow-wrap: anywhere;
}

::ng-deep .frm_tbl_sector .mat-column-status {
  flex: 0 0 10%;
  float: right;
}

::ng-deep .frm_tbl_sector .mat-column-action {
  flex: 0 0 10%;
  float: right;
}

::ng-deep .frm_tbl_sector .btn_downloadForm {
  padding: 0;
  color: #ce7a58;
}

::ng-deep .frm_tbl_sector .cell_code {
  color: #ce7a58;
}

::ng-deep .frm_tbl_sector .btn_downloadForm .mat-button-wrapper {
  display: flex;
}

::ng-deep .frm_tbl_sector .btn_downloadForm .mat-button-wrapper .download_icon .mat-icon {
  vertical-align: middle;
  margin-right: 0.2em;
  background-color: #ce7a58;
  color: #fff;
  border-radius: 50%;
  padding: 0.2em;
  transform: scale(0.8);
}

::ng-deep .frm_tbl_sector .btn_downloadForm .mat-button-wrapper span {
  align-self: center;
}

::ng-deep .frm_tbl_sector .mat-row {
  border: none;
}

::ng-deep .frm_tbl_sector .mat-row:nth-child(even) {
  background-color: #fafafa;
}

::ng-deep .frm_tbl_sector .mat-row:nth-child(odd) {
  background-color: #fff;
}

::ng-deep .menuAction {
  font-weight: 500;
}

::ng-deep .menuAction .mat-icon {
  color: #ce7a58;
}

@media screen and (max-width: 600px) {
  .frm_tbl_sector .mat-header-row {
    display: none;
  }

  .frm_tbl_sector .mat-table {
    border: 0;
    vertical-align: middle;
  }

  .frm_tbl_sector .mat-table caption {
    font-size: 1em;
  }

  .frm_tbl_sector .mat-table .mat-row {
    border-bottom: 5px solid #ddd;
    display: block;
    min-height: unset;
  }

  .frm_tbl_sector .mat-table .mat-cell {
    border-bottom: 1px solid #ddd;
    display: block;
    font-size: 14px;
    text-align: right;
    margin-bottom: 4%;
    padding: 0 0.5em;
  }

  .frm_tbl_sector .mat-table .mat-cell:before {
    content: attr(data-label);
    float: left;
    font-weight: 500;
    font-size: 14px;
  }

  .frm_tbl_sector .mat-table .mat-cell:last-child {
    border-bottom: 0;
  }

  .frm_tbl_sector .mat-table .mat-cell:first-child {
    margin-top: 4%;
  }

  ::ng-deep .frm_tbl_sector .mat-column-status {
    float: unset;
  }

  ::ng-deep .frm_tbl_sector .mat-column-action {
    float: unset;
  }


  ::ng-deep .frm_tbl_sector .mat-row:nth-child(even) {
    background-color: unset;
  }

  ::ng-deep .frm_tbl_sector .mat-row:nth-child(odd) {
    background-color: unset;
  }
}

.mat-tab .label {
  color: red;
}
