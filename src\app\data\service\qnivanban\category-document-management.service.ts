import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { ApiProviderService } from 'src/app/core/service/api-provider.service';


@Injectable({
    providedIn: 'root'
})
export class CategoryDocumentManagementService {
    constructor(
        private http: HttpClient,
        private apiProviderService: ApiProviderService,
    ) { }

    private categoryManagementPath = this.apiProviderService.getUrl('digo', 'basepad') + '/category-document/';

    getListCategory(searchString): Observable<any> {
        let headers = new HttpHeaders();
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        return this.http.get(this.categoryManagementPath + searchString, { headers });
    }

    deleteCategory(id): Observable<any> {
        let headers = new HttpHeaders();
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        return this.http.delete(this.categoryManagementPath + `${id}`, { headers });
    }

    addOrUpdateCategory(id, requestBody): Observable<any> {
        let headers = new HttpHeaders();
        headers = headers.set('Content-Type', 'application/json');
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        if(id == '0'){
            return this.http.post<any>(this.categoryManagementPath, requestBody, { headers });
        }
        return this.http.put<any>(this.categoryManagementPath + `${id}`, requestBody, { headers });
    }

    getCategoryById(id): Observable<any> {
        let headers = new HttpHeaders();
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        return this.http.get(this.categoryManagementPath + `${id}`, { headers });
    }

    getListCategoryIsShow(): Observable<any> {
        let headers = new HttpHeaders();
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        return this.http.get(this.categoryManagementPath + 'get-all-is-show', { headers });
    }
}
