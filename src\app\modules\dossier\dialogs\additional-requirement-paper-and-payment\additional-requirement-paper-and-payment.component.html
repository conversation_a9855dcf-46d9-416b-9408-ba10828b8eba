<button mat-icon-button class="close-button" (click)="onDismiss()">
    <mat-icon>close</mat-icon>
</button>
<h3 class="dialog_title" mat-dialog-title><PERSON><PERSON><PERSON> c<PERSON><PERSON> nộ<PERSON> hồ sơ giấy và thanh toán</h3>
<div mat-dialog-content class="dialog_content">
    <span>Bạn có chắc chắn muốn gửi yêu cầu nộp hồ sơ giấy và thanh toán </span><span
        class="highlight">{{dossierCode}}</span><span>?</span>
    <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="column" *ngIf="showCheckConfirmPayment && showConfirmPayment" style="padding: 10px 0;font-weight: 500;">
        <mat-checkbox [(ngModel)]="checkConfirmPayment"><span i18n="@@ConfirmPaymentLabel">X<PERSON><PERSON> nh<PERSON><PERSON> số tiền yê<PERSON> cầu thanh toán</span>{{totalCost ? ':' : ''}} <span class="highlight">{{totalCost}}</span>
        </mat-checkbox>
    </div>
    <form [formGroup]="updateForm" class="updateForm">
        <div fxLayout="column" fxLayout.xs="column" fxLayout.sm="column" class="formFieldOutline" fxLayoutAlign="space-between" *ngIf="this.dossierDetail?.dossierStatus?.id !== 0">
            <mat-form-field appearance="outline" fxFlex.gt-md="49.5" fxFlex.gt-sm="49.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                <mat-label>Số ngày tạm dừng thanh toán</mat-label>
                <input type="text" type="number" matInput formControlName="numberPausePaidDay" required oninput="this.value = !!this.value && Math.abs(this.value) > 0 ? Math.abs(this.value) : null">
            </mat-form-field>
        </div>
    </form>
    <div class="commentEditor" fxLayout="column" fxLayout.xs="column" fxLayout.sm="column">
        <p class="lbl"><span i18n>Lý do</span><span style="color:#ce7a58;">&nbsp;*</span></p>
        <ckeditor debounce="10" [editor]="Editor" class="customCKEditor" (change)="onCommentEditorChange($event)"
            fxFlex='grow' [config]='commentConfig'>
        </ckeditor>
        <div class="errorMsg" *ngIf="isCKMaxlenght">
            <span i18n>Nội dung không quá 500 ký tự</span>
            <div class="err">
                <mat-icon>priority_high</mat-icon>
            </div>
        </div>
    </div>
    
</div>

<digo-check-send-notify functionType="additionalRequirementPaperCopyAndPaymentRequest" functionTypeOfficer="" [receiveType]="env.enableApprovalOfLeadership"></digo-check-send-notify>

<div fxLayout="row" fxLayout.xs="column" fxLayout.sm="column" fxLayoutAlign="center">
    <button mat-flat-button fxFlex='30' class="applyBtn" (click)="onConfirm()">
        <span i18n>Đồng ý</span>
    </button>
</div>