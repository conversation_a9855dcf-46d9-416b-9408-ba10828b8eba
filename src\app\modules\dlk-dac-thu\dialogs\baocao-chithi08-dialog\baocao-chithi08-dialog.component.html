<!-- <p>baocao-chithi08-dialog works!</p> -->
<!-- <p>{{ data | json }}</p> -->
<div class="body">
    <h3 *ngIf="!data.type"
        class="dialog_title"
        mat-dialog-title
        i18n><PERSON><PERSON> sách hồ sơ</h3>
    <h3 *ngIf="data.type"
        class="dialog_title"
        mat-dialog-title><PERSON>h sách thủ tục</h3>
    <div class="frm_tbl0" *ngIf="data.type == 0">
    <!-- <div class="frm_tbl0"> -->
        <table mat-table
               [dataSource]="dataSource">
            <ng-container matColumnDef="no">
                <th mat-header-cell
                    *matHeaderCellDef>STT</th>
                <td mat-cell
                    *matCellDef="let row; let i = index"> {{size * (pageIndex - 1) + (i + 1)}} </td>
            </ng-container>
            <ng-container matColumnDef="dossierCode">
                <th mat-header-cell
                    *matHeaderCellDef style="text-align: left"> <PERSON><PERSON> hồ sơ </th>
                <td mat-cell
                    *matCellDef="let row"  style="text-align: left">
                    <a> {{row.dossierCode}} </a>
                </td>
            </ng-container>
            <ng-container matColumnDef="procedureName">
                <th mat-header-cell
                    *matHeaderCellDef
                    i18n  style="text-align: left">Tên thủ tục hành chính</th>
                <td mat-cell
                    *matCellDef="let row" style="text-align: left"> {{row.procedureName}} </td>
            </ng-container>
            <ng-container matColumnDef="procedureLevel">
                <th mat-header-cell
                    *matHeaderCellDef
                    i18n>Mức độ</th>
                <td mat-cell
                    *matCellDef="let row"> {{row.procedureLevel}} </td>
            </ng-container>
            <ng-container matColumnDef="applicantOwnerFullName">
                <th mat-header-cell
                    *matHeaderCellDef>Tổ chức, cá nhân</th>
                <td mat-cell
                    *matCellDef="let row"> {{getFullName(row)}} </td>
            </ng-container>
            <ng-container matColumnDef="applicantPhoneNumber">
                <th mat-header-cell
                    *matHeaderCellDef>Địa chỉ, SĐT</th>
                <td mat-cell
                    *matCellDef="let row"> {{row.PhoneNumber}}, {{row.Address}} </td>
            </ng-container>
            <ng-container matColumnDef="agencyName">
                <th mat-header-cell
                    *matHeaderCellDef>Cơ quan chủ trì</th>
                <td mat-cell
                    *matCellDef="let row"> {{data.agencyName}} </td>
            </ng-container>
            <ng-container matColumnDef="agencyAccepterName">
                <th mat-header-cell *matHeaderCellDef>Cơ quan tiếp nhận</th>
                <td mat-cell *matCellDef="let row"> {{row.agencyName}} </td>
            </ng-container>
            <ng-container matColumnDef="acceptedDate">
                <th mat-header-cell
                    *matHeaderCellDef
                    i18n>Ngày tiếp nhận</th>
                <td mat-cell
                    *matCellDef="let row"> {{row.acceptedDate | date: 'dd/MM/yyyy HH:mm:ss'}} </td>
            </ng-container>
            <ng-container matColumnDef="appointmentDate">
                <th mat-header-cell
                    *matHeaderCellDef
                    i18n>Ngày hẹn trả</th>
                <td mat-cell
                    *matCellDef="let row"> {{row.appointmentDate| date: 'dd/MM/yyyy HH:mm:ss'}} </td>
            </ng-container>
            <ng-container matColumnDef="completedDate">
                <th mat-header-cell
                    *matHeaderCellDef>Ngày có kết quả/yc trả lại dân</th>
                <td mat-cell
                    *matCellDef="let row"> {{getCompletedDate(row)}}</td>
                    <!-- *matCellDef="let row"> {{row.completedDate| date: 'dd/MM/yyyy HH:mm:ss'}}</td> -->
            </ng-container>
            <ng-container matColumnDef="returnedDate">
                <th mat-header-cell
                    *matHeaderCellDef>Ngày trả kết quả/trả lại dân</th>
                <td mat-cell
                    *matCellDef="let row"> {{row.returnedDate| date: 'dd/MM/yyyy HH:mm:ss'}} </td>
            </ng-container>

            <ng-container matColumnDef="ReceivingKind">
                <th mat-header-cell
                    *matHeaderCellDef>Hình thức nhận KQ</th>
                <td mat-cell
                    *matCellDef="let row"> {{row.ReceivingKind}} </td>
            </ng-container>
            <ng-container matColumnDef="applyMethod">
                <th mat-header-cell
                    *matHeaderCellDef>Hình thức tiếp nhận</th>
                <td mat-cell
                    *matCellDef="let row"> {{row.applyMethod}} </td>
            </ng-container>

            <tr mat-header-row
                *matHeaderRowDef="displayedColumns; sticky: true"></tr>
            <tr mat-row
                *matRowDef="let row; columns: displayedColumns;"></tr>
        </table>
        <div *ngIf="isWaitingData"
             class="tableSpinnerContainer">
            <mat-spinner diameter="60">
            </mat-spinner>
        </div>
    </div>

    <!-- Chi tiet thu tuc -->
    <div class="frm_tbl0" *ngIf="data.type == 1">
        <table mat-table [dataSource]="procedureDataSource" style="width: 100%;">
            <ng-container matColumnDef="no">
                <th mat-header-cell *matHeaderCellDef>STT</th>
                <td mat-cell *matCellDef="let row; let i = index"> {{size * (pageIndex - 1) + (i + 1)}} </td>
            </ng-container>
            <ng-container matColumnDef="code">
                <th mat-header-cell *matHeaderCellDef style="text-align: left">Tên tắt</th>
                <td mat-cell *matCellDef="let row" style="text-align: left">
                    <a>{{row.code}}</a>
                </td>
            </ng-container>
            <ng-container matColumnDef="procedureName">
                <th mat-header-cell *matHeaderCellDef style="text-align: left"> Thủ tục </th>
                <td mat-cell *matCellDef="let row" style="text-align: left"> {{row.procedureName}} </td>
            </ng-container>
            <ng-container matColumnDef="sectorName">
                <th mat-header-cell *matHeaderCellDef>Lĩnh vực thủ tục</th>
                <td mat-cell *matCellDef="let row"> {{row.sectorName}} </td>
            </ng-container>
            <ng-container matColumnDef="agencyName">
                <th mat-header-cell *matHeaderCellDef>Cơ quan</th>
                <td mat-cell *matCellDef="let row"> {{getAgencyName(row.agencyName)}} </td>
            </ng-container>
            <ng-container matColumnDef="cost">
                <th mat-header-cell *matHeaderCellDef>Lệ phí</th>
                <td mat-cell *matCellDef="let row"> {{row.cost}} </td>
            </ng-container>
            <ng-container matColumnDef="legalGrounds">
                <th mat-header-cell *matHeaderCellDef>Quyết định</th>
                <td mat-cell *matCellDef="let row" [innerHtml]="row.legalGrounds"></td>
            </ng-container>
            <ng-container matColumnDef="agencyLevel">
                <th mat-header-cell *matHeaderCellDef>Cấp thủ tục</th>
                <td mat-cell *matCellDef="let row"> {{getAgencyName(row.agencyLevel)}} </td>
            </ng-container>
            <ng-container matColumnDef="levelName">
                <th mat-header-cell *matHeaderCellDef>Mức độ</th>
                <td mat-cell *matCellDef="let row"> {{row.levelName}} </td>
            </ng-container>

            <tr mat-header-row *matHeaderRowDef="displayedColumns; sticky: true"></tr>
            <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
        </table>
        <div *ngIf="isWaitingData" class="tableSpinnerContainer">
            <mat-spinner diameter="60">
            </mat-spinner>
        </div>
    </div>
    <!--------------------------------------------->
    <div class="pagination">
        <ul class="temp-arr">
            <li *ngFor="let item of ELEMENTDATA  | paginate: {itemsPerPage: size, currentPage: page, totalItems: countResult, id: 'pgnx'}">
            </li>
        </ul>
        <div class="page-size">
            <span i18n>Hiển thị </span>
            <mat-form-field appearance="outline">
                <mat-select [(value)]="size" (valueChange)="paginate(pageIndex, 1)">
                    <mat-option *ngFor='let opt of pgSizeOptions;' [value]="opt">{{opt}}</mat-option>
                </mat-select>
            </mat-form-field>
            <span><span i18n>trên</span> {{countResult}} <span i18n>bản ghi</span></span>
        </div>
        <div class="control">
            <pagination-controls id="pgnx" (pageChange)="page = $event; paginate(page, 0)" responsive="true" previousLabel="" nextLabel="">
            </pagination-controls>
        </div>
    </div>

    <div fxLayout="row"
         fxLayout.xs="column"
         fxLayout.sm="column"
         fxLayoutAlign="center">
        <div class="exportExcelBtnContainer">
            <button mat-flat-button
                    (click)="exportExcel()"
                    class="exportExcelBtn">
                <span>Xuất excel</span>
            </button>
        </div>
        <button mat-flat-button
                class="closeBtn"
                (click)="onClose()">
            <span i18n>Đóng</span>
        </button>
    </div>
</div>