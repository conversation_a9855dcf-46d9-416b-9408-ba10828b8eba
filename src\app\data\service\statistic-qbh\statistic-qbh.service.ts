import {Injectable} from '@angular/core';
import {HttpClient, HttpHeaders} from '@angular/common/http';
import {ApiProviderService} from 'src/app/core/service/api-provider.service';
import {EnvService} from 'src/app/core/service/env.service';
import {Workbook} from 'exceljs';
import * as fs from 'file-saver';
import { Observable } from 'rxjs';

const EXCEL_TYPE = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8';
const EXCEL_EXTENSION = '.xlsx';

@Injectable({
  providedIn: 'root'
})
export class StatisticQbhService {
  config = this.envService.getConfig();
  language = localStorage.getItem('language') === 'vi' ? 228 : 46;
  private sysman = this.apiProviderService.getUrl('digo', 'sysman');
  private padman = this.apiProviderService.getUrl('digo', 'padman');

  constructor(
    private http: HttpClient,
    private apiProviderService: ApiProviderService,
    private envService: EnvService,
  ) { }
  public exportToExcelStatisticDvcOnline(
    nationName: string,
    subNationName: string,
    reportHeading: string,
    subHeading: string,
    headerArray: any[],
    excelData: any[],
    excelFileName: string,
    sheetName: string,
    agencyName: string,
    statisticDate: string,
    isVisibleCancelled: boolean,
    footerData: any[]
  ) {
    // create workbook and worksheet
    const workbook = new Workbook();
    workbook.creator = 'Snippet Coder';
    workbook.lastModifiedBy = 'SnippetCoder';
    workbook.created = new Date();
    workbook.modified = new Date();
    const worksheet = workbook.addWorksheet(sheetName);

    worksheet.getColumn('A').font = {name: 'Times New Roman', size: 12};
    worksheet.getColumn('B').font = {name: 'Times New Roman', size: 12};
    worksheet.getColumn('C').font = {name: 'Times New Roman', size: 12};
    worksheet.getColumn('D').font = {name: 'Times New Roman', size: 12};
    worksheet.getColumn('E').font = {name: 'Times New Roman', size: 12};
    worksheet.getColumn('F').font = {name: 'Times New Roman', size: 12};
    worksheet.getColumn('G').font = {name: 'Times New Roman', size: 12};
    worksheet.getColumn('H').font = {name: 'Times New Roman', size: 12};
    worksheet.getColumn('I').font = {name: 'Times New Roman', size: 12};
    worksheet.getColumn('J').font = {name: 'Times New Roman', size: 12};

    worksheet.getColumn('A').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getColumn('B').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getColumn('C').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getColumn('D').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getColumn('E').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getColumn('F').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getColumn('G').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getColumn('H').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getColumn('I').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getColumn('J').alignment = { horizontal: 'center', vertical: 'middle' };

    // worksheet.getColumn('A').width = 30;
    worksheet.getColumn('B').width = 40;
    worksheet.getColumn('C').width = 20;
    worksheet.getColumn('D').width = 20;
    worksheet.getColumn('E').width = 20;
    worksheet.getColumn('F').width = 20;
    worksheet.getColumn('G').width = 20;
    worksheet.getColumn('H').width = 20;
    worksheet.getColumn('I').width = 20;
    worksheet.getColumn('J').width = 20;

    worksheet.properties.outlineLevelCol = 2;
    worksheet.properties.defaultRowHeight = 15;


    // Add header row
    worksheet.mergeCells('A1:C1');
    worksheet.getCell('A1').value = agencyName;
    worksheet.getCell('A1').style = {font: {name: 'Times New Roman'}, alignment: {horizontal: 'center', vertical: 'middle'}};

    worksheet.addRow([]);

    worksheet.mergeCells('E1:I1');
    worksheet.getCell('E1').value = nationName;
    worksheet.getCell('E1').style = {font: {bold: true, name: 'Times New Roman'}, alignment: {horizontal: 'center', vertical: 'middle'}};

    worksheet.mergeCells('E2:I2');
    worksheet.getCell('E2').value = subNationName;
    worksheet.getCell('E2').style = {font: {bold: true, name: 'Times New Roman'}, alignment: {horizontal: 'center', vertical: 'middle'}};

    worksheet.mergeCells('E4:I4');
    worksheet.getCell('E4').value = reportHeading;
    worksheet.getCell('E4').style = {
      font: {bold: true, name: 'Times New Roman', size: 16},
      alignment: {horizontal: 'center', vertical: 'middle'}
    };

    worksheet.mergeCells('E5:I5');
    worksheet.getCell('E5').value = subHeading;
    worksheet.getCell('E5').style = {font: {size: 12, name: 'Times New Roman'}, alignment: {horizontal: 'center', vertical: 'middle'}};



    if (isVisibleCancelled) {
      worksheet.mergeCells('M6:O6');
      worksheet.getCell('M6').value = statisticDate;
      worksheet.getCell('M6').alignment = {horizontal: 'right', vertical: 'middle'};
    } else {
      worksheet.mergeCells('K6:M6');
      worksheet.getCell('K6').value = statisticDate;
      worksheet.getCell('K6').alignment = {horizontal: 'right', vertical: 'middle'};
    }

    
    worksheet.addRow([]);
    const headerRow = worksheet.addRow(headerArray);
    worksheet.getRow(8).height = 30;

    headerRow.eachCell((cell, index) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: {argb: 'C0C0C0C0'},
        bgColor: {argb: 'FF0000FF'}
      };
      cell.border = {top: {style: 'thin'}, left: {style: 'thin'}, bottom: {style: 'thin'}, right: {style: 'thin'}};
      cell.font = {bold: true, size: 11, name: 'Times New Roman'};
      cell.alignment = {wrapText: true, horizontal: 'center', vertical: 'middle'};
      worksheet.getColumn(index).width = headerArray[index - 1].length < 20 ? 20 : headerArray[index - 1].length;
    });

    worksheet.mergeCells('A8:H8');
    worksheet.getCell('A8').value = 'DANH SÁCH CÁC DỊCH VỤ CÔNG TRỰC TUYẾN';
    worksheet.getCell('A8').style = {font: {name: 'Times New Roman'}, alignment: {horizontal: 'center', vertical: 'middle'}}; 
    worksheet.mergeCells('A9:A10');
    worksheet.getCell('A9').value = 'STT';

    worksheet.mergeCells('B9:B10');
    worksheet.getCell('B9').value = 'Cơ quan';

    worksheet.mergeCells('C9:C10');
    worksheet.getCell('C9').value = 'Nhóm dịch vụ';

    worksheet.mergeCells('D9:D10');
    worksheet.getCell('D9').value = 'Tên dịch vụ';

    worksheet.mergeCells('E9:E10');
    worksheet.getCell('E9').value = 'Địa chỉ đăng tải dịch vụ';

    worksheet.mergeCells('F9:H9');
    worksheet.getCell('F9').value = 'Hiệu quả sử dụng';

    worksheet.getCell('F10').value = 'Số lượng hồ sơ trực tuyến đã tiếp nhận trong kỳ';
    worksheet.getCell('G10').value = 'Tổng số lượng hồ sơ cả hình thức TT và không TT trong kỳ';
    worksheet.getCell('H10').value = 'Tỷ lệ hồ sơ trực tuyến của DVCTT Toàn trình và DVC một phần (%)';

    let i = 8;
        const j = 10;
        for (i; i <= j; i++) {
            let k = 1;
            const l = 8;
            for (k; k <= l; k++) {
                worksheet.findCell(i, k).alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
                worksheet.findCell(i, k).fill = {
                    type: 'pattern',
                    pattern: 'solid',
                    fgColor: { argb: 'C0C0C0C0' },
                    bgColor: { argb: 'FF0000FF' }
                };
                worksheet.findCell(i, k).border = { top: { style: 'thin' }, left: { style: 'thin' }, bottom: { style: 'thin' }, right: { style: 'thin' } };
                worksheet.findCell(i, k).font = { size: 12, bold: true, name: 'Times New Roman' };
            }
        }

    // get all columns from JSON
    let columnsArray: any[];
    for (const key in excelData) {
      if (excelData.hasOwnProperty(key)) {
        columnsArray = Object.keys(excelData[key]);
      }
    }
    // Add Data and Conditional Formatting
    excelData.forEach((element: any, i) => {
      const eachRow = [];
      columnsArray.forEach((column, index) => {
        
        eachRow.push(element[column]);
      });
   
      const borderrow = worksheet.addRow(eachRow);
      const cellMerge = 'B' + borderrow.number + ':E' + borderrow.number;
      worksheet.mergeCells(cellMerge);
      borderrow.eachCell((cell, index) => {
        cell.border = {top: {style: 'thin'}, left: {style: 'thin'}, bottom: {style: 'thin'}, right: {style: 'thin'}};
        cell.font = {size: 12, name: 'Times New Roman'};
   
        if(cell.address.startsWith("B")||cell.address.startsWith("C")
         ||cell.address.startsWith("D")||cell.address.startsWith("E")){
          cell.alignment = {wrapText: true, horizontal: 'left', vertical: 'middle'};
        }else{
        cell.alignment = {wrapText: true, horizontal: 'center', vertical: 'middle'};
        }
      });
    });

    worksheet.getColumn('A').width = 10;
    worksheet.getColumn('B').width = 30;
    worksheet.getColumn('C').width = 20;
    worksheet.getColumn('D').width = 20;
    worksheet.getColumn('E').width = 20;
    worksheet.getColumn('F').width = 20;
    worksheet.getColumn('G').width = 20;
    worksheet.getColumn('H').width = 20;
    worksheet.getColumn('I').width = 20;
    worksheet.getColumn('J').width = 20;
    worksheet.getColumn('K').width = 20;
    worksheet.getColumn('L').width = 20;
    worksheet.getColumn('M').width = 20;
    worksheet.getColumn('N').width = 20;
    worksheet.getColumn('O').width = 20;

    // footer data row
    if (footerData != null) {
      footerData.forEach((element: any) => {
        const eachRow = [];
        element.forEach((val: any) => {
          eachRow.push(val);
        });
        const footerRow = worksheet.addRow(eachRow);
        const cellMerge = 'B' + footerRow.number + ':E' + footerRow.number;
        worksheet.mergeCells(cellMerge);
        footerRow.eachCell((cell) => {
          cell.font = {size: 13, bold: true, name: 'Times New Roman'};
          cell.border = {top: {style: 'thin'}, left: {style: 'thin'}, bottom: {style: 'thin'}, right: {style: 'thin'}};
          cell.alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
        });
      });
    }

    // Save Excel File
    workbook.xlsx.writeBuffer().then((data: ArrayBuffer) => {
      const blob = new Blob([data], {type: EXCEL_TYPE});
      fs.saveAs(blob, excelFileName + EXCEL_EXTENSION);
    });
  }
  getDossierStatistic012020Public(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    if (localStorage.getItem('isLoggedIn') == 'false') {
      const token = localStorage.getItem('OAuth2TOKEN');
      headers = headers.append('Authorization', 'Bearer ' + token);
    }
    return this.http.get(this.padman + '/qbh-statistic-dvc/--01-2020-public' + searchString, { headers }).pipe();
  }
  getStatisticData(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    switch (localStorage.getItem('isLoggedIn')) {
      case 'false':
        const token = localStorage.getItem('OAuth2TOKEN');
        headers = headers.append('Authorization', 'Bearer ' + token);
    }
    return this.http.get(this.padman + '/qbh-statistic-dvc/--public' + searchString, { headers }).pipe();
  }
  getDossierOnlinePublic(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    if (localStorage.getItem('isLoggedIn') == 'false') {
      const token = localStorage.getItem('OAuth2TOKEN');
      headers = headers.append('Authorization', 'Bearer ' + token);
    }
    return this.http.get(this.padman + '/qbh-statistic-dvc/--online-public' + searchString, { headers }).pipe();
  }
  getDossierDitialPublic(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    if (localStorage.getItem('isLoggedIn') == 'false') {
      const token = localStorage.getItem('OAuth2TOKEN');
      headers = headers.append('Authorization', 'Bearer ' + token);
    }
    return this.http.get(this.padman + '/qbh-statistic-dvc/--digital-qbh' + searchString, { headers }).pipe();
  }
  getDossierPaymentOnline(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    if (localStorage.getItem('isLoggedIn') == 'false') {
      const token = localStorage.getItem('OAuth2TOKEN');
      headers = headers.append('Authorization', 'Bearer ' + token);
    }
    return this.http.get(this.padman + '/qbh-statistic-dvc/--payment-online' + searchString, { headers }).pipe();
  }
  getDossierOverdueReport(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    if (localStorage.getItem('isLoggedIn') == 'false') {
      const token = localStorage.getItem('OAuth2TOKEN');
      headers = headers.append('Authorization', 'Bearer ' + token);
    }
    return this.http.get(this.padman + '/qbh-statistic-dvc/--overdue-report' + searchString, { headers }).pipe(); 
  }

  public exportToExcelStatisticDvcDigital(
    nationName: string,
    subNationName: string,
    reportHeading: string,
    subHeading: string,
    headerArray: any[],
    excelData: any[],
    excelFileName: string,
    sheetName: string,
    agencyName: string,
    statisticDate: string,
    isVisibleCancelled: boolean,
    excelColumnName1: string,
    footerData: any[]
  ) {
    // create workbook and worksheet
    const workbook = new Workbook();
    workbook.creator = 'Snippet Coder';
    workbook.lastModifiedBy = 'SnippetCoder';
    workbook.created = new Date();
    workbook.modified = new Date();
    const worksheet = workbook.addWorksheet(sheetName);

    worksheet.getColumn('A').font = {name: 'Times New Roman', size: 12};
    worksheet.getColumn('B').font = {name: 'Times New Roman', size: 12};
    worksheet.getColumn('C').font = {name: 'Times New Roman', size: 12};
    worksheet.getColumn('D').font = {name: 'Times New Roman', size: 12};
    worksheet.getColumn('E').font = {name: 'Times New Roman', size: 12};
    worksheet.getColumn('F').font = {name: 'Times New Roman', size: 12};
    worksheet.getColumn('G').font = {name: 'Times New Roman', size: 12};
    worksheet.getColumn('H').font = {name: 'Times New Roman', size: 12};
    worksheet.getColumn('I').font = {name: 'Times New Roman', size: 12};
    worksheet.getColumn('J').font = {name: 'Times New Roman', size: 12};

    worksheet.getColumn('A').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getColumn('B').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getColumn('C').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getColumn('D').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getColumn('E').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getColumn('F').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getColumn('G').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getColumn('H').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getColumn('I').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getColumn('J').alignment = { horizontal: 'center', vertical: 'middle' };

    // worksheet.getColumn('A').width = 30;
    worksheet.getColumn('B').width = 40;
    worksheet.getColumn('C').width = 20;
    worksheet.getColumn('D').width = 20;
    worksheet.getColumn('E').width = 20;
    worksheet.getColumn('F').width = 20;
    worksheet.getColumn('G').width = 20;
    worksheet.getColumn('H').width = 20;
    worksheet.getColumn('I').width = 20;
    worksheet.getColumn('J').width = 20;

    worksheet.properties.outlineLevelCol = 2;
    worksheet.properties.defaultRowHeight = 15;


    // Add header row
    worksheet.mergeCells('A1:C1');
    worksheet.getCell('A1').value = agencyName;
    worksheet.getCell('A1').style = {font: {name: 'Times New Roman'}, alignment: {horizontal: 'center', vertical: 'middle'}};

    worksheet.addRow([]);

    worksheet.mergeCells('E1:I1');
    worksheet.getCell('E1').value = nationName;
    worksheet.getCell('E1').style = {font: {bold: true, name: 'Times New Roman'}, alignment: {horizontal: 'center', vertical: 'middle'}};

    worksheet.mergeCells('E2:I2');
    worksheet.getCell('E2').value = subNationName;
    worksheet.getCell('E2').style = {font: {bold: true, name: 'Times New Roman'}, alignment: {horizontal: 'center', vertical: 'middle'}};

    worksheet.mergeCells('E4:I4');
    worksheet.getCell('E4').value = reportHeading;
    worksheet.getCell('E4').style = {
      font: {bold: true, name: 'Times New Roman', size: 16},
      alignment: {horizontal: 'center', vertical: 'middle'}
    };

    worksheet.mergeCells('E5:I5');
    worksheet.getCell('E5').value = subHeading;
    worksheet.getCell('E5').style = {font: {size: 12, name: 'Times New Roman'}, alignment: {horizontal: 'center', vertical: 'middle'}};



    if (isVisibleCancelled) {
      worksheet.mergeCells('M6:O6');
      worksheet.getCell('M6').value = statisticDate;
      worksheet.getCell('M6').alignment = {horizontal: 'right', vertical: 'middle'};
    } else {
      worksheet.mergeCells('K6:M6');
      worksheet.getCell('K6').value = statisticDate;
      worksheet.getCell('K6').alignment = {horizontal: 'right', vertical: 'middle'};
    }

    
    worksheet.addRow([]);
    const headerRow = worksheet.addRow(headerArray);
    worksheet.getRow(8).height = 30;

    headerRow.eachCell((cell, index) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: {argb: 'C0C0C0C0'},
        bgColor: {argb: 'FF0000FF'}
      };
      cell.border = {top: {style: 'thin'}, left: {style: 'thin'}, bottom: {style: 'thin'}, right: {style: 'thin'}};
      cell.font = {bold: true, size: 11, name: 'Times New Roman'};
      cell.alignment = {wrapText: true, horizontal: 'center', vertical: 'middle'};
      worksheet.getColumn(index).width = headerArray[index - 1].length < 20 ? 20 : headerArray[index - 1].length;
    });

    worksheet.getCell('A8').value = 'STT';

    worksheet.getCell('B8').value = excelColumnName1.toString();

    worksheet.getCell('C8').value = 'Số hoá hồ sơ đầu vào';

    worksheet.getCell('D8').value = 'Thực hiện quy trình số hóa hồ sơ và kết quả';

    worksheet.getCell('E8').value = 'Hồ sơ số hóa kết quả giải quyết';

    worksheet.getCell('F8').value = 'Tổng số hồ sơ phát sinh trong kỳ';

    worksheet.getCell('G8').value = 'Tỷ lệ số hóa hồ sơ đầu vào';

    worksheet.getCell('H8').value = 'Tỷ lệ thực hiện quy trình số hóa hồ sơ và kết quả';

    worksheet.getCell('I8').value = 'Tỷ lệ số hóa kết quả giải quyết';
    let i = 8;
        const j = 8;
        for (i; i <= j; i++) {
            let k = 1;
            const l = 9;
            for (k; k <= l; k++) {
                worksheet.findCell(i, k).alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
                worksheet.findCell(i, k).fill = {
                    type: 'pattern',
                    pattern: 'solid',
                    fgColor: { argb: 'C0C0C0C0' },
                    bgColor: { argb: 'FF0000FF' }
                };
                worksheet.findCell(i, k).border = { top: { style: 'thin' }, left: { style: 'thin' }, bottom: { style: 'thin' }, right: { style: 'thin' } };
                worksheet.findCell(i, k).font = { size: 12, bold: true, name: 'Times New Roman' };
            }
        }

    // get all columns from JSON
    let columnsArray: any[];
    for (const key in excelData) {
      if (excelData.hasOwnProperty(key)) {
        columnsArray = Object.keys(excelData[key]);
      }
    }
    // Add Data and Conditional Formatting
    excelData.forEach((element: any, i) => {
      const eachRow = [];
      columnsArray.forEach((column, index) => {
        
        eachRow.push(element[column]);
      });
   
      const borderrow = worksheet.addRow(eachRow);
      borderrow.eachCell((cell, index) => {
        cell.border = {top: {style: 'thin'}, left: {style: 'thin'}, bottom: {style: 'thin'}, right: {style: 'thin'}};
        cell.font = {size: 12, name: 'Times New Roman'};
        if(cell.address.startsWith("B")){
         cell.alignment = {wrapText: true, horizontal: 'left', vertical: 'middle'};
       }else{
       cell.alignment = {wrapText: true, horizontal: 'center', vertical: 'middle'};
       }
      });
    });

    //footer data row
    if (footerData != null) {
      footerData.forEach((element: any) => {
        const eachRow = [];
        element.forEach((val: any) => {
          eachRow.push(val);
        });
        const footerRow = worksheet.addRow(eachRow);
        footerRow.eachCell((cell) => {
          cell.font = {size: 13, bold: true, name: 'Times New Roman'};
          cell.border = {top: {style: 'thin'}, left: {style: 'thin'}, bottom: {style: 'thin'}, right: {style: 'thin'}};
          cell.alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
        });
      });
    }

    // Save Excel File
    workbook.xlsx.writeBuffer().then((data: ArrayBuffer) => {
      const blob = new Blob([data], {type: EXCEL_TYPE});
      fs.saveAs(blob, excelFileName + EXCEL_EXTENSION);
    });
  }
  public exportToExcelStatisticDvcPayment(
    nationName: string,
    subNationName: string,
    reportHeading: string,
    subHeading: string,
    headerArray: any[],
    excelData: any[],
    excelFileName: string,
    sheetName: string,
    agencyName: string,
    statisticDate: string,
    isVisibleCancelled: boolean,
    footerData: any[]
  ) {
    // create workbook and worksheet
    const workbook = new Workbook();
    workbook.creator = 'Snippet Coder';
    workbook.lastModifiedBy = 'SnippetCoder';
    workbook.created = new Date();
    workbook.modified = new Date();
    const worksheet = workbook.addWorksheet(sheetName);

    worksheet.getColumn('A').font = {name: 'Times New Roman', size: 12};
    worksheet.getColumn('B').font = {name: 'Times New Roman', size: 12};
    worksheet.getColumn('C').font = {name: 'Times New Roman', size: 12};
    worksheet.getColumn('D').font = {name: 'Times New Roman', size: 12};
    worksheet.getColumn('E').font = {name: 'Times New Roman', size: 12};
    worksheet.getColumn('F').font = {name: 'Times New Roman', size: 12};
    worksheet.getColumn('G').font = {name: 'Times New Roman', size: 12};
    worksheet.getColumn('H').font = {name: 'Times New Roman', size: 12};
    worksheet.getColumn('I').font = {name: 'Times New Roman', size: 12};
    worksheet.getColumn('J').font = {name: 'Times New Roman', size: 12};

    worksheet.getColumn('A').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getColumn('B').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getColumn('C').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getColumn('D').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getColumn('E').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getColumn('F').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getColumn('G').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getColumn('H').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getColumn('I').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getColumn('J').alignment = { horizontal: 'center', vertical: 'middle' };

    // worksheet.getColumn('A').width = 30;
    worksheet.getColumn('B').width = 40;
    worksheet.getColumn('C').width = 20;
    worksheet.getColumn('D').width = 20;
    worksheet.getColumn('E').width = 20;
    worksheet.getColumn('F').width = 20;
    worksheet.getColumn('G').width = 20;
    worksheet.getColumn('H').width = 20;
    worksheet.getColumn('I').width = 20;
    worksheet.getColumn('J').width = 20;

    worksheet.properties.outlineLevelCol = 2;
    worksheet.properties.defaultRowHeight = 15;


    // Add header row
    worksheet.mergeCells('A1:C1');
    worksheet.getCell('A1').value = agencyName;
    worksheet.getCell('A1').style = {font: {name: 'Times New Roman'}, alignment: {horizontal: 'center', vertical: 'middle'}};

    worksheet.addRow([]);

    worksheet.mergeCells('E1:I1');
    worksheet.getCell('E1').value = nationName;
    worksheet.getCell('E1').style = {font: {bold: true, name: 'Times New Roman'}, alignment: {horizontal: 'center', vertical: 'middle'}};

    worksheet.mergeCells('E2:I2');
    worksheet.getCell('E2').value = subNationName;
    worksheet.getCell('E2').style = {font: {bold: true, name: 'Times New Roman'}, alignment: {horizontal: 'center', vertical: 'middle'}};

    worksheet.mergeCells('E4:I4');
    worksheet.getCell('E4').value = reportHeading;
    worksheet.getCell('E4').style = {
      font: {bold: true, name: 'Times New Roman', size: 16},
      alignment: {horizontal: 'center', vertical: 'middle'}
    };

    worksheet.mergeCells('E5:I5');
    worksheet.getCell('E5').value = subHeading;
    worksheet.getCell('E5').style = {font: {size: 12, name: 'Times New Roman'}, alignment: {horizontal: 'center', vertical: 'middle'}};



    if (isVisibleCancelled) {
      worksheet.mergeCells('M6:O6');
      worksheet.getCell('M6').value = statisticDate;
      worksheet.getCell('M6').alignment = {horizontal: 'right', vertical: 'middle'};
    } else {
      worksheet.mergeCells('K6:M6');
      worksheet.getCell('K6').value = statisticDate;
      worksheet.getCell('K6').alignment = {horizontal: 'right', vertical: 'middle'};
    }

    
    worksheet.addRow([]);
    const headerRow = worksheet.addRow(headerArray);
    worksheet.getRow(8).height = 30;

    headerRow.eachCell((cell, index) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: {argb: 'C0C0C0C0'},
        bgColor: {argb: 'FF0000FF'}
      };
      cell.border = {top: {style: 'thin'}, left: {style: 'thin'}, bottom: {style: 'thin'}, right: {style: 'thin'}};
      cell.font = {bold: true, size: 11, name: 'Times New Roman'};
      cell.alignment = {wrapText: true, horizontal: 'center', vertical: 'middle'};
      worksheet.getColumn(index).width = headerArray[index - 1].length < 20 ? 20 : headerArray[index - 1].length;
    });

    worksheet.getCell('A8').value = 'STT';

    worksheet.getCell('B8').value = 'Tên đơn vị';

    worksheet.getCell('C8').value = 'Số DVCTT có phát sinh TTTT';

    worksheet.getCell('D8').value = 'Số DVCTT có phí, lệ phí';

    worksheet.getCell('E8').value = 'Tỷ lệ DVCTT có phát sinh TTTT';

    worksheet.getCell('F8').value = 'Tổng số hồ sơ thanh toán trực tuyến';

    worksheet.getCell('G8').value = 'Tổng số hồ sơ có yêu cầu phí, lệ phí';

    worksheet.getCell('H8').value = 'Tỷ lệ thanh toán (%)';
    let i = 8;
        const j = 8;
        for (i; i <= j; i++) {
            let k = 1;
            const l = 8;
            for (k; k <= l; k++) {
                worksheet.findCell(i, k).alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
                worksheet.findCell(i, k).fill = {
                    type: 'pattern',
                    pattern: 'solid',
                    fgColor: { argb: 'C0C0C0C0' },
                    bgColor: { argb: 'FF0000FF' }
                };
                worksheet.findCell(i, k).border = { top: { style: 'thin' }, left: { style: 'thin' }, bottom: { style: 'thin' }, right: { style: 'thin' } };
                worksheet.findCell(i, k).font = { size: 12, bold: true, name: 'Times New Roman' };
            }
        }

    // get all columns from JSON
    let columnsArray: any[];
    for (const key in excelData) {
      if (excelData.hasOwnProperty(key)) {
        columnsArray = Object.keys(excelData[key]);
      }
    }
    // Add Data and Conditional Formatting
    excelData.forEach((element: any, i) => {
      const eachRow = [];
      columnsArray.forEach((column, index) => {
        
        eachRow.push(element[column]);
      });
   
      const borderrow = worksheet.addRow(eachRow);
      borderrow.eachCell((cell, index) => {
        cell.border = {top: {style: 'thin'}, left: {style: 'thin'}, bottom: {style: 'thin'}, right: {style: 'thin'}};
        cell.font = {size: 12, name: 'Times New Roman'};
        if(cell.address.startsWith("B")){
         cell.alignment = {wrapText: true, horizontal: 'left', vertical: 'middle'};
       }else{
       cell.alignment = {wrapText: true, horizontal: 'center', vertical: 'middle'};
       }
      });
    });

    worksheet.getColumn('A').width = 10;
    worksheet.getColumn('B').width = 30;
    worksheet.getColumn('C').width = 20;
    worksheet.getColumn('D').width = 20;
    worksheet.getColumn('E').width = 20;
    worksheet.getColumn('F').width = 20;
    worksheet.getColumn('G').width = 20;
    worksheet.getColumn('H').width = 20;
    worksheet.getColumn('I').width = 20;
    worksheet.getColumn('J').width = 20;
    worksheet.getColumn('K').width = 20;
    worksheet.getColumn('L').width = 20;
    worksheet.getColumn('M').width = 20;
    worksheet.getColumn('N').width = 20;
    worksheet.getColumn('O').width = 20;

    // footer data row
    if (footerData != null) {
      footerData.forEach((element: any) => {
        const eachRow = [];
        element.forEach((val: any) => {
          eachRow.push(val);
        });
        const footerRow = worksheet.addRow(eachRow);
        footerRow.eachCell((cell) => {
          cell.font = {size: 13, bold: true, name: 'Times New Roman'};
          cell.border = {top: {style: 'thin'}, left: {style: 'thin'}, bottom: {style: 'thin'}, right: {style: 'thin'}};
          cell.alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
        });
      });
    }

    // Save Excel File
    workbook.xlsx.writeBuffer().then((data: ArrayBuffer) => {
      const blob = new Blob([data], {type: EXCEL_TYPE});
      fs.saveAs(blob, excelFileName + EXCEL_EXTENSION);
    });
  }

  public exportToExcelStatisticOverdueReport(
    nationName: string,
    subNationName: string,
    reportHeading: string,
    subHeading: string,
    headerArray: any[],
    excelData: any[],
    excelFileName: string,
    sheetName: string,
    agencyName: string,
    statisticDate: string,
    isVisibleCancelled: boolean,
    footerData: any[]
  ) {
    // create workbook and worksheet
    const workbook = new Workbook();
    workbook.creator = 'Snippet Coder';
    workbook.lastModifiedBy = 'SnippetCoder';
    workbook.created = new Date();
    workbook.modified = new Date();
    const worksheet = workbook.addWorksheet(sheetName);

    worksheet.getColumn('A').font = {name: 'Times New Roman', size: 12};
    worksheet.getColumn('B').font = {name: 'Times New Roman', size: 12};
    worksheet.getColumn('C').font = {name: 'Times New Roman', size: 12};
    worksheet.getColumn('D').font = {name: 'Times New Roman', size: 12};
    worksheet.getColumn('E').font = {name: 'Times New Roman', size: 12};
    worksheet.getColumn('F').font = {name: 'Times New Roman', size: 12};
    worksheet.getColumn('G').font = {name: 'Times New Roman', size: 12};
    worksheet.getColumn('H').font = {name: 'Times New Roman', size: 12};
    worksheet.getColumn('I').font = {name: 'Times New Roman', size: 12};
    worksheet.getColumn('J').font = {name: 'Times New Roman', size: 12};

    worksheet.getColumn('A').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getColumn('B').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getColumn('C').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getColumn('D').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getColumn('E').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getColumn('F').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getColumn('G').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getColumn('H').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getColumn('I').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getColumn('J').alignment = { horizontal: 'center', vertical: 'middle' };

    // worksheet.getColumn('A').width = 30;
    worksheet.getColumn('B').width = 40;
    worksheet.getColumn('C').width = 20;
    worksheet.getColumn('D').width = 20;
    worksheet.getColumn('E').width = 20;
    worksheet.getColumn('F').width = 20;
    worksheet.getColumn('G').width = 20;
    worksheet.getColumn('H').width = 20;
    worksheet.getColumn('I').width = 20;
    worksheet.getColumn('J').width = 20;

    worksheet.properties.outlineLevelCol = 2;
    worksheet.properties.defaultRowHeight = 15;


    // Add header row
    worksheet.mergeCells('A1:C1');
    worksheet.getCell('A1').value = agencyName;
    worksheet.getCell('A1').style = {font: {name: 'Times New Roman'}, alignment: {horizontal: 'center', vertical: 'middle'}};

    worksheet.addRow([]);

    worksheet.mergeCells('E1:I1');
    worksheet.getCell('E1').value = nationName;
    worksheet.getCell('E1').style = {font: {bold: true, name: 'Times New Roman'}, alignment: {horizontal: 'center', vertical: 'middle'}};

    worksheet.mergeCells('E2:I2');
    worksheet.getCell('E2').value = subNationName;
    worksheet.getCell('E2').style = {font: {bold: true, name: 'Times New Roman'}, alignment: {horizontal: 'center', vertical: 'middle'}};

    worksheet.mergeCells('E4:I4');
    worksheet.getCell('E4').value = reportHeading;
    worksheet.getCell('E4').style = {
      font: {bold: true, name: 'Times New Roman', size: 16},
      alignment: {horizontal: 'center', vertical: 'middle'}
    };

    worksheet.mergeCells('E5:I5');
    worksheet.getCell('E5').value = subHeading;
    worksheet.getCell('E5').style = {font: {size: 12, name: 'Times New Roman'}, alignment: {horizontal: 'center', vertical: 'middle'}};



    if (isVisibleCancelled) {
      worksheet.mergeCells('M6:O6');
      worksheet.getCell('M6').value = statisticDate;
      worksheet.getCell('M6').alignment = {horizontal: 'right', vertical: 'middle'};
    } else {
      worksheet.mergeCells('K6:M6');
      worksheet.getCell('K6').value = statisticDate;
      worksheet.getCell('K6').alignment = {horizontal: 'right', vertical: 'middle'};
    }

    
    worksheet.addRow([]);
    const headerRow = worksheet.addRow(headerArray);
    worksheet.getRow(8).height = 30;

    headerRow.eachCell((cell, index) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: {argb: 'C0C0C0C0'},
        bgColor: {argb: 'FF0000FF'}
      };
      cell.border = {top: {style: 'thin'}, left: {style: 'thin'}, bottom: {style: 'thin'}, right: {style: 'thin'}};
      cell.font = {bold: true, size: 11, name: 'Times New Roman'};
      cell.alignment = {wrapText: true, horizontal: 'center', vertical: 'middle'};
      worksheet.getColumn(index).width = headerArray[index - 1].length < 20 ? 20 : headerArray[index - 1].length;
    });
    worksheet.mergeCells('A7:H7');
    worksheet.getCell("A7").value = "DANH SÁCH ĐƠN VỊ CÓ HỒ SƠ CHẬM, MUỘN TRONG GIẢI QUYẾT TTHC CÓ TỈ LỆ VƯỢT QUÁ 1%"

    worksheet.mergeCells('A8:A10');
    worksheet.getCell('A8').value = 'STT';

    worksheet.mergeCells('B8:B10');
    worksheet.getCell('B8').value = 'Tên đơn vị';

    worksheet.mergeCells('C8:C10');
    worksheet.getCell('C8').value = 'Tổng số hồ sơ tiếp nhận';

    worksheet.mergeCells('D8:G8');
    worksheet.getCell('D8').value = 'Kết quả giải quyết';
    worksheet.getCell('D8').style = {font: {name: 'Times New Roman'}, alignment: {horizontal: 'center', vertical: 'middle'}};
    worksheet.mergeCells('D9:E9');
    worksheet.getCell('D9').value = 'Số hồ sơ đã thực hiện';

    worksheet.mergeCells('F9:G9');
    worksheet.getCell('F9').value = 'Số hồ sơ đang thực hiện';

    worksheet.mergeCells('H8:H10');
    worksheet.getCell('H8').value = 'Tỉ lệ hồ sơ chậm,muộn/Tổng số hồ sơ';

    worksheet.getCell('D10').value = 'Tổng số';
    worksheet.getCell('E10').value = 'Trễ hạn';
    worksheet.getCell('F10').value = 'Tổng số';
    worksheet.getCell('G10').value = 'Quá hạn';


    // let i = 8;
    //     const j = 8;
    //     for (i; i <= j; i++) {
    //         let k = 1;
    //         const l = 8;
    //         for (k; k <= l; k++) {
    //             worksheet.findCell(i, k).alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
    //             worksheet.findCell(i, k).fill = {
    //                 type: 'pattern',
    //                 pattern: 'solid',
    //                 fgColor: { argb: 'C0C0C0C0' },
    //                 bgColor: { argb: 'FF0000FF' }
    //             };
    //             worksheet.findCell(i, k).border = { top: { style: 'thin' }, left: { style: 'thin' }, bottom: { style: 'thin' }, right: { style: 'thin' } };
    //             worksheet.findCell(i, k).font = { size: 12, bold: true, name: 'Times New Roman' };
    //         }
    //     }
    let i = 8;
        const j = 10;
        for (i; i <= j; i++) {
            let k = 1;
            const l = 8;
            for (k; k <= l; k++) {
                worksheet.findCell(i, k).alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
                worksheet.findCell(i, k).fill = {
                    type: 'pattern',
                    pattern: 'solid',
                    fgColor: { argb: 'C0C0C0C0' },
                    bgColor: { argb: 'FF0000FF' }
                };
                worksheet.findCell(i, k).border = { top: { style: 'thin' }, left: { style: 'thin' }, bottom: { style: 'thin' }, right: { style: 'thin' } };
                worksheet.findCell(i, k).font = { size: 12, bold: true, name: 'Times New Roman' };
            }
        }

    // get all columns from JSON
    let columnsArray: any[];
    for (const key in excelData) {
      if (excelData.hasOwnProperty(key)) {
        columnsArray = Object.keys(excelData[key]);
      }
    }
    // Add Data and Conditional Formatting
    excelData.forEach((element: any, i) => {
      const eachRow = [];
      columnsArray.forEach((column, index) => {
        
        eachRow.push(element[column]);
      });
   
      const borderrow = worksheet.addRow(eachRow);
      borderrow.eachCell((cell, index) => {
        cell.border = {top: {style: 'thin'}, left: {style: 'thin'}, bottom: {style: 'thin'}, right: {style: 'thin'}};
        cell.font = {size: 12, name: 'Times New Roman'};
        if(cell.address.startsWith("B")){
         cell.alignment = {wrapText: true, horizontal: 'left', vertical: 'middle'};
       }else{
       cell.alignment = {wrapText: true, horizontal: 'center', vertical: 'middle'};
       }
      });
    });

    worksheet.getColumn('A').width = 10;
    worksheet.getColumn('B').width = 30;
    worksheet.getColumn('C').width = 20;
    worksheet.getColumn('D').width = 20;
    worksheet.getColumn('E').width = 20;
    worksheet.getColumn('F').width = 20;
    worksheet.getColumn('G').width = 20;
    worksheet.getColumn('H').width = 20;
    worksheet.getColumn('I').width = 20;
    worksheet.getColumn('J').width = 20;
    worksheet.getColumn('K').width = 20;
    worksheet.getColumn('L').width = 20;
    worksheet.getColumn('M').width = 20;
    worksheet.getColumn('N').width = 20;
    worksheet.getColumn('O').width = 20;

    // footer data row
    if (footerData != null) {
      footerData.forEach((element: any) => {
        const eachRow = [];
        element.forEach((val: any) => {
          eachRow.push(val);
        });
        const footerRow = worksheet.addRow(eachRow);
        footerRow.eachCell((cell) => {
          cell.font = {size: 13, bold: true, name: 'Times New Roman'};
          cell.border = {top: {style: 'thin'}, left: {style: 'thin'}, bottom: {style: 'thin'}, right: {style: 'thin'}};
          cell.alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
        });
      });
    }

    // Save Excel File
    workbook.xlsx.writeBuffer().then((data: ArrayBuffer) => {
      const blob = new Blob([data], {type: EXCEL_TYPE});
      fs.saveAs(blob, excelFileName + EXCEL_EXTENSION);
    });
  }
  getStatisticOnlinePaymentQBH(searchString,type): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    if (localStorage.getItem('isLoggedIn') == 'false') {
      const token = localStorage.getItem('OAuth2TOKEN');
      headers = headers.append('Authorization', 'Bearer ' + token);
    }
    if(type == "0"){
      return this.http.get(this.padman + '/qbh-statistic-dvc/--online-payment-qbh' + searchString, { headers }).pipe();
      //return this.http.get( 'http://localhost:8081/qbh-statistic-dvc/--online-payment-qbh' + searchString, { headers }).pipe();
    }
    else {
      return this.http.get(this.padman + '/qbh-statistic-dvc/--online-payment-secprod-qbh' + searchString, { headers }).pipe();
      //return this.http.get( 'http://localhost:8081/qbh-statistic-dvc/--online-payment-secprod-qbh' + searchString, { headers }).pipe();
    }
  }

  getDossierDitialPublicV1(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    if (localStorage.getItem('isLoggedIn') == 'false') {
      const token = localStorage.getItem('OAuth2TOKEN');
      headers = headers.append('Authorization', 'Bearer ' + token);
    }
    return this.http.get(this.padman + '/qbh-statistic-dvc/--digital-qbh-v1' + searchString, { headers }).pipe();
  }

  public exportToExcelStatisticDvcDigitalV1(
    nationName: string,
    subNationName: string,
    reportHeading: string,
    subHeading: string,
    headerArray: any[],
    excelData: any[],
    excelFileName: string,
    sheetName: string,
    agencyName: string,
    statisticDate: string,
    isVisibleCancelled: boolean,
    excelColumnName1: string,
    footerData: any[]
  ) {
    // create workbook and worksheet
    const workbook = new Workbook();
    workbook.creator = 'Snippet Coder';
    workbook.lastModifiedBy = 'SnippetCoder';
    workbook.created = new Date();
    workbook.modified = new Date();
    const worksheet = workbook.addWorksheet(sheetName);

    worksheet.getColumn('A').font = {name: 'Times New Roman', size: 12};
    worksheet.getColumn('B').font = {name: 'Times New Roman', size: 12};
    worksheet.getColumn('C').font = {name: 'Times New Roman', size: 12};
    worksheet.getColumn('D').font = {name: 'Times New Roman', size: 12};
    worksheet.getColumn('E').font = {name: 'Times New Roman', size: 12};
    worksheet.getColumn('F').font = {name: 'Times New Roman', size: 12};
    worksheet.getColumn('G').font = {name: 'Times New Roman', size: 12};
    worksheet.getColumn('H').font = {name: 'Times New Roman', size: 12};
    worksheet.getColumn('I').font = {name: 'Times New Roman', size: 12};
    worksheet.getColumn('J').font = {name: 'Times New Roman', size: 12};

    worksheet.getColumn('A').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getColumn('B').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getColumn('C').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getColumn('D').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getColumn('E').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getColumn('F').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getColumn('G').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getColumn('H').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getColumn('I').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getColumn('J').alignment = { horizontal: 'center', vertical: 'middle' };

    // worksheet.getColumn('A').width = 30;
    worksheet.getColumn('B').width = 40;
    worksheet.getColumn('C').width = 20;
    worksheet.getColumn('D').width = 20;
    worksheet.getColumn('E').width = 20;
    worksheet.getColumn('F').width = 20;
    worksheet.getColumn('G').width = 20;
    worksheet.getColumn('H').width = 20;
    worksheet.getColumn('I').width = 20;
    worksheet.getColumn('J').width = 20;

    worksheet.properties.outlineLevelCol = 2;
    worksheet.properties.defaultRowHeight = 15;


    // Add header row
    worksheet.mergeCells('A1:C1');
    worksheet.getCell('A1').value = agencyName;
    worksheet.getCell('A1').style = {font: {name: 'Times New Roman'}, alignment: {horizontal: 'center', vertical: 'middle'}};

    worksheet.addRow([]);

    worksheet.mergeCells('E1:I1');
    worksheet.getCell('E1').value = nationName;
    worksheet.getCell('E1').style = {font: {bold: true, name: 'Times New Roman'}, alignment: {horizontal: 'center', vertical: 'middle'}};

    worksheet.mergeCells('E2:I2');
    worksheet.getCell('E2').value = subNationName;
    worksheet.getCell('E2').style = {font: {bold: true, name: 'Times New Roman'}, alignment: {horizontal: 'center', vertical: 'middle'}};

    worksheet.mergeCells('E4:I4');
    worksheet.getCell('E4').value = reportHeading;
    worksheet.getCell('E4').style = {
      font: {bold: true, name: 'Times New Roman', size: 16},
      alignment: {horizontal: 'center', vertical: 'middle'}
    };

    worksheet.mergeCells('E5:I5');
    worksheet.getCell('E5').value = subHeading;
    worksheet.getCell('E5').style = {font: {size: 12, name: 'Times New Roman'}, alignment: {horizontal: 'center', vertical: 'middle'}};



    if (isVisibleCancelled) {
      worksheet.mergeCells('M6:O6');
      worksheet.getCell('M6').value = statisticDate;
      worksheet.getCell('M6').alignment = {horizontal: 'right', vertical: 'middle'};
    } else {
      worksheet.mergeCells('K6:M6');
      worksheet.getCell('K6').value = statisticDate;
      worksheet.getCell('K6').alignment = {horizontal: 'right', vertical: 'middle'};
    }

    
    worksheet.addRow([]);
    const headerRow = worksheet.addRow(headerArray);
    worksheet.getRow(8).height = 30;

    headerRow.eachCell((cell, index) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: {argb: 'C0C0C0C0'},
        bgColor: {argb: 'FF0000FF'}
      };
      cell.border = {top: {style: 'thin'}, left: {style: 'thin'}, bottom: {style: 'thin'}, right: {style: 'thin'}};
      cell.font = {bold: true, size: 11, name: 'Times New Roman'};
      cell.alignment = {wrapText: true, horizontal: 'center', vertical: 'middle'};
      worksheet.getColumn(index).width = headerArray[index - 1].length < 20 ? 20 : headerArray[index - 1].length;
    });

    worksheet.getCell('A8').value = 'STT';

    worksheet.getCell('B8').value = excelColumnName1.toString();

    worksheet.getCell('C8').value = 'Số hóa hồ sơ và kết quả';

    worksheet.getCell('D8').value = 'Hồ sơ số hóa kết quả giải quyết';

    worksheet.getCell('E8').value = 'Tổng số hồ sơ phát sinh trong kỳ';

    worksheet.getCell('F8').value = 'Tỷ lệ hồ sơ số hóa';

    worksheet.getCell('G8').value = 'Tỷ lệ số hóa kết quả';
    let i = 8;
        const j = 8;
        for (i; i <= j; i++) {
            let k = 1;
            const l = 7;
            for (k; k <= l; k++) {
                worksheet.findCell(i, k).alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
                worksheet.findCell(i, k).fill = {
                    type: 'pattern',
                    pattern: 'solid',
                    fgColor: { argb: 'C0C0C0C0' },
                    bgColor: { argb: 'FF0000FF' }
                };
                worksheet.findCell(i, k).border = { top: { style: 'thin' }, left: { style: 'thin' }, bottom: { style: 'thin' }, right: { style: 'thin' } };
                worksheet.findCell(i, k).font = { size: 12, bold: true, name: 'Times New Roman' };
            }
        }

    // get all columns from JSON
    let columnsArray: any[];
    for (const key in excelData) {
      if (excelData.hasOwnProperty(key)) {
        columnsArray = Object.keys(excelData[key]);
      }
    }
    // Add Data and Conditional Formatting
    excelData.forEach((element: any, i) => {
      const eachRow = [];
      columnsArray.forEach((column, index) => {
        
        eachRow.push(element[column]);
      });
   
      const borderrow = worksheet.addRow(eachRow);
      borderrow.eachCell((cell, index) => {
        cell.border = {top: {style: 'thin'}, left: {style: 'thin'}, bottom: {style: 'thin'}, right: {style: 'thin'}};
        cell.font = {size: 12, name: 'Times New Roman'};
        if(cell.address.startsWith("B")){
         cell.alignment = {wrapText: true, horizontal: 'left', vertical: 'middle'};
       }else{
       cell.alignment = {wrapText: true, horizontal: 'center', vertical: 'middle'};
       }
      });
    });

    worksheet.getColumn('A').width = 10;
    worksheet.getColumn('B').width = 30;
    worksheet.getColumn('C').width = 20;
    worksheet.getColumn('D').width = 20;
    worksheet.getColumn('E').width = 20;
    worksheet.getColumn('F').width = 20;
    worksheet.getColumn('G').width = 20;
    worksheet.getColumn('H').width = 20;
    worksheet.getColumn('I').width = 20;
    worksheet.getColumn('J').width = 20;
    worksheet.getColumn('K').width = 20;
    worksheet.getColumn('L').width = 20;
    worksheet.getColumn('M').width = 20;
    worksheet.getColumn('N').width = 20;
    worksheet.getColumn('O').width = 20;

    // footer data row
    // if (footerData != null) {
    //   footerData.forEach((element: any) => {
    //     const eachRow = [];
    //     element.forEach((val: any) => {
    //       eachRow.push(val);
    //     });
    //     const footerRow = worksheet.addRow(eachRow);
    //     footerRow.eachCell((cell) => {
    //       cell.font = {size: 13, bold: true, name: 'Times New Roman'};
    //       cell.border = {top: {style: 'thin'}, left: {style: 'thin'}, bottom: {style: 'thin'}, right: {style: 'thin'}};
    //       cell.alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
    //     });
    //   });
    // }

    // Save Excel File
    workbook.xlsx.writeBuffer().then((data: ArrayBuffer) => {
      const blob = new Blob([data], {type: EXCEL_TYPE});
      fs.saveAs(blob, excelFileName + EXCEL_EXTENSION);
    });
  }
}
