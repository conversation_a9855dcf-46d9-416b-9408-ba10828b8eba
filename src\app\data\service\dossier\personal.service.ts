import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { ApiProviderService } from 'src/app/core/service/api-provider.service';
import { Observable } from 'rxjs';
import {SnackbarService} from "data/service/snackbar/snackbar.service";
import {EnvService} from "core/service/env.service";
import { DeploymentService } from 'src/app/data/service/deployment.service';


@Injectable({
  providedIn: 'root'
})
export class PersonalService {
  config = this.envService.getConfig();
  padmanReport  = this.deploymentService.getAppDeployment()?.padmanReport || false;
  constructor(
    private http: HttpClient,
    private apiProviderService: ApiProviderService,
    private snackbar: SnackbarService,
    private envService: EnvService,
    private deploymentService : DeploymentService
  ) { }

  private padmanPath = this.apiProviderService.getUrl('digo', 'padman');
  private dossierURLReport = this.apiProviderService.getUrl('digo', 'padmanReport');
  getApiDossierPersonal(searchString): Observable<any> {
    const URL =(!this.padmanReport ? this.padmanPath : this.dossierURLReport)+ '/dossier/personal' + searchString;
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get( URL, { headers });
  }

  getApiDossierSytStatistics(searchString): Observable<any> {
    //this.padmanPath = 'http://localhost:8081';
    const URL = (!this.padmanReport ? this.padmanPath : this.dossierURLReport) + '/dossier/syt-dossier-statistics' + searchString; 
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get( URL, { headers });
  }

  
  exportToExcelDossierSytStatistics(params: any) { 
    return new Promise((resolve) => {
      //this.padmanPath = 'http://localhost:8081';
      this.http.get((!this.padmanReport ? this.padmanPath : this.dossierURLReport)+ '/dossier-statistic/--export-syt-dossier-statistics' + params, {
        observe: 'response',
        responseType: 'blob'
      }).toPromise().then(res => {        
        const blob = new Blob([res.body], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'});
        let filename = 'baocao_syt.xlsx';
        if (res.headers.get('content-disposition') != null) {
          filename = res.headers.get('content-disposition').split(';')[1].split('=')[1].replace(/\"/g, '');
        }
        const blobUrl = URL.createObjectURL(blob);

        const xhr = new XMLHttpRequest();
        xhr.responseType = 'blob';

        xhr.onload = () => {
          const recoveredBlob = xhr.response;
          const reader = new FileReader();
          reader.onload = () => {
            const base64data = reader.result.toString();
            const object = JSON.stringify({
              mimeType: filename.substring(filename.lastIndexOf('.') + 1),
              name: filename.substring(0, filename.lastIndexOf('.')),
              data: base64data.split(',')[1]
            });
            const anchor = document.createElement('a');
            anchor.download = filename;
            anchor.href = base64data;
            anchor.click();
          };
          reader.readAsDataURL(recoveredBlob);
        };

        xhr.open('GET', blobUrl);
        xhr.send();
        resolve(true);
      }).catch(err => {
        if (err.status === 500) {
          const message = {
            vi: 'Hệ thống tạm thời không thể xuất file excel!',
            en: 'The system is temporarily unable to export the excel file!'
          };
          this.snackbar.openSnackBar(0, '', message[localStorage.getItem('language')], 'error_notification', this.config.expiredTime);
        }
        resolve(false);
      });
    });

  }


  exportToExcelDossierPersonal(params: any) {
    return new Promise((resolve) => {
      this.http.get(this.padmanPath + '/dossier-statistic/personal/--export' + params, {
        observe: 'response',
        responseType: 'blob'
      }).toPromise().then(res => {
        if (!!res.body && res.body.size === 0) {
          const message = {
            vi: 'Hệ thống tạm thời không thể xuất file excel vì không có dữ liệu!',
            en: 'The system is temporarily unable to export the excel file so not data!'
          };
          this.snackbar.openSnackBar(0, '', message[localStorage.getItem('language')], 'error_notification', this.config.expiredTime);
          resolve(false);
          return;
        }
        // //debugger;
        const blob = new Blob([res.body], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'});
        let filename = 'baocao_hoso_canhan.xlsx';
        if (res.headers.get('content-disposition') != null) {
          filename = res.headers.get('content-disposition').split(';')[1].split('=')[1].replace(/\"/g, '');
        }
        const blobUrl = URL.createObjectURL(blob);

        const xhr = new XMLHttpRequest();
        xhr.responseType = 'blob';

        xhr.onload = () => {
          const recoveredBlob = xhr.response;
          const reader = new FileReader();
          reader.onload = () => {
            const base64data = reader.result.toString();
            const object = JSON.stringify({
              mimeType: filename.substring(filename.lastIndexOf('.') + 1),
              name: filename.substring(0, filename.lastIndexOf('.')),
              data: base64data.split(',')[1]
            });
            const anchor = document.createElement('a');
            anchor.download = filename;
            anchor.href = base64data;
            anchor.click();
          };
          reader.readAsDataURL(recoveredBlob);
        };

        xhr.open('GET', blobUrl);
        xhr.send();
        resolve(true);
      }).catch(err => {
        if (err.status === 500) {
          const message = {
            vi: 'Hệ thống tạm thời không thể xuất file excel!',
            en: 'The system is temporarily unable to export the excel file!'
          };
          this.snackbar.openSnackBar(0, '', message[localStorage.getItem('language')], 'error_notification', this.config.expiredTime);
        }
        resolve(false);
      });
    });

  }

}
