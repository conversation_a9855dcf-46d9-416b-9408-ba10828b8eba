<button mat-icon-button class="close-button" (click)="onDismiss()">
    <mat-icon>close</mat-icon>
</button>
<h3 class="dialog_title" mat-dialog-title >{{typeAction == 'add' ? 'Thu ph<PERSON> hồ sơ' : '<PERSON><PERSON><PERSON> thu phí hồ sơ'}}</h3>
<form [formGroup]="addForm" class="searchForm edit formApprovalAgency">
    <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="column" class="marginRow formFieldOutline">
        <div fxFlex='49.5'>
            <p><b><PERSON><PERSON> hồ sơ</b></p>
            <p>{{dossier.code}}</p>
        </div>
        <div fxFlex='1'></div>
        <div fxFlex='49.5'>
            <p><b>Người đăng ký</b></p>
            <p>
                <span *ngIf="dossier.applicant?.data?.fullname != '' && dossier.applicant?.data?.fullname">{{dossier.applicant?.data?.fullname}} </span>

                <span *ngIf="dossier.applicant?.data?.address != '' && dossier.applicant?.data?.address"> {{dossier.applicant?.data?.address}} </span>
                <span *ngIf="dossier.applicant?.data?.village?.label != '' && dossier.applicant?.data?.village?.label">, {{dossier.applicant?.data?.village?.label}} </span>
                <span *ngIf="dossier.applicant?.data?.district?.label != '' && dossier.applicant?.data?.district?.label">, {{dossier.applicant?.data?.district?.label}} </span>
                <span *ngIf="dossier.applicant?.data?.province?.label != '' && dossier.applicant?.data?.province?.label">, {{dossier.applicant?.data?.province?.label}} </span>
            </p>
        </div>
    </div>
    <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="column" class="marginRow formFieldOutline">
        <div fxFlex='49.5'>
            <p><b>Thủ tục</b></p>
            <p>{{procedureName}}</p>
        </div>
        <div fxFlex='1'></div>
        <mat-form-field appearance="outline" fxFlex='49.5'>
            <mat-label>Lệ phí (đ)</mat-label>
            <input type="number" matInput formControlName="fee" maxlength="500" [readonly] = "true">
        </mat-form-field>
    </div>
    <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="column" class="marginRow formFieldOutline">
        <div fxFlex='49.5'>
            <p><b>Cán bộ tiếp nhận</b></p>
            <p *ngIf="!!this.dossier.accepter?.fullname">{{this.dossier.accepter?.fullname}}</p>
        </div>
        <div fxFlex='1'></div>
        <mat-form-field appearance="outline" fxFlex='49.5'>
            <mat-label>Số điện thoại</mat-label>
            <input type="text" matInput formControlName="phoneNumber" maxlength="500">
        </mat-form-field>
    </div>
    <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="column" class="marginRow formFieldOutline">
        <mat-form-field appearance="outline" fxFlex='49.5'>
            <mat-label>Người nộp tiền</mat-label>
            <input type="text" matInput formControlName="submitterName" maxlength="500">
        </mat-form-field>
        <div fxFlex='1'></div>
        <mat-form-field appearance="outline" fxFlex='24.25'>
            <mat-label>Ngày nộp tiền</mat-label>
            <input matInput [matDatepicker]="pickerfromDate" formControlName="dateSubmit"
                 id="txtfromDate">
            <mat-datepicker-toggle matSuffix [for]="pickerfromDate"></mat-datepicker-toggle>
            <mat-datepicker #pickerfromDate></mat-datepicker>
        </mat-form-field>
        <div fxFlex='1'></div>
        <mat-form-field appearance="outline" fxFlex='24.25'>
            <mat-label>Hình thức nộp</mat-label>
            <mat-select formControlName="type">
                <mat-option value="0">Tiền mặt</mat-option>
                <mat-option value="1">Chuyển khoản</mat-option>
            </mat-select>
        </mat-form-field>
    </div>
    <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="column" class="marginRow formFieldOutline">

        <mat-form-field appearance="outline" fxFlex='100'>
            <mat-label>Ghi chú</mat-label>
            <textarea type="text" matInput formControlName="note" maxlength="1000"></textarea>
        </mat-form-field>
    </div>
    <!-- <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="row">
        <div fxFlex.gt-sm="49" fxFlex.lt-md="49" class="header-sector1">
            <mat-form-field appearance="outline" fxFlex='grow'>
                <mat-label i18n>Cho phép nhập số lượng</mat-label>
                <mat-select formControlName="allow">
                    <mat-option *ngFor='let element of listAllow ; let i = index' value="{{element.id}}">
                        {{ element.name }}</mat-option>
                </mat-select>
            </mat-form-field>
        </div>
        <div fxFlex.gt-sm="2" fxFlex.lt-md="2"></div>
        <div fxFlex.gt-sm="49" fxFlex.lt-md="49" class="bn-left">
            <mat-form-field appearance="outline" fxFlex='grow'>
                <mat-label i18n>Trạng thái</mat-label>
                <mat-select formControlName="status">
                    <mat-option *ngFor='let element of status ; let i = index' value="{{element.status}}">
                        {{ element.name }}</mat-option>
                </mat-select>
            </mat-form-field>
        </div>
    </div> -->
    <div fxLayout="row" fxLayoutAlign="center" fxLayout.xs="row" fxLayout.sm="row">
        <button mat-flat-button fxFlex='grow' class="searchBtn" (click)="save()" [disabled] = "disableUpdate">
            <span>Cập nhật</span>
        </button>
    </div>
</form>