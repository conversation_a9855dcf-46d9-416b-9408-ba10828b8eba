::ng-deep .listAllSector .mat-expansion-panel-body {
    padding-bottom: 0px !important;
  }
  .contentItem{
    max-height: 23em;
    overflow-y: auto;
  }
  .headeritemicon{
    padding-top: 5px;
    padding-bottom: 5px;
  }
  .header-border-bottom{
    border-bottom: 1px solid rgb(100, 100, 100);
  }
  .listAllSector{
    border: 1px solid rgb(100, 100, 100);
    border-radius: 4px;
  }
  .headertable{
    display: table;
    font-size: 15px;
    font-weight: 500;
    padding-left: 16px;
  }
  .headertablechild{
    display: table-cell; /* Important */
    vertical-align: middle;
  }
  
  .headeritem{
    display: table;
    // font-size: 15px;
    // font-weight: 500;
    padding-left: 16px;
  }
  .headeritemchild{
    display: table-cell; /* Important */
    vertical-align: middle;
    max-width: 26vw;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  
  .header-button-search{
    font-size: 13px;
    color: rgb(63, 61, 61);
  
  }
  
  .header-button-search-item{
    background-color: rgb(226, 224, 224);
  }
  .dialog_title {
    font-style: normal;
    font-weight: 500;
    font-size: 20px;
    color: #000000;
  }
  
  .close-button {
    float: right;
    top: -5px;
    right: -14px;
    margin-top: -10px !important;
    width: 25px !important;
    height: 25px !important;
    padding: 0 !important;
    line-height: 25px !important;
  }  
  
  .used{
    color: #CE7A58;
  }
  
  ::ng-deep .searchForm .mat-form-field-wrapper {
    padding-bottom: 2px;
  }
  
  .searchForm .searchBtn {
    margin-top: 1em;
    background-color: #CE7A58;
    color: #fff;
    height: 3em;
    max-width: 100px !important;
  }
  
  .searchForm .label {
    margin: .5em 0 .5em .2em;
    font-weight: 500;
  }
  
  .searchForm .rdoAgency .mat-radio-button {
    padding: 0 2em 1em 0;
  }
  
  ::ng-deep .searchForm .rdoAgency .mat-radio-button .mat-radio-inner-circle {
    background-color: #CE7A58;
  }
  
  ::ng-deep .searchForm .rdoAgency .mat-radio-button.mat-accent.mat-radio-checked .mat-radio-outer-circle {
    border-color: #CE7A58;
  }
  
  ::ng-deep .mat-primary .mat-option.mat-selected:not(.mat-option-disabled) {
    color: #CE7A58 !important;
  }
  ::ng-deep .mat-form-field.mat-focused.mat-primary .mat-select-arrow {
    color: #CE7A58;
  }
  
  .dialog_title {
    font-style: normal;
    font-weight: 500;
    font-size: 20px;
    color: #000000;
  }
  
  ::ng-deep .searchForm .mat-form-field-wrapper {
    padding-bottom: 2px;
  }
  
  .searchForm .searchBtn {
    margin-top: 1em;
    background-color: #CE7A58;
    color: #fff;
    height: 3em;
  }
  
  .searchForm .label {
    margin: .5em 0 .5em .2em;
    font-weight: 500;
  }
  
  .searchForm .rdoAgency .mat-radio-button {
    padding: 0 2em 1em 0;
  }
  
  ::ng-deep .searchForm .rdoAgency .mat-radio-button .mat-radio-inner-circle {
    background-color: #CE7A58;
  }
  
  ::ng-deep .searchForm .rdoAgency .mat-radio-button.mat-accent.mat-radio-checked .mat-radio-outer-circle {
    border-color: #CE7A58;
  }
  .header-sector{
    background-color: #F4EADF;
    align-items: center;
    padding: 5px;
    //justify-content: center;
  }
  
  .header-sector1{
    display: flex;
    align-items: center;
  
    //justify-content: center;
  }
  .header-sector2{
    background-color: #F4EADF;
    // height: 30px;
  }
  .marginRow{
    margin-bottom: 10px;
  }
  
  .bn-add{
    float: right;
    margin-top: auto;
    margin-bottom: auto;
    align-items: center;
    color: #CE7A58
  }
  // ::ng-deep .frm_searchbar .mat-form-field-appearance-outline .mat-form-field-infix {
  //     padding: 0.8em 0;
  // }
  
  // ::ng-deep .frm_searchbar .mat-form-field-label-wrapper {
  //     top: -1em;
  // }
  
  // ::ng-deep .frm_searchbar .mat-form-field.mat-focused .mat-form-field-label {
  //     color: #ce7a58;
  //     font-size: 18px;
  //     margin-bottom: 1em;
  // }
  
  // ::ng-deep .frm_searchbar .mat-form-field-appearance-outline.mat-form-field-can-float.mat-form-field-should-float .mat-form-field-label {
  //     color: #ce7a58;
  //     font-size: 18px;
  //     margin-bottom: 1em;
  // }
  ::ng-deep .edit .mat-form-field.mat-focused .mat-form-field-label {
    color: #ce7a58;
  }
  ::ng-deep .edit .mat-form-field-appearance-outline .mat-form-field-outline {
    color: #ffffff!important;
    // opacity: 1!important;
  }
  ::ng-deep .edit .mat-form-field-appearance-outline .mat-form-field-outline {
    background-color: #eaebeb;
    border-radius: 5px;
  }
  ::ng-deep .edit .mat-form-field-appearance-outline.mat-focused .mat-form-field-outline-thick {
    color: #ce7a58!important;
  }
  
  
  // .bn-left{
  //     float: right;
  // }
  .margin-sectorname{
    margin-top: 15px;
  }
  .padding-sectorname{
    padding-bottom: 15px;
    border: 1px solid #dfdcdc;
  }
  .padding-itemsectorname{
    padding: 12px;
    border-radius: 5px;
  }
  .div-icon{
    display: flex;
    align-items: center;
    padding-left: 5px;
  }
  .icon-color{
  
    color:  #CE7A58;
    margin-top: auto;
    margin-bottom: auto;
  
  }
  a.aHover{
    cursor: pointer;
  }
  @media screen and (max-width: 600px) {
    .bn-add{
      float: left;
      margin-top: auto;
      margin-bottom: auto;
      align-items: center;
      color: #CE7A58;
    }
  }
  @media screen and (max-width: 960px) {
    .padding-itemsectorname{
      padding: 5px;
      border-radius: 5px;
    }
  }
  
  .clear-search-nested {
    right: 0 !important;
    position: absolute !important;
    top: 0 !important;
  }
  
  .search-nested {
    width: 80% !important;
    height: 50px !important;
    margin-left: 15px !important;
  }
  