import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { ApiProviderService } from 'src/app/core/service/api-provider.service';
import { EnvService } from 'src/app/core/service/env.service';
import { SnackbarService } from '../snackbar/snackbar.service';

@Injectable({
  providedIn: 'root'
})
export class AGGStatisticService {

  constructor(
    private http: HttpClient,
    private apiProviderService: ApiProviderService,
    private snackbar: SnackbarService,
    private envService: EnvService
  ) { }

  config = this.envService.getConfig();
  public baseExcelExportURL =
  this.apiProviderService.getUrl('digo', 'reporter') +
  '/dossiercounting/';

  public excelExportURL =
    this.apiProviderService.getUrl('digo', 'reporter') +
    '/dossiercounting/--export';

  downloadExport(params: string){
    return this.http.get(this.excelExportURL  + params, { observe: 'response', responseType: 'blob' }).toPromise();
  }

  // Xuất file excel
  excelExport(params: string): Promise<any> {
    return new Promise((resolve) => {
      this.downloadExport(params).then(res => {
        const blob = new Blob([res.body], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
        let filename = "BC_CHI_TIET.xlsx"
        if(!!res.headers.get('content-disposition')){
          filename = res.headers.get('content-disposition').split(';')[1].split('=')[1].replace(/\"/g, '');
        }

        var blobUrl = URL.createObjectURL(blob);

        var xhr = new XMLHttpRequest;
        xhr.responseType = 'blob';

        xhr.onload = function() {
          var recoveredBlob = xhr.response;
          var reader = new FileReader;
          reader.onload = function() {
            var base64data = reader.result.toString();
            var object = JSON.stringify({
              mimeType:filename.substring(filename.lastIndexOf(".")+1),
              name:filename.substring(0,filename.lastIndexOf(".")),
              data:base64data.split(',')[1]
            });
            let anchor = document.createElement("a");
            anchor.download = filename;
            anchor.href = base64data;
            anchor.click();
          };
          reader.readAsDataURL(recoveredBlob);
        };

        xhr.open('GET', blobUrl);
        xhr.send();
        resolve(true);
      })
        .catch(err => {
          if (err.status === 500) {
            this.snackbar.openSnackBar(0, '', 'Hệ thống tạm thời không thể xuất file excel!', 'error_notification', this.config.expiredTime);
          }
          resolve(false)
        });
    });
  }

  public excelExportPaymentURL = this.apiProviderService.getUrl('digo','padman') +'/dossier-payment/--export-excel-list-dossier-payment-agg'
  downloadExportPayment(params: string){
    return this.http.get(this.excelExportPaymentURL + params, { observe: 'response', responseType: 'blob' }).toPromise();
  }

  excelExportPayment(params: string): Promise<any> {
    return new Promise((resolve) => {
      this.downloadExportPayment(params).then(res => {
        const blob = new Blob([res.body], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
        let filename = "BC_CHI_TIET.xlsx"
        if(!!res.headers.get('content-disposition')){
          filename = res.headers.get('content-disposition').split(';')[1].split('=')[1].replace(/\"/g, '');
        }

        var blobUrl = URL.createObjectURL(blob);

        var xhr = new XMLHttpRequest;
        xhr.responseType = 'blob';

        xhr.onload = function() {
          var recoveredBlob = xhr.response;
          var reader = new FileReader;
          reader.onload = function() {
            var base64data = reader.result.toString();
            var object = JSON.stringify({
              mimeType:filename.substring(filename.lastIndexOf(".")+1),
              name:filename.substring(0,filename.lastIndexOf(".")),
              data:base64data.split(',')[1]
            });
            let anchor = document.createElement("a");
            anchor.download = filename;
            anchor.href = base64data;
            anchor.click();
          };
          reader.readAsDataURL(recoveredBlob);
        };

        xhr.open('GET', blobUrl);
        xhr.send();
        resolve(true);
      })
        .catch(err => {
          if (err.status === 500) {
            this.snackbar.openSnackBar(0, '', 'Hệ thống tạm thời không thể xuất file excel!', 'error_notification', this.config.expiredTime);
          }
          resolve(false)
        });
    });
  }
  downloadExportDossierListDetail(params: string){
    return this.http.get(this.baseExcelExportURL + "--export-list-dossier-detail"  + params, { observe: 'response', responseType: 'blob' }).toPromise();
  }

  // Xuất file excel
  excelExportDossierListDetail(params: string): Promise<any> {
    return new Promise((resolve) => {
      this.downloadExportDossierListDetail(params).then(res => {
        const blob = new Blob([res.body], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
        let filename = "BC_CHI_TIET.xlsx"
        if(!!res.headers.get('content-disposition')){
          filename = res.headers.get('content-disposition').split(';')[1].split('=')[1].replace(/\"/g, '');
        }
        var blobUrl = URL.createObjectURL(blob);

        var xhr = new XMLHttpRequest;
        xhr.responseType = 'blob';

        xhr.onload = function() {
          var recoveredBlob = xhr.response;
          var reader = new FileReader;
          reader.onload = function() {
            var base64data = reader.result.toString();
            var object = JSON.stringify({
              mimeType:filename.substring(filename.lastIndexOf(".")+1),
              name:filename.substring(0,filename.lastIndexOf(".")),
              data:base64data.split(',')[1]
            });
            let anchor = document.createElement("a");
            anchor.download = filename;
            anchor.href = base64data;
            anchor.click();
          };
          reader.readAsDataURL(recoveredBlob);
        };

        xhr.open('GET', blobUrl);
        xhr.send();
        resolve(true);
      })
        .catch(err => {
          if (err.status === 500) {
            this.snackbar.openSnackBar(0, '', 'Hệ thống tạm thời không thể xuất file excel!', 'error_notification', this.config.expiredTime);
          }
          resolve(false)
        });
    });
  }
}
