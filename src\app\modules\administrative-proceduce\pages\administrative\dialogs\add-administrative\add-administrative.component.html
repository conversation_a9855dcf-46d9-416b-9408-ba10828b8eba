<button mat-icon-button class="close-button" (click)="onDismiss()">
    <mat-icon>close</mat-icon>
</button>
<h3 class="dialog_title" mat-dialog-title i18n>Thêm mới</h3>
<form [formGroup]="addForm" class="addForm edit" (submit)="onConfirm()" id="ngAddForm">
    <div  *ngFor="let field of fieldArray; let i = index">
    <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="column" class="marginRow">
        <!-- <mat-form-field appearance="outline" fxFlex.gt-sm="98" fxFlex='98'>
            <mat-label i18n>Mã bộ số</mat-label>
            <input type="text" matInput  (change)="changeCode(i , $event)" value="{{field.code}}">
        </mat-form-field> -->
        <mat-form-field appearance="outline" fxFlex.gt-sm="98" fxFlex.gt-xs="98" fxFlex='grow'>
            <mat-label i18n><PERSON><PERSON> sổ</mat-label>
            <mat-select formControlName="publicAdmin"  required>
                <mat-option *ngFor='let pro of dataSource.data;' value="{{pro.id}}">
                    {{pro.code}} - {{pro.name}}
                    <span *ngIf="pro.code== undefined || pro.code == null || pro.code.trim() == ''" i18n>(Không tìm thấy bản dịch)</span>
                </mat-option>
            </mat-select>
        </mat-form-field>
    </div>
    <br>
    <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="column" class="marginRow">
        <mat-form-field appearance="outline" fxFlex.gt-sm="98" fxFlex='98'>
            <mat-label i18n>Tên bộ số</mat-label>
            <input type="text" matInput (change)="changeName(0 , $event)" value="{{fieldArray[0].name}}">
        </mat-form-field>
    </div>
    <br>
    <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="column" class="marginRow">
        <mat-form-field appearance="outline" fxFlex.gt-sm="98" fxFlex='98'>
            <mat-label i18n>Giá trị bắt đầu</mat-label>
            <input type="number" matInput   (change)="changeStartValid(i , $event)" value="{{field.startValid}}">
        </mat-form-field>
    </div>
    <br>
    <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="column" class="marginRow">
        <mat-form-field appearance="outline" fxFlex.gt-sm="98" fxFlex='98'>
            <mat-label i18n>Giá trị kết thúc</mat-label>
            <input type="number" matInput   (change)="changeEndValid(i , $event)" value="{{field.endValid}}">
        </mat-form-field>
    </div>
    <br>
    <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="column" class="marginRow">
        <mat-form-field appearance="outline" fxFlex.gt-sm="98" fxFlex='98'>
            <mat-label >Giá trị hiện tại</mat-label>
            <input type="number" matInput   (change)="changeNowValid(i , $event)" value="{{field.nowValid}}">
        </mat-form-field>
    </div>
    <br>
    <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="column" class="marginRow">
        <mat-form-field appearance="outline" fxFlex.gt-sm="98" fxFlex='98'>
            <mat-label i18n>Cú pháp hiển thị</mat-label>
            <input type="text" matInput  (change)="changeDisplaySyntax(i , $event)" value="{{field.displaySyntax}}">
        </mat-form-field>
    </div>
    <br>
    <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="column" class="marginRow">
            <mat-form-field appearance="outline" fxFlex.gt-sm="98" fxFlex='98'>
                <mat-label i18n>Trạng thái</mat-label>
                <mat-select formControlName="status">
                    <mat-option *ngFor='let element of status ; let i = index' value="{{element.status}}" formControlName="status">
                        {{ element.name }}</mat-option>
                </mat-select>
            </mat-form-field>
    </div>
    <br>
</div>
    <div fxLayout="row" fxLayoutAlign="center" fxLayout.xs="row" fxLayout.sm="row" class="divAddBtn">
        <button [disabled]="isSubmit" mat-flat-button fxFlex='grow' class="saveBtn" type="submit" form="ngAddForm">
            <span i18n>Lưu lại</span>
        </button>
    </div>
</form>