<button mat-icon-button class="close-button" (click)="onDismiss()">
    <mat-icon>close</mat-icon>
</button>
<h3 class="dialog_title" mat-dialog-title i18n>Thông tin từ chối</h3>
<div mat-dialog-content class="dialog_content">
    <span i18n>Bạn có chắc chắn muốn từ chối hồ sơ </span><span class="highlight">{{dossierCode}}</span><span>?</span>
    <div *ngIf="!isUserRQ" class="commentEditor" fxLayout="column" fxLayout.xs="column" fxLayout.sm="column">
        <p class="lbl"><span i18n>Lý do từ chối</span><span style="color:#ce7a58;">&nbsp;*</span></p>
        <ckeditor [editor]="Editor" class="customCKEditor" (change)="onCommentEditorChange($event)" fxFlex='grow' [config]='commentConfig'>
        </ckeditor>
        <div class="errorMsg" *ngIf="isCKMaxlenght">
            <!-- <span i18n>Nội dung không quá 500 ký tự</span> -->
            <span>{{ckeditorMaxLengthNotification}}</span>
            <div class="err">
                <mat-icon>priority_high</mat-icon>
            </div>
        </div>
    </div>
    <div *ngIf="isUserRQ && dossierDetail?.dossierStatus?.id == '12'" class="commentEditor" fxLayout="column" fxLayout.xs="column" fxLayout.sm="column">
        <p class="lbl"><span i18n>Lý do từ chối</span><span style="color:#ce7a58;">&nbsp;*</span></p>
        <ckeditor [data]="dossierDetail?.dossierStatus?.comment" [editor]="Editor" class="customCKEditor" (change)="onCommentEditorChange($event)" fxFlex='grow' [config]='commentConfig'>
        </ckeditor>
        <div class="errorMsg" *ngIf="isCKMaxlenght">
            <!-- <span i18n>Nội dung không quá 500 ký tự</span> -->
            <span>{{ckeditorMaxLengthNotification}}</span>
            <div class="err">
                <mat-icon>priority_high</mat-icon>
            </div>
        </div>
    </div>
    <div *ngIf="isUserRQ && dossierDetail?.dossierStatus?.id != '12'" class="commentEditor" fxLayout="column" fxLayout.xs="column" fxLayout.sm="column">
        <p class="lbl"><span i18n>Lý do từ chối</span><span style="color:#ce7a58;">&nbsp;*</span></p>
        <ckeditor [editor]="Editor" class="customCKEditor" (change)="onCommentEditorChange($event)" fxFlex='grow' [config]='commentConfig'>
        </ckeditor> 
        <div class="errorMsg" *ngIf="isCKMaxlenght"> 
            <!-- <span i18n>Nội dung không quá 500 ký tự</span> -->
            <span>{{ckeditorMaxLengthNotification}}</span>
            <div class="err">
                <mat-icon>priority_high</mat-icon>
            </div>
        </div>
    </div>

    <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="column" style="margin: 0 0 1em 0" *ngIf="enableShowExportFileBtn">
        <button mat-flat-button fxFlex='20' class="applyBtn" (click)="changeFile('docx')">
            <span>Kết xuất file</span>
        </button>
    </div>

    <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="column" style="margin: 0 0 1em 0" *ngIf="showPrintTemplateComponentInRefuseDossierPopup">
        <button mat-flat-button class="applyBtn" [matMenuTriggerFor]="animals">
            <mat-icon>print</mat-icon>
            <span>In phiếu</span>
            <mat-icon>keyboard_arrow_down</mat-icon>
        </button>

        <mat-menu #animals="matMenu">
            <ng-container *ngIf="!!listConfigTemplate && listConfigTemplate.length !== 0">
                <div *ngFor="let configTemplate of listConfigTemplate" fxLayout="row" fxLayoutAlign="space-between"style="width: auto !important;">
                    <div class="d-flex flex-row" style="width: auto !important;">
                        <div style="background-color: #FFF; cursor: pointer" class="w-250">
                            <button mat-menu-item (click)="createPrintTemplate(configTemplate.file.path, configTemplate)">
                                {{configTemplate.name}}
                            </button>
                        </div>
                        <div id="printTemplate" fxLayout="row" fxLayoutAlign="center center" *ngIf="configTemplate.fileSign.length > 0">
                            <div fxLayout="column">
                                <ng-container *ngFor="let item of configTemplate.fileSign">
                                    <div fxLayout="row" fxLayoutAlign="center center" >
                                        <span class="color" style="cursor: pointer" (click)="downloadFile(item.fileSignId, item.fileSignName)"  matTooltip="Tải xuống tệp tin {{item.fileSignName}}">{{item.fileSignName}}</span>
                                        <a mat-menu-item [matMenuTriggerFor]="vertebrates" (click)="editFile(configTemplate.id, item.fileSignId)">
                                            <mat-icon>more_vert</mat-icon>
                                        </a>
                                    </div>
                                    <mat-menu #vertebrates="matMenu">
                                        <button mat-menu-item class="menuAction" (click)="downloadFile(configTemplate.id, configTemplate.file.filename)">
                                                <mat-icon>cloud_download</mat-icon>
                                                <span i18n="@@fileOriginal">Tải xuống tệp tin gốc</span>
                                            </button>
                                      <button mat-menu-item class="menuAction">
                                        <view-sign-history [fileId]="item.fileSignId">
                                          <mat-icon>refresh</mat-icon>
                                          <span>Xem lịch sử ký số</span>
                                        </view-sign-history>
                                      </button>
                                    </mat-menu>
                                </ng-container>
                            </div>
                        </div>
                    </div>
                </div>
            </ng-container>
        </mat-menu>
    </div>

	 <div class="commentEditor" fxLayout="column" fxLayout.xs="column" fxLayout.sm="column">
        <p class="lbl"><span i18n>File đính kèm</span><span style="color:#ce7a58;" *ngIf="requireAttachmentWhenRefuse">&nbsp;*</span>
            <span class="downloadBtn" (click)="downloadTemplate()" *ngIf="!!fileTemplate">
                <mat-icon class="material-icons-outlined">cloud_download</mat-icon>
                <span class="download-text">Tải file mẫu</span>
            </span>
        </p>

        <div [ngClass]="{'file_uploaded': uploaded == true}" fxFlex='grow' fxShow="true" fxHide.lt-md>
            <div class="drag_upload_btn" [ngClass]="{'no_boder': uploaded == true}">
                <button mat-button [ngClass]="{'btn_uploaded': uploaded == true, 'clear_file_queue': uploaded == false}" fxFlex='grow'>
                    <mat-icon class="material-icons-outlined">cloud_upload</mat-icon> <a href="">
                        <span i18n>Kéo thả tệp tin hoặc </span><span class="txtUpload" i18n>Tải lên</span>
                    </a>
                    <div>
                        <span>Kích thước tối đa của một tệp tin:</span> {{ maxFileSize }}MB
                    </div>
                </button>
                <input id="file_upload" name="file_upload[]" type='file' (change)="onSelectFile($event)" multiple [accept]="acceptFileExtension" value="{{blankVal}}" fxFlex='grow'>
            </div>
            <div class="file_drag_upload_preview">
                <div class="list_uploaded" *ngFor='let url of urls; let i = index;'>
                    <div class="file_icon" [ngStyle]="{'background-image': 'url('+ urls[i] +')'}"></div>
                    <span class="file_name" matTooltip="{{uploadFileNames[i].filename}}" [matTooltipPosition]="'right'" >{{fileNames[i]}}</span>
                    <button mat-icon-button class="deleteFile" [matMenuTriggerFor]="actionMenu">
                        <mat-icon>more_horiz</mat-icon>
                      </button>
                      <mat-menu #actionMenu="matMenu" xPosition="before">
                        <a mat-menu-item class="menuAction" target="_blank" [routerLink]="routerLink(uploadFileNames[i])" >
                            <mat-icon>format_size</mat-icon>
                            <span i18n>Xem trước</span>
                          </a>
                          <a mat-menu-item class="menuAction" (click)="downloadFileExport(i)">
                            <mat-icon>cloud_download</mat-icon>
                            <span i18n>Tải xuống tệp tin</span>
                        </a>
                          <a mat-menu-item class="menuAction" (click)="removeItem(i)">
                            <mat-icon>delete_outline</mat-icon>
                            <span i18n>Xóa</span>
                        </a>
                        <a mat-menu-item class="menuAction" (click)="openPdfDigitalSignatureV2( uploadFileNames[i], i, 1)" *ngIf="digitalSignature.VNPTSim && checkIfFileIsSupported(uploadFileNames[i].filename)">
                            <mat-icon>verified</mat-icon>
                            <span>Ký số sim</span>
                        </a>
                          <a mat-menu-item class="menuAction" (click)="openPdfDigitalSignature( uploadFileNames[i].id, uploadFileNames[i].filename,i)" *ngIf="digitalSignature.SmartCA && checkIfFileIsSupported(uploadFileNames[i].filename)">
                            <mat-icon>verified</mat-icon>
                            <span>Ký số Smart CA</span>
                          </a>
                          <a mat-menu-item class="menuAction" (click)="openVGCAplugin( uploadFileNames[i].id, uploadFileNames[i].filename, uploadFileNames[i].size)" *ngIf="digitalSignature.VGCA && checkIfFileIsSupported(uploadFileNames[i].filename)">
                            <mat-icon>verified</mat-icon>
                            <span *ngIf="!isEditSignTokenName; then thenBlock else elseBlock"></span>
                            <ng-template #thenBlock>Ký số ban cơ yếu</ng-template>
                            <ng-template #elseBlock>Ký số Token</ng-template>
                          </a>
                          <a mat-menu-item class="menuAction" (click)="openVnptCaPlugin(uploadFileNames[i].id, uploadFileNames[i].filename)" *ngIf="digitalSignature.VNPTCA && (checkIfFileIsSupported(uploadFileNames[i].filename) || checkIfDocFileOnly(uploadFileNames[i].filename))">
                            <mat-icon>verified</mat-icon>
                            <span>Ký số VNPT-CA</span>
                          </a>
                          <a mat-menu-item class="menuAction" (click)="openNEAC( uploadFileNames[i], i, 5)" *ngIf="digitalSignature.NEAC && checkIfFileIsSupported(uploadFileNames[i].filename)">
                            <mat-icon class="mainColor">verified</mat-icon>
                            <span i18n="@@digitalSignNEAC">Ký số NEAC</span>
                          </a>
                          <a mat-menu-item class="menuAction" (click)="openPdfDigitalSignatureV2(  uploadFileNames[i], i, 6)" *ngIf="digitalSignature.QNM && checkIfFileIsSupported(uploadFileNames[i].filename)">
                            <mat-icon class="mainColor">verified</mat-icon>
                            <span i18n="@@QNMDigitalSignature">Ký số tập trung QNM</span>
                          </a>
                    </mat-menu>
                </div>
            </div>
        </div>

        <!-- Reponsive -->
        <div fxFlex.gt-sm="49.5" fxFlex.gt-xs="grow" fxFlex='grow' class="res_uploadFile" fxShow="true" fxHide.gt-sm>
            <div class="res_upload_btn">
                <button mat-button fxFlex='grow'>
                            <mat-icon class="material-icons-outlined">cloud_upload</mat-icon>
                            <span class="txtUpload" i18n>Tải lên</span>
                        </button>
                <input id="file_upload" name="file_upload[]" type='file' (change)="onSelectFile($event)" multiple [accept]="acceptFileExtension" value="{{blankVal}}" fxFlex='grow'>
            </div>
            <div class="res_upload_preview">
                <div class="list_uploaded" *ngFor='let url of urls; let i = index;' fxFlex.gt-sm="49.5" fxFlex.gt-xs="48.5" fxFlex='grow'>
                    <div class="file_icon" [ngStyle]="{'background-image': 'url('+ urls[i] +')'}"></div>
                    <span class="file_name" matTooltip="{{fileNamesFull[i]}}" [matTooltipPosition]="'right'" >{{fileNames[i]}}</span>
                    <button mat-icon-button class="deleteFile" [matMenuTriggerFor]="actionMenu">
                        <mat-icon>more_horiz</mat-icon>
                      </button>
                      <mat-menu #actionMenu="matMenu" xPosition="before">
                        <a mat-menu-item class="menuAction" target="_blank" [routerLink]="routerLink(uploadFileNames[i])" >
                            <mat-icon>format_size</mat-icon>
                            <span i18n>Xem trước</span>
                          </a>
                          <a mat-menu-item class="menuAction" (click)="downloadFileExport(i)">
                            <mat-icon>cloud_download</mat-icon>
                            <span i18n>Tải xuống tệp tin</span>
                        </a>
                          <a mat-menu-item class="menuAction" (click)="removeItem(i)">
                            <mat-icon>delete_outline</mat-icon>
                            <span i18n>Xóa</span>
                        </a>
                    </mat-menu>
                </div>
            </div>
        </div>

    </div>
</div>

<digo-check-send-notify *ngIf="!notifyQNI" functionType="refuse" [receiveType]="2"></digo-check-send-notify>

<div fxLayout="row" fxLayout.xs="column" fxLayout.sm="column" fxLayoutAlign="center">
    <button mat-flat-button fxFlex='30' class="applyBtn" (click)="onConfirm()" [disabled]="disableButton">
        <span i18n>Đồng ý</span>
    </button>
</div>
