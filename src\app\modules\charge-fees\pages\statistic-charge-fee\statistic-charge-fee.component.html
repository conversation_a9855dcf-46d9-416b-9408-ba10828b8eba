<h2><PERSON><PERSON><PERSON> cáo thu phí</h2>
<div fxLayout="row" fxLayoutAlign="center">
    <div class="frm_searchbar" fxFlex="grow">
        <form class="searchForm" [formGroup]="searchForm" (submit)="onConfirmSearch()">
            <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="row">
                <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                    <mat-label>Từ ngày</mat-label>
                    <input formControlName="fromDateCtrl" matInput [max]="nowDate" [matDatepicker]="picker1"
                           [(ngModel)]="fromDate">
                    <mat-datepicker-toggle matSuffix [for]="picker1"></mat-datepicker-toggle>
                    <mat-datepicker #picker1></mat-datepicker>
                </mat-form-field>

                <div fxFlex='1'></div>

                <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                    <mat-label>Đến ngày</mat-label>
                    <input formControlName="toDateCtrl" matInput [max]="nowDate" [matDatepicker]="picker2"
                           [(ngModel)]="toDate">
                    <mat-datepicker-toggle matSuffix [for]="picker2"></mat-datepicker-toggle>
                    <mat-datepicker #picker2></mat-datepicker>
                </mat-form-field>

                <div fxFlex='1'></div>

                <mat-form-field appearance="outline" fxFlex.gt-sm="48" fxFlex.gt-xs="49.5" fxFlex='grow'>
                    <mat-label>Cơ quan</mat-label>
                    <mat-select msInfiniteScroll (infiniteScroll)="getAgencyList(true)" formControlName="agency" (selectionChange)="getAgencyList(false)"
                                [complete]="isAgencyListFull">
                        <mat-option>
                            <ngx-mat-select-search formControlName="agencyCtrl" placeholderLabel=""
                                                   (keyup)="searchAgencyList()"
                                                   [disableScrollToActiveOnOptionsChanged]="true"
                                                   i18n-noEntriesFoundLabel
                                                   noEntriesFoundLabel="Không tìm thấy kết quả nào!">
                                <mat-icon ngxMatSelectSearchClear (click)="clearAgencyList()">close</mat-icon>
                            </ngx-mat-select-search>
                        </mat-option>
                        <mat-option value="">Tất cả</mat-option>
                        <mat-option *ngFor="let agency of agencyList" value="{{agency.id}}">
                            {{agency.name}}
                            <span *ngIf="agency.name == undefined || agency.name == null" >(Không tìm thấy bản dịch)</span>
                        </mat-option>
                    </mat-select>
                </mat-form-field>

                <div fxFlex='1'></div>

                <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                    <mat-label>Lĩnh vực</mat-label>
                    <mat-select msInfiniteScroll (infiniteScroll)="getSectorList(true)" formControlName="sector" [complete]="isSectorListFull">
                        <mat-option>
                            <ngx-mat-select-search formControlName="sectorCtrl" placeholderLabel="" [disableScrollToActiveOnOptionsChanged]="true"
                                i18n-noEntriesFoundLabel (keyup)="keyupSearch('sector')" noEntriesFoundLabel="Không tìm thấy kết quả nào!">
                                <mat-icon ngxMatSelectSearchClear>close</mat-icon>
                            </ngx-mat-select-search>
                        </mat-option>
                        <mat-option value="">Tất cả</mat-option>
                        <mat-option *ngFor="let sector of sectorList" value="{{sector.id}}">
                            {{sector.name}}
                            <span *ngIf="sector.name == undefined || sector.name == null">(Không tìm thấy bản
                                dịch)</span>
                        </mat-option>
                    </mat-select>
                </mat-form-field>
            </div>
            <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="row" fxLayoutAlign="end">
                <button mat-flat-button fxFlex.gt-sm="12" fxFlex.gt-md="12" fxFlex.gt-xs="49.5" fxFlex='grow' class="btn-download-excel" (click)="exportToExcel()" [disabled]="waitingDownloadExcel">
                    <mat-icon class="iconStatistical">cloud_download</mat-icon>
                    <span i18n="@@exportExcel">Xuất excel</span>
                    <mat-progress-bar mode="indeterminate" *ngIf="waitingDownloadExcel"></mat-progress-bar>
                </button>
                <div fxFlex='1'></div>
                <button type="submit" mat-flat-button fxFlex.gt-sm="12" fxFlex='12' class="btn-search" [disabled]="waitingSearch">
                    <mat-icon class="iconStatistical">bar_chart</mat-icon>
                    <span>Thống kê</span>
                </button>
            </div>

            <div fxLayout="row" fxLayoutAlign="center center">
                <mat-radio-group formControlName='reportType'>
                    <mat-radio-button value="1" (click)="changeMenu(1)">
                        Báo cáo tổng hợp
                    </mat-radio-button>
                    <div fxFlex='1'></div>
                    <mat-radio-button value="2" (click)="changeMenu(2)">
                        Tổng hợp phiếu thu
                    </mat-radio-button>
                    <div fxFlex='1'></div>
                    <mat-radio-button value="3" (click)="changeMenu(3)">
                        Báo cáo chi tiết
                    </mat-radio-button>
                </mat-radio-group>
            </div>
            
            <div fxLayout="row" *ngIf="isTab3">
                <mat-form-field appearance="outline" fxFlex.gt-sm="30" fxFlex.gt-xs="49.5" fxFlex='grow'>
                    <mat-label>
                        Nhập mã hồ sơ 
                        <mat-icon>search</mat-icon>
                    </mat-label>
                    <input matInput formControlName="code" placeholder="Mã hồ sơ" maxlength="500">
                </mat-form-field>
            </div>
        </form>
    </div>
</div>

<div class="frm_tbl0" *ngIf="isTab1 && !!dataSource.data">
    <table mat-table [dataSource]="dataSource">
        <ng-container matColumnDef="No1">
            <th mat-header-cell *matHeaderCellDef [attr.rowspan]="2">STT</th>
        </ng-container>
        <ng-container matColumnDef="No2">
            <th mat-header-cell *matHeaderCellDef [attr.rowspan]="2">Ngày giao dịch</th>
        </ng-container>
        <ng-container matColumnDef="No3">
            <th mat-header-cell *matHeaderCellDef [attr.rowspan]="2">Kênh giao dịch</th>
        </ng-container>
        <ng-container matColumnDef="No4">
            <th mat-header-cell *matHeaderCellDef [attr.rowspan]="2">Loại GD</th>
        </ng-container>
        <ng-container matColumnDef="No5">
            <th mat-header-cell *matHeaderCellDef [attr.colspan]="3">Tổng số giao dịch</th>
        </ng-container>
        <ng-container matColumnDef="No6">
            <th mat-header-cell *matHeaderCellDef [attr.colspan]="2">Tổng số tiền</th>
        </ng-container>
        <ng-container matColumnDef="No7">
            <th mat-header-cell *matHeaderCellDef [attr.rowspan]="2">Giá trị hạch toán vào TK BÊN A tại BÊN B</th>
        </ng-container>

        <ng-container matColumnDef="Num1">
            <th mat-header-cell *matHeaderCellDef [attr.rowspan]="1">Bên A</th>
        </ng-container>
        <ng-container matColumnDef="Num2">
            <th mat-header-cell *matHeaderCellDef [attr.rowspan]="1">Bên A</th>
        </ng-container>
        <ng-container matColumnDef="Num3">
            <th mat-header-cell *matHeaderCellDef [attr.rowspan]="1">Giao dịch thực tế</th>
        </ng-container>
        <ng-container matColumnDef="Num4">
            <th mat-header-cell *matHeaderCellDef [attr.rowspan]="1">Bên A</th>
        </ng-container>
        <ng-container matColumnDef="Num5">
            <th mat-header-cell *matHeaderCellDef [attr.rowspan]="1">Bên B</th>
        </ng-container>

        <ng-container matColumnDef="stt">
            <th mat-header-cell *matHeaderCellDef [ngStyle]="{'display': 'none'}">STT</th>
            <td mat-cell *matCellDef="let row;">{{row?.stt}}</td>
            <td mat-footer-cell *matFooterCellDef></td>
        </ng-container>
        <ng-container matColumnDef="createdDate">
            <th mat-header-cell *matHeaderCellDef [ngStyle]="{'display': 'none'}">Ngày giao dịch</th>
            <td mat-cell *matCellDef="let row;">{{row?.createdDate}}</td>
            <td mat-footer-cell *matFooterCellDef>###</td>
        </ng-container>
        <ng-container matColumnDef="payment">
            <th mat-header-cell *matHeaderCellDef [ngStyle]="{'display': 'none'}">Kênh giao dịch</th>
            <td mat-cell *matCellDef="let row" class="cell_info" style="text-align: left;">
                {{row?.payment}}
            </td>
            <td mat-footer-cell *matFooterCellDef>Tổng cộng</td>
        </ng-container>

        <ng-container matColumnDef="type">
            <th mat-header-cell *matHeaderCellDef [ngStyle]="{'display': 'none'}">Loại giao dịch</th>
            <td mat-cell *matCellDef="let row" class="cell_info"></td>
            <td mat-footer-cell *matFooterCellDef></td>
        </ng-container>
        <ng-container matColumnDef="countA">
            <th mat-header-cell *matHeaderCellDef [ngStyle]="{'display': 'none'}">Bên A</th>
            <td mat-cell *matCellDef="let row" class="cell_info">
               {{row?.countA}}
            </td>
            <td mat-footer-cell *matFooterCellDef class="cell_info">
                {{ sum('countA', ELEMENTDATA) }}
            </td>
        </ng-container>
        <ng-container matColumnDef="countB">
            <th mat-header-cell *matHeaderCellDef [ngStyle]="{'display': 'none'}">Bên B</th>
            <td mat-cell *matCellDef="let row" class="cell_info">
                0
            </td>
            <td mat-footer-cell *matFooterCellDef class="cell_info">
                0
            </td>
        </ng-container>
        <ng-container matColumnDef="actualTransaction">
            <th mat-header-cell *matHeaderCellDef [ngStyle]="{'display': 'none'}">Giao dịch thực tế</th>
            <td mat-cell *matCellDef="let row" class="cell_info"></td>
            <td mat-footer-cell *matFooterCellDef class="cell_info"></td>
        </ng-container>
        <ng-container matColumnDef="sumA">
            <th mat-header-cell *matHeaderCellDef [ngStyle]="{'display': 'none'}">Bên A</th>
            <td mat-cell *matCellDef="let row" class="cell_info">
               {{row?.sumA}}
            </td>
            <td mat-footer-cell *matFooterCellDef class="cell_info">
                {{ sum('sumA', ELEMENTDATA) }}
            </td>
        </ng-container>
        <ng-container matColumnDef="sumB">
            <th mat-header-cell *matHeaderCellDef [ngStyle]="{'display': 'none'}">Bên B</th>
            <td mat-cell *matCellDef="let row" class="cell_info">
                0
            </td>
            <td mat-footer-cell *matFooterCellDef class="cell_info">
                0
            </td>
        </ng-container>
        <ng-container matColumnDef="value">
            <th mat-header-cell *matHeaderCellDef [ngStyle]="{'display': 'none'}">Giá trị hạch toán vào TK BÊN A tại BÊN B</th>
            <td mat-cell *matCellDef="let row" class="cell_info"></td>
            <td mat-footer-cell *matFooterCellDef class="cell_info"></td>
        </ng-container>

        <tr mat-header-row *matHeaderRowDef="['No1', 'No2', 'No3', 'No4', 'No5', 'No6', 'No7']"></tr>
        <tr mat-header-row *matHeaderRowDef="['Num1', 'Num2', 'Num3', 'Num4', 'Num5']"></tr>
        <tr mat-row *matRowDef="let row; columns: displayedColumns_1;"></tr>
        <tr mat-footer-row *matFooterRowDef="displayedColumns_1; sticky: true"></tr>
    </table>
</div>

<div class="frm_tbl0" *ngIf="isTab2 && !!dataSource1.data">
    <table mat-table [dataSource]="dataSource1">
      <ng-container matColumnDef="A1">
          <th mat-header-cell *matHeaderCellDef>STT</th>
      </ng-container>
      <ng-container matColumnDef="A2">
          <th mat-header-cell *matHeaderCellDef>Cơ quan</th>
      </ng-container>
      <ng-container matColumnDef="A3">
          <th mat-header-cell *matHeaderCellDef>Tổng số giao dịch</th>
      </ng-container>
      <ng-container matColumnDef="A4">
          <th mat-header-cell *matHeaderCellDef>Tổng số tiền</th>
      </ng-container>
      <ng-container matColumnDef="A5">
          <th mat-header-cell *matHeaderCellDef>Ghi chú</th>
      </ng-container>

      <ng-container matColumnDef="B1">
        <th mat-header-cell *matHeaderCellDef>(1)</th>
      </ng-container>
      <ng-container matColumnDef="B2">
        <th mat-header-cell *matHeaderCellDef>(2)</th>
      </ng-container>
      <ng-container matColumnDef="B3">
        <th mat-header-cell *matHeaderCellDef>(3)</th>
      </ng-container>
      <ng-container matColumnDef="B4">
        <th mat-header-cell *matHeaderCellDef>(4)</th>
      </ng-container>
      <ng-container matColumnDef="B5">
        <th mat-header-cell *matHeaderCellDef>(5)</th>
      </ng-container>
  
      <ng-container matColumnDef="stt1">
        <td mat-cell *matCellDef="let row;" class="cell_info">{{row?.stt1}}</td>
        <td mat-footer-cell *matFooterCellDef></td>
      </ng-container>

      <ng-container matColumnDef="agencyName">
        <td mat-cell *matCellDef="let row;" class="cell_info">{{row?.agencyName}}</td>
        <td mat-footer-cell *matFooterCellDef class="cell_info">Tổng cộng</td>
      </ng-container>

      <ng-container matColumnDef="count">
        <td mat-cell *matCellDef="let row;" class="cell_info">{{row?.count}}</td>
        <td mat-footer-cell *matFooterCellDef class="cell_info">
            {{ sum('count', ELEMENTDATA1) }}
        </td>
      </ng-container>

      <ng-container matColumnDef="sum">
        <td mat-cell *matCellDef="let row;" class="cell_info">{{row?.sum}}</td>
        <td mat-footer-cell *matFooterCellDef class="cell_info">
            {{ sum('sum', ELEMENTDATA1) }}
        </td>
      </ng-container>

      <ng-container matColumnDef="note">
        <td mat-cell *matCellDef="let row;" class="cell_info">{{row?.note}}</td>
        <td mat-footer-cell *matFooterCellDef></td>
      </ng-container>

      <tr mat-header-row *matHeaderRowDef="['A1', 'A2', 'A3', 'A4', 'A5']"></tr>
      <tr mat-header-row *matHeaderRowDef="['B1', 'B2', 'B3', 'B4', 'B5']"></tr>
      <tr mat-row *matRowDef="let row; columns: displayedColumns_2;"></tr>
      <tr mat-footer-row *matFooterRowDef="displayedColumns_2; sticky: true"></tr>

    </table>
</div>

<div class="frm_tbl0" *ngIf="isTab3 && !!dataSource2.data">
    <table mat-table [dataSource]="dataSource2">
      <ng-container matColumnDef="stt2" id="stt2">
        <mat-header-cell *matHeaderCellDef > STT </mat-header-cell>
        <mat-cell *matCellDef="let row2" [style.width.px]="10"> {{row2?.stt2}} </mat-cell>
      </ng-container>

      <ng-container matColumnDef="dossierCode">
        <mat-header-cell *matHeaderCellDef > Mã khách hàng </mat-header-cell>
        <mat-cell *matCellDef="let row2"> {{row2?.dossierCode}} </mat-cell>
      </ng-container>

      <ng-container matColumnDef="payment">
        <mat-header-cell *matHeaderCellDef > Kênh giao dịch </mat-header-cell>
        <mat-cell *matCellDef="let row2"> {{row2?.payment}}</mat-cell>
      </ng-container>

      <ng-container matColumnDef="type">
        <mat-header-cell *matHeaderCellDef > Loại giao dịch </mat-header-cell>
        <mat-cell *matCellDef="let row2"></mat-cell>
      </ng-container>

      <ng-container matColumnDef="code">
        <mat-header-cell *matHeaderCellDef > Mã giao dịch </mat-header-cell>
        <mat-cell *matCellDef="let row2"></mat-cell>
      </ng-container>

      <ng-container matColumnDef="createdDate">
        <mat-header-cell *matHeaderCellDef > Ngày giao dịch </mat-header-cell>
        <mat-cell *matCellDef="let row2"> {{row2?.createdDate}} </mat-cell>
      </ng-container>

      <ng-container matColumnDef="time">
          <mat-header-cell *matHeaderCellDef > Giờ giao dịch </mat-header-cell>
          <mat-cell *matCellDef="let row2"> {{row2?.time}} </mat-cell>
      </ng-container>

      <ng-container matColumnDef="accountingCode">
          <mat-header-cell *matHeaderCellDef > Mã hạch toán </mat-header-cell>
          <mat-cell *matCellDef="let row2"></mat-cell>
      </ng-container>

      <ng-container matColumnDef="documentNumber">
          <mat-header-cell *matHeaderCellDef > Số chứng từ </mat-header-cell>
          <mat-cell *matCellDef="let row2"></mat-cell>
        </ng-container>

        <ng-container matColumnDef="sum">
            <mat-header-cell *matHeaderCellDef > Số tiền giao dịch </mat-header-cell>
            <mat-cell *matCellDef="let row2"> {{row2?.sum}} </mat-cell>
        </ng-container>

        <ng-container matColumnDef="status">
            <mat-header-cell *matHeaderCellDef > Tình trạng giao dịch </mat-header-cell>
            <mat-cell *matCellDef="let row2"> Đã thu </mat-cell>
        </ng-container>

      <mat-header-row *matHeaderRowDef="displayedColumns_3"></mat-header-row>
      <mat-row *matRowDef="let row2; columns: displayedColumns_3;"></mat-row>
    </table>

    <div class="frm_Pagination">
        <ul class="temp_Arr">
          <li *ngFor="let item of ELEMENTDATA2  | paginate: {itemsPerPage: size, currentPage: page, totalItems: countResult, id: 'pgnx'}"></li>
        </ul>
        <div class="pageSize">
          <span >Hiển thị </span>
          <mat-form-field appearance="outline">
            <mat-select [(value)]="size" (valueChange)="paginate(pageIndex, 1)">
              <mat-option *ngFor='let opt of pgSizeOptions;' [value]="opt">{{opt}}</mat-option>
            </mat-select>
          </mat-form-field>
          <span >trên </span> {{countResult}} <span >bản ghi</span>
        </div>
        <div class="control">
          <pagination-controls id="pgnx" (pageChange)="page = $event; paginate(page, 0)" responsive="true"
                               previousLabel="" nextLabel="">
          </pagination-controls>
        </div>
    </div>
</div>