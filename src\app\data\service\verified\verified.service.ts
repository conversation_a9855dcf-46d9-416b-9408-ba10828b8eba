import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { ApiProviderService } from 'src/app/core/service/api-provider.service';
import { Observable } from 'rxjs';
import { EnvService } from 'src/app/core/service/env.service';

@Injectable({
  providedIn: 'root'
})
export class VerifiedService {

  config = this.envService.getConfig();
  constructor(
    private envService: EnvService,
    private http: HttpClient,
    private apiProviderService: ApiProviderService
  ) { }

  private human = this.apiProviderService.getUrl('digo', 'human');
  private adapter = this.apiProviderService.getUrl('digo', 'adapter');
  private fileman = this.apiProviderService.getUrl('digo', 'fileman');
  private getUserInfoPath = this.apiProviderService.getUrl('digo', 'human') + '/user/';

  getUserInfo(id: string): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.getUserInfoPath + id, { headers });
  }

  getUserExperience(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.human + '/user/' + id + '/experience', { headers });
  }

  getFileDetail(id): Observable<any> {
    return this.http.get(this.fileman + '/file/' + id + '/filename+size');
  }

  digitalSignature(data): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));

    const formData: FormData = new FormData();
    formData.append('agencyId', data.agencyId);
    formData.append('subsystemId', data.subsystemId);
    formData.append('fileId', data.fileId);
    if (Number(data.signType) !== 0) {
      formData.append('imageId', data.imageId);
    }
    // formData.append('imageId', data.imageId);
    formData.append('signType', data.signType);
    formData.append('phone', data.phone);
    formData.append('messageDisplay', data.messageDisplay);
    formData.append('reason', data.reason);
    formData.append('location', data.location);
    formData.append('signPosition', data.signPosition);
    formData.append('textSearch', data.textSearch);
    formData.append('autoSign', data.autoSign);
    formData.append('signPage', data.signPage);
    formData.append('side', "BOTTOM");
    formData.append('width', "70");
    formData.append('height', "35");
    return this.http.post<any>(this.adapter + '/digital-signature', formData, { headers });
  }

  signDocument(data): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));

    const formData: FormData = new FormData();
    formData.append('agencyId', data.agencyId);
    formData.append('subsystemId', data.subsystemId);
    formData.append('fileId', data.fileId);
    if (Number(data.signType) !== 0) {
      formData.append('imageId', data.imageId);
    }
    // formData.append('imageId', data.imageId);
    formData.append('signType', data.signType);
    formData.append('phone', data.phone);
    formData.append('messageDisplay', data.messageDisplay);
    formData.append('reason', data.reason);
    formData.append('location', data.location);
    formData.append('signPosition', data.signPosition);
    formData.append('textSearch', data.textSearch);
    formData.append('autoSign', data.autoSign);
    formData.append('signPage', data.signPage);
    formData.append('side', "BOTTOM");
    formData.append('width', "70");
    formData.append('height', "35");
    if(data?.msspProvider) formData.append('msspProvider', data?.msspProvider);
    if(data?.dossierId) formData.append('dossierId', data?.dossierId);
    return this.http.post<any>(this.adapter + '/digital-signature/--sign', formData, { headers });
  }

  fileGetLink(id, dossierId?): Promise<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.post<any>(this.fileman + '/file/get-link?id=' + id + '&dossierId=' + dossierId, null, { headers, responseType: 'text' as 'json' }).toPromise();
  }

}
