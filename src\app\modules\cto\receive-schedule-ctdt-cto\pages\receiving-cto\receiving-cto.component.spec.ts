import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { ReceivingCtdtComponent } from './receiving-cto.component';

describe('ReceivingComponent', () => {
  let component: ReceivingCtdtComponent;
  let fixture: ComponentFixture<ReceivingCtdtComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ ReceivingCtdtComponent ]
    })
      .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(ReceivingCtdtComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
