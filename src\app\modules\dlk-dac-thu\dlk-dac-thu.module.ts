import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DlkDacThuRoutingModule } from './dlk-dac-thu-routing.module';
import { SharedModule } from 'src/app/shared/shared.module';
import { ChiThi18Component } from './pages/chithi18/chithi18.component';
import { ChiThi18CtComponent } from './dialogs/chithi18-ct/chithi18-ct.component';
import { DlkSignApologyTextComponent } from './dialogs/dlk-sign-apology-text/dlk-sign-apology-text.component';
import { BaocaoChithi08DialogComponent } from './dialogs/baocao-chithi08-dialog/baocao-chithi08-dialog.component';

@NgModule({
  declarations: [
    ChiThi18Component, ChiThi18CtComponent, DlkSignApologyTextComponent, BaocaoChithi08DialogComponent
  ],
  imports: [
    CommonModule,
    DlkDacThuRoutingModule,
    SharedModule,
  ]
})
export class DLKDacThuModule { }