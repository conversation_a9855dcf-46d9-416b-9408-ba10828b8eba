import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { LGSPHCMLogComponent } from './pages/lgsp-hcm-log.component';


const routes: Routes = [
  {
    path: '',
    children: [
      { path: '', component: LGSPHCMLogComponent },
    ]
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class LGSPHCMLogRoutingModule { }
