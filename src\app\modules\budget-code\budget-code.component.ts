import { DatePipe } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { FormControl, FormGroup } from '@angular/forms';
import { MatDatepickerInputEvent } from '@angular/material/datepicker';
import { MatTableDataSource } from '@angular/material/table';
import { EnvService } from 'src/app/core/service/env.service';
import { DeploymentService } from 'src/app/data/service/deployment.service';
import { SnackbarService } from 'src/app/data/service/snackbar/snackbar.service';
import { AdapterService } from 'src/app/data/service/svc-adapter/adapter.service';
import * as tUtils from 'src/app/data/service/thoai.service';

@Component({
  selector: 'app-budget-code',
  templateUrl: './budget-code.component.html',
  styleUrls: ['./budget-code.component.scss']
})
export class BudgetCodeComponent implements OnInit {

  config = this.envService.getConfig();
  env = this.deploymentService.getAppDeployment()?.env;

  selectedLang = localStorage.getItem('language') || 'vi';
  selectedLangId = Number(localStorage.getItem('languageId')) || 228;

  budgetCodeConfigId = this.env?.budgetCodeConfigId || this.config.budgetCodeConfigId;
  msnsLGSPHCM = this.env?.OS_HCM?.msnsLGSPHCM || false;

  type = -1;
  selected = -1;

  todayDate = tUtils.newDate();
  maxToDate = new Date();
  // Search
  searchForm = new FormGroup({
    id: new FormControl(''),
    fromDateDL: new FormControl(new Date(this.todayDate.getTime() - 4 * 86400000)),
    toDateDL: new FormControl(this.todayDate),
    fromDateDBP: new FormControl(new Date(this.todayDate.getTime() - 6 * 86400000)),
    toDateDBP: new FormControl(this.todayDate),
  });

  // Table
  displayedColumnsDL: string[] = ['hsid', 'ten_hs', 'ngay_dk', 'email', 'nguoi_dk', 'sdt_didong', 'kieu_tiep_nhan', 'trang_thai'];
  ELEMENTDATADL: any[] = [];
  dataSourceDL: MatTableDataSource<any>;

  displayedColumnsDBP: string[] = ['cqtc_ten', 'hso_bo_sung', 'hso_cho_dung_han', 'hso_cho_qua_han', 'hso_dung_han', 'hso_ky_truoc', 'hso_qua_han', 'hso_trong_ky', 'hso_truoc_han'];
  ELEMENTDATADBP: any[] = [];
  dataSourceDBP: MatTableDataSource<any>;

  budgetDossierDetail: any;

  sumDate = 5;

  constructor(
    private adapterService: AdapterService,
    private deploymentService: DeploymentService,
    private envService: EnvService,
    private datepipe: DatePipe,
    private snackbarService: SnackbarService,
  ) {
    this.dataSourceDL = new MatTableDataSource(this.ELEMENTDATADL);
    this.dataSourceDBP = new MatTableDataSource(this.ELEMENTDATADBP);
  }

  ngOnInit(): void { }

  onClickSearch(type) {
    const formObj = this.searchForm.getRawValue();
    this.type = type;
    switch (type) {
      case 0: {
        const msgObj = {
          vi: 'Vui lòng lựa chọn thời gian từ ngày đến ngày.',
          en: 'Please choose "from date" and "to date" to find the result.'
        };
        const msgObj1 = {
          vi: 'Vui lòng lựa chọn từ ngày nhỏ hơn đến ngày.',
          en: 'Please choose "from date" less than "to date".'
        };
        const msgObj2 = {
          vi: 'Vui lòng lựa chọn khoảng thời gian từ ngày và đến ngày không quá 5 ngày.',
          en: 'Please select a period between "from date" and "to date" does not exceed 5 days.'
        };
        if (!formObj.fromDateDL || !formObj.toDateDL) {
          this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
        } else {
          if (formObj.fromDateDL > formObj.toDateDL) {
            this.snackbarService.openSnackBar(0, msgObj1[this.selectedLang], '', 'error_notification', this.config.expiredTime);
          } else {
            if ((formObj.toDateDL - formObj.fromDateDL) / (1000 * 60 * 60 * 24) > 5) {
              this.snackbarService.openSnackBar(0, msgObj2[this.selectedLang], '', 'error_notification', this.config.expiredTime);
            } else {
              if(this.msnsLGSPHCM == true){
                this.executeSearchLGSP(type);
              }
              else{
                this.executeSearch(type);
              }
            }
          }
        }
        break;
      }
      case 1: {
        if (!formObj.id) {
          const msgObj = {
            vi: 'Vui lòng nhập mã hồ sơ!',
            en: 'Please enter dossier code!'
          };
          this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
        } else {
          if (formObj.id.length <= 15) {
            if(this.msnsLGSPHCM == true){
              this.executeSearchLGSP(type);
            }
            else{
              this.executeSearch(type);
            }
          } else {
            const msgObj = {
              vi: 'Vui lòng nhập ít hơn 15 ký tự.',
              en: 'Please enter less than 15 character.'
            };
            this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
          }
        }
        break;
      }
      case 2: {
        const msgObj = {
          vi: 'Vui lòng lựa chọn thời gian bắt đầu và kết thúc.',
          en: 'Please select a start and end time.'
        };
        const msgObj1 = {
          vi: 'Vui lòng lựa chọn thời gian bắt đầu nhỏ hơn thời gian kết thúc.',
          en: 'Please select the start time to be less than the end time.'
        };

        if (!formObj.fromDateDBP || !formObj.toDateDBP) {
          this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
        } else {
          //Bỏ time ra khỏi ngày tháng
          var fromDate = new Date(formObj.fromDateDBP.getTime());
          fromDate.setHours(0, 0, 0, 0); 
          var toDate = new Date(formObj.toDateDBP.getTime());
          toDate.setHours(0, 0, 0, 0);
          if (fromDate > toDate) {
            this.snackbarService.openSnackBar(0, msgObj1[this.selectedLang], '', 'error_notification', this.config.expiredTime);
          } else {
            if(this.msnsLGSPHCM == true){
              this.executeSearchLGSP(type);
            }
            else{
              this.executeSearch(type);
            }
          }
        }
        break;
      }
    }
  }

  executeSearch(type) {
    const formObj = this.searchForm.getRawValue();
    //const config = `?config-id=${this.budgetCodeConfigId}`;
    const config = '?agency-id=' + this.config.rootAgency.id + '&subsystem-id=' + this.config.subsystemId;
    switch (type) {
      case 0: {
        const fromDate = this.datepipe.transform(formObj.fromDateDL, 'ddMMyyyy').toString();
        const toDate = this.datepipe.transform(formObj.toDateDL, 'ddMMyyyy').toString();

        this.adapterService.getBudgetDossierList(config, fromDate, toDate).subscribe(data => {
          this.ELEMENTDATADL = data.content;
          this.dataSourceDL.data = this.ELEMENTDATADL;
        }, err => {
          let msgObj = err.error.message || 'Lỗi không xác định';
          if (msgObj.includes('java.lang.NullPointerException')) {
            msgObj = {
              vi: 'Không tìm thấy hồ sơ',
              en: 'Dossier not found'
            };
            this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
            return;
          }
          this.snackbarService.openSnackBar(0, msgObj, '', 'error_notification', this.config.expiredTime);
        });
        break;
      }
      case 1: {
        this.adapterService.getBudgetDossierDetail(config, formObj.id).subscribe(data => {
          this.budgetDossierDetail = data;
        }, err => {
          this.budgetDossierDetail = null;

          let msgObj = err.error.message || 'Lỗi không xác định';
          if (msgObj.includes('java.lang.NullPointerException')) {
            msgObj = {
              vi: 'Không tìm thấy hồ sơ',
              en: 'Dossier not found'
            };
            this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
            return;
          }
          this.snackbarService.openSnackBar(0, msgObj, '', 'error_notification', this.config.expiredTime);
        });
        break;
      }
      case 2: {
        const fromDate = this.datepipe.transform(formObj.fromDateDBP, 'ddMMyyyy').toString();
        const toDate = this.datepipe.transform(formObj.toDateDBP, 'ddMMyyyy').toString();

        this.adapterService.getBudgetDossierByPeriod(config, fromDate, toDate).subscribe(data => {
          const tempArr = [];
          tempArr.push(data);
          this.dataSourceDBP.data = tempArr;
        }, err => {
          const msgObj = err.error.message || 'Lỗi không xác định';
          this.snackbarService.openSnackBar(0, msgObj, '', 'error_notification', this.config.expiredTime);
        });
        break;
      }
    }
  }

  executeSearchLGSP(type) {
    const formObj = this.searchForm.getRawValue();
    //const config = `?config-id=${this.budgetCodeConfigId}`;
    const config = '?agency-id=' + this.config.rootAgency.id + '&subsystem-id=' + this.config.subsystemId;
    switch (type) {
      case 0: {
        const fromDate = this.datepipe.transform(formObj.fromDateDL, 'ddMMyyyy').toString();
        const toDate = this.datepipe.transform(formObj.toDateDL, 'ddMMyyyy').toString();
        this.onChangeDateDL();
        this.adapterService.getBudgetDossierListLGSP(config, fromDate, toDate).subscribe(data => {
          this.ELEMENTDATADL = data;
          this.dataSourceDL.data = this.ELEMENTDATADL;
        }, err => {
          let msgObj = err.error.message || 'Lỗi không xác định';
          if (msgObj.includes('java.lang.NullPointerException')) {
            msgObj = {
              vi: 'Không tìm thấy hồ sơ',
              en: 'Dossier not found'
            };
            this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
            return;
          }
          this.snackbarService.openSnackBar(0, msgObj, '', 'error_notification', this.config.expiredTime);
        });
        break;
      }
      case 1: {
        this.adapterService.getBudgetDossierDetailLGSP(config, formObj.id).subscribe(data => {
          this.budgetDossierDetail = data;
        }, err => {
          this.budgetDossierDetail = null;

          let msgObj = err.error.message || 'Lỗi không xác định';
          if (msgObj.includes('java.lang.NullPointerException')) {
            msgObj = {
              vi: 'Không tìm thấy hồ sơ',
              en: 'Dossier not found'
            };
            this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
            return;
          }
          this.snackbarService.openSnackBar(0, msgObj, '', 'error_notification', this.config.expiredTime);
        });
        break;
      }
      case 2: {
        const fromDate = this.datepipe.transform(formObj.fromDateDBP, 'ddMMyyyy').toString();
        const toDate = this.datepipe.transform(formObj.toDateDBP, 'ddMMyyyy').toString();

        this.adapterService.getBudgetDossierByPeriodLGSP(config, fromDate, toDate).subscribe(data => {
          const tempArr = [];
          tempArr.push(data);
          this.dataSourceDBP.data = tempArr;
        }, err => {
          const msgObj = err.error.message || 'Lỗi không xác định';
          this.snackbarService.openSnackBar(0, msgObj, '', 'error_notification', this.config.expiredTime);
        });
        break;
      }
    }
  }

  onChangeType(id) {
    this.selected = id.value;
    this.type = -1;
    this.dataSourceDL.data = null;
    this.budgetDossierDetail = null;
    this.dataSourceDBP.data = null;
  }

  onChangeDateDL() {
    const formObj = this.searchForm.getRawValue();
    this.sumDate = (Math.floor((formObj.toDateDL - formObj.fromDateDL) / (1000 * 60 * 60 * 24)) + 1);
  }
  
  triggerToDate(event: MatDatepickerInputEvent<Date> ){
    this.maxToDate = new Date(event.value.getTime() + 4 * 86400000);
    let toDateOld = this.searchForm.controls["toDateDL"].value;
    let diffDate = Math.round((toDateOld.getTime() - event.value.getTime())/86400000);
    let diffToDate = Math.round((this.maxToDate.getTime() - new Date().getTime())/86400000);

    if (diffToDate >= 0) {
      this.maxToDate = new Date();
    }
    
    if ( diffDate < 5 && diffDate >= 0 ) {
      this.searchForm.controls["toDateDL"].setValue(toDateOld);
    }
    else if(diffToDate > 0) {
      this.searchForm.controls["toDateDL"].setValue(new Date());
    }else{
      this.searchForm.controls["toDateDL"].setValue(new Date(event.value.getTime() + 4 * 86400000));
    }
  }
  
}
