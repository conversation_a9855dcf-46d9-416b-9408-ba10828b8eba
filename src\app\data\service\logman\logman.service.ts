import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { ApiProviderService } from 'src/app/core/service/api-provider.service';
import { Observable } from 'rxjs';
import { EnvService } from 'src/app/core/service/env.service';
import { SnackbarService } from '../snackbar/snackbar.service';
import { Workbook } from 'exceljs';
import * as fs from 'file-saver';
const EXCEL_TYPE = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8';
const EXCEL_EXTENSION = '.xlsx';

@Injectable({
  providedIn: 'root'
})
export class LogmanService {

  config = this.envService.getConfig();
  constructor(
    private envService: EnvService,
    private http: HttpClient,
    private apiProviderService: ApiProviderService,
    private snackbar: SnackbarService,
  ) { }

  private logmanUrl = this.apiProviderService.getUrl('digo', 'logman');

  // Xuất file excel
  excelExport(data: string): any {
    return new Promise((resolve) => {
      this.downloadExport(data).then(res => {
        const blob = new Blob([res.body], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
        let filename = 'danh-sach-ho-so-ghi-log.xlsx';
        if (res.headers.get('content-disposition') != null) {
          filename = res.headers.get('content-disposition').split(';')[1].split('=')[1].replace(/\"/g, '');
        }
        var blobUrl = URL.createObjectURL(blob);

        var xhr = new XMLHttpRequest;
        xhr.responseType = 'blob';

        xhr.onload = function () {
          var recoveredBlob = xhr.response;
          var reader = new FileReader;
          reader.onload = function () {
            var base64data = reader.result.toString();
            var object = JSON.stringify({
              mimeType: filename.substring(filename.lastIndexOf(".") + 1),
              name: filename.substring(0, filename.lastIndexOf(".")),
              data: base64data.split(',')[1]
            });
            let anchor = document.createElement("a");
            anchor.download = filename;
            anchor.href = base64data;
            anchor.click();
          };
          reader.readAsDataURL(recoveredBlob);
        };

        xhr.open('GET', blobUrl);
        xhr.send();
        resolve(true);
      })
        .catch(err => {
          if (err.status === 500) {
            this.snackbar.openSnackBar(0, '', 'Hệ thống tạm thời không thể xuất file excel!', 'error_notification', this.config.expiredTime);
          }
          resolve(false)
        });
    })

  }
  downloadExport(requestBody) {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.logmanUrl + "/dossier-log/--export-dossier-log" + requestBody , { headers, observe: 'response', responseType: 'blob' }).toPromise();
  }
  getStatictisDossierLog(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.logmanUrl + '/dossier-log/--statistic-dossier-log' + searchString, { headers }).pipe();
  }

  getAllHistory(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.logmanUrl + '/history/--dossier-by-item-id' + searchString, { headers }).pipe();
  }


  postHistory(itemId, newValue, oldValue){
    const userId = localStorage.getItem("tempUID");
    const fullname = localStorage.getItem("tempUsername");
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));

    const requestBody = {
      user : {
        id : userId,
        name : fullname
      },
      itemId : itemId,
      groupId : 1,
      type: 1,
      action : [
        {
          "fieldNameRbk": "lang.word.status",
          "originalValue": oldValue,
          "newValue": newValue
        }
      ]
    };
    return this.http.post(this.logmanUrl + `/history`, requestBody,{ headers });
  }


  postSignHistory(itemId, newValue){
    const userId = localStorage.getItem("tempUID");
    const fullname = localStorage.getItem("tempUsername");
    const agency = JSON.parse(localStorage.getItem("userAgency"));
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    const originalObj = JSON.stringify({
      agency : {
        id: agency.id,
        name: agency.name
      },
      owner : 1
    });
    const requestBody = {
      user : {
        id : userId,
        name : fullname
      },
      itemId : itemId,
      groupId : 1,
      type: 1,
      action : [
        {
          "fieldNameRbk": "lang.word.status",
          "originalValue": originalObj,
          "newValue": newValue
        }
      ]
    };
    return this.http.post(this.logmanUrl + `/history`, requestBody,{ headers });
  }

  postUserEventsLog(eventsType, data?): Observable<any> {
    const requestBody = {
      eventsType,
      ipAddress: localStorage.getItem('clientIP'),
      browser: this.getBrowserName(),
      url: this.getUrlAddress(),
      data
    };
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    if (localStorage.getItem('isLoggedIn') === 'false') {
      const token = localStorage.getItem('OAuth2TOKEN');
      headers = headers.append('Authorization', 'Bearer ' + token);
    }
    return this.http.post(this.logmanUrl + '/user-events-log', requestBody, { headers });
  }

  getBrowserName() {
    const agent = window.navigator.userAgent.toLowerCase();
    switch (true) {
      case agent.indexOf('edge') > -1:
        return 'edge';
      case agent.indexOf('opr') > -1 && !!(<any>window).opr:
        return 'opera';
      case agent.indexOf('chrome') > -1 && !!(<any>window).chrome:
        return 'chrome';
      case agent.indexOf('trident') > -1:
        return 'ie';
      case agent.indexOf('firefox') > -1:
        return 'firefox';
      case agent.indexOf('safari') > -1:
        return 'safari';
      default:
        return 'other';
    }
  }

  getUrlAddress(){
    return window.location.href;
  }

  getUserEventsLogReceptionDossier(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    //return this.http.get('http://localhost:8089/kha/user-events-log-reception-dossier' + searchString, { headers }).pipe();
    return this.http.get(this.logmanUrl + '/kha/user-events-log-reception-dossier' + searchString, { headers }).pipe();
  }
  getAllUserEventsLogReceptionDossier(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    //return this.http.get('http://localhost:8089/kha/user-events-log-reception-dossier/get-all' + searchString, { headers }).pipe();
    return this.http.get(this.logmanUrl + '/kha/user-events-log-reception-dossier/get-all' + searchString, { headers }).pipe();
  }
  postUserEventsLogReceptionDossier(eventsType, data?): Observable<any> {
    const requestBody = {
      eventsType,
      ipAddress: localStorage.getItem('clientIP'),
      browser: this.getBrowserName(),
      url: this.getUrlAddress(),
      data
    };
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    if (localStorage.getItem('isLoggedIn') === 'false') {
      const token = localStorage.getItem('OAuth2TOKEN');
      headers = headers.append('Authorization', 'Bearer ' + token);
    }
    //return this.http.post('http://localhost:8089' + '/kha/user-events-log-reception-dossier', requestBody, { headers });
    return this.http.post(this.logmanUrl + '/kha/user-events-log-reception-dossier', requestBody, { headers });
  }

  getListUserEventLog(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.logmanUrl + '/user-events-log/--by-user-id' + searchString, { headers }).pipe();
  }
  exportUserEventLog(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.logmanUrl + '/user-events-log/--export-log-by-user-id' + searchString, { headers }).pipe();
  }
   public exportAsExcelLogUserAction(
      reportHeading: string,
      reportSubHeading: string,
      userTitle:string,
      json: any[],
      excelFileName: string,
      sheetName: string,
      isDetail: boolean = false
    ) {
      const data = json;
      console.log('json',data)
      // create workbook and worksheet
      const workbook = new Workbook();
      workbook.creator = 'Snippet Coder';
      workbook.lastModifiedBy = 'SnippetCoder';
      workbook.created = new Date();
      workbook.modified = new Date();
      const worksheet = workbook.addWorksheet(sheetName);
  
      worksheet.getColumn('A').font = {name: 'Times New Roman', size: 12};
      worksheet.getColumn('B').font = {name: 'Times New Roman', size: 12};
      worksheet.getColumn('C').font = {name: 'Times New Roman', size: 12};
      worksheet.getColumn('D').font = {name: 'Times New Roman', size: 12};
      worksheet.getColumn('E').font = {name: 'Times New Roman', size: 12};
      worksheet.getColumn('F').font = {name: 'Times New Roman', size: 12};
      worksheet.getColumn('G').font = {name: 'Times New Roman', size: 12};
      worksheet.getColumn('H').font = {name: 'Times New Roman', size: 12};
  
      worksheet.getColumn('A').alignment = { horizontal: 'center', vertical: 'middle' };
      worksheet.getColumn('B').alignment = { horizontal: 'center', vertical: 'middle' };
      worksheet.getColumn('C').alignment = { horizontal: 'center', vertical: 'middle' };
      worksheet.getColumn('D').alignment = { horizontal: 'center', vertical: 'middle' };
      worksheet.getColumn('E').alignment = { horizontal: 'center', vertical: 'middle' };
      worksheet.getColumn('F').alignment = { horizontal: 'center', vertical: 'middle' };
      worksheet.getColumn('G').alignment = { horizontal: 'center', vertical: 'middle' };
      worksheet.getColumn('H').alignment = { horizontal: 'center', vertical: 'middle' };
  
      // worksheet.getColumn('A').width = 30;
      worksheet.getColumn('B').width = 40;
      worksheet.getColumn('C').width = 20;
      worksheet.getColumn('D').width = 20;
      worksheet.getColumn('E').width = 20;
      worksheet.getColumn('F').width = 20;
      worksheet.getColumn('G').width = 20;
      worksheet.getColumn('H').width = 20;
  
      // Add header row
      worksheet.addRow([]);
      worksheet.mergeCells('A1:H1');
      worksheet.getCell('A1').value = reportHeading;
      worksheet.getCell('A1').alignment = { horizontal: 'center', vertical: 'middle' };
  
      worksheet.addRow([]);
      worksheet.mergeCells('A2:H2');
      worksheet.getCell('A2').value = reportSubHeading;
      worksheet.getCell('A2').alignment = { horizontal: 'center', vertical: 'middle' };
      worksheet.getCell('A2').font = { size: 12, italic: true, name: 'Times New Roman' };

      worksheet.addRow([]);
      worksheet.mergeCells('A3:H3');
      worksheet.getCell('A3').value = userTitle;
      worksheet.getCell('A3').alignment = { horizontal: 'center', vertical: 'middle' };
      worksheet.getCell('A3').font = { size: 12, italic: true, name: 'Times New Roman' };

      if(!isDetail){
      worksheet.getCell('A5').value = 'Stt';
      worksheet.getCell('B5').value = 'IP của máy truy cập';
      worksheet.getCell('C5').value = 'Tài khoản người sử dụng';
      worksheet.getCell('D5').value = 'Họ tên người sử dụng';
      worksheet.getCell('E5').value = 'Ngày giờ thao tác';
      }else{
        worksheet.getCell('A5').value = 'Stt';
        worksheet.getCell('B5').value = 'Ngày giờ thao tác';
        worksheet.getCell('C5').value = 'Chức năng thao tác';
        worksheet.getCell('D5').value = 'Liên kết';
        worksheet.getCell('E5').value = 'Ứng dụng';
      }
  
      worksheet.properties.outlineLevelCol = 2; worksheet.properties.defaultRowHeight = 15;
      let i = 5;
      const j = 5;
      for (i; i <= j; i++) {
        let k = 1;
        const l = 5;
        for (k; k <= l; k++) {
          worksheet.findCell(i, k).alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
          worksheet.findCell(i, k).fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: 'C0C0C0C0' },
            bgColor: { argb: 'FF0000FF' }
          };
          worksheet.findCell(i, k).border = { top: { style: 'thin' }, left: { style: 'thin' }, bottom: { style: 'thin' }, right: { style: 'thin' } };
          worksheet.findCell(i, k).font = { size: 12, bold: true, name: 'Times New Roman' };
        }
      }
  
      // get all columns from JSON
      let columnsArray: any[];
      for (const key in json) {
        if (json.hasOwnProperty(key)) {
          columnsArray = Object.keys(json[key]);
        }
      }
      // Add Data and Conditional Formatting
      data.forEach((element: any) => {
        const eachRow = [];
        columnsArray.forEach((column) => {
          eachRow.push(element[column]);
        });
        const borderrow = worksheet.addRow(eachRow);
          borderrow.eachCell((cell) => {
            cell.border = { top: { style: 'thin' }, left: { style: 'thin' }, bottom: { style: 'thin' }, right: { style: 'thin' } };
            cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
          });
      });
  
      // Save Excel File
      // tslint:disable-next-line:no-shadowed-variable
      workbook.xlsx.writeBuffer().then((data: ArrayBuffer) => {
        const blob = new Blob([data], { type: EXCEL_TYPE });
        fs.saveAs(blob, excelFileName + EXCEL_EXTENSION);
      });
    }
}
