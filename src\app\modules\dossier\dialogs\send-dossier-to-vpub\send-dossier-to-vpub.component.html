<button mat-icon-button class="close-button" (click)="onDismiss()">
    <mat-icon>close</mat-icon>
</button>
<h3 class="dialog_title" mat-dialog-title ><PERSON><PERSON><PERSON> <PERSON>h<PERSON>n hoàn thành</h3>
<form [formGroup]="searchForm" class="dialog_content" >
    <div fxLayout="column" fxLayout.xs="column" fxLayout.sm="column" fxLayoutAlign="space-between" class="formFieldItems" style="flex-direction: column;">
      <mat-form-field appearance="outline" >
        <mat-label >Số tờ trình</mat-label>
        <input type="text" matInput formControlName="SoToTrinhVub" maxlength="500">
      </mat-form-field>
      <mat-form-field appearance="outline" >
        <mat-label  >Cơ quan ban hành</mat-label>
        <input type="text" matInput formControlName="CoQuanBanHanhVub" maxlength="500">
      </mat-form-field> 
      <mat-form-field appearance="outline" fxFlex='grow'>
        <mat-label  ><PERSON><PERSON><PERSON> ban hành</mat-label>
        <input matInput [matDatepicker]="NgayBanHanhVub" formControlName="NgayBanHanhVub" (dateChange)="changeToDate($event)" >
        <mat-datepicker-toggle matSuffix [for]="NgayBanHanhVub" ></mat-datepicker-toggle>
        <mat-datepicker #NgayBanHanhVub></mat-datepicker>
      </mat-form-field>
      <mat-form-field appearance="outline" >
        <mat-label  >Trích yếu</mat-label>
        <input type="text" matInput formControlName="TrichYeuVanBanVub" maxlength="500">
      </mat-form-field>
      <mat-form-field appearance="outline" >
        <mat-label  >Loại văn bản</mat-label>
        <input type="text" matInput formControlName="LoaiVanBanVub" maxlength="500">
      </mat-form-field>
      <mat-form-field appearance="outline" >
        <mat-label>Người trình ký</mat-label>
        <input type="text" matInput formControlName="NguoiKyToTrinh" maxlength="500">
      </mat-form-field> 
    </div>
    <div>
      <span class="lbl title-weight"><span  >File đính kèm</span><span style="color:#ce7a58;">&nbsp;*</span></span>
  </div>
  <div [ngClass]="{'file_uploaded': uploaded == true}" fxHide.lt-md class="marginbottom">
      <div class="drag_upload_btn" [ngClass]="{'no_boder': uploaded == true}">
          <button mat-button [ngClass]="{'btn_uploaded': uploaded == true, 'clear_file_queue': uploaded == false}"
              fxFlex='grow'>
              <mat-icon class="material-icons-outlined">cloud_upload</mat-icon> <a href="">
                  <span  >Kéo thả tệp tin hoặc </span><span class="txtUpload"  >Tải lên</span>
              </a>
              <div>
                  <span>Kích thước tối đa của một tệp tin:</span> {{ maxFileSize }}MB
              </div>
          </button>
          <input id="file_upload" name="file_upload[]" type='file' (change)="onSelectFile($event)" multiple
              [accept]="acceptFileExtension" value="{{blankVal}}" fxFlex='grow'>
      </div>
      <div class="file_drag_upload_preview">
          <div class="list_uploaded" *ngFor='let url of urls; let i = index;'>
              <div class="file_icon" [ngStyle]="{'background-image': 'url('+ urls[i] +')'}"></div>
              <span class="file_name" matTooltip="{{uploadFileNames[i]?.filename}}"
                  [matTooltipPosition]="'right'">{{fileNames[i]}}</span>
              
              <button mat-icon-button class="deleteFile" [matMenuTriggerFor]="actionMenu">
                  <mat-icon>more_horiz</mat-icon>
                </button>
                <mat-menu #actionMenu="matMenu" xPosition="before">
                  <a mat-menu-item class="menuAction" target="_blank" [routerLink]="routerLink(uploadFileNames[i])" >
                      <mat-icon>format_size</mat-icon>
                      <span  >Xem trước</span>
                    </a>
                    <a mat-menu-item class="menuAction" (click)="downloadFileExport(i)">
                      <mat-icon>cloud_download</mat-icon>
                      <span  >Tải xuống tệp tin</span>
                  </a>
                    <a mat-menu-item class="menuAction" (click)="removeItem(i)">
                      <mat-icon>delete_outline</mat-icon>
                      <span  >Xóa</span>
                  </a>
              </mat-menu>
          </div>
      </div>
  </div>
  <div class="res_uploadFile marginbottom" fxShow="true" fxHide.gt-sm>
      <div class="res_upload_btn">
          <button mat-button fxFlex='grow'>
              <mat-icon class="material-icons-outlined">cloud_upload</mat-icon>
              <span class="txtUpload"  >Tải lên</span>
          </button>
          <input id="file_upload" name="file_upload[]" type='file' (change)="onSelectFile($event)" multiple
              [accept]="acceptFileExtension" value="{{blankVal}}" fxFlex='grow'>
      </div>
      <div class="res_upload_preview">
          <div class="list_uploaded" *ngFor='let url of urls; let i = index;' fxFlex.gt-sm="49.5" fxFlex.gt-xs="48.5"
              fxFlex='grow'>
              <div class="file_icon" [ngStyle]="{'background-image': 'url('+ urls[i] +')'}"></div>
              <span class="file_name" matTooltip="{{fileNamesFull[i]}}"
                  [matTooltipPosition]="'right'" >{{fileNames[i]}}</span>
                  <button mat-icon-button class="deleteFile" [matMenuTriggerFor]="actionMenu">
                      <mat-icon>more_horiz</mat-icon>
                    </button>
                    <mat-menu #actionMenu="matMenu" xPosition="before">
                      <a mat-menu-item class="menuAction" target="_blank" [routerLink]="routerLink(uploadFileNames[i])" >
                          <mat-icon>format_size</mat-icon>
                          <span  >Xem trước</span>
                        </a>
                        <a mat-menu-item class="menuAction" (click)="downloadFileExport(i)">
                          <mat-icon>cloud_download</mat-icon>
                          <span  >Tải xuống tệp tin</span>
                      </a>
                        <a mat-menu-item class="menuAction" (click)="removeItem(i)">
                          <mat-icon>delete_outline</mat-icon>
                          <span  >Xóa</span>
                      </a>
                  </mat-menu>
          </div>
      </div>
  </div>
  </form>
    

<div fxLayout="row" fxLayout.xs="column" fxLayout.sm="column" fxLayoutAlign="center">
    <button mat-flat-button fxFlex='30' [disabled]="isDisabled" class="applyBtn" (click)="onConfirm()">
        <span  >Đồng ý</span>
    </button>
</div>
