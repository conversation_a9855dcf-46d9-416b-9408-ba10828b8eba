::ng-deep .cloneLedger {

    .title {
        font-style: normal;
        font-weight: 500;
        font-size: 20px;
        color: #000;
    }

    .header-mobile-accept-petion {
        position: relative;
        width: -moz-calc(100% + 48px);
        width: -webkit-calc(100% + 48px);
        width: calc(100% + 48px);
        height: 60px;
        box-sizing: border-box;
        background-color: #096dd9;
        left: -24px;
        top: -24px;
        padding: 10px;
        padding-left: 24px;
        color: #ffffff;
    }

    .addBtn {
        margin-top: 1em;
        background-color: #CE7A58;
        color: #fff;
        height: 3em;
    }
    
    .closeBtn {
        float: right;
        top: -24px;
        right: -24px;
    }

    .mat-form-field-appearance-outline .mat-form-field-infix {
        padding: 0.8em 0;
    }
    
    .mat-form-field-label-wrapper {
        top: -1em;
    }
    
    .mat-form-field.mat-focused .mat-form-field-label {
        color: #ce7a58;
        font-size: 14px;
    }
    
    .mat-form-field-appearance-outline.mat-form-field-can-float.mat-form-field-should-float
    .mat-form-field-label {
        color: #ce7a58;
        transform: translateY(-1.55em) scale(1);
        margin-bottom: 1em;
    }

    .mat-form-field-appearance-outline .mat-form-field-outline {
        color: transparent;
    }
    
    .mat-form-field-appearance-outline .mat-form-field-outline-thick {
        color: #dddddd;
    }
    
    .mat-form-field-appearance-outline .mat-form-field-outline {
        background-color: #eaebeb;
        border-radius: 5px;
    }
}

.search-nested {
    width: 80% !important;
    height: 50px !important;
    margin-left: 15px !important;
}

.clear-search-nested {
    right: 0 !important;
    position: absolute !important;
    top: 0 !important;
}

::ng-deep .sectorSelection {
    border: 1px solid #e8e8e8;
    padding: 0.5em 0.25em 0.25em 0.25em;

    .formLabel {
        color: #ce7a58;
        margin-top: -1.4em;
        padding: 0.25em;
        background-color: #ffffff;
        width: fit-content;
        .required {
            color: red;
        }
    }

    .searchKeyword {
        width: -webkit-fill-available;
    }

    .mat-form-field-appearance-outline .mat-form-field-outline {
        color: transparent;
        background-color: #eaebeb;
        border-radius: 5px;
    }
    .mat-form-field-appearance-outline .mat-form-field-outline-thick {
        color: #dddddd;
    }

    .mat-form-field-appearance-outline .mat-form-field-infix {
        padding: 0.8em 0;
    }

    .mat-form-field.mat-focused .mat-form-field-label {
        color: #ce7a58;
    }

    .mat-form-field-wrapper {
        padding-bottom: 0.5em;
    }

    .mat-form-field-appearance-outline .mat-form-field-can-float .mat-form-field-should-float .mat-form-field-label {
        color: #ce7a58;
        transform: translateY(-1.55em) scale(1);
        margin-bottom: 1em;
    }

    .mat-form-field-label-wrapper {
        top: -1em;
    }    

    .listContainer {
        width: 100%;

        .list {
            height: 30vh;
            border: 1px solid #e8e8e8;
            border-radius: 4px;
            overflow: auto;
            width: -webkit-fill-available;
    
            .mat-pseudo-checkbox-checked {
                background-color: #ce7a58;
            }
    
            .optionCode {
                color: #ce7a58;
            }
    
            .optionDelimiter {
                margin: 0 0.25em 0 0.25em;
            }
    
            .optionName {
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }
        }

        .sumary {
            margin-top: 1em;
            float: left;
        }
    }  

    .onlyShowSelectedSector .mat-checkbox-label {
        white-space: normal;
        word-wrap: break-word;
        overflow-wrap: break-word;
        display: block;
    }
}