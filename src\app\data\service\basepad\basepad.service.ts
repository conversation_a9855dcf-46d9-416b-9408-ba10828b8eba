import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { ApiProviderService } from 'src/app/core/service/api-provider.service';
import { Observable } from 'rxjs';
import { EnvService } from 'src/app/core/service/env.service';

@Injectable({
  providedIn: 'root'
})
export class BasepadService {

  config = this.envService.getConfig();
  constructor(
    private http: HttpClient,
    private apiProviderService: ApiProviderService,
    private envService: EnvService,
  ) { }

  private basepad = this.apiProviderService.getUrl('digo', 'basepad');
  private adapter = this.apiProviderService.getUrl('digo', 'adapter');

  getProcedureProcessDefinitionById(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.basepad + '/procedure-process-definition/' + searchString, { headers }).pipe();    
  }

  postPublicAdministration(body: any) : Observable<any>{
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    return this.http.post<any>(this.basepad +'/public-admin/--insert', body, { headers });
  }

  putPublicAdministration(body: any, id: any) : Observable<any>{
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    return this.http.put<any>(this.basepad +`/public-admin/${id}/update`, body, { headers });
  }

  deletePublicAdministration( id: any) : Observable<any>{
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    return this.http.delete<any>(this.basepad +`/public-admin/${id}/delete`, { headers });
  }

  searchPublicAdministration(search:any) : Observable<any>{
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    return this.http.get<any>(this.basepad +`/public-admin/--search-by-keyword`+ search, { headers }).pipe();
  }

  statisticPublicAdministration(search:any) : Observable<any>{
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    if (window.location.hostname === 'localhost'){
      return this.http.get<any>('http://localhost:8091' + '/public-admin/--statistics' + search, { headers }).pipe();
    }else {
      return this.http.get<any>(this.basepad +`/public-admin/--statistics`+ search, { headers }).pipe();
    }
  }

  searchAllPublicAdministration() : Observable<any>{
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    return this.http.get<any>(this.basepad +`/public-admin/--get-all`, { headers }).pipe();
  }

  postProceAdministration(body: any) : Observable<any>{
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    return this.http.post<any>(this.basepad +'/procedure-admin/--insert', body, { headers });
  }

  putProceAdministration(body: any, id: any) : Observable<any>{
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    return this.http.put<any>(this.basepad +`/procedure-admin/${id}/update`, body, { headers });
  }

  deleteProceAdministration( id: any) : Observable<any>{
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    return this.http.delete<any>(this.basepad +`/procedure-admin/${id}/delete`, { headers });
  }

  searchProceAdministration(search:any) : Observable<any>{
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    return this.http.get<any>(this.basepad +`/procedure-admin/--search-by-keyword`+ search, { headers }).pipe();
  }

  searchAllProceAdministration() : Observable<any>{
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    return this.http.get<any>(this.basepad +`/procedure-admin/--get-all`, { headers }).pipe();
  }


  getListProcedureLevel(): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.basepad + '/procedure-level', { headers }).pipe();    
  }

  getListSector(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.basepad + '/sector' + searchString, { headers }).pipe();    
  }

  getListSectorWithPage(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.basepad + '/sector/--by-agency-with-page' + searchString, { headers }).pipe();    
  }

  getSectorDetail(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.basepad + '/sector/' + id, { headers }).pipe();   
  }

  getListProcedure(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.basepad + '/procedure' + searchString, { headers }).pipe();    
  }

  getListProcedureCanApply(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.basepad + '/procedure/--can-apply' + searchString, { headers }).pipe();    
  }

  getProcedureByNationCode(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.basepad + '/procedure/--by-nation-code' + searchString, { headers }).pipe();    
  }

  getProcedureDetail(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.basepad + '/procedure/' + id, { headers }).pipe();    
  }

  getProcedureDetailBdg(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.basepad + '/procedure/bdg/' + id, { headers }).pipe();    
  }

  getProcedureIdByNationCode(code): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.basepad + '/procedure/--get-id-by-nation-code?code=' + code, { headers }).pipe();    
  }

  getProcedureForm(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.basepad + '/procedure-form/' + searchString, { headers }).pipe();    
  }

  getProcedureFormEform(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.basepad + '/procedure-form-eform/' + searchString, { headers }).pipe();
  }

  getChangeFileFormEform(file, value, components): Observable<any> {
    let headers = new HttpHeaders();
    // headers = headers.append('Content-Type', 'application/json');
    const formData: FormData = new FormData();
    formData.append('file', file, 'file.docx');
    formData.append('value', value);
    formData.append('components', components);
    return this.http.post(this.basepad + '/procedure-form-eform/--changeFile', formData, { headers,  responseType: 'blob' as 'json' });
   
  }

  getProcostForOnline(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.basepad + '/procost/--for-online?procedure-id=' + id, { headers }).pipe();    
  }

  getListConfigTemplate(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.basepad + '/config/get-template' + searchString, { headers });    
  }

  getConfiguration(): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.basepad + '/configuration/--by-deployment-id?deployment-id=' + this.config.deploymentId, { headers }).pipe();
    
  }

  getListMenuWithAgency(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.basepad + '/menu-configuration/--for-category' + searchString, { headers }).pipe();
  }

  getListMenu(): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.basepad + '/menu-configuration', { headers }).pipe();
  }

  getDetailProcost(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.basepad + '/procost/' + id, { headers }).pipe();
  }

  getListProcedureProcess(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.basepad + '/procedure-process-definition/' + searchString, { headers });
  }

  getDetailProcedureProcess(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.basepad + '/procedure-process-definition/' + id, { headers });
  }

  getCodePattern(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.basepad + '/config/get-pattern' + searchString, { headers });
  }

  getConfiguratingChatbot(search): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get( this.adapter + '/chatbot' + search, { headers }).pipe();
  }
  getListProcedureForm(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.basepad + '/procedure-form' + searchString, { headers });
  }

  getProcedureNumberByLevel(): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.basepad + '/procedure/--counting-procedure-by-level', { headers });
  }

  getListProcedureBDG(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.basepad + '/procedure/--search-bdg' + searchString, { headers }).pipe();
  }

  getEssentialPublicServices(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.basepad + '/procedure/essential-public-services' + searchString, {headers}).pipe();
  }

  getTotalPublicByYear(year, levelId): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.basepad + '/hcm-report/procedure/--total-public-by-year?year=' + year + '&level-id=' + levelId, { headers }).pipe();
  }

  getSectorList(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.basepad + '/sector/--search' + searchString, { headers }).pipe();
  }

  getVietInfoReceiptCode(procedureId): Observable<any>{
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.basepad + '/procost?procedure-id=' + procedureId, { headers }).pipe();
  }

  getListProcedureBySector(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.basepad + '/procedure' + searchString, { headers }).pipe();    
  }
  
  getReportProcedureByAgencyId(body): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
  //  return this.http.post('http://localhost:8069/procedure/report-procedure-by-agency', body, { headers });
    return this.http.post(this.basepad + '/procedure/report-procedure-by-agency', body, { headers });
  }

  getStatisticProcedureCost(body): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
  //  return this.http.post('http://localhost:8069/procedure/statistic-procedure-cost', body, { headers });
    return this.http.post(this.basepad + '/procedure/statistic-procedure-cost', body, { headers });
  }
  
  postNewAuthLedger(body: any) : Observable<any>{
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    return this.http.post<any>(this.basepad +'/auth-info/', body, { headers });
  }
  getSearch(searchString): Observable<any>{
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.basepad + '/auth-ledger/--search' + searchString, { headers }).pipe();
  }
  getById(id): Observable<any>{
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.basepad + '/auth-ledger/' + id, { headers }).pipe();
  }

  getAuthInfoById(id): Observable<any>{
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.basepad + '/auth-info/getAllItem/' + id, { headers }).pipe();
  }

  getSearchAuthInfo(searchString): Observable<any>{
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.basepad + '/auth-info/--search' + searchString, { headers }).pipe();
  }
  addNoteBook(body:any): Observable<any>{
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.post<any>(this.basepad +'/auth-ledger/', body, { headers });
  }
  deleteNoteBook( id: any) : Observable<any>{
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    return this.http.delete<any>(this.basepad +`/auth-ledger/${id}`, { headers });
  }
  putInformation(body: any, id: any) : Observable<any>{
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    return this.http.put<any>(this.basepad +`/auth-ledger/${id}`, body, { headers });
  }
  putAuthInfo(body: any, id: any) : Observable<any>{
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    return this.http.put<any>(this.basepad +`/auth-info/${id}`, body, { headers });
  }

  remove( id: any) : Observable<any>{
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    return this.http.get<any>(this.basepad +`/auth-info/remove/${id}`, { headers });
  }
  getNumberAuth(id): Observable<any>{
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.basepad + `/auth-ledger/${id}/--next-index` , { headers }).pipe();
  }

  putNumberInfo(id) : Observable<any>{
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    return this.http.put<any>(this.basepad +`/auth-ledger/${id}/--next-index`, { headers });
  }
   getListHotline(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers.append('Access-Control-Allow-Origin', '*');
    headers.append('Content-Type', 'application/json');
    return this.http.get(this.basepad +'/hotlineqbh/' + searchString, { headers });
   // return this.http.get('http://localhost:8091/hotlineqbh/' + searchString, { headers });
  }
  postNewHotline(requestBody) {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.post<any>(this.basepad +'/hotlineqbh/', requestBody, { headers });
    //return this.http.post<any>('http://localhost:8091/hotlineqbh/' ,requestBody, { headers });
  }
  deleteBHotline(id: string) {
    let headers = new HttpHeaders();
    headers = headers.append('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.delete(this.basepad +'/hotlineqbh/' + id, { headers });
    //return this.http.delete('http://localhost:8091/hotlineqbh/' + id, { headers });
  }
  getHotlineInfo(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.basepad +'/hotlineqbh/' + id, { headers });
    //return this.http.get('http://localhost:8091/hotlineqbh/' + id, { headers });
  }
  putUpdateHotline(id, requestBody) {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.put<any>(this.basepad+'/hotlineqbh/' + id, requestBody, { headers });
    //return this.http.put<any>('http://localhost:8091/hotlineqbh/' + id, requestBody, { headers });
  }
  importDataHotline(file: File): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    let formData: FormData = new FormData();
    formData.append("file",file);
    return this.http.post(this.basepad + "--import-excell", formData, { headers });
    //return this.http.post("http://localhost:8091/hotlineqbh/--import-excell", formData, { headers });
  }

  excelExportData(type): any {
    return new Promise((resolve) => {
      this.downloadExportData(type).then(res => {
        console.log(res);
        
        let blob = new Blob([res.body], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
        let filename = "export-data";
        if (res.headers.get('content-disposition') !== null) {
          filename = res.headers.get('content-disposition').split(';')[1].split('=')[1].replace(/\"/g, '');
        }
         
        let blobUrl = URL.createObjectURL(blob);
        let xhr = new XMLHttpRequest;
        xhr.responseType = 'blob';
        xhr.onload = function () {
          var recoveredBlob = xhr.response;
          var reader = new FileReader;
          reader.onload = function () {
            var blobAsDataUrl = reader.result;
            console.log("reader", reader);
            // window.open(blobAsDataUrl.toString(),"_blank");
            let anchor = document.createElement("a");
            anchor.download = filename;
            anchor.href = blobAsDataUrl.toString();
            anchor.click();
          };

          reader.readAsDataURL(recoveredBlob);
        };


        xhr.open('GET', blobUrl);
        xhr.send();
        resolve(true);
      })
        .catch(err => {
          console.log("lỗi");
          
          if (err.status === 500) {
            // this.snackbar.openSnackBar('Hệ thống tạm thời không thể xuất file excel!', '', 'Xin vui lòng thử lại sau.', '', 'error_notification');
          }
          resolve(false)
        });
    });
  }

  downloadExportData(type) {
    let headers = new HttpHeaders();
    headers = headers.set("Content-Type", "application/json");
    let url = "";
    switch (type) {
      case 0:
        url = "/sector/--export-data";
        break;
      case 1:
        url = "/procost-type/--export-data";
        break;
      case 2:
        url = "/fee-type/--export-data";
        break;
      case 3:
        url = "/form/--export-data";
        break;
      case 4:
        url = "/procedure/--export-data";
        break;
      case 5:
        url = "/procedure-form/--export-data";
        break;
      case 6:
        url = "/procost/--export-data";
        break;
      case 7:
            url = "/procedure/--export-procedure-to-convert";
            break;

    }
    return this.http.get(this.basepad + url, { observe: 'response', responseType: 'blob' }).toPromise();
  }

  

  postProcedureProcessDefinitionFromFile(data: any): Observable<any> {
        const formData: FormData = new FormData();
        formData.append('file', data.file);
        return this.http.post<any>(this.basepad + '/procedure-process-definition/--from-file-to-convert', formData);
  }


}
