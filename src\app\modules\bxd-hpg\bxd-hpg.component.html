<h2><PERSON><PERSON> <PERSON><PERSON><PERSON> hồ sơ cấp phép x<PERSON>y dựng</h2>
<div fxLayout="row" fxLayoutAlign="center">
  <div class="frm_searchbar" fxFlex="grow">
    <form class="searchForm" [formGroup]="searchForm">
      <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="row">
        <div fxFlex='1'></div>
        <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
          <mat-label i18n="@@HCMLGSPLog_FromDate">Từ ngày</mat-label>
          <input matInput [matDatepicker]="pickerFromDate" formControlName="fromDate" required [max]="currentDate">
          <mat-datepicker-toggle matSuffix [for]="pickerFromDate"></mat-datepicker-toggle>
          <mat-datepicker #pickerFromDate></mat-datepicker>
        </mat-form-field>
        <div fxFlex='1'></div>
        <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
          <mat-label i18n="@@HCMLGSPLog_ToDate">Đến ngày</mat-label>
          <input matInput [matDatepicker]="pickerToDate" formControlName="toDate" required [max]="currentDate">
          <mat-datepicker-toggle matSuffix [for]="pickerToDate"></mat-datepicker-toggle>
          <mat-datepicker #pickerToDate></mat-datepicker>
        </mat-form-field>
        <div fxFlex='1'></div>
        <button mat-flat-button fxFlex='24' class="btn_search" (click)="submit()">
          <mat-icon>search</mat-icon>
          <span>Tra cứu</span>
        </button>
      </div>
    </form>
  </div>
</div>
<div class="tbl">
  <table mat-table [dataSource]="dataSource">
    <ng-container  matColumnDef="STT">
      <th mat-header-cell  *matHeaderCellDef style="text-align: center;">STT</th>
      <td mat-cell  *matCellDef="let row ; index as i" style="text-align: center;"> {{i + number}} </td>
    </ng-container>
    <ng-container  matColumnDef="MaHoSo">
      <th mat-header-cell *matHeaderCellDef style="text-align: center;">Mã hồ sơ</th>
      <td mat-cell *matCellDef="let row" style="text-align: center;"> {{row?.MaHoSo}} </td>
    </ng-container>
    <ng-container matColumnDef="TrangThaiHoSo">
      <th mat-header-cell *matHeaderCellDef style="text-align: center;">Trạng thái hồ sơ</th>
      <td mat-cell *matCellDef="let row" style="text-align: center;">
        <span class="more-text">
          {{setTrangThaiHoSo(row?.TrangThaiHoSo)}}
        </span>
      </td>
    </ng-container>
    <ng-container  matColumnDef="HoTenNguoiNop">
      <th mat-header-cell *matHeaderCellDef style="text-align: center;">Họ tên người nộp</th>
      <td mat-cell *matCellDef="let row" style="text-align: center;">
        <span >
          {{row?.ThongTinNguoiNop.HoTenNguoiNop}}
        </span>
      </td>
    </ng-container>
    <ng-container  matColumnDef="NgayTiepNhan">
      <th mat-header-cell *matHeaderCellDef style="text-align: center;">Ngày tiếp nhận</th>
      <td mat-cell *matCellDef="let row" style="text-align: center;">
        <span *ngIf="row.NgayTiepNhan !== null && row.NgayTiepNhan !== undefined">
                    {{row?.NgayTiepNhan}}
        </span> 
      </td>
    </ng-container>
    <ng-container  matColumnDef="NgayHenTraKetQua">
      <th mat-header-cell *matHeaderCellDef style="text-align: center;">Ngày hẹn trả kết quả</th>
      <td mat-cell *matCellDef="let row" style="text-align: center;">
        <span *ngIf="row.NgayHenTraKetQua !== null && row.NgayHenTraKetQua !== undefined">
                    {{row?.NgayHenTraKetQua}}
        </span> 
      </td>
    </ng-container>
    <ng-container matColumnDef="HinhThucTraKetQua">
      <th mat-header-cell *matHeaderCellDef style="text-align: center;">Hình thức trả kết quả</th>
      <td mat-cell *matCellDef="let row" style="text-align: center;">
        <span>
          {{setHinhThucTraKetQua(row?.HinhThucTraKetQua)}}
        </span>
      </td>
    </ng-container>
    <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
    <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
  </table>
  <div style="text-align: center; padding: 8px 0px" *ngIf="countResult == 0">Không tìm thấy dữ liệu</div>
  <pagination-slice id="pgnx"
        [itemsPerPage]="size"
        [currentPage]="page"
        [totalItems]="countResult"
        [pageSizeOptions]="[].concat(pgSizeOptions)"
        [dataSource]="ELEMENT_DATA"
        (change)="changePageOrSize($event)"
        [type]="paginationType">
  </pagination-slice>
</div>
