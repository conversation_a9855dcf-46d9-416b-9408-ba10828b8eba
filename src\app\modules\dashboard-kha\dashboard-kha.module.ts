import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DashboardKhaComponent } from './pages/dashboard-kha/dashboard-kha.component';
import { SharedModule } from 'src/app/shared/shared.module';
import { DashboardKhaRoutingModule } from './dashboard-kha-routing.module';
import { ListDossierRemindComponent } from './pages/list-dossier-remind/list-dossier-remind.component';
import {
  ListDossierRemindDauKyComponent
} from 'modules/dashboard-kha/pages/list-dossier-remind-dau-ky/list-dossier-remind-dau-ky.component';



@NgModule({
  declarations: [DashboardKhaComponent, ListDossierRemindComponent, ListDossierRemindDauKyComponent],
  imports: [
    CommonModule,
    DashboardKhaRoutingModule,
    SharedModule,
  ]
})
export class DashboardKhaModule { }
