import { Component, OnInit, ChangeDetectorRef, AfterViewInit, ViewChild, OnDestroy } from '@angular/core';
import { FormGroup, FormControl } from '@angular/forms';
import { Router } from '@angular/router';
import { EnvService } from 'src/app/core/service/env.service';
import { MatTableDataSource } from '@angular/material/table';
import { DatePipe } from '@angular/common';
import { ExportExcelService } from 'src/app/data/service/export-excel/export-excel.service';
import { MainService } from 'src/app/data/service/main/main.service';
import { ProcedureService } from 'src/app/data/service/procedure/procedure.service';
import { takeUntil } from 'rxjs/operators';
import { ReplaySubject, Subject } from 'rxjs';
import { ActivatedRoute } from '@angular/router';
import { DossierService } from 'src/app/data/service/dossier/dossier.service';
import { NgxPrinterService } from 'ngx-printer';
import { SnackbarService } from 'src/app/data/service/snackbar/snackbar.service';
import { MatSelect } from '@angular/material/select';
import { ReportService } from 'data/service/report/report.service';
import { DeploymentService } from 'data/service/deployment.service';
import * as tUtils from 'src/app/data/service/thoai.service';
import {CookieService} from 'ngx-cookie-service';
import { ConfirmCreateStatisticDialogModel, CreateStatisticComponent } from '../../pages/logbook/dialogs/create-statistic/create-statistic.component';
import { MatDialog } from '@angular/material/dialog';


export interface Agency {
  id: string;
  name: string;
}

@Component({
  selector: 'app-logbook',
  templateUrl: './logbook.component.html',
  styleUrls: [
    './logbook.component.scss',
    '/src/app/app.component.scss',
    '/src/app/shared/scss/form-field-outline.scss'
  ]
})
export class LogbookComponent implements OnInit, AfterViewInit, OnDestroy {

  nowDate = tUtils.newDate();

  // tslint:disable-next-line: max-line-length
  toDate = this.nowDate.getFullYear() + '-' + ((this.nowDate.getMonth() + 1) <= 9 ? '0' + (this.nowDate.getMonth() + 1) : (this.nowDate.getMonth() + 1)) + '-' + ((this.nowDate.getDate() <= 9) ? ('0' + this.nowDate.getDate()) : this.nowDate.getDate());
  // tslint:disable-next-line: max-line-length
  fromDate = this.nowDate.getFullYear() + '-' + ((this.nowDate.getMonth() + 1) <= 9 ? '0' + (this.nowDate.getMonth() + 1) : (this.nowDate.getMonth() + 1)) + '-01';

  searchForm = new FormGroup({
    acceptFrom: new FormControl(''),
    acceptTo: new FormControl(''),
    agencyCtrl: new FormControl(''),
    searchAgencyCtrl: new FormControl(''),
    applyMethod: new FormControl(''),
    sectorCtrl: new FormControl(''),
    keyword: new FormControl(''),
    nation: new FormControl(''),
    province: new FormControl(''),
    district: new FormControl(''),
    village: new FormControl(''),
    procedureCtrl: new FormControl(''),
    searchProcedureCtrl: new FormControl(''),
    sortCtrl: new FormControl(''),
  });

  config = this.envService.getConfig();
  selectedLang = localStorage.getItem('language') || 'vi';
  selectedLangId = Number(localStorage.getItem('languageId')) || 228;
  pageTitle = {
    vi: `Thống kê sổ theo dõi`,
    en: `Logbook statistics`
  };

  protected onDestroy = new Subject<void>();
  // =================== old
  env: any = this.deploymentService.getAppDeployment()?.env;
  ELEMENTDATA: any[] = [];
  dataSource: MatTableDataSource<any>;
  // listSector: Array<any> = [];
  keySearchSectorAgency = '';
  // Search Sector
  keywordSector = '';
  totalPagesSector = 0;
  currentPageSector = 0;
  pageSizeSector = 10;

  parentAgency = '';
  agencyId = '';
  userAgency?: any = JSON.parse(localStorage.getItem('userAgency'));
  userAgencyCount?: any = JSON.parse(localStorage.getItem('listAgencyUser'))?.length || 0;
  actionSubmitQNM = this.deploymentService.env.OS_QNM.actionSubmitQNM;
  logBookPrintFormatter = this.deploymentService.getAppDeployment().logBookPrintFormatter === true ? true : false; //IGATESUPP-91115:[BLU|iGate 2.0] Điều chỉnh chức năng "Thống kê sổ theo dõi"

  // tslint:disable-next-line: max-line-length
  displayedColumnsDefault: string[] = ['stt', 'code', 'procedure', 'quantity', 'appliciant', 'address', 'phone', 'object','actionSubmit', 'actionSubmitDBN', 'agency', 'fee', 'acceptedDate', 'appointmentDate', 'transferDate', 'receivingResultDate', 'returnedDate', 'sign', 'receivingKind', 'note', 'contentOfRequestResolution', 'addressOfTheLandPlot', 'numberCopies'];
  displayedColumnsOwnerFullname: string[] = ['stt', 'code', 'procedure', 'quantity', 'ownerFullname', 'appliciantName' , 'address', 'phone', 'object','actionSubmit', 'actionSubmitDBN', 'agency', 'fee', 'acceptedDate', 'appointmentDate', 'transferDate', 'receivingResultDate', 'returnedDate', 'sign', 'receivingKind', 'note', 'contentOfRequestResolution', 'addressOfTheLandPlot', 'numberCopies'];
  displayedColumns: string[] = this.env?.AddOwnerStatisticalReport ? this.displayedColumnsOwnerFullname : this.displayedColumnsDefault;
  displayedRow: string[] = this.env?.AddOwnerStatisticalReport ? ['col-1', 'col-2', 'col-3', 'col-4', 'col-5','col-15', 'col-6', 'col-7', 'col-8', 'col-9', 'col-17','col-10', 'col-11', 'row-1', 'row-2', 'col-12', 'col-13', 'col-14', 'col-16'] : ['col-1', 'col-2', 'col-3', 'col-4', 'col-5', 'col-6', 'col-7', 'col-8', 'col-9', 'col-17' ,'col-10', 'col-11', 'row-1', 'row-2', 'col-12', 'col-13', 'col-14', 'col-16'];
  displayedRowBLU: string[] = ['col-1', 'col-2', 'col-3','col-4', 'col-5', 'col-6', 'col-7', 'col-8', 'col-9', 'col-17' ,'col-10', 'col-11', 'col-13', 'col-14', 'M4', 'M5', 'M6', 'M7', 'M8', 'M9', 'M10', 'M11']
  displayedColumnsBLU: string[] = ['stt', 'code', 'procedure', 'quantity', 'appliciant', 'address', 'phone', 'object', 'actionSubmit', 'actionSubmitDBN', 'agency', 'fee', 'acceptedDate', 'appointmentDate', 'transferDate', 'receivingResultDate', 'returnedDate', 'sign', 'receivingKind', 'note'];
  provinceName = this.env?.provinceName[localStorage.getItem('language')];
  agencyTitle = `VĂN PHÒNG UBND ${this.provinceName.toUpperCase()}`;
  subAgencyTitle = 'TRUNG TÂM PHỤC VỤ HÀNH CHÍNH CÔNG';
  ngayThangNamWithLocation= `${this.provinceName.replace("Tỉnh", "")}${this.exportExcelService.formatDateTimeNgayThangNam(new Date().toString(), true)}`;
  ELEMENTDATAALL: any[] = [];
  dataSourceAll: MatTableDataSource<any>;


  size = 10;
  pageIndex = 1;
  page = 1;
  countResult = 0;
  pgSizeOptions = this.config.pageSizeOptions;
  paginationType = this.deploymentService.env.paginationType;
  paramsQuery = {
    page: '1',
    size: '10',
    acceptFrom: '',
    acceptTo: '',
    agency: '',
    applyMethod: '',
    sector: '',
    keyword: '',
    nation: '',
    province: '',
    district: '',
    village: '',
    procedure: '',
    sortId: '',
  };

  //phucnh.it2-37207
  enableSelectColumnDisplayInReportBtnGeneral = this.deploymentService.env?.OS_HCM?.enableSelectColumnDisplayInReportBtnGeneral;
  enableShowComboBoxSortByAppliedDate = this.deploymentService.env.OS_HCM.enableShowComboBoxSortByAppliedDate;

  columnForm: FormGroup = new FormGroup({
    stt: new FormControl(true),
    code: new FormControl(true),
    procedure: new FormControl(true),
    quantity: new FormControl(true),
    appliciant: new FormControl(true),    
    address: new FormControl(true),
    // organizationName: new FormControl(true),
    // organizationAddress: new FormControl(true),
    phone: new FormControl(true),
    agency: new FormControl(true),
    fee: new FormControl(true),
    acceptedDate: new FormControl(true),
    appointmentDate: new FormControl(true),
    transferDate: new FormControl(true),
    receivingResultDate: new FormControl(true),
    returnedDate: new FormControl(true),
    sign: new FormControl(true),
    receivingKind: new FormControl(true),
    note: new FormControl(true),
    numberCopies: new FormControl(false),
  });

  columnDefinitions = [    
    { def: 'stt', label: 'Số thứ tự', show : true },
    { def: 'code', label: 'Mã số hồ sơ', show: true },
    { def: 'procedure', label: 'Tên TTHC', show: true },       
    { def: 'quantity', label: 'Số lượng hồ sơ (bộ)', show: true },
    { def: 'appliciant', label: 'Tên cá nhân/ tổ chức', show: true },    
    { def: 'address', label: 'Địa chỉ', show: true },
    // { def: 'organizationName', label: 'Tên doanh nghiệp', show: true },
    // { def: 'organizationAddress', label: 'Địa chỉ doanh nghiệp', show: true },
    { def: 'phone', label: 'Số điện thoại', show: true },
    { def: 'agency', label: 'Cơ quan chủ trì giải quyết', show: true },
    { def: 'fee', label: 'Lệ phí', show: true },
    { def: 'acceptedDate', label: 'Nhận hồ sơ', show: true },
    { def: 'appointmentDate', label: 'Hẹn trả kết quả', show: true },
    { def: 'transferDate', label: 'Chuyển hồ sơ đến cơ quan giải quyết', show: true },
    { def: 'receivingResultDate', label: 'Nhận kết quả từ cơ quan giải quyết', show: true },
    { def: 'returnedDate', label: 'Ngày trả kết quả', show: true },
    { def: 'sign', label: 'Ký nhận', show: true },
    { def: 'receivingKind', label: 'Phương thức nhận kết quả', show: true },
    { def: 'note', label: 'Ghi chú', show: true },
    //{ def: 'numberCopies', label: 'Số lượng bản sao xin cấp', show: false },
  ]  

  // headersArray = ['', 'No.', 'Dossier code', 'TTHC name', 'Dossier quantity (sets)', "Name of person/ organization", 'Address', 'Organization Name', 'Organization Address', 'Phone number', 'Processing agency', 'Fee', 'Receiving dossier', 'Appointment to return results',
  //       'Transfer of dossier to resolution agency', 'Receiving result from agency', 'Date, month, year', 'Signed', 'Note'];

  columnDefinitionsEn = [    
    { def: 'stt', label: 'No.', show : true },
    { def: 'code', label: 'Dossier code', show: true },
    { def: 'procedure', label: 'TTHC name', show: true },       
    { def: 'quantity', label: 'Dossier quantity (sets)', show: true },
    { def: 'appliciant', label: 'Name of person/ organization', show: true },    
    { def: 'address', label: 'Address', show: true },
    // { def: 'organizationName', label: 'Organization Name', show: true },
    // { def: 'organizationAddress', label: 'Organization Address', show: true },
    { def: 'phone', label: 'Phone number', show: true },
    { def: 'agency', label: 'Processing agency', show: true },
    { def: 'fee', label: 'Fee', show: true },
    { def: 'acceptedDate', label: 'Receiving dossier', show: true },
    { def: 'appointmentDate', label: 'Appointment to return results', show: true },
    { def: 'transferDate', label: 'Transfer of dossier to resolution agency', show: true },
    { def: 'receivingResultDate', label: 'Receiving result from agency', show: true },
    { def: 'returnedDate', label: 'Date, month, year', show: true },
    { def: 'sign', label: 'Signed', show: true },
    { def: 'receivingKind', label: 'Receiving Kind', show: true },
    { def: 'note', label: 'Note', show: true },
    //{ def: 'numberCopies', label: 'Number Copies', show: false },
  ]

  isHidden = {
    stt: false,
    code: false,
    procedure: false,
    quantity: false,
    appliciant: false,
    address: false,
    objectType: true,
    actionSubmit: false,
    // organizationName: false,
    // organizationAddress: false,
    phone: false,
    agency: false,
    fee: false,
    acceptedDate: false,
    appointmentDate: false,
    transferDate: false,    
    receivingResultDate: false,
    returnedDate: false,
    sign: false,
    receivingKind: false,
    note: false,
    numberCopies: true,
    applyMethod: false
  }

  colDateMonthYear = {
    acceptedDate: false,
    appointmentDate: false,
    transferDate: false,    
    receivingResultDate: false
  }
  
  colDateMonthYearTotal = 4;
  colReturnResult = 3;

  _displayedColumns: string[];

  //End phucnh.it2-37207

  // excel
  excelData = [];
  isShowSector = this.env?.OS_TGG?.logbook?.isShowSector === true || this.env?.OS_QNI?.logbook?.ShowSearchByField === 1;
  isShowApplyMethod = this.env?.OS_TGG?.logbook?.isShowApplyMethod === true ? this.env?.OS_TGG?.logbook?.isShowApplyMethod : false;

  enableLogbookProcedureFilter = this.deploymentService.env?.OS_HCM?.enableLogbookProcedureFilter ? this.deploymentService.env?.OS_HCM?.enableLogbookProcedureFilter : 0;
  logBookNumberCopies = this.deploymentService.env?.OS_HCM?.logBookNumberCopies;
  isQNM = this.deploymentService.env.OS_QNM.isQNM;
  private listProcedure: any[] = [];
  listProcedurePage = 0;
  isFullListProcedure = false;
  searchProcedureKeyword = '';
  procedureCtrl: FormControl = new FormControl();
  searchProcedureCtrl: FormControl = new FormControl();
  protected procedures: any[] = this.listProcedure;
  public procedureFiltered: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  @ViewChild('procedureMatSelectInfiniteScroll', { static: true }) procedureMatSelectInfiniteScroll: MatSelect;
  timeOutGetListProcedure: any = null;
  keySearchProcedureAgency = '';

  // Sector infinity scroll with search
  private listSector: any[] = [];
  isFullListSector = false;
  searchSectorKeyword = '';
  sectorCtrl: FormControl = new FormControl();
  searchSectorCtrl: FormControl = new FormControl();
  protected sectors: any[] = this.listSector;
  public sectorFiltered: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  @ViewChild('sectorMatSelectInfiniteScroll', { static: true }) sectorMatSelectInfiniteScroll: MatSelect;

  waitingDownloadExcel = false;

  waitingDownloadPrint = false;

  keywordAgencyAccept = '';
  totalPagesAgencyAccept = 0;
  currentPageAgencyAccept = 0;
  pageSizeAgencyAccept = 50;
  searchType = 1;
  numberOfElements = 0;
  timeOutAgencyAccept: any = null;
  listAgencyAccept: Array<any> = [];
  timeOutSector: any = null;
  listNation = [];
  listProvince = [];
  listDistrict = [];
  listVillage = [];

  arrSortType = [
    { id: 0, value: "Chưa chọn" },
    { id: 1, value: "Ngày nhận hồ sơ tăng dần" },
    { id: 2, value: "Ngày nhận hồ sơ giảm dần" }
  ];
  sortId = '0';

  administrativeAgencyTag = this.deploymentService.env?.statistics?.administrativeAgencyTag ? this.deploymentService.env?.statistics?.administrativeAgencyTag : [];
  administrativeAgency = {
    loaded: false,
    id: '',
  }
  isCheckAdministrativeAgencyTagConfig = this.deploymentService?.getAppDeployment()?.isCheckAdministrativeAgencyTagConfig||false;
  limitTimeExportData = this.deploymentService.getAppDeployment()?.limitTimeExportData || false;
  showPopupCreateStatistic = false;
  statisticES = false;
  enableCreateReport = this.deploymentService?.getAppDeployment()?.enableCreateReport||false;
  administrativeLevelMode = this.deploymentService?.getAppDeployment()?.administrativeLevelMode ?? 0; // IGATESUPP-130841 (Xử lý bãi bỏ cấp huyện) | 0: 3 cấp, 1: 2 cấp

  constructor(
    private envService: EnvService,
    public datepipe: DatePipe,
    private mainService: MainService,
    private cdRef: ChangeDetectorRef,
    private exportExcelService: ExportExcelService,
    private procedureService: ProcedureService,
    private activeRoute: ActivatedRoute,
    private datePipe: DatePipe,
    private dossierService: DossierService,
    private snackbarService: SnackbarService,
    private router: Router,
    private printerService: NgxPrinterService,
    private reportService: ReportService,
    private deploymentService: DeploymentService,
    private cookieService: CookieService,
    private dialog: MatDialog,
  ) {
    this.dataSource = new MatTableDataSource(this.ELEMENTDATA);
    this.dataSourceAll = new MatTableDataSource(this.ELEMENTDATAALL);
    if (this.activeRoute.snapshot.queryParamMap.get('acceptFrom') != null) {
      this.paramsQuery.acceptFrom = this.activeRoute.snapshot.queryParamMap.get('acceptFrom');
      if (this.paramsQuery.acceptFrom !== '') {
        this.searchForm.patchValue({
          acceptFrom: new Date(this.datePipe.transform(this.paramsQuery.acceptFrom, 'dd/MM/yyyy'))
        });
      }
    }
    if (this.activeRoute.snapshot.queryParamMap.get('acceptTo') != null) {
      this.paramsQuery.acceptTo = this.activeRoute.snapshot.queryParamMap.get('acceptTo');
      if (this.paramsQuery.acceptTo !== '') {
        this.searchForm.patchValue({
          acceptTo: new Date(this.datePipe.transform(this.paramsQuery.acceptTo, 'dd/MM/yyyy'))
        });
      }
    }
    if (this.activeRoute.snapshot.queryParamMap.get('page') != null) {
      this.paramsQuery.page = this.activeRoute.snapshot.queryParamMap.get('page');
    }
    if (this.activeRoute.snapshot.queryParamMap.get('size') != null) {
      this.paramsQuery.size = this.activeRoute.snapshot.queryParamMap.get('size');
    }
    if (this.activeRoute.snapshot.queryParamMap.get('village') != null) {
      this.paramsQuery.agency = this.activeRoute.snapshot.queryParamMap.get('village');
    }
    if (this.activeRoute.snapshot.queryParamMap.get('applyMethod') != null) {
      this.paramsQuery.applyMethod = this.activeRoute.snapshot.queryParamMap.get('applyMethod');
      this.searchForm.patchValue({
        applyMethod: this.paramsQuery.applyMethod
      });
    }
    if (this.activeRoute.snapshot.queryParamMap.get('sector') != null) {
      this.paramsQuery.sector = this.activeRoute.snapshot.queryParamMap.get('sector');
      this.searchForm.patchValue({
        sectorCtrl: this.paramsQuery.sector
      });
    }

    if (this.activeRoute.snapshot.queryParamMap.get('keyword') != null) {
      this.paramsQuery.keyword = this.activeRoute.snapshot.queryParamMap.get('keyword');
      this.searchForm.patchValue({
        keyword: this.paramsQuery.keyword
      });
    }

    if (this.activeRoute.snapshot.queryParamMap.get('nation') != null) {
      this.paramsQuery.nation = this.activeRoute.snapshot.queryParamMap.get('nation');
      this.searchForm.patchValue({
        nation: this.paramsQuery.nation
      });
    }

    if (this.activeRoute.snapshot.queryParamMap.get('province') != null) {
      this.paramsQuery.province = this.activeRoute.snapshot.queryParamMap.get('province');
      this.searchForm.patchValue({
        province: this.paramsQuery.province
      });
    }

    if (this.activeRoute.snapshot.queryParamMap.get('district') != null) {
      this.paramsQuery.district = this.activeRoute.snapshot.queryParamMap.get('district');
      this.searchForm.patchValue({
        district: this.paramsQuery.district
      });
    }

    if (this.activeRoute.snapshot.queryParamMap.get('village') != null) {
      this.paramsQuery.village = this.activeRoute.snapshot.queryParamMap.get('village');
      this.searchForm.patchValue({
        village: this.paramsQuery.village
      });
    }

    if (this.activeRoute.snapshot.queryParamMap.get('procedure') != null) {
      this.paramsQuery.procedure = this.activeRoute.snapshot.queryParamMap.get('procedure');
      this.searchForm.patchValue({
        procedureCtrl: this.paramsQuery.procedure
      });
    }
    if(this.logBookNumberCopies && this.logBookNumberCopies.enable)
    {
      this.columnDefinitions.push({ def: 'numberCopies', label: 'Số lượng bản sao xin cấp', show: false });
      this.columnDefinitionsEn.push({ def: 'numberCopies', label: 'Number Copies', show: false });
    }

    if (this.activeRoute.snapshot.queryParamMap.get('sortId') != null) {
      this.sortId = this.paramsQuery.sortId = this.activeRoute.snapshot.queryParamMap.get('sortId');
      this.searchForm.patchValue({
        sortCtrl: this.paramsQuery.sortId
      });
    }
  }
  async ngOnInit(): Promise<void> {
    this.mainService.setPageTitle(this.pageTitle[localStorage.getItem('language')]);
    console.log('enableSelectColumnDisplayInReportBtnGeneral' , this.enableSelectColumnDisplayInReportBtnGeneral);
   /* console.log('on logBookNumberCopies', this.logBookNumberCopies);
    console.log('on enableLogbookProcedureFilter', this.enableLogbookProcedureFilter);
    console.log('on AddOwnerStatisticalReport', this.env?.AddOwnerStatisticalReport);
    console.log('on showActionSubmit', this.env.OS_DBN?.showActionSubmit);*/
    if (this.enableSelectColumnDisplayInReportBtnGeneral == true){
      this.setColumnAtFirstLoad();
    }

    setTimeout(async () => {
      if (this.paramsQuery.acceptTo === '') {
        this.searchForm.patchValue({
          acceptTo: this.toDate
        });
      }

      if (this.paramsQuery.acceptFrom === '') {
        this.searchForm.patchValue({
          acceptFrom: this.fromDate
        });
      }

      if (this.userAgency !== null && this.userAgency !== undefined) {
        if (!!this.userAgency.parent && !!this.userAgency.parent.id && this.userAgencyCount === 1) {
          this.parentAgency = this.userAgency.parent.id;
          this.agencyId = this.userAgency.id;
        } else {
          this.parentAgency = this.userAgency.id;
          this.agencyId = this.userAgency.id;
        }

        this.keySearchSectorAgency = '&agency-id=' + this.agencyId;

        if(this.isCheckAdministrativeAgencyTagConfig){
          await this.getAdministrativeAgencyByLoggedInUser()
          if (!!this.administrativeAgency.id) {
            this.keySearchSectorAgency =  "&agency-id=" + this.administrativeAgency.id;
          }
        }

      }
      this.getListAgencyAccept(this.keywordAgencyAccept, this.currentPageAgencyAccept, this.pageSizeAgencyAccept);
      this.getListSector(this.keywordSector, this.currentPageSector, this.pageSizeSector);
      this.getListNation();
      this.getListProcedure();
    }, 500);
  }

  async getAdministrativeAgencyByLoggedInUser() {
    const userAgency = JSON.parse(localStorage.getItem('userAgency'));
    const userAgencyTag : string[] = userAgency.tag ? userAgency.tag : [];
    if (this.administrativeAgencyTag.length > 0) {
      if (userAgencyTag.length > 0) {
        const index = userAgencyTag.findIndex(item => this.administrativeAgencyTag.map(tag => tag.id).includes(item))
        if (index > -1) {
          this.administrativeAgency.id = userAgency.id;
          this.administrativeAgency.loaded = true;
          return;
        }
      }

      let parentAgencyId = userAgency.parent?.id ? userAgency.parent.id : ""; 
      let parentAgency;
      do {
        if (parentAgencyId === "") {
          this.administrativeAgency.loaded = true;
          break;
        }
        parentAgency = await this.dossierService.getAgencyFully(parentAgencyId);
        if (!!parentAgency) {
          if (!!parentAgency.tag && parentAgency.tag.length > 0) {
            const index = parentAgency.tag.findIndex(item => this.administrativeAgencyTag.map(tag => tag.id).includes(item.id));
            if (index > -1) {
              this.administrativeAgency.id = parentAgency.id;
              this.administrativeAgency.loaded = true;
              break
            }
          }
          parentAgencyId = parentAgency.parent?.id ? parentAgency.parent.id : ""; 
        }
        else {
          this.administrativeAgency.loaded = true;
          break;
        }
        console.log("dossier fee hcm q1");
      } 
      while(true);
    }
  }


  ngAfterViewInit() {
    this.cdRef.detectChanges();
    setTimeout(() => {
      // console.clear();
      this.autoSearch();
      // this.mainService.sideNav.close();
    }, 1000);

    //phucnh.it2 - IGATESUPP-37207
    if (this.enableSelectColumnDisplayInReportBtnGeneral == true){       
      this.columnForm.valueChanges.subscribe(async data => {
        let hidenColumns = [];
        let hidenColumnsExcelName = [];
        if (this.selectedLang === 'en'){
          for await (const column of this.columnDefinitionsEn) { 
            column.show = data?.[column.def];
            if (!data?.[column.def]) {
              hidenColumns.push(column.def);
              hidenColumnsExcelName.push(column.label);          
            }
          } 
        }else {
          for await (const column of this.columnDefinitions) { 
            column.show = data?.[column.def];
            if (!data?.[column.def]) {
              hidenColumns.push(column.def);
              hidenColumnsExcelName.push(column.label);          
            }
          }
        }        
         
        this._displayedColumns = this.getDisplayedColumns();   
        this.cookieService.set("logbook-list-exportExcel-hiden-columns-g", hidenColumnsExcelName.toString());
        this.cookieService.set("logbook-list-export-hiden-columns-g", hidenColumns.toString());

        this.isHidden = {
          stt: false,
          code: false,
          procedure: false,
          quantity: false,
          appliciant: false,
          address: false,
          objectType: true,
          actionSubmit: true,
          // organizationName: false,
          // organizationAddress: false,
          phone: false,
          agency: false,
          fee: false,
          acceptedDate: false,
          appointmentDate: false,
          transferDate: false,    
          receivingResultDate: false,
          returnedDate: false,
          sign: false,
          receivingKind: false,
          note: false,
          numberCopies: false,
          applyMethod: false
        }

        let temptotal = 4;
        let tempTotalReturnCol = 3;
        hidenColumns.forEach(item => {    
          switch (item) {
            case 'stt':
                return this.isHidden.stt = true;
                break;
            case 'code':
                return this.isHidden.code = true;
                break;
            case 'procedure':
                return this.isHidden.procedure = true;
                break;
            case 'quantity':
                return this.isHidden.quantity = true;
                break;
            case 'appliciant':
              return this.isHidden.appliciant = true;
              break;
            case 'address':
                return this.isHidden.address = true;
                break;
            // case 'organizationName':
            //     return this.isHidden.organizationName = true;
            //     break;
            // case 'organizationAddress':
            //     return this.isHidden.organizationAddress = true;
            //     break;
            case 'phone':
                return this.isHidden.phone = true;
                break;
            case 'agency':
                return this.isHidden.agency = true;
                break;
            case 'fee':
                return this.isHidden.fee = true;
                break;
            case 'acceptedDate':
                temptotal -= 1;
                return this.isHidden.acceptedDate = true;
                break;
            case 'appointmentDate':
                temptotal -= 1;
                return this.isHidden.appointmentDate = true;
                break;
            case 'transferDate':
                temptotal -= 1;
                return this.isHidden.transferDate = true;
                break;
            case 'receivingResultDate':
                temptotal -= 1;
                return this.isHidden.receivingResultDate = true;
                break;
            case 'returnedDate':
                tempTotalReturnCol -= 1;
                return this.isHidden.returnedDate = true;
                break;
            case 'sign':
                tempTotalReturnCol -= 1;
                return this.isHidden.sign = true;
                break;
            case 'receivingKind':
                tempTotalReturnCol -= 1;
                return this.isHidden.receivingKind = true;
                break;
            case 'note':
              return this.isHidden.note = true;
              break;
            case 'numberCopies':
              return this.isHidden.numberCopies = true;
              break;
            }
        });
        this.colDateMonthYearTotal = temptotal;
        this.colReturnResult = tempTotalReturnCol;
      });
    }
    //end phucnh.it2 - IGATESUPP-37207

  }

  ngOnDestroy() {
    this.onDestroy.next();
    this.onDestroy.complete();
  }

  setColumnAtFirstLoad() {
    const columnArrStr = this.cookieService.get("logbook-list-export-hiden-columns-g"); 
    if (!!columnArrStr) {
      columnArrStr.split(",").forEach(element => {
        // Lấy check dk để hiển thi table
        //this.columnDefinitions[this.columnDefinitions.findIndex(item => item.def === element)].show = false;
        let def = this.columnDefinitions.find(o => o.def === element);
        if(def) def.show = false;
        //Check thông tin set value của check chọn 
        this.columnForm.get(element).setValue(false);
      });
    } 

    if (!!columnArrStr){
        let hidenColumns = columnArrStr.split(",");
        let temptotal = 4;
        let tempTotalReturnCol = 3;
        hidenColumns.forEach(element => {      
          switch (element) {
            case 'stt':
                return this.isHidden.stt = true;
                break;
            case 'code':
                return this.isHidden.code = true;
                break;
            case 'procedure':
                return this.isHidden.procedure = true;
                break;
            case 'quantity':
                return this.isHidden.quantity = true;
                break;
            case 'appliciant':
              return this.isHidden.appliciant = true;
              break;
            case 'address':
                return this.isHidden.address = true;
                break;
            // case 'organizationName':
            //     return this.isHidden.organizationName = true;
            //     break;
            // case 'organizationAddress':
            //     return this.isHidden.organizationAddress = true;
            //     break;
            case 'phone':
                return this.isHidden.phone = true;
                break;
            case 'agency':
                return this.isHidden.agency = true;
                break;
            case 'fee':
                return this.isHidden.fee = true;
                break;
            case 'acceptedDate':
                temptotal -= 1;
                return this.isHidden.acceptedDate = true;
                break;
            case 'appointmentDate':
                temptotal -= 1;
                return this.isHidden.appointmentDate = true;
                break;
            case 'transferDate':
                temptotal -= 1;
                return this.isHidden.transferDate = true;
                break;
            case 'receivingResultDate':
                temptotal -= 1;
                return this.isHidden.receivingResultDate = true;
                break;
            case 'returnedDate':
                tempTotalReturnCol -= 1;
                return this.isHidden.returnedDate = true;
                break;
            case 'sign':
                tempTotalReturnCol -= 1;
                return this.isHidden.sign = true;
                break;
            case 'receivingKind':
                tempTotalReturnCol -= 1;
                return this.isHidden.receivingKind = true;
                break;
            case 'note':
              return this.isHidden.note = true;
              break;
            case 'numberCopies':
              return this.isHidden.numberCopies = true;
              break;
            }
        });
        this.colDateMonthYearTotal = temptotal; 
        this.colReturnResult = tempTotalReturnCol;
     }
    this._displayedColumns = this.getDisplayedColumns();
  }

  getDisplayedColumns(): string[] {
    return this.columnDefinitions.filter(cd => cd.show).map(cd => cd.def);
  }

  // ========================================== GET

  getListDossier(searchString) {
    this.dossierService.getListDossierPareport(searchString).subscribe(async data => {
      this.ELEMENTDATA = [];
      this.countResult = data.totalElements;
      for (let i = 0; i < data.numberOfElements; i++) {
        data.content[i].stt = this.size * (data.number) + (i + 1);
        if(!data.content[i].numberCopies) data.content[i].numberCopies = null;
        if(data.content[i]?.eForm?.data?.SoLuongBanSaoXinCap) data.content[i].numberCopies = data.content[i]?.eForm?.data?.SoLuongBanSaoXinCap;
        if(data.content[i]?.eForm?.data?.requestQtyAdd) data.content[i].numberCopies = data.content[i]?.eForm?.data?.requestQtyAdd;
        data.content[i].receivingKind = (!!data.content[i]?.dossierReceivingKind?.name && !!data.content[i]?.dossierReceivingKind?.name[0]?.name) ? data.content[i]?.dossierReceivingKind?.name[0]?.name : '';
        this.ELEMENTDATA.push(data.content[i]);

        if (data.content[i].acceptedDate !== undefined) {
          const dossierEndDate = new Date(data.content[i].acceptedDate);

          if (data.content[i].processingTime !== undefined && data.content[i].processingTime != null) {
            let processingTime = 0;
            switch (data.content[i].processingTimeUnit) {
              case 'y':
                processingTime = data.content[i].processingTime * 365;
                break;
              case 'm':
                processingTime = data.content[i].processingTime * 30;
                break;
              case 'd':
                processingTime = data.content[i].processingTime;
                break;
              case 'H:m:s':
                processingTime = data.content[i].processingTime / 24;
                break;
            }
            dossierEndDate.setDate(dossierEndDate.getDate() + processingTime);
            data.content[i].dossierEndDate = new Date(dossierEndDate);

            if (data.content[i].appointmentDate === undefined) {
              data.content[i].appointmentDate = new Date(dossierEndDate);
            }
          }
        }
      }
      this.dataSource.data = this.ELEMENTDATA;
      this.setTotalElements(data, this.paginationType);
    });
  }

  generateSearchString(spec,page, size, sort) {
    const formObj = this.searchForm.getRawValue();
    if (this.isQNM) {
      switch (this.sortId) {
        case '1': {
          sort = 'acceptedDate,asc';
          break;
        }
        case '2': {
          sort = 'acceptedDate,desc';
          break;
        }
      }
    }
    console.log('enableShowComboBoxSortByAppliedDate' , this.enableShowComboBoxSortByAppliedDate);
    if (this.enableShowComboBoxSortByAppliedDate == 1) {
      switch (this.sortId) {
        case '1': {
          sort = 'appliedDate,asc';
          break;
        }
        case '2': {
          sort = 'appliedDate,desc';
          break;
        }
        default:{
          sort = 'appliedDate,asc';
          break;
        }
      }
    }
    let search = '--search-logbook?spec=' + spec +
      '&page=' + page +
      '&size=' + size +
      '&sort=' + sort +
      '&is-form-logbook=1' +
      '&accepted-date-from=' + (formObj.acceptFrom ? this.datePipe.transform(formObj.acceptFrom, 'yyyy-MM-dd') + 'T00:00:00.000Z' : '') +
      '&accepted-date-to=' + (formObj.acceptTo ? this.datePipe.transform(formObj.acceptTo, 'yyyy-MM-dd') + 'T23:59:59.999Z' : '') +
      '&user-agency-id=' + (formObj.agencyCtrl ? formObj.agencyCtrl : this.parentAgency);

    if (formObj.sectorCtrl !== undefined && formObj.sectorCtrl !== '') {
      search += '&sector-id=' + formObj.sectorCtrl;
    }

    if (formObj.applyMethod !== undefined &&
      formObj.applyMethod !== null && formObj.applyMethod !== '') {
      search += '&apply-method-id=' + formObj.applyMethod;
    }

    if (!!formObj.keyword && formObj.keyword !== '') {
      search += '&keyword=' + encodeURIComponent(formObj.keyword.trim());
    }

    if (!!formObj.nation && formObj.nation !== '') {
      search += '&nation-id=' + encodeURIComponent(formObj.nation.trim());
    }

    if (!!formObj.province && formObj.province !== '') {
      search += '&province-id=' + encodeURIComponent(formObj.province.trim());
    }

    if (!!formObj.district && formObj.district !== '') {
      search += '&district-id=' + encodeURIComponent(formObj.district.trim());
    }

    if (!!formObj.village && formObj.village !== '') {
      search += '&ward-id=' + encodeURIComponent(formObj.village.trim());
    }

    if (formObj.procedureCtrl !== null) {
      search += '&procedure-id=' + formObj.procedureCtrl;
    }

    return search;

  }

  autoSearch() {
    this.pageIndex = Number(this.paramsQuery.page);
    this.page = Number(this.paramsQuery.page);
    this.size = Number(this.paramsQuery.size);
    const searchString = this.generateSearchString(this.paginationType, (this.page - 1), this.size, 'id,desc');
    console.log('autoSearch' , searchString);
    this.getListDossier(searchString);
  }

  generateAddress(data) {
    // return this.mainService.generateAddress(placeObj);
    const address = [];
    if (data?.address !== undefined && data?.address !== null) {
      address.push(data.address);
    }
    if (data?.village !== undefined && data?.village !== null) {
      address.push(data.village.label);
    }
    if (data?.district !== undefined && data?.district !== null) {
      address.push(data.district.label);
    }
    if (data?.province !== undefined && data?.province !== null) {
      address.push(data.province.label);
    }
    if (data?.nation !== undefined && data?.nation !== null) {
      address.push(data.nation.label);
    }
    return address.join(', ');
  }

  generateObject(data) {
    var result;

    switch (data) {
      case 1:
        result = "Cá nhân";
        break;
      case 2:
        result = "Tổ chức";
        break;
      default:
        result = "";
        break;
    }

    return result
  }

  generateActionSubmit(data) {
    var result;

    switch (data) {
      case 1:
        result = "Trực tiếp";
        break;
      case 2:
        result = "Trực tuyến";
        break;
        case 3:
        result = "Bưu chính";
        break;
        case 4:
        result = "Bưu chính công ích";
        break;
        case 5:
        result = "Smartphone";
        break;
      default:
        result = "";
        break;
    }

    return result
  }

  generateActionSubmitDBN(data) {
    let result;
    switch (data) {
      case 0:
        result = 'Trực tuyến';
        break;
      case 1:
        result = 'Trực tiếp';
        break;
      default:
        result = '';
        break;
    }
    return result;
  }

  protected filterProcedure() {
    if (!this.procedures) {
      return;
    }
    let search = this.searchProcedureCtrl.value.trim();
    this.searchProcedureKeyword = search;
    if (!search) {
      this.procedureFiltered.next(this.procedures.slice());
      return;
    } else {
      search = search.toLowerCase();
    }
    if (this.procedures.filter(proc => proc.name.toLowerCase().indexOf(search) > -1).length > 0) {
      this.procedureFiltered.next(
        this.procedures.filter(proc => proc.name.toLowerCase().indexOf(search) > -1)
      );
    } else {
      // tslint:disable-next-line: max-line-length
      const searchString =
        '?status=-1&sort=translate.name,asc&keyword=' + search +
        '&spec=page&page=0&size=50' +
        '&sector-id=' + this.paramsQuery.sector + this.keySearchProcedureAgency;
      this.listProcedurePage = 0;
      this.procedureService.getListProcedure(searchString).subscribe(data => {
        for (let i = 0; i < data.numberOfElements; i++) {
          this.listProcedure.push(data.content[i]);
        }
        this.procedures = JSON.parse(JSON.stringify(this.listProcedure, function (key, value) { return (value === null) ? "" : value; }));
      }, err => {
        console.log(err);
      });
    }
  }

  getListProcedure() {
    if (this.isFullListProcedure) {
      return;
    } else {
      const searchString =
        '?status=1&sort=translate.name,asc&keyword=' + this.searchProcedureKeyword +
        '&spec=page&page=' + this.listProcedurePage + '&size=50' +
        '&sector-id=' + this.paramsQuery.sector + this.keySearchProcedureAgency;
      this.procedureService.getListProcedure(searchString).subscribe(data => {
        this.isFullListProcedure = data.last;
        this.listProcedurePage++;
        for (let i = 0; i < data.numberOfElements; i++) {
          this.listProcedure.push(data.content[i]);
        }
        this.procedures = JSON.parse(JSON.stringify(this.listProcedure, function (key, value) { return (value === null) ? "" : value; }));
        this.procedureFiltered.next(this.procedures);
        this.searchProcedureCtrl.valueChanges.pipe(takeUntil(this.onDestroy)).subscribe(() => {
          this.filterProcedure();
        });
      }, err => {
        console.log(err);
      });
    }
  }

  generateSearchStringAll(url, sort) {
    const formObj = this.searchForm.getRawValue();
    if (this.isQNM) {
      switch (this.sortId) {
        case '1': {
          sort = 'asc';
          break;
        }
        case '2': {
          sort = 'desc';
          break;
        }
      }
    }
    let search =  url +
      'accepted-date-from=' + (formObj.acceptFrom ? this.datePipe.transform(formObj.acceptFrom, 'yyyy-MM-dd') + 'T00:00:00.000Z' : '') +
      '&accepted-date-to=' + (formObj.acceptTo ? this.datePipe.transform(formObj.acceptTo, 'yyyy-MM-dd') + 'T23:59:59.999Z' : '') +
      '&user-agency-id=' + (formObj.agencyCtrl ? formObj.agencyCtrl : this.parentAgency) +
      '&is-form-logbook=1'
    ;
    if(this.isQNM && (this.sortId == '1' || this.sortId == '2')){
      search += '&sort-qnm=' + sort;
    }

    if (formObj.sectorCtrl !== undefined && formObj.sectorCtrl !== '') {
      search += '&sector-id=' + formObj.sectorCtrl;
    }

    if (formObj.applyMethod !== undefined &&
      formObj.applyMethod !== null && formObj.applyMethod !== '') {
      search += '&apply-method-id=' + formObj.applyMethod;
    }

    if (!!formObj.keyword && formObj.keyword !== '') {
      search += '&keyword=' + encodeURIComponent(formObj.keyword.trim());
    }

    if (!!formObj.nation && formObj.nation !== '') {
      search += '&nation-id=' + encodeURIComponent(formObj.nation.trim());
    }

    if (!!formObj.province && formObj.province !== '') {
      search += '&province-id=' + encodeURIComponent(formObj.province.trim());
    }

    if (!!formObj.district && formObj.district !== '') {
      search += '&district-id=' + encodeURIComponent(formObj.district.trim());
    }

    if (!!formObj.village && formObj.village !== '') {
      search += '&ward-id=' + encodeURIComponent(formObj.village.trim());
    }

    if (formObj.procedureCtrl !== null) {
      search += '&procedure-id=' + formObj.procedureCtrl;
    }

    return search;
  }

  async getListDossierAllExcel() {
    const formObj = this.searchForm.getRawValue();
    if(await this.checkDateValid(formObj.acceptFrom, formObj.acceptTo) === false){
      return;
    }                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             
    this.waitingDownloadExcel = true;
    let searchString = this.generateSearchStringAll((this.env?.enableLogbookDienBien ? '--export-excel-logbook?' : '--export-logbook?'), 'id,desc');
    if(this.env?.enableLogbookDienBien){
      let rootAgencyName: any = '';
      const userAgency = JSON.parse(localStorage.getItem('userAgency'));
      if (userAgency !== null) {
        rootAgencyName = userAgency.name.toUpperCase();
      } else {
        rootAgencyName = this.config.rootAgency.trans.vi.name.toUpperCase();
      }
      searchString += '&agencyName=' + rootAgencyName +
                      '&envParams=' + (this.env?.AddOwnerStatisticalReport ? 1 : 0 )
      this.dossierService.getListDossierLogBook(searchString).subscribe(async data => {
        var blob = new Blob([data], {type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"});
        var objectUrl = URL.createObjectURL(blob);
        window.open(objectUrl);
        this.waitingDownloadExcel = false;
      },error => {
        this.waitingDownloadExcel = false;
      })
    }else{
      this.dossierService.getListDossierPareport(searchString).subscribe(async data => {
      this.ELEMENTDATAALL = [];
      if (data !== undefined && data.length > 0) {
        for (let i = 0; i < data.length; i++) {
          data[i].stt = i + 1;
          data[i].receivingKind = (!!data[i].dossierReceivingKind?.name && !!data[i].dossierReceivingKind?.name[0]?.name) ? data[i].dossierReceivingKind?.name[0]?.name : '';
          this.ELEMENTDATAALL.push(data[i]);

          if (data[i].acceptedDate !== undefined) {
            const dossierEndDate = new Date(data[i].acceptedDate);
            if (data[i].processingTime !== undefined && data[i].processingTime != null) {
              let processingTime = 0;
              switch (data[i].processingTimeUnit) {
                case 'y':
                  processingTime = data[i].processingTime * 365;
                  break;
                case 'm':
                  processingTime = data[i].processingTime * 30;
                  break;
                case 'd':
                  processingTime = data[i].processingTime;
                  break;
                case 'H:m:s':
                  processingTime = data[i].processingTime / 24;
                  break;
              }
              dossierEndDate.setDate(dossierEndDate.getDate() + processingTime);
              data[i].dossierEndDate = new Date(dossierEndDate);

              if (data[i].appointmentDate === undefined) {
                data[i].appointmentDate = new Date(dossierEndDate);
              }
            }
          }
        }
      }
      this.dataSourceAll.data = this.ELEMENTDATAALL;
      this.generateExcelData();
      this.waitingDownloadExcel = false;
    }, error => {
      console.log(error);
      this.waitingDownloadExcel = false;
    });
    }

  }

  async generateExcelData() {
    this.excelData = [];
    await this.ELEMENTDATAALL.forEach(elem => {
      const dataObj = {
        blank: '',
        stt: elem.stt,
        code: '',
        procedure: '',
        quantity: 1,
        ownerFullname: '',
        appliciant: '',
        address: '',
        phone: '',
        object: '',
        actionSubmit: '',
        agency: '',
        fee: '',
        acceptedDate: '',
        appointmentDate: '',
        transferDate: '',
        receivingResultDate: '',
        returnedDate: '',
        sign: '',
        receivingKind: '',
        note: ' ',
        contentOfRequestResolution: '',
        addressOfTheLandPlot: '',
        numberCopies: '',
      };

      if(!this.env?.AddOwnerStatisticalReport){
        delete dataObj.ownerFullname;
      }

      if (elem.code !== undefined) {
        dataObj.code = elem.code;
      }
      if (elem.procedure.translate !== undefined) {
        if (elem.procedure.translate.name !== undefined) {
          dataObj.procedure = elem.procedure.translate.name;
        }
      }
      if (elem.applicant !== undefined && elem.applicant.data !== undefined) {
        if (this.env?.AddOwnerStatisticalReport) {
          if (elem?.applicant?.data?.ownerFullname) {
            dataObj.ownerFullname = elem.applicant.data.ownerFullname;
          }
          if (elem?.applicant?.data?.fullname) {
            dataObj.appliciant = elem.applicant.data.fullname;
          }
        }else{
          if (elem.applicant.data.ownerFullname !== undefined) {
            dataObj.appliciant = elem.applicant.data.ownerFullname;
          }
        }
        if (this.env?.OS_HCM?.showAmountDossierFeeAndFullName) {
          dataObj.appliciant = elem.applicant.data?.fullname ? elem.applicant.data.fullname : elem.applicant.data?.fullName;
          if (!!elem.applicant.data?.organization && elem.applicant.data?.organization !== ''){
            dataObj.appliciant = dataObj.appliciant + ' - ' + elem.applicant.data?.organization;
          }
        }
        dataObj.address = this.generateAddress(elem.applicant.data);

        if (elem.applicant.data.phoneNumber !== undefined) {
          dataObj.phone = elem.applicant.data.phoneNumber;
        }
      }
      if (elem.feeData !== undefined && !this.env?.AddOwnerStatisticalReport && !this.env?.OS_HCM?.showAmountDossierFeeAndFullName) {
        elem.feeData.forEach((element, index) => {
          dataObj.fee += element;
          if (index < elem.feeData.length - 1) {
            dataObj.fee += ', ';
          }
        });
      }
      if (elem.feeTypeAndAmount !== undefined && this.env?.OS_HCM?.showAmountDossierFeeAndFullName){
        elem.feeTypeAndAmount.forEach((element, index) => {
          dataObj.fee += element;
          if (index < elem.feeTypeAndAmount.length - 1) {
            dataObj.fee += '\n';
          }
        });
      }
      if (elem.amountDossierFee !== undefined && this.env?.AddOwnerStatisticalReport) {
        dataObj.fee = elem?.amountDossierFee + "";
      }
      if (elem.agency !== undefined) {
        if (elem.agency.name !== undefined) {
          dataObj.agency = (elem?.task?.length > 0 && elem?.task[0]?.agency?.parent?.name?.length > 0) ? elem?.task[0]?.agency?.parent?.name[0]?.name : "";
        }
      }
      // qni config
      if(this.env.OS_QNI?.columnMoreShowInLogbook?.includes('object')){
        dataObj.object = this.generateObject(elem?.applicant?.data?.chonDoiTuong);
      }
      if(this.env.OS_QNI?.columnMoreShowInLogbook?.includes('actionSubmit')){
        dataObj.actionSubmit = this.generateActionSubmit(elem?.applicant?.data?.hinhThucNop);
      }

      if (this.env.OS_DBN?.showActionSubmit) {
        if (elem?.applyMethod?.id != null && elem?.applyMethod?.id != undefined){
          dataObj.actionSubmit = this.generateActionSubmitDBN(elem?.applyMethod?.id);
        }else{
          dataObj.actionSubmit = "";
        }
      }

      if (elem.acceptedDate !== undefined) {
        dataObj.acceptedDate = this.datePipe.transform(elem.acceptedDate, 'dd/MM/yyyy HH:mm:ss');
      }
      if (elem.appointmentDate !== undefined) {
        dataObj.appointmentDate = this.datePipe.transform(elem.appointmentDate, 'dd/MM/yyyy HH:mm:ss');
      }
      if (elem.acceptedDate !== undefined) {
        dataObj.transferDate = this.datePipe.transform(elem.acceptedDate, 'dd/MM/yyyy HH:mm:ss');
      }
      if (elem.completedDate !== undefined) {
        dataObj.receivingResultDate = this.datePipe.transform(elem.completedDate, 'dd/MM/yyyy HH:mm:ss');
      }
      if (elem.returnedDate !== undefined) {
        dataObj.returnedDate = this.datePipe.transform(elem.returnedDate, 'dd/MM/yyyy HH:mm:ss');
      }
      if (elem.sign !== undefined) {
        dataObj.sign = this.datePipe.transform(elem.sign, 'dd/MM/yyyy HH:mm:ss');
      }
      if (elem.receivingKind !== undefined){
        dataObj.receivingKind = elem.receivingKind;
      }

      // qni config
      if (this.env.OS_QNI?.statisticalTrackingNumber?.length > 0) {
        if (elem.applicant.data.note) {
          dataObj.note = elem.applicant.data.note;
        }
      } else {
        if (elem.note !== undefined) {
          dataObj.note = elem.note;
        }
      }

      // qni config
      if (this.env.OS_QNI?.statisticalTrackingNumber?.length > 0) {
        if (elem.applicant.data.noidungyeucaugiaiquyet !== undefined) {
          dataObj.contentOfRequestResolution = elem.applicant.data.noidungyeucaugiaiquyet;
        }

        var tempAddressOfTheLandPlot = [];
        if (elem.applicant.data.diaChiThuaDat) {
          tempAddressOfTheLandPlot.push(elem.applicant.data.diaChiThuaDat);
        }

        if (elem.applicant?.data.village2?.label) {
          tempAddressOfTheLandPlot.push(elem.applicant?.data.village2?.label);
        }

        if (elem.applicant?.data.district2?.label) {
          tempAddressOfTheLandPlot.push(elem.applicant?.data.district2?.label);
        }

        if (elem.applicant?.data.province2?.label) {
          tempAddressOfTheLandPlot.push(elem.applicant?.data.province2?.label);
        }

        dataObj.addressOfTheLandPlot = tempAddressOfTheLandPlot.join(", ");

      }

      this.excelData.push(dataObj);
    });
    this.exportToExcel();

  }

  async onConfirmSearch() {
    console.log('onConfirmSearch');
    const formObj = this.searchForm.getRawValue();
    const checkDate = await this.checkDateValid(formObj.acceptFrom, formObj.acceptTo);
    if (checkDate) {
      const searchString = this.generateSearchString(this.paginationType, 0, this.size, 'id,desc');
      this.paramsQuery = {
        page: '1',
        size: this.size.toString(),
        acceptFrom: (formObj.acceptFrom ? this.datePipe.transform(formObj.acceptFrom, 'dd/MM/yyyy') : ''),
        acceptTo: (formObj.acceptTo ? this.datePipe.transform(formObj.acceptTo, 'dd/MM/yyyy') : ''),
        agency: formObj.agencyCtrl,
        applyMethod: formObj.applyMethod,
        sector: formObj.sectorCtrl,
        keyword: formObj.keyword,
        nation: formObj.nation,
        province: formObj.province,
        district: formObj.district,
        village: formObj.village,
        procedure: formObj.procedureCtrl,
        sortId: this.sortId,
      };
     // console.log(this.paramsQuery.acceptFrom);
     // console.log(this.paramsQuery.acceptTo);
      this.pageIndex = 1;
      this.page = 1;

      this.router.navigate([], {
        queryParams: {
          page: 1,
          size: this.size.toString(),
          acceptFrom: (formObj.acceptFrom ? this.datePipe.transform(formObj.acceptFrom, 'dd/MM/yyyy') : ''),
          acceptTo: (formObj.acceptTo ? this.datePipe.transform(formObj.acceptTo, 'dd/MM/yyyy') : ''),
          agency: formObj.agencyCtrl,
          applyMethod: formObj.applyMethod,
          sector: formObj.sectorCtrl,
          keyword: formObj.keyword,
          nation: formObj.nation,
          province: formObj.province,
          district: formObj.district,
          village: formObj.village,
          procedure: formObj.procedureCtrl,
          sortId: this.sortId,
        }
      });
    /* console.log('call router');
      console.log((formObj.acceptFrom ? this.datePipe.transform(formObj.acceptFrom, 'dd/MM/yyyy') : ''));
      console.log((formObj.acceptTo ? this.datePipe.transform(formObj.acceptTo, 'dd/MM/yyyy') : ''));
      console.log('gọi get list dossi');
      console.log(searchString);*/
      this.getListDossier(searchString);
    }
  }

  checkDateValid(startDate, endDate) {
    let toDate = new Date(endDate);
    toDate.setHours(23);
    toDate.setMinutes(59);
    toDate.setSeconds(59);
    toDate.setMilliseconds(165);
    return new Promise((resolve) => {
      try {
        if (startDate === null || toDate === null) {
          const msgObj = {
            vi: 'Vui lòng nhập đúng định dạng!',
            en: 'Please enter the correct format!'
          };
          this.snackbarService.openSnackBar(0, '', msgObj[this.selectedLang], 'error_notification', this.config.expiredTime);
          resolve(false);
        } else if (startDate.getTime() > toDate.getTime()) {
          const msgObj = {
            vi: 'Ngày bắt đầu phải nhỏ hơn ngày kết thúc!',
            en: 'Start date must be lesser than end date!'
          };
          this.snackbarService.openSnackBar(0, '', msgObj[this.selectedLang], 'error_notification', this.config.expiredTime);
          resolve(false);
        } else {
          resolve(true);
        }
      } catch (error) {
        resolve(true);
      }
    });
  }

  paginate() {
    const searchString = this.generateSearchString(
      this.paginationType,
      (this.pageIndex - 1),
      this.size,
      'dueDate,asc');
    this.getListDossier(searchString);
    this.router.navigate([], {
      queryParams: {
        page: this.pageIndex,
        size: this.size,
        acceptFrom: (this.paramsQuery.acceptFrom ? this.datePipe.transform(this.paramsQuery.acceptFrom, 'dd/MM/yyyy') : ''),
        acceptTo: (this.paramsQuery.acceptTo ? this.datePipe.transform(this.paramsQuery.acceptTo, 'dd/MM/yyyy') : ''),
        agency: this.paramsQuery.agency,
        applyMethod: this.paramsQuery.applyMethod,
        sector: this.paramsQuery.sector,
        keyword: this.paramsQuery.keyword,
        nation: this.paramsQuery.nation,
        province: this.paramsQuery.province,
        district: this.paramsQuery.district,
        village: this.paramsQuery.village,
        procedure: this.paramsQuery.procedure,
        sortId: this.paramsQuery.sortId,
      }
    });
    // document.querySelector('.mat-sidenav-content').scrollTo({
    //   top: 0,
    //   left: 0,
    //   behavior: 'smooth'
    // });
  }

  print() {
    this.waitingDownloadPrint = true;
    const searchString = this.generateSearchStringAll('--export-logbook?','id,desc');
    this.dossierService.getListDossier(searchString).subscribe(async data => {
      this.ELEMENTDATAALL = [];
      if (data !== undefined && data.length > 0) {
        for (let i = 0; i < data.length; i++) {
          data[i].stt = i + 1;
          data[i].receivingKind = (!!data[i].dossierReceivingKind?.name && !!data[i].dossierReceivingKind?.name[0]?.name ) ? data[i].dossierReceivingKind?.name[0]?.name : '';
          this.ELEMENTDATAALL.push(data[i]);

          if (data[i].acceptedDate !== undefined) {
            const dossierEndDate = new Date(data[i].acceptedDate);
            if (data[i].processingTime !== undefined && data[i].processingTime != null) {
              let processingTime = 0;
              switch (data[i].processingTimeUnit) {
                case 'y':
                  processingTime = data[i].processingTime * 365;
                  break;
                case 'm':
                  processingTime = data[i].processingTime * 30;
                  break;
                case 'd':
                  processingTime = data[i].processingTime;
                  break;
                case 'H:m:s':
                  processingTime = data[i].processingTime / 24;
                  break;
              }
              dossierEndDate.setDate(dossierEndDate.getDate() + processingTime);
              data[i].dossierEndDate = new Date(dossierEndDate);

              if (data[i].appointmentDate === undefined) {
                data[i].appointmentDate = new Date(dossierEndDate);
              }
            }
          }
        }
      }
      this.dataSourceAll.data = this.ELEMENTDATAALL;
      this.waitingDownloadPrint = false;
      setTimeout(() => {
        if(this.logBookPrintFormatter){
          // print have format
          //format hiveColumns and format noHive view table difference
          //console.log('print divHiveColumns');
          this.setAgencyTitle();
          document.getElementById("agencyTitle").innerHTML = this.agencyTitle;
          document.getElementById("subAgencyTitle").innerHTML = this.subAgencyTitle;
         // this.printerService.printDiv('printDivHideColumns');
          if(this.enableSelectColumnDisplayInReportBtnGeneral==true){
            // table have hive colunm
            this.printerService.printDiv('printDivHideColumns');
          }else{
            //noHive
            this.printerService.printDiv('printDiv');
          }
        }else{
          // print no format print all column
          //console.log('print Div');
          this.printerService.printDiv('printDiv');
        }
    }, 1000);
    }, error => {
      console.log(error);
      this.waitingDownloadPrint = false;
    });
  }
  private setAgencyTitle(){
    let agencyParentName = JSON.parse(localStorage.getItem("userExperienceAgency"))?.agency?.parent?.name;
    let agencyName = JSON.parse(localStorage.getItem("userExperienceAgency"))?.agency?.name?.toUpperCase()
    let arr = agencyParentName.split(" - ");
    if(arr.length<2) return
    if(!arr[1].toLowerCase().startsWith("sở") && !arr[1].toLowerCase().startsWith("ban")){
        if(agencyName.includes("THÀNH PHỐ ")){
          this.agencyTitle = `UBND THÀNH PHỐ ${agencyName.split("THÀNH PHỐ ")[1]}`
        }else if(agencyParentName.toLowerCase().includes("THỊ XÃ")){
          this.agencyTitle = `UBND THỊ XÃ ${agencyName.split("THỊ XÃ ")[1]}`
        }else{
          this.agencyTitle = `UBND HUYỆN ${agencyName.split("HUYỆN ")[1]}`
        }
        this.subAgencyTitle = "BỘ PHẬN TIẾP NHẬN VÀ TRẢ KẾT QUẢ"
    }
  }
  exportToExcel() {
    const newDate = tUtils.newDate();
    const formObj = this.searchForm.getRawValue();
    let fromDateExcel = '';
    let toDateExcel = '';
    let reportHeading = '';
    let headersArray = [];
    let groupHeadersArray = [];
    let excelFileName = '';
    const sheetName = '';
    let agencyName = '';
    let subAgencyName = '';
    let nationName = '';
    let subNationName = '';

    if (formObj.fromDate != null || formObj.fromDate !== '') {
      fromDateExcel = this.datepipe.transform(formObj.fromDate, 'dd/MM/yyyy');
    }
    if (formObj.toDate != null || formObj.toDate !== '') {
      toDateExcel = this.datepipe.transform(formObj.toDate, 'dd/MM/yyyy');
    }
    if (this.selectedLang === 'vi') {
      const userAgency = JSON.parse(localStorage.getItem('userAgency'));
      let rootAgencyName: any = '';
      if (userAgency !== null) {
        rootAgencyName = userAgency.name.toUpperCase();
      } else {
        rootAgencyName = this.config.rootAgency.trans.vi.name.toUpperCase();
      }
      agencyName = rootAgencyName;
      subAgencyName = 'BỘ PHẬN TIẾP NHẬN VÀ TRẢ KẾT QUẢ';
      nationName = 'CỘNG HÒA XÃ HỘI CHỦ NGHĨA VIỆT NAM';
      subNationName = 'Độc lập - Tự do - Hạnh phúc';
      reportHeading = 'SỔ THEO DÕI HỒ SƠ';
      excelFileName = 'Thong ke so theo doi ' + this.datepipe.transform(newDate, 'dd/MM/yyyy');
      headersArray = ['', 'TT', 'Mã hồ sơ', 'Tên TTHC', 'Số lượng hồ sơ (bộ)', !this.env?.AddOwnerStatisticalReport ?'Tên cá nhân, tổ chức' : 'Chủ hồ sơ', 'Địa chỉ', 'Số điện thoại','Loại đối tượng', 'Hình thức nộp hồ sơ', 'Cơ quan chủ trì giải quyết', 'Lệ phí', 'Nhận hồ sơ', 'Hẹn trả kết quả', 'Chuyển hồ sơ đến cơ quan giải quyết', 'Nhận kết quả từ cơ quan giải quyết', 'Ngày, tháng, năm', 'Ký nhận', 'Phương thức nhận kết quả', 'Ghi chú', 'Nội dung yêu cầu giải quyết', 'Địa chỉ thửa đất'];

      
      if(this.logBookPrintFormatter){
        this.setAgencyTitle();
        agencyName = this.agencyTitle;
        subAgencyName = this.subAgencyTitle;
      }

      //phucnh.it2 - IGATESUPP-37207
      if (this.enableSelectColumnDisplayInReportBtnGeneral == true){
        console.log('exportToExcel()....');
        headersArray = ['', 'Số thứ tự', 'Mã số hồ sơ', 'Tên TTHC', 'Số lượng hồ sơ (bộ)', !this.env?.AddOwnerStatisticalReport ?'Tên cá nhân/ tổ chức' : 'Chủ hồ sơ', 'Địa chỉ', 'Số điện thoại', 'Cơ quan chủ trì giải quyết', 'Lệ phí', 'Nhận hồ sơ', 'Hẹn trả kết quả', 'Chuyển hồ sơ đến cơ quan giải quyết', 'Nhận kết quả từ cơ quan giải quyết','Ngày trả kết quả', 'Ký nhận', 'Phương thức nhận kết quả', 'Ghi chú'];
      
        // hidden columns title          
        const columnExcelArrStr = this.cookieService.get("logbook-list-exportExcel-hiden-columns-g"); 
        if (!!columnExcelArrStr){ 
          let hidenColumnsExcel = columnExcelArrStr.split(","); 
          headersArray = headersArray.filter( function( el ) {        
            return hidenColumnsExcel.indexOf( el ) < 0;
          } );
        }
      }
      //End phucnh.it2 - IGATESUPP-37207

      if(this.env.AddOwnerStatisticalReport){
        headersArray.splice(6, 0, "Người nộp");
      }

      groupHeadersArray = ['Ngày, tháng, năm', 'Trả kết quả']; 

      //phucnh.it2 - IGATESUPP-37207
      if (this.enableSelectColumnDisplayInReportBtnGeneral == true){ 
        const columnArrStr = this.cookieService.get("logbook-list-export-hiden-columns-g");
        let tempAcceptedDate = columnArrStr.indexOf('acceptedDate');
        let tempAppointmentDate = columnArrStr.indexOf('appointmentDate');
        let tempTransferDate = columnArrStr.indexOf('transferDate');
        let tempReceivingResultDate = columnArrStr.indexOf('receivingResultDate');
        let tempSeturnedDate = columnArrStr.indexOf('returnedDate');
        let tempSign = columnArrStr.indexOf('sign');
        
        if (tempAcceptedDate > -1 && tempAppointmentDate > -1 && tempTransferDate > -1 && tempReceivingResultDate > -1 && (tempSeturnedDate < 0 || tempSign < 0)){
          groupHeadersArray = ['', 'Trả kết quả'];
        }

        if ((tempAcceptedDate < 0 || tempAppointmentDate < 0 || tempTransferDate < 0 || tempReceivingResultDate < 0) && (tempSeturnedDate > -1 && tempSign > -1)){
          groupHeadersArray = ['Ngày, tháng, năm', ''];
        }

        if (tempAcceptedDate > -1 && tempAppointmentDate > -1 && tempTransferDate > -1 && tempReceivingResultDate > -1 && tempSeturnedDate > -1 && tempSign > -1 ){
          groupHeadersArray = ['', ''];
        }
    }
    //End phucnh.it2 - IGATESUPP-37207


    } else if (this.selectedLang === 'en') {
      const userAgency = JSON.parse(localStorage.getItem('userAgency'));
      let rootAgencyName: any = '';
      if (userAgency !== null) {
        rootAgencyName = userAgency.name.toUpperCase();
      } else {
        rootAgencyName = this.config.rootAgency.trans.en.name.toUpperCase();
      }
      agencyName = rootAgencyName;
      subAgencyName = 'RESULTS RECEIVING AND RULING DEPARTMENT';
      nationName = 'SOCIALIST REPUBLIC OF VIETNAM';
      subNationName = 'Independence - Freedom - Happiness';
      reportHeading = 'DOSSIER LOGBOOK';
      excelFileName = 'Logbook ' + this.datepipe.transform(newDate, 'dd/MM/yyyy');
      headersArray = ['', 'No.', 'Dossier code', 'TTHC name', 'Dossier quantity (sets)', !this.env?.AddOwnerStatisticalReport ? 'Name of person/ organization' : 'Owner profile', 'Address', 'Phone number', 'Object type', 'Process of submitting paperwork', 'Processing agency', 'Fee', 'Receiving dossier', 'Appointment to return results',
        'Transfer of dossier to resolution agency', 'Receiving result from agency', 'Date, month, year', 'Signed', 'Note', 'Content Of Request Resolution', 'Address Of The Land Plot'];
      
      //phucnh.it2 - IGATESUPP-37207
      if (this.enableSelectColumnDisplayInReportBtnGeneral == true){
        console.log('exportToExcel()....'); 
        headersArray = ['', 'No.', 'Dossier code', 'TTHC name', 'Dossier quantity (sets)', !this.env?.AddOwnerStatisticalReport ? 'Name of person/ organization' : 'Owner profile', 'Address', 'Phone number', 'Processing agency', 'Fee', 'Receiving dossier', 'Appointment to return results',
        'Transfer of dossier to resolution agency', 'Receiving result from agency', 'Signed', 'Note'];       
        
        // hidden columns title          
        const columnExcelArrStr = this.cookieService.get("logbook-list-exportExcel-hiden-columns-g");
        if (!!columnExcelArrStr){ 
          let hidenColumnsExcel = columnExcelArrStr.split(","); 
          headersArray = headersArray.filter( function( el ) {        
            return hidenColumnsExcel.indexOf( el ) < 0;
          } );
        }
      }
      //phucnh.it2 - IGATESUPP-37207

        if(this.env.AddOwnerStatisticalReport){
        headersArray.splice(6, 0, "Applicant");
      }
      groupHeadersArray = ['Date, month, year', 'Returns results']; 

      //phucnh.it2 - IGATESUPP-37207
      if (this.enableSelectColumnDisplayInReportBtnGeneral == true){ 
        const columnArrStr = this.cookieService.get("logbook-list-export-hiden-columns-g");
        let tempAcceptedDate = columnArrStr.indexOf('acceptedDate');
        let tempAppointmentDate = columnArrStr.indexOf('appointmentDate');
        let tempTransferDate = columnArrStr.indexOf('transferDate');
        let tempReceivingResultDate = columnArrStr.indexOf('receivingResultDate');
        let tempSeturnedDate = columnArrStr.indexOf('returnedDate');
        let tempSign = columnArrStr.indexOf('sign');
        
        if (tempAcceptedDate > -1 && tempAppointmentDate > -1 && tempTransferDate > -1 && tempReceivingResultDate > -1 && (tempSeturnedDate < 0 || tempSign < 0)){          
          groupHeadersArray = ['', 'Returns results'];
        }

        if ((tempAcceptedDate < 0 || tempAppointmentDate < 0 || tempTransferDate < 0 || tempReceivingResultDate < 0) && (tempSeturnedDate > -1 && tempSign > -1)){          
          groupHeadersArray = ['Date, month, year', ''];
        }

        if (tempAcceptedDate > -1 && tempAppointmentDate > -1 && tempTransferDate > -1 && tempReceivingResultDate > -1 && tempSeturnedDate > -1 && tempSign > -1 ){
          groupHeadersArray = ['', ''];
        }
      }
      //End phucnh.it2 - IGATESUPP-37207

    }

    if (this.enableSelectColumnDisplayInReportBtnGeneral == true){
      this.exportExcelService.exportLogbookExcel37207(
        reportHeading,
        headersArray,
        groupHeadersArray,
        this.excelData,
        excelFileName,
        sheetName,
        agencyName,
        subAgencyName,
        nationName,
        subNationName
      );
    }else {
      this.exportExcelService.exportLogbookExcel(
        reportHeading,
        headersArray,
        groupHeadersArray,
        this.excelData,
        excelFileName,
        sheetName,
        agencyName,
        subAgencyName,
        nationName,
        subNationName,
        this.env.AddOwnerStatisticalReport ? true : ""
      );
    }
    
  }

  getListSector(keyword = '', page = null, size = null) {
    // tslint:disable-next-line:max-line-length
    const searchString = '?keyword=' + keyword + '&page=' + page + '&size=1000'  + '&spec=slice&sort=name.name,asc&status=1' + this.keySearchSectorAgency;
    this.procedureService.getListSector(searchString).subscribe(res => {
      if (page === 0) {
        this.listSector = res.content;
      } else {
        this.listSector = this.listSector.concat(res.content);
      }
      this.totalPagesSector = res.totalPages;
      // this.listSector.sort((a, b) => a.name.localeCompare(b.name));
      this.sectors = JSON.parse(JSON.stringify(this.listSector).replace(/null/g, '""'));
      this.sectorFiltered.next(this.sectors);
      this.searchSectorCtrl.valueChanges.pipe(takeUntil(this.onDestroy)).subscribe(() => {
        this.filterSector();
      });
      /*console.log('this.listSector');
      console.log(this.listSector);
      console.log('this.sectorFiltered');
      console.log(this.sectorFiltered);*/
    }, err => {
      console.log(err);
    });
  }
  getListSectorScroll() {
    this.currentPageSector += 1;
    this.getListSector(this.keywordSector, this.currentPageSector, this.pageSizeSector);
  }

  protected filterSector() {
    if (!this.sectors) {
      return;
    }
    let search = this.searchSectorCtrl.value.trim();
    this.searchSectorKeyword = search;
    if (!search) {
      this.sectorFiltered.next(this.sectors.slice());
      return;
    } else {
      search = search.toLowerCase();
    }
    if (this.sectors.filter(sec => sec.name.toLowerCase().indexOf(search) > -1).length > 0) {
      this.sectorFiltered.next(
        this.sectors.filter(sec => sec.name.toLowerCase().indexOf(search) > -1)
      );
    } else {
      this.currentPageSector = 0;
      const searchString = '?keyword=' + search + '&page=' + this.currentPageSector + '&size=' + 1000 + '&spec=slice&sort=name.name,asc&status=1' + this.keySearchSectorAgency;
      this.procedureService.getListSector(searchString).subscribe(data => {
        for (let i = 0; i < data.numberOfElements; i++) {
          this.listSector.push(data.content[i]);
        }
        // this.listSector.sort((a, b) => a.name.localeCompare(b.name));
        this.sectors = JSON.parse(JSON.stringify(this.listSector).replace(/null/g, '""'));
      }, err => {
        console.log(err);
      });
    }
  }

  sectorChange(event) {
    this.paramsQuery.sector = event.value;
    this.router.navigate([], {
      queryParams: {
        sector: event.value
      },
      queryParamsHandling: 'merge'
    });
    this.listProcedure = [];
    this.isFullListProcedure = false;
    this.listProcedurePage = 0;
    this.getListProcedure();
  }

  applyMethodChange(event) {
    this.paramsQuery.applyMethod = event.value;
    this.router.navigate([], {
      queryParams: {
        applyMethod: event.value
      },
      queryParamsHandling: 'merge'
    });
  }

  changeAgencyAccept() {
    const formObject = this.searchForm.getRawValue();
    if (!!formObject.agencyCtrl && formObject.agencyCtrl !== '') {
      this.searchForm.patchValue({
        sectorCtrl: '',
      });
      this.keySearchSectorAgency = '&agency-id=' + formObject.agencyCtrl;
      this.keywordSector = '';
      this.totalPagesSector = 0;
      this.currentPageSector = 0;
      this.pageSizeSector = 1000;
      this.listSector = [];
      this.getListSector(this.keywordSector, this.currentPageSector, this.pageSizeSector);
    } else {

      this.searchForm.patchValue({
        sectorCtrl: '',
      });
      if (this.parentAgency !== '') {
        this.keySearchSectorAgency = '&agency-id=' + this.parentAgency;
      }
      else {
        this.keySearchSectorAgency = '';
      }
      this.keywordSector = '';
      this.totalPagesSector = 0;
      this.currentPageSector = 0;
      this.pageSizeSector = 1000;
      this.listSector = [];
      this.getListSector(this.keywordSector, this.currentPageSector, this.pageSizeSector);
    }
  }

  // Search Function
  getAgencyScroll() {
    this.currentPageAgencyAccept += 1;
    this.getListAgencyAccept(this.keywordAgencyAccept, this.currentPageAgencyAccept, this.pageSizeAgencyAccept);
  }

  getListAgencyAccept(keyword, page, size) {
    // const userAgency = JSON.parse(localStorage.getItem('userAgency'));
    // let rootAgencyId: any = '';
    // if (userAgency !== null) {
    //   rootAgencyId = userAgency.id;
    // } else {
    //   rootAgencyId = this.config.rootAgency.id;
    // }
    // let agencyid = this.parentAgency;
    // if (this.parentAgency === '') {
    //   agencyid = this.config.rootAgency.id;
    // }
    const searchString = '?parent-id=' + this.parentAgency + '&keyword=' + keyword + '&page=' + page + '&size=' + size + '&sort=name.name,asc&status=1';

    this.procedureService.getListAgencyWithParent(searchString).subscribe(res => {
      if (page === 0) {
        this.listAgencyAccept = res.content;
      } else {
        this.listAgencyAccept = this.listAgencyAccept.concat(res.content);
      }
      this.totalPagesAgencyAccept = res.totalPages;
      //console.log(this.listAgencyAccept);
    }, err => {
      console.log(err);
    });
  }

  async resetSearchForm(type) {
    switch (type) {
      case 'agencyAccept': {
        this.currentPageAgencyAccept = 0;
        this.keywordAgencyAccept = '';
        this.getListAgencyAccept(this.keywordAgencyAccept, this.currentPageAgencyAccept, this.pageSizeAgencyAccept);

        this.searchForm.patchValue({
          sectorCtrl: '',
        });

        this.keywordSector = '';
        this.totalPagesSector = 0;
        this.currentPageSector = 0;
        this.pageSizeSector = 1000;
        this.timeOutSector = null;
        this.listSector = [];
        this.getListSector(this.keywordSector, this.currentPageSector, this.pageSizeSector);
        break;
      }
      case 'sector': {
        this.currentPageSector = 0;
        this.keywordSector = '';
        this.getListSector(this.keywordSector, this.currentPageSector, this.pageSizeSector);

        this.searchForm.patchValue({
          sectorCtrl: '',
        });
        break;
      }
      case 'procedure': {
        this.searchForm.patchValue({
          procedureCtrl: '',
        });
        this.filterProcedure();
        break;
      }
    }
  }

  onEnter(type, event) {
    switch (type) {
      case 'agencyAccept': {
        clearTimeout(this.timeOutAgencyAccept);
        this.timeOutAgencyAccept = setTimeout(async () => {
          this.keywordAgencyAccept = event.target.value;
          this.currentPageAgencyAccept = 0;
          this.getListAgencyAccept(this.keywordAgencyAccept, this.currentPageAgencyAccept, this.pageSizeAgencyAccept);

          this.searchForm.patchValue({
            sectorCtrl: '',
          });

          this.searchForm.patchValue({
            sectorCtrl: '',
          });

          this.keywordSector = '';
          this.totalPagesSector = 0;
          this.currentPageSector = 0;
          this.pageSizeSector = 1000;
          this.timeOutSector = null;
          this.listSector = [];
          this.getListSector(this.keywordSector, this.currentPageSector, this.pageSizeSector);
        }, 300);
        break;
      }
      case 'sector': {
        clearTimeout(this.timeOutSector);
        this.timeOutSector = setTimeout(async () => {
          this.keywordSector = event.target.value;
          this.currentPageSector = 0;
          this.getListSector(this.keywordSector, this.currentPageSector, this.pageSizeSector);

          this.searchForm.patchValue({
            sectorCtrl: '',
          });
        }, 300);
        break;
      }
      case 'procedure': {
        clearTimeout(this.timeOutGetListProcedure);
        this.timeOutGetListProcedure = setTimeout(async () => {
          this.filterProcedure();
        }, 300);
        break;
      }
    }
  }

  setTotalElements(data, paginationType) {
    if (paginationType === 'page') {
      this.countResult = data.totalElements;
    } else {
      if (data.last) {
        if (!!data.number || data.number === 0) {
          this.page = data.number + 1;
          this.countResult = this.size * this.page;
        } else {
          this.countResult = this.size * this.page;
        }
      } else {
        if (data.numberOfElements < this.size) {
          this.countResult = this.size * this.page;
        } else {
          this.countResult = this.countResult + this.ELEMENTDATA.length + 1;
        }
      }
    }
  }

  changePageOrSize(obj) {
    if (tUtils.hasOwnProperty(obj, 'currentPage')) {
      this.page = obj.currentPage;
      this.pageIndex = obj.currentPage;
    }
    if (tUtils.hasOwnProperty(obj, 'itemsPerPage')) {
      this.size = obj.itemsPerPage;
    }
    if (tUtils.hasOwnProperty(obj, 'totalItems')) {
      this.countResult = obj.totalItems;
    } else {
      this.paginate();
    }
  }


  nationChange(event) {

    if (this.searchForm.get('nation').value){
      this.getListProvince();
    }else{
      this.listProvince = [];
      this.listDistrict = [];
      this.listVillage = [];
    }
    this.searchForm.controls.province.reset('');
    this.searchForm.controls.district.reset('');
    this.searchForm.controls.village.reset('');
  }

  provinceChange(event) {
    if (this.administrativeLevelMode === 0) {
      this.getListDistrict();
      this.listVillage = [];
      this.searchForm.controls.district.reset('');
      this.searchForm.controls.village.reset('');
    } else if (this.administrativeLevelMode === 1) { // Xử lý load dữ liệu quận/huyện khi chọn tỉnh/thành phố
      this.getListVillage();
      this.searchForm.controls.village.reset('');
    }
  }

  districtChange(event) {
    this.getListVillage();
    this.searchForm.controls.village.reset('');
  }

  getListNation() {
    this.dossierService.getListNation().subscribe(data => {
      this.listNation = data;
      //console.log(this.listNation);
    }, err => {
      console.log(err);
    });
  }


  getListProvince() {
      this.dossierService.getListPlace(
          this.searchForm.get('nation').value,
          null,
          this.config.placeProvinceTypeId
      ).subscribe(data => {
        this.listProvince = data;
      }, err => {
        console.log(err);
      });
  }

  getListDistrict() {
    this.dossierService.getListPlace(
        this.searchForm.get('nation').value,
        this.searchForm.get('province').value,
        this.config.placeDistrictTypeId
    ).subscribe(data => {
      this.listDistrict = data;
    }, err => {
      console.log(err);
    });
  }

  getListVillage() {
    if (this.administrativeLevelMode === 0) {
      this.dossierService.getListPlace(
          this.searchForm.get('nation').value,
          this.searchForm.get('district').value,
          this.config.placeWardTypeId
      ).subscribe(data => {
        this.listVillage = data;
      }, err => {
        console.log(err);
      });
    } else if (this.administrativeLevelMode === 1) { // Xử lý load dữ liệu phường/xã khi chọn tỉnh/thành phố
      this.dossierService.getListPlaceByParent(
          this.searchForm.get('nation').value,
          this.searchForm.get('province').value,
      ).subscribe(data => {
        this.listVillage = data;
      }, err => {
        console.log(err);
      });
    }
  }

  procedureChange($event)
  {
    if(this.logBookNumberCopies && this.logBookNumberCopies.enable)
    {
      let check = false;
      if($event && $event.value){
        let pro = this.procedures.find(o => o.id == $event.value);
        if(pro){
          let id = this.logBookNumberCopies?.listProcedureCode.find(o => o == pro.code);
          if(id){
            check = true;
          }
        }
      }
      this.columnForm.get('numberCopies').setValue(check);
    }
  }

  async createReport(){
    const formObj = this.searchForm.getRawValue();
    const acceptTo =  formObj.acceptTo ? this.datePipe.transform(formObj.acceptTo, 'yyyy-MM-dd') + 'T23:59:59.999Z' : '';
    const acceptFrom = formObj.acceptFrom ? this.datePipe.transform(formObj.acceptFrom, 'yyyy-MM-dd') + 'T00:00:00.000Z' : '';
    let sumDate = Number(new Date(acceptTo)) - Number(new Date(acceptFrom));
    let constDate = Math.floor(sumDate / 1000 / 60 / 60 / 24);
    let checkAppointmentDate:any = true;
    debugger;
    if(formObj.appointmentFrom && formObj.appointmentTo){
      checkAppointmentDate = await this.checkDateValid(formObj.appointmentFrom, formObj.appointmentTo);
    }
    if(await this.checkDateValid(formObj.acceptFrom, formObj.acceptTo) === false || checkAppointmentDate === false){
      return false;
    }

    this.openCreateStatistic();
  }

  openCreateStatistic() {
    const formObj = this.searchForm.getRawValue();
    const agency =this.listAgencyAccept.filter(item => item.id == formObj.agencyCtrl);
    const sector = this.sectors.filter(item => item.id == formObj.sectorCtrl);
    const procedure = this.procedures.filter(item => item.id == formObj.procedureCtrl);
    const dialogData = new ConfirmCreateStatisticDialogModel(formObj.nation, formObj.province, formObj.district, formObj.village, formObj.acceptFrom, formObj.acceptTo, agency, sector, procedure, this.sortId, formObj.receivingDossierService, this.isHidden, this.statisticES);
    debugger;
    const dialogRef = this.dialog.open(CreateStatisticComponent, {
      minWidth: '90vw',
      maxWidth: '90vw',
      maxHeight: '120vh',
      data: dialogData,
      disableClose: true,
      autoFocus: false
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
      
  });

  }
}
