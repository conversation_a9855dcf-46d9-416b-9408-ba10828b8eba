<div fxLayout="row" fxLayoutAlign="center">
    <div class="frm_main" fxFlex="grow">
        <div class="backToPrevSite">
            <button mat-icon-button (click)="goBack()">
                <mat-icon>keyboard_backspace</mat-icon>
            </button>
            <span *ngIf="isViewer == false && isWithdraw === 0" i18n>X<PERSON> lý hồ sơ</span>
            <span *ngIf="isViewer == true || isWithdraw === 1" i18n>Chi tiết hồ sơ</span>
        </div>
        <div class="dossierDetail">
            <p class="procedureName">
                <!-- <span class="dossierStatus" *ngIf="dossierDetail[0]?.dossierStatus?.id == 5">
                    <span [ngStyle]="{'background-color': '#56565675'}"
                        *ngIf="dossierDetail[0]?.dossierStatus?.id == 0" i18n>Chờ tiếp nhận</span>
                <span [ngStyle]="{'background-color': '#ef6c00'}" *ngIf="dossierDetail[0]?.dossierStatus?.id == 1" i18n>Chờ bổ sung</span>
                <span [ngStyle]="{'background-color': '#f39c12'}" *ngIf="dossierDetail[0]?.dossierStatus?.id == 2" i18n>Đang xử lý</span>
                <span [ngStyle]="{'background-color': '#ef6c00'}" *ngIf="dossierDetail[0]?.dossierStatus?.id == 3" i18n>Đang tạm dừng</span>
                <span [ngStyle]="{'background-color': '#03A9F4'}" *ngIf="dossierDetail[0]?.dossierStatus?.id == 4" i18n>Có kết quả</span>
                <span [ngStyle]="{'background-color': '#03A9F4'}" *ngIf="dossierDetail[0]?.dossierStatus?.id == 5" i18n>Đã trả kết quả</span>
                <span [ngStyle]="{'background-color': '#DE1212'}" *ngIf="dossierDetail[0]?.dossierStatus?.id == 6" i18n>Đã hủy</span>
                </span> -->
                <!-- <span class="dossierStatus" *ngIf="currentTask[0]?.bpmProcessDefinitionTask?.name != undefined && dossierDetail[0]?.dossierStatus?.id !== 5">
                    <span [ngStyle]="{'background-color': '#f39c12'}">
                    {{currentTask[0].bpmProcessDefinitionTask.name['name']}}
                    </span>
                </span> -->
                <span class="dossierStatus">
                    <span [ngStyle]="{'background-color': '#f39c12'}">
                    {{(dossierDetail[0] != null) ? (dossierDetail[0]?.dossierTaskStatus.name) : ''}}
                    </span>
                </span>
                <span class="dossierStatus" *ngIf="checkEnableAllButtonTakeNumberWithAgency && statusProcessing != null">
                    <span [ngStyle]="{'background-color': '#f20c0c'}">
                    {{(statusProcessing != null) ? (statusProcessing) : ''}}
                    </span>
                </span>
                <span class="dossierName" *ngIf="dossierDetail[0]?.code != '' && !isViewProcessingNormal">
                    <div>
                        <div class="row"><span i18n>Hồ sơ: </span>{{dossierDetail[0]?.code}} <span *ngIf="!!dossierDetail[0]?.nationCode && dossierDetail[0]?.nationCode != ''"> ({{dossierDetail[0]?.nationCode}})</span></div>
                        <div *ngIf="isGenerateReceiptCode && isConfigureReceiptCode && isShowReceiptCode" class="row"><span>Mã số biên nhận: </span>{{dossierDetail[0].currentReceiptCode}}</div>
                    </div>
                    <div class="valid-residential-info" id="confirmResidentialInfo" *ngIf="isValidResidentialInfo">
                        <span>(Đã xác nhận danh tính hợp lệ)</span>
                    </div>
                    <div class="valid-residential-info" *ngIf="autofillEntireEfromEnable && khdtHCM == 1">
                        <span>(Kiểm tra thông tin doanh nghiệp hợp lệ)</span>
                    </div>
                </span>
                <span class="dossierName" *ngIf="dossierDetail[0]?.code != '' && isViewProcessingNormal">
                    <div>
                        <div class="row">
                            <span>Mã hồ sơ: </span>{{dossierDetail[0]?.code}}  <span> &emsp;&emsp; Lĩnh vực: </span>
                            <span>
                                {{procedureDetail[0]?.sector?.name}}
                                <span *ngIf="procedureDetail[0]?.sector?.name == undefined || procedureDetail[0]?.sector?.name == null || procedureDetail[0]?.sector?.name.trim() == ''" i18n>(Không tìm thấy bản dịch)</span>
                            </span>
                        </div>
                        <div *ngIf="isGenerateReceiptCode && isConfigureReceiptCode && isShowReceiptCode" class="row"><span>Mã số biên nhận: </span>{{dossierDetail[0].currentReceiptCode}}</div>
                    </div>
                    <div class="valid-residential-info" id="confirmResidentialInfo" *ngIf="isValidResidentialInfo">
                        <span>(Đã xác nhận danh tính hợp lệ)</span>
                    </div>
                </span>
            </p>
            <p class="procedureName">
                {{procedureDetail[0]?.name}}
                <span *ngIf="procedureDetail[0]?.name == undefined || procedureDetail[0]?.name == null || procedureDetail[0]?.name.trim() == ''" i18n>(Không tìm thấy bản dịch)</span>
            </p>
            <p class="agencyAccept"  *ngIf="!isViewProcessingNormal">
                <ng-container *ngIf="ShowParentAgencyQni;else normalagency">
                    <span *ngFor="let agency of dossierDetail[0]?.agency?.parent?.name">
                        <span *ngIf="agency.languageId == selectedLangId">
                            <mat-icon>account_balance</mat-icon>
                            {{agency.name}}
                            <span *ngIf="agency.name == undefined || agency.name == null || agency.name.trim() == ''" i18n>(Không tìm thấy bản dịch)</span>
                    </span>
                    </span>
                </ng-container>
                <ng-template #normalagency>
                    <span *ngFor="let agency of dossierDetail[0]?.agency?.name">
                        <span *ngIf="agency.languageId == selectedLangId">
                            <mat-icon>account_balance</mat-icon>
                            {{agency.name}}
                            <span *ngIf="agency.name == undefined || agency.name == null || agency.name.trim() == ''" i18n>(Không tìm thấy bản dịch)</span>
                    </span>
                    </span>
                    </ng-template>

            </p>
            <p class="procedureLevel"  *ngIf="!isViewProcessingNormal">
                <mat-icon>label_important</mat-icon>
                {{procedureDetail[0]?.level?.name}}
                <span *ngIf="procedureDetail[0]?.level?.name == undefined || procedureDetail[0]?.level?.name == null || procedureDetail[0]?.level?.name.trim() == ''" i18n>(Không tìm thấy bản dịch)</span>
            </p>
            <p class="sector"  *ngIf="!isViewProcessingNormal">
                <span i18n>Lĩnh vực: </span>
                <span>
                    {{procedureDetail[0]?.sector?.name}}
                    <span *ngIf="procedureDetail[0]?.sector?.name == undefined || procedureDetail[0]?.sector?.name == null || procedureDetail[0]?.sector?.name.trim() == ''" i18n>(Không tìm thấy bản dịch)</span>
                </span>
            </p>
            <p class="process" *ngIf="selectedProcess != null  && !isViewProcessingNormal">
                <span i18n>Quy trình: </span>
                <span *ngIf="undefindedCompleteTime === 0">
                    <span *ngIf="selectedProcess.processDefinition.processingTimeUnit == 'y'">
                        {{selectedProcess.processDefinition.processingTime}} <span i18n>năm -</span>
                </span>
                <span *ngIf="selectedProcess.processDefinition.processingTimeUnit == 'M'">
                        {{selectedProcess.processDefinition.processingTime}} <span i18n>tháng -</span>
                </span>
                <span *ngIf="selectedProcess.processDefinition.processingTimeUnit == 'd' && selectedProcess?.processDefinition?.dynamicVariable?.isProcessingTimeUnitType === true">
                    {{selectedProcess?.processDefinition?.processingTime}} <span i18n>ngày làm việc -</span>
                </span>
                <span *ngIf="selectedProcess.processDefinition.processingTimeUnit == 'd' && selectedProcess?.processDefinition?.dynamicVariable?.isProcessingTimeUnitType !== true">
                    {{selectedProcess?.processDefinition?.processingTime}} <span i18n>ngày -</span>
                </span>
                <span *ngIf="selectedProcess.processDefinition.processingTimeUnit == 'H:m:s'">
                        {{selectedProcess.processDefinition.processingTime}} <span i18n>giờ làm việc -</span>
                </span>
                <span *ngIf="selectedProcess.processDefinition.processingTimeUnit == 'h'">
                    {{selectedProcess.processDefinition.processingTime}} <span i18n>giờ làm việc -</span>
                </span>
                <span *ngIf="selectedProcess.processDefinition.processingTimeUnit == 'm'">
                    {{selectedProcess.processDefinition.processingTime}} <span >phút -</span>
                </span>
                </span>
                <span *ngIf="undefindedCompleteTime === 1">
                    <i i18n>Không xác định thời hạn</i> -
                </span>
                <span class="nameProcess"> {{selectedProcess.processDefinition.name}}
                    <span *ngIf="selectedProcess.processDefinition.name == undefined || selectedProcess.processDefinition.name == null || selectedProcess.processDefinition.name.trim() == ''" i18n>(Không tìm thấy bản dịch)</span>
                </span>
            </p>
            <p class="process" *ngIf="this.enableSendDossierViaSoftwareQLVB && this.dossierDetail[0]?.extendHCM?.statusVPUB !== undefined">
                <span>
                    Trạng thái gửi qua QLVB:
                </span>
                <span>
                    {{this.dossierDetail[0]?.extendHCM?.statusVPUB ? 'Gửi văn bản thành công' : 'Gửi văn bản thất bại'}}
                </span>
            </p>
            <p class="sector" *ngIf="dossierDetail[0]?.currentTask[0]?.bpmProcessDefinitionTask?.name.id == env?.vnpost?.idCoKetQua && this.env?.vnpost?.idCoKetQua && dossierDetail[0]?.vnpostStatusReturn">
                <span i18n>Trạng thái VNPost: </span>
                <span>
                    {{dossierDetail[0]?.vnpostStatusReturn?.statusMessage}}
                </span>
            </p>
            <p class="sector" *ngIf="this.showAppointmentNoLLTP == 1 && dossierDetail[0]?.extendHCM?.lltpStatus?.appointmentNo && this.checkAgencyAppointmentNoLLTP">
                <span>Số phiếu LLTP: </span>
                <span>
                    {{dossierDetail[0]?.extendHCM?.lltpStatus?.appointmentNo}}
                </span>
            </p>
            <p class="sector" *ngIf="this.showDocumentNoLLTP == 1 && dossierDetail[0]?.extendHCM?.lltpStatus?.documentNo && this.checkAgencyDocumentNoLLTP">
                <span>Số phiếu LLTP: </span>
                <span>
                    {{dossierDetail[0]?.extendHCM?.lltpStatus?.documentNo}}
                </span>
            </p>
            <p class="sector" *ngIf="this.showStatusLLTP == 1 && dossierDetail[0]?.extendHCM?.lltpStatus">
                <span>Trạng thái lý lịch: </span>
                <span>
                    {{dossierDetail[0]?.extendHCM?.lltpStatus?.statusMessage}}
                </span>
            </p>
            <p class="sector" *ngIf="this.showStatusHTTP == 1 && dossierDetail[0]?.extendHCM?.httpStatus">
                <span>Trạng thái hộ tịch: </span>
                <span>
                    {{dossierDetail[0]?.extendHCM?.httpStatus?.statusMessage}}
                </span>
            </p>
            <p class="sector" *ngIf="this.showStatusHTTPLTKH && dossierDetail[0]?.extendHCM?.httpLTKHStatus">
                <span>Trạng thái hộ tịch: </span>
                <span>
                    {{dossierDetail[0]?.extendHCM?.httpLTKHStatus?.statusLTKHMessage}}
                </span>
            </p>
            <p class="sector" *ngIf="this.showStatusHTTP == 1 && dossierDetail[0]?.extendQNI?.httpStatus && !dossierDetail[0]?.nationCode">
                <span>Trạng thái hộ tịch cổng tỉnh: </span>
                <span>
                    {{dossierDetail[0]?.extendQNI?.httpStatus?.statusMessage}}
                </span>
            </p>
          <p class="sector" *ngIf="isDVCLTEnable == true && dossierDetail[0]?.httpstatus?.statusMessage && dossierDetail[0]?.nationCode">
            <span>Trạng thái hộ tịch DVCLT: </span>
            <span>
                {{dossierDetail[0]?.httpstatus?.statusMessage}}
            </span>
            </p>
            <p class="sector" *ngIf="showStatusLLTPVNeID == true && dossierDetail[0]?.lltpStatusVneid?.statusMessage">
                <span>Trạng thái LLTP: </span>
                <span>
                    {{dossierDetail[0]?.lltpStatusVneid?.statusMessage}}
                </span>
            </p>
            <p class="sector" *ngIf="this.showStatusLLTP == 1 && dossierDetail[0]?.extendQNI?.lltpStatus">
                <span>Trạng thái lý lịch: </span>
                <span>
                    {{dossierDetail[0]?.extendQNI?.lltpStatus?.statusMessage}}
                </span>
            </p>
            <p class="sector" *ngIf="this.showStatusHTTP == 1 && dossierDetail[0]?.extendAGG?.httpStatus">
                <span>Trạng thái liên thông trục AG ESB dịch vụ Hộ Tịch Tư Pháp: </span>
                <span>
                    {{dossierDetail[0]?.extendAGG?.httpStatus?.statusMessage}}
                </span>
            </p>
            <p class="sector" *ngIf="dossierDetail[0]?.extendAGG?.lltpStatus">
                <span>Trạng thái liên thông trục AG ESB dịch vụ Lý Lịch Tư Pháp: </span>
                <span>
                    {{dossierDetail[0]?.extendAGG?.lltpStatus?.statusMessage}}
                </span>
            </p>
            <p class="sector" *ngIf="dossierDetail[0]?.extendAGG?.lltpStatus">
                <span>Phản hồi từ trục liên thông AG ESB: </span>
                <span>
                    Mã trạng thái:{{dossierDetail[0]?.extendAGG?.lltpStatus?.statusCodeResponse!=null?dossierDetail[0]?.extendAGG?.lltpStatus?.statusCodeResponse:"0"}}, Mô tả trạng thái:{{dossierDetail[0]?.extendAGG?.lltpStatus?.statusMessageResponse!=null?dossierDetail[0]?.extendAGG?.lltpStatus?.statusMessageResponse:" Đã có lỗi xảy ra"}}
                </span>
            </p>
            <p class="sector" *ngIf="this.showStatusHTTP == 1 && dossierDetail[0]?.extendAGG?.httpStatus">
                <span>Phản hồi từ trục liên thông AG ESB:  </span>
                <span>
                    Mã trạng thái:{{dossierDetail[0]?.extendAGG?.httpStatus?.statusCodeResponse!=null?dossierDetail[0]?.extendAGG?.httpStatus?.statusCodeResponse:"0"}}, Mô tả trạng thái:{{dossierDetail[0]?.extendAGG?.httpStatus?.statusMessageResponse!=null?dossierDetail[0]?.extendAGG?.httpStatus?.statusMessageResponse:" Đã có lỗi xảy ra"}}
                </span>
            </p>
            <p class="sector" *ngIf="dossierDetail[0]?.extendAGG?.dkkdStatus">
                <span>Trạng thái liên thông trục AG ESB dịch vụ Đăng Ký Kinh Doanh: </span>
                <span>
                    {{dossierDetail[0]?.extendAGG?.dkkdStatus?.statusMessage}}
                </span>
            </p>
            <p class="sector" *ngIf="dossierDetail[0]?.extendAGG?.dkkdStatus">
                <span>Phản hồi từ trục liên thông AG ESB: </span>
                <span>
                    Mã trạng thái:{{dossierDetail[0]?.extendAGG?.dkkdStatus?.statusCodeResponse!=null?dossierDetail[0]?.extendAGG?.dkkdStatus?.statusCodeResponse:"0"}}, Mô tả trạng thái:{{dossierDetail[0]?.extendAGG?.dkkdStatus?.statusMessageResponse!=null?dossierDetail[0]?.extendAGG?.dkkdStatus?.statusMessageResponse:" Đã có lỗi xảy ra"}}
                </span>
            </p>

            <div *ngIf="this.checklienThongSoTNMTAGESB == 1 && dossierDetail[0]?.extendAGG?.listDossierStatus?.length > 0">
                <p class="sector">
                    <span>Trạng thái liên thông Sở Tài nguyên và Môi trường: </span>
                    <span>
                        {{dossierDetail[0]?.extendAGG?.listDossierStatus[dossierDetail[0]?.extendAGG?.listDossierStatus?.length - 1].tinhTrangHoSoESB}}
                    </span>
                </p>
                <p class="sector" *ngIf="dossierDetail[0]?.extendAGG?.listDossierStatus?.length > 1">
                    <span>Phòng ban xử lý: </span>
                    <span>
                        {{dossierDetail[0]?.extendAGG?.listDossierStatus[dossierDetail[0]?.extendAGG?.listDossierStatus?.length - 1].phongBanXuLyESB}}
                    </span>
                </p>
            </div>

            <div *ngIf="checkConfigPowaco === 1">
                <p class="sector">
                    <span>Trạng thái liên thông Powaco: </span>
                    <span>
                        {{dossierDetail[0]?.extendAGG?.powacoStatus?.statusMessage ? dossierDetail[0]?.extendAGG?.powacoStatus?.statusMessage : 'Chưa liên thông' }}
                    </span>
                </p>
                <p class="sector" *ngIf="dossierDetail[0]?.extendAGG?.listDossierStatus?.length > 1">
                    <span>Phòng ban xử lý: </span>
                    <span>
                        {{dossierDetail[0]?.extendAGG?.listDossierStatus[dossierDetail[0]?.extendAGG?.listDossierStatus?.length - 1].phongBanXuLyESB}}
                    </span>
                </p>
            </div>

            <p class="sector" *ngIf="this.showStatusVNPost == 1">
                <span i18n>Trạng thái VNPost: </span>
                <span>
                    {{dossierDetail[0]?.vnpostStatus?.statusMessage}}
                </span>
            </p>
            <p class="paymentMethod" *ngIf="showPaymentMethod">
                <span class="lblBold" i18n="@@paymentMethod">Hình thức thanh toán</span>:
                <span>
                    {{dossierDetail[0]?.paymentMethod?.name}}
                </span>
                <mat-icon style="cursor: pointer" (click)="onClickEdit()"
                                    *ngIf="showComboboxPaymentMethod">edit</mat-icon>
            </p>
            <p class="sector" *ngIf="showStatusSyncDossierDVCQG && statusSyncDossierDVCQG">
                <span i18n="@@statusDVCQG">Trạng thái DVCQG:</span>
                <span *ngIf="statusSyncDossierDVCQG == 1" i18n>Mới đăng ký</span>
                <span *ngIf="statusSyncDossierDVCQG == 2" i18n>Được tiếp nhận</span>
                <span *ngIf="statusSyncDossierDVCQG == 3" i18n>Không được tiếp nhận</span>
                <span *ngIf="statusSyncDossierDVCQG == 4" i18n>Đang xử lý</span>
                <span *ngIf="statusSyncDossierDVCQG == 5" i18n>Yêu cầu bổ sung giấy tờ</span>
                <span *ngIf="statusSyncDossierDVCQG == 6" i18n>Yêu cầu thực hiện nghĩa vụ tài chính</span>
                <span *ngIf="statusSyncDossierDVCQG == 7" i18n>Công dân yêu cầu rút hồ sơ</span>
                <span *ngIf="statusSyncDossierDVCQG == 8" i18n>Dừng xử lý</span>
                <span *ngIf="statusSyncDossierDVCQG == 9" i18n>Đã xử lý xong</span>
                <span *ngIf="statusSyncDossierDVCQG == 10" i18n>Đã trả kết quả</span>
            </p>
            <p class="paymentMethod" *ngIf="showAssignedCode">
                <span class="lblBold" i18n>Mã số được cấp</span>:
                <span *ngIf="isShowProceAdminMultiHCM == false" >
                    {{dossierDetail[0]?.codeTakeNumber}}
                </span>
                <span *ngIf="isShowProceAdminMultiHCM">
                    {{listCodeTakeNumber}}
                </span>
            </p>
            <p class="paymentMethod" *ngIf="allowShowStatusSyncLGSP && dossierDetail[0]?.extendHCM?.isSyncLGSPHCM">
                <span class="lblBold">Trạng thái CN</span>:
                <span>
                    {{dossierDetail[0]?.extendHCM?.isSyncLGSPHCM ? "Đồng bộ hồ sơ thành công" : ""}}
                </span>
            </p>
            <!-- <p class="paymentMethod" >
                <span class="lblBold" i18n>Mã số biên lai được cấp</span>:
                <span>
                    {{dossierDetail[0]?.codeReceiptNumber}}
                </span>
            </p> -->
            <p class="paymentMethod" *ngIf="dossierDetail[0]?.extendDLK?.lltp?.description">
                <span class="lblBold">Trạng thái LLTP</span>:
                <span>
                    {{dossierDetail[0]?.extendDLK?.lltp?.description}}
                </span>
            </p>
            <p class="paymentMethod" *ngIf="dossierDetail[0]?.extendDLK?.http?.statusMessage">
                <span class="lblBold">Trạng thái Hộ tịch tư pháp</span>:
                <span>
                    {{dossierDetail[0]?.extendDLK?.http?.statusMessage}}
                    </span>
            </p>
            <p class="paymentMethod" *ngIf="dossierDetail[0]?.extendGLI?.statusSendDoc && enableSyncQLVBDHSystem">
                <span class="lblBold">Trạng thái liên thông QLVBĐH</span>:
                <span>
                    {{dossierDetail[0]?.extendGLI?.statusSendDoc == true ? "Đồng bộ hồ sơ QLVBĐH thành công" : "Đồng bộ hồ sơ QLVBĐH thất bại"}}
                </span>
            </p>
            <p class="sector" *ngIf="dossierDetail[0]?.viettelPost && viettelPostEnable && dossierDetail[0]?.viettelPost?.vpSend && dossierDetail[0]?.viettelPost?.statusViettelPostS">
                <span class="lblBold">Trạng thái ViettelPost - Đăng ký nộp hồ sơ tại nhà: </span>
                <span>{{dossierDetail[0]?.viettelPost?.statusViettelPostS?.name}}</span>
            </p>
            <p class="sector" *ngIf="dossierDetail[0]?.viettelPost && viettelPostEnable && dossierDetail[0]?.viettelPost?.vpReceive && dossierDetail[0]?.viettelPost?.statusViettelPostR">
                <span class="lblBold">Trạng thái ViettelPost - Đăng ký nhận kết quả tại nhà: </span>
                <span>{{dossierDetail[0]?.viettelPost?.statusViettelPostR?.name}}</span>
            </p>
               <!-- Thêm mới  -->
               <div *ngIf="displayAccountInformation"  class="accountInformation">
                <p class="paymentMethod autoMarginP" >
                    <span class="lblBold" >Tên tài khoản</span>:
                      <span *ngIf="dossierDetail[0]?.applicant?.data?.declarationForm?.length !== 0">
                        {{dossierDetail[0]?.applicant?.data?.declarationForm?.fullName}}
                      </span>
                </p>
                <p class="paymentMethod autoMarginP" >
                    <span class="lblBold" >Ngày sinh</span>:
                    <span *ngIf="dossierDetail[0]?.applicant?.data?.declarationForm?.length !== 0">
                        {{dossierDetail[0]?.applicant?.data?.declarationForm?.birthDateStr}}
                      </span>

                </p>
                <p class="paymentMethod autoMarginP" >
                    <span class="lblBold " >CCCD/CMND/MST</span>:
                     <span *ngIf="dossierDetail[0]?.applicant?.data?.declarationForm?.length !== 0">
                        {{dossierDetail[0]?.applicant?.data?.declarationForm?.identifyNo}}
                      </span>
                </p>
            </div>
            <div class="select-additional"
                *ngIf="this.dossierDetail[0]?.dossierStatus?.id == 23 && this.dossierDetail[0]?.additionalRequirement?.length > 0">
                <div class="additional-row-title">
                    <span>Thông tin yêu cầu bổ sung:</span>
                </div>
                <div class="additional-row" fxLayout="row" *ngFor="let r of this.dossierDetail[0]?.additionalRequirement">
                    <div class="additional-infor" fxLayout="row" fxLayoutAlign="space-between stretch">
                        <div fxFlex="35" fxLayout="column" fxLayoutGap="10px">
                            <div class="infor-line">Ngày yêu cầu bổ sung:<span class="infor-content">{{r.waitUpdateDate | date :
                                    'dd/MM/yyyy HH:mm:ss'}}</span></div>
                            <div class="infor-line">Lý do:<span class="infor-content">{{r.processingReason}}</span></div>
                        </div>
                        <div fxFlex="2"></div>
                        <div fxFlex="63" fxLayout="column" fxLayoutGap="10px">
                            <div class="infor-line">Nội dung:<span class="infor-content">{{r.subject}}</span></div>
                            <div class="infor-line" fxLayout="row">
                                <div>File đính kèm:</div>
                                <div *ngIf="r.attachment?.length > 0" fxLayout="column">
                                    <span *ngFor="let f of r.attachment" class="file"
                                        (click)="downloadFile(f?.id, f?.name)">{{f?.name}}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="topCtrol" fxLayout="row" fxLayoutAlign="end">
            <mat-form-field appearance="outline"  fxFlex.gt-md="20" fxFlex.gt-sm="20" fxFlex.gt-xs="100" fxFlex='grow' class="listProcessingTime" *ngIf="allowShowMultiTimeProcess && !constraintTotalProcessTime && listProcessingTime.length > 1">
                <mat-label>Trường hợp xử lý</mat-label>
                <mat-select [(ngModel)]="processingTime" (ngModelChange)="onProcessingTimeChange($event)">
                    <mat-option *ngFor='let process of listProcessingTime;' [value]="process.id">
                      {{process.processingName}}
                    </mat-option>
                </mat-select>
            </mat-form-field>
            <button mat-flat-button *ngIf="oneGateBeRatingAccepter" class="btnSecondary" (click)='viewRatingHGI()'>
                <mat-icon>star</mat-icon>
                <span>Mời đánh giá</span>
            </button>
          <button mat-flat-button class="btnSecondary" *ngIf="dossierDetail[0]?.lltpStatusVneid && syncLyLichEnable" (click)="syncLLTPVNEID(dossierDetail[0]?.code)">
            <mat-icon>replay</mat-icon>
            <span>Đồng bộ kết quả lý lịch</span>
          </button>
          <button mat-flat-button class="btnSecondary" *ngIf="this.listProcedureCodeHTTP.includes(dossierDetail[0]?.procedure?.code) && !dossierDetail[0]?.nationCode" (click)="syncHTTPTinh(dossierDetail[0]?.code)">
                <mat-icon>replay</mat-icon>
                <span>Đồng bộ kết quả hộ tịch cổng tỉnh</span>
            </button>
            <button mat-flat-button class="btnSecondary" *ngIf="isDVCLTEnable && dossierDetail[0]?.isDVCLT && syncHoTichEnable && dossierDetail[0]?.nationCode" (click)="syncHTTPDVCLT(dossierDetail[0]?.code)">
              <mat-icon>replay</mat-icon>
              <span>Đồng bộ kết quả hộ tịch</span>
            </button>
            <button mat-flat-button class="btnSecondary" *ngIf="isDVCLTEnable && dossierDetail[0]?.isDVCLT && dossierDetail[0]?.nationCode" (click)="sendHTTPDVCLT(dossierDetail[0]?.code, dossierDetail[0]?.HTTPStatus?.statusCode)">
                <mat-icon>replay</mat-icon>
                <span>Gửi lại HTTP liên thông</span>
            </button>
            <!--start namds.dlc-IGATESUPP-113362-->
            <button mat-flat-button class="btnSecondary" *ngIf="isDLKResendJudicialCivil" (click)="resendJudicialCivil()">
                <mat-icon>replay</mat-icon>
                <span>Gửi lại hồ sơ sang hệ thống hộ tịch</span>
            </button>
            <!--end namds.dlc-IGATESUPP-113362-->
            <button mat-flat-button class="btnSecondary" *ngIf="showStatusLLTPVNeID == true && dossierDetail[0]?.lltpStatusVneid" (click)="sendLLTPVNeID(dossierDetail[0]?.code)">
                <mat-icon>replay</mat-icon>
                <span>Gửi lại lý lịch</span>
            </button>
            <button mat-flat-button class="btnSecondary" *ngIf="hiddenResendDossierHT == 0 && showReSendHTTPHCM" (click)="reSendHTTPHCM()">
                <mat-icon>replay</mat-icon>
                <span>Gửi lại hồ sơ hộ tịch</span>
            </button>
            <button mat-flat-button class="btnSecondary" *ngIf="showStatusLLTPVNeID == true && dossierDetail[0]?.lltpVNeIDFileurl" (click)="onViewLTTPVNeID(dossierDetail[0]?.lltpVNeIDFileurl)">
                <mat-icon>replay</mat-icon>
                <span>Thông tin biên lai thanh toán lý lịch</span>
            </button>
            <button mat-flat-button class="btnSecondary" *ngIf="checkConfigHoTichDLK == 1 && isShowBtnResendHoTich_DLK" (click)="sendHoTichDLK()">
                <mat-icon>replay</mat-icon>
                <span>Gửi lại hồ sơ hộ tịch</span>
            </button>
            <button mat-flat-button class="btnSecondary" *ngIf="isShowBtnResendLLTP_DLK" (click)="reSendLLTPDLK(this.dossierDetail[0]?.id)">
                <mat-icon>replay</mat-icon>
                <span>Gửi lại hồ sơ lý lịch</span>
            </button>
            <button mat-flat-button class="btnSecondary" (click)="sendLLTPLgspCMU()" *ngIf="this.checkHcmLyLichTuPhap_CMU && isShowBtnResendLLTP_CMU" [disabled]="this.isLoading">
                <mat-icon>replay</mat-icon>
                <span>Gửi lại PM LLTP Cà Mau</span>
            </button>
            <button mat-flat-button class="btnSecondary" *ngIf="showGrantRandomCodeBtn()" (click)="openGrantRandomCodeDialog(dossierDetail[0])">
                <mat-icon>pin</mat-icon>
                <span i18n>Cấp mã số</span>
              </button>
            <button mat-flat-button class="btnSecondary btn-Secondary" (click)='sendLLTPLgspHCM2()' *ngIf="this.checkHcmLyLichTuPhap && this.callApiLGSPAfterGotReceipt.showBtnTransferToLGSP" [disabled]="this.isLoading">
                <mat-icon>insights</mat-icon>
                <span i18n="@@sendDocumentToPMCN">Chuyển hồ sơ sang PM chuyên ngành</span>
            </button>
            <button mat-flat-button [ngStyle]="{'background-color': '#f39c12'}" class="btnSecondary" *ngIf="showSignDesk && dossierDetail[0]?.dossierStatus?.id == 4" (click)="signDeskQNM()">
                <span>Ký nhận hồ sơ</span>
            </button>
            <button [ngStyle]="{'background-color': '#f39c12'}" mat-flat-button class="btnPrimary" *ngIf="checkCivilStatusJusticeAGESB === 1"
            [disabled]="dossierDetail[0]?.extendAGG?.httpStatus?.statusCode === 1" (click)="sendHTTPAGESB()">
                <mat-icon>send</mat-icon>
                <span>Liên thông hộ tịch</span>
            </button>

            <button [ngStyle]="{'background-color': '#f39c12'}" mat-flat-button class="btnPrimary"
            *ngIf="isShowButtonReceivedILisAgg && isIlis && dossierDetail[0]?.ilis?.dossierStatusInIlis != 8 && dossierDetail[0]?.ilis?.dossierStatusInIlis != 3 && (dossierDetail[0]?.extendQNI?.isIlis == false || dossierDetail[0]?.extendQNI?.isIlis == undefined)"
            (click)="syncILIS(dossierDetail[0]?.id)">
                <mat-icon>send</mat-icon>
                <span>Tiếp nhận iLIS</span>
            </button>

            <button [ngStyle]="{'background-color': '#f39c12'}"
            mat-flat-button
            class="btnPrimary"
            *ngIf="checklienThongSoTNMTAGESB === 1"
            [disabled]="dossierDetail[0]?.extendAGG?.listDossierStatus?.length > 0"
            (click)="sendSoTNMTAGESB()">
                <mat-icon>send</mat-icon>
                <span>Liên thông Sở TNMT</span>
            </button>
    
            <button [ngStyle]="{'background-color': '#f39c12'}"
            mat-flat-button
            class="btnPrimary"
            *ngIf="checkBusinessStatusAGESB === 1"
            [disabled]="dossierDetail[0]?.extendAGG?.dkkdStatus?.statusCode === 1"
            (click)="sendDKKDAGESB()">
                <mat-icon>send</mat-icon>
                <span>Liên thông kinh doanh</span>
            </button>


            <button [ngStyle]="{'background-color': '#f39c12'}"
            mat-flat-button
            class="btnPrimary"
            *ngIf="checkCriminalRecordsAGESB === 1"
            [disabled]="dossierDetail[0]?.extendAGG?.lltpStatus?.statusCode === 1"
            (click)="sendLLTPAGESB()">
                <mat-icon>send</mat-icon>
                <span>Liên thông lý lịch tư pháp </span>
            </button>
            <button mat-flat-button class="btnSecondary" (click)='searchTrangThaiDVCLTHoTich()' *ngIf="isShowSearchTrangThaiDVCLTHoTich">
                <mat-icon>history</mat-icon>
                <span>Tra cứu DVCLT Hộ tịch</span>
            </button>
            <button [ngStyle]="{'background-color': '#f39c12'}"
                mat-flat-button
                class="btnPrimary"
                *ngIf="checkConfigPowaco === 1"
                [disabled]="dossierDetail[0]?.extendAGG?.powacoStatus?.statusCode === 1"
                (click)="sendPowacoAGESB()">
                <mat-icon>send</mat-icon>
                <span>Liên thông Powaco</span>
            </button>
            <button [ngStyle]="{'background-color': '#f39c12'}" mat-flat-button class="btnPrimary" (click)='sendIOffice()' *ngIf="dossierDetail[0]?.code != '' && canConnectedIOffice === 1 && enableIoffice">
              <mat-icon>arrow_right_alt</mat-icon>
              <span i18n="@@documentIoffice">Văn bản liên thông iOffice</span>
            </button>
            <button [ngStyle]="{'background-color': '#f39c12'}" mat-flat-button class="btnPrimary" (click)='sendIOfficeV4()' *ngIf="dossierDetail[0]?.code != '' && canConnectedIOfficeV4 === 1 && enableIofficeV4">
              <mat-icon>arrow_right_alt</mat-icon>
              <span>Văn bản liên thông iOffice</span>
            </button>
            <button mat-flat-button class="btnPrimary" *ngIf="currentTask[0]?.bpmProcessDefinitionTask?.dynamicVariable?.sendQLVB == 1"  (click)='sendQLVB()'>
                <mat-icon>forward_to_inbox</mat-icon>
                <span>{{nameButtonSendDoc}}</span>
            </button>
            <button mat-flat-button class="btnSecondary" *ngIf="(enableButtonResendLGSP || enableButtonResendLGSPHTTP) && !dossierDetail[0]?.nationCode" (click)="confirmResend()">
                <mat-icon>send</mat-icon>
                <span>Gửi lại hộ tịch cổng tỉnh</span>
            </button>
            <button mat-flat-button class="btnPrimary" *ngIf="currentTask[0].bpmProcessDefinitionTask.variable.canSendDossierToVpub == 1 && enableSendDossierViaSoftwareQLVB === 1"  (click)='sendDossierToVPUB()'>
                <mat-icon>forward_to_inbox</mat-icon>
                <span>Gửi qua QLVB</span>
              </button>
            <button mat-flat-button class="btnSecondary" *ngIf=" (this.dossierDetail[0]?.currentTask[0]?.isLast == 1 || (checkEnableAllButtonTakeNumberWithAgency == true && checkEnableUpdateButtonTakeNumber == true) ) && showButtonTakeNumber==1" (click)="addInforDialog(dossierDetail[0].id)" >
                <mat-icon>explore icon</mat-icon>
                <span *ngIf="allowChangeExpertRole == false">Cấp mã số</span>
                <span *ngIf="allowChangeExpertRole == true">Thẩm định hồ sơ</span>
            </button>
            <button mat-flat-button class="btnSecondary" (click)='lookUpCSDLQGDC()' *ngIf="!!qbhSaveInfoCitizens">
                <mat-icon>update</mat-icon>
                <span>Tra cứu thông tin CD từ CSDLQG về DC</span>
            </button>
            <button mat-flat-button class="btnSecondary" (click)='generateWord()' *ngIf="downloadDetailDossierBtn">
                <mat-icon>cloud_download</mat-icon>
                <span>Tải thông tin chi tiết hồ sơ</span>
            </button>
            <button mat-flat-button class="btnSecondary" (click)='excelDoiGPLX()' *ngIf="!!hasExcelGPLXRole && !!enableDoiGPLX">
                <mat-icon>cloud_download</mat-icon>
                <span>Xuất GPLX</span>
            </button>
            <button mat-flat-button class="btnSecondary" *ngIf="isCheckConnectIofficeV5NotLogin" (click)="sendDossierEofficeV5()">
                <mat-icon>send</mat-icon>
                <span>Gửi hồ sơ sang iOfficeV5</span>
            </button>
            <button mat-flat-button class="btnSecondary" (click)="reSyncCTDT(dossierDetail[0]?.id, dossierDetail[0]?.authenticationStatusCurrent?.id ? dossierDetail[0]?.authenticationStatusCurrent?.id : 2)" *ngIf="batLienThongCTDT && showBtnReSync && selectedProcess?.isCTDT && (listAuthStatusCanReSync?.indexOf(dossierDetail[0]?.authenticationStatusCurrent?.id) != -1 || dossierDetail[0]?.authenticationStatusCurrent?.id == false || dossierDetail[0]?.authenticationStatusCurrent?.id == undefined)">
                <mat-icon>reply</mat-icon><span>Đồng bộ lại chứng thực điện tử</span>
            </button>
            <button mat-flat-button class="btnSecondary" (click)='viewProcess()' *ngIf="dossierDetail[0]?.code != ''">
                <mat-icon>insights</mat-icon>
                <span i18n>Xem quy trình</span>
            </button>
            <button mat-flat-button class="btnSecondary" (click)="createPrintBill(qbhPhieuTiepNhan?.file?.path, qbhPhieuTiepNhan)" *ngIf="qbhPrintBtn">
                <mat-icon>print</mat-icon>
                <span>In phiếu tiếp nhận hồ sơ</span>
            </button>
            <button mat-flat-button class="btnSecondary" [matMenuTriggerFor]="animals">
                <mat-icon>print</mat-icon>
                <span *ngIf="!qbhPrintBtn">In phiếu</span>
                <span *ngIf="qbhPrintBtn">In phiếu khác</span>
                <mat-icon>keyboard_arrow_down</mat-icon>
            </button>
            <!--IGATESUPP-113272-->
            <button mat-flat-button class="btnSecondary" (click)='assignDocumentNumber()' *ngIf="dnlEnabled && !dnlDocumentNumberAssigned">
                <mat-icon *ngIf="!dnlIsAssigningNumber">numbers</mat-icon>
                <mat-icon *ngIf="dnlIsAssigningNumber" class="spinning-icon">autorenew</mat-icon>
                <span>Gán số văn bản</span>
            </button>
            <button mat-flat-button class="btnSecondary" *ngIf="dnlEnabled && dnlDocumentNumberAssigned">
                <mat-icon>check</mat-icon>
                <span>Đã gán số văn bản</span>
            </button>
            <!--IGATESUPP-113272-->
            <!-- <div *ngIf="!embedStorage">
                <button *ngIf="configs?.storage468?.saveType=='storage'"
                        (click)="onSaveToStorage()"
                        mat-flat-button class="btnSecondary">
                    <mat-icon>input</mat-icon>
                    <span>Lưu vào Kho</span>
                </button>
            </div> -->
            <app-save-form-origin-item *ngIf="!embedStorage"
                [agencyCode]="agencyLocalStorage?.code"
                [dossierId]="this.dossierId">
            </app-save-form-origin-item>

            <div *ngIf="embedStorage">
                <a routerLink="/storageIntegration"
                        mat-flat-button class="btnSecondary">
                    <mat-icon>input</mat-icon>
                    <span>Lưu vào Kho</span>
                </a>
            </div>

            <!-- <button mat-button [matMenuTriggerFor]="animals">In Phiếu</button> -->
            <!-- <button mat-flat-button class="btnPrimary" (click)="openConfirmRecallDialog()" *ngIf="canRecallDossier">
                <mat-icon>done_all</mat-icon>
                <span i18n>Thu hồi</span>
            </button> -->
            <!-- <button mat-flat-button class="btnPrimary" (click)="confirmationCompleted(dossierDetail[0]?.code)" *ngIf="isWithdraw === 0 && isTheHandler && isTaskComplete(currentTask[0].activitiTask.status) == false && showClaimTaskBtn == false && dossierDetail[0]?.dossierStatus?.id !== 3">
                <mat-icon>done_all</mat-icon>
                <span i18n>Xác nhận hoàn thành</span>
            </button> -->
            <ng-container *ngIf="qbhmenuaction == false; then normal else QBHe"> </ng-container>
            <ng-template #normal>
                <button mat-flat-button class="btnPrimary" (click)="openApprovalDialog()" *ngIf="isTaskComplete(currentTask[0].activitiTask.status) == false && requiredApprovalStatus.includes(dossierDetail[0]?.dossierStatus?.id) && haveStatusPermission && checkApprovalAgency">
                  <mat-icon>assignment_turned_in</mat-icon>
                  <span>Phê duyệt hồ sơ</span>
              </button>
            </ng-template>
            <ng-template #QBHe>
                <button mat-flat-button class="btnPrimary" (click)="openApprovalQBHDialog()" *ngIf="isTaskComplete(currentTask[0].activitiTask.status) == false && requiredApprovalStatus.includes(dossierDetail[0]?.dossierStatus?.id) && haveStatusPermission && checkApprovalAgency">
                    <mat-icon>assignment_turned_in</mat-icon>
                    <span *ngIf="dossierDetail[0]?.dossierStatus?.id == '10'">Xác nhận xin lỗi và xin gia hạn</span>
                    <span *ngIf="dossierDetail[0]?.dossierStatus?.id == '8'">Xác nhận yêu cầu bổ sung</span>
                    <span *ngIf="dossierDetail[0]?.dossierStatus?.id == '11'">Xác nhận từ chối giải quyết</span>
                    <span *ngIf="dossierDetail[0]?.dossierStatus?.id == '6' && dossierDetail[0]?.dossierTaskStatus?.id =='6151c771ba2a04299f949875' ">Xác nhận yêu cầu rút hồ sơ</span>
                </button>
            </ng-template>
            <button mat-flat-button class="btnPrimary" (click)="continueProcessing(dossierDetail[0]?.id, dossierDetail[0]?.code)"
            *ngIf="isWithdraw === 0 && isTheHandler && isTaskComplete(currentTask[0].activitiTask.status) == false && dossierDetail[0]?.dossierStatus?.id == 16 && !requiredApprovalStatus.includes(dossierDetail[0]?.dossierStatus?.id)">
                <mat-icon>arrow_right</mat-icon>
                <span>Tiếp tục xử lý</span>
            </button>
            <button mat-flat-button class="btnPrimary" (click)="showResultDocFromQLVBDH(dossierDetail[0]?.code)" *ngIf="isQLVBDH_GLI == true">
                <mat-icon>assignment_turned_in</mat-icon>
                  <span>Kết quả hồ sơ từ QLVBĐH</span>
            </button>
            <ng-container
                *ngIf="env?.vnpost?.config && (env?.vnpost?.config === '1' || env?.vnpost?.config === 1) && env?.vnpost?.resolveByStatusCode && dossierDetail[0]?.currentTask[0]?.bpmProcessDefinitionTask.name.id == env?.vnpost?.idCoKetQua && this.env?.vnpost?.idCoKetQua && dossierDetail[0]?.vnpostStatusReturn; then resolveVNPost else resolveNormal">
            </ng-container>
            <ng-template #resolveVNPost>
                <button mat-flat-button class="btnPrimary" (click)="confirmationCompleted(dossierDetail[0]?.code)"
                    *ngIf="isWithdraw === 0 && isTheHandler && isTaskComplete(currentTask[0].activitiTask.status) == false
                    && dossierDetail[0]?.dossierStatus?.id !== 1
                    && dossierDetail[0]?.dossierStatus?.id !== 3
                    && dossierDetail[0]?.dossierStatus?.id !== 14
                    && dossierDetail[0]?.dossierStatus?.id !== 16
                    && dossierDetail[0]?.dossierStatus?.id !== 12
                    && (enableProcessingPaidDossiers || dossierDetail[0]?.dossierStatus?.id !== 17)
                    && !requiredApprovalStatus.includes(dossierDetail[0]?.dossierStatus?.id)
                    && dossierDetail[0]?.vnpostStatusReturn?.statusCode?.toString() === env?.vnpost?.resolveByStatusCode
                    && selected !== '1'
                    && selected !== '2'
                    && isDisableBtnCompletedConfirm == false"
                    [disabled]="confirmationCompletedBtnCheck('disabled')"
                    [ngClass]="{ 'btnPrimaryDisabled' : qbhCheckPaid}">
                    <mat-icon>done_all</mat-icon>
                    <span i18n>Xác nhận hoàn thành</span>
                </button>
            </ng-template>
            <ng-template #resolveNormal>

              <button *ngIf="isShowTransferResponsibleUserKHA && dossierDetail[0]?.currentTask[0].isFirst == false " mat-flat-button class="btnPrimary" (click)="reassign(dossierDetail[0]?.id, dossierDetail[0]?.code, dossierDetail[0]?.currentTask, dossierDetail[0]?.procedure?.id)">
                <mat-icon>edit</mat-icon>
                <span>Điều chỉnh lại người xử lý</span>
              </button>

                <button mat-flat-button class="btnPrimary" (click)="confirmationCompleted(dossierDetail[0]?.code)"
                    *ngIf="((isWithdraw === 0 && isTheHandler
                                && isTaskComplete(currentTask[0].activitiTask.status) == false
                                && dossierDetail[0]?.dossierStatus?.id !== 1 && dossierDetail[0]?.dossierStatus?.id !== 3
                                && dossierDetail[0]?.dossierStatus?.id !== 14
                                && dossierDetail[0]?.dossierStatus?.id !== 16
                                && (enableProcessingPaidDossiers || dossierDetail[0]?.dossierStatus?.id !== 17)
                                && dossierDetail[0]?.dossierStatus?.id !== 12
                                && !requiredApprovalStatus.includes(dossierDetail[0]?.dossierStatus?.id)
                                && selected !== '1'
                                && selected !== '2'
                                && (dossierDetail[0]?.dossierTaskStatus?.id !== config?.dossierTaskStatus?.waitWithdrawn.id)
                                && (dossierDetail[0]?.dossierTaskStatus?.id !== config?.dossierTaskStatus?.hasWithdrawn.id)
                            ) ||
                            (!!dossierDetail[0]?.extendQNI?.isVbdlis
                                && dossierDetail[0]?.extendQNI?.isVbdlis !== true
                                && dossierDetail[0]?.dossierStatus?.id !== 4
                                && !!dossierDetail[0]?.extendQNI?.isIlis
                                && dossierDetail[0]?.extendQNI?.isIlis !== true
                                && selected !== '1'
                                && selected !== '2'
                                && !!dossierDetail[0]?.extendCMU?.isBhtn
                                && dossierDetail[0]?.extendCMU?.isBhtn !== true
                            )) && !stepStatusOfProcessingProfessionalDossiers && isDisableBtnCompletedConfirm == false"
                    [disabled]="confirmationCompletedBtnCheck('disabled')"
                    [ngClass]="{ 'btnPrimaryDisabled' : qbhCheckPaid}">
                    <mat-icon>done_all</mat-icon>
                    <span *ngIf="!showButtonSuccessName ||
                        (!showButtonSuccessName && !buttonSuccessName) ||
                        (showButtonSuccessName && !buttonSuccessName)" i18n>Xác nhận hoàn thành</span>
                    <span *ngIf="showButtonSuccessName && buttonSuccessName">{{buttonSuccessName}}</span>
                </button>
            </ng-template>
            <button mat-flat-button class="btnPrimary" (click)="confirmResultsDossier(dossierDetail[0]?.code)" *ngIf="vpcSignaturePad == 1 && this.firstTaskData?.isLast == 1">
              <mat-icon>done_all</mat-icon>
              <span>Trả kết quả</span>
            </button>
            <button mat-flat-button class="btnPrimary" *ngIf="checkDVCLL(dossierDetail[0]?.httpStatusHPG , dossierDetail[0]) && (this.dossierDetail[0]?.currentTask[0]?.isLast == 1 || (checkEnableAllButtonTakeNumberWithAgency == true && checkEnableUpdateButtonTakeNumber == true) ) && showButtonTakeNumber==1 && allowChangeExpertRole == false && selected == '1' && !hideAdditionalRequest" (click)="redirectHandling()" >
                <mat-icon>done_all</mat-icon>
                <span>Yêu cầu bổ sung</span>
            </button>
            <button mat-flat-button class="btnPrimary" *ngIf=" (this.dossierDetail[0]?.currentTask[0]?.isLast == 1 || (checkEnableAllButtonTakeNumberWithAgency == true && checkEnableUpdateButtonTakeNumber == true) ) && showButtonTakeNumber==1 && allowChangeExpertRole == false && selected == '2'" (click)="redirectHandling()" >
                <mat-icon>done_all</mat-icon>
                <span>Từ chối</span>
            </button>
            <button mat-flat-button class="btnPrimary" *ngIf="dossierDetail[0]?.dossierTaskStatus?.id === config?.dossierTaskStatus?.waitWithdrawn.id && !allowOfficerToApproveWithdrawRequest" (click)="confirmHasWithdraw(dossierDetail[0]?.id, dossierDetail[0]?.code, dossierDetail[0]?.dossierStatus.comment)">
                <mat-icon>done_all</mat-icon>
                <span i18n>Xác nhận công dân đã rút HS</span>
            </button>
            <button mat-flat-button class="btnPrimary" *ngIf="dossierDetail[0]?.dossierTaskStatus?.id === config?.dossierTaskStatus?.waitWithdrawn.id && allowOfficerToApproveWithdrawRequest" (click)="withdrawDialogsHcm(dossierDetail[0]?.id, dossierDetail[0]?.code, dossierDetail[0]?.dossierStatus.comment)">
                <mat-icon>done_all</mat-icon>
                <span>Phê duyệt yêu cầu rút hồ sơ</span>
            </button>

            <!-- <button mat-flat-button class="btnPrimary" *ngIf="canConfirmDossierPaper || isWithdraw === 0 && showClaimTaskBtn && dossierDetail[0]?.dossierStatus?.id != 5" (click)="postClaimTask()">
                <mat-icon>call_received</mat-icon>
                <span i18n>Xác nhận HS giấy</span>
            </button> -->
            <button mat-flat-button class="btnPrimary" *ngIf="currentTask[0].activitiTask.status == 'COMPLETED' && dossierDetail[0]?.dossierStatus?.id != 5 && showNextTaskBtn == true && checkShowNextTaskBtn == true" (click)="processingDossier()">
                <mat-icon>double_arrow</mat-icon>
                <span i18n>Công việc tiếp theo</span>
            </button>
            <ng-container *ngIf="!isVbdlis && !isIlis && !isBhtn">
                <ng-container *ngIf="dossierDetail[0]?.dossierStatus?.id === 2 && dossierDetail[0]?.task.length === 1 && dossierDetail[0]?.enableDeleteConfig === 1">
                    <button mat-flat-button class="btnMore" [matMenuTriggerFor]="moreMenu" *ngIf="isWithdraw === 0 && isTheHandler == true && isTaskComplete(currentTask[0].activitiTask.status) == false">
                        <mat-icon>more_horiz</mat-icon>
                    </button>
                </ng-container>
                <ng-container *ngIf="!(dossierDetail[0]?.dossierStatus?.id === 2 && dossierDetail[0]?.task.length === 1) && dossierDetail[0]?.dossierStatus?.id !== 14 && dossierDetail[0]?.dossierStatus?.id !== 16">
                    <button mat-flat-button class="btnMore" [matMenuTriggerFor]="moreMenu" *ngIf="isWithdraw === 0 && isTheHandler == true && isTaskComplete(currentTask[0].activitiTask.status) == false">
                        <mat-icon>more_horiz</mat-icon>
                    </button>
                </ng-container>
                <ng-container *ngIf="!(showPaymentRequestToDetailsDossier === 1 || showRequestWithdrawDossierToDetailsDossier === 1) && (dossierDetail[0]?.dossierStatus?.id === 2 && dossierDetail[0]?.task.length === 1) && paymentLater && dossierDetail[0]?.checkPaymentLater">
                    <button mat-flat-button class="btnMore" [matMenuTriggerFor]="moreMenuContinue" *ngIf="isWithdraw === 0 && isTheHandler == true && isTaskComplete(currentTask[0].activitiTask.status) == false">
                        <mat-icon>more_horiz</mat-icon>
                    </button>
                </ng-container>
                <ng-container *ngIf="dossierDetail[0]?.dossierStatus?.id === 2
                && dossierDetail[0]?.currentTask[0]?.isFirst === 1
                && showCancelProcessingInFirstTask === 1
                && dossierDetail[0]?.task.length === 1
                && dossierDetail[0]?.enableDeleteConfig === 0
                && !(paymentLater && dossierDetail[0]?.checkPaymentLater)">
                  <button mat-flat-button class="btnMore" [matMenuTriggerFor]="moreMenu2"
                          *ngIf="isWithdraw === 0 && isTheHandler == true && isTaskComplete(currentTask[0].activitiTask.status) == false">
                    <mat-icon>more_horiz</mat-icon>
                  </button>
                </ng-container>
            </ng-container>

            <!-- vbdlis -->
            <ng-container *ngIf="isVbdlis || isIlis || isBhtn">
                <button mat-flat-button class="btnMore" [matMenuTriggerFor]="moreMenuVBDLIS">
                    <mat-icon>more_horiz</mat-icon>
                </button>
            </ng-container>
            <!-- #docregion sub-menu  -->
            <mat-menu #animals="matMenu">
                <ng-container *ngIf="!!listConfigTemplate && listConfigTemplate.length !== 0">
                    <div *ngFor="let bill of listConfigTemplate" fxLayout="row" fxLayoutAlign="space-between">
                        <div class="d-flex flex-row">
                            <div style="background-color: #FFF; cursor: pointer" class="w-250">
                                <button mat-menu-item (click)="createPrintBill(bill.file.path, bill)">
                                    {{bill.name}}
                                </button>
                            </div>
                            <div id="printTemplate" fxLayout="row" fxLayoutAlign="center center" *ngIf="bill.fileSign.length > 0">
                                <div fxLayout="column">
                                    <ng-container *ngFor="let item of bill.fileSign">
                                        <div fxLayout="row" fxLayoutAlign="center center" >
                                            <span class="color" style="cursor: pointer" (click)="downloadFile(item.fileSignId, item.fileSignName)"  matTooltip="Tải xuống tệp tin {{item.fileSignName}}">{{item.fileSignName}}</span>
                                            <a mat-menu-item [matMenuTriggerFor]="vertebrates" (click)="editFile(bill.id, item.fileSignId)">
                                                <mat-icon>more_vert</mat-icon>
                                            </a>
                                        </div>
                                        <mat-menu #vertebrates="matMenu">
                                            <button mat-menu-item class="menuAction" (click)="downloadFileTemplate(bill.id, bill.file.filename)">
                                                    <mat-icon>cloud_download</mat-icon>
                                                    <span i18n="@@fileOriginal">Tải xuống tệp tin gốc</span>
                                                </button>
<!--                                            <button mat-menu-item class="menuAction" (click)="openHistory(item.fileSignId)">-->
<!--                                                    <mat-icon>refresh</mat-icon>-->
<!--                                                    <span i18n="@@history">Xem lịch sử ký số</span>-->
<!--                                                </button>-->
                                          <button mat-menu-item class="menuAction">
                                            <view-sign-history [fileId]="item.fileSignId">
                                              <mat-icon>refresh</mat-icon>
                                              <span>Xem lịch sử ký số</span>
                                            </view-sign-history>
                                          </button>
                                        </mat-menu>
                                    </ng-container>

                                </div>
                            </div>
                        </div>
                    </div>
                </ng-container>
                <!-- <button mat-menu-item [matMenuTriggerFor]="vertebrates">Vertebrates</button>  (click)="editFile(bill.id, item.fileSignId)"-->
            </mat-menu>

            <!--
            <mat-menu #printMenu="matMenu" [overlapTrigger]="false">
                <ng-container *ngFor="let bill of listConfigTemplate" fxLayout="row" fxLayoutAlign="space-between">
                    <button mat-menu-item (click)="createPrintBill(bill.file.path, bill)">
                        {{bill.name}}
                    </button>
                    <ng-container *ngIf="bill.fileSign">
                        <ng-container fxLayout="column" *ngFor="let item of bill.fileSign">
                            <ng-container fxLayout="column" fxLayoutAlign="center">
                                <div fxLayout="row" fxLayoutAlign="space-between center">
                                    <span class="color ml-5">{{item.fileSignName}} </span>
                                    <a mat-icon-button class="moreBtn myDIV" (mouseleave)="openMyMenu()" (click)="editFile(bill.id, item.fileSignId)">
                                        <mat-icon>more_vert</mat-icon>
                                    </a>
                                </div>
                                <div *ngIf="item.check">
                                    <button mat-menu-item class="menuAction" (click)="downloadFile(item.fileSignId, item.fileSignName)">
                                    <mat-icon>cloud_download</mat-icon>
                                    <span i18n="@@fileCurrent">Tải xuống tệp tin hiện tại</span>
                                </button>
                                    <button mat-menu-item class="menuAction" (click)="downloadFileTemplate(bill.id, bill.file.filename)">
                                    <mat-icon>cloud_download</mat-icon>
                                    <span i18n="@@fileOriginal">Tải xuống tệp tin gốc</span>
                                </button>
                                    <button mat-menu-item class="menuAction" (click)="openHistory(item.fileSignId)">
                                    <mat-icon>refresh</mat-icon>
                                    <span i18n="@@history">Xem lịch sử ký số</span>
                                </button>
                                </div>
                            </ng-container>
                        </ng-container>
                    </ng-container>
                </ng-container>
            </mat-menu> -->
            <mat-menu #moreMenu="matMenu">

                <ng-container *ngIf="dossierDetail[0]?.dossierStatus?.id === 2 && dossierDetail[0]?.task.length === 1; else normalAction">
                    <button mat-menu-item class="menuAction" (click)="deleteDialog(dossierDetail[0]?.code)" *ngIf="dossierDetail[0]?.enableDeleteConfig === 1 && (checkProvineAdmin || env?.hideDeleteButton !== true || (hasDossierDeletePermission && dossierDetail[0]?.dossierStatus?.id === 2 && dossierDetail[0]?.currentTask[0]?.isFirst === 1 && dossierDetail[0]?.applyMethod?.id !== 0))">
                        <mat-icon>delete_forever</mat-icon>
                        <span i18n>Xóa</span>
                    </button>
                    <button mat-menu-item class="menuAction" (click)="deleteDialog(dossierDetail[0]?.code)" *ngIf="this.deleteDossierQni">
                        <mat-icon>delete_outline</mat-icon>
                        <span i18n>Hủy hồ sơ</span>
                    </button>
                    <button class="menuAction" mat-menu-item *ngIf="dossierDetail[0]?.currentTask[0]?.isFirst === 1 && dossierDetail[0]?.dossierStatus?.id === 2 && paymentLater && (showCheckPaymentLater == true || (paymentRequestDetailProcessing && dossier?.checkPaymentLater)) && !disableRequestPayment" (click)="paymentRequest(dossierDetail[0]?.id, dossierDetail[0]?.code)">
                        <mat-icon>payment</mat-icon><span>Yêu cầu thanh toán hồ sơ</span>
                    </button>
                </ng-container>
                <ng-template #normalAction>
                    <button mat-menu-item class="menuAction" (click)="deleteDialog(dossierDetail[0]?.code)" *ngIf="dossierDetail[0]?.enableDeleteConfig === 1 && (checkProvineAdmin || env?.hideDeleteButton !== true || (hasDossierDeletePermission && dossierDetail[0]?.dossierStatus?.id === 2 && dossierDetail[0]?.currentTask[0]?.isFirst === 1 && dossierDetail[0]?.applyMethod?.id !== 0))">
                        <mat-icon>delete_forever</mat-icon>
                        <span i18n>Xóa</span>
                    </button>
                    <!-- <button mat-menu-item class="menuAction" (click)="pauseDialog(dossierDetail[0]?.code)" *ngIf="currentTask[0]?.activitiTask?.status == 'ASSIGNED' && (dossierDetail[0]?.dossierStatus.id < 4 )">
                        <mat-icon>pause_circle_outline</mat-icon>
                        <span i18n>Tạm dừng</span>
                    </button> -->
                    <!-- <button mat-menu-item class="menuAction" (click)="suspenDialogs(dossierDetail[0]?.code)" *ngIf="currentTask[0]?.activitiTask?.status == 'ASSIGNED' && (dossierDetail[0]?.dossierStatus.id < 3 )">
                        <mat-icon>pause_circle_outline</mat-icon>
                        <span i18n>Tạm dừng</span>
                    </button> -->
                    <button mat-menu-item class="menuAction" (click)="suspenDialogs(dossierDetail[0]?.code)"
                    *ngIf="(dossierDetail[0]?.dossierStatus.id == 2 || dossierDetail[0]?.dossierStatus.id == 4)
                    && (env?.OS_BDG?.isProcessDefinitionBDG == undefined || (env?.OS_BDG?.isProcessDefinitionBDG && dossierDetail[0]?.currentTask[0]?.bpmProcessDefinitionTask?.variable.canPaused == 1)) && !isNotShowPauseEXtendTimeBtn">
                        <mat-icon>pause_circle_outline</mat-icon>
                        <span i18n>Tạm dừng</span>
                    </button>
                    <!-- <button mat-menu-item class="menuAction" (click)="continueDialog(dossierDetail[0]?.code)" *ngIf="currentTask[0]?.activitiTask?.status == 'SUSPENDED' || (dossierDetail[0]?.dossierStatus.id === 3 )">
                        <mat-icon>autorenew</mat-icon>
                        <span i18n>Tiếp tục xử lý</span>
                    </button> -->
                    <button mat-menu-item class="menuAction" (click)="continueDialog(dossierDetail[0]?.code)" *ngIf="dossierDetail[0]?.dossierStatus?.id == 3">
                        <mat-icon>autorenew</mat-icon>
                        <span i18n>Tiếp tục xử lý</span>
                    </button>
                    <!-- <ng-container *ngIf="row.currentTask != undefined"> -->
                    <ng-container *ngIf="this.qbhextendprocess == true; then qbhex else normal4"></ng-container>
                        <ng-template #normal4>
                            <button mat-menu-item class="menuAction" *ngIf="dossierDetail[0]?.currentTask != undefined && dossierDetail[0]?.dossierStatus?.id !== 5 && dossierDetail[0]?.currentTask[0]?.bpmProcessDefinitionTask?.variable?.canIncreaseDue == 1 && !isNotShowPauseEXtendTimeBtn" (click)="extendTimeDialogs(dossierDetail[0]?.id, dossierDetail[0]?.code)">
                                <mat-icon>today</mat-icon><span i18n>Gia hạn hồ sơ</span>
                            </button>
                        </ng-template>
                        <ng-template #qbhex>
                            <button mat-menu-item class="menuAction" *ngIf="dossierDetail[0]?.currentTask != undefined && dossierDetail[0]?.dossierStatus?.id !== 5 && dossierDetail[0]?.currentTask[0]?.bpmProcessDefinitionTask?.variable?.canIncreaseDue == 1" (click)="extendTimeQBHDialogs(dossierDetail[0]?.id, dossierDetail[0]?.code, dossierDetail[0]?.procedureProcessDefinition.id)">
                                <mat-icon>today</mat-icon><span>Xin lỗi và xin gia hạn</span>
                            </button>
                        </ng-template>
                    <!-- </ng-container> -->
                    <button mat-menu-item class="menuAction" (click)="viewSendAnApologyLetter(dossierDetail[0]?.id, dossierDetail[0]?.code)" *ngIf="enableFunctionToSendApologyLetter">
                        <mat-icon>email</mat-icon><span>Gửi thư xin lỗi</span>
                    </button>
                    <button mat-menu-item class="menuAction" (click)="restoreDialog(dossierDetail[0]?.code)" *ngIf="dossierDetail[0]?.dossierStatus?.id == 6">
                        <mat-icon>settings_backup_restore</mat-icon>
                        <span i18n>Khôi phục hồ sơ</span>
                    </button>
                    <button mat-menu-item class="menuAction" *ngIf="checkDVCLL(dossierDetail[0]?.httpStatusHPG , dossierDetail[0]) && dossierDetail[0]?.dossierStatus?.id != 1 && !hideAdditionalRequest" (click)="additionalRequirementNoti ? additionalRequirementQBH(dossierDetail[0]?.code) : additionalRequirement(dossierDetail[0]?.code)">
                        <mat-icon>reply</mat-icon>
                        <span i18n>Yêu cầu bổ sung</span>
                    </button>
                    <!-- <button mat-menu-item class="menuAction" (click)="payment(dossierDetail[0]?.id)" *ngIf="(dossierDetail[0]?.dossierStatus?.id == 14 || dossierDetail[0]?.dossierStatus?.id == 17) && enableOnlinePayment"> -->
                        <button mat-menu-item (click)="payment(dossierDetail[0]?.id)" *ngIf="(
                                        (dossierDetail[0]?.dossierStatus.id == 14 || dossierDetail[0]?.dossierStatus.id == 17)  && enableOnlinePayment) || 
                                        ((dossierDetail[0]?.dossierStatus.id == 2||dossierDetail[0]?.dossierStatus.id==4)  && enablePaymentHPG)">
                        <mat-icon>attach_money</mat-icon>
                        <span>Thanh toán trực tuyến</span>
                    </button>
                    <!-- <button mat-menu-item class="menuAction" (click)="processSelection()">
                        <mat-icon>insights</mat-icon>
                        <span i18n>Thay đổi quy trình</span>
                    </button> -->
                    <ng-container *ngIf="this.qbhwithdrawprocess == true; then qbhdraw3 else normal3"></ng-container>
                    <ng-template #qbhdraw3>
                    <ng-container *ngIf="dossierDetail[0]?.currentTask != undefined">
                        <button mat-menu-item class="menuAction" *ngIf="checkDVCLL(dossierDetail[0]?.isDVCLT , dossierDetail[0]) && (dossierDetail[0]?.dossierStatus.id !== 5 || dossierDetail[0]?.dossierStatus.id !== 4)
                        && (env?.OS_BDG?.isProcessDefinitionBDG == undefined || (env?.OS_BDG?.isProcessDefinitionBDG && dossierDetail[0]?.currentTask[0]?.bpmProcessDefinitionTask?.variable.canEvictDossier == 1))
                        && !hideRequestToWithdraw
                        && this.checkDrawQBH == true"
                        (click)="withdrawQBHDialogs(dossierDetail[0]?.id, dossierDetail[0]?.code)">
                        <mat-icon>reply</mat-icon><span>Yêu cầu rút hồ sơ</span>

                        </button>
                    </ng-container>
                    </ng-template>
                    <ng-template #normal3>
                        <ng-container *ngIf="enableWithdrawDossierBtn; else normalwithdrawn">
                            <button mat-menu-item class="menuAction" *ngIf="checkDVCLL(dossierDetail[0]?.isDVCLT, dossierDetail[0]) && (dossierDetail[0]?.dossierStatus.id !== 5 || dossierDetail[0]?.dossierStatus.id !== 4)
                            && (env?.OS_BDG?.isProcessDefinitionBDG == undefined || (env?.OS_BDG?.isProcessDefinitionBDG && dossierDetail[0]?.currentTask[0]?.bpmProcessDefinitionTask?.variable.canEvictDossier == 1))
                            && !hideRequestToWithdraw && receivingPermission" (click)="withdrawDialogs(dossierDetail[0]?.id, dossierDetail[0]?.code)">
                            <mat-icon>reply</mat-icon><span *ngIf="!isUserRQ;else isUserKGGTemplate">Yêu cầu rút hồ sơ </span>
                                <ng-template #isUserKGGTemplate>
                                    <span>Rút hồ sơ theo yêu cầu</span>
                                </ng-template>
                            </button>
                        </ng-container>
                        <ng-template #normalwithdrawn>
                            <ng-container *ngIf="dossierDetail[0]?.currentTask != undefined">
                                <button mat-menu-item class="menuAction" *ngIf="checkDVCLL(dossierDetail[0]?.isDVCLT , dossierDetail[0]) && (dossierDetail[0]?.dossierStatus.id !== 5 || dossierDetail[0]?.dossierStatus.id !== 4)
                                && (env?.OS_BDG?.isProcessDefinitionBDG == undefined || (env?.OS_BDG?.isProcessDefinitionBDG && dossierDetail[0]?.currentTask[0]?.bpmProcessDefinitionTask?.variable.canEvictDossier == 1))
                                && !hideRequestToWithdraw" (click)="withdrawDialogs(dossierDetail[0]?.id, dossierDetail[0]?.code)">
                                <mat-icon>reply</mat-icon><span *ngIf="!isUserRQ;else isUserKGGTemplate">Yêu cầu rút hồ sơ </span>
                                    <ng-template #isUserKGGTemplate>
                                        <span>Rút hồ sơ theo yêu cầu</span>
                                    </ng-template>
                                </button>
                            </ng-container>
                        </ng-template>
                    </ng-template>
                    <ng-container *ngIf="this.qbhstopprocess == false; then normalC else QBhC"> </ng-container>
                        <ng-template #normalC>
                            <button mat-menu-item class="menuAction" (click)="cancelProcessing(dossierDetail[0]?.id, dossierDetail[0]?.code)" 
                                *ngIf="!hiddenCancelDossierNAST && ((checkDVCLL(dossierDetail[0]?.httpStatusHPG , dossierDetail[0]) && 
                                    (env?.OS_BDG?.isProcessDefinitionBDG == undefined 
                                    || (env?.OS_BDG?.isProcessDefinitionBDG && dossierDetail[0]?.currentTask[0]?.bpmProcessDefinitionTask?.variable.canCancelDosssier == 1) 
                                    || (dossierDetail[0]?.currentTask[0]?.isFirst === 1 && showCancelProcessingInFirstTask === 1))) || hasCancelDossierPermission)">
                            <mat-icon>do_disturb</mat-icon><span>{{ this.changeTagQti ? 'Trả không giải quyết' : 'Dừng xử lý' }}</span>
                            </button>
                            <button mat-menu-item class="menuAction" (click)="cancelProcessing(dossierDetail[0]?.id, dossierDetail[0]?.code)" *ngIf="hiddenCancelDossierNAST && checkCanCancelProcessingQni()">
                                <mat-icon>do_disturb</mat-icon>
                                <span>Dừng xử lý</span>
                            </button>
                        </ng-template>
                        <ng-template #QBhC>
                            <button mat-menu-item class="menuAction" (click)="cancelQBHProcessing(dossierDetail[0]?.id, dossierDetail[0]?.code)"
                                *ngIf="env?.OS_BDG?.isProcessDefinitionBDG == undefined
                                                && dossierDetail[0]?.dossierStatus?.id != 11
                                                || (env?.OS_BDG?.isProcessDefinitionBDG && dossierDetail[0]?.currentTask[0]?.bpmProcessDefinitionTask?.variable.canCancelDosssier == 1)
                                                || (dossierDetail[0]?.currentTask[0].isFirst === 1 && showCancelProcessingInFirstTask === 1)
                                                || dossierDetail[0]?.dossierStatus?.id == 8
                                                || dossierDetail[0]?.dossierStatus?.id == 10
                                                || dossierDetail[0]?.dossierStatus?.id == 6
                                                || dossierDetail[0]?.dossierStatus?.id == 2
                                                || dossierDetail[0]?.dossierStatus?.id == 16 ">
                                <mat-icon>do_disturb</mat-icon><span>Từ chối giải quyết</span>
                                </button>
                        </ng-template>
                    <button mat-menu-item class="menuAction" (click)="viewUpdateHistory(dossierDetail[0]?.id, dossierDetail[0]?.code)">
                        <mat-icon>update</mat-icon><span i18n>Xem lịch sử cập nhật</span>
                    </button>
                    <button mat-menu-item class="menuAction" *ngIf="showChangeDirectPayment?.enable && enableChangeDirectPayment" (click)="allowDirectlyPaymentRequest(dossierDetail[0]?.id, dossierDetail[0]?.code)">
                        <mat-icon>payment</mat-icon>
                        <span i18n="@@showChangeDirectPayment">Chuyển sang thanh toán trực tiếp</span>
                    </button>
                    <!-- *ngIf="(env?.vnpost?.hcmSendVNPostWhenProccessingDossierConfig ==='1' || env?.vnpost?.hcmSendVNPostWhenProccessingDossierConfig === 1) && dossierDetail[0]?.vnpostStatus == undefined " *ngIf="checkhcmSendVNPostWhenProccessingDossierConfig === 1" -->
                    <button mat-menu-item class="menuAction" (click)="sendVNPostOverLgspHcm(dossierDetail[0]?.id, dossierDetail[0]?.code, dossierDetail[0]?.applicant, dossierDetail[0]?.dossierFormFile)" *ngIf="this.checkhcmSendVNPostWhenProccessingDossierConfig == 1 && dossierDetail[0]?.vnpostStatus == undefined && dossierDetail[0]?.receivingPlace?.rcSend == true" >
                        <mat-icon>send</mat-icon><span i18n>Send VNPost</span>
                    </button>
                    <button class="menuAction" mat-menu-item *ngIf="(dossierDetail[0]?.dossierStatus.id == 2 || dossierDetail[0]?.dossierStatus.id == 4) && paymentLater && (dossierDetail[0]?.checkPaymentLater || (paymentRequestDetailProcessing && dossier?.checkPaymentLater) || showCheckPaymentLater == true) && !disableRequestPayment" (click)="paymentRequest(dossierDetail[0]?.id, dossierDetail[0]?.code)">
                        <mat-icon>payment</mat-icon><span>Yêu cầu thanh toán hồ sơ</span>
                    </button>
                    <button class="menuAction" mat-menu-item (click)="directlyPaymentRequest(dossierDetail[0]?.id, dossierDetail[0]?.code)" *ngIf="!isPaid && showDirectPaymentRequestBtn && isDirectPaymentNotificationAllowed">
                        <mat-icon>payment</mat-icon><span>Thông báo thanh toán trực tiếp</span>
                    </button>
                    <!-- <button mat-menu-item class="menuAction" (click)="printReceiptDialog()"   >
                        <mat-icon>local_printshop</mat-icon>
                        <span>In biên lai giấy</span>
                    </button> -->
                    <!-- *ngIf="showDirectPaymentRequestBtn && paymentLater && dossierDetail[0]?.checkPaymentLater" -->
                    <!-- <button mat-menu-item class="menuAction" *ngIf="dossierDetail[0]?.due.length > 0 && dossierDetail[0]?.due[0].timesheet.isOverDue == true" (click)="addApologyText(dossierDetail[0]?.id)">
                        <mat-icon>assignment_returned</mat-icon><span i18n>Tải xuống mẫu văn bản xin lỗi</span>
                    </button>
                    <button mat-menu-item class="menuAction" *ngIf="dossierDetail[0]?.due.length > 0 && dossierDetail[0]?.due[0].timesheet.isOverDue == true" (click)="signApologyText(dossierDetail[0]?.id)">
                        <mat-icon>note_add</mat-icon><span i18n>Thêm văn bản xin lỗi</span>
                    </button> -->
                  <button mat-menu-item class="menuAction"
                          (click)="confirmAdditionalRequestsDeadline(dossierDetail[0])"
                          *ngIf="!!dossierDetail[0]?.currentTask && dossierDetail[0]?.currentTask.length > 0 && dossierDetail[0]?.currentTask[0].bpmProcessDefinitionTask?.dynamicVariable?.deadlineForAdditionalRequests"
                          [disabled]="!!dossierDetail[0]?.additionalRequirementDetail?.deadline">
                    <mat-icon>update</mat-icon>
                    <span i18n="@@enterAdditionalRequestExpirationTime">Nhập thời gian hết hạn bổ sung</span>
                  </button>
                </ng-template>

                <button *ngIf="checkPermissionNotify()" mat-menu-item class="menuAction" (click)="openNotify(dossierDetail[0]?.id, '')">
                    <mat-icon>notifications</mat-icon>
                    <span>Thông báo đến người dân</span>
                </button>
                <button class="menuAction" mat-menu-item (click)="downloadZipFileDossier(dossierDetail[0])" *ngIf="downloadFileDossierRar">
                    <mat-icon>cloud_download</mat-icon>
                    <span>Tải văn bản nén của hồ sơ</span>
                </button>
            </mat-menu>
            <mat-menu #moreMenuContinue="matMenu">
                <button mat-flat-button class="btnSecondary" *ngIf="checkConfigPowaco===1" (click)="sendPowacoAGESB()">
                    <mat-icon>send</mat-icon>
                    <span>Gửi hồ sơ Powaco</span>
                 </button>
                    <button class="menuAction" mat-menu-item *ngIf="(dossierDetail[0]?.dossierStatus?.id == 2 || dossierDetail[0]?.dossierStatus.id == 4) && paymentLater && (dossierDetail[0]?.checkPaymentLater || (paymentRequestDetailProcessing && dossier?.checkPaymentLater) || showCheckPaymentLater == true) && !disableRequestPayment" (click)="paymentRequest(dossierDetail[0]?.id, dossierDetail[0]?.code)">
                        <mat-icon>payment</mat-icon><span>Yêu cầu thanh toán hồ sơ</span>
                    </button>
                    <button class="menuAction" mat-menu-item (click)="directlyPaymentRequest(dossierDetail[0]?.id, dossierDetail[0]?.code)" *ngIf="!isPaid && showDirectPaymentRequestBtn && isDirectPaymentNotificationAllowed">
                        <mat-icon>payment</mat-icon><span>Thông báo thanh toán trực tiếp</span>
                    </button>
                    <!-- *ngIf="showDirectPaymentRequestBtn && paymentLater && dossierDetail[0]?.checkPaymentLater" -->
                    <ng-container *ngIf="qbhstopprocess == false; then normalC else QBhC"> </ng-container>
                    <ng-template #normalC>
                        <button class="menuAction" mat-menu-item
                        (click)="cancelQBHProcessing(dossierDetail[0].id, dossierDetail[0].code)"
                        *ngIf="!hiddenCancelDossierNAST && ((checkDVCLL(dossierDetail[0]?.httpStatusHPG , dossierDetail[0]) &&(dossierDetail[0]?.currentTask[0]?.isFirst === 1 && showCancelProcessingInFirstTask === 1)) || hasCancelDossierPermission)">
                        <mat-icon>do_disturb</mat-icon>
                        <span >{{ this.changeTagQti ? 'Trả không giải quyết' : 'Dừng xử lý' }}</span>
                        </button>
                        <button mat-menu-item class="menuAction" (click)="cancelProcessing(dossierDetail[0]?.id, dossierDetail[0]?.code)" *ngIf="hiddenCancelDossierNAST && checkCanCancelProcessingQni()">
                            <mat-icon>do_disturb</mat-icon>
                            <span>Dừng xử lý</span>
                        </button>
                        <button class="menuAction" mat-menu-item *ngIf="dossierDetail[0]?.currentTask[0]?.isFirst === 1 && dossierDetail[0]?.dossierStatus?.id === 2 && paymentLater && (showCheckPaymentLater == true || (paymentRequestDetailProcessing && dossier?.checkPaymentLater)) && !disableRequestPayment" (click)="paymentRequest(dossierDetail[0]?.id, dossierDetail[0]?.code)">
                            <mat-icon>payment</mat-icon><span>Yêu cầu thanh toán hồ sơ</span>
                        </button>
                    </ng-template>
                    <ng-template #QBhC>
                        <button class="menuAction" mat-menu-item
                        (click)="cancelQBHProcessing(dossierDetail[0].id, dossierDetail[0].code)"
                        *ngIf="(dossierDetail[0]?.currentTask[0]?.isFirst === 1 && showCancelProcessingInFirstTask === 1)">
                        <mat-icon>do_disturb</mat-icon>
                        <span >Từ chối giải quyết</span>
                        </button>
                    </ng-template>
                    <!-- <button mat-menu-item class="menuAction" (click)="printReceiptDialog()"   >
                        <mat-icon>local_printshop</mat-icon>
                        <span>In biên lai giấy</span>
                    </button> -->
                    <button *ngIf="checkPermissionNotify()" mat-menu-item class="menuAction" (click)="openNotify(dossierDetail[0]?.id, '')">
                        <mat-icon>notifications</mat-icon>
                        <span>Thông báo đến người dân</span>
                    </button>
                    <button class="menuAction" mat-menu-item (click)="downloadZipFileDossier(dossierDetail[0])" *ngIf="downloadFileDossierRar">
                        <mat-icon>cloud_download</mat-icon>
                        <span>Tải văn bản nén của hồ sơ</span>
                    </button>
            </mat-menu>

            <mat-menu #moreMenu2="matMenu">
              <button mat-flat-button class="btnSecondary" *ngIf="checkCivilStatusJusticeAGESB===1" (click)="sendHTTPAGESB()">
                <mat-icon>send</mat-icon>
                <span>Gửi hồ sơ hộ tịch</span>
             </button>
                <ng-container *ngIf="qbhstopprocess == false; then normalC else QBhC"> </ng-container>
                <ng-template #normalC>
                    <button class="menuAction" mat-menu-item (click)="cancelProcessing(dossierDetail[0].id, dossierDetail[0].code)"
                        *ngIf="!hiddenCancelDossierNAST && ((checkDVCLL(dossierDetail[0]?.httpStatusHPG , dossierDetail[0]) && (dossierDetail[0]?.currentTask[0]?.isFirst === 1 && showCancelProcessingInFirstTask === 1)) || hasCancelDossierPermission)">
                        <mat-icon>do_disturb</mat-icon>
                        <span>{{ this.changeTagQti ? 'Trả không giải quyết' : 'Dừng xử lý' }}</span>
                    </button>
                    <button mat-menu-item class="menuAction" (click)="cancelProcessing(dossierDetail[0]?.id, dossierDetail[0]?.code)" *ngIf="hiddenCancelDossierNAST && checkCanCancelProcessingQni()">
                        <mat-icon>do_disturb</mat-icon>
                        <span>Dừng xử lý</span>
                    </button>
                    <button class="menuAction" mat-menu-item *ngIf="dossierDetail[0]?.currentTask[0]?.isFirst === 1 && dossierDetail[0]?.dossierStatus?.id === 2 && paymentLater && (showCheckPaymentLater == true || (paymentRequestDetailProcessing && dossier?.checkPaymentLater)) && !disableRequestPayment" (click)="paymentRequest(dossierDetail[0]?.id, dossierDetail[0]?.code)">
                        <mat-icon>payment</mat-icon><span>Yêu cầu thanh toán hồ sơ</span>
                    </button>
                </ng-template>
                <ng-template #QBhC>
                    <button class="menuAction" mat-menu-item (click)="cancelQBHProcessing(dossierDetail[0].id, dossierDetail[0].code)"
                    *ngIf="(dossierDetail[0]?.currentTask[0]?.isFirst === 1 && showCancelProcessingInFirstTask === 1)">
                    <mat-icon>do_disturb</mat-icon>
                    <span >Từ chối giải quyết</span>
                    </button>
                </ng-template>
              <!-- <button mat-menu-item class="menuAction" (click)="printReceiptDialog()"   >
                <mat-icon>local_printshop</mat-icon>
                <span>In biên lai giấy</span>
            </button> -->
                <button *ngIf="checkPermissionNotify()" mat-menu-item class="menuAction" (click)="openNotify(dossierDetail[0]?.id, '')">
                    <mat-icon>notifications</mat-icon>
                    <span>Thông báo đến người dân</span>
                </button>
                <button class="menuAction" mat-menu-item (click)="downloadZipFileDossier(dossierDetail[0])" *ngIf="downloadFileDossierRar">
                    <mat-icon>cloud_download</mat-icon>
                    <span>Tải văn bản nén của hồ sơ</span>
                </button>
            </mat-menu>
            <mat-menu #moreMenuVBDLIS="matMenu">
                <button mat-menu-item class="menuAction" (click)="deleteDialog(dossierDetail[0]?.code)" *ngIf="this.deleteDossierQni && dossierDetail[0]?.dossierStatus?.id === 2 && dossierDetail[0]?.task.length === 1">
                    <mat-icon>delete_outline</mat-icon>
                    <span i18n>Hủy hồ sơ</span>
                </button>
                <button mat-menu-item class="menuAction" (click)="deleteDialog(dossierDetail[0]?.code)" *ngIf="dossierDetail[0]?.enableDeleteConfig === 1 && (checkProvineAdmin || env?.hideDeleteButton !== true || (hasDossierDeletePermission && dossierDetail[0]?.dossierStatus?.id === 2 && dossierDetail[0]?.currentTask[0]?.isFirst === 1 && dossierDetail[0]?.applyMethod?.id !== 0))">
                    <mat-icon>delete_forever</mat-icon>
                    <span i18n>Xóa</span>
                </button>
				<button [disabled]="isLoadingBtnReceiveVbdlis" class="menuAction" mat-menu-item (click)="syncVBDLISDLK(dossierDetail[0]?.id)" *ngIf="isDLKVBDLIS && isVbdlis && dossierDetail[0]?.extendQNI?.dossierStatusInVbdlis != 8 && dossierDetail[0]?.extendQNI?.dossierStatusInVbdlis != 3 && (dossierDetail[0]?.extendQNI?.isVbdlis == false || dossierDetail[0]?.extendQNI?.isVbdlis == undefined)">
                    <mat-icon>reply</mat-icon><span>Tiếp nhận VBDLIS Đắk Lắk</span>
                </button>
				<button [disabled]="isLoadingBtnReceiveVbdlis" class="menuAction" mat-menu-item (click)="syncVBDLISPYN(dossierDetail[0]?.id)" *ngIf="isDLKVBDLIS && isVbdlis && dossierDetail[0]?.extendQNI?.dossierStatusInVbdlis != 8 && dossierDetail[0]?.extendQNI?.dossierStatusInVbdlis != 3 && (dossierDetail[0]?.extendQNI?.isVbdlis == false || dossierDetail[0]?.extendQNI?.isVbdlis == undefined)">
                    <mat-icon>reply</mat-icon><span>Tiếp nhận VBDLIS Phú Yên</span>
                </button>
                <button [disabled]="isLoadingBtnReceiveVbdlis" class="menuAction" mat-menu-item (click)="syncVBDLIS(dossierDetail[0]?.id)" *ngIf="!isDLKVBDLIS && isVbdlis && dossierDetail[0]?.extendQNI?.dossierStatusInVbdlis != 8 && dossierDetail[0]?.extendQNI?.dossierStatusInVbdlis != 3 && (dossierDetail[0]?.extendQNI?.isVbdlis == false || dossierDetail[0]?.extendQNI?.isVbdlis == undefined)">
                    <mat-icon>reply</mat-icon><span>Tiếp nhận VBDLIS</span>
                </button>
                <button class="menuAction" mat-menu-item (click)="syncAdditinalRequestIntoVBDLIS(dossierDetail[0]?.code)" *ngIf="isVbdlis && dossierDetail[0]?.extendQNI?.isVbdlis == true && dossierDetail[0]?.extendQNI?.dossierStatusInVbdlis == 8">
                    <mat-icon>reply</mat-icon><span>Bổ sung hồ sơ qua VBDLIS</span>
                </button>
                <button class="menuAction" mat-menu-item (click)="feedBackResultToVBDLIS(dossierDetail[0]?.code, 'Không đồng ý phê duyệt dừng xử lý')" *ngIf="isVbdlis && dossierDetail[0]?.extendQNI?.isVbdlis == true && dossierDetail[0]?.extendQNI?.dossierStatusInVbdlis == 10">
                    <mat-icon>reply</mat-icon><span>Phản hồi hồ sơ sai kết quả qua VBDLIS</span>
                </button>
                <button class="menuAction" mat-menu-item (click)="syncILIS(dossierDetail[0]?.id)" *ngIf="isIlis && dossierDetail[0]?.ilis?.dossierStatusInIlis != 8 && dossierDetail[0]?.ilis?.dossierStatusInIlis != 3 && (dossierDetail[0]?.extendQNI?.isIlis == false || dossierDetail[0]?.extendQNI?.isIlis == undefined) && (isShowButtonReceivedILisAgg == false || isShowButtonReceivedILisAgg == undefined)">
                    <mat-icon>reply</mat-icon><span>Tiếp nhận ILIS</span>
                </button>
                <button class="menuAction" mat-menu-item (click)="syncAdditinalRequestIntoILIS(dossierDetail[0]?.code)" *ngIf="isIlis && dossierDetail[0]?.extendQNI?.isIlis == true && dossierDetail[0]?.extendQNI?.dossierStatusInIlis == 8">
                    <mat-icon>reply</mat-icon><span>Bổ sung hồ sơ qua ILIS</span>
                </button>
                <button class="menuAction" mat-menu-item (click)="syncBHTN(dossierDetail[0]?.id)" *ngIf="isBhtn && dossierDetail[0]?.bhtn?.dossierStatusInBhtn != 8 && dossierDetail[0]?.bhtn?.dossierStatusInBhtn != 3 && (dossierDetail[0]?.extendCMU?.isBhtn == false || dossierDetail[0]?.extendCMU?.isBhtn == undefined)">
                    <mat-icon>reply</mat-icon><span>Tiếp nhận BHTN</span>
                </button>
                <button class="menuAction" mat-menu-item (click)="syncAdditinalRequestIntoBHTN(dossierDetail[0]?.code)" *ngIf="isBhtn && dossierDetail[0]?.extendCMU?.isBhtn == true && dossierDetail[0]?.extendCMU?.dossierStatusInBhtn == 8">
                    <mat-icon>reply</mat-icon><span>Bổ sung hồ sơ qua BHTN</span>
                </button>
                <button mat-menu-item class="menuAction" (click)="cancelProcessing(dossierDetail[0]?.id, dossierDetail[0]?.code)" *ngIf="!hiddenCancelDossierNAST && ((checkDVCLL(dossierDetail[0]?.httpStatusHPG , dossierDetail[0]) && (env?.OS_BDG?.isProcessDefinitionBDG == undefined || (env?.OS_BDG?.isProcessDefinitionBDG && dossierDetail[0]?.currentTask[0]?.bpmProcessDefinitionTask?.variable?.canCancelDosssier == 1)) && (dossierDetail[0]?.currentTask[0]?.isFirst !== 1 || showCancelProcessingInFirstTask !== 1)) || hasCancelDossierPermission)">
                    <mat-icon>do_disturb</mat-icon><span>{{ this.changeTagQti ? 'Trả không giải quyết' : 'Dừng xử lý' }}</span>
                </button>
                <button mat-menu-item class="menuAction" (click)="cancelProcessing(dossierDetail[0]?.id, dossierDetail[0]?.code)" *ngIf="hiddenCancelDossierNAST && checkCanCancelProcessingQni()">
                    <mat-icon>do_disturb</mat-icon>
                    <span>Dừng xử lý</span>
                </button>
                <ng-container *ngIf="qbhstopprocess == false; then normalC else QBhC"> </ng-container>
                <ng-template #normalC>
                    <button mat-menu-item class="menuAction" (click)="cancelProcessing(dossierDetail[0]?.id, dossierDetail[0]?.code)" 
                    *ngIf="!hiddenCancelDossierNAST && ((checkDVCLL(dossierDetail[0]?.httpStatusHPG , dossierDetail[0]) &&
                    (env?.OS_BDG?.isProcessDefinitionBDG == undefined 
                    || (env?.OS_BDG?.isProcessDefinitionBDG && dossierDetail[0]?.currentTask[0]?.bpmProcessDefinitionTask?.variable?.canCancelDosssier == 1))) || hasCancelDossierPermission)">
                        <mat-icon>do_disturb</mat-icon><span>{{ this.changeTagQti ? 'Trả không giải quyết' : 'Dừng xử lý' }}</span>
                    </button>
                </ng-template>
                <ng-template #QBhC>
                    <button mat-menu-item class="menuAction" (click)="cancelQBHProcessing(dossierDetail[0]?.id, dossierDetail[0]?.code)" *ngIf="env?.OS_BDG?.isProcessDefinitionBDG == undefined || (env?.OS_BDG?.isProcessDefinitionBDG && dossierDetail[0]?.currentTask[0]?.bpmProcessDefinitionTask?.variable.canCancelDosssier == 1)">
                        <mat-icon>do_disturb</mat-icon><span >Từ chối giải quyết</span>
                    </button>
                 </ng-template>
                <ng-container *ngIf="qbhwithdrawprocess == true; then qbhdraw2 else normal2"></ng-container>
                <ng-template #qbhdraw2>
                    <ng-container *ngIf="dossierDetail[0]?.currentTask != undefined">
                        <button mat-menu-item class="menuAction" *ngIf="checkDVCLL(dossierDetail[0]?.isDVCLT , dossierDetail[0]) && (dossierDetail[0]?.dossierStatus.id !== 5 || dossierDetail[0]?.dossierStatus.id !== 4)
                        && (env?.OS_BDG?.isProcessDefinitionBDG == undefined || (env?.OS_BDG?.isProcessDefinitionBDG && dossierDetail[0]?.currentTask[0]?.bpmProcessDefinitionTask?.variable.canEvictDossier == 1))
                        && !hideRequestToWithdraw
                        && this.checkDrawQBH == true"
                        (click)="withdrawQBHDialogs(dossierDetail[0]?.id, dossierDetail[0]?.code)">
                        <mat-icon>reply</mat-icon><span>Yêu cầu rút hồ sơ</span>

                        </button>
                    </ng-container>
                </ng-template>
                <ng-template #normal2>
                <ng-container *ngIf="dossierDetail[0]?.currentTask != undefined">
                    <button mat-menu-item class="menuAction" *ngIf="checkDVCLL(dossierDetail[0]?.isDVCLT , dossierDetail[0]) && (dossierDetail[0]?.dossierStatus.id !== 5 || dossierDetail[0]?.dossierStatus.id !== 4)
                    && (env?.OS_BDG?.isProcessDefinitionBDG == undefined || (env?.OS_BDG?.isProcessDefinitionBDG && dossierDetail[0]?.currentTask[0]?.bpmProcessDefinitionTask?.variable.canEvictDossier == 1))
                    && !hideRequestToWithdraw" (click)="withdrawDialogs(dossierDetail[0]?.id, dossierDetail[0]?.code)">
                    <mat-icon>reply</mat-icon><span *ngIf="!isUserRQ;else isUserKGGTemplate">Yêu cầu rút hồ sơ</span>
                        <ng-template #isUserKGGTemplate>
                            <span>Rút hồ sơ theo yêu cầu</span>
                        </ng-template>
                    </button>
                </ng-container>
                </ng-template>
                <button class="menuAction" mat-menu-item (click)="syncAdditinalRequestIntoVBDLIS(dossierDetail[0]?.code)" *ngIf="isVbdlis && dossierDetail[0]?.extendQNI?.dossierStatusInVbdlis == 3">
                    <mat-icon>reply</mat-icon><span>Cập nhật thực hiện Nghĩa vụ tài chính qua VBDLIS</span>
                </button>
                <button class="menuAction" mat-menu-item (click)="syncPostUpdateFinishDossierVBDLIS(dossierDetail[0]?.code)" *ngIf="isVbdlis && dossierDetail[0]?.extendQNI?.isVbdlis == true &&  ( this.dossierDetail[0]?.dossierStatus?.id === 5 || this.dossierDetail[0]?.dossierStatus?.id === 6 )">
                    <mat-icon>reply</mat-icon><span>Cập nhật hồ sơ đã trả kết quả qua VBDLIS</span>
                </button>
                <!-- <button class="menuAction" mat-menu-item
                        (click)="cancelProcessing(dossierDetail[0].id, dossierDetail[0].code)"
                        *ngIf="(dossierDetail[0]?.currentTask[0]?.isFirst === 1 && showCancelProcessingInFirstTask === 1)">
                  <mat-icon>do_disturb</mat-icon>
                  <span>{{ this.changeTagQti ? 'Trả không giải quyết' : 'Dừng xử lý' }}</span>
                </button> -->
                <button class="menuAction" mat-menu-item (click)="syncPostUpdateFinishDossierILIS(dossierDetail[0]?.code, 1)" *ngIf="isIlis && dossierDetail[0]?.extendQNI?.isIlis == true && dossierDetail[0]?.extendQNI?.dossierStatusInIlis == 3">
                    <mat-icon>reply</mat-icon><span>Cập nhật thực hiện Nghĩa vụ tài chính qua ILIS</span>
                </button>
                <button class="menuAction" mat-menu-item (click)="syncPostUpdateFinishDossierILIS(dossierDetail[0]?.code, 3)" *ngIf="isIlis && dossierDetail[0]?.extendQNI?.isIlis == true && this.dossierDetail[0]?.dossierStatus?.id === 5">
                    <mat-icon>reply</mat-icon><span>Cập nhật hồ sơ đã trả kết quả qua ILIS</span>
                </button>
                <!-- <button mat-menu-item class="menuAction" (click)="printReceiptDialog()"   *ngIf="enableShowPrint == true"  >
                    <mat-icon>local_printshop</mat-icon>
                    <span>In biên lai giấy</span>
                </button> -->
                <button class="menuAction" mat-menu-item (click)="syncPostUpdateFinishDossierBHTN(dossierDetail[0]?.code, 3)" *ngIf="isBhtn && dossierDetail[0]?.extendCMU?.isBhtn == true && this.dossierDetail[0]?.dossierStatus?.id === 5">
                    <mat-icon>reply</mat-icon><span>Cập nhật hồ sơ đã trả kết quả qua BHTN</span>
                </button>
                <button *ngIf="checkPermissionNotify()" mat-menu-item class="menuAction" (click)="openNotify(dossierDetail[0]?.id, '')">
                    <mat-icon>notifications</mat-icon>
                    <span>Thông báo đến người dân</span>
                </button>
                <button class="menuAction" mat-menu-item (click)="downloadZipFileDossier(dossierDetail[0])" *ngIf="downloadFileDossierRar">
                    <mat-icon>cloud_download</mat-icon>
                    <span>Tải văn bản nén của hồ sơ</span>
                </button>
        </mat-menu>
         <!-- IGATESUPP-111043 matmenu xử lý -->
         <ng-container *ngIf="(showPaymentRequestToDetailsDossier === 1 || showRequestWithdrawDossierToDetailsDossier === 1) && this.dossierDetail[0]?.dossierStatus.id == 2 && dossierDetail[0]?.currentTask[0]?.isFirst === 1">
            <button mat-flat-button class="btnMore" [matMenuTriggerFor]="moreMenuPaymentRequestOrefusedAndWithdrawDossier">
                <mat-icon>more_horiz</mat-icon>
            </button>
        </ng-container>
        <mat-menu #moreMenuPaymentRequestOrefusedAndWithdrawDossier="matMenu">
            <button class="menuAction" mat-menu-item *ngIf="showPaymentRequestToDetailsDossier === 1 && ((this.dossierDetail[0]?.dossierStatus.id == 2 || this.dossierDetail[0]?.dossierStatus.id == 4) && paymentLater && this.dossierDetail[0]?.checkPaymentLater) && !disableRequestPayment" (click)="paymentRequest(this.dossierDetail[0]?.id, this.dossierDetail[0]?.code)">
                <mat-icon>payment</mat-icon><span>Yêu cầu thanh toán hồ sơ</span>
            </button>
            <button class="menuAction" mat-menu-item *ngIf="showRequestWithdrawDossierToDetailsDossier === 1 && (checkDVCLL(this.dossierDetail[0]?.isDVCLT , this.dossierDetail[0]) && (this.dossierDetail[0]?.dossierStatus.id !== 5 || this.dossierDetail[0]?.dossierStatus.id !== 4) && receivingPermission)" (click)="withdrawDialogs(this.dossierDetail[0]?.id, this.dossierDetail[0]?.code)">
                <mat-icon>reply</mat-icon><span>Yêu cầu rút hồ sơ</span>
            </button>
        </mat-menu>

        <!-- IGATESUPP-111043 matmenu xử lý  -->
        </div>

        <div class="content">
            <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="column" fxLayoutAlign="space-between" *ngIf="dossierDetail[0]?.code != '' && !isSCT">
                <div class="card" fxFlex='49.5'>
                    <div class="cardTitle">
                        <span i18n>Thời gian xử lý</span>
                    </div>
                    <div class="cardContent">
                        <p class="info-auto-receive-qbh" *ngIf="isCheckNoticeAutoReceiveQBH">
                            (Hồ sơ được hệ thống tự động tiếp nhận và bắt đầu tính thời gian từ <span class="bold-info-notice">ngày tiếp nhận</span>)
                        </p>
                        <p>
                            <mat-icon>move_to_inbox</mat-icon><span class="lbl" i18n>Ngày tiếp nhận: </span> {{(!!dossierDetail[0]?.oldAcceptedDate ? dossierDetail[0]?.oldAcceptedDate : dossierDetail[0]?.acceptedDate) | date : 'dd/MM/yyyy HH:mm:ss'}}
                        </p>
                        <p *ngIf="additionalRequirementDate === true && !!dossierDetail[0]?.additionalRequirementDetail?.acceptedDate">
                            <mat-icon>move_to_inbox</mat-icon><span class="lbl">Ngày tiếp nhận bổ sung: </span> {{dossierDetail[0]?.additionalRequirementDetail?.acceptedDate | date : 'dd/MM/yyyy HH:mm:ss'}}
                        </p>
                        <ng-container  *ngIf="originalAppointmentDate && !dossierDetail[0]?.extendQNI?.originalAppointmentDate == false; else normal">
                            <p *ngIf="undefindedCompleteTime === 0 && !hideInforAdditionalRequirementDate">
                                <mat-icon>rotate_90_degrees_ccw</mat-icon><span class="lbl">Ngày hẹn trả mới: </span>
                                <ng-container *ngIf="extendTimeApproval != ''; then extend else appoint;"></ng-container>
                                <ng-template #extend>
                                    <span>{{extendTimeApproval | date : 'dd/MM/yyyy HH:mm:ss'}}</span>
                                </ng-template>
                                <ng-template #appoint>
                                    <span
                                        *ngIf="dossierDetail[0]?.appointmentDate === null || dossierDetail[0]?.appointmentDate === undefined">{{timesheet?.endDate
                                        | date : 'dd/MM/yyyy HH:mm:ss'}}</span>
                                    <span
                                        *ngIf="dossierDetail[0]?.appointmentDate !== null && dossierDetail[0]?.appointmentDate !== undefined">{{dossierDetail[0]?.appointmentDate
                                        | date : 'dd/MM/yyyy HH:mm:ss'}}</span>
                                </ng-template>
                            </p>
                            <p *ngIf="undefindedCompleteTime === 0 && !hideInforAdditionalRequirementDate">
                                <mat-icon>rotate_90_degrees_ccw</mat-icon><span class="lbl">Ngày hẹn trả ban đầu (trước khi thực hiện bổ sung): </span>
                                    <span *ngIf="dossierDetail[0]?.extendQNI?.originalAppointmentDate !== null || dossierDetail[0]?.extendQNI?.originalAppointmentDate !== undefined">{{dossierDetail[0]?.extendQNI?.originalAppointmentDate
                                        | date : 'dd/MM/yyyy HH:mm:ss'}}</span>
                            </p>
                        </ng-container>
                        <ng-template #normal>
                            <p *ngIf="undefindedCompleteTime === 0 && !hideInforAdditionalRequirementDate">
                                <mat-icon>rotate_90_degrees_ccw</mat-icon><span class="lbl" i18n>Ngày hẹn trả: </span>
                                <ng-container *ngIf="extendTimeApproval != '' && this.dossierDetail[0]?.dossierStatus?.id === 10; then extend else appoint;"></ng-container>
                                <ng-template #extend>
                                    <span>{{extendTimeApproval | date : 'dd/MM/yyyy HH:mm:ss'}}</span>
                                </ng-template>
                                <ng-template #appoint>
                                    <span *ngIf="currentAppointmentDate">{{currentAppointmentDate | date : 'dd/MM/yyyy HH:mm:ss'}}</span>
                                    <ng-container *ngIf="!currentAppointmentDate">
                                        <span
                                            *ngIf="dossierDetail[0]?.appointmentDate === null || dossierDetail[0]?.appointmentDate === undefined">{{timesheet?.endDate
                                            | date : 'dd/MM/yyyy HH:mm:ss'}}</span>
                                        <span
                                            *ngIf="dossierDetail[0]?.appointmentDate !== null && dossierDetail[0]?.appointmentDate !== undefined">{{dossierDetail[0]?.appointmentDate
                                            | date : 'dd/MM/yyyy HH:mm:ss'}}</span>
                                    </ng-container>
                                </ng-template>
                            </p>
                        </ng-template>
                        <p *ngIf="undefindedCompleteTime === 0 && !hideInforAdditionalRequirementDate && hideDueProcessInfo ==false">
                            <mat-icon>developer_board</mat-icon><span class="lbl" i18n>Hạn xử lý toàn quy trình: </span>
                            <ng-container *ngIf="procedureDetail[0]?.sameDayPayProfile == true && dossierDetail[0]?.processingTime == 1; else dueBlock2">
                                <ng-container *ngIf="extendTimeApproval != '' && this.dossierDetail[0]?.dossierStatus?.id === 10; then extend else appoint;"></ng-container>
                                <ng-template #extend>
                                    <span>{{extendTimeApproval | date : 'dd/MM/yyyy HH:mm:ss'}} - </span>
                                </ng-template>
                                <ng-template #appoint>
                                    <span *ngIf="currentAppointmentDate">{{currentAppointmentDate | date : 'dd/MM/yyyy HH:mm:ss'}} - </span>
                                    <ng-container *ngIf="!currentAppointmentDate">
                                        <span
                                            *ngIf="dossierDetail[0]?.appointmentDate === null || dossierDetail[0]?.appointmentDate === undefined">{{timesheet?.endDate
                                            | date : 'dd/MM/yyyy HH:mm:ss'}} - </span>
                                        <span
                                            *ngIf="dossierDetail[0]?.appointmentDate !== null && dossierDetail[0]?.appointmentDate !== undefined">{{dossierDetail[0]?.appointmentDate
                                            | date : 'dd/MM/yyyy HH:mm:ss'}} - </span>
                                    </ng-container>
                                </ng-template>
                            </ng-container>
                            <ng-template #dueBlock2>
                                <ng-container *ngIf="enableRequestAdditionalDossier; else dueBlock">
                                    {{ (dossierDetail[0]?.dueDate? dossierDetail[0]?.dueDate : timesheet?.endDate)  | date : 'dd/MM/yyyy HH:mm:ss'}} -
                                </ng-container>
                                <ng-template #dueBlock>
                                    {{timesheet?.endDate | date : 'dd/MM/yyyy HH:mm:ss'}} -
                                  </ng-template>
                                  <span *ngIf="calculateAppointmentDate == 1 && durationCalculateAppointmentDate != ''">
                                    {{durationCalculateAppointmentDate}}
                                </span>
                               </ng-template>
                            <span *ngIf="dossierDetail[0]?.processingTimeUnit == 'y'">
                                {{dossierDetail[0]?.processingTime}} <span i18n>năm</span>
                            </span>
                            <span *ngIf="dossierDetail[0]?.processingTimeUnit == 'M'">
                                {{dossierDetail[0]?.processingTime}} <span i18n>tháng</span>
                            </span>
                            <span *ngIf="dossierDetail[0]?.processingTimeUnit == 'd' && selectedProcess?.processDefinition?.dynamicVariable?.isProcessingTimeUnitType === true">
                                {{dossierDetail[0]?.processingTime}} <span i18n>ngày làm việc</span>
                            </span>
                            <span *ngIf="dossierDetail[0]?.processingTimeUnit == 'd' && selectedProcess?.processDefinition?.dynamicVariable?.isProcessingTimeUnitType !== true">
                                {{dossierDetail[0]?.processingTime}} <span i18n>ngày</span>
                            </span>
                            <span *ngIf="dossierDetail[0]?.processingTimeUnit == 'H:m:s'">
                                {{dossierDetail[0]?.processingTime}} <span i18n>giờ làm việc</span>
                            </span>
                            <span *ngIf="dossierDetail[0]?.processingTimeUnit == 'h'">
                                {{dossierDetail[0]?.processingTime}} <span i18n>giờ làm việc</span>
                            </span>
                            <span *ngIf="dossierDetail[0]?.processingTimeUnit == 'm'">
                                {{dossierDetail[0]?.processingTime}} <span >phút</span>
                            </span>
                            <span [ngClass]="{'overdue': timesheet?.isOverDue == true}" *ngIf="statusNeedsCalculatorTiming.includes(dossierDetail[0]?.dossierStatus.id) && showRemainTime">
                                <span *ngIf="timesheet.isOverDue == false">
                                    <span i18n> (Còn lại </span>
                            </span>
                            <span *ngIf="timesheet?.isOverDue == true && statusNeedsCalculatorTiming.includes(dossierDetail[0]?.dossierStatus.id)">
                                    <span i18n> (Đã quá hạn </span>
                            </span>
                            <span class="timeNumber">{{timesheet?.dueTimer?.day}}</span><span i18n> ngày </span>
                            <span class="timeNumber">{{timesheet?.dueTimer?.hour}}</span><span i18n> giờ </span>
                            <span class="timeNumber">{{timesheet?.dueTimer?.minute}}</span><span i18n> phút </span>
                            <span class="timeNumber">{{timesheet?.dueTimer?.second}}</span><span i18n> giây</span>)
                            </span>
                        </p>
                        <p *ngIf="undefindedCompleteTime === 1 && !hideInforAdditionalRequirementDate  && hideDueProcessInfo ==false">
                            <mat-icon>developer_board</mat-icon><span class="lbl" i18n>Hạn xử lý toàn quy trình: </span>(<i i18n>Không xác định thời hạn</i>)
                        </p>
                        <p *ngIf="!hideInforAdditionalRequirementDate">
                            <mat-icon>queue</mat-icon><span class="lbl" i18n>Hạn xử lý: </span>
                            <span *ngIf="undefindedCompleteTimeTask === 0">{{timesheet?.receivingDue | date : 'dd/MM/yyyy HH:mm:ss'}}</span>
                            <span *ngIf="undefindedCompleteTimeTask === 1">
                                (<i i18n>Không xác định thời hạn</i>)
                            </span>
                        </p>
                        <p *ngIf="undefindedCompleteTime === 0 && undefindedCompleteTimeTask === 0 && statusNeedsCalculatorTiming.includes(dossierDetail[0]?.dossierStatus.id) && showRemainTime">
                            <mat-icon>more_time</mat-icon><span class="lbl" i18n>Thời gian còn lại: </span>
                            <span *ngIf="isTaskComplete(currentTask[0]?.activitiTask?.status) == false && statusNeedsCalculatorTiming.includes(dossierDetail[0]?.dossierStatus.id)">
                                <span *ngIf="timesheet?.isTaskOverDue == true" class="overdue" i18n>Đã quá hạn </span>
                            <span class="timer">
                                    <span class="timeNumber">{{timesheet?.taskDueTimer?.day}}</span><span i18n> ngày </span>
                            </span>
                            <span class="timer">
                                    <span class="timeNumber">{{timesheet?.taskDueTimer?.hour}}</span><span i18n> giờ </span>
                            </span>
                            <span class="timer">
                                    <span class="timeNumber">{{timesheet?.taskDueTimer?.minute}}</span><span i18n> phút </span>
                            </span>
                            <span class="timer">
                                    <span class="timeNumber">{{timesheet?.taskDueTimer?.second}}</span><span i18n> giây</span>
                            </span>
                            </span>
                            <span *ngIf="isTaskComplete(currentTask[0]?.activitiTask?.status) == true" i18n>Đã kết thúc</span>
                        </p>
                        <p *ngIf="undefindedCompleteTime === 1">
                            <mat-icon>more_time</mat-icon><span class="lbl" i18n>Thời gian còn lại: </span>
                            <i i18n>Không xác định thời hạn</i>
                        </p>
                        <p *ngIf="dossierDetail[0]?.completedDate">
                            <mat-icon>event</mat-icon><span class="lbl" i18n>Ngày có kết quả: </span> {{dossierDetail[0]?.completedDate | date : 'dd/MM/yyyy HH:mm:ss'}}
                        </p>
                        <p *ngIf="dossierDetail[0]?.returnedDate">
                            <mat-icon>reply</mat-icon><span class="lbl" i18n>Ngày trả kết quả: </span> {{dossierDetail[0]?.returnedDate | date : 'dd/MM/yyyy HH:mm:ss'}}
                        </p>
                        <p>
                            <mat-icon>move_to_inbox</mat-icon>
                            <span class="lbl" i18n>Hình thức tiếp nhận</span>:
                            <span>{{dossierDetail[0]?.applyMethod?.name}}</span>
                            <span *ngIf="vpcConfirmOriginalDossier == 1">
                              (
                                <span class="lbl">Hồ sơ gốc: </span>
                                <span *ngIf="dossierDetail[0]?.extendDossierOriginalVPC != null">
                                  <span *ngIf="dossierDetail[0]?.extendDossierOriginalVPC?.updateStatus?.id == 1">
                                    Đã nhận
                                  </span>
                                  <span *ngIf="dossierDetail[0].extendDossierOriginalVPC?.updateStatus?.id == 2">
                                    Đã trả
                                  </span>
                                </span>
                                <span *ngIf="dossierDetail[0].extendDossierOriginalVPC == null">
                                  Chưa nhận
                                </span>
                              )
                            </span>
                        </p>
                        <p>
                            <mat-icon>move_to_inbox</mat-icon>
                            <span class="lbl" i18n>Hình thức nhận kết quả hồ sơ</span>:
                            <span *ngIf="!!dossierDetail[0]?.dossierReceivingKind?.name && dossierDetail[0]?.dossierReceivingKind?.name.length !== 0">
                                {{dossierDetail[0]?.dossierReceivingKind?.name[0]?.name}}
                            </span>
                            <span *ngIf="showDossierVnpostDetail && (!!textDossierVnpostDetailrcSend || !!textDossierVnpostDetailrcReceive)">
                                (
                            </span>
                            <span *ngIf="showDossierVnpostDetail && !!textDossierVnpostDetailrcSend">
                                {{textDossierVnpostDetailrcSend}}
                            </span>
                            <span *ngIf="showDossierVnpostDetail && !!textDossierVnpostDetailrcSend && !!textDossierVnpostDetailrcReceive">
                                +
                            </span>
                            <span *ngIf="showDossierVnpostDetail && !!textDossierVnpostDetailrcReceive">
                                {{textDossierVnpostDetailrcReceive}}
                            </span>
                            <span *ngIf="showDossierVnpostDetail && (!!textDossierVnpostDetailrcSend || !!textDossierVnpostDetailrcReceive)">
                                )
                            </span>
                        </p>
                        <p *ngIf="vpcAuthorizeReceiveResult == 1">
                          <mat-icon>move_to_inbox</mat-icon>
                          <span class="lbl">Ủy quyền nhận kết quả</span>:
                          <span *ngIf="dossierDetail[0]?.extendDossierAuthorizeResultVPC != null">
                            Người nhận: {{dossierDetail[0]?.extendDossierAuthorizeResultVPC?.fullName}}; 
                            CCCD: {{dossierDetail[0]?.extendDossierAuthorizeResultVPC?.identityNumber}};
                            SĐT: {{dossierDetail[0]?.extendDossierAuthorizeResultVPC?.phoneNumber}}
                          </span>
                        </p>
                        <p *ngIf="isWithdraw === 1">
                            <mat-icon>rotate_90_degrees_ccw</mat-icon>
                            <span class="lbl" i18n>Ngày trả hồ sơ</span>:
                            <span>{{dossierDetail[0]?.withdrawDate | date : 'dd/MM/yyyy HH:mm:ss'}}</span>
                        </p>
                        <p *ngIf="dossierDetail[0]?.dossierStatus?.id === 6 || dossierDetail[0]?.dossierStatus?.id === 3">
                            <mat-icon>description</mat-icon>
                            <span class="lbl" i18n>Lý do(Tạm dừng/ Gia hạn/ Trả dân)</span>:
                            <span [innerHTML]="dossierDetail[0]?.dossierStatus?.comment"></span>
                        </p>
                        <p *ngIf="apologyFile?.length > 0">
                            <mat-icon>text_snippet</mat-icon>
                            <span class="lbl" i18n>File đính kèm</span>:
                            <span>
                                <li *ngFor='let file of apologyFile;' style="padding-left: 2rem;">
                                    <u (click)="downloadFile(file.id, file.filename)" style="color: blue; cursor: pointer;">{{file.filename}}</u>
                                </li>
                            </span>
                        </p>


                    </div>
                </div>
                <div class="card" fxFlex='49.5'>
                    <div class="cardTitle">
                        <span i18n>Người xử lý</span>
                    </div>
                    <div class="cardContent">
                        <div *ngFor='let userTask of userTaskCurrent; let j = index'>
                            <p *ngIf="userTask?.bpmProcessDefinitionTask?.name != undefined">
                                <mat-icon>account_tree</mat-icon><span class="lbl" i18n>Công việc: </span> {{userTask?.bpmProcessDefinitionTask?.definitionTask['name']}}
                            </p>
                            <p *ngIf="userTask?.assignee != undefined || userTask.candidateUser?.length !== 0">
                                <mat-icon>account_circle</mat-icon><span class="lbl" i18n>Tên người xử lý: </span>
                                <span *ngIf="!!userTask.assignee && !!userTask.assignee.id">{{userTask.assignee.fullname}}</span>
                                <span *ngIf="(!userTask.assignee || !userTask.assignee.id) && userTask.candidateUser?.length !== 0">
                                    <span *ngFor="let user of userTask.candidateUser; let i = index;">
                                        <span>{{user.fullname}}</span>
                                        <span *ngIf="i !== userTask.candidateUser.length - 1">, </span>
                                    </span>
                                </span>
                                <span *ngIf="(!userTask.assignee || !userTask.assignee.id) && userTask.candidateUser?.length === 0 && userTask.candidateGroup?.length !== 0">
                                    {{userTask.agency?.name[0]?.name}}
                                </span>
                            </p>
                            <p *ngIf="userTask?.sender != undefined">
                                <mat-icon>record_voice_over</mat-icon><span class="lbl" i18n>Người theo dõi: </span> {{userTask?.sender?.fullname}}
                            </p>
                            <hr *ngIf="userTaskCurrent.length > 1 && j !== userTaskCurrent.length - 1" />
                        </div>

                        <!--  <p>
                            <mat-icon>mediation</mat-icon><span class="lbl" i18n>Trạng thái: </span>
                            <span class="status"
                                [ngStyle]="{'background-color': '#34495e'}"
                                *ngIf="currentTask[0].activitiTask.status == 'CREATED'" i18n>CREATED</span>
                            <span class="status"
                                [ngStyle]="{'background-color': '#f39c12'}"
                                *ngIf="currentTask[0].activitiTask.status == 'ASSIGNED'" i18n>ASSIGNED</span>
                            <span class="status"
                                [ngStyle]="{'background-color': '#ef6c00'}"
                                *ngIf="currentTask[0].activitiTask.status == 'SUSPENDED'" i18n>SUSPENDED</span>
                            <span class="status"
                                [ngStyle]="{'background-color': '#3fa200'}"
                                *ngIf="currentTask[0].activitiTask.status == 'COMPLETED'" i18n>COMPLETED</span>
                            <span class="status"
                                [ngStyle]="{'background-color': '#757575'}"
                                *ngIf="currentTask[0].activitiTask.status == 'CANCELLED'" i18n>CANCELLED</span>
                            <span class="status"
                                [ngStyle]="{'background-color': '#DE1212'}"
                                *ngIf="currentTask[0].activitiTask.status == 'DELETED'" i18n>DELETED</span>
                        </p> -->
                    </div>
                </div>
            </div>
            <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="column" fxLayoutAlign="space-between" *ngIf="dossierDetail[0]?.code != '' && isSCT">
                <div class="card" fxFlex='49.5' (click)='onClickOpenMenuProcessTime()'>
                    <div class="cardTitle" *ngIf="xpandStatus == false">
                        <span i18n>Thời gian xử lý</span>
                        <mat-icon>expand_more</mat-icon>
                    </div>
                    <div class="cardTitle" *ngIf="xpandStatus == true">
                        <span i18n>Thời gian xử lý</span>
                        <mat-icon>expand_less</mat-icon>
                    </div>
                    <mat-expansion-panel [(expanded)]="xpandStatus">
                        <p>
                            <mat-icon>move_to_inbox</mat-icon><span class="lbl" i18n>Ngày tiếp nhận: </span> {{(!!dossierDetail[0]?.oldAcceptedDate ? dossierDetail[0]?.oldAcceptedDate : dossierDetail[0]?.acceptedDate) | date : 'dd/MM/yyyy HH:mm:ss'}}
                        </p>
                        <p *ngIf="additionalRequirementDate === true && !!dossierDetail[0]?.additionalRequirementDetail?.acceptedDate">
                            <mat-icon>move_to_inbox</mat-icon><span class="lbl">Ngày tiếp nhận bổ sung: </span> {{dossierDetail[0]?.additionalRequirementDetail?.acceptedDate | date : 'dd/MM/yyyy HH:mm:ss'}}
                        </p>
                        <ng-container  *ngIf="originalAppointmentDate && !dossierDetail[0]?.extendQNI?.originalAppointmentDate == false; else normal">
                            <p *ngIf="undefindedCompleteTime === 0 && !hideInforAdditionalRequirementDate">
                                <mat-icon>rotate_90_degrees_ccw</mat-icon><span class="lbl">Ngày hẹn trả mới: </span>
                                <ng-container *ngIf="extendTimeApproval != ''; then extend else appoint;"></ng-container>
                                <ng-template #extend>
                                    <span>{{extendTimeApproval | date : 'dd/MM/yyyy HH:mm:ss'}}</span>
                                </ng-template>
                                <ng-template #appoint>
                                    <span
                                        *ngIf="dossierDetail[0]?.appointmentDate === null || dossierDetail[0]?.appointmentDate === undefined">{{timesheet?.endDate
                                        | date : 'dd/MM/yyyy HH:mm:ss'}}</span>
                                    <span
                                        *ngIf="dossierDetail[0]?.appointmentDate !== null && dossierDetail[0]?.appointmentDate !== undefined">{{dossierDetail[0]?.appointmentDate
                                        | date : 'dd/MM/yyyy HH:mm:ss'}}</span>
                                </ng-template>
                            </p>
                            <p *ngIf="undefindedCompleteTime === 0 && !hideInforAdditionalRequirementDate">
                                <mat-icon>rotate_90_degrees_ccw</mat-icon><span class="lbl">Ngày hẹn trả ban đầu (trước khi thực hiện bổ sung): </span>
                                    <span *ngIf="dossierDetail[0]?.extendQNI?.originalAppointmentDate !== null || dossierDetail[0]?.extendQNI?.originalAppointmentDate !== undefined">{{dossierDetail[0]?.extendQNI?.originalAppointmentDate
                                        | date : 'dd/MM/yyyy HH:mm:ss'}}</span>
                            </p>
                        </ng-container>
                        <ng-template #normal>
                            <p *ngIf="undefindedCompleteTime === 0">
                                <mat-icon>rotate_90_degrees_ccw</mat-icon><span class="lbl" i18n>Ngày hẹn trả: </span>
                                <ng-container *ngIf="extendTimeApproval != '' && this.dossierDetail[0]?.dossierStatus?.id === 10; then extend else appoint;"></ng-container>
                                <ng-template #extend>
                                    <span>{{extendTimeApproval | date : 'dd/MM/yyyy HH:mm:ss'}}</span>
                                </ng-template>
                                <ng-template #appoint>
                                    <span *ngIf="currentAppointmentDate">{{currentAppointmentDate | date : 'dd/MM/yyyy HH:mm:ss'}}</span>
                                    <ng-container *ngIf="!currentAppointmentDate">
                                        <span
                                            *ngIf="dossierDetail[0]?.appointmentDate === null || dossierDetail[0]?.appointmentDate === undefined">{{timesheet?.endDate
                                            | date : 'dd/MM/yyyy HH:mm:ss'}}</span>
                                        <span
                                            *ngIf="dossierDetail[0]?.appointmentDate !== null && dossierDetail[0]?.appointmentDate !== undefined">{{dossierDetail[0]?.appointmentDate
                                            | date : 'dd/MM/yyyy HH:mm:ss'}}</span>
                                    </ng-container>
                                </ng-template>
                            </p>
                        </ng-template>
                        <p *ngIf="undefindedCompleteTime === 0 && !hideInforAdditionalRequirementDate  && hideDueProcessInfo ==false">
                            <mat-icon>developer_board</mat-icon><span class="lbl" i18n>Hạn xử lý toàn quy trình: </span>
                            <ng-container *ngIf="enableRequestAdditionalDossier; else dueBlock">
                                {{ (dossierDetail[0]?.dueDate? dossierDetail[0]?.dueDate : timesheet?.endDate)  | date : 'dd/MM/yyyy HH:mm:ss'}} -
                            </ng-container>
                            <ng-template #dueBlock>
                                {{timesheet?.endDate | date : 'dd/MM/yyyy HH:mm:ss'}} -
                              </ng-template>
                            <span *ngIf="showDurationCalculateAppointmentDate">
                                {{durationCalculateAppointmentDate}}
                            </span>
                            <span *ngIf="!showDurationCalculateAppointmentDate">
                                <span *ngIf="dossierDetail[0]?.processingTimeUnit == 'y'">
                                    {{dossierDetail[0]?.processingTime}} <span i18n>năm</span>
                                </span>
                                <span *ngIf="dossierDetail[0]?.processingTimeUnit == 'M'">
                                    {{dossierDetail[0]?.processingTime}} <span i18n>tháng</span>
                                </span>
                                <span *ngIf="dossierDetail[0]?.processingTimeUnit == 'd' && selectedProcess?.processDefinition?.dynamicVariable?.isProcessingTimeUnitType === true">
                                    {{dossierDetail[0]?.processingTime}} <span i18n>ngày làm việc</span>
                                </span>
                                <span *ngIf="dossierDetail[0]?.processingTimeUnit == 'd' && selectedProcess?.processDefinition?.dynamicVariable?.isProcessingTimeUnitType !== true">
                                    {{dossierDetail[0]?.processingTime}} <span i18n>ngày</span>
                                </span>
                                <span *ngIf="dossierDetail[0]?.processingTimeUnit == 'H:m:s'">
                                    {{dossierDetail[0]?.processingTime}} <span i18n>giờ làm việc</span>
                                </span>
                                <span *ngIf="dossierDetail[0]?.processingTimeUnit == 'h'">
                                    {{dossierDetail[0]?.processingTime}} <span i18n>giờ làm việc</span>
                                </span>
                                <span *ngIf="dossierDetail[0]?.processingTimeUnit == 'm'">
                                    {{dossierDetail[0]?.processingTime}} <span >phút</span>
                                </span>
                            </span>
                            <span [ngClass]="{'overdue': timesheet?.isOverDue == true}" *ngIf="statusNeedsCalculatorTiming.includes(dossierDetail[0]?.dossierStatus.id) && showRemainTime">
                                <span *ngIf="timesheet.isOverDue == false">
                                    <span i18n> (Còn lại </span>
                            </span>
                            <span *ngIf="timesheet?.isOverDue == true && statusNeedsCalculatorTiming.includes(dossierDetail[0]?.dossierStatus.id)">
                                    <span i18n> (Đã quá hạn </span>
                            </span>
                            <span class="timeNumber">{{timesheet?.dueTimer?.day}}</span><span i18n> ngày </span>
                            <span class="timeNumber">{{timesheet?.dueTimer?.hour}}</span><span i18n> giờ </span>
                            <span class="timeNumber">{{timesheet?.dueTimer?.minute}}</span><span i18n> phút </span>
                            <span class="timeNumber">{{timesheet?.dueTimer?.second}}</span><span i18n> giây</span>)
                            </span>
                        </p>
                        <p *ngIf="undefindedCompleteTime === 1 && !hideInforAdditionalRequirementDate  && hideDueProcessInfo ==false">
                            <mat-icon>developer_board</mat-icon><span class="lbl" i18n>Hạn xử lý toàn quy trình: </span>(<i i18n>Không xác định thời hạn</i>)
                        </p>
                        <p *ngIf="!hideInforAdditionalRequirementDate">
                            <mat-icon>queue</mat-icon><span class="lbl" i18n>Hạn xử lý: </span>
                            <span *ngIf="undefindedCompleteTimeTask === 0">{{timesheet?.receivingDue | date : 'dd/MM/yyyy HH:mm:ss'}}</span>
                            <span *ngIf="undefindedCompleteTimeTask === 1">
                                (<i i18n>Không xác định thời hạn</i>)
                            </span>
                        </p>
                        <p *ngIf="undefindedCompleteTime === 0 && undefindedCompleteTimeTask === 0 && statusNeedsCalculatorTiming.includes(dossierDetail[0]?.dossierStatus.id) && showRemainTime">
                            <mat-icon>more_time</mat-icon><span class="lbl" i18n>Thời gian còn lại: </span>
                            <span *ngIf="isTaskComplete(currentTask[0]?.activitiTask?.status) == false && statusNeedsCalculatorTiming.includes(dossierDetail[0]?.dossierStatus.id)">
                                <span *ngIf="timesheet?.isTaskOverDue == true" class="overdue" i18n>Đã quá hạn </span>
                            <span class="timer">
                                    <span class="timeNumber">{{timesheet?.taskDueTimer?.day}}</span><span i18n> ngày </span>
                            </span>
                            <span class="timer">
                                    <span class="timeNumber">{{timesheet?.taskDueTimer?.hour}}</span><span i18n> giờ </span>
                            </span>
                            <span class="timer">
                                    <span class="timeNumber">{{timesheet?.taskDueTimer?.minute}}</span><span i18n> phút </span>
                            </span>
                            <span class="timer">
                                    <span class="timeNumber">{{timesheet?.taskDueTimer?.second}}</span><span i18n> giây</span>
                            </span>
                            </span>
                            <span *ngIf="isTaskComplete(currentTask[0]?.activitiTask?.status) == true" i18n>Đã kết thúc</span>
                        </p>
                        <p *ngIf="undefindedCompleteTime === 1">
                            <mat-icon>more_time</mat-icon><span class="lbl" i18n>Thời gian còn lại: </span>
                            <i i18n>Không xác định thời hạn</i>
                        </p>
                        <p *ngIf="dossierDetail[0]?.completedDate">
                            <mat-icon>event</mat-icon><span class="lbl" i18n>Ngày có kết quả: </span> {{dossierDetail[0]?.completedDate | date : 'dd/MM/yyyy HH:mm:ss'}}
                        </p>
                        <p *ngIf="dossierDetail[0]?.returnedDate">
                            <mat-icon>reply</mat-icon><span class="lbl" i18n>Ngày trả kết quả: </span> {{dossierDetail[0]?.returnedDate | date : 'dd/MM/yyyy HH:mm:ss'}}
                        </p>
                        <p>
                            <mat-icon>move_to_inbox</mat-icon>
                            <span class="lbl" i18n>Hình thức tiếp nhận</span>:
                            <span>{{dossierDetail[0]?.applyMethod?.name}}</span>
                            <span *ngIf="vpcConfirmOriginalDossier == 1">
                              (
                                <span class="lbl">Hồ sơ gốc: </span>
                                <span *ngIf="dossierDetail[0]?.extendDossierOriginalVPC != null">
                                  <span *ngIf="dossierDetail[0]?.extendDossierOriginalVPC?.updateStatus?.id == 1">
                                    Đã nhận
                                  </span>
                                  <span *ngIf="dossierDetail[0]?.extendDossierOriginalVPC?.updateStatus?.id == 2">
                                    Đã trả
                                  </span>
                                </span>
                                <span *ngIf="dossierDetail[0]?.extendDossierOriginalVPC == null">
                                  Chưa nhận
                                </span>
                              )
                            </span>
                        </p>
                        <p>
                            <mat-icon>move_to_inbox</mat-icon>
                            <span class="lbl" i18n>Hình thức nhận kết quả hồ sơ</span>:
                            <span *ngIf="!!dossierDetail[0]?.dossierReceivingKind?.name && dossierDetail[0]?.dossierReceivingKind?.name.length !== 0">
                                {{dossierDetail[0]?.dossierReceivingKind?.name[0]?.name}}
                            </span>
                        </p>
                        <p *ngIf="vpcAuthorizeReceiveResult == 1">
                          <mat-icon>move_to_inbox</mat-icon>
                          <span class="lbl">Ủy quyền nhận kết quả</span>:
                          <span *ngIf="dossierDetail[0]?.extendDossierAuthorizeResultVPC != null">
                            Người nhận: {{dossierDetail[0]?.extendDossierAuthorizeResultVPC?.fullName}}; 
                            CCCD: {{dossierDetail[0]?.extendDossierAuthorizeResultVPC?.identityNumber}};
                            SĐT: {{dossierDetail[0]?.extendDossierAuthorizeResultVPC?.phoneNumber}}
                          </span>
                        </p>
                        <p *ngIf="isWithdraw === 1">
                            <mat-icon>rotate_90_degrees_ccw</mat-icon>
                            <span class="lbl" i18n>Ngày trả hồ sơ</span>:
                            <span>{{dossierDetail[0]?.withdrawDate | date : 'dd/MM/yyyy HH:mm:ss'}}</span>
                        </p>
                        <p *ngIf="dossierDetail[0]?.dossierStatus?.id === 6 || dossierDetail[0]?.dossierStatus?.id === 3">
                            <mat-icon>description</mat-icon>
                            <span class="lbl" i18n>Lý do(Tạm dừng/ Gia hạn/ Trả dân)</span>:
                            <span [innerHTML]="dossierDetail[0]?.dossierStatus?.comment"></span>
                        </p>
                        <p *ngIf="apologyFile?.length > 0">
                            <mat-icon>text_snippet</mat-icon>
                            <span class="lbl" i18n>File đính kèm</span>:
                            <span>
                                <li *ngFor='let file of apologyFile;' style="padding-left: 2rem;">
                                    <u (click)="downloadFile(file.id, file.filename)" style="color: blue; cursor: pointer;">{{file.filename}}</u>
                                </li>
                            </span>
                        </p>
                    </mat-expansion-panel>
                </div>
                <div class="card" fxFlex='49.5' (click)='onClickOpenMenuHandlers()'>
                    <div class="cardTitle" *ngIf="xpandStatus1 == false">
                        <span i18n>Người xử lý</span>
                        <mat-icon>expand_more</mat-icon>
                    </div>
                    <div class="cardTitle" *ngIf="xpandStatus1 == true">
                        <span i18n>Người xử lý</span>
                        <mat-icon>expand_less</mat-icon>
                    </div>
                    <mat-expansion-panel [(expanded)]="xpandStatus1">
                        <div *ngFor='let userTask of userTaskCurrent; let j = index'>
                            <p *ngIf="userTask?.bpmProcessDefinitionTask?.name != undefined">
                                <mat-icon>account_tree</mat-icon><span class="lbl" i18n>Công việc: </span> {{userTask?.bpmProcessDefinitionTask?.definitionTask['name']}}
                            </p>
                            <p *ngIf="userTask?.assignee != undefined || userTask.candidateUser?.length !== 0">
                                <mat-icon>account_circle</mat-icon><span class="lbl" i18n>Tên người xử lý: </span>
                                <span *ngIf="!!userTask.assignee && !!userTask.assignee.id">{{userTask.assignee.fullname}}</span>
                                <span *ngIf="(!userTask.assignee || !userTask.assignee.id) && userTask.candidateUser?.length !== 0">
                                    <span *ngFor="let user of userTask.candidateUser; let i = index;">
                                        <span>{{user.fullname}}</span>
                                        <span *ngIf="i !== userTask.candidateUser.length - 1">, </span>
                                    </span>
                                </span>
                                <span *ngIf="(!userTask.assignee || !userTask.assignee.id) && userTask.candidateUser?.length === 0 && userTask.candidateGroup?.length !== 0">
                                    {{userTask.agency?.name[0]?.name}}
                                </span>
                            </p>
                            <p *ngIf="userTask?.sender != undefined">
                                <mat-icon>record_voice_over</mat-icon><span class="lbl" i18n>Người theo dõi: </span> {{userTask?.sender?.fullname}}
                            </p>
                            <hr *ngIf="userTaskCurrent.length > 1 && j !== userTaskCurrent.length - 1" />
                        </div>
                        <div *ngIf="!!isDisplayLienThongInfo && enableConfigTotalDayProcessAgency"> <!--sonnn.qbh-IGATESUPP-46020-->
                            <hr>
                            <p>
                                <mat-icon>account_circle</mat-icon><span class="lbl">Cơ quan tiếp nhận: </span> {{nameAgencyAccepter}}
                            </p>
                            <p>
                                <mat-icon>account_circle</mat-icon><span class="lbl">Cơ quan tham gia quy trình: </span> {{nameAgencyJoinProcess}}
                            </p>
                            <p>
                                <mat-icon>more_time</mat-icon><span class="lbl">Thời gian tiếp nhận: </span> {{timeAccepter | date : 'dd/MM/yyyy HH:mm:ss'}}
                            </p>
                            <p *ngIf="!hideInforAdditionalRequirementDate"><mat-icon>queue</mat-icon><span class="lbl">Hạn xử lý: </span> {{timeProcessAllAgency | date : 'dd/MM/yyyy HH:mm:ss'}}</p>
                        </div>
                    </mat-expansion-panel>
                </div>
            </div>
            <div class="infoTabs">
              <get-form-orgin
                [fullname]="!!storage468?.baseCitizenKey ? applicantEForm?.data?.data[storage468?.baseCitizenKey?.fullnameKey] : applicantEForm?.data?.data?.fullname"
                [identityNumber]="!!storage468?.baseCitizenKey ? applicantEForm?.data?.data[storage468?.baseCitizenKey?.identityNumberKey] : applicantEForm?.data?.data?.identityNumber"
                [birthday]="!!storage468?.baseCitizenKey ? applicantEForm?.data?.data[storage468?.baseCitizenKey?.birthdayKey] : applicantEForm?.data?.data?.birthday"
                #getFormOrgin>
              </get-form-orgin>
                <mat-tab-group dynamicHeight [(selectedIndex)]="infoTabsIndex" (selectedTabChange)="onTabChange($event);onloadFormOrgin($event);" [class.hideBody]="isHideBody">
                    <mat-tab>
                        <ng-template mat-tab-label>
                            <mat-icon class="tabIcon">assignment_ind</mat-icon>
                            <span i18n>Thông tin chung</span>
                        </ng-template>
                        <div class="card" fxFlex='grow' fxLayoutAlign="right" *ngIf="iGateDossierProcessCheckCSDLDC">
                            <button mat-button style=" background-color: #ce7a58;
                            color: #fff;
                            margin: 0.2em 0.3em;" (click)="checkCSDLDC(1)">
                                <mat-icon>check</mat-icon>
                                <span>Kiểm tra chủ hồ sơ từ CSDLDC</span>
                            </button>
                            <button mat-button style=" background-color: #ce7a58;
                            color: #fff;
                            margin: 0.2em 0.3em;" (click)="checkCSDLDC(0)"  *ngIf="!laChuHs">
                                <mat-icon>check</mat-icon>
                                <span>Kiểm tra người nộp từ CSDLDC</span>
                            </button>
                        </div>
                        <div class="card" fxFlex='grow'>
                            <div class="cardTitle" fxLayoutAlign="space-between">
                                <span i18n>Thông tin chung</span>
                                <button mat-button class="cardAction" (click)="updateGeneral()" *ngIf="!offDossierDetailUpdate && isTheHandler == true && isTaskComplete(currentTask[0].activitiTask.status) == false && isButtonUpdateInfoKGG == false">
                                    <mat-icon>edit</mat-icon>
                                    <span i18n>Cập nhật thông tin</span>
                                </button>
                            </div>
                            <div class="cardContent">
                                <div class="thoaiisolate">
                                    <get-enterprise-info  [eformId]="applicantEForm.id" [procedureId]="procedureId"
                                      (onLoad)="onLoadEnterpriseInfo($event)">
                                   </get-enterprise-info>
                                    <formio *ngIf="applicantEForm.id != undefined && applicantEForm.id != ''" [form]="applicantEForm.component" [submission]="applicantEForm.data" [renderOptions]="applicantEForm.renderOptions" [readOnly]="true" [viewOnly]="true">
                                    </formio>
                                </div>
                                <!-- <table class="tblInfo">
                                    <colgroup>
                                        <col span="1" style="width: 15%;">
                                        <col span="1" style="width: 35%;">
                                        <col span="1" style="width: 15%;">
                                        <col span="1" style="width: 35%;">
                                    </colgroup>
                                    <tbody>
                                        <tr>
                                            <td class="hidden">
                                                <mat-icon>perm_identity</mat-icon><span i18n>Họ và tên:</span>
                                            </td>
                                            <td i18n-data-label data-label="Họ và tên:">{{dossierDetail[0]?.applicant?.fullname}}
                                            </td>
                                            <td class="hidden">
                                                <mat-icon>payment</mat-icon><span i18n>Số CMND:</span>
                                            </td>
                                            <td i18n-data-label data-label="Số CMND:">
                                                <span *ngIf="dossierDetail[0]?.applicant?.identity?.number != undefined">
                                                    {{dossierDetail[0]?.applicant?.identity?.number}}
                                                </span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td scope="row" class="hidden">
                                                <mat-icon>phone</mat-icon><span i18n>Số điện thoại:</span>
                                            </td>
                                            <td i18n-data-label data-label="Số điện thoại:">
                                                {{dossierDetail[0]?.applicant?.phoneNumber}}
                                            </td>
                                            <td class="hidden">
                                                <mat-icon>event_available</mat-icon><span i18n>Ngày cấp CMND:</span>
                                            </td>
                                            <td i18n-data-label data-label="Ngày cấp CMND:">
                                                {{dossierDetail[0]?.applicant?.identity?.date | date : 'dd/MM/yyyy'}}</td>
                                        </tr>
                                        <tr>
                                            <td scope="row" class="hidden">
                                                <mat-icon>cake</mat-icon><span i18n>Ngày sinh:</span>
                                            </td>
                                            <td i18n-data-label data-label="Ngày sinh:">
                                                <span *ngIf="dossierDetail[0]?.applicant?.birthday != undefined">
                                                    {{dossierDetail[0]?.applicant?.birthday | date : 'dd/MM/yyyy'}}
                                                </span>
                                            </td>
                                            <td class="hidden">
                                                <mat-icon>local_police</mat-icon><span i18n>Nơi cấp CMND:</span>
                                            </td>
                                            <td i18n-data-label data-label="Nơi cấp CMND:">
                                                <span *ngFor="let indentityAgency of dossierDetail[0]?.applicant?.identity?.agency?.name">
                                                    <span *ngIf="indentityAgency.languageId == selectedLangId">
                                                        {{indentityAgency.name}}
                                                    </span>
                                                </span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td scope="row" class="hidden">
                                                <mat-icon>face</mat-icon><span i18n>Giới tính:</span>
                                            </td>
                                            <td i18n-data-label data-label="Giới tính:">
                                                <span *ngIf="dossierDetail[0]?.applicant?.gender == 0" i18n>Nữ</span>
                                                <span *ngIf="dossierDetail[0]?.applicant?.gender == 1" i18n>Nam</span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="hidden">
                                                <mat-icon>emoji_flags</mat-icon><span i18n>Quốc gia:</span>
                                            </td>
                                            <td i18n-data-label data-label="Quốc gia:" *ngIf="dossierDetail[0]?.applicant?.address != undefined">
                                                {{dossierDetail[0]?.applicant?.address?.place?.nation?.name}}
                                            </td>
                                        </tr>
                                        <tr>
                                            <td scope="row" class="hidden">
                                                <mat-icon>account_balance</mat-icon><span i18n>Tên cơ quan/tổ chức:</span>
                                            </td>
                                            <td i18n-data-label data-label="Tên cơ quan/tổ chức:">
                                                <span *ngIf="dossierDetail[0]?.applicant?.agency != undefined">
                                                    {{dossierDetail[0]?.applicant?.agency?.name}}
                                                </span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="hidden">
                                                <mat-icon>receipt_long</mat-icon><span i18n>GCN/ GP:</span>
                                            </td>
                                            <td i18n-data-label data-label="GCN/ GP:"
                                                [innerHTML]="procedureDetail[0]?.legalGrounds">
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="hidden">
                                                <mat-icon>place</mat-icon><span i18n>Đia chỉ:</span>
                                            </td>
                                            <td colspan="3" i18n-data-label data-label="Đia chỉ:">
                                                <span *ngIf="dossierDetail[0]?.applicant?.address != undefined">
                                                    {{dossierDetail[0]?.applicant?.address?.address}}
                                                </span>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table> -->
                            </div>
                        </div>
                    </mat-tab>

                    <mat-tab *ngIf="enableShowCSDLQGVDCBDH" >
                        <ng-template mat-tab-label>
                            <mat-icon class="tabIcon">assignment_ind</mat-icon>
                            <span >Thông tin xác thực với dữ liệu dân cư</span>
                        </ng-template>
                        <div *ngIf="procedureDetail[0]?.isCheckHoseHoldInfo" class="card">
                            <div class="cardTitle" fxLayoutAlign="space-between">
                                <span>Dịch vụ xác thực thông tin hộ gia đình</span>
                                <button mat-button class="cardAction" (click)="updateHouseHold()" *ngIf="!offDossierDetailUpdate && isTheHandler && isTaskComplete(currentTask[0].activitiTask.status) == false">
                                    <mat-icon>edit</mat-icon>
                                    <span i18n>Cập nhật</span>
                                </button>
                            </div>
                            <div class="thoaiisolate" style="padding-top: 2em;">
                            <app-household-info [dataHouseHoldBDH]="houseHoldBDH" [checkId]="1"></app-household-info>
                            <!-- <app-household-info ></app-household-info> -->
                            </div>
                        </div>
                        <div *ngIf="showInfoReplaceID" class="card">
                            <div class="cardTitle" fxLayoutAlign="space-between">
                                <span>Dịch vụ xác thực thay thế thông tin CMND/CCCD</span>
                                <button mat-button class="cardAction" (click)="updateCitizenInfoBDH()" *ngIf="!offDossierDetailUpdate && isTheHandler && isTaskComplete(currentTask[0].activitiTask.status) == false">
                                    <mat-icon>edit</mat-icon>
                                    <span i18n>Cập nhật</span>
                                </button>
                            </div>
                            <div class="thoaiisolate" style="padding-top: 2em;">
                            <app-get-citizen-info-bdh [dataMemberCitizenInfoBDH]="memberCitizenInfoBDH" [checkId]="1"></app-get-citizen-info-bdh>
                            <!-- <app-household-info ></app-household-info> -->
                            </div>
                        </div>
                    </mat-tab>

                    <mat-tab>
                        <ng-template mat-tab-label>
                            <mat-icon class="tabIcon">dns</mat-icon>
                            <span i18n>Thành phần hồ sơ</span>
                        </ng-template>
                        <div class="card" fxFlex='grow'>
                            <div class="cardTitle" fxLayoutAlign="space-between">
                                <span i18n>Thành phần hồ sơ</span>

                                <button mat-button class="cardAction" (click)="authConfirm()" *ngIf="isKTMDigitalAuthEnable">
                                    <mat-icon>check</mat-icon>
                                    <span i18n>Chứng thực điện tử</span>
                                </button>


                                <button mat-button class="cardAction" (click)="updateComposition()" *ngIf="!offDossierDetailUpdate && isTheHandler && isTaskComplete(currentTask[0].activitiTask.status) == false">
                                    <mat-icon>edit</mat-icon>
                                    <span i18n>Cập nhật thành phần hồ sơ</span>
                                </button>
                                <button mat-button class="cardAction" (click)="downloadAllFile()" *ngIf="downloadFileDossier">
                                    <mat-icon>cloud_download</mat-icon>
                                    <span i18n>Tải file đính kèm</span>
                                </button>
                            </div>
                            <div class="cardContent">
                                <div class="procedureForm">
                                    <div *ngIf="this.hideNoAttachmentKHA" style="margin: 5px; margin-top: -15px;">
                                        <mat-checkbox 
                                            (change)="isHideNoAttachment = $event.checked"
                                            [checked]="isHideNoAttachment">
                                            Ẩn/Hiện thành phần hồ sơ không đính kèm
                                        </mat-checkbox>
                                    </div>
                                    <form [formGroup]="prForm">
                                        <!--Thêm loại procedureFormType hiển thị dạng bảng-->
                                        <div *ngIf="procedureFormType && procedureFormType == 1">
                                            <table class="mat-table mat-table-form" style="width: 100%;">
                                                <tr>
                                                    <th class="tablecolor1" i18n>STT</th>
                                                    <th class="tablecolor1" i18n="@@procedureName">Tên giấy tờ</th>
                                                    <th class="tablecolor1" i18n="@@procedureQuantity">Số lượng bản</th>
                                                    <th class="tablecolor1" i18n="@@procedureAttachFile">Đính kèm giấy tờ</th>
                                                </tr>
                                                <tr *ngFor="let form of procedureForm"  class="item" [ngClass]="{'hidden-form': shouldHideForm(form?.detail)}">
                                                    <td class="center">
                                                        <div class="head">{{form.stt}}</div>
                                                    </td>
                                                    <td>
                                                        <div class="head">
                                                            <mat-icon class="requirement">check_circle_outline</mat-icon>
                                                            <span>
                                                                {{ form.form.name }}
                                                                <span *ngIf="form.form.name == undefined || form.form.name == null || form.form.name.trim() == ''" i18n>(Không tìm thấy bản dịch)</span>
                                                            </span>
                                                          </div>
                                                    </td>
                                                    <td>
                                                        <div class="body">
                                                            <mat-radio-group formControlName="rdo_File">
                                                                <div *ngFor="let fd of form.detail; let j = index">
                                                                    <div *ngIf="fd.file.length > 0 || form.directCheck" class="rdo_File" fxLayout="row" fxLayout.xs="row" fxLayout.sm="row" fxLayoutAlign="space-between">
                                                                        <div class="typeName">
                                                                            {{ fd.quantity }} {{ fd.type.name }}
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </mat-radio-group>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <div class="body">
                                                            <mat-radio-group formControlName="rdo_File">
                                                                <div *ngFor="let fd of form.detail; let j = index">
                                                                    <div *ngIf="fd.file.length > 0 || form.directCheck" class="rdo_File" fxLayout="row" fxLayout.xs="row" fxLayout.sm="row" fxLayoutAlign="space-between">
                                                                        <div class="listUploadedFile" fxFlex.gt-xs="grow" fxFlex='grow'>
                                                                            <div class="wrapList" fxLayout.xs="row" fxLayout.sm="row" fxLayout="row" fxLayoutAlign="space-evenly">
                                                                                <div class="file" fxFlex='grow' *ngFor="let detailFile of fd.file; let f = index;">
                                                                                    <!-- <div class="icon" [ngStyle]="{'background-image': 'url(' + config.cloudStaticURL + 'icon/files/512x512/docx.png)'}"> -->
                                                                                    <view-sign-history *ngIf="enablePopupSignature && signedFiles?.has(detailFile?.id)" style="align-content: center" [fileId]="detailFile?.id"><mat-icon style="color: green !important; align-self: center; margin-left: .5em;cursor: pointer">check_circle</mat-icon></view-sign-history>
                                                                                    <div class="icon" [ngStyle]="{'background-image': 'url('+ getFileIcon(detailFile?.filename?.split('.').pop()) +')'}">
                                                                                    </div>
                                                                                    <div class="name">
                                                                                        {{detailFile.filename ? detailFile.filename: detailFile.name}}
                                                                                    </div>
                                                                                    <!-- <button mat-icon-button class="viewFile" (click)="viewFile(detailFile.id)">
                                                                                        <mat-icon>launch</mat-icon>
                                                                                    </button> -->
                                                                                    <div >
                                                                                        <a mat-icon-button class="moreBtn" [matMenuTriggerFor]="digitallySignedFileMenu">
                                                                                            <mat-icon>more_vert</mat-icon>
                                                                                        </a>
                                                                                        <mat-menu #digitallySignedFileMenu="matMenu">
                                                                                            <a mat-menu-item class="menuAction" target="_blank" [routerLink]="routerLink(detailFile)">
                                                                                                <mat-icon>format_size</mat-icon>
                                                                                                <span i18n>Xem trước</span>
                                                                                            </a>
                                                                                            <button mat-menu-item class="menuAction" (click)="downloadFile(detailFile.id, detailFile.filename)">
                                                                                                <mat-icon>cloud_download</mat-icon>
                                                                                                <span i18n>Tải xuống tệp tin</span>
                                                                                            </button>
                                                                                            <!-- <button mat-menu-item class="menuAction" (click)="verifiedSimHSM(detailFile, 0)" *ngIf="checkPermission && digitalSignature.VNPTSim">
                                                                                                <mat-icon>verified</mat-icon>
                                                                                                <span i18n>Ký số sim</span>
                                                                                            </button> -->
                                                                                            <button mat-menu-item class="menuAction" (click)="openPdfDigitalSignatureV2(form.form.id, fd.type.id, detailFile, 3, 1)" *ngIf="isTheHandler && isTaskComplete(currentTask[0].activitiTask.status) == false && digitalSignature.VNPTSim">
                                                                                                <mat-icon>verified</mat-icon>
                                                                                                <span i18n>Ký số sim</span>
                                                                                            </button>
                                                                                            <button mat-menu-item class="menuAction" (click)="openPdfDigitalSignature(3, '',detailFile.id, detailFile.filename,'', form.form.id, fd.type.id)" *ngIf="digitalSignature.SmartCA && checkIfFileIsSupported(detailFile.filename)">
                                                                                                <mat-icon>verified</mat-icon>
                                                                                                <span>Ký số Smart CA</span>
                                                                                            </button>
                                                                                            <button mat-menu-item class="menuAction" (click)="openVGCAplugin(3, '',detailFile.id, detailFile.filename,'', detailFile.size, form.form.id, fd.type.id)" *ngIf="digitalSignature.VGCA && checkIfFileIsSupported(detailFile.filename)">
                                                                                                <mat-icon>verified</mat-icon>
                                                                                                <span *ngIf="!isEditSignTokenName; then thenBlock else elseBlock"></span>
                                                                                                <ng-template #thenBlock>Ký số Ban Cơ yếu</ng-template>
                                                                                                <ng-template #elseBlock>Ký số Token</ng-template>
                                                                                            </button>
                                                                                            <button mat-menu-item class="menuAction" (click)="openVGCApluginIssued(3, '',detailFile.id, detailFile.filename,'', detailFile.size, form.form.id, fd.type.id)" *ngIf="!!allowVGCASignIssue">
                                                                                                <mat-icon>verified</mat-icon>
                                                                                                <span>Ký số văn thư Ban cơ yếu</span>
                                                                                            </button>
                                                                                            <button mat-menu-item class="menuAction" (click)="openVGCApluginIssued(3, '',detailFile.id, detailFile.filename,'', detailFile.size, form.form.id, fd.type.id)" *ngIf="!!isVgcaSignCopy">
                                                                                                <mat-icon>verified</mat-icon>
                                                                                                <span>{{vgcaSignLabel}}</span>
                                                                                            </button>
                                                                                            <button mat-menu-item class="menuAction" (click)="openVGCApluginIssued(3, '',detailFile.id, detailFile.filename,'', detailFile.size, form.form.id, fd.type.id)" *ngIf="digitalSignature.VGCA && checkIfFileIsSupported(detailFile.filename) && checkSignTypeOrg()">
                                                                                                <mat-icon>verified</mat-icon>
                                                                                                <span>Ký số loại mẫu chữ ký tổ chức</span>
                                                                                            </button>
                                                                                            <button mat-menu-item class="menuAction" (click)="openVnptCaPlugin(form.form.id, fd.type.id, detailFile, 3)" *ngIf="digitalSignature.VNPTCA && (checkIfFileIsSupported(detailFile.filename) || checkIfDocFileOnly(detailFile.filename))">
                                                                                                <mat-icon>verified</mat-icon>
                                                                                                <span>Ký số VNPT-CA</span>
                                                                                            </button>

                                                                                            <button mat-menu-item class="menuAction" (click)="openNEAC(form.form.id, fd.type.id, detailFile, 3, 5)" *ngIf="digitalSignature.NEAC && checkIfFileIsSupported(detailFile.filename)">
                                                                                                <mat-icon class="mainColor">verified</mat-icon>
                                                                                                <span i18n="@@digitalSignNEAC">Ký số NEAC</span>
                                                                                            </button>
                                                                                            <button mat-menu-item class="menuAction" (click)="openPdfDigitalSignatureV2(form.form.id, fd.type.id, detailFile, 3, 6)" *ngIf="digitalSignature.QNM && checkIfFileIsSupported(detailFile.filename)">
                                                                                                <mat-icon class="mainColor">verified</mat-icon>
                                                                                                <span i18n="@@QNMDigitalSignature">Ký số tập trung QNM</span>
                                                                                            </button>
<!--                                                                                            <button mat-menu-item class="menuAction" (click)="openHistory(detailFile.id)">-->
<!--                                                                                                <mat-icon>refresh</mat-icon>-->
<!--                                                                                                <span i18n="@@history">Xem lịch sử ký số</span>-->
<!--                                                                                            </button>-->
                                                                                          <button mat-menu-item class="menuAction">
                                                                                            <view-sign-history [fileId]="detailFile.id">
                                                                                              <mat-icon>refresh</mat-icon>
                                                                                              <span>Xem lịch sử ký số</span>
                                                                                            </view-sign-history>
                                                                                          </button>
                                                                                            <button mat-menu-item class="menuAction" (click)="transferDetailToResult(detailFile, 'fileResultDone')" *ngIf="canTransferToResult">
                                                                                                <mat-icon class="material-icons-outlined">shortcut</mat-icon>
                                                                                                <span i18n="@@changeToResult">Chuyển thành file kết quả xử lý</span>
                                                                                            </button>
                                                                                        </mat-menu>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </mat-radio-group>
                                                        </div>
                                                    </td>
                                                </tr>
                                            </table>
                                        </div>
                                        <!--end-->
                                        <div *ngIf="!procedureFormType || procedureFormType == 0">
                                            <div class="item" *ngFor="let form of procedureForm" [ngClass]="{'disabledHide': hiddenProcedureForm(selectedProcess?.id, form.procedureProcessDefinition?.id), 'hidden-form': shouldHideForm(form?.detail)}">
                                                <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="column">
                                                    <div class="head" fxFlex='grow'>
                                                        <mat-icon class="requirement">check_circle_outline</mat-icon>
                                                        <span>
                                                            {{ form.form.name }}
                                                            <span *ngIf="form.form.name == undefined || form.form.name == null || form.form.name.trim() == ''" i18n>(Không tìm thấy bản dịch)</span>
                                                        </span>
                                                    </div>
                                                </div>
                                                <div class="body">
                                                    <mat-radio-group formControlName="rdo_File">
                                                        <div *ngFor="let fd of form.detail; let j = index">
                                                            <div *ngIf="fd.file.length > 0" class="rdo_File" fxLayout="row" fxLayout.xs="row" fxLayout.sm="row" fxLayoutAlign="space-between">
                                                                <div class="typeName" fxFlex.gt-sm='28' fxFlex="65">
                                                                    {{ fd.quantity }} {{ fd.type.name }}
                                                                </div>

                                                                <mat-form-field appearance="fill" class="selectFileTemplate" fxFlex.gt-sm='20' fxFlex="93" [style.z-index]="form.file != null && form.file.length > 0 ? 1 : -1">
                                                                    <mat-select placeholder="Xem mẫu đơn, tờ khai">
                                                                        <mat-option *ngFor="let file of form.file" value="{{ file.id }}" (click)="downloadFile(file.id, file.filename)">
                                                                            {{ file.filename }}
                                                                        </mat-option>
                                                                    </mat-select>
                                                                </mat-form-field>

                                                                <div fxFlex="2"></div>

                                                                <div class="listUploadedFile" fxFlex.gt-sm='50' fxFlex.gt-xs="grow" fxFlex='grow'>
                                                                    <div class="wrapList" fxLayout.xs="row" fxLayout.sm="row" fxLayout="row" fxLayoutAlign="space-evenly">
                                                                        <div class="file" fxFlex.gt-sm="49" fxFlex.gt-xs="49" fxFlex='grow' *ngFor="let detailFile of fd.file; let f = index;">
                                                                            <!-- <div class="icon" [ngStyle]="{'background-image': 'url(' + config.cloudStaticURL + 'icon/files/512x512/docx.png)'}"> -->
                                                                                 <view-sign-history *ngIf="enablePopupSignature && signedFiles?.has(detailFile?.id)" style="align-content: center" [fileId]="detailFile?.id"><mat-icon style="color: green !important; align-self: center; margin-left: .5em;cursor: pointer">check_circle</mat-icon></view-sign-history>
                                                                            <div class="icon" [ngStyle]="{'background-image': 'url('+ getFileIcon(detailFile?.filename?.split('.').pop()) +')'}">
                                                                            </div>
                                                                            <div class="name">
                                                                                {{detailFile.filename ? detailFile.filename: detailFile.name}}
                                                                            </div>
                                                                            <button mat-icon-button class="viewFile" (click)="viewFile(detailFile.id)">
                                                                                <mat-icon>launch</mat-icon>
                                                                            </button>
                                                                            <div style="background-color: #FFF; display:flex">
                                                                                <a *ngIf="digitalSignatureKGGMenu" mat-icon-button class="signFileBtn" [matMenuTriggerFor]="digitallySignedFileKGGMenu">
                                                                                    <mat-icon>edit</mat-icon>
                                                                                    <span>Ký số</span>
                                                                                </a>
                                                                                <a mat-icon-button class="moreBtn" [matMenuTriggerFor]="digitallySignedFileMenu">
                                                                                    <mat-icon>more_vert</mat-icon>
                                                                                </a>
                                                                                <mat-menu #digitallySignedFileKGGMenu="matMenu">
                                                                                    <ng-container
                                                                                        [ngTemplateOutlet]="signItemsMenu"
                                                                                        [ngTemplateOutletContext]="{ form: form, fd: fd, detailFile: detailFile}"
                                                                                    >
                                                                                    </ng-container>
                                                                                </mat-menu>
                                                                                <mat-menu #digitallySignedFileMenu="matMenu">
                                                                                    <a mat-menu-item class="menuAction" target="_blank" [routerLink]="routerLink(detailFile)">
                                                                                        <mat-icon>format_size</mat-icon>
                                                                                        <span i18n>Xem trước</span>
                                                                                    </a>
                                                                                    <button mat-menu-item class="menuAction" (click)="downloadFile(detailFile.id, detailFile.filename)">
                                                                                        <mat-icon>cloud_download</mat-icon>
                                                                                        <span i18n>Tải xuống tệp tin</span>
                                                                                    </button>
                                                                                    <ng-container
                                                                                        [ngTemplateOutlet]="signItemsMenu"
                                                                                        [ngTemplateOutletContext]="{ form: form, fd: fd, detailFile: detailFile}"
                                                                                    >
                                                                                    </ng-container>
                                                                                    <!-- <button mat-menu-item class="menuAction" (click)="verifiedSimHSM(detailFile, 0)" *ngIf="checkPermission && digitalSignature.VNPTSim">
                                                                                        <mat-icon>verified</mat-icon>
                                                                                        <span i18n>Ký số sim</span>
                                                                                    </button> -->
                                                                                    <ng-template #signItemsMenu let-form="form" let-fd="fd" let-detailFile="detailFile">
                                                                                        <button mat-menu-item class="menuAction" (click)="openPdfDigitalSignatureV2(form.form.id, fd.type.id, detailFile, 3, 1)" *ngIf="isTheHandler && isTaskComplete(currentTask[0].activitiTask.status) == false && digitalSignature.VNPTSim">
                                                                                            <mat-icon>verified</mat-icon>
                                                                                            <span i18n>Ký số sim</span>
                                                                                        </button>
                                                                                        <button mat-menu-item class="menuAction" (click)="openPdfDigitalSignature(3, '',detailFile.id, detailFile.filename,'', form.form.id, fd.type.id)" *ngIf="digitalSignature.SmartCA && checkIfFileIsSupported(detailFile.filename)">
                                                                                            <mat-icon>verified</mat-icon>
                                                                                            <span>Ký số Smart CA</span>
                                                                                        </button>
                                                                                        <button mat-menu-item class="menuAction" (click)="openVGCAplugin(3, '',detailFile.id, detailFile.filename,'', detailFile.size, form.form.id, fd.type.id)" *ngIf="digitalSignature.VGCA && checkIfFileIsSupported(detailFile.filename)">
                                                                                            <mat-icon>verified</mat-icon>
                                                                                            <span *ngIf="!isEditSignTokenName; then thenBlock else elseBlock"></span>
                                                                                            <ng-template #thenBlock>Ký số Ban Cơ yếu</ng-template>
                                                                                            <ng-template #elseBlock>Ký số Token</ng-template>
                                                                                        </button>
                                                                                        <button mat-menu-item class="menuAction" (click)="openVGCApluginIssued(3, '',detailFile.id, detailFile.filename,'', detailFile.size, form.form.id, fd.type.id)" *ngIf="!!allowVGCASignIssue">
                                                                                            <mat-icon>verified</mat-icon>
                                                                                            <span>Ký số văn thư Ban cơ yếu</span>
                                                                                        </button>
                                                                                        <button mat-menu-item class="menuAction" (click)="openVGCApluginCopy(3, '',detailFile.id, detailFile.filename,'', detailFile.size, form.form.id, fd.type.id)" *ngIf="!!isVgcaSignCopy">
                                                                                            <mat-icon>verified</mat-icon>
                                                                                            <span>{{vgcaSignLabel}}</span>
                                                                                        </button>
                                                                                        <button mat-menu-item class="menuAction" (click)="openVGCApluginIssued(3, '',detailFile.id, detailFile.filename,'', detailFile.size, form.form.id, fd.type.id)" *ngIf="digitalSignature.VGCA && checkIfFileIsSupported(detailFile.filename) && checkSignTypeOrg()">
                                                                                            <mat-icon>verified</mat-icon>
                                                                                            <span>Ký số loại mẫu chữ ký tổ chức</span>
                                                                                        </button>
                                                                                        <button mat-menu-item class="menuAction" (click)="openVnptCaPlugin(form.form.id, fd.type.id, detailFile, 3)" *ngIf="digitalSignature.VNPTCA && (checkIfFileIsSupported(detailFile.filename) || checkIfDocFileOnly(detailFile.filename))">
                                                                                            <mat-icon>verified</mat-icon>
                                                                                            <span>Ký số VNPT-CA</span>
                                                                                        </button>
                                                                                        <button mat-menu-item class="menuAction" (click)="openNEAC(form.form.id, fd.type.id, detailFile, 5)" *ngIf="digitalSignature.NEAC && checkIfFileIsSupported(detailFile.filename)">
                                                                                            <mat-icon class="mainColor">verified</mat-icon>
                                                                                            <span i18n="@@digitalSignNEAC">Ký số NEAC</span>
                                                                                        </button>
                                                                                        <button mat-menu-item class="menuAction" (click)="openPdfDigitalSignatureV2(form.form.id, fd.type.id, detailFile, 5, 6)" *ngIf="digitalSignature.QNM && checkIfFileIsSupported(detailFile.filename)">
                                                                                            <mat-icon class="mainColor">verified</mat-icon>
                                                                                            <span i18n="@@QNMDigitalSignature">Ký số tập trung QNM</span>
                                                                                        </button>
    <!--                                                                                    <button mat-menu-item class="menuAction" (click)="openHistory(detailFile.id)">-->
    <!--                                                                                        <mat-icon>refresh</mat-icon>-->
    <!--                                                                                        <span i18n="@@history">Xem lịch sử ký số</span>-->
    <!--                                                                                    </button>-->
                                                                                      <button mat-menu-item class="menuAction">
                                                                                        <view-sign-history [fileId]="detailFile.id">
                                                                                          <mat-icon>refresh</mat-icon>
                                                                                          <span>Xem lịch sử ký số</span>
                                                                                        </view-sign-history>
                                                                                      </button>
                                                                                    </ng-template>
                                                                                    <button mat-menu-item class="menuAction" (click)="transferDetailToResult(detailFile, 'fileResultDone')" *ngIf="canTransferToResult">
                                                                                        <mat-icon class="material-icons-outlined">shortcut</mat-icon>
                                                                                        <span i18n="@@changeToResult">Chuyển thành file kết quả xử lý</span>
                                                                                    </button>
                                                                                </mat-menu>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </mat-radio-group>
                                                </div>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </mat-tab>

                    <!-- Fee -->
                    <mat-tab *ngIf="!hiddenDossierFeeTab">
                        <ng-template mat-tab-label>
                            <mat-icon class="tabIcon">attach_money</mat-icon>
                            <span i18n>Lệ phí</span>
                        </ng-template>
                        <div class="card" fxFlex='grow'>
                            <div class="cardTitle" fxLayoutAlign="space-between">
                                <span i18n>Lệ phí</span>
                                <button *ngIf="enableAdditionalFeeDossier && (enableProcessingPaidDossiers == true && checkPayRequest == false || enableProcessingPaidDossiers == false)" mat-button class="cardAction" (click)="addFee()">
                                    <mat-icon>add</mat-icon>
                                    <span >Thêm phí/ lệ phí</span>
                                </button>
                                <button *ngIf="isShowAddFee" mat-button class="cardAction" (click)="addFeeQnm()">
                                    <mat-icon>add</mat-icon>
                                    <span >Thêm phí/ lệ phí</span>
                                </button>
                                <button mat-button *ngIf="enableProcessingPaidDossiers == true && checkPayRequest == false || enableProcessingPaidDossiers == false" class="cardAction" (click)="updateFee()">
                                    <mat-icon>edit</mat-icon>
                                    <span i18n>Cập nhật phí/ lệ phí</span>
                                </button>
                            </div>
                            <div class="cardContent">
                                <div *ngIf="(enableRemoveVnpostFeeToAnotherTable == 1 && dossierReceivingKind_VNP) || dossierDetail[0]?.dossierReceivingKind?.id == receiveResultsAdministrativeCenterId || dossierDetail[0]?.transferPaperDocuments?.id == transferPaperDocumentsPostalId">
                                    <div class="mt-2 fee_head" *ngIf="dossierDetail[0]?.dossierReceivingKind?.id == receiveResultsAdministrativeCenterId">
                                      <div class="s_head mb-1">
                                        <span >Thông tin phí chuyển hồ sơ</span>
                                      </div>
                                      <!-- <div class="s_head mb-1">
                                          <div class='payment-status-vnpost' i18n="@@vnPostDescriptionTitleH2">(Nhân viên bưu chính sẽ thu phí trực tiếp tại nhà khi thu hồ sơ/trả kết quả)</div>
                                      </div> -->
                                    </div>
                                    <div class="mt-2 fee_head" *ngIf="dossierDetail[0]?.dossierReceivingKind?.id !== receiveResultsAdministrativeCenterId">
                                        <div class="s_head mb-1">
                                          <span i18n="@@vnPostDescriptionTitleH1">Thông tin phí thu hồ sơ/trả kết quả tại nhà</span>
                                        </div>
                                        <div class="s_head mb-1">
                                            <div class='payment-status-vnpost' i18n="@@vnPostDescriptionTitleH2">(Nhân viên bưu chính sẽ thu phí trực tiếp tại nhà khi thu hồ sơ/trả kết quả)</div>
                                        </div>
                                      </div>
                                    <div class="procedureFee" fxLayout="row" fxLayout.xs="column" fxLayout.sm="column" style="margin-bottom: 10px;">
                                        <mat-table [dataSource]="feeDataSource_VNP" fxFlex='grow' class="tblFee" *ngIf="!notShowNoFee || (!hideTotal && notShowNoFee)">
                                            <ng-container matColumnDef="procostType">
                                                <mat-header-cell *matHeaderCellDef i18n>Loại lệ phí</mat-header-cell>
                                                <mat-cell *matCellDef="let row" i18n-data-label data-label="Loại lệ phí">
                                                    {{row.typeName}}
                                                    <span *ngIf="row.typeName == undefined || row.typeName == null || row.typeName.trim() == ''" i18n>(Không tìm thấy bản dịch)</span>
                                                </mat-cell>
                                                <mat-footer-cell *matFooterCellDef i18n>Tổng</mat-footer-cell>
                                            </ng-container>

                                            <ng-container matColumnDef="quantity">
                                                <mat-header-cell *matHeaderCellDef i18n>Số lượng</mat-header-cell>
                                                <mat-cell *matCellDef="let row" i18n-data-label data-label="Số lượng">
                                                    {{row.quantity}}
                                                </mat-cell>
                                                <mat-footer-cell *matFooterCellDef></mat-footer-cell>
                                            </ng-container>

                                            <ng-container matColumnDef="cost">
                                                <mat-header-cell *matHeaderCellDef i18n>Mức lệ phí</mat-header-cell>
                                                <mat-cell *matCellDef="let row" i18n-data-label data-label="Mức lệ phí">
                                                    {{row.cost | number}} {{row.monetaryUnit}} </mat-cell>
                                                <mat-footer-cell *matFooterCellDef></mat-footer-cell>
                                            </ng-container>

                                            <ng-container matColumnDef="amount">
                                                <mat-header-cell *matHeaderCellDef i18n>Thành tiền</mat-header-cell>
                                                <mat-cell *matCellDef="let row" i18n-data-label data-label="Thành tiền">
                                                    {{row.quantity*row.cost | number }} {{row.monetaryUnit}} </mat-cell>
                                                <mat-footer-cell *matFooterCellDef class="totalCell">{{totalCost_VNP}}
                                                </mat-footer-cell>
                                            </ng-container>

                                            <ng-container matColumnDef="description" *ngIf="dossierDetail[0]?.dossierReceivingKind?.id !== receiveResultsAdministrativeCenterId">
                                                <mat-header-cell *matHeaderCellDef i18n>Mô tả</mat-header-cell>
                                                <mat-cell *matCellDef="let row" i18n-data-label data-label="Mô tả">
                                                    {{row.description}}
                                                </mat-cell>
                                                <mat-footer-cell *matFooterCellDef class="payment-status-vnpost" i18n="@@vnPostDescriptionTotal">Nhân viên bưu chính sẽ thu phí trực tiếp tại nhà khi thu hồ sơ/trả kết quả</mat-footer-cell>
                                            </ng-container>

                                            <ng-container matColumnDef="description" *ngIf="dossierDetail[0]?.dossierReceivingKind?.id == receiveResultsAdministrativeCenterId">
                                                <mat-header-cell *matHeaderCellDef i18n>Mô tả</mat-header-cell>
                                                <mat-cell *matCellDef="let row" i18n-data-label data-label="Mô tả">
                                                    {{row.description}}
                                                </mat-cell>
                                                <mat-footer-cell *matFooterCellDef></mat-footer-cell>
                                            </ng-container>

                                            <ng-container matColumnDef="pay">
                                                <mat-header-cell *matHeaderCellDef i18n>Thanh toán</mat-header-cell>
                                                <mat-cell *matCellDef="let row" data-label="Thanh toán">
                                                    <span *ngIf="row.paid != row.quantity*row.cost" i18n>Chưa thanh toán</span>
                                                    <span *ngIf="row.paid == row.quantity*row.cost" i18n>Đã thanh toán</span>
                                                </mat-cell>
                                                <mat-footer-cell *matFooterCellDef class="totalCell">
                                                    {{totalCost_VNP}}
                                                    <span class="rest">(<span i18n>Còn lại</span> {{totalRemaining_VNP}})</span>
                                                </mat-footer-cell>
                                            </ng-container>

                                            <ng-container matColumnDef="required">
                                                <mat-header-cell *matHeaderCellDef>Bắt buộc thanh toán</mat-header-cell>
                                                <mat-cell *matCellDef="let row" i18n-data-label data-label="Mô tả" req>
                                                    <mat-icon *ngIf="row.required == 1">checked</mat-icon>
                                                </mat-cell>
                                                <mat-footer-cell *matFooterCellDef></mat-footer-cell>
                                            </ng-container>

                                            <ng-container matColumnDef="typePay">
                                                <mat-header-cell *matHeaderCellDef>Hình thức thanh toán</mat-header-cell>
                                                <mat-cell *matCellDef="let row" i18n-data-label data-label="Mô tả" req>
                                                    {{row.typePay}}
                                                </mat-cell>
                                                <mat-footer-cell *matFooterCellDef></mat-footer-cell>
                                            </ng-container>

                                            <mat-header-row *matHeaderRowDef="feeDisplayedColumns"></mat-header-row>
                                            <mat-row *matRowDef="let row; columns: feeDisplayedColumns;"></mat-row>
                                            <mat-footer-row *matFooterRowDef="feeDisplayedColumns" [ngClass]="{'hidden': hideTotal == true}">
                                            </mat-footer-row>
                                        </mat-table>
                                    </div>
                                </div>
                                <div *ngIf="viettelPostEnable && dossierDetail[0]?.dossierReceivingKind?.id == viettelPostReceiveResultsByAddress">
                                    <div class="s_head mb-1">
                                        <span i18n="@@vnPostDescriptionTitleH1">Thông tin phí thu hồ sơ/trả kết quả tại nhà</span><br fxShow="true" fxHide.gt-sm>
                                    </div>
                                    <div class="s_head mb-1">
                                        <div class='payment-status-vnpost' i18n="@@vnPostDescriptionTitleH2">(Nhân viên bưu chính sẽ thu phí trực tiếp tại nhà khi thu hồ sơ/trả kết quả)</div>
                                    </div>
                                    <br fxShow="true" fxHide.gt-sm>
                                    <div *ngIf="dossierDetail[0]?.viettelPost?.vpSend" class="feeZero mb-1">
                                        <span>Phí nộp hồ sơ tại nhà: </span> {{dossierDetail[0]?.viettelPost?.feeViettelPostS}} (VNĐ)
                                    </div>
                                    <div *ngIf="dossierDetail[0]?.viettelPost?.vpReceive" class="feeZero mb-1">
                                        <span>Phí nhận kết quả hồ sơ tại nhà: </span> {{dossierDetail[0]?.viettelPost?.feeViettelPostR}} (VNĐ)
                                    </div>
                                    <div *ngIf="!dossierDetail[0]?.viettelPost?.vpSend && !dossierDetail[0]?.viettelPost?.vpReceive" class="feeZero mb-1">
                                        <span>Không có thông tin phí, lệ phí</span>
                                    </div>
                                </div>
                                <div *ngIf="!enableFeeVat; else bdhFeeVatTemplate"> <!-- OS_BDH: hiển thị bảng có cột VAT  -->
                                    <div class="mt-2 fee_head" *ngIf="enableRemoveVnpostFeeToAnotherTable == 1 && dossierReceivingKind_VNP">
                                        <div class="s_head mb-1">
                                          <span>Thông tin phí, lệ phí</span>
                                        </div>
                                        <div class="s_head mb-1">
                                            <div class='payment-status-vnpost' i18n="@@dossierFeeDescriptionTitle">(Hiện tại hệ thống chỉ áp dụng thu phí, lệ phí hồ sơ. *Lưu ý: Đối với phí dịch vụ bưu chính (nếu có) người dân vui lòng thanh toán trực tiếp cho nhân viên bưu chính khi nhận kết quả tại nhà)</div>
                                        </div>
                                    </div>
                                    <div class="procedureFee" fxLayout="row" fxLayout.xs="column" fxLayout.sm="column">
                                        <mat-table [dataSource]="feeDataSource" fxFlex='grow' class="tblFee"  *ngIf="(this.hideCurrencyConverter == 1  && this.convertedCurrency == true) && (!notShowNoFee || (!hideTotal && notShowNoFee))">
                                            <ng-container matColumnDef="procostType">
                                                <mat-header-cell *matHeaderCellDef i18n>Loại lệ phí</mat-header-cell>
                                                <mat-cell *matCellDef="let row" i18n-data-label data-label="Loại lệ phí">
                                                    {{row.typeName}}
                                                    <span *ngIf="row.typeName == undefined || row.typeName == null || row.typeName.trim() == ''" i18n>(Không tìm thấy bản dịch)</span>
                                                </mat-cell>
                                                <mat-footer-cell *matFooterCellDef i18n>Tổng</mat-footer-cell>
                                            </ng-container>

                                            <ng-container matColumnDef="quantity">
                                                <mat-header-cell *matHeaderCellDef i18n>Số lượng</mat-header-cell>
                                                <mat-cell *matCellDef="let row" i18n-data-label data-label="Số lượng">
                                                    {{row.quantity}}
                                                </mat-cell>
                                                <mat-footer-cell *matFooterCellDef></mat-footer-cell>
                                            </ng-container>

                                            <ng-container matColumnDef="cost">
                                                <mat-header-cell *matHeaderCellDef i18n>Mức lệ phí</mat-header-cell>
                                                <mat-cell *matCellDef="let row" i18n-data-label data-label="Mức lệ phí">
                                                    {{row.cost | number}} {{row.monetaryUnit}} </mat-cell>
                                                <mat-footer-cell *matFooterCellDef></mat-footer-cell>
                                            </ng-container>
                                            <ng-container matColumnDef="exrate" *ngIf="this.showExchangeRate == 1">
                                                <mat-header-cell *matHeaderCellDef>Tỷ giá (VND)</mat-header-cell>
                                                <mat-cell *matCellDef="let row">{{row.currencyRate | number}}</mat-cell>
                                                <mat-footer-cell *matFooterCellDef></mat-footer-cell>
                                              </ng-container>
                                              <ng-container matColumnDef="changedamount">
                                                <mat-header-cell *matHeaderCellDef>Quy đổi thành (VND)</mat-header-cell>
                                                <mat-cell *matCellDef="let row">{{row.quantity*row.cost * row.currencyRate| number: '1.0-0'}}</mat-cell>
                                                <mat-footer-cell *matFooterCellDef></mat-footer-cell>
                                              </ng-container>

                                            <ng-container matColumnDef="amount">
                                                <mat-header-cell *matHeaderCellDef i18n>Thành tiền</mat-header-cell>
                                                <mat-cell *matCellDef="let row" i18n-data-label data-label="Thành tiền">
                                                    {{row.quantity*row.cost * row.currencyRate| number: '1.0-0' }} &nbsp; VNĐ </mat-cell>
                                                <mat-footer-cell *matFooterCellDef class="totalCell">{{totalCost}}
                                                </mat-footer-cell>
                                            </ng-container>

                                            <ng-container matColumnDef="description">
                                                <mat-header-cell *matHeaderCellDef i18n>Mô tả</mat-header-cell>
                                                <mat-cell *matCellDef="let row" i18n-data-label data-label="Mô tả">
                                                    {{row.description}}
                                                </mat-cell>
                                                <mat-footer-cell *matFooterCellDef></mat-footer-cell>
                                            </ng-container>

                                            <ng-container matColumnDef="pay">
                                                <mat-header-cell *matHeaderCellDef i18n>Thanh toán</mat-header-cell>
                                                <mat-cell *matCellDef="let row" data-label="Thanh toán">
                                                    <span *ngIf="row.paid != row.eachTotalCost" i18n>Chưa thanh toán</span>
                                                    <span *ngIf="row.paid == row.eachTotalCost" i18n>Đã thanh toán</span>
                                                </mat-cell>
                                                <mat-footer-cell *matFooterCellDef class="totalCell">
                                                    {{totalPaided}}
                                                    <span class="rest">(<span i18n>Còn lại</span> {{totalRemaining}})</span>
                                                </mat-footer-cell>
                                            </ng-container>

                                            <ng-container matColumnDef="required">
                                                <mat-header-cell *matHeaderCellDef>Bắt buộc thanh toán</mat-header-cell>
                                                <mat-cell *matCellDef="let row" i18n-data-label data-label="Mô tả" req>
                                                    <mat-icon *ngIf="row.required == 1">checked</mat-icon>
                                                </mat-cell>
                                                <mat-footer-cell *matFooterCellDef></mat-footer-cell>
                                            </ng-container>

                                            <ng-container matColumnDef="typePay">
                                                <mat-header-cell *matHeaderCellDef>Hình thức thanh toán</mat-header-cell>
                                                <mat-cell *matCellDef="let row" i18n-data-label data-label="Mô tả" req>
                                                    {{row.typePay}}
                                                </mat-cell>
                                                <mat-footer-cell *matFooterCellDef></mat-footer-cell>
                                            </ng-container>

                                            <mat-header-row *matHeaderRowDef="ExfeeDisplayedColumns"></mat-header-row>
                                            <mat-row *matRowDef="let row; columns: ExfeeDisplayedColumns;"></mat-row>
                                            <mat-footer-row *matFooterRowDef="ExfeeDisplayedColumns" [ngClass]="{'hidden': hideTotal == true}">
                                            </mat-footer-row>
                                        </mat-table>
                                        <mat-table [dataSource]="feeDataSource" fxFlex='grow' class="tblFee" *ngIf="(this.hideCurrencyConverter == 0 || (this.hideCurrencyConverter == 1  && this.convertedCurrency == false)) && (!notShowNoFee || (!hideTotal && notShowNoFee))">
                                            <ng-container matColumnDef="procostType">
                                                <mat-header-cell *matHeaderCellDef i18n>Loại lệ phí</mat-header-cell>
                                                <mat-cell *matCellDef="let row" i18n-data-label data-label="Loại lệ phí">
                                                    {{row.typeName}}
                                                    <span *ngIf="row.typeName == undefined || row.typeName == null || row.typeName.trim() == ''" i18n>(Không tìm thấy bản dịch)</span>
                                                </mat-cell>
                                                <mat-footer-cell *matFooterCellDef i18n>Tổng</mat-footer-cell>
                                            </ng-container>

                                            <ng-container matColumnDef="quantity">
                                                <mat-header-cell *matHeaderCellDef i18n>Số lượng</mat-header-cell>
                                                <mat-cell *matCellDef="let row" i18n-data-label data-label="Số lượng">
                                                    {{row.quantity}}
                                                </mat-cell>
                                                <mat-footer-cell *matFooterCellDef></mat-footer-cell>
                                            </ng-container>

                                            <ng-container matColumnDef="cost">
                                                <mat-header-cell *matHeaderCellDef i18n>Mức lệ phí</mat-header-cell>
                                                <mat-cell *matCellDef="let row" i18n-data-label data-label="Mức lệ phí">
                                                    {{row.cost | number}} {{row.monetaryUnit}} </mat-cell>
                                                <mat-footer-cell *matFooterCellDef></mat-footer-cell>
                                            </ng-container>
                                            
                                            <ng-container matColumnDef="amount">
                                                <mat-header-cell *matHeaderCellDef i18n>Thành tiền</mat-header-cell>
                                                <mat-cell *matCellDef="let row" i18n-data-label data-label="Thành tiền">
                                                    {{row.quantity*row.cost | number }} {{row.monetaryUnit}} </mat-cell>
                                                <mat-footer-cell *matFooterCellDef class="totalCell">{{totalCost}}
                                                </mat-footer-cell>
                                            </ng-container>

                                            <ng-container matColumnDef="description">
                                                <mat-header-cell *matHeaderCellDef i18n>Mô tả</mat-header-cell>
                                                <mat-cell *matCellDef="let row" i18n-data-label data-label="Mô tả">
                                                    {{row.description}}
                                                </mat-cell>
                                                <mat-footer-cell *matFooterCellDef></mat-footer-cell>
                                            </ng-container>

                                            <ng-container matColumnDef="pay">
                                                <mat-header-cell *matHeaderCellDef i18n>Thanh toán</mat-header-cell>
                                                <mat-cell *matCellDef="let row" data-label="Thanh toán">
                                                    <span *ngIf="row.paid != row.quantity*row.cost" i18n>Chưa thanh toán</span>
                                                    <span *ngIf="row.paid == row.quantity*row.cost" i18n>Đã thanh toán</span>
                                                </mat-cell>
                                                <mat-footer-cell *matFooterCellDef class="totalCell">
                                                    {{totalPaided}}
                                                    <span class="rest">(<span i18n>Còn lại</span> {{totalRemaining}})</span>
                                                </mat-footer-cell>
                                            </ng-container>

                                            <ng-container matColumnDef="required">
                                                <mat-header-cell *matHeaderCellDef>Bắt buộc thanh toán</mat-header-cell>
                                                <mat-cell *matCellDef="let row" i18n-data-label data-label="Mô tả" req>
                                                    <mat-icon *ngIf="row.required == 1">checked</mat-icon>
                                                </mat-cell>
                                                <mat-footer-cell *matFooterCellDef></mat-footer-cell>
                                            </ng-container>

                                            <ng-container matColumnDef="typePay">
                                                <mat-header-cell *matHeaderCellDef>Hình thức thanh toán</mat-header-cell>
                                                <mat-cell *matCellDef="let row" i18n-data-label data-label="Mô tả" req>
                                                    {{row.typePay}}
                                                </mat-cell>
                                                <mat-footer-cell *matFooterCellDef></mat-footer-cell>
                                            </ng-container>

                                            <mat-header-row *matHeaderRowDef="feeDisplayedColumns"></mat-header-row>
                                            <mat-row *matRowDef="let row; columns: feeDisplayedColumns;"></mat-row>
                                            <mat-footer-row *matFooterRowDef="feeDisplayedColumns" [ngClass]="{'hidden': hideTotal == true}">
                                            </mat-footer-row>
                                        </mat-table>

                                    </div>
                                </div>
                                <ng-template #bdhFeeVatTemplate>
                                    <app-bdh-fee-vat-template
                                      [enableRemoveVnpostFeeToAnotherTable]="enableRemoveVnpostFeeToAnotherTable"
                                      [dossierReceivingKind_VNP]="dossierReceivingKind_VNP"
                                      [hideCurrencyConverter]="hideCurrencyConverter"
                                      [convertedCurrency]="convertedCurrency"
                                      [showExchangeRate]="showExchangeRate"
                                      [hideTotal]="hideTotal"
                                      [behaviorData]="FEE_ELEMENTDATA_VAT$"
                                    ></app-bdh-fee-vat-template>
                                  </ng-template>
                                <div style="margin-top: 24px;">
                                    <div class="feeRefund" *ngIf = "showFeeRefundData">
                                        <div class="s_head">
                                            <span >Thông tin hoàn tiền</span>
                                        </div>
                                        <br fxShow="true" fxHide.gt-sm>
                                        <div class="user_info" fxLayout="row wrap" fxLayout.xs="column" fxLayout.sm="row wrap" style="margin-top: 16px;">
                                            <div fxFlex.gt-sm="33" fxFlex="50" class="item">
                                                <div >Số tài khoản:</div>
                                                <div *ngIf="feeRefundData?.stk">{{ feeRefundData.stk }}</div>
                                            </div>
                                            <div fxFlex.gt-sm="33" fxFlex="50" class="item">
                                                <div >Tên chủ tài khoản:</div>
                                                <div *ngIf="feeRefundData?.owner">{{ feeRefundData.owner }}</div>
                                            </div>
                                            <div fxFlex.gt-sm="33" fxFlex="50" class="item">
                                                <div >Tên ngân hàng:</div>
                                                <div *ngIf="feeRefundData?.bank">{{ feeRefundData.bank }}</div>
                                            </div>
                                            <div fxFlex.gt-sm="100" fxFlex="50" class="item" style="margin-top: 8px;" *ngIf="feeRefundData?.status ==1 ">
                                                <div >Số tiền đã hoàn:</div>
                                                <div *ngIf="feeRefundData?.feeRefund">{{ feeRefundData.feeRefund | number  }} VNĐ</div>
                                            </div>
                                            <div fxFlex.gt-sm="100" fxFlex="50" class="item" *ngIf="feeRefundData?.status ==1 ">
                                                <div >Lý do hoàn tiền:</div>
                                                <div *ngIf="feeRefundData?.description">{{ feeRefundData.description }}</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <button mat-button [matMenuTriggerFor]="paymentMenu" class="payControl">
                                    <mat-icon>credit_card</mat-icon>
                                    <span i18n>Thanh toán</span>
                                    <mat-icon>keyboard_arrow_down</mat-icon>
                                </button>
                                <mat-menu #paymentMenu="matMenu">
                                    <button *ngIf="isQRPaymentAtCounterActivated" mat-menu-item class="menuAction" (click)="dniQRCode()">
                                        <mat-icon>payment</mat-icon>
                                        <span>Thanh toán trực tuyến tại quầy</span>
                                    </button>

                                    <button *ngIf="isPaymentCheckingActivated" mat-menu-item class="menuAction" (click)="checkPaymentByCode()">
                                        <mat-icon>payment</mat-icon>
                                        <span>Kiểm tra thanh toán</span>
                                    </button>
                                    <button mat-menu-item class="menuAction" *ngIf="(showPaymentRequestButtonDNI && (dossierDetail[0]?.dossierStatus?.id === 2 || dossierDetail[0]?.dossierStatus.id == 4) && paymentLater && dossierDetail[0]?.checkPaymentLaterDNI) && !disableRequestPayment" (click)="paymentRequest(dossierDetail[0]?.id, dossierDetail[0]?.code)">
                                        <mat-icon>payment</mat-icon>
                                        <span>Yêu cầu thanh toán hồ sơ</span>
                                    </button>
                                    <button mat-menu-item class="menuAction" (click)="payment(dossierDetail[0]?.id)">
                                        <mat-icon>monetization_on</mat-icon>
                                        <span >Thanh toán trực tuyến</span>
                                    </button>
                                    <button *ngIf="isSmsPaymentQNM == true" mat-menu-item class="menuAction" (click)="paymentRequestCitizen()">
                                        <mat-icon>monetization_on</mat-icon>
                                        <span >Gửi yêu cầu thanh toán</span>
                                    </button>
                                    <button *ngIf="isSmsPaymentQNM == true" mat-menu-item class="menuAction" (click)="paymentOnlineOfficer()">
                                        <mat-icon>monetization_on</mat-icon>
                                        <span >Thanh toán trực tuyến</span>
                                    </button>
                                    <button mat-menu-item class="menuAction" (click)="paymentHistory()">
                                        <mat-icon>monetization_on</mat-icon>
                                        <span i18n>Lịch sử thanh toán</span>
                                    </button>
                                    <mat-divider></mat-divider>
                                    <button mat-menu-item class="menuAction" (click)="invoiceCreation()" *ngIf="isTheHandler && isTaskComplete(currentTask[0].activitiTask.status) == false">
                                        <mat-icon>receipt_long</mat-icon>
                                        <span i18n>Phát hành hóa đơn</span>
                                    </button>
                                    <button mat-menu-item class="menuAction" (click)="invoiceList()">
                                        <mat-icon>streetview</mat-icon>
                                        <span i18n>Xem hóa đơn</span>
                                    </button>
                                  <button mat-menu-item class="menuAction" (click)="paymentByPOSMachine()">
                                    <mat-icon>receipt</mat-icon>
                                    <span i18n>Thanh toán qua máy POS</span>
                                  </button>
                                  <button mat-menu-item class="menuAction" (click)="paymentByPOSMachineList()">
                                    <mat-icon>preview</mat-icon>
                                    <span i18n>Danh sách thanh toán qua máy POS</span>
                                  </button>
                                    <mat-divider></mat-divider>
                                    <button mat-menu-item class="menuAction" (click)="receiptCreation()" *ngIf="isTheHandler && isTaskComplete(currentTask[0].activitiTask.status) == false && this.showReceiptMenuProcessPerson == true && issueReceiptsEnable == false">
                                        <mat-icon>receipt</mat-icon>
                                        <span i18n>Phát hành biên lai</span>
                                    </button>
                                    <button mat-menu-item class="menuAction" (click)="receiptList()">
                                        <mat-icon>preview</mat-icon>
                                        <span i18n>Xem biên lai</span>
                                    </button>
                                    <button mat-menu-item class="menuAction" (click)="receiptPaymentFlatformList()">
                                        <mat-icon>preview</mat-icon>
                                        <span>{{paymentPlatformButtonNameLable ? paymentPlatformButtonNameLable : 'Xem biên lai VNPT Payment Platform'}}</span>
                                    </button>
                                    <mat-divider></mat-divider>
                                    <button mat-menu-item class="menuAction">
                                        <mat-icon>hourglass_bottom</mat-icon>
                                        <span i18n>Đang chờ thanh toán</span>
                                    </button>
                                    <button mat-menu-item class="menuAction" *ngIf="showInputMoneyReceipt == 1" (click)="inputReceiptPopup()">
                                        <mat-icon>receipt</mat-icon>
                                        <span i18n="@@inputMoneyReceipt">Nhập mã số biên lai giấy</span>
                                    </button>
                                    <button mat-menu-item class="menuAction" (click)="printReceiptDialog()"   *ngIf=" enableShowPrintReceiptPaper == true && enableShowPrint == true"  >
                                        <mat-icon>local_printshop</mat-icon>
                                        <span>In biên lai giấy</span>
                                    </button>
                                </mat-menu>
                                <div class="btnReceipt">
                                    <issue-receipts-item *ngIf="!!issueReceiptsEnable" [buttonName]="issueReceiptsName" (click)="receiptCreationHGI()">
                                    <!--[buttonName] là Input, truyền issueReceiptsName để đổi tên nút
                                        Truyền các Input khác cũng tương tự
                                    -->
                                    </issue-receipts-item>
                                </div>
                                <!--Có thể dời button bên dưới vào trong component issue-receipts-item trên, nếu dời vào thì bỏ tham số ngIf và hàm click ra
                                <button mat-button class="payControl" [style.margin.px]="10"  (click)="receiptCreationHGI()" *ngIf="issueReceipts==true">
                                    <mat-icon>receipt</mat-icon>
                                    <span i18n>Phát hành biên lai</span>
                                </button>-->
                            </div>
                            <div class="tbthueDvcqg" *ngIf="TBTHUEDVCQG_ELEMENTDATA.length !== 0">
                                <span>Thông báo thuế về nghĩa vụ tài chính đất đai của hồ sơ</span>
                                <mat-table [dataSource]="tbthuedvcqgDataSource">
                                    <ng-container matColumnDef="stt">
                                        <mat-header-cell *matHeaderCellDef>STT</mat-header-cell>
                                        <mat-cell *matCellDef="let row" data-label="STT"> {{row.stt}} </mat-cell>
                                    </ng-container>

                                    <ng-container matColumnDef="maSoThue">
                                        <mat-header-cell *matHeaderCellDef>Mã số thuế</mat-header-cell>
                                        <mat-cell *matCellDef="let row" data-label="Mã số thuế"> {{row.MA_SO_THUE}} </mat-cell>
                                    </ng-container>

                                    <ng-container matColumnDef="soQuyetDinh">
                                        <mat-header-cell *matHeaderCellDef>Số quyết định</mat-header-cell>
                                        <mat-cell *matCellDef="let row" data-label="Số quyết định"> {{row.SO_QUYET_DINH}} </mat-cell>
                                    </ng-container>

                                    <ng-container matColumnDef="ngayQuyetDinh">
                                        <mat-header-cell *matHeaderCellDef>Ngày quyết định</mat-header-cell>
                                        <mat-cell *matCellDef="let row" data-label="Ngày quyết định"> {{row.NGAY_QUYET_DINH}} </mat-cell>
                                    </ng-container>

                                    <ng-container matColumnDef="tenTieuMuc">
                                        <mat-header-cell *matHeaderCellDef>Tên tiểu mục</mat-header-cell>
                                        <mat-cell *matCellDef="let row" data-label="Tên tiểu mục"> {{row.TEN_TIEUMUC}} </mat-cell>
                                    </ng-container>

                                    <ng-container matColumnDef="soTien">
                                        <mat-header-cell *matHeaderCellDef>Số tiền</mat-header-cell>
                                        <mat-cell *matCellDef="let row" data-label="Số tiền"> {{row.SO_TIEN}} </mat-cell>
                                    </ng-container>

                                    <ng-container matColumnDef="ngayNhanTbThue">
                                        <mat-header-cell *matHeaderCellDef>Ngày nhận thông báo</mat-header-cell>
                                        <mat-cell *matCellDef="let row" data-label="Ngày nhận thông báo"> {{row.ngayNhanTbThue}} </mat-cell>
                                    </ng-container>

                                    <mat-header-row *matHeaderRowDef="tbthueDisplayedColumns"></mat-header-row>
                                    <mat-row *matRowDef="let row; columns: tbthueDisplayedColumns;"></mat-row>
                                </mat-table>
                            </div>
                        </div>
                    </mat-tab>

                    <!-- Detail -->
                    <mat-tab *ngIf="!hiddenInformationDetailTab">
                        <ng-template mat-tab-label>
                            <mat-icon class="tabIcon">info</mat-icon>
                            <span i18n>Thông tin chi tiết</span>
                        </ng-template>
                        <div class="card" fxFlex='grow'>
                            <div class="cardTitle" fxLayoutAlign="space-between">
                                <span i18n>Thông tin chi tiết</span>
                                <span style="color: #ce7a58; margin: auto" *ngIf="!!dossier?.extendHCM?.hasNotInputEForm === true">Tiếp nhận trực tiếp không nhập thông tin bắt buộc</span>

                                <div class="cardAction detailDone" *ngIf="!showUpdateDetailInfoBtn()">
                                    <mat-spinner diameter="25"></mat-spinner>
                                    <div class="done ">
                                        <mat-icon>check_circle_outline</mat-icon>
                                        <span i18n>Đã lưu</span>
                                    </div>

                                </div>
                              <button mat-button class="cardAction" (click)="retrieveEformData(dossierDetail[0])" *ngIf="showRetreiveFormDataBtn() && !showUpdateDetailInfoBtn()">
                                <mat-icon>edit</mat-icon>
                                <span fxHide fxShow.gt-sm>Lấy lại thông tin theo mã số</span>
                              </button>
                              <div class="cardAction" *ngIf="showUpdateDetailInfoBtn()">
                                <button mat-button class="cardAction" (click)="retrieveEformData(dossierDetail[0])" *ngIf="showRetreiveFormDataBtn()">
                                  <mat-icon>edit</mat-icon>
                                  <span fxHide fxShow.gt-sm>Lấy lại thông tin theo mã số</span>
                                </button>
                                <div fxFlex='1' *ngIf="showUpdateDetailInfoBtn()"></div>
                                <button mat-button class="cardAction" (click)="updateDetailInfo()" *ngIf="!offDossierDetailUpdate && isTheHandler != false && isTaskComplete(currentTask[0].activitiTask.status) == false">
                                  <mat-icon>edit</mat-icon>
                                  <span i18n>Cập nhật thông tin</span>
                                </button>
                              </div>
                            </div>
                            <div class="cardContent">
                                <div class="thoaiisolate" *ngIf="showUpdateDetailInfoBtn()">
                                    <formio *ngIf="formIO.component !== undefined && formIO.component != null && formIO.component != ''" [form]="formIO.component" [submission]="formIO.data" [renderOptions]="eForm.renderOptions"  [readOnly]="true" [viewOnly]="true">
                                    </formio>
                                </div>
                              <div class="thoaiisolate" *ngIf="!showUpdateDetailInfoBtn()">
                                <formio *ngIf="formIO.component !== undefined && formIO.component != null && formIO.component != ''" [form]="formIO.component" [submission]="formIO.data" [renderOptions]="eForm.renderOptions" (change)="onFormIOSubmit($event, 'detailDone')" [readOnly]="offDossierDetailUpdate || isTheHandler == false || isTaskComplete(currentTask[0].activitiTask.status) == true"
                                        [viewOnly]="offDossierDetailUpdate || isTheHandler == false || isTaskComplete(currentTask[0].activitiTask.status) == true">
                                </formio>
                              </div>
                            </div>
                        </div>

                    </mat-tab>

                          <!-- Thông tin nội bộ -->
                <mat-tab *ngIf="isShowInternalForm == true">
                    <ng-template mat-tab-label>
                        <mat-icon class="tabIcon">info</mat-icon>
                        <span>Thông tin nội bộ</span>
                    </ng-template>
                    <div class="card" fxFlex='grow'>
                        <div class="cardTitle" fxLayoutAlign="space-between">
                            <span>Thông tin nội bộ</span>
                          <div class="cardAction">
                            <button mat-button class="cardAction" (click)="updateEIntervalFormDetailInfo()" *ngIf="isTheHandler != false && isTaskComplete(currentTask[0].activitiTask.status) == false">
                              <mat-icon>edit</mat-icon>
                              <span>Cập nhật thông tin nội bộ</span>
                            </button>
                          </div>
                        </div>
                        <div class="cardContent">
                            <div class="thoaiisolate">
                                <formio *ngIf="internalFormIO.component !== undefined && internalFormIO.component != null && internalFormIO.component != ''" [form]="internalFormIO.component" [submission]="internalFormIO.data" [renderOptions]="eInternalForm.renderOptions">
                                </formio>
                            </div>
                        </div>
                    </div>

                </mat-tab>
                    <!-- Receive Address -->
                    <mat-tab *ngIf="!hiddenReceivingMethodTab">
                        <ng-template mat-tab-label>
                            <mat-icon class="tabIcon">verified</mat-icon>
                            <span i18n>Hình thức nhận kết quả</span>
                        </ng-template>
                        <div class="card" fxFlex='grow'>
                            <div class="cardTitle" fxLayoutAlign="space-between">
                                <span i18n>Hình thức nhận kết quả</span>
                                <div class="cardAction receiveDone">
                                    <mat-spinner diameter="25"></mat-spinner>
                                    <div class="done ">
                                        <mat-icon>check_circle_outline</mat-icon>
                                        <span i18n>Đã lưu</span>
                                    </div>
                                </div>
                            </div>
                            <div class="cardContent">
                                <form [formGroup]="receiveForm" class="receiveForm formFieldOutline">
                                    <div fxLayout="row" fxLayout.xs="row" fxLayout.sm="row" class="formFieldItems" fxLayoutAlign="space-between" *ngIf="dossierReceivingKind != (env?.vnpost?.receiveResultsByAddress != undefined ? env?.vnpost?.receiveResultsByAddress : config.receiveResultsByAddress)">

                                        <mat-form-field appearance="outline" fxFlex.gt-sm="50" fxFlex.gt-xs="grow" fxFlex='grow'>
                                            <mat-label i18n>Hình thức nhận kết quả</mat-label>
                                            <mat-select formControlName="receivingKind" (selectionChange)="receivingKindChange($event, 'receiveDone')"
                                                        [disabled]="(isTheHandler == false || isTaskComplete(currentTask[0].activitiTask.status) == true || env?.vnpost?.disableHinhThucNhanKetQua == 1) || (dossierDetail[0]?.dossierReceivingKind?.id == receiveResultsAdministrativeCenterId && disableDataVnpost5343) || (viettelPostEnable && dossierDetail[0]?.dossierReceivingKind?.id == viettelPostReceiveResultsByAddress)">
                                                <mat-option *ngFor="let kind of listDossierReceivingKind" [value]="kind.id">
                                                    {{kind.name}}
                                                    <span *ngIf="kind.name == undefined || kind.name == null || kind.name.trim() == ''" i18n>(Không tìm thấy bản dịch)</span>
                                                </mat-option>
                                            </mat-select>
                                        </mat-form-field>

                                    </div>
                                    <!--- START --- ViettelPost ---->
                                    <form class="receiveForm" [formGroup]="viettelPostForm" novalidate *ngIf="viettelPostEnable && dossierDetail[0]?.dossierReceivingKind?.id == viettelPostReceiveResultsByAddress">
                                        <div fxLayout="row" fxLayout.xs="row" fxLayout.sm="row" class="formFieldItems" fxLayoutAlign="start center" fxLayoutGap="30px" style="margin-top: -10px;margin-bottom: 10px">
                                            <mat-checkbox formControlName="vpSend" matTooltipClass="custom-tooltip" matTooltip="Đăng ký nộp hồ sơ tại nhà" matTooltipPosition="right" disabled>
                                                Đăng ký nộp hồ sơ tại nhà
                                            </mat-checkbox>
                                            <mat-checkbox formControlName="vpReceive" matTooltipClass="custom-tooltip" matTooltip="Đăng ký nhận kết quả tại nhà" matTooltipPosition="right" disabled>
                                                Đăng ký nhận kết quả tại nhà
                                            </mat-checkbox>
                                        </div>
                                        <div fxLayout="row" fxLayout.xs="row" fxLayout.sm="row" class="formFieldItems" fxLayoutAlign="space-between">
                                            <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                                                <mat-label>Tên</mat-label>
                                                <input type="text" matInput formControlName="vpName" maxlength="150" disabled>
                                            </mat-form-field>
                                            <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                                                <mat-label>Số điện thoại</mat-label>
                                                <input type="text" matInput formControlName="vpPhoneNumber" disabled>
                                            </mat-form-field>
                                            <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                                                <mat-label>Email</mat-label>
                                                <input type="email" matInput formControlName="vpEmail" maxlength="150" disabled>
                                            </mat-form-field>
                                        </div>
                                        <div class="bordered-container formFieldItems" fxLayout.xs="row" fxLayout.sm="row" fxLayoutAlign="space-between" *ngIf="viettelPostAgencyAddressEnable">
                                            <div class="label-text">Địa chỉ cơ quan tiếp nhận hồ sơ</div>
                                            <div class="formFieldItems" fxFlex.gt-sm="100" fxFlex="grow" fxLayoutAlign="space-between">
                                                <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                                                    <mat-label i18n>Tỉnh/TP</mat-label>
                                                    <mat-select formControlName="vpProvinceA" (selectionChange)="vpProvinceChange('1', $event)" disabled>
                                                        <mat-option *ngFor='let provinceA of listProvinceForVP;' value="{{provinceA.id}}">
                                                            {{provinceA.name}}
<!--                                                            <span *ngIf="provinceA.name == undefined || provinceA.name == null || provinceA.name.trim() == ''" i18n>(Không tìm thấy bản dịch)</span>-->
                                                        </mat-option>
                                                    </mat-select>
                                                </mat-form-field>
                                                <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                                                    <mat-label i18n>Quận/huyện</mat-label>
                                                    <mat-select formControlName="vpDistrictA" (selectionChange)="vpDistrictChange('1', $event)" disabled>
                                                        <mat-option *ngFor='let districtA of listDistrictForVPA;' value="{{districtA.id}}">
                                                            {{districtA.name}}
<!--                                                            <span *ngIf="districtA.name == undefined || districtA.name == null || districtA.name.trim() == ''" i18n>(Không tìm thấy bản dịch)</span>-->
                                                        </mat-option>
                                                    </mat-select>
                                                </mat-form-field>
                                                <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                                                    <mat-label i18n>Phường/xã</mat-label>
                                                    <mat-select formControlName="vpVillageA" disabled>
                                                        <mat-option *ngFor='let villageA of listVillageForVPA;' value="{{villageA.id}}">
                                                            {{villageA.name}}
<!--                                                            <span *ngIf="villageA.name == undefined || villageA.name == null || villageA.name.trim() == ''" i18n>(Không tìm thấy bản dịch)</span>-->
                                                        </mat-option>
                                                    </mat-select>
                                                </mat-form-field>
                                                <mat-form-field appearance="outline" fxFlex.gt-sm="100" fxFlex.gt-xs="49.5" fxFlex.gt-xs="grow" fxFlex='grow'>
                                                    <mat-label i18n>Địa chỉ chi tiết</mat-label>
                                                    <input type="text" matInput formControlName="vpAddressDetailA" maxlength="150" disabled>
                                                </mat-form-field>
                                            </div>
                                        </div>
                                        <div class="bordered-container formFieldItems" fxLayout.xs="row" fxLayout.sm="row" fxLayoutAlign="space-between" *ngIf="dossierDetail[0]?.viettelPost?.vpSend">
                                            <div class="label-text">Địa chỉ bưu cục đến nhận hồ sơ của người dân</div>
                                            <div class="formFieldItems" fxFlex.gt-sm="100" fxFlex="grow" fxLayoutAlign="space-between">
                                                <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                                                    <mat-label i18n>Tỉnh/TP</mat-label>
                                                    <mat-select formControlName="vpProvince" (selectionChange)="vpProvinceChange('2', $event)" disabled>
                                                        <mat-option *ngFor='let province of listProvinceForVP;' value="{{province.id}}">
                                                            {{province.name}}
<!--                                                            <span *ngIf="province.name == undefined || province.name == null || province.name.trim() == ''" i18n>(Không tìm thấy bản dịch)</span>-->
                                                        </mat-option>
                                                    </mat-select>
                                                </mat-form-field>
                                                <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                                                    <mat-label i18n>Quận/huyện</mat-label>
                                                    <mat-select formControlName="vpDistrict" (selectionChange)="vpDistrictChange('2', $event)" disabled>
                                                        <mat-option *ngFor='let district of listDistrictForVP;' value="{{district.id}}">
                                                            {{district.name}}
<!--                                                            <span *ngIf="district.name == undefined || district.name == null || district.name.trim() == ''" i18n>(Không tìm thấy bản dịch)</span>-->
                                                        </mat-option>
                                                    </mat-select>
                                                </mat-form-field>
                                                <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                                                    <mat-label i18n>Phường/xã</mat-label>
                                                    <mat-select formControlName="vpVillage" disabled>
                                                        <mat-option *ngFor='let village of listVillageForVP;' value="{{village.id}}">
                                                            {{village.name}}
<!--                                                            <span *ngIf="village.name == undefined || village.name == null || village.name.trim() == ''" i18n>(Không tìm thấy bản dịch)</span>-->
                                                        </mat-option>
                                                    </mat-select>
                                                </mat-form-field>
                                                <mat-form-field appearance="outline" fxFlex.gt-sm="100" fxFlex.gt-xs="49.5" fxFlex.gt-xs="grow" fxFlex='grow'>
                                                    <mat-label i18n>Địa chỉ chi tiết</mat-label>
                                                    <input type="text" matInput formControlName="vpAddressDetail" maxlength="150" disabled>
                                                </mat-form-field>
                                            </div>
                                        </div>
                                        <div class="bordered-container formFieldItems" fxLayout.xs="row" fxLayout.sm="row" fxLayoutAlign="space-between" *ngIf="dossierDetail[0]?.viettelPost?.vpReceive">
                                            <div class="label-text">Địa chỉ bưu cục gửi kết quả người dân</div>
                                            <div class="formFieldItems" fxFlex.gt-sm="100" fxFlex="grow" fxLayoutAlign="space-between">
                                                <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                                                    <mat-label i18n>Tỉnh/TP</mat-label>
                                                    <mat-select formControlName="vpProvinceR" (selectionChange)="vpProvinceChange('3', $event)" disabled>
                                                        <mat-option *ngFor='let provinceR of listProvinceForVP;' value="{{provinceR.id}}">
                                                            {{provinceR.name}}
<!--                                                            <span *ngIf="provinceR.name == undefined || provinceR.name == null || provinceR.name.trim() == ''" i18n>(Không tìm thấy bản dịch)</span>-->
                                                        </mat-option>
                                                    </mat-select>
                                                </mat-form-field>
                                                <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                                                    <mat-label i18n>Quận/huyện</mat-label>
                                                    <mat-select  formControlName="vpDistrictR" (selectionChange)="vpDistrictChange('3', $event)" disabled>
                                                        <mat-option *ngFor='let districtR of listDistrictForVPR;' value="{{districtR.id}}">
                                                            {{districtR.name}}
<!--                                                            <span *ngIf="districtR.name == undefined || districtR.name == null || districtR.name.trim() == ''" i18n>(Không tìm thấy bản dịch)</span>-->
                                                        </mat-option>
                                                    </mat-select>
                                                </mat-form-field>
                                                <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                                                    <mat-label i18n>Phường/xã</mat-label>
                                                    <mat-select formControlName="vpVillageR" disabled>
                                                        <mat-option *ngFor='let villageR of listVillageForVPR;' value="{{villageR.id}}">
                                                            {{villageR.name}}
<!--                                                            <span *ngIf="villageR.name == undefined || villageR.name == null || villageR.name.trim() == ''" i18n>(Không tìm thấy bản dịch)</span>-->
                                                        </mat-option>
                                                    </mat-select>
                                                </mat-form-field>
                                                <mat-form-field appearance="outline" fxFlex.gt-sm="100" fxFlex.gt-xs="49.5" fxFlex.gt-xs="grow" fxFlex='grow'>
                                                    <mat-label i18n>Địa chỉ chi tiết</mat-label>
                                                    <input type="text" matInput formControlName="vpAddressDetailR" maxlength="150" disabled>
                                                </mat-form-field>
                                            </div>
                                        </div>
                                    </form>
                                    <!---- END ---- ViettelPost ---->

                                    <div fxLayout="row" fxLayout.xs="row" fxLayout.sm="row" class="formFieldItems" fxLayoutAlign="space-between" *ngIf="dossierReceivingKind == (env?.vnpost?.receiveResultsByAddress != undefined ? env?.vnpost?.receiveResultsByAddress : config.receiveResultsByAddress)">

                                        <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                                            <mat-label i18n>Hình thức nhận kết quả</mat-label>
                                            <mat-select formControlName="receivingKind" (selectionChange)="receivingKindChange($event, 'receiveDone')"
                                                        [disabled]="(isTheHandler == false || isTaskComplete(currentTask[0].activitiTask.status) == true || env?.vnpost?.disableHinhThucNhanKetQua == 1 ? 'true' : 'false') || (dossierDetail[0]?.dossierReceivingKind?.id == receiveResultsAdministrativeCenterId && disableDataVnpost5343)">
                                                <mat-option *ngFor="let kind of listDossierReceivingKind" [value]="kind.id">
                                                    {{kind.name}}
                                                    <span *ngIf="kind.name == undefined || kind.name == null || kind.name.trim() == ''" i18n>(Không tìm thấy bản dịch)</span>
                                                </mat-option>
                                            </mat-select>
                                        </mat-form-field>

                                        <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                                            <mat-label i18n>Quốc gia</mat-label>
                                            <mat-select formControlName="rcNation" (selectionChange)="nationChange(); onFormGroupChange(receiveForm, 'receiveDone')"
                                                        [disabled]="isTheHandler == false || isTaskComplete(currentTask[0].activitiTask.status) == true || env?.vnpost?.disableHinhThucNhanKetQua == 1 ? 'true' : 'false'">
                                                <mat-option *ngFor='let nationOpt of listNation;' value="{{nationOpt.id}}">
                                                    {{nationOpt.name}}
                                                    <span *ngIf="nationOpt.name == undefined || nationOpt.name == null || nationOpt.name.trim() == ''" i18n>(Không tìm thấy bản dịch)</span>
                                                </mat-option>
                                            </mat-select>
                                        </mat-form-field>

                                        <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                                            <mat-label i18n>Tỉnh/TP</mat-label>
                                            <mat-select formControlName="rcProvince" (selectionChange)="provinceChange(); onFormGroupChange(receiveForm, 'receiveDone')"
                                                        [disabled]="isTheHandler == false || isTaskComplete(currentTask[0].activitiTask.status) == true || env?.vnpost?.disableHinhThucNhanKetQua == 1 ? 'true' : 'false'">
                                                <mat-option *ngFor='let provinceOpt of listProvince;' value="{{provinceOpt.id}}">
                                                    {{provinceOpt.name}}
                                                    <span *ngIf="provinceOpt.name == undefined || provinceOpt.name == null || provinceOpt.name.trim() == ''" i18n>(Không tìm thấy bản dịch)</span>
                                                </mat-option>
                                            </mat-select>
                                        </mat-form-field>

                                        <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                                            <mat-label>Phường/xã</mat-label>
                                            <mat-select formControlName="rcDistrict" (selectionChange)="districtChange(); onFormGroupChange(receiveForm, 'receiveDone')"
                                                        [disabled]="this.receiveForm.get('rcProvince').value == '' || isTheHandler == false || isTaskComplete(currentTask[0].activitiTask.status) == true || env?.vnpost?.disableHinhThucNhanKetQua == 1 ? 'true' : 'false'">
                                                <mat-option *ngFor='let districtOpt of listDistrict;' value="{{districtOpt.id}}">
                                                    {{districtOpt.name}}
                                                    <span *ngIf="districtOpt.name == undefined || districtOpt.name == null || districtOpt.name.trim() == ''" i18n>(Không tìm thấy bản dịch)</span>
                                                </mat-option>
                                            </mat-select>
                                        </mat-form-field>

                                        <!-- <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                                            <mat-label i18n>Phường/xã</mat-label>
                                            <mat-select formControlName="rcVillage" (selectionChange)="onFormGroupChange(receiveForm, 'receiveDone')"
                                                        [disabled]="this.receiveForm.get('rcDistrict').value == '' || isTheHandler == false || isTaskComplete(currentTask[0].activitiTask.status) == true || env?.vnpost?.disableHinhThucNhanKetQua == 1 ? 'true' : 'false'">
                                                <mat-option *ngFor='let wardtOpt of listVillage;' value="{{wardtOpt.id}}">
                                                    {{wardtOpt.name}}
                                                    <span *ngIf="wardtOpt.name == undefined || wardtOpt.name == null || wardtOpt.name.trim() == ''" i18n>(Không tìm thấy bản dịch)</span>
                                                </mat-option>
                                            </mat-select>
                                        </mat-form-field> -->

                                        <mat-form-field appearance="outline" fxFlex.gt-sm="65" fxFlex.gt-xs="grow" fxFlex='grow'>
                                            <mat-label i18n>Địa chỉ chi tiết</mat-label>
                                            <input type="text" matInput formControlName="customAddress" (ngModelChange)="onFormGroupChange(receiveForm, 'receiveDone')"
                                                    [readonly]="isTheHandler == false || isTaskComplete(currentTask[0].activitiTask.status) == true || env?.vnpost?.disableHinhThucNhanKetQua == 1 ? 'true' : 'false'">
                                        </mat-form-field>

                                        <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                                            <mat-label >Tên</mat-label>
                                            <input type="text" matInput formControlName="rcName" maxlength="500" [readonly]="env?.vnpost?.disableHinhThucNhanKetQua == 1 ? 'true' : 'false'">
                                        </mat-form-field>

                                        <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                                            <mat-label >Số điện thoại</mat-label>
                                            <input type="text" matInput formControlName="rcPhoneNumber" [maxlength]="numberOfPhoneVNPostCharacter" [readonly]="env?.vnpost?.disableHinhThucNhanKetQua == 1 ? 'true' : 'false'">
                                        </mat-form-field>

                                        <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                                            <mat-label >Email</mat-label>
                                            <input type="email" matInput formControlName="rcEmail" maxlength="500" [readonly]="env?.vnpost?.disableHinhThucNhanKetQua == 1 ? 'true' : 'false'">
                                        </mat-form-field>
                                    </div>
                                </form>
                                <form [formGroup]="extendCTO" class="receiveForm formFieldOutline" novalidate>
                                    <div fxLayout="row" fxLayout.xs="row" fxLayout.sm="row" class="formFieldItems" fxLayoutAlign="start" *ngIf="enableGetFileDossier === 1">             
                                      <mat-form-field appearance="outline" fxFlex.gt-sm="49.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                                        <mat-label>Sổ tiếp nhận</mat-label>
                                        <mat-select formControlName="receiptBook" msInfiniteScroll (infiniteScroll)="getNextBatchReceiptBook()" (selectionChange)="receiptBookChange($event, 'receiveDone')">
                                          <div>
                                            <input  matInput (keyup)="onSearchReceiptBook($event)" (keydown)="$event.stopPropagation()"
                                                    placeholder="Nhập từ khóa" class="search-nested" />
                                          </div>
                                          <mat-option [value]="null">Chọn sổ tiếp nhận</mat-option>
                                          <mat-option *ngFor="let item of listReceiptBook" [value]="item.id">
                                            {{item.name}}
                                          </mat-option>
                                        </mat-select>
                                      </mat-form-field>                                                  
                                    </div>              
                                </form>                                                                                                                     
                            </div>
                            <div fxLayout="row" fxLayout.xs="row" fxLayout.sm="row" class="formFieldItems" fxLayoutAlign="space-between"
                                *ngIf="dossierDetail[0]?.dossierReceivingKind?.id == receiveResultsAdministrativeCenterId">
                                <div class="bordered-container">
                                    <div class="label-text">Nơi gửi</div>
                                    <form [formGroup]="tabSpecializedAgencyForm" novalidate class="formFieldOutline">
                                        <div fxLayout="row" fxLayout.xs="row" fxLayout.sm="row" class="formFieldItems"
                                        fxLayoutAlign="space-between">
                                        <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                                            <mat-label>Tên</mat-label>
                                            <input type="text"  matInput formControlName="rcName" maxlength="500">
                                        </mat-form-field>
                
                                        <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                                            <mat-label>Số điện thoại</mat-label>
                                            <input type="text"  matInput formControlName="rcPhoneNumber" [maxlength]="numberOfPhoneVNPostCharacter">
                                        </mat-form-field>
                
                                        <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                                            <mat-label>Email</mat-label>
                                            <input type="email"  matInput formControlName="rcEmail" maxlength="500">
                                        </mat-form-field>
                
                                        <div class="groupbox" fxLayout="row wrap" fxFlex.gt-sm="100" fxFlex="grow"
                                            fxLayoutAlign="space-between">
                                            <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                                                <mat-label>Tỉnh/TP</mat-label>
                                                <input type="email"  matInput formControlName="rcProvince">
                                                <!-- <mat-select required formControlName="rcProvince"
                                                    (selectionChange)="rcProvinceChange()">
                                                    <mat-option *ngFor='let provinceOpt of listProvinceForRC;'
                                                        value="{{provinceOpt.id}}">
                                                        {{provinceOpt.name}} </mat-option>
                                                </mat-select> -->
                                            </mat-form-field>
                
                                            <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                                                <mat-label>Phường/xã</mat-label>
                                                <input type="email"  matInput formControlName="rcDistrict">
                                                <!-- <mat-select required formControlName="rcDistrict" (selectionChange)="rcDistrictChange()"
                                                    [disabled]="this.tabAdministrativeCenterForm2.get('rcProvince').value == '' ">
                                                    <mat-option *ngFor='let districtOpt of listDistrictForRC;'
                                                        value="{{districtOpt.id}}">
                                                        {{districtOpt.name}} </mat-option>
                                                </mat-select> -->
                                            </mat-form-field>
                
                                            <!-- <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                                                <mat-label>Phường/xã</mat-label>
                                                <input type="email"  matInput formControlName="rcVillage"> -->
                                                <!-- <mat-select required formControlName="rcVillage"
                                                    [disabled]="this.tabAdministrativeCenterForm2.get('rcDistrict').value == '' ">
                                                    <mat-option *ngFor='let wardtOpt of listVillageForRC;' value="{{wardtOpt.id}}">
                                                        {{wardtOpt.name}}
                                                    </mat-option>
                                                </mat-select> -->
                                            <!-- </mat-form-field> -->
                
                                            <mat-form-field appearance="outline" fxFlex.gt-sm="100" fxFlex.gt-xs="grow" fxFlex='grow'>
                                                <mat-label>Địa chỉ chi tiết</mat-label>
                                                <input type="text"  matInput formControlName="customAddress">
                                            </mat-form-field>
                                        </div>
                                    </div>
                                    </form>
                                </div>
                                <div class="bordered-container">
                                    <div class="label-text">Nơi nhận</div>
                                    <form [formGroup]="tabAdministrativeCenterForm" novalidate class="formFieldOutline">
                                        <div fxLayout="row" fxLayout.xs="row" fxLayout.sm="row" class="formFieldItems"
                                            fxLayoutAlign="space-between">
                                            <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                                                <mat-label>Tên</mat-label>
                                                <input type="text" matInput formControlName="rcName" maxlength="500">
                                            </mat-form-field>
                    
                                            <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                                                <mat-label>Số điện thoại</mat-label>
                                                <input type="text" matInput formControlName="rcPhoneNumber" [maxlength]="numberOfPhoneVNPostCharacter">
                                            </mat-form-field>
                    
                                            <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                                                <mat-label>Email</mat-label>
                                                <input type="email" matInput formControlName="rcEmail" maxlength="500">
                                            </mat-form-field>
                    
                                            <div class="groupbox" fxLayout="row wrap" fxFlex.gt-sm="100" fxFlex="grow"
                                                fxLayoutAlign="space-between">
                                                <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                                                    <mat-label>Tỉnh/TP</mat-label>
                                                    <input type="email" matInput formControlName="rcProvince">
                                                    <!-- <mat-select required formControlName="rcProvince"
                                                        (selectionChange)="rcProvinceChange()">
                                                        <mat-option *ngFor='let provinceOpt of listProvinceForRC;'
                                                            value="{{provinceOpt.id}}">
                                                            {{provinceOpt.name}} </mat-option>
                                                    </mat-select> -->
                                                </mat-form-field>
                    
                                                <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                                                    <mat-label>Phường/xã</mat-label>
                                                    <input type="email"  matInput formControlName="rcDistrict">
                                                    <!-- <mat-select required formControlName="rcDistrict" (selectionChange)="rcDistrictChange()"
                                                        [disabled]="this.tabAdministrativeCenterForm2.get('rcProvince').value == '' ">
                                                        <mat-option *ngFor='let districtOpt of listDistrictForRC;'
                                                            value="{{districtOpt.id}}">
                                                            {{districtOpt.name}} </mat-option>
                                                    </mat-select> -->
                                                </mat-form-field>
                    
                                                <!-- <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                                                    <mat-label>Phường/xã</mat-label>
                                                    <input type="email"  matInput formControlName="rcVillage"> -->
                                                    <!-- <mat-select required formControlName="rcVillage"
                                                        [disabled]="this.tabAdministrativeCenterForm2.get('rcDistrict').value == '' ">
                                                        <mat-option *ngFor='let wardtOpt of listVillageForRC;' value="{{wardtOpt.id}}">
                                                            {{wardtOpt.name}}
                                                        </mat-option>
                                                    </mat-select> -->
                                                <!-- </mat-form-field> -->
                    
                                                <mat-form-field appearance="outline" fxFlex.gt-sm="100" fxFlex.gt-xs="grow" fxFlex='grow'>
                                                    <mat-label>Địa chỉ chi tiết</mat-label>
                                                    <input type="text"  matInput formControlName="customAddress">
                                                </mat-form-field>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                                <div style="width: 100%">
                                    <form [formGroup]="feeForm" class="cardContent cardContent5343">
                                      <div *ngIf="vnpostReceiveResultFeeDataSource?.data != ''">
                                        <div class="fee_head">
                                          <div class="s_head mb-1">
                                            <span>Thông tin phí chuyển hồ sơ</span>
                                          </div>
                                          <!-- <div class="s_head mb-1">
                                            <div class='payment-status-vnpost'>
                                              <div class='payment-status-vnpost' i18n="@@dossierFeeDescriptionTitle">(Hiện tại hệ thống chỉ áp dụng thu phí,
                                                lệ phí hồ sơ. *Lưu ý: Đối với phí dịch vụ bưu chính (nếu có) người dân vui lòng thanh toán trực tiếp cho
                                                nhân viên bưu chính khi nhận kết quả tại nhà)</div>
                                            </div>
                                          </div> -->
                                        </div>
                                        <div class="procedureFee" fxLayout="column" fxLayout.xs="column" fxLayout.sm="column">
                                          <mat-table [dataSource]="vnpostReceiveResultFeeDataSource" fxFlex='grow' class="tblFee">
                                            <!-- <ng-container matColumnDef="required">
                                              <mat-header-cell *matHeaderCellDef class="checkAllProcedureAdd">
                                                Bắt buộc thanh toán
                                              </mat-header-cell>
                                              <mat-cell *matCellDef="let row; let i = index;" data-label="Chọn" class="checkAllProcedureAdd" i18n-data-label>
                                                <mat-checkbox [checked]="row.checked">
                                                </mat-checkbox>
                                              </mat-cell>
                                              <mat-footer-cell *matFooterCellDef></mat-footer-cell>
                                            </ng-container> -->
                                            <ng-container matColumnDef="procostType">
                                              <mat-header-cell *matHeaderCellDef i18n>Loại lệ phí</mat-header-cell>
                                              <mat-cell *matCellDef="let row" i18n-data-label data-label="Loại lệ phí"> {{row.typeName}}
                                              </mat-cell>
                                              <mat-footer-cell *matFooterCellDef>Tổng</mat-footer-cell>
                                            </ng-container>
                
                                            <ng-container matColumnDef="quantity">
                                              <mat-header-cell *matHeaderCellDef i18n>Số lượng</mat-header-cell>
                                              <mat-cell *matCellDef="let row; let i = index" i18n-data-label data-label="Số lượng">
                                                <span *ngIf="editFeeIndex !==i">{{row?.quantity}}</span>
                                              </mat-cell>
                                              <mat-footer-cell *matFooterCellDef></mat-footer-cell>
                                            </ng-container>
                
                                            <ng-container matColumnDef="cost">
                                              <mat-header-cell *matHeaderCellDef i18n>Mức lệ phí</mat-header-cell>
                                              <mat-cell *matCellDef="let row; let i = index" i18n-data-label data-label="Mức lệ phí">
                                                  <span *ngIf="editFeeIndex!==i">{{row?.cost|number}} {{row?.monetaryUnit}}</span>
                                                  <!-- <mat-select (selectionChange)="changeCount(row?.stt, $event)" *ngIf="editFeeIndex === i"
                                                              fxFlex.gt-sm="150" fxFlex.gt-xs="150" value="{{row?.costCaseIndex}}">
                                                      <mat-option *ngFor='let cost of row?.costCase; let j = index;' title="{{cost.cost | number}} {{row?.monetaryUnit}} ({{cost.description}})"
                                                                  value="{{j}}">
                                                          {{cost.cost | number}} {{row?.monetaryUnit}} ({{cost.description}})
                                                      </mat-option>
                                                  </mat-select> -->
                                              </mat-cell>
                                              <mat-footer-cell *matFooterCellDef></mat-footer-cell>
                                            </ng-container>
                
                                            <ng-container matColumnDef="amount">
                                              <mat-header-cell *matHeaderCellDef i18n>Thành tiền</mat-header-cell>
                                              <mat-cell *matCellDef="let row" i18n-data-label data-label="Thành tiền">
                                                {{row?.quantity*row?.cost|number}}&nbsp;{{row?.monetaryUnit}}
                                              </mat-cell>
                                              <mat-footer-cell *matFooterCellDef="let row" class="totalCell">{{vnpostReceiveResultFeeDataSourceTotal}}</mat-footer-cell>
                                            </ng-container>
                
                                            <ng-container matColumnDef="description">
                                              <mat-header-cell *matHeaderCellDef i18n>Mô tả</mat-header-cell>
                                              <mat-cell *matCellDef="let row" i18n-data-label data-label="Mô tả"> {{row?.description}}
                                              </mat-cell>
                                              <mat-footer-cell *matFooterCellDef class="payment-status-vnpost" i18n="@@vnPostDescriptionTotal">Nhân viên bưu chính sẽ thu phí trực tiếp tại nhà khi thu hồ sơ/trả kết quả</mat-footer-cell>
                                            </ng-container>
                                            <ng-container matColumnDef="pay">
                                              <mat-header-cell *matHeaderCellDef i18n>Đã thanh toán</mat-header-cell>
                                              <mat-cell *matCellDef="let row; let i = index" data-label="Thanh toán">
                                                <input *ngIf="editFeeIndex!==i" type="checkbox" [checked]="row?.paid > 0" disabled>
                                                <mat-checkbox *ngIf="editFeeIndex===i" formControlName="paided">
                                                </mat-checkbox>
                                              </mat-cell>
                                              <mat-footer-cell *matFooterCellDef="let row; let i = index" class="totalCell">
                                                {{vnpostReceiveResultFeeDataSourcePaided}}
                                                <br>
                                                (Còn lại: {{vnpostReceiveResultFeeDataSourceDebt}})
                                              </mat-footer-cell>
                                            </ng-container>
                
                                            <ng-container matColumnDef="action">
                                              <mat-header-cell *matHeaderCellDef i18n>Thao tác</mat-header-cell>
                                              <mat-cell *matCellDef="let row; let i = index" i18n-data-label data-label="Cập nhật" class="text-center">
                                                <span *ngIf="editFeeIndex ===i">...</span>
                                              </mat-cell>
                                              <mat-footer-cell *matFooterCellDef></mat-footer-cell>
                                            </ng-container>
                
                                            <mat-header-row *matHeaderRowDef="feeDisplayedColumns5343"></mat-header-row>
                                            <mat-row *matRowDef="let row; columns: feeDisplayedColumns5343;"></mat-row>
                                            <mat-footer-row *matFooterRowDef="feeDisplayedColumns5343" [ngClass]="{'hidden': hideTotal == true}">
                                            </mat-footer-row>
                                          </mat-table>
                                        </div>
                                      </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </mat-tab>

                    <mat-tab disabled *ngIf="isShowOpenButton && isHavePermission">
                        <ng-template mat-tab-label>
                            <button mat-icon-button (click)="toggleTabBodies()">
                                <mat-icon class="addButton">add</mat-icon>
                            </button>
                        </ng-template>
                    </mat-tab>

                      <!-- info license KTM -->
                      <mat-tab *ngIf="checkDossierStatus && isCheck">
                        <ng-template mat-tab-label>
                            <mat-icon class="tabIcon">info</mat-icon>
                            <span>Thông tin giấy phép </span>
                        </ng-template>

                        <ng-template mat-tab-label>
                            <mat-icon class="tabIcon">assignment_ind</mat-icon>
                            <span>Thông tin giấy phép</span>
                        </ng-template>

                        <div class="card" fxFlex='grow'>
                            <div class="cardTitle" fxLayoutAlign="space-between" style="margin-bottom: 20px;">
                                <span>Thông tin giấy phép</span>
                                <button (click)="syncLicense()" mat-button class="cardAction">
                                    <mat-icon>edit</mat-icon>
                                    <span>Cập nhật thông tin</span>
                                </button>

                            </div>
                            <div>
                                <div fxLayout="row" fxLayout.xs="row" fxLayout.sm="row" class="formFieldItems"
                                    fxLayoutAlign="space-between">
                                    <mat-form-field appearance="outline" fxFlex.gt-md="49" fxFlex.gt-sm="49.5"
                                        fxFlex.gt-xs="49.5" fxFlex='grow'>
                                        <mat-label> Số giấy phép </mat-label>
                                        <input matInput formControlName="soGiayPhep" maxlength="500" readonly
                                            value="{{ELEMENTDATA?.SoGiayPhep}}">
                                    </mat-form-field>


                                    <mat-form-field appearance="outline" fxFlex.gt-md="49" fxFlex.gt-sm="49.5"
                                        fxFlex.gt-xs="49.5" fxFlex='grow'>
                                        <mat-label> Ngày lập giấy phép </mat-label>
                                        <input matInput formControlName="ngayLapGiayphep" maxlength="500" readonly
                                            value="{{NgayLapGiayPhep}}">
                                    </mat-form-field>
                                </div>
                            </div>

                            <div>
                                <div fxLayout="row" fxLayout.xs="row" fxLayout.sm="row" class="formFieldItems"
                                    fxLayoutAlign="space-between">
                                    <mat-form-field appearance="outline" fxFlex.gt-md="49" fxFlex.gt-sm="49.5"
                                        fxFlex.gt-xs="49.5" fxFlex='grow'>
                                        <mat-label> Thời hạn từ ngày </mat-label>
                                        <input matInput formControlName="thoiHanTuNgay" maxlength="500" readonly
                                            value="{{TuNgay}}">
                                    </mat-form-field>

                                    <mat-form-field appearance="outline" fxFlex.gt-md="49" fxFlex.gt-sm="49.5"
                                        fxFlex.gt-xs="49.5" fxFlex='grow'>
                                        <mat-label> Thời hạn đến ngày </mat-label>
                                        <input matInput formControlName="thoiHanDenNgay" maxlength="500" readonly
                                            value="{{DenNgay}}">
                                    </mat-form-field>
                                </div>
                            </div>


                            <div>
                                <div fxLayout="row" fxLayout.xs="row" fxLayout.sm="row" class="formFieldItems"
                                    fxLayoutAlign="space-between">
                                    <mat-form-field appearance="outline" fxFlex.gt-md="49" fxFlex.gt-sm="49.5"
                                        fxFlex.gt-xs="49.5" fxFlex='grow'>
                                        <mat-label> Cấp phép cho</mat-label>
                                        <input matInput formControlName="capPhep" maxlength="500" readonly
                                            value="{{ELEMENTDATA?.ThongTinGiayPhep?.CapCho}}">
                                    </mat-form-field>

                                    <mat-form-field appearance="outline" fxFlex.gt-md="49" fxFlex.gt-sm="49.5"
                                        fxFlex.gt-xs="49.5" fxFlex='grow'>
                                        <mat-label> Mã định danh </mat-label>
                                        <input matInput formControlName="maDinhDanh" maxlength="500" readonly
                                            value="{{ELEMENTDATA?.ThongTinGiayPhep?.SoDinhDanh}}">
                                    </mat-form-field>
                                </div>
                            </div>

                            <div>
                                <div fxLayout="row" fxLayout.xs="row" fxLayout.sm="row" class="formFieldItems"
                                    fxLayoutAlign="space-between">
                                    <mat-form-field appearance="outline" fxFlex.gt-md="49" fxFlex.gt-sm="49.5"
                                        fxFlex.gt-xs="49.5" fxFlex='grow'>
                                        <mat-label> Mã số thuế</mat-label>
                                        <input matInput formControlName="maSoThue" maxlength="500" readonly
                                            value="{{ELEMENTDATA?.ThongTinGiayPhep?.MaSoThue}}">
                                    </mat-form-field>

                                    <mat-form-field appearance="outline" fxFlex.gt-md="49" fxFlex.gt-sm="49.5"
                                        fxFlex.gt-xs="49.5" fxFlex='grow'>
                                        <mat-label> Địa chỉ </mat-label>
                                        <input matInput formControlName="diaChi" maxlength="500" readonly
                                            value="{{ELEMENTDATA?.ThongTinGiayPhep?.DiaChi}}">
                                    </mat-form-field>

                                </div>
                            </div>



                        </div>

                    </mat-tab>
                    <!-- end -->

                    <mat-tab *ngIf="canSyncLicenseBXD === 1 && isSyncDBNLGSPTanDanBXD === 1">
                        <ng-template mat-tab-label>
                            <mat-icon class="tabIcon">assignment_ind</mat-icon>
                            <span>Thông tin giấy phép</span>
                        </ng-template>
                        <div class="card" fxFlex='grow'>
                            <div class="cardTitle" fxLayoutAlign="space-between">
                                <span>Thông tin giấy phép</span>
                                <button mat-button class="cardAction" (click)="updateLicense()">
                                    <mat-icon>edit</mat-icon>
                                    <span>Cập nhật thông tin</span>
                                </button>
                            </div>
                            <div class="cardContent">
                                <div class="thoaiisolate">
                                    <formio [form]="licenseEForm.component" [submission]="licenseEForm.data" [renderOptions]="licenseEForm.renderOptions" [readOnly]="true" [viewOnly]="true">
                                    </formio>
                                </div>

                            </div>
                        </div>
                    </mat-tab>

                    <!-- ProcAdmin -->
                    <mat-tab *ngIf="showDossierProAdmins">
                        <ng-template mat-tab-label>
                            <mat-icon class="tabIcon">code_off</mat-icon>
                            <span i18n>Mã số được cấp</span>
                        </ng-template>
                        <div class="card" fxFlex='grow'>
                            <div class="cardContent">
                                <div class="procAdmin" fxLayout="row" fxLayout.xs="column" fxLayout.sm="column">
                                    <mat-table [dataSource]="proAdminDatasource" fxFlex='grow' [ngClass]="{'min-height-20': proAdminDatasource.data.length > 0}">
                                        <ng-container matColumnDef="stt">
                                            <mat-header-cell *matHeaderCellDef i18n>STT</mat-header-cell>
                                            <mat-cell *matCellDef="let row" i18n-data-label data-label="STT">
                                                {{row.stt}}
                                            </mat-cell>
                                        </ng-container>

                                        <ng-container matColumnDef="code">
                                            <mat-header-cell *matHeaderCellDef>
                                                <span class="iconFilter">
                                                    <span (click)="showProcFilter(1)">
                                                        <span class="material-icons-outlined">filter_alt</span>
                                                        <span i18n>Mã số</span>
                                                    </span>
                                                    <div class="searchContainer" [hidden]="!showProAdminFilter.code">
                                                        <form [formGroup]="procAdminForm" (submit)="proAdminConfirm()">
                                                            <div fxLayout="row" fxLayout.xs="row" fxLayout.sm="row">
                                                                <mat-form-field appearance="outline" fxFlex='grow'>
                                                                    <mat-label>Nhập mã số</mat-label>
                                                                    <input type="text" (keydown.enter)="proAdminConfirm()" matInput formControlName="code" maxlength="500">
                                                                </mat-form-field>
                                                            </div>
                                                            <div fxLayout="row" fxLayoutAlign="center" fxLayout.xs="row" fxLayout.sm="row" class="divAddBtn">
                                                                <button mat-flat-button fxFlex='grow' class="searchBtn" type="submit">
                                                                    <span>Áp dụng</span>
                                                                </button>
                                                            </div>
                                                        </form>
                                                    </div>
                                                </span>
                                            </mat-header-cell>
                                            <mat-cell *matCellDef="let row" i18n-data-label data-label="Mã số">
                                                <div class="outlinedContent">
                                                    <span class="breakAll">{{row.code}}</span>
                                                </div>
                                            </mat-cell>
                                        </ng-container>

                                        <ng-container matColumnDef="subject">
                                            <mat-header-cell *matHeaderCellDef>
                                                <span class="iconFilter">
                                                    <span (click)="showProcFilter(2)">
                                                        <span class="material-icons-outlined">filter_alt</span>
                                                        <span i18n>Trích yếu</span>
                                                    </span>
                                                    <div class="searchContainer" [hidden]="!showProAdminFilter.subject">
                                                        <form [formGroup]="procAdminForm" (submit)="proAdminConfirm()">
                                                            <div fxLayout="row" fxLayout.xs="row" fxLayout.sm="row">
                                                                <mat-form-field appearance="outline" fxFlex='grow'>
                                                                    <mat-label>Nhập trích yếu</mat-label>
                                                                    <textarea matInput type="text" (keydown.enter)="proAdminConfirm()" matInput formControlName="subject" maxlength="500"></textarea>
                                                                </mat-form-field>
                                                            </div>
                                                            <div fxLayout="row" fxLayoutAlign="center" fxLayout.xs="row" fxLayout.sm="row" class="divAddBtn">
                                                                <button mat-flat-button fxFlex='grow' class="searchBtn" type="submit">
                                                                    <span>Áp dụng</span>
                                                                </button>
                                                            </div>
                                                        </form>
                                                    </div>
                                                </span>
                                                </mat-header-cell>
                                            <mat-cell *matCellDef="let row" i18n-data-label data-label="Trích yếu">
                                                <div class="outlinedContent">
                                                    <span>{{row.subject}}</span>
                                                </div>
                                            </mat-cell>
                                        </ng-container>

                                        <ng-container matColumnDef="sign">
                                            <mat-header-cell *matHeaderCellDef>
                                                <span class="iconFilter">
                                                    <span (click)="showProcFilter(3)">
                                                        <span class="material-icons-outlined">filter_alt</span>
                                                        <span i18n>Người ký</span>
                                                    </span>
                                                    <div class="searchContainer checkContainer" [hidden]="!showProAdminFilter.sign">
                                                        <form [formGroup]="procAdminForm" (submit)="proAdminConfirm()">
                                                            <div class="title" fxLayout="row" fxLayout.xs="row" fxLayout.sm="row">
                                                                <span>Danh sách cán bộ</span>
                                                            </div>
                                                            <div class="listSigner">
                                                                <mat-checkbox [checked]="checkAllSigner" (change)="checkAllItem($event)">Tất cả</mat-checkbox>
                                                                <div *ngFor="let signer of listSigner">
                                                                    <mat-checkbox  (change)="checkItem($event, signer.id)" [checked]="signer.checked">{{signer.name}}</mat-checkbox>
                                                                </div>
                                                            </div>
                                                            <div fxLayout="row" fxLayoutAlign="center" fxLayout.xs="row" fxLayout.sm="row" class="divAddBtn">
                                                                <button mat-flat-button fxFlex='grow' class="searchBtn" type="submit">
                                                                    <span>Áp dụng</span>
                                                                </button>
                                                            </div>
                                                        </form>
                                                    </div>
                                                </span>
                                                </mat-header-cell>
                                            <mat-cell *matCellDef="let row"  i18n-data-label data-label="Người ký">
                                                <div class="outlinedContent">
                                                    <span>{{row.sign?.name}}</span>
                                                    <!-- <span>Nguyễn Ngọc Nam</span> -->
                                                </div>
                                            </mat-cell>
                                        </ng-container>

                                        <ng-container matColumnDef="files">
                                            <mat-header-cell *matHeaderCellDef i18n="@@procFiles">Tập tin dự thảo</mat-header-cell>
                                            <mat-cell *matCellDef="let row" i18n-data-label data-label="Tập tin dự thảo">
                                                <div class="listUploadedFile" fxFlex.gt-xs="grow" fxFlex='grow' *ngIf="row.files?.length > 0">
                                                    <div class="wrapList" fxLayout.xs="row" fxLayout.sm="row" fxLayout="row" fxLayoutAlign="space-evenly">
                                                        <div class="file" fxFlex='grow' *ngFor="let detailFile of row.files; let f = index;">
                                                            <div class="icon" [ngStyle]="{'background-image': 'url(' + config.cloudStaticURL + 'icon/files/512x512/' + detailFile.type + '.png)'}"></div>
                                                            <div class="name">
                                                                {{detailFile.name ? detailFile.name: detailFile.name}}
                                                            </div>

                                                            <button mat-icon-button class="deleteFile" [matMenuTriggerFor]="actionMenu">
                                                                <mat-icon>more_horiz</mat-icon>
                                                              </button>
                                                              <mat-menu #actionMenu="matMenu" xPosition="before">
                                                                  <button mat-menu-item class="menuAction" (click)="downloadFile(detailFile.id, detailFile.name)">
                                                                    <mat-icon>cloud_download</mat-icon>
                                                                    <span i18n>Tải xuống tệp tin</span>
                                                                   </button>
                                                            </mat-menu>
                                                        </div>
                                                    </div>
                                                </div>
                                            </mat-cell>
                                        </ng-container>

                                        <ng-container matColumnDef="resultFiles">
                                            <mat-header-cell *matHeaderCellDef i18n>Tập tin kết quả</mat-header-cell>
                                            <mat-cell *matCellDef="let row" i18n-data-label data-label="Tập tin kết quả">
                                                <div class="listUploadedFile" fxFlex.gt-xs="grow" fxFlex='grow' *ngIf="row.resultFiles?.length > 0">
                                                    <div class="wrapList" fxLayout.xs="row" fxLayout.sm="row" fxLayout="row" fxLayoutAlign="space-evenly">
                                                        <div class="file" fxFlex='grow' *ngFor="let detailFile of row.resultFiles; let f = index;">
                                                            <div class="icon" [ngStyle]="{'background-image': 'url(' + config.cloudStaticURL + 'icon/files/512x512/' + detailFile.type + '.png)'}"></div>
                                                            <div class="name">
                                                                {{detailFile.name ? detailFile.name: detailFile.name}}
                                                            </div>

                                                            <button mat-icon-button class="deleteFile" [matMenuTriggerFor]="actionMenu">
                                                                <mat-icon>more_horiz</mat-icon>
                                                              </button>
                                                              <mat-menu #actionMenu="matMenu" xPosition="before">
                                                                  <button mat-menu-item class="menuAction" (click)="downloadFile(detailFile.id, detailFile.name)">
                                                                    <mat-icon>cloud_download</mat-icon>
                                                                    <span i18n>Tải xuống tệp tin</span>
                                                                   </button>
                                                            </mat-menu>
                                                        </div>
                                                    </div>
                                                </div>
                                            </mat-cell>
                                        </ng-container>
                                        <mat-header-row *matHeaderRowDef="proAdminDisplayedColumns; sticky: true"  ></mat-header-row>
                                        <mat-row *matRowDef="let row; columns: proAdminDisplayedColumns;"></mat-row>
                                    </mat-table>
                                    <div class="empty" fxLayout="row" fxLayoutAlign="center" *ngIf="proAdminDatasource.data.length == 0">
                                        <div fxFlex="grow" style="text-align: center; font-size: 18px">Không có kết quả tìm kiếm</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </mat-tab>

                    <!-- Hồ sơ kiosk -->
                    <mat-tab *ngIf="receivingOfKiosk && !!dossierDetail[0]?.applyMethod && dossierDetail[0]?.applyMethod?.id ==4 ">
                        <ng-template mat-tab-label>
                            <mat-icon class="tabIcon">how_to_reg</mat-icon>
                            <span>Thông tin xác minh</span>
                        </ng-template>
                        <div class="">
                          <div style="padding: 15px 0;font-weight: 600;">
                            <span>Ảnh chụp người nộp hồ sơ từ máy Kiosk</span>
                          </div>
                          <div class="viewPhotoKiosk" *ngIf="!!dossierDetail[0]?.fileCitizen && dossierDetail[0]?.fileCitizen?.length>0">
                            <div class="fileCitizen" *ngFor="let detailFile of dossierDetail[0].fileCitizen; let f = index;">
                              <div style="cursor: pointer" class="icon" [ngStyle]="{'background-image': 'url(' + config.cloudStaticURL + '/icon/files/512x512/png.png)'}" (click)="viewFile(detailFile.id)"></div>
                              <div style="cursor: pointer" class="name"
                                  (click)="viewFile(detailFile.id)">
                                  {{detailFile.filename ? detailFile.filename : detailFile.name}}
                              </div>
                              <div style="cursor: pointer" class="icon" (click)="downloadFile(detailFile.id,detailFile.filename ? detailFile.filename : detailFile.name)" ><mat-icon>download</mat-icon></div>
                            </div>
                          </div>
                        </div>
                    </mat-tab>
                    
                    <!-- 5343 -->
                    <mat-tab *ngIf="transferPaperId == transferPaperDocumentsPostalId">
                        <ng-template mat-tab-label>
                            <mat-icon class="tabIcon">verified</mat-icon>
                            <span>Hình thức chuyển hồ sơ giấy</span>
                        </ng-template>
                        <div class="card" fxFlex='grow'>
                            <div class="cardTitle" fxLayoutAlign="space-between">
                                <span>Hình thức chuyển hồ sơ giấy</span>
                                <div class="cardAction receiveDone">
                                    <mat-spinner diameter="25"></mat-spinner>
                                    <div class="done ">
                                        <mat-icon>check_circle_outline</mat-icon>
                                        <span i18n>Đã lưu</span>
                                    </div>
                                </div>
                            </div>
                            <div class="cardContent">
                                <form [formGroup]="receiveForm" class="receiveForm formFieldOutline">
                                    <div fxLayout="row" fxLayout.xs="row" fxLayout.sm="row" class="formFieldItems" fxLayoutAlign="space-between"
                                        *ngIf="dossierReceivingKind != (env?.vnpost?.receiveResultsByAddress != undefined ? env?.vnpost?.receiveResultsByAddress : config.receiveResultsByAddress)">
                                        <mat-form-field appearance="outline" fxFlex.gt-sm="50" fxFlex.gt-xs="grow" fxFlex='grow'>
                                            <mat-label>Hình thức chuyển hồ sơ giấy</mat-label>
                                            <mat-select [disabled]="true" formControlName="transferPaper"
                                                (selectionChange)="transferPaperChange($event)" required>
                                                <mat-option *ngFor="let kind of listTransferPaper" value="{{kind.id}}">
                                                    {{kind.name}}
                                                    <span *ngIf="kind.name == undefined || kind.name == null || kind.name.trim() == ''">(Không tìm
                                                        thấy bản
                                                        dịch)</span>
                                                </mat-option>
                                            </mat-select>
                                        </mat-form-field>
                                    </div>
                                </form>
                                <div fxLayout="row" fxLayout.xs="row" fxLayout.sm="row" class="formFieldItems" fxLayoutAlign="space-between"
                                    *ngIf="transferPaperId == transferPaperDocumentsPostalId">
                                    <div class="bordered-container">
                                        <div class="label-text">Nơi gửi</div>
                                        <form [formGroup]="tabAdministrativeCenterForm2" novalidate class="formFieldOutline">
                                            <div fxLayout="row" fxLayout.xs="row" fxLayout.sm="row" class="formFieldItems"
                                                fxLayoutAlign="space-between">
                                                <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                                                    <mat-label>Tên</mat-label>
                                                    <input type="text"  matInput formControlName="rcName" maxlength="500">
                                                </mat-form-field>
                        
                                                <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                                                    <mat-label>Số điện thoại</mat-label>
                                                    <input type="text"  matInput formControlName="rcPhoneNumber" [maxlength]="numberOfPhoneVNPostCharacter">
                                                </mat-form-field>
                        
                                                <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                                                    <mat-label>Email</mat-label>
                                                    <input type="email"  matInput formControlName="rcEmail" maxlength="500">
                                                </mat-form-field>
                        
                                                <div class="groupbox" fxLayout="row wrap" fxFlex.gt-sm="100" fxFlex="grow"
                                                    fxLayoutAlign="space-between">
                                                    <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                                                        <mat-label>Tỉnh/TP</mat-label>
                                                        <input type="email"  matInput formControlName="rcProvince">
                                                        <!-- <mat-select required formControlName="rcProvince"
                                                            (selectionChange)="rcProvinceChange()">
                                                            <mat-option *ngFor='let provinceOpt of listProvinceForRC;'
                                                                value="{{provinceOpt.id}}">
                                                                {{provinceOpt.name}} </mat-option>
                                                        </mat-select> -->
                                                    </mat-form-field>
                        
                                                    <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                                                        <mat-label>Phường/xã</mat-label>
                                                        <input type="email"  matInput formControlName="rcDistrict">
                                                        <!-- <mat-select required formControlName="rcDistrict" (selectionChange)="rcDistrictChange()"
                                                            [disabled]="this.tabAdministrativeCenterForm2.get('rcProvince').value == '' ">
                                                            <mat-option *ngFor='let districtOpt of listDistrictForRC;'
                                                                value="{{districtOpt.id}}">
                                                                {{districtOpt.name}} </mat-option>
                                                        </mat-select> -->
                                                    </mat-form-field>
                        
                                                    <!-- <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                                                        <mat-label>Phường/xã</mat-label>
                                                        <input type="email"  matInput formControlName="rcVillage"> -->
                                                        <!-- <mat-select required formControlName="rcVillage"
                                                            [disabled]="this.tabAdministrativeCenterForm2.get('rcDistrict').value == '' ">
                                                            <mat-option *ngFor='let wardtOpt of listVillageForRC;' value="{{wardtOpt.id}}">
                                                                {{wardtOpt.name}}
                                                            </mat-option>
                                                        </mat-select> -->
                                                    <!-- </mat-form-field> -->
                        
                                                    <mat-form-field appearance="outline" fxFlex.gt-sm="100" fxFlex.gt-xs="grow" fxFlex='grow'>
                                                        <mat-label>Địa chỉ chi tiết</mat-label>
                                                        <input type="text"  matInput formControlName="customAddress">
                                                    </mat-form-field>
                                                </div>
                                            </div>
                                        </form>
                                    </div>
                                    <div class="bordered-container">
                                        <div class="label-text">Nơi nhận</div>
                                        <form [formGroup]="tabSpecializedAgencyForm2" novalidate class="formFieldOutline">
                                            <div fxLayout="row" fxLayout.xs="row" fxLayout.sm="row" class="formFieldItems"
                                                fxLayoutAlign="space-between">
                                                <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                                                    <mat-label>Tên</mat-label>
                                                    <input type="text"  matInput formControlName="rcName" maxlength="500">
                                                </mat-form-field>
                        
                                                <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                                                    <mat-label>Số điện thoại</mat-label>
                                                    <input type="text"  matInput formControlName="rcPhoneNumber" [maxlength]="numberOfPhoneVNPostCharacter">
                                                </mat-form-field>
                        
                                                <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                                                    <mat-label>Email</mat-label>
                                                    <input type="email"  matInput formControlName="rcEmail" maxlength="500">
                                                </mat-form-field>
                        
                                                <div class="groupbox" fxLayout="row wrap" fxFlex.gt-sm="100" fxFlex="grow"
                                                    fxLayoutAlign="space-between">
                                                    <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                                                        <mat-label>Tỉnh/TP</mat-label>
                                                        <input type="rcProvince"  matInput formControlName="rcProvince">
                                                        <!-- <mat-select required formControlName="rcProvince"
                                                            (selectionChange)="rcProvinceChange()">
                                                            <mat-option *ngFor='let provinceOpt of listProvinceForRC;'
                                                                value="{{provinceOpt.id}}">
                                                                {{provinceOpt.name}} </mat-option>
                                                        </mat-select> -->
                                                    </mat-form-field>
                        
                                                    <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                                                        <mat-label>Phường/xã</mat-label>
                                                        <input type="rcDistrict"  matInput formControlName="rcDistrict">
                                                        <!-- <mat-select required formControlName="rcDistrict" (selectionChange)="rcDistrictChange()"
                                                            [disabled]="this.tabSpecializedAgencyForm2.get('rcProvince').value == '' ">
                                                            <mat-option *ngFor='let districtOpt of listDistrictForRC;'
                                                                value="{{districtOpt.id}}">
                                                                {{districtOpt.name}} </mat-option>
                                                        </mat-select> -->
                                                    </mat-form-field>
                        
                                                    <!-- <mat-form-field appearance="outline" fxFlex.gt-sm="32.5" fxFlex.gt-xs="49.5" fxFlex='grow'>
                                                        <mat-label>Phường/xã</mat-label>
                                                        <input type="rcVillage"  matInput formControlName="rcVillage"> -->
                                                        <!-- <mat-select required formControlName="rcVillage"
                                                            [disabled]="this.tabSpecializedAgencyForm2.get('rcDistrict').value == '' ">
                                                            <mat-option *ngFor='let wardtOpt of listVillageForRC;' value="{{wardtOpt.id}}">
                                                                {{wardtOpt.name}}
                                                            </mat-option>
                                                        </mat-select> -->
                                                    <!-- </mat-form-field> -->
                        
                                                    <mat-form-field appearance="outline" fxFlex.gt-sm="100" fxFlex.gt-xs="grow" fxFlex='grow'>
                                                        <mat-label>Địa chỉ chi tiết</mat-label>
                                                        <input type="text"  matInput formControlName="customAddress">
                                                    </mat-form-field>
                                                </div>
                                            </div>
                                        </form>
                                    </div>
                                    <div style="width: 100%">
                                        <form [formGroup]="feeForm" class="cardContent cardContent5343">
                                          <div  *ngIf="vnpostTransferPaperFeeDataSource?.data != ''">
                                            <div class="fee_head">
                                              <div class="s_head mb-1">
                                                <span>Thông tin phí chuyển hồ sơ</span>
                                              </div>
                                              <!-- <div class="s_head mb-1">
                                                <div class='payment-status-vnpost'>(Hiện tại hệ thống chỉ áp dụng thu phí,
                                                  lệ phí hồ sơ. *Lưu ý: Đối với phí dịch vụ bưu chính (nếu có) người dân vui lòng thanh toán trực tiếp cho
                                                  nhân viên bưu chính khi nhận kết quả tại nhà)</div>
                                              </div> -->
                                            </div>
                                            <div class="procedureFee" fxLayout="column" fxLayout.xs="column" fxLayout.sm="column">
                                              <mat-table [dataSource]="vnpostTransferPaperFeeDataSource" fxFlex='grow' class="tblFee">
                                                <!-- <ng-container matColumnDef="required">
                                                  <mat-header-cell *matHeaderCellDef class="checkAllProcedureAdd">
                                                    Bắt buộc thanh toán
                                                  </mat-header-cell>
                                                  <mat-cell *matCellDef="let row; let i = index;" data-label="Chọn" class="checkAllProcedureAdd" i18n-data-label>
                                                    <mat-checkbox [checked]="row?.checked">
                                                    </mat-checkbox>
                                                  </mat-cell>
                                                  <mat-footer-cell *matFooterCellDef></mat-footer-cell>
                                                </ng-container> -->
                                                <ng-container matColumnDef="procostType">
                                                  <mat-header-cell *matHeaderCellDef i18n>Loại lệ phí</mat-header-cell>
                                                  <mat-cell *matCellDef="let row" i18n-data-label data-label="Loại lệ phí"> {{row?.typeName}}
                                                  </mat-cell>
                                                  <mat-footer-cell *matFooterCellDef>Tổng</mat-footer-cell>
                                                </ng-container>
                    
                                                <ng-container matColumnDef="quantity">
                                                  <mat-header-cell *matHeaderCellDef i18n>Số lượng</mat-header-cell>
                                                  <mat-cell *matCellDef="let row; let i = index" i18n-data-label data-label="Số lượng">
                                                    <span *ngIf="editFeeIndex !==i">{{row?.quantity}}</span>
                                                  </mat-cell>
                                                  <mat-footer-cell *matFooterCellDef></mat-footer-cell>
                                                </ng-container>
                    
                                                <ng-container matColumnDef="cost">
                                                  <mat-header-cell *matHeaderCellDef i18n>Mức lệ phí</mat-header-cell>
                                                  <mat-cell *matCellDef="let row; let i = index" i18n-data-label data-label="Mức lệ phí">
                                                      <span *ngIf="editFeeIndex!==i">{{row?.cost|number}} {{row?.monetaryUnit}}</span>
                                                      <!-- <mat-select (selectionChange)="changeCount(row?.stt, $event)" *ngIf="editFeeIndex === i"
                                                                  fxFlex.gt-sm="150" fxFlex.gt-xs="150" value="{{row?.costCaseIndex}}">
                                                          <mat-option *ngFor='let cost of row?.costCase; let j = index;' title="{{cost.cost | number}} {{row?.monetaryUnit}} ({{cost.description}})"
                                                                      value="{{j}}">
                                                              {{cost.cost | number}} {{row?.monetaryUnit}} ({{cost.description}})
                                                          </mat-option>
                                                      </mat-select> -->
                                                  </mat-cell>
                                                  <mat-footer-cell *matFooterCellDef></mat-footer-cell>
                                                </ng-container>
                    
                                                <ng-container matColumnDef="amount">
                                                  <mat-header-cell *matHeaderCellDef i18n>Thành tiền</mat-header-cell>
                                                  <mat-cell *matCellDef="let row" i18n-data-label data-label="Thành tiền">
                                                    {{row?.quantity*row?.cost|number}}&nbsp;{{row?.monetaryUnit}}
                                                  </mat-cell>
                                                  <mat-footer-cell *matFooterCellDef="let row; let i = index" class="totalCell">{{vnpostTransferPaperFeeDataSourceTotal}}</mat-footer-cell>
                                                </ng-container>
                    
                                                <ng-container matColumnDef="description">
                                                  <mat-header-cell *matHeaderCellDef i18n>Mô tả</mat-header-cell>
                                                  <mat-cell *matCellDef="let row" i18n-data-label data-label="Mô tả"> {{row?.description}}
                                                  </mat-cell>
                                                  <mat-footer-cell *matFooterCellDef class="payment-status-vnpost" i18n="@@vnPostDescriptionTotal">Nhân viên bưu chính sẽ thu phí trực tiếp tại nhà khi thu hồ sơ/trả kết quả</mat-footer-cell>
                                                </ng-container>
                                                <ng-container matColumnDef="pay">
                                                  <mat-header-cell *matHeaderCellDef i18n>Đã thanh toán</mat-header-cell>
                                                  <mat-cell *matCellDef="let row; let i = index" data-label="Thanh toán">
                                                    <input *ngIf="editFeeIndex!==i" type="checkbox" [checked]="row?.paid > 0" disabled>
                                                    <mat-checkbox *ngIf="editFeeIndex===i" formControlName="paided">
                                                    </mat-checkbox>
                                                  </mat-cell>
                                                  <mat-footer-cell *matFooterCellDef="let row; let i = index" class="totalCell">
                                                    {{vnpostTransferPaperFeeDataSourcePaided}}
                                                    <br>
                                                    (Còn lại: {{vnpostTransferPaperFeeDataSourceDebt}})
                                                  </mat-footer-cell>
                                                </ng-container>
                    
                                                <ng-container matColumnDef="action">
                                                  <mat-header-cell *matHeaderCellDef i18n>Thao tác</mat-header-cell>
                                                  <mat-cell *matCellDef="let row; let i = index" i18n-data-label data-label="Cập nhật" class="text-center">
                                                    <span *ngIf="editFeeIndex ===i">...</span>
                                                  </mat-cell>
                                                  <mat-footer-cell *matFooterCellDef></mat-footer-cell>
                                                </ng-container>
                    
                                                <mat-header-row *matHeaderRowDef="feeDisplayedColumns5343"></mat-header-row>
                                                <mat-row *matRowDef="let row; columns: feeDisplayedColumns5343;"></mat-row>
                                                <mat-footer-row *matFooterRowDef="feeDisplayedColumns5343" [ngClass]="{'hidden': hideTotal == true}">
                                                </mat-footer-row>
                                              </mat-table>
                                            </div>
                                          </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </mat-tab>
                  <!-- KHA OS-124494: Tab Xin ký kiến -->
                  <mat-tab *ngIf="checkPermitShowConsultationTabKHA == true">
                    <!--                  <mat-tab>-->
                    <ng-template mat-tab-label>
                      <mat-icon class="tabIcon">live_help</mat-icon>
                      <span>Xin ý kiến</span>
                    </ng-template>
                    <div class="card" fxFlex='grow'>
                      <div class="cardTitle" fxLayoutAlign="space-between">
                        <span>Xin ý kiến</span>

                        <button mat-button class="cardAction" (click)="addConsultation()" *ngIf="!offDossierDetailUpdate && isTheHandler && isTaskComplete(currentTask[0].activitiTask.status) == false">
                          <mat-icon>edit</mat-icon>
                          <span>Thêm đơn vị xin kiến</span>
                        </button>
                      </div>
                      <div class="cardContent">
                        <div class="procedureForm">
                          <form [formGroup]="consultationForm">
                            <table class="mat-table mat-table-form" style="width: 100%;">
                              <tr>
                                <th class="tablecolor1">STT</th>
                                <th class="tablecolor1">Đơn vị nhận thông tin</th>
                                <th class="tablecolor1">Chức vụ</th>
                                <th class="tablecolor1">Nội dung</th>
                                <th class="tablecolor1">Đính kèm giấy tờ</th>
                                <th class="tablecolor1">Người thực hiện</th>
                                <th class="tablecolor1">Trạng thái</th>
                                <th class="tablecolor1">Thao tác</th>
                              </tr>
                              <tr *ngFor="let consultation of consultationUnions" class="item">
                                <td class="center">
                                  <div class="body">{{consultation.stt}}</div>
                                </td>
                                <td>
                                  <div class="body">
                                    <!--                                    <mat-icon class="requirement">check_circle_outline</mat-icon>-->
                                    <span>
                                                                {{ consultation.agencyName }}
                                      <span *ngIf="consultation.agencyName == undefined || consultation.agencyName == null || consultation.agencyName.trim() == ''" i18n>(Không tìm thấy bản dịch)</span>
                                                            </span>
                                  </div>
                                </td>
                                <td>
                                  <div class="body">
                                    <span> {{ consultation.position }} </span>
                                  </div>
                                </td>
                                <td>
                                  <div class="body">
                                    <span>{{ consultation.summary }}</span>
                                  </div>
                                </td>
                                <td>
                                  <div class="body">
                                    <mat-radio-group formControlName="rdo_File">
                                      <div *ngFor="let fd of consultation.details; let j = index">
                                        <div *ngIf="fd.size > 0" class="rdo_File" fxLayout="row" fxLayout.xs="row" fxLayout.sm="row" fxLayoutAlign="space-between">
                                          <div class="listUploadedFile" fxFlex.gt-xs="grow" fxFlex='grow'>
                                            <div class="wrapList" fxLayout.xs="row" fxLayout.sm="row" fxLayout="row" fxLayoutAlign="space-evenly">
                                              <div class="file" fxFlex='grow'>
                                                <div class="icon" [ngStyle]="{'background-image': 'url('+ fd.icon +')'}">
                                                </div>
                                                <div class="name">
                                                  {{fd.filename}}
                                                </div>
                                                <div >
                                                  <a mat-icon-button class="moreBtn" [matMenuTriggerFor]="digitallySignedFileMenu">
                                                    <mat-icon>more_vert</mat-icon>
                                                  </a>
                                                  <mat-menu #digitallySignedFileMenu="matMenu">
                                                    <a mat-menu-item class="menuAction" target="_blank" [routerLink]="routerLinkConsultationFile(fd.filename, fd.id)">
                                                      <mat-icon>format_size</mat-icon>
                                                      <span i18n>Xem trước</span>
                                                    </a>
                                                    <button mat-menu-item class="menuAction" (click)="downloadFile(fd.id, fd.filename)">
                                                      <mat-icon>cloud_download</mat-icon>
                                                      <span i18n>Tải xuống tệp tin</span>
                                                    </button>
                                                  </mat-menu>
                                                </div>
                                              </div>
                                            </div>
                                          </div>
                                        </div>
                                      </div>
                                    </mat-radio-group>
                                  </div>
                                </td>
                                <td>
                                  <div class="body">
                                    <span> {{ consultation.assigneeName }} </span>
                                  </div>
                                </td>
                                <td>
                                  <div class="body">
                                    <span matTooltipPosition="center">{{ getStatusName(consultation.status) }}</span>
                                  </div>
                                </td>
                                <td>
                                  <div class="body">
                                    <button mat-flat-button class="btnRemove" (click)="deleteConsultation(consultation.id, consultation.agencyName)">
                                      <mat-icon>delete_outline</mat-icon>
                                      <span i18n>Xoá</span>
                                    </button>
                                  </div>
                                </td>
                              </tr>
                            </table>
                            <!--end-->
                          </form>
                        </div>
                      </div>
                    </div>
                  </mat-tab>

                </mat-tab-group>
            </div>
            <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="column" fxLayoutAlign="space-between" *ngIf="batLienThongCTDT && showGridResultCTDT && selectedProcess?.isCTDT">
              <div class="card" fxFlex='grow'>
                <div class="cardTitle">
                  <span>Danh sách giấy tờ chứng thực</span>
                  <!-- <button (click)="updateFeeChungThuc()" mat-button class="cardAction">
                    <mat-icon>edit</mat-icon>
                    <span>Xác nhận thực hiện nghĩa vụ tài chính</span>
                  </button> -->
                  <button (click)="saveFileToResultFile()" mat-button class="cardAction" *ngIf="showBtnSaveToResultFile">
                    <mat-icon>save_alt</mat-icon>
                    <span>Lưu file vào Kết quả xử lý</span>
                  </button>
                </div>
                <div class="cardContent infoTabs" style="height: auto">
                  <div class="procedureFee" fxLayout="row" fxLayout.xs="column" fxLayout.sm="column">
                    <mat-table [dataSource]="ctdtDataSource" fxFlex='grow'>
                        <ng-container matColumnDef="maGiayTo">
                            <mat-header-cell *matHeaderCellDef>Mã giấy tờ</mat-header-cell>
                            <mat-cell *matCellDef="let row" data-label="Mã giấy tờ">
                                {{row.maGiayTo}}
                            </mat-cell>
                            <mat-footer-cell *matFooterCellDef><b>Tổng</b></mat-footer-cell>
                        </ng-container>

                        <ng-container matColumnDef="tenGiayTo">
                            <mat-header-cell *matHeaderCellDef>Tên giấy tờ</mat-header-cell>
                            <mat-cell *matCellDef="let row" data-label="Tên giấy tờ">
                                {{row.tenGiayTo}}
                            </mat-cell>
                            <mat-footer-cell *matFooterCellDef></mat-footer-cell>
                        </ng-container>

                        <ng-container matColumnDef="soLuongBan" *ngIf="showCopies || enableShowCopyNumberElectronicAuthentication">
                            <mat-header-cell *matHeaderCellDef>Số lượng bản</mat-header-cell>
                            <mat-cell *matCellDef="let row" data-label="Số lượng bản">
                                {{row?.soLuongBan ? row.soLuongBan : (row?.soBan ? row.soBan : row?.soBanSao ) }}
                            </mat-cell>
                            <mat-footer-cell *matFooterCellDef></mat-footer-cell>
                        </ng-container>

                        <ng-container matColumnDef="soTrang">
                          <mat-header-cell *matHeaderCellDef>Số trang</mat-header-cell>
                          <mat-cell *matCellDef="let row" data-label="Số trang">
                              {{row.soTrang}}
                          </mat-cell>
                          <mat-footer-cell *matFooterCellDef></mat-footer-cell>
                      </ng-container>

                        <ng-container matColumnDef="typeName">
                            <mat-header-cell *matHeaderCellDef>Loại chứng thực</mat-header-cell>
                            <mat-cell *matCellDef="let row" data-label="Loại chứng thực">
                                {{row.typeName}}
                              </mat-cell>
                            <mat-footer-cell *matFooterCellDef></mat-footer-cell>
                        </ng-container>

                        <ng-container matColumnDef="soCT">
                            <mat-header-cell *matHeaderCellDef>Số chứng thực</mat-header-cell>
                            <mat-cell *matCellDef="let row" data-label="Số chứng thực">
                                {{row.soCT}}
                            </mat-cell>
                            <mat-footer-cell *matFooterCellDef></mat-footer-cell>
                        </ng-container>

                        <ng-container matColumnDef="quyenSo">
                            <mat-header-cell *matHeaderCellDef>Quyển sổ</mat-header-cell>
                            <mat-cell *matCellDef="let row" data-label="Quyển sổ">
                                {{row.quyenSo}}
                            </mat-cell>
                            <mat-footer-cell *matFooterCellDef></mat-footer-cell>
                        </ng-container>

                        <ng-container matColumnDef="thanhTien">
                            <mat-header-cell *matHeaderCellDef>Thành tiền</mat-header-cell>
                            <mat-cell *matCellDef="let row" data-label="Thành tiền">
                              {{row.thanhTien | number}} {{"VND"}}
                            </mat-cell>
                            <mat-footer-cell *matFooterCellDef class="totalCell">
                              {{(dataChungThuc?.phiLePhiTong ? dataChungThuc?.phiLePhiTong : 0) | number}} {{"VND"}}
                              <!-- <br>
                              <i style="color:#8f969c">{{dataChungThuc?.isPaid ? '(Đã thanh toán)' : '(Chưa thanh toán)'}}</i> -->
                            </mat-footer-cell>
                        </ng-container>

                        <ng-container matColumnDef="file">
                          <mat-header-cell *matHeaderCellDef>File</mat-header-cell>
                          <mat-cell *matCellDef="let row" data-label="File">
                            {{row.file}}
                            <div fxLayout="row" fxLayoutAlign="none center">
                              <mat-icon>picture_as_pdf</mat-icon>
                              <span (click)="downloadFileCTDT(row?.urlSign, row?.fileSignInfo)" class="file">
                                  {{row?.fileSignInfo?.filename ? row?.fileSignInfo?.filename : row?.urlSign}}
                              </span>
                          </div>
                          </mat-cell>
                          <mat-footer-cell *matFooterCellDef></mat-footer-cell>
                      </ng-container>

                        <ng-container matColumnDef="dongY">
                          <mat-header-cell *matHeaderCellDef>Đồng ý</mat-header-cell>
                          <mat-cell *matCellDef="let row" data-label="Đồng ý">
                              {{row.dongY}}
                          </mat-cell>
                          <mat-footer-cell *matFooterCellDef></mat-footer-cell>
                        </ng-container>

                        <ng-container matColumnDef="lyDo">
                          <mat-header-cell *matHeaderCellDef>Lý do</mat-header-cell>
                          <mat-cell *matCellDef="let row" data-label="Lý do">
                              {{row.lyDo}}
                          </mat-cell>
                          <mat-footer-cell *matFooterCellDef></mat-footer-cell>
                        </ng-container>

                        <mat-header-row *matHeaderRowDef="ctdtDisplayedColumns"></mat-header-row>
                        <mat-row *matRowDef="let row; columns: ctdtDisplayedColumns;"></mat-row>
                        <mat-footer-row *matFooterRowDef="ctdtDisplayedColumns">
                        </mat-footer-row>
                    </mat-table>
                  </div>
                </div>
              </div>
            </div>
            <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="column" fxLayoutAlign="space-between" class="resultFiles" *ngIf="!hiddenResultBox">
                <div class="card" fxFlex='49.5' *ngIf="!hideHandlingComments">
                    <div class="cardTitle">
                        <span *ngIf="qbhRenameYKXL == false" i18n>Ý kiến xử lý</span>
                        <span *ngIf="qbhRenameYKXL == true" >Tài liệu, hồ sơ trong quá trình xử lý</span>
                        <div class="cardAction fileAttachDone" fxLayout="row" fxLayoutAlign="space-between center">
                            <mat-spinner diameter="25"></mat-spinner>
                            <div class="done ">
                                <mat-icon>check_circle_outline</mat-icon>
                                <span i18n>Đã lưu</span>
                            </div>
                            <button mat-button class="cardAction" (click)="openHandlingCommentDialog()" *ngIf="isTheHandler == true && isTaskComplete(currentTask[0].activitiTask.status) == false">
                                <mat-icon>edit</mat-icon>
                                <span i18n>Nhập ý kiến xử lý</span>
                            </button>
                        </div>
                    </div>
                    <div class="cardContent">
                        <div class="drag_upload_btn">
                            <!-- <button mat-button *ngIf="isTheHandler && isTaskComplete(currentTask[0].activitiTask.status) == false">
                                <mat-icon>cloud_upload</mat-icon>
                                <span i18n>Kéo thả tệp tin hoặc</span><a href=""><span i18n> Tải lên</span></a>
                            </button>
                            <input id="img_upload" name="img_upload[]" type='file' (change)="onSelectFileAttach($event, 'fileAttachDone')" [accept]="acceptFileExtension" value="{{blankVal}}" *ngIf="isTheHandler && isTaskComplete(currentTask[0].activitiTask.status) == false"> -->
                            <div class="fileUploadPreview">
                                <div class="listUploaded" *ngFor="let attach of attachFilePreview; let i = index">
                                    <div class="fileInfo">
                                        <div class="fileIcon" [ngStyle]="{'background-image': 'url('+ attach.icon +')'}">
                                        </div>
                                        <div class="dGrid">
                                            <p class="fileName" matTooltip="" [matTooltipPosition]="'right'">
                                                {{attach.name}}</p>
                                            <p class="fileSize">{{bytesToSize(attach.size)}}</p>
                                        </div>
                                        <a mat-icon-button class="deleteFile" *ngIf="isTheHandler && isTaskComplete(currentTask[0].activitiTask.status) == false">
                                            <mat-icon (click)="confirmDeleteFileDialog(1, i, attach.id, attach.name, 'fileAttachDone')">close
                                            </mat-icon>
                                        </a>
                                    </div>
                                    <a mat-icon-button class="moreBtn" [matMenuTriggerFor]="digitallySignedFileMenu">
                                        <mat-icon>more_vert</mat-icon>
                                    </a>
                                    <mat-menu #digitallySignedFileMenu="matMenu">
                                        <a mat-menu-item class="menuAction" target="_blank" [routerLink]="routerLink(attach)">
                                            <mat-icon>format_size</mat-icon>
                                            <span i18n>Xem trước</span>
                                        </a>
                                        <button mat-menu-item class="menuAction" (click)="downloadFile(attach.id, attach.name)">
                                            <mat-icon>cloud_download</mat-icon>
                                            <span i18n>Tải xuống tệp tin</span>
                                        </button>
                                        <button mat-menu-item class="menuAction" (click)="openPdfDigitalSignatureV2(null, null, attach, 1, 1)" *ngIf="isTheHandler && isTaskComplete(currentTask[0].activitiTask.status) == false && digitalSignature.VNPTSim">
                                            <mat-icon>verified</mat-icon>
                                            <span i18n>Ký số sim</span>
                                        </button>
                                        <button mat-menu-item class="menuAction" (click)="openPdfDigitalSignature(1, i, attach.id, attach.name, 'fileAttachDone')" *ngIf="digitalSignature.SmartCA && checkIfFileIsSupported(attach.name)">
                                            <mat-icon>verified</mat-icon>
                                            <span>Ký số Smart CA</span>
                                        </button>
                                        <button mat-menu-item class="menuAction" (click)="openVGCAplugin(1, i, attach.id, attach.name, 'fileAttachDone', attach.size)" *ngIf="digitalSignature.VGCA && checkIfFileIsSupported(attach.name)">
                                            <mat-icon>verified</mat-icon>
                                            <span *ngIf="!isEditSignTokenName; then thenBlock else elseBlock"></span>
                                            <ng-template #thenBlock>Ký số Ban Cơ yếu</ng-template>
                                            <ng-template #elseBlock>Ký số Token</ng-template>
                                        </button>
                                        <button mat-menu-item class="menuAction" (click)="openVGCApluginIssued(1, i, attach.id, attach.name, 'fileAttachDone', attach.size)" *ngIf="!!allowVGCASignIssue">
                                            <mat-icon>verified</mat-icon>
                                            <span>Ký số văn thư Ban cơ yếu</span>
                                        </button>
                                        <button mat-menu-item class="menuAction" (click)="openVGCApluginCopy(1, i, attach.id, attach.name, 'fileAttachDone', attach.size)" *ngIf="!!isVgcaSignCopy">
                                            <mat-icon>verified</mat-icon>
                                            <span>{{vgcaSignLabel}}</span>
                                        </button>
                                        <button mat-menu-item class="menuAction" (click)="openVGCApluginIssued(1, i, attach.id, attach.name, 'fileAttachDone', attach.size)" *ngIf="digitalSignature.VGCA && checkIfFileIsSupported(attach.name) && checkSignTypeOrg()">
                                            <mat-icon>verified</mat-icon>
                                            <span>Ký số loại mẫu chữ ký tổ chức</span>
                                        </button>
                                        <button mat-menu-item class="menuAction" (click)="openVGCApluginCopyHCM(1, i, attach.id, attach.name, 'fileAttachDone', attach.size)" *ngIf="showSignCopyOrg == 1 && allowSignCopyOrg">
                                            <mat-icon>verified</mat-icon>
                                            <span>Ký số sao văn bản</span>
                                        </button>
                                        <button mat-menu-item class="menuAction" (click)="openVnptCaPlugin(null, null, attach, 1)" *ngIf="digitalSignature.VNPTCA && (checkIfFileIsSupported(attach.name) || checkIfDocFileOnly(attach.name))">
                                            <mat-icon>verified</mat-icon>
                                            <span>Ký số VNPT-CA</span>
                                        </button>
                                        <button mat-menu-item class="menuAction" (click)="openNEAC(null, null, attach, 1, 5)" *ngIf="digitalSignature.NEAC && checkIfFileIsSupported(attach.name)">
                                            <mat-icon class="mainColor">verified</mat-icon>
                                            <span i18n="@@digitalSignNEAC">Ký số NEAC</span>
                                        </button>
                                        <button mat-menu-item class="menuAction" (click)="openPdfDigitalSignatureV2(null, null, attach, 1, 6)" *ngIf="digitalSignature.QNM && checkIfFileIsSupported(attach.name)">
                                            <mat-icon class="mainColor">verified</mat-icon>
                                            <span i18n="@@QNMDigitalSignature">Ký số tập trung QNM</span>
                                        </button>
<!--                                        <button mat-menu-item class="menuAction" (click)="openHistory(attach.id)">-->
<!--                                            <mat-icon>refresh</mat-icon>-->
<!--                                            <span i18n="@@history">Xem lịch sử ký số</span>-->
<!--                                        </button>-->
                                      <button mat-menu-item class="menuAction">
                                        <view-sign-history [fileId]="attach.id">
                                          <mat-icon>refresh</mat-icon>
                                          <span>Xem lịch sử ký số</span>
                                        </view-sign-history>
                                      </button>
                                        <button mat-menu-item class="menuAction" (click)="transferToResult(attach, 'fileResultDone')" *ngIf="canTransferToResult">
                                            <mat-icon class="material-icons-outlined">shortcut</mat-icon>
                                            <span i18n="@@changeToResult">Chuyển thành file kết quả xử lý</span>
                                        </button>
                                    </mat-menu>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
                <div class="card" fxFlex= "{{hideHandlingComments ? '100' : '49.5'}}">
                    <div class="cardTitle">
                        <span *ngIf="!!isDigitizeOutput; then thenBlock else elseBlock" i18n></span>
                        <ng-template #thenBlock>Kết quả xử lý (số hóa đầu ra)</ng-template>
                        <ng-template #elseBlock>Kết quả xử lý</ng-template>
                        <div class="cardAction fileResultDone">
                            <mat-spinner diameter="25"></mat-spinner>
                            <div class="done ">
                                <mat-icon>check_circle_outline</mat-icon>
                                <span i18n>Đã lưu</span>
                            </div>
                        </div>
						<button mat-button class="cardAction" (click)="syncEdoc()" *ngIf="enableIoffice === true && canConnectedIOffice === 1">
                            <mat-icon>sync</mat-icon>
                            <span i18n="@@syncIoffice" *ngIf="!eOfficeLabelKHA">Đồng bộ VB từ iOffice</span>
                            <span *ngIf="eOfficeLabelKHA">Đồng bộ VB từ E-Office</span>
                        </button>
                        <button mat-button class="cardAction" (click)="getDocumentIoffice('fileResultDone')" *ngIf="!hiddenGetDocumentIoffice && isTheHandler == true && isTaskComplete(currentTask[0].activitiTask.status) == false && !iOfficeQni">
                            <mat-icon>cloud_download</mat-icon>
                            <span>{{ eOfficeLabelKHA ? 'Lấy VB từ E-Office' : 'Lấy VB từ iOffice' }}</span>
                        </button>
                        <button mat-button class="cardAction" (click)="getDocumentIofficeQni()" *ngIf="!hiddenGetDocumentIoffice && isTheHandler == true && isTaskComplete(currentTask[0].activitiTask.status) == false && iOfficeQni">
                            <mat-icon>cloud_download</mat-icon>
                            <span>Lấy VB từ iOffice</span>
                        </button>
                        <button mat-button class="cardAction" (click)="getDocumentIofficeQbh()" *ngIf="!hiddenGetDocumentIoffice && isTheHandler == true && isTaskComplete(currentTask[0].activitiTask.status) == false && iOfficeQbh">
                            <mat-icon>cloud_download</mat-icon>
                            <span>Lấy VB từ iOffice</span>
                        </button>
                        <button mat-button class="cardAction" (click)="getDocumentIofficeKtm()" *ngIf="isCheckConnectIofficeV5">
                            <mat-icon>cloud_download</mat-icon>
                            <span>Lấy VB từ iOfficeV5</span>
                        </button>
                        <button mat-button class="cardAction" (click)="getDocumentIofficeKGG()" *ngIf="isCheckConnectIofficeV5NotLogin">
                            <mat-icon>cloud_download</mat-icon>
                            <span>Lấy VB từ iOfficeV5</span>
                        </button>
                    </div>
                    <div class="cardContent" [ngStyle]="{ 'height': resultFilePreview.length > 5 ? 'auto' : '' }">
                        <btn-save-form-orgin (saveSuccess)="saveFileToStorageMarking($event)" #autoUploadFormogrin hidden></btn-save-form-orgin>
                        <div class="drag_upload_btn">
                            <div fxLayout="row" fxLayoutAlign="start center" fxLayoutGap="10px">
                                <button mat-button *ngIf="isTheHandler && isTaskComplete(currentTask[0].activitiTask.status) == false">
                                    <mat-icon>cloud_upload</mat-icon>
                                    <span i18n>Kéo thả tệp tin hoặc</span><a href=""><span i18n> Tải lên</span></a>
                                    <div>
                                        <span>Kích thước tối đa của một tệp tin:</span> {{ maxFileSize }}MB
                                    </div>
                                </button>
                                <input multiple id="img_upload" name="img_upload[]" type='file' (change)="onSelectFileResult($event, 'fileResultDone')" [accept]="acceptFileExtension" value="{{blankVal}}" *ngIf="isTheHandler && isTaskComplete(currentTask[0].activitiTask.status) == false">
                                <button mat-button class="ioffice_button" (click)="getDocumentIofficeHgi('fileResultDone')" *ngIf="ViewConnectEoffice && isTheHandler && isTaskComplete(currentTask[0].activitiTask.status) == false">
                                    <span>Thêm kết quả điện tử</span>
                                </button>
                            </div>
                            <div class="fileUploadPreview">
                                <div class="listUploaded" *ngFor="let result of resultFilePreview; let i = index">
                                    <div class="last-user" *ngIf="enableUserManipulateFileConfig && result?.lastUserAction">
                                        <p>{{result?.lastUserAction?.name}}</p>
                                    </div>
                                    <div class="fileInfo">
                                       
                                        <mat-icon *ngIf="enableShowSignIconResultFile && signedFiles?.has(result.id) && !enablePopupSignature" style="color: green !important; align-self: center; margin-left: .5em;">check_circle</mat-icon>
                                        <view-sign-history *ngIf="enablePopupSignature && signedFiles?.has(result?.id)" style="align-content: center; z-index: 1000;" [fileId]="result?.id"><mat-icon style="color: green !important; align-self: center; margin-left: .5em;cursor: pointer">check_circle</mat-icon></view-sign-history>

                                        <div class="fileIcon" [ngStyle]="{'background-image': 'url('+ result.icon +')'}">
                                        </div>
                                        <div class="dGrid">
                                            <p class="fileName" matTooltip="" [matTooltipPosition]="'right'">
                                                {{ result.name.length > (fileNameLength || 20) ? (result.name | slice:0:(fileNameLength || 20)) + '...' : result.name }}</p>
                                            <p class="fileSize">{{bytesToSize(result.size)}}</p>
                                        </div>
                                        <a mat-icon-button class="deleteFile" *ngIf="isTheHandler && isTaskComplete(currentTask[0].activitiTask.status) == false">
                                            <mat-icon (click)="confirmDeleteFileDialog(2, i, result.id, result.name, 'fileResultDone')">close
                                            </mat-icon>
                                        </a>
                                    </div>
                                    <a mat-icon-button *ngIf="digitalSignatureKGGMenu" class="signFileBtn" [matMenuTriggerFor]="digitallySignedFileKGGMenu_">
                                        <mat-icon>edit</mat-icon>
                                        <span>Ký số</span>
                                    </a>
                                    <a mat-icon-button class="moreBtn" [matMenuTriggerFor]="digitallySignedFileMenu">
                                        <mat-icon>more_vert</mat-icon>
                                    </a>
                                    <mat-menu #digitallySignedFileKGGMenu_="matMenu">
                                        <ng-container
                                            [ngTemplateOutlet]="signItemsMenu_"
                                            [ngTemplateOutletContext]="{ i: i, result: result}"
                                        >
                                        </ng-container>
                                    </mat-menu>
                                    <mat-menu #digitallySignedFileMenu="matMenu">
                                        <a mat-menu-item class="menuAction" target="_blank" [routerLink]="routerLink(result)">
                                            <mat-icon>format_size</mat-icon>
                                            <span i18n>Xem trước</span>
                                        </a>
                                        <button mat-menu-item class="menuAction" (click)="downloadFile(result.id, result.name)">
                                            <mat-icon>cloud_download</mat-icon>
                                            <span i18n>Tải xuống tệp tin</span>
                                        </button>
                                        <ng-container
                                            [ngTemplateOutlet]="signItemsMenu_"
                                            [ngTemplateOutletContext]="{ i: i, result: result}"
                                        >
                                        </ng-container>
                                        <ng-template #signItemsMenu_ let-form="form" let-fd="fd">
                                            <button mat-menu-item class="menuAction" (click)="openPdfDigitalSignatureV2(null, null, result, 2, 1)" *ngIf="isTheHandler && isTaskComplete(currentTask[0].activitiTask.status) == false && digitalSignature.VNPTSim">
                                                <mat-icon>verified</mat-icon>
                                                <span i18n>Ký số sim</span>
                                            </button>
                                            <button mat-menu-item class="menuAction" (click)="openPdfDigitalSignature(2, i, result.id, result.name, 'fileResultDone')" *ngIf="digitalSignature.SmartCA && checkIfFileIsSupported(result.name)">
                                                <mat-icon>verified</mat-icon>
                                                <span>Ký số Smart CA</span>
                                            </button>
                                            <button mat-menu-item class="menuAction" (click)="openVGCAplugin(2, i, result.id, result.name, 'fileResultDone', result.size)" *ngIf="digitalSignature.VGCA && checkIfFileIsSupported(result.name)">
                                                <mat-icon>verified</mat-icon>
                                                <span *ngIf="!isEditSignTokenName; then thenBlock else elseBlock"></span>
                                                <ng-template #thenBlock>Ký số Ban Cơ yếu</ng-template>
                                                <ng-template #elseBlock>Ký số Token</ng-template>
                                            </button>
                                            <button mat-menu-item class="menuAction" (click)="openVGCApluginIssued(2, i, result.id, result.name, 'fileResultDone', result.size)" *ngIf="!!allowVGCASignIssue">
                                                <mat-icon>verified</mat-icon>
                                                <span>Ký số văn thư Ban cơ yếu</span>
                                            </button>
                                            <button mat-menu-item class="menuAction" (click)="openVGCApluginCopyHCM(2, i, result.id, result.name, 'fileResultDone', result.size)" *ngIf="showSignCopyOrg == 1 && allowSignCopyOrg">
                                                <mat-icon>verified</mat-icon>
                                                <span>Ký số sao văn bản</span>
                                            </button>
                                            <button mat-menu-item class="menuAction" (click)="openVGCApluginCopy(2, i, result.id, result.name, 'fileResultDone', result.size)" *ngIf="!!isVgcaSignCopy">
                                                <mat-icon>verified</mat-icon>
                                                <span>{{vgcaSignLabel}}</span>
                                            </button>
                                            <button mat-menu-item class="menuAction" (click)="openVGCApluginIssued(2, i, result.id, result.name, 'fileResultDone', result.size)" *ngIf="digitalSignature.VGCA && checkIfFileIsSupported(result.name) && checkSignTypeOrg()">
                                                <mat-icon>verified</mat-icon>
                                                <span>Ký số loại mẫu chữ ký tổ chức</span>
                                            </button>
                                            <button mat-menu-item class="menuAction" (click)="openVnptCaPlugin(null, null, result, 2)" *ngIf="digitalSignature.VNPTCA && (checkIfFileIsSupported(result.name) || checkIfDocFileOnly(result.name))">
                                                <mat-icon>verified</mat-icon>
                                                <span>Ký số VNPT-CA</span>
                                            </button>
                                            <button mat-menu-item class="menuAction" (click)="openNEAC(null, null, result, 2, 5)" *ngIf="digitalSignature.NEAC && checkIfFileIsSupported(result.name)">
                                                <mat-icon class="mainColor">verified</mat-icon>
                                                <span i18n="@@digitalSignNEAC">Ký số NEAC</span>
                                            </button>
                                            <button mat-menu-item class="menuAction" (click)="openPdfDigitalSignatureV2(null, null, result, 2, 6)" *ngIf="digitalSignature.QNM && checkIfFileIsSupported(result.name)">
                                                <mat-icon class="mainColor">verified</mat-icon>
                                                <span i18n="@@QNMDigitalSignature">Ký số tập trung QNM</span>
                                            </button>
    <!--                                        <button mat-menu-item class="menuAction" (click)="openHistory(result.id)">-->
    <!--                                            <mat-icon>refresh</mat-icon>-->
    <!--                                            <span i18n="@@history">Xem lịch sử ký số</span>-->
    <!--                                        </button>-->
                                            <!-- <button mat-menu-item class="menuAction" (click)="openVGCAChungThucDienTu(2, i, result.id, result.name, 'fileResultDone', result.size)" *ngIf="digitalSignature.VGCA && checkIfFileIsSupported(result.name)">
                                                <mat-icon>verified</mat-icon>
                                                <span>Ký số chứng thực điện tử</span>
                                            </button> -->
                                            <button mat-menu-item class="menuAction" (click)="stampFormDialog(2, i, result.id, result.name, 'fileResultDone', result.size)" *ngIf="isLedgerAuth && checkIfFileIsSupported(result.name)">
                                                <mat-icon>verified</mat-icon>
                                                <span>Ký số chứng thực điện tử</span>
                                            </button>
                                            <button mat-menu-item class="menuAction">
                                                <view-sign-history [fileId]="result.id">
                                                <mat-icon>refresh</mat-icon>
                                                <span>Xem lịch sử ký số</span>
                                                </view-sign-history>
                                            </button>
                                        </ng-template>
                                        <button mat-menu-item class="menuAction" (click)="converPaper(result.id, result.name, dossierDetail[0]?.code)"  *ngIf="convertPrintPaper">
                                            <mat-icon class="mainColor">swap_horiz</mat-icon>
                                            <span i18n>Chuyển đổi bảng giấy</span>
                                        </button>
                                        <btn-save-form-orgin [getFormOrgin]="getFormOrgin"
                                                             [procedureId]="procedureId"
                                                             [files]="resultFilePreview"
                                                             filenameKey="name"
                                                             [fileId]="result.id"
                                                             btnClass="menuAction"
                                                             [dossierCode]="dossierDetail[0]?.code"
                                                             [storage468]="storage468"
                                                             isOutput="true"
                                                             (saveSuccess)="saveFileToStorageMarking($event)">
                                        </btn-save-form-orgin>
                                        <button mat-menu-item class="menuAction" (click)="openAddTextPDF(result)" *ngIf="assignedCodeToResultDossier && assignedCodeToResultDossier.enable">
                                          <mat-icon class="mainColor">verified</mat-icon>
                                          <span>Cấp số ban hành cho văn bản</span>
                                      </button>
                                      <button mat-menu-item class="menuAction" (click)="converToPaper(result, dossierDetail[0]?.code)"  *ngIf="digitalToPaper?.enable">
                                        <mat-icon class="mainColor">swap_horiz</mat-icon>
                                        <span>Chuyển đổi bản giấy</span>
                                    </button>
                                    </mat-menu>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="column" fxLayoutAlign="space-between" class="commentBlock">
                <div class="card" fxFlex='grow'>
                    <div class="cardTitle">
                        <span i18n>Hoạt động</span>
                    </div>
                    <div class="cardContent">
                        <mat-tab-group dynamicHeight>
                            <mat-tab>
                                <ng-template mat-tab-label>
                                    <mat-icon class="tabIcon">account_tree</mat-icon>
                                    <span i18n>Nội dung xử lý</span>
                                </ng-template>
                                <!-- <div class="commentEditor" fxLayout="column" fxLayout.xs="column" fxLayout.sm="column" *ngIf="isTheHandler && isTaskComplete(currentTask[0].activitiTask.status) == false">
                                    <span class="editorLabel" fxFlex='grow' i18n>Nội dung xử lý</span>
                                    <ckeditor [editor]="Editor" class="customCKEditor" (change)="onCommentEditorChange($event)" fxFlex='grow' [config]='commentConfig' [(ngModel)]="commentContent" [ngModelOptions]="{standalone: true}">
                                    </ckeditor>
                                    <div class="cmtControl" fxFlex='grow'>
                                        <button mat-button class="btnSave" (click)="postComment()" i18n>Lưu</button>
                                        <button mat-button (click)="cancelComment()" i18n>Huỷ</button>
                                    </div>
                                    <mat-menu #menuPermission="matMenu">
                                        <button mat-menu-item i18n>Tất cả</button>
                                        <button mat-menu-item i18n>Cán bộ</button>
                                        <button mat-menu-item i18n>Công dân</button>
                                    </mat-menu>
                                    <div class="errorMsg" *ngIf="isCKMaxlenght">
                                        <span i18n>Nội dung không quá 500 ký tự</span>
                                        <div class="err">
                                            <mat-icon>priority_high</mat-icon>
                                        </div>
                                    </div>
                                </div> -->
                                <div class="listComment" infinite-scroll [infiniteScrollDistance]="2" [infiniteScrollThrottle]="50" [scrollWindow]="false" (scrolled)="onScrollListComment()">
                                    <div class="items" fxLayout="row" fxLayout.xs="row" fxLayout.sm="row" *ngFor="let cmt of listComment">
                                        <div class="icon">
                                            <mat-icon>insert_comment</mat-icon>
                                        </div>
                                        <div class="mainCmt">
                                            <div class="sender">
                                                <span class="name">{{cmt.user.fullname}}</span>
                                                <span class="time"><span i18n>đã nhập ý kiến xử lý</span> - {{cmt.createdDate | date : 'dd/MM/yyyy HH:mm:ss'}}</span>
                                            </div>
                                            <div fxLayout="row" fxLayoutAlign="none center">
                                                <div class="title">
                                                    <p i18n>Nội dung xử lý: </p>
                                                </div>
                                                <div class="content" [innerHTML]="cmt.content.trim()"></div>
                                            </div>
                                            <div fxLayout="row" fxLayoutAlign="none center" *ngIf="isApplyShowInforDraft">
                                                <div class="content" [innerHTML]="cmt?.inforDraft.trim()"></div>
                                            </div>
                                            <div *ngIf="cmt?.file?.length > 0">
                                                <span class="title" i18n>File xử lý đính kèm: </span>
                                                <div *ngFor="let f of cmt?.file">
                                                    <div fxLayout="row" fxLayoutAlign="none center">
                                                        <span class="material-icons-outlined">
                                                            attachment
                                                        </span>
                                                        <span (click)="downloadFile(f?.id, f?.filename)" class="file">
                                                            {{f?.filename}}
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </mat-tab>
                            <mat-tab *ngIf="!hideTabHistory">
                                <ng-template mat-tab-label>
                                    <mat-icon class="tabIcon">history</mat-icon>
                                    <span i18n>Lịch sử</span>
                                </ng-template>
                                <div class="listHistory" *ngIf="listHistory.length > 0" infinite-scroll [infiniteScrollDistance]="2" [infiniteScrollThrottle]="50" [scrollWindow]="false" (scrolled)="onScrollListHistory()">
                                    <div class="items" fxLayout="row" fxLayout.xs="row" fxLayout.sm="row" *ngFor="let history of listHistory">
                                        <div class="icon">
                                            <mat-icon>history</mat-icon>
                                        </div>
                                        <div class="mainHistory">
                                            <div class="sender">
                                                <span class="name">{{history.user.name}}</span>
                                                <span class="time">
                                                    <span *ngIf="history.type == 1" i18n>đã bình luận</span>
                                                <span *ngIf="history.type == 2" i18n>đã chuyển bước</span>
                                                <span *ngIf="history.type == 3 || history.type==7" i18n>đã thay đổi</span>
                                                <span *ngIf="history.type == 4" i18n>đã thêm mới</span>
                                                <span *ngIf="history.type == 5" i18n>đã xoá</span>
                                                <span *ngIf="history.type == 6" i18n>đã tìm kiếm</span> - {{history.affectedDate | date : 'dd/MM/yyyy HH:mm:ss'}}
                                                </span>
                                            </div>
                                            <div class="content">
                                                <table class="tblHistory" *ngIf="history.type != 4">
                                                    <colgroup>
                                                        <col span="1" style="width: 25%;">
                                                        <col span="1" style="width: 35%;">
                                                        <col span="1" style="width: 35%;">
                                                    </colgroup>
                                                    <tr>
                                                        <th i18n>Trường</th>
                                                        <th i18n>Giá trị cũ</th>
                                                        <th i18n>Giá trị mới</th>
                                                    </tr>
                                                    <tr *ngFor="let action of history.action">
                                                        <td i18n-data-label data-label="Trường">
                                                            <span>{{action.fieldName}}</span>
                                                        </td>
                                                        <td i18n-data-label data-label="Giá trị cũ" *ngIf="!showFileUpdateHistory && !action?.originalObject?.isFile && !action?.oldParsed?.length">
                                                            <div fxLayout="row" fxLayoutAlign="none center" *ngIf="convertStringtoObject(action.originalValue)?.type == 'file' && showHistoryFile">
                                                              <span class="material-icons-outlined" style="width: auto; margin-right: 10px">
                                                                    attachment
                                                              </span>
                                                                <span (click)="downloadFile(convertStringtoObject(action.originalValue).id, convertStringtoObject(action.originalValue).name)" class="file">
                                                                    {{convertStringtoObject(action.originalValue).name}}
                                                              </span>
                                                            </div>
                                                            <span *ngIf="!convertStringtoObject(action.originalValue) && showHistoryFile" [innerHTML]="formatNewHistoryValue(action.originalValue)"></span>
                                                            <span *ngIf="!showHistoryFile" [innerHTML]="formatNewHistoryValue(action.originalValue)"></span>
                                                        </td>
                                                        <td i18n-data-label data-label="Giá trị cũ" *ngIf="showFileUpdateHistory && !action?.oldParsed?.length">
                                                            <span [innerHTML]="formatNewHistoryValue(action.originalValue)"></span>
                                                            <div *ngIf="action.originalValue == dossierMenuTaskRemind">
                                                                <span class="file" *ngFor="let file of action.listFileOld" (click)="downloadFile(file?.id, file?.filename)">{{file.filename}}</span>
                                                            </div>
                                                            <div *ngIf="action.originalValue == dossierTaskStatus || action.originalValue == dossierTaskStatusNew">
                                                                <span class="file" *ngFor="let file of action.listFileNew" (click)="downloadFile(file?.id, file?.filename)">{{file.filename}}</span>
                                                            </div>
                                                        </td>
                                                        <td i18n-data-label data-label="Giá trị cũ" *ngIf="!showFileUpdateHistory && action?.originalObject?.isFile && !action?.oldParsed?.length">
                                                            <span> {{action.originalValue?.replaceAll('"', '')}} </span>
                                                        </td>
                                                        <td i18n-data-label data-label="Giá trị cũ" *ngIf="action?.oldParsed?.length > 0">
                                                            <div *ngFor="let fee of action.oldParsed; let i = index">
                                                                <p><b>Loại lệ phí: </b> {{ fee?.typeName }} </p>
                                                                <p><b>Số lượng: </b> {{ fee?.quantity}} </p>
                                                                <p><b>Mức lệ phí: </b> {{ fee?.amount}} </p>
                                                                <p><b>Thành tiền: </b> {{ fee?.quantity * fee?.amount }} </p>
                                                                <p><b>Mô tả: </b> {{ fee?.descriptionTrans }} </p>
                                                                <p><b>Thanh toán: </b> {{ (fee?.quantity * fee?.amount) > fee?.paid ? 'Chưa thanh toán' : 'Đã thanh toán' }} </p>
                                                                <p><b>Bắt buộc thanh toán: </b>{{ fee?.required ? 'Có' : 'Không'}}</p>
                                                                <mat-divider *ngIf="i < action.oldParsed?.length - 1"></mat-divider>
                                                            </div>
                                                        </td>
                                                        <td i18n-data-label data-label="Giá trị mới" *ngIf="action?.newParsed?.length > 0">
                                                            <div *ngFor="let fee of action.newParsed; let i = index">
                                                                <p><b>Loại lệ phí: </b> {{ fee?.typeName }} </p>
                                                                <p><b>Số lượng: </b> {{ fee?.quantity}} </p>
                                                                <p><b>Mức lệ phí: </b> {{ fee?.amount}} </p>
                                                                <p><b>Thành tiền: </b> {{ fee?.quantity * fee?.amount }} </p>
                                                                <p><b>Mô tả: </b> {{ fee?.descriptionTrans }} </p>
                                                                <p><b>Thanh toán: </b> {{ (fee?.quantity * fee?.amount) > fee?.paid ? 'Chưa thanh toán' : 'Đã thanh toán' }} </p>
                                                                <p><b>Bắt buộc thanh toán: </b>{{ fee?.required ? 'Có' : 'Không'}}</p>
                                                                <mat-divider *ngIf="i < action.newParsed?.length - 1"></mat-divider>
                                                            </div>
                                                        </td>
                                                        <td i18n-data-label data-label="Giá trị mới" *ngIf="!showFileUpdateHistory && !action?.newObject?.isFile && !action?.newParsed?.length">
                                                            <span  [innerHTML]="formatNewHistoryValue(action.newValue)"></span>
                                                        </td>
                                                        <td i18n-data-label data-label="Giá trị mới" *ngIf="!showFileUpdateHistory && action?.newObject?.isFile && !action?.newParsed?.length">
                                                            <div fxLayout="row" fxLayoutAlign="none center" *ngIf="convertStringtoObject(action.newValue)?.type == 'file' && showHistoryFile">
                                                              <span class="material-icons-outlined" style="width: auto; margin-right: 10px">
                                                                    attachment
                                                              </span>
                                                                <span (click)="downloadFile(convertStringtoObject(action.newValue).id, convertStringtoObject(action.newValue).name)" class="file">
                                                                    {{convertStringtoObject(action.newValue).name}}
                                                              </span>
                                                            </div>
                                                            <span *ngIf="!convertStringtoObject(action.newValue) && showHistoryFile" [innerHTML]="formatNewHistoryValue(action.newValue)"></span>
                                                            <span *ngIf="!showHistoryFile" [innerHTML]="formatNewHistoryValue(action.newValue)"></span>
                                                        </td>
                                                        <td i18n-data-label data-label="Giá trị mới" *ngIf="showFileUpdateHistory && !action?.newParsed?.length">
                                                            <span [innerHTML]="formatNewHistoryValue(action.newValue)"></span>
                                                            <div  *ngIf="action.newValue == dossierTaskStatus">
                                                                <span class="file" *ngFor="let file of action.listFileNew" (click)="downloadFile(file?.id, file?.filename)">{{file.filename}}</span>
                                                            </div>
                                                            <div  *ngIf="action.newValue == dossierMenuTaskRemind">
                                                                <span class="file" *ngFor="let file of action.listFileOld" (click)="downloadFile(file?.id, file?.filename)">{{file.filename}}</span>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </mat-tab>
                            <mat-tab *ngIf="showNhatKyTab && showNhatKyTab == true">
                                <ng-template mat-tab-label>
                                    <mat-icon class="tabIcon">history</mat-icon>
                                    <span i18n>Nhật ký</span>
                                </ng-template>
                                <div class="listComment" infinite-scroll [infiniteScrollDistance]="2" [infiniteScrollThrottle]="50" [scrollWindow]="false" >
                                    <div class="items" fxLayout="row" fxLayout.xs="row" fxLayout.sm="row" *ngFor="let status of listStatus">
                                        <div class="icon">
                                            <mat-icon>history</mat-icon>
                                        </div>
                                        <div class="mainCmt">
                                            <div class="sender">
                                                <span class="name">{{status.NguoiXuLy}}</span>
                                                <span *ngIf="status.TrangThai == 1" class="time"><span i18n>Mới đăng ký</span> - {{ getDate(status.ThoiDiemXuLy) | date : 'dd/MM/yyyy HH:mm:ss'}}</span>
                                                <span *ngIf="status.TrangThai == 2" class="time"><span i18n>Được tiếp nhận</span> - {{getDate(status.ThoiDiemXuLy) | date : 'dd/MM/yyyy HH:mm:ss'}}</span>
                                                <span *ngIf="status.TrangThai == 3" class="time"><span i18n>Không được tiếp nhận</span> - {{getDate(status.ThoiDiemXuLy) | date : 'dd/MM/yyyy HH:mm:ss'}}</span>
                                                <span *ngIf="status.TrangThai == 4" class="time"><span i18n>Đang xử lý</span> - {{getDate(status.ThoiDiemXuLy) | date : 'dd/MM/yyyy HH:mm:ss'}}</span>
                                                <span *ngIf="status.TrangThai == 5" class="time"><span i18n>Yêu cầu bổ sung giấy tờ</span> - {{getDate(status.ThoiDiemXuLy) | date : 'dd/MM/yyyy HH:mm:ss'}}</span>
                                                <span *ngIf="status.TrangThai == 6" class="time"><span i18n>Yêu cầu thực hiện nghĩa vụ tài chính</span> - {{getDate(status.ThoiDiemXuLy) | date : 'dd/MM/yyyy HH:mm:ss'}}</span>
                                                <span *ngIf="status.TrangThai == 7" class="time"><span i18n>Công dân yêu cầu rút hồ sơ</span> - {{getDate(status.ThoiDiemXuLy) | date : 'dd/MM/yyyy HH:mm:ss'}}</span>
                                                <span *ngIf="status.TrangThai == 8" class="time"><span i18n>Dừng xử lý</span> - {{getDate(status.ThoiDiemXuLy) | date : 'dd/MM/yyyy HH:mm:ss'}}</span>
                                                <span *ngIf="status.TrangThai == 9" class="time"><span i18n>Đã xử lý xong</span> - {{getDate(status.ThoiDiemXuLy) | date : 'dd/MM/yyyy HH:mm:ss'}}</span>
                                                <span *ngIf="status.TrangThai == 10" class="time"><span i18n>Đã trả kết quả</span> - {{getDate(status.ThoiDiemXuLy) | date : 'dd/MM/yyyy HH:mm:ss'}}</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </mat-tab>
                        </mat-tab-group>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
