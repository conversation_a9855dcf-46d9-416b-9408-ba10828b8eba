import { Component, OnInit, Inject } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA, MatDialog } from '@angular/material/dialog';
import { KeycloakService } from 'keycloak-angular';
import { DossierService } from 'src/app/data/service/dossier/dossier.service';
import { UserService } from 'src/app/data/service/user.service';
import * as ClassicEditor from '@ckeditor/ckeditor5-build-classic/build/ckeditor';
import '@ckeditor/ckeditor5-build-classic/build/translations/vi';
import { EnvService } from 'src/app/core/service/env.service';
import { SnackbarService } from 'src/app/data/service/snackbar/snackbar.service';
import { DatePipe } from '@angular/common';
import { ProcedureService } from 'src/app/data/service/procedure/procedure.service';
import * as tUtils from 'src/app/data/service/thoai.service';
import { DeploymentService } from 'src/app/data/service/deployment.service';
import {NotificationService} from "data/service/notification.service";
import { PadmanService } from 'src/app/data/service/svc-padman/padman.service';
import { AdapterService } from 'src/app/data/service/adapter/adapter.service';
import { HumanService } from 'src/app/data/service/human/human.service';
import { SocialProtectionKTMService } from 'src/app/data/service/ktm-social-protection/social-protection-ktm.service';
import { SocialProtectionService } from 'src/app/data/service/social-protection/social-protection.service';
import {LogmanService} from 'data/service/logman/logman.service';
import { AgencyService } from 'src/app/data/service/basedata/agency.service';
import {TrinamService} from 'modules/hbh/trinam.service';
import { FileService } from 'src/app/data/service/file.service';
import { PdfViewerComponent, PdfViewerDialog } from 'src/app/shared/components/pdf-viewer/pdf-viewer.component';
import { UploadService } from 'src/app/data/service/upload/upload.service';
import { ConfigService } from 'src/app/data/service/dossier/config.service';
import { DigitalSignatureService } from 'src/app/data/service/digital-signature/digital-signature.service';
import { ApiProviderService } from 'src/app/core/service/api-provider.service';
import { ConfirmPrintTemplateDialogModel, PrintTemplateComponent } from 'src/app/shared/components/print-template/print-template.component';
declare var vgca_sign_approved: any;
@Component({
  selector: 'app-refuse',
  templateUrl: './refuse.component.html',
  styleUrls: ['./refuse.component.scss']
})
export class RefuseComponent implements OnInit {

  config = this.envService.getConfig();
  env = this.deploymentService.getAppDeployment()?.env;
  selectedLangId = Number(localStorage.getItem('languageId')) || 228;
  selectedLang: string;
  dossierId: string;
  dossierDetail: any;
  commentContent = '';
  commentContentPlainText = '';
  userName: string;
  accountId: string;
  userId: string;
  procedureProcessDetail : any;
  dossierCode: string;
  isCKMaxlenght = false;
  fullname: string;
  filePDF = {
    id: "",
    filename: ""
  };
  tempObj : any = {};
  checkIsDocFile = false;
  public Editor = ClassicEditor;
  commentConfig = {
    placeholder: 'Nhập nội dung...',
    language: 'vi',
    removePlugins: ['MediaEmbed', 'ImageUpload'],
    toolbarLocation: 'bottom'
  };
  dossierTaskStatus = { id: '', name: []};
  dossierMenuTaskRemind = {id: '', name: []};
  currentDossierStatus : number;
  //Thu tuc co cau hinh dong bo BXD-LGSP Tan Tan
  isSyncDBNLGSPTanDanBXD = 0;
  isHasCodeLDXH = false;
  statusName = "";
  // ================================================= Upload file
  acceptFileType = this.config.acceptFileType;
  maxFileSize = this.env?.maxFileSize || this.config.maxFileSize;
  fileTemplate = "";
  uploaded: boolean;
  acceptFileExtension = this.config.acceptFileExtension;
  blankVal: any;
  listConfigTemplate = [];
  files = [];
  urls = [];
  fileNames = [];
  uploadFileNames = [];
  fileNamesFull = [];
  uploadedImage = [];
  notifyQNI = this.deploymentService.getAppDeployment()?.notifyqni?.enable;
  digitalsignatureButton = this.deploymentService.env?.OS_HCM?.digitalsignatureButton ? this.deploymentService.env?.OS_HCM?.digitalsignatureButton : false;
  requireAttachmentWhenRefuse = false;
  digitalSignatureEnable: boolean = false;
  digitalSignature = {
    SmartCA:false,
    VGCA:false,
    VNPTCA:false,
    VNPTSim:false,
    NEAC:false,
    QNM: false
  }
  isEditSignTokenName = false;
  totalCost = '';
  codeMap='';

  ckeditorMaxLengthNotification = "";
  ckeditorMaxLength = this.deploymentService.env.OS_HCM.ckeditorMaxLength;
  ckeditorMaxLengthQNI = this.deploymentService?.env?.OS_QNI?.noCharacterCommentRefuse;
  // OS KGG
  showPrintTemplateComponentInRefuseDossierPopup = this.deploymentService.getAppDeployment()?.showPrintTemplateComponentInRefuseDossierPopup || false;
  isUserRQ = this.env?.OS_KGG?.isUserRQ ? this.env?.OS_KGG?.isUserRQ : false;

  ktmDossierToSyncStatusBLDTBXH = null;
  ktmEnableSyncToBLDTBXH = this.deploymentService.env.OS_KTM.enableSyncToBLDTBXH;
  ktmSyncToBLDTBXHConfigId = this.deploymentService.env.OS_KTM.syncToBLDTBXHConfigId;
  enableSyncDossierTNMTQni = this.deploymentService.env?.OS_QNI?.enableSyncDossierTNMTQni ? this.deploymentService.env?.OS_QNI?.enableSyncDossierTNMTQni : false;
  integratedTnmtConfigIdQni = this.deploymentService.env?.OS_QNI?.integratedTnmtConfigIdQni || "66554990ed9f4f516a5b81c7";

  dossierToSyncStatusBLDTBXH = null;
  enableSyncToBLDTBXH = this.deploymentService.env.OS_QNM.enableSyncToBLDTBXH;
  syncToBLDTBXHConfigId = this.deploymentService.env.OS_QNM.syncToBLDTBXHConfigId;
  isBoTNMTQNM = this.env?.OS_QNM?.boTNMT === true ? this.env?.OS_QNM?.boTNMT : false;
  isBoTNMTHCM = this.deploymentService.env.OS_HCM.maTTBoTNMT;
  isBoTNMTGLI = this.deploymentService.env.OS_GLI.boTNMT;
  showExportFileBtn = this.deploymentService.env.OS_HCM.showExportFileBtn;
  enableShowExportFileBtn = false;
  listAgencyShowExportFileBtn = this.deploymentService.env.OS_HCM?.listAgencyShowExportFileBtn; 
  template = this.deploymentService.env.OS_HCM.template;
  refuseTemplate = "";
  isSyncConstructKTM=this.deploymentService.env?.OS_KTM?.isSyncConstructKTM;
  constructConfigId=this.deploymentService.env?.OS_KTM?.constructConfigId;
  isSmsQNM = this.env?.OS_QNM?.isSmsQNM ? true : false;
  listAgencyAllowApplyRenderDepartmentForRefuse = this.deploymentService.env?.OS_HCM?.allowApplyRenderDepartmentForRefuse?.agencyId ? this.deploymentService.env?.OS_HCM?.allowApplyRenderDepartmentForRefuse?.agencyId : [];
  isAllowApplyRenderDepartmentForRefuse = false;
  isAdmin?: boolean = sessionStorage.getItem('oneGateAdminMaster') === '1';
  parentAgencyName = '';
  agencyName = '';

    //IGATESUPP-86102
    isDVCLTEnable = this.deploymentService.getAppDeployment()?.isDVCLTEnable;
    // isDVCLT = true;

  //ket qua xu ly 
  //
  isUploadResultProcessing = this.deploymentService.getAppDeployment()?.isUploadResultProcessing ? this.deploymentService.getAppDeployment()?.isUploadResultProcessing : false;

  //IGATESUPP-108635
  checkRefusedDossierOnce = this.deploymentService.getAppDeployment()?.checkRefusedDossierOnce ? this.deploymentService.getAppDeployment().checkRefusedDossierOnce : false;
  enablePrintBillNew = this.deploymentService.getAppDeployment()?.enablePrintBillNew ? this.deploymentService.getAppDeployment().enablePrintBillNew : true


  isSyncConstructHPG = this.deploymentService?.syncConstructHPG?.isSyncConstructHPG;
  disableButton = false;
  constructor(
    private userService: UserService,
    private envService: EnvService,
    private keycloakService: KeycloakService,
    public dialogRef: MatDialogRef<RefuseComponent>,
    @Inject(MAT_DIALOG_DATA) public data: RefuseDialogModel,
    private snackbarService: SnackbarService,
    private dossierService: DossierService,
    private datePipe: DatePipe,
    private procedureService: ProcedureService,
    private deploymentService: DeploymentService,
    private notiService: NotificationService,
    private padmanService: PadmanService,
    private socialProtectionKTMService: SocialProtectionKTMService,
    private socialProtectionService: SocialProtectionService,
    private adapterService: AdapterService,
    private humanService: HumanService,
    private logmanService: LogmanService,
    private agencyService: AgencyService,
    private trinamService: TrinamService,
    private fileService: FileService,
    private dialog: MatDialog,
    private uploadService: UploadService,
    private configService: ConfigService,
    private digitalSignatureService: DigitalSignatureService,
    private apiProviderService: ApiProviderService,    
  ) {
    this.dossierId = data.dossierId;
    this.dossierCode = data.dossierCode;
    this.currentDossierStatus = data.dossierStatus;
  }

  ngOnInit(): void {
    const userAgency = JSON.parse(localStorage.getItem('userAgency'));
    if(this.listAgencyAllowApplyRenderDepartmentForRefuse.filter(item => item == userAgency?.id || item == userAgency?.parent?.id ).length > 0){
      this.isAllowApplyRenderDepartmentForRefuse = true;
    }
    if(!!userAgency?.parent?.name){
      this.parentAgencyName = userAgency.parent?.name;
    }
    if(!!userAgency?.name){
      this.agencyName= userAgency?.name;
    }
    this.getUserAccount();
    this.getDossierTaskStatus();
    this.getDossierMenuTaskRemind();
    this.getDetailDossier();
    this.selectedLang = localStorage.getItem('language');
    this.setEnvVariable();
    
    if(this.listAgencyShowExportFileBtn.filter(item => item == userAgency?.id || item == userAgency?.parent?.id ).length > 0){
      this.enableShowExportFileBtn = true;
    }
    // Nếu QNI bật tham số thì gán lại giá trị -> tránh viết nhiều code
    if(this.ckeditorMaxLengthQNI > 0)
    this.ckeditorMaxLength = this.ckeditorMaxLengthQNI;
    if (this.selectedLang === 'vi') {0
      this.ckeditorMaxLengthNotification = "Nội dung không quá 50 ký tự!";
      if (this.ckeditorMaxLength > 0)
        this.ckeditorMaxLengthNotification = "Nội dung không quá "+ this.ckeditorMaxLength + " ký tự!";
    } else if (this.selectedLang === 'en') {
      this.ckeditorMaxLengthNotification = "Content must not exceed 500 characters!";
      if (this.ckeditorMaxLength > 0)
        this.ckeditorMaxLengthNotification = "Content must not exceed "+ this.ckeditorMaxLength + " characters!";
    }
    
    // Check if digital signature supported
    if(this.digitalsignatureButton){
      this.digitalSignatureEnable = true;
      this.digitalSignature = {
        SmartCA:false,
        VGCA:false,
        VNPTCA:false,
        VNPTSim:false,
        NEAC:false,
        QNM: false
      }
      this.digitalSignature.SmartCA = this.env?.digitalSignature?.SmartCA == 1;
      this.digitalSignature.VGCA = this.env?.digitalSignature?.VGCA == 1;
      this.digitalSignature.VNPTCA = this.env?.digitalSignature?.VNPTCA == 1;
      this.digitalSignature.VNPTSim = this.env?.digitalSignature?.VNPTSim == 1;
      this.digitalSignature.NEAC = this.env?.digitalSignature?.NEAC == 1;
      this.digitalSignature.QNM = this.env?.digitalSignature?.QNM == 1;
      this.isEditSignTokenName = this.env?.OS_KTM?.isEditSignTokenName == true ? true : false ;
    }

    if(this.showPrintTemplateComponentInRefuseDossierPopup) {
      this.digitalSignature.SmartCA = this.env?.digitalSignature?.SmartCA == 1;
      this.digitalSignature.VGCA = this.env?.digitalSignature?.VGCA == 1;
      this.digitalSignature.VNPTCA = this.env?.digitalSignature?.VNPTCA == 1;
      this.digitalSignature.VNPTSim = this.env?.digitalSignature?.VNPTSim == 1;
      this.digitalSignature.NEAC = this.env?.digitalSignature?.NEAC == 1;
      this.digitalSignature.QNM = this.env?.digitalSignature?.QNM == 1;
      this.isEditSignTokenName = this.env?.OS_KTM?.isEditSignTokenName == true ? true : false ;
    }
  }
  setEnvVariable(){
    this.fileTemplate = !!this.env?.fileTemplate?.refuse ? this.env?.fileTemplate?.refuse : "";
    this.requireAttachmentWhenRefuse = this.env?.OS_BDG?.isRequiredUploadFileBDG?.refuse != undefined && this.env?.OS_BDG?.isRequiredUploadFileBDG?.refuse != null ? this.env?.OS_BDG?.isRequiredUploadFileBDG?.refuse : false;
    let enableAllowShowInfoDossierWhenExportFileHCM = this.deploymentService.env?.OS_HCM?.allowShowInfoDossierWhenExportFileHCM?.enable ? this.deploymentService.env?.OS_HCM?.allowShowInfoDossierWhenExportFileHCM?.enable : false;
    if(enableAllowShowInfoDossierWhenExportFileHCM){
      this.refuseTemplate = this.deploymentService.env?.OS_HCM?.allowShowInfoDossierWhenExportFileHCM?.templateHCM?.refuseTemplate ? this.deploymentService.env?.OS_HCM?.allowShowInfoDossierWhenExportFileHCM?.templateHCM?.refuseTemplate : "";
      if(this.isAllowApplyRenderDepartmentForRefuse){
        this.refuseTemplate = this.deploymentService.env?.OS_HCM?.allowApplyRenderDepartmentForRefuse?.template?.refuseTemplateHCM ? this.deploymentService.env?.OS_HCM?.allowApplyRenderDepartmentForRefuse?.template?.refuseTemplateHCM : "";
      }
    } else {
      this.refuseTemplate = this.template?.refuseTemplate ? this.template?.refuseTemplate : "";
      if(this.isAllowApplyRenderDepartmentForRefuse){
        this.refuseTemplate = this.deploymentService.env?.OS_HCM?.allowApplyRenderDepartmentForRefuse?.template?.refuseTemplate ? this.deploymentService.env?.OS_HCM?.allowApplyRenderDepartmentForRefuse?.template?.refuseTemplate : "";
      }
    }
    
  }

  downloadTemplate(){
    window.open(this.fileTemplate, '_self');
  }

  async openPdfDigitalSignatureV2( attach, index, dsType?:number){
    // dsType => 1: ký số sim, default: smart-ca
    // type => 1: ý kiến xử lý, 2: kết quả, 3: thành phần hồ sơ
    let originDocFileName = attach.name ? attach.name : attach.filename;
    let filePdf, filePdfName, dialogData, checkIsDocFile = false;
    if(originDocFileName.indexOf('.docx') !== -1 || originDocFileName.indexOf('.doc') !== -1){
      filePdf = await this.fileService.convertDoc2Pdf(attach.id);
      filePdfName = originDocFileName.replace('.docx', '.pdf').replace('.doc', '.pdf');
      checkIsDocFile = true;
      dialogData = new PdfViewerDialog(filePdf.id, filePdfName, this.dossierId, dsType);
    } else {
      dialogData = new PdfViewerDialog(attach.id, attach.name ? attach.name : attach.filename, this.dossierId, dsType);
    }
    const dialogRef = this.dialog.open(PdfViewerComponent, {
      maxWidth: '100%',
      width: '100%',
      height: '100%',
      maxHeight: '100%',
      data: dialogData,
      disableClose: false,
      panelClass: 'custom-dialog-container'
      // autoFocus: false
    });
    dialogRef.afterClosed().subscribe( async rs => {
      if(rs){
        
        for await (const file of this.uploadFileNames) {
          if (file.id == attach.id) {
            let fileCurrent = this.uploadFileNames.filter(item => item.id == attach.id)[0];
            let index = this.uploadFileNames.indexOf(fileCurrent);
            if (rs.filename.length > 20) {
              const startText = rs.filename.substring(0, 5);
              const shortText = rs.filename
                .substring(rs.filename.length - 7, rs.filename.length);
              this.fileNames = this.fileNames.map((item, i) => i === index ? (startText + '...' + shortText) : item);
              const indexType = rs.filename.lastIndexOf(".");
              const extention = rs.filename.substring(indexType + 1);
              this.urls = this.urls.map((item, i) => i === index ? this.getFileIcon(extention) : item);
            }
            file.filename = rs.filename;
            file.size = rs.size;
            file.id = rs.id;
          }
        }  
        // await this.addFileSign( rs.id, rs.filename, rs.size);
        this.logmanService.postSignHistory(rs.id,rs.filename).subscribe();
      }
    });
  }

  async addFileSign( fileId, filename, size){
    const file = {
      id: fileId,
      filename: filename,
      size: size,
    };
    if (file.size >= this.maxFileSize * 1024 * 1024) {
      const msgObj = {
        vi: 'Kích thước tệp tin quá lớn, tên tệp: ' + file.filename,
        en: 'The file is too large, file name: ' + file.filename
      };
      this.snackbarService.openSnackBar(0, '', msgObj[this.selectedLang], 'error_notification', this.config.expiredTime);
      return;
    }
    let newFile = [];
    newFile.push(file);
    if(newFile.length >0){
      await this.uploadFileExport(newFile, this.accountId);
    }
        
  }

  openPdfDigitalSignature( fileId, filename, index) {
    if(filename.indexOf('.docx') !== -1 || filename.indexOf('.doc') !== -1){
      const file = {
        id: fileId,
        name: filename
      };
      this.openPdfDigitalSignatureV2( file, null, 2);
      return;
    }
    const dialogData = new PdfViewerDialog(fileId, filename, this.dossierId);
    const dialogRef = this.dialog.open(PdfViewerComponent, {
      maxWidth: '100%',
      width: '100%',
      height: '100%',
      maxHeight: '100%',
      data: dialogData,
      disableClose: false,
      panelClass: 'custom-dialog-container',
      // autoFocus: false
    });
    dialogRef.afterClosed().subscribe(async (rs) => {
      if(rs){
        // update file in procedureForm
       
        for await (const file of this.uploadFileNames) {
          if (file.id == fileId) {
            let fileCurrent = this.uploadFileNames.filter(item => item.id == fileId)[0];
            let index = this.uploadFileNames.indexOf(fileCurrent);
            if (rs.filename.length > 20) {
              const startText = rs.filename.substring(0, 5);
              const shortText = rs.filename
                .substring(rs.filename.length - 7, rs.filename.length);
              this.fileNames = this.fileNames.map((item, i) => i === index ? (startText + '...' + shortText) : item);
              const indexType = rs.filename.lastIndexOf(".");
              const extention = rs.filename.substring(indexType + 1);
              this.urls = this.urls.map((item, i) => i === index ? this.getFileIcon(extention) : item);
            }
            file.filename = rs.filename;
            file.size = rs.size;
            file.id = rs.id;
          }
        }  
        // await this.addFileSign( rs.id, rs.filename, rs.size);
        this.logmanService.postSignHistory(rs.id,rs.filename).subscribe();
      }
    });
  }

  async openNEAC( attach, index, dsType?:number){
  //   const file = {
  //     "id": "662604cb9a4e6b6207532e0a",
  //     "filename": "Mẫu file từ chối hồ sơ_signed.pdf",
  //     "size": 129292
  // };
  //   if (file.size >= this.maxFileSize * 1024 * 1024) {
  //     const msgObj = {
  //       vi: 'Kích thước tệp tin quá lớn, tên tệp: ' + file.filename,
  //       en: 'The file is too large, file name: ' + file.filename
  //     };
  //     this.snackbarService.openSnackBar(0, '', msgObj[this.selectedLang], 'error_notification', this.config.expiredTime);
  //     return;
  //   }
  //   let newFile = [];
  //   newFile.push(file)
  //   await this.uploadFileExport(newFile, this.accountId);
    this.openPdfDigitalSignatureV2( attach, index, dsType);
  }

  async openVGCAplugin( fileId, filename, size){
    this.tempObj.fileId = fileId;
    this.tempObj.filename = filename;
    this.tempObj.size = size;
    this.filePDF.id = fileId;
    this.filePDF.filename = filename;
    this.checkIsDocFile = false;
    if( filename.indexOf('.docx') !== -1 || filename.indexOf('.doc') !== -1){
      const  filePdf = await this.fileService.convertDoc2Pdf(fileId);
      this.filePDF.filename = filename.replace('.docx', '.pdf').replace('.doc', '.pdf');
      this.filePDF.id  = filePdf.id;
      this.checkIsDocFile = true;
    }
    this.handleBinaryString(fileId);
  }

  handleBinaryString(fileId){

    this.configService.downloadFile( this.filePDF.id, this.dossierId).subscribe( async (file) => {
      var prms = {};
      prms["FileUploadHandler"] = this.adapterService.getVGCAAdapterCallBackUrl(fileId,this.tempObj.filename,this.accountId);
      prms["SessionId"] = "";
      // Create a public link
      const formData: FormData = new FormData();
      file.name = "example.pdf";
      formData.append('file', file, file.name);
      const fileResponse = await  this.uploadService.uploadFile(formData).toPromise();
      this.tempObj.tempFileId = fileResponse.id;
      const publicFileUrl = `${this.uploadService.publicFileUrl}/${fileResponse.id}.pdf`;
      console.log("publicFileUrl",publicFileUrl);
      // prms["FileName"] = "https://staticv2.vnptigate.vn/file/cmnd.pdf";
      prms["FileName"] = publicFileUrl;
      var json_prms = JSON.stringify(prms);
      vgca_sign_approved(json_prms, this.SignFileCallBack);
    });
  }

  SignFileCallBack = async rv => {
    var received_msg = JSON.parse(rv);
    if(this.tempObj.tempFileId){
      this.uploadService.deleteFile(this.tempObj.tempFileId).subscribe();
    }
    switch(received_msg.Status){
      case (14):{
        // Cancel :: do nothing
        const msgObj = {
          vi: 'Đã hủy ký số!',
          en: 'Cancel sign document!'
        };
        this.snackbarService.openSnackBar(2, msgObj[this.selectedLang], '', 'warning_notification', this.config.expiredTime);
        break;
      }
      case true: {
        // Sign file successfully
        break;
      }
      case false: {
        // Failed to sign file
        const msgObj = {
          vi: 'Ký số thất bại!',
          en: 'Signing failed!'
        };
        this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
        break;
      }
      case 0: {
        if (received_msg.FileServer === ''){
          const msgObj = {
            vi: 'Đã hủy ký số!',
            en: 'Cancel sign document!'
          };
          this.snackbarService.openSnackBar(2, msgObj[this.selectedLang], '', 'warning_notification', this.config.expiredTime);
          break;
        }
        // Sign file successfully
        let file = this.uploadFileNames.filter(item => item.id == this.tempObj.fileId);
        let index = this.uploadFileNames.indexOf(file);
        const newFilename = this.changeFilenameOfSignedFile(this.filePDF.filename);
        // await this.addFileSign( received_msg.FileServer, newFilename, this.tempObj.size);
        for await (const file of this.uploadFileNames) {
          if (file.id == this.tempObj.fileId) {
            let fileCurrent = this.uploadFileNames.filter(item => item.id == this.tempObj.fileId)[0];
            let index = this.uploadFileNames.indexOf(fileCurrent);
            if (newFilename.length > 20) {
              const startText = newFilename.substring(0, 5);
              const shortText = newFilename
                .substring(newFilename.length - 7, newFilename.length);
              this.fileNames = this.fileNames.map((item, i) => i === index ? (startText + '...' + shortText) : item);
              const indexType = newFilename.lastIndexOf(".");
              const extention = newFilename.substring(indexType + 1);
              this.urls = this.urls.map((item, i) => i === index ? this.getFileIcon(extention) : item);
            }
            file.filename = newFilename;
            file.size = this.tempObj.size;
            file.id = received_msg.FileServer;
          }
        }  
        
        this.logmanService.postSignHistory(received_msg.FileServer, newFilename).subscribe();
  
        const msgObjNoti = {
          vi: 'Ký số thành công',
          en: 'Signed successfully'
        };
        this.snackbarService.openSnackBar(1, msgObjNoti[localStorage.getItem('language')], '', 'success_notification', this.config.expiredTime);
        break;
      }
      default: {
        // Error
        console.log("error: ",received_msg.Message);
        const msgObj = {
          vi: 'Ký số thất bại!',
          en: 'Signing failed!'
        };
        this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
      }
    }
    return received_msg;
  }


  changeFilenameOfSignedFile(fullFilename:string):string{
    const signalStr = "_signed";
    const index = fullFilename.lastIndexOf(".");
    const extention = fullFilename.substring(index+1);
    const filename = fullFilename.substring(0,index);
    if(filename.length < 8){
      return `${filename}${signalStr}.${extention}`;
    }
    if(filename.slice(-7) != signalStr){
      return `${filename}${signalStr}.${extention}`;
    }
    return fullFilename;
  }

  checkIfDocFileOnly(filename): boolean {
    // File type
    const fileExtention = filename.split('.').pop();
    if (fileExtention.toLowerCase() == 'doc' ||  fileExtention.toLowerCase() == 'docx') {
      return true;
    }
    return false;
  }

  async openVnptCaPlugin( fileId, filename){
    if(this.checkIfDocFileOnly(filename))
        this.openVnptCaPluginForDocFile( fileId, filename);
      else
        this.openVnptCaPluginForPdfFile( fileId, filename);
    }
    async openVnptCaPluginForDocFile( fileId, filename){
      const filePdf = await this.fileService.convertDoc2Pdf(fileId);
      const fileNameDigitalSign = filename.split('.').slice(0, -1).join('.') + ".pdf";
      const result = await this.digitalSignatureService.signWithVNPTCA(filePdf.id,fileNameDigitalSign, this.dossierId);
      console.log("result",result);
      if(result.status == 1){
        for await (const file of this.uploadFileNames) {
          if (file.id == fileId) {
            let fileCurrent = this.uploadFileNames.filter(item => item.id == fileId)[0];
            let index = this.uploadFileNames.indexOf(fileCurrent);
            if (result.data.filename.length > 20) {
              const startText = result.data.filename.substring(0, 5);
              const shortText = result.data.filename
                .substring(result.data.filename.length - 7, result.data.filename.length);
              this.fileNames = this.fileNames.map((item, i) => i === index ? (startText + '...' + shortText) : item);
              const indexType = result.data.filename.lastIndexOf(".");
              const extention = result.data.filename.substring(indexType + 1);
              this.urls = this.urls.map((item, i) => i === index ? this.getFileIcon(extention) : item);
            }
            file.filename = result.data.filename;
            file.size = result.data.size;
            file.id = result.data.fileId;
          }
        }  
        
      }
    }
    async openVnptCaPluginForPdfFile( fileId, filename){
      const result = await this.digitalSignatureService.signWithVNPTCA(fileId,filename, this.dossierId);
      console.log("result",result);
      if(result.status == 1){
        for await (const file of this.uploadFileNames) {
          if (file.id == fileId) {
            let fileCurrent = this.uploadFileNames.filter(item => item.id == fileId)[0];
            let index = this.uploadFileNames.indexOf(fileCurrent);
            if (result.data.filename.length > 20) {
              const startText = result.data.filename.substring(0, 5);
              const shortText = result.data.filename
                .substring(result.data.filename.length - 7, result.data.filename.length);
              this.fileNames = this.fileNames.map((item, i) => i === index ? (startText + '...' + shortText) : item);
              const indexType = result.data.filename.lastIndexOf(".");
              const extention = result.data.filename.substring(indexType + 1);
              this.urls = this.urls.map((item, i) => i === index ? this.getFileIcon(extention) : item);
            }
            file.filename = result.data.filename;
            file.size = result.data.size;
          }
        }    
          
      }
    }

    checkIfFileIsSupported(filename): boolean {
      // File type
      const fileExtention = filename.split('.').pop();
      if (fileExtention.toLowerCase() == 'pdf' || 'doc' || 'docx') {
        return true;
      }
      return false;
    }
 

  async getDetailDossier(){
    await this.getDossierFeeNotify();
    this.dossierService.getDossierDetail(this.dossierId).subscribe(data => {
      this.dossierDetail = data;
      this.getProcedureDetail(data?.procedure?.id);
      this.getListConfigTemplate();
      // Todo Check here
      this.notiService.checkSendSubject.next(
        {
          id: data?.procedureProcessDefinition?.id,
          phone: data?.applicant?.data?.phoneNumber,
          email: data?.applicant?.data?.email,
          contentParams: {
            parameters: {
              sector: !!data?.procedure?.sector?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name ? data?.procedure?.sector?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name : '',
              procedure: !!data?.procedure?.translate?.find(agency => agency.languageId === Number(this.selectedLangId))?.name ? data?.procedure?.translate?.find(agency => agency.languageId === Number(this.selectedLangId))?.name : '',
              agency: !!data?.agency?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name ? data?.agency?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name : data?.agency?.parent?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name,
              agencyId: !!data?.agency?.id ? data?.agency?.id : data?.agency?.parent?.id,
              applicantFullname: data?.applicant?.data?.fullname,
              officerFullname: '',
              subsystem: !!this.env?.subsystem?.name ? this.env?.subsystem?.name : "Hệ thống Một cửa điện tử",
              dossierCode: data?.nationCode ? data?.nationCode : data?.code,
              nextTask: 'Từ chối',
              // IGATESUPP-63184
              nextStatus: !!this.env?.notify?.cancelDossier?.nextStatus ? this.env?.notify?.cancelDossier?.nextStatus : (data?.dossierTaskStatus?.name ?? ''), // cho bo sung, trang thai hien tai cua ho so
              dossierStatus: !!this.env?.notify?.cancelDossier?.dossierStatus ? this.env?.notify?.cancelDossier?.dossierStatus : (data?.dossierStatus?.name?.find(t => t.languageId === Number(this.selectedLangId))?.name ?? ''), // cho bo sung, trang thai hien tai cua ho so
              dossierSearchPadsvcUrl: (!!this.env?.domain?.padsvc ? this.env?.domain?.padsvc : "http://padsvctest.vncitizens.vn/vi/") + "dossier/public",
              dossierDetailPadsvcUrl: (!!this.env?.domain?.padsvc ? this.env?.domain?.padsvc : "http://padsvctest.vncitizens.vn/vi/") + "dossier/detail/" + this.dossierId,
              dossierSearchUrl: (!!this.env?.domain?.onegate ? this.env?.domain?.onegate : "http://padsvctest.vncitizens.vn/vi/") + "dossier/search/",
              dossierDetailUrl: (!!this.env?.domain?.onegate ? this.env?.domain?.onegate : "http://padsvctest.vncitizens.vn/vi/") + "dossier/search/" + this.dossierId,
              returnMethod: !!data?.dossierReceivingKind?.name?.find(recieve => recieve.languageId === Number(this.selectedLangId))?.name ? data?.dossierReceivingKind?.name?.find(recieve => recieve.languageId === Number(this.selectedLangId))?.name : '',
              receivingDate: '',
              appointmentDate: '',
              dossierFee: this.totalCost,
              reason: this.commentContentPlainText
            }
          }
        }
      );
    });
  }

  

  getDossierFeeNotify() {
    return new Promise<void>(resolve => {
      this.padmanService.getDossierFee(this.dossierId).toPromise().then(res => {
        if(res && res.length > 0)
        {
          let totalCostValue = 0;
          let totalPaidValue = 0;
          res.forEach(element => {
            totalCostValue += element.quantity * element.amount;
            totalPaidValue += element.paid;
          });
          const monetaryUnit = res[0]?.monetaryUnit ? res[0].monetaryUnit : 'VNĐ';
          let total = totalCostValue - totalPaidValue;
          this.totalCost = total > 0 ? Number((total).toFixed(1)).toLocaleString() + ' ' + monetaryUnit : '';
        }
        resolve();
      }).catch(err => {
        resolve();
      });
    });
  }

  getProcedureDetail(procedureId) {
    this.procedureService.getProcedureDetail(procedureId).subscribe(async data => {
      this.isSyncDBNLGSPTanDanBXD = data?.isSyncDBNLGSPTanDanBXD ? Number(data?.isSyncDBNLGSPTanDanBXD) : 0;
      this.isHasCodeLDXH = !!data.btxhcode;
    }, err => {
      console.log(err);
    });
  }


  getUserAccount() {
    this.keycloakService.loadUserProfile().then(user => {
      // tslint:disable-next-line: no-string-literal
      this.userId = user['attributes'].user_id[0];
      this.accountId = user['attributes'].account_id[0];
      this.userName =  user.username;
      this.userService.getUserInfo(this.userId).subscribe(data => {
        this.fullname = data.fullname;
      });
    });
  }

  
  getDossierTaskStatus() {
    let tagId = this.deploymentService.env.dossierTaskStatus.notAccepted.id;
    if(this.currentDossierStatus == 12){
      tagId = this.deploymentService.env.dossierTaskStatus.cancelDossier.id;
    }
    this.dossierService.getAgencyTag(tagId).subscribe(rs => {
      this.dossierTaskStatus.id = rs.id;
      this.dossierTaskStatus.name = rs.trans;
    }, err => {
      console.log(err);
    });
  }
  getDossierMenuTaskRemind() {
    let tagId = this.deploymentService.env.dossierMenuTaskRemind.notAccepted.id;
    // if(this.currentDossierStatus == 12){
    //   tagId = this.deploymentService.env.dossierMenuTaskRemind.cancelDossier.id;
    // }
    this.dossierService.getAgencyTag(tagId).subscribe(rs => {
      this.dossierMenuTaskRemind.id = rs.id;
      this.dossierMenuTaskRemind.name = rs.trans;
      this.statusName = rs?.trans.filter(t => t.languageId === Number(this.selectedLangId))?.name;
    }, err => {
      console.log(err);
    });
  }

  async onConfirm() {
    this.disableButton = true;
    console.log("this.uploadFileNames",this.uploadFileNames);
     // xóa dữ liệu file tạm
    //  this.uploadFileNames.forEach(element => {
    //   this.procedureService.deleteFile(element.id).subscribe(res => {
    //   }, err => {
    //     console.log(err);
    //   });
    // });
    // this.uploadFileNames = [];
    //

    if(this.isUploadResultProcessing){
      //upload file kết quả xử lý
      await this.putDossierAttachmentMulti("", 3, 'lang.word.result', '', '', this.uploadFileNames)
    }

    await this.dossierService.getDossierDetail(this.dossierId).toPromise().then(data => {
      this.dossierDetail = data;
    });
    if (this.isUserRQ && this.dossierDetail.dossierStatus?.id == '12')
    {
      this.commentContent = this.dossierDetail.dossierStatus?.comment;
    }
    let checkDossierStatus = true;
    if (this.dossierDetail.dossierStatus?.id == '6' || (this.checkRefusedDossierOnce && this.dossierDetail.dossierStatus?.id == '12' && this.dossierDetail.dossierTaskStatus.name === 'Không được tiếp nhận'))
    {
      this.disableButton = false;
      checkDossierStatus = false;
      const msgObj = {
        vi: 'Hồ sơ đã được thực hiện thao tác từ chối!',
        en: 'Dossier has been refused!'
      };
      this.snackbarService.openSnackBar(2, '', msgObj[this.selectedLang], 'warning_notification', this.config.expiredTime);
    }
    if (this.commentContent.trim() === ''){
      this.disableButton = false;
      const msgObj = {
        vi: 'Vui lòng nhập lý do từ chối!',
        en: 'Please enter a reason for not accepted!'
      };
      this.snackbarService.openSnackBar(2, '', msgObj[this.selectedLang], 'warning_notification', this.config.expiredTime);
    }
    if (this.isCKMaxlenght){
      this.disableButton = false;
      let msgObj;
      if (this.ckeditorMaxLength > 0) {
        msgObj = {
          vi: 'Nội dung không quá ' + this.ckeditorMaxLength + ' ký tự!',
          en: 'Content must not exceed ' + this.ckeditorMaxLength + ' characters!'
        };
      } else {
        msgObj = {
          vi: 'Nội dung không quá 500 ký tự!',
          en: 'Content must not exceed 500 characters!'
        };
      }
      this.snackbarService.openSnackBar(2, '', msgObj[this.selectedLang], 'warning_notification', this.config.expiredTime);
    }
    let checkRequireAttachment = true;
    if (this.requireAttachmentWhenRefuse && this.uploadFileNames.length === 0){
      this.disableButton = false;
      checkRequireAttachment = false;
      const msgObj = {
        vi: 'Vui lòng đính kèm tệp tin!',
        en: 'Please attach file!'
      };
      this.snackbarService.openSnackBar(2, '', msgObj[this.selectedLang], 'warning_notification', this.config.expiredTime);
    }
    if (!this.isCKMaxlenght && this.commentContent.trim() !== '' && checkRequireAttachment && checkDossierStatus) {
      this.putDossierRefuseWithComment();
    }
  }


  async putDossierRefuseWithComment(){
    const requestBodyObj = {
      dossierStatus: 6,
      comment: '',
      //attachment: this.uploadedImage,
      dossierTaskStatus: this.dossierTaskStatus,
      dossierMenuTaskRemind :  this.dossierMenuTaskRemind,
      refuse: 1,
      user: {
        id: this.userId,
        fullname: this.fullname,
        account: {
          id: this.accountId,
          username: [
            {
              value: this.userName
            }
          ]
        }
      },
      //canceledProcessing = false khong cap nhat duoc dossierStatus=dossierStatus cũ
      canceledProcessing: false
    };
    if (this.commentContent.trim() !== '') {
      this.postComment(this.commentContent.trim());
      requestBodyObj.comment = this.commentContent.trim();
    } else {
      const msgObj = {
        vi: 'Từ chối hồ sơ <b>' + this.dossierCode + '</b>',
        en: 'Dossier <b>' + this.dossierCode + '</b> has been refused!'
      };
      this.postComment(msgObj[this.selectedLang]);
      requestBodyObj.comment = msgObj[this.selectedLang];
    }

    // Todo Check here
    if(this.isSmsQNM) {
      const agencies = this.agencyService.getAgencies();
      const extend = {
        dossier:{
          id: this.dossierId,
          code: this.dossierCode,
        },
        agencies: agencies
      };
      this.notiService.changeSendSubject.next(
        {
          id: this.dossierDetail?.procedureProcessDefinition?.id,
          phone: this.dossierDetail?.applicant?.data?.phoneNumber,
          email: this.dossierDetail?.applicant?.data?.email,
          updateSend: true,
          renewContent: true,
          contentParams: {
            parameters: {
              sector: !!this.dossierDetail?.procedure?.sector?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name ? this.dossierDetail?.procedure?.sector?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name : '',
              procedure: !!this.dossierDetail?.procedure?.translate?.find(agency => agency.languageId === Number(this.selectedLangId))?.name ? this.dossierDetail?.procedure?.translate?.find(agency => agency.languageId === Number(this.selectedLangId))?.name : '',
              agency: !!this.dossierDetail?.agency?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name ? this.dossierDetail?.agency?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name : this.dossierDetail?.agency?.parent?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name,
              agencyId: !!this.dossierDetail?.agency?.id ? this.dossierDetail?.agency?.id : this.dossierDetail?.agency?.parent?.id,
              applicantFullname: this.dossierDetail?.applicant?.data?.fullname,
              officerFullname: '',
              subsystem: !!this.env?.subsystem?.name ? this.env?.subsystem?.name : "Hệ thống Một cửa điện tử",
              dossierCode: this.dossierDetail?.nationCode ? this.dossierDetail?.nationCode : this.dossierDetail?.code,
              nextTask: 'Từ chối',
              dossierSearchPadsvcUrl: (!!this.env?.domain?.padsvc ? this.env?.domain?.padsvc : "http://padsvctest.vncitizens.vn/vi/") + "dossier/public",
              dossierDetailPadsvcUrl: (!!this.env?.domain?.padsvc ? this.env?.domain?.padsvc : "http://padsvctest.vncitizens.vn/vi/") + "dossier/detail/" + this.dossierId,
              dossierSearchUrl: (!!this.env?.domain?.onegate ? this.env?.domain?.onegate : "http://padsvctest.vncitizens.vn/vi/") + "dossier/search/",
              dossierDetailUrl: (!!this.env?.domain?.onegate ? this.env?.domain?.onegate : "http://padsvctest.vncitizens.vn/vi/") + "dossier/search/" + this.dossierId,
              returnMethod: !!this.dossierDetail?.dossierReceivingKind?.name?.find(recieve => recieve.languageId === Number(this.selectedLangId))?.name ? this.dossierDetail?.dossierReceivingKind?.name?.find(recieve => recieve.languageId === Number(this.selectedLangId))?.name : '',
              receivingDate: '',
              appointmentDate: '',
              dossierFee: this.totalCost,
              reason: this.commentContentPlainText,
              extend: extend
            }
          }
        }
      );
    }
    else {
      this.notiService.changeSendSubject.next(
        {
          id: this.dossierDetail?.procedureProcessDefinition?.id,
          phone: this.dossierDetail?.applicant?.data?.phoneNumber,
          email: this.dossierDetail?.applicant?.data?.email,
          updateSend: true,
          renewContent: false,
          contentParams: {
            parameters: {
              sector: !!this.dossierDetail?.procedure?.sector?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name ? this.dossierDetail?.procedure?.sector?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name : '',
              procedure: !!this.dossierDetail?.procedure?.translate?.find(agency => agency.languageId === Number(this.selectedLangId))?.name ? this.dossierDetail?.procedure?.translate?.find(agency => agency.languageId === Number(this.selectedLangId))?.name : '',
              agency: !!this.dossierDetail?.agency?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name ? this.dossierDetail?.agency?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name : this.dossierDetail?.agency?.parent?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name,
              agencyId: !!this.dossierDetail?.agency?.id ? this.dossierDetail?.agency?.id : this.dossierDetail?.agency?.parent?.id,
              applicantFullname: this.dossierDetail?.applicant?.data?.fullname,
              officerFullname: '',
              subsystem: !!this.env?.subsystem?.name ? this.env?.subsystem?.name : "Hệ thống Một cửa điện tử",
              dossierCode: this.dossierDetail?.nationCode ? this.dossierDetail?.nationCode : this.dossierDetail?.code,
              nextTask: 'Từ chối',
              // IGATESUPP-63184
              nextStatus: !!this.env?.notify?.cancelDossier?.nextStatus ? this.env?.notify?.cancelDossier?.nextStatus : (this.dossierDetail?.dossierTaskStatus?.name ?? ''), // cho bo sung, trang thai hien tai cua ho so
              dossierStatus: !!this.env?.notify?.cancelDossier?.dossierStatus ? this.env?.notify?.cancelDossier?.dossierStatus : (this.dossierDetail?.dossierStatus?.name?.find(t => t.languageId === Number(this.selectedLangId))?.name ?? ''), // cho bo sung, trang thai hien tai cua ho so
              dossierSearchPadsvcUrl: (!!this.env?.domain?.padsvc ? this.env?.domain?.padsvc : "http://padsvctest.vncitizens.vn/vi/") + "dossier/public",
              dossierDetailPadsvcUrl: (!!this.env?.domain?.padsvc ? this.env?.domain?.padsvc : "http://padsvctest.vncitizens.vn/vi/") + "dossier/detail/" + this.dossierId,
              dossierSearchUrl: (!!this.env?.domain?.onegate ? this.env?.domain?.onegate : "http://padsvctest.vncitizens.vn/vi/") + "dossier/search/",
              dossierDetailUrl: (!!this.env?.domain?.onegate ? this.env?.domain?.onegate : "http://padsvctest.vncitizens.vn/vi/") + "dossier/search/" + this.dossierId,
              returnMethod: !!this.dossierDetail?.dossierReceivingKind?.name?.find(recieve => recieve.languageId === Number(this.selectedLangId))?.name ? this.dossierDetail?.dossierReceivingKind?.name?.find(recieve => recieve.languageId === Number(this.selectedLangId))?.name : '',
              receivingDate: '',
              appointmentDate: '',
              dossierFee: this.totalCost,
              reason: this.commentContentPlainText
            }
          }
        }
      );
    }

    let isQNI = this.env?.OS_QNI?.canceledProcessing;
    if (isQNI){
      requestBodyObj.dossierStatus = 12;
      requestBodyObj.canceledProcessing = true;
    }

    const requestBody = JSON.stringify(requestBodyObj, null, 2);

    this.dossierService.putDossierStatusWithComment(this.dossierId, requestBody).subscribe(data => {
      if (data.affectedRows === 1) {
        if (this.config.receivePromotionalProcedureUpdated === 1 || this.config.receivePromotionalProcedureUpdated === '1') {
          this.dossierService.getDossierDetail(this.dossierId).subscribe(data => {
            if (data.sync !== undefined) {
              const dataRequest: any = {};
              const agency = JSON.parse(localStorage.getItem('userAgency'));
              if (agency !== null && agency !== undefined) {
                dataRequest.agencyId = agency.id;
              } else {
                dataRequest.agencyId = this.config.rootAgency.id;
              }

              if (this.config.subsystemId !== null && this.config.subsystemId !== undefined) {
                dataRequest.subsystemId = this.config.subsystemId;
              }
              // dataRequest.configId = '5f8950dce35ad03536550c38';

              dataRequest.agencyCode = data.agency.id;
              dataRequest.status = 5;
              dataRequest.code = data.code;
              dataRequest.sourceCode = data.sync.sourceCode;
              dataRequest.comment = this.commentContent.trim();
              dataRequest.date = this.datePipe.transform(tUtils.newDate(), 'yyyyMMddHHmmss');
              this.dossierService.postSynchronizePromotionStatus(dataRequest).subscribe(data => {
              });
              if(this.isSyncDBNLGSPTanDanBXD === 1 && !this.isSyncConstructKTM && !this.isSyncConstructHPG){                   
                this.syncPostReceiveInforFileFromLocal();
              }
              if(this.isSyncConstructKTM && this.isSyncDBNLGSPTanDanBXD === 1){
                this.syncDossierStatusConstructKTM();
              }
              if(this.isSyncConstructHPG && this.isSyncDBNLGSPTanDanBXD === 1){
                this.syncDossierStatusConstructHPG();
              }

            }
          });


        }

        if(this.ktmEnableSyncToBLDTBXH){
          this.dossierService.getDossierDetail(this.dossierId).subscribe(async dossierData => {
            const procedureData = await this.procedureService.getProcedureDetail(dossierData?.procedure?.id).toPromise();
            if(this.socialProtectionKTMService.checkIfDossierNeedSyncBTXH(procedureData)){
              this.syncDossierStatusKTMToBLDTBXH(dossierData);
            }
          });
        }
        if(this.enableSyncDossierTNMTQni){
          this.adapterService.syncDossierTNMTQni(this.integratedTnmtConfigIdQni, this.dossierId).subscribe(res => {
          }, err => {
          });
        }
        // Lay thong tin ho so de dong bo len Bo LDTBXH
        this.dossierService.getDossierDetail(this.dossierId).subscribe(async dossierData => {
          if (this.enableSyncToBLDTBXH && this.isHasCodeLDXH) {
            this.syncDossierStatusToBLDTBXH(dossierData);
          }
          if (this.isHasCodeLDXH && this.deploymentService?.env?.OS_HBH?.enableLDXHTriNam) {
            this.trinamService.syncTaskLDXH(dossierData);
          }
        });

        // Todo check here
        this.notiService.confirmSendSubject.next({
          confirm: true,
          renewContent: false,
          component: 'refuse'
        });
        if(this.isBoTNMTQNM || this.isBoTNMTHCM || this.isBoTNMTGLI){
          this.dossierService.updateDossierBoTNMT(this.dossierId).subscribe(test => {});
        }
        if (this.deploymentService.env?.OS_HBH?.isTnmtTriNam) {
          this.adapterService.sendTNMTTriNam(this.dossierId);
        }
        this.dialogRef.close(true);
        const dataLog = {
          dossierId: this.dossierId,
          code: this.dossierCode,
          body: requestBody
        };
        this.logmanService.postUserEventsLog('refuseDossier', dataLog).subscribe();
        if (this.deploymentService?.env?.OS_BTTTT?.isEnabledNotify) {
          // IGATESUPP-62366 thong bao cho nguoi dan
          this.dossierService.noticeDossier(this.dossierId, {comment: 'Lý do:&nbsp;' + this.commentContent.trim()}).subscribe(res => {});
        }

        if(this.isDVCLTEnable && this.dossierDetail?.isDVCLT && this.dossierDetail?.nationCode != null) {
             this.capNhatTrangThaiDVCLT(6);
      }

      } else {
        this.dialogRef.close(false);
      }
    }, err => {
      this.dialogRef.close(false);
    });

  }

  async capNhatTrangThaiDVCLT(trangThaiDVCLT: number){
    console.log(this.dossierDetail);
    const userExperienceAgency = JSON.parse(localStorage.getItem('userExperienceAgency'));
    const UID = localStorage.getItem('UID');
    const user = await this.humanService.getUserInfo3(UID).toPromise();

    const currentDate = new Date();
    let dataBody = {
      maTTHC: this.dossierDetail?.procedure?.dvcltProcedureCode,
      soHoSoLT: this.dossierDetail?.nationCode,
      coQuanXuLy: 5,
      maHoSo: this.dossierDetail?.code,
      trangThai: trangThaiDVCLT,
      thoiGianThucHien: this.datePipe.transform(currentDate, 'dd/MM/yyyy HH:mm:ss'),
      ghiChu: !!this.commentContent ? this.commentContent.substring(3,this.commentContent.length - 4) : "Từ chối",
      nguoiXuLy: user?.fullname,
      chucDanh: userExperienceAgency?.position?.name,
      phongBanXuLy: this.dossierDetail?.agency?.parent?.name[0]?.name,
      noiDungXuLy: !!this.commentContent ? this.commentContent.substring(3,this.commentContent.length - 4) : "Không có nội dung xử lý",
      // ngayBatDau: this.dossierDetail?.acceptedDate ? this.datePipe.transform(this.dossierDetail?.acceptedDate, 'dd/MM/yyyy HH:mm:ss') : this.datePipe.transform(this.dossierDetail?.appliedDate, 'dd/MM/yyyy HH:mm:ss'),
      // ngayKetThucTheoQuyDinh: this.dossierDetail?.appointmentDate ? this.datePipe.transform(this.dossierDetail?.appointmentDate, 'dd/MM/yyyy HH:mm:ss') : this.datePipe.transform(this.dossierDetail?.appliedDate, 'dd/MM/yyyy HH:mm:ss'),
      // ngayHenTraTruoc: this.dossierDetail?.appointmentDate ? this.datePipe.transform(this.dossierDetail?.appointmentDate, 'dd/MM/yyyy HH:mm:ss'): this.datePipe.transform(currentDate, 'dd/MM/yyyy HH:mm:ss'),
      // ngayHenTraMoi: this.datePipe.transform(currentDate, 'dd/MM/yyyy HH:mm:ss'),
      // hanBoSungHoSo: this.dossierDetail?.appointmentDate ? this.datePipe.transform(this.dossierDetail?.appointmentDate, 'dd/MM/yyyy HH:mm:ss'): this.datePipe.transform(currentDate, 'dd/MM/yyyy HH:mm:ss'),
      ketQuaXuLy:''
    };
    this.adapterService.capNhatTrangThaiDVCLT(dataBody).subscribe(res => {
      if(res){

      }
      
    }, err => {
    });
  }
  onDismiss() {
    // xóa dữ liệu file tạm
    this.uploadFileNames.forEach(element => {
      this.procedureService.deleteFile(element.id).subscribe(res => {
      }, err => {
        console.log(err);
      });
    });
    this.uploadFileNames = [];
    //
    this.dialogRef.close();
  }

  postComment(commentContent) {
    const msgObj = {
      vi: 'Lý do từ chối: ',
      en: 'Reason of refused: '
    };
    const content = {
      groupId: 2,
      itemId: this.dossierId,
      user: {
        id: this.userId,
        fullname: this.fullname
      },
      content: msgObj[this.selectedLang] + commentContent.trim(),
      file: this.uploadFileNames
    };
    const requestBody = JSON.stringify(content, null, 2);
    this.dossierService.postComment(requestBody).subscribe(data => { });
  }

  getPlainText( strSrc ) {
    let resultStr = "";

    // Ignore the <p> tag if it is in very start of the text
    if(strSrc.indexOf('<p>') == 0)
        resultStr = strSrc.substring(3);
    else
        resultStr = strSrc;

    // Replace <p> with two newlines
    resultStr = resultStr.replace(/<p>/gi, "\r\n\r\n");
    // Replace <br /> with one newline
    resultStr = resultStr.replace(/<br \/>/gi, "\r\n");
    resultStr = resultStr.replace(/<br>/gi, "\r\n");

    // Replace &nbsp; with space
    resultStr = resultStr.replace(/&nbsp;/gi, " ");

    //-+-+-+-+-+-+-+-+-+-+-+
    // Strip off other HTML tags.
    //-+-+-+-+-+-+-+-+-+-+-+

    return  resultStr.replace( /<[^<|>]+?>/gi,'' );
  }

  getPlainText1( strSrc ) {
    let resultStr = "";

    // Ignore the <p> tag if it is in very start of the text
    if(strSrc.indexOf('<p>') == 0)
        resultStr = strSrc.substring(3);
    else
        resultStr = strSrc;

    // Replace <p> with one newline
    resultStr = resultStr.replace(/<p>/gi, "\n"); // --> Diff between getPlainText && getPlainText1
    // Replace <br /> with one newline
    resultStr = resultStr.replace(/<br \/>/gi, "\r\n");
    resultStr = resultStr.replace(/<br>/gi, "\r\n");

    // Replace &nbsp; with space
    resultStr = resultStr.replace(/&nbsp;/gi, " ");
    //-+-+-+-+-+-+-+-+-+-+-+
    // Strip off other HTML tags.
    //-+-+-+-+-+-+-+-+-+-+-+

    return  resultStr.replace( /<[^<|>]+?>/gi,'' );
  }

  onCommentEditorChange(event) {
    this.commentContent = event.editor.getData();
    if (this.ckeditorMaxLength > 0) {
      if (event.editor.getData().trim().length > this.ckeditorMaxLength) {
        this.isCKMaxlenght = true;
      } else {
        this.isCKMaxlenght = false;
      }
    } else if (event.editor.getData().trim().length > 500) {
      this.isCKMaxlenght = true;
    } else {
      this.isCKMaxlenght = false;
    }

    this.commentContentPlainText = "";
    this.commentContentPlainText = this.getPlainText(this.commentContent).trim();
    if (this.deploymentService.env?.isTrimRefuseReasonText) {
      this.commentContent = this.removeExtraSpacesAndLine(this.commentContent);
    }
  }
    // DBN - BXD - dong bo thong hs qua LGSP Tan Dan
    syncPostReceiveInforFileFromLocal (){
      this.dossierService.getDossierDetail(this.dossierId).subscribe(data => {
        let dataApplicant =  data?.applicant?.data;
        let nhanThongTinHSTuDP = [];
        let thongTinNguoiNop = {
          HoTenNguoiNop : dataApplicant?.fullname, //*
          SoCMND : dataApplicant?.identityNumber, //*
          EmailNguoiNop: dataApplicant?.email,
          SoDienThoaiNguoiNop: dataApplicant?.phoneNumber, //*
          DiaChiThuongTruNguoiNop: dataApplicant?.address + ","
                                 + dataApplicant?.village?.label + ","
                                 + dataApplicant?.district?.label + ","
                                 + dataApplicant?.province?.label + ","
                                 + dataApplicant?.nation?.label, //*
          MaDoiTuong: ""
        };
        let giayToTrongQuaTrinhXuLy = {
          MaGiayTo: "",
          TenGiayTo: "",
          NoiDungBase64: "",
          DinhDangTepTin: "", // dinh dang .pdf, .doc, .png,...
          MoTa: "",
          LoaiGiayTo: ""
        }
        let hinhThucTraKetQua = 0;
        switch(data?.dossierReceivingKind?.id) {
          case '5f8968888fffa53e4c073ded':
          hinhThucTraKetQua = 0;
          break;
          case '604ecde877bed4110c1dd0d1':
            hinhThucTraKetQua = 1;
            break;
          case '6163f1e20f363b04cf60b224':
            hinhThucTraKetQua = 1;
            break;
          case '5f8969018fffa53e4c073dee':
            hinhThucTraKetQua = 1;
            break;
        }
        let arrThongTinTienTrinh = [];
        let dossierStatus = this.statusName;
        if(data?.task){
            data?.task.forEach(task => {
              if(task?.isCurrent === 1){
                let thongTinTienTrinh = {
                  NguoiXuLy: task?.assignee?.fullname, //*
                  ChucDanh: "",
                  ThoiDiemXuLy: this.datePipe.transform(task?.createdDate, 'yyyyMMddHHmmss'), //* yyyyMMddHHmmss
                  PhongBanXuLy: task?.agency?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name,
                  NoiDungXuLy:  task?.bpmProcessDefinitionTask?.remind ?  task?.bpmProcessDefinitionTask?.remind?.name : "",
                  NgayBatDau:   this.datePipe.transform(task?.createdDate, 'yyyyMMddHHmmss'), //yyyyMMddHHmmss
                  NgayKetThucTheoQuyDinh: task?.isCurrent === 0? this.datePipe.transform(task?.updatedDate, 'yyyyMMddHHmmss') : ""
                }
                arrThongTinTienTrinh.push(thongTinTienTrinh);
              }

          });
        }
        let dossierSync = {
          MaHoSo: data.code, //*
          TrangThaiHoSo: dossierStatus, // phu luc 2 *
          NgayTiepNhan: this.datePipe.transform(data?.acceptedDate, 'yyyyMMddHHmmss') ,// yyyyMMddHHmmss*
          NgayHenTraKetQua: this.datePipe.transform(data?.appointmentDate, 'yyyyMMddHHmmss'), //yyyyMMddHHmmss
          ThongTinNguoiNop : thongTinNguoiNop,
          GiayToTrongQuaTrinhXuLy : [],
          HinhThucTraKetQua: hinhThucTraKetQua, // 0: tra truc tiep 1: tra qua duong buu dien 2: tra kq truc tuyen
          ThongTinTienTrinh : arrThongTinTienTrinh,
          MaTTHC: data?.procedure?.code,
          TenTTHC: data?.procedure?.translate?.find(procedure => procedure.languageId === Number(this.selectedLangId))?.name ? data?.procedure?.translate?.find(procedure => procedure.languageId === Number(this.selectedLangId))?.name : '',
        }
        //nhanThongTinHSTuDP.push(dossierSync);
        let params = "agencyId=" + data?.agency.id + "&subsystemId=" + this.config?.subsystemId ; // &configId
        this.dossierService.postReceiveInforFileFromLocal(params, dossierSync).subscribe(async data => {
          console.log("===postReceiveInforFileFromLocal===");
          console.log(data);
        }, er => {
          console.log(er);
        })
      }, err => {
        console.log(err);
      });
    }
    syncDossierStatusConstructKTM(){
      this.dossierService.getDossierDetail(this.dossierId).subscribe(data => {
        let dataApplicant =  data?.applicant?.data;
        let thongTinNguoiNop = {
          HoTenNguoiNop : dataApplicant?.fullname, //*
          SoCMND : dataApplicant?.identityNumber, //*
          EmailNguoiNop: dataApplicant?.email,
          SoDienThoaiNguoiNop: dataApplicant?.phoneNumber, //*
          DiaChiThuongTruNguoiNop: dataApplicant?.address + ","
                                 + dataApplicant?.village?.label + ","
                                 + dataApplicant?.district?.label + ","
                                 + dataApplicant?.province?.label + ","
                                 + dataApplicant?.nation?.label, //*
          MaDoiTuong: ""
        };
        
        let hinhThucTraKetQua = 0;
        switch(data?.dossierReceivingKind?.id) {
          case '5f8968888fffa53e4c073ded':
          hinhThucTraKetQua = 0;
          break;
          case '604ecde877bed4110c1dd0d1':
            hinhThucTraKetQua = 1;
            break;
          case '6163f1e20f363b04cf60b224':
            hinhThucTraKetQua = 1;
            break;
          case '5f8969018fffa53e4c073dee':
            hinhThucTraKetQua = 1;
            break;
        }
        let arrThongTinTienTrinh = [];
        if(data?.task){
            data?.task.forEach(task => {
              if(task?.isCurrent === 1){
                let thongTinTienTrinh = {
                  NguoiXuLy: task?.assignee?.fullname, //*
                  ChucDanh: "",
                  ThoiDiemXuLy: this.datePipe.transform(task?.createdDate, 'yyyyMMddHHmmss'), //* yyyyMMddHHmmss
                  PhongBanXuLy: task?.agency?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name,
                  NoiDungXuLy:  task?.bpmProcessDefinitionTask?.remind ?  task?.bpmProcessDefinitionTask?.remind?.name : "",
                  NgayBatDau:   this.datePipe.transform(task?.createdDate, 'yyyyMMddHHmmss'), //yyyyMMddHHmmss
                  NgayKetThucTheoQuyDinh: task?.isCurrent === 0? this.datePipe.transform(task?.updatedDate, 'yyyyMMddHHmmss') : ""
                }
                arrThongTinTienTrinh.push(thongTinTienTrinh);
              }
  
          });
        }
        let giayToTrongQuaTrinhXuLy=[];
        let filename="";
        for (let i = 0; i < this.uploadFileNames.length; i++) {
          filename=this.uploadFileNames[i].filename;
          const nameDoc = filename.split(".")[0];
          const extension = filename.split(".").pop();
          const base64Filename = btoa(unescape(encodeURIComponent(filename)));
          giayToTrongQuaTrinhXuLy.push({
            MaGiayTo: null,
            TenGiayTo: nameDoc,
            NoiDungBase64: base64Filename,
            DinhDangTepTin:extension, // dinh dang .pdf, .doc, .png,...
            MoTa: null,
            LoaiGiayTo: "2"
          })
        } 
        let maHoSo = data.code
        if(maHoSo.includes("000.00")){
            this.codeMap = maHoSo;
          }else{      
            const inputString = maHoSo;
            const parts = inputString.split("-");
            const prefixParts = parts[0].split(".");
            const prefix = `000.00.${prefixParts[1]}.${prefixParts[0]}`;
            this.codeMap = `${prefix}-${parts[1]}-${parts[2]}`;  
          }
        let originalString = data.procedure.code;
        let result = originalString.split(".")[0] + '.' + originalString.split(".")[1];
        let dossierSync = {   
        data: [
          {
              MaHoSo: this.codeMap,
              MaTTHC: result,
              NgayTiepNhan: this.datePipe.transform(new Date(), 'yyyyMMddHHmmss'),
              NgayHenTraKetQua: this.datePipe.transform(data?.appointmentDate, 'yyyyMMddHHmmss'),
              TrangThaiHoSo: "3",
              ThongTinNguoiNop: thongTinNguoiNop,
              GiayToTrongQuaTrinhXuLy:giayToTrongQuaTrinhXuLy,
              HinhThucTraKetQua: hinhThucTraKetQua,
              ThongTinTienTrinh: arrThongTinTienTrinh             
          }
      ]
    }     
        let config = this.constructConfigId// &configId
        console.log(dossierSync)
        this.adapterService.syncDossierConstructKTM(config,dossierSync).subscribe(async data=>{
          console.log(data);
      })
      }, err => {
        console.log(err);
      });
    }
    syncDossierStatusConstructHPG(){
      this.dossierService.getDossierDetail(this.dossierId).subscribe(data => {
        let dataApplicant =  data?.applicant?.data;
        let thongTinNguoiNop = {
          HoTenNguoiNop : dataApplicant?.ownerFullname, //*
          SoCMND : dataApplicant?.identityNumber, //*
          EmailNguoiNop: dataApplicant?.email,
          SoDienThoaiNguoiNop: dataApplicant?.phoneNumber, //*
          DiaChiThuongTruNguoiNop: dataApplicant?.address + ","
                                 + dataApplicant?.village?.label + ","
                                 + dataApplicant?.district?.label + ","
                                 + dataApplicant?.province?.label + ","
                                 + dataApplicant?.nation?.label, //*
          MaDoiTuong: ""
        };
        
        let hinhThucTraKetQua = 0;
        switch(data?.dossierReceivingKind?.id) {
          case '5f8968888fffa53e4c073ded':
          hinhThucTraKetQua = 0;
          break;
          case '604ecde877bed4110c1dd0d1':
            hinhThucTraKetQua = 1;
            break;
          case '6163f1e20f363b04cf60b224':
            hinhThucTraKetQua = 1;
            break;
          case '5f8969018fffa53e4c073dee':
            hinhThucTraKetQua = 1;
            break;
        }
        let arrThongTinTienTrinh = [];
        if(data?.task){
            data?.task.forEach(task => {
              if(task?.isCurrent === 1){
                let thongTinTienTrinh = {
                  NguoiXuLy: task?.assignee?.fullname, //*
                  ChucDanh: "",
                  ThoiDiemXuLy: this.datePipe.transform(task?.createdDate, 'yyyyMMddHHmmss'), //* yyyyMMddHHmmss
                  PhongBanXuLy: task?.agency?.name?.find(agency => agency.languageId === Number(this.selectedLangId))?.name,
                  NoiDungXuLy:  task?.bpmProcessDefinitionTask?.remind ?  task?.bpmProcessDefinitionTask?.remind?.name : "",
                  NgayBatDau:   this.datePipe.transform(task?.createdDate, 'yyyyMMddHHmmss'), //yyyyMMddHHmmss
                  NgayKetThucTheoQuyDinh: task?.isCurrent === 0? this.datePipe.transform(task?.updatedDate, 'yyyyMMddHHmmss') : ""
                }
                arrThongTinTienTrinh.push(thongTinTienTrinh);
              }
  
          });
        }
        let giayToTrongQuaTrinhXuLy=[];
        let filename="";
        for (let i = 0; i < this.uploadFileNames.length; i++) {
          filename=this.uploadFileNames[i].filename;
          const nameDoc = filename.split(".")[0];
          const extension = filename.split(".").pop();
          const base64Filename = btoa(unescape(encodeURIComponent(filename)));
          giayToTrongQuaTrinhXuLy.push({
            MaGiayTo: null,
            TenGiayTo: nameDoc,
            NoiDungBase64: base64Filename,
            DinhDangTepTin:extension, // dinh dang .pdf, .doc, .png,...
            MoTa: null,
            LoaiGiayTo: "2"
          })
        } 
        let maHoSo = data.code
        let originalString = data.procedure.code;
        let result = originalString.split(".")[0] + '.' + originalString.split(".")[1];
        let dossierSync = {   
        data: [
          {
              MaHoSo: maHoSo,
              MaTTHC: result,
              NgayTiepNhan: this.datePipe.transform(new Date(), 'yyyyMMddHHmmss'),
              NgayHenTraKetQua: this.datePipe.transform(data?.appointmentDate, 'yyyyMMddHHmmss'),
              TrangThaiHoSo: "3",
              ThongTinNguoiNop: thongTinNguoiNop,
              GiayToTrongQuaTrinhXuLy:giayToTrongQuaTrinhXuLy,
              HinhThucTraKetQua: hinhThucTraKetQua,
              ThongTinTienTrinh: arrThongTinTienTrinh             
          }
      ]
    }     
      this.adapterService.syncDossierConstructHPG(dossierSync).subscribe(async data=>{
        console.log(data);
    })
      }, err => {
        console.log(err);
      });
    }

    uploadFileExport(file, accountId) {
      this.procedureService.uploadMultiFile(file, accountId).subscribe(data => {
        this.uploadFileNames.push(data[0]);
        console.log("this.uploadFileNames",this.uploadFileNames);
      }, err => {
        console.log(err);
      });
    }
    // ========= file
    uploadMultiFile(file, accountId) {
      this.procedureService.uploadMultiFile(file, accountId).subscribe(data => {
        this.uploadedImage = data;
        this.putDossierRefuseWithComment();
      }, err => {
        console.log(err);
      });
    }
    async onSelectFile(event) {
      if (event.target.files && event.target.files[0]) {
        for (const i of event.target.files) {
          if (i.size >= this.maxFileSize * 1024 * 1024) {
            const msgObj = {
              vi: 'Kích thước tệp tin quá lớn, tên tệp: ' + i.name,
              en: 'The file is too large, file name: ' + i.name
            };
            this.snackbarService.openSnackBar(0, '', msgObj[this.selectedLang], 'error_notification', this.config.expiredTime);
            return;
          }
          if (this.acceptFileType.filter(t => t === i.type.toLowerCase()).length > 0) {
            
            let newFile = [];
            newFile.push(i);
            this.files.push(i);
            const extension = i.name.substring(i.name.lastIndexOf('.')).split('.').pop();
            this.urls.push(this.getFileIcon(extension));

            const reader = new FileReader();
            reader.onload = () => {
              this.uploaded = true;
            };
            if (i.name.length > 20) {
              const startText = i.name.substr(0, 5);
              const shortText = i
                .name
                .substring(i.name.length - 7, i.name.length);
              this.fileNames.push(startText + '...' + shortText);
              this.fileNamesFull.push(i.name);
            } else {
              this.fileNames.push(i.name);
              this.fileNamesFull.push(i.name);
            }
            reader.readAsDataURL(i);
            if (this.files.length > 0) {
              await this.uploadFileExport(newFile, this.accountId);
           }
          }
          else {
            const msgObj = {
              vi: 'Không hỗ trợ loại tệp tin ',
              en: 'File type is not supported '
            };
            // tslint:disable-next-line: max-line-length
            this.snackbarService.openSnackBar(0, '', msgObj[this.selectedLang] + i.type.substring(i.type.lastIndexOf('/') + 1).toUpperCase(), 'error_notification', this.config.expiredTime);
          }
        }
      }
    }

    downloadFileExport(index: number){
      let file = this.uploadFileNames[index];
      this.downloadFile(file.id, file.filename);
    }

    routerLink(file)
  {
    let name = file.filename ? file.filename: file.name;
    name = name.toUpperCase();
    if(name.indexOf('.JPG') >= 0 || name.indexOf('.JPEG') >= 0 || name.indexOf('.PNG') >= 0)
      return ['/viewer-image/' + file.id, {dossierId: this.dossierId }];

    return ['/viewer/' + file.id, {dossierId: this.dossierId }];
  }
  
    downloadFile(id, filename) {
      this.procedureService.downloadFile(id, this.dossierId).subscribe(data => {
        const dataType = data.type;
        const binaryData = [];
        binaryData.push(data);
        const downloadLink = document.createElement('a');
        downloadLink.href = window.URL.createObjectURL(new Blob(binaryData, { type: dataType }));
        downloadLink.setAttribute('download', filename);
        document.body.appendChild(downloadLink);
        downloadLink.click();
      }, err => {
        const msgObj = {
          vi: 'Không tìm thấy file!',
          en: 'File not found!'
        };
        this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
        console.log(err);
      });
    }
  

    removeItem(index: number) {
      this.procedureService.deleteFile(this.uploadFileNames[index].id).subscribe(res => {
      }, err => {
        console.log(err);
      });
      this.uploadFileNames.splice(index, 1);
      this.urls.splice(index, 1);
      this.fileNames.splice(index, 1);
      this.fileNamesFull.splice(index, 1);
      this.files.splice(index, 1);
      this.blankVal = '';
    }

    getFileIcon(ext) {
      return this.config.cloudStaticURL + 'icon/files/300x300/' + ext + '.png';
    }

    async getDossierStatusSyncToBLDTBXH(dossierData){
      const userExperienceAgency = JSON.parse(localStorage.getItem('userExperienceAgency'));
      const UID = localStorage.getItem('UID');
      const user = await this.humanService.getUserInfo3(UID).toPromise();
      this.dossierToSyncStatusBLDTBXH = {
        "MaHoSo": dossierData.code,
        "TaiKhoanXuLy": (!!user?.account?.username && user?.account?.username.length > 0) ? user?.account?.username[0]?.value : "",
        "NguoiXuLy": user?.fullname,
        "ChucDanh": userExperienceAgency?.position?.name,
        "ThoiDiemXuLy": this.datePipe.transform(new Date(), 'dd/MM/yyyy'),
        // "DonViXuLy": userExperienceAgency?.agency?.name,
        "DonViXuLy": dossierData.agency?.parent?.name[0]?.name,
        "NoiDungXuLy": !!this.commentContent ? this.commentContent.substring(3,this.commentContent.length - 4) : "Không có nội dung xử lý",
        // "StatusId": this.socialProtectionService.mappingStatusId(dossierData.dossierStatus?.id, dossierData.dossierMenuTaskRemind?.id),
        "StatusId" : "",
        "NgayBatDau": this.datePipe.transform(dossierData.appliedDate, 'dd/MM/yyyy'),
        // "NgayBatDau": "",
        // "NgayKetThucTheoQuyDinh": this.datePipe.transform(dossierData.appointmentDate, 'dd/MM/yyyy'),
        "NgayKetThucTheoQuyDinh": this.datePipe.transform(dossierData.appliedDate, 'dd/MM/yyyy'),
        "UseridCreated": null,
        "UseridEdited": null,
        "DateCreated": null,
        "DateEdited": null
      }
    }

    async syncDossierStatusToBLDTBXH(dossierData){
      await this.getDossierStatusSyncToBLDTBXH(dossierData);
      if(this.dossierToSyncStatusBLDTBXH) {
        this.socialProtectionService.mappingStatusId(dossierData.dossierStatus?.id, dossierData.dossierMenuTaskRemind?.id).then(res => {
          const status = res;

          this.dossierToSyncStatusBLDTBXH.StatusId = status;
          const body = {
            "configId" : this.syncToBLDTBXHConfigId,
            "statusDossier" : this.dossierToSyncStatusBLDTBXH,
          }
          this.adapterService.syncDossierStatusToBLDTBXH(body).subscribe(res => {
            if(res){

            }
          });
        });
      }
    }

    async getDossierStatusSyncKTMToBLDTBXH(dossierData){
      const userExperienceAgency = JSON.parse(localStorage.getItem('userExperienceAgency'));
      const UID = localStorage.getItem('UID');
      const user = await this.humanService.getUserInfoKTM(UID).toPromise();
      this.ktmDossierToSyncStatusBLDTBXH = {
        "MaHoSo": dossierData.code,
        "TaiKhoanXuLy": (!!user?.account?.username && user?.account?.username.length > 0) ? user?.account?.username[0]?.value : "",
        "NguoiXuLy": user?.fullname,
        "ChucDanh": userExperienceAgency?.position?.name,
        "ThoiDiemXuLy": this.datePipe.transform(new Date(), 'dd/MM/yyyy'),
        // "DonViXuLy": userExperienceAgency?.agency?.name,
        "DonViXuLy": dossierData.agency?.parent?.name[0]?.name,
        "NoiDungXuLy": !!this.commentContent ? this.commentContent.substring(3,this.commentContent.length - 4) : "Không có nội dung xử lý",
        // "StatusId": this.socialProtectionService.mappingStatusId(dossierData.dossierStatus?.id, dossierData.dossierMenuTaskRemind?.id),
        "StatusId" : "",
        "NgayBatDau": this.datePipe.transform(dossierData.appliedDate, 'dd/MM/yyyy'),
        // "NgayBatDau": "",
        // "NgayKetThucTheoQuyDinh": this.datePipe.transform(dossierData.appointmentDate, 'dd/MM/yyyy'),
        "NgayKetThucTheoQuyDinh": this.datePipe.transform(dossierData.appliedDate, 'dd/MM/yyyy'),
        "UseridCreated": null,
        "UseridEdited": null,
        "DateCreated": null,
        "DateEdited": null
      }
    }

    async syncDossierStatusKTMToBLDTBXH(dossierData){
      await this.getDossierStatusSyncKTMToBLDTBXH(dossierData);
      if(this.ktmDossierToSyncStatusBLDTBXH) {
        this.socialProtectionKTMService.mappingStatusId(dossierData.dossierStatus?.id, dossierData.dossierMenuTaskRemind?.id).then(res => {
          const status = res;

          this.ktmDossierToSyncStatusBLDTBXH.StatusId = status;
          const body = {
            "configId" : this.ktmSyncToBLDTBXHConfigId,
            "statusDossier" : this.ktmDossierToSyncStatusBLDTBXH,
          }
          this.adapterService.syncDossierStatusKTMToBLDTBXH(body).subscribe(res => {
            if(res){

            }
          });
        });
      }
    }

    getProcedureProcessDetail(prDefId) {
      return new Promise<void>((resolve) => {
        this.procedureService.getProcedureProcessDetail(prDefId).subscribe((data) => {
          this.procedureProcessDetail = data;
          resolve();
        }, (err) => {
          const msgObj = {
            vi: 'Không tìm thấy quy trình cho hồ sơ này!',
            en: 'No process found for this dossier!',
          };
          this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
          resolve();
        });
      });
    }

    getListConfigTemplate() {
      const procedureId = this.dossierDetail?.procedure?.id;
      const agencyId = JSON.parse(localStorage.getItem('userAgency')).id;
      const hasSignedFile = this.dossierDetail?.signedTemplateFile?.length > 0;

      const listConfigTemplate = [];
      const searchString = '?procedure-id=' + procedureId + '&agency-id=' + agencyId;
      this.procedureService.getListConfigTemplate(searchString).subscribe(async data => {
        for (const configTemplate of data) {
          if (configTemplate.location === 0 || configTemplate.location === 1 || configTemplate.location === null || typeof configTemplate.location === 'undefined') {
            const detailConfigTemplate = await this.getDetailTemplate(configTemplate.id);
            if (!!detailConfigTemplate) {
              const itemList = [];
              if(hasSignedFile) {
                for (const template of this.dossierDetail[0].signedTemplateFile) {
                  if(detailConfigTemplate.id == template.id) {
                    for (const value of template.fileSign) {
                      itemList.push({
                        fileSignId: value.id,
                        fileSignName: value.fileSignName,
                        check: false
                      });
                    }
                  }
                }
              }
              const item = { fileSign: itemList };
              const obj = Object.assign({}, configTemplate, item);
              listConfigTemplate.push(obj);
            }
          }
        }
  
        this.listConfigTemplate = listConfigTemplate;
      });
    }

    getDetailTemplate(id) {
      return new Promise<any>(resolve => {
        this.configService.getDetailTemplate(id).subscribe(detail => {
          resolve(detail);
        }, err => {
          resolve(null);
        });
      });
    }

    editFile(idDetailFile, idFile) {
      for (const configTemplate of this.listConfigTemplate) {
        if (configTemplate.id === idDetailFile) {
          for (const value of configTemplate.fileSign) {
            if (value.fileSignId === idFile) {
              value.check = !value.check;
            }
          }
        }
      }
    }

    createPrintTemplate(reportType, template?) {
      let url = '';
      let templateId = template?.id ? template.id : null;
      this.keycloakService.getToken().then(token => {
        const taskId = '';
        let data = {};
        if(!!this.enablePrintBillNew){
          data = {
            "report": reportType,
            "apiGateway": this.apiProviderService.getUrl('digo', 'padman'),
            "dossierId": this.dossierId,
            "id": (templateId != null ? templateId : '')
          };
        }else{
        url = this.config.birtviewerURL
          + 'output?__report=' + reportType
          + '&&displayNone=true&__dpi=96&__format=html&__pageoverflow=0&__overwrite=false'
          + '&token=' + token
          + '&apiGateway=' + this.apiProviderService.getUrl('digo', 'padman')
          + '&dossierId=' + this.dossierId
          + (templateId != null ? '&id=' + templateId : '');
        }
          const dialogData = new ConfirmPrintTemplateDialogModel(url, template, this.dossierId, this.config.dossierAttachedFileTagId, this.digitalSignature, taskId, false, this.enablePrintBillNew, data);
          const dialogRef = this.dialog.open(PrintTemplateComponent, {
            minWidth: '55vw',
            maxHeight: '90vh',
            data: dialogData,
            autoFocus: false
          });
          dialogRef.afterClosed().subscribe(dialogResult => {
            if(dialogResult == true) {
              this.getListConfigTemplate();
            }
          });
      });
    }

    async changeFile(type){
      if (this.commentContent.trim() === ''){
        const msgObj = {
          vi: 'Vui lòng nhập lý do từ chối!',
          en: 'Please enter a reason for not accepted!'
        };
        this.snackbarService.openSnackBar(2, '', msgObj[this.selectedLang], 'warning_notification', this.config.expiredTime);
        return
      }
      if (this.isCKMaxlenght){
        let msgObj;
        if (this.ckeditorMaxLength > 0) {
          msgObj = {
            vi: 'Nội dung không quá ' + this.ckeditorMaxLength + ' ký tự!',
            en: 'Content must not exceed ' + this.ckeditorMaxLength + ' characters!'
          };
        } else {
          msgObj = {
            vi: 'Nội dung không quá 500 ký tự!',
            en: 'Content must not exceed 500 characters!'
          };
        }
        this.snackbarService.openSnackBar(2, '', msgObj[this.selectedLang], 'warning_notification', this.config.expiredTime);
        return
      }
      if (!this.refuseTemplate) {
        const msgObj = {
          vi: 'Không tìm thấy mẫu file',
          en: 'Template not found'
        };
        this.snackbarService.openSnackBar(0, '', msgObj[this.selectedLang], 'error_notification', this.config.expiredTime);
        return;
      }
      const url = this.refuseTemplate;
      let blob = await fetch(url).then(r => r.blob());
      let address = this.dossierDetail?.applicant?.data?.address ? this.dossierDetail?.applicant?.data?.address :"";
      let village = this.dossierDetail?.applicant?.data?.village?.label ? this.dossierDetail?.applicant?.data?.village?.label : "";
      let district = this.dossierDetail?.applicant?.data?.district?.label ? this.dossierDetail?.applicant?.data?.district?.label : "";
      let province = this.dossierDetail?.applicant?.data?.province?.label ? this.dossierDetail?.applicant?.data?.province?.label : "";
      const value = {
        "parentName": this.parentAgencyName,
        "agencyName":this.agencyName,
        "day": tUtils.newDate().getDate(),
        "month": tUtils.newDate().getMonth() + 1,
        "year" : tUtils.newDate().getFullYear(),
        "fullName": this.dossierDetail?.applicant?.data?.fullname,
        "code": this.dossierDetail?.code,
        "address": address + ', '+ village + ', ' + district + ', ' + province,
        "phoneNumber": this.dossierDetail?.applicant?.data?.phoneNumber + ' ',
        "email": this.dossierDetail?.applicant?.data?.email,
        "reason": this.getPlainText1(this.commentContent),
        "nameOfficer": localStorage.getItem('tempUsername').trim()
      };
      const newComponent = [ 
        {
          "label" : "Tên cơ quan",
          "key": "parentName"
        },
        {
          "label" : "Tên đơn vị",
          "key": "agencyName"
        },
        {
          "label" : "ngày",
          "key": "day"
        },
        {
          "label" : "tháng",
          "key" : "month"
        },
        {
          "label" : "năm",
          "key" : "year"
        },
        {
          "label" : "Hồ sơ của",
          "key" : "fullName"
        },
        {
          "label" : "Nội dung yêu cầu giải quyết",
          "key" : "code"
        },
        {
          "label" : "địa chỉ",
          "key" : "address"
        },
        {
          "label" : "số điện thoại",
          "key" : "phoneNumber"
        },
        {
          "label" : "email",
          "key" : "email"
        },
        {
          "label": "Lý do",
          "key": "reason"
        },
        {
          "label": "Ông bà liên hệ với",
          "key": "nameOfficer"
        }];
      this.procedureService.getChangeFileFormEform(blob, JSON.stringify(value), JSON.stringify(newComponent), type).subscribe(async data => {
        const dataType = data.type;
        const binaryData = [];
        binaryData.push(data);
        const blob = new Blob(binaryData, { type: dataType });
        let fileName = `Mẫu file từ chối hồ sơ.${type}`;
        const file = new File(binaryData, fileName);
        if (file.size >= this.maxFileSize * 1024 * 1024) {
          const msgObj = {
            vi: 'Kích thước tệp tin quá lớn, tên tệp: ' + file.name,
            en: 'The file is too large, file name: ' + file.name
          };
          this.snackbarService.openSnackBar(0, '', msgObj[this.selectedLang], 'error_notification', this.config.expiredTime);
          return;
        }
        let newFile =[];
        newFile.push(file);
        this.files.push(file);
        this.urls.push(this.getFileIcon(type));
  
        const reader = new FileReader();
        reader.onload = () => {
          this.uploaded = true;
        };
        if (fileName.length > 20) {
          const startText = fileName.substring(0, 5);
          const shortText = fileName
            .substring(fileName.length - 7, fileName.length);
          this.fileNames.push(startText + '...' + shortText);
          this.fileNamesFull.push(fileName);
        } else {
          this.fileNames.push(fileName);
          this.fileNamesFull.push(fileName);
        }
        reader.readAsDataURL(blob);
        if (this.files.length > 0) {
           await this.uploadFileExport(newFile, this.accountId);
        }
     }, err => {
        const msgObj = {
          vi: 'Lỗi trong quá trình kết xuất file',
          en: 'Error occurred when extract file'
        };
        this.snackbarService.openSnackBar(0, '', msgObj[this.selectedLang], 'error_notification', this.config.expiredTime);
        return;
     });
    }

    removeExtraSpacesAndLine(htmlString: string): string {
      let cleanedHtml = htmlString.replace(/&nbsp;/g, ' ');
      cleanedHtml = cleanedHtml.replace(/<p>\s*<\/p>/gi, '');
      cleanedHtml = cleanedHtml.replace(/<p>\s+<\/p>/gi, '');
      cleanedHtml = cleanedHtml.replace(/\s+/g, ' ');
      return cleanedHtml.trim();
    }

    //ket qua xu ly
    async putDossierAttachmentMulti(doneClass, type, rbk, oldName, newName, dataFile){
      const rs = [];
      //get dossier online 
      let dossierOnline = await this.dossierService.getDossierDetailKGG(this.dossierId)

      if(dossierOnline?.attachment !== null && dossierOnline?.attachment !== undefined){
        const attachment = dossierOnline.attachment;
        if (attachment && attachment.length > 0) {
          //check id include in attachment
          attachment.forEach(element => {
            rs.push(element);
          });
        }
      }

      //check include file
      if(type === 5) {
        // delete file in rs if id include in dataFile
        for (let i = 0; i < dataFile.length; i++) {
          if(rs.findIndex(x => x.id === dataFile[i].id) !== -1){
            rs.splice(rs.findIndex(x => x.id === dataFile[i].id), 1);
          }
        }
      } else {
        if(dataFile.length > 0){
          for (let i = 0; i < dataFile.length; i++) {
            //check dataFile[i].id include in rs
            if(rs.findIndex(x => x.id === dataFile[i].id) === -1){
              rs.push({
                id: dataFile[i].id,
                filename: dataFile[i].filename,
                uuid: dataFile[i].uuid,
                size: dataFile[i].size,
                group: this.config.dossierResultFileTagId
              });
            }
          }
        } else {
          const msgObj = {
            vi: 'Vui lòng chọn file!',
            en: 'Please choose file!'
          };

          this.snackbarService.openSnackBar(0, '', msgObj[this.selectedLang], 'error_notification', this.config.expiredTime);
          return;
        }
      }

      const putBody = {
        attachment: rs
      };

     const requestBody = JSON.stringify(putBody, null, 2);
      this.dossierService.putDossierOnline(this.dossierId, requestBody).subscribe(data => {
        if (data.affectedRows === 1) {
          for (let j = 0; j < dataFile.length; j++) {
            this.postHistory(type, rbk, oldName, JSON.stringify({
              id: dataFile[j].id,
              type: 'file',
              name: dataFile[j].filename,
              uuid: dataFile[j].uuid,
            }));
          }
          // setTimeout(() => {
          //   this.showResult(doneClass);
          //   setTimeout(() => {
          //     this.hideSpinner(doneClass);
          //   }, 1000);
          // }, 1000);
  
        } else {
          const msgObj = {
            vi: 'Cập nhật thất bại!',
            en: 'Update failed!'
          };
          this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
          // setTimeout(() => {
          //   this.hideSpinner(doneClass);
          // }, 1000);
        }
      });
    }
    //
    postHistory(type, col, oldVal, newVal) {
      const body = {
        groupId: 1,
        itemId: this.dossierId,
        user: {
          id: this.accountId,
          name: this.userName
        },
        type,
        deploymentId: this.config.deploymentId,
        action: [
          {
            fieldNameRbk: col,
            originalValue: oldVal,
            newValue: newVal
          }
        ]
      };
      const requestBody = JSON.stringify(body, null, 2);
      this.dossierService.postHistory(requestBody).subscribe(data => {
        if (data.affectedRows === 1) {
          // Post successful
          console.log("Post successful");
        } else {
          // Post failed
          console.log("Post failed");
        }
      });
    }
}

export class RefuseDialogModel {
  constructor(public dossierId: string, public dossierCode: string, public dossierStatus?: number) {
  }
}
