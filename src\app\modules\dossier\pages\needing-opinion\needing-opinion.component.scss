@import "~src/styles/custom-material.scss";

::ng-deep .dossier-online-reception-one-level {
    @import "~src/styles/framework.scss";
    @import "~src/styles/custom.scss";
    @import "~src/styles/pagination.scss";
    @import "~src/styles/buttons.scss";

    .title-reminder {
        cursor: pointer;
        padding-right: 1rem;

        .title {
            font: 500 20px/32px Roboto, "Helvetica Neue", sans-serif;
        }

        .content {
            padding-left: 0.5rem;
            width: 100%;
            vertical-align: middle;
            align-items: center !important;
        }

        .content:hover {
            border-radius: 10px;
            background-color: #f1f1f1;
        }

        .count-task {
            color: red;
        }
    }

    .menu_reminder {
        padding-right: 1rem;

        .panel {
            overflow-y: auto;

            span {
                height: 100%;
            }

            .active {
                color: #ce7a58;
                background-color: #f4eadf;
            }

            .interfaceWorkHorizontalClass {
                width: unset !important;
            }

            #submenu {
                margin: 0 0 0 0;
                width: 100%;
                text-align: left;
                padding: 0.2em 1em;

                .submenuTitle {
                    //   display: block;
                    //   width: 100%;
                    line-height: 20px;
                    white-space: pre-wrap;
                    padding: 0.5em 0 0.5em 0.5em;
                }

                .count {
                    min-width: 20%;
                    background-color: #db3700 !important;
                    padding: 2px 10px !important;
                    border-radius: 15px !important;
                    color: white !important;
                    margin-right: 0.3em;
                }
            }

            #submenu:focus {
                background-color: 'blue';
            }
        }

        .mat-expansion-panel-body {
            padding: 5px 0 5px 0 !important;
        }
    }

    .form-search {
        @extend .cform-search;

        .advanced-box {
            .panel {
                background-color: #f9f9f9;
                box-shadow: none;
                display: flex;
                flex-wrap: wrap;
            }

            .mat-expansion-panel-body {
                padding: 0 !important;
            }
        }

        .advanced-button {
            margin: auto;
            color: #ce7a58;
            margin-bottom: 0.7em;
            cursor: pointer;

            .mat-icon {
                vertical-align: middle;
            }
        }

        .btn-search {
            @extend .t-btn-search;
        }
    }

    .tbl {
        @extend .cmat-table;
        @extend .bgc-white;

        table {
            width: 100%;
        }

        .mat-column-code {
            @extend .mat-column-procedure;
            @extend .f-0-0-10;
        }

        .mat-column-procedure {
            @extend .pdr-10px;
            @extend .pdl-10px;
            @extend .ofw-anywhere;
        }

        .mat-column-applicant {
            @extend .mat-column-procedure;

            ul {
                padding: 0;

                li:nth-child(1) {
                    letter-spacing: 0;
                    font-size: 15px;
                    font-weight: 500;
                    color: #1E2F41;
                }
            }
        }

        .mat-column-appliedDate {
            @extend .mat-column-procedure;
            @extend .f-0-0-15;
        }

        .mat-column-status {
            @extend .mat-column-procedure;
            @extend .f-0-0-15;
        }

        //tlqkhanh.hcm-IGATESUPP-68633
        .mat-column-organizationName {
            @extend .mat-column-procedure;
            @extend .f-0-0-15;
        }        
        //endof tlqkhanh.hcm-IGATESUPP-68633

        .cell {
            font-weight: 500;
            text-decoration: none;
            cursor: pointer;
            max-width: 100%;
            word-break: break-all;
        }

        .cell_code {
            color: #ce7a58;
            @extend .cell;
        }

        .cell_code_online {
            color: #ce7a58;
            @extend .cell;
        }

        .cell_code_direct {
            color: blue;
            @extend .cell;
        }

        .cell_code_other {
            color: green;
            @extend .cell;
        }
    }

    ::-webkit-scrollbar-track {
        box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.1);
        background-color: #F5F5F5;
        border-radius: 10px;
    }

    ::-webkit-scrollbar {
        width: 10px;
        background-color: #F5F5F5;
    }

    ::-webkit-scrollbar-thumb {
        border-radius: 10px;
        background-color: #FFF;
        background-image: -webkit-gradient(linear, 40% 0%, 75% 84%, from(#ce7a58), to(#ce7a58), color-stop(.6, #ce7a58))
    }

    @media screen and (max-width: 600px) {
        .tbl {
            .mat-header-row {
                display: none;
            }

            .mat-table {
                border: 0;
                vertical-align: middle;

                .mat-row {
                    border-bottom: 5px solid #ddd;
                    display: block;
                    min-height: unset;
                }

                .mat-cell {
                    border-bottom: 1px solid #ddd;
                    display: block;
                    font-size: 14px;
                    text-align: right;
                    margin-bottom: 4%;
                    padding: 0 0.5em;

                    &:before {
                        content: attr(data-label);
                        float: left;
                        font-weight: 500;
                        font-size: 14px;
                    }

                    &:last-child {
                        border-bottom: 0;
                    }

                    &:first-child {
                        margin-top: 4%;
                    }
                }
            }

            .mat-row {
                &:nth-child(even) {
                    background-color: unset;
                }

                &:nth-child(odd) {
                    background-color: unset;
                }
            }
        }
    }
}

.save2storage{
  padding-left: 18px;
  button{
    color: #4CA2DD;
  }
}

.save2storage .title{
  color: #1e2f41!important;
  font-weight: initial;
}

.no-hover-effect:hover {
  background-color: #FFFFFF;
}

.clear-search-nested {
    right: 0 !important;
    position: absolute !important;
    top: 0 !important;
  }

.search-nested {
    width: 80% !important;
    height: 50px !important;
    margin-left: 15px !important;
}

.primary-btn {
    background-color: #ce7a58;
    color: #fff;
    height: 2.8em;
}
.checkSeen{
    margin-left: 10px; 
    background-color:#FFFFFF ; 
    height: 2.8em;
}
::ng-deep .mat-tooltip  {
    white-space: pre-line !important;
  }
  .btn-advanced-search {
    border: 1px solid #ce7a58;
    color: #ce7a58;
    height: 3.6em;
    margin-top: 0.3em;
    text-wrap: wrap;
    line-height: normal;
    padding: 0px;
}

  .checkSeen {
    background-color: #fafafa;
} 
.titleGroup {
    font-size: 1.3em;
    font-weight: bold;
}

.titleCv {
    height: 35px;
    background-color: #DFF0FF;
    color: #1D86E7;
    text-align: center;
    padding: 5px 10px;
    border-top-right-radius: 10px;
    border-top-left-radius: 10px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.valueCv {
    height: 25px;
    padding: 5px 10px;
    text-align: right;
    font-size: 1.5em;
    font-weight: bold;
    display: flex;
    background-color: white;
    color: #1D86E7;
    justify-content: center;
    align-items: flex-end;
    border-bottom-left-radius: 10px;
    border-bottom-right-radius: 10px;
}
.notHasValue {
    background-color: #DFF0FF;
    color: #1D86E7;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 10px;
    border-bottom-left-radius: 10px;
    border-bottom-right-radius: 10px;
}
.listCv {
    border-radius: 25px;
    row-gap: 0.5em;
}
.titleGroup, .listCv {
    padding-bottom: 0.5em;
}
.itemCv{
    background-color: #0064bb94;
    border-radius: 10px;
    color: white;
    cursor: pointer;
}
.itemCv.active {
    box-sizing: border-box;
    -moz-box-sizing: border-box;
    -webkit-box-sizing: border-box;
    z-index: 10;
    outline: 4px solid #1D86E7;
    outline-offset: -4px;
}
.more-text-qbh {
    display: -webkit-box;
    -webkit-line-clamp: 7;
    -webkit-box-orient: vertical;
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    word-break: break-word;
}