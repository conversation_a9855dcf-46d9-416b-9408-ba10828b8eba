<button mat-icon-button class="close-button" (click)="onDismiss()">
    <mat-icon>close</mat-icon>
</button>
<h3 class="dialog_title" mat-dialog-title>Cho <PERSON> kiến</h3>
<div mat-dialog-content class="dialog_content">
    <div>
        <span>Nội dung: </span><span>{{summary}}</span>
    </div>
    <div>
        <span>Số hồ sơ: </span><span class="highlight">{{dossierCode}}</span>
    </div>
    <div class="commentEditor" fxLayout="column" fxLayout.xs="column" fxLayout.sm="column">
        <p class="lbl"><span>Nội dung trả lời</span><span style="color:#ce7a58;">&nbsp;*</span></p>
        <ckeditor debounce="10" [editor]="Editor" class="customCKEditor" (change)="onContentEditorChange($event)" [formControl]="tittle"
            fxFlex='grow' [config]='descriptionConfig'>
        </ckeditor>
        <div class="errorMsg" *ngIf="isCKDescriptionMaxlenght">
            <span>{{ckeditorMaxLengthNotification}}</span>
            <div class="err">
                <mat-icon>priority_high</mat-icon>
            </div>
        </div>
    </div>

    <div>
        <span class="lbl title-weight"><span>Tệp đính kèm</span></span>
    </div>
    <div [ngClass]="{'file_uploaded': uploaded == true}" fxHide.lt-md class="marginbottom">
        <div class="drag_upload_btn" [ngClass]="{'no_boder': uploaded == true}">
            <button mat-button [ngClass]="{'btn_uploaded': uploaded == true, 'clear_file_queue': uploaded == false}"
                fxFlex='grow'>
                <mat-icon class="material-icons-outlined">cloud_upload</mat-icon> <a href="">
                    <span i18n>Kéo thả tệp tin hoặc </span><span class="txtUpload" i18n>Tải lên</span>
                </a>
                <div>
                    <span>Kích thước tối đa của một tệp tin:</span> {{ maxFileSize }}MB
                </div>
            </button>
            <input id="file_upload" name="file_upload[]" type='file' (change)="onSelectFile($event)" multiple
                [accept]="acceptFileExtension" value="{{blankVal}}" fxFlex='grow'>
        </div>
        <div class="file_drag_upload_preview">
            <div class="list_uploaded" *ngFor='let url of urls; let i = index;'>
                <div class="file_icon" [ngStyle]="{'background-image': 'url('+ urls[i] +')'}"></div>
                <span class="file_name" matTooltip="{{uploadFileNames[i].filename}}"
                    [matTooltipPosition]="'right'">{{fileNames[i]}}</span>
                
                <button mat-icon-button class="deleteFile" [matMenuTriggerFor]="actionMenu">
                    <mat-icon>more_horiz</mat-icon>
                  </button>
                  <mat-menu #actionMenu="matMenu" xPosition="before">
                    <a mat-menu-item class="menuAction" target="_blank" [routerLink]="routerLink(uploadFileNames[i])" >
                        <mat-icon>format_size</mat-icon>
                        <span i18n>Xem trước</span>
                      </a>
                      <a mat-menu-item class="menuAction" (click)="downloadFileExport(i)">
                        <mat-icon>cloud_download</mat-icon>
                        <span i18n>Tải xuống tệp tin</span>
                    </a>
                      <a mat-menu-item class="menuAction" (click)="removeItem(i)">
                        <mat-icon>delete_outline</mat-icon>
                        <span i18n>Xóa</span>
                    </a>
                </mat-menu>
            </div>
        </div>
    </div>
</div>

<mat-checkbox
  [disabled]="isDisabled"
  [checked]="isConfirm"
  (change)="onConfirmChange($event)">
  Xác nhận hoàn thành
</mat-checkbox>

<div fxLayout="row" fxLayout.xs="column" fxLayout.sm="column" fxLayoutAlign="center">
    <button mat-flat-button fxFlex='30' [disabled]="isDisabled" class="applyBtn" (click)="onConfirm()">
        <span>Gửi</span>
    </button>
</div>
