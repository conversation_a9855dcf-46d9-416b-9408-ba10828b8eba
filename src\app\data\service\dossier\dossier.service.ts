import {Injectable} from '@angular/core';
import {HttpClient, HttpHeaders, HttpParams} from '@angular/common/http';
import {ApiProviderService} from 'src/app/core/service/api-provider.service';
import {BehaviorSubject, Observable, of} from 'rxjs';
import {EnvService} from 'src/app/core/service/env.service';
import {SnackbarService} from "data/service/snackbar/snackbar.service";
import { IdName } from '../../schema/id-name';
import { ErrorComponent, ErrorDialogModel } from 'src/app/shared/components/dialogs/error/error.component';
import { AlertComponent, AlertDialogModel } from 'src/app/shared/components/dialogs/alert/alert.component';
import { MatDialog } from '@angular/material/dialog';
import { DeploymentService } from 'src/app/data/service/deployment.service';
import { Workbook } from 'exceljs';
import { saveAs } from 'file-saver';
import * as fs from 'file-saver';
import {DatePipe} from '@angular/common';
import { ExportExcelService } from 'src/app/data/service/export-excel/export-excel.service';
import * as tUtils from 'src/app/data/service/thoai.service';
import { map, catchError } from 'rxjs/operators';

const EXCEL_TYPE = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8';
const EXCEL_EXTENSION = '.xlsx';

@Injectable({
  providedIn: 'root'
})
export class DossierService {
  public StatusYCBS = 23;
  config = this.envService.getConfig();
  env = this.deploymentService.getAppDeployment()?.env;
  envConfig = this.deploymentService.env;

  //tlqkhanh.hcm-IGATESUPP-68633
  allowShowOrganizationName = (tUtils.isAllowedAgency(localStorage,this.deploymentService.env?.OS_HCM?.isShowOrganizationName.agencyIds))? true : false;
  isShowOrganizationName = (this.deploymentService.env?.OS_HCM?.isShowOrganizationName.enable && this.allowShowOrganizationName) ? this.deploymentService.env?.OS_HCM?.isShowOrganizationName.enable : false;
  //endof tlqkhanh.hcm-IGATESUPP-68633
  paginationType = this.deploymentService.env.paginationType;

  procedureCodeSLDTBXHWorkPermit = this.deploymentService.env?.OS_HCM?.procedureCodeSLDTBXHWorkPermit
                    ? this.deploymentService.env.OS_HCM.procedureCodeSLDTBXHWorkPermit
                    : {procedureCodeLicensingWorkPermit: "", procedureCodeRenewalWorkPermit: "", procedureCodeExtendWorkPermit: ""};
  // IGATESUPP-86045
  isShowColumnsBillingInformation = this.deploymentService?.env?.OS_HCM?.showColumnsBillingInformation? this.deploymentService?.env?.OS_HCM?.showColumnsBillingInformation?.enable:false;
  hiddenCheckboxUpdateDueDate = this.deploymentService.getAppDeployment()?.hiddenCheckboxUpdateDueDate || false;
  isAddColumDataExport = this.deploymentService.getAppDeployment()?.isAddColumDataExport || false;
  padmanReport  = this.deploymentService.getAppDeployment()?.padmanReport || false;
  constructor(
    private envService: EnvService,
    private http: HttpClient,
    private apiProviderService: ApiProviderService,
    private snackbar: SnackbarService,
    private dialog: MatDialog,
    private deploymentService: DeploymentService,
    private datePipe: DatePipe,
  ) { }

  // private dossierURL = 'http://localhost:8081/dossier/';
  // private padman = 'http://localhost:8081';
  private digitizeURL = this.apiProviderService.getUrl('digo', 'padman') + '/dbn-digitize/';
  private dossierURL = this.apiProviderService.getUrl('digo', 'padman') + '/dossier/';
  private dossierURLKHA = this.apiProviderService.getUrl('digo', 'padman') + '/dossier-kha/';
  private dossierURLReport = this.apiProviderService.getUrl('digo', 'padmanReport') + '/dossier/';
  private dossierFeeURL = this.apiProviderService.getUrl('digo', 'padman') + '/dossier-fee/';
  private padman = this.apiProviderService.getUrl('digo', 'padman');
  private padmanURLReport = this.apiProviderService.getUrl('digo', 'padmanReport');
  private statistics = this.apiProviderService.getUrl('digo', 'statistics') ;
  private basedata = this.apiProviderService.getUrl('digo', 'basedata');
  private basecat = this.apiProviderService.getUrl('digo', 'basecat');
  private basepad = this.apiProviderService.getUrl('digo', 'basepad');
  //private basepad = 'http://localhost:8080';
  private rbo = this.apiProviderService.getUrl('digo', 'rbo');
  private adapter = this.apiProviderService.getUrl('digo', 'adapter');
  private logman = this.apiProviderService.getUrl('digo', 'logman');
  private messenger = this.apiProviderService.getUrl('digo', 'messenger');
  private filePath = this.apiProviderService.getUrl('digo', 'fileman') + '/file/';
  private fileman = this.apiProviderService.getUrl('digo', 'fileman');
  private storage = this.apiProviderService.getUrl('digo', 'storage');
  private getFormio = this.config.formioURL + '/form/';
  private reporter = this.apiProviderService.getUrl('digo', 'reporter');
  private bpmPath = this.apiProviderService.getUrl('digo', 'bpm');
  private query = this.apiProviderService.getUrl('digo', 'query');
  private dossierDVCLTURL = this.apiProviderService.getUrl('digo', 'padman') + '/dvclt-dossier/';
  private dossierDVCLTURLTracking = this.apiProviderService.getUrl('digo', 'padman') + '/dvclt-dossier-tracking/';
  private qbhDanhGiaLog = this.apiProviderService.getUrl('digo', 'padman') + '/qbh-danh-gia-log/';
  private dossierHgiURL = this.apiProviderService.getUrl('digo', 'padman') + '/dossier-hgi/';
  private dossierId: BehaviorSubject<any> = new BehaviorSubject<any>(null);
  private dossierURLCTO = this.apiProviderService.getUrl('digo', 'padman') + '/dossier-cto/';
    // excel
    excelData = [];
    agencyNameExcel: any = '';
    columns: any[];
    footerData: any[][] = [];
    waitingDownloadExcel = false;

  sendHTTPDVCLT(code){
    if (code != null) {
      const URL = this.padman + "/judicial-civil-status/--send?code=" + code;
      console.log("URL DVCLT", URL);
        let headers = new HttpHeaders();
        headers = headers.set('Content-Type', 'application/json');
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        return this.http.post<any>(URL, null, {headers});
    }
  }

  syncHTTPDVCLT(code){
    if (code != null) {
      const URL = this.padman + "/judicial-civil-status/--sync-dossiers?code=" + code;
      console.log("URL DVCLT", URL);
      let headers = new HttpHeaders();
      headers = headers.set('Content-Type', 'application/json');
      headers = headers.set('Accept-Language', localStorage.getItem('language'));
      return this.http.post<any>(URL, null, {headers});
    }
  }

  syncHTTPTinh(code){
    if (code != null) {
      const URL = this.padman + "/judicial-civil-status/--sync-dossiers-tinh?code=" + code;
      console.log("URL DVCLT", URL);
      let headers = new HttpHeaders();
      headers = headers.set('Content-Type', 'application/json');
      headers = headers.set('Accept-Language', localStorage.getItem('language'));
      return this.http.post<any>(URL, null, {headers});
    }
  }

  syncLLTPVNEID(code){
    if (code != null) {
      const URL = this.padman + "/v2/lyLichTuPhap/--sync-dossiers?code=" + code;
      console.log("URL VNEID", URL);
      let headers = new HttpHeaders();
      headers = headers.set('Content-Type', 'application/json');
      headers = headers.set('Accept-Language', localStorage.getItem('language'));
      return this.http.post<any>(URL, null, {headers});
    }
  }


  returnAdditionalRequestDossier(data, params): Observable<any>{
    const URL = this.padman + '/additional-request/--return-dossier' + params;
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.post<any>(URL, data, {headers});
  } 

  returnCancelProcessingDossier(dossierId): Observable<any>{
    const URL = this.padman + '/cancel-processing-dossier-qni/return-dossier/' + dossierId;
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.post<any>(URL, {} , {headers});
  } 

  updateConvertedCurrency(dossierid , convertedCurrency) : Observable<any>{
    const URL = `${this.dossierURL}${dossierid}/--update-converted-currency?convertedCurrency=`;
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.put(URL + convertedCurrency, { headers });
  }
  postDossierStatus(data): Observable<any>{
      const URL = this.padman + '/dossier/--check-status';
      let headers = new HttpHeaders();
      headers = headers.set('Content-Type', 'application/json');
      headers = headers.set('Accept-Language', localStorage.getItem('language'));
      return this.http.post<any>(URL, data, {headers});
  }

  updateAttachmentVbdlisQni(dossierCode: String, type: String): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    return this.http.put<any>(this.padman + '/vbdlis/--update-attachment-approval-vbdlis?code=' + dossierCode + "&typeUpdate=" + type , { headers });
  }

  excelExportDossierAppointmentDate(data): any {
      return new Promise((resolve) => {
        this.postDossierAppointmentDate(data).then(res => {
          const blob = new Blob([res.body], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
          let filename = 'danh-sach-ho-so.xlsx';
          if (res.headers.get('content-disposition') != null) {
            filename = res.headers.get('content-disposition').split(';')[1].split('=')[1].replace(/\"/g, '');
          }
          var blobUrl = URL.createObjectURL(blob);

          var xhr = new XMLHttpRequest;
          xhr.responseType = 'blob';

          xhr.onload = function () {
            var recoveredBlob = xhr.response;
            var reader = new FileReader;
            reader.onload = function () {
              var base64data = reader.result.toString();
              var object = JSON.stringify({
                mimeType: filename.substring(filename.lastIndexOf(".") + 1),
                name: filename.substring(0, filename.lastIndexOf(".")),
                data: base64data.split(',')[1]
              });
              let anchor = document.createElement("a");
              anchor.download = filename;
              anchor.href = base64data;
              anchor.click();
            };
            reader.readAsDataURL(recoveredBlob);
          };

          xhr.open('GET', blobUrl);
          xhr.send();
          resolve(true);
        })
          .catch(err => {
            console.log("err", err.status );
            if (err.status === 404) {
              this.snackbar.openSnackBar(0, '', 'Hệ thống đã kiểm tra không tìm thấy hồ sơ nào sai ngày hẹn trả ở bước nghĩa vụ tài chính để xuất dữ liệu file !', 'error_notification', this.config.expiredTime);
            }else{
              this.snackbar.openSnackBar(0, '', 'Hệ thống tạm thời không thể xuất file excel!', 'error_notification', this.config.expiredTime);
            }
            resolve(false)
          });
      })

  }

  downloadReportDossierFeeStatictis(searchString, excelFileName): void {
    this.getListDossierFeeStatictis(searchString).subscribe((response: Blob) => {
      const filename = excelFileName;
      saveAs(response, filename);
    });
  }

  getListDossierFeeStatictis(searchParams): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.set('Content-type', 'application/json');
    return this.http.get(this.statistics + '/dossier-fee-statistic/--export-excel-fee-statictis-dossier' , {params: searchParams, headers, responseType: 'blob' });
  }

  downloadExcelDossierFeeByProcedure(searchString, excelFileName): void {
    this.getExcelDossierFeeByProcedure(searchString).subscribe((response: Blob) => {
      const filename = excelFileName;
      saveAs(response, filename);
    });
  }

  getExcelDossierFeeByProcedure(searchParams): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.set('Content-type', 'application/json');
    return this.http.get(this.statistics + '/dossier-fee-statistic/--export-excel-fee-statictis-procedure' , {params: searchParams, headers, responseType: 'blob' });
  }

  downloadExcelDossierFeeBySector(searchString, excelFileName): void {
    this.getExcelDossierFeeBySector(searchString).subscribe((response: Blob) => {
      const filename = excelFileName;
      saveAs(response, filename);
    });
  }


  downloadExcelDossierFeeByAgency(searchString, excelFileName): void {
    this.getExcelDossierFeeByAgency(searchString).subscribe((response: Blob) => {
      const filename = excelFileName;
      saveAs(response, filename);
    });
  }

  getExcelDossierFeeBySector(searchParams): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.set('Content-type', 'application/json');
    return this.http.get(this.statistics + '/dossier-fee-statistic/--export-excel-fee-statictis-sector' , {params: searchParams, headers, responseType: 'blob' });
  }

  getExcelDossierFeeByAgency(searchParams): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.set('Content-type', 'application/json');
    return this.http.get(this.statistics + '/dossier-fee-statistic/--export-excel-fee-statictis-agency' , {params: searchParams, headers, responseType: 'blob' });
  }


  getListDossierSurvey(searchString): Observable<any> {
    const URL = this.padman + '/dossier/survey-all' ;
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get( URL+ searchString, { headers });
  }

  postDossierAppointmentDate(data){
    const URL = this.padman + '/dossier/'  +  '/--update-dossier-appointmentDate';
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.post<any>(URL, data, {headers, observe: 'response', responseType: 'blob' as 'json'}).toPromise();
  }

  putDossierNote(dossierId, listNote ): Observable<any>{
    const URL = this.padman + '/dossier/' + dossierId + "/note";
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.put<any>(URL, listNote, {headers});
  }

  // env = this.deploymentService.getAppDeployment()?.env;
  // put template sign
  getTeamplate(id): Observable<any> {
    return this.http.get(this.reporter + '/template/' + id + '/--file', { responseType: 'blob' as 'json' }).pipe();
  }
  putFileSign(id, body): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    const url = this.reporter + '/template/' + id + '/FileSign';
    return this.http.put<any>(url, body, { headers });
  }
  // ==================================================================== GET
  getListDossierLLTP(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    //return this.http.get('http://localhost:8081/dossier/' + searchString, { headers });
    return this.http.get(this.statistics + '/dossier/' + searchString, { headers });
  }

  getListDossier(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    //return this.http.get('http://localhost:8081/dossier/' + searchString, { headers });
    return this.http.get(this.dossierURL + searchString, { headers });
  }

  getListDossierKHA(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    //return this.http.get('http://localhost:8081/dossier-kha/' + searchString, { headers });
    return this.http.get(this.dossierURLKHA + searchString, { headers });
  }
  
  getListDossierPareport(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    //return this.http.get('http://localhost:8081/dossier/' + searchString, { headers });
    return this.http.get((!this.padmanReport ? this.dossierURL : this.dossierURLReport) + searchString, { headers });
  }

  getListDossierNotHandle(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    //return this.http.get('http://localhost:8081/dossier/' + searchString, { headers });
    return this.http.get(this.dossierURL + searchString, { headers });
  }

  getListDossierFeeChange(listId): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    const url = this.padman + '/charge-fees-dossier/--get-fee-by-list-dossier';
    return this.http.post<any>(url, listId, { headers });
  }

  postChangeFees(data): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    const url = this.padman + '/charge-fees-dossier';
    return this.http.post<any>(url, data, { headers });
  }

  cancelChangeFees(id, data): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    const url = this.padman + '/charge-fees-dossier/'+ id+ "/cancel";
    return this.http.put<any>(url, data, { headers });
  }

  putPaymentMethodDossier(id, requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    const url = this.dossierURL + 'update-payment-method/'+ id;
    return this.http.put<any>(url, requestBody, { headers });
  }

  //pqlong-IGATESUPP-50946
  getListUbmttqDossier(searchString): Observable<any> {
    let headers = new HttpHeaders();
    const URL = `${this.padman}/statistic-ubmttq/--ubmttq`
    // const URL = `http://localhost:8081/statistic-ubmttq/--ubmttq`
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(URL + searchString, { headers });
  }

  exportListUbmttqDossier(searchString): any {
    const url_API = `${this.padman}/statistic-ubmttq/--excel`;
    return new Promise((resolve) => {
      this.http.get(url_API + searchString, {
        observe: 'response',
        responseType: 'blob'
      }).toPromise().then(res => {
        console.log(res);
        const blob = new Blob([res.body], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'});
        console.log(blob);
        let filename = 'bao_cao_ubmttq.xlsx';
        if (res.headers.get('Content-Disposition') != null) {
          filename = res.headers.get('Content-Disposition').split(';')[1].split('=')[1];
        }
        const blobUrl = URL.createObjectURL(blob);
        const xhr = new XMLHttpRequest();
        xhr.responseType = 'blob';

        xhr.onload = () => {
          const recoveredBlob = xhr.response;
          const reader = new FileReader();
          reader.onload = () => {
            const base64data = reader.result.toString();
            const anchor = document.createElement('a');
            anchor.download = filename;
            anchor.href = base64data;
            anchor.click();
          };
          reader.readAsDataURL(recoveredBlob);
        };

        xhr.open('GET', blobUrl);
        xhr.send();
        resolve(true);
      }).catch(err => {
        if (err.status === 500) {
          const message = {
            vi: 'Hệ thống tạm thời không thể xuất file excel!',
            en: 'The system is temporarily unable to export the excel file!'
          };
          this.snackbar.openSnackBar(0, '', message[localStorage.getItem('language')], 'error_notification', this.config.expiredTime);
        }
        resolve(false);
      });
    });
  }

  //pqlong-IGATESUPP-65994
  getListRefusedDossier(searchString): Observable<any> {
    let headers = new HttpHeaders();
    let URL = `${this.padman}/refused-statistics/--get`;
    if(this.isShowColumnsBillingInformation){
      URL = `${(!this.padmanReport ? this.padman : this.padmanURLReport) }/refused-statistics/--get-department-of-industry-and-trade`;

    }
    //const URL = `http://localhost:8081/refused-statistics/--get`
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(URL + searchString, { headers });
  }

  exportListRefusedDossier(searchString): any {
    let url_API = `${this.padman}/refused-statistics/--excel`;
    if(this.isShowColumnsBillingInformation){
      url_API = `${this.padman}/refused-statistics/--excel-department-of-industry-and-trade`;
    }
    //const url_API = `http://localhost:8081/refused-statistics/--excel`
    return new Promise((resolve) => {
      this.http.get(url_API + searchString, {
        observe: 'response',
        responseType: 'blob'
      }).toPromise().then(res => {
        console.log(res);
        const blob = new Blob([res.body], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'});
        console.log(blob);
        let filename = 'thong_ke_ho_so_tu_choi.xlsx';
        if (res.headers.get('Content-Disposition') != null) {
          filename = res.headers.get('Content-Disposition').split(';')[1].split('=')[1];
        }
        const blobUrl = URL.createObjectURL(blob);
        const xhr = new XMLHttpRequest();
        xhr.responseType = 'blob';

        xhr.onload = () => {
          const recoveredBlob = xhr.response;
          const reader = new FileReader();
          reader.onload = () => {
            const base64data = reader.result.toString();
            const anchor = document.createElement('a');
            anchor.download = filename;
            anchor.href = base64data;
            anchor.click();
          };
          reader.readAsDataURL(recoveredBlob);
        };

        xhr.open('GET', blobUrl);
        xhr.send();
        resolve(true);
      }).catch(err => {
        if (err.status === 500) {
          const message = {
            vi: 'Hệ thống tạm thời không thể xuất file excel!',
            en: 'The system is temporarily unable to export the excel file!'
          };
          this.snackbar.openSnackBar(0, '', message[localStorage.getItem('language')], 'error_notification', this.config.expiredTime);
        }
        resolve(false);
      });
    });
  }

  exportLltpHCMES(searchString): any{
    const url_API = this.statistics + '/dossier/' + searchString;
    return new Promise((resolve) => {
      this.http.get(url_API, {
        observe: 'response',
        responseType: 'blob'
      }).toPromise().then(res => {
        console.log(res);
        const blob = new Blob([res.body], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'});
        console.log(blob);
        let filename = 'thong_ke_danh_sach_yeu_cau_cap_phieu_lltp.xlsx';
        if (res.headers.get('Content-Disposition') != null) {
          filename = res.headers.get('Content-Disposition').split(';')[1].split('=')[1];
        }
        const blobUrl = URL.createObjectURL(blob);
        const xhr = new XMLHttpRequest();
        xhr.responseType = 'blob';

        xhr.onload = () => {
          const recoveredBlob = xhr.response;
          const reader = new FileReader();
          reader.onload = () => {
            const base64data = reader.result.toString();
            const anchor = document.createElement('a');
            anchor.download = filename;
            anchor.href = base64data;
            anchor.click();
          };
          reader.readAsDataURL(recoveredBlob);
        };

        xhr.open('GET', blobUrl);
        xhr.send();
        resolve(true);
      }).catch(err => {
        if (err.status === 500) {
          const message = {
            vi: 'Hệ thống tạm thời không thể xuất file excel!',
            en: 'The system is temporarily unable to export the excel file!'
          };
          this.snackbar.openSnackBar(0, '', message[localStorage.getItem('language')], 'error_notification', this.config.expiredTime);
        }
        resolve(false);
      });
    });
  }

  exportLltpHCM(searchString): any {
    const url_API = this.dossierURL + searchString;
    return new Promise((resolve) => {
      this.http.get(url_API, {
        observe: 'response',
        responseType: 'blob'
      }).toPromise().then(res => {
        console.log(res);
        const blob = new Blob([res.body], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'});
        console.log(blob);
        let filename = 'thong_ke_danh_sach_yeu_cau_cap_phieu_lltp.xlsx';
        if (res.headers.get('Content-Disposition') != null) {
          filename = res.headers.get('Content-Disposition').split(';')[1].split('=')[1];
        }
        const blobUrl = URL.createObjectURL(blob);
        const xhr = new XMLHttpRequest();
        xhr.responseType = 'blob';

        xhr.onload = () => {
          const recoveredBlob = xhr.response;
          const reader = new FileReader();
          reader.onload = () => {
            const base64data = reader.result.toString();
            const anchor = document.createElement('a');
            anchor.download = filename;
            anchor.href = base64data;
            anchor.click();
          };
          reader.readAsDataURL(recoveredBlob);
        };

        xhr.open('GET', blobUrl);
        xhr.send();
        resolve(true);
      }).catch(err => {
        if (err.status === 500) {
          const message = {
            vi: 'Hệ thống tạm thời không thể xuất file excel!',
            en: 'The system is temporarily unable to export the excel file!'
          };
          this.snackbar.openSnackBar(0, '', message[localStorage.getItem('language')], 'error_notification', this.config.expiredTime);
        }
        resolve(false);
      });
    });
  }

  getDossierAppliedOld(search): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.dossierURL + '--get-list-dossier-applied-old' + search, { headers }).pipe();
}
  getListDossierLogBook(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.set('Content-type', 'application/json');
    return this.http.get((!this.padmanReport ? this.dossierURL : this.dossierURLReport) + searchString, { headers, responseType: 'arraybuffer' });
  }

  getListDossierLogBookHCM(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.set('Content-type', 'application/json');
    return this.http.get((!this.padmanReport ? this.dossierURL : this.dossierURLReport) + searchString, { headers, responseType: 'blob' });
  }

  getListDossierLogBookHCMWhenAllowSendMail(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.set('Content-type', 'application/json');
    return this.http.get(this.dossierURL + searchString);
  }

  getListDossierLogBookWhenAllowSendMail(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.set('Content-type', 'application/json');
    return this.http.get(this.dossierURL + searchString);
  }

  getListReportReceipt(): Observable<any>{
    const URL = this.padman + '/dossier-receipt'
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(URL )
  }

  getListReportReceiptSearch(searchString): Observable<any>{
    const URL = (!this.padmanReport ? this.padman : this.padmanURLReport)  + '/dossier-receipt/search'
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(URL + searchString)
  }

  getListReportReceiptExport(searchString): Observable<any>{
    const URL = this.padman + '/dossier-receipt/export'
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get( URL + searchString, { headers, responseType: 'blob' })
  }

  getListDoiGPLXExport(searchString): Observable<any>{
    const URL = this.padman + '/dossier/export-excel-gtvt-blu'
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
   
      return this.http.get( URL + searchString, { headers, responseType: 'blob' });  
  }

  downloadDoiGPLX(response: Blob, suffix: String): void {
      suffix = suffix.replace(/\//gi, "-")
      const filename = `Xuat-doi-GPLX-${suffix}.xlsx`;
      saveAs(response, filename);
  }
  

  downloadReportReceiptExport(searchString): void {
    this.getListReportReceiptExport(searchString).subscribe((response: Blob) => {
      const filename = 'baocaobienlai.xlsx';
      saveAs(response, filename);
    });
  }

  downloadReportLogBookExport(searchString, excelFileName): void {
    this.getListDossierLogBookHCM(searchString).subscribe((response: Blob) => {
      const filename = excelFileName;
      saveAs(response, filename);
    });
  }



  getDossierDetail(id): Observable<any> {
    const URL = `${this.dossierURL}${id}/--online`;
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(URL, { headers });
  }

  getLogSyncDossierDetail(id): Observable<any> {
    const URL = `${this.padman}/dossier-sync-log/${id}/`;
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(URL, { headers });
  }

  getNextTaskConfig(id): Observable<any> {
    const URL = `${this.padman}/v2/dossiers/${id}/--next-task-config`;
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(URL, { headers });
  }

  postCompleteTaskV2(reqBody): Observable<any> {
    const URL = `${this.padman}/v2/dossiers/--complete-task`;
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    return this.http.post<any>(URL, reqBody, { headers });
  }

  //phucnh.it2-IGATESUPP-40295
  getVnpostStatus(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.adapter + '/vnpost-status' + searchString, { headers });
  }
  //end phucnh.it2-IGATESUPP-40295

  getDossierDetailByCode(code): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.dossierURL + code + '/--by-code', { headers });
  }

  getListDossierByKeyword(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.dossierURL + '--public/' + searchString, { headers });
  }

  getRequestCertification(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.get(this.dossierURL + 'request-certification' + searchString, { headers }).pipe();
  }
  getDossierMenuTaskRemind(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.dossierURL + '--menu-remind/' + searchString, { headers });
  }
  getDossierMenuTaskRemindProcess(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.dossierURL + '--menu-remind-process?' + searchString, { headers });
  }

  getDossierMenuTaskRemindAllAgency(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get((!this.padmanReport ? this.dossierURL : this.dossierURLReport) + '--menu-remind-all-agency?' + searchString, { headers });
  }

  getDossierMenuTaskRing(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get((!this.padmanReport ? this.dossierURL : this.dossierURLReport) + '--menu-remind-all-agency2?' + searchString, { headers });
  }

  getDossierMenuTaskRingOpt(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.query + '/dossier/--menu-ring-notify?' + searchString, { headers });
  }

  getCheckDossierByProcedureProcessDefinition(procedureId, processDefinitionId): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    let param = `?procedure-id=${procedureId ? procedureId : ''}`;
    param += `&process-definition-id=${processDefinitionId ? processDefinitionId : ''}`;
    return this.http.get(this.dossierURL + '--check-by-procedure-process-definition' + param, { headers });
  }

  getTbthueDvcqg(code): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.padman + `/dvcqg-thue/${code}/--tbthuedvcqg`, { headers });
  }

  getListNation(): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.basedata + '/nation', { headers });
  }

  getListPlace(nationId, parentId, parentTypeId): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    let param = '?nation-id=' + nationId;
    param += parentId ? '&parent-id=' + parentId : '';
    param += '&parent-type-id=' + parentTypeId + '&sort=name,asc';
    return this.http.get(this.basedata + '/place/--search' + param, { headers });
  }

  getListPlaceByParent(nationId, parentId): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    let param = '?nation-id=' + nationId;
    param += parentId ? '&parent-id=' + parentId : '';
    param += '&sort=name,asc';
    return this.http.get(this.basedata + '/place/--search' + param, { headers });
  }

  getPlaceAddress(id: string): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.basedata + '/place/' + id + '/--address', { headers });
  }

  getListTagByCategoryId(categoryId, page): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    const param = '?category-id=' + categoryId + '&page=' + page + '&size=10&spec=' + this.deploymentService.env.paginationType +'&size=50';
    return this.http.get(this.basecat + '/tag/--by-category-id' + param, { headers });
  }

  getListSector(page): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    const param = '?page=' + page + '&size=10&spec=' + this.deploymentService.env.paginationType +'&status=1';
    return this.http.get(this.basepad + '/sector' + param, { headers });
  }

  getListSectorSort(page): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    const param = '?page=' + page + '&size=1000&spec=' + this.deploymentService.env.paginationType +'&status=1';
    return this.http.get(this.basepad + '/sector' + param, { headers });
  }

  getListSectorSlice(page): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    const param = '?page=' + page + '&size=10&status=1' + '&spec='+ this.paginationType ;
    return this.http.get(this.basepad + '/sector' + param, { headers });
  }

  getListSectorSliceSort(page): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    const param = '?page=' + page + '&size=1000&status=1' + '&spec='+ this.paginationType ;
    return this.http.get(this.basepad + '/sector' + param, { headers });
  }

  getListErrorEvent(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.adapter + '/dossier-event-log-error/' + searchString, { headers });
  }

  getCountNumberDossier(search): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.padman + '/re-sync-dossier/--count-dossier-number' + search, { headers });
  }

  getListDossierDue(searchString): Observable<any> {
    const URL = this.padman + '/dossier/due' + searchString;
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get( URL, { headers });
  }

  getListDossierDueAll(searchString): Observable<any> {
    const URL = this.padman + '/dossier/due-all' + searchString;
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get( URL, { headers });
  }

  getListProcedure(sectorId, page): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    const param = '?page=' + page +'&size=10&spec=' + this.deploymentService.env.paginationType + '&sector-id=' + sectorId + '&status=1';
    return this.http.get(this.basepad + '/procedure' + param, { headers });
  }

  getDossierFee(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.padman + '/dossier-fee?dossier-id=' + id, { headers });
  }

  getDossierFeeByDossiers(ids): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.padman + '/dossier-fee/--by-dossiers?dossier-ids=' + ids+'&required=', { headers });
  }

  getDossierReceiptByDossiers(ids): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.padman + '/dossier-receipt/--list-receipt-by-dossierId?dossier-ids=' + ids, { headers });
  }

  putDossierFeeByDossier(id,requestBody):Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.set('Content-Type', 'application/json');
    return this.http.put(this.padman + '/dossier-fee/--by-dossier?dossier-id=' + id, requestBody,{ headers });
  }

  updateSyncFeeVneid(id,requestBody):Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.set('Content-Type', 'application/json');
    return this.http.put(this.padman + '/dossier-fee/--sync-fee-vneid?dossier-id=' + id, requestBody,{ headers });
  }

  puDossierQualifiedRecept(id,requestBody):Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.put(this.padman + `/dossier/${id}/--list-officer-dossier-qualified`, requestBody,{ headers });
  }

  putDossierPaymentOnline(requestBody):Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.put(this.padman + '/dossier/confirm-payment-online', requestBody,{ headers });
  }

  getVnpostFee(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.padman + '/dossier-transport-fee?dossier-id=' + id, { headers });
  }

  getAgencyConfig(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.basepad + '/agency-config' + searchString, { headers });
  }

  getAgencyLevel(levelId): Observable<any> {
    return this.http.get(this.basedata + '/agency-level/' + levelId);
  }

  getDossierCodePattern(agencyId, procedureId): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.basepad + '/config/get-pattern?agency-id=' + agencyId + '&procedure-id=' + procedureId, { headers });
  }

  getDossierCode(dossierCodePattern, agencyCode): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.basecat + '/pattern/' + dossierCodePattern + '/--next-value?code=' + agencyCode, { headers });
  }

  getPatternCode(dossierCodePattern, agencyCode): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.put(this.basecat + '/pattern/' + dossierCodePattern + '/--get-next-value?code=' + agencyCode, { headers });
  }

  getListHistory(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.logman + '/history/' + searchString, { headers });
  }

  getListComment(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.messenger + '/comment/' + searchString, { headers });
  }

  getListCommentDossier(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.messenger + '/comment/--list-comment' + searchString, { headers });
  }

  getListStatus(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.dossierURL + 'status_dvc' + searchString, { headers });
  }

  getProcessInstances(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept', 'image/svg+xml');
    return this.http.get(this.rbo + '/v1/process-instances/' + id + '/model', { headers, responseType: 'blob' });
  }

  getDossierApology(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.padman + '/apology/' + searchString, { headers });
  }

  getDossierApologyByDossierId(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.padman + '/apology/' + id, { headers });
  }

  getDossierPayment(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.padman + '/dossier-payment/' + searchString, { headers });
  }

  getDossierPaymentKTM(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.padman + '/dossier-payment/--list-dossier-payment' + searchString, { headers });
  }

  getDossierPaymentAGG(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.padman + '/dossier-payment/--list-dossier-payment-agg' + searchString, { headers });
  }

  getExcelDossierPaymentKTM(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.padman + '/dossier-payment/--export-excel-list-dossier-payment' + searchString, { headers });
  }

  getListForm(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.basepad + '/form/' + searchString, { headers }).pipe();
  }

  getListTagByCategoryIdPadsvc(id: string, page: number, size: number, sort: string): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    // tslint:disable-next-line: max-line-length
    return this.http.get(this.basecat + '/tag/--by-category-id?category-id=' + id + '&page=' + page + '&size=' + size + '&sort=' + sort, { headers }).pipe();
  }

  getListAgency(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.basedata + '/agency/' + searchString, { headers });
  }

  getAgencyInfo(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.basedata + '/agency/' + id, { headers }).pipe();
  }

  getAgencyFullAddress(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.basedata + '/agency/' + id + '/--full-address', { headers }).pipe();
  }

  getAgencyFullyByCode(code): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.basedata + '/agency/name+code+parent+ancestor/--fully-by-code/?code=' + code, { headers }).pipe();
  }

  getAgencyTag(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.basecat + '/tag/' + id, { headers }).pipe();
  }

  getFormIoData(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get<any>(this.getFormio + id, { headers }).pipe();
  }

  getCheckGateway(id): Observable<any> {
    return this.http.get<any>(this.rbo + '/digo/task/' + id + '/--check-gateway').pipe();
  }

  getCheckZaloUser(requestString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    return this.http.get<any>(this.adapter + '/zalo/--check-user/' + requestString, { headers });
  }

  getNextFlowElement(id, requestString): Observable<any> {
    return this.http.get<any>(this.rbo + '/digo/task/' + id + '/--next-flow-element' + requestString).pipe();
  }

  getNextFlowVariable(id, requestString): Promise<any> {
    return this.http.get<any>(this.rbo + '/digo/task/' + id + '/--next-flow-element' + requestString).toPromise();
  }

  //KGG OS
  getAgencySearchKGG(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.dossierURL + '--get-all-agency-first-kgg?' + searchString, { headers });
  }

  getListDossierWithdrawalGLI(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.dossierURL + 'search-dossier-withdrawal-gli/'+searchString, { headers });
  }

  searchKggDossierReportOnline(searchString): Observable<any> {
    const URL = this.padman + '/kgg-dossier-statistic/--kgg-dossier-report-online' + searchString;
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get( URL, { headers });
  }

  searchKggDossierReportDetailOnline(searchString): Observable<any> {
    const URL = this.padman + '/kgg-dossier-statistic/--kgg-dossier-report-detail' + searchString;
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get( URL, { headers });
  }

  // ==================================================================== POST
  startReceivingDossier(requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.post<any>(this.rbo + '/v1/process-instances', requestBody, { headers });
  }

  postAssignTask(taskId, requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    return this.http.post<any>(this.rbo + '/admin/v1/tasks/' + taskId + '/assign', requestBody, { headers });
  }

  postComment(requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.post<any>(this.messenger + '/comment/', requestBody, { headers });
  }

  postCommentList(requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.post<any>(this.messenger + '/comment/list', requestBody, { headers });
  }

  postListComment(requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.post<any>(this.messenger + '/comment/--list-comment', requestBody, { headers });
  }

  postHistory(requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.post<any>(this.logman + '/history/', requestBody, { headers });
  }

  postHistoryList(requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.post<any>(this.logman + '/history/list', requestBody, { headers });
  }

  postDossierLog(requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.post<any>(this.logman + '/dossier-log', requestBody, { headers });
  }

  postDossierOnline(requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));

    try {
      var objectRequestBody = JSON.parse(requestBody);
      if (objectRequestBody?.applicant?.data?.fullname) {
        objectRequestBody.applicant.data!.fullnameNotTone = objectRequestBody?.applicant?.data?.fullname.normalize("NFD").replace(/[\u0300-\u036f]/g, "").replace(/đ/g, "d").replace(/Đ/g, "D");
      }
      if (objectRequestBody?.applicant?.data?.organization) {
        objectRequestBody.applicant.data!.organizationNotTone = objectRequestBody?.applicant?.data?.organization.normalize("NFD").replace(/[\u0300-\u036f]/g, "").replace(/đ/g, "d").replace(/Đ/g, "D");
      }
      requestBody = JSON.stringify(objectRequestBody);
    }
    catch (error) {

    }

    return this.http.post<any>(this.dossierURL + '--apply-online', requestBody, { headers });
  }

  postDossierApology(requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.post<any>(this.padman + '/apology/', requestBody, { headers });
  }

  postForm(requestBody) {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.post<any>(this.basepad + '/form', requestBody, { headers });
  }

  //// TIMESHEETV3
  postTimesheet(requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    if (this.envConfig?.timesheetV3) {
      return this.http.post<any>(this.basecat + '/v3/timesheet-gen/--by-dossier-id/', requestBody, {headers});
    } else if (this.envConfig?.timesheetV2) {
      return this.http.post<any>(this.basecat + '/v2/timesheet-gen/--by-dossier-id/', requestBody, {headers});
    }else {
      return this.http.post<any>(this.basecat + '/timesheet-gen/--by-dossier-id/', requestBody, {headers});
    }
  }

  //// TIMESHEETV3
  genDurationTimesheet(requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    if (this.envConfig?.timesheetV3) {
      return this.http.post<any>(this.basecat + '/v3/timesheet-gen/--gen-duration-by-dossier-id', requestBody, {headers});
    } else {
      return this.http.post<any>(this.basecat + '/timesheet-gen/--gen-duration-by-dossier-id', requestBody, {headers});
    }
  }

  // Hàm gọi API check ngày lễ
  checkHoliday(date: string): Observable<boolean> {
    const headers = new HttpHeaders()
      .set('Content-Type', 'application/json')
      .set('Accept-Language', localStorage.getItem('language') || 'vi')
      .set('Authorization', `Bearer ${localStorage.getItem('token') || ''}`);

    const url = `${this.basecat}/timesheet/check-holiday?d=${date}`;
    return this.http.get<{ is_holiday: boolean; code: number }>(url, { headers }).pipe(
      map(response => response.is_holiday),
      catchError(() => of(false)) // Fallback: nếu API lỗi, coi như không phải ngày lễ
    );
  }

  postCompleteTask(id, requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    return this.http.post<any>(this.rbo + '/v1/tasks/' + id + '/complete', requestBody, { headers });
  }

  postClaimTask(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    return this.http.post<any>(this.rbo + '/admin/v1/tasks/' + id + '/claim', { headers });
  }

  postDossierCreateProcess(requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    return this.http.post<any>(this.padman + '/dossier/--create-process', requestBody, { headers });
  }

  postDossierCreateVbdlis(dossierId, configId, isQNI): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    return this.http.post<any>(this.padman + '/vbdlis/--receiving-dossier?id=' + dossierId + "&config-id=" + configId + "&is-qni=" + isQNI, { headers });
  }

  postDossierCreateDlkVbdlis(dossierId, configId): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    return this.http.post<any>(this.padman + '/vbdlis/--receiving-dossier-dlk?id=' + dossierId + "&config-id=" + configId, { headers });
  }

  postDossierCreatePYNVbdlis(dossierId, configId): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    return this.http.post<any>(this.padman + '/dlk-vbdlis/--receiving-dossier-dlk?id=' + dossierId + "&config-id=" + configId, { headers });
  }

  postDossierCreateTask(requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    return this.http.post<any>(this.padman + '/dossier/--create-task', requestBody, { headers });
  }

  postApologyLetter(requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.post<any>(this.padman + '/apology-letter', requestBody, { headers });
  }

  postSMSByAgency(agencyId, subsystemId, content, listPhoneNumber): Observable<any> {
    const formData: FormData = new FormData();
    formData.append('agencyId', agencyId);
    formData.append('subsystemId', subsystemId);
    formData.append('content', content);
    listPhoneNumber.forEach(email => {
      formData.append('phoneNumber', email);
    });
    return this.http.post<any>(this.adapter + '/sms-brandname/--send-by-agency', formData).pipe();
  }

  postEmailByAgency(agencyId, subsystemId, content, listEmail, subject): Observable<any> {
    const formData: FormData = new FormData();
    formData.append('agencyId', agencyId);
    formData.append('subsystemId', subsystemId);
    formData.append('content', content);
    formData.append('subject', subject);
    listEmail.forEach(email => {
      formData.append('emailAddress', email);
    });
    return this.http.post<any>(this.adapter + '/email/--send-by-agency', formData).pipe();
  }

  postZaloNewTemplate(requestString, requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    return this.http.post<any>(this.adapter + '/zalo/--send-message/' + requestString, requestBody, { headers });
  }

  postOcr(requestBody): Observable<any>{
    // hardcode
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    return this.http.post<any>(this.adapter + '/ekyc/--translate/', requestBody, { headers });
  }

  postNewDossierPayment(requestBody): Observable<any>{
    // hardcode
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    return this.http.post<any>(this.padman + '/dossier-payment/', requestBody, { headers });
  }

  // ==================================================================== PUT
  putResumeTask(dossierId, taskId): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.put<any>(this.dossierURL + dossierId + '/--resume?task-id=' + taskId, null, { headers });
  }

  putSuspendTask(dossierId, taskId): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.put<any>(this.dossierURL + dossierId + '/--suspend?task-id=' + taskId, null, { headers });
  }

  putDossierCode(dossierCodePattern, agencyCode): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.put<any>(this.basecat + '/pattern/' + dossierCodePattern + '/--next-value?code=' + agencyCode, { headers });
  }

  putDossierAcceptOnline(dossierId, requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.put<any>(this.dossierURL + dossierId + '/--accept-online', requestBody, { headers });
  }

  putAddFirstTaskDossier(dossierId, requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.put<any>(this.dossierURL + dossierId + '/--add-task', requestBody, { headers });
  }

  putDossierStatus(requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.put<any>(this.dossierURL + 'status', requestBody, { headers });
  }

  putDossierOnline(dossierId, requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));

    try {
      var objectRequestBody = JSON.parse(requestBody);
      if (objectRequestBody?.applicant?.data?.fullname) {
        objectRequestBody.applicant.data!.fullnameNotTone = objectRequestBody?.applicant?.data?.fullname.normalize("NFD").replace(/[\u0300-\u036f]/g, "").replace(/đ/g, "d").replace(/Đ/g, "D");
      }
      if (objectRequestBody?.applicant?.data?.organization) {
        objectRequestBody.applicant.data!.organizationNotTone = objectRequestBody?.applicant?.data?.organization.normalize("NFD").replace(/[\u0300-\u036f]/g, "").replace(/đ/g, "d").replace(/Đ/g, "D");
      }
      requestBody = JSON.stringify(objectRequestBody);
    }
    catch (error) {

    }

    return this.http.put<any>(this.dossierURL + dossierId + '/--online', requestBody, { headers });
    // return this.http.put<any>('http://localhost:8081/' + dossierId + '/--online', requestBody, { headers });
  }

  postDossierFee(requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.post<any>(this.dossierFeeURL, requestBody, { headers });
    // return this.http.post<any>('http://localhost:8081/dossier-fee', requestBody, { headers });
  }

  putDossierApology(dossierId, requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.put<any>(this.padman + '/apology/' + dossierId, requestBody, { headers });
  }

  putDueDateCurrentTask(dossierId, requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.put<any>(this.dossierURL  + `${dossierId}/--update-due-task`, requestBody, { headers });
  }

  putDossierStatusWithComment(id, requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.put<any>(this.dossierURL + id + '/status', requestBody, { headers });
    //return this.http.put<any>("http://localhost:8081/dossier/" + id + '/status', requestBody, { headers });
  }

  putDossierContinueStatusWithComment(id, requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.put<any>(this.dossierURL + id + '/--continue-status', requestBody, { headers });
  }

  putProcedureAdmininstrationCodeDraft(id, requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.set('Content-Type', 'application/json; ; charset=utf-8');
    return this.http.put(this.dossierURL  + `${id}/--update-procedure-admininstration-code-draft`,requestBody, { headers });
  }


  putTaskVariables(id, taskId, requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.put<any>(this.dossierURL + id + '/--task-variables?task-id=' + taskId, requestBody, { headers });
  }

  putReporterCounting(agencyId, searchString, requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.put<any>(this.reporter + '/agency/' + agencyId + '/--dossier/' + searchString, requestBody, { headers });
  }

  putDossierExtendTimeWithComment(id, requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.put<any>(this.dossierURL + id + '/--extend-time', requestBody, { headers });
  }

  putDossierWithdrawWithComment(id, requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.put<any>(this.dossierURL + id + '/--withdraw', requestBody, { headers });
  }

  putDossierPauseWithComment(id, requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.put<any>(this.dossierURL + id + '/--pause', requestBody, { headers });
  }

  sendDossierDirectlyPaymentNotification(id, requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.put<any>(this.dossierURL + id + '/--send-directly-payment-notification', requestBody, {headers});
  }

  confirmDirectlyPaymentDossier(id, requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.put<any>(this.dossierURL + id + '/--confirm-directly-payment', requestBody, {headers});
  }
  getProcessTimeResume(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get<any>(this.dossierURL + id + '/--resume', { headers });
  }

  putDossierDate(requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    return this.http.put<any>(this.dossierURL + '--date', requestBody, { headers });
  }

  putDossierPayment(id, requestBody): Observable<any>{
    // hardcode
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    return this.http.post<any>(this.padman + '/dossier-payment/'+id, requestBody, { headers });
  }

  // ==================================================================== DEL
  deleteDossier(id, isCanceled) {
    console.log("deleteDossier(id, isCanceled):id = ");
    console.log(id);

    return this.http.delete(this.dossierURL + id + '/?is-canceled=' + isCanceled);
    //return this.http.delete('http://localhost/dossier/' + id + '/?is-canceled=' + isCanceled);

  }

  deleteDossierApology(id) {
    return this.http.delete(this.padman + '/apology/' + id);
  }

  deleteDossierFee(id) {
    return this.http.delete(this.padman + '/dossier-fee/' + id);
  }

  deleteDossierPayment(id) {
    return this.http.delete(this.padman + '/dossier-payment/' + id);
  }

  deleteFile(id) {
    return this.http.delete<any>(this.filePath + id).pipe();
  }

  deleteDossierFormFile(id, dossierId) {
    return this.http.delete(this.padman + '/dossier-form-file/' + id + '/?dossier-id=' + dossierId);
  }

  deleteMultiDossier(listId): Observable<any> {
    return this.http.delete(this.dossierURL + '/--multiple?list-id=' + listId);
  }

  getListPoliceDepartment(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.basedata + '/agency/name+logo-id/' + searchString, { headers }).pipe();
  }

  getListDossierDefinationTask(categoryId): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    const param = '?category-id=' + categoryId + '&page=0&status=1';
    return this.http.get(this.basecat + '/tag/--by-category-id' + param, { headers });
  }

  getLLTPCategory(search, requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.post<any>(this.adapter + '/judicial/--get-category' + search, requestBody, { headers });
  }

  getHTTPCategory(search, requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.post<any>(this.adapter + '/civil-status/--send' + search, requestBody, { headers });
  }

  sendLLTP(search, requestBody, token, code): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers.append('Access-Control-Allow-Origin', '*');
    if (this.env?.judicalRecords?.qni && this.env?.judicalRecords?.qni == 1) {
      if(this.env?.judicalRecords?.qniConfig?.urlSend && this.env?.judicalRecords?.qniConfig?.urlSend != null && this.env?.judicalRecords?.qniConfig?.urlSend != '')
      {
        let tokenBearer:any = 'Bearer ' + token;
        headers = headers.set('Authorization', tokenBearer);
        return this.http.post(this.env?.judicalRecords?.qniConfig?.urlSend, requestBody, { headers }).pipe();
      }else{
        search += `&code=${code}`;
        return this.http.post<any>(this.adapter + '/judicial/--send-qni' + search, requestBody, { headers });
      }
    } else {
    search += `&code=${code}`;
      return this.http.post<any>(this.adapter + '/judicial/--send' + search, requestBody, { headers });
    }
  }

  sendHTTP(search, requestBody, tokenHTTP): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers.append('Access-Control-Allow-Origin', '*');
    if (this.env?.civilStatusJustice?.qni && this.env?.civilStatusJustice?.qni == 1) {
      if (this.env?.civilStatusJustice?.qniConfig?.urlSend && this.env?.civilStatusJustice?.qniConfig?.urlSend != null && this.env?.civilStatusJustice?.qniConfig?.urlSend != '') {
        let tokenBearer: any = 'Bearer ' + tokenHTTP;
        headers = headers.set('Authorization', tokenBearer);
        return this.http.post(this.env?.civilStatusJustice?.qniConfig?.urlSend, requestBody, { headers }).pipe();
      }
      else {
        search += `&code=${requestBody.maHoSo}`;
        return this.http.post<any>(this.adapter + '/civil-status/--send-qni' + search, requestBody, { headers });
      }
    } else {
      search += `&code=${requestBody.maHoSo}`;
      return this.http.post<any>(this.adapter + '/civil-status/--send' + search, requestBody, { headers });
    }
  }

  sendHoTichV2(dossierId, dossierCode): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers.append('Access-Control-Allow-Origin', '*');
    let search = `?dossierId=${dossierId}&code=${dossierCode}`;
    return this.http.post<any>(this.adapter + '/judicial-civil-status/--resend' + search, {}, { headers });
  }

  getMapping(search): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.get(this.adapter + '/mapping-data' + search, { headers });
  }

  getJudicialRecordsConnected(search): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.get(this.basepad + '/procedure-config/jud-record/--list?spec=page' + search, { headers });
  }

  getJudicialCivilStatusConnected(search): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.get(this.basepad + '/procedure-config/jud-civil-status/--list?spec=page' + search, { headers });
  }

  getDetailEnterprise(search, requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    if(this.env?.businessRegistration && this.env?.businessRegistration?.typeGet == '1')
    {
      search += '&msdn=' + requestBody.msdn;
      return this.http.get<any>(this.adapter + '/dkdn/--get-detail-enterprise-get' + search, { headers });
    }else{
      return this.http.post<any>(this.adapter + '/dkdn/--get-detail-enterprise' + search, requestBody, { headers });
    }

    // return this.http.post<any>('http://localhost:8080' + '/dkdn/--get-detail-enterprise' + search, requestBody, { headers });
  }

  getMappingId(search): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.adapter + '/mapping-data' + search, { headers }).pipe();
  }

  getBusinessRegistrationConnected(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.get(this.bpmPath + '/process-definition-task/' + id, { headers });
  }

  getDetailDossier(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.get(this.padman + '/dossier/' + id + '/--public/', { headers });
    // return this.http.get('http://localhost:8080' + '/dossier/' + id + '/--public/', { headers });
  }

  postSynchronizePromotionStatus(requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.set('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');
    // return this.http.get(this.padman + '/dossier/' + id + '/--public/', { headers });
    return this.http.post<any>(this.adapter + '/npadsvc/--synchronize-promotion-status', requestBody, { headers });
  }

  getComment(search): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.set('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.get(this.messenger + '/comment' + search, { headers });
  }

  recallDossier(id, requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.put<any>(this.dossierURL + `${id}/--evict-dossier`, requestBody, { headers });
  }

  recallMultipleDossiers(requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.put<any>(this.dossierURL + `--evict-multiple-dossiers`, requestBody, { headers });
  }

  statisticByReminderMenu(): Promise<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get<any>(this.dossierURL + `reminder-menu/--statistic-by-reminder-menu`, { headers }).toPromise();
  }

  getAgencyFully(id): Promise<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.set('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.get(this.basedata + `/agency/${id}/--fully`, { headers }).toPromise();
  }

  getAgencyNameCodeFully(id): Promise<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.set('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.get(this.basedata + `/agency/${id}/name+code/--fully`, { headers }).toPromise();
  }

  getDossierFormEformData(search): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.padman + '/dossier-form-eform' + search, { headers });
  }
  postDossierFormEform(requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.post<any>(this.padman + '/dossier-form-eform', requestBody, { headers });
  }

  importDossierFromExcell(formData): Observable<any> {
    let headers = new HttpHeaders();
    // headers = headers.set('Content-Type', 'multipart/form-data');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.post<any>(this.dossierURL + '--import-excell', formData, { headers });
  }
  checkImportDossierFromExcell(formData): Observable<any> {
    let headers = new HttpHeaders();
    //headers = headers.set('Content-Type', 'multipart/form-data');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.post<any>(this.dossierURL + '--check-import-excell', formData, { headers });
  }

    importDossierV2FromExcell(formData): Observable<any> {
        let headers = new HttpHeaders();
        // headers = headers.set('Content-Type', 'multipart/form-data');
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        return this.http.post<any>(this.dossierURL + '--import-dossier-excell-v2', formData, { headers });
    }

  importEventSyncStatusDossierNPS(formData, configId): Observable<any> {
    let headers = new HttpHeaders();
    // headers = headers.set('Content-Type', 'multipart/form-data');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.post<any>(this.adapter + '/integrated-event/' + '--import-excel?config-id=' + configId, formData, { headers });
  }

  updateApprovalData(id, requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.put<any>(this.dossierURL + `${id}/--update-approval-data`, requestBody, { headers });
  }

  rejectRequest(id, requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.put<any>(this.dossierURL + `${id}/--reject-approval`, requestBody, { headers });
  }

  approveExtendTime(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.put<any>(this.dossierURL + `${id}/--approve-extend-time`, {}, { headers });
  }
  
  getCommentApprovalInfo(dossierId: string): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get<any>(this.messenger + '/qni-comment/--approval-info/' + dossierId, { headers });
  }

  getListDossierTransferred(searchString): Observable<any> {
    const URL = this.dossierURL + 'transferred' + searchString;
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get( URL, { headers });
  }

  getListDossierTransferredAll(searchString): Observable<any> {
    const URL = this.dossierURL + 'transferred-all' + searchString;
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get( URL, { headers });
  }

  downloadExport(params: string){
    return this.http.get(this.dossierURL + '--export' + params, { observe: 'response', responseType: 'blob' }).toPromise();
  }
  downloadExportSurveyDossier(params: string){
    return this.http.get(this.dossierURL + '--export-survey' + params, { observe: 'response', responseType: 'blob' }).toPromise();
  }

  // Xuất file excel
  excelExport(params: string): any {
    return new Promise((resolve) => {
      this.downloadExport(params).then(res => {
        const blob = new Blob([res.body], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
        let filename = 'mau_ho_so_den_han.xlsx';
        if (res.headers.get('content-disposition') != null) {
          filename = res.headers.get('content-disposition').split(';')[1].split('=')[1].replace(/\"/g, '');
        }
        var blobUrl = URL.createObjectURL(blob);

        var xhr = new XMLHttpRequest;
        xhr.responseType = 'blob';

        xhr.onload = function() {
          var recoveredBlob = xhr.response;
          var reader = new FileReader;
          reader.onload = function() {
            var base64data = reader.result.toString();
            var object = JSON.stringify({
              mimeType:filename.substring(filename.lastIndexOf(".")+1),
              name:filename.substring(0,filename.lastIndexOf(".")),
              data:base64data.split(',')[1]
            });
            let anchor = document.createElement("a");
            anchor.download = filename;
            anchor.href = base64data;
            anchor.click();
          };
          reader.readAsDataURL(recoveredBlob);
        };

        xhr.open('GET', blobUrl);
        xhr.send();
        resolve(true);
      })
        .catch(err => {
          if (err.status === 500) {
            this.snackbar.openSnackBar(0, '', 'Hệ thống tạm thời không thể xuất file excel!', 'error_notification', this.config.expiredTime);
          }
          resolve(false)
        });
    });
  }

  getPaymentDossier(search): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.padman + '/dossier-payment/' + search, { headers }).pipe();
  }

  getPaymentDossierKTM(search): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.padman + '/dossier-payment' + search, { headers }).pipe();
  }

  addSignedTemplateFile(id, body): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.post<any>(this.dossierURL + `${id}/--add-signed-template-file`, body, { headers });
  }

  updateAppointmentDate(id, requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.put<any>(this.dossierURL + id + '/--appointment-date', requestBody, { headers });
  }

  getDetailProcessDefinitionTask(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.bpmPath + '/process-definition-task/' + id, { headers }).pipe();
  }

  putAttachmentFromEdoc(dossierId, requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.put<any>(this.dossierURL + dossierId + '/--attachment-edoc', requestBody, { headers });
  }

  putRemoveIofficeRemind(dossierId): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.put<any>(this.dossierURL + dossierId + '/--remove-ioffice-remind', null , { headers });
  }

  putIncreaseCountSendIoffice(dossierId): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.put<any>(this.dossierURL + dossierId + '/--increase-count-send-ioffice', null, {headers});
  }

  putDossierReturnAccept(dossierId): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.put<any>(this.dossierURL + dossierId + '/--return-accept', null, { headers });
  }

  putDossierForceEndProcess(requestBody, keepAppointmentDate): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    if(keepAppointmentDate || this.hiddenCheckboxUpdateDueDate){
      return this.http.put<any>(this.dossierURL + '--force-end-process?keep-date=true', requestBody, {headers});
    }else {
      return this.http.put<any>(this.dossierURL + '--force-end-process', requestBody, {headers});
    }
  }

  putDossierReassign(id, requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.put<any>(this.dossierURL + id + '/--reassign', requestBody, { headers });
  }

  getApplyMethods(page, size, keyword):Promise<any>{
    let URL = `${this.basecat}/tag/--by-category-id?category-id=5f3a491c4e1bd312a6f00058&status=1`;
    if (!!keyword){
      URL += `&keyword=${keyword}`;
    }
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return new Promise((resolve,reject)=>{
      this.http.get<any>(URL, { headers }).subscribe(res=>{
        let data = [].concat(res?.content).map(i=> new IdName(`${i.code}`.replace('HTTN',''),i.name ));
        resolve({data: data, last: res.last});
      });
    });
  }

  error(vi, en) {
    const dialogData = new ErrorDialogModel(vi, en);
    const dialogRef = this.dialog.open(ErrorComponent, {
      width: '500px',
      data: dialogData,
      disableClose: false,
      autoFocus: false
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
      // console.log(dialogResult);
    });
  }

  alert(vi, en, icon, reload, routerLink, queryParams, timer) {
    const dialogData = new AlertDialogModel(vi, en, icon, reload, routerLink, queryParams, timer);
    const dialogRef = this.dialog.open(AlertComponent, {
      width: '500px',
      data: dialogData,
      disableClose: false,
      autoFocus: false
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
      // console.log(dialogResult);
    });
  }

  sendIntegrationV2(endpoint, requestBody){
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.post<any>(this.adapter + endpoint, requestBody, { headers });
  }

  sendVNPost(search, requestBody) {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.post<any>(this.adapter + '/vnpost/--post-order' + search, requestBody, { headers });
//     return this.http.post<any>('http://localhost:8080' + '/vnpost/--post-order' + search, requestBody, { headers });
  }
  sendVNPostResult(search, requestBody) {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.put<any>(this.padman + search, requestBody, { headers });
  }

  sendKonTumVNPost(search, requestBody) {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.post<any>(this.adapter + '/KonTumVnpost/--post-order' + search, requestBody, { headers });
    //return this.http.post<any>('http://localhost:8080' + '/KonTumVnpost/--post-order' + search, requestBody, { headers });
  }

  //IGATESUPP-22991:
  sendVNPostDossierOverLgspHcm(search, requestBody) {
    console.log("service: sendVNPostDossierOverLgspHcm: start!");
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers.append('Access-Control-Allow-Origin', '*');

    console.log('call adapter: ' + this.adapter + '/lgspHcmVnpost/getOrder' + search);
    return this.http.post<any>(this.adapter + '/lgspHcmVnpost/getOrder' + search, requestBody, { headers });
  }

  //sendVNPostOverLgspHcm
  sendVNPostOverLgspHcm(search, requestBody) {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.post<any>(this.adapter + '/lgspHcmVnpost/getOrder' + search, requestBody, { headers });
//     return this.http.post<any>('http://localhost:8080' + '/vnpost/--post-order' + search, requestBody, { headers });
  }

    //IGATESUPP-94962
    sendVNPostOverLgspHcmSync(search, requestBody) {
      let headers = new HttpHeaders();
      headers = headers.set('Content-Type', 'application/json');
      headers = headers.set('Accept-Language', localStorage.getItem('language'));
      headers.append('Access-Control-Allow-Origin', '*');
      return this.http.post<any>(this.adapter + '/lgspHcmVnpost/getOrder-sync' + search, requestBody, { headers });
    }

  //IGATESUPP-22991:
  getDossierFeeOverLgspHCM(requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.post(this.adapter + '/lgspHcmVnpost/getPrice' , requestBody, { headers });
  }


  getDossierFeeDirectReception(search, requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.post<any>(this.adapter + '/lgspHcmVnpost/getPrice' + search, requestBody, { headers });
  }

  // LGSP Tan Dan -DBN
  //=== dong bo trang thai - tien trinh xu ly ho so
  postReceiveInforFileFromLocal(params,requestBody): Observable<any>{
    // hardcode
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    return this.http.post<any>(this.adapter + '/lgsp-tandan/--receive-file-local?' + params, requestBody, { headers });
  }
  //=== dong bo thong tin giay phep
  postSyncConstPerLocal(params,requestBody): Observable<any>{
    // hardcode
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    return this.http.post<any>(this.adapter + '/lgsp-tandan/--sync-construction-permit?' + params, requestBody, { headers });
  }

  getStatictisReportDossier(agencyId): Observable<any>{
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    const params = {
      'agency-root' : this.config.rootAgency.id,
      'province-name' : this.config.provinceName[localStorage.getItem('language')],
      'agency-id' : agencyId === undefined ? '' : agencyId
    };
    return this.http.get<any>( this.dossierURL + `--statictis-report`, { headers, params});
  }

  getListDossierAll(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.dossierURL + searchString, { headers });
  }

  saveLog(search, requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.post<any>(this.adapter + '/inter-log' + search, requestBody, { headers });
  }

  getTokenVNPost(search) { /////
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.get<any>(this.adapter + '/vnpost/token' + search, { headers });
    // return this.http.get<any>('http://localhost:8080' + '/vnpost/token' + search, { headers });
  }

  getAgencyAddress(id)
  {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.get<any>(this.basedata + '/agency/' + id + '/--full-address', { headers });
  }

  getTokenLGSP(search): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers.append('Access-Control-Allow-Origin', '*');
    // return this.http.get(this.adapter + '/civil-status/--token-lgsp' + search, { headers });
    return this.http.get(this.adapter + '/civil-status/--token-lgsp' + search, { headers });
  }

  getTokenLGSPLyLich(search): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers.append('Access-Control-Allow-Origin', '*');
    // return this.http.get(this.adapter + '/civil-status/--token-lgsp' + search, { headers });
    return this.http.get(this.adapter + '/judicial/--token-lgsp' + search, { headers });
  }

  getDetailAgency(id): Observable<any>
  {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.get<any>(this.basedata + '/agency/' + id, { headers });
  }

  getListDossierDefinitionTask(requestBody): Observable<any>
  {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.post<any>(this.bpmPath + '/process-definition-task/--get-tasks', requestBody, { headers });
  }

  postVNPTPaymentBillInit(body): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    return this.http.post<any>(this.adapter + '/payment-platform/--get-bill', body, { headers });
  }

  postVNPTPaymentBillByUrl(body): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    return this.http.post<any>(this.adapter + '/payment-platform/--get-bill-byURL', body, { headers });
  }

  getDossierPaymentDetail(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    return this.http.get<any>(this.padman + '/dossier-payment/' + id, { headers });
  }

  downloadFile(id, idCheck, uuid?): Observable<any> {
    let headers = new HttpHeaders();
    if(!!idCheck){
      return this.http.get(this.filePath + id + '?dossier-id=' + idCheck, { responseType: 'blob' as 'json' }).pipe();
    }else{
      return this.http.get(this.filePath + id, { responseType: 'blob' as 'json' }).pipe();
    }
  }

  putBillPayment(id, data): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.put<any>(this.padman + '/dossier-payment/' + id + '/--update-bill', data, { headers });
  }

  getListDossierDetail(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.post(this.dossierURL + '--get-dossiers',id, { headers });
  }

  //Gửi rút hồ sơ sang LGSP HCM
  cancelLGSPHCM(body): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    return this.http.post<any>(this.adapter + '/lgsp-hcm-dossier/--sync-dossier', body, { headers });
  }

  //Lấy Log LGSP HCM
  getLGSPHCMLog(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    return this.http.get<any>(this.adapter + '/lgsp-hcm-log' + searchString, { headers });
  }

  //Lấy Log đồng bộ hồ sơ DVCQG LGSP HCM
  getLGSPHCMLogSyncNps(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    return this.http.get<any>(this.adapter + '/log-sync-nps' + searchString, { headers });
  }


  async exportListLGSPLog(searchString) {    
    let response = await this.http.get(this.adapter + '/lgsp-hcm-log/--excel-lgsp-hcm-log' + searchString, { observe: 'response', responseType: 'blob' }).toPromise();
    const blob = response.body;
    saveAs(blob, 'Danh mục thông báo dịch vụ công.xlsx');
  }



  putPauseApprovalDossier(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.put<any>(this.dossierURL + id + '/--approval-pause', { headers });
  }

  //Đồng bộ hồ sơ
  pushDossierBdg(dossierId): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    let confidUrl = "?configId="
    if(this.env?.OS_BDG?.configNPSId)
    {
      confidUrl+=this.env?.OS_BDG?.configNPSId;
    }
    //return this.http.post<any>('http://localhost:8080/dossier/' + dossierId + '/--syncDossierBdg'+confidUrl, { headers });
    return this.http.post<any>(this.dossierURL + dossierId + "/--syncDossierBdg" + confidUrl,{ headers });
  }

  //Đồng bộ trạng thái hồ sơ
  pushDossierStatusBdg(dossierId, requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    console.log(requestBody);
    //return this.http.post<any>('http://localhost:8080/dossier/' + dossierId + '/--syncDossierStepCompleteBdg', requestBody, { headers });
    return this.http.post<any>(this.dossierURL + dossierId + "/--syncDossierStepCompleteBdg", requestBody, { headers });
  }

  searchBdg800DossierReport(searchString): Observable<any> {
    const URL = this.padman + '/dossier/--bdg-800-dossier-report' + searchString;
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get( URL, { headers });
  }

  sendVNPostBdg(search, requestBody) {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.post<any>(this.adapter + '/vnpostBdg/--post-order' + search, requestBody, { headers });
    //return this.http.post<any>('http://localhost:8088' + '/vnpostBdg/--post-order' + search, requestBody, { headers });
  }

  restoreDossierStatus(id) {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));

    return this.http.put<any>(this.dossierURL + id + '/--restore-dossier-status', null, { headers });
  }

  confirmFulfillingFinancialObligations(dossierId, body): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.put<any>(this.dossierURL + dossierId + '/--confirm-fulfilling-financial-obligations', body, { headers });
 }

 receivingDossierV2(dossierId, configId, isQNI): Observable<any> {
  let headers = new HttpHeaders();
  headers = headers.set('Content-Type', 'application/json');
  headers = headers.set('Accept-Language', localStorage.getItem('language'));
  // return this.http.put<any>('http://localhost:8081/vbdlis/--receiving-dossier-v2?id=' + dossierId + '&config-id='+ configId , { headers });
  return this.http.put<any>(this.padman + '/vbdlis/--receiving-dossier-v2?id=' + dossierId + '&config-id='+ configId + "&is-qni=" + isQNI, { headers });
}

  getAllFileDossier(dossierId): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.padman + "/dossier-form-file/" + dossierId + "/--get-all-file", { headers });
  }

  importDataSync(file: File, isLGSPHCM): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    const formData: FormData = new FormData();
    formData.append('file',file);
    return this.http.post(this.padman + '/re-sync-dossier/--import-excell?isLGSPHCM=' + isLGSPHCM, formData, { headers });
  }

  syncDossierByCode(listcode, isLGSPHCM): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.post(this.padman + '/re-sync-dossier/--sync-by-code?isLGSPHCM='+ isLGSPHCM, listcode, { headers });
  }

  syncSynthesisReport(data): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.put(this.adapter + '/dvcqg/--sync-synthesis-report-result', data, { headers });
  }

  getSynthesisReport(search): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.padman + '/dossier-statistic/--all-sync-dvcqg' + search, { headers });
  }

  syncCheckConnectSync(data): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.post(this.adapter + '/nps-dossier/--check-connect', data, { headers });
  }

  postDossierAdditionalRequestVbdlis(code, configId, isReject: any = false): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    return this.http.put<any>(this.padman + '/vbdlis/' + code + '/--update-additional-request' + "?config-id=" + configId + "&is-reject=" + isReject , { headers });
  }

  feedBackResultVbdlis(code, configId): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    return this.http.put<any>(this.padman + '/vbdlis/' + code + '/--feedback-result-vbdlis' + "?config-id=" + configId , { headers });
  }

  postFeedBackResultVbdlis(code, configId, requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.post<any>(this.padman + '/vbdlis/' + code + '/--feedback-result-vbdlis' + "?config-id=" + configId , requestBody, { headers });
  }

  sendNotiToVbdlis(configId, requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.post<any>(this.padman + '/vbdlis/--send-noti-vbdlis' + "?config-id=" + configId , requestBody, { headers });
  }

  postUpdateFinishDossierVbdlis(code, configId): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    return this.http.put<any>(this.padman + '/vbdlis/' + code + '/--update-result-finish-dossier' + "?config-id=" + configId , { headers });
  }

  generateProcedureAdmininstrationCode(requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.post(this.dossierURL  + '--generate-procedure-admininstration-code',requestBody, { headers });
  }

  generateProcedureAdmininstrationCodeByYear(requestBody, isApplyTakeCodeByYear): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    if(isApplyTakeCodeByYear){
        return this.http.post(this.dossierURL  + '--generate-procedure-admininstration-code?is-check-year=true',requestBody, { headers });
    } else {
        return this.http.post(this.dossierURL  + '--generate-procedure-admininstration-code',requestBody, { headers });
    }
  }

  generateListProcedureAdmininstrationCode(number,requestBody, isApplyTakeCodeByYear): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    if(isApplyTakeCodeByYear){
      return this.http.post(this.dossierURL  + `${number}/--generate-list-procedure-admininstration-code` + '?is-check-year=true',requestBody, { headers });
    } else {
      return this.http.post(this.dossierURL  + `${number}/--generate-list-procedure-admininstration-code`,requestBody, { headers });
    }
  }

  generateProcedureReceiptNumberCode(requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.post(this.dossierURL  + '--generate-receipt-number-code',requestBody, { headers });
  }

  putGenerateProcedureAdmininstrationCode(id, requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.put(this.dossierURL  + `--update-procedure-admininstration-code/${id}`,requestBody, { headers });
  }

  putListProcedureAdmininstrationCode(id, requestBody, ...isEnableYT): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.set('Content-Type', 'application/json; ; charset=utf-8');
    if(isEnableYT){
      return this.http.put(this.dossierURL  + `${id}/--update-list-procedure-admininstration-code?isEnableYT=` + isEnableYT,requestBody, { headers });
    }
    return this.http.put(this.dossierURL  + `${id}/--update-list-procedure-admininstration-code`,requestBody, { headers });
  }

  putSyncDossierEvictLTTP(code, requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.set('Content-Type', 'application/json; ; charset=utf-8');
    return this.http.post(this.apiProviderService.getUrl('digo', 'padman')  + `/v2/lyLichTuPhap/${code}/thuHoiHoSo`,requestBody, { headers });
  }

  putListProcedureAdmininstrationCodeMultiDossier(id, requestBody, ...isEnableYT): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.set('Content-Type', 'application/json; ; charset=utf-8');
    if(isEnableYT){
      return this.http.put(this.dossierURL  + `${id}/--update-list-procedure-admininstration-code-multi-dossier?isEnableYT=` + isEnableYT,requestBody, { headers });
    }
    return this.http.put(this.dossierURL  + `${id}/--update-list-procedure-admininstration-code-multi-dossier`,requestBody, { headers });
  }

  putGenerateProcedureAdmininstrationReceiptNumber(id, requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.put(this.dossierURL  + `${id}/--procedure-receipt-number-code`,requestBody, { headers });
  }

  // tbnohtttl qni
  postInfoDosserTBNOHTTTLQNI(dossierId, configId): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    return this.http.post<any>(this.padman + '/tbnohtttlqni/--send-info-dossier?id=' + dossierId + "&config-id=" + configId, { headers });
  }

  checkPaymentByCode(code): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    return this.http.post<any>(this.padman + '/dossier-payment/--check-dossier-payment?code=' + code , { headers });
  }

  updateAgency(dossierCode, agencyId): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    return this.http.put<any>(this.padman + '/dossier/update-agency?dossier-code=' + dossierCode + '&agency-id=' + agencyId, { headers });
  }

  postInfoProcessDosserTBNOHTTTLQNI(code, configId): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    return this.http.post<any>(this.padman + '/tbnohtttlqni/--send-info-process-dossier?code=' + code + "&config-id=" + configId, { headers });
  }

  findDossierByGeneratedProcedureAdmininstrationCode(body:any): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.post(this.dossierURL  + '--find-dossier-by-procedure-admininstration-code',body, { headers });
  }

  exportPageDossierToExcel(
    data: any[],
    excelFileName: string,
    sheetName: string
  ) {
    let columns = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z', 'AA', 'AB', 'AC', 'AD'];

    // create workbook and worksheet
    const workbook = new Workbook();
    workbook.creator = 'Snippet Coder';
    workbook.lastModifiedBy = 'SnippetCoder';
    workbook.created = new Date();
    workbook.modified = new Date();
    const worksheet = workbook.addWorksheet(sheetName);

    // Set row height
    worksheet.getRow(1).height = 60;
    worksheet.getRow(2).height = 90;

    // Set column width
    columns.forEach(columnName => worksheet.getColumn(columnName).width = 12)
    worksheet.getColumn("A").width = 7

    // Set table font
    columns.forEach(columnName => worksheet.getColumn(columnName).font = {name: 'Times New Roman', size: 12})

    // Set table header border, alignment, font
    columns.forEach(columnName => {
      for (var i = 1; i <= 2; i++) {
        worksheet.getCell(columnName + i).border = {top: {style: 'thin'}, left: {style: 'thin'}, bottom: {style: 'thin'}, right: {style: 'thin'}};
        worksheet.getCell(columnName + i).alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
        worksheet.getCell(columnName + i).font = {size: 13, bold: true, name: 'Times New Roman'};
      }
    })

    // TABLE HEADER
    worksheet.mergeCells('A1:A2');
    worksheet.mergeCells('B1:B2');
    worksheet.mergeCells('C1:C2');
    worksheet.mergeCells('D1:D2');
    worksheet.mergeCells('E1:E2');
    worksheet.mergeCells('F1:F2');
    worksheet.mergeCells('G1:G2');
    worksheet.mergeCells('H1:H2');
    worksheet.mergeCells('I1:I2');
    worksheet.mergeCells('J1:K1');
    worksheet.mergeCells('L1:L2');
    worksheet.mergeCells('M1:M2');
    worksheet.mergeCells('N1:N2');
    worksheet.mergeCells('O1:O2');
    worksheet.mergeCells('P1:P2');
    worksheet.mergeCells('Q1:Q2');
    worksheet.mergeCells('R1:R2');
    worksheet.mergeCells('S1:S2');
    worksheet.mergeCells('T1:T2');
    worksheet.mergeCells('U1:U2');
    worksheet.mergeCells('V1:V2');
    worksheet.mergeCells('W1:W2');
    worksheet.mergeCells('X1:X2');
    worksheet.mergeCells('Y1:Y2');
    worksheet.mergeCells('Z1:Z2');
    worksheet.mergeCells('AA1:AA2');
    worksheet.mergeCells('AB1:AD1');

    if (localStorage.getItem('language') === 'vi') {
      worksheet.getCell('A1').value = 'STT';
      worksheet.getCell('B1').value = 'Tên công ty';
      worksheet.getCell('C1').value = 'Địa chỉ';
      worksheet.getCell('D1').value = 'Loại hình doanh nghiệp';
      worksheet.getCell('E1').value = 'Số giấy chứng nhận ĐKKD';
      worksheet.getCell('F1').value = 'Lĩnh vực kinh doanh của đơn vị';
      worksheet.getCell('G1').value = 'Điện thoại';
      worksheet.getCell('H1').value = 'Email';
      worksheet.getCell('I1').value = 'Họ tên người nước ngoài';
      worksheet.getCell('J1').value = 'Giới tính';
      worksheet.getCell('J2').value = 'Nam';
      worksheet.getCell('K2').value = 'Nữ';
      worksheet.getCell('L1').value = 'Ngày tháng năm sinh';
      worksheet.getCell('M1').value = 'Quốc tịch';
      worksheet.getCell('N1').value = 'Số hộ chiếu';
      worksheet.getCell('O2').value = 'Vị trí công việc';
      worksheet.getCell('P2').value = 'Chức danh công việc';
      worksheet.getCell('Q2').value = 'Hình thức làm việc';
      worksheet.getCell('R2').value = 'Tên doanh nghiệp làm việc';
      worksheet.getCell('S2').value = 'Địa điểm làm việc';
      worksheet.getCell('T2').value = 'Mức lương';
      worksheet.getCell('U2').value = 'Ngày bắt đầu làm việc';
      worksheet.getCell('V2').value = 'Ngày kết thúc làm việc';
      worksheet.getCell('W1').value = 'Số biên nhận';
      worksheet.getCell('X2').value = 'Mã thủ tục';
      worksheet.getCell('Y1').value = 'Tên thủ tục';
      worksheet.getCell('Z1').value = 'Ngày nộp hồ sơ';
      worksheet.getCell('AA1').value = 'Ngày tiếp nhận hồ sơ';
      worksheet.getCell('AB1').value = 'Thông tin người nộp hồ sơ';
      worksheet.getCell('AB2').value = 'Họ tên';
      worksheet.getCell('AC2').value = 'Số điện thoại';
      worksheet.getCell('AD2').value = 'Email';
    }
    else if (localStorage.getItem('language') === 'en') {
      worksheet.getCell('A1').value = 'No.';
      worksheet.getCell('B1').value = 'Name of organization';
      worksheet.getCell('C1').value = 'Address';
      worksheet.getCell('D1').value = 'Type of business';
      worksheet.getCell('E1').value = 'Business registration certificate code';
      worksheet.getCell('F1').value = 'Business sector';
      worksheet.getCell('G1').value = 'Phone number';
      worksheet.getCell('H1').value = 'Email';
      worksheet.getCell('I1').value = 'Foreigner fullname';
      worksheet.getCell('J1').value = 'Gender';
      worksheet.getCell('J2').value = 'Male';
      worksheet.getCell('K2').value = 'Female';
      worksheet.getCell('L1').value = 'Date of birth';
      worksheet.getCell('M1').value = 'Nationality';
      worksheet.getCell('N1').value = 'Passport number';
      worksheet.getCell('O2').value = 'Job position';
      worksheet.getCell('P2').value = 'Job title';
      worksheet.getCell('Q2').value = 'Ways of working';
      worksheet.getCell('R2').value = 'Company name';
      worksheet.getCell('S2').value = 'Company address';
      worksheet.getCell('T2').value = 'Salary';
      worksheet.getCell('U2').value = 'Start date';
      worksheet.getCell('V2').value = 'End date';
      worksheet.getCell('W1').value = 'Dossier code';
      worksheet.getCell('X2').value = 'Procedure code';
      worksheet.getCell('Y1').value = 'Procedure name';
      worksheet.getCell('Z1').value = 'Applied date';
      worksheet.getCell('AA1').value = 'Accepted date';
      worksheet.getCell('AA2').value = 'Infomation of applicant';
      worksheet.getCell('AB2').value = 'Fullname';
      worksheet.getCell('AC2').value = 'Phone number';
      worksheet.getCell('AD2').value = 'Email';
    }

    // Add Data
    var i = 1;
    data.forEach(dossier => {
      // Prepare Data
      const organizationAddress = []
      if (!!dossier.applicant?.data?.address1) {
        organizationAddress.push(dossier.applicant.data.address1);
      }
      if (!!dossier.applicant?.data?.village1?.label) {
        organizationAddress.push(dossier.applicant.data.village1.label);
      }
      if (!!dossier.applicant?.data?.district1?.label) {
        organizationAddress.push(dossier.applicant.data.district1.label);
      }
      if (!!dossier.applicant?.data?.province1?.label) {
        organizationAddress.push(dossier.applicant.data.province1.label);
      }
      const soGP = !!dossier.eForm?.data?.SoGP ? dossier.eForm.data.SoGP : '';
      const birthdate = this.datePipe.transform(dossier.eForm?.data?.NTNSNLD, 'dd/MM/yyyy');
      const workLocation = [];
      if (!!dossier.eForm?.data?.SNLV) {
        workLocation.push(dossier.eForm.data.SNLV);
      }
      if (!!dossier.eForm?.data?.PXLV?.label) {
        workLocation.push(dossier.eForm.data.PXLV.label);
      }
      if (!!dossier.eForm?.data?.QHLV?.label) {
        workLocation.push(dossier.eForm.data.QHLV.label);
      }
      if (!!dossier.eForm?.data?.TinhTPLV?.label) {
        workLocation.push(dossier.eForm.data.TinhTPLV.label);
      }
      const startDate = this.datePipe.transform(dossier.eForm?.data?.TuNgay, 'dd/MM/yyyy');
      const endDate = this.datePipe.transform(dossier.eForm?.data?.DenNgay, 'dd/MM/yyyy');
      const appliedDate = this.datePipe.transform(dossier.appliedDate, 'dd/MM/yyyy HH:mm:ss');
      const acceptedDate = this.datePipe.transform(dossier.acceptedDate, 'dd/MM/yyyy HH:mm:ss');

      const eachRow = [
        i++,
        dossier.applicant?.data?.organization,
        organizationAddress.join(', '),
        dossier.eForm?.data?.LoaiHinh,
        soGP,
        dossier.eForm?.data?.LVHD,
        dossier.eForm?.data?.DT?.value,
        dossier.eForm?.data?.Email,
        dossier.eForm?.data?.HoTenNLD,
        (dossier.eForm?.data?.GT == "Nam"  ? "x" : ""),
        (dossier.eForm?.data?.GT == "Nữ" ? "x" : ""),
        birthdate,
        dossier.eForm?.data?.QuocTichNLD?.label,
        dossier.eForm?.data?.HoChieu,
        dossier.eForm?.data?.ViTriCV,
        dossier.eForm?.data?.ChucDanhCV,
        dossier.eForm?.data?.HinhThucLV,
        dossier.eForm?.data?.LamViecTai,
        workLocation.join(', '),
        dossier.eForm?.data?.MucLuong,
        startDate,
        endDate,
        dossier.code,
        dossier.procedure?.code,
        dossier.procedure?.translate?.name,
        appliedDate,
        acceptedDate,
        dossier.applicant?.data?.fullname,
        dossier.applicant?.data?.phoneNumber,
        dossier.applicant?.data?.email
      ];
      worksheet.addRow(eachRow);
    })

    // Add Border & Alignment for data cell
    for (var j = 3; j <= i + 1; j++) {
      columns.forEach(columnName => {
        worksheet.getCell(columnName + j).border = {top: {style: 'thin'}, left: {style: 'thin'}, bottom: {style: 'thin'}, right: {style: 'thin'}};
        worksheet.getCell(columnName + j).alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
      })
    }

    // Save Excel File
    workbook.xlsx.writeBuffer().then((dataBuffer: ArrayBuffer) => {
      const blob = new Blob([dataBuffer], {type: EXCEL_TYPE});
      fs.saveAs(blob, excelFileName + EXCEL_EXTENSION);
    });
  }

  exportSelectedDossierToExcel(
    data: any[],
    excelFileName: string,
    sheetName: string
  ) {
    let columns = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z', 'AA', 'AB'];

    // create workbook and worksheet
    const workbook = new Workbook();
    workbook.creator = 'Snippet Coder';
    workbook.lastModifiedBy = 'SnippetCoder';
    workbook.created = new Date();
    workbook.modified = new Date();
    const worksheet = workbook.addWorksheet(sheetName);

    // Set row height
    worksheet.getRow(1).height = 60;
    worksheet.getRow(2).height = 90;

    // Set column width
    columns.forEach(columnName => worksheet.getColumn(columnName).width = 12)
    worksheet.getColumn("A").width = 7

    // Set table font
    columns.forEach(columnName => worksheet.getColumn(columnName).font = {name: 'Times New Roman', size: 12})

    // Set table header border, alignment, font
    columns.forEach(columnName => {
      for (var i = 1; i <= 2; i++) {
        worksheet.getCell(columnName + i).border = {top: {style: 'thin'}, left: {style: 'thin'}, bottom: {style: 'thin'}, right: {style: 'thin'}};
        worksheet.getCell(columnName + i).alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
        worksheet.getCell(columnName + i).font = {size: 13, bold: true, name: 'Times New Roman'};
      }
    })

    // TABLE HEADER
    worksheet.mergeCells('A1:A2');
    worksheet.mergeCells('B1:B2');
    worksheet.mergeCells('C1:C2');
    worksheet.mergeCells('D1:D2');
    worksheet.mergeCells('E1:E2');
    worksheet.mergeCells('F1:F2');
    worksheet.mergeCells('G1:G2');
    worksheet.mergeCells('H1:H2');
    worksheet.mergeCells('I1:I2');
    worksheet.mergeCells('J1:K1');
    worksheet.mergeCells('L1:L2');
    worksheet.mergeCells('M1:M2');
    worksheet.mergeCells('N1:N2');
    worksheet.mergeCells('U1:V1');
    worksheet.mergeCells('W1:W2');
    worksheet.mergeCells('X1:X2');
    worksheet.mergeCells('Y1:Y2');
    worksheet.mergeCells('Z1:AB1');

    if (localStorage.getItem('language') === 'vi') {
      worksheet.getCell('A1').value = 'STT';
      worksheet.getCell('B1').value = 'Tên công ty';
      worksheet.getCell('C1').value = 'Địa chỉ';
      worksheet.getCell('D1').value = 'Loại hình doanh nghiệp';
      worksheet.getCell('E1').value = 'Số giấy chứng nhận ĐKKD';
      worksheet.getCell('F1').value = 'Lĩnh vực kinh doanh của đơn vị';
      worksheet.getCell('G1').value = 'Điện thoại';
      worksheet.getCell('H1').value = 'Email';
      worksheet.getCell('I1').value = 'Họ tên người nước ngoài';
      worksheet.getCell('J1').value = 'Giới tính';
      worksheet.getCell('J2').value = 'Nam';
      worksheet.getCell('K2').value = 'Nữ';
      worksheet.getCell('L1').value = 'Ngày tháng năm sinh';
      worksheet.getCell('M1').value = 'Quốc tịch';
      worksheet.getCell('N1').value = 'Số hộ chiếu';
      worksheet.getCell('O2').value = 'Vị trí công việc';
      worksheet.getCell('P2').value = 'Chức danh công việc';
      worksheet.getCell('Q2').value = 'Hình thức làm việc';
      worksheet.getCell('R2').value = 'Tên doanh nghiệp làm việc';
      worksheet.getCell('S2').value = 'Địa điểm làm việc';
      worksheet.getCell('T2').value = 'Mức lương';

      if (!!data[0].procedure?.code && data[0].procedure.code == this.procedureCodeSLDTBXHWorkPermit.procedureCodeLicensingWorkPermit) {
        worksheet.getCell("U1").value = "Giấy phép lao động";
      }
      else if (!!data[0].procedure?.code && data[0].procedure.code == this.procedureCodeSLDTBXHWorkPermit.procedureCodeRenewalWorkPermit) {
        worksheet.getCell("U1").value = "Cấp lại giấy phép lao động";
      }
      else if (!!data[0].procedure?.code && data[0].procedure.code == this.procedureCodeSLDTBXHWorkPermit.procedureCodeExtendWorkPermit) {
        worksheet.getCell("U1").value = "Gia hạn giấy phép lao động";
      }

      worksheet.getCell('U2').value = 'Ngày bắt đầu làm việc';
      worksheet.getCell('V2').value = 'Ngày kết thúc làm việc';
      worksheet.getCell('W1').value = 'Số biên nhận';
      worksheet.getCell('X1').value = 'Ngày nộp hồ sơ';
      worksheet.getCell('Y1').value = 'Ngày tiếp nhận hồ sơ';
      worksheet.getCell('Z1').value = 'Thông tin người nộp hồ sơ';
      worksheet.getCell('Z2').value = 'Họ tên';
      worksheet.getCell('AA2').value = 'Số điện thoại';
      worksheet.getCell('AB2').value = 'Email';
    }
    else if (localStorage.getItem('language') === 'en') {
      worksheet.getCell('A1').value = 'No.';
      worksheet.getCell('B1').value = 'Name of organization';
      worksheet.getCell('C1').value = 'Address';
      worksheet.getCell('D1').value = 'Type of business';
      worksheet.getCell('E1').value = 'Business registration certificate code';
      worksheet.getCell('F1').value = 'Business sector';
      worksheet.getCell('G1').value = 'Phone number';
      worksheet.getCell('H1').value = 'Email';
      worksheet.getCell('I1').value = 'Foreigner fullname';
      worksheet.getCell('J1').value = 'Gender';
      worksheet.getCell('J2').value = 'Male';
      worksheet.getCell('K2').value = 'Female';
      worksheet.getCell('L1').value = 'Date of birth';
      worksheet.getCell('M1').value = 'Nationality';
      worksheet.getCell('N1').value = 'Passport number';
      worksheet.getCell('O2').value = 'Job position';
      worksheet.getCell('P2').value = 'Job title';
      worksheet.getCell('Q2').value = 'Ways of working';
      worksheet.getCell('R2').value = 'Company name';
      worksheet.getCell('S2').value = 'Company address';
      worksheet.getCell('T2').value = 'Salary';

      if (!!data[0].procedure?.code && data[0].procedure.code == this.procedureCodeSLDTBXHWorkPermit.procedureCodeLicensingWorkPermit) {
        worksheet.getCell("U1").value = "Licensing work permit";
      }
      else if (!!data[0].procedure?.code && data[0].procedure.code == this.procedureCodeSLDTBXHWorkPermit.procedureCodeRenewalWorkPermit) {
        worksheet.getCell("U1").value = "Renewal work permit";
      }
      else if (!!data[0].procedure?.code && data[0].procedure.code == this.procedureCodeSLDTBXHWorkPermit.procedureCodeExtendWorkPermit) {
        worksheet.getCell("U1").value = "Extend work permit";
      }

      worksheet.getCell('U2').value = 'Start date';
      worksheet.getCell('V2').value = 'End date';
      worksheet.getCell('W1').value = 'Dossier code';
      worksheet.getCell('X1').value = 'Applied date';
      worksheet.getCell('Y1').value = 'Accepted date';
      worksheet.getCell('Z1').value = 'Infomation of applicant';
      worksheet.getCell('Z2').value = 'Fullname';
      worksheet.getCell('AA2').value = 'Phone number';
      worksheet.getCell('AB2').value = 'Email';
    }

    // Add Data
    var i = 1;
    data.forEach(dossier => {
      // Prepare Data
      const organizationAddress = []
      if (!!dossier.applicant?.data?.address1) {
        organizationAddress.push(dossier.applicant.data.address1);
      }
      if (!!dossier.applicant?.data?.village1?.label) {
        organizationAddress.push(dossier.applicant.data.village1.label);
      }
      if (!!dossier.applicant?.data?.district1?.label) {
        organizationAddress.push(dossier.applicant.data.district1.label);
      }
      if (!!dossier.applicant?.data?.province1?.label) {
        organizationAddress.push(dossier.applicant.data.province1.label);
      }
      const soGP = !!dossier.eForm?.data?.SoGP ? dossier.eForm.data.SoGP : '';
      const birthdate = this.datePipe.transform(dossier.eForm?.data?.NTNSNLD, 'dd/MM/yyyy');
      const workLocation = [];
      if (!!dossier.eForm?.data?.SNLV) {
        workLocation.push(dossier.eForm.data.SNLV);
      }
      if (!!dossier.eForm?.data?.PXLV?.label) {
        workLocation.push(dossier.eForm.data.PXLV.label);
      }
      if (!!dossier.eForm?.data?.QHLV?.label) {
        workLocation.push(dossier.eForm.data.QHLV.label);
      }
      if (!!dossier.eForm?.data?.TinhTPLV?.label) {
        workLocation.push(dossier.eForm.data.TinhTPLV.label);
      }
      const startDate = this.datePipe.transform(dossier.eForm?.data?.TuNgay, 'dd/MM/yyyy');
      const endDate = this.datePipe.transform(dossier.eForm?.data?.DenNgay, 'dd/MM/yyyy');
      const appliedDate = this.datePipe.transform(dossier.appliedDate, 'dd/MM/yyyy HH:mm:ss');
      const acceptedDate = this.datePipe.transform(dossier.acceptedDate, 'dd/MM/yyyy HH:mm:ss');

      const eachRow = [
        i++,
        dossier.applicant?.data?.organization,
        organizationAddress.join(', '),
        dossier.eForm?.data?.LoaiHinh,
        soGP,
        dossier.eForm?.data?.LVHD,
        dossier.eForm?.data?.DT?.value,
        dossier.eForm?.data?.Email,
        dossier.eForm?.data?.HoTenNLD,
        (dossier.eForm?.data?.GT == "Nam"  ? "x" : ""),
        (dossier.eForm?.data?.GT == "Nữ" ? "x" : ""),
        birthdate,
        dossier.eForm?.data?.QuocTichNLD?.label,
        dossier.eForm?.data?.HoChieu,
        dossier.eForm?.data?.ViTriCV,
        dossier.eForm?.data?.ChucDanhCV,
        dossier.eForm?.data?.HinhThucLV,
        dossier.eForm?.data?.LamViecTai,
        workLocation.join(', '),
        dossier.eForm?.data?.MucLuong,
        startDate,
        endDate,
        dossier.code,
        appliedDate,
        acceptedDate,
        dossier.applicant?.data?.fullname,
        dossier.applicant?.data?.phoneNumber,
        dossier.applicant?.data?.email
      ];
      worksheet.addRow(eachRow);
    })

    // Add Border & Alignment for data cell
    for (var j = 3; j <= i + 1; j++) {
      columns.forEach(columnName => {
        worksheet.getCell(columnName + j).border = {top: {style: 'thin'}, left: {style: 'thin'}, bottom: {style: 'thin'}, right: {style: 'thin'}};
        worksheet.getCell(columnName + j).alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
      })
    }

    // Save Excel File
    workbook.xlsx.writeBuffer().then((dataBuffer: ArrayBuffer) => {
      const blob = new Blob([dataBuffer], {type: EXCEL_TYPE});
      fs.saveAs(blob, excelFileName + EXCEL_EXTENSION);
    });
  }

  reReceptionAdditionalDossier(dossierId, body): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.put<any>(this.dossierURL + dossierId + '/--re-reception-additional-dossier', body, {headers});
  }
  exportPageDossierToExcelQni(
    data: any[],
    excelFileName: string,
    sheetName: string
  ) {
    let columns = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L'];

    // create workbook and worksheet
    const workbook = new Workbook();
    workbook.creator = 'Snippet Coder';
    workbook.lastModifiedBy = 'SnippetCoder';
    workbook.created = new Date();
    workbook.modified = new Date();
    const worksheet = workbook.addWorksheet(sheetName);

    // Set row height
    worksheet.getRow(1).height = 60;
   // worksheet.getRow(2).height = 90;

    // Set column width
    columns.forEach(columnName => worksheet.getColumn(columnName).width = 12)
    worksheet.getColumn("A").width = 7

    // Set table font
    columns.forEach(columnName => worksheet.getColumn(columnName).font = {name: 'Times New Roman', size: 12})

    // Set table header border, alignment, font
    columns.forEach(columnName => {
      for (var i = 1; i <= 1; i++) {
        worksheet.getCell(columnName + i).border = {top: {style: 'thin'}, left: {style: 'thin'}, bottom: {style: 'thin'}, right: {style: 'thin'}};
        worksheet.getCell(columnName + i).alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
        worksheet.getCell(columnName + i).font = {size: 13, bold: true, name: 'Times New Roman'};
      }
    })

      worksheet.getCell('A1').value = 'STT';
      worksheet.getCell('B1').value = 'Số hồ sơ';
      worksheet.getCell('C1').value = 'Tên thủ tục hành chính';
      worksheet.getCell('D1').value = 'Tên lĩnh vực';
      worksheet.getCell('E1').value = 'Nội dung yêu cầu giải quyết';
      worksheet.getCell('F1').value = 'Ngày tiếp nhận';
      worksheet.getCell('G1').value = 'Ngày hẹn trả';
      worksheet.getCell('H1').value = 'Ngày kết thúc xử lý';
      worksheet.getCell('I1').value = 'Chủ hồ sơ';
      worksheet.getCell('J1').value = 'Người nộp hồ sơ';
      worksheet.getCell('K1').value = 'Số điện thoại';
      worksheet.getCell('L1').value = 'Trạng thái hồ sơ';

    // Add Data
    var i = 1;
    data.forEach(dossier => {
      const eachRow = [
        i++,
        dossier.code,
        dossier.procedure?.translate[0]?.name,
        dossier.procedure?.sector?.name[0]?.name,
        dossier.applicant?.data?.noidungyeucaugiaiquyet,
        this.datePipe.transform(dossier.acceptedDate, 'dd/MM/yyyy HH:mm:ss'),
          this.datePipe.transform(dossier.appointmentDate, 'dd/MM/yyyy HH:mm:ss'),
            this.datePipe.transform(dossier.completedDate, 'dd/MM/yyyy HH:mm:ss'),
        dossier.applicant?.data?.ownerFullname,
        dossier.applicant?.data?.fullname,
        dossier.applicant?.data?.phoneNumber,
        dossier.dossierTaskStatus?.name[0]?.name
      ];
      worksheet.addRow(eachRow);
    })

    // Add Border & Alignment for data cell
    for (var j = 1; j <= i; j++) {
      columns.forEach(columnName => {
        worksheet.getCell(columnName + j).border = {top: {style: 'thin'}, left: {style: 'thin'}, bottom: {style: 'thin'}, right: {style: 'thin'}};
        worksheet.getCell(columnName + j).alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
      })
    }

    // Save Excel File
    workbook.xlsx.writeBuffer().then((dataBuffer: ArrayBuffer) => {
      const blob = new Blob([dataBuffer], {type: EXCEL_TYPE});
      fs.saveAs(blob, excelFileName + EXCEL_EXTENSION);
    });
  }


  exportPageDossierToExcelHCM(
    data: any[],
    excelFileName: string,
    sheetName: string
  ) {
    let columns = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I'];

    //tlqkhanh.hcm-IGATESUPP-68633
    if(this.isShowOrganizationName){
      columns.push('J');
    }
    //endof tlqkhanh.hcm-IGATESUPP-68633    
    // create workbook and worksheet
    const workbook = new Workbook();
    workbook.creator = 'Snippet Coder';
    workbook.lastModifiedBy = 'SnippetCoder';
    workbook.created = new Date();
    workbook.modified = new Date();
    const worksheet = workbook.addWorksheet(sheetName);

    // Set row height
    worksheet.getRow(1).height = 60;
   // worksheet.getRow(2).height = 90;

    // Set column width
    columns.forEach(columnName => worksheet.getColumn(columnName).width = 12)
    worksheet.getColumn("A").width = 7
    worksheet.getColumn("B").width = 24
    worksheet.getColumn("C").width = 36
    // Set table font
    columns.forEach(columnName => worksheet.getColumn(columnName).font = {name: 'Times New Roman', size: 12})

    // Set table header border, alignment, font
    columns.forEach(columnName => {
      for (var i = 1; i <= 1; i++) {
        worksheet.getCell(columnName + i).border = {top: {style: 'thin'}, left: {style: 'thin'}, bottom: {style: 'thin'}, right: {style: 'thin'}};
        worksheet.getCell(columnName + i).alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
        worksheet.getCell(columnName + i).font = {size: 13, bold: true, name: 'Times New Roman'};
      }
    })

      worksheet.getCell('A1').value = 'STT';
      worksheet.getCell('B1').value = 'Số hồ sơ';
      worksheet.getCell('C1').value = 'Tên thủ tục hành chính';
      worksheet.getCell('D1').value = 'Người nộp hồ sơ';
      //tlqkhanh.hcm-IGATESUPP-68633
      if(this.isShowOrganizationName){
        worksheet.getCell('E1').value = 'Tên tổ chức/cá nhân';
        worksheet.getCell('F1').value = 'Ngày nộp';
        worksheet.getCell('G1').value = 'Ngày tiếp nhận';
        worksheet.getCell('H1').value = 'Ngày hẹn trả';
        worksheet.getCell('I1').value = 'Cơ quan thực hiện';
        worksheet.getCell('J1').value = 'Trạng thái hồ sơ';
        }else{
      worksheet.getCell('E1').value = 'Ngày nộp';
      worksheet.getCell('F1').value = 'Ngày tiếp nhận';
      worksheet.getCell('G1').value = 'Ngày hẹn trả';
      worksheet.getCell('H1').value = 'Cơ quan thực hiện';
      worksheet.getCell('I1').value = 'Trạng thái hồ sơ';
      }
      //endof tlqkhanh.hcm-IGATESUPP-68633   

    // Add Data
    var i = 1;
    //tlqkhanh.hcm-IGATESUPP-68633
    if(this.isShowOrganizationName){    
    data.forEach(dossier => {
      const eachRow = [
        i++,
        dossier.code,
        dossier.procedure?.translate ? dossier.procedure?.translate?.name : '',
        dossier.applicant?.data?.fullname,
        dossier.hoVaTen,
        this.datePipe.transform(dossier.appliedDate, 'dd/MM/yyyy HH:mm:ss'),
        this.datePipe.transform(dossier.acceptedDate, 'dd/MM/yyyy HH:mm:ss'),
          this.datePipe.transform(dossier.appointmentDate, 'dd/MM/yyyy HH:mm:ss'),
        dossier.agencyProcessing ? dossier.agencyProcessing : '',
        dossier.dossierTaskStatus?.name ? dossier.dossierTaskStatus?.name : ''
      ];
      worksheet.addRow(eachRow);
    })
  }else{
    data.forEach(dossier => {
      const eachRow = [
        i++,
        dossier.code,
        dossier.procedure?.translate ? dossier.procedure?.translate?.name : '',
        dossier.applicant?.data?.fullname,
        this.datePipe.transform(dossier.appliedDate, 'dd/MM/yyyy HH:mm:ss'),
        this.datePipe.transform(dossier.acceptedDate, 'dd/MM/yyyy HH:mm:ss'),
          this.datePipe.transform(dossier.appointmentDate, 'dd/MM/yyyy HH:mm:ss'),
        dossier.agencyProcessing ? dossier.agencyProcessing : '',
        dossier.dossierTaskStatus?.name ? dossier.dossierTaskStatus?.name : ''
      ];
      worksheet.addRow(eachRow);
    })      
    }
    //endof tlqkhanh.hcm-IGATESUPP-68633

    // Add Border & Alignment for data cell
    for (var j = 1; j <= i; j++) {
      columns.forEach(columnName => {
        worksheet.getCell(columnName + j).border = {top: {style: 'thin'}, left: {style: 'thin'}, bottom: {style: 'thin'}, right: {style: 'thin'}};
        worksheet.getCell(columnName + j).alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
      })
    }

    // Save Excel File
    workbook.xlsx.writeBuffer().then((dataBuffer: ArrayBuffer) => {
      const blob = new Blob([dataBuffer], {type: EXCEL_TYPE});
      fs.saveAs(blob, excelFileName + EXCEL_EXTENSION);
    });
  }

  updateDossierMoneyReceipt(id, moneyReceipt): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    return this.http.put<any>(this.dossierURL + id + '/--update-money-receipt-hcm' + "?money-receipt=" + moneyReceipt , { headers });
  }

  updateDeadlineForAdditionalRequests(id, body): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    return this.http.put<any>(this.dossierURL + id + '/--update-deadline-for-additional-requests', body , { headers });
  }
  //Ko dùng
  updateStatusDigitizeBDG(searchStringDigitize): Observable<any> {
    return new Observable();
  }
  updateDossierBoTNMT(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.post(this.padman + '/dossier/' + id + '/--capnhathosoboTNMT/', { headers });
  }
  // ILis
  postDossierCreateIlis(dossierId, configId): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    return this.http.post<any>(this.padman + '/ilis/--receiving-dossier?id=' + dossierId + "&config-id=" + configId, { headers });
  }
  postDossierAdditionalRequestIlis(code, configId): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    return this.http.put<any>(this.padman + '/ilis/' + code + '/--update-additional-request' + "?config-id=" + configId , { headers });
  }
  postUpdateFinishDossierIlis(code, configId, type): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    return this.http.put<any>(this.padman + '/ilis/' + code + '/--update-result-finish-dossier' + "?config-id=" + configId +"&type=" + type , { headers });
  }
  // IGATESUPP-42021
  getListFileUpdateHistory (searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    return this.http.get(this.padman + '/dossier-form-file/getListFileAdditional?dossierId=' + searchString, { headers });
  }
    // Bhtn (ASXH)
    postDossierCreateBhtn(dossierId, configId): Observable<any> {
      let headers = new HttpHeaders();
      headers = headers.set('Content-Type', 'application/json');
      return this.http.post<any>(this.padman + '/bhtn/--receiving-dossier?id=' + dossierId + "&config-id=" + configId, { headers });
    }
    postDossierAdditionalRequestBhtn(code, configId): Observable<any> {
      let headers = new HttpHeaders();
      headers = headers.set('Content-Type', 'application/json');
      return this.http.put<any>(this.padman + '/bhtn/' + code + '/--update-additional-request' + "?config-id=" + configId , { headers });
    }
    postUpdateFinishDossierBhtn(code, configId, type): Observable<any> {
      let headers = new HttpHeaders();
      headers = headers.set('Content-Type', 'application/json');
      return this.http.put<any>(this.padman + '/bhtn/' + code + '/--update-result-finish-dossier' + "?config-id=" + configId +"&type=" + type , { headers });
    }

  syncDossierAsxh(id, configId): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    return this.http.post<any>(this.padman + '/asxh/--send?id=' + id + '&config-id=' + configId , { headers });
  }

  syncDossierAIMS(id, configId): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    return this.http.post<any>(this.padman + '/aims/--send?id=' + id + '&config-id=' + configId , { headers });
  }
  // save info licensebuild
  putLicenseBuildOnline(dossierId, body): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.put<any>(this.dossierURL + dossierId + '/--license', body, { headers });


  }
// get info licensebuild
  getInforLicenseBuild(dossierId): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
   return this.http.get(this.dossierURL + dossierId + '/--license',  { headers });
  }

  getListDossierDVCLT(search): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.get(this.dossierDVCLTURL + 'search' + search, { headers });
  }

  getListDossierDVCLTTracking(search): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers.append('Access-Control-Allow-Origin', '*');
   return this.http.get(this.dossierDVCLTURLTracking + 'search' + search, { headers });
  }

  getCommentOpinon(id, searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.dossierURL + id + '/--list-comment' + searchString, { headers });
  }

  postDeleteCommentOpinionDossier(dossierId, commentId): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.post<any>(this.dossierURL + dossierId +'/'+ commentId + '/--delete-comment', { headers });
  }

  postCommentOpinionDossier(id, requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.post<any>(this.dossierURL + id + '/--comment', requestBody, { headers });
  }

  getDossierMenuTaskRingSubMenu(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get((!this.padmanReport ? this.dossierURL : this.dossierURLReport)  + '--menu-remind-all-agency-sub-noti-menu?' + searchString, { headers });
  }

  getDossierMenuTaskRingSubMenuDetail(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get((!this.padmanReport ? this.dossierURL : this.dossierURLReport)  + '--menu-remind-all-agency-sub-noti-menu-detail?' + searchString, { headers });
  }

  exportPageDossierToExcelQNM(
    data: any[],
    excelFileName: string,
    sheetName: string,
    isRefuse = false
  ) {
    let columns = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M'];
    if (this.isAddColumDataExport) {
      columns = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P'];
    }

    // create workbook and worksheet
    const workbook = new Workbook();
    workbook.creator = 'Snippet Coder';
    workbook.lastModifiedBy = 'SnippetCoder';
    workbook.created = new Date();
    workbook.modified = new Date();
    const worksheet = workbook.addWorksheet(sheetName);

    // Set row height
    worksheet.getRow(1).height = 60;
   // worksheet.getRow(2).height = 90;

    // Set column width
    columns.forEach(columnName => worksheet.getColumn(columnName).width = 12)
    worksheet.getColumn("A").width = 7

    // Set table font
    columns.forEach(columnName => worksheet.getColumn(columnName).font = {name: 'Times New Roman', size: 12})

    // Set table header border, alignment, font
    columns.forEach(columnName => {
      for (var i = 1; i <= 1; i++) {
        worksheet.getCell(columnName + i).border = {top: {style: 'thin'}, left: {style: 'thin'}, bottom: {style: 'thin'}, right: {style: 'thin'}};
        worksheet.getCell(columnName + i).alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
        worksheet.getCell(columnName + i).font = {size: 13, bold: true, name: 'Times New Roman'};
      }
    })

      worksheet.getCell('A1').value = 'STT';
      worksheet.getCell('B1').value = 'Số hồ sơ';
      worksheet.getCell('C1').value = 'Tên thủ tục hành chính';
      worksheet.getCell('D1').value = 'Tên lĩnh vực';
      worksheet.getCell('E1').value = 'Nội dung yêu cầu giải quyết';
      worksheet.getCell('F1').value = 'Ngày tiếp nhận';
      worksheet.getCell('G1').value = 'Ngày hẹn trả';
      worksheet.getCell('H1').value = 'Ngày kết thúc xử lý';
      worksheet.getCell('I1').value = 'Chủ hồ sơ';
      worksheet.getCell('J1').value = 'Người nộp hồ sơ';
      worksheet.getCell('K1').value = 'Số điện thoại';
      worksheet.getCell('L1').value = 'Trạng thái hồ sơ';
      if(isRefuse){
        worksheet.getCell('M1').value = 'Lý do từ chối';
      }else{
        worksheet.getCell('M1').value = 'Trạng thái thanh toán';
      }
      if (this.isAddColumDataExport) {
        worksheet.getCell('K1').value = 'Số điện thoại người nộp';
        worksheet.getCell('N1').value = 'Số điện thoại chủ hồ sơ';
        worksheet.getCell('O1').value = 'Địa chỉ chủ hồ sơ';
        worksheet.getCell('P1').value = 'Địa chỉ người nộp';
      }


    // Add Data
    var i = 1;
    data.forEach(dossier => {
      const tongTienTemp = dossier?.extendQNM?.tongTien;
      const thanhToanTemp = dossier?.extendQNM?.thanhToan;
      const dossierFeeTemp = dossier?.extendQNM?.dossierFee;
      const totalCostQNM = this.getTotalCostQNM(tongTienTemp, thanhToanTemp, dossierFeeTemp);
      let eachRow = [
        i++,
        dossier.code,
        dossier.procedure?.translate[0]?.name,
        dossier.procedure?.sector?.name[0]?.name,
        dossier.applicant?.data?.noidungyeucaugiaiquyet,
        this.datePipe.transform(dossier.acceptedDate, 'dd/MM/yyyy HH:mm:ss'),
          this.datePipe.transform(dossier.appointmentDate, 'dd/MM/yyyy HH:mm:ss'),
            this.datePipe.transform(dossier.completedDate, 'dd/MM/yyyy HH:mm:ss'),
        dossier.applicant?.data?.ownerFullname,
        dossier.applicant?.data?.fullname,
        dossier.applicant?.data?.phoneNumber,
        dossier.dossierTaskStatus?.name[0]?.name,
        isRefuse?dossier.dossierStatus?.comment?.replace(/<[^>]*>/g, '').replace(/\&nbsp;/g, '').replace(/&lt;/g, '<').replace(/&gt;/g, '>'):totalCostQNM.value
      ];
      if (this.isAddColumDataExport) {
        const strAddress = dossier.applicant?.data?.address
            + (!!dossier.applicant?.data?.village?.label ? ',' + dossier.applicant?.data?.village?.label : '')
            + (!!dossier.applicant?.data?.district?.label ? ',' + dossier.applicant?.data?.district?.label : '')
            + (!!dossier.applicant?.data?.province?.label ? ',' + dossier.applicant?.data?.province?.label : '');
        eachRow.push(dossier.applicant?.data?.ownerPhoneNumber, dossier.applicant?.data?.ownerAddress, strAddress);
      }
      worksheet.addRow(eachRow);
    });

    // Add Border & Alignment for data cell
    for (var j = 1; j <= i; j++) {
      columns.forEach(columnName => {
        worksheet.getCell(columnName + j).border = {top: {style: 'thin'}, left: {style: 'thin'}, bottom: {style: 'thin'}, right: {style: 'thin'}};
        worksheet.getCell(columnName + j).alignment = {horizontal: 'center', vertical: 'middle', wrapText: true};
      })
    }

    // Save Excel File
    workbook.xlsx.writeBuffer().then((dataBuffer: ArrayBuffer) => {
      const blob = new Blob([dataBuffer], {type: EXCEL_TYPE});
      fs.saveAs(blob, excelFileName + EXCEL_EXTENSION);
    });
  }

  exportCancelDossierToExcel(
    data: any[],
    excelFileName: string,
    sheetName: string
  ) {
    // create workbook and worksheet
    const workbook = new Workbook();
    workbook.creator = 'Snippet Coder';
    workbook.lastModifiedBy = 'SnippetCoder';
    workbook.created = new Date();
    workbook.modified = new Date();
    const worksheet = workbook.addWorksheet(sheetName);

    let columns = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J'];
    worksheet.views = [{}]

    columns.forEach(columnName => {
      worksheet.getColumn(columnName).font = { name: 'Times New Roman', size: 11 };
      worksheet.getColumn(columnName).alignment = { horizontal: 'left', vertical: 'middle', wrapText: true };
      worksheet.getColumn(columnName).width = 15;
    })

    worksheet.mergeCells('A1:J1');
    worksheet.getCell('A1').value = 'DANH SÁCH HỒ SƠ KHÔNG CẦN XỬ LÝ';
    worksheet.getCell('A1').font = { bold : true, size: 12 };
    worksheet.getCell('A1').alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
    worksheet.getRow(1).height = 30;

    worksheet.getRow(2).height = 30;
    worksheet.getRow(2).alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getRow(2).font = { bold: true };
    worksheet.getRow(3).height = 30;
    worksheet.getRow(3).alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getRow(3).font = { bold: true };

    // header
    worksheet.mergeCells('A2:A3');
    worksheet.getCell('A2').value = 'STT';
    worksheet.getColumn('A').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getColumn('A').width = 5;

    worksheet.mergeCells('B2:B3');
    worksheet.getCell('B2').value = 'Mã hồ sơ';

    worksheet.mergeCells('C2:C3');
    worksheet.getCell('C2').value = 'Mã thủ tục';

    worksheet.mergeCells('D2:D3');
    worksheet.getCell('D2').value = 'Tên thủ tục';

    worksheet.mergeCells('E2:E3');
    worksheet.getCell('E2').value = 'Ngày nộp hồ sơ';

    worksheet.mergeCells('F2:H2');
    worksheet.getCell('F2').value = 'Thông tin người nộp hồ sơ';
    worksheet.getCell('F3').value = 'Họ tên';
    worksheet.getCell('G3').value = 'Số điện thoại';
    worksheet.getCell('H3').value = 'Email';

    worksheet.mergeCells('I2:I3');
    worksheet.getCell('I2').value = 'Lí do';

    worksheet.mergeCells('J2:J3');
    worksheet.getCell('J2').value = 'Trạng thái';

    // header border
    ['A2','B2','C2','D2','E2','F3','G3','H3','F2','I2','J2'].forEach(columnName => {
      worksheet.getCell(columnName).border = {top: {style: 'thin'}, left: {style: 'thin'}, bottom: {style: 'thin'}, right: {style: 'thin'}};
    });

    // add data
    let i = 1;
    data.forEach( dossier => {
      const appliedDate = this.datePipe.transform(dossier.appliedDate, 'dd/MM/yyyy HH:mm:ss');
      let comment = '';
      if (!!dossier.dossierStatus.comment) {
        comment = dossier.dossierStatus.comment.replace(/<\/?[^>]+(>|$)/g, "");
      }

      const eachRow = [
        i++,                                // STT
        dossier.code,                       // Mã hồ sơ
        dossier.procedure.code,             // Mã thủ tục
        dossier.procedure.translate.name,   // Tên thủ tục
        appliedDate,                        // Ngày nộp hồ sơ
        dossier.applicant.data.fullname,    // Họ tên
        dossier.applicant.data.phoneNumber, // Số điện thoại
        dossier.applicant.data.email,       // Email
        comment,                            // Lí do
        dossier.dossierStatus.name          // Trạng thái
      ];
      worksheet.addRow(eachRow);
    })

    // Add Border & Alignment for data cell
    for (var j = 4; j < i+3; j++) {
      columns.forEach(columnName => {
        worksheet.getCell(columnName + j).border = {left: {style: 'thin'}, bottom: {style: 'thin'}, right: {style: 'thin'}};
      })
    }

    // Save Excel File
    workbook.xlsx.writeBuffer().then((dataBuffer: ArrayBuffer) => {
      const blob = new Blob([dataBuffer], {type: EXCEL_TYPE});
      fs.saveAs(blob, excelFileName + EXCEL_EXTENSION);
    });
  }

  getTotalCostQNM(cost, paid, dossierFee) {
    // cost: tong tien
    // paid: đa thanh toan

    let result = {
      id: '0',
      value: ''
    };
    if (!dossierFee || dossierFee?.length === 0) {
      result = {
        id: '1',
        value: 'Không tính phí'
      };
    } else {
      let totalPaid = dossierFee.reduce((sum, item) => sum + item.paid, 0);
      if (totalPaid === 0 && cost !== 0) {
        result = {
          id: '2',
          value: 'Chưa thanh toán'
        };
      } else {
        if (cost === totalPaid && cost !== 0) {
          result = {
            id: '4',
            value: 'Đã thanh toán'
          };
        } else {
          if (cost > totalPaid && paid !== 0) {
            result = {
              id: '3',
              value: 'Đã thanh toán 1 phần'
            };
          } else {
            result = {
              id: '1',
              value: 'Không tính phí'
            };
          }
        }
      }
    }
    return result;
  }


  getConfigureReceiptCode(agencyId): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.basepad + '/configure-receipt-code/get-configure-by-agency?agency-id=' + agencyId, { headers });
  }

  putReceiptCode(dossierId, requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.put<any>(this.dossierURL + dossierId + '/--add-receipt-code', requestBody, { headers });
  }
  getReceiptCodeByTaskId(dossierId,taskId): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get<any>(this.dossierURL + dossierId + '/--get-receipt-code-by-task-id?task-id=' + taskId, { headers });
  }
  findProcessDefinitionTaskById(taskId): Observable<any>{
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    return this.http.get<any>(this.bpmPath + '/process-definition-task/' + taskId, { headers });
  }
  setDossierId(data: any) {
    this.dossierId.next(data);
  }

  getDossierId() {
    return this.dossierId.asObservable();
  }

  putTaskProcessingTime(id, taskId, requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.put<any>(this.dossierURL + id + '/--update-dossier-task-processing-time?task-id=' + taskId, requestBody, { headers });
  }
  //
  getListReportReceiptSearchInfo(searchString): Observable<any>{
    const URL = this.padman + '/dossier-receipt/search-list-receipt'
    let headers = new HttpHeaders();
    headers = headers.set('Accep-Language', localStorage.getItem('language'));
    return this.http.get(URL + searchString)
  }

  // IGATESUPP-51500 - Đánh giá lại chức năng phê duyệt hồ sơ - 19.06.2023
  postOnlyApprovalAgencyOfDossier(requestBody, code): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.put<any>(this.dossierURL + code + '/--set-only-approval-agency/', requestBody, { headers });
  }

  getListInfoDossierFee(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.dossierFeeURL +'--get-dossier-fee-list-nopublish' + searchString, { headers });
  }
  getListDossierReceiptPayment(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.padman +'/dossier-receipt/--find-dossier-receipt-payment' + searchString, { headers });
  }

  getProcessTimeResumeV2(id,type): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get<any>(this.dossierURL + id + '/--resumev2?type=' + type, { headers });
  }

  putDossierFeeQNM(dossierId): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    return this.http.put<any>(this.padman + '/dossier/' + dossierId + '/--update-dossierfee-extendqnm', { headers });
  }
  // region IGATESUPP-56935
  getAssigneeTask(dossierId, agencyId): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.dossierURL + dossierId + '/--assignee-task?agency-id=' + agencyId, { headers });
  }
  // endregion

  getCheckEnableLLTPDLK(processDefinitionId): Promise<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    let param = `?process-definition-id=${processDefinitionId ? processDefinitionId : ''}`;
    return this.http.get(this.dossierURL + '--isEnable-lltp-lgsp-dlk' + param, { headers }).toPromise();
  }

  checkReSendLLTPDLK(dossierId): Promise<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    let param = `?dossierId=${dossierId ? dossierId : ''}`;
    return this.http.get(this.dossierURL + '--check-resend-lltp-dlk' + param, { headers }).toPromise();
  }

  postLgspLLTPNhanHoSoDangKy_DLK(requestBody): Observable<any> {
    let headers = new HttpHeaders();
    // headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.post<any>(this.adapter + '/lgsp-dlk-lltp/nhanHoSoDangKy', requestBody, { headers });
    // return this.http.post<any>('http://localhost:8084/lgsp-dlk-lltp/nhanHoSoDangKy', requestBody, { headers });
  }

  isShowBtnResend(dossierId): Promise<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    let param = `?dossierId=${dossierId ? dossierId : ''}`;
    return this.http.get(this.dossierURL + '--is-showButton-lltp-dlk' + param, { headers }).toPromise();
  }

  postDossierOnlineQBH(requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.post<any>(this.dossierURL + '--apply-online-qbh', requestBody, { headers });
  }
  postLogSearhCSDLDCQBH(requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.post<any>(this.padman +'/qbh-log-statistic/logsearch-csdldc', requestBody, { headers });
  }
  putSurveyDossierOffline(requestBody, maHoSo): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.put<any>(this.dossierURL + '--update-survey-offline?maHoSo=' + maHoSo, requestBody, { headers });
  }

  getExportDossierExcelSync(data): any {
    return new Promise((resolve) => {
      this.getDossierExcelSync(data).then(res => {
        const blob = new Blob([res.body], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
        let filename = 'danh-sach-ho-so-dong-bo.xlsx';
        if (res.headers.get('content-disposition') != null) {
          filename = res.headers.get('content-disposition').split(';')[1].split('=')[1].replace(/\"/g, '');
        }
        var blobUrl = URL.createObjectURL(blob);

        var xhr = new XMLHttpRequest;
        xhr.responseType = 'blob';

        xhr.onload = function () {
          var recoveredBlob = xhr.response;
          var reader = new FileReader;
          reader.onload = function () {
            var base64data = reader.result.toString();
            var object = JSON.stringify({
              mimeType: filename.substring(filename.lastIndexOf(".") + 1),
              name: filename.substring(0, filename.lastIndexOf(".")),
              data: base64data.split(',')[1]
            });
            let anchor = document.createElement("a");
            anchor.download = filename;
            anchor.href = base64data;
            anchor.click();
          };
          reader.readAsDataURL(recoveredBlob);
        };

        xhr.open('GET', blobUrl);
        xhr.send();
        resolve(true);
      })
        .catch(err => {
          console.log("err", err.status );
          if (err.status === 500) {
            this.snackbar.openSnackBar(0, '', 'Hệ thống tạm thời không thể xuất file excel!', 'error_notification', this.config.expiredTime);
          }
          resolve(false)
        });
    })

  }
  excelExportSurveyDossier(params: string): any {
    return new Promise((resolve) => {
      this.downloadExportSurveyDossier(params).then(res => {
        const blob = new Blob([res.body], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
        let filename = 'mau_ho_so_den_han.xlsx';
        if (res.headers.get('content-disposition') != null) {
          filename = res.headers.get('content-disposition').split(';')[1].split('=')[1].replace(/\"/g, '');
        }
        var blobUrl = URL.createObjectURL(blob);

        var xhr = new XMLHttpRequest;
        xhr.responseType = 'blob';

        xhr.onload = function() {
          var recoveredBlob = xhr.response;
          var reader = new FileReader;
          reader.onload = function() {
            var base64data = reader.result.toString();
            var object = JSON.stringify({
              mimeType:filename.substring(filename.lastIndexOf(".")+1),
              name:filename.substring(0,filename.lastIndexOf(".")),
              data:base64data.split(',')[1]
            });
            let anchor = document.createElement("a");
            anchor.download = filename;
            anchor.href = base64data;
            anchor.click();
          };
          reader.readAsDataURL(recoveredBlob);
        };

        xhr.open('GET', blobUrl);
        xhr.send();
        resolve(true);
      })
        .catch(err => {
          if (err.status === 404) {
            this.snackbar.openSnackBar(0, '', 'Hệ thống đã kiểm tra không tìm thấy hồ sơ nào để xuất dữ liệu file !', 'error_notification', this.config.expiredTime);
          }
          else if (err.status === 500) {
            this.snackbar.openSnackBar(0, '', 'Hệ thống tạm thời không thể xuất file excel!', 'error_notification', this.config.expiredTime);
          }
          resolve(false)
        });
    });
  }

getDossierExcelSync(data){
  const URL = this.padman + '/dossier'  +  '/--dossier-excel-sync';
  console.log(URL);

  let headers = new HttpHeaders();
  headers = headers.set('Content-Type', 'application/json');
  headers = headers.set('Accept-Language', localStorage.getItem('language'));
  return this.http.post<any>(URL, data, {headers, observe: 'response', responseType: 'blob' as 'json'}).toPromise();

}

  updateDossierFormFile(id, body): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.put(this.padman + `/dossier/${id}/dossier-form-file/--update`, body, { headers });
  }

  async clearDataTimezone (data) {
    return new Promise((resolve) => {
      const newData = { ...data };
      const listDateProperties = this.deploymentService.env.OS_HCM.listEformKeyClearTimezone;
      const clientDate = new Date();
      const timezoneOffsetMinutes = clientDate.getTimezoneOffset();
      const offsetHours = Math.abs(timezoneOffsetMinutes) / 60;
      const offsetSign = timezoneOffsetMinutes < 0 ? '+' : '-';
      const offsetString = `${offsetSign}${offsetHours.toString().padStart(2, '0')}:00`;
      listDateProperties.forEach(item => {
        if (!!data.applicant.data[item] && data.applicant.data[item] != '') {
          try {
            newData.applicant.data[item] = `${data.applicant.data[item].slice(0, 16)}${offsetString}`;
          } catch (error) {
            console.log('Clear timezone fail!');
          }
        }
      });
      resolve(newData);
    })
  }


  noticeDossier(id, body) {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.put(this.padman + `/dossier-btttt/${id}/notify`, body, { headers });
  }

  //// TIMESHEETV3
  getNextWorkingDate(body): Promise<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    if (this.envConfig?.timesheetV3) {
      return this.http.post(this.basecat + `/v3/timesheet-gen/--get-next-working-date`, body, {headers}).toPromise();
    } else {
      return this.http.post(this.basecat + `/timesheet-gen/--get-next-working-date`, body, {headers}).toPromise();
    }
  }

  //// TIMESHEETV3
  genDurationTimesheetV2(requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    if (this.envConfig?.timesheetV3) {
      return this.http.post<any>(this.basecat + '/v3/timesheet-gen/--gen-duration-by-dossier-id', requestBody, {headers});
    } else {
      return this.http.post<any>(this.basecat + '/timesheet-gen/--gen-duration-by-dossier-id-V2', requestBody, {headers});
    }
  }

  updateCheckHasFileTPHS(id, checkHasFileTPHS) {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.put(this.padman + `/dossier/${id}/--update-check-tphs/${checkHasFileTPHS}`, {},{ headers });
  }

  rejectWithdrawRequest(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.put<any>(this.dossierURL + `${id}/--reject-withdraw-request`, {}, { headers });
  }

  checkApologyByDossierCode(code:String): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.padman + `/apology/${code}/--check-having-apology-dossier`, { headers });
  }

  getSecondPrintedCheck(dossierCode): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.padman +`/dossier/${dossierCode}/--check-print-second-receive-receipt`, { headers });
  }

  setSecondPrintedCheck(dossierCode): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.post(this.padman +`/dossier/${dossierCode}/--set-printed-second-receive-receipt`, { headers });
  }

  handleRemainingTime(data): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.post(this.padman + '/qbh-remind-notify/--handle-remaining-time',data,{headers});
  }// tinh thoi gian con lai cua ho so khi thuc hien dung roi tiep nhan , ko đc sử dụng hàm này

  // handleTaskDue(data): Observable<any> {
  //   let headers = new HttpHeaders();
  //   headers = headers.set('Content-Type', 'application/json');
  //   headers = headers.set('Accept-Language', localStorage.getItem('language'));
  //   return this.http.post(this.padman + '/qbh-remind-notify/--handle-remain-due',data,{headers});
  // }// tinh thoi gian con lai cua task hien tai, ko su dung ham nay

  // hàm này lấy thời gian hẹn trả mới khi in phiếu tiếp nhận
  getNewAppointmentDate(id) :Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.padman + `/dossier/${id}/--get-new-appointment-date`,{headers});
  }

  setNewAppointmentDate(dossierCode,data): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.post(this.padman + `/dossier/--set-new-appointment-date/${dossierCode}`,data,{headers});
  }

  setFlagExtendTimeForAddDossier(dossierID,data): Observable<any> {
    let headers = new HttpHeaders();
    let params = new HttpParams();
    params = params.set("status",data["status"]);
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.put(this.padman + `/dossier/${dossierID}/--qbh-extend-time`,{},{headers,params});
  }

  showWarningDialog(message, dialogRef?) {
    const dataModel = new ErrorDialogModel(message["vi"],message["en"]);
    const instance = this.dialog.open(ErrorComponent,{
      width: '500px',
      data:dataModel
    });
    if (dialogRef)
      dialogRef.close();
    return instance;
  }
  // vui lòng ko dùng chức nang này có thể gây lỗi !
  checkOverDue(dossierDetail):boolean{
    const acceptedDate = dossierDetail?.acceptedDate ?? null;
    const appointmentDate = dossierDetail?.appointmentDate ?? null;
    const dueDate = dossierDetail?.dueDate ?? null;
    const newDate = tUtils.newDate();
    if (acceptedDate === null ) return false; // chua tiep nhan ho so
    if (dueDate !== null && newDate.getTime() > new Date(dueDate).getTime()) {
      return true;
    }
    else if (dueDate !== null && newDate.getTime() < new Date(dueDate).getTime()) {
      return false;
    }
    if (appointmentDate !== null && newDate.getTime() > new Date(appointmentDate).getTime()) {
      return true
    }
    return false;
  }

  resetParamAddDossier(dossierId) :Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.put(this.padman + `/dossier/${dossierId}/--reset-print-second-receipt`,{headers});
  }

  updateAcceptedDate(dossierId, body): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.put(this.padman + `/dossier/${dossierId}/--accepted-date`,body,{headers});
  }


  updateReceivingMethod(dossierId, tagId) :Observable<any>{
    let params = new HttpParams();
    params = params.set("tagId",tagId);
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.put(this.padman + `/dossier/${dossierId}/--update-receiving-method`,{},{headers,params});
  }
  getStaticLogDLDC(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.padman + '/qbh-log-statistic/--getlogseach'+ searchString, { headers });
  }
  getDetailStaticLogDLDC(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.padman + '/qbh-log-statistic/--detaillog'+ searchString, { headers });
  }
  // changePTAtStepReRecevingAddDossier(dossierId) {
  //   let headers = new HttpHeaders();
  //   headers = headers.set('Content-Type', 'application/json');
  //   headers = headers.set('Accept-Language', localStorage.getItem('language'));
  //   return this.http.put(this.padman + `/dossier/${dossierId}/--change-processing-re-receiving-add-dossier`,{},{headers});
  // }
  getProcedureDetail(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.basepad + '/procedure/' + id, { headers }).pipe();
  }

  updateDossierFromExcel(formData): Observable<any> {
    let headers = new HttpHeaders();
    // headers = headers.set('Content-Type', 'multipart/form-data');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.post<any>(this.dossierURL + '--update-attachment-from-file', formData, { headers });
  }

  uploadDossierAttachment(formData): Observable<any> {
    let headers = new HttpHeaders();
    // headers = headers.set('Content-Type', 'multipart/form-data');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.post<any>(this.dossierURL + '--upload-attachment-files', formData, { headers });
  }

  getListQBHDanhGiaLog(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.qbhDanhGiaLog + searchString, { headers });
  }
  create(body): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.set('Content-Type', 'application/json');
    return this.http.post(this.qbhDanhGiaLog,body,{headers});
  }  putFileILisDossierOnline(dossierId, requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.put<any>(this.dossierURL + dossierId + '/--update-file-ilis-online', requestBody, { headers });
    // return this.http.put<any>('http://localhost:8081/' + dossierId + '/--online', requestBody, { headers });
  }

  putResultFileDossierOnline(dossierId, requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.put<any>(this.dossierURL + dossierId + '/--update-result-file-attachment-cto', requestBody, { headers });
  }
  
  getNextTaskConfigPipe(id): Observable<any> {
    const URL = `${this.padman}/v2/dossiers/${id}/--next-task-config`;
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(URL, { headers }).pipe();
  }

  getNextTaskConfigPromise(id): Promise<any> {
    const URL = `${this.padman}/v2/dossiers/${id}/--next-task-config`;
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(URL, { headers }).toPromise();
  }

  createTask (body) {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.post<any>(this.dossierURL + '--create-task', body, { headers });
  }

  sendHTTPDLK(requestBody, maDonVi): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers.append('Access-Control-Allow-Origin', '*');
    let search = `?code=${requestBody.maHoSo}&maDonVi=${maDonVi}`;
    return this.http.post<any>(this.adapter + '/lgsp-dlk-http/--send-dang-ky-ho-tich' + search, requestBody, { headers });
  }
  getCheckEnableHoTichDLK(processDefinitionId): Promise<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    let param = `?process-definition-id=${processDefinitionId ? processDefinitionId : ''}`;
    return this.http.get(this.dossierURL + '--isEnable-http-lgsp-dlk' + param, { headers }).toPromise();
  }

  reSendHoTichDLK(dossierId): Promise<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    let param = `?dossierId=${dossierId ? dossierId : ''}`;
    return this.http.get(this.dossierURL + '--check-resend-http-dlk' + param, { headers }).toPromise();
  }

  putUpdateHinhThucThanhToanTrucTiep(dossierId, paymentMethodId, paymentMethodName): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    let params = `?paymentMethodId=${paymentMethodId}&paymentMethodName=${paymentMethodName}`;
    
    return this.http.put<any>(this.dossierURL + dossierId + '/--cmu-update-hinh-thuc-thanh-toan-truc-tiep'+params, { headers });
    // return this.http.put<any>('http://localhost:8081/dossier/' + dossierId + '/--cmu-update-hinh-thuc-thanh-toan-truc-tiep'+params, { headers });
  }
  postDossierPayment(dataPost): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    return this.http.post<any>(this.padman + '/dossier-payment', dataPost, { headers });
  }
  postPaymentPlatformInit(requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    return this.http.post<any>(this.adapter + '/payment-platform/--init', requestBody, { headers });
  }
  postCreateVNPTPayInit(data, agencyId, subsystemId): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    // tslint:disable-next-line:max-line-length
    return this.http.post<any>(this.adapter + '/vnpt-pay/get-init' + '?agency-id=' + agencyId + '&subsystem-id=' + subsystemId, data, {headers});
  }
  getAcountPayment(agencyId, subsystemId, procedureProcessId): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    // tslint:disable-next-line: max-line-length
    return this.http
      .get(
        this.adapter +
          '/payment-platform/--get-beneficiary-account?agency-id=' +
          agencyId +
          '&subsystem-id=' +
          subsystemId +
          '&procedure-process-id=' +
          procedureProcessId,
        { headers }
      )
      .pipe();
  }

  getSynthesisReportFee(searchString){
    let headers = new HttpHeaders();
    const URL = `${this.padman}/charge-fees-dossier/synthesis-report`
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(URL + searchString, { headers });
  }

  getCollectReceipt(searchString){
    let headers = new HttpHeaders();
    const URL = `${this.padman}/charge-fees-dossier/collect-receipt`
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(URL + searchString, { headers });
  }

  getDetailedReport(searchString){
    let headers = new HttpHeaders();
    const URL = `${this.padman}/charge-fees-dossier/detailed-report`
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(URL + searchString, { headers });
  }

  getDetailedReportExcel(searchString){
    let headers = new HttpHeaders();
    const URL = `${this.padman}/charge-fees-dossier/detailed-report-excel`
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(URL + searchString, { headers });
  }

  exportToExcelStatisticHCM(params: string): any {
    return new Promise((resolve) => {
      this.http.get(this.padman + "/dossier/" + params, {
        observe: 'response',
        responseType: 'blob'
      }).toPromise().then(res => {
        const blob = new Blob([res.body], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'});
        let filename = 'bao_cao_chi_tiet.xlsx';
        if (res.headers.get('content-disposition') != null) {
          filename = res.headers.get('content-disposition').split(';')[1].split('=')[1].replace(/"/g, '');
        }
        const blobUrl = URL.createObjectURL(blob);
        const xhr = new XMLHttpRequest();
        xhr.responseType = 'blob';

        xhr.onload = () => {
          const recoveredBlob = xhr.response;
          const reader = new FileReader();
          reader.onload = () => {
            const base64data = reader.result.toString();
            const anchor = document.createElement('a');
            anchor.download = filename;
            anchor.href = base64data;
            anchor.click();
          };
          reader.readAsDataURL(recoveredBlob);
        };

        xhr.open('GET', blobUrl);
        xhr.send();
        resolve(true);
      }).catch(err => {
        if (err.status === 500) {
          const message = {
            vi: 'Hệ thống tạm thời không thể xuất file excel!',
            en: 'The system is temporarily unable to export the excel file!'
          };
          this.snackbar.openSnackBar(0, '', message[localStorage.getItem('language')], 'error_notification', this.config.expiredTime);
        }
        resolve(false);
      });
    });
  }
  confirmRefund(id,requestBody):Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.put(this.dossierURL + id + "/--confirm-refund" , requestBody,{ headers });
  }

  cloneDossier(dossierId, quantity):Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.dossierURL + `${dossierId}/--clone-dossier?quantity=${quantity}`, { headers });
  }

  syncDossierTaskStatus(id,requestBody):Observable<any>{
      return this.http.put(this.dossierURL + id + "/--sync-dossier-task-status",requestBody);
  }

  syncDossierDVCLT(code): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    return this.http.post<any>(this.padman + '/dvclt-dossier/sync?code=' + code,'', { headers });
  }

  getIgateV2StorageComponent(id): Observable<any> {
    const URL = `${this.storage}/igate/--components?system=igate2&dossier-id=${id}`;
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    return this.http.get(URL, { headers });
  }
  
  postDossierToQLVBDH_GLI(requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.post<any>(this.padman + '/qlvbdh/sendDocToQLVBDH', requestBody, { headers });
    // return this.http.post<any>('http://localhost:8081/qlvbdh/sendDocToQLVBDH', requestBody, { headers });
  }
  downloadAllFile(searchParams: any) {
      return new Promise((resolve) => {
        this.http.get(this.padman + '/dossier-form-file/--download-all-file', {
          params: searchParams,
          observe: 'response',
          responseType: 'blob'
        }).toPromise().then(res => {
          console.log('PHUONG', res);
          if (res.status === 204) {
            resolve(false);
            return this.snackbar.openSnackBar(0, '', 'Hồ sơ không có tệp đính kèm', 'error_notification', this.config.expiredTime);
          }
          const blob = new Blob([res.body], {type: 'application/zip, application/octet-stream, application/x-zip-compressed, multipart/x-zip'});
          let filename = searchParams.code;
          if (res.headers.get('content-disposition') != null) {
            filename = res.headers.get('content-disposition').split(';')[1].split('=')[1].replace(/"/g, '');
          }
          filename = filename + '.zip';
          const blobUrl = URL.createObjectURL(blob);
          const xhr = new XMLHttpRequest();
          xhr.responseType = 'blob';

          xhr.onload = () => {
            const recoveredBlob = xhr.response;
            const reader = new FileReader();
            reader.onload = () => {
              const base64data = reader.result.toString();
              const anchor = document.createElement('a');
              anchor.download = filename;
              anchor.href = base64data;
              anchor.click();
            };
            reader.readAsDataURL(recoveredBlob);
          };

          xhr.open('GET', blobUrl);
          xhr.send();
          resolve(true);
        }).catch(err => {
            const message = {
              vi: 'Hệ thống tạm thời không thể xuất file!',
              en: 'The system is temporarily unable to export the file!'
            };
            this.snackbar.openSnackBar(0, '', message[localStorage.getItem('language')], 'error_notification', this.config.expiredTime);
            resolve(false);
        });
      });
  }

  //KGG OS
  getDossierDetailKGG(id): Promise<any> {
    return new Promise((resolve, reject) => {
      const URL = `${this.dossierURL}${id}/--online`;
      let headers = new HttpHeaders();
      headers = headers.set('Accept-Language', localStorage.getItem('language'));
      this.http.get(URL, { headers }).subscribe(
        (response) => {
          resolve(response);
        },
        (error) => {
          reject(error);
        }
      );
    });
  }

  postRequestForAdditionalVnpostFee(dossierId): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.post<any>(this.padman + '/dossier-transport-fee/requestForAdditional?dossier-id=' + dossierId, { headers });
  }

  getReportDossierReceivedReport(search): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.padman + '/dossier-statistic/report-dossier-recieved-results' + search, { headers });
  }

  excelExportStatisticReportDossierReceived(params: string): any{
    return new Promise((resolve) => {
      this.http.get(this.padman + '/dossier-statistic/report-dossier-recieved-results/--export' + params, {
        observe: 'response',
        responseType: 'blob'
      }).toPromise().then(res => {
        const blob = new Blob([res.body], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'});
        let filename = 'Thong_ke_ho_so_tiep_nhan_vpc.xlsx';
        if (res.headers.get('content-disposition') != null) {
          filename = res.headers.get('content-disposition').split(';')[1].split('=')[1].replace(/"/g, '');
        }
        const blobUrl = URL.createObjectURL(blob);
        const xhr = new XMLHttpRequest();
        xhr.responseType = 'blob';

        xhr.onload = () => {
          const recoveredBlob = xhr.response;
          const reader = new FileReader();
          reader.onload = () => {
            const base64data = reader.result.toString();
            const anchor = document.createElement('a');
            anchor.download = filename;
            anchor.href = base64data;
            anchor.click();
          };
          reader.readAsDataURL(recoveredBlob);
        };

        xhr.open('GET', blobUrl);
        xhr.send();
        resolve(true);
      }).catch(err => {
        if (err.status === 500) {
          const message = {
            vi: 'Hệ thống tạm thời không thể xuất file excel!',
            en: 'The system is temporarily unable to export the excel file!'
          };
          this.snackbar.openSnackBar(0, '', message[localStorage.getItem('language')], 'error_notification', this.config.expiredTime);
        }
        resolve(false);
      });
    });
  }

  getStatisticNQ17(search): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.padman + '/dossier-statistic/nq17-vpc' + search, { headers });
  }

  excelExportStatisticNQ17(params: string): any{
    return new Promise((resolve) => {
      this.http.get(this.padman + '/dossier-statistic/nq17-vpc/--export' + params, {
        observe: 'response',
        responseType: 'blob'
      }).toPromise().then(res => {
        const blob = new Blob([res.body], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'});
        let filename = 'Thong_ke_ho_so_theo_mau_6g_nq17_.xlsx';
        if (res.headers.get('content-disposition') != null) {
          filename = res.headers.get('content-disposition').split(';')[1].split('=')[1].replace(/"/g, '');
        }
        const blobUrl = URL.createObjectURL(blob);
        const xhr = new XMLHttpRequest();
        xhr.responseType = 'blob';

        xhr.onload = () => {
          const recoveredBlob = xhr.response;
          const reader = new FileReader();
          reader.onload = () => {
            const base64data = reader.result.toString();
            const anchor = document.createElement('a');
            anchor.download = filename;
            anchor.href = base64data;
            anchor.click();
          };
          reader.readAsDataURL(recoveredBlob);
        };

        xhr.open('GET', blobUrl);
        xhr.send();
        resolve(true);
      }).catch(err => {
        if (err.status === 500) {
          const message = {
            vi: 'Hệ thống tạm thời không thể xuất file excel!',
            en: 'The system is temporarily unable to export the excel file!'
          };
          this.snackbar.openSnackBar(0, '', message[localStorage.getItem('language')], 'error_notification', this.config.expiredTime);
        }
        resolve(false);
      });
    });
  }

  postDossierQLVB(body): Observable<any> {
    let headers = new HttpHeaders()
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.post(this.padman + '/vpub/--sending-data', body, { headers }).pipe();
  }

  //Lấy Log LGSP HCM LLTPVNEID
  getLGSPHCMLLTPVNEIDLog(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    return this.http.get<any>(this.padman + '/v2/lyLichTuPhap/lgsp-hcm-lltp-vneid-log' + searchString, { headers });
  }

  async exportListLGSPLLTPVNEIDLog(searchString) {    
    let response = await this.http.get(this.padman + '/v2/lyLichTuPhap/--excel-lgsp-hcm-lltp-vneid-log' + searchString, { observe: 'response', responseType: 'blob' }).toPromise();
    const blob = response.body;
    saveAs(blob, 'Danh sach ho so lltp vneid.xlsx');
  }
  getDueDateResume(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get<any>(this.dossierURL + id + '/--due-date-resume', { headers });
  }
  putDossierRejectStatusWithComment(id, requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.put<any>(this.dossierURL + id + '/reject/status', requestBody, { headers });
  }
  checkOnlinePayment(dossierId): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.padman + '/dossier-payment/--check-online-payment-hgi?dossier-id=' + dossierId, { headers });
  }

  updateCountReceptionAdditionalDossier(dossierId): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.put<any>(this.dossierURL + dossierId + '/--update-count-reception-additional-dossier', {headers});
  }

  getListDossierDueHGI(searchString): Observable<any> {
    const URL = this.padman + '/procedure-list-hgi/due' + searchString;
    // const URL = 'http://localhost:8081/procedure-list-hgi/due' + searchString;
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get( URL, { headers });
  }

  downloadExportHGI(params: string){
    return this.http.get(this.padman + '/procedure-list-hgi/--export' + params, { observe: 'response', responseType: 'blob' }).toPromise();
    // return this.http.get('http://localhost:8081/procedure-list-hgi/--export' + params, { observe: 'response', responseType: 'blob' }).toPromise();
  }
  excelExportHGI(params: string): any {
    return new Promise((resolve) => {
      this.downloadExportHGI(params).then(res => {
        const blob = new Blob([res.body], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
        let filename = 'mau_ho_so_den_han.xlsx';
        if (res.headers.get('content-disposition') != null) {
          filename = res.headers.get('content-disposition').split(';')[1].split('=')[1].replace(/\"/g, '');
        }
        var blobUrl = URL.createObjectURL(blob);

        var xhr = new XMLHttpRequest;
        xhr.responseType = 'blob';

        xhr.onload = function() {
          var recoveredBlob = xhr.response;
          var reader = new FileReader;
          reader.onload = function() {
            var base64data = reader.result.toString();
            var object = JSON.stringify({
              mimeType:filename.substring(filename.lastIndexOf(".")+1),
              name:filename.substring(0,filename.lastIndexOf(".")),
              data:base64data.split(',')[1]
            });
            let anchor = document.createElement("a");
            anchor.download = filename;
            anchor.href = base64data;
            anchor.click();
          };
          reader.readAsDataURL(recoveredBlob);
        };

        xhr.open('GET', blobUrl);
        xhr.send();
        resolve(true);
      })
        .catch(err => {
          if (err.status === 500) {
            this.snackbar.openSnackBar(0, '', 'Hệ thống tạm thời không thể xuất file excel!', 'error_notification', this.config.expiredTime);
          }
          resolve(false)
        });
    });
  }

  getListDossierDueAllHGI(searchString): Observable<any> {
     const URL = this.padman + '/procedure-list-hgi/due-all' + searchString;
    // const URL ='http://localhost:8081/procedure-list-hgi/due-all' + searchString;
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get( URL, { headers });
  }

  putDossierrequestAdditionalWithComment(id, requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.put<any>(this.dossierURL + id + '/request-additional', requestBody, { headers });
  }
  addRatingHGI(id): Observable<any> {
    let headers = new HttpHeaders()
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.post(this.dossierHgiURL + id+ '/--add-rating-hgi', { headers }).pipe();
  }
  getLatestUnratedDossier(userId): Observable<any> {
    const URL = `${this.dossierHgiURL}${userId}/--rating-lastest-hgi`;
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(URL, { headers });
  }

  rateService(dossierId, selectedRating): Observable<any> {
    let headers = new HttpHeaders()
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');
    let params = new HttpParams().set('selectedRating', selectedRating.toString());
    return this.http.put<any>(this.dossierHgiURL + dossierId+ '/--rating-dossier-hgi',{}, { headers,params});
  }

  updateViettelPost(requestBody) {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.post<any>(this.padman + '/viettelpost/--update', requestBody, { headers });
  }

  putAnsweredDossier(dossierId, consultationId): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    // return this.http.put<any>('http://localhost:8081/dossier/--update-answered-dossier?dossier-id=' + dossierId + '&consultation-id=' + consultationId, { headers });
    return this.http.put<any>(this.dossierURL + '--update-answered-dossier?dossier-id=' + dossierId + '&consultation-id=' + consultationId, { headers });
  }

    excelExportDataTemplate(params: string): any {
        return new Promise((resolve) => {
            this.downloadExportDataTemplate(params).then(res => {
                const blob = new Blob([res.body], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
                let filename = res.headers.get('content-disposition').split(';')[1].split('=')[1].replace(/\"/g, '');
                var blobUrl = URL.createObjectURL(blob);

                var xhr = new XMLHttpRequest;
                xhr.responseType = 'blob';

                xhr.onload = function() {
                    var recoveredBlob = xhr.response;
                    var reader = new FileReader;
                    reader.onload = function() {
                        var base64data = reader.result.toString();
                        var object = JSON.stringify({
                            mimeType:filename.substring(filename.lastIndexOf(".")+1),
                            name:filename.substring(0,filename.lastIndexOf(".")),
                            data:base64data.split(',')[1]
                        });
                        let anchor = document.createElement("a");
                        anchor.download = filename;
                        anchor.href = base64data;
                        anchor.click();
                    };
                    reader.readAsDataURL(recoveredBlob);
                };

                xhr.open('GET', blobUrl);
                xhr.send();
                resolve(true);
            })
                .catch(err => {
                    if (err.status === 500) {
                        this.snackbar.openSnackBar(0, '', 'Hệ thống tạm thời không thể xuất file excel!', 'error_notification', this.config.expiredTime);
                    }
                    resolve(false);
                });
        });
    }

    downloadExportDataTemplate(params){
        const url = '--export-dossier-to-convert?';
        return this.http.get(this.dossierURL + url + params, { observe: 'response', responseType: 'blob' }).toPromise();
    }

    postDossierInSystemFromFile(data: any): Observable<any> {
        const formData: FormData = new FormData();
        formData.append('file', data.file);
        return this.http.post<any>(this.dossierURL + '--from-file-to-convert', formData);
    }

    updateRequiredFee(dossierId): Observable<any> {
      let headers = new HttpHeaders();
      headers = headers.set('Content-Type', 'application/json');
      headers = headers.set('Accept-Language', localStorage.getItem('language'));
      return this.http.put<any>(this.padman + '/dossier-fee/--update-required-fee?dossier-id=' + dossierId, { headers });
    }
    
  putDossierReason(requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    return this.http.put<any>(this.dossierURLCTO + '--update-reason', requestBody, { headers });
  }   

  putReceiptBook(dossierId, requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    return this.http.put<any>(this.dossierURLCTO + `${dossierId}/--receipt-book`, requestBody, { headers });
  }  
  getAdministrativeResolutionResult(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');
        return this.http.get(this.padman + '/dossier-kha/--bc-tuan' + searchString, { headers }).pipe();
  }
  exportAdministrativeResolutionResult(params: string): any {
    return new Promise((resolve) => {
         this.http.get(this.padman + '/dossier-kha/--export-bc-tuan' + params, {
            observe: 'response',
            responseType: 'blob'
        }).toPromise().then(res => {
            const blob = new Blob([res.body], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'});
            let filename = 'Bao_cao_kq_giai_quyet_tthc_.xlsx';
            if (res.headers.get('content-disposition') != null) {
                filename = res.headers.get('content-disposition').split(';')[1].split('=')[1].replace(/"/g, '');
            }
            const blobUrl = URL.createObjectURL(blob);
            const xhr = new XMLHttpRequest();
            xhr.responseType = 'blob';
  
            xhr.onload = () => {
                const recoveredBlob = xhr.response;
                const reader = new FileReader();
                reader.onload = () => {
                    const base64data = reader.result.toString();
                    const anchor = document.createElement('a');
                    anchor.download = filename;
                    anchor.href = base64data;
                    anchor.click();
                };
  
                reader.readAsDataURL(recoveredBlob);
            };
  
            xhr.open('GET', blobUrl);
            xhr.send();
            resolve(true);
        }).catch(err => {
            if (err.status === 500) {
                const message = {
                    vi: 'Hệ thống tạm thời không thể xuất file excel!',
                    en: 'The system is temporarily unable to export the excel file!'
                };
                this.snackbar.openSnackBar(0, '', message[localStorage.getItem('language')], 'error_notification', this.config.expiredTime);
            }
            resolve(false);
        });
    });
  }
  getDossierBCTuanDetail(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.get(this.padman + '/dossier-kha/--bc-tuan-detail' + searchString, { headers }).pipe();
  }
  exportDossierBCTuanDetail(params: string): any {
    return new Promise((resolve) => {
         this.http.get(this.padman + '/dossier-kha/--export-bc-tuan--detail' + params, {
            observe: 'response',
            responseType: 'blob'
        }).toPromise().then(res => {
            const blob = new Blob([res.body], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'});
            let filename = 'Bao_cao_kq_giai_quyet_tthc_chi_tiet.xlsx';
            if (res.headers.get('content-disposition') != null) {
                filename = res.headers.get('content-disposition').split(';')[1].split('=')[1].replace(/"/g, '');
            }
            const blobUrl = URL.createObjectURL(blob);
            const xhr = new XMLHttpRequest();
            xhr.responseType = 'blob';
  
            xhr.onload = () => {
                const recoveredBlob = xhr.response;
                const reader = new FileReader();
                reader.onload = () => {
                    const base64data = reader.result.toString();
                    const anchor = document.createElement('a');
                    anchor.download = filename;
                    anchor.href = base64data;
                    anchor.click();
                };
                reader.readAsDataURL(recoveredBlob);
            };
  
            xhr.open('GET', blobUrl);
            xhr.send();
            resolve(true);
        }).catch(err => {
            if (err.status === 500) {
                const message = {
                    vi: 'Hệ thống tạm thời không thể xuất file excel!',
                    en: 'The system is temporarily unable to export the excel file!'
                };
                this.snackbar.openSnackBar(0, '', message[localStorage.getItem('language')], 'error_notification', this.config.expiredTime);
            }
            resolve(false);
        });
    });
  }
}
