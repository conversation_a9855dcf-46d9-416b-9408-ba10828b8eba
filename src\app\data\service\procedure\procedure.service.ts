import { Inject, Injectable, LOCALE_ID } from '@angular/core';
import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { ApiProviderService } from 'src/app/core/service/api-provider.service';
import { Observable } from 'rxjs';
import { formatDate } from '@angular/common';
import { EnvService } from 'src/app/core/service/env.service';
import { DeploymentService } from '../deployment.service';
import { SnackbarService } from '../snackbar/snackbar.service';

@Injectable({
  providedIn: 'root'
})
export class ProcedureService {

  constructor(
    private http: HttpClient,
    private apiProviderService: ApiProviderService,
    private envservice: EnvService,
    private snackbarService: SnackbarService,
    @Inject(LOCALE_ID) protected localeId: string,
    private deploymentService: DeploymentService
  ) { }
  config = this.envservice.getConfig();
  private basepad = this.apiProviderService.getUrl('digo', 'basepad');
  private basedataURL = this.apiProviderService.getUrl('digo', 'basedata');
  // private basepad = "http://localhost:8069";
  private padman = this.apiProviderService.getUrl('digo', 'padman');
  private storage = this.apiProviderService.getUrl('digo', 'storage');
  private procedurePath = this.apiProviderService.getUrl('digo', 'basepad') + '/procedure/';
  private procedurePathFull = this.apiProviderService.getUrl('digo', 'basepad') + '/procedure/--find-by-public-admin';
  private procedurePathV2 = this.apiProviderService.getUrl('digo', 'basepad') + '/v2/procedures/';
  private agencyPath = this.apiProviderService.getUrl('digo', 'basedata') + '/agency/';
  private agencyPathQni = this.apiProviderService.getUrl('digo', 'basedata') + '/qni-agency/';
  private agencyPathTGG = this.apiProviderService.getUrl('digo', 'basedata') + '/agency/--child+parent/';
  private sectorPath = this.apiProviderService.getUrl('digo', 'basepad') + '/sector/';
  private tagPath = this.apiProviderService.getUrl('digo', 'basecat') + '/tag/';
  private procostPath = this.apiProviderService.getUrl('digo', 'basepad') + '/procost/';
  private procostTypePath = this.apiProviderService.getUrl('digo', 'basepad') + '/procost-type/';
  private procedureFormPath = this.apiProviderService.getUrl('digo', 'basepad') + '/procedure-form/';
  private procedureFormEformPath = this.apiProviderService.getUrl('digo', 'basepad') + '/procedure-form-eform/';
  private formPath = this.apiProviderService.getUrl('digo', 'basepad') + '/form/';
  private filePath = this.apiProviderService.getUrl('digo', 'fileman') + '/file/';
  private processPath = this.apiProviderService.getUrl('digo', 'basepad') + '/procedure-process-definition/';
  private modelingPath = this.apiProviderService.getUrl('digo', 'modeling') + '/v1/models/';
  private actModelingContentAPI = this.apiProviderService.getUrl('digo', 'bpm') + '/process-definition/models/';
  // private actModelingContentAPI = 'http://localhost:8080/process-definition/models/';
  private bpmProcessPath = this.apiProviderService.getUrl('digo', 'bpm') + '/process-definition/';
  private bpm = this.apiProviderService.getUrl('digo', 'bpm');
  private adapter = this.apiProviderService.getUrl('digo', 'adapter');
  private human = this.apiProviderService.getUrl('digo', 'human');
  private iofficePath = 'https://camau-api.vnptioffice.vn';
  private formio = this.config.formioURL;
  private size = 200;
  private exportUrl = this.apiProviderService.getUrl('digo', 'basepad') + '/procedure/--export-tracking?';
  private procedureSpecific = this.apiProviderService.getUrl('digo', 'basepad')+'/qbh-procedure-specific/';
  private oneLevelHCCAssignment = this.apiProviderService.getUrl('digo', 'basepad') + '/one-level-hcc-assignment/';
  private getUpdateNotiConfigPath(id: string){
    return this.apiProviderService.getUrl('digo', 'basepad') + `/procedure-process-definition/${id}/--notification-config`;
  }

  getListUserWithAgencyId(searchString): Observable<any> {
    return this.http.get(this.human + '/user/fullname+experience/' + searchString);
  }

  getListAgency(keyword: string, page: number, size: number, spec: string): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.agencyPath + 'name/?keyword=' + keyword + '&page=' + page + '&size=' + size + '&spec=' + spec + '&sort=name,asc', { headers });
  }

  getListAgencyWithParent(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.agencyPath + searchString, { headers });
  }
  getAllProcedureWithAgencyDLK(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.basepad + '/procedure/--find-all-procedure-qni'+ searchString, { headers }).pipe();
  }
  getListAgencyWithParentTGG(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.agencyPathTGG + searchString, { headers });
  }

  getAgencyParent(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.get(this.basedataURL + '/agency/name+logo-id' + searchString, { headers }).pipe();
  }

  getListFullAgency(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.agencyPath + searchString, { headers });
  }

  getListProcedure(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.procedurePath + searchString, { headers });
  }

  getListSearchProcedure(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.procedurePath + `--search${searchString}`, { headers });
  }
  getListSearchProcedureCTO(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    console.log("Search procedure cto: " + this.procedurePath + `--search-cto${searchString}`)
    return this.http.get(this.procedurePath + `--search-cto${searchString}`, { headers });
  }

  getListSearchAssignProcedure(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.procedurePath + `--search-assign${searchString}`, { headers });
  }

  getListProcedureProcessDefinition(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.basepad + '/procedure-process-definition/' + searchString, { headers });
  }

  getListProcedureProcessDefinitionFollowSector(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.basepad + '/procedure-process-definition' + searchString, { headers });
  }

  getProcedureProcessDefinitionDetail(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.basepad + '/procedure-process-definition/' + id, { headers });
  }
  getAgencyDetail(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.agencyPath + id, { headers });
  }
  getProcedureDetail(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    //return this.http.get("http://localhost:8091/procedure/" + id, { headers });
    return this.http.get(this.procedurePath + id, { headers });
  }

  getDetailsProcedures(ids: string[]): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');
    // Chuẩn bị query params từ danh sách ids
    //let params = new HttpParams();
    //params = params.append('ids', ids.toString());  // ObjectId trong API thường là chuỗi
    //return this.http.get("http://localhost:8091/procedure/getDetailsProcedures?ids=" + ids.toString(), { headers });
    return this.http.get(this.procedurePath + '/getDetailsProcedures?ids=' + ids.toString(), { headers });
  }
  
  getProcedureDetailPublic(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.procedurePath + id+'/--full', { headers });
  }

  getListSector(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    // tslint:disable-next-line: max-line-length
    return this.http.get(this.sectorPath + searchString, { headers }).pipe();
  }

  getListSectorByAgency(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    // tslint:disable-next-line: max-line-length
    return this.http.get(this.sectorPath + '--by-agency-with-page' + searchString, { headers }).pipe();
  }

  getListSectorFromProcedureAssigneId(searchString): Observable<any> {
    const URL = `${this.processPath}/--list-sector-by-procedure-assigneid/${searchString}`;
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(URL, { headers }).pipe();
  }

  getListTagByCategoryId(id: string, page: number, size: number, sort: string, keyword?: string): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    keyword = !!keyword ? keyword : '';
    // tslint:disable-next-line: max-line-length
    return this.http.get(this.tagPath + '--by-category-id?category-id=' + id + '&page=' + page + '&size=' + size + '&sort=' + sort + '&status=1' + '&keyword=' + keyword , { headers }).pipe();
  }

  getDetailTag(id: string): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    // tslint:disable-next-line: max-line-length
    return this.http.get(this.tagPath + id, { headers }).pipe();
  }

  postProcedure(requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    //return this.http.post<any>("http://localhost:8091/procedure", requestBody, { headers });
    return this.http.post<any>(this.procedurePath, requestBody, { headers });
  }

  putProcedure(id, requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.put<any>(this.procedurePath + id, requestBody, { headers });
  }

  putProcedureParam(id, requestBody, param): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    //return this.http.put<any>("http://localhost:8091/procedure/" + id + param, requestBody, { headers });
    return this.http.put<any>(this.procedurePath + id + param, requestBody, { headers });
  }

  putProcedureProDef(requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.put<any>(this.processPath + "update-procedure", requestBody, { headers });
  }

  changeAssignee(id, requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.put<any>(this.procedurePath + id + '/--change-assignee', requestBody, { headers });
  }

  deleteProcedure(id: string): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.append('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.delete(this.procedurePath + id, { headers });
  }

  getProcedureProcost(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.procostPath + searchString, { headers });
  }

  getListProcostType(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    // tslint:disable-next-line: max-line-length
    return this.http.get(this.procostTypePath + searchString, { headers }).pipe();
  }

  postProcost(requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.post<any>(this.procostPath, requestBody, { headers });
  }

  postProcostParam(requestBody, param): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.post<any>(this.procostPath + param, requestBody, { headers });
  }

  getProcostDetail(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.procostPath + id, { headers });
  }

  putProcost(id, requestBody, param): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.put<any>(this.procostPath + id + param, requestBody, { headers });
  }

  deleteProcost(id: string, param) {
    let headers = new HttpHeaders();
    headers = headers.append('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.delete(this.procostPath + id + param, { headers });
  }

  putAssignedAgency(id, requestBody, option): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.put<any>(this.procedurePath + id + '/children' + option, requestBody, { headers });
  }

  getProcedureForm(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.procedureFormPath + searchString, { headers });
  }

  getProcedureFormDetail(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.procedureFormPath + id, { headers });
  }


  getListForm(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.formPath + searchString, { headers });
  }

  getListFormPromise(searchString): Promise<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.formPath + searchString, { headers }).toPromise();
  }

  postNewForm(requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.post<any>(this.formPath, requestBody, { headers });
  }
  uploadFile(data: any): Observable<any> {
    const formData: FormData = new FormData();
    formData.append('file', data);
    return this.http.post<any>(this.filePath, formData);
  }
  uploadBase64File(data: any): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.append('Accept', 'application/json');
    const formData: FormData = new FormData();
    formData.append('files', data.base64File, data.base64File.name);
    formData.append('account-id', data.accountId);
    //formData.append('fileName', data.fileName);
    //formData.append('dossier-id', data.dossierId);
    return this.http.post<any>(this.filePath + '--multiple', formData, { headers });
    //return this.http.post<any>("http://localhost:8080/file/" + '--upload-Base64', formData);
  }
  uploadFiles(imgFile): Observable<any> {
    const formData: FormData = new FormData();
    const file: File = imgFile;
    formData.append('file', file, file.name);
    return this.http.post(this.filePath, formData).pipe();
  }

  uploadMultiFile(imgFiles, accountId): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.append('Accept', 'application/json');
    const formData: FormData = new FormData();
    imgFiles.forEach(files => {
      const file: File = files;
      formData.append('files', file, file.name);
    });
    formData.append('account-id', accountId);
    formData.append('uuid', "1");
    return this.http.post<any>(this.filePath + '--multiple', formData, { headers });
  }

  uploadMultiFileKGG(imgFiles, accountId): Promise<any> {
    let headers = new HttpHeaders();
    headers = headers.append('Accept', 'application/json');
    const formData: FormData = new FormData();
    imgFiles.forEach(files => {
      const file: File = files;
      formData.append('files', file, file.name);
    });
    formData.append('account-id', accountId);
    formData.append('uuid', "1");
    return this.http.post<any>(this.filePath + '--multiple', formData, { headers }).toPromise();
  }
  uploadMultiFileWithUUID(imgFiles, accountId, imageToPdf=false): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.append('Accept', 'application/json');
    const formData: FormData = new FormData();
    // imgFiles.forEach(files => {
    //   const file: File = files;
    //   formData.append('files', file, file.name);
    // });
    // tslint:disable-next-line:prefer-for-of
    for (let i = 0; i < imgFiles.length; i++){
      const file: File = imgFiles[i];
      formData.append('files', file, file.name);
    }
    formData.append('account-id', accountId);
    formData.append('image-to-pdf', imageToPdf.toString());
    return this.http.post<any>(this.filePath + '--multiple?uuid=1', formData, { headers });
  }

  uploadMultiFilePromise(imgFiles, accountId): Promise<any> {
    let headers = new HttpHeaders();
    headers = headers.append('Accept', 'application/json');
    const formData: FormData = new FormData();
    imgFiles.forEach(files => {
      const file: File = files;
      formData.append('files', file, file.name);
    });
    formData.append('account-id', accountId);
    return this.http.post<any>(this.filePath + '--multiple', formData, { headers }).toPromise();
  }

  getFileNameSize(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.filePath + id + '/filename+size', { headers });
  }

  postProcedureForm(requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.post<any>(this.procedureFormPath, requestBody, { headers });
  }

  getTemplateProcedureBTTTT(requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.post<any>(this.sectorPath+"export/BTTTT/--all", requestBody, { headers });
  }

  putProcedureForm(id, requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.put<any>(this.procedureFormPath + id, requestBody, { headers });
  }

  downloadFile(id, idCheck?): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    // idCheck co the la dossierID hoacj procedureId
    if(!!idCheck){
      return this.http.get(this.filePath + id + '?dossier-id=' + idCheck, { responseType: 'blob' as 'json' }).pipe();
    }else{
      return this.http.get(this.filePath + id, { responseType: 'blob' as 'json' }).pipe();
    }
    
  }

  downloadAllFile(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.filePath + "/" + id + "/--download-all", { responseType: 'blob' as 'json' }).pipe();
  }
  
  async downloadFilePrintPaper(id, uuid) {
    let url = this.filePath + id + (uuid ? "?uuid=" + uuid : "");
    const existingPdfBytes = await fetch(url,{
      headers: {
        'Authorization': 'bearer ' + localStorage.getItem('userToken')
      }
    }).then(res => res.arrayBuffer());
    return existingPdfBytes;
  }
  
  deleteFile(id) {
    return this.http.delete<any>(this.filePath + id).pipe();
  }

  deleteForm(id: string) {
    let headers = new HttpHeaders();
    headers = headers.append('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.delete(this.procedureFormPath + id, { headers });
  }

  postProcedureFormParam(requestBody, param): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.post<any>(this.procedureFormPath + param, requestBody, { headers });
  }

  putProcedureFormParam(id, requestBody, param): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.put<any>(this.procedureFormPath + id + param, requestBody, { headers });
  }

  deleteFormParam(id: string, param) {
    let headers = new HttpHeaders();
    headers = headers.append('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.delete(this.procedureFormPath + id + param, { headers });
  }

  getListProcedureProcess(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.processPath + searchString, { headers });
  }

  getModelDeploy(modelId) {
    const headers = new HttpHeaders().set('Accept', 'application/json');
    return this.http.get(this.actModelingContentAPI + modelId + '/content', { headers, responseType: 'blob' });
  }

  getUrlModel(modelId) {
    return this.actModelingContentAPI + modelId + '/content';
  }

  deleteProcess(id: string) {
    let headers = new HttpHeaders();
    headers = headers.append('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.delete(this.processPath + id, { headers });
  }

  getProcedureProcessDetail(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.processPath + id, { headers });
  }

  setNotificationConfig(id, body): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.put<any>(this.getUpdateNotiConfigPath(id), body, { headers });
  }

  putProcedureProcessDetail(id, requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.put<any>(this.processPath + id, requestBody, { headers });
  }

  getListBPMProcess(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.bpmProcessPath + searchString, { headers });
  }

  getBPMProcessDefinitionTask(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.bpm + '/process-definition-task/' + searchString, { headers });
  }

  postProcedureProcessDefinition(requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.post<any>(this.processPath, requestBody, { headers });
  }

  addProcess(procedureId, requestBody, listDisableOnline): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.post<any>(this.procedurePathV2 + `${procedureId}/--process?disable-online-ids=` + listDisableOnline, requestBody, { headers });
  }

  getSyncProcedure(agencyId, agencyCode, subsystemId): Observable<any> {
    return this.http.get(this.adapter + '/npadsvc/--procedures?agency-id=' + agencyId +
      '&subsystem-id=' + subsystemId + '&agency-code=' + agencyCode).pipe();
  }

  //Không lấy config theo subsystemId và angencyId
  getSyncProcedureLGSPHCM(agencyId, agencyCode, subsystemId): Observable<any> {
    return this.http.get(this.adapter + '/lgsp-hcm-procedures/--procedures?agency-code=' + agencyCode
      ).pipe();
  }

  getChangedProceduresLGSPHCM(agencyCode, fromDate, toDate): Observable<any> {
    return this.http.get(this.adapter + '/lgsp-hcm-procedures/--changed-procedures?agency-code=' + agencyCode + '&fromDate=' + fromDate + '&toDate=' + toDate
      ).pipe();
  }

  getSyncProcedureDetailLGSPHCM(agencyId, agencyCode, subsystemId, code): Observable<any> {
    return this.http.get(this.adapter + '/lgsp-hcm-procedures/--procedure-detail?agency-code=' + agencyCode + '&code=' + code).pipe();
  }

  putSyncAllProcedureLGSPHCM(body): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    const url = this.basepad + '/procedure/--sync-lgsp-hcm-all';
    return this.http.put<any>(url, body, { headers });
  }

  putSyncSingleProcedureLGSPHCM(body): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    const url = this.basepad + '/procedure/--sync-lgsp-hcm-by-code';
    return this.http.put<any>(url, body, { headers });
  }

  getSyncProcedureDetail(agencyId, agencyCode, subsystemId, code): Observable<any> {
    return this.http.get(this.adapter + '/npadsvc/--procedure-detail?agency-id=' + agencyId +
      '&subsystem-id=' + subsystemId + '&agency-code=' + agencyCode + '&code=' + code).pipe();
  }

  getProcostForOnline(id): Observable<any> {
    return this.http.get(this.basepad + '/procost/--for-online?procedure-id=' + id).pipe();
  }

  getListProcostByProcedureId(procedureId): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.basepad + '/procost?procedure-id=' + procedureId, { headers }).pipe();
  }

  getListConfigTemplate(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.basepad + '/config/get-template' + searchString, { headers });
  }

  getListConfigTemplateProcess(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));

    return this.http.get(this.basepad +'/config/get-template-process'+ searchString, { headers });
  }
  putSyncAllProcedure(body): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    const url = this.basepad + '/procedure/--sync-npad-all?envRequirement=' + body.envRequiredment;
    return this.http.put<any>(url, body, { headers });
  }

  putSyncSingleProcedure(body): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    const url = this.basepad + '/procedure/--sync-npad-by-code?envRequiredment=' + body.envRequiredment;
    return this.http.put<any>(url, body, { headers });
  }

  getSyncStatus(code): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    const url = this.basepad + '/sync-procedure-status/--get-by-code?code=' + code;
    return this.http.get<any>(url, { headers });
  }

  checkProcedureExist(searchString, requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.post<any>(this.basepad + '/procedure/--has-children' + searchString, requestBody, { headers });
  }

  getDetailAgencyFully(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.agencyPath + id + '/name+code+parent+ancestor/--fully', { headers }).pipe();
  }

  getDetailAgencyFull(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.agencyPath + id + '/--fully', { headers }).pipe();
  }

  postProcudureFromFile(data: any): Observable<any> {
    const formData: FormData = new FormData();
    formData.append('file', data.file);
    return this.http.post<any>(this.procedurePath + '--from-file', formData);
  }

  getSyncProcedureHue(agencyId, subsystemId): Observable<any> {
    return this.http.get(this.adapter + '/tthc-hue/--sync-procedure?agency-id=' + agencyId +
      '&subsystem-id=' + subsystemId).pipe();
  }

  getUpdateAgencyTreeDVC(justContainProvinceLevel?: boolean, syncAgencyTreeDVC = false): Observable<any> {
    let url = this.basepad + '/procedure/update-agency-sector-reporter';
    if (justContainProvinceLevel != null) {
      url = this.basepad + '/procedure/update-agency-sector-reporter?just-contain-province-level=' + justContainProvinceLevel;
    }
    if(syncAgencyTreeDVC == true){
      url = this.basepad + '/procedure/update-agency-sector-reporter-lan';
    }
    return this.http.get(url).pipe();
  }

  getUpdateAgencyTreeDVCByLevel(params): Observable<any> {
    return this.http.get(this.basepad + '/procedure/update-agency-sector-reporter-by-level' + params).pipe();
  }

  getUpdateDVCOnline(): Observable<any> {
    return this.http.put<any>(this.basepad + '/procedure/updateCountProcessProcedure', null);
  }

  getEnableHueSync(): boolean{
    const config = this.deploymentService.getAppDeployment();
    if (!!config?.env?.sync?.hue?.enable){
      return config?.env?.sync?.hue?.enable;
    } else {
      return false;
    }
  }

  getRefreshTokenIoffice(maDonViNhan): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    // tslint:disable-next-line: max-line-length
    return this.http.get(this.iofficePath + '/api/can-bo/refresh-token-by-ma-dinh-danh-dv?ma_dinh_danh_dv_nhan='  + maDonViNhan, { headers }).pipe();
  }

  getAccessTokenIoffice(refreshToken): Observable<any> {
	let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    // tslint:disable-next-line: max-line-length
    return this.http.get(this.iofficePath +'/api/can-bo/access-token?refresh_token='  + refreshToken,{headers}).pipe();
  }

  getListVanBanIoffice(accessToken, ma_dinh_danh_dv,so_ky_hieu,tu_ngay, den_ngay, page, size): Observable<any> {
	let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
	headers = headers.append('Authorization', 'Bearer ' + accessToken);
	const format = 'dd/MM/yyyy';
	const locate = 'vi-VN';
	console.log(tu_ngay);
	let tu_ngay_f = '';
	let den_ngay_f = '';
	if(tu_ngay != '' && tu_ngay != null ){
		tu_ngay_f = formatDate(tu_ngay, format, locate);
	}
	if(den_ngay != '' && den_ngay != null ){
		den_ngay_f = formatDate(den_ngay, format, locate);
	}

	const formData: FormData = new FormData();
	formData.append('page', page);
	formData.append('size', size);
	formData.append('so_ky_hieu', so_ky_hieu);
    formData.append('trich_yeu', '');
	formData.append('ten_co_quan_ban_hanh', '');
	formData.append('ma_dinh_danh_dv', ma_dinh_danh_dv);
	formData.append('nam', '0');
	formData.append('ma_ctcb_van_thu', '');
	formData.append('ma_don_vi_quan_tri', '');
	formData.append('tu_ngay', tu_ngay_f);
	formData.append('den_ngay', den_ngay_f);
    // tslint:disable-next-line: max-line-length
    return this.http.post(this.iofficePath +'/api/van-ban-di/danh-sach-vbdi-dph-lt-igate',formData,{headers}).pipe();
  }

  getFileVbIoffice(accessToken, maVanBanDi): Promise<any> {
	let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
	headers = headers.append('Authorization', 'Bearer ' + accessToken);
	const formData: FormData = new FormData();
	formData.append('chuoi_ma_vbdi', maVanBanDi);
    // tslint:disable-next-line: max-line-length
    return this.http.post(this.iofficePath +'/api/van-ban-di/danh-sach-file-lien-thong-igate',formData,{headers}).pipe().toPromise();
  }

  getListVanBanIofficeQni(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.agencyPathQni +'--ioffice-qni' + searchString, { headers });
  }
  getFilesVanBanIofficeQni(searchString): Promise<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.agencyPathQni +'--ioffice-qni-files' + searchString, { headers }, ).pipe().toPromise();
    }

    getListVanBanIofficeQbh(searchString): Observable<any> {
      let headers = new HttpHeaders();
      headers = headers.set('Accept-Language', localStorage.getItem('language'));
      return this.http.get(this.adapter +'/qbh-qlvb/--ioffice-qbh' + searchString, { headers });
    }
    getFilesVanBanIofficeQbh(searchString): Promise<any> {
      let headers = new HttpHeaders();
      headers = headers.set('Accept-Language', localStorage.getItem('language'));
      return this.http.get(this.adapter +'/qbh-qlvb/--ioffice-qbh-files' + searchString, { headers }, ).pipe().toPromise();
      }
    
  getProcedureFormEform(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.procedureFormEformPath + searchString, { headers });
  }

  getListEForm(tag, skip): Observable<any> {
    console.log('=========================================');
    console.log(this.formio);
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    // tslint:disable-next-line: max-line-length
    return this.http.get<any>(this.formio + '/form' + '?name__regex=' + this.config.regexEform + '&skip=' + skip + '&limit=' + this.size, { headers }).pipe();
  }

  getListEFormWithKeyWord(tag, skip, limit, keyword: string): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    let _keyword = '';
    if (keyword && keyword.length > 0){
      _keyword = `&title__regex=/${keyword}/i`;
    }
    return this.http.get<any>(this.formio + '/form' + '?name__regex=' + this.config.regexEform + '&skip=' + skip + '&limit=' + limit + _keyword, { headers }).pipe();
  }

  postProcedureFormEform(requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.post<any>(this.procedureFormEformPath, requestBody, { headers });
  }

  getAllProcedureFormEform(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.procedureFormEformPath + '--all/' + searchString, { headers });
  }

  putProcedureFormEform(id, requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.put<any>(this.procedureFormEformPath + id, requestBody, { headers });
  }

  getProcedureFormEformDetail(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.procedureFormEformPath + id, { headers });
  }

  deleteProcedureFormEform(id: string) {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.delete(this.procedureFormEformPath + id, { headers });
  }

  postProcedureFormEformParam(requestBody, param): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.post<any>(this.procedureFormEformPath + param, requestBody, { headers });
  }

  putProcedureFormEformParam(id, requestBody, param): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.put<any>(this.procedureFormEformPath + id + param, requestBody, { headers });
  }

  deleteProcedureFormEformParam(id: string, param) {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.delete(this.procedureFormEformPath + id + param, { headers });
  }

  getChangeFileFormEform(file, value, components, type, changeCheckbox = false): Observable<any> {
    let headers = new HttpHeaders();
    // headers = headers.append('Content-Type', 'application/json');
    const formData: FormData = new FormData();
    formData.append('file', file, 'file.docx');
    formData.append('value', value);
    formData.append('components', components);
    formData.append('type', type);
    formData.append('changeCheckbox', changeCheckbox.toString());
    const temp = 'http://localhost:8080';
    return this.http.post(this.basepad + '/procedure-form-eform/--changeFile', formData, { headers,  responseType: 'blob' as 'json' });
  }

  getExportFormEform(filename, value, type): Observable<any> {
    let headers = new HttpHeaders();
    const formData: FormData = new FormData();
    formData.append('filename', filename);
    formData.append('value', value);
    formData.append('type', type);
    return this.http.post(this.basepad + '/procedure-form-eform/--export-form', formData, { headers,  responseType: 'blob' as 'json' });
  }

  importProcedureFormFromExcel(file: File): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    let formData: FormData = new FormData();
    formData.append("file",file);
    return this.http.post(this.procedureFormPath + "--import-excel", formData, { headers });
  }

  downloadExport(params: string){
    return this.http.get(this.exportUrl + params, { observe: 'response', responseType: 'blob' }).toPromise();
  }

  excelExport(params: string): any {
    return new Promise((resolve) => {
      this.downloadExport(params).then(res => {
        const blob = new Blob([res.body], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
        let filename = "procedure.xlsx"
        if(!!res.headers.get('content-disposition')){
          filename = res.headers.get('content-disposition').split(';')[1].split('=')[1].replace(/\"/g, '');
        }
        var blobUrl = URL.createObjectURL(blob);

        var xhr = new XMLHttpRequest;
        xhr.responseType = 'blob';

        xhr.onload = function() {
          var recoveredBlob = xhr.response;
          var reader = new FileReader;
          reader.onload = function() {
            var base64data = reader.result.toString();
            var object = JSON.stringify({
              mimeType:filename.substring(filename.lastIndexOf(".")+1),
              name:filename.substring(0,filename.lastIndexOf(".")),
              data:base64data.split(',')[1]
            });
            let anchor = document.createElement("a");
            anchor.download = filename;
            anchor.href = base64data;
            anchor.click();
          };
          reader.readAsDataURL(recoveredBlob);
        };

        xhr.open('GET', blobUrl);
        xhr.send();
        resolve(true);
      })
      .catch(err => {
        if (err.status === 500) {
          this.snackbarService.openSnackBar(0,
            this.envservice.getTranslateNotificationLabel(this.localeId, 'error', 'exportProcedure'), '',
            'error_notification',
            this.config.expiredTime
          );
        }
        resolve(false)
      });
    });
  }

  calculateProcessingTime(procedureProcessDefinitionId: String): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get<any>(this.basepad + `/procedure-process-definition/${procedureProcessDefinitionId}/--calculate-processing-time`, { headers });
  }

  uploadFileToStorage(identityNumber,formOrginId, fileId, filename): Observable<any>{
    let URL = `${this.padman}/dossier-form-file/--upload-to-storage/${fileId}?identity-number=${identityNumber}&filename=${filename}`;
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.post(URL,{},{ headers });
  }

  saveFileToStorage(identityNumber,formOrginId, fileId):Observable<any>{
    let URL = `${this.storage}/form-orgin-data/${formOrginId}?identity-number=${identityNumber}`;
    let files = [fileId];
    let body = {files: files};
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.post(URL,body,{ headers });
  }

  setInOut(id,inout):Observable<any>{
    let URL = `${this.basepad}/procedure-form/${id}/--set-inout?inout=${inout}`;
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.put(URL,{},{ headers });
  }

  getProcedureProcessDefinitionSector(page, size, keyword, assigneeId,agencyId): Observable<any> {
    let URL = `${this.basepad}/procedure-process-definition/--sector`;
    URL += `?page=${page}`;
    URL += `&size=${size}`;
    URL += `&assignee-id=${assigneeId}`;
    URL += `&applied-agency-id=${agencyId}`;
    if(keyword)
      URL += `&keyword=${keyword}`;

    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(URL, { headers });
  }

  getSignHistories(fileId): Observable<any>{
    const URL = `${this.filePath}/${fileId}/--signs`;
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(URL, { headers });
  }

  getListLevelProcedureFail(): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.procedurePath + '--get-level-procedure-fail', { headers });
  }

  updateLevelProcedure(type, oldLevelId): Observable<any> {
    const param = `?type=${type}&old-level-id=${oldLevelId}`;
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.put<any>(this.procedurePath + '--update-level-procedure' + param, { headers });
  }

  updateFileProcedure(id, data): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.put<any>(this.procedurePath + id + '/--attached-files', data, { headers });
  }

  getDetailProcedureFull(code): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.procedurePath + 'code/--full?code=' + code, { headers });
  }

  getProcedureChangeSync(body): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.post(this.adapter + '/npadsvc/--procedure-change', body, { headers }).pipe();
  }

  getOutputForms(procedureId): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.procedurePath + `${procedureId}/--get-output-form`, { headers });
  }

  mapProcedureWithStorage(procedureId, listFormOriginId): Observable<any>{
    let URL = this.procedurePath + `${procedureId}/--map-with-storage?form-origin-ids=${listFormOriginId}`;
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.put(URL, {headers});
  }

  deleteFormMapWithStorage(procedureId, listFormOriginId): Observable<any>{
    let URL = this.procedurePath + `${procedureId}/--delete-mapping?form-origin-ids=${listFormOriginId}`;
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.
    delete(URL, {headers});
  }

  getParentAgencyForSector(agencyId): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.agencyPath + agencyId + '/--parent-id-for-sector', { headers }).pipe();
  }

  checkProcedureBoTNMT(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.processPath + "--check-procedure-BoTNMT" + searchString, { headers });
  }
  getProcedureGPLXFull(code): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.procedurePath + '--get-procedure-by-code-gplx?code-type-gplx=' + code, { headers });
  }
  // get config procedure
  getInfoProcedure(idProcedure):Observable<any>{
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    let URL = `${this.procedurePath}`;
    URL += idProcedure;
    return this.http.get(URL,{ headers });
  }

  getListProcedureFull(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.procedurePathFull + searchString, { headers });
  }

  checkExistCodeProcedureSpecific(data): Observable<any> {
    const token = localStorage.getItem('OAuth2TOKEN');
    let headers = new HttpHeaders();
    headers = headers.append('Authorization', 'OAuth 2.0' + token);
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.procedureSpecific + '--check-exist-code' + data, {headers} );
  }

  postProcedureSpecific(data): Observable<any> {
    return this.http.post<any>(this.procedureSpecific, data);
  }

  getListQBHProcedureSpecific(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.procedureSpecific + searchString, { headers });
  }

  updateProcedureSpecific(id, data): Observable<any> {
    return this.http.put<any>(this.procedureSpecific + id, data);
  }

  deleteProcedureSpecific(id, data): Observable<any> {
    return this.http.put<any>(this.procedureSpecific + id +'/--delete', data);
  }

  getDetailProcedureSpecific(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.procedureSpecific + id + '/--full', { headers });
  }

  getListSectorTTHC(searchString, body): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    // tslint:disable-next-line: max-line-length
    return this.http.post(this.sectorPath + searchString, body).pipe();
  }

  getSpecificProcedures(param): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    let params = new HttpParams();
    Object.keys(param).forEach(key => {
      params = params.set(key,param[key].toString())
    });
    return this.http.get(this.basepad + `/procedure/--get-specific-procedure-by-agency-id`, { headers,params});
  }

  postLog(requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.post<any>(this.basepad + '/action-logs', requestBody, { headers });
  }

  getListActonLogs(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.basepad + '/action-logs/' + searchString, { headers });
  }

  getListSearchProcedureQNM(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.procedurePath + `--search-qnm${searchString}`, { headers });
  }

  importDataLogSync(file: File, query): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    const formData: FormData = new FormData();
    formData.append('file',file);
    return this.http.post(this.adapter + '/integrated-event/get-list-log-form-file' + query, formData, { headers });
  }

  getListDataLogSync( listcode): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.post(this.adapter + '/integrated-event/get-log-by-list-code', listcode, { headers });
  }

    //Lấy log chi tiết cho tỉnh Đồng Nai
    getDetailedLogByCode(listcode): Observable<any> {
        let headers = new HttpHeaders();
        headers = headers.set('Accept-Language', localStorage.getItem('language'));
        //headers = headers.set('Content-Type', 'application/json');

        return this.http.post(this.adapter + '/integrated-event/get-detailed-log-by-code', listcode, { headers });
        //return this.http.post('http://127.0.0.1:8081/integrated-event/get-detailed-log-by-code', listcode, { headers });
    }

  putSyncProcedureProcessSector(status): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    let path = this.procedurePath +"--sync-procedure-sector?status="+ status;
    return this.http.put<any>(path, null);
  }
  getListAgencyWithParentAgg(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.basedataURL + searchString, { headers });
  }
 


  getCheckProcedureProcessTNMT(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.processPath + id+"/--check-isTNMT", { headers });
  }

  getProcessDefinition(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.bpmProcessPath + id, { headers });
  }

  getListOneLevelHCCAssignment(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.oneLevelHCCAssignment + searchString, { headers });
  }

  getOneLevelHCCAssignment(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.oneLevelHCCAssignment + id, { headers });
  }

  postLevelHCCAssignment(requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.post(this.oneLevelHCCAssignment, requestBody, { headers });
  }

  putOneLevelHCCAssignment(id, requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.put(this.oneLevelHCCAssignment + id, requestBody, { headers });
  }

  deleteOneLevelHCCAssignment(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.delete(this.oneLevelHCCAssignment + id, { headers });
  }

  getAgencyOneLevelHCCAssignmentByUserId(userId): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.oneLevelHCCAssignment + '--agency?user-id=' + userId, { headers });
  }

  getListProcedureOneLevelHCC(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.procedurePath + '--one-level-hcc' + searchString, { headers });
  }

  getListProcedureOneLevelHCCByCode(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.procedurePath + '--one-level-hcc-by-code' + searchString, { headers });
  }
  getEFormDetail(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get<any>(this.formio + '/form/' + id, { headers }).pipe();
  }
  getListFormLazyLoad(keyword, skip, size): Observable<any> {
    console.log('=========================================');
    console.log(this.size);
    console.log(size);
    let url = this.formio + '/form' + '?name__regex=';
    if(!!keyword){
      url += `${keyword}`;
    } else {
      url += this.config.regexEform;
    }
    console.log('skip---- ', skip);
    url = `${url}&skip=${skip}&limit=${size}`;
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    // tslint:disable-next-line: max-line-length
    return this.http.get<any>(url, { headers }).pipe();
  }
  getListEFormWithKeyWord2(skip, limit, keyword: string): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    let _keyword = '';
    if (!!keyword){
      _keyword = `&title__regex=/${keyword}/i`;
    }
    else {
      _keyword = `&title__regex=/${this.config.regexEform}/i`;
    }
    return this.http.get<any>(this.formio + '/form' + '?' + 'skip=' + skip + '&limit=' + limit + _keyword, { headers }).pipe();
  }

  getTokenIoffice(body): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.post(this.adapter + "/ktm/get-token-ioffice", body, { headers});
  }
  
  getListVanBanIofficeV5(body): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.post(this.adapter + "/ktm/get-vb-ioffice-v5", body, { headers });
  }

  getListFileIofficeV5(body): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.post(this.adapter + "/ktm/get-file-vb", body, { headers });
  }

  getFileIofficeV5(body): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.post(this.adapter + "/ktm/download-file-vb", body, { headers });
  }

  addProcessNew(procedureId, requestBody): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.post<any>(this.procedurePathV2 + `${procedureId}/--process-new`, requestBody, { headers });
  }

  closeAllProduceByAgency(tagId): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.put<any>(this.procedurePath + `close-all-procedure-by-agency?tag-id=${tagId}`,null, { headers });
  }
  getListProcedureHGI(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.procedurePath+'--hgi' + searchString, { headers });
  }


  inCorporateAgencyProcedure(agencyOld, listAgencyNew): Observable<any> {
    let requestBody = {
      "idAgencyOld": null,
      "listIdAgencyNew": []
    };
    requestBody.idAgencyOld=agencyOld;
    requestBody.listIdAgencyNew = listAgencyNew ?? [];
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.post<any>(this.procedurePath + "incorporate-procedure", requestBody, { headers });
  }

  //KGG OS
  getTokenIofficeNoLoginKGG(body): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.post(this.adapter + "/kgg-eoffice/get-token-ioffice-nologin", body, { headers});
  }

  getTokenIofficeKGG(body): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.post(this.adapter + "/kgg-eoffice/get-token-ioffice", body, { headers});
  }
  
  getListVanBanIofficeV5KGG(body): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.post(this.adapter + "/kgg-eoffice/get-vb-ioffice-v5", body, { headers });
  }

  getListFileIofficeV5KGG(body): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.post(this.adapter + "/kgg-eoffice/get-file-vb", body, { headers });
  }

  getFileIofficeV5KGG(body): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.post(this.adapter + "/kgg-eoffice/download-file-vb", body, { headers });
  }

  postDossierToIoffice(body): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.post(this.adapter + "/kgg-eoffice/post-dossier-to-ioffice", body, { headers });
  }

  getProcedureSyncDateCTO(code): Observable<any> {
    return this.http.get(this.adapter + '/npadsvc/--save-procedure-sync-date?code=' + code).pipe();
  }

  getSyncProcedureCTO(agencyId, agencyCode, subsystemId): Observable<any> {
    return this.http.get(this.adapter + '/npadsvc/--procedures-cto?agency-id=' + agencyId +
      '&subsystem-id=' + subsystemId + '&agency-code=' + agencyCode).pipe();
  }

  getCheckChangeProcedure(agencyId, agencyCode, subsystemId, code, id): Observable<any> {
    console.log(this.adapter);
    return this.http.get(this.adapter + '/npadsvc/--save-procedure-sync-change?agency-id=' + agencyId +
      '&subsystem-id=' + subsystemId + '&agency-code=' + agencyCode + '&code=' + code + '&id=' + id).pipe();
  }
  checkAndUpdateEform(id): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.put<any>(this.processPath + 'check-and-eform-clone?id=' + id, { headers });
  }
}

