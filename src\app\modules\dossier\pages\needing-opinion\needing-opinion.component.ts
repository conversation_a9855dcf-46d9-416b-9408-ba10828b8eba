import { After<PERSON>iewInit, Component, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { FormGroup, FormControl } from '@angular/forms';
import { DossierSearchElement } from 'src/app/data/schema/dossier-search-element';
import { MatTableDataSource } from '@angular/material/table';
import { DossierService } from 'src/app/data/service/dossier/dossier.service';
import { ActivatedRoute, Router } from '@angular/router';
import { EnvService } from 'src/app/core/service/env.service';
import { SelectionModel } from '@angular/cdk/collections';
import { SnackbarService } from 'src/app/data/service/snackbar/snackbar.service';
import { MatDialog } from '@angular/material/dialog';
import { MainService } from 'src/app/data/service/main/main.service';
import { Subject } from 'rxjs';
import { DeploymentService } from 'src/app/data/service/deployment.service';
import * as tUtils from 'src/app/data/service/thoai.service';
import { GetOpinionComponent, GetOpinionDialogModel } from 'src/app/modules/dossier/dialogs/get-opinion/get-opinion.component';
import { ProcedureService } from 'src/app/data/service/procedure/procedure.service';
import { TransferProcessingComponent, TransferProcessingDialogModel } from '../../dialogs/transfer-processing/transfer-processing.component';
import { NotifyDialogClose } from 'src/app/data/service/notification.service';

@Component({
  selector: 'app-needing-opinion',
  templateUrl: './needing-opinion.component.html',
  styleUrls: ['./needing-opinion.component.scss']
})
export class NeedingOpinionComponent implements OnInit, AfterViewInit, OnDestroy {
  config = this.envService.getConfig();
  env = this.deploymentService.getAppDeployment()?.env;
  userAgency = JSON.parse(localStorage.getItem('userAgency'));
  userExperienceAgency = JSON.parse(localStorage.getItem('userExperienceAgency'));
  userId = localStorage.getItem('UID');
  selectedLang: string;
  pageTitle = {
    vi: `Hồ sơ cần xin ý kiến`,
    en: `Dossier needing opinion`
  };
  paymentLater = false;
  searchForm= new FormGroup({
    code: new FormControl(''),
    nationCode: new FormControl(''),
    dossierStatus: new FormControl('2'),
  });
  
  protected onDestroy = new Subject<void>();
  // ================================================= Table
  displayedColumns: string[] = ['stt', 'code', 'agency', 'content', 'attachments', 'assignee', 'status', 'action'];
  ELEMENTDATA: DossierSearchElement[] = [];
  dataSource: MatTableDataSource<DossierSearchElement>;
  selection = new SelectionModel<any>(true, []);
  countResult = 0;
  size = this.env?.OS_KTM?.defaultPageSize ? this.env?.OS_KTM?.defaultPageSize : 10;
  page = 1;
  pageIndex = 1;
  pgSizeOptions = this.config.pageSizeOptions;
  paramsQuery = {
    code: '',
    identity: '',
    page: '1',
    size: this.env?.OS_KTM?.defaultPageSize ? this.env?.OS_KTM?.defaultPageSize : 10,
    procedure: '',
    sortId: '',
  };
  searchDomain = '';
  justRegistered: any;
  paginationType = this.deploymentService.env.paginationType;
  numberOfElements = 0;

  listDisplayingDossier = [];
  checkNullData = 0;
  sortId = sessionStorage.getItem('dossierArrSortType') ? sessionStorage.getItem('dossierArrSortType') : '0';

  constructor(
    private router: Router,
    private dossierService: DossierService,
    private activeRoute: ActivatedRoute,
    private envService: EnvService,
    private deploymentService: DeploymentService,
    private dialog: MatDialog,
    private snackbarService: SnackbarService,
    private mainService: MainService,
    private procedureService: ProcedureService,
  ) {
    this.dataSource = new MatTableDataSource(this.ELEMENTDATA);
    this.getConfig();
    if (this.activeRoute.snapshot.queryParamMap.get('code') != null) {
      this.paramsQuery.code = this.activeRoute.snapshot.queryParamMap.get('code');
      this.searchForm.patchValue({
        code: this.paramsQuery.code
      });
    }
    if (this.activeRoute.snapshot.queryParamMap.get('procedure') != null) {
      this.paramsQuery.procedure = this.activeRoute.snapshot.queryParamMap.get('procedure');
    }
    if (this.activeRoute.snapshot.queryParamMap.get('page') != null) {
      this.paramsQuery.page = this.activeRoute.snapshot.queryParamMap.get('page');
    }
    if (this.activeRoute.snapshot.queryParamMap.get('size') != null) {
      this.paramsQuery.size = this.activeRoute.snapshot.queryParamMap.get('size');
    }
  }

  async ngOnInit(): Promise<void> {
    this.env = this.deploymentService.getAppDeployment()?.env;
    this.selectedLang = localStorage.getItem('language');
    this.mainService.setPageTitle(this.pageTitle[this.selectedLang]);
    this.getDossierTaskStatus();
    if (!!this.env?.paymentLater && this.env?.paymentLater === true)
    {
      this.paymentLater = true;
    }
    this.autoSearch();
  }

  viewOpinion(dossierId, dossierCode, consultationId, summary) {
    const dialogData = new GetOpinionDialogModel(dossierId, dossierCode, consultationId, summary);
    const dialogRef = this.dialog.open(GetOpinionComponent, {
      minWidth: '50vw',
      maxHeight: "85vh",
      data: dialogData,
      disableClose: true,
      autoFocus: false
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
      if (dialogResult === true) {
        const msgObj = {
          vi: 'Đã tiếp nhận ý kiến xử lý!',
          en: 'Received comments for processing!'
        };
        this.snackbarService.openSnackBar(1, msgObj[localStorage.getItem('language')], '', 'success_notification', this.config.expiredTime);
      }

      if (dialogResult === false) {
        const msgObj = {
          vi: 'Tiếp nhận ý kiến xử lý không thành công!',
          en: 'Receive feedback on unsuccessful processing!'
        };
        this.snackbarService.openSnackBar(1, msgObj[localStorage.getItem('language')], '', 'success_notification', this.config.expiredTime);
      }
      this.autoSearch();
    });
  }

  transferProcessing(dossierId, dossierCode, consultationId, summary) {
    const dialogData = new TransferProcessingDialogModel(dossierId, dossierCode, consultationId, summary);
    const dialogRef = this.dialog.open(TransferProcessingComponent, {
      minWidth: '50vw',
      maxHeight: "85vh",
      data: dialogData,
      disableClose: true,
      autoFocus: false
    });
    dialogRef.afterClosed().subscribe(dialogResult => {
      if (dialogResult === true) {
        const msgObj = {
          vi: 'Chuyển xử lý thành công!',
          en: 'Successful transfer processing!'
        };
        this.snackbarService.openSnackBar(1, msgObj[localStorage.getItem('language')], '', 'success_notification', this.config.expiredTime);
      }

      if (dialogResult === false) {
        const msgObj = {
          vi: 'Chuyển xử lý thất bại!',
          en: 'Transfer processing failed!'
        };
        this.snackbarService.openSnackBar(1, msgObj[localStorage.getItem('language')], '', 'success_notification', this.config.expiredTime);
      }
      this.autoSearch();
    });
  }

  downloadFile(id, filename, dossierId) {
    this.procedureService.downloadFile(id, dossierId).subscribe(data => {
      const dataType = data.type;
      const binaryData = [];
      binaryData.push(data);
      const downloadLink = document.createElement('a');
      downloadLink.href = window.URL.createObjectURL(new Blob(binaryData, { type: dataType }));
      downloadLink.setAttribute('download', filename);
      document.body.appendChild(downloadLink);
      downloadLink.click();
    }, err => {
      const msgObj = {
        vi: 'Không tìm thấy file!',
        en: 'File not found!'
      };
      this.snackbarService.openSnackBar(0, msgObj[this.selectedLang], '', 'error_notification', this.config.expiredTime);
      console.log(err);
    });
  }

  getConfig(){
    const config = this.deploymentService.getAppDeployment();
    if (!!config) {
        if (config.domain && config.domain.length > 0){
          // tslint:disable-next-line:max-line-length
          const domain = config.domain.filter(listdomain => ((listdomain.domain.toLowerCase().indexOf(window.location.host) > -1) || (window.location.host.toLowerCase().indexOf(listdomain.domain) > -1)));
          if (domain && domain.length > 0 && domain[0].rootAgency){
            this.searchDomain = '&domain-agency-id=' + domain[0].rootAgency.id;
          }
        }
    }
  }

  ngAfterViewInit() {
    // setTimeout(() => {
    //   // console.clear();
    //   this.autoSearch();
    // }, 1000);
  }

  ngOnDestroy() {
    this.onDestroy.next();
    this.onDestroy.complete();
  }
  // ========================================================== GET
  async getListDossier(searchString) {
    searchString = this.remakeRequestUrl(searchString);
    this.dossierService.getListDossier(searchString + this.searchDomain).subscribe(async data => {
      this.ELEMENTDATA = [];
      this.listDisplayingDossier = [];
      // this.countResult = data.totalElements;
      if (data.content.length > 0){
        this.checkNullData = 0;
      } else {
        this.checkNullData = 1;
      }
      this.numberOfElements = data.numberOfElements;
      this.pageIndex = data?.number + 1;
      for (let i = 0; i < data.numberOfElements; i++) {
        let requireAdditional = true;
        data.content[i].stt = this.size * (this.pageIndex - 1) + (i + 1);
        this.ELEMENTDATA.push(data.content[i]);
        this.listDisplayingDossier.push(data.content[i]);
      }
      this.dataSource.data = this.ELEMENTDATA;
      console.log('dataSource', this.dataSource);
      this.setTotalElements(data, this.paginationType);
    });
  }

  getDossierTaskStatus() {
    this.dossierService.getAgencyTag(this.deploymentService.env.dossierTaskStatus.justRegistered.id).subscribe(rs => {
      this.justRegistered = rs;
    }, err => {
      console.log(err);
    });
  }

  // ========================================================== FUNCTION
  generateSearchString(spec, page, size, sort) {
    const rootAgencyId = this.userAgency?.ancestors[this.userAgency?.ancestors.length - 1].id;
    let agencyIdSearch = '';
    if (this.userAgency?.parent.id == rootAgencyId) {
      agencyIdSearch = `&agency-id=${this.userAgency.id}`;
    } else {
      agencyIdSearch = `&agency-id=${this.userAgency.parent.id}`;
    }
    let position = `&position=${this.userExperienceAgency.position.name}`;
    let assignee = `&assignee-id=${this.userId}`;
    const formObj = this.searchForm.getRawValue();
    let searchString = 'search-dossier-need-opinion?code=' + encodeURIComponent(formObj.code.trim()).substring(0, 1000) +
      '&spec=' + spec +
      '&page=' + page +  
      '&size=' + size +
      '&sort=' + sort +
      agencyIdSearch + position + assignee;
    return searchString;
  }

  remakeRequestUrl(searchString) { 
    const formObj = this.searchForm.getRawValue();

    searchString = searchString.replace('search-dossier-need-opinion?', '');
    const searchStringObj = tUtils.parseParams(searchString);
    if (this.deploymentService.optimize.dossierSearch.codeMatch) {
      searchStringObj['code-match'] = searchStringObj['code'];
      delete searchStringObj['code'];

      searchStringObj['nation-code-match'] = formObj.nationCode;
    }

    if (this.deploymentService.optimize.dossierSearch.onlineReceptionSort.length !== 0) {
      searchStringObj['sort'] = this.deploymentService.optimize.dossierSearch.onlineReceptionSort;
    }

    const params = new URLSearchParams(searchStringObj);
    searchString = 'search-dossier-need-opinion?' + params.toString();

    return searchString;
  }

  autoSearch() {
    this.pageIndex = Number(this.paramsQuery.page);
    this.page = Number(this.paramsQuery.page);
    this.size = Number(this.paramsQuery.size);
    const searchString = this.generateSearchString(this.paginationType, (this.page - 1), this.size, 'id,desc');
    console.log('searchString', searchString);
    this.getListDossier(searchString);
  }

  // paginate(event: any, type) {
  paginate(){
    const searchString = this.generateSearchString(this.paginationType, (this.pageIndex - 1), this.size, 'id,desc');
    this.getListDossier(searchString);
    this.router.navigate([], {
      queryParams: {
        code: this.paramsQuery.code,
        identity: this.paramsQuery.identity.trim(),
        page: this.pageIndex,
        size: this.size,
        procedure: this.paramsQuery.procedure,
        sortId: this.sortId,
      }
    });
  }

  // ========================================================== DIALOGS
  receivingDossier(event:MouseEvent,id, procedure, procedureProcessDef, dossierStatusId) {
    const isOpenInNewTabHCM = this.deploymentService.env?.OS_HCM?.isOpenInNewTabHCM
    const isOpenInNewTab = this.deploymentService.getAppDeployment()?.env?.OS_KTM?.openInNewTab == true ? true  : false;
    if (this.deploymentService.env?.disableCancelDossier && dossierStatusId === 12) {
      return;
    }
    if (isOpenInNewTab) {
      const url = this.router.serializeUrl(
        this.router.createUrlTree(['dossier/processing/' + id], {
          queryParams: {
            procedure,
            procedureProcessDef
          }
        })
      );

      window.open(url, '_blank');
      return;
    }
    if(isOpenInNewTabHCM&& event.ctrlKey){
      const url = this.router.serializeUrl(
        this.router.createUrlTree(['dossier/processing/' + id], {
          queryParams: {
            procedure,
            procedureProcessDef
          }
        }),
      );
      window.open(url, '_blank');
      return;
    }
    this.router.navigate(['dossier/processing/' + id], {
      queryParams: {
        procedure,
        procedureProcessDef
      }
    });
  }
  onRightClick(id, procedure, procedureProcessDef){
    const queryParamsObject = {
      procedure: procedure,
      procedureProcessDef
    };
    const isOpenInNewTabHCM = this.deploymentService.env?.OS_HCM?.isOpenInNewTabHCM
    if(isOpenInNewTabHCM){
    const url = this.router.serializeUrl(
      this.router.createUrlTree(['dossier/processing/' + id], {
          queryParams: queryParamsObject,
        }),
    );
    window.open(url, '_blank');
    return;
    }
  }

  setTotalElements(data, paginationType) {
    if (paginationType === 'page') {
      this.countResult = data.totalElements;
    } else {
      if (data.last) {
        if (!!data.number) {
          this.page = data.number + 1;
          this.countResult = this.size * this.page;
        } else {
          this.countResult = this.size * this.page;
        }
      } else {
        if (this.numberOfElements < this.size) {
          this.countResult = this.size * this.page;
        } else {
          this.countResult = this.countResult + this.ELEMENTDATA.length + 1;
        }
      }
    }
  }

  changePageOrSize(obj) {
    if (tUtils.hasOwnProperty(obj, 'currentPage')) {
      this.page = obj.currentPage;
      this.pageIndex = obj.currentPage;
    }
    if (tUtils.hasOwnProperty(obj, 'itemsPerPage')) {
      this.size = obj.itemsPerPage;
    }
    if (tUtils.hasOwnProperty(obj, 'totalItems')) {
      this.countResult = obj.totalItems;
    } else {
      this.paginate();
    }
  }
  reloadPage() {
    this.router.navigateByUrl('/dossier/needing-opinion', { skipLocationChange: true }).then(() => {
      this.router.navigate(['/dossier/needing-opinion']);
    });
  }

  getStatusName(status: number | undefined): string {
    switch (status) {
      case 0: return 'Chờ xử lý';
      case 1: return 'Đang xử lý';
      case 2: return 'Đã cho ý kiến';
      default: return 'Không xác định';
    }
  }
}