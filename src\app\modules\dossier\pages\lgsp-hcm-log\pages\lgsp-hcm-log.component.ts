import { Component, OnInit, ChangeDetectorRef, AfterViewInit, ViewChild, ElementRef  } from '@angular/core';
import { FormGroup, Validators, FormControl } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import { KeycloakService } from 'keycloak-angular';
import { Router, ActivatedRoute } from '@angular/router';
import { FormService } from 'src/app/data/service/form/form.service';
import { ProcostTypeService } from 'src/app/data/service/procost-type/procost-type.service';
import { ConfigService } from 'src/app/data/service/dossier/config.service';
import { EnvService } from 'src/app/core/service/env.service';
import { FormElement } from 'src/app/data/schema/form-element';
import { MatTableDataSource } from '@angular/material/table';
import { SnackbarService } from 'src/app/data/service/snackbar/snackbar.service';
import { MainService } from 'src/app/data/service/main/main.service';
import { DossierService } from 'src/app/data/service/dossier/dossier.service';
import { DatePipe } from '@angular/common';
import { LGSPHCMLogDetailComponent } from '../dialogs/view-detail/view-detail.component';
import { HttpResponse } from '@angular/common/http';
import * as tUtils from 'src/app/data/service/thoai.service';

@Component({
  selector: 'app-lgsp-hcm-log',
  templateUrl: './lgsp-hcm-log.component.html',
  styleUrls: ['./lgsp-hcm-log.component.scss']
})

export class LGSPHCMLogComponent implements OnInit, AfterViewInit {
  constructor(
    private router: Router,
    private keycloak: KeycloakService,
    private dialog: MatDialog,
    private formService: FormService,
    private procostTypeService: ProcostTypeService,
    private configService: ConfigService,
    private envService: EnvService,
    private cdRef: ChangeDetectorRef,
    private mainService: MainService,
    private snackbarService: SnackbarService,
    private dossierService: DossierService,
    private datePipe: DatePipe,
    private activeRoute: ActivatedRoute,
  ) {
    this.dataSource = new MatTableDataSource(this.ELEMENTDATA);
  }
  config = this.envService.getConfig();

  ELEMENTDATA: FormElement[] = [];
  dataSource: MatTableDataSource<FormElement>;

  searchForm = new FormGroup({
    keyword: new FormControl(''),
    dateFrom: new FormControl(''),
    dateTo: new FormControl(''),
  });
  pageTitle = {
    vi: `Tra cứu Log LGSP HCM`,
    en: `LGSP HCM Log`
  };
  resultSuccess = {
    vi: `Thành công`,
    en: `Success`
  };
  resultFail = {
    vi: `Thất bại`,
    en: `Fail`
  };

  displayedColumns: string[] = ['stt', 'APIFunction', 'DossierCode', 'CallTime', 'ResultName', 'action'];
  listPattern: Array<any> = [];
  size = 10;
  pageIndex = 1;
  page = 1;
  countResult = 0;
  pgSizeOptions = this.config.pageSizeOptions;
  selectedLang: string;
  defautlConfigId: Array<string> = [];
  defautlPatternId: Array<string> = [];
  idDefault = '';
  formOject: any;
  isStatus: any = null;

  ngOnInit(): void {
    const d = tUtils.newDate();
    this.searchForm.patchValue({
      dateFrom: d.getFullYear() + '-' + ('0' + (d.getMonth() + 1)).slice(-2) + '-' + '01',
      dateTo: d.getFullYear() + '-' + ('0' + (d.getMonth() + 1)).slice(-2) + '-' + ('0' + d.getDate()).slice(-2)
    });
    this.size = Number(this.activeRoute.snapshot.queryParamMap.get('size')) === 0 ? this.size : Number(this.activeRoute.snapshot.queryParamMap.get('size'));
    this.page = Number(this.activeRoute.snapshot.queryParamMap.get('page')) === 0 ? this.page : Number(this.activeRoute.snapshot.queryParamMap.get('page')); 
    this.selectedLang = localStorage.getItem('language');
    this.mainService.setPageTitle(this.pageTitle[localStorage.getItem('language')]);
    this.getLGSPHCMLog();
  }

  ngAfterViewInit() {
    this.cdRef.detectChanges();
  }

  getLGSPHCMLog() {
    let searchString = '?page=' + (this.page - 1) + '&size=' + this.size;
    this.formOject = this.searchForm.getRawValue();
    const formObj = this.formOject;
    searchString = searchString + '&keyword=' + encodeURIComponent(formObj.keyword.trim());
    if (formObj.dateFrom != null && formObj.dateFrom !== '') {
      searchString = searchString + '&from-date=' + this.datePipe.transform(formObj.dateFrom, 'yyyyMMdd000000');
    }
    if (formObj.dateTo != null && formObj.dateTo !== '') {
      searchString = searchString + '&to-date=' + this.datePipe.transform(formObj.dateTo, 'yyyyMMdd235959');
    }
    searchString = searchString + '&sort=CallTime,desc';
    if(this.isStatus != null)
    {
      searchString += '&isStatus=' + this.isStatus
    }

    const fromDate = new Date(formObj.dateFrom);
    fromDate.setHours(0);
    fromDate.setMinutes(0);
    fromDate.setSeconds(0);
    fromDate.setMilliseconds(165);
    const toDate = new Date(formObj.dateTo);
    toDate.setHours(23);
    toDate.setMinutes(59);
    toDate.setSeconds(59);
    toDate.setMilliseconds(165);

    if (formObj.dateFrom === '' || formObj.dateTo === '') {
      const message = {
        vi: 'Ngày thống kê không được để trống!',
        en: 'Statistical date cannot be blank!'
      };
      this.snackbarService.openSnackBar(0, '', message[localStorage.getItem('language')], 'error_notification', 3000);
      return;
    } 
    else if (fromDate > toDate) {
      const message = {
        vi: 'Ngày bắt đầu phải nhỏ hơn hoặc bằng ngày kết thúc!',
        en: 'Start date must be less than or equal to end date!'
      };
      this.snackbarService.openSnackBar(0, '', message[localStorage.getItem('language')], 'error_notification', 3000);
      return;
    }

    this.dossierService.getLGSPHCMLog(searchString).subscribe(data => {
      this.ELEMENTDATA = [];
      this.countResult = data.totalElements;
      for (let i = 0; i < data.numberOfElements; i++) {
        data.content[i].stt = this.size * (this.pageIndex - 1) + (i + 1);
        data.content[i].ResultName = this.getResultName(data.content[i].Result);
        this.ELEMENTDATA.push(data.content[i]);
       }
      this.dataSource.data = this.ELEMENTDATA;
    });
  }

  onReportTypeChange(event) {
    switch (event?.value) {
      case '1': {
        this.isStatus = true;
        break;
      }
      case '2': {
        this.isStatus = false;
        break;
      }
      default: {
       this.isStatus = null;
        break;
      }
    }
    console.log(this.isStatus);
  }


  async getListLGSPHCMExcel() {
    this.formOject = this.searchForm.getRawValue();
    const formObj = this.formOject;
    let searchString ='?keyword=' + encodeURIComponent(formObj.keyword.trim());
    if (formObj.dateFrom != null && formObj.dateFrom !== '') {
      searchString = searchString + '&from-date=' + this.datePipe.transform(formObj.dateFrom, 'yyyyMMdd000000');
    }
    if (formObj.dateTo != null && formObj.dateTo !== '') {
      searchString = searchString + '&to-date=' + this.datePipe.transform(formObj.dateTo, 'yyyyMMdd235959');
    }
    searchString = searchString + '&sort=CallTime,desc';
    if(this.isStatus != null)
    {
      searchString += '&isStatus=' + this.isStatus
    }
    const fromDate = new Date(formObj.dateFrom);
    fromDate.setHours(0);
    fromDate.setMinutes(0);
    fromDate.setSeconds(0);
    fromDate.setMilliseconds(165);
    const toDate = new Date(formObj.toDate);
    toDate.setHours(23);
    toDate.setMinutes(59);
    toDate.setSeconds(59);
    toDate.setMilliseconds(165);

    if (formObj.dateFrom === '' || formObj.dateTo === '') {
      const message = {
        vi: 'Ngày thống kê không được để trống!',
        en: 'Statistical date cannot be blank!'
      };
      this.snackbarService.openSnackBar(0, '', message[localStorage.getItem('language')], 'error_notification', 3000);
      return;
    } 
    else if (fromDate > toDate) {
      const message = {
        vi: 'Ngày bắt đầu phải nhỏ hơn hoặc bằng ngày kết thúc!',
        en: 'Start date must be less than or equal to end date!'
      };
      this.snackbarService.openSnackBar(0, '', message[localStorage.getItem('language')], 'error_notification', 3000);
      return;
    }

    await this.dossierService.exportListLGSPLog(searchString);

    // await this.dossierService.exportLGSPHCMLog(searchString);
    // this.dossierService.exportLGSPHCMLog(searchString).subscribe((response: HttpResponse<Blob>) => {
    //   const blob = new Blob([response.body], { type: 'application/vnd.ms-excel' });
    //   const link = document.createElement('a');
    //   link.href = window.URL.createObjectURL(blob);
      
    //   // Extract the filename from the Content-Disposition header
    //   const contentDisposition = response.headers.get('Content-Disposition')
    //   const filenameMatch = contentDisposition && contentDisposition.match(/filename="(.+?)"/);
    //   const filename = filenameMatch ? filenameMatch[1] : 'download.xlsx';
    //   link.download = filename;
    //   link.click();
    // })
  }

  onConfirm() {
    this.page = 1;
    this.pageIndex = 1;
    this.getLGSPHCMLog();
  }

  getResultName(result)
  {
    if(result == true)
    {
      return this.resultSuccess[localStorage.getItem('language')];
    }
    else
    {
      return this.resultFail[localStorage.getItem('language')];
    }
  }

  paginate(event: any, type) {
    switch (type) {
      case 0:
        this.pageIndex = event;
        this.getLGSPHCMLog();
        break;
      case 1:
        this.pageIndex = 1;
        this.page = 1;
        this.getLGSPHCMLog();
        break;
    }
  }
  
  viewDetail(data) {
    this.dialog.open(LGSPHCMLogDetailComponent, {
      width: '900px',
      height: '90%',
      data: data,
      disableClose: true,
      autoFocus: false
    });
  }
}
