<button mat-icon-button class="close-button" (click)="onDismiss()">
    <mat-icon>close</mat-icon>
</button>
<h3 class="dialog_title" mat-dialog-title i18n="@@printReceiptPaper">In biên lai giấy</h3>

<div class="receipt" fxLayout="row" fxLayout.xs="column" fxLayout.sm="column">
    <mat-table [dataSource]="receiptDataSource" fxFlex='grow'>
      
        <ng-container matColumnDef="codeReceipt">
            <mat-header-cell *matHeaderCellDef i18n="@@receiptCode1">Số biên lai</mat-header-cell>
            <mat-cell *matCellDef="let row" i18n-data-label="Số biên lai"> {{row.code}}
            </mat-cell>
           
        </ng-container>
        <ng-container matColumnDef="procostType">
            <mat-header-cell *matHeaderCellDef i18n><PERSON><PERSON><PERSON> phí</mat-header-cell>
            <mat-cell *matCellDef="let row" i18n-data-label="Loại lệ phí"> {{row.typeFee}}
            </mat-cell>
           
        </ng-container>

        <ng-container matColumnDef="amount">
            <mat-header-cell *matHeaderCellDef i18n>Thành tiền</mat-header-cell>
            <mat-cell *matCellDef="let row" i18n-data-label="Thành tiền">
                <div>{{row.total}} </div></mat-cell>
            <!-- <mat-footer-cell *matFooterCellDef class="totalCell">{{totalCost}}
                <span class="rest">(<span i18n>Còn lại</span> {{totalRemaining}})</span>
            </mat-footer-cell> -->
        </ng-container>
        
        <ng-container matColumnDef="action">
            <mat-header-cell *matHeaderCellDef i18n>Thao tác</mat-header-cell>
            <mat-cell *matCellDef="let row" i18n-data-label="Thao tác">
                <button mat-flat-button class="btnSecondary" [matMenuTriggerFor]="animals" >
                    <mat-icon>print</mat-icon>
                    <span>In biên lai</span>
                    <mat-icon>keyboard_arrow_down</mat-icon>
                </button>
                <mat-menu #animals="matMenu">
                    <ng-container >
                        <div *ngFor="let bill of listConfigTemplate" fxLayout="row" fxLayoutAlign="space-between">
                            <div class="d-flex flex-row">
                                <div style="background-color: #FFF; cursor: pointer" class="w-250">
                                    <button mat-menu-item (click)="createPrintBill(row.code, bill.file.path, bill)" >
                                        {{bill.name}}
                                    </button>
                                </div>
                            </div>
                        </div>
                    </ng-container>
                    <!-- <button mat-menu-item [matMenuTriggerFor]="vertebrates">Vertebrates</button>  (click)="editFile(bill.id, item.fileSignId)"-->
                </mat-menu>
            </mat-cell>
            <!-- <mat-footer-cell *matFooterCellDef class="totalCell">{{totalCost}}
                <span class="rest">(<span i18n>Còn lại</span> {{totalRemaining}})</span>
            </mat-footer-cell> -->
        </ng-container>

        

        <mat-header-row *matHeaderRowDef="receiptDisplayedColumns"></mat-header-row>
        <mat-row *matRowDef="let row; columns: receiptDisplayedColumns;"></mat-row>
      
    </mat-table>
</div>

<!-- <form [formGroup]="saveForm" class="addForm edit" (submit)="onConfirm()" id="ngSaveForm">
    <div fxLayout="row" fxLayoutAlign="center" fxLayout.xs="row" fxLayout.sm="row" class="divAddBtn">
        <button [disabled]="isSubmit" mat-flat-button fxFlex='grow' class="saveBtn" type="submit" form="ngSaveForm">
            <span i18n>Phát hành biên lai</span>
        </button>
    </div>
</form> -->
<!-- <button mat-icon-button class="close-button" (click)="onDismiss()">
    <mat-icon>close</mat-icon>
</button>
<h3 class="dialog_title" mat-dialog-title i18n>Cấp mã số</h3>
<form [formGroup]="addForm" class="addForm edit" (submit)="takeNumber()" id="ngAddForm">
    <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="column" class="marginRow">
        <mat-form-field appearance="outline" fxFlex.gt-sm="98" fxFlex.gt-xs="98" fxFlex='grow'>
            <mat-label i18n>Loại quyết định</mat-label>
            <mat-select formControlName="loaiQuyetDinh"  required [disabled]="disableTagSelectBox">
                <mat-option *ngFor='let tag of dataSource.data;' value="{{tag.id}}">
                    {{tag.name}}
                    <span *ngIf="tag.name== undefined || tag.name == null || tag.name.trim() == ''" i18n>(Không tìm thấy bản dịch)</span>
                </mat-option>
            </mat-select>
        </mat-form-field>
    </div>
    <br>
    <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="column" class="marginRow">
        <mat-form-field style="margin-right: 10px;" appearance="outline" fxFlex.gt-sm="48" fxFlex='48'>
            <mat-label i18n>Cú pháp bộ số</mat-label>
            <mat-select formControlName="displaySyntax"   required>
                <mat-option *ngFor='let proce of dataSource1.data;' value="{{proce.id}}">
                    {{proce.name}}
                    <span *ngIf="proce.name== undefined || proce.name == null || proce.name.trim() == ''" i18n>(Không tìm thấy bản dịch)</span>
                </mat-option>
            </mat-select>
        </mat-form-field>
      
            <button  mat-flat-button style="margin-top: 5px" class="saveBtnCode"   fxFlex.gt-sm="48" fxFlex='48'>
                <span i18n>Lấy số</span>
            </button>
       
    </div>
    <br>
   
    <div fxLayout="row" fxLayout.xs="column" fxLayout.sm="column" class="marginRow" >
            <mat-form-field appearance="outline" fxFlex.gt-sm="98" fxFlex='98' >
                <mat-label i18n>Mã số được cấp</mat-label>
                <input type="text" matInput (change)="changeCode(0 , $event)" value="{{fieldArray[0].code}}" readonly="false" >
                <mat-icon matSuffix >mode_edit</mat-icon>
            </mat-form-field>
        </div>
    <br>
    
</form>

<div class="receipt" fxLayout="row" fxLayout.xs="column" fxLayout.sm="column">
    <mat-table [dataSource]="receiptDataSource" fxFlex='grow'>
        <ng-container matColumnDef="select">
            <mat-header-cell *matHeaderCellDef>
                <mat-checkbox (change)="$event ? masterToggle() : null; changeProduct(selection)"
                    [checked]="selection.hasValue() && isAllSelected()"
                    [indeterminate]="selection.hasValue() && !isAllSelected()" [aria-label]="checkboxLabel()" [disabled]= "otherUnit">
                </mat-checkbox>
            </mat-header-cell>
            <mat-cell *matCellDef="let row" i18n-data-label="Chọn">
                <mat-checkbox (click)="$event.stopPropagation()" (change)="$event ? selection.toggle(row) : null; changeProduct(selection)"
                    [checked]="selection.isSelected(row)" [aria-label]="checkboxLabel(row)" [disabled]= "mainUnit != '' && mainUnit != row.monetaryUnit">
                </mat-checkbox>
            </mat-cell>
            <mat-footer-cell *matFooterCellDef></mat-footer-cell>
        </ng-container>

        <ng-container matColumnDef="procostType">
            <mat-header-cell *matHeaderCellDef i18n>Loại lệ phí</mat-header-cell>
            <mat-cell *matCellDef="let row" i18n-data-label="Loại lệ phí"> {{row.typeName}}
            </mat-cell>
            <mat-footer-cell *matFooterCellDef i18n>Tổng cộng:</mat-footer-cell>
        </ng-container>

        <ng-container matColumnDef="quantity">
            <mat-header-cell *matHeaderCellDef i18n>Số lượng</mat-header-cell>
            <mat-cell *matCellDef="let row" i18n-data-label="Số lượng">
                {{row.quantity}}
            </mat-cell>
            <mat-footer-cell *matFooterCellDef></mat-footer-cell>
        </ng-container>

        <ng-container matColumnDef="cost">
            <mat-header-cell *matHeaderCellDef i18n>Mức lệ phí</mat-header-cell>
            <mat-cell *matCellDef="let row" i18n-data-label="Mức lệ phí">
                {{row.cost | number}}
                {{row.monetaryUnit}} </mat-cell>
            <mat-footer-cell *matFooterCellDef></mat-footer-cell>
        </ng-container>

        <ng-container matColumnDef="amount">
            <mat-header-cell *matHeaderCellDef i18n>Thành tiền</mat-header-cell>
            <mat-cell *matCellDef="let row" i18n-data-label="Thành tiền">
                <div>{{row.quantity*row.cost | number }} {{row.monetaryUnit}} <span *ngIf= "row.paid == row.quantity*row.cost"> (Đã thanh toán)</span></div></mat-cell>
            <mat-footer-cell *matFooterCellDef class="totalCell">{{totalCost}}
                <span class="rest">(<span i18n>Còn lại</span> {{totalRemaining}})</span>
            </mat-footer-cell>
        </ng-container>

        <mat-header-row *matHeaderRowDef="receiptDisplayedColumns"></mat-header-row>
        <mat-row *matRowDef="let row; columns: receiptDisplayedColumns;"></mat-row>
        <mat-footer-row *matFooterRowDef="receiptDisplayedColumns"></mat-footer-row>
    </mat-table>
</div>

<form [formGroup]="saveForm" class="addForm edit" (submit)="onConfirm()" id="ngSaveForm">
    <div fxLayout="row" fxLayoutAlign="center" fxLayout.xs="row" fxLayout.sm="row" class="divAddBtn">
        <button [disabled]="isSubmit" mat-flat-button fxFlex='grow' class="saveBtn" type="submit" form="ngSaveForm">
            <span i18n>Phát hành biên lai</span>
        </button>
    </div>
</form> -->