import {Client, messageCallbackType, StompSubscription, StompConfig} from '@stomp/stompjs';
import {ApiProviderService} from 'core/service/api-provider.service';

export class WebsocketService {

  private static instance: WebsocketService;

  private readonly wsUrl: string;

  private client: Client;

  private onConnectCb?: Function;
  private onDisconnectCb?: Function;
  private onErrorCb?: Function;
  private isConnected = false;


  subs: StompSubscription[] = [];

  constructor(private apiProviderService: ApiProviderService) {

    this.wsUrl = buildWebsocketUrl(apiProviderService);

    const token = localStorage.getItem('userToken');

    const stompConfig = new WSStompConfig(this.wsUrl, token, 5000);

    this.client = new Client(stompConfig);


    this.client.onConnect = () => {
      this.isConnected = true;
      this.onConnectCb && this.onConnectCb();
    };

    this.client.onDisconnect = () => {
      this.isConnected = false;
      this.onDisconnectCb && this.onDisconnectCb();
    };

    this.client.onStompError = (frame: any) => {
      console.error('WS: Broker reported error: ' + frame.headers.message);
      console.error('WS: Additional details: ' + frame.body);
      this.onErrorCb && this.onErrorCb();
    };

  }

  static getInstance(apiProviderService): WebsocketService {
    if (!WebsocketService.instance) {
      return new WebsocketService(apiProviderService);
    }
    return WebsocketService.instance;
  }

  connect(onConnectCb: Function, onDisconnectCb: Function, onErrorCb: Function): void {

    this.onConnectCb = onConnectCb;
    this.onDisconnectCb = onDisconnectCb;
    this.onErrorCb = onErrorCb;

    this.client.activate();

  }

  disconnect(): void {
    if (this.client) {
      this.client.deactivate();
    }
    if (this.subs) {
      this.subs.forEach(s => s.unsubscribe());
    }
  }

  subscribe(destination: string, cb: messageCallbackType): void {
    const sub = this.client.subscribe(destination, cb);
    this.subs.push(sub);
  }

  sendMessage(destination: string, body: string): void {
    this.client.publish({destination, body});
  }

}

export function buildWebsocketUrl(apiProviderService) {

  let url = apiProviderService.getUrl('digo', 'notification');
  url = 'ws://' + url.substring(url.indexOf('//') + 2, url.length) + '/notification-ws-service/ws-endpoint';
  // "wss://" + NotificationConstant.NOTIFICATION_WS_ENDPOINT;;  // wss for backend https

  return url;

}

class WSStompConfig extends StompConfig {
  constructor(url: string, token: string, reconnectDelay: number) {
    super();
    this.brokerURL = url;
    this.debug = (str) => {
      console.log('STOMP: url = ' + url + '\n' + str);
    };
    this.reconnectDelay = reconnectDelay;
    this.connectHeaders = {Authorization: 'Bearer ' + token};
  }
}
