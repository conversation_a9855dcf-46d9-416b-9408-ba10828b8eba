import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { ApiProviderService } from 'src/app/core/service/api-provider.service';
import { Observable } from 'rxjs';
import { EnvService } from 'src/app/core/service/env.service';
import { DeploymentService } from '../deployment.service';

@Injectable({
  providedIn: 'root'
})
export class CitizensIdService {

  config = this.envService.getConfig();
  constructor(
    private envService: EnvService,
    private http: HttpClient,
    private apiProviderService: ApiProviderService,
    private deploymentService: DeploymentService
  ) { }

  private adapter = this.apiProviderService.getUrl('digo', 'adapter');

  checkUserInfo(data: any, agencyId: string, subsystemId: string): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.set('Content-Type', 'application/json');
    // tslint:disable-next-line: max-line-length
    return this.http.put<any>(this.adapter + '/national-citizens/--verify?agency-id=' + agencyId + '&subsystem-id=' + subsystemId, data, { headers });
  }

  getVerifyCitizens(): boolean {
    const config = this.deploymentService.getAppDeployment();
    if (!!config?.env?.verifyCitizens) {
      return config?.env?.verifyCitizens;
    } else {
      return false;
    }
  }

}
