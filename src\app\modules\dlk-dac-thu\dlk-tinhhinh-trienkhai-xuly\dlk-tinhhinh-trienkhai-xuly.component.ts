import {Component, OnInit} from '@angular/core';
import {FormControl, FormGroup} from '@angular/forms';
import {MatTableDataSource} from '@angular/material/table';
import * as tUtils from 'data/service/thoai.service';
import {SnackbarService} from 'src/app/data/service/snackbar/snackbar.service';
import {DatePipe} from '@angular/common';
import {DeploymentService} from 'src/app/data/service/deployment.service';
import {MatTabChangeEvent} from '@angular/material/tabs';
import {animate, state, style, transition, trigger} from '@angular/animations';
import {
  ElementProcessingStatistic,
  ElementDigitizationStatistic
} from './model/element-table';
import {MatDialog} from '@angular/material/dialog';
import { DLKStatisticsService } from 'src/app/data/service/dlk-statistics/dlk-statistics.service';
import { EnvService } from 'src/app/core/service/env.service';
import { UserService } from 'src/app/data/service/user.service';
import { MainService } from 'src/app/data/service/main/main.service';

@Component({
  selector: 'app-dlk-tinhhinh-trienkhai-xuly',
  templateUrl: './dlk-tinhhinh-trienkhai-xuly.component.html',
  styleUrls: ['./dlk-tinhhinh-trienkhai-xuly.component.scss'],
  animations: [
    trigger('detailExpand', [
      state('collapsed', style({height: '0px', minHeight: '0'})),
      state('expanded', style({height: '*'})),
      transition('expanded <=> collapsed', animate('225ms cubic-bezier(0.4, 0.0, 0.2, 1)')),
    ]),
    trigger('detail6dExpand', [
      state('collapsed6d', style({height: '0px', minHeight: '0'})),
      state('expanded6d', style({height: '*'})),
      transition('expanded6d <=> collapsed6d', animate('225ms cubic-bezier(0.4, 0.0, 0.2, 1)')),
    ]),
  ],
})

export class DlkTinhHinhTrienKhaiXuLyComponent implements OnInit {
  language = localStorage.getItem('language') === 'vi' ? 228 : 46;
  userAgency?: any = JSON.parse(localStorage.getItem('userAgency'));
  userAgencyCount?: any = JSON.parse(localStorage.getItem('listAgencyUser'))?.length || 0;
  parentAgency = '';
  env = this.deploymentService.getAppDeployment()?.env;

  searchForm = new FormGroup({
    fromDate: new FormControl(''),
    currentFromDate: new FormControl(''),
    toDate: new FormControl(''),
    currentToDate: new FormControl(''),
    agency: new FormControl(''),
    agencyCtrl: new FormControl(''),
    submissionMethod: new FormControl(''),
    receivingResultsMethod: new FormControl(''),
    bypassAuthorizationProcedure: new FormControl('')
  });

  agencyPage = 0;
  isAgencyListFull = true;
  agencyList: Array<any> = [];

  subAgencyPage = 0;
  isSubAgencyListFull = true;
  subAgencyList: Array<any> = [];

  displayedProcessingColumns: string[] = ['stt', 'agencyName',  'totalHSTN', 'totalTTHC', 'directPartHSTN', 'directPartHSTNBCCI', 'directPartTTHC', 'onlinePartHSTNDirect', 'onlinePartHSTNOnline', 'onlinePartHSTNBCCI', 'onlinePartTTHC', 'wholeProcessHSTNDirect', 'wholeProcessHSTNonline', 'wholeProcessHSTNBCCI', 'wholeProcessTTHC', 'recordsExist', 'receiveMCDT', 'onlineReception', 'redirectAheadOfTime', 'redirectOnTime', 'redirectLate', 'onlineAheadOfTime', 'onlineOnTime', 'onlineLate', 'wholeProcessAheadOfTime', 'wholeProcessOnTime', 'wholeProcessLate', 'solving', 'settlementRate'];
  displayed2584Columns: string[] = ['stt', 'agency', 'totalReceiverDossier', 'totalDossierNotDigitized', 'totalDossierDigitized', 'digitizedRate', 'fullDigitizedDossier', 'fullDigitizedRate', 'totalResolvedDossier', 'totalDossierNotDigitizedResolutionResult', 'totalDossierDigitizedResolutionResult', 'digitizedResolutionResultRate', 'totalDossierDigitizedAndResolutionResult', 'digitizedRateAndResolutionResultRate', 'digitizedRateAndResolutionResultRate1', 'digitizedRateAndResolutionResultRate2'];
  displayedDigitizationColumns: string[] = ['stt', 'agency', 'totalReceiverDossier', 'totalDossierNotDigitized', 'totalDossierDigitized', 'digitizedRate', 'fullDigitizedDossier', 'fullDigitizedRate', 'totalResolvedDossier', 'totalDossierNotDigitizedResolutionResult', 'totalDossierDigitizedResolutionResult', 'digitizedResolutionResultRate', 'totalDossierDigitizedAndResolutionResult', 'digitizedRateAndResolutionResultRate'];
  
  dataProcessingSource: MatTableDataSource<ElementProcessingStatistic>;
  dataDigitizationSource: MatTableDataSource<ElementDigitizationStatistic>;

  ELEMENTPROCESSINGSTATISTIC: ElementProcessingStatistic[] = [];
  ELEMENTDIGITIZATIONSTATISTIC: ElementDigitizationStatistic[] = [];
  ELEMENTPROCESSINGSTATISTICEXPORT: ElementProcessingStatistic[] = [];


  PROCEDUREDATA: any[] = [];

  procedureReady = 0;
  isLoading = false;
  agencyId: '';
  agencyName: '';
  tabIndex = 0;
  receivingKinds = this.deploymentService.env?.OS_HCM?.receivingKinds ? this.deploymentService.env.OS_HCM.receivingKinds : [];

  timeOutSearch = null;
  msTimeOutSearch = this.deploymentService.env.statistics.msTimeOutSearch;
  
  config = this.envService.getConfig();
  rootAgencyId: string;
  isAdmin = false;
  isDigitizedStatistic = false;
  footerData: any[][] = [];

  constructor(
    private statisticsService: DLKStatisticsService,
    private deploymentService: DeploymentService,
    private snackbarService: SnackbarService,
    private envService: EnvService,
    private datePipe: DatePipe,
    private dialog: MatDialog,
    private mainService: MainService,
    private userService: UserService
  ) {
    this.parentAgency = this.env?.OS_DLK?.rootAgencyId;
    this.agencyId = this.env?.OS_DLK?.rootAgencyId;
    this.dataProcessingSource = new MatTableDataSource();
    this.dataDigitizationSource = new MatTableDataSource();
  }

  pageTitle = {
    vi: `Tình hình triển khai, xử lý`,
    en: `Statistics processing & handle`
  };

  ngOnInit(): void {
    this.mainService.setPageTitle(this.pageTitle[localStorage.getItem('language')]);
    const d = tUtils.newDate();
    this.searchForm.patchValue({
      fromDate: d.getFullYear() + '-' + ('0' + (d.getMonth() + 1)).slice(-2) + '-' + '01',
      toDate: d.getFullYear() + '-' + ('0' + (d.getMonth() + 1)).slice(-2) + '-' + ('0' + d.getDate()).slice(-2)
    });
    const permissions = this.userService.getUserPermissions();
        for (const p of permissions) {
            if (p.permission.code === 'admin' || p.permission.code === 'oneGateAdminMaster') {
                this.isAdmin = true;
                break;
            }
        }

    this.getAgencyList(false);
  }

  onValChange(val) {
    // tslint:disable-next-line:radix
    this.tabIndex = parseInt(val);
    this.searchBtn();
  }

  getAgencyList(scroll: boolean) {
    if (!scroll) {
      this.agencyPage = 0;
      this.isAgencyListFull = false;
      this.agencyList = [];
    }

    const formObj = this.searchForm.getRawValue();
    const searchString = '?page=' + this.agencyPage + '&size=50&status=1&spec=slice'
      + '&parent-id=' + this.parentAgency + '&keyword=' + encodeURIComponent(formObj.agencyCtrl.trim());
    this.statisticsService.getListAgencyWithParent(searchString).subscribe(data => {
      console.log(data);
      this.agencyList = [...this.agencyList, ...data.content];
      this.agencyList = Object.values(this.agencyList.reduce((acc, cur) => Object.assign(acc, {[cur.id]: cur}), {}));
      this.agencyPage++;
      this.isAgencyListFull = data.last;
      if(!this.isAgencyListFull){
        this.getAgencyList(true);
      }
    }, err => {
      console.log(err);
    });
  }

  searchAgencyList() {
    clearTimeout(this.timeOutSearch);
    this.timeOutSearch = setTimeout(() => {
      this.getAgencyList(false);
    }, this.msTimeOutSearch);
  }

  clearAgencyList() {
    this.searchForm.patchValue({
      agencyCtrl: ''
    });
    this.getAgencyList(false);
  }

  onChangeFromDate($event): void { 
    const formObj = this.searchForm.getRawValue(); 
    let dayEvent = $event.value.getDate();
    let monthEvent = $event.value.getMonth()+1;
    let yearEvent = $event.value.getFullYear();
    let dateEvent = ""+yearEvent+"-"+monthEvent+"-"+dayEvent;

    let toDateConvert = new Date(formObj.toDate);
    let dayToDate = toDateConvert.getDate();
    let monthToDate =toDateConvert.getMonth()+1;
    let yearToDate = toDateConvert.getFullYear();
    let dateToDate = ""+yearToDate+"-"+monthToDate+"-"+dayToDate;

    if(new Date(dateEvent) > new Date(dateToDate)){
      const message = {
        vi: 'Ngày bắt đầu phải nhỏ hơn hoặc bằng ngày kết thúc!',
      };
      this.snackbarService.openSnackBar(0, '', message[localStorage.getItem('language')], 'error_notification', 3000);
      const formObj = this.searchForm.getRawValue();
      this.searchForm.patchValue({
        fromDate:formObj.currentFromDate
      });
      return;
    }else{
      let pickDate = this.datePipe.transform($event.value, 'yyyy-MM-dd');
      this.searchForm.patchValue({
        fromDate:pickDate
      });
    }
  }
  
  onChangeToDate($event): void {  
    const formObj = this.searchForm.getRawValue(); 
    let dayEvent = $event.value.getDate();
    let monthEvent = $event.value.getMonth()+1;
    let yearEvent = $event.value.getFullYear();
    let date = ""+yearEvent+"-"+monthEvent+"-"+dayEvent;

    let fromDateConvert = new Date(formObj.fromDate);
    let dayFromDate = fromDateConvert.getDate();
    let monthFromDate =fromDateConvert.getMonth()+1;
    let yearFromDate = fromDateConvert.getFullYear();
    let dateFromDate = ""+yearFromDate+"-"+monthFromDate+"-"+dayFromDate;
    if(new Date(dateFromDate) > new Date(date)){
      const message = {
        vi: 'Ngày kết thúc phải lớn hơn hoặc bằng ngày bắt đầu!',
      };
      this.snackbarService.openSnackBar(0, '', message[localStorage.getItem('language')], 'error_notification', 3000);
      const formObj = this.searchForm.getRawValue();
      this.searchForm.patchValue({
        toDate:formObj.currentToDate
      });
      return;
    }else{
      let pickDate = this.datePipe.transform($event.value, 'yyyy-MM-dd');
      this.searchForm.patchValue({
        toDate:pickDate
      });
    }
  }

  async searchBtn() {
    const formObj = this.searchForm.getRawValue();
    this.searchForm.patchValue({
      currentFromDate:formObj.fromDate,
      currentToDate:formObj.toDate
    });

    if (formObj.toDate === null || formObj.fromDate === null) {
      const message = {
        vi: 'Ngày thống kê không được để trống!',
        en: 'Statistical date cannot be blank!'
      };
      this.snackbarService.openSnackBar(0, '', message[localStorage.getItem('language')], 'error_notification', 3000);
      return;
    } else if (new Date(formObj.toDate) < new Date(formObj.fromDate)) {
      const message = {
        vi: 'Ngày bắt đầu phải nhỏ hơn hoặc bằng ngày kết thúc!',
        en: 'Start date must be less than or equal to end date!'
      };
      this.snackbarService.openSnackBar(0, '', message[localStorage.getItem('language')], 'error_notification', 3000);
      return;
    }

    this.isLoading = true;

    let searchString = '?from-date=' + this.datePipe.transform(formObj.fromDate, 'yyyy-MM-dd') + 'T00:00:00.000Z'
      + '&to-date=' + this.datePipe.transform(formObj.toDate, 'yyyy-MM-dd') + 'T23:59:59.999Z'
      + '&statistic-type=' + this.tabIndex;

    if (formObj.agency) {
      searchString += '&agency-id=' + formObj.agency;
    } else {
      searchString += '&agency-id=' + this.agencyId;
    }
    if (formObj.submissionMethod !== undefined &&
      formObj.submissionMethod !== null && formObj.submissionMethod !== '') {
      searchString += '&submission-method-id=' + formObj.submissionMethod;
    }

    if (formObj.receivingResultsMethod !== undefined &&
      formObj.receivingResultsMethod !== null && formObj.receivingResultsMethod !== '') {
      searchString += '&receiving-results-method-id=' + formObj.receivingResultsMethod;
    }

    if (formObj.bypassAuthorizationProcedure !== undefined &&
      formObj.bypassAuthorizationProcedure !== null && formObj.bypassAuthorizationProcedure !== '') {
      searchString += '&bypass-authorization-procedure=' + formObj.bypassAuthorizationProcedure;
    }

    await this.getDossierProcessingStatistic(searchString);
  }

  async getDossierProcessingStatistic(searchString) {
    await this.statisticsService.getDossierProcessingStatisticDLK(searchString).toPromise().then(async result => {
     if (result.length > 0) {
        var data = result;
        let acencyArray = "";
        for (var i = 0; i < data.length; i++) {
          if (i != data.length-1) {
            acencyArray = acencyArray.concat(data[i].agency.id).concat(",");
          } else {
            acencyArray = acencyArray.concat(data[i].agency.id);
          }
        }
        
        let searchStringProcedure = '?agency-id=' + acencyArray;
        await this.statisticsService.getTotalByAgencyDLK(searchStringProcedure).toPromise().then(listTotalProcedure => {
          this.fillData(data, listTotalProcedure);
        });
      } else {
        this.dataProcessingSource.data = [];
        this.isLoading = false;
      }
    }, () => {
      this.isLoading = false;
    });
  }

  fillData(data, procedures) {
    try {
      this.ELEMENTPROCESSINGSTATISTIC = [];
      for (let i = 0; i < data.length; i++) {
        const totalProcedure = procedures.filter(f => f.id === data[i].agency?.id);
        if (totalProcedure.length > 0) {
          data[i].totalProcedure = totalProcedure[0].total;
          data[i].procedureLevel2 = totalProcedure[0].numberOfLevel2;
          data[i].procedureLevel3 = totalProcedure[0].numberOfLevel3;
          data[i].procedureLevel4 = totalProcedure[0].numberOfLevel4;
        }
        this.ELEMENTPROCESSINGSTATISTIC.push({
          agencyId: data[i].agency?.id,
          stt: i + 1 + '',
          agencyName: data[i].agency?.name,
          totalDossier: data[i].totalDossier,
          totalProcedure: data[i].totalProcedure,
          dossierLevel2: data[i].dossierLevel2,
          dossierBCCILevel2: data[i].dossierBCCILevel2,
          procedureLevel2: data[i].procedureLevel2,
          dossierLevel3: data[i].dossierLevel3,
          dossierOnlineLevel3: data[i].dossierOnlineLevel3,
          dossierBCCILevel3: data[i].dossierBCCILevel3,
          procedureLevel3: data[i].procedureLevel3,
          dossierLevel4: data[i].dossierLevel4,
          dossierOnlineLevel4: data[i].dossierOnlineLevel4,
          dossierBCCILevel4: data[i].dossierBCCILevel4,
          procedureLevel4: data[i].procedureLevel4,
          dossierExist: data[i].dossierExist,
          dossierReceived: data[i].dossierReceived,
          dossierOnlineReceived: data[i].dossierOnlineReceived,
          resolvedEarlyLevel2: data[i].resolvedEarlyLevel2,
          resolvedOnTimeLevel2: data[i].resolvedOnTimeLevel2,
          resolvedOverdueLevel2: data[i].resolvedOverdueLevel2,
          resolvedEarlyLevel3: data[i].resolvedEarlyLevel3,
          resolvedOnTimeLevel3: data[i].resolvedOnTimeLevel3,
          resolvedOverdueLevel3: data[i].resolvedOverdueLevel3,
          resolvedEarlyLevel4: data[i].resolvedEarlyLevel4,
          resolvedOnTimeLevel4: data[i].resolvedOnTimeLevel4,
          resolvedOverdueLevel4: data[i].resolvedOverdueLevel4,
          unresolved: data[i].unresolved,
          resolveRate: data[i].resolveRate,
          resolved : data[i].resolved,
        });
      }
      this.dataProcessingSource.data = this.ELEMENTPROCESSINGSTATISTIC;
      this.isLoading = false;
    } catch (error) {
      this.isLoading = false;
    }
  }

  formatRation(input){
    if (Number.isNaN(input)) return '0';
    return (input * 100).toFixed(2).replace('.00','') + '%';
  }

  calculateResolutionRate(){
    return this.formatRation(this.sum("resolved", this.dataProcessingSource)/(this.sum("resolved", this.dataProcessingSource)+this.sum("unresolved", this.dataProcessingSource)));
  }

  formatNumber(n: number) {
    return (n == null || false) ? '' : n.toString().replace(/(.)(?=(\d{3})+$)/g, '$1.');
  }

  sum(key: keyof any, source: any) {
    return source.data.reduce((a, b) => a + (Number(b[key]) || 0), 0);
  }

  tabChanged = (tabChangeEvent: MatTabChangeEvent): void => {
    this.tabIndex = tabChangeEvent.index;

    if (this.tabIndex == 0) {
      this.isDigitizedStatistic = false;
    } else {
      this.isDigitizedStatistic = true;
    }
  }

  exportDataToExcel() {
    const newDate = tUtils.newDate();
    let title = '';
  
    const formObj = this.searchForm.getRawValue();
    let fromDateExcel = '';
    let from = [];
    let toDateExcel = '';
    let to = [];
    let name = '';
    let subTitle = '';

    if (formObj.fromDate != null || formObj.fromDate !== '') {
        fromDateExcel = this.datePipe.transform(formObj.fromDate, 'dd/MM/yyyy');
        from = fromDateExcel.split('/');
        fromDateExcel = 'ngày ' + from[0] + ' tháng ' + from[1] + ' năm ' + from[2];
    }
    if (formObj.toDate != null || formObj.toDate !== '') {
        toDateExcel = this.datePipe.transform(formObj.toDate, 'dd/MM/yyyy');
        to = toDateExcel.split('/');
        toDateExcel = 'ngày ' + to[0] + ' tháng ' + to[1] + ' năm ' + to[2];
    }
  
    if (localStorage.getItem('language') === 'vi') {
        title = 'BÁO CÁO THỐNG KÊ TOÀN TỈNH THEO CƠ QUAN';
        name = 'bao_cao_co_quan' + this.datePipe.transform(newDate, 'dd/MM/yyyy');
    } else if (localStorage.getItem('language') === 'en') {
        name = 'processing_report ' + this.datePipe.transform(newDate, 'dd/MM/yyyy');
        title = 'PROCEDURE DOSSIER DIGITIZATION REPORT';
    }
    if (fromDateExcel !== '' && toDateExcel !== '') {
        subTitle = '(Từ ' + fromDateExcel + ' đến ' + toDateExcel + ')';
    }
  
    const totalDossier = this.sum('totalDossier', this.dataProcessingSource);
    const totalProcedure = this.sum('totalProcedure', this.dataProcessingSource);
    const dossierLevel2 = this.sum('dossierLevel2', this.dataProcessingSource);
    const dossierBCCILevel2 = this.sum('dossierBCCILevel2', this.dataProcessingSource);
    const procedureLevel2 = this.sum('procedureLevel2', this.dataProcessingSource);
    const dossierLevel3 = this.sum('dossierLevel3', this.dataProcessingSource);
    const dossierOnlineLevel3 = this.sum('dossierOnlineLevel3', this.dataProcessingSource);
    const dossierBCCILevel3 = this.sum('dossierBCCILevel3', this.dataProcessingSource);
    const procedureLevel3 = this.sum('procedureLevel3', this.dataProcessingSource);
    const dossierLevel4 = this.sum('dossierLevel4', this.dataProcessingSource);
    const dossierOnlineLevel4 = this.sum('dossierOnlineLevel4', this.dataProcessingSource);
    const dossierBCCILevel4 = this.sum('dossierBCCILevel4', this.dataProcessingSource);
    const procedureLevel4 = this.sum('procedureLevel4', this.dataProcessingSource);
    const dossierExist = this.sum('dossierExist', this.dataProcessingSource);
    const dossierReceived = this.sum('dossierReceived', this.dataProcessingSource);
    const dossierOnlineReceived = this.sum('dossierOnlineReceived', this.dataProcessingSource);
    const resolvedEarlyLevel2 = this.sum('resolvedEarlyLevel2', this.dataProcessingSource);
    const resolvedOnTimeLevel2 = this.sum('resolvedOnTimeLevel2', this.dataProcessingSource);
    const resolvedOverdueLevel2 = this.sum('resolvedOverdueLevel2', this.dataProcessingSource);
    const resolvedEarlyLevel3 = this.sum('resolvedEarlyLevel3', this.dataProcessingSource);
    const resolvedOnTimeLevel3 = this.sum('resolvedOnTimeLevel3', this.dataProcessingSource);
    const resolvedOverdueLevel3 = this.sum('resolvedOverdueLevel3', this.dataProcessingSource);
    const resolvedEarlyLevel4 = this.sum('resolvedEarlyLevel4', this.dataProcessingSource);
    const resolvedOnTimeLevel4 = this.sum('resolvedOnTimeLevel4', this.dataProcessingSource);
    const resolvedOverdueLevel4 = this.sum('resolvedOverdueLevel4', this.dataProcessingSource);
    const unresolved = this.sum('unresolved', this.dataProcessingSource);
    const resolveRate = this.calculateResolutionRate();

    this.footerData = [[
        'TỔNG SỐ', '', totalDossier, totalProcedure, dossierLevel2, dossierBCCILevel2, procedureLevel2,
        dossierLevel3, dossierOnlineLevel3, dossierBCCILevel3, procedureLevel3, dossierLevel4,
        dossierOnlineLevel4, dossierBCCILevel4, procedureLevel4, dossierExist, dossierReceived,
        dossierOnlineReceived, resolvedEarlyLevel2, resolvedOnTimeLevel2, resolvedOverdueLevel2,
        resolvedEarlyLevel3, resolvedOnTimeLevel3, resolvedOverdueLevel3, resolvedEarlyLevel4, 
        resolvedOnTimeLevel4, resolvedOverdueLevel4, unresolved, resolveRate]];
    this.statisticsService.exportAsExcelProcessingReport(title, subTitle, this.dataProcessingSource.data, this.footerData, name, 'ProcessingReport');
  }
}
