import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { ApiProviderService } from 'src/app/core/service/api-provider.service';
import { SnackbarService } from '../snackbar/snackbar.service';
import { EnvService } from 'src/app/core/service/env.service';

@Injectable({
  providedIn: 'root'
})
export class LdgStatisticService {
  config = this.envService.getConfig();
  constructor(
    private http: HttpClient,
    private apiProviderService: ApiProviderService,
    private snackbar: SnackbarService,
    private envService: EnvService,
  ) { }
  private padmanURL = this.apiProviderService.getUrl('digo', 'padman');
  private basedataURL = this.apiProviderService.getUrl('digo', 'basedata');
  private basepadURL = this.apiProviderService.getUrl('digo', 'basepad');
  getDossier_bc_tuan(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');
        return this.http.get(this.padmanURL + '/statistics-ldg/--bc-tuan' + searchString, { headers }).pipe();
  }
  getDossier_bc_tuan_detail(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.get(this.padmanURL + '/statistics-ldg/--bc-tuan-detail' + searchString, { headers }).pipe();
  }
  getAgencyNameCode(agencyId, tagId) : Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.get(`${this.basedataURL}/agency/name+code?ancestor-id=${agencyId}&tag-id=${tagId}&page=0&size=2000`, { headers }).pipe();
  }
  getAgencyList(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.get(this.basedataURL + '/agency/--search' + searchString, { headers }).pipe();
  }
  getDossier_export_bctuan(params: string): any {
    return new Promise((resolve) => {
         this.http.get(this.padmanURL + '/statistics-ldg/--export-bc-tuan' + params, {
            observe: 'response',
            responseType: 'blob'
        }).toPromise().then(res => {
            const blob = new Blob([res.body], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'});
            let filename = 'bao_cao_dinh_ky.xlsx';
            if (res.headers.get('content-disposition') != null) {
                filename = res.headers.get('content-disposition').split(';')[1].split('=')[1].replace(/"/g, '');
            }
            const blobUrl = URL.createObjectURL(blob);
            const xhr = new XMLHttpRequest();
            xhr.responseType = 'blob';
  
            xhr.onload = () => {
                const recoveredBlob = xhr.response;
                const reader = new FileReader();
                reader.onload = () => {
                    const base64data = reader.result.toString();
                    const anchor = document.createElement('a');
                    anchor.download = filename;
                    anchor.href = base64data;
                    anchor.click();
                };
  
                reader.readAsDataURL(recoveredBlob);
            };
  
            xhr.open('GET', blobUrl);
            xhr.send();
            resolve(true);
        }).catch(err => {
            if (err.status === 500) {
                const message = {
                    vi: 'Hệ thống tạm thời không thể xuất file excel!',
                    en: 'The system is temporarily unable to export the excel file!'
                };
                this.snackbar.openSnackBar(0, '', message[localStorage.getItem('language')], 'error_notification', this.config.expiredTime);
            }
            resolve(false);
        });
    });
  }
  getDossier_export_bctuan_detail(params: string): any {
    return new Promise((resolve) => {
         this.http.get(this.padmanURL + '/statistics-ldg/--export-bc-tuan--detail' + params, {
            observe: 'response',
            responseType: 'blob'
        }).toPromise().then(res => {
            const blob = new Blob([res.body], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'});
            let filename = 'bao_cao_dinh_ky_chitiet.xlsx';
            if (res.headers.get('content-disposition') != null) {
                filename = res.headers.get('content-disposition').split(';')[1].split('=')[1].replace(/"/g, '');
            }
            const blobUrl = URL.createObjectURL(blob);
            const xhr = new XMLHttpRequest();
            xhr.responseType = 'blob';
  
            xhr.onload = () => {
                const recoveredBlob = xhr.response;
                const reader = new FileReader();
                reader.onload = () => {
                    const base64data = reader.result.toString();
                    const anchor = document.createElement('a');
                    anchor.download = filename;
                    anchor.href = base64data;
                    anchor.click();
                };
                reader.readAsDataURL(recoveredBlob);
            };
  
            xhr.open('GET', blobUrl);
            xhr.send();
            resolve(true);
        }).catch(err => {
            if (err.status === 500) {
                const message = {
                    vi: 'Hệ thống tạm thời không thể xuất file excel!',
                    en: 'The system is temporarily unable to export the excel file!'
                };
                this.snackbar.openSnackBar(0, '', message[localStorage.getItem('language')], 'error_notification', this.config.expiredTime);
            }
            resolve(false);
        });
    });
  }
  getDossier_bc_hsquahan(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.get(this.padmanURL + '/statistics-ldg/--bc-hs-quahan' + searchString, { headers }).pipe();
  }
  getDossier_export_bchsquahan(params: string): any {
    return new Promise((resolve) => {
         this.http.get(this.padmanURL + '/statistics-ldg/--export-bc-hs-quahan' + params, {
            observe: 'response',
            responseType: 'blob'
        }).toPromise().then(res => {
            const blob = new Blob([res.body], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'});
            let filename = 'bao_cao_dinh_ky_chitiet.xlsx';
            if (res.headers.get('content-disposition') != null) {
                filename = res.headers.get('content-disposition').split(';')[1].split('=')[1].replace(/"/g, '');
            }
            const blobUrl = URL.createObjectURL(blob);
            const xhr = new XMLHttpRequest();
            xhr.responseType = 'blob';
  
            xhr.onload = () => {
                const recoveredBlob = xhr.response;
                const reader = new FileReader();
                reader.onload = () => {
                    const base64data = reader.result.toString();
                    const anchor = document.createElement('a');
                    anchor.download = filename;
                    anchor.href = base64data;
                    anchor.click();
                };
                reader.readAsDataURL(recoveredBlob);
            };
  
            xhr.open('GET', blobUrl);
            xhr.send();
            resolve(true);
        }).catch(err => {
            if (err.status === 500) {
                const message = {
                    vi: 'Hệ thống tạm thời không thể xuất file excel!',
                    en: 'The system is temporarily unable to export the excel file!'
                };
                this.snackbar.openSnackBar(0, '', message[localStorage.getItem('language')], 'error_notification', this.config.expiredTime);
            }
            resolve(false);
        });
    });
  }
  getDsChiNhanhVPDKDD(): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.get(this.padmanURL + '/statistics-ldg/--ds-cnvpdkdd', { headers }).pipe();
  }
  getDossier_bc_dvcong(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.get(this.padmanURL + '/statistics-ldg/--bc-dvcong' + searchString, { headers }).pipe();
  }
  getDossier_bc_dvcong_detail(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.get(this.padmanURL + '/statistics-ldg/--bc-dvcong-detail' + searchString, { headers }).pipe();
  }
  getDossier_export_bcdvcong(params: string): any {
    return new Promise((resolve) => {
         this.http.get(this.padmanURL + '/statistics-ldg/--export-bc-dvcong' + params, {
            observe: 'response',
            responseType: 'blob'
        }).toPromise().then(res => {
            const blob = new Blob([res.body], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'});
            let filename = 'bao_cao_dvcong.xlsx';
            if (res.headers.get('content-disposition') != null) {
                filename = res.headers.get('content-disposition').split(';')[1].split('=')[1].replace(/"/g, '');
            }
            const blobUrl = URL.createObjectURL(blob);
            const xhr = new XMLHttpRequest();
            xhr.responseType = 'blob';
  
            xhr.onload = () => {
                const recoveredBlob = xhr.response;
                const reader = new FileReader();
                reader.onload = () => {
                    const base64data = reader.result.toString();
                    const anchor = document.createElement('a');
                    anchor.download = filename;
                    anchor.href = base64data;
                    anchor.click();
                };
  
                reader.readAsDataURL(recoveredBlob);
            };
  
            xhr.open('GET', blobUrl);
            xhr.send();
            resolve(true);
        }).catch(err => {
            if (err.status === 500) {
                const message = {
                    vi: 'Hệ thống tạm thời không thể xuất file excel!',
                    en: 'The system is temporarily unable to export the excel file!'
                };
                this.snackbar.openSnackBar(0, '', message[localStorage.getItem('language')], 'error_notification', this.config.expiredTime);
            }
            resolve(false);
        });
    });
  }
  getDossier_export_bcdvcong_detail(params: string): any {
    return new Promise((resolve) => {
         this.http.get(this.padmanURL + '/statistics-ldg/--export-bc-dvcong--detail' + params, {
            observe: 'response',
            responseType: 'blob'
        }).toPromise().then(res => {
            const blob = new Blob([res.body], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'});
            let filename = 'bao_cao_dvcong_chitiet.xlsx';
            if (res.headers.get('content-disposition') != null) {
                filename = res.headers.get('content-disposition').split(';')[1].split('=')[1].replace(/"/g, '');
            }
            const blobUrl = URL.createObjectURL(blob);
            const xhr = new XMLHttpRequest();
            xhr.responseType = 'blob';
  
            xhr.onload = () => {
                const recoveredBlob = xhr.response;
                const reader = new FileReader();
                reader.onload = () => {
                    const base64data = reader.result.toString();
                    const anchor = document.createElement('a');
                    anchor.download = filename;
                    anchor.href = base64data;
                    anchor.click();
                };
                reader.readAsDataURL(recoveredBlob);
            };
  
            xhr.open('GET', blobUrl);
            xhr.send();
            resolve(true);
        }).catch(err => {
            if (err.status === 500) {
                const message = {
                    vi: 'Hệ thống tạm thời không thể xuất file excel!',
                    en: 'The system is temporarily unable to export the excel file!'
                };
                this.snackbar.openSnackBar(0, '', message[localStorage.getItem('language')], 'error_notification', this.config.expiredTime);
            }
            resolve(false);
        });
    });
  }
  //
  getDossier_bc_kiemsoattthc(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.get(this.padmanURL + '/statistics-ldg/--bc-kiemsoattthc' + searchString, { headers }).pipe();
  }
  getDossier_bc_kiemsoattthc_detail(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.get(this.padmanURL + '/statistics-ldg/--bc-kiemsoattthc-detail' + searchString, { headers }).pipe();
  }
  getDossier_export_kiemsoattthc(params: string): any {
    return new Promise((resolve) => {
         this.http.get(this.padmanURL + '/statistics-ldg/--export-bc-kiemsoattthc' + params, {
            observe: 'response',
            responseType: 'blob'
        }).toPromise().then(res => {
            const blob = new Blob([res.body], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'});
            let filename = 'bao_cao_kiemsoattthc.xlsx';
            if (res.headers.get('content-disposition') != null) {
                filename = res.headers.get('content-disposition').split(';')[1].split('=')[1].replace(/"/g, '');
            }
            const blobUrl = URL.createObjectURL(blob);
            const xhr = new XMLHttpRequest();
            xhr.responseType = 'blob';
  
            xhr.onload = () => {
                const recoveredBlob = xhr.response;
                const reader = new FileReader();
                reader.onload = () => {
                    const base64data = reader.result.toString();
                    const anchor = document.createElement('a');
                    anchor.download = filename;
                    anchor.href = base64data;
                    anchor.click();
                };
  
                reader.readAsDataURL(recoveredBlob);
            };
  
            xhr.open('GET', blobUrl);
            xhr.send();
            resolve(true);
        }).catch(err => {
            if (err.status === 500) {
                const message = {
                    vi: 'Hệ thống tạm thời không thể xuất file excel!',
                    en: 'The system is temporarily unable to export the excel file!'
                };
                this.snackbar.openSnackBar(0, '', message[localStorage.getItem('language')], 'error_notification', this.config.expiredTime);
            }
            resolve(false);
        });
    });
  }
  getDossier_export_kiemsoattthc_detail(params: string): any {
    return new Promise((resolve) => {
         this.http.get(this.padmanURL + '/statistics-ldg/--export-bc-kiemsoattthc--detail' + params, {
            observe: 'response',
            responseType: 'blob'
        }).toPromise().then(res => {
            const blob = new Blob([res.body], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'});
            let filename = 'bao_cao_kiemsoattthc_chitiet.xlsx';
            if (res.headers.get('content-disposition') != null) {
                filename = res.headers.get('content-disposition').split(';')[1].split('=')[1].replace(/"/g, '');
            }
            const blobUrl = URL.createObjectURL(blob);
            const xhr = new XMLHttpRequest();
            xhr.responseType = 'blob';
  
            xhr.onload = () => {
                const recoveredBlob = xhr.response;
                const reader = new FileReader();
                reader.onload = () => {
                    const base64data = reader.result.toString();
                    const anchor = document.createElement('a');
                    anchor.download = filename;
                    anchor.href = base64data;
                    anchor.click();
                };
                reader.readAsDataURL(recoveredBlob);
            };
  
            xhr.open('GET', blobUrl);
            xhr.send();
            resolve(true);
        }).catch(err => {
            if (err.status === 500) {
                const message = {
                    vi: 'Hệ thống tạm thời không thể xuất file excel!',
                    en: 'The system is temporarily unable to export the excel file!'
                };
                this.snackbar.openSnackBar(0, '', message[localStorage.getItem('language')], 'error_notification', this.config.expiredTime);
            }
            resolve(false);
        });
    });
  }
  //
  getDossier_bc_sapdenhan(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.get(this.padmanURL + '/statistics-ldg/--bc-sapdenhan' + searchString, { headers }).pipe();
  }
  getDossier_bc_sapdenhan_detail(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.get(this.padmanURL + '/statistics-ldg/--bc-sapdenhan-detail' + searchString, { headers }).pipe();
  }
  //
  getDossier_bc_dvcongtheothutuc(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.get(this.padmanURL + '/statistics-ldg/--bc-dvcongtheothutuc' + searchString, { headers }).pipe();
  }
  getDossier_bc_dvcongtheothutuc_detail(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    headers = headers.append('Content-Type', 'application/json');
    headers.append('Access-Control-Allow-Origin', '*');
    return this.http.get(this.padmanURL + '/statistics-ldg/--bc-dvcongtheothutuc-detail' + searchString, { headers }).pipe();
  }
  getDossier_export_bcdvcongtheothutuc(params: string): any {
    return new Promise((resolve) => {
         this.http.get(this.padmanURL + '/statistics-ldg/--export-bc-dvcongtheothutuc' + params, {
            observe: 'response',
            responseType: 'blob'
        }).toPromise().then(res => {
            const blob = new Blob([res.body], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'});
            let filename = 'bao_cao_dvcongtheothutuc.xlsx';
            if (res.headers.get('content-disposition') != null) {
                filename = res.headers.get('content-disposition').split(';')[1].split('=')[1].replace(/"/g, '');
            }
            const blobUrl = URL.createObjectURL(blob);
            const xhr = new XMLHttpRequest();
            xhr.responseType = 'blob';
  
            xhr.onload = () => {
                const recoveredBlob = xhr.response;
                const reader = new FileReader();
                reader.onload = () => {
                    const base64data = reader.result.toString();
                    const anchor = document.createElement('a');
                    anchor.download = filename;
                    anchor.href = base64data;
                    anchor.click();
                };
  
                reader.readAsDataURL(recoveredBlob);
            };
  
            xhr.open('GET', blobUrl);
            xhr.send();
            resolve(true);
        }).catch(err => {
            if (err.status === 500) {
                const message = {
                    vi: 'Hệ thống tạm thời không thể xuất file excel!',
                    en: 'The system is temporarily unable to export the excel file!'
                };
                this.snackbar.openSnackBar(0, '', message[localStorage.getItem('language')], 'error_notification', this.config.expiredTime);
            }
            resolve(false);
        });
    });
  }
  getDossier_export_bcdvcongtheothutuc_detail(params: string): any {
    return new Promise((resolve) => {
         this.http.get(this.padmanURL + '/statistics-ldg/--export-bc-dvcongtheothutuc--detail' + params, {
            observe: 'response',
            responseType: 'blob'
        }).toPromise().then(res => {
            const blob = new Blob([res.body], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'});
            let filename = 'bao_cao_dvcongtheothutuc_chitiet.xlsx';
            if (res.headers.get('content-disposition') != null) {
                filename = res.headers.get('content-disposition').split(';')[1].split('=')[1].replace(/"/g, '');
            }
            const blobUrl = URL.createObjectURL(blob);
            const xhr = new XMLHttpRequest();
            xhr.responseType = 'blob';
  
            xhr.onload = () => {
                const recoveredBlob = xhr.response;
                const reader = new FileReader();
                reader.onload = () => {
                    const base64data = reader.result.toString();
                    const anchor = document.createElement('a');
                    anchor.download = filename;
                    anchor.href = base64data;
                    anchor.click();
                };
                reader.readAsDataURL(recoveredBlob);
            };
  
            xhr.open('GET', blobUrl);
            xhr.send();
            resolve(true);
        }).catch(err => {
            if (err.status === 500) {
                const message = {
                    vi: 'Hệ thống tạm thời không thể xuất file excel!',
                    en: 'The system is temporarily unable to export the excel file!'
                };
                this.snackbar.openSnackBar(0, '', message[localStorage.getItem('language')], 'error_notification', this.config.expiredTime);
            }
            resolve(false);
        });
    });
  }
  //
  getListSectorAll(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.basepadURL + '/sector' + searchString, { headers });
  }
  getListProcedureAll(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.basepadURL + '/procedure' + searchString, { headers });
  }
  getListAgency(searchString): Observable<any> {
    let headers = new HttpHeaders();
    headers = headers.set('Accept-Language', localStorage.getItem('language'));
    return this.http.get(this.basedataURL + '/agency/--by-parent-agency' + searchString, { headers });
  }
}
